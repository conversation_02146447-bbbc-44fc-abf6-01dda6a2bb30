import { CreateEcomProductCategoriesService } from '../services/create.ecom-product-categories.service';
import { EcomProductCategoryDto } from '../dto/ecom-product-category.dto';
import { CreateEcomProductCategoryDto } from '../dto/create-ecom-product-category.dto';
export declare class CreateEcomProductCategoriesController {
    private readonly ecomProductCategoriesService;
    constructor(ecomProductCategoriesService: CreateEcomProductCategoriesService);
    create(createEcomProductCategoryDto: CreateEcomProductCategoryDto, userId: string): Promise<EcomProductCategoryDto>;
    bulkCreate(createEcomProductCategoryDtos: CreateEcomProductCategoryDto[], userId: string): Promise<EcomProductCategoryDto[]>;
    duplicate(id: string, userId: string): Promise<EcomProductCategoryDto>;
}

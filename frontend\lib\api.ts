/**
 * API client cho ứng dụng
 */
import { authService, isTokenExpired } from './auth';
import { isPublicPath } from './constants/paths';

const NEXT_PUBLIC_API_URL = process.env.NEXT_PUBLIC_API_URL || "http://localhost:3000/api/v1"; 

// Biến để theo dõi các yêu cầu đang chờ refresh token
let isRefreshing = false;
let failedQueue: { resolve: (value: unknown) => void; reject: (reason?: any) => void }[] = [];

/**
 * Xử lý hàng đợi các yêu cầu thất bại
 */
function processQueue(error: any | null, token: string | null = null) {
  failedQueue.forEach(prom => {
    if (error) {
      prom.reject(error);
    } else {
      prom.resolve(token);
    }
  });

  failedQueue = [];
}

/**
 * Hàm thử refresh token và thực hiện lại yêu cầu
 */
async function handleTokenRefresh() {
  try {
    if (!isRefreshing) {
      isRefreshing = true;
      
      const success = await authService.tryRefreshToken();
      isRefreshing = false;
      

      if (success) {
        // Lấy token mới
        const newToken = authService.getAccessToken();
        // Xử lý hàng đợi với token mới
        processQueue(null, newToken);

        // Lấy thông tin người dùng hiện tại
        const currentUser = authService.getCurrentUser();
        if (currentUser) {
          // Chỉ redirect nếu user đang ở trang không phù hợp với role
          // Không redirect nếu user đã ở đúng trang của role họ
          const currentPath = window.location.pathname;
          const isAdmin = currentUser.roles?.includes('ADMIN');

          // Chỉ redirect khi cần thiết và không phải trang public
          const isOnPublicPath = isPublicPath(currentPath);

          const shouldRedirect = !isOnPublicPath && (
            (isAdmin && !currentPath.startsWith('/admin')) ||
            (!isAdmin && currentPath.startsWith('/admin'))
          );

          if (shouldRedirect) {
            authService.redirectBasedOnRole(currentUser);
          }
        }

        return newToken;
      } else {
        // Nếu refresh token thất bại, đăng xuất người dùng
        
        authService.logout();
        processQueue(new Error('Refresh token failed'));
        return null;
      }
    } else {
      
      // Nếu đang refresh token, thêm yêu cầu vào hàng đợi
      return new Promise((resolve, reject) => {
        failedQueue.push({ resolve, reject });
      });
    }
  } catch (error) {
    console.error("API: Error during token refresh:", error);
    isRefreshing = false;
    processQueue(error);
    throw error;
  }
}

/**
 * Hàm fetch API với xử lý lỗi và headers mặc định
 */
export async function fetchApi<T>(
  endpoint: string,
  options: RequestInit = {}
): Promise<T> {
  const url = `${NEXT_PUBLIC_API_URL}${endpoint.startsWith('/') ? endpoint : `/${endpoint}`}`;

  // Hàm để thực hiện yêu cầu API
  const executeRequest = async (token?: string | null) => {
    // Tạo headers mới
    const headers = new Headers(options.headers);
    headers.set('Content-Type', 'application/json');

    // Thêm token xác thực nếu có
    const accessToken = token || (typeof window !== 'undefined' ? localStorage.getItem('accessToken') : null);
    if (accessToken) {
      headers.set('Authorization', `Bearer ${accessToken}`);
    }

    const config = {
      ...options,
      headers,
    };

    const response = await fetch(url, config);

    // Kiểm tra nếu response không thành công
    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      // Xử lý cấu trúc lỗi từ backend
      const errorMessage = errorData.message ||
                         (errorData.data && errorData.data.message) ||
                         `API request failed with status ${response.status}`;
      console.error('API Error:', errorData);

      // Tạo error object với thông tin chi tiết hơn
      const error: any = new Error(errorMessage);
      error.status = response.status;
      error.data = errorData;
      throw error;
    }

    // Parse JSON response
    const responseData = await response.json();

    // Kiểm tra cấu trúc response từ backend (ApiResponseDto)
    if (responseData.data !== undefined) {
      // Trường hợp response có cấu trúc ApiResponseDto
      return responseData.data;
    } else {
      // Trường hợp response không có cấu trúc ApiResponseDto
      console.warn('API response does not follow standard structure:', responseData);
      return responseData;
    }
  };

  try {
    // Thử thực hiện yêu cầu
    return await executeRequest();
  } catch (error: any) {
    // Nếu lỗi 401 Unauthorized và không phải là yêu cầu đăng nhập/đăng ký/refresh token
    if (error.status === 401 &&
        !endpoint.includes('auth/login') &&
        !endpoint.includes('auth/register') &&
        !endpoint.includes('auth/refresh-token')) {
      try {
        // Thử refresh token
        const newToken = await handleTokenRefresh();
        if (newToken) {
          // Thực hiện lại yêu cầu với token mới
          return await executeRequest(newToken);
        }
      } catch (refreshError) {
        console.error('Error refreshing token:', refreshError);
        // Nếu refresh token thất bại, đăng xuất người dùng
        authService.logout();
        throw error; // Trả về lỗi ban đầu
      }
    }

    console.error('API request error:', error);
    throw error;
  }
}

/**
 * Các phương thức HTTP
 */
export const api = {
  baseUrl: NEXT_PUBLIC_API_URL,

  getToken: () => {
    return typeof window !== 'undefined' ? localStorage.getItem('accessToken') : null;
  },

  get: <T>(endpoint: string, options?: RequestInit) =>
    fetchApi<T>(endpoint, { ...options, method: 'GET' }),

  post: <T>(endpoint: string, data?: any, options?: RequestInit) =>
    fetchApi<T>(endpoint, {
      ...options,
      method: 'POST',
      body: data ? JSON.stringify(data) : undefined,
    }),

  put: <T>(endpoint: string, data?: any, options?: RequestInit) =>
    fetchApi<T>(endpoint, {
      ...options,
      method: 'PUT',
      body: data ? JSON.stringify(data) : undefined,
    }),

  patch: <T>(endpoint: string, data?: any, options?: RequestInit) =>
    fetchApi<T>(endpoint, {
      ...options,
      method: 'PATCH',
      body: data ? JSON.stringify(data) : undefined,
    }),

  delete: <T>(endpoint: string, options?: RequestInit) =>
    fetchApi<T>(endpoint, { ...options, method: 'DELETE' }),

  // Phương thức download file
  downloadFile: async (endpoint: string, format: string, filename: string) => {
    const url = `${NEXT_PUBLIC_API_URL}${endpoint.startsWith('/') ? endpoint : `/${endpoint}`}`;
    const token = typeof window !== 'undefined' ? localStorage.getItem('accessToken') : null;

    try {
      // Sử dụng XMLHttpRequest thay vì fetch để xử lý tải file tốt hơn
      return new Promise((resolve, reject) => {
        const xhr = new XMLHttpRequest();
        xhr.open('GET', url, true);
        xhr.responseType = 'blob';
        xhr.setRequestHeader('Authorization', token ? `Bearer ${token}` : '');

        xhr.onload = function() {
          if (this.status === 200) {
            const blob = new Blob([this.response], {
              type: format === 'csv' ? 'text/csv' : 'application/json'
            });
            const downloadUrl = window.URL.createObjectURL(blob);

            const a = document.createElement('a');
            a.href = downloadUrl;
            a.download = filename;
            document.body.appendChild(a);
            a.click();

            // Cleanup
            window.URL.revokeObjectURL(downloadUrl);
            document.body.removeChild(a);

            resolve(true);
          } else {
            reject(new Error(`Download failed: ${this.status} ${this.statusText}`));
          }
        };

        xhr.onerror = function() {
          reject(new Error('Network error occurred'));
        };

        xhr.send();
      });
    } catch (error) {
      console.error('Download error:', error);
      throw error;
    }
  }
};

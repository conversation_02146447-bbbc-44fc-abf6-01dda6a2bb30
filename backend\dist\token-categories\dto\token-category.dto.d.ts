import { UserDto } from '../../users/dto/user.dto';
import { TokenDto } from '../../tokens/dto/token.dto';
export declare class TokenCategoryDto {
    id: string;
    categoryName: string;
    description?: string | null;
    isActive: boolean;
    createdAt: Date;
    updatedAt: Date;
    createdBy?: string | null;
    updatedBy?: string | null;
    isDeleted: boolean;
    deletedBy?: string | null;
    tokens?: TokenDto[];
    creator?: UserDto;
    updater?: UserDto;
    deleter?: UserDto;
}

import { OrderType } from '../enums/order-type.enum';
import { OrderStatus } from '../enums/order-status.enum';
import { BusinessType } from '../enums/business-type.enum';
import { ApproveStatus } from '../enums/approve-status.enum';
import { BaseDto } from '../../common/dto/base.dto';
import { User } from '../../users/entities/user.entity';
import { OrderBookDetailDto } from './order-book-detail.dto';
export declare class OrderBookDto extends BaseDto {
    userId: string;
    orderType: OrderType;
    status: OrderStatus;
    businessType: BusinessType;
    totalPrice: number;
    processingPrice: number;
    depositPrice: number;
    storageFee: number;
    settlementPrice: number;
    totalPriceFinal: number;
    contractNumber: string;
    settlementDeadline: Date;
    settlementAt: Date;
    approveStatus: ApproveStatus;
    approvedAt: Date;
    user: User;
    details: OrderBookDetailDto[];
    creator: User;
    updater: User;
}

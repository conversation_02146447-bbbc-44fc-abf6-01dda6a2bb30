"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var ActivityLogDeleteController_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.ActivityLogDeleteController = void 0;
const openapi = require("@nestjs/swagger");
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const jwt_auth_guard_1 = require("../../common/guards/jwt-auth.guard");
const roles_guard_1 = require("../../common/guards/roles.guard");
const roles_decorator_1 = require("../../common/decorators/roles.decorator");
const get_user_decorator_1 = require("../../common/decorators/get-user.decorator");
const default_user_role_enum_1 = require("../../users/enums/default-user-role.enum");
const delete_activity_log_service_1 = require("../services/delete.activity-log.service");
const activity_log_dto_1 = require("../dto/activity-log.dto");
const pagination_response_dto_1 = require("../../common/dto/pagination-response.dto");
const custom_pagination_meta_dto_1 = require("../../common/dto/custom-pagination-meta.dto");
const custom_pagination_query_dto_1 = require("../../common/dto/custom-pagination-query.dto");
let ActivityLogDeleteController = ActivityLogDeleteController_1 = class ActivityLogDeleteController {
    activityLogService;
    logger = new common_1.Logger(ActivityLogDeleteController_1.name);
    constructor(activityLogService) {
        this.activityLogService = activityLogService;
    }
    async softDelete(id, userId) {
        this.logger.debug(`Đang xóa mềm lịch sử hoạt động với ID: ${id}`);
        return this.activityLogService.softDelete(id, userId);
    }
    async bulkSoftDelete(ids, userId) {
        this.logger.debug(`Đang xóa mềm hàng loạt ${ids.length} lịch sử hoạt động`);
        return this.activityLogService.bulkSoftDelete(ids, userId);
    }
    async remove(id) {
        this.logger.debug(`Đang xóa cứng lịch sử hoạt động với ID: ${id}`);
        return this.activityLogService.remove(id);
    }
    async bulkRemove(ids) {
        this.logger.debug(`Đang xóa cứng hàng loạt ${ids.length} lịch sử hoạt động`);
        return this.activityLogService.bulkRemove(ids);
    }
    async restore(id) {
        this.logger.debug(`Đang khôi phục lịch sử hoạt động với ID: ${id}`);
        return this.activityLogService.restore(id);
    }
    async findDeleted(limit, page) {
        this.logger.debug(`Đang lấy danh sách lịch sử hoạt động đã xóa mềm`);
        const paginationQuery = new custom_pagination_query_dto_1.CustomPaginationQueryDto();
        Object.assign(paginationQuery, {
            limit,
            page
        });
        const data = [];
        const total = 0;
        const meta = new custom_pagination_meta_dto_1.CustomPageMetaDto({
            pageQueryDto: paginationQuery,
            itemCount: total,
        });
        return new pagination_response_dto_1.PaginationResponseDto(data, meta);
    }
    async clearOldLogs(days) {
        this.logger.debug(`Đang xóa lịch sử hoạt động cũ hơn ${days} ngày`);
        const count = await this.activityLogService.clearOldLogs(days);
        return { count };
    }
};
exports.ActivityLogDeleteController = ActivityLogDeleteController;
__decorate([
    (0, common_1.Delete)(':id'),
    (0, roles_decorator_1.Roles)(default_user_role_enum_1.PrimaryUserRole.ADMIN),
    (0, swagger_1.ApiOperation)({ summary: 'Xóa mềm lịch sử hoạt động' }),
    (0, swagger_1.ApiParam)({
        name: 'id',
        description: 'ID của lịch sử hoạt động cần xóa',
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Lịch sử hoạt động đã được xóa mềm thành công',
        type: activity_log_dto_1.ActivityLogDto,
    }),
    (0, swagger_1.ApiResponse)({
        status: 404,
        description: 'Không tìm thấy lịch sử hoạt động',
    }),
    openapi.ApiResponse({ status: 200, type: require("../dto/activity-log.dto").ActivityLogDto }),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __param(1, (0, get_user_decorator_1.GetUser)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String]),
    __metadata("design:returntype", Promise)
], ActivityLogDeleteController.prototype, "softDelete", null);
__decorate([
    (0, common_1.Delete)('bulk'),
    (0, roles_decorator_1.Roles)(default_user_role_enum_1.PrimaryUserRole.ADMIN),
    (0, swagger_1.ApiOperation)({ summary: 'Xóa mềm hàng loạt lịch sử hoạt động' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Các lịch sử hoạt động đã được xóa mềm thành công',
        type: [activity_log_dto_1.ActivityLogDto],
    }),
    (0, swagger_1.ApiResponse)({
        status: 404,
        description: 'Không tìm thấy một hoặc nhiều lịch sử hoạt động',
    }),
    (0, swagger_1.ApiBody)({ type: [String], description: 'Danh sách ID của lịch sử hoạt động cần xóa' }),
    openapi.ApiResponse({ status: 200, type: [require("../dto/activity-log.dto").ActivityLogDto] }),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, get_user_decorator_1.GetUser)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Array, String]),
    __metadata("design:returntype", Promise)
], ActivityLogDeleteController.prototype, "bulkSoftDelete", null);
__decorate([
    (0, common_1.Delete)('permanent/:id'),
    (0, roles_decorator_1.Roles)(default_user_role_enum_1.PrimaryUserRole.ADMIN),
    (0, swagger_1.ApiOperation)({ summary: 'Xóa cứng lịch sử hoạt động' }),
    (0, swagger_1.ApiParam)({
        name: 'id',
        description: 'ID của lịch sử hoạt động cần xóa cứng',
    }),
    (0, swagger_1.ApiResponse)({
        status: 204,
        description: 'Lịch sử hoạt động đã được xóa cứng thành công',
    }),
    (0, swagger_1.ApiResponse)({
        status: 404,
        description: 'Không tìm thấy lịch sử hoạt động',
    }),
    (0, common_1.HttpCode)(common_1.HttpStatus.NO_CONTENT),
    openapi.ApiResponse({ status: common_1.HttpStatus.NO_CONTENT }),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], ActivityLogDeleteController.prototype, "remove", null);
__decorate([
    (0, common_1.Delete)('permanent/bulk'),
    (0, roles_decorator_1.Roles)(default_user_role_enum_1.PrimaryUserRole.ADMIN),
    (0, swagger_1.ApiOperation)({ summary: 'Xóa cứng hàng loạt lịch sử hoạt động' }),
    (0, swagger_1.ApiResponse)({
        status: 204,
        description: 'Các lịch sử hoạt động đã được xóa cứng thành công',
    }),
    (0, swagger_1.ApiResponse)({
        status: 404,
        description: 'Không tìm thấy một hoặc nhiều lịch sử hoạt động',
    }),
    (0, swagger_1.ApiBody)({ type: [String], description: 'Danh sách ID của lịch sử hoạt động cần xóa cứng' }),
    (0, common_1.HttpCode)(common_1.HttpStatus.NO_CONTENT),
    openapi.ApiResponse({ status: common_1.HttpStatus.NO_CONTENT }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Array]),
    __metadata("design:returntype", Promise)
], ActivityLogDeleteController.prototype, "bulkRemove", null);
__decorate([
    (0, common_1.Patch)('restore/:id'),
    (0, roles_decorator_1.Roles)(default_user_role_enum_1.PrimaryUserRole.ADMIN),
    (0, swagger_1.ApiOperation)({ summary: 'Khôi phục lịch sử hoạt động đã xóa mềm' }),
    (0, swagger_1.ApiParam)({
        name: 'id',
        description: 'ID của lịch sử hoạt động cần khôi phục',
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Lịch sử hoạt động đã được khôi phục thành công',
        type: activity_log_dto_1.ActivityLogDto,
    }),
    (0, swagger_1.ApiResponse)({
        status: 404,
        description: 'Không tìm thấy lịch sử hoạt động',
    }),
    openapi.ApiResponse({ status: 200, type: require("../dto/activity-log.dto").ActivityLogDto }),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], ActivityLogDeleteController.prototype, "restore", null);
__decorate([
    (0, common_1.Get)('deleted'),
    (0, roles_decorator_1.Roles)(default_user_role_enum_1.PrimaryUserRole.ADMIN),
    (0, swagger_1.ApiOperation)({ summary: 'Lấy danh sách lịch sử hoạt động đã xóa mềm' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Danh sách lịch sử hoạt động đã xóa mềm',
        type: pagination_response_dto_1.PaginationResponseDto,
    }),
    openapi.ApiResponse({ status: 200 }),
    __param(0, (0, common_1.Query)('limit', new common_1.DefaultValuePipe(10), common_1.ParseIntPipe)),
    __param(1, (0, common_1.Query)('page', new common_1.DefaultValuePipe(1), common_1.ParseIntPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, Number]),
    __metadata("design:returntype", Promise)
], ActivityLogDeleteController.prototype, "findDeleted", null);
__decorate([
    (0, common_1.Delete)('clear-old/:days'),
    (0, roles_decorator_1.Roles)(default_user_role_enum_1.PrimaryUserRole.ADMIN),
    (0, swagger_1.ApiOperation)({ summary: 'Xóa lịch sử hoạt động cũ' }),
    (0, swagger_1.ApiParam)({
        name: 'days',
        description: 'Số ngày (xóa các lịch sử hoạt động cũ hơn số ngày này)',
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Số lượng lịch sử hoạt động đã xóa',
        schema: { properties: { count: { type: 'number' } } },
    }),
    openapi.ApiResponse({ status: 200 }),
    __param(0, (0, common_1.Param)('days', common_1.ParseIntPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", Promise)
], ActivityLogDeleteController.prototype, "clearOldLogs", null);
exports.ActivityLogDeleteController = ActivityLogDeleteController = ActivityLogDeleteController_1 = __decorate([
    (0, swagger_1.ApiTags)('activity-logs'),
    (0, swagger_1.ApiBearerAuth)(),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard, roles_guard_1.RolesGuard),
    (0, common_1.Controller)('activity-logs'),
    __metadata("design:paramtypes", [delete_activity_log_service_1.DeleteActivityLogService])
], ActivityLogDeleteController);
//# sourceMappingURL=activity-log.delete.controller.js.map
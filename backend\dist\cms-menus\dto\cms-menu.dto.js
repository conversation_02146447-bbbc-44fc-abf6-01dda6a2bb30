"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CmsMenuDto = void 0;
const openapi = require("@nestjs/swagger");
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
const class_transformer_1 = require("class-transformer");
const user_dto_1 = require("../../users/dto/user.dto");
const cms_menus_entity_1 = require("../entity/cms-menus.entity");
class CmsMenuDto {
    id;
    businessCode;
    name;
    slug;
    description;
    postType;
    imageUrl;
    metaTitle;
    metaDescription;
    metaKeywords;
    status;
    parentId;
    parent;
    children;
    creator;
    updater;
    deleter;
    createdAt;
    updatedAt;
    deletedAt;
    isDeleted;
    static _OPENAPI_METADATA_FACTORY() {
        return { id: { required: true, type: () => String, format: "uuid" }, businessCode: { required: true, type: () => String }, name: { required: true, type: () => String }, slug: { required: true, type: () => String }, description: { required: false, type: () => String }, postType: { required: true, enum: require("../entity/cms-menus.entity").CmsMenuPostType }, imageUrl: { required: false, type: () => String }, metaTitle: { required: false, type: () => String }, metaDescription: { required: false, type: () => String }, metaKeywords: { required: false, type: () => String }, status: { required: true, enum: require("../entity/cms-menus.entity").CmsMenuStatus }, parentId: { required: false, type: () => String, format: "uuid" }, parent: { required: false, type: () => require("./cms-menu.dto").CmsMenuDto }, children: { required: false, type: () => [require("./cms-menu.dto").CmsMenuDto] }, creator: { required: false, type: () => require("../../users/dto/user.dto").UserDto }, updater: { required: false, type: () => require("../../users/dto/user.dto").UserDto }, deleter: { required: false, type: () => require("../../users/dto/user.dto").UserDto }, createdAt: { required: true, type: () => Date }, updatedAt: { required: true, type: () => Date }, deletedAt: { required: false, type: () => Date }, isDeleted: { required: true, type: () => Boolean } };
    }
}
exports.CmsMenuDto = CmsMenuDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'ID của menu',
        example: '550e8400-e29b-41d4-a716-************',
    }),
    (0, class_transformer_1.Expose)(),
    (0, class_validator_1.IsUUID)('4'),
    __metadata("design:type", String)
], CmsMenuDto.prototype, "id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Mã kinh doanh của menu',
        example: 'CMM-240101-00001',
    }),
    (0, class_transformer_1.Expose)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CmsMenuDto.prototype, "businessCode", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Tên menu',
        example: 'Menu chính',
    }),
    (0, class_transformer_1.Expose)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CmsMenuDto.prototype, "name", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Chuỗi URL thân thiện',
        example: 'menu-chinh',
    }),
    (0, class_transformer_1.Expose)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CmsMenuDto.prototype, "slug", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Mô tả menu',
        example: 'Menu điều hướng chính của website',
    }),
    (0, class_transformer_1.Expose)(),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CmsMenuDto.prototype, "description", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Loại post mà menu này áp dụng',
        example: cms_menus_entity_1.CmsMenuPostType.POST,
        enum: cms_menus_entity_1.CmsMenuPostType,
    }),
    (0, class_transformer_1.Expose)(),
    (0, class_validator_1.IsEnum)(cms_menus_entity_1.CmsMenuPostType),
    __metadata("design:type", String)
], CmsMenuDto.prototype, "postType", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'URL hình ảnh đại diện cho menu',
        example: 'https://example.com/images/menu.jpg',
    }),
    (0, class_transformer_1.Expose)(),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CmsMenuDto.prototype, "imageUrl", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Tiêu đề SEO',
        example: 'Menu chính - Điều hướng website',
    }),
    (0, class_transformer_1.Expose)(),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CmsMenuDto.prototype, "metaTitle", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Mô tả SEO',
        example: 'Menu điều hướng chính giúp người dùng dễ dàng tìm kiếm thông tin',
    }),
    (0, class_transformer_1.Expose)(),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CmsMenuDto.prototype, "metaDescription", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Từ khóa SEO',
        example: 'menu, điều hướng, website',
    }),
    (0, class_transformer_1.Expose)(),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CmsMenuDto.prototype, "metaKeywords", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Trạng thái menu',
        example: cms_menus_entity_1.CmsMenuStatus.ACTIVE,
        enum: cms_menus_entity_1.CmsMenuStatus,
    }),
    (0, class_transformer_1.Expose)(),
    (0, class_validator_1.IsEnum)(cms_menus_entity_1.CmsMenuStatus),
    __metadata("design:type", String)
], CmsMenuDto.prototype, "status", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'ID của menu cha',
        example: '550e8400-e29b-41d4-a716-************',
    }),
    (0, class_transformer_1.Expose)(),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsUUID)('4'),
    __metadata("design:type", String)
], CmsMenuDto.prototype, "parentId", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Thông tin menu cha',
        type: () => CmsMenuDto,
    }),
    (0, class_transformer_1.Expose)(),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => CmsMenuDto),
    __metadata("design:type", CmsMenuDto)
], CmsMenuDto.prototype, "parent", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Danh sách menu con',
        type: () => [CmsMenuDto],
    }),
    (0, class_transformer_1.Expose)(),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => CmsMenuDto),
    __metadata("design:type", Array)
], CmsMenuDto.prototype, "children", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Thông tin người tạo',
        type: () => user_dto_1.UserDto,
    }),
    (0, class_transformer_1.Expose)(),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => user_dto_1.UserDto),
    __metadata("design:type", user_dto_1.UserDto)
], CmsMenuDto.prototype, "creator", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Thông tin người cập nhật cuối',
        type: () => user_dto_1.UserDto,
    }),
    (0, class_transformer_1.Expose)(),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => user_dto_1.UserDto),
    __metadata("design:type", user_dto_1.UserDto)
], CmsMenuDto.prototype, "updater", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Thông tin người xóa',
        type: () => user_dto_1.UserDto,
    }),
    (0, class_transformer_1.Expose)(),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => user_dto_1.UserDto),
    __metadata("design:type", user_dto_1.UserDto)
], CmsMenuDto.prototype, "deleter", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Thời gian tạo',
        example: '2024-01-01T00:00:00.000Z',
    }),
    (0, class_transformer_1.Expose)(),
    (0, class_validator_1.IsDate)(),
    (0, class_transformer_1.Type)(() => Date),
    __metadata("design:type", Date)
], CmsMenuDto.prototype, "createdAt", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Thời gian cập nhật cuối',
        example: '2024-01-01T00:00:00.000Z',
    }),
    (0, class_transformer_1.Expose)(),
    (0, class_validator_1.IsDate)(),
    (0, class_transformer_1.Type)(() => Date),
    __metadata("design:type", Date)
], CmsMenuDto.prototype, "updatedAt", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Thời gian xóa mềm',
        example: '2024-01-01T00:00:00.000Z',
    }),
    (0, class_transformer_1.Expose)(),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsDate)(),
    (0, class_transformer_1.Type)(() => Date),
    __metadata("design:type", Date)
], CmsMenuDto.prototype, "deletedAt", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Trạng thái đã bị xóa mềm',
        example: false,
    }),
    (0, class_transformer_1.Expose)(),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], CmsMenuDto.prototype, "isDeleted", void 0);
//# sourceMappingURL=cms-menu.dto.js.map
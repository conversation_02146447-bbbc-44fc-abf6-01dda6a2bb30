import { Repository, DataSource } from 'typeorm';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { BaseCmsTagsService } from './base.cms-tags.service';
import { SlugCmsTagsService } from './slug.cms-tags.service';
import { CmsTags } from '../entity/cms-tags.entity';
import { CreateCmsTagDto } from '../dto/create.cms-tag.dto';
import { CmsTagDto } from '../dto/cms-tag.dto';
export declare class CreateCmsTagsService extends BaseCmsTagsService {
    protected readonly tagRepository: Repository<CmsTags>;
    protected readonly dataSource: DataSource;
    protected readonly eventEmitter: EventEmitter2;
    private readonly slugService;
    constructor(tagRepository: Repository<CmsTags>, dataSource: DataSource, eventEmitter: EventEmitter2, slugService: SlugCmsTagsService);
    create(createDto: CreateCmsTagDto, userId: string): Promise<CmsTagDto>;
    bulkCreate(createDtos: CreateCmsTagDto[], userId: string): Promise<CmsTagDto[]>;
    createFromName(name: string, userId: string): Promise<CmsTagDto>;
    findOrCreate(name: string, userId: string): Promise<CmsTagDto>;
    createFromNames(names: string[], userId: string): Promise<CmsTagDto[]>;
}

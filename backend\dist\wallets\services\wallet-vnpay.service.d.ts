import { VnpayPaymentService } from '../../payment-gateways/services/vnpay-payment.service';
import { WalletService } from './wallet.service';
import { WalletTransactionService } from './wallet-transaction.service';
import { PaymentEventHandler } from '../../payment-gateways/interfaces/payment-integration.interface';
export interface DepositRequest {
    userId: number;
    amount: number;
    description?: string;
    clientIp: string;
    bankCode?: string;
}
export interface DepositResponse {
    paymentUrl: string;
    merchantTxnRef: string;
    amount: number;
    expiresAt: Date;
}
export declare class WalletVnpayService implements PaymentEventHandler {
    private readonly vnpayPaymentService;
    private readonly walletService;
    private readonly walletTransactionService;
    private readonly logger;
    constructor(vnpayPaymentService: VnpayPaymentService, walletService: WalletService, walletTransactionService: WalletTransactionService);
    createDeposit(request: DepositRequest): Promise<DepositResponse>;
    onPaymentCreated(data: {
        merchantTxnRef: string;
        externalRef: string;
        amount: number;
        paymentUrl: string;
    }): Promise<void>;
    onPaymentSuccess(data: {
        merchantTxnRef: string;
        externalRef: string;
        amount: number;
        vnpayTxnRef: string;
        vnpayTxnNo: string;
        bankCode?: string;
        payDate: string;
    }): Promise<void>;
    onPaymentFailed(data: {
        merchantTxnRef: string;
        externalRef: string;
        amount: number;
        responseCode: string;
        message: string;
    }): Promise<void>;
    onPaymentCancelled(data: {
        merchantTxnRef: string;
        externalRef: string;
        amount: number;
    }): Promise<void>;
    private validateDepositRequest;
    getDepositStatus(merchantTxnRef: string): Promise<any>;
}

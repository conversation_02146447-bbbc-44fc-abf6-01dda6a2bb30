import { Repository, DataSource } from 'typeorm';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { BaseCmsPostsService } from './base.cms-posts.service';
import { CmsPosts, CmsPostStatus, CmsPostType } from '../entity/cms-posts.entity';
import { CmsPostDto } from '../dto/cms-post.dto';
import { CustomPaginationQueryDto } from '../../common/dto/custom-pagination-query.dto';
export declare class ReadCmsPostsService extends BaseCmsPostsService {
    protected readonly postRepository: Repository<CmsPosts>;
    protected readonly dataSource: DataSource;
    protected readonly eventEmitter: EventEmitter2;
    constructor(postRepository: Repository<CmsPosts>, dataSource: DataSource, eventEmitter: EventEmitter2);
    findAll(params: CustomPaginationQueryDto): Promise<{
        data: CmsPostDto[];
        total: number;
    }>;
    findByPostType(postType: CmsPostType, params: CustomPaginationQueryDto): Promise<{
        data: CmsPostDto[];
        total: number;
    }>;
    findByStatus(status: CmsPostStatus, params: CustomPaginationQueryDto): Promise<{
        data: CmsPostDto[];
        total: number;
    }>;
    findPublished(params: CustomPaginationQueryDto): Promise<{
        data: CmsPostDto[];
        total: number;
    }>;
    findDrafts(params: CustomPaginationQueryDto): Promise<{
        data: CmsPostDto[];
        total: number;
    }>;
    search(keyword: string, params: CustomPaginationQueryDto): Promise<{
        data: CmsPostDto[];
        total: number;
    }>;
    count(filter?: string): Promise<number>;
    findOne(id: string, relations?: string[]): Promise<CmsPostDto | null>;
    findOneOrFail(id: string, relations?: string[]): Promise<CmsPostDto | null>;
    findBySlugPublic(slug: string, postType?: CmsPostType, incrementView?: boolean): Promise<CmsPostDto | null>;
    findDeleted(params: CustomPaginationQueryDto): Promise<{
        data: CmsPostDto[];
        total: number;
    }>;
    getStatistics(): Promise<{
        total: number;
        statusCounts: Record<CmsPostStatus, number>;
        typeCounts: Record<CmsPostType, number>;
        publishedToday: number;
        totalViews: number;
    }>;
    findByCategorySlug(categorySlug: string, params: CustomPaginationQueryDto & {
        postType?: CmsPostType;
    }): Promise<{
        data: CmsPostDto[];
        total: number;
    }>;
    findByTagSlug(tagSlug: string, params: CustomPaginationQueryDto & {
        postType?: CmsPostType;
    }): Promise<{
        data: CmsPostDto[];
        total: number;
    }>;
}

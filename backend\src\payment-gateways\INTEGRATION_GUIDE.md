# VNPAY Payment Gateway Integration Guide

## Tổng quan

VNPAY PaymentGatewaysService đã đư<PERSON>c thiết kế để **trong suốt và có thể tái sử dụng** cho nhiều dự án khác nhau. Service này tập trung tối ưu hóa cho VNPAY và cung cấp:

- **VNPAY Optimized** - Tối ưu hóa hoàn toàn cho VNPAY gateway
- **Callback System** - Hệ thống callback linh hoạt cho custom business logic
- **Standardized Response** - Response chuẩn hóa cho VNPAY
- **Transaction Management** - Quản lý giao dịch tự động
- **Event System** - Phát sự kiện cho integration với hệ thống khác
- **Copy-ready** - D<PERSON> dàng copy sang dự án khác

## Backend Integration

### 1. Environment Configuration

```env
# VNPAY Configuration
VNPAY_TMN_CODE=your_tmn_code
VNPAY_HASH_SECRET=your_hash_secret
VNPAY_URL=https://sandbox.vnpayment.vn/paymentv2/vpcpay.html
VNPAY_API_URL=https://sandbox.vnpayment.vn/merchant_webapi/api/transaction
VNPAY_RETURN_URL=https://yourdomain.com/api/payment-gateways/vnpay/return
VNPAY_IPN_URL=https://yourdomain.com/api/payment-gateways/vnpay/ipn

# Frontend URLs
FRONTEND_URL=https://yourdomain.com
```

### 2. Copy Module to New Project

```bash
# Copy entire module
cp -r backend/src/payment-gateways /path/to/new-project/src/

# Install dependencies in new project
npm install @nestjs/common @nestjs/core @nestjs/typeorm typeorm
npm install @nestjs/axios @nestjs/event-emitter
npm install class-validator class-transformer
npm install @nestjs/swagger
```

### 3. Basic Usage

```typescript
import { PaymentGatewaysService, PaymentCallbacks } from './payment-gateways.service';

@Injectable()
export class YourService {
  constructor(
    private readonly paymentService: PaymentGatewaysService
  ) {}

  async createVnpayPayment(userId: string, walletId: string, amount: number) {
    // Custom callbacks cho business logic
    const callbacks: PaymentCallbacks = {
      onPaymentCreated: async (result) => {
        // Gửi email confirmation
        await this.emailService.sendPaymentCreated(userId, result);
      },
      onPaymentSuccess: async (result) => {
        // Cập nhật user level, unlock features, etc.
        await this.userService.updateAfterPayment(userId, result.amount);
      },
      onPaymentFailed: async (result) => {
        // Log failure, send notification
        await this.notificationService.sendPaymentFailed(userId, result.message);
      }
    };

    return await this.paymentService.createPayment({
      userId,
      walletId,
      amount,
      gatewayType: PaymentGatewayType.VNPAY,
      description: 'Nạp tiền vào ví',
      ipAddress: '127.0.0.1'
    }, callbacks);
  }
}
```

### 4. Controller Integration

```typescript
@Controller('payments')
export class PaymentsController {
  constructor(private readonly paymentService: PaymentGatewaysService) {}

  @Post('create')
  async createPayment(@Body() dto: CreatePaymentDto) {
    // Validate VNPAY only
    if (dto.gatewayType !== PaymentGatewayType.VNPAY) {
      throw new BadRequestException('Chỉ hỗ trợ VNPAY');
    }
    
    return await this.paymentService.createPayment(dto);
  }

  @Get('vnpay/return')
  async vnpayReturn(@Query() query: any, @Res() res: Response) {
    const result = await this.paymentService.handleVnpayCallback(query);
    
    if (result.isSuccess) {
      res.redirect(`${process.env.FRONTEND_URL}/payment/success`);
    } else {
      res.redirect(`${process.env.FRONTEND_URL}/payment/failure`);
    }
  }

  @Post('vnpay/ipn')
  async vnpayIpn(@Query() query: any) {
    const result = await this.paymentService.handleVnpayIpn(query);
    return { RspCode: result.code, Message: result.message };
  }
}
```

## Frontend Integration

### 1. VNPAY Payment Flow

```typescript
// 1. Tạo VNPAY payment URL
const createVnpayPayment = async (paymentData: {
  userId: string;
  walletId: string;
  amount: number;
  description?: string;
}) => {
  try {
    const response = await fetch('/api/payment-gateways/create-payment', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${accessToken}`
      },
      body: JSON.stringify({
        ...paymentData,
        gatewayType: 'VNPAY',
        ipAddress: '127.0.0.1'
      })
    });

    const result = await response.json();
    
    if (result.paymentUrl) {
      // Redirect user đến VNPAY
      window.location.href = result.paymentUrl;
    }
  } catch (error) {
    console.error('VNPAY payment creation failed:', error);
  }
};
```

### 2. React/Next.js VNPAY Component

```tsx
// components/VnpayPaymentButton.tsx
import { useState } from 'react';

interface VnpayPaymentButtonProps {
  userId: string;
  walletId: string;
  amount: number;
  onSuccess?: (result: any) => void;
  onError?: (error: any) => void;
}

export const VnpayPaymentButton: React.FC<VnpayPaymentButtonProps> = ({
  userId,
  walletId,
  amount,
  onSuccess,
  onError
}) => {
  const [loading, setLoading] = useState(false);

  const handleVnpayPayment = async () => {
    setLoading(true);
    
    try {
      const response = await fetch('/api/payment-gateways/create-payment', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('accessToken')}`
        },
        body: JSON.stringify({
          userId,
          walletId,
          amount,
          gatewayType: 'VNPAY',
          description: `Nạp ${amount.toLocaleString()} VND qua VNPAY`,
          ipAddress: '127.0.0.1'
        })
      });

      const result = await response.json();
      
      if (result.paymentUrl) {
        // Redirect đến VNPAY
        window.location.href = result.paymentUrl;
      } else {
        throw new Error('Không thể tạo URL thanh toán VNPAY');
      }
    } catch (error) {
      console.error('VNPAY payment error:', error);
      onError?.(error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <button 
      onClick={handleVnpayPayment}
      disabled={loading}
      className="bg-blue-500 text-white px-6 py-3 rounded-lg disabled:opacity-50 flex items-center gap-2"
    >
      {loading ? (
        <>
          <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
          Đang xử lý...
        </>
      ) : (
        <>
          <img src="/vnpay-logo.png" alt="VNPAY" className="h-5 w-5" />
          Thanh toán {amount.toLocaleString()} VND qua VNPAY
        </>
      )}
    </button>
  );
};
```

### 3. Payment Success/Failure Pages

```tsx
// pages/payment/success.tsx
import { useEffect, useState } from 'react';
import { useRouter } from 'next/router';

export default function VnpayPaymentSuccess() {
  const router = useRouter();
  const { transactionId, amount } = router.query;

  return (
    <div className="container mx-auto p-6">
      <div className="bg-green-50 border border-green-200 rounded-lg p-6">
        <h1 className="text-2xl font-bold text-green-800 mb-4">
          ✅ Thanh toán VNPAY thành công!
        </h1>
        
        <div className="space-y-2">
          <p><strong>Mã giao dịch:</strong> {transactionId}</p>
          <p><strong>Số tiền:</strong> {Number(amount).toLocaleString()} VND</p>
          <p><strong>Phương thức:</strong> VNPAY</p>
          <p><strong>Thời gian:</strong> {new Date().toLocaleString()}</p>
        </div>

        <div className="mt-6">
          <button 
            onClick={() => router.push('/wallet')}
            className="bg-blue-500 text-white px-4 py-2 rounded mr-4"
          >
            Xem ví của tôi
          </button>
          <button 
            onClick={() => router.push('/')}
            className="bg-gray-500 text-white px-4 py-2 rounded"
          >
            Về trang chủ
          </button>
        </div>
      </div>
    </div>
  );
}
```

## Advanced VNPAY Features

### 1. Custom Business Logic với Callbacks

```typescript
const vnpayCallbacks: PaymentCallbacks = {
  onPaymentCreated: async (result) => {
    // Log analytics cho VNPAY
    await analytics.track('vnpay_payment_initiated', {
      transactionId: result.transactionId,
      amount: result.amount
    });
  },

  onPaymentSuccess: async (result) => {
    // Multiple business actions cho VNPAY success
    await Promise.all([
      // Cập nhật user tier
      userService.updateTier(result.userId, result.amount),
      
      // Gửi SMS confirmation
      smsService.sendVnpaySuccess(result.userId, result.amount),
      
      // Cập nhật loyalty points
      loyaltyService.addPoints(result.userId, result.amount * 0.01),
      
      // Log to analytics
      analytics.track('vnpay_payment_completed', result)
    ]);
  },

  onPaymentFailed: async (result) => {
    // VNPAY specific error tracking
    await errorTracker.captureVnpayFailure({
      transactionId: result.transactionId,
      reason: result.message,
      amount: result.amount,
      responseCode: result.responseCode
    });
  }
};
```

### 2. VNPAY Query và Refund

```typescript
// Query VNPAY transaction status
const queryVnpayResult = await paymentService.queryTransaction(
  PaymentGatewayType.VNPAY,
  {
    txnRef: 'vnpay_transaction_id',
    transactionDate: '20231207153333',
    orderInfo: 'Query VNPAY transaction',
    ipAddr: '127.0.0.1'
  }
);

// Refund VNPAY transaction
const refundVnpayResult = await paymentService.refundTransaction(
  PaymentGatewayType.VNPAY,
  {
    txnRef: 'vnpay_transaction_id',
    amount: 50000,
    orderInfo: 'Refund VNPAY payment',
    transactionDate: '20231207153333',
    transactionType: '02', // Full refund
    createBy: 'admin_user',
    ipAddr: '127.0.0.1'
  }
);
```

## Copy to New Project Checklist

### ✅ Files to Copy
- [ ] Copy entire `payment-gateways/` directory
- [ ] Update import paths if needed
- [ ] Add to main app module

### ✅ Environment Setup
- [ ] Set VNPAY credentials
- [ ] Configure return/IPN URLs
- [ ] Set frontend URL

### ✅ Database Setup
- [ ] Ensure Transaction entity exists
- [ ] Ensure Wallet entity exists
- [ ] Run migrations if needed

### ✅ Dependencies
- [ ] Install required NestJS packages
- [ ] Install validation packages
- [ ] Install HTTP client packages

### ✅ Testing
- [ ] Test payment creation
- [ ] Test VNPAY sandbox
- [ ] Test callbacks and IPN
- [ ] Test query and refund

## Production Deployment

### Security
- Use production VNPAY credentials
- Enable HTTPS for all URLs
- Validate VNPAY IP whitelist
- Implement rate limiting

### Monitoring
- Monitor payment success rates
- Track VNPAY response times
- Alert on payment failures
- Log all VNPAY interactions

### Performance
- Cache VNPAY configurations
- Optimize database queries
- Use connection pooling
- Monitor memory usage

**Module này đã được tối ưu hóa hoàn toàn cho VNPAY và sẵn sàng copy sang dự án khác!** 🚀

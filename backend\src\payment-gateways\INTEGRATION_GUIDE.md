# Payment Gateway Integration Guide

## Tổng quan

Service PaymentGatewaysService đã được thiết kế để **trong suốt và có thể tái sử dụng** cho nhiều dự án khác nhau. Service này cung cấp:

- **Unified Interface** - Giao diện thống nhất cho tất cả payment gateways
- **Callback System** - Hệ thống callback linh hoạt cho custom business logic
- **Standardized Response** - Response chuẩn hóa cho tất cả gateways
- **Transaction Management** - Quản lý giao dịch tự động
- **Event System** - <PERSON><PERSON><PERSON> sự kiện cho integration với hệ thống khác

## Backend Integration

### 1. Environment Configuration

```env
# VNPAY Configuration
VNPAY_TMN_CODE=your_tmn_code
VNPAY_HASH_SECRET=your_hash_secret
VNPAY_URL=https://sandbox.vnpayment.vn/paymentv2/vpcpay.html
VNPAY_API_URL=https://sandbox.vnpayment.vn/merchant_webapi/api/transaction
VNPAY_RETURN_URL=https://yourdomain.com/api/payment-gateways/vnpay/return
VNPAY_IPN_URL=https://yourdomain.com/api/payment-gateways/vnpay/ipn

# Frontend URLs
FRONTEND_URL=https://yourdomain.com
```

### 2. Basic Usage

```typescript
import { PaymentGatewaysService, PaymentCallbacks } from './payment-gateways.service';

@Injectable()
export class YourService {
  constructor(
    private readonly paymentService: PaymentGatewaysService
  ) {}

  async createPayment(userId: string, walletId: string, amount: number) {
    // Custom callbacks cho business logic
    const callbacks: PaymentCallbacks = {
      onPaymentCreated: async (result) => {
        // Gửi email confirmation
        await this.emailService.sendPaymentCreated(userId, result);
      },
      onPaymentSuccess: async (result) => {
        // Cập nhật user level, unlock features, etc.
        await this.userService.updateAfterPayment(userId, result.amount);
      },
      onPaymentFailed: async (result) => {
        // Log failure, send notification
        await this.notificationService.sendPaymentFailed(userId, result.message);
      }
    };

    return await this.paymentService.createPayment({
      userId,
      walletId,
      amount,
      gatewayType: PaymentGatewayType.VNPAY,
      description: 'Nạp tiền vào ví',
      ipAddress: '127.0.0.1'
    }, callbacks);
  }
}
```

### 3. Controller Integration

```typescript
@Controller('payments')
export class PaymentsController {
  constructor(private readonly paymentService: PaymentGatewaysService) {}

  @Post('create')
  async createPayment(@Body() dto: CreatePaymentDto) {
    return await this.paymentService.createPayment(dto);
  }

  @Get('vnpay/return')
  async vnpayReturn(@Query() query: any, @Res() res: Response) {
    const result = await this.paymentService.handleVnpayCallback(query);
    
    if (result.isSuccess) {
      res.redirect(`${process.env.FRONTEND_URL}/payment/success`);
    } else {
      res.redirect(`${process.env.FRONTEND_URL}/payment/failure`);
    }
  }

  @Post('vnpay/ipn')
  async vnpayIpn(@Query() query: any) {
    const result = await this.paymentService.handleVnpayIpn(query);
    return { RspCode: result.code, Message: result.message };
  }
}
```

## Frontend Integration

### 1. Payment Flow

```typescript
// 1. Tạo payment URL
const createPayment = async (paymentData: {
  userId: string;
  walletId: string;
  amount: number;
  gatewayType: 'VNPAY' | 'MOMO';
  description?: string;
}) => {
  try {
    const response = await fetch('/api/payment-gateways/create-payment', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${accessToken}`
      },
      body: JSON.stringify(paymentData)
    });

    const result = await response.json();
    
    if (result.paymentUrl) {
      // Redirect user đến payment gateway
      window.location.href = result.paymentUrl;
    }
  } catch (error) {
    console.error('Payment creation failed:', error);
  }
};

// 2. Xử lý return từ payment gateway
const handlePaymentReturn = () => {
  const urlParams = new URLSearchParams(window.location.search);
  const status = urlParams.get('status');
  const transactionId = urlParams.get('transactionId');
  const amount = urlParams.get('amount');

  if (status === 'success') {
    showSuccessMessage(`Thanh toán thành công! Số tiền: ${amount} VND`);
    // Redirect về trang chính hoặc cập nhật UI
  } else {
    showErrorMessage('Thanh toán thất bại!');
  }
};
```

### 2. React/Next.js Integration

```tsx
// components/PaymentButton.tsx
import { useState } from 'react';

interface PaymentButtonProps {
  userId: string;
  walletId: string;
  amount: number;
  gatewayType: 'VNPAY' | 'MOMO';
  onSuccess?: (result: any) => void;
  onError?: (error: any) => void;
}

export const PaymentButton: React.FC<PaymentButtonProps> = ({
  userId,
  walletId,
  amount,
  gatewayType,
  onSuccess,
  onError
}) => {
  const [loading, setLoading] = useState(false);

  const handlePayment = async () => {
    setLoading(true);
    
    try {
      const response = await fetch('/api/payment-gateways/create-payment', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('accessToken')}`
        },
        body: JSON.stringify({
          userId,
          walletId,
          amount,
          gatewayType,
          description: `Nạp ${amount.toLocaleString()} VND vào ví`,
          ipAddress: '127.0.0.1' // Có thể lấy từ client
        })
      });

      const result = await response.json();
      
      if (result.paymentUrl) {
        // Redirect đến payment gateway
        window.location.href = result.paymentUrl;
      } else {
        throw new Error('Không thể tạo URL thanh toán');
      }
    } catch (error) {
      console.error('Payment error:', error);
      onError?.(error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <button 
      onClick={handlePayment}
      disabled={loading}
      className="bg-blue-500 text-white px-4 py-2 rounded disabled:opacity-50"
    >
      {loading ? 'Đang xử lý...' : `Thanh toán ${amount.toLocaleString()} VND`}
    </button>
  );
};
```

### 3. Payment Status Pages

```tsx
// pages/payment/success.tsx
import { useEffect, useState } from 'react';
import { useRouter } from 'next/router';

export default function PaymentSuccess() {
  const router = useRouter();
  const { transactionId, amount } = router.query;
  const [transactionDetails, setTransactionDetails] = useState(null);

  useEffect(() => {
    if (transactionId) {
      // Lấy chi tiết giao dịch từ API
      fetchTransactionDetails(transactionId as string);
    }
  }, [transactionId]);

  const fetchTransactionDetails = async (txnId: string) => {
    try {
      const response = await fetch(`/api/payment-gateways/transaction/${txnId}/status`);
      const details = await response.json();
      setTransactionDetails(details);
    } catch (error) {
      console.error('Error fetching transaction details:', error);
    }
  };

  return (
    <div className="container mx-auto p-6">
      <div className="bg-green-50 border border-green-200 rounded-lg p-6">
        <h1 className="text-2xl font-bold text-green-800 mb-4">
          ✅ Thanh toán thành công!
        </h1>
        
        <div className="space-y-2">
          <p><strong>Mã giao dịch:</strong> {transactionId}</p>
          <p><strong>Số tiền:</strong> {Number(amount).toLocaleString()} VND</p>
          <p><strong>Thời gian:</strong> {new Date().toLocaleString()}</p>
        </div>

        <div className="mt-6">
          <button 
            onClick={() => router.push('/wallet')}
            className="bg-blue-500 text-white px-4 py-2 rounded mr-4"
          >
            Xem ví của tôi
          </button>
          <button 
            onClick={() => router.push('/')}
            className="bg-gray-500 text-white px-4 py-2 rounded"
          >
            Về trang chủ
          </button>
        </div>
      </div>
    </div>
  );
}
```

## Advanced Features

### 1. Custom Business Logic với Callbacks

```typescript
const customCallbacks: PaymentCallbacks = {
  onPaymentCreated: async (result) => {
    // Log analytics
    await analytics.track('payment_initiated', {
      userId: result.transactionId,
      amount: result.amount,
      gateway: result.gatewayType
    });
  },

  onPaymentSuccess: async (result) => {
    // Multiple business actions
    await Promise.all([
      // Cập nhật user tier
      userService.updateTier(result.userId, result.amount),
      
      // Gửi notification
      notificationService.send(result.userId, {
        title: 'Thanh toán thành công',
        body: `Bạn đã nạp thành công ${result.amount} VND`
      }),
      
      // Cập nhật analytics
      analytics.track('payment_completed', result),
      
      // Trigger other services
      loyaltyService.addPoints(result.userId, result.amount * 0.01)
    ]);
  },

  onPaymentFailed: async (result) => {
    // Error tracking
    await errorTracker.capturePaymentFailure({
      transactionId: result.transactionId,
      reason: result.message,
      gateway: result.gatewayType,
      amount: result.amount
    });
  }
};
```

### 2. Query và Refund

```typescript
// Query transaction status
const queryResult = await paymentService.queryTransaction(
  PaymentGatewayType.VNPAY,
  {
    txnRef: 'transaction_id',
    transactionDate: '20231207153333',
    orderInfo: 'Query transaction',
    ipAddr: '127.0.0.1'
  }
);

// Refund transaction
const refundResult = await paymentService.refundTransaction(
  PaymentGatewayType.VNPAY,
  {
    txnRef: 'transaction_id',
    amount: 50000,
    orderInfo: 'Refund for order cancellation',
    transactionDate: '20231207153333',
    transactionType: '02', // Full refund
    createBy: 'admin_user',
    ipAddr: '127.0.0.1'
  }
);
```

## Testing

### 1. Unit Tests

```typescript
describe('PaymentGatewaysService', () => {
  it('should create payment URL successfully', async () => {
    const result = await service.createPayment(mockPaymentDto);
    expect(result.paymentUrl).toBeDefined();
    expect(result.transactionId).toBeDefined();
  });

  it('should handle callback correctly', async () => {
    const result = await service.handleVnpayCallback(mockVnpayParams);
    expect(result.isValid).toBe(true);
    expect(result.isSuccess).toBe(true);
  });
});
```

### 2. Integration Tests

```typescript
describe('Payment Flow Integration', () => {
  it('should complete full payment flow', async () => {
    // 1. Create payment
    const payment = await service.createPayment(paymentDto);
    
    // 2. Simulate gateway callback
    const callback = await service.handleVnpayCallback(mockSuccessParams);
    
    // 3. Verify transaction status
    expect(callback.isSuccess).toBe(true);
    
    // 4. Verify wallet balance updated
    const wallet = await walletService.findById(paymentDto.walletId);
    expect(wallet.balance).toBe(originalBalance + paymentDto.amount);
  });
});
```

## Deployment Notes

1. **SSL Required**: IPN URLs phải có SSL (HTTPS)
2. **Firewall**: Mở port cho webhook callbacks
3. **Environment Variables**: Đảm bảo tất cả env vars được set
4. **Database**: Đảm bảo transaction tables có indexes phù hợp
5. **Monitoring**: Setup monitoring cho payment failures và timeouts

## Security Considerations

1. **Signature Validation**: Luôn validate signature từ gateway
2. **IP Whitelist**: Chỉ accept callbacks từ gateway IPs
3. **Rate Limiting**: Implement rate limiting cho payment endpoints
4. **Logging**: Log tất cả payment activities cho audit
5. **Error Handling**: Không expose sensitive information trong error messages

import { CreateCmsPartnersService } from '../services/create.cms-partners.service';
import { CreateCmsPartnerDto, BulkCreateCmsPartnersDto, CreateCmsPartnerFromTemplateDto, DuplicateCmsPartnerDto, ImportCmsPartnersDto, BulkOperationResponseDto, CmsPartnerDto } from '../dto';
export declare class CreateCmsPartnersController {
    private readonly createCmsPartnersService;
    private readonly logger;
    constructor(createCmsPartnersService: CreateCmsPartnersService);
    create(createCmsPartnerDto: CreateCmsPartnerDto, req: any): Promise<CmsPartnerDto>;
    bulkCreate(bulkCreateCmsPartnersDto: BulkCreateCmsPartnersDto, req: any): Promise<BulkOperationResponseDto>;
    createFromTemplate(createFromTemplateDto: CreateCmsPartnerFromTemplateDto, req: any): Promise<CmsPartnerDto>;
    duplicate(id: string, duplicateDto: DuplicateCmsPartnerDto, req: any): Promise<CmsPartnerDto>;
    import(importDto: ImportCmsPartnersDto, req: any): Promise<BulkOperationResponseDto>;
}

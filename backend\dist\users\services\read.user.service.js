"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ReadUserService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const event_emitter_1 = require("@nestjs/event-emitter");
const base_user_service_1 = require("./base.user.service");
const user_entity_1 = require("../entities/user.entity");
const role_entity_1 = require("../../roles/entities/role.entity");
let ReadUserService = class ReadUserService extends base_user_service_1.BaseUserService {
    userRepository;
    roleRepository;
    eventEmitter;
    constructor(userRepository, roleRepository, eventEmitter) {
        super(userRepository, roleRepository, eventEmitter);
        this.userRepository = userRepository;
        this.roleRepository = roleRepository;
        this.eventEmitter = eventEmitter;
    }
    async findAll(params) {
        const { limit, page, sortBy = 'createdAt', sortOrder = 'DESC', filter, relations = [], } = params;
        const skip = (page - 1) * limit;
        try {
            const validRelationsToLoad = [
                'userRoles',
                'userRoles.role',
                'creator',
                'updater',
                'deleter',
                ...relations.filter((rel) => this.validRelations.includes(rel)),
            ];
            const query = this.userRepository
                .createQueryBuilder('user')
                .where('user.isDeleted = :isDeleted', { isDeleted: false });
            if (validRelationsToLoad.includes('userRoles')) {
                query
                    .leftJoinAndSelect('user.userRoles', 'userRoles', 'userRoles.isDeleted = :userRolesDeleted', { userRolesDeleted: false })
                    .leftJoinAndSelect('userRoles.role', 'role')
                    .leftJoinAndSelect('role.rolePermissions', 'rolePermissions', 'rolePermissions.isDeleted = :rolePermissionsDeleted', { rolePermissionsDeleted: false })
                    .leftJoinAndSelect('rolePermissions.permission', 'permission');
            }
            if (validRelationsToLoad.includes('creator')) {
                query.leftJoinAndSelect('user.creator', 'creator');
            }
            if (validRelationsToLoad.includes('updater')) {
                query.leftJoinAndSelect('user.updater', 'updater');
            }
            if (validRelationsToLoad.includes('deleter')) {
                query.leftJoinAndSelect('user.deleter', 'deleter');
            }
            if (validRelationsToLoad.includes('referredBy')) {
                query.leftJoinAndSelect('user.referredBy', 'referredBy', 'referredBy.isDeleted = :referredByDeleted', { referredByDeleted: false });
            }
            if (validRelationsToLoad.includes('referrals')) {
                query.leftJoinAndSelect('user.referrals', 'referrals', 'referrals.isDeleted = :referralsDeleted', { referralsDeleted: false });
            }
            if (filter) {
                try {
                    if (filter.includes(':')) {
                        const [field, value] = filter.split(':');
                        if (this.userRepository.metadata.ownColumns.some((col) => col.propertyName === field)) {
                            query.andWhere(`user.${field} = :value`, { value });
                        }
                    }
                    else {
                        query.andWhere('(user.username LIKE :search OR user.email LIKE :search OR user.fullName LIKE :search)', { search: `%${filter}%` });
                    }
                }
                catch (e) {
                    this.logger.warn(`Ignoring invalid filter format: ${filter}`);
                }
            }
            query.orderBy(`user.${sortBy}`, sortOrder);
            query.skip(skip).take(limit);
            const [users, total] = await query.getManyAndCount();
            this.logger.debug(`Loaded users: ${users.length}`);
            const userDtos = users.map((user) => this.convertToDto(user));
            return {
                data: userDtos,
                total,
            };
        }
        catch (error) {
            this.logger.error(`Failed to fetch users: ${error.message}`, error.stack);
            throw new common_1.InternalServerErrorException('Failed to fetch users.');
        }
    }
    async findOne(id, relations = []) {
        this.logger.log(`Finding user by ID: ${id}`);
        try {
            const validRelationsToLoad = [
                'userRoles',
                'userRoles.role',
                'creator',
                'updater',
                'deleter',
                ...relations.filter((rel) => this.validRelations.includes(rel)),
            ];
            const query = this.userRepository
                .createQueryBuilder('user')
                .where('user.id = :id', { id })
                .andWhere('user.isDeleted = :isDeleted', { isDeleted: false });
            if (validRelationsToLoad.includes('userRoles')) {
                query
                    .leftJoinAndSelect('user.userRoles', 'userRoles', 'userRoles.isDeleted = :userRolesDeleted', { userRolesDeleted: false })
                    .leftJoinAndSelect('userRoles.role', 'role')
                    .leftJoinAndSelect('role.rolePermissions', 'rolePermissions', 'rolePermissions.isDeleted = :rolePermissionsDeleted', { rolePermissionsDeleted: false })
                    .leftJoinAndSelect('rolePermissions.permission', 'permission');
            }
            if (validRelationsToLoad.includes('creator')) {
                query.leftJoinAndSelect('user.creator', 'creator');
            }
            if (validRelationsToLoad.includes('updater')) {
                query.leftJoinAndSelect('user.updater', 'updater');
            }
            if (validRelationsToLoad.includes('deleter')) {
                query.leftJoinAndSelect('user.deleter', 'deleter');
            }
            if (validRelationsToLoad.includes('referredBy')) {
                query.leftJoinAndSelect('user.referredBy', 'referredBy', 'referredBy.isDeleted = :referredByDeleted', { referredByDeleted: false });
            }
            if (validRelationsToLoad.includes('referrals')) {
                query.leftJoinAndSelect('user.referrals', 'referrals', 'referrals.isDeleted = :referralsDeleted', { referralsDeleted: false });
            }
            const user = await query.getOne();
            if (!user) {
                this.logger.warn(`User with ID ${id} not found`);
                throw new common_1.NotFoundException(`User with ID ${id} not found`);
            }
            const roles = user.userRoles?.map((ur) => ur.role.name) || [];
            this.logger.debug(`Loaded user with roles: ${JSON.stringify(roles)}`);
            return this.convertToDto(user);
        }
        catch (error) {
            this.logger.error(`Error finding user: ${error.message}`, error.stack);
            if (error instanceof common_1.NotFoundException)
                throw error;
            throw new common_1.InternalServerErrorException('Failed to find user.');
        }
    }
    async findOneOrFail(id, relations = []) {
        this.logger.log(`Finding user by ID or failing: ${id}`);
        const user = await this.findByIdOrFail(id, relations, false);
        return this.convertToDto(user);
    }
    async findDeleted(params) {
        const { limit, page } = params;
        const skip = (page - 1) * limit;
        try {
            const [users, total] = await this.userRepository.findAndCount({
                where: { isDeleted: true },
                withDeleted: true,
                skip,
                take: limit,
                order: { deletedAt: 'DESC' },
            });
            return {
                data: this.convertToDtoArray(users),
                total,
            };
        }
        catch (error) {
            this.logger.error(`Failed to fetch deleted users: ${error.message}`, error.stack);
            throw new common_1.InternalServerErrorException('Failed to fetch deleted users.');
        }
    }
    async findByUsername(username) {
        this.logger.log(`Finding user by username: ${username}`);
        try {
            const user = await this.userRepository.findOne({
                where: { username, isDeleted: false },
            });
            if (!user) {
                throw new common_1.NotFoundException(`User with username ${username} not found`);
            }
            return user;
        }
        catch (error) {
            this.logger.error(`Error finding user by username ${username}: ${error.message}`, error.stack);
            if (error instanceof common_1.NotFoundException)
                throw error;
            throw new common_1.InternalServerErrorException('Error fetching user by username.');
        }
    }
    async findByEmail(email) {
        this.logger.log(`Finding user by email: ${email}`);
        try {
            const user = await this.userRepository.findOne({
                where: { email, isDeleted: false },
            });
            if (!user) {
                throw new common_1.NotFoundException(`User with email ${email} not found`);
            }
            return this.convertToDto(user);
        }
        catch (error) {
            this.logger.error(`Error finding user by email ${email}: ${error.message}`, error.stack);
            if (error instanceof common_1.NotFoundException)
                throw error;
            throw new common_1.InternalServerErrorException('Error fetching user by email.');
        }
    }
    async findByResetToken(resetToken) {
        this.logger.log(`Finding user by reset token`);
        try {
            const user = await this.userRepository.findOne({
                where: { resetToken, isDeleted: false },
            });
            if (!user) {
                throw new common_1.NotFoundException(`User with reset token not found`);
            }
            return user;
        }
        catch (error) {
            this.logger.error(`Error finding user by reset token: ${error.message}`, error.stack);
            if (error instanceof common_1.NotFoundException)
                throw error;
            throw new common_1.InternalServerErrorException('Error fetching user by reset token.');
        }
    }
    async findByVerificationToken(verificationToken) {
        this.logger.log(`Finding user by verification token`);
        try {
            const user = await this.userRepository.findOne({
                where: { verificationToken, isDeleted: false },
            });
            if (!user) {
                throw new common_1.NotFoundException(`User with verification token not found`);
            }
            return user;
        }
        catch (error) {
            this.logger.error(`Error finding user by verification token: ${error.message}`, error.stack);
            if (error instanceof common_1.NotFoundException)
                throw error;
            throw new common_1.InternalServerErrorException('Error fetching user by verification token.');
        }
    }
    async search(keyword, params) {
        const { limit, page } = params;
        const skip = (page - 1) * limit;
        const searchTerm = `%${keyword}%`;
        try {
            const query = this.userRepository
                .createQueryBuilder('user')
                .leftJoinAndSelect('user.userRoles', 'userRoles')
                .leftJoinAndSelect('userRoles.role', 'role')
                .leftJoinAndSelect('user.tokenAssets', 'tokenAssets')
                .leftJoinAndSelect('user.creator', 'creator')
                .leftJoinAndSelect('user.updater', 'updater')
                .leftJoinAndSelect('user.deleter', 'deleter')
                .where('user.isDeleted = :isDeleted', { isDeleted: false })
                .andWhere(new typeorm_2.Brackets((qb) => {
                qb.where('LOWER(user.username) LIKE LOWER(:searchTerm)', {
                    searchTerm,
                })
                    .orWhere('LOWER(user.email) LIKE LOWER(:searchTerm)', {
                    searchTerm,
                })
                    .orWhere('LOWER(user.fullName) LIKE LOWER(:searchTerm)', {
                    searchTerm,
                })
                    .orWhere('LOWER(user.phone) LIKE LOWER(:searchTerm)', {
                    searchTerm,
                })
                    .orWhere('LOWER(user.address) LIKE LOWER(:searchTerm)', {
                    searchTerm,
                });
            }))
                .orderBy('user.createdAt', 'DESC')
                .skip(skip)
                .take(limit);
            const [users, total] = await query.getManyAndCount();
            return {
                data: this.convertToDtoArray(users),
                total,
            };
        }
        catch (error) {
            this.logger.error(`Failed to search users: ${error.message}`, error.stack);
            throw new common_1.InternalServerErrorException('Failed to search users.');
        }
    }
    async count(filter) {
        const query = this.userRepository
            .createQueryBuilder('user')
            .where({ isDeleted: false });
        if (filter) {
            const [field, value] = filter.split(':');
            query.andWhere(`user.${field} = :value`, { value });
        }
        return query.getCount();
    }
    async getStatistics() {
        try {
            this.logger.debug('Đang lấy thống kê người dùng theo trạng thái');
            const baseQueryBuilder = this.userRepository.createQueryBuilder('user')
                .where('user.isDeleted = :isDeleted', { isDeleted: false });
            const totalQueryBuilder = baseQueryBuilder.clone();
            const activeQueryBuilder = baseQueryBuilder.clone()
                .andWhere('user.isActive = :isActive', { isActive: true });
            const inactiveQueryBuilder = baseQueryBuilder.clone()
                .andWhere('user.isActive = :isActive', { isActive: false });
            const pendingQueryBuilder = baseQueryBuilder.clone()
                .andWhere('user.emailVerified = :emailVerified', { emailVerified: false });
            const [total, active, inactive, pending] = await Promise.all([
                totalQueryBuilder.getCount(),
                activeQueryBuilder.getCount(),
                inactiveQueryBuilder.getCount(),
                pendingQueryBuilder.getCount()
            ]);
            return {
                total,
                activeCounts: {
                    true: active,
                    false: inactive,
                    PENDING: pending
                }
            };
        }
        catch (error) {
            this.logger.error(`Lỗi khi lấy thống kê người dùng: ${error.message}`, error.stack);
            throw new common_1.InternalServerErrorException(`Không thể lấy thống kê người dùng: ${error.message}`);
        }
    }
};
exports.ReadUserService = ReadUserService;
exports.ReadUserService = ReadUserService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(user_entity_1.User)),
    __param(1, (0, typeorm_1.InjectRepository)(role_entity_1.Role)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        typeorm_2.Repository,
        event_emitter_1.EventEmitter2])
], ReadUserService);
//# sourceMappingURL=read.user.service.js.map
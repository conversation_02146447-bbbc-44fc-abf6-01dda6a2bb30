import { Repository } from 'typeorm';
import { OrderBook } from '../order-book/entities/order-book.entity';
import { EventEmitter2 } from '@nestjs/event-emitter';
export declare class OrderExpirationService {
    private readonly orderBookRepository;
    private readonly eventEmitter;
    private readonly logger;
    private readonly EVENT_ORDER_TERMINATED;
    constructor(orderBookRepository: Repository<OrderBook>, eventEmitter: EventEmitter2);
    checkExpiredOrders(): Promise<void>;
    private terminateExpiredOrderBook;
    manualCheckExpiredOrders(): Promise<void>;
}

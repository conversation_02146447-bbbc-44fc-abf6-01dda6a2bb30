"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.DeleteCmsMenusService = exports.UpdateCmsMenusService = exports.ReadCmsMenusService = exports.CreateCmsMenusService = exports.BaseCmsMenusService = void 0;
var base_cms_menus_service_1 = require("./base.cms-menus.service");
Object.defineProperty(exports, "BaseCmsMenusService", { enumerable: true, get: function () { return base_cms_menus_service_1.BaseCmsMenusService; } });
var create_cms_menus_service_1 = require("./create.cms-menus.service");
Object.defineProperty(exports, "CreateCmsMenusService", { enumerable: true, get: function () { return create_cms_menus_service_1.CreateCmsMenusService; } });
var read_cms_menus_service_1 = require("./read.cms-menus.service");
Object.defineProperty(exports, "ReadCmsMenusService", { enumerable: true, get: function () { return read_cms_menus_service_1.ReadCmsMenusService; } });
var update_cms_menus_service_1 = require("./update.cms-menus.service");
Object.defineProperty(exports, "UpdateCmsMenusService", { enumerable: true, get: function () { return update_cms_menus_service_1.UpdateCmsMenusService; } });
var delete_cms_menus_service_1 = require("./delete.cms-menus.service");
Object.defineProperty(exports, "DeleteCmsMenusService", { enumerable: true, get: function () { return delete_cms_menus_service_1.DeleteCmsMenusService; } });
//# sourceMappingURL=index.js.map
import { Logger } from '@nestjs/common';
import { Repository, DataSource } from 'typeorm';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { ActivityLog } from '../entities/activity-log.entity';
import { ActivityLogDto } from '../dto/activity-log.dto';
export declare class BaseActivityLogService {
    protected readonly activityLogRepository: Repository<ActivityLog>;
    protected readonly dataSource: DataSource;
    protected readonly eventEmitter: EventEmitter2;
    protected readonly logger: Logger;
    protected readonly validRelations: string[];
    protected readonly EVENT_ACTIVITY_LOG_CREATED = "activity-log.created";
    protected readonly EVENT_ACTIVITY_LOG_UPDATED = "activity-log.updated";
    protected readonly EVENT_ACTIVITY_LOG_DELETED = "activity-log.deleted";
    constructor(activityLogRepository: Repository<ActivityLog>, dataSource: DataSource, eventEmitter: EventEmitter2);
    protected getQueryRunner(): Promise<import("typeorm").QueryRunner>;
    protected validateRelations(relations: string[]): string[];
    protected findByIdOrFail(id: string, relations?: string[], withDeleted?: boolean): Promise<ActivityLog>;
    protected determineModule(action: string): string;
    protected toDto(activityLog: ActivityLog): ActivityLogDto;
}

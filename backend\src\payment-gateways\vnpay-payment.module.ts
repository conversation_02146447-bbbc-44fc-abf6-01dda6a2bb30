import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { TypeOrmModule } from '@nestjs/typeorm';
import { EventEmitterModule } from '@nestjs/event-emitter';
import { HttpModule } from '@nestjs/axios';

// Entities
import { VnpayTransaction } from './entities/vnpay-transaction.entity';

// Services
import { VnpayService } from './services/vnpay.service';
import { VnpayPaymentService } from './services/vnpay-payment.service';

// Repositories
import { VnpayTransactionRepository } from './repositories/vnpay-transaction.repository';

// Controllers
import { VnpayPaymentController } from './controllers/vnpay-payment.controller';

/**
 * Portable VNPAY Payment Module
 * 
 * This module is completely self-contained and can be copied to any NestJS project.
 * It has no dependencies on external entities or services.
 * 
 * Features:
 * - Complete VNPAY integration (Payment, Query, Refund, IPN)
 * - Independent transaction storage
 * - Event-driven architecture
 * - Comprehensive logging and error handling
 * - Statistics and reporting
 * - Transaction history and reconciliation
 * 
 * To use in a new project:
 * 1. Copy this entire module directory
 * 2. Add to your app.module.ts imports
 * 3. Configure environment variables
 * 4. Run database migrations
 * 5. Implement PaymentEventHandler in your application
 */
@Module({
  imports: [
    // Configuration
    ConfigModule,
    
    // Database
    TypeOrmModule.forFeature([VnpayTransaction]),
    
    // Events
    EventEmitterModule.forRoot(),
    
    // HTTP client for VNPAY API calls
    HttpModule,
  ],
  
  controllers: [
    VnpayPaymentController,
  ],
  
  providers: [
    // Core VNPAY service
    VnpayService,
    
    // Portable payment service
    VnpayPaymentService,
    
    // Transaction repository
    VnpayTransactionRepository,
  ],
  
  exports: [
    // Export main service for external use
    VnpayPaymentService,
    
    // Export repository for advanced usage
    VnpayTransactionRepository,
    
    // Export VNPAY service for direct access if needed
    VnpayService,
  ],
})
export class VnpayPaymentModule {
  /**
   * Configure the module with custom event handler
   */
  static forRoot(eventHandler?: any): any {
    return {
      module: VnpayPaymentModule,
      providers: [
        VnpayService,
        VnpayPaymentService,
        VnpayTransactionRepository,
        ...(eventHandler ? [
          {
            provide: 'PAYMENT_EVENT_HANDLER',
            useValue: eventHandler,
          }
        ] : []),
      ],
      exports: [
        VnpayPaymentService,
        VnpayTransactionRepository,
        VnpayService,
      ],
    };
  }
}

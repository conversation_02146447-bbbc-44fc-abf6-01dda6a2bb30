import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { TypeOrmModule } from '@nestjs/typeorm';
import { EventEmitterModule } from '@nestjs/event-emitter';
import { HttpModule } from '@nestjs/axios';

// Entities
import { VnpayTransaction } from './entities/vnpay-transaction.entity';

// Services
import { VnpayService } from './services/vnpay.service';
import { VnpayPaymentService } from './services/vnpay-payment.service';

// Repositories
import { VnpayTransactionRepository } from './repositories/vnpay-transaction.repository';

// Controllers
import { VnpayPaymentController } from './controllers/vnpay-payment.controller';

/**
 * 🚀 Portable VNPAY Payment Module - 100% Independent
 *
 * ✅ COMPLETELY SELF-CONTAINED - No external dependencies
 * ✅ COPY-READY - Copy directory and use immediately
 * ✅ 1-1 VNPAY MAPPING - Perfect reconciliation
 * ✅ EVENT-DRIVEN - Flexible business logic integration
 * ✅ PRODUCTION-READY - Full logging, error handling, monitoring
 *
 * 📦 Copy to new project:
 * 1. cp -r payment-gateways /new-project/src/
 * 2. Add VnpayPaymentModule to imports
 * 3. Set environment variables
 * 4. Run migration
 * 5. Implement PaymentEventHandler
 *
 * 🎯 Zero dependencies on external entities!
 */
@Module({
  imports: [
    // Configuration
    ConfigModule,
    
    // Database
    TypeOrmModule.forFeature([VnpayTransaction]),
    
    // Events
    EventEmitterModule.forRoot(),
    
    // HTTP client for VNPAY API calls
    HttpModule,
  ],
  
  controllers: [
    VnpayPaymentController,
  ],
  
  providers: [
    // Core VNPAY service
    VnpayService,
    
    // Portable payment service
    VnpayPaymentService,
    
    // Transaction repository
    VnpayTransactionRepository,
  ],
  
  exports: [
    // Export main service for external use
    VnpayPaymentService,
    
    // Export repository for advanced usage
    VnpayTransactionRepository,
    
    // Export VNPAY service for direct access if needed
    VnpayService,
  ],
})
export class VnpayPaymentModule {
  /**
   * Configure the module with custom event handler
   */
  static forRoot(eventHandler?: any): any {
    return {
      module: VnpayPaymentModule,
      providers: [
        VnpayService,
        VnpayPaymentService,
        VnpayTransactionRepository,
        ...(eventHandler ? [
          {
            provide: 'PAYMENT_EVENT_HANDLER',
            useValue: eventHandler,
          }
        ] : []),
      ],
      exports: [
        VnpayPaymentService,
        VnpayTransactionRepository,
        VnpayService,
      ],
    };
  }
}

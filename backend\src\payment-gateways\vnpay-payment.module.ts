import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { TypeOrmModule } from '@nestjs/typeorm';
import { EventEmitterModule } from '@nestjs/event-emitter';
import { HttpModule } from '@nestjs/axios';

// Entity VNPAY
import { VnpayTransaction } from './entities/vnpay-transaction.entity';

// Services VNPAY
import { VnpayService } from './services/vnpay.service';
import { VnpayPaymentService } from './services/vnpay-payment.service';

// Repository VNPAY
import { VnpayTransactionRepository } from './repositories/vnpay-transaction.repository';

// Controller VNPAY
import { VnpayPaymentController } from './controllers/vnpay-payment.controller';

// Module VNPAY Payment Gateway - Hoàn toàn độc lập
@Module({
  imports: [
    ConfigModule,                                    // C<PERSON>u hình môi trường
    TypeOrmModule.forFeature([VnpayTransaction]),   // Entity VNPAY
    EventEmitterModule.forRoot(),                   // Hệ thống sự kiện
    HttpModule,                                     // HTTP client cho VNPAY API
  ],

  controllers: [VnpayPaymentController],            // Controller xử lý API

  providers: [
    VnpayService,                                   // Service VNPAY API
    VnpayPaymentService,                           // Service thanh toán chính
    VnpayTransactionRepository,                    // Repository giao dịch
  ],

  exports: [
    VnpayPaymentService,                           // Export service chính
    VnpayTransactionRepository,                    // Export repository
    VnpayService,                                  // Export VNPAY service
  ],
})
export class VnpayPaymentModule {
  // Cấu hình module với event handler tùy chỉnh
  static forRoot(eventHandler?: any): any {
    return {
      module: VnpayPaymentModule,
      providers: [
        VnpayService,
        VnpayPaymentService,
        VnpayTransactionRepository,
        ...(eventHandler
          ? [
              {
                provide: 'PAYMENT_EVENT_HANDLER',
                useValue: eventHandler,
              },
            ]
          : []),
      ],
      exports: [VnpayPaymentService, VnpayTransactionRepository, VnpayService],
    };
  }
}

import { OnGatewayConnection, OnGatewayDisconnect } from '@nestjs/websockets';
import { Server, Socket } from 'socket.io';
import { TokenDto } from '../dto/token.dto';
export declare class TokenEventsGateway implements OnGatewayConnection, OnGatewayDisconnect {
    server: Server;
    private readonly logger;
    handleConnection(client: Socket, ...args: any[]): void;
    handleDisconnect(client: Socket): void;
    emitEntityCreated(dto: TokenDto): void;
    emitEntityUpdated(dto: TokenDto): void;
    emitStatusToggled(id: string, status: boolean): void;
    emitEntityDuplicated(dto: TokenDto): void;
    emitEntityDeleted(id: string, isSoftDelete: boolean): void;
    handleSubscribe(entityId: string, client: Socket): void;
    handleUnsubscribe(entityId: string, client: Socket): void;
}

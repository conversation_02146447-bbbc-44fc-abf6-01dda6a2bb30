import { CreateCmsMenusService } from '../services/create.cms-menus.service';
import { CreateCmsMenuDto } from '../dto/create.cms-menu.dto';
import { CmsMenuDto } from '../dto/cms-menu.dto';
export declare class CreateCmsMenusController {
    private readonly cmsMenusService;
    constructor(cmsMenusService: CreateCmsMenusService);
    create(createCmsMenuDto: CreateCmsMenuDto, userId: string): Promise<CmsMenuDto>;
    bulkCreate(createCmsMenuDtos: CreateCmsMenuDto[], userId: string): Promise<CmsMenuDto[]>;
    duplicate(id: string, userId: string): Promise<CmsMenuDto>;
}

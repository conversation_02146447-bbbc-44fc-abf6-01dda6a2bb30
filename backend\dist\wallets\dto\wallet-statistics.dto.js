"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.WalletStatisticsDto = void 0;
const openapi = require("@nestjs/swagger");
const swagger_1 = require("@nestjs/swagger");
class WalletStatisticsDto {
    total;
    active;
    locked;
    constructor(partial) {
        Object.assign(this, partial);
    }
    static _OPENAPI_METADATA_FACTORY() {
        return { total: { required: true, type: () => Number }, active: { required: true, type: () => Number }, locked: { required: true, type: () => Number } };
    }
}
exports.WalletStatisticsDto = WalletStatisticsDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Tổng số ví',
        example: 100,
    }),
    __metadata("design:type", Number)
], WalletStatisticsDto.prototype, "total", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Số ví đang hoạt động',
        example: 80,
    }),
    __metadata("design:type", Number)
], WalletStatisticsDto.prototype, "active", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Số ví đã khóa',
        example: 20,
    }),
    __metadata("design:type", Number)
], WalletStatisticsDto.prototype, "locked", void 0);
//# sourceMappingURL=wallet-statistics.dto.js.map
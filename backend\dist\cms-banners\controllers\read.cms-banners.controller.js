"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ReadCmsBannersController = void 0;
const openapi = require("@nestjs/swagger");
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const jwt_auth_guard_1 = require("../../common/guards/jwt-auth.guard");
const read_cms_banners_service_1 = require("../services/read.cms-banners.service");
const cms_banners_entity_1 = require("../entity/cms-banners.entity");
const api_response_dto_1 = require("../../common/dto/api-response.dto");
const custom_pagination_query_dto_1 = require("../../common/dto/custom-pagination-query.dto");
const pagination_response_dto_1 = require("../../common/dto/pagination-response.dto");
const custom_pagination_meta_dto_1 = require("../../common/dto/custom-pagination-meta.dto");
const roles_decorator_1 = require("../../common/decorators/roles.decorator");
const permissions_decorator_1 = require("../../common/decorators/permissions.decorator");
let ReadCmsBannersController = class ReadCmsBannersController {
    cmsBannersService;
    constructor(cmsBannersService) {
        this.cmsBannersService = cmsBannersService;
    }
    async findAll(paginationQuery) {
        const { data, total } = await this.cmsBannersService.findAll(paginationQuery);
        const meta = new custom_pagination_meta_dto_1.CustomPageMetaDto({
            pageQueryDto: paginationQuery,
            itemCount: total,
        });
        return new pagination_response_dto_1.PaginationResponseDto(data, meta);
    }
    async findByStatus(status, paginationQuery) {
        const { data, total } = await this.cmsBannersService.findByStatus(status, paginationQuery);
        const meta = new custom_pagination_meta_dto_1.CustomPageMetaDto({
            pageQueryDto: paginationQuery,
            itemCount: total,
        });
        return new pagination_response_dto_1.PaginationResponseDto(data, meta);
    }
    async findByLocation(location, paginationQuery) {
        const { data, total } = await this.cmsBannersService.findByLocation(location, paginationQuery);
        const meta = new custom_pagination_meta_dto_1.CustomPageMetaDto({
            pageQueryDto: paginationQuery,
            itemCount: total,
        });
        return new pagination_response_dto_1.PaginationResponseDto(data, meta);
    }
    async getActiveBanners(location, limit) {
        return this.cmsBannersService.getActiveBanners(location, limit || 10);
    }
    async getStatistics() {
        return this.cmsBannersService.getStatistics();
    }
    async count(filter) {
        return this.cmsBannersService.count(filter);
    }
    async search(keyword, paginationQuery) {
        const { data, total } = await this.cmsBannersService.search(keyword, paginationQuery);
        const meta = new custom_pagination_meta_dto_1.CustomPageMetaDto({
            pageQueryDto: paginationQuery,
            itemCount: total,
        });
        return new pagination_response_dto_1.PaginationResponseDto(data, meta);
    }
    async findDeleted(paginationQuery) {
        const { data, total } = await this.cmsBannersService.findDeleted(paginationQuery);
        const meta = new custom_pagination_meta_dto_1.CustomPageMetaDto({
            pageQueryDto: paginationQuery,
            itemCount: total,
        });
        return new pagination_response_dto_1.PaginationResponseDto(data, meta);
    }
    async findOne(id, relations) {
        const relationsArray = relations ? relations.split(',').map(r => r.trim()) : [];
        return this.cmsBannersService.findOneOrFail(id, relationsArray);
    }
    async debugDatabase() {
        return this.cmsBannersService.debugDatabase();
    }
    async findByBusinessCode(businessCode) {
        return this.cmsBannersService.findByBusinessCodePublic(businessCode);
    }
};
exports.ReadCmsBannersController = ReadCmsBannersController;
__decorate([
    (0, common_1.Get)(),
    (0, roles_decorator_1.Roles)('ADMIN', 'EDITOR', 'USER'),
    (0, permissions_decorator_1.Permissions)('cms-banner:read'),
    (0, swagger_1.ApiOperation)({ summary: 'Lấy danh sách banner CMS với phân trang' }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Danh sách banner CMS.',
        type: api_response_dto_1.ApiResponseDto,
        schema: {
            properties: {
                data: {
                    type: 'object',
                    properties: {
                        data: {
                            type: 'array',
                            items: { $ref: '#/components/schemas/CmsBannerDto' },
                        },
                        meta: {
                            type: 'object',
                            properties: {
                                total: { type: 'number' },
                                page: { type: 'number' },
                                limit: { type: 'number' },
                                totalPages: { type: 'number' },
                            },
                        },
                    },
                },
            },
        },
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.BAD_REQUEST,
        description: 'Tham số không hợp lệ.',
        type: api_response_dto_1.ApiResponseDto,
        schema: { properties: { data: { type: 'object', nullable: true } } },
    }),
    (0, swagger_1.ApiQuery)({
        name: 'limit',
        required: false,
        description: 'Giới hạn số lượng kết quả',
    }),
    (0, swagger_1.ApiQuery)({
        name: 'page',
        required: false,
        description: 'Số trang cho phân trang',
    }),
    (0, swagger_1.ApiQuery)({
        name: 'sort',
        required: false,
        description: 'Sắp xếp theo định dạng field:order (ví dụ: displayOrder:ASC,createdAt:DESC)',
    }),
    (0, swagger_1.ApiQuery)({
        name: 'filter',
        required: false,
        description: 'Lọc theo trường (ví dụ: status:ACTIVE)',
    }),
    (0, swagger_1.ApiQuery)({
        name: 'search',
        required: false,
        description: 'Tìm kiếm theo từ khóa',
    }),
    (0, swagger_1.ApiQuery)({
        name: 'relations',
        required: false,
        description: 'Các mối quan hệ cần tải (ví dụ: creator,updater)',
    }),
    openapi.ApiResponse({ status: 200 }),
    __param(0, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [custom_pagination_query_dto_1.CustomPaginationQueryDto]),
    __metadata("design:returntype", Promise)
], ReadCmsBannersController.prototype, "findAll", null);
__decorate([
    (0, common_1.Get)('by-status/:status'),
    (0, roles_decorator_1.Roles)('ADMIN', 'EDITOR', 'USER'),
    (0, permissions_decorator_1.Permissions)('cms-banner:read'),
    (0, swagger_1.ApiOperation)({ summary: 'Lấy danh sách banner CMS theo trạng thái' }),
    (0, swagger_1.ApiParam)({
        name: 'status',
        enum: cms_banners_entity_1.CmsBannerStatus,
        description: 'Trạng thái banner',
    }),
    (0, swagger_1.ApiQuery)({ name: 'page', required: false, type: Number, description: 'Số trang' }),
    (0, swagger_1.ApiQuery)({ name: 'limit', required: false, type: Number, description: 'Số lượng bản ghi mỗi trang' }),
    (0, swagger_1.ApiQuery)({ name: 'search', required: false, type: String, description: 'Tìm kiếm theo từ khóa' }),
    openapi.ApiResponse({ status: 200 }),
    __param(0, (0, common_1.Param)('status')),
    __param(1, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, custom_pagination_query_dto_1.CustomPaginationQueryDto]),
    __metadata("design:returntype", Promise)
], ReadCmsBannersController.prototype, "findByStatus", null);
__decorate([
    (0, common_1.Get)('by-location/:location'),
    (0, roles_decorator_1.Roles)('ADMIN', 'EDITOR', 'USER'),
    (0, permissions_decorator_1.Permissions)('cms-banner:read'),
    (0, swagger_1.ApiOperation)({ summary: 'Lấy danh sách banner CMS theo vị trí hiển thị' }),
    (0, swagger_1.ApiParam)({
        name: 'location',
        enum: cms_banners_entity_1.CmsBannerLocation,
        description: 'Vị trí hiển thị banner',
    }),
    (0, swagger_1.ApiQuery)({ name: 'page', required: false, type: Number, description: 'Số trang' }),
    (0, swagger_1.ApiQuery)({ name: 'limit', required: false, type: Number, description: 'Số lượng bản ghi mỗi trang' }),
    (0, swagger_1.ApiQuery)({ name: 'search', required: false, type: String, description: 'Tìm kiếm theo từ khóa' }),
    openapi.ApiResponse({ status: 200 }),
    __param(0, (0, common_1.Param)('location')),
    __param(1, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, custom_pagination_query_dto_1.CustomPaginationQueryDto]),
    __metadata("design:returntype", Promise)
], ReadCmsBannersController.prototype, "findByLocation", null);
__decorate([
    (0, common_1.Get)('active'),
    (0, roles_decorator_1.Roles)('ADMIN', 'EDITOR', 'USER'),
    (0, permissions_decorator_1.Permissions)('cms-banner:read'),
    (0, swagger_1.ApiOperation)({ summary: 'Lấy danh sách banner đang hoạt động' }),
    (0, swagger_1.ApiQuery)({
        name: 'location',
        required: false,
        enum: cms_banners_entity_1.CmsBannerLocation,
        description: 'Vị trí hiển thị (tùy chọn)',
    }),
    (0, swagger_1.ApiQuery)({
        name: 'limit',
        required: false,
        type: Number,
        description: 'Số lượng banner tối đa',
        example: 10,
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Danh sách banner đang hoạt động.',
        type: api_response_dto_1.ApiResponseDto,
        schema: {
            properties: {
                data: {
                    type: 'array',
                    items: { $ref: '#/components/schemas/CmsBannerDto' },
                },
            },
        },
    }),
    openapi.ApiResponse({ status: 200, type: [require("../dto/cms-banner.dto").CmsBannerDto] }),
    __param(0, (0, common_1.Query)('location')),
    __param(1, (0, common_1.Query)('limit')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Number]),
    __metadata("design:returntype", Promise)
], ReadCmsBannersController.prototype, "getActiveBanners", null);
__decorate([
    (0, common_1.Get)('statistics'),
    (0, roles_decorator_1.Roles)('ADMIN', 'EDITOR'),
    (0, permissions_decorator_1.Permissions)('cms-banner:read'),
    (0, swagger_1.ApiOperation)({ summary: 'Lấy thống kê banner CMS' }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Thống kê banner CMS.',
        type: api_response_dto_1.ApiResponseDto,
        schema: {
            properties: {
                data: {
                    type: 'object',
                    properties: {
                        total: { type: 'number' },
                        byStatus: {
                            type: 'object',
                            additionalProperties: { type: 'number' },
                        },
                        byLocation: {
                            type: 'object',
                            additionalProperties: { type: 'number' },
                        },
                        active: { type: 'number' },
                        expired: { type: 'number' },
                    },
                },
            },
        },
    }),
    openapi.ApiResponse({ status: 200 }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], ReadCmsBannersController.prototype, "getStatistics", null);
__decorate([
    (0, common_1.Get)('count'),
    (0, roles_decorator_1.Roles)('ADMIN', 'EDITOR', 'USER'),
    (0, permissions_decorator_1.Permissions)('cms-banner:read'),
    (0, swagger_1.ApiOperation)({ summary: 'Đếm số lượng banner CMS' }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Số lượng banner CMS.',
        type: api_response_dto_1.ApiResponseDto,
        schema: {
            properties: {
                data: { type: 'number' },
            },
        },
    }),
    (0, swagger_1.ApiQuery)({ name: 'filter', required: false, type: String, description: 'Bộ lọc' }),
    openapi.ApiResponse({ status: 200, type: Number }),
    __param(0, (0, common_1.Query)('filter')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], ReadCmsBannersController.prototype, "count", null);
__decorate([
    (0, common_1.Get)('search'),
    (0, roles_decorator_1.Roles)('ADMIN', 'EDITOR', 'USER'),
    (0, permissions_decorator_1.Permissions)('cms-banner:read'),
    (0, swagger_1.ApiOperation)({ summary: 'Tìm kiếm banner CMS' }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Kết quả tìm kiếm banner CMS.',
        type: api_response_dto_1.ApiResponseDto,
        schema: {
            properties: {
                data: {
                    type: 'object',
                    properties: {
                        data: {
                            type: 'array',
                            items: { $ref: '#/components/schemas/CmsBannerDto' },
                        },
                        meta: {
                            type: 'object',
                            properties: {
                                total: { type: 'number' },
                                page: { type: 'number' },
                                limit: { type: 'number' },
                                totalPages: { type: 'number' },
                            },
                        },
                    },
                },
            },
        },
    }),
    (0, swagger_1.ApiQuery)({ name: 'keyword', required: true, type: String, description: 'Từ khóa tìm kiếm' }),
    (0, swagger_1.ApiQuery)({ name: 'page', required: false, type: Number, description: 'Số trang' }),
    (0, swagger_1.ApiQuery)({ name: 'limit', required: false, type: Number, description: 'Số lượng bản ghi mỗi trang' }),
    openapi.ApiResponse({ status: 200 }),
    __param(0, (0, common_1.Query)('keyword')),
    __param(1, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, custom_pagination_query_dto_1.CustomPaginationQueryDto]),
    __metadata("design:returntype", Promise)
], ReadCmsBannersController.prototype, "search", null);
__decorate([
    (0, common_1.Get)('deleted'),
    (0, roles_decorator_1.Roles)('ADMIN'),
    (0, permissions_decorator_1.Permissions)('cms-banner:read'),
    (0, swagger_1.ApiOperation)({ summary: 'Lấy danh sách banner CMS đã xóa' }),
    (0, swagger_1.ApiQuery)({ name: 'page', required: false, type: Number, description: 'Số trang' }),
    (0, swagger_1.ApiQuery)({ name: 'limit', required: false, type: Number, description: 'Số lượng bản ghi mỗi trang' }),
    openapi.ApiResponse({ status: 200 }),
    __param(0, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [custom_pagination_query_dto_1.CustomPaginationQueryDto]),
    __metadata("design:returntype", Promise)
], ReadCmsBannersController.prototype, "findDeleted", null);
__decorate([
    (0, common_1.Get)(':id'),
    (0, roles_decorator_1.Roles)('ADMIN', 'EDITOR', 'USER'),
    (0, permissions_decorator_1.Permissions)('cms-banner:read'),
    (0, swagger_1.ApiOperation)({ summary: 'Lấy thông tin chi tiết banner CMS' }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Thông tin chi tiết banner CMS.',
        type: api_response_dto_1.ApiResponseDto,
        schema: {
            properties: {
                data: { $ref: '#/components/schemas/CmsBannerDto' },
            },
        },
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.NOT_FOUND,
        description: 'Không tìm thấy banner CMS.',
        type: api_response_dto_1.ApiResponseDto,
        schema: { properties: { data: { type: 'object', nullable: true } } },
    }),
    (0, swagger_1.ApiParam)({ name: 'id', type: String, description: 'ID của banner CMS' }),
    openapi.ApiResponse({ status: 200, type: Object }),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __param(1, (0, common_1.Query)('relations')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String]),
    __metadata("design:returntype", Promise)
], ReadCmsBannersController.prototype, "findOne", null);
__decorate([
    (0, common_1.Get)('debug/database'),
    (0, roles_decorator_1.Roles)('ADMIN'),
    (0, permissions_decorator_1.Permissions)('cms-banner:read'),
    (0, swagger_1.ApiOperation)({ summary: 'Debug database connectivity and table structure' }),
    openapi.ApiResponse({ status: 200, type: Object }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], ReadCmsBannersController.prototype, "debugDatabase", null);
__decorate([
    (0, common_1.Get)('business-code/:businessCode'),
    (0, roles_decorator_1.Roles)('ADMIN', 'EDITOR', 'USER'),
    (0, permissions_decorator_1.Permissions)('cms-banner:read'),
    (0, swagger_1.ApiOperation)({ summary: 'Lấy thông tin banner CMS theo business code' }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Thông tin banner CMS.',
        type: api_response_dto_1.ApiResponseDto,
        schema: {
            properties: {
                data: { $ref: '#/components/schemas/CmsBannerDto' },
            },
        },
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.NOT_FOUND,
        description: 'Không tìm thấy banner CMS.',
        type: api_response_dto_1.ApiResponseDto,
        schema: { properties: { data: { type: 'object', nullable: true } } },
    }),
    (0, swagger_1.ApiParam)({ name: 'businessCode', type: String, description: 'Business code của banner CMS' }),
    openapi.ApiResponse({ status: 200, type: Object }),
    __param(0, (0, common_1.Param)('businessCode')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], ReadCmsBannersController.prototype, "findByBusinessCode", null);
exports.ReadCmsBannersController = ReadCmsBannersController = __decorate([
    (0, swagger_1.ApiTags)('cms-banners'),
    (0, swagger_1.ApiBearerAuth)(),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, common_1.Controller)('cms/banners'),
    __metadata("design:paramtypes", [read_cms_banners_service_1.ReadCmsBannersService])
], ReadCmsBannersController);
//# sourceMappingURL=read.cms-banners.controller.js.map
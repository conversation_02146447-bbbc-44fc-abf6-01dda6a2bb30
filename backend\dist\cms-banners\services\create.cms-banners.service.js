"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreateCmsBannersService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const event_emitter_1 = require("@nestjs/event-emitter");
const typeorm_transactional_1 = require("typeorm-transactional");
const base_cms_banners_service_1 = require("./base.cms-banners.service");
const cms_banners_entity_1 = require("../entity/cms-banners.entity");
const create_cms_banner_dto_1 = require("../dto/create.cms-banner.dto");
let CreateCmsBannersService = class CreateCmsBannersService extends base_cms_banners_service_1.BaseCmsBannersService {
    bannerRepository;
    dataSource;
    eventEmitter;
    constructor(bannerRepository, dataSource, eventEmitter) {
        super(bannerRepository, dataSource, eventEmitter);
        this.bannerRepository = bannerRepository;
        this.dataSource = dataSource;
        this.eventEmitter = eventEmitter;
    }
    async create(createDto, userId) {
        try {
            this.logger.debug(`Đang tạo banner CMS: ${JSON.stringify(createDto)}`);
            const existingBannerByTitle = await this.bannerRepository.findOne({
                where: { title: createDto.title, isDeleted: false },
            });
            if (existingBannerByTitle) {
                throw new common_1.ConflictException(`Tên banner "${createDto.title}" đã tồn tại`);
            }
            if (createDto.startDate && createDto.endDate) {
                const startDate = new Date(createDto.startDate);
                const endDate = new Date(createDto.endDate);
                if (startDate >= endDate) {
                    throw new common_1.BadRequestException('Ngày bắt đầu phải nhỏ hơn ngày kết thúc');
                }
            }
            const banner = this.bannerRepository.create();
            banner.title = createDto.title;
            banner.imageUrlDesktop = createDto.imageUrlDesktop;
            banner.imageUrlMobile = createDto.imageUrlMobile || null;
            banner.linkUrl = createDto.linkUrl || null;
            banner.altText = createDto.altText || null;
            banner.status = createDto.status || cms_banners_entity_1.CmsBannerStatus.ACTIVE;
            banner.location = createDto.location || null;
            banner.createdBy = userId;
            banner.updatedBy = userId;
            if (createDto.displayOrder !== undefined) {
                banner.displayOrder = createDto.displayOrder;
                await this.updateDisplayOrders(banner.location, createDto.displayOrder, 1);
            }
            else {
                banner.displayOrder = await this.getNextDisplayOrder(banner.location || undefined);
            }
            if (createDto.startDate) {
                banner.startDate = new Date(createDto.startDate);
            }
            if (createDto.endDate) {
                banner.endDate = new Date(createDto.endDate);
            }
            const savedBanner = await this.bannerRepository.save(banner);
            const bannerDto = this.toDto(savedBanner);
            if (!bannerDto) {
                throw new common_1.InternalServerErrorException('Không thể chuyển đổi entity thành DTO');
            }
            this.eventEmitter.emit(this.EVENT_BANNER_CREATED, {
                bannerId: bannerDto.id,
                userId,
                newData: bannerDto,
            });
            return bannerDto;
        }
        catch (error) {
            this.logger.error(`Lỗi khi tạo banner CMS: ${error.message}`, error.stack);
            if (error instanceof common_1.BadRequestException || error instanceof common_1.ConflictException) {
                throw error;
            }
            throw new common_1.InternalServerErrorException(`Không thể tạo banner CMS: ${error.message}`);
        }
    }
    async bulkCreate(createDtos, userId) {
        try {
            this.logger.debug(`Đang tạo ${createDtos.length} banner CMS`);
            const banners = [];
            for (const createDto of createDtos) {
                const banner = await this.create(createDto, userId);
                banners.push(banner);
            }
            return banners;
        }
        catch (error) {
            this.logger.error(`Lỗi khi tạo nhiều banner CMS: ${error.message}`, error.stack);
            if (error instanceof common_1.BadRequestException || error instanceof common_1.ConflictException) {
                throw error;
            }
            throw new common_1.InternalServerErrorException(`Không thể tạo nhiều banner CMS: ${error.message}`);
        }
    }
    async duplicate(id, userId, newTitle) {
        try {
            this.logger.debug(`Đang sao chép banner CMS với ID: ${id}`);
            const originalBanner = await this.findById(id, []);
            if (!originalBanner) {
                throw new common_1.NotFoundException(`Không tìm thấy banner với ID: ${id}`);
            }
            const title = newTitle || `${originalBanner.title} (Bản sao)`;
            const createDto = {
                title,
                imageUrlDesktop: originalBanner.imageUrlDesktop,
                imageUrlMobile: originalBanner.imageUrlMobile || undefined,
                linkUrl: originalBanner.linkUrl || undefined,
                altText: originalBanner.altText || undefined,
                status: originalBanner.status,
                location: originalBanner.location || undefined,
                startDate: originalBanner.startDate?.toISOString(),
                endDate: originalBanner.endDate?.toISOString(),
            };
            return this.create(createDto, userId);
        }
        catch (error) {
            this.logger.error(`Lỗi khi sao chép banner CMS: ${error.message}`, error.stack);
            if (error instanceof common_1.NotFoundException || error instanceof common_1.ConflictException) {
                throw error;
            }
            throw new common_1.InternalServerErrorException(`Không thể sao chép banner CMS: ${error.message}`);
        }
    }
    async createFromTemplate(templateData, userId) {
        try {
            this.logger.debug(`Đang tạo banner CMS từ template`);
            const createDto = {
                title: templateData.title || 'Banner từ template',
                imageUrlDesktop: templateData.imageUrlDesktop,
                imageUrlMobile: templateData.imageUrlMobile,
                linkUrl: templateData.linkUrl,
                altText: templateData.altText,
                status: templateData.status || cms_banners_entity_1.CmsBannerStatus.ACTIVE,
                location: templateData.location,
                displayOrder: templateData.displayOrder,
                startDate: templateData.startDate,
                endDate: templateData.endDate,
            };
            return this.create(createDto, userId);
        }
        catch (error) {
            this.logger.error(`Lỗi khi tạo banner từ template: ${error.message}`, error.stack);
            throw new common_1.InternalServerErrorException(`Không thể tạo banner từ template: ${error.message}`);
        }
    }
};
exports.CreateCmsBannersService = CreateCmsBannersService;
__decorate([
    (0, typeorm_transactional_1.Transactional)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [create_cms_banner_dto_1.CreateCmsBannerDto, String]),
    __metadata("design:returntype", Promise)
], CreateCmsBannersService.prototype, "create", null);
__decorate([
    (0, typeorm_transactional_1.Transactional)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Array, String]),
    __metadata("design:returntype", Promise)
], CreateCmsBannersService.prototype, "bulkCreate", null);
__decorate([
    (0, typeorm_transactional_1.Transactional)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String, String]),
    __metadata("design:returntype", Promise)
], CreateCmsBannersService.prototype, "duplicate", null);
__decorate([
    (0, typeorm_transactional_1.Transactional)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, String]),
    __metadata("design:returntype", Promise)
], CreateCmsBannersService.prototype, "createFromTemplate", null);
exports.CreateCmsBannersService = CreateCmsBannersService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(cms_banners_entity_1.CmsBanners)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        typeorm_2.DataSource,
        event_emitter_1.EventEmitter2])
], CreateCmsBannersService);
//# sourceMappingURL=create.cms-banners.service.js.map
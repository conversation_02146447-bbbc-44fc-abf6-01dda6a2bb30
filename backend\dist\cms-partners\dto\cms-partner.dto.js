"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CmsPartnerDto = void 0;
const openapi = require("@nestjs/swagger");
const swagger_1 = require("@nestjs/swagger");
const class_transformer_1 = require("class-transformer");
const class_validator_1 = require("class-validator");
const user_dto_1 = require("../../users/dto/user.dto");
const cms_partners_entity_1 = require("../entity/cms-partners.entity");
class CmsPartnerDto {
    id;
    name;
    logoUrl;
    websiteUrl;
    description;
    type;
    displayOrder;
    status;
    createdAt;
    updatedAt;
    createdBy;
    updatedBy;
    deletedAt;
    deletedBy;
    isDeleted;
    isActive;
    hasLogo;
    hasWebsite;
    hasDescription;
    hasCompleteInfo;
    typeDisplayName;
    statusDisplayName;
    canDisplayPublicly;
    slug;
    isPartner;
    isClient;
    isSupplier;
    creator;
    updater;
    deleter;
    static _OPENAPI_METADATA_FACTORY() {
        return { id: { required: true, type: () => String }, name: { required: true, type: () => String }, logoUrl: { required: false, type: () => String, nullable: true }, websiteUrl: { required: false, type: () => String, nullable: true }, description: { required: false, type: () => String, nullable: true }, type: { required: true, enum: require("../entity/cms-partners.entity").CmsPartnerType }, displayOrder: { required: true, type: () => Number }, status: { required: true, enum: require("../entity/cms-partners.entity").CmsPartnerStatus }, createdAt: { required: true, type: () => Date }, updatedAt: { required: true, type: () => Date }, createdBy: { required: false, type: () => String, nullable: true }, updatedBy: { required: false, type: () => String, nullable: true }, deletedAt: { required: false, type: () => Date, nullable: true }, deletedBy: { required: false, type: () => String, nullable: true }, isDeleted: { required: true, type: () => Boolean }, isActive: { required: true, type: () => Boolean }, hasLogo: { required: true, type: () => Boolean }, hasWebsite: { required: true, type: () => Boolean }, hasDescription: { required: true, type: () => Boolean }, hasCompleteInfo: { required: true, type: () => Boolean }, typeDisplayName: { required: true, type: () => String }, statusDisplayName: { required: true, type: () => String }, canDisplayPublicly: { required: true, type: () => Boolean }, slug: { required: true, type: () => String }, isPartner: { required: true, type: () => Boolean }, isClient: { required: true, type: () => Boolean }, isSupplier: { required: true, type: () => Boolean }, creator: { required: false, type: () => require("../../users/dto/user.dto").UserDto }, updater: { required: false, type: () => require("../../users/dto/user.dto").UserDto }, deleter: { required: false, type: () => require("../../users/dto/user.dto").UserDto } };
    }
}
exports.CmsPartnerDto = CmsPartnerDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'ID của đối tác',
        example: '123e4567-e89b-12d3-a456-426614174000',
    }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", String)
], CmsPartnerDto.prototype, "id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Tên đối tác/khách hàng',
        example: 'Công ty ABC',
    }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", String)
], CmsPartnerDto.prototype, "name", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'URL logo của đối tác',
        example: 'https://example.com/logo.png',
    }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", Object)
], CmsPartnerDto.prototype, "logoUrl", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'URL website của đối tác',
        example: 'https://partner-website.com',
    }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", Object)
], CmsPartnerDto.prototype, "websiteUrl", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Mô tả ngắn về đối tác',
        example: 'Đối tác chiến lược trong lĩnh vực công nghệ',
    }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", Object)
], CmsPartnerDto.prototype, "description", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Loại đối tác',
        example: cms_partners_entity_1.CmsPartnerType.PARTNER,
        enum: cms_partners_entity_1.CmsPartnerType,
    }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", String)
], CmsPartnerDto.prototype, "type", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Thứ tự hiển thị',
        example: 1,
    }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", Number)
], CmsPartnerDto.prototype, "displayOrder", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Trạng thái đối tác',
        example: cms_partners_entity_1.CmsPartnerStatus.ACTIVE,
        enum: cms_partners_entity_1.CmsPartnerStatus,
    }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", String)
], CmsPartnerDto.prototype, "status", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Thời gian tạo',
        example: '2024-01-01T00:00:00.000Z',
    }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", Date)
], CmsPartnerDto.prototype, "createdAt", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Thời gian cập nhật',
        example: '2024-01-01T00:00:00.000Z',
    }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", Date)
], CmsPartnerDto.prototype, "updatedAt", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'ID người tạo',
        example: '123e4567-e89b-12d3-a456-426614174000',
    }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", Object)
], CmsPartnerDto.prototype, "createdBy", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'ID người cập nhật',
        example: '123e4567-e89b-12d3-a456-426614174000',
    }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", Object)
], CmsPartnerDto.prototype, "updatedBy", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Thời gian xóa mềm',
        example: '2024-01-01T00:00:00.000Z',
    }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", Object)
], CmsPartnerDto.prototype, "deletedAt", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'ID người xóa',
        example: '123e4567-e89b-12d3-a456-426614174000',
    }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", Object)
], CmsPartnerDto.prototype, "deletedBy", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Trạng thái xóa mềm',
        example: false,
    }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", Boolean)
], CmsPartnerDto.prototype, "isDeleted", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Đối tác có đang hoạt động không',
        example: true,
    }),
    (0, class_transformer_1.Expose)(),
    (0, class_transformer_1.Transform)(({ obj }) => obj.status === cms_partners_entity_1.CmsPartnerStatus.ACTIVE),
    __metadata("design:type", Boolean)
], CmsPartnerDto.prototype, "isActive", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Đối tác có logo không',
        example: true,
    }),
    (0, class_transformer_1.Expose)(),
    (0, class_transformer_1.Transform)(({ obj }) => !!obj.logoUrl),
    __metadata("design:type", Boolean)
], CmsPartnerDto.prototype, "hasLogo", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Đối tác có website không',
        example: true,
    }),
    (0, class_transformer_1.Expose)(),
    (0, class_transformer_1.Transform)(({ obj }) => !!obj.websiteUrl),
    __metadata("design:type", Boolean)
], CmsPartnerDto.prototype, "hasWebsite", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Đối tác có mô tả không',
        example: true,
    }),
    (0, class_transformer_1.Expose)(),
    (0, class_transformer_1.Transform)(({ obj }) => !!obj.description),
    __metadata("design:type", Boolean)
], CmsPartnerDto.prototype, "hasDescription", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Đối tác có đầy đủ thông tin không',
        example: true,
    }),
    (0, class_transformer_1.Expose)(),
    (0, class_transformer_1.Transform)(({ obj }) => !!(obj.name && obj.logoUrl && obj.websiteUrl && obj.description)),
    __metadata("design:type", Boolean)
], CmsPartnerDto.prototype, "hasCompleteInfo", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Tên loại đối tác bằng tiếng Việt',
        example: 'Đối tác',
    }),
    (0, class_transformer_1.Expose)(),
    (0, class_transformer_1.Transform)(({ obj }) => {
        const typeNames = {
            [cms_partners_entity_1.CmsPartnerType.PARTNER]: 'Đối tác',
            [cms_partners_entity_1.CmsPartnerType.CLIENT]: 'Khách hàng',
            [cms_partners_entity_1.CmsPartnerType.SUPPLIER]: 'Nhà cung cấp',
        };
        return typeNames[obj.type] || 'Không xác định';
    }),
    __metadata("design:type", String)
], CmsPartnerDto.prototype, "typeDisplayName", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Tên trạng thái bằng tiếng Việt',
        example: 'Hoạt động',
    }),
    (0, class_transformer_1.Expose)(),
    (0, class_transformer_1.Transform)(({ obj }) => {
        const statusNames = {
            [cms_partners_entity_1.CmsPartnerStatus.ACTIVE]: 'Hoạt động',
            [cms_partners_entity_1.CmsPartnerStatus.INACTIVE]: 'Không hoạt động',
        };
        return statusNames[obj.status] || 'Không xác định';
    }),
    __metadata("design:type", String)
], CmsPartnerDto.prototype, "statusDisplayName", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Có thể hiển thị công khai không',
        example: true,
    }),
    (0, class_transformer_1.Expose)(),
    (0, class_transformer_1.Transform)(({ obj }) => obj.status === cms_partners_entity_1.CmsPartnerStatus.ACTIVE && !!obj.logoUrl),
    __metadata("design:type", Boolean)
], CmsPartnerDto.prototype, "canDisplayPublicly", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Slug được tạo từ tên đối tác',
        example: 'cong-ty-abc',
    }),
    (0, class_transformer_1.Expose)(),
    (0, class_transformer_1.Transform)(({ obj }) => {
        return obj.name
            ?.toLowerCase()
            ?.normalize('NFD')
            ?.replace(/[\u0300-\u036f]/g, '')
            ?.replace(/[^a-z0-9\s-]/g, '')
            ?.replace(/\s+/g, '-')
            ?.replace(/-+/g, '-')
            ?.trim() || '';
    }),
    __metadata("design:type", String)
], CmsPartnerDto.prototype, "slug", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Đối tác có phải là đối tác không',
        example: true,
    }),
    (0, class_transformer_1.Expose)(),
    (0, class_transformer_1.Transform)(({ obj }) => obj.type === cms_partners_entity_1.CmsPartnerType.PARTNER),
    __metadata("design:type", Boolean)
], CmsPartnerDto.prototype, "isPartner", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Đối tác có phải là khách hàng không',
        example: false,
    }),
    (0, class_transformer_1.Expose)(),
    (0, class_transformer_1.Transform)(({ obj }) => obj.type === cms_partners_entity_1.CmsPartnerType.CLIENT),
    __metadata("design:type", Boolean)
], CmsPartnerDto.prototype, "isClient", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Đối tác có phải là nhà cung cấp không',
        example: false,
    }),
    (0, class_transformer_1.Expose)(),
    (0, class_transformer_1.Transform)(({ obj }) => obj.type === cms_partners_entity_1.CmsPartnerType.SUPPLIER),
    __metadata("design:type", Boolean)
], CmsPartnerDto.prototype, "isSupplier", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Thông tin người tạo',
        type: () => user_dto_1.UserDto,
    }),
    (0, class_transformer_1.Expose)(),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => user_dto_1.UserDto),
    __metadata("design:type", user_dto_1.UserDto)
], CmsPartnerDto.prototype, "creator", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Thông tin người cập nhật',
        type: () => user_dto_1.UserDto,
    }),
    (0, class_transformer_1.Expose)(),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => user_dto_1.UserDto),
    __metadata("design:type", user_dto_1.UserDto)
], CmsPartnerDto.prototype, "updater", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Thông tin người xóa',
        type: () => user_dto_1.UserDto,
    }),
    (0, class_transformer_1.Expose)(),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => user_dto_1.UserDto),
    __metadata("design:type", user_dto_1.UserDto)
], CmsPartnerDto.prototype, "deleter", void 0);
//# sourceMappingURL=cms-partner.dto.js.map
/* [project]/app/globals.css [app-client] (css) */
@layer properties {
  @supports (((-webkit-hyphens: none)) and (not (margin-trim: inline))) or ((-moz-orient: inline) and (not (color: rgb(from red r g b)))) {
    *, :before, :after, ::backdrop {
      --tw-translate-x: 0;
      --tw-translate-y: 0;
      --tw-translate-z: 0;
      --tw-scale-x: 1;
      --tw-scale-y: 1;
      --tw-scale-z: 1;
      --tw-rotate-x: rotateX(0);
      --tw-rotate-y: rotateY(0);
      --tw-rotate-z: rotateZ(0);
      --tw-skew-x: skewX(0);
      --tw-skew-y: skewY(0);
      --tw-scroll-snap-strictness: proximity;
      --tw-space-y-reverse: 0;
      --tw-space-x-reverse: 0;
      --tw-divide-y-reverse: 0;
      --tw-border-style: solid;
      --tw-gradient-position: initial;
      --tw-gradient-from: #0000;
      --tw-gradient-via: #0000;
      --tw-gradient-to: #0000;
      --tw-gradient-stops: initial;
      --tw-gradient-via-stops: initial;
      --tw-gradient-from-position: 0%;
      --tw-gradient-via-position: 50%;
      --tw-gradient-to-position: 100%;
      --tw-leading: initial;
      --tw-font-weight: initial;
      --tw-tracking: initial;
      --tw-ordinal: initial;
      --tw-slashed-zero: initial;
      --tw-numeric-figure: initial;
      --tw-numeric-spacing: initial;
      --tw-numeric-fraction: initial;
      --tw-shadow: 0 0 #0000;
      --tw-shadow-color: initial;
      --tw-shadow-alpha: 100%;
      --tw-inset-shadow: 0 0 #0000;
      --tw-inset-shadow-color: initial;
      --tw-inset-shadow-alpha: 100%;
      --tw-ring-color: initial;
      --tw-ring-shadow: 0 0 #0000;
      --tw-inset-ring-color: initial;
      --tw-inset-ring-shadow: 0 0 #0000;
      --tw-ring-inset: initial;
      --tw-ring-offset-width: 0px;
      --tw-ring-offset-color: #fff;
      --tw-ring-offset-shadow: 0 0 #0000;
      --tw-outline-style: solid;
      --tw-blur: initial;
      --tw-brightness: initial;
      --tw-contrast: initial;
      --tw-grayscale: initial;
      --tw-hue-rotate: initial;
      --tw-invert: initial;
      --tw-opacity: initial;
      --tw-saturate: initial;
      --tw-sepia: initial;
      --tw-drop-shadow: initial;
      --tw-drop-shadow-color: initial;
      --tw-drop-shadow-alpha: 100%;
      --tw-drop-shadow-size: initial;
      --tw-backdrop-blur: initial;
      --tw-backdrop-brightness: initial;
      --tw-backdrop-contrast: initial;
      --tw-backdrop-grayscale: initial;
      --tw-backdrop-hue-rotate: initial;
      --tw-backdrop-invert: initial;
      --tw-backdrop-opacity: initial;
      --tw-backdrop-saturate: initial;
      --tw-backdrop-sepia: initial;
      --tw-duration: initial;
      --tw-ease: initial;
      --tw-content: "";
    }
  }
}

@layer theme {
  :root, :host {
    --color-red-50: oklch(97.1% .013 17.38);
    --color-red-100: oklch(93.6% .032 17.717);
    --color-red-200: oklch(88.5% .062 18.334);
    --color-red-500: oklch(63.7% .237 25.331);
    --color-red-600: oklch(57.7% .245 27.325);
    --color-red-700: oklch(50.5% .213 27.518);
    --color-red-800: oklch(44.4% .177 26.899);
    --color-orange-100: oklch(95.4% .038 75.164);
    --color-orange-500: oklch(70.5% .213 47.604);
    --color-orange-600: oklch(64.6% .222 41.116);
    --color-orange-800: oklch(47% .157 37.304);
    --color-amber-100: oklch(96.2% .059 95.617);
    --color-amber-400: oklch(82.8% .189 84.429);
    --color-amber-500: oklch(76.9% .188 70.08);
    --color-amber-600: oklch(66.6% .179 58.318);
    --color-amber-800: oklch(47.3% .137 46.201);
    --color-yellow-50: oklch(98.7% .026 102.212);
    --color-yellow-100: oklch(97.3% .071 103.193);
    --color-yellow-200: oklch(94.5% .129 101.54);
    --color-yellow-400: oklch(85.2% .199 91.936);
    --color-yellow-500: oklch(79.5% .184 86.047);
    --color-yellow-600: oklch(68.1% .162 75.834);
    --color-yellow-700: oklch(55.4% .135 66.442);
    --color-yellow-800: oklch(47.6% .114 61.907);
    --color-green-50: oklch(98.2% .018 155.826);
    --color-green-100: oklch(96.2% .044 156.743);
    --color-green-200: oklch(92.5% .084 155.995);
    --color-green-400: oklch(79.2% .209 151.711);
    --color-green-500: oklch(72.3% .219 149.579);
    --color-green-600: oklch(62.7% .194 149.214);
    --color-green-700: oklch(52.7% .154 150.069);
    --color-green-800: oklch(44.8% .119 151.328);
    --color-emerald-50: oklch(97.9% .021 166.113);
    --color-teal-100: oklch(95.3% .051 180.801);
    --color-teal-800: oklch(43.7% .078 188.216);
    --color-cyan-100: oklch(95.6% .045 203.388);
    --color-cyan-800: oklch(45% .085 224.283);
    --color-blue-50: oklch(97% .014 254.604);
    --color-blue-100: oklch(93.2% .032 255.585);
    --color-blue-200: oklch(88.2% .059 254.128);
    --color-blue-500: oklch(62.3% .214 259.815);
    --color-blue-600: oklch(54.6% .245 262.881);
    --color-blue-700: oklch(48.8% .243 264.376);
    --color-blue-800: oklch(42.4% .199 265.638);
    --color-indigo-50: oklch(96.2% .018 272.314);
    --color-indigo-100: oklch(93% .034 272.788);
    --color-indigo-500: oklch(58.5% .233 277.117);
    --color-indigo-600: oklch(51.1% .262 276.966);
    --color-indigo-800: oklch(39.8% .195 277.366);
    --color-purple-50: oklch(97.7% .014 308.299);
    --color-purple-100: oklch(94.6% .033 307.174);
    --color-purple-200: oklch(90.2% .063 306.703);
    --color-purple-500: oklch(62.7% .265 303.9);
    --color-purple-600: oklch(55.8% .288 302.321);
    --color-purple-800: oklch(43.8% .218 303.724);
    --color-pink-100: oklch(94.8% .028 342.258);
    --color-pink-500: oklch(65.6% .241 354.308);
    --color-pink-800: oklch(45.9% .187 3.815);
    --color-rose-50: oklch(96.9% .015 12.422);
    --color-rose-100: oklch(94.1% .03 12.58);
    --color-rose-800: oklch(45.5% .188 13.697);
    --color-slate-50: oklch(98.4% .003 247.858);
    --color-slate-100: oklch(96.8% .007 247.896);
    --color-slate-800: oklch(27.9% .041 260.031);
    --color-slate-900: oklch(20.8% .042 265.755);
    --color-gray-50: oklch(98.5% .002 247.839);
    --color-gray-100: oklch(96.7% .003 264.542);
    --color-gray-200: oklch(92.8% .006 264.531);
    --color-gray-300: oklch(87.2% .01 258.338);
    --color-gray-400: oklch(70.7% .022 261.325);
    --color-gray-500: oklch(55.1% .027 264.364);
    --color-gray-600: oklch(44.6% .03 256.802);
    --color-gray-700: oklch(37.3% .034 259.733);
    --color-gray-800: oklch(27.8% .033 256.848);
    --color-gray-900: oklch(21% .034 264.665);
    --color-black: #000;
    --color-white: #fff;
    --spacing: .25rem;
    --container-xs: 20rem;
    --container-sm: 24rem;
    --container-md: 28rem;
    --container-lg: 32rem;
    --container-xl: 36rem;
    --container-2xl: 42rem;
    --container-3xl: 48rem;
    --container-4xl: 56rem;
    --container-5xl: 64rem;
    --container-6xl: 72rem;
    --container-7xl: 80rem;
    --text-xs: .75rem;
    --text-xs--line-height: calc(1 / .75);
    --text-sm: .875rem;
    --text-sm--line-height: calc(1.25 / .875);
    --text-base: 1rem;
    --text-base--line-height: calc(1.5 / 1);
    --text-lg: 1.125rem;
    --text-lg--line-height: calc(1.75 / 1.125);
    --text-xl: 1.25rem;
    --text-xl--line-height: calc(1.75 / 1.25);
    --text-2xl: 1.5rem;
    --text-2xl--line-height: calc(2 / 1.5);
    --text-3xl: 1.875rem;
    --text-3xl--line-height: calc(2.25 / 1.875);
    --text-6xl: 3.75rem;
    --text-6xl--line-height: 1;
    --font-weight-extralight: 200;
    --font-weight-normal: 400;
    --font-weight-medium: 500;
    --font-weight-semibold: 600;
    --font-weight-bold: 700;
    --tracking-tight: -.025em;
    --tracking-wider: .05em;
    --tracking-widest: .1em;
    --leading-tight: 1.25;
    --leading-relaxed: 1.625;
    --radius-xs: .125rem;
    --drop-shadow-md: 0 3px 3px #0000001f;
    --ease-in: cubic-bezier(.4, 0, 1, 1);
    --ease-out: cubic-bezier(0, 0, .2, 1);
    --ease-in-out: cubic-bezier(.4, 0, .2, 1);
    --animate-spin: spin 1s linear infinite;
    --animate-pulse: pulse 2s cubic-bezier(.4, 0, .6, 1) infinite;
    --blur-sm: 8px;
    --blur-md: 12px;
    --blur-lg: 16px;
    --blur-xl: 24px;
    --aspect-video: 16 / 9;
    --default-transition-duration: .15s;
    --default-transition-timing-function: cubic-bezier(.4, 0, .2, 1);
    --default-font-family: var(--font-geist-sans);
    --default-mono-font-family: var(--font-geist-mono);
    --color-border: var(--border);
  }
}

@layer base {
  *, :after, :before, ::backdrop, ::file-selector-button {
    box-sizing: border-box;
    border: 0 solid;
    margin: 0;
    padding: 0;
  }

  html, :host {
    -webkit-text-size-adjust: 100%;
    tab-size: 4;
    line-height: 1.5;
    font-family: var(--default-font-family, ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji");
    font-feature-settings: var(--default-font-feature-settings, normal);
    font-variation-settings: var(--default-font-variation-settings, normal);
    -webkit-tap-highlight-color: transparent;
  }

  hr {
    height: 0;
    color: inherit;
    border-top-width: 1px;
  }

  abbr:where([title]) {
    text-decoration: underline dotted;
  }

  h1, h2, h3, h4, h5, h6 {
    font-size: inherit;
    font-weight: inherit;
  }

  a {
    color: inherit;
    -webkit-text-decoration: inherit;
    text-decoration: inherit;
  }

  b, strong {
    font-weight: bolder;
  }

  code, kbd, samp, pre {
    font-family: var(--default-mono-font-family, ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace);
    font-feature-settings: var(--default-mono-font-feature-settings, normal);
    font-variation-settings: var(--default-mono-font-variation-settings, normal);
    font-size: 1em;
  }

  small {
    font-size: 80%;
  }

  sub, sup {
    vertical-align: baseline;
    font-size: 75%;
    line-height: 0;
    position: relative;
  }

  sub {
    bottom: -.25em;
  }

  sup {
    top: -.5em;
  }

  table {
    text-indent: 0;
    border-color: inherit;
    border-collapse: collapse;
  }

  :-moz-focusring {
    outline: auto;
  }

  progress {
    vertical-align: baseline;
  }

  summary {
    display: list-item;
  }

  ol, ul, menu {
    list-style: none;
  }

  img, svg, video, canvas, audio, iframe, embed, object {
    vertical-align: middle;
    display: block;
  }

  img, video {
    max-width: 100%;
    height: auto;
  }

  button, input, select, optgroup, textarea, ::file-selector-button {
    font: inherit;
    font-feature-settings: inherit;
    font-variation-settings: inherit;
    letter-spacing: inherit;
    color: inherit;
    opacity: 1;
    background-color: #0000;
    border-radius: 0;
  }

  :where(select:is([multiple], [size])) optgroup {
    font-weight: bolder;
  }

  :where(select:is([multiple], [size])) optgroup option {
    padding-inline-start: 20px;
  }

  ::file-selector-button {
    margin-inline-end: 4px;
  }

  ::placeholder {
    opacity: 1;
  }

  @supports (not ((-webkit-appearance: -apple-pay-button))) or (contain-intrinsic-size: 1px) {
    ::placeholder {
      color: currentColor;
    }

    @supports (color: color-mix(in lab, red, red)) {
      ::placeholder {
        color: color-mix(in oklab, currentcolor 50%, transparent);
      }
    }
  }

  textarea {
    resize: vertical;
  }

  ::-webkit-search-decoration {
    -webkit-appearance: none;
  }

  ::-webkit-date-and-time-value {
    min-height: 1lh;
    text-align: inherit;
  }

  ::-webkit-datetime-edit {
    display: inline-flex;
  }

  ::-webkit-datetime-edit-fields-wrapper {
    padding: 0;
  }

  ::-webkit-datetime-edit, ::-webkit-datetime-edit-year-field, ::-webkit-datetime-edit-month-field, ::-webkit-datetime-edit-day-field, ::-webkit-datetime-edit-hour-field, ::-webkit-datetime-edit-minute-field, ::-webkit-datetime-edit-second-field, ::-webkit-datetime-edit-millisecond-field, ::-webkit-datetime-edit-meridiem-field {
    padding-block: 0;
  }

  :-moz-ui-invalid {
    box-shadow: none;
  }

  button, input:where([type="button"], [type="reset"], [type="submit"]), ::file-selector-button {
    appearance: button;
  }

  ::-webkit-inner-spin-button, ::-webkit-outer-spin-button {
    height: auto;
  }

  [hidden]:where(:not([hidden="until-found"])) {
    display: none !important;
  }

  * {
    border-color: var(--border);
    outline-color: var(--ring);
  }

  @supports (color: color-mix(in lab, red, red)) {
    * {
      outline-color: color-mix(in oklab, var(--ring) 50%, transparent);
    }
  }

  body {
    background-color: var(--background);
    color: var(--foreground);
  }

  :root, [data-theme] {
    color: hsl(var(--heroui-foreground));
    background-color: hsl(var(--heroui-background));
  }

  :root, [data-theme="light"] {
    color-scheme: light;
    --heroui-background: 0 0% 100%;
    --heroui-foreground-50: 0 0% 98.04%;
    --heroui-foreground-100: 240 4.76% 95.88%;
    --heroui-foreground-200: 240 5.88% 90%;
    --heroui-foreground-300: 240 4.88% 83.92%;
    --heroui-foreground-400: 240 5.03% 64.9%;
    --heroui-foreground-500: 240 3.83% 46.08%;
    --heroui-foreground-600: 240 5.2% 33.92%;
    --heroui-foreground-700: 240 5.26% 26.08%;
    --heroui-foreground-800: 240 3.7% 15.88%;
    --heroui-foreground-900: 240 5.88% 10%;
    --heroui-foreground: 201.82 24.44% 8.82%;
    --heroui-divider: 0 0% 6.67%;
    --heroui-focus: 212.02 100% 46.67%;
    --heroui-overlay: 0 0% 0%;
    --heroui-content1: 0 0% 100%;
    --heroui-content1-foreground: 201.82 24.44% 8.82%;
    --heroui-content2: 240 4.76% 95.88%;
    --heroui-content2-foreground: 240 3.7% 15.88%;
    --heroui-content3: 240 5.88% 90%;
    --heroui-content3-foreground: 240 5.26% 26.08%;
    --heroui-content4: 240 4.88% 83.92%;
    --heroui-content4-foreground: 240 5.2% 33.92%;
    --heroui-default-50: 0 0% 98.04%;
    --heroui-default-100: 240 4.76% 95.88%;
    --heroui-default-200: 240 5.88% 90%;
    --heroui-default-300: 240 4.88% 83.92%;
    --heroui-default-400: 240 5.03% 64.9%;
    --heroui-default-500: 240 3.83% 46.08%;
    --heroui-default-600: 240 5.2% 33.92%;
    --heroui-default-700: 240 5.26% 26.08%;
    --heroui-default-800: 240 3.7% 15.88%;
    --heroui-default-900: 240 5.88% 10%;
    --heroui-default-foreground: 0 0% 0%;
    --heroui-default: 240 4.88% 83.92%;
    --heroui-primary-50: 212.5 92.31% 94.9%;
    --heroui-primary-100: 211.84 92.45% 89.61%;
    --heroui-primary-200: 211.84 92.45% 79.22%;
    --heroui-primary-300: 212.24 92.45% 68.82%;
    --heroui-primary-400: 212.14 92.45% 58.43%;
    --heroui-primary-500: 212.02 100% 46.67%;
    --heroui-primary-600: 212.14 100% 38.43%;
    --heroui-primary-700: 212.24 100% 28.82%;
    --heroui-primary-800: 211.84 100% 19.22%;
    --heroui-primary-900: 211.84 100% 9.61%;
    --heroui-primary-foreground: 0 0% 100%;
    --heroui-primary: 212.02 100% 46.67%;
    --heroui-secondary-50: 270 61.54% 94.9%;
    --heroui-secondary-100: 270 59.26% 89.41%;
    --heroui-secondary-200: 270 59.26% 78.82%;
    --heroui-secondary-300: 270 59.26% 68.24%;
    --heroui-secondary-400: 270 59.26% 57.65%;
    --heroui-secondary-500: 270 66.67% 47.06%;
    --heroui-secondary-600: 270 66.67% 37.65%;
    --heroui-secondary-700: 270 66.67% 28.24%;
    --heroui-secondary-800: 270 66.67% 18.82%;
    --heroui-secondary-900: 270 66.67% 9.41%;
    --heroui-secondary-foreground: 0 0% 100%;
    --heroui-secondary: 270 66.67% 47.06%;
    --heroui-success-50: 146.67 64.29% 94.51%;
    --heroui-success-100: 145.71 61.4% 88.82%;
    --heroui-success-200: 146.2 61.74% 77.45%;
    --heroui-success-300: 145.79 62.57% 66.47%;
    --heroui-success-400: 146.01 62.45% 55.1%;
    --heroui-success-500: 145.96 79.46% 43.92%;
    --heroui-success-600: 146.01 79.89% 35.1%;
    --heroui-success-700: 145.79 79.26% 26.47%;
    --heroui-success-800: 146.2 79.78% 17.45%;
    --heroui-success-900: 145.71 77.78% 8.82%;
    --heroui-success-foreground: 0 0% 0%;
    --heroui-success: 145.96 79.46% 43.92%;
    --heroui-warning-50: 54.55 91.67% 95.29%;
    --heroui-warning-100: 37.14 91.3% 90.98%;
    --heroui-warning-200: 37.14 91.3% 81.96%;
    --heroui-warning-300: 36.96 91.24% 73.14%;
    --heroui-warning-400: 37.01 91.26% 64.12%;
    --heroui-warning-500: 37.03 91.27% 55.1%;
    --heroui-warning-600: 37.01 74.22% 44.12%;
    --heroui-warning-700: 36.96 73.96% 33.14%;
    --heroui-warning-800: 37.14 75% 21.96%;
    --heroui-warning-900: 37.14 75% 10.98%;
    --heroui-warning-foreground: 0 0% 0%;
    --heroui-warning: 37.03 91.27% 55.1%;
    --heroui-danger-50: 339.13 92% 95.1%;
    --heroui-danger-100: 340 91.84% 90.39%;
    --heroui-danger-200: 339.33 90% 80.39%;
    --heroui-danger-300: 339.11 90.6% 70.78%;
    --heroui-danger-400: 339 90% 60.78%;
    --heroui-danger-500: 339.2 90.36% 51.18%;
    --heroui-danger-600: 339 86.54% 40.78%;
    --heroui-danger-700: 339.11 85.99% 30.78%;
    --heroui-danger-800: 339.33 86.54% 20.39%;
    --heroui-danger-900: 340 84.91% 10.39%;
    --heroui-danger-foreground: 0 0% 100%;
    --heroui-danger: 339.2 90.36% 51.18%;
    --heroui-divider-weight: 1px;
    --heroui-disabled-opacity: .5;
    --heroui-font-size-tiny: .75rem;
    --heroui-font-size-small: .875rem;
    --heroui-font-size-medium: 1rem;
    --heroui-font-size-large: 1.125rem;
    --heroui-line-height-tiny: 1rem;
    --heroui-line-height-small: 1.25rem;
    --heroui-line-height-medium: 1.5rem;
    --heroui-line-height-large: 1.75rem;
    --heroui-radius-small: 8px;
    --heroui-radius-medium: 12px;
    --heroui-radius-large: 14px;
    --heroui-border-width-small: 1px;
    --heroui-border-width-medium: 2px;
    --heroui-border-width-large: 3px;
    --heroui-box-shadow-small: 0px 0px 5px 0px #00000005, 0px 2px 10px 0px #0000000f, 0px 0px 1px 0px #0000004d;
    --heroui-box-shadow-medium: 0px 0px 15px 0px #00000008, 0px 2px 30px 0px #00000014, 0px 0px 1px 0px #0000004d;
    --heroui-box-shadow-large: 0px 0px 30px 0px #0000000a, 0px 30px 60px 0px #0000001f, 0px 0px 1px 0px #0000004d;
    --heroui-hover-opacity: .8;
  }
}

@layer components;

@layer utilities {
  .\@container\/card {
    container: card / inline-size;
  }

  .\@container\/card-header {
    container: card-header / inline-size;
  }

  .\@container\/main {
    container: main / inline-size;
  }

  .pointer-events-auto {
    pointer-events: auto;
  }

  .pointer-events-none {
    pointer-events: none;
  }

  .invisible {
    visibility: hidden;
  }

  .visible {
    visibility: visible;
  }

  .sr-only {
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border-width: 0;
    width: 1px;
    height: 1px;
    margin: -1px;
    padding: 0;
    position: absolute;
    overflow: hidden;
  }

  .absolute {
    position: absolute;
  }

  .fixed {
    position: fixed;
  }

  .relative {
    position: relative;
  }

  .static {
    position: static;
  }

  .sticky {
    position: sticky;
  }

  .inset-0 {
    inset: calc(var(--spacing) * 0);
  }

  .inset-x-0 {
    inset-inline: calc(var(--spacing) * 0);
  }

  .inset-y-0 {
    inset-block: calc(var(--spacing) * 0);
  }

  .start-0 {
    inset-inline-start: calc(var(--spacing) * 0);
  }

  .start-1\.5 {
    inset-inline-start: calc(var(--spacing) * 1.5);
  }

  .start-2 {
    inset-inline-start: calc(var(--spacing) * 2);
  }

  .start-3 {
    inset-inline-start: calc(var(--spacing) * 3);
  }

  .start-auto {
    inset-inline-start: auto;
  }

  .-end-0\.5 {
    inset-inline-end: calc(var(--spacing) * -.5);
  }

  .end-1 {
    inset-inline-end: calc(var(--spacing) * 1);
  }

  .end-1\.5 {
    inset-inline-end: calc(var(--spacing) * 1.5);
  }

  .end-3 {
    inset-inline-end: calc(var(--spacing) * 3);
  }

  .end-auto {
    inset-inline-end: auto;
  }

  .-top-1 {
    top: calc(var(--spacing) * -1);
  }

  .-top-2 {
    top: calc(var(--spacing) * -2);
  }

  .-top-12 {
    top: calc(var(--spacing) * -12);
  }

  .-top-px {
    top: -1px;
  }

  .top-0 {
    top: calc(var(--spacing) * 0);
  }

  .top-1 {
    top: calc(var(--spacing) * 1);
  }

  .top-1\.5 {
    top: calc(var(--spacing) * 1.5);
  }

  .top-1\/2 {
    top: 50%;
  }

  .top-2 {
    top: calc(var(--spacing) * 2);
  }

  .top-2\.5 {
    top: calc(var(--spacing) * 2.5);
  }

  .top-3\.5 {
    top: calc(var(--spacing) * 3.5);
  }

  .top-4 {
    top: calc(var(--spacing) * 4);
  }

  .top-5 {
    top: calc(var(--spacing) * 5);
  }

  .top-\[1px\] {
    top: 1px;
  }

  .top-\[5\%\] {
    top: 5%;
  }

  .top-\[10\%\] {
    top: 10%;
  }

  .top-\[50\%\] {
    top: 50%;
  }

  .top-\[60\%\] {
    top: 60%;
  }

  .top-\[calc\(46\%\)\] {
    top: 46%;
  }

  .top-\[calc\(100\%_\+_2px\)\] {
    top: calc(100% + 2px);
  }

  .top-\[var\(--navbar-height\)\] {
    top: var(--navbar-height);
  }

  .top-full {
    top: 100%;
  }

  .-right-1 {
    right: calc(var(--spacing) * -1);
  }

  .-right-2 {
    right: calc(var(--spacing) * -2);
  }

  .-right-12 {
    right: calc(var(--spacing) * -12);
  }

  .-right-px {
    right: -1px;
  }

  .right-0 {
    right: calc(var(--spacing) * 0);
  }

  .right-1 {
    right: calc(var(--spacing) * 1);
  }

  .right-2 {
    right: calc(var(--spacing) * 2);
  }

  .right-3 {
    right: calc(var(--spacing) * 3);
  }

  .right-4 {
    right: calc(var(--spacing) * 4);
  }

  .right-6 {
    right: calc(var(--spacing) * 6);
  }

  .right-\[5\%\] {
    right: 5%;
  }

  .right-\[10\%\] {
    right: 10%;
  }

  .right-\[calc\(-50\%\+20px\)\] {
    right: calc(20px - 50%);
  }

  .-bottom-0\.5 {
    bottom: calc(var(--spacing) * -.5);
  }

  .-bottom-12 {
    bottom: calc(var(--spacing) * -12);
  }

  .bottom-0 {
    bottom: calc(var(--spacing) * 0);
  }

  .bottom-4 {
    bottom: calc(var(--spacing) * 4);
  }

  .bottom-20 {
    bottom: calc(var(--spacing) * 20);
  }

  .bottom-\[5\%\] {
    bottom: 5%;
  }

  .bottom-\[10\%\] {
    bottom: 10%;
  }

  .-left-12 {
    left: calc(var(--spacing) * -12);
  }

  .left-0 {
    left: calc(var(--spacing) * 0);
  }

  .left-1 {
    left: calc(var(--spacing) * 1);
  }

  .left-1\/2 {
    left: 50%;
  }

  .left-2 {
    left: calc(var(--spacing) * 2);
  }

  .left-2\.5 {
    left: calc(var(--spacing) * 2.5);
  }

  .left-\[5\%\] {
    left: 5%;
  }

  .left-\[10\%\] {
    left: 10%;
  }

  .left-\[50\%\] {
    left: 50%;
  }

  .left-\[calc\(37\.5\%\)\] {
    left: 37.5%;
  }

  .left-\[calc\(50\%\+30px\)\] {
    left: calc(50% + 30px);
  }

  .isolate {
    isolation: isolate;
  }

  .-z-30 {
    z-index: calc(30 * -1);
  }

  .z-0 {
    z-index: 0;
  }

  .z-10 {
    z-index: 10;
  }

  .z-20 {
    z-index: 20;
  }

  .z-30 {
    z-index: 30;
  }

  .z-40 {
    z-index: 40;
  }

  .z-50 {
    z-index: 50;
  }

  .z-\[1\] {
    z-index: 1;
  }

  .z-\[100\] {
    z-index: 100;
  }

  .order-1 {
    order: 1;
  }

  .order-2 {
    order: 2;
  }

  .order-3 {
    order: 3;
  }

  .col-span-1 {
    grid-column: span 1 / span 1;
  }

  .col-span-2 {
    grid-column: span 2 / span 2;
  }

  .col-span-3 {
    grid-column: span 3 / span 3;
  }

  .col-span-full {
    grid-column: 1 / -1;
  }

  .col-start-2 {
    grid-column-start: 2;
  }

  .row-span-2 {
    grid-row: span 2 / span 2;
  }

  .row-start-1 {
    grid-row-start: 1;
  }

  .container {
    width: 100%;
  }

  @media (width >= 40rem) {
    .container {
      max-width: 40rem;
    }
  }

  @media (width >= 48rem) {
    .container {
      max-width: 48rem;
    }
  }

  @media (width >= 64rem) {
    .container {
      max-width: 64rem;
    }
  }

  @media (width >= 80rem) {
    .container {
      max-width: 80rem;
    }
  }

  @media (width >= 96rem) {
    .container {
      max-width: 96rem;
    }
  }

  .-m-2 {
    margin: calc(var(--spacing) * -2);
  }

  .-m-2\.5 {
    margin: calc(var(--spacing) * -2.5);
  }

  .m-0 {
    margin: calc(var(--spacing) * 0);
  }

  .-mx-1 {
    margin-inline: calc(var(--spacing) * -1);
  }

  .-mx-2 {
    margin-inline: calc(var(--spacing) * -2);
  }

  .mx-0 {
    margin-inline: calc(var(--spacing) * 0);
  }

  .mx-1 {
    margin-inline: calc(var(--spacing) * 1);
  }

  .mx-2 {
    margin-inline: calc(var(--spacing) * 2);
  }

  .mx-3\.5 {
    margin-inline: calc(var(--spacing) * 3.5);
  }

  .mx-\[calc\(\(theme\(spacing\.5\)-theme\(spacing\.1\)\)\/2\)\] {
    margin-inline: .5rem;
  }

  .mx-\[calc\(\(theme\(spacing\.6\)-theme\(spacing\.3\)\)\/2\)\] {
    margin-inline: .375rem;
  }

  .mx-\[calc\(\(theme\(spacing\.7\)-theme\(spacing\.5\)\)\/2\)\] {
    margin-inline: .25rem;
  }

  .mx-auto {
    margin-inline: auto;
  }

  .my-0 {
    margin-block: calc(var(--spacing) * 0);
  }

  .my-0\.5 {
    margin-block: calc(var(--spacing) * .5);
  }

  .my-1 {
    margin-block: calc(var(--spacing) * 1);
  }

  .my-3 {
    margin-block: calc(var(--spacing) * 3);
  }

  .my-4 {
    margin-block: calc(var(--spacing) * 4);
  }

  .my-16 {
    margin-block: calc(var(--spacing) * 16);
  }

  .my-\[calc\(\(theme\(spacing\.5\)-theme\(spacing\.1\)\)\/2\)\] {
    margin-block: .5rem;
  }

  .my-\[calc\(\(theme\(spacing\.6\)-theme\(spacing\.3\)\)\/2\)\] {
    margin-block: .375rem;
  }

  .my-\[calc\(\(theme\(spacing\.7\)-theme\(spacing\.5\)\)\/2\)\] {
    margin-block: .25rem;
  }

  .my-auto {
    margin-block: auto;
  }

  .-ms-2 {
    margin-inline-start: calc(var(--spacing) * -2);
  }

  .ms-2 {
    margin-inline-start: calc(var(--spacing) * 2);
  }

  .me-2 {
    margin-inline-end: calc(var(--spacing) * 2);
  }

  .me-4 {
    margin-inline-end: calc(var(--spacing) * 4);
  }

  .-mt-1 {
    margin-top: calc(var(--spacing) * -1);
  }

  .-mt-4 {
    margin-top: calc(var(--spacing) * -4);
  }

  .mt-0 {
    margin-top: calc(var(--spacing) * 0);
  }

  .mt-0\.5 {
    margin-top: calc(var(--spacing) * .5);
  }

  .mt-1 {
    margin-top: calc(var(--spacing) * 1);
  }

  .mt-1\.5 {
    margin-top: calc(var(--spacing) * 1.5);
  }

  .mt-2 {
    margin-top: calc(var(--spacing) * 2);
  }

  .mt-4 {
    margin-top: calc(var(--spacing) * 4);
  }

  .mt-6 {
    margin-top: calc(var(--spacing) * 6);
  }

  .mt-8 {
    margin-top: calc(var(--spacing) * 8);
  }

  .mt-\[1px\] {
    margin-top: 1px;
  }

  .mt-auto {
    margin-top: auto;
  }

  .-mr-2 {
    margin-right: calc(var(--spacing) * -2);
  }

  .mr-1 {
    margin-right: calc(var(--spacing) * 1);
  }

  .mr-2 {
    margin-right: calc(var(--spacing) * 2);
  }

  .mr-3 {
    margin-right: calc(var(--spacing) * 3);
  }

  .mb-0\.5 {
    margin-bottom: calc(var(--spacing) * .5);
  }

  .mb-1 {
    margin-bottom: calc(var(--spacing) * 1);
  }

  .mb-1\.5 {
    margin-bottom: calc(var(--spacing) * 1.5);
  }

  .mb-2 {
    margin-bottom: calc(var(--spacing) * 2);
  }

  .mb-3 {
    margin-bottom: calc(var(--spacing) * 3);
  }

  .mb-4 {
    margin-bottom: calc(var(--spacing) * 4);
  }

  .mb-5 {
    margin-bottom: calc(var(--spacing) * 5);
  }

  .mb-6 {
    margin-bottom: calc(var(--spacing) * 6);
  }

  .mb-8 {
    margin-bottom: calc(var(--spacing) * 8);
  }

  .mb-10 {
    margin-bottom: calc(var(--spacing) * 10);
  }

  .mb-px {
    margin-bottom: 1px;
  }

  .-ml-1 {
    margin-left: calc(var(--spacing) * -1);
  }

  .-ml-4 {
    margin-left: calc(var(--spacing) * -4);
  }

  .ml-1 {
    margin-left: calc(var(--spacing) * 1);
  }

  .ml-1\.5 {
    margin-left: calc(var(--spacing) * 1.5);
  }

  .ml-2 {
    margin-left: calc(var(--spacing) * 2);
  }

  .ml-4 {
    margin-left: calc(var(--spacing) * 4);
  }

  .ml-auto {
    margin-left: auto;
  }

  .box-border {
    box-sizing: border-box;
  }

  .box-content {
    box-sizing: content-box;
  }

  .line-clamp-1 {
    -webkit-line-clamp: 1;
    -webkit-box-orient: vertical;
    display: -webkit-box;
    overflow: hidden;
  }

  .line-clamp-2 {
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    display: -webkit-box;
    overflow: hidden;
  }

  .line-clamp-3 {
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    display: -webkit-box;
    overflow: hidden;
  }

  .scrollbar-default {
    -ms-overflow-style: auto;
    scrollbar-width: auto;
  }

  .scrollbar-default::-webkit-scrollbar {
    display: block;
  }

  .scrollbar-hide {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }

  .scrollbar-hide::-webkit-scrollbar {
    display: none;
  }

  .block {
    display: block;
  }

  .flex {
    display: flex;
  }

  .grid {
    display: grid;
  }

  .hidden {
    display: none;
  }

  .inline {
    display: inline;
  }

  .inline-block {
    display: inline-block;
  }

  .inline-flex {
    display: inline-flex;
  }

  .inline-grid {
    display: inline-grid;
  }

  .table {
    display: table;
  }

  .table-caption {
    display: table-caption;
  }

  .table-cell {
    display: table-cell;
  }

  .table-row {
    display: table-row;
  }

  .field-sizing-content {
    field-sizing: content;
  }

  .aspect-\[3\/2\] {
    aspect-ratio: 3 / 2;
  }

  .aspect-auto {
    aspect-ratio: auto;
  }

  .aspect-square {
    aspect-ratio: 1;
  }

  .aspect-video {
    aspect-ratio: var(--aspect-video);
  }

  .size-1 {
    width: calc(var(--spacing) * 1);
    height: calc(var(--spacing) * 1);
  }

  .size-1\.5 {
    width: calc(var(--spacing) * 1.5);
    height: calc(var(--spacing) * 1.5);
  }

  .size-2 {
    width: calc(var(--spacing) * 2);
    height: calc(var(--spacing) * 2);
  }

  .size-2\.5 {
    width: calc(var(--spacing) * 2.5);
    height: calc(var(--spacing) * 2.5);
  }

  .size-3 {
    width: calc(var(--spacing) * 3);
    height: calc(var(--spacing) * 3);
  }

  .size-3\.5 {
    width: calc(var(--spacing) * 3.5);
    height: calc(var(--spacing) * 3.5);
  }

  .size-4 {
    width: calc(var(--spacing) * 4);
    height: calc(var(--spacing) * 4);
  }

  .size-5 {
    width: calc(var(--spacing) * 5);
    height: calc(var(--spacing) * 5);
  }

  .size-6 {
    width: calc(var(--spacing) * 6);
    height: calc(var(--spacing) * 6);
  }

  .size-7 {
    width: calc(var(--spacing) * 7);
    height: calc(var(--spacing) * 7);
  }

  .size-8 {
    width: calc(var(--spacing) * 8);
    height: calc(var(--spacing) * 8);
  }

  .size-9 {
    width: calc(var(--spacing) * 9);
    height: calc(var(--spacing) * 9);
  }

  .size-10 {
    width: calc(var(--spacing) * 10);
    height: calc(var(--spacing) * 10);
  }

  .size-12 {
    width: calc(var(--spacing) * 12);
    height: calc(var(--spacing) * 12);
  }

  .size-16 {
    width: calc(var(--spacing) * 16);
    height: calc(var(--spacing) * 16);
  }

  .size-full {
    width: 100%;
    height: 100%;
  }

  .\!h-auto {
    height: auto !important;
  }

  .h-\(--header-height\) {
    height: var(--header-height);
  }

  .h-0\.5 {
    height: calc(var(--spacing) * .5);
  }

  .h-1 {
    height: calc(var(--spacing) * 1);
  }

  .h-1\.5 {
    height: calc(var(--spacing) * 1.5);
  }

  .h-2 {
    height: calc(var(--spacing) * 2);
  }

  .h-2\.5 {
    height: calc(var(--spacing) * 2.5);
  }

  .h-3 {
    height: calc(var(--spacing) * 3);
  }

  .h-3\.5 {
    height: calc(var(--spacing) * 3.5);
  }

  .h-4 {
    height: calc(var(--spacing) * 4);
  }

  .h-5 {
    height: calc(var(--spacing) * 5);
  }

  .h-6 {
    height: calc(var(--spacing) * 6);
  }

  .h-7 {
    height: calc(var(--spacing) * 7);
  }

  .h-8 {
    height: calc(var(--spacing) * 8);
  }

  .h-9 {
    height: calc(var(--spacing) * 9);
  }

  .h-10 {
    height: calc(var(--spacing) * 10);
  }

  .h-12 {
    height: calc(var(--spacing) * 12);
  }

  .h-14 {
    height: calc(var(--spacing) * 14);
  }

  .h-15 {
    height: calc(var(--spacing) * 15);
  }

  .h-16 {
    height: calc(var(--spacing) * 16);
  }

  .h-20 {
    height: calc(var(--spacing) * 20);
  }

  .h-24 {
    height: calc(var(--spacing) * 24);
  }

  .h-40 {
    height: calc(var(--spacing) * 40);
  }

  .h-64 {
    height: calc(var(--spacing) * 64);
  }

  .h-80 {
    height: calc(var(--spacing) * 80);
  }

  .h-\[--visual-viewport-height\] {
    height: --visual-viewport-height;
  }

  .h-\[1\.15rem\] {
    height: 1.15rem;
  }

  .h-\[2px\] {
    height: 2px;
  }

  .h-\[8\%\] {
    height: 8%;
  }

  .h-\[40px\] {
    height: 40px;
  }

  .h-\[50\%\] {
    height: 50%;
  }

  .h-\[100dvh\] {
    height: 100dvh;
  }

  .h-\[100px\] {
    height: 100px;
  }

  .h-\[250px\] {
    height: 250px;
  }

  .h-\[300px\] {
    height: 300px;
  }

  .h-\[320px\] {
    height: 320px;
  }

  .h-\[350px\] {
    height: 350px;
  }

  .h-\[400px\] {
    height: 400px;
  }

  .h-\[500px\] {
    height: 500px;
  }

  .h-\[calc\(100\%-1px\)\] {
    height: calc(100% - 1px);
  }

  .h-\[calc\(100dvh_-_var\(--navbar-height\)\)\] {
    height: calc(100dvh - var(--navbar-height));
  }

  .h-\[calc\(100svh-40px\)\] {
    height: calc(100svh - 40px);
  }

  .h-\[calc\(100svh-80px\)\] {
    height: calc(100svh - 80px);
  }

  .h-\[var\(--navbar-height\)\] {
    height: var(--navbar-height);
  }

  .h-\[var\(--picker-height\)\] {
    height: var(--picker-height);
  }

  .h-\[var\(--radix-navigation-menu-viewport-height\)\] {
    height: var(--radix-navigation-menu-viewport-height);
  }

  .h-\[var\(--radix-select-trigger-height\)\] {
    height: var(--radix-select-trigger-height);
  }

  .h-auto {
    height: auto;
  }

  .h-divider {
    height: var(--heroui-divider-weight);
  }

  .h-fit {
    height: fit-content;
  }

  .h-full {
    height: 100%;
  }

  .h-px {
    height: 1px;
  }

  .h-screen {
    height: 100vh;
  }

  .h-svh {
    height: 100svh;
  }

  .max-h-\(--radix-context-menu-content-available-height\) {
    max-height: var(--radix-context-menu-content-available-height);
  }

  .max-h-\(--radix-dropdown-menu-content-available-height\) {
    max-height: var(--radix-dropdown-menu-content-available-height);
  }

  .max-h-\(--radix-select-content-available-height\) {
    max-height: var(--radix-select-content-available-height);
  }

  .max-h-60 {
    max-height: calc(var(--spacing) * 60);
  }

  .max-h-64 {
    max-height: calc(var(--spacing) * 64);
  }

  .max-h-\[20rem\] {
    max-height: 20rem;
  }

  .max-h-\[24rem\] {
    max-height: 24rem;
  }

  .max-h-\[28rem\] {
    max-height: 28rem;
  }

  .max-h-\[32rem\] {
    max-height: 32rem;
  }

  .max-h-\[36rem\] {
    max-height: 36rem;
  }

  .max-h-\[42rem\] {
    max-height: 42rem;
  }

  .max-h-\[48rem\] {
    max-height: 48rem;
  }

  .max-h-\[56rem\] {
    max-height: 56rem;
  }

  .max-h-\[60px\] {
    max-height: 60px;
  }

  .max-h-\[60vh\] {
    max-height: 60vh;
  }

  .max-h-\[64rem\] {
    max-height: 64rem;
  }

  .max-h-\[80vh\] {
    max-height: 80vh;
  }

  .max-h-\[90vh\] {
    max-height: 90vh;
  }

  .max-h-\[100px\] {
    max-height: 100px;
  }

  .max-h-\[150px\] {
    max-height: 150px;
  }

  .max-h-\[200px\] {
    max-height: 200px;
  }

  .max-h-\[250px\] {
    max-height: 250px;
  }

  .max-h-\[300px\] {
    max-height: 300px;
  }

  .max-h-\[400px\] {
    max-height: 400px;
  }

  .max-h-\[calc\(90vh-180px\)\] {
    max-height: calc(90vh - 180px);
  }

  .max-h-\[calc\(100\%_-_8rem\)\] {
    max-height: calc(100% - 8rem);
  }

  .max-h-\[none\] {
    max-height: none;
  }

  .max-h-\[var\(--radix-dropdown-menu-content-available-height\)\] {
    max-height: var(--radix-dropdown-menu-content-available-height);
  }

  .max-h-full {
    max-height: 100%;
  }

  .min-h-0 {
    min-height: calc(var(--spacing) * 0);
  }

  .min-h-3 {
    min-height: calc(var(--spacing) * 3);
  }

  .min-h-3\.5 {
    min-height: calc(var(--spacing) * 3.5);
  }

  .min-h-4 {
    min-height: calc(var(--spacing) * 4);
  }

  .min-h-5 {
    min-height: calc(var(--spacing) * 5);
  }

  .min-h-6 {
    min-height: calc(var(--spacing) * 6);
  }

  .min-h-7 {
    min-height: calc(var(--spacing) * 7);
  }

  .min-h-8 {
    min-height: calc(var(--spacing) * 8);
  }

  .min-h-10 {
    min-height: calc(var(--spacing) * 10);
  }

  .min-h-12 {
    min-height: calc(var(--spacing) * 12);
  }

  .min-h-14 {
    min-height: calc(var(--spacing) * 14);
  }

  .min-h-16 {
    min-height: calc(var(--spacing) * 16);
  }

  .min-h-\[32px\] {
    min-height: 32px;
  }

  .min-h-\[52px\] {
    min-height: 52px;
  }

  .min-h-\[60px\] {
    min-height: 60px;
  }

  .min-h-\[60vh\] {
    min-height: 60vh;
  }

  .min-h-\[80px\] {
    min-height: 80px;
  }

  .min-h-\[80vh\] {
    min-height: 80vh;
  }

  .min-h-\[85px\] {
    min-height: 85px;
  }

  .min-h-\[100dvh\] {
    min-height: 100dvh;
  }

  .min-h-\[100px\] {
    min-height: 100px;
  }

  .min-h-\[105px\] {
    min-height: 105px;
  }

  .min-h-\[120px\] {
    min-height: 120px;
  }

  .min-h-\[150px\] {
    min-height: 150px;
  }

  .min-h-\[200px\] {
    min-height: 200px;
  }

  .min-h-\[300px\] {
    min-height: 300px;
  }

  .min-h-screen {
    min-height: 100vh;
  }

  .min-h-svh {
    min-height: 100svh;
  }

  .w-\(--radix-dropdown-menu-trigger-width\) {
    width: var(--radix-dropdown-menu-trigger-width);
  }

  .w-\(--sidebar-width\) {
    width: var(--sidebar-width);
  }

  .w-0 {
    width: calc(var(--spacing) * 0);
  }

  .w-0\.5 {
    width: calc(var(--spacing) * .5);
  }

  .w-1 {
    width: calc(var(--spacing) * 1);
  }

  .w-1\.5 {
    width: calc(var(--spacing) * 1.5);
  }

  .w-1\/2 {
    width: 50%;
  }

  .w-1\/3 {
    width: 33.3333%;
  }

  .w-2 {
    width: calc(var(--spacing) * 2);
  }

  .w-2\.5 {
    width: calc(var(--spacing) * 2.5);
  }

  .w-2\/3 {
    width: 66.6667%;
  }

  .w-3 {
    width: calc(var(--spacing) * 3);
  }

  .w-3\.5 {
    width: calc(var(--spacing) * 3.5);
  }

  .w-3\/4 {
    width: 75%;
  }

  .w-4 {
    width: calc(var(--spacing) * 4);
  }

  .w-5 {
    width: calc(var(--spacing) * 5);
  }

  .w-6 {
    width: calc(var(--spacing) * 6);
  }

  .w-7 {
    width: calc(var(--spacing) * 7);
  }

  .w-8 {
    width: calc(var(--spacing) * 8);
  }

  .w-9 {
    width: calc(var(--spacing) * 9);
  }

  .w-10 {
    width: calc(var(--spacing) * 10);
  }

  .w-12 {
    width: calc(var(--spacing) * 12);
  }

  .w-14 {
    width: calc(var(--spacing) * 14);
  }

  .w-16 {
    width: calc(var(--spacing) * 16);
  }

  .w-20 {
    width: calc(var(--spacing) * 20);
  }

  .w-24 {
    width: calc(var(--spacing) * 24);
  }

  .w-32 {
    width: calc(var(--spacing) * 32);
  }

  .w-38 {
    width: calc(var(--spacing) * 38);
  }

  .w-40 {
    width: calc(var(--spacing) * 40);
  }

  .w-48 {
    width: calc(var(--spacing) * 48);
  }

  .w-56 {
    width: calc(var(--spacing) * 56);
  }

  .w-60 {
    width: calc(var(--spacing) * 60);
  }

  .w-64 {
    width: calc(var(--spacing) * 64);
  }

  .w-72 {
    width: calc(var(--spacing) * 72);
  }

  .w-80 {
    width: calc(var(--spacing) * 80);
  }

  .w-\[--radix-dropdown-menu-trigger-width\] {
    width: --radix-dropdown-menu-trigger-width;
  }

  .w-\[10\%\] {
    width: 10%;
  }

  .w-\[15\%\] {
    width: 15%;
  }

  .w-\[25\%\] {
    width: 25%;
  }

  .w-\[30\%\] {
    width: 30%;
  }

  .w-\[50px\] {
    width: 50px;
  }

  .w-\[60px\] {
    width: 60px;
  }

  .w-\[70px\] {
    width: 70px;
  }

  .w-\[80\%\] {
    width: 80%;
  }

  .w-\[100px\] {
    width: 100px;
  }

  .w-\[100x\] {
    width: 100x;
  }

  .w-\[120px\] {
    width: 120px;
  }

  .w-\[150px\] {
    width: 150px;
  }

  .w-\[160px\] {
    width: 160px;
  }

  .w-\[180px\] {
    width: 180px;
  }

  .w-\[200px\] {
    width: 200px;
  }

  .w-\[240px\] {
    width: 240px;
  }

  .w-\[250px\] {
    width: 250px;
  }

  .w-\[300px\] {
    width: 300px;
  }

  .w-\[350px\] {
    width: 350px;
  }

  .w-\[400px\] {
    width: 400px;
  }

  .w-\[450px\] {
    width: 450px;
  }

  .w-\[480px\] {
    width: 480px;
  }

  .w-\[calc\(100\%_-_16px\)\] {
    width: calc(100% - 16px);
  }

  .w-\[calc\(100\%_-_theme\(spacing\.6\)\)\] {
    width: calc(100% - 1.5rem);
  }

  .w-\[calc\(var\(--visible-months\)_\*_var\(--calendar-width\)\)\] {
    width: calc(var(--visible-months) * var(--calendar-width));
  }

  .w-\[var\(--radix-popover-trigger-width\)\] {
    width: var(--radix-popover-trigger-width);
  }

  .w-auto {
    width: auto;
  }

  .w-divider {
    width: var(--heroui-divider-weight);
  }

  .w-fit {
    width: fit-content;
  }

  .w-full {
    width: 100%;
  }

  .w-max {
    width: max-content;
  }

  .w-px {
    width: 1px;
  }

  .w-screen {
    width: 100vw;
  }

  .max-w-\(--skeleton-width\) {
    max-width: var(--skeleton-width);
  }

  .max-w-2xl {
    max-width: var(--container-2xl);
  }

  .max-w-3xl {
    max-width: var(--container-3xl);
  }

  .max-w-4xl {
    max-width: var(--container-4xl);
  }

  .max-w-5xl {
    max-width: var(--container-5xl);
  }

  .max-w-6xl {
    max-width: var(--container-6xl);
  }

  .max-w-7xl {
    max-width: var(--container-7xl);
  }

  .max-w-\[100px\] {
    max-width: 100px;
  }

  .max-w-\[120px\] {
    max-width: 120px;
  }

  .max-w-\[140px\] {
    max-width: 140px;
  }

  .max-w-\[150px\] {
    max-width: 150px;
  }

  .max-w-\[180px\] {
    max-width: 180px;
  }

  .max-w-\[200px\] {
    max-width: 200px;
  }

  .max-w-\[250px\] {
    max-width: 250px;
  }

  .max-w-\[270px\] {
    max-width: 270px;
  }

  .max-w-\[300px\] {
    max-width: 300px;
  }

  .max-w-\[400px\] {
    max-width: 400px;
  }

  .max-w-\[640px\] {
    max-width: 640px;
  }

  .max-w-\[768px\] {
    max-width: 768px;
  }

  .max-w-\[1024px\] {
    max-width: 1024px;
  }

  .max-w-\[1280px\] {
    max-width: 1280px;
  }

  .max-w-\[1536px\] {
    max-width: 1536px;
  }

  .max-w-\[calc\(100\%-2rem\)\] {
    max-width: calc(100% - 2rem);
  }

  .max-w-\[none\] {
    max-width: none;
  }

  .max-w-fit {
    max-width: fit-content;
  }

  .max-w-full {
    max-width: 100%;
  }

  .max-w-lg {
    max-width: var(--container-lg);
  }

  .max-w-max {
    max-width: max-content;
  }

  .max-w-md {
    max-width: var(--container-md);
  }

  .max-w-none {
    max-width: none;
  }

  .max-w-sm {
    max-width: var(--container-sm);
  }

  .max-w-xl {
    max-width: var(--container-xl);
  }

  .max-w-xs {
    max-width: var(--container-xs);
  }

  .min-w-0 {
    min-width: calc(var(--spacing) * 0);
  }

  .min-w-3 {
    min-width: calc(var(--spacing) * 3);
  }

  .min-w-3\.5 {
    min-width: calc(var(--spacing) * 3.5);
  }

  .min-w-4 {
    min-width: calc(var(--spacing) * 4);
  }

  .min-w-5 {
    min-width: calc(var(--spacing) * 5);
  }

  .min-w-6 {
    min-width: calc(var(--spacing) * 6);
  }

  .min-w-7 {
    min-width: calc(var(--spacing) * 7);
  }

  .min-w-8 {
    min-width: calc(var(--spacing) * 8);
  }

  .min-w-9 {
    min-width: calc(var(--spacing) * 9);
  }

  .min-w-10 {
    min-width: calc(var(--spacing) * 10);
  }

  .min-w-12 {
    min-width: calc(var(--spacing) * 12);
  }

  .min-w-16 {
    min-width: calc(var(--spacing) * 16);
  }

  .min-w-20 {
    min-width: calc(var(--spacing) * 20);
  }

  .min-w-24 {
    min-width: calc(var(--spacing) * 24);
  }

  .min-w-40 {
    min-width: calc(var(--spacing) * 40);
  }

  .min-w-56 {
    min-width: calc(var(--spacing) * 56);
  }

  .min-w-60 {
    min-width: calc(var(--spacing) * 60);
  }

  .min-w-\[8rem\] {
    min-width: 8rem;
  }

  .min-w-\[12rem\] {
    min-width: 12rem;
  }

  .min-w-\[40px\] {
    min-width: 40px;
  }

  .min-w-\[100px\] {
    min-width: 100px;
  }

  .min-w-\[120px\] {
    min-width: 120px;
  }

  .min-w-\[180px\] {
    min-width: 180px;
  }

  .min-w-\[200px\] {
    min-width: 200px;
  }

  .min-w-\[var\(--radix-select-trigger-width\)\] {
    min-width: var(--radix-select-trigger-width);
  }

  .min-w-full {
    min-width: 100%;
  }

  .min-w-max {
    min-width: max-content;
  }

  .min-w-min {
    min-width: min-content;
  }

  .flex-1 {
    flex: 1;
  }

  .flex-\[3\] {
    flex: 3;
  }

  .flex-auto {
    flex: auto;
  }

  .flex-initial {
    flex: 0 auto;
  }

  .flex-none {
    flex: none;
  }

  .flex-shrink-0, .shrink-0 {
    flex-shrink: 0;
  }

  .flex-grow, .grow {
    flex-grow: 1;
  }

  .grow-0 {
    flex-grow: 0;
  }

  .basis-0 {
    flex-basis: calc(var(--spacing) * 0);
  }

  .basis-full {
    flex-basis: 100%;
  }

  .table-auto {
    table-layout: auto;
  }

  .table-fixed {
    table-layout: fixed;
  }

  .caption-bottom {
    caption-side: bottom;
  }

  .border-collapse {
    border-collapse: collapse;
  }

  .origin-\(--radix-context-menu-content-transform-origin\) {
    transform-origin: var(--radix-context-menu-content-transform-origin);
  }

  .origin-\(--radix-dropdown-menu-content-transform-origin\) {
    transform-origin: var(--radix-dropdown-menu-content-transform-origin);
  }

  .origin-\(--radix-hover-card-content-transform-origin\) {
    transform-origin: var(--radix-hover-card-content-transform-origin);
  }

  .origin-\(--radix-menubar-content-transform-origin\) {
    transform-origin: var(--radix-menubar-content-transform-origin);
  }

  .origin-\(--radix-popover-content-transform-origin\) {
    transform-origin: var(--radix-popover-content-transform-origin);
  }

  .origin-\(--radix-select-content-transform-origin\) {
    transform-origin: var(--radix-select-content-transform-origin);
  }

  .origin-\(--radix-tooltip-content-transform-origin\) {
    transform-origin: var(--radix-tooltip-content-transform-origin);
  }

  .origin-center {
    transform-origin: center;
  }

  .origin-left {
    transform-origin: 0;
  }

  .origin-right {
    transform-origin: 100%;
  }

  .origin-top {
    transform-origin: top;
  }

  .origin-top-left {
    transform-origin: 0 0;
  }

  .-translate-x-1\/2 {
    --tw-translate-x: calc(calc(1 / 2 * 100%) * -1);
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }

  .-translate-x-px {
    --tw-translate-x: -1px;
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }

  .translate-x-1 {
    --tw-translate-x: calc(var(--spacing) * 1);
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }

  .translate-x-1\/2 {
    --tw-translate-x: calc(1 / 2 * 100%);
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }

  .translate-x-\[-50\%\] {
    --tw-translate-x: -50%;
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }

  .translate-x-px {
    --tw-translate-x: 1px;
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }

  .-translate-y-1 {
    --tw-translate-y: calc(var(--spacing) * -1);
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }

  .-translate-y-1\/2 {
    --tw-translate-y: calc(calc(1 / 2 * 100%) * -1);
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }

  .translate-y-0 {
    --tw-translate-y: calc(var(--spacing) * 0);
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }

  .translate-y-0\.5 {
    --tw-translate-y: calc(var(--spacing) * .5);
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }

  .translate-y-1 {
    --tw-translate-y: calc(var(--spacing) * 1);
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }

  .translate-y-1\/2 {
    --tw-translate-y: calc(1 / 2 * 100%);
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }

  .translate-y-2\/4 {
    --tw-translate-y: calc(2 / 4 * 100%);
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }

  .translate-y-3\/4 {
    --tw-translate-y: calc(3 / 4 * 100%);
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }

  .translate-y-16 {
    --tw-translate-y: calc(var(--spacing) * 16);
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }

  .translate-y-\[-50\%\] {
    --tw-translate-y: -50%;
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }

  .translate-y-\[2px\] {
    --tw-translate-y: 2px;
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }

  .translate-y-\[calc\(-50\%_-_2px\)\] {
    --tw-translate-y: calc(-50% - 2px);
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }

  .scale-0 {
    --tw-scale-x: 0%;
    --tw-scale-y: 0%;
    --tw-scale-z: 0%;
    scale: var(--tw-scale-x) var(--tw-scale-y);
  }

  .scale-50 {
    --tw-scale-x: 50%;
    --tw-scale-y: 50%;
    --tw-scale-z: 50%;
    scale: var(--tw-scale-x) var(--tw-scale-y);
  }

  .scale-90 {
    --tw-scale-x: 90%;
    --tw-scale-y: 90%;
    --tw-scale-z: 90%;
    scale: var(--tw-scale-x) var(--tw-scale-y);
  }

  .scale-100 {
    --tw-scale-x: 100%;
    --tw-scale-y: 100%;
    --tw-scale-z: 100%;
    scale: var(--tw-scale-x) var(--tw-scale-y);
  }

  .scale-105 {
    --tw-scale-x: 105%;
    --tw-scale-y: 105%;
    --tw-scale-z: 105%;
    scale: var(--tw-scale-x) var(--tw-scale-y);
  }

  .rotate-0 {
    rotate: none;
  }

  .rotate-45 {
    rotate: 45deg;
  }

  .rotate-90 {
    rotate: 90deg;
  }

  .rotate-180 {
    rotate: 180deg;
  }

  .spinner-bar-animation {
    animation-delay: calc(-1.2s + (.1s * var(--bar-index)));
    transform: rotate(calc(30deg * var(--bar-index))) translate(140%);
  }

  .transform {
    transform: var(--tw-rotate-x) var(--tw-rotate-y) var(--tw-rotate-z) var(--tw-skew-x) var(--tw-skew-y);
  }

  .transform-gpu {
    transform: translateZ(0) var(--tw-rotate-x) var(--tw-rotate-y) var(--tw-rotate-z) var(--tw-skew-x) var(--tw-skew-y);
  }

  .animate-\[appearance-in_1s_infinite\] {
    animation: 1s infinite appearance-in;
  }

  .animate-blink {
    animation: 1.4s infinite both blink;
  }

  .animate-caret-blink {
    animation: 1.25s ease-out infinite caret-blink;
  }

  .animate-drip-expand {
    animation: .42s linear drip-expand;
  }

  .animate-fade-out {
    animation: 1.2s linear infinite fade-out;
  }

  .animate-in {
    animation: enter var(--tw-duration, .15s) var(--tw-ease, ease);
  }

  .animate-indeterminate-bar {
    animation: 1.5s cubic-bezier(.65, .815, .735, .395) infinite indeterminate-bar;
  }

  .animate-none {
    animation: none;
  }

  .animate-pulse {
    animation: var(--animate-pulse);
  }

  .animate-spin {
    animation: var(--animate-spin);
  }

  .animate-spinner-ease-spin {
    animation: .8s infinite spinner-spin;
  }

  .animate-spinner-linear-spin {
    animation: .8s linear infinite spinner-spin;
  }

  .animate-sway {
    animation: .75s infinite sway;
  }

  .cursor-default {
    cursor: default;
  }

  .cursor-grab {
    cursor: grab;
  }

  .cursor-help {
    cursor: help;
  }

  .cursor-not-allowed {
    cursor: not-allowed;
  }

  .cursor-pointer {
    cursor: pointer;
  }

  .cursor-text {
    cursor: text;
  }

  .touch-none {
    touch-action: none;
  }

  .resize {
    resize: both;
  }

  .resize-none {
    resize: none;
  }

  .resize-y {
    resize: vertical;
  }

  .snap-y {
    scroll-snap-type: y var(--tw-scroll-snap-strictness);
  }

  .snap-mandatory {
    --tw-scroll-snap-strictness: mandatory;
  }

  .snap-center {
    scroll-snap-align: center;
  }

  .scroll-my-1 {
    scroll-margin-block: calc(var(--spacing) * 1);
  }

  .scroll-ml-6 {
    scroll-margin-left: calc(var(--spacing) * 6);
  }

  .scroll-py-1 {
    scroll-padding-block: calc(var(--spacing) * 1);
  }

  .scroll-py-6 {
    scroll-padding-block: calc(var(--spacing) * 6);
  }

  .list-disc {
    list-style-type: disc;
  }

  .list-none {
    list-style-type: none;
  }

  .appearance-none {
    appearance: none;
  }

  .auto-rows-fr {
    grid-auto-rows: minmax(0, 1fr);
  }

  .auto-rows-min {
    grid-auto-rows: min-content;
  }

  .grid-cols-1 {
    grid-template-columns: repeat(1, minmax(0, 1fr));
  }

  .grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }

  .grid-cols-3 {
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }

  .grid-cols-4 {
    grid-template-columns: repeat(4, minmax(0, 1fr));
  }

  .grid-cols-5 {
    grid-template-columns: repeat(5, minmax(0, 1fr));
  }

  .grid-cols-6 {
    grid-template-columns: repeat(6, minmax(0, 1fr));
  }

  .grid-cols-\[0_1fr\] {
    grid-template-columns: 0 1fr;
  }

  .grid-rows-\[auto_auto\] {
    grid-template-rows: auto auto;
  }

  .flex-col {
    flex-direction: column;
  }

  .flex-col-reverse {
    flex-direction: column-reverse;
  }

  .flex-row {
    flex-direction: row;
  }

  .flex-row-reverse {
    flex-direction: row-reverse;
  }

  .flex-nowrap {
    flex-wrap: nowrap;
  }

  .flex-wrap {
    flex-wrap: wrap;
  }

  .place-content-center {
    place-content: center;
  }

  .place-items-center {
    place-items: center;
  }

  .items-center {
    align-items: center;
  }

  .items-end {
    align-items: flex-end;
  }

  .items-start {
    align-items: flex-start;
  }

  .items-stretch {
    align-items: stretch;
  }

  .justify-between {
    justify-content: space-between;
  }

  .justify-center {
    justify-content: center;
  }

  .justify-end {
    justify-content: flex-end;
  }

  .justify-start {
    justify-content: flex-start;
  }

  .justify-items-start {
    justify-items: start;
  }

  .\!gap-0 {
    gap: calc(var(--spacing) * 0) !important;
  }

  .gap-0 {
    gap: calc(var(--spacing) * 0);
  }

  .gap-0\.5 {
    gap: calc(var(--spacing) * .5);
  }

  .gap-1 {
    gap: calc(var(--spacing) * 1);
  }

  .gap-1\.5 {
    gap: calc(var(--spacing) * 1.5);
  }

  .gap-2 {
    gap: calc(var(--spacing) * 2);
  }

  .gap-3 {
    gap: calc(var(--spacing) * 3);
  }

  .gap-4 {
    gap: calc(var(--spacing) * 4);
  }

  .gap-6 {
    gap: calc(var(--spacing) * 6);
  }

  .gap-8 {
    gap: calc(var(--spacing) * 8);
  }

  :where(.space-y-0 > :not(:last-child)) {
    --tw-space-y-reverse: 0;
    margin-block-start: calc(calc(var(--spacing) * 0) * var(--tw-space-y-reverse));
    margin-block-end: calc(calc(var(--spacing) * 0) * calc(1 - var(--tw-space-y-reverse)));
  }

  :where(.space-y-0\.5 > :not(:last-child)) {
    --tw-space-y-reverse: 0;
    margin-block-start: calc(calc(var(--spacing) * .5) * var(--tw-space-y-reverse));
    margin-block-end: calc(calc(var(--spacing) * .5) * calc(1 - var(--tw-space-y-reverse)));
  }

  :where(.space-y-1 > :not(:last-child)) {
    --tw-space-y-reverse: 0;
    margin-block-start: calc(calc(var(--spacing) * 1) * var(--tw-space-y-reverse));
    margin-block-end: calc(calc(var(--spacing) * 1) * calc(1 - var(--tw-space-y-reverse)));
  }

  :where(.space-y-1\.5 > :not(:last-child)) {
    --tw-space-y-reverse: 0;
    margin-block-start: calc(calc(var(--spacing) * 1.5) * var(--tw-space-y-reverse));
    margin-block-end: calc(calc(var(--spacing) * 1.5) * calc(1 - var(--tw-space-y-reverse)));
  }

  :where(.space-y-2 > :not(:last-child)) {
    --tw-space-y-reverse: 0;
    margin-block-start: calc(calc(var(--spacing) * 2) * var(--tw-space-y-reverse));
    margin-block-end: calc(calc(var(--spacing) * 2) * calc(1 - var(--tw-space-y-reverse)));
  }

  :where(.space-y-3 > :not(:last-child)) {
    --tw-space-y-reverse: 0;
    margin-block-start: calc(calc(var(--spacing) * 3) * var(--tw-space-y-reverse));
    margin-block-end: calc(calc(var(--spacing) * 3) * calc(1 - var(--tw-space-y-reverse)));
  }

  :where(.space-y-4 > :not(:last-child)) {
    --tw-space-y-reverse: 0;
    margin-block-start: calc(calc(var(--spacing) * 4) * var(--tw-space-y-reverse));
    margin-block-end: calc(calc(var(--spacing) * 4) * calc(1 - var(--tw-space-y-reverse)));
  }

  :where(.space-y-6 > :not(:last-child)) {
    --tw-space-y-reverse: 0;
    margin-block-start: calc(calc(var(--spacing) * 6) * var(--tw-space-y-reverse));
    margin-block-end: calc(calc(var(--spacing) * 6) * calc(1 - var(--tw-space-y-reverse)));
  }

  .gap-x-0 {
    column-gap: calc(var(--spacing) * 0);
  }

  .gap-x-0\.5 {
    column-gap: calc(var(--spacing) * .5);
  }

  .gap-x-1 {
    column-gap: calc(var(--spacing) * 1);
  }

  .gap-x-2 {
    column-gap: calc(var(--spacing) * 2);
  }

  .gap-x-4 {
    column-gap: calc(var(--spacing) * 4);
  }

  .gap-x-6 {
    column-gap: calc(var(--spacing) * 6);
  }

  :where(.space-x-0\.5 > :not(:last-child)) {
    --tw-space-x-reverse: 0;
    margin-inline-start: calc(calc(var(--spacing) * .5) * var(--tw-space-x-reverse));
    margin-inline-end: calc(calc(var(--spacing) * .5) * calc(1 - var(--tw-space-x-reverse)));
  }

  :where(.space-x-1 > :not(:last-child)) {
    --tw-space-x-reverse: 0;
    margin-inline-start: calc(calc(var(--spacing) * 1) * var(--tw-space-x-reverse));
    margin-inline-end: calc(calc(var(--spacing) * 1) * calc(1 - var(--tw-space-x-reverse)));
  }

  :where(.space-x-2 > :not(:last-child)) {
    --tw-space-x-reverse: 0;
    margin-inline-start: calc(calc(var(--spacing) * 2) * var(--tw-space-x-reverse));
    margin-inline-end: calc(calc(var(--spacing) * 2) * calc(1 - var(--tw-space-x-reverse)));
  }

  :where(.space-x-3 > :not(:last-child)) {
    --tw-space-x-reverse: 0;
    margin-inline-start: calc(calc(var(--spacing) * 3) * var(--tw-space-x-reverse));
    margin-inline-end: calc(calc(var(--spacing) * 3) * calc(1 - var(--tw-space-x-reverse)));
  }

  :where(.space-x-4 > :not(:last-child)) {
    --tw-space-x-reverse: 0;
    margin-inline-start: calc(calc(var(--spacing) * 4) * var(--tw-space-x-reverse));
    margin-inline-end: calc(calc(var(--spacing) * 4) * calc(1 - var(--tw-space-x-reverse)));
  }

  :where(.space-x-6 > :not(:last-child)) {
    --tw-space-x-reverse: 0;
    margin-inline-start: calc(calc(var(--spacing) * 6) * var(--tw-space-x-reverse));
    margin-inline-end: calc(calc(var(--spacing) * 6) * calc(1 - var(--tw-space-x-reverse)));
  }

  .gap-y-0 {
    row-gap: calc(var(--spacing) * 0);
  }

  .gap-y-0\.5 {
    row-gap: calc(var(--spacing) * .5);
  }

  .gap-y-1\.5 {
    row-gap: calc(var(--spacing) * 1.5);
  }

  .gap-y-2 {
    row-gap: calc(var(--spacing) * 2);
  }

  :where(.divide-y > :not(:last-child)) {
    --tw-divide-y-reverse: 0;
    border-bottom-style: var(--tw-border-style);
    border-top-style: var(--tw-border-style);
    border-top-width: calc(1px * var(--tw-divide-y-reverse));
    border-bottom-width: calc(1px * calc(1 - var(--tw-divide-y-reverse)));
  }

  .self-center {
    align-self: center;
  }

  .self-start {
    align-self: flex-start;
  }

  .justify-self-end {
    justify-self: flex-end;
  }

  .truncate {
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
  }

  .overflow-auto {
    overflow: auto;
  }

  .overflow-clip {
    overflow: clip;
  }

  .overflow-hidden {
    overflow: hidden;
  }

  .overflow-visible {
    overflow: visible;
  }

  .overflow-x-auto {
    overflow-x: auto;
  }

  .overflow-x-hidden {
    overflow-x: hidden;
  }

  .overflow-x-scroll {
    overflow-x: scroll;
  }

  .overflow-y-auto {
    overflow-y: auto;
  }

  .overflow-y-hidden {
    overflow-y: hidden;
  }

  .overflow-y-scroll {
    overflow-y: scroll;
  }

  .\!rounded-none {
    border-radius: 0 !important;
  }

  .rounded {
    border-radius: .25rem;
  }

  .rounded-\[2px\] {
    border-radius: 2px;
  }

  .rounded-\[4px\] {
    border-radius: 4px;
  }

  .rounded-\[calc\(theme\(borderRadius\.large\)\/1\.5\)\] {
    border-radius: calc(var(--heroui-radius-large) / 1.5);
  }

  .rounded-\[calc\(theme\(borderRadius\.medium\)\*0\.5\)\] {
    border-radius: calc(var(--heroui-radius-medium) * .5);
  }

  .rounded-\[calc\(theme\(borderRadius\.medium\)\*0\.6\)\] {
    border-radius: calc(var(--heroui-radius-medium) * .6);
  }

  .rounded-\[calc\(theme\(borderRadius\.medium\)\*0\.7\)\] {
    border-radius: calc(var(--heroui-radius-medium) * .7);
  }

  .rounded-\[calc\(theme\(borderRadius\.medium\)\/2\)\] {
    border-radius: calc(var(--heroui-radius-medium) / 2);
  }

  .rounded-\[calc\(theme\(borderRadius\.small\)\/2\)\] {
    border-radius: calc(var(--heroui-radius-small) / 2);
  }

  .rounded-\[inherit\] {
    border-radius: inherit;
  }

  .rounded-full {
    border-radius: 3.40282e38px;
  }

  .rounded-large {
    border-radius: var(--heroui-radius-large);
  }

  .rounded-lg {
    border-radius: var(--radius);
  }

  .rounded-md {
    border-radius: calc(var(--radius)  - 2px);
  }

  .rounded-medium {
    border-radius: var(--heroui-radius-medium);
  }

  .rounded-none {
    border-radius: 0;
  }

  .rounded-sm {
    border-radius: calc(var(--radius)  - 4px);
  }

  .rounded-small {
    border-radius: var(--heroui-radius-small);
  }

  .rounded-xl {
    border-radius: calc(var(--radius)  + 4px);
  }

  .rounded-xs {
    border-radius: var(--radius-xs);
  }

  .\!rounded-s-none {
    border-start-start-radius: 0 !important;
    border-end-start-radius: 0 !important;
  }

  .\!rounded-e-none {
    border-start-end-radius: 0 !important;
    border-end-end-radius: 0 !important;
  }

  .rounded-t-large {
    border-top-left-radius: var(--heroui-radius-large);
    border-top-right-radius: var(--heroui-radius-large);
  }

  .rounded-t-medium {
    border-top-left-radius: var(--heroui-radius-medium);
    border-top-right-radius: var(--heroui-radius-medium);
  }

  .rounded-t-none {
    border-top-left-radius: 0;
    border-top-right-radius: 0;
  }

  .rounded-t-small {
    border-top-left-radius: var(--heroui-radius-small);
    border-top-right-radius: var(--heroui-radius-small);
  }

  .rounded-l-none {
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
  }

  .rounded-tl-sm {
    border-top-left-radius: calc(var(--radius)  - 4px);
  }

  .rounded-r-none {
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
  }

  .rounded-b-large {
    border-bottom-right-radius: var(--heroui-radius-large);
    border-bottom-left-radius: var(--heroui-radius-large);
  }

  .rounded-b-medium {
    border-bottom-right-radius: var(--heroui-radius-medium);
    border-bottom-left-radius: var(--heroui-radius-medium);
  }

  .rounded-b-none {
    border-bottom-right-radius: 0;
    border-bottom-left-radius: 0;
  }

  .rounded-b-small {
    border-bottom-right-radius: var(--heroui-radius-small);
    border-bottom-left-radius: var(--heroui-radius-small);
  }

  .border {
    border-style: var(--tw-border-style);
    border-width: 1px;
  }

  .border-0 {
    border-style: var(--tw-border-style);
    border-width: 0;
  }

  .border-1 {
    border-style: var(--tw-border-style);
    border-width: 1px;
  }

  .border-2 {
    border-style: var(--tw-border-style);
    border-width: 2px;
  }

  .border-3 {
    border-style: var(--tw-border-style);
    border-width: 3px;
  }

  .border-\[1\.5px\] {
    border-style: var(--tw-border-style);
    border-width: 1.5px;
  }

  .border-medium {
    border-style: var(--tw-border-style);
    border-width: var(--heroui-border-width-medium);
  }

  .border-small {
    border-style: var(--tw-border-style);
    border-width: var(--heroui-border-width-small);
  }

  .border-x-\[calc\(theme\(spacing\.5\)\/2\)\] {
    border-inline-style: var(--tw-border-style);
    border-inline-width: .625rem;
  }

  .border-x-\[calc\(theme\(spacing\.6\)\/2\)\] {
    border-inline-style: var(--tw-border-style);
    border-inline-width: .75rem;
  }

  .border-x-\[calc\(theme\(spacing\.7\)\/2\)\] {
    border-inline-style: var(--tw-border-style);
    border-inline-width: .875rem;
  }

  .border-y {
    border-block-style: var(--tw-border-style);
    border-block-width: 1px;
  }

  .border-y-\[calc\(theme\(spacing\.5\)\/2\)\] {
    border-block-style: var(--tw-border-style);
    border-block-width: .625rem;
  }

  .border-y-\[calc\(theme\(spacing\.6\)\/2\)\] {
    border-block-style: var(--tw-border-style);
    border-block-width: .75rem;
  }

  .border-y-\[calc\(theme\(spacing\.7\)\/2\)\] {
    border-block-style: var(--tw-border-style);
    border-block-width: .875rem;
  }

  .border-t {
    border-top-style: var(--tw-border-style);
    border-top-width: 1px;
  }

  .border-t-2 {
    border-top-style: var(--tw-border-style);
    border-top-width: 2px;
  }

  .border-r {
    border-right-style: var(--tw-border-style);
    border-right-width: 1px;
  }

  .border-b {
    border-bottom-style: var(--tw-border-style);
    border-bottom-width: 1px;
  }

  .border-b-2 {
    border-bottom-style: var(--tw-border-style);
    border-bottom-width: 2px;
  }

  .border-b-medium {
    border-bottom-style: var(--tw-border-style);
    border-bottom-width: var(--heroui-border-width-medium);
  }

  .border-l {
    border-left-style: var(--tw-border-style);
    border-left-width: 1px;
  }

  .border-l-4 {
    border-left-style: var(--tw-border-style);
    border-left-width: 4px;
  }

  .\!border-none {
    --tw-border-style: none !important;
    border-style: none !important;
  }

  .border-dashed {
    --tw-border-style: dashed;
    border-style: dashed;
  }

  .border-dotted {
    --tw-border-style: dotted;
    border-style: dotted;
  }

  .border-none {
    --tw-border-style: none;
    border-style: none;
  }

  .border-solid {
    --tw-border-style: solid;
    border-style: solid;
  }

  .\!border-danger {
    border-color: hsl(var(--heroui-danger) / 1) !important;
  }

  .border-\(--color-border\) {
    border-color: var(--color-border);
  }

  .border-accent {
    border-color: var(--accent);
  }

  .border-background {
    border-color: var(--background);
  }

  .border-blue-100 {
    border-color: var(--color-blue-100);
  }

  .border-blue-200 {
    border-color: var(--color-blue-200);
  }

  .border-border {
    border-color: var(--border);
  }

  .border-border\/50 {
    border-color: var(--border);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .border-border\/50 {
      border-color: color-mix(in oklab, var(--border) 50%, transparent);
    }
  }

  .border-danger {
    border-color: hsl(var(--heroui-danger) / 1);
  }

  .border-danger-100 {
    border-color: hsl(var(--heroui-danger-100) / 1);
  }

  .border-danger-200 {
    border-color: hsl(var(--heroui-danger-200) / 1);
  }

  .border-danger-400 {
    border-color: hsl(var(--heroui-danger-400) / 1);
  }

  .border-default {
    border-color: hsl(var(--heroui-default) / 1);
  }

  .border-default-100 {
    border-color: hsl(var(--heroui-default-100) / 1);
  }

  .border-default-200 {
    border-color: hsl(var(--heroui-default-200) / 1);
  }

  .border-default-300 {
    border-color: hsl(var(--heroui-default-300) / 1);
  }

  .border-default-400 {
    border-color: hsl(var(--heroui-default-400) / 1);
  }

  .border-destructive\/30 {
    border-color: var(--destructive);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .border-destructive\/30 {
      border-color: color-mix(in oklab, var(--destructive) 30%, transparent);
    }
  }

  .border-divider {
    border-color: hsl(var(--heroui-divider) / .15);
  }

  .border-foreground {
    border-color: var(--foreground);
  }

  .border-foreground-400 {
    border-color: hsl(var(--heroui-foreground-400) / 1);
  }

  .border-gray-100 {
    border-color: var(--color-gray-100);
  }

  .border-gray-200 {
    border-color: var(--color-gray-200);
  }

  .border-green-100 {
    border-color: var(--color-green-100);
  }

  .border-green-200 {
    border-color: var(--color-green-200);
  }

  .border-input {
    border-color: var(--input);
  }

  .border-muted-foreground\/5 {
    border-color: var(--muted-foreground);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .border-muted-foreground\/5 {
      border-color: color-mix(in oklab, var(--muted-foreground) 5%, transparent);
    }
  }

  .border-primary {
    border-color: var(--primary);
  }

  .border-primary-100 {
    border-color: hsl(var(--heroui-primary-100) / 1);
  }

  .border-primary-200 {
    border-color: hsl(var(--heroui-primary-200) / 1);
  }

  .border-primary-400 {
    border-color: hsl(var(--heroui-primary-400) / 1);
  }

  .border-primary\/20 {
    border-color: var(--primary);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .border-primary\/20 {
      border-color: color-mix(in oklab, var(--primary) 20%, transparent);
    }
  }

  .border-red-100 {
    border-color: var(--color-red-100);
  }

  .border-red-200 {
    border-color: var(--color-red-200);
  }

  .border-red-500 {
    border-color: var(--color-red-500);
  }

  .border-secondary {
    border-color: var(--secondary);
  }

  .border-secondary-100 {
    border-color: hsl(var(--heroui-secondary-100) / 1);
  }

  .border-secondary-200 {
    border-color: hsl(var(--heroui-secondary-200) / 1);
  }

  .border-secondary-400 {
    border-color: hsl(var(--heroui-secondary-400) / 1);
  }

  .border-sidebar-border {
    border-color: var(--sidebar-border);
  }

  .border-success {
    border-color: hsl(var(--heroui-success) / 1);
  }

  .border-success-100 {
    border-color: hsl(var(--heroui-success-100) / 1);
  }

  .border-success-200 {
    border-color: hsl(var(--heroui-success-200) / 1);
  }

  .border-success-300 {
    border-color: hsl(var(--heroui-success-300) / 1);
  }

  .border-success-400 {
    border-color: hsl(var(--heroui-success-400) / 1);
  }

  .border-transparent {
    border-color: #0000;
  }

  .border-warning {
    border-color: hsl(var(--heroui-warning) / 1);
  }

  .border-warning-100 {
    border-color: hsl(var(--heroui-warning-100) / 1);
  }

  .border-warning-200 {
    border-color: hsl(var(--heroui-warning-200) / 1);
  }

  .border-warning-300 {
    border-color: hsl(var(--heroui-warning-300) / 1);
  }

  .border-warning-400 {
    border-color: hsl(var(--heroui-warning-400) / 1);
  }

  .border-yellow-200 {
    border-color: var(--color-yellow-200);
  }

  .border-x-transparent {
    border-inline-color: #0000;
  }

  .border-y-transparent {
    border-block-color: #0000;
  }

  .border-t-transparent {
    border-top-color: #0000;
  }

  .border-r-transparent {
    border-right-color: #0000;
  }

  .border-b-current {
    border-bottom-color: currentColor;
  }

  .border-b-danger {
    border-bottom-color: hsl(var(--heroui-danger) / 1);
  }

  .border-b-default {
    border-bottom-color: hsl(var(--heroui-default) / 1);
  }

  .border-b-primary {
    border-bottom-color: var(--primary);
  }

  .border-b-secondary {
    border-bottom-color: var(--secondary);
  }

  .border-b-success {
    border-bottom-color: hsl(var(--heroui-success) / 1);
  }

  .border-b-warning {
    border-bottom-color: hsl(var(--heroui-warning) / 1);
  }

  .border-b-white {
    border-bottom-color: var(--color-white);
  }

  .border-l-primary {
    border-left-color: var(--primary);
  }

  .border-l-transparent {
    border-left-color: #0000;
  }

  .\!bg-danger-50 {
    background-color: hsl(var(--heroui-danger-50) / 1) !important;
  }

  .\!bg-transparent {
    background-color: #0000 !important;
  }

  .bg-\(--color-bg\) {
    background-color: var(--color-bg);
  }

  .bg-accent {
    background-color: var(--accent);
  }

  .bg-amber-100 {
    background-color: var(--color-amber-100);
  }

  .bg-amber-500 {
    background-color: var(--color-amber-500);
  }

  .bg-background {
    background-color: var(--background);
  }

  .bg-background\/10 {
    background-color: var(--background);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .bg-background\/10 {
      background-color: color-mix(in oklab, var(--background) 10%, transparent);
    }
  }

  .bg-background\/70 {
    background-color: var(--background);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .bg-background\/70 {
      background-color: color-mix(in oklab, var(--background) 70%, transparent);
    }
  }

  .bg-background\/80 {
    background-color: var(--background);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .bg-background\/80 {
      background-color: color-mix(in oklab, var(--background) 80%, transparent);
    }
  }

  .bg-background\/95 {
    background-color: var(--background);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .bg-background\/95 {
      background-color: color-mix(in oklab, var(--background) 95%, transparent);
    }
  }

  .bg-black\/5 {
    background-color: #0000000d;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .bg-black\/5 {
      background-color: color-mix(in oklab, var(--color-black) 5%, transparent);
    }
  }

  .bg-black\/50 {
    background-color: #00000080;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .bg-black\/50 {
      background-color: color-mix(in oklab, var(--color-black) 50%, transparent);
    }
  }

  .bg-blue-50 {
    background-color: var(--color-blue-50);
  }

  .bg-blue-100 {
    background-color: var(--color-blue-100);
  }

  .bg-blue-500 {
    background-color: var(--color-blue-500);
  }

  .bg-blue-500\/10 {
    background-color: #3080ff1a;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .bg-blue-500\/10 {
      background-color: color-mix(in oklab, var(--color-blue-500) 10%, transparent);
    }
  }

  .bg-blue-600 {
    background-color: var(--color-blue-600);
  }

  .bg-border {
    background-color: var(--border);
  }

  .bg-border\/60 {
    background-color: var(--border);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .bg-border\/60 {
      background-color: color-mix(in oklab, var(--border) 60%, transparent);
    }
  }

  .bg-card {
    background-color: var(--card);
  }

  .bg-content1 {
    background-color: hsl(var(--heroui-content1) / 1);
  }

  .bg-content3 {
    background-color: hsl(var(--heroui-content3) / 1);
  }

  .bg-current {
    background-color: currentColor;
  }

  .bg-cyan-100 {
    background-color: var(--color-cyan-100);
  }

  .bg-danger {
    background-color: hsl(var(--heroui-danger) / 1);
  }

  .bg-danger-50 {
    background-color: hsl(var(--heroui-danger-50) / 1);
  }

  .bg-danger-100 {
    background-color: hsl(var(--heroui-danger-100) / 1);
  }

  .bg-danger-400 {
    background-color: hsl(var(--heroui-danger-400) / 1);
  }

  .bg-danger\/20 {
    background-color: hsl(var(--heroui-danger) / 1);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .bg-danger\/20 {
      background-color: color-mix(in oklab, hsl(var(--heroui-danger) / 1) 20%, transparent);
    }
  }

  .bg-default {
    background-color: hsl(var(--heroui-default) / 1);
  }

  .bg-default-50 {
    background-color: hsl(var(--heroui-default-50) / 1);
  }

  .bg-default-100 {
    background-color: hsl(var(--heroui-default-100) / 1);
  }

  .bg-default-200 {
    background-color: hsl(var(--heroui-default-200) / 1);
  }

  .bg-default-300\/50 {
    background-color: hsl(var(--heroui-default-300) / 1);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .bg-default-300\/50 {
      background-color: color-mix(in oklab, hsl(var(--heroui-default-300) / 1) 50%, transparent);
    }
  }

  .bg-default-400 {
    background-color: hsl(var(--heroui-default-400) / 1);
  }

  .bg-default-500 {
    background-color: hsl(var(--heroui-default-500) / 1);
  }

  .bg-default-800 {
    background-color: hsl(var(--heroui-default-800) / 1);
  }

  .bg-default\/40 {
    background-color: hsl(var(--heroui-default) / 1);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .bg-default\/40 {
      background-color: color-mix(in oklab, hsl(var(--heroui-default) / 1) 40%, transparent);
    }
  }

  .bg-destructive {
    background-color: var(--destructive);
  }

  .bg-divider {
    background-color: hsl(var(--heroui-divider) / .15);
  }

  .bg-foreground {
    background-color: var(--foreground);
  }

  .bg-foreground-100 {
    background-color: hsl(var(--heroui-foreground-100) / 1);
  }

  .bg-foreground-400 {
    background-color: hsl(var(--heroui-foreground-400) / 1);
  }

  .bg-foreground\/10 {
    background-color: var(--foreground);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .bg-foreground\/10 {
      background-color: color-mix(in oklab, var(--foreground) 10%, transparent);
    }
  }

  .bg-gray-50 {
    background-color: var(--color-gray-50);
  }

  .bg-gray-100 {
    background-color: var(--color-gray-100);
  }

  .bg-gray-200 {
    background-color: var(--color-gray-200);
  }

  .bg-gray-300 {
    background-color: var(--color-gray-300);
  }

  .bg-gray-400 {
    background-color: var(--color-gray-400);
  }

  .bg-gray-500 {
    background-color: var(--color-gray-500);
  }

  .bg-green-50 {
    background-color: var(--color-green-50);
  }

  .bg-green-100 {
    background-color: var(--color-green-100);
  }

  .bg-green-500 {
    background-color: var(--color-green-500);
  }

  .bg-green-500\/10 {
    background-color: #00c7581a;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .bg-green-500\/10 {
      background-color: color-mix(in oklab, var(--color-green-500) 10%, transparent);
    }
  }

  .bg-green-600 {
    background-color: var(--color-green-600);
  }

  .bg-indigo-50 {
    background-color: var(--color-indigo-50);
  }

  .bg-indigo-100 {
    background-color: var(--color-indigo-100);
  }

  .bg-muted {
    background-color: var(--muted);
  }

  .bg-muted\/20 {
    background-color: var(--muted);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .bg-muted\/20 {
      background-color: color-mix(in oklab, var(--muted) 20%, transparent);
    }
  }

  .bg-muted\/30 {
    background-color: var(--muted);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .bg-muted\/30 {
      background-color: color-mix(in oklab, var(--muted) 30%, transparent);
    }
  }

  .bg-muted\/50 {
    background-color: var(--muted);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .bg-muted\/50 {
      background-color: color-mix(in oklab, var(--muted) 50%, transparent);
    }
  }

  .bg-orange-100 {
    background-color: var(--color-orange-100);
  }

  .bg-orange-500 {
    background-color: var(--color-orange-500);
  }

  .bg-overlay\/30 {
    background-color: hsl(var(--heroui-overlay) / 1);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .bg-overlay\/30 {
      background-color: color-mix(in oklab, hsl(var(--heroui-overlay) / 1) 30%, transparent);
    }
  }

  .bg-overlay\/50 {
    background-color: hsl(var(--heroui-overlay) / 1);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .bg-overlay\/50 {
      background-color: color-mix(in oklab, hsl(var(--heroui-overlay) / 1) 50%, transparent);
    }
  }

  .bg-pink-100 {
    background-color: var(--color-pink-100);
  }

  .bg-popover {
    background-color: var(--popover);
  }

  .bg-primary {
    background-color: var(--primary);
  }

  .bg-primary-50 {
    background-color: hsl(var(--heroui-primary-50) / 1);
  }

  .bg-primary-100 {
    background-color: hsl(var(--heroui-primary-100) / 1);
  }

  .bg-primary-400 {
    background-color: hsl(var(--heroui-primary-400) / 1);
  }

  .bg-primary\/5 {
    background-color: var(--primary);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .bg-primary\/5 {
      background-color: color-mix(in oklab, var(--primary) 5%, transparent);
    }
  }

  .bg-primary\/10 {
    background-color: var(--primary);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .bg-primary\/10 {
      background-color: color-mix(in oklab, var(--primary) 10%, transparent);
    }
  }

  .bg-primary\/20 {
    background-color: var(--primary);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .bg-primary\/20 {
      background-color: color-mix(in oklab, var(--primary) 20%, transparent);
    }
  }

  .bg-purple-50 {
    background-color: var(--color-purple-50);
  }

  .bg-purple-100 {
    background-color: var(--color-purple-100);
  }

  .bg-red-50 {
    background-color: var(--color-red-50);
  }

  .bg-red-100 {
    background-color: var(--color-red-100);
  }

  .bg-red-500 {
    background-color: var(--color-red-500);
  }

  .bg-red-500\/10 {
    background-color: #fb2c361a;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .bg-red-500\/10 {
      background-color: color-mix(in oklab, var(--color-red-500) 10%, transparent);
    }
  }

  .bg-red-600 {
    background-color: var(--color-red-600);
  }

  .bg-rose-100 {
    background-color: var(--color-rose-100);
  }

  .bg-secondary {
    background-color: var(--secondary);
  }

  .bg-secondary-50 {
    background-color: hsl(var(--heroui-secondary-50) / 1);
  }

  .bg-secondary-100 {
    background-color: hsl(var(--heroui-secondary-100) / 1);
  }

  .bg-secondary-400 {
    background-color: hsl(var(--heroui-secondary-400) / 1);
  }

  .bg-secondary\/20 {
    background-color: var(--secondary);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .bg-secondary\/20 {
      background-color: color-mix(in oklab, var(--secondary) 20%, transparent);
    }
  }

  .bg-sidebar {
    background-color: var(--sidebar);
  }

  .bg-sidebar-border {
    background-color: var(--sidebar-border);
  }

  .bg-slate-50 {
    background-color: var(--color-slate-50);
  }

  .bg-slate-100 {
    background-color: var(--color-slate-100);
  }

  .bg-slate-900 {
    background-color: var(--color-slate-900);
  }

  .bg-success {
    background-color: hsl(var(--heroui-success) / 1);
  }

  .bg-success-50 {
    background-color: hsl(var(--heroui-success-50) / 1);
  }

  .bg-success-100 {
    background-color: hsl(var(--heroui-success-100) / 1);
  }

  .bg-success-400 {
    background-color: hsl(var(--heroui-success-400) / 1);
  }

  .bg-success\/20 {
    background-color: hsl(var(--heroui-success) / 1);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .bg-success\/20 {
      background-color: color-mix(in oklab, hsl(var(--heroui-success) / 1) 20%, transparent);
    }
  }

  .bg-teal-100 {
    background-color: var(--color-teal-100);
  }

  .bg-transparent {
    background-color: #0000;
  }

  .bg-warning {
    background-color: hsl(var(--heroui-warning) / 1);
  }

  .bg-warning-50 {
    background-color: hsl(var(--heroui-warning-50) / 1);
  }

  .bg-warning-100 {
    background-color: hsl(var(--heroui-warning-100) / 1);
  }

  .bg-warning-400 {
    background-color: hsl(var(--heroui-warning-400) / 1);
  }

  .bg-warning\/20 {
    background-color: hsl(var(--heroui-warning) / 1);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .bg-warning\/20 {
      background-color: color-mix(in oklab, hsl(var(--heroui-warning) / 1) 20%, transparent);
    }
  }

  .bg-white {
    background-color: var(--color-white);
  }

  .bg-white\/80 {
    background-color: #fffc;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .bg-white\/80 {
      background-color: color-mix(in oklab, var(--color-white) 80%, transparent);
    }
  }

  .bg-yellow-50 {
    background-color: var(--color-yellow-50);
  }

  .bg-yellow-100 {
    background-color: var(--color-yellow-100);
  }

  .bg-yellow-500 {
    background-color: var(--color-yellow-500);
  }

  .bg-yellow-500\/10 {
    background-color: #edb2001a;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .bg-yellow-500\/10 {
      background-color: color-mix(in oklab, var(--color-yellow-500) 10%, transparent);
    }
  }

  .bg-gradient-to-b {
    --tw-gradient-position: to bottom in oklab;
    background-image: linear-gradient(var(--tw-gradient-stops));
  }

  .bg-gradient-to-r {
    --tw-gradient-position: to right in oklab;
    background-image: linear-gradient(var(--tw-gradient-stops));
  }

  .bg-img-inherit {
    background-image: inherit;
  }

  .bg-stripe-gradient-danger {
    background-image: linear-gradient(45deg, hsl(var(--heroui-danger-200)) 25%, hsl(var(--heroui-danger)) 25%, hsl(var(--heroui-danger)) 50%, hsl(var(--heroui-danger-200)) 50%, hsl(var(--heroui-danger-200)) 75%, hsl(var(--heroui-danger)) 75%, hsl(var(--heroui-danger)));
  }

  .bg-stripe-gradient-default {
    background-image: linear-gradient(45deg, hsl(var(--heroui-default-200)) 25%, hsl(var(--heroui-default-400)) 25%, hsl(var(--heroui-default-400)) 50%, hsl(var(--heroui-default-200)) 50%, hsl(var(--heroui-default-200)) 75%, hsl(var(--heroui-default-400)) 75%, hsl(var(--heroui-default-400)));
  }

  .bg-stripe-gradient-primary {
    background-image: linear-gradient(45deg, hsl(var(--heroui-primary-200)) 25%, hsl(var(--heroui-primary)) 25%, hsl(var(--heroui-primary)) 50%, hsl(var(--heroui-primary-200)) 50%, hsl(var(--heroui-primary-200)) 75%, hsl(var(--heroui-primary)) 75%, hsl(var(--heroui-primary)));
  }

  .bg-stripe-gradient-secondary {
    background-image: linear-gradient(45deg, hsl(var(--heroui-secondary-200)) 25%, hsl(var(--heroui-secondary)) 25%, hsl(var(--heroui-secondary)) 50%, hsl(var(--heroui-secondary-200)) 50%, hsl(var(--heroui-secondary-200)) 75%, hsl(var(--heroui-secondary)) 75%, hsl(var(--heroui-secondary)));
  }

  .bg-stripe-gradient-success {
    background-image: linear-gradient(45deg, hsl(var(--heroui-success-200)) 25%, hsl(var(--heroui-success)) 25%, hsl(var(--heroui-success)) 50%, hsl(var(--heroui-success-200)) 50%, hsl(var(--heroui-success-200)) 75%, hsl(var(--heroui-success)) 75%, hsl(var(--heroui-success)));
  }

  .bg-stripe-gradient-warning {
    background-image: linear-gradient(45deg, hsl(var(--heroui-warning-200)) 25%, hsl(var(--heroui-warning)) 25%, hsl(var(--heroui-warning)) 50%, hsl(var(--heroui-warning-200)) 50%, hsl(var(--heroui-warning-200)) 75%, hsl(var(--heroui-warning)) 75%, hsl(var(--heroui-warning)));
  }

  .from-blue-50 {
    --tw-gradient-from: var(--color-blue-50);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .from-green-50 {
    --tw-gradient-from: var(--color-green-50);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .from-red-50 {
    --tw-gradient-from: var(--color-red-50);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .from-transparent {
    --tw-gradient-from: transparent;
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .via-transparent {
    --tw-gradient-via: transparent;
    --tw-gradient-via-stops: var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-via) var(--tw-gradient-via-position), var(--tw-gradient-to) var(--tw-gradient-to-position);
    --tw-gradient-stops: var(--tw-gradient-via-stops);
  }

  .to-current {
    --tw-gradient-to: currentcolor;
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .to-danger {
    --tw-gradient-to: hsl(var(--heroui-danger) / 1);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .to-default {
    --tw-gradient-to: hsl(var(--heroui-default) / 1);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .to-emerald-50 {
    --tw-gradient-to: var(--color-emerald-50);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .to-indigo-50 {
    --tw-gradient-to: var(--color-indigo-50);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .to-primary {
    --tw-gradient-to: var(--primary);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .to-rose-50 {
    --tw-gradient-to: var(--color-rose-50);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .to-secondary {
    --tw-gradient-to: var(--secondary);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .to-success {
    --tw-gradient-to: hsl(var(--heroui-success) / 1);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .to-warning {
    --tw-gradient-to: hsl(var(--heroui-warning) / 1);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .to-white {
    --tw-gradient-to: var(--color-white);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .\[mask-image\:linear-gradient\(\#000\,\#000\,transparent_0\,\#000_var\(--scroll-shadow-size\)\,\#000_calc\(100\%_-_var\(--scroll-shadow-size\)\)\,transparent\)\] {
    mask-image: linear-gradient(#000, #000, transparent 0, #000 var(--scroll-shadow-size), #000 calc(100% - var(--scroll-shadow-size)), transparent);
  }

  .bg-clip-inherit {
    background-clip: inherit;
  }

  .bg-clip-text {
    background-clip: text;
  }

  .fill-current {
    fill: currentColor;
  }

  .fill-green-500 {
    fill: var(--color-green-500);
  }

  .fill-primary {
    fill: var(--primary);
  }

  .stroke-current {
    stroke: currentColor;
  }

  .stroke-default-300\/50 {
    stroke: hsl(var(--heroui-default-300) / 1);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .stroke-default-300\/50 {
      stroke: color-mix(in oklab, hsl(var(--heroui-default-300) / 1) 50%, transparent);
    }
  }

  .object-contain {
    object-fit: contain;
  }

  .object-cover {
    object-fit: cover;
  }

  .object-center {
    object-position: center;
  }

  .object-left {
    object-position: left;
  }

  .p-0 {
    padding: calc(var(--spacing) * 0);
  }

  .p-0\.5 {
    padding: calc(var(--spacing) * .5);
  }

  .p-1 {
    padding: calc(var(--spacing) * 1);
  }

  .p-2 {
    padding: calc(var(--spacing) * 2);
  }

  .p-2\.5 {
    padding: calc(var(--spacing) * 2.5);
  }

  .p-3 {
    padding: calc(var(--spacing) * 3);
  }

  .p-4 {
    padding: calc(var(--spacing) * 4);
  }

  .p-5 {
    padding: calc(var(--spacing) * 5);
  }

  .p-6 {
    padding: calc(var(--spacing) * 6);
  }

  .p-8 {
    padding: calc(var(--spacing) * 8);
  }

  .p-\[3px\] {
    padding: 3px;
  }

  .p-px {
    padding: 1px;
  }

  .\!px-1 {
    padding-inline: calc(var(--spacing) * 1) !important;
  }

  .px-0 {
    padding-inline: calc(var(--spacing) * 0);
  }

  .px-0\.5 {
    padding-inline: calc(var(--spacing) * .5);
  }

  .px-1 {
    padding-inline: calc(var(--spacing) * 1);
  }

  .px-1\.5 {
    padding-inline: calc(var(--spacing) * 1.5);
  }

  .px-2 {
    padding-inline: calc(var(--spacing) * 2);
  }

  .px-2\.5 {
    padding-inline: calc(var(--spacing) * 2.5);
  }

  .px-3 {
    padding-inline: calc(var(--spacing) * 3);
  }

  .px-4 {
    padding-inline: calc(var(--spacing) * 4);
  }

  .px-5 {
    padding-inline: calc(var(--spacing) * 5);
  }

  .px-6 {
    padding-inline: calc(var(--spacing) * 6);
  }

  .px-8 {
    padding-inline: calc(var(--spacing) * 8);
  }

  .py-0 {
    padding-block: calc(var(--spacing) * 0);
  }

  .py-0\.5 {
    padding-block: calc(var(--spacing) * .5);
  }

  .py-1 {
    padding-block: calc(var(--spacing) * 1);
  }

  .py-1\.5 {
    padding-block: calc(var(--spacing) * 1.5);
  }

  .py-2 {
    padding-block: calc(var(--spacing) * 2);
  }

  .py-2\.5 {
    padding-block: calc(var(--spacing) * 2.5);
  }

  .py-3 {
    padding-block: calc(var(--spacing) * 3);
  }

  .py-4 {
    padding-block: calc(var(--spacing) * 4);
  }

  .py-6 {
    padding-block: calc(var(--spacing) * 6);
  }

  .py-8 {
    padding-block: calc(var(--spacing) * 8);
  }

  .py-10 {
    padding-block: calc(var(--spacing) * 10);
  }

  .py-12 {
    padding-block: calc(var(--spacing) * 12);
  }

  .py-16 {
    padding-block: calc(var(--spacing) * 16);
  }

  .ps-1 {
    padding-inline-start: calc(var(--spacing) * 1);
  }

  .ps-2 {
    padding-inline-start: calc(var(--spacing) * 2);
  }

  .ps-4 {
    padding-inline-start: calc(var(--spacing) * 4);
  }

  .ps-\[calc\(var\(--spacing\)_\*_4\.5_-_1px\)\] {
    padding-inline-start: calc(var(--spacing) * 4.5 - 1px);
  }

  .pe-2 {
    padding-inline-end: calc(var(--spacing) * 2);
  }

  .pe-6 {
    padding-inline-end: calc(var(--spacing) * 6);
  }

  .pt-0 {
    padding-top: calc(var(--spacing) * 0);
  }

  .pt-1 {
    padding-top: calc(var(--spacing) * 1);
  }

  .pt-2 {
    padding-top: calc(var(--spacing) * 2);
  }

  .pt-3 {
    padding-top: calc(var(--spacing) * 3);
  }

  .pt-4 {
    padding-top: calc(var(--spacing) * 4);
  }

  .pt-16 {
    padding-top: calc(var(--spacing) * 16);
  }

  .pr-0 {
    padding-right: calc(var(--spacing) * 0);
  }

  .pr-0\.5 {
    padding-right: calc(var(--spacing) * .5);
  }

  .pr-1 {
    padding-right: calc(var(--spacing) * 1);
  }

  .pr-2 {
    padding-right: calc(var(--spacing) * 2);
  }

  .pr-2\.5 {
    padding-right: calc(var(--spacing) * 2.5);
  }

  .pr-3 {
    padding-right: calc(var(--spacing) * 3);
  }

  .pr-4 {
    padding-right: calc(var(--spacing) * 4);
  }

  .pr-8 {
    padding-right: calc(var(--spacing) * 8);
  }

  .pr-10 {
    padding-right: calc(var(--spacing) * 10);
  }

  .\!pb-0 {
    padding-bottom: calc(var(--spacing) * 0) !important;
  }

  .pb-0 {
    padding-bottom: calc(var(--spacing) * 0);
  }

  .pb-0\.5 {
    padding-bottom: calc(var(--spacing) * .5);
  }

  .pb-1 {
    padding-bottom: calc(var(--spacing) * 1);
  }

  .pb-1\.5 {
    padding-bottom: calc(var(--spacing) * 1.5);
  }

  .pb-2 {
    padding-bottom: calc(var(--spacing) * 2);
  }

  .pb-3 {
    padding-bottom: calc(var(--spacing) * 3);
  }

  .pb-4 {
    padding-bottom: calc(var(--spacing) * 4);
  }

  .pl-0\.5 {
    padding-left: calc(var(--spacing) * .5);
  }

  .pl-1 {
    padding-left: calc(var(--spacing) * 1);
  }

  .pl-2 {
    padding-left: calc(var(--spacing) * 2);
  }

  .pl-3 {
    padding-left: calc(var(--spacing) * 3);
  }

  .pl-4 {
    padding-left: calc(var(--spacing) * 4);
  }

  .pl-5 {
    padding-left: calc(var(--spacing) * 5);
  }

  .pl-7 {
    padding-left: calc(var(--spacing) * 7);
  }

  .pl-8 {
    padding-left: calc(var(--spacing) * 8);
  }

  .pl-\[1px\] {
    padding-left: 1px;
  }

  .text-center {
    text-align: center;
  }

  .text-end {
    text-align: end;
  }

  .text-left {
    text-align: left;
  }

  .text-right {
    text-align: right;
  }

  .text-start {
    text-align: start;
  }

  .align-middle {
    vertical-align: middle;
  }

  .font-mono {
    font-family: var(--font-geist-mono);
  }

  .font-sans {
    font-family: var(--font-geist-sans);
  }

  .text-2xl {
    font-size: var(--text-2xl);
    line-height: var(--tw-leading, var(--text-2xl--line-height));
  }

  .text-3xl {
    font-size: var(--text-3xl);
    line-height: var(--tw-leading, var(--text-3xl--line-height));
  }

  .text-6xl {
    font-size: var(--text-6xl);
    line-height: var(--tw-leading, var(--text-6xl--line-height));
  }

  .text-base {
    font-size: var(--text-base);
    line-height: var(--tw-leading, var(--text-base--line-height));
  }

  .text-large {
    font-size: var(--heroui-font-size-large);
    line-height: var(--heroui-line-height-large);
  }

  .text-lg {
    font-size: var(--text-lg);
    line-height: var(--tw-leading, var(--text-lg--line-height));
  }

  .text-medium {
    font-size: var(--heroui-font-size-medium);
    line-height: var(--heroui-line-height-medium);
  }

  .text-sm {
    font-size: var(--text-sm);
    line-height: var(--tw-leading, var(--text-sm--line-height));
  }

  .text-small {
    font-size: var(--heroui-font-size-small);
    line-height: var(--heroui-line-height-small);
  }

  .text-tiny {
    font-size: var(--heroui-font-size-tiny);
    line-height: var(--heroui-line-height-tiny);
  }

  .text-xl {
    font-size: var(--text-xl);
    line-height: var(--tw-leading, var(--text-xl--line-height));
  }

  .text-xs {
    font-size: var(--text-xs);
    line-height: var(--tw-leading, var(--text-xs--line-height));
  }

  .text-\[0\.5rem\] {
    font-size: .5rem;
  }

  .text-\[0\.6rem\] {
    font-size: .6rem;
  }

  .text-\[0\.8rem\] {
    font-size: .8rem;
  }

  .text-\[0\.55rem\] {
    font-size: .55rem;
  }

  .text-\[10px\] {
    font-size: 10px;
  }

  .text-\[100\%\] {
    font-size: 100%;
  }

  .leading-5 {
    --tw-leading: calc(var(--spacing) * 5);
    line-height: calc(var(--spacing) * 5);
  }

  .leading-\[1\.15\] {
    --tw-leading: 1.15;
    line-height: 1.15;
  }

  .leading-\[32px\] {
    --tw-leading: 32px;
    line-height: 32px;
  }

  .leading-none {
    --tw-leading: 1;
    line-height: 1;
  }

  .leading-relaxed {
    --tw-leading: var(--leading-relaxed);
    line-height: var(--leading-relaxed);
  }

  .leading-tight {
    --tw-leading: var(--leading-tight);
    line-height: var(--leading-tight);
  }

  .leading-inherit {
    line-height: inherit;
  }

  .font-bold {
    --tw-font-weight: var(--font-weight-bold);
    font-weight: var(--font-weight-bold);
  }

  .font-extralight {
    --tw-font-weight: var(--font-weight-extralight);
    font-weight: var(--font-weight-extralight);
  }

  .font-medium {
    --tw-font-weight: var(--font-weight-medium);
    font-weight: var(--font-weight-medium);
  }

  .font-normal {
    --tw-font-weight: var(--font-weight-normal);
    font-weight: var(--font-weight-normal);
  }

  .font-semibold {
    --tw-font-weight: var(--font-weight-semibold);
    font-weight: var(--font-weight-semibold);
  }

  .tracking-tight {
    --tw-tracking: var(--tracking-tight);
    letter-spacing: var(--tracking-tight);
  }

  .tracking-wider {
    --tw-tracking: var(--tracking-wider);
    letter-spacing: var(--tracking-wider);
  }

  .tracking-widest {
    --tw-tracking: var(--tracking-widest);
    letter-spacing: var(--tracking-widest);
  }

  .text-balance {
    text-wrap: balance;
  }

  .break-words {
    overflow-wrap: break-word;
  }

  .break-all {
    word-break: break-all;
  }

  .text-ellipsis {
    text-overflow: ellipsis;
  }

  .whitespace-normal {
    white-space: normal;
  }

  .whitespace-nowrap {
    white-space: nowrap;
  }

  .whitespace-pre-wrap {
    white-space: pre-wrap;
  }

  .\!text-danger {
    color: hsl(var(--heroui-danger) / 1) !important;
  }

  .text-accent-foreground {
    color: var(--accent-foreground);
  }

  .text-amber-500 {
    color: var(--color-amber-500);
  }

  .text-amber-600 {
    color: var(--color-amber-600);
  }

  .text-amber-800 {
    color: var(--color-amber-800);
  }

  .text-background {
    color: var(--background);
  }

  .text-black {
    color: var(--color-black);
  }

  .text-blue-500 {
    color: var(--color-blue-500);
  }

  .text-blue-600 {
    color: var(--color-blue-600);
  }

  .text-blue-700 {
    color: var(--color-blue-700);
  }

  .text-blue-800 {
    color: var(--color-blue-800);
  }

  .text-card-foreground {
    color: var(--card-foreground);
  }

  .text-current {
    color: currentColor;
  }

  .text-cyan-800 {
    color: var(--color-cyan-800);
  }

  .text-danger {
    color: hsl(var(--heroui-danger) / 1);
  }

  .text-danger-300 {
    color: hsl(var(--heroui-danger-300) / 1);
  }

  .text-danger-400 {
    color: hsl(var(--heroui-danger-400) / 1);
  }

  .text-danger-500 {
    color: hsl(var(--heroui-danger-500) / 1);
  }

  .text-danger-600 {
    color: hsl(var(--heroui-danger-600) / 1);
  }

  .text-danger-foreground {
    color: hsl(var(--heroui-danger-foreground) / 1);
  }

  .text-danger\/80 {
    color: hsl(var(--heroui-danger) / 1);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .text-danger\/80 {
      color: color-mix(in oklab, hsl(var(--heroui-danger) / 1) 80%, transparent);
    }
  }

  .text-default {
    color: hsl(var(--heroui-default) / 1);
  }

  .text-default-400 {
    color: hsl(var(--heroui-default-400) / 1);
  }

  .text-default-500 {
    color: hsl(var(--heroui-default-500) / 1);
  }

  .text-default-600 {
    color: hsl(var(--heroui-default-600) / 1);
  }

  .text-default-700 {
    color: hsl(var(--heroui-default-700) / 1);
  }

  .text-default-foreground {
    color: hsl(var(--heroui-default-foreground) / 1);
  }

  .text-destructive {
    color: var(--destructive);
  }

  .text-foreground {
    color: var(--foreground);
  }

  .text-foreground-400 {
    color: hsl(var(--heroui-foreground-400) / 1);
  }

  .text-foreground-500 {
    color: hsl(var(--heroui-foreground-500) / 1);
  }

  .text-foreground-600 {
    color: hsl(var(--heroui-foreground-600) / 1);
  }

  .text-foreground\/50 {
    color: var(--foreground);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .text-foreground\/50 {
      color: color-mix(in oklab, var(--foreground) 50%, transparent);
    }
  }

  .text-gray-300 {
    color: var(--color-gray-300);
  }

  .text-gray-400 {
    color: var(--color-gray-400);
  }

  .text-gray-500 {
    color: var(--color-gray-500);
  }

  .text-gray-600 {
    color: var(--color-gray-600);
  }

  .text-gray-700 {
    color: var(--color-gray-700);
  }

  .text-gray-800 {
    color: var(--color-gray-800);
  }

  .text-gray-900 {
    color: var(--color-gray-900);
  }

  .text-green-500 {
    color: var(--color-green-500);
  }

  .text-green-600 {
    color: var(--color-green-600);
  }

  .text-green-700 {
    color: var(--color-green-700);
  }

  .text-green-800 {
    color: var(--color-green-800);
  }

  .text-indigo-500 {
    color: var(--color-indigo-500);
  }

  .text-indigo-600 {
    color: var(--color-indigo-600);
  }

  .text-indigo-800 {
    color: var(--color-indigo-800);
  }

  .text-inherit {
    color: inherit;
  }

  .text-muted-foreground {
    color: var(--muted-foreground);
  }

  .text-orange-500 {
    color: var(--color-orange-500);
  }

  .text-orange-600 {
    color: var(--color-orange-600);
  }

  .text-orange-800 {
    color: var(--color-orange-800);
  }

  .text-pink-500 {
    color: var(--color-pink-500);
  }

  .text-pink-800 {
    color: var(--color-pink-800);
  }

  .text-popover-foreground {
    color: var(--popover-foreground);
  }

  .text-primary {
    color: var(--primary);
  }

  .text-primary-300 {
    color: hsl(var(--heroui-primary-300) / 1);
  }

  .text-primary-400 {
    color: hsl(var(--heroui-primary-400) / 1);
  }

  .text-primary-500 {
    color: hsl(var(--heroui-primary-500) / 1);
  }

  .text-primary-600 {
    color: hsl(var(--heroui-primary-600) / 1);
  }

  .text-primary-foreground {
    color: var(--primary-foreground);
  }

  .text-primary\/80 {
    color: var(--primary);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .text-primary\/80 {
      color: color-mix(in oklab, var(--primary) 80%, transparent);
    }
  }

  .text-purple-500 {
    color: var(--color-purple-500);
  }

  .text-purple-600 {
    color: var(--color-purple-600);
  }

  .text-purple-800 {
    color: var(--color-purple-800);
  }

  .text-red-500 {
    color: var(--color-red-500);
  }

  .text-red-600 {
    color: var(--color-red-600);
  }

  .text-red-700 {
    color: var(--color-red-700);
  }

  .text-red-800 {
    color: var(--color-red-800);
  }

  .text-rose-800 {
    color: var(--color-rose-800);
  }

  .text-secondary {
    color: var(--secondary);
  }

  .text-secondary-300 {
    color: hsl(var(--heroui-secondary-300) / 1);
  }

  .text-secondary-400 {
    color: hsl(var(--heroui-secondary-400) / 1);
  }

  .text-secondary-500 {
    color: hsl(var(--heroui-secondary-500) / 1);
  }

  .text-secondary-600 {
    color: hsl(var(--heroui-secondary-600) / 1);
  }

  .text-secondary-foreground {
    color: var(--secondary-foreground);
  }

  .text-secondary\/80 {
    color: var(--secondary);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .text-secondary\/80 {
      color: color-mix(in oklab, var(--secondary) 80%, transparent);
    }
  }

  .text-sidebar-foreground {
    color: var(--sidebar-foreground);
  }

  .text-sidebar-foreground\/70 {
    color: var(--sidebar-foreground);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .text-sidebar-foreground\/70 {
      color: color-mix(in oklab, var(--sidebar-foreground) 70%, transparent);
    }
  }

  .text-sidebar-primary-foreground {
    color: var(--sidebar-primary-foreground);
  }

  .text-slate-800 {
    color: var(--color-slate-800);
  }

  .text-success {
    color: hsl(var(--heroui-success) / 1);
  }

  .text-success-400 {
    color: hsl(var(--heroui-success-400) / 1);
  }

  .text-success-500 {
    color: hsl(var(--heroui-success-500) / 1);
  }

  .text-success-600 {
    color: hsl(var(--heroui-success-600) / 1);
  }

  .text-success-700 {
    color: hsl(var(--heroui-success-700) / 1);
  }

  .text-success-foreground {
    color: hsl(var(--heroui-success-foreground) / 1);
  }

  .text-success\/80 {
    color: hsl(var(--heroui-success) / 1);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .text-success\/80 {
      color: color-mix(in oklab, hsl(var(--heroui-success) / 1) 80%, transparent);
    }
  }

  .text-teal-800 {
    color: var(--color-teal-800);
  }

  .text-transparent {
    color: #0000;
  }

  .text-warning {
    color: hsl(var(--heroui-warning) / 1);
  }

  .text-warning-400 {
    color: hsl(var(--heroui-warning-400) / 1);
  }

  .text-warning-500 {
    color: hsl(var(--heroui-warning-500) / 1);
  }

  .text-warning-600 {
    color: hsl(var(--heroui-warning-600) / 1);
  }

  .text-warning-700 {
    color: hsl(var(--heroui-warning-700) / 1);
  }

  .text-warning-foreground {
    color: hsl(var(--heroui-warning-foreground) / 1);
  }

  .text-warning\/80 {
    color: hsl(var(--heroui-warning) / 1);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .text-warning\/80 {
      color: color-mix(in oklab, hsl(var(--heroui-warning) / 1) 80%, transparent);
    }
  }

  .text-white {
    color: var(--color-white);
  }

  .text-yellow-400 {
    color: var(--color-yellow-400);
  }

  .text-yellow-500 {
    color: var(--color-yellow-500);
  }

  .text-yellow-600 {
    color: var(--color-yellow-600);
  }

  .text-yellow-700 {
    color: var(--color-yellow-700);
  }

  .text-yellow-800 {
    color: var(--color-yellow-800);
  }

  .capitalize {
    text-transform: capitalize;
  }

  .uppercase {
    text-transform: uppercase;
  }

  .italic {
    font-style: italic;
  }

  .tabular-nums {
    --tw-numeric-spacing: tabular-nums;
    font-variant-numeric: var(--tw-ordinal, ) var(--tw-slashed-zero, ) var(--tw-numeric-figure, ) var(--tw-numeric-spacing, ) var(--tw-numeric-fraction, );
  }

  .no-underline {
    text-decoration-line: none;
  }

  .underline {
    text-decoration-line: underline;
  }

  .underline-offset-4 {
    text-underline-offset: 4px;
  }

  .antialiased {
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }

  .subpixel-antialiased {
    -webkit-font-smoothing: auto;
    -moz-osx-font-smoothing: auto;
  }

  .dark {
    color-scheme: dark;
    --heroui-background: 0 0% 0%;
    --heroui-foreground-50: 240 5.88% 10%;
    --heroui-foreground-100: 240 3.7% 15.88%;
    --heroui-foreground-200: 240 5.26% 26.08%;
    --heroui-foreground-300: 240 5.2% 33.92%;
    --heroui-foreground-400: 240 3.83% 46.08%;
    --heroui-foreground-500: 240 5.03% 64.9%;
    --heroui-foreground-600: 240 4.88% 83.92%;
    --heroui-foreground-700: 240 5.88% 90%;
    --heroui-foreground-800: 240 4.76% 95.88%;
    --heroui-foreground-900: 0 0% 98.04%;
    --heroui-foreground: 210 5.56% 92.94%;
    --heroui-focus: 212.02 100% 46.67%;
    --heroui-overlay: 0 0% 0%;
    --heroui-divider: 0 0% 100%;
    --heroui-content1: 240 5.88% 10%;
    --heroui-content1-foreground: 0 0% 98.04%;
    --heroui-content2: 240 3.7% 15.88%;
    --heroui-content2-foreground: 240 4.76% 95.88%;
    --heroui-content3: 240 5.26% 26.08%;
    --heroui-content3-foreground: 240 5.88% 90%;
    --heroui-content4: 240 5.2% 33.92%;
    --heroui-content4-foreground: 240 4.88% 83.92%;
    --heroui-default-50: 240 5.88% 10%;
    --heroui-default-100: 240 3.7% 15.88%;
    --heroui-default-200: 240 5.26% 26.08%;
    --heroui-default-300: 240 5.2% 33.92%;
    --heroui-default-400: 240 3.83% 46.08%;
    --heroui-default-500: 240 5.03% 64.9%;
    --heroui-default-600: 240 4.88% 83.92%;
    --heroui-default-700: 240 5.88% 90%;
    --heroui-default-800: 240 4.76% 95.88%;
    --heroui-default-900: 0 0% 98.04%;
    --heroui-default-foreground: 0 0% 100%;
    --heroui-default: 240 5.26% 26.08%;
    --heroui-primary-50: 211.84 100% 9.61%;
    --heroui-primary-100: 211.84 100% 19.22%;
    --heroui-primary-200: 212.24 100% 28.82%;
    --heroui-primary-300: 212.14 100% 38.43%;
    --heroui-primary-400: 212.02 100% 46.67%;
    --heroui-primary-500: 212.14 92.45% 58.43%;
    --heroui-primary-600: 212.24 92.45% 68.82%;
    --heroui-primary-700: 211.84 92.45% 79.22%;
    --heroui-primary-800: 211.84 92.45% 89.61%;
    --heroui-primary-900: 212.5 92.31% 94.9%;
    --heroui-primary-foreground: 0 0% 100%;
    --heroui-primary: 212.02 100% 46.67%;
    --heroui-secondary-50: 270 66.67% 9.41%;
    --heroui-secondary-100: 270 66.67% 18.82%;
    --heroui-secondary-200: 270 66.67% 28.24%;
    --heroui-secondary-300: 270 66.67% 37.65%;
    --heroui-secondary-400: 270 66.67% 47.06%;
    --heroui-secondary-500: 270 59.26% 57.65%;
    --heroui-secondary-600: 270 59.26% 68.24%;
    --heroui-secondary-700: 270 59.26% 78.82%;
    --heroui-secondary-800: 270 59.26% 89.41%;
    --heroui-secondary-900: 270 61.54% 94.9%;
    --heroui-secondary-foreground: 0 0% 100%;
    --heroui-secondary: 270 59.26% 57.65%;
    --heroui-success-50: 145.71 77.78% 8.82%;
    --heroui-success-100: 146.2 79.78% 17.45%;
    --heroui-success-200: 145.79 79.26% 26.47%;
    --heroui-success-300: 146.01 79.89% 35.1%;
    --heroui-success-400: 145.96 79.46% 43.92%;
    --heroui-success-500: 146.01 62.45% 55.1%;
    --heroui-success-600: 145.79 62.57% 66.47%;
    --heroui-success-700: 146.2 61.74% 77.45%;
    --heroui-success-800: 145.71 61.4% 88.82%;
    --heroui-success-900: 146.67 64.29% 94.51%;
    --heroui-success-foreground: 0 0% 0%;
    --heroui-success: 145.96 79.46% 43.92%;
    --heroui-warning-50: 37.14 75% 10.98%;
    --heroui-warning-100: 37.14 75% 21.96%;
    --heroui-warning-200: 36.96 73.96% 33.14%;
    --heroui-warning-300: 37.01 74.22% 44.12%;
    --heroui-warning-400: 37.03 91.27% 55.1%;
    --heroui-warning-500: 37.01 91.26% 64.12%;
    --heroui-warning-600: 36.96 91.24% 73.14%;
    --heroui-warning-700: 37.14 91.3% 81.96%;
    --heroui-warning-800: 37.14 91.3% 90.98%;
    --heroui-warning-900: 54.55 91.67% 95.29%;
    --heroui-warning-foreground: 0 0% 0%;
    --heroui-warning: 37.03 91.27% 55.1%;
    --heroui-danger-50: 340 84.91% 10.39%;
    --heroui-danger-100: 339.33 86.54% 20.39%;
    --heroui-danger-200: 339.11 85.99% 30.78%;
    --heroui-danger-300: 339 86.54% 40.78%;
    --heroui-danger-400: 339.2 90.36% 51.18%;
    --heroui-danger-500: 339 90% 60.78%;
    --heroui-danger-600: 339.11 90.6% 70.78%;
    --heroui-danger-700: 339.33 90% 80.39%;
    --heroui-danger-800: 340 91.84% 90.39%;
    --heroui-danger-900: 339.13 92% 95.1%;
    --heroui-danger-foreground: 0 0% 100%;
    --heroui-danger: 339.2 90.36% 51.18%;
    --heroui-divider-weight: 1px;
    --heroui-disabled-opacity: .5;
    --heroui-font-size-tiny: .75rem;
    --heroui-font-size-small: .875rem;
    --heroui-font-size-medium: 1rem;
    --heroui-font-size-large: 1.125rem;
    --heroui-line-height-tiny: 1rem;
    --heroui-line-height-small: 1.25rem;
    --heroui-line-height-medium: 1.5rem;
    --heroui-line-height-large: 1.75rem;
    --heroui-radius-small: 8px;
    --heroui-radius-medium: 12px;
    --heroui-radius-large: 14px;
    --heroui-border-width-small: 1px;
    --heroui-border-width-medium: 2px;
    --heroui-border-width-large: 3px;
    --heroui-box-shadow-small: 0px 0px 5px 0px #0000000d, 0px 2px 10px 0px #0003, inset 0px 0px 1px 0px #ffffff26;
    --heroui-box-shadow-medium: 0px 0px 15px 0px #0000000f, 0px 2px 30px 0px #00000038, inset 0px 0px 1px 0px #ffffff26;
    --heroui-box-shadow-large: 0px 0px 30px 0px #00000012, 0px 30px 60px 0px #00000042, inset 0px 0px 1px 0px #ffffff26;
    --heroui-hover-opacity: .9;
  }

  .light {
    color-scheme: light;
    --heroui-background: 0 0% 100%;
    --heroui-foreground-50: 0 0% 98.04%;
    --heroui-foreground-100: 240 4.76% 95.88%;
    --heroui-foreground-200: 240 5.88% 90%;
    --heroui-foreground-300: 240 4.88% 83.92%;
    --heroui-foreground-400: 240 5.03% 64.9%;
    --heroui-foreground-500: 240 3.83% 46.08%;
    --heroui-foreground-600: 240 5.2% 33.92%;
    --heroui-foreground-700: 240 5.26% 26.08%;
    --heroui-foreground-800: 240 3.7% 15.88%;
    --heroui-foreground-900: 240 5.88% 10%;
    --heroui-foreground: 201.82 24.44% 8.82%;
    --heroui-divider: 0 0% 6.67%;
    --heroui-focus: 212.02 100% 46.67%;
    --heroui-overlay: 0 0% 0%;
    --heroui-content1: 0 0% 100%;
    --heroui-content1-foreground: 201.82 24.44% 8.82%;
    --heroui-content2: 240 4.76% 95.88%;
    --heroui-content2-foreground: 240 3.7% 15.88%;
    --heroui-content3: 240 5.88% 90%;
    --heroui-content3-foreground: 240 5.26% 26.08%;
    --heroui-content4: 240 4.88% 83.92%;
    --heroui-content4-foreground: 240 5.2% 33.92%;
    --heroui-default-50: 0 0% 98.04%;
    --heroui-default-100: 240 4.76% 95.88%;
    --heroui-default-200: 240 5.88% 90%;
    --heroui-default-300: 240 4.88% 83.92%;
    --heroui-default-400: 240 5.03% 64.9%;
    --heroui-default-500: 240 3.83% 46.08%;
    --heroui-default-600: 240 5.2% 33.92%;
    --heroui-default-700: 240 5.26% 26.08%;
    --heroui-default-800: 240 3.7% 15.88%;
    --heroui-default-900: 240 5.88% 10%;
    --heroui-default-foreground: 0 0% 0%;
    --heroui-default: 240 4.88% 83.92%;
    --heroui-primary-50: 212.5 92.31% 94.9%;
    --heroui-primary-100: 211.84 92.45% 89.61%;
    --heroui-primary-200: 211.84 92.45% 79.22%;
    --heroui-primary-300: 212.24 92.45% 68.82%;
    --heroui-primary-400: 212.14 92.45% 58.43%;
    --heroui-primary-500: 212.02 100% 46.67%;
    --heroui-primary-600: 212.14 100% 38.43%;
    --heroui-primary-700: 212.24 100% 28.82%;
    --heroui-primary-800: 211.84 100% 19.22%;
    --heroui-primary-900: 211.84 100% 9.61%;
    --heroui-primary-foreground: 0 0% 100%;
    --heroui-primary: 212.02 100% 46.67%;
    --heroui-secondary-50: 270 61.54% 94.9%;
    --heroui-secondary-100: 270 59.26% 89.41%;
    --heroui-secondary-200: 270 59.26% 78.82%;
    --heroui-secondary-300: 270 59.26% 68.24%;
    --heroui-secondary-400: 270 59.26% 57.65%;
    --heroui-secondary-500: 270 66.67% 47.06%;
    --heroui-secondary-600: 270 66.67% 37.65%;
    --heroui-secondary-700: 270 66.67% 28.24%;
    --heroui-secondary-800: 270 66.67% 18.82%;
    --heroui-secondary-900: 270 66.67% 9.41%;
    --heroui-secondary-foreground: 0 0% 100%;
    --heroui-secondary: 270 66.67% 47.06%;
    --heroui-success-50: 146.67 64.29% 94.51%;
    --heroui-success-100: 145.71 61.4% 88.82%;
    --heroui-success-200: 146.2 61.74% 77.45%;
    --heroui-success-300: 145.79 62.57% 66.47%;
    --heroui-success-400: 146.01 62.45% 55.1%;
    --heroui-success-500: 145.96 79.46% 43.92%;
    --heroui-success-600: 146.01 79.89% 35.1%;
    --heroui-success-700: 145.79 79.26% 26.47%;
    --heroui-success-800: 146.2 79.78% 17.45%;
    --heroui-success-900: 145.71 77.78% 8.82%;
    --heroui-success-foreground: 0 0% 0%;
    --heroui-success: 145.96 79.46% 43.92%;
    --heroui-warning-50: 54.55 91.67% 95.29%;
    --heroui-warning-100: 37.14 91.3% 90.98%;
    --heroui-warning-200: 37.14 91.3% 81.96%;
    --heroui-warning-300: 36.96 91.24% 73.14%;
    --heroui-warning-400: 37.01 91.26% 64.12%;
    --heroui-warning-500: 37.03 91.27% 55.1%;
    --heroui-warning-600: 37.01 74.22% 44.12%;
    --heroui-warning-700: 36.96 73.96% 33.14%;
    --heroui-warning-800: 37.14 75% 21.96%;
    --heroui-warning-900: 37.14 75% 10.98%;
    --heroui-warning-foreground: 0 0% 0%;
    --heroui-warning: 37.03 91.27% 55.1%;
    --heroui-danger-50: 339.13 92% 95.1%;
    --heroui-danger-100: 340 91.84% 90.39%;
    --heroui-danger-200: 339.33 90% 80.39%;
    --heroui-danger-300: 339.11 90.6% 70.78%;
    --heroui-danger-400: 339 90% 60.78%;
    --heroui-danger-500: 339.2 90.36% 51.18%;
    --heroui-danger-600: 339 86.54% 40.78%;
    --heroui-danger-700: 339.11 85.99% 30.78%;
    --heroui-danger-800: 339.33 86.54% 20.39%;
    --heroui-danger-900: 340 84.91% 10.39%;
    --heroui-danger-foreground: 0 0% 100%;
    --heroui-danger: 339.2 90.36% 51.18%;
    --heroui-divider-weight: 1px;
    --heroui-disabled-opacity: .5;
    --heroui-font-size-tiny: .75rem;
    --heroui-font-size-small: .875rem;
    --heroui-font-size-medium: 1rem;
    --heroui-font-size-large: 1.125rem;
    --heroui-line-height-tiny: 1rem;
    --heroui-line-height-small: 1.25rem;
    --heroui-line-height-medium: 1.5rem;
    --heroui-line-height-large: 1.75rem;
    --heroui-radius-small: 8px;
    --heroui-radius-medium: 12px;
    --heroui-radius-large: 14px;
    --heroui-border-width-small: 1px;
    --heroui-border-width-medium: 2px;
    --heroui-border-width-large: 3px;
    --heroui-box-shadow-small: 0px 0px 5px 0px #00000005, 0px 2px 10px 0px #0000000f, 0px 0px 1px 0px #0000004d;
    --heroui-box-shadow-medium: 0px 0px 15px 0px #00000008, 0px 2px 30px 0px #00000014, 0px 0px 1px 0px #0000004d;
    --heroui-box-shadow-large: 0px 0px 30px 0px #0000000a, 0px 30px 60px 0px #0000001f, 0px 0px 1px 0px #0000004d;
    --heroui-hover-opacity: .8;
  }

  .opacity-0 {
    opacity: 0;
  }

  .opacity-20 {
    opacity: .2;
  }

  .opacity-25 {
    opacity: .25;
  }

  .opacity-30 {
    opacity: .3;
  }

  .opacity-50 {
    opacity: .5;
  }

  .opacity-70 {
    opacity: .7;
  }

  .opacity-75 {
    opacity: .75;
  }

  .opacity-100 {
    opacity: 1;
  }

  .opacity-\[0\.0001\] {
    opacity: .0001;
  }

  .opacity-\[value\] {
    opacity: value;
  }

  .opacity-disabled {
    opacity: var(--heroui-disabled-opacity);
  }

  .\!shadow-none {
    --tw-shadow: 0 0 #0000 !important;
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow) !important;
  }

  .shadow {
    --tw-shadow: 0 1px 3px 0 var(--tw-shadow-color, #0000001a), 0 1px 2px -1px var(--tw-shadow-color, #0000001a);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .shadow-\[0_0_0_1px_hsl\(var\(--sidebar-border\)\)\] {
    --tw-shadow: 0 0 0 1px var(--tw-shadow-color, hsl(var(--sidebar-border)));
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .shadow-\[0_1px_0px_0_rgba\(0\,0\,0\,0\.05\)\] {
    --tw-shadow: 0 1px 0px 0 var(--tw-shadow-color, #0000000d);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .shadow-\[0px_20px_20px_0px_rgb\(0_0_0\/0\.05\)\] {
    --tw-shadow: 0px 20px 20px 0px var(--tw-shadow-color, #0000000d);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .shadow-large {
    --tw-shadow: var(--heroui-box-shadow-large);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .shadow-lg {
    --tw-shadow: 0 10px 15px -3px var(--tw-shadow-color, #0000001a), 0 4px 6px -4px var(--tw-shadow-color, #0000001a);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .shadow-md {
    --tw-shadow: 0 4px 6px -1px var(--tw-shadow-color, #0000001a), 0 2px 4px -2px var(--tw-shadow-color, #0000001a);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .shadow-medium {
    --tw-shadow: var(--heroui-box-shadow-medium);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .shadow-none {
    --tw-shadow: 0 0 #0000;
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .shadow-sm {
    --tw-shadow: 0 1px 3px 0 var(--tw-shadow-color, #0000001a), 0 1px 2px -1px var(--tw-shadow-color, #0000001a);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .shadow-small {
    --tw-shadow: var(--heroui-box-shadow-small);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .shadow-xl {
    --tw-shadow: 0 20px 25px -5px var(--tw-shadow-color, #0000001a), 0 8px 10px -6px var(--tw-shadow-color, #0000001a);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .shadow-xs {
    --tw-shadow: 0 1px 2px 0 var(--tw-shadow-color, #0000000d);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .ring {
    --tw-ring-shadow: var(--tw-ring-inset, ) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .ring-0 {
    --tw-ring-shadow: var(--tw-ring-inset, ) 0 0 0 calc(0px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .ring-1 {
    --tw-ring-shadow: var(--tw-ring-inset, ) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .ring-2 {
    --tw-ring-shadow: var(--tw-ring-inset, ) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .shadow-black\/5 {
    --tw-shadow-color: #0000000d;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .shadow-black\/5 {
      --tw-shadow-color: color-mix(in oklab, color-mix(in oklab, var(--color-black) 5%, transparent) var(--tw-shadow-alpha), transparent);
    }
  }

  .shadow-danger\/40 {
    --tw-shadow-color: hsl(var(--heroui-danger) / 1);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .shadow-danger\/40 {
      --tw-shadow-color: color-mix(in oklab, color-mix(in oklab, hsl(var(--heroui-danger) / 1) 40%, transparent) var(--tw-shadow-alpha), transparent);
    }
  }

  .shadow-default\/50 {
    --tw-shadow-color: hsl(var(--heroui-default) / 1);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .shadow-default\/50 {
      --tw-shadow-color: color-mix(in oklab, color-mix(in oklab, hsl(var(--heroui-default) / 1) 50%, transparent) var(--tw-shadow-alpha), transparent);
    }
  }

  .shadow-foreground\/40 {
    --tw-shadow-color: var(--foreground);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .shadow-foreground\/40 {
      --tw-shadow-color: color-mix(in oklab, color-mix(in oklab, var(--foreground) 40%, transparent) var(--tw-shadow-alpha), transparent);
    }
  }

  .shadow-primary\/40 {
    --tw-shadow-color: var(--primary);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .shadow-primary\/40 {
      --tw-shadow-color: color-mix(in oklab, color-mix(in oklab, var(--primary) 40%, transparent) var(--tw-shadow-alpha), transparent);
    }
  }

  .shadow-secondary\/40 {
    --tw-shadow-color: var(--secondary);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .shadow-secondary\/40 {
      --tw-shadow-color: color-mix(in oklab, color-mix(in oklab, var(--secondary) 40%, transparent) var(--tw-shadow-alpha), transparent);
    }
  }

  .shadow-success\/40 {
    --tw-shadow-color: hsl(var(--heroui-success) / 1);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .shadow-success\/40 {
      --tw-shadow-color: color-mix(in oklab, color-mix(in oklab, hsl(var(--heroui-success) / 1) 40%, transparent) var(--tw-shadow-alpha), transparent);
    }
  }

  .shadow-warning\/40 {
    --tw-shadow-color: hsl(var(--heroui-warning) / 1);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .shadow-warning\/40 {
      --tw-shadow-color: color-mix(in oklab, color-mix(in oklab, hsl(var(--heroui-warning) / 1) 40%, transparent) var(--tw-shadow-alpha), transparent);
    }
  }

  .ring-background {
    --tw-ring-color: var(--background);
  }

  .ring-danger {
    --tw-ring-color: hsl(var(--heroui-danger) / 1);
  }

  .ring-default {
    --tw-ring-color: hsl(var(--heroui-default) / 1);
  }

  .ring-focus {
    --tw-ring-color: hsl(var(--heroui-focus) / 1);
  }

  .ring-primary {
    --tw-ring-color: var(--primary);
  }

  .ring-ring\/50 {
    --tw-ring-color: var(--ring);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .ring-ring\/50 {
      --tw-ring-color: color-mix(in oklab, var(--ring) 50%, transparent);
    }
  }

  .ring-secondary {
    --tw-ring-color: var(--secondary);
  }

  .ring-sidebar-ring {
    --tw-ring-color: var(--sidebar-ring);
  }

  .ring-success {
    --tw-ring-color: hsl(var(--heroui-success) / 1);
  }

  .ring-transparent {
    --tw-ring-color: transparent;
  }

  .ring-warning {
    --tw-ring-color: hsl(var(--heroui-warning) / 1);
  }

  .ring-offset-2 {
    --tw-ring-offset-width: 2px;
    --tw-ring-offset-shadow: var(--tw-ring-inset, ) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  }

  .ring-offset-background {
    --tw-ring-offset-color: var(--background);
  }

  .outline-hidden {
    --tw-outline-style: none;
    outline-style: none;
  }

  @media (forced-colors: active) {
    .outline-hidden {
      outline-offset: 2px;
      outline: 2px solid #0000;
    }
  }

  .outline {
    outline-style: var(--tw-outline-style);
    outline-width: 1px;
  }

  .blur {
    --tw-blur: blur(8px);
    filter: var(--tw-blur, ) var(--tw-brightness, ) var(--tw-contrast, ) var(--tw-grayscale, ) var(--tw-hue-rotate, ) var(--tw-invert, ) var(--tw-saturate, ) var(--tw-sepia, ) var(--tw-drop-shadow, );
  }

  .blur-lg {
    --tw-blur: blur(var(--blur-lg));
    filter: var(--tw-blur, ) var(--tw-brightness, ) var(--tw-contrast, ) var(--tw-grayscale, ) var(--tw-hue-rotate, ) var(--tw-invert, ) var(--tw-saturate, ) var(--tw-sepia, ) var(--tw-drop-shadow, );
  }

  .drop-shadow-md {
    --tw-drop-shadow-size: drop-shadow(0 3px 3px var(--tw-drop-shadow-color, #0000001f));
    --tw-drop-shadow: drop-shadow(var(--drop-shadow-md));
    filter: var(--tw-blur, ) var(--tw-brightness, ) var(--tw-contrast, ) var(--tw-grayscale, ) var(--tw-hue-rotate, ) var(--tw-invert, ) var(--tw-saturate, ) var(--tw-sepia, ) var(--tw-drop-shadow, );
  }

  .grayscale {
    --tw-grayscale: grayscale(100%);
    filter: var(--tw-blur, ) var(--tw-brightness, ) var(--tw-contrast, ) var(--tw-grayscale, ) var(--tw-hue-rotate, ) var(--tw-invert, ) var(--tw-saturate, ) var(--tw-sepia, ) var(--tw-drop-shadow, );
  }

  .saturate-150 {
    --tw-saturate: saturate(150%);
    filter: var(--tw-blur, ) var(--tw-brightness, ) var(--tw-contrast, ) var(--tw-grayscale, ) var(--tw-hue-rotate, ) var(--tw-invert, ) var(--tw-saturate, ) var(--tw-sepia, ) var(--tw-drop-shadow, );
  }

  .filter {
    filter: var(--tw-blur, ) var(--tw-brightness, ) var(--tw-contrast, ) var(--tw-grayscale, ) var(--tw-hue-rotate, ) var(--tw-invert, ) var(--tw-saturate, ) var(--tw-sepia, ) var(--tw-drop-shadow, );
  }

  .backdrop-blur {
    --tw-backdrop-blur: blur(8px);
    backdrop-filter: var(--tw-backdrop-blur, ) var(--tw-backdrop-brightness, ) var(--tw-backdrop-contrast, ) var(--tw-backdrop-grayscale, ) var(--tw-backdrop-hue-rotate, ) var(--tw-backdrop-invert, ) var(--tw-backdrop-opacity, ) var(--tw-backdrop-saturate, ) var(--tw-backdrop-sepia, );
  }

  .backdrop-blur-lg {
    --tw-backdrop-blur: blur(var(--blur-lg));
    backdrop-filter: var(--tw-backdrop-blur, ) var(--tw-backdrop-brightness, ) var(--tw-backdrop-contrast, ) var(--tw-backdrop-grayscale, ) var(--tw-backdrop-hue-rotate, ) var(--tw-backdrop-invert, ) var(--tw-backdrop-opacity, ) var(--tw-backdrop-saturate, ) var(--tw-backdrop-sepia, );
  }

  .backdrop-blur-md {
    --tw-backdrop-blur: blur(var(--blur-md));
    backdrop-filter: var(--tw-backdrop-blur, ) var(--tw-backdrop-brightness, ) var(--tw-backdrop-contrast, ) var(--tw-backdrop-grayscale, ) var(--tw-backdrop-hue-rotate, ) var(--tw-backdrop-invert, ) var(--tw-backdrop-opacity, ) var(--tw-backdrop-saturate, ) var(--tw-backdrop-sepia, );
  }

  .backdrop-blur-sm {
    --tw-backdrop-blur: blur(var(--blur-sm));
    backdrop-filter: var(--tw-backdrop-blur, ) var(--tw-backdrop-brightness, ) var(--tw-backdrop-contrast, ) var(--tw-backdrop-grayscale, ) var(--tw-backdrop-hue-rotate, ) var(--tw-backdrop-invert, ) var(--tw-backdrop-opacity, ) var(--tw-backdrop-saturate, ) var(--tw-backdrop-sepia, );
  }

  .backdrop-blur-xl {
    --tw-backdrop-blur: blur(var(--blur-xl));
    backdrop-filter: var(--tw-backdrop-blur, ) var(--tw-backdrop-brightness, ) var(--tw-backdrop-contrast, ) var(--tw-backdrop-grayscale, ) var(--tw-backdrop-hue-rotate, ) var(--tw-backdrop-invert, ) var(--tw-backdrop-opacity, ) var(--tw-backdrop-saturate, ) var(--tw-backdrop-sepia, );
  }

  .backdrop-opacity-disabled {
    --tw-backdrop-opacity: opacity(var(--heroui-disabled-opacity));
    backdrop-filter: var(--tw-backdrop-blur, ) var(--tw-backdrop-brightness, ) var(--tw-backdrop-contrast, ) var(--tw-backdrop-grayscale, ) var(--tw-backdrop-hue-rotate, ) var(--tw-backdrop-invert, ) var(--tw-backdrop-opacity, ) var(--tw-backdrop-saturate, ) var(--tw-backdrop-sepia, );
  }

  .backdrop-saturate-150 {
    --tw-backdrop-saturate: saturate(150%);
    backdrop-filter: var(--tw-backdrop-blur, ) var(--tw-backdrop-brightness, ) var(--tw-backdrop-contrast, ) var(--tw-backdrop-grayscale, ) var(--tw-backdrop-hue-rotate, ) var(--tw-backdrop-invert, ) var(--tw-backdrop-opacity, ) var(--tw-backdrop-saturate, ) var(--tw-backdrop-sepia, );
  }

  .transition {
    transition-property: color, background-color, border-color, outline-color, text-decoration-color, fill, stroke, --tw-gradient-from, --tw-gradient-via, --tw-gradient-to, opacity, box-shadow, transform, translate, scale, rotate, filter, -webkit-backdrop-filter, backdrop-filter;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }

  .transition-\[color\,box-shadow\] {
    transition-property: color, box-shadow;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }

  .transition-\[color\,opacity\] {
    transition-property: color, opacity;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }

  .transition-\[left\,right\,width\] {
    transition-property: left, right, width;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }

  .transition-\[margin\,opacity\] {
    transition-property: margin, opacity;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }

  .transition-\[opacity\,transform\] {
    transition-property: opacity, transform;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }

  .transition-\[transform\,background-color\,color\] {
    transition-property: transform, background-color, color;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }

  .transition-\[transform\,color\,left\,opacity\] {
    transition-property: transform, color, left, opacity;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }

  .transition-\[width\,height\,padding\] {
    transition-property: width, height, padding;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }

  .transition-\[width\,height\] {
    transition-property: width, height;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }

  .transition-\[width\] {
    transition-property: width;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }

  .transition-all {
    transition-property: all;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }

  .transition-background {
    transition-property: background;
    transition-duration: .25s;
    transition-timing-function: ease;
  }

  .transition-colors {
    transition-property: color, background-color, border-color, outline-color, text-decoration-color, fill, stroke, --tw-gradient-from, --tw-gradient-via, --tw-gradient-to;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }

  .transition-colors-opacity {
    transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity;
    transition-duration: .25s;
    transition-timing-function: ease;
  }

  .transition-height {
    transition-property: height;
    transition-duration: .25s;
    transition-timing-function: ease;
  }

  .transition-left {
    transition-property: left;
    transition-duration: .25s;
    transition-timing-function: ease;
  }

  .transition-opacity {
    transition-property: opacity;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }

  .transition-shadow {
    transition-property: box-shadow;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }

  .transition-size {
    transition-property: width, height;
    transition-duration: .25s;
    transition-timing-function: ease;
  }

  .transition-transform {
    transition-property: transform, translate, scale, rotate;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }

  .transition-transform-background {
    transition-property: transform, scale, background;
    transition-duration: .25s;
    transition-timing-function: ease;
  }

  .transition-transform-colors {
    transition-property: transform, scale, color, background, background-color, border-color, text-decoration-color, fill, stroke;
    transition-duration: .25s;
    transition-timing-function: ease;
  }

  .transition-transform-colors-opacity {
    transition-property: transform, scale, color, background, background-color, border-color, text-decoration-color, fill, stroke, opacity;
    transition-duration: .25s;
    transition-timing-function: ease;
  }

  .transition-transform-opacity {
    transition-property: transform, scale, opacity;
    transition-duration: .25s;
    transition-timing-function: ease;
  }

  .transition-width {
    transition-property: width;
    transition-duration: .25s;
    transition-timing-function: ease;
  }

  .\!transition-none {
    transition-property: none !important;
  }

  .transition-none {
    transition-property: none;
  }

  .\!duration-100 {
    --tw-duration: .1s !important;
    transition-duration: .1s !important;
  }

  .\!duration-150 {
    --tw-duration: .15s !important;
    transition-duration: .15s !important;
  }

  .\!duration-200 {
    --tw-duration: .2s !important;
    transition-duration: .2s !important;
  }

  .\!duration-250 {
    --tw-duration: .25s !important;
    transition-duration: .25s !important;
  }

  .\!duration-300 {
    --tw-duration: .3s !important;
    transition-duration: .3s !important;
  }

  .\!duration-500 {
    --tw-duration: .5s !important;
    transition-duration: .5s !important;
  }

  .duration-150 {
    --tw-duration: .15s;
    transition-duration: .15s;
  }

  .duration-200 {
    --tw-duration: .2s;
    transition-duration: .2s;
  }

  .duration-300 {
    --tw-duration: .3s;
    transition-duration: .3s;
  }

  .duration-1000 {
    --tw-duration: 1s;
    transition-duration: 1s;
  }

  .\!ease-out {
    --tw-ease: var(--ease-out) !important;
    transition-timing-function: var(--ease-out) !important;
  }

  .\!ease-soft-spring {
    --tw-ease: cubic-bezier(.155, 1.105, .295, 1.12) !important;
    transition-timing-function: cubic-bezier(.155, 1.105, .295, 1.12) !important;
  }

  .ease-in {
    --tw-ease: var(--ease-in);
    transition-timing-function: var(--ease-in);
  }

  .ease-in-out {
    --tw-ease: var(--ease-in-out);
    transition-timing-function: var(--ease-in-out);
  }

  .ease-linear {
    --tw-ease: linear;
    transition-timing-function: linear;
  }

  .ease-out {
    --tw-ease: var(--ease-out);
    transition-timing-function: var(--ease-out);
  }

  .will-change-auto {
    will-change: auto;
  }

  .will-change-transform {
    will-change: transform;
  }

  .\!outline-none {
    --tw-outline-style: none !important;
    outline-style: none !important;
  }

  .outline-none {
    --tw-outline-style: none;
    outline-style: none;
  }

  .select-none {
    -webkit-user-select: none;
    user-select: none;
  }

  .slide-in-from-bottom-5 {
    --tw-enter-translate-y: calc(.05 * 100%);
  }

  .\[--picker-height\:224px\] {
    --picker-height: 224px;
  }

  .\[--scale-enter\:100\%\] {
    --scale-enter: 100%;
  }

  .\[--scale-exit\:100\%\] {
    --scale-exit: 100%;
  }

  .\[--scroll-shadow-size\:100px\] {
    --scroll-shadow-size: 100px;
  }

  .\[--slide-enter\:0px\] {
    --slide-enter: 0px;
  }

  .\[--slide-exit\:80px\] {
    --slide-exit: 80px;
  }

  .\[-webkit-mask\:radial-gradient\(closest-side\,rgba\(0\,0\,0\,0\.0\)calc\(100\%-3px\)\,rgba\(0\,0\,0\,1\)calc\(100\%-3px\)\)\] {
    -webkit-mask: radial-gradient(closest-side, #0000 calc(100% - 3px), #000 calc(100% - 3px));
  }

  .\[animation-duration\:1s\] {
    animation-duration: 1s;
  }

  .fade-in, .fade-in-0 {
    --tw-enter-opacity: 0;
  }

  .fade-out {
    --tw-exit-opacity: 0;
  }

  .input-search-cancel-button-none::-webkit-search-cancel-button {
    -webkit-appearance: none;
  }

  .running {
    animation-play-state: running;
  }

  .spinner-dot-animation {
    animation-delay: calc(.25s * var(--dot-index));
  }

  .spinner-dot-blink-animation {
    animation-delay: calc(.2s * var(--dot-index));
  }

  .tap-highlight-transparent {
    -webkit-tap-highlight-color: transparent;
  }

  .text-fill-inherit {
    -webkit-text-fill-color: inherit;
  }

  .zoom-in {
    --tw-enter-scale: 0;
  }

  .zoom-in-95 {
    --tw-enter-scale: .95;
  }

  .zoom-out {
    --tw-exit-scale: 0;
  }

  .group-focus-within\/menu-item\:opacity-100:is(:where(.group\/menu-item):focus-within *) {
    opacity: 1;
  }

  @media (hover: hover) {
    .group-hover\:pointer-events-auto:is(:where(.group):hover *) {
      pointer-events: auto;
    }
  }

  @media (hover: hover) {
    .group-hover\:block:is(:where(.group):hover *) {
      display: block;
    }
  }

  @media (hover: hover) {
    .group-hover\:hidden:is(:where(.group):hover *) {
      display: none;
    }
  }

  @media (hover: hover) {
    .group-hover\:scale-110:is(:where(.group):hover *) {
      --tw-scale-x: 110%;
      --tw-scale-y: 110%;
      --tw-scale-z: 110%;
      scale: var(--tw-scale-x) var(--tw-scale-y);
    }
  }

  @media (hover: hover) {
    .group-hover\:border-current:is(:where(.group):hover *) {
      border-color: currentColor;
    }
  }

  @media (hover: hover) {
    .group-hover\:text-current:is(:where(.group):hover *) {
      color: currentColor;
    }
  }

  @media (hover: hover) {
    .group-hover\:opacity-100:is(:where(.group):hover *) {
      opacity: 1;
    }
  }

  @media (hover: hover) {
    .group-hover\/menu-item\:opacity-100:is(:where(.group\/menu-item):hover *) {
      opacity: 1;
    }
  }

  @media (hover: hover) {
    .group-hover\/sidebar\:underline:is(:where(.group\/sidebar):hover *) {
      text-decoration-line: underline;
    }
  }

  .group-has-data-\[collapsible\=icon\]\/sidebar-wrapper\:h-\(--header-height\):is(:where(.group\/sidebar-wrapper):has([data-collapsible="icon"]) *) {
    height: var(--header-height);
  }

  .group-has-data-\[sidebar\=menu-action\]\/menu-item\:pr-8:is(:where(.group\/menu-item):has([data-sidebar="menu-action"]) *) {
    padding-right: calc(var(--spacing) * 8);
  }

  .group-data-\[collapsible\=icon\]\:-mt-8:is(:where(.group)[data-collapsible="icon"] *) {
    margin-top: calc(var(--spacing) * -8);
  }

  .group-data-\[collapsible\=icon\]\:block:is(:where(.group)[data-collapsible="icon"] *) {
    display: block;
  }

  .group-data-\[collapsible\=icon\]\:hidden:is(:where(.group)[data-collapsible="icon"] *) {
    display: none;
  }

  .group-data-\[collapsible\=icon\]\:size-8\!:is(:where(.group)[data-collapsible="icon"] *) {
    width: calc(var(--spacing) * 8) !important;
    height: calc(var(--spacing) * 8) !important;
  }

  .group-data-\[collapsible\=icon\]\:w-\(--sidebar-width-icon\):is(:where(.group)[data-collapsible="icon"] *) {
    width: var(--sidebar-width-icon);
  }

  .group-data-\[collapsible\=icon\]\:w-\[calc\(var\(--sidebar-width-icon\)\+\(--spacing\(4\)\)\)\]:is(:where(.group)[data-collapsible="icon"] *) {
    width: calc(var(--sidebar-width-icon)  + (calc(var(--spacing) * 4)));
  }

  .group-data-\[collapsible\=icon\]\:w-\[calc\(var\(--sidebar-width-icon\)\+\(--spacing\(4\)\)\+2px\)\]:is(:where(.group)[data-collapsible="icon"] *) {
    width: calc(var(--sidebar-width-icon)  + (calc(var(--spacing) * 4))  + 2px);
  }

  .group-data-\[collapsible\=icon\]\:overflow-hidden:is(:where(.group)[data-collapsible="icon"] *) {
    overflow: hidden;
  }

  .group-data-\[collapsible\=icon\]\:p-0\!:is(:where(.group)[data-collapsible="icon"] *) {
    padding: calc(var(--spacing) * 0) !important;
  }

  .group-data-\[collapsible\=icon\]\:p-2\!:is(:where(.group)[data-collapsible="icon"] *) {
    padding: calc(var(--spacing) * 2) !important;
  }

  .group-data-\[collapsible\=icon\]\:opacity-0:is(:where(.group)[data-collapsible="icon"] *) {
    opacity: 0;
  }

  .group-data-\[collapsible\=offcanvas\]\:right-\[calc\(var\(--sidebar-width\)\*-1\)\]:is(:where(.group)[data-collapsible="offcanvas"] *) {
    right: calc(var(--sidebar-width) * -1);
  }

  .group-data-\[collapsible\=offcanvas\]\:left-\[calc\(var\(--sidebar-width\)\*-1\)\]:is(:where(.group)[data-collapsible="offcanvas"] *) {
    left: calc(var(--sidebar-width) * -1);
  }

  .group-data-\[collapsible\=offcanvas\]\:w-0:is(:where(.group)[data-collapsible="offcanvas"] *) {
    width: calc(var(--spacing) * 0);
  }

  .group-data-\[collapsible\=offcanvas\]\:translate-x-0:is(:where(.group)[data-collapsible="offcanvas"] *) {
    --tw-translate-x: calc(var(--spacing) * 0);
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }

  .group-data-\[copied\=true\]\:scale-50:is(:where(.group)[data-copied="true"] *) {
    --tw-scale-x: 50%;
    --tw-scale-y: 50%;
    --tw-scale-z: 50%;
    scale: var(--tw-scale-x) var(--tw-scale-y);
  }

  .group-data-\[copied\=true\]\:scale-100:is(:where(.group)[data-copied="true"] *) {
    --tw-scale-x: 100%;
    --tw-scale-y: 100%;
    --tw-scale-z: 100%;
    scale: var(--tw-scale-x) var(--tw-scale-y);
  }

  .group-data-\[copied\=true\]\:opacity-0:is(:where(.group)[data-copied="true"] *) {
    opacity: 0;
  }

  .group-data-\[copied\=true\]\:opacity-100:is(:where(.group)[data-copied="true"] *) {
    opacity: 1;
  }

  .group-data-\[disabled\=true\]\:pointer-events-none:is(:where(.group)[data-disabled="true"] *) {
    pointer-events: none;
  }

  .group-data-\[disabled\=true\]\:opacity-50:is(:where(.group)[data-disabled="true"] *) {
    opacity: .5;
  }

  .group-data-\[disabled\=true\]\/tr\:cursor-not-allowed:is(:where(.group\/tr)[data-disabled="true"] *) {
    cursor: not-allowed;
  }

  .group-data-\[disabled\=true\]\/tr\:text-foreground-300:is(:where(.group\/tr)[data-disabled="true"] *) {
    color: hsl(var(--heroui-foreground-300) / 1);
  }

  .group-data-\[filled-within\=true\]\:pointer-events-auto:is(:where(.group)[data-filled-within="true"] *) {
    pointer-events: auto;
  }

  .group-data-\[filled-within\=true\]\:start-0:is(:where(.group)[data-filled-within="true"] *) {
    inset-inline-start: calc(var(--spacing) * 0);
  }

  .group-data-\[filled-within\=true\]\:scale-85:is(:where(.group)[data-filled-within="true"] *) {
    --tw-scale-x: .85;
    --tw-scale-y: .85;
    --tw-scale-z: .85;
    scale: var(--tw-scale-x) var(--tw-scale-y);
  }

  .group-data-\[filled-within\=true\]\:text-default-600:is(:where(.group)[data-filled-within="true"] *) {
    color: hsl(var(--heroui-default-600) / 1);
  }

  .group-data-\[filled-within\=true\]\:text-foreground:is(:where(.group)[data-filled-within="true"] *) {
    color: var(--foreground);
  }

  .group-data-\[filled\=true\]\:start-0:is(:where(.group)[data-filled="true"] *) {
    inset-inline-start: calc(var(--spacing) * 0);
  }

  .group-data-\[filled\=true\]\:scale-85:is(:where(.group)[data-filled="true"] *) {
    --tw-scale-x: .85;
    --tw-scale-y: .85;
    --tw-scale-z: .85;
    scale: var(--tw-scale-x) var(--tw-scale-y);
  }

  .group-data-\[filled\=true\]\:text-default-600:is(:where(.group)[data-filled="true"] *) {
    color: hsl(var(--heroui-default-600) / 1);
  }

  .group-data-\[filled\=true\]\:text-foreground:is(:where(.group)[data-filled="true"] *) {
    color: var(--foreground);
  }

  .group-data-\[focus-visible\=true\]\:z-10:is(:where(.group)[data-focus-visible="true"] *) {
    z-index: 10;
  }

  .group-data-\[focus-visible\=true\]\:block:is(:where(.group)[data-focus-visible="true"] *) {
    display: block;
  }

  .group-data-\[focus-visible\=true\]\:hidden:is(:where(.group)[data-focus-visible="true"] *) {
    display: none;
  }

  .group-data-\[focus-visible\=true\]\:ring-2:is(:where(.group)[data-focus-visible="true"] *) {
    --tw-ring-shadow: var(--tw-ring-inset, ) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .group-data-\[focus-visible\=true\]\:ring-focus:is(:where(.group)[data-focus-visible="true"] *) {
    --tw-ring-color: hsl(var(--heroui-focus) / 1);
  }

  .group-data-\[focus-visible\=true\]\:ring-offset-2:is(:where(.group)[data-focus-visible="true"] *) {
    --tw-ring-offset-width: 2px;
    --tw-ring-offset-shadow: var(--tw-ring-inset, ) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  }

  .group-data-\[focus-visible\=true\]\:ring-offset-background:is(:where(.group)[data-focus-visible="true"] *) {
    --tw-ring-offset-color: var(--background);
  }

  .group-data-\[focus\=true\]\:\!border-danger:is(:where(.group)[data-focus="true"] *) {
    border-color: hsl(var(--heroui-danger) / 1) !important;
  }

  .group-data-\[focus\=true\]\:border-danger:is(:where(.group)[data-focus="true"] *) {
    border-color: hsl(var(--heroui-danger) / 1);
  }

  .group-data-\[focus\=true\]\:border-default-foreground:is(:where(.group)[data-focus="true"] *) {
    border-color: hsl(var(--heroui-default-foreground) / 1);
  }

  .group-data-\[focus\=true\]\:border-primary:is(:where(.group)[data-focus="true"] *) {
    border-color: var(--primary);
  }

  .group-data-\[focus\=true\]\:border-secondary:is(:where(.group)[data-focus="true"] *) {
    border-color: var(--secondary);
  }

  .group-data-\[focus\=true\]\:border-success:is(:where(.group)[data-focus="true"] *) {
    border-color: hsl(var(--heroui-success) / 1);
  }

  .group-data-\[focus\=true\]\:border-warning:is(:where(.group)[data-focus="true"] *) {
    border-color: hsl(var(--heroui-warning) / 1);
  }

  .group-data-\[focus\=true\]\:\!bg-danger-50:is(:where(.group)[data-focus="true"] *) {
    background-color: hsl(var(--heroui-danger-50) / 1) !important;
  }

  .group-data-\[focus\=true\]\:bg-danger-50:is(:where(.group)[data-focus="true"] *) {
    background-color: hsl(var(--heroui-danger-50) / 1);
  }

  .group-data-\[focus\=true\]\:bg-default-100:is(:where(.group)[data-focus="true"] *) {
    background-color: hsl(var(--heroui-default-100) / 1);
  }

  .group-data-\[focus\=true\]\:bg-default-200:is(:where(.group)[data-focus="true"] *) {
    background-color: hsl(var(--heroui-default-200) / 1);
  }

  .group-data-\[focus\=true\]\:bg-primary-50:is(:where(.group)[data-focus="true"] *) {
    background-color: hsl(var(--heroui-primary-50) / 1);
  }

  .group-data-\[focus\=true\]\:bg-secondary-50:is(:where(.group)[data-focus="true"] *) {
    background-color: hsl(var(--heroui-secondary-50) / 1);
  }

  .group-data-\[focus\=true\]\:bg-success-50:is(:where(.group)[data-focus="true"] *) {
    background-color: hsl(var(--heroui-success-50) / 1);
  }

  .group-data-\[focus\=true\]\:bg-warning-50:is(:where(.group)[data-focus="true"] *) {
    background-color: hsl(var(--heroui-warning-50) / 1);
  }

  .group-data-\[has-helper\=true\]\:flex:is(:where(.group)[data-has-helper="true"] *) {
    display: flex;
  }

  .group-data-\[has-helper\=true\]\:pt-2:is(:where(.group)[data-has-helper="true"] *) {
    padding-top: calc(var(--spacing) * 2);
  }

  .group-data-\[has-helper\=true\]\:pt-3:is(:where(.group)[data-has-helper="true"] *) {
    padding-top: calc(var(--spacing) * 3);
  }

  .group-data-\[has-helper\=true\]\:pt-4:is(:where(.group)[data-has-helper="true"] *) {
    padding-top: calc(var(--spacing) * 4);
  }

  .group-data-\[has-label-outside\=true\]\:pointer-events-auto:is(:where(.group)[data-has-label-outside="true"] *) {
    pointer-events: auto;
  }

  .group-data-\[has-label\=true\]\:items-end:is(:where(.group)[data-has-label="true"] *) {
    align-items: flex-end;
  }

  .group-data-\[has-label\=true\]\:items-start:is(:where(.group)[data-has-label="true"] *) {
    align-items: flex-start;
  }

  .group-data-\[has-label\=true\]\:pt-4:is(:where(.group)[data-has-label="true"] *) {
    padding-top: calc(var(--spacing) * 4);
  }

  .group-data-\[has-label\=true\]\:pt-5:is(:where(.group)[data-has-label="true"] *) {
    padding-top: calc(var(--spacing) * 5);
  }

  .group-data-\[has-multiple-months\=true\]\:flex-row:is(:where(.group)[data-has-multiple-months="true"] *) {
    flex-direction: row;
  }

  .group-data-\[has-value\=true\]\:pointer-events-auto:is(:where(.group)[data-has-value="true"] *) {
    pointer-events: auto;
  }

  .group-data-\[has-value\=true\]\:block:is(:where(.group)[data-has-value="true"] *) {
    display: block;
  }

  .group-data-\[has-value\=true\]\:scale-100:is(:where(.group)[data-has-value="true"] *) {
    --tw-scale-x: 100%;
    --tw-scale-y: 100%;
    --tw-scale-z: 100%;
    scale: var(--tw-scale-x) var(--tw-scale-y);
  }

  .group-data-\[has-value\=true\]\:text-default-foreground:is(:where(.group)[data-has-value="true"] *) {
    color: hsl(var(--heroui-default-foreground) / 1);
  }

  .group-data-\[has-value\=true\]\:text-foreground:is(:where(.group)[data-has-value="true"] *) {
    color: var(--foreground);
  }

  .group-data-\[has-value\=true\]\:opacity-70:is(:where(.group)[data-has-value="true"] *) {
    opacity: .7;
  }

  .group-data-\[hover-unselected\=true\]\:bg-default-100:is(:where(.group)[data-hover-unselected="true"] *) {
    background-color: hsl(var(--heroui-default-100) / 1);
  }

  .group-data-\[hover\=true\]\/th\:opacity-100:is(:where(.group\/th)[data-hover="true"] *) {
    opacity: 1;
  }

  .group-data-\[invalid\=true\]\:border-danger:is(:where(.group)[data-invalid="true"] *) {
    border-color: hsl(var(--heroui-danger) / 1);
  }

  .group-data-\[invalid\=true\]\:bg-danger-50:is(:where(.group)[data-invalid="true"] *) {
    background-color: hsl(var(--heroui-danger-50) / 1);
  }

  .group-data-\[invalid\=true\]\:text-danger:is(:where(.group)[data-invalid="true"] *) {
    color: hsl(var(--heroui-danger) / 1);
  }

  .group-data-\[loaded\=true\]\:opacity-100:is(:where(.group)[data-loaded="true"] *) {
    opacity: 1;
  }

  .group-data-\[pressed\=true\]\:w-5:is(:where(.group)[data-pressed="true"] *) {
    width: calc(var(--spacing) * 5);
  }

  .group-data-\[pressed\=true\]\:w-6:is(:where(.group)[data-pressed="true"] *) {
    width: calc(var(--spacing) * 6);
  }

  .group-data-\[pressed\=true\]\:w-7:is(:where(.group)[data-pressed="true"] *) {
    width: calc(var(--spacing) * 7);
  }

  .group-data-\[pressed\=true\]\:scale-95:is(:where(.group)[data-pressed="true"] *) {
    --tw-scale-x: 95%;
    --tw-scale-y: 95%;
    --tw-scale-z: 95%;
    scale: var(--tw-scale-x) var(--tw-scale-y);
  }

  .group-data-\[pressed\=true\]\:opacity-70:is(:where(.group)[data-pressed="true"] *) {
    opacity: .7;
  }

  .group-data-\[selected\]\:group-data-\[pressed\]\:ml-3:is(:where(.group)[data-selected] *):is(:where(.group)[data-pressed] *) {
    margin-left: calc(var(--spacing) * 3);
  }

  .group-data-\[selected\]\:group-data-\[pressed\]\:ml-4:is(:where(.group)[data-selected] *):is(:where(.group)[data-pressed] *) {
    margin-left: calc(var(--spacing) * 4);
  }

  .group-data-\[selected\]\:group-data-\[pressed\]\:ml-5:is(:where(.group)[data-selected] *):is(:where(.group)[data-pressed] *) {
    margin-left: calc(var(--spacing) * 5);
  }

  .group-data-\[selected\=true\]\:ms-4:is(:where(.group)[data-selected="true"] *) {
    margin-inline-start: calc(var(--spacing) * 4);
  }

  .group-data-\[selected\=true\]\:ms-5:is(:where(.group)[data-selected="true"] *) {
    margin-inline-start: calc(var(--spacing) * 5);
  }

  .group-data-\[selected\=true\]\:ms-6:is(:where(.group)[data-selected="true"] *) {
    margin-inline-start: calc(var(--spacing) * 6);
  }

  .group-data-\[selected\=true\]\:translate-x-3:is(:where(.group)[data-selected="true"] *) {
    --tw-translate-x: calc(var(--spacing) * 3);
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }

  .group-data-\[selected\=true\]\:scale-100:is(:where(.group)[data-selected="true"] *) {
    --tw-scale-x: 100%;
    --tw-scale-y: 100%;
    --tw-scale-z: 100%;
    scale: var(--tw-scale-x) var(--tw-scale-y);
  }

  .group-data-\[selected\=true\]\:border-danger:is(:where(.group)[data-selected="true"] *) {
    border-color: hsl(var(--heroui-danger) / 1);
  }

  .group-data-\[selected\=true\]\:border-default-500:is(:where(.group)[data-selected="true"] *) {
    border-color: hsl(var(--heroui-default-500) / 1);
  }

  .group-data-\[selected\=true\]\:border-primary:is(:where(.group)[data-selected="true"] *) {
    border-color: var(--primary);
  }

  .group-data-\[selected\=true\]\:border-secondary:is(:where(.group)[data-selected="true"] *) {
    border-color: var(--secondary);
  }

  .group-data-\[selected\=true\]\:border-success:is(:where(.group)[data-selected="true"] *) {
    border-color: hsl(var(--heroui-success) / 1);
  }

  .group-data-\[selected\=true\]\:border-warning:is(:where(.group)[data-selected="true"] *) {
    border-color: hsl(var(--heroui-warning) / 1);
  }

  .group-data-\[selected\=true\]\:bg-danger:is(:where(.group)[data-selected="true"] *) {
    background-color: hsl(var(--heroui-danger) / 1);
  }

  .group-data-\[selected\=true\]\:bg-default-400:is(:where(.group)[data-selected="true"] *) {
    background-color: hsl(var(--heroui-default-400) / 1);
  }

  .group-data-\[selected\=true\]\:bg-primary:is(:where(.group)[data-selected="true"] *) {
    background-color: var(--primary);
  }

  .group-data-\[selected\=true\]\:bg-secondary:is(:where(.group)[data-selected="true"] *) {
    background-color: var(--secondary);
  }

  .group-data-\[selected\=true\]\:bg-success:is(:where(.group)[data-selected="true"] *) {
    background-color: hsl(var(--heroui-success) / 1);
  }

  .group-data-\[selected\=true\]\:bg-warning:is(:where(.group)[data-selected="true"] *) {
    background-color: hsl(var(--heroui-warning) / 1);
  }

  .group-data-\[selected\=true\]\:text-danger:is(:where(.group)[data-selected="true"] *) {
    color: hsl(var(--heroui-danger) / 1);
  }

  .group-data-\[selected\=true\]\:text-danger-foreground:is(:where(.group)[data-selected="true"] *) {
    color: hsl(var(--heroui-danger-foreground) / 1);
  }

  .group-data-\[selected\=true\]\:text-default-foreground:is(:where(.group)[data-selected="true"] *) {
    color: hsl(var(--heroui-default-foreground) / 1);
  }

  .group-data-\[selected\=true\]\:text-foreground:is(:where(.group)[data-selected="true"] *) {
    color: var(--foreground);
  }

  .group-data-\[selected\=true\]\:text-primary:is(:where(.group)[data-selected="true"] *) {
    color: var(--primary);
  }

  .group-data-\[selected\=true\]\:text-primary-foreground:is(:where(.group)[data-selected="true"] *) {
    color: var(--primary-foreground);
  }

  .group-data-\[selected\=true\]\:text-secondary:is(:where(.group)[data-selected="true"] *) {
    color: var(--secondary);
  }

  .group-data-\[selected\=true\]\:text-secondary-foreground:is(:where(.group)[data-selected="true"] *) {
    color: var(--secondary-foreground);
  }

  .group-data-\[selected\=true\]\:text-success:is(:where(.group)[data-selected="true"] *) {
    color: hsl(var(--heroui-success) / 1);
  }

  .group-data-\[selected\=true\]\:text-success-foreground:is(:where(.group)[data-selected="true"] *) {
    color: hsl(var(--heroui-success-foreground) / 1);
  }

  .group-data-\[selected\=true\]\:text-warning:is(:where(.group)[data-selected="true"] *) {
    color: hsl(var(--heroui-warning) / 1);
  }

  .group-data-\[selected\=true\]\:text-warning-foreground:is(:where(.group)[data-selected="true"] *) {
    color: hsl(var(--heroui-warning-foreground) / 1);
  }

  .group-data-\[selected\=true\]\:opacity-0:is(:where(.group)[data-selected="true"] *) {
    opacity: 0;
  }

  .group-data-\[selected\=true\]\:opacity-60:is(:where(.group)[data-selected="true"] *) {
    opacity: .6;
  }

  .group-data-\[selected\=true\]\:opacity-100:is(:where(.group)[data-selected="true"] *) {
    opacity: 1;
  }

  .group-data-\[side\=left\]\:-right-4:is(:where(.group)[data-side="left"] *) {
    right: calc(var(--spacing) * -4);
  }

  .group-data-\[side\=left\]\:border-r:is(:where(.group)[data-side="left"] *) {
    border-right-style: var(--tw-border-style);
    border-right-width: 1px;
  }

  .group-data-\[side\=right\]\:left-0:is(:where(.group)[data-side="right"] *) {
    left: calc(var(--spacing) * 0);
  }

  .group-data-\[side\=right\]\:rotate-180:is(:where(.group)[data-side="right"] *) {
    rotate: 180deg;
  }

  .group-data-\[side\=right\]\:border-l:is(:where(.group)[data-side="right"] *) {
    border-left-style: var(--tw-border-style);
    border-left-width: 1px;
  }

  .group-data-\[state\=open\]\:rotate-180:is(:where(.group)[data-state="open"] *) {
    rotate: 180deg;
  }

  .group-data-\[variant\=floating\]\:rounded-lg:is(:where(.group)[data-variant="floating"] *) {
    border-radius: var(--radius);
  }

  .group-data-\[variant\=floating\]\:border:is(:where(.group)[data-variant="floating"] *) {
    border-style: var(--tw-border-style);
    border-width: 1px;
  }

  .group-data-\[variant\=floating\]\:border-sidebar-border:is(:where(.group)[data-variant="floating"] *) {
    border-color: var(--sidebar-border);
  }

  .group-data-\[variant\=floating\]\:shadow-sm:is(:where(.group)[data-variant="floating"] *) {
    --tw-shadow: 0 1px 3px 0 var(--tw-shadow-color, #0000001a), 0 1px 2px -1px var(--tw-shadow-color, #0000001a);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .group-data-\[vaul-drawer-direction\=bottom\]\/drawer-content\:block:is(:where(.group\/drawer-content)[data-vaul-drawer-direction="bottom"] *) {
    display: block;
  }

  .group-data-\[viewport\=false\]\/navigation-menu\:top-full:is(:where(.group\/navigation-menu)[data-viewport="false"] *) {
    top: 100%;
  }

  .group-data-\[viewport\=false\]\/navigation-menu\:mt-1\.5:is(:where(.group\/navigation-menu)[data-viewport="false"] *) {
    margin-top: calc(var(--spacing) * 1.5);
  }

  .group-data-\[viewport\=false\]\/navigation-menu\:overflow-hidden:is(:where(.group\/navigation-menu)[data-viewport="false"] *) {
    overflow: hidden;
  }

  .group-data-\[viewport\=false\]\/navigation-menu\:rounded-md:is(:where(.group\/navigation-menu)[data-viewport="false"] *) {
    border-radius: calc(var(--radius)  - 2px);
  }

  .group-data-\[viewport\=false\]\/navigation-menu\:border:is(:where(.group\/navigation-menu)[data-viewport="false"] *) {
    border-style: var(--tw-border-style);
    border-width: 1px;
  }

  .group-data-\[viewport\=false\]\/navigation-menu\:bg-popover:is(:where(.group\/navigation-menu)[data-viewport="false"] *) {
    background-color: var(--popover);
  }

  .group-data-\[viewport\=false\]\/navigation-menu\:text-popover-foreground:is(:where(.group\/navigation-menu)[data-viewport="false"] *) {
    color: var(--popover-foreground);
  }

  .group-data-\[viewport\=false\]\/navigation-menu\:shadow:is(:where(.group\/navigation-menu)[data-viewport="false"] *) {
    --tw-shadow: 0 1px 3px 0 var(--tw-shadow-color, #0000001a), 0 1px 2px -1px var(--tw-shadow-color, #0000001a);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .group-data-\[viewport\=false\]\/navigation-menu\:duration-200:is(:where(.group\/navigation-menu)[data-viewport="false"] *) {
    --tw-duration: .2s;
    transition-duration: .2s;
  }

  @media (hover: hover) {
    .peer-hover\/menu-button\:text-sidebar-accent-foreground:is(:where(.peer\/menu-button):hover ~ *) {
      color: var(--sidebar-accent-foreground);
    }
  }

  .peer-disabled\:cursor-not-allowed:is(:where(.peer):disabled ~ *) {
    cursor: not-allowed;
  }

  .peer-disabled\:opacity-50:is(:where(.peer):disabled ~ *) {
    opacity: .5;
  }

  .peer-disabled\:opacity-70:is(:where(.peer):disabled ~ *) {
    opacity: .7;
  }

  .peer-data-\[active\=true\]\/menu-button\:text-sidebar-accent-foreground:is(:where(.peer\/menu-button)[data-active="true"] ~ *) {
    color: var(--sidebar-accent-foreground);
  }

  .peer-data-\[filled\=true\]\:pointer-events-auto:is(:where(.peer)[data-filled="true"] ~ *) {
    pointer-events: auto;
  }

  .peer-data-\[filled\=true\]\:block:is(:where(.peer)[data-filled="true"] ~ *) {
    display: block;
  }

  .peer-data-\[filled\=true\]\:scale-100:is(:where(.peer)[data-filled="true"] ~ *) {
    --tw-scale-x: 100%;
    --tw-scale-y: 100%;
    --tw-scale-z: 100%;
    scale: var(--tw-scale-x) var(--tw-scale-y);
  }

  .peer-data-\[filled\=true\]\:opacity-70:is(:where(.peer)[data-filled="true"] ~ *) {
    opacity: .7;
  }

  .peer-data-\[size\=default\]\/menu-button\:top-1\.5:is(:where(.peer\/menu-button)[data-size="default"] ~ *) {
    top: calc(var(--spacing) * 1.5);
  }

  .peer-data-\[size\=lg\]\/menu-button\:top-2\.5:is(:where(.peer\/menu-button)[data-size="lg"] ~ *) {
    top: calc(var(--spacing) * 2.5);
  }

  .peer-data-\[size\=sm\]\/menu-button\:top-1:is(:where(.peer\/menu-button)[data-size="sm"] ~ *) {
    top: calc(var(--spacing) * 1);
  }

  .selection\:bg-primary ::selection, .selection\:bg-primary::selection {
    background-color: var(--primary);
  }

  .selection\:text-primary-foreground ::selection, .selection\:text-primary-foreground::selection {
    color: var(--primary-foreground);
  }

  .file\:inline-flex::file-selector-button {
    display: inline-flex;
  }

  .file\:h-7::file-selector-button {
    height: calc(var(--spacing) * 7);
  }

  .file\:cursor-pointer::file-selector-button {
    cursor: pointer;
  }

  .file\:border-0::file-selector-button {
    border-style: var(--tw-border-style);
    border-width: 0;
  }

  .file\:bg-transparent::file-selector-button {
    background-color: #0000;
  }

  .file\:text-sm::file-selector-button {
    font-size: var(--text-sm);
    line-height: var(--tw-leading, var(--text-sm--line-height));
  }

  .file\:font-medium::file-selector-button {
    --tw-font-weight: var(--font-weight-medium);
    font-weight: var(--font-weight-medium);
  }

  .file\:text-foreground::file-selector-button {
    color: var(--foreground);
  }

  .placeholder\:text-danger::placeholder {
    color: hsl(var(--heroui-danger) / 1);
  }

  .placeholder\:text-foreground-500::placeholder {
    color: hsl(var(--heroui-foreground-500) / 1);
  }

  .placeholder\:text-muted-foreground::placeholder {
    color: var(--muted-foreground);
  }

  .placeholder\:text-primary::placeholder {
    color: var(--primary);
  }

  .placeholder\:text-secondary::placeholder {
    color: var(--secondary);
  }

  .placeholder\:text-success-600::placeholder {
    color: hsl(var(--heroui-success-600) / 1);
  }

  .placeholder\:text-warning-600::placeholder {
    color: hsl(var(--heroui-warning-600) / 1);
  }

  .before\:pointer-events-auto:before {
    content: var(--tw-content);
    pointer-events: auto;
  }

  .before\:absolute:before {
    content: var(--tw-content);
    position: absolute;
  }

  .before\:inset-0:before {
    content: var(--tw-content);
    inset: calc(var(--spacing) * 0);
  }

  .before\:top-\[calc\(-1\*var\(--top-extension\,16px\)\)\]:before {
    content: var(--tw-content);
    top: calc(-1 * var(--top-extension, 16px));
  }

  .before\:right-0:before {
    content: var(--tw-content);
    right: calc(var(--spacing) * 0);
  }

  .before\:left-0:before {
    content: var(--tw-content);
    left: calc(var(--spacing) * 0);
  }

  .before\:z-0:before {
    content: var(--tw-content);
    z-index: 0;
  }

  .before\:z-\[-1\]:before {
    content: var(--tw-content);
    z-index: -1;
  }

  .before\:box-border:before {
    content: var(--tw-content);
    box-sizing: border-box;
  }

  .before\:block:before {
    content: var(--tw-content);
    display: block;
  }

  .before\:hidden:before {
    content: var(--tw-content);
    display: none;
  }

  .before\:h-0\.5:before {
    content: var(--tw-content);
    height: calc(var(--spacing) * .5);
  }

  .before\:h-2\.5:before {
    content: var(--tw-content);
    height: calc(var(--spacing) * 2.5);
  }

  .before\:h-4:before {
    content: var(--tw-content);
    height: calc(var(--spacing) * 4);
  }

  .before\:h-6:before {
    content: var(--tw-content);
    height: calc(var(--spacing) * 6);
  }

  .before\:h-8:before {
    content: var(--tw-content);
    height: calc(var(--spacing) * 8);
  }

  .before\:h-11:before {
    content: var(--tw-content);
    height: calc(var(--spacing) * 11);
  }

  .before\:h-\[var\(--top-extension\,16px\)\]:before {
    content: var(--tw-content);
    height: var(--top-extension, 16px);
  }

  .before\:h-px:before {
    content: var(--tw-content);
    height: 1px;
  }

  .before\:w-0:before {
    content: var(--tw-content);
    width: calc(var(--spacing) * 0);
  }

  .before\:w-2\.5:before {
    content: var(--tw-content);
    width: calc(var(--spacing) * 2.5);
  }

  .before\:w-6:before {
    content: var(--tw-content);
    width: calc(var(--spacing) * 6);
  }

  .before\:w-8:before {
    content: var(--tw-content);
    width: calc(var(--spacing) * 8);
  }

  .before\:w-11:before {
    content: var(--tw-content);
    width: calc(var(--spacing) * 11);
  }

  .before\:-translate-x-full:before {
    content: var(--tw-content);
    --tw-translate-x: -100%;
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }

  .before\:-translate-y-1:before {
    content: var(--tw-content);
    --tw-translate-y: calc(var(--spacing) * -1);
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }

  .before\:rotate-0:before {
    content: var(--tw-content);
    rotate: none;
  }

  .before\:rotate-45:before {
    content: var(--tw-content);
    rotate: 45deg;
  }

  .before\:animate-\[shimmer_2s_infinite\]:before {
    content: var(--tw-content);
    animation: 2s infinite shimmer;
  }

  .before\:animate-none:before {
    content: var(--tw-content);
    animation: none;
  }

  .before\:rounded-\[calc\(theme\(borderRadius\.medium\)\*0\.5\)\]:before {
    content: var(--tw-content);
    border-radius: calc(var(--heroui-radius-medium) * .5);
  }

  .before\:rounded-\[calc\(theme\(borderRadius\.medium\)\*0\.6\)\]:before {
    content: var(--tw-content);
    border-radius: calc(var(--heroui-radius-medium) * .6);
  }

  .before\:rounded-\[calc\(theme\(borderRadius\.medium\)\*0\.7\)\]:before {
    content: var(--tw-content);
    border-radius: calc(var(--heroui-radius-medium) * .7);
  }

  .before\:rounded-full:before {
    content: var(--tw-content);
    border-radius: 3.40282e38px;
  }

  .before\:rounded-none:before {
    content: var(--tw-content);
    border-radius: 0;
  }

  .before\:rounded-sm:before {
    content: var(--tw-content);
    border-radius: calc(var(--radius)  - 4px);
  }

  .before\:border-2:before {
    content: var(--tw-content);
    border-style: var(--tw-border-style);
    border-width: 2px;
  }

  .before\:border-t:before {
    content: var(--tw-content);
    border-top-style: var(--tw-border-style);
    border-top-width: 1px;
  }

  .before\:border-solid:before {
    content: var(--tw-content);
    --tw-border-style: solid;
    border-style: solid;
  }

  .before\:border-content4\/30:before {
    content: var(--tw-content);
    border-color: hsl(var(--heroui-content4) / 1);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .before\:border-content4\/30:before {
      border-color: color-mix(in oklab, hsl(var(--heroui-content4) / 1) 30%, transparent);
    }
  }

  .before\:border-danger:before {
    content: var(--tw-content);
    border-color: hsl(var(--heroui-danger) / 1);
  }

  .before\:border-default:before {
    content: var(--tw-content);
    border-color: hsl(var(--heroui-default) / 1);
  }

  .before\:bg-content1:before {
    content: var(--tw-content);
    background-color: hsl(var(--heroui-content1) / 1);
  }

  .before\:bg-current:before {
    content: var(--tw-content);
    background-color: currentColor;
  }

  .before\:bg-danger:before {
    content: var(--tw-content);
    background-color: hsl(var(--heroui-danger) / 1);
  }

  .before\:bg-danger\/20:before {
    content: var(--tw-content);
    background-color: hsl(var(--heroui-danger) / 1);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .before\:bg-danger\/20:before {
      background-color: color-mix(in oklab, hsl(var(--heroui-danger) / 1) 20%, transparent);
    }
  }

  .before\:bg-default\/60:before {
    content: var(--tw-content);
    background-color: hsl(var(--heroui-default) / 1);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .before\:bg-default\/60:before {
      background-color: color-mix(in oklab, hsl(var(--heroui-default) / 1) 60%, transparent);
    }
  }

  .before\:bg-foreground:before {
    content: var(--tw-content);
    background-color: var(--foreground);
  }

  .before\:bg-primary:before {
    content: var(--tw-content);
    background-color: var(--primary);
  }

  .before\:bg-primary\/20:before {
    content: var(--tw-content);
    background-color: var(--primary);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .before\:bg-primary\/20:before {
      background-color: color-mix(in oklab, var(--primary) 20%, transparent);
    }
  }

  .before\:bg-secondary:before {
    content: var(--tw-content);
    background-color: var(--secondary);
  }

  .before\:bg-secondary\/20:before {
    content: var(--tw-content);
    background-color: var(--secondary);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .before\:bg-secondary\/20:before {
      background-color: color-mix(in oklab, var(--secondary) 20%, transparent);
    }
  }

  .before\:bg-success:before {
    content: var(--tw-content);
    background-color: hsl(var(--heroui-success) / 1);
  }

  .before\:bg-success\/20:before {
    content: var(--tw-content);
    background-color: hsl(var(--heroui-success) / 1);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .before\:bg-success\/20:before {
      background-color: color-mix(in oklab, hsl(var(--heroui-success) / 1) 20%, transparent);
    }
  }

  .before\:bg-transparent:before {
    content: var(--tw-content);
    background-color: #0000;
  }

  .before\:bg-warning:before {
    content: var(--tw-content);
    background-color: hsl(var(--heroui-warning) / 1);
  }

  .before\:bg-warning\/20:before {
    content: var(--tw-content);
    background-color: hsl(var(--heroui-warning) / 1);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .before\:bg-warning\/20:before {
      background-color: color-mix(in oklab, hsl(var(--heroui-warning) / 1) 20%, transparent);
    }
  }

  .before\:bg-gradient-to-r:before {
    content: var(--tw-content);
    --tw-gradient-position: to right in oklab;
    background-image: linear-gradient(var(--tw-gradient-stops));
  }

  .before\:from-transparent:before {
    content: var(--tw-content);
    --tw-gradient-from: transparent;
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .before\:via-content4:before {
    content: var(--tw-content);
    --tw-gradient-via: hsl(var(--heroui-content4) / 1);
    --tw-gradient-via-stops: var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-via) var(--tw-gradient-via-position), var(--tw-gradient-to) var(--tw-gradient-to-position);
    --tw-gradient-stops: var(--tw-gradient-via-stops);
  }

  .before\:to-transparent:before {
    content: var(--tw-content);
    --tw-gradient-to: transparent;
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .before\:opacity-0:before {
    content: var(--tw-content);
    opacity: 0;
  }

  .before\:opacity-100:before {
    content: var(--tw-content);
    opacity: 1;
  }

  .before\:shadow-small:before {
    content: var(--tw-content);
    --tw-shadow: var(--heroui-box-shadow-small);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .before\:transition-colors:before {
    content: var(--tw-content);
    transition-property: color, background-color, border-color, outline-color, text-decoration-color, fill, stroke, --tw-gradient-from, --tw-gradient-via, --tw-gradient-to;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }

  .before\:transition-transform:before {
    content: var(--tw-content);
    transition-property: transform, translate, scale, rotate;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }

  .before\:transition-width:before {
    content: var(--tw-content);
    transition-property: width;
    transition-duration: .25s;
    transition-timing-function: ease;
  }

  .before\:transition-none:before {
    content: var(--tw-content);
    transition-property: none;
  }

  .before\:duration-150:before {
    content: var(--tw-content);
    --tw-duration: .15s;
    transition-duration: .15s;
  }

  .before\:content-\[\'\'\]:before {
    content: var(--tw-content);
    --tw-content: "";
    content: var(--tw-content);
  }

  .group-data-\[hover\=true\]\:before\:bg-default-100:is(:where(.group)[data-hover="true"] *):before {
    content: var(--tw-content);
    background-color: hsl(var(--heroui-default-100) / 1);
  }

  .group-aria-\[selected\=false\]\/tr\:group-data-\[hover\=true\]\/tr\:before\:bg-default-100:is(:where(.group\/tr)[aria-selected="false"] *):is(:where(.group\/tr)[data-hover="true"] *):before {
    content: var(--tw-content);
    background-color: hsl(var(--heroui-default-100) / 1);
  }

  .group-aria-\[selected\=false\]\/tr\:group-data-\[hover\=true\]\/tr\:before\:opacity-70:is(:where(.group\/tr)[aria-selected="false"] *):is(:where(.group\/tr)[data-hover="true"] *):before {
    content: var(--tw-content);
    opacity: .7;
  }

  .group-data-\[middle\=true\]\/tr\:before\:rounded-none:is(:where(.group\/tr)[data-middle="true"] *):before {
    content: var(--tw-content);
    border-radius: 0;
  }

  .group-data-\[odd\=true\]\/tr\:before\:-z-10:is(:where(.group\/tr)[data-odd="true"] *):before {
    content: var(--tw-content);
    z-index: calc(10 * -1);
  }

  .group-data-\[odd\=true\]\/tr\:before\:bg-default-100:is(:where(.group\/tr)[data-odd="true"] *):before {
    content: var(--tw-content);
    background-color: hsl(var(--heroui-default-100) / 1);
  }

  .group-data-\[odd\=true\]\/tr\:before\:opacity-100:is(:where(.group\/tr)[data-odd="true"] *):before {
    content: var(--tw-content);
    opacity: 1;
  }

  .group-data-\[open\=true\]\:before\:translate-y-px:is(:where(.group)[data-open="true"] *):before {
    content: var(--tw-content);
    --tw-translate-y: 1px;
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }

  .group-data-\[open\=true\]\:before\:rotate-45:is(:where(.group)[data-open="true"] *):before {
    content: var(--tw-content);
    rotate: 45deg;
  }

  .group-data-\[selected\=true\]\:before\:w-full:is(:where(.group)[data-selected="true"] *):before {
    content: var(--tw-content);
    width: 100%;
  }

  .after\:pointer-events-auto:after {
    content: var(--tw-content);
    pointer-events: auto;
  }

  .after\:absolute:after {
    content: var(--tw-content);
    position: absolute;
  }

  .after\:-inset-2:after {
    content: var(--tw-content);
    inset: calc(var(--spacing) * -2);
  }

  .after\:inset-0:after {
    content: var(--tw-content);
    inset: calc(var(--spacing) * 0);
  }

  .after\:inset-y-0:after {
    content: var(--tw-content);
    inset-block: calc(var(--spacing) * 0);
  }

  .after\:top-0:after {
    content: var(--tw-content);
    top: calc(var(--spacing) * 0);
  }

  .after\:top-1\/2:after {
    content: var(--tw-content);
    top: 50%;
  }

  .after\:right-0:after {
    content: var(--tw-content);
    right: calc(var(--spacing) * 0);
  }

  .after\:-bottom-1:after {
    content: var(--tw-content);
    bottom: calc(var(--spacing) * -1);
  }

  .after\:-bottom-\[2px\]:after {
    content: var(--tw-content);
    bottom: -2px;
  }

  .after\:bottom-0:after {
    content: var(--tw-content);
    bottom: calc(var(--spacing) * 0);
  }

  .after\:bottom-\[calc\(-1\*var\(--bottom-extension\,16px\)\)\]:after {
    content: var(--tw-content);
    bottom: calc(-1 * var(--bottom-extension, 16px));
  }

  .after\:left-0:after {
    content: var(--tw-content);
    left: calc(var(--spacing) * 0);
  }

  .after\:left-1\/2:after {
    content: var(--tw-content);
    left: 50%;
  }

  .after\:-z-10:after {
    content: var(--tw-content);
    z-index: calc(10 * -1);
  }

  .after\:z-0:after {
    content: var(--tw-content);
    z-index: 0;
  }

  .after\:z-\[-1\]:after {
    content: var(--tw-content);
    z-index: -1;
  }

  .after\:ms-0\.5:after {
    content: var(--tw-content);
    margin-inline-start: calc(var(--spacing) * .5);
  }

  .after\:ml-0\.5:after {
    content: var(--tw-content);
    margin-left: calc(var(--spacing) * .5);
  }

  .after\:block:after {
    content: var(--tw-content);
    display: block;
  }

  .after\:flex:after {
    content: var(--tw-content);
    display: flex;
  }

  .after\:h-0:after {
    content: var(--tw-content);
    height: calc(var(--spacing) * 0);
  }

  .after\:h-4:after {
    content: var(--tw-content);
    height: calc(var(--spacing) * 4);
  }

  .after\:h-5:after {
    content: var(--tw-content);
    height: calc(var(--spacing) * 5);
  }

  .after\:h-\[2px\]:after {
    content: var(--tw-content);
    height: 2px;
  }

  .after\:h-\[var\(--bottom-extension\,16px\)\]:after {
    content: var(--tw-content);
    height: var(--bottom-extension, 16px);
  }

  .after\:h-divider:after {
    content: var(--tw-content);
    height: var(--heroui-divider-weight);
  }

  .after\:h-full:after {
    content: var(--tw-content);
    height: 100%;
  }

  .after\:h-px:after {
    content: var(--tw-content);
    height: 1px;
  }

  .after\:w-0:after {
    content: var(--tw-content);
    width: calc(var(--spacing) * 0);
  }

  .after\:w-1:after {
    content: var(--tw-content);
    width: calc(var(--spacing) * 1);
  }

  .after\:w-4:after {
    content: var(--tw-content);
    width: calc(var(--spacing) * 4);
  }

  .after\:w-5:after {
    content: var(--tw-content);
    width: calc(var(--spacing) * 5);
  }

  .after\:w-6:after {
    content: var(--tw-content);
    width: calc(var(--spacing) * 6);
  }

  .after\:w-\[2px\]:after {
    content: var(--tw-content);
    width: 2px;
  }

  .after\:w-\[80\%\]:after {
    content: var(--tw-content);
    width: 80%;
  }

  .after\:w-full:after {
    content: var(--tw-content);
    width: 100%;
  }

  .after\:origin-center:after {
    content: var(--tw-content);
    transform-origin: center;
  }

  .after\:-translate-x-1\/2:after {
    content: var(--tw-content);
    --tw-translate-x: calc(calc(1 / 2 * 100%) * -1);
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }

  .after\:translate-y-1:after {
    content: var(--tw-content);
    --tw-translate-y: calc(var(--spacing) * 1);
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }

  .after\:scale-50:after {
    content: var(--tw-content);
    --tw-scale-x: 50%;
    --tw-scale-y: 50%;
    --tw-scale-z: 50%;
    scale: var(--tw-scale-x) var(--tw-scale-y);
  }

  .after\:rotate-0:after {
    content: var(--tw-content);
    rotate: none;
  }

  .after\:items-center:after {
    content: var(--tw-content);
    align-items: center;
  }

  .after\:rounded-\[calc\(theme\(borderRadius\.large\)\/2\)\]:after {
    content: var(--tw-content);
    border-radius: calc(var(--heroui-radius-large) / 2);
  }

  .after\:rounded-\[calc\(theme\(borderRadius\.medium\)\*0\.5\)\]:after {
    content: var(--tw-content);
    border-radius: calc(var(--heroui-radius-medium) * .5);
  }

  .after\:rounded-\[calc\(theme\(borderRadius\.medium\)\*0\.6\)\]:after {
    content: var(--tw-content);
    border-radius: calc(var(--heroui-radius-medium) * .6);
  }

  .after\:rounded-\[calc\(theme\(borderRadius\.medium\)\*0\.7\)\]:after {
    content: var(--tw-content);
    border-radius: calc(var(--heroui-radius-medium) * .7);
  }

  .after\:rounded-\[calc\(theme\(borderRadius\.medium\)\/3\)\]:after {
    content: var(--tw-content);
    border-radius: calc(var(--heroui-radius-medium) / 3);
  }

  .after\:rounded-\[calc\(theme\(borderRadius\.small\)\/3\)\]:after {
    content: var(--tw-content);
    border-radius: calc(var(--heroui-radius-small) / 3);
  }

  .after\:rounded-full:after {
    content: var(--tw-content);
    border-radius: 3.40282e38px;
  }

  .after\:rounded-none:after {
    content: var(--tw-content);
    border-radius: 0;
  }

  .after\:rounded-xl:after {
    content: var(--tw-content);
    border-radius: calc(var(--radius)  + 4px);
  }

  .after\:border-t:after {
    content: var(--tw-content);
    border-top-style: var(--tw-border-style);
    border-top-width: 1px;
  }

  .after\:border-border:after {
    content: var(--tw-content);
    border-color: var(--border);
  }

  .after\:\!bg-danger:after {
    content: var(--tw-content);
    background-color: hsl(var(--heroui-danger) / 1) !important;
  }

  .after\:bg-background:after {
    content: var(--tw-content);
    background-color: var(--background);
  }

  .after\:bg-content1:after {
    content: var(--tw-content);
    background-color: hsl(var(--heroui-content1) / 1);
  }

  .after\:bg-content3:after {
    content: var(--tw-content);
    background-color: hsl(var(--heroui-content3) / 1);
  }

  .after\:bg-current:after {
    content: var(--tw-content);
    background-color: currentColor;
  }

  .after\:bg-danger:after {
    content: var(--tw-content);
    background-color: hsl(var(--heroui-danger) / 1);
  }

  .after\:bg-default:after {
    content: var(--tw-content);
    background-color: hsl(var(--heroui-default) / 1);
  }

  .after\:bg-default-foreground:after {
    content: var(--tw-content);
    background-color: hsl(var(--heroui-default-foreground) / 1);
  }

  .after\:bg-divider:after {
    content: var(--tw-content);
    background-color: hsl(var(--heroui-divider) / .15);
  }

  .after\:bg-foreground:after {
    content: var(--tw-content);
    background-color: var(--foreground);
  }

  .after\:bg-primary:after {
    content: var(--tw-content);
    background-color: var(--primary);
  }

  .after\:bg-secondary:after {
    content: var(--tw-content);
    background-color: var(--secondary);
  }

  .after\:bg-success:after {
    content: var(--tw-content);
    background-color: hsl(var(--heroui-success) / 1);
  }

  .after\:bg-transparent:after {
    content: var(--tw-content);
    background-color: #0000;
  }

  .after\:bg-warning:after {
    content: var(--tw-content);
    background-color: hsl(var(--heroui-warning) / 1);
  }

  .after\:text-danger:after {
    content: var(--tw-content);
    color: hsl(var(--heroui-danger) / 1);
  }

  .after\:text-danger-foreground:after {
    content: var(--tw-content);
    color: hsl(var(--heroui-danger-foreground) / 1);
  }

  .after\:text-default-foreground:after {
    content: var(--tw-content);
    color: hsl(var(--heroui-default-foreground) / 1);
  }

  .after\:text-primary-foreground:after {
    content: var(--tw-content);
    color: var(--primary-foreground);
  }

  .after\:text-secondary-foreground:after {
    content: var(--tw-content);
    color: var(--secondary-foreground);
  }

  .after\:text-success-foreground:after {
    content: var(--tw-content);
    color: hsl(var(--heroui-success-foreground) / 1);
  }

  .after\:text-warning-foreground:after {
    content: var(--tw-content);
    color: hsl(var(--heroui-warning-foreground) / 1);
  }

  .after\:opacity-0:after {
    content: var(--tw-content);
    opacity: 0;
  }

  .after\:opacity-100:after {
    content: var(--tw-content);
    opacity: 1;
  }

  .after\:shadow-\[0_1px_0px_0_rgba\(0\,0\,0\,0\.05\)\]:after {
    content: var(--tw-content);
    --tw-shadow: 0 1px 0px 0 var(--tw-shadow-color, #0000000d);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .after\:shadow-small:after {
    content: var(--tw-content);
    --tw-shadow: var(--heroui-box-shadow-small);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .after\:transition-all:after {
    content: var(--tw-content);
    transition-property: all;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }

  .after\:transition-background:after {
    content: var(--tw-content);
    transition-property: background;
    transition-duration: .25s;
    transition-timing-function: ease;
  }

  .after\:transition-height:after {
    content: var(--tw-content);
    transition-property: height;
    transition-duration: .25s;
    transition-timing-function: ease;
  }

  .after\:transition-transform:after {
    content: var(--tw-content);
    transition-property: transform, translate, scale, rotate;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }

  .after\:transition-transform-opacity:after {
    content: var(--tw-content);
    transition-property: transform, scale, opacity;
    transition-duration: .25s;
    transition-timing-function: ease;
  }

  .after\:transition-width:after {
    content: var(--tw-content);
    transition-property: width;
    transition-duration: .25s;
    transition-timing-function: ease;
  }

  .after\:transition-none:after {
    content: var(--tw-content);
    transition-property: none;
  }

  .after\:\!duration-200:after {
    content: var(--tw-content);
    --tw-duration: .2s !important;
    transition-duration: .2s !important;
  }

  .after\:duration-150:after {
    content: var(--tw-content);
    --tw-duration: .15s;
    transition-duration: .15s;
  }

  .after\:\!ease-linear:after {
    content: var(--tw-content);
    --tw-ease: linear !important;
    transition-timing-function: linear !important;
  }

  .after\:content-\[\'\'\]:after {
    content: var(--tw-content);
    --tw-content: "";
    content: var(--tw-content);
  }

  .after\:content-\[\'\*\'\]:after {
    content: var(--tw-content);
    --tw-content: "*";
    content: var(--tw-content);
  }

  .group-data-\[collapsible\=offcanvas\]\:after\:left-full:is(:where(.group)[data-collapsible="offcanvas"] *):after {
    content: var(--tw-content);
    left: 100%;
  }

  .group-data-\[focus\=true\]\:after\:w-full:is(:where(.group)[data-focus="true"] *):after {
    content: var(--tw-content);
    width: 100%;
  }

  .group-data-\[invalid\=true\]\:after\:bg-danger:is(:where(.group)[data-invalid="true"] *):after {
    content: var(--tw-content);
    background-color: hsl(var(--heroui-danger) / 1);
  }

  .group-data-\[open\=true\]\:after\:translate-y-0:is(:where(.group)[data-open="true"] *):after {
    content: var(--tw-content);
    --tw-translate-y: calc(var(--spacing) * 0);
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }

  .group-data-\[open\=true\]\:after\:-rotate-45:is(:where(.group)[data-open="true"] *):after {
    content: var(--tw-content);
    rotate: -45deg;
  }

  .group-data-\[required\=true\]\:after\:ml-0\.5:is(:where(.group)[data-required="true"] *):after {
    content: var(--tw-content);
    margin-left: calc(var(--spacing) * .5);
  }

  .group-data-\[required\=true\]\:after\:text-danger:is(:where(.group)[data-required="true"] *):after {
    content: var(--tw-content);
    color: hsl(var(--heroui-danger) / 1);
  }

  .group-data-\[required\=true\]\:after\:content-\[\'\*\'\]:is(:where(.group)[data-required="true"] *):after {
    content: var(--tw-content);
    --tw-content: "*";
    content: var(--tw-content);
  }

  .group-data-\[selected\=true\]\:after\:scale-100:is(:where(.group)[data-selected="true"] *):after {
    content: var(--tw-content);
    --tw-scale-x: 100%;
    --tw-scale-y: 100%;
    --tw-scale-z: 100%;
    scale: var(--tw-scale-x) var(--tw-scale-y);
  }

  .group-data-\[selected\=true\]\:after\:opacity-100:is(:where(.group)[data-selected="true"] *):after {
    content: var(--tw-content);
    opacity: 1;
  }

  .first\:mt-2:first-child {
    margin-top: calc(var(--spacing) * 2);
  }

  .first\:-ml-0\.5:first-child {
    margin-left: calc(var(--spacing) * -.5);
  }

  .first\:rounded-s-full:first-child {
    border-start-start-radius: 3.40282e38px;
    border-end-start-radius: 3.40282e38px;
  }

  .first\:rounded-s-large:first-child {
    border-start-start-radius: var(--heroui-radius-large);
    border-end-start-radius: var(--heroui-radius-large);
  }

  .first\:rounded-s-lg:first-child {
    border-start-start-radius: var(--radius);
    border-end-start-radius: var(--radius);
  }

  .first\:rounded-s-medium:first-child {
    border-start-start-radius: var(--heroui-radius-medium);
    border-end-start-radius: var(--heroui-radius-medium);
  }

  .first\:rounded-s-none:first-child {
    border-start-start-radius: 0;
    border-end-start-radius: 0;
  }

  .first\:rounded-s-small:first-child {
    border-start-start-radius: var(--heroui-radius-small);
    border-end-start-radius: var(--heroui-radius-small);
  }

  .first\:rounded-l-md:first-child {
    border-top-left-radius: calc(var(--radius)  - 2px);
    border-bottom-left-radius: calc(var(--radius)  - 2px);
  }

  .first\:border-l:first-child {
    border-left-style: var(--tw-border-style);
    border-left-width: 1px;
  }

  .first\:before\:rounded-s-lg:first-child:before {
    content: var(--tw-content);
    border-start-start-radius: var(--radius);
    border-end-start-radius: var(--radius);
  }

  .group-data-\[first\=true\]\/tr\:first\:before\:rounded-ss-lg:is(:where(.group\/tr)[data-first="true"] *):first-child:before {
    content: var(--tw-content);
    border-start-start-radius: var(--radius);
  }

  .group-data-\[last\=true\]\/tr\:first\:before\:rounded-es-lg:is(:where(.group\/tr)[data-last="true"] *):first-child:before {
    content: var(--tw-content);
    border-end-start-radius: var(--radius);
  }

  .last\:rounded-e-full:last-child {
    border-start-end-radius: 3.40282e38px;
    border-end-end-radius: 3.40282e38px;
  }

  .last\:rounded-e-large:last-child {
    border-start-end-radius: var(--heroui-radius-large);
    border-end-end-radius: var(--heroui-radius-large);
  }

  .last\:rounded-e-lg:last-child {
    border-start-end-radius: var(--radius);
    border-end-end-radius: var(--radius);
  }

  .last\:rounded-e-medium:last-child {
    border-start-end-radius: var(--heroui-radius-medium);
    border-end-end-radius: var(--heroui-radius-medium);
  }

  .last\:rounded-e-none:last-child {
    border-start-end-radius: 0;
    border-end-end-radius: 0;
  }

  .last\:rounded-e-small:last-child {
    border-start-end-radius: var(--heroui-radius-small);
    border-end-end-radius: var(--heroui-radius-small);
  }

  .last\:rounded-r-md:last-child {
    border-top-right-radius: calc(var(--radius)  - 2px);
    border-bottom-right-radius: calc(var(--radius)  - 2px);
  }

  .last\:border-0:last-child {
    border-style: var(--tw-border-style);
    border-width: 0;
  }

  .last\:border-b-0:last-child {
    border-bottom-style: var(--tw-border-style);
    border-bottom-width: 0;
  }

  .last\:before\:rounded-e-lg:last-child:before {
    content: var(--tw-content);
    border-start-end-radius: var(--radius);
    border-end-end-radius: var(--radius);
  }

  .group-data-\[first\=true\]\/tr\:last\:before\:rounded-se-lg:is(:where(.group\/tr)[data-first="true"] *):last-child:before {
    content: var(--tw-content);
    border-start-end-radius: var(--radius);
  }

  .group-data-\[last\=true\]\/tr\:last\:before\:rounded-ee-lg:is(:where(.group\/tr)[data-last="true"] *):last-child:before {
    content: var(--tw-content);
    border-end-end-radius: var(--radius);
  }

  .first-of-type\:rounded-e-none:first-of-type {
    border-start-end-radius: 0;
    border-end-end-radius: 0;
  }

  .last-of-type\:rounded-s-none:last-of-type {
    border-start-start-radius: 0;
    border-end-start-radius: 0;
  }

  .autofill\:bg-transparent:autofill {
    background-color: #0000;
  }

  .focus-within\:relative:focus-within {
    position: relative;
  }

  .focus-within\:z-20:focus-within {
    z-index: 20;
  }

  .focus-within\:border-danger:focus-within {
    border-color: hsl(var(--heroui-danger) / 1);
  }

  .focus-within\:border-default-400:focus-within {
    border-color: hsl(var(--heroui-default-400) / 1);
  }

  .focus-within\:border-default-foreground:focus-within {
    border-color: hsl(var(--heroui-default-foreground) / 1);
  }

  .focus-within\:border-primary:focus-within {
    border-color: var(--primary);
  }

  .focus-within\:border-secondary:focus-within {
    border-color: var(--secondary);
  }

  .focus-within\:border-success:focus-within {
    border-color: hsl(var(--heroui-success) / 1);
  }

  .focus-within\:border-warning:focus-within {
    border-color: hsl(var(--heroui-warning) / 1);
  }

  .focus-within\:bg-danger-50:focus-within {
    background-color: hsl(var(--heroui-danger-50) / 1);
  }

  .focus-within\:bg-primary-50:focus-within {
    background-color: hsl(var(--heroui-primary-50) / 1);
  }

  .focus-within\:bg-secondary-50:focus-within {
    background-color: hsl(var(--heroui-secondary-50) / 1);
  }

  .focus-within\:bg-success-50:focus-within {
    background-color: hsl(var(--heroui-success-50) / 1);
  }

  .focus-within\:bg-warning-50:focus-within {
    background-color: hsl(var(--heroui-warning-50) / 1);
  }

  .focus-within\:ring-2:focus-within {
    --tw-ring-shadow: var(--tw-ring-inset, ) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .focus-within\:ring-ring:focus-within {
    --tw-ring-color: var(--ring);
  }

  .focus-within\:ring-offset-2:focus-within {
    --tw-ring-offset-width: 2px;
    --tw-ring-offset-shadow: var(--tw-ring-inset, ) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  }

  .focus-within\:outline-none:focus-within {
    --tw-outline-style: none;
    outline-style: none;
  }

  .focus-within\:after\:w-full:focus-within:after {
    content: var(--tw-content);
    width: 100%;
  }

  @media (hover: hover) {
    .hover\:-translate-x-0:hover {
      --tw-translate-x: calc(var(--spacing) * 0);
      translate: var(--tw-translate-x) var(--tw-translate-y);
    }
  }

  @media (hover: hover) {
    .hover\:scale-105:hover {
      --tw-scale-x: 105%;
      --tw-scale-y: 105%;
      --tw-scale-z: 105%;
      scale: var(--tw-scale-x) var(--tw-scale-y);
    }
  }

  @media (hover: hover) {
    .hover\:scale-110:hover {
      --tw-scale-x: 110%;
      --tw-scale-y: 110%;
      --tw-scale-z: 110%;
      scale: var(--tw-scale-x) var(--tw-scale-y);
    }
  }

  @media (hover: hover) {
    .hover\:scale-125:hover {
      --tw-scale-x: 125%;
      --tw-scale-y: 125%;
      --tw-scale-z: 125%;
      scale: var(--tw-scale-x) var(--tw-scale-y);
    }
  }

  @media (hover: hover) {
    .hover\:border-danger:hover {
      border-color: hsl(var(--heroui-danger) / 1);
    }
  }

  @media (hover: hover) {
    .hover\:border-default:hover {
      border-color: hsl(var(--heroui-default) / 1);
    }
  }

  @media (hover: hover) {
    .hover\:border-default-300:hover {
      border-color: hsl(var(--heroui-default-300) / 1);
    }
  }

  @media (hover: hover) {
    .hover\:border-default-400:hover {
      border-color: hsl(var(--heroui-default-400) / 1);
    }
  }

  @media (hover: hover) {
    .hover\:border-primary:hover {
      border-color: var(--primary);
    }
  }

  @media (hover: hover) {
    .hover\:border-secondary:hover {
      border-color: var(--secondary);
    }
  }

  @media (hover: hover) {
    .hover\:border-success:hover {
      border-color: hsl(var(--heroui-success) / 1);
    }
  }

  @media (hover: hover) {
    .hover\:border-warning:hover {
      border-color: hsl(var(--heroui-warning) / 1);
    }
  }

  @media (hover: hover) {
    .hover\:\!bg-foreground:hover {
      background-color: var(--foreground) !important;
    }
  }

  @media (hover: hover) {
    .hover\:bg-accent:hover {
      background-color: var(--accent);
    }
  }

  @media (hover: hover) {
    .hover\:bg-background\/50:hover {
      background-color: var(--background);
    }

    @supports (color: color-mix(in lab, red, red)) {
      .hover\:bg-background\/50:hover {
        background-color: color-mix(in oklab, var(--background) 50%, transparent);
      }
    }
  }

  @media (hover: hover) {
    .hover\:bg-background\/90:hover {
      background-color: var(--background);
    }

    @supports (color: color-mix(in lab, red, red)) {
      .hover\:bg-background\/90:hover {
        background-color: color-mix(in oklab, var(--background) 90%, transparent);
      }
    }
  }

  @media (hover: hover) {
    .hover\:bg-blue-100:hover {
      background-color: var(--color-blue-100);
    }
  }

  @media (hover: hover) {
    .hover\:bg-blue-100\/80:hover {
      background-color: #dbeafecc;
    }

    @supports (color: color-mix(in lab, red, red)) {
      .hover\:bg-blue-100\/80:hover {
        background-color: color-mix(in oklab, var(--color-blue-100) 80%, transparent);
      }
    }
  }

  @media (hover: hover) {
    .hover\:bg-blue-200:hover {
      background-color: var(--color-blue-200);
    }
  }

  @media (hover: hover) {
    .hover\:bg-danger:hover {
      background-color: hsl(var(--heroui-danger) / 1);
    }
  }

  @media (hover: hover) {
    .hover\:bg-danger-50:hover {
      background-color: hsl(var(--heroui-danger-50) / 1);
    }
  }

  @media (hover: hover) {
    .hover\:bg-default-100:hover {
      background-color: hsl(var(--heroui-default-100) / 1);
    }
  }

  @media (hover: hover) {
    .hover\:bg-default-200:hover {
      background-color: hsl(var(--heroui-default-200) / 1);
    }
  }

  @media (hover: hover) {
    .hover\:bg-destructive\/10:hover {
      background-color: var(--destructive);
    }

    @supports (color: color-mix(in lab, red, red)) {
      .hover\:bg-destructive\/10:hover {
        background-color: color-mix(in oklab, var(--destructive) 10%, transparent);
      }
    }
  }

  @media (hover: hover) {
    .hover\:bg-destructive\/90:hover {
      background-color: var(--destructive);
    }

    @supports (color: color-mix(in lab, red, red)) {
      .hover\:bg-destructive\/90:hover {
        background-color: color-mix(in oklab, var(--destructive) 90%, transparent);
      }
    }
  }

  @media (hover: hover) {
    .hover\:bg-gray-50:hover {
      background-color: var(--color-gray-50);
    }
  }

  @media (hover: hover) {
    .hover\:bg-gray-100:hover {
      background-color: var(--color-gray-100);
    }
  }

  @media (hover: hover) {
    .hover\:bg-gray-100\/80:hover {
      background-color: #f3f4f6cc;
    }

    @supports (color: color-mix(in lab, red, red)) {
      .hover\:bg-gray-100\/80:hover {
        background-color: color-mix(in oklab, var(--color-gray-100) 80%, transparent);
      }
    }
  }

  @media (hover: hover) {
    .hover\:bg-gray-200:hover {
      background-color: var(--color-gray-200);
    }
  }

  @media (hover: hover) {
    .hover\:bg-green-100:hover {
      background-color: var(--color-green-100);
    }
  }

  @media (hover: hover) {
    .hover\:bg-green-100\/80:hover {
      background-color: #dcfce7cc;
    }

    @supports (color: color-mix(in lab, red, red)) {
      .hover\:bg-green-100\/80:hover {
        background-color: color-mix(in oklab, var(--color-green-100) 80%, transparent);
      }
    }
  }

  @media (hover: hover) {
    .hover\:bg-green-200:hover {
      background-color: var(--color-green-200);
    }
  }

  @media (hover: hover) {
    .hover\:bg-green-600:hover {
      background-color: var(--color-green-600);
    }
  }

  @media (hover: hover) {
    .hover\:bg-green-700:hover {
      background-color: var(--color-green-700);
    }
  }

  @media (hover: hover) {
    .hover\:bg-input\/30:hover {
      background-color: var(--input);
    }

    @supports (color: color-mix(in lab, red, red)) {
      .hover\:bg-input\/30:hover {
        background-color: color-mix(in oklab, var(--input) 30%, transparent);
      }
    }
  }

  @media (hover: hover) {
    .hover\:bg-muted:hover {
      background-color: var(--muted);
    }
  }

  @media (hover: hover) {
    .hover\:bg-muted\/50:hover {
      background-color: var(--muted);
    }

    @supports (color: color-mix(in lab, red, red)) {
      .hover\:bg-muted\/50:hover {
        background-color: color-mix(in oklab, var(--muted) 50%, transparent);
      }
    }
  }

  @media (hover: hover) {
    .hover\:bg-muted\/80:hover {
      background-color: var(--muted);
    }

    @supports (color: color-mix(in lab, red, red)) {
      .hover\:bg-muted\/80:hover {
        background-color: color-mix(in oklab, var(--muted) 80%, transparent);
      }
    }
  }

  @media (hover: hover) {
    .hover\:bg-primary:hover {
      background-color: var(--primary);
    }
  }

  @media (hover: hover) {
    .hover\:bg-primary-50:hover {
      background-color: hsl(var(--heroui-primary-50) / 1);
    }
  }

  @media (hover: hover) {
    .hover\:bg-primary\/90:hover {
      background-color: var(--primary);
    }

    @supports (color: color-mix(in lab, red, red)) {
      .hover\:bg-primary\/90:hover {
        background-color: color-mix(in oklab, var(--primary) 90%, transparent);
      }
    }
  }

  @media (hover: hover) {
    .hover\:bg-purple-200:hover {
      background-color: var(--color-purple-200);
    }
  }

  @media (hover: hover) {
    .hover\:bg-red-50:hover {
      background-color: var(--color-red-50);
    }
  }

  @media (hover: hover) {
    .hover\:bg-red-100:hover {
      background-color: var(--color-red-100);
    }
  }

  @media (hover: hover) {
    .hover\:bg-red-100\/80:hover {
      background-color: #ffe2e2cc;
    }

    @supports (color: color-mix(in lab, red, red)) {
      .hover\:bg-red-100\/80:hover {
        background-color: color-mix(in oklab, var(--color-red-100) 80%, transparent);
      }
    }
  }

  @media (hover: hover) {
    .hover\:bg-red-200:hover {
      background-color: var(--color-red-200);
    }
  }

  @media (hover: hover) {
    .hover\:bg-red-600:hover {
      background-color: var(--color-red-600);
    }
  }

  @media (hover: hover) {
    .hover\:bg-red-700:hover {
      background-color: var(--color-red-700);
    }
  }

  @media (hover: hover) {
    .hover\:bg-secondary-50:hover {
      background-color: hsl(var(--heroui-secondary-50) / 1);
    }
  }

  @media (hover: hover) {
    .hover\:bg-secondary\/80:hover {
      background-color: var(--secondary);
    }

    @supports (color: color-mix(in lab, red, red)) {
      .hover\:bg-secondary\/80:hover {
        background-color: color-mix(in oklab, var(--secondary) 80%, transparent);
      }
    }
  }

  @media (hover: hover) {
    .hover\:bg-sidebar-accent:hover {
      background-color: var(--sidebar-accent);
    }
  }

  @media (hover: hover) {
    .hover\:bg-sidebar\/50:hover {
      background-color: var(--sidebar);
    }

    @supports (color: color-mix(in lab, red, red)) {
      .hover\:bg-sidebar\/50:hover {
        background-color: color-mix(in oklab, var(--sidebar) 50%, transparent);
      }
    }
  }

  @media (hover: hover) {
    .hover\:bg-slate-50:hover {
      background-color: var(--color-slate-50);
    }
  }

  @media (hover: hover) {
    .hover\:bg-success-50:hover {
      background-color: hsl(var(--heroui-success-50) / 1);
    }
  }

  @media (hover: hover) {
    .hover\:bg-transparent:hover {
      background-color: #0000;
    }
  }

  @media (hover: hover) {
    .hover\:bg-warning-50:hover {
      background-color: hsl(var(--heroui-warning-50) / 1);
    }
  }

  @media (hover: hover) {
    .hover\:bg-yellow-100:hover {
      background-color: var(--color-yellow-100);
    }
  }

  @media (hover: hover) {
    .hover\:bg-yellow-100\/80:hover {
      background-color: #fef9c2cc;
    }

    @supports (color: color-mix(in lab, red, red)) {
      .hover\:bg-yellow-100\/80:hover {
        background-color: color-mix(in oklab, var(--color-yellow-100) 80%, transparent);
      }
    }
  }

  @media (hover: hover) {
    .hover\:bg-yellow-200:hover {
      background-color: var(--color-yellow-200);
    }
  }

  @media (hover: hover) {
    .hover\:text-accent-foreground:hover {
      color: var(--accent-foreground);
    }
  }

  @media (hover: hover) {
    .hover\:text-blue-800:hover {
      color: var(--color-blue-800);
    }
  }

  @media (hover: hover) {
    .hover\:text-danger-600:hover {
      color: hsl(var(--heroui-danger-600) / 1);
    }
  }

  @media (hover: hover) {
    .hover\:text-default-600:hover {
      color: hsl(var(--heroui-default-600) / 1);
    }
  }

  @media (hover: hover) {
    .hover\:text-destructive:hover {
      color: var(--destructive);
    }
  }

  @media (hover: hover) {
    .hover\:text-foreground:hover {
      color: var(--foreground);
    }
  }

  @media (hover: hover) {
    .hover\:text-foreground-600:hover {
      color: hsl(var(--heroui-foreground-600) / 1);
    }
  }

  @media (hover: hover) {
    .hover\:text-gray-700:hover {
      color: var(--color-gray-700);
    }
  }

  @media (hover: hover) {
    .hover\:text-gray-800:hover {
      color: var(--color-gray-800);
    }
  }

  @media (hover: hover) {
    .hover\:text-muted-foreground:hover {
      color: var(--muted-foreground);
    }
  }

  @media (hover: hover) {
    .hover\:text-primary-600:hover {
      color: hsl(var(--heroui-primary-600) / 1);
    }
  }

  @media (hover: hover) {
    .hover\:text-primary-foreground:hover {
      color: var(--primary-foreground);
    }
  }

  @media (hover: hover) {
    .hover\:text-primary\/90:hover {
      color: var(--primary);
    }

    @supports (color: color-mix(in lab, red, red)) {
      .hover\:text-primary\/90:hover {
        color: color-mix(in oklab, var(--primary) 90%, transparent);
      }
    }
  }

  @media (hover: hover) {
    .hover\:text-red-700:hover {
      color: var(--color-red-700);
    }
  }

  @media (hover: hover) {
    .hover\:text-secondary-600:hover {
      color: hsl(var(--heroui-secondary-600) / 1);
    }
  }

  @media (hover: hover) {
    .hover\:text-sidebar-accent-foreground:hover {
      color: var(--sidebar-accent-foreground);
    }
  }

  @media (hover: hover) {
    .hover\:text-slate-900:hover {
      color: var(--color-slate-900);
    }
  }

  @media (hover: hover) {
    .hover\:text-success-600:hover {
      color: hsl(var(--heroui-success-600) / 1);
    }
  }

  @media (hover: hover) {
    .hover\:text-warning-600:hover {
      color: hsl(var(--heroui-warning-600) / 1);
    }
  }

  @media (hover: hover) {
    .hover\:underline:hover {
      text-decoration-line: underline;
    }
  }

  @media (hover: hover) {
    .hover\:\!opacity-100:hover {
      opacity: 1 !important;
    }
  }

  @media (hover: hover) {
    .hover\:opacity-100:hover {
      opacity: 1;
    }
  }

  @media (hover: hover) {
    .hover\:opacity-hover:hover {
      opacity: var(--heroui-hover-opacity);
    }
  }

  @media (hover: hover) {
    .hover\:shadow-\[0_0_0_1px_hsl\(var\(--sidebar-accent\)\)\]:hover {
      --tw-shadow: 0 0 0 1px var(--tw-shadow-color, hsl(var(--sidebar-accent)));
      box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
    }
  }

  @media (hover: hover) {
    .hover\:shadow-lg:hover {
      --tw-shadow: 0 10px 15px -3px var(--tw-shadow-color, #0000001a), 0 4px 6px -4px var(--tw-shadow-color, #0000001a);
      box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
    }
  }

  @media (hover: hover) {
    .hover\:shadow-xl:hover {
      --tw-shadow: 0 20px 25px -5px var(--tw-shadow-color, #0000001a), 0 8px 10px -6px var(--tw-shadow-color, #0000001a);
      box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
    }
  }

  @media (hover: hover) {
    .hover\:ring-4:hover {
      --tw-ring-shadow: var(--tw-ring-inset, ) 0 0 0 calc(4px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);
      box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
    }
  }

  @media (hover: hover) {
    .hover\:group-data-\[collapsible\=offcanvas\]\:bg-sidebar:hover:is(:where(.group)[data-collapsible="offcanvas"] *) {
      background-color: var(--sidebar);
    }
  }

  @media (hover: hover) {
    .group-data-\[invalid\=true\]\:hover\:border-danger:is(:where(.group)[data-invalid="true"] *):hover {
      border-color: hsl(var(--heroui-danger) / 1);
    }
  }

  @media (hover: hover) {
    .group-data-\[invalid\=true\]\:hover\:bg-danger-100:is(:where(.group)[data-invalid="true"] *):hover {
      background-color: hsl(var(--heroui-danger-100) / 1);
    }
  }

  @media (hover: hover) {
    .hover\:after\:bg-danger\/20:hover:after {
      content: var(--tw-content);
      background-color: hsl(var(--heroui-danger) / 1);
    }

    @supports (color: color-mix(in lab, red, red)) {
      .hover\:after\:bg-danger\/20:hover:after {
        background-color: color-mix(in oklab, hsl(var(--heroui-danger) / 1) 20%, transparent);
      }
    }
  }

  @media (hover: hover) {
    .hover\:after\:bg-foreground\/10:hover:after {
      content: var(--tw-content);
      background-color: var(--foreground);
    }

    @supports (color: color-mix(in lab, red, red)) {
      .hover\:after\:bg-foreground\/10:hover:after {
        background-color: color-mix(in oklab, var(--foreground) 10%, transparent);
      }
    }
  }

  @media (hover: hover) {
    .hover\:after\:bg-primary\/20:hover:after {
      content: var(--tw-content);
      background-color: var(--primary);
    }

    @supports (color: color-mix(in lab, red, red)) {
      .hover\:after\:bg-primary\/20:hover:after {
        background-color: color-mix(in oklab, var(--primary) 20%, transparent);
      }
    }
  }

  @media (hover: hover) {
    .hover\:after\:bg-secondary\/20:hover:after {
      content: var(--tw-content);
      background-color: var(--secondary);
    }

    @supports (color: color-mix(in lab, red, red)) {
      .hover\:after\:bg-secondary\/20:hover:after {
        background-color: color-mix(in oklab, var(--secondary) 20%, transparent);
      }
    }
  }

  @media (hover: hover) {
    .hover\:after\:bg-sidebar-border:hover:after {
      content: var(--tw-content);
      background-color: var(--sidebar-border);
    }
  }

  @media (hover: hover) {
    .hover\:after\:bg-success\/20:hover:after {
      content: var(--tw-content);
      background-color: hsl(var(--heroui-success) / 1);
    }

    @supports (color: color-mix(in lab, red, red)) {
      .hover\:after\:bg-success\/20:hover:after {
        background-color: color-mix(in oklab, hsl(var(--heroui-success) / 1) 20%, transparent);
      }
    }
  }

  @media (hover: hover) {
    .hover\:after\:bg-warning\/20:hover:after {
      content: var(--tw-content);
      background-color: hsl(var(--heroui-warning) / 1);
    }

    @supports (color: color-mix(in lab, red, red)) {
      .hover\:after\:bg-warning\/20:hover:after {
        background-color: color-mix(in oklab, hsl(var(--heroui-warning) / 1) 20%, transparent);
      }
    }
  }

  @media (hover: hover) {
    .hover\:after\:opacity-100:hover:after {
      content: var(--tw-content);
      opacity: 1;
    }
  }

  @media (hover: hover) {
    .focus-within\:hover\:border-danger:focus-within:hover {
      border-color: hsl(var(--heroui-danger) / 1);
    }
  }

  @media (hover: hover) {
    .focus-within\:hover\:border-default-foreground:focus-within:hover {
      border-color: hsl(var(--heroui-default-foreground) / 1);
    }
  }

  @media (hover: hover) {
    .focus-within\:hover\:border-primary:focus-within:hover {
      border-color: var(--primary);
    }
  }

  @media (hover: hover) {
    .focus-within\:hover\:border-secondary:focus-within:hover {
      border-color: var(--secondary);
    }
  }

  @media (hover: hover) {
    .focus-within\:hover\:border-success:focus-within:hover {
      border-color: hsl(var(--heroui-success) / 1);
    }
  }

  @media (hover: hover) {
    .focus-within\:hover\:border-warning:focus-within:hover {
      border-color: hsl(var(--heroui-warning) / 1);
    }
  }

  @media (hover: hover) {
    .focus-within\:hover\:bg-default-100:focus-within:hover {
      background-color: hsl(var(--heroui-default-100) / 1);
    }
  }

  @media (hover: hover) {
    .group-data-\[invalid\=true\]\:focus-within\:hover\:border-danger:is(:where(.group)[data-invalid="true"] *):focus-within:hover {
      border-color: hsl(var(--heroui-danger) / 1);
    }
  }

  @media (hover: hover) {
    .group-data-\[invalid\=true\]\:focus-within\:hover\:bg-danger-50:is(:where(.group)[data-invalid="true"] *):focus-within:hover {
      background-color: hsl(var(--heroui-danger-50) / 1);
    }
  }

  .focus\:z-10:focus {
    z-index: 10;
  }

  .focus\:bg-accent:focus {
    background-color: var(--accent);
  }

  .focus\:bg-danger-400\/50:focus {
    background-color: hsl(var(--heroui-danger-400) / 1);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .focus\:bg-danger-400\/50:focus {
      background-color: color-mix(in oklab, hsl(var(--heroui-danger-400) / 1) 50%, transparent);
    }
  }

  .focus\:bg-default-400\/50:focus {
    background-color: hsl(var(--heroui-default-400) / 1);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .focus\:bg-default-400\/50:focus {
      background-color: color-mix(in oklab, hsl(var(--heroui-default-400) / 1) 50%, transparent);
    }
  }

  .focus\:bg-primary:focus {
    background-color: var(--primary);
  }

  .focus\:bg-primary-400\/50:focus {
    background-color: hsl(var(--heroui-primary-400) / 1);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .focus\:bg-primary-400\/50:focus {
      background-color: color-mix(in oklab, hsl(var(--heroui-primary-400) / 1) 50%, transparent);
    }
  }

  .focus\:bg-secondary-400\/50:focus {
    background-color: hsl(var(--heroui-secondary-400) / 1);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .focus\:bg-secondary-400\/50:focus {
      background-color: color-mix(in oklab, hsl(var(--heroui-secondary-400) / 1) 50%, transparent);
    }
  }

  .focus\:bg-success-400\/50:focus {
    background-color: hsl(var(--heroui-success-400) / 1);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .focus\:bg-success-400\/50:focus {
      background-color: color-mix(in oklab, hsl(var(--heroui-success-400) / 1) 50%, transparent);
    }
  }

  .focus\:bg-transparent:focus {
    background-color: #0000;
  }

  .focus\:bg-warning-400\/50:focus {
    background-color: hsl(var(--heroui-warning-400) / 1);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .focus\:bg-warning-400\/50:focus {
      background-color: color-mix(in oklab, hsl(var(--heroui-warning-400) / 1) 50%, transparent);
    }
  }

  .focus\:text-accent-foreground:focus {
    color: var(--accent-foreground);
  }

  .focus\:text-blue-600:focus {
    color: var(--color-blue-600);
  }

  .focus\:text-destructive:focus {
    color: var(--destructive);
  }

  .focus\:text-green-600:focus {
    color: var(--color-green-600);
  }

  .focus\:text-primary-foreground:focus {
    color: var(--primary-foreground);
  }

  .focus\:text-red-500:focus {
    color: var(--color-red-500);
  }

  .focus\:text-red-600:focus {
    color: var(--color-red-600);
  }

  .focus\:underline:focus {
    text-decoration-line: underline;
  }

  .focus\:shadow-sm:focus {
    --tw-shadow: 0 1px 3px 0 var(--tw-shadow-color, #0000001a), 0 1px 2px -1px var(--tw-shadow-color, #0000001a);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .focus\:ring-2:focus {
    --tw-ring-shadow: var(--tw-ring-inset, ) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .focus\:ring-ring:focus {
    --tw-ring-color: var(--ring);
  }

  .focus\:ring-offset-2:focus {
    --tw-ring-offset-width: 2px;
    --tw-ring-offset-shadow: var(--tw-ring-inset, ) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  }

  .focus\:outline-hidden:focus {
    --tw-outline-style: none;
    outline-style: none;
  }

  @media (forced-colors: active) {
    .focus\:outline-hidden:focus {
      outline-offset: 2px;
      outline: 2px solid #0000;
    }
  }

  .focus\:outline-none:focus {
    --tw-outline-style: none;
    outline-style: none;
  }

  .focus-visible\:z-10:focus-visible {
    z-index: 10;
  }

  .focus-visible\:border:focus-visible {
    border-style: var(--tw-border-style);
    border-width: 1px;
  }

  .focus-visible\:border-ring:focus-visible {
    border-color: var(--ring);
  }

  .focus-visible\:bg-background:focus-visible {
    background-color: var(--background);
  }

  .focus-visible\:ring-0:focus-visible {
    --tw-ring-shadow: var(--tw-ring-inset, ) 0 0 0 calc(0px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .focus-visible\:ring-1:focus-visible {
    --tw-ring-shadow: var(--tw-ring-inset, ) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .focus-visible\:ring-2:focus-visible {
    --tw-ring-shadow: var(--tw-ring-inset, ) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .focus-visible\:ring-4:focus-visible {
    --tw-ring-shadow: var(--tw-ring-inset, ) 0 0 0 calc(4px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .focus-visible\:ring-\[3px\]:focus-visible {
    --tw-ring-shadow: var(--tw-ring-inset, ) 0 0 0 calc(3px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .focus-visible\:ring-destructive\/20:focus-visible {
    --tw-ring-color: var(--destructive);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .focus-visible\:ring-destructive\/20:focus-visible {
      --tw-ring-color: color-mix(in oklab, var(--destructive) 20%, transparent);
    }
  }

  .focus-visible\:ring-red-500:focus-visible {
    --tw-ring-color: var(--color-red-500);
  }

  .focus-visible\:ring-ring:focus-visible {
    --tw-ring-color: var(--ring);
  }

  .focus-visible\:ring-ring\/50:focus-visible {
    --tw-ring-color: var(--ring);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .focus-visible\:ring-ring\/50:focus-visible {
      --tw-ring-color: color-mix(in oklab, var(--ring) 50%, transparent);
    }
  }

  .focus-visible\:ring-offset-0:focus-visible {
    --tw-ring-offset-width: 0px;
    --tw-ring-offset-shadow: var(--tw-ring-inset, ) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  }

  .focus-visible\:ring-offset-1:focus-visible {
    --tw-ring-offset-width: 1px;
    --tw-ring-offset-shadow: var(--tw-ring-inset, ) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  }

  .focus-visible\:outline-hidden:focus-visible {
    --tw-outline-style: none;
    outline-style: none;
  }

  @media (forced-colors: active) {
    .focus-visible\:outline-hidden:focus-visible {
      outline-offset: 2px;
      outline: 2px solid #0000;
    }
  }

  .focus-visible\:outline-1:focus-visible {
    outline-style: var(--tw-outline-style);
    outline-width: 1px;
  }

  .focus-visible\:outline-2:focus-visible {
    outline-style: var(--tw-outline-style);
    outline-width: 2px;
  }

  .focus-visible\:outline-offset-2:focus-visible {
    outline-offset: 2px;
  }

  .focus-visible\:outline-focus:focus-visible {
    outline-color: hsl(var(--heroui-focus) / 1);
  }

  .focus-visible\:outline-ring:focus-visible {
    outline-color: var(--ring);
  }

  .focus-visible\:outline-none:focus-visible {
    --tw-outline-style: none;
    outline-style: none;
  }

  .active\:bg-default-200:active {
    background-color: hsl(var(--heroui-default-200) / 1);
  }

  .active\:bg-default-300:active {
    background-color: hsl(var(--heroui-default-300) / 1);
  }

  .active\:bg-sidebar-accent:active {
    background-color: var(--sidebar-accent);
  }

  .active\:bg-transparent:active {
    background-color: #0000;
  }

  .active\:text-sidebar-accent-foreground:active {
    color: var(--sidebar-accent-foreground);
  }

  .active\:underline:active {
    text-decoration-line: underline;
  }

  .active\:\!opacity-70:active {
    opacity: .7 !important;
  }

  .active\:opacity-disabled:active {
    opacity: var(--heroui-disabled-opacity);
  }

  .disabled\:pointer-events-none:disabled {
    pointer-events: none;
  }

  .disabled\:cursor-default:disabled {
    cursor: default;
  }

  .disabled\:cursor-not-allowed:disabled {
    cursor: not-allowed;
  }

  .disabled\:text-gray-400:disabled {
    color: var(--color-gray-400);
  }

  .disabled\:no-underline:disabled {
    text-decoration-line: none;
  }

  .disabled\:opacity-50:disabled {
    opacity: .5;
  }

  @media (hover: hover) {
    .disabled\:hover\:scale-100:disabled:hover {
      --tw-scale-x: 100%;
      --tw-scale-y: 100%;
      --tw-scale-z: 100%;
      scale: var(--tw-scale-x) var(--tw-scale-y);
    }
  }

  :where([data-side="left"]) .in-data-\[side\=left\]\:cursor-w-resize {
    cursor: w-resize;
  }

  :where([data-side="right"]) .in-data-\[side\=right\]\:cursor-e-resize {
    cursor: e-resize;
  }

  .has-disabled\:opacity-50:has(:disabled) {
    opacity: .5;
  }

  .has-data-\[slot\=card-action\]\:grid-cols-\[1fr_auto\]:has([data-slot="card-action"]) {
    grid-template-columns: 1fr auto;
  }

  .has-data-\[variant\=inset\]\:bg-sidebar:has([data-variant="inset"]) {
    background-color: var(--sidebar);
  }

  .has-\[\:disabled\]\:opacity-60:has(:disabled) {
    opacity: .6;
  }

  .has-\[\>svg\]\:grid-cols-\[calc\(var\(--spacing\)\*4\)_1fr\]:has( > svg) {
    grid-template-columns: calc(var(--spacing) * 4) 1fr;
  }

  .has-\[\>svg\]\:gap-x-3:has( > svg) {
    column-gap: calc(var(--spacing) * 3);
  }

  .has-\[\>svg\]\:px-2\.5:has( > svg) {
    padding-inline: calc(var(--spacing) * 2.5);
  }

  .has-\[\>svg\]\:px-3:has( > svg) {
    padding-inline: calc(var(--spacing) * 3);
  }

  .has-\[\>svg\]\:px-4:has( > svg) {
    padding-inline: calc(var(--spacing) * 4);
  }

  .aria-disabled\:pointer-events-none[aria-disabled="true"] {
    pointer-events: none;
  }

  .aria-disabled\:opacity-50[aria-disabled="true"] {
    opacity: .5;
  }

  .aria-expanded\:scale-\[0\.97\][aria-expanded="true"] {
    scale: .97;
  }

  .aria-expanded\:opacity-70[aria-expanded="true"] {
    opacity: .7;
  }

  .aria-invalid\:border-destructive[aria-invalid="true"] {
    border-color: var(--destructive);
  }

  .aria-invalid\:ring-destructive\/20[aria-invalid="true"] {
    --tw-ring-color: var(--destructive);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .aria-invalid\:ring-destructive\/20[aria-invalid="true"] {
      --tw-ring-color: color-mix(in oklab, var(--destructive) 20%, transparent);
    }
  }

  .aria-selected\:bg-accent[aria-selected="true"] {
    background-color: var(--accent);
  }

  .aria-selected\:bg-primary[aria-selected="true"] {
    background-color: var(--primary);
  }

  .aria-selected\:text-accent-foreground[aria-selected="true"] {
    color: var(--accent-foreground);
  }

  .aria-selected\:text-muted-foreground[aria-selected="true"] {
    color: var(--muted-foreground);
  }

  .aria-selected\:text-primary-foreground[aria-selected="true"] {
    color: var(--primary-foreground);
  }

  .aria-selected\:opacity-100[aria-selected="true"] {
    opacity: 1;
  }

  .data-\[active\=true\]\:z-10[data-active="true"] {
    z-index: 10;
  }

  .data-\[active\=true\]\:scale-100[data-active="true"] {
    --tw-scale-x: 100%;
    --tw-scale-y: 100%;
    --tw-scale-z: 100%;
    scale: var(--tw-scale-x) var(--tw-scale-y);
  }

  .data-\[active\=true\]\:scale-110[data-active="true"] {
    --tw-scale-x: 110%;
    --tw-scale-y: 110%;
    --tw-scale-z: 110%;
    scale: var(--tw-scale-x) var(--tw-scale-y);
  }

  .data-\[active\=true\]\:border-danger[data-active="true"] {
    border-color: hsl(var(--heroui-danger) / 1);
  }

  .data-\[active\=true\]\:border-danger-400[data-active="true"] {
    border-color: hsl(var(--heroui-danger-400) / 1);
  }

  .data-\[active\=true\]\:border-default-300[data-active="true"] {
    border-color: hsl(var(--heroui-default-300) / 1);
  }

  .data-\[active\=true\]\:border-default-400[data-active="true"] {
    border-color: hsl(var(--heroui-default-400) / 1);
  }

  .data-\[active\=true\]\:border-foreground[data-active="true"] {
    border-color: var(--foreground);
  }

  .data-\[active\=true\]\:border-primary[data-active="true"] {
    border-color: var(--primary);
  }

  .data-\[active\=true\]\:border-ring[data-active="true"] {
    border-color: var(--ring);
  }

  .data-\[active\=true\]\:border-secondary[data-active="true"] {
    border-color: var(--secondary);
  }

  .data-\[active\=true\]\:border-success[data-active="true"] {
    border-color: hsl(var(--heroui-success) / 1);
  }

  .data-\[active\=true\]\:border-warning[data-active="true"] {
    border-color: hsl(var(--heroui-warning) / 1);
  }

  .data-\[active\=true\]\:bg-accent\/50[data-active="true"] {
    background-color: var(--accent);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .data-\[active\=true\]\:bg-accent\/50[data-active="true"] {
      background-color: color-mix(in oklab, var(--accent) 50%, transparent);
    }
  }

  .data-\[active\=true\]\:bg-danger[data-active="true"] {
    background-color: hsl(var(--heroui-danger) / 1);
  }

  .data-\[active\=true\]\:bg-danger-100[data-active="true"] {
    background-color: hsl(var(--heroui-danger-100) / 1);
  }

  .data-\[active\=true\]\:bg-danger-200[data-active="true"] {
    background-color: hsl(var(--heroui-danger-200) / 1);
  }

  .data-\[active\=true\]\:bg-default-200[data-active="true"] {
    background-color: hsl(var(--heroui-default-200) / 1);
  }

  .data-\[active\=true\]\:bg-default-400[data-active="true"] {
    background-color: hsl(var(--heroui-default-400) / 1);
  }

  .data-\[active\=true\]\:bg-primary[data-active="true"] {
    background-color: var(--primary);
  }

  .data-\[active\=true\]\:bg-primary-200[data-active="true"] {
    background-color: hsl(var(--heroui-primary-200) / 1);
  }

  .data-\[active\=true\]\:bg-secondary[data-active="true"] {
    background-color: var(--secondary);
  }

  .data-\[active\=true\]\:bg-secondary-200[data-active="true"] {
    background-color: hsl(var(--heroui-secondary-200) / 1);
  }

  .data-\[active\=true\]\:bg-sidebar-accent[data-active="true"] {
    background-color: var(--sidebar-accent);
  }

  .data-\[active\=true\]\:bg-success[data-active="true"] {
    background-color: hsl(var(--heroui-success) / 1);
  }

  .data-\[active\=true\]\:bg-success-200[data-active="true"] {
    background-color: hsl(var(--heroui-success-200) / 1);
  }

  .data-\[active\=true\]\:bg-warning[data-active="true"] {
    background-color: hsl(var(--heroui-warning) / 1);
  }

  .data-\[active\=true\]\:bg-warning-200[data-active="true"] {
    background-color: hsl(var(--heroui-warning-200) / 1);
  }

  .data-\[active\=true\]\:font-medium[data-active="true"] {
    --tw-font-weight: var(--font-weight-medium);
    font-weight: var(--font-weight-medium);
  }

  .data-\[active\=true\]\:font-semibold[data-active="true"] {
    --tw-font-weight: var(--font-weight-semibold);
    font-weight: var(--font-weight-semibold);
  }

  .data-\[active\=true\]\:text-accent-foreground[data-active="true"] {
    color: var(--accent-foreground);
  }

  .data-\[active\=true\]\:text-danger-foreground[data-active="true"] {
    color: hsl(var(--heroui-danger-foreground) / 1);
  }

  .data-\[active\=true\]\:text-default-foreground[data-active="true"] {
    color: hsl(var(--heroui-default-foreground) / 1);
  }

  .data-\[active\=true\]\:text-primary-foreground[data-active="true"] {
    color: var(--primary-foreground);
  }

  .data-\[active\=true\]\:text-secondary-foreground[data-active="true"] {
    color: var(--secondary-foreground);
  }

  .data-\[active\=true\]\:text-sidebar-accent-foreground[data-active="true"] {
    color: var(--sidebar-accent-foreground);
  }

  .data-\[active\=true\]\:text-success-foreground[data-active="true"] {
    color: hsl(var(--heroui-success-foreground) / 1);
  }

  .data-\[active\=true\]\:text-warning-foreground[data-active="true"] {
    color: hsl(var(--heroui-warning-foreground) / 1);
  }

  .data-\[active\=true\]\:shadow-md[data-active="true"] {
    --tw-shadow: 0 4px 6px -1px var(--tw-shadow-color, #0000001a), 0 2px 4px -2px var(--tw-shadow-color, #0000001a);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .data-\[active\=true\]\:ring-\[3px\][data-active="true"] {
    --tw-ring-shadow: var(--tw-ring-inset, ) 0 0 0 calc(3px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .data-\[active\=true\]\:shadow-danger\/40[data-active="true"] {
    --tw-shadow-color: hsl(var(--heroui-danger) / 1);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .data-\[active\=true\]\:shadow-danger\/40[data-active="true"] {
      --tw-shadow-color: color-mix(in oklab, color-mix(in oklab, hsl(var(--heroui-danger) / 1) 40%, transparent) var(--tw-shadow-alpha), transparent);
    }
  }

  .data-\[active\=true\]\:shadow-default\/50[data-active="true"] {
    --tw-shadow-color: hsl(var(--heroui-default) / 1);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .data-\[active\=true\]\:shadow-default\/50[data-active="true"] {
      --tw-shadow-color: color-mix(in oklab, color-mix(in oklab, hsl(var(--heroui-default) / 1) 50%, transparent) var(--tw-shadow-alpha), transparent);
    }
  }

  .data-\[active\=true\]\:shadow-primary\/40[data-active="true"] {
    --tw-shadow-color: var(--primary);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .data-\[active\=true\]\:shadow-primary\/40[data-active="true"] {
      --tw-shadow-color: color-mix(in oklab, color-mix(in oklab, var(--primary) 40%, transparent) var(--tw-shadow-alpha), transparent);
    }
  }

  .data-\[active\=true\]\:shadow-secondary\/40[data-active="true"] {
    --tw-shadow-color: var(--secondary);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .data-\[active\=true\]\:shadow-secondary\/40[data-active="true"] {
      --tw-shadow-color: color-mix(in oklab, color-mix(in oklab, var(--secondary) 40%, transparent) var(--tw-shadow-alpha), transparent);
    }
  }

  .data-\[active\=true\]\:shadow-success\/40[data-active="true"] {
    --tw-shadow-color: hsl(var(--heroui-success) / 1);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .data-\[active\=true\]\:shadow-success\/40[data-active="true"] {
      --tw-shadow-color: color-mix(in oklab, color-mix(in oklab, hsl(var(--heroui-success) / 1) 40%, transparent) var(--tw-shadow-alpha), transparent);
    }
  }

  .data-\[active\=true\]\:shadow-warning\/40[data-active="true"] {
    --tw-shadow-color: hsl(var(--heroui-warning) / 1);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .data-\[active\=true\]\:shadow-warning\/40[data-active="true"] {
      --tw-shadow-color: color-mix(in oklab, color-mix(in oklab, hsl(var(--heroui-warning) / 1) 40%, transparent) var(--tw-shadow-alpha), transparent);
    }
  }

  .data-\[active\=true\]\:ring-ring\/50[data-active="true"] {
    --tw-ring-color: var(--ring);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .data-\[active\=true\]\:ring-ring\/50[data-active="true"] {
      --tw-ring-color: color-mix(in oklab, var(--ring) 50%, transparent);
    }
  }

  .data-\[active\=true\]\:after\:w-full[data-active="true"]:after {
    content: var(--tw-content);
    width: 100%;
  }

  .data-\[active\=true\]\:after\:bg-danger-400[data-active="true"]:after {
    content: var(--tw-content);
    background-color: hsl(var(--heroui-danger-400) / 1);
  }

  @media (hover: hover) {
    .data-\[active\=true\]\:hover\:bg-accent[data-active="true"]:hover {
      background-color: var(--accent);
    }
  }

  .data-\[active\=true\]\:focus\:bg-accent[data-active="true"]:focus {
    background-color: var(--accent);
  }

  .data-\[active\=true\]\:aria-invalid\:border-destructive[data-active="true"][aria-invalid="true"] {
    border-color: var(--destructive);
  }

  .data-\[active\=true\]\:aria-invalid\:ring-destructive\/20[data-active="true"][aria-invalid="true"] {
    --tw-ring-color: var(--destructive);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .data-\[active\=true\]\:aria-invalid\:ring-destructive\/20[data-active="true"][aria-invalid="true"] {
      --tw-ring-color: color-mix(in oklab, var(--destructive) 20%, transparent);
    }
  }

  .data-\[animation\=exiting\]\:opacity-0[data-animation="exiting"] {
    opacity: 0;
  }

  .data-\[arrow\=true\]\:before\:block[data-arrow="true"]:before {
    content: var(--tw-content);
    display: block;
  }

  .data-\[before\=true\]\:rotate-180[data-before="true"] {
    rotate: 180deg;
  }

  .data-\[bottom-scroll\=true\]\:\[mask-image\:linear-gradient\(180deg\,\#000_calc\(100\%_-_var\(--scroll-shadow-size\)\)\,transparent\)\][data-bottom-scroll="true"] {
    mask-image: linear-gradient(180deg, #000 calc(100% - var(--scroll-shadow-size)), transparent);
  }

  .data-\[direction\=ascending\]\:rotate-180[data-direction="ascending"] {
    rotate: 180deg;
  }

  .data-\[disabled\]\:pointer-events-none[data-disabled] {
    pointer-events: none;
  }

  .data-\[disabled\]\:opacity-50[data-disabled] {
    opacity: .5;
  }

  .data-\[disabled\=true\]\:pointer-events-none[data-disabled="true"] {
    pointer-events: none;
  }

  .data-\[disabled\=true\]\:cursor-default[data-disabled="true"] {
    cursor: default;
  }

  .data-\[disabled\=true\]\:cursor-not-allowed[data-disabled="true"] {
    cursor: not-allowed;
  }

  .data-\[disabled\=true\]\:text-default-300[data-disabled="true"] {
    color: hsl(var(--heroui-default-300) / 1);
  }

  .data-\[disabled\=true\]\:opacity-30[data-disabled="true"] {
    opacity: .3;
  }

  .data-\[disabled\=true\]\:opacity-50[data-disabled="true"] {
    opacity: .5;
  }

  .data-\[disabled\=true\]\:transition-none[data-disabled="true"] {
    transition-property: none;
  }

  .data-\[dragging\=true\]\:z-10[data-dragging="true"] {
    z-index: 10;
  }

  .data-\[dragging\=true\]\:cursor-grabbing[data-dragging="true"] {
    cursor: grabbing;
  }

  .data-\[dragging\=true\]\:opacity-80[data-dragging="true"] {
    opacity: .8;
  }

  .data-\[dragging\=true\]\:after\:scale-80[data-dragging="true"]:after {
    content: var(--tw-content);
    --tw-scale-x: .8;
    --tw-scale-y: .8;
    --tw-scale-z: .8;
    scale: var(--tw-scale-x) var(--tw-scale-y);
  }

  .data-\[dragging\=true\]\:after\:scale-100[data-dragging="true"]:after {
    content: var(--tw-content);
    --tw-scale-x: 100%;
    --tw-scale-y: 100%;
    --tw-scale-z: 100%;
    scale: var(--tw-scale-x) var(--tw-scale-y);
  }

  .data-\[editable\=true\]\:text-danger[data-editable="true"] {
    color: hsl(var(--heroui-danger) / 1);
  }

  .data-\[editable\=true\]\:text-foreground[data-editable="true"] {
    color: var(--foreground);
  }

  .data-\[editable\=true\]\:text-primary[data-editable="true"] {
    color: var(--primary);
  }

  .data-\[editable\=true\]\:text-secondary[data-editable="true"] {
    color: var(--secondary);
  }

  .data-\[editable\=true\]\:text-success-600[data-editable="true"] {
    color: hsl(var(--heroui-success-600) / 1);
  }

  .data-\[editable\=true\]\:text-warning-600[data-editable="true"] {
    color: hsl(var(--heroui-warning-600) / 1);
  }

  .data-\[editable\=true\]\:focus\:text-danger[data-editable="true"]:focus {
    color: hsl(var(--heroui-danger) / 1);
  }

  .data-\[editable\=true\]\:focus\:text-default-foreground[data-editable="true"]:focus {
    color: hsl(var(--heroui-default-foreground) / 1);
  }

  .data-\[editable\=true\]\:focus\:text-primary[data-editable="true"]:focus {
    color: var(--primary);
  }

  .data-\[editable\=true\]\:focus\:text-secondary[data-editable="true"]:focus {
    color: var(--secondary);
  }

  .data-\[editable\=true\]\:focus\:text-success[data-editable="true"]:focus {
    color: hsl(var(--heroui-success) / 1);
  }

  .data-\[editable\=true\]\:focus\:text-success-600[data-editable="true"]:focus {
    color: hsl(var(--heroui-success-600) / 1);
  }

  .data-\[editable\=true\]\:focus\:text-warning[data-editable="true"]:focus {
    color: hsl(var(--heroui-warning) / 1);
  }

  .data-\[editable\=true\]\:focus\:text-warning-600[data-editable="true"]:focus {
    color: hsl(var(--heroui-warning-600) / 1);
  }

  .data-\[error\=true\]\:text-destructive[data-error="true"] {
    color: var(--destructive);
  }

  .data-\[fill-end\=true\]\:border-e-danger[data-fill-end="true"] {
    border-inline-end-color: hsl(var(--heroui-danger) / 1);
  }

  .data-\[fill-end\=true\]\:border-e-foreground[data-fill-end="true"] {
    border-inline-end-color: var(--foreground);
  }

  .data-\[fill-end\=true\]\:border-e-primary[data-fill-end="true"] {
    border-inline-end-color: var(--primary);
  }

  .data-\[fill-end\=true\]\:border-e-secondary[data-fill-end="true"] {
    border-inline-end-color: var(--secondary);
  }

  .data-\[fill-end\=true\]\:border-e-success[data-fill-end="true"] {
    border-inline-end-color: hsl(var(--heroui-success) / 1);
  }

  .data-\[fill-end\=true\]\:border-e-warning[data-fill-end="true"] {
    border-inline-end-color: hsl(var(--heroui-warning) / 1);
  }

  .data-\[fill-end\=true\]\:border-t-danger[data-fill-end="true"] {
    border-top-color: hsl(var(--heroui-danger) / 1);
  }

  .data-\[fill-end\=true\]\:border-t-foreground[data-fill-end="true"] {
    border-top-color: var(--foreground);
  }

  .data-\[fill-end\=true\]\:border-t-primary[data-fill-end="true"] {
    border-top-color: var(--primary);
  }

  .data-\[fill-end\=true\]\:border-t-secondary[data-fill-end="true"] {
    border-top-color: var(--secondary);
  }

  .data-\[fill-end\=true\]\:border-t-success[data-fill-end="true"] {
    border-top-color: hsl(var(--heroui-success) / 1);
  }

  .data-\[fill-end\=true\]\:border-t-warning[data-fill-end="true"] {
    border-top-color: hsl(var(--heroui-warning) / 1);
  }

  .data-\[fill-start\=true\]\:border-s-danger[data-fill-start="true"] {
    border-inline-start-color: hsl(var(--heroui-danger) / 1);
  }

  .data-\[fill-start\=true\]\:border-s-foreground[data-fill-start="true"] {
    border-inline-start-color: var(--foreground);
  }

  .data-\[fill-start\=true\]\:border-s-primary[data-fill-start="true"] {
    border-inline-start-color: var(--primary);
  }

  .data-\[fill-start\=true\]\:border-s-secondary[data-fill-start="true"] {
    border-inline-start-color: var(--secondary);
  }

  .data-\[fill-start\=true\]\:border-s-success[data-fill-start="true"] {
    border-inline-start-color: hsl(var(--heroui-success) / 1);
  }

  .data-\[fill-start\=true\]\:border-s-warning[data-fill-start="true"] {
    border-inline-start-color: hsl(var(--heroui-warning) / 1);
  }

  .data-\[fill-start\=true\]\:border-b-danger[data-fill-start="true"] {
    border-bottom-color: hsl(var(--heroui-danger) / 1);
  }

  .data-\[fill-start\=true\]\:border-b-foreground[data-fill-start="true"] {
    border-bottom-color: var(--foreground);
  }

  .data-\[fill-start\=true\]\:border-b-primary[data-fill-start="true"] {
    border-bottom-color: var(--primary);
  }

  .data-\[fill-start\=true\]\:border-b-secondary[data-fill-start="true"] {
    border-bottom-color: var(--secondary);
  }

  .data-\[fill-start\=true\]\:border-b-success[data-fill-start="true"] {
    border-bottom-color: hsl(var(--heroui-success) / 1);
  }

  .data-\[fill-start\=true\]\:border-b-warning[data-fill-start="true"] {
    border-bottom-color: hsl(var(--heroui-warning) / 1);
  }

  .data-\[focus-visible\]\:outline-danger-foreground[data-focus-visible] {
    outline-color: hsl(var(--heroui-danger-foreground) / 1);
  }

  .data-\[focus-visible\]\:outline-default-foreground[data-focus-visible] {
    outline-color: hsl(var(--heroui-default-foreground) / 1);
  }

  .data-\[focus-visible\]\:outline-primary-foreground[data-focus-visible] {
    outline-color: var(--primary-foreground);
  }

  .data-\[focus-visible\]\:outline-secondary-foreground[data-focus-visible] {
    outline-color: var(--secondary-foreground);
  }

  .data-\[focus-visible\]\:outline-success-foreground[data-focus-visible] {
    outline-color: hsl(var(--heroui-success-foreground) / 1);
  }

  .data-\[focus-visible\]\:outline-warning-foreground[data-focus-visible] {
    outline-color: hsl(var(--heroui-warning-foreground) / 1);
  }

  .data-\[focus-visible\=true\]\:z-10[data-focus-visible="true"] {
    z-index: 10;
  }

  .data-\[focus-visible\=true\]\:-translate-x-3[data-focus-visible="true"] {
    --tw-translate-x: calc(var(--spacing) * -3);
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }

  .data-\[focus-visible\=true\]\:outline-2[data-focus-visible="true"] {
    outline-style: var(--tw-outline-style);
    outline-width: 2px;
  }

  .data-\[focus-visible\=true\]\:outline-offset-2[data-focus-visible="true"] {
    outline-offset: 2px;
  }

  .data-\[focus-visible\=true\]\:outline-focus[data-focus-visible="true"] {
    outline-color: hsl(var(--heroui-focus) / 1);
  }

  .data-\[focus\=true\]\:border-danger[data-focus="true"] {
    border-color: hsl(var(--heroui-danger) / 1);
  }

  .data-\[focus\=true\]\:border-default-400[data-focus="true"] {
    border-color: hsl(var(--heroui-default-400) / 1);
  }

  .data-\[focus\=true\]\:border-default-foreground[data-focus="true"] {
    border-color: hsl(var(--heroui-default-foreground) / 1);
  }

  .data-\[focus\=true\]\:border-primary[data-focus="true"] {
    border-color: var(--primary);
  }

  .data-\[focus\=true\]\:border-secondary[data-focus="true"] {
    border-color: var(--secondary);
  }

  .data-\[focus\=true\]\:border-success[data-focus="true"] {
    border-color: hsl(var(--heroui-success) / 1);
  }

  .data-\[focus\=true\]\:border-warning[data-focus="true"] {
    border-color: hsl(var(--heroui-warning) / 1);
  }

  .data-\[focus\=true\]\:after\:w-full[data-focus="true"]:after {
    content: var(--tw-content);
    width: 100%;
  }

  .data-\[focused\=true\]\:z-10[data-focused="true"] {
    z-index: 10;
  }

  .data-\[has-end-content\=true\]\:pe-1\.5[data-has-end-content="true"] {
    padding-inline-end: calc(var(--spacing) * 1.5);
  }

  .data-\[has-helper\=true\]\:items-start[data-has-helper="true"] {
    align-items: flex-start;
  }

  .data-\[has-multiple-rows\=true\]\:rounded-large[data-has-multiple-rows="true"] {
    border-radius: var(--heroui-radius-large);
  }

  .data-\[has-start-content\=true\]\:ps-1\.5[data-has-start-content="true"] {
    padding-inline-start: calc(var(--spacing) * 1.5);
  }

  .data-\[has-title\=true\]\:pt-1[data-has-title="true"] {
    padding-top: calc(var(--spacing) * 1);
  }

  .data-\[has-value\=true\]\:text-default-foreground[data-has-value="true"] {
    color: hsl(var(--heroui-default-foreground) / 1);
  }

  .data-\[hidden\=true\]\:hidden[data-hidden="true"] {
    display: none;
  }

  .data-\[hide-scroll\=true\]\:scrollbar-hide[data-hide-scroll="true"] {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }

  .data-\[hide-scroll\=true\]\:scrollbar-hide[data-hide-scroll="true"]::-webkit-scrollbar {
    display: none;
  }

  .data-\[hover\]\:bg-danger-50[data-hover] {
    background-color: hsl(var(--heroui-danger-50) / 1);
  }

  .data-\[hover\]\:bg-danger-200[data-hover] {
    background-color: hsl(var(--heroui-danger-200) / 1);
  }

  .data-\[hover\]\:bg-default-100[data-hover] {
    background-color: hsl(var(--heroui-default-100) / 1);
  }

  .data-\[hover\]\:bg-primary-50[data-hover] {
    background-color: hsl(var(--heroui-primary-50) / 1);
  }

  .data-\[hover\]\:bg-primary-200[data-hover] {
    background-color: hsl(var(--heroui-primary-200) / 1);
  }

  .data-\[hover\]\:bg-secondary-50[data-hover] {
    background-color: hsl(var(--heroui-secondary-50) / 1);
  }

  .data-\[hover\]\:bg-secondary-200[data-hover] {
    background-color: hsl(var(--heroui-secondary-200) / 1);
  }

  .data-\[hover\]\:bg-success-50[data-hover] {
    background-color: hsl(var(--heroui-success-50) / 1);
  }

  .data-\[hover\]\:bg-success-200[data-hover] {
    background-color: hsl(var(--heroui-success-200) / 1);
  }

  .data-\[hover\]\:bg-warning-100[data-hover] {
    background-color: hsl(var(--heroui-warning-100) / 1);
  }

  .data-\[hover\]\:bg-warning-200[data-hover] {
    background-color: hsl(var(--heroui-warning-200) / 1);
  }

  .data-\[hover-unselected\=true\]\:opacity-disabled[data-hover-unselected="true"] {
    opacity: var(--heroui-disabled-opacity);
  }

  .data-\[hover\=true\]\:-translate-x-3[data-hover="true"] {
    --tw-translate-x: calc(var(--spacing) * -3);
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }

  .data-\[hover\=true\]\:translate-x-0[data-hover="true"] {
    --tw-translate-x: calc(var(--spacing) * 0);
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }

  .data-\[hover\=true\]\:border-danger[data-hover="true"] {
    border-color: hsl(var(--heroui-danger) / 1);
  }

  .data-\[hover\=true\]\:border-default[data-hover="true"] {
    border-color: hsl(var(--heroui-default) / 1);
  }

  .data-\[hover\=true\]\:border-default-400[data-hover="true"] {
    border-color: hsl(var(--heroui-default-400) / 1);
  }

  .data-\[hover\=true\]\:border-primary[data-hover="true"] {
    border-color: var(--primary);
  }

  .data-\[hover\=true\]\:border-secondary[data-hover="true"] {
    border-color: var(--secondary);
  }

  .data-\[hover\=true\]\:border-success[data-hover="true"] {
    border-color: hsl(var(--heroui-success) / 1);
  }

  .data-\[hover\=true\]\:border-warning[data-hover="true"] {
    border-color: hsl(var(--heroui-warning) / 1);
  }

  .data-\[hover\=true\]\:\!bg-danger[data-hover="true"] {
    background-color: hsl(var(--heroui-danger) / 1) !important;
  }

  .data-\[hover\=true\]\:\!bg-danger-100[data-hover="true"] {
    background-color: hsl(var(--heroui-danger-100) / 1) !important;
  }

  .data-\[hover\=true\]\:\!bg-default[data-hover="true"] {
    background-color: hsl(var(--heroui-default) / 1) !important;
  }

  .data-\[hover\=true\]\:\!bg-primary[data-hover="true"] {
    background-color: var(--primary) !important;
  }

  .data-\[hover\=true\]\:\!bg-secondary[data-hover="true"] {
    background-color: var(--secondary) !important;
  }

  .data-\[hover\=true\]\:\!bg-success[data-hover="true"] {
    background-color: hsl(var(--heroui-success) / 1) !important;
  }

  .data-\[hover\=true\]\:\!bg-warning[data-hover="true"] {
    background-color: hsl(var(--heroui-warning) / 1) !important;
  }

  .data-\[hover\=true\]\:bg-content2[data-hover="true"] {
    background-color: hsl(var(--heroui-content2) / 1);
  }

  .data-\[hover\=true\]\:bg-danger[data-hover="true"] {
    background-color: hsl(var(--heroui-danger) / 1);
  }

  .data-\[hover\=true\]\:bg-danger-50[data-hover="true"] {
    background-color: hsl(var(--heroui-danger-50) / 1);
  }

  .data-\[hover\=true\]\:bg-danger-100[data-hover="true"] {
    background-color: hsl(var(--heroui-danger-100) / 1);
  }

  .data-\[hover\=true\]\:bg-danger\/20[data-hover="true"] {
    background-color: hsl(var(--heroui-danger) / 1);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .data-\[hover\=true\]\:bg-danger\/20[data-hover="true"] {
      background-color: color-mix(in oklab, hsl(var(--heroui-danger) / 1) 20%, transparent);
    }
  }

  .data-\[hover\=true\]\:bg-default[data-hover="true"] {
    background-color: hsl(var(--heroui-default) / 1);
  }

  .data-\[hover\=true\]\:bg-default-100[data-hover="true"] {
    background-color: hsl(var(--heroui-default-100) / 1);
  }

  .data-\[hover\=true\]\:bg-default-200[data-hover="true"] {
    background-color: hsl(var(--heroui-default-200) / 1);
  }

  .data-\[hover\=true\]\:bg-default\/40[data-hover="true"] {
    background-color: hsl(var(--heroui-default) / 1);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .data-\[hover\=true\]\:bg-default\/40[data-hover="true"] {
      background-color: color-mix(in oklab, hsl(var(--heroui-default) / 1) 40%, transparent);
    }
  }

  .data-\[hover\=true\]\:bg-foreground-200[data-hover="true"] {
    background-color: hsl(var(--heroui-foreground-200) / 1);
  }

  .data-\[hover\=true\]\:bg-primary[data-hover="true"] {
    background-color: var(--primary);
  }

  .data-\[hover\=true\]\:bg-primary-50[data-hover="true"] {
    background-color: hsl(var(--heroui-primary-50) / 1);
  }

  .data-\[hover\=true\]\:bg-primary\/20[data-hover="true"] {
    background-color: var(--primary);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .data-\[hover\=true\]\:bg-primary\/20[data-hover="true"] {
      background-color: color-mix(in oklab, var(--primary) 20%, transparent);
    }
  }

  .data-\[hover\=true\]\:bg-secondary[data-hover="true"] {
    background-color: var(--secondary);
  }

  .data-\[hover\=true\]\:bg-secondary-50[data-hover="true"] {
    background-color: hsl(var(--heroui-secondary-50) / 1);
  }

  .data-\[hover\=true\]\:bg-secondary\/20[data-hover="true"] {
    background-color: var(--secondary);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .data-\[hover\=true\]\:bg-secondary\/20[data-hover="true"] {
      background-color: color-mix(in oklab, var(--secondary) 20%, transparent);
    }
  }

  .data-\[hover\=true\]\:bg-success[data-hover="true"] {
    background-color: hsl(var(--heroui-success) / 1);
  }

  .data-\[hover\=true\]\:bg-success-50[data-hover="true"] {
    background-color: hsl(var(--heroui-success-50) / 1);
  }

  .data-\[hover\=true\]\:bg-success-100[data-hover="true"] {
    background-color: hsl(var(--heroui-success-100) / 1);
  }

  .data-\[hover\=true\]\:bg-success\/20[data-hover="true"] {
    background-color: hsl(var(--heroui-success) / 1);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .data-\[hover\=true\]\:bg-success\/20[data-hover="true"] {
      background-color: color-mix(in oklab, hsl(var(--heroui-success) / 1) 20%, transparent);
    }
  }

  .data-\[hover\=true\]\:bg-transparent[data-hover="true"] {
    background-color: #0000;
  }

  .data-\[hover\=true\]\:bg-warning[data-hover="true"] {
    background-color: hsl(var(--heroui-warning) / 1);
  }

  .data-\[hover\=true\]\:bg-warning-50[data-hover="true"] {
    background-color: hsl(var(--heroui-warning-50) / 1);
  }

  .data-\[hover\=true\]\:bg-warning-100[data-hover="true"] {
    background-color: hsl(var(--heroui-warning-100) / 1);
  }

  .data-\[hover\=true\]\:bg-warning\/20[data-hover="true"] {
    background-color: hsl(var(--heroui-warning) / 1);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .data-\[hover\=true\]\:bg-warning\/20[data-hover="true"] {
      background-color: color-mix(in oklab, hsl(var(--heroui-warning) / 1) 20%, transparent);
    }
  }

  .data-\[hover\=true\]\:\!text-danger-foreground[data-hover="true"] {
    color: hsl(var(--heroui-danger-foreground) / 1) !important;
  }

  .data-\[hover\=true\]\:\!text-primary-foreground[data-hover="true"] {
    color: var(--primary-foreground) !important;
  }

  .data-\[hover\=true\]\:\!text-secondary-foreground[data-hover="true"] {
    color: var(--secondary-foreground) !important;
  }

  .data-\[hover\=true\]\:\!text-success-foreground[data-hover="true"] {
    color: hsl(var(--heroui-success-foreground) / 1) !important;
  }

  .data-\[hover\=true\]\:\!text-warning-foreground[data-hover="true"] {
    color: hsl(var(--heroui-warning-foreground) / 1) !important;
  }

  .data-\[hover\=true\]\:text-danger[data-hover="true"] {
    color: hsl(var(--heroui-danger) / 1);
  }

  .data-\[hover\=true\]\:text-danger-500[data-hover="true"] {
    color: hsl(var(--heroui-danger-500) / 1);
  }

  .data-\[hover\=true\]\:text-danger-foreground[data-hover="true"] {
    color: hsl(var(--heroui-danger-foreground) / 1);
  }

  .data-\[hover\=true\]\:text-default-500[data-hover="true"] {
    color: hsl(var(--heroui-default-500) / 1);
  }

  .data-\[hover\=true\]\:text-default-foreground[data-hover="true"] {
    color: hsl(var(--heroui-default-foreground) / 1);
  }

  .data-\[hover\=true\]\:text-foreground-400[data-hover="true"] {
    color: hsl(var(--heroui-foreground-400) / 1);
  }

  .data-\[hover\=true\]\:text-foreground-600[data-hover="true"] {
    color: hsl(var(--heroui-foreground-600) / 1);
  }

  .data-\[hover\=true\]\:text-primary[data-hover="true"] {
    color: var(--primary);
  }

  .data-\[hover\=true\]\:text-primary-400[data-hover="true"] {
    color: hsl(var(--heroui-primary-400) / 1);
  }

  .data-\[hover\=true\]\:text-primary-foreground[data-hover="true"] {
    color: var(--primary-foreground);
  }

  .data-\[hover\=true\]\:text-secondary[data-hover="true"] {
    color: var(--secondary);
  }

  .data-\[hover\=true\]\:text-secondary-400[data-hover="true"] {
    color: hsl(var(--heroui-secondary-400) / 1);
  }

  .data-\[hover\=true\]\:text-secondary-foreground[data-hover="true"] {
    color: var(--secondary-foreground);
  }

  .data-\[hover\=true\]\:text-success[data-hover="true"] {
    color: hsl(var(--heroui-success) / 1);
  }

  .data-\[hover\=true\]\:text-success-600[data-hover="true"] {
    color: hsl(var(--heroui-success-600) / 1);
  }

  .data-\[hover\=true\]\:text-success-foreground[data-hover="true"] {
    color: hsl(var(--heroui-success-foreground) / 1);
  }

  .data-\[hover\=true\]\:text-warning[data-hover="true"] {
    color: hsl(var(--heroui-warning) / 1);
  }

  .data-\[hover\=true\]\:text-warning-600[data-hover="true"] {
    color: hsl(var(--heroui-warning-600) / 1);
  }

  .data-\[hover\=true\]\:text-warning-foreground[data-hover="true"] {
    color: hsl(var(--heroui-warning-foreground) / 1);
  }

  .data-\[hover\=true\]\:opacity-70[data-hover="true"] {
    opacity: .7;
  }

  .data-\[hover\=true\]\:opacity-hover[data-hover="true"] {
    opacity: var(--heroui-hover-opacity);
  }

  .data-\[hover\=true\]\:shadow-lg[data-hover="true"] {
    --tw-shadow: 0 10px 15px -3px var(--tw-shadow-color, #0000001a), 0 4px 6px -4px var(--tw-shadow-color, #0000001a);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .data-\[hover\=true\]\:shadow-danger\/30[data-hover="true"] {
    --tw-shadow-color: hsl(var(--heroui-danger) / 1);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .data-\[hover\=true\]\:shadow-danger\/30[data-hover="true"] {
      --tw-shadow-color: color-mix(in oklab, color-mix(in oklab, hsl(var(--heroui-danger) / 1) 30%, transparent) var(--tw-shadow-alpha), transparent);
    }
  }

  .data-\[hover\=true\]\:shadow-default\/50[data-hover="true"] {
    --tw-shadow-color: hsl(var(--heroui-default) / 1);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .data-\[hover\=true\]\:shadow-default\/50[data-hover="true"] {
      --tw-shadow-color: color-mix(in oklab, color-mix(in oklab, hsl(var(--heroui-default) / 1) 50%, transparent) var(--tw-shadow-alpha), transparent);
    }
  }

  .data-\[hover\=true\]\:shadow-primary\/30[data-hover="true"] {
    --tw-shadow-color: var(--primary);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .data-\[hover\=true\]\:shadow-primary\/30[data-hover="true"] {
      --tw-shadow-color: color-mix(in oklab, color-mix(in oklab, var(--primary) 30%, transparent) var(--tw-shadow-alpha), transparent);
    }
  }

  .data-\[hover\=true\]\:shadow-secondary\/30[data-hover="true"] {
    --tw-shadow-color: var(--secondary);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .data-\[hover\=true\]\:shadow-secondary\/30[data-hover="true"] {
      --tw-shadow-color: color-mix(in oklab, color-mix(in oklab, var(--secondary) 30%, transparent) var(--tw-shadow-alpha), transparent);
    }
  }

  .data-\[hover\=true\]\:shadow-success\/30[data-hover="true"] {
    --tw-shadow-color: hsl(var(--heroui-success) / 1);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .data-\[hover\=true\]\:shadow-success\/30[data-hover="true"] {
      --tw-shadow-color: color-mix(in oklab, color-mix(in oklab, hsl(var(--heroui-success) / 1) 30%, transparent) var(--tw-shadow-alpha), transparent);
    }
  }

  .data-\[hover\=true\]\:shadow-warning\/30[data-hover="true"] {
    --tw-shadow-color: hsl(var(--heroui-warning) / 1);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .data-\[hover\=true\]\:shadow-warning\/30[data-hover="true"] {
      --tw-shadow-color: color-mix(in oklab, color-mix(in oklab, hsl(var(--heroui-warning) / 1) 30%, transparent) var(--tw-shadow-alpha), transparent);
    }
  }

  .data-\[hover\=true\]\:transition-colors[data-hover="true"] {
    transition-property: color, background-color, border-color, outline-color, text-decoration-color, fill, stroke, --tw-gradient-from, --tw-gradient-via, --tw-gradient-to;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }

  .data-\[in-range\=false\]\:bg-default-200[data-in-range="false"] {
    background-color: hsl(var(--heroui-default-200) / 1);
  }

  .data-\[in-range\=true\]\:bg-background\/50[data-in-range="true"] {
    background-color: var(--background);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .data-\[in-range\=true\]\:bg-background\/50[data-in-range="true"] {
      background-color: color-mix(in oklab, var(--background) 50%, transparent);
    }
  }

  .data-\[in-range\=true\]\:bg-danger[data-in-range="true"] {
    background-color: hsl(var(--heroui-danger) / 1);
  }

  .data-\[in-range\=true\]\:bg-foreground[data-in-range="true"] {
    background-color: var(--foreground);
  }

  .data-\[in-range\=true\]\:bg-primary[data-in-range="true"] {
    background-color: var(--primary);
  }

  .data-\[in-range\=true\]\:bg-secondary[data-in-range="true"] {
    background-color: var(--secondary);
  }

  .data-\[in-range\=true\]\:bg-success[data-in-range="true"] {
    background-color: hsl(var(--heroui-success) / 1);
  }

  .data-\[in-range\=true\]\:bg-warning[data-in-range="true"] {
    background-color: hsl(var(--heroui-warning) / 1);
  }

  .data-\[in-range\=true\]\:opacity-100[data-in-range="true"] {
    opacity: 1;
  }

  .data-\[inert\=true\]\:hidden[data-inert="true"] {
    display: none;
  }

  .data-\[inset\]\:pl-8[data-inset] {
    padding-left: calc(var(--spacing) * 8);
  }

  .data-\[invalid\=true\]\:text-danger-300[data-invalid="true"] {
    color: hsl(var(--heroui-danger-300) / 1);
  }

  .data-\[invalid\=true\]\:focus\:bg-danger-400\/50[data-invalid="true"]:focus {
    background-color: hsl(var(--heroui-danger-400) / 1);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .data-\[invalid\=true\]\:focus\:bg-danger-400\/50[data-invalid="true"]:focus {
      background-color: color-mix(in oklab, hsl(var(--heroui-danger-400) / 1) 50%, transparent);
    }
  }

  .data-\[invalid\=true\]\:data-\[editable\=true\]\:text-danger[data-invalid="true"][data-editable="true"] {
    color: hsl(var(--heroui-danger) / 1);
  }

  .data-\[invalid\=true\]\:data-\[editable\=true\]\:focus\:text-danger[data-invalid="true"][data-editable="true"]:focus {
    color: hsl(var(--heroui-danger) / 1);
  }

  .data-\[invisible\=true\]\:scale-0[data-invisible="true"] {
    --tw-scale-x: 0%;
    --tw-scale-y: 0%;
    --tw-scale-z: 0%;
    scale: var(--tw-scale-x) var(--tw-scale-y);
  }

  .data-\[invisible\=true\]\:opacity-0[data-invisible="true"] {
    opacity: 0;
  }

  .data-\[justify\=center\]\:justify-center[data-justify="center"] {
    justify-content: center;
  }

  .data-\[justify\=end\]\:flex-grow[data-justify="end"] {
    flex-grow: 1;
  }

  .data-\[justify\=end\]\:basis-0[data-justify="end"] {
    flex-basis: calc(var(--spacing) * 0);
  }

  .data-\[justify\=end\]\:justify-end[data-justify="end"] {
    justify-content: flex-end;
  }

  .data-\[justify\=start\]\:flex-grow[data-justify="start"] {
    flex-grow: 1;
  }

  .data-\[justify\=start\]\:basis-0[data-justify="start"] {
    flex-basis: calc(var(--spacing) * 0);
  }

  .data-\[justify\=start\]\:justify-start[data-justify="start"] {
    justify-content: flex-start;
  }

  .data-\[label-orientation\=vertical\]\:w-full[data-label-orientation="vertical"] {
    width: 100%;
  }

  .data-\[label-orientation\=vertical\]\:flex-col[data-label-orientation="vertical"] {
    flex-direction: column;
  }

  .data-\[label-orientation\=vertical\]\:justify-center[data-label-orientation="vertical"] {
    justify-content: center;
  }

  .data-\[left-right-scroll\=true\]\:\[mask-image\:linear-gradient\(to_right\,\#000\,\#000\,transparent_0\,\#000_var\(--scroll-shadow-size\)\,\#000_calc\(100\%_-_var\(--scroll-shadow-size\)\)\,transparent\)\][data-left-right-scroll="true"] {
    mask-image: linear-gradient(to right, #000, #000, transparent 0, #000 var(--scroll-shadow-size), #000 calc(100% - var(--scroll-shadow-size)), transparent);
  }

  .data-\[left-scroll\=true\]\:\[mask-image\:linear-gradient\(270deg\,\#000_calc\(100\%_-_var\(--scroll-shadow-size\)\)\,transparent\)\][data-left-scroll="true"] {
    mask-image: linear-gradient(270deg, #000 calc(100% - var(--scroll-shadow-size)), transparent);
  }

  .data-\[loaded\=true\]\:pointer-events-auto[data-loaded="true"] {
    pointer-events: auto;
  }

  .data-\[loaded\=true\]\:overflow-visible[data-loaded="true"] {
    overflow: visible;
  }

  .data-\[loaded\=true\]\:\!bg-transparent[data-loaded="true"] {
    background-color: #0000 !important;
  }

  .data-\[loaded\=true\]\:opacity-100[data-loaded="true"] {
    opacity: 1;
  }

  .data-\[loaded\=true\]\:before\:-z-10[data-loaded="true"]:before {
    content: var(--tw-content);
    z-index: calc(10 * -1);
  }

  .data-\[loaded\=true\]\:before\:animate-none[data-loaded="true"]:before {
    content: var(--tw-content);
    animation: none;
  }

  .data-\[loaded\=true\]\:before\:opacity-0[data-loaded="true"]:before {
    content: var(--tw-content);
    opacity: 0;
  }

  .data-\[loaded\=true\]\:after\:opacity-0[data-loaded="true"]:after {
    content: var(--tw-content);
    opacity: 0;
  }

  .data-\[menu-open\=true\]\:border-none[data-menu-open="true"] {
    --tw-border-style: none;
    border-style: none;
  }

  .data-\[menu-open\=true\]\:backdrop-blur-xl[data-menu-open="true"] {
    --tw-backdrop-blur: blur(var(--blur-xl));
    backdrop-filter: var(--tw-backdrop-blur, ) var(--tw-backdrop-brightness, ) var(--tw-backdrop-contrast, ) var(--tw-backdrop-grayscale, ) var(--tw-backdrop-hue-rotate, ) var(--tw-backdrop-invert, ) var(--tw-backdrop-opacity, ) var(--tw-backdrop-saturate, ) var(--tw-backdrop-sepia, );
  }

  .data-\[motion\=from-end\]\:slide-in-from-right-52[data-motion="from-end"] {
    --tw-enter-translate-x: calc(52 * var(--spacing));
  }

  .data-\[motion\=from-start\]\:slide-in-from-left-52[data-motion="from-start"] {
    --tw-enter-translate-x: calc(52 * var(--spacing) * -1);
  }

  .data-\[motion\=to-end\]\:slide-out-to-right-52[data-motion="to-end"] {
    --tw-exit-translate-x: calc(52 * var(--spacing));
  }

  .data-\[motion\=to-start\]\:slide-out-to-left-52[data-motion="to-start"] {
    --tw-exit-translate-x: calc(52 * var(--spacing) * -1);
  }

  .data-\[motion\^\=from-\]\:animate-in[data-motion^="from-"] {
    animation: enter var(--tw-duration, .15s) var(--tw-ease, ease);
  }

  .data-\[motion\^\=from-\]\:fade-in[data-motion^="from-"] {
    --tw-enter-opacity: 0;
  }

  .data-\[motion\^\=to-\]\:animate-out[data-motion^="to-"] {
    animation: exit var(--tw-duration, .15s) var(--tw-ease, ease);
  }

  .data-\[motion\^\=to-\]\:fade-out[data-motion^="to-"] {
    --tw-exit-opacity: 0;
  }

  .data-\[moving\]\:opacity-100[data-moving] {
    opacity: 1;
  }

  .data-\[moving\=true\]\:transition-transform[data-moving="true"] {
    transition-property: transform, translate, scale, rotate;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }

  .data-\[open\=true\]\:block[data-open="true"] {
    display: block;
  }

  .data-\[open\=true\]\:flex[data-open="true"] {
    display: flex;
  }

  .data-\[open\=true\]\:-rotate-90[data-open="true"] {
    rotate: -90deg;
  }

  .data-\[open\=true\]\:rotate-180[data-open="true"] {
    rotate: 180deg;
  }

  .data-\[open\=true\]\:border-danger[data-open="true"] {
    border-color: hsl(var(--heroui-danger) / 1);
  }

  .data-\[open\=true\]\:border-default-400[data-open="true"] {
    border-color: hsl(var(--heroui-default-400) / 1);
  }

  .data-\[open\=true\]\:border-default-foreground[data-open="true"] {
    border-color: hsl(var(--heroui-default-foreground) / 1);
  }

  .data-\[open\=true\]\:border-primary[data-open="true"] {
    border-color: var(--primary);
  }

  .data-\[open\=true\]\:border-secondary[data-open="true"] {
    border-color: var(--secondary);
  }

  .data-\[open\=true\]\:border-success[data-open="true"] {
    border-color: hsl(var(--heroui-success) / 1);
  }

  .data-\[open\=true\]\:border-warning[data-open="true"] {
    border-color: hsl(var(--heroui-warning) / 1);
  }

  .data-\[open\=true\]\:after\:w-full[data-open="true"]:after {
    content: var(--tw-content);
    width: 100%;
  }

  .data-\[orientation\=horizontal\]\:h-1\.5[data-orientation="horizontal"] {
    height: calc(var(--spacing) * 1.5);
  }

  .data-\[orientation\=horizontal\]\:h-full[data-orientation="horizontal"] {
    height: 100%;
  }

  .data-\[orientation\=horizontal\]\:h-px[data-orientation="horizontal"] {
    height: 1px;
  }

  .data-\[orientation\=horizontal\]\:w-full[data-orientation="horizontal"] {
    width: 100%;
  }

  .data-\[orientation\=horizontal\]\:flex-row[data-orientation="horizontal"] {
    flex-direction: row;
  }

  .data-\[orientation\=vertical\]\:h-4[data-orientation="vertical"] {
    height: calc(var(--spacing) * 4);
  }

  .data-\[orientation\=vertical\]\:h-full[data-orientation="vertical"] {
    height: 100%;
  }

  .data-\[orientation\=vertical\]\:min-h-44[data-orientation="vertical"] {
    min-height: calc(var(--spacing) * 44);
  }

  .data-\[orientation\=vertical\]\:w-1\.5[data-orientation="vertical"] {
    width: calc(var(--spacing) * 1.5);
  }

  .data-\[orientation\=vertical\]\:w-auto[data-orientation="vertical"] {
    width: auto;
  }

  .data-\[orientation\=vertical\]\:w-full[data-orientation="vertical"] {
    width: 100%;
  }

  .data-\[orientation\=vertical\]\:w-px[data-orientation="vertical"] {
    width: 1px;
  }

  .data-\[orientation\=vertical\]\:flex-col[data-orientation="vertical"] {
    flex-direction: column;
  }

  .data-\[outside-month\=true\]\:before\:hidden[data-outside-month="true"]:before {
    content: var(--tw-content);
    display: none;
  }

  .data-\[disabled\=true\]\:data-\[outside-month\=true\]\:opacity-0[data-disabled="true"][data-outside-month="true"] {
    opacity: 0;
  }

  .data-\[panel-group-direction\=vertical\]\:h-px[data-panel-group-direction="vertical"] {
    height: 1px;
  }

  .data-\[panel-group-direction\=vertical\]\:w-full[data-panel-group-direction="vertical"] {
    width: 100%;
  }

  .data-\[panel-group-direction\=vertical\]\:flex-col[data-panel-group-direction="vertical"] {
    flex-direction: column;
  }

  .data-\[panel-group-direction\=vertical\]\:after\:left-0[data-panel-group-direction="vertical"]:after {
    content: var(--tw-content);
    left: calc(var(--spacing) * 0);
  }

  .data-\[panel-group-direction\=vertical\]\:after\:h-1[data-panel-group-direction="vertical"]:after {
    content: var(--tw-content);
    height: calc(var(--spacing) * 1);
  }

  .data-\[panel-group-direction\=vertical\]\:after\:w-full[data-panel-group-direction="vertical"]:after {
    content: var(--tw-content);
    width: 100%;
  }

  .data-\[panel-group-direction\=vertical\]\:after\:translate-x-0[data-panel-group-direction="vertical"]:after {
    content: var(--tw-content);
    --tw-translate-x: calc(var(--spacing) * 0);
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }

  .data-\[panel-group-direction\=vertical\]\:after\:-translate-y-1\/2[data-panel-group-direction="vertical"]:after {
    content: var(--tw-content);
    --tw-translate-y: calc(calc(1 / 2 * 100%) * -1);
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }

  .data-\[placeholder\]\:text-muted-foreground[data-placeholder] {
    color: var(--muted-foreground);
  }

  .data-\[editable\=true\]\:data-\[placeholder\=true\]\:text-danger-300[data-editable="true"][data-placeholder="true"] {
    color: hsl(var(--heroui-danger-300) / 1);
  }

  .data-\[editable\=true\]\:data-\[placeholder\=true\]\:text-foreground-500[data-editable="true"][data-placeholder="true"] {
    color: hsl(var(--heroui-foreground-500) / 1);
  }

  .data-\[editable\=true\]\:data-\[placeholder\=true\]\:text-primary-300[data-editable="true"][data-placeholder="true"] {
    color: hsl(var(--heroui-primary-300) / 1);
  }

  .data-\[editable\=true\]\:data-\[placeholder\=true\]\:text-secondary-300[data-editable="true"][data-placeholder="true"] {
    color: hsl(var(--heroui-secondary-300) / 1);
  }

  .data-\[editable\=true\]\:data-\[placeholder\=true\]\:text-success-400[data-editable="true"][data-placeholder="true"] {
    color: hsl(var(--heroui-success-400) / 1);
  }

  .data-\[editable\=true\]\:data-\[placeholder\=true\]\:text-warning-400[data-editable="true"][data-placeholder="true"] {
    color: hsl(var(--heroui-warning-400) / 1);
  }

  .data-\[placement\=bottom\]\:before\:-top-\[calc\(theme\(spacing\.5\)\/4_-_1\.5px\)\][data-placement="bottom"]:before {
    content: var(--tw-content);
    top: calc(-.3125rem + 1.5px);
  }

  .data-\[placement\=bottom\]\:before\:left-1\/2[data-placement="bottom"]:before {
    content: var(--tw-content);
    left: 50%;
  }

  .data-\[placement\=bottom\]\:before\:-translate-x-1\/2[data-placement="bottom"]:before {
    content: var(--tw-content);
    --tw-translate-x: calc(calc(1 / 2 * 100%) * -1);
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }

  .data-\[placement\=bottom-center\]\:fixed[data-placement="bottom-center"] {
    position: fixed;
  }

  .data-\[placement\=bottom-center\]\:right-0[data-placement="bottom-center"] {
    right: calc(var(--spacing) * 0);
  }

  .data-\[placement\=bottom-center\]\:bottom-0[data-placement="bottom-center"] {
    bottom: calc(var(--spacing) * 0);
  }

  .data-\[placement\=bottom-center\]\:left-0[data-placement="bottom-center"] {
    left: calc(var(--spacing) * 0);
  }

  .data-\[placement\=bottom-center\]\:left-1\/2[data-placement="bottom-center"] {
    left: 50%;
  }

  .data-\[placement\=bottom-center\]\:flex[data-placement="bottom-center"] {
    display: flex;
  }

  .data-\[placement\=bottom-center\]\:-translate-x-1\/2[data-placement="bottom-center"] {
    --tw-translate-x: calc(calc(1 / 2 * 100%) * -1);
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }

  .data-\[placement\=bottom-center\]\:flex-col[data-placement="bottom-center"] {
    flex-direction: column;
  }

  .data-\[placement\=bottom-end\]\:before\:-top-\[calc\(theme\(spacing\.5\)\/4_-_1\.5px\)\][data-placement="bottom-end"]:before {
    content: var(--tw-content);
    top: calc(-.3125rem + 1.5px);
  }

  .data-\[placement\=bottom-end\]\:before\:right-3[data-placement="bottom-end"]:before {
    content: var(--tw-content);
    right: calc(var(--spacing) * 3);
  }

  .data-\[placement\=bottom-left\]\:fixed[data-placement="bottom-left"] {
    position: fixed;
  }

  .data-\[placement\=bottom-left\]\:bottom-0[data-placement="bottom-left"] {
    bottom: calc(var(--spacing) * 0);
  }

  .data-\[placement\=bottom-left\]\:left-0[data-placement="bottom-left"] {
    left: calc(var(--spacing) * 0);
  }

  .data-\[placement\=bottom-left\]\:mx-auto[data-placement="bottom-left"] {
    margin-inline: auto;
  }

  .data-\[placement\=bottom-left\]\:flex[data-placement="bottom-left"] {
    display: flex;
  }

  .data-\[placement\=bottom-left\]\:flex-col[data-placement="bottom-left"] {
    flex-direction: column;
  }

  .data-\[placement\=bottom-right\]\:fixed[data-placement="bottom-right"] {
    position: fixed;
  }

  .data-\[placement\=bottom-right\]\:right-0[data-placement="bottom-right"] {
    right: calc(var(--spacing) * 0);
  }

  .data-\[placement\=bottom-right\]\:bottom-0[data-placement="bottom-right"] {
    bottom: calc(var(--spacing) * 0);
  }

  .data-\[placement\=bottom-right\]\:mx-auto[data-placement="bottom-right"] {
    margin-inline: auto;
  }

  .data-\[placement\=bottom-right\]\:flex[data-placement="bottom-right"] {
    display: flex;
  }

  .data-\[placement\=bottom-right\]\:flex-col[data-placement="bottom-right"] {
    flex-direction: column;
  }

  .data-\[placement\=bottom-start\]\:before\:-top-\[calc\(theme\(spacing\.5\)\/4_-_1\.5px\)\][data-placement="bottom-start"]:before {
    content: var(--tw-content);
    top: calc(-.3125rem + 1.5px);
  }

  .data-\[placement\=bottom-start\]\:before\:left-3[data-placement="bottom-start"]:before {
    content: var(--tw-content);
    left: calc(var(--spacing) * 3);
  }

  .data-\[placement\=left\]\:before\:top-1\/2[data-placement="left"]:before {
    content: var(--tw-content);
    top: 50%;
  }

  .data-\[placement\=left\]\:before\:-right-\[calc\(theme\(spacing\.5\)\/4_-_2px\)\][data-placement="left"]:before {
    content: var(--tw-content);
    right: calc(-.3125rem + 2px);
  }

  .data-\[placement\=left\]\:before\:-translate-y-1\/2[data-placement="left"]:before {
    content: var(--tw-content);
    --tw-translate-y: calc(calc(1 / 2 * 100%) * -1);
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }

  .data-\[placement\=left-end\]\:before\:-right-\[calc\(theme\(spacing\.5\)\/4_-_3px\)\][data-placement="left-end"]:before {
    content: var(--tw-content);
    right: calc(-.3125rem + 3px);
  }

  .data-\[placement\=left-end\]\:before\:bottom-1\/4[data-placement="left-end"]:before {
    content: var(--tw-content);
    bottom: 25%;
  }

  .data-\[placement\=left-start\]\:before\:top-1\/4[data-placement="left-start"]:before {
    content: var(--tw-content);
    top: 25%;
  }

  .data-\[placement\=left-start\]\:before\:-right-\[calc\(theme\(spacing\.5\)\/4_-_3px\)\][data-placement="left-start"]:before {
    content: var(--tw-content);
    right: calc(-.3125rem + 3px);
  }

  .data-\[placement\=right\]\:before\:top-1\/2[data-placement="right"]:before {
    content: var(--tw-content);
    top: 50%;
  }

  .data-\[placement\=right\]\:before\:-left-\[calc\(theme\(spacing\.5\)\/4_-_2px\)\][data-placement="right"]:before {
    content: var(--tw-content);
    left: calc(-.3125rem + 2px);
  }

  .data-\[placement\=right\]\:before\:-translate-y-1\/2[data-placement="right"]:before {
    content: var(--tw-content);
    --tw-translate-y: calc(calc(1 / 2 * 100%) * -1);
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }

  .data-\[placement\=right-end\]\:before\:bottom-1\/4[data-placement="right-end"]:before {
    content: var(--tw-content);
    bottom: 25%;
  }

  .data-\[placement\=right-end\]\:before\:-left-\[calc\(theme\(spacing\.5\)\/4_-_3px\)\][data-placement="right-end"]:before {
    content: var(--tw-content);
    left: calc(-.3125rem + 3px);
  }

  .data-\[placement\=right-start\]\:before\:top-1\/4[data-placement="right-start"]:before {
    content: var(--tw-content);
    top: 25%;
  }

  .data-\[placement\=right-start\]\:before\:-left-\[calc\(theme\(spacing\.5\)\/4_-_3px\)\][data-placement="right-start"]:before {
    content: var(--tw-content);
    left: calc(-.3125rem + 3px);
  }

  .data-\[placement\=top\]\:before\:-bottom-\[calc\(theme\(spacing\.5\)\/4_-_1\.5px\)\][data-placement="top"]:before {
    content: var(--tw-content);
    bottom: calc(-.3125rem + 1.5px);
  }

  .data-\[placement\=top\]\:before\:left-1\/2[data-placement="top"]:before {
    content: var(--tw-content);
    left: 50%;
  }

  .data-\[placement\=top\]\:before\:-translate-x-1\/2[data-placement="top"]:before {
    content: var(--tw-content);
    --tw-translate-x: calc(calc(1 / 2 * 100%) * -1);
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }

  .data-\[placement\=top-center\]\:fixed[data-placement="top-center"] {
    position: fixed;
  }

  .data-\[placement\=top-center\]\:top-0[data-placement="top-center"] {
    top: calc(var(--spacing) * 0);
  }

  .data-\[placement\=top-center\]\:right-0[data-placement="top-center"] {
    right: calc(var(--spacing) * 0);
  }

  .data-\[placement\=top-center\]\:left-0[data-placement="top-center"] {
    left: calc(var(--spacing) * 0);
  }

  .data-\[placement\=top-center\]\:left-1\/2[data-placement="top-center"] {
    left: 50%;
  }

  .data-\[placement\=top-center\]\:flex[data-placement="top-center"] {
    display: flex;
  }

  .data-\[placement\=top-center\]\:-translate-x-1\/2[data-placement="top-center"] {
    --tw-translate-x: calc(calc(1 / 2 * 100%) * -1);
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }

  .data-\[placement\=top-center\]\:flex-col[data-placement="top-center"] {
    flex-direction: column;
  }

  .data-\[placement\=top-end\]\:before\:right-3[data-placement="top-end"]:before {
    content: var(--tw-content);
    right: calc(var(--spacing) * 3);
  }

  .data-\[placement\=top-end\]\:before\:-bottom-\[calc\(theme\(spacing\.5\)\/4_-_1\.5px\)\][data-placement="top-end"]:before {
    content: var(--tw-content);
    bottom: calc(-.3125rem + 1.5px);
  }

  .data-\[placement\=top-left\]\:fixed[data-placement="top-left"] {
    position: fixed;
  }

  .data-\[placement\=top-left\]\:top-0[data-placement="top-left"] {
    top: calc(var(--spacing) * 0);
  }

  .data-\[placement\=top-left\]\:left-0[data-placement="top-left"] {
    left: calc(var(--spacing) * 0);
  }

  .data-\[placement\=top-left\]\:mx-auto[data-placement="top-left"] {
    margin-inline: auto;
  }

  .data-\[placement\=top-left\]\:flex[data-placement="top-left"] {
    display: flex;
  }

  .data-\[placement\=top-left\]\:flex-col[data-placement="top-left"] {
    flex-direction: column;
  }

  .data-\[placement\=top-right\]\:fixed[data-placement="top-right"] {
    position: fixed;
  }

  .data-\[placement\=top-right\]\:top-0[data-placement="top-right"] {
    top: calc(var(--spacing) * 0);
  }

  .data-\[placement\=top-right\]\:right-0[data-placement="top-right"] {
    right: calc(var(--spacing) * 0);
  }

  .data-\[placement\=top-right\]\:mx-auto[data-placement="top-right"] {
    margin-inline: auto;
  }

  .data-\[placement\=top-right\]\:flex[data-placement="top-right"] {
    display: flex;
  }

  .data-\[placement\=top-right\]\:flex-col[data-placement="top-right"] {
    flex-direction: column;
  }

  .data-\[placement\=top-start\]\:before\:-bottom-\[calc\(theme\(spacing\.5\)\/4_-_1\.5px\)\][data-placement="top-start"]:before {
    content: var(--tw-content);
    bottom: calc(-.3125rem + 1.5px);
  }

  .data-\[placement\=top-start\]\:before\:left-3[data-placement="top-start"]:before {
    content: var(--tw-content);
    left: calc(var(--spacing) * 3);
  }

  .data-\[pressed\=true\]\:scale-100[data-pressed="true"] {
    --tw-scale-x: 100%;
    --tw-scale-y: 100%;
    --tw-scale-z: 100%;
    scale: var(--tw-scale-x) var(--tw-scale-y);
  }

  .data-\[pressed\=true\]\:scale-\[0\.97\][data-pressed="true"] {
    scale: .97;
  }

  .data-\[pressed\=true\]\:opacity-50[data-pressed="true"] {
    opacity: .5;
  }

  .data-\[pressed\=true\]\:opacity-70[data-pressed="true"] {
    opacity: .7;
  }

  .data-\[pressed\=true\]\:opacity-disabled[data-pressed="true"] {
    opacity: var(--heroui-disabled-opacity);
  }

  .data-\[range-end\=true\]\:before\:rounded-e-full[data-range-end="true"]:before {
    content: var(--tw-content);
    border-start-end-radius: 3.40282e38px;
    border-end-end-radius: 3.40282e38px;
  }

  .data-\[range-start\=true\]\:before\:rounded-s-full[data-range-start="true"]:before {
    content: var(--tw-content);
    border-start-start-radius: 3.40282e38px;
    border-end-start-radius: 3.40282e38px;
  }

  .data-\[readonly\=true\]\:cursor-default[data-readonly="true"] {
    cursor: default;
  }

  .data-\[right-scroll\=true\]\:\[mask-image\:linear-gradient\(90deg\,\#000_calc\(100\%_-_var\(--scroll-shadow-size\)\)\,transparent\)\][data-right-scroll="true"] {
    mask-image: linear-gradient(90deg, #000 calc(100% - var(--scroll-shadow-size)), transparent);
  }

  .data-\[selectable\=true\]\:focus\:border-danger[data-selectable="true"]:focus {
    border-color: hsl(var(--heroui-danger) / 1);
  }

  .data-\[selectable\=true\]\:focus\:border-default[data-selectable="true"]:focus {
    border-color: hsl(var(--heroui-default) / 1);
  }

  .data-\[selectable\=true\]\:focus\:border-primary[data-selectable="true"]:focus {
    border-color: var(--primary);
  }

  .data-\[selectable\=true\]\:focus\:border-secondary[data-selectable="true"]:focus {
    border-color: var(--secondary);
  }

  .data-\[selectable\=true\]\:focus\:border-success[data-selectable="true"]:focus {
    border-color: hsl(var(--heroui-success) / 1);
  }

  .data-\[selectable\=true\]\:focus\:border-warning[data-selectable="true"]:focus {
    border-color: hsl(var(--heroui-warning) / 1);
  }

  .data-\[selectable\=true\]\:focus\:bg-danger[data-selectable="true"]:focus {
    background-color: hsl(var(--heroui-danger) / 1);
  }

  .data-\[selectable\=true\]\:focus\:bg-danger\/20[data-selectable="true"]:focus {
    background-color: hsl(var(--heroui-danger) / 1);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .data-\[selectable\=true\]\:focus\:bg-danger\/20[data-selectable="true"]:focus {
      background-color: color-mix(in oklab, hsl(var(--heroui-danger) / 1) 20%, transparent);
    }
  }

  .data-\[selectable\=true\]\:focus\:bg-default[data-selectable="true"]:focus {
    background-color: hsl(var(--heroui-default) / 1);
  }

  .data-\[selectable\=true\]\:focus\:bg-default-100[data-selectable="true"]:focus {
    background-color: hsl(var(--heroui-default-100) / 1);
  }

  .data-\[selectable\=true\]\:focus\:bg-default\/40[data-selectable="true"]:focus {
    background-color: hsl(var(--heroui-default) / 1);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .data-\[selectable\=true\]\:focus\:bg-default\/40[data-selectable="true"]:focus {
      background-color: color-mix(in oklab, hsl(var(--heroui-default) / 1) 40%, transparent);
    }
  }

  .data-\[selectable\=true\]\:focus\:bg-primary[data-selectable="true"]:focus {
    background-color: var(--primary);
  }

  .data-\[selectable\=true\]\:focus\:bg-primary\/20[data-selectable="true"]:focus {
    background-color: var(--primary);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .data-\[selectable\=true\]\:focus\:bg-primary\/20[data-selectable="true"]:focus {
      background-color: color-mix(in oklab, var(--primary) 20%, transparent);
    }
  }

  .data-\[selectable\=true\]\:focus\:bg-secondary[data-selectable="true"]:focus {
    background-color: var(--secondary);
  }

  .data-\[selectable\=true\]\:focus\:bg-secondary\/20[data-selectable="true"]:focus {
    background-color: var(--secondary);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .data-\[selectable\=true\]\:focus\:bg-secondary\/20[data-selectable="true"]:focus {
      background-color: color-mix(in oklab, var(--secondary) 20%, transparent);
    }
  }

  .data-\[selectable\=true\]\:focus\:bg-success[data-selectable="true"]:focus {
    background-color: hsl(var(--heroui-success) / 1);
  }

  .data-\[selectable\=true\]\:focus\:bg-success\/20[data-selectable="true"]:focus {
    background-color: hsl(var(--heroui-success) / 1);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .data-\[selectable\=true\]\:focus\:bg-success\/20[data-selectable="true"]:focus {
      background-color: color-mix(in oklab, hsl(var(--heroui-success) / 1) 20%, transparent);
    }
  }

  .data-\[selectable\=true\]\:focus\:bg-warning[data-selectable="true"]:focus {
    background-color: hsl(var(--heroui-warning) / 1);
  }

  .data-\[selectable\=true\]\:focus\:bg-warning\/20[data-selectable="true"]:focus {
    background-color: hsl(var(--heroui-warning) / 1);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .data-\[selectable\=true\]\:focus\:bg-warning\/20[data-selectable="true"]:focus {
      background-color: color-mix(in oklab, hsl(var(--heroui-warning) / 1) 20%, transparent);
    }
  }

  .data-\[selectable\=true\]\:focus\:text-danger[data-selectable="true"]:focus {
    color: hsl(var(--heroui-danger) / 1);
  }

  .data-\[selectable\=true\]\:focus\:text-danger-foreground[data-selectable="true"]:focus {
    color: hsl(var(--heroui-danger-foreground) / 1);
  }

  .data-\[selectable\=true\]\:focus\:text-default-500[data-selectable="true"]:focus {
    color: hsl(var(--heroui-default-500) / 1);
  }

  .data-\[selectable\=true\]\:focus\:text-default-foreground[data-selectable="true"]:focus {
    color: hsl(var(--heroui-default-foreground) / 1);
  }

  .data-\[selectable\=true\]\:focus\:text-primary[data-selectable="true"]:focus {
    color: var(--primary);
  }

  .data-\[selectable\=true\]\:focus\:text-primary-foreground[data-selectable="true"]:focus {
    color: var(--primary-foreground);
  }

  .data-\[selectable\=true\]\:focus\:text-secondary[data-selectable="true"]:focus {
    color: var(--secondary);
  }

  .data-\[selectable\=true\]\:focus\:text-secondary-foreground[data-selectable="true"]:focus {
    color: var(--secondary-foreground);
  }

  .data-\[selectable\=true\]\:focus\:text-success[data-selectable="true"]:focus {
    color: hsl(var(--heroui-success) / 1);
  }

  .data-\[selectable\=true\]\:focus\:text-success-foreground[data-selectable="true"]:focus {
    color: hsl(var(--heroui-success-foreground) / 1);
  }

  .data-\[selectable\=true\]\:focus\:text-warning[data-selectable="true"]:focus {
    color: hsl(var(--heroui-warning) / 1);
  }

  .data-\[selectable\=true\]\:focus\:text-warning-foreground[data-selectable="true"]:focus {
    color: hsl(var(--heroui-warning-foreground) / 1);
  }

  .data-\[selectable\=true\]\:focus\:shadow-danger\/30[data-selectable="true"]:focus {
    --tw-shadow-color: hsl(var(--heroui-danger) / 1);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .data-\[selectable\=true\]\:focus\:shadow-danger\/30[data-selectable="true"]:focus {
      --tw-shadow-color: color-mix(in oklab, color-mix(in oklab, hsl(var(--heroui-danger) / 1) 30%, transparent) var(--tw-shadow-alpha), transparent);
    }
  }

  .data-\[selectable\=true\]\:focus\:shadow-default\/50[data-selectable="true"]:focus {
    --tw-shadow-color: hsl(var(--heroui-default) / 1);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .data-\[selectable\=true\]\:focus\:shadow-default\/50[data-selectable="true"]:focus {
      --tw-shadow-color: color-mix(in oklab, color-mix(in oklab, hsl(var(--heroui-default) / 1) 50%, transparent) var(--tw-shadow-alpha), transparent);
    }
  }

  .data-\[selectable\=true\]\:focus\:shadow-primary\/30[data-selectable="true"]:focus {
    --tw-shadow-color: var(--primary);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .data-\[selectable\=true\]\:focus\:shadow-primary\/30[data-selectable="true"]:focus {
      --tw-shadow-color: color-mix(in oklab, color-mix(in oklab, var(--primary) 30%, transparent) var(--tw-shadow-alpha), transparent);
    }
  }

  .data-\[selectable\=true\]\:focus\:shadow-secondary\/30[data-selectable="true"]:focus {
    --tw-shadow-color: var(--secondary);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .data-\[selectable\=true\]\:focus\:shadow-secondary\/30[data-selectable="true"]:focus {
      --tw-shadow-color: color-mix(in oklab, color-mix(in oklab, var(--secondary) 30%, transparent) var(--tw-shadow-alpha), transparent);
    }
  }

  .data-\[selectable\=true\]\:focus\:shadow-success\/30[data-selectable="true"]:focus {
    --tw-shadow-color: hsl(var(--heroui-success) / 1);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .data-\[selectable\=true\]\:focus\:shadow-success\/30[data-selectable="true"]:focus {
      --tw-shadow-color: color-mix(in oklab, color-mix(in oklab, hsl(var(--heroui-success) / 1) 30%, transparent) var(--tw-shadow-alpha), transparent);
    }
  }

  .data-\[selectable\=true\]\:focus\:shadow-warning\/30[data-selectable="true"]:focus {
    --tw-shadow-color: hsl(var(--heroui-warning) / 1);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .data-\[selectable\=true\]\:focus\:shadow-warning\/30[data-selectable="true"]:focus {
      --tw-shadow-color: color-mix(in oklab, color-mix(in oklab, hsl(var(--heroui-warning) / 1) 30%, transparent) var(--tw-shadow-alpha), transparent);
    }
  }

  .data-\[selected\=true\]\:bg-accent[data-selected="true"] {
    background-color: var(--accent);
  }

  .data-\[selected\=true\]\:bg-danger[data-selected="true"] {
    background-color: hsl(var(--heroui-danger) / 1);
  }

  .data-\[selected\=true\]\:bg-default[data-selected="true"] {
    background-color: hsl(var(--heroui-default) / 1);
  }

  .data-\[selected\=true\]\:bg-foreground[data-selected="true"] {
    background-color: var(--foreground);
  }

  .data-\[selected\=true\]\:bg-primary[data-selected="true"] {
    background-color: var(--primary);
  }

  .data-\[selected\=true\]\:bg-secondary[data-selected="true"] {
    background-color: var(--secondary);
  }

  .data-\[selected\=true\]\:bg-success[data-selected="true"] {
    background-color: hsl(var(--heroui-success) / 1);
  }

  .data-\[selected\=true\]\:bg-warning[data-selected="true"] {
    background-color: hsl(var(--heroui-warning) / 1);
  }

  .data-\[selected\=true\]\:text-accent-foreground[data-selected="true"] {
    color: var(--accent-foreground);
  }

  .data-\[selected\=true\]\:text-background[data-selected="true"] {
    color: var(--background);
  }

  .data-\[selected\=true\]\:text-danger[data-selected="true"] {
    color: hsl(var(--heroui-danger) / 1);
  }

  .data-\[selected\=true\]\:text-danger-foreground[data-selected="true"] {
    color: hsl(var(--heroui-danger-foreground) / 1);
  }

  .data-\[selected\=true\]\:text-default-foreground[data-selected="true"] {
    color: hsl(var(--heroui-default-foreground) / 1);
  }

  .data-\[selected\=true\]\:text-primary[data-selected="true"] {
    color: var(--primary);
  }

  .data-\[selected\=true\]\:text-primary-foreground[data-selected="true"] {
    color: var(--primary-foreground);
  }

  .data-\[selected\=true\]\:text-secondary[data-selected="true"] {
    color: var(--secondary);
  }

  .data-\[selected\=true\]\:text-secondary-foreground[data-selected="true"] {
    color: var(--secondary-foreground);
  }

  .data-\[selected\=true\]\:text-success-600[data-selected="true"] {
    color: hsl(var(--heroui-success-600) / 1);
  }

  .data-\[selected\=true\]\:text-success-foreground[data-selected="true"] {
    color: hsl(var(--heroui-success-foreground) / 1);
  }

  .data-\[selected\=true\]\:text-warning-600[data-selected="true"] {
    color: hsl(var(--heroui-warning-600) / 1);
  }

  .data-\[selected\=true\]\:text-warning-foreground[data-selected="true"] {
    color: hsl(var(--heroui-warning-foreground) / 1);
  }

  .data-\[selected\=true\]\:shadow-md[data-selected="true"] {
    --tw-shadow: 0 4px 6px -1px var(--tw-shadow-color, #0000001a), 0 2px 4px -2px var(--tw-shadow-color, #0000001a);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .data-\[selected\=true\]\:shadow-none[data-selected="true"] {
    --tw-shadow: 0 0 #0000;
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .data-\[selected\=true\]\:shadow-danger\/40[data-selected="true"] {
    --tw-shadow-color: hsl(var(--heroui-danger) / 1);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .data-\[selected\=true\]\:shadow-danger\/40[data-selected="true"] {
      --tw-shadow-color: color-mix(in oklab, color-mix(in oklab, hsl(var(--heroui-danger) / 1) 40%, transparent) var(--tw-shadow-alpha), transparent);
    }
  }

  .data-\[selected\=true\]\:shadow-foreground\/40[data-selected="true"] {
    --tw-shadow-color: var(--foreground);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .data-\[selected\=true\]\:shadow-foreground\/40[data-selected="true"] {
      --tw-shadow-color: color-mix(in oklab, color-mix(in oklab, var(--foreground) 40%, transparent) var(--tw-shadow-alpha), transparent);
    }
  }

  .data-\[selected\=true\]\:shadow-primary\/40[data-selected="true"] {
    --tw-shadow-color: var(--primary);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .data-\[selected\=true\]\:shadow-primary\/40[data-selected="true"] {
      --tw-shadow-color: color-mix(in oklab, color-mix(in oklab, var(--primary) 40%, transparent) var(--tw-shadow-alpha), transparent);
    }
  }

  .data-\[selected\=true\]\:shadow-secondary\/40[data-selected="true"] {
    --tw-shadow-color: var(--secondary);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .data-\[selected\=true\]\:shadow-secondary\/40[data-selected="true"] {
      --tw-shadow-color: color-mix(in oklab, color-mix(in oklab, var(--secondary) 40%, transparent) var(--tw-shadow-alpha), transparent);
    }
  }

  .data-\[selected\=true\]\:shadow-success\/40[data-selected="true"] {
    --tw-shadow-color: hsl(var(--heroui-success) / 1);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .data-\[selected\=true\]\:shadow-success\/40[data-selected="true"] {
      --tw-shadow-color: color-mix(in oklab, color-mix(in oklab, hsl(var(--heroui-success) / 1) 40%, transparent) var(--tw-shadow-alpha), transparent);
    }
  }

  .data-\[selected\=true\]\:shadow-warning\/40[data-selected="true"] {
    --tw-shadow-color: hsl(var(--heroui-warning) / 1);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .data-\[selected\=true\]\:shadow-warning\/40[data-selected="true"] {
      --tw-shadow-color: color-mix(in oklab, color-mix(in oklab, hsl(var(--heroui-warning) / 1) 40%, transparent) var(--tw-shadow-alpha), transparent);
    }
  }

  .data-\[selected\=true\]\:before\:opacity-100[data-selected="true"]:before {
    content: var(--tw-content);
    opacity: 1;
  }

  .data-\[selected\=true\]\:after\:bg-danger[data-selected="true"]:after {
    content: var(--tw-content);
    background-color: hsl(var(--heroui-danger) / 1);
  }

  .data-\[selected\=true\]\:after\:bg-foreground[data-selected="true"]:after {
    content: var(--tw-content);
    background-color: var(--foreground);
  }

  .data-\[selected\=true\]\:after\:bg-primary[data-selected="true"]:after {
    content: var(--tw-content);
    background-color: var(--primary);
  }

  .data-\[selected\=true\]\:after\:bg-secondary[data-selected="true"]:after {
    content: var(--tw-content);
    background-color: var(--secondary);
  }

  .data-\[selected\=true\]\:after\:bg-success[data-selected="true"]:after {
    content: var(--tw-content);
    background-color: hsl(var(--heroui-success) / 1);
  }

  .data-\[selected\=true\]\:after\:bg-warning[data-selected="true"]:after {
    content: var(--tw-content);
    background-color: hsl(var(--heroui-warning) / 1);
  }

  .data-\[selected\=true\]\:after\:opacity-100[data-selected="true"]:after {
    content: var(--tw-content);
    opacity: 1;
  }

  .data-\[selected\=true\]\:data-\[hover\=true\]\:bg-danger[data-selected="true"][data-hover="true"] {
    background-color: hsl(var(--heroui-danger) / 1);
  }

  .data-\[selected\=true\]\:data-\[hover\=true\]\:bg-foreground[data-selected="true"][data-hover="true"] {
    background-color: var(--foreground);
  }

  .data-\[selected\=true\]\:data-\[hover\=true\]\:bg-primary[data-selected="true"][data-hover="true"] {
    background-color: var(--primary);
  }

  .data-\[selected\=true\]\:data-\[hover\=true\]\:bg-secondary[data-selected="true"][data-hover="true"] {
    background-color: var(--secondary);
  }

  .data-\[selected\=true\]\:data-\[hover\=true\]\:bg-success[data-selected="true"][data-hover="true"] {
    background-color: hsl(var(--heroui-success) / 1);
  }

  .data-\[selected\=true\]\:data-\[hover\=true\]\:bg-warning[data-selected="true"][data-hover="true"] {
    background-color: hsl(var(--heroui-warning) / 1);
  }

  .data-\[selected\=true\]\:data-\[hover\=true\]\:text-background[data-selected="true"][data-hover="true"] {
    color: var(--background);
  }

  .data-\[selected\=true\]\:data-\[hover\=true\]\:text-danger-foreground[data-selected="true"][data-hover="true"] {
    color: hsl(var(--heroui-danger-foreground) / 1);
  }

  .data-\[selected\=true\]\:data-\[hover\=true\]\:text-primary-foreground[data-selected="true"][data-hover="true"] {
    color: var(--primary-foreground);
  }

  .data-\[selected\=true\]\:data-\[hover\=true\]\:text-secondary-foreground[data-selected="true"][data-hover="true"] {
    color: var(--secondary-foreground);
  }

  .data-\[selected\=true\]\:data-\[hover\=true\]\:text-success-foreground[data-selected="true"][data-hover="true"] {
    color: hsl(var(--heroui-success-foreground) / 1);
  }

  .data-\[selected\=true\]\:data-\[hover\=true\]\:text-warning-foreground[data-selected="true"][data-hover="true"] {
    color: hsl(var(--heroui-warning-foreground) / 1);
  }

  .data-\[selected\=true\]\:data-\[range-selection\=true\]\:bg-transparent[data-selected="true"][data-range-selection="true"] {
    background-color: #0000;
  }

  .data-\[selected\=true\]\:data-\[range-selection\=true\]\:text-danger-500[data-selected="true"][data-range-selection="true"] {
    color: hsl(var(--heroui-danger-500) / 1);
  }

  .data-\[selected\=true\]\:data-\[range-selection\=true\]\:text-foreground[data-selected="true"][data-range-selection="true"] {
    color: var(--foreground);
  }

  .data-\[selected\=true\]\:data-\[range-selection\=true\]\:text-primary[data-selected="true"][data-range-selection="true"] {
    color: var(--primary);
  }

  .data-\[selected\=true\]\:data-\[range-selection\=true\]\:text-secondary[data-selected="true"][data-range-selection="true"] {
    color: var(--secondary);
  }

  .data-\[selected\=true\]\:data-\[range-selection\=true\]\:text-success-600[data-selected="true"][data-range-selection="true"] {
    color: hsl(var(--heroui-success-600) / 1);
  }

  .data-\[selected\=true\]\:data-\[range-selection\=true\]\:text-warning-500[data-selected="true"][data-range-selection="true"] {
    color: hsl(var(--heroui-warning-500) / 1);
  }

  .data-\[selected\=true\]\:data-\[range-selection\=true\]\:before\:bg-danger-50[data-selected="true"][data-range-selection="true"]:before {
    content: var(--tw-content);
    background-color: hsl(var(--heroui-danger-50) / 1);
  }

  .data-\[selected\=true\]\:data-\[range-selection\=true\]\:before\:bg-foreground\/10[data-selected="true"][data-range-selection="true"]:before {
    content: var(--tw-content);
    background-color: var(--foreground);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .data-\[selected\=true\]\:data-\[range-selection\=true\]\:before\:bg-foreground\/10[data-selected="true"][data-range-selection="true"]:before {
      background-color: color-mix(in oklab, var(--foreground) 10%, transparent);
    }
  }

  .data-\[selected\=true\]\:data-\[range-selection\=true\]\:before\:bg-primary-50[data-selected="true"][data-range-selection="true"]:before {
    content: var(--tw-content);
    background-color: hsl(var(--heroui-primary-50) / 1);
  }

  .data-\[selected\=true\]\:data-\[range-selection\=true\]\:before\:bg-secondary-50[data-selected="true"][data-range-selection="true"]:before {
    content: var(--tw-content);
    background-color: hsl(var(--heroui-secondary-50) / 1);
  }

  .data-\[selected\=true\]\:data-\[range-selection\=true\]\:before\:bg-success-100[data-selected="true"][data-range-selection="true"]:before {
    content: var(--tw-content);
    background-color: hsl(var(--heroui-success-100) / 1);
  }

  .data-\[selected\=true\]\:data-\[range-selection\=true\]\:before\:bg-warning-100[data-selected="true"][data-range-selection="true"]:before {
    content: var(--tw-content);
    background-color: hsl(var(--heroui-warning-100) / 1);
  }

  .data-\[selected\=true\]\:data-\[range-selection\=true\]\:data-\[outside-month\=true\]\:bg-transparent[data-selected="true"][data-range-selection="true"][data-outside-month="true"] {
    background-color: #0000;
  }

  .data-\[selected\=true\]\:data-\[range-selection\=true\]\:data-\[outside-month\=true\]\:text-default-300[data-selected="true"][data-range-selection="true"][data-outside-month="true"] {
    color: hsl(var(--heroui-default-300) / 1);
  }

  .data-\[selection-end\=true\]\:before\:rounded-e-full[data-selection-end="true"]:before {
    content: var(--tw-content);
    border-start-end-radius: 3.40282e38px;
    border-end-end-radius: 3.40282e38px;
  }

  .data-\[selected\=true\]\:data-\[selection-end\=true\]\:shadow-md[data-selected="true"][data-selection-end="true"] {
    --tw-shadow: 0 4px 6px -1px var(--tw-shadow-color, #0000001a), 0 2px 4px -2px var(--tw-shadow-color, #0000001a);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .data-\[selected\=true\]\:data-\[selection-end\=true\]\:data-\[range-selection\=true\]\:rounded-full[data-selected="true"][data-selection-end="true"][data-range-selection="true"] {
    border-radius: 3.40282e38px;
  }

  .data-\[selected\=true\]\:data-\[selection-end\=true\]\:data-\[range-selection\=true\]\:bg-danger[data-selected="true"][data-selection-end="true"][data-range-selection="true"] {
    background-color: hsl(var(--heroui-danger) / 1);
  }

  .data-\[selected\=true\]\:data-\[selection-end\=true\]\:data-\[range-selection\=true\]\:bg-foreground[data-selected="true"][data-selection-end="true"][data-range-selection="true"] {
    background-color: var(--foreground);
  }

  .data-\[selected\=true\]\:data-\[selection-end\=true\]\:data-\[range-selection\=true\]\:bg-primary[data-selected="true"][data-selection-end="true"][data-range-selection="true"] {
    background-color: var(--primary);
  }

  .data-\[selected\=true\]\:data-\[selection-end\=true\]\:data-\[range-selection\=true\]\:bg-secondary[data-selected="true"][data-selection-end="true"][data-range-selection="true"] {
    background-color: var(--secondary);
  }

  .data-\[selected\=true\]\:data-\[selection-end\=true\]\:data-\[range-selection\=true\]\:bg-success[data-selected="true"][data-selection-end="true"][data-range-selection="true"] {
    background-color: hsl(var(--heroui-success) / 1);
  }

  .data-\[selected\=true\]\:data-\[selection-end\=true\]\:data-\[range-selection\=true\]\:bg-warning[data-selected="true"][data-selection-end="true"][data-range-selection="true"] {
    background-color: hsl(var(--heroui-warning) / 1);
  }

  .data-\[selected\=true\]\:data-\[selection-end\=true\]\:data-\[range-selection\=true\]\:text-background[data-selected="true"][data-selection-end="true"][data-range-selection="true"] {
    color: var(--background);
  }

  .data-\[selected\=true\]\:data-\[selection-end\=true\]\:data-\[range-selection\=true\]\:text-danger-foreground[data-selected="true"][data-selection-end="true"][data-range-selection="true"] {
    color: hsl(var(--heroui-danger-foreground) / 1);
  }

  .data-\[selected\=true\]\:data-\[selection-end\=true\]\:data-\[range-selection\=true\]\:text-primary-foreground[data-selected="true"][data-selection-end="true"][data-range-selection="true"] {
    color: var(--primary-foreground);
  }

  .data-\[selected\=true\]\:data-\[selection-end\=true\]\:data-\[range-selection\=true\]\:text-secondary-foreground[data-selected="true"][data-selection-end="true"][data-range-selection="true"] {
    color: var(--secondary-foreground);
  }

  .data-\[selected\=true\]\:data-\[selection-end\=true\]\:data-\[range-selection\=true\]\:text-success-foreground[data-selected="true"][data-selection-end="true"][data-range-selection="true"] {
    color: hsl(var(--heroui-success-foreground) / 1);
  }

  .data-\[selected\=true\]\:data-\[selection-end\=true\]\:data-\[range-selection\=true\]\:text-warning-foreground[data-selected="true"][data-selection-end="true"][data-range-selection="true"] {
    color: hsl(var(--heroui-warning-foreground) / 1);
  }

  .data-\[selection-start\=true\]\:before\:rounded-s-full[data-selection-start="true"]:before {
    content: var(--tw-content);
    border-start-start-radius: 3.40282e38px;
    border-end-start-radius: 3.40282e38px;
  }

  .data-\[selected\=true\]\:data-\[selection-start\=true\]\:shadow-md[data-selected="true"][data-selection-start="true"] {
    --tw-shadow: 0 4px 6px -1px var(--tw-shadow-color, #0000001a), 0 2px 4px -2px var(--tw-shadow-color, #0000001a);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .data-\[selected\=true\]\:data-\[selection-start\=true\]\:data-\[range-selection\=true\]\:rounded-full[data-selected="true"][data-selection-start="true"][data-range-selection="true"] {
    border-radius: 3.40282e38px;
  }

  .data-\[selected\=true\]\:data-\[selection-start\=true\]\:data-\[range-selection\=true\]\:bg-danger[data-selected="true"][data-selection-start="true"][data-range-selection="true"] {
    background-color: hsl(var(--heroui-danger) / 1);
  }

  .data-\[selected\=true\]\:data-\[selection-start\=true\]\:data-\[range-selection\=true\]\:bg-foreground[data-selected="true"][data-selection-start="true"][data-range-selection="true"] {
    background-color: var(--foreground);
  }

  .data-\[selected\=true\]\:data-\[selection-start\=true\]\:data-\[range-selection\=true\]\:bg-primary[data-selected="true"][data-selection-start="true"][data-range-selection="true"] {
    background-color: var(--primary);
  }

  .data-\[selected\=true\]\:data-\[selection-start\=true\]\:data-\[range-selection\=true\]\:bg-secondary[data-selected="true"][data-selection-start="true"][data-range-selection="true"] {
    background-color: var(--secondary);
  }

  .data-\[selected\=true\]\:data-\[selection-start\=true\]\:data-\[range-selection\=true\]\:bg-success[data-selected="true"][data-selection-start="true"][data-range-selection="true"] {
    background-color: hsl(var(--heroui-success) / 1);
  }

  .data-\[selected\=true\]\:data-\[selection-start\=true\]\:data-\[range-selection\=true\]\:bg-warning[data-selected="true"][data-selection-start="true"][data-range-selection="true"] {
    background-color: hsl(var(--heroui-warning) / 1);
  }

  .data-\[selected\=true\]\:data-\[selection-start\=true\]\:data-\[range-selection\=true\]\:text-background[data-selected="true"][data-selection-start="true"][data-range-selection="true"] {
    color: var(--background);
  }

  .data-\[selected\=true\]\:data-\[selection-start\=true\]\:data-\[range-selection\=true\]\:text-danger-foreground[data-selected="true"][data-selection-start="true"][data-range-selection="true"] {
    color: hsl(var(--heroui-danger-foreground) / 1);
  }

  .data-\[selected\=true\]\:data-\[selection-start\=true\]\:data-\[range-selection\=true\]\:text-primary-foreground[data-selected="true"][data-selection-start="true"][data-range-selection="true"] {
    color: var(--primary-foreground);
  }

  .data-\[selected\=true\]\:data-\[selection-start\=true\]\:data-\[range-selection\=true\]\:text-secondary-foreground[data-selected="true"][data-selection-start="true"][data-range-selection="true"] {
    color: var(--secondary-foreground);
  }

  .data-\[selected\=true\]\:data-\[selection-start\=true\]\:data-\[range-selection\=true\]\:text-success-foreground[data-selected="true"][data-selection-start="true"][data-range-selection="true"] {
    color: hsl(var(--heroui-success-foreground) / 1);
  }

  .data-\[selected\=true\]\:data-\[selection-start\=true\]\:data-\[range-selection\=true\]\:text-warning-foreground[data-selected="true"][data-selection-start="true"][data-range-selection="true"] {
    color: hsl(var(--heroui-warning-foreground) / 1);
  }

  .data-\[side\=bottom\]\:translate-y-1[data-side="bottom"] {
    --tw-translate-y: calc(var(--spacing) * 1);
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }

  .data-\[side\=bottom\]\:slide-in-from-top-2[data-side="bottom"] {
    --tw-enter-translate-y: calc(2 * var(--spacing) * -1);
  }

  .data-\[side\=left\]\:-translate-x-1[data-side="left"] {
    --tw-translate-x: calc(var(--spacing) * -1);
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }

  .data-\[side\=left\]\:slide-in-from-right-2[data-side="left"] {
    --tw-enter-translate-x: calc(2 * var(--spacing));
  }

  .data-\[side\=right\]\:translate-x-1[data-side="right"] {
    --tw-translate-x: calc(var(--spacing) * 1);
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }

  .data-\[side\=right\]\:slide-in-from-left-2[data-side="right"] {
    --tw-enter-translate-x: calc(2 * var(--spacing) * -1);
  }

  .data-\[side\=top\]\:-translate-y-1[data-side="top"] {
    --tw-translate-y: calc(var(--spacing) * -1);
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }

  .data-\[side\=top\]\:slide-in-from-bottom-2[data-side="top"] {
    --tw-enter-translate-y: calc(2 * var(--spacing));
  }

  .data-\[size\=default\]\:h-9[data-size="default"] {
    height: calc(var(--spacing) * 9);
  }

  .data-\[size\=sm\]\:h-8[data-size="sm"] {
    height: calc(var(--spacing) * 8);
  }

  :is(.\*\:data-\[slot\=alert-description\]\:text-destructive\/90 > *)[data-slot="alert-description"] {
    color: var(--destructive);
  }

  @supports (color: color-mix(in lab, red, red)) {
    :is(.\*\:data-\[slot\=alert-description\]\:text-destructive\/90 > *)[data-slot="alert-description"] {
      color: color-mix(in oklab, var(--destructive) 90%, transparent);
    }
  }

  :is(.\*\*\:data-\[slot\=badge\]\:size-5 *)[data-slot="badge"] {
    width: calc(var(--spacing) * 5);
    height: calc(var(--spacing) * 5);
  }

  :is(.\*\*\:data-\[slot\=badge\]\:rounded-full *)[data-slot="badge"] {
    border-radius: 3.40282e38px;
  }

  :is(.\*\*\:data-\[slot\=badge\]\:bg-muted-foreground\/30 *)[data-slot="badge"] {
    background-color: var(--muted-foreground);
  }

  @supports (color: color-mix(in lab, red, red)) {
    :is(.\*\*\:data-\[slot\=badge\]\:bg-muted-foreground\/30 *)[data-slot="badge"] {
      background-color: color-mix(in oklab, var(--muted-foreground) 30%, transparent);
    }
  }

  :is(.\*\*\:data-\[slot\=badge\]\:px-1 *)[data-slot="badge"] {
    padding-inline: calc(var(--spacing) * 1);
  }

  :is(.\*\:data-\[slot\=card\]\:bg-gradient-to-t > *)[data-slot="card"] {
    --tw-gradient-position: to top in oklab;
    background-image: linear-gradient(var(--tw-gradient-stops));
  }

  :is(.\*\:data-\[slot\=card\]\:from-primary\/5 > *)[data-slot="card"] {
    --tw-gradient-from: var(--primary);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  @supports (color: color-mix(in lab, red, red)) {
    :is(.\*\:data-\[slot\=card\]\:from-primary\/5 > *)[data-slot="card"] {
      --tw-gradient-from: color-mix(in oklab, var(--primary) 5%, transparent);
    }
  }

  :is(.\*\:data-\[slot\=card\]\:to-card > *)[data-slot="card"] {
    --tw-gradient-to: var(--card);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  :is(.\*\:data-\[slot\=card\]\:shadow-xs > *)[data-slot="card"] {
    --tw-shadow: 0 1px 2px 0 var(--tw-shadow-color, #0000000d);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  :is(.\*\*\:data-\[slot\=command-input-wrapper\]\:h-12 *)[data-slot="command-input-wrapper"] {
    height: calc(var(--spacing) * 12);
  }

  :is(.\*\*\:data-\[slot\=navigation-menu-link\]\:focus\:ring-0 *)[data-slot="navigation-menu-link"]:focus {
    --tw-ring-shadow: var(--tw-ring-inset, ) 0 0 0 calc(0px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  :is(.\*\*\:data-\[slot\=navigation-menu-link\]\:focus\:outline-none *)[data-slot="navigation-menu-link"]:focus {
    --tw-outline-style: none;
    outline-style: none;
  }

  :is(.\*\:data-\[slot\=select-value\]\:line-clamp-1 > *)[data-slot="select-value"] {
    -webkit-line-clamp: 1;
    -webkit-box-orient: vertical;
    display: -webkit-box;
    overflow: hidden;
  }

  :is(.\*\:data-\[slot\=select-value\]\:flex > *)[data-slot="select-value"] {
    display: flex;
  }

  :is(.\*\:data-\[slot\=select-value\]\:items-center > *)[data-slot="select-value"] {
    align-items: center;
  }

  :is(.\*\:data-\[slot\=select-value\]\:gap-2 > *)[data-slot="select-value"] {
    gap: calc(var(--spacing) * 2);
  }

  :is(.\*\*\:data-\[slot\=select-value\]\:block *)[data-slot="select-value"] {
    display: block;
  }

  :is(.\*\*\:data-\[slot\=select-value\]\:truncate *)[data-slot="select-value"] {
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
  }

  .data-\[slot\=sidebar-menu-button\]\:\!p-1\.5[data-slot="sidebar-menu-button"] {
    padding: calc(var(--spacing) * 1.5) !important;
  }

  :is(.\*\*\:data-\[slot\=table-cell\]\:first\:w-8 *)[data-slot="table-cell"]:first-child {
    width: calc(var(--spacing) * 8);
  }

  :is(.\*\:data-\[slot\=toggle-group-item\]\:\!px-4 > *)[data-slot="toggle-group-item"] {
    padding-inline: calc(var(--spacing) * 4) !important;
  }

  .data-\[sortable\=true\]\:cursor-pointer[data-sortable="true"] {
    cursor: pointer;
  }

  .data-\[state\=active\]\:bg-background[data-state="active"] {
    background-color: var(--background);
  }

  .data-\[state\=active\]\:shadow-sm[data-state="active"] {
    --tw-shadow: 0 1px 3px 0 var(--tw-shadow-color, #0000001a), 0 1px 2px -1px var(--tw-shadow-color, #0000001a);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .data-\[state\=checked\]\:translate-x-\[calc\(100\%-2px\)\][data-state="checked"] {
    --tw-translate-x: calc(100% - 2px);
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }

  .data-\[state\=checked\]\:border-primary[data-state="checked"] {
    border-color: var(--primary);
  }

  .data-\[state\=checked\]\:bg-primary[data-state="checked"] {
    background-color: var(--primary);
  }

  .data-\[state\=checked\]\:text-primary-foreground[data-state="checked"] {
    color: var(--primary-foreground);
  }

  .data-\[state\=closed\]\:animate-accordion-up[data-state="closed"] {
    animation: .2s ease-out accordion-up;
  }

  .data-\[state\=closed\]\:animate-out[data-state="closed"] {
    animation: exit var(--tw-duration, .15s) var(--tw-ease, ease);
  }

  .data-\[state\=closed\]\:duration-300[data-state="closed"] {
    --tw-duration: .3s;
    transition-duration: .3s;
  }

  .data-\[state\=closed\]\:fade-out-0[data-state="closed"] {
    --tw-exit-opacity: 0;
  }

  .data-\[state\=closed\]\:slide-out-to-bottom[data-state="closed"] {
    --tw-exit-translate-y: 100%;
  }

  .data-\[state\=closed\]\:slide-out-to-left[data-state="closed"] {
    --tw-exit-translate-x: -100%;
  }

  .data-\[state\=closed\]\:slide-out-to-right[data-state="closed"] {
    --tw-exit-translate-x: 100%;
  }

  .data-\[state\=closed\]\:slide-out-to-top[data-state="closed"] {
    --tw-exit-translate-y: -100%;
  }

  .data-\[state\=closed\]\:zoom-out-95[data-state="closed"] {
    --tw-exit-scale: .95;
  }

  .group-data-\[viewport\=false\]\/navigation-menu\:data-\[state\=closed\]\:animate-out:is(:where(.group\/navigation-menu)[data-viewport="false"] *)[data-state="closed"] {
    animation: exit var(--tw-duration, .15s) var(--tw-ease, ease);
  }

  .group-data-\[viewport\=false\]\/navigation-menu\:data-\[state\=closed\]\:fade-out-0:is(:where(.group\/navigation-menu)[data-viewport="false"] *)[data-state="closed"] {
    --tw-exit-opacity: 0;
  }

  .group-data-\[viewport\=false\]\/navigation-menu\:data-\[state\=closed\]\:zoom-out-95:is(:where(.group\/navigation-menu)[data-viewport="false"] *)[data-state="closed"] {
    --tw-exit-scale: .95;
  }

  .data-\[state\=completed\]\:bg-primary[data-state="completed"] {
    background-color: var(--primary);
  }

  .data-\[state\=hidden\]\:animate-out[data-state="hidden"] {
    animation: exit var(--tw-duration, .15s) var(--tw-ease, ease);
  }

  .data-\[state\=hidden\]\:fade-out[data-state="hidden"] {
    --tw-exit-opacity: 0;
  }

  .data-\[state\=on\]\:bg-accent[data-state="on"] {
    background-color: var(--accent);
  }

  .data-\[state\=on\]\:text-accent-foreground[data-state="on"] {
    color: var(--accent-foreground);
  }

  .data-\[state\=open\]\:animate-accordion-down[data-state="open"] {
    animation: .2s ease-out accordion-down;
  }

  .data-\[state\=open\]\:animate-in[data-state="open"] {
    animation: enter var(--tw-duration, .15s) var(--tw-ease, ease);
  }

  .data-\[state\=open\]\:bg-accent[data-state="open"] {
    background-color: var(--accent);
  }

  .data-\[state\=open\]\:bg-accent\/50[data-state="open"] {
    background-color: var(--accent);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .data-\[state\=open\]\:bg-accent\/50[data-state="open"] {
      background-color: color-mix(in oklab, var(--accent) 50%, transparent);
    }
  }

  .data-\[state\=open\]\:bg-muted[data-state="open"] {
    background-color: var(--muted);
  }

  .data-\[state\=open\]\:bg-secondary[data-state="open"] {
    background-color: var(--secondary);
  }

  .data-\[state\=open\]\:bg-sidebar-accent[data-state="open"] {
    background-color: var(--sidebar-accent);
  }

  .data-\[state\=open\]\:text-accent-foreground[data-state="open"] {
    color: var(--accent-foreground);
  }

  .data-\[state\=open\]\:text-muted-foreground[data-state="open"] {
    color: var(--muted-foreground);
  }

  .data-\[state\=open\]\:text-sidebar-accent-foreground[data-state="open"] {
    color: var(--sidebar-accent-foreground);
  }

  .data-\[state\=open\]\:opacity-100[data-state="open"] {
    opacity: 1;
  }

  .data-\[state\=open\]\:duration-500[data-state="open"] {
    --tw-duration: .5s;
    transition-duration: .5s;
  }

  .data-\[state\=open\]\:fade-in-0[data-state="open"] {
    --tw-enter-opacity: 0;
  }

  .data-\[state\=open\]\:slide-in-from-bottom[data-state="open"] {
    --tw-enter-translate-y: 100%;
  }

  .data-\[state\=open\]\:slide-in-from-left[data-state="open"] {
    --tw-enter-translate-x: -100%;
  }

  .data-\[state\=open\]\:slide-in-from-right[data-state="open"] {
    --tw-enter-translate-x: 100%;
  }

  .data-\[state\=open\]\:slide-in-from-top[data-state="open"] {
    --tw-enter-translate-y: -100%;
  }

  .data-\[state\=open\]\:zoom-in-90[data-state="open"] {
    --tw-enter-scale: .9;
  }

  .data-\[state\=open\]\:zoom-in-95[data-state="open"] {
    --tw-enter-scale: .95;
  }

  .group-data-\[viewport\=false\]\/navigation-menu\:data-\[state\=open\]\:animate-in:is(:where(.group\/navigation-menu)[data-viewport="false"] *)[data-state="open"] {
    animation: enter var(--tw-duration, .15s) var(--tw-ease, ease);
  }

  .group-data-\[viewport\=false\]\/navigation-menu\:data-\[state\=open\]\:fade-in-0:is(:where(.group\/navigation-menu)[data-viewport="false"] *)[data-state="open"] {
    --tw-enter-opacity: 0;
  }

  .group-data-\[viewport\=false\]\/navigation-menu\:data-\[state\=open\]\:zoom-in-95:is(:where(.group\/navigation-menu)[data-viewport="false"] *)[data-state="open"] {
    --tw-enter-scale: .95;
  }

  @media (hover: hover) {
    .data-\[state\=open\]\:hover\:bg-accent[data-state="open"]:hover {
      background-color: var(--accent);
    }
  }

  @media (hover: hover) {
    .data-\[state\=open\]\:hover\:bg-sidebar-accent[data-state="open"]:hover {
      background-color: var(--sidebar-accent);
    }
  }

  @media (hover: hover) {
    .data-\[state\=open\]\:hover\:text-sidebar-accent-foreground[data-state="open"]:hover {
      color: var(--sidebar-accent-foreground);
    }
  }

  .data-\[state\=open\]\:focus\:bg-accent[data-state="open"]:focus {
    background-color: var(--accent);
  }

  .data-\[state\=selected\]\:bg-muted[data-state="selected"] {
    background-color: var(--muted);
  }

  .data-\[state\=unchecked\]\:translate-x-0[data-state="unchecked"] {
    --tw-translate-x: calc(var(--spacing) * 0);
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }

  .data-\[state\=unchecked\]\:bg-input[data-state="unchecked"] {
    background-color: var(--input);
  }

  .data-\[state\=visible\]\:animate-in[data-state="visible"] {
    animation: enter var(--tw-duration, .15s) var(--tw-ease, ease);
  }

  .data-\[state\=visible\]\:fade-in[data-state="visible"] {
    --tw-enter-opacity: 0;
  }

  .data-\[toast-exiting\=true\]\:transform-gpu[data-toast-exiting="true"] {
    transform: translateZ(0) var(--tw-rotate-x) var(--tw-rotate-y) var(--tw-rotate-z) var(--tw-skew-x) var(--tw-skew-y);
  }

  .data-\[toast-exiting\=true\]\:opacity-0[data-toast-exiting="true"] {
    opacity: 0;
  }

  .data-\[toast-exiting\=true\]\:transition-all[data-toast-exiting="true"] {
    transition-property: all;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }

  .data-\[toast-exiting\=true\]\:duration-300[data-toast-exiting="true"] {
    --tw-duration: .3s;
    transition-duration: .3s;
  }

  .data-\[toast-exiting\=true\]\:ease-out[data-toast-exiting="true"] {
    --tw-ease: var(--ease-out);
    transition-timing-function: var(--ease-out);
  }

  .data-\[toast-exiting\=true\]\:will-change-transform[data-toast-exiting="true"] {
    will-change: transform;
  }

  .data-\[toast-exiting\=true\]\:data-\[placement\=bottom-center\]\:translate-y-full[data-toast-exiting="true"][data-placement="bottom-center"] {
    --tw-translate-y: 100%;
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }

  .data-\[toast-exiting\=true\]\:data-\[placement\=bottom-left\]\:-translate-x-full[data-toast-exiting="true"][data-placement="bottom-left"] {
    --tw-translate-x: -100%;
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }

  .data-\[toast-exiting\=true\]\:data-\[placement\=bottom-right\]\:translate-x-full[data-toast-exiting="true"][data-placement="bottom-right"] {
    --tw-translate-x: 100%;
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }

  .data-\[toast-exiting\=true\]\:data-\[placement\=top-center\]\:-translate-y-full[data-toast-exiting="true"][data-placement="top-center"] {
    --tw-translate-y: -100%;
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }

  .data-\[toast-exiting\=true\]\:data-\[placement\=top-left\]\:-translate-x-full[data-toast-exiting="true"][data-placement="top-left"] {
    --tw-translate-x: -100%;
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }

  .data-\[toast-exiting\=true\]\:data-\[placement\=top-right\]\:translate-x-full[data-toast-exiting="true"][data-placement="top-right"] {
    --tw-translate-x: 100%;
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }

  .data-\[top-bottom-scroll\=true\]\:\[mask-image\:linear-gradient\(\#000\,\#000\,transparent_0\,\#000_var\(--scroll-shadow-size\)\,\#000_calc\(100\%_-_var\(--scroll-shadow-size\)\)\,transparent\)\][data-top-bottom-scroll="true"] {
    mask-image: linear-gradient(#000, #000, transparent 0, #000 var(--scroll-shadow-size), #000 calc(100% - var(--scroll-shadow-size)), transparent);
  }

  .data-\[top-scroll\=true\]\:\[mask-image\:linear-gradient\(0deg\,\#000_calc\(100\%_-_var\(--scroll-shadow-size\)\)\,transparent\)\][data-top-scroll="true"] {
    mask-image: linear-gradient(0deg, #000 calc(100% - var(--scroll-shadow-size)), transparent);
  }

  .data-\[type\=color\]\:rounded-none[data-type="color"] {
    border-radius: 0;
  }

  .data-\[unavailable\=true\]\:cursor-default[data-unavailable="true"] {
    cursor: default;
  }

  .data-\[unavailable\=true\]\:text-default-300[data-unavailable="true"] {
    color: hsl(var(--heroui-default-300) / 1);
  }

  .data-\[unavailable\=true\]\:line-through[data-unavailable="true"] {
    text-decoration-line: line-through;
  }

  .data-\[variant\=destructive\]\:text-destructive[data-variant="destructive"] {
    color: var(--destructive);
  }

  .data-\[variant\=destructive\]\:focus\:bg-destructive\/10[data-variant="destructive"]:focus {
    background-color: var(--destructive);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .data-\[variant\=destructive\]\:focus\:bg-destructive\/10[data-variant="destructive"]:focus {
      background-color: color-mix(in oklab, var(--destructive) 10%, transparent);
    }
  }

  .data-\[variant\=destructive\]\:focus\:text-destructive[data-variant="destructive"]:focus {
    color: var(--destructive);
  }

  .data-\[variant\=outline\]\:border-l-0[data-variant="outline"] {
    border-left-style: var(--tw-border-style);
    border-left-width: 0;
  }

  .data-\[variant\=outline\]\:shadow-xs[data-variant="outline"] {
    --tw-shadow: 0 1px 2px 0 var(--tw-shadow-color, #0000000d);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .data-\[variant\=outline\]\:first\:border-l[data-variant="outline"]:first-child {
    border-left-style: var(--tw-border-style);
    border-left-width: 1px;
  }

  .data-\[variant\=vertical\]\:flex-row[data-variant="vertical"] {
    flex-direction: row;
  }

  .data-\[vaul-drawer-direction\=bottom\]\:inset-x-0[data-vaul-drawer-direction="bottom"] {
    inset-inline: calc(var(--spacing) * 0);
  }

  .data-\[vaul-drawer-direction\=bottom\]\:bottom-0[data-vaul-drawer-direction="bottom"] {
    bottom: calc(var(--spacing) * 0);
  }

  .data-\[vaul-drawer-direction\=bottom\]\:mt-24[data-vaul-drawer-direction="bottom"] {
    margin-top: calc(var(--spacing) * 24);
  }

  .data-\[vaul-drawer-direction\=bottom\]\:max-h-\[80vh\][data-vaul-drawer-direction="bottom"] {
    max-height: 80vh;
  }

  .data-\[vaul-drawer-direction\=bottom\]\:rounded-t-lg[data-vaul-drawer-direction="bottom"] {
    border-top-left-radius: var(--radius);
    border-top-right-radius: var(--radius);
  }

  .data-\[vaul-drawer-direction\=bottom\]\:border-t[data-vaul-drawer-direction="bottom"] {
    border-top-style: var(--tw-border-style);
    border-top-width: 1px;
  }

  .data-\[vaul-drawer-direction\=left\]\:inset-y-0[data-vaul-drawer-direction="left"] {
    inset-block: calc(var(--spacing) * 0);
  }

  .data-\[vaul-drawer-direction\=left\]\:left-0[data-vaul-drawer-direction="left"] {
    left: calc(var(--spacing) * 0);
  }

  .data-\[vaul-drawer-direction\=left\]\:w-3\/4[data-vaul-drawer-direction="left"] {
    width: 75%;
  }

  .data-\[vaul-drawer-direction\=left\]\:border-r[data-vaul-drawer-direction="left"] {
    border-right-style: var(--tw-border-style);
    border-right-width: 1px;
  }

  .data-\[vaul-drawer-direction\=right\]\:inset-y-0[data-vaul-drawer-direction="right"] {
    inset-block: calc(var(--spacing) * 0);
  }

  .data-\[vaul-drawer-direction\=right\]\:right-0[data-vaul-drawer-direction="right"] {
    right: calc(var(--spacing) * 0);
  }

  .data-\[vaul-drawer-direction\=right\]\:w-3\/4[data-vaul-drawer-direction="right"] {
    width: 75%;
  }

  .data-\[vaul-drawer-direction\=right\]\:border-l[data-vaul-drawer-direction="right"] {
    border-left-style: var(--tw-border-style);
    border-left-width: 1px;
  }

  .data-\[vaul-drawer-direction\=top\]\:inset-x-0[data-vaul-drawer-direction="top"] {
    inset-inline: calc(var(--spacing) * 0);
  }

  .data-\[vaul-drawer-direction\=top\]\:top-0[data-vaul-drawer-direction="top"] {
    top: calc(var(--spacing) * 0);
  }

  .data-\[vaul-drawer-direction\=top\]\:mb-24[data-vaul-drawer-direction="top"] {
    margin-bottom: calc(var(--spacing) * 24);
  }

  .data-\[vaul-drawer-direction\=top\]\:max-h-\[80vh\][data-vaul-drawer-direction="top"] {
    max-height: 80vh;
  }

  .data-\[vaul-drawer-direction\=top\]\:rounded-b-lg[data-vaul-drawer-direction="top"] {
    border-bottom-right-radius: var(--radius);
    border-bottom-left-radius: var(--radius);
  }

  .data-\[vaul-drawer-direction\=top\]\:border-b[data-vaul-drawer-direction="top"] {
    border-bottom-style: var(--tw-border-style);
    border-bottom-width: 1px;
  }

  .data-\[visible\=true\]\:pointer-events-auto[data-visible="true"] {
    pointer-events: auto;
  }

  .data-\[visible\=true\]\:cursor-pointer[data-visible="true"] {
    cursor: pointer;
  }

  .data-\[visible\=true\]\:opacity-100[data-visible="true"] {
    opacity: 1;
  }

  @media (prefers-reduced-motion: reduce) {
    .motion-reduce\:scale-100 {
      --tw-scale-x: 100%;
      --tw-scale-y: 100%;
      --tw-scale-z: 100%;
      scale: var(--tw-scale-x) var(--tw-scale-y);
    }
  }

  @media (prefers-reduced-motion: reduce) {
    .motion-reduce\:transition-none {
      transition-property: none;
    }
  }

  @media (prefers-reduced-motion: reduce) {
    .motion-reduce\:after\:transition-none:after {
      content: var(--tw-content);
      transition-property: none;
    }
  }

  @media (width >= 40rem) {
    .sm\:m-0 {
      margin: calc(var(--spacing) * 0);
    }
  }

  @media (width >= 40rem) {
    .sm\:mx-0 {
      margin-inline: calc(var(--spacing) * 0);
    }
  }

  @media (width >= 40rem) {
    .sm\:mx-1 {
      margin-inline: calc(var(--spacing) * 1);
    }
  }

  @media (width >= 40rem) {
    .sm\:mx-6 {
      margin-inline: calc(var(--spacing) * 6);
    }
  }

  @media (width >= 40rem) {
    .sm\:my-0 {
      margin-block: calc(var(--spacing) * 0);
    }
  }

  @media (width >= 40rem) {
    .sm\:my-16 {
      margin-block: calc(var(--spacing) * 16);
    }
  }

  @media (width >= 40rem) {
    .sm\:block {
      display: block;
    }
  }

  @media (width >= 40rem) {
    .sm\:flex {
      display: flex;
    }
  }

  @media (width >= 40rem) {
    .sm\:inline {
      display: inline;
    }
  }

  @media (width >= 40rem) {
    .sm\:w-64 {
      width: calc(var(--spacing) * 64);
    }
  }

  @media (width >= 40rem) {
    .sm\:w-\[350px\] {
      width: 350px;
    }
  }

  @media (width >= 40rem) {
    .sm\:w-\[356px\] {
      width: 356px;
    }
  }

  @media (width >= 40rem) {
    .sm\:w-auto {
      width: auto;
    }
  }

  @media (width >= 40rem) {
    .sm\:max-w-\[500px\] {
      max-width: 500px;
    }
  }

  @media (width >= 40rem) {
    .sm\:max-w-\[550px\] {
      max-width: 550px;
    }
  }

  @media (width >= 40rem) {
    .sm\:max-w-\[600px\] {
      max-width: 600px;
    }
  }

  @media (width >= 40rem) {
    .sm\:max-w-\[800px\] {
      max-width: 800px;
    }
  }

  @media (width >= 40rem) {
    .sm\:max-w-lg {
      max-width: var(--container-lg);
    }
  }

  @media (width >= 40rem) {
    .sm\:max-w-md {
      max-width: var(--container-md);
    }
  }

  @media (width >= 40rem) {
    .sm\:max-w-sm {
      max-width: var(--container-sm);
    }
  }

  @media (width >= 40rem) {
    .sm\:max-w-xl {
      max-width: var(--container-xl);
    }
  }

  @media (width >= 40rem) {
    .sm\:grid-cols-2 {
      grid-template-columns: repeat(2, minmax(0, 1fr));
    }
  }

  @media (width >= 40rem) {
    .sm\:grid-cols-3 {
      grid-template-columns: repeat(3, minmax(0, 1fr));
    }
  }

  @media (width >= 40rem) {
    .sm\:flex-row {
      flex-direction: row;
    }
  }

  @media (width >= 40rem) {
    .sm\:items-center {
      align-items: center;
    }
  }

  @media (width >= 40rem) {
    .sm\:items-end {
      align-items: flex-end;
    }
  }

  @media (width >= 40rem) {
    .sm\:items-start {
      align-items: flex-start;
    }
  }

  @media (width >= 40rem) {
    .sm\:justify-between {
      justify-content: space-between;
    }
  }

  @media (width >= 40rem) {
    .sm\:justify-end {
      justify-content: flex-end;
    }
  }

  @media (width >= 40rem) {
    .sm\:gap-2\.5 {
      gap: calc(var(--spacing) * 2.5);
    }
  }

  @media (width >= 40rem) {
    .sm\:px-0 {
      padding-inline: calc(var(--spacing) * 0);
    }
  }

  @media (width >= 40rem) {
    .sm\:px-6 {
      padding-inline: calc(var(--spacing) * 6);
    }
  }

  @media (width >= 40rem) {
    .sm\:pt-6 {
      padding-top: calc(var(--spacing) * 6);
    }
  }

  @media (width >= 40rem) {
    .sm\:pr-2\.5 {
      padding-right: calc(var(--spacing) * 2.5);
    }
  }

  @media (width >= 40rem) {
    .sm\:pl-2\.5 {
      padding-left: calc(var(--spacing) * 2.5);
    }
  }

  @media (width >= 40rem) {
    .sm\:text-left {
      text-align: left;
    }
  }

  @media (width >= 40rem) {
    .sm\:\[--scale-enter\:100\%\] {
      --scale-enter: 100%;
    }
  }

  @media (width >= 40rem) {
    .sm\:\[--scale-exit\:103\%\] {
      --scale-exit: 103%;
    }
  }

  @media (width >= 40rem) {
    .sm\:\[--slide-enter\:0px\] {
      --slide-enter: 0px;
    }
  }

  @media (width >= 40rem) {
    .sm\:\[--slide-exit\:0px\] {
      --slide-exit: 0px;
    }
  }

  @media (width >= 40rem) {
    .sm\:data-\[placement\=bottom-center\]\:mx-auto[data-placement="bottom-center"] {
      margin-inline: auto;
    }
  }

  @media (width >= 40rem) {
    .sm\:data-\[placement\=bottom-center\]\:w-max[data-placement="bottom-center"] {
      width: max-content;
    }
  }

  @media (width >= 40rem) {
    .sm\:data-\[placement\=bottom-left\]\:ml-2[data-placement="bottom-left"] {
      margin-left: calc(var(--spacing) * 2);
    }
  }

  @media (width >= 40rem) {
    .sm\:data-\[placement\=bottom-left\]\:w-max[data-placement="bottom-left"] {
      width: max-content;
    }
  }

  @media (width >= 40rem) {
    .sm\:data-\[placement\=bottom-right\]\:mr-2[data-placement="bottom-right"] {
      margin-right: calc(var(--spacing) * 2);
    }
  }

  @media (width >= 40rem) {
    .sm\:data-\[placement\=bottom-right\]\:w-max[data-placement="bottom-right"] {
      width: max-content;
    }
  }

  @media (width >= 40rem) {
    .sm\:data-\[placement\=top-center\]\:mx-auto[data-placement="top-center"] {
      margin-inline: auto;
    }
  }

  @media (width >= 40rem) {
    .sm\:data-\[placement\=top-center\]\:w-max[data-placement="top-center"] {
      width: max-content;
    }
  }

  @media (width >= 40rem) {
    .sm\:data-\[placement\=top-left\]\:ml-2[data-placement="top-left"] {
      margin-left: calc(var(--spacing) * 2);
    }
  }

  @media (width >= 40rem) {
    .sm\:data-\[placement\=top-left\]\:w-max[data-placement="top-left"] {
      width: max-content;
    }
  }

  @media (width >= 40rem) {
    .sm\:data-\[placement\=top-right\]\:mr-2[data-placement="top-right"] {
      margin-right: calc(var(--spacing) * 2);
    }
  }

  @media (width >= 40rem) {
    .sm\:data-\[placement\=top-right\]\:w-max[data-placement="top-right"] {
      width: max-content;
    }
  }

  @media (width >= 40rem) {
    .data-\[vaul-drawer-direction\=left\]\:sm\:max-w-sm[data-vaul-drawer-direction="left"] {
      max-width: var(--container-sm);
    }
  }

  @media (width >= 40rem) {
    .data-\[vaul-drawer-direction\=right\]\:sm\:max-w-sm[data-vaul-drawer-direction="right"] {
      max-width: var(--container-sm);
    }
  }

  @media (width >= 40rem) {
    .sm\:data-\[visible\=true\]\:pointer-events-none[data-visible="true"] {
      pointer-events: none;
    }
  }

  @media (width >= 40rem) {
    .sm\:data-\[visible\=true\]\:opacity-0[data-visible="true"] {
      opacity: 0;
    }
  }

  @media (width >= 40rem) {
    .sm\:group-data-\[hover\=true\]\:data-\[visible\=true\]\:pointer-events-auto:is(:where(.group)[data-hover="true"] *)[data-visible="true"] {
      pointer-events: auto;
    }
  }

  @media (width >= 40rem) {
    .sm\:group-data-\[hover\=true\]\:data-\[visible\=true\]\:opacity-100:is(:where(.group)[data-hover="true"] *)[data-visible="true"] {
      opacity: 1;
    }
  }

  @media (width >= 48rem) {
    .md\:absolute {
      position: absolute;
    }
  }

  @media (width >= 48rem) {
    .md\:col-span-1 {
      grid-column: span 1 / span 1;
    }
  }

  @media (width >= 48rem) {
    .md\:col-span-2 {
      grid-column: span 2 / span 2;
    }
  }

  @media (width >= 48rem) {
    .md\:col-span-4 {
      grid-column: span 4 / span 4;
    }
  }

  @media (width >= 48rem) {
    .md\:col-span-8 {
      grid-column: span 8 / span 8;
    }
  }

  @media (width >= 48rem) {
    .md\:mr-0\.5 {
      margin-right: calc(var(--spacing) * .5);
    }
  }

  @media (width >= 48rem) {
    .md\:block {
      display: block;
    }
  }

  @media (width >= 48rem) {
    .md\:flex {
      display: flex;
    }
  }

  @media (width >= 48rem) {
    .md\:hidden {
      display: none;
    }
  }

  @media (width >= 48rem) {
    .md\:table-cell {
      display: table-cell;
    }
  }

  @media (width >= 48rem) {
    .md\:w-60 {
      width: calc(var(--spacing) * 60);
    }
  }

  @media (width >= 48rem) {
    .md\:w-64 {
      width: calc(var(--spacing) * 64);
    }
  }

  @media (width >= 48rem) {
    .md\:w-\[var\(--radix-navigation-menu-viewport-width\)\] {
      width: var(--radix-navigation-menu-viewport-width);
    }
  }

  @media (width >= 48rem) {
    .md\:w-auto {
      width: auto;
    }
  }

  @media (width >= 48rem) {
    .md\:max-w-2xl {
      max-width: var(--container-2xl);
    }
  }

  @media (width >= 48rem) {
    .md\:grid-cols-1 {
      grid-template-columns: repeat(1, minmax(0, 1fr));
    }
  }

  @media (width >= 48rem) {
    .md\:grid-cols-2 {
      grid-template-columns: repeat(2, minmax(0, 1fr));
    }
  }

  @media (width >= 48rem) {
    .md\:grid-cols-3 {
      grid-template-columns: repeat(3, minmax(0, 1fr));
    }
  }

  @media (width >= 48rem) {
    .md\:grid-cols-4 {
      grid-template-columns: repeat(4, minmax(0, 1fr));
    }
  }

  @media (width >= 48rem) {
    .md\:grid-cols-5 {
      grid-template-columns: repeat(5, minmax(0, 1fr));
    }
  }

  @media (width >= 48rem) {
    .md\:grid-cols-12 {
      grid-template-columns: repeat(12, minmax(0, 1fr));
    }
  }

  @media (width >= 48rem) {
    .md\:flex-row {
      flex-direction: row;
    }
  }

  @media (width >= 48rem) {
    .md\:justify-start {
      justify-content: flex-start;
    }
  }

  @media (width >= 48rem) {
    .md\:gap-6 {
      gap: calc(var(--spacing) * 6);
    }
  }

  @media (width >= 48rem) {
    .md\:p-10 {
      padding: calc(var(--spacing) * 10);
    }
  }

  @media (width >= 48rem) {
    .md\:py-6 {
      padding-block: calc(var(--spacing) * 6);
    }
  }

  @media (width >= 48rem) {
    .md\:text-sm {
      font-size: var(--text-sm);
      line-height: var(--tw-leading, var(--text-sm--line-height));
    }
  }

  @media (width >= 48rem) {
    .md\:opacity-0 {
      opacity: 0;
    }
  }

  @media (width >= 48rem) {
    .md\:peer-data-\[variant\=inset\]\:m-2:is(:where(.peer)[data-variant="inset"] ~ *) {
      margin: calc(var(--spacing) * 2);
    }
  }

  @media (width >= 48rem) {
    .md\:peer-data-\[variant\=inset\]\:ml-0:is(:where(.peer)[data-variant="inset"] ~ *) {
      margin-left: calc(var(--spacing) * 0);
    }
  }

  @media (width >= 48rem) {
    .md\:peer-data-\[variant\=inset\]\:rounded-xl:is(:where(.peer)[data-variant="inset"] ~ *) {
      border-radius: calc(var(--radius)  + 4px);
    }
  }

  @media (width >= 48rem) {
    .md\:peer-data-\[variant\=inset\]\:shadow-sm:is(:where(.peer)[data-variant="inset"] ~ *) {
      --tw-shadow: 0 1px 3px 0 var(--tw-shadow-color, #0000001a), 0 1px 2px -1px var(--tw-shadow-color, #0000001a);
      box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
    }
  }

  @media (width >= 48rem) {
    .md\:peer-data-\[variant\=inset\]\:peer-data-\[state\=collapsed\]\:ml-2:is(:where(.peer)[data-variant="inset"] ~ *):is(:where(.peer)[data-state="collapsed"] ~ *) {
      margin-left: calc(var(--spacing) * 2);
    }
  }

  @media (width >= 48rem) {
    .md\:after\:hidden:after {
      content: var(--tw-content);
      display: none;
    }
  }

  @media (width >= 64rem) {
    .lg\:col-span-2 {
      grid-column: span 2 / span 2;
    }
  }

  @media (width >= 64rem) {
    .lg\:ml-0 {
      margin-left: calc(var(--spacing) * 0);
    }
  }

  @media (width >= 64rem) {
    .lg\:block {
      display: block;
    }
  }

  @media (width >= 64rem) {
    .lg\:flex {
      display: flex;
    }
  }

  @media (width >= 64rem) {
    .lg\:hidden {
      display: none;
    }
  }

  @media (width >= 64rem) {
    .lg\:inline {
      display: inline;
    }
  }

  @media (width >= 64rem) {
    .lg\:h-\[calc\(100svh-56px\)\] {
      height: calc(100svh - 56px);
    }
  }

  @media (width >= 64rem) {
    .lg\:h-\[calc\(100svh-96px\)\] {
      height: calc(100svh - 96px);
    }
  }

  @media (width >= 64rem) {
    .lg\:w-fit {
      width: fit-content;
    }
  }

  @media (width >= 64rem) {
    .lg\:max-w-3xl {
      max-width: var(--container-3xl);
    }
  }

  @media (width >= 64rem) {
    .lg\:grid-cols-2 {
      grid-template-columns: repeat(2, minmax(0, 1fr));
    }
  }

  @media (width >= 64rem) {
    .lg\:grid-cols-3 {
      grid-template-columns: repeat(3, minmax(0, 1fr));
    }
  }

  @media (width >= 64rem) {
    .lg\:grid-cols-4 {
      grid-template-columns: repeat(4, minmax(0, 1fr));
    }
  }

  @media (width >= 64rem) {
    .lg\:grid-cols-5 {
      grid-template-columns: repeat(5, minmax(0, 1fr));
    }
  }

  @media (width >= 64rem) {
    .lg\:grid-cols-6 {
      grid-template-columns: repeat(6, minmax(0, 1fr));
    }
  }

  @media (width >= 64rem) {
    .lg\:gap-2 {
      gap: calc(var(--spacing) * 2);
    }
  }

  @media (width >= 64rem) {
    :where(.lg\:space-x-8 > :not(:last-child)) {
      --tw-space-x-reverse: 0;
      margin-inline-start: calc(calc(var(--spacing) * 8) * var(--tw-space-x-reverse));
      margin-inline-end: calc(calc(var(--spacing) * 8) * calc(1 - var(--tw-space-x-reverse)));
    }
  }

  @media (width >= 64rem) {
    .lg\:rounded-md {
      border-radius: calc(var(--radius)  - 2px);
    }
  }

  @media (width >= 64rem) {
    .lg\:border {
      border-style: var(--tw-border-style);
      border-width: 1px;
    }
  }

  @media (width >= 64rem) {
    .lg\:p-2 {
      padding: calc(var(--spacing) * 2);
    }
  }

  @media (width >= 64rem) {
    .lg\:px-6 {
      padding-inline: calc(var(--spacing) * 6);
    }
  }

  @media (width >= 80rem) {
    .xl\:inline {
      display: inline;
    }
  }

  @media (width >= 80rem) {
    .xl\:grid-cols-6 {
      grid-template-columns: repeat(6, minmax(0, 1fr));
    }
  }

  @container card (width >= 200px) {
    .\@\[200px\]\/card\:text-2xl {
      font-size: var(--text-2xl);
      line-height: var(--tw-leading, var(--text-2xl--line-height));
    }
  }

  @container card (width >= 250px) {
    .\@\[250px\]\/card\:text-3xl {
      font-size: var(--text-3xl);
      line-height: var(--tw-leading, var(--text-3xl--line-height));
    }
  }

  @container card (width >= 540px) {
    .\@\[540px\]\/card\:block {
      display: block;
    }
  }

  @container card (width >= 540px) {
    .\@\[540px\]\/card\:hidden {
      display: none;
    }
  }

  @container card (width >= 767px) {
    .\@\[767px\]\/card\:flex {
      display: flex;
    }
  }

  @container card (width >= 767px) {
    .\@\[767px\]\/card\:hidden {
      display: none;
    }
  }

  @container main (width >= 36rem) {
    .\@xl\/main\:grid-cols-2 {
      grid-template-columns: repeat(2, minmax(0, 1fr));
    }
  }

  @container main (width >= 56rem) {
    .\@4xl\/main\:flex {
      display: flex;
    }
  }

  @container main (width >= 56rem) {
    .\@4xl\/main\:hidden {
      display: none;
    }
  }

  @container main (width >= 64rem) {
    .\@5xl\/main\:grid-cols-4 {
      grid-template-columns: repeat(4, minmax(0, 1fr));
    }
  }

  .rtl\:right-auto:where(:dir(rtl), [dir="rtl"], [dir="rtl"] *) {
    right: auto;
  }

  .rtl\:left-2:where(:dir(rtl), [dir="rtl"], [dir="rtl"] *) {
    left: calc(var(--spacing) * 2);
  }

  .rtl\:origin-top-right:where(:dir(rtl), [dir="rtl"], [dir="rtl"] *) {
    transform-origin: 100% 0;
  }

  .rtl\:-rotate-180:where(:dir(rtl), [dir="rtl"], [dir="rtl"] *) {
    rotate: -180deg;
  }

  .rtl\:rotate-180:where(:dir(rtl), [dir="rtl"], [dir="rtl"] *) {
    rotate: 180deg;
  }

  .rtl\:flex-row-reverse:where(:dir(rtl), [dir="rtl"], [dir="rtl"] *) {
    flex-direction: row-reverse;
  }

  :where(.rtl\:space-x-reverse:where(:dir(rtl), [dir="rtl"], [dir="rtl"] *) > :not(:last-child)) {
    --tw-space-x-reverse: 1;
  }

  .rtl\:data-\[focus-visible\=true\]\:translate-x-3:where(:dir(rtl), [dir="rtl"], [dir="rtl"] *)[data-focus-visible="true"] {
    --tw-translate-x: calc(var(--spacing) * 3);
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }

  .rtl\:data-\[hover\=true\]\:translate-x-3:where(:dir(rtl), [dir="rtl"], [dir="rtl"] *)[data-hover="true"] {
    --tw-translate-x: calc(var(--spacing) * 3);
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }

  .rtl\:data-\[open\=true\]\:-rotate-90:where(:dir(rtl), [dir="rtl"], [dir="rtl"] *)[data-open="true"] {
    rotate: -90deg;
  }

  .dark\:border-danger-100:is(.dark *) {
    border-color: hsl(var(--heroui-danger-100) / 1);
  }

  .dark\:border-default-200:is(.dark *) {
    border-color: hsl(var(--heroui-default-200) / 1);
  }

  .dark\:border-input:is(.dark *) {
    border-color: var(--input);
  }

  .dark\:border-primary-100:is(.dark *) {
    border-color: hsl(var(--heroui-primary-100) / 1);
  }

  .dark\:border-success-100:is(.dark *) {
    border-color: hsl(var(--heroui-success-100) / 1);
  }

  .dark\:border-warning-100:is(.dark *) {
    border-color: hsl(var(--heroui-warning-100) / 1);
  }

  .dark\:bg-background:is(.dark *) {
    background-color: var(--background);
  }

  .dark\:bg-background\/20:is(.dark *) {
    background-color: var(--background);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:bg-background\/20:is(.dark *) {
      background-color: color-mix(in oklab, var(--background) 20%, transparent);
    }
  }

  .dark\:bg-content2:is(.dark *) {
    background-color: hsl(var(--heroui-content2) / 1);
  }

  .dark\:bg-danger-50:is(.dark *) {
    background-color: hsl(var(--heroui-danger-50) / 1);
  }

  .dark\:bg-danger-50\/50:is(.dark *) {
    background-color: hsl(var(--heroui-danger-50) / 1);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:bg-danger-50\/50:is(.dark *) {
      background-color: color-mix(in oklab, hsl(var(--heroui-danger-50) / 1) 50%, transparent);
    }
  }

  .dark\:bg-danger-100:is(.dark *) {
    background-color: hsl(var(--heroui-danger-100) / 1);
  }

  .dark\:bg-default:is(.dark *) {
    background-color: hsl(var(--heroui-default) / 1);
  }

  .dark\:bg-default-50\/50:is(.dark *) {
    background-color: hsl(var(--heroui-default-50) / 1);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:bg-default-50\/50:is(.dark *) {
      background-color: color-mix(in oklab, hsl(var(--heroui-default-50) / 1) 50%, transparent);
    }
  }

  .dark\:bg-default-100:is(.dark *) {
    background-color: hsl(var(--heroui-default-100) / 1);
  }

  .dark\:bg-destructive\/60:is(.dark *) {
    background-color: var(--destructive);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:bg-destructive\/60:is(.dark *) {
      background-color: color-mix(in oklab, var(--destructive) 60%, transparent);
    }
  }

  .dark\:bg-input\/30:is(.dark *) {
    background-color: var(--input);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:bg-input\/30:is(.dark *) {
      background-color: color-mix(in oklab, var(--input) 30%, transparent);
    }
  }

  .dark\:bg-primary-50:is(.dark *) {
    background-color: hsl(var(--heroui-primary-50) / 1);
  }

  .dark\:bg-primary-50\/50:is(.dark *) {
    background-color: hsl(var(--heroui-primary-50) / 1);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:bg-primary-50\/50:is(.dark *) {
      background-color: color-mix(in oklab, hsl(var(--heroui-primary-50) / 1) 50%, transparent);
    }
  }

  .dark\:bg-primary-100:is(.dark *) {
    background-color: hsl(var(--heroui-primary-100) / 1);
  }

  .dark\:bg-secondary-50:is(.dark *) {
    background-color: hsl(var(--heroui-secondary-50) / 1);
  }

  .dark\:bg-secondary-50\/50:is(.dark *) {
    background-color: hsl(var(--heroui-secondary-50) / 1);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:bg-secondary-50\/50:is(.dark *) {
      background-color: color-mix(in oklab, hsl(var(--heroui-secondary-50) / 1) 50%, transparent);
    }
  }

  .dark\:bg-secondary-100:is(.dark *) {
    background-color: hsl(var(--heroui-secondary-100) / 1);
  }

  .dark\:bg-success-50:is(.dark *) {
    background-color: hsl(var(--heroui-success-50) / 1);
  }

  .dark\:bg-success-50\/50:is(.dark *) {
    background-color: hsl(var(--heroui-success-50) / 1);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:bg-success-50\/50:is(.dark *) {
      background-color: color-mix(in oklab, hsl(var(--heroui-success-50) / 1) 50%, transparent);
    }
  }

  .dark\:bg-success-100:is(.dark *) {
    background-color: hsl(var(--heroui-success-100) / 1);
  }

  .dark\:bg-transparent:is(.dark *) {
    background-color: #0000;
  }

  .dark\:bg-warning-50:is(.dark *) {
    background-color: hsl(var(--heroui-warning-50) / 1);
  }

  .dark\:bg-warning-50\/50:is(.dark *) {
    background-color: hsl(var(--heroui-warning-50) / 1);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:bg-warning-50\/50:is(.dark *) {
      background-color: color-mix(in oklab, hsl(var(--heroui-warning-50) / 1) 50%, transparent);
    }
  }

  .dark\:bg-warning-100:is(.dark *) {
    background-color: hsl(var(--heroui-warning-100) / 1);
  }

  .dark\:fill-green-400:is(.dark *) {
    fill: var(--color-green-400);
  }

  .dark\:text-amber-400:is(.dark *) {
    color: var(--color-amber-400);
  }

  .dark\:text-amber-500:is(.dark *) {
    color: var(--color-amber-500);
  }

  .dark\:text-danger-500:is(.dark *) {
    color: hsl(var(--heroui-danger-500) / 1);
  }

  .dark\:text-green-500:is(.dark *) {
    color: var(--color-green-500);
  }

  .dark\:text-muted-foreground:is(.dark *) {
    color: var(--muted-foreground);
  }

  .dark\:text-red-500:is(.dark *) {
    color: var(--color-red-500);
  }

  .dark\:text-success:is(.dark *) {
    color: hsl(var(--heroui-success) / 1);
  }

  .dark\:text-warning:is(.dark *) {
    color: hsl(var(--heroui-warning) / 1);
  }

  .dark\:brightness-\[0\.2\]:is(.dark *) {
    --tw-brightness: brightness(.2);
    filter: var(--tw-blur, ) var(--tw-brightness, ) var(--tw-contrast, ) var(--tw-grayscale, ) var(--tw-hue-rotate, ) var(--tw-invert, ) var(--tw-saturate, ) var(--tw-sepia, ) var(--tw-drop-shadow, );
  }

  .dark\:grayscale:is(.dark *) {
    --tw-grayscale: grayscale(100%);
    filter: var(--tw-blur, ) var(--tw-brightness, ) var(--tw-contrast, ) var(--tw-grayscale, ) var(--tw-hue-rotate, ) var(--tw-invert, ) var(--tw-saturate, ) var(--tw-sepia, ) var(--tw-drop-shadow, );
  }

  .dark\:placeholder\:text-danger-500:is(.dark *)::placeholder {
    color: hsl(var(--heroui-danger-500) / 1);
  }

  .dark\:placeholder\:text-success:is(.dark *)::placeholder {
    color: hsl(var(--heroui-success) / 1);
  }

  .dark\:placeholder\:text-warning:is(.dark *)::placeholder {
    color: hsl(var(--heroui-warning) / 1);
  }

  .dark\:before\:via-default-700\/10:is(.dark *):before {
    content: var(--tw-content);
    --tw-gradient-via: hsl(var(--heroui-default-700) / 1);
    --tw-gradient-via-stops: var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-via) var(--tw-gradient-via-position), var(--tw-gradient-to) var(--tw-gradient-to-position);
    --tw-gradient-stops: var(--tw-gradient-via-stops);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:before\:via-default-700\/10:is(.dark *):before {
      --tw-gradient-via: color-mix(in oklab, hsl(var(--heroui-default-700) / 1) 10%, transparent);
    }
  }

  .dark\:after\:bg-content2:is(.dark *):after {
    content: var(--tw-content);
    background-color: hsl(var(--heroui-content2) / 1);
  }

  @media (hover: hover) {
    .dark\:hover\:bg-accent\/50:is(.dark *):hover {
      background-color: var(--accent);
    }

    @supports (color: color-mix(in lab, red, red)) {
      .dark\:hover\:bg-accent\/50:is(.dark *):hover {
        background-color: color-mix(in oklab, var(--accent) 50%, transparent);
      }
    }
  }

  @media (hover: hover) {
    .dark\:hover\:bg-input\/30:is(.dark *):hover {
      background-color: var(--input);
    }

    @supports (color: color-mix(in lab, red, red)) {
      .dark\:hover\:bg-input\/30:is(.dark *):hover {
        background-color: color-mix(in oklab, var(--input) 30%, transparent);
      }
    }
  }

  @media (hover: hover) {
    .dark\:hover\:bg-input\/50:is(.dark *):hover {
      background-color: var(--input);
    }

    @supports (color: color-mix(in lab, red, red)) {
      .dark\:hover\:bg-input\/50:is(.dark *):hover {
        background-color: color-mix(in oklab, var(--input) 50%, transparent);
      }
    }
  }

  @media (hover: hover) {
    .dark\:hover\:bg-slate-900:is(.dark *):hover {
      background-color: var(--color-slate-900);
    }
  }

  @media (hover: hover) {
    .dark\:hover\:text-slate-50:is(.dark *):hover {
      color: var(--color-slate-50);
    }
  }

  .dark\:focus\:bg-danger-400\/20:is(.dark *):focus {
    background-color: hsl(var(--heroui-danger-400) / 1);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:focus\:bg-danger-400\/20:is(.dark *):focus {
      background-color: color-mix(in oklab, hsl(var(--heroui-danger-400) / 1) 20%, transparent);
    }
  }

  .dark\:focus\:bg-success-400\/20:is(.dark *):focus {
    background-color: hsl(var(--heroui-success-400) / 1);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:focus\:bg-success-400\/20:is(.dark *):focus {
      background-color: color-mix(in oklab, hsl(var(--heroui-success-400) / 1) 20%, transparent);
    }
  }

  .dark\:focus\:bg-warning-400\/20:is(.dark *):focus {
    background-color: hsl(var(--heroui-warning-400) / 1);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:focus\:bg-warning-400\/20:is(.dark *):focus {
      background-color: color-mix(in oklab, hsl(var(--heroui-warning-400) / 1) 20%, transparent);
    }
  }

  .dark\:focus-visible\:bg-input\/30:is(.dark *):focus-visible {
    background-color: var(--input);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:focus-visible\:bg-input\/30:is(.dark *):focus-visible {
      background-color: color-mix(in oklab, var(--input) 30%, transparent);
    }
  }

  .dark\:focus-visible\:ring-destructive\/40:is(.dark *):focus-visible {
    --tw-ring-color: var(--destructive);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:focus-visible\:ring-destructive\/40:is(.dark *):focus-visible {
      --tw-ring-color: color-mix(in oklab, var(--destructive) 40%, transparent);
    }
  }

  .dark\:aria-invalid\:ring-destructive\/40:is(.dark *)[aria-invalid="true"] {
    --tw-ring-color: var(--destructive);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:aria-invalid\:ring-destructive\/40:is(.dark *)[aria-invalid="true"] {
      --tw-ring-color: color-mix(in oklab, var(--destructive) 40%, transparent);
    }
  }

  .dark\:data-\[active\=true\]\:aria-invalid\:ring-destructive\/40:is(.dark *)[data-active="true"][aria-invalid="true"] {
    --tw-ring-color: var(--destructive);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:data-\[active\=true\]\:aria-invalid\:ring-destructive\/40:is(.dark *)[data-active="true"][aria-invalid="true"] {
      --tw-ring-color: color-mix(in oklab, var(--destructive) 40%, transparent);
    }
  }

  .dark\:data-\[hover\=true\]\:bg-content2:is(.dark *)[data-hover="true"] {
    background-color: hsl(var(--heroui-content2) / 1);
  }

  .dark\:data-\[hover\=true\]\:bg-danger-50:is(.dark *)[data-hover="true"] {
    background-color: hsl(var(--heroui-danger-50) / 1);
  }

  .dark\:data-\[hover\=true\]\:bg-success-50:is(.dark *)[data-hover="true"] {
    background-color: hsl(var(--heroui-success-50) / 1);
  }

  .dark\:data-\[hover\=true\]\:bg-warning-50:is(.dark *)[data-hover="true"] {
    background-color: hsl(var(--heroui-warning-50) / 1);
  }

  .dark\:data-\[hover\=true\]\:text-danger-500:is(.dark *)[data-hover="true"] {
    color: hsl(var(--heroui-danger-500) / 1);
  }

  .dark\:data-\[hover\=true\]\:text-success-500:is(.dark *)[data-hover="true"] {
    color: hsl(var(--heroui-success-500) / 1);
  }

  .dark\:data-\[hover\=true\]\:text-warning-500:is(.dark *)[data-hover="true"] {
    color: hsl(var(--heroui-warning-500) / 1);
  }

  .dark\:data-\[invalid\=true\]\:focus\:bg-danger-400\/20:is(.dark *)[data-invalid="true"]:focus {
    background-color: hsl(var(--heroui-danger-400) / 1);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:data-\[invalid\=true\]\:focus\:bg-danger-400\/20:is(.dark *)[data-invalid="true"]:focus {
      background-color: color-mix(in oklab, hsl(var(--heroui-danger-400) / 1) 20%, transparent);
    }
  }

  .dark\:data-\[selected\=true\]\:text-danger-500:is(.dark *)[data-selected="true"] {
    color: hsl(var(--heroui-danger-500) / 1);
  }

  .dark\:data-\[selected\=true\]\:text-success:is(.dark *)[data-selected="true"] {
    color: hsl(var(--heroui-success) / 1);
  }

  .dark\:data-\[selected\=true\]\:text-warning:is(.dark *)[data-selected="true"] {
    color: hsl(var(--heroui-warning) / 1);
  }

  .dark\:data-\[selected\=true\]\:data-\[hover\=true\]\:bg-danger:is(.dark *)[data-selected="true"][data-hover="true"] {
    background-color: hsl(var(--heroui-danger) / 1);
  }

  .dark\:data-\[selected\=true\]\:data-\[hover\=true\]\:bg-success:is(.dark *)[data-selected="true"][data-hover="true"] {
    background-color: hsl(var(--heroui-success) / 1);
  }

  .dark\:data-\[selected\=true\]\:data-\[hover\=true\]\:bg-warning:is(.dark *)[data-selected="true"][data-hover="true"] {
    background-color: hsl(var(--heroui-warning) / 1);
  }

  .dark\:data-\[selected\=true\]\:data-\[hover\=true\]\:text-danger-foreground:is(.dark *)[data-selected="true"][data-hover="true"] {
    color: hsl(var(--heroui-danger-foreground) / 1);
  }

  .dark\:data-\[selected\=true\]\:data-\[hover\=true\]\:text-success-foreground:is(.dark *)[data-selected="true"][data-hover="true"] {
    color: hsl(var(--heroui-success-foreground) / 1);
  }

  .dark\:data-\[selected\=true\]\:data-\[hover\=true\]\:text-warning-foreground:is(.dark *)[data-selected="true"][data-hover="true"] {
    color: hsl(var(--heroui-warning-foreground) / 1);
  }

  .dark\:data-\[selected\=true\]\:data-\[range-selection\=true\]\:text-success-500:is(.dark *)[data-selected="true"][data-range-selection="true"] {
    color: hsl(var(--heroui-success-500) / 1);
  }

  .dark\:data-\[selected\=true\]\:data-\[range-selection\=true\]\:before\:bg-success-50:is(.dark *)[data-selected="true"][data-range-selection="true"]:before {
    content: var(--tw-content);
    background-color: hsl(var(--heroui-success-50) / 1);
  }

  .dark\:data-\[selected\=true\]\:data-\[range-selection\=true\]\:before\:bg-warning-50:is(.dark *)[data-selected="true"][data-range-selection="true"]:before {
    content: var(--tw-content);
    background-color: hsl(var(--heroui-warning-50) / 1);
  }

  .dark\:data-\[selected\=true\]\:data-\[selection-end\=true\]\:data-\[range-selection\=true\]\:text-success-foreground:is(.dark *)[data-selected="true"][data-selection-end="true"][data-range-selection="true"] {
    color: hsl(var(--heroui-success-foreground) / 1);
  }

  .dark\:data-\[selected\=true\]\:data-\[selection-start\=true\]\:data-\[range-selection\=true\]\:text-success-foreground:is(.dark *)[data-selected="true"][data-selection-start="true"][data-range-selection="true"] {
    color: hsl(var(--heroui-success-foreground) / 1);
  }

  :is(.dark\:\*\:data-\[slot\=card\]\:bg-card:is(.dark *) > *)[data-slot="card"] {
    background-color: var(--card);
  }

  .dark\:data-\[state\=active\]\:border-input:is(.dark *)[data-state="active"] {
    border-color: var(--input);
  }

  .dark\:data-\[state\=active\]\:bg-input\/30:is(.dark *)[data-state="active"] {
    background-color: var(--input);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:data-\[state\=active\]\:bg-input\/30:is(.dark *)[data-state="active"] {
      background-color: color-mix(in oklab, var(--input) 30%, transparent);
    }
  }

  .dark\:data-\[state\=active\]\:text-foreground:is(.dark *)[data-state="active"] {
    color: var(--foreground);
  }

  .dark\:data-\[state\=checked\]\:bg-primary:is(.dark *)[data-state="checked"] {
    background-color: var(--primary);
  }

  .dark\:data-\[state\=checked\]\:bg-primary-foreground:is(.dark *)[data-state="checked"] {
    background-color: var(--primary-foreground);
  }

  .dark\:data-\[state\=unchecked\]\:bg-foreground:is(.dark *)[data-state="unchecked"] {
    background-color: var(--foreground);
  }

  .dark\:data-\[state\=unchecked\]\:bg-input\/80:is(.dark *)[data-state="unchecked"] {
    background-color: var(--input);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:data-\[state\=unchecked\]\:bg-input\/80:is(.dark *)[data-state="unchecked"] {
      background-color: color-mix(in oklab, var(--input) 80%, transparent);
    }
  }

  .dark\:data-\[variant\=destructive\]\:focus\:bg-destructive\/20:is(.dark *)[data-variant="destructive"]:focus {
    background-color: var(--destructive);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:data-\[variant\=destructive\]\:focus\:bg-destructive\/20:is(.dark *)[data-variant="destructive"]:focus {
      background-color: color-mix(in oklab, var(--destructive) 20%, transparent);
    }
  }

  .\[\&_\.ProseMirror\]\:min-h-\[80px\] .ProseMirror {
    min-height: 80px;
  }

  .\[\&_\.ProseMirror\]\:outline-none .ProseMirror {
    --tw-outline-style: none;
    outline-style: none;
  }

  .\[\&_\.chevron-icon\]\:flex-none .chevron-icon {
    flex: none;
  }

  .\[\&_\.chevron-icon\]\:rotate-180 .chevron-icon {
    rotate: 180deg;
  }

  .\[\&_\.chevron-icon\]\:transition-transform .chevron-icon {
    transition-property: transform, translate, scale, rotate;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }

  .\[\&_\.recharts-cartesian-axis-tick_text\]\:fill-muted-foreground .recharts-cartesian-axis-tick text {
    fill: var(--muted-foreground);
  }

  .\[\&_\.recharts-cartesian-grid_line\[stroke\=\'\#ccc\'\]\]\:stroke-border\/50 .recharts-cartesian-grid line[stroke="#ccc"] {
    stroke: var(--border);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .\[\&_\.recharts-cartesian-grid_line\[stroke\=\'\#ccc\'\]\]\:stroke-border\/50 .recharts-cartesian-grid line[stroke="#ccc"] {
      stroke: color-mix(in oklab, var(--border) 50%, transparent);
    }
  }

  .\[\&_\.recharts-curve\.recharts-tooltip-cursor\]\:stroke-border .recharts-curve.recharts-tooltip-cursor {
    stroke: var(--border);
  }

  .\[\&_\.recharts-dot\[stroke\=\'\#fff\'\]\]\:stroke-transparent .recharts-dot[stroke="#fff"] {
    stroke: #0000;
  }

  .\[\&_\.recharts-layer\]\:outline-hidden .recharts-layer {
    --tw-outline-style: none;
    outline-style: none;
  }

  @media (forced-colors: active) {
    .\[\&_\.recharts-layer\]\:outline-hidden .recharts-layer {
      outline-offset: 2px;
      outline: 2px solid #0000;
    }
  }

  .\[\&_\.recharts-polar-grid_\[stroke\=\'\#ccc\'\]\]\:stroke-border .recharts-polar-grid [stroke="#ccc"] {
    stroke: var(--border);
  }

  .\[\&_\.recharts-radial-bar-background-sector\]\:fill-muted .recharts-radial-bar-background-sector {
    fill: var(--muted);
  }

  .\[\&_\.recharts-rectangle\.recharts-tooltip-cursor\]\:fill-muted .recharts-rectangle.recharts-tooltip-cursor {
    fill: var(--muted);
  }

  .\[\&_\.recharts-reference-line_\[stroke\=\'\#ccc\'\]\]\:stroke-border .recharts-reference-line [stroke="#ccc"] {
    stroke: var(--border);
  }

  .\[\&_\.recharts-sector\]\:outline-hidden .recharts-sector {
    --tw-outline-style: none;
    outline-style: none;
  }

  @media (forced-colors: active) {
    .\[\&_\.recharts-sector\]\:outline-hidden .recharts-sector {
      outline-offset: 2px;
      outline: 2px solid #0000;
    }
  }

  .\[\&_\.recharts-sector\[stroke\=\'\#fff\'\]\]\:stroke-transparent .recharts-sector[stroke="#fff"] {
    stroke: #0000;
  }

  .\[\&_\.recharts-surface\]\:outline-hidden .recharts-surface {
    --tw-outline-style: none;
    outline-style: none;
  }

  @media (forced-colors: active) {
    .\[\&_\.recharts-surface\]\:outline-hidden .recharts-surface {
      outline-offset: 2px;
      outline: 2px solid #0000;
    }
  }

  .\[\&_\.tabler-icon\]\:h-5 .tabler-icon {
    height: calc(var(--spacing) * 5);
  }

  .\[\&_\.tabler-icon\]\:min-h-\[20px\] .tabler-icon {
    min-height: 20px;
  }

  .\[\&_\.tabler-icon\]\:w-5 .tabler-icon {
    width: calc(var(--spacing) * 5);
  }

  .\[\&_\.tabler-icon\]\:min-w-\[20px\] .tabler-icon {
    min-width: 20px;
  }

  .\[\&_\[cmdk-group-heading\]\]\:px-2 [cmdk-group-heading] {
    padding-inline: calc(var(--spacing) * 2);
  }

  .\[\&_\[cmdk-group-heading\]\]\:py-1\.5 [cmdk-group-heading] {
    padding-block: calc(var(--spacing) * 1.5);
  }

  .\[\&_\[cmdk-group-heading\]\]\:text-xs [cmdk-group-heading] {
    font-size: var(--text-xs);
    line-height: var(--tw-leading, var(--text-xs--line-height));
  }

  .\[\&_\[cmdk-group-heading\]\]\:font-medium [cmdk-group-heading] {
    --tw-font-weight: var(--font-weight-medium);
    font-weight: var(--font-weight-medium);
  }

  .\[\&_\[cmdk-group-heading\]\]\:text-muted-foreground [cmdk-group-heading] {
    color: var(--muted-foreground);
  }

  .\[\&_\[cmdk-group\]\]\:px-2 [cmdk-group] {
    padding-inline: calc(var(--spacing) * 2);
  }

  .\[\&_\[cmdk-group\]\:not\(\[hidden\]\)_\~\[cmdk-group\]\]\:pt-0 [cmdk-group]:not([hidden]) ~ [cmdk-group] {
    padding-top: calc(var(--spacing) * 0);
  }

  .\[\&_\[cmdk-input-wrapper\]_svg\]\:h-5 [cmdk-input-wrapper] svg {
    height: calc(var(--spacing) * 5);
  }

  .\[\&_\[cmdk-input-wrapper\]_svg\]\:w-5 [cmdk-input-wrapper] svg {
    width: calc(var(--spacing) * 5);
  }

  .\[\&_\[cmdk-input\]\]\:h-12 [cmdk-input] {
    height: calc(var(--spacing) * 12);
  }

  .\[\&_\[cmdk-item\]\]\:px-2 [cmdk-item] {
    padding-inline: calc(var(--spacing) * 2);
  }

  .\[\&_\[cmdk-item\]\]\:py-3 [cmdk-item] {
    padding-block: calc(var(--spacing) * 3);
  }

  .\[\&_\[cmdk-item\]_svg\]\:h-5 [cmdk-item] svg {
    height: calc(var(--spacing) * 5);
  }

  .\[\&_\[cmdk-item\]_svg\]\:w-5 [cmdk-item] svg {
    width: calc(var(--spacing) * 5);
  }

  .\[\&_p\]\:leading-relaxed p {
    --tw-leading: var(--leading-relaxed);
    line-height: var(--leading-relaxed);
  }

  .\[\&_svg\]\:pointer-events-none svg {
    pointer-events: none;
  }

  .\[\&_svg\]\:shrink-0 svg {
    flex-shrink: 0;
  }

  .\[\&_svg\:not\(\[class\*\=\'size-\'\]\)\]\:size-4 svg:not([class*="size-"]) {
    width: calc(var(--spacing) * 4);
    height: calc(var(--spacing) * 4);
  }

  .\[\&_svg\:not\(\[class\*\=\'text-\'\]\)\]\:text-muted-foreground svg:not([class*="text-"]) {
    color: var(--muted-foreground);
  }

  .\[\&_tr\]\:border-b tr {
    border-bottom-style: var(--tw-border-style);
    border-bottom-width: 1px;
  }

  .\[\&_tr\:last-child\]\:border-0 tr:last-child {
    border-style: var(--tw-border-style);
    border-width: 0;
  }

  .\[\&\+\.border-medium\.border-danger\]\:ms-\[calc\(theme\(borderWidth\.medium\)\*-1\)\] + .border-medium.border-danger {
    margin-inline-start: calc(var(--heroui-border-width-medium) * -1);
  }

  .\[\&\+\.border-medium\.border-default\]\:ms-\[calc\(theme\(borderWidth\.medium\)\*-1\)\] + .border-medium.border-default {
    margin-inline-start: calc(var(--heroui-border-width-medium) * -1);
  }

  .\[\&\+\.border-medium\.border-primary\]\:ms-\[calc\(theme\(borderWidth\.medium\)\*-1\)\] + .border-medium.border-primary {
    margin-inline-start: calc(var(--heroui-border-width-medium) * -1);
  }

  .\[\&\+\.border-medium\.border-secondary\]\:ms-\[calc\(theme\(borderWidth\.medium\)\*-1\)\] + .border-medium.border-secondary {
    margin-inline-start: calc(var(--heroui-border-width-medium) * -1);
  }

  .\[\&\+\.border-medium\.border-success\]\:ms-\[calc\(theme\(borderWidth\.medium\)\*-1\)\] + .border-medium.border-success {
    margin-inline-start: calc(var(--heroui-border-width-medium) * -1);
  }

  .\[\&\+\.border-medium\.border-warning\]\:ms-\[calc\(theme\(borderWidth\.medium\)\*-1\)\] + .border-medium.border-warning {
    margin-inline-start: calc(var(--heroui-border-width-medium) * -1);
  }

  .\[\&\:has\(\>\.day-range-end\)\]\:rounded-r-md:has( > .day-range-end) {
    border-top-right-radius: calc(var(--radius)  - 2px);
    border-bottom-right-radius: calc(var(--radius)  - 2px);
  }

  .\[\&\:has\(\>\.day-range-start\)\]\:rounded-l-md:has( > .day-range-start) {
    border-top-left-radius: calc(var(--radius)  - 2px);
    border-bottom-left-radius: calc(var(--radius)  - 2px);
  }

  .\[\&\:has\(\[aria-selected\]\)\]\:rounded-md:has([aria-selected]) {
    border-radius: calc(var(--radius)  - 2px);
  }

  .\[\&\:has\(\[aria-selected\]\)\]\:bg-accent:has([aria-selected]) {
    background-color: var(--accent);
  }

  .first\:\[\&\:has\(\[aria-selected\]\)\]\:rounded-l-md:first-child:has([aria-selected]) {
    border-top-left-radius: calc(var(--radius)  - 2px);
    border-bottom-left-radius: calc(var(--radius)  - 2px);
  }

  .last\:\[\&\:has\(\[aria-selected\]\)\]\:rounded-r-md:last-child:has([aria-selected]) {
    border-top-right-radius: calc(var(--radius)  - 2px);
    border-bottom-right-radius: calc(var(--radius)  - 2px);
  }

  .\[\&\:has\(\[aria-selected\]\.day-range-end\)\]\:rounded-r-md:has([aria-selected].day-range-end) {
    border-top-right-radius: calc(var(--radius)  - 2px);
    border-bottom-right-radius: calc(var(--radius)  - 2px);
  }

  .\[\&\:has\(\[data-state\=checked\]\)\]\:border-primary:has([data-state="checked"]) {
    border-color: var(--primary);
  }

  .\[\&\:has\(\[data-state\=checked\]\)\]\:bg-accent:has([data-state="checked"]) {
    background-color: var(--accent);
  }

  .\[\&\:has\(\[role\=checkbox\]\)\]\:pr-0:has([role="checkbox"]) {
    padding-right: calc(var(--spacing) * 0);
  }

  .\[\.border-b\]\:pb-6.border-b {
    padding-bottom: calc(var(--spacing) * 6);
  }

  .\[\.border-t\]\:pt-6.border-t {
    padding-top: calc(var(--spacing) * 6);
  }

  :is(.\*\:\[span\]\:last\:flex > *):is(span):last-child {
    display: flex;
  }

  :is(.\*\:\[span\]\:last\:items-center > *):is(span):last-child {
    align-items: center;
  }

  :is(.\*\:\[span\]\:last\:gap-2 > *):is(span):last-child {
    gap: calc(var(--spacing) * 2);
  }

  :is(.data-\[variant\=destructive\]\:\*\:\[svg\]\:\!text-destructive[data-variant="destructive"] > *):is(svg) {
    color: var(--destructive) !important;
  }

  .\[\&\:not\(\:first-child\)\]\:-ml-1:not(:first-child) {
    margin-left: calc(var(--spacing) * -1);
  }

  .\[\&\:not\(\:first-child\)\:not\(\:last-child\)\]\:rounded-none:not(:first-child):not(:last-child) {
    border-radius: 0;
  }

  .\[\&\:not\(\:first-of-type\)\]\:ms-\[calc\(theme\(borderWidth\.2\)\*-1\)\]:not(:first-of-type) {
    margin-inline-start: -2px;
  }

  .\[\&\:not\(\:first-of-type\)\:not\(\:last-of-type\)\]\:rounded-none:not(:first-of-type):not(:last-of-type) {
    border-radius: 0;
  }

  .\[\&\>\*\]\:relative > * {
    position: relative;
  }

  .\[\&\>\*\]\:z-1 > * {
    z-index: 1;
  }

  .\[\&\>\[role\=checkbox\]\]\:translate-y-\[2px\] > [role="checkbox"] {
    --tw-translate-y: 2px;
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }

  .\[\&\>button\]\:hidden > button {
    display: none;
  }

  .\[\&\>span\:last-child\]\:truncate > span:last-child {
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
  }

  .\[\&\>svg\]\:pointer-events-none > svg {
    pointer-events: none;
  }

  .\[\&\>svg\]\:size-3 > svg {
    width: calc(var(--spacing) * 3);
    height: calc(var(--spacing) * 3);
  }

  .\[\&\>svg\]\:size-3\.5 > svg {
    width: calc(var(--spacing) * 3.5);
    height: calc(var(--spacing) * 3.5);
  }

  .\[\&\>svg\]\:size-4 > svg {
    width: calc(var(--spacing) * 4);
    height: calc(var(--spacing) * 4);
  }

  .\[\&\>svg\]\:h-2\.5 > svg {
    height: calc(var(--spacing) * 2.5);
  }

  .\[\&\>svg\]\:h-3 > svg {
    height: calc(var(--spacing) * 3);
  }

  .\[\&\>svg\]\:w-2\.5 > svg {
    width: calc(var(--spacing) * 2.5);
  }

  .\[\&\>svg\]\:w-3 > svg {
    width: calc(var(--spacing) * 3);
  }

  .\[\&\>svg\]\:max-w-\[theme\(spacing\.8\)\] > svg {
    max-width: 2rem;
  }

  .\[\&\>svg\]\:shrink-0 > svg {
    flex-shrink: 0;
  }

  .\[\&\>svg\]\:translate-y-0\.5 > svg {
    --tw-translate-y: calc(var(--spacing) * .5);
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }

  .\[\&\>svg\]\:text-current > svg {
    color: currentColor;
  }

  .\[\&\>svg\]\:text-muted-foreground > svg {
    color: var(--muted-foreground);
  }

  .\[\&\>svg\]\:text-sidebar-accent-foreground > svg {
    color: var(--sidebar-accent-foreground);
  }

  .\[\&\>tr\]\:first\:rounded-lg > tr:first-child {
    border-radius: var(--radius);
  }

  .\[\&\>tr\]\:first\:shadow-small > tr:first-child {
    --tw-shadow: var(--heroui-box-shadow-small);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .\[\&\>tr\]\:last\:border-b-0 > tr:last-child {
    border-bottom-style: var(--tw-border-style);
    border-bottom-width: 0;
  }

  .\[\&\[data-hover\=true\]\:not\(\[data-active\=true\]\)\]\:bg-default-100[data-hover="true"]:not([data-active="true"]) {
    background-color: hsl(var(--heroui-default-100) / 1);
  }

  .\[\&\[data-hover\=true\]\:not\(\[data-active\=true\]\)\]\:bg-default-200[data-hover="true"]:not([data-active="true"]) {
    background-color: hsl(var(--heroui-default-200) / 1);
  }

  .\[\&\[data-panel-group-direction\=vertical\]\>div\]\:rotate-90[data-panel-group-direction="vertical"] > div {
    rotate: 90deg;
  }

  .\[\&\[data-state\=\"checked\"\]\]\:border-primary[data-state="checked"] {
    border-color: var(--primary);
  }

  .\[\&\[data-state\=\"checked\"\]\]\:bg-primary-foreground[data-state="checked"] {
    background-color: var(--primary-foreground);
  }

  .\[\&\[data-state\=open\]\>svg\]\:rotate-180[data-state="open"] > svg {
    rotate: 180deg;
  }

  [data-side="left"][data-collapsible="offcanvas"] .\[\[data-side\=left\]\[data-collapsible\=offcanvas\]_\&\]\:-right-2 {
    right: calc(var(--spacing) * -2);
  }

  [data-side="left"][data-state="collapsed"] .\[\[data-side\=left\]\[data-state\=collapsed\]_\&\]\:cursor-e-resize {
    cursor: e-resize;
  }

  [data-side="right"][data-collapsible="offcanvas"] .\[\[data-side\=right\]\[data-collapsible\=offcanvas\]_\&\]\:-left-2 {
    left: calc(var(--spacing) * -2);
  }

  [data-side="right"][data-state="collapsed"] .\[\[data-side\=right\]\[data-state\=collapsed\]_\&\]\:cursor-w-resize {
    cursor: w-resize;
  }

  @media (hover: hover) {
    a.\[a\&\]\:hover\:bg-accent:hover {
      background-color: var(--accent);
    }
  }

  @media (hover: hover) {
    a.\[a\&\]\:hover\:bg-destructive\/90:hover {
      background-color: var(--destructive);
    }

    @supports (color: color-mix(in lab, red, red)) {
      a.\[a\&\]\:hover\:bg-destructive\/90:hover {
        background-color: color-mix(in oklab, var(--destructive) 90%, transparent);
      }
    }
  }

  @media (hover: hover) {
    a.\[a\&\]\:hover\:bg-primary\/90:hover {
      background-color: var(--primary);
    }

    @supports (color: color-mix(in lab, red, red)) {
      a.\[a\&\]\:hover\:bg-primary\/90:hover {
        background-color: color-mix(in oklab, var(--primary) 90%, transparent);
      }
    }
  }

  @media (hover: hover) {
    a.\[a\&\]\:hover\:bg-secondary\/90:hover {
      background-color: var(--secondary);
    }

    @supports (color: color-mix(in lab, red, red)) {
      a.\[a\&\]\:hover\:bg-secondary\/90:hover {
        background-color: color-mix(in oklab, var(--secondary) 90%, transparent);
      }
    }
  }

  @media (hover: hover) {
    a.\[a\&\]\:hover\:text-accent-foreground:hover {
      color: var(--accent-foreground);
    }
  }

  .animate-scrolling-banner {
    animation: scrolling-banner var(--duration) linear infinite;
  }

  .animate-scrolling-banner-vertical {
    animation: scrolling-banner-vertical var(--duration) linear infinite;
  }
}

.ProseMirror {
  outline: none;
  min-height: 120px;
  padding: 1rem;
}

.ProseMirror p {
  margin: .5rem 0;
}

.ProseMirror p:first-child {
  margin-top: 0;
}

.ProseMirror p:last-child {
  margin-bottom: 0;
}

.ProseMirror h1, .ProseMirror h2, .ProseMirror h3, .ProseMirror h4, .ProseMirror h5, .ProseMirror h6 {
  margin: 1rem 0 .5rem;
  font-weight: 600;
}

.ProseMirror h1 {
  font-size: 1.5rem;
}

.ProseMirror h2 {
  font-size: 1.25rem;
}

.ProseMirror h3 {
  font-size: 1.125rem;
}

.ProseMirror ul, .ProseMirror ol {
  margin: .5rem 0;
  padding-left: 1.5rem;
}

.ProseMirror li {
  margin: .25rem 0;
}

.ProseMirror a {
  color: #3b82f6;
  text-decoration: underline;
}

.ProseMirror a:hover {
  color: #1d4ed8;
}

.ProseMirror strong {
  font-weight: 600;
}

.ProseMirror em {
  font-style: italic;
}

.ProseMirror p.is-editor-empty:first-child:before {
  content: attr(data-placeholder);
  float: left;
  color: #9ca3af;
  pointer-events: none;
  height: 0;
}

:root {
  --radius: .625rem;
  --background: oklch(1 0 0);
  --foreground: oklch(.145 0 0);
  --card: oklch(1 0 0);
  --card-foreground: oklch(.145 0 0);
  --popover: oklch(1 0 0);
  --popover-foreground: oklch(.145 0 0);
  --primary: oklch(.55 .22 263);
  --primary-foreground: oklch(.97 .01 255);
  --secondary: oklch(.97 0 0);
  --secondary-foreground: oklch(.205 0 0);
  --muted: oklch(.97 0 0);
  --muted-foreground: oklch(.556 0 0);
  --accent: oklch(.97 0 0);
  --accent-foreground: oklch(.205 0 0);
  --destructive: oklch(.577 .245 27.325);
  --border: oklch(.922 0 0);
  --input: oklch(.922 0 0);
  --ring: oklch(.708 0 0);
  --chart-1: oklch(.646 .222 41.116);
  --chart-2: oklch(.6 .118 184.704);
  --chart-3: oklch(.398 .07 227.392);
  --chart-4: oklch(.828 .189 84.429);
  --chart-5: oklch(.769 .188 70.08);
  --sidebar: oklch(.985 0 0);
  --sidebar-foreground: oklch(.145 0 0);
  --sidebar-primary: oklch(.205 0 0);
  --sidebar-primary-foreground: oklch(.985 0 0);
  --sidebar-accent: oklch(.97 0 0);
  --sidebar-accent-foreground: oklch(.205 0 0);
  --sidebar-border: oklch(.922 0 0);
  --sidebar-ring: oklch(.708 0 0);
}

.dark {
  --background: oklch(.145 0 0);
  --foreground: oklch(.985 0 0);
  --card: oklch(.205 0 0);
  --card-foreground: oklch(.985 0 0);
  --popover: oklch(.205 0 0);
  --popover-foreground: oklch(.985 0 0);
  --primary: oklch(.62 .19 260);
  --primary-foreground: oklch(.97 .01 255);
  --secondary: oklch(.269 0 0);
  --secondary-foreground: oklch(.985 0 0);
  --muted: oklch(.269 0 0);
  --muted-foreground: oklch(.708 0 0);
  --accent: oklch(.269 0 0);
  --accent-foreground: oklch(.985 0 0);
  --destructive: oklch(.704 .191 22.216);
  --border: oklch(1 0 0 / 10%);
  --input: oklch(1 0 0 / 15%);
  --ring: oklch(.556 0 0);
  --chart-1: oklch(.488 .243 264.376);
  --chart-2: oklch(.696 .17 162.48);
  --chart-3: oklch(.769 .188 70.08);
  --chart-4: oklch(.627 .265 303.9);
  --chart-5: oklch(.645 .246 16.439);
  --sidebar: oklch(.205 0 0);
  --sidebar-foreground: oklch(.985 0 0);
  --sidebar-primary: oklch(.488 .243 264.376);
  --sidebar-primary-foreground: oklch(.985 0 0);
  --sidebar-accent: oklch(.269 0 0);
  --sidebar-accent-foreground: oklch(.985 0 0);
  --sidebar-border: oklch(1 0 0 / 10%);
  --sidebar-ring: oklch(.556 0 0);
}

@property --tw-translate-x {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}

@property --tw-translate-y {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}

@property --tw-translate-z {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}

@property --tw-scale-x {
  syntax: "*";
  inherits: false;
  initial-value: 1;
}

@property --tw-scale-y {
  syntax: "*";
  inherits: false;
  initial-value: 1;
}

@property --tw-scale-z {
  syntax: "*";
  inherits: false;
  initial-value: 1;
}

@property --tw-rotate-x {
  syntax: "*";
  inherits: false;
  initial-value: rotateX(0);
}

@property --tw-rotate-y {
  syntax: "*";
  inherits: false;
  initial-value: rotateY(0);
}

@property --tw-rotate-z {
  syntax: "*";
  inherits: false;
  initial-value: rotateZ(0);
}

@property --tw-skew-x {
  syntax: "*";
  inherits: false;
  initial-value: skewX(0);
}

@property --tw-skew-y {
  syntax: "*";
  inherits: false;
  initial-value: skewY(0);
}

@property --tw-scroll-snap-strictness {
  syntax: "*";
  inherits: false;
  initial-value: proximity;
}

@property --tw-space-y-reverse {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}

@property --tw-space-x-reverse {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}

@property --tw-divide-y-reverse {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}

@property --tw-border-style {
  syntax: "*";
  inherits: false;
  initial-value: solid;
}

@property --tw-gradient-position {
  syntax: "*";
  inherits: false
}

@property --tw-gradient-from {
  syntax: "<color>";
  inherits: false;
  initial-value: #0000;
}

@property --tw-gradient-via {
  syntax: "<color>";
  inherits: false;
  initial-value: #0000;
}

@property --tw-gradient-to {
  syntax: "<color>";
  inherits: false;
  initial-value: #0000;
}

@property --tw-gradient-stops {
  syntax: "*";
  inherits: false
}

@property --tw-gradient-via-stops {
  syntax: "*";
  inherits: false
}

@property --tw-gradient-from-position {
  syntax: "<length-percentage>";
  inherits: false;
  initial-value: 0%;
}

@property --tw-gradient-via-position {
  syntax: "<length-percentage>";
  inherits: false;
  initial-value: 50%;
}

@property --tw-gradient-to-position {
  syntax: "<length-percentage>";
  inherits: false;
  initial-value: 100%;
}

@property --tw-leading {
  syntax: "*";
  inherits: false
}

@property --tw-font-weight {
  syntax: "*";
  inherits: false
}

@property --tw-tracking {
  syntax: "*";
  inherits: false
}

@property --tw-ordinal {
  syntax: "*";
  inherits: false
}

@property --tw-slashed-zero {
  syntax: "*";
  inherits: false
}

@property --tw-numeric-figure {
  syntax: "*";
  inherits: false
}

@property --tw-numeric-spacing {
  syntax: "*";
  inherits: false
}

@property --tw-numeric-fraction {
  syntax: "*";
  inherits: false
}

@property --tw-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}

@property --tw-shadow-color {
  syntax: "*";
  inherits: false
}

@property --tw-shadow-alpha {
  syntax: "<percentage>";
  inherits: false;
  initial-value: 100%;
}

@property --tw-inset-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}

@property --tw-inset-shadow-color {
  syntax: "*";
  inherits: false
}

@property --tw-inset-shadow-alpha {
  syntax: "<percentage>";
  inherits: false;
  initial-value: 100%;
}

@property --tw-ring-color {
  syntax: "*";
  inherits: false
}

@property --tw-ring-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}

@property --tw-inset-ring-color {
  syntax: "*";
  inherits: false
}

@property --tw-inset-ring-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}

@property --tw-ring-inset {
  syntax: "*";
  inherits: false
}

@property --tw-ring-offset-width {
  syntax: "<length>";
  inherits: false;
  initial-value: 0;
}

@property --tw-ring-offset-color {
  syntax: "*";
  inherits: false;
  initial-value: #fff;
}

@property --tw-ring-offset-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}

@property --tw-outline-style {
  syntax: "*";
  inherits: false;
  initial-value: solid;
}

@property --tw-blur {
  syntax: "*";
  inherits: false
}

@property --tw-brightness {
  syntax: "*";
  inherits: false
}

@property --tw-contrast {
  syntax: "*";
  inherits: false
}

@property --tw-grayscale {
  syntax: "*";
  inherits: false
}

@property --tw-hue-rotate {
  syntax: "*";
  inherits: false
}

@property --tw-invert {
  syntax: "*";
  inherits: false
}

@property --tw-opacity {
  syntax: "*";
  inherits: false
}

@property --tw-saturate {
  syntax: "*";
  inherits: false
}

@property --tw-sepia {
  syntax: "*";
  inherits: false
}

@property --tw-drop-shadow {
  syntax: "*";
  inherits: false
}

@property --tw-drop-shadow-color {
  syntax: "*";
  inherits: false
}

@property --tw-drop-shadow-alpha {
  syntax: "<percentage>";
  inherits: false;
  initial-value: 100%;
}

@property --tw-drop-shadow-size {
  syntax: "*";
  inherits: false
}

@property --tw-backdrop-blur {
  syntax: "*";
  inherits: false
}

@property --tw-backdrop-brightness {
  syntax: "*";
  inherits: false
}

@property --tw-backdrop-contrast {
  syntax: "*";
  inherits: false
}

@property --tw-backdrop-grayscale {
  syntax: "*";
  inherits: false
}

@property --tw-backdrop-hue-rotate {
  syntax: "*";
  inherits: false
}

@property --tw-backdrop-invert {
  syntax: "*";
  inherits: false
}

@property --tw-backdrop-opacity {
  syntax: "*";
  inherits: false
}

@property --tw-backdrop-saturate {
  syntax: "*";
  inherits: false
}

@property --tw-backdrop-sepia {
  syntax: "*";
  inherits: false
}

@property --tw-duration {
  syntax: "*";
  inherits: false
}

@property --tw-ease {
  syntax: "*";
  inherits: false
}

@property --tw-content {
  syntax: "*";
  inherits: false;
  initial-value: "";
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

@keyframes pulse {
  50% {
    opacity: .5;
  }
}

@keyframes enter {
  from {
    opacity: var(--tw-enter-opacity, 1);
    transform: translate3d(var(--tw-enter-translate-x, 0), var(--tw-enter-translate-y, 0), 0) scale3d(var(--tw-enter-scale, 1), var(--tw-enter-scale, 1), var(--tw-enter-scale, 1)) rotate(var(--tw-enter-rotate, 0));
  }
}

@keyframes exit {
  to {
    opacity: var(--tw-exit-opacity, 1);
    transform: translate3d(var(--tw-exit-translate-x, 0), var(--tw-exit-translate-y, 0), 0) scale3d(var(--tw-exit-scale, 1), var(--tw-exit-scale, 1), var(--tw-exit-scale, 1)) rotate(var(--tw-exit-rotate, 0));
  }
}

@keyframes accordion-down {
  from {
    height: 0;
  }

  to {
    height: var(--radix-accordion-content-height);
  }
}

@keyframes accordion-up {
  from {
    height: var(--radix-accordion-content-height);
  }

  to {
    height: 0;
  }
}

@keyframes caret-blink {
  0%, 70%, 100% {
    opacity: 1;
  }

  20%, 50% {
    opacity: 0;
  }
}

@keyframes scrolling-banner {
  from {
    transform: translateX(0);
  }

  to {
    transform: translateX(calc(-50% - var(--gap) / 2));
  }
}

@keyframes scrolling-banner-vertical {
  from {
    transform: translateY(0);
  }

  to {
    transform: translateY(calc(-50% - var(--gap) / 2));
  }
}

@keyframes shimmer {
  100% {
    transform: translateX(200%);
  }
}

@keyframes spinner-spin {
  0% {
    transform: rotate(0);
  }

  100% {
    transform: rotate(360deg);
  }
}

@keyframes drip-expand {
  0% {
    opacity: .2;
    transform: scale(0);
  }

  100% {
    opacity: 0;
    transform: scale(2);
  }
}

@keyframes appearance-in {
  0% {
    opacity: 0;
    transform: translateZ(0)scale(.95);
  }

  60% {
    opacity: .75;
    backface-visibility: hidden;
    webkit-font-smoothing: antialiased;
    transform: translateZ(0)scale(1.05);
  }

  100% {
    opacity: 1;
    transform: translateZ(0)scale(1);
  }
}

@keyframes indeterminate-bar {
  0% {
    transform: translateX(-50%)scaleX(.2);
  }

  100% {
    transform: translateX(100%)scaleX(1);
  }
}

@keyframes sway {
  0% {
    transform: translate(0);
  }

  50% {
    transform: translate(0, -150%);
  }

  100% {
    transform: translate(0);
  }
}

@keyframes blink {
  0% {
    opacity: .2;
  }

  20% {
    opacity: 1;
  }

  100% {
    opacity: .2;
  }
}

@keyframes fade-out {
  0% {
    opacity: 1;
  }

  100% {
    opacity: .15;
  }
}

/*# sourceMappingURL=app_globals_css_f9ee138c._.single.css.map*/
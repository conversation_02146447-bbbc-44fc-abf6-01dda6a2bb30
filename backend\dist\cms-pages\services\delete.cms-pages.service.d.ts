import { Repository, DataSource } from 'typeorm';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { BaseCmsPagesService } from './base.cms-pages.service';
import { CmsPages } from '../entity/cms-pages.entity';
import { CmsPageDto } from '../dto/cms-page.dto';
export declare class DeleteCmsPagesService extends BaseCmsPagesService {
    protected readonly pageRepository: Repository<CmsPages>;
    protected readonly dataSource: DataSource;
    protected readonly eventEmitter: EventEmitter2;
    constructor(pageRepository: Repository<CmsPages>, dataSource: DataSource, eventEmitter: EventEmitter2);
    softDelete(id: string, userId: string): Promise<CmsPageDto | null>;
    restore(id: string, userId: string): Promise<CmsPageDto | null>;
    remove(id: string): Promise<{
        affected: number;
    }>;
    bulkSoftDelete(ids: string[], userId: string): Promise<CmsPageDto[]>;
    bulkRestore(ids: string[], userId: string): Promise<CmsPageDto[]>;
    bulkRemove(ids: string[]): Promise<{
        affected: number;
    }>;
}

import { User } from './user.entity';
import { Role } from '../../roles/entities/role.entity';
export declare class UserRole {
    userId: string;
    roleId: string;
    createdAt: Date;
    updatedAt: Date;
    deletedAt: Date | null;
    isDeleted: boolean;
    createdBy: string | null;
    updatedBy: string | null;
    deletedBy: string | null;
    user: User;
    role: Role;
    creator: User;
    updater: User;
    deleter: User;
}

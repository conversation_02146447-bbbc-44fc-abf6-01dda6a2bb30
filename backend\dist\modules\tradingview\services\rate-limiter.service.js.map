{"version": 3, "file": "rate-limiter.service.js", "sourceRoot": "", "sources": ["../../../../src/modules/tradingview/services/rate-limiter.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;AAAA,2CAAoD;AACpD,2CAA+C;AAC/C,6CAAmD;AACnD,qCAAqC;AACrC,gGAAqF;AACrF,6EAAyE;AAGlE,IAAM,kBAAkB,0BAAxB,MAAM,kBAAkB;IAOV;IAEA;IARF,MAAM,GAAG,IAAI,eAAM,CAAC,oBAAkB,CAAC,IAAI,CAAC,CAAC;IAC7C,cAAc,GAAsD,IAAI,GAAG,EAAE,CAAC;IACvF,SAAS,CAAS;IAClB,eAAe,CAAS;IAEhC,YACmB,aAA4B,EAE5B,sBAAgD;QAFhD,kBAAa,GAAb,aAAa,CAAe;QAE5B,2BAAsB,GAAtB,sBAAsB,CAA0B;QAEjE,IAAI,CAAC,oBAAoB,EAAE,CAAC;IAC9B,CAAC;IAKO,KAAK,CAAC,oBAAoB;QAChC,IAAI,CAAC;YAEH,MAAM,eAAe,GAAG,MAAM,IAAI,CAAC,sBAAsB,CAAC,OAAO,CAAC;gBAChE,KAAK,EAAE,EAAE,SAAS,EAAE,8CAAqB,CAAC,UAAU,EAAE,SAAS,EAAE,KAAK,EAAE;aACzE,CAAC,CAAC;YAEH,MAAM,qBAAqB,GAAG,MAAM,IAAI,CAAC,sBAAsB,CAAC,OAAO,CAAC;gBACtE,KAAK,EAAE,EAAE,SAAS,EAAE,8CAAqB,CAAC,iBAAiB,EAAE,SAAS,EAAE,KAAK,EAAE;aAChF,CAAC,CAAC;YAEH,IAAI,eAAe,IAAI,qBAAqB,EAAE,CAAC;gBAC7C,IAAI,CAAC,SAAS,GAAG,QAAQ,CAAC,eAAe,CAAC,WAAW,EAAE,EAAE,CAAC,CAAC;gBAC3D,IAAI,CAAC,eAAe,GAAG,QAAQ,CAAC,qBAAqB,CAAC,WAAW,EAAE,EAAE,CAAC,CAAC;YACzE,CAAC;iBAAM,CAAC;gBAEN,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,wBAAwB,EAAE,GAAG,CAAC,CAAC;gBACvE,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,+BAA+B,EAAE,EAAE,CAAC,CAAC;gBAGnF,IAAI,CAAC,eAAe,EAAE,CAAC;oBACrB,MAAM,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC;wBACrC,SAAS,EAAE,8CAAqB,CAAC,UAAU;wBAC3C,WAAW,EAAE,IAAI,CAAC,SAAS,CAAC,QAAQ,EAAE;wBACtC,WAAW,EAAE,0EAA0E;wBACvF,WAAW,EAAE,aAAa;wBAC1B,UAAU,EAAE,QAAQ;qBACrB,CAAC,CAAC;gBACL,CAAC;gBAED,IAAI,CAAC,qBAAqB,EAAE,CAAC;oBAC3B,MAAM,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC;wBACrC,SAAS,EAAE,8CAAqB,CAAC,iBAAiB;wBAClD,WAAW,EAAE,IAAI,CAAC,eAAe,CAAC,QAAQ,EAAE;wBAC5C,WAAW,EAAE,sEAAsE;wBACnF,WAAW,EAAE,aAAa;wBAC1B,UAAU,EAAE,QAAQ;qBACrB,CAAC,CAAC;gBACL,CAAC;YACH,CAAC;YAED,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,uCAAuC,IAAI,CAAC,SAAS,kBAAkB,IAAI,CAAC,eAAe,OAAO,CAAC,CAAC;QACtH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,uCAAuC,KAAK,CAAC,OAAO,EAAE,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC;YAEvF,IAAI,CAAC,SAAS,GAAG,GAAG,CAAC;YACrB,IAAI,CAAC,eAAe,GAAG,EAAE,CAAC;QAC5B,CAAC;IACH,CAAC;IAKD,KAAK,CAAC,aAAa,CAAC,QAAgB;QAClC,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QACvB,IAAI,UAAU,GAAG,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;QAGnD,IAAI,CAAC,UAAU,IAAI,GAAG,GAAG,UAAU,CAAC,SAAS,EAAE,CAAC;YAC9C,UAAU,GAAG;gBACX,KAAK,EAAE,CAAC;gBACR,SAAS,EAAE,GAAG,GAAG,IAAI,CAAC,eAAe,GAAG,IAAI;aAC7C,CAAC;YACF,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,QAAQ,EAAE,UAAU,CAAC,CAAC;YAC9C,OAAO,KAAK,CAAC;QACf,CAAC;QAGD,UAAU,CAAC,KAAK,EAAE,CAAC;QAGnB,IAAI,UAAU,CAAC,KAAK,GAAG,IAAI,CAAC,SAAS,EAAE,CAAC;YACtC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,yCAAyC,QAAQ,KAAK,UAAU,CAAC,KAAK,UAAU,CAAC,CAAC;YACnG,OAAO,IAAI,CAAC;QACd,CAAC;QAED,OAAO,KAAK,CAAC;IACf,CAAC;IAKD,oBAAoB,CAAC,QAAgB;QACnC,MAAM,UAAU,GAAG,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;QACrD,IAAI,CAAC,UAAU,EAAE,CAAC;YAChB,OAAO,IAAI,CAAC,SAAS,CAAC;QACxB,CAAC;QAED,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,SAAS,GAAG,UAAU,CAAC,KAAK,CAAC,CAAC;QACjE,OAAO,SAAS,CAAC;IACnB,CAAC;IAKD,YAAY,CAAC,QAAgB;QAC3B,MAAM,UAAU,GAAG,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;QACrD,IAAI,CAAC,UAAU,EAAE,CAAC;YAChB,OAAO,IAAI,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC;QAClD,CAAC;QAED,OAAO,UAAU,CAAC,SAAS,CAAC;IAC9B,CAAC;CACF,CAAA;AAxHY,gDAAkB;6BAAlB,kBAAkB;IAD9B,IAAA,mBAAU,GAAE;IASR,WAAA,IAAA,0BAAgB,EAAC,mCAAY,CAAC,CAAA;qCADC,sBAAa;QAEJ,oBAAU;GAT1C,kBAAkB,CAwH9B"}
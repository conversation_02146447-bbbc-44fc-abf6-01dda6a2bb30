"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreatePaymentDto = void 0;
const openapi = require("@nestjs/swagger");
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
const payment_gateway_type_enum_1 = require("../enums/payment-gateway-type.enum");
class CreatePaymentDto {
    userId;
    walletId;
    amount;
    gatewayType;
    ipAddress;
    description;
    bankCode;
    locale;
    orderType;
    static _OPENAPI_METADATA_FACTORY() {
        return { userId: { required: true, type: () => String, format: "uuid" }, walletId: { required: true, type: () => String, format: "uuid" }, amount: { required: true, type: () => Number, minimum: 10000 }, gatewayType: { required: true, enum: require("../enums/payment-gateway-type.enum").PaymentGatewayType }, ipAddress: { required: false, type: () => String }, description: { required: false, type: () => String }, bankCode: { required: false, type: () => String }, locale: { required: false, type: () => String }, orderType: { required: false, type: () => String } };
    }
}
exports.CreatePaymentDto = CreatePaymentDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'ID người dùng thực hiện thanh toán',
        example: '550e8400-e29b-41d4-a716-************',
    }),
    (0, class_validator_1.IsUUID)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], CreatePaymentDto.prototype, "userId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'ID ví nhận tiền',
        example: '550e8400-e29b-41d4-a716-************',
    }),
    (0, class_validator_1.IsUUID)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], CreatePaymentDto.prototype, "walletId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Số tiền thanh toán (VND)',
        example: 100000,
        minimum: 10000,
    }),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.IsNotEmpty)(),
    (0, class_validator_1.Min)(10000),
    __metadata("design:type", Number)
], CreatePaymentDto.prototype, "amount", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Loại cổng thanh toán',
        enum: payment_gateway_type_enum_1.PaymentGatewayType,
        example: payment_gateway_type_enum_1.PaymentGatewayType.VNPAY,
    }),
    (0, class_validator_1.IsNotEmpty)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreatePaymentDto.prototype, "gatewayType", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Địa chỉ IP của người dùng',
        example: '127.0.0.1',
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], CreatePaymentDto.prototype, "ipAddress", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Ghi chú thanh toán',
        example: 'Nạp tiền vào ví',
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], CreatePaymentDto.prototype, "description", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Mã ngân hàng (VNPAY)',
        example: 'VNPAYQR',
        required: false,
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], CreatePaymentDto.prototype, "bankCode", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Ngôn ngữ giao diện (vn/en)',
        example: 'vn',
        required: false,
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], CreatePaymentDto.prototype, "locale", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Loại đơn hàng (VNPAY)',
        example: 'other',
        required: false,
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], CreatePaymentDto.prototype, "orderType", void 0);
//# sourceMappingURL=create-payment.dto.js.map
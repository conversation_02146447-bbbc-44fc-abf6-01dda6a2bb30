"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AddOtpRelatedInUser1747641294440 = void 0;
class AddOtpRelatedInUser1747641294440 {
    async up(queryRunner) {
        try {
            await queryRunner.query(`
                ALTER TABLE "users" 
                ADD COLUMN IF NOT EXISTS "phone_verified" BOOLEAN DEFAULT FALSE
            `);
            await queryRunner.query(`
                ALTER TABLE "users" 
                ADD COLUMN IF NOT EXISTS "phone_verification_token" VARCHAR(255)
            `);
            await queryRunner.query(`
                ALTER TABLE "users" 
                ADD COLUMN IF NOT EXISTS "phone_verification_token_expiry" TIMESTAMP
            `);
            await queryRunner.query(`
                CREATE INDEX IF NOT EXISTS "IDX_users_phone_verified" ON "users" ("phone_verified")
            `);
            await queryRunner.query(`
                UPDATE "users"
                SET "phone_verified" = FALSE
                WHERE "phone_verified" IS NULL
            `);
        }
        catch (error) {
            console.error('Lỗi khi thực hiện migration:', error);
            throw error;
        }
    }
    async down(queryRunner) {
        try {
            await queryRunner.query(`DROP INDEX IF EXISTS "IDX_users_phone_verified"`);
            await queryRunner.query(`ALTER TABLE "users" DROP COLUMN IF EXISTS "phone_verification_token_expiry"`);
            await queryRunner.query(`ALTER TABLE "users" DROP COLUMN IF EXISTS "phone_verification_token"`);
            await queryRunner.query(`ALTER TABLE "users" DROP COLUMN IF EXISTS "phone_verified"`);
        }
        catch (error) {
            console.error('Lỗi khi rollback migration:', error);
            throw error;
        }
    }
}
exports.AddOtpRelatedInUser1747641294440 = AddOtpRelatedInUser1747641294440;
//# sourceMappingURL=1747641294440-addOtpRelatedInUser.js.map
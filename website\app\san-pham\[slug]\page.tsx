"use client";

import React, { useEffect, useState } from "react";
import { useParams, notFound } from "next/navigation";
import {
  Button,
  Image,
} from "@heroui/react";
import { Icon } from "@iconify/react";

import {
  useEcomProducts,
  type EcomProductPublic,
  transformEcomProductToProductItem,
  useEcomHomepageSections,
} from "@/hooks/use-ecom-products";
import { SectionTitle } from "@/components/title";
import ProductListItem from "@/components/e-commerce/product-list/components/product-list-item";
import NewsBreadcrumb from "@/components/news/news-breadcrumb";

// Định nghĩa interface cho pagination response
interface PaginationMeta {
  page: number;
  limit: number;
  itemCount: number;
  pageCount: number;
  hasPreviousPage: boolean;
  hasNextPage: boolean;
}

interface PaginationResponse<T> {
  data: T[];
  meta: PaginationMeta;
}

// Product detail structure for enhanced display
type ProductViewDetail = {
  title: string;
  items: string[];
};

type ProductViewItemExtended = EcomProductPublic & {
  images?: string[];
  details?: ProductViewDetail[];
};

export default function ProductDetailPage() {
  const params = useParams();
  const slug = params.slug as string;
  const { fetchProductBySlug } = useEcomProducts();
  
  // Sử dụng useEcomHomepageSections để lấy sản phẩm gợi ý
  const {
    sections,
    loading: sectionsLoading,
    error: sectionsError,
  } = useEcomHomepageSections(8, true); // Lấy 8 sản phẩm mỗi danh mục

  const [product, setProduct] = useState<ProductViewItemExtended | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [activeAccordion, setActiveAccordion] = useState<Set<string>>(
    new Set(["Thông tin sản phẩm"]),
  );
  const [recommendedProducts, setRecommendedProducts] = useState<ProductViewItemExtended[]>([]);
  const [error, setError] = useState<string | null>(null);

  // Fetch sản phẩm chính
  useEffect(() => {
    const fetchProduct = async () => {
      if (!slug) return;

      setIsLoading(true);
      setError(null);

      try {
        const foundProduct = await fetchProductBySlug(slug);

        if (!foundProduct) {
          setError("Không tìm thấy sản phẩm");
          notFound();
          return;
        }

        const enhancedProduct: ProductViewItemExtended = {
          ...foundProduct,
          details: [
            {
              title: "Thông tin sản phẩm",
              items: [
                `Trọng lượng: ${foundProduct.weight ? foundProduct.weight + "g" : "Liên hệ"}`,
                "Xuất xứ: Việt Nam",
              ],
            },
            {
              title: "Vận chuyển & Đổi trả",
              items: [
                "Miễn phí vận chuyển toàn quốc",
                "Đổi trả miễn phí trong 30 ngày",
                "Đóng gói quà tặng tinh tế",
                "Giao hàng trong vòng 24 giờ!",
              ],
            },
            {
              title: "Ghi chú thiết kế",
              items: [
                "Thiết kế độc quyền",
                "Chế tác thủ công bởi nghệ nhân Việt Nam",
                "Kết hợp giữa giá trị truyền thống và thiết kế hiện đại",
              ],
            },
          ],
        };

        setProduct(enhancedProduct);
      } catch (error) {
        console.error("Error fetching product:", error);
        setError("Có lỗi xảy ra khi tải thông tin sản phẩm");
      } finally {
        setIsLoading(false);
      }
    };

    fetchProduct();
  }, [slug, fetchProductBySlug]);

  // Xử lý sản phẩm gợi ý từ sections
  useEffect(() => {
    if (sections && sections.length > 0 && product) {
      // Gộp tất cả sản phẩm từ các sections
      const allProducts = sections.reduce<EcomProductPublic[]>((acc, section) => {
        if (section.products && section.products.length > 0) {
          acc.push(...section.products);
        }
        return acc;
      }, []);

      // Lọc bỏ sản phẩm hiện tại và lấy 4 sản phẩm ngẫu nhiên
      const filtered = allProducts
        .filter(p => p.id !== product.id)
        .sort(() => Math.random() - 0.5) // Xáo trộn mảng
        .slice(0, 4);

      setRecommendedProducts(filtered);
    }
  }, [sections, product]);

  const toggleAccordion = (key: string) => {
    setActiveAccordion((prev) => {
      const newSet = new Set(prev);
      if (newSet.has(key)) {
        newSet.delete(key);
      } else {
        newSet.add(key);
      }
      return newSet;
    });
  };

  if (isLoading) {
    return (
      <div className="flex flex-col min-h-screen">
        <div className="container mx-auto px-4 py-10 flex-grow flex items-center justify-center">
          <p>Đang tải thông tin sản phẩm...</p>
        </div>
      </div>
    );
  }

  if (!product) {
    return (
      <div className="flex flex-col min-h-screen">
        <div className="container mx-auto px-4 py-10 flex-grow flex items-center justify-center">
          <p>Không tìm thấy sản phẩm</p>
        </div>
      </div>
    );
  }

  return (
    <div className="flex flex-col min-h-screen">
      <div className="container mx-auto px-4 py-8 pt-8">
        <div className="mb-8">
          <NewsBreadcrumb
            items={[
              { label: "Sản phẩm bạc", href: "/san-pham" },
              { label: product.productName }
            ]}
          />
        </div>

        <div className="relative flex flex-col gap-4 lg:grid lg:grid-cols-2 lg:items-start lg:gap-x-8">
          {/* Product Gallery */}
          <div className="relative h-full w-full flex-none">
            {/* Main Image */}
            <Image
              alt={product.productName}
              className="h-full w-full"
              radius="lg"
              src={product.imageUrl || "/images/placeholder-product.jpg"}
            />
          </div>

          {/* Product Info */}
          <div className="flex flex-col">
            <h1 className="text-2xl font-bold tracking-tight">
              {product.productName}
            </h1>
            <h2 className="sr-only">Thông tin sản phẩm</h2>

            <div className="mt-4">
              <p className="sr-only">Mô tả sản phẩm</p>
              {product.description && (
                <div
                  dangerouslySetInnerHTML={{ __html: product.description }}
                  className="text-medium text-default-500 prose prose-sm max-w-none"
                />
              )}
            </div>

            <div className="my-6 flex items-center gap-2 text-default-700" />

            {/* Product Details - Using custom accordion for type safety */}
            <div className="-mx-1 mt-4">
              {product.details?.map((detail) => (
                <div key={detail.title} className="border-b border-default-200">
                  <button
                    className="flex w-full justify-between py-3 px-1 text-medium font-medium text-default-600"
                    onClick={() => toggleAccordion(detail.title)}
                  >
                    {detail.title}
                    <Icon
                      className="text-default-400"
                      icon={
                        activeAccordion.has(detail.title)
                          ? "solar:minus-circle-line-duotone"
                          : "solar:plus-circle-line-duotone"
                      }
                      width={20}
                    />
                  </button>
                  {activeAccordion.has(detail.title) && (
                    <div className="pb-6 pt-0 text-base text-default-500">
                      <ul className="list-inside list-disc">
                        {detail.items.map((item, index) => (
                          <li key={index} className="text-default-500">
                            {item}
                          </li>
                        ))}
                      </ul>
                    </div>
                  )}
                </div>
              ))}
            </div>

            {/* Buy Now */}
            <div className="mt-6">
              <Button
                as="a"
                href={`${process.env.NEXT_PUBLIC_AUTH_URL}/login`}
                target="_blank"
                rel="noopener noreferrer"
                fullWidth
                className="text-medium font-medium"
                color="primary"
                size="lg"
                startContent={
                  <Icon icon="solar:cart-large-2-bold" width={24} />
                }
              >
                Mua ngay
              </Button>
            </div>
          </div>
        </div>

        {/* Recommended Products Section */}
        <div className="mt-16">
          <div className="container mx-auto">
            <SectionTitle
              align="center"
              className="mb-8"
              description="Các sản phẩm tương tự có thể bạn sẽ thích"
              size="lg"
              title="Sản phẩm gợi ý"
            />

            {/* Loading state */}
            {sectionsLoading && (
              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
                {Array.from({ length: 4 }).map((_, index) => (
                  <div key={index} className="animate-pulse">
                    <div className="bg-default-200 aspect-square rounded-lg mb-4" />
                    <div className="bg-default-200 h-4 rounded mb-2" />
                    <div className="bg-default-200 h-4 rounded w-3/4" />
                  </div>
                ))}
              </div>
            )}

            {/* Error state */}
            {sectionsError && (
              <div className="text-center text-danger">
                {sectionsError}
              </div>
            )}

            {/* Products grid */}
            {!sectionsLoading && !sectionsError && (
              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
                {recommendedProducts.map((product) => {
                  const productItem = transformEcomProductToProductItem(product);
                  return (
                    <div key={product.id} className="flex justify-center">
                      <ProductListItem {...productItem} className="w-full" />
                    </div>
                  );
                })}
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}

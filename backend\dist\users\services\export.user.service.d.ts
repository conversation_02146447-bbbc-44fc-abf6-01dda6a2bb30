import { Repository } from 'typeorm';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { BaseUserService } from './base.user.service';
import { User } from '../entities/user.entity';
import { Role } from '../../roles/entities/role.entity';
import { UserDto } from '../dto/user.dto';
import { StreamingExportService } from '../../common/services/streaming-export.service';
import { Response } from 'express';
export declare class ExportUserService extends BaseUserService {
    protected readonly userRepository: Repository<User>;
    protected readonly roleRepository: Repository<Role>;
    protected readonly eventEmitter: EventEmitter2;
    private readonly streamingExportService;
    constructor(userRepository: Repository<User>, roleRepository: Repository<Role>, eventEmitter: EventEmitter2, streamingExportService: StreamingExportService);
    exportStream(format: "csv" | "json" | undefined, response: Response, batchSize?: number): Promise<void>;
    export(format?: 'csv' | 'json'): Promise<string | UserDto[]>;
    getExportEstimate(): Promise<{
        totalRecords: number;
        estimatedTimeSeconds: number;
        recommendedBatchSize: number;
        maxAllowedRecords: number;
    }>;
}

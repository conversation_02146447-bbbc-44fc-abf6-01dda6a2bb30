"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CmsTagsModule = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const cms_tags_entity_1 = require("./entity/cms-tags.entity");
const user_entity_1 = require("../users/entities/user.entity");
const base_cms_tags_service_1 = require("./services/base.cms-tags.service");
const slug_cms_tags_service_1 = require("./services/slug.cms-tags.service");
const create_cms_tags_service_1 = require("./services/create.cms-tags.service");
const read_cms_tags_service_1 = require("./services/read.cms-tags.service");
const update_cms_tags_service_1 = require("./services/update.cms-tags.service");
const delete_cms_tags_service_1 = require("./services/delete.cms-tags.service");
const unified_slug_service_1 = require("../common/services/unified-slug.service");
const controllers_1 = require("./controllers");
let CmsTagsModule = class CmsTagsModule {
};
exports.CmsTagsModule = CmsTagsModule;
exports.CmsTagsModule = CmsTagsModule = __decorate([
    (0, common_1.Module)({
        imports: [
            typeorm_1.TypeOrmModule.forFeature([cms_tags_entity_1.CmsTags, user_entity_1.User]),
        ],
        controllers: [
            controllers_1.CreateCmsTagsController,
            controllers_1.ReadCmsTagsController,
            controllers_1.UpdateCmsTagsController,
            controllers_1.DeleteCmsTagsController,
            controllers_1.ReadCmsTagsPublicController,
        ],
        providers: [
            unified_slug_service_1.UnifiedSlugService,
            base_cms_tags_service_1.BaseCmsTagsService,
            slug_cms_tags_service_1.SlugCmsTagsService,
            create_cms_tags_service_1.CreateCmsTagsService,
            read_cms_tags_service_1.ReadCmsTagsService,
            update_cms_tags_service_1.UpdateCmsTagsService,
            delete_cms_tags_service_1.DeleteCmsTagsService,
        ],
        exports: [
            typeorm_1.TypeOrmModule,
            base_cms_tags_service_1.BaseCmsTagsService,
            slug_cms_tags_service_1.SlugCmsTagsService,
            create_cms_tags_service_1.CreateCmsTagsService,
            read_cms_tags_service_1.ReadCmsTagsService,
            update_cms_tags_service_1.UpdateCmsTagsService,
            delete_cms_tags_service_1.DeleteCmsTagsService,
        ],
    })
], CmsTagsModule);
//# sourceMappingURL=cms-tags.module.js.map
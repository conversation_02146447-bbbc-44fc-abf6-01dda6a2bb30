import { Logger } from '@nestjs/common';
import { Repository, DataSource } from 'typeorm';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { SystemConfig } from '../entities/system-config.entity';
import { SystemConfigDto } from '../dto/system-config.dto';
import { CreateSystemConfigDto } from '../dto/create-system-config.dto';
import { BaseSystemConfigService } from './base.system-config.service';
import { BulkUpdateSystemConfigItemDto } from '../dto/bulk-update-system-config.dto';
export declare class CreateSystemConfigService extends BaseSystemConfigService {
    protected readonly systemConfigRepository: Repository<SystemConfig>;
    protected readonly dataSource: DataSource;
    protected readonly eventEmitter: EventEmitter2;
    protected logger: Logger;
    constructor(systemConfigRepository: Repository<SystemConfig>, dataSource: DataSource, eventEmitter: EventEmitter2);
    create(createSystemConfigDto: CreateSystemConfigDto): Promise<SystemConfigDto>;
    createOrUpdateBulk(items: BulkUpdateSystemConfigItemDto[]): Promise<SystemConfigDto[]>;
}

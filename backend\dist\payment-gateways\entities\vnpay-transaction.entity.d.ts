export declare enum VnpayTransactionStatus {
    PENDING = "PENDING",
    SUCCESS = "SUCCESS",
    FAILED = "FAILED",
    CANCELLED = "CANCELLED",
    EXPIRED = "EXPIRED"
}
export declare enum VnpayTransactionType {
    PAYMENT = "PAYMENT",
    REFUND = "REFUND",
    QUERY = "QUERY"
}
export declare class VnpayTransaction {
    id: string;
    merchantTxnRef: string;
    vnpayTxnRef: string;
    vnpayTxnNo: string;
    type: VnpayTransactionType;
    status: VnpayTransactionStatus;
    amount: number;
    currency: string;
    orderInfo: string;
    bankCode: string;
    cardType: string;
    vnpayResponseCode: string;
    vnpayTransactionStatus: string;
    vnpayPayDate: string;
    clientIp: string;
    locale: string;
    vnpayRequest: string;
    vnpayResponse: string;
    returnCallbackData: string;
    ipnCallbackData: string;
    externalRef: string;
    externalMetadata: string;
    errorMessage: string;
    retryCount: number;
    expiresAt: Date;
    processedAt: Date;
    createdAt: Date;
    updatedAt: Date;
}

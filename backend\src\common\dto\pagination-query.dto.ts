// src/common/dto/pagination-query.dto.ts
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { IsBoolean, IsEnum, IsIn, IsInt, IsOptional, IsString, Max, Min } from 'class-validator';

export enum SortOrder {
  ASC = 'ASC',
  DESC = 'DESC',
}

export class PaginationQueryDto {
  @ApiPropertyOptional({
    minimum: 1,
    default: 1,
    description: 'Số trang hiện tại',
  })
  @Type(() => Number) // Chuyển đổi string từ query param thành number
  @IsInt()
  @Min(1)
  @IsOptional()
  readonly page: number = 1;

  @ApiPropertyOptional({
    minimum: 1,
    maximum: 200, // Giới hạn số lượng item mỗi trang
    default: 10,
    description: 'Số lượng item trên mỗi trang',
  })
  @Type(() => Number)
  @IsInt()
  @Min(1)
  @Max(100) // Đặt giới hạn max hợp lý
  @IsOptional()
  readonly limit: number = 10;

  @ApiPropertyOptional({
    description: 'Từ khóa tìm kiếm (áp dụng cho các trường được chỉ định)',
  })
  @IsString()
  @IsOptional()
  readonly search?: string;

  @ApiPropertyOptional({
    description: 'Bộ lọc theo trường (format: field:value)',
    example: 'isActive:true',
  })
  @IsString()
  @IsOptional()
  readonly filter?: string;

  @ApiPropertyOptional({
    description: 'Trường dùng để sắp xếp',
    example: 'createdAt', // Ví dụ
  })
  @IsString()
  @IsOptional()
  readonly sortBy?: string; // Service sẽ cần validate trường này có hợp lệ không

  @ApiPropertyOptional({
    enum: SortOrder,
    default: SortOrder.DESC,
    description: 'Thứ tự sắp xếp',
  })
  @IsEnum(SortOrder)
  @IsOptional()
  readonly sortOrder: SortOrder = SortOrder.DESC;

  @ApiPropertyOptional({
    description: 'Export data instead of paginated results',
    type: Boolean,
  })
  @IsOptional()
  @IsBoolean()
  @Type(() => Boolean)
  readonly export?: boolean;

  @ApiPropertyOptional({
    description: 'Export format (only used when export=true)',
    enum: ['csv', 'json'],
    default: 'json',
  })
  @IsOptional()
  @IsIn(['csv', 'json'])
  readonly format?: 'csv' | 'json' = 'json';

  // Tính toán giá trị skip để dùng trong TypeORM
  get skip(): number {
    return (this.page - 1) * this.limit;
  }
}

"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ReadCmsPartnersService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const event_emitter_1 = require("@nestjs/event-emitter");
const cms_partners_entity_1 = require("../entity/cms-partners.entity");
const base_cms_partners_service_1 = require("./base.cms-partners.service");
const dto_1 = require("../dto");
const class_transformer_1 = require("class-transformer");
let ReadCmsPartnersService = class ReadCmsPartnersService extends base_cms_partners_service_1.BaseCmsPartnersService {
    partnerRepository;
    eventEmitter;
    constructor(partnerRepository, eventEmitter) {
        super(partnerRepository, eventEmitter);
        this.partnerRepository = partnerRepository;
        this.eventEmitter = eventEmitter;
        this.logger.log('ReadCmsPartnersService đã được khởi tạo');
    }
    async findAll(paginationQuery, relations = []) {
        try {
            const { page = 1, limit = 10 } = paginationQuery;
            const skip = (page - 1) * limit;
            let relationsArray = paginationQuery.relationsArray || [];
            const requiredRelations = ['creator', 'updater', 'deleter', 'approver'];
            requiredRelations.forEach(relation => {
                if (!relationsArray.includes(relation)) {
                    relationsArray.push(relation);
                }
            });
            const queryBuilder = this.partnerRepository
                .createQueryBuilder('partner')
                .where('partner.isDeleted = :isDeleted', { isDeleted: false });
            relations.forEach(relation => {
                queryBuilder.leftJoinAndSelect(`partner.${relation}`, relation);
            });
            const sortOptions = paginationQuery.sortOptions;
            if (sortOptions.length > 0) {
                const firstSort = sortOptions[0];
                queryBuilder.orderBy(`partner.${firstSort.field}`, firstSort.order);
                for (let i = 1; i < sortOptions.length; i++) {
                    const sort = sortOptions[i];
                    queryBuilder.addOrderBy(`partner.${sort.field}`, sort.order);
                }
            }
            else {
                queryBuilder.orderBy('partner.displayOrder', 'ASC');
            }
            queryBuilder.skip(skip).take(limit);
            const [partners, total] = await queryBuilder.getManyAndCount();
            const data = partners.map(partner => (0, class_transformer_1.plainToClass)(dto_1.CmsPartnerDto, partner, { excludeExtraneousValues: true }));
            return { data, total };
        }
        catch (error) {
            this.logger.error(`Lỗi khi lấy danh sách đối tác: ${error.message}`, error.stack);
            throw error;
        }
    }
    async findByStatus(status, paginationQuery, relations = []) {
        try {
            const { page = 1, limit = 10 } = paginationQuery;
            const skip = (page - 1) * limit;
            const queryBuilder = this.partnerRepository
                .createQueryBuilder('partner')
                .where('partner.isDeleted = :isDeleted', { isDeleted: false })
                .andWhere('partner.status = :status', { status });
            relations.forEach(relation => {
                queryBuilder.leftJoinAndSelect(`partner.${relation}`, relation);
            });
            const sortOptions = paginationQuery.sortOptions;
            if (sortOptions.length > 0) {
                const firstSort = sortOptions[0];
                queryBuilder.orderBy(`partner.${firstSort.field}`, firstSort.order);
                for (let i = 1; i < sortOptions.length; i++) {
                    const sort = sortOptions[i];
                    queryBuilder.addOrderBy(`partner.${sort.field}`, sort.order);
                }
            }
            else {
                queryBuilder.orderBy('partner.displayOrder', 'ASC');
            }
            queryBuilder.skip(skip).take(limit);
            const [partners, total] = await queryBuilder.getManyAndCount();
            const data = partners.map(partner => (0, class_transformer_1.plainToClass)(dto_1.CmsPartnerDto, partner, { excludeExtraneousValues: true }));
            return { data, total };
        }
        catch (error) {
            this.logger.error(`Lỗi khi lấy danh sách đối tác theo trạng thái ${status}: ${error.message}`, error.stack);
            throw error;
        }
    }
    async findByType(type, paginationQuery, relations = []) {
        try {
            const { page = 1, limit = 10 } = paginationQuery;
            const skip = (page - 1) * limit;
            const queryBuilder = this.partnerRepository
                .createQueryBuilder('partner')
                .where('partner.isDeleted = :isDeleted', { isDeleted: false })
                .andWhere('partner.type = :type', { type });
            relations.forEach(relation => {
                queryBuilder.leftJoinAndSelect(`partner.${relation}`, relation);
            });
            const sortOptions = paginationQuery.sortOptions;
            if (sortOptions.length > 0) {
                const firstSort = sortOptions[0];
                queryBuilder.orderBy(`partner.${firstSort.field}`, firstSort.order);
                for (let i = 1; i < sortOptions.length; i++) {
                    const sort = sortOptions[i];
                    queryBuilder.addOrderBy(`partner.${sort.field}`, sort.order);
                }
            }
            else {
                queryBuilder.orderBy('partner.displayOrder', 'ASC');
            }
            queryBuilder.skip(skip).take(limit);
            const [partners, total] = await queryBuilder.getManyAndCount();
            const data = partners.map(partner => (0, class_transformer_1.plainToClass)(dto_1.CmsPartnerDto, partner, { excludeExtraneousValues: true }));
            return { data, total };
        }
        catch (error) {
            this.logger.error(`Lỗi khi lấy danh sách đối tác theo loại ${type}: ${error.message}`, error.stack);
            throw error;
        }
    }
    async getActivePartners(paginationQuery, relations = []) {
        return this.findByStatus(cms_partners_entity_1.CmsPartnerStatus.ACTIVE, paginationQuery, relations);
    }
    async getStatistics() {
        try {
            const [total, active, inactive, partnerCount, clientCount, supplierCount, withLogo, withWebsite, withCompleteInfo,] = await Promise.all([
                this.count(),
                this.count(cms_partners_entity_1.CmsPartnerStatus.ACTIVE),
                this.count(cms_partners_entity_1.CmsPartnerStatus.INACTIVE),
                this.countByType(cms_partners_entity_1.CmsPartnerType.PARTNER),
                this.countByType(cms_partners_entity_1.CmsPartnerType.CLIENT),
                this.countByType(cms_partners_entity_1.CmsPartnerType.SUPPLIER),
                this.partnerRepository
                    .createQueryBuilder('partner')
                    .where('partner.isDeleted = :isDeleted', { isDeleted: false })
                    .andWhere('partner.logoUrl IS NOT NULL')
                    .getCount(),
                this.partnerRepository
                    .createQueryBuilder('partner')
                    .where('partner.isDeleted = :isDeleted', { isDeleted: false })
                    .andWhere('partner.websiteUrl IS NOT NULL')
                    .getCount(),
                this.partnerRepository
                    .createQueryBuilder('partner')
                    .where('partner.isDeleted = :isDeleted', { isDeleted: false })
                    .andWhere('partner.name IS NOT NULL')
                    .andWhere('partner.logoUrl IS NOT NULL')
                    .andWhere('partner.websiteUrl IS NOT NULL')
                    .andWhere('partner.description IS NOT NULL')
                    .getCount(),
            ]);
            return {
                total,
                active,
                inactive,
                byType: {
                    [cms_partners_entity_1.CmsPartnerType.PARTNER]: partnerCount,
                    [cms_partners_entity_1.CmsPartnerType.CLIENT]: clientCount,
                    [cms_partners_entity_1.CmsPartnerType.SUPPLIER]: supplierCount,
                },
                withLogo,
                withWebsite,
                withCompleteInfo,
            };
        }
        catch (error) {
            this.logger.error(`Lỗi khi lấy thống kê đối tác: ${error.message}`, error.stack);
            throw error;
        }
    }
    async count(status) {
        try {
            const queryBuilder = this.partnerRepository
                .createQueryBuilder('partner')
                .where('partner.isDeleted = :isDeleted', { isDeleted: false });
            if (status) {
                queryBuilder.andWhere('partner.status = :status', { status });
            }
            return await queryBuilder.getCount();
        }
        catch (error) {
            this.logger.error(`Lỗi khi đếm đối tác: ${error.message}`, error.stack);
            throw error;
        }
    }
    async countByType(type) {
        try {
            return await this.partnerRepository.count({
                where: { type, isDeleted: false },
            });
        }
        catch (error) {
            this.logger.error(`Lỗi khi đếm đối tác theo loại ${type}: ${error.message}`, error.stack);
            throw error;
        }
    }
    async search(searchTerm, paginationQuery, relations = []) {
        try {
            const { page = 1, limit = 10 } = paginationQuery;
            const skip = (page - 1) * limit;
            const queryBuilder = this.partnerRepository
                .createQueryBuilder('partner')
                .where('partner.isDeleted = :isDeleted', { isDeleted: false })
                .andWhere('(partner.name ILIKE :searchTerm OR partner.description ILIKE :searchTerm)', { searchTerm: `%${searchTerm}%` });
            relations.forEach(relation => {
                queryBuilder.leftJoinAndSelect(`partner.${relation}`, relation);
            });
            const sortOptions = paginationQuery.sortOptions;
            if (sortOptions.length > 0) {
                const firstSort = sortOptions[0];
                queryBuilder.orderBy(`partner.${firstSort.field}`, firstSort.order);
                for (let i = 1; i < sortOptions.length; i++) {
                    const sort = sortOptions[i];
                    queryBuilder.addOrderBy(`partner.${sort.field}`, sort.order);
                }
            }
            else {
                queryBuilder.orderBy('partner.displayOrder', 'ASC');
            }
            queryBuilder.skip(skip).take(limit);
            const [partners, total] = await queryBuilder.getManyAndCount();
            const data = partners.map(partner => (0, class_transformer_1.plainToClass)(dto_1.CmsPartnerDto, partner, { excludeExtraneousValues: true }));
            return { data, total };
        }
        catch (error) {
            this.logger.error(`Lỗi khi tìm kiếm đối tác với từ khóa "${searchTerm}": ${error.message}`, error.stack);
            throw error;
        }
    }
    async getDeleted(paginationQuery, relations = []) {
        try {
            const { page = 1, limit = 10 } = paginationQuery;
            const skip = (page - 1) * limit;
            const queryBuilder = this.partnerRepository
                .createQueryBuilder('partner')
                .where('partner.isDeleted = :isDeleted', { isDeleted: true });
            relations.forEach(relation => {
                queryBuilder.leftJoinAndSelect(`partner.${relation}`, relation);
            });
            const sortOptions = paginationQuery.sortOptions;
            if (sortOptions.length > 0) {
                const firstSort = sortOptions[0];
                queryBuilder.orderBy(`partner.${firstSort.field}`, firstSort.order);
                for (let i = 1; i < sortOptions.length; i++) {
                    const sort = sortOptions[i];
                    queryBuilder.addOrderBy(`partner.${sort.field}`, sort.order);
                }
            }
            else {
                queryBuilder.orderBy('partner.deletedAt', 'DESC');
            }
            queryBuilder.skip(skip).take(limit);
            const [partners, total] = await queryBuilder.getManyAndCount();
            const data = partners.map(partner => (0, class_transformer_1.plainToClass)(dto_1.CmsPartnerDto, partner, { excludeExtraneousValues: true }));
            return { data, total };
        }
        catch (error) {
            this.logger.error(`Lỗi khi lấy danh sách đối tác đã xóa: ${error.message}`, error.stack);
            throw error;
        }
    }
    async getPartnerById(id, relations = []) {
        try {
            const partner = await super.findById(id, relations);
            if (!partner) {
                throw new common_1.NotFoundException(`Không tìm thấy đối tác với ID: ${id}`);
            }
            return (0, class_transformer_1.plainToClass)(dto_1.CmsPartnerDto, partner, { excludeExtraneousValues: true });
        }
        catch (error) {
            if (error instanceof common_1.NotFoundException) {
                throw error;
            }
            this.logger.error(`Lỗi khi lấy thông tin đối tác theo ID ${id}: ${error.message}`, error.stack);
            throw error;
        }
    }
    async getPartnerByName(name, relations = []) {
        try {
            const partner = await super.findByName(name, relations);
            if (!partner) {
                return null;
            }
            return (0, class_transformer_1.plainToClass)(dto_1.CmsPartnerDto, partner, { excludeExtraneousValues: true });
        }
        catch (error) {
            this.logger.error(`Lỗi khi lấy thông tin đối tác theo tên ${name}: ${error.message}`, error.stack);
            throw error;
        }
    }
};
exports.ReadCmsPartnersService = ReadCmsPartnersService;
exports.ReadCmsPartnersService = ReadCmsPartnersService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(cms_partners_entity_1.CmsPartners)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        event_emitter_1.EventEmitter2])
], ReadCmsPartnersService);
//# sourceMappingURL=read.cms-partners.service.js.map
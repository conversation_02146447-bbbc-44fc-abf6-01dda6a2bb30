"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.BaseAssetsService = void 0;
const common_1 = require("@nestjs/common");
const event_emitter_1 = require("@nestjs/event-emitter");
const typeorm_1 = require("@nestjs/typeorm");
const class_transformer_1 = require("class-transformer");
const typeorm_2 = require("typeorm");
const ecom_products_entity_1 = require("../../ecom-products/entity/ecom-products.entity");
const user_entity_1 = require("../../users/entities/user.entity");
const wallet_entity_1 = require("../../wallets/entities/wallet.entity");
const asset_dto_1 = require("../dto/asset.dto");
const asset_entity_1 = require("../entities/asset.entity");
let BaseAssetsService = class BaseAssetsService {
    assetRepository;
    ecomProductRepository;
    userRepository;
    walletRepository;
    eventEmitter;
    logger = new common_1.Logger('TokenAssetsService');
    validRelations = [
        'product',
        'user',
        'creator',
        'updater',
        'deleter',
    ];
    EVENT_TOKEN_ASSET_CREATED = 'asset.created';
    EVENT_TOKEN_ASSET_UPDATED = 'asset.updated';
    EVENT_TOKEN_ASSET_DELETED = 'asset.deleted';
    EVENT_TOKEN_ASSET_RESTORED = 'asset.restored';
    EVENT_TOKEN_ASSET_DUPLICATED = 'asset.duplicated';
    EVENT_TOKEN_ASSET_CLEANUP = 'asset.cleanup';
    constructor(assetRepository, ecomProductRepository, userRepository, walletRepository, eventEmitter) {
        this.assetRepository = assetRepository;
        this.ecomProductRepository = ecomProductRepository;
        this.userRepository = userRepository;
        this.walletRepository = walletRepository;
        this.eventEmitter = eventEmitter;
    }
    toDto(tokenAsset) {
        return (0, class_transformer_1.plainToInstance)(asset_dto_1.AssetDto, tokenAsset, {
            excludeExtraneousValues: true,
        });
    }
    validateRelations(relations) {
        if (!relations || !relations.length)
            return [];
        return relations.filter((rel) => this.validRelations.includes(rel));
    }
    async findByIdOrFail(id, relations = [], withDeleted = false) {
        const validatedRelations = this.validateRelations(relations);
        const tokenAsset = await this.assetRepository.findOne({
            where: { id, ...(withDeleted ? {} : { isDeleted: false }) },
            relations: validatedRelations,
        });
        if (!tokenAsset) {
            throw new common_1.NotFoundException(`Không tìm thấy tài sản token với ID "${id}"${withDeleted ? '' : ' hoặc đã bị xóa'}`);
        }
        return tokenAsset;
    }
    buildWhereClause(filter) {
        const whereClause = { isDeleted: false };
        if (filter) {
            try {
                const [field, value] = filter.split(':');
                whereClause[field] = value;
            }
            catch (error) {
                this.logger.warn(`Định dạng lọc không hợp lệ: ${filter}`);
            }
        }
        return whereClause;
    }
    async getQueryRunner() {
        const queryRunner = this.assetRepository.manager.connection.createQueryRunner();
        await queryRunner.connect();
        await queryRunner.startTransaction();
        return queryRunner;
    }
    async findTokenOrFail(tokenId) {
        const token = await this.ecomProductRepository.findOne({
            where: { id: tokenId },
        });
        if (!token) {
            throw new common_1.NotFoundException(`Token với ID ${tokenId} không tồn tại`);
        }
        return token;
    }
    async findEcomProductOrFail(productId) {
        const product = await this.ecomProductRepository.findOne({
            where: { id: productId },
        });
        if (!product) {
            throw new common_1.NotFoundException(`Sản phẩm với ID ${productId} không tồn tại`);
        }
        return product;
    }
    async findUserOrFail(userId) {
        const user = await this.userRepository.findOne({
            where: { id: userId },
        });
        if (!user) {
            throw new common_1.NotFoundException(`User với ID ${userId} không tồn tại`);
        }
        return user;
    }
};
exports.BaseAssetsService = BaseAssetsService;
exports.BaseAssetsService = BaseAssetsService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(asset_entity_1.Asset)),
    __param(1, (0, typeorm_1.InjectRepository)(ecom_products_entity_1.EcomProduct)),
    __param(2, (0, typeorm_1.InjectRepository)(user_entity_1.User)),
    __param(3, (0, typeorm_1.InjectRepository)(wallet_entity_1.Wallet)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository,
        event_emitter_1.EventEmitter2])
], BaseAssetsService);
//# sourceMappingURL=base-assets.service.js.map
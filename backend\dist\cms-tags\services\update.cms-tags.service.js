"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UpdateCmsTagsService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const event_emitter_1 = require("@nestjs/event-emitter");
const typeorm_transactional_1 = require("typeorm-transactional");
const base_cms_tags_service_1 = require("./base.cms-tags.service");
const slug_cms_tags_service_1 = require("./slug.cms-tags.service");
const cms_tags_entity_1 = require("../entity/cms-tags.entity");
const update_cms_tag_dto_1 = require("../dto/update.cms-tag.dto");
let UpdateCmsTagsService = class UpdateCmsTagsService extends base_cms_tags_service_1.BaseCmsTagsService {
    tagRepository;
    dataSource;
    eventEmitter;
    slugService;
    constructor(tagRepository, dataSource, eventEmitter, slugService) {
        super(tagRepository, dataSource, eventEmitter);
        this.tagRepository = tagRepository;
        this.dataSource = dataSource;
        this.eventEmitter = eventEmitter;
        this.slugService = slugService;
    }
    async update(id, updateDto, userId) {
        try {
            this.logger.debug(`Đang cập nhật thẻ CMS với ID: ${id}`);
            const tag = await this.findById(id, []);
            if (!tag) {
                throw new common_1.NotFoundException(`Không tìm thấy thẻ với ID: ${id}`);
            }
            if (updateDto.name && updateDto.name !== tag.name) {
                const existingTagByName = await this.findByName(updateDto.name, false);
                if (existingTagByName && existingTagByName.id !== id) {
                    throw new common_1.ConflictException(`Tên thẻ "${updateDto.name}" đã tồn tại`);
                }
            }
            const oldData = this.toDto(tag);
            let newSlug = tag.slug;
            const nameChanged = updateDto.name !== undefined && updateDto.name !== tag.name;
            const slugChanged = updateDto.slug !== undefined && updateDto.slug !== tag.slug;
            if (nameChanged || slugChanged) {
                const nameToUse = updateDto.name !== undefined ? updateDto.name : tag.name;
                newSlug = await this.slugService.generateUniqueSlugForUpdate(nameToUse, id, updateDto.slug);
            }
            if (updateDto.name !== undefined) {
                tag.name = updateDto.name;
            }
            if (nameChanged || slugChanged) {
                tag.slug = newSlug;
            }
            if (updateDto.description !== undefined) {
                tag.description = updateDto.description || null;
            }
            if (updateDto.imageUrl !== undefined) {
                tag.imageUrl = updateDto.imageUrl || null;
            }
            if (updateDto.metaTitle !== undefined) {
                tag.metaTitle = updateDto.metaTitle || null;
            }
            if (updateDto.metaDescription !== undefined) {
                tag.metaDescription = updateDto.metaDescription || null;
            }
            if (updateDto.metaKeywords !== undefined) {
                tag.metaKeywords = updateDto.metaKeywords || null;
            }
            tag.updatedBy = userId;
            const updatedTag = await this.tagRepository.save(tag);
            const tagDto = this.toDto(updatedTag);
            if (!tagDto) {
                throw new common_1.InternalServerErrorException('Không thể chuyển đổi entity thành DTO');
            }
            this.eventEmitter.emit(this.EVENT_TAG_UPDATED, {
                tagId: tagDto.id,
                userId,
                oldData,
                newData: tagDto,
            });
            return tagDto;
        }
        catch (error) {
            this.logger.error(`Lỗi khi cập nhật thẻ CMS: ${error.message}`, error.stack);
            if (error instanceof common_1.BadRequestException || error instanceof common_1.NotFoundException || error instanceof common_1.ConflictException) {
                throw error;
            }
            throw new common_1.InternalServerErrorException(`Không thể cập nhật thẻ CMS: ${error.message}`);
        }
    }
    async bulkUpdate(updates, userId) {
        try {
            this.logger.debug(`Đang cập nhật ${updates.length} thẻ CMS`);
            const updatedTags = [];
            for (const update of updates) {
                const tag = await this.update(update.id, update.data, userId);
                if (tag) {
                    updatedTags.push(tag);
                }
            }
            return updatedTags;
        }
        catch (error) {
            this.logger.error(`Lỗi khi cập nhật nhiều thẻ CMS: ${error.message}`, error.stack);
            throw new common_1.InternalServerErrorException(`Không thể cập nhật nhiều thẻ CMS: ${error.message}`);
        }
    }
    async updateSlugFromName(id, userId) {
        try {
            this.logger.debug(`Đang cập nhật slug từ tên cho thẻ CMS với ID: ${id}`);
            const tag = await this.findById(id, []);
            if (!tag) {
                throw new common_1.NotFoundException(`Không tìm thấy thẻ với ID: ${id}`);
            }
            const newSlug = await this.slugService.generateUniqueSlugForUpdate(tag.name, id);
            const updateDto = {
                slug: newSlug,
            };
            return this.update(id, updateDto, userId);
        }
        catch (error) {
            this.logger.error(`Lỗi khi cập nhật slug từ tên: ${error.message}`, error.stack);
            if (error instanceof common_1.NotFoundException) {
                throw error;
            }
            throw new common_1.InternalServerErrorException(`Không thể cập nhật slug từ tên: ${error.message}`);
        }
    }
    async updateNameAndSlug(id, name, userId) {
        try {
            this.logger.debug(`Đang cập nhật tên và slug cho thẻ CMS với ID: ${id}`);
            const newSlug = await this.slugService.generateUniqueSlugForUpdate(name, id);
            const updateDto = {
                name,
                slug: newSlug,
            };
            return this.update(id, updateDto, userId);
        }
        catch (error) {
            this.logger.error(`Lỗi khi cập nhật tên và slug: ${error.message}`, error.stack);
            if (error instanceof common_1.NotFoundException || error instanceof common_1.ConflictException) {
                throw error;
            }
            throw new common_1.InternalServerErrorException(`Không thể cập nhật tên và slug: ${error.message}`);
        }
    }
};
exports.UpdateCmsTagsService = UpdateCmsTagsService;
__decorate([
    (0, typeorm_transactional_1.Transactional)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, update_cms_tag_dto_1.UpdateCmsTagDto, String]),
    __metadata("design:returntype", Promise)
], UpdateCmsTagsService.prototype, "update", null);
__decorate([
    (0, typeorm_transactional_1.Transactional)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Array, String]),
    __metadata("design:returntype", Promise)
], UpdateCmsTagsService.prototype, "bulkUpdate", null);
__decorate([
    (0, typeorm_transactional_1.Transactional)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String]),
    __metadata("design:returntype", Promise)
], UpdateCmsTagsService.prototype, "updateSlugFromName", null);
__decorate([
    (0, typeorm_transactional_1.Transactional)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String, String]),
    __metadata("design:returntype", Promise)
], UpdateCmsTagsService.prototype, "updateNameAndSlug", null);
exports.UpdateCmsTagsService = UpdateCmsTagsService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(cms_tags_entity_1.CmsTags)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        typeorm_2.DataSource,
        event_emitter_1.EventEmitter2,
        slug_cms_tags_service_1.SlugCmsTagsService])
], UpdateCmsTagsService);
//# sourceMappingURL=update.cms-tags.service.js.map
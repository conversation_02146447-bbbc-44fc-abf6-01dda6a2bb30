"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UpdateSystemConfigDto = void 0;
const openapi = require("@nestjs/swagger");
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
const system_config_dto_1 = require("./system-config.dto");
class UpdateSystemConfigDto {
    configValue;
    description;
    configGroup;
    sectionName;
    sectionDisplayName;
    sectionDescription;
    sectionOrder;
    displayOrder;
    groupDisplayName;
    groupDescription;
    groupIcon;
    groupOrder;
    isGroupConfig;
    configType;
    configOptions;
    updatedBy;
    static _OPENAPI_METADATA_FACTORY() {
        return { configValue: { required: false, type: () => String }, description: { required: false, type: () => String }, configGroup: { required: false, type: () => String }, sectionName: { required: false, type: () => String }, sectionDisplayName: { required: false, type: () => String }, sectionDescription: { required: false, type: () => String }, sectionOrder: { required: false, type: () => Number }, displayOrder: { required: false, type: () => Number }, groupDisplayName: { required: false, type: () => String }, groupDescription: { required: false, type: () => String }, groupIcon: { required: false, type: () => String }, groupOrder: { required: false, type: () => Number }, isGroupConfig: { required: false, type: () => Boolean }, configType: { required: false, enum: require("./system-config.dto").ConfigType }, configOptions: { required: false, type: () => String }, updatedBy: { required: false, type: () => String } };
    }
}
exports.UpdateSystemConfigDto = UpdateSystemConfigDto;
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Giá trị cấu hình' }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], UpdateSystemConfigDto.prototype, "configValue", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Mô tả cấu hình' }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], UpdateSystemConfigDto.prototype, "description", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Nhóm cấu hình',
        example: 'general'
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], UpdateSystemConfigDto.prototype, "configGroup", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Tên section của cấu hình',
        example: 'header'
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], UpdateSystemConfigDto.prototype, "sectionName", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Tên hiển thị của section',
        example: 'Cấu hình Header'
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], UpdateSystemConfigDto.prototype, "sectionDisplayName", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Mô tả của section',
        example: 'Cấu hình phần header của website'
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], UpdateSystemConfigDto.prototype, "sectionDescription", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Thứ tự hiển thị của section',
        example: 1
    }),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Number)
], UpdateSystemConfigDto.prototype, "sectionOrder", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Thứ tự hiển thị của trường cấu hình trong section',
        example: 1
    }),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Number)
], UpdateSystemConfigDto.prototype, "displayOrder", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Tên hiển thị của nhóm cấu hình',
        example: 'Cấu hình chung'
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], UpdateSystemConfigDto.prototype, "groupDisplayName", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Mô tả của nhóm cấu hình',
        example: 'Cấu hình chung của hệ thống'
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], UpdateSystemConfigDto.prototype, "groupDescription", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Icon của nhóm cấu hình',
        example: 'settings'
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], UpdateSystemConfigDto.prototype, "groupIcon", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Thứ tự hiển thị của nhóm cấu hình',
        example: 1
    }),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Number)
], UpdateSystemConfigDto.prototype, "groupOrder", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Cờ đánh dấu đây là cấu hình nhóm',
        example: false
    }),
    (0, class_validator_1.IsBoolean)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Boolean)
], UpdateSystemConfigDto.prototype, "isGroupConfig", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Loại cấu hình',
        enum: system_config_dto_1.ConfigType
    }),
    (0, class_validator_1.IsEnum)(system_config_dto_1.ConfigType),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], UpdateSystemConfigDto.prototype, "configType", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Tùy chọn cho loại select',
        example: '["option1", "option2"]'
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], UpdateSystemConfigDto.prototype, "configOptions", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'ID người cập nhật',
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], UpdateSystemConfigDto.prototype, "updatedBy", void 0);
//# sourceMappingURL=update-system-config.dto.js.map
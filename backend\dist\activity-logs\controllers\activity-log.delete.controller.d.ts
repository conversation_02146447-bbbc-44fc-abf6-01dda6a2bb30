import { DeleteActivityLogService } from '../services/delete.activity-log.service';
import { ActivityLogDto } from '../dto/activity-log.dto';
import { PaginationResponseDto } from '../../common/dto/pagination-response.dto';
export declare class ActivityLogDeleteController {
    private readonly activityLogService;
    private readonly logger;
    constructor(activityLogService: DeleteActivityLogService);
    softDelete(id: string, userId: string): Promise<ActivityLogDto>;
    bulkSoftDelete(ids: string[], userId: string): Promise<ActivityLogDto[]>;
    remove(id: string): Promise<void>;
    bulkRemove(ids: string[]): Promise<void>;
    restore(id: string): Promise<ActivityLogDto>;
    findDeleted(limit: number, page: number): Promise<PaginationResponseDto<ActivityLogDto>>;
    clearOldLogs(days: number): Promise<{
        count: number;
    }>;
}

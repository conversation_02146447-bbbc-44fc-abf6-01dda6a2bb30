import { UserDto } from '../../users/dto/user.dto';
export declare enum ConfigType {
    TEXT = "text",
    TEXTAREA = "textarea",
    NUMBER = "number",
    BOOLEAN = "boolean",
    SELECT = "select"
}
export declare class SystemConfigDto {
    id: string;
    configKey: string;
    configValue?: string;
    description?: string;
    configGroup?: string;
    sectionName?: string;
    sectionDisplayName?: string;
    sectionDescription?: string;
    sectionOrder?: number;
    displayOrder?: number;
    groupDisplayName?: string;
    groupDescription?: string;
    groupIcon?: string;
    groupOrder?: number;
    isGroupConfig?: boolean;
    configType?: ConfigType;
    configOptions?: string;
    createdAt: Date;
    updatedAt: Date;
    createdBy?: string;
    updatedBy?: string;
    isDeleted: boolean;
    deletedBy?: string;
    creator?: UserDto;
    updater?: UserDto;
    deleter?: UserDto;
}

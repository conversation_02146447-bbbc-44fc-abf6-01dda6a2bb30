"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UpdateCmsCustomerFeedbacksService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const event_emitter_1 = require("@nestjs/event-emitter");
const typeorm_transactional_1 = require("typeorm-transactional");
const base_cms_customer_feedbacks_service_1 = require("./base.cms-customer-feedbacks.service");
const cms_customer_feedbacks_entity_1 = require("../entity/cms-customer-feedbacks.entity");
const update_cms_customer_feedback_dto_1 = require("../dto/update.cms-customer-feedback.dto");
let UpdateCmsCustomerFeedbacksService = class UpdateCmsCustomerFeedbacksService extends base_cms_customer_feedbacks_service_1.BaseCmsCustomerFeedbacksService {
    feedbackRepository;
    dataSource;
    eventEmitter;
    constructor(feedbackRepository, dataSource, eventEmitter) {
        super(feedbackRepository, dataSource, eventEmitter);
        this.feedbackRepository = feedbackRepository;
        this.dataSource = dataSource;
        this.eventEmitter = eventEmitter;
    }
    async update(id, updateDto, userId) {
        try {
            this.logger.debug(`Đang cập nhật feedback khách hàng CMS với ID: ${id}`);
            const feedback = await this.findById(id, []);
            if (!feedback) {
                throw new common_1.NotFoundException(`Không tìm thấy feedback với ID: ${id}`);
            }
            if (updateDto.rating !== undefined && updateDto.rating !== null && (updateDto.rating < 1 || updateDto.rating > 5)) {
                throw new common_1.BadRequestException('Đánh giá phải từ 1 đến 5 sao');
            }
            const oldData = this.toDto(feedback);
            if (updateDto.customerName !== undefined) {
                feedback.customerName = updateDto.customerName;
            }
            if (updateDto.customerTitleCompany !== undefined) {
                feedback.customerTitleCompany = updateDto.customerTitleCompany || null;
            }
            if (updateDto.feedbackText !== undefined) {
                feedback.feedbackText = updateDto.feedbackText;
            }
            if (updateDto.rating !== undefined) {
                feedback.rating = updateDto.rating || null;
            }
            if (updateDto.avatarUrl !== undefined) {
                feedback.avatarUrl = updateDto.avatarUrl || null;
            }
            if (updateDto.productServiceName !== undefined) {
                feedback.productServiceName = updateDto.productServiceName || null;
            }
            if (updateDto.status !== undefined) {
                feedback.status = updateDto.status;
            }
            feedback.updatedBy = userId;
            const updatedFeedback = await this.feedbackRepository.save(feedback);
            const feedbackDto = this.toDto(updatedFeedback);
            if (!feedbackDto) {
                throw new common_1.InternalServerErrorException('Không thể chuyển đổi entity thành DTO');
            }
            this.eventEmitter.emit(this.EVENT_FEEDBACK_UPDATED, {
                feedbackId: feedbackDto.id,
                userId,
                oldData,
                newData: feedbackDto,
            });
            return feedbackDto;
        }
        catch (error) {
            this.logger.error(`Lỗi khi cập nhật feedback khách hàng CMS: ${error.message}`, error.stack);
            if (error instanceof common_1.BadRequestException || error instanceof common_1.NotFoundException) {
                throw error;
            }
            throw new common_1.InternalServerErrorException(`Không thể cập nhật feedback khách hàng CMS: ${error.message}`);
        }
    }
    async bulkUpdate(updates, userId) {
        try {
            this.logger.debug(`Đang cập nhật ${updates.length} feedback khách hàng CMS`);
            const updatedFeedbacks = [];
            for (const update of updates) {
                const feedback = await this.update(update.id, update.data, userId);
                if (feedback) {
                    updatedFeedbacks.push(feedback);
                }
            }
            return updatedFeedbacks;
        }
        catch (error) {
            this.logger.error(`Lỗi khi cập nhật nhiều feedback khách hàng CMS: ${error.message}`, error.stack);
            throw new common_1.InternalServerErrorException(`Không thể cập nhật nhiều feedback khách hàng CMS: ${error.message}`);
        }
    }
    async approve(id, userId) {
        try {
            this.logger.debug(`Đang duyệt feedback khách hàng CMS với ID: ${id}`);
            const feedback = await this.findById(id, []);
            if (!feedback) {
                throw new common_1.NotFoundException(`Không tìm thấy feedback với ID: ${id}`);
            }
            const oldData = this.toDto(feedback);
            feedback.status = cms_customer_feedbacks_entity_1.CmsCustomerFeedbackStatus.APPROVED;
            feedback.approvedBy = userId;
            feedback.updatedBy = userId;
            const updatedFeedback = await this.feedbackRepository.save(feedback);
            const feedbackDto = this.toDto(updatedFeedback);
            if (!feedbackDto) {
                throw new common_1.InternalServerErrorException('Không thể chuyển đổi entity thành DTO');
            }
            this.eventEmitter.emit(this.EVENT_FEEDBACK_APPROVED, {
                feedbackId: feedbackDto.id,
                userId,
                oldData,
                newData: feedbackDto,
            });
            return feedbackDto;
        }
        catch (error) {
            this.logger.error(`Lỗi khi duyệt feedback khách hàng CMS: ${error.message}`, error.stack);
            if (error instanceof common_1.NotFoundException) {
                throw error;
            }
            throw new common_1.InternalServerErrorException(`Không thể duyệt feedback khách hàng CMS: ${error.message}`);
        }
    }
    async reject(id, userId) {
        try {
            this.logger.debug(`Đang từ chối feedback khách hàng CMS với ID: ${id}`);
            const feedback = await this.findById(id, []);
            if (!feedback) {
                throw new common_1.NotFoundException(`Không tìm thấy feedback với ID: ${id}`);
            }
            const oldData = this.toDto(feedback);
            feedback.status = cms_customer_feedbacks_entity_1.CmsCustomerFeedbackStatus.REJECTED;
            feedback.approvedBy = userId;
            feedback.updatedBy = userId;
            const updatedFeedback = await this.feedbackRepository.save(feedback);
            const feedbackDto = this.toDto(updatedFeedback);
            if (!feedbackDto) {
                throw new common_1.InternalServerErrorException('Không thể chuyển đổi entity thành DTO');
            }
            this.eventEmitter.emit(this.EVENT_FEEDBACK_REJECTED, {
                feedbackId: feedbackDto.id,
                userId,
                oldData,
                newData: feedbackDto,
            });
            return feedbackDto;
        }
        catch (error) {
            this.logger.error(`Lỗi khi từ chối feedback khách hàng CMS: ${error.message}`, error.stack);
            if (error instanceof common_1.NotFoundException) {
                throw error;
            }
            throw new common_1.InternalServerErrorException(`Không thể từ chối feedback khách hàng CMS: ${error.message}`);
        }
    }
    async updateStatus(id, status, userId) {
        try {
            this.logger.debug(`Đang cập nhật trạng thái feedback khách hàng CMS với ID: ${id} thành ${status}`);
            if (status === cms_customer_feedbacks_entity_1.CmsCustomerFeedbackStatus.APPROVED) {
                return this.approve(id, userId);
            }
            else if (status === cms_customer_feedbacks_entity_1.CmsCustomerFeedbackStatus.REJECTED) {
                return this.reject(id, userId);
            }
            else {
                const updateDto = { status };
                return this.update(id, updateDto, userId);
            }
        }
        catch (error) {
            this.logger.error(`Lỗi khi cập nhật trạng thái feedback khách hàng CMS: ${error.message}`, error.stack);
            if (error instanceof common_1.NotFoundException) {
                throw error;
            }
            throw new common_1.InternalServerErrorException(`Không thể cập nhật trạng thái feedback khách hàng CMS: ${error.message}`);
        }
    }
    async bulkApprove(ids, userId) {
        try {
            this.logger.debug(`Đang duyệt ${ids.length} feedback khách hàng CMS`);
            const approvedFeedbacks = [];
            for (const id of ids) {
                try {
                    const feedback = await this.approve(id, userId);
                    if (feedback) {
                        approvedFeedbacks.push(feedback);
                    }
                }
                catch (error) {
                    this.logger.warn(`Không thể duyệt feedback với ID ${id}: ${error.message}`);
                }
            }
            return approvedFeedbacks;
        }
        catch (error) {
            this.logger.error(`Lỗi khi duyệt nhiều feedback khách hàng CMS: ${error.message}`, error.stack);
            throw new common_1.InternalServerErrorException(`Không thể duyệt nhiều feedback khách hàng CMS: ${error.message}`);
        }
    }
    async bulkReject(ids, userId) {
        try {
            this.logger.debug(`Đang từ chối ${ids.length} feedback khách hàng CMS`);
            const rejectedFeedbacks = [];
            for (const id of ids) {
                try {
                    const feedback = await this.reject(id, userId);
                    if (feedback) {
                        rejectedFeedbacks.push(feedback);
                    }
                }
                catch (error) {
                    this.logger.warn(`Không thể từ chối feedback với ID ${id}: ${error.message}`);
                }
            }
            return rejectedFeedbacks;
        }
        catch (error) {
            this.logger.error(`Lỗi khi từ chối nhiều feedback khách hàng CMS: ${error.message}`, error.stack);
            throw new common_1.InternalServerErrorException(`Không thể từ chối nhiều feedback khách hàng CMS: ${error.message}`);
        }
    }
};
exports.UpdateCmsCustomerFeedbacksService = UpdateCmsCustomerFeedbacksService;
__decorate([
    (0, typeorm_transactional_1.Transactional)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, update_cms_customer_feedback_dto_1.UpdateCmsCustomerFeedbackDto, String]),
    __metadata("design:returntype", Promise)
], UpdateCmsCustomerFeedbacksService.prototype, "update", null);
__decorate([
    (0, typeorm_transactional_1.Transactional)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Array, String]),
    __metadata("design:returntype", Promise)
], UpdateCmsCustomerFeedbacksService.prototype, "bulkUpdate", null);
__decorate([
    (0, typeorm_transactional_1.Transactional)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String]),
    __metadata("design:returntype", Promise)
], UpdateCmsCustomerFeedbacksService.prototype, "approve", null);
__decorate([
    (0, typeorm_transactional_1.Transactional)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String]),
    __metadata("design:returntype", Promise)
], UpdateCmsCustomerFeedbacksService.prototype, "reject", null);
__decorate([
    (0, typeorm_transactional_1.Transactional)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String, String]),
    __metadata("design:returntype", Promise)
], UpdateCmsCustomerFeedbacksService.prototype, "updateStatus", null);
__decorate([
    (0, typeorm_transactional_1.Transactional)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Array, String]),
    __metadata("design:returntype", Promise)
], UpdateCmsCustomerFeedbacksService.prototype, "bulkApprove", null);
__decorate([
    (0, typeorm_transactional_1.Transactional)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Array, String]),
    __metadata("design:returntype", Promise)
], UpdateCmsCustomerFeedbacksService.prototype, "bulkReject", null);
exports.UpdateCmsCustomerFeedbacksService = UpdateCmsCustomerFeedbacksService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(cms_customer_feedbacks_entity_1.CmsCustomerFeedbacks)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        typeorm_2.DataSource,
        event_emitter_1.EventEmitter2])
], UpdateCmsCustomerFeedbacksService);
//# sourceMappingURL=update.cms-customer-feedbacks.service.js.map
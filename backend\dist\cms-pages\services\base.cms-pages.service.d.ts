import { Logger } from '@nestjs/common';
import { Repository, DataSource, FindOptionsWhere } from 'typeorm';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { CmsPages } from '../entity/cms-pages.entity';
import { CmsPageDto } from '../dto/cms-page.dto';
export declare class BaseCmsPagesService {
    protected readonly pageRepository: Repository<CmsPages>;
    protected readonly dataSource: DataSource;
    protected readonly eventEmitter: EventEmitter2;
    protected readonly logger: Logger;
    protected readonly EVENT_PAGE_CREATED = "cms-page.created";
    protected readonly EVENT_PAGE_UPDATED = "cms-page.updated";
    protected readonly EVENT_PAGE_DELETED = "cms-page.deleted";
    protected readonly EVENT_PAGE_PUBLISHED = "cms-page.published";
    protected readonly EVENT_PAGE_DRAFTED = "cms-page.drafted";
    protected readonly validRelations: string[];
    constructor(pageRepository: Repository<CmsPages>, dataSource: DataSource, eventEmitter: EventEmitter2);
    protected validateRelations(relations: string[]): string[];
    protected buildWhereClause(filter?: string): FindOptionsWhere<CmsPages> | FindOptionsWhere<CmsPages>[];
    protected findById(id: string, relations?: string[], throwError?: boolean): Promise<CmsPages | null>;
    protected findByBusinessCode(businessCode: string, throwError?: boolean): Promise<CmsPages | null>;
    protected findBySlug(slug: string, throwError?: boolean): Promise<CmsPages | null>;
    protected isSlugExists(slug: string, excludeId?: string): Promise<boolean>;
    protected toDto(page: CmsPages | null): CmsPageDto | null;
    protected toDtos(pages: CmsPages[]): CmsPageDto[];
    protected isPagePublished(page: CmsPages): boolean;
    protected isPageDraft(page: CmsPages): boolean;
    protected generateSlugFromTitle(title: string): string;
    protected generateUniqueSlug(title: string, excludeId?: string): Promise<string>;
    protected getAvailableTemplates(): string[];
    protected isValidTemplate(template: string): boolean;
}

import { Injectable } from '@nestjs/common';
import { VnpayPaymentService } from '../services/vnpay-payment.service';
import { 
  PaymentRequest, 
  PaymentEventHandler,
} from '../interfaces/payment-integration.interface';

/**
 * Example usage of Portable VNPAY Payment Gateway
 * This demonstrates how to integrate the portable VNPAY module into your application
 */
@Injectable()
export class VnpayPortableUsageExample {
  constructor(private readonly vnpayPaymentService: VnpayPaymentService) {
    // Set up event handler for payment notifications
    this.setupEventHandler();
  }

  /**
   * Setup event handler to receive payment notifications
   */
  private setupEventHandler(): void {
    const eventHandler: PaymentEventHandler = {
      onPaymentCreated: async (data) => {
        console.log('🎯 Payment Created:', data);
        // TODO: Send email notification
        // TODO: Log to analytics
        // TODO: Update user interface
      },

      onPaymentSuccess: async (data) => {
        console.log('✅ Payment Success:', data);
        // TODO: Update user wallet balance
        // TODO: Update user tier/level
        // TODO: Send success notification
        // TODO: Trigger business logic (unlock features, etc.)
        
        // Example: Update wallet balance
        await this.updateWalletBalance(data.externalRef, data.amount);
      },

      onPaymentFailed: async (data) => {
        console.log('❌ Payment Failed:', data);
        // TODO: Log failure reason
        // TODO: Send failure notification
        // TODO: Trigger retry logic if applicable
      },

      onPaymentCancelled: async (data) => {
        console.log('🚫 Payment Cancelled:', data);
        // TODO: Handle cancellation
      },

      onPaymentExpired: async (data) => {
        console.log('⏰ Payment Expired:', data);
        // TODO: Handle expiration
      },
    };

    this.vnpayPaymentService.setEventHandler(eventHandler);
  }

  /**
   * Example: Create payment for user wallet deposit
   */
  async createWalletDepositPayment(
    userId: string, 
    amount: number, 
    clientIp: string = '127.0.0.1'
  ): Promise<string> {
    try {
      const paymentRequest: PaymentRequest = {
        externalRef: userId, // Use user ID as external reference
        amount: amount,
        description: `Nạp ${amount.toLocaleString()} VND vào ví`,
        clientIp: clientIp,
        bankCode: 'VNPAYQR', // Optional: for QR code payment
        locale: 'vn',
        metadata: {
          type: 'wallet_deposit',
          userId: userId,
          timestamp: new Date().toISOString(),
        },
      };

      const result = await this.vnpayPaymentService.createPayment(paymentRequest);
      
      console.log('💳 Payment created successfully:');
      console.log('  - Payment URL:', result.paymentUrl);
      console.log('  - Merchant Ref:', result.merchantTxnRef);
      console.log('  - Transaction ID:', result.transactionId);
      console.log('  - Expires At:', result.expiresAt);
      
      return result.paymentUrl;
    } catch (error) {
      console.error('❌ Error creating payment:', error.message);
      throw error;
    }
  }

  /**
   * Example: Create payment for order purchase
   */
  async createOrderPayment(
    userId: string,
    orderId: string,
    amount: number,
    orderDescription: string,
    clientIp: string
  ): Promise<string> {
    try {
      const paymentRequest: PaymentRequest = {
        externalRef: orderId, // Use order ID as external reference
        amount: amount,
        description: orderDescription,
        clientIp: clientIp,
        locale: 'vn',
        metadata: {
          type: 'order_payment',
          userId: userId,
          orderId: orderId,
          timestamp: new Date().toISOString(),
        },
      };

      const result = await this.vnpayPaymentService.createPayment(paymentRequest);
      
      console.log(`🛒 Order payment created for order ${orderId}:`, result.paymentUrl);
      return result.paymentUrl;
    } catch (error) {
      console.error('❌ Error creating order payment:', error.message);
      throw error;
    }
  }

  /**
   * Example: Query transaction status
   */
  async queryTransactionStatus(merchantTxnRef: string): Promise<void> {
    try {
      const transaction = await this.vnpayPaymentService.getTransaction(merchantTxnRef);
      
      if (transaction) {
        console.log('🔍 Transaction found:');
        console.log('  - Status:', transaction.status);
        console.log('  - Amount:', transaction.getFormattedAmount());
        console.log('  - External Ref:', transaction.externalRef);
        console.log('  - Created:', transaction.createdAt);
        console.log('  - Processed:', transaction.processedAt);
        
        if (transaction.isSuccess()) {
          console.log('  - VNPAY Txn No:', transaction.vnpayTxnNo);
          console.log('  - Bank Code:', transaction.bankCode);
          console.log('  - Pay Date:', transaction.vnpayPayDate);
        }
      } else {
        console.log('❌ Transaction not found');
      }
    } catch (error) {
      console.error('❌ Error querying transaction:', error.message);
      throw error;
    }
  }

  /**
   * Example: Get payment statistics
   */
  async getPaymentStatistics(userId?: string): Promise<void> {
    try {
      const filter = userId ? { externalRef: userId } : undefined;
      const stats = await this.vnpayPaymentService.getStatistics(filter);
      
      console.log('📊 Payment Statistics:');
      console.log('  - Total Transactions:', stats.totalTransactions);
      console.log('  - Successful:', stats.successfulTransactions);
      console.log('  - Failed:', stats.failedTransactions);
      console.log('  - Pending:', stats.pendingTransactions);
      console.log('  - Success Rate:', `${stats.successRate}%`);
      console.log('  - Total Amount:', `${stats.totalAmount.toLocaleString()} VND`);
      console.log('  - Successful Amount:', `${stats.successfulAmount.toLocaleString()} VND`);
    } catch (error) {
      console.error('❌ Error getting statistics:', error.message);
      throw error;
    }
  }

  /**
   * Example: Complete payment flow demonstration
   */
  async demonstrateCompleteFlow(): Promise<void> {
    console.log('\n🚀 === VNPAY Portable Payment Flow Demo ===\n');
    
    try {
      // Step 1: Create payment
      console.log('1️⃣ Creating wallet deposit payment...');
      const paymentUrl = await this.createWalletDepositPayment('user-123', 100000);
      console.log('✅ Payment URL created\n');
      
      // Step 2: Simulate payment completion (in real app, user pays on VNPAY)
      console.log('2️⃣ User completes payment on VNPAY...');
      console.log('✅ Payment completed (simulated)\n');
      
      // Step 3: Get statistics
      console.log('3️⃣ Getting payment statistics...');
      await this.getPaymentStatistics('user-123');
      console.log('✅ Statistics retrieved\n');
      
      console.log('🎉 === Demo Complete ===\n');
      
    } catch (error) {
      console.error('❌ Demo failed:', error.message);
    }
  }

  /**
   * Example: Business logic integration
   */
  private async updateWalletBalance(userId: string, amount: number): Promise<void> {
    // This is where you integrate with your existing business logic
    console.log(`💰 Updating wallet balance for user ${userId}: +${amount.toLocaleString()} VND`);
    
    // TODO: Implement your wallet update logic here
    // Example:
    // await this.walletService.addBalance(userId, amount);
    // await this.transactionService.createDepositRecord(userId, amount);
    // await this.notificationService.sendDepositSuccess(userId, amount);
  }

  /**
   * Example: Error handling and retry logic
   */
  async handlePaymentWithRetry(
    userId: string, 
    amount: number, 
    maxRetries: number = 3
  ): Promise<string> {
    let lastError: Error;
    
    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        console.log(`🔄 Payment attempt ${attempt}/${maxRetries}`);
        return await this.createWalletDepositPayment(userId, amount);
      } catch (error) {
        lastError = error;
        console.log(`❌ Attempt ${attempt} failed:`, error.message);
        
        if (attempt < maxRetries) {
          const delay = Math.pow(2, attempt) * 1000; // Exponential backoff
          console.log(`⏳ Waiting ${delay}ms before retry...`);
          await new Promise(resolve => setTimeout(resolve, delay));
        }
      }
    }
    
    throw new Error(`Payment failed after ${maxRetries} attempts: ${lastError.message}`);
  }
}

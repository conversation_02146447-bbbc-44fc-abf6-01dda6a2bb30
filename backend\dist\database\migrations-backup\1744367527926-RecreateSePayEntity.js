"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.RecreateSePayEntity174********** = void 0;
class RecreateSePayEntity174********** {
    async up(queryRunner) {
        await queryRunner.query(`
            DROP TABLE IF EXISTS "sepay_transactions" CASCADE
        `);
        await queryRunner.query(`
            CREATE TABLE "sepay_transactions" (
                "id" UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
                "sepay_id" INTEGER NOT NULL,
                "gateway" VARCHAR(100) NOT NULL,
                "transaction_date" TIMESTAMP NOT NULL,
                "account_number" VARCHAR(100) NULL,
                "code" VARCHAR(100) NULL,
                "content" TEXT NULL,
                "transfer_type" VARCHAR(10) NOT NULL,
                "transfer_amount" DECIMAL(20,2) NOT NULL,
                "accumulated" DECIMAL(20,2) NOT NULL,
                "sub_account" VARCHAR(100) NULL,
                "reference_code" VARCHAR(100) NULL,
                "description" TEXT NULL,
                "status" VARCHAR(50) NOT NULL DEFAULT 'PENDING',
                "notes" TEXT NULL,
                "created_by" VARCHAR(100) NULL,
                "updated_by" VARCHAR(100) NULL,
                "created_at" TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
                "updated_at" TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
                "payment_transaction_id" UUID NULL
            )
        `);
        await queryRunner.query(`
            ALTER TABLE "sepay_transactions" 
            ADD CONSTRAINT "fk_sepay_transactions_payment_transaction" 
            FOREIGN KEY ("payment_transaction_id") 
            REFERENCES "payment_transactions"("id") 
            ON DELETE SET NULL
        `);
        await queryRunner.query(`
            CREATE INDEX "idx_sepay_transactions_sepay_id" 
            ON "sepay_transactions" ("sepay_id")
        `);
        await queryRunner.query(`
            CREATE INDEX "idx_sepay_transactions_payment_transaction_id" 
            ON "sepay_transactions" ("payment_transaction_id")
        `);
    }
    async down(queryRunner) {
        await queryRunner.query(`
            DROP INDEX IF EXISTS "idx_sepay_transactions_sepay_id"
        `);
        await queryRunner.query(`
            DROP INDEX IF EXISTS "idx_sepay_transactions_payment_transaction_id"
        `);
        await queryRunner.query(`
            ALTER TABLE "sepay_transactions" 
            DROP CONSTRAINT IF EXISTS "fk_sepay_transactions_payment_transaction"
        `);
        await queryRunner.query(`
            DROP TABLE IF EXISTS "sepay_transactions"
        `);
        await queryRunner.query(`
            CREATE TABLE "sepay_transactions" (
                "id" SERIAL PRIMARY KEY,
                "gateway" VARCHAR(100) NOT NULL,
                "transaction_date" TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
                "account_number" VARCHAR(100) NULL,
                "sub_account" VARCHAR(250) NULL,
                "amount_in" DECIMAL(20,2) NOT NULL DEFAULT 0.00,
                "amount_out" DECIMAL(20,2) NOT NULL DEFAULT 0.00,
                "accumulated" DECIMAL(20,2) NOT NULL DEFAULT 0.00,
                "code" VARCHAR(250) NULL,
                "transaction_content" TEXT NULL,
                "reference_number" VARCHAR(255) NULL,
                "body" TEXT NULL,
                "created_at" TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
                "payment_transaction_id" UUID NULL
            )
        `);
        await queryRunner.query(`
            ALTER TABLE "sepay_transactions" 
            ADD CONSTRAINT "fk_sepay_transactions_payment_transaction" 
            FOREIGN KEY ("payment_transaction_id") 
            REFERENCES "payment_transactions"("id") 
            ON DELETE CASCADE
        `);
        await queryRunner.query(`
            CREATE INDEX "idx_sepay_transactions_payment_transaction_id" 
            ON "sepay_transactions" ("payment_transaction_id")
        `);
    }
}
exports.RecreateSePayEntity174********** = RecreateSePayEntity174**********;
//# sourceMappingURL=174**********-RecreateSePayEntity.js.map
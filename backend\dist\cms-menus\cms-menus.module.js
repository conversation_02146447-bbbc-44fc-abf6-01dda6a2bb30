"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CmsMenusModule = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const cms_menus_entity_1 = require("./entity/cms-menus.entity");
const services_1 = require("./services");
const controllers_1 = require("./controllers");
let CmsMenusModule = class CmsMenusModule {
};
exports.CmsMenusModule = CmsMenusModule;
exports.CmsMenusModule = CmsMenusModule = __decorate([
    (0, common_1.Module)({
        imports: [
            typeorm_1.TypeOrmModule.forFeature([cms_menus_entity_1.CmsMenus]),
        ],
        controllers: [
            controllers_1.CreateCmsMenusController,
            controllers_1.ReadCmsMenusController,
            controllers_1.UpdateCmsMenusController,
            controllers_1.DeleteCmsMenusController,
        ],
        providers: [
            services_1.BaseCmsMenusService,
            services_1.CreateCmsMenusService,
            services_1.ReadCmsMenusService,
            services_1.UpdateCmsMenusService,
            services_1.DeleteCmsMenusService,
        ],
        exports: [
            services_1.BaseCmsMenusService,
            services_1.CreateCmsMenusService,
            services_1.ReadCmsMenusService,
            services_1.UpdateCmsMenusService,
            services_1.DeleteCmsMenusService,
        ],
    })
], CmsMenusModule);
//# sourceMappingURL=cms-menus.module.js.map
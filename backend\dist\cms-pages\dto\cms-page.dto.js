"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CmsPageDto = void 0;
const openapi = require("@nestjs/swagger");
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
const class_transformer_1 = require("class-transformer");
const user_dto_1 = require("../../users/dto/user.dto");
const cms_pages_entity_1 = require("../entity/cms-pages.entity");
class CmsPageDto {
    id;
    businessCode;
    title;
    slug;
    content;
    template;
    status;
    metaTitle;
    metaDescription;
    metaKeywords;
    createdAt;
    updatedAt;
    createdBy;
    updatedBy;
    deletedBy;
    isDeleted;
    deletedAt;
    creator;
    updater;
    deleter;
    seoTitle;
    seoDescription;
    seoKeywordsArray;
    usedTemplate;
    fullUrl;
    excerpt;
    wordCount;
    estimatedReadingTime;
    static _OPENAPI_METADATA_FACTORY() {
        return { id: { required: true, type: () => String, format: "uuid" }, businessCode: { required: true, type: () => String }, title: { required: true, type: () => String }, slug: { required: true, type: () => String }, content: { required: true, type: () => String }, template: { required: false, type: () => String, nullable: true }, status: { required: true, enum: require("../entity/cms-pages.entity").CmsPageStatus }, metaTitle: { required: false, type: () => String, nullable: true }, metaDescription: { required: false, type: () => String, nullable: true }, metaKeywords: { required: false, type: () => String, nullable: true }, createdAt: { required: true, type: () => Date }, updatedAt: { required: true, type: () => Date }, createdBy: { required: false, type: () => String, format: "uuid" }, updatedBy: { required: false, type: () => String, format: "uuid" }, deletedBy: { required: false, type: () => String, format: "uuid" }, isDeleted: { required: true, type: () => Boolean }, deletedAt: { required: false, type: () => Date }, creator: { required: false, type: () => require("../../users/dto/user.dto").UserDto }, updater: { required: false, type: () => require("../../users/dto/user.dto").UserDto }, deleter: { required: false, type: () => require("../../users/dto/user.dto").UserDto }, seoTitle: { required: false, type: () => String }, seoDescription: { required: false, type: () => String }, seoKeywordsArray: { required: false, type: () => [String] }, usedTemplate: { required: false, type: () => String }, fullUrl: { required: false, type: () => String }, excerpt: { required: false, type: () => String }, wordCount: { required: false, type: () => Number }, estimatedReadingTime: { required: false, type: () => Number } };
    }
}
exports.CmsPageDto = CmsPageDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'ID của trang',
        example: '550e8400-e29b-41d4-a716-************',
    }),
    (0, class_transformer_1.Expose)(),
    (0, class_validator_1.IsUUID)('4'),
    __metadata("design:type", String)
], CmsPageDto.prototype, "id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Mã kinh doanh của trang',
        example: 'CMP-240101-00001',
    }),
    (0, class_transformer_1.Expose)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CmsPageDto.prototype, "businessCode", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Tiêu đề trang',
        example: 'Giới thiệu về công ty',
    }),
    (0, class_transformer_1.Expose)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CmsPageDto.prototype, "title", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Chuỗi URL thân thiện',
        example: 'gioi-thieu-ve-cong-ty',
    }),
    (0, class_transformer_1.Expose)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CmsPageDto.prototype, "slug", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Nội dung đầy đủ của trang (HTML)',
        example: '<h1>Giới thiệu</h1><p>Nội dung giới thiệu về công ty...</p>',
    }),
    (0, class_transformer_1.Expose)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CmsPageDto.prototype, "content", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Tên file template sử dụng cho trang này',
        example: 'default',
    }),
    (0, class_transformer_1.Expose)(),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", Object)
], CmsPageDto.prototype, "template", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Trạng thái trang',
        example: cms_pages_entity_1.CmsPageStatus.PUBLISHED,
        enum: cms_pages_entity_1.CmsPageStatus,
    }),
    (0, class_transformer_1.Expose)(),
    (0, class_validator_1.IsEnum)(cms_pages_entity_1.CmsPageStatus),
    __metadata("design:type", String)
], CmsPageDto.prototype, "status", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Tiêu đề SEO',
        example: 'Giới thiệu về công ty - Trang chủ',
    }),
    (0, class_transformer_1.Expose)(),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", Object)
], CmsPageDto.prototype, "metaTitle", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Mô tả SEO',
        example: 'Tìm hiểu về lịch sử, tầm nhìn và sứ mệnh của công ty chúng tôi.',
    }),
    (0, class_transformer_1.Expose)(),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", Object)
], CmsPageDto.prototype, "metaDescription", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Từ khóa SEO',
        example: 'giới thiệu, công ty, lịch sử, tầm nhìn',
    }),
    (0, class_transformer_1.Expose)(),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", Object)
], CmsPageDto.prototype, "metaKeywords", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Thời gian tạo',
        example: '2023-01-01T00:00:00.000Z',
    }),
    (0, class_transformer_1.Expose)(),
    (0, class_validator_1.IsDate)(),
    __metadata("design:type", Date)
], CmsPageDto.prototype, "createdAt", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Thời gian cập nhật',
        example: '2023-01-01T00:00:00.000Z',
    }),
    (0, class_transformer_1.Expose)(),
    (0, class_validator_1.IsDate)(),
    __metadata("design:type", Date)
], CmsPageDto.prototype, "updatedAt", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'ID người tạo',
        example: '550e8400-e29b-41d4-a716-************',
    }),
    (0, class_transformer_1.Expose)(),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsUUID)('4'),
    __metadata("design:type", String)
], CmsPageDto.prototype, "createdBy", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'ID người cập nhật',
        example: '550e8400-e29b-41d4-a716-************',
    }),
    (0, class_transformer_1.Expose)(),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsUUID)('4'),
    __metadata("design:type", String)
], CmsPageDto.prototype, "updatedBy", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'ID người xóa',
        example: '550e8400-e29b-41d4-a716-************',
    }),
    (0, class_transformer_1.Expose)(),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsUUID)('4'),
    __metadata("design:type", String)
], CmsPageDto.prototype, "deletedBy", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Trạng thái xóa',
        example: false,
    }),
    (0, class_transformer_1.Expose)(),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], CmsPageDto.prototype, "isDeleted", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Thời gian xóa',
        example: '2023-01-01T00:00:00.000Z',
    }),
    (0, class_transformer_1.Expose)(),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsDate)(),
    __metadata("design:type", Date)
], CmsPageDto.prototype, "deletedAt", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Thông tin người tạo',
        type: () => user_dto_1.UserDto,
    }),
    (0, class_transformer_1.Expose)(),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => user_dto_1.UserDto),
    __metadata("design:type", user_dto_1.UserDto)
], CmsPageDto.prototype, "creator", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Thông tin người cập nhật',
        type: () => user_dto_1.UserDto,
    }),
    (0, class_transformer_1.Expose)(),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => user_dto_1.UserDto),
    __metadata("design:type", user_dto_1.UserDto)
], CmsPageDto.prototype, "updater", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Thông tin người xóa',
        type: () => user_dto_1.UserDto,
    }),
    (0, class_transformer_1.Expose)(),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => user_dto_1.UserDto),
    __metadata("design:type", user_dto_1.UserDto)
], CmsPageDto.prototype, "deleter", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Tiêu đề SEO (computed)',
        example: 'Giới thiệu về công ty - Trang chủ',
    }),
    (0, class_transformer_1.Expose)(),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CmsPageDto.prototype, "seoTitle", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Mô tả SEO (computed)',
        example: 'Tìm hiểu về lịch sử, tầm nhìn và sứ mệnh của công ty chúng tôi.',
    }),
    (0, class_transformer_1.Expose)(),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CmsPageDto.prototype, "seoDescription", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Từ khóa SEO dưới dạng mảng',
        example: ['giới thiệu', 'công ty', 'lịch sử', 'tầm nhìn'],
    }),
    (0, class_transformer_1.Expose)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Array)
], CmsPageDto.prototype, "seoKeywordsArray", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Template được sử dụng',
        example: 'default',
    }),
    (0, class_transformer_1.Expose)(),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CmsPageDto.prototype, "usedTemplate", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'URL đầy đủ của trang',
        example: '/gioi-thieu-ve-cong-ty',
    }),
    (0, class_transformer_1.Expose)(),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CmsPageDto.prototype, "fullUrl", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Excerpt từ content',
        example: 'Giới thiệu về lịch sử, tầm nhìn và sứ mệnh của công ty...',
    }),
    (0, class_transformer_1.Expose)(),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CmsPageDto.prototype, "excerpt", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Số từ trong content',
        example: 1250,
    }),
    (0, class_transformer_1.Expose)(),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], CmsPageDto.prototype, "wordCount", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Thời gian đọc ước tính (phút)',
        example: 7,
    }),
    (0, class_transformer_1.Expose)(),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], CmsPageDto.prototype, "estimatedReadingTime", void 0);
//# sourceMappingURL=cms-page.dto.js.map
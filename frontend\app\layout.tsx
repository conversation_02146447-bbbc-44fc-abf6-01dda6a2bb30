import { WebSocketProvider } from "@/components/providers/WebSocketProvider";
import { AppLoadingProvider } from "@/components/providers/app-loading-provider";
import { Toaster } from "@/components/ui/sonner";
import { AuthProvider } from "@/hooks/use-auth";
import { DynamicMetadata } from "@/components/dynamic-metadata";
import { siteMetadataService } from "@/lib/system-metadata";
import "@/styles/sticky-columns.css";
import type { Metadata } from "next";
import { <PERSON>eist, Geist_Mono } from "next/font/google";
import "./globals.css";

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

// Dynamic metadata will be handled by DynamicMetadata component
// This is just a fallback for SSR
export async function generateMetadata(): Promise<Metadata> {
  try {
    // Try to get metadata from service (will use default if API fails)
    const config = await siteMetadataService.getMetadata();

    return {
      title: config['website_seo_title'] || 'PHYGITAL-V Platform',
      description: config['website_seo_description'] || 'Digital Gold Exchange Platform',
      keywords: config['website_seo_keywords'] || undefined,
      authors: config['website_seo_author'] ? [{ name: config['website_seo_author'] }] : undefined,
      creator: config['website_seo_author'] || undefined,
      publisher: config['website_company_name'] || undefined,

      icons: {
        icon: '/favicon_sgs.ico',
        apple: config['website_apple_touch_icon'] || undefined,
      },

      manifest: config['website_manifest_url'] || undefined,

      openGraph: {
        title: config['website_seo_og_title'] || config['website_seo_title'] || 'PHYGITAL-V Platform',
        description: config['website_seo_og_description'] || config['website_seo_description'] || 'Digital Gold Exchange Platform',
        url: config['website_site_url'] || undefined,
        siteName: config['website_site_name'] || undefined,
        images: config['website_seo_og_image'] ? [
          {
            url: config['website_seo_og_image'],
            width: 1200,
            height: 630,
            alt: config['website_seo_og_title'] || config['website_seo_title'] || 'PHYGITAL-V Platform',
          },
        ] : undefined,
        locale: config['website_locale'] || undefined,
        type: 'website',
      },

      twitter: {
        card: 'summary_large_image',
        title: config['website_seo_og_title'] || config['website_seo_title'] || 'PHYGITAL-V Platform',
        description: config['website_seo_og_description'] || config['website_seo_description'] || 'Digital Gold Exchange Platform',
        site: config['website_seo_twitter_site'] || undefined,
        creator: config['website_seo_twitter_creator'] || undefined,
        images: config['website_seo_twitter_image'] ? [config['website_seo_twitter_image']] : undefined,
      },

      robots: {
        index: true,
        follow: true,
        googleBot: {
          index: true,
          follow: true,
          'max-video-preview': -1,
          'max-image-preview': 'large',
          'max-snippet': -1,
        },
      },

      verification: {
        google: process.env.NEXT_PUBLIC_GOOGLE_VERIFICATION,
      },
    };
  } catch (error) {
    console.error('Failed to generate metadata:', error);

    // Fallback metadata - minimal defaults
    return {
      title: "Loading...",
      description: "Please wait while we load the site information.",
      icons: {
        icon: "/favicon.ico",
      },
    };
  }
}

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased`}
      >
        <AuthProvider>
          <AppLoadingProvider>
            <WebSocketProvider>
              <DynamicMetadata />
              {children}
              <Toaster richColors />
            </WebSocketProvider>
          </AppLoadingProvider>
        </AuthProvider>
      </body>
    </html>
  );
}

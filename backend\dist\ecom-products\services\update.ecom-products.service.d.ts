import { Repository, DataSource } from 'typeorm';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { BaseEcomProductsService } from './base.ecom-products.service';
import { EcomProductDto } from '../dto/ecom-product.dto';
import { UpdateEcomProductDto } from '../dto/update-ecom-product.dto';
import { ReadEcomProductsService } from './read.ecom-products.service';
import { EcomProduct } from '../entity/ecom-products.entity';
export declare class UpdateEcomProductsService extends BaseEcomProductsService {
    protected readonly ecomProductRepository: Repository<EcomProduct>;
    protected readonly dataSource: DataSource;
    protected readonly eventEmitter: EventEmitter2;
    private readonly readEcomProductsService;
    constructor(ecomProductRepository: Repository<EcomProduct>, dataSource: DataSource, eventEmitter: EventEmitter2, readEcomProductsService: ReadEcomProductsService);
    update(productId: string, updateEcomProductDto: UpdateEcomProductDto, userId?: string): Promise<EcomProductDto | null>;
    updateStock(id: string, quantity: number, userId?: string): Promise<EcomProductDto>;
    updateStatus(id: string, isActive: boolean, userId?: string): Promise<EcomProductDto>;
    bulkUpdate(updateEcomProductDtos: UpdateEcomProductDto[], userId?: string): Promise<EcomProductDto[]>;
    restore(id: string, userId?: string): Promise<EcomProductDto | null>;
}

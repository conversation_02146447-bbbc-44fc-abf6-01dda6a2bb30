"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.EcomProductsPublicController = void 0;
const openapi = require("@nestjs/swagger");
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const public_ecom_products_service_1 = require("../services/public.ecom-products.service");
const ecom_product_public_dto_1 = require("../dto/ecom-product.public.dto");
const custom_pagination_query_dto_1 = require("../../common/dto/custom-pagination-query.dto");
const api_response_dto_1 = require("../../common/dto/api-response.dto");
const pagination_response_dto_1 = require("../../common/dto/pagination-response.dto");
const custom_pagination_meta_dto_1 = require("../../common/dto/custom-pagination-meta.dto");
const public_decorator_1 = require("../../common/decorators/public.decorator");
let EcomProductsPublicController = class EcomProductsPublicController {
    ecomProductsService;
    constructor(ecomProductsService) {
        this.ecomProductsService = ecomProductsService;
    }
    async getHomepageSections(productsPerCategory) {
        const productsLimit = productsPerCategory && productsPerCategory > 0 ? productsPerCategory : 4;
        return this.ecomProductsService.findHomepageSections(productsLimit);
    }
    async findAll(paginationQuery) {
        const { data, total } = await this.ecomProductsService.findAll(paginationQuery);
        const meta = new custom_pagination_meta_dto_1.CustomPageMetaDto({
            pageQueryDto: paginationQuery,
            itemCount: total,
        });
        return new pagination_response_dto_1.PaginationResponseDto(data, meta);
    }
    async findById(id) {
        return this.ecomProductsService.findById(id);
    }
    async findByCode(code) {
        return this.ecomProductsService.findByCode(code);
    }
    async findBySlug(slug) {
        return this.ecomProductsService.findBySlug(slug);
    }
    async findByCategory(categorySlug, paginationQuery) {
        const { data, total } = await this.ecomProductsService.findByCategory(categorySlug, paginationQuery);
        const meta = new custom_pagination_meta_dto_1.CustomPageMetaDto({
            pageQueryDto: paginationQuery,
            itemCount: total,
        });
        return new pagination_response_dto_1.PaginationResponseDto(data, meta);
    }
};
exports.EcomProductsPublicController = EcomProductsPublicController;
__decorate([
    (0, common_1.Get)('homepage-sections'),
    (0, public_decorator_1.Public)(),
    (0, swagger_1.ApiOperation)({ summary: 'Lấy dữ liệu các section trang chủ (tất cả danh mục có sản phẩm với thứ tự ngẫu nhiên)' }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Dữ liệu các section trang chủ. Sản phẩm trong mỗi danh mục được randomize mỗi lần gọi API.',
        type: ecom_product_public_dto_1.HomepageSectionsResponseDto,
    }),
    (0, swagger_1.ApiQuery)({
        name: 'productsPerCategory',
        required: false,
        type: Number,
        description: 'Số lượng sản phẩm mỗi danh mục (mặc định 4)',
        example: 4
    }),
    openapi.ApiResponse({ status: 200, type: require("../dto/ecom-product.public.dto").HomepageSectionsResponseDto }),
    __param(0, (0, common_1.Query)('productsPerCategory')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", Promise)
], EcomProductsPublicController.prototype, "getHomepageSections", null);
__decorate([
    (0, common_1.Get)(),
    (0, public_decorator_1.Public)(),
    (0, swagger_1.ApiOperation)({ summary: 'Lấy danh sách sản phẩm công khai với phân trang và lọc' }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Danh sách sản phẩm công khai.',
        type: api_response_dto_1.ApiResponseDto,
        schema: {
            properties: {
                data: {
                    type: 'array',
                    items: { $ref: '#/components/schemas/EcomProductPublicDto' },
                },
                meta: {
                    type: 'object',
                    properties: {
                        total: { type: 'number' },
                        page: { type: 'number' },
                        limit: { type: 'number' },
                        totalPages: { type: 'number' },
                    },
                },
            },
        },
    }),
    (0, swagger_1.ApiQuery)({ name: 'page', required: false, type: Number, description: 'Số trang' }),
    (0, swagger_1.ApiQuery)({ name: 'limit', required: false, type: Number, description: 'Số lượng mục trên mỗi trang' }),
    (0, swagger_1.ApiQuery)({
        name: 'sort',
        required: false,
        description: 'Sắp xếp theo định dạng field:order (ví dụ: createdAt:DESC,productName:ASC)',
    }),
    (0, swagger_1.ApiQuery)({ name: 'filter', required: false, type: String, description: 'Lọc theo điều kiện' }),
    (0, swagger_1.ApiQuery)({ name: 'search', required: false, type: String, description: 'Tìm kiếm theo từ khóa' }),
    openapi.ApiResponse({ status: 200 }),
    __param(0, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [custom_pagination_query_dto_1.CustomPaginationQueryDto]),
    __metadata("design:returntype", Promise)
], EcomProductsPublicController.prototype, "findAll", null);
__decorate([
    (0, common_1.Get)(':id'),
    (0, public_decorator_1.Public)(),
    (0, swagger_1.ApiOperation)({ summary: 'Lấy thông tin sản phẩm công khai theo ID' }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Thông tin sản phẩm công khai.',
        type: api_response_dto_1.ApiResponseDto,
        schema: {
            properties: {
                data: { $ref: '#/components/schemas/EcomProductPublicDto' },
            },
        },
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.NOT_FOUND,
        description: 'Không tìm thấy sản phẩm.',
        type: api_response_dto_1.ApiResponseDto,
        schema: { properties: { data: { type: 'object', nullable: true } } },
    }),
    (0, swagger_1.ApiParam)({ name: 'id', type: String, description: 'ID của sản phẩm' }),
    openapi.ApiResponse({ status: 200, type: require("../dto/ecom-product.public.dto").EcomProductPublicDto }),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], EcomProductsPublicController.prototype, "findById", null);
__decorate([
    (0, common_1.Get)('code/:code'),
    (0, public_decorator_1.Public)(),
    (0, swagger_1.ApiOperation)({ summary: 'Lấy thông tin sản phẩm công khai theo mã sản phẩm' }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Thông tin sản phẩm công khai.',
        type: api_response_dto_1.ApiResponseDto,
        schema: {
            properties: {
                data: { $ref: '#/components/schemas/EcomProductPublicDto' },
            },
        },
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.NOT_FOUND,
        description: 'Không tìm thấy sản phẩm.',
        type: api_response_dto_1.ApiResponseDto,
        schema: { properties: { data: { type: 'object', nullable: true } } },
    }),
    (0, swagger_1.ApiParam)({ name: 'code', type: String, description: 'Mã sản phẩm' }),
    openapi.ApiResponse({ status: 200, type: require("../dto/ecom-product.public.dto").EcomProductPublicDto }),
    __param(0, (0, common_1.Param)('code')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], EcomProductsPublicController.prototype, "findByCode", null);
__decorate([
    (0, common_1.Get)('slug/:slug'),
    (0, public_decorator_1.Public)(),
    (0, swagger_1.ApiOperation)({ summary: 'Lấy thông tin sản phẩm công khai theo slug' }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Thông tin sản phẩm công khai.',
        type: api_response_dto_1.ApiResponseDto,
        schema: {
            properties: {
                data: { $ref: '#/components/schemas/EcomProductPublicDto' },
            },
        },
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.NOT_FOUND,
        description: 'Không tìm thấy sản phẩm.',
        type: api_response_dto_1.ApiResponseDto,
        schema: { properties: { data: { type: 'object', nullable: true } } },
    }),
    (0, swagger_1.ApiParam)({ name: 'slug', type: String, description: 'Slug của sản phẩm (VD: nhan-bac-925-cao-cap)' }),
    openapi.ApiResponse({ status: 200, type: require("../dto/ecom-product.public.dto").EcomProductPublicDto }),
    __param(0, (0, common_1.Param)('slug')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], EcomProductsPublicController.prototype, "findBySlug", null);
__decorate([
    (0, common_1.Get)('category/:categorySlug'),
    (0, public_decorator_1.Public)(),
    (0, swagger_1.ApiOperation)({ summary: 'Lấy danh sách sản phẩm công khai theo slug danh mục' }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Danh sách sản phẩm công khai theo danh mục.',
        type: api_response_dto_1.ApiResponseDto,
        schema: {
            properties: {
                data: {
                    type: 'array',
                    items: { $ref: '#/components/schemas/EcomProductPublicDto' },
                },
                meta: {
                    type: 'object',
                    properties: {
                        total: { type: 'number' },
                        page: { type: 'number' },
                        limit: { type: 'number' },
                        totalPages: { type: 'number' },
                    },
                },
            },
        },
    }),
    (0, swagger_1.ApiParam)({ name: 'categorySlug', type: String, description: 'Slug của danh mục sản phẩm' }),
    (0, swagger_1.ApiQuery)({ name: 'page', required: false, type: Number, description: 'Số trang' }),
    (0, swagger_1.ApiQuery)({ name: 'limit', required: false, type: Number, description: 'Số lượng mục trên mỗi trang' }),
    (0, swagger_1.ApiQuery)({
        name: 'sort',
        required: false,
        description: 'Sắp xếp theo định dạng field:order (ví dụ: createdAt:DESC,productName:ASC)',
    }),
    openapi.ApiResponse({ status: 200 }),
    __param(0, (0, common_1.Param)('categorySlug')),
    __param(1, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, custom_pagination_query_dto_1.CustomPaginationQueryDto]),
    __metadata("design:returntype", Promise)
], EcomProductsPublicController.prototype, "findByCategory", null);
exports.EcomProductsPublicController = EcomProductsPublicController = __decorate([
    (0, swagger_1.ApiTags)('ecom-products-public'),
    (0, common_1.Controller)('ecom-products/public'),
    (0, public_decorator_1.Public)(),
    __metadata("design:paramtypes", [public_ecom_products_service_1.PublicEcomProductsService])
], EcomProductsPublicController);
//# sourceMappingURL=ecom-products.public.controller.js.map
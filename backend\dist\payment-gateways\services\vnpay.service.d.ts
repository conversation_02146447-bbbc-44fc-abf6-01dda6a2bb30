import { ConfigService } from '@nestjs/config';
import { HttpService } from '@nestjs/axios';
import { IPaymentGateway } from '../interfaces/payment-gateway.interface';
import { CreatePaymentDto } from '../dto/create-payment.dto';
export interface VnpayCreatePaymentParams {
    vnp_Version: string;
    vnp_Command: string;
    vnp_TmnCode: string;
    vnp_Amount: number;
    vnp_CurrCode: string;
    vnp_TxnRef: string;
    vnp_OrderInfo: string;
    vnp_OrderType: string;
    vnp_Locale: string;
    vnp_ReturnUrl: string;
    vnp_IpAddr: string;
    vnp_CreateDate: string;
    vnp_ExpireDate?: string;
    vnp_BankCode?: string;
}
export interface VnpayQueryRequest {
    vnp_RequestId: string;
    vnp_Version: string;
    vnp_Command: string;
    vnp_TmnCode: string;
    vnp_TxnRef: string;
    vnp_OrderInfo: string;
    vnp_TransactionDate: string;
    vnp_CreateDate: string;
    vnp_IpAddr: string;
    vnp_TransactionNo?: string;
    vnp_SecureHash: string;
}
export interface VnpayRefundRequest {
    vnp_RequestId: string;
    vnp_Version: string;
    vnp_Command: string;
    vnp_TmnCode: string;
    vnp_TransactionType: string;
    vnp_TxnRef: string;
    vnp_Amount: number;
    vnp_OrderInfo: string;
    vnp_TransactionNo?: string;
    vnp_TransactionDate: string;
    vnp_CreateBy: string;
    vnp_CreateDate: string;
    vnp_IpAddr: string;
    vnp_SecureHash: string;
}
export declare class VnpayService implements IPaymentGateway {
    private readonly configService;
    private readonly httpService;
    private readonly logger;
    private readonly tmnCode;
    private readonly hashSecret;
    private readonly vnpayUrl;
    private readonly vnpayApiUrl;
    private readonly returnUrl;
    private readonly ipnUrl;
    constructor(configService: ConfigService, httpService: HttpService);
    createPaymentUrl(paymentData: CreatePaymentDto): Promise<{
        paymentUrl: string;
        transactionId: string;
    }>;
    verifyReturnUrl(params: Record<string, string>): Promise<{
        isValid: boolean;
        transactionId?: string;
        amount?: number;
        responseCode?: string;
        transactionStatus?: string;
        message?: string;
        vnpayTransactionNo?: string;
        bankCode?: string;
        payDate?: string;
    }>;
    handleIpnCallback(params: Record<string, string>): Promise<{
        isValid: boolean;
        transactionId?: string;
        amount?: number;
        message?: string;
    }>;
    createIpnResponse(params: Record<string, string>): Promise<{
        RspCode: string;
        Message: string;
    }>;
    private formatDate;
    private sanitizeOrderInfo;
    private createSignData;
    private createSecureHash;
    private createQueryString;
    private sortObject;
    queryTransaction(queryData: {
        txnRef: string;
        transactionDate: string;
        transactionNo?: string;
        orderInfo: string;
        ipAddr: string;
    }): Promise<any>;
    refundTransaction(refundData: {
        txnRef: string;
        amount: number;
        orderInfo: string;
        transactionNo?: string;
        transactionDate: string;
        transactionType: '02' | '03';
        createBy: string;
        ipAddr: string;
    }): Promise<any>;
    private sendApiRequest;
    private generateRequestId;
    private getErrorMessage;
}

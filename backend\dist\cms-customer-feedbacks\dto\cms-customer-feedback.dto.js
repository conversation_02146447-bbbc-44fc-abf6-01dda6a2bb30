"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CmsCustomerFeedbackDto = void 0;
const openapi = require("@nestjs/swagger");
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
const class_transformer_1 = require("class-transformer");
const user_dto_1 = require("../../users/dto/user.dto");
const cms_customer_feedbacks_entity_1 = require("../entity/cms-customer-feedbacks.entity");
class CmsCustomerFeedbackDto {
    id;
    businessCode;
    customerName;
    customerTitleCompany;
    feedbackText;
    rating;
    avatarUrl;
    productServiceName;
    status;
    approvedBy;
    createdAt;
    updatedAt;
    createdBy;
    updatedBy;
    deletedBy;
    isDeleted;
    deletedAt;
    creator;
    updater;
    deleter;
    approver;
    ratingText;
    ratingColor;
    displayName;
    static _OPENAPI_METADATA_FACTORY() {
        return { id: { required: true, type: () => String, format: "uuid" }, businessCode: { required: true, type: () => String }, customerName: { required: true, type: () => String }, customerTitleCompany: { required: false, type: () => String, nullable: true }, feedbackText: { required: true, type: () => String }, rating: { required: false, type: () => Number, nullable: true, minimum: 1, maximum: 5 }, avatarUrl: { required: false, type: () => String, nullable: true, format: "uri" }, productServiceName: { required: false, type: () => String, nullable: true }, status: { required: true, enum: require("../entity/cms-customer-feedbacks.entity").CmsCustomerFeedbackStatus }, approvedBy: { required: false, type: () => String, nullable: true, format: "uuid" }, createdAt: { required: true, type: () => Date }, updatedAt: { required: true, type: () => Date }, createdBy: { required: false, type: () => String, format: "uuid" }, updatedBy: { required: false, type: () => String, format: "uuid" }, deletedBy: { required: false, type: () => String, format: "uuid" }, isDeleted: { required: true, type: () => Boolean }, deletedAt: { required: false, type: () => Date }, creator: { required: false, type: () => require("../../users/dto/user.dto").UserDto }, updater: { required: false, type: () => require("../../users/dto/user.dto").UserDto }, deleter: { required: false, type: () => require("../../users/dto/user.dto").UserDto }, approver: { required: false, type: () => require("../../users/dto/user.dto").UserDto }, ratingText: { required: false, type: () => String }, ratingColor: { required: false, type: () => String }, displayName: { required: false, type: () => String } };
    }
}
exports.CmsCustomerFeedbackDto = CmsCustomerFeedbackDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'ID của feedback',
        example: '550e8400-e29b-41d4-a716-************',
    }),
    (0, class_transformer_1.Expose)(),
    (0, class_validator_1.IsUUID)('4'),
    __metadata("design:type", String)
], CmsCustomerFeedbackDto.prototype, "id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Mã kinh doanh của feedback',
        example: 'CMF-240101-00001',
    }),
    (0, class_transformer_1.Expose)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CmsCustomerFeedbackDto.prototype, "businessCode", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Tên khách hàng',
        example: 'Nguyễn Văn A',
    }),
    (0, class_transformer_1.Expose)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CmsCustomerFeedbackDto.prototype, "customerName", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Chức danh, công ty của khách hàng',
        example: 'Giám đốc, Công ty ABC',
    }),
    (0, class_transformer_1.Expose)(),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", Object)
], CmsCustomerFeedbackDto.prototype, "customerTitleCompany", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Nội dung phản hồi',
        example: 'Dịch vụ rất tốt, tôi rất hài lòng với chất lượng sản phẩm.',
    }),
    (0, class_transformer_1.Expose)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CmsCustomerFeedbackDto.prototype, "feedbackText", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Đánh giá sao (1-5)',
        example: 5,
        minimum: 1,
        maximum: 5,
    }),
    (0, class_transformer_1.Expose)(),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsInt)(),
    (0, class_validator_1.Min)(1),
    (0, class_validator_1.Max)(5),
    __metadata("design:type", Object)
], CmsCustomerFeedbackDto.prototype, "rating", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'URL ảnh đại diện của khách hàng',
        example: 'https://example.com/avatar.jpg',
    }),
    (0, class_transformer_1.Expose)(),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsUrl)(),
    __metadata("design:type", Object)
], CmsCustomerFeedbackDto.prototype, "avatarUrl", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Tên sản phẩm/dịch vụ được đánh giá',
        example: 'Dịch vụ mua bán vàng',
    }),
    (0, class_transformer_1.Expose)(),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", Object)
], CmsCustomerFeedbackDto.prototype, "productServiceName", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Trạng thái feedback',
        example: cms_customer_feedbacks_entity_1.CmsCustomerFeedbackStatus.APPROVED,
        enum: cms_customer_feedbacks_entity_1.CmsCustomerFeedbackStatus,
    }),
    (0, class_transformer_1.Expose)(),
    (0, class_validator_1.IsEnum)(cms_customer_feedbacks_entity_1.CmsCustomerFeedbackStatus),
    __metadata("design:type", String)
], CmsCustomerFeedbackDto.prototype, "status", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'ID người duyệt feedback',
        example: '550e8400-e29b-41d4-a716-************',
    }),
    (0, class_transformer_1.Expose)(),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsUUID)('4'),
    __metadata("design:type", Object)
], CmsCustomerFeedbackDto.prototype, "approvedBy", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Thời gian tạo',
        example: '2023-01-01T00:00:00.000Z',
    }),
    (0, class_transformer_1.Expose)(),
    (0, class_validator_1.IsDate)(),
    __metadata("design:type", Date)
], CmsCustomerFeedbackDto.prototype, "createdAt", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Thời gian cập nhật',
        example: '2023-01-01T00:00:00.000Z',
    }),
    (0, class_transformer_1.Expose)(),
    (0, class_validator_1.IsDate)(),
    __metadata("design:type", Date)
], CmsCustomerFeedbackDto.prototype, "updatedAt", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'ID người tạo',
        example: '550e8400-e29b-41d4-a716-************',
    }),
    (0, class_transformer_1.Expose)(),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsUUID)('4'),
    __metadata("design:type", String)
], CmsCustomerFeedbackDto.prototype, "createdBy", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'ID người cập nhật',
        example: '550e8400-e29b-41d4-a716-************',
    }),
    (0, class_transformer_1.Expose)(),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsUUID)('4'),
    __metadata("design:type", String)
], CmsCustomerFeedbackDto.prototype, "updatedBy", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'ID người xóa',
        example: '550e8400-e29b-41d4-a716-************',
    }),
    (0, class_transformer_1.Expose)(),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsUUID)('4'),
    __metadata("design:type", String)
], CmsCustomerFeedbackDto.prototype, "deletedBy", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Trạng thái xóa',
        example: false,
    }),
    (0, class_transformer_1.Expose)(),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], CmsCustomerFeedbackDto.prototype, "isDeleted", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Thời gian xóa',
        example: '2023-01-01T00:00:00.000Z',
    }),
    (0, class_transformer_1.Expose)(),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsDate)(),
    __metadata("design:type", Date)
], CmsCustomerFeedbackDto.prototype, "deletedAt", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Thông tin người tạo',
        type: () => user_dto_1.UserDto,
    }),
    (0, class_transformer_1.Expose)(),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => user_dto_1.UserDto),
    __metadata("design:type", user_dto_1.UserDto)
], CmsCustomerFeedbackDto.prototype, "creator", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Thông tin người cập nhật',
        type: () => user_dto_1.UserDto,
    }),
    (0, class_transformer_1.Expose)(),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => user_dto_1.UserDto),
    __metadata("design:type", user_dto_1.UserDto)
], CmsCustomerFeedbackDto.prototype, "updater", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Thông tin người xóa',
        type: () => user_dto_1.UserDto,
    }),
    (0, class_transformer_1.Expose)(),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => user_dto_1.UserDto),
    __metadata("design:type", user_dto_1.UserDto)
], CmsCustomerFeedbackDto.prototype, "deleter", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Thông tin người duyệt',
        type: () => user_dto_1.UserDto,
    }),
    (0, class_transformer_1.Expose)(),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => user_dto_1.UserDto),
    __metadata("design:type", user_dto_1.UserDto)
], CmsCustomerFeedbackDto.prototype, "approver", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Text hiển thị cho rating',
        example: 'Rất hài lòng',
    }),
    (0, class_transformer_1.Expose)(),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CmsCustomerFeedbackDto.prototype, "ratingText", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Màu sắc cho rating',
        example: '#007E33',
    }),
    (0, class_transformer_1.Expose)(),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CmsCustomerFeedbackDto.prototype, "ratingColor", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Tên hiển thị của khách hàng',
        example: 'Nguyễn Văn A - Giám đốc, Công ty ABC',
    }),
    (0, class_transformer_1.Expose)(),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CmsCustomerFeedbackDto.prototype, "displayName", void 0);
//# sourceMappingURL=cms-customer-feedback.dto.js.map
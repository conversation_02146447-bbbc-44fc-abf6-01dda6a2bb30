"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var VnpayService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.VnpayService = void 0;
const common_1 = require("@nestjs/common");
const config_1 = require("@nestjs/config");
const axios_1 = require("@nestjs/axios");
const crypto = require("crypto");
const rxjs_1 = require("rxjs");
let VnpayService = VnpayService_1 = class VnpayService {
    configService;
    httpService;
    logger = new common_1.Logger(VnpayService_1.name);
    tmnCode;
    hashSecret;
    vnpayUrl;
    vnpayApiUrl;
    returnUrl;
    ipnUrl;
    constructor(configService, httpService) {
        this.configService = configService;
        this.httpService = httpService;
        this.tmnCode = this.configService.get('VNPAY_TMN_CODE') || '';
        this.hashSecret = this.configService.get('VNPAY_HASH_SECRET') || '';
        this.vnpayUrl =
            this.configService.get('VNPAY_URL') ||
                'https://sandbox.vnpayment.vn/paymentv2/vpcpay.html';
        this.vnpayApiUrl =
            this.configService.get('VNPAY_API_URL') ||
                'https://sandbox.vnpayment.vn/merchant_webapi/api/transaction';
        this.returnUrl = this.configService.get('VNPAY_RETURN_URL') || '';
        this.ipnUrl = this.configService.get('VNPAY_IPN_URL') || '';
    }
    async createPaymentUrl(paymentData) {
        try {
            const date = new Date();
            const createDate = this.formatDate(date);
            const orderId = `${paymentData.userId}_${Date.now()}`;
            const transactionId = orderId;
            const expireDate = new Date(date.getTime() + 15 * 60 * 1000);
            const vnp_ExpireDate = this.formatDate(expireDate);
            const vnpParams = {
                vnp_Version: '2.1.0',
                vnp_Command: 'pay',
                vnp_TmnCode: this.tmnCode,
                vnp_Amount: paymentData.amount * 100,
                vnp_CurrCode: 'VND',
                vnp_TxnRef: orderId,
                vnp_OrderInfo: this.sanitizeOrderInfo(paymentData.description || `Thanh toan don hang ${orderId}`),
                vnp_OrderType: paymentData.orderType || 'other',
                vnp_Locale: paymentData.locale || 'vn',
                vnp_ReturnUrl: this.returnUrl,
                vnp_IpAddr: paymentData.ipAddress || '127.0.0.1',
                vnp_CreateDate: createDate,
                vnp_ExpireDate: vnp_ExpireDate,
            };
            if (paymentData.bankCode) {
                vnpParams.vnp_BankCode = paymentData.bankCode;
            }
            const sortedParams = this.sortObject(vnpParams);
            const signData = this.createSignData(sortedParams);
            const vnp_SecureHash = this.createSecureHash(signData);
            sortedParams['vnp_SecureHash'] = vnp_SecureHash;
            const paymentUrl = this.vnpayUrl + '?' + this.createQueryString(sortedParams);
            this.logger.log(`Đã tạo URL thanh toán VNPAY cho đơn hàng: ${orderId}`);
            this.logger.debug(`Payment URL: ${paymentUrl}`);
            return { paymentUrl, transactionId };
        }
        catch (error) {
            this.logger.error(`Lỗi khi tạo URL thanh toán VNPAY: ${error.message}`, error.stack);
            throw error;
        }
    }
    async verifyReturnUrl(params) {
        try {
            const secureHash = params['vnp_SecureHash'];
            const orderId = params['vnp_TxnRef'];
            const responseCode = params['vnp_ResponseCode'];
            const transactionStatus = params['vnp_TransactionStatus'];
            const vnpayTransactionNo = params['vnp_TransactionNo'];
            const bankCode = params['vnp_BankCode'];
            const payDate = params['vnp_PayDate'];
            if (!secureHash || !orderId) {
                return {
                    isValid: false,
                    message: 'Thiếu thông tin bắt buộc từ VNPAY',
                };
            }
            const verifyParams = { ...params };
            delete verifyParams['vnp_SecureHash'];
            delete verifyParams['vnp_SecureHashType'];
            const sortedParams = this.sortObject(verifyParams);
            const signData = this.createSignData(sortedParams);
            const calculatedHash = this.createSecureHash(signData);
            const isValidSignature = secureHash === calculatedHash;
            if (!isValidSignature) {
                this.logger.warn(`Chữ ký không hợp lệ cho đơn hàng: ${orderId}`);
                this.logger.debug(`Expected: ${calculatedHash}, Received: ${secureHash}`);
                return {
                    isValid: false,
                    transactionId: orderId,
                    responseCode,
                    message: 'Chữ ký không hợp lệ',
                };
            }
            const amount = parseInt(params['vnp_Amount']) / 100;
            const isSuccessful = responseCode === '00' && transactionStatus === '00';
            if (isSuccessful) {
                this.logger.log(`Xác thực thanh toán VNPAY thành công - Đơn hàng: ${orderId}, Số tiền: ${amount}, VNPAY TxnNo: ${vnpayTransactionNo}`);
                return {
                    isValid: true,
                    transactionId: orderId,
                    amount,
                    responseCode,
                    transactionStatus,
                    vnpayTransactionNo,
                    bankCode,
                    payDate,
                    message: 'Thanh toán thành công',
                };
            }
            else {
                this.logger.warn(`Thanh toán không thành công - Đơn hàng: ${orderId}, ResponseCode: ${responseCode}, TransactionStatus: ${transactionStatus}`);
                return {
                    isValid: true,
                    transactionId: orderId,
                    amount,
                    responseCode,
                    transactionStatus,
                    vnpayTransactionNo,
                    bankCode,
                    payDate,
                    message: this.getErrorMessage(responseCode),
                };
            }
        }
        catch (error) {
            this.logger.error(`Lỗi khi xác thực callback VNPAY: ${error.message}`, error.stack);
            return {
                isValid: false,
                message: `Lỗi xử lý: ${error.message}`,
            };
        }
    }
    async handleIpnCallback(params) {
        const result = await this.verifyReturnUrl(params);
        if (result.isValid) {
            this.logger.log(`IPN - Nhận thông báo thanh toán: ${result.transactionId}, Status: ${result.responseCode}`);
        }
        else {
            this.logger.error(`IPN - Thông báo không hợp lệ: ${result.message}`);
        }
        return {
            isValid: result.isValid,
            transactionId: result.transactionId,
            amount: result.amount,
            message: result.message,
        };
    }
    async createIpnResponse(params) {
        try {
            const verifyResult = await this.verifyReturnUrl(params);
            if (!verifyResult.isValid) {
                this.logger.error(`IPN Response - Chữ ký không hợp lệ: ${verifyResult.message}`);
                return {
                    RspCode: '97',
                    Message: 'Invalid signature',
                };
            }
            const responseCode = verifyResult.responseCode;
            const transactionStatus = verifyResult.transactionStatus;
            if (responseCode === '00' && transactionStatus === '00') {
                this.logger.log(`IPN Response - Giao dịch thành công: ${verifyResult.transactionId}`);
                return {
                    RspCode: '00',
                    Message: 'Confirm Success',
                };
            }
            else {
                this.logger.warn(`IPN Response - Giao dịch không thành công: ${verifyResult.transactionId}, Code: ${responseCode}`);
                return {
                    RspCode: '00',
                    Message: 'Transaction failed but confirmed',
                };
            }
        }
        catch (error) {
            this.logger.error(`IPN Response - Lỗi xử lý: ${error.message}`, error.stack);
            return {
                RspCode: '99',
                Message: 'Unknown error',
            };
        }
    }
    formatDate(date) {
        const vietnamTime = new Date(date.getTime() + 7 * 60 * 60 * 1000);
        const year = vietnamTime.getUTCFullYear();
        const month = String(vietnamTime.getUTCMonth() + 1).padStart(2, '0');
        const day = String(vietnamTime.getUTCDate()).padStart(2, '0');
        const hours = String(vietnamTime.getUTCHours()).padStart(2, '0');
        const minutes = String(vietnamTime.getUTCMinutes()).padStart(2, '0');
        const seconds = String(vietnamTime.getUTCSeconds()).padStart(2, '0');
        return `${year}${month}${day}${hours}${minutes}${seconds}`;
    }
    sanitizeOrderInfo(orderInfo) {
        return orderInfo
            .normalize('NFD')
            .replace(/[\u0300-\u036f]/g, '')
            .replace(/[^a-zA-Z0-9\s]/g, '')
            .replace(/\s+/g, ' ')
            .trim()
            .substring(0, 255);
    }
    createSignData(params) {
        const result = [];
        for (const key in params) {
            if (params.hasOwnProperty(key) &&
                params[key] !== null &&
                params[key] !== undefined &&
                params[key] !== '') {
                result.push(`${encodeURIComponent(key)}=${encodeURIComponent(params[key])}`);
            }
        }
        return result.join('&');
    }
    createSecureHash(data) {
        const hmac = crypto.createHmac('sha512', this.hashSecret);
        return hmac.update(Buffer.from(data, 'utf-8')).digest('hex');
    }
    createQueryString(params) {
        const result = [];
        for (const key in params) {
            if (params.hasOwnProperty(key) &&
                params[key] !== null &&
                params[key] !== undefined &&
                params[key] !== '') {
                result.push(`${encodeURIComponent(key)}=${encodeURIComponent(params[key])}`);
            }
        }
        return result.join('&');
    }
    sortObject(obj) {
        const sorted = {};
        const keys = Object.keys(obj).sort();
        for (const key of keys) {
            if (obj[key] !== null && obj[key] !== undefined && obj[key] !== '') {
                sorted[key] = obj[key];
            }
        }
        return sorted;
    }
    async queryTransaction(queryData) {
        try {
            const requestId = this.generateRequestId();
            const createDate = this.formatDate(new Date());
            const queryParams = {
                vnp_RequestId: requestId,
                vnp_Version: '2.1.0',
                vnp_Command: 'querydr',
                vnp_TmnCode: this.tmnCode,
                vnp_TxnRef: queryData.txnRef,
                vnp_OrderInfo: queryData.orderInfo,
                vnp_TransactionDate: queryData.transactionDate,
                vnp_CreateDate: createDate,
                vnp_IpAddr: queryData.ipAddr,
                vnp_SecureHash: '',
            };
            if (queryData.transactionNo) {
                queryParams.vnp_TransactionNo = queryData.transactionNo;
            }
            const hashData = [
                requestId,
                '2.1.0',
                'querydr',
                this.tmnCode,
                queryData.txnRef,
                queryData.transactionDate,
                createDate,
                queryData.ipAddr,
                queryData.orderInfo,
            ].join('|');
            queryParams.vnp_SecureHash = this.createSecureHash(hashData);
            const response = await this.sendApiRequest(queryParams);
            this.logger.log(`Query transaction result for ${queryData.txnRef}: ${JSON.stringify(response)}`);
            return response;
        }
        catch (error) {
            this.logger.error(`Lỗi khi truy vấn giao dịch: ${error.message}`, error.stack);
            throw error;
        }
    }
    async refundTransaction(refundData) {
        try {
            const requestId = this.generateRequestId();
            const createDate = this.formatDate(new Date());
            const refundParams = {
                vnp_RequestId: requestId,
                vnp_Version: '2.1.0',
                vnp_Command: 'refund',
                vnp_TmnCode: this.tmnCode,
                vnp_TransactionType: refundData.transactionType,
                vnp_TxnRef: refundData.txnRef,
                vnp_Amount: refundData.amount * 100,
                vnp_OrderInfo: refundData.orderInfo,
                vnp_TransactionDate: refundData.transactionDate,
                vnp_CreateBy: refundData.createBy,
                vnp_CreateDate: createDate,
                vnp_IpAddr: refundData.ipAddr,
                vnp_SecureHash: '',
            };
            if (refundData.transactionNo) {
                refundParams.vnp_TransactionNo = refundData.transactionNo;
            }
            const hashData = [
                requestId,
                '2.1.0',
                'refund',
                this.tmnCode,
                refundData.transactionType,
                refundData.txnRef,
                refundData.amount * 100,
                refundData.transactionNo || '',
                refundData.transactionDate,
                refundData.createBy,
                createDate,
                refundData.ipAddr,
                refundData.orderInfo,
            ].join('|');
            refundParams.vnp_SecureHash = this.createSecureHash(hashData);
            const response = await this.sendApiRequest(refundParams);
            this.logger.log(`Refund transaction result for ${refundData.txnRef}: ${JSON.stringify(response)}`);
            return response;
        }
        catch (error) {
            this.logger.error(`Lỗi khi hoàn tiền: ${error.message}`, error.stack);
            throw error;
        }
    }
    async sendApiRequest(params) {
        try {
            const response = await (0, rxjs_1.firstValueFrom)(this.httpService.post(this.vnpayApiUrl, params, {
                headers: {
                    'Content-Type': 'application/json',
                },
                timeout: 30000,
            }));
            this.logger.debug(`VNPAY API Response: ${JSON.stringify(response.data)}`);
            return response.data;
        }
        catch (error) {
            this.logger.error(`Lỗi khi gửi API request đến VNPAY: ${error.message}`, error.stack);
            if (error.response) {
                this.logger.error(`Response status: ${error.response.status}`);
                this.logger.error(`Response data: ${JSON.stringify(error.response.data)}`);
            }
            throw error;
        }
    }
    generateRequestId() {
        const timestamp = Date.now().toString();
        const random = Math.random().toString(36).substring(2, 8);
        return `${timestamp}${random}`.substring(0, 32);
    }
    getErrorMessage(responseCode) {
        const errorMessages = {
            '00': 'Giao dịch thành công',
            '07': 'Trừ tiền thành công. Giao dịch bị nghi ngờ (liên quan tới lừa đảo, giao dịch bất thường)',
            '09': 'Giao dịch không thành công do: Thẻ/Tài khoản của khách hàng chưa đăng ký dịch vụ InternetBanking tại ngân hàng',
            '10': 'Giao dịch không thành công do: Khách hàng xác thực thông tin thẻ/tài khoản không đúng quá 3 lần',
            '11': 'Giao dịch không thành công do: Đã hết hạn chờ thanh toán. Xin quý khách vui lòng thực hiện lại giao dịch',
            '12': 'Giao dịch không thành công do: Thẻ/Tài khoản của khách hàng bị khóa',
            '13': 'Giao dịch không thành công do Quý khách nhập sai mật khẩu xác thực giao dịch (OTP)',
            '24': 'Giao dịch không thành công do: Khách hàng hủy giao dịch',
            '51': 'Giao dịch không thành công do: Tài khoản của quý khách không đủ số dư để thực hiện giao dịch',
            '65': 'Giao dịch không thành công do: Tài khoản của Quý khách đã vượt quá hạn mức giao dịch trong ngày',
            '75': 'Ngân hàng thanh toán đang bảo trì',
            '79': 'Giao dịch không thành công do: KH nhập sai mật khẩu thanh toán quá số lần quy định',
            '99': 'Các lỗi khác (lỗi còn lại, không có trong danh sách mã lỗi đã liệt kê)',
        };
        return errorMessages[responseCode] || `Lỗi không xác định: ${responseCode}`;
    }
};
exports.VnpayService = VnpayService;
exports.VnpayService = VnpayService = VnpayService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [config_1.ConfigService,
        axios_1.HttpService])
], VnpayService);
//# sourceMappingURL=vnpay.service.js.map
"use client"

import { useAuth } from "@/hooks/use-auth"
import {
  IconBuildingBank,
  IconCash,
  IconCategory,
  IconChartCandle,
  IconCoin,
  IconCreditCard,
  IconCurrencyDollar,
  IconDashboard,
  IconDeviceAnalytics,
  IconExchange,
  IconFileAnalytics,
  IconFileDescription,
  IconFolders,
  IconGift,
  IconHistory,
  IconMoneybag,
  IconPackage,
  IconPackageImport,
  IconReceipt,
  IconRobot,
  IconSettings,
  IconShieldLock,
  IconShoppingCart,
  IconUser,
  IconUserCheck,
  IconUserCircle,
  IconUserCog,
  IconUserStar,
  IconWallet,
  IconNews,
  IconTags,
  IconPhoto,
  IconMessageCircle,
  IconBuilding,
  IconBrandPagekit,
  IconHeartHandshake,
  IconWorld
} from "@tabler/icons-react"
import Link from "next/link"
import * as React from "react"

import { NavMain } from "@/components/nav-main"
import { NavSecondary } from "@/components/nav-secondary"
import { NavUser } from "@/components/nav-user"
import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
} from "@/components/ui/sidebar"
import { Avatar, AvatarFallback, AvatarImage } from "./ui/avatar"
import { useSiteMetadata } from "@/hooks/use-site-metadata"

// Dữ liệu menu cho USER

const userNavData = {
  navMain: [
    {
      key: "user-dashboard",
      title: "Bảng quản trị",
      url: "/reports",
      // url: "/dashboard",
      icon: IconDashboard,
    },
    // {
    //   title: "Báo cáo thống kê",
    //   url: "/reports",
    //   icon: IconFileAnalytics,
    // },
    {
      type: "separator",
      title: "QUẢN TRỊ BẠC TRỰC TUYẾN",
    },
    {
      key: "user-trading",
      title: "Giao dịch mua bán",
      url: "/trading",
      icon: IconChartCandle,
    },
    {
      key: "user-order-books",
      title: "Lịch sử giao dịch",
      url: "/order-books",
      icon: IconReceipt,
    },
    {
      key: "user-token-assets",
      title: "Tài sản",
      url: "/token-assets",
      icon: IconCoin,
    },
    {
      type: "separator",
      title: "QUẢN TRỊ SẢN PHẨM BẠC",
    },
    {
      key: "user-ecom-products",
      title: "Sản phẩm",
      url: "/ecom-products",
      icon: IconPackageImport,
    },
    {
      key: "user-ecom-orders",
      title: "Đơn hàng",
      url: "/ecom-orders",
      icon: IconShoppingCart,
    },
    {
      type: "separator",
      title: "QUẢN TRỊ TÀI CHÍNH",
    },
    {
      key: "transactions",
      title: "Lịch sử thanh toán",
      url: "/transactions",
      icon: IconCurrencyDollar,
    },
    {
      type: "separator",
      title: "QUẢN TRỊ THÔNG TIN CÁ NHÂN",
    },
    {
      key: "user-profile",
      title: "Hồ sơ cá nhân",
      url: "/profile",
      icon: IconUser,
    },
    {
      key: "user-kyc",
      title: "Xác thực danh tính",
      url: "/kyc",
      icon: IconUserCheck,
    },
  ],
};

// Dữ liệu menu cho ADMIN
const adminNavData = {
  navMain: [
    {
      key: "admin-dashboard",
      title: "Bảng quản trị",
      url: "/admin/dashboard",
      icon: IconDashboard,
    },
    {
      key: "admin-reports",
      title: "Báo cáo thống kê",
      url: "/admin/reports",
      icon: IconDeviceAnalytics,
    },
    {
      type: "separator",
      title: "QUẢN TRỊ BẠC TRỰC TUYẾN",
    },
    {
      key: "admin-order-books",
      title: "Giao dịch online",
      url: "/admin/order-books",
      icon: IconExchange,
    },
    {
      key: "admin-token-assets",
      title: "Quản lý tài sản",
      url: "/admin/token-assets",
      icon: IconMoneybag,
    },
    {
      key: "tokens",
      title: "Quản lý sản phẩm",
      url: "/admin/tokens",
      icon: IconPackage,
    },
    {
      key: "token-categories",
      title: "Danh mục sản phẩm",
      url: "/admin/token-categories",
      icon: IconCategory,
    },
    {
      type: "separator",
      title: "QUẢN TRỊ SẢN PHẨM BẠC",
    },
    {
      key: "admin-ecom-orders",
      title: "Quản lý đơn hàng",
      url: "/admin/ecom-orders",
      icon: IconShoppingCart,
    },
    {
      key: "ecom-products",
      title: "Quản lý sản phẩm",
      url: "/admin/ecom-products",
      icon: IconPackageImport,
    },
    {
      key: "ecom-product-categories",
      title: "Danh mục sản phẩm",
      url: "/admin/ecom-product-categories",
      icon: IconFolders,
    },
    {
      type: "separator",
      title: "QUẢN TRỊ TÀI CHÍNH",
    },
    {
      key: "admin-wallets",
      title: "Quản lý tài khoản",
      url: "/admin/wallets",
      icon: IconWallet,
    },
    {
      key: "admin-transactions",
      title: "Lịch sử thanh toán",
      url: "/admin/transactions",
      icon: IconCurrencyDollar,
    },
    {
      key: "admin-payment-methods",
      title: "Phương thức thanh toán",
      url: "/admin/payment-methods",
      icon: IconCreditCard,
    },
    {
      key: "admin-banks",
      title: "Quản lý ngân hàng",
      url: "/admin/banks",
      icon: IconBuildingBank,
    },
    {
      type: "separator",
      title: "QUẢN TRỊ ĐIỂM THƯỞNG",
    },
    {
      key: "admin-crypto-wallets",
      title: "Tài khoản điểm thưởng",
      url: "/admin/crypto-wallets",
      icon: IconGift,
    },
    {
      key: "admin-crypto-transactions",
      title: "Lịch sử điểm thưởng",
      url: "/admin/crypto-transactions",
      icon: IconHistory,
    },
    {
      type: "separator",
      title: "QUẢN TRỊ ĐẠI LÝ",
    },
    {
      key: "admin-agents",
      title: "Quản lý đại lý",
      url: "/admin/agents",
      icon: IconUserStar,
    },
    {
      key: "admin-agent-commissions",
      title: "Lịch sử hoa hồng",
      url: "/admin/agent-commissions",
      icon: IconCash,
    },
    {
      type: "separator",
      title: "QUẢN TRỊ NỘI DUNG",
    },
    {
      key: "cms-categories",
      title: "Chuyên mục",
      url: "/admin/cms/categories",
      icon: IconCategory,
    },
    {
      key: "cms-posts",
      title: "Bài viết",
      url: "/admin/cms/posts",
      icon: IconNews,
    },
    {
      key: "cms-tags",
      title: "Thẻ",
      url: "/admin/cms/tags",
      icon: IconTags,
    },
    {
      key: "cms-pages",
      title: "Trang",
      url: "/admin/cms/pages",
      icon: IconBrandPagekit,
    },
    {
      key: "cms-banners",
      title: "Banner",
      url: "/admin/cms/banners",
      icon: IconPhoto,
    },
    {
      key: "cms-customer-feedbacks",
      title: "Phản hồi khách hàng",
      url: "/admin/cms/customer-feedbacks",
      icon: IconMessageCircle,
    },
    {
      key: "cms-showrooms",
      title: "Cửa hàng/đại lý",
      url: "/admin/cms/showrooms",
      icon: IconBuilding,
    },
    {
      key: "cms-partners",
      title: "Đối tác",
      url: "/admin/cms/partners",
      icon: IconHeartHandshake,
    },
    {
      type: "separator",
      title: "QUẢN TRỊ NGƯỜI DÙNG",
    },
    {
      key: "admin-users",
      title: "Quản lý người dùng",
      url: "/admin/users",
      icon: IconUserCog,
    },
    {
      key: "admin-roles",
      title: "Vai trò & quyền hạn",
      url: "/admin/roles",
      icon: IconShieldLock,
    },
    {
      key: "admin-kyc",
      title: "Xác thực danh tính",
      url: "/admin/kyc",
      icon: IconUserCheck,
    },
    {
      type: "separator",
      title: "QUẢN TRỊ HỆ THỐNG",
    },
    {
      key: "admin-profile",
      title: "Hồ sơ quản trị viên",
      url: "/admin/profile",
      icon: IconUserCircle,
    },
    {
      key: "admin-settings",
      title: "Cấu hình hệ thống",
      url: "/admin/settings",
      icon: IconSettings,
    },
    {
      key: "admin-website-settings",
      title: "Cấu hình website",
      url: "/admin/website-settings",
      icon: IconWorld,
    },
  ],
};

// Dữ liệu menu cho ADMIN
const agentNavData = {
  navMain: [
    {
      key: "agent-dashboard",
      title: "Bảng quản trị",
      url: "/agent/dashboard",
      icon: IconDashboard,
    },
    {
      key: "agent-reports",
      title: "Báo cáo thống kê",
      url: "/agent/reports",
      icon: IconDeviceAnalytics,
    },
    {
      type: "separator",
      title: "QUẢN TRỊ BẠC TRỰC TUYẾN",
    },
    {
      key: "agent-order-books",
      title: "Giao dịch online",
      url: "/agent/order-books",
      icon: IconExchange,
    },
    {
      key: "agent-token-assets",
      title: "Quản lý tài sản",
      url: "/agent/token-assets",
      icon: IconMoneybag,
    },
    // {
    //   title: "Quản lý sản phẩm",
    //   url: "/agent/tokens",
    //   icon: IconPackage,
    // },
    // {
    //   title: "Danh mục sản phẩm",
    //   url: "/agent/token-categories",
    //   icon: IconCategory,
    // },
    {
      type: "separator",
      title: "QUẢN TRỊ SẢN PHẨM BẠC",
    },
    {
      key: "agent-ecom-orders",
      title: "Quản lý đơn hàng",
      url: "/agent/ecom-orders",
      icon: IconShoppingCart,
    },
    // {
    //   title: "Quản lý sản phẩm",
    //   url: "/agent/ecom-products",
    //   icon: IconPackageImport,
    // },
    // {
    //   title: "Danh mục sản phẩm",
    //   url: "/agent/ecom-product-categories",
    //   icon: IconFolders,
    // },
    {
      type: "separator",
      title: "QUẢN TRỊ TÀI CHÍNH",
    },
    {
      key: "agent-wallets",
      title: "Quản lý tài khoản",
      url: "/agent/wallets",
      icon: IconWallet,
    },
    {
      key: "agent-transactions",
      title: "Lịch sử thanh toán",
      url: "/agent/transactions",
      icon: IconCurrencyDollar,
    },
    {
      key: "agent-payment-methods",
      title: "Phương thức thanh toán",
      url: "/agent/payment-methods",
      icon: IconCreditCard,
    },
    {
      type: "separator",
      title: "QUẢN TRỊ ĐIỂM THƯỞNG",
    },
    {
      key: "agent-crypto-wallets",
      title: "Tài khoản điểm thưởng",
      url: "/agent/crypto-wallets",
      icon: IconGift,
    },
    {
      key: "agent-crypto-transactions",
      title: "Lịch sử điểm thưởng",
      url: "/agent/crypto-transactions",
      icon: IconHistory,
    },
    {
      type: "separator",
      title: "QUẢN TRỊ ĐẠI LÝ",
    },
    {
      key: "agent-agents-hierarchical",
      title: "Quản lý hoa hồng",
      url: "/agent/agents-hierarchical",
      icon: IconUserStar,
    },
    {
      key: "agent-agent-commissions",
      title: "Lịch sử hoa hồng",
      url: "/agent/agent-commissions",
      icon: IconCash,
    },
    // {
    //   type: "separator",
    //   title: "QUẢN LÝ NGƯỜI DÙNG",
    // },
    // {
    //   title: "Quản lý người dùng",
    //   url: "/agent/users",
    //   icon: IconUserCog,
    // },
    // {
    //   title: "Vai trò & quyền hạn",
    //   url: "/agent/roles",
    //   icon: IconShieldLock,
    // },
    // {
    //   title: "Xác thực danh tính",
    //   url: "/agent/kyc",
    //   icon: IconUserCheck,
    // }
  ],
};

const data = {
  navClouds: [
    {
      title: "Capture",
      icon: IconFileAnalytics,
      isActive: true,
      url: "#",
      items: [
        {
          title: "Active Proposals",
          url: "#",
        },
        {
          title: "Archived",
          url: "#",
        },
      ],
    },
    {
      title: "Proposal",
      icon: IconFileDescription,
      url: "#",
      items: [
        {
          title: "Active Proposals",
          url: "#",
        },
        {
          title: "Archived",
          url: "#",
        },
      ],
    },
    {
      title: "Prompts",
      icon: IconRobot,
      url: "#",
      items: [
        {
          title: "Active Proposals",
          url: "#",
        },
        {
          title: "Archived",
          url: "#",
        },
      ],
    },
  ],
  navSecondary: [
  ],
}

export function AppSidebar({ ...props }: React.ComponentProps<typeof Sidebar>) {
  const { user } = useAuth();
  const { config } = useSiteMetadata();
  const siteName = config?.['site_name'];
  const siteLogo = config?.['site_logo'];

  // Guard: Nếu không có user, sử dụng userNavData để tránh race condition
  if (!user) {
    return (
      <Sidebar collapsible="icon" {...props}>
        <SidebarHeader>
          <SidebarMenu>
            <SidebarMenuItem>
              <SidebarMenuButton
                asChild
                className="data-[slot=sidebar-menu-button]:!p-1.5"
              >
                <Link href="/dashboard" className="flex items-center">
                  <img src={siteLogo} alt={siteName} className="h-10 w-[160px] min-w-[40px] object-contain object-left" />
                </Link>
              </SidebarMenuButton>
            </SidebarMenuItem>
          </SidebarMenu>
        </SidebarHeader>
        <SidebarContent className="[&_.tabler-icon]:w-5 [&_.tabler-icon]:h-5 [&_.tabler-icon]:min-w-[20px] [&_.tabler-icon]:min-h-[20px]">
          <NavMain items={userNavData.navMain} />
          <NavSecondary items={data.navSecondary} className="mt-auto" />
        </SidebarContent>
        <SidebarFooter>
          <NavUser />
        </SidebarFooter>
      </Sidebar>
    );
  }

  const isAdmin = user.roles?.includes("ADMIN");
  const isAgent = user.roles?.includes("AGENT");

  // Chọn menu dựa trên vai trò người dùng
  const navData = isAdmin ? adminNavData : isAgent ? agentNavData : userNavData;

  return (
    <Sidebar collapsible="icon" {...props}>
      <SidebarHeader>
        <SidebarMenu>
          <SidebarMenuItem>
            <SidebarMenuButton
              asChild
              className="data-[slot=sidebar-menu-button]:!p-1.5"
            >
              <Link href={isAdmin ? "/admin/dashboard" : isAgent ? "/agent/dashboard" : "/dashboard"} 
                className="flex items-center">
                <img src={siteLogo} alt={siteName} className="h-10 w-[160px] min-w-[40px] object-contain object-left" />
              </Link>
            </SidebarMenuButton>
          </SidebarMenuItem>
        </SidebarMenu>
      </SidebarHeader>
      <SidebarContent className="[&_.tabler-icon]:w-5 [&_.tabler-icon]:h-5 [&_.tabler-icon]:min-w-[20px] [&_.tabler-icon]:min-h-[20px]">
        <NavMain items={navData.navMain} />
        <NavSecondary items={data.navSecondary} className="mt-auto" />
      </SidebarContent>
      <SidebarFooter>
        <NavUser />
      </SidebarFooter>
    </Sidebar>
  )
}

import { EventEmitter2 } from '@nestjs/event-emitter';
import { Repository } from 'typeorm';
import { OrderBookDto } from '../dto/order-book.dto';
import { OrderBookDetail } from '../entities/order-book-detail.entity';
import { OrderBook } from '../entities/order-book.entity';
import { BaseOrderBookService } from './base.order-book.service';
export declare class DeleteOrderBookService extends BaseOrderBookService {
    protected readonly orderBookRepository: Repository<OrderBook>;
    private readonly orderBookDetailRepository;
    protected readonly eventEmitter: EventEmitter2;
    constructor(orderBookRepository: Repository<OrderBook>, orderBookDetailRepository: Repository<OrderBookDetail>, eventEmitter: EventEmitter2);
    remove(id: string): Promise<void>;
    bulkDelete(ids: string[]): Promise<{
        affected: number;
    }>;
    softDelete(id: string, userId: string): Promise<OrderBookDto>;
    bulkSoftDelete(ids: string[], userId: string): Promise<{
        affected: number;
        dtos: OrderBookDto[];
    }>;
    restore(id: string, userId: string): Promise<OrderBookDto>;
    cleanupOldRecords(days: number): Promise<number>;
}

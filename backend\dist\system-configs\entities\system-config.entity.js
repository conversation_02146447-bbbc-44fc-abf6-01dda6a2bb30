"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SystemConfig = void 0;
const openapi = require("@nestjs/swagger");
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
const typeorm_1 = require("typeorm");
const base_entity_1 = require("../../common/entities/base.entity");
let SystemConfig = class SystemConfig extends base_entity_1.BaseEntity {
    configKey;
    configValue;
    description;
    configGroup;
    sectionName;
    sectionDisplayName;
    sectionDescription;
    sectionOrder;
    displayOrder;
    groupDisplayName;
    groupDescription;
    groupIcon;
    groupOrder;
    isGroupConfig;
    configType;
    configOptions;
    getEntityName() {
        return 'system_configs';
    }
    static _OPENAPI_METADATA_FACTORY() {
        return { configKey: { required: true, type: () => String }, configValue: { required: true, type: () => String }, description: { required: true, type: () => String }, configGroup: { required: true, type: () => String }, sectionName: { required: true, type: () => String }, sectionDisplayName: { required: true, type: () => String }, sectionDescription: { required: true, type: () => String }, sectionOrder: { required: true, type: () => Number }, displayOrder: { required: true, type: () => Number }, groupDisplayName: { required: true, type: () => String }, groupDescription: { required: true, type: () => String }, groupIcon: { required: true, type: () => String }, groupOrder: { required: true, type: () => Number }, isGroupConfig: { required: true, type: () => Boolean }, configType: { required: true, type: () => String }, configOptions: { required: true, type: () => String } };
    }
};
exports.SystemConfig = SystemConfig;
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Khóa cấu hình', example: 'MAINTENANCE_MODE' }),
    (0, class_validator_1.IsNotEmpty)(),
    (0, class_validator_1.IsString)(),
    (0, typeorm_1.Column)({
        type: 'varchar',
        length: 50,
        nullable: false,
        unique: true,
        name: 'config_key',
    }),
    __metadata("design:type", String)
], SystemConfig.prototype, "configKey", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Giá trị cấu hình', example: 'false' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, typeorm_1.Column)({ type: 'text', nullable: true, name: 'config_value' }),
    __metadata("design:type", String)
], SystemConfig.prototype, "configValue", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Mô tả cấu hình',
        example: 'Chế độ bảo trì hệ thống',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, typeorm_1.Column)({ type: 'text', nullable: true, name: 'description' }),
    __metadata("design:type", String)
], SystemConfig.prototype, "description", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Nhóm cấu hình',
        example: 'general',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, typeorm_1.Column)({
        type: 'varchar',
        length: 50,
        nullable: true,
        name: 'config_group',
        default: 'general',
    }),
    __metadata("design:type", String)
], SystemConfig.prototype, "configGroup", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Tên section của cấu hình',
        example: 'header',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, typeorm_1.Column)({
        type: 'varchar',
        length: 50,
        nullable: true,
        name: 'section_name',
        default: 'default',
    }),
    __metadata("design:type", String)
], SystemConfig.prototype, "sectionName", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Tên hiển thị của section',
        example: 'Cấu hình Header',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, typeorm_1.Column)({
        type: 'varchar',
        length: 100,
        nullable: true,
        name: 'section_display_name',
    }),
    __metadata("design:type", String)
], SystemConfig.prototype, "sectionDisplayName", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Mô tả của section',
        example: 'Cấu hình phần header của website',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, typeorm_1.Column)({
        type: 'text',
        nullable: true,
        name: 'section_description',
    }),
    __metadata("design:type", String)
], SystemConfig.prototype, "sectionDescription", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Thứ tự hiển thị của section',
        example: 1,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    (0, typeorm_1.Column)({
        type: 'int',
        nullable: true,
        name: 'section_order',
        default: 999,
    }),
    __metadata("design:type", Number)
], SystemConfig.prototype, "sectionOrder", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Thứ tự hiển thị của trường cấu hình trong section',
        example: 1,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    (0, typeorm_1.Column)({
        type: 'int',
        nullable: true,
        name: 'display_order',
        default: 999,
    }),
    __metadata("design:type", Number)
], SystemConfig.prototype, "displayOrder", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Tên hiển thị của nhóm cấu hình',
        example: 'Cấu hình chung',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, typeorm_1.Column)({
        type: 'varchar',
        length: 100,
        nullable: true,
        name: 'group_display_name',
    }),
    __metadata("design:type", String)
], SystemConfig.prototype, "groupDisplayName", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Mô tả của nhóm cấu hình',
        example: 'Cấu hình chung của hệ thống',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, typeorm_1.Column)({
        type: 'text',
        nullable: true,
        name: 'group_description',
    }),
    __metadata("design:type", String)
], SystemConfig.prototype, "groupDescription", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Icon của nhóm cấu hình',
        example: 'settings',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, typeorm_1.Column)({
        type: 'varchar',
        length: 50,
        nullable: true,
        name: 'group_icon',
    }),
    __metadata("design:type", String)
], SystemConfig.prototype, "groupIcon", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Thứ tự hiển thị của nhóm cấu hình',
        example: 1,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    (0, typeorm_1.Column)({
        type: 'int',
        nullable: true,
        name: 'group_order',
        default: 999,
    }),
    __metadata("design:type", Number)
], SystemConfig.prototype, "groupOrder", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Cờ đánh dấu đây là cấu hình nhóm',
        example: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsBoolean)(),
    (0, typeorm_1.Column)({
        type: 'boolean',
        nullable: true,
        name: 'is_group_config',
        default: false,
    }),
    __metadata("design:type", Boolean)
], SystemConfig.prototype, "isGroupConfig", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Loại cấu hình',
        example: 'boolean',
        enum: ['text', 'textarea', 'number', 'boolean', 'select'],
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, typeorm_1.Column)({
        type: 'varchar',
        length: 20,
        nullable: true,
        name: 'config_type',
        default: 'text',
    }),
    __metadata("design:type", String)
], SystemConfig.prototype, "configType", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Tùy chọn cho loại select',
        example: '["option1", "option2"]',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, typeorm_1.Column)({
        type: 'text',
        nullable: true,
        name: 'config_options',
    }),
    __metadata("design:type", String)
], SystemConfig.prototype, "configOptions", void 0);
exports.SystemConfig = SystemConfig = __decorate([
    (0, typeorm_1.Entity)('system_configs')
], SystemConfig);
//# sourceMappingURL=system-config.entity.js.map
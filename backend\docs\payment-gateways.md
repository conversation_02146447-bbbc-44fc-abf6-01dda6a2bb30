# Hướng dẫn tích hợp cổng thanh toán

Hệ thống hỗ trợ tích hợp hai cổng thanh toán phổ biến tại Việt Nam: VNPAY và MOMO. Tài liệu này hướng dẫn cách cấu hình và sử dụng các cổng thanh toán này.

## Cấu hình cổng thanh toán

### Cấu hình trong file .env

Đ<PERSON> sử dụng các cổng thanh toán, bạn cần cấu hình các thông tin sau trong file `.env`:

```
# Frontend URL for payment redirects
FRONTEND_URL=http://localhost:3188

# VNPay Configuration
VNPAY_TMN_CODE=YOUR_VNPAY_TMN_CODE
VNPAY_HASH_SECRET=YOUR_VNPAY_HASH_SECRET
VNPAY_URL=https://sandbox.vnpayment.vn/paymentv2/vpcpay.html
VNPAY_RETURN_URL=http://locahost:3000/api/v1/payment-gateways/vnpay/return

# MoMo Configuration
MOMO_PARTNER_CODE=YOUR_MOMO_PARTNER_CODE
MOMO_ACCESS_KEY=YOUR_MOMO_ACCESS_KEY
MOMO_SECRET_KEY=YOUR_MOMO_SECRET_KEY
MOMO_ENDPOINT=https://test-payment.momo.vn/v2/gateway/api/create
MOMO_REDIRECT_URL=http://locahost:3000/api/v1/payment-gateways/momo/return
MOMO_IPN_URL=http://locahost:3000/api/v1/payment-gateways/momo/ipn
```

### Lấy thông tin cấu hình từ VNPAY và MOMO

1. **VNPAY**:
   - Đăng ký tài khoản merchant tại [VNPAY Sandbox](https://sandbox.vnpayment.vn/merchantv2/)
   - Sau khi đăng ký, bạn sẽ nhận được `TMN_CODE` và `HASH_SECRET`
   - Cấu hình URL callback trong tài khoản merchant VNPAY

2. **MOMO**:
   - Đăng ký tài khoản merchant tại [MoMo Developer](https://business.momo.vn/)
   - Sau khi đăng ký, bạn sẽ nhận được `PARTNER_CODE`, `ACCESS_KEY` và `SECRET_KEY`
   - Cấu hình URL callback trong tài khoản merchant MoMo

## Sử dụng API thanh toán

### Tạo yêu cầu thanh toán

**Endpoint**: `POST /api/v1/payment-gateways/create-payment`

**Headers**:
```
Authorization: Bearer YOUR_JWT_TOKEN
Content-Type: application/json
```

**Request Body**:
```json
{
  "walletId": "550e8400-e29b-41d4-a716-446655440000",
  "amount": 100000,
  "gatewayType": "VNPAY",
  "description": "Nạp tiền vào ví"
}
```

**Tham số**:
- `walletId`: ID của ví nhận tiền
- `amount`: Số tiền thanh toán (VND)
- `gatewayType`: Loại cổng thanh toán (`VNPAY` hoặc `MOMO`)
- `description`: Mô tả giao dịch (tùy chọn)

**Response**:
```json
{
  "paymentUrl": "https://sandbox.vnpayment.vn/paymentv2/vpcpay.html?vnp_Amount=10000000&vnp_Command=pay&...",
  "transactionId": "550e8400-e29b-41d4-a716-446655440000"
}
```

### Xử lý callback từ cổng thanh toán

Hệ thống tự động xử lý callback từ cổng thanh toán và cập nhật số dư ví khi thanh toán thành công. Các endpoint callback đã được cấu hình sẵn:

- VNPAY Return URL: `/api/v1/payment-gateways/vnpay/return`
- VNPAY IPN URL: `/api/v1/payment-gateways/vnpay/ipn`
- MOMO Return URL: `/api/v1/payment-gateways/momo/return`
- MOMO IPN URL: `/api/v1/payment-gateways/momo/ipn`

## Luồng thanh toán

1. Frontend gọi API `create-payment` để tạo yêu cầu thanh toán
2. Backend tạo giao dịch với trạng thái `PENDING` và trả về URL thanh toán
3. Frontend chuyển hướng người dùng đến URL thanh toán
4. Người dùng hoàn thành thanh toán trên cổng thanh toán
5. Cổng thanh toán chuyển hướng người dùng về Return URL và gửi IPN
6. Backend xử lý callback, cập nhật trạng thái giao dịch và số dư ví
7. Backend chuyển hướng người dùng về trang kết quả thanh toán trên frontend

## Xử lý lỗi

Khi thanh toán thất bại, hệ thống sẽ chuyển hướng người dùng về trang lỗi trên frontend với thông báo lỗi tương ứng.

## Môi trường test

Trong môi trường phát triển, bạn có thể sử dụng các tài khoản test của VNPAY và MOMO:

### VNPAY Sandbox
- Ngân hàng: NCB
- Số thẻ: 9704198526191432198
- Tên chủ thẻ: NGUYEN VAN A
- Ngày phát hành: 07/15
- Mật khẩu OTP: 123456

### MOMO Sandbox
- Số điện thoại: 0919123456
- Mật khẩu: 123456
- OTP: 000000

## Chuyển sang môi trường production

Khi chuyển sang môi trường production, bạn cần:

1. Cập nhật các thông tin cấu hình trong file `.env` với thông tin từ tài khoản merchant production
2. Cập nhật các URL callback để trỏ đến domain production
3. Kiểm tra kỹ các thông tin thanh toán trước khi triển khai

## Lưu ý bảo mật

- Luôn xác thực chữ ký từ cổng thanh toán trước khi cập nhật số dư ví
- Không lưu trữ thông tin thẻ hoặc thông tin thanh toán nhạy cảm
- Sử dụng HTTPS cho tất cả các giao tiếp với cổng thanh toán
- Kiểm tra kỹ các tham số từ cổng thanh toán trước khi xử lý

"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ExportUserService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const event_emitter_1 = require("@nestjs/event-emitter");
const base_user_service_1 = require("./base.user.service");
const user_entity_1 = require("../entities/user.entity");
const role_entity_1 = require("../../roles/entities/role.entity");
const streaming_export_service_1 = require("../../common/services/streaming-export.service");
let ExportUserService = class ExportUserService extends base_user_service_1.BaseUserService {
    userRepository;
    roleRepository;
    eventEmitter;
    streamingExportService;
    constructor(userRepository, roleRepository, eventEmitter, streamingExportService) {
        super(userRepository, roleRepository, eventEmitter);
        this.userRepository = userRepository;
        this.roleRepository = roleRepository;
        this.eventEmitter = eventEmitter;
        this.streamingExportService = streamingExportService;
    }
    async exportStream(format = 'json', response, batchSize = 1000) {
        this.logger.log(`Streaming export user data in ${format} format with batch size: ${batchSize}`);
        try {
            const queryBuilder = this.userRepository
                .createQueryBuilder('user')
                .leftJoinAndSelect('user.roles', 'roles')
                .where('user.isDeleted = :isDeleted', { isDeleted: false })
                .orderBy('user.createdAt', 'DESC');
            const totalCount = await this.streamingExportService.getTotalCount(queryBuilder);
            const validation = this.streamingExportService.validateExportRequest(totalCount);
            if (!validation.valid) {
                throw new common_1.InternalServerErrorException(validation.message);
            }
            const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
            const filename = `users-export-${timestamp}.${format}`;
            this.logger.log(`Starting streaming export: ${filename}, total records: ${totalCount}`);
            if (format === 'csv') {
                const headers = [
                    'id', 'username', 'email', 'fullName', 'phone', 'address',
                    'isActive', 'isKycVerified', 'emailVerified', 'createdAt', 'updatedAt'
                ];
                await this.streamingExportService.exportToCsvStream(queryBuilder, response, filename, batchSize, headers);
            }
            else {
                await this.streamingExportService.exportToJsonStream(queryBuilder, response, filename, batchSize);
            }
            this.logger.log(`Successfully completed streaming export: ${filename}`);
        }
        catch (error) {
            this.logger.error(`Failed to stream export users: ${error.message}`, error.stack);
            throw new common_1.InternalServerErrorException('Failed to stream export users.');
        }
    }
    async export(format = 'json') {
        this.logger.log(`Exporting user data in ${format} format`);
        try {
            const users = await this.userRepository.find({
                where: { isDeleted: false },
                relations: ['roles'],
            });
            const dtos = this.convertToDtoArray(users);
            if (format === 'csv') {
                if (dtos.length === 0)
                    return '';
                try {
                    const headers = Object.keys(dtos[0]).join(',');
                    const csvRows = dtos.map((row) => Object.values(row)
                        .map((value) => {
                        const strVal = String(value ?? '');
                        if (strVal.includes(',') ||
                            strVal.includes('"') ||
                            strVal.includes('\n')) {
                            return `"${strVal.replace(/"/g, '""')}"`;
                        }
                        return strVal;
                    })
                        .join(','));
                    return `${headers}\n${csvRows.join('\n')}`;
                }
                catch (csvError) {
                    this.logger.error(`Error generating CSV: ${csvError.message}`, csvError.stack);
                    throw new common_1.InternalServerErrorException('Failed to generate CSV export.');
                }
            }
            return dtos;
        }
        catch (error) {
            this.logger.error(`Failed to export users: ${error.message}`, error.stack);
            throw new common_1.InternalServerErrorException('Failed to export users.');
        }
    }
    async getExportEstimate() {
        try {
            const totalRecords = await this.userRepository.count({
                where: { isDeleted: false },
            });
            const recommendedBatchSize = totalRecords > 10000 ? 1000 : 500;
            const estimatedTimeSeconds = this.streamingExportService.estimateExportTime(totalRecords, recommendedBatchSize);
            return {
                totalRecords,
                estimatedTimeSeconds,
                recommendedBatchSize,
                maxAllowedRecords: 1000000,
            };
        }
        catch (error) {
            this.logger.error(`Failed to get export estimate: ${error.message}`, error.stack);
            throw new common_1.InternalServerErrorException('Failed to get export estimate.');
        }
    }
};
exports.ExportUserService = ExportUserService;
exports.ExportUserService = ExportUserService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(user_entity_1.User)),
    __param(1, (0, typeorm_1.InjectRepository)(role_entity_1.Role)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        typeorm_2.Repository,
        event_emitter_1.EventEmitter2,
        streaming_export_service_1.StreamingExportService])
], ExportUserService);
//# sourceMappingURL=export.user.service.js.map
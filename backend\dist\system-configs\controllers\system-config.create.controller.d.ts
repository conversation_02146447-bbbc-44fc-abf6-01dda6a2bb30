import { CreateSystemConfigService } from '../services/create.system-config.service';
import { SystemConfigDto } from '../dto/system-config.dto';
import { CreateSystemConfigDto } from '../dto/create-system-config.dto';
import { BulkUpdateSystemConfigItemDto } from '../dto/bulk-update-system-config.dto';
export declare class SystemConfigCreateController {
    private readonly systemConfigService;
    private readonly logger;
    constructor(systemConfigService: CreateSystemConfigService);
    create(createSystemConfigDto: CreateSystemConfigDto, userId: string): Promise<SystemConfigDto>;
    createOrUpdateBulk(bulkDto: BulkUpdateSystemConfigItemDto[], userId: string): Promise<SystemConfigDto[]>;
}

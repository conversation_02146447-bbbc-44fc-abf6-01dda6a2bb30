"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreateCmsPostsController = void 0;
const openapi = require("@nestjs/swagger");
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const jwt_auth_guard_1 = require("../../common/guards/jwt-auth.guard");
const create_cms_posts_service_1 = require("../services/create.cms-posts.service");
const create_cms_post_dto_1 = require("../dto/create.cms-post.dto");
const api_response_dto_1 = require("../../common/dto/api-response.dto");
const get_user_decorator_1 = require("../../common/decorators/get-user.decorator");
const roles_decorator_1 = require("../../common/decorators/roles.decorator");
const permissions_decorator_1 = require("../../common/decorators/permissions.decorator");
let CreateCmsPostsController = class CreateCmsPostsController {
    cmsPostsService;
    constructor(cmsPostsService) {
        this.cmsPostsService = cmsPostsService;
    }
    async create(createCmsPostDto, userId) {
        return this.cmsPostsService.create(createCmsPostDto, userId);
    }
    async bulkCreate(createCmsPostDtos, userId) {
        return this.cmsPostsService.bulkCreate(createCmsPostDtos, userId);
    }
    async duplicate(id, userId) {
        return this.cmsPostsService.duplicate(id, userId);
    }
    async createFromTemplate(templateId, createCmsPostDto, userId) {
        return this.cmsPostsService.createFromTemplate(templateId, createCmsPostDto, userId);
    }
};
exports.CreateCmsPostsController = CreateCmsPostsController;
__decorate([
    (0, common_1.Post)(),
    (0, roles_decorator_1.Roles)('ADMIN', 'EDITOR'),
    (0, permissions_decorator_1.Permissions)('cms-post:create'),
    (0, swagger_1.ApiOperation)({ summary: 'Tạo mới bài viết CMS' }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.CREATED,
        description: 'Bài viết CMS đã được tạo thành công.',
        type: api_response_dto_1.ApiResponseDto,
        schema: {
            properties: {
                data: { $ref: '#/components/schemas/CmsPostDto' },
            },
        },
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.BAD_REQUEST,
        description: 'Dữ liệu đầu vào không hợp lệ.',
        type: api_response_dto_1.ApiResponseDto,
        schema: { properties: { data: { type: 'object', nullable: true } } },
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.CONFLICT,
        description: 'Slug đã tồn tại.',
        type: api_response_dto_1.ApiResponseDto,
        schema: { properties: { data: { type: 'object', nullable: true } } },
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.NOT_FOUND,
        description: 'Không tìm thấy chuyên mục hoặc tác giả.',
        type: api_response_dto_1.ApiResponseDto,
        schema: { properties: { data: { type: 'object', nullable: true } } },
    }),
    (0, swagger_1.ApiBody)({ type: create_cms_post_dto_1.CreateCmsPostDto }),
    openapi.ApiResponse({ status: 201, type: require("../dto/cms-post.dto").CmsPostDto }),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, get_user_decorator_1.GetUser)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [create_cms_post_dto_1.CreateCmsPostDto, String]),
    __metadata("design:returntype", Promise)
], CreateCmsPostsController.prototype, "create", null);
__decorate([
    (0, common_1.Post)('bulk'),
    (0, roles_decorator_1.Roles)('ADMIN'),
    (0, permissions_decorator_1.Permissions)('cms-post:create'),
    (0, swagger_1.ApiOperation)({ summary: 'Tạo nhiều bài viết CMS cùng lúc' }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.CREATED,
        description: 'Các bài viết CMS đã được tạo thành công.',
        type: api_response_dto_1.ApiResponseDto,
        schema: {
            properties: {
                data: {
                    type: 'array',
                    items: { $ref: '#/components/schemas/CmsPostDto' },
                },
            },
        },
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.BAD_REQUEST,
        description: 'Dữ liệu đầu vào không hợp lệ.',
        type: api_response_dto_1.ApiResponseDto,
        schema: { properties: { data: { type: 'object', nullable: true } } },
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.CONFLICT,
        description: 'Có slug trùng lặp.',
        type: api_response_dto_1.ApiResponseDto,
        schema: { properties: { data: { type: 'object', nullable: true } } },
    }),
    (0, swagger_1.ApiBody)({ type: [create_cms_post_dto_1.CreateCmsPostDto] }),
    openapi.ApiResponse({ status: 201, type: [require("../dto/cms-post.dto").CmsPostDto] }),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, get_user_decorator_1.GetUser)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Array, String]),
    __metadata("design:returntype", Promise)
], CreateCmsPostsController.prototype, "bulkCreate", null);
__decorate([
    (0, common_1.Post)(':id/duplicate'),
    (0, roles_decorator_1.Roles)('ADMIN', 'EDITOR'),
    (0, permissions_decorator_1.Permissions)('cms-post:create'),
    (0, swagger_1.ApiOperation)({ summary: 'Nhân bản bài viết CMS' }),
    (0, swagger_1.ApiParam)({
        name: 'id',
        description: 'ID của bài viết CMS cần nhân bản',
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.CREATED,
        description: 'Bài viết CMS đã được nhân bản thành công.',
        type: api_response_dto_1.ApiResponseDto,
        schema: {
            properties: { data: { $ref: '#/components/schemas/CmsPostDto' } },
        },
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.NOT_FOUND,
        description: 'Không tìm thấy bài viết CMS.',
        type: api_response_dto_1.ApiResponseDto,
        schema: { properties: { data: { type: 'object', nullable: true } } },
    }),
    openapi.ApiResponse({ status: 201, type: require("../dto/cms-post.dto").CmsPostDto }),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __param(1, (0, get_user_decorator_1.GetUser)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String]),
    __metadata("design:returntype", Promise)
], CreateCmsPostsController.prototype, "duplicate", null);
__decorate([
    (0, common_1.Post)('from-template/:templateId'),
    (0, roles_decorator_1.Roles)('ADMIN', 'EDITOR'),
    (0, permissions_decorator_1.Permissions)('cms-post:create'),
    (0, swagger_1.ApiOperation)({ summary: 'Tạo bài viết CMS từ template' }),
    (0, swagger_1.ApiParam)({
        name: 'templateId',
        description: 'ID của template',
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.CREATED,
        description: 'Bài viết CMS đã được tạo từ template thành công.',
        type: api_response_dto_1.ApiResponseDto,
        schema: {
            properties: { data: { $ref: '#/components/schemas/CmsPostDto' } },
        },
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.NOT_FOUND,
        description: 'Không tìm thấy template.',
        type: api_response_dto_1.ApiResponseDto,
        schema: { properties: { data: { type: 'object', nullable: true } } },
    }),
    (0, swagger_1.ApiBody)({ type: create_cms_post_dto_1.CreateCmsPostDto }),
    openapi.ApiResponse({ status: 201, type: require("../dto/cms-post.dto").CmsPostDto }),
    __param(0, (0, common_1.Param)('templateId', common_1.ParseUUIDPipe)),
    __param(1, (0, common_1.Body)()),
    __param(2, (0, get_user_decorator_1.GetUser)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, create_cms_post_dto_1.CreateCmsPostDto, String]),
    __metadata("design:returntype", Promise)
], CreateCmsPostsController.prototype, "createFromTemplate", null);
exports.CreateCmsPostsController = CreateCmsPostsController = __decorate([
    (0, swagger_1.ApiTags)('cms-posts'),
    (0, swagger_1.ApiBearerAuth)(),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, common_1.Controller)('cms/posts'),
    __metadata("design:paramtypes", [create_cms_posts_service_1.CreateCmsPostsService])
], CreateCmsPostsController);
//# sourceMappingURL=create.cms-posts.controller.js.map
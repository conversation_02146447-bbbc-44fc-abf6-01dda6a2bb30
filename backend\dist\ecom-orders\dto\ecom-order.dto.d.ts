import { OrderStatus, PaymentStatus } from '../enum/ecom-orders-enum';
import { UserDto } from '../../users/dto/user.dto';
import { EcomOrderDetailDto } from '../../ecom-order-details/dto/ecom-order-detail.dto';
export declare class EcomOrderDto {
    id: string;
    userId: string;
    orderNumber: string;
    totalAmount: number;
    status: OrderStatus;
    paymentStatus: PaymentStatus;
    notes?: string;
    isDeleted: boolean;
    createdAt: Date;
    updatedAt: Date;
    createdBy?: string | null;
    updatedBy?: string | null;
    deletedAt?: Date | null;
    deletedBy?: string | null;
    user: UserDto;
    creator?: UserDto | null;
    updater?: UserDto | null;
    deleter?: UserDto | null;
    orderDetails?: EcomOrderDetailDto[];
}

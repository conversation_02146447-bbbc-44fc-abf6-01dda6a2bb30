{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/bloomgoo/gold-exchange-platform/frontend/components/ui/label.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as LabelPrimitive from \"@radix-ui/react-label\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction Label({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof LabelPrimitive.Root>) {\r\n  return (\r\n    <LabelPrimitive.Root\r\n      data-slot=\"label\"\r\n      className={cn(\r\n        \"flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport { Label }\r\n"], "names": [], "mappings": ";;;;AAGA;AAEA;AAAA;AALA;;;;AAOA,SAAS,MAAM,EACb,SAAS,EACT,GAAG,OAC8C;IACjD,qBACE,sSAAC,iRAAA,CAAA,OAAmB;QAClB,aAAU;QACV,WAAW,CAAA,GAAA,+HAAA,CAAA,KAAE,AAAD,EACV,uNACA;QAED,GAAG,KAAK;;;;;;AAGf;KAdS", "debugId": null}}, {"offset": {"line": 42, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/bloomgoo/gold-exchange-platform/frontend/components/ui/textarea.tsx"], "sourcesContent": ["import * as React from \"react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction Textarea({ className, ...props }: React.ComponentProps<\"textarea\">) {\r\n  return (\r\n    <textarea\r\n      data-slot=\"textarea\"\r\n      className={cn(\r\n        \"border-input placeholder:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 flex field-sizing-content min-h-16 w-full rounded-md border bg-transparent px-3 py-2 text-base shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport { Textarea }\r\n"], "names": [], "mappings": ";;;;AAEA;AAAA;;;AAEA,SAAS,SAAS,EAAE,SAAS,EAAE,GAAG,OAAyC;IACzE,qBACE,sSAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,+HAAA,CAAA,KAAE,AAAD,EACV,ucACA;QAED,GAAG,KAAK;;;;;;AAGf;KAXS", "debugId": null}}, {"offset": {"line": 74, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/bloomgoo/gold-exchange-platform/frontend/components/ui/switch.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as SwitchPrimitive from \"@radix-ui/react-switch\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction Switch({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof SwitchPrimitive.Root>) {\r\n  return (\r\n    <SwitchPrimitive.Root\r\n      data-slot=\"switch\"\r\n      className={cn(\r\n        \"peer data-[state=checked]:bg-primary data-[state=unchecked]:bg-input focus-visible:border-ring focus-visible:ring-ring/50 dark:data-[state=unchecked]:bg-input/80 inline-flex h-[1.15rem] w-8 shrink-0 items-center rounded-full border border-transparent shadow-xs transition-all outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50\",\r\n        className\r\n      )}\r\n      {...props}\r\n    >\r\n      <SwitchPrimitive.Thumb\r\n        data-slot=\"switch-thumb\"\r\n        className={cn(\r\n          \"bg-background dark:data-[state=unchecked]:bg-foreground dark:data-[state=checked]:bg-primary-foreground pointer-events-none block size-4 rounded-full ring-0 transition-transform data-[state=checked]:translate-x-[calc(100%-2px)] data-[state=unchecked]:translate-x-0\"\r\n        )}\r\n      />\r\n    </SwitchPrimitive.Root>\r\n  )\r\n}\r\n\r\nexport { Switch }\r\n"], "names": [], "mappings": ";;;;AAGA;AAEA;AAAA;AALA;;;;AAOA,SAAS,OAAO,EACd,SAAS,EACT,GAAG,OAC+C;IAClD,qBACE,sSAAC,kRAAA,CAAA,OAAoB;QACnB,aAAU;QACV,WAAW,CAAA,GAAA,+HAAA,CAAA,KAAE,AAAD,EACV,6WACA;QAED,GAAG,KAAK;kBAET,cAAA,sSAAC,kRAAA,CAAA,QAAqB;YACpB,aAAU;YACV,WAAW,CAAA,GAAA,+HAAA,CAAA,KAAE,AAAD,EACV;;;;;;;;;;;AAKV;KArBS", "debugId": null}}, {"offset": {"line": 117, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/bloomgoo/gold-exchange-platform/frontend/components/ui/select.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as SelectPrimitive from \"@radix-ui/react-select\"\r\nimport { Check<PERSON><PERSON>, ChevronDownIcon, ChevronUpIcon } from \"lucide-react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction Select({\r\n  ...props\r\n}: React.ComponentProps<typeof SelectPrimitive.Root>) {\r\n  return <SelectPrimitive.Root data-slot=\"select\" {...props} />\r\n}\r\n\r\nfunction SelectGroup({\r\n  ...props\r\n}: React.ComponentProps<typeof SelectPrimitive.Group>) {\r\n  return <SelectPrimitive.Group data-slot=\"select-group\" {...props} />\r\n}\r\n\r\nfunction SelectValue({\r\n  ...props\r\n}: React.ComponentProps<typeof SelectPrimitive.Value>) {\r\n  return <SelectPrimitive.Value data-slot=\"select-value\" {...props} />\r\n}\r\n\r\nfunction SelectTrigger({\r\n  className,\r\n  size = \"default\",\r\n  children,\r\n  ...props\r\n}: React.ComponentProps<typeof SelectPrimitive.Trigger> & {\r\n  size?: \"sm\" | \"default\"\r\n}) {\r\n  return (\r\n    <SelectPrimitive.Trigger\r\n      data-slot=\"select-trigger\"\r\n      data-size={size}\r\n      className={cn(\r\n        \"border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-9 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\r\n        className\r\n      )}\r\n      {...props}\r\n    >\r\n      {children}\r\n      <SelectPrimitive.Icon asChild>\r\n        <ChevronDownIcon className=\"size-4 opacity-50\" />\r\n      </SelectPrimitive.Icon>\r\n    </SelectPrimitive.Trigger>\r\n  )\r\n}\r\n\r\nfunction SelectContent({\r\n  className,\r\n  children,\r\n  position = \"popper\",\r\n  ...props\r\n}: React.ComponentProps<typeof SelectPrimitive.Content>) {\r\n  return (\r\n    <SelectPrimitive.Portal>\r\n      <SelectPrimitive.Content\r\n        data-slot=\"select-content\"\r\n        className={cn(\r\n          \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border shadow-md\",\r\n          position === \"popper\" &&\r\n            \"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1\",\r\n          className\r\n        )}\r\n        position={position}\r\n        {...props}\r\n      >\r\n        <SelectScrollUpButton />\r\n        <SelectPrimitive.Viewport\r\n          className={cn(\r\n            \"p-1\",\r\n            position === \"popper\" &&\r\n              \"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1\"\r\n          )}\r\n        >\r\n          {children}\r\n        </SelectPrimitive.Viewport>\r\n        <SelectScrollDownButton />\r\n      </SelectPrimitive.Content>\r\n    </SelectPrimitive.Portal>\r\n  )\r\n}\r\n\r\nfunction SelectLabel({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof SelectPrimitive.Label>) {\r\n  return (\r\n    <SelectPrimitive.Label\r\n      data-slot=\"select-label\"\r\n      className={cn(\"text-muted-foreground px-2 py-1.5 text-xs\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction SelectItem({\r\n  className,\r\n  children,\r\n  ...props\r\n}: React.ComponentProps<typeof SelectPrimitive.Item>) {\r\n  return (\r\n    <SelectPrimitive.Item\r\n      data-slot=\"select-item\"\r\n      className={cn(\r\n        \"focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2\",\r\n        className\r\n      )}\r\n      {...props}\r\n    >\r\n      <span className=\"absolute right-2 flex size-3.5 items-center justify-center\">\r\n        <SelectPrimitive.ItemIndicator>\r\n          <CheckIcon className=\"size-4\" />\r\n        </SelectPrimitive.ItemIndicator>\r\n      </span>\r\n      <SelectPrimitive.ItemText>{children}</SelectPrimitive.ItemText>\r\n    </SelectPrimitive.Item>\r\n  )\r\n}\r\n\r\nfunction SelectSeparator({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof SelectPrimitive.Separator>) {\r\n  return (\r\n    <SelectPrimitive.Separator\r\n      data-slot=\"select-separator\"\r\n      className={cn(\"bg-border pointer-events-none -mx-1 my-1 h-px\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction SelectScrollUpButton({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof SelectPrimitive.ScrollUpButton>) {\r\n  return (\r\n    <SelectPrimitive.ScrollUpButton\r\n      data-slot=\"select-scroll-up-button\"\r\n      className={cn(\r\n        \"flex cursor-default items-center justify-center py-1\",\r\n        className\r\n      )}\r\n      {...props}\r\n    >\r\n      <ChevronUpIcon className=\"size-4\" />\r\n    </SelectPrimitive.ScrollUpButton>\r\n  )\r\n}\r\n\r\nfunction SelectScrollDownButton({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof SelectPrimitive.ScrollDownButton>) {\r\n  return (\r\n    <SelectPrimitive.ScrollDownButton\r\n      data-slot=\"select-scroll-down-button\"\r\n      className={cn(\r\n        \"flex cursor-default items-center justify-center py-1\",\r\n        className\r\n      )}\r\n      {...props}\r\n    >\r\n      <ChevronDownIcon className=\"size-4\" />\r\n    </SelectPrimitive.ScrollDownButton>\r\n  )\r\n}\r\n\r\nexport {\r\n  Select,\r\n  SelectContent,\r\n  SelectGroup,\r\n  SelectItem,\r\n  SelectLabel,\r\n  SelectScrollDownButton,\r\n  SelectScrollUpButton,\r\n  SelectSeparator,\r\n  SelectTrigger,\r\n  SelectValue,\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAGA;AACA;AAAA;AAAA;AAEA;AAAA;AANA;;;;;AAQA,SAAS,OAAO,EACd,GAAG,OAC+C;IAClD,qBAAO,sSAAC,kRAAA,CAAA,OAAoB;QAAC,aAAU;QAAU,GAAG,KAAK;;;;;;AAC3D;KAJS;AAMT,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,sSAAC,kRAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;MAJS;AAMT,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,sSAAC,kRAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;MAJS;AAMT,SAAS,cAAc,EACrB,SAAS,EACT,OAAO,SAAS,EAChB,QAAQ,EACR,GAAG,OAGJ;IACC,qBACE,sSAAC,kRAAA,CAAA,UAAuB;QACtB,aAAU;QACV,aAAW;QACX,WAAW,CAAA,GAAA,+HAAA,CAAA,KAAE,AAAD,EACV,gzBACA;QAED,GAAG,KAAK;;YAER;0BACD,sSAAC,kRAAA,CAAA,OAAoB;gBAAC,OAAO;0BAC3B,cAAA,sSAAC,+SAAA,CAAA,kBAAe;oBAAC,WAAU;;;;;;;;;;;;;;;;;AAInC;MAxBS;AA0BT,SAAS,cAAc,EACrB,SAAS,EACT,QAAQ,EACR,WAAW,QAAQ,EACnB,GAAG,OACkD;IACrD,qBACE,sSAAC,kRAAA,CAAA,SAAsB;kBACrB,cAAA,sSAAC,kRAAA,CAAA,UAAuB;YACtB,aAAU;YACV,WAAW,CAAA,GAAA,+HAAA,CAAA,KAAE,AAAD,EACV,ijBACA,aAAa,YACX,mIACF;YAEF,UAAU;YACT,GAAG,KAAK;;8BAET,sSAAC;;;;;8BACD,sSAAC,kRAAA,CAAA,WAAwB;oBACvB,WAAW,CAAA,GAAA,+HAAA,CAAA,KAAE,AAAD,EACV,OACA,aAAa,YACX;8BAGH;;;;;;8BAEH,sSAAC;;;;;;;;;;;;;;;;AAIT;MAjCS;AAmCT,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,sSAAC,kRAAA,CAAA,QAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,+HAAA,CAAA,KAAE,AAAD,EAAE,6CAA6C;QAC1D,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,WAAW,EAClB,SAAS,EACT,QAAQ,EACR,GAAG,OAC+C;IAClD,qBACE,sSAAC,kRAAA,CAAA,OAAoB;QACnB,aAAU;QACV,WAAW,CAAA,GAAA,+HAAA,CAAA,KAAE,AAAD,EACV,6aACA;QAED,GAAG,KAAK;;0BAET,sSAAC;gBAAK,WAAU;0BACd,cAAA,sSAAC,kRAAA,CAAA,gBAA6B;8BAC5B,cAAA,sSAAC,+RAAA,CAAA,YAAS;wBAAC,WAAU;;;;;;;;;;;;;;;;0BAGzB,sSAAC,kRAAA,CAAA,WAAwB;0BAAE;;;;;;;;;;;;AAGjC;MAtBS;AAwBT,SAAS,gBAAgB,EACvB,SAAS,EACT,GAAG,OACoD;IACvD,qBACE,sSAAC,kRAAA,CAAA,YAAyB;QACxB,aAAU;QACV,WAAW,CAAA,GAAA,+HAAA,CAAA,KAAE,AAAD,EAAE,iDAAiD;QAC9D,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,qBAAqB,EAC5B,SAAS,EACT,GAAG,OACyD;IAC5D,qBACE,sSAAC,kRAAA,CAAA,iBAA8B;QAC7B,aAAU;QACV,WAAW,CAAA,GAAA,+HAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,sSAAC,2SAAA,CAAA,gBAAa;YAAC,WAAU;;;;;;;;;;;AAG/B;MAhBS;AAkBT,SAAS,uBAAuB,EAC9B,SAAS,EACT,GAAG,OAC2D;IAC9D,qBACE,sSAAC,kRAAA,CAAA,mBAAgC;QAC/B,aAAU;QACV,WAAW,CAAA,GAAA,+HAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,sSAAC,+SAAA,CAAA,kBAAe;YAAC,WAAU;;;;;;;;;;;AAGjC;MAhBS", "debugId": null}}, {"offset": {"line": 367, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/bloomgoo/gold-exchange-platform/frontend/components/common/admin/website-settings/update-settings.tsx"], "sourcesContent": ["import { toast } from 'sonner';\r\nimport { api } from '@/lib/api';\r\n\r\n/**\r\n * <PERSON>àm cập nhật cấu hình hệ thống theo nhóm\r\n * @param group Nhóm cấu hình\r\n * @param formData Dữ liệu form\r\n * @param settings Danh sách cấu hình hiện tại\r\n * @returns Promise<boolean> Kết quả cập nhật\r\n */\r\nexport async function updateSettingsByGroup(\r\n  group: string,\r\n  formData: Record<string, string | boolean | number>,\r\n  settings: Array<{\r\n    id: string;                    // ID duy nhất của cấu hình\r\n    key: string;                   // Kh<PERSON>a cấu hình (config key)\r\n    value: string;                 // Giá trị cấu hình\r\n    description?: string;          // Mô tả cấu hình (hiển thị cho admin)\r\n    group: string;                 // Nhóm cấu hình\r\n    type: 'text' | 'textarea' | 'number' | 'boolean' | 'select' | 'image' | 'icon'; // <PERSON><PERSON>i input (đã thêm 'image' và 'icon')\r\n    options?: string[];            // <PERSON><PERSON><PERSON> tùy chọn cho type 'select'\r\n    groupDisplayName?: string;     // Tên hiển thị của nhóm\r\n    groupDescription?: string;     // Mô tả của nhóm\r\n    groupIcon?: string;            // Icon của nhóm\r\n    groupOrder?: number;           // Thứ tự hiển thị của nhóm\r\n    sectionName?: string;          // Tên section trong group\r\n    sectionDisplayName?: string;   // Tên hiển thị của section\r\n    sectionDescription?: string;   // Mô tả của section\r\n    sectionOrder?: number;         // Thứ tự hiển thị của section\r\n    isGroupConfig?: boolean;       // Đánh dấu cấu hình của nhóm\r\n  }>\r\n): Promise<boolean> {\r\n  try {\r\n    // Lọc cấu hình theo nhóm\r\n    const groupSettings = settings.filter((setting) => setting.group === group);\r\n\r\n     \r\n     \r\n\r\n    try {\r\n      // Lấy danh sách cấu hình hiện tại từ API\r\n       \r\n      const response = await api.get<any>(`system-configs/group/${group}?limit=100`);\r\n       \r\n\r\n      // Lấy danh sách cấu hình hiện tại - cấu trúc mới: response.data.data.data\r\n      let existingConfigs: any[] = [];\r\n      if (response && response.data) {\r\n        if (response.data.data && response.data.data.data && Array.isArray(response.data.data.data)) {\r\n          existingConfigs = response.data.data.data;\r\n        } else if (response.data.data && Array.isArray(response.data.data)) {\r\n          existingConfigs = response.data.data;\r\n        } else if (Array.isArray(response.data)) {\r\n          existingConfigs = response.data;\r\n        } else if (response.data.data) {\r\n          existingConfigs = [response.data.data];\r\n        } else if (response.data) {\r\n          existingConfigs = [response.data];\r\n        }\r\n      }\r\n\r\n       \r\n\r\n      // Tạo map để dễ dàng tìm kiếm cấu hình theo key\r\n      const configKeyToConfigMap = new Map();\r\n      existingConfigs.forEach((config: any) => {\r\n        configKeyToConfigMap.set(config.configKey, config);\r\n      });\r\n\r\n      // Chuẩn bị dữ liệu để gửi đi\r\n      const configsToSend = [];\r\n\r\n      // Cập nhật các cấu hình\r\n      for (const setting of groupSettings) {\r\n        let value = formData[setting.key];\r\n\r\n        // Chuyển đổi boolean và number thành string cho API\r\n        if (typeof value === 'boolean') {\r\n          value = value ? 'true' : 'false';\r\n        } else if (typeof value === 'number') {\r\n          value = value.toString();\r\n        }\r\n\r\n        // Tìm cấu hình hiện tại\r\n        const existingConfig = configKeyToConfigMap.get(setting.key);\r\n\r\n        // Chuẩn bị dữ liệu theo định dạng API yêu cầu\r\n        const configToSend: any = {\r\n          configKey: setting.key,\r\n          configValue: value as string,\r\n          configGroup: setting.group,\r\n          configType: setting.type || 'text'\r\n        };\r\n\r\n        // Thêm các trường tùy chọn nếu có\r\n        if (setting.description) {\r\n          configToSend.description = setting.description;\r\n        }\r\n\r\n        if (setting.options) {\r\n          configToSend.configOptions = JSON.stringify(setting.options);\r\n        }\r\n\r\n        // Thêm thông tin hiển thị nhóm\r\n        if (setting.groupDisplayName) {\r\n          configToSend.groupDisplayName = setting.groupDisplayName;\r\n        }\r\n\r\n        if (setting.groupDescription) {\r\n          configToSend.groupDescription = setting.groupDescription;\r\n        }\r\n\r\n        if (setting.groupIcon) {\r\n          configToSend.groupIcon = setting.groupIcon;\r\n        }\r\n\r\n        if (setting.groupOrder !== undefined) {\r\n          configToSend.groupOrder = setting.groupOrder;\r\n        }\r\n\r\n        if (setting.isGroupConfig !== undefined) {\r\n          configToSend.isGroupConfig = setting.isGroupConfig;\r\n        }\r\n\r\n        // Không thêm ID vào request, API không yêu cầu ID\r\n        if (existingConfig && existingConfig.id) {\r\n           \r\n        } else {\r\n           \r\n        }\r\n\r\n        // Thêm vào danh sách cấu hình cần gửi\r\n        configsToSend.push(configToSend);\r\n      }\r\n\r\n      // Kiểm tra dữ liệu trước khi gửi\r\n       \r\n       \r\n\r\n      // Đảm bảo configsToSend là một mảng\r\n      if (!Array.isArray(configsToSend)) {\r\n        console.error('Dữ liệu không phải là mảng!');\r\n        toast.error('Lỗi cấu trúc dữ liệu');\r\n        return false;\r\n      }\r\n\r\n      // Kiểm tra xem có dữ liệu để gửi không\r\n      if (configsToSend.length === 0) {\r\n         \r\n        toast.info('Không có cấu hình để cập nhật');\r\n        return true;\r\n      }\r\n\r\n       \r\n       \r\n       \r\n\r\n      // Kiểm tra cấu trúc của từng item trong configsToSend\r\n      configsToSend.forEach((item, index) => {\r\n         \r\n\r\n        // Kiểm tra các trường bắt buộc\r\n        if (!item.configKey) {\r\n          console.error(`Item ${index} thiếu configKey!`);\r\n        }\r\n\r\n        if (!item.configValue && item.configValue !== '') {\r\n          console.warn(`Item ${index} thiếu configValue!`);\r\n        }\r\n\r\n        if (!item.configGroup) {\r\n          console.warn(`Item ${index} thiếu configGroup!`);\r\n        }\r\n\r\n        if (!item.configType) {\r\n          console.warn(`Item ${index} thiếu configType!`);\r\n        }\r\n\r\n        // Xóa ID và các trường không cần thiết để tránh lỗi với API mới\r\n        if (item.id) {\r\n          console.warn(`Item ${index} có ID, sẽ bị xóa để tránh lỗi với API mới`);\r\n          delete item.id;\r\n        }\r\n\r\n        // Xóa các trường không cần thiết\r\n        delete item.createdAt;\r\n        delete item.updatedAt;\r\n        delete item.isDeleted;\r\n        delete item.deletedBy;\r\n      });\r\n\r\n      try {\r\n        // Sử dụng endpoint POST /system-configs/bulk thay vì PUT\r\n         \r\n\r\n        const updateResponse = await api.post('system-configs/bulk', configsToSend);\r\n         \r\n        toast.success(`Đã cập nhật ${configsToSend.length} cấu hình trong nhóm \"${group}\"`);\r\n        return true;\r\n      } catch (error: any) {\r\n        console.error('Lỗi khi gọi API POST system-configs/bulk:', error);\r\n\r\n        if (error.response && error.response.data) {\r\n          const errorMessage = error.response.data.message || 'Không thể cập nhật cấu hình';\r\n          toast.error(`Lỗi: ${errorMessage}`);\r\n\r\n          // Hiển thị chi tiết lỗi nếu có\r\n          console.error('Chi tiết lỗi:', error.response.data);\r\n\r\n          // Hiển thị lỗi validation nếu có\r\n          if (error.response.data.data && error.response.data.data.validationErrors) {\r\n            const validationErrors = error.response.data.data.validationErrors;\r\n            console.error('Lỗi validation:', validationErrors);\r\n            toast.error(`Lỗi validation: ${validationErrors.join(', ')}`);\r\n          }\r\n        } else {\r\n          toast.error('Không thể cập nhật cấu hình: ' + (error.message || 'Lỗi không xác định'));\r\n        }\r\n\r\n        return false;\r\n      }\r\n    } catch (error: any) {\r\n      console.error('Lỗi khi gọi API PUT system-configs/bulk:', error);\r\n      console.error('Chi tiết lỗi:', error.response?.data || error.message);\r\n\r\n      // Hiển thị thông báo lỗi chi tiết\r\n      if (error.response && error.response.data) {\r\n        const errorMessage = error.response.data.message || 'Không thể cập nhật cấu hình';\r\n        toast.error(`Lỗi: ${errorMessage}`);\r\n\r\n        // Hiển thị lỗi validation nếu có\r\n        if (error.response.data.data && error.response.data.data.validationErrors) {\r\n          const validationErrors = error.response.data.data.validationErrors;\r\n          console.error('Lỗi validation:', validationErrors);\r\n          toast.error(`Lỗi validation: ${validationErrors.join(', ')}`);\r\n        }\r\n      } else {\r\n        toast.error('Không thể cập nhật cấu hình');\r\n      }\r\n\r\n      return false;\r\n    }\r\n  } catch (error: any) {\r\n    console.error('Lỗi khi cập nhật cấu hình:', error);\r\n\r\n    // Hiển thị thông báo lỗi chi tiết\r\n    if (error.response && error.response.data) {\r\n      console.error('Chi tiết lỗi:', error.response.data);\r\n\r\n      // Hiển thị lỗi validation nếu có\r\n      if (error.response.data.data && error.response.data.data.validationErrors) {\r\n        const validationErrors = error.response.data.data.validationErrors;\r\n        toast.error(`Lỗi validation: ${validationErrors.join(', ')}`);\r\n      } else {\r\n        toast.error(`Lỗi: ${error.response.data.message || 'Không thể cập nhật cấu hình'}`);\r\n      }\r\n    } else if (error.message) {\r\n      // Hiển thị thông báo lỗi từ JavaScript\r\n      toast.error(`Lỗi: ${error.message}`);\r\n      console.error('Stack trace:', error.stack);\r\n    } else {\r\n      toast.error('Không thể cập nhật cấu hình');\r\n    }\r\n\r\n    return false;\r\n  }\r\n}\r\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AASO,eAAe,sBACpB,KAAa,EACb,QAAmD,EACnD,QAiBE;IAEF,IAAI;QACF,yBAAyB;QACzB,MAAM,gBAAgB,SAAS,MAAM,CAAC,CAAC,UAAY,QAAQ,KAAK,KAAK;QAKrE,IAAI;YACF,yCAAyC;YAEzC,MAAM,WAAW,MAAM,6GAAA,CAAA,MAAG,CAAC,GAAG,CAAM,CAAC,qBAAqB,EAAE,MAAM,UAAU,CAAC;YAG7E,0EAA0E;YAC1E,IAAI,kBAAyB,EAAE;YAC/B,IAAI,YAAY,SAAS,IAAI,EAAE;gBAC7B,IAAI,SAAS,IAAI,CAAC,IAAI,IAAI,SAAS,IAAI,CAAC,IAAI,CAAC,IAAI,IAAI,MAAM,OAAO,CAAC,SAAS,IAAI,CAAC,IAAI,CAAC,IAAI,GAAG;oBAC3F,kBAAkB,SAAS,IAAI,CAAC,IAAI,CAAC,IAAI;gBAC3C,OAAO,IAAI,SAAS,IAAI,CAAC,IAAI,IAAI,MAAM,OAAO,CAAC,SAAS,IAAI,CAAC,IAAI,GAAG;oBAClE,kBAAkB,SAAS,IAAI,CAAC,IAAI;gBACtC,OAAO,IAAI,MAAM,OAAO,CAAC,SAAS,IAAI,GAAG;oBACvC,kBAAkB,SAAS,IAAI;gBACjC,OAAO,IAAI,SAAS,IAAI,CAAC,IAAI,EAAE;oBAC7B,kBAAkB;wBAAC,SAAS,IAAI,CAAC,IAAI;qBAAC;gBACxC,OAAO,IAAI,SAAS,IAAI,EAAE;oBACxB,kBAAkB;wBAAC,SAAS,IAAI;qBAAC;gBACnC;YACF;YAIA,gDAAgD;YAChD,MAAM,uBAAuB,IAAI;YACjC,gBAAgB,OAAO,CAAC,CAAC;gBACvB,qBAAqB,GAAG,CAAC,OAAO,SAAS,EAAE;YAC7C;YAEA,6BAA6B;YAC7B,MAAM,gBAAgB,EAAE;YAExB,wBAAwB;YACxB,KAAK,MAAM,WAAW,cAAe;gBACnC,IAAI,QAAQ,QAAQ,CAAC,QAAQ,GAAG,CAAC;gBAEjC,oDAAoD;gBACpD,IAAI,OAAO,UAAU,WAAW;oBAC9B,QAAQ,QAAQ,SAAS;gBAC3B,OAAO,IAAI,OAAO,UAAU,UAAU;oBACpC,QAAQ,MAAM,QAAQ;gBACxB;gBAEA,wBAAwB;gBACxB,MAAM,iBAAiB,qBAAqB,GAAG,CAAC,QAAQ,GAAG;gBAE3D,8CAA8C;gBAC9C,MAAM,eAAoB;oBACxB,WAAW,QAAQ,GAAG;oBACtB,aAAa;oBACb,aAAa,QAAQ,KAAK;oBAC1B,YAAY,QAAQ,IAAI,IAAI;gBAC9B;gBAEA,kCAAkC;gBAClC,IAAI,QAAQ,WAAW,EAAE;oBACvB,aAAa,WAAW,GAAG,QAAQ,WAAW;gBAChD;gBAEA,IAAI,QAAQ,OAAO,EAAE;oBACnB,aAAa,aAAa,GAAG,KAAK,SAAS,CAAC,QAAQ,OAAO;gBAC7D;gBAEA,+BAA+B;gBAC/B,IAAI,QAAQ,gBAAgB,EAAE;oBAC5B,aAAa,gBAAgB,GAAG,QAAQ,gBAAgB;gBAC1D;gBAEA,IAAI,QAAQ,gBAAgB,EAAE;oBAC5B,aAAa,gBAAgB,GAAG,QAAQ,gBAAgB;gBAC1D;gBAEA,IAAI,QAAQ,SAAS,EAAE;oBACrB,aAAa,SAAS,GAAG,QAAQ,SAAS;gBAC5C;gBAEA,IAAI,QAAQ,UAAU,KAAK,WAAW;oBACpC,aAAa,UAAU,GAAG,QAAQ,UAAU;gBAC9C;gBAEA,IAAI,QAAQ,aAAa,KAAK,WAAW;oBACvC,aAAa,aAAa,GAAG,QAAQ,aAAa;gBACpD;gBAEA,kDAAkD;gBAClD,IAAI,kBAAkB,eAAe,EAAE,EAAE,CAEzC,OAAO,CAEP;gBAEA,sCAAsC;gBACtC,cAAc,IAAI,CAAC;YACrB;YAEA,iCAAiC;YAIjC,oCAAoC;YACpC,IAAI,CAAC,MAAM,OAAO,CAAC,gBAAgB;gBACjC,QAAQ,KAAK,CAAC;gBACd,2QAAA,CAAA,QAAK,CAAC,KAAK,CAAC;gBACZ,OAAO;YACT;YAEA,uCAAuC;YACvC,IAAI,cAAc,MAAM,KAAK,GAAG;gBAE9B,2QAAA,CAAA,QAAK,CAAC,IAAI,CAAC;gBACX,OAAO;YACT;YAMA,sDAAsD;YACtD,cAAc,OAAO,CAAC,CAAC,MAAM;gBAG3B,+BAA+B;gBAC/B,IAAI,CAAC,KAAK,SAAS,EAAE;oBACnB,QAAQ,KAAK,CAAC,CAAC,KAAK,EAAE,MAAM,iBAAiB,CAAC;gBAChD;gBAEA,IAAI,CAAC,KAAK,WAAW,IAAI,KAAK,WAAW,KAAK,IAAI;oBAChD,QAAQ,IAAI,CAAC,CAAC,KAAK,EAAE,MAAM,mBAAmB,CAAC;gBACjD;gBAEA,IAAI,CAAC,KAAK,WAAW,EAAE;oBACrB,QAAQ,IAAI,CAAC,CAAC,KAAK,EAAE,MAAM,mBAAmB,CAAC;gBACjD;gBAEA,IAAI,CAAC,KAAK,UAAU,EAAE;oBACpB,QAAQ,IAAI,CAAC,CAAC,KAAK,EAAE,MAAM,kBAAkB,CAAC;gBAChD;gBAEA,gEAAgE;gBAChE,IAAI,KAAK,EAAE,EAAE;oBACX,QAAQ,IAAI,CAAC,CAAC,KAAK,EAAE,MAAM,0CAA0C,CAAC;oBACtE,OAAO,KAAK,EAAE;gBAChB;gBAEA,iCAAiC;gBACjC,OAAO,KAAK,SAAS;gBACrB,OAAO,KAAK,SAAS;gBACrB,OAAO,KAAK,SAAS;gBACrB,OAAO,KAAK,SAAS;YACvB;YAEA,IAAI;gBACF,yDAAyD;gBAGzD,MAAM,iBAAiB,MAAM,6GAAA,CAAA,MAAG,CAAC,IAAI,CAAC,uBAAuB;gBAE7D,2QAAA,CAAA,QAAK,CAAC,OAAO,CAAC,CAAC,YAAY,EAAE,cAAc,MAAM,CAAC,sBAAsB,EAAE,MAAM,CAAC,CAAC;gBAClF,OAAO;YACT,EAAE,OAAO,OAAY;gBACnB,QAAQ,KAAK,CAAC,6CAA6C;gBAE3D,IAAI,MAAM,QAAQ,IAAI,MAAM,QAAQ,CAAC,IAAI,EAAE;oBACzC,MAAM,eAAe,MAAM,QAAQ,CAAC,IAAI,CAAC,OAAO,IAAI;oBACpD,2QAAA,CAAA,QAAK,CAAC,KAAK,CAAC,CAAC,KAAK,EAAE,cAAc;oBAElC,+BAA+B;oBAC/B,QAAQ,KAAK,CAAC,iBAAiB,MAAM,QAAQ,CAAC,IAAI;oBAElD,iCAAiC;oBACjC,IAAI,MAAM,QAAQ,CAAC,IAAI,CAAC,IAAI,IAAI,MAAM,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,gBAAgB,EAAE;wBACzE,MAAM,mBAAmB,MAAM,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,gBAAgB;wBAClE,QAAQ,KAAK,CAAC,mBAAmB;wBACjC,2QAAA,CAAA,QAAK,CAAC,KAAK,CAAC,CAAC,gBAAgB,EAAE,iBAAiB,IAAI,CAAC,OAAO;oBAC9D;gBACF,OAAO;oBACL,2QAAA,CAAA,QAAK,CAAC,KAAK,CAAC,kCAAkC,CAAC,MAAM,OAAO,IAAI,oBAAoB;gBACtF;gBAEA,OAAO;YACT;QACF,EAAE,OAAO,OAAY;YACnB,QAAQ,KAAK,CAAC,4CAA4C;YAC1D,QAAQ,KAAK,CAAC,iBAAiB,MAAM,QAAQ,EAAE,QAAQ,MAAM,OAAO;YAEpE,kCAAkC;YAClC,IAAI,MAAM,QAAQ,IAAI,MAAM,QAAQ,CAAC,IAAI,EAAE;gBACzC,MAAM,eAAe,MAAM,QAAQ,CAAC,IAAI,CAAC,OAAO,IAAI;gBACpD,2QAAA,CAAA,QAAK,CAAC,KAAK,CAAC,CAAC,KAAK,EAAE,cAAc;gBAElC,iCAAiC;gBACjC,IAAI,MAAM,QAAQ,CAAC,IAAI,CAAC,IAAI,IAAI,MAAM,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,gBAAgB,EAAE;oBACzE,MAAM,mBAAmB,MAAM,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,gBAAgB;oBAClE,QAAQ,KAAK,CAAC,mBAAmB;oBACjC,2QAAA,CAAA,QAAK,CAAC,KAAK,CAAC,CAAC,gBAAgB,EAAE,iBAAiB,IAAI,CAAC,OAAO;gBAC9D;YACF,OAAO;gBACL,2QAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACd;YAEA,OAAO;QACT;IACF,EAAE,OAAO,OAAY;QACnB,QAAQ,KAAK,CAAC,8BAA8B;QAE5C,kCAAkC;QAClC,IAAI,MAAM,QAAQ,IAAI,MAAM,QAAQ,CAAC,IAAI,EAAE;YACzC,QAAQ,KAAK,CAAC,iBAAiB,MAAM,QAAQ,CAAC,IAAI;YAElD,iCAAiC;YACjC,IAAI,MAAM,QAAQ,CAAC,IAAI,CAAC,IAAI,IAAI,MAAM,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,gBAAgB,EAAE;gBACzE,MAAM,mBAAmB,MAAM,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,gBAAgB;gBAClE,2QAAA,CAAA,QAAK,CAAC,KAAK,CAAC,CAAC,gBAAgB,EAAE,iBAAiB,IAAI,CAAC,OAAO;YAC9D,OAAO;gBACL,2QAAA,CAAA,QAAK,CAAC,KAAK,CAAC,CAAC,KAAK,EAAE,MAAM,QAAQ,CAAC,IAAI,CAAC,OAAO,IAAI,+BAA+B;YACpF;QACF,OAAO,IAAI,MAAM,OAAO,EAAE;YACxB,uCAAuC;YACvC,2QAAA,CAAA,QAAK,CAAC,KAAK,CAAC,CAAC,KAAK,EAAE,MAAM,OAAO,EAAE;YACnC,QAAQ,KAAK,CAAC,gBAAgB,MAAM,KAAK;QAC3C,OAAO;YACL,2QAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd;QAEA,OAAO;IACT;AACF", "debugId": null}}, {"offset": {"line": 563, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/bloomgoo/gold-exchange-platform/frontend/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction Card({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card\"\r\n      className={cn(\r\n        \"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CardHeader({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-header\"\r\n      className={cn(\r\n        \"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CardTitle({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-title\"\r\n      className={cn(\"leading-none font-semibold\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CardDescription({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-description\"\r\n      className={cn(\"text-muted-foreground text-sm\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CardAction({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-action\"\r\n      className={cn(\r\n        \"col-start-2 row-span-2 row-start-1 self-start justify-self-end\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CardContent({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-content\"\r\n      className={cn(\"px-6\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CardFooter({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-footer\"\r\n      className={cn(\"flex items-center px-6 [.border-t]:pt-6\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport {\r\n  Card,\r\n  CardHeader,\r\n  CardFooter,\r\n  CardTitle,\r\n  CardAction,\r\n  CardDescription,\r\n  CardContent,\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;AAAA;;;AAEA,SAAS,KAAK,EAAE,SAAS,EAAE,GAAG,OAAoC;IAChE,qBACE,sSAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,+HAAA,CAAA,KAAE,AAAD,EACV,qFACA;QAED,GAAG,KAAK;;;;;;AAGf;KAXS;AAaT,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,sSAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,+HAAA,CAAA,KAAE,AAAD,EACV,8JACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAoC;IACrE,qBACE,sSAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,+HAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC3E,qBACE,sSAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,+HAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,sSAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,+HAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,sSAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,+HAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QACrB,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,sSAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,+HAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf;MARS", "debugId": null}}, {"offset": {"line": 679, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/bloomgoo/gold-exchange-platform/frontend/components/ui/tabs.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as TabsPrimitive from \"@radix-ui/react-tabs\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction Tabs({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof TabsPrimitive.Root>) {\r\n  return (\r\n    <TabsPrimitive.Root\r\n      data-slot=\"tabs\"\r\n      className={cn(\"flex flex-col gap-2\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction TabsList({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof TabsPrimitive.List>) {\r\n  return (\r\n    <TabsPrimitive.List\r\n      data-slot=\"tabs-list\"\r\n      className={cn(\r\n        \"bg-muted text-muted-foreground inline-flex h-9 w-fit items-center justify-center rounded-lg p-[3px]\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction TabsTrigger({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof TabsPrimitive.Trigger>) {\r\n  return (\r\n    <TabsPrimitive.Trigger\r\n      data-slot=\"tabs-trigger\"\r\n      className={cn(\r\n        \"data-[state=active]:bg-background dark:data-[state=active]:text-foreground focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:outline-ring dark:data-[state=active]:border-input dark:data-[state=active]:bg-input/30 text-foreground dark:text-muted-foreground inline-flex h-[calc(100%-1px)] flex-1 items-center justify-center gap-1.5 rounded-md border border-transparent px-2 py-1 text-sm font-medium whitespace-nowrap transition-[color,box-shadow] focus-visible:ring-[3px] focus-visible:outline-1 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:shadow-sm [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction TabsContent({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof TabsPrimitive.Content>) {\r\n  return (\r\n    <TabsPrimitive.Content\r\n      data-slot=\"tabs-content\"\r\n      className={cn(\"flex-1 outline-none\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport { Tabs, TabsList, TabsTrigger, TabsContent }\r\n"], "names": [], "mappings": ";;;;;;;AAGA;AAEA;AAAA;AALA;;;;AAOA,SAAS,KAAK,EACZ,SAAS,EACT,GAAG,OAC6C;IAChD,qBACE,sSAAC,mRAAA,CAAA,OAAkB;QACjB,aAAU;QACV,WAAW,CAAA,GAAA,+HAAA,CAAA,KAAE,AAAD,EAAE,uBAAuB;QACpC,GAAG,KAAK;;;;;;AAGf;KAXS;AAaT,SAAS,SAAS,EAChB,SAAS,EACT,GAAG,OAC6C;IAChD,qBACE,sSAAC,mRAAA,CAAA,OAAkB;QACjB,aAAU;QACV,WAAW,CAAA,GAAA,+HAAA,CAAA,KAAE,AAAD,EACV,uGACA;QAED,GAAG,KAAK;;;;;;AAGf;MAdS;AAgBT,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,sSAAC,mRAAA,CAAA,UAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,+HAAA,CAAA,KAAE,AAAD,EACV,mqBACA;QAED,GAAG,KAAK;;;;;;AAGf;MAdS;AAgBT,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,sSAAC,mRAAA,CAAA,UAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,+HAAA,CAAA,KAAE,AAAD,EAAE,uBAAuB;QACpC,GAAG,KAAK;;;;;;AAGf;MAXS", "debugId": null}}, {"offset": {"line": 756, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/bloomgoo/gold-exchange-platform/frontend/components/ui/image-upload.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { Button } from '@/components/ui/button';\r\nimport { Input } from '@/components/ui/input';\r\nimport { Card, CardContent } from '@/components/ui/card';\r\nimport { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';\r\nimport { cn } from '@/lib/utils';\r\nimport { Loader2, Upload, X } from 'lucide-react';\r\nimport React, { useCallback, useRef, useState } from 'react';\r\nimport { toast } from 'sonner';\r\n\r\nexport interface FileUploadResult {\r\n  id: string;\r\n  url?: string;\r\n  filename?: string;\r\n}\r\n\r\ninterface ImageUploadProps {\r\n  // Required props\r\n  value?: string;\r\n  onChange: (url: string) => void;\r\n\r\n  // Upload configuration\r\n  endpoint?: string; // API endpoint for file upload\r\n  maxSize?: number; // in bytes, default 5MB\r\n  allowedTypes?: string[]; // MIME types, default ['image/jpeg', 'image/png', 'image/jpg', 'image/webp']\r\n\r\n  // Event handlers\r\n  onUploadSuccess?: (result: FileUploadResult) => void;\r\n  onUploadError?: (error: Error) => void;\r\n  onRemove?: () => void;\r\n\r\n  // UI props\r\n  disabled?: boolean;\r\n  className?: string;\r\n  accept?: string; // HTML accept attribute\r\n  placeholder?: string;\r\n\r\n  // Validation\r\n  validateFile?: (file: File) => string | null; // return error message or null if valid\r\n  validateBeforeUpload?: () => string | null; // return error message or null if valid\r\n}\r\n\r\nexport function ImageUpload({\r\n  value,\r\n  onChange,\r\n  endpoint,\r\n  maxSize = 5 * 1024 * 1024, // 5MB in bytes\r\n  allowedTypes: propAllowedTypes,\r\n  onUploadSuccess,\r\n  onUploadError,\r\n  onRemove,\r\n  disabled = false,\r\n  className,\r\n  accept = \"image/*\",\r\n  placeholder,\r\n  validateFile,\r\n  validateBeforeUpload,\r\n}: ImageUploadProps) {\r\n  const [isUploading, setIsUploading] = useState(false);\r\n  const [urlInput, setUrlInput] = useState('');\r\n  const [dragActive, setDragActive] = useState(false);\r\n  const [preview, setPreview] = useState<string>('');\r\n  const fileInputRef = useRef<HTMLInputElement>(null);\r\n\r\n  // Convert accept attribute to allowedTypes if provided\r\n  const allowedTypes = propAllowedTypes || (() => {\r\n    if (accept && accept !== 'image/*') {\r\n      return accept.split(',').map(type => type.trim());\r\n    }\r\n    return ['image/jpeg', 'image/png', 'image/jpg', 'image/webp', 'image/gif'];\r\n  })();\r\n\r\n  // Handle file validation\r\n  const validateFileInternal = useCallback((file: File): string | null => {\r\n    // Custom validation first\r\n    if (validateFile) {\r\n      const customError = validateFile(file);\r\n      if (customError) return customError;\r\n    }\r\n\r\n    // Size validation\r\n    if (file.size > maxSize) {\r\n      const maxSizeMB = Math.round(maxSize / (1024 * 1024));\r\n      return `Kích thước file không được vượt quá ${maxSizeMB}MB`;\r\n    }\r\n\r\n    // Type validation\r\n    if (!allowedTypes.includes(file.type)) {\r\n      const typeNames = allowedTypes.map((type: string) => {\r\n        switch (type) {\r\n          case 'image/jpeg': return 'JPEG';\r\n          case 'image/jpg': return 'JPG';\r\n          case 'image/png': return 'PNG';\r\n          case 'image/webp': return 'WebP';\r\n          case 'image/gif': return 'GIF';\r\n          default: return type.split('/')[1]?.toUpperCase() || type;\r\n        }\r\n      }).join(', ');\r\n      return `Chỉ chấp nhận file ảnh định dạng ${typeNames}`;\r\n    }\r\n\r\n    return null;\r\n  }, [validateFile, maxSize, allowedTypes]);\r\n\r\n  const handleFileSelect = async (file: File) => {\r\n    // Pre-upload validation\r\n    if (validateBeforeUpload) {\r\n      const preUploadError = validateBeforeUpload();\r\n      if (preUploadError) {\r\n        toast.error(preUploadError);\r\n        return;\r\n      }\r\n    }\r\n\r\n    // Validate file\r\n    const error = validateFileInternal(file);\r\n    if (error) {\r\n      toast.error(error);\r\n      return;\r\n    }\r\n\r\n    setIsUploading(true);\r\n\r\n    try {\r\n      // Create preview\r\n      const reader = new FileReader();\r\n      const previewPromise = new Promise<string>((resolve) => {\r\n        reader.onload = (e) => resolve(e.target?.result as string);\r\n        reader.readAsDataURL(file);\r\n      });\r\n\r\n      const previewUrl = await previewPromise;\r\n      setPreview(previewUrl);\r\n\r\n      // If no endpoint provided, just use the preview URL (for backward compatibility)\r\n      if (!endpoint) {\r\n        onChange(previewUrl);\r\n        toast.success('Tải lên file thành công');\r\n        return;\r\n      }\r\n\r\n      // Upload file to server\r\n      const formData = new FormData();\r\n      formData.append('file', file);\r\n\r\n      const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3000/api/v1'}${endpoint}`, {\r\n        method: 'POST',\r\n        headers: {\r\n          'Authorization': `Bearer ${localStorage.getItem('accessToken')}`,\r\n        },\r\n        body: formData,\r\n      });\r\n\r\n      if (!response.ok) {\r\n        throw new Error(`Upload failed with status: ${response.status}`);\r\n      }\r\n\r\n      const result = await response.json();\r\n\r\n      if (!result || !result.data) {\r\n        throw new Error('Invalid response from server');\r\n      }\r\n\r\n      const uploadResult: FileUploadResult = {\r\n        id: result.data.id,\r\n        url: result.data.url,\r\n        filename: result.data.filename || file.name,\r\n      };\r\n\r\n      // Update value with server URL\r\n      onChange(uploadResult.url || uploadResult.id);\r\n      onUploadSuccess?.(uploadResult);\r\n      toast.success('Tải lên file thành công');\r\n\r\n    } catch (error: any) {\r\n      console.error('Upload failed:', error);\r\n      const errorMessage = error.message || 'Có lỗi xảy ra khi tải lên file';\r\n      toast.error(errorMessage);\r\n      onUploadError?.(error);\r\n\r\n      // Clear preview on error\r\n      setPreview('');\r\n    } finally {\r\n      setIsUploading(false);\r\n      // Reset file input\r\n      if (fileInputRef.current) {\r\n        fileInputRef.current.value = '';\r\n      }\r\n    }\r\n  };\r\n\r\n  const handleDrop = useCallback((e: React.DragEvent) => {\r\n    e.preventDefault();\r\n    e.stopPropagation();\r\n    setDragActive(false);\r\n\r\n    if (disabled) return;\r\n\r\n    const files = Array.from(e.dataTransfer.files);\r\n    if (files.length > 0 && files[0].type.startsWith('image/')) {\r\n      handleFileSelect(files[0]);\r\n    }\r\n  }, [disabled]);\r\n\r\n  const handleDragOver = useCallback((e: React.DragEvent) => {\r\n    e.preventDefault();\r\n    e.stopPropagation();\r\n    if (!disabled) {\r\n      setDragActive(true);\r\n    }\r\n  }, [disabled]);\r\n\r\n  const handleDragLeave = useCallback((e: React.DragEvent) => {\r\n    e.preventDefault();\r\n    e.stopPropagation();\r\n    setDragActive(false);\r\n  }, []);\r\n\r\n  const handleFileInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {\r\n    const files = e.target.files;\r\n    if (files && files[0]) {\r\n      handleFileSelect(files[0]);\r\n    }\r\n  };\r\n\r\n  const handleUrlSubmit = () => {\r\n    if (urlInput.trim()) {\r\n      onChange(urlInput.trim());\r\n      setUrlInput('');\r\n    }\r\n  };\r\n\r\n  const handleRemove = () => {\r\n    onChange('');\r\n    setPreview('');\r\n    onRemove?.();\r\n  };\r\n\r\n  // Get display image URL (prefer preview for uploaded files, then value)\r\n  const displayImageUrl = preview || value;\r\n\r\n  return (\r\n    <div className={cn(\"space-y-2\", className)}>\r\n      {displayImageUrl ? (\r\n        // Preview existing image - theo pattern của file-upload.tsx\r\n        <div className=\"relative\">\r\n          <img\r\n            src={displayImageUrl}\r\n            alt=\"Preview\"\r\n            className=\"w-full h-40 object-contain rounded-md border\"\r\n            onError={(e) => {\r\n              // Thay thế bằng data URL placeholder thay vì file không tồn tại\r\n              e.currentTarget.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgdmlld0JveD0iMCAwIDIwMCAyMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIyMDAiIGhlaWdodD0iMjAwIiBmaWxsPSIjRjNGNEY2Ii8+CjxwYXRoIGQ9Ik0xMDAgMTAwTDEyNSA3NUwxNzUgMTI1SDE3NVYxNzVIMjVWMTI1TDc1IDc1TDEwMCAxMDBaIiBmaWxsPSIjOUNBM0FGIi8+CjxjaXJjbGUgY3g9IjE0NSIgY3k9IjY1IiByPSIxNSIgZmlsbD0iIzlDQTNBRiIvPgo8L3N2Zz4K';\r\n            }}\r\n          />\r\n          {!disabled && (\r\n            <Button\r\n              type=\"button\"\r\n              variant=\"destructive\"\r\n              size=\"icon\"\r\n              className=\"absolute top-2 right-2 h-8 w-8\"\r\n              onClick={handleRemove}\r\n              disabled={isUploading}\r\n              aria-label=\"Xóa hình ảnh\"\r\n            >\r\n              <X className=\"h-4 w-4\" />\r\n            </Button>\r\n          )}\r\n          {isUploading && (\r\n            <div className=\"absolute inset-0 bg-black/50 rounded-md flex items-center justify-center\">\r\n              <div className=\"text-white text-center\">\r\n                <Loader2 className=\"h-8 w-8 animate-spin mx-auto mb-2\" />\r\n                <p className=\"text-sm\">Đang tải lên...</p>\r\n              </div>\r\n            </div>\r\n          )}\r\n        </div>\r\n      ) : (\r\n        // Upload interface - theo pattern của file-upload.tsx\r\n        <Tabs defaultValue=\"upload\" className=\"w-full\">\r\n          <TabsList className=\"grid w-full grid-cols-2\">\r\n            <TabsTrigger value=\"upload\">Tải lên</TabsTrigger>\r\n            <TabsTrigger value=\"url\">URL</TabsTrigger>\r\n          </TabsList>\r\n\r\n          <TabsContent value=\"upload\" className=\"space-y-2\">\r\n            <input\r\n              ref={fileInputRef}\r\n              type=\"file\"\r\n              accept={accept}\r\n              onChange={handleFileInputChange}\r\n              className=\"hidden\"\r\n              disabled={disabled}\r\n            />\r\n\r\n            <Card\r\n              className={cn(\r\n                \"transition-colors cursor-pointer\",\r\n                dragActive && \"border-primary bg-primary/5\",\r\n                disabled && \"cursor-not-allowed opacity-50\"\r\n              )}\r\n              onDrop={handleDrop}\r\n              onDragOver={handleDragOver}\r\n              onDragLeave={handleDragLeave}\r\n              onClick={!disabled ? () => fileInputRef.current?.click() : undefined}\r\n            >\r\n              <CardContent className=\"p-4\">\r\n                <div className=\"flex flex-col items-center justify-center space-y-2\">\r\n                  <Upload className={cn(\r\n                    \"h-8 w-8 text-muted-foreground transition-colors\",\r\n                    dragActive && \"text-primary\"\r\n                  )} />\r\n                  <p className=\"text-sm text-muted-foreground text-center\">\r\n                    {disabled\r\n                      ? 'Không có hình ảnh'\r\n                      : dragActive\r\n                        ? 'Thả hình ảnh vào đây'\r\n                        : placeholder || 'Kéo thả hình ảnh vào đây hoặc click để chọn'}\r\n                  </p>\r\n                  {!disabled && (\r\n                    <Button\r\n                      type=\"button\"\r\n                      variant=\"outline\"\r\n                      size=\"sm\"\r\n                      disabled={isUploading}\r\n                      onClick={(e) => {\r\n                        e.stopPropagation();\r\n                        fileInputRef.current?.click();\r\n                      }}\r\n                    >\r\n                      {isUploading ? (\r\n                        <>\r\n                          <Loader2 className=\"mr-2 h-4 w-4 animate-spin\" />\r\n                          Đang tải lên...\r\n                        </>\r\n                      ) : (\r\n                        'Chọn hình ảnh'\r\n                      )}\r\n                    </Button>\r\n                  )}\r\n                  {!disabled && (\r\n                    <p className=\"text-xs text-muted-foreground\">\r\n                      Tối đa {Math.round(maxSize / (1024 * 1024))}MB\r\n                    </p>\r\n                  )}\r\n                </div>\r\n              </CardContent>\r\n            </Card>\r\n          </TabsContent>\r\n\r\n          <TabsContent value=\"url\" className=\"space-y-2\">\r\n            <div className=\"flex gap-2\">\r\n              <Input\r\n                placeholder={placeholder || \"https://example.com/image.jpg\"}\r\n                value={urlInput}\r\n                onChange={(e) => setUrlInput(e.target.value)}\r\n                disabled={disabled}\r\n                onKeyDown={(e) => {\r\n                  if (e.key === 'Enter') {\r\n                    e.preventDefault();\r\n                    handleUrlSubmit();\r\n                  }\r\n                }}\r\n              />\r\n              <Button\r\n                type=\"button\"\r\n                onClick={handleUrlSubmit}\r\n                disabled={!urlInput.trim() || disabled}\r\n              >\r\n                Thêm\r\n              </Button>\r\n            </div>\r\n          </TabsContent>\r\n        </Tabs>\r\n      )}\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;AAkJsC;;AAhJtC;AACA;AACA;AACA;AACA;AAAA;AACA;AAAA;AAAA;AACA;AACA;;;AATA;;;;;;;;;AA2CO,SAAS,YAAY,EAC1B,KAAK,EACL,QAAQ,EACR,QAAQ,EACR,UAAU,IAAI,OAAO,IAAI,EACzB,cAAc,gBAAgB,EAC9B,eAAe,EACf,aAAa,EACb,QAAQ,EACR,WAAW,KAAK,EAChB,SAAS,EACT,SAAS,SAAS,EAClB,WAAW,EACX,YAAY,EACZ,oBAAoB,EACH;;IACjB,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,sQAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,sQAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,sQAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,sQAAA,CAAA,WAAQ,AAAD,EAAU;IAC/C,MAAM,eAAe,CAAA,GAAA,sQAAA,CAAA,SAAM,AAAD,EAAoB;IAE9C,uDAAuD;IACvD,MAAM,eAAe,oBAAoB,CAAC;QACxC,IAAI,UAAU,WAAW,WAAW;YAClC,OAAO,OAAO,KAAK,CAAC,KAAK,GAAG,CAAC,CAAA,OAAQ,KAAK,IAAI;QAChD;QACA,OAAO;YAAC;YAAc;YAAa;YAAa;YAAc;SAAY;IAC5E,CAAC;IAED,yBAAyB;IACzB,MAAM,uBAAuB,CAAA,GAAA,sQAAA,CAAA,cAAW,AAAD;yDAAE,CAAC;YACxC,0BAA0B;YAC1B,IAAI,cAAc;gBAChB,MAAM,cAAc,aAAa;gBACjC,IAAI,aAAa,OAAO;YAC1B;YAEA,kBAAkB;YAClB,IAAI,KAAK,IAAI,GAAG,SAAS;gBACvB,MAAM,YAAY,KAAK,KAAK,CAAC,UAAU,CAAC,OAAO,IAAI;gBACnD,OAAO,CAAC,oCAAoC,EAAE,UAAU,EAAE,CAAC;YAC7D;YAEA,kBAAkB;YAClB,IAAI,CAAC,aAAa,QAAQ,CAAC,KAAK,IAAI,GAAG;gBACrC,MAAM,YAAY,aAAa,GAAG;+EAAC,CAAC;wBAClC,OAAQ;4BACN,KAAK;gCAAc,OAAO;4BAC1B,KAAK;gCAAa,OAAO;4BACzB,KAAK;gCAAa,OAAO;4BACzB,KAAK;gCAAc,OAAO;4BAC1B,KAAK;gCAAa,OAAO;4BACzB;gCAAS,OAAO,KAAK,KAAK,CAAC,IAAI,CAAC,EAAE,EAAE,iBAAiB;wBACvD;oBACF;8EAAG,IAAI,CAAC;gBACR,OAAO,CAAC,iCAAiC,EAAE,WAAW;YACxD;YAEA,OAAO;QACT;wDAAG;QAAC;QAAc;QAAS;KAAa;IAExC,MAAM,mBAAmB,OAAO;QAC9B,wBAAwB;QACxB,IAAI,sBAAsB;YACxB,MAAM,iBAAiB;YACvB,IAAI,gBAAgB;gBAClB,2QAAA,CAAA,QAAK,CAAC,KAAK,CAAC;gBACZ;YACF;QACF;QAEA,gBAAgB;QAChB,MAAM,QAAQ,qBAAqB;QACnC,IAAI,OAAO;YACT,2QAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ;QACF;QAEA,eAAe;QAEf,IAAI;YACF,iBAAiB;YACjB,MAAM,SAAS,IAAI;YACnB,MAAM,iBAAiB,IAAI,QAAgB,CAAC;gBAC1C,OAAO,MAAM,GAAG,CAAC,IAAM,QAAQ,EAAE,MAAM,EAAE;gBACzC,OAAO,aAAa,CAAC;YACvB;YAEA,MAAM,aAAa,MAAM;YACzB,WAAW;YAEX,iFAAiF;YACjF,IAAI,CAAC,UAAU;gBACb,SAAS;gBACT,2QAAA,CAAA,QAAK,CAAC,OAAO,CAAC;gBACd;YACF;YAEA,wBAAwB;YACxB,MAAM,WAAW,IAAI;YACrB,SAAS,MAAM,CAAC,QAAQ;YAExB,MAAM,WAAW,MAAM,MAAM,GAAG,oEAAmC,iCAAiC,UAAU,EAAE;gBAC9G,QAAQ;gBACR,SAAS;oBACP,iBAAiB,CAAC,OAAO,EAAE,aAAa,OAAO,CAAC,gBAAgB;gBAClE;gBACA,MAAM;YACR;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,CAAC,2BAA2B,EAAE,SAAS,MAAM,EAAE;YACjE;YAEA,MAAM,SAAS,MAAM,SAAS,IAAI;YAElC,IAAI,CAAC,UAAU,CAAC,OAAO,IAAI,EAAE;gBAC3B,MAAM,IAAI,MAAM;YAClB;YAEA,MAAM,eAAiC;gBACrC,IAAI,OAAO,IAAI,CAAC,EAAE;gBAClB,KAAK,OAAO,IAAI,CAAC,GAAG;gBACpB,UAAU,OAAO,IAAI,CAAC,QAAQ,IAAI,KAAK,IAAI;YAC7C;YAEA,+BAA+B;YAC/B,SAAS,aAAa,GAAG,IAAI,aAAa,EAAE;YAC5C,kBAAkB;YAClB,2QAAA,CAAA,QAAK,CAAC,OAAO,CAAC;QAEhB,EAAE,OAAO,OAAY;YACnB,QAAQ,KAAK,CAAC,kBAAkB;YAChC,MAAM,eAAe,MAAM,OAAO,IAAI;YACtC,2QAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ,gBAAgB;YAEhB,yBAAyB;YACzB,WAAW;QACb,SAAU;YACR,eAAe;YACf,mBAAmB;YACnB,IAAI,aAAa,OAAO,EAAE;gBACxB,aAAa,OAAO,CAAC,KAAK,GAAG;YAC/B;QACF;IACF;IAEA,MAAM,aAAa,CAAA,GAAA,sQAAA,CAAA,cAAW,AAAD;+CAAE,CAAC;YAC9B,EAAE,cAAc;YAChB,EAAE,eAAe;YACjB,cAAc;YAEd,IAAI,UAAU;YAEd,MAAM,QAAQ,MAAM,IAAI,CAAC,EAAE,YAAY,CAAC,KAAK;YAC7C,IAAI,MAAM,MAAM,GAAG,KAAK,KAAK,CAAC,EAAE,CAAC,IAAI,CAAC,UAAU,CAAC,WAAW;gBAC1D,iBAAiB,KAAK,CAAC,EAAE;YAC3B;QACF;8CAAG;QAAC;KAAS;IAEb,MAAM,iBAAiB,CAAA,GAAA,sQAAA,CAAA,cAAW,AAAD;mDAAE,CAAC;YAClC,EAAE,cAAc;YAChB,EAAE,eAAe;YACjB,IAAI,CAAC,UAAU;gBACb,cAAc;YAChB;QACF;kDAAG;QAAC;KAAS;IAEb,MAAM,kBAAkB,CAAA,GAAA,sQAAA,CAAA,cAAW,AAAD;oDAAE,CAAC;YACnC,EAAE,cAAc;YAChB,EAAE,eAAe;YACjB,cAAc;QAChB;mDAAG,EAAE;IAEL,MAAM,wBAAwB,CAAC;QAC7B,MAAM,QAAQ,EAAE,MAAM,CAAC,KAAK;QAC5B,IAAI,SAAS,KAAK,CAAC,EAAE,EAAE;YACrB,iBAAiB,KAAK,CAAC,EAAE;QAC3B;IACF;IAEA,MAAM,kBAAkB;QACtB,IAAI,SAAS,IAAI,IAAI;YACnB,SAAS,SAAS,IAAI;YACtB,YAAY;QACd;IACF;IAEA,MAAM,eAAe;QACnB,SAAS;QACT,WAAW;QACX;IACF;IAEA,wEAAwE;IACxE,MAAM,kBAAkB,WAAW;IAEnC,qBACE,sSAAC;QAAI,WAAW,CAAA,GAAA,+HAAA,CAAA,KAAE,AAAD,EAAE,aAAa;kBAC7B,kBACC,4DAA4D;sBAC5D,sSAAC;YAAI,WAAU;;8BACb,sSAAC;oBACC,KAAK;oBACL,KAAI;oBACJ,WAAU;oBACV,SAAS,CAAC;wBACR,gEAAgE;wBAChE,EAAE,aAAa,CAAC,GAAG,GAAG;oBACxB;;;;;;gBAED,CAAC,0BACA,sSAAC,8HAAA,CAAA,SAAM;oBACL,MAAK;oBACL,SAAQ;oBACR,MAAK;oBACL,WAAU;oBACV,SAAS;oBACT,UAAU;oBACV,cAAW;8BAEX,cAAA,sSAAC,mRAAA,CAAA,IAAC;wBAAC,WAAU;;;;;;;;;;;gBAGhB,6BACC,sSAAC;oBAAI,WAAU;8BACb,cAAA,sSAAC;wBAAI,WAAU;;0CACb,sSAAC,wSAAA,CAAA,UAAO;gCAAC,WAAU;;;;;;0CACnB,sSAAC;gCAAE,WAAU;0CAAU;;;;;;;;;;;;;;;;;;;;;;mBAM/B,sDAAsD;sBACtD,sSAAC,4HAAA,CAAA,OAAI;YAAC,cAAa;YAAS,WAAU;;8BACpC,sSAAC,4HAAA,CAAA,WAAQ;oBAAC,WAAU;;sCAClB,sSAAC,4HAAA,CAAA,cAAW;4BAAC,OAAM;sCAAS;;;;;;sCAC5B,sSAAC,4HAAA,CAAA,cAAW;4BAAC,OAAM;sCAAM;;;;;;;;;;;;8BAG3B,sSAAC,4HAAA,CAAA,cAAW;oBAAC,OAAM;oBAAS,WAAU;;sCACpC,sSAAC;4BACC,KAAK;4BACL,MAAK;4BACL,QAAQ;4BACR,UAAU;4BACV,WAAU;4BACV,UAAU;;;;;;sCAGZ,sSAAC,4HAAA,CAAA,OAAI;4BACH,WAAW,CAAA,GAAA,+HAAA,CAAA,KAAE,AAAD,EACV,oCACA,cAAc,+BACd,YAAY;4BAEd,QAAQ;4BACR,YAAY;4BACZ,aAAa;4BACb,SAAS,CAAC,WAAW,IAAM,aAAa,OAAO,EAAE,UAAU;sCAE3D,cAAA,sSAAC,4HAAA,CAAA,cAAW;gCAAC,WAAU;0CACrB,cAAA,sSAAC;oCAAI,WAAU;;sDACb,sSAAC,6RAAA,CAAA,SAAM;4CAAC,WAAW,CAAA,GAAA,+HAAA,CAAA,KAAE,AAAD,EAClB,mDACA,cAAc;;;;;;sDAEhB,sSAAC;4CAAE,WAAU;sDACV,WACG,sBACA,aACE,yBACA,eAAe;;;;;;wCAEtB,CAAC,0BACA,sSAAC,8HAAA,CAAA,SAAM;4CACL,MAAK;4CACL,SAAQ;4CACR,MAAK;4CACL,UAAU;4CACV,SAAS,CAAC;gDACR,EAAE,eAAe;gDACjB,aAAa,OAAO,EAAE;4CACxB;sDAEC,4BACC;;kEACE,sSAAC,wSAAA,CAAA,UAAO;wDAAC,WAAU;;;;;;oDAA8B;;+DAInD;;;;;;wCAIL,CAAC,0BACA,sSAAC;4CAAE,WAAU;;gDAAgC;gDACnC,KAAK,KAAK,CAAC,UAAU,CAAC,OAAO,IAAI;gDAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAQxD,sSAAC,4HAAA,CAAA,cAAW;oBAAC,OAAM;oBAAM,WAAU;8BACjC,cAAA,sSAAC;wBAAI,WAAU;;0CACb,sSAAC,6HAAA,CAAA,QAAK;gCACJ,aAAa,eAAe;gCAC5B,OAAO;gCACP,UAAU,CAAC,IAAM,YAAY,EAAE,MAAM,CAAC,KAAK;gCAC3C,UAAU;gCACV,WAAW,CAAC;oCACV,IAAI,EAAE,GAAG,KAAK,SAAS;wCACrB,EAAE,cAAc;wCAChB;oCACF;gCACF;;;;;;0CAEF,sSAAC,8HAAA,CAAA,SAAM;gCACL,MAAK;gCACL,SAAS;gCACT,UAAU,CAAC,SAAS,IAAI,MAAM;0CAC/B;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASf;GA/UgB;KAAA", "debugId": null}}, {"offset": {"line": 1243, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/bloomgoo/gold-exchange-platform/frontend/components/ui/popover.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as PopoverPrimitive from \"@radix-ui/react-popover\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction Popover({\r\n  ...props\r\n}: React.ComponentProps<typeof PopoverPrimitive.Root>) {\r\n  return <PopoverPrimitive.Root data-slot=\"popover\" {...props} />\r\n}\r\n\r\nfunction PopoverTrigger({\r\n  ...props\r\n}: React.ComponentProps<typeof PopoverPrimitive.Trigger>) {\r\n  return <PopoverPrimitive.Trigger data-slot=\"popover-trigger\" {...props} />\r\n}\r\n\r\nfunction PopoverContent({\r\n  className,\r\n  align = \"center\",\r\n  sideOffset = 4,\r\n  ...props\r\n}: React.ComponentProps<typeof PopoverPrimitive.Content>) {\r\n  return (\r\n    <PopoverPrimitive.Portal>\r\n      <PopoverPrimitive.Content\r\n        data-slot=\"popover-content\"\r\n        align={align}\r\n        sideOffset={sideOffset}\r\n        className={cn(\r\n          \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 w-72 origin-(--radix-popover-content-transform-origin) rounded-md border p-4 shadow-md outline-hidden\",\r\n          className\r\n        )}\r\n        {...props}\r\n      />\r\n    </PopoverPrimitive.Portal>\r\n  )\r\n}\r\n\r\nfunction PopoverAnchor({\r\n  ...props\r\n}: React.ComponentProps<typeof PopoverPrimitive.Anchor>) {\r\n  return <PopoverPrimitive.Anchor data-slot=\"popover-anchor\" {...props} />\r\n}\r\n\r\nexport { Popover, PopoverTrigger, PopoverContent, PopoverAnchor }\r\n"], "names": [], "mappings": ";;;;;;;AAGA;AAEA;AAAA;AALA;;;;AAOA,SAAS,QAAQ,EACf,GAAG,OACgD;IACnD,qBAAO,sSAAC,gRAAA,CAAA,OAAqB;QAAC,aAAU;QAAW,GAAG,KAAK;;;;;;AAC7D;KAJS;AAMT,SAAS,eAAe,EACtB,GAAG,OACmD;IACtD,qBAAO,sSAAC,gRAAA,CAAA,UAAwB;QAAC,aAAU;QAAmB,GAAG,KAAK;;;;;;AACxE;MAJS;AAMT,SAAS,eAAe,EACtB,SAAS,EACT,QAAQ,QAAQ,EAChB,aAAa,CAAC,EACd,GAAG,OACmD;IACtD,qBACE,sSAAC,gRAAA,CAAA,SAAuB;kBACtB,cAAA,sSAAC,gRAAA,CAAA,UAAwB;YACvB,aAAU;YACV,OAAO;YACP,YAAY;YACZ,WAAW,CAAA,GAAA,+HAAA,CAAA,KAAE,AAAD,EACV,keACA;YAED,GAAG,KAAK;;;;;;;;;;;AAIjB;MApBS;AAsBT,SAAS,cAAc,EACrB,GAAG,OACkD;IACrD,qBAAO,sSAAC,gRAAA,CAAA,SAAuB;QAAC,aAAU;QAAkB,GAAG,KAAK;;;;;;AACtE;MAJS", "debugId": null}}, {"offset": {"line": 1325, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/bloomgoo/gold-exchange-platform/frontend/components/ui/dialog.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as DialogPrimitive from \"@radix-ui/react-dialog\"\r\nimport { XIcon } from \"lucide-react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction Dialog({\r\n  ...props\r\n}: React.ComponentProps<typeof DialogPrimitive.Root>) {\r\n  return <DialogPrimitive.Root data-slot=\"dialog\" {...props} />\r\n}\r\n\r\nfunction DialogTrigger({\r\n  ...props\r\n}: React.ComponentProps<typeof DialogPrimitive.Trigger>) {\r\n  return <DialogPrimitive.Trigger data-slot=\"dialog-trigger\" {...props} />\r\n}\r\n\r\nfunction DialogPortal({\r\n  ...props\r\n}: React.ComponentProps<typeof DialogPrimitive.Portal>) {\r\n  return <DialogPrimitive.Portal data-slot=\"dialog-portal\" {...props} />\r\n}\r\n\r\nfunction DialogClose({\r\n  ...props\r\n}: React.ComponentProps<typeof DialogPrimitive.Close>) {\r\n  return <DialogPrimitive.Close data-slot=\"dialog-close\" {...props} />\r\n}\r\n\r\nfunction DialogOverlay({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof DialogPrimitive.Overlay>) {\r\n  return (\r\n    <DialogPrimitive.Overlay\r\n      data-slot=\"dialog-overlay\"\r\n      className={cn(\r\n        \"data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction DialogContent({\r\n  className,\r\n  children,\r\n  ...props\r\n}: React.ComponentProps<typeof DialogPrimitive.Content>) {\r\n  return (\r\n    <DialogPortal data-slot=\"dialog-portal\">\r\n      <DialogOverlay />\r\n      <DialogPrimitive.Content\r\n        data-slot=\"dialog-content\"\r\n        className={cn(\r\n          \"bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg\",\r\n          className\r\n        )}\r\n        {...props}\r\n      >\r\n        {children}\r\n        <DialogPrimitive.Close className=\"ring-offset-background focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\">\r\n          <XIcon />\r\n          <span className=\"sr-only\">Close</span>\r\n        </DialogPrimitive.Close>\r\n      </DialogPrimitive.Content>\r\n    </DialogPortal>\r\n  )\r\n}\r\n\r\nfunction DialogHeader({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"dialog-header\"\r\n      className={cn(\"flex flex-col gap-2 text-center sm:text-left\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction DialogFooter({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"dialog-footer\"\r\n      className={cn(\r\n        \"flex flex-col-reverse gap-2 sm:flex-row sm:justify-end\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction DialogTitle({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof DialogPrimitive.Title>) {\r\n  return (\r\n    <DialogPrimitive.Title\r\n      data-slot=\"dialog-title\"\r\n      className={cn(\"text-lg leading-none font-semibold\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction DialogDescription({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof DialogPrimitive.Description>) {\r\n  return (\r\n    <DialogPrimitive.Description\r\n      data-slot=\"dialog-description\"\r\n      className={cn(\"text-muted-foreground text-sm\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport {\r\n  Dialog,\r\n  DialogClose,\r\n  DialogContent,\r\n  DialogDescription,\r\n  DialogFooter,\r\n  DialogHeader,\r\n  DialogOverlay,\r\n  DialogPortal,\r\n  DialogTitle,\r\n  DialogTrigger,\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAGA;AACA;AAEA;AAAA;AANA;;;;;AAQA,SAAS,OAAO,EACd,GAAG,OAC+C;IAClD,qBAAO,sSAAC,kRAAA,CAAA,OAAoB;QAAC,aAAU;QAAU,GAAG,KAAK;;;;;;AAC3D;KAJS;AAMT,SAAS,cAAc,EACrB,GAAG,OACkD;IACrD,qBAAO,sSAAC,kRAAA,CAAA,UAAuB;QAAC,aAAU;QAAkB,GAAG,KAAK;;;;;;AACtE;MAJS;AAMT,SAAS,aAAa,EACpB,GAAG,OACiD;IACpD,qBAAO,sSAAC,kRAAA,CAAA,SAAsB;QAAC,aAAU;QAAiB,GAAG,KAAK;;;;;;AACpE;MAJS;AAMT,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,sSAAC,kRAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;MAJS;AAMT,SAAS,cAAc,EACrB,SAAS,EACT,GAAG,OACkD;IACrD,qBACE,sSAAC,kRAAA,CAAA,UAAuB;QACtB,aAAU;QACV,WAAW,CAAA,GAAA,+HAAA,CAAA,KAAE,AAAD,EACV,0JACA;QAED,GAAG,KAAK;;;;;;AAGf;MAdS;AAgBT,SAAS,cAAc,EACrB,SAAS,EACT,QAAQ,EACR,GAAG,OACkD;IACrD,qBACE,sSAAC;QAAa,aAAU;;0BACtB,sSAAC;;;;;0BACD,sSAAC,kRAAA,CAAA,UAAuB;gBACtB,aAAU;gBACV,WAAW,CAAA,GAAA,+HAAA,CAAA,KAAE,AAAD,EACV,+WACA;gBAED,GAAG,KAAK;;oBAER;kCACD,sSAAC,kRAAA,CAAA,QAAqB;wBAAC,WAAU;;0CAC/B,sSAAC,uRAAA,CAAA,QAAK;;;;;0CACN,sSAAC;gCAAK,WAAU;0CAAU;;;;;;;;;;;;;;;;;;;;;;;;AAKpC;MAxBS;AA0BT,SAAS,aAAa,EAAE,SAAS,EAAE,GAAG,OAAoC;IACxE,qBACE,sSAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,+HAAA,CAAA,KAAE,AAAD,EAAE,gDAAgD;QAC7D,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,aAAa,EAAE,SAAS,EAAE,GAAG,OAAoC;IACxE,qBACE,sSAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,+HAAA,CAAA,KAAE,AAAD,EACV,0DACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,sSAAC,kRAAA,CAAA,QAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,+HAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QACnD,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,kBAAkB,EACzB,SAAS,EACT,GAAG,OACsD;IACzD,qBACE,sSAAC,kRAAA,CAAA,cAA2B;QAC1B,aAAU;QACV,WAAW,CAAA,GAAA,+HAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;MAXS", "debugId": null}}, {"offset": {"line": 1523, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/bloomgoo/gold-exchange-platform/frontend/components/ui/command.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport { Command as CommandPrimitive } from \"cmdk\"\r\nimport { SearchIcon } from \"lucide-react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\nimport {\r\n  Dialog,\r\n  DialogContent,\r\n  DialogDescription,\r\n  DialogHeader,\r\n  DialogTitle,\r\n} from \"@/components/ui/dialog\"\r\n\r\nfunction Command({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof CommandPrimitive>) {\r\n  return (\r\n    <CommandPrimitive\r\n      data-slot=\"command\"\r\n      className={cn(\r\n        \"bg-popover text-popover-foreground flex h-full w-full flex-col overflow-hidden rounded-md\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CommandDialog({\r\n  title = \"Command Palette\",\r\n  description = \"Search for a command to run...\",\r\n  children,\r\n  ...props\r\n}: React.ComponentProps<typeof Dialog> & {\r\n  title?: string\r\n  description?: string\r\n}) {\r\n  return (\r\n    <Dialog {...props}>\r\n      <DialogHeader className=\"sr-only\">\r\n        <DialogTitle>{title}</DialogTitle>\r\n        <DialogDescription>{description}</DialogDescription>\r\n      </DialogHeader>\r\n      <DialogContent className=\"overflow-hidden p-0\">\r\n        <Command className=\"[&_[cmdk-group-heading]]:text-muted-foreground **:data-[slot=command-input-wrapper]:h-12 [&_[cmdk-group-heading]]:px-2 [&_[cmdk-group-heading]]:font-medium [&_[cmdk-group]]:px-2 [&_[cmdk-group]:not([hidden])_~[cmdk-group]]:pt-0 [&_[cmdk-input-wrapper]_svg]:h-5 [&_[cmdk-input-wrapper]_svg]:w-5 [&_[cmdk-input]]:h-12 [&_[cmdk-item]]:px-2 [&_[cmdk-item]]:py-3 [&_[cmdk-item]_svg]:h-5 [&_[cmdk-item]_svg]:w-5\">\r\n          {children}\r\n        </Command>\r\n      </DialogContent>\r\n    </Dialog>\r\n  )\r\n}\r\n\r\nfunction CommandInput({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof CommandPrimitive.Input>) {\r\n  return (\r\n    <div\r\n      data-slot=\"command-input-wrapper\"\r\n      className=\"flex h-9 items-center gap-2 border-b px-3\"\r\n    >\r\n      <SearchIcon className=\"size-4 shrink-0 opacity-50\" />\r\n      <CommandPrimitive.Input\r\n        data-slot=\"command-input\"\r\n        className={cn(\r\n          \"placeholder:text-muted-foreground flex h-10 w-full rounded-md bg-transparent py-3 text-sm outline-hidden disabled:cursor-not-allowed disabled:opacity-50\",\r\n          className\r\n        )}\r\n        {...props}\r\n      />\r\n    </div>\r\n  )\r\n}\r\n\r\nfunction CommandList({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof CommandPrimitive.List>) {\r\n  return (\r\n    <CommandPrimitive.List\r\n      data-slot=\"command-list\"\r\n      className={cn(\r\n        \"max-h-[300px] scroll-py-1 overflow-x-hidden overflow-y-auto\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CommandEmpty({\r\n  ...props\r\n}: React.ComponentProps<typeof CommandPrimitive.Empty>) {\r\n  return (\r\n    <CommandPrimitive.Empty\r\n      data-slot=\"command-empty\"\r\n      className=\"py-6 text-center text-sm\"\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CommandGroup({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof CommandPrimitive.Group>) {\r\n  return (\r\n    <CommandPrimitive.Group\r\n      data-slot=\"command-group\"\r\n      className={cn(\r\n        \"text-foreground [&_[cmdk-group-heading]]:text-muted-foreground overflow-hidden p-1 [&_[cmdk-group-heading]]:px-2 [&_[cmdk-group-heading]]:py-1.5 [&_[cmdk-group-heading]]:text-xs [&_[cmdk-group-heading]]:font-medium\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CommandSeparator({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof CommandPrimitive.Separator>) {\r\n  return (\r\n    <CommandPrimitive.Separator\r\n      data-slot=\"command-separator\"\r\n      className={cn(\"bg-border -mx-1 h-px\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CommandItem({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof CommandPrimitive.Item>) {\r\n  return (\r\n    <CommandPrimitive.Item\r\n      data-slot=\"command-item\"\r\n      className={cn(\r\n        \"data-[selected=true]:bg-accent data-[selected=true]:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex cursor-default items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[disabled=true]:pointer-events-none data-[disabled=true]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CommandShortcut({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<\"span\">) {\r\n  return (\r\n    <span\r\n      data-slot=\"command-shortcut\"\r\n      className={cn(\r\n        \"text-muted-foreground ml-auto text-xs tracking-widest\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport {\r\n  Command,\r\n  CommandDialog,\r\n  CommandInput,\r\n  CommandList,\r\n  CommandEmpty,\r\n  CommandGroup,\r\n  CommandItem,\r\n  CommandShortcut,\r\n  CommandSeparator,\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;;;AAGA;AACA;AAEA;AAAA;AACA;AAPA;;;;;;AAeA,SAAS,QAAQ,EACf,SAAS,EACT,GAAG,OAC2C;IAC9C,qBACE,sSAAC,qPAAA,CAAA,UAAgB;QACf,aAAU;QACV,WAAW,CAAA,GAAA,+HAAA,CAAA,KAAE,AAAD,EACV,6FACA;QAED,GAAG,KAAK;;;;;;AAGf;KAdS;AAgBT,SAAS,cAAc,EACrB,QAAQ,iBAAiB,EACzB,cAAc,gCAAgC,EAC9C,QAAQ,EACR,GAAG,OAIJ;IACC,qBACE,sSAAC,8HAAA,CAAA,SAAM;QAAE,GAAG,KAAK;;0BACf,sSAAC,8HAAA,CAAA,eAAY;gBAAC,WAAU;;kCACtB,sSAAC,8HAAA,CAAA,cAAW;kCAAE;;;;;;kCACd,sSAAC,8HAAA,CAAA,oBAAiB;kCAAE;;;;;;;;;;;;0BAEtB,sSAAC,8HAAA,CAAA,gBAAa;gBAAC,WAAU;0BACvB,cAAA,sSAAC;oBAAQ,WAAU;8BAChB;;;;;;;;;;;;;;;;;AAKX;MAtBS;AAwBT,SAAS,aAAa,EACpB,SAAS,EACT,GAAG,OACiD;IACpD,qBACE,sSAAC;QACC,aAAU;QACV,WAAU;;0BAEV,sSAAC,iSAAA,CAAA,aAAU;gBAAC,WAAU;;;;;;0BACtB,sSAAC,qPAAA,CAAA,UAAgB,CAAC,KAAK;gBACrB,aAAU;gBACV,WAAW,CAAA,GAAA,+HAAA,CAAA,KAAE,AAAD,EACV,4JACA;gBAED,GAAG,KAAK;;;;;;;;;;;;AAIjB;MApBS;AAsBT,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,sSAAC,qPAAA,CAAA,UAAgB,CAAC,IAAI;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,+HAAA,CAAA,KAAE,AAAD,EACV,+DACA;QAED,GAAG,KAAK;;;;;;AAGf;MAdS;AAgBT,SAAS,aAAa,EACpB,GAAG,OACiD;IACpD,qBACE,sSAAC,qPAAA,CAAA,UAAgB,CAAC,KAAK;QACrB,aAAU;QACV,WAAU;QACT,GAAG,KAAK;;;;;;AAGf;MAVS;AAYT,SAAS,aAAa,EACpB,SAAS,EACT,GAAG,OACiD;IACpD,qBACE,sSAAC,qPAAA,CAAA,UAAgB,CAAC,KAAK;QACrB,aAAU;QACV,WAAW,CAAA,GAAA,+HAAA,CAAA,KAAE,AAAD,EACV,0NACA;QAED,GAAG,KAAK;;;;;;AAGf;MAdS;AAgBT,SAAS,iBAAiB,EACxB,SAAS,EACT,GAAG,OACqD;IACxD,qBACE,sSAAC,qPAAA,CAAA,UAAgB,CAAC,SAAS;QACzB,aAAU;QACV,WAAW,CAAA,GAAA,+HAAA,CAAA,KAAE,AAAD,EAAE,wBAAwB;QACrC,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,sSAAC,qPAAA,CAAA,UAAgB,CAAC,IAAI;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,+HAAA,CAAA,KAAE,AAAD,EACV,uYACA;QAED,GAAG,KAAK;;;;;;AAGf;MAdS;AAgBT,SAAS,gBAAgB,EACvB,SAAS,EACT,GAAG,OAC0B;IAC7B,qBACE,sSAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,+HAAA,CAAA,KAAE,AAAD,EACV,yDACA;QAED,GAAG,KAAK;;;;;;AAGf;MAdS", "debugId": null}}, {"offset": {"line": 1729, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/bloomgoo/gold-exchange-platform/frontend/components/ui/scroll-area.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as ScrollAreaPrimitive from \"@radix-ui/react-scroll-area\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction ScrollArea({\r\n  className,\r\n  children,\r\n  ...props\r\n}: React.ComponentProps<typeof ScrollAreaPrimitive.Root>) {\r\n  return (\r\n    <ScrollAreaPrimitive.Root\r\n      data-slot=\"scroll-area\"\r\n      className={cn(\"relative\", className)}\r\n      {...props}\r\n    >\r\n      <ScrollAreaPrimitive.Viewport\r\n        data-slot=\"scroll-area-viewport\"\r\n        className=\"focus-visible:ring-ring/50 size-full rounded-[inherit] transition-[color,box-shadow] outline-none focus-visible:ring-[3px] focus-visible:outline-1\"\r\n      >\r\n        {children}\r\n      </ScrollAreaPrimitive.Viewport>\r\n      <ScrollBar />\r\n      <ScrollAreaPrimitive.Corner />\r\n    </ScrollAreaPrimitive.Root>\r\n  )\r\n}\r\n\r\nfunction ScrollBar({\r\n  className,\r\n  orientation = \"vertical\",\r\n  ...props\r\n}: React.ComponentProps<typeof ScrollAreaPrimitive.ScrollAreaScrollbar>) {\r\n  return (\r\n    <ScrollAreaPrimitive.ScrollAreaScrollbar\r\n      data-slot=\"scroll-area-scrollbar\"\r\n      orientation={orientation}\r\n      className={cn(\r\n        \"flex touch-none p-px transition-colors select-none\",\r\n        orientation === \"vertical\" &&\r\n          \"h-full w-2.5 border-l border-l-transparent\",\r\n        orientation === \"horizontal\" &&\r\n          \"h-2.5 flex-col border-t border-t-transparent\",\r\n        className\r\n      )}\r\n      {...props}\r\n    >\r\n      <ScrollAreaPrimitive.ScrollAreaThumb\r\n        data-slot=\"scroll-area-thumb\"\r\n        className=\"bg-border relative flex-1 rounded-full\"\r\n      />\r\n    </ScrollAreaPrimitive.ScrollAreaScrollbar>\r\n  )\r\n}\r\n\r\nexport { ScrollArea, ScrollBar }\r\n"], "names": [], "mappings": ";;;;;AAGA;AAEA;AAAA;AALA;;;;AAOA,SAAS,WAAW,EAClB,SAAS,EACT,QAAQ,EACR,GAAG,OACmD;IACtD,qBACE,sSAAC,oRAAA,CAAA,OAAwB;QACvB,aAAU;QACV,WAAW,CAAA,GAAA,+HAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QACzB,GAAG,KAAK;;0BAET,sSAAC,oRAAA,CAAA,WAA4B;gBAC3B,aAAU;gBACV,WAAU;0BAET;;;;;;0BAEH,sSAAC;;;;;0BACD,sSAAC,oRAAA,CAAA,SAA0B;;;;;;;;;;;AAGjC;KArBS;AAuBT,SAAS,UAAU,EACjB,SAAS,EACT,cAAc,UAAU,EACxB,GAAG,OACkE;IACrE,qBACE,sSAAC,oRAAA,CAAA,sBAAuC;QACtC,aAAU;QACV,aAAa;QACb,WAAW,CAAA,GAAA,+HAAA,CAAA,KAAE,AAAD,EACV,sDACA,gBAAgB,cACd,8CACF,gBAAgB,gBACd,gDACF;QAED,GAAG,KAAK;kBAET,cAAA,sSAAC,oRAAA,CAAA,kBAAmC;YAClC,aAAU;YACV,WAAU;;;;;;;;;;;AAIlB;MAzBS", "debugId": null}}, {"offset": {"line": 1808, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/bloomgoo/gold-exchange-platform/frontend/components/ui/icon-picker.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useMemo, useEffect } from 'react';\nimport { Icon } from '@iconify/react';\nimport { Button } from '@/components/ui/button';\nimport {\n  Popover,\n  PopoverContent,\n  PopoverTrigger,\n} from '@/components/ui/popover';\nimport {\n  Command,\n  CommandInput,\n} from '@/components/ui/command';\nimport { ScrollArea } from '@/components/ui/scroll-area';\nimport { X, ChevronDown, ChevronLeft, ChevronRight } from 'lucide-react';\n\n// Solar icon collection will be loaded dynamically\n\ninterface IconPickerProps {\n  value?: string;\n  onChange?: (iconName: string) => void;\n  placeholder?: string;\n  disabled?: boolean;\n  className?: string;\n}\n\n\n\n// Function để generate toàn bộ Solar icons\nconst generateAllSolarIcons = (): string[] => {\n  const solarIcons: string[] = [];\n\n  // Danh sách tất cả Solar icon names (từ Solar icon set documentation)\n  const solarIconNames = [\n    // UI & Navigation\n    'home', 'menu-dots', 'hamburger-menu', 'close-circle', 'close-square', 'add-circle', 'add-square',\n    'minus-circle', 'minus-square', 'check-circle', 'check-square', 'info-circle', 'info-square',\n    'question-circle', 'question-square', 'danger-triangle', 'danger-circle', 'danger-square',\n    'arrow-left', 'arrow-right', 'arrow-up', 'arrow-down', 'arrow-left-up', 'arrow-right-up',\n    'arrow-left-down', 'arrow-right-down', 'refresh', 'restart', 'logout', 'login', 'exit',\n    'import', 'export', 'download', 'upload', 'share', 'link', 'external-link', 'copy',\n    'duplicate', 'clipboard', 'paste', 'cut', 'scissors', 'undo', 'redo', 'save', 'bookmark',\n\n    // Users & People\n    'user', 'users-group-rounded', 'users-group-two-rounded', 'user-circle', 'user-rounded',\n    'user-plus', 'user-minus', 'user-check', 'user-cross', 'user-block', 'user-speak',\n    'user-hands-up', 'user-heart', 'user-id', 'users', 'team', 'crown', 'shield-user',\n\n    // Communication & Social\n    'chat-round', 'chat-square', 'chat-dots', 'letter', 'letter-opened', 'letter-unread',\n    'inbox', 'outbox', 'mailbox', 'send-square', 'phone', 'phone-calling', 'phone-rounded',\n    'smartphone', 'tablet', 'laptop', 'monitor', 'tv', 'radio', 'microphone', 'microphone-2',\n    'video-camera', 'camera', 'gallery', 'notification', 'bell', 'bell-bing', 'bell-off',\n\n    // Files & Documents\n    'document', 'document-text', 'documents', 'folder', 'folder-open', 'folder-plus',\n    'folder-minus', 'folder-check', 'folder-cross', 'file', 'file-text', 'file-plus',\n    'file-minus', 'file-check', 'file-cross', 'file-download', 'file-upload', 'file-send',\n    'archive', 'zip-file', 'code-file', 'css-file', 'html-file', 'js-file', 'json-file',\n\n    // Actions & Status\n    'heart', 'heart-broken', 'like', 'dislike', 'star', 'star-circle', 'star-fall',\n    'flag', 'bookmark-circle', 'bookmark-square', 'tag', 'tags', 'label', 'sticker',\n    'pin', 'unpin', 'thumbs-up', 'thumbs-down', 'hand', 'clap', 'fire', 'flame',\n\n    // Settings & Tools\n    'settings', 'settings-minimalistic', 'tuning', 'tuning-2', 'tuning-3', 'slider',\n    'slider-horizontal', 'slider-vertical', 'filter', 'sort-vertical', 'sort-horizontal',\n    'sort-by-alphabet', 'sort-by-time', 'search', 'zoom-in', 'zoom-out', 'eye', 'eye-closed',\n    'visibility', 'invisible', 'show', 'hide', 'focus', 'target', 'crosshair',\n\n    // Time & Calendar\n    'calendar', 'calendar-add', 'calendar-mark', 'calendar-minimalistic', 'clock-circle',\n    'clock-square', 'history', 'history-2', 'history-3', 'timer', 'stopwatch', 'alarm',\n    'hourglass', 'time-machine', 'schedule', 'timeline', 'chronometer',\n\n    // Security & Privacy\n    'lock', 'lock-keyhole', 'lock-password', 'unlock', 'unlock-keyhole', 'key',\n    'key-minimalistic', 'key-square', 'shield', 'shield-check', 'shield-cross',\n    'shield-keyhole', 'shield-minimalistic', 'shield-plus', 'shield-minus', 'shield-user',\n    'password', 'fingerprint', 'incognito', 'spy', 'mask', 'safe', 'safe-2',\n\n    // Media & Entertainment\n    'gallery', 'gallery-add', 'gallery-check', 'gallery-edit', 'gallery-favourite',\n    'gallery-minimalistic', 'gallery-remove', 'gallery-round', 'gallery-send', 'gallery-wide',\n    'image', 'images', 'picture', 'photo', 'camera-add', 'camera-minimalistic', 'camera-square',\n    'video-frame', 'video-frame-2', 'video-frame-cut', 'video-frame-cut-2', 'video-frame-play-horizontal',\n    'video-frame-play-vertical', 'video-library', 'clapperboard', 'clapperboard-edit',\n    'clapperboard-open', 'clapperboard-play', 'clapperboard-text', 'film', 'reel',\n    'reel-2', 'cassette', 'cassette-2', 'record', 'record-circle', 'record-minimalistic',\n    'record-square', 'play', 'play-circle', 'play-stream', 'pause', 'pause-circle',\n    'stop', 'stop-circle', 'previous', 'next', 'rewind-back', 'rewind-forward',\n    'skip-next', 'skip-previous', 'repeat', 'repeat-one', 'shuffle', 'volume-high',\n    'volume-low', 'volume-off', 'volume-small', 'music-note', 'music-note-2',\n    'music-note-3', 'music-note-4', 'music-notes', 'headphones', 'headphones-round',\n    'headphones-round-sound', 'headphones-square', 'headphones-square-sound', 'speaker',\n    'speaker-minimalistic', 'turntable', 'turntable-music-note',\n\n    // Shopping & Commerce\n    'cart', 'cart-2', 'cart-3', 'cart-4', 'cart-5', 'cart-check', 'cart-cross',\n    'cart-large', 'cart-large-2', 'cart-large-3', 'cart-large-4', 'cart-large-minimalistic',\n    'cart-plus', 'bag', 'bag-2', 'bag-3', 'bag-4', 'bag-5', 'bag-check', 'bag-cross',\n    'bag-heart', 'bag-music', 'bag-smile', 'handbag', 'shop', 'shop-2', 'shop-minimalistic',\n    'card', 'card-2', 'card-receive', 'card-search', 'card-send', 'card-transfer',\n    'wallet', 'wallet-2', 'wallet-money', 'banknote', 'banknote-2', 'dollar',\n    'dollar-minimalistic', 'euro', 'pound', 'ruble', 'yen', 'won', 'bitcoin',\n    'tag-price', 'tag-horizontal', 'sale', 'percent', 'gift', 'present',\n    'ticket', 'ticket-sale', 'receipt', 'bill-check', 'bill-cross', 'bill-list',\n\n    // Business & Finance\n    'chart', 'chart-2', 'chart-square', 'graph', 'graph-up', 'graph-down',\n    'graph-new-up', 'presentation-graph', 'diagram-up', 'diagram-down', 'analytics',\n    'pie-chart', 'pie-chart-2', 'pie-chart-3', 'calculator', 'calculator-minimalistic',\n    'briefcase', 'case', 'case-minimalistic', 'case-round', 'case-round-minimalistic',\n    'suitcase', 'suitcase-tag', 'building', 'building-2', 'building-3', 'building-4',\n    'buildings', 'buildings-2', 'buildings-3', 'city', 'bank', 'hospital',\n    'home-2', 'home-add', 'home-add-angle', 'home-angle', 'home-angle-2', 'home-smile',\n    'home-wifi', 'smart-home', 'smart-home-angle', 'smart-home-check', 'smart-home-cross',\n\n    // Technology & Devices\n    'smartphone', 'smartphone-2', 'smartphone-rotate', 'smartphone-rotate-2', 'smartphone-update',\n    'smartphone-vibration', 'tablet', 'laptop', 'laptop-2', 'laptop-minimalistic',\n    'monitor', 'monitor-camera', 'monitor-smartphone', 'tv', 'tv-minimalistic',\n    'gameboy', 'gamepad', 'gamepad-charge', 'gamepad-minimalistic', 'gamepad-old',\n    'server', 'server-2', 'server-minimalistic', 'server-path', 'server-square',\n    'database', 'programming', 'code', 'code-2', 'code-circle', 'code-file',\n    'code-square', 'terminal', 'command', 'bug', 'bug-minimalistic', 'widget',\n    'widget-2', 'widget-3', 'widget-4', 'widget-5', 'widget-6', 'widget-add',\n    'cpu', 'cpu-bolt', 'microchip', 'sim-card', 'sim-card-minimalistic',\n    'usb', 'usb-square', 'bluetooth', 'bluetooth-circle', 'bluetooth-square',\n    'bluetooth-wave', 'wifi-router', 'wifi-router-minimalistic', 'wifi-router-round',\n    'antenna', 'satellite', 'radar', 'radar-2', 'soundwave', 'soundwave-circle',\n    'soundwave-square', 'battery-full', 'battery-half', 'battery-low', 'battery-charge',\n    'battery-charge-minimalistic', 'power', 'power-minimalistic', 'plug', 'plug-circle',\n\n    // Transportation & Travel\n    'car', 'car-2', 'car-3', 'electric-refueling', 'gas-station', 'speedometer',\n    'transmission', 'transmission-circle', 'transmission-square', 'wheel', 'wheel-angle',\n    'bus', 'tram', 'train', 'subway', 'airplane', 'airplane-2', 'airplane-3',\n    'rocket', 'rocket-2', 'ufo', 'ufo-2', 'ufo-3', 'ship', 'ship-2', 'sailboat',\n    'bicycle', 'kick-scooter', 'skateboard', 'skateboarding', 'walking', 'running',\n    'running-2', 'running-round', 'figure', 'figure-2d', 'map', 'map-2', 'map-arrow-down',\n    'map-arrow-left', 'map-arrow-right', 'map-arrow-square', 'map-arrow-up', 'map-point',\n    'map-point-add', 'map-point-favourite', 'map-point-hospital', 'map-point-remove',\n    'map-point-rotate', 'map-point-school', 'map-point-search', 'map-point-wave',\n    'gps', 'compass', 'compass-big', 'compass-square', 'routing', 'routing-2',\n    'routing-3', 'road', 'road-sign', 'signpost', 'signpost-2',\n\n    // Weather & Nature\n    'sun', 'sun-2', 'sun-fog', 'moon', 'moon-fog', 'moon-sleep', 'moon-stars',\n    'cloud', 'cloud-2', 'cloud-bolt', 'cloud-check', 'cloud-cross', 'cloud-download',\n    'cloud-fog', 'cloud-minus', 'cloud-plus', 'cloud-rain', 'cloud-snow', 'cloud-snowfall',\n    'cloud-storm', 'cloud-sun', 'cloud-sun-2', 'cloud-upload', 'cloud-waterdrop',\n    'cloudy', 'fog', 'rain', 'snow', 'snowflake', 'tornado', 'tornado-small',\n    'wind', 'windsock', 'temperature', 'temperature-plus', 'temperature-minus',\n    'thermometer', 'drop', 'waterdrop', 'fire', 'bonfire', 'flame', 'leaf',\n    'leaves', 'tree', 'forest', 'flower', 'flower-2', 'planet', 'planet-2',\n    'planet-3', 'planet-4', 'earth', 'global', 'asteroid', 'black-hole',\n\n    // Health & Medical\n    'health', 'medical-kit', 'pill', 'pills', 'capsule', 'syringe', 'thermometer',\n    'stethoscope', 'heart-pulse', 'pulse', 'pulse-2', 'dna', 'bacteria', 'virus',\n    'bone', 'bone-broken', 'tooth', 'eye', 'glasses', 'wheelchair', 'crutch',\n    'bandage', 'plaster', 'hospital', 'ambulance', 'first-aid-kit',\n\n    // Food & Drink\n    'cup', 'cup-first', 'cup-hot', 'cup-music', 'cup-paper', 'cup-star', 'coffee',\n    'tea', 'wine', 'champagne', 'cocktail', 'beer', 'bottle', 'chef-hat',\n    'fork-knife', 'spoon', 'pizza', 'hamburger', 'hotdog', 'donut', 'cake',\n    'cake-minimalistic', 'candy', 'ice-cream', 'apple', 'carrot', 'pepper',\n\n    // Sports & Activities\n    'football', 'basketball', 'tennis-ball', 'tennis-2', 'volleyball', 'volleyball-2',\n    'golf', 'bowling', 'dumbbell', 'dumbbell-large', 'dumbbell-large-minimalistic',\n    'dumbbell-small', 'gym', 'medal', 'medal-ribbon', 'medal-ribbons-star', 'trophy',\n    'cup-first', 'target', 'dart', 'fishing', 'swimming', 'surfing', 'skiing',\n\n    // Education & Learning\n    'book', 'book-2', 'book-bookmark', 'book-minimalistic', 'book-open', 'books',\n    'notebook', 'notebook-bookmark', 'notebook-minimalistic', 'diploma', 'diploma-verified',\n    'graduation-square', 'hat-graduation', 'school', 'university', 'library',\n    'pen', 'pen-2', 'pen-new-round', 'pen-new-square', 'pencil', 'eraser',\n    'eraser-circle', 'eraser-square', 'ruler', 'ruler-angular', 'ruler-cross-pen',\n    'palette', 'palette-2', 'palette-round', 'paintbrush', 'magic-stick',\n\n    // Miscellaneous\n    'question', 'help', 'support', 'chat-help', 'lifebuoy', 'sos', 'danger',\n    'warning', 'forbidden', 'forbidden-2', 'stop-sign', 'traffic-economy',\n    'traffic', 'flashlight', 'flashlight-on', 'magnet', 'magnet-wave',\n    'telescope', 'microscope', 'atom', 'molecule', 'flask', 'flask-2',\n    'test-tube', 'test-tube-minimalistic', 'scale', 'scale-minimalistic',\n    'measuring-tape', 'ruler-pen', 'geometry', 'triangle', 'square',\n    'circle', 'hexagon', 'pentagon', 'rhombus', 'star-angle',\n    'infinity', 'infinity-square', 'verified', 'quality', 'crown-minimalistic',\n    'crown-star', 'medal-star', 'medal-star-circle', 'medal-star-square',\n    'ranking', 'ranking-star', 'award', 'certificate', 'ribbon',\n    'ribbon-star', 'rosette', 'rosette-discount-check'\n  ];\n\n  // Generate icons với tất cả weights\n  const weights = ['linear', 'bold', 'outline', 'broken', 'bold-duotone', 'line-duotone'];\n\n  solarIconNames.forEach(iconName => {\n    weights.forEach(weight => {\n      solarIcons.push(`solar:${iconName}-${weight}`);\n    });\n  });\n\n  return solarIcons;\n};\n\n// Sử dụng toàn bộ Solar icons\nconst ALL_SOLAR_ICONS = generateAllSolarIcons();\n\n// Pagination constants\nconst ICONS_PER_PAGE = 48; // 6x8 grid\n\n// Helper function to search Solar icons\nfunction searchSolarIcons(query: string): string[] {\n  if (!query || query.length < 2) {\n    return [];\n  }\n\n  const searchTerm = query.toLowerCase();\n\n  return ALL_SOLAR_ICONS.filter(iconName => {\n    const name = iconName.toLowerCase();\n    const baseName = iconName.split(':')[1] || iconName;\n\n    return name.includes(searchTerm) ||\n           baseName.includes(searchTerm) ||\n           baseName.replace(/-/g, ' ').includes(searchTerm);\n  });\n}\n\nexport function IconPicker({\n  value = '',\n  onChange,\n  placeholder = 'Chọn icon...',\n  disabled = false,\n  className = '',\n}: IconPickerProps) {\n  const [open, setOpen] = useState(false);\n  const [searchQuery, setSearchQuery] = useState('');\n  const [currentPage, setCurrentPage] = useState(1);\n\n\n\n  // Get filtered icons based on search\n  const filteredIcons = useMemo(() => {\n    if (searchQuery && searchQuery.length >= 2) {\n      return searchSolarIcons(searchQuery);\n    }\n    return ALL_SOLAR_ICONS;\n  }, [searchQuery]);\n\n  // Calculate pagination\n  const totalPages = Math.ceil(filteredIcons.length / ICONS_PER_PAGE);\n  const startIndex = (currentPage - 1) * ICONS_PER_PAGE;\n  const endIndex = startIndex + ICONS_PER_PAGE;\n  const currentIcons = filteredIcons.slice(startIndex, endIndex);\n\n  // Reset page when search changes\n  useEffect(() => {\n    setCurrentPage(1);\n  }, [searchQuery]);\n\n  // Handle page change\n  const handlePageChange = (page: number) => {\n    setCurrentPage(page);\n  };\n\n  const handleIconSelect = (iconName: string) => {\n    onChange?.(iconName);\n    setOpen(false);\n    setSearchQuery('');\n    setCurrentPage(1);\n  };\n\n  const clearSelection = () => {\n    onChange?.('');\n  };\n\n\n\n  return (\n    <div className={className}>\n      <Popover open={open} onOpenChange={setOpen}>\n        <PopoverTrigger asChild>\n          <Button\n            variant=\"outline\"\n            role=\"combobox\"\n            aria-expanded={open}\n            className=\"w-full justify-between h-10 px-3\" // Đồng bộ chiều cao với Input (h-10)\n            disabled={disabled}\n          >\n            <div className=\"flex items-center gap-2 flex-1 min-w-0\">\n              {value ? (\n                <>\n                  <Icon icon={value} className=\"h-4 w-4 flex-shrink-0\" />\n                  <span className=\"text-sm truncate\">{value}</span>\n                </>\n              ) : (\n                <span className=\"text-muted-foreground text-sm\">{placeholder}</span>\n              )}\n            </div>\n            <div className=\"flex items-center gap-1 flex-shrink-0\">\n              {value && (\n                <Button\n                  variant=\"ghost\"\n                  size=\"sm\"\n                  className=\"h-6 w-6 p-0 hover:bg-destructive/10\"\n                  onClick={(e) => {\n                    e.stopPropagation();\n                    clearSelection();\n                  }}\n                >\n                  <X className=\"h-3 w-3\" />\n                </Button>\n              )}\n              <ChevronDown className=\"h-4 w-4 opacity-50\" />\n            </div>\n          </Button>\n        </PopoverTrigger>\n        <PopoverContent className=\"w-[480px] p-0\" align=\"start\">\n          <div className=\"flex flex-col h-[500px]\">\n            {/* Search Input */}\n            <div className=\"p-2 border-b\">\n              <Command shouldFilter={false}>\n                <CommandInput\n                  placeholder=\"Tìm kiếm icons...\"\n                  className=\"h-8\"\n                  value={searchQuery}\n                  onValueChange={setSearchQuery}\n                />\n              </Command>\n            </div>\n\n            {/* Header Info */}\n            <div className=\"px-2 py-1.5 border-b bg-muted/50\">\n              <div className=\"flex items-center justify-between text-xs text-muted-foreground\">\n                <span>\n                  {searchQuery ? `${filteredIcons.length} kết quả` : `${ALL_SOLAR_ICONS.length} Solar icons`}\n                </span>\n                {!searchQuery && totalPages > 1 && (\n                  <span>Trang {currentPage}/{totalPages}</span>\n                )}\n              </div>\n            </div>\n\n            {/* Icons Grid */}\n            <div className=\"flex-1 overflow-hidden\">\n              <ScrollArea className=\"h-full\">\n                {currentIcons.length === 0 ? (\n                  <div className=\"py-8 text-center text-sm\">\n                    <p className=\"text-muted-foreground\">\n                      {searchQuery ? 'Không tìm thấy icon phù hợp.' : 'Không có icons.'}\n                    </p>\n                    {searchQuery && (\n                      <p className=\"text-xs text-muted-foreground mt-1\">\n                        Thử từ khóa khác như: home, user, settings\n                      </p>\n                    )}\n                  </div>\n                ) : (\n                  <div className=\"grid grid-cols-6 gap-2 p-3\">\n                    {currentIcons.map((iconName) => (\n                      <button\n                        key={iconName}\n                        onClick={() => handleIconSelect(iconName)}\n                        className=\"flex flex-col items-center justify-center p-2 h-20 cursor-pointer hover:bg-accent rounded-md border-0 transition-colors group\"\n                        title={iconName}\n                      >\n                        <Icon icon={iconName} className=\"h-6 w-6 mb-1 group-hover:scale-110 transition-transform\" />\n                        <span className=\"text-xs truncate w-full text-center leading-tight\">\n                          {iconName.split(':')[1]?.replace(/-/g, ' ') || iconName}\n                        </span>\n                      </button>\n                    ))}\n                  </div>\n                )}\n              </ScrollArea>\n            </div>\n\n            {/* Pagination */}\n            {!searchQuery && totalPages > 1 && (\n              <div className=\"p-2 border-t bg-muted/50\">\n                <div className=\"flex items-center justify-between\">\n                  <Button\n                    variant=\"outline\"\n                    size=\"sm\"\n                    onClick={() => handlePageChange(currentPage - 1)}\n                    disabled={currentPage === 1}\n                    className=\"h-8\"\n                  >\n                    <ChevronLeft className=\"h-4 w-4\" />\n                    Trước\n                  </Button>\n\n                  <div className=\"flex items-center gap-1\">\n                    {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {\n                      let pageNum;\n                      if (totalPages <= 5) {\n                        pageNum = i + 1;\n                      } else if (currentPage <= 3) {\n                        pageNum = i + 1;\n                      } else if (currentPage >= totalPages - 2) {\n                        pageNum = totalPages - 4 + i;\n                      } else {\n                        pageNum = currentPage - 2 + i;\n                      }\n\n                      return (\n                        <Button\n                          key={pageNum}\n                          variant={currentPage === pageNum ? \"default\" : \"outline\"}\n                          size=\"sm\"\n                          onClick={() => handlePageChange(pageNum)}\n                          className=\"h-8 w-8 p-0\"\n                        >\n                          {pageNum}\n                        </Button>\n                      );\n                    })}\n                  </div>\n\n                  <Button\n                    variant=\"outline\"\n                    size=\"sm\"\n                    onClick={() => handlePageChange(currentPage + 1)}\n                    disabled={currentPage === totalPages}\n                    className=\"h-8\"\n                  >\n                    Sau\n                    <ChevronRight className=\"h-4 w-4\" />\n                  </Button>\n                </div>\n              </div>\n            )}\n          </div>\n        </PopoverContent>\n      </Popover>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAKA;AAIA;AACA;AAAA;AAAA;AAAA;;;AAfA;;;;;;;;AA6BA,2CAA2C;AAC3C,MAAM,wBAAwB;IAC5B,MAAM,aAAuB,EAAE;IAE/B,sEAAsE;IACtE,MAAM,iBAAiB;QACrB,kBAAkB;QAClB;QAAQ;QAAa;QAAkB;QAAgB;QAAgB;QAAc;QACrF;QAAgB;QAAgB;QAAgB;QAAgB;QAAe;QAC/E;QAAmB;QAAmB;QAAmB;QAAiB;QAC1E;QAAc;QAAe;QAAY;QAAc;QAAiB;QACxE;QAAmB;QAAoB;QAAW;QAAW;QAAU;QAAS;QAChF;QAAU;QAAU;QAAY;QAAU;QAAS;QAAQ;QAAiB;QAC5E;QAAa;QAAa;QAAS;QAAO;QAAY;QAAQ;QAAQ;QAAQ;QAE9E,iBAAiB;QACjB;QAAQ;QAAuB;QAA2B;QAAe;QACzE;QAAa;QAAc;QAAc;QAAc;QAAc;QACrE;QAAiB;QAAc;QAAW;QAAS;QAAQ;QAAS;QAEpE,yBAAyB;QACzB;QAAc;QAAe;QAAa;QAAU;QAAiB;QACrE;QAAS;QAAU;QAAW;QAAe;QAAS;QAAiB;QACvE;QAAc;QAAU;QAAU;QAAW;QAAM;QAAS;QAAc;QAC1E;QAAgB;QAAU;QAAW;QAAgB;QAAQ;QAAa;QAE1E,oBAAoB;QACpB;QAAY;QAAiB;QAAa;QAAU;QAAe;QACnE;QAAgB;QAAgB;QAAgB;QAAQ;QAAa;QACrE;QAAc;QAAc;QAAc;QAAiB;QAAe;QAC1E;QAAW;QAAY;QAAa;QAAY;QAAa;QAAW;QAExE,mBAAmB;QACnB;QAAS;QAAgB;QAAQ;QAAW;QAAQ;QAAe;QACnE;QAAQ;QAAmB;QAAmB;QAAO;QAAQ;QAAS;QACtE;QAAO;QAAS;QAAa;QAAe;QAAQ;QAAQ;QAAQ;QAEpE,mBAAmB;QACnB;QAAY;QAAyB;QAAU;QAAY;QAAY;QACvE;QAAqB;QAAmB;QAAU;QAAiB;QACnE;QAAoB;QAAgB;QAAU;QAAW;QAAY;QAAO;QAC5E;QAAc;QAAa;QAAQ;QAAQ;QAAS;QAAU;QAE9D,kBAAkB;QAClB;QAAY;QAAgB;QAAiB;QAAyB;QACtE;QAAgB;QAAW;QAAa;QAAa;QAAS;QAAa;QAC3E;QAAa;QAAgB;QAAY;QAAY;QAErD,qBAAqB;QACrB;QAAQ;QAAgB;QAAiB;QAAU;QAAkB;QACrE;QAAoB;QAAc;QAAU;QAAgB;QAC5D;QAAkB;QAAuB;QAAe;QAAgB;QACxE;QAAY;QAAe;QAAa;QAAO;QAAQ;QAAQ;QAE/D,wBAAwB;QACxB;QAAW;QAAe;QAAiB;QAAgB;QAC3D;QAAwB;QAAkB;QAAiB;QAAgB;QAC3E;QAAS;QAAU;QAAW;QAAS;QAAc;QAAuB;QAC5E;QAAe;QAAiB;QAAmB;QAAqB;QACxE;QAA6B;QAAiB;QAAgB;QAC9D;QAAqB;QAAqB;QAAqB;QAAQ;QACvE;QAAU;QAAY;QAAc;QAAU;QAAiB;QAC/D;QAAiB;QAAQ;QAAe;QAAe;QAAS;QAChE;QAAQ;QAAe;QAAY;QAAQ;QAAe;QAC1D;QAAa;QAAiB;QAAU;QAAc;QAAW;QACjE;QAAc;QAAc;QAAgB;QAAc;QAC1D;QAAgB;QAAgB;QAAe;QAAc;QAC7D;QAA0B;QAAqB;QAA2B;QAC1E;QAAwB;QAAa;QAErC,sBAAsB;QACtB;QAAQ;QAAU;QAAU;QAAU;QAAU;QAAc;QAC9D;QAAc;QAAgB;QAAgB;QAAgB;QAC9D;QAAa;QAAO;QAAS;QAAS;QAAS;QAAS;QAAa;QACrE;QAAa;QAAa;QAAa;QAAW;QAAQ;QAAU;QACpE;QAAQ;QAAU;QAAgB;QAAe;QAAa;QAC9D;QAAU;QAAY;QAAgB;QAAY;QAAc;QAChE;QAAuB;QAAQ;QAAS;QAAS;QAAO;QAAO;QAC/D;QAAa;QAAkB;QAAQ;QAAW;QAAQ;QAC1D;QAAU;QAAe;QAAW;QAAc;QAAc;QAEhE,qBAAqB;QACrB;QAAS;QAAW;QAAgB;QAAS;QAAY;QACzD;QAAgB;QAAsB;QAAc;QAAgB;QACpE;QAAa;QAAe;QAAe;QAAc;QACzD;QAAa;QAAQ;QAAqB;QAAc;QACxD;QAAY;QAAgB;QAAY;QAAc;QAAc;QACpE;QAAa;QAAe;QAAe;QAAQ;QAAQ;QAC3D;QAAU;QAAY;QAAkB;QAAc;QAAgB;QACtE;QAAa;QAAc;QAAoB;QAAoB;QAEnE,uBAAuB;QACvB;QAAc;QAAgB;QAAqB;QAAuB;QAC1E;QAAwB;QAAU;QAAU;QAAY;QACxD;QAAW;QAAkB;QAAsB;QAAM;QACzD;QAAW;QAAW;QAAkB;QAAwB;QAChE;QAAU;QAAY;QAAuB;QAAe;QAC5D;QAAY;QAAe;QAAQ;QAAU;QAAe;QAC5D;QAAe;QAAY;QAAW;QAAO;QAAoB;QACjE;QAAY;QAAY;QAAY;QAAY;QAAY;QAC5D;QAAO;QAAY;QAAa;QAAY;QAC5C;QAAO;QAAc;QAAa;QAAoB;QACtD;QAAkB;QAAe;QAA4B;QAC7D;QAAW;QAAa;QAAS;QAAW;QAAa;QACzD;QAAoB;QAAgB;QAAgB;QAAe;QACnE;QAA+B;QAAS;QAAsB;QAAQ;QAEtE,0BAA0B;QAC1B;QAAO;QAAS;QAAS;QAAsB;QAAe;QAC9D;QAAgB;QAAuB;QAAuB;QAAS;QACvE;QAAO;QAAQ;QAAS;QAAU;QAAY;QAAc;QAC5D;QAAU;QAAY;QAAO;QAAS;QAAS;QAAQ;QAAU;QACjE;QAAW;QAAgB;QAAc;QAAiB;QAAW;QACrE;QAAa;QAAiB;QAAU;QAAa;QAAO;QAAS;QACrE;QAAkB;QAAmB;QAAoB;QAAgB;QACzE;QAAiB;QAAuB;QAAsB;QAC9D;QAAoB;QAAoB;QAAoB;QAC5D;QAAO;QAAW;QAAe;QAAkB;QAAW;QAC9D;QAAa;QAAQ;QAAa;QAAY;QAE9C,mBAAmB;QACnB;QAAO;QAAS;QAAW;QAAQ;QAAY;QAAc;QAC7D;QAAS;QAAW;QAAc;QAAe;QAAe;QAChE;QAAa;QAAe;QAAc;QAAc;QAAc;QACtE;QAAe;QAAa;QAAe;QAAgB;QAC3D;QAAU;QAAO;QAAQ;QAAQ;QAAa;QAAW;QACzD;QAAQ;QAAY;QAAe;QAAoB;QACvD;QAAe;QAAQ;QAAa;QAAQ;QAAW;QAAS;QAChE;QAAU;QAAQ;QAAU;QAAU;QAAY;QAAU;QAC5D;QAAY;QAAY;QAAS;QAAU;QAAY;QAEvD,mBAAmB;QACnB;QAAU;QAAe;QAAQ;QAAS;QAAW;QAAW;QAChE;QAAe;QAAe;QAAS;QAAW;QAAO;QAAY;QACrE;QAAQ;QAAe;QAAS;QAAO;QAAW;QAAc;QAChE;QAAW;QAAW;QAAY;QAAa;QAE/C,eAAe;QACf;QAAO;QAAa;QAAW;QAAa;QAAa;QAAY;QACrE;QAAO;QAAQ;QAAa;QAAY;QAAQ;QAAU;QAC1D;QAAc;QAAS;QAAS;QAAa;QAAU;QAAS;QAChE;QAAqB;QAAS;QAAa;QAAS;QAAU;QAE9D,sBAAsB;QACtB;QAAY;QAAc;QAAe;QAAY;QAAc;QACnE;QAAQ;QAAW;QAAY;QAAkB;QACjD;QAAkB;QAAO;QAAS;QAAgB;QAAsB;QACxE;QAAa;QAAU;QAAQ;QAAW;QAAY;QAAW;QAEjE,uBAAuB;QACvB;QAAQ;QAAU;QAAiB;QAAqB;QAAa;QACrE;QAAY;QAAqB;QAAyB;QAAW;QACrE;QAAqB;QAAkB;QAAU;QAAc;QAC/D;QAAO;QAAS;QAAiB;QAAkB;QAAU;QAC7D;QAAiB;QAAiB;QAAS;QAAiB;QAC5D;QAAW;QAAa;QAAiB;QAAc;QAEvD,gBAAgB;QAChB;QAAY;QAAQ;QAAW;QAAa;QAAY;QAAO;QAC/D;QAAW;QAAa;QAAe;QAAa;QACpD;QAAW;QAAc;QAAiB;QAAU;QACpD;QAAa;QAAc;QAAQ;QAAY;QAAS;QACxD;QAAa;QAA0B;QAAS;QAChD;QAAkB;QAAa;QAAY;QAAY;QACvD;QAAU;QAAW;QAAY;QAAW;QAC5C;QAAY;QAAmB;QAAY;QAAW;QACtD;QAAc;QAAc;QAAqB;QACjD;QAAW;QAAgB;QAAS;QAAe;QACnD;QAAe;QAAW;KAC3B;IAED,oCAAoC;IACpC,MAAM,UAAU;QAAC;QAAU;QAAQ;QAAW;QAAU;QAAgB;KAAe;IAEvF,eAAe,OAAO,CAAC,CAAA;QACrB,QAAQ,OAAO,CAAC,CAAA;YACd,WAAW,IAAI,CAAC,CAAC,MAAM,EAAE,SAAS,CAAC,EAAE,QAAQ;QAC/C;IACF;IAEA,OAAO;AACT;AAEA,8BAA8B;AAC9B,MAAM,kBAAkB;AAExB,uBAAuB;AACvB,MAAM,iBAAiB,IAAI,WAAW;AAEtC,wCAAwC;AACxC,SAAS,iBAAiB,KAAa;IACrC,IAAI,CAAC,SAAS,MAAM,MAAM,GAAG,GAAG;QAC9B,OAAO,EAAE;IACX;IAEA,MAAM,aAAa,MAAM,WAAW;IAEpC,OAAO,gBAAgB,MAAM,CAAC,CAAA;QAC5B,MAAM,OAAO,SAAS,WAAW;QACjC,MAAM,WAAW,SAAS,KAAK,CAAC,IAAI,CAAC,EAAE,IAAI;QAE3C,OAAO,KAAK,QAAQ,CAAC,eACd,SAAS,QAAQ,CAAC,eAClB,SAAS,OAAO,CAAC,MAAM,KAAK,QAAQ,CAAC;IAC9C;AACF;AAEO,SAAS,WAAW,EACzB,QAAQ,EAAE,EACV,QAAQ,EACR,cAAc,cAAc,EAC5B,WAAW,KAAK,EAChB,YAAY,EAAE,EACE;;IAChB,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,sQAAA,CAAA,WAAQ,AAAD,EAAE;IACjC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,sQAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,sQAAA,CAAA,WAAQ,AAAD,EAAE;IAI/C,qCAAqC;IACrC,MAAM,gBAAgB,CAAA,GAAA,sQAAA,CAAA,UAAO,AAAD;6CAAE;YAC5B,IAAI,eAAe,YAAY,MAAM,IAAI,GAAG;gBAC1C,OAAO,iBAAiB;YAC1B;YACA,OAAO;QACT;4CAAG;QAAC;KAAY;IAEhB,uBAAuB;IACvB,MAAM,aAAa,KAAK,IAAI,CAAC,cAAc,MAAM,GAAG;IACpD,MAAM,aAAa,CAAC,cAAc,CAAC,IAAI;IACvC,MAAM,WAAW,aAAa;IAC9B,MAAM,eAAe,cAAc,KAAK,CAAC,YAAY;IAErD,iCAAiC;IACjC,CAAA,GAAA,sQAAA,CAAA,YAAS,AAAD;gCAAE;YACR,eAAe;QACjB;+BAAG;QAAC;KAAY;IAEhB,qBAAqB;IACrB,MAAM,mBAAmB,CAAC;QACxB,eAAe;IACjB;IAEA,MAAM,mBAAmB,CAAC;QACxB,WAAW;QACX,QAAQ;QACR,eAAe;QACf,eAAe;IACjB;IAEA,MAAM,iBAAiB;QACrB,WAAW;IACb;IAIA,qBACE,sSAAC;QAAI,WAAW;kBACd,cAAA,sSAAC,+HAAA,CAAA,UAAO;YAAC,MAAM;YAAM,cAAc;;8BACjC,sSAAC,+HAAA,CAAA,iBAAc;oBAAC,OAAO;8BACrB,cAAA,sSAAC,8HAAA,CAAA,SAAM;wBACL,SAAQ;wBACR,MAAK;wBACL,iBAAe;wBACf,WAAU,mCAAmC,qCAAqC;;wBAClF,UAAU;;0CAEV,sSAAC;gCAAI,WAAU;0CACZ,sBACC;;sDACE,sSAAC,6OAAA,CAAA,OAAI;4CAAC,MAAM;4CAAO,WAAU;;;;;;sDAC7B,sSAAC;4CAAK,WAAU;sDAAoB;;;;;;;iEAGtC,sSAAC;oCAAK,WAAU;8CAAiC;;;;;;;;;;;0CAGrD,sSAAC;gCAAI,WAAU;;oCACZ,uBACC,sSAAC,8HAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,WAAU;wCACV,SAAS,CAAC;4CACR,EAAE,eAAe;4CACjB;wCACF;kDAEA,cAAA,sSAAC,mRAAA,CAAA,IAAC;4CAAC,WAAU;;;;;;;;;;;kDAGjB,sSAAC,2SAAA,CAAA,cAAW;wCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;8BAI7B,sSAAC,+HAAA,CAAA,iBAAc;oBAAC,WAAU;oBAAgB,OAAM;8BAC9C,cAAA,sSAAC;wBAAI,WAAU;;0CAEb,sSAAC;gCAAI,WAAU;0CACb,cAAA,sSAAC,+HAAA,CAAA,UAAO;oCAAC,cAAc;8CACrB,cAAA,sSAAC,+HAAA,CAAA,eAAY;wCACX,aAAY;wCACZ,WAAU;wCACV,OAAO;wCACP,eAAe;;;;;;;;;;;;;;;;0CAMrB,sSAAC;gCAAI,WAAU;0CACb,cAAA,sSAAC;oCAAI,WAAU;;sDACb,sSAAC;sDACE,cAAc,GAAG,cAAc,MAAM,CAAC,QAAQ,CAAC,GAAG,GAAG,gBAAgB,MAAM,CAAC,YAAY,CAAC;;;;;;wCAE3F,CAAC,eAAe,aAAa,mBAC5B,sSAAC;;gDAAK;gDAAO;gDAAY;gDAAE;;;;;;;;;;;;;;;;;;0CAMjC,sSAAC;gCAAI,WAAU;0CACb,cAAA,sSAAC,sIAAA,CAAA,aAAU;oCAAC,WAAU;8CACnB,aAAa,MAAM,KAAK,kBACvB,sSAAC;wCAAI,WAAU;;0DACb,sSAAC;gDAAE,WAAU;0DACV,cAAc,iCAAiC;;;;;;4CAEjD,6BACC,sSAAC;gDAAE,WAAU;0DAAqC;;;;;;;;;;;6DAMtD,sSAAC;wCAAI,WAAU;kDACZ,aAAa,GAAG,CAAC,CAAC,yBACjB,sSAAC;gDAEC,SAAS,IAAM,iBAAiB;gDAChC,WAAU;gDACV,OAAO;;kEAEP,sSAAC,6OAAA,CAAA,OAAI;wDAAC,MAAM;wDAAU,WAAU;;;;;;kEAChC,sSAAC;wDAAK,WAAU;kEACb,SAAS,KAAK,CAAC,IAAI,CAAC,EAAE,EAAE,QAAQ,MAAM,QAAQ;;;;;;;+CAP5C;;;;;;;;;;;;;;;;;;;;4BAiBhB,CAAC,eAAe,aAAa,mBAC5B,sSAAC;gCAAI,WAAU;0CACb,cAAA,sSAAC;oCAAI,WAAU;;sDACb,sSAAC,8HAAA,CAAA,SAAM;4CACL,SAAQ;4CACR,MAAK;4CACL,SAAS,IAAM,iBAAiB,cAAc;4CAC9C,UAAU,gBAAgB;4CAC1B,WAAU;;8DAEV,sSAAC,2SAAA,CAAA,cAAW;oDAAC,WAAU;;;;;;gDAAY;;;;;;;sDAIrC,sSAAC;4CAAI,WAAU;sDACZ,MAAM,IAAI,CAAC;gDAAE,QAAQ,KAAK,GAAG,CAAC,GAAG;4CAAY,GAAG,CAAC,GAAG;gDACnD,IAAI;gDACJ,IAAI,cAAc,GAAG;oDACnB,UAAU,IAAI;gDAChB,OAAO,IAAI,eAAe,GAAG;oDAC3B,UAAU,IAAI;gDAChB,OAAO,IAAI,eAAe,aAAa,GAAG;oDACxC,UAAU,aAAa,IAAI;gDAC7B,OAAO;oDACL,UAAU,cAAc,IAAI;gDAC9B;gDAEA,qBACE,sSAAC,8HAAA,CAAA,SAAM;oDAEL,SAAS,gBAAgB,UAAU,YAAY;oDAC/C,MAAK;oDACL,SAAS,IAAM,iBAAiB;oDAChC,WAAU;8DAET;mDANI;;;;;4CASX;;;;;;sDAGF,sSAAC,8HAAA,CAAA,SAAM;4CACL,SAAQ;4CACR,MAAK;4CACL,SAAS,IAAM,iBAAiB,cAAc;4CAC9C,UAAU,gBAAgB;4CAC1B,WAAU;;gDACX;8DAEC,sSAAC,6SAAA,CAAA,eAAY;oDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAU5C;GAjNgB;KAAA", "debugId": null}}, {"offset": {"line": 3026, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/bloomgoo/gold-exchange-platform/frontend/components/common/admin/website-settings/website-settings.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { useState, useEffect, useCallback, useMemo } from 'react';\r\nimport { Button } from '@/components/ui/button';\r\nimport { Input } from '@/components/ui/input';\r\nimport { Label } from '@/components/ui/label';\r\nimport { Textarea } from '@/components/ui/textarea';\r\nimport { Switch } from '@/components/ui/switch';\r\nimport { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';\r\nimport { api } from '@/lib/api';\r\nimport { toast } from 'sonner';\r\nimport { Separator } from '@/components/ui/separator';\r\nimport {\r\n  Loader2, Settings2, Mail, Share2, BarChart,\r\n  Layout, Home, Briefcase, Zap, Search, Palette, Shield\r\n} from 'lucide-react';\r\nimport { cn } from '@/lib/utils';\r\nimport { updateSettingsByGroup } from './update-settings';\r\nimport { ImageUpload } from '@/components/ui/image-upload';\r\nimport { IconPicker } from '@/components/ui/icon-picker';\r\n\r\ninterface SystemSetting {\r\n  id: string;                    // ID duy nhất của cấu hình\r\n  key: string;                   // Khóa cấu hình (config key)\r\n  value: string;                 // Giá trị cấu hình\r\n  description?: string;          // Mô tả cấu hình (hiển thị cho admin)\r\n  group: string;                 // Nhóm cấu hình (ví dụ: website_general, website_contact)\r\n  type: 'text' | 'textarea' | 'number' | 'boolean' | 'select' | 'image' | 'icon'; // Loại input\r\n  options?: string[];            // Các tùy chọn cho type 'select'\r\n\r\n  // Thông tin hiển thị của group\r\n  groupDisplayName?: string;     // Tên hiển thị của nhóm\r\n  groupDescription?: string;     // Mô tả của nhóm\r\n  groupIcon?: string;            // Icon của nhóm\r\n  groupOrder?: number;           // Thứ tự hiển thị của nhóm\r\n\r\n  // Thông tin hiển thị của section\r\n  sectionName?: string;          // Tên section trong group để tổ chức thành các vùng riêng biệt\r\n  sectionDisplayName?: string;   // Tên hiển thị của section\r\n  sectionDescription?: string;   // Mô tả của section\r\n  sectionOrder?: number;         // Thứ tự hiển thị của section trong group\r\n  displayOrder?: number;         // Thứ tự hiển thị của trường cấu hình trong section\r\n\r\n  // Cấu hình đặc biệt\r\n  isGroupConfig?: boolean;       // Đánh dấu đây là cấu hình của nhóm (không phải setting thực tế)\r\n}\r\n\r\nexport default function WebsiteSettings() {\r\n  const [settings, setSettings] = useState<SystemSetting[]>([]);\r\n  const [formData, setFormData] = useState<Record<string, string | boolean | number>>({});\r\n  const [isLoading, setIsLoading] = useState(true);\r\n  const [isSaving, setIsSaving] = useState(false);\r\n  const [activeTab, setActiveTab] = useState('');\r\n\r\n  // Group settings by group and section (sử dụng useMemo để tránh re-compute)\r\n  const sectionedSettings = useMemo(() => {\r\n    const result = settings.reduce((acc, setting) => {\r\n      const group = setting.group;\r\n      const section = setting.sectionName || 'default';\r\n\r\n      if (!acc[group]) {\r\n        acc[group] = {};\r\n      }\r\n      if (!acc[group][section]) {\r\n        acc[group][section] = [];\r\n      }\r\n      acc[group][section].push(setting);\r\n      return acc;\r\n    }, {} as Record<string, Record<string, SystemSetting[]>>);\r\n    return result;\r\n  }, [settings]);\r\n\r\n  // Danh sách nhóm cấu hình website cố định\r\n  const predefinedGroups = [\r\n    { id: 'website_general', displayName: 'Cấu hình chung', description: 'Cấu hình chung của website', icon: 'settings', order: 1 },\r\n    { id: 'website_seo', displayName: 'SEO & Meta', description: 'Cấu hình SEO và metadata', icon: 'search', order: 2 },\r\n    { id: 'website_social', displayName: 'Mạng xã hội', description: 'Liên kết mạng xã hội', icon: 'share', order: 3 },\r\n    { id: 'website_legal', displayName: 'Pháp lý', description: 'Điều khoản, chính sách bảo mật', icon: 'shield', order: 4 },\r\n    { id: 'website_header', displayName: 'Cấu hình Header', description: 'Cấu hình header và navigation', icon: 'layout', order: 5 },\r\n    { id: 'website_footer', displayName: 'Footer', description: 'Cấu hình footer website', icon: 'layout', order: 6 },\r\n    { id: 'website_homepage', displayName: 'Trang chủ', description: 'Cấu hình hiển thị trang chủ', icon: 'home', order: 7 },\r\n    { id: 'website_branding', displayName: 'Thương hiệu', description: 'Cấu hình thương hiệu', icon: 'palette', order: 8 },\r\n    { id: 'website_services', displayName: 'Dịch vụ', description: 'Cấu hình trang dịch vụ', icon: 'briefcase', order: 9 },\r\n    { id: 'website_contact', displayName: 'Liên hệ', description: 'Thông tin liên hệ hiển thị trên website', icon: 'mail', order: 10 },\r\n    { id: 'website_analytics', displayName: 'Analytics', description: 'Cấu hình Google Analytics, Facebook Pixel', icon: 'bar-chart', order: 11 },\r\n   \r\n  ];\r\n\r\n  // Lấy danh sách ID nhóm\r\n  const groups = predefinedGroups.map(group => group.id);\r\n\r\n  useEffect(() => {\r\n    fetchSettings();\r\n  }, []);\r\n\r\n  // Set default active tab if not set\r\n  useEffect(() => {\r\n    if (activeTab === '' && groups.length > 0) {\r\n      setActiveTab(groups[0]);\r\n    }\r\n  }, [groups, activeTab]);\r\n\r\n  const fetchSettings = async () => {\r\n    setIsLoading(true);\r\n    try {\r\n      // Get settings from API - lấy tất cả cấu hình với limit tối đa cho phép\r\n       \r\n      // Sử dụng nhiều request để lấy tất cả dữ liệu\r\n      let allSettings: any[] = [];\r\n      let page = 1;\r\n      const limit = 100; // Giới hạn tối đa cho phép\r\n      let hasMoreData = true;\r\n\r\n      while (hasMoreData) {\r\n         \r\n        const pageResponse = await api.get<any>(`system-configs?limit=${limit}&page=${page}&search=website_`);\r\n\r\n        // Xác định dữ liệu từ response\r\n        let pageData: any[] = [];\r\n         \r\n\r\n        // Cấu trúc mới: response.data.data.data\r\n        if (pageResponse.data && pageResponse.data.data && pageResponse.data.data.data && Array.isArray(pageResponse.data.data.data)) {\r\n          pageData = pageResponse.data.data.data;\r\n           \r\n        }\r\n        // Cấu trúc cũ: response.data.data\r\n        else if (pageResponse.data && pageResponse.data.data && Array.isArray(pageResponse.data.data)) {\r\n          pageData = pageResponse.data.data;\r\n           \r\n        }\r\n        // Cấu trúc khác: response.data\r\n        else if (pageResponse.data && Array.isArray(pageResponse.data)) {\r\n          pageData = pageResponse.data;\r\n           \r\n        }\r\n\r\n         \r\n\r\n        // Lọc chỉ lấy settings có key bắt đầu với website_\r\n        const websiteData = pageData.filter((item: any) =>\r\n          item.configKey && item.configKey.startsWith('website_')\r\n        );\r\n\r\n        // Thêm dữ liệu vào mảng kết quả\r\n        allSettings = [...allSettings, ...websiteData];\r\n\r\n        // Kiểm tra xem còn dữ liệu không\r\n        if (pageData.length < limit) {\r\n          hasMoreData = false;\r\n        } else {\r\n          page++;\r\n        }\r\n      }\r\n\r\n       \r\n\r\n      // Nếu không có dữ liệu, hiển thị thông báo\r\n      if (allSettings.length === 0) {\r\n        console.error('Không tìm thấy dữ liệu cấu hình nào');\r\n        toast.error('Không tìm thấy dữ liệu cấu hình nào');\r\n        setIsLoading(false);\r\n        return;\r\n      }\r\n\r\n      const response = { data: { data: allSettings } };\r\n       \r\n\r\n      // Kiểm tra cấu trúc response\r\n      if (!response) {\r\n        console.error('Không nhận được response từ API');\r\n        toast.error('Không thể tải cấu hình từ API');\r\n        setIsLoading(false);\r\n        return;\r\n      }\r\n\r\n      // Xác định dữ liệu từ response - cấu trúc mới: response.data.data\r\n      let apiData = [];\r\n      if (response.data && response.data.data && Array.isArray(response.data.data)) {\r\n        apiData = response.data.data;\r\n      } else if (response.data && Array.isArray(response.data)) {\r\n        apiData = response.data;\r\n      } else if (Array.isArray(response)) {\r\n        apiData = response;\r\n      } else {\r\n        console.error('Cấu trúc dữ liệu không đúng định dạng:', response);\r\n        toast.error('Cấu trúc dữ liệu không đúng định dạng');\r\n        setIsLoading(false);\r\n        return;\r\n      }\r\n\r\n       \r\n       \r\n\r\n      // Convert API response to frontend format\r\n      const apiSettings = apiData.map((item: any) => ({\r\n        id: item.id || '', // Sử dụng chuỗi rỗng nếu không có ID\r\n        key: item.configKey,\r\n        value: item.configValue || '',\r\n        description: item.description || '',\r\n        group: item.configGroup || 'website_general',\r\n        type: item.configType || 'text',\r\n        options: item.configOptions ?\r\n          (typeof item.configOptions === 'string' ? JSON.parse(item.configOptions) : item.configOptions)\r\n          : undefined,\r\n        groupDisplayName: item.groupDisplayName,\r\n        groupDescription: item.groupDescription,\r\n        groupIcon: item.groupIcon,\r\n        groupOrder: item.groupOrder,\r\n        sectionName: item.sectionName,\r\n        sectionDisplayName: item.sectionDisplayName,\r\n        sectionDescription: item.sectionDescription,\r\n        sectionOrder: item.sectionOrder,\r\n        displayOrder: item.displayOrder,\r\n        isGroupConfig: item.isGroupConfig\r\n      }));\r\n\r\n      // Kiểm tra xem có bao nhiêu cấu hình không có ID\r\n      const configsWithoutId = apiSettings.filter((setting: SystemSetting) => !setting.id);\r\n      if (configsWithoutId.length > 0) {\r\n         \r\n      }\r\n\r\n       \r\n\r\n\r\n\r\n      setSettings(apiSettings);\r\n\r\n      // Initialize form data\r\n      const initialFormData: Record<string, string | boolean | number> = {};\r\n      apiSettings.forEach((setting: SystemSetting) => {\r\n        if (setting.type === 'boolean') {\r\n          initialFormData[setting.key] = setting.value === 'true';\r\n        } else if (setting.type === 'number') {\r\n          initialFormData[setting.key] = parseFloat(setting.value) || 0;\r\n        } else {\r\n          initialFormData[setting.key] = setting.value;\r\n        }\r\n      });\r\n      setFormData(initialFormData);\r\n    } catch (error: any) {\r\n      console.error('Error fetching settings:', error);\r\n      if (error.response) {\r\n        console.error('Error response:', error.response.data);\r\n        toast.error(`Không thể tải cấu hình: ${error.response.data.message || 'Lỗi không xác định'}`);\r\n      } else {\r\n        toast.error('Không thể tải cấu hình từ API');\r\n      }\r\n    } finally {\r\n      setIsLoading(false);\r\n    }\r\n  };\r\n\r\n  const handleInputChange = (key: string, value: string | boolean | number) => {\r\n    setFormData((prev) => ({ ...prev, [key]: value }));\r\n  };\r\n\r\n  const handleSaveSettings = async (group: string) => {\r\n    setIsSaving(true);\r\n    try {\r\n       \r\n\r\n      // Lấy thông tin hiển thị của nhóm từ danh sách cố định\r\n      const groupInfo = predefinedGroups.find(g => g.id === group);\r\n      const displayName = groupInfo?.displayName || `Cấu hình ${group.charAt(0).toUpperCase() + group.slice(1)}`;\r\n      const description = groupInfo?.description || `Cấu hình ${group.charAt(0).toUpperCase() + group.slice(1)}`;\r\n      const icon = groupInfo?.icon || '';\r\n      const order = groupInfo?.order || 999;\r\n\r\n      // Cập nhật thông tin hiển thị cho tất cả cấu hình trong nhóm\r\n      const updatedSettings = settings.map(setting => {\r\n        if (setting.group === group) {\r\n          return {\r\n            ...setting,\r\n            groupDisplayName: displayName,\r\n            groupDescription: description,\r\n            groupIcon: icon,\r\n            groupOrder: order\r\n          };\r\n        }\r\n        return setting;\r\n      });\r\n\r\n      // Kiểm tra xem có cấu hình nhóm không\r\n      const groupConfig = updatedSettings.find(setting =>\r\n        setting.group === group && setting.isGroupConfig === true\r\n      );\r\n\r\n      // Nếu không có cấu hình nhóm, tạo một cấu hình nhóm mới\r\n      if (!groupConfig) {\r\n         \r\n\r\n        // Tạo cấu hình nhóm mới\r\n        const newGroupConfig: SystemSetting = {\r\n          id: '', // ID sẽ được tạo bởi backend\r\n          key: `GROUP_${group.toUpperCase()}`,\r\n          value: group,\r\n          description: `Group configuration for ${group}`,\r\n          group: group,\r\n          type: 'text',\r\n          groupDisplayName: displayName,\r\n          groupDescription: description,\r\n          groupIcon: icon,\r\n          groupOrder: order,\r\n          isGroupConfig: true\r\n        };\r\n\r\n        // Thêm cấu hình nhóm mới vào danh sách cấu hình\r\n        updatedSettings.push(newGroupConfig);\r\n      } else {\r\n        // Cập nhật thông tin hiển thị cho cấu hình nhóm hiện có\r\n        const updatedGroupConfig = {\r\n          ...groupConfig,\r\n          groupDisplayName: displayName,\r\n          groupDescription: description,\r\n          groupIcon: icon,\r\n          groupOrder: order\r\n        };\r\n\r\n        // Cập nhật cấu hình nhóm trong danh sách\r\n        const index = updatedSettings.findIndex(setting =>\r\n          setting.group === group && setting.isGroupConfig === true\r\n        );\r\n\r\n        if (index !== -1) {\r\n          updatedSettings[index] = updatedGroupConfig;\r\n        }\r\n      }\r\n\r\n      // Sử dụng phương thức cập nhật với cấu hình đã cập nhật\r\n      const success = await updateSettingsByGroup(group, formData, updatedSettings);\r\n\r\n      if (success) {\r\n         \r\n        toast.success('Cập nhật cấu hình thành công');\r\n\r\n        // Không cần tải lại dữ liệu, chỉ cập nhật state hiện tại\r\n        setSettings(updatedSettings);\r\n      }\r\n    } catch (error: any) {\r\n      console.error('Lỗi khi cập nhật cấu hình:', error);\r\n      if (error.response) {\r\n        console.error('Chi tiết lỗi:', error.response.data);\r\n      }\r\n      toast.error('Không thể cập nhật cấu hình');\r\n    } finally {\r\n      setIsSaving(false);\r\n    }\r\n  };\r\n\r\n  const renderSettingInput = (setting: SystemSetting) => {\r\n    const value = formData[setting.key];\r\n\r\n    // Xử lý các loại setting\r\n    switch (setting.type) {\r\n      case 'text':\r\n        return (\r\n          <Input\r\n            id={setting.key}\r\n            value={value as string}\r\n            onChange={(e) => handleInputChange(setting.key, e.target.value)}\r\n            className=\"w-full\"\r\n          />\r\n        );\r\n      case 'textarea':\r\n        return (\r\n          <Textarea\r\n            id={setting.key}\r\n            value={value as string}\r\n            onChange={(e) => handleInputChange(setting.key, e.target.value)}\r\n            rows={3}\r\n            className=\"w-full resize-y min-h-[80px]\"\r\n          />\r\n        );\r\n      case 'number':\r\n        return (\r\n          <Input\r\n            id={setting.key}\r\n            type=\"number\"\r\n            value={value as number}\r\n            onChange={(e) => handleInputChange(setting.key, parseFloat(e.target.value))}\r\n            className=\"w-full\"\r\n          />\r\n        );\r\n      case 'boolean':\r\n        return (\r\n          <div className=\"flex items-center space-x-2\">\r\n            <Switch\r\n              id={setting.key}\r\n              checked={value as boolean}\r\n              onCheckedChange={(checked) => handleInputChange(setting.key, checked)}\r\n            />\r\n            <Label htmlFor={setting.key} className=\"text-sm text-muted-foreground cursor-pointer\">\r\n              {(value as boolean) ? 'Bật' : 'Tắt'}\r\n            </Label>\r\n          </div>\r\n        );\r\n      case 'select':\r\n        return (\r\n          <Select\r\n            value={value as string}\r\n            onValueChange={(newValue) => handleInputChange(setting.key, newValue)}\r\n          >\r\n            <SelectTrigger className=\"w-full\">\r\n              <SelectValue placeholder=\"Chọn giá trị\" />\r\n            </SelectTrigger>\r\n            <SelectContent>\r\n              {setting.options?.map((option) => (\r\n                <SelectItem key={option} value={option}>\r\n                  {option}\r\n                </SelectItem>\r\n              ))}\r\n            </SelectContent>\r\n          </Select>\r\n        );\r\n      case 'image':\r\n        return (\r\n          <div className=\"space-y-2\">\r\n            <ImageUpload\r\n              value={value as string}\r\n              onChange={(newValue) => handleInputChange(setting.key, newValue)}\r\n              endpoint=\"/attachments/upload/website-settings\" // Endpoint upload mặc định\r\n              maxSize={5 * 1024 * 1024} // 5MB\r\n              allowedTypes={['image/jpeg', 'image/png', 'image/gif', 'image/webp']}\r\n              placeholder={`Tải lên hình ảnh cho ${setting.description || setting.key}`}\r\n              className=\"w-full\"\r\n            />\r\n            <p className=\"text-xs text-muted-foreground\">\r\n              Hỗ trợ: JPG, PNG, GIF, WebP. Tối đa 5MB.\r\n            </p>\r\n          </div>\r\n        );\r\n      case 'icon':\r\n        return (\r\n          <div className=\"space-y-2\">\r\n            <IconPicker\r\n              value={value as string}\r\n              onChange={(newValue) => handleInputChange(setting.key, newValue)}\r\n              placeholder={`Chọn icon cho ${setting.description || setting.key}`}\r\n              className=\"w-full\"\r\n            />\r\n            <p className=\"text-xs text-muted-foreground\">\r\n              Chọn từ thư viện icon Iconify hoặc nhập tên icon tùy chỉnh.\r\n            </p>\r\n          </div>\r\n        );\r\n      default:\r\n        return (\r\n          <Input\r\n            id={setting.key}\r\n            value={value as string}\r\n            onChange={(e) => handleInputChange(setting.key, e.target.value)}\r\n            className=\"w-full\"\r\n          />\r\n        );\r\n    }\r\n  };\r\n\r\n\r\n\r\n\r\n\r\n  // Mapping icon từ tên icon sang component cho website settings\r\n  const iconComponentMap: Record<string, React.ReactNode> = {\r\n    'settings': <Settings2 className=\"h-4 w-4 mr-2\" />,\r\n    'mail': <Mail className=\"h-4 w-4 mr-2\" />,\r\n    'layout': <Layout className=\"h-4 w-4 mr-2\" />,\r\n    'home': <Home className=\"h-4 w-4 mr-2\" />,\r\n    'briefcase': <Briefcase className=\"h-4 w-4 mr-2\" />,\r\n    'zap': <Zap className=\"h-4 w-4 mr-2\" />,\r\n    'search': <Search className=\"h-4 w-4 mr-2\" />,\r\n    'share': <Share2 className=\"h-4 w-4 mr-2\" />,\r\n    'bar-chart': <BarChart className=\"h-4 w-4 mr-2\" />,\r\n    'palette': <Palette className=\"h-4 w-4 mr-2\" />,\r\n    'shield': <Shield className=\"h-4 w-4 mr-2\" />,\r\n  };\r\n\r\n  // Không cần fetch thông tin nhóm cấu hình từ backend vì đã có danh sách cố định\r\n\r\n  // Tải dữ liệu khi người dùng chọn một nhóm cấu hình\r\n  const fetchGroupSettings = useCallback(async (group: string) => {\r\n    try {\r\n       \r\n      const response = await api.get<any>(`system-configs/group/${group}?limit=100`);\r\n\r\n      // Xác định dữ liệu từ response\r\n      let groupData: any[] = [];\r\n\r\n      // Cấu trúc mới: response.data.data.data\r\n      if (response.data && response.data.data && response.data.data.data && Array.isArray(response.data.data.data)) {\r\n        groupData = response.data.data.data;\r\n      }\r\n      // Cấu trúc cũ: response.data.data\r\n      else if (response.data && response.data.data && Array.isArray(response.data.data)) {\r\n        groupData = response.data.data;\r\n      }\r\n      // Cấu trúc khác: response.data\r\n      else if (response.data && Array.isArray(response.data)) {\r\n        groupData = response.data;\r\n      }\r\n\r\n       \r\n\r\n      if (groupData.length > 0) {\r\n        // Chuyển đổi dữ liệu\r\n        const apiSettings = groupData.map((item: any) => ({\r\n          id: item.id || '',\r\n          key: item.configKey,\r\n          value: item.configValue || '',\r\n          description: item.description || '',\r\n          group: item.configGroup || 'website_general',\r\n          sectionName: item.sectionName, // Thêm sectionName từ API\r\n          type: item.configType || 'text',\r\n          options: item.configOptions ?\r\n            (typeof item.configOptions === 'string' ? JSON.parse(item.configOptions) : item.configOptions)\r\n            : undefined,\r\n          groupDisplayName: item.groupDisplayName,\r\n          groupDescription: item.groupDescription,\r\n          groupIcon: item.groupIcon,\r\n          groupOrder: item.groupOrder,\r\n          sectionDisplayName: item.sectionDisplayName, // Thêm section display name\r\n          sectionDescription: item.sectionDescription, // Thêm section description\r\n          sectionOrder: item.sectionOrder, // Thêm section order\r\n          displayOrder: item.displayOrder, // Thêm display order\r\n          isGroupConfig: item.isGroupConfig\r\n        }));\r\n\r\n        // Cập nhật settings cho nhóm này\r\n        setSettings(prevSettings => {\r\n          // Lọc ra các cấu hình không thuộc nhóm này\r\n          const otherSettings = prevSettings.filter(setting => setting.group !== group);\r\n          // Kết hợp với cấu hình mới\r\n          return [...otherSettings, ...apiSettings];\r\n        });\r\n\r\n        // Cập nhật formData\r\n        const newFormData: Record<string, string | boolean | number> = { ...formData };\r\n        apiSettings.forEach((setting: SystemSetting) => {\r\n          if (setting.type === 'boolean') {\r\n            newFormData[setting.key] = setting.value === 'true';\r\n          } else if (setting.type === 'number') {\r\n            newFormData[setting.key] = parseFloat(setting.value) || 0;\r\n          } else if (setting.type === 'image') {\r\n            // URL type được xử lý như string\r\n            newFormData[setting.key] = setting.value || '';\r\n          } else if (setting.type === 'icon') {\r\n            // Icon type được xử lý như string\r\n            newFormData[setting.key] = setting.value || '';\r\n          } else {\r\n            newFormData[setting.key] = setting.value;\r\n          }\r\n        });\r\n        setFormData(newFormData);\r\n\r\n         \r\n      }\r\n    } catch (error: any) {\r\n      console.error(`Lỗi khi tải dữ liệu cho nhóm ${group}:`, error);\r\n      if (error.response) {\r\n        console.error('Error response:', error.response.data);\r\n        toast.error(`Không thể tải cấu hình nhóm ${group}: ${error.response.data.message || 'Lỗi không xác định'}`);\r\n      } else {\r\n        toast.error(`Không thể tải cấu hình nhóm ${group}`);\r\n      }\r\n    }\r\n  }, []); // Loại bỏ formData dependency để tránh infinite loop\r\n\r\n  // Tải dữ liệu khi người dùng chọn một nhóm cấu hình\r\n  useEffect(() => {\r\n    if (activeTab && !isLoading) {\r\n      console.log('🔄 Fetching group settings for:', activeTab);\r\n      fetchGroupSettings(activeTab);\r\n    }\r\n  }, [activeTab]); // Chỉ depend on activeTab để tránh infinite loop\r\n\r\n  // Không cần cập nhật thông tin nhóm khi phát hiện nhóm mới vì đã có danh sách cố định\r\n\r\n  // Helper function to get group icon\r\n  const getGroupIcon = (group: string) => {\r\n    const groupInfo = predefinedGroups.find(g => g.id === group);\r\n    const iconName = groupInfo?.icon || 'settings';\r\n    if (iconName && iconComponentMap[iconName]) {\r\n      return iconComponentMap[iconName];\r\n    }\r\n    return <Settings2 className=\"h-4 w-4 mr-2\" />;\r\n  };\r\n\r\n  // Helper function to get section display name\r\n  const getSectionDisplayName = (group: string, sectionName: string, settings: SystemSetting[]): string => {\r\n    // Tìm setting đầu tiên trong section này có sectionDisplayName\r\n    const settingWithDisplayName = settings.find(s =>\r\n      s.group === group &&\r\n      (s.sectionName || 'default') === sectionName &&\r\n      s.sectionDisplayName\r\n    );\r\n\r\n    if (settingWithDisplayName?.sectionDisplayName) {\r\n      return settingWithDisplayName.sectionDisplayName;\r\n    }\r\n\r\n    // Fallback: capitalize section name\r\n    if (sectionName === 'default') {\r\n      return 'Cấu hình chung';\r\n    }\r\n    return sectionName.charAt(0).toUpperCase() + sectionName.slice(1).replace(/_/g, ' ');\r\n  };\r\n\r\n  // Helper function to get section description\r\n  const getSectionDescription = (group: string, sectionName: string, settings: SystemSetting[]): string => {\r\n    // Tìm setting đầu tiên trong section này có sectionDescription\r\n    const settingWithDescription = settings.find(s =>\r\n      s.group === group &&\r\n      (s.sectionName || 'default') === sectionName &&\r\n      s.sectionDescription\r\n    );\r\n\r\n    return settingWithDescription?.sectionDescription || '';\r\n  };\r\n\r\n  // Helper function to get section order\r\n  const getSectionOrder = (group: string, sectionName: string, settings: SystemSetting[]): number => {\r\n    // Tìm setting đầu tiên trong section này có sectionOrder\r\n    const settingWithOrder = settings.find(s =>\r\n      s.group === group &&\r\n      (s.sectionName || 'default') === sectionName &&\r\n      s.sectionOrder !== undefined\r\n    );\r\n\r\n    return settingWithOrder?.sectionOrder ?? 999;\r\n  };\r\n\r\n  // Helper function to sort sections by order\r\n  const getSortedSections = (group: string): string[] => {\r\n    if (!sectionedSettings[group]) return [];\r\n\r\n    const sections = Object.keys(sectionedSettings[group]);\r\n    return sections.sort((a, b) => {\r\n      // Lấy order từ settings trong section\r\n      const orderA = getSectionOrder(group, a, settings); // Sử dụng toàn bộ settings array\r\n      const orderB = getSectionOrder(group, b, settings); // Sử dụng toàn bộ settings array\r\n\r\n      // Nếu order bằng nhau, sắp xếp theo tên (default luôn đầu tiên)\r\n      if (orderA === orderB) {\r\n        if (a === 'default') return -1;\r\n        if (b === 'default') return 1;\r\n        return a.localeCompare(b);\r\n      }\r\n\r\n      return orderA - orderB;\r\n    });\r\n  };\r\n\r\n  // Helper function to get group display name\r\n  const getGroupDisplayName = (group: string) => {\r\n    const groupInfo = predefinedGroups.find(g => g.id === group);\r\n    return groupInfo?.displayName || `Cấu hình ${group}`;\r\n  };\r\n\r\n  // Helper function to get group description\r\n  const getGroupDescription = (group: string) => {\r\n    const groupInfo = predefinedGroups.find(g => g.id === group);\r\n    return groupInfo?.description || `Cấu hình ${group}`;\r\n  };\r\n\r\n  // Helper function to sort groups by order\r\n  const getSortedGroups = () => {\r\n    // Sắp xếp theo thứ tự từ danh sách cố định\r\n    return [...groups].sort((a, b) => {\r\n      const groupA = predefinedGroups.find(g => g.id === a);\r\n      const groupB = predefinedGroups.find(g => g.id === b);\r\n      const orderA = groupA?.order || 999;\r\n      const orderB = groupB?.order || 999;\r\n      return orderA - orderB;\r\n    });\r\n  };\r\n\r\n\r\n\r\n  if (isLoading) {\r\n    return (\r\n      <div className=\"flex items-center justify-center h-[400px]\">\r\n        <Loader2 className=\"h-8 w-8 animate-spin text-primary\" />\r\n      </div>\r\n    );\r\n  }\r\n\r\n\r\n\r\n  return (\r\n    <div className=\"w-full h-full\">\r\n      <div className=\"flex w-full h-full\">\r\n        {/* Sidebar Menu */}\r\n        <div className=\"w-60 min-w-60 bg-muted/20 border-r p-2 space-y-0.5\">\r\n          {getSortedGroups().map((group) => (\r\n            <button\r\n              key={group}\r\n              onClick={() => setActiveTab(group)}\r\n              className={cn(\r\n                \"flex items-center w-full text-left px-3 py-2.5 rounded-md text-sm\",\r\n                activeTab === group\r\n                  ? \"bg-primary/10 text-primary font-medium\"\r\n                  : \"hover:bg-muted/50 text-foreground\"\r\n              )}\r\n            >\r\n              {getGroupIcon(group)}\r\n              <span className=\"capitalize\">\r\n                {getGroupDisplayName(group).replace('Cấu hình ', '')}\r\n              </span>\r\n            </button>\r\n          ))}\r\n        </div>\r\n\r\n        {/* Content Area */}\r\n        <div className=\"flex-1 min-w-0 overflow-auto\">\r\n          <div className=\"w-full\">\r\n            {groups.map((group) => (\r\n              activeTab === group && (\r\n                <div key={group}>\r\n                  <div className=\"p-4\">\r\n                    <h2 className=\"text-lg font-medium\">{getGroupDisplayName(group)}</h2>\r\n                    <p className=\"text-sm text-muted-foreground mt-1\">{getGroupDescription(group)}</p>\r\n                  </div>\r\n                  <Separator />\r\n                  <div className=\"p-4\">\r\n                    <div className=\"space-y-6\">\r\n                      {sectionedSettings[group] ? (\r\n                        // Hiển thị theo sections\r\n                        getSortedSections(group).map((sectionName, sectionIndex) => {\r\n                          const sectionSettings = sectionedSettings[group][sectionName]\r\n                            .filter(setting => !setting.isGroupConfig) // Lọc ra các cấu hình không phải là cấu hình nhóm\r\n                            .sort((a, b) => {\r\n                              // Sắp xếp theo displayOrder ASC, sau đó theo key\r\n                              const orderA = a.displayOrder ?? 999;\r\n                              const orderB = b.displayOrder ?? 999;\r\n                              if (orderA !== orderB) {\r\n                                return orderA - orderB;\r\n                              }\r\n                              return a.key.localeCompare(b.key);\r\n                            });\r\n\r\n                          if (sectionSettings.length === 0) return null;\r\n\r\n                          const sectionDisplayName = getSectionDisplayName(group, sectionName, settings); // Sử dụng toàn bộ settings\r\n                          const sectionDescription = getSectionDescription(group, sectionName, settings); // Sử dụng toàn bộ settings\r\n                          const showSectionHeader = sectionName !== 'default' || getSortedSections(group).length > 1;\r\n\r\n                          // Debug log cho section info (chỉ trong development)\r\n                          if (process.env.NODE_ENV === 'development') {\r\n                            console.log(`🔍 Section Info - Group: ${group}, Section: ${sectionName}`, {\r\n                              displayName: sectionDisplayName,\r\n                              description: sectionDescription,\r\n                              settingsCount: sectionSettings.length,\r\n                              showHeader: showSectionHeader\r\n                            });\r\n                          }\r\n\r\n                          return (\r\n                            <div key={sectionName} className=\"space-y-4\">\r\n                              {/* Section Header */}\r\n                              {showSectionHeader && (\r\n                                <div className=\"space-y-2\">\r\n                                  <h3 className=\"text-base font-medium text-foreground\">\r\n                                    {sectionDisplayName}\r\n                                  </h3>\r\n                                  {sectionDescription && (\r\n                                    <p className=\"text-sm text-muted-foreground\">\r\n                                      {sectionDescription}\r\n                                    </p>\r\n                                  )}\r\n                                  <Separator />\r\n                                </div>\r\n                              )}\r\n\r\n                              {/* Section Settings */}\r\n                              <div className=\"space-y-4\">\r\n                                {sectionSettings.map((setting, settingIndex) => (\r\n                                  <div key={setting.id} className=\"space-y-2\">\r\n                                    <div className=\"grid grid-cols-1 md:grid-cols-12 gap-4 items-start\">\r\n                                      <div className=\"space-y-1 md:col-span-4\">\r\n                                        <Label htmlFor={setting.key} className=\"text-sm font-medium text-foreground\">\r\n                                          {setting.description || setting.key}\r\n                                        </Label>\r\n                                        \r\n                                      </div>\r\n                                      <div className=\"md:col-span-8\">\r\n                                        {renderSettingInput(setting)}\r\n                                      </div>\r\n                                    </div>\r\n                                    {settingIndex < sectionSettings.length - 1 && (\r\n                                      <Separator className=\"my-4\" />\r\n                                    )}\r\n                                  </div>\r\n                                ))}\r\n                              </div>\r\n\r\n                              {/* Section Separator */}\r\n                              {sectionIndex < getSortedSections(group).length - 1 && (\r\n                                <div className=\"py-2\">\r\n                                  <Separator className=\"bg-border/60\" />\r\n                                </div>\r\n                              )}\r\n                            </div>\r\n                          );\r\n                        })\r\n                      ) : (\r\n                        // Hiển thị thông báo khi không có dữ liệu\r\n                        <div className=\"p-4 text-center\">\r\n                          <p className=\"text-muted-foreground\">Chưa có cấu hình nào cho nhóm này.</p>\r\n                          <p className=\"text-sm text-muted-foreground mt-2\">\r\n                            Hãy tạo cấu hình mới từ backend hoặc import dữ liệu mẫu.\r\n                          </p>\r\n                        </div>\r\n                      )}\r\n                    </div>\r\n                  </div>\r\n                  <div className=\"flex justify-end p-4 border-t\">\r\n                    <Button\r\n                      onClick={() => handleSaveSettings(group)}\r\n                      disabled={isSaving}\r\n                      className=\"px-4\"\r\n                    >\r\n                      {isSaving ? (\r\n                        <>\r\n                          <Loader2 className=\"mr-2 h-4 w-4 animate-spin\" />\r\n                          Đang lưu...\r\n                        </>\r\n                      ) : (\r\n                        'Lưu thay đổi'\r\n                      )}\r\n                    </Button>\r\n                  </div>\r\n                </div>\r\n              )\r\n            ))}\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;AA4uB8B;;AA1uB9B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAIA;AAAA;AACA;AACA;AACA;;;AAnBA;;;;;;;;;;;;;;;;AA+Ce,SAAS;;IACtB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,sQAAA,CAAA,WAAQ,AAAD,EAAmB,EAAE;IAC5D,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,sQAAA,CAAA,WAAQ,AAAD,EAA6C,CAAC;IACrF,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,sQAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,sQAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,sQAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,4EAA4E;IAC5E,MAAM,oBAAoB,CAAA,GAAA,sQAAA,CAAA,UAAO,AAAD;sDAAE;YAChC,MAAM,SAAS,SAAS,MAAM;qEAAC,CAAC,KAAK;oBACnC,MAAM,QAAQ,QAAQ,KAAK;oBAC3B,MAAM,UAAU,QAAQ,WAAW,IAAI;oBAEvC,IAAI,CAAC,GAAG,CAAC,MAAM,EAAE;wBACf,GAAG,CAAC,MAAM,GAAG,CAAC;oBAChB;oBACA,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,QAAQ,EAAE;wBACxB,GAAG,CAAC,MAAM,CAAC,QAAQ,GAAG,EAAE;oBAC1B;oBACA,GAAG,CAAC,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC;oBACzB,OAAO;gBACT;oEAAG,CAAC;YACJ,OAAO;QACT;qDAAG;QAAC;KAAS;IAEb,0CAA0C;IAC1C,MAAM,mBAAmB;QACvB;YAAE,IAAI;YAAmB,aAAa;YAAkB,aAAa;YAA8B,MAAM;YAAY,OAAO;QAAE;QAC9H;YAAE,IAAI;YAAe,aAAa;YAAc,aAAa;YAA4B,MAAM;YAAU,OAAO;QAAE;QAClH;YAAE,IAAI;YAAkB,aAAa;YAAe,aAAa;YAAwB,MAAM;YAAS,OAAO;QAAE;QACjH;YAAE,IAAI;YAAiB,aAAa;YAAW,aAAa;YAAkC,MAAM;YAAU,OAAO;QAAE;QACvH;YAAE,IAAI;YAAkB,aAAa;YAAmB,aAAa;YAAiC,MAAM;YAAU,OAAO;QAAE;QAC/H;YAAE,IAAI;YAAkB,aAAa;YAAU,aAAa;YAA2B,MAAM;YAAU,OAAO;QAAE;QAChH;YAAE,IAAI;YAAoB,aAAa;YAAa,aAAa;YAA+B,MAAM;YAAQ,OAAO;QAAE;QACvH;YAAE,IAAI;YAAoB,aAAa;YAAe,aAAa;YAAwB,MAAM;YAAW,OAAO;QAAE;QACrH;YAAE,IAAI;YAAoB,aAAa;YAAW,aAAa;YAA0B,MAAM;YAAa,OAAO;QAAE;QACrH;YAAE,IAAI;YAAmB,aAAa;YAAW,aAAa;YAA2C,MAAM;YAAQ,OAAO;QAAG;QACjI;YAAE,IAAI;YAAqB,aAAa;YAAa,aAAa;YAA6C,MAAM;YAAa,OAAO;QAAG;KAE7I;IAED,wBAAwB;IACxB,MAAM,SAAS,iBAAiB,GAAG,CAAC,CAAA,QAAS,MAAM,EAAE;IAErD,CAAA,GAAA,sQAAA,CAAA,YAAS,AAAD;qCAAE;YACR;QACF;oCAAG,EAAE;IAEL,oCAAoC;IACpC,CAAA,GAAA,sQAAA,CAAA,YAAS,AAAD;qCAAE;YACR,IAAI,cAAc,MAAM,OAAO,MAAM,GAAG,GAAG;gBACzC,aAAa,MAAM,CAAC,EAAE;YACxB;QACF;oCAAG;QAAC;QAAQ;KAAU;IAEtB,MAAM,gBAAgB;QACpB,aAAa;QACb,IAAI;YACF,wEAAwE;YAExE,8CAA8C;YAC9C,IAAI,cAAqB,EAAE;YAC3B,IAAI,OAAO;YACX,MAAM,QAAQ,KAAK,2BAA2B;YAC9C,IAAI,cAAc;YAElB,MAAO,YAAa;gBAElB,MAAM,eAAe,MAAM,6GAAA,CAAA,MAAG,CAAC,GAAG,CAAM,CAAC,qBAAqB,EAAE,MAAM,MAAM,EAAE,KAAK,gBAAgB,CAAC;gBAEpG,+BAA+B;gBAC/B,IAAI,WAAkB,EAAE;gBAGxB,wCAAwC;gBACxC,IAAI,aAAa,IAAI,IAAI,aAAa,IAAI,CAAC,IAAI,IAAI,aAAa,IAAI,CAAC,IAAI,CAAC,IAAI,IAAI,MAAM,OAAO,CAAC,aAAa,IAAI,CAAC,IAAI,CAAC,IAAI,GAAG;oBAC5H,WAAW,aAAa,IAAI,CAAC,IAAI,CAAC,IAAI;gBAExC,OAEK,IAAI,aAAa,IAAI,IAAI,aAAa,IAAI,CAAC,IAAI,IAAI,MAAM,OAAO,CAAC,aAAa,IAAI,CAAC,IAAI,GAAG;oBAC7F,WAAW,aAAa,IAAI,CAAC,IAAI;gBAEnC,OAEK,IAAI,aAAa,IAAI,IAAI,MAAM,OAAO,CAAC,aAAa,IAAI,GAAG;oBAC9D,WAAW,aAAa,IAAI;gBAE9B;gBAIA,mDAAmD;gBACnD,MAAM,cAAc,SAAS,MAAM,CAAC,CAAC,OACnC,KAAK,SAAS,IAAI,KAAK,SAAS,CAAC,UAAU,CAAC;gBAG9C,gCAAgC;gBAChC,cAAc;uBAAI;uBAAgB;iBAAY;gBAE9C,iCAAiC;gBACjC,IAAI,SAAS,MAAM,GAAG,OAAO;oBAC3B,cAAc;gBAChB,OAAO;oBACL;gBACF;YACF;YAIA,2CAA2C;YAC3C,IAAI,YAAY,MAAM,KAAK,GAAG;gBAC5B,QAAQ,KAAK,CAAC;gBACd,2QAAA,CAAA,QAAK,CAAC,KAAK,CAAC;gBACZ,aAAa;gBACb;YACF;YAEA,MAAM,WAAW;gBAAE,MAAM;oBAAE,MAAM;gBAAY;YAAE;YAG/C,6BAA6B;YAC7B,uCAAe;;YAKf;YAEA,kEAAkE;YAClE,IAAI,UAAU,EAAE;YAChB,IAAI,SAAS,IAAI,IAAI,SAAS,IAAI,CAAC,IAAI,IAAI,MAAM,OAAO,CAAC,SAAS,IAAI,CAAC,IAAI,GAAG;gBAC5E,UAAU,SAAS,IAAI,CAAC,IAAI;YAC9B,OAAO,IAAI,SAAS,IAAI,IAAI,MAAM,OAAO,CAAC,SAAS,IAAI,GAAG;gBACxD,UAAU,SAAS,IAAI;YACzB,OAAO,IAAI,MAAM,OAAO,CAAC,WAAW;gBAClC,UAAU;YACZ,OAAO;gBACL,QAAQ,KAAK,CAAC,0CAA0C;gBACxD,2QAAA,CAAA,QAAK,CAAC,KAAK,CAAC;gBACZ,aAAa;gBACb;YACF;YAKA,0CAA0C;YAC1C,MAAM,cAAc,QAAQ,GAAG,CAAC,CAAC,OAAc,CAAC;oBAC9C,IAAI,KAAK,EAAE,IAAI;oBACf,KAAK,KAAK,SAAS;oBACnB,OAAO,KAAK,WAAW,IAAI;oBAC3B,aAAa,KAAK,WAAW,IAAI;oBACjC,OAAO,KAAK,WAAW,IAAI;oBAC3B,MAAM,KAAK,UAAU,IAAI;oBACzB,SAAS,KAAK,aAAa,GACxB,OAAO,KAAK,aAAa,KAAK,WAAW,KAAK,KAAK,CAAC,KAAK,aAAa,IAAI,KAAK,aAAa,GAC3F;oBACJ,kBAAkB,KAAK,gBAAgB;oBACvC,kBAAkB,KAAK,gBAAgB;oBACvC,WAAW,KAAK,SAAS;oBACzB,YAAY,KAAK,UAAU;oBAC3B,aAAa,KAAK,WAAW;oBAC7B,oBAAoB,KAAK,kBAAkB;oBAC3C,oBAAoB,KAAK,kBAAkB;oBAC3C,cAAc,KAAK,YAAY;oBAC/B,cAAc,KAAK,YAAY;oBAC/B,eAAe,KAAK,aAAa;gBACnC,CAAC;YAED,iDAAiD;YACjD,MAAM,mBAAmB,YAAY,MAAM,CAAC,CAAC,UAA2B,CAAC,QAAQ,EAAE;YACnF,IAAI,iBAAiB,MAAM,GAAG,GAAG,CAEjC;YAMA,YAAY;YAEZ,uBAAuB;YACvB,MAAM,kBAA6D,CAAC;YACpE,YAAY,OAAO,CAAC,CAAC;gBACnB,IAAI,QAAQ,IAAI,KAAK,WAAW;oBAC9B,eAAe,CAAC,QAAQ,GAAG,CAAC,GAAG,QAAQ,KAAK,KAAK;gBACnD,OAAO,IAAI,QAAQ,IAAI,KAAK,UAAU;oBACpC,eAAe,CAAC,QAAQ,GAAG,CAAC,GAAG,WAAW,QAAQ,KAAK,KAAK;gBAC9D,OAAO;oBACL,eAAe,CAAC,QAAQ,GAAG,CAAC,GAAG,QAAQ,KAAK;gBAC9C;YACF;YACA,YAAY;QACd,EAAE,OAAO,OAAY;YACnB,QAAQ,KAAK,CAAC,4BAA4B;YAC1C,IAAI,MAAM,QAAQ,EAAE;gBAClB,QAAQ,KAAK,CAAC,mBAAmB,MAAM,QAAQ,CAAC,IAAI;gBACpD,2QAAA,CAAA,QAAK,CAAC,KAAK,CAAC,CAAC,wBAAwB,EAAE,MAAM,QAAQ,CAAC,IAAI,CAAC,OAAO,IAAI,sBAAsB;YAC9F,OAAO;gBACL,2QAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACd;QACF,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,oBAAoB,CAAC,KAAa;QACtC,YAAY,CAAC,OAAS,CAAC;gBAAE,GAAG,IAAI;gBAAE,CAAC,IAAI,EAAE;YAAM,CAAC;IAClD;IAEA,MAAM,qBAAqB,OAAO;QAChC,YAAY;QACZ,IAAI;YAGF,uDAAuD;YACvD,MAAM,YAAY,iBAAiB,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;YACtD,MAAM,cAAc,WAAW,eAAe,CAAC,SAAS,EAAE,MAAM,MAAM,CAAC,GAAG,WAAW,KAAK,MAAM,KAAK,CAAC,IAAI;YAC1G,MAAM,cAAc,WAAW,eAAe,CAAC,SAAS,EAAE,MAAM,MAAM,CAAC,GAAG,WAAW,KAAK,MAAM,KAAK,CAAC,IAAI;YAC1G,MAAM,OAAO,WAAW,QAAQ;YAChC,MAAM,QAAQ,WAAW,SAAS;YAElC,6DAA6D;YAC7D,MAAM,kBAAkB,SAAS,GAAG,CAAC,CAAA;gBACnC,IAAI,QAAQ,KAAK,KAAK,OAAO;oBAC3B,OAAO;wBACL,GAAG,OAAO;wBACV,kBAAkB;wBAClB,kBAAkB;wBAClB,WAAW;wBACX,YAAY;oBACd;gBACF;gBACA,OAAO;YACT;YAEA,sCAAsC;YACtC,MAAM,cAAc,gBAAgB,IAAI,CAAC,CAAA,UACvC,QAAQ,KAAK,KAAK,SAAS,QAAQ,aAAa,KAAK;YAGvD,wDAAwD;YACxD,IAAI,CAAC,aAAa;gBAGhB,wBAAwB;gBACxB,MAAM,iBAAgC;oBACpC,IAAI;oBACJ,KAAK,CAAC,MAAM,EAAE,MAAM,WAAW,IAAI;oBACnC,OAAO;oBACP,aAAa,CAAC,wBAAwB,EAAE,OAAO;oBAC/C,OAAO;oBACP,MAAM;oBACN,kBAAkB;oBAClB,kBAAkB;oBAClB,WAAW;oBACX,YAAY;oBACZ,eAAe;gBACjB;gBAEA,gDAAgD;gBAChD,gBAAgB,IAAI,CAAC;YACvB,OAAO;gBACL,wDAAwD;gBACxD,MAAM,qBAAqB;oBACzB,GAAG,WAAW;oBACd,kBAAkB;oBAClB,kBAAkB;oBAClB,WAAW;oBACX,YAAY;gBACd;gBAEA,yCAAyC;gBACzC,MAAM,QAAQ,gBAAgB,SAAS,CAAC,CAAA,UACtC,QAAQ,KAAK,KAAK,SAAS,QAAQ,aAAa,KAAK;gBAGvD,IAAI,UAAU,CAAC,GAAG;oBAChB,eAAe,CAAC,MAAM,GAAG;gBAC3B;YACF;YAEA,wDAAwD;YACxD,MAAM,UAAU,MAAM,CAAA,GAAA,8KAAA,CAAA,wBAAqB,AAAD,EAAE,OAAO,UAAU;YAE7D,IAAI,SAAS;gBAEX,2QAAA,CAAA,QAAK,CAAC,OAAO,CAAC;gBAEd,yDAAyD;gBACzD,YAAY;YACd;QACF,EAAE,OAAO,OAAY;YACnB,QAAQ,KAAK,CAAC,8BAA8B;YAC5C,IAAI,MAAM,QAAQ,EAAE;gBAClB,QAAQ,KAAK,CAAC,iBAAiB,MAAM,QAAQ,CAAC,IAAI;YACpD;YACA,2QAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd,SAAU;YACR,YAAY;QACd;IACF;IAEA,MAAM,qBAAqB,CAAC;QAC1B,MAAM,QAAQ,QAAQ,CAAC,QAAQ,GAAG,CAAC;QAEnC,yBAAyB;QACzB,OAAQ,QAAQ,IAAI;YAClB,KAAK;gBACH,qBACE,sSAAC,6HAAA,CAAA,QAAK;oBACJ,IAAI,QAAQ,GAAG;oBACf,OAAO;oBACP,UAAU,CAAC,IAAM,kBAAkB,QAAQ,GAAG,EAAE,EAAE,MAAM,CAAC,KAAK;oBAC9D,WAAU;;;;;;YAGhB,KAAK;gBACH,qBACE,sSAAC,gIAAA,CAAA,WAAQ;oBACP,IAAI,QAAQ,GAAG;oBACf,OAAO;oBACP,UAAU,CAAC,IAAM,kBAAkB,QAAQ,GAAG,EAAE,EAAE,MAAM,CAAC,KAAK;oBAC9D,MAAM;oBACN,WAAU;;;;;;YAGhB,KAAK;gBACH,qBACE,sSAAC,6HAAA,CAAA,QAAK;oBACJ,IAAI,QAAQ,GAAG;oBACf,MAAK;oBACL,OAAO;oBACP,UAAU,CAAC,IAAM,kBAAkB,QAAQ,GAAG,EAAE,WAAW,EAAE,MAAM,CAAC,KAAK;oBACzE,WAAU;;;;;;YAGhB,KAAK;gBACH,qBACE,sSAAC;oBAAI,WAAU;;sCACb,sSAAC,8HAAA,CAAA,SAAM;4BACL,IAAI,QAAQ,GAAG;4BACf,SAAS;4BACT,iBAAiB,CAAC,UAAY,kBAAkB,QAAQ,GAAG,EAAE;;;;;;sCAE/D,sSAAC,6HAAA,CAAA,QAAK;4BAAC,SAAS,QAAQ,GAAG;4BAAE,WAAU;sCACpC,AAAC,QAAoB,QAAQ;;;;;;;;;;;;YAItC,KAAK;gBACH,qBACE,sSAAC,8HAAA,CAAA,SAAM;oBACL,OAAO;oBACP,eAAe,CAAC,WAAa,kBAAkB,QAAQ,GAAG,EAAE;;sCAE5D,sSAAC,8HAAA,CAAA,gBAAa;4BAAC,WAAU;sCACvB,cAAA,sSAAC,8HAAA,CAAA,cAAW;gCAAC,aAAY;;;;;;;;;;;sCAE3B,sSAAC,8HAAA,CAAA,gBAAa;sCACX,QAAQ,OAAO,EAAE,IAAI,CAAC,uBACrB,sSAAC,8HAAA,CAAA,aAAU;oCAAc,OAAO;8CAC7B;mCADc;;;;;;;;;;;;;;;;YAO3B,KAAK;gBACH,qBACE,sSAAC;oBAAI,WAAU;;sCACb,sSAAC,uIAAA,CAAA,cAAW;4BACV,OAAO;4BACP,UAAU,CAAC,WAAa,kBAAkB,QAAQ,GAAG,EAAE;4BACvD,UAAS,uCAAuC,2BAA2B;;4BAC3E,SAAS,IAAI,OAAO;4BACpB,cAAc;gCAAC;gCAAc;gCAAa;gCAAa;6BAAa;4BACpE,aAAa,CAAC,qBAAqB,EAAE,QAAQ,WAAW,IAAI,QAAQ,GAAG,EAAE;4BACzE,WAAU;;;;;;sCAEZ,sSAAC;4BAAE,WAAU;sCAAgC;;;;;;;;;;;;YAKnD,KAAK;gBACH,qBACE,sSAAC;oBAAI,WAAU;;sCACb,sSAAC,sIAAA,CAAA,aAAU;4BACT,OAAO;4BACP,UAAU,CAAC,WAAa,kBAAkB,QAAQ,GAAG,EAAE;4BACvD,aAAa,CAAC,cAAc,EAAE,QAAQ,WAAW,IAAI,QAAQ,GAAG,EAAE;4BAClE,WAAU;;;;;;sCAEZ,sSAAC;4BAAE,WAAU;sCAAgC;;;;;;;;;;;;YAKnD;gBACE,qBACE,sSAAC,6HAAA,CAAA,QAAK;oBACJ,IAAI,QAAQ,GAAG;oBACf,OAAO;oBACP,UAAU,CAAC,IAAM,kBAAkB,QAAQ,GAAG,EAAE,EAAE,MAAM,CAAC,KAAK;oBAC9D,WAAU;;;;;;QAGlB;IACF;IAMA,+DAA+D;IAC/D,MAAM,mBAAoD;QACxD,0BAAY,sSAAC,uSAAA,CAAA,YAAS;YAAC,WAAU;;;;;;QACjC,sBAAQ,sSAAC,yRAAA,CAAA,OAAI;YAAC,WAAU;;;;;;QACxB,wBAAU,sSAAC,4SAAA,CAAA,SAAM;YAAC,WAAU;;;;;;QAC5B,sBAAQ,sSAAC,0RAAA,CAAA,OAAI;YAAC,WAAU;;;;;;QACxB,2BAAa,sSAAC,mSAAA,CAAA,YAAS;YAAC,WAAU;;;;;;QAClC,qBAAO,sSAAC,uRAAA,CAAA,MAAG;YAAC,WAAU;;;;;;QACtB,wBAAU,sSAAC,6RAAA,CAAA,SAAM;YAAC,WAAU;;;;;;QAC5B,uBAAS,sSAAC,iSAAA,CAAA,SAAM;YAAC,WAAU;;;;;;QAC3B,2BAAa,sSAAC,oUAAA,CAAA,WAAQ;YAAC,WAAU;;;;;;QACjC,yBAAW,sSAAC,+RAAA,CAAA,UAAO;YAAC,WAAU;;;;;;QAC9B,wBAAU,sSAAC,6RAAA,CAAA,SAAM;YAAC,WAAU;;;;;;IAC9B;IAEA,gFAAgF;IAEhF,oDAAoD;IACpD,MAAM,qBAAqB,CAAA,GAAA,sQAAA,CAAA,cAAW,AAAD;2DAAE,OAAO;YAC5C,IAAI;gBAEF,MAAM,WAAW,MAAM,6GAAA,CAAA,MAAG,CAAC,GAAG,CAAM,CAAC,qBAAqB,EAAE,MAAM,UAAU,CAAC;gBAE7E,+BAA+B;gBAC/B,IAAI,YAAmB,EAAE;gBAEzB,wCAAwC;gBACxC,IAAI,SAAS,IAAI,IAAI,SAAS,IAAI,CAAC,IAAI,IAAI,SAAS,IAAI,CAAC,IAAI,CAAC,IAAI,IAAI,MAAM,OAAO,CAAC,SAAS,IAAI,CAAC,IAAI,CAAC,IAAI,GAAG;oBAC5G,YAAY,SAAS,IAAI,CAAC,IAAI,CAAC,IAAI;gBACrC,OAEK,IAAI,SAAS,IAAI,IAAI,SAAS,IAAI,CAAC,IAAI,IAAI,MAAM,OAAO,CAAC,SAAS,IAAI,CAAC,IAAI,GAAG;oBACjF,YAAY,SAAS,IAAI,CAAC,IAAI;gBAChC,OAEK,IAAI,SAAS,IAAI,IAAI,MAAM,OAAO,CAAC,SAAS,IAAI,GAAG;oBACtD,YAAY,SAAS,IAAI;gBAC3B;gBAIA,IAAI,UAAU,MAAM,GAAG,GAAG;oBACxB,qBAAqB;oBACrB,MAAM,cAAc,UAAU,GAAG;uFAAC,CAAC,OAAc,CAAC;gCAChD,IAAI,KAAK,EAAE,IAAI;gCACf,KAAK,KAAK,SAAS;gCACnB,OAAO,KAAK,WAAW,IAAI;gCAC3B,aAAa,KAAK,WAAW,IAAI;gCACjC,OAAO,KAAK,WAAW,IAAI;gCAC3B,aAAa,KAAK,WAAW;gCAC7B,MAAM,KAAK,UAAU,IAAI;gCACzB,SAAS,KAAK,aAAa,GACxB,OAAO,KAAK,aAAa,KAAK,WAAW,KAAK,KAAK,CAAC,KAAK,aAAa,IAAI,KAAK,aAAa,GAC3F;gCACJ,kBAAkB,KAAK,gBAAgB;gCACvC,kBAAkB,KAAK,gBAAgB;gCACvC,WAAW,KAAK,SAAS;gCACzB,YAAY,KAAK,UAAU;gCAC3B,oBAAoB,KAAK,kBAAkB;gCAC3C,oBAAoB,KAAK,kBAAkB;gCAC3C,cAAc,KAAK,YAAY;gCAC/B,cAAc,KAAK,YAAY;gCAC/B,eAAe,KAAK,aAAa;4BACnC,CAAC;;oBAED,iCAAiC;oBACjC;2EAAY,CAAA;4BACV,2CAA2C;4BAC3C,MAAM,gBAAgB,aAAa,MAAM;iGAAC,CAAA,UAAW,QAAQ,KAAK,KAAK;;4BACvE,2BAA2B;4BAC3B,OAAO;mCAAI;mCAAkB;6BAAY;wBAC3C;;oBAEA,oBAAoB;oBACpB,MAAM,cAAyD;wBAAE,GAAG,QAAQ;oBAAC;oBAC7E,YAAY,OAAO;2EAAC,CAAC;4BACnB,IAAI,QAAQ,IAAI,KAAK,WAAW;gCAC9B,WAAW,CAAC,QAAQ,GAAG,CAAC,GAAG,QAAQ,KAAK,KAAK;4BAC/C,OAAO,IAAI,QAAQ,IAAI,KAAK,UAAU;gCACpC,WAAW,CAAC,QAAQ,GAAG,CAAC,GAAG,WAAW,QAAQ,KAAK,KAAK;4BAC1D,OAAO,IAAI,QAAQ,IAAI,KAAK,SAAS;gCACnC,iCAAiC;gCACjC,WAAW,CAAC,QAAQ,GAAG,CAAC,GAAG,QAAQ,KAAK,IAAI;4BAC9C,OAAO,IAAI,QAAQ,IAAI,KAAK,QAAQ;gCAClC,kCAAkC;gCAClC,WAAW,CAAC,QAAQ,GAAG,CAAC,GAAG,QAAQ,KAAK,IAAI;4BAC9C,OAAO;gCACL,WAAW,CAAC,QAAQ,GAAG,CAAC,GAAG,QAAQ,KAAK;4BAC1C;wBACF;;oBACA,YAAY;gBAGd;YACF,EAAE,OAAO,OAAY;gBACnB,QAAQ,KAAK,CAAC,CAAC,6BAA6B,EAAE,MAAM,CAAC,CAAC,EAAE;gBACxD,IAAI,MAAM,QAAQ,EAAE;oBAClB,QAAQ,KAAK,CAAC,mBAAmB,MAAM,QAAQ,CAAC,IAAI;oBACpD,2QAAA,CAAA,QAAK,CAAC,KAAK,CAAC,CAAC,4BAA4B,EAAE,MAAM,EAAE,EAAE,MAAM,QAAQ,CAAC,IAAI,CAAC,OAAO,IAAI,sBAAsB;gBAC5G,OAAO;oBACL,2QAAA,CAAA,QAAK,CAAC,KAAK,CAAC,CAAC,4BAA4B,EAAE,OAAO;gBACpD;YACF;QACF;0DAAG,EAAE,GAAG,qDAAqD;IAE7D,oDAAoD;IACpD,CAAA,GAAA,sQAAA,CAAA,YAAS,AAAD;qCAAE;YACR,IAAI,aAAa,CAAC,WAAW;gBAC3B,QAAQ,GAAG,CAAC,mCAAmC;gBAC/C,mBAAmB;YACrB;QACF;oCAAG;QAAC;KAAU,GAAG,iDAAiD;IAElE,sFAAsF;IAEtF,oCAAoC;IACpC,MAAM,eAAe,CAAC;QACpB,MAAM,YAAY,iBAAiB,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;QACtD,MAAM,WAAW,WAAW,QAAQ;QACpC,IAAI,YAAY,gBAAgB,CAAC,SAAS,EAAE;YAC1C,OAAO,gBAAgB,CAAC,SAAS;QACnC;QACA,qBAAO,sSAAC,uSAAA,CAAA,YAAS;YAAC,WAAU;;;;;;IAC9B;IAEA,8CAA8C;IAC9C,MAAM,wBAAwB,CAAC,OAAe,aAAqB;QACjE,+DAA+D;QAC/D,MAAM,yBAAyB,SAAS,IAAI,CAAC,CAAA,IAC3C,EAAE,KAAK,KAAK,SACZ,CAAC,EAAE,WAAW,IAAI,SAAS,MAAM,eACjC,EAAE,kBAAkB;QAGtB,IAAI,wBAAwB,oBAAoB;YAC9C,OAAO,uBAAuB,kBAAkB;QAClD;QAEA,oCAAoC;QACpC,IAAI,gBAAgB,WAAW;YAC7B,OAAO;QACT;QACA,OAAO,YAAY,MAAM,CAAC,GAAG,WAAW,KAAK,YAAY,KAAK,CAAC,GAAG,OAAO,CAAC,MAAM;IAClF;IAEA,6CAA6C;IAC7C,MAAM,wBAAwB,CAAC,OAAe,aAAqB;QACjE,+DAA+D;QAC/D,MAAM,yBAAyB,SAAS,IAAI,CAAC,CAAA,IAC3C,EAAE,KAAK,KAAK,SACZ,CAAC,EAAE,WAAW,IAAI,SAAS,MAAM,eACjC,EAAE,kBAAkB;QAGtB,OAAO,wBAAwB,sBAAsB;IACvD;IAEA,uCAAuC;IACvC,MAAM,kBAAkB,CAAC,OAAe,aAAqB;QAC3D,yDAAyD;QACzD,MAAM,mBAAmB,SAAS,IAAI,CAAC,CAAA,IACrC,EAAE,KAAK,KAAK,SACZ,CAAC,EAAE,WAAW,IAAI,SAAS,MAAM,eACjC,EAAE,YAAY,KAAK;QAGrB,OAAO,kBAAkB,gBAAgB;IAC3C;IAEA,4CAA4C;IAC5C,MAAM,oBAAoB,CAAC;QACzB,IAAI,CAAC,iBAAiB,CAAC,MAAM,EAAE,OAAO,EAAE;QAExC,MAAM,WAAW,OAAO,IAAI,CAAC,iBAAiB,CAAC,MAAM;QACrD,OAAO,SAAS,IAAI,CAAC,CAAC,GAAG;YACvB,sCAAsC;YACtC,MAAM,SAAS,gBAAgB,OAAO,GAAG,WAAW,iCAAiC;YACrF,MAAM,SAAS,gBAAgB,OAAO,GAAG,WAAW,iCAAiC;YAErF,gEAAgE;YAChE,IAAI,WAAW,QAAQ;gBACrB,IAAI,MAAM,WAAW,OAAO,CAAC;gBAC7B,IAAI,MAAM,WAAW,OAAO;gBAC5B,OAAO,EAAE,aAAa,CAAC;YACzB;YAEA,OAAO,SAAS;QAClB;IACF;IAEA,4CAA4C;IAC5C,MAAM,sBAAsB,CAAC;QAC3B,MAAM,YAAY,iBAAiB,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;QACtD,OAAO,WAAW,eAAe,CAAC,SAAS,EAAE,OAAO;IACtD;IAEA,2CAA2C;IAC3C,MAAM,sBAAsB,CAAC;QAC3B,MAAM,YAAY,iBAAiB,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;QACtD,OAAO,WAAW,eAAe,CAAC,SAAS,EAAE,OAAO;IACtD;IAEA,0CAA0C;IAC1C,MAAM,kBAAkB;QACtB,2CAA2C;QAC3C,OAAO;eAAI;SAAO,CAAC,IAAI,CAAC,CAAC,GAAG;YAC1B,MAAM,SAAS,iBAAiB,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;YACnD,MAAM,SAAS,iBAAiB,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;YACnD,MAAM,SAAS,QAAQ,SAAS;YAChC,MAAM,SAAS,QAAQ,SAAS;YAChC,OAAO,SAAS;QAClB;IACF;IAIA,IAAI,WAAW;QACb,qBACE,sSAAC;YAAI,WAAU;sBACb,cAAA,sSAAC,wSAAA,CAAA,UAAO;gBAAC,WAAU;;;;;;;;;;;IAGzB;IAIA,qBACE,sSAAC;QAAI,WAAU;kBACb,cAAA,sSAAC;YAAI,WAAU;;8BAEb,sSAAC;oBAAI,WAAU;8BACZ,kBAAkB,GAAG,CAAC,CAAC,sBACtB,sSAAC;4BAEC,SAAS,IAAM,aAAa;4BAC5B,WAAW,CAAA,GAAA,+HAAA,CAAA,KAAE,AAAD,EACV,qEACA,cAAc,QACV,2CACA;;gCAGL,aAAa;8CACd,sSAAC;oCAAK,WAAU;8CACb,oBAAoB,OAAO,OAAO,CAAC,aAAa;;;;;;;2BAX9C;;;;;;;;;;8BAkBX,sSAAC;oBAAI,WAAU;8BACb,cAAA,sSAAC;wBAAI,WAAU;kCACZ,OAAO,GAAG,CAAC,CAAC,QACX,cAAc,uBACZ,sSAAC;;kDACC,sSAAC;wCAAI,WAAU;;0DACb,sSAAC;gDAAG,WAAU;0DAAuB,oBAAoB;;;;;;0DACzD,sSAAC;gDAAE,WAAU;0DAAsC,oBAAoB;;;;;;;;;;;;kDAEzE,sSAAC,iIAAA,CAAA,YAAS;;;;;kDACV,sSAAC;wCAAI,WAAU;kDACb,cAAA,sSAAC;4CAAI,WAAU;sDACZ,iBAAiB,CAAC,MAAM,GACvB,yBAAyB;4CACzB,kBAAkB,OAAO,GAAG,CAAC,CAAC,aAAa;gDACzC,MAAM,kBAAkB,iBAAiB,CAAC,MAAM,CAAC,YAAY,CAC1D,MAAM,CAAC,CAAA,UAAW,CAAC,QAAQ,aAAa,EAAE,kDAAkD;iDAC5F,IAAI,CAAC,CAAC,GAAG;oDACR,iDAAiD;oDACjD,MAAM,SAAS,EAAE,YAAY,IAAI;oDACjC,MAAM,SAAS,EAAE,YAAY,IAAI;oDACjC,IAAI,WAAW,QAAQ;wDACrB,OAAO,SAAS;oDAClB;oDACA,OAAO,EAAE,GAAG,CAAC,aAAa,CAAC,EAAE,GAAG;gDAClC;gDAEF,IAAI,gBAAgB,MAAM,KAAK,GAAG,OAAO;gDAEzC,MAAM,qBAAqB,sBAAsB,OAAO,aAAa,WAAW,2BAA2B;gDAC3G,MAAM,qBAAqB,sBAAsB,OAAO,aAAa,WAAW,2BAA2B;gDAC3G,MAAM,oBAAoB,gBAAgB,aAAa,kBAAkB,OAAO,MAAM,GAAG;gDAEzF,qDAAqD;gDACrD,wCAA4C;oDAC1C,QAAQ,GAAG,CAAC,CAAC,yBAAyB,EAAE,MAAM,WAAW,EAAE,aAAa,EAAE;wDACxE,aAAa;wDACb,aAAa;wDACb,eAAe,gBAAgB,MAAM;wDACrC,YAAY;oDACd;gDACF;gDAEA,qBACE,sSAAC;oDAAsB,WAAU;;wDAE9B,mCACC,sSAAC;4DAAI,WAAU;;8EACb,sSAAC;oEAAG,WAAU;8EACX;;;;;;gEAEF,oCACC,sSAAC;oEAAE,WAAU;8EACV;;;;;;8EAGL,sSAAC,iIAAA,CAAA,YAAS;;;;;;;;;;;sEAKd,sSAAC;4DAAI,WAAU;sEACZ,gBAAgB,GAAG,CAAC,CAAC,SAAS,6BAC7B,sSAAC;oEAAqB,WAAU;;sFAC9B,sSAAC;4EAAI,WAAU;;8FACb,sSAAC;oFAAI,WAAU;8FACb,cAAA,sSAAC,6HAAA,CAAA,QAAK;wFAAC,SAAS,QAAQ,GAAG;wFAAE,WAAU;kGACpC,QAAQ,WAAW,IAAI,QAAQ,GAAG;;;;;;;;;;;8FAIvC,sSAAC;oFAAI,WAAU;8FACZ,mBAAmB;;;;;;;;;;;;wEAGvB,eAAe,gBAAgB,MAAM,GAAG,mBACvC,sSAAC,iIAAA,CAAA,YAAS;4EAAC,WAAU;;;;;;;mEAbf,QAAQ,EAAE;;;;;;;;;;wDAoBvB,eAAe,kBAAkB,OAAO,MAAM,GAAG,mBAChD,sSAAC;4DAAI,WAAU;sEACb,cAAA,sSAAC,iIAAA,CAAA,YAAS;gEAAC,WAAU;;;;;;;;;;;;mDAzCjB;;;;;4CA8Cd,KAEA,0CAA0C;0DAC1C,sSAAC;gDAAI,WAAU;;kEACb,sSAAC;wDAAE,WAAU;kEAAwB;;;;;;kEACrC,sSAAC;wDAAE,WAAU;kEAAqC;;;;;;;;;;;;;;;;;;;;;;kDAO1D,sSAAC;wCAAI,WAAU;kDACb,cAAA,sSAAC,8HAAA,CAAA,SAAM;4CACL,SAAS,IAAM,mBAAmB;4CAClC,UAAU;4CACV,WAAU;sDAET,yBACC;;kEACE,sSAAC,wSAAA,CAAA,UAAO;wDAAC,WAAU;;;;;;oDAA8B;;+DAInD;;;;;;;;;;;;+BA9GE;;;;;;;;;;;;;;;;;;;;;;;;;;AA0H1B;GAzxBwB;KAAA", "debugId": null}}]}
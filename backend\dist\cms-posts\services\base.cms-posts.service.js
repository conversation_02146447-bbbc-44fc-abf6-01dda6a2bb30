"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var BaseCmsPostsService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.BaseCmsPostsService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const event_emitter_1 = require("@nestjs/event-emitter");
const class_transformer_1 = require("class-transformer");
const cms_posts_entity_1 = require("../entity/cms-posts.entity");
const cms_post_dto_1 = require("../dto/cms-post.dto");
let BaseCmsPostsService = BaseCmsPostsService_1 = class BaseCmsPostsService {
    postRepository;
    dataSource;
    eventEmitter;
    logger = new common_1.Logger(BaseCmsPostsService_1.name);
    EVENT_POST_CREATED = 'cms-post.created';
    EVENT_POST_UPDATED = 'cms-post.updated';
    EVENT_POST_DELETED = 'cms-post.deleted';
    EVENT_POST_PUBLISHED = 'cms-post.published';
    EVENT_POST_VIEWED = 'cms-post.viewed';
    validRelations = [
        'category',
        'author',
        'creator',
        'updater',
        'deleter'
    ];
    constructor(postRepository, dataSource, eventEmitter) {
        this.postRepository = postRepository;
        this.dataSource = dataSource;
        this.eventEmitter = eventEmitter;
    }
    validateRelations(relations) {
        if (!relations || !Array.isArray(relations)) {
            return [];
        }
        return relations.filter(relation => {
            const isValid = this.validRelations.includes(relation);
            if (!isValid) {
                this.logger.warn(`Mối quan hệ không hợp lệ: ${relation}`);
            }
            return isValid;
        });
    }
    buildWhereClause(filter) {
        if (!filter) {
            return { isDeleted: false };
        }
        try {
            const filterObj = JSON.parse(filter);
            return {
                ...filterObj,
                isDeleted: false,
            };
        }
        catch (e) {
            return [
                { title: (0, typeorm_2.ILike)(`%${filter}%`), isDeleted: false },
                { excerpt: (0, typeorm_2.ILike)(`%${filter}%`), isDeleted: false },
                { content: (0, typeorm_2.ILike)(`%${filter}%`), isDeleted: false },
                { slug: (0, typeorm_2.ILike)(`%${filter}%`), isDeleted: false },
            ];
        }
    }
    async findById(id, relations = [], throwError = true) {
        const validatedRelations = this.validateRelations(relations);
        const post = await this.postRepository.findOne({
            where: { id, isDeleted: false },
            relations: validatedRelations,
        });
        if (!post && throwError) {
            throw new common_1.NotFoundException(`Không tìm thấy bài viết với ID: ${id}`);
        }
        return post;
    }
    async findBySlug(slug, postType, throwError = true) {
        const whereCondition = { slug, isDeleted: false };
        if (postType) {
            whereCondition.postType = postType;
        }
        const post = await this.postRepository.findOne({
            where: whereCondition,
        });
        if (!post && throwError) {
            throw new common_1.NotFoundException(`Không tìm thấy bài viết với slug: ${slug}`);
        }
        return post;
    }
    toDto(post) {
        if (!post) {
            return null;
        }
        const plainObj = Object.assign({}, post);
        if (post.category) {
            plainObj.categoryId = post.category.id;
        }
        if (post.author) {
            plainObj.authorId = post.author.id;
        }
        return (0, class_transformer_1.plainToInstance)(cms_post_dto_1.CmsPostDto, plainObj, {
            excludeExtraneousValues: true,
            enableImplicitConversion: true,
            exposeDefaultValues: true,
            exposeUnsetFields: false,
        });
    }
    toDtos(posts) {
        if (!posts || !Array.isArray(posts)) {
            return [];
        }
        return posts.map(post => this.toDto(post))
            .filter((dto) => dto !== null);
    }
    async incrementViewCount(id) {
        try {
            const result = await this.postRepository.increment({ id, isDeleted: false }, 'viewCount', 1);
            if (result.affected === 0) {
                throw new common_1.NotFoundException(`Không tìm thấy bài viết với ID: ${id}`);
            }
            const post = await this.postRepository.findOne({
                where: { id },
                select: ['viewCount'],
            });
            const newViewCount = post?.viewCount || 0;
            this.eventEmitter.emit(this.EVENT_POST_VIEWED, {
                postId: id,
                viewCount: newViewCount,
            });
            return newViewCount;
        }
        catch (error) {
            this.logger.error(`Lỗi khi tăng số lượt xem bài viết: ${error.message}`, error.stack);
            throw error;
        }
    }
    canPublish(post) {
        if (!post.title || !post.content) {
            return false;
        }
        return true;
    }
    generateSlugFromTitle(title) {
        return title
            .toLowerCase()
            .normalize('NFD')
            .replace(/[\u0300-\u036f]/g, '')
            .replace(/[đĐ]/g, 'd')
            .replace(/[^a-z0-9\s-]/g, '')
            .replace(/\s+/g, '-')
            .replace(/-+/g, '-')
            .replace(/^-|-$/g, '');
    }
    async isSlugUnique(slug, postType, excludeId) {
        const whereCondition = { slug, postType, isDeleted: false };
        if (excludeId) {
            whereCondition.id = { $ne: excludeId };
        }
        const existingPost = await this.postRepository.findOne({
            where: whereCondition,
        });
        return !existingPost;
    }
};
exports.BaseCmsPostsService = BaseCmsPostsService;
exports.BaseCmsPostsService = BaseCmsPostsService = BaseCmsPostsService_1 = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(cms_posts_entity_1.CmsPosts)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        typeorm_2.DataSource,
        event_emitter_1.EventEmitter2])
], BaseCmsPostsService);
//# sourceMappingURL=base.cms-posts.service.js.map
'use client';

import React from 'react';
import { cn } from '@/lib/utils';
import { EcomProduct } from './type/ecom-product';
import { EcomProductCard } from './components/ecom-product-card';
import { Skeleton } from '@/components/ui/skeleton';
import { Package, ShoppingCart } from 'lucide-react';

/**
 * Props interface cho EcomProductsGrid component
 */
interface EcomProductsGridProps {
  products: EcomProduct[];
  onAddToCart: (product: EcomProduct, quantity?: number) => void;
  onProductClick?: (product: EcomProduct) => void;
  loading?: boolean;
  className?: string;
}

/**
 * Skeleton component cho loading state - sao chép design từ product-list-item.tsx
 */
const ProductCardSkeleton = () => (
  <div className="relative flex w-full max-w-full flex-none scroll-ml-6 flex-col gap-3 rounded-lg bg-white p-4 shadow-md h-full">
    {/* Image skeleton - aspect-square như design gốc */}
    <Skeleton className="aspect-square w-full rounded-md" />

    {/* Content skeleton - match với design gốc với spacer */}
    <div className="flex flex-col gap-3 px-1 h-full">
      {/* Name skeleton - 2 dòng */}
      <div className="flex items-center justify-center text-center">
        <div className="space-y-1 w-full">
          <Skeleton className="h-4 w-3/4 mx-auto" />
          <Skeleton className="h-4 w-1/2 mx-auto" />
        </div>
      </div>

      {/* Description skeleton - 2 dòng */}
      <div className="flex items-center justify-center text-center">
        <div className="space-y-1 w-full">
          <Skeleton className="h-3 w-2/3 mx-auto" />
          <Skeleton className="h-3 w-1/3 mx-auto" />
        </div>
      </div>

      {/* Spacer */}
      <div className="flex-1" />

      {/* Button skeleton - cố định ở cuối */}
      <div className="flex gap-2 mt-auto">
        <Skeleton className="h-10 w-full" />
      </div>
    </div>
  </div>
);

/**
 * Empty state component khi không có sản phẩm
 */
const EmptyState = () => (
  <div className="col-span-full flex flex-col items-center justify-center py-16 text-center">
    <div className="rounded-full bg-muted p-4 mb-4">
      <Package className="h-8 w-8 text-muted-foreground" />
    </div>
    <h3 className="text-lg font-semibold text-foreground mb-2">
      Không có sản phẩm nào
    </h3>
    <p className="text-sm text-muted-foreground max-w-sm">
      Hiện tại không có sản phẩm nào để hiển thị. Vui lòng thử lại sau hoặc điều chỉnh bộ lọc.
    </p>
  </div>
);

/**
 * Component hiển thị danh sách sản phẩm dưới dạng grid layout
 * Thay thế DataTable để phù hợp với user interface ecommerce
 *
 * @param products - Danh sách sản phẩm từ API
 * @param onAddToCart - Callback function khi user nhấn button "Giỏ hàng"
 * @param onProductClick - Callback function khi user nhấn vào sản phẩm
 * @param loading - Trạng thái loading
 * @param className - CSS classes tùy chỉnh
 */
export const EcomProductsGrid = React.forwardRef<HTMLDivElement, EcomProductsGridProps>(
  ({ products, onAddToCart, onProductClick, loading = false, className }, ref) => {
    // Render loading skeletons
    if (loading) {
      return (
        <div
          ref={ref}
          className={cn(
            "grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 xl:grid-cols-6 gap-4 auto-rows-fr p-6",
            className
          )}
        >
          {/* Hiển thị 12 skeleton cards khi loading */}
          {Array.from({ length: 12 }).map((_, index) => (
            <ProductCardSkeleton key={`skeleton-${index}`} />
          ))}
        </div>
      );
    }

    // Render empty state
    if (!products || products.length === 0) {
      return (
        <div
          ref={ref}
          className={cn(
            "grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 xl:grid-cols-6 gap-4 auto-rows-fr p-6",
            className
          )}
        >
          <EmptyState />
        </div>
      );
    }

    // Render products grid
    return (
      <div
        ref={ref}
        className={cn(
          "grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 xl:grid-cols-6 gap-4 auto-rows-fr p-6",
          className
        )}
        role="grid"
        aria-label="Danh sách sản phẩm"
      >
        {products.map((product) => (
          <EcomProductCard
            key={product.id}
            product={product}
            onAddToCart={onAddToCart}
            onProductClick={onProductClick}
          />
        ))}
      </div>
    );
  }
);

EcomProductsGrid.displayName = 'EcomProductsGrid';

export default EcomProductsGrid;



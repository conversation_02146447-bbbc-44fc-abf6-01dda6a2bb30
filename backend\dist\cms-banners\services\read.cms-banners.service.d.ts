import { Repository, DataSource } from 'typeorm';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { BaseCmsBannersService } from './base.cms-banners.service';
import { CmsBanners, CmsBannerStatus, CmsBannerLocation } from '../entity/cms-banners.entity';
import { CmsBannerDto } from '../dto/cms-banner.dto';
import { CustomPaginationQueryDto } from '../../common/dto/custom-pagination-query.dto';
export declare class ReadCmsBannersService extends BaseCmsBannersService {
    protected readonly bannerRepository: Repository<CmsBanners>;
    protected readonly dataSource: DataSource;
    protected readonly eventEmitter: EventEmitter2;
    constructor(bannerRepository: Repository<CmsBanners>, dataSource: DataSource, eventEmitter: EventEmitter2);
    findAll(params: CustomPaginationQueryDto): Promise<{
        data: CmsBannerDto[];
        total: number;
    }>;
    findByStatus(status: CmsBannerStatus, params: CustomPaginationQueryDto): Promise<{
        data: CmsBannerDto[];
        total: number;
    }>;
    findByLocation(location: CmsBannerLocation, params: CustomPaginationQueryDto): Promise<{
        data: CmsBannerDto[];
        total: number;
    }>;
    getActiveBanners(location?: CmsBannerLocation, limit?: number): Promise<CmsBannerDto[]>;
    count(filter?: string): Promise<number>;
    findOne(id: string, relations?: string[]): Promise<CmsBannerDto | null>;
    findOneOrFail(id: string, relations?: string[]): Promise<CmsBannerDto | null>;
    findByBusinessCodePublic(businessCode: string): Promise<CmsBannerDto | null>;
    search(keyword: string, params: CustomPaginationQueryDto): Promise<{
        data: CmsBannerDto[];
        total: number;
    }>;
    findDeleted(params: CustomPaginationQueryDto): Promise<{
        data: CmsBannerDto[];
        total: number;
    }>;
    debugDatabase(): Promise<any>;
    getStatistics(): Promise<{
        total: number;
        byStatus: Record<string, number>;
        byLocation: Record<string, number>;
        active: number;
        expired: number;
    }>;
}

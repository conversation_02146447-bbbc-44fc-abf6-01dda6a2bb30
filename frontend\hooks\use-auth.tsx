"use client";

import React, { createContext, useContext, useState, useEffect, ReactNode, useRef } from "react";
import { authService, User as ApiUser, LoginCredentials, RegisterCredentials, decodeToken, isTokenExpired, isTokenExpiringSoon } from "@/lib/auth";

// Định nghĩa kiểu dữ liệu cho User
export interface User {
  id: string;
  name: string;
  email: string;
  roles: string[];
  avatar?: string;
  fullName?: string;
  username?: string;
  phone?: string;
  address?: string;
}

// Định nghĩa kiểu dữ liệu cho AuthContext
interface AuthContextType {
  user: User | null;
  isLoading: boolean;
  login: (credentials: LoginCredentials) => Promise<void>;
  logout: () => void;
  register: (credentials: RegisterCredentials) => Promise<void>;
  hasRole: (roleName: string) => boolean;
  isAdmin: () => boolean;
  isAgent: () => boolean;
  autoLogin: () => Promise<boolean>;
}

// Tạo AuthContext
const AuthContext = createContext<AuthContextType | undefined>(undefined);

// Provider component
export const AuthProvider = ({ children }: { children: ReactNode }) => {
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  // Hàm tự động đăng nhập với token
  const autoLogin = async (): Promise<boolean> => {
    try {
      // Kiểm tra xác thực từ localStorage hoặc cookie
      if (authService.isAuthenticated()) {
        // Thử lấy thông tin người dùng từ localStorage
        let currentUser = authService.getCurrentUser();

        // Nếu không có thông tin người dùng trong localStorage, thử giải mã token
        if (!currentUser) {
          const accessToken = authService.getAccessToken();
          if (accessToken) {
            currentUser = decodeToken(accessToken);
            if (currentUser) {
              authService.saveUser(currentUser);
            }
          }
        }

        if (currentUser) {
          // Chuyển đổi từ ApiUser sang User
          const userData: User = {
            id: currentUser.id,
            name: currentUser.fullName || currentUser.username || '',
            email: currentUser.email,
            roles: currentUser.roles || [],
            fullName: currentUser.fullName,
            username: currentUser.username,
            phone: currentUser.phone,
            address: currentUser.address,
          };
          setUser(userData);
          return true;
        }
      }
      return false;
    } catch (error) {
      console.error("Error in auto login:", error);
      return false;
    } finally {
      setIsLoading(false);
    }
  };

  // Tham chiếu đến interval để có thể xóa khi component unmount
  const tokenCheckIntervalRef = useRef<NodeJS.Timeout | null>(null);

  // Hàm kiểm tra token định kỳ
  const setupTokenCheck = () => {
    // Xóa interval cũ nếu có
    if (tokenCheckIntervalRef.current) {
      clearInterval(tokenCheckIntervalRef.current);
    }

    // Tạo interval mới để kiểm tra token mỗi phút
    tokenCheckIntervalRef.current = setInterval(() => {
      const token = authService.getAccessToken();
      if (token) {
        // Nếu token đã hết hạn, thử refresh token
        if (isTokenExpired(token)) {
           
          authService.tryRefreshToken().catch(() => {
            // Nếu refresh token thất bại, đăng xuất người dùng
            logout();
          });
        }
        // Nếu token sắp hết hạn, thử refresh token
        else if (isTokenExpiringSoon(token)) {
           
          authService.tryRefreshToken().catch(error => {
            console.error('Error refreshing token:', error);
          });
        }
      }
    }, 60000); // Kiểm tra mỗi phút
  };

  // Kiểm tra xem người dùng đã đăng nhập chưa khi component được mount
  useEffect(() => {
    const checkAuth = async () => {
      try {
        const success = await autoLogin();
        if (success) {
          // Nếu đăng nhập thành công, thiết lập kiểm tra token định kỳ
          setupTokenCheck();
        }
      } catch (error) {
        console.error("Error checking auth:", error);
      } finally {
        setIsLoading(false);
      }
    };

    // Lắng nghe user update events từ refresh token
    const handleUserUpdate = (event: CustomEvent) => {
      const updatedUser = event.detail.user;
      if (updatedUser) {
        const userData: User = {
          id: updatedUser.id,
          name: updatedUser.fullName || updatedUser.username || '',
          email: updatedUser.email,
          roles: updatedUser.roles || [],
          fullName: updatedUser.fullName,
          username: updatedUser.username,
          phone: updatedUser.phone,
          address: updatedUser.address,
        };
        setUser(userData);
      }
    };

    window.addEventListener('auth:userUpdated', handleUserUpdate as EventListener);
    checkAuth();

    // Cleanup khi component unmount
    return () => {
      if (tokenCheckIntervalRef.current) {
        clearInterval(tokenCheckIntervalRef.current);
      }
      window.removeEventListener('auth:userUpdated', handleUserUpdate as EventListener);
    };
  }, []);

  // Hàm kiểm tra xem người dùng có role cụ thể không
  const hasRole = (roleName: string): boolean => {
    if (!user || !user.roles) return false;
    return user.roles.includes(roleName);
  };

  // Hàm kiểm tra xem người dùng có phải là admin không
  const isAdmin = (): boolean => {
    return hasRole("ADMIN");
  };

  // Hàm kiểm tra xem người dùng có phải là agent không
  const isAgent = (): boolean => {
    return hasRole("AGENT");
  };

  // Hàm đăng nhập
  const login = async (credentials: LoginCredentials) => {
    setIsLoading(true);
    try {
       

      // Gọi API đăng nhập thực tế
      const response = await authService.login(credentials);
       

      // Lấy thông tin người dùng từ response hoặc từ token
      let userData: User | null = null;

      if (response.user) {
        // Chuyển đổi từ ApiUser sang User
        userData = {
          id: response.user.id,
          name: response.user.fullName || response.user.username || '',
          email: response.user.email,
          roles: response.user.roles || [],
          fullName: response.user.fullName,
          username: response.user.username,
          phone: response.user.phone,
          address: response.user.address,
        };
      } else if (response.access_token) {
        // Nếu không có thông tin người dùng trong response, giải mã token
        const decodedUser = decodeToken(response.access_token);
        if (decodedUser) {
          userData = {
            id: decodedUser.id,
            name: decodedUser.fullName || decodedUser.username || '',
            email: decodedUser.email,
            roles: decodedUser.roles || [],
            fullName: decodedUser.fullName,
            username: decodedUser.username,
            phone: decodedUser.phone,
            address: decodedUser.address,
          };
        }
      }

      if (userData) {
         
        setUser(userData);

        // Thiết lập kiểm tra token định kỳ sau khi đăng nhập thành công
        setupTokenCheck();
      }
    } catch (error) {
      console.error("Login error:", error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  // Hàm đăng xuất
  const logout = () => {
    // Xóa interval kiểm tra token
    if (tokenCheckIntervalRef.current) {
      clearInterval(tokenCheckIntervalRef.current);
      tokenCheckIntervalRef.current = null;
    }

    // Set loading state để tránh UI flicker
    setIsLoading(true);

    // Xóa user state
    setUser(null);

    // Sau đó mới gọi authService.logout() (sẽ redirect)
    authService.logout();
  };

  // Hàm đăng ký
  const register = async (credentials: RegisterCredentials) => {
    setIsLoading(true);
    try {
       

      // Gọi API đăng ký thực tế
      const response = await authService.register(credentials);
       

      // Lấy thông tin người dùng từ response hoặc từ token
      let userData: User | null = null;

      if (response.user) {
        // Chuyển đổi từ ApiUser sang User
        userData = {
          id: response.user.id,
          name: response.user.fullName || response.user.username || '',
          email: response.user.email,
          roles: response.user.roles || [],
          fullName: response.user.fullName,
          username: response.user.username,
          phone: response.user.phone,
          address: response.user.address,
        };
      } else if (response.access_token) {
        // Nếu không có thông tin người dùng trong response, giải mã token
        const decodedUser = decodeToken(response.access_token);
        if (decodedUser) {
          userData = {
            id: decodedUser.id,
            name: decodedUser.fullName || decodedUser.username || '',
            email: decodedUser.email,
            roles: decodedUser.roles || [],
            fullName: decodedUser.fullName,
            username: decodedUser.username,
            phone: decodedUser.phone,
            address: decodedUser.address,
          };
        }
      }

      if (userData) {
         
        setUser(userData);

        // Thiết lập kiểm tra token định kỳ sau khi đăng ký thành công
        setupTokenCheck();
      }
    } catch (error) {
      console.error("Register error:", error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  const value = {
    user,
    isLoading,
    login,
    logout,
    register,
    hasRole,
    isAdmin,
    isAgent,
    autoLogin,
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
}

// Hook để sử dụng AuthContext
export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error("useAuth must be used within an AuthProvider");
  }
  return context;
}

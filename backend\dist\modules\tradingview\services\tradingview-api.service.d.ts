import { OnModuleInit } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { Repository } from 'typeorm';
import { HttpService } from '@nestjs/axios';
import { SystemConfig } from '../../../system-configs/entities/system-config.entity';
import { SilverPrice } from '../entities/silver-price.entity';
import { CurrentPrice } from '../models/tradingview-data.model';
import { TradingViewWebsocketService } from './tradingview-websocket.service';
import { CacheService } from './cache.service';
export declare class TradingViewApiService implements OnModuleInit {
    private readonly configService;
    private readonly httpService;
    private readonly tradingViewWebsocketService;
    private readonly cacheService;
    private readonly systemConfigRepository;
    private readonly silverPriceRepository;
    private readonly logger;
    private apiKey;
    private apiSecret;
    private baseUrl;
    private wsEndpoint;
    private wsClient;
    private isConnected;
    private reconnectAttempts;
    private readonly maxReconnectAttempts;
    private reconnectInterval;
    private dataPollingInterval;
    private defaultSymbol;
    constructor(configService: ConfigService, httpService: HttpService, tradingViewWebsocketService: TradingViewWebsocketService, cacheService: CacheService, systemConfigRepository: Repository<SystemConfig>, silverPriceRepository: Repository<SilverPrice>);
    onModuleInit(): Promise<void>;
    private loadConfig;
    private getOrCreateConfig;
    private connectWebSocket;
    private attemptReconnect;
    private handleWebSocketMessage;
    private subscribeToSymbol;
    private broadcastPriceUpdate;
    private savePriceData;
    private startDataPolling;
    fetchLatestPrice(): Promise<CurrentPrice>;
}

{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/bloomgoo/gold-exchange-platform/frontend/hooks/use-mobile.ts"], "sourcesContent": ["import * as React from \"react\"\r\n\r\nconst MO<PERSON>LE_BREAKPOINT = 768\r\n\r\nexport function useIsMobile() {\r\n  const [isMobile, setIsMobile] = React.useState<boolean | undefined>(undefined)\r\n\r\n  React.useEffect(() => {\r\n    const mql = window.matchMedia(`(max-width: ${MOBILE_BREAKPOINT - 1}px)`)\r\n    const onChange = () => {\r\n      setIsMobile(window.innerWidth < MOBILE_BREAKPOINT)\r\n    }\r\n    mql.addEventListener(\"change\", onChange)\r\n    setIsMobile(window.innerWidth < MOBILE_BREAKPOINT)\r\n    return () => mql.removeEventListener(\"change\", onChange)\r\n  }, [])\r\n\r\n  return !!isMobile\r\n}\r\n"], "names": [], "mappings": ";;;AAAA;;;AAEA,MAAM,oBAAoB;AAEnB,SAAS;;IACd,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,sQAAA,CAAA,WAAc,AAAD,EAAuB;IAEpE,CAAA,GAAA,sQAAA,CAAA,YAAe,AAAD;iCAAE;YACd,MAAM,MAAM,OAAO,UAAU,CAAC,CAAC,YAAY,EAAE,oBAAoB,EAAE,GAAG,CAAC;YACvE,MAAM;kDAAW;oBACf,YAAY,OAAO,UAAU,GAAG;gBAClC;;YACA,IAAI,gBAAgB,CAAC,UAAU;YAC/B,YAAY,OAAO,UAAU,GAAG;YAChC;yCAAO,IAAM,IAAI,mBAAmB,CAAC,UAAU;;QACjD;gCAAG,EAAE;IAEL,OAAO,CAAC,CAAC;AACX;GAdgB", "debugId": null}}, {"offset": {"line": 44, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/bloomgoo/gold-exchange-platform/frontend/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\r\nimport { Slot } from \"@radix-ui/react-slot\"\r\nimport { cva, type VariantProps } from \"class-variance-authority\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst buttonVariants = cva(\r\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\r\n  {\r\n    variants: {\r\n      variant: {\r\n        default:\r\n          \"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90\",\r\n        destructive:\r\n          \"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\r\n        outline:\r\n          \"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50\",\r\n        secondary:\r\n          \"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80\",\r\n        ghost:\r\n          \"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50\",\r\n        link: \"text-primary underline-offset-4 hover:underline\",\r\n      },\r\n      size: {\r\n        default: \"h-9 px-4 py-2 has-[>svg]:px-3\",\r\n        sm: \"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5\",\r\n        lg: \"h-10 rounded-md px-6 has-[>svg]:px-4\",\r\n        icon: \"size-9\",\r\n      },\r\n    },\r\n    defaultVariants: {\r\n      variant: \"default\",\r\n      size: \"default\",\r\n    },\r\n  }\r\n)\r\n\r\nfunction Button({\r\n  className,\r\n  variant,\r\n  size,\r\n  asChild = false,\r\n  ...props\r\n}: React.ComponentProps<\"button\"> &\r\n  VariantProps<typeof buttonVariants> & {\r\n    asChild?: boolean\r\n  }) {\r\n  const Comp = asChild ? Slot : \"button\"\r\n\r\n  return (\r\n    <Comp\r\n      data-slot=\"button\"\r\n      className={cn(buttonVariants({ variant, size, className }))}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport { Button, buttonVariants }\r\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;AAAA;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,8OAAA,CAAA,MAAG,AAAD,EACvB,+bACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OACE;YACF,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,SAAS,OAAO,EACd,SAAS,EACT,OAAO,EACP,IAAI,EACJ,UAAU,KAAK,EACf,GAAG,OAIF;IACD,MAAM,OAAO,UAAU,uSAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,sSAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,+HAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACvD,GAAG,KAAK;;;;;;AAGf;KAnBS", "debugId": null}}, {"offset": {"line": 108, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/bloomgoo/gold-exchange-platform/frontend/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction Input({ className, type, ...props }: React.ComponentProps<\"input\">) {\r\n  return (\r\n    <input\r\n      type={type}\r\n      data-slot=\"input\"\r\n      className={cn(\r\n        \"file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\r\n        \"focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]\",\r\n        \"aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport { Input }\r\n"], "names": [], "mappings": ";;;;AAEA;AAAA;;;AAEA,SAAS,MAAM,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAsC;IACzE,qBACE,sSAAC;QACC,MAAM;QACN,aAAU;QACV,WAAW,CAAA,GAAA,+HAAA,CAAA,KAAE,AAAD,EACV,mcACA,iFACA,0GACA;QAED,GAAG,KAAK;;;;;;AAGf;KAdS", "debugId": null}}, {"offset": {"line": 141, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/bloomgoo/gold-exchange-platform/frontend/components/ui/separator.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as SeparatorPrimitive from \"@radix-ui/react-separator\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction Separator({\r\n  className,\r\n  orientation = \"horizontal\",\r\n  decorative = true,\r\n  ...props\r\n}: React.ComponentProps<typeof SeparatorPrimitive.Root>) {\r\n  return (\r\n    <SeparatorPrimitive.Root\r\n      data-slot=\"separator-root\"\r\n      decorative={decorative}\r\n      orientation={orientation}\r\n      className={cn(\r\n        \"bg-border shrink-0 data-[orientation=horizontal]:h-px data-[orientation=horizontal]:w-full data-[orientation=vertical]:h-full data-[orientation=vertical]:w-px\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport { Separator }\r\n"], "names": [], "mappings": ";;;;AAGA;AAEA;AAAA;AALA;;;;AAOA,SAAS,UAAU,EACjB,SAAS,EACT,cAAc,YAAY,EAC1B,aAAa,IAAI,EACjB,GAAG,OACkD;IACrD,qBACE,sSAAC,+QAAA,CAAA,OAAuB;QACtB,aAAU;QACV,YAAY;QACZ,aAAa;QACb,WAAW,CAAA,GAAA,+HAAA,CAAA,KAAE,AAAD,EACV,kKACA;QAED,GAAG,KAAK;;;;;;AAGf;KAlBS", "debugId": null}}, {"offset": {"line": 178, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/bloomgoo/gold-exchange-platform/frontend/components/ui/sheet.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as SheetPrimitive from \"@radix-ui/react-dialog\"\r\nimport { XIcon } from \"lucide-react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction Sheet({ ...props }: React.ComponentProps<typeof SheetPrimitive.Root>) {\r\n  return <SheetPrimitive.Root data-slot=\"sheet\" {...props} />\r\n}\r\n\r\nfunction SheetTrigger({\r\n  ...props\r\n}: React.ComponentProps<typeof SheetPrimitive.Trigger>) {\r\n  return <SheetPrimitive.Trigger data-slot=\"sheet-trigger\" {...props} />\r\n}\r\n\r\nfunction SheetClose({\r\n  ...props\r\n}: React.ComponentProps<typeof SheetPrimitive.Close>) {\r\n  return <SheetPrimitive.Close data-slot=\"sheet-close\" {...props} />\r\n}\r\n\r\nfunction SheetPortal({\r\n  ...props\r\n}: React.ComponentProps<typeof SheetPrimitive.Portal>) {\r\n  return <SheetPrimitive.Portal data-slot=\"sheet-portal\" {...props} />\r\n}\r\n\r\nfunction SheetOverlay({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof SheetPrimitive.Overlay>) {\r\n  return (\r\n    <SheetPrimitive.Overlay\r\n      data-slot=\"sheet-overlay\"\r\n      className={cn(\r\n        \"data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction SheetContent({\r\n  className,\r\n  children,\r\n  side = \"right\",\r\n  ...props\r\n}: React.ComponentProps<typeof SheetPrimitive.Content> & {\r\n  side?: \"top\" | \"right\" | \"bottom\" | \"left\"\r\n}) {\r\n  return (\r\n    <SheetPortal>\r\n      <SheetOverlay />\r\n      <SheetPrimitive.Content\r\n        data-slot=\"sheet-content\"\r\n        className={cn(\r\n          \"bg-background data-[state=open]:animate-in data-[state=closed]:animate-out fixed z-50 flex flex-col gap-4 shadow-lg transition ease-in-out data-[state=closed]:duration-300 data-[state=open]:duration-500\",\r\n          side === \"right\" &&\r\n            \"data-[state=closed]:slide-out-to-right data-[state=open]:slide-in-from-right inset-y-0 right-0 h-full w-3/4 border-l sm:max-w-sm\",\r\n          side === \"left\" &&\r\n            \"data-[state=closed]:slide-out-to-left data-[state=open]:slide-in-from-left inset-y-0 left-0 h-full w-3/4 border-r sm:max-w-sm\",\r\n          side === \"top\" &&\r\n            \"data-[state=closed]:slide-out-to-top data-[state=open]:slide-in-from-top inset-x-0 top-0 h-auto border-b\",\r\n          side === \"bottom\" &&\r\n            \"data-[state=closed]:slide-out-to-bottom data-[state=open]:slide-in-from-bottom inset-x-0 bottom-0 h-auto border-t\",\r\n          className\r\n        )}\r\n        {...props}\r\n      >\r\n        {children}\r\n        <SheetPrimitive.Close className=\"ring-offset-background focus:ring-ring data-[state=open]:bg-secondary absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none\">\r\n          <XIcon className=\"size-4\" />\r\n          <span className=\"sr-only\">Close</span>\r\n        </SheetPrimitive.Close>\r\n      </SheetPrimitive.Content>\r\n    </SheetPortal>\r\n  )\r\n}\r\n\r\nfunction SheetHeader({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"sheet-header\"\r\n      className={cn(\"flex flex-col gap-1.5 p-4\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction SheetFooter({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"sheet-footer\"\r\n      className={cn(\"mt-auto flex flex-col gap-2 p-4\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction SheetTitle({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof SheetPrimitive.Title>) {\r\n  return (\r\n    <SheetPrimitive.Title\r\n      data-slot=\"sheet-title\"\r\n      className={cn(\"text-foreground font-semibold\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction SheetDescription({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof SheetPrimitive.Description>) {\r\n  return (\r\n    <SheetPrimitive.Description\r\n      data-slot=\"sheet-description\"\r\n      className={cn(\"text-muted-foreground text-sm\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport {\r\n  Sheet,\r\n  SheetTrigger,\r\n  SheetClose,\r\n  SheetContent,\r\n  SheetHeader,\r\n  SheetFooter,\r\n  SheetTitle,\r\n  SheetDescription,\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;;AAGA;AACA;AAEA;AAAA;AANA;;;;;AAQA,SAAS,MAAM,EAAE,GAAG,OAAyD;IAC3E,qBAAO,sSAAC,kRAAA,CAAA,OAAmB;QAAC,aAAU;QAAS,GAAG,KAAK;;;;;;AACzD;KAFS;AAIT,SAAS,aAAa,EACpB,GAAG,OACiD;IACpD,qBAAO,sSAAC,kRAAA,CAAA,UAAsB;QAAC,aAAU;QAAiB,GAAG,KAAK;;;;;;AACpE;MAJS;AAMT,SAAS,WAAW,EAClB,GAAG,OAC+C;IAClD,qBAAO,sSAAC,kRAAA,CAAA,QAAoB;QAAC,aAAU;QAAe,GAAG,KAAK;;;;;;AAChE;MAJS;AAMT,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,sSAAC,kRAAA,CAAA,SAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;MAJS;AAMT,SAAS,aAAa,EACpB,SAAS,EACT,GAAG,OACiD;IACpD,qBACE,sSAAC,kRAAA,CAAA,UAAsB;QACrB,aAAU;QACV,WAAW,CAAA,GAAA,+HAAA,CAAA,KAAE,AAAD,EACV,0JACA;QAED,GAAG,KAAK;;;;;;AAGf;MAdS;AAgBT,SAAS,aAAa,EACpB,SAAS,EACT,QAAQ,EACR,OAAO,OAAO,EACd,GAAG,OAGJ;IACC,qBACE,sSAAC;;0BACC,sSAAC;;;;;0BACD,sSAAC,kRAAA,CAAA,UAAsB;gBACrB,aAAU;gBACV,WAAW,CAAA,GAAA,+HAAA,CAAA,KAAE,AAAD,EACV,8MACA,SAAS,WACP,oIACF,SAAS,UACP,iIACF,SAAS,SACP,4GACF,SAAS,YACP,qHACF;gBAED,GAAG,KAAK;;oBAER;kCACD,sSAAC,kRAAA,CAAA,QAAoB;wBAAC,WAAU;;0CAC9B,sSAAC,uRAAA,CAAA,QAAK;gCAAC,WAAU;;;;;;0CACjB,sSAAC;gCAAK,WAAU;0CAAU;;;;;;;;;;;;;;;;;;;;;;;;AAKpC;MAnCS;AAqCT,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,sSAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,+HAAA,CAAA,KAAE,AAAD,EAAE,6BAA6B;QAC1C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,sSAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,+HAAA,CAAA,KAAE,AAAD,EAAE,mCAAmC;QAChD,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,WAAW,EAClB,SAAS,EACT,GAAG,OAC+C;IAClD,qBACE,sSAAC,kRAAA,CAAA,QAAoB;QACnB,aAAU;QACV,WAAW,CAAA,GAAA,+HAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,iBAAiB,EACxB,SAAS,EACT,GAAG,OACqD;IACxD,qBACE,sSAAC,kRAAA,CAAA,cAA0B;QACzB,aAAU;QACV,WAAW,CAAA,GAAA,+HAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;MAXS", "debugId": null}}, {"offset": {"line": 375, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/bloomgoo/gold-exchange-platform/frontend/components/ui/skeleton.tsx"], "sourcesContent": ["import { cn } from \"@/lib/utils\"\r\n\r\nfunction Skeleton({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"skeleton\"\r\n      className={cn(\"bg-accent animate-pulse rounded-md\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport { Skeleton }\r\n"], "names": [], "mappings": ";;;;AAAA;AAAA;;;AAEA,SAAS,SAAS,EAAE,SAAS,EAAE,GAAG,OAAoC;IACpE,qBACE,sSAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,+HAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QACnD,GAAG,KAAK;;;;;;AAGf;KARS", "debugId": null}}, {"offset": {"line": 407, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/bloomgoo/gold-exchange-platform/frontend/components/ui/tooltip.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as TooltipPrimitive from \"@radix-ui/react-tooltip\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction TooltipProvider({\r\n  delayDuration = 0,\r\n  ...props\r\n}: React.ComponentProps<typeof TooltipPrimitive.Provider>) {\r\n  return (\r\n    <TooltipPrimitive.Provider\r\n      data-slot=\"tooltip-provider\"\r\n      delayDuration={delayDuration}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction Tooltip({\r\n  ...props\r\n}: React.ComponentProps<typeof TooltipPrimitive.Root>) {\r\n  return (\r\n    <TooltipProvider>\r\n      <TooltipPrimitive.Root data-slot=\"tooltip\" {...props} />\r\n    </TooltipProvider>\r\n  )\r\n}\r\n\r\nfunction TooltipTrigger({\r\n  ...props\r\n}: React.ComponentProps<typeof TooltipPrimitive.Trigger>) {\r\n  return <TooltipPrimitive.Trigger data-slot=\"tooltip-trigger\" {...props} />\r\n}\r\n\r\nfunction TooltipContent({\r\n  className,\r\n  sideOffset = 0,\r\n  children,\r\n  ...props\r\n}: React.ComponentProps<typeof TooltipPrimitive.Content>) {\r\n  return (\r\n    <TooltipPrimitive.Portal>\r\n      <TooltipPrimitive.Content\r\n        data-slot=\"tooltip-content\"\r\n        sideOffset={sideOffset}\r\n        className={cn(\r\n          \"bg-primary text-primary-foreground animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 w-fit origin-(--radix-tooltip-content-transform-origin) rounded-md px-3 py-1.5 text-xs text-balance\",\r\n          className\r\n        )}\r\n        {...props}\r\n      >\r\n        {children}\r\n        <TooltipPrimitive.Arrow className=\"bg-primary fill-primary z-50 size-2.5 translate-y-[calc(-50%_-_2px)] rotate-45 rounded-[2px]\" />\r\n      </TooltipPrimitive.Content>\r\n    </TooltipPrimitive.Portal>\r\n  )\r\n}\r\n\r\nexport { Tooltip, TooltipTrigger, TooltipContent, TooltipProvider }\r\n"], "names": [], "mappings": ";;;;;;;AAGA;AAEA;AAAA;AALA;;;;AAOA,SAAS,gBAAgB,EACvB,gBAAgB,CAAC,EACjB,GAAG,OACoD;IACvD,qBACE,sSAAC,gRAAA,CAAA,WAAyB;QACxB,aAAU;QACV,eAAe;QACd,GAAG,KAAK;;;;;;AAGf;KAXS;AAaT,SAAS,QAAQ,EACf,GAAG,OACgD;IACnD,qBACE,sSAAC;kBACC,cAAA,sSAAC,gRAAA,CAAA,OAAqB;YAAC,aAAU;YAAW,GAAG,KAAK;;;;;;;;;;;AAG1D;MARS;AAUT,SAAS,eAAe,EACtB,GAAG,OACmD;IACtD,qBAAO,sSAAC,gRAAA,CAAA,UAAwB;QAAC,aAAU;QAAmB,GAAG,KAAK;;;;;;AACxE;MAJS;AAMT,SAAS,eAAe,EACtB,SAAS,EACT,aAAa,CAAC,EACd,QAAQ,EACR,GAAG,OACmD;IACtD,qBACE,sSAAC,gRAAA,CAAA,SAAuB;kBACtB,cAAA,sSAAC,gRAAA,CAAA,UAAwB;YACvB,aAAU;YACV,YAAY;YACZ,WAAW,CAAA,GAAA,+HAAA,CAAA,KAAE,AAAD,EACV,0aACA;YAED,GAAG,KAAK;;gBAER;8BACD,sSAAC,gRAAA,CAAA,QAAsB;oBAAC,WAAU;;;;;;;;;;;;;;;;;AAI1C;MAtBS", "debugId": null}}, {"offset": {"line": 505, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/bloomgoo/gold-exchange-platform/frontend/components/ui/sidebar.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport { Slot } from \"@radix-ui/react-slot\"\r\nimport { VariantProps, cva } from \"class-variance-authority\"\r\nimport { PanelLeftIcon } from \"lucide-react\"\r\n\r\nimport { useIsMobile } from \"@/hooks/use-mobile\"\r\nimport { cn } from \"@/lib/utils\"\r\nimport { Button } from \"@/components/ui/button\"\r\nimport { Input } from \"@/components/ui/input\"\r\nimport { Separator } from \"@/components/ui/separator\"\r\nimport {\r\n  Sheet,\r\n  SheetContent,\r\n  SheetDescription,\r\n  SheetHeader,\r\n  SheetTitle,\r\n} from \"@/components/ui/sheet\"\r\nimport { Skeleton } from \"@/components/ui/skeleton\"\r\nimport {\r\n  Toolt<PERSON>,\r\n  TooltipContent,\r\n  TooltipProvider,\r\n  TooltipTrigger,\r\n} from \"@/components/ui/tooltip\"\r\n\r\nconst SIDEBAR_COOKIE_NAME = \"sidebar_state\"\r\nconst SIDEBAR_COOKIE_MAX_AGE = 60 * 60 * 24 * 7\r\nconst SIDEBAR_WIDTH = \"16rem\"\r\nconst SIDEBAR_WIDTH_MOBILE = \"18rem\"\r\nconst SIDEBAR_WIDTH_ICON = \"3rem\"\r\nconst SIDEBAR_KEYBOARD_SHORTCUT = \"b\"\r\n\r\ntype SidebarContextProps = {\r\n  state: \"expanded\" | \"collapsed\"\r\n  open: boolean\r\n  setOpen: (open: boolean) => void\r\n  openMobile: boolean\r\n  setOpenMobile: (open: boolean) => void\r\n  isMobile: boolean\r\n  toggleSidebar: () => void\r\n}\r\n\r\nconst SidebarContext = React.createContext<SidebarContextProps | null>(null)\r\n\r\nfunction useSidebar() {\r\n  const context = React.useContext(SidebarContext)\r\n  if (!context) {\r\n    throw new Error(\"useSidebar must be used within a SidebarProvider.\")\r\n  }\r\n\r\n  return context\r\n}\r\n\r\nfunction SidebarProvider({\r\n  defaultOpen = true,\r\n  open: openProp,\r\n  onOpenChange: setOpenProp,\r\n  className,\r\n  style,\r\n  children,\r\n  ...props\r\n}: React.ComponentProps<\"div\"> & {\r\n  defaultOpen?: boolean\r\n  open?: boolean\r\n  onOpenChange?: (open: boolean) => void\r\n}) {\r\n  const isMobile = useIsMobile()\r\n  const [openMobile, setOpenMobile] = React.useState(false)\r\n\r\n  // This is the internal state of the sidebar.\r\n  // We use openProp and setOpenProp for control from outside the component.\r\n  const [_open, _setOpen] = React.useState(defaultOpen)\r\n  const open = openProp ?? _open\r\n  const setOpen = React.useCallback(\r\n    (value: boolean | ((value: boolean) => boolean)) => {\r\n      const openState = typeof value === \"function\" ? value(open) : value\r\n      if (setOpenProp) {\r\n        setOpenProp(openState)\r\n      } else {\r\n        _setOpen(openState)\r\n      }\r\n\r\n      // This sets the cookie to keep the sidebar state.\r\n      document.cookie = `${SIDEBAR_COOKIE_NAME}=${openState}; path=/; max-age=${SIDEBAR_COOKIE_MAX_AGE}`\r\n    },\r\n    [setOpenProp, open]\r\n  )\r\n\r\n  // Helper to toggle the sidebar.\r\n  const toggleSidebar = React.useCallback(() => {\r\n    return isMobile ? setOpenMobile((open) => !open) : setOpen((open) => !open)\r\n  }, [isMobile, setOpen, setOpenMobile])\r\n\r\n  // Adds a keyboard shortcut to toggle the sidebar.\r\n  React.useEffect(() => {\r\n    const handleKeyDown = (event: KeyboardEvent) => {\r\n      if (\r\n        event.key === SIDEBAR_KEYBOARD_SHORTCUT &&\r\n        (event.metaKey || event.ctrlKey)\r\n      ) {\r\n        event.preventDefault()\r\n        toggleSidebar()\r\n      }\r\n    }\r\n\r\n    window.addEventListener(\"keydown\", handleKeyDown)\r\n    return () => window.removeEventListener(\"keydown\", handleKeyDown)\r\n  }, [toggleSidebar])\r\n\r\n  // We add a state so that we can do data-state=\"expanded\" or \"collapsed\".\r\n  // This makes it easier to style the sidebar with Tailwind classes.\r\n  const state = open ? \"expanded\" : \"collapsed\"\r\n\r\n  const contextValue = React.useMemo<SidebarContextProps>(\r\n    () => ({\r\n      state,\r\n      open,\r\n      setOpen,\r\n      isMobile,\r\n      openMobile,\r\n      setOpenMobile,\r\n      toggleSidebar,\r\n    }),\r\n    [state, open, setOpen, isMobile, openMobile, setOpenMobile, toggleSidebar]\r\n  )\r\n\r\n  return (\r\n    <SidebarContext.Provider value={contextValue}>\r\n      <TooltipProvider delayDuration={0}>\r\n        <div\r\n          data-slot=\"sidebar-wrapper\"\r\n          style={\r\n            {\r\n              \"--sidebar-width\": SIDEBAR_WIDTH,\r\n              \"--sidebar-width-icon\": SIDEBAR_WIDTH_ICON,\r\n              ...style,\r\n            } as React.CSSProperties\r\n          }\r\n          className={cn(\r\n            \"group/sidebar-wrapper has-data-[variant=inset]:bg-sidebar flex min-h-svh w-full\",\r\n            className\r\n          )}\r\n          {...props}\r\n        >\r\n          {children}\r\n        </div>\r\n      </TooltipProvider>\r\n    </SidebarContext.Provider>\r\n  )\r\n}\r\n\r\nfunction Sidebar({\r\n  side = \"left\",\r\n  variant = \"sidebar\",\r\n  collapsible = \"offcanvas\",\r\n  className,\r\n  children,\r\n  ...props\r\n}: React.ComponentProps<\"div\"> & {\r\n  side?: \"left\" | \"right\"\r\n  variant?: \"sidebar\" | \"floating\" | \"inset\"\r\n  collapsible?: \"offcanvas\" | \"icon\" | \"none\"\r\n}) {\r\n  const { isMobile, state, openMobile, setOpenMobile } = useSidebar()\r\n\r\n  if (collapsible === \"none\") {\r\n    return (\r\n      <div\r\n        data-slot=\"sidebar\"\r\n        className={cn(\r\n          \"bg-sidebar text-sidebar-foreground flex h-full w-(--sidebar-width) flex-col\",\r\n          className\r\n        )}\r\n        {...props}\r\n      >\r\n        {children}\r\n      </div>\r\n    )\r\n  }\r\n\r\n  if (isMobile) {\r\n    return (\r\n      <Sheet open={openMobile} onOpenChange={setOpenMobile} {...props}>\r\n        <SheetContent\r\n          data-sidebar=\"sidebar\"\r\n          data-slot=\"sidebar\"\r\n          data-mobile=\"true\"\r\n          className=\"bg-sidebar text-sidebar-foreground w-(--sidebar-width) p-0 [&>button]:hidden\"\r\n          style={\r\n            {\r\n              \"--sidebar-width\": SIDEBAR_WIDTH_MOBILE,\r\n            } as React.CSSProperties\r\n          }\r\n          side={side}\r\n        >\r\n          <SheetHeader className=\"sr-only\">\r\n            <SheetTitle>Sidebar</SheetTitle>\r\n            <SheetDescription>Displays the mobile sidebar.</SheetDescription>\r\n          </SheetHeader>\r\n          <div className=\"flex h-full w-full flex-col\">{children}</div>\r\n        </SheetContent>\r\n      </Sheet>\r\n    )\r\n  }\r\n\r\n  return (\r\n    <div\r\n      className=\"group peer text-sidebar-foreground hidden md:block\"\r\n      data-state={state}\r\n      data-collapsible={state === \"collapsed\" ? collapsible : \"\"}\r\n      data-variant={variant}\r\n      data-side={side}\r\n      data-slot=\"sidebar\"\r\n    >\r\n      {/* This is what handles the sidebar gap on desktop */}\r\n      <div\r\n        data-slot=\"sidebar-gap\"\r\n        className={cn(\r\n          \"relative w-(--sidebar-width) bg-transparent transition-[width] duration-200 ease-linear\",\r\n          \"group-data-[collapsible=offcanvas]:w-0\",\r\n          \"group-data-[side=right]:rotate-180\",\r\n          variant === \"floating\" || variant === \"inset\"\r\n            ? \"group-data-[collapsible=icon]:w-[calc(var(--sidebar-width-icon)+(--spacing(4)))]\"\r\n            : \"group-data-[collapsible=icon]:w-(--sidebar-width-icon)\"\r\n        )}\r\n      />\r\n      <div\r\n        data-slot=\"sidebar-container\"\r\n        className={cn(\r\n          \"fixed inset-y-0 z-10 hidden h-svh w-(--sidebar-width) transition-[left,right,width] duration-200 ease-linear md:flex\",\r\n          side === \"left\"\r\n            ? \"left-0 group-data-[collapsible=offcanvas]:left-[calc(var(--sidebar-width)*-1)]\"\r\n            : \"right-0 group-data-[collapsible=offcanvas]:right-[calc(var(--sidebar-width)*-1)]\",\r\n          // Adjust the padding for floating and inset variants.\r\n          variant === \"floating\" || variant === \"inset\"\r\n            ? \"p-2 group-data-[collapsible=icon]:w-[calc(var(--sidebar-width-icon)+(--spacing(4))+2px)]\"\r\n            : \"group-data-[collapsible=icon]:w-(--sidebar-width-icon) group-data-[side=left]:border-r group-data-[side=right]:border-l\",\r\n          className\r\n        )}\r\n        {...props}\r\n      >\r\n        <div\r\n          data-sidebar=\"sidebar\"\r\n          data-slot=\"sidebar-inner\"\r\n          className=\"bg-sidebar group-data-[variant=floating]:border-sidebar-border flex h-full w-full flex-col group-data-[variant=floating]:rounded-lg group-data-[variant=floating]:border group-data-[variant=floating]:shadow-sm\"\r\n        >\r\n          {children}\r\n        </div>\r\n      </div>\r\n    </div>\r\n  )\r\n}\r\n\r\nfunction SidebarTrigger({\r\n  className,\r\n  onClick,\r\n  ...props\r\n}: React.ComponentProps<typeof Button>) {\r\n  const { toggleSidebar } = useSidebar()\r\n\r\n  return (\r\n    <Button\r\n      data-sidebar=\"trigger\"\r\n      data-slot=\"sidebar-trigger\"\r\n      variant=\"ghost\"\r\n      size=\"icon\"\r\n      className={cn(\"size-7\", className)}\r\n      onClick={(event) => {\r\n        onClick?.(event)\r\n        toggleSidebar()\r\n      }}\r\n      {...props}\r\n    >\r\n      <PanelLeftIcon />\r\n      <span className=\"sr-only\">Toggle Sidebar</span>\r\n    </Button>\r\n  )\r\n}\r\n\r\nfunction SidebarRail({ className, ...props }: React.ComponentProps<\"button\">) {\r\n  const { toggleSidebar } = useSidebar()\r\n\r\n  return (\r\n    <button\r\n      data-sidebar=\"rail\"\r\n      data-slot=\"sidebar-rail\"\r\n      aria-label=\"Toggle Sidebar\"\r\n      tabIndex={-1}\r\n      onClick={toggleSidebar}\r\n      title=\"Toggle Sidebar\"\r\n      className={cn(\r\n        \"hover:after:bg-sidebar-border absolute inset-y-0 z-20 hidden w-4 -translate-x-1/2 transition-all ease-linear group-data-[side=left]:-right-4 group-data-[side=right]:left-0 after:absolute after:inset-y-0 after:left-1/2 after:w-[2px] sm:flex\",\r\n        \"in-data-[side=left]:cursor-w-resize in-data-[side=right]:cursor-e-resize\",\r\n        \"[[data-side=left][data-state=collapsed]_&]:cursor-e-resize [[data-side=right][data-state=collapsed]_&]:cursor-w-resize\",\r\n        \"hover:group-data-[collapsible=offcanvas]:bg-sidebar group-data-[collapsible=offcanvas]:translate-x-0 group-data-[collapsible=offcanvas]:after:left-full\",\r\n        \"[[data-side=left][data-collapsible=offcanvas]_&]:-right-2\",\r\n        \"[[data-side=right][data-collapsible=offcanvas]_&]:-left-2\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction SidebarInset({ className, ...props }: React.ComponentProps<\"main\">) {\r\n  return (\r\n    <main\r\n      data-slot=\"sidebar-inset\"\r\n      className={cn(\r\n        \"bg-background relative flex w-full flex-1 flex-col\",\r\n        \"md:peer-data-[variant=inset]:m-2 md:peer-data-[variant=inset]:ml-0 md:peer-data-[variant=inset]:rounded-xl md:peer-data-[variant=inset]:shadow-sm md:peer-data-[variant=inset]:peer-data-[state=collapsed]:ml-2\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction SidebarInput({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof Input>) {\r\n  return (\r\n    <Input\r\n      data-slot=\"sidebar-input\"\r\n      data-sidebar=\"input\"\r\n      className={cn(\"bg-background h-8 w-full shadow-none\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction SidebarHeader({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"sidebar-header\"\r\n      data-sidebar=\"header\"\r\n      className={cn(\"flex flex-col gap-2 p-2\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction SidebarFooter({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"sidebar-footer\"\r\n      data-sidebar=\"footer\"\r\n      className={cn(\"flex flex-col gap-2 p-2\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction SidebarSeparator({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof Separator>) {\r\n  return (\r\n    <Separator\r\n      data-slot=\"sidebar-separator\"\r\n      data-sidebar=\"separator\"\r\n      className={cn(\"bg-sidebar-border mx-2 w-auto\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction SidebarContent({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"sidebar-content\"\r\n      data-sidebar=\"content\"\r\n      className={cn(\r\n        \"flex min-h-0 flex-1 flex-col gap-2 overflow-auto group-data-[collapsible=icon]:overflow-hidden\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction SidebarGroup({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"sidebar-group\"\r\n      data-sidebar=\"group\"\r\n      className={cn(\"relative flex w-full min-w-0 flex-col p-2\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction SidebarGroupLabel({\r\n  className,\r\n  asChild = false,\r\n  ...props\r\n}: React.ComponentProps<\"div\"> & { asChild?: boolean }) {\r\n  const Comp = asChild ? Slot : \"div\"\r\n\r\n  return (\r\n    <Comp\r\n      data-slot=\"sidebar-group-label\"\r\n      data-sidebar=\"group-label\"\r\n      className={cn(\r\n        \"text-sidebar-foreground/70 ring-sidebar-ring flex h-8 shrink-0 items-center rounded-md px-2 text-xs font-medium outline-hidden transition-[margin,opacity] duration-200 ease-linear focus-visible:ring-2 [&>svg]:size-4 [&>svg]:shrink-0\",\r\n        \"group-data-[collapsible=icon]:-mt-8 group-data-[collapsible=icon]:opacity-0\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction SidebarGroupAction({\r\n  className,\r\n  asChild = false,\r\n  ...props\r\n}: React.ComponentProps<\"button\"> & { asChild?: boolean }) {\r\n  const Comp = asChild ? Slot : \"button\"\r\n\r\n  return (\r\n    <Comp\r\n      data-slot=\"sidebar-group-action\"\r\n      data-sidebar=\"group-action\"\r\n      className={cn(\r\n        \"text-sidebar-foreground ring-sidebar-ring hover:bg-sidebar-accent hover:text-sidebar-accent-foreground absolute top-3.5 right-3 flex aspect-square w-5 items-center justify-center rounded-md p-0 outline-hidden transition-transform focus-visible:ring-2 [&>svg]:size-4 [&>svg]:shrink-0\",\r\n        // Increases the hit area of the button on mobile.\r\n        \"after:absolute after:-inset-2 md:after:hidden\",\r\n        \"group-data-[collapsible=icon]:hidden\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction SidebarGroupContent({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"sidebar-group-content\"\r\n      data-sidebar=\"group-content\"\r\n      className={cn(\"w-full text-sm\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction SidebarMenu({ className, ...props }: React.ComponentProps<\"ul\">) {\r\n  return (\r\n    <ul\r\n      data-slot=\"sidebar-menu\"\r\n      data-sidebar=\"menu\"\r\n      className={cn(\"flex w-full min-w-0 flex-col gap-1\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction SidebarMenuItem({ className, ...props }: React.ComponentProps<\"li\">) {\r\n  return (\r\n    <li\r\n      data-slot=\"sidebar-menu-item\"\r\n      data-sidebar=\"menu-item\"\r\n      className={cn(\"group/menu-item relative\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nconst sidebarMenuButtonVariants = cva(\r\n  \"peer/menu-button flex w-full items-center gap-2 overflow-hidden rounded-md p-2 text-left text-sm outline-hidden ring-sidebar-ring transition-[width,height,padding] hover:bg-sidebar-accent hover:text-sidebar-accent-foreground focus-visible:ring-2 active:bg-sidebar-accent active:text-sidebar-accent-foreground disabled:pointer-events-none disabled:opacity-50 group-has-data-[sidebar=menu-action]/menu-item:pr-8 aria-disabled:pointer-events-none aria-disabled:opacity-50 data-[active=true]:bg-sidebar-accent data-[active=true]:font-medium data-[active=true]:text-sidebar-accent-foreground data-[state=open]:hover:bg-sidebar-accent data-[state=open]:hover:text-sidebar-accent-foreground group-data-[collapsible=icon]:size-8! group-data-[collapsible=icon]:p-2! [&>span:last-child]:truncate [&>svg]:size-4 [&>svg]:shrink-0\",\r\n  {\r\n    variants: {\r\n      variant: {\r\n        default: \"hover:bg-sidebar-accent hover:text-sidebar-accent-foreground\",\r\n        outline:\r\n          \"bg-background shadow-[0_0_0_1px_hsl(var(--sidebar-border))] hover:bg-sidebar-accent hover:text-sidebar-accent-foreground hover:shadow-[0_0_0_1px_hsl(var(--sidebar-accent))]\",\r\n      },\r\n      size: {\r\n        default: \"h-8 text-sm\",\r\n        sm: \"h-7 text-xs\",\r\n        lg: \"h-12 text-sm group-data-[collapsible=icon]:p-0!\",\r\n      },\r\n    },\r\n    defaultVariants: {\r\n      variant: \"default\",\r\n      size: \"default\",\r\n    },\r\n  }\r\n)\r\n\r\nfunction SidebarMenuButton({\r\n  asChild = false,\r\n  isActive = false,\r\n  variant = \"default\",\r\n  size = \"default\",\r\n  tooltip,\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<\"button\"> & {\r\n  asChild?: boolean\r\n  isActive?: boolean\r\n  tooltip?: string | React.ComponentProps<typeof TooltipContent>\r\n} & VariantProps<typeof sidebarMenuButtonVariants>) {\r\n  const Comp = asChild ? Slot : \"button\"\r\n  const { isMobile, state } = useSidebar()\r\n\r\n  const button = (\r\n    <Comp\r\n      data-slot=\"sidebar-menu-button\"\r\n      data-sidebar=\"menu-button\"\r\n      data-size={size}\r\n      data-active={isActive}\r\n      className={cn(sidebarMenuButtonVariants({ variant, size }), className)}\r\n      {...props}\r\n    />\r\n  )\r\n\r\n  if (!tooltip) {\r\n    return button\r\n  }\r\n\r\n  if (typeof tooltip === \"string\") {\r\n    tooltip = {\r\n      children: tooltip,\r\n    }\r\n  }\r\n\r\n  return (\r\n    <Tooltip>\r\n      <TooltipTrigger asChild>{button}</TooltipTrigger>\r\n      <TooltipContent\r\n        side=\"right\"\r\n        align=\"center\"\r\n        hidden={state !== \"collapsed\" || isMobile}\r\n        {...tooltip}\r\n      />\r\n    </Tooltip>\r\n  )\r\n}\r\n\r\nfunction SidebarMenuAction({\r\n  className,\r\n  asChild = false,\r\n  showOnHover = false,\r\n  ...props\r\n}: React.ComponentProps<\"button\"> & {\r\n  asChild?: boolean\r\n  showOnHover?: boolean\r\n}) {\r\n  const Comp = asChild ? Slot : \"button\"\r\n\r\n  return (\r\n    <Comp\r\n      data-slot=\"sidebar-menu-action\"\r\n      data-sidebar=\"menu-action\"\r\n      className={cn(\r\n        \"text-sidebar-foreground ring-sidebar-ring hover:bg-sidebar-accent hover:text-sidebar-accent-foreground peer-hover/menu-button:text-sidebar-accent-foreground absolute top-1.5 right-1 flex aspect-square w-5 items-center justify-center rounded-md p-0 outline-hidden transition-transform focus-visible:ring-2 [&>svg]:size-4 [&>svg]:shrink-0\",\r\n        // Increases the hit area of the button on mobile.\r\n        \"after:absolute after:-inset-2 md:after:hidden\",\r\n        \"peer-data-[size=sm]/menu-button:top-1\",\r\n        \"peer-data-[size=default]/menu-button:top-1.5\",\r\n        \"peer-data-[size=lg]/menu-button:top-2.5\",\r\n        \"group-data-[collapsible=icon]:hidden\",\r\n        showOnHover &&\r\n          \"peer-data-[active=true]/menu-button:text-sidebar-accent-foreground group-focus-within/menu-item:opacity-100 group-hover/menu-item:opacity-100 data-[state=open]:opacity-100 md:opacity-0\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction SidebarMenuBadge({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"sidebar-menu-badge\"\r\n      data-sidebar=\"menu-badge\"\r\n      className={cn(\r\n        \"text-sidebar-foreground pointer-events-none absolute right-1 flex h-5 min-w-5 items-center justify-center rounded-md px-1 text-xs font-medium tabular-nums select-none\",\r\n        \"peer-hover/menu-button:text-sidebar-accent-foreground peer-data-[active=true]/menu-button:text-sidebar-accent-foreground\",\r\n        \"peer-data-[size=sm]/menu-button:top-1\",\r\n        \"peer-data-[size=default]/menu-button:top-1.5\",\r\n        \"peer-data-[size=lg]/menu-button:top-2.5\",\r\n        \"group-data-[collapsible=icon]:hidden\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction SidebarMenuSkeleton({\r\n  className,\r\n  showIcon = false,\r\n  ...props\r\n}: React.ComponentProps<\"div\"> & {\r\n  showIcon?: boolean\r\n}) {\r\n  // Random width between 50 to 90%.\r\n  const width = React.useMemo(() => {\r\n    return `${Math.floor(Math.random() * 40) + 50}%`\r\n  }, [])\r\n\r\n  return (\r\n    <div\r\n      data-slot=\"sidebar-menu-skeleton\"\r\n      data-sidebar=\"menu-skeleton\"\r\n      className={cn(\"flex h-8 items-center gap-2 rounded-md px-2\", className)}\r\n      {...props}\r\n    >\r\n      {showIcon && (\r\n        <Skeleton\r\n          className=\"size-4 rounded-md\"\r\n          data-sidebar=\"menu-skeleton-icon\"\r\n        />\r\n      )}\r\n      <Skeleton\r\n        className=\"h-4 max-w-(--skeleton-width) flex-1\"\r\n        data-sidebar=\"menu-skeleton-text\"\r\n        style={\r\n          {\r\n            \"--skeleton-width\": width,\r\n          } as React.CSSProperties\r\n        }\r\n      />\r\n    </div>\r\n  )\r\n}\r\n\r\nfunction SidebarMenuSub({ className, ...props }: React.ComponentProps<\"ul\">) {\r\n  return (\r\n    <ul\r\n      data-slot=\"sidebar-menu-sub\"\r\n      data-sidebar=\"menu-sub\"\r\n      className={cn(\r\n        \"border-sidebar-border mx-3.5 flex min-w-0 translate-x-px flex-col gap-1 border-l px-2.5 py-0.5\",\r\n        \"group-data-[collapsible=icon]:hidden\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction SidebarMenuSubItem({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<\"li\">) {\r\n  return (\r\n    <li\r\n      data-slot=\"sidebar-menu-sub-item\"\r\n      data-sidebar=\"menu-sub-item\"\r\n      className={cn(\"group/menu-sub-item relative\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction SidebarMenuSubButton({\r\n  asChild = false,\r\n  size = \"md\",\r\n  isActive = false,\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<\"a\"> & {\r\n  asChild?: boolean\r\n  size?: \"sm\" | \"md\"\r\n  isActive?: boolean\r\n}) {\r\n  const Comp = asChild ? Slot : \"a\"\r\n\r\n  return (\r\n    <Comp\r\n      data-slot=\"sidebar-menu-sub-button\"\r\n      data-sidebar=\"menu-sub-button\"\r\n      data-size={size}\r\n      data-active={isActive}\r\n      className={cn(\r\n        \"text-sidebar-foreground ring-sidebar-ring hover:bg-sidebar-accent hover:text-sidebar-accent-foreground active:bg-sidebar-accent active:text-sidebar-accent-foreground [&>svg]:text-sidebar-accent-foreground flex h-7 min-w-0 -translate-x-px items-center gap-2 overflow-hidden rounded-md px-2 outline-hidden focus-visible:ring-2 disabled:pointer-events-none disabled:opacity-50 aria-disabled:pointer-events-none aria-disabled:opacity-50 [&>span:last-child]:truncate [&>svg]:size-4 [&>svg]:shrink-0\",\r\n        \"data-[active=true]:bg-sidebar-accent data-[active=true]:text-sidebar-accent-foreground\",\r\n        size === \"sm\" && \"text-xs\",\r\n        size === \"md\" && \"text-sm\",\r\n        \"group-data-[collapsible=icon]:hidden\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport {\r\n  Sidebar,\r\n  SidebarContent,\r\n  SidebarFooter,\r\n  SidebarGroup,\r\n  SidebarGroupAction,\r\n  SidebarGroupContent,\r\n  SidebarGroupLabel,\r\n  SidebarHeader,\r\n  SidebarInput,\r\n  SidebarInset,\r\n  SidebarMenu,\r\n  SidebarMenuAction,\r\n  SidebarMenuBadge,\r\n  SidebarMenuButton,\r\n  SidebarMenuItem,\r\n  SidebarMenuSkeleton,\r\n  SidebarMenuSub,\r\n  SidebarMenuSubButton,\r\n  SidebarMenuSubItem,\r\n  SidebarProvider,\r\n  SidebarRail,\r\n  SidebarSeparator,\r\n  SidebarTrigger,\r\n  useSidebar,\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;AAEA;AACA;AACA;AACA;AAEA;AACA;AAAA;AACA;AACA;AACA;AACA;AAOA;AACA;;;AApBA;;;;;;;;;;;;;AA2BA,MAAM,sBAAsB;AAC5B,MAAM,yBAAyB,KAAK,KAAK,KAAK;AAC9C,MAAM,gBAAgB;AACtB,MAAM,uBAAuB;AAC7B,MAAM,qBAAqB;AAC3B,MAAM,4BAA4B;AAYlC,MAAM,+BAAiB,CAAA,GAAA,sQAAA,CAAA,gBAAmB,AAAD,EAA8B;AAEvE,SAAS;;IACP,MAAM,UAAU,CAAA,GAAA,sQAAA,CAAA,aAAgB,AAAD,EAAE;IACjC,IAAI,CAAC,SAAS;QACZ,MAAM,IAAI,MAAM;IAClB;IAEA,OAAO;AACT;GAPS;AAST,SAAS,gBAAgB,EACvB,cAAc,IAAI,EAClB,MAAM,QAAQ,EACd,cAAc,WAAW,EACzB,SAAS,EACT,KAAK,EACL,QAAQ,EACR,GAAG,OAKJ;;IACC,MAAM,WAAW,CAAA,GAAA,yHAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,sQAAA,CAAA,WAAc,AAAD,EAAE;IAEnD,6CAA6C;IAC7C,0EAA0E;IAC1E,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,sQAAA,CAAA,WAAc,AAAD,EAAE;IACzC,MAAM,OAAO,YAAY;IACzB,MAAM,UAAU,CAAA,GAAA,sQAAA,CAAA,cAAiB,AAAD;gDAC9B,CAAC;YACC,MAAM,YAAY,OAAO,UAAU,aAAa,MAAM,QAAQ;YAC9D,IAAI,aAAa;gBACf,YAAY;YACd,OAAO;gBACL,SAAS;YACX;YAEA,kDAAkD;YAClD,SAAS,MAAM,GAAG,GAAG,oBAAoB,CAAC,EAAE,UAAU,kBAAkB,EAAE,wBAAwB;QACpG;+CACA;QAAC;QAAa;KAAK;IAGrB,gCAAgC;IAChC,MAAM,gBAAgB,CAAA,GAAA,sQAAA,CAAA,cAAiB,AAAD;sDAAE;YACtC,OAAO,WAAW;8DAAc,CAAC,OAAS,CAAC;+DAAQ;8DAAQ,CAAC,OAAS,CAAC;;QACxE;qDAAG;QAAC;QAAU;QAAS;KAAc;IAErC,kDAAkD;IAClD,CAAA,GAAA,sQAAA,CAAA,YAAe,AAAD;qCAAE;YACd,MAAM;2DAAgB,CAAC;oBACrB,IACE,MAAM,GAAG,KAAK,6BACd,CAAC,MAAM,OAAO,IAAI,MAAM,OAAO,GAC/B;wBACA,MAAM,cAAc;wBACpB;oBACF;gBACF;;YAEA,OAAO,gBAAgB,CAAC,WAAW;YACnC;6CAAO,IAAM,OAAO,mBAAmB,CAAC,WAAW;;QACrD;oCAAG;QAAC;KAAc;IAElB,yEAAyE;IACzE,mEAAmE;IACnE,MAAM,QAAQ,OAAO,aAAa;IAElC,MAAM,eAAe,CAAA,GAAA,sQAAA,CAAA,UAAa,AAAD;iDAC/B,IAAM,CAAC;gBACL;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;YACF,CAAC;gDACD;QAAC;QAAO;QAAM;QAAS;QAAU;QAAY;QAAe;KAAc;IAG5E,qBACE,sSAAC,eAAe,QAAQ;QAAC,OAAO;kBAC9B,cAAA,sSAAC,+HAAA,CAAA,kBAAe;YAAC,eAAe;sBAC9B,cAAA,sSAAC;gBACC,aAAU;gBACV,OACE;oBACE,mBAAmB;oBACnB,wBAAwB;oBACxB,GAAG,KAAK;gBACV;gBAEF,WAAW,CAAA,GAAA,+HAAA,CAAA,KAAE,AAAD,EACV,mFACA;gBAED,GAAG,KAAK;0BAER;;;;;;;;;;;;;;;;AAKX;IAhGS;;QAaU,yHAAA,CAAA,cAAW;;;KAbrB;AAkGT,SAAS,QAAQ,EACf,OAAO,MAAM,EACb,UAAU,SAAS,EACnB,cAAc,WAAW,EACzB,SAAS,EACT,QAAQ,EACR,GAAG,OAKJ;;IACC,MAAM,EAAE,QAAQ,EAAE,KAAK,EAAE,UAAU,EAAE,aAAa,EAAE,GAAG;IAEvD,IAAI,gBAAgB,QAAQ;QAC1B,qBACE,sSAAC;YACC,aAAU;YACV,WAAW,CAAA,GAAA,+HAAA,CAAA,KAAE,AAAD,EACV,+EACA;YAED,GAAG,KAAK;sBAER;;;;;;IAGP;IAEA,IAAI,UAAU;QACZ,qBACE,sSAAC,6HAAA,CAAA,QAAK;YAAC,MAAM;YAAY,cAAc;YAAgB,GAAG,KAAK;sBAC7D,cAAA,sSAAC,6HAAA,CAAA,eAAY;gBACX,gBAAa;gBACb,aAAU;gBACV,eAAY;gBACZ,WAAU;gBACV,OACE;oBACE,mBAAmB;gBACrB;gBAEF,MAAM;;kCAEN,sSAAC,6HAAA,CAAA,cAAW;wBAAC,WAAU;;0CACrB,sSAAC,6HAAA,CAAA,aAAU;0CAAC;;;;;;0CACZ,sSAAC,6HAAA,CAAA,mBAAgB;0CAAC;;;;;;;;;;;;kCAEpB,sSAAC;wBAAI,WAAU;kCAA+B;;;;;;;;;;;;;;;;;IAItD;IAEA,qBACE,sSAAC;QACC,WAAU;QACV,cAAY;QACZ,oBAAkB,UAAU,cAAc,cAAc;QACxD,gBAAc;QACd,aAAW;QACX,aAAU;;0BAGV,sSAAC;gBACC,aAAU;gBACV,WAAW,CAAA,GAAA,+HAAA,CAAA,KAAE,AAAD,EACV,2FACA,0CACA,sCACA,YAAY,cAAc,YAAY,UAClC,qFACA;;;;;;0BAGR,sSAAC;gBACC,aAAU;gBACV,WAAW,CAAA,GAAA,+HAAA,CAAA,KAAE,AAAD,EACV,wHACA,SAAS,SACL,mFACA,oFACJ,sDAAsD;gBACtD,YAAY,cAAc,YAAY,UAClC,6FACA,2HACJ;gBAED,GAAG,KAAK;0BAET,cAAA,sSAAC;oBACC,gBAAa;oBACb,aAAU;oBACV,WAAU;8BAET;;;;;;;;;;;;;;;;;AAKX;IApGS;;QAYgD;;;MAZhD;AAsGT,SAAS,eAAe,EACtB,SAAS,EACT,OAAO,EACP,GAAG,OACiC;;IACpC,MAAM,EAAE,aAAa,EAAE,GAAG;IAE1B,qBACE,sSAAC,8HAAA,CAAA,SAAM;QACL,gBAAa;QACb,aAAU;QACV,SAAQ;QACR,MAAK;QACL,WAAW,CAAA,GAAA,+HAAA,CAAA,KAAE,AAAD,EAAE,UAAU;QACxB,SAAS,CAAC;YACR,UAAU;YACV;QACF;QACC,GAAG,KAAK;;0BAET,sSAAC,2SAAA,CAAA,gBAAa;;;;;0BACd,sSAAC;gBAAK,WAAU;0BAAU;;;;;;;;;;;;AAGhC;IAxBS;;QAKmB;;;MALnB;AA0BT,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAuC;;IAC1E,MAAM,EAAE,aAAa,EAAE,GAAG;IAE1B,qBACE,sSAAC;QACC,gBAAa;QACb,aAAU;QACV,cAAW;QACX,UAAU,CAAC;QACX,SAAS;QACT,OAAM;QACN,WAAW,CAAA,GAAA,+HAAA,CAAA,KAAE,AAAD,EACV,mPACA,4EACA,0HACA,2JACA,6DACA,6DACA;QAED,GAAG,KAAK;;;;;;AAGf;IAvBS;;QACmB;;;MADnB;AAyBT,SAAS,aAAa,EAAE,SAAS,EAAE,GAAG,OAAqC;IACzE,qBACE,sSAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,+HAAA,CAAA,KAAE,AAAD,EACV,sDACA,mNACA;QAED,GAAG,KAAK;;;;;;AAGf;MAZS;AAcT,SAAS,aAAa,EACpB,SAAS,EACT,GAAG,OACgC;IACnC,qBACE,sSAAC,6HAAA,CAAA,QAAK;QACJ,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,+HAAA,CAAA,KAAE,AAAD,EAAE,wCAAwC;QACrD,GAAG,KAAK;;;;;;AAGf;MAZS;AAcT,SAAS,cAAc,EAAE,SAAS,EAAE,GAAG,OAAoC;IACzE,qBACE,sSAAC;QACC,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,+HAAA,CAAA,KAAE,AAAD,EAAE,2BAA2B;QACxC,GAAG,KAAK;;;;;;AAGf;MATS;AAWT,SAAS,cAAc,EAAE,SAAS,EAAE,GAAG,OAAoC;IACzE,qBACE,sSAAC;QACC,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,+HAAA,CAAA,KAAE,AAAD,EAAE,2BAA2B;QACxC,GAAG,KAAK;;;;;;AAGf;MATS;AAWT,SAAS,iBAAiB,EACxB,SAAS,EACT,GAAG,OACoC;IACvC,qBACE,sSAAC,iIAAA,CAAA,YAAS;QACR,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,+HAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;MAZS;AAcT,SAAS,eAAe,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC1E,qBACE,sSAAC;QACC,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,+HAAA,CAAA,KAAE,AAAD,EACV,kGACA;QAED,GAAG,KAAK;;;;;;AAGf;MAZS;AAcT,SAAS,aAAa,EAAE,SAAS,EAAE,GAAG,OAAoC;IACxE,qBACE,sSAAC;QACC,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,+HAAA,CAAA,KAAE,AAAD,EAAE,6CAA6C;QAC1D,GAAG,KAAK;;;;;;AAGf;OATS;AAWT,SAAS,kBAAkB,EACzB,SAAS,EACT,UAAU,KAAK,EACf,GAAG,OACiD;IACpD,MAAM,OAAO,UAAU,uSAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,sSAAC;QACC,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,+HAAA,CAAA,KAAE,AAAD,EACV,4OACA,+EACA;QAED,GAAG,KAAK;;;;;;AAGf;OAnBS;AAqBT,SAAS,mBAAmB,EAC1B,SAAS,EACT,UAAU,KAAK,EACf,GAAG,OACoD;IACvD,MAAM,OAAO,UAAU,uSAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,sSAAC;QACC,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,+HAAA,CAAA,KAAE,AAAD,EACV,8RACA,kDAAkD;QAClD,iDACA,wCACA;QAED,GAAG,KAAK;;;;;;AAGf;OArBS;AAuBT,SAAS,oBAAoB,EAC3B,SAAS,EACT,GAAG,OACyB;IAC5B,qBACE,sSAAC;QACC,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,+HAAA,CAAA,KAAE,AAAD,EAAE,kBAAkB;QAC/B,GAAG,KAAK;;;;;;AAGf;OAZS;AAcT,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAmC;IACtE,qBACE,sSAAC;QACC,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,+HAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QACnD,GAAG,KAAK;;;;;;AAGf;OATS;AAWT,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAmC;IAC1E,qBACE,sSAAC;QACC,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,+HAAA,CAAA,KAAE,AAAD,EAAE,4BAA4B;QACzC,GAAG,KAAK;;;;;;AAGf;OATS;AAWT,MAAM,4BAA4B,CAAA,GAAA,8OAAA,CAAA,MAAG,AAAD,EAClC,qzBACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,SACE;QACJ;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;QACN;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,SAAS,kBAAkB,EACzB,UAAU,KAAK,EACf,WAAW,KAAK,EAChB,UAAU,SAAS,EACnB,OAAO,SAAS,EAChB,OAAO,EACP,SAAS,EACT,GAAG,OAK6C;;IAChD,MAAM,OAAO,UAAU,uSAAA,CAAA,OAAI,GAAG;IAC9B,MAAM,EAAE,QAAQ,EAAE,KAAK,EAAE,GAAG;IAE5B,MAAM,uBACJ,sSAAC;QACC,aAAU;QACV,gBAAa;QACb,aAAW;QACX,eAAa;QACb,WAAW,CAAA,GAAA,+HAAA,CAAA,KAAE,AAAD,EAAE,0BAA0B;YAAE;YAAS;QAAK,IAAI;QAC3D,GAAG,KAAK;;;;;;IAIb,IAAI,CAAC,SAAS;QACZ,OAAO;IACT;IAEA,IAAI,OAAO,YAAY,UAAU;QAC/B,UAAU;YACR,UAAU;QACZ;IACF;IAEA,qBACE,sSAAC,+HAAA,CAAA,UAAO;;0BACN,sSAAC,+HAAA,CAAA,iBAAc;gBAAC,OAAO;0BAAE;;;;;;0BACzB,sSAAC,+HAAA,CAAA,iBAAc;gBACb,MAAK;gBACL,OAAM;gBACN,QAAQ,UAAU,eAAe;gBAChC,GAAG,OAAO;;;;;;;;;;;;AAInB;IAhDS;;QAcqB;;;OAdrB;AAkDT,SAAS,kBAAkB,EACzB,SAAS,EACT,UAAU,KAAK,EACf,cAAc,KAAK,EACnB,GAAG,OAIJ;IACC,MAAM,OAAO,UAAU,uSAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,sSAAC;QACC,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,+HAAA,CAAA,KAAE,AAAD,EACV,oVACA,kDAAkD;QAClD,iDACA,yCACA,gDACA,2CACA,wCACA,eACE,4LACF;QAED,GAAG,KAAK;;;;;;AAGf;OA9BS;AAgCT,SAAS,iBAAiB,EACxB,SAAS,EACT,GAAG,OACyB;IAC5B,qBACE,sSAAC;QACC,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,+HAAA,CAAA,KAAE,AAAD,EACV,0KACA,4HACA,yCACA,gDACA,2CACA,wCACA;QAED,GAAG,KAAK;;;;;;AAGf;OApBS;AAsBT,SAAS,oBAAoB,EAC3B,SAAS,EACT,WAAW,KAAK,EAChB,GAAG,OAGJ;;IACC,kCAAkC;IAClC,MAAM,QAAQ,CAAA,GAAA,sQAAA,CAAA,UAAa,AAAD;8CAAE;YAC1B,OAAO,GAAG,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,MAAM,GAAG,CAAC,CAAC;QAClD;6CAAG,EAAE;IAEL,qBACE,sSAAC;QACC,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,+HAAA,CAAA,KAAE,AAAD,EAAE,+CAA+C;QAC5D,GAAG,KAAK;;YAER,0BACC,sSAAC,gIAAA,CAAA,WAAQ;gBACP,WAAU;gBACV,gBAAa;;;;;;0BAGjB,sSAAC,gIAAA,CAAA,WAAQ;gBACP,WAAU;gBACV,gBAAa;gBACb,OACE;oBACE,oBAAoB;gBACtB;;;;;;;;;;;;AAKV;IApCS;OAAA;AAsCT,SAAS,eAAe,EAAE,SAAS,EAAE,GAAG,OAAmC;IACzE,qBACE,sSAAC;QACC,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,+HAAA,CAAA,KAAE,AAAD,EACV,kGACA,wCACA;QAED,GAAG,KAAK;;;;;;AAGf;OAbS;AAeT,SAAS,mBAAmB,EAC1B,SAAS,EACT,GAAG,OACwB;IAC3B,qBACE,sSAAC;QACC,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,+HAAA,CAAA,KAAE,AAAD,EAAE,gCAAgC;QAC7C,GAAG,KAAK;;;;;;AAGf;OAZS;AAcT,SAAS,qBAAqB,EAC5B,UAAU,KAAK,EACf,OAAO,IAAI,EACX,WAAW,KAAK,EAChB,SAAS,EACT,GAAG,OAKJ;IACC,MAAM,OAAO,UAAU,uSAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,sSAAC;QACC,aAAU;QACV,gBAAa;QACb,aAAW;QACX,eAAa;QACb,WAAW,CAAA,GAAA,+HAAA,CAAA,KAAE,AAAD,EACV,ifACA,0FACA,SAAS,QAAQ,WACjB,SAAS,QAAQ,WACjB,wCACA;QAED,GAAG,KAAK;;;;;;AAGf;OA9BS", "debugId": null}}, {"offset": {"line": 1258, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/bloomgoo/gold-exchange-platform/frontend/components/nav-main.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport Link from \"next/link\"\r\n\r\nimport {\r\n  SidebarGroup,\r\n  SidebarGroupContent,\r\n  SidebarMenu,\r\n  SidebarMenuButton,\r\n  SidebarMenuItem,\r\n} from \"@/components/ui/sidebar\"\r\n\r\nexport function NavMain({\r\n  items,\r\n}: {\r\n  items: any[]\r\n}) {\r\n\r\n  return (\r\n    <SidebarGroup>\r\n      <SidebarGroupContent className=\"flex flex-col gap-2\">\r\n        <SidebarMenu>\r\n          {items.map((item, index) => {\r\n            if ('type' in item && item.type === 'separator') {\r\n              return (\r\n                <SidebarMenuItem key={`separator-${index}`} className=\"mt-2 mb-1\">\r\n                  {/* Separator khi expanded - hiển thị text đầy đủ */}\r\n                  <div className=\"group-data-[collapsible=icon]:hidden\">\r\n                    <p className=\"px-2 text-xs font-semibold text-muted-foreground uppercase tracking-wider\">\r\n                      {item.title}\r\n                    </p>\r\n                  </div>\r\n\r\n                  {/* <PERSON>arator khi collapsed - hiển thị divider với tooltip */}\r\n                  <div className=\"hidden group-data-[collapsible=icon]:block\">\r\n                    <SidebarMenuButton\r\n                      tooltip={{\r\n                        children: item.title,\r\n                        side: \"right\",\r\n                        align: \"center\"\r\n                      }}\r\n                      className=\"h-6 justify-center cursor-default hover:bg-transparent focus:bg-transparent active:bg-transparent pointer-events-auto\"\r\n                      onClick={(e) => e.preventDefault()}\r\n                    >\r\n                      <div className=\"w-4 h-px bg-sidebar-border pointer-events-none\" />\r\n                    </SidebarMenuButton>\r\n                  </div>\r\n                </SidebarMenuItem>\r\n              );\r\n            }\r\n\r\n            // Tạo key unique bằng cách kết hợp key có sẵn, url và index\r\n            const uniqueKey = item.key || item.url || `${item.title}-${index}`;\r\n\r\n            return (\r\n              <SidebarMenuItem key={uniqueKey}>\r\n                <SidebarMenuButton asChild tooltip={item.title}>\r\n                  <Link href={item.url}>\r\n                    {item.icon && <item.icon />}\r\n                    <span>{item.title}</span>\r\n                  </Link>\r\n                </SidebarMenuButton>\r\n              </SidebarMenuItem>\r\n            );\r\n          })}\r\n        </SidebarMenu>\r\n      </SidebarGroupContent>\r\n    </SidebarGroup>\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AAEA;AAJA;;;;AAYO,SAAS,QAAQ,EACtB,KAAK,EAGN;IAEC,qBACE,sSAAC,+HAAA,CAAA,eAAY;kBACX,cAAA,sSAAC,+HAAA,CAAA,sBAAmB;YAAC,WAAU;sBAC7B,cAAA,sSAAC,+HAAA,CAAA,cAAW;0BACT,MAAM,GAAG,CAAC,CAAC,MAAM;oBAChB,IAAI,UAAU,QAAQ,KAAK,IAAI,KAAK,aAAa;wBAC/C,qBACE,sSAAC,+HAAA,CAAA,kBAAe;4BAA4B,WAAU;;8CAEpD,sSAAC;oCAAI,WAAU;8CACb,cAAA,sSAAC;wCAAE,WAAU;kDACV,KAAK,KAAK;;;;;;;;;;;8CAKf,sSAAC;oCAAI,WAAU;8CACb,cAAA,sSAAC,+HAAA,CAAA,oBAAiB;wCAChB,SAAS;4CACP,UAAU,KAAK,KAAK;4CACpB,MAAM;4CACN,OAAO;wCACT;wCACA,WAAU;wCACV,SAAS,CAAC,IAAM,EAAE,cAAc;kDAEhC,cAAA,sSAAC;4CAAI,WAAU;;;;;;;;;;;;;;;;;2BAnBC,CAAC,UAAU,EAAE,OAAO;;;;;oBAwB9C;oBAEA,4DAA4D;oBAC5D,MAAM,YAAY,KAAK,GAAG,IAAI,KAAK,GAAG,IAAI,GAAG,KAAK,KAAK,CAAC,CAAC,EAAE,OAAO;oBAElE,qBACE,sSAAC,+HAAA,CAAA,kBAAe;kCACd,cAAA,sSAAC,+HAAA,CAAA,oBAAiB;4BAAC,OAAO;4BAAC,SAAS,KAAK,KAAK;sCAC5C,cAAA,sSAAC,wQAAA,CAAA,UAAI;gCAAC,MAAM,KAAK,GAAG;;oCACjB,KAAK,IAAI,kBAAI,sSAAC,KAAK,IAAI;;;;;kDACxB,sSAAC;kDAAM,KAAK,KAAK;;;;;;;;;;;;;;;;;uBAJD;;;;;gBAS1B;;;;;;;;;;;;;;;;AAKV;KAzDgB", "debugId": null}}, {"offset": {"line": 1393, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/bloomgoo/gold-exchange-platform/frontend/components/nav-secondary.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport { type Icon } from \"@tabler/icons-react\"\r\n\r\nimport {\r\n  SidebarGroup,\r\n  SidebarGroupContent,\r\n  SidebarMenu,\r\n  SidebarMenuButton,\r\n  SidebarMenuItem,\r\n} from \"@/components/ui/sidebar\"\r\n\r\nexport function NavSecondary({\r\n  items,\r\n  ...props\r\n}: {\r\n  items: {\r\n    title: string\r\n    url: string\r\n    icon: Icon\r\n  }[]\r\n} & React.ComponentPropsWithoutRef<typeof SidebarGroup>) {\r\n  return (\r\n    <SidebarGroup {...props}>\r\n      <SidebarGroupContent>\r\n        <SidebarMenu>\r\n          {items.map((item) => (\r\n            <SidebarMenuItem key={item.title}>\r\n              <SidebarMenuButton asChild>\r\n                <a href={item.url}>\r\n                  <item.icon />\r\n                  <span>{item.title}</span>\r\n                </a>\r\n              </SidebarMenuButton>\r\n            </SidebarMenuItem>\r\n          ))}\r\n        </SidebarMenu>\r\n      </SidebarGroupContent>\r\n    </SidebarGroup>\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;AAKA;AALA;;;AAaO,SAAS,aAAa,EAC3B,KAAK,EACL,GAAG,OAOkD;IACrD,qBACE,sSAAC,+HAAA,CAAA,eAAY;QAAE,GAAG,KAAK;kBACrB,cAAA,sSAAC,+HAAA,CAAA,sBAAmB;sBAClB,cAAA,sSAAC,+HAAA,CAAA,cAAW;0BACT,MAAM,GAAG,CAAC,CAAC,qBACV,sSAAC,+HAAA,CAAA,kBAAe;kCACd,cAAA,sSAAC,+HAAA,CAAA,oBAAiB;4BAAC,OAAO;sCACxB,cAAA,sSAAC;gCAAE,MAAM,KAAK,GAAG;;kDACf,sSAAC,KAAK,IAAI;;;;;kDACV,sSAAC;kDAAM,KAAK,KAAK;;;;;;;;;;;;;;;;;uBAJD,KAAK,KAAK;;;;;;;;;;;;;;;;;;;;AAa5C;KA5BgB", "debugId": null}}, {"offset": {"line": 1468, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/bloomgoo/gold-exchange-platform/frontend/components/ui/avatar.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as AvatarPrimitive from \"@radix-ui/react-avatar\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction Avatar({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof AvatarPrimitive.Root>) {\r\n  return (\r\n    <AvatarPrimitive.Root\r\n      data-slot=\"avatar\"\r\n      className={cn(\r\n        \"relative flex size-8 shrink-0 overflow-hidden rounded-full\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction AvatarImage({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof AvatarPrimitive.Image>) {\r\n  return (\r\n    <AvatarPrimitive.Image\r\n      data-slot=\"avatar-image\"\r\n      className={cn(\"aspect-square size-full\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction AvatarFallback({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof AvatarPrimitive.Fallback>) {\r\n  return (\r\n    <AvatarPrimitive.Fallback\r\n      data-slot=\"avatar-fallback\"\r\n      className={cn(\r\n        \"bg-muted flex size-full items-center justify-center rounded-full\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport { Avatar, AvatarImage, AvatarFallback }\r\n"], "names": [], "mappings": ";;;;;;AAGA;AAEA;AAAA;AALA;;;;AAOA,SAAS,OAAO,EACd,SAAS,EACT,GAAG,OAC+C;IAClD,qBACE,sSAAC,kRAAA,CAAA,OAAoB;QACnB,aAAU;QACV,WAAW,CAAA,GAAA,+HAAA,CAAA,KAAE,AAAD,EACV,8DACA;QAED,GAAG,KAAK;;;;;;AAGf;KAdS;AAgBT,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,sSAAC,kRAAA,CAAA,QAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,+HAAA,CAAA,KAAE,AAAD,EAAE,2BAA2B;QACxC,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,eAAe,EACtB,SAAS,EACT,GAAG,OACmD;IACtD,qBACE,sSAAC,kRAAA,CAAA,WAAwB;QACvB,aAAU;QACV,WAAW,CAAA,GAAA,+HAAA,CAAA,KAAE,AAAD,EACV,oEACA;QAED,GAAG,KAAK;;;;;;AAGf;MAdS", "debugId": null}}, {"offset": {"line": 1531, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/bloomgoo/gold-exchange-platform/frontend/components/ui/dropdown-menu.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as DropdownMenuPrimitive from \"@radix-ui/react-dropdown-menu\"\r\nimport { CheckIcon, ChevronRightIcon, CircleIcon } from \"lucide-react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction DropdownMenu({\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Root>) {\r\n  return <DropdownMenuPrimitive.Root data-slot=\"dropdown-menu\" {...props} />\r\n}\r\n\r\nfunction DropdownMenuPortal({\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Portal>) {\r\n  return (\r\n    <DropdownMenuPrimitive.Portal data-slot=\"dropdown-menu-portal\" {...props} />\r\n  )\r\n}\r\n\r\nfunction DropdownMenuTrigger({\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Trigger>) {\r\n  return (\r\n    <DropdownMenuPrimitive.Trigger\r\n      data-slot=\"dropdown-menu-trigger\"\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction DropdownMenuContent({\r\n  className,\r\n  sideOffset = 4,\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Content>) {\r\n  return (\r\n    <DropdownMenuPrimitive.Portal>\r\n      <DropdownMenuPrimitive.Content\r\n        data-slot=\"dropdown-menu-content\"\r\n        sideOffset={sideOffset}\r\n        className={cn(\r\n          \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 max-h-(--radix-dropdown-menu-content-available-height) min-w-[8rem] origin-(--radix-dropdown-menu-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border p-1 shadow-md\",\r\n          className\r\n        )}\r\n        {...props}\r\n      />\r\n    </DropdownMenuPrimitive.Portal>\r\n  )\r\n}\r\n\r\nfunction DropdownMenuGroup({\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Group>) {\r\n  return (\r\n    <DropdownMenuPrimitive.Group data-slot=\"dropdown-menu-group\" {...props} />\r\n  )\r\n}\r\n\r\nfunction DropdownMenuItem({\r\n  className,\r\n  inset,\r\n  variant = \"default\",\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Item> & {\r\n  inset?: boolean\r\n  variant?: \"default\" | \"destructive\"\r\n}) {\r\n  return (\r\n    <DropdownMenuPrimitive.Item\r\n      data-slot=\"dropdown-menu-item\"\r\n      data-inset={inset}\r\n      data-variant={variant}\r\n      className={cn(\r\n        \"focus:bg-accent focus:text-accent-foreground data-[variant=destructive]:text-destructive data-[variant=destructive]:focus:bg-destructive/10 dark:data-[variant=destructive]:focus:bg-destructive/20 data-[variant=destructive]:focus:text-destructive data-[variant=destructive]:*:[svg]:!text-destructive [&_svg:not([class*='text-'])]:text-muted-foreground relative flex cursor-default items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 data-[inset]:pl-8 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction DropdownMenuCheckboxItem({\r\n  className,\r\n  children,\r\n  checked,\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.CheckboxItem>) {\r\n  return (\r\n    <DropdownMenuPrimitive.CheckboxItem\r\n      data-slot=\"dropdown-menu-checkbox-item\"\r\n      className={cn(\r\n        \"focus:bg-accent focus:text-accent-foreground relative flex cursor-default items-center gap-2 rounded-sm py-1.5 pr-2 pl-8 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\r\n        className\r\n      )}\r\n      checked={checked}\r\n      {...props}\r\n    >\r\n      <span className=\"pointer-events-none absolute left-2 flex size-3.5 items-center justify-center\">\r\n        <DropdownMenuPrimitive.ItemIndicator>\r\n          <CheckIcon className=\"size-4\" />\r\n        </DropdownMenuPrimitive.ItemIndicator>\r\n      </span>\r\n      {children}\r\n    </DropdownMenuPrimitive.CheckboxItem>\r\n  )\r\n}\r\n\r\nfunction DropdownMenuRadioGroup({\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.RadioGroup>) {\r\n  return (\r\n    <DropdownMenuPrimitive.RadioGroup\r\n      data-slot=\"dropdown-menu-radio-group\"\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction DropdownMenuRadioItem({\r\n  className,\r\n  children,\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.RadioItem>) {\r\n  return (\r\n    <DropdownMenuPrimitive.RadioItem\r\n      data-slot=\"dropdown-menu-radio-item\"\r\n      className={cn(\r\n        \"focus:bg-accent focus:text-accent-foreground relative flex cursor-default items-center gap-2 rounded-sm py-1.5 pr-2 pl-8 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\r\n        className\r\n      )}\r\n      {...props}\r\n    >\r\n      <span className=\"pointer-events-none absolute left-2 flex size-3.5 items-center justify-center\">\r\n        <DropdownMenuPrimitive.ItemIndicator>\r\n          <CircleIcon className=\"size-2 fill-current\" />\r\n        </DropdownMenuPrimitive.ItemIndicator>\r\n      </span>\r\n      {children}\r\n    </DropdownMenuPrimitive.RadioItem>\r\n  )\r\n}\r\n\r\nfunction DropdownMenuLabel({\r\n  className,\r\n  inset,\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Label> & {\r\n  inset?: boolean\r\n}) {\r\n  return (\r\n    <DropdownMenuPrimitive.Label\r\n      data-slot=\"dropdown-menu-label\"\r\n      data-inset={inset}\r\n      className={cn(\r\n        \"px-2 py-1.5 text-sm font-medium data-[inset]:pl-8\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction DropdownMenuSeparator({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Separator>) {\r\n  return (\r\n    <DropdownMenuPrimitive.Separator\r\n      data-slot=\"dropdown-menu-separator\"\r\n      className={cn(\"bg-border -mx-1 my-1 h-px\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction DropdownMenuShortcut({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<\"span\">) {\r\n  return (\r\n    <span\r\n      data-slot=\"dropdown-menu-shortcut\"\r\n      className={cn(\r\n        \"text-muted-foreground ml-auto text-xs tracking-widest\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction DropdownMenuSub({\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Sub>) {\r\n  return <DropdownMenuPrimitive.Sub data-slot=\"dropdown-menu-sub\" {...props} />\r\n}\r\n\r\nfunction DropdownMenuSubTrigger({\r\n  className,\r\n  inset,\r\n  children,\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.SubTrigger> & {\r\n  inset?: boolean\r\n}) {\r\n  return (\r\n    <DropdownMenuPrimitive.SubTrigger\r\n      data-slot=\"dropdown-menu-sub-trigger\"\r\n      data-inset={inset}\r\n      className={cn(\r\n        \"focus:bg-accent focus:text-accent-foreground data-[state=open]:bg-accent data-[state=open]:text-accent-foreground flex cursor-default items-center rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[inset]:pl-8\",\r\n        className\r\n      )}\r\n      {...props}\r\n    >\r\n      {children}\r\n      <ChevronRightIcon className=\"ml-auto size-4\" />\r\n    </DropdownMenuPrimitive.SubTrigger>\r\n  )\r\n}\r\n\r\nfunction DropdownMenuSubContent({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.SubContent>) {\r\n  return (\r\n    <DropdownMenuPrimitive.SubContent\r\n      data-slot=\"dropdown-menu-sub-content\"\r\n      className={cn(\r\n        \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 min-w-[8rem] origin-(--radix-dropdown-menu-content-transform-origin) overflow-hidden rounded-md border p-1 shadow-lg\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport {\r\n  DropdownMenu,\r\n  DropdownMenuPortal,\r\n  DropdownMenuTrigger,\r\n  DropdownMenuContent,\r\n  DropdownMenuGroup,\r\n  DropdownMenuLabel,\r\n  DropdownMenuItem,\r\n  DropdownMenuCheckboxItem,\r\n  DropdownMenuRadioGroup,\r\n  DropdownMenuRadioItem,\r\n  DropdownMenuSeparator,\r\n  DropdownMenuShortcut,\r\n  DropdownMenuSub,\r\n  DropdownMenuSubTrigger,\r\n  DropdownMenuSubContent,\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;AAGA;AACA;AAAA;AAAA;AAEA;AAAA;AANA;;;;;AAQA,SAAS,aAAa,EACpB,GAAG,OACqD;IACxD,qBAAO,sSAAC,sRAAA,CAAA,OAA0B;QAAC,aAAU;QAAiB,GAAG,KAAK;;;;;;AACxE;KAJS;AAMT,SAAS,mBAAmB,EAC1B,GAAG,OACuD;IAC1D,qBACE,sSAAC,sRAAA,CAAA,SAA4B;QAAC,aAAU;QAAwB,GAAG,KAAK;;;;;;AAE5E;MANS;AAQT,SAAS,oBAAoB,EAC3B,GAAG,OACwD;IAC3D,qBACE,sSAAC,sRAAA,CAAA,UAA6B;QAC5B,aAAU;QACT,GAAG,KAAK;;;;;;AAGf;MATS;AAWT,SAAS,oBAAoB,EAC3B,SAAS,EACT,aAAa,CAAC,EACd,GAAG,OACwD;IAC3D,qBACE,sSAAC,sRAAA,CAAA,SAA4B;kBAC3B,cAAA,sSAAC,sRAAA,CAAA,UAA6B;YAC5B,aAAU;YACV,YAAY;YACZ,WAAW,CAAA,GAAA,+HAAA,CAAA,KAAE,AAAD,EACV,0jBACA;YAED,GAAG,KAAK;;;;;;;;;;;AAIjB;MAlBS;AAoBT,SAAS,kBAAkB,EACzB,GAAG,OACsD;IACzD,qBACE,sSAAC,sRAAA,CAAA,QAA2B;QAAC,aAAU;QAAuB,GAAG,KAAK;;;;;;AAE1E;MANS;AAQT,SAAS,iBAAiB,EACxB,SAAS,EACT,KAAK,EACL,UAAU,SAAS,EACnB,GAAG,OAIJ;IACC,qBACE,sSAAC,sRAAA,CAAA,OAA0B;QACzB,aAAU;QACV,cAAY;QACZ,gBAAc;QACd,WAAW,CAAA,GAAA,+HAAA,CAAA,KAAE,AAAD,EACV,+mBACA;QAED,GAAG,KAAK;;;;;;AAGf;MArBS;AAuBT,SAAS,yBAAyB,EAChC,SAAS,EACT,QAAQ,EACR,OAAO,EACP,GAAG,OAC6D;IAChE,qBACE,sSAAC,sRAAA,CAAA,eAAkC;QACjC,aAAU;QACV,WAAW,CAAA,GAAA,+HAAA,CAAA,KAAE,AAAD,EACV,gTACA;QAEF,SAAS;QACR,GAAG,KAAK;;0BAET,sSAAC;gBAAK,WAAU;0BACd,cAAA,sSAAC,sRAAA,CAAA,gBAAmC;8BAClC,cAAA,sSAAC,+RAAA,CAAA,YAAS;wBAAC,WAAU;;;;;;;;;;;;;;;;YAGxB;;;;;;;AAGP;MAxBS;AA0BT,SAAS,uBAAuB,EAC9B,GAAG,OAC2D;IAC9D,qBACE,sSAAC,sRAAA,CAAA,aAAgC;QAC/B,aAAU;QACT,GAAG,KAAK;;;;;;AAGf;MATS;AAWT,SAAS,sBAAsB,EAC7B,SAAS,EACT,QAAQ,EACR,GAAG,OAC0D;IAC7D,qBACE,sSAAC,sRAAA,CAAA,YAA+B;QAC9B,aAAU;QACV,WAAW,CAAA,GAAA,+HAAA,CAAA,KAAE,AAAD,EACV,gTACA;QAED,GAAG,KAAK;;0BAET,sSAAC;gBAAK,WAAU;0BACd,cAAA,sSAAC,sRAAA,CAAA,gBAAmC;8BAClC,cAAA,sSAAC,iSAAA,CAAA,aAAU;wBAAC,WAAU;;;;;;;;;;;;;;;;YAGzB;;;;;;;AAGP;MAtBS;AAwBT,SAAS,kBAAkB,EACzB,SAAS,EACT,KAAK,EACL,GAAG,OAGJ;IACC,qBACE,sSAAC,sRAAA,CAAA,QAA2B;QAC1B,aAAU;QACV,cAAY;QACZ,WAAW,CAAA,GAAA,+HAAA,CAAA,KAAE,AAAD,EACV,qDACA;QAED,GAAG,KAAK;;;;;;AAGf;MAlBS;AAoBT,SAAS,sBAAsB,EAC7B,SAAS,EACT,GAAG,OAC0D;IAC7D,qBACE,sSAAC,sRAAA,CAAA,YAA+B;QAC9B,aAAU;QACV,WAAW,CAAA,GAAA,+HAAA,CAAA,KAAE,AAAD,EAAE,6BAA6B;QAC1C,GAAG,KAAK;;;;;;AAGf;OAXS;AAaT,SAAS,qBAAqB,EAC5B,SAAS,EACT,GAAG,OAC0B;IAC7B,qBACE,sSAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,+HAAA,CAAA,KAAE,AAAD,EACV,yDACA;QAED,GAAG,KAAK;;;;;;AAGf;OAdS;AAgBT,SAAS,gBAAgB,EACvB,GAAG,OACoD;IACvD,qBAAO,sSAAC,sRAAA,CAAA,MAAyB;QAAC,aAAU;QAAqB,GAAG,KAAK;;;;;;AAC3E;OAJS;AAMT,SAAS,uBAAuB,EAC9B,SAAS,EACT,KAAK,EACL,QAAQ,EACR,GAAG,OAGJ;IACC,qBACE,sSAAC,sRAAA,CAAA,aAAgC;QAC/B,aAAU;QACV,cAAY;QACZ,WAAW,CAAA,GAAA,+HAAA,CAAA,KAAE,AAAD,EACV,kOACA;QAED,GAAG,KAAK;;YAER;0BACD,sSAAC,iTAAA,CAAA,mBAAgB;gBAAC,WAAU;;;;;;;;;;;;AAGlC;OAtBS;AAwBT,SAAS,uBAAuB,EAC9B,SAAS,EACT,GAAG,OAC2D;IAC9D,qBACE,sSAAC,sRAAA,CAAA,aAAgC;QAC/B,aAAU;QACV,WAAW,CAAA,GAAA,+HAAA,CAAA,KAAE,AAAD,EACV,ifACA;QAED,GAAG,KAAK;;;;;;AAGf;OAdS", "debugId": null}}, {"offset": {"line": 1828, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/bloomgoo/gold-exchange-platform/frontend/components/nav-user.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport {\r\n  IconCreditCard,\r\n  IconDotsVertical,\r\n  IconLogout,\r\n  IconNotification,\r\n  IconUserCircle,\r\n} from \"@tabler/icons-react\"\r\n\r\nimport {\r\n  Avatar,\r\n  AvatarFallback,\r\n  AvatarImage,\r\n} from \"@/components/ui/avatar\"\r\nimport {\r\n  DropdownMenu,\r\n  DropdownMenuContent,\r\n  DropdownMenuGroup,\r\n  DropdownMenuItem,\r\n  DropdownMenuLabel,\r\n  DropdownMenuSeparator,\r\n  DropdownMenuTrigger,\r\n} from \"@/components/ui/dropdown-menu\"\r\nimport {\r\n  SidebarMenu,\r\n  SidebarMenuButton,\r\n  SidebarMenuItem,\r\n  useSidebar,\r\n} from \"@/components/ui/sidebar\"\r\nimport { useAuth } from \"@/hooks/use-auth\"\r\nimport { toast } from \"sonner\"\r\n\r\nexport function NavUser() {\r\n  const { user, logout } = useAuth();\r\n  // Sử dụng toast trực tiếp từ sonner\r\n  const { isMobile } = useSidebar()\r\n\r\n  // Kiểm tra role của user\r\n  const isAdmin = user?.roles?.includes(\"ADMIN\");\r\n  const isAgent = user?.roles?.includes(\"AGENT\");\r\n\r\n  // N<PERSON>u chưa đăng nhập, hiển thị nút đăng nhập\r\n  if (!user) {\r\n    return (\r\n      <SidebarMenu>\r\n        <SidebarMenuItem>\r\n          <SidebarMenuButton\r\n            size=\"lg\"\r\n            onClick={() => window.location.href = \"/login\"}\r\n          >\r\n            <span>Đăng nhập</span>\r\n          </SidebarMenuButton>\r\n        </SidebarMenuItem>\r\n      </SidebarMenu>\r\n    )\r\n  }\r\n\r\n  return (\r\n    <SidebarMenu>\r\n      <SidebarMenuItem>\r\n        <DropdownMenu>\r\n          <DropdownMenuTrigger asChild>\r\n            <SidebarMenuButton\r\n              size=\"lg\"\r\n              className=\"data-[state=open]:bg-sidebar-accent data-[state=open]:text-sidebar-accent-foreground\"\r\n            >\r\n              <Avatar className=\"h-8 w-8 rounded-lg grayscale\">\r\n                <AvatarImage src={user.avatar || \"/avatars/default.png\"} alt={user.name || \"User\"} />\r\n                <AvatarFallback className=\"rounded-lg\">{user.name ? user.name.charAt(0) : \"U\"}</AvatarFallback>\r\n              </Avatar>\r\n              <div className=\"grid flex-1 text-left text-sm leading-tight\">\r\n                <span className=\"truncate font-medium\">{user.name || \"User\"}</span>\r\n                <span className=\"text-muted-foreground truncate text-xs\">\r\n                  {user.email}\r\n                  {isAdmin && <span className=\"bg-primary/20 text-primary px-1 py-0.5 rounded text-[10px] ml-1\">Admin</span>}\r\n                  {isAgent && <span className=\"bg-orange-100 text-orange-600 px-1 py-0.5 rounded text-[10px] ml-1\">Agent</span>}\r\n                </span>\r\n              </div>\r\n              <IconDotsVertical className=\"ml-auto size-4\" />\r\n            </SidebarMenuButton>\r\n          </DropdownMenuTrigger>\r\n          <DropdownMenuContent\r\n            className=\"w-(--radix-dropdown-menu-trigger-width) min-w-56 rounded-lg\"\r\n            side={isMobile ? \"bottom\" : \"right\"}\r\n            align=\"end\"\r\n            sideOffset={4}\r\n          >\r\n            <DropdownMenuLabel className=\"p-0 font-normal\">\r\n              <div className=\"flex items-center gap-2 px-1 py-1.5 text-left text-sm\">\r\n                <Avatar className=\"h-8 w-8 rounded-lg\">\r\n                  <AvatarImage src={user.avatar || \"/avatars/default.png\"} alt={user.name || \"User\"} />\r\n                  <AvatarFallback className=\"rounded-lg\">{user.name ? user.name.charAt(0) : \"U\"}</AvatarFallback>\r\n                </Avatar>\r\n                <div className=\"grid flex-1 text-left text-sm leading-tight\">\r\n                  <span className=\"truncate font-medium\">{user.name || \"User\"}</span>\r\n                  <span className=\"text-muted-foreground truncate text-xs\">\r\n                    {user.email}\r\n                    {isAdmin && <span className=\"bg-primary/20 text-primary px-1 py-0.5 rounded text-[10px] ml-1\">Admin</span>}\r\n                    {isAgent && <span className=\"bg-orange-100 text-orange-600 px-1 py-0.5 rounded text-[10px] ml-1\">Agent</span>}\r\n                  </span>\r\n                </div>\r\n              </div>\r\n            </DropdownMenuLabel>\r\n            <DropdownMenuSeparator />\r\n            <DropdownMenuGroup>\r\n              {isAdmin ? (\r\n                // Menu cho ADMIN\r\n                <>\r\n                  <DropdownMenuItem onClick={() => window.location.href = \"/admin/dashboard\"}>\r\n                    <IconUserCircle />\r\n                    Dashboard Admin\r\n                  </DropdownMenuItem>\r\n\r\n                  <DropdownMenuSeparator />\r\n                  <DropdownMenuLabel className=\"text-xs font-semibold text-muted-foreground\">QUẢN LÝ NGƯỜI DÙNG</DropdownMenuLabel>\r\n                  <DropdownMenuItem onClick={() => window.location.href = \"/admin/users\"}>\r\n                    <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\" className=\"mr-2\">\r\n                      <path d=\"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2\" />\r\n                      <circle cx=\"9\" cy=\"7\" r=\"4\" />\r\n                      <path d=\"M22 21v-2a4 4 0 0 0-3-3.87\" />\r\n                      <path d=\"M16 3.13a4 4 0 0 1 0 7.75\" />\r\n                    </svg>\r\n                    Quản lý người dùng\r\n                  </DropdownMenuItem>\r\n                  <DropdownMenuItem onClick={() => window.location.href = \"/admin/kyc\"}>\r\n                    <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\" className=\"mr-2\">\r\n                      <path d=\"M16 18a4 4 0 0 0-8 0\" />\r\n                      <circle cx=\"12\" cy=\"10\" r=\"4\" />\r\n                      <rect width=\"20\" height=\"16\" x=\"2\" y=\"4\" rx=\"2\" />\r\n                    </svg>\r\n                    Quản lý KYC\r\n                  </DropdownMenuItem>\r\n\r\n                  <DropdownMenuSeparator />\r\n                  <DropdownMenuLabel className=\"text-xs font-semibold text-muted-foreground\">QUẢN LÝ TÀI CHÍNH</DropdownMenuLabel>\r\n                  <DropdownMenuItem onClick={() => window.location.href = \"/admin/transactions\"}>\r\n                    <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\" className=\"mr-2\">\r\n                      <path d=\"M2 9a2 2 0 0 1 2-2h16a2 2 0 0 1 2 2v9a2 2 0 0 1-2 2H4a2 2 0 0 1-2-2V9Z\" />\r\n                      <path d=\"M2 13h20\" />\r\n                      <path d=\"M9 17h6\" />\r\n                      <path d=\"M4 9V6a2 2 0 0 1 2-2h12a2 2 0 0 1 2 2v3\" />\r\n                    </svg>\r\n                    Quản lý giao dịch\r\n                  </DropdownMenuItem>\r\n                  <DropdownMenuItem onClick={() => window.location.href = \"/admin/contracts\"}>\r\n                    <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\" className=\"mr-2\">\r\n                      <path d=\"M14 3v4a1 1 0 0 0 1 1h4\" />\r\n                      <path d=\"M17 21H7a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h7l5 5v11a2 2 0 0 1-2 2Z\" />\r\n                      <path d=\"M9 9h1\" />\r\n                      <path d=\"M9 13h6\" />\r\n                      <path d=\"M9 17h6\" />\r\n                    </svg>\r\n                    Quản lý hợp đồng\r\n                  </DropdownMenuItem>\r\n                  <DropdownMenuItem onClick={() => window.location.href = \"/admin/agents\"}>\r\n                    <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\" className=\"mr-2\">\r\n                      <path d=\"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2\" />\r\n                      <circle cx=\"9\" cy=\"7\" r=\"4\" />\r\n                      <path d=\"M22 21v-2a4 4 0 0 0-3-3.87\" />\r\n                      <path d=\"M16 3.13a4 4 0 0 1 0 7.75\" />\r\n                    </svg>\r\n                    Quản lý đại lý\r\n                  </DropdownMenuItem>\r\n                  <DropdownMenuItem onClick={() => window.location.href = \"/admin/wallets\"}>\r\n                    <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\" className=\"mr-2\">\r\n                      <path d=\"M2 9a2 2 0 0 1 2-2h16a2 2 0 0 1 2 2v9a2 2 0 0 1-2 2H4a2 2 0 0 1-2-2V9Z\" />\r\n                      <path d=\"M2 13h20\" />\r\n                      <path d=\"M9 17h6\" />\r\n                      <path d=\"M4 9V6a2 2 0 0 1 2-2h12a2 2 0 0 1 2 2v3\" />\r\n                    </svg>\r\n                    Quản lý tài khoản\r\n                  </DropdownMenuItem>\r\n                  <DropdownMenuItem onClick={() => window.location.href = \"/admin/banks\"}>\r\n                    <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\" className=\"mr-2\">\r\n                      <path d=\"M3 21h18\" />\r\n                      <path d=\"M3 7h18\" />\r\n                      <path d=\"M6 10v4\" />\r\n                      <path d=\"M10 10v4\" />\r\n                      <path d=\"M14 10v4\" />\r\n                      <path d=\"M18 10v4\" />\r\n                      <path d=\"m4 4 8-2 8 2\" />\r\n                    </svg>\r\n                    Quản lý ngân hàng\r\n                  </DropdownMenuItem>\r\n                  <DropdownMenuItem onClick={() => window.location.href = \"/admin/user-bank-accounts\"}>\r\n                    <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\" className=\"mr-2\">\r\n                      <path d=\"M3 21h18\" />\r\n                      <path d=\"M3 7h18\" />\r\n                      <path d=\"M6 10v4\" />\r\n                      <path d=\"M10 10v4\" />\r\n                      <path d=\"M14 10v4\" />\r\n                      <path d=\"M18 10v4\" />\r\n                      <path d=\"m4 4 8-2 8 2\" />\r\n                    </svg>\r\n                    Quản lý tài khoản ngân hàng\r\n                  </DropdownMenuItem>\r\n                  <DropdownMenuItem onClick={() => window.location.href = \"/admin/payment-methods\"}>\r\n                    <IconCreditCard />\r\n                    Quản lý thanh toán\r\n                  </DropdownMenuItem>\r\n\r\n                  <DropdownMenuSeparator />\r\n                  <DropdownMenuLabel className=\"text-xs font-semibold text-muted-foreground\">QUẢN LÝ GIAO DỊCH</DropdownMenuLabel>\r\n                  <DropdownMenuItem onClick={() => window.location.href = \"/admin/tokens\"}>\r\n                    <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\" className=\"mr-2\">\r\n                      <circle cx=\"8\" cy=\"8\" r=\"7\" />\r\n                      <circle cx=\"16\" cy=\"16\" r=\"7\" />\r\n                    </svg>\r\n                    Quản lý vàng\r\n                  </DropdownMenuItem>\r\n\r\n                  <DropdownMenuSeparator />\r\n                  <DropdownMenuLabel className=\"text-xs font-semibold text-muted-foreground\">HỆ THỐNG</DropdownMenuLabel>\r\n                  <DropdownMenuItem onClick={() => window.location.href = \"/admin/reports\"}>\r\n                    <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\" className=\"mr-2\">\r\n                      <path d=\"M3 3v18h18\" />\r\n                      <path d=\"m19 9-5-5-4 4-3-3\" />\r\n                    </svg>\r\n                    Báo cáo thống kê\r\n                  </DropdownMenuItem>\r\n                  <DropdownMenuItem onClick={() => window.location.href = \"/admin/roles\"}>\r\n                    <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\" className=\"mr-2\">\r\n                      <rect width=\"18\" height=\"18\" x=\"3\" y=\"3\" rx=\"2\" />\r\n                      <path d=\"M7 15h0M7 11h0M7 7h0\" />\r\n                      <path d=\"M11 15h6M11 11h6M11 7h6\" />\r\n                    </svg>\r\n                    Vai trò & quyền hạn\r\n                  </DropdownMenuItem>\r\n                  <DropdownMenuItem onClick={() => window.location.href = \"/admin/profile\"}>\r\n                    <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\" className=\"mr-2\">\r\n                      <path d=\"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2\" />\r\n                      <circle cx=\"9\" cy=\"7\" r=\"4\" />\r\n                      <path d=\"M22 21v-2a4 4 0 0 0-3-3.87\" />\r\n                      <path d=\"M16 3.13a4 4 0 0 1 0 7.75\" />\r\n                    </svg>\r\n                    Hồ sơ quản trị viên\r\n                  </DropdownMenuItem>\r\n                  <DropdownMenuItem onClick={() => window.location.href = \"/admin/settings\"}>\r\n                    <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\" className=\"mr-2\">\r\n                      <path d=\"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z\" />\r\n                      <circle cx=\"12\" cy=\"12\" r=\"3\" />\r\n                    </svg>\r\n                    Cấu hình hệ thống\r\n                  </DropdownMenuItem>\r\n                </>\r\n              ) : isAgent ? (\r\n                // Menu cho AGENT\r\n                <>\r\n                  <DropdownMenuItem onClick={() => window.location.href = \"/agent/dashboard\"}>\r\n                    <IconUserCircle />\r\n                    Dashboard Agent\r\n                  </DropdownMenuItem>\r\n\r\n                  <DropdownMenuSeparator />\r\n                  <DropdownMenuLabel className=\"text-xs font-semibold text-muted-foreground\">QUẢN LÝ NGƯỜI DÙNG</DropdownMenuLabel>\r\n                  <DropdownMenuItem onClick={() => window.location.href = \"/agent/users\"}>\r\n                    <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\" className=\"mr-2\">\r\n                      <path d=\"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2\" />\r\n                      <circle cx=\"9\" cy=\"7\" r=\"4\" />\r\n                      <path d=\"M22 21v-2a4 4 0 0 0-3-3.87\" />\r\n                      <path d=\"M16 3.13a4 4 0 0 1 0 7.75\" />\r\n                    </svg>\r\n                    Quản lý người dùng\r\n                  </DropdownMenuItem>\r\n                  <DropdownMenuItem onClick={() => window.location.href = \"/agent/kyc\"}>\r\n                    <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\" className=\"mr-2\">\r\n                      <path d=\"M16 18a4 4 0 0 0-8 0\" />\r\n                      <circle cx=\"12\" cy=\"10\" r=\"4\" />\r\n                      <rect width=\"20\" height=\"16\" x=\"2\" y=\"4\" rx=\"2\" />\r\n                    </svg>\r\n                    Quản lý KYC\r\n                  </DropdownMenuItem>\r\n\r\n                  <DropdownMenuSeparator />\r\n                  <DropdownMenuLabel className=\"text-xs font-semibold text-muted-foreground\">QUẢN LÝ TÀI CHÍNH</DropdownMenuLabel>\r\n                  <DropdownMenuItem onClick={() => window.location.href = \"/agent/transactions\"}>\r\n                    <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\" className=\"mr-2\">\r\n                      <path d=\"M2 9a2 2 0 0 1 2-2h16a2 2 0 0 1 2 2v9a2 2 0 0 1-2 2H4a2 2 0 0 1-2-2V9Z\" />\r\n                      <path d=\"M2 13h20\" />\r\n                      <path d=\"M9 17h6\" />\r\n                      <path d=\"M4 9V6a2 2 0 0 1 2-2h12a2 2 0 0 1 2 2v3\" />\r\n                    </svg>\r\n                    Quản lý giao dịch\r\n                  </DropdownMenuItem>\r\n                  <DropdownMenuItem onClick={() => window.location.href = \"/agent/agents\"}>\r\n                    <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\" className=\"mr-2\">\r\n                      <path d=\"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2\" />\r\n                      <circle cx=\"9\" cy=\"7\" r=\"4\" />\r\n                      <path d=\"M22 21v-2a4 4 0 0 0-3-3.87\" />\r\n                      <path d=\"M16 3.13a4 4 0 0 1 0 7.75\" />\r\n                    </svg>\r\n                    Quản lý đại lý\r\n                  </DropdownMenuItem>\r\n                  <DropdownMenuItem onClick={() => window.location.href = \"/agent/wallets\"}>\r\n                    <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\" className=\"mr-2\">\r\n                      <path d=\"M2 9a2 2 0 0 1 2-2h16a2 2 0 0 1 2 2v9a2 2 0 0 1-2 2H4a2 2 0 0 1-2-2V9Z\" />\r\n                      <path d=\"M2 13h20\" />\r\n                      <path d=\"M9 17h6\" />\r\n                      <path d=\"M4 9V6a2 2 0 0 1 2-2h12a2 2 0 0 1 2 2v3\" />\r\n                    </svg>\r\n                    Quản lý tài khoản\r\n                  </DropdownMenuItem>\r\n                  <DropdownMenuItem onClick={() => window.location.href = \"/agent/payment-methods\"}>\r\n                    <IconCreditCard />\r\n                    Quản lý thanh toán\r\n                  </DropdownMenuItem>\r\n\r\n                  <DropdownMenuSeparator />\r\n                  <DropdownMenuLabel className=\"text-xs font-semibold text-muted-foreground\">QUẢN LÝ GIAO DỊCH</DropdownMenuLabel>\r\n                  <DropdownMenuItem onClick={() => window.location.href = \"/agent/tokens\"}>\r\n                    <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\" className=\"mr-2\">\r\n                      <circle cx=\"8\" cy=\"8\" r=\"7\" />\r\n                      <circle cx=\"16\" cy=\"16\" r=\"7\" />\r\n                    </svg>\r\n                    Quản lý bạc\r\n                  </DropdownMenuItem>\r\n                  <DropdownMenuItem onClick={() => window.location.href = \"/agent/order-books\"}>\r\n                    <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\" className=\"mr-2\">\r\n                      <path d=\"M14 3v4a1 1 0 0 0 1 1h4\" />\r\n                      <path d=\"M17 21H7a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h7l5 5v11a2 2 0 0 1-2 2Z\" />\r\n                      <path d=\"M9 9h1\" />\r\n                      <path d=\"M9 13h6\" />\r\n                      <path d=\"M9 17h6\" />\r\n                    </svg>\r\n                    Quản lý giao dịch online\r\n                  </DropdownMenuItem>\r\n\r\n                  <DropdownMenuSeparator />\r\n                  <DropdownMenuLabel className=\"text-xs font-semibold text-muted-foreground\">HỆ THỐNG</DropdownMenuLabel>\r\n                  <DropdownMenuItem onClick={() => window.location.href = \"/agent/reports\"}>\r\n                    <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\" className=\"mr-2\">\r\n                      <path d=\"M3 3v18h18\" />\r\n                      <path d=\"m19 9-5-5-4 4-3-3\" />\r\n                    </svg>\r\n                    Báo cáo thống kê\r\n                  </DropdownMenuItem>\r\n                  <DropdownMenuItem onClick={() => window.location.href = \"/agent/roles\"}>\r\n                    <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\" className=\"mr-2\">\r\n                      <rect width=\"18\" height=\"18\" x=\"3\" y=\"3\" rx=\"2\" />\r\n                      <path d=\"M7 15h0M7 11h0M7 7h0\" />\r\n                      <path d=\"M11 15h6M11 11h6M11 7h6\" />\r\n                    </svg>\r\n                    Vai trò & quyền hạn\r\n                  </DropdownMenuItem>\r\n                </>\r\n              ) : (\r\n                // Menu cho USER\r\n                <>\r\n                  <DropdownMenuItem onClick={() => window.location.href = \"/profile\"}>\r\n                    <IconUserCircle />\r\n                    Thông tin cá nhân\r\n                  </DropdownMenuItem>\r\n                  <DropdownMenuItem onClick={() => window.location.href = \"/kyc\"}>\r\n                    <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\" className=\"mr-2\">\r\n                      <path d=\"M16 18a4 4 0 0 0-8 0\" />\r\n                      <circle cx=\"12\" cy=\"10\" r=\"4\" />\r\n                      <rect width=\"20\" height=\"16\" x=\"2\" y=\"4\" rx=\"2\" />\r\n                    </svg>\r\n                    Xác minh danh tính (KYC)\r\n                  </DropdownMenuItem>\r\n                  <DropdownMenuItem onClick={() => window.location.href = \"/wallet\"}>\r\n                    <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\" className=\"mr-2\">\r\n                      <path d=\"M2 9a2 2 0 0 1 2-2h16a2 2 0 0 1 2 2v9a2 2 0 0 1-2 2H4a2 2 0 0 1-2-2V9Z\" />\r\n                      <path d=\"M2 13h20\" />\r\n                      <path d=\"M9 17h6\" />\r\n                      <path d=\"M4 9V6a2 2 0 0 1 2-2h12a2 2 0 0 1 2 2v3\" />\r\n                    </svg>\r\n                    Ví tiền\r\n                  </DropdownMenuItem>\r\n                  <DropdownMenuItem onClick={() => window.location.href = \"/trading\"}>\r\n                    <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\" className=\"mr-2\">\r\n                      <path d=\"M3 3v18h18\" />\r\n                      <path d=\"m19 9-5-5-4 4-3-3\" />\r\n                    </svg>\r\n                    Giao dịch vàng\r\n                  </DropdownMenuItem>\r\n                  <DropdownMenuItem onClick={() => window.location.href = \"/contracts\"}>\r\n                    <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\" className=\"mr-2\">\r\n                      <path d=\"M14 3v4a1 1 0 0 0 1 1h4\" />\r\n                      <path d=\"M17 21H7a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h7l5 5v11a2 2 0 0 1-2 2Z\" />\r\n                      <path d=\"M9 9h1\" />\r\n                      <path d=\"M9 13h6\" />\r\n                      <path d=\"M9 17h6\" />\r\n                    </svg>\r\n                    Hợp đồng\r\n                  </DropdownMenuItem>\r\n                  <DropdownMenuItem onClick={() => window.location.href = \"/payment-methods\"}>\r\n                    <IconCreditCard />\r\n                    Phương thức thanh toán\r\n                  </DropdownMenuItem>\r\n                  <DropdownMenuItem>\r\n                    <IconNotification />\r\n                    Thông báo\r\n                  </DropdownMenuItem>\r\n                </>\r\n              )}\r\n            </DropdownMenuGroup>\r\n            <DropdownMenuSeparator />\r\n            <DropdownMenuItem onClick={() => {\r\n              logout();\r\n              toast.success(\"Đăng xuất thành công\", {\r\n                description: \"Bạn đã đăng xuất khỏi hệ thống.\"\r\n              });\r\n            }} className=\"text-destructive\">\r\n              <IconLogout className=\"text-destructive\"/>\r\n              Đăng xuất\r\n            </DropdownMenuItem>\r\n          </DropdownMenuContent>\r\n        </DropdownMenu>\r\n      </SidebarMenuItem>\r\n    </SidebarMenu>\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AAAA;AAAA;AAAA;AAAA;AAQA;AAKA;AASA;AAMA;AACA;;;AA/BA;;;;;;;AAiCO,SAAS;;IACd,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,wHAAA,CAAA,UAAO,AAAD;IAC/B,oCAAoC;IACpC,MAAM,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,aAAU,AAAD;IAE9B,yBAAyB;IACzB,MAAM,UAAU,MAAM,OAAO,SAAS;IACtC,MAAM,UAAU,MAAM,OAAO,SAAS;IAEtC,6CAA6C;IAC7C,IAAI,CAAC,MAAM;QACT,qBACE,sSAAC,+HAAA,CAAA,cAAW;sBACV,cAAA,sSAAC,+HAAA,CAAA,kBAAe;0BACd,cAAA,sSAAC,+HAAA,CAAA,oBAAiB;oBAChB,MAAK;oBACL,SAAS,IAAM,OAAO,QAAQ,CAAC,IAAI,GAAG;8BAEtC,cAAA,sSAAC;kCAAK;;;;;;;;;;;;;;;;;;;;;IAKhB;IAEA,qBACE,sSAAC,+HAAA,CAAA,cAAW;kBACV,cAAA,sSAAC,+HAAA,CAAA,kBAAe;sBACd,cAAA,sSAAC,wIAAA,CAAA,eAAY;;kCACX,sSAAC,wIAAA,CAAA,sBAAmB;wBAAC,OAAO;kCAC1B,cAAA,sSAAC,+HAAA,CAAA,oBAAiB;4BAChB,MAAK;4BACL,WAAU;;8CAEV,sSAAC,8HAAA,CAAA,SAAM;oCAAC,WAAU;;sDAChB,sSAAC,8HAAA,CAAA,cAAW;4CAAC,KAAK,KAAK,MAAM,IAAI;4CAAwB,KAAK,KAAK,IAAI,IAAI;;;;;;sDAC3E,sSAAC,8HAAA,CAAA,iBAAc;4CAAC,WAAU;sDAAc,KAAK,IAAI,GAAG,KAAK,IAAI,CAAC,MAAM,CAAC,KAAK;;;;;;;;;;;;8CAE5E,sSAAC;oCAAI,WAAU;;sDACb,sSAAC;4CAAK,WAAU;sDAAwB,KAAK,IAAI,IAAI;;;;;;sDACrD,sSAAC;4CAAK,WAAU;;gDACb,KAAK,KAAK;gDACV,yBAAW,sSAAC;oDAAK,WAAU;8DAAkE;;;;;;gDAC7F,yBAAW,sSAAC;oDAAK,WAAU;8DAAqE;;;;;;;;;;;;;;;;;;8CAGrG,sSAAC,uUAAA,CAAA,mBAAgB;oCAAC,WAAU;;;;;;;;;;;;;;;;;kCAGhC,sSAAC,wIAAA,CAAA,sBAAmB;wBAClB,WAAU;wBACV,MAAM,WAAW,WAAW;wBAC5B,OAAM;wBACN,YAAY;;0CAEZ,sSAAC,wIAAA,CAAA,oBAAiB;gCAAC,WAAU;0CAC3B,cAAA,sSAAC;oCAAI,WAAU;;sDACb,sSAAC,8HAAA,CAAA,SAAM;4CAAC,WAAU;;8DAChB,sSAAC,8HAAA,CAAA,cAAW;oDAAC,KAAK,KAAK,MAAM,IAAI;oDAAwB,KAAK,KAAK,IAAI,IAAI;;;;;;8DAC3E,sSAAC,8HAAA,CAAA,iBAAc;oDAAC,WAAU;8DAAc,KAAK,IAAI,GAAG,KAAK,IAAI,CAAC,MAAM,CAAC,KAAK;;;;;;;;;;;;sDAE5E,sSAAC;4CAAI,WAAU;;8DACb,sSAAC;oDAAK,WAAU;8DAAwB,KAAK,IAAI,IAAI;;;;;;8DACrD,sSAAC;oDAAK,WAAU;;wDACb,KAAK,KAAK;wDACV,yBAAW,sSAAC;4DAAK,WAAU;sEAAkE;;;;;;wDAC7F,yBAAW,sSAAC;4DAAK,WAAU;sEAAqE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAKzG,sSAAC,wIAAA,CAAA,wBAAqB;;;;;0CACtB,sSAAC,wIAAA,CAAA,oBAAiB;0CACf,UACC,iBAAiB;8CACjB;;sDACE,sSAAC,wIAAA,CAAA,mBAAgB;4CAAC,SAAS,IAAM,OAAO,QAAQ,CAAC,IAAI,GAAG;;8DACtD,sSAAC,mUAAA,CAAA,iBAAc;;;;;gDAAG;;;;;;;sDAIpB,sSAAC,wIAAA,CAAA,wBAAqB;;;;;sDACtB,sSAAC,wIAAA,CAAA,oBAAiB;4CAAC,WAAU;sDAA8C;;;;;;sDAC3E,sSAAC,wIAAA,CAAA,mBAAgB;4CAAC,SAAS,IAAM,OAAO,QAAQ,CAAC,IAAI,GAAG;;8DACtD,sSAAC;oDAAI,OAAM;oDAA6B,OAAM;oDAAK,QAAO;oDAAK,SAAQ;oDAAY,MAAK;oDAAO,QAAO;oDAAe,aAAY;oDAAI,eAAc;oDAAQ,gBAAe;oDAAQ,WAAU;;sEAC1L,sSAAC;4DAAK,GAAE;;;;;;sEACR,sSAAC;4DAAO,IAAG;4DAAI,IAAG;4DAAI,GAAE;;;;;;sEACxB,sSAAC;4DAAK,GAAE;;;;;;sEACR,sSAAC;4DAAK,GAAE;;;;;;;;;;;;gDACJ;;;;;;;sDAGR,sSAAC,wIAAA,CAAA,mBAAgB;4CAAC,SAAS,IAAM,OAAO,QAAQ,CAAC,IAAI,GAAG;;8DACtD,sSAAC;oDAAI,OAAM;oDAA6B,OAAM;oDAAK,QAAO;oDAAK,SAAQ;oDAAY,MAAK;oDAAO,QAAO;oDAAe,aAAY;oDAAI,eAAc;oDAAQ,gBAAe;oDAAQ,WAAU;;sEAC1L,sSAAC;4DAAK,GAAE;;;;;;sEACR,sSAAC;4DAAO,IAAG;4DAAK,IAAG;4DAAK,GAAE;;;;;;sEAC1B,sSAAC;4DAAK,OAAM;4DAAK,QAAO;4DAAK,GAAE;4DAAI,GAAE;4DAAI,IAAG;;;;;;;;;;;;gDACxC;;;;;;;sDAIR,sSAAC,wIAAA,CAAA,wBAAqB;;;;;sDACtB,sSAAC,wIAAA,CAAA,oBAAiB;4CAAC,WAAU;sDAA8C;;;;;;sDAC3E,sSAAC,wIAAA,CAAA,mBAAgB;4CAAC,SAAS,IAAM,OAAO,QAAQ,CAAC,IAAI,GAAG;;8DACtD,sSAAC;oDAAI,OAAM;oDAA6B,OAAM;oDAAK,QAAO;oDAAK,SAAQ;oDAAY,MAAK;oDAAO,QAAO;oDAAe,aAAY;oDAAI,eAAc;oDAAQ,gBAAe;oDAAQ,WAAU;;sEAC1L,sSAAC;4DAAK,GAAE;;;;;;sEACR,sSAAC;4DAAK,GAAE;;;;;;sEACR,sSAAC;4DAAK,GAAE;;;;;;sEACR,sSAAC;4DAAK,GAAE;;;;;;;;;;;;gDACJ;;;;;;;sDAGR,sSAAC,wIAAA,CAAA,mBAAgB;4CAAC,SAAS,IAAM,OAAO,QAAQ,CAAC,IAAI,GAAG;;8DACtD,sSAAC;oDAAI,OAAM;oDAA6B,OAAM;oDAAK,QAAO;oDAAK,SAAQ;oDAAY,MAAK;oDAAO,QAAO;oDAAe,aAAY;oDAAI,eAAc;oDAAQ,gBAAe;oDAAQ,WAAU;;sEAC1L,sSAAC;4DAAK,GAAE;;;;;;sEACR,sSAAC;4DAAK,GAAE;;;;;;sEACR,sSAAC;4DAAK,GAAE;;;;;;sEACR,sSAAC;4DAAK,GAAE;;;;;;sEACR,sSAAC;4DAAK,GAAE;;;;;;;;;;;;gDACJ;;;;;;;sDAGR,sSAAC,wIAAA,CAAA,mBAAgB;4CAAC,SAAS,IAAM,OAAO,QAAQ,CAAC,IAAI,GAAG;;8DACtD,sSAAC;oDAAI,OAAM;oDAA6B,OAAM;oDAAK,QAAO;oDAAK,SAAQ;oDAAY,MAAK;oDAAO,QAAO;oDAAe,aAAY;oDAAI,eAAc;oDAAQ,gBAAe;oDAAQ,WAAU;;sEAC1L,sSAAC;4DAAK,GAAE;;;;;;sEACR,sSAAC;4DAAO,IAAG;4DAAI,IAAG;4DAAI,GAAE;;;;;;sEACxB,sSAAC;4DAAK,GAAE;;;;;;sEACR,sSAAC;4DAAK,GAAE;;;;;;;;;;;;gDACJ;;;;;;;sDAGR,sSAAC,wIAAA,CAAA,mBAAgB;4CAAC,SAAS,IAAM,OAAO,QAAQ,CAAC,IAAI,GAAG;;8DACtD,sSAAC;oDAAI,OAAM;oDAA6B,OAAM;oDAAK,QAAO;oDAAK,SAAQ;oDAAY,MAAK;oDAAO,QAAO;oDAAe,aAAY;oDAAI,eAAc;oDAAQ,gBAAe;oDAAQ,WAAU;;sEAC1L,sSAAC;4DAAK,GAAE;;;;;;sEACR,sSAAC;4DAAK,GAAE;;;;;;sEACR,sSAAC;4DAAK,GAAE;;;;;;sEACR,sSAAC;4DAAK,GAAE;;;;;;;;;;;;gDACJ;;;;;;;sDAGR,sSAAC,wIAAA,CAAA,mBAAgB;4CAAC,SAAS,IAAM,OAAO,QAAQ,CAAC,IAAI,GAAG;;8DACtD,sSAAC;oDAAI,OAAM;oDAA6B,OAAM;oDAAK,QAAO;oDAAK,SAAQ;oDAAY,MAAK;oDAAO,QAAO;oDAAe,aAAY;oDAAI,eAAc;oDAAQ,gBAAe;oDAAQ,WAAU;;sEAC1L,sSAAC;4DAAK,GAAE;;;;;;sEACR,sSAAC;4DAAK,GAAE;;;;;;sEACR,sSAAC;4DAAK,GAAE;;;;;;sEACR,sSAAC;4DAAK,GAAE;;;;;;sEACR,sSAAC;4DAAK,GAAE;;;;;;sEACR,sSAAC;4DAAK,GAAE;;;;;;sEACR,sSAAC;4DAAK,GAAE;;;;;;;;;;;;gDACJ;;;;;;;sDAGR,sSAAC,wIAAA,CAAA,mBAAgB;4CAAC,SAAS,IAAM,OAAO,QAAQ,CAAC,IAAI,GAAG;;8DACtD,sSAAC;oDAAI,OAAM;oDAA6B,OAAM;oDAAK,QAAO;oDAAK,SAAQ;oDAAY,MAAK;oDAAO,QAAO;oDAAe,aAAY;oDAAI,eAAc;oDAAQ,gBAAe;oDAAQ,WAAU;;sEAC1L,sSAAC;4DAAK,GAAE;;;;;;sEACR,sSAAC;4DAAK,GAAE;;;;;;sEACR,sSAAC;4DAAK,GAAE;;;;;;sEACR,sSAAC;4DAAK,GAAE;;;;;;sEACR,sSAAC;4DAAK,GAAE;;;;;;sEACR,sSAAC;4DAAK,GAAE;;;;;;sEACR,sSAAC;4DAAK,GAAE;;;;;;;;;;;;gDACJ;;;;;;;sDAGR,sSAAC,wIAAA,CAAA,mBAAgB;4CAAC,SAAS,IAAM,OAAO,QAAQ,CAAC,IAAI,GAAG;;8DACtD,sSAAC,mUAAA,CAAA,iBAAc;;;;;gDAAG;;;;;;;sDAIpB,sSAAC,wIAAA,CAAA,wBAAqB;;;;;sDACtB,sSAAC,wIAAA,CAAA,oBAAiB;4CAAC,WAAU;sDAA8C;;;;;;sDAC3E,sSAAC,wIAAA,CAAA,mBAAgB;4CAAC,SAAS,IAAM,OAAO,QAAQ,CAAC,IAAI,GAAG;;8DACtD,sSAAC;oDAAI,OAAM;oDAA6B,OAAM;oDAAK,QAAO;oDAAK,SAAQ;oDAAY,MAAK;oDAAO,QAAO;oDAAe,aAAY;oDAAI,eAAc;oDAAQ,gBAAe;oDAAQ,WAAU;;sEAC1L,sSAAC;4DAAO,IAAG;4DAAI,IAAG;4DAAI,GAAE;;;;;;sEACxB,sSAAC;4DAAO,IAAG;4DAAK,IAAG;4DAAK,GAAE;;;;;;;;;;;;gDACtB;;;;;;;sDAIR,sSAAC,wIAAA,CAAA,wBAAqB;;;;;sDACtB,sSAAC,wIAAA,CAAA,oBAAiB;4CAAC,WAAU;sDAA8C;;;;;;sDAC3E,sSAAC,wIAAA,CAAA,mBAAgB;4CAAC,SAAS,IAAM,OAAO,QAAQ,CAAC,IAAI,GAAG;;8DACtD,sSAAC;oDAAI,OAAM;oDAA6B,OAAM;oDAAK,QAAO;oDAAK,SAAQ;oDAAY,MAAK;oDAAO,QAAO;oDAAe,aAAY;oDAAI,eAAc;oDAAQ,gBAAe;oDAAQ,WAAU;;sEAC1L,sSAAC;4DAAK,GAAE;;;;;;sEACR,sSAAC;4DAAK,GAAE;;;;;;;;;;;;gDACJ;;;;;;;sDAGR,sSAAC,wIAAA,CAAA,mBAAgB;4CAAC,SAAS,IAAM,OAAO,QAAQ,CAAC,IAAI,GAAG;;8DACtD,sSAAC;oDAAI,OAAM;oDAA6B,OAAM;oDAAK,QAAO;oDAAK,SAAQ;oDAAY,MAAK;oDAAO,QAAO;oDAAe,aAAY;oDAAI,eAAc;oDAAQ,gBAAe;oDAAQ,WAAU;;sEAC1L,sSAAC;4DAAK,OAAM;4DAAK,QAAO;4DAAK,GAAE;4DAAI,GAAE;4DAAI,IAAG;;;;;;sEAC5C,sSAAC;4DAAK,GAAE;;;;;;sEACR,sSAAC;4DAAK,GAAE;;;;;;;;;;;;gDACJ;;;;;;;sDAGR,sSAAC,wIAAA,CAAA,mBAAgB;4CAAC,SAAS,IAAM,OAAO,QAAQ,CAAC,IAAI,GAAG;;8DACtD,sSAAC;oDAAI,OAAM;oDAA6B,OAAM;oDAAK,QAAO;oDAAK,SAAQ;oDAAY,MAAK;oDAAO,QAAO;oDAAe,aAAY;oDAAI,eAAc;oDAAQ,gBAAe;oDAAQ,WAAU;;sEAC1L,sSAAC;4DAAK,GAAE;;;;;;sEACR,sSAAC;4DAAO,IAAG;4DAAI,IAAG;4DAAI,GAAE;;;;;;sEACxB,sSAAC;4DAAK,GAAE;;;;;;sEACR,sSAAC;4DAAK,GAAE;;;;;;;;;;;;gDACJ;;;;;;;sDAGR,sSAAC,wIAAA,CAAA,mBAAgB;4CAAC,SAAS,IAAM,OAAO,QAAQ,CAAC,IAAI,GAAG;;8DACtD,sSAAC;oDAAI,OAAM;oDAA6B,OAAM;oDAAK,QAAO;oDAAK,SAAQ;oDAAY,MAAK;oDAAO,QAAO;oDAAe,aAAY;oDAAI,eAAc;oDAAQ,gBAAe;oDAAQ,WAAU;;sEAC1L,sSAAC;4DAAK,GAAE;;;;;;sEACR,sSAAC;4DAAO,IAAG;4DAAK,IAAG;4DAAK,GAAE;;;;;;;;;;;;gDACtB;;;;;;;;mDAIR,UACF,iBAAiB;8CACjB;;sDACE,sSAAC,wIAAA,CAAA,mBAAgB;4CAAC,SAAS,IAAM,OAAO,QAAQ,CAAC,IAAI,GAAG;;8DACtD,sSAAC,mUAAA,CAAA,iBAAc;;;;;gDAAG;;;;;;;sDAIpB,sSAAC,wIAAA,CAAA,wBAAqB;;;;;sDACtB,sSAAC,wIAAA,CAAA,oBAAiB;4CAAC,WAAU;sDAA8C;;;;;;sDAC3E,sSAAC,wIAAA,CAAA,mBAAgB;4CAAC,SAAS,IAAM,OAAO,QAAQ,CAAC,IAAI,GAAG;;8DACtD,sSAAC;oDAAI,OAAM;oDAA6B,OAAM;oDAAK,QAAO;oDAAK,SAAQ;oDAAY,MAAK;oDAAO,QAAO;oDAAe,aAAY;oDAAI,eAAc;oDAAQ,gBAAe;oDAAQ,WAAU;;sEAC1L,sSAAC;4DAAK,GAAE;;;;;;sEACR,sSAAC;4DAAO,IAAG;4DAAI,IAAG;4DAAI,GAAE;;;;;;sEACxB,sSAAC;4DAAK,GAAE;;;;;;sEACR,sSAAC;4DAAK,GAAE;;;;;;;;;;;;gDACJ;;;;;;;sDAGR,sSAAC,wIAAA,CAAA,mBAAgB;4CAAC,SAAS,IAAM,OAAO,QAAQ,CAAC,IAAI,GAAG;;8DACtD,sSAAC;oDAAI,OAAM;oDAA6B,OAAM;oDAAK,QAAO;oDAAK,SAAQ;oDAAY,MAAK;oDAAO,QAAO;oDAAe,aAAY;oDAAI,eAAc;oDAAQ,gBAAe;oDAAQ,WAAU;;sEAC1L,sSAAC;4DAAK,GAAE;;;;;;sEACR,sSAAC;4DAAO,IAAG;4DAAK,IAAG;4DAAK,GAAE;;;;;;sEAC1B,sSAAC;4DAAK,OAAM;4DAAK,QAAO;4DAAK,GAAE;4DAAI,GAAE;4DAAI,IAAG;;;;;;;;;;;;gDACxC;;;;;;;sDAIR,sSAAC,wIAAA,CAAA,wBAAqB;;;;;sDACtB,sSAAC,wIAAA,CAAA,oBAAiB;4CAAC,WAAU;sDAA8C;;;;;;sDAC3E,sSAAC,wIAAA,CAAA,mBAAgB;4CAAC,SAAS,IAAM,OAAO,QAAQ,CAAC,IAAI,GAAG;;8DACtD,sSAAC;oDAAI,OAAM;oDAA6B,OAAM;oDAAK,QAAO;oDAAK,SAAQ;oDAAY,MAAK;oDAAO,QAAO;oDAAe,aAAY;oDAAI,eAAc;oDAAQ,gBAAe;oDAAQ,WAAU;;sEAC1L,sSAAC;4DAAK,GAAE;;;;;;sEACR,sSAAC;4DAAK,GAAE;;;;;;sEACR,sSAAC;4DAAK,GAAE;;;;;;sEACR,sSAAC;4DAAK,GAAE;;;;;;;;;;;;gDACJ;;;;;;;sDAGR,sSAAC,wIAAA,CAAA,mBAAgB;4CAAC,SAAS,IAAM,OAAO,QAAQ,CAAC,IAAI,GAAG;;8DACtD,sSAAC;oDAAI,OAAM;oDAA6B,OAAM;oDAAK,QAAO;oDAAK,SAAQ;oDAAY,MAAK;oDAAO,QAAO;oDAAe,aAAY;oDAAI,eAAc;oDAAQ,gBAAe;oDAAQ,WAAU;;sEAC1L,sSAAC;4DAAK,GAAE;;;;;;sEACR,sSAAC;4DAAO,IAAG;4DAAI,IAAG;4DAAI,GAAE;;;;;;sEACxB,sSAAC;4DAAK,GAAE;;;;;;sEACR,sSAAC;4DAAK,GAAE;;;;;;;;;;;;gDACJ;;;;;;;sDAGR,sSAAC,wIAAA,CAAA,mBAAgB;4CAAC,SAAS,IAAM,OAAO,QAAQ,CAAC,IAAI,GAAG;;8DACtD,sSAAC;oDAAI,OAAM;oDAA6B,OAAM;oDAAK,QAAO;oDAAK,SAAQ;oDAAY,MAAK;oDAAO,QAAO;oDAAe,aAAY;oDAAI,eAAc;oDAAQ,gBAAe;oDAAQ,WAAU;;sEAC1L,sSAAC;4DAAK,GAAE;;;;;;sEACR,sSAAC;4DAAK,GAAE;;;;;;sEACR,sSAAC;4DAAK,GAAE;;;;;;sEACR,sSAAC;4DAAK,GAAE;;;;;;;;;;;;gDACJ;;;;;;;sDAGR,sSAAC,wIAAA,CAAA,mBAAgB;4CAAC,SAAS,IAAM,OAAO,QAAQ,CAAC,IAAI,GAAG;;8DACtD,sSAAC,mUAAA,CAAA,iBAAc;;;;;gDAAG;;;;;;;sDAIpB,sSAAC,wIAAA,CAAA,wBAAqB;;;;;sDACtB,sSAAC,wIAAA,CAAA,oBAAiB;4CAAC,WAAU;sDAA8C;;;;;;sDAC3E,sSAAC,wIAAA,CAAA,mBAAgB;4CAAC,SAAS,IAAM,OAAO,QAAQ,CAAC,IAAI,GAAG;;8DACtD,sSAAC;oDAAI,OAAM;oDAA6B,OAAM;oDAAK,QAAO;oDAAK,SAAQ;oDAAY,MAAK;oDAAO,QAAO;oDAAe,aAAY;oDAAI,eAAc;oDAAQ,gBAAe;oDAAQ,WAAU;;sEAC1L,sSAAC;4DAAO,IAAG;4DAAI,IAAG;4DAAI,GAAE;;;;;;sEACxB,sSAAC;4DAAO,IAAG;4DAAK,IAAG;4DAAK,GAAE;;;;;;;;;;;;gDACtB;;;;;;;sDAGR,sSAAC,wIAAA,CAAA,mBAAgB;4CAAC,SAAS,IAAM,OAAO,QAAQ,CAAC,IAAI,GAAG;;8DACtD,sSAAC;oDAAI,OAAM;oDAA6B,OAAM;oDAAK,QAAO;oDAAK,SAAQ;oDAAY,MAAK;oDAAO,QAAO;oDAAe,aAAY;oDAAI,eAAc;oDAAQ,gBAAe;oDAAQ,WAAU;;sEAC1L,sSAAC;4DAAK,GAAE;;;;;;sEACR,sSAAC;4DAAK,GAAE;;;;;;sEACR,sSAAC;4DAAK,GAAE;;;;;;sEACR,sSAAC;4DAAK,GAAE;;;;;;sEACR,sSAAC;4DAAK,GAAE;;;;;;;;;;;;gDACJ;;;;;;;sDAIR,sSAAC,wIAAA,CAAA,wBAAqB;;;;;sDACtB,sSAAC,wIAAA,CAAA,oBAAiB;4CAAC,WAAU;sDAA8C;;;;;;sDAC3E,sSAAC,wIAAA,CAAA,mBAAgB;4CAAC,SAAS,IAAM,OAAO,QAAQ,CAAC,IAAI,GAAG;;8DACtD,sSAAC;oDAAI,OAAM;oDAA6B,OAAM;oDAAK,QAAO;oDAAK,SAAQ;oDAAY,MAAK;oDAAO,QAAO;oDAAe,aAAY;oDAAI,eAAc;oDAAQ,gBAAe;oDAAQ,WAAU;;sEAC1L,sSAAC;4DAAK,GAAE;;;;;;sEACR,sSAAC;4DAAK,GAAE;;;;;;;;;;;;gDACJ;;;;;;;sDAGR,sSAAC,wIAAA,CAAA,mBAAgB;4CAAC,SAAS,IAAM,OAAO,QAAQ,CAAC,IAAI,GAAG;;8DACtD,sSAAC;oDAAI,OAAM;oDAA6B,OAAM;oDAAK,QAAO;oDAAK,SAAQ;oDAAY,MAAK;oDAAO,QAAO;oDAAe,aAAY;oDAAI,eAAc;oDAAQ,gBAAe;oDAAQ,WAAU;;sEAC1L,sSAAC;4DAAK,OAAM;4DAAK,QAAO;4DAAK,GAAE;4DAAI,GAAE;4DAAI,IAAG;;;;;;sEAC5C,sSAAC;4DAAK,GAAE;;;;;;sEACR,sSAAC;4DAAK,GAAE;;;;;;;;;;;;gDACJ;;;;;;;;mDAKV,gBAAgB;8CAChB;;sDACE,sSAAC,wIAAA,CAAA,mBAAgB;4CAAC,SAAS,IAAM,OAAO,QAAQ,CAAC,IAAI,GAAG;;8DACtD,sSAAC,mUAAA,CAAA,iBAAc;;;;;gDAAG;;;;;;;sDAGpB,sSAAC,wIAAA,CAAA,mBAAgB;4CAAC,SAAS,IAAM,OAAO,QAAQ,CAAC,IAAI,GAAG;;8DACtD,sSAAC;oDAAI,OAAM;oDAA6B,OAAM;oDAAK,QAAO;oDAAK,SAAQ;oDAAY,MAAK;oDAAO,QAAO;oDAAe,aAAY;oDAAI,eAAc;oDAAQ,gBAAe;oDAAQ,WAAU;;sEAC1L,sSAAC;4DAAK,GAAE;;;;;;sEACR,sSAAC;4DAAO,IAAG;4DAAK,IAAG;4DAAK,GAAE;;;;;;sEAC1B,sSAAC;4DAAK,OAAM;4DAAK,QAAO;4DAAK,GAAE;4DAAI,GAAE;4DAAI,IAAG;;;;;;;;;;;;gDACxC;;;;;;;sDAGR,sSAAC,wIAAA,CAAA,mBAAgB;4CAAC,SAAS,IAAM,OAAO,QAAQ,CAAC,IAAI,GAAG;;8DACtD,sSAAC;oDAAI,OAAM;oDAA6B,OAAM;oDAAK,QAAO;oDAAK,SAAQ;oDAAY,MAAK;oDAAO,QAAO;oDAAe,aAAY;oDAAI,eAAc;oDAAQ,gBAAe;oDAAQ,WAAU;;sEAC1L,sSAAC;4DAAK,GAAE;;;;;;sEACR,sSAAC;4DAAK,GAAE;;;;;;sEACR,sSAAC;4DAAK,GAAE;;;;;;sEACR,sSAAC;4DAAK,GAAE;;;;;;;;;;;;gDACJ;;;;;;;sDAGR,sSAAC,wIAAA,CAAA,mBAAgB;4CAAC,SAAS,IAAM,OAAO,QAAQ,CAAC,IAAI,GAAG;;8DACtD,sSAAC;oDAAI,OAAM;oDAA6B,OAAM;oDAAK,QAAO;oDAAK,SAAQ;oDAAY,MAAK;oDAAO,QAAO;oDAAe,aAAY;oDAAI,eAAc;oDAAQ,gBAAe;oDAAQ,WAAU;;sEAC1L,sSAAC;4DAAK,GAAE;;;;;;sEACR,sSAAC;4DAAK,GAAE;;;;;;;;;;;;gDACJ;;;;;;;sDAGR,sSAAC,wIAAA,CAAA,mBAAgB;4CAAC,SAAS,IAAM,OAAO,QAAQ,CAAC,IAAI,GAAG;;8DACtD,sSAAC;oDAAI,OAAM;oDAA6B,OAAM;oDAAK,QAAO;oDAAK,SAAQ;oDAAY,MAAK;oDAAO,QAAO;oDAAe,aAAY;oDAAI,eAAc;oDAAQ,gBAAe;oDAAQ,WAAU;;sEAC1L,sSAAC;4DAAK,GAAE;;;;;;sEACR,sSAAC;4DAAK,GAAE;;;;;;sEACR,sSAAC;4DAAK,GAAE;;;;;;sEACR,sSAAC;4DAAK,GAAE;;;;;;sEACR,sSAAC;4DAAK,GAAE;;;;;;;;;;;;gDACJ;;;;;;;sDAGR,sSAAC,wIAAA,CAAA,mBAAgB;4CAAC,SAAS,IAAM,OAAO,QAAQ,CAAC,IAAI,GAAG;;8DACtD,sSAAC,mUAAA,CAAA,iBAAc;;;;;gDAAG;;;;;;;sDAGpB,sSAAC,wIAAA,CAAA,mBAAgB;;8DACf,sSAAC,uUAAA,CAAA,mBAAgB;;;;;gDAAG;;;;;;;;;;;;;;0CAM5B,sSAAC,wIAAA,CAAA,wBAAqB;;;;;0CACtB,sSAAC,wIAAA,CAAA,mBAAgB;gCAAC,SAAS;oCACzB;oCACA,2QAAA,CAAA,QAAK,CAAC,OAAO,CAAC,wBAAwB;wCACpC,aAAa;oCACf;gCACF;gCAAG,WAAU;;kDACX,sSAAC,2TAAA,CAAA,aAAU;wCAAC,WAAU;;;;;;oCAAoB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQxD;GA5XgB;;QACW,wHAAA,CAAA,UAAO;QAEX,+HAAA,CAAA,aAAU;;;KAHjB", "debugId": null}}, {"offset": {"line": 3822, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/bloomgoo/gold-exchange-platform/frontend/components/app-sidebar.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport { useAuth } from \"@/hooks/use-auth\"\r\nimport {\r\n  IconBuildingBank,\r\n  IconCash,\r\n  IconCategory,\r\n  IconChartCandle,\r\n  IconCoin,\r\n  IconCreditCard,\r\n  IconCurrencyDollar,\r\n  IconDashboard,\r\n  IconDeviceAnalytics,\r\n  IconExchange,\r\n  IconFileAnalytics,\r\n  IconFileDescription,\r\n  IconFolders,\r\n  IconGift,\r\n  IconHistory,\r\n  IconMoneybag,\r\n  IconPackage,\r\n  IconPackageImport,\r\n  IconReceipt,\r\n  IconRobot,\r\n  IconSettings,\r\n  IconShieldLock,\r\n  IconShoppingCart,\r\n  IconUser,\r\n  IconUserCheck,\r\n  IconUserCircle,\r\n  IconUserCog,\r\n  IconUserStar,\r\n  IconWallet,\r\n  IconNews,\r\n  IconTags,\r\n  IconPhoto,\r\n  IconMessageCircle,\r\n  IconBuilding,\r\n  IconBrandPagekit,\r\n  IconHeartHandshake,\r\n  IconWorld\r\n} from \"@tabler/icons-react\"\r\nimport Link from \"next/link\"\r\nimport * as React from \"react\"\r\n\r\nimport { NavMain } from \"@/components/nav-main\"\r\nimport { NavSecondary } from \"@/components/nav-secondary\"\r\nimport { NavUser } from \"@/components/nav-user\"\r\nimport {\r\n  Sidebar,\r\n  SidebarContent,\r\n  SidebarFooter,\r\n  SidebarHeader,\r\n  SidebarMenu,\r\n  SidebarMenuButton,\r\n  SidebarMenuItem,\r\n} from \"@/components/ui/sidebar\"\r\nimport { Avatar, AvatarFallback, AvatarImage } from \"./ui/avatar\"\r\nimport { useSiteMetadata } from \"@/hooks/use-site-metadata\"\r\n\r\n// Dữ liệu menu cho USER\r\n\r\nconst userNavData = {\r\n  navMain: [\r\n    {\r\n      key: \"user-dashboard\",\r\n      title: \"Bảng quản trị\",\r\n      url: \"/reports\",\r\n      // url: \"/dashboard\",\r\n      icon: IconDashboard,\r\n    },\r\n    // {\r\n    //   title: \"Báo cáo thống kê\",\r\n    //   url: \"/reports\",\r\n    //   icon: IconFileAnalytics,\r\n    // },\r\n    {\r\n      type: \"separator\",\r\n      title: \"QUẢN TRỊ BẠC TRỰC TUYẾN\",\r\n    },\r\n    {\r\n      key: \"user-trading\",\r\n      title: \"Giao dịch mua bán\",\r\n      url: \"/trading\",\r\n      icon: IconChartCandle,\r\n    },\r\n    {\r\n      key: \"user-order-books\",\r\n      title: \"Lịch sử giao dịch\",\r\n      url: \"/order-books\",\r\n      icon: IconReceipt,\r\n    },\r\n    {\r\n      key: \"user-token-assets\",\r\n      title: \"Tài sản\",\r\n      url: \"/token-assets\",\r\n      icon: IconCoin,\r\n    },\r\n    {\r\n      type: \"separator\",\r\n      title: \"QUẢN TRỊ SẢN PHẨM BẠC\",\r\n    },\r\n    {\r\n      key: \"user-ecom-products\",\r\n      title: \"Sản phẩm\",\r\n      url: \"/ecom-products\",\r\n      icon: IconPackageImport,\r\n    },\r\n    {\r\n      key: \"user-ecom-orders\",\r\n      title: \"Đơn hàng\",\r\n      url: \"/ecom-orders\",\r\n      icon: IconShoppingCart,\r\n    },\r\n    {\r\n      type: \"separator\",\r\n      title: \"QUẢN TRỊ TÀI CHÍNH\",\r\n    },\r\n    {\r\n      key: \"transactions\",\r\n      title: \"Lịch sử thanh toán\",\r\n      url: \"/transactions\",\r\n      icon: IconCurrencyDollar,\r\n    },\r\n    {\r\n      type: \"separator\",\r\n      title: \"QUẢN TRỊ THÔNG TIN CÁ NHÂN\",\r\n    },\r\n    {\r\n      key: \"user-profile\",\r\n      title: \"Hồ sơ cá nhân\",\r\n      url: \"/profile\",\r\n      icon: IconUser,\r\n    },\r\n    {\r\n      key: \"user-kyc\",\r\n      title: \"Xác thực danh tính\",\r\n      url: \"/kyc\",\r\n      icon: IconUserCheck,\r\n    },\r\n  ],\r\n};\r\n\r\n// Dữ liệu menu cho ADMIN\r\nconst adminNavData = {\r\n  navMain: [\r\n    {\r\n      key: \"admin-dashboard\",\r\n      title: \"Bảng quản trị\",\r\n      url: \"/admin/dashboard\",\r\n      icon: IconDashboard,\r\n    },\r\n    {\r\n      key: \"admin-reports\",\r\n      title: \"Báo cáo thống kê\",\r\n      url: \"/admin/reports\",\r\n      icon: IconDeviceAnalytics,\r\n    },\r\n    {\r\n      type: \"separator\",\r\n      title: \"QUẢN TRỊ BẠC TRỰC TUYẾN\",\r\n    },\r\n    {\r\n      key: \"admin-order-books\",\r\n      title: \"Giao dịch online\",\r\n      url: \"/admin/order-books\",\r\n      icon: IconExchange,\r\n    },\r\n    {\r\n      key: \"admin-token-assets\",\r\n      title: \"Quản lý tài sản\",\r\n      url: \"/admin/token-assets\",\r\n      icon: IconMoneybag,\r\n    },\r\n    {\r\n      key: \"tokens\",\r\n      title: \"Quản lý sản phẩm\",\r\n      url: \"/admin/tokens\",\r\n      icon: IconPackage,\r\n    },\r\n    {\r\n      key: \"token-categories\",\r\n      title: \"Danh mục sản phẩm\",\r\n      url: \"/admin/token-categories\",\r\n      icon: IconCategory,\r\n    },\r\n    {\r\n      type: \"separator\",\r\n      title: \"QUẢN TRỊ SẢN PHẨM BẠC\",\r\n    },\r\n    {\r\n      key: \"admin-ecom-orders\",\r\n      title: \"Quản lý đơn hàng\",\r\n      url: \"/admin/ecom-orders\",\r\n      icon: IconShoppingCart,\r\n    },\r\n    {\r\n      key: \"ecom-products\",\r\n      title: \"Quản lý sản phẩm\",\r\n      url: \"/admin/ecom-products\",\r\n      icon: IconPackageImport,\r\n    },\r\n    {\r\n      key: \"ecom-product-categories\",\r\n      title: \"Danh mục sản phẩm\",\r\n      url: \"/admin/ecom-product-categories\",\r\n      icon: IconFolders,\r\n    },\r\n    {\r\n      type: \"separator\",\r\n      title: \"QUẢN TRỊ TÀI CHÍNH\",\r\n    },\r\n    {\r\n      key: \"admin-wallets\",\r\n      title: \"Quản lý tài khoản\",\r\n      url: \"/admin/wallets\",\r\n      icon: IconWallet,\r\n    },\r\n    {\r\n      key: \"admin-transactions\",\r\n      title: \"Lịch sử thanh toán\",\r\n      url: \"/admin/transactions\",\r\n      icon: IconCurrencyDollar,\r\n    },\r\n    {\r\n      key: \"admin-payment-methods\",\r\n      title: \"Phương thức thanh toán\",\r\n      url: \"/admin/payment-methods\",\r\n      icon: IconCreditCard,\r\n    },\r\n    {\r\n      key: \"admin-banks\",\r\n      title: \"Quản lý ngân hàng\",\r\n      url: \"/admin/banks\",\r\n      icon: IconBuildingBank,\r\n    },\r\n    {\r\n      type: \"separator\",\r\n      title: \"QUẢN TRỊ ĐIỂM THƯỞNG\",\r\n    },\r\n    {\r\n      key: \"admin-crypto-wallets\",\r\n      title: \"Tài khoản điểm thưởng\",\r\n      url: \"/admin/crypto-wallets\",\r\n      icon: IconGift,\r\n    },\r\n    {\r\n      key: \"admin-crypto-transactions\",\r\n      title: \"Lịch sử điểm thưởng\",\r\n      url: \"/admin/crypto-transactions\",\r\n      icon: IconHistory,\r\n    },\r\n    {\r\n      type: \"separator\",\r\n      title: \"QUẢN TRỊ ĐẠI LÝ\",\r\n    },\r\n    {\r\n      key: \"admin-agents\",\r\n      title: \"Quản lý đại lý\",\r\n      url: \"/admin/agents\",\r\n      icon: IconUserStar,\r\n    },\r\n    {\r\n      key: \"admin-agent-commissions\",\r\n      title: \"Lịch sử hoa hồng\",\r\n      url: \"/admin/agent-commissions\",\r\n      icon: IconCash,\r\n    },\r\n    {\r\n      type: \"separator\",\r\n      title: \"QUẢN TRỊ NỘI DUNG\",\r\n    },\r\n    {\r\n      key: \"cms-categories\",\r\n      title: \"Chuyên mục\",\r\n      url: \"/admin/cms/categories\",\r\n      icon: IconCategory,\r\n    },\r\n    {\r\n      key: \"cms-posts\",\r\n      title: \"Bài viết\",\r\n      url: \"/admin/cms/posts\",\r\n      icon: IconNews,\r\n    },\r\n    {\r\n      key: \"cms-tags\",\r\n      title: \"Thẻ\",\r\n      url: \"/admin/cms/tags\",\r\n      icon: IconTags,\r\n    },\r\n    {\r\n      key: \"cms-pages\",\r\n      title: \"Trang\",\r\n      url: \"/admin/cms/pages\",\r\n      icon: IconBrandPagekit,\r\n    },\r\n    {\r\n      key: \"cms-banners\",\r\n      title: \"Banner\",\r\n      url: \"/admin/cms/banners\",\r\n      icon: IconPhoto,\r\n    },\r\n    {\r\n      key: \"cms-customer-feedbacks\",\r\n      title: \"Phản hồi khách hàng\",\r\n      url: \"/admin/cms/customer-feedbacks\",\r\n      icon: IconMessageCircle,\r\n    },\r\n    {\r\n      key: \"cms-showrooms\",\r\n      title: \"Cửa hàng/đại lý\",\r\n      url: \"/admin/cms/showrooms\",\r\n      icon: IconBuilding,\r\n    },\r\n    {\r\n      key: \"cms-partners\",\r\n      title: \"Đối tác\",\r\n      url: \"/admin/cms/partners\",\r\n      icon: IconHeartHandshake,\r\n    },\r\n    {\r\n      type: \"separator\",\r\n      title: \"QUẢN TRỊ NGƯỜI DÙNG\",\r\n    },\r\n    {\r\n      key: \"admin-users\",\r\n      title: \"Quản lý người dùng\",\r\n      url: \"/admin/users\",\r\n      icon: IconUserCog,\r\n    },\r\n    {\r\n      key: \"admin-roles\",\r\n      title: \"Vai trò & quyền hạn\",\r\n      url: \"/admin/roles\",\r\n      icon: IconShieldLock,\r\n    },\r\n    {\r\n      key: \"admin-kyc\",\r\n      title: \"Xác thực danh tính\",\r\n      url: \"/admin/kyc\",\r\n      icon: IconUserCheck,\r\n    },\r\n    {\r\n      type: \"separator\",\r\n      title: \"QUẢN TRỊ HỆ THỐNG\",\r\n    },\r\n    {\r\n      key: \"admin-profile\",\r\n      title: \"Hồ sơ quản trị viên\",\r\n      url: \"/admin/profile\",\r\n      icon: IconUserCircle,\r\n    },\r\n    {\r\n      key: \"admin-settings\",\r\n      title: \"Cấu hình hệ thống\",\r\n      url: \"/admin/settings\",\r\n      icon: IconSettings,\r\n    },\r\n    {\r\n      key: \"admin-website-settings\",\r\n      title: \"Cấu hình website\",\r\n      url: \"/admin/website-settings\",\r\n      icon: IconWorld,\r\n    },\r\n  ],\r\n};\r\n\r\n// Dữ liệu menu cho ADMIN\r\nconst agentNavData = {\r\n  navMain: [\r\n    {\r\n      key: \"agent-dashboard\",\r\n      title: \"Bảng quản trị\",\r\n      url: \"/agent/dashboard\",\r\n      icon: IconDashboard,\r\n    },\r\n    {\r\n      key: \"agent-reports\",\r\n      title: \"Báo cáo thống kê\",\r\n      url: \"/agent/reports\",\r\n      icon: IconDeviceAnalytics,\r\n    },\r\n    {\r\n      type: \"separator\",\r\n      title: \"QUẢN TRỊ BẠC TRỰC TUYẾN\",\r\n    },\r\n    {\r\n      key: \"agent-order-books\",\r\n      title: \"Giao dịch online\",\r\n      url: \"/agent/order-books\",\r\n      icon: IconExchange,\r\n    },\r\n    {\r\n      key: \"agent-token-assets\",\r\n      title: \"Quản lý tài sản\",\r\n      url: \"/agent/token-assets\",\r\n      icon: IconMoneybag,\r\n    },\r\n    // {\r\n    //   title: \"Quản lý sản phẩm\",\r\n    //   url: \"/agent/tokens\",\r\n    //   icon: IconPackage,\r\n    // },\r\n    // {\r\n    //   title: \"Danh mục sản phẩm\",\r\n    //   url: \"/agent/token-categories\",\r\n    //   icon: IconCategory,\r\n    // },\r\n    {\r\n      type: \"separator\",\r\n      title: \"QUẢN TRỊ SẢN PHẨM BẠC\",\r\n    },\r\n    {\r\n      key: \"agent-ecom-orders\",\r\n      title: \"Quản lý đơn hàng\",\r\n      url: \"/agent/ecom-orders\",\r\n      icon: IconShoppingCart,\r\n    },\r\n    // {\r\n    //   title: \"Quản lý sản phẩm\",\r\n    //   url: \"/agent/ecom-products\",\r\n    //   icon: IconPackageImport,\r\n    // },\r\n    // {\r\n    //   title: \"Danh mục sản phẩm\",\r\n    //   url: \"/agent/ecom-product-categories\",\r\n    //   icon: IconFolders,\r\n    // },\r\n    {\r\n      type: \"separator\",\r\n      title: \"QUẢN TRỊ TÀI CHÍNH\",\r\n    },\r\n    {\r\n      key: \"agent-wallets\",\r\n      title: \"Quản lý tài khoản\",\r\n      url: \"/agent/wallets\",\r\n      icon: IconWallet,\r\n    },\r\n    {\r\n      key: \"agent-transactions\",\r\n      title: \"Lịch sử thanh toán\",\r\n      url: \"/agent/transactions\",\r\n      icon: IconCurrencyDollar,\r\n    },\r\n    {\r\n      key: \"agent-payment-methods\",\r\n      title: \"Phương thức thanh toán\",\r\n      url: \"/agent/payment-methods\",\r\n      icon: IconCreditCard,\r\n    },\r\n    {\r\n      type: \"separator\",\r\n      title: \"QUẢN TRỊ ĐIỂM THƯỞNG\",\r\n    },\r\n    {\r\n      key: \"agent-crypto-wallets\",\r\n      title: \"Tài khoản điểm thưởng\",\r\n      url: \"/agent/crypto-wallets\",\r\n      icon: IconGift,\r\n    },\r\n    {\r\n      key: \"agent-crypto-transactions\",\r\n      title: \"Lịch sử điểm thưởng\",\r\n      url: \"/agent/crypto-transactions\",\r\n      icon: IconHistory,\r\n    },\r\n    {\r\n      type: \"separator\",\r\n      title: \"QUẢN TRỊ ĐẠI LÝ\",\r\n    },\r\n    {\r\n      key: \"agent-agents-hierarchical\",\r\n      title: \"Quản lý hoa hồng\",\r\n      url: \"/agent/agents-hierarchical\",\r\n      icon: IconUserStar,\r\n    },\r\n    {\r\n      key: \"agent-agent-commissions\",\r\n      title: \"Lịch sử hoa hồng\",\r\n      url: \"/agent/agent-commissions\",\r\n      icon: IconCash,\r\n    },\r\n    // {\r\n    //   type: \"separator\",\r\n    //   title: \"QUẢN LÝ NGƯỜI DÙNG\",\r\n    // },\r\n    // {\r\n    //   title: \"Quản lý người dùng\",\r\n    //   url: \"/agent/users\",\r\n    //   icon: IconUserCog,\r\n    // },\r\n    // {\r\n    //   title: \"Vai trò & quyền hạn\",\r\n    //   url: \"/agent/roles\",\r\n    //   icon: IconShieldLock,\r\n    // },\r\n    // {\r\n    //   title: \"Xác thực danh tính\",\r\n    //   url: \"/agent/kyc\",\r\n    //   icon: IconUserCheck,\r\n    // }\r\n  ],\r\n};\r\n\r\nconst data = {\r\n  navClouds: [\r\n    {\r\n      title: \"Capture\",\r\n      icon: IconFileAnalytics,\r\n      isActive: true,\r\n      url: \"#\",\r\n      items: [\r\n        {\r\n          title: \"Active Proposals\",\r\n          url: \"#\",\r\n        },\r\n        {\r\n          title: \"Archived\",\r\n          url: \"#\",\r\n        },\r\n      ],\r\n    },\r\n    {\r\n      title: \"Proposal\",\r\n      icon: IconFileDescription,\r\n      url: \"#\",\r\n      items: [\r\n        {\r\n          title: \"Active Proposals\",\r\n          url: \"#\",\r\n        },\r\n        {\r\n          title: \"Archived\",\r\n          url: \"#\",\r\n        },\r\n      ],\r\n    },\r\n    {\r\n      title: \"Prompts\",\r\n      icon: IconRobot,\r\n      url: \"#\",\r\n      items: [\r\n        {\r\n          title: \"Active Proposals\",\r\n          url: \"#\",\r\n        },\r\n        {\r\n          title: \"Archived\",\r\n          url: \"#\",\r\n        },\r\n      ],\r\n    },\r\n  ],\r\n  navSecondary: [\r\n  ],\r\n}\r\n\r\nexport function AppSidebar({ ...props }: React.ComponentProps<typeof Sidebar>) {\r\n  const { user, isLoading } = useAuth();\r\n  const { config } = useSiteMetadata();\r\n  const siteName = config?.['site_name'];\r\n  const siteLogo = config?.['site_logo'];\r\n\r\n  // Sử dụng ref để lưu menu cuối cùng khi user còn tồn tại\r\n  const lastValidNavDataRef = React.useRef(userNavData);\r\n  const lastValidUserRef = React.useRef<any>(null);\r\n\r\n  // Cập nhật ref khi có user hợp lệ - ưu tiên ADMIN trước\r\n  if (user && user.roles && Array.isArray(user.roles)) {\r\n    const isAdmin = user.roles.includes(\"ADMIN\");\r\n    const isAgent = user.roles.includes(\"AGENT\");\r\n    // Ưu tiên ADMIN trước, sau đó AGENT, cuối cùng USER\r\n    lastValidNavDataRef.current = isAdmin ? adminNavData : isAgent ? agentNavData : userNavData;\r\n    lastValidUserRef.current = user;\r\n  }\r\n\r\n  // Nếu đang loading (logout), giữ nguyên menu cuối cùng nhưng disable interactions\r\n  if (isLoading && !user) {\r\n    return (\r\n      <Sidebar collapsible=\"icon\" {...props} className=\"pointer-events-none opacity-75\">\r\n        <SidebarHeader>\r\n          <SidebarMenu>\r\n            <SidebarMenuItem>\r\n              <SidebarMenuButton\r\n                asChild\r\n                className=\"data-[slot=sidebar-menu-button]:!p-1.5\"\r\n              >\r\n                <div className=\"flex items-center\">\r\n                  <img src={siteLogo} alt={siteName} className=\"h-10 w-[160px] min-w-[40px] object-contain object-left\" />\r\n                </div>\r\n              </SidebarMenuButton>\r\n            </SidebarMenuItem>\r\n          </SidebarMenu>\r\n        </SidebarHeader>\r\n        <SidebarContent className=\"[&_.tabler-icon]:w-5 [&_.tabler-icon]:h-5 [&_.tabler-icon]:min-w-[20px] [&_.tabler-icon]:min-h-[20px]\">\r\n          {/* Giữ nguyên menu cuối cùng */}\r\n          <NavMain items={lastValidNavDataRef.current.navMain} />\r\n          <NavSecondary items={data.navSecondary} className=\"mt-auto\" />\r\n        </SidebarContent>\r\n        <SidebarFooter>\r\n          <NavUser />\r\n        </SidebarFooter>\r\n      </Sidebar>\r\n    );\r\n  }\r\n\r\n  // Guard: Nếu không có user sau khi loading xong\r\n  if (!user) {\r\n    return (\r\n      <Sidebar collapsible=\"icon\" {...props}>\r\n        <SidebarHeader>\r\n          <SidebarMenu>\r\n            <SidebarMenuItem>\r\n              <SidebarMenuButton\r\n                asChild\r\n                className=\"data-[slot=sidebar-menu-button]:!p-1.5\"\r\n              >\r\n                <Link href=\"/dashboard\" className=\"flex items-center\">\r\n                  <img src={siteLogo} alt={siteName} className=\"h-10 w-[160px] min-w-[40px] object-contain object-left\" />\r\n                </Link>\r\n              </SidebarMenuButton>\r\n            </SidebarMenuItem>\r\n          </SidebarMenu>\r\n        </SidebarHeader>\r\n        <SidebarContent className=\"[&_.tabler-icon]:w-5 [&_.tabler-icon]:h-5 [&_.tabler-icon]:min-w-[20px] [&_.tabler-icon]:min-h-[20px]\">\r\n          <NavMain items={userNavData.navMain} />\r\n          <NavSecondary items={data.navSecondary} className=\"mt-auto\" />\r\n        </SidebarContent>\r\n        <SidebarFooter>\r\n          <NavUser />\r\n        </SidebarFooter>\r\n      </Sidebar>\r\n    );\r\n  }\r\n\r\n  // Kiểm tra roles an toàn và ưu tiên ADMIN trước\r\n  const isAdmin = user.roles && Array.isArray(user.roles) && user.roles.includes(\"ADMIN\");\r\n  const isAgent = user.roles && Array.isArray(user.roles) && user.roles.includes(\"AGENT\");\r\n\r\n  // Debug logging để theo dõi role changes\r\n  React.useEffect(() => {\r\n    if (user && user.roles) {\r\n      console.log('Sidebar role check:', {\r\n        userId: user.id,\r\n        roles: user.roles,\r\n        isAdmin,\r\n        isAgent,\r\n        selectedNav: isAdmin ? 'admin' : isAgent ? 'agent' : 'user'\r\n      });\r\n    }\r\n  }, [user, isAdmin, isAgent]);\r\n\r\n  // Chọn menu dựa trên vai trò người dùng - ưu tiên ADMIN trước\r\n  const navData = isAdmin ? adminNavData : isAgent ? agentNavData : userNavData;\r\n\r\n  return (\r\n    <Sidebar collapsible=\"icon\" {...props}>\r\n      <SidebarHeader>\r\n        <SidebarMenu>\r\n          <SidebarMenuItem>\r\n            <SidebarMenuButton\r\n              asChild\r\n              className=\"data-[slot=sidebar-menu-button]:!p-1.5\"\r\n            >\r\n              <Link href={isAdmin ? \"/admin/dashboard\" : isAgent ? \"/agent/dashboard\" : \"/dashboard\"} \r\n                className=\"flex items-center\">\r\n                <img src={siteLogo} alt={siteName} className=\"h-10 w-[160px] min-w-[40px] object-contain object-left\" />\r\n              </Link>\r\n            </SidebarMenuButton>\r\n          </SidebarMenuItem>\r\n        </SidebarMenu>\r\n      </SidebarHeader>\r\n      <SidebarContent className=\"[&_.tabler-icon]:w-5 [&_.tabler-icon]:h-5 [&_.tabler-icon]:min-w-[20px] [&_.tabler-icon]:min-h-[20px]\">\r\n        <NavMain items={navData.navMain} />\r\n        <NavSecondary items={data.navSecondary} className=\"mt-auto\" />\r\n      </SidebarContent>\r\n      <SidebarFooter>\r\n        <NavUser />\r\n      </SidebarFooter>\r\n    </Sidebar>\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAuCA;AACA;AAEA;AACA;AACA;AACA;AAUA;;;AA1DA;;;;;;;;;;AA4DA,wBAAwB;AAExB,MAAM,cAAc;IAClB,SAAS;QACP;YACE,KAAK;YACL,OAAO;YACP,KAAK;YACL,qBAAqB;YACrB,MAAM,iUAAA,CAAA,gBAAa;QACrB;QACA,IAAI;QACJ,+BAA+B;QAC/B,qBAAqB;QACrB,6BAA6B;QAC7B,KAAK;QACL;YACE,MAAM;YACN,OAAO;QACT;QACA;YACE,KAAK;YACL,OAAO;YACP,KAAK;YACL,MAAM,qUAAA,CAAA,kBAAe;QACvB;QACA;YACE,KAAK;YACL,OAAO;YACP,KAAK;YACL,MAAM,6TAAA,CAAA,cAAW;QACnB;QACA;YACE,KAAK;YACL,OAAO;YACP,KAAK;YACL,MAAM,uTAAA,CAAA,WAAQ;QAChB;QACA;YACE,MAAM;YACN,OAAO;QACT;QACA;YACE,KAAK;YACL,OAAO;YACP,KAAK;YACL,MAAM,yUAAA,CAAA,oBAAiB;QACzB;QACA;YACE,KAAK;YACL,OAAO;YACP,KAAK;YACL,MAAM,uUAAA,CAAA,mBAAgB;QACxB;QACA;YACE,MAAM;YACN,OAAO;QACT;QACA;YACE,KAAK;YACL,OAAO;YACP,KAAK;YACL,MAAM,2UAAA,CAAA,qBAAkB;QAC1B;QACA;YACE,MAAM;YACN,OAAO;QACT;QACA;YACE,KAAK;YACL,OAAO;YACP,KAAK;YACL,MAAM,uTAAA,CAAA,WAAQ;QAChB;QACA;YACE,KAAK;YACL,OAAO;YACP,KAAK;YACL,MAAM,iUAAA,CAAA,gBAAa;QACrB;KACD;AACH;AAEA,yBAAyB;AACzB,MAAM,eAAe;IACnB,SAAS;QACP;YACE,KAAK;YACL,OAAO;YACP,KAAK;YACL,MAAM,iUAAA,CAAA,gBAAa;QACrB;QACA;YACE,KAAK;YACL,OAAO;YACP,KAAK;YACL,MAAM,6UAAA,CAAA,sBAAmB;QAC3B;QACA;YACE,MAAM;YACN,OAAO;QACT;QACA;YACE,KAAK;YACL,OAAO;YACP,KAAK;YACL,MAAM,+TAAA,CAAA,eAAY;QACpB;QACA;YACE,KAAK;YACL,OAAO;YACP,KAAK;YACL,MAAM,+TAAA,CAAA,eAAY;QACpB;QACA;YACE,KAAK;YACL,OAAO;YACP,KAAK;YACL,MAAM,6TAAA,CAAA,cAAW;QACnB;QACA;YACE,KAAK;YACL,OAAO;YACP,KAAK;YACL,MAAM,+TAAA,CAAA,eAAY;QACpB;QACA;YACE,MAAM;YACN,OAAO;QACT;QACA;YACE,KAAK;YACL,OAAO;YACP,KAAK;YACL,MAAM,uUAAA,CAAA,mBAAgB;QACxB;QACA;YACE,KAAK;YACL,OAAO;YACP,KAAK;YACL,MAAM,yUAAA,CAAA,oBAAiB;QACzB;QACA;YACE,KAAK;YACL,OAAO;YACP,KAAK;YACL,MAAM,6TAAA,CAAA,cAAW;QACnB;QACA;YACE,MAAM;YACN,OAAO;QACT;QACA;YACE,KAAK;YACL,OAAO;YACP,KAAK;YACL,MAAM,2TAAA,CAAA,aAAU;QAClB;QACA;YACE,KAAK;YACL,OAAO;YACP,KAAK;YACL,MAAM,2UAAA,CAAA,qBAAkB;QAC1B;QACA;YACE,KAAK;YACL,OAAO;YACP,KAAK;YACL,MAAM,mUAAA,CAAA,iBAAc;QACtB;QACA;YACE,KAAK;YACL,OAAO;YACP,KAAK;YACL,MAAM,uUAAA,CAAA,mBAAgB;QACxB;QACA;YACE,MAAM;YACN,OAAO;QACT;QACA;YACE,KAAK;YACL,OAAO;YACP,KAAK;YACL,MAAM,uTAAA,CAAA,WAAQ;QAChB;QACA;YACE,KAAK;YACL,OAAO;YACP,KAAK;YACL,MAAM,6TAAA,CAAA,cAAW;QACnB;QACA;YACE,MAAM;YACN,OAAO;QACT;QACA;YACE,KAAK;YACL,OAAO;YACP,KAAK;YACL,MAAM,+TAAA,CAAA,eAAY;QACpB;QACA;YACE,KAAK;YACL,OAAO;YACP,KAAK;YACL,MAAM,uTAAA,CAAA,WAAQ;QAChB;QACA;YACE,MAAM;YACN,OAAO;QACT;QACA;YACE,KAAK;YACL,OAAO;YACP,KAAK;YACL,MAAM,+TAAA,CAAA,eAAY;QACpB;QACA;YACE,KAAK;YACL,OAAO;YACP,KAAK;YACL,MAAM,uTAAA,CAAA,WAAQ;QAChB;QACA;YACE,KAAK;YACL,OAAO;YACP,KAAK;YACL,MAAM,uTAAA,CAAA,WAAQ;QAChB;QACA;YACE,KAAK;YACL,OAAO;YACP,KAAK;YACL,MAAM,uUAAA,CAAA,mBAAgB;QACxB;QACA;YACE,KAAK;YACL,OAAO;YACP,KAAK;YACL,MAAM,yTAAA,CAAA,YAAS;QACjB;QACA;YACE,KAAK;YACL,OAAO;YACP,KAAK;YACL,MAAM,yUAAA,CAAA,oBAAiB;QACzB;QACA;YACE,KAAK;YACL,OAAO;YACP,KAAK;YACL,MAAM,+TAAA,CAAA,eAAY;QACpB;QACA;YACE,KAAK;YACL,OAAO;YACP,KAAK;YACL,MAAM,2UAAA,CAAA,qBAAkB;QAC1B;QACA;YACE,MAAM;YACN,OAAO;QACT;QACA;YACE,KAAK;YACL,OAAO;YACP,KAAK;YACL,MAAM,6TAAA,CAAA,cAAW;QACnB;QACA;YACE,KAAK;YACL,OAAO;YACP,KAAK;YACL,MAAM,mUAAA,CAAA,iBAAc;QACtB;QACA;YACE,KAAK;YACL,OAAO;YACP,KAAK;YACL,MAAM,iUAAA,CAAA,gBAAa;QACrB;QACA;YACE,MAAM;YACN,OAAO;QACT;QACA;YACE,KAAK;YACL,OAAO;YACP,KAAK;YACL,MAAM,mUAAA,CAAA,iBAAc;QACtB;QACA;YACE,KAAK;YACL,OAAO;YACP,KAAK;YACL,MAAM,+TAAA,CAAA,eAAY;QACpB;QACA;YACE,KAAK;YACL,OAAO;YACP,KAAK;YACL,MAAM,yTAAA,CAAA,YAAS;QACjB;KACD;AACH;AAEA,yBAAyB;AACzB,MAAM,eAAe;IACnB,SAAS;QACP;YACE,KAAK;YACL,OAAO;YACP,KAAK;YACL,MAAM,iUAAA,CAAA,gBAAa;QACrB;QACA;YACE,KAAK;YACL,OAAO;YACP,KAAK;YACL,MAAM,6UAAA,CAAA,sBAAmB;QAC3B;QACA;YACE,MAAM;YACN,OAAO;QACT;QACA;YACE,KAAK;YACL,OAAO;YACP,KAAK;YACL,MAAM,+TAAA,CAAA,eAAY;QACpB;QACA;YACE,KAAK;YACL,OAAO;YACP,KAAK;YACL,MAAM,+TAAA,CAAA,eAAY;QACpB;QACA,IAAI;QACJ,+BAA+B;QAC/B,0BAA0B;QAC1B,uBAAuB;QACvB,KAAK;QACL,IAAI;QACJ,gCAAgC;QAChC,oCAAoC;QACpC,wBAAwB;QACxB,KAAK;QACL;YACE,MAAM;YACN,OAAO;QACT;QACA;YACE,KAAK;YACL,OAAO;YACP,KAAK;YACL,MAAM,uUAAA,CAAA,mBAAgB;QACxB;QACA,IAAI;QACJ,+BAA+B;QAC/B,iCAAiC;QACjC,6BAA6B;QAC7B,KAAK;QACL,IAAI;QACJ,gCAAgC;QAChC,2CAA2C;QAC3C,uBAAuB;QACvB,KAAK;QACL;YACE,MAAM;YACN,OAAO;QACT;QACA;YACE,KAAK;YACL,OAAO;YACP,KAAK;YACL,MAAM,2TAAA,CAAA,aAAU;QAClB;QACA;YACE,KAAK;YACL,OAAO;YACP,KAAK;YACL,MAAM,2UAAA,CAAA,qBAAkB;QAC1B;QACA;YACE,KAAK;YACL,OAAO;YACP,KAAK;YACL,MAAM,mUAAA,CAAA,iBAAc;QACtB;QACA;YACE,MAAM;YACN,OAAO;QACT;QACA;YACE,KAAK;YACL,OAAO;YACP,KAAK;YACL,MAAM,uTAAA,CAAA,WAAQ;QAChB;QACA;YACE,KAAK;YACL,OAAO;YACP,KAAK;YACL,MAAM,6TAAA,CAAA,cAAW;QACnB;QACA;YACE,MAAM;YACN,OAAO;QACT;QACA;YACE,KAAK;YACL,OAAO;YACP,KAAK;YACL,MAAM,+TAAA,CAAA,eAAY;QACpB;QACA;YACE,KAAK;YACL,OAAO;YACP,KAAK;YACL,MAAM,uTAAA,CAAA,WAAQ;QAChB;KAoBD;AACH;AAEA,MAAM,OAAO;IACX,WAAW;QACT;YACE,OAAO;YACP,MAAM,yUAAA,CAAA,oBAAiB;YACvB,UAAU;YACV,KAAK;YACL,OAAO;gBACL;oBACE,OAAO;oBACP,KAAK;gBACP;gBACA;oBACE,OAAO;oBACP,KAAK;gBACP;aACD;QACH;QACA;YACE,OAAO;YACP,MAAM,6UAAA,CAAA,sBAAmB;YACzB,KAAK;YACL,OAAO;gBACL;oBACE,OAAO;oBACP,KAAK;gBACP;gBACA;oBACE,OAAO;oBACP,KAAK;gBACP;aACD;QACH;QACA;YACE,OAAO;YACP,MAAM,yTAAA,CAAA,YAAS;YACf,KAAK;YACL,OAAO;gBACL;oBACE,OAAO;oBACP,KAAK;gBACP;gBACA;oBACE,OAAO;oBACP,KAAK;gBACP;aACD;QACH;KACD;IACD,cAAc,EACb;AACH;AAEO,SAAS,WAAW,EAAE,GAAG,OAA6C;;IAC3E,MAAM,EAAE,IAAI,EAAE,SAAS,EAAE,GAAG,CAAA,GAAA,wHAAA,CAAA,UAAO,AAAD;IAClC,MAAM,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,mIAAA,CAAA,kBAAe,AAAD;IACjC,MAAM,WAAW,QAAQ,CAAC,YAAY;IACtC,MAAM,WAAW,QAAQ,CAAC,YAAY;IAEtC,yDAAyD;IACzD,MAAM,sBAAsB,CAAA,GAAA,sQAAA,CAAA,SAAY,AAAD,EAAE;IACzC,MAAM,mBAAmB,CAAA,GAAA,sQAAA,CAAA,SAAY,AAAD,EAAO;IAE3C,wDAAwD;IACxD,IAAI,QAAQ,KAAK,KAAK,IAAI,MAAM,OAAO,CAAC,KAAK,KAAK,GAAG;QACnD,MAAM,UAAU,KAAK,KAAK,CAAC,QAAQ,CAAC;QACpC,MAAM,UAAU,KAAK,KAAK,CAAC,QAAQ,CAAC;QACpC,oDAAoD;QACpD,oBAAoB,OAAO,GAAG,UAAU,eAAe,UAAU,eAAe;QAChF,iBAAiB,OAAO,GAAG;IAC7B;IAEA,kFAAkF;IAClF,IAAI,aAAa,CAAC,MAAM;QACtB,qBACE,sSAAC,+HAAA,CAAA,UAAO;YAAC,aAAY;YAAQ,GAAG,KAAK;YAAE,WAAU;;8BAC/C,sSAAC,+HAAA,CAAA,gBAAa;8BACZ,cAAA,sSAAC,+HAAA,CAAA,cAAW;kCACV,cAAA,sSAAC,+HAAA,CAAA,kBAAe;sCACd,cAAA,sSAAC,+HAAA,CAAA,oBAAiB;gCAChB,OAAO;gCACP,WAAU;0CAEV,cAAA,sSAAC;oCAAI,WAAU;8CACb,cAAA,sSAAC;wCAAI,KAAK;wCAAU,KAAK;wCAAU,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAMvD,sSAAC,+HAAA,CAAA,iBAAc;oBAAC,WAAU;;sCAExB,sSAAC,6HAAA,CAAA,UAAO;4BAAC,OAAO,oBAAoB,OAAO,CAAC,OAAO;;;;;;sCACnD,sSAAC,kIAAA,CAAA,eAAY;4BAAC,OAAO,KAAK,YAAY;4BAAE,WAAU;;;;;;;;;;;;8BAEpD,sSAAC,+HAAA,CAAA,gBAAa;8BACZ,cAAA,sSAAC,6HAAA,CAAA,UAAO;;;;;;;;;;;;;;;;IAIhB;IAEA,gDAAgD;IAChD,IAAI,CAAC,MAAM;QACT,qBACE,sSAAC,+HAAA,CAAA,UAAO;YAAC,aAAY;YAAQ,GAAG,KAAK;;8BACnC,sSAAC,+HAAA,CAAA,gBAAa;8BACZ,cAAA,sSAAC,+HAAA,CAAA,cAAW;kCACV,cAAA,sSAAC,+HAAA,CAAA,kBAAe;sCACd,cAAA,sSAAC,+HAAA,CAAA,oBAAiB;gCAChB,OAAO;gCACP,WAAU;0CAEV,cAAA,sSAAC,wQAAA,CAAA,UAAI;oCAAC,MAAK;oCAAa,WAAU;8CAChC,cAAA,sSAAC;wCAAI,KAAK;wCAAU,KAAK;wCAAU,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAMvD,sSAAC,+HAAA,CAAA,iBAAc;oBAAC,WAAU;;sCACxB,sSAAC,6HAAA,CAAA,UAAO;4BAAC,OAAO,YAAY,OAAO;;;;;;sCACnC,sSAAC,kIAAA,CAAA,eAAY;4BAAC,OAAO,KAAK,YAAY;4BAAE,WAAU;;;;;;;;;;;;8BAEpD,sSAAC,+HAAA,CAAA,gBAAa;8BACZ,cAAA,sSAAC,6HAAA,CAAA,UAAO;;;;;;;;;;;;;;;;IAIhB;IAEA,gDAAgD;IAChD,MAAM,UAAU,KAAK,KAAK,IAAI,MAAM,OAAO,CAAC,KAAK,KAAK,KAAK,KAAK,KAAK,CAAC,QAAQ,CAAC;IAC/E,MAAM,UAAU,KAAK,KAAK,IAAI,MAAM,OAAO,CAAC,KAAK,KAAK,KAAK,KAAK,KAAK,CAAC,QAAQ,CAAC;IAE/E,yCAAyC;IACzC,CAAA,GAAA,sQAAA,CAAA,YAAe,AAAD;gCAAE;YACd,IAAI,QAAQ,KAAK,KAAK,EAAE;gBACtB,QAAQ,GAAG,CAAC,uBAAuB;oBACjC,QAAQ,KAAK,EAAE;oBACf,OAAO,KAAK,KAAK;oBACjB;oBACA;oBACA,aAAa,UAAU,UAAU,UAAU,UAAU;gBACvD;YACF;QACF;+BAAG;QAAC;QAAM;QAAS;KAAQ;IAE3B,8DAA8D;IAC9D,MAAM,UAAU,UAAU,eAAe,UAAU,eAAe;IAElE,qBACE,sSAAC,+HAAA,CAAA,UAAO;QAAC,aAAY;QAAQ,GAAG,KAAK;;0BACnC,sSAAC,+HAAA,CAAA,gBAAa;0BACZ,cAAA,sSAAC,+HAAA,CAAA,cAAW;8BACV,cAAA,sSAAC,+HAAA,CAAA,kBAAe;kCACd,cAAA,sSAAC,+HAAA,CAAA,oBAAiB;4BAChB,OAAO;4BACP,WAAU;sCAEV,cAAA,sSAAC,wQAAA,CAAA,UAAI;gCAAC,MAAM,UAAU,qBAAqB,UAAU,qBAAqB;gCACxE,WAAU;0CACV,cAAA,sSAAC;oCAAI,KAAK;oCAAU,KAAK;oCAAU,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAMvD,sSAAC,+HAAA,CAAA,iBAAc;gBAAC,WAAU;;kCACxB,sSAAC,6HAAA,CAAA,UAAO;wBAAC,OAAO,QAAQ,OAAO;;;;;;kCAC/B,sSAAC,kIAAA,CAAA,eAAY;wBAAC,OAAO,KAAK,YAAY;wBAAE,WAAU;;;;;;;;;;;;0BAEpD,sSAAC,+HAAA,CAAA,gBAAa;0BACZ,cAAA,sSAAC,6HAAA,CAAA,UAAO;;;;;;;;;;;;;;;;AAIhB;GA5HgB;;QACc,wHAAA,CAAA,UAAO;QAChB,mIAAA,CAAA,kBAAe;;;KAFpB", "debugId": null}}, {"offset": {"line": 4684, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/bloomgoo/gold-exchange-platform/frontend/components/layout/main-layout.tsx"], "sourcesContent": ["import { AppSidebar } from '@/components/app-sidebar';\r\nimport { SidebarProvider } from '@/components/ui/sidebar';\r\nimport { cn } from '@/lib/utils';\r\n\r\ninterface MainLayoutProps {\r\n   children: React.ReactNode;\r\n   header: React.ReactNode;\r\n   headersNumber?: 1 | 2;\r\n}\r\n\r\nexport default function MainLayout({ children, header, headersNumber = 2 }: MainLayoutProps) {\r\n   const height = {\r\n      1: 'h-[calc(100svh-40px)] lg:h-[calc(100svh-56px)]',\r\n      2: 'h-[calc(100svh-80px)] lg:h-[calc(100svh-96px)]',\r\n   };\r\n   return (\r\n      <SidebarProvider>\r\n         <AppSidebar variant=\"inset\" />\r\n         <div className=\"h-svh overflow-hidden lg:p-2 w-full\">\r\n            <div className=\"lg:border lg:rounded-md overflow-hidden flex flex-col items-center justify-start bg-container h-full w-full\">\r\n               {header}\r\n               <div\r\n                  className={cn(\r\n                     'overflow-auto w-full',\r\n                     height[headersNumber as keyof typeof height]\r\n                  )}\r\n               >\r\n                  {children}\r\n               </div>\r\n            </div>\r\n         </div>\r\n      </SidebarProvider>\r\n   );\r\n}\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AAAA;;;;;AAQe,SAAS,WAAW,EAAE,QAAQ,EAAE,MAAM,EAAE,gBAAgB,CAAC,EAAmB;IACxF,MAAM,SAAS;QACZ,GAAG;QACH,GAAG;IACN;IACA,qBACG,sSAAC,+HAAA,CAAA,kBAAe;;0BACb,sSAAC,gIAAA,CAAA,aAAU;gBAAC,SAAQ;;;;;;0BACpB,sSAAC;gBAAI,WAAU;0BACZ,cAAA,sSAAC;oBAAI,WAAU;;wBACX;sCACD,sSAAC;4BACE,WAAW,CAAA,GAAA,+HAAA,CAAA,KAAE,AAAD,EACT,wBACA,MAAM,CAAC,cAAqC;sCAG9C;;;;;;;;;;;;;;;;;;;;;;;AAMnB;KAvBwB", "debugId": null}}, {"offset": {"line": 4754, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/bloomgoo/gold-exchange-platform/frontend/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\r\nimport { Slot } from \"@radix-ui/react-slot\"\r\nimport { cva, type VariantProps } from \"class-variance-authority\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst badgeVariants = cva(\r\n  \"inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden\",\r\n  {\r\n    variants: {\r\n      variant: {\r\n        default:\r\n          \"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90\",\r\n        secondary:\r\n          \"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90\",\r\n        destructive:\r\n          \"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\r\n        outline:\r\n          \"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground\",\r\n      },\r\n    },\r\n    defaultVariants: {\r\n      variant: \"default\",\r\n    },\r\n  }\r\n)\r\n\r\nfunction Badge({\r\n  className,\r\n  variant,\r\n  asChild = false,\r\n  ...props\r\n}: React.ComponentProps<\"span\"> &\r\n  VariantProps<typeof badgeVariants> & { asChild?: boolean }) {\r\n  const Comp = asChild ? Slot : \"span\"\r\n\r\n  return (\r\n    <Comp\r\n      data-slot=\"badge\"\r\n      className={cn(badgeVariants({ variant }), className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport { Badge, badgeVariants }\r\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;AAAA;;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,8OAAA,CAAA,MAAG,AAAD,EACtB,kZACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,SAAS,MAAM,EACb,SAAS,EACT,OAAO,EACP,UAAU,KAAK,EACf,GAAG,OAEuD;IAC1D,MAAM,OAAO,UAAU,uSAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,sSAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,+HAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;AAGf;KAhBS", "debugId": null}}, {"offset": {"line": 4807, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/bloomgoo/gold-exchange-platform/frontend/components/common/SilverPriceIndicator.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { usePolygonForex } from '@/services/websocket/polygon-forex-socket.service';\r\nimport { Badge } from '@/components/ui/badge';\r\n\r\nexport function SilverPriceIndicator() {\r\n  const {\r\n    currentQuote,\r\n    isMockData\r\n  } = usePolygonForex();\r\n\r\n  // Format giá\r\n  const formatPrice = (price?: number) => {\r\n    if (price === undefined || price === null) return '---.----';\r\n    return price.toFixed(4);\r\n  };\r\n\r\n  // Format giá VND\r\n  const formatVND = (price?: number) => {\r\n    if (price === undefined || price === null) return '---,---';\r\n    return price.toLocaleString('vi-VN');\r\n  };\r\n\r\n  // Xác định màu sắc cho chênh lệch giá\r\n  const getSpreadColor = (spread?: number) => {\r\n    if (spread === undefined || spread === null) return 'bg-gray-500';\r\n    if (spread > 0.1) return 'bg-red-500';\r\n    if (spread < 0.05) return 'bg-green-500';\r\n    return 'bg-yellow-500';\r\n  };\r\n\r\n  return (\r\n    <div className=\"flex items-center gap-2\">\r\n      <Badge variant=\"outline\" className=\"flex items-center gap-1 py-1\">\r\n        <span className=\"text-xs font-medium\">AXGUSD:</span>\r\n        <span className=\"text-xs font-bold\">{formatPrice(currentQuote?.bidPrice)}</span>\r\n        {currentQuote?.spread !== undefined && (\r\n          <span className={`inline-block w-2 h-2 rounded-full ${getSpreadColor(currentQuote?.spread)}`} />\r\n        )}\r\n      </Badge>\r\n      \r\n      {isMockData && (\r\n        <Badge variant=\"outline\" className=\"bg-yellow-50 text-yellow-700 border-yellow-200 text-xs\">\r\n          Mẫu\r\n        </Badge>\r\n      )}\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;;;AAHA;;;AAKO,SAAS;;IACd,MAAM,EACJ,YAAY,EACZ,UAAU,EACX,GAAG,CAAA,GAAA,iKAAA,CAAA,kBAAe,AAAD;IAElB,aAAa;IACb,MAAM,cAAc,CAAC;QACnB,IAAI,UAAU,aAAa,UAAU,MAAM,OAAO;QAClD,OAAO,MAAM,OAAO,CAAC;IACvB;IAEA,iBAAiB;IACjB,MAAM,YAAY,CAAC;QACjB,IAAI,UAAU,aAAa,UAAU,MAAM,OAAO;QAClD,OAAO,MAAM,cAAc,CAAC;IAC9B;IAEA,sCAAsC;IACtC,MAAM,iBAAiB,CAAC;QACtB,IAAI,WAAW,aAAa,WAAW,MAAM,OAAO;QACpD,IAAI,SAAS,KAAK,OAAO;QACzB,IAAI,SAAS,MAAM,OAAO;QAC1B,OAAO;IACT;IAEA,qBACE,sSAAC;QAAI,WAAU;;0BACb,sSAAC,6HAAA,CAAA,QAAK;gBAAC,SAAQ;gBAAU,WAAU;;kCACjC,sSAAC;wBAAK,WAAU;kCAAsB;;;;;;kCACtC,sSAAC;wBAAK,WAAU;kCAAqB,YAAY,cAAc;;;;;;oBAC9D,cAAc,WAAW,2BACxB,sSAAC;wBAAK,WAAW,CAAC,kCAAkC,EAAE,eAAe,cAAc,SAAS;;;;;;;;;;;;YAI/F,4BACC,sSAAC,6HAAA,CAAA,QAAK;gBAAC,SAAQ;gBAAU,WAAU;0BAAyD;;;;;;;;;;;;AAMpG;GA3CgB;;QAIV,iKAAA,CAAA,kBAAe;;;KAJL", "debugId": null}}, {"offset": {"line": 4907, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/bloomgoo/gold-exchange-platform/frontend/components/layout/headers/page-header.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React from \"react\";\r\nimport { cn } from \"@/lib/utils\";\r\nimport { SidebarTrigger } from \"@/components/ui/sidebar\";\r\nimport { SilverPriceIndicator } from \"@/components/common/SilverPriceIndicator\";\r\n\r\ninterface PageHeaderProps {\r\n  title: string;\r\n  description?: string;\r\n  actions?: React.ReactNode;\r\n  className?: string;\r\n}\r\n\r\nexport function PageHeader({ title, description, actions, className }: PageHeaderProps) {\r\n  return (\r\n    <div className={cn(\"flex items-center justify-between gap-2 p-4 border-b w-full\", className)}>\r\n      {/* Left: SidebarTrigger and Title/Description */}\r\n      <div className=\"flex items-center gap-3 overflow-hidden\">\r\n        <SidebarTrigger className=\"-ml-1 flex-shrink-0\" />\r\n        <div className=\"flex flex-col gap-1 overflow-hidden\">\r\n          <h1 className=\"text-xl font-semibold truncate\">{title}</h1>\r\n          {description && <p className=\"text-sm text-muted-foreground truncate\">{description}</p>}\r\n        </div>\r\n      </div>\r\n\r\n      {/* Right: Price Indicator and Actions */}\r\n      <div className=\"flex items-center gap-2 ml-4\">\r\n        <SilverPriceIndicator />\r\n        {actions && (\r\n          <div className=\"flex items-center gap-2 ml-2\">\r\n            {actions}\r\n          </div>\r\n        )}\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAGA;AAAA;AACA;AACA;AALA;;;;;AAcO,SAAS,WAAW,EAAE,KAAK,EAAE,WAAW,EAAE,OAAO,EAAE,SAAS,EAAmB;IACpF,qBACE,sSAAC;QAAI,WAAW,CAAA,GAAA,+HAAA,CAAA,KAAE,AAAD,EAAE,+DAA+D;;0BAEhF,sSAAC;gBAAI,WAAU;;kCACb,sSAAC,+HAAA,CAAA,iBAAc;wBAAC,WAAU;;;;;;kCAC1B,sSAAC;wBAAI,WAAU;;0CACb,sSAAC;gCAAG,WAAU;0CAAkC;;;;;;4BAC/C,6BAAe,sSAAC;gCAAE,WAAU;0CAA0C;;;;;;;;;;;;;;;;;;0BAK3E,sSAAC;gBAAI,WAAU;;kCACb,sSAAC,gJAAA,CAAA,uBAAoB;;;;;oBACpB,yBACC,sSAAC;wBAAI,WAAU;kCACZ;;;;;;;;;;;;;;;;;;AAMb;KAvBgB", "debugId": null}}, {"offset": {"line": 5006, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/bloomgoo/gold-exchange-platform/frontend/components/layout-provider.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React, { createContext, useContext, ReactNode } from \"react\";\r\nimport { useAuth } from \"@/hooks/use-auth\";\r\nimport { AppLayout } from \"@/components/app-layout\";\r\nimport MainLayout from \"@/components/layout/main-layout\";\r\nimport { PageHeader } from \"@/components/layout/headers/page-header\";\r\n\r\ntype LayoutContextType = {\r\n  isAdmin: boolean;\r\n};\r\n\r\nconst LayoutContext = createContext<LayoutContextType | undefined>(undefined);\r\n\r\nexport function useLayout() {\r\n  const context = useContext(LayoutContext);\r\n  if (context === undefined) {\r\n    throw new Error(\"useLayout must be used within a LayoutProvider\");\r\n  }\r\n  return context;\r\n}\r\n\r\ninterface LayoutProviderProps {\r\n  children: ReactNode;\r\n  header?: ReactNode;\r\n  title?: string;\r\n  description?: string;\r\n  actions?: ReactNode;\r\n}\r\n\r\nexport function LayoutProvider({\r\n  children,\r\n  header,\r\n  title,\r\n  description,\r\n  actions,\r\n}: LayoutProviderProps) {\r\n  const { user } = useAuth();\r\n  const isAdmin = user?.roles.includes(\"ADMIN\");\r\n\r\n  // Nếu là admin, sử dụng layout mới\r\n  if (isAdmin) {\r\n    return (\r\n      <LayoutContext.Provider value={{ isAdmin }}>\r\n        <MainLayout\r\n          header={\r\n            header || (\r\n              <PageHeader\r\n                title={title || \"Dashboard\"}\r\n                description={description}\r\n                actions={actions}\r\n              />\r\n            )\r\n          }\r\n        >\r\n          {children}\r\n        </MainLayout>\r\n      </LayoutContext.Provider>\r\n    );\r\n  }\r\n\r\n  // Nếu là user thông thường, sử dụng layout cũ\r\n  return (\r\n    <LayoutContext.Provider value={{ isAdmin: isAdmin ?? false }}>\r\n      <MainLayout\r\n          header={\r\n            header || (\r\n              <PageHeader\r\n                title={title || \"Dashboard\"}\r\n                description={description}\r\n                actions={actions}\r\n              />\r\n            )\r\n          }\r\n        >\r\n          {children}\r\n        </MainLayout>\r\n    </LayoutContext.Provider>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AAEA;AACA;;;AANA;;;;;AAYA,MAAM,8BAAgB,CAAA,GAAA,sQAAA,CAAA,gBAAa,AAAD,EAAiC;AAE5D,SAAS;;IACd,MAAM,UAAU,CAAA,GAAA,sQAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,YAAY,WAAW;QACzB,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT;GANgB;AAgBT,SAAS,eAAe,EAC7B,QAAQ,EACR,MAAM,EACN,KAAK,EACL,WAAW,EACX,OAAO,EACa;;IACpB,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,wHAAA,CAAA,UAAO,AAAD;IACvB,MAAM,UAAU,MAAM,MAAM,SAAS;IAErC,mCAAmC;IACnC,IAAI,SAAS;QACX,qBACE,sSAAC,cAAc,QAAQ;YAAC,OAAO;gBAAE;YAAQ;sBACvC,cAAA,sSAAC,0IAAA,CAAA,UAAU;gBACT,QACE,wBACE,sSAAC,qJAAA,CAAA,aAAU;oBACT,OAAO,SAAS;oBAChB,aAAa;oBACb,SAAS;;;;;;0BAKd;;;;;;;;;;;IAIT;IAEA,8CAA8C;IAC9C,qBACE,sSAAC,cAAc,QAAQ;QAAC,OAAO;YAAE,SAAS,WAAW;QAAM;kBACzD,cAAA,sSAAC,0IAAA,CAAA,UAAU;YACP,QACE,wBACE,sSAAC,qJAAA,CAAA,aAAU;gBACT,OAAO,SAAS;gBAChB,aAAa;gBACb,SAAS;;;;;;sBAKd;;;;;;;;;;;AAIX;IAjDgB;;QAOG,wHAAA,CAAA,UAAO;;;KAPV", "debugId": null}}, {"offset": {"line": 5108, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/bloomgoo/gold-exchange-platform/frontend/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction Card({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card\"\r\n      className={cn(\r\n        \"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CardHeader({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-header\"\r\n      className={cn(\r\n        \"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CardTitle({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-title\"\r\n      className={cn(\"leading-none font-semibold\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CardDescription({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-description\"\r\n      className={cn(\"text-muted-foreground text-sm\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CardAction({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-action\"\r\n      className={cn(\r\n        \"col-start-2 row-span-2 row-start-1 self-start justify-self-end\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CardContent({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-content\"\r\n      className={cn(\"px-6\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CardFooter({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-footer\"\r\n      className={cn(\"flex items-center px-6 [.border-t]:pt-6\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport {\r\n  Card,\r\n  CardHeader,\r\n  CardFooter,\r\n  CardTitle,\r\n  CardAction,\r\n  CardDescription,\r\n  CardContent,\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;AAAA;;;AAEA,SAAS,KAAK,EAAE,SAAS,EAAE,GAAG,OAAoC;IAChE,qBACE,sSAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,+HAAA,CAAA,KAAE,AAAD,EACV,qFACA;QAED,GAAG,KAAK;;;;;;AAGf;KAXS;AAaT,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,sSAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,+HAAA,CAAA,KAAE,AAAD,EACV,8JACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAoC;IACrE,qBACE,sSAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,+HAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC3E,qBACE,sSAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,+HAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,sSAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,+HAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,sSAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,+HAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QACrB,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,sSAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,+HAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf;MARS", "debugId": null}}, {"offset": {"line": 5224, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/bloomgoo/gold-exchange-platform/frontend/components/common/admin/reports/services/reports.service.ts"], "sourcesContent": ["'use client';\r\n\r\nimport { api } from '@/lib/api';\r\nimport { OverviewReportDto, TransactionReportDto, OrderBookReportDto, TokenAssetReportDto, DashboardReportDto } from '../types';\r\nimport { UserReportDto } from '../types/user-report';\r\nimport { ApiResponse } from '@/lib/response';\r\n\r\nexport class ReportsService {\r\n  /**\r\n   * Lấy báo cáo tổng quan\r\n   * @param period Kỳ báo cáo (day, week, month, quarter, year)\r\n   * @param startDate Ngày bắt đầu (YYYY-MM-DD)\r\n   * @param endDate Ngày kết thúc (YYYY-MM-DD)\r\n   * @returns Báo cáo tổng quan\r\n   */\r\n  static async getOverviewReport(\r\n    period: string = 'month',\r\n    startDate?: string,\r\n    endDate?: string,\r\n  ): Promise<OverviewReportDto> {\r\n    try {\r\n      let url = `/reports/overview?period=${period}`;\r\n      if (startDate) url += `&startDate=${startDate}`;\r\n      if (endDate) url += `&endDate=${endDate}`;\r\n\r\n      const response = await api.get<OverviewReportDto>(url);\r\n      return response;\r\n    } catch (error) {\r\n      console.error('Error fetching overview report:', error);\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Lấy báo cáo giao dịch\r\n   * @param period Kỳ báo cáo (day, week, month, quarter, year)\r\n   * @param startDate Ngày bắt đầu (YYYY-MM-DD)\r\n   * @param endDate Ngày kết thúc (YYYY-MM-DD)\r\n   * @returns Báo cáo giao dịch\r\n   */\r\n  static async getTransactionReport(\r\n    period: string = 'month',\r\n    startDate?: string,\r\n    endDate?: string,\r\n  ): Promise<TransactionReportDto> {\r\n    try {\r\n      let url = `/reports/transactions?period=${period}`;\r\n      if (startDate) url += `&startDate=${startDate}`;\r\n      if (endDate) url += `&endDate=${endDate}`;\r\n\r\n      const response = await api.get<TransactionReportDto>(url);\r\n      return response;\r\n    } catch (error) {\r\n      console.error('Error fetching transaction report:', error);\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Lấy báo cáo lệnh đặt hàng\r\n   * @param period Kỳ báo cáo (day, week, month, quarter, year)\r\n   * @param startDate Ngày bắt đầu (YYYY-MM-DD)\r\n   * @param endDate Ngày kết thúc (YYYY-MM-DD)\r\n   * @returns Báo cáo lệnh đặt hàng\r\n   */\r\n  static async getOrderBookReport(\r\n    period: string = 'month',\r\n    startDate?: string,\r\n    endDate?: string,\r\n  ): Promise<OrderBookReportDto> {\r\n    try {\r\n      let url = `/reports/order-books?period=${period}`;\r\n      if (startDate) url += `&startDate=${startDate}`;\r\n      if (endDate) url += `&endDate=${endDate}`;\r\n\r\n      const response = await api.get<OrderBookReportDto>(url);\r\n      return response;\r\n    } catch (error) {\r\n      console.error('Error fetching order book report:', error);\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Lấy báo cáo tài sản token\r\n   * @param period Kỳ báo cáo (day, week, month, quarter, year)\r\n   * @param startDate Ngày bắt đầu (YYYY-MM-DD)\r\n   * @param endDate Ngày kết thúc (YYYY-MM-DD)\r\n   * @returns Báo cáo tài sản token\r\n   */\r\n  static async getTokenAssetReport(\r\n    period: string = 'month',\r\n    startDate?: string,\r\n    endDate?: string,\r\n  ): Promise<TokenAssetReportDto> {\r\n    try {\r\n      let url = `/reports/token-assets?period=${period}`;\r\n      if (startDate) url += `&startDate=${startDate}`;\r\n      if (endDate) url += `&endDate=${endDate}`;\r\n\r\n      const response = await api.get<TokenAssetReportDto>(url);\r\n      return response;\r\n    } catch (error) {\r\n      console.error('Error fetching token asset report:', error);\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Lấy báo cáo người dùng\r\n   * @param period Kỳ báo cáo (day, week, month, quarter, year)\r\n   * @param startDate Ngày bắt đầu (YYYY-MM-DD)\r\n   * @param endDate Ngày kết thúc (YYYY-MM-DD)\r\n   * @returns Báo cáo người dùng\r\n   */\r\n  static async getUserReport(\r\n    period: string = 'month',\r\n    startDate?: string,\r\n    endDate?: string,\r\n  ): Promise<UserReportDto> {\r\n    try {\r\n      let url = `/reports/users?period=${period}`;\r\n      if (startDate) url += `&startDate=${startDate}`;\r\n      if (endDate) url += `&endDate=${endDate}`;\r\n\r\n      const response = await api.get<UserReportDto>(url);\r\n      return response;\r\n    } catch (error) {\r\n      console.error('Error fetching user report:', error);\r\n      // Dữ liệu mẫu khi API chưa hoàn thiện\r\n      const today = new Date();\r\n      const mockData: UserReportDto = {\r\n        period,\r\n        startDate: startDate || today.toISOString().split('T')[0],\r\n        endDate: endDate || today.toISOString().split('T')[0],\r\n        summary: {\r\n          total: 1250,\r\n          active: 1100,\r\n          inactive: 150,\r\n          kycVerified: 850,\r\n          emailVerified: 1200,\r\n          newUsers: 120,\r\n        },\r\n        byStatus: [\r\n          { status: 'Active', count: 1100, percentage: 88 },\r\n          { status: 'Inactive', count: 150, percentage: 12 },\r\n        ],\r\n        byRole: [\r\n          { role: 'USER', count: 1200, percentage: 96 },\r\n          { role: 'ADMIN', count: 30, percentage: 2.4 },\r\n          { role: 'AGENT', count: 20, percentage: 1.6 },\r\n        ],\r\n        byVerification: [\r\n          { verificationType: 'Email', verified: 1200, notVerified: 50, percentage: 96 },\r\n          { verificationType: 'KYC', verified: 850, notVerified: 400, percentage: 68 },\r\n        ],\r\n        byDate: [\r\n          { date: '2023-01-01', newCount: 10, activeCount: 950 },\r\n          { date: '2023-01-02', newCount: 12, activeCount: 962 },\r\n          { date: '2023-01-03', newCount: 8, activeCount: 970 },\r\n          { date: '2023-01-04', newCount: 15, activeCount: 985 },\r\n          { date: '2023-01-05', newCount: 9, activeCount: 994 },\r\n          { date: '2023-01-06', newCount: 11, activeCount: 1005 },\r\n          { date: '2023-01-07', newCount: 7, activeCount: 1012 },\r\n          { date: '2023-01-08', newCount: 14, activeCount: 1026 },\r\n          { date: '2023-01-09', newCount: 6, activeCount: 1032 },\r\n          { date: '2023-01-10', newCount: 13, activeCount: 1045 },\r\n          { date: '2023-01-11', newCount: 8, activeCount: 1053 },\r\n          { date: '2023-01-12', newCount: 10, activeCount: 1063 },\r\n          { date: '2023-01-13', newCount: 12, activeCount: 1075 },\r\n          { date: '2023-01-14', newCount: 9, activeCount: 1084 },\r\n          { date: '2023-01-15', newCount: 16, activeCount: 1100 },\r\n        ],\r\n        byActivity: [\r\n          { userId: '1', userName: 'Nguyễn Văn A', userEmail: '<EMAIL>', activityCount: 250, lastActivity: '2023-01-15T10:30:00Z' },\r\n          { userId: '2', userName: 'Trần Thị B', userEmail: '<EMAIL>', activityCount: 180, lastActivity: '2023-01-15T09:45:00Z' },\r\n          { userId: '3', userName: 'Lê Văn C', userEmail: '<EMAIL>', activityCount: 320, lastActivity: '2023-01-15T11:20:00Z' },\r\n          { userId: '4', userName: 'Phạm Thị D', userEmail: '<EMAIL>', activityCount: 150, lastActivity: '2023-01-14T16:10:00Z' },\r\n          { userId: '5', userName: 'Hoàng Văn E', userEmail: '<EMAIL>', activityCount: 280, lastActivity: '2023-01-15T08:55:00Z' },\r\n        ],\r\n      };\r\n      return mockData;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Lấy báo cáo bảng quản trị\r\n   * @returns Báo cáo bảng quản trị\r\n   */\r\n  static async getDashboardReport(): Promise<DashboardReportDto> {\r\n    try {\r\n      const response = await api.get<DashboardReportDto>('/reports/dashboard');\r\n      return response;\r\n    } catch (error) {\r\n      console.error('Error fetching dashboard report:', error);\r\n      throw error;\r\n    }\r\n  }\r\n}\r\n"], "names": [], "mappings": ";;;AAEA;AAFA;;AAOO,MAAM;IACX;;;;;;GAMC,GACD,aAAa,kBACX,SAAiB,OAAO,EACxB,SAAkB,EAClB,OAAgB,EACY;QAC5B,IAAI;YACF,IAAI,MAAM,CAAC,yBAAyB,EAAE,QAAQ;YAC9C,IAAI,WAAW,OAAO,CAAC,WAAW,EAAE,WAAW;YAC/C,IAAI,SAAS,OAAO,CAAC,SAAS,EAAE,SAAS;YAEzC,MAAM,WAAW,MAAM,6GAAA,CAAA,MAAG,CAAC,GAAG,CAAoB;YAClD,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,mCAAmC;YACjD,MAAM;QACR;IACF;IAEA;;;;;;GAMC,GACD,aAAa,qBACX,SAAiB,OAAO,EACxB,SAAkB,EAClB,OAAgB,EACe;QAC/B,IAAI;YACF,IAAI,MAAM,CAAC,6BAA6B,EAAE,QAAQ;YAClD,IAAI,WAAW,OAAO,CAAC,WAAW,EAAE,WAAW;YAC/C,IAAI,SAAS,OAAO,CAAC,SAAS,EAAE,SAAS;YAEzC,MAAM,WAAW,MAAM,6GAAA,CAAA,MAAG,CAAC,GAAG,CAAuB;YACrD,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,sCAAsC;YACpD,MAAM;QACR;IACF;IAEA;;;;;;GAMC,GACD,aAAa,mBACX,SAAiB,OAAO,EACxB,SAAkB,EAClB,OAAgB,EACa;QAC7B,IAAI;YACF,IAAI,MAAM,CAAC,4BAA4B,EAAE,QAAQ;YACjD,IAAI,WAAW,OAAO,CAAC,WAAW,EAAE,WAAW;YAC/C,IAAI,SAAS,OAAO,CAAC,SAAS,EAAE,SAAS;YAEzC,MAAM,WAAW,MAAM,6GAAA,CAAA,MAAG,CAAC,GAAG,CAAqB;YACnD,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,qCAAqC;YACnD,MAAM;QACR;IACF;IAEA;;;;;;GAMC,GACD,aAAa,oBACX,SAAiB,OAAO,EACxB,SAAkB,EAClB,OAAgB,EACc;QAC9B,IAAI;YACF,IAAI,MAAM,CAAC,6BAA6B,EAAE,QAAQ;YAClD,IAAI,WAAW,OAAO,CAAC,WAAW,EAAE,WAAW;YAC/C,IAAI,SAAS,OAAO,CAAC,SAAS,EAAE,SAAS;YAEzC,MAAM,WAAW,MAAM,6GAAA,CAAA,MAAG,CAAC,GAAG,CAAsB;YACpD,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,sCAAsC;YACpD,MAAM;QACR;IACF;IAEA;;;;;;GAMC,GACD,aAAa,cACX,SAAiB,OAAO,EACxB,SAAkB,EAClB,OAAgB,EACQ;QACxB,IAAI;YACF,IAAI,MAAM,CAAC,sBAAsB,EAAE,QAAQ;YAC3C,IAAI,WAAW,OAAO,CAAC,WAAW,EAAE,WAAW;YAC/C,IAAI,SAAS,OAAO,CAAC,SAAS,EAAE,SAAS;YAEzC,MAAM,WAAW,MAAM,6GAAA,CAAA,MAAG,CAAC,GAAG,CAAgB;YAC9C,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,+BAA+B;YAC7C,sCAAsC;YACtC,MAAM,QAAQ,IAAI;YAClB,MAAM,WAA0B;gBAC9B;gBACA,WAAW,aAAa,MAAM,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;gBACzD,SAAS,WAAW,MAAM,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;gBACrD,SAAS;oBACP,OAAO;oBACP,QAAQ;oBACR,UAAU;oBACV,aAAa;oBACb,eAAe;oBACf,UAAU;gBACZ;gBACA,UAAU;oBACR;wBAAE,QAAQ;wBAAU,OAAO;wBAAM,YAAY;oBAAG;oBAChD;wBAAE,QAAQ;wBAAY,OAAO;wBAAK,YAAY;oBAAG;iBAClD;gBACD,QAAQ;oBACN;wBAAE,MAAM;wBAAQ,OAAO;wBAAM,YAAY;oBAAG;oBAC5C;wBAAE,MAAM;wBAAS,OAAO;wBAAI,YAAY;oBAAI;oBAC5C;wBAAE,MAAM;wBAAS,OAAO;wBAAI,YAAY;oBAAI;iBAC7C;gBACD,gBAAgB;oBACd;wBAAE,kBAAkB;wBAAS,UAAU;wBAAM,aAAa;wBAAI,YAAY;oBAAG;oBAC7E;wBAAE,kBAAkB;wBAAO,UAAU;wBAAK,aAAa;wBAAK,YAAY;oBAAG;iBAC5E;gBACD,QAAQ;oBACN;wBAAE,MAAM;wBAAc,UAAU;wBAAI,aAAa;oBAAI;oBACrD;wBAAE,MAAM;wBAAc,UAAU;wBAAI,aAAa;oBAAI;oBACrD;wBAAE,MAAM;wBAAc,UAAU;wBAAG,aAAa;oBAAI;oBACpD;wBAAE,MAAM;wBAAc,UAAU;wBAAI,aAAa;oBAAI;oBACrD;wBAAE,MAAM;wBAAc,UAAU;wBAAG,aAAa;oBAAI;oBACpD;wBAAE,MAAM;wBAAc,UAAU;wBAAI,aAAa;oBAAK;oBACtD;wBAAE,MAAM;wBAAc,UAAU;wBAAG,aAAa;oBAAK;oBACrD;wBAAE,MAAM;wBAAc,UAAU;wBAAI,aAAa;oBAAK;oBACtD;wBAAE,MAAM;wBAAc,UAAU;wBAAG,aAAa;oBAAK;oBACrD;wBAAE,MAAM;wBAAc,UAAU;wBAAI,aAAa;oBAAK;oBACtD;wBAAE,MAAM;wBAAc,UAAU;wBAAG,aAAa;oBAAK;oBACrD;wBAAE,MAAM;wBAAc,UAAU;wBAAI,aAAa;oBAAK;oBACtD;wBAAE,MAAM;wBAAc,UAAU;wBAAI,aAAa;oBAAK;oBACtD;wBAAE,MAAM;wBAAc,UAAU;wBAAG,aAAa;oBAAK;oBACrD;wBAAE,MAAM;wBAAc,UAAU;wBAAI,aAAa;oBAAK;iBACvD;gBACD,YAAY;oBACV;wBAAE,QAAQ;wBAAK,UAAU;wBAAgB,WAAW;wBAA0B,eAAe;wBAAK,cAAc;oBAAuB;oBACvI;wBAAE,QAAQ;wBAAK,UAAU;wBAAc,WAAW;wBAAwB,eAAe;wBAAK,cAAc;oBAAuB;oBACnI;wBAAE,QAAQ;wBAAK,UAAU;wBAAY,WAAW;wBAAsB,eAAe;wBAAK,cAAc;oBAAuB;oBAC/H;wBAAE,QAAQ;wBAAK,UAAU;wBAAc,WAAW;wBAAwB,eAAe;wBAAK,cAAc;oBAAuB;oBACnI;wBAAE,QAAQ;wBAAK,UAAU;wBAAe,WAAW;wBAAyB,eAAe;wBAAK,cAAc;oBAAuB;iBACtI;YACH;YACA,OAAO;QACT;IACF;IAEA;;;GAGC,GACD,aAAa,qBAAkD;QAC7D,IAAI;YACF,MAAM,WAAW,MAAM,6GAAA,CAAA,MAAG,CAAC,GAAG,CAAqB;YACnD,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,oCAAoC;YAClD,MAAM;QACR;IACF;AACF", "debugId": null}}, {"offset": {"line": 5515, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/bloomgoo/gold-exchange-platform/frontend/components/common/SilverPriceDisplay.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { usePolygonForex } from '@/services/websocket/polygon-forex-socket.service';\r\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';\r\nimport { Badge } from '@/components/ui/badge';\r\nimport { Skeleton } from '@/components/ui/skeleton';\r\n\r\nexport function SilverPriceDisplay() {\r\n  const {\r\n    isConnected,\r\n    isMockData,\r\n    currentQuote,\r\n    lastError\r\n  } = usePolygonForex();\r\n\r\n  // Format giá\r\n  const formatPrice = (price?: number) => {\r\n    if (price === undefined || price === null) return '---.----';\r\n    return price.toFixed(4);\r\n  };\r\n\r\n  // Format giá VND\r\n  const formatVND = (price?: number) => {\r\n    if (price === undefined || price === null) return '---,---';\r\n    return price.toLocaleString('vi-VN');\r\n  };\r\n\r\n  // Format thời gian\r\n  const formatTime = (timestamp?: Date | string) => {\r\n    if (!timestamp) return '--:--:--';\r\n    try {\r\n      // Chuyển đổi timestamp thành đối tượng Date nếu nó là chuỗi\r\n      const date = typeof timestamp === 'string' ? new Date(timestamp) : timestamp;\r\n      // Kiểm tra xem date có hợp lệ không\r\n      if (isNaN(date.getTime())) {\r\n        return '--:--:--';\r\n      }\r\n      return date.toLocaleTimeString();\r\n    } catch (error) {\r\n      console.warn('Error formatting timestamp:', error);\r\n      return '--:--:--';\r\n    }\r\n  };\r\n\r\n  // Xác định màu sắc cho chênh lệch giá\r\n  const getSpreadColor = (spread?: number) => {\r\n    if (spread === undefined || spread === null) return 'text-gray-500';\r\n    if (spread > 0.1) return 'text-red-500';\r\n    if (spread < 0.05) return 'text-green-500';\r\n    return 'text-yellow-500';\r\n  };\r\n\r\n  return (\r\n    <Card>\r\n      <CardHeader className=\"pb-2\">\r\n        <div className=\"flex justify-between items-center\">\r\n          <CardTitle>Giá bạc (AXGUSD)</CardTitle>\r\n          <div className=\"flex items-center gap-2\">\r\n            {isConnected ? (\r\n              <Badge variant=\"outline\" className=\"bg-green-50 text-green-700 border-green-200\">\r\n                Đã kết nối\r\n              </Badge>\r\n            ) : (\r\n              <Badge variant=\"outline\" className=\"bg-red-50 text-red-700 border-red-200\">\r\n                Mất kết nối\r\n              </Badge>\r\n            )}\r\n\r\n            {isMockData && (\r\n              <Badge variant=\"outline\" className=\"bg-yellow-50 text-yellow-700 border-yellow-200\">\r\n                Dữ liệu mẫu\r\n              </Badge>\r\n            )}\r\n          </div>\r\n        </div>\r\n        <CardDescription>\r\n          Cập nhật lúc: {currentQuote ? formatTime(currentQuote.timestamp) : '--:--:--'}\r\n        </CardDescription>\r\n      </CardHeader>\r\n\r\n      <CardContent>\r\n        <div className=\"space-y-4\">\r\n          {/* Chia thành 2 cột chính đều nhau */}\r\n          <div className=\"grid grid-cols-2 gap-4\">\r\n            {/* Cột bên trái - Giá mua */}\r\n            <div className=\"space-y-4\">\r\n              {/* Giá mua USD */}\r\n              <div className=\"bg-blue-50 p-3 rounded-lg\">\r\n                <div className=\"text-sm text-blue-700 font-medium mb-1\">Giá mua (USD):</div>\r\n                {currentQuote ? (\r\n                  <div className=\"text-xl font-bold text-blue-600\">\r\n                    {formatPrice(currentQuote.bidPrice)}\r\n                  </div>\r\n                ) : (\r\n                  <Skeleton className=\"h-7 w-24\" />\r\n                )}\r\n              </div>\r\n\r\n              {/* Giá mua VND */}\r\n              <div className=\"bg-blue-50 p-3 rounded-lg\">\r\n                <div className=\"text-sm text-blue-700 font-medium mb-1\">Giá mua (VND):</div>\r\n                {currentQuote ? (\r\n                  <div className=\"text-xl font-bold text-blue-600\">\r\n                    {formatVND(currentQuote.buyPriceVND)}\r\n                  </div>\r\n                ) : (\r\n                  <Skeleton className=\"h-7 w-24\" />\r\n                )}\r\n              </div>\r\n\r\n              {/* Chênh lệch */}\r\n              <div className=\"p-3 rounded-lg border border-gray-100\">\r\n                <div className=\"text-sm text-muted-foreground mb-1\">Chênh lệch:</div>\r\n                {currentQuote ? (\r\n                  <div className={`text-sm font-medium ${getSpreadColor(currentQuote.spread)}`}>\r\n                    {formatPrice(currentQuote.spread)} ({currentQuote.spreadPercentage.toFixed(2)}%)\r\n                  </div>\r\n                ) : (\r\n                  <Skeleton className=\"h-5 w-20\" />\r\n                )}\r\n              </div>\r\n            </div>\r\n\r\n            {/* Cột bên phải - Giá bán */}\r\n            <div className=\"space-y-4\">\r\n              {/* Giá bán USD */}\r\n              <div className=\"bg-red-50 p-3 rounded-lg\">\r\n                <div className=\"text-sm text-red-700 font-medium mb-1\">Giá bán (USD):</div>\r\n                {currentQuote ? (\r\n                  <div className=\"text-xl font-bold text-red-600\">\r\n                    {formatPrice(currentQuote.askPrice)}\r\n                  </div>\r\n                ) : (\r\n                  <Skeleton className=\"h-7 w-24\" />\r\n                )}\r\n              </div>\r\n\r\n              {/* Giá bán VND */}\r\n              <div className=\"bg-red-50 p-3 rounded-lg\">\r\n                <div className=\"text-sm text-red-700 font-medium mb-1\">Giá bán (VND):</div>\r\n                {currentQuote ? (\r\n                  <div className=\"text-xl font-bold text-red-600\">\r\n                    {formatVND(currentQuote.sellPriceVND)}\r\n                  </div>\r\n                ) : (\r\n                  <Skeleton className=\"h-7 w-24\" />\r\n                )}\r\n              </div>\r\n\r\n              {/* Giá trung bình */}\r\n              <div className=\"p-3 rounded-lg border border-gray-100\">\r\n                <div className=\"text-sm text-muted-foreground mb-1\">Giá trung bình:</div>\r\n                {currentQuote ? (\r\n                  <div className=\"text-sm font-medium\">\r\n                    {formatPrice(currentQuote.averagePrice)}\r\n                  </div>\r\n                ) : (\r\n                  <Skeleton className=\"h-5 w-20\" />\r\n                )}\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          {/* Hiển thị lỗi nếu có */}\r\n          {lastError && (\r\n            <div className=\"mt-2 p-2 bg-red-50 text-red-700 text-sm rounded\">\r\n              <p>{lastError}</p>\r\n            </div>\r\n          )}\r\n        </div>\r\n      </CardContent>\r\n    </Card>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;;;AALA;;;;;AAOO,SAAS;;IACd,MAAM,EACJ,WAAW,EACX,UAAU,EACV,YAAY,EACZ,SAAS,EACV,GAAG,CAAA,GAAA,iKAAA,CAAA,kBAAe,AAAD;IAElB,aAAa;IACb,MAAM,cAAc,CAAC;QACnB,IAAI,UAAU,aAAa,UAAU,MAAM,OAAO;QAClD,OAAO,MAAM,OAAO,CAAC;IACvB;IAEA,iBAAiB;IACjB,MAAM,YAAY,CAAC;QACjB,IAAI,UAAU,aAAa,UAAU,MAAM,OAAO;QAClD,OAAO,MAAM,cAAc,CAAC;IAC9B;IAEA,mBAAmB;IACnB,MAAM,aAAa,CAAC;QAClB,IAAI,CAAC,WAAW,OAAO;QACvB,IAAI;YACF,4DAA4D;YAC5D,MAAM,OAAO,OAAO,cAAc,WAAW,IAAI,KAAK,aAAa;YACnE,oCAAoC;YACpC,IAAI,MAAM,KAAK,OAAO,KAAK;gBACzB,OAAO;YACT;YACA,OAAO,KAAK,kBAAkB;QAChC,EAAE,OAAO,OAAO;YACd,QAAQ,IAAI,CAAC,+BAA+B;YAC5C,OAAO;QACT;IACF;IAEA,sCAAsC;IACtC,MAAM,iBAAiB,CAAC;QACtB,IAAI,WAAW,aAAa,WAAW,MAAM,OAAO;QACpD,IAAI,SAAS,KAAK,OAAO;QACzB,IAAI,SAAS,MAAM,OAAO;QAC1B,OAAO;IACT;IAEA,qBACE,sSAAC,4HAAA,CAAA,OAAI;;0BACH,sSAAC,4HAAA,CAAA,aAAU;gBAAC,WAAU;;kCACpB,sSAAC;wBAAI,WAAU;;0CACb,sSAAC,4HAAA,CAAA,YAAS;0CAAC;;;;;;0CACX,sSAAC;gCAAI,WAAU;;oCACZ,4BACC,sSAAC,6HAAA,CAAA,QAAK;wCAAC,SAAQ;wCAAU,WAAU;kDAA8C;;;;;6DAIjF,sSAAC,6HAAA,CAAA,QAAK;wCAAC,SAAQ;wCAAU,WAAU;kDAAwC;;;;;;oCAK5E,4BACC,sSAAC,6HAAA,CAAA,QAAK;wCAAC,SAAQ;wCAAU,WAAU;kDAAiD;;;;;;;;;;;;;;;;;;kCAM1F,sSAAC,4HAAA,CAAA,kBAAe;;4BAAC;4BACA,eAAe,WAAW,aAAa,SAAS,IAAI;;;;;;;;;;;;;0BAIvE,sSAAC,4HAAA,CAAA,cAAW;0BACV,cAAA,sSAAC;oBAAI,WAAU;;sCAEb,sSAAC;4BAAI,WAAU;;8CAEb,sSAAC;oCAAI,WAAU;;sDAEb,sSAAC;4CAAI,WAAU;;8DACb,sSAAC;oDAAI,WAAU;8DAAyC;;;;;;gDACvD,6BACC,sSAAC;oDAAI,WAAU;8DACZ,YAAY,aAAa,QAAQ;;;;;yEAGpC,sSAAC,gIAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;;;;;;;;sDAKxB,sSAAC;4CAAI,WAAU;;8DACb,sSAAC;oDAAI,WAAU;8DAAyC;;;;;;gDACvD,6BACC,sSAAC;oDAAI,WAAU;8DACZ,UAAU,aAAa,WAAW;;;;;yEAGrC,sSAAC,gIAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;;;;;;;;sDAKxB,sSAAC;4CAAI,WAAU;;8DACb,sSAAC;oDAAI,WAAU;8DAAqC;;;;;;gDACnD,6BACC,sSAAC;oDAAI,WAAW,CAAC,oBAAoB,EAAE,eAAe,aAAa,MAAM,GAAG;;wDACzE,YAAY,aAAa,MAAM;wDAAE;wDAAG,aAAa,gBAAgB,CAAC,OAAO,CAAC;wDAAG;;;;;;yEAGhF,sSAAC,gIAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;;;;;;;;;;;;;;8CAM1B,sSAAC;oCAAI,WAAU;;sDAEb,sSAAC;4CAAI,WAAU;;8DACb,sSAAC;oDAAI,WAAU;8DAAwC;;;;;;gDACtD,6BACC,sSAAC;oDAAI,WAAU;8DACZ,YAAY,aAAa,QAAQ;;;;;yEAGpC,sSAAC,gIAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;;;;;;;;sDAKxB,sSAAC;4CAAI,WAAU;;8DACb,sSAAC;oDAAI,WAAU;8DAAwC;;;;;;gDACtD,6BACC,sSAAC;oDAAI,WAAU;8DACZ,UAAU,aAAa,YAAY;;;;;yEAGtC,sSAAC,gIAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;;;;;;;;sDAKxB,sSAAC;4CAAI,WAAU;;8DACb,sSAAC;oDAAI,WAAU;8DAAqC;;;;;;gDACnD,6BACC,sSAAC;oDAAI,WAAU;8DACZ,YAAY,aAAa,YAAY;;;;;yEAGxC,sSAAC,gIAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;wBAO3B,2BACC,sSAAC;4BAAI,WAAU;sCACb,cAAA,sSAAC;0CAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOlB;GAtKgB;;QAMV,iKAAA,CAAA,kBAAe;;;KANL", "debugId": null}}, {"offset": {"line": 5909, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/bloomgoo/gold-exchange-platform/frontend/app/%28admin%29/admin/dashboard/page.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { LayoutProvider } from \"@/components/layout-provider\";\r\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from \"@/components/ui/card\";\r\nimport { Button } from \"@/components/ui/button\";\r\nimport {\r\n  Loader2, RefreshCw, Users, UserCheck,\r\n  Wallet, DollarSign, CreditCard,\r\n  ShoppingCart, FileText, Database, Activity\r\n} from \"lucide-react\";\r\nimport { useState, useEffect } from \"react\";\r\nimport { ReportsService } from \"@/components/common/admin/reports/services/reports.service\";\r\nimport { DashboardReportDto } from \"@/components/common/admin/reports/types\";\r\nimport { SilverPriceDisplay } from \"@/components/common/SilverPriceDisplay\";\r\n\r\nexport default function AdminDashboardPage() {\r\n  const [isLoading, setIsLoading] = useState(true);\r\n  const [dashboardData, setDashboardData] = useState<DashboardReportDto | null>(null);\r\n\r\n  // Dữ liệu mặc định khi API chưa trả về\r\n  const defaultStats = {\r\n    // Người dùng\r\n    users: {\r\n      total: 0,\r\n      newToday: 0\r\n    },\r\n\r\n    // Hoạt động\r\n    activities: {\r\n      total: 0,\r\n      today: 0\r\n    },\r\n\r\n    // Giao dịch\r\n    transactions: {\r\n      total: 0,\r\n      today: 0,\r\n      totalValue: 0\r\n    },\r\n\r\n    // Ví\r\n    wallets: {\r\n      totalBalance: 0\r\n    },\r\n\r\n    // Lệnh đặt hàng\r\n    orderBooks: {\r\n      total: 0,\r\n      buyToday: 0,\r\n      sellToday: 0,\r\n      completedToday: 0\r\n    },\r\n\r\n    // Tài sản\r\n    tokenAssets: {\r\n      total: 0,\r\n      totalValue: 0,\r\n      totalUsers: 0,\r\n      totalTokens: 0\r\n    }\r\n  };\r\n\r\n  useEffect(() => {\r\n    fetchDashboardData();\r\n  }, []);\r\n\r\n  const fetchDashboardData = async () => {\r\n    setIsLoading(true);\r\n    try {\r\n      const data = await ReportsService.getDashboardReport();\r\n      setDashboardData(data);\r\n    } catch (error) {\r\n      console.error(\"Error fetching dashboard data:\", error);\r\n    } finally {\r\n      setIsLoading(false);\r\n    }\r\n  };\r\n\r\n  // Sử dụng dữ liệu từ API hoặc dữ liệu mặc định nếu API chưa trả về\r\n  const stats = dashboardData || defaultStats;\r\n\r\n  const formatCurrency = (amount: number) => {\r\n    return new Intl.NumberFormat('vi-VN', { style: 'currency', currency: 'VND' }).format(amount);\r\n  };\r\n\r\n  const handleRefresh = () => {\r\n    fetchDashboardData();\r\n  };\r\n\r\n  return (\r\n    <LayoutProvider\r\n      title=\"Bảng quản trị\"\r\n      description=\"Tổng quan về hệ thống\"\r\n      actions={\r\n        <Button variant=\"outline\" size=\"sm\" onClick={handleRefresh} disabled={isLoading}>\r\n          {isLoading ? <Loader2 className=\"mr-2 h-4 w-4 animate-spin\" /> : <RefreshCw className=\"mr-2 h-4 w-4\" />}\r\n          Làm mới\r\n        </Button>\r\n      }\r\n    >\r\n      <div className=\"p-4\">\r\n        {/* Component hiển thị giá bạc */}\r\n        <div className=\"mb-8\">\r\n          <SilverPriceDisplay />\r\n        </div>\r\n\r\n        <div className=\"grid grid-cols-1 md:grid-cols-2 gap-8\">\r\n          {/* Card 1: Người dùng & Hoạt động */}\r\n          <Card className=\"shadow-md h-full\">\r\n            <CardHeader className=\"pb-4\">\r\n              <CardTitle className=\"text-xl\">Người dùng & Hoạt động</CardTitle>\r\n              <CardDescription>Thống kê người dùng và hoạt động hệ thống</CardDescription>\r\n            </CardHeader>\r\n            <CardContent>\r\n              <div className=\"space-y-4\">\r\n                <div className=\"flex justify-between items-center p-2 bg-slate-50 rounded-md\">\r\n                  <div className=\"flex items-center\">\r\n                    <Users className=\"mr-3 h-5 w-5 text-blue-500\" />\r\n                    <span className=\"text-base font-medium\">Tổng người dùng (toàn thời gian)</span>\r\n                  </div>\r\n                  <span className=\"text-lg font-semibold\">{stats.users.total.toLocaleString()}</span>\r\n                </div>\r\n\r\n                <div className=\"flex justify-between items-center p-2 bg-slate-50 rounded-md\">\r\n                  <div className=\"flex items-center\">\r\n                    <UserCheck className=\"mr-3 h-5 w-5 text-green-500\" />\r\n                    <span className=\"text-base font-medium\">Người dùng mới (hôm nay)</span>\r\n                  </div>\r\n                  <span className=\"text-lg font-semibold\">{stats.users.newToday.toLocaleString()}</span>\r\n                </div>\r\n\r\n                <div className=\"flex justify-between items-center p-2 bg-slate-50 rounded-md\">\r\n                  <div className=\"flex items-center\">\r\n                    <Activity className=\"mr-3 h-5 w-5 text-purple-500\" />\r\n                    <span className=\"text-base font-medium\">Tổng hoạt động (toàn thời gian)</span>\r\n                  </div>\r\n                  <span className=\"text-lg font-semibold\">{stats.activities.total.toLocaleString()}</span>\r\n                </div>\r\n\r\n                <div className=\"flex justify-between items-center p-2 bg-slate-50 rounded-md\">\r\n                  <div className=\"flex items-center\">\r\n                    <Activity className=\"mr-3 h-5 w-5 text-indigo-500\" />\r\n                    <span className=\"text-base font-medium\">Hoạt động (hôm nay)</span>\r\n                  </div>\r\n                  <span className=\"text-lg font-semibold\">{stats.activities.today.toLocaleString()}</span>\r\n                </div>\r\n              </div>\r\n            </CardContent>\r\n          </Card>\r\n\r\n          {/* Card 2: Tài sản */}\r\n          <Card className=\"shadow-md h-full\">\r\n            <CardHeader className=\"pb-4\">\r\n              <CardTitle className=\"text-xl\">Tài sản</CardTitle>\r\n              <CardDescription>Thống kê tài sản </CardDescription>\r\n            </CardHeader>\r\n            <CardContent>\r\n              <div className=\"space-y-4\">\r\n                <div className=\"flex justify-between items-center p-2 bg-slate-50 rounded-md\">\r\n                  <div className=\"flex items-center\">\r\n                    <Database className=\"mr-3 h-5 w-5 text-purple-500\" />\r\n                    <span className=\"text-base font-medium\">Tổng tài sản (toàn thời gian)</span>\r\n                  </div>\r\n                  <span className=\"text-lg font-semibold\">{stats.tokenAssets.total.toLocaleString()}</span>\r\n                </div>\r\n\r\n                <div className=\"flex justify-between items-center p-2 bg-slate-50 rounded-md\">\r\n                  <div className=\"flex items-center\">\r\n                    <DollarSign className=\"mr-3 h-5 w-5 text-yellow-500\" />\r\n                    <span className=\"text-base font-medium\">Tổng giá trị tài sản (toàn thời gian)</span>\r\n                  </div>\r\n                  <span className=\"text-lg font-semibold\">{formatCurrency(stats.tokenAssets.totalValue)}</span>\r\n                </div>\r\n\r\n                <div className=\"flex justify-between items-center p-2 bg-slate-50 rounded-md\">\r\n                  <div className=\"flex items-center\">\r\n                    <Users className=\"mr-3 h-5 w-5 text-blue-500\" />\r\n                    <span className=\"text-base font-medium\">Người dùng có tài sản (toàn thời gian)</span>\r\n                  </div>\r\n                  <span className=\"text-lg font-semibold\">{stats.tokenAssets.totalUsers.toLocaleString()}</span>\r\n                </div>\r\n\r\n                <div className=\"flex justify-between items-center p-2 bg-slate-50 rounded-md\">\r\n                  <div className=\"flex items-center\">\r\n                    <Database className=\"mr-3 h-5 w-5 text-green-500\" />\r\n                    <span className=\"text-base font-medium\">Số loại token (toàn thời gian)</span>\r\n                  </div>\r\n                  <span className=\"text-lg font-semibold\">{stats.tokenAssets.totalTokens.toLocaleString()}</span>\r\n                </div>\r\n              </div>\r\n            </CardContent>\r\n          </Card>\r\n\r\n          {/* Card 3: Tài chính */}\r\n          <Card className=\"shadow-md h-full\">\r\n            <CardHeader className=\"pb-4\">\r\n              <CardTitle className=\"text-xl\">Tài chính</CardTitle>\r\n              <CardDescription>Thống kê giao dịch tài chính</CardDescription>\r\n            </CardHeader>\r\n            <CardContent>\r\n              <div className=\"space-y-4\">\r\n                <div className=\"flex justify-between items-center p-2 bg-slate-50 rounded-md\">\r\n                  <div className=\"flex items-center\">\r\n                    <CreditCard className=\"mr-3 h-5 w-5 text-blue-500\" />\r\n                    <span className=\"text-base font-medium\">Tổng giao dịch (toàn thời gian)</span>\r\n                  </div>\r\n                  <span className=\"text-lg font-semibold\">{stats.transactions.total.toLocaleString()}</span>\r\n                </div>\r\n\r\n                <div className=\"flex justify-between items-center p-2 bg-slate-50 rounded-md\">\r\n                  <div className=\"flex items-center\">\r\n                    <CreditCard className=\"mr-3 h-5 w-5 text-indigo-500\" />\r\n                    <span className=\"text-base font-medium\">Giao dịch (hôm nay)</span>\r\n                  </div>\r\n                  <span className=\"text-lg font-semibold\">{stats.transactions.today.toLocaleString()}</span>\r\n                </div>\r\n\r\n                <div className=\"flex justify-between items-center p-2 bg-slate-50 rounded-md\">\r\n                  <div className=\"flex items-center\">\r\n                    <DollarSign className=\"mr-3 h-5 w-5 text-green-500\" />\r\n                    <span className=\"text-base font-medium\">Tổng giá trị (toàn thời gian)</span>\r\n                  </div>\r\n                  <span className=\"text-lg font-semibold\">{formatCurrency(stats.transactions.totalValue)}</span>\r\n                </div>\r\n\r\n                <div className=\"flex justify-between items-center p-2 bg-slate-50 rounded-md\">\r\n                  <div className=\"flex items-center\">\r\n                    <Wallet className=\"mr-3 h-5 w-5 text-gray-500\" />\r\n                    <span className=\"text-base font-medium\">Số dư ví hệ thống (hiện tại)</span>\r\n                  </div>\r\n                  <span className=\"text-lg font-semibold\">{formatCurrency(stats.wallets.totalBalance)}</span>\r\n                </div>\r\n              </div>\r\n            </CardContent>\r\n          </Card>\r\n\r\n          {/* Card 4: Giao dịch mua - bán */}\r\n          <Card className=\"shadow-md h-full\">\r\n            <CardHeader className=\"pb-4\">\r\n              <CardTitle className=\"text-xl\">Giao dịch mua - bán</CardTitle>\r\n              <CardDescription>Thống kê lệnh</CardDescription>\r\n            </CardHeader>\r\n            <CardContent>\r\n              <div className=\"space-y-4\">\r\n                <div className=\"flex justify-between items-center p-2 bg-slate-50 rounded-md\">\r\n                  <div className=\"flex items-center\">\r\n                    <FileText className=\"mr-3 h-5 w-5 text-blue-500\" />\r\n                    <span className=\"text-base font-medium\">Tổng lệnh (toàn thời gian)</span>\r\n                  </div>\r\n                  <span className=\"text-lg font-semibold\">{stats.orderBooks.total.toLocaleString()}</span>\r\n                </div>\r\n\r\n                <div className=\"flex justify-between items-center p-2 bg-slate-50 rounded-md\">\r\n                  <div className=\"flex items-center\">\r\n                    <ShoppingCart className=\"mr-3 h-5 w-5 text-green-500\" />\r\n                    <span className=\"text-base font-medium\">Lệnh mua (hôm nay)</span>\r\n                  </div>\r\n                  <span className=\"text-lg font-semibold\">{stats.orderBooks.buyToday.toLocaleString()}</span>\r\n                </div>\r\n\r\n                <div className=\"flex justify-between items-center p-2 bg-slate-50 rounded-md\">\r\n                  <div className=\"flex items-center\">\r\n                    <ShoppingCart className=\"mr-3 h-5 w-5 text-red-500\" />\r\n                    <span className=\"text-base font-medium\">Lệnh bán (hôm nay)</span>\r\n                  </div>\r\n                  <span className=\"text-lg font-semibold\">{stats.orderBooks.sellToday.toLocaleString()}</span>\r\n                </div>\r\n\r\n                <div className=\"flex justify-between items-center p-2 bg-slate-50 rounded-md\">\r\n                  <div className=\"flex items-center\">\r\n                    <FileText className=\"mr-3 h-5 w-5 text-green-600\" />\r\n                    <span className=\"text-base font-medium\">Lệnh hoàn thành (hôm nay)</span>\r\n                  </div>\r\n                  <span className=\"text-lg font-semibold\">{stats.orderBooks.completedToday.toLocaleString()}</span>\r\n                </div>\r\n              </div>\r\n            </CardContent>\r\n          </Card>\r\n        </div>\r\n      </div>\r\n    </LayoutProvider>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAKA;AACA;AAEA;;;AAbA;;;;;;;;AAee,SAAS;;IACtB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,sQAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,sQAAA,CAAA,WAAQ,AAAD,EAA6B;IAE9E,uCAAuC;IACvC,MAAM,eAAe;QACnB,aAAa;QACb,OAAO;YACL,OAAO;YACP,UAAU;QACZ;QAEA,YAAY;QACZ,YAAY;YACV,OAAO;YACP,OAAO;QACT;QAEA,YAAY;QACZ,cAAc;YACZ,OAAO;YACP,OAAO;YACP,YAAY;QACd;QAEA,KAAK;QACL,SAAS;YACP,cAAc;QAChB;QAEA,gBAAgB;QAChB,YAAY;YACV,OAAO;YACP,UAAU;YACV,WAAW;YACX,gBAAgB;QAClB;QAEA,UAAU;QACV,aAAa;YACX,OAAO;YACP,YAAY;YACZ,YAAY;YACZ,aAAa;QACf;IACF;IAEA,CAAA,GAAA,sQAAA,CAAA,YAAS,AAAD;wCAAE;YACR;QACF;uCAAG,EAAE;IAEL,MAAM,qBAAqB;QACzB,aAAa;QACb,IAAI;YACF,MAAM,OAAO,MAAM,6KAAA,CAAA,iBAAc,CAAC,kBAAkB;YACpD,iBAAiB;QACnB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,kCAAkC;QAClD,SAAU;YACR,aAAa;QACf;IACF;IAEA,mEAAmE;IACnE,MAAM,QAAQ,iBAAiB;IAE/B,MAAM,iBAAiB,CAAC;QACtB,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;YAAE,OAAO;YAAY,UAAU;QAAM,GAAG,MAAM,CAAC;IACvF;IAEA,MAAM,gBAAgB;QACpB;IACF;IAEA,qBACE,sSAAC,oIAAA,CAAA,iBAAc;QACb,OAAM;QACN,aAAY;QACZ,uBACE,sSAAC,8HAAA,CAAA,SAAM;YAAC,SAAQ;YAAU,MAAK;YAAK,SAAS;YAAe,UAAU;;gBACnE,0BAAY,sSAAC,wSAAA,CAAA,UAAO;oBAAC,WAAU;;;;;2CAAiC,sSAAC,uSAAA,CAAA,YAAS;oBAAC,WAAU;;;;;;gBAAkB;;;;;;;kBAK5G,cAAA,sSAAC;YAAI,WAAU;;8BAEb,sSAAC;oBAAI,WAAU;8BACb,cAAA,sSAAC,8IAAA,CAAA,qBAAkB;;;;;;;;;;8BAGrB,sSAAC;oBAAI,WAAU;;sCAEb,sSAAC,4HAAA,CAAA,OAAI;4BAAC,WAAU;;8CACd,sSAAC,4HAAA,CAAA,aAAU;oCAAC,WAAU;;sDACpB,sSAAC,4HAAA,CAAA,YAAS;4CAAC,WAAU;sDAAU;;;;;;sDAC/B,sSAAC,4HAAA,CAAA,kBAAe;sDAAC;;;;;;;;;;;;8CAEnB,sSAAC,4HAAA,CAAA,cAAW;8CACV,cAAA,sSAAC;wCAAI,WAAU;;0DACb,sSAAC;gDAAI,WAAU;;kEACb,sSAAC;wDAAI,WAAU;;0EACb,sSAAC,2RAAA,CAAA,QAAK;gEAAC,WAAU;;;;;;0EACjB,sSAAC;gEAAK,WAAU;0EAAwB;;;;;;;;;;;;kEAE1C,sSAAC;wDAAK,WAAU;kEAAyB,MAAM,KAAK,CAAC,KAAK,CAAC,cAAc;;;;;;;;;;;;0DAG3E,sSAAC;gDAAI,WAAU;;kEACb,sSAAC;wDAAI,WAAU;;0EACb,sSAAC,uSAAA,CAAA,YAAS;gEAAC,WAAU;;;;;;0EACrB,sSAAC;gEAAK,WAAU;0EAAwB;;;;;;;;;;;;kEAE1C,sSAAC;wDAAK,WAAU;kEAAyB,MAAM,KAAK,CAAC,QAAQ,CAAC,cAAc;;;;;;;;;;;;0DAG9E,sSAAC;gDAAI,WAAU;;kEACb,sSAAC;wDAAI,WAAU;;0EACb,sSAAC,iSAAA,CAAA,WAAQ;gEAAC,WAAU;;;;;;0EACpB,sSAAC;gEAAK,WAAU;0EAAwB;;;;;;;;;;;;kEAE1C,sSAAC;wDAAK,WAAU;kEAAyB,MAAM,UAAU,CAAC,KAAK,CAAC,cAAc;;;;;;;;;;;;0DAGhF,sSAAC;gDAAI,WAAU;;kEACb,sSAAC;wDAAI,WAAU;;0EACb,sSAAC,iSAAA,CAAA,WAAQ;gEAAC,WAAU;;;;;;0EACpB,sSAAC;gEAAK,WAAU;0EAAwB;;;;;;;;;;;;kEAE1C,sSAAC;wDAAK,WAAU;kEAAyB,MAAM,UAAU,CAAC,KAAK,CAAC,cAAc;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAOtF,sSAAC,4HAAA,CAAA,OAAI;4BAAC,WAAU;;8CACd,sSAAC,4HAAA,CAAA,aAAU;oCAAC,WAAU;;sDACpB,sSAAC,4HAAA,CAAA,YAAS;4CAAC,WAAU;sDAAU;;;;;;sDAC/B,sSAAC,4HAAA,CAAA,kBAAe;sDAAC;;;;;;;;;;;;8CAEnB,sSAAC,4HAAA,CAAA,cAAW;8CACV,cAAA,sSAAC;wCAAI,WAAU;;0DACb,sSAAC;gDAAI,WAAU;;kEACb,sSAAC;wDAAI,WAAU;;0EACb,sSAAC,iSAAA,CAAA,WAAQ;gEAAC,WAAU;;;;;;0EACpB,sSAAC;gEAAK,WAAU;0EAAwB;;;;;;;;;;;;kEAE1C,sSAAC;wDAAK,WAAU;kEAAyB,MAAM,WAAW,CAAC,KAAK,CAAC,cAAc;;;;;;;;;;;;0DAGjF,sSAAC;gDAAI,WAAU;;kEACb,sSAAC;wDAAI,WAAU;;0EACb,sSAAC,ySAAA,CAAA,aAAU;gEAAC,WAAU;;;;;;0EACtB,sSAAC;gEAAK,WAAU;0EAAwB;;;;;;;;;;;;kEAE1C,sSAAC;wDAAK,WAAU;kEAAyB,eAAe,MAAM,WAAW,CAAC,UAAU;;;;;;;;;;;;0DAGtF,sSAAC;gDAAI,WAAU;;kEACb,sSAAC;wDAAI,WAAU;;0EACb,sSAAC,2RAAA,CAAA,QAAK;gEAAC,WAAU;;;;;;0EACjB,sSAAC;gEAAK,WAAU;0EAAwB;;;;;;;;;;;;kEAE1C,sSAAC;wDAAK,WAAU;kEAAyB,MAAM,WAAW,CAAC,UAAU,CAAC,cAAc;;;;;;;;;;;;0DAGtF,sSAAC;gDAAI,WAAU;;kEACb,sSAAC;wDAAI,WAAU;;0EACb,sSAAC,iSAAA,CAAA,WAAQ;gEAAC,WAAU;;;;;;0EACpB,sSAAC;gEAAK,WAAU;0EAAwB;;;;;;;;;;;;kEAE1C,sSAAC;wDAAK,WAAU;kEAAyB,MAAM,WAAW,CAAC,WAAW,CAAC,cAAc;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAO7F,sSAAC,4HAAA,CAAA,OAAI;4BAAC,WAAU;;8CACd,sSAAC,4HAAA,CAAA,aAAU;oCAAC,WAAU;;sDACpB,sSAAC,4HAAA,CAAA,YAAS;4CAAC,WAAU;sDAAU;;;;;;sDAC/B,sSAAC,4HAAA,CAAA,kBAAe;sDAAC;;;;;;;;;;;;8CAEnB,sSAAC,4HAAA,CAAA,cAAW;8CACV,cAAA,sSAAC;wCAAI,WAAU;;0DACb,sSAAC;gDAAI,WAAU;;kEACb,sSAAC;wDAAI,WAAU;;0EACb,sSAAC,ySAAA,CAAA,aAAU;gEAAC,WAAU;;;;;;0EACtB,sSAAC;gEAAK,WAAU;0EAAwB;;;;;;;;;;;;kEAE1C,sSAAC;wDAAK,WAAU;kEAAyB,MAAM,YAAY,CAAC,KAAK,CAAC,cAAc;;;;;;;;;;;;0DAGlF,sSAAC;gDAAI,WAAU;;kEACb,sSAAC;wDAAI,WAAU;;0EACb,sSAAC,ySAAA,CAAA,aAAU;gEAAC,WAAU;;;;;;0EACtB,sSAAC;gEAAK,WAAU;0EAAwB;;;;;;;;;;;;kEAE1C,sSAAC;wDAAK,WAAU;kEAAyB,MAAM,YAAY,CAAC,KAAK,CAAC,cAAc;;;;;;;;;;;;0DAGlF,sSAAC;gDAAI,WAAU;;kEACb,sSAAC;wDAAI,WAAU;;0EACb,sSAAC,ySAAA,CAAA,aAAU;gEAAC,WAAU;;;;;;0EACtB,sSAAC;gEAAK,WAAU;0EAAwB;;;;;;;;;;;;kEAE1C,sSAAC;wDAAK,WAAU;kEAAyB,eAAe,MAAM,YAAY,CAAC,UAAU;;;;;;;;;;;;0DAGvF,sSAAC;gDAAI,WAAU;;kEACb,sSAAC;wDAAI,WAAU;;0EACb,sSAAC,6RAAA,CAAA,SAAM;gEAAC,WAAU;;;;;;0EAClB,sSAAC;gEAAK,WAAU;0EAAwB;;;;;;;;;;;;kEAE1C,sSAAC;wDAAK,WAAU;kEAAyB,eAAe,MAAM,OAAO,CAAC,YAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAO1F,sSAAC,4HAAA,CAAA,OAAI;4BAAC,WAAU;;8CACd,sSAAC,4HAAA,CAAA,aAAU;oCAAC,WAAU;;sDACpB,sSAAC,4HAAA,CAAA,YAAS;4CAAC,WAAU;sDAAU;;;;;;sDAC/B,sSAAC,4HAAA,CAAA,kBAAe;sDAAC;;;;;;;;;;;;8CAEnB,sSAAC,4HAAA,CAAA,cAAW;8CACV,cAAA,sSAAC;wCAAI,WAAU;;0DACb,sSAAC;gDAAI,WAAU;;kEACb,sSAAC;wDAAI,WAAU;;0EACb,sSAAC,qSAAA,CAAA,WAAQ;gEAAC,WAAU;;;;;;0EACpB,sSAAC;gEAAK,WAAU;0EAAwB;;;;;;;;;;;;kEAE1C,sSAAC;wDAAK,WAAU;kEAAyB,MAAM,UAAU,CAAC,KAAK,CAAC,cAAc;;;;;;;;;;;;0DAGhF,sSAAC;gDAAI,WAAU;;kEACb,sSAAC;wDAAI,WAAU;;0EACb,sSAAC,6SAAA,CAAA,eAAY;gEAAC,WAAU;;;;;;0EACxB,sSAAC;gEAAK,WAAU;0EAAwB;;;;;;;;;;;;kEAE1C,sSAAC;wDAAK,WAAU;kEAAyB,MAAM,UAAU,CAAC,QAAQ,CAAC,cAAc;;;;;;;;;;;;0DAGnF,sSAAC;gDAAI,WAAU;;kEACb,sSAAC;wDAAI,WAAU;;0EACb,sSAAC,6SAAA,CAAA,eAAY;gEAAC,WAAU;;;;;;0EACxB,sSAAC;gEAAK,WAAU;0EAAwB;;;;;;;;;;;;kEAE1C,sSAAC;wDAAK,WAAU;kEAAyB,MAAM,UAAU,CAAC,SAAS,CAAC,cAAc;;;;;;;;;;;;0DAGpF,sSAAC;gDAAI,WAAU;;kEACb,sSAAC;wDAAI,WAAU;;0EACb,sSAAC,qSAAA,CAAA,WAAQ;gEAAC,WAAU;;;;;;0EACpB,sSAAC;gEAAK,WAAU;0EAAwB;;;;;;;;;;;;kEAE1C,sSAAC;wDAAK,WAAU;kEAAyB,MAAM,UAAU,CAAC,cAAc,CAAC,cAAc;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASzG;GA3QwB;KAAA", "debugId": null}}]}
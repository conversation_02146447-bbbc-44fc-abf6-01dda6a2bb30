"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.BulkUpdateSystemConfigDto = exports.BulkUpdateSystemConfigItemDto = void 0;
const openapi = require("@nestjs/swagger");
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
const class_transformer_1 = require("class-transformer");
const system_config_dto_1 = require("./system-config.dto");
class BulkUpdateSystemConfigItemDto {
    configKey;
    configValue;
    description;
    configGroup;
    sectionName;
    sectionDisplayName;
    sectionDescription;
    sectionOrder;
    displayOrder;
    groupDisplayName;
    groupDescription;
    groupIcon;
    groupOrder;
    isGroupConfig;
    configType;
    configOptions;
    updatedBy;
    static _OPENAPI_METADATA_FACTORY() {
        return { configKey: { required: true, type: () => String }, configValue: { required: false, type: () => String }, description: { required: false, type: () => String }, configGroup: { required: false, type: () => String }, sectionName: { required: false, type: () => String }, sectionDisplayName: { required: false, type: () => String }, sectionDescription: { required: false, type: () => String }, sectionOrder: { required: false, type: () => Number }, displayOrder: { required: false, type: () => Number }, groupDisplayName: { required: false, type: () => String }, groupDescription: { required: false, type: () => String }, groupIcon: { required: false, type: () => String }, groupOrder: { required: false, type: () => Number }, isGroupConfig: { required: false, type: () => Boolean }, configType: { required: false, enum: require("./system-config.dto").ConfigType }, configOptions: { required: false, type: () => String }, updatedBy: { required: false, type: () => String } };
    }
}
exports.BulkUpdateSystemConfigItemDto = BulkUpdateSystemConfigItemDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Khóa cấu hình' }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], BulkUpdateSystemConfigItemDto.prototype, "configKey", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Giá trị cấu hình' }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], BulkUpdateSystemConfigItemDto.prototype, "configValue", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Mô tả cấu hình' }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], BulkUpdateSystemConfigItemDto.prototype, "description", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Nhóm cấu hình',
        example: 'general'
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], BulkUpdateSystemConfigItemDto.prototype, "configGroup", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Tên section của cấu hình',
        example: 'header'
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], BulkUpdateSystemConfigItemDto.prototype, "sectionName", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Tên hiển thị của section',
        example: 'Cấu hình Header'
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], BulkUpdateSystemConfigItemDto.prototype, "sectionDisplayName", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Mô tả của section',
        example: 'Cấu hình phần header của website'
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], BulkUpdateSystemConfigItemDto.prototype, "sectionDescription", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Thứ tự hiển thị của section',
        example: 1
    }),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Number)
], BulkUpdateSystemConfigItemDto.prototype, "sectionOrder", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Thứ tự hiển thị của trường cấu hình trong section',
        example: 1
    }),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Number)
], BulkUpdateSystemConfigItemDto.prototype, "displayOrder", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Tên hiển thị của nhóm cấu hình',
        example: 'Cấu hình chung'
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], BulkUpdateSystemConfigItemDto.prototype, "groupDisplayName", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Mô tả của nhóm cấu hình',
        example: 'Cấu hình chung của hệ thống'
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], BulkUpdateSystemConfigItemDto.prototype, "groupDescription", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Icon của nhóm cấu hình',
        example: 'settings'
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], BulkUpdateSystemConfigItemDto.prototype, "groupIcon", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Thứ tự hiển thị của nhóm cấu hình',
        example: 1
    }),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Number)
], BulkUpdateSystemConfigItemDto.prototype, "groupOrder", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Cờ đánh dấu đây là cấu hình nhóm',
        example: false
    }),
    (0, class_validator_1.IsBoolean)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Boolean)
], BulkUpdateSystemConfigItemDto.prototype, "isGroupConfig", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Loại cấu hình',
        enum: system_config_dto_1.ConfigType
    }),
    (0, class_validator_1.IsEnum)(system_config_dto_1.ConfigType),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], BulkUpdateSystemConfigItemDto.prototype, "configType", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Tùy chọn cho loại select',
        example: '["option1", "option2"]'
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], BulkUpdateSystemConfigItemDto.prototype, "configOptions", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'ID người cập nhật',
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], BulkUpdateSystemConfigItemDto.prototype, "updatedBy", void 0);
class BulkUpdateSystemConfigDto {
    configs;
    static _OPENAPI_METADATA_FACTORY() {
        return { configs: { required: true, type: () => [require("./bulk-update-system-config.dto").BulkUpdateSystemConfigItemDto] } };
    }
}
exports.BulkUpdateSystemConfigDto = BulkUpdateSystemConfigDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Danh sách cấu hình cần cập nhật',
        type: [BulkUpdateSystemConfigItemDto],
    }),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.ValidateNested)({ each: true }),
    (0, class_transformer_1.Type)(() => BulkUpdateSystemConfigItemDto),
    __metadata("design:type", Array)
], BulkUpdateSystemConfigDto.prototype, "configs", void 0);
//# sourceMappingURL=bulk-update-system-config.dto.js.map
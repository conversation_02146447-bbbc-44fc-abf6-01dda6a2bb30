import { BaseActivityLogService } from './base.activity-log.service';
import { CustomPaginationQueryDto } from '../../common/dto/custom-pagination-query.dto';
export declare class ExportActivityLogService extends BaseActivityLogService {
    export(format?: 'csv' | 'json'): Promise<any>;
    exportByUser(userId: string, format?: 'csv' | 'json'): Promise<any>;
    exportByModule(module: string, format?: 'csv' | 'json'): Promise<any>;
    exportWithFilter(paginationQuery: CustomPaginationQueryDto, format?: 'csv' | 'json'): Promise<any>;
    private formatCsv;
    private formatCsvFromDto;
}

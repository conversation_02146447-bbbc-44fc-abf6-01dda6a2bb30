"use client"

import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { useAuth } from '@/hooks/use-auth';
import { Loader2 } from 'lucide-react';

interface AppLoadingContextType {
  isAppLoading: boolean;
  setAppLoading: (loading: boolean) => void;
}

const AppLoadingContext = createContext<AppLoadingContextType | undefined>(undefined);

export const useAppLoading = () => {
  const context = useContext(AppLoadingContext);
  if (!context) {
    throw new Error('useAppLoading must be used within AppLoadingProvider');
  }
  return context;
};

interface AppLoadingProviderProps {
  children: ReactNode;
}

export function AppLoadingProvider({ children }: AppLoadingProviderProps) {
  const [isAppLoading, setIsAppLoading] = useState(true);
  const { isLoading: isAuthLoading, user } = useAuth();

  // App loading logic
  useEffect(() => {
    // App sẽ loading cho đến khi:
    // 1. Auth loading hoàn tất
    // 2. User state đã ổn định (có user hoặc không có user)
    if (!isAuthLoading) {
      // Thêm delay nhỏ để đảm bảo user state đã ổn định
      // Điều này tránh sidebar "nhảy" từ USER → ADMIN
      const timer = setTimeout(() => {
        setIsAppLoading(false);
      }, 200); // Tăng delay lên 200ms để đảm bảo

      return () => clearTimeout(timer);
    }
  }, [isAuthLoading]);

  const setAppLoading = (loading: boolean) => {
    setIsAppLoading(loading);
  };

  // Hiển thị loading screen khi app đang loading
  if (isAppLoading) {
    return (
      <div className="fixed inset-0 bg-background flex items-center justify-center z-50">
        <div className="flex flex-col items-center gap-6">
          {/* Logo hoặc brand */}
          <div className="flex items-center gap-2">
            <div className="w-8 h-8 bg-primary rounded-lg flex items-center justify-center">
              <div className="w-4 h-4 bg-primary-foreground rounded-sm"></div>
            </div>
            <span className="text-xl font-semibold">PHYGITAL-V</span>
          </div>

          {/* Loading spinner */}
          <div className="flex flex-col items-center gap-3">
            <Loader2 className="h-6 w-6 animate-spin text-primary" />
            <p className="text-sm text-muted-foreground">Đang khởi tạo ứng dụng...</p>
          </div>

          {/* Progress bar animation */}
          <div className="w-48 h-1 bg-muted rounded-full overflow-hidden">
            <div className="h-full bg-primary rounded-full animate-pulse"></div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <AppLoadingContext.Provider value={{ isAppLoading, setAppLoading }}>
      {children}
    </AppLoadingContext.Provider>
  );
}

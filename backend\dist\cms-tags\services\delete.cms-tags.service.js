"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.DeleteCmsTagsService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const event_emitter_1 = require("@nestjs/event-emitter");
const typeorm_transactional_1 = require("typeorm-transactional");
const base_cms_tags_service_1 = require("./base.cms-tags.service");
const cms_tags_entity_1 = require("../entity/cms-tags.entity");
let DeleteCmsTagsService = class DeleteCmsTagsService extends base_cms_tags_service_1.BaseCmsTagsService {
    tagRepository;
    dataSource;
    eventEmitter;
    constructor(tagRepository, dataSource, eventEmitter) {
        super(tagRepository, dataSource, eventEmitter);
        this.tagRepository = tagRepository;
        this.dataSource = dataSource;
        this.eventEmitter = eventEmitter;
    }
    async softDelete(id, userId) {
        try {
            this.logger.debug(`Đang xóa mềm thẻ CMS với ID: ${id}`);
            const tag = await this.findById(id, []);
            if (!tag) {
                throw new common_1.NotFoundException(`Không tìm thấy thẻ với ID: ${id}`);
            }
            const oldData = this.toDto(tag);
            tag.isDeleted = true;
            tag.deletedBy = userId;
            tag.deletedAt = new Date();
            const deletedTag = await this.tagRepository.save(tag);
            const tagDto = this.toDto(deletedTag);
            if (!tagDto) {
                throw new common_1.InternalServerErrorException('Không thể chuyển đổi entity thành DTO');
            }
            this.eventEmitter.emit(this.EVENT_TAG_DELETED, {
                tagId: tagDto.id,
                userId,
                oldData,
                newData: tagDto,
            });
            return tagDto;
        }
        catch (error) {
            this.logger.error(`Lỗi khi xóa mềm thẻ CMS: ${error.message}`, error.stack);
            if (error instanceof common_1.BadRequestException || error instanceof common_1.NotFoundException) {
                throw error;
            }
            throw new common_1.InternalServerErrorException(`Không thể xóa thẻ CMS: ${error.message}`);
        }
    }
    async restore(id, userId) {
        try {
            this.logger.debug(`Đang khôi phục thẻ CMS với ID: ${id}`);
            const tag = await this.tagRepository.findOne({
                where: { id, isDeleted: true },
            });
            if (!tag) {
                throw new common_1.BadRequestException(`Không tìm thấy thẻ đã xóa với ID: ${id}`);
            }
            const oldData = this.toDto(tag);
            tag.isDeleted = false;
            tag.deletedBy = null;
            tag.deletedAt = null;
            tag.updatedBy = userId;
            const restoredTag = await this.tagRepository.save(tag);
            const tagDto = this.toDto(restoredTag);
            if (!tagDto) {
                throw new common_1.InternalServerErrorException('Không thể chuyển đổi entity thành DTO');
            }
            this.eventEmitter.emit('cms-tag.restored', {
                tagId: tagDto.id,
                userId,
                oldData,
                newData: tagDto,
            });
            return tagDto;
        }
        catch (error) {
            this.logger.error(`Lỗi khi khôi phục thẻ CMS: ${error.message}`, error.stack);
            if (error instanceof common_1.BadRequestException) {
                throw error;
            }
            throw new common_1.InternalServerErrorException(`Không thể khôi phục thẻ CMS: ${error.message}`);
        }
    }
    async remove(id) {
        try {
            this.logger.debug(`Đang xóa vĩnh viễn thẻ CMS với ID: ${id}`);
            const tag = await this.tagRepository.findOne({
                where: { id },
            });
            if (!tag) {
                throw new common_1.NotFoundException(`Không tìm thấy thẻ với ID: ${id}`);
            }
            const result = await this.tagRepository.delete(id);
            if (result.affected === 0) {
                throw new common_1.InternalServerErrorException(`Không thể xóa thẻ CMS với ID: ${id}`);
            }
            const affectedCount = result.affected ?? 0;
            return { affected: affectedCount };
        }
        catch (error) {
            this.logger.error(`Lỗi khi xóa vĩnh viễn thẻ CMS: ${error.message}`, error.stack);
            if (error instanceof common_1.BadRequestException || error instanceof common_1.NotFoundException) {
                throw error;
            }
            throw new common_1.InternalServerErrorException(`Không thể xóa vĩnh viễn thẻ CMS: ${error.message}`);
        }
    }
    async bulkSoftDelete(ids, userId) {
        try {
            this.logger.debug(`Đang xóa mềm ${ids.length} thẻ CMS`);
            const deletedTags = [];
            for (const id of ids) {
                try {
                    const tag = await this.softDelete(id, userId);
                    if (tag) {
                        deletedTags.push(tag);
                    }
                }
                catch (error) {
                    this.logger.warn(`Không thể xóa thẻ với ID ${id}: ${error.message}`);
                }
            }
            return deletedTags;
        }
        catch (error) {
            this.logger.error(`Lỗi khi xóa mềm nhiều thẻ CMS: ${error.message}`, error.stack);
            throw new common_1.InternalServerErrorException(`Không thể xóa nhiều thẻ CMS: ${error.message}`);
        }
    }
    async bulkRestore(ids, userId) {
        try {
            this.logger.debug(`Đang khôi phục ${ids.length} thẻ CMS`);
            const restoredTags = [];
            for (const id of ids) {
                try {
                    const tag = await this.restore(id, userId);
                    if (tag) {
                        restoredTags.push(tag);
                    }
                }
                catch (error) {
                    this.logger.warn(`Không thể khôi phục thẻ với ID ${id}: ${error.message}`);
                }
            }
            return restoredTags;
        }
        catch (error) {
            this.logger.error(`Lỗi khi khôi phục nhiều thẻ CMS: ${error.message}`, error.stack);
            throw new common_1.InternalServerErrorException(`Không thể khôi phục nhiều thẻ CMS: ${error.message}`);
        }
    }
    async bulkRemove(ids) {
        try {
            this.logger.debug(`Đang xóa vĩnh viễn ${ids.length} thẻ CMS`);
            let totalAffected = 0;
            for (const id of ids) {
                try {
                    const result = await this.remove(id);
                    totalAffected += result.affected;
                }
                catch (error) {
                    this.logger.warn(`Không thể xóa vĩnh viễn thẻ với ID ${id}: ${error.message}`);
                }
            }
            return { affected: totalAffected };
        }
        catch (error) {
            this.logger.error(`Lỗi khi xóa vĩnh viễn nhiều thẻ CMS: ${error.message}`, error.stack);
            throw new common_1.InternalServerErrorException(`Không thể xóa vĩnh viễn nhiều thẻ CMS: ${error.message}`);
        }
    }
};
exports.DeleteCmsTagsService = DeleteCmsTagsService;
__decorate([
    (0, typeorm_transactional_1.Transactional)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String]),
    __metadata("design:returntype", Promise)
], DeleteCmsTagsService.prototype, "softDelete", null);
__decorate([
    (0, typeorm_transactional_1.Transactional)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String]),
    __metadata("design:returntype", Promise)
], DeleteCmsTagsService.prototype, "restore", null);
__decorate([
    (0, typeorm_transactional_1.Transactional)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], DeleteCmsTagsService.prototype, "remove", null);
__decorate([
    (0, typeorm_transactional_1.Transactional)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Array, String]),
    __metadata("design:returntype", Promise)
], DeleteCmsTagsService.prototype, "bulkSoftDelete", null);
__decorate([
    (0, typeorm_transactional_1.Transactional)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Array, String]),
    __metadata("design:returntype", Promise)
], DeleteCmsTagsService.prototype, "bulkRestore", null);
__decorate([
    (0, typeorm_transactional_1.Transactional)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Array]),
    __metadata("design:returntype", Promise)
], DeleteCmsTagsService.prototype, "bulkRemove", null);
exports.DeleteCmsTagsService = DeleteCmsTagsService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(cms_tags_entity_1.CmsTags)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        typeorm_2.DataSource,
        event_emitter_1.EventEmitter2])
], DeleteCmsTagsService);
//# sourceMappingURL=delete.cms-tags.service.js.map
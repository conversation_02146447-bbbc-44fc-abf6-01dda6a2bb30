{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/bloomgoo/gold-exchange-platform/frontend/components/common/SilverPriceIndicator.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { usePolygonForex } from '@/services/websocket/polygon-forex-socket.service';\r\nimport { Badge } from '@/components/ui/badge';\r\n\r\nexport function SilverPriceIndicator() {\r\n  const {\r\n    currentQuote,\r\n    isMockData\r\n  } = usePolygonForex();\r\n\r\n  // Format giá\r\n  const formatPrice = (price?: number) => {\r\n    if (price === undefined || price === null) return '---.----';\r\n    return price.toFixed(4);\r\n  };\r\n\r\n  // Format giá VND\r\n  const formatVND = (price?: number) => {\r\n    if (price === undefined || price === null) return '---,---';\r\n    return price.toLocaleString('vi-VN');\r\n  };\r\n\r\n  // Xác định màu sắc cho chênh lệch giá\r\n  const getSpreadColor = (spread?: number) => {\r\n    if (spread === undefined || spread === null) return 'bg-gray-500';\r\n    if (spread > 0.1) return 'bg-red-500';\r\n    if (spread < 0.05) return 'bg-green-500';\r\n    return 'bg-yellow-500';\r\n  };\r\n\r\n  return (\r\n    <div className=\"flex items-center gap-2\">\r\n      <Badge variant=\"outline\" className=\"flex items-center gap-1 py-1\">\r\n        <span className=\"text-xs font-medium\">AXGUSD:</span>\r\n        <span className=\"text-xs font-bold\">{formatPrice(currentQuote?.bidPrice)}</span>\r\n        {currentQuote?.spread !== undefined && (\r\n          <span className={`inline-block w-2 h-2 rounded-full ${getSpreadColor(currentQuote?.spread)}`} />\r\n        )}\r\n      </Badge>\r\n      \r\n      {isMockData && (\r\n        <Badge variant=\"outline\" className=\"bg-yellow-50 text-yellow-700 border-yellow-200 text-xs\">\r\n          Mẫu\r\n        </Badge>\r\n      )}\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAKO,SAAS;IACd,MAAM,EACJ,YAAY,EACZ,UAAU,EACX,GAAG,CAAA,GAAA,8JAAA,CAAA,kBAAe,AAAD;IAElB,aAAa;IACb,MAAM,cAAc,CAAC;QACnB,IAAI,UAAU,aAAa,UAAU,MAAM,OAAO;QAClD,OAAO,MAAM,OAAO,CAAC;IACvB;IAEA,iBAAiB;IACjB,MAAM,YAAY,CAAC;QACjB,IAAI,UAAU,aAAa,UAAU,MAAM,OAAO;QAClD,OAAO,MAAM,cAAc,CAAC;IAC9B;IAEA,sCAAsC;IACtC,MAAM,iBAAiB,CAAC;QACtB,IAAI,WAAW,aAAa,WAAW,MAAM,OAAO;QACpD,IAAI,SAAS,KAAK,OAAO;QACzB,IAAI,SAAS,MAAM,OAAO;QAC1B,OAAO;IACT;IAEA,qBACE,uVAAC;QAAI,WAAU;;0BACb,uVAAC,0HAAA,CAAA,QAAK;gBAAC,SAAQ;gBAAU,WAAU;;kCACjC,uVAAC;wBAAK,WAAU;kCAAsB;;;;;;kCACtC,uVAAC;wBAAK,WAAU;kCAAqB,YAAY,cAAc;;;;;;oBAC9D,cAAc,WAAW,2BACxB,uVAAC;wBAAK,WAAW,CAAC,kCAAkC,EAAE,eAAe,cAAc,SAAS;;;;;;;;;;;;YAI/F,4BACC,uVAAC,0HAAA,CAAA,QAAK;gBAAC,SAAQ;gBAAU,WAAU;0BAAyD;;;;;;;;;;;;AAMpG", "debugId": null}}, {"offset": {"line": 94, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/bloomgoo/gold-exchange-platform/frontend/components/common/data-table/data-table.tsx"], "sourcesContent": ["\"use client\"\r\n\r\n/**\r\n * Component DataTable cung cấp bảng dữ liệu với các tính năng:\r\n * - Header cố định khi cuộn\r\n * - Cột cố định (sticky columns) ở bên trái và bên phải\r\n * - Hỗ trợ đầy đủ các tính năng của TanStack Table\r\n *\r\n * Để sử dụng sticky columns, hãy thêm thuộc tính meta vào định nghĩa cột:\r\n * ```\r\n * {\r\n *   id: 'actions',\r\n *   meta: {\r\n *     isSticky: true,\r\n *     position: 'right' // hoặc 'left'\r\n *   }\r\n * }\r\n * ```\r\n *\r\n * Xem thêm hướng dẫn chi tiết trong file README.md\r\n */\r\n\r\nimport { flexRender, Table as TableType } from \"@tanstack/react-table\";\r\n\r\nimport { cn } from \"@/lib/utils\";\r\n\r\n// Định nghĩa kiểu dữ liệu cho thuộc tính meta của cột\r\ndeclare module '@tanstack/react-table' {\r\n  interface ColumnMeta<TData, TValue> {\r\n    isSticky?: boolean;\r\n    position?: 'left' | 'right';\r\n    header?: string;\r\n  }\r\n}\r\n\r\ninterface DataTableProps<TData> {\r\n    table: TableType<TData>\r\n    className?: string\r\n    isLoading?: boolean\r\n}\r\n\r\nexport function DataTable<TData>({\r\n    table,\r\n    className,\r\n    isLoading = false,\r\n}: DataTableProps<TData>) {\r\n    return (\r\n        <div className={cn(\"w-full flex flex-col h-full\", className)}>\r\n            {/* Table Container with border */}\r\n            <div className=\"border flex flex-col flex-1 overflow-hidden\">\r\n                {/* Single table with sticky header */}\r\n                <div className=\"table-scroll-container\">\r\n                    <div className=\"table-viewport\">\r\n                        <table className=\"data-table\">\r\n                            <thead className=\"sticky-header\">\r\n                                {table.getHeaderGroups().map((headerGroup) => (\r\n                                    <tr key={headerGroup.id}>\r\n                                        {headerGroup.headers.map((header, index) => {\r\n                                            // Determine if this column has sticky metadata\r\n                                            const hasSticky = header.column.columnDef.meta?.isSticky;\r\n\r\n                                            return (\r\n                                                <th\r\n                                                    key={header.id}\r\n                                                    style={{\r\n                                                        width: header.getSize(),\r\n                                                        minWidth: header.getSize(),\r\n                                                        maxWidth: header.getSize(),\r\n                                                        ...(hasSticky && {\r\n                                                            position: 'sticky',\r\n                                                            [header.column.columnDef.meta?.position === 'right' ? 'right' : 'left']: 0,\r\n                                                            zIndex: 60,\r\n                                                            boxShadow: header.column.columnDef.meta?.position === 'right'\r\n                                                                ? '-5px 0 5px -5px rgba(0,0,0,0.1)'\r\n                                                                : '5px 0 5px -5px rgba(0,0,0,0.1)'\r\n                                                        }),\r\n                                                        // Không cần logic fallback cho first column nữa\r\n                                                        // Tất cả sticky columns phải được định nghĩa qua meta\r\n                                                    }}\r\n                                                    className={cn(\r\n                                                        \"bg-background py-2 text-left align-middle font-medium text-muted-foreground\",\r\n                                                        hasSticky && `sticky-${header.column.columnDef.meta?.position || 'left'}`,\r\n                                                        // Thêm class dựa trên column ID thay vì index\r\n                                                        header.column.id === 'select' && \"checkbox-header\"\r\n                                                    )}\r\n                                                >\r\n                                                    {header.isPlaceholder\r\n                                                        ? null\r\n                                                        : flexRender(\r\n                                                            header.column.columnDef.header,\r\n                                                            header.getContext()\r\n                                                        )}\r\n                                                </th>\r\n                                            )\r\n                                        })}\r\n                                    </tr>\r\n                                ))}\r\n                            </thead>\r\n                            <tbody>\r\n                                {isLoading ? (\r\n                                    <tr>\r\n                                        <td\r\n                                            colSpan={table.getAllColumns().length}\r\n                                            className=\"h-24 text-center\"\r\n                                        >\r\n                                            Đang tải dữ liệu...\r\n                                        </td>\r\n                                    </tr>\r\n                                ) : table.getRowModel().rows?.length ? (\r\n                                    table.getRowModel().rows.map((row) => (\r\n                                        <tr\r\n                                            key={row.id}\r\n                                            data-state={row.getIsSelected() && \"selected\"}\r\n                                            className=\"border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted\"\r\n                                        >\r\n                                            {row.getVisibleCells().map((cell, index) => {\r\n                                                // Determine if this column has sticky metadata\r\n                                                const hasSticky = cell.column.columnDef.meta?.isSticky;\r\n\r\n                                                return (\r\n                                                    <td\r\n                                                        key={cell.id}\r\n                                                        style={{\r\n                                                            width: cell.column.getSize(),\r\n                                                            minWidth: cell.column.getSize(),\r\n                                                            maxWidth: cell.column.getSize(),\r\n                                                            ...(hasSticky && {\r\n                                                                position: 'sticky',\r\n                                                                [cell.column.columnDef.meta?.position === 'right' ? 'right' : 'left']: 0,\r\n                                                                backgroundColor: 'var(--background)',\r\n                                                                zIndex: 30,\r\n                                                                boxShadow: cell.column.columnDef.meta?.position === 'right'\r\n                                                                    ? '-5px 0 5px -5px rgba(0,0,0,0.1)'\r\n                                                                    : '5px 0 5px -5px rgba(0,0,0,0.1)'\r\n                                                            }),\r\n                                                            // Không cần logic fallback cho first column nữa\r\n                                                            // Tất cả sticky columns phải được định nghĩa qua meta\r\n                                                        }}\r\n                                                        className={cn(\r\n                                                            \"py-1 px-2 align-middle\",\r\n                                                            hasSticky && `sticky-${cell.column.columnDef.meta?.position || 'left'}`,\r\n                                                            // Thêm class dựa trên column ID\r\n                                                            cell.column.id === 'select' && \"checkbox-cell\"\r\n                                                        )}\r\n                                                    >\r\n                                                        {flexRender(\r\n                                                            cell.column.columnDef.cell,\r\n                                                            cell.getContext()\r\n                                                        )}\r\n                                                    </td>\r\n                                                )\r\n                                            })}\r\n                                        </tr>\r\n                                    ))\r\n                                ) : (\r\n                                    <tr>\r\n                                        <td\r\n                                            colSpan={table.getAllColumns().length}\r\n                                            className=\"h-24 text-center\"\r\n                                        >\r\n                                            Không có dữ liệu\r\n                                        </td>\r\n                                    </tr>\r\n                                )}\r\n                            </tbody>\r\n                        </table>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n        </div>\r\n    )\r\n}"], "names": [], "mappings": ";;;;AAEA;;;;;;;;;;;;;;;;;;CAkBC,GAED;AAEA;AAAA;AAxBA;;;;AAyCO,SAAS,UAAiB,EAC7B,KAAK,EACL,SAAS,EACT,YAAY,KAAK,EACG;IACpB,qBACI,uVAAC;QAAI,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EAAE,+BAA+B;kBAE9C,cAAA,uVAAC;YAAI,WAAU;sBAEX,cAAA,uVAAC;gBAAI,WAAU;0BACX,cAAA,uVAAC;oBAAI,WAAU;8BACX,cAAA,uVAAC;wBAAM,WAAU;;0CACb,uVAAC;gCAAM,WAAU;0CACZ,MAAM,eAAe,GAAG,GAAG,CAAC,CAAC,4BAC1B,uVAAC;kDACI,YAAY,OAAO,CAAC,GAAG,CAAC,CAAC,QAAQ;4CAC9B,+CAA+C;4CAC/C,MAAM,YAAY,OAAO,MAAM,CAAC,SAAS,CAAC,IAAI,EAAE;4CAEhD,qBACI,uVAAC;gDAEG,OAAO;oDACH,OAAO,OAAO,OAAO;oDACrB,UAAU,OAAO,OAAO;oDACxB,UAAU,OAAO,OAAO;oDACxB,GAAI,aAAa;wDACb,UAAU;wDACV,CAAC,OAAO,MAAM,CAAC,SAAS,CAAC,IAAI,EAAE,aAAa,UAAU,UAAU,OAAO,EAAE;wDACzE,QAAQ;wDACR,WAAW,OAAO,MAAM,CAAC,SAAS,CAAC,IAAI,EAAE,aAAa,UAChD,oCACA;oDACV,CAAC;gDAGL;gDACA,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EACR,+EACA,aAAa,CAAC,OAAO,EAAE,OAAO,MAAM,CAAC,SAAS,CAAC,IAAI,EAAE,YAAY,QAAQ,EACzE,8CAA8C;gDAC9C,OAAO,MAAM,CAAC,EAAE,KAAK,YAAY;0DAGpC,OAAO,aAAa,GACf,OACA,CAAA,GAAA,gSAAA,CAAA,aAAU,AAAD,EACP,OAAO,MAAM,CAAC,SAAS,CAAC,MAAM,EAC9B,OAAO,UAAU;+CA3BpB,OAAO,EAAE;;;;;wCA+B1B;uCAtCK,YAAY,EAAE;;;;;;;;;;0CA0C/B,uVAAC;0CACI,0BACG,uVAAC;8CACG,cAAA,uVAAC;wCACG,SAAS,MAAM,aAAa,GAAG,MAAM;wCACrC,WAAU;kDACb;;;;;;;;;;2CAIL,MAAM,WAAW,GAAG,IAAI,EAAE,SAC1B,MAAM,WAAW,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,oBAC1B,uVAAC;wCAEG,cAAY,IAAI,aAAa,MAAM;wCACnC,WAAU;kDAET,IAAI,eAAe,GAAG,GAAG,CAAC,CAAC,MAAM;4CAC9B,+CAA+C;4CAC/C,MAAM,YAAY,KAAK,MAAM,CAAC,SAAS,CAAC,IAAI,EAAE;4CAE9C,qBACI,uVAAC;gDAEG,OAAO;oDACH,OAAO,KAAK,MAAM,CAAC,OAAO;oDAC1B,UAAU,KAAK,MAAM,CAAC,OAAO;oDAC7B,UAAU,KAAK,MAAM,CAAC,OAAO;oDAC7B,GAAI,aAAa;wDACb,UAAU;wDACV,CAAC,KAAK,MAAM,CAAC,SAAS,CAAC,IAAI,EAAE,aAAa,UAAU,UAAU,OAAO,EAAE;wDACvE,iBAAiB;wDACjB,QAAQ;wDACR,WAAW,KAAK,MAAM,CAAC,SAAS,CAAC,IAAI,EAAE,aAAa,UAC9C,oCACA;oDACV,CAAC;gDAGL;gDACA,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EACR,0BACA,aAAa,CAAC,OAAO,EAAE,KAAK,MAAM,CAAC,SAAS,CAAC,IAAI,EAAE,YAAY,QAAQ,EACvE,gCAAgC;gDAChC,KAAK,MAAM,CAAC,EAAE,KAAK,YAAY;0DAGlC,CAAA,GAAA,gSAAA,CAAA,aAAU,AAAD,EACN,KAAK,MAAM,CAAC,SAAS,CAAC,IAAI,EAC1B,KAAK,UAAU;+CA1Bd,KAAK,EAAE;;;;;wCA8BxB;uCAxCK,IAAI,EAAE;;;;8DA4CnB,uVAAC;8CACG,cAAA,uVAAC;wCACG,SAAS,MAAM,aAAa,GAAG,MAAM;wCACrC,WAAU;kDACb;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAYzC", "debugId": null}}, {"offset": {"line": 272, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/bloomgoo/gold-exchange-platform/frontend/components/common/user/order-books/type/order-books.ts"], "sourcesContent": ["import { EcomProduct } from \"@/components/common/admin/ecom-products/type/ecom-product\";\r\nimport { User } from \"@/components/common/admin/users/type/user\";\r\n\r\n/**\r\n * Enum đại diện cho loại lệnh (OrderType) từ backend\r\n * Đại diện cho hướng của lệnh (mua/bán/rút)\r\n */\r\nexport enum OrderType {\r\n    BUY = 'BUY',\r\n    SELL = 'SELL',\r\n}\r\n\r\n/**\r\n * Enum đại diện cho trạng thái của lệnh (OrderStatus) từ backend\r\n */\r\nexport enum OrderStatus {\r\n    COMPLETED = 'COMPLETED',\r\n    TERMINATED = 'TERMINATED',\r\n    CANCELLED = 'CANCELLED',\r\n    DEPOSITED = 'DEPOSITED',\r\n}\r\n\r\n/**\r\n * Enum đại diện cho loại hình kinh doanh (BusinessType) từ backend\r\n */\r\nexport enum BusinessType {\r\n    NORMAL = 'NORMAL',\r\n    IMMEDIATE_DELIVERY = 'IMMEDIATE_DELIVERY',\r\n}\r\n\r\n/**\r\n * Enum đại diện cho trạng thái phê duyệt (ApproveStatus) từ backend\r\n */\r\nexport enum ApproveStatus {\r\n    PENDING = 'PENDING',\r\n    APPROVED = 'APPROVED',\r\n}\r\n\r\n/**\r\n * Interface đại diện cho chi tiết token trong lệnh\r\n * Dựa trên OrderTokenDetailDto từ backend\r\n */\r\nexport interface OrderTokenDetail {\r\n    productId: string;\r\n    price: number;\r\n    volume: number;\r\n}\r\n\r\n/**\r\n * Interface đại diện chi tiết trong sổ lệnh (OrderBookDetail) từ backend\r\n */\r\nexport interface OrderBookDetail {\r\n    id: string;\r\n    orderBookId: string;\r\n    productId: string;\r\n    price: number;\r\n    quantity: number;\r\n    totalPrice: number;\r\n    createdAt: string;\r\n    updatedAt: string;\r\n    createdBy?: string;\r\n    updatedBy?: string;\r\n    product?: EcomProduct;\r\n}\r\n\r\n/**\r\n * Interface đại diện cho sổ lệnh (OrderBook) từ backend\r\n */\r\nexport interface OrderBook {\r\n    id: string;\r\n    userId: string;\r\n    orderType: OrderType;\r\n    totalPrice?: number;\r\n    processingPrice?: number;\r\n    status: OrderStatus;\r\n    businessType?: BusinessType;\r\n    depositPrice?: number;\r\n    settlementPrice?: number;\r\n    contractNumber?: string;\r\n    storageFee?: number;\r\n    settlementDeadline?: Date;\r\n    settlementAt?: Date;\r\n    approveStatus?: ApproveStatus;\r\n    approvedAt?: Date;\r\n    createdAt: string;\r\n    updatedAt: string;\r\n    createdBy?: string;\r\n    updatedBy?: string;\r\n    isDeleted: boolean;\r\n    deletedBy?: string;\r\n    user?: User;\r\n    details: OrderBookDetail[];\r\n}\r\n\r\n/**\r\n * Interface cho dữ liệu tạo lệnh mới\r\n * Dựa trên CreateOrderBookDto từ backend\r\n */\r\nexport interface CreateOrderBookDto {\r\n    userId: string;\r\n    orderType: OrderType;\r\n    products: OrderTokenDetail[];\r\n    depositAmount: number;\r\n    settlementAmount: number;\r\n    contractNumber?: string;\r\n    storageFee: number;\r\n    settlementDeadline?: Date;\r\n    createdBy: string;\r\n}\r\n\r\n/**\r\n * Interface cho dữ liệu cập nhật lệnh\r\n * Dựa trên UpdateOrderBookDto từ backend\r\n */\r\nexport interface UpdateOrderBookDto {\r\n    id?: string;\r\n    userId?: string;\r\n    orderType?: OrderType;\r\n    totalPrice?: number;\r\n    status?: OrderStatus;\r\n    businessType?: BusinessType;\r\n    depositAmount?: number;\r\n    settlementAmount?: number;\r\n    contractNumber?: string;\r\n    storageFee?: number;\r\n    settlementDeadline?: Date;\r\n    settlementAt?: Date;\r\n    tokens?: OrderTokenDetail[];\r\n    updatedBy?: string;\r\n}\r\n\r\n/**\r\n * Interface cho bộ lọc sổ lệnh\r\n */\r\nexport interface OrderBookFilter {\r\n    userId?: string;\r\n    tokenId?: string;\r\n    orderType?: OrderType;\r\n    status?: OrderStatus;\r\n    dateFrom?: Date;\r\n    dateTo?: Date;\r\n    contractNumber?: string;\r\n}\r\n"], "names": [], "mappings": ";;;;;;AAOO,IAAA,AAAK,mCAAA;;;WAAA;;AAQL,IAAA,AAAK,qCAAA;;;;;WAAA;;AAUL,IAAA,AAAK,sCAAA;;;WAAA;;AAQL,IAAA,AAAK,uCAAA;;;WAAA", "debugId": null}}, {"offset": {"line": 306, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/bloomgoo/gold-exchange-platform/frontend/components/common/user/order-books/table/order-book-cell.tsx"], "sourcesContent": ["import { Badge } from \"@/components/ui/badge\"\r\nimport { <PERSON><PERSON> } from \"@/components/ui/button\"\r\nimport {\r\n  <PERSON>alog,\r\n  DialogContent,\r\n  DialogDescription,\r\n  DialogFooter,\r\n  DialogHeader,\r\n  DialogTitle\r\n} from \"@/components/ui/dialog\"\r\nimport {\r\n  DropdownMenu,\r\n  DropdownMenuContent,\r\n  DropdownMenuItem,\r\n  DropdownMenuSeparator,\r\n  DropdownMenuShortcut,\r\n  DropdownMenuTrigger\r\n} from \"@/components/ui/dropdown-menu\"\r\nimport { Input } from \"@/components/ui/input\"\r\nimport { Label } from \"@/components/ui/label\"\r\nimport { Popconfirm } from \"@/components/ui/popconfirm\"\r\nimport {\r\n  Popover,\r\n  PopoverContent,\r\n  PopoverTrigger\r\n} from \"@/components/ui/popover\"\r\n\r\nimport {\r\n  Command,\r\n  CommandEmpty,\r\n  CommandGroup,\r\n  CommandInput,\r\n  CommandItem,\r\n} from \"@/components/ui/command\"\r\nimport { DateTimeDisplay } from \"@/components/ui/date-time-display\"\r\nimport { api } from '@/lib/api'\r\nimport { cn } from \"@/lib/utils\"\r\nimport { ForexQuote } from '@/services/websocket/polygon-forex-socket.service'\r\nimport { IconCurrencyDong } from \"@tabler/icons-react\"\r\nimport { type ColumnDef, Row } from \"@tanstack/react-table\"\r\nimport { format, isValid } from \"date-fns\"\r\nimport { vi } from \"date-fns/locale\"\r\nimport {\r\n  ArrowUpDown,\r\n  CalendarPlus,\r\n  Check,\r\n  ChevronsUpDown,\r\n  Eye,\r\n  MoreVertical\r\n} from \"lucide-react\"\r\nimport { useId, useState } from \"react\"\r\nimport { toast } from \"sonner\"\r\nimport { ApproveStatus, BusinessType, OrderBook, OrderStatus, OrderType } from \"../type/order-books\"\r\n\r\n// Format currency with VND\r\nconst formatCurrency = (amount: number | undefined) => {\r\n  if (amount === undefined || amount === null) return \"---\"\r\n  return new Intl.NumberFormat('vi-VN', {\r\n    maximumFractionDigits: 0\r\n  }).format(amount)\r\n}\r\n\r\n// Format date from ISO string\r\nconst formatDate = (dateStr: string | null) => {\r\n  if (!dateStr) return \"---\"\r\n  const date = new Date(dateStr)\r\n  if (!isValid(date)) return \"Không hợp lệ\"\r\n  try {\r\n    return format(date, \"dd/MM/yyyy HH:mm\", { locale: vi })\r\n  } catch (error) {\r\n    return \"Không hợp lệ\"\r\n  }\r\n}\r\n\r\n// Get color for order status badge\r\nconst getStatusColor = (status: OrderStatus) => {\r\n  switch (status) {\r\n    case OrderStatus.COMPLETED:\r\n      return 'bg-green-100 text-green-800 font-normal';\r\n    case OrderStatus.DEPOSITED:\r\n      return 'bg-purple-100 text-purple-800 font-normal';\r\n    case OrderStatus.TERMINATED:\r\n      return 'bg-red-100 text-red-800 font-normal';\r\n    case OrderStatus.CANCELLED:\r\n      return 'bg-orange-100 text-orange-800 font-normal';\r\n    default:\r\n      return 'bg-gray-100 text-gray-800 font-normal';\r\n  }\r\n};\r\n\r\n// Order status in Vietnamese\r\nconst getStatusName = (status: OrderStatus) => {\r\n  switch (status) {\r\n    case OrderStatus.COMPLETED:\r\n      return 'Đã tất toán';\r\n    case OrderStatus.DEPOSITED:\r\n      return 'Đã ký quỹ';\r\n    case OrderStatus.TERMINATED:\r\n      return 'Đã cắt hợp đồng';\r\n    case OrderStatus.CANCELLED:\r\n      return 'Đã hủy';\r\n    default:\r\n      return status;\r\n  }\r\n};\r\n\r\n// Order type in Vietnamese\r\nconst getOrderTypeName = (type: OrderType) => {\r\n  switch (type) {\r\n    case OrderType.BUY:\r\n      return 'Mua';\r\n    case OrderType.SELL:\r\n      return 'Bán';\r\n    default:\r\n      return type;\r\n  }\r\n};\r\n\r\n// Get color for order type\r\nconst getOrderTypeColor = (type: OrderType) => {\r\n  switch (type) {\r\n    case OrderType.BUY:\r\n      return 'bg-green-100 text-green-800 font-normal';\r\n    case OrderType.SELL:\r\n      return 'bg-red-100 text-red-800 font-normal';\r\n    default:\r\n      return 'bg-gray-100 text-gray-800 font-normal';\r\n  }\r\n};\r\n\r\n// Get color for business type\r\nconst getBusinessTypeColor = (type: BusinessType | undefined) => {\r\n  switch (type) {\r\n    case BusinessType.NORMAL:\r\n      return 'bg-orange-100 text-orange-800 font-normal';\r\n    case BusinessType.IMMEDIATE_DELIVERY:\r\n      return 'bg-blue-100 text-blue-800 font-normal';\r\n    default:\r\n      return 'bg-gray-100 text-gray-800 font-normal';\r\n  }\r\n};\r\n\r\n// Get color for approve status badge\r\nconst getApproveStatusColor = (status: ApproveStatus | undefined) => {\r\n  switch (status) {\r\n    case ApproveStatus.PENDING:\r\n      return 'bg-yellow-100 text-yellow-800 font-normal';\r\n    case ApproveStatus.APPROVED:\r\n      return 'bg-green-100 text-green-800 font-normal';\r\n    default:\r\n      return 'bg-gray-100 text-gray-800 font-normal';\r\n  }\r\n};\r\n\r\n// Approve status in Vietnamese\r\nconst getApproveStatusName = (status: ApproveStatus | undefined) => {\r\n  switch (status) {\r\n    case ApproveStatus.PENDING:\r\n      return 'Chờ phê duyệt';\r\n    case ApproveStatus.APPROVED:\r\n      return 'Đã phê duyệt';\r\n    default:\r\n      return '---';\r\n  }\r\n};\r\n\r\n// Add helper function to calculate real-time total price\r\nconst calculateRealTimePrice = (order: OrderBook, currentQuote: ForexQuote | null) => {\r\n  if (!currentQuote || order.status === OrderStatus.COMPLETED) {\r\n    return order.totalPrice || 0;\r\n  }\r\n\r\n  // Get the appropriate price based on order type\r\n  const price = order.orderType === OrderType.BUY ? currentQuote.askPrice : currentQuote.bidPrice;\r\n\r\n  // Calculate total volume and price\r\n  const totalVolume = order.details?.reduce((sum, detail) => {\r\n    const volume = detail.quantity || 0;\r\n    const weight = detail.product?.weight || 1;\r\n    return sum + (volume * weight);\r\n  }, 0) || 0;\r\n\r\n  // Calculate new total price\r\n  const basePrice = price * totalVolume * 1.2056 * 27000;\r\n  return Math.round(basePrice + (basePrice * 0.11)); // Add 11% VAT\r\n};\r\n\r\n// Add helper function to calculate real-time settlement amount\r\nconst calculateRealTimeSettlement = (order: OrderBook, currentQuote: ForexQuote | null) => {\r\n  if (!currentQuote || order.status === OrderStatus.COMPLETED) {\r\n    return order.settlementPrice || 0;\r\n  }\r\n\r\n  const totalPrice = calculateRealTimePrice(order, currentQuote);\r\n\r\n  // For physical orders, settlement is 100%\r\n  if (order.businessType === BusinessType.IMMEDIATE_DELIVERY) {\r\n    return totalPrice;\r\n  }\r\n\r\n  // For short investment, settlement is remaining balance (90% if not paid)\r\n  return Math.round(totalPrice * 0.9);\r\n};\r\n\r\n// Add helper function to get price color based on order type\r\nconst getPriceColor = (order: OrderBook) => {\r\n  if (order.status === OrderStatus.COMPLETED) return \"\";\r\n  return order.orderType === OrderType.BUY ? \"text-green-500\" : \"text-red-500\";\r\n};\r\n\r\ninterface ActionsProps {\r\n  row: Row<OrderBook>\r\n  onViewDetail: (order: OrderBook) => void\r\n  onExtendSettlement?: (order: OrderBook) => void\r\n  onSettlement?: (order: OrderBook, option: 'silver' | 'deposit' | 'cancel') => void\r\n}\r\n\r\nfunction Actions({ row, onViewDetail, onExtendSettlement, onSettlement }: ActionsProps) {\r\n  const order = row.original\r\n  const [showExtendDialog, setShowExtendDialog] = useState(false)\r\n\r\n  // Xử lý khi click vào nút xem chi tiết\r\n  const handleViewDetail = async (order: OrderBook) => {\r\n    try {\r\n      // Sử dụng api client để gọi API\r\n      const response = await api.get<OrderBook>(`order-books/${order.id}`);\r\n\r\n      // Chỉ truyền dữ liệu chi tiết nếu API trả về thành công\r\n      if (response) {\r\n        onViewDetail(response);\r\n      } else {\r\n        // Nếu không có response, hiển thị thông báo lỗi\r\n        toast.error(\"Không thể tải thông tin chi tiết lệnh. Vui lòng thử lại sau.\");\r\n      }\r\n    } catch (error) {\r\n      console.error('Error fetching order detail:', error);\r\n      // Hiển thị thông báo lỗi cho người dùng\r\n      toast.error(\"Có lỗi xảy ra khi tải thông tin lệnh. Vui lòng thử lại sau.\");\r\n    }\r\n  };\r\n\r\n  // Kiểm tra xem lệnh có thể gia hạn hay không\r\n  const canExtend = () => {\r\n    // Chỉ cho phép gia hạn lệnh mua/bán đang ở trạng thái WAIT_PAYMENT\r\n    return (\r\n      order.status === OrderStatus.DEPOSITED &&\r\n      order.settlementDeadline // Phải có hạn tất toán\r\n    )\r\n  }\r\n\r\n  // Kiểm tra xem lệnh có thể tất toán hay không\r\n  const canSettle = () => {\r\n    // Chỉ cho phép tất toán lệnh mua/bán đang ở trạng thái WAIT_PAYMENT\r\n    return (\r\n      (order.orderType === OrderType.BUY || order.orderType === OrderType.SELL) &&\r\n      order.status === OrderStatus.DEPOSITED\r\n    )\r\n  }\r\n\r\n  // Xử lý khi người dùng chọn phương thức tất toán\r\n  const handleSettlementOption = (option: 'silver' | 'deposit' | 'cancel') => {\r\n    if (onSettlement) {\r\n      onSettlement(order, option);\r\n    }\r\n  };\r\n\r\n  return (\r\n    <>\r\n      {/* Dialog xác nhận gia hạn */}\r\n      <Dialog open={showExtendDialog} onOpenChange={setShowExtendDialog}>\r\n        <DialogContent>\r\n          <DialogHeader>\r\n            <DialogTitle>Xác nhận gia hạn</DialogTitle>\r\n            <DialogDescription>\r\n              Bạn có chắc chắn muốn gia hạn thời gian tất toán thêm 15 ngày cho lệnh này?\r\n              <p className=\"mt-2\">Phí gia hạn là 100,000 VND và sẽ được trừ từ số dư của bạn.</p>\r\n            </DialogDescription>\r\n          </DialogHeader>\r\n          <DialogFooter>\r\n            <Button variant=\"outline\" onClick={() => setShowExtendDialog(false)}>Hủy</Button>\r\n            <Button\r\n              onClick={() => {\r\n                if (onExtendSettlement) {\r\n                  onExtendSettlement(order);\r\n                }\r\n                setShowExtendDialog(false);\r\n              }}\r\n            >\r\n              Xác nhận gia hạn\r\n            </Button>\r\n          </DialogFooter>\r\n        </DialogContent>\r\n      </Dialog>\r\n\r\n      <div className=\"flex justify-end px-1 sticky-action-cell h-full items-center\">\r\n        <DropdownMenu>\r\n          <DropdownMenuTrigger asChild>\r\n            <Button\r\n              variant=\"ghost\"\r\n              size=\"sm\"\r\n              className=\"flex h-6 w-6 p-0 data-[state=open]:bg-muted transition-colors hover:bg-muted\"\r\n            >\r\n              <MoreVertical className=\"h-3 w-3\" />\r\n              <span className=\"sr-only\">Mở menu</span>\r\n            </Button>\r\n          </DropdownMenuTrigger>\r\n          <DropdownMenuContent align=\"end\" className=\"p-1\">\r\n            <DropdownMenuItem onClick={() => handleViewDetail(order)}>\r\n              <Eye className=\"mr-2 h-3.5 w-3.5\" />\r\n              <span className=\"flex-1 text-sm\">Chi tiết</span>\r\n              <DropdownMenuShortcut className=\"text-sm\">⌘V</DropdownMenuShortcut>\r\n            </DropdownMenuItem>\r\n\r\n            {canExtend() && onExtendSettlement && (\r\n              <>\r\n                <DropdownMenuSeparator />\r\n                <DropdownMenuItem onClick={() => setShowExtendDialog(true)}>\r\n                  <CalendarPlus className=\"mr-2 h-3.5 w-3.5 text-blue-500\" />\r\n                  <span className=\"flex-1 text-sm text-blue-500\">Gia hạn thời gian</span>\r\n                </DropdownMenuItem>\r\n              </>\r\n            )}\r\n\r\n            {canSettle() && onSettlement && (\r\n              <>\r\n                <DropdownMenuSeparator />\r\n                {order.businessType === BusinessType.IMMEDIATE_DELIVERY ? (\r\n                  // Với bạc giao ngay, hiển thị popconfirm hỏi có muốn rút bạc không\r\n                  <DropdownMenuItem onSelect={(e) => {\r\n                    // Ngăn đóng dropdown menu khi mở popconfirm\r\n                    e.preventDefault();\r\n                  }}>\r\n                    <Popconfirm\r\n                      title=\"Xác nhận rút bạc\"\r\n                      description=\"Bạn có muốn rút bạc không?\"\r\n                      cancelText=\"Không\"\r\n                      confirmText=\"Có\"\r\n                      onConfirm={() => handleSettlementOption('silver')}\r\n                      onCancel={() => handleSettlementOption('cancel')}\r\n                    >\r\n                      <div className=\"flex items-center w-full cursor-pointer\">\r\n                        <Check className=\"mr-2 h-3.5 w-3.5 text-green-500\" />\r\n                        <span className=\"flex-1 text-sm text-green-500\">Tất toán</span>\r\n                      </div>\r\n                    </Popconfirm>\r\n                  </DropdownMenuItem>\r\n                ) : (\r\n                  // Với bạc ký quỹ online, mở modal tất toán\r\n                  <DropdownMenuItem onClick={() => handleSettlementOption('silver')}>\r\n                    <div className=\"flex items-center w-full cursor-pointer\">\r\n                      <Check className=\"mr-2 h-3.5 w-3.5 text-green-500\" />\r\n                      <span className=\"flex-1 text-sm text-green-500\">Tất toán</span>\r\n                    </div>\r\n                  </DropdownMenuItem>\r\n                )}\r\n              </>\r\n            )}\r\n          </DropdownMenuContent>\r\n        </DropdownMenu>\r\n      </div>\r\n    </>\r\n  )\r\n}\r\n\r\n// Status selector for changing order status\r\ninterface OrderStatusSelectorProps {\r\n  status: OrderStatus;\r\n  onStatusChange: (status: OrderStatus) => void;\r\n  disabled?: boolean;\r\n}\r\n\r\nexport function OrderStatusSelector({ status, onStatusChange, disabled = false }: OrderStatusSelectorProps) {\r\n  const [open, setOpen] = useState<boolean>(false);\r\n\r\n  // Tất cả các trạng thái có thể có\r\n  const statusOptions = [\r\n    { value: OrderStatus.COMPLETED as string, label: 'Đã tất toán', color: 'bg-green-100 text-green-800 font-normal' },\r\n    { value: OrderStatus.TERMINATED as string, label: 'Đã cắt hợp đồng', color: 'bg-red-100 text-red-800 font-normal' },\r\n    { value: OrderStatus.DEPOSITED as string, label: 'Đã ký quỹ', color: 'bg-purple-100 text-purple-800 font-normal' },\r\n  ];\r\n\r\n  const currentStatus = statusOptions.find((option) => option.value === status) || statusOptions[0];\r\n\r\n  return (\r\n    <Popover open={open} onOpenChange={setOpen}>\r\n      <PopoverTrigger asChild>\r\n        <Button\r\n          variant=\"ghost\"\r\n          role=\"combobox\"\r\n          aria-expanded={open}\r\n          disabled={disabled}\r\n          className=\"justify-between h-auto p-1 hover:bg-transparent\"\r\n        >\r\n          <Badge className={currentStatus.color}>\r\n            {currentStatus.label}\r\n          </Badge>\r\n          <ChevronsUpDown className=\"ml-2 h-4 w-4 shrink-0 opacity-50\" />\r\n        </Button>\r\n      </PopoverTrigger>\r\n      <PopoverContent className=\"w-[200px] p-0\">\r\n        <Command>\r\n          <CommandInput placeholder=\"Tìm trạng thái...\" />\r\n          <CommandEmpty>Không tìm thấy trạng thái</CommandEmpty>\r\n          <CommandGroup>\r\n            {statusOptions.map((option) => (\r\n              <CommandItem\r\n                key={option.value}\r\n                value={option.value}\r\n                onSelect={() => {\r\n                  onStatusChange(option.value as OrderStatus);\r\n                  setOpen(false);\r\n                }}\r\n              >\r\n                <Check\r\n                  className={cn(\r\n                    \"mr-2 h-4 w-4\",\r\n                    status === option.value ? \"opacity-100\" : \"opacity-0\"\r\n                  )}\r\n                />\r\n                <Badge className={option.color}>\r\n                  {option.label}\r\n                </Badge>\r\n              </CommandItem>\r\n            ))}\r\n          </CommandGroup>\r\n        </Command>\r\n      </PopoverContent>\r\n    </Popover>\r\n  );\r\n}\r\n\r\n// Price editor for changing order price\r\ninterface PriceSelectorProps {\r\n  price: number;\r\n  onPriceChange: (price: number) => void;\r\n  disabled?: boolean;\r\n  buttonText?: string;\r\n}\r\n\r\nexport function PriceSelector({\r\n  price,\r\n  onPriceChange,\r\n  disabled = false,\r\n  buttonText = 'Áp dụng'\r\n}: PriceSelectorProps) {\r\n  const id = useId();\r\n  const [open, setOpen] = useState<boolean>(false);\r\n  const [inputValue, setInputValue] = useState<string>(price?.toString() || '');\r\n\r\n  // Xử lý khi người dùng nhấn nút Áp dụng\r\n  const handleApply = () => {\r\n    const numValue = Number(inputValue);\r\n    if (!isNaN(numValue) && numValue > 0) {\r\n      onPriceChange(numValue);\r\n    }\r\n    setOpen(false);\r\n  };\r\n\r\n  // Xử lý khi đóng popover mà không áp dụng\r\n  const handleOpenChange = (isOpen: boolean) => {\r\n    if (isOpen) {\r\n      // Khi mở popover, cập nhật lại giá trị input\r\n      setInputValue(price?.toString() || '');\r\n    }\r\n    setOpen(isOpen);\r\n  };\r\n\r\n  // Hiển thị giá theo format\r\n  const displayPrice = () => {\r\n    if (!price) return \"---\";\r\n    return formatCurrency(price);\r\n  };\r\n\r\n  return (\r\n    <Popover open={open} onOpenChange={handleOpenChange}>\r\n      <PopoverTrigger asChild>\r\n        <Button\r\n          id={id}\r\n          className=\"p-0 h-auto\"\r\n          variant=\"ghost\"\r\n          role=\"combobox\"\r\n          aria-expanded={open}\r\n          disabled={disabled}\r\n        >\r\n          <div className=\"flex items-center gap-1 text-left\">\r\n            <span className=\"font-medium truncate\">{displayPrice()}</span>\r\n          </div>\r\n        </Button>\r\n      </PopoverTrigger>\r\n      <PopoverContent className=\"p-4 w-64\" align=\"start\">\r\n        <div className=\"flex flex-col space-y-4\">\r\n          <div className=\"space-y-2\">\r\n            <Label htmlFor={`price-input-${id}`}>Giá</Label>\r\n            <div className=\"relative\">\r\n              <IconCurrencyDong className=\"absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground\" />\r\n              <Input\r\n                id={`price-input-${id}`}\r\n                value={inputValue}\r\n                onChange={(e) => setInputValue(e.target.value)}\r\n                placeholder=\"Nhập giá mới\"\r\n                type=\"number\"\r\n                min=\"0\"\r\n                className=\"pl-8\"\r\n              />\r\n            </div>\r\n          </div>\r\n\r\n          <Button\r\n            onClick={handleApply}\r\n            className=\"w-full\"\r\n            size=\"sm\"\r\n          >\r\n            {buttonText}\r\n          </Button>\r\n        </div>\r\n      </PopoverContent>\r\n    </Popover>\r\n  );\r\n}\r\n\r\n// Volume selector for changing order volume\r\ninterface VolumeSelectorProps {\r\n  volume: number;\r\n  onVolumeChange: (volume: number) => void;\r\n  disabled?: boolean;\r\n  buttonText?: string;\r\n}\r\n\r\nexport function VolumeSelector({\r\n  volume,\r\n  onVolumeChange,\r\n  disabled = false,\r\n  buttonText = 'Áp dụng'\r\n}: VolumeSelectorProps) {\r\n  const id = useId();\r\n  const [open, setOpen] = useState<boolean>(false);\r\n  const [inputValue, setInputValue] = useState<string>(volume?.toString() || '');\r\n\r\n  // Xử lý khi người dùng nhấn nút Áp dụng\r\n  const handleApply = () => {\r\n    const numValue = Number(inputValue);\r\n    if (!isNaN(numValue) && numValue > 0) {\r\n      onVolumeChange(numValue);\r\n    }\r\n    setOpen(false);\r\n  };\r\n\r\n  // Xử lý khi đóng popover mà không áp dụng\r\n  const handleOpenChange = (isOpen: boolean) => {\r\n    if (isOpen) {\r\n      // Khi mở popover, cập nhật lại giá trị input\r\n      setInputValue(volume?.toString() || '');\r\n    }\r\n    setOpen(isOpen);\r\n  };\r\n\r\n  // Hiển thị khối lượng theo format\r\n  const displayVolume = () => {\r\n    if (volume === undefined || volume === null) return \"---\";\r\n    return `${volume.toLocaleString()}`;\r\n  };\r\n\r\n  return (\r\n    <Popover open={open} onOpenChange={handleOpenChange}>\r\n      <PopoverTrigger asChild>\r\n        <Button\r\n          id={id}\r\n          className=\"p-0 h-auto\"\r\n          variant=\"ghost\"\r\n          role=\"combobox\"\r\n          aria-expanded={open}\r\n          disabled={disabled}\r\n        >\r\n          <div className=\"flex items-center gap-1 text-left\">\r\n            <span className=\"font-medium truncate\">{displayVolume()}</span>\r\n          </div>\r\n        </Button>\r\n      </PopoverTrigger>\r\n      <PopoverContent className=\"p-4 w-64\" align=\"start\">\r\n        <div className=\"flex flex-col space-y-4\">\r\n          <div className=\"space-y-2\">\r\n            <Label htmlFor={`volume-input-${id}`}>Số lượng</Label>\r\n            <Input\r\n              id={`volume-input-${id}`}\r\n              value={inputValue}\r\n              onChange={(e) => setInputValue(e.target.value)}\r\n              placeholder=\"Nhập số lượng mới\"\r\n              type=\"number\"\r\n              min=\"0\"\r\n              step=\"1\"\r\n            />\r\n          </div>\r\n\r\n          <Button\r\n            onClick={handleApply}\r\n            className=\"w-full\"\r\n            size=\"sm\"\r\n          >\r\n            {buttonText}\r\n          </Button>\r\n        </div>\r\n      </PopoverContent>\r\n    </Popover>\r\n  );\r\n}\r\n\r\ninterface OrderBookColumnsProps {\r\n  onViewDetail: (order: OrderBook) => void\r\n  onExtendSettlement?: (order: OrderBook) => void\r\n  onSettlement?: (order: OrderBook, option: 'silver' | 'deposit' | 'cancel') => void\r\n  currentQuote?: ForexQuote | null\r\n}\r\n\r\nexport function getOrderBookColumns({\r\n  onViewDetail,\r\n  onExtendSettlement,\r\n  onSettlement,\r\n  currentQuote\r\n}: OrderBookColumnsProps): ColumnDef<OrderBook>[] {\r\n  return [\r\n    {\r\n      accessorKey: \"contractNumber\",\r\n      header: ({ column }) => {\r\n        return (\r\n          <Button\r\n            variant=\"ghost\"\r\n            size=\"sm\"\r\n            onClick={() => column.toggleSorting(column.getIsSorted() === \"asc\")}\r\n            className=\"flex items-center gap-1 px-0 h-6 py-0\"\r\n            data-column-id=\"contractNumber\"\r\n          >\r\n            <span className=\"text-xs\">Số hợp đồng</span>\r\n            <ArrowUpDown className=\"ml-1 h-3 w-3\" />\r\n          </Button>\r\n        )\r\n      },\r\n      meta: {\r\n        header: \"Số hợp đồng\",\r\n        isSticky: true,\r\n        position: 'left',\r\n      },\r\n      cell: ({ row }) => {\r\n        const contractNumber = row.original.contractNumber;\r\n        return (\r\n          <div className=\"text-sm\">\r\n            {contractNumber || \"---\"}\r\n          </div>\r\n        )\r\n      },\r\n      enableSorting: true,\r\n    },\r\n    {\r\n      accessorKey: \"orderType\",\r\n      header: ({ column }) => {\r\n        return (\r\n          <Button\r\n            variant=\"ghost\"\r\n            size=\"sm\"\r\n            onClick={() => column.toggleSorting(column.getIsSorted() === \"asc\")}\r\n            className=\"flex items-center gap-1 px-0 h-6 py-0\"\r\n            data-column-id=\"orderType\"\r\n          >\r\n            <span className=\"text-xs\">Loại lệnh</span>\r\n            <ArrowUpDown className=\"ml-1 h-3 w-3\" />\r\n          </Button>\r\n        )\r\n      },\r\n      meta: {\r\n        header: \"Loại lệnh\"\r\n      },\r\n      cell: ({ row }) => {\r\n        const type = row.getValue(\"orderType\") as OrderType\r\n        return (\r\n          <Badge className={getOrderTypeColor(type)}>\r\n            {getOrderTypeName(type)}\r\n          </Badge>\r\n        )\r\n      },\r\n      enableSorting: true,\r\n    },\r\n    {\r\n      accessorKey: \"businessType\",\r\n      header: ({ column }) => {\r\n        return (\r\n          <Button\r\n            variant=\"ghost\"\r\n            size=\"sm\"\r\n            onClick={() => column.toggleSorting(column.getIsSorted() === \"asc\")}\r\n            className=\"flex items-center gap-1 px-0 h-6 py-0\"\r\n            data-column-id=\"businessType\"\r\n          >\r\n            <span className=\"text-xs\">Loại hình giao dịch</span>\r\n            <ArrowUpDown className=\"ml-1 h-3 w-3\" />\r\n          </Button>\r\n        )\r\n      },\r\n      meta: {\r\n        header: \"Loại hình giao dịch\"\r\n      },\r\n      cell: ({ row }) => {\r\n        const businessType = row.getValue(\"businessType\") as BusinessType | undefined\r\n\r\n        if (!businessType || row.original.orderType !== OrderType.BUY) {\r\n          return <span className=\"text-xs text-muted-foreground\">---</span>\r\n        }\r\n\r\n        return (\r\n          <Badge className={getBusinessTypeColor(businessType)}>\r\n            {businessType === BusinessType.NORMAL ? \"Bạc online ký quỹ\" : \"Bạc giao ngay\"}\r\n          </Badge>\r\n        )\r\n      },\r\n      enableSorting: true,\r\n    },\r\n    {\r\n      accessorKey: \"totalPrice\",\r\n      header: ({ column }) => {\r\n        return (\r\n          <Button\r\n            variant=\"ghost\"\r\n            size=\"sm\"\r\n            onClick={() => column.toggleSorting(column.getIsSorted() === \"asc\")}\r\n            className=\"flex items-center gap-1 px-0 h-6 py-0\"\r\n            data-column-id=\"totalPrice\"\r\n          >\r\n            <span className=\"text-xs\">Tổng giá trị</span>\r\n            <ArrowUpDown className=\"ml-1 h-3 w-3\" />\r\n          </Button>\r\n        )\r\n      },\r\n      meta: {\r\n        header: \"Tổng giá trị\"\r\n      },\r\n      cell: ({ row }) => {\r\n        const order = row.original;\r\n        const totalPrice = calculateRealTimePrice(order, currentQuote || null);\r\n\r\n        return (\r\n          <div className=\"flex items-center gap-1 text-sm\">\r\n            <span className={getPriceColor(order)}>\r\n              {formatCurrency(totalPrice)} VND\r\n            </span>\r\n          </div>\r\n        )\r\n      },\r\n      enableSorting: true,\r\n    },\r\n    {\r\n      accessorKey: \"processingPrice\",\r\n      header: ({ column }) => {\r\n        return (\r\n          <Button\r\n            variant=\"ghost\"\r\n            size=\"sm\"\r\n            onClick={() => column.toggleSorting(column.getIsSorted() === \"asc\")}\r\n            className=\"flex items-center gap-1 px-0 h-6 py-0\"\r\n            data-column-id=\"processingPrice\"\r\n          >\r\n            <span className=\"text-xs\">Phí gia công</span>\r\n            <ArrowUpDown className=\"ml-1 h-3 w-3\" />\r\n          </Button>\r\n        )\r\n      },\r\n      meta: {\r\n        header: \"Phí gia công\"\r\n      },\r\n      cell: ({ row }) => {\r\n        const order = row.original;\r\n        const processingPrice = order.processingPrice;\r\n\r\n        return (\r\n          <div className=\"flex items-center gap-1 text-sm\">\r\n            <span>\r\n              {processingPrice ? formatCurrency(processingPrice) : \"0\"} VND\r\n            </span>\r\n          </div>\r\n        )\r\n      },\r\n      enableSorting: true,\r\n    },\r\n    {\r\n      id: \"totalVolume\",\r\n      header: () => {\r\n        return (\r\n          <Button\r\n            variant=\"ghost\"\r\n            size=\"sm\"\r\n            className=\"flex items-center gap-1 px-0 h-6 py-0\"\r\n            data-column-id=\"totalVolume\"\r\n          >\r\n            <span className=\"text-xs\">Tổng khối lượng</span>\r\n          </Button>\r\n        )\r\n      },\r\n      meta: {\r\n        header: \"Tổng khối lượng\"\r\n      },\r\n      cell: ({ row }) => {\r\n        const order = row.original;\r\n        // Tính tổng khối lượng bằng cách nhân số lượng với oz của token\r\n        const totalVolume = order.details?.reduce((sum, detail) => {\r\n          const volume = detail.quantity || 0;\r\n          const weight = detail.product?.weight || 1; // Lấy oz từ token, mặc định là 1 nếu không có\r\n          return sum + (volume * weight);\r\n        }, 0) || 0;\r\n        return (\r\n          <div className=\"text-sm\">\r\n            {totalVolume.toLocaleString()}\r\n          </div>\r\n        )\r\n      },\r\n      enableSorting: false,\r\n    },\r\n    {\r\n      accessorKey: \"depositPrice\",\r\n      header: ({ column }) => {\r\n        return (\r\n          <Button\r\n            variant=\"ghost\"\r\n            size=\"sm\"\r\n            onClick={() => column.toggleSorting(column.getIsSorted() === \"asc\")}\r\n            className=\"flex items-center gap-1 px-0 h-6 py-0\"\r\n            data-column-id=\"depositPrice\"\r\n          >\r\n            <span className=\"text-xs\">Tiền ký quỹ</span>\r\n            <ArrowUpDown className=\"ml-1 h-3 w-3\" />\r\n          </Button>\r\n        )\r\n      },\r\n      meta: {\r\n        header: \"Tiền ký quỹ\"\r\n      },\r\n      cell: ({ row }) => {\r\n        const depositPrice = row.getValue(\"depositPrice\") as number | undefined\r\n\r\n        return (\r\n          <div className=\"flex items-center gap-1 text-sm\">\r\n            <span>{formatCurrency(depositPrice)} VND</span>\r\n          </div>\r\n        )\r\n      },\r\n      enableSorting: true,\r\n    },\r\n    {\r\n      accessorKey: \"settlementPrice\",\r\n      header: ({ column }) => {\r\n        return (\r\n          <Button\r\n            variant=\"ghost\"\r\n            size=\"sm\"\r\n            onClick={() => column.toggleSorting(column.getIsSorted() === \"asc\")}\r\n            className=\"flex items-center gap-1 px-0 h-6 py-0\"\r\n            data-column-id=\"settlementPrice\"\r\n          >\r\n            <span className=\"text-xs\">Tiền tất toán</span>\r\n            <ArrowUpDown className=\"ml-1 h-3 w-3\" />\r\n          </Button>\r\n        )\r\n      },\r\n      meta: {\r\n        header: \"Tiền tất toán\"\r\n      },\r\n      cell: ({ row }) => {\r\n        const order = row.original;\r\n        const settlementPrice = calculateRealTimeSettlement(order, currentQuote || null);\r\n\r\n        return (\r\n          <div className=\"flex items-center gap-1 text-sm\">\r\n            <span className={getPriceColor(order)}>\r\n              {formatCurrency(settlementPrice)} VND\r\n            </span>\r\n          </div>\r\n        )\r\n      },\r\n      enableSorting: true,\r\n    },\r\n    {\r\n      accessorKey: \"storageFee\",\r\n      header: ({ column }) => {\r\n        return (\r\n          <Button\r\n            variant=\"ghost\"\r\n            size=\"sm\"\r\n            onClick={() => column.toggleSorting(column.getIsSorted() === \"asc\")}\r\n            className=\"flex items-center gap-1 px-0 h-6 py-0\"\r\n            data-column-id=\"storageFee\"\r\n          >\r\n            <span className=\"text-xs\">Phí lưu kho</span>\r\n            <ArrowUpDown className=\"ml-1 h-3 w-3\" />\r\n          </Button>\r\n        )\r\n      },\r\n      meta: {\r\n        header: \"Phí lưu kho\"\r\n      },\r\n      cell: ({ row }) => {\r\n        const storageFee = row.getValue(\"storageFee\") as number | undefined\r\n\r\n        return (\r\n          <div className=\"flex items-center gap-1 text-sm\">\r\n            <span>{formatCurrency(storageFee)} VND</span>\r\n          </div>\r\n        )\r\n      },\r\n      enableSorting: true,\r\n    },\r\n    {\r\n      accessorKey: \"settlementDeadline\",\r\n      header: ({ column }) => {\r\n        return (\r\n          <Button\r\n            variant=\"ghost\"\r\n            size=\"sm\"\r\n            onClick={() => column.toggleSorting(column.getIsSorted() === \"asc\")}\r\n            className=\"flex items-center gap-1 px-0 h-6 py-0\"\r\n            data-column-id=\"settlementDeadline\"\r\n          >\r\n            <span className=\"text-xs\">Hạn tất toán</span>\r\n            <ArrowUpDown className=\"ml-1 h-3 w-3\" />\r\n          </Button>\r\n        )\r\n      },\r\n      meta: {\r\n        header: \"Hạn tất toán\"\r\n      },\r\n      cell: ({ row }) => {\r\n        const date = row.original\r\n        return (\r\n          <DateTimeDisplay\r\n            date={date.settlementDeadline}\r\n            className=\"text-sm\"\r\n            timeClassName=\"text-xs text-muted-foreground\"\r\n          />\r\n        )\r\n      },\r\n      enableSorting: true,\r\n      size: 150,\r\n    },\r\n    {\r\n      accessorKey: \"settlementAt\",\r\n      header: ({ column }) => {\r\n        return (\r\n          <Button\r\n            variant=\"ghost\"\r\n            size=\"sm\"\r\n            onClick={() => column.toggleSorting(column.getIsSorted() === \"asc\")}\r\n            className=\"flex items-center gap-1 px-0 h-6 py-0\"\r\n            data-column-id=\"settlementAt\"\r\n          >\r\n            <span className=\"text-xs\">Ngày tất toán</span>\r\n            <ArrowUpDown className=\"ml-1 h-3 w-3\" />\r\n          </Button>\r\n        )\r\n      },\r\n      meta: {\r\n        header: \"Ngày tất toán\"\r\n      },\r\n      cell: ({ row }) => {\r\n        const date = row.original\r\n        return (\r\n          <DateTimeDisplay\r\n            date={date.settlementAt}\r\n            className=\"text-sm\"\r\n            timeClassName=\"text-xs text-muted-foreground\"\r\n          />\r\n        )\r\n      },\r\n      enableSorting: true,\r\n      size: 150,\r\n    },\r\n    {\r\n      accessorKey: \"approveStatus\",\r\n      header: ({ column }) => {\r\n        return (\r\n          <Button\r\n            variant=\"ghost\"\r\n            size=\"sm\"\r\n            onClick={() => column.toggleSorting(column.getIsSorted() === \"asc\")}\r\n            className=\"flex items-center gap-1 px-0 h-6 py-0\"\r\n            data-column-id=\"approveStatus\"\r\n          >\r\n            <span className=\"text-xs\">Phê duyệt</span>\r\n            <ArrowUpDown className=\"ml-1 h-3 w-3\" />\r\n          </Button>\r\n        )\r\n      },\r\n      meta: {\r\n        header: \"Phê duyệt\"\r\n      },\r\n      cell: ({ row }) => {\r\n        const approveStatus = row.original.approveStatus\r\n\r\n        return (\r\n          <Badge className={getApproveStatusColor(approveStatus)}>\r\n            {getApproveStatusName(approveStatus)}\r\n          </Badge>\r\n        )\r\n      },\r\n      enableSorting: true,\r\n      size: 120,\r\n    },\r\n    {\r\n      accessorKey: \"status\",\r\n      header: ({ column }) => {\r\n        return (\r\n          <Button\r\n            variant=\"ghost\"\r\n            size=\"sm\"\r\n            onClick={() => column.toggleSorting(column.getIsSorted() === \"asc\")}\r\n            className=\"flex items-center gap-1 px-0 h-6 py-0\"\r\n            data-column-id=\"status\"\r\n          >\r\n            <span className=\"text-xs\">Trạng thái</span>\r\n            <ArrowUpDown className=\"ml-1 h-3 w-3\" />\r\n          </Button>\r\n        )\r\n      },\r\n      meta: {\r\n        header: \"Trạng thái\"\r\n      },\r\n      cell: ({ row }) => {\r\n        const status = row.getValue(\"status\") as OrderStatus\r\n\r\n        return (\r\n          <Badge className={getStatusColor(status)}>\r\n            {getStatusName(status)}\r\n          </Badge>\r\n        )\r\n      },\r\n      enableSorting: true,\r\n      size: 120,\r\n    },\r\n    {\r\n      accessorKey: \"createdAt\",\r\n      header: ({ column }) => {\r\n        return (\r\n          <Button\r\n            variant=\"ghost\"\r\n            size=\"sm\"\r\n            onClick={() => column.toggleSorting(column.getIsSorted() === \"asc\")}\r\n            className=\"flex items-center gap-1 px-0 h-6 py-0\"\r\n            data-column-id=\"createdAt\"\r\n          >\r\n            <span className=\"text-xs\">Ngày tạo</span>\r\n            <ArrowUpDown className=\"ml-1 h-3 w-3\" />\r\n          </Button>\r\n        )\r\n      },\r\n      meta: {\r\n        header: \"Ngày tạo\"\r\n      },\r\n      cell: ({ row }) => {\r\n        const date = row.original\r\n        return (\r\n          <DateTimeDisplay\r\n            date={date.createdAt}\r\n            className=\"text-sm\"\r\n            timeClassName=\"text-xs text-muted-foreground\"\r\n          />\r\n        )\r\n      },\r\n      enableSorting: true,\r\n      size: 150,\r\n    },\r\n    {\r\n      id: 'actions',\r\n      size: 40,\r\n      enableHiding: false,\r\n      header: () => <div data-column-id=\"actions\"></div>,\r\n      cell: ({ row }) => (\r\n        <Actions\r\n          row={row}\r\n          onViewDetail={onViewDetail}\r\n          onExtendSettlement={onExtendSettlement}\r\n          onSettlement={onSettlement}\r\n        />\r\n      ),\r\n      meta: {\r\n        isSticky: true, // Đánh dấu cột này là cố định\r\n        position: 'right', // Vị trí cố định (right hoặc left)\r\n        header: \"Thao tác\"\r\n      }\r\n    }\r\n  ]\r\n}"], "names": [], "mappings": ";;;;;;;AAAA;AACA;AACA;AAQA;AAQA;AACA;AACA;AACA;AAMA;AAOA;AACA;AACA;AAAA;AAEA;AAEA;AAAA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAQA;AACA;AACA;;;;;;;;;;;;;;;;;;;;;AAEA,2BAA2B;AAC3B,MAAM,iBAAiB,CAAC;IACtB,IAAI,WAAW,aAAa,WAAW,MAAM,OAAO;IACpD,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;QACpC,uBAAuB;IACzB,GAAG,MAAM,CAAC;AACZ;AAEA,8BAA8B;AAC9B,MAAM,aAAa,CAAC;IAClB,IAAI,CAAC,SAAS,OAAO;IACrB,MAAM,OAAO,IAAI,KAAK;IACtB,IAAI,CAAC,CAAA,GAAA,8LAAA,CAAA,UAAO,AAAD,EAAE,OAAO,OAAO;IAC3B,IAAI;QACF,OAAO,CAAA,GAAA,6MAAA,CAAA,SAAM,AAAD,EAAE,MAAM,oBAAoB;YAAE,QAAQ,mMAAA,CAAA,KAAE;QAAC;IACvD,EAAE,OAAO,OAAO;QACd,OAAO;IACT;AACF;AAEA,mCAAmC;AACnC,MAAM,iBAAiB,CAAC;IACtB,OAAQ;QACN,KAAK,wKAAA,CAAA,cAAW,CAAC,SAAS;YACxB,OAAO;QACT,KAAK,wKAAA,CAAA,cAAW,CAAC,SAAS;YACxB,OAAO;QACT,KAAK,wKAAA,CAAA,cAAW,CAAC,UAAU;YACzB,OAAO;QACT,KAAK,wKAAA,CAAA,cAAW,CAAC,SAAS;YACxB,OAAO;QACT;YACE,OAAO;IACX;AACF;AAEA,6BAA6B;AAC7B,MAAM,gBAAgB,CAAC;IACrB,OAAQ;QACN,KAAK,wKAAA,CAAA,cAAW,CAAC,SAAS;YACxB,OAAO;QACT,KAAK,wKAAA,CAAA,cAAW,CAAC,SAAS;YACxB,OAAO;QACT,KAAK,wKAAA,CAAA,cAAW,CAAC,UAAU;YACzB,OAAO;QACT,KAAK,wKAAA,CAAA,cAAW,CAAC,SAAS;YACxB,OAAO;QACT;YACE,OAAO;IACX;AACF;AAEA,2BAA2B;AAC3B,MAAM,mBAAmB,CAAC;IACxB,OAAQ;QACN,KAAK,wKAAA,CAAA,YAAS,CAAC,GAAG;YAChB,OAAO;QACT,KAAK,wKAAA,CAAA,YAAS,CAAC,IAAI;YACjB,OAAO;QACT;YACE,OAAO;IACX;AACF;AAEA,2BAA2B;AAC3B,MAAM,oBAAoB,CAAC;IACzB,OAAQ;QACN,KAAK,wKAAA,CAAA,YAAS,CAAC,GAAG;YAChB,OAAO;QACT,KAAK,wKAAA,CAAA,YAAS,CAAC,IAAI;YACjB,OAAO;QACT;YACE,OAAO;IACX;AACF;AAEA,8BAA8B;AAC9B,MAAM,uBAAuB,CAAC;IAC5B,OAAQ;QACN,KAAK,wKAAA,CAAA,eAAY,CAAC,MAAM;YACtB,OAAO;QACT,KAAK,wKAAA,CAAA,eAAY,CAAC,kBAAkB;YAClC,OAAO;QACT;YACE,OAAO;IACX;AACF;AAEA,qCAAqC;AACrC,MAAM,wBAAwB,CAAC;IAC7B,OAAQ;QACN,KAAK,wKAAA,CAAA,gBAAa,CAAC,OAAO;YACxB,OAAO;QACT,KAAK,wKAAA,CAAA,gBAAa,CAAC,QAAQ;YACzB,OAAO;QACT;YACE,OAAO;IACX;AACF;AAEA,+BAA+B;AAC/B,MAAM,uBAAuB,CAAC;IAC5B,OAAQ;QACN,KAAK,wKAAA,CAAA,gBAAa,CAAC,OAAO;YACxB,OAAO;QACT,KAAK,wKAAA,CAAA,gBAAa,CAAC,QAAQ;YACzB,OAAO;QACT;YACE,OAAO;IACX;AACF;AAEA,yDAAyD;AACzD,MAAM,yBAAyB,CAAC,OAAkB;IAChD,IAAI,CAAC,gBAAgB,MAAM,MAAM,KAAK,wKAAA,CAAA,cAAW,CAAC,SAAS,EAAE;QAC3D,OAAO,MAAM,UAAU,IAAI;IAC7B;IAEA,gDAAgD;IAChD,MAAM,QAAQ,MAAM,SAAS,KAAK,wKAAA,CAAA,YAAS,CAAC,GAAG,GAAG,aAAa,QAAQ,GAAG,aAAa,QAAQ;IAE/F,mCAAmC;IACnC,MAAM,cAAc,MAAM,OAAO,EAAE,OAAO,CAAC,KAAK;QAC9C,MAAM,SAAS,OAAO,QAAQ,IAAI;QAClC,MAAM,SAAS,OAAO,OAAO,EAAE,UAAU;QACzC,OAAO,MAAO,SAAS;IACzB,GAAG,MAAM;IAET,4BAA4B;IAC5B,MAAM,YAAY,QAAQ,cAAc,SAAS;IACjD,OAAO,KAAK,KAAK,CAAC,YAAa,YAAY,OAAQ,cAAc;AACnE;AAEA,+DAA+D;AAC/D,MAAM,8BAA8B,CAAC,OAAkB;IACrD,IAAI,CAAC,gBAAgB,MAAM,MAAM,KAAK,wKAAA,CAAA,cAAW,CAAC,SAAS,EAAE;QAC3D,OAAO,MAAM,eAAe,IAAI;IAClC;IAEA,MAAM,aAAa,uBAAuB,OAAO;IAEjD,0CAA0C;IAC1C,IAAI,MAAM,YAAY,KAAK,wKAAA,CAAA,eAAY,CAAC,kBAAkB,EAAE;QAC1D,OAAO;IACT;IAEA,0EAA0E;IAC1E,OAAO,KAAK,KAAK,CAAC,aAAa;AACjC;AAEA,6DAA6D;AAC7D,MAAM,gBAAgB,CAAC;IACrB,IAAI,MAAM,MAAM,KAAK,wKAAA,CAAA,cAAW,CAAC,SAAS,EAAE,OAAO;IACnD,OAAO,MAAM,SAAS,KAAK,wKAAA,CAAA,YAAS,CAAC,GAAG,GAAG,mBAAmB;AAChE;AASA,SAAS,QAAQ,EAAE,GAAG,EAAE,YAAY,EAAE,kBAAkB,EAAE,YAAY,EAAgB;IACpF,MAAM,QAAQ,IAAI,QAAQ;IAC1B,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,8SAAA,CAAA,WAAQ,AAAD,EAAE;IAEzD,uCAAuC;IACvC,MAAM,mBAAmB,OAAO;QAC9B,IAAI;YACF,gCAAgC;YAChC,MAAM,WAAW,MAAM,0GAAA,CAAA,MAAG,CAAC,GAAG,CAAY,CAAC,YAAY,EAAE,MAAM,EAAE,EAAE;YAEnE,wDAAwD;YACxD,IAAI,UAAU;gBACZ,aAAa;YACf,OAAO;gBACL,gDAAgD;gBAChD,wQAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACd;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,gCAAgC;YAC9C,wCAAwC;YACxC,wQAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd;IACF;IAEA,6CAA6C;IAC7C,MAAM,YAAY;QAChB,mEAAmE;QACnE,OACE,MAAM,MAAM,KAAK,wKAAA,CAAA,cAAW,CAAC,SAAS,IACtC,MAAM,kBAAkB,CAAC,uBAAuB;;IAEpD;IAEA,8CAA8C;IAC9C,MAAM,YAAY;QAChB,oEAAoE;QACpE,OACE,CAAC,MAAM,SAAS,KAAK,wKAAA,CAAA,YAAS,CAAC,GAAG,IAAI,MAAM,SAAS,KAAK,wKAAA,CAAA,YAAS,CAAC,IAAI,KACxE,MAAM,MAAM,KAAK,wKAAA,CAAA,cAAW,CAAC,SAAS;IAE1C;IAEA,iDAAiD;IACjD,MAAM,yBAAyB,CAAC;QAC9B,IAAI,cAAc;YAChB,aAAa,OAAO;QACtB;IACF;IAEA,qBACE;;0BAEE,uVAAC,2HAAA,CAAA,SAAM;gBAAC,MAAM;gBAAkB,cAAc;0BAC5C,cAAA,uVAAC,2HAAA,CAAA,gBAAa;;sCACZ,uVAAC,2HAAA,CAAA,eAAY;;8CACX,uVAAC,2HAAA,CAAA,cAAW;8CAAC;;;;;;8CACb,uVAAC,2HAAA,CAAA,oBAAiB;;wCAAC;sDAEjB,uVAAC;4CAAE,WAAU;sDAAO;;;;;;;;;;;;;;;;;;sCAGxB,uVAAC,2HAAA,CAAA,eAAY;;8CACX,uVAAC,2HAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAU,SAAS,IAAM,oBAAoB;8CAAQ;;;;;;8CACrE,uVAAC,2HAAA,CAAA,SAAM;oCACL,SAAS;wCACP,IAAI,oBAAoB;4CACtB,mBAAmB;wCACrB;wCACA,oBAAoB;oCACtB;8CACD;;;;;;;;;;;;;;;;;;;;;;;0BAOP,uVAAC;gBAAI,WAAU;0BACb,cAAA,uVAAC,qIAAA,CAAA,eAAY;;sCACX,uVAAC,qIAAA,CAAA,sBAAmB;4BAAC,OAAO;sCAC1B,cAAA,uVAAC,2HAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,WAAU;;kDAEV,uVAAC,8SAAA,CAAA,eAAY;wCAAC,WAAU;;;;;;kDACxB,uVAAC;wCAAK,WAAU;kDAAU;;;;;;;;;;;;;;;;;sCAG9B,uVAAC,qIAAA,CAAA,sBAAmB;4BAAC,OAAM;4BAAM,WAAU;;8CACzC,uVAAC,qIAAA,CAAA,mBAAgB;oCAAC,SAAS,IAAM,iBAAiB;;sDAChD,uVAAC,oRAAA,CAAA,MAAG;4CAAC,WAAU;;;;;;sDACf,uVAAC;4CAAK,WAAU;sDAAiB;;;;;;sDACjC,uVAAC,qIAAA,CAAA,uBAAoB;4CAAC,WAAU;sDAAU;;;;;;;;;;;;gCAG3C,eAAe,oCACd;;sDACE,uVAAC,qIAAA,CAAA,wBAAqB;;;;;sDACtB,uVAAC,qIAAA,CAAA,mBAAgB;4CAAC,SAAS,IAAM,oBAAoB;;8DACnD,uVAAC,0SAAA,CAAA,eAAY;oDAAC,WAAU;;;;;;8DACxB,uVAAC;oDAAK,WAAU;8DAA+B;;;;;;;;;;;;;;gCAKpD,eAAe,8BACd;;sDACE,uVAAC,qIAAA,CAAA,wBAAqB;;;;;wCACrB,MAAM,YAAY,KAAK,wKAAA,CAAA,eAAY,CAAC,kBAAkB,GACrD,mEAAmE;sDACnE,uVAAC,qIAAA,CAAA,mBAAgB;4CAAC,UAAU,CAAC;gDAC3B,4CAA4C;gDAC5C,EAAE,cAAc;4CAClB;sDACE,cAAA,uVAAC,+HAAA,CAAA,aAAU;gDACT,OAAM;gDACN,aAAY;gDACZ,YAAW;gDACX,aAAY;gDACZ,WAAW,IAAM,uBAAuB;gDACxC,UAAU,IAAM,uBAAuB;0DAEvC,cAAA,uVAAC;oDAAI,WAAU;;sEACb,uVAAC,wRAAA,CAAA,QAAK;4DAAC,WAAU;;;;;;sEACjB,uVAAC;4DAAK,WAAU;sEAAgC;;;;;;;;;;;;;;;;;;;;;mDAKtD,2CAA2C;sDAC3C,uVAAC,qIAAA,CAAA,mBAAgB;4CAAC,SAAS,IAAM,uBAAuB;sDACtD,cAAA,uVAAC;gDAAI,WAAU;;kEACb,uVAAC,wRAAA,CAAA,QAAK;wDAAC,WAAU;;;;;;kEACjB,uVAAC;wDAAK,WAAU;kEAAgC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAWtE;AASO,SAAS,oBAAoB,EAAE,MAAM,EAAE,cAAc,EAAE,WAAW,KAAK,EAA4B;IACxG,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,8SAAA,CAAA,WAAQ,AAAD,EAAW;IAE1C,kCAAkC;IAClC,MAAM,gBAAgB;QACpB;YAAE,OAAO,wKAAA,CAAA,cAAW,CAAC,SAAS;YAAY,OAAO;YAAe,OAAO;QAA0C;QACjH;YAAE,OAAO,wKAAA,CAAA,cAAW,CAAC,UAAU;YAAY,OAAO;YAAmB,OAAO;QAAsC;QAClH;YAAE,OAAO,wKAAA,CAAA,cAAW,CAAC,SAAS;YAAY,OAAO;YAAa,OAAO;QAA4C;KAClH;IAED,MAAM,gBAAgB,cAAc,IAAI,CAAC,CAAC,SAAW,OAAO,KAAK,KAAK,WAAW,aAAa,CAAC,EAAE;IAEjG,qBACE,uVAAC,4HAAA,CAAA,UAAO;QAAC,MAAM;QAAM,cAAc;;0BACjC,uVAAC,4HAAA,CAAA,iBAAc;gBAAC,OAAO;0BACrB,cAAA,uVAAC,2HAAA,CAAA,SAAM;oBACL,SAAQ;oBACR,MAAK;oBACL,iBAAe;oBACf,UAAU;oBACV,WAAU;;sCAEV,uVAAC,0HAAA,CAAA,QAAK;4BAAC,WAAW,cAAc,KAAK;sCAClC,cAAc,KAAK;;;;;;sCAEtB,uVAAC,kTAAA,CAAA,iBAAc;4BAAC,WAAU;;;;;;;;;;;;;;;;;0BAG9B,uVAAC,4HAAA,CAAA,iBAAc;gBAAC,WAAU;0BACxB,cAAA,uVAAC,4HAAA,CAAA,UAAO;;sCACN,uVAAC,4HAAA,CAAA,eAAY;4BAAC,aAAY;;;;;;sCAC1B,uVAAC,4HAAA,CAAA,eAAY;sCAAC;;;;;;sCACd,uVAAC,4HAAA,CAAA,eAAY;sCACV,cAAc,GAAG,CAAC,CAAC,uBAClB,uVAAC,4HAAA,CAAA,cAAW;oCAEV,OAAO,OAAO,KAAK;oCACnB,UAAU;wCACR,eAAe,OAAO,KAAK;wCAC3B,QAAQ;oCACV;;sDAEA,uVAAC,wRAAA,CAAA,QAAK;4CACJ,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EACV,gBACA,WAAW,OAAO,KAAK,GAAG,gBAAgB;;;;;;sDAG9C,uVAAC,0HAAA,CAAA,QAAK;4CAAC,WAAW,OAAO,KAAK;sDAC3B,OAAO,KAAK;;;;;;;mCAdV,OAAO,KAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;AAuBjC;AAUO,SAAS,cAAc,EAC5B,KAAK,EACL,aAAa,EACb,WAAW,KAAK,EAChB,aAAa,SAAS,EACH;IACnB,MAAM,KAAK,CAAA,GAAA,8SAAA,CAAA,QAAK,AAAD;IACf,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,8SAAA,CAAA,WAAQ,AAAD,EAAW;IAC1C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,8SAAA,CAAA,WAAQ,AAAD,EAAU,OAAO,cAAc;IAE1E,wCAAwC;IACxC,MAAM,cAAc;QAClB,MAAM,WAAW,OAAO;QACxB,IAAI,CAAC,MAAM,aAAa,WAAW,GAAG;YACpC,cAAc;QAChB;QACA,QAAQ;IACV;IAEA,0CAA0C;IAC1C,MAAM,mBAAmB,CAAC;QACxB,IAAI,QAAQ;YACV,6CAA6C;YAC7C,cAAc,OAAO,cAAc;QACrC;QACA,QAAQ;IACV;IAEA,2BAA2B;IAC3B,MAAM,eAAe;QACnB,IAAI,CAAC,OAAO,OAAO;QACnB,OAAO,eAAe;IACxB;IAEA,qBACE,uVAAC,4HAAA,CAAA,UAAO;QAAC,MAAM;QAAM,cAAc;;0BACjC,uVAAC,4HAAA,CAAA,iBAAc;gBAAC,OAAO;0BACrB,cAAA,uVAAC,2HAAA,CAAA,SAAM;oBACL,IAAI;oBACJ,WAAU;oBACV,SAAQ;oBACR,MAAK;oBACL,iBAAe;oBACf,UAAU;8BAEV,cAAA,uVAAC;wBAAI,WAAU;kCACb,cAAA,uVAAC;4BAAK,WAAU;sCAAwB;;;;;;;;;;;;;;;;;;;;;0BAI9C,uVAAC,4HAAA,CAAA,iBAAc;gBAAC,WAAU;gBAAW,OAAM;0BACzC,cAAA,uVAAC;oBAAI,WAAU;;sCACb,uVAAC;4BAAI,WAAU;;8CACb,uVAAC,0HAAA,CAAA,QAAK;oCAAC,SAAS,CAAC,YAAY,EAAE,IAAI;8CAAE;;;;;;8CACrC,uVAAC;oCAAI,WAAU;;sDACb,uVAAC,oUAAA,CAAA,mBAAgB;4CAAC,WAAU;;;;;;sDAC5B,uVAAC,0HAAA,CAAA,QAAK;4CACJ,IAAI,CAAC,YAAY,EAAE,IAAI;4CACvB,OAAO;4CACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;4CAC7C,aAAY;4CACZ,MAAK;4CACL,KAAI;4CACJ,WAAU;;;;;;;;;;;;;;;;;;sCAKhB,uVAAC,2HAAA,CAAA,SAAM;4BACL,SAAS;4BACT,WAAU;4BACV,MAAK;sCAEJ;;;;;;;;;;;;;;;;;;;;;;;AAMb;AAUO,SAAS,eAAe,EAC7B,MAAM,EACN,cAAc,EACd,WAAW,KAAK,EAChB,aAAa,SAAS,EACF;IACpB,MAAM,KAAK,CAAA,GAAA,8SAAA,CAAA,QAAK,AAAD;IACf,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,8SAAA,CAAA,WAAQ,AAAD,EAAW;IAC1C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,8SAAA,CAAA,WAAQ,AAAD,EAAU,QAAQ,cAAc;IAE3E,wCAAwC;IACxC,MAAM,cAAc;QAClB,MAAM,WAAW,OAAO;QACxB,IAAI,CAAC,MAAM,aAAa,WAAW,GAAG;YACpC,eAAe;QACjB;QACA,QAAQ;IACV;IAEA,0CAA0C;IAC1C,MAAM,mBAAmB,CAAC;QACxB,IAAI,QAAQ;YACV,6CAA6C;YAC7C,cAAc,QAAQ,cAAc;QACtC;QACA,QAAQ;IACV;IAEA,kCAAkC;IAClC,MAAM,gBAAgB;QACpB,IAAI,WAAW,aAAa,WAAW,MAAM,OAAO;QACpD,OAAO,GAAG,OAAO,cAAc,IAAI;IACrC;IAEA,qBACE,uVAAC,4HAAA,CAAA,UAAO;QAAC,MAAM;QAAM,cAAc;;0BACjC,uVAAC,4HAAA,CAAA,iBAAc;gBAAC,OAAO;0BACrB,cAAA,uVAAC,2HAAA,CAAA,SAAM;oBACL,IAAI;oBACJ,WAAU;oBACV,SAAQ;oBACR,MAAK;oBACL,iBAAe;oBACf,UAAU;8BAEV,cAAA,uVAAC;wBAAI,WAAU;kCACb,cAAA,uVAAC;4BAAK,WAAU;sCAAwB;;;;;;;;;;;;;;;;;;;;;0BAI9C,uVAAC,4HAAA,CAAA,iBAAc;gBAAC,WAAU;gBAAW,OAAM;0BACzC,cAAA,uVAAC;oBAAI,WAAU;;sCACb,uVAAC;4BAAI,WAAU;;8CACb,uVAAC,0HAAA,CAAA,QAAK;oCAAC,SAAS,CAAC,aAAa,EAAE,IAAI;8CAAE;;;;;;8CACtC,uVAAC,0HAAA,CAAA,QAAK;oCACJ,IAAI,CAAC,aAAa,EAAE,IAAI;oCACxB,OAAO;oCACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;oCAC7C,aAAY;oCACZ,MAAK;oCACL,KAAI;oCACJ,MAAK;;;;;;;;;;;;sCAIT,uVAAC,2HAAA,CAAA,SAAM;4BACL,SAAS;4BACT,WAAU;4BACV,MAAK;sCAEJ;;;;;;;;;;;;;;;;;;;;;;;AAMb;AASO,SAAS,oBAAoB,EAClC,YAAY,EACZ,kBAAkB,EAClB,YAAY,EACZ,YAAY,EACU;IACtB,OAAO;QACL;YACE,aAAa;YACb,QAAQ,CAAC,EAAE,MAAM,EAAE;gBACjB,qBACE,uVAAC,2HAAA,CAAA,SAAM;oBACL,SAAQ;oBACR,MAAK;oBACL,SAAS,IAAM,OAAO,aAAa,CAAC,OAAO,WAAW,OAAO;oBAC7D,WAAU;oBACV,kBAAe;;sCAEf,uVAAC;4BAAK,WAAU;sCAAU;;;;;;sCAC1B,uVAAC,4SAAA,CAAA,cAAW;4BAAC,WAAU;;;;;;;;;;;;YAG7B;YACA,MAAM;gBACJ,QAAQ;gBACR,UAAU;gBACV,UAAU;YACZ;YACA,MAAM,CAAC,EAAE,GAAG,EAAE;gBACZ,MAAM,iBAAiB,IAAI,QAAQ,CAAC,cAAc;gBAClD,qBACE,uVAAC;oBAAI,WAAU;8BACZ,kBAAkB;;;;;;YAGzB;YACA,eAAe;QACjB;QACA;YACE,aAAa;YACb,QAAQ,CAAC,EAAE,MAAM,EAAE;gBACjB,qBACE,uVAAC,2HAAA,CAAA,SAAM;oBACL,SAAQ;oBACR,MAAK;oBACL,SAAS,IAAM,OAAO,aAAa,CAAC,OAAO,WAAW,OAAO;oBAC7D,WAAU;oBACV,kBAAe;;sCAEf,uVAAC;4BAAK,WAAU;sCAAU;;;;;;sCAC1B,uVAAC,4SAAA,CAAA,cAAW;4BAAC,WAAU;;;;;;;;;;;;YAG7B;YACA,MAAM;gBACJ,QAAQ;YACV;YACA,MAAM,CAAC,EAAE,GAAG,EAAE;gBACZ,MAAM,OAAO,IAAI,QAAQ,CAAC;gBAC1B,qBACE,uVAAC,0HAAA,CAAA,QAAK;oBAAC,WAAW,kBAAkB;8BACjC,iBAAiB;;;;;;YAGxB;YACA,eAAe;QACjB;QACA;YACE,aAAa;YACb,QAAQ,CAAC,EAAE,MAAM,EAAE;gBACjB,qBACE,uVAAC,2HAAA,CAAA,SAAM;oBACL,SAAQ;oBACR,MAAK;oBACL,SAAS,IAAM,OAAO,aAAa,CAAC,OAAO,WAAW,OAAO;oBAC7D,WAAU;oBACV,kBAAe;;sCAEf,uVAAC;4BAAK,WAAU;sCAAU;;;;;;sCAC1B,uVAAC,4SAAA,CAAA,cAAW;4BAAC,WAAU;;;;;;;;;;;;YAG7B;YACA,MAAM;gBACJ,QAAQ;YACV;YACA,MAAM,CAAC,EAAE,GAAG,EAAE;gBACZ,MAAM,eAAe,IAAI,QAAQ,CAAC;gBAElC,IAAI,CAAC,gBAAgB,IAAI,QAAQ,CAAC,SAAS,KAAK,wKAAA,CAAA,YAAS,CAAC,GAAG,EAAE;oBAC7D,qBAAO,uVAAC;wBAAK,WAAU;kCAAgC;;;;;;gBACzD;gBAEA,qBACE,uVAAC,0HAAA,CAAA,QAAK;oBAAC,WAAW,qBAAqB;8BACpC,iBAAiB,wKAAA,CAAA,eAAY,CAAC,MAAM,GAAG,sBAAsB;;;;;;YAGpE;YACA,eAAe;QACjB;QACA;YACE,aAAa;YACb,QAAQ,CAAC,EAAE,MAAM,EAAE;gBACjB,qBACE,uVAAC,2HAAA,CAAA,SAAM;oBACL,SAAQ;oBACR,MAAK;oBACL,SAAS,IAAM,OAAO,aAAa,CAAC,OAAO,WAAW,OAAO;oBAC7D,WAAU;oBACV,kBAAe;;sCAEf,uVAAC;4BAAK,WAAU;sCAAU;;;;;;sCAC1B,uVAAC,4SAAA,CAAA,cAAW;4BAAC,WAAU;;;;;;;;;;;;YAG7B;YACA,MAAM;gBACJ,QAAQ;YACV;YACA,MAAM,CAAC,EAAE,GAAG,EAAE;gBACZ,MAAM,QAAQ,IAAI,QAAQ;gBAC1B,MAAM,aAAa,uBAAuB,OAAO,gBAAgB;gBAEjE,qBACE,uVAAC;oBAAI,WAAU;8BACb,cAAA,uVAAC;wBAAK,WAAW,cAAc;;4BAC5B,eAAe;4BAAY;;;;;;;;;;;;YAIpC;YACA,eAAe;QACjB;QACA;YACE,aAAa;YACb,QAAQ,CAAC,EAAE,MAAM,EAAE;gBACjB,qBACE,uVAAC,2HAAA,CAAA,SAAM;oBACL,SAAQ;oBACR,MAAK;oBACL,SAAS,IAAM,OAAO,aAAa,CAAC,OAAO,WAAW,OAAO;oBAC7D,WAAU;oBACV,kBAAe;;sCAEf,uVAAC;4BAAK,WAAU;sCAAU;;;;;;sCAC1B,uVAAC,4SAAA,CAAA,cAAW;4BAAC,WAAU;;;;;;;;;;;;YAG7B;YACA,MAAM;gBACJ,QAAQ;YACV;YACA,MAAM,CAAC,EAAE,GAAG,EAAE;gBACZ,MAAM,QAAQ,IAAI,QAAQ;gBAC1B,MAAM,kBAAkB,MAAM,eAAe;gBAE7C,qBACE,uVAAC;oBAAI,WAAU;8BACb,cAAA,uVAAC;;4BACE,kBAAkB,eAAe,mBAAmB;4BAAI;;;;;;;;;;;;YAIjE;YACA,eAAe;QACjB;QACA;YACE,IAAI;YACJ,QAAQ;gBACN,qBACE,uVAAC,2HAAA,CAAA,SAAM;oBACL,SAAQ;oBACR,MAAK;oBACL,WAAU;oBACV,kBAAe;8BAEf,cAAA,uVAAC;wBAAK,WAAU;kCAAU;;;;;;;;;;;YAGhC;YACA,MAAM;gBACJ,QAAQ;YACV;YACA,MAAM,CAAC,EAAE,GAAG,EAAE;gBACZ,MAAM,QAAQ,IAAI,QAAQ;gBAC1B,gEAAgE;gBAChE,MAAM,cAAc,MAAM,OAAO,EAAE,OAAO,CAAC,KAAK;oBAC9C,MAAM,SAAS,OAAO,QAAQ,IAAI;oBAClC,MAAM,SAAS,OAAO,OAAO,EAAE,UAAU,GAAG,8CAA8C;oBAC1F,OAAO,MAAO,SAAS;gBACzB,GAAG,MAAM;gBACT,qBACE,uVAAC;oBAAI,WAAU;8BACZ,YAAY,cAAc;;;;;;YAGjC;YACA,eAAe;QACjB;QACA;YACE,aAAa;YACb,QAAQ,CAAC,EAAE,MAAM,EAAE;gBACjB,qBACE,uVAAC,2HAAA,CAAA,SAAM;oBACL,SAAQ;oBACR,MAAK;oBACL,SAAS,IAAM,OAAO,aAAa,CAAC,OAAO,WAAW,OAAO;oBAC7D,WAAU;oBACV,kBAAe;;sCAEf,uVAAC;4BAAK,WAAU;sCAAU;;;;;;sCAC1B,uVAAC,4SAAA,CAAA,cAAW;4BAAC,WAAU;;;;;;;;;;;;YAG7B;YACA,MAAM;gBACJ,QAAQ;YACV;YACA,MAAM,CAAC,EAAE,GAAG,EAAE;gBACZ,MAAM,eAAe,IAAI,QAAQ,CAAC;gBAElC,qBACE,uVAAC;oBAAI,WAAU;8BACb,cAAA,uVAAC;;4BAAM,eAAe;4BAAc;;;;;;;;;;;;YAG1C;YACA,eAAe;QACjB;QACA;YACE,aAAa;YACb,QAAQ,CAAC,EAAE,MAAM,EAAE;gBACjB,qBACE,uVAAC,2HAAA,CAAA,SAAM;oBACL,SAAQ;oBACR,MAAK;oBACL,SAAS,IAAM,OAAO,aAAa,CAAC,OAAO,WAAW,OAAO;oBAC7D,WAAU;oBACV,kBAAe;;sCAEf,uVAAC;4BAAK,WAAU;sCAAU;;;;;;sCAC1B,uVAAC,4SAAA,CAAA,cAAW;4BAAC,WAAU;;;;;;;;;;;;YAG7B;YACA,MAAM;gBACJ,QAAQ;YACV;YACA,MAAM,CAAC,EAAE,GAAG,EAAE;gBACZ,MAAM,QAAQ,IAAI,QAAQ;gBAC1B,MAAM,kBAAkB,4BAA4B,OAAO,gBAAgB;gBAE3E,qBACE,uVAAC;oBAAI,WAAU;8BACb,cAAA,uVAAC;wBAAK,WAAW,cAAc;;4BAC5B,eAAe;4BAAiB;;;;;;;;;;;;YAIzC;YACA,eAAe;QACjB;QACA;YACE,aAAa;YACb,QAAQ,CAAC,EAAE,MAAM,EAAE;gBACjB,qBACE,uVAAC,2HAAA,CAAA,SAAM;oBACL,SAAQ;oBACR,MAAK;oBACL,SAAS,IAAM,OAAO,aAAa,CAAC,OAAO,WAAW,OAAO;oBAC7D,WAAU;oBACV,kBAAe;;sCAEf,uVAAC;4BAAK,WAAU;sCAAU;;;;;;sCAC1B,uVAAC,4SAAA,CAAA,cAAW;4BAAC,WAAU;;;;;;;;;;;;YAG7B;YACA,MAAM;gBACJ,QAAQ;YACV;YACA,MAAM,CAAC,EAAE,GAAG,EAAE;gBACZ,MAAM,aAAa,IAAI,QAAQ,CAAC;gBAEhC,qBACE,uVAAC;oBAAI,WAAU;8BACb,cAAA,uVAAC;;4BAAM,eAAe;4BAAY;;;;;;;;;;;;YAGxC;YACA,eAAe;QACjB;QACA;YACE,aAAa;YACb,QAAQ,CAAC,EAAE,MAAM,EAAE;gBACjB,qBACE,uVAAC,2HAAA,CAAA,SAAM;oBACL,SAAQ;oBACR,MAAK;oBACL,SAAS,IAAM,OAAO,aAAa,CAAC,OAAO,WAAW,OAAO;oBAC7D,WAAU;oBACV,kBAAe;;sCAEf,uVAAC;4BAAK,WAAU;sCAAU;;;;;;sCAC1B,uVAAC,4SAAA,CAAA,cAAW;4BAAC,WAAU;;;;;;;;;;;;YAG7B;YACA,MAAM;gBACJ,QAAQ;YACV;YACA,MAAM,CAAC,EAAE,GAAG,EAAE;gBACZ,MAAM,OAAO,IAAI,QAAQ;gBACzB,qBACE,uVAAC,4IAAA,CAAA,kBAAe;oBACd,MAAM,KAAK,kBAAkB;oBAC7B,WAAU;oBACV,eAAc;;;;;;YAGpB;YACA,eAAe;YACf,MAAM;QACR;QACA;YACE,aAAa;YACb,QAAQ,CAAC,EAAE,MAAM,EAAE;gBACjB,qBACE,uVAAC,2HAAA,CAAA,SAAM;oBACL,SAAQ;oBACR,MAAK;oBACL,SAAS,IAAM,OAAO,aAAa,CAAC,OAAO,WAAW,OAAO;oBAC7D,WAAU;oBACV,kBAAe;;sCAEf,uVAAC;4BAAK,WAAU;sCAAU;;;;;;sCAC1B,uVAAC,4SAAA,CAAA,cAAW;4BAAC,WAAU;;;;;;;;;;;;YAG7B;YACA,MAAM;gBACJ,QAAQ;YACV;YACA,MAAM,CAAC,EAAE,GAAG,EAAE;gBACZ,MAAM,OAAO,IAAI,QAAQ;gBACzB,qBACE,uVAAC,4IAAA,CAAA,kBAAe;oBACd,MAAM,KAAK,YAAY;oBACvB,WAAU;oBACV,eAAc;;;;;;YAGpB;YACA,eAAe;YACf,MAAM;QACR;QACA;YACE,aAAa;YACb,QAAQ,CAAC,EAAE,MAAM,EAAE;gBACjB,qBACE,uVAAC,2HAAA,CAAA,SAAM;oBACL,SAAQ;oBACR,MAAK;oBACL,SAAS,IAAM,OAAO,aAAa,CAAC,OAAO,WAAW,OAAO;oBAC7D,WAAU;oBACV,kBAAe;;sCAEf,uVAAC;4BAAK,WAAU;sCAAU;;;;;;sCAC1B,uVAAC,4SAAA,CAAA,cAAW;4BAAC,WAAU;;;;;;;;;;;;YAG7B;YACA,MAAM;gBACJ,QAAQ;YACV;YACA,MAAM,CAAC,EAAE,GAAG,EAAE;gBACZ,MAAM,gBAAgB,IAAI,QAAQ,CAAC,aAAa;gBAEhD,qBACE,uVAAC,0HAAA,CAAA,QAAK;oBAAC,WAAW,sBAAsB;8BACrC,qBAAqB;;;;;;YAG5B;YACA,eAAe;YACf,MAAM;QACR;QACA;YACE,aAAa;YACb,QAAQ,CAAC,EAAE,MAAM,EAAE;gBACjB,qBACE,uVAAC,2HAAA,CAAA,SAAM;oBACL,SAAQ;oBACR,MAAK;oBACL,SAAS,IAAM,OAAO,aAAa,CAAC,OAAO,WAAW,OAAO;oBAC7D,WAAU;oBACV,kBAAe;;sCAEf,uVAAC;4BAAK,WAAU;sCAAU;;;;;;sCAC1B,uVAAC,4SAAA,CAAA,cAAW;4BAAC,WAAU;;;;;;;;;;;;YAG7B;YACA,MAAM;gBACJ,QAAQ;YACV;YACA,MAAM,CAAC,EAAE,GAAG,EAAE;gBACZ,MAAM,SAAS,IAAI,QAAQ,CAAC;gBAE5B,qBACE,uVAAC,0HAAA,CAAA,QAAK;oBAAC,WAAW,eAAe;8BAC9B,cAAc;;;;;;YAGrB;YACA,eAAe;YACf,MAAM;QACR;QACA;YACE,aAAa;YACb,QAAQ,CAAC,EAAE,MAAM,EAAE;gBACjB,qBACE,uVAAC,2HAAA,CAAA,SAAM;oBACL,SAAQ;oBACR,MAAK;oBACL,SAAS,IAAM,OAAO,aAAa,CAAC,OAAO,WAAW,OAAO;oBAC7D,WAAU;oBACV,kBAAe;;sCAEf,uVAAC;4BAAK,WAAU;sCAAU;;;;;;sCAC1B,uVAAC,4SAAA,CAAA,cAAW;4BAAC,WAAU;;;;;;;;;;;;YAG7B;YACA,MAAM;gBACJ,QAAQ;YACV;YACA,MAAM,CAAC,EAAE,GAAG,EAAE;gBACZ,MAAM,OAAO,IAAI,QAAQ;gBACzB,qBACE,uVAAC,4IAAA,CAAA,kBAAe;oBACd,MAAM,KAAK,SAAS;oBACpB,WAAU;oBACV,eAAc;;;;;;YAGpB;YACA,eAAe;YACf,MAAM;QACR;QACA;YACE,IAAI;YACJ,MAAM;YACN,cAAc;YACd,QAAQ,kBAAM,uVAAC;oBAAI,kBAAe;;;;;;YAClC,MAAM,CAAC,EAAE,GAAG,EAAE,iBACZ,uVAAC;oBACC,KAAK;oBACL,cAAc;oBACd,oBAAoB;oBACpB,cAAc;;;;;;YAGlB,MAAM;gBACJ,UAAU;gBACV,UAAU;gBACV,QAAQ;YACV;QACF;KACD;AACH", "debugId": null}}, {"offset": {"line": 2005, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/bloomgoo/gold-exchange-platform/frontend/components/common/selector/column-visibility-selector.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { But<PERSON> } from '@/components/ui/button';\r\nimport { Checkbox } from '@/components/ui/checkbox';\r\nimport {\r\n  DropdownMenu,\r\n  DropdownMenuContent,\r\n  DropdownMenuTrigger,\r\n} from '@/components/ui/dropdown-menu';\r\nimport { Eye, Search, Settings2 } from 'lucide-react';\r\nimport { Table } from '@tanstack/react-table';\r\nimport { Input } from '@/components/ui/input';\r\nimport { useState, useMemo } from 'react';\r\n\r\n// Định nghĩa kiểu dữ liệu cho thuộc tính meta của cột\r\ndeclare module '@tanstack/react-table' {\r\n  interface ColumnMeta<TData, TValue> {\r\n    isSticky?: boolean;\r\n    position?: 'left' | 'right';\r\n    header?: string;\r\n  }\r\n}\r\n\r\ninterface ColumnVisibilitySelectorProps<TData> {\r\n  table: Table<TData>;\r\n  className?: string;\r\n  buttonClassName?: string;\r\n  contentClassName?: string;\r\n  label?: string;\r\n}\r\n\r\nexport function ColumnVisibilitySelector<TData>({\r\n  table,\r\n  className,\r\n  buttonClassName,\r\n  contentClassName = 'w-[250px] max-h-[var(--radix-dropdown-menu-content-available-height)] overflow-y-auto',\r\n  label = 'Tất cả',\r\n}: ColumnVisibilitySelectorProps<TData>) {\r\n  // State để lưu từ khóa tìm kiếm\r\n  const [searchQuery, setSearchQuery] = useState('');\r\n  // Lấy tất cả các cột có thể ẩn/hiện\r\n  const allColumns = useMemo(() => {\r\n    return table.getAllColumns().filter(column => column.getCanHide());\r\n  }, [table]);\r\n\r\n  // Lấy các cột đang hiển thị\r\n  const visibleColumns = useMemo(() => {\r\n    return allColumns.filter(column => column.getIsVisible());\r\n  }, [allColumns, table.getState().columnVisibility]);\r\n\r\n  // Lọc các cột dựa trên từ khóa tìm kiếm\r\n  const filteredColumns = useMemo(() => {\r\n    if (!searchQuery.trim()) {\r\n      return allColumns;\r\n    }\r\n\r\n    const query = searchQuery.toLowerCase().trim();\r\n    return allColumns.filter(column => {\r\n      // Lấy tên cột thân thiện từ header\r\n      let header = column.id;\r\n\r\n      // Ưu tiên sử dụng meta.header\r\n      if (column.columnDef.meta?.header) {\r\n        header = column.columnDef.meta.header;\r\n      }\r\n      // Nếu header là string, sử dụng trực tiếp\r\n      else if (typeof column.columnDef.header === 'string') {\r\n        header = column.columnDef.header;\r\n      }\r\n      // Nếu header là function, tìm kiếm phần tử span chứa tên cột\r\n      else if (typeof column.columnDef.header === 'function') {\r\n        const headerElement = document.querySelector(`[data-column-id=\"${column.id}\"] span`);\r\n        if (headerElement && headerElement.textContent) {\r\n          header = headerElement.textContent;\r\n        }\r\n      }\r\n\r\n      return header.toString().toLowerCase().includes(query);\r\n    });\r\n  }, [allColumns, searchQuery]);\r\n\r\n  // Hàm để toggle tất cả các cột\r\n  const toggleAllColumns = (checked: boolean) => {\r\n    table.toggleAllColumnsVisible(checked);\r\n  };\r\n\r\n  return (\r\n    <div className={className}>\r\n      <DropdownMenu>\r\n        <DropdownMenuTrigger asChild>\r\n          <Button\r\n            variant=\"ghost\"\r\n            size=\"icon\"\r\n            className={`relative ${buttonClassName || ''}`}\r\n          >\r\n            <Settings2 className=\"h-4 w-4\" />\r\n            {visibleColumns.length < allColumns.length && (\r\n              <span className=\"absolute -top-1 -right-1 h-2 w-2 rounded-full bg-blue-600\" />\r\n            )}\r\n          </Button>\r\n        </DropdownMenuTrigger>\r\n        <DropdownMenuContent\r\n          align=\"end\"\r\n          className={contentClassName}\r\n        >\r\n          <div className=\"sticky top-0 bg-background z-10 border-b\">\r\n            <div className=\"p-2\">\r\n              <div className=\"flex items-center space-x-2 px-1 py-1\">\r\n                <Checkbox\r\n                  checked={visibleColumns.length === allColumns.length}\r\n                  onCheckedChange={(checked) => toggleAllColumns(!!checked)}\r\n                  id=\"all-columns\"\r\n                />\r\n                <label\r\n                  htmlFor=\"all-columns\"\r\n                  className=\"text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70\"\r\n                >\r\n                  {label}\r\n                </label>\r\n              </div>\r\n\r\n              {/* Thêm ô tìm kiếm */}\r\n              <div className=\"relative mt-2\">\r\n                <Search className=\"absolute left-2 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground\" />\r\n                <Input\r\n                  placeholder=\"Tìm kiếm cột...\"\r\n                  value={searchQuery}\r\n                  onChange={(e) => setSearchQuery(e.target.value)}\r\n                  className=\"pl-8 h-8 text-sm\"\r\n                />\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <div className=\"p-2 pt-0\">\r\n            {filteredColumns.length === 0 ? (\r\n              <div className=\"py-6 text-center text-sm text-muted-foreground\">\r\n                Không tìm thấy cột nào\r\n              </div>\r\n            ) : (\r\n              filteredColumns.map(column => {\r\n                const isVisible = column.getIsVisible();\r\n                return (\r\n                  <div\r\n                    key={column.id}\r\n                    className=\"flex items-center space-x-2 px-1 py-1.5 rounded hover:bg-accent\"\r\n                  >\r\n                    <Checkbox\r\n                      checked={isVisible}\r\n                      onCheckedChange={(checked) => column.toggleVisibility(!!checked)}\r\n                      id={column.id}\r\n                    />\r\n                    <label\r\n                      htmlFor={column.id}\r\n                      className=\"flex-1 text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70\"\r\n                    >\r\n                      {(() => {\r\n                        // Ưu tiên sử dụng meta.header\r\n                        if (column.columnDef.meta?.header) {\r\n                          return column.columnDef.meta.header;\r\n                        }\r\n\r\n                        // Nếu header là string, sử dụng trực tiếp\r\n                        if (typeof column.columnDef.header === 'string') {\r\n                          return column.columnDef.header;\r\n                        }\r\n\r\n                        // Tìm kiếm phần tử span trong header\r\n                        const headerElement = document.querySelector(`[data-column-id=\"${column.id}\"] span`);\r\n                        if (headerElement && headerElement.textContent) {\r\n                          return headerElement.textContent;\r\n                        }\r\n\r\n                        // Fallback: Sử dụng ID\r\n                        return column.id;\r\n                      })()}\r\n                    </label>\r\n                  </div>\r\n                );\r\n              })\r\n            )}\r\n          </div>\r\n        </DropdownMenuContent>\r\n      </DropdownMenu>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAKA;AAAA;AAEA;AACA;AAZA;;;;;;;;AA+BO,SAAS,yBAAgC,EAC9C,KAAK,EACL,SAAS,EACT,eAAe,EACf,mBAAmB,uFAAuF,EAC1G,QAAQ,QAAQ,EACqB;IACrC,gCAAgC;IAChC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,8SAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,oCAAoC;IACpC,MAAM,aAAa,CAAA,GAAA,8SAAA,CAAA,UAAO,AAAD,EAAE;QACzB,OAAO,MAAM,aAAa,GAAG,MAAM,CAAC,CAAA,SAAU,OAAO,UAAU;IACjE,GAAG;QAAC;KAAM;IAEV,4BAA4B;IAC5B,MAAM,iBAAiB,CAAA,GAAA,8SAAA,CAAA,UAAO,AAAD,EAAE;QAC7B,OAAO,WAAW,MAAM,CAAC,CAAA,SAAU,OAAO,YAAY;IACxD,GAAG;QAAC;QAAY,MAAM,QAAQ,GAAG,gBAAgB;KAAC;IAElD,wCAAwC;IACxC,MAAM,kBAAkB,CAAA,GAAA,8SAAA,CAAA,UAAO,AAAD,EAAE;QAC9B,IAAI,CAAC,YAAY,IAAI,IAAI;YACvB,OAAO;QACT;QAEA,MAAM,QAAQ,YAAY,WAAW,GAAG,IAAI;QAC5C,OAAO,WAAW,MAAM,CAAC,CAAA;YACvB,mCAAmC;YACnC,IAAI,SAAS,OAAO,EAAE;YAEtB,8BAA8B;YAC9B,IAAI,OAAO,SAAS,CAAC,IAAI,EAAE,QAAQ;gBACjC,SAAS,OAAO,SAAS,CAAC,IAAI,CAAC,MAAM;YACvC,OAEK,IAAI,OAAO,OAAO,SAAS,CAAC,MAAM,KAAK,UAAU;gBACpD,SAAS,OAAO,SAAS,CAAC,MAAM;YAClC,OAEK,IAAI,OAAO,OAAO,SAAS,CAAC,MAAM,KAAK,YAAY;gBACtD,MAAM,gBAAgB,SAAS,aAAa,CAAC,CAAC,iBAAiB,EAAE,OAAO,EAAE,CAAC,OAAO,CAAC;gBACnF,IAAI,iBAAiB,cAAc,WAAW,EAAE;oBAC9C,SAAS,cAAc,WAAW;gBACpC;YACF;YAEA,OAAO,OAAO,QAAQ,GAAG,WAAW,GAAG,QAAQ,CAAC;QAClD;IACF,GAAG;QAAC;QAAY;KAAY;IAE5B,+BAA+B;IAC/B,MAAM,mBAAmB,CAAC;QACxB,MAAM,uBAAuB,CAAC;IAChC;IAEA,qBACE,uVAAC;QAAI,WAAW;kBACd,cAAA,uVAAC,qIAAA,CAAA,eAAY;;8BACX,uVAAC,qIAAA,CAAA,sBAAmB;oBAAC,OAAO;8BAC1B,cAAA,uVAAC,2HAAA,CAAA,SAAM;wBACL,SAAQ;wBACR,MAAK;wBACL,WAAW,CAAC,SAAS,EAAE,mBAAmB,IAAI;;0CAE9C,uVAAC,oSAAA,CAAA,YAAS;gCAAC,WAAU;;;;;;4BACpB,eAAe,MAAM,GAAG,WAAW,MAAM,kBACxC,uVAAC;gCAAK,WAAU;;;;;;;;;;;;;;;;;8BAItB,uVAAC,qIAAA,CAAA,sBAAmB;oBAClB,OAAM;oBACN,WAAW;;sCAEX,uVAAC;4BAAI,WAAU;sCACb,cAAA,uVAAC;gCAAI,WAAU;;kDACb,uVAAC;wCAAI,WAAU;;0DACb,uVAAC,6HAAA,CAAA,WAAQ;gDACP,SAAS,eAAe,MAAM,KAAK,WAAW,MAAM;gDACpD,iBAAiB,CAAC,UAAY,iBAAiB,CAAC,CAAC;gDACjD,IAAG;;;;;;0DAEL,uVAAC;gDACC,SAAQ;gDACR,WAAU;0DAET;;;;;;;;;;;;kDAKL,uVAAC;wCAAI,WAAU;;0DACb,uVAAC,0RAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;0DAClB,uVAAC,0HAAA,CAAA,QAAK;gDACJ,aAAY;gDACZ,OAAO;gDACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;gDAC9C,WAAU;;;;;;;;;;;;;;;;;;;;;;;sCAMlB,uVAAC;4BAAI,WAAU;sCACZ,gBAAgB,MAAM,KAAK,kBAC1B,uVAAC;gCAAI,WAAU;0CAAiD;;;;;uCAIhE,gBAAgB,GAAG,CAAC,CAAA;gCAClB,MAAM,YAAY,OAAO,YAAY;gCACrC,qBACE,uVAAC;oCAEC,WAAU;;sDAEV,uVAAC,6HAAA,CAAA,WAAQ;4CACP,SAAS;4CACT,iBAAiB,CAAC,UAAY,OAAO,gBAAgB,CAAC,CAAC,CAAC;4CACxD,IAAI,OAAO,EAAE;;;;;;sDAEf,uVAAC;4CACC,SAAS,OAAO,EAAE;4CAClB,WAAU;sDAET,CAAC;gDACA,8BAA8B;gDAC9B,IAAI,OAAO,SAAS,CAAC,IAAI,EAAE,QAAQ;oDACjC,OAAO,OAAO,SAAS,CAAC,IAAI,CAAC,MAAM;gDACrC;gDAEA,0CAA0C;gDAC1C,IAAI,OAAO,OAAO,SAAS,CAAC,MAAM,KAAK,UAAU;oDAC/C,OAAO,OAAO,SAAS,CAAC,MAAM;gDAChC;gDAEA,qCAAqC;gDACrC,MAAM,gBAAgB,SAAS,aAAa,CAAC,CAAC,iBAAiB,EAAE,OAAO,EAAE,CAAC,OAAO,CAAC;gDACnF,IAAI,iBAAiB,cAAc,WAAW,EAAE;oDAC9C,OAAO,cAAc,WAAW;gDAClC;gDAEA,uBAAuB;gDACvB,OAAO,OAAO,EAAE;4CAClB,CAAC;;;;;;;mCA/BE,OAAO,EAAE;;;;;4BAmCpB;;;;;;;;;;;;;;;;;;;;;;;AAOd", "debugId": null}}, {"offset": {"line": 2263, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/bloomgoo/gold-exchange-platform/frontend/components/common/selector/sort-selector.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { But<PERSON> } from '@/components/ui/button';\r\nimport {\r\n  Popover,\r\n  PopoverContent,\r\n  PopoverTrigger,\r\n} from '@/components/ui/popover';\r\nimport {\r\n  ArrowDownWideNarrow,\r\n  ArrowUpDown,\r\n  ArrowUpNarrowWide,\r\n  X,\r\n  Plus,\r\n  GripVertical,\r\n  Search\r\n} from 'lucide-react';\r\nimport { Input } from '@/components/ui/input';\r\nimport { SortingState, Table } from '@tanstack/react-table';\r\nimport { cn } from '@/lib/utils';\r\nimport { Badge } from '@/components/ui/badge';\r\n\r\nimport { useState, useMemo, useEffect } from 'react';\r\n\r\n// Định nghĩa kiểu dữ liệu cho thuộc tính meta của cột\r\ndeclare module '@tanstack/react-table' {\r\n  interface ColumnMeta<TData, TValue> {\r\n    isSticky?: boolean;\r\n    position?: 'left' | 'right';\r\n    header?: string;\r\n  }\r\n}\r\nimport {\r\n  DndContext,\r\n  closestCenter,\r\n  KeyboardSensor,\r\n  PointerSensor,\r\n  useSensor,\r\n  useSensors,\r\n  DragEndEvent\r\n} from '@dnd-kit/core';\r\nimport {\r\n  arrayMove,\r\n  SortableContext,\r\n  sortableKeyboardCoordinates,\r\n  useSortable,\r\n  verticalListSortingStrategy,\r\n} from '@dnd-kit/sortable';\r\nimport { CSS } from '@dnd-kit/utilities';\r\n\r\ninterface SortSelectorProps<TData> {\r\n  table: Table<TData>;\r\n  className?: string;\r\n  buttonClassName?: string;\r\n  contentClassName?: string;\r\n  maxSortFields?: number;\r\n}\r\n\r\n// Component cho mỗi mục sort có thể kéo thả\r\ninterface SortableItemProps {\r\n  id: string;\r\n  columnName: string;\r\n  direction: 'asc' | 'desc';\r\n  onDirectionChange: (direction: 'asc' | 'desc') => void;\r\n  onRemove: () => void;\r\n}\r\n\r\nfunction SortableItem({\r\n  id,\r\n  columnName,\r\n  direction,\r\n  onDirectionChange,\r\n  onRemove\r\n}: SortableItemProps) {\r\n  const {\r\n    attributes,\r\n    listeners,\r\n    setNodeRef,\r\n    transform,\r\n    transition,\r\n  } = useSortable({ id });\r\n\r\n  const style = {\r\n    transform: CSS.Transform.toString(transform),\r\n    transition,\r\n  };\r\n\r\n  return (\r\n    <div\r\n      ref={setNodeRef}\r\n      style={style}\r\n      className=\"flex items-center justify-between p-2 mb-1 bg-muted/50 rounded-md\"\r\n    >\r\n      <div className=\"flex items-center gap-2\">\r\n        <button\r\n          {...attributes}\r\n          {...listeners}\r\n          className=\"cursor-grab touch-none p-1 rounded hover:bg-muted\"\r\n        >\r\n          <GripVertical className=\"h-4 w-4 text-muted-foreground\" />\r\n        </button>\r\n        <span className=\"text-sm\">{columnName}</span>\r\n      </div>\r\n      <div className=\"flex items-center gap-1\">\r\n        <Button\r\n          variant=\"ghost\"\r\n          size=\"icon\"\r\n          className=\"h-7 w-7\"\r\n          onClick={() => onDirectionChange(direction === 'asc' ? 'desc' : 'asc')}\r\n        >\r\n          {direction === 'asc' ? (\r\n            <ArrowDownWideNarrow className=\"h-4 w-4\" />\r\n          ) : (\r\n            <ArrowUpNarrowWide className=\"h-4 w-4\" />\r\n          )}\r\n        </Button>\r\n        <Button\r\n          variant=\"ghost\"\r\n          size=\"icon\"\r\n          className=\"h-7 w-7 text-destructive\"\r\n          onClick={onRemove}\r\n        >\r\n          <X className=\"h-4 w-4\" />\r\n        </Button>\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n\r\nexport function SortSelector<TData>({\r\n  table,\r\n  className,\r\n  buttonClassName,\r\n  contentClassName = 'w-[300px]',\r\n  maxSortFields = 3,\r\n}: SortSelectorProps<TData>) {\r\n  const [open, setOpen] = useState(false);\r\n  const [searchQuery, setSearchQuery] = useState('');\r\n  const [tempSorting, setTempSorting] = useState<SortingState>([]);\r\n\r\n  // Lấy tất cả các cột có thể sắp xếp\r\n  const sortableColumns = useMemo(() => {\r\n    return table.getAllColumns().filter(column =>\r\n      column.getCanSort() && column.getIsVisible()\r\n    );\r\n  }, [table]);\r\n\r\n  // Lọc các cột dựa trên từ khóa tìm kiếm\r\n  const filteredColumns = useMemo(() => {\r\n    if (!searchQuery.trim()) {\r\n      return sortableColumns;\r\n    }\r\n\r\n    const query = searchQuery.toLowerCase().trim();\r\n    return sortableColumns.filter(column => {\r\n      // Lấy tên cột thân thiện từ header\r\n      let header = column.id;\r\n\r\n      // Ưu tiên sử dụng meta.header\r\n      if (column.columnDef.meta?.header) {\r\n        header = column.columnDef.meta.header;\r\n      }\r\n      // Nếu header là string, sử dụng trực tiếp\r\n      else if (typeof column.columnDef.header === 'string') {\r\n        header = column.columnDef.header;\r\n      }\r\n      // Nếu header là function, tìm kiếm phần tử span chứa tên cột\r\n      else if (typeof column.columnDef.header === 'function') {\r\n        const headerElement = document.querySelector(`[data-column-id=\"${column.id}\"] span`);\r\n        if (headerElement && headerElement.textContent) {\r\n          header = headerElement.textContent;\r\n        }\r\n      }\r\n\r\n      return header.toString().toLowerCase().includes(query);\r\n    });\r\n  }, [sortableColumns, searchQuery]);\r\n\r\n  // Khi mở dropdown, sao chép trạng thái sắp xếp hiện tại vào tempSorting\r\n  useEffect(() => {\r\n    if (open) {\r\n      setTempSorting(table.getState().sorting);\r\n    }\r\n  }, [open, table]);\r\n\r\n  // Lấy thông tin các cột đang được sắp xếp (dựa trên tempSorting)\r\n  const sortedColumns = useMemo(() => {\r\n    return tempSorting.map(sort => {\r\n      const column = table.getColumn(sort.id);\r\n\r\n      // Lấy tên cột thân thiện từ header\r\n      let columnName = sort.id;\r\n\r\n      if (column) {\r\n        // Ưu tiên sử dụng meta.header\r\n        if (column.columnDef.meta?.header) {\r\n          columnName = column.columnDef.meta.header;\r\n        }\r\n        // Nếu header là string, sử dụng trực tiếp\r\n        else if (typeof column.columnDef.header === 'string') {\r\n          columnName = column.columnDef.header;\r\n        }\r\n        // Nếu header là function, tìm kiếm phần tử span chứa tên cột\r\n        else if (typeof column.columnDef.header === 'function') {\r\n          const headerElement = document.querySelector(`[data-column-id=\"${sort.id}\"] span`);\r\n          if (headerElement && headerElement.textContent) {\r\n            columnName = headerElement.textContent;\r\n          }\r\n        }\r\n      }\r\n\r\n      return {\r\n        id: sort.id,\r\n        name: columnName,\r\n        direction: (sort.desc ? 'desc' : 'asc') as 'desc' | 'asc'\r\n      };\r\n    });\r\n  }, [tempSorting, table]);\r\n\r\n  // Sensors cho DnD\r\n  const sensors = useSensors(\r\n    useSensor(PointerSensor),\r\n    useSensor(KeyboardSensor, {\r\n      coordinateGetter: sortableKeyboardCoordinates,\r\n    })\r\n  );\r\n\r\n  // Xử lý khi kết thúc kéo thả\r\n  const handleDragEnd = (event: DragEndEvent) => {\r\n    const { active, over } = event;\r\n\r\n    if (over && active.id !== over.id) {\r\n      const oldIndex = sortedColumns.findIndex(item => item.id === active.id);\r\n      const newIndex = sortedColumns.findIndex(item => item.id === over.id);\r\n\r\n      const newSortedColumns = arrayMove(sortedColumns, oldIndex, newIndex);\r\n\r\n      // Cập nhật tempSorting\r\n      const newSorting = newSortedColumns.map(col => ({\r\n        id: col.id,\r\n        desc: col.direction === 'desc'\r\n      }));\r\n\r\n      setTempSorting(newSorting);\r\n    }\r\n  };\r\n\r\n  // Xử lý thêm tiêu chí sắp xếp mới\r\n  const handleAddSort = (columnId: string) => {\r\n    // Kiểm tra xem cột đã được sắp xếp chưa\r\n    const existingSort = tempSorting.find(sort => sort.id === columnId);\r\n\r\n    if (existingSort) {\r\n      // Nếu đã có, thay đổi hướng sắp xếp\r\n      const newSorting = tempSorting.map(sort => {\r\n        if (sort.id === columnId) {\r\n          return { ...sort, desc: !sort.desc };\r\n        }\r\n        return sort;\r\n      });\r\n\r\n      setTempSorting(newSorting);\r\n    } else if (tempSorting.length < maxSortFields) {\r\n      // Nếu chưa có và chưa đạt giới hạn, thêm mới\r\n      const newSorting = [\r\n        ...tempSorting,\r\n        { id: columnId, desc: false }\r\n      ];\r\n\r\n      setTempSorting(newSorting);\r\n    }\r\n\r\n    // Xóa từ khóa tìm kiếm sau khi chọn\r\n    setSearchQuery('');\r\n  };\r\n\r\n  // Xử lý thay đổi hướng sắp xếp\r\n  const handleDirectionChange = (columnId: string, direction: 'asc' | 'desc') => {\r\n    const newSorting = tempSorting.map(sort => {\r\n      if (sort.id === columnId) {\r\n        return { ...sort, desc: direction === 'desc' };\r\n      }\r\n      return sort;\r\n    });\r\n\r\n    setTempSorting(newSorting);\r\n  };\r\n\r\n  // Xử lý xóa tiêu chí sắp xếp\r\n  const handleRemoveSort = (columnId: string) => {\r\n    const newSorting = tempSorting.filter(sort => sort.id !== columnId);\r\n    setTempSorting(newSorting);\r\n  };\r\n\r\n  // Xử lý xóa tất cả tiêu chí sắp xếp\r\n  const handleClearAll = () => {\r\n    setTempSorting([]);\r\n  };\r\n\r\n  // Xử lý áp dụng các thay đổi\r\n  const handleApply = () => {\r\n    table.setSorting(tempSorting);\r\n    setOpen(false);\r\n  };\r\n\r\n  // Xử lý hủy thay đổi\r\n  const handleCancel = () => {\r\n    setTempSorting(table.getState().sorting);\r\n    setOpen(false);\r\n  };\r\n\r\n  // Lấy icon sắp xếp cho button\r\n  const getSortIcon = () => {\r\n    const currentSorting = table.getState().sorting;\r\n\r\n    if (currentSorting.length === 0) {\r\n      return <ArrowUpDown className=\"h-4 w-4\" />;\r\n    }\r\n\r\n    if (currentSorting.length === 1) {\r\n      return currentSorting[0].desc ?\r\n        <ArrowUpNarrowWide className=\"h-4 w-4\" /> :\r\n        <ArrowDownWideNarrow className=\"h-4 w-4\" />;\r\n    }\r\n\r\n    // Nếu có nhiều tiêu chí sắp xếp, hiển thị số lượng\r\n    return (\r\n      <div className=\"relative\">\r\n        <ArrowUpDown className=\"h-4 w-4\" />\r\n        <Badge\r\n          className=\"absolute -top-2 -right-2 h-4 w-4 flex items-center justify-center p-0 text-[10px]\"\r\n          variant=\"default\"\r\n        >\r\n          {currentSorting.length}\r\n        </Badge>\r\n      </div>\r\n    );\r\n  };\r\n\r\n  return (\r\n    <div className={className}>\r\n      <Popover open={open} onOpenChange={setOpen}>\r\n        <PopoverTrigger asChild>\r\n          <Button\r\n            variant=\"ghost\"\r\n            size=\"icon\"\r\n            className={cn(\"relative\", buttonClassName, table.getState().sorting.length > 0 && \"bg-accent\")}\r\n          >\r\n            {getSortIcon()}\r\n          </Button>\r\n        </PopoverTrigger>\r\n        <PopoverContent align=\"end\" className={contentClassName} onEscapeKeyDown={handleCancel} onInteractOutside={(e) => e.preventDefault()}>\r\n          <div className=\"p-0\">\r\n            <h4 className=\"mb-2 text-sm font-medium\">Sắp xếp theo</h4>\r\n\r\n            {/* Danh sách các tiêu chí sắp xếp hiện tại */}\r\n            {sortedColumns.length > 0 ? (\r\n              <div className=\"mb-2\">\r\n                <DndContext\r\n                  sensors={sensors}\r\n                  collisionDetection={closestCenter}\r\n                  onDragEnd={handleDragEnd}\r\n                >\r\n                  <SortableContext\r\n                    items={sortedColumns.map(col => col.id)}\r\n                    strategy={verticalListSortingStrategy}\r\n                  >\r\n                    {sortedColumns.map((column) => (\r\n                      <SortableItem\r\n                        key={column.id}\r\n                        id={column.id}\r\n                        columnName={column.name}\r\n                        direction={column.direction}\r\n                        onDirectionChange={(direction) =>\r\n                          handleDirectionChange(column.id, direction)\r\n                        }\r\n                        onRemove={() => handleRemoveSort(column.id)}\r\n                      />\r\n                    ))}\r\n                  </SortableContext>\r\n                </DndContext>\r\n              </div>\r\n            ) : (\r\n              <div className=\"text-sm text-muted-foreground mb-2 py-2 text-center\">\r\n                Chưa có tiêu chí sắp xếp nào\r\n              </div>\r\n            )}\r\n\r\n            {/* Phần chọn trường sắp xếp */}\r\n            {tempSorting.length < maxSortFields && (\r\n              <div className=\"mb-2\">\r\n                <div className=\"text-xs font-medium mb-1 text-muted-foreground\">Thêm tiêu chí sắp xếp</div>\r\n\r\n                {/* Ô tìm kiếm trường */}\r\n                <div className=\"relative mb-2\">\r\n                  <Search className=\"absolute left-2 top-1/2 h-3.5 w-3.5 -translate-y-1/2 text-muted-foreground\" />\r\n                  <Input\r\n                    placeholder=\"Tìm kiếm trường...\"\r\n                    value={searchQuery}\r\n                    onChange={(e) => setSearchQuery(e.target.value)}\r\n                    className=\"pl-7 h-8 text-sm\"\r\n                  />\r\n                </div>\r\n\r\n                {/* Danh sách các trường có thể sắp xếp */}\r\n                <div className=\"max-h-[150px] overflow-y-auto border rounded-md\">\r\n                  {filteredColumns.length === 0 ? (\r\n                    <div className=\"p-2 text-center text-sm text-muted-foreground\">\r\n                      Không tìm thấy trường nào\r\n                    </div>\r\n                  ) : (\r\n                    <div className=\"p-1\">\r\n                      {filteredColumns.map(column => {\r\n                        const isSelected = tempSorting.some(sort => sort.id === column.id);\r\n                        return (\r\n                          <button\r\n                            key={column.id}\r\n                            onClick={() => handleAddSort(column.id)}\r\n                            disabled={isSelected}\r\n                            className={cn(\r\n                              \"w-full text-left px-2 py-1.5 text-sm rounded-md hover:bg-accent flex items-center justify-between\",\r\n                              isSelected && \"opacity-50 cursor-not-allowed\"\r\n                            )}\r\n                          >\r\n                            <span>\r\n                              {(() => {\r\n                                // Ưu tiên sử dụng meta.header\r\n                                if (column.columnDef.meta?.header) {\r\n                                  return column.columnDef.meta.header;\r\n                                }\r\n\r\n                                // Nếu header là string, sử dụng trực tiếp\r\n                                if (typeof column.columnDef.header === 'string') {\r\n                                  return column.columnDef.header;\r\n                                }\r\n\r\n                                // Tìm kiếm phần tử span trong header\r\n                                const headerElement = document.querySelector(`[data-column-id=\"${column.id}\"] span`);\r\n                                if (headerElement && headerElement.textContent) {\r\n                                  return headerElement.textContent;\r\n                                }\r\n\r\n                                // Fallback: Sử dụng ID\r\n                                return column.id;\r\n                              })()}\r\n                            </span>\r\n                          </button>\r\n                        );\r\n                      })}\r\n                    </div>\r\n                  )}\r\n                </div>\r\n              </div>\r\n            )}\r\n\r\n            {/* Nút Áp dụng và Xóa tất cả */}\r\n            <div className=\"flex items-center justify-between mt-4\">\r\n              <Button\r\n                onClick={handleApply}\r\n                size=\"sm\"\r\n                className=\"flex-1 mr-1\"\r\n              >\r\n                Áp dụng\r\n              </Button>\r\n\r\n              <Button\r\n                onClick={handleClearAll}\r\n                variant=\"link\"\r\n                size=\"sm\"\r\n                className=\"flex-1 ml-1 text-destructive\"\r\n                disabled={tempSorting.length === 0}\r\n              >\r\n                Xóa tất cả\r\n              </Button>\r\n            </div>\r\n          </div>\r\n        </PopoverContent>\r\n      </Popover>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAKA;AAAA;AAAA;AAAA;AAAA;AAAA;AASA;AAEA;AAAA;AACA;AAEA;AAUA;AASA;AAOA;AAhDA;;;;;;;;;;;;AAmEA,SAAS,aAAa,EACpB,EAAE,EACF,UAAU,EACV,SAAS,EACT,iBAAiB,EACjB,QAAQ,EACU;IAClB,MAAM,EACJ,UAAU,EACV,SAAS,EACT,UAAU,EACV,SAAS,EACT,UAAU,EACX,GAAG,CAAA,GAAA,gRAAA,CAAA,cAAW,AAAD,EAAE;QAAE;IAAG;IAErB,MAAM,QAAQ;QACZ,WAAW,iQAAA,CAAA,MAAG,CAAC,SAAS,CAAC,QAAQ,CAAC;QAClC;IACF;IAEA,qBACE,uVAAC;QACC,KAAK;QACL,OAAO;QACP,WAAU;;0BAEV,uVAAC;gBAAI,WAAU;;kCACb,uVAAC;wBACE,GAAG,UAAU;wBACb,GAAG,SAAS;wBACb,WAAU;kCAEV,cAAA,uVAAC,0SAAA,CAAA,eAAY;4BAAC,WAAU;;;;;;;;;;;kCAE1B,uVAAC;wBAAK,WAAU;kCAAW;;;;;;;;;;;;0BAE7B,uVAAC;gBAAI,WAAU;;kCACb,uVAAC,2HAAA,CAAA,SAAM;wBACL,SAAQ;wBACR,MAAK;wBACL,WAAU;wBACV,SAAS,IAAM,kBAAkB,cAAc,QAAQ,SAAS;kCAE/D,cAAc,sBACb,uVAAC,gUAAA,CAAA,sBAAmB;4BAAC,WAAU;;;;;iDAE/B,uVAAC,4TAAA,CAAA,oBAAiB;4BAAC,WAAU;;;;;;;;;;;kCAGjC,uVAAC,2HAAA,CAAA,SAAM;wBACL,SAAQ;wBACR,MAAK;wBACL,WAAU;wBACV,SAAS;kCAET,cAAA,uVAAC,gRAAA,CAAA,IAAC;4BAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;AAKvB;AAEO,SAAS,aAAoB,EAClC,KAAK,EACL,SAAS,EACT,eAAe,EACf,mBAAmB,WAAW,EAC9B,gBAAgB,CAAC,EACQ;IACzB,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,8SAAA,CAAA,WAAQ,AAAD,EAAE;IACjC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,8SAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,8SAAA,CAAA,WAAQ,AAAD,EAAgB,EAAE;IAE/D,oCAAoC;IACpC,MAAM,kBAAkB,CAAA,GAAA,8SAAA,CAAA,UAAO,AAAD,EAAE;QAC9B,OAAO,MAAM,aAAa,GAAG,MAAM,CAAC,CAAA,SAClC,OAAO,UAAU,MAAM,OAAO,YAAY;IAE9C,GAAG;QAAC;KAAM;IAEV,wCAAwC;IACxC,MAAM,kBAAkB,CAAA,GAAA,8SAAA,CAAA,UAAO,AAAD,EAAE;QAC9B,IAAI,CAAC,YAAY,IAAI,IAAI;YACvB,OAAO;QACT;QAEA,MAAM,QAAQ,YAAY,WAAW,GAAG,IAAI;QAC5C,OAAO,gBAAgB,MAAM,CAAC,CAAA;YAC5B,mCAAmC;YACnC,IAAI,SAAS,OAAO,EAAE;YAEtB,8BAA8B;YAC9B,IAAI,OAAO,SAAS,CAAC,IAAI,EAAE,QAAQ;gBACjC,SAAS,OAAO,SAAS,CAAC,IAAI,CAAC,MAAM;YACvC,OAEK,IAAI,OAAO,OAAO,SAAS,CAAC,MAAM,KAAK,UAAU;gBACpD,SAAS,OAAO,SAAS,CAAC,MAAM;YAClC,OAEK,IAAI,OAAO,OAAO,SAAS,CAAC,MAAM,KAAK,YAAY;gBACtD,MAAM,gBAAgB,SAAS,aAAa,CAAC,CAAC,iBAAiB,EAAE,OAAO,EAAE,CAAC,OAAO,CAAC;gBACnF,IAAI,iBAAiB,cAAc,WAAW,EAAE;oBAC9C,SAAS,cAAc,WAAW;gBACpC;YACF;YAEA,OAAO,OAAO,QAAQ,GAAG,WAAW,GAAG,QAAQ,CAAC;QAClD;IACF,GAAG;QAAC;QAAiB;KAAY;IAEjC,wEAAwE;IACxE,CAAA,GAAA,8SAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,MAAM;YACR,eAAe,MAAM,QAAQ,GAAG,OAAO;QACzC;IACF,GAAG;QAAC;QAAM;KAAM;IAEhB,iEAAiE;IACjE,MAAM,gBAAgB,CAAA,GAAA,8SAAA,CAAA,UAAO,AAAD,EAAE;QAC5B,OAAO,YAAY,GAAG,CAAC,CAAA;YACrB,MAAM,SAAS,MAAM,SAAS,CAAC,KAAK,EAAE;YAEtC,mCAAmC;YACnC,IAAI,aAAa,KAAK,EAAE;YAExB,IAAI,QAAQ;gBACV,8BAA8B;gBAC9B,IAAI,OAAO,SAAS,CAAC,IAAI,EAAE,QAAQ;oBACjC,aAAa,OAAO,SAAS,CAAC,IAAI,CAAC,MAAM;gBAC3C,OAEK,IAAI,OAAO,OAAO,SAAS,CAAC,MAAM,KAAK,UAAU;oBACpD,aAAa,OAAO,SAAS,CAAC,MAAM;gBACtC,OAEK,IAAI,OAAO,OAAO,SAAS,CAAC,MAAM,KAAK,YAAY;oBACtD,MAAM,gBAAgB,SAAS,aAAa,CAAC,CAAC,iBAAiB,EAAE,KAAK,EAAE,CAAC,OAAO,CAAC;oBACjF,IAAI,iBAAiB,cAAc,WAAW,EAAE;wBAC9C,aAAa,cAAc,WAAW;oBACxC;gBACF;YACF;YAEA,OAAO;gBACL,IAAI,KAAK,EAAE;gBACX,MAAM;gBACN,WAAY,KAAK,IAAI,GAAG,SAAS;YACnC;QACF;IACF,GAAG;QAAC;QAAa;KAAM;IAEvB,kBAAkB;IAClB,MAAM,UAAU,CAAA,GAAA,wQAAA,CAAA,aAAU,AAAD,EACvB,CAAA,GAAA,wQAAA,CAAA,YAAS,AAAD,EAAE,wQAAA,CAAA,gBAAa,GACvB,CAAA,GAAA,wQAAA,CAAA,YAAS,AAAD,EAAE,wQAAA,CAAA,iBAAc,EAAE;QACxB,kBAAkB,gRAAA,CAAA,8BAA2B;IAC/C;IAGF,6BAA6B;IAC7B,MAAM,gBAAgB,CAAC;QACrB,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,GAAG;QAEzB,IAAI,QAAQ,OAAO,EAAE,KAAK,KAAK,EAAE,EAAE;YACjC,MAAM,WAAW,cAAc,SAAS,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK,OAAO,EAAE;YACtE,MAAM,WAAW,cAAc,SAAS,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK,KAAK,EAAE;YAEpE,MAAM,mBAAmB,CAAA,GAAA,gRAAA,CAAA,YAAS,AAAD,EAAE,eAAe,UAAU;YAE5D,uBAAuB;YACvB,MAAM,aAAa,iBAAiB,GAAG,CAAC,CAAA,MAAO,CAAC;oBAC9C,IAAI,IAAI,EAAE;oBACV,MAAM,IAAI,SAAS,KAAK;gBAC1B,CAAC;YAED,eAAe;QACjB;IACF;IAEA,kCAAkC;IAClC,MAAM,gBAAgB,CAAC;QACrB,wCAAwC;QACxC,MAAM,eAAe,YAAY,IAAI,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK;QAE1D,IAAI,cAAc;YAChB,oCAAoC;YACpC,MAAM,aAAa,YAAY,GAAG,CAAC,CAAA;gBACjC,IAAI,KAAK,EAAE,KAAK,UAAU;oBACxB,OAAO;wBAAE,GAAG,IAAI;wBAAE,MAAM,CAAC,KAAK,IAAI;oBAAC;gBACrC;gBACA,OAAO;YACT;YAEA,eAAe;QACjB,OAAO,IAAI,YAAY,MAAM,GAAG,eAAe;YAC7C,6CAA6C;YAC7C,MAAM,aAAa;mBACd;gBACH;oBAAE,IAAI;oBAAU,MAAM;gBAAM;aAC7B;YAED,eAAe;QACjB;QAEA,oCAAoC;QACpC,eAAe;IACjB;IAEA,+BAA+B;IAC/B,MAAM,wBAAwB,CAAC,UAAkB;QAC/C,MAAM,aAAa,YAAY,GAAG,CAAC,CAAA;YACjC,IAAI,KAAK,EAAE,KAAK,UAAU;gBACxB,OAAO;oBAAE,GAAG,IAAI;oBAAE,MAAM,cAAc;gBAAO;YAC/C;YACA,OAAO;QACT;QAEA,eAAe;IACjB;IAEA,6BAA6B;IAC7B,MAAM,mBAAmB,CAAC;QACxB,MAAM,aAAa,YAAY,MAAM,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK;QAC1D,eAAe;IACjB;IAEA,oCAAoC;IACpC,MAAM,iBAAiB;QACrB,eAAe,EAAE;IACnB;IAEA,6BAA6B;IAC7B,MAAM,cAAc;QAClB,MAAM,UAAU,CAAC;QACjB,QAAQ;IACV;IAEA,qBAAqB;IACrB,MAAM,eAAe;QACnB,eAAe,MAAM,QAAQ,GAAG,OAAO;QACvC,QAAQ;IACV;IAEA,8BAA8B;IAC9B,MAAM,cAAc;QAClB,MAAM,iBAAiB,MAAM,QAAQ,GAAG,OAAO;QAE/C,IAAI,eAAe,MAAM,KAAK,GAAG;YAC/B,qBAAO,uVAAC,4SAAA,CAAA,cAAW;gBAAC,WAAU;;;;;;QAChC;QAEA,IAAI,eAAe,MAAM,KAAK,GAAG;YAC/B,OAAO,cAAc,CAAC,EAAE,CAAC,IAAI,iBAC3B,uVAAC,4TAAA,CAAA,oBAAiB;gBAAC,WAAU;;;;;qCAC7B,uVAAC,gUAAA,CAAA,sBAAmB;gBAAC,WAAU;;;;;;QACnC;QAEA,mDAAmD;QACnD,qBACE,uVAAC;YAAI,WAAU;;8BACb,uVAAC,4SAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;8BACvB,uVAAC,0HAAA,CAAA,QAAK;oBACJ,WAAU;oBACV,SAAQ;8BAEP,eAAe,MAAM;;;;;;;;;;;;IAI9B;IAEA,qBACE,uVAAC;QAAI,WAAW;kBACd,cAAA,uVAAC,4HAAA,CAAA,UAAO;YAAC,MAAM;YAAM,cAAc;;8BACjC,uVAAC,4HAAA,CAAA,iBAAc;oBAAC,OAAO;8BACrB,cAAA,uVAAC,2HAAA,CAAA,SAAM;wBACL,SAAQ;wBACR,MAAK;wBACL,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EAAE,YAAY,iBAAiB,MAAM,QAAQ,GAAG,OAAO,CAAC,MAAM,GAAG,KAAK;kCAEjF;;;;;;;;;;;8BAGL,uVAAC,4HAAA,CAAA,iBAAc;oBAAC,OAAM;oBAAM,WAAW;oBAAkB,iBAAiB;oBAAc,mBAAmB,CAAC,IAAM,EAAE,cAAc;8BAChI,cAAA,uVAAC;wBAAI,WAAU;;0CACb,uVAAC;gCAAG,WAAU;0CAA2B;;;;;;4BAGxC,cAAc,MAAM,GAAG,kBACtB,uVAAC;gCAAI,WAAU;0CACb,cAAA,uVAAC,wQAAA,CAAA,aAAU;oCACT,SAAS;oCACT,oBAAoB,wQAAA,CAAA,gBAAa;oCACjC,WAAW;8CAEX,cAAA,uVAAC,gRAAA,CAAA,kBAAe;wCACd,OAAO,cAAc,GAAG,CAAC,CAAA,MAAO,IAAI,EAAE;wCACtC,UAAU,gRAAA,CAAA,8BAA2B;kDAEpC,cAAc,GAAG,CAAC,CAAC,uBAClB,uVAAC;gDAEC,IAAI,OAAO,EAAE;gDACb,YAAY,OAAO,IAAI;gDACvB,WAAW,OAAO,SAAS;gDAC3B,mBAAmB,CAAC,YAClB,sBAAsB,OAAO,EAAE,EAAE;gDAEnC,UAAU,IAAM,iBAAiB,OAAO,EAAE;+CAPrC,OAAO,EAAE;;;;;;;;;;;;;;;;;;;qDAcxB,uVAAC;gCAAI,WAAU;0CAAsD;;;;;;4BAMtE,YAAY,MAAM,GAAG,+BACpB,uVAAC;gCAAI,WAAU;;kDACb,uVAAC;wCAAI,WAAU;kDAAiD;;;;;;kDAGhE,uVAAC;wCAAI,WAAU;;0DACb,uVAAC,0RAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;0DAClB,uVAAC,0HAAA,CAAA,QAAK;gDACJ,aAAY;gDACZ,OAAO;gDACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;gDAC9C,WAAU;;;;;;;;;;;;kDAKd,uVAAC;wCAAI,WAAU;kDACZ,gBAAgB,MAAM,KAAK,kBAC1B,uVAAC;4CAAI,WAAU;sDAAgD;;;;;iEAI/D,uVAAC;4CAAI,WAAU;sDACZ,gBAAgB,GAAG,CAAC,CAAA;gDACnB,MAAM,aAAa,YAAY,IAAI,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK,OAAO,EAAE;gDACjE,qBACE,uVAAC;oDAEC,SAAS,IAAM,cAAc,OAAO,EAAE;oDACtC,UAAU;oDACV,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EACV,qGACA,cAAc;8DAGhB,cAAA,uVAAC;kEACE,CAAC;4DACA,8BAA8B;4DAC9B,IAAI,OAAO,SAAS,CAAC,IAAI,EAAE,QAAQ;gEACjC,OAAO,OAAO,SAAS,CAAC,IAAI,CAAC,MAAM;4DACrC;4DAEA,0CAA0C;4DAC1C,IAAI,OAAO,OAAO,SAAS,CAAC,MAAM,KAAK,UAAU;gEAC/C,OAAO,OAAO,SAAS,CAAC,MAAM;4DAChC;4DAEA,qCAAqC;4DACrC,MAAM,gBAAgB,SAAS,aAAa,CAAC,CAAC,iBAAiB,EAAE,OAAO,EAAE,CAAC,OAAO,CAAC;4DACnF,IAAI,iBAAiB,cAAc,WAAW,EAAE;gEAC9C,OAAO,cAAc,WAAW;4DAClC;4DAEA,uBAAuB;4DACvB,OAAO,OAAO,EAAE;wDAClB,CAAC;;;;;;mDA5BE,OAAO,EAAE;;;;;4CAgCpB;;;;;;;;;;;;;;;;;0CAQV,uVAAC;gCAAI,WAAU;;kDACb,uVAAC,2HAAA,CAAA,SAAM;wCACL,SAAS;wCACT,MAAK;wCACL,WAAU;kDACX;;;;;;kDAID,uVAAC,2HAAA,CAAA,SAAM;wCACL,SAAS;wCACT,SAAQ;wCACR,MAAK;wCACL,WAAU;wCACV,UAAU,YAAY,MAAM,KAAK;kDAClC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASf", "debugId": null}}, {"offset": {"line": 2846, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/bloomgoo/gold-exchange-platform/frontend/components/common/data-table/table-toolbar.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport { ColumnVisibilitySelector } from \"@/components/common/selector/column-visibility-selector\"\r\nimport { SortSelector } from \"@/components/common/selector/sort-selector\"\r\nimport { Button } from \"@/components/ui/button\"\r\nimport { Input } from \"@/components/ui/input\"\r\nimport { cn } from \"@/lib/utils\"\r\nimport { Table } from \"@tanstack/react-table\"\r\nimport {\r\n    RefreshCcw,\r\n    Search\r\n} from \"lucide-react\"\r\n\r\ninterface TableToolbarProps<TData> {\r\n    table: Table<TData>\r\n    globalFilter: string\r\n    setGlobalFilter: (value: string) => void\r\n    onRefresh: () => void\r\n    isRefreshing?: boolean\r\n    searchPlaceholder?: string\r\n\r\n    onDeleteSelected?: () => void\r\n    isShowSelectedRows?: boolean\r\n    onShowSelectedRows?: () => void\r\n    // Slot tùy chỉnh để thêm nội dung vào bên trái của thanh tìm kiếm\r\n    beforeSearchSlot?: React.ReactNode\r\n    // Slot tùy chỉnh để thêm nội dung vào bên phải của thanh tìm kiếm\r\n    afterSearchSlot?: React.ReactNode\r\n    // Slot tùy chỉnh để thêm nội dung vào bên trái của các nút hành động\r\n    beforeActionsSlot?: React.ReactNode\r\n    // Slot tùy chỉnh để thêm nội dung vào bên phải của các nút hành động\r\n    afterActionsSlot?: React.ReactNode\r\n    // Tùy chỉnh class cho thanh công cụ\r\n    className?: string\r\n}\r\n\r\nexport function TableToolbar<TData>({\r\n    table,\r\n    globalFilter,\r\n    setGlobalFilter,\r\n    onRefresh,\r\n    isRefreshing = false,\r\n    searchPlaceholder = \"Tìm kiếm...\",\r\n\r\n    onDeleteSelected,\r\n    isShowSelectedRows,\r\n    onShowSelectedRows,\r\n    beforeSearchSlot,\r\n    afterSearchSlot,\r\n    beforeActionsSlot,\r\n    afterActionsSlot,\r\n    className,\r\n}: TableToolbarProps<TData>) {\r\n    const selectedRows = table.getSelectedRowModel().rows\r\n\r\n    return (\r\n        <div className={cn(\"flex items-center justify-between gap-2 px-4 py-2 border-b\", className)}>\r\n            {/* Left Side */}\r\n            <div className=\"flex items-center space-x-4 flex-1\">\r\n                {/* Slot trước thanh tìm kiếm */}\r\n                {beforeSearchSlot}\r\n\r\n                <div className=\"relative w-64\">\r\n                    <Search\r\n                        className=\"h-4 w-4 absolute left-2 top-1/2 transform -translate-y-1/2 text-muted-foreground\"\r\n                    />\r\n                    <Input\r\n                        placeholder={searchPlaceholder}\r\n                        className=\"pl-8 h-9\"\r\n                        value={globalFilter}\r\n                        onChange={(e) => setGlobalFilter(e.target.value)}\r\n                    />\r\n                </div>\r\n\r\n                {/* Slot sau thanh tìm kiếm */}\r\n                {afterSearchSlot}\r\n            </div>\r\n\r\n            {/* Right Side */}\r\n            <div className=\"flex items-center space-x-2\">\r\n                {/* Slot trước các nút hành động */}\r\n                {beforeActionsSlot}\r\n                {onDeleteSelected && (<></>)}\r\n\r\n                {/* <FilterSelector table={table} /> */}\r\n\r\n                <SortSelector table={table} />\r\n\r\n                <ColumnVisibilitySelector table={table} />\r\n\r\n                <Button\r\n                    variant=\"ghost\"\r\n                    size=\"icon\"\r\n                    onClick={onRefresh}\r\n                    disabled={isRefreshing}\r\n                >\r\n                    <RefreshCcw className={cn(\"h-4 w-4\", isRefreshing && \"animate-spin\")} />\r\n                </Button>\r\n\r\n                {/* Slot sau các nút hành động */}\r\n                {afterActionsSlot}\r\n            </div>\r\n        </div>\r\n    )\r\n}"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AAAA;AAEA;AAAA;AARA;;;;;;;;AAoCO,SAAS,aAAoB,EAChC,KAAK,EACL,YAAY,EACZ,eAAe,EACf,SAAS,EACT,eAAe,KAAK,EACpB,oBAAoB,aAAa,EAEjC,gBAAgB,EAChB,kBAAkB,EAClB,kBAAkB,EAClB,gBAAgB,EAChB,eAAe,EACf,iBAAiB,EACjB,gBAAgB,EAChB,SAAS,EACc;IACvB,MAAM,eAAe,MAAM,mBAAmB,GAAG,IAAI;IAErD,qBACI,uVAAC;QAAI,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EAAE,8DAA8D;;0BAE7E,uVAAC;gBAAI,WAAU;;oBAEV;kCAED,uVAAC;wBAAI,WAAU;;0CACX,uVAAC,0RAAA,CAAA,SAAM;gCACH,WAAU;;;;;;0CAEd,uVAAC,0HAAA,CAAA,QAAK;gCACF,aAAa;gCACb,WAAU;gCACV,OAAO;gCACP,UAAU,CAAC,IAAM,gBAAgB,EAAE,MAAM,CAAC,KAAK;;;;;;;;;;;;oBAKtD;;;;;;;0BAIL,uVAAC;gBAAI,WAAU;;oBAEV;oBACA,kCAAqB;kCAItB,uVAAC,qJAAA,CAAA,eAAY;wBAAC,OAAO;;;;;;kCAErB,uVAAC,qKAAA,CAAA,2BAAwB;wBAAC,OAAO;;;;;;kCAEjC,uVAAC,2HAAA,CAAA,SAAM;wBACH,SAAQ;wBACR,MAAK;wBACL,SAAS;wBACT,UAAU;kCAEV,cAAA,uVAAC,sSAAA,CAAA,aAAU;4BAAC,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EAAE,WAAW,gBAAgB;;;;;;;;;;;oBAIxD;;;;;;;;;;;;;AAIjB", "debugId": null}}, {"offset": {"line": 2964, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/bloomgoo/gold-exchange-platform/frontend/components/common/selector/page-selector.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { Button } from '@/components/ui/button';\r\nimport {\r\n   Command,\r\n   CommandEmpty,\r\n   CommandGroup,\r\n   CommandInput,\r\n   CommandItem,\r\n   CommandList,\r\n} from '@/components/ui/command';\r\nimport { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';\r\nimport { CheckIcon, ChevronLeft, ChevronRight, ChevronsLeft, ChevronsRight } from 'lucide-react';\r\nimport { useId, useState, useEffect } from 'react';\r\nimport { Table as TableType } from \"@tanstack/react-table\";\r\n\r\n// Interface for table-based pagination\r\ninterface TablePaginationProps {\r\n   table: TableType<any>;\r\n   type: 'table';\r\n}\r\n\r\n// Interface for custom pagination\r\ninterface CustomPaginationProps {\r\n   type: 'custom';\r\n   currentPage: number;\r\n   totalPages: number;\r\n   onPageChange: (page: number) => void;\r\n}\r\n\r\n// Combined props type\r\ntype PageSelectorProps = {\r\n   maxPagesToShow?: number;\r\n   showFirstLastButtons?: boolean;\r\n   showPrevNextButtons?: boolean;\r\n   className?: string;\r\n} & (TablePaginationProps | CustomPaginationProps);\r\n\r\nexport function PageSelector(props: PageSelectorProps) {\r\n   const id = useId();\r\n   const [open, setOpen] = useState<boolean>(false);\r\n   \r\n   // Determine current page and total pages based on props type\r\n   const currentPage = props.type === 'table' \r\n      ? props.table.getState().pagination.pageIndex + 1 \r\n      : props.currentPage;\r\n   \r\n   const totalPages = props.type === 'table'\r\n      ? props.table.getPageCount()\r\n      : props.totalPages;\r\n   \r\n   // Default to showing 5 pages in the selector\r\n   const maxPagesToShow = props.maxPagesToShow || 5;\r\n   \r\n   // Generate array of page numbers to display\r\n   const getPageNumbers = () => {\r\n      if (totalPages <= maxPagesToShow) {\r\n         // If total pages is less than max to show, display all pages\r\n         return Array.from({ length: totalPages }, (_, i) => i + 1);\r\n      }\r\n      \r\n      // Calculate start and end page numbers\r\n      let startPage = Math.max(1, currentPage - Math.floor(maxPagesToShow / 2));\r\n      let endPage = startPage + maxPagesToShow - 1;\r\n      \r\n      // Adjust if end page exceeds total pages\r\n      if (endPage > totalPages) {\r\n         endPage = totalPages;\r\n         startPage = Math.max(1, endPage - maxPagesToShow + 1);\r\n      }\r\n      \r\n      return Array.from({ length: endPage - startPage + 1 }, (_, i) => startPage + i);\r\n   };\r\n   \r\n   const pageNumbers = getPageNumbers();\r\n   \r\n   // Handle page change\r\n   const handlePageChange = (page: number) => {\r\n      if (props.type === 'table') {\r\n         props.table.setPageIndex(page - 1);\r\n      } else {\r\n         props.onPageChange(page);\r\n      }\r\n      setOpen(false);\r\n   };\r\n   \r\n   // Handle navigation to first page\r\n   const goToFirstPage = () => {\r\n      if (props.type === 'table') {\r\n         props.table.setPageIndex(0);\r\n      } else {\r\n         props.onPageChange(1);\r\n      }\r\n   };\r\n   \r\n   // Handle navigation to previous page\r\n   const goToPreviousPage = () => {\r\n      if (props.type === 'table') {\r\n         props.table.previousPage();\r\n      } else {\r\n         props.onPageChange(Math.max(1, currentPage - 1));\r\n      }\r\n   };\r\n   \r\n   // Handle navigation to next page\r\n   const goToNextPage = () => {\r\n      if (props.type === 'table') {\r\n         props.table.nextPage();\r\n      } else {\r\n         props.onPageChange(Math.min(totalPages, currentPage + 1));\r\n      }\r\n   };\r\n   \r\n   // Handle navigation to last page\r\n   const goToLastPage = () => {\r\n      if (props.type === 'table') {\r\n         props.table.setPageIndex(totalPages - 1);\r\n      } else {\r\n         props.onPageChange(totalPages);\r\n      }\r\n   };\r\n   \r\n   // Check if can go to previous or next page\r\n   const canPreviousPage = props.type === 'table' \r\n      ? props.table.getCanPreviousPage()\r\n      : currentPage > 1;\r\n      \r\n   const canNextPage = props.type === 'table'\r\n      ? props.table.getCanNextPage()\r\n      : currentPage < totalPages;\r\n\r\n   return (\r\n      <div className=\"flex items-center space-x-2\">\r\n         {/* First Page Button */}\r\n         {props.showFirstLastButtons && (\r\n            <Button\r\n               variant=\"outline\"\r\n               className=\"hidden h-8 w-8 p-0 lg:flex\"\r\n               onClick={goToFirstPage}\r\n               disabled={!canPreviousPage}\r\n            >\r\n               <ChevronsLeft className=\"size-4\" />\r\n            </Button>\r\n         )}\r\n         \r\n         {/* Previous Page Button */}\r\n         {props.showPrevNextButtons && (\r\n            <Button\r\n               variant=\"outline\"\r\n               className=\"h-8 w-8 p-0\"\r\n               onClick={goToPreviousPage}\r\n               disabled={!canPreviousPage}\r\n            >\r\n               <ChevronLeft className=\"size-4\" />\r\n            </Button>\r\n         )}\r\n         \r\n         {/* Page Selector */}\r\n         <Popover open={open} onOpenChange={setOpen}>\r\n            <PopoverTrigger asChild>\r\n               <Button\r\n                  id={id}\r\n                  className=\"flex items-center justify-center h-8 px-3\"\r\n                  size=\"sm\"\r\n                  variant=\"outline\"\r\n                  role=\"combobox\"\r\n                  aria-expanded={open}\r\n               >\r\n                  <span className=\"text-sm font-medium\">\r\n                     {currentPage} / {totalPages}\r\n                  </span>\r\n               </Button>\r\n            </PopoverTrigger>\r\n            <PopoverContent className=\"border-input w-48 p-0\" align=\"center\" side=\"top\">\r\n               <Command>\r\n                  <CommandInput placeholder=\"Trang...\" />\r\n                  <CommandList>\r\n                     <CommandEmpty>Không tìm thấy trang.</CommandEmpty>\r\n                     <CommandGroup>\r\n                        {pageNumbers.map((page) => (\r\n                           <CommandItem\r\n                              key={page}\r\n                              value={page.toString()}\r\n                              onSelect={() => handlePageChange(page)}\r\n                              className=\"flex items-center justify-between\"\r\n                           >\r\n                              <div className=\"flex items-center gap-2\">\r\n                                 <span className=\"text-xs\">Trang {page}</span>\r\n                              </div>\r\n                              {currentPage === page && <CheckIcon size={14} className=\"ml-auto\" />}\r\n                           </CommandItem>\r\n                        ))}\r\n                     </CommandGroup>\r\n                  </CommandList>\r\n               </Command>\r\n            </PopoverContent>\r\n         </Popover>\r\n         \r\n         {/* Next Page Button */}\r\n         {props.showPrevNextButtons && (\r\n            <Button\r\n               variant=\"outline\"\r\n               className=\"h-8 w-8 p-0\"\r\n               onClick={goToNextPage}\r\n               disabled={!canNextPage}\r\n            >\r\n               <ChevronRight className=\"size-4\" />\r\n            </Button>\r\n         )}\r\n         \r\n         {/* Last Page Button */}\r\n         {props.showFirstLastButtons && (\r\n            <Button\r\n               variant=\"outline\"\r\n               className=\"hidden h-8 w-8 p-0 lg:flex\"\r\n               onClick={goToLastPage}\r\n               disabled={!canNextPage}\r\n            >\r\n               <ChevronsRight className=\"size-4\" />\r\n            </Button>\r\n         )}\r\n      </div>\r\n   );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAQA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AAbA;;;;;;;AAsCO,SAAS,aAAa,KAAwB;IAClD,MAAM,KAAK,CAAA,GAAA,8SAAA,CAAA,QAAK,AAAD;IACf,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,8SAAA,CAAA,WAAQ,AAAD,EAAW;IAE1C,6DAA6D;IAC7D,MAAM,cAAc,MAAM,IAAI,KAAK,UAC9B,MAAM,KAAK,CAAC,QAAQ,GAAG,UAAU,CAAC,SAAS,GAAG,IAC9C,MAAM,WAAW;IAEtB,MAAM,aAAa,MAAM,IAAI,KAAK,UAC7B,MAAM,KAAK,CAAC,YAAY,KACxB,MAAM,UAAU;IAErB,6CAA6C;IAC7C,MAAM,iBAAiB,MAAM,cAAc,IAAI;IAE/C,4CAA4C;IAC5C,MAAM,iBAAiB;QACpB,IAAI,cAAc,gBAAgB;YAC/B,6DAA6D;YAC7D,OAAO,MAAM,IAAI,CAAC;gBAAE,QAAQ;YAAW,GAAG,CAAC,GAAG,IAAM,IAAI;QAC3D;QAEA,uCAAuC;QACvC,IAAI,YAAY,KAAK,GAAG,CAAC,GAAG,cAAc,KAAK,KAAK,CAAC,iBAAiB;QACtE,IAAI,UAAU,YAAY,iBAAiB;QAE3C,yCAAyC;QACzC,IAAI,UAAU,YAAY;YACvB,UAAU;YACV,YAAY,KAAK,GAAG,CAAC,GAAG,UAAU,iBAAiB;QACtD;QAEA,OAAO,MAAM,IAAI,CAAC;YAAE,QAAQ,UAAU,YAAY;QAAE,GAAG,CAAC,GAAG,IAAM,YAAY;IAChF;IAEA,MAAM,cAAc;IAEpB,qBAAqB;IACrB,MAAM,mBAAmB,CAAC;QACvB,IAAI,MAAM,IAAI,KAAK,SAAS;YACzB,MAAM,KAAK,CAAC,YAAY,CAAC,OAAO;QACnC,OAAO;YACJ,MAAM,YAAY,CAAC;QACtB;QACA,QAAQ;IACX;IAEA,kCAAkC;IAClC,MAAM,gBAAgB;QACnB,IAAI,MAAM,IAAI,KAAK,SAAS;YACzB,MAAM,KAAK,CAAC,YAAY,CAAC;QAC5B,OAAO;YACJ,MAAM,YAAY,CAAC;QACtB;IACH;IAEA,qCAAqC;IACrC,MAAM,mBAAmB;QACtB,IAAI,MAAM,IAAI,KAAK,SAAS;YACzB,MAAM,KAAK,CAAC,YAAY;QAC3B,OAAO;YACJ,MAAM,YAAY,CAAC,KAAK,GAAG,CAAC,GAAG,cAAc;QAChD;IACH;IAEA,iCAAiC;IACjC,MAAM,eAAe;QAClB,IAAI,MAAM,IAAI,KAAK,SAAS;YACzB,MAAM,KAAK,CAAC,QAAQ;QACvB,OAAO;YACJ,MAAM,YAAY,CAAC,KAAK,GAAG,CAAC,YAAY,cAAc;QACzD;IACH;IAEA,iCAAiC;IACjC,MAAM,eAAe;QAClB,IAAI,MAAM,IAAI,KAAK,SAAS;YACzB,MAAM,KAAK,CAAC,YAAY,CAAC,aAAa;QACzC,OAAO;YACJ,MAAM,YAAY,CAAC;QACtB;IACH;IAEA,2CAA2C;IAC3C,MAAM,kBAAkB,MAAM,IAAI,KAAK,UAClC,MAAM,KAAK,CAAC,kBAAkB,KAC9B,cAAc;IAEnB,MAAM,cAAc,MAAM,IAAI,KAAK,UAC9B,MAAM,KAAK,CAAC,cAAc,KAC1B,cAAc;IAEnB,qBACG,uVAAC;QAAI,WAAU;;YAEX,MAAM,oBAAoB,kBACxB,uVAAC,2HAAA,CAAA,SAAM;gBACJ,SAAQ;gBACR,WAAU;gBACV,SAAS;gBACT,UAAU,CAAC;0BAEX,cAAA,uVAAC,0SAAA,CAAA,eAAY;oBAAC,WAAU;;;;;;;;;;;YAK7B,MAAM,mBAAmB,kBACvB,uVAAC,2HAAA,CAAA,SAAM;gBACJ,SAAQ;gBACR,WAAU;gBACV,SAAS;gBACT,UAAU,CAAC;0BAEX,cAAA,uVAAC,wSAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;;;;;;0BAK7B,uVAAC,4HAAA,CAAA,UAAO;gBAAC,MAAM;gBAAM,cAAc;;kCAChC,uVAAC,4HAAA,CAAA,iBAAc;wBAAC,OAAO;kCACpB,cAAA,uVAAC,2HAAA,CAAA,SAAM;4BACJ,IAAI;4BACJ,WAAU;4BACV,MAAK;4BACL,SAAQ;4BACR,MAAK;4BACL,iBAAe;sCAEf,cAAA,uVAAC;gCAAK,WAAU;;oCACZ;oCAAY;oCAAI;;;;;;;;;;;;;;;;;kCAI1B,uVAAC,4HAAA,CAAA,iBAAc;wBAAC,WAAU;wBAAwB,OAAM;wBAAS,MAAK;kCACnE,cAAA,uVAAC,4HAAA,CAAA,UAAO;;8CACL,uVAAC,4HAAA,CAAA,eAAY;oCAAC,aAAY;;;;;;8CAC1B,uVAAC,4HAAA,CAAA,cAAW;;sDACT,uVAAC,4HAAA,CAAA,eAAY;sDAAC;;;;;;sDACd,uVAAC,4HAAA,CAAA,eAAY;sDACT,YAAY,GAAG,CAAC,CAAC,qBACf,uVAAC,4HAAA,CAAA,cAAW;oDAET,OAAO,KAAK,QAAQ;oDACpB,UAAU,IAAM,iBAAiB;oDACjC,WAAU;;sEAEV,uVAAC;4DAAI,WAAU;sEACZ,cAAA,uVAAC;gEAAK,WAAU;;oEAAU;oEAAO;;;;;;;;;;;;wDAEnC,gBAAgB,sBAAQ,uVAAC,4RAAA,CAAA,YAAS;4DAAC,MAAM;4DAAI,WAAU;;;;;;;mDARnD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAkBzB,MAAM,mBAAmB,kBACvB,uVAAC,2HAAA,CAAA,SAAM;gBACJ,SAAQ;gBACR,WAAU;gBACV,SAAS;gBACT,UAAU,CAAC;0BAEX,cAAA,uVAAC,0SAAA,CAAA,eAAY;oBAAC,WAAU;;;;;;;;;;;YAK7B,MAAM,oBAAoB,kBACxB,uVAAC,2HAAA,CAAA,SAAM;gBACJ,SAAQ;gBACR,WAAU;gBACV,SAAS;gBACT,UAAU,CAAC;0BAEX,cAAA,uVAAC,4SAAA,CAAA,gBAAa;oBAAC,WAAU;;;;;;;;;;;;;;;;;AAKxC", "debugId": null}}, {"offset": {"line": 3264, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/bloomgoo/gold-exchange-platform/frontend/components/common/selector/page-size-selector.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { Button } from '@/components/ui/button';\r\nimport {\r\n   Command,\r\n   CommandEmpty,\r\n   CommandGroup,\r\n   CommandInput,\r\n   CommandItem,\r\n   CommandList,\r\n} from '@/components/ui/command';\r\nimport { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';\r\nimport { CheckIcon } from 'lucide-react';\r\nimport { useId, useState } from 'react';\r\nimport { Table as TableType } from \"@tanstack/react-table\";\r\n\r\n// Interface for table-based pagination\r\ninterface TablePageSizeProps {\r\n   table: TableType<any>;\r\n   type: 'table';\r\n}\r\n\r\n// Interface for custom pagination\r\ninterface CustomPageSizeProps {\r\n   type: 'custom';\r\n   pageSize: number;\r\n   onPageSizeChange: (pageSize: number) => void;\r\n}\r\n\r\n// Combined props type\r\ntype PageSizeSelectorProps = {\r\n   pageSizeOptions?: number[];\r\n   className?: string;\r\n   label?: string;\r\n   triggerClassName?: string;\r\n} & (TablePageSizeProps | CustomPageSizeProps);\r\n\r\nexport function PageSizeSelector(props: PageSizeSelectorProps) {\r\n   const id = useId();\r\n   const [open, setOpen] = useState<boolean>(false);\r\n   \r\n   // Default page size options if not provided\r\n   const pageSizeOptions = props.pageSizeOptions || [10, 20, 30, 50, 100];\r\n   \r\n   // Get current page size based on props type\r\n   const currentPageSize = props.type === 'table' \r\n      ? props.table.getState().pagination.pageSize \r\n      : props.pageSize;\r\n   \r\n   // Handle page size change\r\n   const handlePageSizeChange = (pageSize: number) => {\r\n      if (props.type === 'table') {\r\n         props.table.setPageSize(pageSize);\r\n      } else {\r\n         props.onPageSizeChange(pageSize);\r\n      }\r\n      setOpen(false);\r\n   };\r\n\r\n   return (\r\n      <div className=\"flex items-center space-x-2\">\r\n         {props.label && (\r\n            <span className=\"text-sm text-muted-foreground\">{props.label}</span>\r\n         )}\r\n         \r\n         <Popover open={open} onOpenChange={setOpen}>\r\n            <PopoverTrigger asChild>\r\n               <Button\r\n                  id={id}\r\n                  className={`flex items-center justify-center h-8 px-3 ${props.triggerClassName || ''}`}\r\n                  size=\"sm\"\r\n                  variant=\"outline\"\r\n                  role=\"combobox\"\r\n                  aria-expanded={open}\r\n               >\r\n                  <span className=\"text-sm font-medium\">\r\n                     {currentPageSize} bản ghi\r\n                  </span>\r\n               </Button>\r\n            </PopoverTrigger>\r\n            <PopoverContent className=\"border-input w-48 p-0\" align=\"start\" side=\"top\">\r\n               <Command>\r\n                  <CommandInput placeholder=\"Số bản ghi...\" />\r\n                  <CommandList>\r\n                     <CommandEmpty>Không tìm thấy tùy chọn.</CommandEmpty>\r\n                     <CommandGroup>\r\n                        {pageSizeOptions.map((size) => (\r\n                           <CommandItem\r\n                              key={size}\r\n                              value={size.toString()}\r\n                              onSelect={() => handlePageSizeChange(size)}\r\n                              className=\"flex items-center justify-between\"\r\n                           >\r\n                              <div className=\"flex items-center gap-2\">\r\n                                 <span className=\"text-xs\">{size} bản ghi</span>\r\n                              </div>\r\n                              {currentPageSize === size && <CheckIcon size={14} className=\"ml-auto\" />}\r\n                           </CommandItem>\r\n                        ))}\r\n                     </CommandGroup>\r\n                  </CommandList>\r\n               </Command>\r\n            </PopoverContent>\r\n         </Popover>\r\n      </div>\r\n   );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAQA;AACA;AACA;AAbA;;;;;;;AAqCO,SAAS,iBAAiB,KAA4B;IAC1D,MAAM,KAAK,CAAA,GAAA,8SAAA,CAAA,QAAK,AAAD;IACf,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,8SAAA,CAAA,WAAQ,AAAD,EAAW;IAE1C,4CAA4C;IAC5C,MAAM,kBAAkB,MAAM,eAAe,IAAI;QAAC;QAAI;QAAI;QAAI;QAAI;KAAI;IAEtE,4CAA4C;IAC5C,MAAM,kBAAkB,MAAM,IAAI,KAAK,UAClC,MAAM,KAAK,CAAC,QAAQ,GAAG,UAAU,CAAC,QAAQ,GAC1C,MAAM,QAAQ;IAEnB,0BAA0B;IAC1B,MAAM,uBAAuB,CAAC;QAC3B,IAAI,MAAM,IAAI,KAAK,SAAS;YACzB,MAAM,KAAK,CAAC,WAAW,CAAC;QAC3B,OAAO;YACJ,MAAM,gBAAgB,CAAC;QAC1B;QACA,QAAQ;IACX;IAEA,qBACG,uVAAC;QAAI,WAAU;;YACX,MAAM,KAAK,kBACT,uVAAC;gBAAK,WAAU;0BAAiC,MAAM,KAAK;;;;;;0BAG/D,uVAAC,4HAAA,CAAA,UAAO;gBAAC,MAAM;gBAAM,cAAc;;kCAChC,uVAAC,4HAAA,CAAA,iBAAc;wBAAC,OAAO;kCACpB,cAAA,uVAAC,2HAAA,CAAA,SAAM;4BACJ,IAAI;4BACJ,WAAW,CAAC,0CAA0C,EAAE,MAAM,gBAAgB,IAAI,IAAI;4BACtF,MAAK;4BACL,SAAQ;4BACR,MAAK;4BACL,iBAAe;sCAEf,cAAA,uVAAC;gCAAK,WAAU;;oCACZ;oCAAgB;;;;;;;;;;;;;;;;;kCAI1B,uVAAC,4HAAA,CAAA,iBAAc;wBAAC,WAAU;wBAAwB,OAAM;wBAAQ,MAAK;kCAClE,cAAA,uVAAC,4HAAA,CAAA,UAAO;;8CACL,uVAAC,4HAAA,CAAA,eAAY;oCAAC,aAAY;;;;;;8CAC1B,uVAAC,4HAAA,CAAA,cAAW;;sDACT,uVAAC,4HAAA,CAAA,eAAY;sDAAC;;;;;;sDACd,uVAAC,4HAAA,CAAA,eAAY;sDACT,gBAAgB,GAAG,CAAC,CAAC,qBACnB,uVAAC,4HAAA,CAAA,cAAW;oDAET,OAAO,KAAK,QAAQ;oDACpB,UAAU,IAAM,qBAAqB;oDACrC,WAAU;;sEAEV,uVAAC;4DAAI,WAAU;sEACZ,cAAA,uVAAC;gEAAK,WAAU;;oEAAW;oEAAK;;;;;;;;;;;;wDAElC,oBAAoB,sBAAQ,uVAAC,4RAAA,CAAA,YAAS;4DAAC,MAAM;4DAAI,WAAU;;;;;;;mDARvD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAkBnC", "debugId": null}}, {"offset": {"line": 3448, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/bloomgoo/gold-exchange-platform/frontend/components/common/data-table/table-footer.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport { Table as TableType } from \"@tanstack/react-table\"\r\nimport { But<PERSON> } from \"@/components/ui/button\"\r\nimport { ChevronLeft, ChevronRight, ChevronsLeft, ChevronsRight } from \"lucide-react\"\r\nimport { PageSelector } from \"@/components/common/selector/page-selector\"\r\nimport { PageSizeSelector } from \"@/components/common/selector/page-size-selector\"\r\n\r\ninterface TableFooterProps<TData> {\r\n  table: TableType<TData>\r\n  className?: string\r\n  totalItems?: number\r\n  isShowSelectedRows?: boolean\r\n  onShowSelectedRows?: () => void\r\n}\r\n\r\nexport function TableFooter<TData>({\r\n  table,\r\n  className,\r\n  totalItems,\r\n  isShowSelectedRows,\r\n  onShowSelectedRows,\r\n}: TableFooterProps<TData>) {\r\n  // Kiểm tra xem có hàng nào được chọn không\r\n  const selectedRowsCount = table.getFilteredSelectedRowModel().rows.length;\r\n  const hasSelectedRows = selectedRowsCount > 0;\r\n\r\n  return (\r\n    <div className=\"sticky bottom-0 bg-background border-t mt-auto\">\r\n      <div className=\"flex items-center justify-between space-x-2 px-4 py-2\">\r\n        <div className=\"flex items-center space-x-2\">\r\n          <PageSizeSelector\r\n            type=\"table\"\r\n            table={table}\r\n            pageSizeOptions={[20, 50, 100, 200]}\r\n            triggerClassName=\"w-[120px]\"\r\n          />\r\n\r\n          {/* Hiển thị thông tin về hàng đã chọn khi có hàng được chọn */}\r\n          {hasSelectedRows && (\r\n            <p className=\"text-sm text-muted-foreground\">\r\n              {selectedRowsCount} trên{\" \"}\r\n              {table.getFilteredRowModel().rows.length} bản ghi đã chọn.\r\n            </p>\r\n          )}\r\n        </div>\r\n\r\n        <div className=\"flex items-center space-x-6 lg:space-x-8\">\r\n          <div className=\"flex w-[100x] items-center justify-center text-sm font-medium\">\r\n            Hiển thị {table.getRowCount()} trên {totalItems} bản ghi\r\n          </div>\r\n          <PageSelector\r\n            type=\"table\"\r\n            table={table}\r\n            showFirstLastButtons={true}\r\n            showPrevNextButtons={true}\r\n            maxPagesToShow={7}\r\n          />\r\n        </div>\r\n      </div>\r\n    </div>\r\n  )\r\n}"], "names": [], "mappings": ";;;;AAKA;AACA;AANA;;;;AAgBO,SAAS,YAAmB,EACjC,KAAK,EACL,SAAS,EACT,UAAU,EACV,kBAAkB,EAClB,kBAAkB,EACM;IACxB,2CAA2C;IAC3C,MAAM,oBAAoB,MAAM,2BAA2B,GAAG,IAAI,CAAC,MAAM;IACzE,MAAM,kBAAkB,oBAAoB;IAE5C,qBACE,uVAAC;QAAI,WAAU;kBACb,cAAA,uVAAC;YAAI,WAAU;;8BACb,uVAAC;oBAAI,WAAU;;sCACb,uVAAC,6JAAA,CAAA,mBAAgB;4BACf,MAAK;4BACL,OAAO;4BACP,iBAAiB;gCAAC;gCAAI;gCAAI;gCAAK;6BAAI;4BACnC,kBAAiB;;;;;;wBAIlB,iCACC,uVAAC;4BAAE,WAAU;;gCACV;gCAAkB;gCAAM;gCACxB,MAAM,mBAAmB,GAAG,IAAI,CAAC,MAAM;gCAAC;;;;;;;;;;;;;8BAK/C,uVAAC;oBAAI,WAAU;;sCACb,uVAAC;4BAAI,WAAU;;gCAAgE;gCACnE,MAAM,WAAW;gCAAG;gCAAO;gCAAW;;;;;;;sCAElD,uVAAC,qJAAA,CAAA,eAAY;4BACX,MAAK;4BACL,OAAO;4BACP,sBAAsB;4BACtB,qBAAqB;4BACrB,gBAAgB;;;;;;;;;;;;;;;;;;;;;;;AAM5B", "debugId": null}}, {"offset": {"line": 3557, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/bloomgoo/gold-exchange-platform/frontend/components/common/user/order-books/order-book-detail-sheet.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { useState, useEffect } from 'react';\r\nimport { format } from 'date-fns';\r\nimport { vi } from 'date-fns/locale';\r\nimport {\r\n  FileText,\r\n  Calendar,\r\n  DollarSign,\r\n  CreditCard,\r\n  Tag,\r\n  BarChart2,\r\n  Clock,\r\n  User,\r\n  Activity,\r\n  Landmark,\r\n  Home,\r\n  ScrollText,\r\n  Pencil,\r\n  Loader2\r\n} from 'lucide-react';\r\n\r\nimport {\r\n  Sheet,\r\n  SheetContent,\r\n  SheetDescription,\r\n  SheetHeader,\r\n  SheetTitle,\r\n} from '@/components/ui/sheet';\r\n\r\nimport {\r\n  Table,\r\n  TableBody,\r\n  TableCaption,\r\n  TableCell,\r\n  TableHead,\r\n  TableHeader,\r\n  TableRow,\r\n} from \"@/components/ui/table\";\r\n\r\nimport { Badge } from '@/components/ui/badge';\r\nimport { Separator } from '@/components/ui/separator';\r\nimport { Button } from '@/components/ui/button';\r\nimport { OrderBook, OrderStatus, OrderType, OrderBookDetail } from './type/order-books';\r\nimport { api } from '@/lib/api';\r\nimport { IconCurrencyDong, IconExchange } from '@tabler/icons-react';\r\nimport { InfoCard } from '@/components/info-card';\r\nimport { toast } from 'sonner';\r\n\r\n// Define InfoCardItem component directly in this file\r\ninterface InfoCardItemProps {\r\n  label: React.ReactNode;\r\n  value: React.ReactNode;\r\n  className?: string;\r\n}\r\n\r\nfunction InfoCardItem({ label, value, className }: InfoCardItemProps) {\r\n  return (\r\n    <div className={`space-y-1 ${className}`}>\r\n      <div className=\"text-xs font-medium text-muted-foreground\">{label}</div>\r\n      <div className=\"text-md\">{value}</div>\r\n    </div>\r\n  );\r\n}\r\n\r\ninterface OrderBookDetailSheetProps {\r\n  orderBook: OrderBook | null;\r\n  isOpen: boolean;\r\n  onClose: () => void;\r\n  onEdit: (orderBook: OrderBook) => void;\r\n}\r\n\r\nexport function OrderBookDetailSheet({ orderBook: initialOrderBook, isOpen, onClose, onEdit }: OrderBookDetailSheetProps) {\r\n  const [orderBook, setOrderBook] = useState<OrderBook | null>(initialOrderBook);\r\n  const [loading, setLoading] = useState<boolean>(false);\r\n\r\n  // Gọi API để lấy thông tin chi tiết của lệnh khi mở sheet\r\n  useEffect(() => {\r\n    if (isOpen && initialOrderBook?.id) {\r\n      setLoading(true);\r\n      api.get<OrderBook>(`order-books/${initialOrderBook.id}`)\r\n        .then(response => {\r\n          setOrderBook(response);\r\n          setLoading(false);\r\n        })\r\n        .catch(error => {\r\n          console.error('Error fetching order book details:', error);\r\n          toast.error('Không thể tải thông tin chi tiết lệnh');\r\n          setLoading(false);\r\n        });\r\n    }\r\n  }, [isOpen, initialOrderBook?.id]);\r\n\r\n  if (!orderBook) return null;\r\n\r\n  const formatDate = (dateStr: string | null | undefined) => {\r\n    if (!dateStr) return '---';\r\n    try {\r\n      return format(new Date(dateStr), 'dd/MM/yyyy HH:mm', { locale: vi });\r\n    } catch (error) {\r\n      return 'Không hợp lệ';\r\n    }\r\n  };\r\n\r\n  // Format currency with VND\r\n  const formatCurrency = (amount: number | undefined) => {\r\n    if (amount === undefined || amount === null) return \"---\";\r\n    return new Intl.NumberFormat('vi-VN', {\r\n      maximumFractionDigits: 0\r\n    }).format(amount);\r\n  };\r\n\r\n  // Get color for order status badge\r\n  const getStatusColor = (status: OrderStatus) => {\r\n    switch (status) {\r\n      case OrderStatus.DEPOSITED:\r\n        return 'bg-purple-100 text-purple-800 hover:bg-purple-200';\r\n      case OrderStatus.COMPLETED:\r\n        return 'bg-green-100 text-green-800 hover:bg-green-200';\r\n      case OrderStatus.TERMINATED:\r\n        return 'bg-red-100 text-red-800 hover:bg-red-200';\r\n      case OrderStatus.CANCELLED:\r\n        return 'bg-gray-300 text-gray-800 hover:bg-gray-200';\r\n      default:\r\n        return 'bg-gray-100 text-gray-800 hover:bg-gray-200';\r\n    }\r\n  };\r\n\r\n  // Order status in Vietnamese\r\n  const getStatusName = (status: OrderStatus) => {\r\n    switch (status) {\r\n      case OrderStatus.DEPOSITED:\r\n        return 'Đã ký quỹ';\r\n      case OrderStatus.COMPLETED:\r\n        return 'Đã tất toán';\r\n      case OrderStatus.TERMINATED:\r\n        return 'Đã cắt hợp đồng';\r\n      case OrderStatus.CANCELLED:\r\n        return 'Đã hủy';\r\n      default:\r\n        return status;\r\n    }\r\n  };\r\n\r\n  // Order type in Vietnamese\r\n  const getOrderTypeName = (type: OrderType) => {\r\n    switch (type) {\r\n      case OrderType.BUY:\r\n        return 'Mua';\r\n      case OrderType.SELL:\r\n        return 'Bán';\r\n      default:\r\n        return type;\r\n    }\r\n  };\r\n\r\n  // Get color for order type\r\n  const getOrderTypeColor = (type: OrderType) => {\r\n    switch (type) {\r\n      case OrderType.BUY:\r\n        return 'bg-green-100 text-green-800 hover:bg-green-200';\r\n      case OrderType.SELL:\r\n        return 'bg-red-100 text-red-800 hover:bg-red-200';\r\n      default:\r\n        return 'bg-gray-100 text-gray-800 hover:bg-gray-200';\r\n    }\r\n  };\r\n\r\n  // Tính tổng token\r\n  const getTotalTokens = () => {\r\n    return orderBook.details?.length || 0;\r\n  };\r\n\r\n  // Tính tổng khối lượng (số lượng × oz)\r\n  const getTotalVolume = () => {\r\n    if (!orderBook.details || orderBook.details.length === 0) return 0;\r\n\r\n    return orderBook.details.reduce((sum, detail) => {\r\n      const volume = detail.quantity || 0;\r\n      const weight = detail.product?.weight || 1;\r\n      return sum + (volume * weight);\r\n    }, 0);\r\n  };\r\n\r\n  return (\r\n    <Sheet open={isOpen} onOpenChange={(open) => !open && onClose()}>\r\n      <SheetContent className=\"w-full sm:max-w-xl md:max-w-2xl lg:max-w-3xl flex flex-col p-0\">\r\n        {/* Header cố định */}\r\n        <SheetHeader className=\"px-0 py-0 border-b\">\r\n          <div className=\"flex items-center gap-4 px-4 py-2\">\r\n            <div className=\"h-16 w-16 shrink-0 flex items-center justify-center\">\r\n              {loading ? (\r\n                <Loader2 className=\"h-8 w-8 animate-spin text-muted-foreground\" />\r\n              ) : (\r\n                <IconExchange className=\"h-8 w-8 text-primary\" />\r\n              )}\r\n            </div>\r\n            <div className=\"flex-1\">\r\n              <SheetTitle className=\"text-md font-semibold\">\r\n                {loading ? (\r\n                  <div className=\"flex items-center gap-2\">\r\n                    <Loader2 className=\"h-4 w-4 animate-spin\" />\r\n                    <span>Đang tải...</span>\r\n                  </div>\r\n                ) : (\r\n                  `Lệnh #${orderBook.contractNumber || orderBook.id?.substring(0, 8)}`\r\n                )}\r\n              </SheetTitle>\r\n              <SheetDescription>\r\n                {loading ? (\r\n                  <span className=\"flex items-center gap-2\">\r\n                    <Loader2 className=\"h-3 w-3 animate-spin\" />\r\n                    <span>Đang tải thông tin...</span>\r\n                  </span>\r\n                ) : (\r\n                  <span className=\"flex items-center gap-2\">\r\n                    <Badge className={getStatusColor(orderBook.status)}>\r\n                      {getStatusName(orderBook.status)}\r\n                    </Badge>\r\n                    <Badge className={getOrderTypeColor(orderBook.orderType)}>\r\n                      {getOrderTypeName(orderBook.orderType)}\r\n                    </Badge>\r\n                  </span>\r\n                )}\r\n              </SheetDescription>\r\n            </div>\r\n          </div>\r\n\r\n          {/* Đường kẻ phân tách */}\r\n          <div className=\"h-px w-full bg-border\"></div>\r\n\r\n          {/* Các nút chức năng */}\r\n          <div className=\"flex items-center gap-2 px-4 mb-2\">\r\n            <Button\r\n              variant=\"outline\"\r\n              size=\"sm\"\r\n              className=\"flex items-center gap-1\"\r\n              onClick={() => onEdit(orderBook)}\r\n            >\r\n              <Pencil className=\"h-3.5 w-3.5\" />\r\n              <span>Chỉnh sửa</span>\r\n            </Button>\r\n          </div>\r\n        </SheetHeader>\r\n\r\n        {/* Body có thể scroll */}\r\n        <div className=\"flex-1 overflow-y-auto px-4\">\r\n          {loading ? (\r\n            <div className=\"flex flex-col items-center justify-center h-full py-8 gap-4\">\r\n              <Loader2 className=\"h-8 w-8 animate-spin text-primary\" />\r\n              <p className=\"text-muted-foreground\">Đang tải thông tin chi tiết...</p>\r\n            </div>\r\n          ) : (\r\n            <>\r\n              {/* Thông tin cơ bản */}\r\n              <InfoCard\r\n                title=\"Thông tin lệnh\"\r\n                description=\"Thông tin chi tiết về lệnh\"\r\n                className=\"py-4\"\r\n              >\r\n                <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\r\n                  <InfoCardItem\r\n                    label={<div className=\"flex items-center gap-1\"><Tag className=\"h-3.5 w-3.5\" /> ID</div>}\r\n                    value={<div className=\"font-mono text-xs\">{orderBook.id}</div>}\r\n                  />\r\n                  <InfoCardItem\r\n                    label={<div className=\"flex items-center gap-1\"><ScrollText className=\"h-3.5 w-3.5\" /> Số hợp đồng</div>}\r\n                    value={orderBook.contractNumber || '---'}\r\n                  />\r\n                  <InfoCardItem\r\n                    label={<div className=\"flex items-center gap-1\"><DollarSign className=\"h-3.5 w-3.5\" /> Tổng giá trị</div>}\r\n                    value={<div className=\"flex items-center gap-1\">\r\n                      {formatCurrency(orderBook.totalPrice)} VND\r\n                    </div>}\r\n                  />\r\n                  <InfoCardItem\r\n                    label={<div className=\"flex items-center gap-1\"><Landmark className=\"h-3.5 w-3.5\" /> Tiền cọc</div>}\r\n                    value={<div className=\"flex items-center gap-1\">\r\n                      {formatCurrency(orderBook.depositPrice)} VND\r\n                    </div>}\r\n                  />\r\n                  <InfoCardItem\r\n                    label={<div className=\"flex items-center gap-1\"><DollarSign className=\"h-3.5 w-3.5\" /> Tiền tất toán</div>}\r\n                    value={<div className=\"flex items-center gap-1\">\r\n                      {formatCurrency(orderBook.settlementPrice)} VND\r\n                    </div>}\r\n                  />\r\n                  <InfoCardItem\r\n                    label={<div className=\"flex items-center gap-1\"><Home className=\"h-3.5 w-3.5\" /> Phí lưu kho</div>}\r\n                    value={<div className=\"flex items-center gap-1\">\r\n                      {formatCurrency(orderBook.storageFee)} VND\r\n                    </div>}\r\n                  />\r\n                  <InfoCardItem\r\n                    label={<div className=\"flex items-center gap-1\"><Calendar className=\"h-3.5 w-3.5\" /> Thời hạn tất toán</div>}\r\n                    value={orderBook.settlementDeadline ? formatDate(orderBook.settlementDeadline.toString()) : '---'}\r\n                  />\r\n                  <InfoCardItem\r\n                    label={<div className=\"flex items-center gap-1\"><Calendar className=\"h-3.5 w-3.5\" /> Thời gian tất toán</div>}\r\n                    value={orderBook.settlementAt ? formatDate(orderBook.settlementAt.toString()) : '---'}\r\n                  />\r\n                  <InfoCardItem\r\n                    label={<div className=\"flex items-center gap-1\"><BarChart2 className=\"h-3.5 w-3.5\" /> Số lượng sản phẩm</div>}\r\n                    value={getTotalTokens()}\r\n                  />\r\n                  <InfoCardItem\r\n                    label={<div className=\"flex items-center gap-1\"><BarChart2 className=\"h-3.5 w-3.5\" /> Tổng khối lượng</div>}\r\n                    value={`${getTotalVolume().toLocaleString()}`}\r\n                  />\r\n                </div>\r\n              </InfoCard>\r\n\r\n              {/* Thông tin thời gian */}\r\n              <InfoCard\r\n                title=\"Thông tin thời gian\"\r\n                description=\"Thời gian tạo và cập nhật lệnh\"\r\n                className=\"py-4\"\r\n              >\r\n                <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\r\n                  <InfoCardItem\r\n                    label={<div className=\"flex items-center gap-1\"><Calendar className=\"h-3.5 w-3.5\" /> Ngày tạo</div>}\r\n                    value={formatDate(orderBook.createdAt)}\r\n                  />\r\n                  <InfoCardItem\r\n                    label={<div className=\"flex items-center gap-1\"><Clock className=\"h-3.5 w-3.5\" /> Cập nhật lần cuối</div>}\r\n                    value={formatDate(orderBook.updatedAt)}\r\n                  />\r\n                </div>\r\n              </InfoCard>\r\n\r\n              {/* Danh sách sản phẩm */}\r\n              <InfoCard\r\n                title=\"Danh sách sản phẩm\"\r\n                description={`${orderBook.details?.length || 0} sản phẩm trong lệnh này`}\r\n                className=\"py-4\"\r\n              >\r\n                {orderBook.details && orderBook.details.length > 0 ? (\r\n                  <div className=\"overflow-x-auto\">\r\n                    <Table>\r\n                      <TableCaption>Tổng cộng: {orderBook.details.length} sản phẩm</TableCaption>\r\n                      <TableHeader>\r\n                        <TableRow>\r\n                          <TableHead className=\"w-[60px]\">STT</TableHead>\r\n                          <TableHead>Tên sản phẩm</TableHead>\r\n                          <TableHead className=\"text-right\">Giá (VND)</TableHead>\r\n                          <TableHead className=\"text-right\">Số lượng</TableHead>\r\n                          <TableHead className=\"text-right\">Khối lượng </TableHead>\r\n                          <TableHead className=\"text-right\">Thành tiền (VND)</TableHead>\r\n                        </TableRow>\r\n                      </TableHeader>\r\n                      <TableBody>\r\n                        {orderBook.details.map((detail, index) => (\r\n                          <TableRow key={detail.id || `product-${index}`}>\r\n                            <TableCell>{index + 1}</TableCell>\r\n                            <TableCell className=\"font-medium\">\r\n                              {detail.product?.productName  || `Sản phẩm #${index + 1}`}\r\n                            </TableCell>\r\n                            <TableCell className=\"text-right\">\r\n                              {formatCurrency(detail.price)}\r\n                            </TableCell>\r\n                            <TableCell className=\"text-right\">\r\n                              {detail.quantity.toLocaleString()}\r\n                            </TableCell>\r\n                            <TableCell className=\"text-right\">\r\n                              {((detail.quantity || 0) * (detail.product?.weight || 1)).toLocaleString()}\r\n                            </TableCell>\r\n                            <TableCell className=\"text-right\">\r\n                              {formatCurrency(detail.totalPrice)}\r\n                            </TableCell>\r\n                          </TableRow>\r\n                        ))}\r\n                        {orderBook.details.length > 1 && (\r\n                          <TableRow className=\"font-medium bg-muted/50\">\r\n                            <TableCell colSpan={3} className=\"text-right\">Tổng cộng:</TableCell>\r\n                            <TableCell className=\"text-right\">\r\n                              {orderBook.details.reduce((sum, detail) => sum + detail.quantity, 0).toLocaleString()}\r\n                            </TableCell>\r\n                            <TableCell className=\"text-right\">\r\n                              {orderBook.details.reduce((sum, detail) => {\r\n                                const volume = detail.quantity || 0;\r\n                                const weight = detail.product?.weight || 1;\r\n                                return sum + (volume * weight);\r\n                              }, 0).toLocaleString()}\r\n                            </TableCell>\r\n                            <TableCell className=\"text-right\">\r\n                              {formatCurrency(orderBook.details.reduce((sum, detail) => sum + (detail.totalPrice || 0), 0))}\r\n                            </TableCell>\r\n                          </TableRow>\r\n                        )}\r\n                      </TableBody>\r\n                    </Table>\r\n                  </div>\r\n                ) : (\r\n                  <div className=\"text-center text-muted-foreground py-4\">\r\n                    Không có dữ liệu sản phẩm trong lệnh này\r\n                  </div>\r\n                )}\r\n              </InfoCard>\r\n\r\n              {/* Lịch sử giao dịch */}\r\n              <InfoCard\r\n                title=\"Lịch sử giao dịch\"\r\n                description=\"Các giao dịch liên quan đến lệnh này\"\r\n                className=\"py-4\"\r\n              >\r\n                <div className=\"text-center text-muted-foreground py-4\">\r\n                  Chưa có dữ liệu lịch sử giao dịch\r\n                </div>\r\n              </InfoCard>\r\n            </>\r\n          )}\r\n        </div>\r\n      </SheetContent>\r\n    </Sheet>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAiBA;AAQA;AAUA;AAEA;AACA;AACA;AACA;AACA;AACA;AA/CA;;;;;;;;;;;;;;;AAwDA,SAAS,aAAa,EAAE,KAAK,EAAE,KAAK,EAAE,SAAS,EAAqB;IAClE,qBACE,uVAAC;QAAI,WAAW,CAAC,UAAU,EAAE,WAAW;;0BACtC,uVAAC;gBAAI,WAAU;0BAA6C;;;;;;0BAC5D,uVAAC;gBAAI,WAAU;0BAAW;;;;;;;;;;;;AAGhC;AASO,SAAS,qBAAqB,EAAE,WAAW,gBAAgB,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAA6B;IACtH,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,8SAAA,CAAA,WAAQ,AAAD,EAAoB;IAC7D,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,8SAAA,CAAA,WAAQ,AAAD,EAAW;IAEhD,0DAA0D;IAC1D,CAAA,GAAA,8SAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,UAAU,kBAAkB,IAAI;YAClC,WAAW;YACX,0GAAA,CAAA,MAAG,CAAC,GAAG,CAAY,CAAC,YAAY,EAAE,iBAAiB,EAAE,EAAE,EACpD,IAAI,CAAC,CAAA;gBACJ,aAAa;gBACb,WAAW;YACb,GACC,KAAK,CAAC,CAAA;gBACL,QAAQ,KAAK,CAAC,sCAAsC;gBACpD,wQAAA,CAAA,QAAK,CAAC,KAAK,CAAC;gBACZ,WAAW;YACb;QACJ;IACF,GAAG;QAAC;QAAQ,kBAAkB;KAAG;IAEjC,IAAI,CAAC,WAAW,OAAO;IAEvB,MAAM,aAAa,CAAC;QAClB,IAAI,CAAC,SAAS,OAAO;QACrB,IAAI;YACF,OAAO,CAAA,GAAA,6MAAA,CAAA,SAAM,AAAD,EAAE,IAAI,KAAK,UAAU,oBAAoB;gBAAE,QAAQ,mMAAA,CAAA,KAAE;YAAC;QACpE,EAAE,OAAO,OAAO;YACd,OAAO;QACT;IACF;IAEA,2BAA2B;IAC3B,MAAM,iBAAiB,CAAC;QACtB,IAAI,WAAW,aAAa,WAAW,MAAM,OAAO;QACpD,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;YACpC,uBAAuB;QACzB,GAAG,MAAM,CAAC;IACZ;IAEA,mCAAmC;IACnC,MAAM,iBAAiB,CAAC;QACtB,OAAQ;YACN,KAAK,wKAAA,CAAA,cAAW,CAAC,SAAS;gBACxB,OAAO;YACT,KAAK,wKAAA,CAAA,cAAW,CAAC,SAAS;gBACxB,OAAO;YACT,KAAK,wKAAA,CAAA,cAAW,CAAC,UAAU;gBACzB,OAAO;YACT,KAAK,wKAAA,CAAA,cAAW,CAAC,SAAS;gBACxB,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,6BAA6B;IAC7B,MAAM,gBAAgB,CAAC;QACrB,OAAQ;YACN,KAAK,wKAAA,CAAA,cAAW,CAAC,SAAS;gBACxB,OAAO;YACT,KAAK,wKAAA,CAAA,cAAW,CAAC,SAAS;gBACxB,OAAO;YACT,KAAK,wKAAA,CAAA,cAAW,CAAC,UAAU;gBACzB,OAAO;YACT,KAAK,wKAAA,CAAA,cAAW,CAAC,SAAS;gBACxB,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,2BAA2B;IAC3B,MAAM,mBAAmB,CAAC;QACxB,OAAQ;YACN,KAAK,wKAAA,CAAA,YAAS,CAAC,GAAG;gBAChB,OAAO;YACT,KAAK,wKAAA,CAAA,YAAS,CAAC,IAAI;gBACjB,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,2BAA2B;IAC3B,MAAM,oBAAoB,CAAC;QACzB,OAAQ;YACN,KAAK,wKAAA,CAAA,YAAS,CAAC,GAAG;gBAChB,OAAO;YACT,KAAK,wKAAA,CAAA,YAAS,CAAC,IAAI;gBACjB,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,kBAAkB;IAClB,MAAM,iBAAiB;QACrB,OAAO,UAAU,OAAO,EAAE,UAAU;IACtC;IAEA,uCAAuC;IACvC,MAAM,iBAAiB;QACrB,IAAI,CAAC,UAAU,OAAO,IAAI,UAAU,OAAO,CAAC,MAAM,KAAK,GAAG,OAAO;QAEjE,OAAO,UAAU,OAAO,CAAC,MAAM,CAAC,CAAC,KAAK;YACpC,MAAM,SAAS,OAAO,QAAQ,IAAI;YAClC,MAAM,SAAS,OAAO,OAAO,EAAE,UAAU;YACzC,OAAO,MAAO,SAAS;QACzB,GAAG;IACL;IAEA,qBACE,uVAAC,0HAAA,CAAA,QAAK;QAAC,MAAM;QAAQ,cAAc,CAAC,OAAS,CAAC,QAAQ;kBACpD,cAAA,uVAAC,0HAAA,CAAA,eAAY;YAAC,WAAU;;8BAEtB,uVAAC,0HAAA,CAAA,cAAW;oBAAC,WAAU;;sCACrB,uVAAC;4BAAI,WAAU;;8CACb,uVAAC;oCAAI,WAAU;8CACZ,wBACC,uVAAC,qSAAA,CAAA,UAAO;wCAAC,WAAU;;;;;6DAEnB,uVAAC,4TAAA,CAAA,eAAY;wCAAC,WAAU;;;;;;;;;;;8CAG5B,uVAAC;oCAAI,WAAU;;sDACb,uVAAC,0HAAA,CAAA,aAAU;4CAAC,WAAU;sDACnB,wBACC,uVAAC;gDAAI,WAAU;;kEACb,uVAAC,qSAAA,CAAA,UAAO;wDAAC,WAAU;;;;;;kEACnB,uVAAC;kEAAK;;;;;;;;;;;uDAGR,CAAC,MAAM,EAAE,UAAU,cAAc,IAAI,UAAU,EAAE,EAAE,UAAU,GAAG,IAAI;;;;;;sDAGxE,uVAAC,0HAAA,CAAA,mBAAgB;sDACd,wBACC,uVAAC;gDAAK,WAAU;;kEACd,uVAAC,qSAAA,CAAA,UAAO;wDAAC,WAAU;;;;;;kEACnB,uVAAC;kEAAK;;;;;;;;;;;qEAGR,uVAAC;gDAAK,WAAU;;kEACd,uVAAC,0HAAA,CAAA,QAAK;wDAAC,WAAW,eAAe,UAAU,MAAM;kEAC9C,cAAc,UAAU,MAAM;;;;;;kEAEjC,uVAAC,0HAAA,CAAA,QAAK;wDAAC,WAAW,kBAAkB,UAAU,SAAS;kEACpD,iBAAiB,UAAU,SAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCASjD,uVAAC;4BAAI,WAAU;;;;;;sCAGf,uVAAC;4BAAI,WAAU;sCACb,cAAA,uVAAC,2HAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,WAAU;gCACV,SAAS,IAAM,OAAO;;kDAEtB,uVAAC,0RAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;kDAClB,uVAAC;kDAAK;;;;;;;;;;;;;;;;;;;;;;;8BAMZ,uVAAC;oBAAI,WAAU;8BACZ,wBACC,uVAAC;wBAAI,WAAU;;0CACb,uVAAC,qSAAA,CAAA,UAAO;gCAAC,WAAU;;;;;;0CACnB,uVAAC;gCAAE,WAAU;0CAAwB;;;;;;;;;;;6CAGvC;;0CAEE,uVAAC,2HAAA,CAAA,WAAQ;gCACP,OAAM;gCACN,aAAY;gCACZ,WAAU;0CAEV,cAAA,uVAAC;oCAAI,WAAU;;sDACb,uVAAC;4CACC,qBAAO,uVAAC;gDAAI,WAAU;;kEAA0B,uVAAC,oRAAA,CAAA,MAAG;wDAAC,WAAU;;;;;;oDAAgB;;;;;;;4CAC/E,qBAAO,uVAAC;gDAAI,WAAU;0DAAqB,UAAU,EAAE;;;;;;;;;;;sDAEzD,uVAAC;4CACC,qBAAO,uVAAC;gDAAI,WAAU;;kEAA0B,uVAAC,sSAAA,CAAA,aAAU;wDAAC,WAAU;;;;;;oDAAgB;;;;;;;4CACtF,OAAO,UAAU,cAAc,IAAI;;;;;;sDAErC,uVAAC;4CACC,qBAAO,uVAAC;gDAAI,WAAU;;kEAA0B,uVAAC,sSAAA,CAAA,aAAU;wDAAC,WAAU;;;;;;oDAAgB;;;;;;;4CACtF,qBAAO,uVAAC;gDAAI,WAAU;;oDACnB,eAAe,UAAU,UAAU;oDAAE;;;;;;;;;;;;sDAG1C,uVAAC;4CACC,qBAAO,uVAAC;gDAAI,WAAU;;kEAA0B,uVAAC,8RAAA,CAAA,WAAQ;wDAAC,WAAU;;;;;;oDAAgB;;;;;;;4CACpF,qBAAO,uVAAC;gDAAI,WAAU;;oDACnB,eAAe,UAAU,YAAY;oDAAE;;;;;;;;;;;;sDAG5C,uVAAC;4CACC,qBAAO,uVAAC;gDAAI,WAAU;;kEAA0B,uVAAC,sSAAA,CAAA,aAAU;wDAAC,WAAU;;;;;;oDAAgB;;;;;;;4CACtF,qBAAO,uVAAC;gDAAI,WAAU;;oDACnB,eAAe,UAAU,eAAe;oDAAE;;;;;;;;;;;;sDAG/C,uVAAC;4CACC,qBAAO,uVAAC;gDAAI,WAAU;;kEAA0B,uVAAC,uRAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;oDAAgB;;;;;;;4CAChF,qBAAO,uVAAC;gDAAI,WAAU;;oDACnB,eAAe,UAAU,UAAU;oDAAE;;;;;;;;;;;;sDAG1C,uVAAC;4CACC,qBAAO,uVAAC;gDAAI,WAAU;;kEAA0B,uVAAC,8RAAA,CAAA,WAAQ;wDAAC,WAAU;;;;;;oDAAgB;;;;;;;4CACpF,OAAO,UAAU,kBAAkB,GAAG,WAAW,UAAU,kBAAkB,CAAC,QAAQ,MAAM;;;;;;sDAE9F,uVAAC;4CACC,qBAAO,uVAAC;gDAAI,WAAU;;kEAA0B,uVAAC,8RAAA,CAAA,WAAQ;wDAAC,WAAU;;;;;;oDAAgB;;;;;;;4CACpF,OAAO,UAAU,YAAY,GAAG,WAAW,UAAU,YAAY,CAAC,QAAQ,MAAM;;;;;;sDAElF,uVAAC;4CACC,qBAAO,uVAAC;gDAAI,WAAU;;kEAA0B,uVAAC,oTAAA,CAAA,YAAS;wDAAC,WAAU;;;;;;oDAAgB;;;;;;;4CACrF,OAAO;;;;;;sDAET,uVAAC;4CACC,qBAAO,uVAAC;gDAAI,WAAU;;kEAA0B,uVAAC,oTAAA,CAAA,YAAS;wDAAC,WAAU;;;;;;oDAAgB;;;;;;;4CACrF,OAAO,GAAG,iBAAiB,cAAc,IAAI;;;;;;;;;;;;;;;;;0CAMnD,uVAAC,2HAAA,CAAA,WAAQ;gCACP,OAAM;gCACN,aAAY;gCACZ,WAAU;0CAEV,cAAA,uVAAC;oCAAI,WAAU;;sDACb,uVAAC;4CACC,qBAAO,uVAAC;gDAAI,WAAU;;kEAA0B,uVAAC,8RAAA,CAAA,WAAQ;wDAAC,WAAU;;;;;;oDAAgB;;;;;;;4CACpF,OAAO,WAAW,UAAU,SAAS;;;;;;sDAEvC,uVAAC;4CACC,qBAAO,uVAAC;gDAAI,WAAU;;kEAA0B,uVAAC,wRAAA,CAAA,QAAK;wDAAC,WAAU;;;;;;oDAAgB;;;;;;;4CACjF,OAAO,WAAW,UAAU,SAAS;;;;;;;;;;;;;;;;;0CAM3C,uVAAC,2HAAA,CAAA,WAAQ;gCACP,OAAM;gCACN,aAAa,GAAG,UAAU,OAAO,EAAE,UAAU,EAAE,wBAAwB,CAAC;gCACxE,WAAU;0CAET,UAAU,OAAO,IAAI,UAAU,OAAO,CAAC,MAAM,GAAG,kBAC/C,uVAAC;oCAAI,WAAU;8CACb,cAAA,uVAAC,0HAAA,CAAA,QAAK;;0DACJ,uVAAC,0HAAA,CAAA,eAAY;;oDAAC;oDAAY,UAAU,OAAO,CAAC,MAAM;oDAAC;;;;;;;0DACnD,uVAAC,0HAAA,CAAA,cAAW;0DACV,cAAA,uVAAC,0HAAA,CAAA,WAAQ;;sEACP,uVAAC,0HAAA,CAAA,YAAS;4DAAC,WAAU;sEAAW;;;;;;sEAChC,uVAAC,0HAAA,CAAA,YAAS;sEAAC;;;;;;sEACX,uVAAC,0HAAA,CAAA,YAAS;4DAAC,WAAU;sEAAa;;;;;;sEAClC,uVAAC,0HAAA,CAAA,YAAS;4DAAC,WAAU;sEAAa;;;;;;sEAClC,uVAAC,0HAAA,CAAA,YAAS;4DAAC,WAAU;sEAAa;;;;;;sEAClC,uVAAC,0HAAA,CAAA,YAAS;4DAAC,WAAU;sEAAa;;;;;;;;;;;;;;;;;0DAGtC,uVAAC,0HAAA,CAAA,YAAS;;oDACP,UAAU,OAAO,CAAC,GAAG,CAAC,CAAC,QAAQ,sBAC9B,uVAAC,0HAAA,CAAA,WAAQ;;8EACP,uVAAC,0HAAA,CAAA,YAAS;8EAAE,QAAQ;;;;;;8EACpB,uVAAC,0HAAA,CAAA,YAAS;oEAAC,WAAU;8EAClB,OAAO,OAAO,EAAE,eAAgB,CAAC,UAAU,EAAE,QAAQ,GAAG;;;;;;8EAE3D,uVAAC,0HAAA,CAAA,YAAS;oEAAC,WAAU;8EAClB,eAAe,OAAO,KAAK;;;;;;8EAE9B,uVAAC,0HAAA,CAAA,YAAS;oEAAC,WAAU;8EAClB,OAAO,QAAQ,CAAC,cAAc;;;;;;8EAEjC,uVAAC,0HAAA,CAAA,YAAS;oEAAC,WAAU;8EAClB,CAAC,CAAC,OAAO,QAAQ,IAAI,CAAC,IAAI,CAAC,OAAO,OAAO,EAAE,UAAU,CAAC,CAAC,EAAE,cAAc;;;;;;8EAE1E,uVAAC,0HAAA,CAAA,YAAS;oEAAC,WAAU;8EAClB,eAAe,OAAO,UAAU;;;;;;;2DAftB,OAAO,EAAE,IAAI,CAAC,QAAQ,EAAE,OAAO;;;;;oDAmB/C,UAAU,OAAO,CAAC,MAAM,GAAG,mBAC1B,uVAAC,0HAAA,CAAA,WAAQ;wDAAC,WAAU;;0EAClB,uVAAC,0HAAA,CAAA,YAAS;gEAAC,SAAS;gEAAG,WAAU;0EAAa;;;;;;0EAC9C,uVAAC,0HAAA,CAAA,YAAS;gEAAC,WAAU;0EAClB,UAAU,OAAO,CAAC,MAAM,CAAC,CAAC,KAAK,SAAW,MAAM,OAAO,QAAQ,EAAE,GAAG,cAAc;;;;;;0EAErF,uVAAC,0HAAA,CAAA,YAAS;gEAAC,WAAU;0EAClB,UAAU,OAAO,CAAC,MAAM,CAAC,CAAC,KAAK;oEAC9B,MAAM,SAAS,OAAO,QAAQ,IAAI;oEAClC,MAAM,SAAS,OAAO,OAAO,EAAE,UAAU;oEACzC,OAAO,MAAO,SAAS;gEACzB,GAAG,GAAG,cAAc;;;;;;0EAEtB,uVAAC,0HAAA,CAAA,YAAS;gEAAC,WAAU;0EAClB,eAAe,UAAU,OAAO,CAAC,MAAM,CAAC,CAAC,KAAK,SAAW,MAAM,CAAC,OAAO,UAAU,IAAI,CAAC,GAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;yDAQtG,uVAAC;oCAAI,WAAU;8CAAyC;;;;;;;;;;;0CAO5D,uVAAC,2HAAA,CAAA,WAAQ;gCACP,OAAM;gCACN,aAAY;gCACZ,WAAU;0CAEV,cAAA,uVAAC;oCAAI,WAAU;8CAAyC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUxE", "debugId": null}}, {"offset": {"line": 4556, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/bloomgoo/gold-exchange-platform/frontend/components/common/user/order-books/components/order-business-type-tabs.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { cn } from '@/lib/utils';\r\nimport { Badge } from '@/components/ui/badge';\r\nimport { OrderStatus, OrderType, BusinessType } from '../type/order-books';\r\n\r\nexport type OrderBusinessTypeFilter = 'all' | BusinessType;\r\n\r\ninterface OrderBusinessTypeTabsProps {\r\n  currentStatus: OrderBusinessTypeFilter;\r\n  onStatusChange: (status: OrderBusinessTypeFilter) => void;\r\n  counts: {\r\n    all: number;\r\n    normal: number;\r\n    immediateDelivery: number;\r\n  };\r\n  className?: string;\r\n}\r\n\r\nexport function OrderBusinessTypeTabs({\r\n  currentStatus,\r\n  onStatusChange,\r\n  counts,\r\n  className\r\n}: OrderBusinessTypeTabsProps) {\r\n  const tabs = [\r\n    {\r\n      id: 'all' as const,\r\n      label: 'Tất cả',\r\n      count: counts.all,\r\n    },\r\n    {\r\n      id: BusinessType.NORMAL,\r\n      label: 'Bạc online ký quỹ',\r\n      count: counts.normal,\r\n    },\r\n    {\r\n      id: BusinessType.IMMEDIATE_DELIVERY,\r\n      label: 'Bạc giao ngay',\r\n      count: counts.immediateDelivery,\r\n    },\r\n\r\n  ];\r\n\r\n  return (\r\n    <div className={cn(\"flex items-center space-x-1 overflow-x-auto bg-muted/50 p-1 rounded-md\", className)}>\r\n      {tabs.map((tab) => (\r\n        <button\r\n          key={tab.id}\r\n          onClick={() => onStatusChange(tab.id)}\r\n          className={cn(\r\n            \"flex items-center px-3 py-1.5 text-sm font-medium rounded-md transition-colors whitespace-nowrap\",\r\n            currentStatus === tab.id\r\n              ? \"bg-background text-foreground shadow-sm\"\r\n              : \"text-muted-foreground hover:bg-background/50\"\r\n          )}\r\n        >\r\n          {tab.label}\r\n          <Badge\r\n            variant={currentStatus === tab.id ? \"default\" : \"secondary\"}\r\n            className={cn(\r\n              \"ml-2\",\r\n              currentStatus === tab.id\r\n                ? \"bg-primary/10 text-primary\"\r\n                : \"bg-muted text-muted-foreground\"\r\n            )}\r\n            title={`Tổng số lệnh ${tab.label.toLowerCase()} trong hệ thống`}\r\n          >\r\n            {tab.count.toLocaleString('vi-VN')}\r\n          </Badge>\r\n        </button>\r\n      ))}\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AAAA;AACA;AACA;AAJA;;;;;AAmBO,SAAS,sBAAsB,EACpC,aAAa,EACb,cAAc,EACd,MAAM,EACN,SAAS,EACkB;IAC3B,MAAM,OAAO;QACX;YACE,IAAI;YACJ,OAAO;YACP,OAAO,OAAO,GAAG;QACnB;QACA;YACE,IAAI,wKAAA,CAAA,eAAY,CAAC,MAAM;YACvB,OAAO;YACP,OAAO,OAAO,MAAM;QACtB;QACA;YACE,IAAI,wKAAA,CAAA,eAAY,CAAC,kBAAkB;YACnC,OAAO;YACP,OAAO,OAAO,iBAAiB;QACjC;KAED;IAED,qBACE,uVAAC;QAAI,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EAAE,0EAA0E;kBAC1F,KAAK,GAAG,CAAC,CAAC,oBACT,uVAAC;gBAEC,SAAS,IAAM,eAAe,IAAI,EAAE;gBACpC,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EACV,oGACA,kBAAkB,IAAI,EAAE,GACpB,4CACA;;oBAGL,IAAI,KAAK;kCACV,uVAAC,0HAAA,CAAA,QAAK;wBACJ,SAAS,kBAAkB,IAAI,EAAE,GAAG,YAAY;wBAChD,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EACV,QACA,kBAAkB,IAAI,EAAE,GACpB,+BACA;wBAEN,OAAO,CAAC,aAAa,EAAE,IAAI,KAAK,CAAC,WAAW,GAAG,eAAe,CAAC;kCAE9D,IAAI,KAAK,CAAC,cAAc,CAAC;;;;;;;eApBvB,IAAI,EAAE;;;;;;;;;;AA0BrB", "debugId": null}}, {"offset": {"line": 4622, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/bloomgoo/gold-exchange-platform/frontend/components/common/user/order-books/settlement-modal.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { Badge } from '@/components/ui/badge';\r\nimport { Button } from '@/components/ui/button';\r\nimport {\r\n  Dialog,\r\n  DialogContent,\r\n  DialogDescription,\r\n  DialogFooter,\r\n  DialogHeader,\r\n  DialogTitle,\r\n} from '@/components/ui/dialog';\r\nimport {\r\n  Form,\r\n  FormControl,\r\n  FormDescription,\r\n  FormField,\r\n  FormItem,\r\n  FormLabel,\r\n  FormMessage,\r\n} from '@/components/ui/form';\r\nimport { api } from '@/lib/api';\r\nimport { ApiResponse } from '@/lib/response';\r\nimport { calculateNormalProductPrice, formatCurrency } from '@/lib/utils';\r\nimport { usePolygonForex } from '@/services/websocket/polygon-forex-socket.service';\r\nimport { Loader2, TrendingDown, TrendingUp } from 'lucide-react';\r\nimport { useEffect, useState } from 'react';\r\nimport { useForm } from 'react-hook-form';\r\nimport { toast } from 'sonner';\r\nimport { BusinessType, OrderBook, OrderStatus, OrderType } from './type/order-books';\r\n\r\ninterface SettlementModalProps {\r\n  orderBook: OrderBook | null;\r\n  isOpen: boolean;\r\n  onClose: () => void;\r\n  onSuccess: () => void;\r\n  withdrawalMode?: boolean;\r\n  fixedPrice?: {\r\n    usd: number;\r\n    vnd: number;\r\n  } | null;\r\n}\r\n\r\nexport function SettlementModal({ orderBook, isOpen, onClose, onSuccess, withdrawalMode = false, fixedPrice = null }: SettlementModalProps) {\r\n  const [isSubmitting, setIsSubmitting] = useState(false);\r\n  const [isLoading, setIsLoading] = useState(false);\r\n  const [orderDetail, setOrderDetail] = useState<OrderBook | null>(null);\r\n  const [marketPrice, setMarketPrice] = useState<string>('');\r\n  const [calculatedPrices, setCalculatedPrices] = useState<{ [key: string]: number }>({});\r\n  const [calculatedSettlementAmount, setCalculatedSettlementAmount] = useState<number | null>(null);\r\n  const [calculatedProfit, setCalculatedProfit] = useState<number | null>(null);\r\n\r\n  // Sử dụng hook usePolygonForex để lấy thông tin giá từ websocket\r\n  const { currentQuote, isConnected } = usePolygonForex();\r\n\r\n  // Khởi tạo form\r\n  const form = useForm({\r\n    defaultValues: {\r\n      marketPrice: ''\r\n    }\r\n  });\r\n\r\n  // Lấy thông tin chi tiết của lệnh khi modal mở\r\n  useEffect(() => {\r\n    if (isOpen && orderBook) {\r\n      fetchOrderDetail(orderBook.id);\r\n      // Reset các state khi mở modal\r\n      setMarketPrice('');\r\n      setCalculatedPrices({});\r\n      setCalculatedSettlementAmount(null);\r\n      setCalculatedProfit(null);\r\n    }\r\n  }, [isOpen, orderBook]);\r\n\r\n  // Thiết lập giá trị tất toán cho chế độ rút bạc\r\n  useEffect(() => {\r\n    if (isOpen && orderDetail && withdrawalMode) {\r\n      // Tính toán số tiền cần thanh toán (tổng giá trị - tiền đặt cọc)\r\n      const remainingAmount = (orderDetail.totalPrice || 0) - (orderDetail.depositPrice || 0);\r\n      setCalculatedSettlementAmount(remainingAmount);\r\n    }\r\n  }, [isOpen, orderDetail, withdrawalMode]);\r\n\r\n  // Effect để tự động cập nhật giá thị trường khi có dữ liệu mới từ websocket\r\n  useEffect(() => {\r\n    if (currentQuote && !withdrawalMode && orderDetail?.businessType !== BusinessType.IMMEDIATE_DELIVERY) {\r\n      // Nếu có fixedPrice, sử dụng giá cố định\r\n      if (fixedPrice) {\r\n        setMarketPrice(fixedPrice.usd.toString());\r\n        handleMarketPriceChange(fixedPrice.usd.toString(), fixedPrice.vnd);\r\n      } else {\r\n        // Nếu không có fixedPrice, sử dụng giá từ websocket như cũ\r\n        const price = orderDetail?.orderType === OrderType.BUY ? currentQuote.buyPriceVND : currentQuote.sellPriceVND;\r\n        const usdPrice = orderDetail?.orderType === OrderType.BUY ? currentQuote.bidPrice : currentQuote.askPrice;\r\n\r\n        setMarketPrice(usdPrice.toString());\r\n        handleMarketPriceChange(usdPrice.toString(), price);\r\n      }\r\n    }\r\n  }, [currentQuote, orderDetail, withdrawalMode, fixedPrice]);\r\n\r\n  // Hàm tính giá token đã được chuyển sang utils.ts\r\n\r\n  // Xử lý khi thay đổi giá thị trường\r\n  const handleMarketPriceChange = (value: string, priceVND?: number) => {\r\n    setMarketPrice(value);\r\n\r\n    if (!orderDetail?.details || orderDetail.details.length === 0) return;\r\n\r\n    const numValue = parseFloat(value);\r\n    if (isNaN(numValue) || numValue <= 0) {\r\n      setCalculatedPrices({});\r\n      setCalculatedSettlementAmount(null);\r\n      setCalculatedProfit(null);\r\n      return;\r\n    }\r\n\r\n    // Tính toán giá mới cho từng loại bạc dựa trên giá thị trường\r\n    const newPrices: { [key: string]: number } = {};\r\n\r\n    orderDetail.details.forEach(detail => {\r\n      if (detail.product) {\r\n        // Sử dụng giá VND từ websocket nếu có, nếu không thì tính từ giá USD\r\n        const tokenOz = detail.product.weight || 1;\r\n        const price = priceVND || calculateNormalProductPrice(numValue, tokenOz);\r\n        newPrices[detail.productId] = price;\r\n      }\r\n    });\r\n\r\n    setCalculatedPrices(newPrices);\r\n\r\n    // Tính toán tổng giá trị mới\r\n    const newTotalPrice = orderDetail.details.reduce((sum, detail) => {\r\n      return sum + (newPrices[detail.productId] || 0) * (detail.quantity || 0);\r\n    }, 0);\r\n\r\n    // Tính toán tiền tất toán mới (90% của tổng giá trị)\r\n    const newSettlementAmount = newTotalPrice * 0.9;\r\n    setCalculatedSettlementAmount(newSettlementAmount);\r\n\r\n    // Tính toán lợi nhuận\r\n    const originalTotalPrice = orderDetail.totalPrice || 0;\r\n    const swapFee = 0; // Phí swap tạm thời để 0\r\n    const profit = newTotalPrice - originalTotalPrice - swapFee;\r\n    setCalculatedProfit(profit);\r\n  };\r\n\r\n  // Hàm lấy thông tin chi tiết của lệnh\r\n  const fetchOrderDetail = async (orderId: string) => {\r\n    try {\r\n      setIsLoading(true);\r\n      const response = await api.get<OrderBook>(`order-books/${orderId}`);\r\n      if (response) {\r\n        setOrderDetail(response);\r\n      }\r\n    } catch (error) {\r\n      console.error('Error fetching order detail:', error);\r\n      toast.error('Không thể lấy thông tin chi tiết lệnh', {\r\n        description: error instanceof Error ? error.message : 'Vui lòng thử lại sau',\r\n      });\r\n    } finally {\r\n      setIsLoading(false);\r\n    }\r\n  };\r\n\r\n  // Xử lý tất toán lệnh\r\n  const handleSettlement = async () => {\r\n    try {\r\n      setIsSubmitting(true);\r\n\r\n      // Chuẩn bị dữ liệu theo DTO\r\n      const payload: {\r\n        price?: number;\r\n        isImmediateDelivery?: boolean;\r\n      } = {};\r\n\r\n      // Xử lý khác nhau dựa trên loại hình kinh doanh và chế độ rút bạc\r\n      if (orderDetail?.businessType === BusinessType.IMMEDIATE_DELIVERY) {\r\n        // Với bạc giao ngay, chỉ cần gọi API tất toán lệnh\r\n        await api.patch(`order-books/${orderBook?.id}/status/completed`, {\r\n          isImmediateDelivery: true\r\n        });\r\n        toast.success('Đã xác nhận rút bạc giao ngay thành công');\r\n      } else if (withdrawalMode) {\r\n        // Chế độ rút bạc: Chuyển lệnh mua bạc ký quỹ sang bạc giao ngay\r\n        await api.patch(`order-books/${orderBook?.id}/status/completed`, {\r\n          price: orderDetail?.totalPrice || 0,\r\n          isImmediateDelivery: true\r\n        });\r\n        toast.success('Đã xác nhận chuyển đổi sang bạc giao ngay thành công');\r\n      } else {\r\n        // Tất toán bình thường cho bạc ký quỹ online\r\n        if (fixedPrice) {\r\n          await api.patch<ApiResponse<OrderBook>>(`order-books/${orderBook?.id}/status/completed`, {\r\n            price: fixedPrice.vnd\r\n          });\r\n          \r\n          toast.success('Đã tất toán lệnh thành công');\r\n        } else {\r\n          // Nếu không có giá cố định, yêu cầu người dùng chờ\r\n          toast.error('Vui lòng chờ giá cố định được cập nhật');\r\n          setIsSubmitting(false);\r\n          return;\r\n        }\r\n      }\r\n\r\n      onSuccess();\r\n      onClose();\r\n    } catch (error) {\r\n      console.error('Error completing order:', error);\r\n\r\n      // Thông báo lỗi tùy theo loại hình kinh doanh\r\n      if (orderDetail?.businessType === BusinessType.IMMEDIATE_DELIVERY) {\r\n        toast.error('Không thể rút bạc giao ngay', {\r\n          description: error instanceof Error ? error.message : 'Vui lòng thử lại sau',\r\n        });\r\n      } else {\r\n        toast.error('Không thể tất toán lệnh', {\r\n          description: error instanceof Error ? error.message : 'Vui lòng thử lại sau',\r\n        });\r\n      }\r\n    } finally {\r\n      setIsSubmitting(false);\r\n    }\r\n  };\r\n\r\n  // Chuyển đổi trạng thái sang tiếng Việt\r\n  const getStatusName = (status: OrderStatus) => {\r\n    switch (status) {\r\n      case OrderStatus.COMPLETED:\r\n        return 'Đã tất toán';\r\n      case OrderStatus.DEPOSITED:\r\n        return 'Đã ký quỹ';\r\n      case OrderStatus.TERMINATED:\r\n        return 'Đã cắt hợp đồng';\r\n      case OrderStatus.CANCELLED:\r\n        return 'Đã hủy';\r\n      default:\r\n        return status;\r\n    }\r\n  };\r\n\r\n  // Lấy màu cho badge trạng thái\r\n  const getStatusColor = (status: OrderStatus) => {\r\n    switch (status) {\r\n      case OrderStatus.COMPLETED:\r\n        return 'bg-green-100 text-green-800';\r\n      case OrderStatus.DEPOSITED:\r\n        return 'bg-purple-100 text-purple-800';\r\n      case OrderStatus.TERMINATED:\r\n        return 'bg-red-100 text-red-800';\r\n      case OrderStatus.CANCELLED:\r\n        return 'bg-orange-100 text-orange-800';\r\n      default:\r\n        return 'bg-gray-100 text-gray-800';\r\n    }\r\n  };\r\n\r\n  // Tính tổng khối lượng\r\n  const totalVolume = orderDetail?.details?.reduce((sum, detail) => sum + (detail.quantity || 0), 0) || 0;\r\n\r\n  if (!orderBook) return null;\r\n\r\n  return (\r\n    <Dialog open={isOpen} onOpenChange={onClose}>\r\n      <DialogContent className=\"sm:max-w-[550px]\">\r\n        <DialogHeader>\r\n          <DialogTitle>\r\n            {orderDetail?.businessType === BusinessType.IMMEDIATE_DELIVERY\r\n              ? \"Rút bạc giao ngay\"\r\n              : withdrawalMode\r\n                ? \"Chuyển đổi sang bạc giao ngay\"\r\n                : \"Tất toán lệnh\"}\r\n          </DialogTitle>\r\n          <DialogDescription>\r\n            {orderDetail?.businessType === BusinessType.IMMEDIATE_DELIVERY\r\n              ? \"Xác nhận thông tin để rút bạc giao ngay. Vui lòng kiểm tra thông tin lệnh trước khi xác nhận.\"\r\n              : withdrawalMode\r\n                ? \"Xác nhận chuyển đổi lệnh bạc ký quỹ sang bạc giao ngay. Bạn cần thanh toán 90% giá trị còn lại để rút bạc.\"\r\n                : \"Xác nhận tất toán lệnh. Bạn có thể điều chỉnh giá thị trường để tính toán giá tất toán.\"}\r\n          </DialogDescription>\r\n        </DialogHeader>\r\n\r\n        {isLoading ? (\r\n          <div className=\"flex items-center justify-center py-8\">\r\n            <Loader2 className=\"h-8 w-8 animate-spin text-primary\" />\r\n            <span className=\"ml-2\">Đang tải thông tin...</span>\r\n          </div>\r\n        ) : (\r\n          <Form {...form}>\r\n            <form className=\"grid gap-4 py-4\">\r\n              <div className=\"grid grid-cols-2 gap-4\">\r\n                <div className=\"space-y-1\">\r\n                  <p className=\"text-sm font-medium text-muted-foreground\">Số hợp đồng</p>\r\n                  <p className=\"text-sm font-semibold\">{orderDetail?.contractNumber || '---'}</p>\r\n                </div>\r\n                <div className=\"space-y-1\">\r\n                  <p className=\"text-sm font-medium text-muted-foreground\">Trạng thái</p>\r\n                  <Badge className={getStatusColor(orderDetail?.status || OrderStatus.DEPOSITED)}>\r\n                    {getStatusName(orderDetail?.status || OrderStatus.DEPOSITED)}\r\n                  </Badge>\r\n                </div>\r\n              </div>\r\n\r\n              <div className=\"grid grid-cols-2 gap-4\">\r\n                <div className=\"space-y-1\">\r\n                  <p className=\"text-sm font-medium text-muted-foreground\">Khối lượng</p>\r\n                  <p className=\"text-sm font-semibold\">{totalVolume.toLocaleString()} g</p>\r\n                </div>\r\n                <div className=\"space-y-1\">\r\n                  <p className=\"text-sm font-medium text-muted-foreground\">Tổng giá trị</p>\r\n                  <p className=\"text-sm font-semibold\">{formatCurrency(orderDetail?.totalPrice)}</p>\r\n                </div>\r\n              </div>\r\n\r\n              <div className=\"grid grid-cols-2 gap-4\">\r\n                <div className=\"space-y-1\">\r\n                  <p className=\"text-sm font-medium text-muted-foreground\">Tiền đặt cọc</p>\r\n                  <p className=\"text-sm font-semibold\">{formatCurrency(orderDetail?.depositPrice)}</p>\r\n                </div>\r\n                <div className=\"space-y-1\">\r\n                  <p className=\"text-sm font-medium text-muted-foreground\">Tiền tất toán</p>\r\n                  <p className=\"text-sm font-semibold\">\r\n                    {orderDetail?.businessType === BusinessType.IMMEDIATE_DELIVERY || !calculatedSettlementAmount\r\n                      ? formatCurrency(orderDetail?.settlementPrice)\r\n                      : formatCurrency(calculatedSettlementAmount)}\r\n                  </p>\r\n                </div>\r\n              </div>\r\n\r\n              <div className=\"grid grid-cols-2 gap-4\">\r\n                <div className=\"space-y-1\">\r\n                  <p className=\"text-sm font-medium text-muted-foreground\">Phí lưu kho</p>\r\n                  <p className=\"text-sm font-semibold\">{formatCurrency(orderDetail?.storageFee)}</p>\r\n                </div>\r\n                <div className=\"space-y-1\">\r\n                  <p className=\"text-sm font-medium text-muted-foreground\">Hạn tất toán</p>\r\n                  <p className=\"text-sm font-semibold\">\r\n                    {orderDetail?.settlementDeadline\r\n                      ? new Date(orderDetail.settlementDeadline).toLocaleDateString('vi-VN')\r\n                      : '---'}\r\n                  </p>\r\n                </div>\r\n              </div>\r\n\r\n              <div className=\"space-y-4 mt-4\">\r\n                {orderDetail?.businessType === BusinessType.IMMEDIATE_DELIVERY ? (\r\n                  // Form cho bạc giao ngay - chỉ hiển thị thông tin chi tiết\r\n                  <div className=\"space-y-2\">\r\n                    <div className=\"flex items-center space-x-2 mb-2\">\r\n                      <Badge className=\"bg-blue-100 text-blue-800\">Bạc giao ngay</Badge>\r\n                      <p className=\"text-xs text-muted-foreground\">\r\n                        Xác nhận thông tin để rút bạc giao ngay\r\n                      </p>\r\n                    </div>\r\n\r\n                    <p className=\"text-sm font-medium\">Thông tin bạc</p>\r\n                    <div className=\"border rounded-md p-3 bg-muted/50\">\r\n                      <div className=\"grid grid-cols-4 gap-2 mb-2 text-xs font-medium text-muted-foreground\">\r\n                        <div>Loại bạc</div>\r\n                        <div>Khối lượng</div>\r\n                        <div>Giá mua</div>\r\n                        <div>Thành tiền</div>\r\n                      </div>\r\n                      {orderDetail?.details?.map((detail, index) => (\r\n                        <div key={index} className=\"grid grid-cols-4 gap-2 py-1.5 border-t text-sm\">\r\n                          <div className=\"font-medium\">{detail.product?.productName || 'Bạc'}</div>\r\n                          <div>{detail.quantity?.toLocaleString() || 0} g</div>\r\n                          <div>{formatCurrency(detail.price)}</div>\r\n                          <div>{formatCurrency(detail.price ? detail.price * (detail.quantity || 0) : 0)}</div>\r\n                        </div>\r\n                      ))}\r\n                      <div className=\"grid grid-cols-4 gap-2 py-1.5 border-t text-sm font-semibold\">\r\n                        <div>Tổng cộng</div>\r\n                        <div>{totalVolume.toLocaleString()} g</div>\r\n                        <div></div>\r\n                        <div>{formatCurrency(orderDetail?.totalPrice)}</div>\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n                ) : (\r\n                  // Form cho bạc ký quỹ online - có ô nhập giá thị trường\r\n                  <>\r\n                    <div className=\"flex items-center space-x-2 mb-2\">\r\n                      <Badge className=\"bg-orange-100 text-orange-800\">Bạc ký quỹ online</Badge>\r\n                      <p className=\"text-xs text-muted-foreground\">\r\n                        {withdrawalMode\r\n                          ? \"Thanh toán 90% giá trị còn lại để rút bạc\"\r\n                          : \"Nhập giá thị trường để tính toán giá tất toán\"}\r\n                      </p>\r\n                    </div>\r\n\r\n                    {withdrawalMode ? (\r\n                      // Hiển thị thông tin thanh toán cho chế độ rút bạc\r\n                      <div className=\"p-3 bg-blue-50 rounded-md\">\r\n                        <p className=\"text-sm font-medium mb-2\">Thông tin thanh toán</p>\r\n                        <div className=\"grid grid-cols-2 gap-2 text-sm\">\r\n                          <div className=\"text-muted-foreground\">Tổng giá trị:</div>\r\n                          <div className=\"font-medium\">{formatCurrency(orderDetail?.totalPrice)}</div>\r\n\r\n                          <div className=\"text-muted-foreground\">Đã đặt cọc:</div>\r\n                          <div className=\"font-medium\">{formatCurrency(orderDetail?.depositPrice)}</div>\r\n\r\n                          <div className=\"text-muted-foreground font-medium\">Cần thanh toán:</div>\r\n                          <div className=\"font-medium text-blue-700\">\r\n                            {formatCurrency((orderDetail?.totalPrice || 0) - (orderDetail?.depositPrice || 0))}\r\n                          </div>\r\n                        </div>\r\n                      </div>\r\n                    ) : (\r\n                      // Hiển thị ô nhập giá thị trường cho chế độ tất toán thông thường\r\n                      <>\r\n                        <FormField\r\n                          control={form.control}\r\n                          name=\"marketPrice\"\r\n                          render={({ field }) => (\r\n                            <FormItem>\r\n                              <FormLabel>Giá thị trường</FormLabel>\r\n                              <FormControl>\r\n                                <div className=\"p-2 flex items-center justify-between\">\r\n                                  <div className=\"space-y-1\">\r\n                                    <span className={`text-base font-medium ${orderDetail?.orderType === OrderType.BUY ? 'text-green-600' : 'text-red-600'}`}>\r\n                                      {formatCurrency(fixedPrice ? fixedPrice.vnd : (currentQuote ?\r\n                                        orderDetail?.orderType === OrderType.BUY ? currentQuote.sellPriceVND : currentQuote.buyPriceVND\r\n                                        : 0\r\n                                      ))}\r\n                                    </span>\r\n                                    <p className=\"text-sm text-muted-foreground\">\r\n                                      ${fixedPrice ? fixedPrice.usd.toFixed(2) : (currentQuote ?\r\n                                        (orderDetail?.orderType === OrderType.BUY ? currentQuote.askPrice : currentQuote.bidPrice).toFixed(2)\r\n                                        : \"0.00\"\r\n                                      )}\r\n                                    </p>\r\n                                  </div>\r\n                                  <span className={`font-bold text-lg ${orderDetail?.orderType === OrderType.BUY ? 'text-green-600' : 'text-red-600'}`}>\r\n                                    {orderDetail?.orderType === OrderType.BUY ?\r\n                                      <TrendingUp className=\"h-5 w-5\" /> :\r\n                                      <TrendingDown className=\"h-5 w-5\" />\r\n                                    }\r\n                                  </span>\r\n                                </div>\r\n                              </FormControl>\r\n                              <FormDescription>\r\n                                {fixedPrice ? \"Giá cố định tại thời điểm tất toán\" : (currentQuote ? \"Giá thị trường tự động cập nhật từ dữ liệu thời gian thực\" : \"Giá thị trường hiện tại của bạc\")}\r\n                              </FormDescription>\r\n                              <FormMessage />\r\n                            </FormItem>\r\n                          )}\r\n                        />\r\n\r\n                        {/* Hiển thị lợi nhuận dự kiến */}\r\n                        {calculatedProfit !== null && (\r\n                          <div className={`p-2 rounded-md mb-2 ${calculatedProfit >= 0 ? 'bg-green-50' : 'bg-red-50'}`}>\r\n                            <p className=\"text-sm font-medium\">\r\n                              Lợi nhuận dự kiến:\r\n                              <span className={`ml-1 font-semibold ${calculatedProfit >= 0 ? 'text-green-600' : 'text-red-600'}`}>\r\n                                {formatCurrency(calculatedProfit)}\r\n                              </span>\r\n                            </p>\r\n                          </div>\r\n                        )}\r\n                      </>\r\n                    )}\r\n\r\n                    <div className=\"space-y-2\">\r\n                      <p className=\"text-sm font-medium\">Thông tin bạc</p>\r\n                      <div className=\"border rounded-md p-3 bg-muted/50\">\r\n                        <div className=\"grid grid-cols-4 gap-2 mb-2 text-xs font-medium text-muted-foreground\">\r\n                          <div>Loại bạc</div>\r\n                          <div>Khối lượng</div>\r\n                          <div>Giá mua</div>\r\n                          <div>Thành tiền</div>\r\n                        </div>\r\n                        {orderDetail?.details?.map((detail, index) => (\r\n                          <div key={index} className=\"grid grid-cols-4 gap-2 py-1.5 border-t text-sm\">\r\n                            <div className=\"font-medium\">{detail.product?.productName || 'Bạc'}</div>\r\n                            <div>{detail.quantity?.toLocaleString() || 0} g</div>\r\n                            <div>\r\n                              {withdrawalMode\r\n                                ? formatCurrency(detail.price)\r\n                                : calculatedPrices[detail.productId]\r\n                                  ? formatCurrency(calculatedPrices[detail.productId])\r\n                                  : formatCurrency(detail.price)}\r\n                            </div>\r\n                            <div>\r\n                              {withdrawalMode\r\n                                ? formatCurrency(detail.price ? detail.price * (detail.quantity || 0) : 0)\r\n                                : calculatedPrices[detail.productId]\r\n                                  ? formatCurrency(calculatedPrices[detail.productId] * (detail.quantity || 0))\r\n                                  : formatCurrency(detail.price ? detail.price * (detail.quantity || 0) : 0)}\r\n                            </div>\r\n                          </div>\r\n                        ))}\r\n                        <div className=\"grid grid-cols-4 gap-2 py-1.5 border-t text-sm font-semibold\">\r\n                          <div>Tổng cộng</div>\r\n                          <div>{totalVolume.toLocaleString()} g</div>\r\n                          <div></div>\r\n                          <div>\r\n                            {withdrawalMode\r\n                              ? formatCurrency(orderDetail?.totalPrice)\r\n                              : Object.keys(calculatedPrices).length > 0\r\n                                ? formatCurrency(\r\n                                  orderDetail?.details?.reduce((sum, detail) => {\r\n                                    return sum + (calculatedPrices[detail.productId] || 0) * (detail.quantity || 0);\r\n                                  }, 0) || 0\r\n                                )\r\n                                : formatCurrency(orderDetail?.totalPrice)}\r\n                          </div>\r\n                        </div>\r\n                      </div>\r\n                    </div>\r\n                  </>\r\n                )}\r\n              </div>\r\n            </form>\r\n          </Form>\r\n        )}\r\n\r\n        <DialogFooter>\r\n          <Button variant=\"outline\" onClick={onClose} disabled={isSubmitting || isLoading}>\r\n            Hủy\r\n          </Button>\r\n          <Button onClick={handleSettlement} disabled={isSubmitting || isLoading}>\r\n            {isSubmitting\r\n              ? 'Đang xử lý...'\r\n              : orderDetail?.businessType === BusinessType.IMMEDIATE_DELIVERY\r\n                ? 'Xác nhận rút bạc'\r\n                : withdrawalMode\r\n                  ? 'Xác nhận chuyển đổi và rút bạc'\r\n                  : 'Xác nhận tất toán'\r\n            }\r\n          </Button>\r\n        </DialogFooter>\r\n      </DialogContent>\r\n    </Dialog>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAQA;AASA;AAEA;AAAA;AACA;AACA;AAAA;AAAA;AACA;AACA;AACA;AACA;AA7BA;;;;;;;;;;;;;;AA2CO,SAAS,gBAAgB,EAAE,SAAS,EAAE,MAAM,EAAE,OAAO,EAAE,SAAS,EAAE,iBAAiB,KAAK,EAAE,aAAa,IAAI,EAAwB;IACxI,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,8SAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,8SAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,8SAAA,CAAA,WAAQ,AAAD,EAAoB;IACjE,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,8SAAA,CAAA,WAAQ,AAAD,EAAU;IACvD,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,8SAAA,CAAA,WAAQ,AAAD,EAA6B,CAAC;IACrF,MAAM,CAAC,4BAA4B,8BAA8B,GAAG,CAAA,GAAA,8SAAA,CAAA,WAAQ,AAAD,EAAiB;IAC5F,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,8SAAA,CAAA,WAAQ,AAAD,EAAiB;IAExE,iEAAiE;IACjE,MAAM,EAAE,YAAY,EAAE,WAAW,EAAE,GAAG,CAAA,GAAA,8JAAA,CAAA,kBAAe,AAAD;IAEpD,gBAAgB;IAChB,MAAM,OAAO,CAAA,GAAA,uPAAA,CAAA,UAAO,AAAD,EAAE;QACnB,eAAe;YACb,aAAa;QACf;IACF;IAEA,+CAA+C;IAC/C,CAAA,GAAA,8SAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,UAAU,WAAW;YACvB,iBAAiB,UAAU,EAAE;YAC7B,+BAA+B;YAC/B,eAAe;YACf,oBAAoB,CAAC;YACrB,8BAA8B;YAC9B,oBAAoB;QACtB;IACF,GAAG;QAAC;QAAQ;KAAU;IAEtB,gDAAgD;IAChD,CAAA,GAAA,8SAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,UAAU,eAAe,gBAAgB;YAC3C,iEAAiE;YACjE,MAAM,kBAAkB,CAAC,YAAY,UAAU,IAAI,CAAC,IAAI,CAAC,YAAY,YAAY,IAAI,CAAC;YACtF,8BAA8B;QAChC;IACF,GAAG;QAAC;QAAQ;QAAa;KAAe;IAExC,4EAA4E;IAC5E,CAAA,GAAA,8SAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,gBAAgB,CAAC,kBAAkB,aAAa,iBAAiB,wKAAA,CAAA,eAAY,CAAC,kBAAkB,EAAE;YACpG,yCAAyC;YACzC,IAAI,YAAY;gBACd,eAAe,WAAW,GAAG,CAAC,QAAQ;gBACtC,wBAAwB,WAAW,GAAG,CAAC,QAAQ,IAAI,WAAW,GAAG;YACnE,OAAO;gBACL,2DAA2D;gBAC3D,MAAM,QAAQ,aAAa,cAAc,wKAAA,CAAA,YAAS,CAAC,GAAG,GAAG,aAAa,WAAW,GAAG,aAAa,YAAY;gBAC7G,MAAM,WAAW,aAAa,cAAc,wKAAA,CAAA,YAAS,CAAC,GAAG,GAAG,aAAa,QAAQ,GAAG,aAAa,QAAQ;gBAEzG,eAAe,SAAS,QAAQ;gBAChC,wBAAwB,SAAS,QAAQ,IAAI;YAC/C;QACF;IACF,GAAG;QAAC;QAAc;QAAa;QAAgB;KAAW;IAE1D,kDAAkD;IAElD,oCAAoC;IACpC,MAAM,0BAA0B,CAAC,OAAe;QAC9C,eAAe;QAEf,IAAI,CAAC,aAAa,WAAW,YAAY,OAAO,CAAC,MAAM,KAAK,GAAG;QAE/D,MAAM,WAAW,WAAW;QAC5B,IAAI,MAAM,aAAa,YAAY,GAAG;YACpC,oBAAoB,CAAC;YACrB,8BAA8B;YAC9B,oBAAoB;YACpB;QACF;QAEA,8DAA8D;QAC9D,MAAM,YAAuC,CAAC;QAE9C,YAAY,OAAO,CAAC,OAAO,CAAC,CAAA;YAC1B,IAAI,OAAO,OAAO,EAAE;gBAClB,qEAAqE;gBACrE,MAAM,UAAU,OAAO,OAAO,CAAC,MAAM,IAAI;gBACzC,MAAM,QAAQ,YAAY,CAAA,GAAA,4HAAA,CAAA,8BAA2B,AAAD,EAAE,UAAU;gBAChE,SAAS,CAAC,OAAO,SAAS,CAAC,GAAG;YAChC;QACF;QAEA,oBAAoB;QAEpB,6BAA6B;QAC7B,MAAM,gBAAgB,YAAY,OAAO,CAAC,MAAM,CAAC,CAAC,KAAK;YACrD,OAAO,MAAM,CAAC,SAAS,CAAC,OAAO,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,QAAQ,IAAI,CAAC;QACzE,GAAG;QAEH,qDAAqD;QACrD,MAAM,sBAAsB,gBAAgB;QAC5C,8BAA8B;QAE9B,sBAAsB;QACtB,MAAM,qBAAqB,YAAY,UAAU,IAAI;QACrD,MAAM,UAAU,GAAG,yBAAyB;QAC5C,MAAM,SAAS,gBAAgB,qBAAqB;QACpD,oBAAoB;IACtB;IAEA,sCAAsC;IACtC,MAAM,mBAAmB,OAAO;QAC9B,IAAI;YACF,aAAa;YACb,MAAM,WAAW,MAAM,0GAAA,CAAA,MAAG,CAAC,GAAG,CAAY,CAAC,YAAY,EAAE,SAAS;YAClE,IAAI,UAAU;gBACZ,eAAe;YACjB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,gCAAgC;YAC9C,wQAAA,CAAA,QAAK,CAAC,KAAK,CAAC,yCAAyC;gBACnD,aAAa,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YACxD;QACF,SAAU;YACR,aAAa;QACf;IACF;IAEA,sBAAsB;IACtB,MAAM,mBAAmB;QACvB,IAAI;YACF,gBAAgB;YAEhB,4BAA4B;YAC5B,MAAM,UAGF,CAAC;YAEL,kEAAkE;YAClE,IAAI,aAAa,iBAAiB,wKAAA,CAAA,eAAY,CAAC,kBAAkB,EAAE;gBACjE,mDAAmD;gBACnD,MAAM,0GAAA,CAAA,MAAG,CAAC,KAAK,CAAC,CAAC,YAAY,EAAE,WAAW,GAAG,iBAAiB,CAAC,EAAE;oBAC/D,qBAAqB;gBACvB;gBACA,wQAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YAChB,OAAO,IAAI,gBAAgB;gBACzB,gEAAgE;gBAChE,MAAM,0GAAA,CAAA,MAAG,CAAC,KAAK,CAAC,CAAC,YAAY,EAAE,WAAW,GAAG,iBAAiB,CAAC,EAAE;oBAC/D,OAAO,aAAa,cAAc;oBAClC,qBAAqB;gBACvB;gBACA,wQAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YAChB,OAAO;gBACL,6CAA6C;gBAC7C,IAAI,YAAY;oBACd,MAAM,0GAAA,CAAA,MAAG,CAAC,KAAK,CAAyB,CAAC,YAAY,EAAE,WAAW,GAAG,iBAAiB,CAAC,EAAE;wBACvF,OAAO,WAAW,GAAG;oBACvB;oBAEA,wQAAA,CAAA,QAAK,CAAC,OAAO,CAAC;gBAChB,OAAO;oBACL,mDAAmD;oBACnD,wQAAA,CAAA,QAAK,CAAC,KAAK,CAAC;oBACZ,gBAAgB;oBAChB;gBACF;YACF;YAEA;YACA;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2BAA2B;YAEzC,8CAA8C;YAC9C,IAAI,aAAa,iBAAiB,wKAAA,CAAA,eAAY,CAAC,kBAAkB,EAAE;gBACjE,wQAAA,CAAA,QAAK,CAAC,KAAK,CAAC,+BAA+B;oBACzC,aAAa,iBAAiB,QAAQ,MAAM,OAAO,GAAG;gBACxD;YACF,OAAO;gBACL,wQAAA,CAAA,QAAK,CAAC,KAAK,CAAC,2BAA2B;oBACrC,aAAa,iBAAiB,QAAQ,MAAM,OAAO,GAAG;gBACxD;YACF;QACF,SAAU;YACR,gBAAgB;QAClB;IACF;IAEA,wCAAwC;IACxC,MAAM,gBAAgB,CAAC;QACrB,OAAQ;YACN,KAAK,wKAAA,CAAA,cAAW,CAAC,SAAS;gBACxB,OAAO;YACT,KAAK,wKAAA,CAAA,cAAW,CAAC,SAAS;gBACxB,OAAO;YACT,KAAK,wKAAA,CAAA,cAAW,CAAC,UAAU;gBACzB,OAAO;YACT,KAAK,wKAAA,CAAA,cAAW,CAAC,SAAS;gBACxB,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,+BAA+B;IAC/B,MAAM,iBAAiB,CAAC;QACtB,OAAQ;YACN,KAAK,wKAAA,CAAA,cAAW,CAAC,SAAS;gBACxB,OAAO;YACT,KAAK,wKAAA,CAAA,cAAW,CAAC,SAAS;gBACxB,OAAO;YACT,KAAK,wKAAA,CAAA,cAAW,CAAC,UAAU;gBACzB,OAAO;YACT,KAAK,wKAAA,CAAA,cAAW,CAAC,SAAS;gBACxB,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,uBAAuB;IACvB,MAAM,cAAc,aAAa,SAAS,OAAO,CAAC,KAAK,SAAW,MAAM,CAAC,OAAO,QAAQ,IAAI,CAAC,GAAG,MAAM;IAEtG,IAAI,CAAC,WAAW,OAAO;IAEvB,qBACE,uVAAC,2HAAA,CAAA,SAAM;QAAC,MAAM;QAAQ,cAAc;kBAClC,cAAA,uVAAC,2HAAA,CAAA,gBAAa;YAAC,WAAU;;8BACvB,uVAAC,2HAAA,CAAA,eAAY;;sCACX,uVAAC,2HAAA,CAAA,cAAW;sCACT,aAAa,iBAAiB,wKAAA,CAAA,eAAY,CAAC,kBAAkB,GAC1D,sBACA,iBACE,kCACA;;;;;;sCAER,uVAAC,2HAAA,CAAA,oBAAiB;sCACf,aAAa,iBAAiB,wKAAA,CAAA,eAAY,CAAC,kBAAkB,GAC1D,kGACA,iBACE,+GACA;;;;;;;;;;;;gBAIT,0BACC,uVAAC;oBAAI,WAAU;;sCACb,uVAAC,qSAAA,CAAA,UAAO;4BAAC,WAAU;;;;;;sCACnB,uVAAC;4BAAK,WAAU;sCAAO;;;;;;;;;;;yCAGzB,uVAAC,yHAAA,CAAA,OAAI;oBAAE,GAAG,IAAI;8BACZ,cAAA,uVAAC;wBAAK,WAAU;;0CACd,uVAAC;gCAAI,WAAU;;kDACb,uVAAC;wCAAI,WAAU;;0DACb,uVAAC;gDAAE,WAAU;0DAA4C;;;;;;0DACzD,uVAAC;gDAAE,WAAU;0DAAyB,aAAa,kBAAkB;;;;;;;;;;;;kDAEvE,uVAAC;wCAAI,WAAU;;0DACb,uVAAC;gDAAE,WAAU;0DAA4C;;;;;;0DACzD,uVAAC,0HAAA,CAAA,QAAK;gDAAC,WAAW,eAAe,aAAa,UAAU,wKAAA,CAAA,cAAW,CAAC,SAAS;0DAC1E,cAAc,aAAa,UAAU,wKAAA,CAAA,cAAW,CAAC,SAAS;;;;;;;;;;;;;;;;;;0CAKjE,uVAAC;gCAAI,WAAU;;kDACb,uVAAC;wCAAI,WAAU;;0DACb,uVAAC;gDAAE,WAAU;0DAA4C;;;;;;0DACzD,uVAAC;gDAAE,WAAU;;oDAAyB,YAAY,cAAc;oDAAG;;;;;;;;;;;;;kDAErE,uVAAC;wCAAI,WAAU;;0DACb,uVAAC;gDAAE,WAAU;0DAA4C;;;;;;0DACzD,uVAAC;gDAAE,WAAU;0DAAyB,CAAA,GAAA,4HAAA,CAAA,iBAAc,AAAD,EAAE,aAAa;;;;;;;;;;;;;;;;;;0CAItE,uVAAC;gCAAI,WAAU;;kDACb,uVAAC;wCAAI,WAAU;;0DACb,uVAAC;gDAAE,WAAU;0DAA4C;;;;;;0DACzD,uVAAC;gDAAE,WAAU;0DAAyB,CAAA,GAAA,4HAAA,CAAA,iBAAc,AAAD,EAAE,aAAa;;;;;;;;;;;;kDAEpE,uVAAC;wCAAI,WAAU;;0DACb,uVAAC;gDAAE,WAAU;0DAA4C;;;;;;0DACzD,uVAAC;gDAAE,WAAU;0DACV,aAAa,iBAAiB,wKAAA,CAAA,eAAY,CAAC,kBAAkB,IAAI,CAAC,6BAC/D,CAAA,GAAA,4HAAA,CAAA,iBAAc,AAAD,EAAE,aAAa,mBAC5B,CAAA,GAAA,4HAAA,CAAA,iBAAc,AAAD,EAAE;;;;;;;;;;;;;;;;;;0CAKzB,uVAAC;gCAAI,WAAU;;kDACb,uVAAC;wCAAI,WAAU;;0DACb,uVAAC;gDAAE,WAAU;0DAA4C;;;;;;0DACzD,uVAAC;gDAAE,WAAU;0DAAyB,CAAA,GAAA,4HAAA,CAAA,iBAAc,AAAD,EAAE,aAAa;;;;;;;;;;;;kDAEpE,uVAAC;wCAAI,WAAU;;0DACb,uVAAC;gDAAE,WAAU;0DAA4C;;;;;;0DACzD,uVAAC;gDAAE,WAAU;0DACV,aAAa,qBACV,IAAI,KAAK,YAAY,kBAAkB,EAAE,kBAAkB,CAAC,WAC5D;;;;;;;;;;;;;;;;;;0CAKV,uVAAC;gCAAI,WAAU;0CACZ,aAAa,iBAAiB,wKAAA,CAAA,eAAY,CAAC,kBAAkB,GAC5D,2DAA2D;8CAC3D,uVAAC;oCAAI,WAAU;;sDACb,uVAAC;4CAAI,WAAU;;8DACb,uVAAC,0HAAA,CAAA,QAAK;oDAAC,WAAU;8DAA4B;;;;;;8DAC7C,uVAAC;oDAAE,WAAU;8DAAgC;;;;;;;;;;;;sDAK/C,uVAAC;4CAAE,WAAU;sDAAsB;;;;;;sDACnC,uVAAC;4CAAI,WAAU;;8DACb,uVAAC;oDAAI,WAAU;;sEACb,uVAAC;sEAAI;;;;;;sEACL,uVAAC;sEAAI;;;;;;sEACL,uVAAC;sEAAI;;;;;;sEACL,uVAAC;sEAAI;;;;;;;;;;;;gDAEN,aAAa,SAAS,IAAI,CAAC,QAAQ,sBAClC,uVAAC;wDAAgB,WAAU;;0EACzB,uVAAC;gEAAI,WAAU;0EAAe,OAAO,OAAO,EAAE,eAAe;;;;;;0EAC7D,uVAAC;;oEAAK,OAAO,QAAQ,EAAE,oBAAoB;oEAAE;;;;;;;0EAC7C,uVAAC;0EAAK,CAAA,GAAA,4HAAA,CAAA,iBAAc,AAAD,EAAE,OAAO,KAAK;;;;;;0EACjC,uVAAC;0EAAK,CAAA,GAAA,4HAAA,CAAA,iBAAc,AAAD,EAAE,OAAO,KAAK,GAAG,OAAO,KAAK,GAAG,CAAC,OAAO,QAAQ,IAAI,CAAC,IAAI;;;;;;;uDAJpE;;;;;8DAOZ,uVAAC;oDAAI,WAAU;;sEACb,uVAAC;sEAAI;;;;;;sEACL,uVAAC;;gEAAK,YAAY,cAAc;gEAAG;;;;;;;sEACnC,uVAAC;;;;;sEACD,uVAAC;sEAAK,CAAA,GAAA,4HAAA,CAAA,iBAAc,AAAD,EAAE,aAAa;;;;;;;;;;;;;;;;;;;;;;;2CAKxC,wDAAwD;8CACxD;;sDACE,uVAAC;4CAAI,WAAU;;8DACb,uVAAC,0HAAA,CAAA,QAAK;oDAAC,WAAU;8DAAgC;;;;;;8DACjD,uVAAC;oDAAE,WAAU;8DACV,iBACG,8CACA;;;;;;;;;;;;wCAIP,iBACC,mDAAmD;sDACnD,uVAAC;4CAAI,WAAU;;8DACb,uVAAC;oDAAE,WAAU;8DAA2B;;;;;;8DACxC,uVAAC;oDAAI,WAAU;;sEACb,uVAAC;4DAAI,WAAU;sEAAwB;;;;;;sEACvC,uVAAC;4DAAI,WAAU;sEAAe,CAAA,GAAA,4HAAA,CAAA,iBAAc,AAAD,EAAE,aAAa;;;;;;sEAE1D,uVAAC;4DAAI,WAAU;sEAAwB;;;;;;sEACvC,uVAAC;4DAAI,WAAU;sEAAe,CAAA,GAAA,4HAAA,CAAA,iBAAc,AAAD,EAAE,aAAa;;;;;;sEAE1D,uVAAC;4DAAI,WAAU;sEAAoC;;;;;;sEACnD,uVAAC;4DAAI,WAAU;sEACZ,CAAA,GAAA,4HAAA,CAAA,iBAAc,AAAD,EAAE,CAAC,aAAa,cAAc,CAAC,IAAI,CAAC,aAAa,gBAAgB,CAAC;;;;;;;;;;;;;;;;;mDAKtF,kEAAkE;sDAClE;;8DACE,uVAAC,yHAAA,CAAA,YAAS;oDACR,SAAS,KAAK,OAAO;oDACrB,MAAK;oDACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,uVAAC,yHAAA,CAAA,WAAQ;;8EACP,uVAAC,yHAAA,CAAA,YAAS;8EAAC;;;;;;8EACX,uVAAC,yHAAA,CAAA,cAAW;8EACV,cAAA,uVAAC;wEAAI,WAAU;;0FACb,uVAAC;gFAAI,WAAU;;kGACb,uVAAC;wFAAK,WAAW,CAAC,sBAAsB,EAAE,aAAa,cAAc,wKAAA,CAAA,YAAS,CAAC,GAAG,GAAG,mBAAmB,gBAAgB;kGACrH,CAAA,GAAA,4HAAA,CAAA,iBAAc,AAAD,EAAE,aAAa,WAAW,GAAG,GAAI,eAC7C,aAAa,cAAc,wKAAA,CAAA,YAAS,CAAC,GAAG,GAAG,aAAa,YAAY,GAAG,aAAa,WAAW,GAC7F;;;;;;kGAGN,uVAAC;wFAAE,WAAU;;4FAAgC;4FACzC,aAAa,WAAW,GAAG,CAAC,OAAO,CAAC,KAAM,eAC1C,CAAC,aAAa,cAAc,wKAAA,CAAA,YAAS,CAAC,GAAG,GAAG,aAAa,QAAQ,GAAG,aAAa,QAAQ,EAAE,OAAO,CAAC,KACjG;;;;;;;;;;;;;0FAIR,uVAAC;gFAAK,WAAW,CAAC,kBAAkB,EAAE,aAAa,cAAc,wKAAA,CAAA,YAAS,CAAC,GAAG,GAAG,mBAAmB,gBAAgB;0FACjH,aAAa,cAAc,wKAAA,CAAA,YAAS,CAAC,GAAG,iBACvC,uVAAC,sSAAA,CAAA,aAAU;oFAAC,WAAU;;;;;2GACtB,uVAAC,0SAAA,CAAA,eAAY;oFAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;8EAKhC,uVAAC,yHAAA,CAAA,kBAAe;8EACb,aAAa,uCAAwC,eAAe,8DAA8D;;;;;;8EAErI,uVAAC,yHAAA,CAAA,cAAW;;;;;;;;;;;;;;;;gDAMjB,qBAAqB,sBACpB,uVAAC;oDAAI,WAAW,CAAC,oBAAoB,EAAE,oBAAoB,IAAI,gBAAgB,aAAa;8DAC1F,cAAA,uVAAC;wDAAE,WAAU;;4DAAsB;0EAEjC,uVAAC;gEAAK,WAAW,CAAC,mBAAmB,EAAE,oBAAoB,IAAI,mBAAmB,gBAAgB;0EAC/F,CAAA,GAAA,4HAAA,CAAA,iBAAc,AAAD,EAAE;;;;;;;;;;;;;;;;;;;sDAQ5B,uVAAC;4CAAI,WAAU;;8DACb,uVAAC;oDAAE,WAAU;8DAAsB;;;;;;8DACnC,uVAAC;oDAAI,WAAU;;sEACb,uVAAC;4DAAI,WAAU;;8EACb,uVAAC;8EAAI;;;;;;8EACL,uVAAC;8EAAI;;;;;;8EACL,uVAAC;8EAAI;;;;;;8EACL,uVAAC;8EAAI;;;;;;;;;;;;wDAEN,aAAa,SAAS,IAAI,CAAC,QAAQ,sBAClC,uVAAC;gEAAgB,WAAU;;kFACzB,uVAAC;wEAAI,WAAU;kFAAe,OAAO,OAAO,EAAE,eAAe;;;;;;kFAC7D,uVAAC;;4EAAK,OAAO,QAAQ,EAAE,oBAAoB;4EAAE;;;;;;;kFAC7C,uVAAC;kFACE,iBACG,CAAA,GAAA,4HAAA,CAAA,iBAAc,AAAD,EAAE,OAAO,KAAK,IAC3B,gBAAgB,CAAC,OAAO,SAAS,CAAC,GAChC,CAAA,GAAA,4HAAA,CAAA,iBAAc,AAAD,EAAE,gBAAgB,CAAC,OAAO,SAAS,CAAC,IACjD,CAAA,GAAA,4HAAA,CAAA,iBAAc,AAAD,EAAE,OAAO,KAAK;;;;;;kFAEnC,uVAAC;kFACE,iBACG,CAAA,GAAA,4HAAA,CAAA,iBAAc,AAAD,EAAE,OAAO,KAAK,GAAG,OAAO,KAAK,GAAG,CAAC,OAAO,QAAQ,IAAI,CAAC,IAAI,KACtE,gBAAgB,CAAC,OAAO,SAAS,CAAC,GAChC,CAAA,GAAA,4HAAA,CAAA,iBAAc,AAAD,EAAE,gBAAgB,CAAC,OAAO,SAAS,CAAC,GAAG,CAAC,OAAO,QAAQ,IAAI,CAAC,KACzE,CAAA,GAAA,4HAAA,CAAA,iBAAc,AAAD,EAAE,OAAO,KAAK,GAAG,OAAO,KAAK,GAAG,CAAC,OAAO,QAAQ,IAAI,CAAC,IAAI;;;;;;;+DAftE;;;;;sEAmBZ,uVAAC;4DAAI,WAAU;;8EACb,uVAAC;8EAAI;;;;;;8EACL,uVAAC;;wEAAK,YAAY,cAAc;wEAAG;;;;;;;8EACnC,uVAAC;;;;;8EACD,uVAAC;8EACE,iBACG,CAAA,GAAA,4HAAA,CAAA,iBAAc,AAAD,EAAE,aAAa,cAC5B,OAAO,IAAI,CAAC,kBAAkB,MAAM,GAAG,IACrC,CAAA,GAAA,4HAAA,CAAA,iBAAc,AAAD,EACb,aAAa,SAAS,OAAO,CAAC,KAAK;wEACjC,OAAO,MAAM,CAAC,gBAAgB,CAAC,OAAO,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,QAAQ,IAAI,CAAC;oEAChF,GAAG,MAAM,KAET,CAAA,GAAA,4HAAA,CAAA,iBAAc,AAAD,EAAE,aAAa;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAYtD,uVAAC,2HAAA,CAAA,eAAY;;sCACX,uVAAC,2HAAA,CAAA,SAAM;4BAAC,SAAQ;4BAAU,SAAS;4BAAS,UAAU,gBAAgB;sCAAW;;;;;;sCAGjF,uVAAC,2HAAA,CAAA,SAAM;4BAAC,SAAS;4BAAkB,UAAU,gBAAgB;sCAC1D,eACG,kBACA,aAAa,iBAAiB,wKAAA,CAAA,eAAY,CAAC,kBAAkB,GAC3D,qBACA,iBACE,mCACA;;;;;;;;;;;;;;;;;;;;;;;AAOpB", "debugId": null}}, {"offset": {"line": 5757, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/bloomgoo/gold-exchange-platform/frontend/components/common/user/order-books/withdrawal-confirm-dialog.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { useState } from 'react';\r\nimport {\r\n  AlertDialog,\r\n  AlertDialogAction,\r\n  AlertDialogCancel,\r\n  AlertDialogContent,\r\n  AlertDialogDescription,\r\n  AlertDialogFooter,\r\n  AlertDialogHeader,\r\n  AlertDialogTitle,\r\n} from '@/components/ui/alert-dialog';\r\nimport { Button } from '@/components/ui/button';\r\n\r\ninterface WithdrawalConfirmDialogProps {\r\n  isOpen: boolean;\r\n  onClose: () => void;\r\n  onConfirmWithdraw: () => void;\r\n  onConfirmNormal: () => void;\r\n}\r\n\r\nexport function WithdrawalConfirmDialog({\r\n  isOpen,\r\n  onClose,\r\n  onConfirmWithdraw,\r\n  onConfirmNormal,\r\n}: WithdrawalConfirmDialogProps) {\r\n  return (\r\n    <AlertDialog open={isOpen} onOpenChange={onClose}>\r\n      <AlertDialogContent className=\"max-w-[400px]\">\r\n        <AlertDialogHeader>\r\n          <AlertDialogTitle>X<PERSON><PERSON> nhận tất toán lệnh</AlertDialogTitle>\r\n          <AlertDialogDescription>\r\n            Bạn có muốn rút bạc không?\r\n          </AlertDialogDescription>\r\n        </AlertDialogHeader>\r\n        <AlertDialogFooter>\r\n          <AlertDialogCancel asChild>\r\n            <Button variant=\"outline\" onClick={onClose}>\r\n              Hủy\r\n            </Button>\r\n          </AlertDialogCancel>\r\n          <Button variant=\"outline\" onClick={onConfirmNormal}>\r\n            Không\r\n          </Button>\r\n          <AlertDialogAction asChild>\r\n            <Button onClick={onConfirmWithdraw}>\r\n              Có\r\n            </Button>\r\n          </AlertDialogAction>\r\n        </AlertDialogFooter>\r\n      </AlertDialogContent>\r\n    </AlertDialog>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAGA;AAUA;AAbA;;;;AAsBO,SAAS,wBAAwB,EACtC,MAAM,EACN,OAAO,EACP,iBAAiB,EACjB,eAAe,EACc;IAC7B,qBACE,uVAAC,oIAAA,CAAA,cAAW;QAAC,MAAM;QAAQ,cAAc;kBACvC,cAAA,uVAAC,oIAAA,CAAA,qBAAkB;YAAC,WAAU;;8BAC5B,uVAAC,oIAAA,CAAA,oBAAiB;;sCAChB,uVAAC,oIAAA,CAAA,mBAAgB;sCAAC;;;;;;sCAClB,uVAAC,oIAAA,CAAA,yBAAsB;sCAAC;;;;;;;;;;;;8BAI1B,uVAAC,oIAAA,CAAA,oBAAiB;;sCAChB,uVAAC,oIAAA,CAAA,oBAAiB;4BAAC,OAAO;sCACxB,cAAA,uVAAC,2HAAA,CAAA,SAAM;gCAAC,SAAQ;gCAAU,SAAS;0CAAS;;;;;;;;;;;sCAI9C,uVAAC,2HAAA,CAAA,SAAM;4BAAC,SAAQ;4BAAU,SAAS;sCAAiB;;;;;;sCAGpD,uVAAC,oIAAA,CAAA,oBAAiB;4BAAC,OAAO;sCACxB,cAAA,uVAAC,2HAAA,CAAA,SAAM;gCAAC,SAAS;0CAAmB;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQhD", "debugId": null}}, {"offset": {"line": 5862, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/bloomgoo/gold-exchange-platform/frontend/components/common/user/order-books/order-books.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { useState, useEffect, useCallback, useRef } from 'react';\r\nimport { Plus, FileDown } from 'lucide-react';\r\nimport { DataTable } from '../../data-table/data-table';\r\nimport { api } from '@/lib/api';\r\nimport { BusinessType, OrderBook, OrderStatus, OrderType } from './type/order-books';\r\nimport { getOrderBookColumns } from './table/order-book-cell';\r\nimport { useReactTable, ColumnFiltersState, PaginationState, SortingState, VisibilityState, getCoreRowModel, getSortedRowModel, getFilteredRowModel, Updater } from '@tanstack/react-table';\r\nimport { TableToolbar } from '../../data-table/table-toolbar';\r\nimport { TableFooter } from '../../data-table/table-footer';\r\nimport { Button } from '@/components/ui/button';\r\nimport { PaginationResponse, SortOrder } from '@/lib/response';\r\nimport { toast } from \"sonner\";\r\nimport { OrderBookDetailSheet } from './order-book-detail-sheet';\r\nimport { OrderBusinessTypeTabs, OrderBusinessTypeFilter } from './components/order-business-type-tabs';\r\nimport { SettlementModal } from './settlement-modal';\r\nimport { WithdrawalConfirmDialog } from './withdrawal-confirm-dialog';\r\nimport {\r\n  AlertDialog,\r\n  AlertDialogAction,\r\n  AlertDialogCancel,\r\n  AlertDialogContent,\r\n  AlertDialogDescription,\r\n  AlertDialogFooter,\r\n  AlertDialogHeader,\r\n  AlertDialogTitle,\r\n} from \"@/components/ui/alert-dialog\";\r\nimport { usePolygonForex } from '@/services/websocket/polygon-forex-socket.service';\r\n\r\n// Define interface for order book statistics response\r\ninterface OrderBookStatistics {\r\n  total: number;\r\n  businessTypeCounts: {\r\n    NORMAL: number;\r\n    IMMEDIATE_DELIVERY: number;\r\n  };\r\n}\r\n\r\nexport default function OrderBooks() {\r\n  const [orderBooks, setOrderBooks] = useState<OrderBook[]>([]);\r\n  const [loading, setLoading] = useState(true);\r\n  const [sorting, setSorting] = useState<SortingState>([]);\r\n  const sortingRef = useRef<SortingState>([]);\r\n  const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([]);\r\n  const [pagination, setPagination] = useState<PaginationState>({\r\n    pageIndex: 0,\r\n    pageSize: 20,\r\n  });\r\n  const [meta, setMeta] = useState({\r\n    page: 1,\r\n    limit: 10,\r\n    itemCount: 0,\r\n    pageCount: 0,\r\n    hasPreviousPage: false,\r\n    hasNextPage: false\r\n  });\r\n  const [globalFilter, setGlobalFilter] = useState(\"\");\r\n\r\n  const [columnVisibility, setColumnVisibility] = useState<VisibilityState>({});\r\n  const [isRefreshing, setIsRefreshing] = useState(false);\r\n  const [selectedOrderBook, setSelectedOrderBook] = useState<OrderBook | null>(null);\r\n  const [showDetail, setShowDetail] = useState(false);\r\n  const [isUpdating, setIsUpdating] = useState(false);\r\n  const [businessTypeFilter, setBusinessTypeFilter] = useState<OrderBusinessTypeFilter>('all');\r\n\r\n  // State để lưu trữ số lượng thống kê từ API statistics\r\n  const [statisticsCounts, setStatisticsCounts] = useState({ all: 0, normal: 0, immediateDelivery: 0 });\r\n  const [showOrderBookFormModal, setShowOrderBookFormModal] = useState(false)\r\n  const [formMode, setFormMode] = useState<'create' | 'update' | 'view'>('create')\r\n  const [showSettlementModal, setShowSettlementModal] = useState(false)\r\n  const [settlementOption, setSettlementOption] = useState<'silver' | 'deposit' | 'cancel' | null>(null)\r\n  const [showWithdrawalConfirm, setShowWithdrawalConfirm] = useState(false)\r\n  const [withdrawalMode, setWithdrawalMode] = useState(false)\r\n  const [fixedSettlementPrice, setFixedSettlementPrice] = useState<{ usd: number; vnd: number; } | null>(null)\r\n\r\n  // Add real-time forex quote\r\n  const { currentQuote, isConnected } = usePolygonForex();\r\n\r\n  // Custom sorting state handler that updates both state and ref\r\n  const handleSortingChange = useCallback((updaterOrValue: Updater<SortingState>) => {\r\n    // Handle both direct value and updater function\r\n    setSorting(updaterOrValue);\r\n\r\n    // Update ref with the new value\r\n    if (typeof updaterOrValue === 'function') {\r\n      // If it's an updater function, call it with current state to get new value\r\n      const newValue = updaterOrValue(sorting);\r\n      sortingRef.current = newValue;\r\n    } else {\r\n      // If it's a direct value\r\n      sortingRef.current = updaterOrValue;\r\n    }\r\n  }, [sorting]);\r\n\r\n  // Xử lý xem chi tiết lệnh\r\n  const handleViewDetail = (orderBook: OrderBook) => {\r\n    setSelectedOrderBook(orderBook);\r\n    setShowDetail(true);\r\n    toast.success(`Đang xem chi tiết lệnh #${orderBook.id}`);\r\n  };\r\n\r\n  // Xử lý chỉnh sửa lệnh\r\n  const handleEdit = (orderBook: OrderBook) => {\r\n    setSelectedOrderBook(orderBook);\r\n    setFormMode('update');\r\n    setShowOrderBookFormModal(true);\r\n    toast.success(`Đang chỉnh sửa lệnh #${orderBook.id}`);\r\n  };\r\n\r\n\r\n  // Xử lý gia hạn thời gian tất toán\r\n  const handleExtendSettlement = async (orderBook: OrderBook) => {\r\n    try {\r\n      setIsUpdating(true);\r\n      await api.patch(`order-books/${orderBook.id}/extend-settlement`);\r\n\r\n      // Tính thời gian tất toán mới (thêm 15 ngày)\r\n      const currentDeadline = new Date(orderBook.settlementDeadline || new Date());\r\n      const newDeadline = new Date(currentDeadline);\r\n      newDeadline.setDate(newDeadline.getDate() + 15);\r\n\r\n      // Cập nhật dữ liệu local\r\n      const updatedOrderBooks = orderBooks.map(ob => {\r\n        if (ob.id === orderBook.id) {\r\n          return {\r\n            ...ob,\r\n            settlementDeadline: newDeadline\r\n          };\r\n        }\r\n        return ob;\r\n      });\r\n\r\n      setOrderBooks(updatedOrderBooks);\r\n      toast.success(`Đã gia hạn thời gian tất toán cho lệnh #${orderBook.id} thêm 15 ngày`);\r\n\r\n      // Refresh để lấy dữ liệu mới nhất\r\n      fetchOrderBooks();\r\n    } catch (error) {\r\n      console.error('Error extending settlement deadline:', error);\r\n      toast.error(\"Không thể gia hạn thời gian tất toán\", {\r\n        description: error instanceof Error ? error.message : \"Vui lòng thử lại sau.\"\r\n      });\r\n    } finally {\r\n      setIsUpdating(false);\r\n    }\r\n  };\r\n\r\n  // Xử lý tất toán lệnh\r\n  const handleSettlement = (orderBook: OrderBook, option: 'silver' | 'deposit' | 'cancel') => {\r\n    setSelectedOrderBook(orderBook);\r\n    setSettlementOption(option);\r\n\r\n    // Lấy giá cố định tại thời điểm mở modal\r\n    if (currentQuote) {\r\n      const price = {\r\n        usd: orderBook.orderType === OrderType.BUY ? currentQuote.askPrice : currentQuote.bidPrice,\r\n        vnd: orderBook.orderType === OrderType.BUY ? currentQuote.sellPriceVND : currentQuote.buyPriceVND\r\n      };\r\n      setFixedSettlementPrice(price);\r\n    }\r\n\r\n    if (option === 'silver') {\r\n      // Kiểm tra nếu là lệnh mua bạc ký quỹ online và khối lượng >= 10\r\n      if (\r\n        orderBook.orderType === OrderType.BUY &&\r\n        orderBook.businessType === BusinessType.NORMAL\r\n      ) {\r\n        // Tính tổng khối lượng bạc\r\n        const totalVolume = orderBook.details?.reduce((sum, detail) => sum + (detail.quantity || 0), 0) || 0;\r\n\r\n        if (totalVolume >= 10) {\r\n          // Hiển thị dialog xác nhận rút bạc\r\n          setShowWithdrawalConfirm(true);\r\n          return;\r\n        }\r\n      }\r\n\r\n      // Nếu không đủ điều kiện hoặc không phải lệnh mua bạc ký quỹ, hiển thị modal tất toán bình thường\r\n      setWithdrawalMode(false);\r\n      setShowSettlementModal(true);\r\n    } else if (option === 'deposit') {\r\n      // TODO: Xử lý khi chọn \"Ký quỹ\"\r\n      toast.info(\"Chức năng ký quỹ đang được phát triển\");\r\n    } else if (option === 'cancel') {\r\n      // Đóng popover khi chọn \"Hủy\"\r\n      setSettlementOption(null);\r\n      setFixedSettlementPrice(null);\r\n    }\r\n  };\r\n\r\n  // Reset fixedSettlementPrice khi đóng modal\r\n  const handleCloseSettlementModal = () => {\r\n    setShowSettlementModal(false);\r\n    setSettlementOption(null);\r\n    setWithdrawalMode(false);\r\n    setFixedSettlementPrice(null);\r\n  };\r\n\r\n  // Xử lý khi người dùng chọn \"Có\" trong dialog xác nhận rút bạc\r\n  const handleConfirmWithdraw = () => {\r\n    setShowWithdrawalConfirm(false);\r\n    setWithdrawalMode(true);\r\n    setShowSettlementModal(true);\r\n  };\r\n\r\n  // Xử lý khi người dùng chọn \"Không\" trong dialog xác nhận rút bạc\r\n  const handleConfirmNormal = () => {\r\n    setShowWithdrawalConfirm(false);\r\n    setWithdrawalMode(false);\r\n    setShowSettlementModal(true);\r\n  };\r\n\r\n  const table = useReactTable({\r\n    data: orderBooks,\r\n    columns: getOrderBookColumns({\r\n      onViewDetail: handleViewDetail,\r\n      onExtendSettlement: handleExtendSettlement,\r\n      onSettlement: handleSettlement,\r\n      currentQuote: currentQuote\r\n    }),\r\n    state: {\r\n      sorting,\r\n      columnFilters,\r\n      pagination,\r\n      columnVisibility,\r\n      globalFilter,\r\n    },\r\n    pageCount: meta.pageCount,\r\n    onSortingChange: handleSortingChange,\r\n    onColumnFiltersChange: setColumnFilters,\r\n    onPaginationChange: setPagination,\r\n    onColumnVisibilityChange: setColumnVisibility,\r\n    onGlobalFilterChange: setGlobalFilter,\r\n    getCoreRowModel: getCoreRowModel(),\r\n    getSortedRowModel: getSortedRowModel(),\r\n    getFilteredRowModel: getFilteredRowModel(),\r\n    manualPagination: true,\r\n    enableSorting: true,\r\n    enableColumnFilters: true,\r\n    enableMultiSort: false,\r\n    manualSorting: false,\r\n    manualFiltering: false,\r\n  });\r\n\r\n  useEffect(() => {\r\n    fetchOrderBooks();\r\n  }, [pagination.pageIndex, pagination.pageSize, businessTypeFilter, sorting, globalFilter]);\r\n\r\n  useEffect(() => {\r\n    fetchStatistics();\r\n  }, []);\r\n\r\n  const fetchOrderBooks = async () => {\r\n    setLoading(true);\r\n    try {\r\n      // Use the ref for sorting\r\n      let sortByParam = '';\r\n      let sortOrderParam = '';\r\n      if (sortingRef.current.length > 0) {\r\n        const firstSort = sortingRef.current[0];\r\n        sortByParam = `&sortBy=${firstSort.id}`;\r\n        sortOrderParam = `&sortOrder=${firstSort.desc ? 'DESC' : 'ASC'}`;\r\n      }\r\n\r\n      // Tạo query string cho search\r\n      let searchParam = '';\r\n\r\n      // Chỉ sử dụng globalFilter cho tìm kiếm\r\n      if (globalFilter) {\r\n        searchParam = `&search=${encodeURIComponent(globalFilter)}`;\r\n      }\r\n\r\n      // Xử lý filter theo loại hình giao dịch\r\n      let filterParam = '';\r\n\r\n      // Thêm filter theo loại hình giao dịch\r\n      if (businessTypeFilter !== 'all') {\r\n        filterParam = `&filter=businessType:${businessTypeFilter}`;\r\n      }\r\n\r\n      const response = await api.get<PaginationResponse<OrderBook>>(\r\n        `order-books?page=${pagination.pageIndex + 1}&limit=${pagination.pageSize}${sortByParam}${sortOrderParam}${searchParam}${filterParam}`\r\n      );\r\n\r\n      if (response && response.data) {\r\n        // Sử dụng dữ liệu trực tiếp từ API mà không cần lọc ở client\r\n        setOrderBooks(response.data);\r\n\r\n        if (response.meta) {\r\n          setMeta(response.meta);\r\n        }\r\n\r\n        // Không cập nhật statisticsCounts ở đây nữa\r\n        // Chúng ta sẽ sử dụng số liệu từ API statistics\r\n      } else {\r\n        setOrderBooks([]);\r\n        setMeta({\r\n          page: 1,\r\n          limit: pagination.pageSize,\r\n          itemCount: 0,\r\n          pageCount: 0,\r\n          hasPreviousPage: false,\r\n          hasNextPage: false\r\n        });\r\n      }\r\n    } catch (error: any) {\r\n      console.error('Error fetching order books:', error);\r\n\r\n      // Kiểm tra lỗi 401/403\r\n      if (error.status === 401) {\r\n        toast.error(\"Bạn chưa đăng nhập hoặc phiên đăng nhập đã hết hạn\");\r\n      } else if (error.status === 403) {\r\n        toast.error(\"Bạn không có quyền truy cập vào tài nguyên này\");\r\n      } else if (error.status === 404) {\r\n        toast.error(\"Không tìm thấy tài nguyên\");\r\n      } else {\r\n        toast.error(\"Không thể tải dữ liệu sổ lệnh\");\r\n      }\r\n\r\n      // Đặt giá trị mặc định khi có lỗi\r\n      setOrderBooks([]);\r\n      setMeta({\r\n        page: 1,\r\n        limit: pagination.pageSize,\r\n        itemCount: 0,\r\n        pageCount: 0,\r\n        hasPreviousPage: false,\r\n        hasNextPage: false\r\n      });\r\n    } finally {\r\n      setLoading(false);\r\n      setIsRefreshing(false);\r\n    }\r\n  };\r\n\r\n  // Hàm lấy thống kê số lượng theo loại hình kinh doanh\r\n  const fetchStatistics = async () => {\r\n    try {\r\n      const response = await api.get<OrderBookStatistics>('order-books/statistics');\r\n      if (response) {\r\n        // Lưu trữ số lượng thống kê tổng cộng\r\n        const statsData = {\r\n          all: response.total || 0,\r\n          normal: response.businessTypeCounts?.NORMAL || 0,\r\n          immediateDelivery: response.businessTypeCounts?.IMMEDIATE_DELIVERY || 0\r\n        };\r\n\r\n        // Cập nhật state thống kê\r\n        setStatisticsCounts(statsData);\r\n\r\n        // Log thông tin để debug\r\n         \r\n      }\r\n    } catch (error) {\r\n      console.error('Error fetching statistics:', error);\r\n      toast.error('Không thể tải thống kê. Vui lòng thử lại sau.');\r\n\r\n      // Đặt giá trị mặc định khi có lỗi\r\n      const defaultStats = {\r\n        all: 0,\r\n        normal: 0,\r\n        immediateDelivery: 0\r\n      };\r\n\r\n      setStatisticsCounts(defaultStats);\r\n    }\r\n  };\r\n\r\n  const handleRefresh = useCallback(async () => {\r\n    setIsRefreshing(true);\r\n    try {\r\n      // Đặt lại các bộ lọc và sắp xếp\r\n      table.resetColumnFilters();\r\n      table.resetSorting();\r\n      setBusinessTypeFilter('all');\r\n      setGlobalFilter('');\r\n\r\n      // Fetch dữ liệu mới và thống kê\r\n      await Promise.all([\r\n        fetchOrderBooks(),\r\n        fetchStatistics()\r\n      ]);\r\n    } finally {\r\n      setTimeout(() => {\r\n        setIsRefreshing(false);\r\n      }, 1000);\r\n    }\r\n  }, [table]);\r\n\r\n  return (\r\n    <div className=\"w-full flex flex-col h-full\">\r\n      {/* Header Navigation */}\r\n      <div className=\"w-full flex justify-between items-center border-b py-1.5 px-6 h-10\">\r\n        <div className=\"flex items-center gap-2\">\r\n          <div className=\"flex items-center gap-1\">\r\n            <span className=\"text-sm font-medium\">Sổ Lệnh</span>\r\n            <span className=\"text-xs bg-accent rounded-md px-1.5 py-1\">{statisticsCounts.all}</span>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Table Toolbar with Status Tabs */}\r\n      <TableToolbar\r\n        table={table}\r\n        globalFilter={globalFilter}\r\n        setGlobalFilter={setGlobalFilter}\r\n        onRefresh={handleRefresh}\r\n        isRefreshing={isRefreshing}\r\n        beforeSearchSlot={\r\n          <OrderBusinessTypeTabs\r\n            currentStatus={businessTypeFilter}\r\n            onStatusChange={setBusinessTypeFilter}\r\n            counts={statisticsCounts}\r\n            className=\"w-fit\"\r\n          />\r\n        }\r\n      />\r\n\r\n      {/* Data Table */}\r\n      <div className=\"flex-1 overflow-auto\">\r\n        <DataTable\r\n          table={table}\r\n          className=\"w-full\"\r\n          isLoading={loading}\r\n        />\r\n      </div>\r\n\r\n      {/* Fixed Pagination Footer */}\r\n      <TableFooter\r\n        table={table}\r\n        totalItems={meta.itemCount}\r\n      />\r\n\r\n      <OrderBookDetailSheet\r\n        orderBook={selectedOrderBook}\r\n        isOpen={showDetail}\r\n        onClose={() => setShowDetail(false)}\r\n        onEdit={handleEdit}\r\n      />\r\n\r\n      {/* Modal tất toán lệnh */}\r\n      <SettlementModal\r\n        orderBook={selectedOrderBook}\r\n        isOpen={showSettlementModal}\r\n        onClose={handleCloseSettlementModal}\r\n        onSuccess={() => {\r\n          handleCloseSettlementModal();\r\n          fetchOrderBooks();\r\n        }}\r\n        withdrawalMode={withdrawalMode}\r\n        fixedPrice={fixedSettlementPrice}\r\n      />\r\n\r\n      {/* Dialog xác nhận rút bạc */}\r\n      <WithdrawalConfirmDialog\r\n        isOpen={showWithdrawalConfirm}\r\n        onClose={() => setShowWithdrawalConfirm(false)}\r\n        onConfirmWithdraw={handleConfirmWithdraw}\r\n        onConfirmNormal={handleConfirmNormal}\r\n      />\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AAEA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AAGA;AACA;AACA;AACA;AACA;AAWA;AA5BA;;;;;;;;;;;;;;;;AAuCe,SAAS;IACtB,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,8SAAA,CAAA,WAAQ,AAAD,EAAe,EAAE;IAC5D,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,8SAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,8SAAA,CAAA,WAAQ,AAAD,EAAgB,EAAE;IACvD,MAAM,aAAa,CAAA,GAAA,8SAAA,CAAA,SAAM,AAAD,EAAgB,EAAE;IAC1C,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,8SAAA,CAAA,WAAQ,AAAD,EAAsB,EAAE;IACzE,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,8SAAA,CAAA,WAAQ,AAAD,EAAmB;QAC5D,WAAW;QACX,UAAU;IACZ;IACA,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,8SAAA,CAAA,WAAQ,AAAD,EAAE;QAC/B,MAAM;QACN,OAAO;QACP,WAAW;QACX,WAAW;QACX,iBAAiB;QACjB,aAAa;IACf;IACA,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,8SAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,8SAAA,CAAA,WAAQ,AAAD,EAAmB,CAAC;IAC3E,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,8SAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,8SAAA,CAAA,WAAQ,AAAD,EAAoB;IAC7E,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,8SAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,8SAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,8SAAA,CAAA,WAAQ,AAAD,EAA2B;IAEtF,uDAAuD;IACvD,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,8SAAA,CAAA,WAAQ,AAAD,EAAE;QAAE,KAAK;QAAG,QAAQ;QAAG,mBAAmB;IAAE;IACnG,MAAM,CAAC,wBAAwB,0BAA0B,GAAG,CAAA,GAAA,8SAAA,CAAA,WAAQ,AAAD,EAAE;IACrE,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,8SAAA,CAAA,WAAQ,AAAD,EAAgC;IACvE,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,8SAAA,CAAA,WAAQ,AAAD,EAAE;IAC/D,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,8SAAA,CAAA,WAAQ,AAAD,EAA0C;IACjG,MAAM,CAAC,uBAAuB,yBAAyB,GAAG,CAAA,GAAA,8SAAA,CAAA,WAAQ,AAAD,EAAE;IACnE,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,8SAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,sBAAsB,wBAAwB,GAAG,CAAA,GAAA,8SAAA,CAAA,WAAQ,AAAD,EAAwC;IAEvG,4BAA4B;IAC5B,MAAM,EAAE,YAAY,EAAE,WAAW,EAAE,GAAG,CAAA,GAAA,8JAAA,CAAA,kBAAe,AAAD;IAEpD,+DAA+D;IAC/D,MAAM,sBAAsB,CAAA,GAAA,8SAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QACvC,gDAAgD;QAChD,WAAW;QAEX,gCAAgC;QAChC,IAAI,OAAO,mBAAmB,YAAY;YACxC,2EAA2E;YAC3E,MAAM,WAAW,eAAe;YAChC,WAAW,OAAO,GAAG;QACvB,OAAO;YACL,yBAAyB;YACzB,WAAW,OAAO,GAAG;QACvB;IACF,GAAG;QAAC;KAAQ;IAEZ,0BAA0B;IAC1B,MAAM,mBAAmB,CAAC;QACxB,qBAAqB;QACrB,cAAc;QACd,wQAAA,CAAA,QAAK,CAAC,OAAO,CAAC,CAAC,wBAAwB,EAAE,UAAU,EAAE,EAAE;IACzD;IAEA,uBAAuB;IACvB,MAAM,aAAa,CAAC;QAClB,qBAAqB;QACrB,YAAY;QACZ,0BAA0B;QAC1B,wQAAA,CAAA,QAAK,CAAC,OAAO,CAAC,CAAC,qBAAqB,EAAE,UAAU,EAAE,EAAE;IACtD;IAGA,mCAAmC;IACnC,MAAM,yBAAyB,OAAO;QACpC,IAAI;YACF,cAAc;YACd,MAAM,0GAAA,CAAA,MAAG,CAAC,KAAK,CAAC,CAAC,YAAY,EAAE,UAAU,EAAE,CAAC,kBAAkB,CAAC;YAE/D,6CAA6C;YAC7C,MAAM,kBAAkB,IAAI,KAAK,UAAU,kBAAkB,IAAI,IAAI;YACrE,MAAM,cAAc,IAAI,KAAK;YAC7B,YAAY,OAAO,CAAC,YAAY,OAAO,KAAK;YAE5C,yBAAyB;YACzB,MAAM,oBAAoB,WAAW,GAAG,CAAC,CAAA;gBACvC,IAAI,GAAG,EAAE,KAAK,UAAU,EAAE,EAAE;oBAC1B,OAAO;wBACL,GAAG,EAAE;wBACL,oBAAoB;oBACtB;gBACF;gBACA,OAAO;YACT;YAEA,cAAc;YACd,wQAAA,CAAA,QAAK,CAAC,OAAO,CAAC,CAAC,wCAAwC,EAAE,UAAU,EAAE,CAAC,aAAa,CAAC;YAEpF,kCAAkC;YAClC;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,wCAAwC;YACtD,wQAAA,CAAA,QAAK,CAAC,KAAK,CAAC,wCAAwC;gBAClD,aAAa,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YACxD;QACF,SAAU;YACR,cAAc;QAChB;IACF;IAEA,sBAAsB;IACtB,MAAM,mBAAmB,CAAC,WAAsB;QAC9C,qBAAqB;QACrB,oBAAoB;QAEpB,yCAAyC;QACzC,IAAI,cAAc;YAChB,MAAM,QAAQ;gBACZ,KAAK,UAAU,SAAS,KAAK,wKAAA,CAAA,YAAS,CAAC,GAAG,GAAG,aAAa,QAAQ,GAAG,aAAa,QAAQ;gBAC1F,KAAK,UAAU,SAAS,KAAK,wKAAA,CAAA,YAAS,CAAC,GAAG,GAAG,aAAa,YAAY,GAAG,aAAa,WAAW;YACnG;YACA,wBAAwB;QAC1B;QAEA,IAAI,WAAW,UAAU;YACvB,iEAAiE;YACjE,IACE,UAAU,SAAS,KAAK,wKAAA,CAAA,YAAS,CAAC,GAAG,IACrC,UAAU,YAAY,KAAK,wKAAA,CAAA,eAAY,CAAC,MAAM,EAC9C;gBACA,2BAA2B;gBAC3B,MAAM,cAAc,UAAU,OAAO,EAAE,OAAO,CAAC,KAAK,SAAW,MAAM,CAAC,OAAO,QAAQ,IAAI,CAAC,GAAG,MAAM;gBAEnG,IAAI,eAAe,IAAI;oBACrB,mCAAmC;oBACnC,yBAAyB;oBACzB;gBACF;YACF;YAEA,kGAAkG;YAClG,kBAAkB;YAClB,uBAAuB;QACzB,OAAO,IAAI,WAAW,WAAW;YAC/B,gCAAgC;YAChC,wQAAA,CAAA,QAAK,CAAC,IAAI,CAAC;QACb,OAAO,IAAI,WAAW,UAAU;YAC9B,8BAA8B;YAC9B,oBAAoB;YACpB,wBAAwB;QAC1B;IACF;IAEA,4CAA4C;IAC5C,MAAM,6BAA6B;QACjC,uBAAuB;QACvB,oBAAoB;QACpB,kBAAkB;QAClB,wBAAwB;IAC1B;IAEA,+DAA+D;IAC/D,MAAM,wBAAwB;QAC5B,yBAAyB;QACzB,kBAAkB;QAClB,uBAAuB;IACzB;IAEA,kEAAkE;IAClE,MAAM,sBAAsB;QAC1B,yBAAyB;QACzB,kBAAkB;QAClB,uBAAuB;IACzB;IAEA,MAAM,QAAQ,CAAA,GAAA,gSAAA,CAAA,gBAAa,AAAD,EAAE;QAC1B,MAAM;QACN,SAAS,CAAA,GAAA,iLAAA,CAAA,sBAAmB,AAAD,EAAE;YAC3B,cAAc;YACd,oBAAoB;YACpB,cAAc;YACd,cAAc;QAChB;QACA,OAAO;YACL;YACA;YACA;YACA;YACA;QACF;QACA,WAAW,KAAK,SAAS;QACzB,iBAAiB;QACjB,uBAAuB;QACvB,oBAAoB;QACpB,0BAA0B;QAC1B,sBAAsB;QACtB,iBAAiB,CAAA,GAAA,8OAAA,CAAA,kBAAe,AAAD;QAC/B,mBAAmB,CAAA,GAAA,8OAAA,CAAA,oBAAiB,AAAD;QACnC,qBAAqB,CAAA,GAAA,8OAAA,CAAA,sBAAmB,AAAD;QACvC,kBAAkB;QAClB,eAAe;QACf,qBAAqB;QACrB,iBAAiB;QACjB,eAAe;QACf,iBAAiB;IACnB;IAEA,CAAA,GAAA,8SAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG;QAAC,WAAW,SAAS;QAAE,WAAW,QAAQ;QAAE;QAAoB;QAAS;KAAa;IAEzF,CAAA,GAAA,8SAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG,EAAE;IAEL,MAAM,kBAAkB;QACtB,WAAW;QACX,IAAI;YACF,0BAA0B;YAC1B,IAAI,cAAc;YAClB,IAAI,iBAAiB;YACrB,IAAI,WAAW,OAAO,CAAC,MAAM,GAAG,GAAG;gBACjC,MAAM,YAAY,WAAW,OAAO,CAAC,EAAE;gBACvC,cAAc,CAAC,QAAQ,EAAE,UAAU,EAAE,EAAE;gBACvC,iBAAiB,CAAC,WAAW,EAAE,UAAU,IAAI,GAAG,SAAS,OAAO;YAClE;YAEA,8BAA8B;YAC9B,IAAI,cAAc;YAElB,wCAAwC;YACxC,IAAI,cAAc;gBAChB,cAAc,CAAC,QAAQ,EAAE,mBAAmB,eAAe;YAC7D;YAEA,wCAAwC;YACxC,IAAI,cAAc;YAElB,uCAAuC;YACvC,IAAI,uBAAuB,OAAO;gBAChC,cAAc,CAAC,qBAAqB,EAAE,oBAAoB;YAC5D;YAEA,MAAM,WAAW,MAAM,0GAAA,CAAA,MAAG,CAAC,GAAG,CAC5B,CAAC,iBAAiB,EAAE,WAAW,SAAS,GAAG,EAAE,OAAO,EAAE,WAAW,QAAQ,GAAG,cAAc,iBAAiB,cAAc,aAAa;YAGxI,IAAI,YAAY,SAAS,IAAI,EAAE;gBAC7B,6DAA6D;gBAC7D,cAAc,SAAS,IAAI;gBAE3B,IAAI,SAAS,IAAI,EAAE;oBACjB,QAAQ,SAAS,IAAI;gBACvB;YAEA,4CAA4C;YAC5C,gDAAgD;YAClD,OAAO;gBACL,cAAc,EAAE;gBAChB,QAAQ;oBACN,MAAM;oBACN,OAAO,WAAW,QAAQ;oBAC1B,WAAW;oBACX,WAAW;oBACX,iBAAiB;oBACjB,aAAa;gBACf;YACF;QACF,EAAE,OAAO,OAAY;YACnB,QAAQ,KAAK,CAAC,+BAA+B;YAE7C,uBAAuB;YACvB,IAAI,MAAM,MAAM,KAAK,KAAK;gBACxB,wQAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACd,OAAO,IAAI,MAAM,MAAM,KAAK,KAAK;gBAC/B,wQAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACd,OAAO,IAAI,MAAM,MAAM,KAAK,KAAK;gBAC/B,wQAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACd,OAAO;gBACL,wQAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACd;YAEA,kCAAkC;YAClC,cAAc,EAAE;YAChB,QAAQ;gBACN,MAAM;gBACN,OAAO,WAAW,QAAQ;gBAC1B,WAAW;gBACX,WAAW;gBACX,iBAAiB;gBACjB,aAAa;YACf;QACF,SAAU;YACR,WAAW;YACX,gBAAgB;QAClB;IACF;IAEA,sDAAsD;IACtD,MAAM,kBAAkB;QACtB,IAAI;YACF,MAAM,WAAW,MAAM,0GAAA,CAAA,MAAG,CAAC,GAAG,CAAsB;YACpD,IAAI,UAAU;gBACZ,sCAAsC;gBACtC,MAAM,YAAY;oBAChB,KAAK,SAAS,KAAK,IAAI;oBACvB,QAAQ,SAAS,kBAAkB,EAAE,UAAU;oBAC/C,mBAAmB,SAAS,kBAAkB,EAAE,sBAAsB;gBACxE;gBAEA,0BAA0B;gBAC1B,oBAAoB;YAEpB,yBAAyB;YAE3B;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,8BAA8B;YAC5C,wQAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YAEZ,kCAAkC;YAClC,MAAM,eAAe;gBACnB,KAAK;gBACL,QAAQ;gBACR,mBAAmB;YACrB;YAEA,oBAAoB;QACtB;IACF;IAEA,MAAM,gBAAgB,CAAA,GAAA,8SAAA,CAAA,cAAW,AAAD,EAAE;QAChC,gBAAgB;QAChB,IAAI;YACF,gCAAgC;YAChC,MAAM,kBAAkB;YACxB,MAAM,YAAY;YAClB,sBAAsB;YACtB,gBAAgB;YAEhB,gCAAgC;YAChC,MAAM,QAAQ,GAAG,CAAC;gBAChB;gBACA;aACD;QACH,SAAU;YACR,WAAW;gBACT,gBAAgB;YAClB,GAAG;QACL;IACF,GAAG;QAAC;KAAM;IAEV,qBACE,uVAAC;QAAI,WAAU;;0BAEb,uVAAC;gBAAI,WAAU;0BACb,cAAA,uVAAC;oBAAI,WAAU;8BACb,cAAA,uVAAC;wBAAI,WAAU;;0CACb,uVAAC;gCAAK,WAAU;0CAAsB;;;;;;0CACtC,uVAAC;gCAAK,WAAU;0CAA4C,iBAAiB,GAAG;;;;;;;;;;;;;;;;;;;;;;0BAMtF,uVAAC,0JAAA,CAAA,eAAY;gBACX,OAAO;gBACP,cAAc;gBACd,iBAAiB;gBACjB,WAAW;gBACX,cAAc;gBACd,gCACE,uVAAC,kMAAA,CAAA,wBAAqB;oBACpB,eAAe;oBACf,gBAAgB;oBAChB,QAAQ;oBACR,WAAU;;;;;;;;;;;0BAMhB,uVAAC;gBAAI,WAAU;0BACb,cAAA,uVAAC,uJAAA,CAAA,YAAS;oBACR,OAAO;oBACP,WAAU;oBACV,WAAW;;;;;;;;;;;0BAKf,uVAAC,yJAAA,CAAA,cAAW;gBACV,OAAO;gBACP,YAAY,KAAK,SAAS;;;;;;0BAG5B,uVAAC,mLAAA,CAAA,uBAAoB;gBACnB,WAAW;gBACX,QAAQ;gBACR,SAAS,IAAM,cAAc;gBAC7B,QAAQ;;;;;;0BAIV,uVAAC,sKAAA,CAAA,kBAAe;gBACd,WAAW;gBACX,QAAQ;gBACR,SAAS;gBACT,WAAW;oBACT;oBACA;gBACF;gBACA,gBAAgB;gBAChB,YAAY;;;;;;0BAId,uVAAC,kLAAA,CAAA,0BAAuB;gBACtB,QAAQ;gBACR,SAAS,IAAM,yBAAyB;gBACxC,mBAAmB;gBACnB,iBAAiB;;;;;;;;;;;;AAIzB", "debugId": null}}]}
"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.WalletService = void 0;
const common_1 = require("@nestjs/common");
const update_wallet_service_1 = require("./update.wallet.service");
const read_wallet_service_1 = require("./read.wallet.service");
let WalletService = class WalletService {
    updateWalletService;
    readWalletService;
    constructor(updateWalletService, readWalletService) {
        this.updateWalletService = updateWalletService;
        this.readWalletService = readWalletService;
    }
    async addBalance(userId, amount, metadata) {
        const wallets = await this.readWalletService.findAll({
            filter: `userId:${userId}`,
            limit: 1,
        });
        if (!wallets.data || wallets.data.length === 0) {
            throw new Error(`Wallet not found for user ${userId}`);
        }
        const wallet = wallets.data[0];
        const newBalance = wallet.balance + amount;
        await this.updateWalletService.update(wallet.id, {
            balance: newBalance,
        });
    }
    async subtractBalance(userId, amount, metadata) {
        const wallets = await this.readWalletService.findAll({
            filter: `userId:${userId}`,
            limit: 1,
        });
        if (!wallets.data || wallets.data.length === 0) {
            throw new Error(`Wallet not found for user ${userId}`);
        }
        const wallet = wallets.data[0];
        if (wallet.balance < amount) {
            throw new Error(`Insufficient balance. Current: ${wallet.balance}, Required: ${amount}`);
        }
        const newBalance = wallet.balance - amount;
        await this.updateWalletService.update(wallet.id, {
            balance: newBalance,
        });
    }
    async getWallet(userId) {
        const wallets = await this.readWalletService.findAll({
            filter: `userId:${userId}`,
            limit: 1,
        });
        return wallets.data && wallets.data.length > 0 ? wallets.data[0] : null;
    }
};
exports.WalletService = WalletService;
exports.WalletService = WalletService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [update_wallet_service_1.UpdateWalletService,
        read_wallet_service_1.ReadWalletService])
], WalletService);
//# sourceMappingURL=wallet.service.js.map
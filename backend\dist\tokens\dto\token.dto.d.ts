import { TokenCategoryDto } from '../../token-categories/dto/token-category.dto';
import { TokenPriceDto } from '../../token-prices/dto/token-price.dto';
import { AssetDto } from '../../token-assets/dto/asset.dto';
import { UserDto } from '../../users/dto/user.dto';
export declare class TokenDto {
    id: string;
    tokenCode: string;
    tokenName: string;
    categoryId: string;
    description?: string;
    decimalPlaces?: number;
    minTradeAmount?: number;
    maxTradeAmount?: number;
    oz?: number;
    isActive: boolean;
    createdAt: Date;
    updatedAt: Date;
    createdBy?: string;
    updatedBy?: string;
    category?: TokenCategoryDto;
    tokenAssets?: AssetDto[];
    tokenPrices?: TokenPriceDto[];
    creator?: UserDto;
    updater?: UserDto;
    deleter?: UserDto;
}

"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SystemConfigModule = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const event_emitter_1 = require("@nestjs/event-emitter");
const system_config_entity_1 = require("./entities/system-config.entity");
const user_entity_1 = require("../users/entities/user.entity");
const base_system_config_service_1 = require("./services/base.system-config.service");
const create_system_config_service_1 = require("./services/create.system-config.service");
const read_system_config_service_1 = require("./services/read.system-config.service");
const update_system_config_service_1 = require("./services/update.system-config.service");
const delete_system_config_service_1 = require("./services/delete.system-config.service");
const system_config_read_controller_1 = require("./controllers/system-config.read.controller");
const system_config_create_controller_1 = require("./controllers/system-config.create.controller");
const system_config_update_controller_1 = require("./controllers/system-config.update.controller");
const system_config_delete_controller_1 = require("./controllers/system-config.delete.controller");
const system_config_public_controller_1 = require("./controllers/system-config.public.controller");
let SystemConfigModule = class SystemConfigModule {
};
exports.SystemConfigModule = SystemConfigModule;
exports.SystemConfigModule = SystemConfigModule = __decorate([
    (0, common_1.Module)({
        imports: [
            typeorm_1.TypeOrmModule.forFeature([system_config_entity_1.SystemConfig, user_entity_1.User]),
            event_emitter_1.EventEmitterModule.forRoot(),
        ],
        controllers: [
            system_config_public_controller_1.SystemConfigPublicController,
            system_config_read_controller_1.SystemConfigReadController,
            system_config_create_controller_1.SystemConfigCreateController,
            system_config_update_controller_1.SystemConfigUpdateController,
            system_config_delete_controller_1.SystemConfigDeleteController,
        ],
        providers: [
            base_system_config_service_1.BaseSystemConfigService,
            create_system_config_service_1.CreateSystemConfigService,
            read_system_config_service_1.ReadSystemConfigService,
            update_system_config_service_1.UpdateSystemConfigService,
            delete_system_config_service_1.DeleteSystemConfigService,
        ],
        exports: [
            create_system_config_service_1.CreateSystemConfigService,
            read_system_config_service_1.ReadSystemConfigService,
            update_system_config_service_1.UpdateSystemConfigService,
            delete_system_config_service_1.DeleteSystemConfigService,
        ],
    })
], SystemConfigModule);
//# sourceMappingURL=system-config.module.js.map
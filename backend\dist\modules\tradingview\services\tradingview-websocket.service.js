"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var TradingViewWebsocketService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.TradingViewWebsocketService = void 0;
const common_1 = require("@nestjs/common");
const websockets_1 = require("@nestjs/websockets");
const socket_io_1 = require("socket.io");
const config_1 = require("@nestjs/config");
const tradingview_data_model_1 = require("../models/tradingview-data.model");
const rate_limiter_service_1 = require("./rate-limiter.service");
let TradingViewWebsocketService = TradingViewWebsocketService_1 = class TradingViewWebsocketService {
    configService;
    rateLimiterService;
    server;
    logger = new common_1.Logger(TradingViewWebsocketService_1.name);
    clients = new Map();
    subscriptions = new Map();
    heartbeatInterval;
    constructor(configService, rateLimiterService) {
        this.configService = configService;
        this.rateLimiterService = rateLimiterService;
    }
    onModuleInit() {
        this.startHeartbeat();
        this.logger.log('Dịch vụ WebSocket TradingView đã được khởi tạo');
        this.logger.log(`Cấu hình WebSocket: namespace=tradingview, path=/socket.io`);
        this.server?.on('connection', (socket) => {
            this.logger.log(`[Server] Client kết nối: ${socket.id}`);
            this.logger.log(`[Server] Handshake: ${JSON.stringify({
                query: socket.handshake.query,
                headers: socket.handshake.headers,
                auth: socket.handshake.auth
            })}`);
            socket.on('disconnect', (reason) => {
                this.logger.log(`[Server] Client ngắt kết nối: ${socket.id}, lý do: ${reason}`);
            });
            socket.on('error', (error) => {
                this.logger.error(`[Server] Lỗi socket: ${socket.id}`, error);
            });
        });
        if (this.server) {
            this.logger.log(`[Server] Socket.IO server đã sẵn sàng với namespace: tradingview`);
        }
        else {
            this.logger.error('[Server] Socket.IO server chưa được khởi tạo');
        }
    }
    onModuleDestroy() {
        if (this.heartbeatInterval) {
            clearInterval(this.heartbeatInterval);
        }
        this.logger.log('Dịch vụ WebSocket TradingView đã bị hủy');
    }
    handleConnection(client) {
        this.clients.set(client.id, client);
        this.subscriptions.set(client.id, new Set());
        this.logger.log(`Client connected: ${client.id}`);
        this.logger.log(`Total clients connected: ${this.clients.size}`);
        this.logger.log(`Client handshake data: ${JSON.stringify({
            auth: client.handshake.auth ? 'exists' : 'not exists',
            query: client.handshake.query ? JSON.stringify(client.handshake.query) : 'not exists',
            headers: client.handshake.headers ? 'exists' : 'not exists',
            address: client.handshake.address
        })}`);
        this.sendToClient(client, {
            type: tradingview_data_model_1.WebSocketMessageType.AUTH,
            data: { message: 'Đã kết nối đến máy chủ WebSocket TradingView' }
        });
    }
    handleDisconnect(client) {
        this.clients.delete(client.id);
        this.subscriptions.delete(client.id);
        this.logger.log(`Client disconnected: ${client.id}`);
    }
    async handleSubscription(client, request) {
        try {
            const rateLimited = await this.rateLimiterService.isRateLimited(client.id);
            if (rateLimited) {
                return {
                    success: false,
                    message: 'Rate limit exceeded. Please try again later.'
                };
            }
            if (!request.symbol) {
                return {
                    success: false,
                    message: 'Cần phải có mã symbol'
                };
            }
            const clientSubscriptions = this.subscriptions.get(client.id);
            if (clientSubscriptions) {
                clientSubscriptions.add(request.symbol);
            }
            else {
                this.subscriptions.set(client.id, new Set([request.symbol]));
            }
            this.logger.log(`Client ${client.id} subscribed to ${request.symbol}`);
            return {
                success: true,
                message: `Đã đăng ký thành công cho ${request.symbol}`,
                subscriptionId: `${client.id}-${request.symbol}`
            };
        }
        catch (error) {
            this.logger.error(`Error handling subscription: ${error.message}`, error.stack);
            return {
                success: false,
                message: 'Đã xảy ra lỗi khi xử lý yêu cầu đăng ký của bạn'
            };
        }
    }
    handleUnsubscription(client, symbol) {
        try {
            const clientSubscriptions = this.subscriptions.get(client.id);
            if (clientSubscriptions) {
                clientSubscriptions.delete(symbol);
                this.logger.log(`Client ${client.id} unsubscribed from ${symbol}`);
            }
            return {
                success: true,
                message: `Đã hủy đăng ký thành công cho ${symbol}`
            };
        }
        catch (error) {
            this.logger.error(`Error handling unsubscription: ${error.message}`, error.stack);
            return {
                success: false,
                message: 'Đã xảy ra lỗi khi xử lý yêu cầu hủy đăng ký của bạn'
            };
        }
    }
    broadcastPriceUpdate(priceData) {
        const message = {
            type: tradingview_data_model_1.WebSocketMessageType.PRICE_UPDATE,
            data: priceData
        };
        let sentCount = 0;
        this.clients.forEach((client, clientId) => {
            const clientSubscriptions = this.subscriptions.get(clientId);
            if (clientSubscriptions && clientSubscriptions.has(priceData.symbol)) {
                this.sendToClient(client, message);
                sentCount++;
            }
        });
        if (sentCount > 0) {
            this.logger.log(`Đã gửi cập nhật giá ${priceData.symbol}: ${priceData.price} đến ${sentCount} client`);
        }
        else {
            this.logger.debug(`Không có client nào đăng ký ${priceData.symbol} để nhận cập nhật giá`);
        }
    }
    sendToClient(client, message) {
        client.emit('message', message);
    }
    sendErrorToClient(client, errorMessage) {
        this.sendToClient(client, {
            type: tradingview_data_model_1.WebSocketMessageType.ERROR,
            data: { message: errorMessage }
        });
    }
    startHeartbeat() {
        const heartbeatInterval = this.configService.get('WEBSOCKET_HEARTBEAT_INTERVAL', 30000);
        this.heartbeatInterval = setInterval(() => {
            this.clients.forEach((client) => {
                client.emit('heartbeat', { timestamp: Date.now() });
            });
        }, heartbeatInterval);
    }
};
exports.TradingViewWebsocketService = TradingViewWebsocketService;
__decorate([
    (0, websockets_1.WebSocketServer)(),
    __metadata("design:type", socket_io_1.Server)
], TradingViewWebsocketService.prototype, "server", void 0);
exports.TradingViewWebsocketService = TradingViewWebsocketService = TradingViewWebsocketService_1 = __decorate([
    (0, websockets_1.WebSocketGateway)({
        namespace: 'tradingview',
        cors: {
            origin: '*',
            credentials: true
        }
    }),
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [config_1.ConfigService,
        rate_limiter_service_1.RateLimiterService])
], TradingViewWebsocketService);
//# sourceMappingURL=tradingview-websocket.service.js.map
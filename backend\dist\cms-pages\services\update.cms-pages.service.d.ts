import { Repository, DataSource } from 'typeorm';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { BaseCmsPagesService } from './base.cms-pages.service';
import { CmsPages, CmsPageStatus } from '../entity/cms-pages.entity';
import { UpdateCmsPageDto } from '../dto/update.cms-page.dto';
import { CmsPageDto } from '../dto/cms-page.dto';
export declare class UpdateCmsPagesService extends BaseCmsPagesService {
    protected readonly pageRepository: Repository<CmsPages>;
    protected readonly dataSource: DataSource;
    protected readonly eventEmitter: EventEmitter2;
    constructor(pageRepository: Repository<CmsPages>, dataSource: DataSource, eventEmitter: EventEmitter2);
    update(id: string, updateDto: UpdateCmsPageDto, userId: string): Promise<CmsPageDto | null>;
    bulkUpdate(updates: Array<{
        id: string;
        data: UpdateCmsPageDto;
    }>, userId: string): Promise<CmsPageDto[]>;
    publish(id: string, userId: string): Promise<CmsPageDto | null>;
    draft(id: string, userId: string): Promise<CmsPageDto | null>;
    updateStatus(id: string, status: CmsPageStatus, userId: string): Promise<CmsPageDto | null>;
    updateTemplate(id: string, template: string, userId: string): Promise<CmsPageDto | null>;
    bulkPublish(ids: string[], userId: string): Promise<CmsPageDto[]>;
    bulkDraft(ids: string[], userId: string): Promise<CmsPageDto[]>;
}

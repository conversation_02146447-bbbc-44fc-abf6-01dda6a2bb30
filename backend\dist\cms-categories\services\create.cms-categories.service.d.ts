import { Repository, DataSource } from 'typeorm';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { BaseCmsCategoriesService } from './base.cms-categories.service';
import { SlugCmsCategoriesService } from './slug.cms-categories.service';
import { CmsCategories } from '../entity/cms-categories.entity';
import { CreateCmsCategoryDto } from '../dto/create.cms-category.dto';
import { CmsCategoryDto } from '../dto/cms-category.dto';
export declare class CreateCmsCategoriesService extends BaseCmsCategoriesService {
    protected readonly categoryRepository: Repository<CmsCategories>;
    protected readonly dataSource: DataSource;
    protected readonly eventEmitter: EventEmitter2;
    private readonly slugService;
    constructor(categoryRepository: Repository<CmsCategories>, dataSource: DataSource, eventEmitter: EventEmitter2, slugService: SlugCmsCategoriesService);
    create(createDto: CreateCmsCategoryDto, userId: string): Promise<CmsCategoryDto>;
    bulkCreate(createDtos: CreateCmsCategoryDto[], userId: string): Promise<CmsCategoryDto[]>;
    duplicate(id: string, userId: string): Promise<CmsCategoryDto>;
}

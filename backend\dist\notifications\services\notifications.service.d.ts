import { NotificationsGateway, Notification, NotificationType } from '../gateways/notifications.gateway';
export declare class NotificationsService {
    private readonly notificationsGateway;
    private readonly logger;
    constructor(notificationsGateway: NotificationsGateway);
    sendNotification(userId: string, type: NotificationType, title: string, message: string, data?: any): Notification;
    sendSystemNotification(userId: string, title: string, message: string, data?: any): Notification;
    sendTransactionNotification(userId: string, title: string, message: string, data?: any): Notification;
    sendUserNotification(userId: string, title: string, message: string, data?: any): Notification;
    sendContractNotification(userId: string, title: string, message: string, data?: any): Notification;
    sendPaymentNotification(userId: string, title: string, message: string, data?: any): Notification;
}

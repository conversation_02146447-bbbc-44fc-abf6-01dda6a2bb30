"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AddReferenceCodeToTransactions1747800000000 = void 0;
class AddReferenceCodeToTransactions1747800000000 {
    async up(queryRunner) {
        await queryRunner.query(`
      ALTER TABLE transactions
      ADD COLUMN IF NOT EXISTS reference_code VARCHAR(100)
    `);
        await queryRunner.query(`
      ALTER TABLE crypto_transactions
      ADD COLUMN IF NOT EXISTS reference_code VARCHAR(100)
    `);
        await queryRunner.query(`
      CREATE INDEX IF NOT EXISTS idx_transactions_reference_code
      ON transactions(reference_code)
    `);
        await queryRunner.query(`
      CREATE INDEX IF NOT EXISTS idx_crypto_transactions_reference_code
      ON crypto_transactions(reference_code)
    `);
    }
    async down(queryRunner) {
        await queryRunner.query(`
      DROP INDEX IF EXISTS idx_transactions_reference_code
    `);
        await queryRunner.query(`
      DROP INDEX IF EXISTS idx_crypto_transactions_reference_code
    `);
        await queryRunner.query(`
      ALTER TABLE transactions
      DROP COLUMN IF EXISTS reference_code
    `);
        await queryRunner.query(`
      ALTER TABLE crypto_transactions
      DROP COLUMN IF EXISTS reference_code
    `);
    }
}
exports.AddReferenceCodeToTransactions1747800000000 = AddReferenceCodeToTransactions1747800000000;
//# sourceMappingURL=1747800000000-AddReferenceCodeToTransactions.js.map
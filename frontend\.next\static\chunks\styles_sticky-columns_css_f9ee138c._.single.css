/* [project]/styles/sticky-columns.css [app-client] (css) */
.table-scroll-container {
  scrollbar-width: thin;
  scrollbar-color: var(--border) var(--muted);
  height: 100%;
  min-height: 200px;
  position: relative;
  overflow: auto;
}

.table-viewport {
  width: 100%;
  height: 100%;
  position: relative;
}

.data-table {
  table-layout: fixed;
  border-collapse: collapse;
  width: 100%;
  min-width: max-content;
}

.sticky-header {
  z-index: 50 !important;
  background-color: var(--background) !important;
  height: auto !important;
  position: sticky !important;
  top: 0 !important;
  box-shadow: 0 2px 4px #0000001a !important;
}

.sticky-header th {
  background-color: var(--background) !important;
  vertical-align: middle !important;
  height: 36px !important;
  padding-top: 8px !important;
  padding-bottom: 8px !important;
}

.sticky-right {
  z-index: 40 !important;
  background-color: var(--background) !important;
  position: sticky !important;
  right: 0 !important;
  box-shadow: -5px 0 5px -5px #0000001a !important;
}

.sticky-left {
  z-index: 40 !important;
  background-color: var(--background) !important;
  position: sticky !important;
  left: 0 !important;
  box-shadow: 5px 0 5px -5px #0000001a !important;
}

.sticky-header th.sticky-right, .sticky-header th.sticky-left {
  z-index: 60 !important;
}

.data-table .checkbox-header, .data-table .checkbox-cell, th.checkbox-header, td.checkbox-cell {
  z-index: 40 !important;
  background-color: var(--background) !important;
  text-align: center !important;
  width: 40px !important;
  min-width: 40px !important;
  max-width: 40px !important;
  padding: 0 !important;
  position: sticky !important;
  left: 0 !important;
  box-shadow: 5px 0 5px -5px #0000001a !important;
}

.data-table .checkbox-header, th.checkbox-header, .sticky-header .checkbox-header {
  z-index: 60 !important;
}

.checkbox-header > div, .checkbox-cell > div {
  justify-content: center !important;
  align-items: center !important;
  width: 100% !important;
  height: 100% !important;
  display: flex !important;
}

th:first-child:not(.checkbox-header), td:first-child:not(.checkbox-cell) {
  z-index: auto !important;
  box-shadow: none !important;
  text-align: left !important;
  background-color: #0000 !important;
  width: auto !important;
  min-width: auto !important;
  max-width: none !important;
  padding: 8px 16px !important;
  position: static !important;
  left: auto !important;
}

th:first-child:not(.checkbox-header) > div, td:first-child:not(.checkbox-cell) > div {
  justify-content: flex-start !important;
  align-items: flex-start !important;
  width: auto !important;
  height: auto !important;
  display: block !important;
}

td:not(.checkbox-cell) {
  padding-right: 8px !important;
}

.checkbox-cell {
  padding-right: 0 !important;
}

.data-table th[data-column-id="select"], .data-table td[data-column-id="select"], .data-table th.checkbox-header, .data-table td.checkbox-cell {
  z-index: 50 !important;
  background-color: var(--background) !important;
  width: 40px !important;
  min-width: 40px !important;
  max-width: 40px !important;
  position: sticky !important;
  left: 0 !important;
  box-shadow: 5px 0 5px -5px #0000001a !important;
}

.data-table th[data-column-id="select"], .data-table th.checkbox-header {
  z-index: 70 !important;
}

.table-scroll-container {
  position: relative;
}

.data-table th[data-column-id="select"], .data-table td[data-column-id="select"] {
  z-index: 70 !important;
  position: sticky !important;
  left: 0 !important;
}

.data-table th.sticky-left:not(.checkbox-header), .data-table td.sticky-left:not(.checkbox-cell) {
  z-index: 50 !important;
  background-color: var(--background) !important;
  position: sticky !important;
  left: 40px !important;
  box-shadow: 5px 0 5px -5px #0000001a !important;
}

.sticky-action-cell {
  justify-content: flex-end;
  align-items: center;
  height: 100%;
  transition: background-color .2s;
  display: flex;
  width: auto !important;
  padding: 0 !important;
}

[id^="actions"] {
  z-index: 40 !important;
  background-color: var(--background) !important;
  width: auto !important;
  min-width: 80px !important;
  max-width: 100px !important;
  position: sticky !important;
  right: 0 !important;
}

.sticky-right:after {
  content: "";
  pointer-events: none;
  background: linear-gradient(to right, #0000, #0000000d);
  width: 8px;
  position: absolute;
  top: 0;
  bottom: 0;
  left: -8px;
}

.table-scroll-container::-webkit-scrollbar {
  background-color: var(--muted);
  width: 10px;
  height: 10px;
}

.table-scroll-container::-webkit-scrollbar-thumb {
  background-color: var(--border);
  border-radius: 5px;
}

.table-scroll-container::-webkit-scrollbar-thumb:hover {
  background-color: var(--muted-foreground);
}

@supports (scrollbar-width: thin) {
  .table-scroll-container {
    scrollbar-width: thin !important;
  }
}

.rounded-md.border.flex.flex-col.flex-1.overflow-hidden, .w-full.flex.flex-col.h-full {
  flex-direction: column;
  height: 100%;
  min-height: 0;
  display: flex;
}

.sticky.bottom-0.bg-background.border-t.mt-auto {
  margin-top: 0 !important;
}

.data-table tbody tr {
  height: 32px !important;
}

.data-table tbody tr:hover {
  background-color: var(--muted) !important;
}

.data-table tbody tr[data-state="selected"] {
  background-color: var(--accent) !important;
}

/*# sourceMappingURL=styles_sticky-columns_css_f9ee138c._.single.css.map*/
(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/components/common/admin/ecom-orders/ecom-orders.tsx [app-client] (ecmascript, next/dynamic entry, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/_b076ade3._.js",
  "static/chunks/node_modules__pnpm_21f78e3b._.js",
  "static/chunks/components_common_admin_ecom-orders_ecom-orders_tsx_6c050c7b._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/components/common/admin/ecom-orders/ecom-orders.tsx [app-client] (ecmascript, next/dynamic entry)");
    });
});
}}),
}]);
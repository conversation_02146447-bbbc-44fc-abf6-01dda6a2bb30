"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var SystemConfigPublicController_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.SystemConfigPublicController = void 0;
const openapi = require("@nestjs/swagger");
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const public_decorator_1 = require("../../common/decorators/public.decorator");
const read_system_config_service_1 = require("../services/read.system-config.service");
const api_response_dto_1 = require("../../common/dto/api-response.dto");
const custom_pagination_query_dto_1 = require("../../common/dto/custom-pagination-query.dto");
let SystemConfigPublicController = SystemConfigPublicController_1 = class SystemConfigPublicController {
    systemConfigService;
    logger = new common_1.Logger(SystemConfigPublicController_1.name);
    constructor(systemConfigService) {
        this.systemConfigService = systemConfigService;
    }
    async getAllConfigs(query) {
        this.logger.debug('Lấy toàn bộ cấu hình hệ thống (public)');
        const { data } = await this.systemConfigService.findAll(query);
        const map = {};
        data.forEach(cfg => {
            if (cfg.configKey && typeof cfg.configValue === 'string') {
                map[cfg.configKey] = cfg.configValue;
            }
        });
        return map;
    }
};
exports.SystemConfigPublicController = SystemConfigPublicController;
__decorate([
    (0, common_1.Get)(),
    (0, swagger_1.ApiOperation)({ summary: 'Lấy toàn bộ cấu hình hệ thống (public, hỗ trợ phân trang, tìm kiếm, lọc)' }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Danh sách cấu hình hệ thống (public)',
        type: api_response_dto_1.ApiResponseDto,
        schema: {
            properties: {
                data: {
                    type: 'array',
                    items: { $ref: '#/components/schemas/SystemConfigDto' },
                },
            },
        },
    }),
    openapi.ApiResponse({ status: 200, type: Object }),
    __param(0, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [custom_pagination_query_dto_1.CustomPaginationQueryDto]),
    __metadata("design:returntype", Promise)
], SystemConfigPublicController.prototype, "getAllConfigs", null);
exports.SystemConfigPublicController = SystemConfigPublicController = SystemConfigPublicController_1 = __decorate([
    (0, swagger_1.ApiTags)('system-configs-public'),
    (0, common_1.Controller)('public/system-configs'),
    (0, public_decorator_1.Public)(),
    __metadata("design:paramtypes", [read_system_config_service_1.ReadSystemConfigService])
], SystemConfigPublicController);
//# sourceMappingURL=system-config.public.controller.js.map
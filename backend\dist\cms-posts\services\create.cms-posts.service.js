"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreateCmsPostsService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const event_emitter_1 = require("@nestjs/event-emitter");
const typeorm_transactional_1 = require("typeorm-transactional");
const base_cms_posts_service_1 = require("./base.cms-posts.service");
const slug_cms_posts_service_1 = require("./slug.cms-posts.service");
const cms_posts_entity_1 = require("../entity/cms-posts.entity");
const create_cms_post_dto_1 = require("../dto/create.cms-post.dto");
const cms_categories_entity_1 = require("../../cms-categories/entity/cms-categories.entity");
const user_entity_1 = require("../../users/entities/user.entity");
let CreateCmsPostsService = class CreateCmsPostsService extends base_cms_posts_service_1.BaseCmsPostsService {
    postRepository;
    categoryRepository;
    userRepository;
    slugService;
    dataSource;
    eventEmitter;
    constructor(postRepository, categoryRepository, userRepository, slugService, dataSource, eventEmitter) {
        super(postRepository, dataSource, eventEmitter);
        this.postRepository = postRepository;
        this.categoryRepository = categoryRepository;
        this.userRepository = userRepository;
        this.slugService = slugService;
        this.dataSource = dataSource;
        this.eventEmitter = eventEmitter;
    }
    async create(createDto, userId) {
        try {
            this.logger.debug(`Đang tạo bài viết CMS: ${JSON.stringify(createDto)}`);
            const postType = createDto.postType || cms_posts_entity_1.CmsPostType.POST;
            const uniqueSlug = await this.slugService.generateUniqueSlugForCreate(createDto.title, postType, createDto.slug);
            const post = this.postRepository.create();
            post.postType = postType;
            post.title = createDto.title;
            post.slug = uniqueSlug;
            post.excerpt = createDto.excerpt;
            post.content = createDto.content;
            post.featuredImageUrl = createDto.featuredImageUrl;
            post.status = createDto.status || cms_posts_entity_1.CmsPostStatus.DRAFT;
            post.publishedAt = createDto.publishedAt ? new Date(createDto.publishedAt) : null;
            post.eventStartDate = createDto.eventStartDate ? new Date(createDto.eventStartDate) : null;
            post.eventEndDate = createDto.eventEndDate ? new Date(createDto.eventEndDate) : null;
            post.eventLocation = createDto.eventLocation;
            post.metaTitle = createDto.metaTitle;
            post.metaDescription = createDto.metaDescription;
            post.metaKeywords = createDto.metaKeywords;
            post.allowComments = createDto.allowComments !== undefined ? createDto.allowComments : true;
            post.viewCount = 0;
            post.createdBy = userId;
            post.updatedBy = userId;
            if (createDto.categoryId) {
                const category = await this.categoryRepository.findOne({
                    where: { id: createDto.categoryId, isDeleted: false },
                });
                if (!category) {
                    throw new common_1.NotFoundException(`Không tìm thấy chuyên mục với ID: ${createDto.categoryId}`);
                }
                post.category = category;
            }
            const author = await this.userRepository.findOne({
                where: { id: createDto.authorId, isDeleted: false },
            });
            if (!author) {
                throw new common_1.NotFoundException(`Không tìm thấy tác giả với ID: ${createDto.authorId}`);
            }
            post.author = author;
            if (createDto.status === cms_posts_entity_1.CmsPostStatus.PUBLISHED && !createDto.publishedAt) {
                post.publishedAt = new Date();
            }
            const savedPost = await this.postRepository.save(post);
            const postDto = this.toDto(savedPost);
            if (!postDto) {
                throw new common_1.InternalServerErrorException('Không thể chuyển đổi entity thành DTO');
            }
            this.eventEmitter.emit(this.EVENT_POST_CREATED, {
                postId: postDto.id,
                userId,
                newData: postDto,
            });
            if (createDto.status === cms_posts_entity_1.CmsPostStatus.PUBLISHED) {
                this.eventEmitter.emit(this.EVENT_POST_PUBLISHED, {
                    postId: postDto.id,
                    userId,
                    publishedAt: post.publishedAt,
                });
            }
            return postDto;
        }
        catch (error) {
            this.logger.error(`Lỗi khi tạo bài viết CMS: ${error.message}`, error.stack);
            if (error instanceof common_1.BadRequestException || error instanceof common_1.ConflictException || error instanceof common_1.NotFoundException) {
                throw error;
            }
            throw new common_1.InternalServerErrorException(`Không thể tạo bài viết CMS: ${error.message}`);
        }
    }
    async bulkCreate(createDtos, userId) {
        try {
            this.logger.debug(`Đang tạo ${createDtos.length} bài viết CMS`);
            const posts = [];
            for (const createDto of createDtos) {
                const post = await this.create(createDto, userId);
                posts.push(post);
            }
            return posts;
        }
        catch (error) {
            this.logger.error(`Lỗi khi tạo nhiều bài viết CMS: ${error.message}`, error.stack);
            if (error instanceof common_1.BadRequestException || error instanceof common_1.ConflictException) {
                throw error;
            }
            throw new common_1.InternalServerErrorException(`Không thể tạo nhiều bài viết CMS: ${error.message}`);
        }
    }
    async duplicate(id, userId) {
        try {
            this.logger.debug(`Đang nhân bản bài viết CMS với ID: ${id}`);
            const post = await this.findById(id, ['category', 'author'], true);
            if (!post) {
                throw new common_1.InternalServerErrorException(`Không tìm thấy bài viết với ID: ${id}`);
            }
            const createDto = {
                postType: post.postType,
                title: `${post.title} (Bản sao)`,
                excerpt: post.excerpt,
                content: post.content,
                featuredImageUrl: post.featuredImageUrl,
                status: cms_posts_entity_1.CmsPostStatus.DRAFT,
                eventStartDate: post.eventStartDate?.toISOString(),
                eventEndDate: post.eventEndDate?.toISOString(),
                eventLocation: post.eventLocation,
                metaTitle: post.metaTitle,
                metaDescription: post.metaDescription,
                metaKeywords: post.metaKeywords,
                allowComments: post.allowComments,
                categoryId: post.category?.id,
                authorId: post.author.id,
            };
            return this.create(createDto, userId);
        }
        catch (error) {
            this.logger.error(`Lỗi khi nhân bản bài viết CMS: ${error.message}`, error.stack);
            throw new common_1.InternalServerErrorException(`Không thể nhân bản bài viết CMS: ${error.message}`);
        }
    }
    async createFromTemplate(templateId, createDto, userId) {
        try {
            this.logger.debug(`Đang tạo bài viết CMS từ template: ${templateId}`);
            const template = await this.findById(templateId, ['category'], true);
            if (!template) {
                throw new common_1.NotFoundException(`Không tìm thấy template với ID: ${templateId}`);
            }
            const mergedDto = {
                ...createDto,
                content: createDto.content || template.content,
                metaTitle: createDto.metaTitle || template.metaTitle,
                metaDescription: createDto.metaDescription || template.metaDescription,
                metaKeywords: createDto.metaKeywords || template.metaKeywords,
                allowComments: createDto.allowComments !== undefined ? createDto.allowComments : template.allowComments,
                categoryId: createDto.categoryId || template.category?.id,
            };
            return this.create(mergedDto, userId);
        }
        catch (error) {
            this.logger.error(`Lỗi khi tạo bài viết từ template: ${error.message}`, error.stack);
            if (error instanceof common_1.NotFoundException) {
                throw error;
            }
            throw new common_1.InternalServerErrorException(`Không thể tạo bài viết từ template: ${error.message}`);
        }
    }
};
exports.CreateCmsPostsService = CreateCmsPostsService;
__decorate([
    (0, typeorm_transactional_1.Transactional)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [create_cms_post_dto_1.CreateCmsPostDto, String]),
    __metadata("design:returntype", Promise)
], CreateCmsPostsService.prototype, "create", null);
__decorate([
    (0, typeorm_transactional_1.Transactional)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Array, String]),
    __metadata("design:returntype", Promise)
], CreateCmsPostsService.prototype, "bulkCreate", null);
__decorate([
    (0, typeorm_transactional_1.Transactional)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String]),
    __metadata("design:returntype", Promise)
], CreateCmsPostsService.prototype, "duplicate", null);
__decorate([
    (0, typeorm_transactional_1.Transactional)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, create_cms_post_dto_1.CreateCmsPostDto, String]),
    __metadata("design:returntype", Promise)
], CreateCmsPostsService.prototype, "createFromTemplate", null);
exports.CreateCmsPostsService = CreateCmsPostsService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(cms_posts_entity_1.CmsPosts)),
    __param(1, (0, typeorm_1.InjectRepository)(cms_categories_entity_1.CmsCategories)),
    __param(2, (0, typeorm_1.InjectRepository)(user_entity_1.User)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository,
        slug_cms_posts_service_1.SlugCmsPostsService,
        typeorm_2.DataSource,
        event_emitter_1.EventEmitter2])
], CreateCmsPostsService);
//# sourceMappingURL=create.cms-posts.service.js.map
"use client";

import React, { createContext, useContext, ReactNode } from "react";
import { useAuth } from "@/hooks/use-auth";
import { useAppLoading } from "@/components/providers/app-loading-provider";
import { AppLayout } from "@/components/app-layout";
import MainLayout from "@/components/layout/main-layout";
import { PageHeader } from "@/components/layout/headers/page-header";

type LayoutContextType = {
  isAdmin: boolean;
};

const LayoutContext = createContext<LayoutContextType | undefined>(undefined);

export function useLayout() {
  const context = useContext(LayoutContext);
  if (context === undefined) {
    throw new Error("useLayout must be used within a LayoutProvider");
  }
  return context;
}

interface LayoutProviderProps {
  children: ReactNode;
  header?: ReactNode;
  title?: string;
  description?: string;
  actions?: ReactNode;
}

export function LayoutProvider({
  children,
  header,
  title,
  description,
  actions,
}: LayoutProviderProps) {
  const { user } = useAuth();
  const { isAppLoading } = useAppLoading();

  // Không render gì khi app đang loading
  if (isAppLoading) {
    return null;
  }

  const isAdmin = user?.roles?.includes("ADMIN");

  // Nếu là admin, sử dụng layout mới
  if (isAdmin) {
    return (
      <LayoutContext.Provider value={{ isAdmin }}>
        <MainLayout
          header={
            header || (
              <PageHeader
                title={title || "Dashboard"}
                description={description}
                actions={actions}
              />
            )
          }
        >
          {children}
        </MainLayout>
      </LayoutContext.Provider>
    );
  }

  // Nếu là user thông thường, sử dụng layout cũ
  return (
    <LayoutContext.Provider value={{ isAdmin: isAdmin ?? false }}>
      <MainLayout
          header={
            header || (
              <PageHeader
                title={title || "Dashboard"}
                description={description}
                actions={actions}
              />
            )
          }
        >
          {children}
        </MainLayout>
    </LayoutContext.Provider>
  );
}

import { BaseActivityLogService } from './base.activity-log.service';
import { ActivityLogDto } from '../dto/activity-log.dto';
import { CreateActivityLogDto } from '../dto/create-activity-log.dto';
export declare class CreateActivityLogService extends BaseActivityLogService {
    create(createActivityLogDto: CreateActivityLogDto, userId?: string): Promise<ActivityLogDto>;
    bulkCreate(createActivityLogDtos: CreateActivityLogDto[], userId?: string): Promise<ActivityLogDto[]>;
}

"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.DeleteCmsPartnersService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const event_emitter_1 = require("@nestjs/event-emitter");
const typeorm_transactional_1 = require("typeorm-transactional");
const cms_partners_entity_1 = require("../entity/cms-partners.entity");
const base_cms_partners_service_1 = require("./base.cms-partners.service");
const dto_1 = require("../dto");
const class_transformer_1 = require("class-transformer");
let DeleteCmsPartnersService = class DeleteCmsPartnersService extends base_cms_partners_service_1.BaseCmsPartnersService {
    partnerRepository;
    eventEmitter;
    constructor(partnerRepository, eventEmitter) {
        super(partnerRepository, eventEmitter);
        this.partnerRepository = partnerRepository;
        this.eventEmitter = eventEmitter;
        this.logger.log('DeleteCmsPartnersService đã được khởi tạo');
    }
    async softDelete(id, userId) {
        this.logger.log(`Attempting to soft delete CMS Partner: ${id}`);
        try {
            const partner = await this.findById(id);
            if (!partner) {
                throw new common_1.NotFoundException(`Không tìm thấy đối tác với ID: ${id}`);
            }
            partner.isDeleted = true;
            partner.deletedAt = new Date();
            partner.deletedBy = userId;
            partner.updatedBy = userId;
            partner.updatedAt = new Date();
            const deletedPartner = await this.partnerRepository.save(partner);
            this.emitEvent('cms-partner.soft-deleted', {
                partnerId: deletedPartner.id,
                partnerName: deletedPartner.name,
                userId,
                timestamp: new Date(),
            });
            this.logOperation('SOFT_DELETE', deletedPartner.id, userId);
            const result = (0, class_transformer_1.plainToClass)(dto_1.CmsPartnerDto, deletedPartner, {
                excludeExtraneousValues: true,
            });
            this.logger.log(`CMS Partner soft deleted successfully: ${id}`);
            return result;
        }
        catch (error) {
            this.logger.error(`Failed to soft delete CMS Partner ${id}: ${error.message}`, error.stack);
            if (error instanceof common_1.NotFoundException) {
                throw error;
            }
            throw new common_1.InternalServerErrorException('Không thể xóa đối tác. Vui lòng thử lại sau.');
        }
    }
    async forceDelete(id, userId) {
        this.logger.log(`Attempting to force delete CMS Partner: ${id}`);
        try {
            const partner = await this.partnerRepository.findOne({
                where: { id },
            });
            if (!partner) {
                throw new common_1.NotFoundException(`Không tìm thấy đối tác với ID: ${id}`);
            }
            const partnerInfo = {
                id: partner.id,
                name: partner.name,
                type: partner.type,
                status: partner.status,
            };
            await this.partnerRepository.remove(partner);
            this.emitEvent('cms-partner.force-deleted', {
                partnerInfo,
                userId,
                timestamp: new Date(),
            });
            this.logOperation('FORCE_DELETE', id, userId);
            this.logger.log(`CMS Partner force deleted successfully: ${id}`);
        }
        catch (error) {
            this.logger.error(`Failed to force delete CMS Partner ${id}: ${error.message}`, error.stack);
            if (error instanceof common_1.NotFoundException) {
                throw error;
            }
            throw new common_1.InternalServerErrorException('Không thể xóa vĩnh viễn đối tác. Vui lòng thử lại sau.');
        }
    }
    async restore(id, userId) {
        this.logger.log(`Attempting to restore CMS Partner: ${id}`);
        try {
            const partner = await this.partnerRepository.findOne({
                where: { id, isDeleted: true },
            });
            if (!partner) {
                throw new common_1.NotFoundException(`Không tìm thấy đối tác đã xóa với ID: ${id}`);
            }
            partner.isDeleted = false;
            partner.deletedAt = null;
            partner.deletedBy = null;
            partner.updatedBy = userId;
            partner.updatedAt = new Date();
            const restoredPartner = await this.partnerRepository.save(partner);
            this.emitEvent('cms-partner.restored', {
                partnerId: restoredPartner.id,
                partnerName: restoredPartner.name,
                userId,
                timestamp: new Date(),
            });
            this.logOperation('RESTORE', restoredPartner.id, userId);
            const result = (0, class_transformer_1.plainToClass)(dto_1.CmsPartnerDto, restoredPartner, {
                excludeExtraneousValues: true,
            });
            this.logger.log(`CMS Partner restored successfully: ${id}`);
            return result;
        }
        catch (error) {
            this.logger.error(`Failed to restore CMS Partner ${id}: ${error.message}`, error.stack);
            if (error instanceof common_1.NotFoundException) {
                throw error;
            }
            throw new common_1.InternalServerErrorException('Không thể khôi phục đối tác. Vui lòng thử lại sau.');
        }
    }
    async bulkSoftDelete(bulkDeleteDto, userId) {
        this.logger.log(`Attempting to bulk soft delete ${bulkDeleteDto.ids.length} CMS Partners`);
        const startTime = Date.now();
        const result = {
            successCount: 0,
            failureCount: 0,
            totalCount: bulkDeleteDto.ids.length,
            successIds: [],
            errors: [],
            processingTime: 0,
        };
        try {
            for (let i = 0; i < bulkDeleteDto.ids.length; i++) {
                try {
                    const id = bulkDeleteDto.ids[i];
                    await this.softDelete(id, userId);
                    result.successCount++;
                    result.successIds.push(id);
                }
                catch (error) {
                    result.failureCount++;
                    result.errors.push({
                        index: i,
                        error: error.message,
                        data: { id: bulkDeleteDto.ids[i] },
                    });
                }
            }
            result.processingTime = Date.now() - startTime;
            this.emitEvent('cms-partner.bulk-soft-deleted', {
                totalCount: result.totalCount,
                successCount: result.successCount,
                failureCount: result.failureCount,
                userId,
                timestamp: new Date(),
            });
            this.logger.log(`Bulk soft delete completed: ${result.successCount}/${result.totalCount} successful`);
            return result;
        }
        catch (error) {
            this.logger.error(`Failed to bulk soft delete CMS Partners: ${error.message}`, error.stack);
            throw new common_1.InternalServerErrorException('Không thể xóa nhiều đối tác. Vui lòng thử lại sau.');
        }
    }
    async bulkForceDelete(bulkForceDeleteDto, userId) {
        this.logger.log(`Attempting to bulk force delete ${bulkForceDeleteDto.ids.length} CMS Partners`);
        const startTime = Date.now();
        const result = {
            successCount: 0,
            failureCount: 0,
            totalCount: bulkForceDeleteDto.ids.length,
            successIds: [],
            errors: [],
            processingTime: 0,
        };
        try {
            for (let i = 0; i < bulkForceDeleteDto.ids.length; i++) {
                try {
                    const id = bulkForceDeleteDto.ids[i];
                    await this.forceDelete(id, userId);
                    result.successCount++;
                    result.successIds.push(id);
                }
                catch (error) {
                    result.failureCount++;
                    result.errors.push({
                        index: i,
                        error: error.message,
                        data: { id: bulkForceDeleteDto.ids[i] },
                    });
                }
            }
            result.processingTime = Date.now() - startTime;
            this.emitEvent('cms-partner.bulk-force-deleted', {
                totalCount: result.totalCount,
                successCount: result.successCount,
                failureCount: result.failureCount,
                userId,
                timestamp: new Date(),
            });
            this.logger.log(`Bulk force delete completed: ${result.successCount}/${result.totalCount} successful`);
            return result;
        }
        catch (error) {
            this.logger.error(`Failed to bulk force delete CMS Partners: ${error.message}`, error.stack);
            throw new common_1.InternalServerErrorException('Không thể xóa vĩnh viễn nhiều đối tác. Vui lòng thử lại sau.');
        }
    }
    async bulkRestore(bulkRestoreDto, userId) {
        this.logger.log(`Attempting to bulk restore ${bulkRestoreDto.ids.length} CMS Partners`);
        const startTime = Date.now();
        const result = {
            successCount: 0,
            failureCount: 0,
            totalCount: bulkRestoreDto.ids.length,
            successIds: [],
            errors: [],
            processingTime: 0,
        };
        try {
            for (let i = 0; i < bulkRestoreDto.ids.length; i++) {
                try {
                    const id = bulkRestoreDto.ids[i];
                    await this.restore(id, userId);
                    result.successCount++;
                    result.successIds.push(id);
                }
                catch (error) {
                    result.failureCount++;
                    result.errors.push({
                        index: i,
                        error: error.message,
                        data: { id: bulkRestoreDto.ids[i] },
                    });
                }
            }
            result.processingTime = Date.now() - startTime;
            this.emitEvent('cms-partner.bulk-restored', {
                totalCount: result.totalCount,
                successCount: result.successCount,
                failureCount: result.failureCount,
                userId,
                timestamp: new Date(),
            });
            this.logger.log(`Bulk restore completed: ${result.successCount}/${result.totalCount} successful`);
            return result;
        }
        catch (error) {
            this.logger.error(`Failed to bulk restore CMS Partners: ${error.message}`, error.stack);
            throw new common_1.InternalServerErrorException('Không thể khôi phục nhiều đối tác. Vui lòng thử lại sau.');
        }
    }
};
exports.DeleteCmsPartnersService = DeleteCmsPartnersService;
__decorate([
    (0, typeorm_transactional_1.Transactional)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String]),
    __metadata("design:returntype", Promise)
], DeleteCmsPartnersService.prototype, "softDelete", null);
__decorate([
    (0, typeorm_transactional_1.Transactional)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String]),
    __metadata("design:returntype", Promise)
], DeleteCmsPartnersService.prototype, "forceDelete", null);
__decorate([
    (0, typeorm_transactional_1.Transactional)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String]),
    __metadata("design:returntype", Promise)
], DeleteCmsPartnersService.prototype, "restore", null);
__decorate([
    (0, typeorm_transactional_1.Transactional)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [dto_1.BulkDeleteCmsPartnersDto, String]),
    __metadata("design:returntype", Promise)
], DeleteCmsPartnersService.prototype, "bulkSoftDelete", null);
__decorate([
    (0, typeorm_transactional_1.Transactional)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [dto_1.BulkForceDeleteCmsPartnersDto, String]),
    __metadata("design:returntype", Promise)
], DeleteCmsPartnersService.prototype, "bulkForceDelete", null);
__decorate([
    (0, typeorm_transactional_1.Transactional)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [dto_1.BulkRestoreCmsPartnersDto, String]),
    __metadata("design:returntype", Promise)
], DeleteCmsPartnersService.prototype, "bulkRestore", null);
exports.DeleteCmsPartnersService = DeleteCmsPartnersService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(cms_partners_entity_1.CmsPartners)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        event_emitter_1.EventEmitter2])
], DeleteCmsPartnersService);
//# sourceMappingURL=delete.cms-partners.service.js.map
"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var PaymentGatewaysService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.PaymentGatewaysService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const typeorm_transactional_1 = require("typeorm-transactional");
const event_emitter_1 = require("@nestjs/event-emitter");
const vnpay_service_1 = require("./services/vnpay.service");
const momo_service_1 = require("./services/momo.service");
const payment_gateway_type_enum_1 = require("./enums/payment-gateway-type.enum");
const transaction_entity_1 = require("../transactions/entities/transaction.entity");
const wallet_entity_1 = require("../wallets/entities/wallet.entity");
const transaction_type_enum_1 = require("../transactions/enums/transaction-type.enum");
const transaction_status_enum_1 = require("../transactions/enums/transaction-status.enum");
const update_wallet_service_1 = require("../wallets/services/update.wallet.service");
const read_wallet_service_1 = require("../wallets/services/read.wallet.service");
let PaymentGatewaysService = PaymentGatewaysService_1 = class PaymentGatewaysService {
    transactionRepository;
    walletRepository;
    vnpayService;
    momoService;
    updateWalletService;
    readWalletService;
    eventEmitter;
    logger = new common_1.Logger(PaymentGatewaysService_1.name);
    constructor(transactionRepository, walletRepository, vnpayService, momoService, updateWalletService, readWalletService, eventEmitter) {
        this.transactionRepository = transactionRepository;
        this.walletRepository = walletRepository;
        this.vnpayService = vnpayService;
        this.momoService = momoService;
        this.updateWalletService = updateWalletService;
        this.readWalletService = readWalletService;
        this.eventEmitter = eventEmitter;
    }
    async createPayment(createPaymentDto, callbacks) {
        try {
            this.logger.log(`Đang tạo thanh toán với cổng: ${createPaymentDto.gatewayType}`);
            await this.validatePaymentRequest(createPaymentDto);
            let gatewayResult;
            switch (createPaymentDto.gatewayType) {
                case payment_gateway_type_enum_1.PaymentGatewayType.VNPAY:
                    gatewayResult =
                        await this.vnpayService.createPaymentUrl(createPaymentDto);
                    break;
                case payment_gateway_type_enum_1.PaymentGatewayType.MOMO:
                    gatewayResult =
                        await this.momoService.createPaymentUrl(createPaymentDto);
                    break;
                default:
                    throw new common_1.BadRequestException(`Cổng thanh toán không được hỗ trợ: ${createPaymentDto.gatewayType}`);
            }
            await this.createPendingTransaction(createPaymentDto.userId, createPaymentDto.walletId, createPaymentDto.amount, gatewayResult.transactionId, createPaymentDto.gatewayType, createPaymentDto.description || 'Nạp tiền vào ví');
            const paymentResult = {
                paymentUrl: gatewayResult.paymentUrl,
                transactionId: gatewayResult.transactionId,
                gatewayType: createPaymentDto.gatewayType,
                expiresAt: new Date(Date.now() + 15 * 60 * 1000),
            };
            if (callbacks?.onPaymentCreated) {
                await callbacks.onPaymentCreated(paymentResult);
            }
            return paymentResult;
        }
        catch (error) {
            this.logger.error(`Lỗi khi tạo thanh toán: ${error.message}`, error.stack);
            throw error;
        }
    }
    async validatePaymentRequest(createPaymentDto) {
        const wallet = await this.readWalletService.findById(createPaymentDto.walletId);
        if (!wallet) {
            throw new common_1.NotFoundException(`Không tìm thấy ví với ID: ${createPaymentDto.walletId}`);
        }
        if (wallet.userId !== createPaymentDto.userId) {
            throw new common_1.BadRequestException('Bạn không có quyền truy cập ví này');
        }
        if (createPaymentDto.amount <= 0) {
            throw new common_1.BadRequestException('Số tiền phải lớn hơn 0');
        }
        const maxAmount = 50000000;
        if (createPaymentDto.amount > maxAmount) {
            throw new common_1.BadRequestException(`Số tiền không được vượt quá ${maxAmount.toLocaleString()} VND`);
        }
    }
    async handleVnpayCallback(params, callbacks) {
        try {
            this.logger.log(`Đang xử lý callback từ VNPAY: ${JSON.stringify(params)}`);
            const verifyResult = await this.vnpayService.verifyReturnUrl(params);
            const callbackResult = {
                isValid: verifyResult.isValid,
                isSuccess: verifyResult.responseCode === '00' &&
                    verifyResult.transactionStatus === '00',
                transactionId: verifyResult.transactionId,
                amount: verifyResult.amount,
                responseCode: verifyResult.responseCode,
                transactionStatus: verifyResult.transactionStatus,
                message: verifyResult.message,
                gatewayTransactionNo: verifyResult.vnpayTransactionNo,
                bankCode: verifyResult.bankCode,
                payDate: verifyResult.payDate,
                gatewayType: payment_gateway_type_enum_1.PaymentGatewayType.VNPAY,
                rawData: params,
            };
            if (callbackResult.isValid && callbackResult.transactionId) {
                if (callbackResult.isSuccess) {
                    await this.processSuccessfulPayment(callbackResult.transactionId, callbackResult.amount, payment_gateway_type_enum_1.PaymentGatewayType.VNPAY, params);
                    if (callbacks?.onPaymentSuccess) {
                        await callbacks.onPaymentSuccess(callbackResult);
                    }
                }
                else {
                    await this.processFailedPayment(callbackResult.transactionId, payment_gateway_type_enum_1.PaymentGatewayType.VNPAY, callbackResult.message || 'Payment failed', params);
                    if (callbacks?.onPaymentFailed) {
                        await callbacks.onPaymentFailed(callbackResult);
                    }
                }
            }
            return callbackResult;
        }
        catch (error) {
            this.logger.error(`Lỗi khi xử lý callback VNPAY: ${error.message}`, error.stack);
            return {
                isValid: false,
                isSuccess: false,
                message: `Lỗi xử lý: ${error.message}`,
                gatewayType: payment_gateway_type_enum_1.PaymentGatewayType.VNPAY,
                rawData: params,
            };
        }
    }
    async handleMomoCallback(params) {
        try {
            this.logger.log(`Đang xử lý callback từ MOMO: ${JSON.stringify(params)}`);
            const verifyResult = await this.momoService.verifyReturnUrl(params);
            if (verifyResult.isValid && verifyResult.transactionId) {
                await this.processSuccessfulPayment(verifyResult.transactionId, verifyResult.amount, payment_gateway_type_enum_1.PaymentGatewayType.MOMO, params);
            }
            return verifyResult;
        }
        catch (error) {
            this.logger.error(`Lỗi khi xử lý callback MOMO: ${error.message}`, error.stack);
            return {
                isValid: false,
                message: `Lỗi xử lý: ${error.message}`,
            };
        }
    }
    async handleMomoIpn(params) {
        try {
            this.logger.log(`Đang xử lý IPN từ MOMO: ${JSON.stringify(params)}`);
            const verifyResult = await this.momoService.handleIpnCallback(params);
            if (verifyResult.isValid && verifyResult.transactionId) {
                await this.processSuccessfulPayment(verifyResult.transactionId, verifyResult.amount, payment_gateway_type_enum_1.PaymentGatewayType.MOMO, params);
                return {
                    ...verifyResult,
                    status: 0,
                    message: 'Thành công',
                };
            }
            return {
                ...verifyResult,
                status: 1,
            };
        }
        catch (error) {
            this.logger.error(`Lỗi khi xử lý IPN MOMO: ${error.message}`, error.stack);
            return {
                isValid: false,
                message: `Lỗi xử lý: ${error.message}`,
                status: 1,
            };
        }
    }
    async createPendingTransaction(userId, walletId, amount, transactionId, gatewayType, description) {
        try {
            const transaction = this.transactionRepository.create({
                userId,
                walletId,
                transactionType: transaction_type_enum_1.TransactionType.CREDIT,
                amount,
                status: transaction_status_enum_1.TransactionStatus.PENDING,
                notes: description,
                referenceCode: `DEP-${transactionId}`,
                gatewayTransactionId: transactionId,
                createdBy: userId,
                gatewayResponse: JSON.stringify({
                    gatewayType,
                    transactionId,
                }),
            });
            await this.transactionRepository.save(transaction);
            this.logger.log(`Đã tạo giao dịch PENDING với ID: ${transaction.id}`);
        }
        catch (error) {
            this.logger.error(`Lỗi khi tạo giao dịch PENDING: ${error.message}`, error.stack);
            throw error;
        }
    }
    async processSuccessfulPayment(transactionId, amount, gatewayType, gatewayResponse) {
        try {
            const transaction = await this.transactionRepository.findOne({
                where: {
                    gatewayTransactionId: transactionId,
                    status: transaction_status_enum_1.TransactionStatus.PENDING,
                },
            });
            if (!transaction) {
                this.logger.warn(`Không tìm thấy giao dịch PENDING với mã: ${transactionId}`);
                return;
            }
            const paymentAmount = amount || Number(transaction.amount);
            const wallet = await this.walletRepository.findOne({
                where: { id: transaction.walletId },
            });
            if (!wallet) {
                this.logger.warn(`Không tìm thấy ví với ID: ${transaction.walletId}`);
                return;
            }
            const currentBalance = Number(wallet.balance);
            const currentAvailableBalance = Number(wallet.availableBalance);
            const newBalance = currentBalance + paymentAmount;
            const newAvailableBalance = currentAvailableBalance + paymentAmount;
            wallet.balance = newBalance;
            wallet.availableBalance = newAvailableBalance;
            wallet.updatedBy = 'system';
            transaction.status = transaction_status_enum_1.TransactionStatus.COMPLETED;
            transaction.updatedAt = new Date();
            transaction.balanceBefore = currentBalance;
            transaction.balanceAfter = newBalance;
            transaction.gatewayResponse = JSON.stringify({
                gatewayType,
                response: gatewayResponse,
            });
            await this.walletRepository.save(wallet);
            await this.transactionRepository.save(transaction);
            this.logger.log(`Đã cập nhật giao dịch ${transaction.id} thành COMPLETED`);
            this.logger.log(`Đã cập nhật số dư ví ${transaction.walletId} từ ${currentBalance} thành ${newBalance}`);
            this.eventEmitter.emit('wallet.balance.changed', {
                walletId: wallet.id,
                oldBalance: currentBalance,
                newBalance: newBalance,
                amount: paymentAmount,
                performedBy: 'system',
            });
            this.eventEmitter.emit('payment.success', {
                userId: transaction.userId,
                walletId: transaction.walletId,
                amount: paymentAmount,
                transactionId: transaction.id,
                gatewayType,
            });
        }
        catch (error) {
            this.logger.error(`Lỗi khi xử lý thanh toán thành công: ${error.message}`, error.stack);
            throw error;
        }
    }
    async processFailedPayment(transactionId, gatewayType, reason, gatewayResponse) {
        try {
            const transaction = await this.transactionRepository.findOne({
                where: {
                    gatewayTransactionId: transactionId,
                    status: transaction_status_enum_1.TransactionStatus.PENDING,
                },
            });
            if (!transaction) {
                this.logger.warn(`Không tìm thấy giao dịch PENDING với mã: ${transactionId}`);
                return;
            }
            transaction.status = transaction_status_enum_1.TransactionStatus.FAILED;
            transaction.updatedAt = new Date();
            transaction.notes = `${transaction.notes} - Lý do thất bại: ${reason}`;
            transaction.gatewayResponse = JSON.stringify({
                gatewayType,
                response: gatewayResponse,
                failureReason: reason,
            });
            await this.transactionRepository.save(transaction);
            this.logger.log(`Đã cập nhật giao dịch ${transaction.id} thành FAILED: ${reason}`);
            this.eventEmitter.emit('payment.failed', {
                userId: transaction.userId,
                walletId: transaction.walletId,
                amount: Number(transaction.amount),
                transactionId: transaction.id,
                gatewayType,
                reason,
            });
        }
        catch (error) {
            this.logger.error(`Lỗi khi xử lý thanh toán thất bại: ${error.message}`, error.stack);
            throw error;
        }
    }
    async handleVnpayIpn(params, callbacks) {
        try {
            this.logger.log(`Đang xử lý IPN từ VNPAY: ${JSON.stringify(params)}`);
            const callbackResult = await this.handleVnpayCallback(params, callbacks);
            if (callbackResult.isValid) {
                return {
                    success: true,
                    code: '00',
                    message: 'Confirm Success',
                    data: callbackResult,
                };
            }
            else {
                return {
                    success: false,
                    code: '97',
                    message: 'Invalid signature',
                    data: callbackResult,
                };
            }
        }
        catch (error) {
            this.logger.error(`Lỗi khi xử lý IPN VNPAY: ${error.message}`, error.stack);
            return {
                success: false,
                code: '99',
                message: 'Unknown error',
            };
        }
    }
    async queryTransaction(gatewayType, queryData) {
        try {
            this.logger.log(`Đang truy vấn giao dịch từ ${gatewayType}: ${queryData.txnRef}`);
            switch (gatewayType) {
                case payment_gateway_type_enum_1.PaymentGatewayType.VNPAY:
                    return await this.vnpayService.queryTransaction(queryData);
                case payment_gateway_type_enum_1.PaymentGatewayType.MOMO:
                    throw new common_1.BadRequestException('MOMO query transaction chưa được hỗ trợ');
                default:
                    throw new common_1.BadRequestException(`Cổng thanh toán không được hỗ trợ: ${gatewayType}`);
            }
        }
        catch (error) {
            this.logger.error(`Lỗi khi truy vấn giao dịch: ${error.message}`, error.stack);
            throw error;
        }
    }
    async refundTransaction(gatewayType, refundData) {
        try {
            this.logger.log(`Đang hoàn tiền từ ${gatewayType}: ${refundData.txnRef}`);
            switch (gatewayType) {
                case payment_gateway_type_enum_1.PaymentGatewayType.VNPAY:
                    return await this.vnpayService.refundTransaction(refundData);
                case payment_gateway_type_enum_1.PaymentGatewayType.MOMO:
                    throw new common_1.BadRequestException('MOMO refund chưa được hỗ trợ');
                default:
                    throw new common_1.BadRequestException(`Cổng thanh toán không được hỗ trợ: ${gatewayType}`);
            }
        }
        catch (error) {
            this.logger.error(`Lỗi khi hoàn tiền: ${error.message}`, error.stack);
            throw error;
        }
    }
};
exports.PaymentGatewaysService = PaymentGatewaysService;
__decorate([
    (0, typeorm_transactional_1.Transactional)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", Promise)
], PaymentGatewaysService.prototype, "handleVnpayCallback", null);
__decorate([
    (0, typeorm_transactional_1.Transactional)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], PaymentGatewaysService.prototype, "handleMomoCallback", null);
__decorate([
    (0, typeorm_transactional_1.Transactional)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], PaymentGatewaysService.prototype, "handleMomoIpn", null);
__decorate([
    (0, typeorm_transactional_1.Transactional)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", Promise)
], PaymentGatewaysService.prototype, "handleVnpayIpn", null);
exports.PaymentGatewaysService = PaymentGatewaysService = PaymentGatewaysService_1 = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(transaction_entity_1.Transaction)),
    __param(1, (0, typeorm_1.InjectRepository)(wallet_entity_1.Wallet)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        typeorm_2.Repository,
        vnpay_service_1.VnpayService,
        momo_service_1.MomoService,
        update_wallet_service_1.UpdateWalletService,
        read_wallet_service_1.ReadWalletService,
        event_emitter_1.EventEmitter2])
], PaymentGatewaysService);
//# sourceMappingURL=payment-gateways.service.js.map
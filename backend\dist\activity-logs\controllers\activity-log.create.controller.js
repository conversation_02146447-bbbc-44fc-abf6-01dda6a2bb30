"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var ActivityLogCreateController_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.ActivityLogCreateController = void 0;
const openapi = require("@nestjs/swagger");
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const jwt_auth_guard_1 = require("../../common/guards/jwt-auth.guard");
const roles_guard_1 = require("../../common/guards/roles.guard");
const roles_decorator_1 = require("../../common/decorators/roles.decorator");
const get_user_decorator_1 = require("../../common/decorators/get-user.decorator");
const default_user_role_enum_1 = require("../../users/enums/default-user-role.enum");
const user_entity_1 = require("../../users/entities/user.entity");
const create_activity_log_service_1 = require("../services/create.activity-log.service");
const create_activity_log_dto_1 = require("../dto/create-activity-log.dto");
const api_response_dto_1 = require("../../common/dto/api-response.dto");
let ActivityLogCreateController = ActivityLogCreateController_1 = class ActivityLogCreateController {
    activityLogService;
    logger = new common_1.Logger(ActivityLogCreateController_1.name);
    constructor(activityLogService) {
        this.activityLogService = activityLogService;
    }
    async create(createActivityLogDto, userId) {
        this.logger.debug(`Đang tạo lịch sử hoạt động mới: ${JSON.stringify(createActivityLogDto)}`);
        return this.activityLogService.create(createActivityLogDto, userId);
    }
    async bulkCreate(createActivityLogDtos, userId) {
        this.logger.debug(`Đang tạo hàng loạt ${createActivityLogDtos.length} lịch sử hoạt động`);
        return this.activityLogService.bulkCreate(createActivityLogDtos, userId);
    }
    async logActivity(createActivityLogDto, user) {
        this.logger.debug(`Đang ghi lại hoạt động của người dùng: ${user.id}`);
        if (!createActivityLogDto.userId) {
            createActivityLogDto.userId = user.id;
        }
        return this.activityLogService.create(createActivityLogDto, user.id);
    }
};
exports.ActivityLogCreateController = ActivityLogCreateController;
__decorate([
    (0, common_1.Post)(),
    (0, roles_decorator_1.Roles)(default_user_role_enum_1.PrimaryUserRole.ADMIN),
    (0, swagger_1.ApiOperation)({ summary: 'Tạo mới lịch sử hoạt động' }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.CREATED,
        description: 'Lịch sử hoạt động đã được tạo thành công.',
        type: api_response_dto_1.ApiResponseDto,
        schema: {
            properties: { data: { $ref: '#/components/schemas/ActivityLogDto' } },
        },
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.BAD_REQUEST,
        description: 'Dữ liệu đầu vào không hợp lệ.',
        type: api_response_dto_1.ApiResponseDto,
        schema: { properties: { data: { type: 'object', nullable: true } } },
    }),
    (0, swagger_1.ApiBody)({ type: create_activity_log_dto_1.CreateActivityLogDto }),
    openapi.ApiResponse({ status: 201, type: require("../dto/activity-log.dto").ActivityLogDto }),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, get_user_decorator_1.GetUser)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [create_activity_log_dto_1.CreateActivityLogDto, String]),
    __metadata("design:returntype", Promise)
], ActivityLogCreateController.prototype, "create", null);
__decorate([
    (0, common_1.Post)('bulk'),
    (0, roles_decorator_1.Roles)(default_user_role_enum_1.PrimaryUserRole.ADMIN),
    (0, swagger_1.ApiOperation)({ summary: 'Tạo hàng loạt lịch sử hoạt động' }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.CREATED,
        description: 'Các lịch sử hoạt động đã được tạo thành công.',
        type: api_response_dto_1.ApiResponseDto,
        schema: {
            properties: { data: { type: 'array', items: { $ref: '#/components/schemas/ActivityLogDto' } } },
        },
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.BAD_REQUEST,
        description: 'Dữ liệu đầu vào không hợp lệ.',
        type: api_response_dto_1.ApiResponseDto,
        schema: { properties: { data: { type: 'object', nullable: true } } },
    }),
    (0, swagger_1.ApiBody)({ type: [create_activity_log_dto_1.CreateActivityLogDto] }),
    openapi.ApiResponse({ status: 201, type: [require("../dto/activity-log.dto").ActivityLogDto] }),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, get_user_decorator_1.GetUser)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Array, String]),
    __metadata("design:returntype", Promise)
], ActivityLogCreateController.prototype, "bulkCreate", null);
__decorate([
    (0, common_1.Post)('log-activity'),
    (0, swagger_1.ApiOperation)({ summary: 'Ghi lại hoạt động của người dùng' }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.CREATED,
        description: 'Hoạt động đã được ghi lại thành công.',
        type: api_response_dto_1.ApiResponseDto,
        schema: {
            properties: { data: { $ref: '#/components/schemas/ActivityLogDto' } },
        },
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.BAD_REQUEST,
        description: 'Dữ liệu đầu vào không hợp lệ.',
        type: api_response_dto_1.ApiResponseDto,
        schema: { properties: { data: { type: 'object', nullable: true } } },
    }),
    (0, swagger_1.ApiBody)({ type: create_activity_log_dto_1.CreateActivityLogDto }),
    openapi.ApiResponse({ status: 201, type: require("../dto/activity-log.dto").ActivityLogDto }),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, get_user_decorator_1.GetUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [create_activity_log_dto_1.CreateActivityLogDto,
        user_entity_1.User]),
    __metadata("design:returntype", Promise)
], ActivityLogCreateController.prototype, "logActivity", null);
exports.ActivityLogCreateController = ActivityLogCreateController = ActivityLogCreateController_1 = __decorate([
    (0, swagger_1.ApiTags)('activity-logs'),
    (0, swagger_1.ApiBearerAuth)(),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard, roles_guard_1.RolesGuard),
    (0, common_1.Controller)('activity-logs'),
    __metadata("design:paramtypes", [create_activity_log_service_1.CreateActivityLogService])
], ActivityLogCreateController);
//# sourceMappingURL=activity-log.create.controller.js.map
"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var DeleteCmsPartnersController_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.DeleteCmsPartnersController = void 0;
const openapi = require("@nestjs/swagger");
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const permissions_decorator_1 = require("../../common/decorators/permissions.decorator");
const roles_decorator_1 = require("../../common/decorators/roles.decorator");
const custom_pagination_meta_dto_1 = require("../../common/dto/custom-pagination-meta.dto");
const custom_pagination_query_dto_1 = require("../../common/dto/custom-pagination-query.dto");
const pagination_response_dto_1 = require("../../common/dto/pagination-response.dto");
const jwt_auth_guard_1 = require("../../common/guards/jwt-auth.guard");
const dto_1 = require("../dto");
const delete_cms_partners_service_1 = require("../services/delete.cms-partners.service");
const read_cms_partners_service_1 = require("../services/read.cms-partners.service");
let DeleteCmsPartnersController = DeleteCmsPartnersController_1 = class DeleteCmsPartnersController {
    deleteCmsPartnersService;
    readCmsPartnersService;
    logger = new common_1.Logger(DeleteCmsPartnersController_1.name);
    constructor(deleteCmsPartnersService, readCmsPartnersService) {
        this.deleteCmsPartnersService = deleteCmsPartnersService;
        this.readCmsPartnersService = readCmsPartnersService;
    }
    async softDelete(id, req) {
        this.logger.log(`Soft deleting CMS Partner: ${id}`);
        return this.deleteCmsPartnersService.softDelete(id, req.user.id);
    }
    async restore(id, req) {
        this.logger.log(`Restoring CMS Partner: ${id}`);
        return this.deleteCmsPartnersService.restore(id, req.user.id);
    }
    async forceDelete(id, req) {
        this.logger.log(`Force deleting CMS Partner: ${id}`);
        return this.deleteCmsPartnersService.forceDelete(id, req.user.id);
    }
    async bulkSoftDelete(bulkDeleteDto, req) {
        this.logger.log(`Bulk soft deleting ${bulkDeleteDto.ids.length} CMS Partners`);
        return this.deleteCmsPartnersService.bulkSoftDelete(bulkDeleteDto, req.user.id);
    }
    async bulkRestore(bulkRestoreDto, req) {
        this.logger.log(`Bulk restoring ${bulkRestoreDto.ids.length} CMS Partners`);
        return this.deleteCmsPartnersService.bulkRestore(bulkRestoreDto, req.user.id);
    }
    async bulkForceDelete(bulkForceDeleteDto, req) {
        this.logger.log(`Bulk force deleting ${bulkForceDeleteDto.ids.length} CMS Partners`);
        return this.deleteCmsPartnersService.bulkForceDelete(bulkForceDeleteDto, req.user.id);
    }
    async getDeleted(paginationQuery, req) {
        this.logger.log(`Getting deleted CMS Partners for user: ${req.user.id}`);
        const { data, total } = await this.readCmsPartnersService.getDeleted(paginationQuery);
        const meta = new custom_pagination_meta_dto_1.CustomPageMetaDto({
            pageQueryDto: paginationQuery,
            itemCount: total,
        });
        return new pagination_response_dto_1.PaginationResponseDto(data, meta);
    }
};
exports.DeleteCmsPartnersController = DeleteCmsPartnersController;
__decorate([
    (0, common_1.Delete)(':id'),
    (0, common_1.HttpCode)(common_1.HttpStatus.OK),
    (0, roles_decorator_1.Roles)('ADMIN', 'EDITOR'),
    (0, permissions_decorator_1.Permissions)('cms_partners:delete'),
    (0, swagger_1.ApiOperation)({
        summary: 'Xóa mềm đối tác',
        description: 'Xóa mềm một đối tác (có thể khôi phục sau)',
    }),
    (0, swagger_1.ApiParam)({
        name: 'id',
        description: 'ID của đối tác cần xóa',
        example: '123e4567-e89b-12d3-a456-426614174000',
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Đối tác đã được xóa mềm thành công',
        type: dto_1.CmsPartnerDto,
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.NOT_FOUND,
        description: 'Không tìm thấy đối tác',
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.UNAUTHORIZED,
        description: 'Không có quyền truy cập',
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.FORBIDDEN,
        description: 'Không có quyền thực hiện thao tác này',
    }),
    openapi.ApiResponse({ status: common_1.HttpStatus.OK, type: require("../dto/cms-partner.dto").CmsPartnerDto }),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], DeleteCmsPartnersController.prototype, "softDelete", null);
__decorate([
    (0, common_1.Patch)(':id/restore'),
    (0, roles_decorator_1.Roles)('ADMIN', 'EDITOR'),
    (0, permissions_decorator_1.Permissions)('cms_partners:delete'),
    (0, swagger_1.ApiOperation)({
        summary: 'Khôi phục đối tác đã xóa',
        description: 'Khôi phục một đối tác đã bị xóa mềm',
    }),
    (0, swagger_1.ApiParam)({
        name: 'id',
        description: 'ID của đối tác cần khôi phục',
        example: '123e4567-e89b-12d3-a456-426614174000',
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Đối tác đã được khôi phục thành công',
        type: dto_1.CmsPartnerDto,
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.NOT_FOUND,
        description: 'Không tìm thấy đối tác đã xóa',
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.UNAUTHORIZED,
        description: 'Không có quyền truy cập',
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.FORBIDDEN,
        description: 'Không có quyền thực hiện thao tác này',
    }),
    openapi.ApiResponse({ status: 200, type: require("../dto/cms-partner.dto").CmsPartnerDto }),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], DeleteCmsPartnersController.prototype, "restore", null);
__decorate([
    (0, common_1.Delete)(':id/force'),
    (0, common_1.HttpCode)(common_1.HttpStatus.NO_CONTENT),
    (0, roles_decorator_1.Roles)('ADMIN'),
    (0, permissions_decorator_1.Permissions)('cms_partners:force_delete'),
    (0, swagger_1.ApiOperation)({
        summary: 'Xóa cứng đối tác',
        description: 'Xóa vĩnh viễn một đối tác (không thể khôi phục)',
    }),
    (0, swagger_1.ApiParam)({
        name: 'id',
        description: 'ID của đối tác cần xóa vĩnh viễn',
        example: '123e4567-e89b-12d3-a456-426614174000',
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.NO_CONTENT,
        description: 'Đối tác đã được xóa vĩnh viễn thành công',
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.NOT_FOUND,
        description: 'Không tìm thấy đối tác',
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.UNAUTHORIZED,
        description: 'Không có quyền truy cập',
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.FORBIDDEN,
        description: 'Không có quyền thực hiện thao tác này',
    }),
    openapi.ApiResponse({ status: common_1.HttpStatus.NO_CONTENT }),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], DeleteCmsPartnersController.prototype, "forceDelete", null);
__decorate([
    (0, common_1.Delete)('bulk'),
    (0, common_1.HttpCode)(common_1.HttpStatus.OK),
    (0, roles_decorator_1.Roles)('ADMIN', 'EDITOR'),
    (0, permissions_decorator_1.Permissions)('cms_partners:delete'),
    (0, common_1.UsePipes)(new common_1.ValidationPipe({ transform: true, whitelist: true })),
    (0, swagger_1.ApiOperation)({
        summary: 'Xóa mềm nhiều đối tác',
        description: 'Xóa mềm nhiều đối tác cùng lúc',
    }),
    (0, swagger_1.ApiBody)({
        type: dto_1.BulkDeleteCmsPartnersDto,
        description: 'Danh sách ID đối tác cần xóa',
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Kết quả xóa mềm nhiều đối tác',
        type: dto_1.BulkOperationResponseDto,
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.BAD_REQUEST,
        description: 'Dữ liệu đầu vào không hợp lệ',
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.UNAUTHORIZED,
        description: 'Không có quyền truy cập',
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.FORBIDDEN,
        description: 'Không có quyền thực hiện thao tác này',
    }),
    openapi.ApiResponse({ status: common_1.HttpStatus.OK, type: require("../dto/create.cms-partner.dto").BulkOperationResponseDto }),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [dto_1.BulkDeleteCmsPartnersDto, Object]),
    __metadata("design:returntype", Promise)
], DeleteCmsPartnersController.prototype, "bulkSoftDelete", null);
__decorate([
    (0, common_1.Patch)('bulk/restore'),
    (0, roles_decorator_1.Roles)('ADMIN', 'EDITOR'),
    (0, permissions_decorator_1.Permissions)('cms_partners:delete'),
    (0, common_1.UsePipes)(new common_1.ValidationPipe({ transform: true, whitelist: true })),
    (0, swagger_1.ApiOperation)({
        summary: 'Khôi phục nhiều đối tác',
        description: 'Khôi phục nhiều đối tác đã bị xóa mềm',
    }),
    (0, swagger_1.ApiBody)({
        type: dto_1.BulkRestoreCmsPartnersDto,
        description: 'Danh sách ID đối tác cần khôi phục',
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Kết quả khôi phục nhiều đối tác',
        type: dto_1.BulkOperationResponseDto,
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.BAD_REQUEST,
        description: 'Dữ liệu đầu vào không hợp lệ',
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.UNAUTHORIZED,
        description: 'Không có quyền truy cập',
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.FORBIDDEN,
        description: 'Không có quyền thực hiện thao tác này',
    }),
    openapi.ApiResponse({ status: 200, type: require("../dto/create.cms-partner.dto").BulkOperationResponseDto }),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [dto_1.BulkRestoreCmsPartnersDto, Object]),
    __metadata("design:returntype", Promise)
], DeleteCmsPartnersController.prototype, "bulkRestore", null);
__decorate([
    (0, common_1.Delete)('bulk/force'),
    (0, common_1.HttpCode)(common_1.HttpStatus.OK),
    (0, roles_decorator_1.Roles)('ADMIN'),
    (0, permissions_decorator_1.Permissions)('cms_partners:force_delete'),
    (0, common_1.UsePipes)(new common_1.ValidationPipe({ transform: true, whitelist: true })),
    (0, swagger_1.ApiOperation)({
        summary: 'Xóa cứng nhiều đối tác',
        description: 'Xóa vĩnh viễn nhiều đối tác cùng lúc',
    }),
    (0, swagger_1.ApiBody)({
        type: dto_1.BulkForceDeleteCmsPartnersDto,
        description: 'Danh sách ID đối tác cần xóa vĩnh viễn',
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Kết quả xóa cứng nhiều đối tác',
        type: dto_1.BulkOperationResponseDto,
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.BAD_REQUEST,
        description: 'Dữ liệu đầu vào không hợp lệ',
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.UNAUTHORIZED,
        description: 'Không có quyền truy cập',
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.FORBIDDEN,
        description: 'Không có quyền thực hiện thao tác này',
    }),
    openapi.ApiResponse({ status: common_1.HttpStatus.OK, type: require("../dto/create.cms-partner.dto").BulkOperationResponseDto }),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [dto_1.BulkForceDeleteCmsPartnersDto, Object]),
    __metadata("design:returntype", Promise)
], DeleteCmsPartnersController.prototype, "bulkForceDelete", null);
__decorate([
    (0, common_1.Get)('deleted'),
    (0, roles_decorator_1.Roles)('ADMIN', 'EDITOR'),
    (0, permissions_decorator_1.Permissions)('cms_partners:read'),
    (0, common_1.UsePipes)(new common_1.ValidationPipe({ transform: true, whitelist: true })),
    (0, swagger_1.ApiOperation)({
        summary: 'Lấy đối tác đã xóa',
        description: 'Lấy danh sách đối tác đã bị xóa mềm với phân trang',
    }),
    (0, swagger_1.ApiQuery)({
        name: 'page',
        required: false,
        type: Number,
        description: 'Số trang (mặc định: 1)',
        example: 1,
    }),
    (0, swagger_1.ApiQuery)({
        name: 'limit',
        required: false,
        type: Number,
        description: 'Số lượng mỗi trang (mặc định: 10)',
        example: 10,
    }),
    (0, swagger_1.ApiQuery)({
        name: 'sortBy',
        required: false,
        type: String,
        description: 'Trường sắp xếp (mặc định: deletedAt)',
        example: 'deletedAt',
    }),
    (0, swagger_1.ApiQuery)({
        name: 'sortOrder',
        required: false,
        enum: ['ASC', 'DESC'],
        description: 'Thứ tự sắp xếp (mặc định: DESC)',
        example: 'DESC',
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Danh sách đối tác đã xóa',
        type: pagination_response_dto_1.PaginationResponseDto,
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.UNAUTHORIZED,
        description: 'Không có quyền truy cập',
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.FORBIDDEN,
        description: 'Không có quyền thực hiện thao tác này',
    }),
    openapi.ApiResponse({ status: 200 }),
    __param(0, (0, common_1.Query)()),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [custom_pagination_query_dto_1.CustomPaginationQueryDto, Object]),
    __metadata("design:returntype", Promise)
], DeleteCmsPartnersController.prototype, "getDeleted", null);
exports.DeleteCmsPartnersController = DeleteCmsPartnersController = DeleteCmsPartnersController_1 = __decorate([
    (0, swagger_1.ApiTags)('CMS Partners - Delete'),
    (0, common_1.Controller)('cms/partners'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, swagger_1.ApiBearerAuth)(),
    __metadata("design:paramtypes", [delete_cms_partners_service_1.DeleteCmsPartnersService,
        read_cms_partners_service_1.ReadCmsPartnersService])
], DeleteCmsPartnersController);
//# sourceMappingURL=delete.cms-partners.controller.js.map
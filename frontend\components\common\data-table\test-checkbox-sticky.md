# Test Case: Checkbox Column Sticky Behavior

## 🎯 **M<PERSON><PERSON> tiêu test:**
Đ<PERSON>m bảo checkbox column luôn đư<PERSON><PERSON> cố định bên trái khi scroll ngang, giống như action column bên phả<PERSON>.

## 🧪 **Test Cases:**

### **Test 1: Checkbox column sticky khi scroll ngang**
- ✅ Mở trang có datatable với nhiều columns (ví dụ: /admin/transactions)
- ✅ Scroll ngang sang phải → Checkbox column vẫn visible và cố định bên trái
- ✅ Scroll ngang sang trái → Checkbox column vẫn ở vị trí ban đầu
- ✅ So sánh với action column: Cả hai đều sticky

### **Test 2: Visual indicators**
- ✅ Checkbox column có border màu primary (debug mode)
- ✅ Box shadow hiển thị đúng
- ✅ Z-index cao (999) để đảm bảo luôn ở trên

### **Test 3: Functionality khi sticky**
- ✅ Click checkbox vẫn hoạt động khi scroll
- ✅ Select all vẫn hoạt động
- ✅ Row selection không bị ảnh hưởng

## 🔧 **Cách test:**

### **1. Manual Test:**
```bash
# 1. Mở trang có datatable với nhiều columns
# 2. Scroll ngang để test sticky behavior
# 3. Verify checkbox column luôn visible
# 4. Test checkbox functionality
```

### **2. CSS Selectors để check:**
```css
/* ✅ Checkbox columns có styling sticky */
.data-table th[data-column-id="select"],
.data-table td[data-column-id="select"] {
  position: sticky;
  left: 0;
  z-index: 999;
  border-right: 2px solid var(--primary); /* Debug border */
}
```

### **3. Browser DevTools:**
```javascript
// Check sticky positioning
const checkboxHeader = document.querySelector('th[data-column-id="select"]');
console.log(getComputedStyle(checkboxHeader).position); // Should be "sticky"
console.log(getComputedStyle(checkboxHeader).left); // Should be "0px"
console.log(getComputedStyle(checkboxHeader).zIndex); // Should be "999"
```

## 🐛 **Vấn đề đã fix:**

### **❌ Trước khi fix:**
- Checkbox column không sticky → Bị ẩn khi scroll ngang
- Không thể select rows khi scroll
- UX kém vì mất chức năng chính

### **✅ Sau khi fix:**
- Checkbox column luôn sticky bên trái
- Có thể select rows ở bất kỳ vị trí scroll nào
- Consistent với action column behavior

## 📋 **Checklist:**

- [ ] Checkbox column visible khi scroll ngang
- [ ] Position sticky hoạt động đúng
- [ ] Z-index đủ cao để không bị che
- [ ] Border debug hiển thị (có thể remove sau)
- [ ] Checkbox functionality hoạt động
- [ ] Select all hoạt động
- [ ] Performance không bị impact
- [ ] Responsive behavior vẫn đúng

## 🎉 **Expected Result:**
Checkbox column **luôn cố định bên trái** khi scroll ngang, giống như action column cố định bên phải.

## 🔧 **Debug Mode:**
- **Border màu primary**: Để dễ nhận biết checkbox columns
- **Z-index 999**: Để đảm bảo luôn ở trên
- **Multiple CSS selectors**: Để override mọi conflict

## 📝 **Notes:**
- Có thể remove debug border sau khi confirm hoạt động
- Z-index có thể giảm xuống 70 sau khi stable
- CSS specificity đã được tăng để tránh conflicts

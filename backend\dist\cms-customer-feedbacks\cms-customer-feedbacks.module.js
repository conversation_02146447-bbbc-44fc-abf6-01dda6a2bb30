"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var CmsCustomerFeedbacksModule_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.CmsCustomerFeedbacksModule = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const cms_customer_feedbacks_entity_1 = require("./entity/cms-customer-feedbacks.entity");
const base_cms_customer_feedbacks_service_1 = require("./services/base.cms-customer-feedbacks.service");
const create_cms_customer_feedbacks_service_1 = require("./services/create.cms-customer-feedbacks.service");
const read_cms_customer_feedbacks_service_1 = require("./services/read.cms-customer-feedbacks.service");
const update_cms_customer_feedbacks_service_1 = require("./services/update.cms-customer-feedbacks.service");
const delete_cms_customer_feedbacks_service_1 = require("./services/delete.cms-customer-feedbacks.service");
const controllers_1 = require("./controllers");
const read_cms_customer_feedbacks_public_controller_1 = require("./controllers/read.cms-customer-feedbacks.public.controller");
let CmsCustomerFeedbacksModule = CmsCustomerFeedbacksModule_1 = class CmsCustomerFeedbacksModule {
    logger = new common_1.Logger(CmsCustomerFeedbacksModule_1.name);
    constructor() {
        this.logger.log('CmsCustomerFeedbacksModule dependencies initialized');
    }
};
exports.CmsCustomerFeedbacksModule = CmsCustomerFeedbacksModule;
exports.CmsCustomerFeedbacksModule = CmsCustomerFeedbacksModule = CmsCustomerFeedbacksModule_1 = __decorate([
    (0, common_1.Module)({
        imports: [
            typeorm_1.TypeOrmModule.forFeature([cms_customer_feedbacks_entity_1.CmsCustomerFeedbacks]),
        ],
        controllers: [
            controllers_1.CreateCmsCustomerFeedbacksController,
            controllers_1.ReadCmsCustomerFeedbacksController,
            controllers_1.UpdateCmsCustomerFeedbacksController,
            controllers_1.DeleteCmsCustomerFeedbacksController,
            read_cms_customer_feedbacks_public_controller_1.ReadCmsCustomerFeedbacksPublicController,
        ],
        providers: [
            base_cms_customer_feedbacks_service_1.BaseCmsCustomerFeedbacksService,
            create_cms_customer_feedbacks_service_1.CreateCmsCustomerFeedbacksService,
            read_cms_customer_feedbacks_service_1.ReadCmsCustomerFeedbacksService,
            update_cms_customer_feedbacks_service_1.UpdateCmsCustomerFeedbacksService,
            delete_cms_customer_feedbacks_service_1.DeleteCmsCustomerFeedbacksService,
        ],
        exports: [
            base_cms_customer_feedbacks_service_1.BaseCmsCustomerFeedbacksService,
            create_cms_customer_feedbacks_service_1.CreateCmsCustomerFeedbacksService,
            read_cms_customer_feedbacks_service_1.ReadCmsCustomerFeedbacksService,
            update_cms_customer_feedbacks_service_1.UpdateCmsCustomerFeedbacksService,
            delete_cms_customer_feedbacks_service_1.DeleteCmsCustomerFeedbacksService,
        ],
    }),
    __metadata("design:paramtypes", [])
], CmsCustomerFeedbacksModule);
//# sourceMappingURL=cms-customer-feedbacks.module.js.map
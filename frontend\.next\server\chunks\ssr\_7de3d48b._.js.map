{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/bloomgoo/gold-exchange-platform/frontend/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\r\nimport { Slot } from \"@radix-ui/react-slot\"\r\nimport { cva, type VariantProps } from \"class-variance-authority\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst buttonVariants = cva(\r\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\r\n  {\r\n    variants: {\r\n      variant: {\r\n        default:\r\n          \"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90\",\r\n        destructive:\r\n          \"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\r\n        outline:\r\n          \"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50\",\r\n        secondary:\r\n          \"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80\",\r\n        ghost:\r\n          \"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50\",\r\n        link: \"text-primary underline-offset-4 hover:underline\",\r\n      },\r\n      size: {\r\n        default: \"h-9 px-4 py-2 has-[>svg]:px-3\",\r\n        sm: \"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5\",\r\n        lg: \"h-10 rounded-md px-6 has-[>svg]:px-4\",\r\n        icon: \"size-9\",\r\n      },\r\n    },\r\n    defaultVariants: {\r\n      variant: \"default\",\r\n      size: \"default\",\r\n    },\r\n  }\r\n)\r\n\r\nfunction Button({\r\n  className,\r\n  variant,\r\n  size,\r\n  asChild = false,\r\n  ...props\r\n}: React.ComponentProps<\"button\"> &\r\n  VariantProps<typeof buttonVariants> & {\r\n    asChild?: boolean\r\n  }) {\r\n  const Comp = asChild ? Slot : \"button\"\r\n\r\n  return (\r\n    <Comp\r\n      data-slot=\"button\"\r\n      className={cn(buttonVariants({ variant, size, className }))}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport { Button, buttonVariants }\r\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;AAAA;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,2OAAA,CAAA,MAAG,AAAD,EACvB,+bACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OACE;YACF,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,SAAS,OAAO,EACd,SAAS,EACT,OAAO,EACP,IAAI,EACJ,UAAU,KAAK,EACf,GAAG,OAIF;IACD,MAAM,OAAO,UAAU,oSAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,uVAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACvD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 65, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/bloomgoo/gold-exchange-platform/frontend/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction Input({ className, type, ...props }: React.ComponentProps<\"input\">) {\r\n  return (\r\n    <input\r\n      type={type}\r\n      data-slot=\"input\"\r\n      className={cn(\r\n        \"file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\r\n        \"focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]\",\r\n        \"aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport { Input }\r\n"], "names": [], "mappings": ";;;;AAEA;AAAA;;;AAEA,SAAS,MAAM,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAsC;IACzE,qBACE,uVAAC;QACC,MAAM;QACN,aAAU;QACV,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EACV,mcACA,iFACA,0GACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 92, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/bloomgoo/gold-exchange-platform/frontend/components/ui/label.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as LabelPrimitive from \"@radix-ui/react-label\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction Label({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof LabelPrimitive.Root>) {\r\n  return (\r\n    <LabelPrimitive.Root\r\n      data-slot=\"label\"\r\n      className={cn(\r\n        \"flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport { Label }\r\n"], "names": [], "mappings": ";;;;AAGA;AAEA;AAAA;AALA;;;;AAOA,SAAS,MAAM,EACb,SAAS,EACT,GAAG,OAC8C;IACjD,qBACE,uVAAC,8QAAA,CAAA,OAAmB;QAClB,aAAU;QACV,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EACV,uNACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 121, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/bloomgoo/gold-exchange-platform/frontend/components/forgot-password-form.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useState } from \"react\";\r\nimport { cn } from \"@/lib/utils\"\r\nimport { Button } from \"@/components/ui/button\"\r\nimport { Input } from \"@/components/ui/input\"\r\nimport { Label } from \"@/components/ui/label\"\r\nimport { toast } from \"sonner\"\r\nimport { Loader2 } from \"lucide-react\"\r\nimport Link from \"next/link\"\r\nimport { api } from \"@/lib/api\";\r\nimport { ApiResponse } from \"@/lib/response\";\r\n\r\nexport function ForgotPasswordForm({\r\n  className,\r\n  ...props\r\n}: React.ComponentPropsWithoutRef<\"form\">) {\r\n  const [email, setEmail] = useState(\"\");\r\n  const [isLoading, setIsLoading] = useState(false);\r\n  const [isSubmitted, setIsSubmitted] = useState(false);\r\n\r\n  const handleSubmit = async (e: React.FormEvent) => {\r\n    e.preventDefault();\r\n    \r\n    if (!email.trim()) {\r\n      toast.error(\"Lỗi\", {\r\n        description: \"<PERSON>ui lòng nhập địa chỉ email của bạn.\"\r\n      });\r\n      return;\r\n    }\r\n\r\n    setIsLoading(true);\r\n\r\n    try {\r\n      // Gọi API yêu cầu đặt lại mật khẩu\r\n      await api.post<ApiResponse<any>>('/auth/forgot-password', { email });\r\n      \r\n      // Đánh dấu đã gửi yêu cầu thành công\r\n      setIsSubmitted(true);\r\n      \r\n      toast.success(\"Yêu cầu đã được gửi\", {\r\n        description: \"Vui lòng kiểm tra email của bạn để đặt lại mật khẩu.\"\r\n      });\r\n    } catch (error: any) {\r\n      console.error('Forgot password error:', error);\r\n      \r\n      let errorMessage = \"Có lỗi xảy ra. Vui lòng thử lại sau.\";\r\n      \r\n      if (error.response?.data) {\r\n        const apiError = error.response.data as ApiResponse<any>;\r\n        errorMessage = apiError.message || errorMessage;\r\n      } else if (error.message) {\r\n        if (error.message.includes('NOT_FOUND')) {\r\n          errorMessage = \"Không tìm thấy API endpoint. Vui lòng kiểm tra cấu hình API.\";\r\n        } else if (error.message.includes('Network Error')) {\r\n          errorMessage = \"Không thể kết nối đến máy chủ. Vui lòng kiểm tra kết nối mạng.\";\r\n        } else {\r\n          errorMessage = error.message;\r\n        }\r\n      }\r\n      \r\n      toast.error(\"Yêu cầu thất bại\", {\r\n        description: errorMessage\r\n      });\r\n    } finally {\r\n      setIsLoading(false);\r\n    }\r\n  };\r\n\r\n  return (\r\n    <form onSubmit={handleSubmit} className={cn(\"flex flex-col gap-6\", className)} {...props}>\r\n      <div className=\"flex flex-col items-center gap-2 text-center\">\r\n        <h1 className=\"text-2xl font-bold\">Quên mật khẩu</h1>\r\n        <p className=\"text-balance text-sm text-muted-foreground\">\r\n          Nhập email của bạn để nhận liên kết đặt lại mật khẩu\r\n        </p>\r\n      </div>\r\n      \r\n      {!isSubmitted ? (\r\n        <div className=\"grid gap-6\">\r\n          <div className=\"grid gap-2\">\r\n            <Label htmlFor=\"email\">Email</Label>\r\n            <Input \r\n              id=\"email\" \r\n              type=\"email\" \r\n              placeholder=\"<EMAIL>\" \r\n              value={email}\r\n              onChange={(e) => setEmail(e.target.value)}\r\n              required \r\n            />\r\n          </div>\r\n          \r\n          <Button type=\"submit\" className=\"w-full\" disabled={isLoading}>\r\n            {isLoading ? (\r\n              <>\r\n                <Loader2 className=\"mr-2 h-4 w-4 animate-spin\" />\r\n                Đang gửi...\r\n              </>\r\n            ) : (\r\n              \"Gửi liên kết đặt lại\"\r\n            )}\r\n          </Button>\r\n        </div>\r\n      ) : (\r\n        <div className=\"rounded-lg border border-border bg-card p-6 text-center\">\r\n          <div className=\"mb-4 flex justify-center\">\r\n            <div className=\"rounded-full bg-primary/10 p-3\">\r\n              <svg\r\n                xmlns=\"http://www.w3.org/2000/svg\"\r\n                fill=\"none\"\r\n                viewBox=\"0 0 24 24\"\r\n                strokeWidth={1.5}\r\n                stroke=\"currentColor\"\r\n                className=\"h-6 w-6 text-primary\"\r\n              >\r\n                <path\r\n                  strokeLinecap=\"round\"\r\n                  strokeLinejoin=\"round\"\r\n                  d=\"M21.75 6.75v10.5a2.25 2.25 0 01-2.25 2.25h-15a2.25 2.25 0 01-2.25-2.25V6.75m19.5 0A2.25 2.25 0 0019.5 4.5h-15a2.25 2.25 0 00-2.25 2.25m19.5 0v.243a2.25 2.25 0 01-1.07 1.916l-7.5 4.615a2.25 2.25 0 01-2.36 0L3.32 8.91a2.25 2.25 0 01-1.07-1.916V6.75\"\r\n                />\r\n              </svg>\r\n            </div>\r\n          </div>\r\n          <h3 className=\"mb-2 text-lg font-medium\">Kiểm tra email của bạn</h3>\r\n          <p className=\"mb-4 text-sm text-muted-foreground\">\r\n            Chúng tôi đã gửi một liên kết đặt lại mật khẩu đến {email}. Vui lòng kiểm tra hộp thư đến của bạn.\r\n          </p>\r\n          <Button\r\n            variant=\"outline\"\r\n            className=\"w-full\"\r\n            onClick={() => setIsSubmitted(false)}\r\n          >\r\n            Gửi lại liên kết\r\n          </Button>\r\n        </div>\r\n      )}\r\n      \r\n      <div className=\"text-center text-sm\">\r\n        <Link href=\"/login\" className=\"font-medium text-primary underline underline-offset-4 hover:text-primary/90\">\r\n          Quay lại đăng nhập\r\n        </Link>\r\n      </div>\r\n    </form>\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAVA;;;;;;;;;;;AAaO,SAAS,mBAAmB,EACjC,SAAS,EACT,GAAG,OACoC;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,8SAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,8SAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,8SAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAEhB,IAAI,CAAC,MAAM,IAAI,IAAI;YACjB,wQAAA,CAAA,QAAK,CAAC,KAAK,CAAC,OAAO;gBACjB,aAAa;YACf;YACA;QACF;QAEA,aAAa;QAEb,IAAI;YACF,mCAAmC;YACnC,MAAM,0GAAA,CAAA,MAAG,CAAC,IAAI,CAAmB,yBAAyB;gBAAE;YAAM;YAElE,qCAAqC;YACrC,eAAe;YAEf,wQAAA,CAAA,QAAK,CAAC,OAAO,CAAC,uBAAuB;gBACnC,aAAa;YACf;QACF,EAAE,OAAO,OAAY;YACnB,QAAQ,KAAK,CAAC,0BAA0B;YAExC,IAAI,eAAe;YAEnB,IAAI,MAAM,QAAQ,EAAE,MAAM;gBACxB,MAAM,WAAW,MAAM,QAAQ,CAAC,IAAI;gBACpC,eAAe,SAAS,OAAO,IAAI;YACrC,OAAO,IAAI,MAAM,OAAO,EAAE;gBACxB,IAAI,MAAM,OAAO,CAAC,QAAQ,CAAC,cAAc;oBACvC,eAAe;gBACjB,OAAO,IAAI,MAAM,OAAO,CAAC,QAAQ,CAAC,kBAAkB;oBAClD,eAAe;gBACjB,OAAO;oBACL,eAAe,MAAM,OAAO;gBAC9B;YACF;YAEA,wQAAA,CAAA,QAAK,CAAC,KAAK,CAAC,oBAAoB;gBAC9B,aAAa;YACf;QACF,SAAU;YACR,aAAa;QACf;IACF;IAEA,qBACE,uVAAC;QAAK,UAAU;QAAc,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EAAE,uBAAuB;QAAa,GAAG,KAAK;;0BACtF,uVAAC;gBAAI,WAAU;;kCACb,uVAAC;wBAAG,WAAU;kCAAqB;;;;;;kCACnC,uVAAC;wBAAE,WAAU;kCAA6C;;;;;;;;;;;;YAK3D,CAAC,4BACA,uVAAC;gBAAI,WAAU;;kCACb,uVAAC;wBAAI,WAAU;;0CACb,uVAAC,0HAAA,CAAA,QAAK;gCAAC,SAAQ;0CAAQ;;;;;;0CACvB,uVAAC,0HAAA,CAAA,QAAK;gCACJ,IAAG;gCACH,MAAK;gCACL,aAAY;gCACZ,OAAO;gCACP,UAAU,CAAC,IAAM,SAAS,EAAE,MAAM,CAAC,KAAK;gCACxC,QAAQ;;;;;;;;;;;;kCAIZ,uVAAC,2HAAA,CAAA,SAAM;wBAAC,MAAK;wBAAS,WAAU;wBAAS,UAAU;kCAChD,0BACC;;8CACE,uVAAC,qSAAA,CAAA,UAAO;oCAAC,WAAU;;;;;;gCAA8B;;2CAInD;;;;;;;;;;;qCAKN,uVAAC;gBAAI,WAAU;;kCACb,uVAAC;wBAAI,WAAU;kCACb,cAAA,uVAAC;4BAAI,WAAU;sCACb,cAAA,uVAAC;gCACC,OAAM;gCACN,MAAK;gCACL,SAAQ;gCACR,aAAa;gCACb,QAAO;gCACP,WAAU;0CAEV,cAAA,uVAAC;oCACC,eAAc;oCACd,gBAAe;oCACf,GAAE;;;;;;;;;;;;;;;;;;;;;kCAKV,uVAAC;wBAAG,WAAU;kCAA2B;;;;;;kCACzC,uVAAC;wBAAE,WAAU;;4BAAqC;4BACI;4BAAM;;;;;;;kCAE5D,uVAAC,2HAAA,CAAA,SAAM;wBACL,SAAQ;wBACR,WAAU;wBACV,SAAS,IAAM,eAAe;kCAC/B;;;;;;;;;;;;0BAML,uVAAC;gBAAI,WAAU;0BACb,cAAA,uVAAC,qQAAA,CAAA,UAAI;oBAAC,MAAK;oBAAS,WAAU;8BAA8E;;;;;;;;;;;;;;;;;AAMpH", "debugId": null}}, {"offset": {"line": 382, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/bloomgoo/gold-exchange-platform/frontend/components/auth/public-route.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useEffect, useState } from \"react\";\r\nimport { useRouter } from \"next/navigation\";\r\nimport { useAuth } from \"@/hooks/use-auth\";\r\nimport { Loader2 } from \"lucide-react\";\r\n\r\ninterface PublicRouteProps {\r\n  children: React.ReactNode;\r\n}\r\n\r\nexport function PublicRoute({ children }: PublicRouteProps) {\r\n  const { user, isLoading, autoLogin } = useAuth();\r\n  const router = useRouter();\r\n  const [isCheckingAuth, setIsCheckingAuth] = useState(true);\r\n\r\n  useEffect(() => {\r\n    const checkAuth = async () => {\r\n      setIsCheckingAuth(true);\r\n\r\n      // Nế<PERSON> đã có user, kiểm tra vai trò và chuyển hướng\r\n      if (user) {\r\n        if (user.roles?.includes('ADMIN')) {\r\n          // Nếu là admin, chuyển hướng đến trang admin dashboard\r\n          router.push('/admin/dashboard');\r\n        } else {\r\n          // Nếu không phải admin, chuyể<PERSON> hướng đến trang dashboard thông thường\r\n          router.push('/dashboard');\r\n        }\r\n        return;\r\n      }\r\n\r\n      // Nếu chưa có user, thử tự động đăng nhập\r\n      const success = await autoLogin();\r\n\r\n      // Sau khi tự động đăng nhập, kiểm tra lại user\r\n      // Lưu ý: user có thể đã được cập nhật trong autoLogin\r\n      const currentUser = JSON.parse(localStorage.getItem('user') || '{}');\r\n\r\n      if (success && currentUser) {\r\n        if (currentUser.roles?.includes('ADMIN')) {\r\n          // Nếu là admin, chuyển hướng đến trang admin dashboard\r\n          router.push('/admin/dashboard');\r\n        } else {\r\n          // Nếu không phải admin, chuyển hướng đến trang dashboard thông thường\r\n          router.push('/dashboard');\r\n        }\r\n        return;\r\n      }\r\n\r\n      setIsCheckingAuth(false);\r\n    };\r\n\r\n    checkAuth();\r\n  }, [user, router, autoLogin]);\r\n\r\n  if (isLoading || isCheckingAuth) {\r\n    return (\r\n      <div className=\"flex h-screen w-full items-center justify-center\">\r\n        <div className=\"flex flex-col items-center gap-2\">\r\n          <Loader2 className=\"h-8 w-8 animate-spin text-primary\" />\r\n          <p className=\"text-sm text-muted-foreground\">Đang tải...</p>\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  return <>{children}</>;\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AALA;;;;;;AAWO,SAAS,YAAY,EAAE,QAAQ,EAAoB;IACxD,MAAM,EAAE,IAAI,EAAE,SAAS,EAAE,SAAS,EAAE,GAAG,CAAA,GAAA,qHAAA,CAAA,UAAO,AAAD;IAC7C,MAAM,SAAS,CAAA,GAAA,2OAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,8SAAA,CAAA,WAAQ,AAAD,EAAE;IAErD,CAAA,GAAA,8SAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,YAAY;YAChB,kBAAkB;YAElB,mDAAmD;YACnD,IAAI,MAAM;gBACR,IAAI,KAAK,KAAK,EAAE,SAAS,UAAU;oBACjC,uDAAuD;oBACvD,OAAO,IAAI,CAAC;gBACd,OAAO;oBACL,sEAAsE;oBACtE,OAAO,IAAI,CAAC;gBACd;gBACA;YACF;YAEA,0CAA0C;YAC1C,MAAM,UAAU,MAAM;YAEtB,+CAA+C;YAC/C,sDAAsD;YACtD,MAAM,cAAc,KAAK,KAAK,CAAC,aAAa,OAAO,CAAC,WAAW;YAE/D,IAAI,WAAW,aAAa;gBAC1B,IAAI,YAAY,KAAK,EAAE,SAAS,UAAU;oBACxC,uDAAuD;oBACvD,OAAO,IAAI,CAAC;gBACd,OAAO;oBACL,sEAAsE;oBACtE,OAAO,IAAI,CAAC;gBACd;gBACA;YACF;YAEA,kBAAkB;QACpB;QAEA;IACF,GAAG;QAAC;QAAM;QAAQ;KAAU;IAE5B,IAAI,aAAa,gBAAgB;QAC/B,qBACE,uVAAC;YAAI,WAAU;sBACb,cAAA,uVAAC;gBAAI,WAAU;;kCACb,uVAAC,qSAAA,CAAA,UAAO;wBAAC,WAAU;;;;;;kCACnB,uVAAC;wBAAE,WAAU;kCAAgC;;;;;;;;;;;;;;;;;IAIrD;IAEA,qBAAO;kBAAG;;AACZ", "debugId": null}}, {"offset": {"line": 480, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/bloomgoo/gold-exchange-platform/frontend/app/%28auth%29/forgot-password/page.tsx"], "sourcesContent": ["\"use client\";\r\nimport { ForgotPasswordForm } from \"@/components/forgot-password-form\";\r\nimport { ArrowLeft, GalleryVerticalEnd } from \"lucide-react\";\r\nimport Link from \"next/link\";\r\nimport { PublicRoute } from \"@/components/auth/public-route\";\r\nimport type { Metadata } from \"next\";\r\nimport { useSiteMetadata } from \"@/hooks/use-site-metadata\";\r\n\r\n\r\nexport default function ForgotPasswordPage() {\r\n  const { metadata } = useSiteMetadata();\r\n  const siteName = metadata?.title;\r\n  const siteLogo = metadata?.ogImage;\r\n\r\n  return (\r\n    <PublicRoute>\r\n      <div className=\"grid min-h-svh lg:grid-cols-2\">\r\n        <div className=\"flex flex-col gap-4 p-6 md:p-10\">\r\n          <div className=\"flex justify-center gap-2 md:justify-start\">\r\n            <a href=\"#\" className=\"flex items-center gap-2 font-medium\">\r\n              <div className=\"flex size-8 items-center justify-center\">\r\n                <img src={siteLogo} alt={siteName} className=\"h-8 w-8\" />\r\n              </div>\r\n              {siteName}\r\n            </a>\r\n          </div>\r\n          <div className=\"flex flex-1 items-center justify-center\">\r\n            <div className=\"w-full max-w-md\">\r\n              <div className=\"mb-4\">\r\n                <Link\r\n                  href=\"/login\"\r\n                  className=\"inline-flex items-center text-sm font-medium text-primary hover:underline\"\r\n                >\r\n                  <ArrowLeft className=\"mr-1 h-4 w-4\" />\r\n                  Quay lại trang đăng nhập\r\n                </Link>\r\n              </div>\r\n              <ForgotPasswordForm />\r\n            </div>\r\n          </div>\r\n        </div>\r\n        <div className=\"bg-muted relative hidden lg:block\">\r\n          <img\r\n            src=\"/banner-thuong-hieu.png\"\r\n            alt={siteName}\r\n            className=\"absolute inset-0 h-full w-full object-cover dark:brightness-[0.2] dark:grayscale\"\r\n          />\r\n        </div>\r\n      </div>\r\n    </PublicRoute>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AACA;AACA;AACA;AACA;AAEA;AANA;;;;;;;AASe,SAAS;IACtB,MAAM,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,gIAAA,CAAA,kBAAe,AAAD;IACnC,MAAM,WAAW,UAAU;IAC3B,MAAM,WAAW,UAAU;IAE3B,qBACE,uVAAC,sIAAA,CAAA,cAAW;kBACV,cAAA,uVAAC;YAAI,WAAU;;8BACb,uVAAC;oBAAI,WAAU;;sCACb,uVAAC;4BAAI,WAAU;sCACb,cAAA,uVAAC;gCAAE,MAAK;gCAAI,WAAU;;kDACpB,uVAAC;wCAAI,WAAU;kDACb,cAAA,uVAAC;4CAAI,KAAK;4CAAU,KAAK;4CAAU,WAAU;;;;;;;;;;;oCAE9C;;;;;;;;;;;;sCAGL,uVAAC;4BAAI,WAAU;sCACb,cAAA,uVAAC;gCAAI,WAAU;;kDACb,uVAAC;wCAAI,WAAU;kDACb,cAAA,uVAAC,qQAAA,CAAA,UAAI;4CACH,MAAK;4CACL,WAAU;;8DAEV,uVAAC,oSAAA,CAAA,YAAS;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;;;;;;kDAI1C,uVAAC,yIAAA,CAAA,qBAAkB;;;;;;;;;;;;;;;;;;;;;;8BAIzB,uVAAC;oBAAI,WAAU;8BACb,cAAA,uVAAC;wBACC,KAAI;wBACJ,KAAK;wBACL,WAAU;;;;;;;;;;;;;;;;;;;;;;AAMtB", "debugId": null}}]}
"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var BaseCmsTagsService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.BaseCmsTagsService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const event_emitter_1 = require("@nestjs/event-emitter");
const class_transformer_1 = require("class-transformer");
const cms_tags_entity_1 = require("../entity/cms-tags.entity");
const cms_tag_dto_1 = require("../dto/cms-tag.dto");
let BaseCmsTagsService = BaseCmsTagsService_1 = class BaseCmsTagsService {
    tagRepository;
    dataSource;
    eventEmitter;
    logger = new common_1.Logger(BaseCmsTagsService_1.name);
    EVENT_TAG_CREATED = 'cms-tag.created';
    EVENT_TAG_UPDATED = 'cms-tag.updated';
    EVENT_TAG_DELETED = 'cms-tag.deleted';
    validRelations = [
        'posts',
        'creator',
        'updater',
        'deleter'
    ];
    constructor(tagRepository, dataSource, eventEmitter) {
        this.tagRepository = tagRepository;
        this.dataSource = dataSource;
        this.eventEmitter = eventEmitter;
    }
    validateRelations(relations) {
        if (!relations || !Array.isArray(relations)) {
            return [];
        }
        return relations.filter(relation => {
            const isValid = this.validRelations.includes(relation);
            if (!isValid) {
                this.logger.warn(`Mối quan hệ không hợp lệ: ${relation}`);
            }
            return isValid;
        });
    }
    buildWhereClause(filter) {
        if (!filter) {
            return { isDeleted: false };
        }
        try {
            const filterObj = JSON.parse(filter);
            return {
                ...filterObj,
                isDeleted: false,
            };
        }
        catch (e) {
            return [
                { name: (0, typeorm_2.ILike)(`%${filter}%`), isDeleted: false },
                { slug: (0, typeorm_2.ILike)(`%${filter}%`), isDeleted: false },
            ];
        }
    }
    async findById(id, relations = [], throwError = true) {
        const validatedRelations = this.validateRelations(relations);
        const tag = await this.tagRepository.findOne({
            where: { id, isDeleted: false },
            relations: validatedRelations,
        });
        if (!tag && throwError) {
            throw new common_1.NotFoundException(`Không tìm thấy thẻ với ID: ${id}`);
        }
        return tag;
    }
    async findBySlug(slug, throwError = true) {
        const tag = await this.tagRepository.findOne({
            where: { slug, isDeleted: false },
        });
        if (!tag && throwError) {
            throw new common_1.NotFoundException(`Không tìm thấy thẻ với slug: ${slug}`);
        }
        return tag;
    }
    async findByName(name, throwError = true) {
        const tag = await this.tagRepository.findOne({
            where: { name, isDeleted: false },
        });
        if (!tag && throwError) {
            throw new common_1.NotFoundException(`Không tìm thấy thẻ với tên: ${name}`);
        }
        return tag;
    }
    toDto(tag) {
        if (!tag) {
            return null;
        }
        const plainObj = Object.assign({}, tag);
        if (tag.posts && Array.isArray(tag.posts)) {
            plainObj.postsCount = tag.posts.length;
        }
        return (0, class_transformer_1.plainToInstance)(cms_tag_dto_1.CmsTagDto, plainObj, {
            excludeExtraneousValues: true,
            enableImplicitConversion: true,
            exposeDefaultValues: true,
            exposeUnsetFields: false,
        });
    }
    toDtos(tags) {
        if (!tags || !Array.isArray(tags)) {
            return [];
        }
        return tags.map(tag => this.toDto(tag))
            .filter((dto) => dto !== null);
    }
    generateSlugFromName(name) {
        return name
            .toLowerCase()
            .normalize('NFD')
            .replace(/[\u0300-\u036f]/g, '')
            .replace(/[đĐ]/g, 'd')
            .replace(/[^a-z0-9\s-]/g, '')
            .replace(/\s+/g, '-')
            .replace(/-+/g, '-')
            .replace(/^-|-$/g, '');
    }
    async isSlugUnique(slug, excludeId) {
        const whereCondition = { slug, isDeleted: false };
        if (excludeId) {
            whereCondition.id = { $ne: excludeId };
        }
        const existingTag = await this.tagRepository.findOne({
            where: whereCondition,
        });
        return !existingTag;
    }
    async isNameUnique(name, excludeId) {
        const whereCondition = { name, isDeleted: false };
        if (excludeId) {
            whereCondition.id = { $ne: excludeId };
        }
        const existingTag = await this.tagRepository.findOne({
            where: whereCondition,
        });
        return !existingTag;
    }
};
exports.BaseCmsTagsService = BaseCmsTagsService;
exports.BaseCmsTagsService = BaseCmsTagsService = BaseCmsTagsService_1 = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(cms_tags_entity_1.CmsTags)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        typeorm_2.DataSource,
        event_emitter_1.EventEmitter2])
], BaseCmsTagsService);
//# sourceMappingURL=base.cms-tags.service.js.map
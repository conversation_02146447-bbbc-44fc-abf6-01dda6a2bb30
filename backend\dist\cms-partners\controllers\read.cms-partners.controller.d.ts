import { ReadCmsPartnersService } from '../services/read.cms-partners.service';
import { CmsPartnerDto, CmsPartnerStatus, CmsPartnerType } from '../dto';
import { CustomPaginationQueryDto } from 'src/common/dto/custom-pagination-query.dto';
import { PaginationResponseDto } from 'src/common/dto/pagination-response.dto';
export declare class ReadCmsPartnersController {
    private readonly readCmsPartnersService;
    private readonly logger;
    constructor(readCmsPartnersService: ReadCmsPartnersService);
    findAll(paginationQuery: CustomPaginationQueryDto): Promise<PaginationResponseDto<CmsPartnerDto>>;
    findByStatus(status: CmsPartnerStatus, paginationQuery: CustomPaginationQueryDto, req: any): Promise<PaginationResponseDto<CmsPartnerDto>>;
    findByType(type: CmsPartnerType, paginationQuery: CustomPaginationQueryDto): Promise<PaginationResponseDto<CmsPartnerDto>>;
    getActivePartners(paginationQuery: CustomPaginationQueryDto, req: any): Promise<PaginationResponseDto<CmsPartnerDto>>;
    getStatistics(): Promise<{
        total: number;
        active: number;
        inactive: number;
        byType: Record<CmsPartnerType, number>;
        withLogo: number;
        withWebsite: number;
        withCompleteInfo: number;
    }>;
    count(status?: CmsPartnerStatus, req?: any): Promise<{
        count: number;
    }>;
    search(searchTerm: string, paginationQuery: CustomPaginationQueryDto, req: any): Promise<PaginationResponseDto<CmsPartnerDto>>;
    getDeleted(paginationQuery: CustomPaginationQueryDto, req: any): Promise<PaginationResponseDto<CmsPartnerDto>>;
    findById(id: string, relations?: string): Promise<CmsPartnerDto>;
}

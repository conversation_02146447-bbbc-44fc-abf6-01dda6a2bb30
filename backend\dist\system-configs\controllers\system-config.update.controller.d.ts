import { UpdateSystemConfigService } from '../services/update.system-config.service';
import { SystemConfigDto } from '../dto/system-config.dto';
import { UpdateSystemConfigDto } from '../dto/update-system-config.dto';
import { BulkUpdateSystemConfigItemDto } from '../dto/bulk-update-system-config.dto';
export declare class SystemConfigUpdateController {
    private readonly systemConfigService;
    private readonly logger;
    constructor(systemConfigService: UpdateSystemConfigService);
    update(id: string, updateSystemConfigDto: UpdateSystemConfigDto, userId: string): Promise<SystemConfigDto>;
    updateByKey(key: string, updateSystemConfigDto: UpdateSystemConfigDto, userId: string): Promise<SystemConfigDto>;
    updateBulk(bulkDto: BulkUpdateSystemConfigItemDto[], userId: string): Promise<SystemConfigDto[]>;
}

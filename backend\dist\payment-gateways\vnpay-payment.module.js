"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var VnpayPaymentModule_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.VnpayPaymentModule = void 0;
const common_1 = require("@nestjs/common");
const config_1 = require("@nestjs/config");
const typeorm_1 = require("@nestjs/typeorm");
const event_emitter_1 = require("@nestjs/event-emitter");
const axios_1 = require("@nestjs/axios");
const vnpay_transaction_entity_1 = require("./entities/vnpay-transaction.entity");
const vnpay_service_1 = require("./services/vnpay.service");
const vnpay_payment_service_1 = require("./services/vnpay-payment.service");
const vnpay_transaction_repository_1 = require("./repositories/vnpay-transaction.repository");
const vnpay_payment_controller_1 = require("./controllers/vnpay-payment.controller");
let VnpayPaymentModule = VnpayPaymentModule_1 = class VnpayPaymentModule {
    static forRoot(eventHandler) {
        return {
            module: VnpayPaymentModule_1,
            providers: [
                vnpay_service_1.VnpayService,
                vnpay_payment_service_1.VnpayPaymentService,
                vnpay_transaction_repository_1.VnpayTransactionRepository,
                ...(eventHandler
                    ? [
                        {
                            provide: 'PAYMENT_EVENT_HANDLER',
                            useValue: eventHandler,
                        },
                    ]
                    : []),
            ],
            exports: [vnpay_payment_service_1.VnpayPaymentService, vnpay_transaction_repository_1.VnpayTransactionRepository, vnpay_service_1.VnpayService],
        };
    }
};
exports.VnpayPaymentModule = VnpayPaymentModule;
exports.VnpayPaymentModule = VnpayPaymentModule = VnpayPaymentModule_1 = __decorate([
    (0, common_1.Module)({
        imports: [
            config_1.ConfigModule,
            typeorm_1.TypeOrmModule.forFeature([vnpay_transaction_entity_1.VnpayTransaction]),
            event_emitter_1.EventEmitterModule.forRoot(),
            axios_1.HttpModule,
        ],
        controllers: [vnpay_payment_controller_1.VnpayPaymentController],
        providers: [
            vnpay_service_1.VnpayService,
            vnpay_payment_service_1.VnpayPaymentService,
            vnpay_transaction_repository_1.VnpayTransactionRepository,
        ],
        exports: [
            vnpay_payment_service_1.VnpayPaymentService,
            vnpay_transaction_repository_1.VnpayTransactionRepository,
            vnpay_service_1.VnpayService,
        ],
    })
], VnpayPaymentModule);
//# sourceMappingURL=vnpay-payment.module.js.map
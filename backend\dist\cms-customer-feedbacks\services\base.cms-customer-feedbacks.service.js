"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var BaseCmsCustomerFeedbacksService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.BaseCmsCustomerFeedbacksService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const event_emitter_1 = require("@nestjs/event-emitter");
const class_transformer_1 = require("class-transformer");
const cms_customer_feedbacks_entity_1 = require("../entity/cms-customer-feedbacks.entity");
const cms_customer_feedback_dto_1 = require("../dto/cms-customer-feedback.dto");
let BaseCmsCustomerFeedbacksService = BaseCmsCustomerFeedbacksService_1 = class BaseCmsCustomerFeedbacksService {
    feedbackRepository;
    dataSource;
    eventEmitter;
    logger = new common_1.Logger(BaseCmsCustomerFeedbacksService_1.name);
    EVENT_FEEDBACK_CREATED = 'cms-customer-feedback.created';
    EVENT_FEEDBACK_UPDATED = 'cms-customer-feedback.updated';
    EVENT_FEEDBACK_DELETED = 'cms-customer-feedback.deleted';
    EVENT_FEEDBACK_APPROVED = 'cms-customer-feedback.approved';
    EVENT_FEEDBACK_REJECTED = 'cms-customer-feedback.rejected';
    validRelations = [
        'creator',
        'updater',
        'deleter',
        'approver'
    ];
    constructor(feedbackRepository, dataSource, eventEmitter) {
        this.feedbackRepository = feedbackRepository;
        this.dataSource = dataSource;
        this.eventEmitter = eventEmitter;
    }
    validateRelations(relations) {
        if (!relations || !Array.isArray(relations)) {
            return [];
        }
        return relations.filter(relation => {
            const isValid = this.validRelations.includes(relation);
            if (!isValid) {
                this.logger.warn(`Mối quan hệ không hợp lệ: ${relation}`);
            }
            return isValid;
        });
    }
    buildWhereClause(filter) {
        if (!filter) {
            return { isDeleted: false };
        }
        try {
            const filterObj = JSON.parse(filter);
            return {
                ...filterObj,
                isDeleted: false,
            };
        }
        catch (e) {
            return [
                { customerName: (0, typeorm_2.ILike)(`%${filter}%`), isDeleted: false },
                { feedbackText: (0, typeorm_2.ILike)(`%${filter}%`), isDeleted: false },
                { productServiceName: (0, typeorm_2.ILike)(`%${filter}%`), isDeleted: false },
                { customerTitleCompany: (0, typeorm_2.ILike)(`%${filter}%`), isDeleted: false },
            ];
        }
    }
    async findById(id, relations = [], throwError = true) {
        const validatedRelations = this.validateRelations(relations);
        const feedback = await this.feedbackRepository.findOne({
            where: { id, isDeleted: false },
            relations: validatedRelations,
        });
        if (!feedback && throwError) {
            throw new common_1.NotFoundException(`Không tìm thấy feedback với ID: ${id}`);
        }
        return feedback;
    }
    async findByBusinessCode(businessCode, throwError = true) {
        const feedback = await this.feedbackRepository.findOne({
            where: { businessCode, isDeleted: false },
        });
        if (!feedback && throwError) {
            throw new common_1.NotFoundException(`Không tìm thấy feedback với mã: ${businessCode}`);
        }
        return feedback;
    }
    toDto(feedback) {
        if (!feedback) {
            return null;
        }
        const plainObj = Object.assign({}, feedback);
        plainObj.ratingText = feedback.getRatingText();
        plainObj.ratingColor = feedback.getRatingColor();
        plainObj.displayName = feedback.getDisplayName();
        return (0, class_transformer_1.plainToInstance)(cms_customer_feedback_dto_1.CmsCustomerFeedbackDto, plainObj, {
            excludeExtraneousValues: true,
            enableImplicitConversion: true,
            exposeDefaultValues: true,
            exposeUnsetFields: false,
        });
    }
    toDtos(feedbacks) {
        if (!feedbacks || !Array.isArray(feedbacks)) {
            return [];
        }
        return feedbacks.map(feedback => this.toDto(feedback))
            .filter((dto) => dto !== null);
    }
    isFeedbackApproved(feedback) {
        return feedback.isApproved();
    }
    isFeedbackPending(feedback) {
        return feedback.isPending();
    }
    isFeedbackRejected(feedback) {
        return feedback.isRejected();
    }
    getRatingText(rating) {
        if (!rating)
            return 'Chưa đánh giá';
        const ratingTexts = {
            1: 'Rất không hài lòng',
            2: 'Không hài lòng',
            3: 'Bình thường',
            4: 'Hài lòng',
            5: 'Rất hài lòng'
        };
        return ratingTexts[rating] || 'Không xác định';
    }
    getRatingColor(rating) {
        if (!rating)
            return '#gray';
        const ratingColors = {
            1: '#ff4444',
            2: '#ff8800',
            3: '#ffbb33',
            4: '#00C851',
            5: '#007E33'
        };
        return ratingColors[rating] || '#gray';
    }
    calculateAverageRating(feedbacks) {
        const validRatings = feedbacks
            .filter(f => f.rating && f.rating > 0)
            .map(f => f.rating);
        if (validRatings.length === 0)
            return 0;
        const sum = validRatings.reduce((acc, rating) => acc + rating, 0);
        return Math.round((sum / validRatings.length) * 10) / 10;
    }
    categorizeByRating(feedbacks) {
        const categories = {
            '1': 0,
            '2': 0,
            '3': 0,
            '4': 0,
            '5': 0,
            'no_rating': 0
        };
        feedbacks.forEach(feedback => {
            if (feedback.rating && feedback.rating >= 1 && feedback.rating <= 5) {
                categories[feedback.rating.toString()]++;
            }
            else {
                categories['no_rating']++;
            }
        });
        return categories;
    }
};
exports.BaseCmsCustomerFeedbacksService = BaseCmsCustomerFeedbacksService;
exports.BaseCmsCustomerFeedbacksService = BaseCmsCustomerFeedbacksService = BaseCmsCustomerFeedbacksService_1 = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(cms_customer_feedbacks_entity_1.CmsCustomerFeedbacks)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        typeorm_2.DataSource,
        event_emitter_1.EventEmitter2])
], BaseCmsCustomerFeedbacksService);
//# sourceMappingURL=base.cms-customer-feedbacks.service.js.map
"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.BaseSystemConfigService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const event_emitter_1 = require("@nestjs/event-emitter");
const system_config_entity_1 = require("../entities/system-config.entity");
const system_config_dto_1 = require("../dto/system-config.dto");
const class_transformer_1 = require("class-transformer");
let BaseSystemConfigService = class BaseSystemConfigService {
    systemConfigRepository;
    dataSource;
    eventEmitter;
    logger = new common_1.Logger('SystemConfigService');
    validRelations = ['creator', 'updater', 'deleter'];
    EVENT_SYSTEM_CONFIG_CREATED = 'system-config.created';
    EVENT_SYSTEM_CONFIG_UPDATED = 'system-config.updated';
    EVENT_SYSTEM_CONFIG_DELETED = 'system-config.deleted';
    EVENT_SYSTEM_CONFIG_RESTORED = 'system-config.restored';
    constructor(systemConfigRepository, dataSource, eventEmitter) {
        this.systemConfigRepository = systemConfigRepository;
        this.dataSource = dataSource;
        this.eventEmitter = eventEmitter;
    }
    validateRelations(relations) {
        return relations.filter(relation => this.validRelations.includes(relation));
    }
    async findByIdOrFail(id, relations = [], withDeleted = false) {
        this.logger.debug(`Tìm cấu hình với ID: ${id}, withDeleted: ${withDeleted}, relations: ${JSON.stringify(relations)}`);
        const count = await this.systemConfigRepository.count({
            where: { id },
            withDeleted: true,
        });
        const exists = count > 0;
        this.logger.debug(`Cấu hình với ID ${id} tồn tại: ${exists}`);
        const validatedRelations = this.validateRelations(relations);
        const systemConfig = await this.systemConfigRepository.findOne({
            where: { id },
            relations: validatedRelations,
            withDeleted: withDeleted,
        });
        if (!systemConfig) {
            throw new common_1.NotFoundException(`Không tìm thấy cấu hình với ID "${id}"${withDeleted ? '' : ' hoặc đã bị xóa'}`);
        }
        return systemConfig;
    }
    async findByKeyOrFail(key, relations = [], withDeleted = false) {
        this.logger.debug(`Tìm cấu hình với khóa: ${key}, withDeleted: ${withDeleted}, relations: ${JSON.stringify(relations)}`);
        const validatedRelations = this.validateRelations(relations);
        const systemConfig = await this.systemConfigRepository.findOne({
            where: { configKey: key },
            relations: validatedRelations,
            withDeleted: withDeleted,
        });
        if (!systemConfig) {
            throw new common_1.NotFoundException(`Không tìm thấy cấu hình với khóa "${key}"${withDeleted ? '' : ' hoặc đã bị xóa'}`);
        }
        return systemConfig;
    }
    toDto(systemConfig) {
        return (0, class_transformer_1.plainToInstance)(system_config_dto_1.SystemConfigDto, systemConfig, {
            excludeExtraneousValues: true,
        });
    }
};
exports.BaseSystemConfigService = BaseSystemConfigService;
exports.BaseSystemConfigService = BaseSystemConfigService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(system_config_entity_1.SystemConfig)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        typeorm_2.DataSource,
        event_emitter_1.EventEmitter2])
], BaseSystemConfigService);
//# sourceMappingURL=base.system-config.service.js.map
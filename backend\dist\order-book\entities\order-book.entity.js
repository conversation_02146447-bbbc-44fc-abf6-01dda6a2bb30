"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.OrderBook = void 0;
const openapi = require("@nestjs/swagger");
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
const typeorm_1 = require("typeorm");
const user_entity_1 = require("../../users/entities/user.entity");
const order_type_enum_1 = require("../enums/order-type.enum");
const order_status_enum_1 = require("../enums/order-status.enum");
const business_type_enum_1 = require("../enums/business-type.enum");
const approve_status_enum_1 = require("../enums/approve-status.enum");
const order_book_detail_entity_1 = require("./order-book-detail.entity");
const base_entity_1 = require("../../common/entities/base.entity");
let OrderBook = class OrderBook extends base_entity_1.BaseEntity {
    userId;
    orderType;
    status;
    businessType;
    totalPrice;
    processingPrice;
    depositPrice;
    storageFee;
    settlementPrice;
    totalPriceFinal;
    contractNumber;
    settlementDeadline;
    settlementAt;
    approveStatus;
    approvedAt;
    user;
    getEntityName() {
        return 'order_book';
    }
    details;
    static _OPENAPI_METADATA_FACTORY() {
        return { userId: { required: true, type: () => String }, orderType: { required: true, enum: require("../enums/order-type.enum").OrderType }, status: { required: true, enum: require("../enums/order-status.enum").OrderStatus }, businessType: { required: true, enum: require("../enums/business-type.enum").BusinessType }, totalPrice: { required: true, type: () => Number }, processingPrice: { required: true, type: () => Number }, depositPrice: { required: true, type: () => Number }, storageFee: { required: true, type: () => Number }, settlementPrice: { required: true, type: () => Number }, totalPriceFinal: { required: true, type: () => Number }, contractNumber: { required: true, type: () => String }, settlementDeadline: { required: true, type: () => Date }, settlementAt: { required: true, type: () => Date }, approveStatus: { required: true, enum: require("../enums/approve-status.enum").ApproveStatus }, approvedAt: { required: true, type: () => Date }, user: { required: true, type: () => require("../../users/entities/user.entity").User }, details: { required: true, type: () => [require("./order-book-detail.entity").OrderBookDetail] } };
    }
};
exports.OrderBook = OrderBook;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'ID người dùng',
        example: '550e8400-e29b-41d4-a716-************',
    }),
    (0, class_validator_1.IsNotEmpty)(),
    (0, class_validator_1.IsString)(),
    (0, typeorm_1.Column)({ type: 'uuid', nullable: false, name: 'user_id' }),
    __metadata("design:type", String)
], OrderBook.prototype, "userId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Loại lệnh (mua/bán)',
        enum: order_type_enum_1.OrderType,
        example: order_type_enum_1.OrderType.BUY,
    }),
    (0, class_validator_1.IsNotEmpty)(),
    (0, class_validator_1.IsEnum)(order_type_enum_1.OrderType),
    (0, typeorm_1.Column)({
        type: 'varchar',
        length: 50,
        nullable: false,
        default: order_type_enum_1.OrderType.BUY,
        name: 'order_type',
    }),
    __metadata("design:type", String)
], OrderBook.prototype, "orderType", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Trạng thái lệnh (mặc định là đã đặt cọc)',
        enum: order_status_enum_1.OrderStatus,
        example: order_status_enum_1.OrderStatus.DEPOSITED,
    }),
    (0, class_validator_1.IsNotEmpty)(),
    (0, class_validator_1.IsEnum)(order_status_enum_1.OrderStatus),
    (0, typeorm_1.Column)({
        type: 'varchar',
        length: 50,
        nullable: false,
        default: order_status_enum_1.OrderStatus.DEPOSITED,
        name: 'status',
    }),
    __metadata("design:type", String)
], OrderBook.prototype, "status", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Loại hình giao dịch',
        enum: business_type_enum_1.BusinessType,
        example: business_type_enum_1.BusinessType.NORMAL,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)(business_type_enum_1.BusinessType),
    (0, typeorm_1.Column)({
        type: 'varchar',
        length: 50,
        nullable: false,
        default: business_type_enum_1.BusinessType.NORMAL,
        name: 'business_type',
    }),
    __metadata("design:type", String)
], OrderBook.prototype, "businessType", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Tổng tiền đơn hàng', example: '5000000.00' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsDecimal)({ decimal_digits: '2' }),
    (0, typeorm_1.Column)({
        type: 'decimal',
        precision: 20,
        scale: 2,
        nullable: true,
        name: 'total_price',
    }),
    __metadata("design:type", Number)
], OrderBook.prototype, "totalPrice", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Tổng tiền gia công', example: '5000000.00' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsDecimal)({ decimal_digits: '2' }),
    (0, typeorm_1.Column)({
        type: 'decimal',
        precision: 20,
        scale: 2,
        nullable: true,
        name: 'processing_price',
    }),
    __metadata("design:type", Number)
], OrderBook.prototype, "processingPrice", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Tổng tiền cọc',
        example: '1000000.00',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsDecimal)({ decimal_digits: '2' }),
    (0, typeorm_1.Column)({
        type: 'decimal',
        precision: 20,
        scale: 2,
        nullable: true,
        name: 'deposit_price',
    }),
    __metadata("design:type", Number)
], OrderBook.prototype, "depositPrice", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Phí lưu kho',
        example: '50000.00',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsDecimal)({ decimal_digits: '2' }),
    (0, typeorm_1.Column)({
        type: 'decimal',
        precision: 20,
        scale: 2,
        nullable: true,
        name: 'storage_fee',
    }),
    __metadata("design:type", Number)
], OrderBook.prototype, "storageFee", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Tổng tiền tất toán',
        example: '4000000.00',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsDecimal)({ decimal_digits: '2' }),
    (0, typeorm_1.Column)({
        type: 'decimal',
        precision: 20,
        scale: 2,
        nullable: true,
        name: 'settlement_price',
    }),
    __metadata("design:type", Number)
], OrderBook.prototype, "settlementPrice", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Tổng tiền cuối', example: '5000000.00' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsDecimal)({ decimal_digits: '2' }),
    (0, typeorm_1.Column)({
        type: 'decimal',
        precision: 20,
        scale: 2,
        nullable: true,
        name: 'total_price_final',
    }),
    __metadata("design:type", Number)
], OrderBook.prototype, "totalPriceFinal", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Số hợp đồng',
        example: 'HD-2023-001',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, typeorm_1.Column)({
        type: 'varchar',
        length: 255,
        nullable: true,
        unique: true,
        name: 'contract_number',
    }),
    __metadata("design:type", String)
], OrderBook.prototype, "contractNumber", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Hạn chót tất toán',
        example: '2023-12-31T23:59:59Z',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsDate)(),
    (0, typeorm_1.Column)({
        type: 'timestamp',
        nullable: true,
        name: 'settlement_deadline',
    }),
    __metadata("design:type", Date)
], OrderBook.prototype, "settlementDeadline", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Thời gian tất toán',
        example: '2023-12-15T10:30:00Z',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsDate)(),
    (0, typeorm_1.Column)({
        type: 'timestamp',
        nullable: true,
        name: 'settlement_at',
    }),
    __metadata("design:type", Date)
], OrderBook.prototype, "settlementAt", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Trạng thái phê duyệt',
        enum: approve_status_enum_1.ApproveStatus,
        example: approve_status_enum_1.ApproveStatus.PENDING,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)(approve_status_enum_1.ApproveStatus),
    (0, typeorm_1.Column)({
        type: 'varchar',
        length: 50,
        nullable: false,
        default: approve_status_enum_1.ApproveStatus.PENDING,
        name: 'approve_status',
    }),
    __metadata("design:type", String)
], OrderBook.prototype, "approveStatus", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Thời gian phê duyệt',
        example: '2023-12-15T10:30:00Z',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsDate)(),
    (0, typeorm_1.Column)({
        type: 'timestamp',
        nullable: true,
        name: 'approved_at',
    }),
    __metadata("design:type", Date)
], OrderBook.prototype, "approvedAt", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Người dùng liên kết' }),
    (0, typeorm_1.ManyToOne)(() => user_entity_1.User),
    (0, typeorm_1.JoinColumn)({ name: 'user_id' }),
    (0, swagger_1.ApiProperty)({
        type: () => user_entity_1.User,
        description: 'Người dùng liên quan đến đơn hàng',
    }),
    __metadata("design:type", user_entity_1.User)
], OrderBook.prototype, "user", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Chi tiết đơn hàng' }),
    (0, typeorm_1.OneToMany)(() => order_book_detail_entity_1.OrderBookDetail, (detail) => detail.orderBook),
    (0, swagger_1.ApiProperty)({
        type: () => order_book_detail_entity_1.OrderBookDetail,
        isArray: true,
        description: 'Chi tiết các sản phẩm trong đơn hàng',
    }),
    __metadata("design:type", Array)
], OrderBook.prototype, "details", void 0);
exports.OrderBook = OrderBook = __decorate([
    (0, typeorm_1.Entity)('order_book')
], OrderBook);
//# sourceMappingURL=order-book.entity.js.map
import { Repository, DataSource } from 'typeorm';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { BaseCmsPagesService } from './base.cms-pages.service';
import { CmsPages } from '../entity/cms-pages.entity';
import { CreateCmsPageDto } from '../dto/create.cms-page.dto';
import { CmsPageDto } from '../dto/cms-page.dto';
export declare class CreateCmsPagesService extends BaseCmsPagesService {
    protected readonly pageRepository: Repository<CmsPages>;
    protected readonly dataSource: DataSource;
    protected readonly eventEmitter: EventEmitter2;
    constructor(pageRepository: Repository<CmsPages>, dataSource: DataSource, eventEmitter: EventEmitter2);
    create(createDto: CreateCmsPageDto, userId: string): Promise<CmsPageDto>;
    bulkCreate(createDtos: CreateCmsPageDto[], userId: string): Promise<CmsPageDto[]>;
    createFromTemplate(templateName: string, title: string, userId: string): Promise<CmsPageDto>;
    duplicate(sourceId: string, newTitle: string | undefined, userId: string): Promise<CmsPageDto>;
    importPages(pagesData: any[], userId: string): Promise<CmsPageDto[]>;
    private getTemplateContent;
}

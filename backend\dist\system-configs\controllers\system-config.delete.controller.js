"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var SystemConfigDeleteController_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.SystemConfigDeleteController = void 0;
const openapi = require("@nestjs/swagger");
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const jwt_auth_guard_1 = require("../../common/guards/jwt-auth.guard");
const delete_system_config_service_1 = require("../services/delete.system-config.service");
const api_response_dto_1 = require("../../common/dto/api-response.dto");
const get_user_decorator_1 = require("../../common/decorators/get-user.decorator");
const roles_decorator_1 = require("../../common/decorators/roles.decorator");
const permissions_decorator_1 = require("../../common/decorators/permissions.decorator");
let SystemConfigDeleteController = SystemConfigDeleteController_1 = class SystemConfigDeleteController {
    systemConfigService;
    logger = new common_1.Logger(SystemConfigDeleteController_1.name);
    constructor(systemConfigService) {
        this.systemConfigService = systemConfigService;
    }
    async softDelete(id, userId) {
        this.logger.debug(`Đang xóa mềm cấu hình hệ thống với ID: ${id}`);
        return this.systemConfigService.softDelete(id, userId);
    }
    async softDeleteByKey(key, userId) {
        this.logger.debug(`Đang xóa mềm cấu hình hệ thống với khóa: ${key}`);
        return this.systemConfigService.softDeleteByKey(key, userId);
    }
    async restore(id) {
        this.logger.debug(`Đang khôi phục cấu hình hệ thống với ID: ${id}`);
        return this.systemConfigService.restore(id);
    }
    async hardDelete(id) {
        this.logger.debug(`Đang xóa cứng cấu hình hệ thống với ID: ${id}`);
        return this.systemConfigService.hardDelete(id);
    }
};
exports.SystemConfigDeleteController = SystemConfigDeleteController;
__decorate([
    (0, common_1.Delete)(':id'),
    (0, roles_decorator_1.Roles)('ADMIN'),
    (0, permissions_decorator_1.Permissions)('config:delete'),
    (0, swagger_1.ApiOperation)({ summary: 'Xóa mềm cấu hình hệ thống theo ID' }),
    (0, swagger_1.ApiParam)({
        name: 'id',
        description: 'ID của cấu hình hệ thống cần xóa',
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Cấu hình hệ thống được xóa mềm thành công.',
        type: api_response_dto_1.ApiResponseDto,
        schema: {
            properties: { data: { $ref: '#/components/schemas/SystemConfigDto' } },
        },
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.NOT_FOUND,
        description: 'Không tìm thấy cấu hình hệ thống.',
        type: api_response_dto_1.ApiResponseDto,
        schema: { properties: { data: { type: 'object', nullable: true } } },
    }),
    openapi.ApiResponse({ status: 200, type: require("../dto/system-config.dto").SystemConfigDto }),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __param(1, (0, get_user_decorator_1.GetUser)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String]),
    __metadata("design:returntype", Promise)
], SystemConfigDeleteController.prototype, "softDelete", null);
__decorate([
    (0, common_1.Delete)('key/:key'),
    (0, roles_decorator_1.Roles)('ADMIN'),
    (0, permissions_decorator_1.Permissions)('config:delete'),
    (0, swagger_1.ApiOperation)({ summary: 'Xóa mềm cấu hình hệ thống theo khóa' }),
    (0, swagger_1.ApiParam)({
        name: 'key',
        description: 'Khóa của cấu hình hệ thống cần xóa',
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Cấu hình hệ thống được xóa mềm thành công.',
        type: api_response_dto_1.ApiResponseDto,
        schema: {
            properties: { data: { $ref: '#/components/schemas/SystemConfigDto' } },
        },
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.NOT_FOUND,
        description: 'Không tìm thấy cấu hình hệ thống.',
        type: api_response_dto_1.ApiResponseDto,
        schema: { properties: { data: { type: 'object', nullable: true } } },
    }),
    openapi.ApiResponse({ status: 200, type: require("../dto/system-config.dto").SystemConfigDto }),
    __param(0, (0, common_1.Param)('key')),
    __param(1, (0, get_user_decorator_1.GetUser)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String]),
    __metadata("design:returntype", Promise)
], SystemConfigDeleteController.prototype, "softDeleteByKey", null);
__decorate([
    (0, common_1.Post)('restore/:id'),
    (0, roles_decorator_1.Roles)('ADMIN'),
    (0, permissions_decorator_1.Permissions)('config:restore'),
    (0, swagger_1.ApiOperation)({ summary: 'Khôi phục cấu hình hệ thống đã xóa mềm' }),
    (0, swagger_1.ApiParam)({
        name: 'id',
        description: 'ID của cấu hình hệ thống cần khôi phục',
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Cấu hình hệ thống được khôi phục thành công.',
        type: api_response_dto_1.ApiResponseDto,
        schema: {
            properties: { data: { $ref: '#/components/schemas/SystemConfigDto' } },
        },
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.NOT_FOUND,
        description: 'Không tìm thấy cấu hình hệ thống.',
        type: api_response_dto_1.ApiResponseDto,
        schema: { properties: { data: { type: 'object', nullable: true } } },
    }),
    openapi.ApiResponse({ status: 201, type: require("../dto/system-config.dto").SystemConfigDto }),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], SystemConfigDeleteController.prototype, "restore", null);
__decorate([
    (0, common_1.Delete)('hard/:id'),
    (0, roles_decorator_1.Roles)('ADMIN'),
    (0, permissions_decorator_1.Permissions)('config:delete'),
    (0, swagger_1.ApiOperation)({ summary: 'Xóa cứng cấu hình hệ thống' }),
    (0, swagger_1.ApiParam)({
        name: 'id',
        description: 'ID của cấu hình hệ thống cần xóa cứng',
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Cấu hình hệ thống được xóa cứng thành công.',
        type: api_response_dto_1.ApiResponseDto,
        schema: {
            properties: { data: { type: 'boolean' } },
        },
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.NOT_FOUND,
        description: 'Không tìm thấy cấu hình hệ thống.',
        type: api_response_dto_1.ApiResponseDto,
        schema: { properties: { data: { type: 'object', nullable: true } } },
    }),
    openapi.ApiResponse({ status: 200, type: Boolean }),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], SystemConfigDeleteController.prototype, "hardDelete", null);
exports.SystemConfigDeleteController = SystemConfigDeleteController = SystemConfigDeleteController_1 = __decorate([
    (0, swagger_1.ApiTags)('system-configs'),
    (0, swagger_1.ApiBearerAuth)(),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, common_1.Controller)('system-configs'),
    __metadata("design:paramtypes", [delete_system_config_service_1.DeleteSystemConfigService])
], SystemConfigDeleteController);
//# sourceMappingURL=system-config.delete.controller.js.map
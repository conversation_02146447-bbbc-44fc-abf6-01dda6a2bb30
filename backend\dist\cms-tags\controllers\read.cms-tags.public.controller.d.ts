import { ReadCmsTagsService } from '../services/read.cms-tags.service';
import { CmsTagDto } from '../dto/cms-tag.dto';
import { CustomPaginationQueryDto } from '../../common/dto/custom-pagination-query.dto';
import { PaginationResponseDto } from '../../common/dto/pagination-response.dto';
export declare class ReadCmsTagsPublicController {
    private readonly cmsTagsService;
    constructor(cmsTagsService: ReadCmsTagsService);
    getTags(paginationQuery: CustomPaginationQueryDto): Promise<PaginationResponseDto<CmsTagDto>>;
    getPopularTags(limit?: number): Promise<CmsTagDto[]>;
    findBySlug(slug: string): Promise<CmsTagDto | null>;
    searchTags(keyword: string, paginationQuery: CustomPaginationQueryDto): Promise<PaginationResponseDto<CmsTagDto>>;
    getStatistics(): Promise<{
        total: number;
        mostUsed: Array<{
            name: string;
            slug: string;
            postsCount: number;
        }>;
    }>;
}

'use client';

import { format } from 'date-fns';
import { vi } from 'date-fns/locale';
import {
  Calendar,
  CheckCircle,
  Clock,
  Coins,
  FileText,
  Image,
  Loader2,
  Pencil,
  Tag,
  User,
  Weight
} from 'lucide-react';
import { useEffect, useState } from 'react';

import {
  Sheet,
  SheetContent,
  SheetDescription,
  SheetHeader,
  SheetTitle,
} from '@/components/ui/sheet';

import { UserHoverCard } from '@/components/common/user/user-hover-card';
import { InfoCard } from '@/components/info-card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { api } from '@/lib/api';
import { toast } from 'sonner';
import { EcomProduct } from './type/ecom-product';

interface EcomProductDetailSheetProps {
  product: EcomProduct | null;
  isOpen: boolean;
  onClose: () => void;
  onEdit: (product: EcomProduct) => void;
}

export function EcomProductDetailSheet({ product: initialProduct, isOpen, onClose, onEdit }: EcomProductDetailSheetProps) {
  const [product, setProduct] = useState<EcomProduct | null>(initialProduct);
  const [loading, setLoading] = useState<boolean>(false);

  // Fetch detailed product information when sheet is opened
  useEffect(() => {
    if (isOpen && initialProduct?.id) {
      setLoading(true);
      api.get<EcomProduct>(`ecom-products/${initialProduct.id}?relations=category,creator,updater,deleter`)
        .then(response => {
          setProduct(response);
          setLoading(false);
        })
        .catch(error => {
          console.error('Error fetching product details:', error);
          toast.error('Không thể tải thông tin chi tiết sản phẩm');
          setLoading(false);
        });
    }
  }, [isOpen, initialProduct?.id]);

  if (!product) return null;

  const formatDate = (dateStr: string | Date | null | undefined) => {
    if (!dateStr) return '---';
    try {
      return format(new Date(dateStr), 'dd/MM/yyyy HH:mm', { locale: vi });
    } catch (error) {
      return 'Không hợp lệ';
    }
  };

  const getStatusBadge = (isActive: boolean | undefined) => {
    if (isActive === undefined) return null;
    return (
      <Badge
        variant={isActive ? 'default' : 'secondary'}
      >
        {isActive ? 'Hoạt động' : 'Vô hiệu hóa'}
      </Badge>
    );
  };

  return (
    <Sheet open={isOpen} onOpenChange={(open) => !open && onClose()}>
      <SheetContent className="w-full sm:max-w-xl md:max-w-2xl lg:max-w-3xl flex flex-col p-0">
        {/* Fixed header */}
        <SheetHeader className="px-0 py-0 border-b">
          <div className="flex items-center gap-4 px-4 py-2">
            <div className="h-12 w-12 rounded-full flex items-center justify-center">
              <Coins className="h-6 w-6 text-primary" />
            </div>
            <div className="flex-1">
              <SheetTitle className="text-md font-semibold">
                {loading ? (
                  <span className="flex items-center gap-2">
                    <Loader2 className="h-4 w-4 animate-spin" />
                    <span>Đang tải...</span>
                  </span>
                ) : (
                  product.productName
                )}
              </SheetTitle>
              <SheetDescription className="flex items-center gap-2">
                {loading ? (
                  <span className="flex items-center gap-2">
                    <Loader2 className="h-3 w-3 animate-spin" />
                    <span>Đang tải thông tin...</span>
                  </span>
                ) : (
                  <>
                    <span>Mã: {product.productCode}</span>
                    {getStatusBadge(product.isActive)}
                  </>
                )}
              </SheetDescription>
            </div>
          </div>

          {/* Divider */}
          <div className="h-px w-full bg-border"></div>

          {/* Action buttons */}
          <div className="flex items-center gap-2 px-4 py-2">
            <Button
              variant="outline"
              size="sm"
              className="flex items-center gap-1"
              onClick={() => onEdit(product)}
            >
              <Pencil className="h-3.5 w-3.5" />
              <span>Chỉnh sửa</span>
            </Button>
          </div>
        </SheetHeader>

        {/* Scrollable content */}
        <div className="flex-1 overflow-y-auto px-4">
          {loading ? (
            <div className="flex flex-col items-center justify-center h-full py-8 gap-4">
              <Loader2 className="h-8 w-8 animate-spin text-primary" />
              <span className="text-muted-foreground">Đang tải thông tin chi tiết...</span>
            </div>
          ) : (
            <>
              {/* Basic information */}
              <InfoCard
                title="Thông tin cơ bản"
                description="Thông tin cơ bản của sản phẩm"
                className="py-4"
              >
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-1">
                    <div className="text-xs font-medium text-muted-foreground flex items-center gap-1">
                      <Coins className="h-3.5 w-3.5" /> Tên sản phẩm
                    </div>
                    <div className="text-md">{product.productName}</div>
                  </div>
                  <div className="space-y-1">
                    <div className="text-xs font-medium text-muted-foreground flex items-center gap-1">
                      <Tag className="h-3.5 w-3.5" /> Mã sản phẩm
                    </div>
                    <div className="text-md">{product.productCode}</div>
                  </div>
                  <div className="space-y-1">
                    <div className="text-xs font-medium text-muted-foreground flex items-center gap-1">
                      <FileText className="h-3.5 w-3.5" /> Danh mục
                    </div>
                    <div className="text-md">{product.category?.name || '---'}</div>
                  </div>
                  <div className="space-y-1">
                    <div className="text-xs font-medium text-muted-foreground flex items-center gap-1">
                      <CheckCircle className="h-3.5 w-3.5" /> Trạng thái
                    </div>
                    <div className="text-md">{getStatusBadge(product.isActive)}</div>
                  </div>
                  <div className="space-y-1">
                    <div className="text-xs font-medium text-muted-foreground flex items-center gap-1">
                      <Weight className="h-3.5 w-3.5" /> Oz
                    </div>
                    <div className="text-md">{product.weight || '---'}</div>
                  </div>
                  {product.description && (
                    <div className="space-y-1 col-span-2">
                      <div className="text-xs font-medium text-muted-foreground flex items-center gap-1">
                        <FileText className="h-3.5 w-3.5" /> Mô tả
                      </div>
                      <div className="text-md" dangerouslySetInnerHTML={{ __html: product.description }}></div>
                    </div>
                  )}
                  {product.imageUrl && (
                    <div className="space-y-1 col-span-2">
                      <div className="text-xs font-medium text-muted-foreground flex items-center gap-1">
                        <Image className="h-3.5 w-3.5" /> Hình ảnh sản phẩm
                      </div>
                      <div className="mt-2">
                        <img
                          src={product.imageUrl}
                          alt={product.productName}
                          className="max-w-full h-auto max-h-64 rounded-lg border object-contain"
                        />
                      </div>
                    </div>
                  )}
                </div>
              </InfoCard>



              {/* Thông tin hệ thống */}
              <InfoCard
                title="Thông tin hệ thống"
                description="Thông tin hệ thống của sản phẩm"
                className="py-4"
              >
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">

                  <div className="space-y-1">
                    <div className="text-xs font-medium text-muted-foreground flex items-center gap-1">
                      <Calendar className="h-3.5 w-3.5" /> Ngày tạo
                    </div>
                    <div className="text-md">{formatDate(product.createdAt)}</div>
                  </div>
                  <div className="space-y-1">
                    <div className="text-xs font-medium text-muted-foreground flex items-center gap-1">
                      <Clock className="h-3.5 w-3.5" /> Cập nhật lần cuối
                    </div>
                    <div className="text-md">{formatDate(product.updatedAt)}</div>
                  </div>
                  <div className="space-y-1">
                    <div className="text-xs font-medium text-muted-foreground flex items-center gap-1">
                      <User className="h-3.5 w-3.5" /> Người tạo
                    </div>
                    <div>
                      {product.creator ? (
                        <UserHoverCard user={product.creator} showAvatar={true} size="sm">
                          <span className="max-w-[120px] overflow-hidden">
                            <span className="text-md truncate">{product.creator.fullName || product.creator.username || 'User'}</span>
                          </span>
                        </UserHoverCard>
                      ) : product.createdBy ? (
                        <UserHoverCard userId={product.createdBy as string} showAvatar={true} size="sm">
                          <span className="flex items-center gap-1">
                            <User className="h-4 w-4 text-blue-500" />
                            <span className="text-xs">
                              {(product.createdBy as string).substring(0, 8)}...
                            </span>
                          </span>
                        </UserHoverCard>
                      ) : (
                        <span className="text-md">Không có thông tin</span>
                      )}
                    </div>
                  </div>
                  <div className="space-y-1">
                    <div className="text-xs font-medium text-muted-foreground flex items-center gap-1">
                      <User className="h-3.5 w-3.5" /> Người cập nhật
                    </div>
                    <div>
                      {product.updater ? (
                        <UserHoverCard user={product.updater} showAvatar={true} size="sm">
                          <span className="max-w-[120px] overflow-hidden">
                            <span className="text-md truncate">{product.updater.fullName || product.updater.username || 'User'}</span>
                          </span>
                        </UserHoverCard>
                      ) : product.updatedBy ? (
                        <UserHoverCard userId={product.updatedBy as string} showAvatar={true} size="sm">
                          <span className="flex items-center gap-1">
                            <User className="h-4 w-4 text-blue-500" />
                            <span className="text-xs">
                              {(product.updatedBy as string).substring(0, 8)}...
                            </span>
                          </span>
                        </UserHoverCard>
                      ) : (
                        <span className="text-md">Không có thông tin</span>
                      )}
                    </div>
                  </div>
                </div>
              </InfoCard>


            </>
          )}
        </div>
      </SheetContent>
    </Sheet>
  );
}

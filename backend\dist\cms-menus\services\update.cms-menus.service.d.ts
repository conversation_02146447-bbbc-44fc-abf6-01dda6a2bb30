import { Repository, DataSource } from 'typeorm';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { BaseCmsMenusService } from './base.cms-menus.service';
import { CmsMenus, CmsMenuStatus } from '../entity/cms-menus.entity';
import { UpdateCmsMenuDto } from '../dto/update.cms-menu.dto';
import { CmsMenuDto } from '../dto/cms-menu.dto';
export declare class UpdateCmsMenusService extends BaseCmsMenusService {
    protected readonly menuRepository: Repository<CmsMenus>;
    protected readonly dataSource: DataSource;
    protected readonly eventEmitter: EventEmitter2;
    constructor(menuRepository: Repository<CmsMenus>, dataSource: DataSource, eventEmitter: EventEmitter2);
    update(id: string, updateDto: UpdateCmsMenuDto, userId: string): Promise<CmsMenuDto | null>;
    updateStatus(id: string, status: CmsMenuStatus, userId: string): Promise<CmsMenuDto | null>;
    updateSlugFromName(id: string, userId: string): Promise<CmsMenuDto | null>;
    private checkCircularReference;
}

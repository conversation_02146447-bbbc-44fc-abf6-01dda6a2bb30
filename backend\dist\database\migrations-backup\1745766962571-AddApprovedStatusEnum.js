"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AddApprovedStatusEnum1745766962571 = void 0;
class AddApprovedStatusEnum1745766962571 {
    async up(queryRunner) {
        await queryRunner.query(`
            DO $$
            BEGIN
                IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'approve_status_enum') THEN
                    CREATE TYPE "approve_status_enum" AS ENUM (
                        'WAIT_APPROVE',
                        'APPROVED'
                    );
                END IF;
            END
            $$;
        `);
        await queryRunner.query(`
            ALTER TABLE "order_book" 
            DROP COLUMN IF EXISTS "is_approved"
        `);
        await queryRunner.query(`
            ALTER TABLE "order_book" 
            ADD COLUMN IF NOT EXISTS "approve_status" approve_status_enum NULL
        `);
    }
    async down(queryRunner) {
        await queryRunner.query(`
            ALTER TABLE "order_book" 
            DROP COLUMN IF EXISTS "approve_status"
        `);
        await queryRunner.query(`
            ALTER TABLE "order_book" 
            ADD COLUMN IF NOT EXISTS "is_approved" BOOLEAN DEFAULT false
        `);
        await queryRunner.query(`
            DROP TYPE IF EXISTS "approve_status_enum"
        `);
    }
}
exports.AddApprovedStatusEnum1745766962571 = AddApprovedStatusEnum1745766962571;
//# sourceMappingURL=1745766962571-AddApprovedStatusEnum.js.map
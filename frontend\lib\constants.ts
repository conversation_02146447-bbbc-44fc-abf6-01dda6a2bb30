/**
 * Constants cho frontend application
 */

// API Pagination Limits (phải khớp với backend)
export const API_PAGINATION = {
  MAX_LIMIT: 100,           // Giới hạn tối đa từ backend PaginationQueryDto
  DEFAULT_LIMIT: 20,        // Limit mặc định
  DEFAULT_PAGE: 1,          // Page mặc định
  
  // Limits cho các endpoint đặc biệt
  CUSTOM_MAX_LIMIT: 300,    // Từ CustomPaginationQueryDto
  SEARCH_LIMIT: 10,         // Cho search/autocomplete
} as const;

// Validation Rules
export const VALIDATION = {
  PASSWORD_MIN_LENGTH: 8,
  USERNAME_MIN_LENGTH: 3,
  USERNAME_MAX_LENGTH: 20,
  PHONE_REGEX: /^[0-9]{10,11}$/,
  EMAIL_REGEX: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
} as const;

// Error Messages
export const ERROR_MESSAGES = {
  LIMIT_EXCEEDED: 'Số lượng yêu cầu vượt quá giới hạn cho phép',
  INVALID_PAGE: 'Số trang không hợp lệ',
  NETWORK_ERROR: 'Lỗi kết nối mạng',
  UNAUTHORIZED: 'Bạn không có quyền truy cập',
  FORBIDDEN: 'Truy cập bị từ chối',
  NOT_FOUND: 'Không tìm thấy tài nguyên',
  SERVER_ERROR: 'Lỗi máy chủ',
} as const;

// Success Messages
export const SUCCESS_MESSAGES = {
  CREATED: 'Tạo thành công',
  UPDATED: 'Cập nhật thành công',
  DELETED: 'Xóa thành công',
  SAVED: 'Lưu thành công',
} as const;

// Storage Keys
export const STORAGE_KEYS = {
  ACCESS_TOKEN: 'accessToken',
  REFRESH_TOKEN: 'refreshToken',
  USER_DATA: 'userData',
  THEME: 'theme',
  LANGUAGE: 'language',
} as const;

// API Endpoints Base
export const API_ENDPOINTS = {
  AUTH: '/auth',
  USERS: '/users',
  BANKS: '/banks',
  TRANSACTIONS: '/transactions',
  WALLETS: '/wallets',
  ORDERS: '/orders',
  PRODUCTS: '/products',
} as const;

// Table/DataGrid Settings
export const TABLE_SETTINGS = {
  DEFAULT_PAGE_SIZE: 20,
  PAGE_SIZE_OPTIONS: [10, 20, 50, 100],
  MAX_PAGE_SIZE: API_PAGINATION.MAX_LIMIT,
} as const;

// File Upload Limits
export const FILE_UPLOAD = {
  MAX_SIZE: 5 * 1024 * 1024, // 5MB
  ALLOWED_TYPES: ['image/jpeg', 'image/png', 'image/gif', 'application/pdf'],
  ALLOWED_EXTENSIONS: ['.jpg', '.jpeg', '.png', '.gif', '.pdf'],
} as const;

// Date/Time Formats
export const DATE_FORMATS = {
  DISPLAY: 'dd/MM/yyyy',
  DISPLAY_WITH_TIME: 'dd/MM/yyyy HH:mm',
  API: 'yyyy-MM-dd',
  API_WITH_TIME: "yyyy-MM-dd'T'HH:mm:ss.SSSxxx",
} as const;

// Currency
export const CURRENCY = {
  VND: 'VND',
  USD: 'USD',
  DEFAULT_EXCHANGE_RATE: 24000, // USD to VND
} as const;

// Timeouts
export const TIMEOUTS = {
  API_REQUEST: 30000,      // 30 seconds
  DEBOUNCE_SEARCH: 300,    // 300ms
  TOAST_DURATION: 5000,    // 5 seconds
  POLLING_INTERVAL: 5000,  // 5 seconds
} as const;

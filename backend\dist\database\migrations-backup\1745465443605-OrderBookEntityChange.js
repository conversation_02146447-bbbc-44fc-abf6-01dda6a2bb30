"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.OrderBookEntityChange1745465443605 = void 0;
class OrderBookEntityChange1745465443605 {
    async up(queryRunner) {
        try {
            await queryRunner.query(`
              CREATE TABLE "order_book_detail" (
                "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
                "order_book_id" uuid NOT NULL,
                "token_id" uuid NOT NULL,
                "price" decimal(20,2) NOT NULL,
                "volume" decimal(20,4) NOT NULL,
                "total_price" decimal(20,2) NOT NULL,
                "bid_price" decimal(20,2),
                "ask_price" decimal(20,2),
                "created_at" TIMESTAMP NOT NULL DEFAULT now(),
                "updated_at" TIMESTAMP NOT NULL DEFAULT now(),
                "created_by" uuid,
                "updated_by" uuid,
                CONSTRAINT "PK_order_book_detail" PRIMARY KEY ("id")
              )
            `);
            await queryRunner.query(`
              ALTER TABLE "order_book_detail" 
              ADD CONSTRAINT "FK_order_book_detail_order_book" 
              FOREIGN KEY ("order_book_id") REFERENCES "order_book"("id") ON DELETE CASCADE
            `);
            await queryRunner.query(`
              ALTER TABLE "order_book_detail" 
              ADD CONSTRAINT "FK_order_book_detail_token" 
              FOREIGN KEY ("token_id") REFERENCES "tokens"("id") ON DELETE CASCADE
            `);
            await queryRunner.query(`
              ALTER TABLE "order_book" DROP COLUMN IF EXISTS "token_id"
            `);
            await queryRunner.query(`
              ALTER TABLE "order_book" DROP COLUMN IF EXISTS "volume"
            `);
            await queryRunner.query(`
              ALTER TABLE "order_book" DROP COLUMN IF EXISTS "remaining_volume"
            `);
            await queryRunner.query(`
              ALTER TABLE "order_book" 
              RENAME COLUMN "price" TO "total_price"
            `);
            await queryRunner.query(`ALTER TABLE "order_book" ADD "temp_status" varchar`);
            await queryRunner.query(`
              UPDATE "order_book" SET "temp_status" = 
                CASE 
                  WHEN "status" = 'PENDING' THEN 'PENDING'
                  WHEN "status" = 'FILLED' THEN 'COMPLETED'
                  WHEN "status" = 'PARTIAL' THEN 'PENDING'
                  WHEN "status" = 'CANCELLED' THEN 'PENDING'
                  ELSE 'PENDING'
                END
            `);
            await queryRunner.query(`ALTER TABLE "order_book" DROP CONSTRAINT IF EXISTS "order_book_status_check"`);
            await queryRunner.query(`ALTER TABLE "order_book" DROP COLUMN "status"`);
            await queryRunner.query(`DROP TYPE IF EXISTS "public"."order_book_status_enum_new"`);
            await queryRunner.query(`CREATE TYPE "public"."order_book_status_enum_new" AS ENUM('PENDING', 'COMPLETED', 'WAIT_PAYMENT')`);
            await queryRunner.query(`ALTER TABLE "order_book" ADD "status" "public"."order_book_status_enum_new"`);
            await queryRunner.query(`UPDATE "order_book" SET "status" = "temp_status"::text::"public"."order_book_status_enum_new"`);
            await queryRunner.query(`ALTER TABLE "order_book" ALTER COLUMN "status" SET NOT NULL`);
            await queryRunner.query(`ALTER TABLE "order_book" ALTER COLUMN "status" SET DEFAULT 'PENDING'`);
            await queryRunner.query(`ALTER TABLE "order_book" DROP COLUMN "temp_status"`);
            await queryRunner.query(`DROP TYPE IF EXISTS "public"."order_book_status_enum"`);
            await queryRunner.query(`ALTER TYPE "public"."order_book_status_enum_new" RENAME TO "order_book_status_enum"`);
        }
        catch (error) {
            console.error('Migration failed:', error);
            throw error;
        }
    }
    async down(queryRunner) {
        try {
            await queryRunner.query(`DROP TYPE IF EXISTS "public"."order_book_status_enum_old"`);
            await queryRunner.query(`CREATE TYPE "public"."order_book_status_enum_old" AS ENUM('PENDING', 'PARTIAL', 'FILLED', 'CANCELLED', 'PENDING_SETTLEMENT')`);
            await queryRunner.query(`ALTER TABLE "order_book" ADD "temp_status" varchar`);
            await queryRunner.query(`
              UPDATE "order_book" SET "temp_status" = 
                CASE 
                  WHEN "status" = 'COMPLETED' THEN 'FILLED'
                  WHEN "status" = 'WAIT_PAYMENT' THEN 'PENDING_SETTLEMENT'
                  WHEN "status" = 'PENDING' THEN 'PENDING'
                  ELSE 'PENDING'
                END
            `);
            await queryRunner.query(`ALTER TABLE "order_book" DROP CONSTRAINT IF EXISTS "order_book_status_check"`);
            await queryRunner.query(`ALTER TABLE "order_book" DROP COLUMN "status"`);
            await queryRunner.query(`ALTER TABLE "order_book" ADD "status" "public"."order_book_status_enum_old"`);
            await queryRunner.query(`UPDATE "order_book" SET "status" = "temp_status"::text::"public"."order_book_status_enum_old"`);
            await queryRunner.query(`ALTER TABLE "order_book" ALTER COLUMN "status" SET NOT NULL`);
            await queryRunner.query(`ALTER TABLE "order_book" ALTER COLUMN "status" SET DEFAULT 'PENDING'`);
            await queryRunner.query(`ALTER TABLE "order_book" DROP COLUMN "temp_status"`);
            await queryRunner.query(`DROP TYPE IF EXISTS "public"."order_book_status_enum_new"`);
            await queryRunner.query(`ALTER TYPE "public"."order_book_status_enum_old" RENAME TO "order_book_status_enum"`);
            await queryRunner.query(`
              ALTER TABLE "order_book" 
              RENAME COLUMN "total_price" TO "price"
            `);
            await queryRunner.query(`
              ALTER TABLE "order_book" 
              ADD COLUMN "token_id" uuid
            `);
            await queryRunner.query(`
              ALTER TABLE "order_book" 
              ADD COLUMN "volume" decimal(20,4)
            `);
            await queryRunner.query(`
              ALTER TABLE "order_book" 
              ADD COLUMN "remaining_volume" decimal(20,4)
            `);
            await queryRunner.query(`
              UPDATE "order_book" o
              SET "token_id" = (
                SELECT "token_id" FROM "order_book_detail" od 
                WHERE od."order_book_id" = o."id" 
                LIMIT 1
              )
            `);
            await queryRunner.query(`
              UPDATE "order_book" o
              SET "volume" = (
                SELECT SUM("volume") FROM "order_book_detail" od 
                WHERE od."order_book_id" = o."id"
              )
            `);
            await queryRunner.query(`
              UPDATE "order_book"
              SET "remaining_volume" = "volume"
            `);
            await queryRunner.query(`
              ALTER TABLE "order_book_detail" DROP CONSTRAINT "FK_order_book_detail_token"
            `);
            await queryRunner.query(`
              ALTER TABLE "order_book_detail" DROP CONSTRAINT "FK_order_book_detail_order_book"
            `);
            await queryRunner.query(`
              DROP TABLE "order_book_detail"
            `);
        }
        catch (error) {
            console.error('Rollback failed:', error);
            throw error;
        }
    }
}
exports.OrderBookEntityChange1745465443605 = OrderBookEntityChange1745465443605;
//# sourceMappingURL=1745465443605-OrderBookEntityChange.js.map
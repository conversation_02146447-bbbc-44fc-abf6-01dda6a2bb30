"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var OrderExpirationService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.OrderExpirationService = void 0;
const common_1 = require("@nestjs/common");
const schedule_1 = require("@nestjs/schedule");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const order_book_entity_1 = require("../order-book/entities/order-book.entity");
const order_status_enum_1 = require("../order-book/enums/order-status.enum");
const order_type_enum_1 = require("../order-book/enums/order-type.enum");
const event_emitter_1 = require("@nestjs/event-emitter");
let OrderExpirationService = OrderExpirationService_1 = class OrderExpirationService {
    orderBookRepository;
    eventEmitter;
    logger = new common_1.Logger(OrderExpirationService_1.name);
    EVENT_ORDER_TERMINATED = 'order.terminated';
    constructor(orderBookRepository, eventEmitter) {
        this.orderBookRepository = orderBookRepository;
        this.eventEmitter = eventEmitter;
    }
    async checkExpiredOrders() {
        this.logger.log('[CRON - checkExpiredOrders] Bắt đầu kiểm tra các order book quá hạn tất toán');
        try {
            const now = new Date();
            const expiredOrderBooks = await this.orderBookRepository.find({
                where: {
                    orderType: order_type_enum_1.OrderType.BUY,
                    status: (0, typeorm_2.Not)((0, typeorm_2.In)([order_status_enum_1.OrderStatus.COMPLETED, order_status_enum_1.OrderStatus.TERMINATED])),
                    settlementDeadline: (0, typeorm_2.LessThan)(now),
                    isDeleted: false,
                },
                relations: ['details', 'user'],
            });
            this.logger.log(`[CRON - checkExpiredOrders] Tìm thấy ${expiredOrderBooks.length} order book đã quá hạn tất toán`);
            for (const orderBook of expiredOrderBooks) {
                await this.terminateExpiredOrderBook(orderBook);
            }
            this.logger.log('[CRON - checkExpiredOrders] Hoàn thành kiểm tra và cập nhật trạng thái các order book quá hạn tất toán');
        }
        catch (error) {
            this.logger.error(`[CRON - checkExpiredOrders] Lỗi khi kiểm tra các order book quá hạn tất toán: ${error.message}`, error.stack);
        }
    }
    async terminateExpiredOrderBook(orderBook) {
        try {
            this.logger.log(`[CRON - checkExpiredOrders] Đang chấm dứt order book ${orderBook.id} (Hạn tất toán: ${orderBook.settlementDeadline})`);
            await this.orderBookRepository.update(orderBook.id, {
                status: order_status_enum_1.OrderStatus.TERMINATED,
                updatedBy: 'system',
            });
            this.eventEmitter.emit(this.EVENT_ORDER_TERMINATED, {
                orderId: orderBook.id,
                userId: orderBook.userId,
                timestamp: new Date(),
                reason: 'Quá hạn tất toán',
                data: orderBook,
            });
            this.logger.log(`[CRON - checkExpiredOrders] Đã chấm dứt order book ${orderBook.id} do quá hạn tất toán`);
        }
        catch (error) {
            this.logger.error(`[CRON - checkExpiredOrders] Lỗi khi chấm dứt order book ${orderBook.id}: ${error.message}`, error.stack);
        }
    }
    async manualCheckExpiredOrders() {
        return this.checkExpiredOrders();
    }
};
exports.OrderExpirationService = OrderExpirationService;
__decorate([
    (0, schedule_1.Cron)(schedule_1.CronExpression.EVERY_DAY_AT_MIDNIGHT, {
        name: 'checkExpiredOrders',
        timeZone: 'Asia/Ho_Chi_Minh',
    }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], OrderExpirationService.prototype, "checkExpiredOrders", null);
exports.OrderExpirationService = OrderExpirationService = OrderExpirationService_1 = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(order_book_entity_1.OrderBook)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        event_emitter_1.EventEmitter2])
], OrderExpirationService);
//# sourceMappingURL=order-expiration.service.js.map
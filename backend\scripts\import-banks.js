const axios = require('axios');
const fs = require('fs');
const path = require('path');

/**
 * <PERSON>ript để import dữ liệu ngân hàng từ API banklookup.net
 * và test chức năng tạo mới ngân hàng trong hệ thống
 */

// C<PERSON><PERSON> hình
const CONFIG = {
  // API banklookup.net
  BANK_API_URL: 'https://api.banklookup.net/bank/list',
  
  // API backend local
  BACKEND_URL: 'http://localhost:3168',
  
  // Thông tin đăng nhập admin (cần thay đổi theo hệ thống thực tế)
  ADMIN_CREDENTIALS: {
    identity: '<EMAIL>',
    password: 'adminX@123'
  },
  
  // File output
  OUTPUT_DIR: './scripts/output',
  BANKS_JSON_FILE: './scripts/output/banks-data.json',
  LOG_FILE: './scripts/output/import-banks.log'
};

// Utility functions
const log = (message, type = 'INFO') => {
  const timestamp = new Date().toISOString();
  const logMessage = `[${timestamp}] [${type}] ${message}`;
  console.log(logMessage);
  
  // Ghi vào file log
  if (!fs.existsSync(CONFIG.OUTPUT_DIR)) {
    fs.mkdirSync(CONFIG.OUTPUT_DIR, { recursive: true });
  }
  fs.appendFileSync(CONFIG.LOG_FILE, logMessage + '\n');
};

const sleep = (ms) => new Promise(resolve => setTimeout(resolve, ms));

// Lấy dữ liệu từ API banklookup.net
async function fetchBankData() {
  try {
    log('Đang lấy dữ liệu từ API banklookup.net...');
    const response = await axios.get(CONFIG.BANK_API_URL);
    
    if (response.data.success && response.data.data) {
      log(`Lấy thành công ${response.data.data.length} ngân hàng từ API`);
      return response.data.data;
    } else {
      throw new Error('API trả về dữ liệu không hợp lệ');
    }
  } catch (error) {
    log(`Lỗi khi lấy dữ liệu từ API: ${error.message}`, 'ERROR');
    throw error;
  }
}

// Chuyển đổi dữ liệu từ API sang format của hệ thống
function transformBankData(apiData) {
  return apiData.map(bank => ({
    brandName: bank.short_name || bank.name,
    fullName: bank.name,
    shortName: bank.short_name || bank.code,
    code: bank.code,
    bin: bank.bin.toString(),
    logoPath: bank.logo_url || null,
    icon: bank.icon_url || null,
    isActive: bank.lookup_supported === 1
    // Loại bỏ metadata vì backend không chấp nhận field này
  }));
}

// Đăng nhập và lấy JWT token
async function loginAndGetToken() {
  try {
    log('Đang đăng nhập để lấy JWT token...');
    const loginData = {
      identity: CONFIG.ADMIN_CREDENTIALS.identity,
      password: CONFIG.ADMIN_CREDENTIALS.password
    };
    const response = await axios.post(`${CONFIG.BACKEND_URL}/api/v1/auth/login`, loginData);

    log(`Response status: ${response.status}`);
    log(`Response data: ${JSON.stringify(response.data, null, 2)}`);

    // Kiểm tra các cấu trúc response có thể có
    let accessToken = null;

    if (response.data) {
      // Cấu trúc 1: response.data.access_token
      if (response.data.access_token) {
        accessToken = response.data.access_token;
      }
      // Cấu trúc 2: response.data.data.access_token
      else if (response.data.data && response.data.data.access_token) {
        accessToken = response.data.data.access_token;
      }
      // Cấu trúc 3: response.data.accessToken
      else if (response.data.accessToken) {
        accessToken = response.data.accessToken;
      }
    }

    if (accessToken) {
      log('Đăng nhập thành công');
      log(`Access token: ${accessToken.substring(0, 50)}...`);
      return accessToken;
    } else {
      log('Không tìm thấy access token trong response', 'ERROR');
      log(`Available fields: ${Object.keys(response.data || {})}`, 'ERROR');
      throw new Error('Không nhận được access token');
    }
  } catch (error) {
    log(`Lỗi khi đăng nhập: ${error.message}`, 'ERROR');
    if (error.response) {
      log(`Response status: ${error.response.status}`, 'ERROR');
      log(`Response data: ${JSON.stringify(error.response.data)}`, 'ERROR');
    }
    throw error;
  }
}

// Test tạo một ngân hàng
async function createSingleBank(bankData, token) {
  try {
    const response = await axios.post(
      `${CONFIG.BACKEND_URL}/api/v1/banks`,
      bankData,
      {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      }
    );
    
    return response.data;
  } catch (error) {
    if (error.response) {
      throw new Error(`HTTP ${error.response.status}: ${JSON.stringify(error.response.data)}`);
    }
    throw error;
  }
}

// Test tạo nhiều ngân hàng cùng lúc
async function createBulkBanks(banksData, token, batchSize = 10) {
  try {
    const response = await axios.post(
      `${CONFIG.BACKEND_URL}/api/v1/banks/bulk`,
      banksData.slice(0, batchSize), // Chỉ test với một số lượng nhỏ
      {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      }
    );
    
    return response.data;
  } catch (error) {
    if (error.response) {
      throw new Error(`HTTP ${error.response.status}: ${JSON.stringify(error.response.data)}`);
    }
    throw error;
  }
}

// Test chính
async function runTest() {
  try {
    log('=== BẮT ĐẦU TEST IMPORT NGÂN HÀNG ===');
    
    // 1. Lấy dữ liệu từ API
    const rawBankData = await fetchBankData();
    
    // 2. Chuyển đổi dữ liệu
    log('Đang chuyển đổi dữ liệu...');
    const transformedData = transformBankData(rawBankData);
    
    // 3. Lưu dữ liệu đã chuyển đổi
    fs.writeFileSync(CONFIG.BANKS_JSON_FILE, JSON.stringify(transformedData, null, 2));
    log(`Đã lưu dữ liệu đã chuyển đổi vào ${CONFIG.BANKS_JSON_FILE}`);
    
    // 4. Đăng nhập
    const token = await loginAndGetToken();
    
    // 5. Test tạo một ngân hàng đầu tiên
    log('=== TEST TẠO MỘT NGÂN HÀNG ===');
    const firstBank = transformedData[0];
    log(`Đang test tạo ngân hàng: ${firstBank.fullName}`);
    
    try {
      const singleResult = await createSingleBank(firstBank, token);
      log(`✅ Tạo thành công ngân hàng: ${singleResult.fullName || singleResult.brandName}`);
      log(`   ID: ${singleResult.id}`);
      log(`   Code: ${singleResult.code}`);
    } catch (error) {
      log(`❌ Lỗi khi tạo ngân hàng đơn lẻ: ${error.message}`, 'ERROR');
    }
    
    await sleep(1000); // Đợi 1 giây
    
    // 6. Test tạo nhiều ngân hàng (chỉ test 5 ngân hàng)
    log('=== TEST TẠO NHIỀU NGÂN HÀNG ===');
    const testBanks = transformedData.slice(1, 6); // Lấy 5 ngân hàng tiếp theo
    log(`Đang test tạo ${testBanks.length} ngân hàng cùng lúc...`);
    
    try {
      const bulkResult = await createBulkBanks(testBanks, token);
      log(`✅ Tạo thành công ${bulkResult.length || testBanks.length} ngân hàng`);
      
      if (Array.isArray(bulkResult)) {
        bulkResult.forEach((bank, index) => {
          log(`   ${index + 1}. ${bank.fullName || bank.brandName} (${bank.code})`);
        });
      }
    } catch (error) {
      log(`❌ Lỗi khi tạo nhiều ngân hàng: ${error.message}`, 'ERROR');
    }
    
    // 7. Thống kê
    log('=== THỐNG KÊ ===');
    log(`Tổng số ngân hàng từ API: ${rawBankData.length}`);
    log(`Số ngân hàng hỗ trợ lookup: ${rawBankData.filter(b => b.lookup_supported === 1).length}`);
    log(`Số ngân hàng có SWIFT code: ${rawBankData.filter(b => b.swift_code).length}`);
    
    // 8. Tạo file mẫu để import thủ công
    const sampleData = transformedData.slice(0, 10);
    const sampleFile = path.join(CONFIG.OUTPUT_DIR, 'sample-banks.json');
    fs.writeFileSync(sampleFile, JSON.stringify(sampleData, null, 2));
    log(`Đã tạo file mẫu 10 ngân hàng: ${sampleFile}`);
    
    log('=== HOÀN THÀNH TEST ===');
    
  } catch (error) {
    log(`❌ Lỗi trong quá trình test: ${error.message}`, 'ERROR');
    process.exit(1);
  }
}

// Chạy script
if (require.main === module) {
  runTest().catch(error => {
    log(`❌ Lỗi không mong đợi: ${error.message}`, 'ERROR');
    process.exit(1);
  });
}

module.exports = {
  fetchBankData,
  transformBankData,
  runTest
};

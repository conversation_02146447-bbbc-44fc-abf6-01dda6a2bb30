"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CmsPostPublicDto = void 0;
const openapi = require("@nestjs/swagger");
const swagger_1 = require("@nestjs/swagger");
const class_transformer_1 = require("class-transformer");
const cms_posts_entity_1 = require("../entity/cms-posts.entity");
const cms_category_dto_1 = require("../../cms-categories/dto/cms-category.dto");
class CmsPostPublicDto {
    id;
    businessCode;
    postType;
    title;
    slug;
    excerpt;
    content;
    featuredImageUrl;
    status;
    publishedAt;
    eventStartDate;
    eventEndDate;
    eventLocation;
    viewCount;
    metaTitle;
    metaDescription;
    metaKeywords;
    allowComments;
    category;
    createdAt;
    updatedAt;
    canDisplayPublicly;
    hasFeaturedImage;
    hasExcerpt;
    hasCategory;
    postTypeDisplayName;
    statusDisplayName;
    key;
    url;
    isEvent;
    isEventOngoing;
    isEventUpcoming;
    isEventPast;
    estimatedReadingTime;
    summary;
    static _OPENAPI_METADATA_FACTORY() {
        return { id: { required: true, type: () => String }, businessCode: { required: true, type: () => String }, postType: { required: true, enum: require("../entity/cms-posts.entity").CmsPostType }, title: { required: true, type: () => String }, slug: { required: true, type: () => String }, excerpt: { required: false, type: () => String, nullable: true }, content: { required: true, type: () => String }, featuredImageUrl: { required: false, type: () => String, nullable: true }, status: { required: true, enum: require("../entity/cms-posts.entity").CmsPostStatus }, publishedAt: { required: false, type: () => Date, nullable: true }, eventStartDate: { required: false, type: () => Date, nullable: true }, eventEndDate: { required: false, type: () => Date, nullable: true }, eventLocation: { required: false, type: () => String, nullable: true }, viewCount: { required: true, type: () => Number }, metaTitle: { required: false, type: () => String, nullable: true }, metaDescription: { required: false, type: () => String, nullable: true }, metaKeywords: { required: false, type: () => String, nullable: true }, allowComments: { required: true, type: () => Boolean }, category: { required: false, type: () => require("../../cms-categories/dto/cms-category.dto").CmsCategoryDto, nullable: true }, createdAt: { required: true, type: () => Date }, updatedAt: { required: true, type: () => Date }, canDisplayPublicly: { required: true, type: () => Boolean }, hasFeaturedImage: { required: true, type: () => Boolean }, hasExcerpt: { required: true, type: () => Boolean }, hasCategory: { required: true, type: () => Boolean }, postTypeDisplayName: { required: true, type: () => String }, statusDisplayName: { required: true, type: () => String }, key: { required: true, type: () => String }, url: { required: true, type: () => String }, isEvent: { required: true, type: () => Boolean }, isEventOngoing: { required: true, type: () => Boolean }, isEventUpcoming: { required: true, type: () => Boolean }, isEventPast: { required: true, type: () => Boolean }, estimatedReadingTime: { required: true, type: () => Number }, summary: { required: true, type: () => String } };
    }
}
exports.CmsPostPublicDto = CmsPostPublicDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'ID của bài viết',
        example: '550e8400-e29b-41d4-a716-446655440000',
    }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", String)
], CmsPostPublicDto.prototype, "id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Mã kinh doanh của bài viết',
        example: 'CMP-240101-00001',
    }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", String)
], CmsPostPublicDto.prototype, "businessCode", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Loại bài viết',
        example: cms_posts_entity_1.CmsPostType.POST,
        enum: cms_posts_entity_1.CmsPostType,
    }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", String)
], CmsPostPublicDto.prototype, "postType", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Tiêu đề bài viết',
        example: 'Tin tức mới nhất về thị trường vàng',
    }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", String)
], CmsPostPublicDto.prototype, "title", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Chuỗi URL thân thiện',
        example: 'tin-tuc-moi-nhat-ve-thi-truong-vang',
    }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", String)
], CmsPostPublicDto.prototype, "slug", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Đoạn tóm tắt ngắn',
        example: 'Thị trường vàng tuần này có nhiều biến động...',
        nullable: true,
    }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", Object)
], CmsPostPublicDto.prototype, "excerpt", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Nội dung đầy đủ (HTML)',
        example: '<p>Nội dung chi tiết về thị trường vàng...</p>',
    }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", String)
], CmsPostPublicDto.prototype, "content", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'URL hình ảnh đại diện',
        example: 'https://example.com/images/featured.jpg',
        nullable: true,
    }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", Object)
], CmsPostPublicDto.prototype, "featuredImageUrl", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Trạng thái bài viết',
        example: cms_posts_entity_1.CmsPostStatus.PUBLISHED,
        enum: cms_posts_entity_1.CmsPostStatus,
    }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", String)
], CmsPostPublicDto.prototype, "status", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Thời gian xuất bản',
        example: '2023-01-01T00:00:00.000Z',
        nullable: true,
    }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", Object)
], CmsPostPublicDto.prototype, "publishedAt", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Ngày bắt đầu sự kiện (cho post_type = event)',
        example: '2023-01-01T09:00:00.000Z',
        nullable: true,
    }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", Object)
], CmsPostPublicDto.prototype, "eventStartDate", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Ngày kết thúc sự kiện (cho post_type = event)',
        example: '2023-01-01T17:00:00.000Z',
        nullable: true,
    }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", Object)
], CmsPostPublicDto.prototype, "eventEndDate", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Địa điểm sự kiện (cho post_type = event)',
        example: 'Trung tâm Hội nghị Quốc gia',
        nullable: true,
    }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", Object)
], CmsPostPublicDto.prototype, "eventLocation", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Số lượt xem',
        example: 100,
    }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", Number)
], CmsPostPublicDto.prototype, "viewCount", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Tiêu đề SEO',
        example: 'Tin tức thị trường vàng - Cập nhật mới nhất',
        nullable: true,
    }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", Object)
], CmsPostPublicDto.prototype, "metaTitle", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Mô tả SEO',
        example: 'Theo dõi tin tức và cập nhật mới nhất về thị trường vàng',
        nullable: true,
    }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", Object)
], CmsPostPublicDto.prototype, "metaDescription", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Từ khóa SEO',
        example: 'thị trường vàng, tin tức, giá vàng',
        nullable: true,
    }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", Object)
], CmsPostPublicDto.prototype, "metaKeywords", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Cho phép bình luận',
        example: true,
    }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", Boolean)
], CmsPostPublicDto.prototype, "allowComments", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Thông tin chuyên mục',
        type: () => cms_category_dto_1.CmsCategoryDto,
        nullable: true,
    }),
    (0, class_transformer_1.Expose)(),
    (0, class_transformer_1.Type)(() => cms_category_dto_1.CmsCategoryDto),
    __metadata("design:type", Object)
], CmsPostPublicDto.prototype, "category", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Thời gian tạo',
        example: '2023-01-01T00:00:00.000Z',
    }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", Date)
], CmsPostPublicDto.prototype, "createdAt", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Thời gian cập nhật',
        example: '2023-01-01T00:00:00.000Z',
    }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", Date)
], CmsPostPublicDto.prototype, "updatedAt", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Bài viết có thể hiển thị công khai không',
        example: true,
    }),
    (0, class_transformer_1.Expose)(),
    (0, class_transformer_1.Transform)(({ obj }) => {
        return obj.status === cms_posts_entity_1.CmsPostStatus.PUBLISHED && !obj.isDeleted;
    }),
    __metadata("design:type", Boolean)
], CmsPostPublicDto.prototype, "canDisplayPublicly", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Bài viết có hình ảnh đại diện không',
        example: true,
    }),
    (0, class_transformer_1.Expose)(),
    (0, class_transformer_1.Transform)(({ obj }) => !!obj.featuredImageUrl),
    __metadata("design:type", Boolean)
], CmsPostPublicDto.prototype, "hasFeaturedImage", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Bài viết có tóm tắt không',
        example: true,
    }),
    (0, class_transformer_1.Expose)(),
    (0, class_transformer_1.Transform)(({ obj }) => !!obj.excerpt),
    __metadata("design:type", Boolean)
], CmsPostPublicDto.prototype, "hasExcerpt", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Bài viết có thuộc chuyên mục không',
        example: true,
    }),
    (0, class_transformer_1.Expose)(),
    (0, class_transformer_1.Transform)(({ obj }) => !!obj.category),
    __metadata("design:type", Boolean)
], CmsPostPublicDto.prototype, "hasCategory", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Tên loại bài viết bằng tiếng Việt',
        example: 'Bài viết',
    }),
    (0, class_transformer_1.Expose)(),
    (0, class_transformer_1.Transform)(({ obj }) => {
        const typeNames = {
            [cms_posts_entity_1.CmsPostType.POST]: 'Bài viết',
            [cms_posts_entity_1.CmsPostType.NEWS]: 'Tin tức',
            [cms_posts_entity_1.CmsPostType.PRESS_RELEASE]: 'Thông cáo báo chí',
            [cms_posts_entity_1.CmsPostType.EVENT]: 'Sự kiện',
            [cms_posts_entity_1.CmsPostType.KNOWLEDGE_BASE]: 'Kiến thức',
        };
        return typeNames[obj.postType] || 'Không xác định';
    }),
    __metadata("design:type", String)
], CmsPostPublicDto.prototype, "postTypeDisplayName", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Tên trạng thái bằng tiếng Việt',
        example: 'Đã xuất bản',
    }),
    (0, class_transformer_1.Expose)(),
    (0, class_transformer_1.Transform)(({ obj }) => {
        const statusNames = {
            [cms_posts_entity_1.CmsPostStatus.DRAFT]: 'Bản nháp',
            [cms_posts_entity_1.CmsPostStatus.PUBLISHED]: 'Đã xuất bản',
        };
        return statusNames[obj.status] || 'Không xác định';
    }),
    __metadata("design:type", String)
], CmsPostPublicDto.prototype, "statusDisplayName", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Key duy nhất cho React component',
        example: 'post-tin-tuc-moi-nhat-ve-thi-truong-vang',
    }),
    (0, class_transformer_1.Expose)(),
    (0, class_transformer_1.Transform)(({ obj }) => {
        const slug = obj.slug || obj.title
            ?.toLowerCase()
            ?.normalize('NFD')
            ?.replace(/[\u0300-\u036f]/g, '')
            ?.replace(/[^a-z0-9\s-]/g, '')
            ?.replace(/\s+/g, '-')
            ?.replace(/-+/g, '-')
            ?.trim() || '';
        return `post-${slug || obj.id}`;
    }),
    __metadata("design:type", String)
], CmsPostPublicDto.prototype, "key", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'URL đầy đủ cho bài viết',
        example: '/bai-viet/tin-tuc-moi-nhat-ve-thi-truong-vang',
    }),
    (0, class_transformer_1.Expose)(),
    (0, class_transformer_1.Transform)(({ obj }) => `/bai-viet/${obj.slug}`),
    __metadata("design:type", String)
], CmsPostPublicDto.prototype, "url", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Bài viết có phải là sự kiện không',
        example: false,
    }),
    (0, class_transformer_1.Expose)(),
    (0, class_transformer_1.Transform)(({ obj }) => obj.postType === cms_posts_entity_1.CmsPostType.EVENT),
    __metadata("design:type", Boolean)
], CmsPostPublicDto.prototype, "isEvent", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Sự kiện có đang diễn ra không (chỉ áp dụng cho event)',
        example: false,
    }),
    (0, class_transformer_1.Expose)(),
    (0, class_transformer_1.Transform)(({ obj }) => {
        if (obj.postType !== cms_posts_entity_1.CmsPostType.EVENT)
            return false;
        if (!obj.eventStartDate || !obj.eventEndDate)
            return false;
        const now = new Date();
        const startDate = new Date(obj.eventStartDate);
        const endDate = new Date(obj.eventEndDate);
        return now >= startDate && now <= endDate;
    }),
    __metadata("design:type", Boolean)
], CmsPostPublicDto.prototype, "isEventOngoing", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Sự kiện sắp diễn ra không (chỉ áp dụng cho event)',
        example: false,
    }),
    (0, class_transformer_1.Expose)(),
    (0, class_transformer_1.Transform)(({ obj }) => {
        if (obj.postType !== cms_posts_entity_1.CmsPostType.EVENT)
            return false;
        if (!obj.eventStartDate)
            return false;
        const now = new Date();
        const startDate = new Date(obj.eventStartDate);
        return now < startDate;
    }),
    __metadata("design:type", Boolean)
], CmsPostPublicDto.prototype, "isEventUpcoming", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Sự kiện đã kết thúc không (chỉ áp dụng cho event)',
        example: false,
    }),
    (0, class_transformer_1.Expose)(),
    (0, class_transformer_1.Transform)(({ obj }) => {
        if (obj.postType !== cms_posts_entity_1.CmsPostType.EVENT)
            return false;
        if (!obj.eventEndDate)
            return false;
        const now = new Date();
        const endDate = new Date(obj.eventEndDate);
        return now > endDate;
    }),
    __metadata("design:type", Boolean)
], CmsPostPublicDto.prototype, "isEventPast", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Thời gian đọc ước tính (phút)',
        example: 5,
    }),
    (0, class_transformer_1.Expose)(),
    (0, class_transformer_1.Transform)(({ obj }) => {
        if (!obj.content)
            return 0;
        const textContent = obj.content.replace(/<[^>]*>/g, '');
        const wordCount = textContent.split(/\s+/).length;
        return Math.max(1, Math.ceil(wordCount / 200));
    }),
    __metadata("design:type", Number)
], CmsPostPublicDto.prototype, "estimatedReadingTime", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Tóm tắt ngắn từ content nếu không có excerpt',
        example: 'Thị trường vàng tuần này có nhiều biến động...',
    }),
    (0, class_transformer_1.Expose)(),
    (0, class_transformer_1.Transform)(({ obj }) => {
        if (obj.excerpt)
            return obj.excerpt;
        if (!obj.content)
            return '';
        const textContent = obj.content.replace(/<[^>]*>/g, '');
        return textContent.length > 200
            ? textContent.substring(0, 200) + '...'
            : textContent;
    }),
    __metadata("design:type", String)
], CmsPostPublicDto.prototype, "summary", void 0);
//# sourceMappingURL=cms-post.public.dto.js.map
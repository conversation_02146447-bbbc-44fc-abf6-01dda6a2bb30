"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.VnpayPortableUsageExample = void 0;
const common_1 = require("@nestjs/common");
const vnpay_payment_service_1 = require("../services/vnpay-payment.service");
let VnpayPortableUsageExample = class VnpayPortableUsageExample {
    vnpayPaymentService;
    constructor(vnpayPaymentService) {
        this.vnpayPaymentService = vnpayPaymentService;
        this.setupEventHandler();
    }
    setupEventHandler() {
        const eventHandler = {
            onPaymentCreated: async (data) => {
                console.log('🎯 Payment Created:', data);
            },
            onPaymentSuccess: async (data) => {
                console.log('✅ Payment Success:', data);
                await this.updateWalletBalance(data.externalRef, data.amount);
            },
            onPaymentFailed: async (data) => {
                console.log('❌ Payment Failed:', data);
            },
            onPaymentCancelled: async (data) => {
                console.log('🚫 Payment Cancelled:', data);
            },
            onPaymentExpired: async (data) => {
                console.log('⏰ Payment Expired:', data);
            },
        };
        this.vnpayPaymentService.setEventHandler(eventHandler);
    }
    async createWalletDepositPayment(userId, amount, clientIp = '127.0.0.1') {
        try {
            const paymentRequest = {
                externalRef: userId,
                amount: amount,
                description: `Nạp ${amount.toLocaleString()} VND vào ví`,
                clientIp: clientIp,
                bankCode: 'VNPAYQR',
                locale: 'vn',
                metadata: {
                    type: 'wallet_deposit',
                    userId: userId,
                    timestamp: new Date().toISOString(),
                },
            };
            const result = await this.vnpayPaymentService.createPayment(paymentRequest);
            console.log('💳 Payment created successfully:');
            console.log('  - Payment URL:', result.paymentUrl);
            console.log('  - Merchant Ref:', result.merchantTxnRef);
            console.log('  - Transaction ID:', result.transactionId);
            console.log('  - Expires At:', result.expiresAt);
            return result.paymentUrl;
        }
        catch (error) {
            console.error('❌ Error creating payment:', error.message);
            throw error;
        }
    }
    async createOrderPayment(userId, orderId, amount, orderDescription, clientIp) {
        try {
            const paymentRequest = {
                externalRef: orderId,
                amount: amount,
                description: orderDescription,
                clientIp: clientIp,
                locale: 'vn',
                metadata: {
                    type: 'order_payment',
                    userId: userId,
                    orderId: orderId,
                    timestamp: new Date().toISOString(),
                },
            };
            const result = await this.vnpayPaymentService.createPayment(paymentRequest);
            console.log(`🛒 Order payment created for order ${orderId}:`, result.paymentUrl);
            return result.paymentUrl;
        }
        catch (error) {
            console.error('❌ Error creating order payment:', error.message);
            throw error;
        }
    }
    async queryTransactionStatus(merchantTxnRef) {
        try {
            const transaction = await this.vnpayPaymentService.getTransaction(merchantTxnRef);
            if (transaction) {
                console.log('🔍 Transaction found:');
                console.log('  - Status:', transaction.status);
                console.log('  - Amount:', transaction.getFormattedAmount());
                console.log('  - External Ref:', transaction.externalRef);
                console.log('  - Created:', transaction.createdAt);
                console.log('  - Processed:', transaction.processedAt);
                if (transaction.isSuccess()) {
                    console.log('  - VNPAY Txn No:', transaction.vnpayTxnNo);
                    console.log('  - Bank Code:', transaction.bankCode);
                    console.log('  - Pay Date:', transaction.vnpayPayDate);
                }
            }
            else {
                console.log('❌ Transaction not found');
            }
        }
        catch (error) {
            console.error('❌ Error querying transaction:', error.message);
            throw error;
        }
    }
    async getPaymentStatistics(userId) {
        try {
            const filter = userId ? { externalRef: userId } : undefined;
            const stats = await this.vnpayPaymentService.getStatistics(filter);
            console.log('📊 Payment Statistics:');
            console.log('  - Total Transactions:', stats.totalTransactions);
            console.log('  - Successful:', stats.successfulTransactions);
            console.log('  - Failed:', stats.failedTransactions);
            console.log('  - Pending:', stats.pendingTransactions);
            console.log('  - Success Rate:', `${stats.successRate}%`);
            console.log('  - Total Amount:', `${stats.totalAmount.toLocaleString()} VND`);
            console.log('  - Successful Amount:', `${stats.successfulAmount.toLocaleString()} VND`);
        }
        catch (error) {
            console.error('❌ Error getting statistics:', error.message);
            throw error;
        }
    }
    async demonstrateCompleteFlow() {
        console.log('\n🚀 === VNPAY Portable Payment Flow Demo ===\n');
        try {
            console.log('1️⃣ Creating wallet deposit payment...');
            const paymentUrl = await this.createWalletDepositPayment('user-123', 100000);
            console.log('✅ Payment URL created\n');
            console.log('2️⃣ User completes payment on VNPAY...');
            console.log('✅ Payment completed (simulated)\n');
            console.log('3️⃣ Getting payment statistics...');
            await this.getPaymentStatistics('user-123');
            console.log('✅ Statistics retrieved\n');
            console.log('🎉 === Demo Complete ===\n');
        }
        catch (error) {
            console.error('❌ Demo failed:', error.message);
        }
    }
    async updateWalletBalance(userId, amount) {
        console.log(`💰 Updating wallet balance for user ${userId}: +${amount.toLocaleString()} VND`);
    }
    async handlePaymentWithRetry(userId, amount, maxRetries = 3) {
        let lastError;
        for (let attempt = 1; attempt <= maxRetries; attempt++) {
            try {
                console.log(`🔄 Payment attempt ${attempt}/${maxRetries}`);
                return await this.createWalletDepositPayment(userId, amount);
            }
            catch (error) {
                lastError = error;
                console.log(`❌ Attempt ${attempt} failed:`, error.message);
                if (attempt < maxRetries) {
                    const delay = Math.pow(2, attempt) * 1000;
                    console.log(`⏳ Waiting ${delay}ms before retry...`);
                    await new Promise(resolve => setTimeout(resolve, delay));
                }
            }
        }
        throw new Error(`Payment failed after ${maxRetries} attempts: ${lastError.message}`);
    }
};
exports.VnpayPortableUsageExample = VnpayPortableUsageExample;
exports.VnpayPortableUsageExample = VnpayPortableUsageExample = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [vnpay_payment_service_1.VnpayPaymentService])
], VnpayPortableUsageExample);
//# sourceMappingURL=vnpay-portable-usage.example.js.map
"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.EcomOrderDto = void 0;
const openapi = require("@nestjs/swagger");
const swagger_1 = require("@nestjs/swagger");
const class_transformer_1 = require("class-transformer");
const class_validator_1 = require("class-validator");
const ecom_orders_enum_1 = require("../enum/ecom-orders-enum");
const user_dto_1 = require("../../users/dto/user.dto");
const ecom_order_detail_dto_1 = require("../../ecom-order-details/dto/ecom-order-detail.dto");
class EcomOrderDto {
    id;
    userId;
    orderNumber;
    totalAmount;
    status;
    paymentStatus;
    notes;
    isDeleted;
    createdAt;
    updatedAt;
    createdBy;
    updatedBy;
    deletedAt;
    deletedBy;
    user;
    creator;
    updater;
    deleter;
    orderDetails;
    static _OPENAPI_METADATA_FACTORY() {
        return { id: { required: true, type: () => String, format: "uuid" }, userId: { required: true, type: () => String, format: "uuid" }, orderNumber: { required: true, type: () => String }, totalAmount: { required: true, type: () => Number }, status: { required: true, enum: require("../enum/ecom-orders-enum").OrderStatus }, paymentStatus: { required: true, enum: require("../enum/ecom-orders-enum").PaymentStatus }, notes: { required: false, type: () => String }, isDeleted: { required: true, type: () => Boolean }, createdAt: { required: true, type: () => Date }, updatedAt: { required: true, type: () => Date }, createdBy: { required: false, type: () => String, nullable: true, format: "uuid" }, updatedBy: { required: false, type: () => String, nullable: true, format: "uuid" }, deletedAt: { required: false, type: () => Date, nullable: true }, deletedBy: { required: false, type: () => String, nullable: true, format: "uuid" }, user: { required: true, type: () => require("../../users/dto/user.dto").UserDto }, creator: { required: false, type: () => require("../../users/dto/user.dto").UserDto, nullable: true }, updater: { required: false, type: () => require("../../users/dto/user.dto").UserDto, nullable: true }, deleter: { required: false, type: () => require("../../users/dto/user.dto").UserDto, nullable: true }, orderDetails: { required: false, type: () => [require("../../ecom-order-details/dto/ecom-order-detail.dto").EcomOrderDetailDto] } };
    }
}
exports.EcomOrderDto = EcomOrderDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'ID của đơn hàng',
        example: '550e8400-e29b-41d4-a716-************',
    }),
    (0, class_validator_1.IsUUID)(),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", String)
], EcomOrderDto.prototype, "id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'ID người dùng',
        example: '550e8400-e29b-41d4-a716-************',
    }),
    (0, class_validator_1.IsUUID)(),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", String)
], EcomOrderDto.prototype, "userId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Số đơn hàng',
        example: 'ORD-2023-001',
    }),
    (0, class_validator_1.IsString)(),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", String)
], EcomOrderDto.prototype, "orderNumber", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Tổng giá trị đơn hàng',
        example: 1500000,
    }),
    (0, class_validator_1.IsDecimal)({ decimal_digits: '2' }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", Number)
], EcomOrderDto.prototype, "totalAmount", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Trạng thái đơn hàng',
        enum: ecom_orders_enum_1.OrderStatus,
        example: ecom_orders_enum_1.OrderStatus.PENDING,
    }),
    (0, class_validator_1.IsEnum)(ecom_orders_enum_1.OrderStatus),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", String)
], EcomOrderDto.prototype, "status", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Trạng thái thanh toán',
        enum: ecom_orders_enum_1.PaymentStatus,
        example: ecom_orders_enum_1.PaymentStatus.UNPAID,
    }),
    (0, class_validator_1.IsEnum)(ecom_orders_enum_1.PaymentStatus),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", String)
], EcomOrderDto.prototype, "paymentStatus", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Ghi chú đơn hàng',
        example: 'Giao hàng trong giờ hành chính',
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", String)
], EcomOrderDto.prototype, "notes", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Trạng thái xóa',
        example: false,
    }),
    (0, class_validator_1.IsBoolean)(),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", Boolean)
], EcomOrderDto.prototype, "isDeleted", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Thời gian tạo',
        example: '2021-01-01T00:00:00.000Z',
    }),
    (0, class_validator_1.IsDate)(),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", Date)
], EcomOrderDto.prototype, "createdAt", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Thời gian cập nhật',
        example: '2021-01-01T00:00:00.000Z',
    }),
    (0, class_validator_1.IsDate)(),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", Date)
], EcomOrderDto.prototype, "updatedAt", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'ID người tạo',
        example: '550e8400-e29b-41d4-a716-************',
    }),
    (0, class_validator_1.IsUUID)(),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", Object)
], EcomOrderDto.prototype, "createdBy", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'ID người cập nhật',
        example: '550e8400-e29b-41d4-a716-************',
    }),
    (0, class_validator_1.IsUUID)(),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", Object)
], EcomOrderDto.prototype, "updatedBy", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Thời gian xóa',
        example: '2021-01-01T00:00:00.000Z',
    }),
    (0, class_validator_1.IsDate)(),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", Object)
], EcomOrderDto.prototype, "deletedAt", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'ID người xóa',
        example: '550e8400-e29b-41d4-a716-************',
    }),
    (0, class_validator_1.IsUUID)(),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", Object)
], EcomOrderDto.prototype, "deletedBy", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Người dùng đặt hàng',
        type: () => user_dto_1.UserDto,
    }),
    (0, class_transformer_1.Expose)(),
    (0, class_transformer_1.Type)(() => user_dto_1.UserDto),
    __metadata("design:type", user_dto_1.UserDto)
], EcomOrderDto.prototype, "user", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Người tạo đơn hàng',
        type: () => user_dto_1.UserDto,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Expose)(),
    (0, class_transformer_1.Type)(() => user_dto_1.UserDto),
    __metadata("design:type", Object)
], EcomOrderDto.prototype, "creator", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Người cập nhật đơn hàng',
        type: () => user_dto_1.UserDto,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Expose)(),
    (0, class_transformer_1.Type)(() => user_dto_1.UserDto),
    __metadata("design:type", Object)
], EcomOrderDto.prototype, "updater", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Người xóa đơn hàng',
        type: () => user_dto_1.UserDto,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Expose)(),
    (0, class_transformer_1.Type)(() => user_dto_1.UserDto),
    __metadata("design:type", Object)
], EcomOrderDto.prototype, "deleter", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Chi tiết đơn hàng',
        type: [ecom_order_detail_dto_1.EcomOrderDetailDto],
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Expose)(),
    (0, class_transformer_1.Type)(() => ecom_order_detail_dto_1.EcomOrderDetailDto),
    __metadata("design:type", Array)
], EcomOrderDto.prototype, "orderDetails", void 0);
//# sourceMappingURL=ecom-order.dto.js.map
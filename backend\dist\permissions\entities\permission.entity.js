"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.Permission = void 0;
const openapi = require("@nestjs/swagger");
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
const typeorm_1 = require("typeorm");
const role_permission_entity_1 = require("../../roles/entities/role-permission.entity");
const base_entity_1 = require("../../common/entities/base.entity");
let Permission = class Permission extends base_entity_1.BaseEntity {
    name;
    description;
    displayName;
    parentId;
    rolePermissions;
    getEntityName() {
        return 'permissions';
    }
    static _OPENAPI_METADATA_FACTORY() {
        return { name: { required: true, type: () => String, maxLength: 100 }, description: { required: true, type: () => String, nullable: true }, displayName: { required: true, type: () => String, nullable: true, maxLength: 200 }, parentId: { required: true, type: () => String, nullable: true }, rolePermissions: { required: true, type: () => [require("../../roles/entities/role-permission.entity").RolePermission] } };
    }
};
exports.Permission = Permission;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Tên định danh quyền (vd: post:create)',
        example: 'post:create',
        maxLength: 100,
    }),
    (0, class_validator_1.IsNotEmpty)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MaxLength)(100),
    (0, typeorm_1.Column)({
        type: 'varchar',
        length: 100,
        unique: true,
        nullable: false,
        name: 'name',
    }),
    __metadata("design:type", String)
], Permission.prototype, "name", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Mô tả quyền',
        example: 'Cho phép tạo bài viết mới',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, typeorm_1.Column)({ type: 'text', nullable: true, name: 'description' }),
    __metadata("design:type", Object)
], Permission.prototype, "description", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Tên hiển thị của quyền (tiếng Việt)',
        example: 'Xem bài viết',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MaxLength)(200),
    (0, typeorm_1.Column)({ type: 'varchar', length: 200, nullable: true, name: 'display_name' }),
    __metadata("design:type", Object)
], Permission.prototype, "displayName", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'ID của quyền cha',
        example: '550e8400-e29b-41d4-a716-************',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, typeorm_1.Column)({ type: 'uuid', nullable: true, name: 'parent_id' }),
    __metadata("design:type", Object)
], Permission.prototype, "parentId", void 0);
__decorate([
    (0, typeorm_1.OneToMany)(() => role_permission_entity_1.RolePermission, (rolePermission) => rolePermission.permission),
    (0, swagger_1.ApiProperty)({
        type: () => [role_permission_entity_1.RolePermission],
        description: 'Các vai trò có quyền này',
    }),
    __metadata("design:type", Array)
], Permission.prototype, "rolePermissions", void 0);
exports.Permission = Permission = __decorate([
    (0, typeorm_1.Entity)('permissions')
], Permission);
//# sourceMappingURL=permission.entity.js.map
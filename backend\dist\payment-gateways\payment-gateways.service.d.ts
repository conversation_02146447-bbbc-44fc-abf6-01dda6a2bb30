import { Repository } from 'typeorm';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { VnpayService } from './services/vnpay.service';
import { MomoService } from './services/momo.service';
import { CreatePaymentDto } from './dto/create-payment.dto';
import { PaymentGatewayType } from './enums/payment-gateway-type.enum';
import { Transaction } from '../transactions/entities/transaction.entity';
import { Wallet } from '../wallets/entities/wallet.entity';
import { UpdateWalletService } from '../wallets/services/update.wallet.service';
import { ReadWalletService } from '../wallets/services/read.wallet.service';
export interface PaymentResult {
    paymentUrl: string;
    transactionId: string;
    gatewayType: PaymentGatewayType;
    expiresAt?: Date;
}
export interface CallbackResult {
    isValid: boolean;
    isSuccess?: boolean;
    transactionId?: string;
    amount?: number;
    responseCode?: string;
    transactionStatus?: string;
    message?: string;
    gatewayTransactionNo?: string;
    bankCode?: string;
    payDate?: string;
    gatewayType: PaymentGatewayType;
    rawData: Record<string, any>;
}
export interface IpnResponse {
    success: boolean;
    code: string;
    message: string;
    data?: any;
}
export interface PaymentCallbacks {
    onPaymentCreated?: (result: PaymentResult) => Promise<void>;
    onPaymentSuccess?: (result: CallbackResult) => Promise<void>;
    onPaymentFailed?: (result: CallbackResult) => Promise<void>;
    onPaymentPending?: (result: CallbackResult) => Promise<void>;
}
export declare class PaymentGatewaysService {
    private readonly transactionRepository;
    private readonly walletRepository;
    private readonly vnpayService;
    private readonly momoService;
    private readonly updateWalletService;
    private readonly readWalletService;
    private readonly eventEmitter;
    private readonly logger;
    constructor(transactionRepository: Repository<Transaction>, walletRepository: Repository<Wallet>, vnpayService: VnpayService, momoService: MomoService, updateWalletService: UpdateWalletService, readWalletService: ReadWalletService, eventEmitter: EventEmitter2);
    createPayment(createPaymentDto: CreatePaymentDto, callbacks?: PaymentCallbacks): Promise<PaymentResult>;
    private validatePaymentRequest;
    handleVnpayCallback(params: Record<string, string>, callbacks?: PaymentCallbacks): Promise<CallbackResult>;
    handleMomoCallback(params: Record<string, string>): Promise<{
        isValid: boolean;
        transactionId?: string;
        amount?: number;
        message?: string;
    }>;
    handleMomoIpn(params: Record<string, string>): Promise<{
        isValid: boolean;
        transactionId?: string;
        amount?: number;
        message?: string;
        status?: number;
    }>;
    private createPendingTransaction;
    private processSuccessfulPayment;
    private processFailedPayment;
    handleVnpayIpn(params: Record<string, string>, callbacks?: PaymentCallbacks): Promise<IpnResponse>;
    queryTransaction(gatewayType: PaymentGatewayType, queryData: {
        txnRef: string;
        transactionDate: string;
        orderInfo: string;
        ipAddr: string;
        transactionNo?: string;
    }): Promise<any>;
    refundTransaction(gatewayType: PaymentGatewayType, refundData: {
        txnRef: string;
        amount: number;
        orderInfo: string;
        transactionDate: string;
        transactionType: '02' | '03';
        createBy: string;
        ipAddr: string;
        transactionNo?: string;
    }): Promise<any>;
}

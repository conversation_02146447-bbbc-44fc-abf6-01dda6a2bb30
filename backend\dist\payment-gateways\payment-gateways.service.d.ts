import { Repository } from 'typeorm';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { VnpayService } from './services/vnpay.service';
import { MomoService } from './services/momo.service';
import { CreatePaymentDto } from './dto/create-payment.dto';
import { Transaction } from '../transactions/entities/transaction.entity';
import { Wallet } from '../wallets/entities/wallet.entity';
import { UpdateWalletService } from '../wallets/services/update.wallet.service';
import { ReadWalletService } from '../wallets/services/read.wallet.service';
export declare class PaymentGatewaysService {
    private readonly transactionRepository;
    private readonly walletRepository;
    private readonly vnpayService;
    private readonly momoService;
    private readonly updateWalletService;
    private readonly readWalletService;
    private readonly eventEmitter;
    private readonly logger;
    constructor(transactionRepository: Repository<Transaction>, walletRepository: Repository<Wallet>, vnpayService: VnpayService, momoService: MomoService, updateWalletService: UpdateWalletService, readWalletService: ReadWalletService, eventEmitter: EventEmitter2);
    createPayment(createPaymentDto: CreatePaymentDto): Promise<{
        paymentUrl: string;
        transactionId: string;
    }>;
    handleVnpayCallback(params: Record<string, string>): Promise<{
        isValid: boolean;
        transactionId?: string;
        amount?: number;
        message?: string;
    }>;
    handleMomoCallback(params: Record<string, string>): Promise<{
        isValid: boolean;
        transactionId?: string;
        amount?: number;
        message?: string;
    }>;
    handleVnpayIpn(params: Record<string, string>): Promise<{
        isValid: boolean;
        transactionId?: string;
        amount?: number;
        message?: string;
        RspCode?: string;
        Message?: string;
    }>;
    handleMomoIpn(params: Record<string, string>): Promise<{
        isValid: boolean;
        transactionId?: string;
        amount?: number;
        message?: string;
        status?: number;
    }>;
    private createPendingTransaction;
    private processSuccessfulPayment;
}

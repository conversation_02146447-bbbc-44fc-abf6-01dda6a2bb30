import { BaseActivityLogService } from './base.activity-log.service';
import { ActivityLogDto } from '../dto/activity-log.dto';
export declare class DeleteActivityLogService extends BaseActivityLogService {
    softDelete(id: string, userId?: string): Promise<ActivityLogDto>;
    bulkSoftDelete(ids: string[], userId?: string): Promise<ActivityLogDto[]>;
    remove(id: string): Promise<void>;
    bulkRemove(ids: string[]): Promise<void>;
    restore(id: string): Promise<ActivityLogDto>;
    clearOldLogs(days: number): Promise<number>;
}

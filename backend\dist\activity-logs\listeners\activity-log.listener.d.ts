import { CreateActivityLogService } from '../services/create.activity-log.service';
import { ActivityLogEvent } from '../events/activity-log.event';
export declare class ActivityLogListener {
    private readonly createActivityLogService;
    private readonly logger;
    constructor(createActivityLogService: CreateActivityLogService);
    handleActivityEvent(payload: ActivityLogEvent): Promise<void>;
    handleAllActivityEvents(payload: ActivityLogEvent): Promise<void>;
    handleLoginEvent(payload: ActivityLogEvent): Promise<void>;
    handleLogoutEvent(payload: ActivityLogEvent): Promise<void>;
    handleFailedLoginEvent(payload: ActivityLogEvent): Promise<void>;
    private createActivityLog;
}

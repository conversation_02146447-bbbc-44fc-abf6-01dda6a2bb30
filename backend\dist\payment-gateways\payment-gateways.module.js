"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PaymentGatewaysModule = void 0;
const common_1 = require("@nestjs/common");
const config_1 = require("@nestjs/config");
const typeorm_1 = require("@nestjs/typeorm");
const event_emitter_1 = require("@nestjs/event-emitter");
const axios_1 = require("@nestjs/axios");
const payment_gateways_controller_1 = require("./controllers/payment-gateways.controller");
const vnpay_service_1 = require("./services/vnpay.service");
const momo_service_1 = require("./services/momo.service");
const payment_gateways_service_1 = require("./payment-gateways.service");
const transaction_entity_1 = require("../transactions/entities/transaction.entity");
const wallet_entity_1 = require("../wallets/entities/wallet.entity");
const wallets_module_1 = require("../wallets/wallets.module");
const transactions_module_1 = require("../transactions/transactions.module");
let PaymentGatewaysModule = class PaymentGatewaysModule {
};
exports.PaymentGatewaysModule = PaymentGatewaysModule;
exports.PaymentGatewaysModule = PaymentGatewaysModule = __decorate([
    (0, common_1.Module)({
        imports: [
            config_1.ConfigModule,
            typeorm_1.TypeOrmModule.forFeature([transaction_entity_1.Transaction, wallet_entity_1.Wallet]),
            event_emitter_1.EventEmitterModule.forRoot(),
            axios_1.HttpModule,
            wallets_module_1.WalletsModule,
            transactions_module_1.TransactionsModule,
        ],
        controllers: [payment_gateways_controller_1.PaymentGatewaysController],
        providers: [payment_gateways_service_1.PaymentGatewaysService, vnpay_service_1.VnpayService, momo_service_1.MomoService],
        exports: [payment_gateways_service_1.PaymentGatewaysService, vnpay_service_1.VnpayService, momo_service_1.MomoService],
    })
], PaymentGatewaysModule);
//# sourceMappingURL=payment-gateways.module.js.map
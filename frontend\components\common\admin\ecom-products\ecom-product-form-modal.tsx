'use client';

import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import { But<PERSON> } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle
} from '@/components/ui/dialog';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage
} from '@/components/ui/form';
import { ImageUpload } from '@/components/ui/image-upload';
import { Input } from '@/components/ui/input';
import { SearchableSelect } from '@/components/ui/searchable-select';
import { Switch } from '@/components/ui/switch';
import { TiptapEditor } from '@/components/ui/tiptap-editor';
import { api } from '@/lib/api';
import { zodResolver } from '@hookform/resolvers/zod';
import { Loader2 } from 'lucide-react';
import { useEffect, useRef, useState } from 'react';
import { useForm } from 'react-hook-form';
import { toast } from 'sonner';
import { EcomProductCategory } from '../ecom-product-categories/type/ecom-product-category';
import {
  ecomProductFormSchema,
  EcomProductFormValues,
  ecomProductUpdateFormSchema,
  EcomProductUpdateFormValues,
  validateProductPrice
} from './schemas/ecom-product-form-schema';
import { CreateEcomProductDto, EcomProduct, UpdateEcomProductDto } from './type/ecom-product';

interface EcomProductFormModalProps {
  isOpen: boolean;
  onClose: () => void;
  product: EcomProduct | null;
  mode: 'create' | 'update' | 'view';
  onSuccess: () => void;
}

export function EcomProductFormModal({ isOpen, onClose, product, mode, onSuccess }: EcomProductFormModalProps) {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isFormReady, setIsFormReady] = useState(false);
  const [showConfirmDialog, setShowConfirmDialog] = useState(false);
  const formIsDirty = useRef(false);

  const isEditMode = mode === 'update';
  const isViewMode = mode === 'view';
  const isCreateMode = mode === 'create';

  // Chọn schema phù hợp dựa vào mode
  const formSchema = isEditMode ? ecomProductUpdateFormSchema : ecomProductFormSchema;

  // Setup form với react-hook-form và zod validator
  const form = useForm<EcomProductFormValues | EcomProductUpdateFormValues>({
    resolver: zodResolver(formSchema) as any,
    defaultValues: isCreateMode ? {
      productCode: '',
      productName: '',
      categoryId: '',
      description: '',
      regularPrice: '',
      salePrice: '',
      stockQuantity: '',
      imageUrl: '',
      isActive: true
    } : {
      id: product?.id || '',
      productCode: product?.productCode || '',
      productName: product?.productName || '',
      categoryId: product?.categoryId || '',
      description: product?.description || '',
      regularPrice: product?.regularPrice ? String(product.regularPrice) : '',
      salePrice: product?.salePrice ? String(product.salePrice) : '',
      stockQuantity: product?.stockQuantity ? String(product.stockQuantity) : '',
      imageUrl: product?.imageUrl || '',
      isActive: product?.isActive !== undefined ? product.isActive : true
    },
  });

  // Theo dõi thay đổi của form
  useEffect(() => {
    const subscription = form.watch(() => {
      formIsDirty.current = form.formState.isDirty;
    });
    return () => subscription.unsubscribe();
  }, [form, form.watch]);

  // Xử lý đóng form
  const handleClose = () => {
    if (mode !== 'view' && formIsDirty.current) {
      setShowConfirmDialog(true);
    } else {
      onClose();
    }
  };

  // Fetch danh mục sản phẩm theo từ khóa
  const fetchCategories = async (search: string) => {
    try {
      const response = await api.get<{ data: EcomProductCategory[] }>(`ecom-product-categories?page=1&limit=10&search=${encodeURIComponent(search)}`);
      // Nếu đang edit và có category, thêm vào đầu danh sách để đảm bảo hiển thị
      if (isEditMode && product?.category) {
        const existingCategory = response.data.find(cat => cat.id === product.category?.id);
        if (!existingCategory) {
          response.data.unshift(product.category);
        }
      }
      return response?.data || [];
    } catch (error) {
      console.error('Error fetching product categories:', error);
      return [];
    }
  };

  // Reset form khi product thay đổi
  useEffect(() => {
    if (!isOpen) {
      // Reset form khi modal đóng
      setTimeout(() => {
        form.reset();
        formIsDirty.current = false;
        setIsFormReady(false);
      }, 300);
    } else {
      // Đặt trạng thái đang tải khi mở modal
      setIsFormReady(false);

      setTimeout(() => {
        if (isCreateMode) {
          form.reset({
            productCode: '',
            productName: '',
            categoryId: '',
            description: '',
            regularPrice: '',
            salePrice: '',
            stockQuantity: '',
            imageUrl: '',
            isActive: true
          });
        } else {
          form.reset({
            id: product?.id || '',
            productCode: product?.productCode || '',
            productName: product?.productName || '',
            categoryId: product?.categoryId || '',
            description: product?.description || '',
            regularPrice: product?.regularPrice ? String(product.regularPrice) : '',
            salePrice: product?.salePrice ? String(product.salePrice) : '',
            stockQuantity: product?.stockQuantity ? String(product.stockQuantity) : '',
            weight: product?.weight ? String(product.weight) : '',
            imageUrl: product?.imageUrl || '',
            isActive: product?.isActive !== undefined ? product.isActive : true
          });
        }
        formIsDirty.current = false;
        setIsFormReady(true);
      }, 300);
    }
  }, [form, product, isCreateMode, isOpen]);

  // Xử lý khi submit form
  const onSubmit = async (data: any) => {
    // Kiểm tra logic between fields
    const customErrors = validateProductPrice(data);
    if (Object.keys(customErrors).length > 0) {
      Object.entries(customErrors).forEach(([field, message]) => {
        form.setError(field as any, { message });
      });
      return;
    }

    setIsSubmitting(true);
    try {
      // Convert weight to a valid decimal number
      const weightValue = data.weight ? Number(parseFloat(data.weight).toFixed(3)) : null;

      if (isCreateMode) {
        const createData: CreateEcomProductDto = {
          productCode: data.productCode,
          productName: data.productName,
          categoryId: data.categoryId,
          description: data.description,
          weight: weightValue || undefined,
          imageUrl: data.imageUrl,
          regularPrice: Number(data.regularPrice),
          salePrice: data.salePrice ? Number(data.salePrice) : undefined,
          stockQuantity: data.stockQuantity ? Number(data.stockQuantity) : 0,
          isActive: data.isActive
        };
        await api.post('/ecom-products', createData);
        toast.success('Đã tạo sản phẩm thành công');
      } else if (isEditMode) {
        const updateData: UpdateEcomProductDto = {
          id: product?.id || '',
          productCode: data.productCode,
          productName: data.productName,
          categoryId: data.categoryId,
          description: data.description,
          weight: weightValue,
          imageUrl: data.imageUrl,
          regularPrice: Number(data.regularPrice),
          salePrice: data.salePrice ? Number(data.salePrice) : null,
          stockQuantity: data.stockQuantity ? Number(data.stockQuantity) : 0,
          isActive: data.isActive
        };
        await api.put(`/ecom-products/${product?.id}`, updateData);
        toast.success('Đã cập nhật sản phẩm thành công');
      }
      formIsDirty.current = false;
      onSuccess();
      onClose();
    } catch (error: any) {
      const errorMessage = error.response?.data?.message || 'Đã xảy ra lỗi';
      toast.error(errorMessage);
      console.error('Error submitting product form:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <>
      <Dialog open={isOpen} onOpenChange={(open) => !open && handleClose()}>
        <DialogContent className="sm:max-w-[600px] max-h-[90vh] overflow-hidden flex flex-col">
          <DialogHeader>
            <DialogTitle>
              {isCreateMode && 'Tạo sản phẩm mới'}
              {isEditMode && 'Cập nhật sản phẩm'}
              {isViewMode && 'Thông tin sản phẩm'}
            </DialogTitle>
            <DialogDescription>
              {isCreateMode && 'Nhập thông tin để tạo sản phẩm mới'}
              {isEditMode && 'Chỉnh sửa thông tin của sản phẩm'}
              {isViewMode && 'Xem chi tiết thông tin sản phẩm'}
            </DialogDescription>
          </DialogHeader>

          {!isFormReady ? (
            <div className="flex flex-col items-center justify-center py-8">
              <Loader2 className="h-8 w-8 animate-spin text-primary mb-4" />
              <p className="text-sm text-muted-foreground">Đang tải dữ liệu...</p>
            </div>
          ) : (
            <Form {...form}>
              <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6 overflow-y-auto pr-2">
                <div className="grid grid-cols-2 gap-4">
                  {/* Product Code */}
                  <FormField
                    control={form.control}
                    name="productCode"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Mã sản phẩm</FormLabel>
                        <FormControl>
                          <Input
                            {...field}
                            disabled={isViewMode || isSubmitting}
                            placeholder="Nhập mã sản phẩm"
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  {/* Product Name */}
                  <FormField
                    control={form.control}
                    name="productName"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Tên sản phẩm</FormLabel>
                        <FormControl>
                          <Input
                            {...field}
                            disabled={isViewMode || isSubmitting}
                            placeholder="Nhập tên sản phẩm"
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                <div className="grid grid-cols-2 gap-4">
                  {/* Category */}
                  <FormField
                    control={form.control}
                    name="categoryId"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Danh mục</FormLabel>
                        <FormControl>
                          <SearchableSelect
                            value={field.value}
                            onChange={field.onChange}
                            placeholder="Chọn danh mục"
                            searchPlaceholder="Tìm kiếm danh mục..."
                            fetchOptions={fetchCategories}
                            renderOption={(category) => category ? category.name : ''}
                            disabled={isViewMode || isSubmitting}
                            loadOnMount={isEditMode && !!product?.category}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  {/* Weight */}
                  <FormField
                    control={form.control}
                    name="weight"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Khối lượng (oz)</FormLabel>
                        <FormControl>
                          <Input
                            type="number"
                            min={0}
                            step="0.001"
                            disabled={isViewMode || isSubmitting}
                            value={field.value || ''}
                            onChange={(e) => field.onChange(e.target.value)}
                            placeholder="Nhập khối lượng (oz)"
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
                {/* Description */}
                <FormField
                  control={form.control}
                  name="description"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Mô tả</FormLabel>
                      <FormControl>
                        <TiptapEditor
                          content={field.value || ''}
                          onChange={field.onChange}
                          placeholder="Nhập mô tả về sản phẩm"
                          editable={!isViewMode && !isSubmitting}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Image Upload */}
                <FormField
                  control={form.control}
                  name="imageUrl"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Hình ảnh sản phẩm</FormLabel>
                      <FormControl>
                        <ImageUpload
                          value={field.value || ''}
                          onChange={(url) => {
                            field.onChange(url);
                            formIsDirty.current = true;
                          }}
                          endpoint="/attachments/upload/ecom/product"
                          disabled={isViewMode || isSubmitting}
                          placeholder="Tải lên hình ảnh sản phẩm"
                          maxSize={5 * 1024 * 1024} // 5MB
                          allowedTypes={['image/jpeg', 'image/png', 'image/jpg', 'image/webp']}
                          onUploadSuccess={(result) => {
                            toast.success('Tải lên hình ảnh thành công');
                          }}
                          onUploadError={(error) => {
                            toast.error(error.message || 'Có lỗi xảy ra khi tải lên hình ảnh');
                          }}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <div className="grid grid-cols-2 gap-4">
                  {/* Regular Price Field */}
                  <FormField
                    control={form.control}
                    name="regularPrice"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Giá bán (VND)</FormLabel>
                        <FormControl>
                          <Input
                            type="number"
                            min={0}
                            step="1000"
                            disabled={isViewMode || isSubmitting}
                            value={field.value}
                            onChange={(e) => field.onChange(e.target.value)}
                            placeholder="Nhập giá bán"
                          />
                        </FormControl>
                        <FormDescription>
                          Giá bán chính thức của sản phẩm
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  {/* Sale Price Field */}
                  <FormField
                    control={form.control}
                    name="salePrice"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Giá khuyến mãi (VND)</FormLabel>
                        <FormControl>
                          <Input
                            type="number"
                            min={0}
                            step="1000"
                            disabled={isViewMode || isSubmitting}
                            value={field.value || ''}
                            onChange={(e) => field.onChange(e.target.value)}
                            placeholder="Nhập giá khuyến mãi (nếu có)"
                          />
                        </FormControl>
                        <FormDescription>
                          Giá khuyến mãi của sản phẩm (nếu có)
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                {/* Stock Quantity Field */}
                <FormField
                  control={form.control}
                  name="stockQuantity"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Số lượng tồn kho</FormLabel>
                      <FormControl>
                        <Input
                          type="number"
                          min={0}
                          step="1"
                          disabled={isViewMode || isSubmitting}
                          value={field.value}
                          onChange={(e) => field.onChange(e.target.value)}
                          placeholder="Nhập số lượng tồn kho"
                        />
                      </FormControl>
                      <FormDescription>
                        Số lượng sản phẩm hiện có trong kho
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Is Active */}
                <FormField
                  control={form.control}
                  name="isActive"
                  render={({ field }) => (
                    <FormItem className="flex flex-row items-center justify-between rounded-lg border p-3">
                      <div className="space-y-0.5">
                        <FormLabel>Trạng thái</FormLabel>
                        <FormDescription>
                          Sản phẩm {field.value ? 'đang hoạt động' : 'không hoạt động'}
                        </FormDescription>
                      </div>
                      <FormControl>
                        <Switch
                          checked={field.value}
                          onCheckedChange={field.onChange}
                          disabled={isViewMode || isSubmitting}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <DialogFooter>
                  <Button
                    type="button"
                    variant="outline"
                    onClick={handleClose}
                    disabled={isSubmitting}
                  >
                    {isViewMode ? 'Đóng' : 'Hủy'}
                  </Button>
                  {!isViewMode && (
                    <Button type="submit" disabled={isSubmitting}>
                      {isSubmitting ? (
                        <>
                          <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                          Đang xử lý...
                        </>
                      ) : isCreateMode ? 'Tạo mới' : 'Cập nhật'}
                    </Button>
                  )}
                </DialogFooter>
              </form>
            </Form>
          )}
        </DialogContent>
      </Dialog>

      <AlertDialog open={showConfirmDialog} onOpenChange={setShowConfirmDialog}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Bạn có chắc muốn hủy?</AlertDialogTitle>
            <AlertDialogDescription>
              Các thay đổi của bạn sẽ không được lưu. Bạn có chắc muốn hủy bỏ không?
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Tiếp tục chỉnh sửa</AlertDialogCancel>
            <AlertDialogAction
              onClick={() => {
                formIsDirty.current = false;
                onClose();
              }}
              className="bg-destructive hover:bg-destructive/90"
            >
              Hủy thay đổi
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
}

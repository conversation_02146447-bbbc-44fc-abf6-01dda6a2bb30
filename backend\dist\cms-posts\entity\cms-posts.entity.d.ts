import { BaseEntity } from '../../common/entities/base.entity';
import { CmsCategories } from '../../cms-categories/entity/cms-categories.entity';
import { User } from '../../users/entities/user.entity';
export declare enum CmsPostType {
    POST = "post",
    NEWS = "news",
    PRESS_RELEASE = "press_release",
    EVENT = "event",
    KNOWLEDGE_BASE = "knowledge_base"
}
export declare enum CmsPostStatus {
    DRAFT = "draft",
    PUBLISHED = "published"
}
export declare class CmsPosts extends BaseEntity {
    postType: CmsPostType;
    title: string;
    slug: string;
    excerpt?: string;
    content: string;
    featuredImageUrl?: string;
    status: CmsPostStatus;
    publishedAt?: Date | null;
    eventStartDate?: Date | null;
    eventEndDate?: Date | null;
    eventLocation?: string;
    viewCount: number;
    metaTitle?: string;
    metaDescription?: string;
    metaKeywords?: string;
    allowComments: boolean;
    isFeatured: boolean;
    category?: CmsCategories | null;
    author: User;
    tags?: any[];
    getEntityName(): string;
}

{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 18, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/bloomgoo/gold-exchange-platform/frontend/components/layout-provider.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const LayoutProvider = registerClientReference(\n    function() { throw new Error(\"Attempted to call LayoutProvider() from the server but LayoutProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/layout-provider.tsx <module evaluation>\",\n    \"LayoutProvider\",\n);\nexport const useLayout = registerClientReference(\n    function() { throw new Error(\"Attempted to call useLayout() from the server but useLayout is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/layout-provider.tsx <module evaluation>\",\n    \"useLayout\",\n);\n"], "names": [], "mappings": ";;;;AAAA;;AACO,MAAM,iBAAiB,CAAA,GAAA,8VAAA,CAAA,0BAAuB,AAAD,EAChD;IAAa,MAAM,IAAI,MAAM;AAA4O,GACzQ,gEACA;AAEG,MAAM,YAAY,CAAA,GAAA,8VAAA,CAAA,0BAAuB,AAAD,EAC3C;IAAa,MAAM,IAAI,MAAM;AAAkO,GAC/P,gEACA", "debugId": null}}, {"offset": {"line": 36, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/bloomgoo/gold-exchange-platform/frontend/components/layout-provider.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const LayoutProvider = registerClientReference(\n    function() { throw new Error(\"Attempted to call LayoutProvider() from the server but LayoutProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/layout-provider.tsx\",\n    \"LayoutProvider\",\n);\nexport const useLayout = registerClientReference(\n    function() { throw new Error(\"Attempted to call useLayout() from the server but useLayout is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/layout-provider.tsx\",\n    \"useLayout\",\n);\n"], "names": [], "mappings": ";;;;AAAA;;AACO,MAAM,iBAAiB,CAAA,GAAA,8VAAA,CAAA,0BAAuB,AAAD,EAChD;IAAa,MAAM,IAAI,MAAM;AAA4O,GACzQ,4CACA;AAEG,MAAM,YAAY,CAAA,GAAA,8VAAA,CAAA,0BAAuB,AAAD,EAC3C;IAAa,MAAM,IAAI,MAAM;AAAkO,GAC/P,4CACA", "debugId": null}}, {"offset": {"line": 54, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 64, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/bloomgoo/gold-exchange-platform/frontend/components/common/admin/ebanks/ebanks.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/components/common/admin/ebanks/ebanks.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/common/admin/ebanks/ebanks.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,8VAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA6S,GAC1U,2EACA", "debugId": null}}, {"offset": {"line": 78, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/bloomgoo/gold-exchange-platform/frontend/components/common/admin/ebanks/ebanks.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/components/common/admin/ebanks/ebanks.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/common/admin/ebanks/ebanks.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,8VAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAyR,GACtT,uDACA", "debugId": null}}, {"offset": {"line": 92, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 102, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/bloomgoo/gold-exchange-platform/frontend/components/common/admin/ebanks/components/bank-header.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const BankHeader = registerClientReference(\n    function() { throw new Error(\"Attempted to call BankHeader() from the server but BankHeader is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/common/admin/ebanks/components/bank-header.tsx <module evaluation>\",\n    \"BankHeader\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,aAAa,CAAA,GAAA,8VAAA,CAAA,0BAAuB,AAAD,EAC5C;IAAa,MAAM,IAAI,MAAM;AAAoO,GACjQ,2FACA", "debugId": null}}, {"offset": {"line": 116, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/bloomgoo/gold-exchange-platform/frontend/components/common/admin/ebanks/components/bank-header.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const BankHeader = registerClientReference(\n    function() { throw new Error(\"Attempted to call BankHeader() from the server but BankHeader is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/common/admin/ebanks/components/bank-header.tsx\",\n    \"BankHeader\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,aAAa,CAAA,GAAA,8VAAA,CAAA,0BAAuB,AAAD,EAC5C;IAAa,MAAM,IAAI,MAAM;AAAoO,GACjQ,uEACA", "debugId": null}}, {"offset": {"line": 130, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 140, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/bloomgoo/gold-exchange-platform/frontend/components/common/admin/ebanks/components/bank-toolbar.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const BankToolbar = registerClientReference(\n    function() { throw new Error(\"Attempted to call BankToolbar() from the server but BankToolbar is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/common/admin/ebanks/components/bank-toolbar.tsx <module evaluation>\",\n    \"BankToolbar\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,cAAc,CAAA,GAAA,8VAAA,CAAA,0BAAuB,AAAD,EAC7C;IAAa,MAAM,IAAI,MAAM;AAAsO,GACnQ,4FACA", "debugId": null}}, {"offset": {"line": 154, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/bloomgoo/gold-exchange-platform/frontend/components/common/admin/ebanks/components/bank-toolbar.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const BankToolbar = registerClientReference(\n    function() { throw new Error(\"Attempted to call BankToolbar() from the server but BankToolbar is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/common/admin/ebanks/components/bank-toolbar.tsx\",\n    \"BankToolbar\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,cAAc,CAAA,GAAA,8VAAA,CAAA,0BAAuB,AAAD,EAC7C;IAAa,MAAM,IAAI,MAAM;AAAsO,GACnQ,wEACA", "debugId": null}}, {"offset": {"line": 168, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 178, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/bloomgoo/gold-exchange-platform/frontend/components/common/admin/ebanks/components/bank-table.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const BankTable = registerClientReference(\n    function() { throw new Error(\"Attempted to call BankTable() from the server but BankTable is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/common/admin/ebanks/components/bank-table.tsx <module evaluation>\",\n    \"BankTable\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,YAAY,CAAA,GAAA,8VAAA,CAAA,0BAAuB,AAAD,EAC3C;IAAa,MAAM,IAAI,MAAM;AAAkO,GAC/P,0FACA", "debugId": null}}, {"offset": {"line": 192, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/bloomgoo/gold-exchange-platform/frontend/components/common/admin/ebanks/components/bank-table.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const BankTable = registerClientReference(\n    function() { throw new Error(\"Attempted to call BankTable() from the server but BankTable is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/common/admin/ebanks/components/bank-table.tsx\",\n    \"BankTable\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,YAAY,CAAA,GAAA,8VAAA,CAAA,0BAAuB,AAAD,EAC3C;IAAa,MAAM,IAAI,MAAM;AAAkO,GAC/P,sEACA", "debugId": null}}, {"offset": {"line": 206, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 216, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/bloomgoo/gold-exchange-platform/frontend/components/common/admin/ebanks/components/status-tabs.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const StatusTabs = registerClientReference(\n    function() { throw new Error(\"Attempted to call StatusTabs() from the server but StatusTabs is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/common/admin/ebanks/components/status-tabs.tsx <module evaluation>\",\n    \"StatusTabs\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,aAAa,CAAA,GAAA,8VAAA,CAAA,0BAAuB,AAAD,EAC5C;IAAa,MAAM,IAAI,MAAM;AAAoO,GACjQ,2FACA", "debugId": null}}, {"offset": {"line": 230, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/bloomgoo/gold-exchange-platform/frontend/components/common/admin/ebanks/components/status-tabs.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const StatusTabs = registerClientReference(\n    function() { throw new Error(\"Attempted to call StatusTabs() from the server but StatusTabs is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/common/admin/ebanks/components/status-tabs.tsx\",\n    \"StatusTabs\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,aAAa,CAAA,GAAA,8VAAA,CAAA,0BAAuB,AAAD,EAC5C;IAAa,MAAM,IAAI,MAAM;AAAoO,GACjQ,uEACA", "debugId": null}}, {"offset": {"line": 244, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 254, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/bloomgoo/gold-exchange-platform/frontend/components/common/admin/ebanks/components/float-delete-button.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const FloatDeleteButton = registerClientReference(\n    function() { throw new Error(\"Attempted to call FloatDeleteButton() from the server but FloatDeleteButton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/common/admin/ebanks/components/float-delete-button.tsx <module evaluation>\",\n    \"FloatDeleteButton\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,oBAAoB,CAAA,GAAA,8VAAA,CAAA,0BAAuB,AAAD,EACnD;IAAa,MAAM,IAAI,MAAM;AAAkP,GAC/Q,mGACA", "debugId": null}}, {"offset": {"line": 268, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/bloomgoo/gold-exchange-platform/frontend/components/common/admin/ebanks/components/float-delete-button.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const FloatDeleteButton = registerClientReference(\n    function() { throw new Error(\"Attempted to call FloatDeleteButton() from the server but FloatDeleteButton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/common/admin/ebanks/components/float-delete-button.tsx\",\n    \"FloatDeleteButton\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,oBAAoB,CAAA,GAAA,8VAAA,CAAA,0BAAuB,AAAD,EACnD;IAAa,MAAM,IAAI,MAAM;AAAkP,GAC/Q,+EACA", "debugId": null}}, {"offset": {"line": 282, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 292, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/bloomgoo/gold-exchange-platform/frontend/components/common/admin/ebanks/form/bank-form-modal.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const BankFormModal = registerClientReference(\n    function() { throw new Error(\"Attempted to call BankFormModal() from the server but BankFormModal is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/common/admin/ebanks/form/bank-form-modal.tsx <module evaluation>\",\n    \"BankFormModal\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,gBAAgB,CAAA,GAAA,8VAAA,CAAA,0BAAuB,AAAD,EAC/C;IAAa,MAAM,IAAI,MAAM;AAA0O,GACvQ,yFACA", "debugId": null}}, {"offset": {"line": 306, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/bloomgoo/gold-exchange-platform/frontend/components/common/admin/ebanks/form/bank-form-modal.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const BankFormModal = registerClientReference(\n    function() { throw new Error(\"Attempted to call BankFormModal() from the server but BankFormModal is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/common/admin/ebanks/form/bank-form-modal.tsx\",\n    \"BankFormModal\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,gBAAgB,CAAA,GAAA,8VAAA,CAAA,0BAAuB,AAAD,EAC/C;IAAa,MAAM,IAAI,MAAM;AAA0O,GACvQ,qEACA", "debugId": null}}, {"offset": {"line": 320, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 330, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/bloomgoo/gold-exchange-platform/frontend/components/common/admin/ebanks/form/bank-form-fields.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const BankFormFields = registerClientReference(\n    function() { throw new Error(\"Attempted to call BankFormFields() from the server but BankFormFields is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/common/admin/ebanks/form/bank-form-fields.tsx <module evaluation>\",\n    \"BankFormFields\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,iBAAiB,CAAA,GAAA,8VAAA,CAAA,0BAAuB,AAAD,EAChD;IAAa,MAAM,IAAI,MAAM;AAA4O,GACzQ,0FACA", "debugId": null}}, {"offset": {"line": 344, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/bloomgoo/gold-exchange-platform/frontend/components/common/admin/ebanks/form/bank-form-fields.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const BankFormFields = registerClientReference(\n    function() { throw new Error(\"Attempted to call BankFormFields() from the server but BankFormFields is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/common/admin/ebanks/form/bank-form-fields.tsx\",\n    \"BankFormFields\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,iBAAiB,CAAA,GAAA,8VAAA,CAAA,0BAAuB,AAAD,EAChD;IAAa,MAAM,IAAI,MAAM;AAA4O,GACzQ,sEACA", "debugId": null}}, {"offset": {"line": 358, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 368, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/bloomgoo/gold-exchange-platform/frontend/components/common/admin/ebanks/detail/bank-detail-sheet.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const BankDetailSheet = registerClientReference(\n    function() { throw new Error(\"Attempted to call BankDetailSheet() from the server but BankDetailSheet is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/common/admin/ebanks/detail/bank-detail-sheet.tsx <module evaluation>\",\n    \"BankDetailSheet\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,kBAAkB,CAAA,GAAA,8VAAA,CAAA,0BAAuB,AAAD,EACjD;IAAa,MAAM,IAAI,MAAM;AAA8O,GAC3Q,6FACA", "debugId": null}}, {"offset": {"line": 382, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/bloomgoo/gold-exchange-platform/frontend/components/common/admin/ebanks/detail/bank-detail-sheet.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const BankDetailSheet = registerClientReference(\n    function() { throw new Error(\"Attempted to call BankDetailSheet() from the server but BankDetailSheet is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/common/admin/ebanks/detail/bank-detail-sheet.tsx\",\n    \"BankDetailSheet\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,kBAAkB,CAAA,GAAA,8VAAA,CAAA,0BAAuB,AAAD,EACjD;IAAa,MAAM,IAAI,MAAM;AAA8O,GAC3Q,yEACA", "debugId": null}}, {"offset": {"line": 396, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 406, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/bloomgoo/gold-exchange-platform/frontend/components/common/admin/ebanks/detail/bank-detail-header.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const BankDetailHeader = registerClientReference(\n    function() { throw new Error(\"Attempted to call BankDetailHeader() from the server but BankDetailHeader is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/common/admin/ebanks/detail/bank-detail-header.tsx <module evaluation>\",\n    \"BankDetailHeader\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,mBAAmB,CAAA,GAAA,8VAAA,CAAA,0BAAuB,AAAD,EAClD;IAAa,MAAM,IAAI,MAAM;AAAgP,GAC7Q,8FACA", "debugId": null}}, {"offset": {"line": 420, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/bloomgoo/gold-exchange-platform/frontend/components/common/admin/ebanks/detail/bank-detail-header.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const BankDetailHeader = registerClientReference(\n    function() { throw new Error(\"Attempted to call BankDetailHeader() from the server but BankDetailHeader is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/common/admin/ebanks/detail/bank-detail-header.tsx\",\n    \"BankDetailHeader\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,mBAAmB,CAAA,GAAA,8VAAA,CAAA,0BAAuB,AAAD,EAClD;IAAa,MAAM,IAAI,MAAM;AAAgP,GAC7Q,0EACA", "debugId": null}}, {"offset": {"line": 434, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 444, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/bloomgoo/gold-exchange-platform/frontend/components/common/admin/ebanks/detail/bank-detail-info.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const BankDetailInfo = registerClientReference(\n    function() { throw new Error(\"Attempted to call BankDetailInfo() from the server but BankDetailInfo is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/common/admin/ebanks/detail/bank-detail-info.tsx <module evaluation>\",\n    \"BankDetailInfo\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,iBAAiB,CAAA,GAAA,8VAAA,CAAA,0BAAuB,AAAD,EAChD;IAAa,MAAM,IAAI,MAAM;AAA4O,GACzQ,4FACA", "debugId": null}}, {"offset": {"line": 458, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/bloomgoo/gold-exchange-platform/frontend/components/common/admin/ebanks/detail/bank-detail-info.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const BankDetailInfo = registerClientReference(\n    function() { throw new Error(\"Attempted to call BankDetailInfo() from the server but BankDetailInfo is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/common/admin/ebanks/detail/bank-detail-info.tsx\",\n    \"BankDetailInfo\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,iBAAiB,CAAA,GAAA,8VAAA,CAAA,0BAAuB,AAAD,EAChD;IAAa,MAAM,IAAI,MAAM;AAA4O,GACzQ,wEACA", "debugId": null}}, {"offset": {"line": 472, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 482, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/bloomgoo/gold-exchange-platform/frontend/components/common/admin/ebanks/detail/bank-detail-images.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const BankDetailImages = registerClientReference(\n    function() { throw new Error(\"Attempted to call BankDetailImages() from the server but BankDetailImages is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/common/admin/ebanks/detail/bank-detail-images.tsx <module evaluation>\",\n    \"BankDetailImages\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,mBAAmB,CAAA,GAAA,8VAAA,CAAA,0BAAuB,AAAD,EAClD;IAAa,MAAM,IAAI,MAAM;AAAgP,GAC7Q,8FACA", "debugId": null}}, {"offset": {"line": 496, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/bloomgoo/gold-exchange-platform/frontend/components/common/admin/ebanks/detail/bank-detail-images.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const BankDetailImages = registerClientReference(\n    function() { throw new Error(\"Attempted to call BankDetailImages() from the server but BankDetailImages is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/common/admin/ebanks/detail/bank-detail-images.tsx\",\n    \"BankDetailImages\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,mBAAmB,CAAA,GAAA,8VAAA,CAAA,0BAAuB,AAAD,EAClD;IAAa,MAAM,IAAI,MAAM;AAAgP,GAC7Q,0EACA", "debugId": null}}, {"offset": {"line": 510, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 520, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/bloomgoo/gold-exchange-platform/frontend/components/common/admin/ebanks/detail/bank-detail-system.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const BankDetailSystem = registerClientReference(\n    function() { throw new Error(\"Attempted to call BankDetailSystem() from the server but BankDetailSystem is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/common/admin/ebanks/detail/bank-detail-system.tsx <module evaluation>\",\n    \"BankDetailSystem\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,mBAAmB,CAAA,GAAA,8VAAA,CAAA,0BAAuB,AAAD,EAClD;IAAa,MAAM,IAAI,MAAM;AAAgP,GAC7Q,8FACA", "debugId": null}}, {"offset": {"line": 534, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/bloomgoo/gold-exchange-platform/frontend/components/common/admin/ebanks/detail/bank-detail-system.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const BankDetailSystem = registerClientReference(\n    function() { throw new Error(\"Attempted to call BankDetailSystem() from the server but BankDetailSystem is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/common/admin/ebanks/detail/bank-detail-system.tsx\",\n    \"BankDetailSystem\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,mBAAmB,CAAA,GAAA,8VAAA,CAAA,0BAAuB,AAAD,EAClD;IAAa,MAAM,IAAI,MAAM;AAAgP,GAC7Q,0EACA", "debugId": null}}, {"offset": {"line": 548, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 558, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/bloomgoo/gold-exchange-platform/frontend/lib/utils.ts"], "sourcesContent": ["import LexoRank from '@kayron013/lexorank'\r\nimport { clsx, type ClassValue } from \"clsx\"\r\nimport { format, isValid } from \"date-fns\"\r\nimport { vi } from \"date-fns/locale\"\r\nimport { twMerge } from \"tailwind-merge\"\r\n\r\nexport type OrderType = 'buy' | 'sell';\r\nexport type BusinessType = 'NORMAL' | 'IMMEDIATE_DELIVERY';\r\n\r\nexport interface ProductItem {\r\n  productId: string;\r\n  volume: number;\r\n  productInfo?: {\r\n    weight: number;\r\n    [key: string]: any;\r\n  };\r\n  [key: string]: any;\r\n}\r\n\r\nexport function cn(...inputs: ClassValue[]) {\r\n  return twMerge(clsx(inputs))\r\n}\r\n\r\n/**\r\n * Format a number as currency in VND (đồng bộ với backend)\r\n * @param amount The amount to format\r\n * @param options Additional formatting options\r\n * @returns Formatted currency string\r\n */\r\nexport function formatCurrency(\r\n  amount: number | undefined | null,\r\n  options?: Intl.NumberFormatOptions\r\n) {\r\n  if (amount === undefined || amount === null) return \"---\"\r\n  return new Intl.NumberFormat('vi-VN', {\r\n    minimumFractionDigits: 0,\r\n    maximumFractionDigits: 0,\r\n    ...options\r\n  }).format(amount)\r\n}\r\n\r\n/**\r\n * Format number with thousand separators (đồng bộ với backend)\r\n * @param value Number to format\r\n * @param decimalPlaces Number of decimal places\r\n * @returns Formatted number string\r\n */\r\nexport function formatNumber(value: number | undefined | null, decimalPlaces: number = 0): string {\r\n  if (value === undefined || value === null) return \"---\"\r\n  return new Intl.NumberFormat('vi-VN', {\r\n    minimumFractionDigits: decimalPlaces,\r\n    maximumFractionDigits: decimalPlaces,\r\n  }).format(value)\r\n}\r\n\r\n/**\r\n * Format percentage (đồng bộ với backend)\r\n * @param value Value (0.15 = 15%)\r\n * @param decimalPlaces Number of decimal places\r\n * @returns Formatted percentage string\r\n */\r\nexport function formatPercentage(value: number | undefined | null, decimalPlaces: number = 2): string {\r\n  if (value === undefined || value === null) return \"---\"\r\n  return new Intl.NumberFormat('vi-VN', {\r\n    style: 'percent',\r\n    minimumFractionDigits: decimalPlaces,\r\n    maximumFractionDigits: decimalPlaces,\r\n  }).format(value)\r\n}\r\n\r\n/**\r\n * Tính giá quy đổi đơn giản từ USD sang VND\r\n * @param usdPrice Giá USD\r\n * @param exchangeRate Tỷ giá USD/VND (mặc định: 27000)\r\n * @returns Giá quy đổi sang VND\r\n */\r\nexport function calculateExchangePrice(usdPrice: number, exchangeRate: number = 27000) {\r\n  return Math.round(usdPrice * exchangeRate);\r\n}\r\n\r\n/**\r\n * Format giá quy đổi từ USD sang VND\r\n * @param usdPrice Giá USD\r\n * @param exchangeRate Tỷ giá USD/VND (mặc định: 27000)\r\n * @returns Giá quy đổi sang VND đã định dạng\r\n */\r\nexport function formatExchangePrice(usdPrice: number | undefined | null, exchangeRate: number = 27000) {\r\n  if (usdPrice === undefined || usdPrice === null) return \"---\";\r\n\r\n  const vndPrice = calculateExchangePrice(usdPrice, exchangeRate);\r\n\r\n  return new Intl.NumberFormat('vi-VN', {\r\n    style: 'currency',\r\n    currency: 'VND',\r\n    maximumFractionDigits: 0\r\n  }).format(vndPrice);\r\n}\r\n\r\n/**\r\n * Tính giá product cho bạc ký quỹ online và bán\r\n * @param priceVND Giá bạc (VND) từ websocket\r\n * @param oz Trọng lượng bạc (oz)\r\n * @param quantity Số lượng product (mặc định là 1)\r\n * @returns Giá product đã tính toán (VND)\r\n */\r\nexport function calculateNormalProductPrice(priceVND: number, oz: number = 1, quantity: number = 1) {\r\n  if (isNaN(priceVND) || priceVND <= 0 || isNaN(oz) || oz <= 0) {\r\n    return 0;\r\n  }\r\n  return Math.round((priceVND * oz) * quantity);\r\n}\r\n\r\n/**\r\n * Tính giá product cho bạc giao ngay\r\n * @param priceVND Giá bạc (VND) từ websocket\r\n * @param oz Trọng lượng bạc (oz)\r\n * @param quantity Số lượng product (mặc định là 1)\r\n * @returns Giá product đã tính toán (VND)\r\n */\r\nexport function calculateDeliveryProductPrice(priceVND: number, oz: number = 1, quantity: number = 1) {\r\n  if (isNaN(priceVND) || priceVND <= 0 || isNaN(oz) || oz <= 0) {\r\n    return 0;\r\n  }\r\n  // Giá = (giá * oz * số lượng) + phí gia công\r\n  // Phí gia công = (oz * số lượng) * 50,000\r\n  // Ví dụ: sản phẩm 3oz, số lượng 2\r\n  // - Giá = 761,000 * 3 * 2 = 4,566,000\r\n  // - Phí gia công = (3 * 2) * 50,000 = 300,000\r\n  // - Tổng = 4,566,000 + 300,000 = 4,866,000\r\n  const totalOz = oz * quantity;\r\n  return Math.round((priceVND * oz * quantity) + calculateProcessingFee(totalOz));\r\n}\r\n\r\n/**\r\n * Tính phí gia công cho bạc giao ngay\r\n * @param oz Tổng trọng lượng bạc (oz) = weight * volume\r\n * @returns Phí gia công (VND)\r\n */\r\nexport function calculateProcessingFee(oz: number = 1) {\r\n  if (isNaN(oz) || oz <= 0) {\r\n    return 0;\r\n  }\r\n  // Phí gia công = tổng oz * 50,000\r\n  return Math.round(oz * 50000);\r\n}\r\n\r\n/**\r\n * Format giá token dựa trên giá VND từ websocket\r\n * @param priceVND Giá bạc (VND) từ websocket\r\n * @param oz Trọng lượng bạc (oz)\r\n * @param volume Số lượng token (mặc định là 1)\r\n * @param isDelivery Có phải là bạc giao ngay không\r\n * @returns Giá token đã định dạng (VND)\r\n */\r\nexport function formatTokenPrice(priceVND: number | undefined | null, oz: number = 1, volume: number = 1, isDelivery: boolean = false) {\r\n  if (priceVND === undefined || priceVND === null) return \"---\";\r\n\r\n  const price = isDelivery \r\n    ? calculateDeliveryProductPrice(priceVND, oz, volume)\r\n    : calculateNormalProductPrice(priceVND, oz, volume);\r\n\r\n  return formatCurrency(price);\r\n}\r\n\r\n/**\r\n * Tính tổng giá trị của các product items\r\n * @param items Danh sách các product items\r\n * @param priceVND Giá bạc (VND) từ websocket\r\n * @param orderType Loại lệnh ('buy' hoặc 'sell')\r\n * @param businessType Loại hình giao dịch (chỉ áp dụng cho lệnh mua)\r\n * @returns Tổng giá trị (VND)\r\n */\r\nexport function calculateTotal(\r\n  items: ProductItem[], \r\n  priceVND: number, \r\n  orderType: OrderType,\r\n  businessType?: BusinessType\r\n): number {\r\n  // Kiểm tra input\r\n  if (!items?.length || priceVND <= 0) return 0;\r\n\r\n  return items.reduce((total, item) => {\r\n    // Bỏ qua nếu không có thông tin sản phẩm hoặc số lượng\r\n    if (!item.productId || !item.volume || !item.productInfo?.weight) return total;\r\n\r\n    const weight = item.productInfo.weight;\r\n    const volume = item.volume;\r\n\r\n    // Xử lý theo loại lệnh\r\n    if (orderType === 'buy') {\r\n      // Lệnh mua: phân biệt theo businessType\r\n      if (businessType === 'IMMEDIATE_DELIVERY') {\r\n        // Bạc giao ngay: giá = (giá * oz * số lượng) + phí gia công\r\n        return total + calculateDeliveryProductPrice(priceVND, weight, volume);\r\n      } else {\r\n        // Bạc ký quỹ online: giá = giá * oz * số lượng\r\n        return total + calculateNormalProductPrice(priceVND, weight, volume);\r\n      }\r\n    } else {\r\n      // Lệnh bán: luôn tính như bạc ký quỹ\r\n      return total + calculateNormalProductPrice(priceVND, weight, volume);\r\n    }\r\n  }, 0);\r\n}\r\n\r\n/**\r\n * Tính tiền đặt cọc (10% tổng giá trị)\r\n * @param items Danh sách các product items\r\n * @param priceVND Giá bạc (VND) từ websocket\r\n * @param orderType Loại lệnh ('buy' hoặc 'sell')\r\n * @param businessType Loại hình giao dịch (chỉ áp dụng cho lệnh mua)\r\n * @returns Số tiền đặt cọc (VND)\r\n */\r\nexport function calculateDeposit(\r\n  items: ProductItem[], \r\n  priceVND: number, \r\n  orderType: OrderType,\r\n  businessType?: BusinessType\r\n): number {\r\n  const total = calculateTotal(items, priceVND, orderType, businessType);\r\n  return total * 0.1; // 10% tổng giá trị\r\n}\r\n\r\n/**\r\n * Tính tiền tất toán (90% tổng giá trị)\r\n * @param items Danh sách các token items\r\n * @param worldPrice Giá bạc thế giới (USD/oz)\r\n * @returns Số tiền tất toán (VND)\r\n */\r\nexport function calculateSettlement(items: any[], worldPrice: number): number {\r\n  return calculateTotal(items, worldPrice) * 0.9;\r\n}\r\n\r\n/**\r\n * Định dạng ngày giờ theo chuẩn Việt Nam (dd/MM/yyyy HH:mm)\r\n * @param date Đối tượng Date, chuỗi ISO hoặc timestamp\r\n * @param emptyValue Giá trị trả về khi date là null hoặc undefined\r\n * @returns Chuỗi ngày giờ đã định dạng\r\n */\r\nexport function formatDateTime(date: Date | string | number | null | undefined, emptyValue: string = \"---\"): string {\r\n  if (date === null || date === undefined) return emptyValue;\r\n\r\n  const dateObj = typeof date === 'string' || typeof date === 'number'\r\n    ? new Date(date)\r\n    : date;\r\n\r\n  if (!isValid(dateObj)) return \"Không hợp lệ\";\r\n\r\n  try {\r\n    return format(dateObj, \"dd/MM/yyyy HH:mm\", { locale: vi });\r\n  } catch (error) {\r\n    return \"Không hợp lệ\";\r\n  }\r\n}\r\n\r\n/**\r\n * Định dạng chỉ ngày theo chuẩn Việt Nam (dd/MM/yyyy)\r\n * @param date Đối tượng Date, chuỗi ISO hoặc timestamp\r\n * @param emptyValue Giá trị trả về khi date là null hoặc undefined\r\n * @returns Chuỗi ngày đã định dạng\r\n */\r\nexport function formatDate(date: Date | string | number | null | undefined, emptyValue: string = \"---\"): string {\r\n  if (date === null || date === undefined) return emptyValue;\r\n\r\n  const dateObj = typeof date === 'string' || typeof date === 'number'\r\n    ? new Date(date)\r\n    : date;\r\n\r\n  if (!isValid(dateObj)) return \"Không hợp lệ\";\r\n\r\n  try {\r\n    return format(dateObj, \"dd/MM/yyyy\", { locale: vi });\r\n  } catch (error) {\r\n    return \"Không hợp lệ\";\r\n  }\r\n}\r\n\r\n/**\r\n * Định dạng chỉ thời gian theo chuẩn Việt Nam (HH:mm:ss)\r\n * @param date Đối tượng Date, chuỗi ISO hoặc timestamp\r\n * @param emptyValue Giá trị trả về khi date là null hoặc undefined\r\n * @returns Chuỗi thời gian đã định dạng\r\n */\r\nexport function formatTime(date: Date | string | number | null | undefined, emptyValue: string = \"---\"): string {\r\n  if (date === null || date === undefined) return emptyValue;\r\n\r\n  const dateObj = typeof date === 'string' || typeof date === 'number'\r\n    ? new Date(date)\r\n    : date;\r\n\r\n  if (!isValid(dateObj)) return \"Không hợp lệ\";\r\n\r\n  try {\r\n    return format(dateObj, \"HH:mm:ss\", { locale: vi });\r\n  } catch (error) {\r\n    return \"Không hợp lệ\";\r\n  }\r\n}\r\n\r\n/**\r\n * Định dạng ngày giờ đầy đủ theo chuẩn Việt Nam (dd/MM/yyyy HH:mm:ss)\r\n * @param date Đối tượng Date, chuỗi ISO hoặc timestamp\r\n * @param emptyValue Giá trị trả về khi date là null hoặc undefined\r\n * @returns Chuỗi ngày giờ đầy đủ đã định dạng\r\n */\r\nexport function formatFullDateTime(date: Date | string | number | null | undefined, emptyValue: string = \"---\"): string {\r\n  if (date === null || date === undefined) return emptyValue;\r\n\r\n  const dateObj = typeof date === 'string' || typeof date === 'number'\r\n    ? new Date(date)\r\n    : date;\r\n\r\n  if (!isValid(dateObj)) return \"Không hợp lệ\";\r\n\r\n  try {\r\n    return format(dateObj, \"dd/MM/yyyy HH:mm:ss\", { locale: vi });\r\n  } catch (error) {\r\n    return \"Không hợp lệ\";\r\n  }\r\n}\r\n\r\nexport function censorPhone(phone: string): string {\r\n  if (!phone || phone.length < 4) return phone;\r\n  const visiblePart = phone.slice(-3);\r\n  const hiddenPart = \"*\".repeat(Math.max(0, phone.length - 3));\r\n  return hiddenPart + visiblePart;\r\n}\r\n\r\nexport function censorEmail(email: string): string {\r\n  if (!email || !email.includes('@')) return email;\r\n  const [localPart, domain] = email.split('@');\r\n  if (localPart.length <= 2) return email;\r\n\r\n  const visibleStart = localPart.slice(0, 2);\r\n  const visibleEnd = localPart.slice(-1);\r\n  const hiddenPart = \"*\".repeat(Math.max(0, localPart.length - 3));\r\n\r\n  return `${visibleStart}${hiddenPart}${visibleEnd}@${domain}`;\r\n};\r\n\r\n// JSON parsing utility\r\nexport function safeParseJSON<T>(jsonString: string | null | undefined, fallback: T): T {\r\n  if (!jsonString) return fallback;\r\n\r\n  try {\r\n    return JSON.parse(jsonString) as T;\r\n  } catch (error) {\r\n    console.warn('Failed to parse JSON:', error);\r\n    return fallback;\r\n  }\r\n}\r\n\r\n\r\nexport { LexoRank }\r\n\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;AAAA;AACA;AACA;AAAA;AACA;AACA;;;;;;AAeO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,yNAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,sLAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAQO,SAAS,eACd,MAAiC,EACjC,OAAkC;IAElC,IAAI,WAAW,aAAa,WAAW,MAAM,OAAO;IACpD,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;QACpC,uBAAuB;QACvB,uBAAuB;QACvB,GAAG,OAAO;IACZ,GAAG,MAAM,CAAC;AACZ;AAQO,SAAS,aAAa,KAAgC,EAAE,gBAAwB,CAAC;IACtF,IAAI,UAAU,aAAa,UAAU,MAAM,OAAO;IAClD,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;QACpC,uBAAuB;QACvB,uBAAuB;IACzB,GAAG,MAAM,CAAC;AACZ;AAQO,SAAS,iBAAiB,KAAgC,EAAE,gBAAwB,CAAC;IAC1F,IAAI,UAAU,aAAa,UAAU,MAAM,OAAO;IAClD,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;QACpC,OAAO;QACP,uBAAuB;QACvB,uBAAuB;IACzB,GAAG,MAAM,CAAC;AACZ;AAQO,SAAS,uBAAuB,QAAgB,EAAE,eAAuB,KAAK;IACnF,OAAO,KAAK,KAAK,CAAC,WAAW;AAC/B;AAQO,SAAS,oBAAoB,QAAmC,EAAE,eAAuB,KAAK;IACnG,IAAI,aAAa,aAAa,aAAa,MAAM,OAAO;IAExD,MAAM,WAAW,uBAAuB,UAAU;IAElD,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;QACpC,OAAO;QACP,UAAU;QACV,uBAAuB;IACzB,GAAG,MAAM,CAAC;AACZ;AASO,SAAS,4BAA4B,QAAgB,EAAE,KAAa,CAAC,EAAE,WAAmB,CAAC;IAChG,IAAI,MAAM,aAAa,YAAY,KAAK,MAAM,OAAO,MAAM,GAAG;QAC5D,OAAO;IACT;IACA,OAAO,KAAK,KAAK,CAAC,AAAC,WAAW,KAAM;AACtC;AASO,SAAS,8BAA8B,QAAgB,EAAE,KAAa,CAAC,EAAE,WAAmB,CAAC;IAClG,IAAI,MAAM,aAAa,YAAY,KAAK,MAAM,OAAO,MAAM,GAAG;QAC5D,OAAO;IACT;IACA,6CAA6C;IAC7C,0CAA0C;IAC1C,kCAAkC;IAClC,sCAAsC;IACtC,8CAA8C;IAC9C,2CAA2C;IAC3C,MAAM,UAAU,KAAK;IACrB,OAAO,KAAK,KAAK,CAAC,AAAC,WAAW,KAAK,WAAY,uBAAuB;AACxE;AAOO,SAAS,uBAAuB,KAAa,CAAC;IACnD,IAAI,MAAM,OAAO,MAAM,GAAG;QACxB,OAAO;IACT;IACA,kCAAkC;IAClC,OAAO,KAAK,KAAK,CAAC,KAAK;AACzB;AAUO,SAAS,iBAAiB,QAAmC,EAAE,KAAa,CAAC,EAAE,SAAiB,CAAC,EAAE,aAAsB,KAAK;IACnI,IAAI,aAAa,aAAa,aAAa,MAAM,OAAO;IAExD,MAAM,QAAQ,aACV,8BAA8B,UAAU,IAAI,UAC5C,4BAA4B,UAAU,IAAI;IAE9C,OAAO,eAAe;AACxB;AAUO,SAAS,eACd,KAAoB,EACpB,QAAgB,EAChB,SAAoB,EACpB,YAA2B;IAE3B,iBAAiB;IACjB,IAAI,CAAC,OAAO,UAAU,YAAY,GAAG,OAAO;IAE5C,OAAO,MAAM,MAAM,CAAC,CAAC,OAAO;QAC1B,uDAAuD;QACvD,IAAI,CAAC,KAAK,SAAS,IAAI,CAAC,KAAK,MAAM,IAAI,CAAC,KAAK,WAAW,EAAE,QAAQ,OAAO;QAEzE,MAAM,SAAS,KAAK,WAAW,CAAC,MAAM;QACtC,MAAM,SAAS,KAAK,MAAM;QAE1B,uBAAuB;QACvB,IAAI,cAAc,OAAO;YACvB,wCAAwC;YACxC,IAAI,iBAAiB,sBAAsB;gBACzC,4DAA4D;gBAC5D,OAAO,QAAQ,8BAA8B,UAAU,QAAQ;YACjE,OAAO;gBACL,+CAA+C;gBAC/C,OAAO,QAAQ,4BAA4B,UAAU,QAAQ;YAC/D;QACF,OAAO;YACL,qCAAqC;YACrC,OAAO,QAAQ,4BAA4B,UAAU,QAAQ;QAC/D;IACF,GAAG;AACL;AAUO,SAAS,iBACd,KAAoB,EACpB,QAAgB,EAChB,SAAoB,EACpB,YAA2B;IAE3B,MAAM,QAAQ,eAAe,OAAO,UAAU,WAAW;IACzD,OAAO,QAAQ,KAAK,mBAAmB;AACzC;AAQO,SAAS,oBAAoB,KAAY,EAAE,UAAkB;IAClE,OAAO,eAAe,OAAO,cAAc;AAC7C;AAQO,SAAS,eAAe,IAA+C,EAAE,aAAqB,KAAK;IACxG,IAAI,SAAS,QAAQ,SAAS,WAAW,OAAO;IAEhD,MAAM,UAAU,OAAO,SAAS,YAAY,OAAO,SAAS,WACxD,IAAI,KAAK,QACT;IAEJ,IAAI,CAAC,CAAA,GAAA,8LAAA,CAAA,UAAO,AAAD,EAAE,UAAU,OAAO;IAE9B,IAAI;QACF,OAAO,CAAA,GAAA,6MAAA,CAAA,SAAM,AAAD,EAAE,SAAS,oBAAoB;YAAE,QAAQ,mMAAA,CAAA,KAAE;QAAC;IAC1D,EAAE,OAAO,OAAO;QACd,OAAO;IACT;AACF;AAQO,SAAS,WAAW,IAA+C,EAAE,aAAqB,KAAK;IACpG,IAAI,SAAS,QAAQ,SAAS,WAAW,OAAO;IAEhD,MAAM,UAAU,OAAO,SAAS,YAAY,OAAO,SAAS,WACxD,IAAI,KAAK,QACT;IAEJ,IAAI,CAAC,CAAA,GAAA,8LAAA,CAAA,UAAO,AAAD,EAAE,UAAU,OAAO;IAE9B,IAAI;QACF,OAAO,CAAA,GAAA,6MAAA,CAAA,SAAM,AAAD,EAAE,SAAS,cAAc;YAAE,QAAQ,mMAAA,CAAA,KAAE;QAAC;IACpD,EAAE,OAAO,OAAO;QACd,OAAO;IACT;AACF;AAQO,SAAS,WAAW,IAA+C,EAAE,aAAqB,KAAK;IACpG,IAAI,SAAS,QAAQ,SAAS,WAAW,OAAO;IAEhD,MAAM,UAAU,OAAO,SAAS,YAAY,OAAO,SAAS,WACxD,IAAI,KAAK,QACT;IAEJ,IAAI,CAAC,CAAA,GAAA,8LAAA,CAAA,UAAO,AAAD,EAAE,UAAU,OAAO;IAE9B,IAAI;QACF,OAAO,CAAA,GAAA,6MAAA,CAAA,SAAM,AAAD,EAAE,SAAS,YAAY;YAAE,QAAQ,mMAAA,CAAA,KAAE;QAAC;IAClD,EAAE,OAAO,OAAO;QACd,OAAO;IACT;AACF;AAQO,SAAS,mBAAmB,IAA+C,EAAE,aAAqB,KAAK;IAC5G,IAAI,SAAS,QAAQ,SAAS,WAAW,OAAO;IAEhD,MAAM,UAAU,OAAO,SAAS,YAAY,OAAO,SAAS,WACxD,IAAI,KAAK,QACT;IAEJ,IAAI,CAAC,CAAA,GAAA,8LAAA,CAAA,UAAO,AAAD,EAAE,UAAU,OAAO;IAE9B,IAAI;QACF,OAAO,CAAA,GAAA,6MAAA,CAAA,SAAM,AAAD,EAAE,SAAS,uBAAuB;YAAE,QAAQ,mMAAA,CAAA,KAAE;QAAC;IAC7D,EAAE,OAAO,OAAO;QACd,OAAO;IACT;AACF;AAEO,SAAS,YAAY,KAAa;IACvC,IAAI,CAAC,SAAS,MAAM,MAAM,GAAG,GAAG,OAAO;IACvC,MAAM,cAAc,MAAM,KAAK,CAAC,CAAC;IACjC,MAAM,aAAa,IAAI,MAAM,CAAC,KAAK,GAAG,CAAC,GAAG,MAAM,MAAM,GAAG;IACzD,OAAO,aAAa;AACtB;AAEO,SAAS,YAAY,KAAa;IACvC,IAAI,CAAC,SAAS,CAAC,MAAM,QAAQ,CAAC,MAAM,OAAO;IAC3C,MAAM,CAAC,WAAW,OAAO,GAAG,MAAM,KAAK,CAAC;IACxC,IAAI,UAAU,MAAM,IAAI,GAAG,OAAO;IAElC,MAAM,eAAe,UAAU,KAAK,CAAC,GAAG;IACxC,MAAM,aAAa,UAAU,KAAK,CAAC,CAAC;IACpC,MAAM,aAAa,IAAI,MAAM,CAAC,KAAK,GAAG,CAAC,GAAG,UAAU,MAAM,GAAG;IAE7D,OAAO,GAAG,eAAe,aAAa,WAAW,CAAC,EAAE,QAAQ;AAC9D;AAGO,SAAS,cAAiB,UAAqC,EAAE,QAAW;IACjF,IAAI,CAAC,YAAY,OAAO;IAExB,IAAI;QACF,OAAO,KAAK,KAAK,CAAC;IACpB,EAAE,OAAO,OAAO;QACd,QAAQ,IAAI,CAAC,yBAAyB;QACtC,OAAO;IACT;AACF", "debugId": null}}, {"offset": {"line": 780, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/bloomgoo/gold-exchange-platform/frontend/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\r\nimport { Slot } from \"@radix-ui/react-slot\"\r\nimport { cva, type VariantProps } from \"class-variance-authority\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst buttonVariants = cva(\r\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\r\n  {\r\n    variants: {\r\n      variant: {\r\n        default:\r\n          \"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90\",\r\n        destructive:\r\n          \"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\r\n        outline:\r\n          \"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50\",\r\n        secondary:\r\n          \"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80\",\r\n        ghost:\r\n          \"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50\",\r\n        link: \"text-primary underline-offset-4 hover:underline\",\r\n      },\r\n      size: {\r\n        default: \"h-9 px-4 py-2 has-[>svg]:px-3\",\r\n        sm: \"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5\",\r\n        lg: \"h-10 rounded-md px-6 has-[>svg]:px-4\",\r\n        icon: \"size-9\",\r\n      },\r\n    },\r\n    defaultVariants: {\r\n      variant: \"default\",\r\n      size: \"default\",\r\n    },\r\n  }\r\n)\r\n\r\nfunction Button({\r\n  className,\r\n  variant,\r\n  size,\r\n  asChild = false,\r\n  ...props\r\n}: React.ComponentProps<\"button\"> &\r\n  VariantProps<typeof buttonVariants> & {\r\n    asChild?: boolean\r\n  }) {\r\n  const Comp = asChild ? Slot : \"button\"\r\n\r\n  return (\r\n    <Comp\r\n      data-slot=\"button\"\r\n      className={cn(buttonVariants({ variant, size, className }))}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport { Button, buttonVariants }\r\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;AAAA;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,2OAAA,CAAA,MAAG,AAAD,EACvB,+bACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OACE;YACF,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,SAAS,OAAO,EACd,SAAS,EACT,OAAO,EACP,IAAI,EACJ,UAAU,KAAK,EACf,GAAG,OAIF;IACD,MAAM,OAAO,UAAU,oSAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,uVAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACvD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 838, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/bloomgoo/gold-exchange-platform/frontend/components/ui/avatar.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const Avatar = registerClientReference(\n    function() { throw new Error(\"Attempted to call Avatar() from the server but Avatar is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/ui/avatar.tsx <module evaluation>\",\n    \"Avatar\",\n);\nexport const AvatarFallback = registerClientReference(\n    function() { throw new Error(\"Attempted to call AvatarFallback() from the server but AvatarFallback is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/ui/avatar.tsx <module evaluation>\",\n    \"AvatarFallback\",\n);\nexport const AvatarImage = registerClientReference(\n    function() { throw new Error(\"Attempted to call AvatarImage() from the server but AvatarImage is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/ui/avatar.tsx <module evaluation>\",\n    \"AvatarImage\",\n);\n"], "names": [], "mappings": ";;;;;AAAA;;AACO,MAAM,SAAS,CAAA,GAAA,8VAAA,CAAA,0BAAuB,AAAD,EACxC;IAAa,MAAM,IAAI,MAAM;AAA4N,GACzP,0DACA;AAEG,MAAM,iBAAiB,CAAA,GAAA,8VAAA,CAAA,0BAAuB,AAAD,EAChD;IAAa,MAAM,IAAI,MAAM;AAA4O,GACzQ,0DACA;AAEG,MAAM,cAAc,CAAA,GAAA,8VAAA,CAAA,0BAAuB,AAAD,EAC7C;IAAa,MAAM,IAAI,MAAM;AAAsO,GACnQ,0DACA", "debugId": null}}, {"offset": {"line": 860, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/bloomgoo/gold-exchange-platform/frontend/components/ui/avatar.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const Avatar = registerClientReference(\n    function() { throw new Error(\"Attempted to call Avatar() from the server but Avatar is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/ui/avatar.tsx\",\n    \"Avatar\",\n);\nexport const AvatarFallback = registerClientReference(\n    function() { throw new Error(\"Attempted to call AvatarFallback() from the server but AvatarFallback is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/ui/avatar.tsx\",\n    \"AvatarFallback\",\n);\nexport const AvatarImage = registerClientReference(\n    function() { throw new Error(\"Attempted to call AvatarImage() from the server but AvatarImage is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/ui/avatar.tsx\",\n    \"AvatarImage\",\n);\n"], "names": [], "mappings": ";;;;;AAAA;;AACO,MAAM,SAAS,CAAA,GAAA,8VAAA,CAAA,0BAAuB,AAAD,EACxC;IAAa,MAAM,IAAI,MAAM;AAA4N,GACzP,sCACA;AAEG,MAAM,iBAAiB,CAAA,GAAA,8VAAA,CAAA,0BAAuB,AAAD,EAChD;IAAa,MAAM,IAAI,MAAM;AAA4O,GACzQ,sCACA;AAEG,MAAM,cAAc,CAAA,GAAA,8VAAA,CAAA,0BAAuB,AAAD,EAC7C;IAAa,MAAM,IAAI,MAAM;AAAsO,GACnQ,sCACA", "debugId": null}}, {"offset": {"line": 882, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 892, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/bloomgoo/gold-exchange-platform/frontend/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\r\nimport { Slot } from \"@radix-ui/react-slot\"\r\nimport { cva, type VariantProps } from \"class-variance-authority\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst badgeVariants = cva(\r\n  \"inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden\",\r\n  {\r\n    variants: {\r\n      variant: {\r\n        default:\r\n          \"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90\",\r\n        secondary:\r\n          \"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90\",\r\n        destructive:\r\n          \"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\r\n        outline:\r\n          \"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground\",\r\n      },\r\n    },\r\n    defaultVariants: {\r\n      variant: \"default\",\r\n    },\r\n  }\r\n)\r\n\r\nfunction Badge({\r\n  className,\r\n  variant,\r\n  asChild = false,\r\n  ...props\r\n}: React.ComponentProps<\"span\"> &\r\n  VariantProps<typeof badgeVariants> & { asChild?: boolean }) {\r\n  const Comp = asChild ? Slot : \"span\"\r\n\r\n  return (\r\n    <Comp\r\n      data-slot=\"badge\"\r\n      className={cn(badgeVariants({ variant }), className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport { Badge, badgeVariants }\r\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;AAAA;;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,2OAAA,CAAA,MAAG,AAAD,EACtB,kZACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,SAAS,MAAM,EACb,SAAS,EACT,OAAO,EACP,UAAU,KAAK,EACf,GAAG,OAEuD;IAC1D,MAAM,OAAO,UAAU,oSAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,uVAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 939, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/bloomgoo/gold-exchange-platform/frontend/components/ui/tooltip.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const Tooltip = registerClientReference(\n    function() { throw new Error(\"Attempted to call Tooltip() from the server but Tooltip is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/ui/tooltip.tsx <module evaluation>\",\n    \"Tooltip\",\n);\nexport const TooltipContent = registerClientReference(\n    function() { throw new Error(\"Attempted to call TooltipContent() from the server but TooltipContent is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/ui/tooltip.tsx <module evaluation>\",\n    \"TooltipContent\",\n);\nexport const TooltipProvider = registerClientReference(\n    function() { throw new Error(\"Attempted to call TooltipProvider() from the server but TooltipProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/ui/tooltip.tsx <module evaluation>\",\n    \"TooltipProvider\",\n);\nexport const TooltipTrigger = registerClientReference(\n    function() { throw new Error(\"Attempted to call TooltipTrigger() from the server but TooltipTrigger is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/ui/tooltip.tsx <module evaluation>\",\n    \"TooltipTrigger\",\n);\n"], "names": [], "mappings": ";;;;;;AAAA;;AACO,MAAM,UAAU,CAAA,GAAA,8VAAA,CAAA,0BAAuB,AAAD,EACzC;IAAa,MAAM,IAAI,MAAM;AAA8N,GAC3P,2DACA;AAEG,MAAM,iBAAiB,CAAA,GAAA,8VAAA,CAAA,0BAAuB,AAAD,EAChD;IAAa,MAAM,IAAI,MAAM;AAA4O,GACzQ,2DACA;AAEG,MAAM,kBAAkB,CAAA,GAAA,8VAAA,CAAA,0BAAuB,AAAD,EACjD;IAAa,MAAM,IAAI,MAAM;AAA8O,GAC3Q,2DACA;AAEG,MAAM,iBAAiB,CAAA,GAAA,8VAAA,CAAA,0BAAuB,AAAD,EAChD;IAAa,MAAM,IAAI,MAAM;AAA4O,GACzQ,2DACA", "debugId": null}}, {"offset": {"line": 965, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/bloomgoo/gold-exchange-platform/frontend/components/ui/tooltip.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const Tooltip = registerClientReference(\n    function() { throw new Error(\"Attempted to call Tooltip() from the server but Tooltip is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/ui/tooltip.tsx\",\n    \"Tooltip\",\n);\nexport const TooltipContent = registerClientReference(\n    function() { throw new Error(\"Attempted to call TooltipContent() from the server but TooltipContent is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/ui/tooltip.tsx\",\n    \"TooltipContent\",\n);\nexport const TooltipProvider = registerClientReference(\n    function() { throw new Error(\"Attempted to call TooltipProvider() from the server but TooltipProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/ui/tooltip.tsx\",\n    \"TooltipProvider\",\n);\nexport const TooltipTrigger = registerClientReference(\n    function() { throw new Error(\"Attempted to call TooltipTrigger() from the server but TooltipTrigger is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/ui/tooltip.tsx\",\n    \"TooltipTrigger\",\n);\n"], "names": [], "mappings": ";;;;;;AAAA;;AACO,MAAM,UAAU,CAAA,GAAA,8VAAA,CAAA,0BAAuB,AAAD,EACzC;IAAa,MAAM,IAAI,MAAM;AAA8N,GAC3P,uCACA;AAEG,MAAM,iBAAiB,CAAA,GAAA,8VAAA,CAAA,0BAAuB,AAAD,EAChD;IAAa,MAAM,IAAI,MAAM;AAA4O,GACzQ,uCACA;AAEG,MAAM,kBAAkB,CAAA,GAAA,8VAAA,CAAA,0BAAuB,AAAD,EACjD;IAAa,MAAM,IAAI,MAAM;AAA8O,GAC3Q,uCACA;AAEG,MAAM,iBAAiB,CAAA,GAAA,8VAAA,CAAA,0BAAuB,AAAD,EAChD;IAAa,MAAM,IAAI,MAAM;AAA4O,GACzQ,uCACA", "debugId": null}}, {"offset": {"line": 991, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 1001, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/bloomgoo/gold-exchange-platform/frontend/components/common/user/user-hover-card.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const UserHoverCard = registerClientReference(\n    function() { throw new Error(\"Attempted to call UserHoverCard() from the server but UserHoverCard is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/common/user/user-hover-card.tsx <module evaluation>\",\n    \"UserHoverCard\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,gBAAgB,CAAA,GAAA,8VAAA,CAAA,0BAAuB,AAAD,EAC/C;IAAa,MAAM,IAAI,MAAM;AAA0O,GACvQ,4EACA", "debugId": null}}, {"offset": {"line": 1015, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/bloomgoo/gold-exchange-platform/frontend/components/common/user/user-hover-card.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const UserHoverCard = registerClientReference(\n    function() { throw new Error(\"Attempted to call UserHoverCard() from the server but UserHoverCard is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/common/user/user-hover-card.tsx\",\n    \"UserHoverCard\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,gBAAgB,CAAA,GAAA,8VAAA,CAAA,0BAAuB,AAAD,EAC/C;IAAa,MAAM,IAAI,MAAM;AAA0O,GACvQ,wDACA", "debugId": null}}, {"offset": {"line": 1029, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 1039, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/bloomgoo/gold-exchange-platform/frontend/components/ui/date-time-display.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const DateDisplay = registerClientReference(\n    function() { throw new Error(\"Attempted to call DateDisplay() from the server but DateDisplay is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/ui/date-time-display.tsx <module evaluation>\",\n    \"DateDisplay\",\n);\nexport const DateTimeDisplay = registerClientReference(\n    function() { throw new Error(\"Attempted to call DateTimeDisplay() from the server but DateTimeDisplay is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/ui/date-time-display.tsx <module evaluation>\",\n    \"DateTimeDisplay\",\n);\nexport const DateTimeWithSeparateTimeDisplay = registerClientReference(\n    function() { throw new Error(\"Attempted to call DateTimeWithSeparateTimeDisplay() from the server but DateTimeWithSeparateTimeDisplay is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/ui/date-time-display.tsx <module evaluation>\",\n    \"DateTimeWithSeparateTimeDisplay\",\n);\nexport const FullDateTimeDisplay = registerClientReference(\n    function() { throw new Error(\"Attempted to call FullDateTimeDisplay() from the server but FullDateTimeDisplay is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/ui/date-time-display.tsx <module evaluation>\",\n    \"FullDateTimeDisplay\",\n);\nexport const TimeDisplay = registerClientReference(\n    function() { throw new Error(\"Attempted to call TimeDisplay() from the server but TimeDisplay is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/ui/date-time-display.tsx <module evaluation>\",\n    \"TimeDisplay\",\n);\n"], "names": [], "mappings": ";;;;;;;AAAA;;AACO,MAAM,cAAc,CAAA,GAAA,8VAAA,CAAA,0BAAuB,AAAD,EAC7C;IAAa,MAAM,IAAI,MAAM;AAAsO,GACnQ,qEACA;AAEG,MAAM,kBAAkB,CAAA,GAAA,8VAAA,CAAA,0BAAuB,AAAD,EACjD;IAAa,MAAM,IAAI,MAAM;AAA8O,GAC3Q,qEACA;AAEG,MAAM,kCAAkC,CAAA,GAAA,8VAAA,CAAA,0BAAuB,AAAD,EACjE;IAAa,MAAM,IAAI,MAAM;AAA8Q,GAC3S,qEACA;AAEG,MAAM,sBAAsB,CAAA,GAAA,8VAAA,CAAA,0BAAuB,AAAD,EACrD;IAAa,MAAM,IAAI,MAAM;AAAsP,GACnR,qEACA;AAEG,MAAM,cAAc,CAAA,GAAA,8VAAA,CAAA,0BAAuB,AAAD,EAC7C;IAAa,MAAM,IAAI,MAAM;AAAsO,GACnQ,qEACA", "debugId": null}}, {"offset": {"line": 1069, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/bloomgoo/gold-exchange-platform/frontend/components/ui/date-time-display.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const DateDisplay = registerClientReference(\n    function() { throw new Error(\"Attempted to call DateDisplay() from the server but DateDisplay is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/ui/date-time-display.tsx\",\n    \"DateDisplay\",\n);\nexport const DateTimeDisplay = registerClientReference(\n    function() { throw new Error(\"Attempted to call DateTimeDisplay() from the server but DateTimeDisplay is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/ui/date-time-display.tsx\",\n    \"DateTimeDisplay\",\n);\nexport const DateTimeWithSeparateTimeDisplay = registerClientReference(\n    function() { throw new Error(\"Attempted to call DateTimeWithSeparateTimeDisplay() from the server but DateTimeWithSeparateTimeDisplay is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/ui/date-time-display.tsx\",\n    \"DateTimeWithSeparateTimeDisplay\",\n);\nexport const FullDateTimeDisplay = registerClientReference(\n    function() { throw new Error(\"Attempted to call FullDateTimeDisplay() from the server but FullDateTimeDisplay is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/ui/date-time-display.tsx\",\n    \"FullDateTimeDisplay\",\n);\nexport const TimeDisplay = registerClientReference(\n    function() { throw new Error(\"Attempted to call TimeDisplay() from the server but TimeDisplay is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/ui/date-time-display.tsx\",\n    \"TimeDisplay\",\n);\n"], "names": [], "mappings": ";;;;;;;AAAA;;AACO,MAAM,cAAc,CAAA,GAAA,8VAAA,CAAA,0BAAuB,AAAD,EAC7C;IAAa,MAAM,IAAI,MAAM;AAAsO,GACnQ,iDACA;AAEG,MAAM,kBAAkB,CAAA,GAAA,8VAAA,CAAA,0BAAuB,AAAD,EACjD;IAAa,MAAM,IAAI,MAAM;AAA8O,GAC3Q,iDACA;AAEG,MAAM,kCAAkC,CAAA,GAAA,8VAAA,CAAA,0BAAuB,AAAD,EACjE;IAAa,MAAM,IAAI,MAAM;AAA8Q,GAC3S,iDACA;AAEG,MAAM,sBAAsB,CAAA,GAAA,8VAAA,CAAA,0BAAuB,AAAD,EACrD;IAAa,MAAM,IAAI,MAAM;AAAsP,GACnR,iDACA;AAEG,MAAM,cAAc,CAAA,GAAA,8VAAA,CAAA,0BAAuB,AAAD,EAC7C;IAAa,MAAM,IAAI,MAAM;AAAsO,GACnQ,iDACA", "debugId": null}}, {"offset": {"line": 1099, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 1109, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/bloomgoo/gold-exchange-platform/frontend/components/common/admin/ebanks/utils/bank-helpers.ts"], "sourcesContent": ["/**\r\n * Bank Helpers - <PERSON><PERSON><PERSON> hàm tiện ích cho module ngân hàng\r\n */\r\n\r\nimport { format } from 'date-fns';\r\nimport { vi } from 'date-fns/locale';\r\nimport type { BankDto, StatusFilter } from '../types';\r\n\r\n/**\r\n * Format date string theo định dạng Việt Nam\r\n */\r\nexport const formatDate = (dateStr: string | null | undefined): string => {\r\n  if (!dateStr) return '---';\r\n  try {\r\n    return format(new Date(dateStr), 'dd/MM/yyyy HH:mm', { locale: vi });\r\n  } catch (error) {\r\n    return 'Không hợp lệ';\r\n  }\r\n};\r\n\r\n/**\r\n * Lấy màu sắc cho status badge\r\n */\r\nexport const getStatusColor = (isActive: boolean): string => {\r\n  if (isActive) {\r\n    return 'bg-green-100 text-green-800 hover:bg-green-100/80';\r\n  } else {\r\n    return 'bg-gray-100 text-gray-800 hover:bg-gray-100/80';\r\n  }\r\n};\r\n\r\n/**\r\n * Lấy text cho status\r\n */\r\nexport const getStatusText = (isActive: boolean): string => {\r\n  return isActive ? 'Kích hoạt' : 'Vô hiệu hóa';\r\n};\r\n\r\n/**\r\n * Lấy variant cho status badge\r\n */\r\nexport const getStatusVariant = (isActive: boolean): 'success' | 'secondary' => {\r\n  return isActive ? 'success' : 'secondary';\r\n};\r\n\r\n/**\r\n * Filter banks theo status\r\n */\r\nexport const filterBanksByStatus = (banks: BankDto[], statusFilter: StatusFilter): BankDto[] => {\r\n  if (statusFilter === 'all') return banks;\r\n  if (statusFilter === 'active') return banks.filter(bank => bank.isActive);\r\n  if (statusFilter === 'inactive') return banks.filter(bank => !bank.isActive);\r\n  return banks;\r\n};\r\n\r\n/**\r\n * Tính toán status counts từ danh sách banks\r\n */\r\nexport const calculateStatusCounts = (banks: BankDto[]) => {\r\n  const active = banks.filter(bank => bank.isActive).length;\r\n  const inactive = banks.filter(bank => !bank.isActive).length;\r\n  \r\n  return {\r\n    all: banks.length,\r\n    active,\r\n    inactive,\r\n  };\r\n};\r\n\r\n/**\r\n * Tạo avatar fallback từ tên ngân hàng\r\n */\r\nexport const getBankAvatarFallback = (brandName: string): string => {\r\n  return brandName ? brandName[0].toUpperCase() : 'B';\r\n};\r\n\r\n/**\r\n * Tạo avatar URL từ tên ngân hàng\r\n */\r\nexport const getBankAvatarUrl = (brandName: string, logoPath?: string): string => {\r\n  if (logoPath) return logoPath;\r\n  return `https://api.dicebear.com/9.x/initials/svg?seed=${brandName}`;\r\n};\r\n\r\n/**\r\n * Validate URL\r\n */\r\nexport const isValidUrl = (url: string): boolean => {\r\n  try {\r\n    new URL(url);\r\n    return true;\r\n  } catch {\r\n    return false;\r\n  }\r\n};\r\n\r\n/**\r\n * Truncate text với tooltip\r\n */\r\nexport const truncateText = (text: string, maxLength: number): string => {\r\n  if (text.length <= maxLength) return text;\r\n  return text.substring(0, maxLength) + '...';\r\n};\r\n\r\n/**\r\n * Parse CSV content\r\n */\r\nexport const parseCSV = (content: string): any[] => {\r\n  const lines = content.split('\\n');\r\n  const headers = lines[0].split(',');\r\n  const data: any[] = [];\r\n\r\n  for (let i = 1; i < lines.length; i++) {\r\n    if (!lines[i].trim()) continue;\r\n\r\n    const values = lines[i].split(',');\r\n    const row: any = {};\r\n\r\n    headers.forEach((header, index) => {\r\n      row[header.trim()] = values[index]?.trim() || '';\r\n    });\r\n\r\n    data.push(row);\r\n  }\r\n\r\n  return data;\r\n};\r\n\r\n/**\r\n * Generate export filename\r\n */\r\nexport const generateExportFilename = (format: 'csv' | 'json'): string => {\r\n  const timestamp = format(new Date(), 'yyyy-MM-dd_HH-mm-ss');\r\n  return `banks-export_${timestamp}.${format}`;\r\n};\r\n\r\n/**\r\n * Map backend data to frontend DTO\r\n */\r\nexport const mapBankData = (backendData: any): BankDto => {\r\n  return {\r\n    ...backendData,\r\n    status: backendData.isActive ? 'ACTIVE' : 'INACTIVE',\r\n  };\r\n};\r\n\r\n/**\r\n * Prepare form data for API\r\n */\r\nexport const prepareFormData = (formData: any, mode: 'create' | 'update', bankId?: string) => {\r\n  const baseData = {\r\n    brandName: formData.brandName,\r\n    fullName: formData.fullName,\r\n    shortName: formData.shortName,\r\n    code: formData.code,\r\n    bin: formData.bin,\r\n    logoPath: formData.logoPath || undefined,\r\n    icon: formData.icon || undefined,\r\n    isActive: formData.isActive,\r\n  };\r\n\r\n  if (mode === 'update' && bankId) {\r\n    return {\r\n      id: bankId,\r\n      ...baseData,\r\n    };\r\n  }\r\n\r\n  return baseData;\r\n};\r\n"], "names": [], "mappings": "AAAA;;CAEC;;;;;;;;;;;;;;;;AAED;AACA;;;AAMO,MAAM,aAAa,CAAC;IACzB,IAAI,CAAC,SAAS,OAAO;IACrB,IAAI;QACF,OAAO,CAAA,GAAA,6MAAA,CAAA,SAAM,AAAD,EAAE,IAAI,KAAK,UAAU,oBAAoB;YAAE,QAAQ,mMAAA,CAAA,KAAE;QAAC;IACpE,EAAE,OAAO,OAAO;QACd,OAAO;IACT;AACF;AAKO,MAAM,iBAAiB,CAAC;IAC7B,IAAI,UAAU;QACZ,OAAO;IACT,OAAO;QACL,OAAO;IACT;AACF;AAKO,MAAM,gBAAgB,CAAC;IAC5B,OAAO,WAAW,cAAc;AAClC;AAKO,MAAM,mBAAmB,CAAC;IAC/B,OAAO,WAAW,YAAY;AAChC;AAKO,MAAM,sBAAsB,CAAC,OAAkB;IACpD,IAAI,iBAAiB,OAAO,OAAO;IACnC,IAAI,iBAAiB,UAAU,OAAO,MAAM,MAAM,CAAC,CAAA,OAAQ,KAAK,QAAQ;IACxE,IAAI,iBAAiB,YAAY,OAAO,MAAM,MAAM,CAAC,CAAA,OAAQ,CAAC,KAAK,QAAQ;IAC3E,OAAO;AACT;AAKO,MAAM,wBAAwB,CAAC;IACpC,MAAM,SAAS,MAAM,MAAM,CAAC,CAAA,OAAQ,KAAK,QAAQ,EAAE,MAAM;IACzD,MAAM,WAAW,MAAM,MAAM,CAAC,CAAA,OAAQ,CAAC,KAAK,QAAQ,EAAE,MAAM;IAE5D,OAAO;QACL,KAAK,MAAM,MAAM;QACjB;QACA;IACF;AACF;AAKO,MAAM,wBAAwB,CAAC;IACpC,OAAO,YAAY,SAAS,CAAC,EAAE,CAAC,WAAW,KAAK;AAClD;AAKO,MAAM,mBAAmB,CAAC,WAAmB;IAClD,IAAI,UAAU,OAAO;IACrB,OAAO,CAAC,+CAA+C,EAAE,WAAW;AACtE;AAKO,MAAM,aAAa,CAAC;IACzB,IAAI;QACF,IAAI,IAAI;QACR,OAAO;IACT,EAAE,OAAM;QACN,OAAO;IACT;AACF;AAKO,MAAM,eAAe,CAAC,MAAc;IACzC,IAAI,KAAK,MAAM,IAAI,WAAW,OAAO;IACrC,OAAO,KAAK,SAAS,CAAC,GAAG,aAAa;AACxC;AAKO,MAAM,WAAW,CAAC;IACvB,MAAM,QAAQ,QAAQ,KAAK,CAAC;IAC5B,MAAM,UAAU,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC;IAC/B,MAAM,OAAc,EAAE;IAEtB,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,MAAM,EAAE,IAAK;QACrC,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC,IAAI,IAAI;QAEtB,MAAM,SAAS,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC;QAC9B,MAAM,MAAW,CAAC;QAElB,QAAQ,OAAO,CAAC,CAAC,QAAQ;YACvB,GAAG,CAAC,OAAO,IAAI,GAAG,GAAG,MAAM,CAAC,MAAM,EAAE,UAAU;QAChD;QAEA,KAAK,IAAI,CAAC;IACZ;IAEA,OAAO;AACT;AAKO,MAAM,yBAAyB,CAAC;IACrC,MAAM,YAAY,OAAO,IAAI,QAAQ;IACrC,OAAO,CAAC,aAAa,EAAE,UAAU,CAAC,EAAE,QAAQ;AAC9C;AAKO,MAAM,cAAc,CAAC;IAC1B,OAAO;QACL,GAAG,WAAW;QACd,QAAQ,YAAY,QAAQ,GAAG,WAAW;IAC5C;AACF;AAKO,MAAM,kBAAkB,CAAC,UAAe,MAA2B;IACxE,MAAM,WAAW;QACf,WAAW,SAAS,SAAS;QAC7B,UAAU,SAAS,QAAQ;QAC3B,WAAW,SAAS,SAAS;QAC7B,MAAM,SAAS,IAAI;QACnB,KAAK,SAAS,GAAG;QACjB,UAAU,SAAS,QAAQ,IAAI;QAC/B,MAAM,SAAS,IAAI,IAAI;QACvB,UAAU,SAAS,QAAQ;IAC7B;IAEA,IAAI,SAAS,YAAY,QAAQ;QAC/B,OAAO;YACL,IAAI;YACJ,GAAG,QAAQ;QACb;IACF;IAEA,OAAO;AACT", "debugId": null}}, {"offset": {"line": 1238, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/bloomgoo/gold-exchange-platform/frontend/components/common/admin/ebanks/table/bank-table-cells.tsx"], "sourcesContent": ["/**\r\n * BankTableCells - Các cell components cho table\r\n */\r\n\r\nimport { ImageIcon, User } from 'lucide-react';\r\nimport { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';\r\nimport { Badge } from '@/components/ui/badge';\r\nimport { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';\r\nimport { UserHoverCard } from '@/components/common/user/user-hover-card';\r\nimport { DateTimeDisplay } from '@/components/ui/date-time-display';\r\nimport type { BankDto } from '../types';\r\nimport { getBankAvatarUrl, getBankAvatarFallback, getStatusVariant } from '../utils/bank-helpers';\r\n\r\n// Bank name cell với avatar\r\nexport function BankNameCell({ bank }: { bank: BankDto }) {\r\n  return (\r\n    <div className=\"flex items-center gap-2\">\r\n      <div className=\"relative\">\r\n        <Avatar className=\"size-8 shrink-0\">\r\n          <AvatarImage \r\n            src={getBankAvatarUrl(bank.brandName, bank.logoPath)} \r\n            alt={bank.brandName} \r\n          />\r\n          <AvatarFallback>{getBankAvatarFallback(bank.brandName)}</AvatarFallback>\r\n        </Avatar>\r\n        <span\r\n          className={`border-background absolute -end-0.5 -bottom-0.5 size-2.5 rounded-full border-2 ${\r\n            bank.isActive ? 'bg-green-500' : 'bg-gray-500'\r\n          }`}\r\n        >\r\n          <span className=\"sr-only\">{bank.isActive ? 'Active' : 'Inactive'}</span>\r\n        </span>\r\n      </div>\r\n      <div className=\"flex flex-col items-start overflow-hidden\">\r\n        <span className=\"text-sm font-medium truncate w-full\">{bank.brandName}</span>\r\n        <span className=\"text-xs text-muted-foreground truncate w-full\">\r\n          Mã: {bank.code}\r\n        </span>\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n\r\n// Text cell với tooltip\r\nexport function TextCell({ value, maxWidth = 200 }: { value: string; maxWidth?: number }) {\r\n  if (!value) return <span className=\"text-xs text-muted-foreground\">---</span>;\r\n\r\n  return (\r\n    <TooltipProvider>\r\n      <Tooltip>\r\n        <TooltipTrigger asChild>\r\n          <span \r\n            className={`truncate block text-sm`}\r\n            style={{ maxWidth: `${maxWidth}px` }}\r\n          >\r\n            {value}\r\n          </span>\r\n        </TooltipTrigger>\r\n        <TooltipContent>\r\n          <p>{value}</p>\r\n        </TooltipContent>\r\n      </Tooltip>\r\n    </TooltipProvider>\r\n  );\r\n}\r\n\r\n// Image cell với preview\r\nexport function ImageCell({ url, alt }: { url?: string; alt: string }) {\r\n  if (!url) return <span className=\"text-xs text-muted-foreground\">---</span>;\r\n\r\n  return (\r\n    <div className=\"flex items-center gap-2\">\r\n      <ImageIcon className=\"h-3.5 w-3.5 text-muted-foreground\" />\r\n      <TooltipProvider>\r\n        <Tooltip>\r\n          <TooltipTrigger asChild>\r\n            <a \r\n              href={url} \r\n              target=\"_blank\" \r\n              rel=\"noopener noreferrer\" \r\n              className=\"text-blue-500 hover:underline truncate max-w-[120px] inline-block text-sm\"\r\n            >\r\n              Xem {alt}\r\n            </a>\r\n          </TooltipTrigger>\r\n          <TooltipContent side=\"bottom\">\r\n            <div className=\"flex flex-col items-center gap-2\">\r\n              <img \r\n                src={url} \r\n                alt={alt} \r\n                className=\"max-w-[200px] max-h-[100px] object-contain\" \r\n              />\r\n              <p className=\"text-xs\">{url}</p>\r\n            </div>\r\n          </TooltipContent>\r\n        </Tooltip>\r\n      </TooltipProvider>\r\n    </div>\r\n  );\r\n}\r\n\r\n// Status cell\r\nexport function StatusCell({ isActive }: { isActive: boolean }) {\r\n  return (\r\n    <Badge variant={getStatusVariant(isActive)} className=\"whitespace-nowrap\">\r\n      {isActive ? 'Kích hoạt' : 'Vô hiệu hóa'}\r\n    </Badge>\r\n  );\r\n}\r\n\r\n// Date cell\r\nexport function DateCell({ date }: { date?: string }) {\r\n  if (!date) return <span className=\"text-xs text-muted-foreground\">---</span>;\r\n\r\n  return (\r\n    <DateTimeDisplay\r\n      date={date}\r\n      className=\"text-sm\"\r\n      timeClassName=\"text-xs text-muted-foreground\"\r\n    />\r\n  );\r\n}\r\n\r\n// User cell\r\nexport function UserCell({ \r\n  user, \r\n  userId, \r\n  fallbackText = \"---\" \r\n}: { \r\n  user?: BankDto['creator']; \r\n  userId?: string; \r\n  fallbackText?: string;\r\n}) {\r\n  // Nếu có thông tin user object\r\n  if (user && typeof user === 'object') {\r\n    return (\r\n      <UserHoverCard user={user} showAvatar={true} size=\"sm\">\r\n        <div className=\"max-w-[120px] overflow-hidden\">\r\n          <div className=\"text-sm truncate\">\r\n            {user.fullName || user.username || 'User'}\r\n          </div>\r\n          <div className=\"text-xs text-muted-foreground truncate\">\r\n            {user.email || ''}\r\n          </div>\r\n        </div>\r\n      </UserHoverCard>\r\n    );\r\n  }\r\n\r\n  // Nếu chỉ có ID\r\n  if (userId && typeof userId === 'string') {\r\n    return (\r\n      <UserHoverCard userId={userId} showAvatar={true} size=\"sm\">\r\n        <div className=\"flex items-center gap-1\">\r\n          <User className=\"h-4 w-4 text-blue-500\" />\r\n          <span className=\"text-sm text-muted-foreground\">\r\n            {userId.substring(0, 8)}...\r\n          </span>\r\n        </div>\r\n      </UserHoverCard>\r\n    );\r\n  }\r\n\r\n  // Fallback\r\n  return <span className=\"text-xs text-muted-foreground\">{fallbackText}</span>;\r\n}\r\n"], "names": [], "mappings": "AAAA;;CAEC;;;;;;;;;AAED;AAAA;AACA;AACA;AACA;AACA;AACA;AAEA;;;;;;;;;AAGO,SAAS,aAAa,EAAE,IAAI,EAAqB;IACtD,qBACE,uVAAC;QAAI,WAAU;;0BACb,uVAAC;gBAAI,WAAU;;kCACb,uVAAC,2HAAA,CAAA,SAAM;wBAAC,WAAU;;0CAChB,uVAAC,2HAAA,CAAA,cAAW;gCACV,KAAK,CAAA,GAAA,mKAAA,CAAA,mBAAgB,AAAD,EAAE,KAAK,SAAS,EAAE,KAAK,QAAQ;gCACnD,KAAK,KAAK,SAAS;;;;;;0CAErB,uVAAC,2HAAA,CAAA,iBAAc;0CAAE,CAAA,GAAA,mKAAA,CAAA,wBAAqB,AAAD,EAAE,KAAK,SAAS;;;;;;;;;;;;kCAEvD,uVAAC;wBACC,WAAW,CAAC,+EAA+E,EACzF,KAAK,QAAQ,GAAG,iBAAiB,eACjC;kCAEF,cAAA,uVAAC;4BAAK,WAAU;sCAAW,KAAK,QAAQ,GAAG,WAAW;;;;;;;;;;;;;;;;;0BAG1D,uVAAC;gBAAI,WAAU;;kCACb,uVAAC;wBAAK,WAAU;kCAAuC,KAAK,SAAS;;;;;;kCACrE,uVAAC;wBAAK,WAAU;;4BAAgD;4BACzD,KAAK,IAAI;;;;;;;;;;;;;;;;;;;AAKxB;AAGO,SAAS,SAAS,EAAE,KAAK,EAAE,WAAW,GAAG,EAAwC;IACtF,IAAI,CAAC,OAAO,qBAAO,uVAAC;QAAK,WAAU;kBAAgC;;;;;;IAEnE,qBACE,uVAAC,4HAAA,CAAA,kBAAe;kBACd,cAAA,uVAAC,4HAAA,CAAA,UAAO;;8BACN,uVAAC,4HAAA,CAAA,iBAAc;oBAAC,OAAO;8BACrB,cAAA,uVAAC;wBACC,WAAW,CAAC,sBAAsB,CAAC;wBACnC,OAAO;4BAAE,UAAU,GAAG,SAAS,EAAE,CAAC;wBAAC;kCAElC;;;;;;;;;;;8BAGL,uVAAC,4HAAA,CAAA,iBAAc;8BACb,cAAA,uVAAC;kCAAG;;;;;;;;;;;;;;;;;;;;;;AAKd;AAGO,SAAS,UAAU,EAAE,GAAG,EAAE,GAAG,EAAiC;IACnE,IAAI,CAAC,KAAK,qBAAO,uVAAC;QAAK,WAAU;kBAAgC;;;;;;IAEjE,qBACE,uVAAC;QAAI,WAAU;;0BACb,uVAAC,4RAAA,CAAA,YAAS;gBAAC,WAAU;;;;;;0BACrB,uVAAC,4HAAA,CAAA,kBAAe;0BACd,cAAA,uVAAC,4HAAA,CAAA,UAAO;;sCACN,uVAAC,4HAAA,CAAA,iBAAc;4BAAC,OAAO;sCACrB,cAAA,uVAAC;gCACC,MAAM;gCACN,QAAO;gCACP,KAAI;gCACJ,WAAU;;oCACX;oCACM;;;;;;;;;;;;sCAGT,uVAAC,4HAAA,CAAA,iBAAc;4BAAC,MAAK;sCACnB,cAAA,uVAAC;gCAAI,WAAU;;kDACb,uVAAC;wCACC,KAAK;wCACL,KAAK;wCACL,WAAU;;;;;;kDAEZ,uVAAC;wCAAE,WAAU;kDAAW;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOtC;AAGO,SAAS,WAAW,EAAE,QAAQ,EAAyB;IAC5D,qBACE,uVAAC,0HAAA,CAAA,QAAK;QAAC,SAAS,CAAA,GAAA,mKAAA,CAAA,mBAAgB,AAAD,EAAE;QAAW,WAAU;kBACnD,WAAW,cAAc;;;;;;AAGhC;AAGO,SAAS,SAAS,EAAE,IAAI,EAAqB;IAClD,IAAI,CAAC,MAAM,qBAAO,uVAAC;QAAK,WAAU;kBAAgC;;;;;;IAElE,qBACE,uVAAC,4IAAA,CAAA,kBAAe;QACd,MAAM;QACN,WAAU;QACV,eAAc;;;;;;AAGpB;AAGO,SAAS,SAAS,EACvB,IAAI,EACJ,MAAM,EACN,eAAe,KAAK,EAKrB;IACC,+BAA+B;IAC/B,IAAI,QAAQ,OAAO,SAAS,UAAU;QACpC,qBACE,uVAAC,sJAAA,CAAA,gBAAa;YAAC,MAAM;YAAM,YAAY;YAAM,MAAK;sBAChD,cAAA,uVAAC;gBAAI,WAAU;;kCACb,uVAAC;wBAAI,WAAU;kCACZ,KAAK,QAAQ,IAAI,KAAK,QAAQ,IAAI;;;;;;kCAErC,uVAAC;wBAAI,WAAU;kCACZ,KAAK,KAAK,IAAI;;;;;;;;;;;;;;;;;IAKzB;IAEA,gBAAgB;IAChB,IAAI,UAAU,OAAO,WAAW,UAAU;QACxC,qBACE,uVAAC,sJAAA,CAAA,gBAAa;YAAC,QAAQ;YAAQ,YAAY;YAAM,MAAK;sBACpD,cAAA,uVAAC;gBAAI,WAAU;;kCACb,uVAAC,sRAAA,CAAA,OAAI;wBAAC,WAAU;;;;;;kCAChB,uVAAC;wBAAK,WAAU;;4BACb,OAAO,SAAS,CAAC,GAAG;4BAAG;;;;;;;;;;;;;;;;;;IAKlC;IAEA,WAAW;IACX,qBAAO,uVAAC;QAAK,WAAU;kBAAiC;;;;;;AAC1D", "debugId": null}}, {"offset": {"line": 1624, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/bloomgoo/gold-exchange-platform/frontend/components/common/admin/ebanks/table/bank-table-actions.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const BankTableActions = registerClientReference(\n    function() { throw new Error(\"Attempted to call BankTableActions() from the server but BankTableActions is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/common/admin/ebanks/table/bank-table-actions.tsx <module evaluation>\",\n    \"BankTableActions\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,mBAAmB,CAAA,GAAA,8VAAA,CAAA,0BAAuB,AAAD,EAClD;IAAa,MAAM,IAAI,MAAM;AAAgP,GAC7Q,6FACA", "debugId": null}}, {"offset": {"line": 1638, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/bloomgoo/gold-exchange-platform/frontend/components/common/admin/ebanks/table/bank-table-actions.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const BankTableActions = registerClientReference(\n    function() { throw new Error(\"Attempted to call BankTableActions() from the server but BankTableActions is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/common/admin/ebanks/table/bank-table-actions.tsx\",\n    \"BankTableActions\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,mBAAmB,CAAA,GAAA,8VAAA,CAAA,0BAAuB,AAAD,EAClD;IAAa,MAAM,IAAI,MAAM;AAAgP,GAC7Q,yEACA", "debugId": null}}, {"offset": {"line": 1652, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 1662, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/bloomgoo/gold-exchange-platform/frontend/components/common/admin/ebanks/table/bank-table-columns.tsx"], "sourcesContent": ["/**\r\n * BankTableColumns - Định nghĩa columns cho table ngân hàng\r\n */\r\n\r\nimport { type ColumnDef } from '@tanstack/react-table';\r\nimport { ArrowUpDown } from 'lucide-react';\r\nimport { Button } from '@/components/ui/button';\r\nimport type { BankDto } from '../types';\r\nimport { \r\n  BankNameCell, \r\n  TextCell, \r\n  ImageCell, \r\n  StatusCell, \r\n  DateCell, \r\n  UserCell \r\n} from './bank-table-cells';\r\nimport { BankTableActions } from './bank-table-actions';\r\n\r\ninterface ColumnsProps {\r\n  onViewDetail: (bank: BankDto) => void;\r\n  onDelete: (bank: BankDto) => void;\r\n  onEdit: (bank: BankDto) => void;\r\n  onToggleStatus: (bank: BankDto) => void;\r\n  onDuplicate?: (bank: BankDto) => void;\r\n}\r\n\r\nexport function getBankTableColumns({\r\n  onViewDetail,\r\n  onDelete,\r\n  onEdit,\r\n  onToggleStatus,\r\n  onDuplicate,\r\n}: ColumnsProps): ColumnDef<BankDto>[] {\r\n  return [\r\n    // Tên ngân hàng\r\n    {\r\n      accessorKey: 'brandName',\r\n      header: ({ column }) => (\r\n        <Button\r\n          variant=\"ghost\"\r\n          size=\"sm\"\r\n          onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}\r\n          className=\"flex items-center gap-1 px-0 h-6 py-0\"\r\n          data-column-id=\"brandName\"\r\n        >\r\n          <span className=\"text-xs\">Tên ngân hàng</span>\r\n          <ArrowUpDown className=\"ml-1 h-3 w-3\" />\r\n        </Button>\r\n      ),\r\n      meta: {\r\n        header: 'Tên ngân hàng',\r\n        isSticky: true,\r\n        position: 'left',\r\n      },\r\n      cell: ({ row }) => <BankNameCell bank={row.original} />,\r\n      enableSorting: true,\r\n      size: 200,\r\n    },\r\n\r\n    // Tên đầy đủ\r\n    {\r\n      accessorKey: 'fullName',\r\n      header: ({ column }) => (\r\n        <Button\r\n          variant=\"ghost\"\r\n          size=\"sm\"\r\n          onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}\r\n          className=\"flex items-center gap-1 px-0 h-6 py-0\"\r\n          data-column-id=\"fullName\"\r\n        >\r\n          <span className=\"text-xs\">Tên đầy đủ</span>\r\n          <ArrowUpDown className=\"ml-1 h-3 w-3\" />\r\n        </Button>\r\n      ),\r\n      meta: {\r\n        header: 'Tên đầy đủ',\r\n        isSticky: false,\r\n        position: 'left',\r\n      },\r\n      cell: ({ row }) => <TextCell value={row.getValue('fullName')} maxWidth={200} />,\r\n      enableSorting: true,\r\n      size: 180,\r\n    },\r\n\r\n    // Tên ngắn gọn\r\n    {\r\n      accessorKey: 'shortName',\r\n      header: ({ column }) => (\r\n        <Button\r\n          variant=\"ghost\"\r\n          size=\"sm\"\r\n          onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}\r\n          className=\"flex items-center gap-1 px-0 h-6 py-0\"\r\n          data-column-id=\"shortName\"\r\n        >\r\n          <span className=\"text-xs\">Tên ngắn gọn</span>\r\n          <ArrowUpDown className=\"ml-1 h-3 w-3\" />\r\n        </Button>\r\n      ),\r\n      meta: {\r\n        header: 'Tên ngắn gọn',\r\n        isSticky: false,\r\n        position: 'left',\r\n      },\r\n      cell: ({ row }) => <TextCell value={row.getValue('shortName')} maxWidth={150} />,\r\n      enableSorting: true,\r\n      size: 150,\r\n    },\r\n\r\n    // Bin\r\n    {\r\n      accessorKey: 'bin',\r\n      header: ({ column }) => (\r\n        <Button\r\n          variant=\"ghost\"\r\n          size=\"sm\"\r\n          onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}\r\n          className=\"flex items-center gap-1 px-0 h-6 py-0\"\r\n          data-column-id=\"bin\"\r\n        >\r\n          <span className=\"text-xs\">Bin</span>\r\n          <ArrowUpDown className=\"ml-1 h-3 w-3\" />\r\n        </Button>\r\n      ),\r\n      meta: {\r\n        header: 'Bin',\r\n        isSticky: false,\r\n        position: 'left',\r\n      },\r\n      cell: ({ row }) => {\r\n        const bin = row.getValue('bin') as string;\r\n        return bin ? (\r\n          <span className=\"text-sm\">{bin}</span>\r\n        ) : (\r\n          <span className=\"text-xs text-muted-foreground\">---</span>\r\n        );\r\n      },\r\n      enableSorting: true,\r\n      size: 100,\r\n    },\r\n\r\n    // Logo\r\n    {\r\n      accessorKey: 'logoPath',\r\n      header: () => (\r\n        <Button\r\n          variant=\"ghost\"\r\n          size=\"sm\"\r\n          className=\"flex items-center gap-1 px-0 h-6 py-0\"\r\n          data-column-id=\"logoPath\"\r\n        >\r\n          <span className=\"text-xs\">Logo</span>\r\n        </Button>\r\n      ),\r\n      meta: {\r\n        header: 'Logo',\r\n        isSticky: false,\r\n        position: 'left',\r\n      },\r\n      cell: ({ row }) => (\r\n        <ImageCell url={row.getValue('logoPath')} alt=\"logo\" />\r\n      ),\r\n      enableSorting: false,\r\n      size: 120,\r\n    },\r\n\r\n    // Icon\r\n    {\r\n      accessorKey: 'icon',\r\n      header: () => (\r\n        <Button\r\n          variant=\"ghost\"\r\n          size=\"sm\"\r\n          className=\"flex items-center gap-1 px-0 h-6 py-0\"\r\n          data-column-id=\"icon\"\r\n        >\r\n          <span className=\"text-xs\">Icon</span>\r\n        </Button>\r\n      ),\r\n      meta: {\r\n        header: 'Icon',\r\n        isSticky: false,\r\n        position: 'left',\r\n      },\r\n      cell: ({ row }) => (\r\n        <ImageCell url={row.getValue('icon')} alt=\"icon\" />\r\n      ),\r\n      enableSorting: false,\r\n      size: 120,\r\n    },\r\n\r\n    // Trạng thái\r\n    {\r\n      accessorKey: 'isActive',\r\n      header: ({ column }) => (\r\n        <Button\r\n          variant=\"ghost\"\r\n          size=\"sm\"\r\n          onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}\r\n          className=\"flex items-center gap-1 px-0 h-6 py-0\"\r\n          data-column-id=\"isActive\"\r\n        >\r\n          <span className=\"text-xs\">Trạng thái</span>\r\n          <ArrowUpDown className=\"ml-1 h-3 w-3\" />\r\n        </Button>\r\n      ),\r\n      meta: {\r\n        header: 'Trạng thái',\r\n        isSticky: false,\r\n        position: 'right',\r\n      },\r\n      cell: ({ row }) => <StatusCell isActive={row.getValue('isActive')} />,\r\n      enableSorting: true,\r\n      size: 120,\r\n    },\r\n\r\n    // Ngày tạo\r\n    {\r\n      accessorKey: 'createdAt',\r\n      header: ({ column }) => (\r\n        <Button\r\n          variant=\"ghost\"\r\n          size=\"sm\"\r\n          onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}\r\n          className=\"flex items-center gap-1 px-0 h-6 py-0\"\r\n          data-column-id=\"createdAt\"\r\n        >\r\n          <span className=\"text-xs\">Ngày tạo</span>\r\n          <ArrowUpDown className=\"ml-1 h-3 w-3\" />\r\n        </Button>\r\n      ),\r\n      meta: {\r\n        header: 'Ngày tạo',\r\n        isSticky: false,\r\n        position: 'right',\r\n      },\r\n      cell: ({ row }) => <DateCell date={row.getValue('createdAt')} />,\r\n      enableSorting: true,\r\n      size: 150,\r\n    },\r\n\r\n    // Cập nhật lần cuối\r\n    {\r\n      accessorKey: 'updatedAt',\r\n      header: ({ column }) => (\r\n        <Button\r\n          variant=\"ghost\"\r\n          size=\"sm\"\r\n          onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}\r\n          className=\"flex items-center gap-1 px-0 h-6 py-0\"\r\n          data-column-id=\"updatedAt\"\r\n        >\r\n          <span className=\"text-xs\">Cập nhật lần cuối</span>\r\n          <ArrowUpDown className=\"ml-1 h-3 w-3\" />\r\n        </Button>\r\n      ),\r\n      meta: {\r\n        header: 'Cập nhật lần cuối',\r\n        isSticky: false,\r\n        position: 'right',\r\n      },\r\n      cell: ({ row }) => <DateCell date={row.getValue('updatedAt')} />,\r\n      enableSorting: true,\r\n      size: 150,\r\n    },\r\n\r\n    // Ngày xóa\r\n    {\r\n      accessorKey: 'deletedAt',\r\n      header: ({ column }) => (\r\n        <Button\r\n          variant=\"ghost\"\r\n          size=\"sm\"\r\n          onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}\r\n          className=\"flex items-center gap-1 px-0 h-6 py-0\"\r\n          data-column-id=\"deletedAt\"\r\n        >\r\n          <span className=\"text-xs\">Ngày xóa</span>\r\n          <ArrowUpDown className=\"ml-1 h-3 w-3\" />\r\n        </Button>\r\n      ),\r\n      meta: {\r\n        header: 'Ngày xóa',\r\n        isSticky: false,\r\n        position: 'right',\r\n      },\r\n      cell: ({ row }) => <DateCell date={row.getValue('deletedAt')} />,\r\n      enableSorting: true,\r\n      size: 150,\r\n    },\r\n\r\n    // Người tạo\r\n    {\r\n      accessorKey: 'createdBy',\r\n      header: ({ column }) => (\r\n        <Button\r\n          variant=\"ghost\"\r\n          size=\"sm\"\r\n          onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}\r\n          className=\"flex items-center gap-1 px-0 h-6 py-0\"\r\n          data-column-id=\"createdBy\"\r\n        >\r\n          <span className=\"text-xs\">Người tạo</span>\r\n          <ArrowUpDown className=\"ml-1 h-3 w-3\" />\r\n        </Button>\r\n      ),\r\n      meta: {\r\n        header: 'Người tạo',\r\n        isSticky: false,\r\n        position: 'right',\r\n      },\r\n      cell: ({ row }) => {\r\n        const bank = row.original;\r\n        return <UserCell user={bank.creator} userId={bank.createdBy} />;\r\n      },\r\n      enableSorting: true,\r\n      enableHiding: true,\r\n      size: 180,\r\n    },\r\n\r\n    // Người cập nhật\r\n    {\r\n      accessorKey: 'updatedBy',\r\n      header: ({ column }) => (\r\n        <Button\r\n          variant=\"ghost\"\r\n          size=\"sm\"\r\n          onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}\r\n          className=\"flex items-center gap-1 px-0 h-6 py-0\"\r\n          data-column-id=\"updatedBy\"\r\n        >\r\n          <span className=\"text-xs\">Người cập nhật</span>\r\n          <ArrowUpDown className=\"ml-1 h-3 w-3\" />\r\n        </Button>\r\n      ),\r\n      meta: {\r\n        header: 'Người cập nhật',\r\n        isSticky: false,\r\n        position: 'right',\r\n      },\r\n      cell: ({ row }) => {\r\n        const bank = row.original;\r\n        return <UserCell user={bank.updater} userId={bank.updatedBy} />;\r\n      },\r\n      enableSorting: true,\r\n      enableHiding: true,\r\n      size: 180,\r\n    },\r\n\r\n    // Người xóa\r\n    {\r\n      accessorKey: 'deletedBy',\r\n      header: ({ column }) => (\r\n        <Button\r\n          variant=\"ghost\"\r\n          size=\"sm\"\r\n          onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}\r\n          className=\"flex items-center gap-1 px-0 h-6 py-0\"\r\n          data-column-id=\"deletedBy\"\r\n        >\r\n          <span className=\"text-xs\">Người xóa</span>\r\n          <ArrowUpDown className=\"ml-1 h-3 w-3\" />\r\n        </Button>\r\n      ),\r\n      meta: {\r\n        header: 'Người xóa',\r\n        isSticky: false,\r\n        position: 'right',\r\n      },\r\n      cell: ({ row }) => {\r\n        const bank = row.original;\r\n        return <UserCell user={bank.deleter} userId={bank.deletedBy} />;\r\n      },\r\n      enableSorting: true,\r\n      enableHiding: true,\r\n      size: 180,\r\n    },\r\n\r\n    // Actions\r\n    {\r\n      id: 'actions',\r\n      size: 50,\r\n      enableHiding: false,\r\n      enableSorting: false,\r\n      header: () => <div className=\"text-right\"></div>,\r\n      cell: ({ row }) => (\r\n        <BankTableActions\r\n          row={row}\r\n          onViewDetail={onViewDetail}\r\n          onDelete={onDelete}\r\n          onEdit={onEdit}\r\n          onToggleStatus={onToggleStatus}\r\n          onDuplicate={onDuplicate}\r\n        />\r\n      ),\r\n      meta: {\r\n        header: 'Thao tác',\r\n        isSticky: true,\r\n        position: 'right',\r\n      },\r\n    },\r\n  ];\r\n}\r\n"], "names": [], "mappings": "AAAA;;CAEC;;;;AAGD;AACA;AAEA;AAQA;;;;;;AAUO,SAAS,oBAAoB,EAClC,YAAY,EACZ,QAAQ,EACR,MAAM,EACN,cAAc,EACd,WAAW,EACE;IACb,OAAO;QACL,gBAAgB;QAChB;YACE,aAAa;YACb,QAAQ,CAAC,EAAE,MAAM,EAAE,iBACjB,uVAAC,2HAAA,CAAA,SAAM;oBACL,SAAQ;oBACR,MAAK;oBACL,SAAS,IAAM,OAAO,aAAa,CAAC,OAAO,WAAW,OAAO;oBAC7D,WAAU;oBACV,kBAAe;;sCAEf,uVAAC;4BAAK,WAAU;sCAAU;;;;;;sCAC1B,uVAAC,4SAAA,CAAA,cAAW;4BAAC,WAAU;;;;;;;;;;;;YAG3B,MAAM;gBACJ,QAAQ;gBACR,UAAU;gBACV,UAAU;YACZ;YACA,MAAM,CAAC,EAAE,GAAG,EAAE,iBAAK,uVAAC,2KAAA,CAAA,eAAY;oBAAC,MAAM,IAAI,QAAQ;;;;;;YACnD,eAAe;YACf,MAAM;QACR;QAEA,aAAa;QACb;YACE,aAAa;YACb,QAAQ,CAAC,EAAE,MAAM,EAAE,iBACjB,uVAAC,2HAAA,CAAA,SAAM;oBACL,SAAQ;oBACR,MAAK;oBACL,SAAS,IAAM,OAAO,aAAa,CAAC,OAAO,WAAW,OAAO;oBAC7D,WAAU;oBACV,kBAAe;;sCAEf,uVAAC;4BAAK,WAAU;sCAAU;;;;;;sCAC1B,uVAAC,4SAAA,CAAA,cAAW;4BAAC,WAAU;;;;;;;;;;;;YAG3B,MAAM;gBACJ,QAAQ;gBACR,UAAU;gBACV,UAAU;YACZ;YACA,MAAM,CAAC,EAAE,GAAG,EAAE,iBAAK,uVAAC,2KAAA,CAAA,WAAQ;oBAAC,OAAO,IAAI,QAAQ,CAAC;oBAAa,UAAU;;;;;;YACxE,eAAe;YACf,MAAM;QACR;QAEA,eAAe;QACf;YACE,aAAa;YACb,QAAQ,CAAC,EAAE,MAAM,EAAE,iBACjB,uVAAC,2HAAA,CAAA,SAAM;oBACL,SAAQ;oBACR,MAAK;oBACL,SAAS,IAAM,OAAO,aAAa,CAAC,OAAO,WAAW,OAAO;oBAC7D,WAAU;oBACV,kBAAe;;sCAEf,uVAAC;4BAAK,WAAU;sCAAU;;;;;;sCAC1B,uVAAC,4SAAA,CAAA,cAAW;4BAAC,WAAU;;;;;;;;;;;;YAG3B,MAAM;gBACJ,QAAQ;gBACR,UAAU;gBACV,UAAU;YACZ;YACA,MAAM,CAAC,EAAE,GAAG,EAAE,iBAAK,uVAAC,2KAAA,CAAA,WAAQ;oBAAC,OAAO,IAAI,QAAQ,CAAC;oBAAc,UAAU;;;;;;YACzE,eAAe;YACf,MAAM;QACR;QAEA,MAAM;QACN;YACE,aAAa;YACb,QAAQ,CAAC,EAAE,MAAM,EAAE,iBACjB,uVAAC,2HAAA,CAAA,SAAM;oBACL,SAAQ;oBACR,MAAK;oBACL,SAAS,IAAM,OAAO,aAAa,CAAC,OAAO,WAAW,OAAO;oBAC7D,WAAU;oBACV,kBAAe;;sCAEf,uVAAC;4BAAK,WAAU;sCAAU;;;;;;sCAC1B,uVAAC,4SAAA,CAAA,cAAW;4BAAC,WAAU;;;;;;;;;;;;YAG3B,MAAM;gBACJ,QAAQ;gBACR,UAAU;gBACV,UAAU;YACZ;YACA,MAAM,CAAC,EAAE,GAAG,EAAE;gBACZ,MAAM,MAAM,IAAI,QAAQ,CAAC;gBACzB,OAAO,oBACL,uVAAC;oBAAK,WAAU;8BAAW;;;;;yCAE3B,uVAAC;oBAAK,WAAU;8BAAgC;;;;;;YAEpD;YACA,eAAe;YACf,MAAM;QACR;QAEA,OAAO;QACP;YACE,aAAa;YACb,QAAQ,kBACN,uVAAC,2HAAA,CAAA,SAAM;oBACL,SAAQ;oBACR,MAAK;oBACL,WAAU;oBACV,kBAAe;8BAEf,cAAA,uVAAC;wBAAK,WAAU;kCAAU;;;;;;;;;;;YAG9B,MAAM;gBACJ,QAAQ;gBACR,UAAU;gBACV,UAAU;YACZ;YACA,MAAM,CAAC,EAAE,GAAG,EAAE,iBACZ,uVAAC,2KAAA,CAAA,YAAS;oBAAC,KAAK,IAAI,QAAQ,CAAC;oBAAa,KAAI;;;;;;YAEhD,eAAe;YACf,MAAM;QACR;QAEA,OAAO;QACP;YACE,aAAa;YACb,QAAQ,kBACN,uVAAC,2HAAA,CAAA,SAAM;oBACL,SAAQ;oBACR,MAAK;oBACL,WAAU;oBACV,kBAAe;8BAEf,cAAA,uVAAC;wBAAK,WAAU;kCAAU;;;;;;;;;;;YAG9B,MAAM;gBACJ,QAAQ;gBACR,UAAU;gBACV,UAAU;YACZ;YACA,MAAM,CAAC,EAAE,GAAG,EAAE,iBACZ,uVAAC,2KAAA,CAAA,YAAS;oBAAC,KAAK,IAAI,QAAQ,CAAC;oBAAS,KAAI;;;;;;YAE5C,eAAe;YACf,MAAM;QACR;QAEA,aAAa;QACb;YACE,aAAa;YACb,QAAQ,CAAC,EAAE,MAAM,EAAE,iBACjB,uVAAC,2HAAA,CAAA,SAAM;oBACL,SAAQ;oBACR,MAAK;oBACL,SAAS,IAAM,OAAO,aAAa,CAAC,OAAO,WAAW,OAAO;oBAC7D,WAAU;oBACV,kBAAe;;sCAEf,uVAAC;4BAAK,WAAU;sCAAU;;;;;;sCAC1B,uVAAC,4SAAA,CAAA,cAAW;4BAAC,WAAU;;;;;;;;;;;;YAG3B,MAAM;gBACJ,QAAQ;gBACR,UAAU;gBACV,UAAU;YACZ;YACA,MAAM,CAAC,EAAE,GAAG,EAAE,iBAAK,uVAAC,2KAAA,CAAA,aAAU;oBAAC,UAAU,IAAI,QAAQ,CAAC;;;;;;YACtD,eAAe;YACf,MAAM;QACR;QAEA,WAAW;QACX;YACE,aAAa;YACb,QAAQ,CAAC,EAAE,MAAM,EAAE,iBACjB,uVAAC,2HAAA,CAAA,SAAM;oBACL,SAAQ;oBACR,MAAK;oBACL,SAAS,IAAM,OAAO,aAAa,CAAC,OAAO,WAAW,OAAO;oBAC7D,WAAU;oBACV,kBAAe;;sCAEf,uVAAC;4BAAK,WAAU;sCAAU;;;;;;sCAC1B,uVAAC,4SAAA,CAAA,cAAW;4BAAC,WAAU;;;;;;;;;;;;YAG3B,MAAM;gBACJ,QAAQ;gBACR,UAAU;gBACV,UAAU;YACZ;YACA,MAAM,CAAC,EAAE,GAAG,EAAE,iBAAK,uVAAC,2KAAA,CAAA,WAAQ;oBAAC,MAAM,IAAI,QAAQ,CAAC;;;;;;YAChD,eAAe;YACf,MAAM;QACR;QAEA,oBAAoB;QACpB;YACE,aAAa;YACb,QAAQ,CAAC,EAAE,MAAM,EAAE,iBACjB,uVAAC,2HAAA,CAAA,SAAM;oBACL,SAAQ;oBACR,MAAK;oBACL,SAAS,IAAM,OAAO,aAAa,CAAC,OAAO,WAAW,OAAO;oBAC7D,WAAU;oBACV,kBAAe;;sCAEf,uVAAC;4BAAK,WAAU;sCAAU;;;;;;sCAC1B,uVAAC,4SAAA,CAAA,cAAW;4BAAC,WAAU;;;;;;;;;;;;YAG3B,MAAM;gBACJ,QAAQ;gBACR,UAAU;gBACV,UAAU;YACZ;YACA,MAAM,CAAC,EAAE,GAAG,EAAE,iBAAK,uVAAC,2KAAA,CAAA,WAAQ;oBAAC,MAAM,IAAI,QAAQ,CAAC;;;;;;YAChD,eAAe;YACf,MAAM;QACR;QAEA,WAAW;QACX;YACE,aAAa;YACb,QAAQ,CAAC,EAAE,MAAM,EAAE,iBACjB,uVAAC,2HAAA,CAAA,SAAM;oBACL,SAAQ;oBACR,MAAK;oBACL,SAAS,IAAM,OAAO,aAAa,CAAC,OAAO,WAAW,OAAO;oBAC7D,WAAU;oBACV,kBAAe;;sCAEf,uVAAC;4BAAK,WAAU;sCAAU;;;;;;sCAC1B,uVAAC,4SAAA,CAAA,cAAW;4BAAC,WAAU;;;;;;;;;;;;YAG3B,MAAM;gBACJ,QAAQ;gBACR,UAAU;gBACV,UAAU;YACZ;YACA,MAAM,CAAC,EAAE,GAAG,EAAE,iBAAK,uVAAC,2KAAA,CAAA,WAAQ;oBAAC,MAAM,IAAI,QAAQ,CAAC;;;;;;YAChD,eAAe;YACf,MAAM;QACR;QAEA,YAAY;QACZ;YACE,aAAa;YACb,QAAQ,CAAC,EAAE,MAAM,EAAE,iBACjB,uVAAC,2HAAA,CAAA,SAAM;oBACL,SAAQ;oBACR,MAAK;oBACL,SAAS,IAAM,OAAO,aAAa,CAAC,OAAO,WAAW,OAAO;oBAC7D,WAAU;oBACV,kBAAe;;sCAEf,uVAAC;4BAAK,WAAU;sCAAU;;;;;;sCAC1B,uVAAC,4SAAA,CAAA,cAAW;4BAAC,WAAU;;;;;;;;;;;;YAG3B,MAAM;gBACJ,QAAQ;gBACR,UAAU;gBACV,UAAU;YACZ;YACA,MAAM,CAAC,EAAE,GAAG,EAAE;gBACZ,MAAM,OAAO,IAAI,QAAQ;gBACzB,qBAAO,uVAAC,2KAAA,CAAA,WAAQ;oBAAC,MAAM,KAAK,OAAO;oBAAE,QAAQ,KAAK,SAAS;;;;;;YAC7D;YACA,eAAe;YACf,cAAc;YACd,MAAM;QACR;QAEA,iBAAiB;QACjB;YACE,aAAa;YACb,QAAQ,CAAC,EAAE,MAAM,EAAE,iBACjB,uVAAC,2HAAA,CAAA,SAAM;oBACL,SAAQ;oBACR,MAAK;oBACL,SAAS,IAAM,OAAO,aAAa,CAAC,OAAO,WAAW,OAAO;oBAC7D,WAAU;oBACV,kBAAe;;sCAEf,uVAAC;4BAAK,WAAU;sCAAU;;;;;;sCAC1B,uVAAC,4SAAA,CAAA,cAAW;4BAAC,WAAU;;;;;;;;;;;;YAG3B,MAAM;gBACJ,QAAQ;gBACR,UAAU;gBACV,UAAU;YACZ;YACA,MAAM,CAAC,EAAE,GAAG,EAAE;gBACZ,MAAM,OAAO,IAAI,QAAQ;gBACzB,qBAAO,uVAAC,2KAAA,CAAA,WAAQ;oBAAC,MAAM,KAAK,OAAO;oBAAE,QAAQ,KAAK,SAAS;;;;;;YAC7D;YACA,eAAe;YACf,cAAc;YACd,MAAM;QACR;QAEA,YAAY;QACZ;YACE,aAAa;YACb,QAAQ,CAAC,EAAE,MAAM,EAAE,iBACjB,uVAAC,2HAAA,CAAA,SAAM;oBACL,SAAQ;oBACR,MAAK;oBACL,SAAS,IAAM,OAAO,aAAa,CAAC,OAAO,WAAW,OAAO;oBAC7D,WAAU;oBACV,kBAAe;;sCAEf,uVAAC;4BAAK,WAAU;sCAAU;;;;;;sCAC1B,uVAAC,4SAAA,CAAA,cAAW;4BAAC,WAAU;;;;;;;;;;;;YAG3B,MAAM;gBACJ,QAAQ;gBACR,UAAU;gBACV,UAAU;YACZ;YACA,MAAM,CAAC,EAAE,GAAG,EAAE;gBACZ,MAAM,OAAO,IAAI,QAAQ;gBACzB,qBAAO,uVAAC,2KAAA,CAAA,WAAQ;oBAAC,MAAM,KAAK,OAAO;oBAAE,QAAQ,KAAK,SAAS;;;;;;YAC7D;YACA,eAAe;YACf,cAAc;YACd,MAAM;QACR;QAEA,UAAU;QACV;YACE,IAAI;YACJ,MAAM;YACN,cAAc;YACd,eAAe;YACf,QAAQ,kBAAM,uVAAC;oBAAI,WAAU;;;;;;YAC7B,MAAM,CAAC,EAAE,GAAG,EAAE,iBACZ,uVAAC,6KAAA,CAAA,mBAAgB;oBACf,KAAK;oBACL,cAAc;oBACd,UAAU;oBACV,QAAQ;oBACR,gBAAgB;oBAChB,aAAa;;;;;;YAGjB,MAAM;gBACJ,QAAQ;gBACR,UAAU;gBACV,UAAU;YACZ;QACF;KACD;AACH", "debugId": null}}, {"offset": {"line": 2326, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/bloomgoo/gold-exchange-platform/frontend/components/common/admin/ebanks/hooks/use-bank-table.ts/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const useBankTable = registerClientReference(\n    function() { throw new Error(\"Attempted to call useBankTable() from the server but useBankTable is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/common/admin/ebanks/hooks/use-bank-table.ts <module evaluation>\",\n    \"useBankTable\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,eAAe,CAAA,GAAA,8VAAA,CAAA,0BAAuB,AAAD,EAC9C;IAAa,MAAM,IAAI,MAAM;AAAwO,GACrQ,wFACA", "debugId": null}}, {"offset": {"line": 2340, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/bloomgoo/gold-exchange-platform/frontend/components/common/admin/ebanks/hooks/use-bank-table.ts/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const useBankTable = registerClientReference(\n    function() { throw new Error(\"Attempted to call useBankTable() from the server but useBankTable is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/common/admin/ebanks/hooks/use-bank-table.ts\",\n    \"useBankTable\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,eAAe,CAAA,GAAA,8VAAA,CAAA,0BAAuB,AAAD,EAC9C;IAAa,MAAM,IAAI,MAAM;AAAwO,GACrQ,oEACA", "debugId": null}}, {"offset": {"line": 2354, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 2364, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/bloomgoo/gold-exchange-platform/frontend/components/common/admin/ebanks/hooks/use-bank-actions.ts/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const useBankActions = registerClientReference(\n    function() { throw new Error(\"Attempted to call useBankActions() from the server but useBankActions is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/common/admin/ebanks/hooks/use-bank-actions.ts <module evaluation>\",\n    \"useBankActions\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,iBAAiB,CAAA,GAAA,8VAAA,CAAA,0BAAuB,AAAD,EAChD;IAAa,MAAM,IAAI,MAAM;AAA4O,GACzQ,0FACA", "debugId": null}}, {"offset": {"line": 2378, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/bloomgoo/gold-exchange-platform/frontend/components/common/admin/ebanks/hooks/use-bank-actions.ts/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const useBankActions = registerClientReference(\n    function() { throw new Error(\"Attempted to call useBankActions() from the server but useBankActions is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/common/admin/ebanks/hooks/use-bank-actions.ts\",\n    \"useBankActions\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,iBAAiB,CAAA,GAAA,8VAAA,CAAA,0BAAuB,AAAD,EAChD;IAAa,MAAM,IAAI,MAAM;AAA4O,GACzQ,sEACA", "debugId": null}}, {"offset": {"line": 2392, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 2402, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/bloomgoo/gold-exchange-platform/frontend/components/common/admin/ebanks/form/bank-form-hooks.ts/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const useBankForm = registerClientReference(\n    function() { throw new Error(\"Attempted to call useBankForm() from the server but useBankForm is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/common/admin/ebanks/form/bank-form-hooks.ts <module evaluation>\",\n    \"useBankForm\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,cAAc,CAAA,GAAA,8VAAA,CAAA,0BAAuB,AAAD,EAC7C;IAAa,MAAM,IAAI,MAAM;AAAsO,GACnQ,wFACA", "debugId": null}}, {"offset": {"line": 2416, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/bloomgoo/gold-exchange-platform/frontend/components/common/admin/ebanks/form/bank-form-hooks.ts/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const useBankForm = registerClientReference(\n    function() { throw new Error(\"Attempted to call useBankForm() from the server but useBankForm is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/common/admin/ebanks/form/bank-form-hooks.ts\",\n    \"useBankForm\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,cAAc,CAAA,GAAA,8VAAA,CAAA,0BAAuB,AAAD,EAC7C;IAAa,MAAM,IAAI,MAAM;AAAsO,GACnQ,oEACA", "debugId": null}}, {"offset": {"line": 2430, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 2440, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/bloomgoo/gold-exchange-platform/frontend/components/common/admin/ebanks/schemas/form-schema.ts"], "sourcesContent": ["/**\r\n * Form Schema - Validation schemas cho form ngân hàng\r\n * Sử dụng Zod để validate dữ liệu form\r\n */\r\n\r\nimport { z } from \"zod\";\r\n\r\nexport const formSchema = z.object({\r\n  brandName: z\r\n    .string()\r\n    .min(2, { message: \"Tên thương hiệu ngân hàng phải có ít nhất 2 ký tự\" })\r\n    .max(100, { message: \"Tên thương hiệu ngân hàng không được vượt quá 100 ký tự\" }),\r\n  fullName: z\r\n    .string()\r\n    .min(2, { message: \"Tên đầy đủ ngân hàng phải có ít nhất 2 ký tự\" })\r\n    .max(200, { message: \"Tên đầy đủ ngân hàng không được vượt quá 200 ký tự\" }),\r\n  shortName: z\r\n    .string()\r\n    .min(2, { message: \"Tên ngắn gọn ngân hàng phải có ít nhất 2 ký tự\" })\r\n    .max(50, { message: \"Tên ngắn gọn ngân hàng không được vượt quá 50 ký tự\" }),\r\n  code: z\r\n    .string()\r\n    .min(2, { message: \"<PERSON>ã ngân hàng phải có ít nhất 2 ký tự\" })\r\n    .max(20, { message: \"Mã ngân hàng không được vượt quá 20 ký tự\" }),\r\n  bin: z\r\n    .string()\r\n    .min(2, { message: \"Bin ngân hàng phải có ít nhất 2 ký tự\" })\r\n    .max(20, { message: \"Bin ngân hàng không được vượt quá 20 ký tự\" }),\r\n  logoPath: z\r\n    .string()\r\n    .url({ message: \"URL Logo không hợp lệ\" })\r\n    .optional()\r\n    .or(z.literal(\"\")),\r\n  icon: z\r\n    .string()\r\n    .url({ message: \"URL Icon không hợp lệ\" })\r\n    .optional()\r\n    .or(z.literal(\"\")),\r\n  isActive: z.boolean().default(true),\r\n});\r\n\r\nexport type BankFormValues = z.infer<typeof formSchema>;\r\n\r\n// Schema cho form cập nhật (giống với create)\r\nexport const bankUpdateFormSchema = formSchema;\r\n\r\nexport type BankUpdateFormValues = z.infer<typeof bankUpdateFormSchema>;\r\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;AAED;;AAEO,MAAM,aAAa,qLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IACjC,WAAW,qLAAA,CAAA,IAAC,CACT,MAAM,GACN,GAAG,CAAC,GAAG;QAAE,SAAS;IAAoD,GACtE,GAAG,CAAC,KAAK;QAAE,SAAS;IAA0D;IACjF,UAAU,qLAAA,CAAA,IAAC,CACR,MAAM,GACN,GAAG,CAAC,GAAG;QAAE,SAAS;IAA+C,GACjE,GAAG,CAAC,KAAK;QAAE,SAAS;IAAqD;IAC5E,WAAW,qLAAA,CAAA,IAAC,CACT,MAAM,GACN,GAAG,CAAC,GAAG;QAAE,SAAS;IAAiD,GACnE,GAAG,CAAC,IAAI;QAAE,SAAS;IAAsD;IAC5E,MAAM,qLAAA,CAAA,IAAC,CACJ,MAAM,GACN,GAAG,CAAC,GAAG;QAAE,SAAS;IAAuC,GACzD,GAAG,CAAC,IAAI;QAAE,SAAS;IAA4C;IAClE,KAAK,qLAAA,CAAA,IAAC,CACH,MAAM,GACN,GAAG,CAAC,GAAG;QAAE,SAAS;IAAwC,GAC1D,GAAG,CAAC,IAAI;QAAE,SAAS;IAA6C;IACnE,UAAU,qLAAA,CAAA,IAAC,CACR,MAAM,GACN,GAAG,CAAC;QAAE,SAAS;IAAwB,GACvC,QAAQ,GACR,EAAE,CAAC,qLAAA,CAAA,IAAC,CAAC,OAAO,CAAC;IAChB,MAAM,qLAAA,CAAA,IAAC,CACJ,MAAM,GACN,GAAG,CAAC;QAAE,SAAS;IAAwB,GACvC,QAAQ,GACR,EAAE,CAAC,qLAAA,CAAA,IAAC,CAAC,OAAO,CAAC;IAChB,UAAU,qLAAA,CAAA,IAAC,CAAC,OAAO,GAAG,OAAO,CAAC;AAChC;AAKO,MAAM,uBAAuB", "debugId": null}}, {"offset": {"line": 2490, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/bloomgoo/gold-exchange-platform/frontend/components/common/admin/ebanks/utils/bank-constants.ts"], "sourcesContent": ["/**\r\n * Bank Constants - Các hằng số và enums cho module ngân hàng\r\n */\r\n\r\n// API endpoints\r\nexport const BANK_ENDPOINTS = {\r\n  LIST: 'banks',\r\n  STATISTICS: 'banks/statistics',\r\n  CREATE: 'banks',\r\n  UPDATE: (id: string) => `banks/${id}`,\r\n  DELETE: (id: string) => `banks/${id}/soft-delete`,\r\n  TOGGLE_STATUS: (id: string) => `banks/${id}/toggle-status`,\r\n  DUPLICATE: (id: string) => `banks/${id}/duplicate`,\r\n  BULK_DELETE: 'banks/bulk-soft-delete',\r\n  EXPORT: 'banks/export',\r\n  BULK_CREATE: 'banks/bulk',\r\n} as const;\r\n\r\n// Default pagination\r\nexport const DEFAULT_PAGINATION = {\r\n  pageIndex: 0,\r\n  pageSize: 20,\r\n} as const;\r\n\r\n// Default column visibility\r\nexport const DEFAULT_COLUMN_VISIBILITY = {\r\n  deletedAt: false,\r\n  deletedBy: false,\r\n} as const;\r\n\r\n// Status options\r\nexport const STATUS_OPTIONS = [\r\n  { value: 'all', label: 'Tất cả' },\r\n  { value: 'active', label: 'Đã kích hoạt' },\r\n  { value: 'inactive', label: 'V<PERSON> hiệu hóa' },\r\n] as const;\r\n\r\n// Form default values\r\nexport const FORM_DEFAULT_VALUES = {\r\n  brandName: '',\r\n  fullName: '',\r\n  shortName: '',\r\n  code: '',\r\n  bin: '',\r\n  logoPath: '',\r\n  icon: '',\r\n  isActive: true,\r\n} as const;\r\n\r\n// Toast messages\r\nexport const TOAST_MESSAGES = {\r\n  CREATE_SUCCESS: 'Tạo ngân hàng thành công',\r\n  UPDATE_SUCCESS: 'Cập nhật ngân hàng thành công',\r\n  DELETE_SUCCESS: (name: string) => `Đã xóa ngân hàng ${name}`,\r\n  TOGGLE_SUCCESS: (name: string, isActive: boolean) =>\r\n    `Đã ${isActive ? 'kích hoạt' : 'vô hiệu hóa'} ngân hàng ${name}`,\r\n  DUPLICATE_SUCCESS: (name: string) => `Đã nhân bản ngân hàng ${name} thành công`,\r\n  BULK_DELETE_SUCCESS: (count: number) => `Đã xóa ${count} ngân hàng thành công`,\r\n  EXPORT_SUCCESS: (format: string) => `Đã xuất dữ liệu thành công dưới dạng ${format.toUpperCase()}`,\r\n  IMPORT_SUCCESS: (count: number) => `Đã import ${count} ngân hàng thành công`,\r\n\r\n  // Error messages\r\n  CREATE_ERROR: 'Không thể tạo ngân hàng',\r\n  UPDATE_ERROR: 'Không thể cập nhật ngân hàng',\r\n  DELETE_ERROR: 'Không thể xóa ngân hàng',\r\n  TOGGLE_ERROR: 'Không thể thay đổi trạng thái ngân hàng',\r\n  DUPLICATE_ERROR: 'Không thể nhân bản ngân hàng',\r\n  BULK_DELETE_ERROR: 'Không thể xóa các ngân hàng đã chọn',\r\n  EXPORT_ERROR: (format: string) => `Không thể xuất dữ liệu dưới dạng ${format.toUpperCase()}`,\r\n  IMPORT_ERROR: 'Không thể import dữ liệu ngân hàng',\r\n  FETCH_ERROR: 'Không thể tải danh sách ngân hàng',\r\n  FETCH_DETAIL_ERROR: 'Không thể tải thông tin chi tiết ngân hàng',\r\n  FETCH_STATISTICS_ERROR: 'Không thể lấy thống kê ngân hàng',\r\n\r\n  // Auth errors\r\n  UNAUTHORIZED: 'Bạn chưa đăng nhập hoặc phiên đăng nhập đã hết hạn',\r\n  FORBIDDEN: 'Bạn không có quyền truy cập vào tài nguyên này',\r\n  NOT_FOUND: 'Không tìm thấy tài nguyên',\r\n} as const;\r\n\r\n// Loading messages\r\nexport const LOADING_MESSAGES = {\r\n  FETCHING: 'Đang tải dữ liệu...',\r\n  CREATING: 'Đang tạo ngân hàng...',\r\n  UPDATING: 'Đang cập nhật...',\r\n  DELETING: 'Đang xóa...',\r\n  EXPORTING: 'Đang xuất dữ liệu...',\r\n  IMPORTING: 'Đang import dữ liệu...',\r\n} as const;\r\n"], "names": [], "mappings": "AAAA;;CAEC,GAED,gBAAgB;;;;;;;;;;AACT,MAAM,iBAAiB;IAC5B,MAAM;IACN,YAAY;IACZ,QAAQ;IACR,QAAQ,CAAC,KAAe,CAAC,MAAM,EAAE,IAAI;IACrC,QAAQ,CAAC,KAAe,CAAC,MAAM,EAAE,GAAG,YAAY,CAAC;IACjD,eAAe,CAAC,KAAe,CAAC,MAAM,EAAE,GAAG,cAAc,CAAC;IAC1D,WAAW,CAAC,KAAe,CAAC,MAAM,EAAE,GAAG,UAAU,CAAC;IAClD,aAAa;IACb,QAAQ;IACR,aAAa;AACf;AAGO,MAAM,qBAAqB;IAChC,WAAW;IACX,UAAU;AACZ;AAGO,MAAM,4BAA4B;IACvC,WAAW;IACX,WAAW;AACb;AAGO,MAAM,iBAAiB;IAC5B;QAAE,OAAO;QAAO,OAAO;IAAS;IAChC;QAAE,OAAO;QAAU,OAAO;IAAe;IACzC;QAAE,OAAO;QAAY,OAAO;IAAc;CAC3C;AAGM,MAAM,sBAAsB;IACjC,WAAW;IACX,UAAU;IACV,WAAW;IACX,MAAM;IACN,KAAK;IACL,UAAU;IACV,MAAM;IACN,UAAU;AACZ;AAGO,MAAM,iBAAiB;IAC5B,gBAAgB;IAChB,gBAAgB;IAChB,gBAAgB,CAAC,OAAiB,CAAC,iBAAiB,EAAE,MAAM;IAC5D,gBAAgB,CAAC,MAAc,WAC7B,CAAC,GAAG,EAAE,WAAW,cAAc,cAAc,WAAW,EAAE,MAAM;IAClE,mBAAmB,CAAC,OAAiB,CAAC,sBAAsB,EAAE,KAAK,WAAW,CAAC;IAC/E,qBAAqB,CAAC,QAAkB,CAAC,OAAO,EAAE,MAAM,qBAAqB,CAAC;IAC9E,gBAAgB,CAAC,SAAmB,CAAC,qCAAqC,EAAE,OAAO,WAAW,IAAI;IAClG,gBAAgB,CAAC,QAAkB,CAAC,UAAU,EAAE,MAAM,qBAAqB,CAAC;IAE5E,iBAAiB;IACjB,cAAc;IACd,cAAc;IACd,cAAc;IACd,cAAc;IACd,iBAAiB;IACjB,mBAAmB;IACnB,cAAc,CAAC,SAAmB,CAAC,iCAAiC,EAAE,OAAO,WAAW,IAAI;IAC5F,cAAc;IACd,aAAa;IACb,oBAAoB;IACpB,wBAAwB;IAExB,cAAc;IACd,cAAc;IACd,WAAW;IACX,WAAW;AACb;AAGO,MAAM,mBAAmB;IAC9B,UAAU;IACV,UAAU;IACV,UAAU;IACV,UAAU;IACV,WAAW;IACX,WAAW;AACb", "debugId": null}}, {"offset": {"line": 2586, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/bloomgoo/gold-exchange-platform/frontend/components/common/admin/ebanks/index.tsx"], "sourcesContent": ["/**\r\n * EBanks Module - Barrel export\r\n * Export tất cả components và utilities của module ebanks\r\n */\r\n\r\n// Main component\r\nexport { default as EBanks } from './ebanks';\r\n\r\n// Sub components\r\nexport { BankHeader } from './components/bank-header';\r\nexport { BankToolbar } from './components/bank-toolbar';\r\nexport { BankTable } from './components/bank-table';\r\nexport { StatusTabs } from './components/status-tabs';\r\nexport { FloatDeleteButton } from './components/float-delete-button';\r\n\r\n// Form components\r\nexport { BankFormModal } from './form/bank-form-modal';\r\nexport { BankFormFields } from './form/bank-form-fields';\r\n\r\n// Detail components\r\nexport { BankDetailSheet } from './detail/bank-detail-sheet';\r\nexport { BankDetailHeader } from './detail/bank-detail-header';\r\nexport { BankDetailInfo } from './detail/bank-detail-info';\r\nexport { BankDetailImages } from './detail/bank-detail-images';\r\nexport { BankDetailSystem } from './detail/bank-detail-system';\r\n\r\n// Table components\r\nexport { getBankTableColumns } from './table/bank-table-columns';\r\nexport { BankTableActions } from './table/bank-table-actions';\r\nexport { \r\n  BankNameCell,\r\n  TextCell,\r\n  ImageCell,\r\n  StatusCell,\r\n  DateCell,\r\n  UserCell\r\n} from './table/bank-table-cells';\r\n\r\n// Hooks\r\nexport { useBankData } from './hooks/use-bank-data';\r\nexport { useBankTable } from './hooks/use-bank-table';\r\nexport { useBankActions } from './hooks/use-bank-actions';\r\nexport { useBankForm } from './form/bank-form-hooks';\r\n\r\n// Types\r\nexport type {\r\n  BankDto,\r\n  CreateBankDto,\r\n  UpdateBankDto,\r\n  BankStatisticsDto,\r\n  BankFormMode,\r\n  StatusFilter,\r\n  StatusCounts,\r\n} from './types';\r\n\r\n// Schemas\r\nexport { \r\n  formSchema, \r\n  bankUpdateFormSchema,\r\n  type BankFormValues,\r\n  type BankUpdateFormValues \r\n} from './schemas/form-schema';\r\n\r\n// Utils\r\nexport * from './utils/bank-constants';\r\nexport * from './utils/bank-helpers';\r\n"], "names": [], "mappings": "AAAA;;;CAGC,GAED,iBAAiB;;AACjB;AAEA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AAEA,kBAAkB;AAClB;AACA;AAEA,oBAAoB;AACpB;AACA;AACA;AACA;AACA;AAEA,mBAAmB;AACnB;AACA;AACA;;;;;;AAWA;AACA;AACA;AAaA,UAAU;AACV;AAOA,QAAQ;AACR;AACA", "debugId": null}}, {"offset": {"line": 2698, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/bloomgoo/gold-exchange-platform/frontend/app/%28admin%29/admin/banks/page.tsx"], "sourcesContent": ["import { LayoutProvider } from \"@/components/layout-provider\";\r\nimport { EBanks } from \"@/components/common/admin/ebanks\";\r\nimport type { Metadata } from \"next\";\r\n\r\n\r\nexport const metadata: Metadata = {\r\n  title: 'Quản lý ngân hàng',\r\n  description: 'Quản lý danh sách ngân hàng trong hệ thống',\r\n};\r\n\r\nexport default function AdminBanksPage() {\r\n  return (\r\n    <LayoutProvider\r\n      title=\"Quản lý ngân hàng\"\r\n      description=\"Quản lý danh sách ngân hàng trong hệ thống\"\r\n    >\r\n      <EBanks />\r\n    </LayoutProvider>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AAAA;;;;AAIO,MAAM,WAAqB;IAChC,OAAO;IACP,aAAa;AACf;AAEe,SAAS;IACtB,qBACE,uVAAC,iIAAA,CAAA,iBAAc;QACb,OAAM;QACN,aAAY;kBAEZ,cAAA,uVAAC,uLAAA,CAAA,SAAM;;;;;;;;;;AAGb", "debugId": null}}]}
# 🏦 Scripts Test Import Ngân Hàng

Bộ scripts để test chức năng tạo mới ngân hàng trong hệ thống, sử dụng dữ liệu từ API [banklookup.net](https://api.banklookup.net/bank/list).

## 📁 Các File Scripts

### 1. `test-bank-api.js` - Script Test Đơn Giản
Script đơn giản để test API tạo ngân hàng với dữ liệu mẫu.

**Tính năng:**
- ✅ Test đăng nhập và lấy JWT token
- ✅ Test tạo một ngân hàng đơn lẻ
- ✅ Test tạo nhiều ngân hàng cùng lúc (bulk create)
- ✅ Test đọc danh sách ngân hàng
- ✅ Thống kê kết quả

### 2. `import-banks.js` - Script Import Đầy Đủ
Script đầy đủ để lấy dữ liệu từ API banklookup.net và import vào hệ thống.

**Tính năng:**
- ✅ Lấy dữ liệu từ API banklookup.net (60+ ngân hàng Việt Nam)
- ✅ Chuyển đổi dữ liệu sang format hệ thống
- ✅ Lưu dữ liệu vào file JSON
- ✅ Test import vào hệ thống
- ✅ Ghi log chi tiết

### 3. `run-bank-import-test.ps1` - Script PowerShell
Script PowerShell để chạy test một cách thuận tiện trên Windows.

**Tính năng:**
- ✅ Kiểm tra môi trường (Node.js, dependencies)
- ✅ Cài đặt dependencies tự động
- ✅ Cấu hình linh hoạt
- ✅ Hiển thị kết quả đẹp mắt

## 🚀 Cách Sử Dụng

### Yêu Cầu Tiên Quyết
1. **Node.js** đã được cài đặt
2. **Backend đang chạy** tại `http://localhost:3000`
3. **Tài khoản admin** có quyền tạo ngân hàng

### Cách 1: Script Test Đơn Giản (Khuyến nghị)

```bash
# Từ thư mục backend
cd backend

# Cài đặt axios nếu chưa có
npm install axios

# Chạy test với cấu hình mặc định
node scripts/test-bank-api.js

# Hoặc với cấu hình tùy chỉnh
node scripts/test-bank-api.js --backend-url http://localhost:3001 --admin-email <EMAIL> --admin-password test123
```

### Cách 2: Script PowerShell (Windows)

```powershell
# Từ thư mục backend
cd backend

# Chạy với cấu hình mặc định
.\scripts\run-bank-import-test.ps1

# Chỉ lấy dữ liệu, không test tạo
.\scripts\run-bank-import-test.ps1 -OnlyFetch

# Với cấu hình tùy chỉnh
.\scripts\run-bank-import-test.ps1 -BackendUrl "http://localhost:3001" -AdminEmail "<EMAIL>" -AdminPassword "test123"

# Bỏ qua cài đặt dependencies
.\scripts\run-bank-import-test.ps1 -SkipInstall

# Hiển thị log chi tiết
.\scripts\run-bank-import-test.ps1 -Verbose
```

### Cách 3: Script Import Đầy Đủ

```bash
# Từ thư mục backend
cd backend

# Cài đặt axios
npm install axios

# Chạy script import đầy đủ
node scripts/import-banks.js
```

## 📊 Dữ Liệu Ngân Hàng

### Nguồn Dữ Liệu
- **API**: https://api.banklookup.net/bank/list
- **Số lượng**: 60+ ngân hàng Việt Nam
- **Thông tin**: Tên, mã, BIN, logo, icon, SWIFT code

### Format Dữ Liệu Đầu Vào (API)
```json
{
  "id": "cc2fa1a9-6ad5-4cc8-aee7-84a8cde9f62e",
  "name": "Ngân hàng TMCP Ngoại Thương Việt Nam",
  "code": "VCB",
  "bin": 970436,
  "short_name": "Vietcombank",
  "logo_url": "https://api.vietqr.io/img/VCB.png",
  "icon_url": "https://cdn.banklookup.net/assets/images/bank-icons/VCB.svg",
  "swift_code": "BFTVVNVX",
  "lookup_supported": 1
}
```

### Format Dữ Liệu Đầu Ra (Hệ Thống)
```json
{
  "brandName": "Vietcombank",
  "fullName": "Ngân hàng TMCP Ngoại Thương Việt Nam",
  "shortName": "Vietcombank",
  "code": "VCB",
  "bin": "970436",
  "logoPath": "https://api.vietqr.io/img/VCB.png",
  "icon": "https://cdn.banklookup.net/assets/images/bank-icons/VCB.svg",
  "isActive": true
}
```

## 📁 File Output

Sau khi chạy script, các file sau sẽ được tạo trong `scripts/output/`:

- **`banks-data.json`**: Dữ liệu đã chuyển đổi từ API
- **`sample-banks.json`**: 10 ngân hàng mẫu để test
- **`import-banks.log`**: Log chi tiết quá trình import
- **`console.log`**: Log console (nếu chạy bằng PowerShell)

## 🔧 Cấu Hình

### Thay Đổi Cấu Hình Backend
```javascript
// Trong scripts/test-bank-api.js hoặc scripts/import-banks.js
const CONFIG = {
  BACKEND_URL: 'http://localhost:3000',  // Thay đổi URL backend
  ADMIN_EMAIL: '<EMAIL>',      // Thay đổi email admin
  ADMIN_PASSWORD: 'admin123'             // Thay đổi password admin
};
```

### Thay Đổi Dữ Liệu Test
```javascript
// Trong scripts/test-bank-api.js
const SAMPLE_BANKS = [
  {
    brandName: 'Tên ngân hàng',
    fullName: 'Tên đầy đủ',
    shortName: 'Tên ngắn',
    code: 'CODE',
    bin: '970xxx',
    logoPath: 'https://example.com/logo.png',
    icon: 'https://example.com/icon.svg',
    isActive: true
  }
  // Thêm ngân hàng khác...
];
```

## 🐛 Troubleshooting

### Lỗi Thường Gặp

#### 1. "Cannot connect to backend"
```bash
# Kiểm tra backend có đang chạy không
curl http://localhost:3000/api/v1/health

# Hoặc
Invoke-WebRequest -Uri "http://localhost:3000/api/v1/health"
```

#### 2. "Login failed"
- Kiểm tra email/password admin
- Kiểm tra tài khoản admin có tồn tại không
- Kiểm tra quyền của tài khoản

#### 3. "Permission denied"
- Kiểm tra role của user (cần ADMIN)
- Kiểm tra permission (cần payment:create)

#### 4. "axios not found"
```bash
# Cài đặt axios
npm install axios
```

### Debug Mode

```bash
# Chạy với debug
DEBUG=* node scripts/test-bank-api.js

# Hoặc với log chi tiết
node scripts/import-banks.js 2>&1 | tee debug.log
```

## 📈 Kết Quả Mong Đợi

### Test Thành Công
```
✅ Backend đang hoạt động
✅ Đăng nhập thành công
✅ Tạo thành công: Vietcombank (ID: 123)
✅ Tạo bulk thành công 3 ngân hàng
📊 Số ngân hàng đã tạo thành công: 4
```

### Thống Kê
- **Tổng số ngân hàng từ API**: 60+
- **Ngân hàng hỗ trợ lookup**: 50+
- **Ngân hàng có SWIFT code**: 40+
- **Ngân hàng test thành công**: 5

## 🔗 API Endpoints Được Test

- `POST /api/v1/auth/login` - Đăng nhập
- `GET /api/v1/banks` - Lấy danh sách ngân hàng
- `POST /api/v1/banks` - Tạo một ngân hàng
- `POST /api/v1/banks/bulk` - Tạo nhiều ngân hàng
- `GET /api/v1/health` - Kiểm tra health

## 📞 Hỗ Trợ

Nếu gặp vấn đề, vui lòng:
1. Kiểm tra log trong `scripts/output/import-banks.log`
2. Kiểm tra backend có đang chạy không
3. Kiểm tra quyền của tài khoản admin
4. Liên hệ team phát triển

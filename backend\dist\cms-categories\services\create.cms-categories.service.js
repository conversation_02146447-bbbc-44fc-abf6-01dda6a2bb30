"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreateCmsCategoriesService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const event_emitter_1 = require("@nestjs/event-emitter");
const typeorm_transactional_1 = require("typeorm-transactional");
const base_cms_categories_service_1 = require("./base.cms-categories.service");
const slug_cms_categories_service_1 = require("./slug.cms-categories.service");
const cms_categories_entity_1 = require("../entity/cms-categories.entity");
const create_cms_category_dto_1 = require("../dto/create.cms-category.dto");
let CreateCmsCategoriesService = class CreateCmsCategoriesService extends base_cms_categories_service_1.BaseCmsCategoriesService {
    categoryRepository;
    dataSource;
    eventEmitter;
    slugService;
    constructor(categoryRepository, dataSource, eventEmitter, slugService) {
        super(categoryRepository, dataSource, eventEmitter);
        this.categoryRepository = categoryRepository;
        this.dataSource = dataSource;
        this.eventEmitter = eventEmitter;
        this.slugService = slugService;
    }
    async create(createDto, userId) {
        try {
            this.logger.debug(`Đang tạo chuyên mục CMS: ${JSON.stringify(createDto)}`);
            const uniqueSlug = await this.slugService.generateUniqueSlugForCreate(createDto.name, createDto.postType || cms_categories_entity_1.CmsCategoryPostType.POST, createDto.slug);
            const category = this.categoryRepository.create({
                name: createDto.name,
                slug: uniqueSlug,
                description: createDto.description,
                postType: createDto.postType,
                imageUrl: createDto.imageUrl,
                metaTitle: createDto.metaTitle,
                metaDescription: createDto.metaDescription,
                metaKeywords: createDto.metaKeywords,
                status: createDto.status,
                createdBy: userId,
                updatedBy: userId,
            });
            if (createDto.parentId) {
                const parent = await this.findById(createDto.parentId);
                if (parent) {
                    category.parent = parent;
                }
            }
            const savedCategory = await this.categoryRepository.save(category);
            const categoryDto = this.toDto(savedCategory);
            if (!categoryDto) {
                throw new common_1.InternalServerErrorException('Không thể chuyển đổi entity thành DTO');
            }
            this.eventEmitter.emit(this.EVENT_CATEGORY_CREATED, {
                categoryId: categoryDto.id,
                userId,
                newData: categoryDto,
            });
            return categoryDto;
        }
        catch (error) {
            this.logger.error(`Lỗi khi tạo chuyên mục CMS: ${error.message}`, error.stack);
            if (error instanceof common_1.BadRequestException || error instanceof common_1.ConflictException) {
                throw error;
            }
            throw new common_1.InternalServerErrorException(`Không thể tạo chuyên mục CMS: ${error.message}`);
        }
    }
    async bulkCreate(createDtos, userId) {
        try {
            this.logger.debug(`Đang tạo ${createDtos.length} chuyên mục CMS`);
            const categories = [];
            for (const createDto of createDtos) {
                const category = await this.create(createDto, userId);
                categories.push(category);
            }
            return categories;
        }
        catch (error) {
            this.logger.error(`Lỗi khi tạo nhiều chuyên mục CMS: ${error.message}`, error.stack);
            if (error instanceof common_1.BadRequestException || error instanceof common_1.ConflictException) {
                throw error;
            }
            throw new common_1.InternalServerErrorException(`Không thể tạo nhiều chuyên mục CMS: ${error.message}`);
        }
    }
    async duplicate(id, userId) {
        try {
            this.logger.debug(`Đang nhân bản chuyên mục CMS với ID: ${id}`);
            const category = await this.findById(id, ['parent'], true);
            if (!category) {
                throw new common_1.InternalServerErrorException(`Không tìm thấy chuyên mục với ID: ${id}`);
            }
            const createDto = {
                name: `${category.name} (Bản sao)`,
                description: category.description,
                postType: category.postType,
                imageUrl: category.imageUrl,
                metaTitle: category.metaTitle,
                metaDescription: category.metaDescription,
                metaKeywords: category.metaKeywords,
                status: category.status,
                parentId: category.parent?.id,
            };
            return this.create(createDto, userId);
        }
        catch (error) {
            this.logger.error(`Lỗi khi nhân bản chuyên mục CMS: ${error.message}`, error.stack);
            throw new common_1.InternalServerErrorException(`Không thể nhân bản chuyên mục CMS: ${error.message}`);
        }
    }
};
exports.CreateCmsCategoriesService = CreateCmsCategoriesService;
__decorate([
    (0, typeorm_transactional_1.Transactional)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [create_cms_category_dto_1.CreateCmsCategoryDto, String]),
    __metadata("design:returntype", Promise)
], CreateCmsCategoriesService.prototype, "create", null);
__decorate([
    (0, typeorm_transactional_1.Transactional)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Array, String]),
    __metadata("design:returntype", Promise)
], CreateCmsCategoriesService.prototype, "bulkCreate", null);
__decorate([
    (0, typeorm_transactional_1.Transactional)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String]),
    __metadata("design:returntype", Promise)
], CreateCmsCategoriesService.prototype, "duplicate", null);
exports.CreateCmsCategoriesService = CreateCmsCategoriesService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(cms_categories_entity_1.CmsCategories)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        typeorm_2.DataSource,
        event_emitter_1.EventEmitter2,
        slug_cms_categories_service_1.SlugCmsCategoriesService])
], CreateCmsCategoriesService);
//# sourceMappingURL=create.cms-categories.service.js.map
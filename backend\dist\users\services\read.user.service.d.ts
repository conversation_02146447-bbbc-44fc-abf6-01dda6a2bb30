import { Repository } from 'typeorm';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { BaseUserService } from './base.user.service';
import { User } from '../entities/user.entity';
import { Role } from '../../roles/entities/role.entity';
import { UserDto } from '../dto/user.dto';
export declare class ReadUserService extends BaseUserService {
    protected readonly userRepository: Repository<User>;
    protected readonly roleRepository: Repository<Role>;
    protected readonly eventEmitter: EventEmitter2;
    constructor(userRepository: Repository<User>, roleRepository: Repository<Role>, eventEmitter: EventEmitter2);
    findAll(params: {
        limit: number;
        page: number;
        sortBy?: string;
        sortOrder?: 'ASC' | 'DESC';
        filter?: string;
        relations: string[];
    }): Promise<{
        data: UserDto[];
        total: number;
    }>;
    findOne(id: string, relations?: string[]): Promise<UserDto>;
    findOneOrFail(id: string, relations?: string[]): Promise<UserDto>;
    findDeleted(params: {
        limit: number;
        page: number;
    }): Promise<{
        data: UserDto[];
        total: number;
    }>;
    findByUsername(username: string): Promise<User>;
    findByEmail(email: string): Promise<UserDto>;
    findByResetToken(resetToken: string): Promise<User>;
    findByVerificationToken(verificationToken: string): Promise<User>;
    search(keyword: string, params: {
        limit: number;
        page: number;
    }): Promise<{
        data: UserDto[];
        total: number;
    }>;
    count(filter?: string): Promise<number>;
    getStatistics(): Promise<{
        total: number;
        activeCounts: {
            true: number;
            false: number;
            PENDING: number;
        };
    }>;
}

import { Response } from 'express';
import { VnpayPaymentService } from '../services/vnpay-payment.service';
import { PaymentRequest, PaymentResponse } from '../interfaces/payment-integration.interface';
export declare class VnpayPaymentController {
    private readonly vnpayPaymentService;
    private readonly logger;
    constructor(vnpayPaymentService: VnpayPaymentService);
    createPayment(request: PaymentRequest): Promise<PaymentResponse>;
    handleReturn(query: Record<string, string>, res: Response): Promise<void>;
    handleIpn(query: Record<string, string>): Promise<{
        RspCode: string;
        Message: string;
    }>;
}

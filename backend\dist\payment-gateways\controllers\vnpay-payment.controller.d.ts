import { Response } from 'express';
import { VnpayPaymentService } from '../services/vnpay-payment.service';
import { VnpayTransactionRepository } from '../repositories/vnpay-transaction.repository';
import { PaymentRequest, PaymentResponse, PaymentQueryRequest, PaymentQueryResponse, PaymentRefundRequest, PaymentRefundResponse } from '../interfaces/payment-integration.interface';
export declare class VnpayPaymentController {
    private readonly vnpayPaymentService;
    private readonly vnpayTransactionRepository;
    private readonly logger;
    constructor(vnpayPaymentService: VnpayPaymentService, vnpayTransactionRepository: VnpayTransactionRepository);
    createPayment(request: PaymentRequest): Promise<PaymentResponse>;
    handleReturn(query: Record<string, string>, res: Response): Promise<void>;
    handleIpn(query: Record<string, string>): Promise<{
        RspCode: string;
        Message: string;
    }>;
    queryTransaction(request: PaymentQueryRequest): Promise<PaymentQueryResponse>;
    refundTransaction(request: PaymentRefundRequest): Promise<PaymentRefundResponse>;
    getTransaction(merchantTxnRef: string): Promise<any>;
    getStatistics(externalRef?: string, fromDate?: string, toDate?: string): Promise<any>;
    getTransactions(externalRef?: string, status?: string, fromDate?: string, toDate?: string, page?: number, limit?: number): Promise<any>;
    healthCheck(): Promise<any>;
    markExpiredTransactions(): Promise<any>;
}

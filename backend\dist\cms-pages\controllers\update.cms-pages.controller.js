"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UpdateCmsPagesController = void 0;
const openapi = require("@nestjs/swagger");
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const jwt_auth_guard_1 = require("../../common/guards/jwt-auth.guard");
const update_cms_pages_service_1 = require("../services/update.cms-pages.service");
const update_cms_page_dto_1 = require("../dto/update.cms-page.dto");
const cms_pages_entity_1 = require("../entity/cms-pages.entity");
const api_response_dto_1 = require("../../common/dto/api-response.dto");
const get_user_decorator_1 = require("../../common/decorators/get-user.decorator");
const roles_decorator_1 = require("../../common/decorators/roles.decorator");
const permissions_decorator_1 = require("../../common/decorators/permissions.decorator");
let UpdateCmsPagesController = class UpdateCmsPagesController {
    cmsPagesService;
    constructor(cmsPagesService) {
        this.cmsPagesService = cmsPagesService;
    }
    async update(id, updateCmsPageDto, userId) {
        return this.cmsPagesService.update(id, updateCmsPageDto, userId);
    }
    async bulkUpdate(updates, userId) {
        return this.cmsPagesService.bulkUpdate(updates, userId);
    }
    async publish(id, userId) {
        return this.cmsPagesService.publish(id, userId);
    }
    async draft(id, userId) {
        return this.cmsPagesService.draft(id, userId);
    }
    async updateStatus(id, status, userId) {
        return this.cmsPagesService.updateStatus(id, status, userId);
    }
    async updateTemplate(id, template, userId) {
        return this.cmsPagesService.updateTemplate(id, template, userId);
    }
    async bulkPublish(ids, userId) {
        return this.cmsPagesService.bulkPublish(ids, userId);
    }
    async bulkDraft(ids, userId) {
        return this.cmsPagesService.bulkDraft(ids, userId);
    }
};
exports.UpdateCmsPagesController = UpdateCmsPagesController;
__decorate([
    (0, common_1.Patch)(':id'),
    (0, roles_decorator_1.Roles)('ADMIN', 'EDITOR'),
    (0, permissions_decorator_1.Permissions)('cms-page:update'),
    (0, swagger_1.ApiOperation)({ summary: 'Cập nhật thông tin trang CMS' }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Trang CMS đã được cập nhật thành công.',
        type: api_response_dto_1.ApiResponseDto,
        schema: {
            properties: {
                data: { $ref: '#/components/schemas/CmsPageDto' },
            },
        },
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.NOT_FOUND,
        description: 'Không tìm thấy trang CMS.',
        type: api_response_dto_1.ApiResponseDto,
        schema: { properties: { data: { type: 'object', nullable: true } } },
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.BAD_REQUEST,
        description: 'Dữ liệu đầu vào không hợp lệ.',
        type: api_response_dto_1.ApiResponseDto,
        schema: { properties: { data: { type: 'object', nullable: true } } },
    }),
    (0, swagger_1.ApiParam)({ name: 'id', type: String, description: 'ID của trang CMS' }),
    (0, swagger_1.ApiBody)({ type: update_cms_page_dto_1.UpdateCmsPageDto }),
    openapi.ApiResponse({ status: 200, type: Object }),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __param(1, (0, common_1.Body)()),
    __param(2, (0, get_user_decorator_1.GetUser)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, update_cms_page_dto_1.UpdateCmsPageDto, String]),
    __metadata("design:returntype", Promise)
], UpdateCmsPagesController.prototype, "update", null);
__decorate([
    (0, common_1.Patch)('bulk'),
    (0, roles_decorator_1.Roles)('ADMIN'),
    (0, permissions_decorator_1.Permissions)('cms-page:update'),
    (0, swagger_1.ApiOperation)({ summary: 'Cập nhật nhiều trang CMS cùng lúc' }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Các trang CMS đã được cập nhật thành công.',
        type: api_response_dto_1.ApiResponseDto,
        schema: {
            properties: {
                data: {
                    type: 'array',
                    items: { $ref: '#/components/schemas/CmsPageDto' },
                },
            },
        },
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.BAD_REQUEST,
        description: 'Dữ liệu đầu vào không hợp lệ.',
        type: api_response_dto_1.ApiResponseDto,
        schema: { properties: { data: { type: 'object', nullable: true } } },
    }),
    (0, swagger_1.ApiBody)({
        schema: {
            type: 'array',
            items: {
                type: 'object',
                properties: {
                    id: { type: 'string', format: 'uuid' },
                    data: { $ref: '#/components/schemas/UpdateCmsPageDto' },
                },
                required: ['id', 'data'],
            },
        },
    }),
    openapi.ApiResponse({ status: 200, type: [require("../dto/cms-page.dto").CmsPageDto] }),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, get_user_decorator_1.GetUser)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Array, String]),
    __metadata("design:returntype", Promise)
], UpdateCmsPagesController.prototype, "bulkUpdate", null);
__decorate([
    (0, common_1.Patch)(':id/publish'),
    (0, roles_decorator_1.Roles)('ADMIN', 'EDITOR'),
    (0, permissions_decorator_1.Permissions)('cms-page:update'),
    (0, swagger_1.ApiOperation)({ summary: 'Xuất bản trang CMS' }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Trang CMS đã được xuất bản thành công.',
        type: api_response_dto_1.ApiResponseDto,
        schema: {
            properties: {
                data: { $ref: '#/components/schemas/CmsPageDto' },
            },
        },
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.NOT_FOUND,
        description: 'Không tìm thấy trang CMS.',
        type: api_response_dto_1.ApiResponseDto,
        schema: { properties: { data: { type: 'object', nullable: true } } },
    }),
    (0, swagger_1.ApiParam)({ name: 'id', type: String, description: 'ID của trang CMS' }),
    openapi.ApiResponse({ status: 200, type: Object }),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __param(1, (0, get_user_decorator_1.GetUser)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String]),
    __metadata("design:returntype", Promise)
], UpdateCmsPagesController.prototype, "publish", null);
__decorate([
    (0, common_1.Patch)(':id/draft'),
    (0, roles_decorator_1.Roles)('ADMIN', 'EDITOR'),
    (0, permissions_decorator_1.Permissions)('cms-page:update'),
    (0, swagger_1.ApiOperation)({ summary: 'Chuyển trang CMS thành draft' }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Trang CMS đã được chuyển thành draft thành công.',
        type: api_response_dto_1.ApiResponseDto,
        schema: {
            properties: {
                data: { $ref: '#/components/schemas/CmsPageDto' },
            },
        },
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.NOT_FOUND,
        description: 'Không tìm thấy trang CMS.',
        type: api_response_dto_1.ApiResponseDto,
        schema: { properties: { data: { type: 'object', nullable: true } } },
    }),
    (0, swagger_1.ApiParam)({ name: 'id', type: String, description: 'ID của trang CMS' }),
    openapi.ApiResponse({ status: 200, type: Object }),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __param(1, (0, get_user_decorator_1.GetUser)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String]),
    __metadata("design:returntype", Promise)
], UpdateCmsPagesController.prototype, "draft", null);
__decorate([
    (0, common_1.Patch)(':id/status'),
    (0, roles_decorator_1.Roles)('ADMIN', 'EDITOR'),
    (0, permissions_decorator_1.Permissions)('cms-page:update'),
    (0, swagger_1.ApiOperation)({ summary: 'Cập nhật trạng thái trang CMS' }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Trạng thái trang CMS đã được cập nhật thành công.',
        type: api_response_dto_1.ApiResponseDto,
        schema: {
            properties: {
                data: { $ref: '#/components/schemas/CmsPageDto' },
            },
        },
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.NOT_FOUND,
        description: 'Không tìm thấy trang CMS.',
        type: api_response_dto_1.ApiResponseDto,
        schema: { properties: { data: { type: 'object', nullable: true } } },
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.BAD_REQUEST,
        description: 'Trạng thái không hợp lệ.',
        type: api_response_dto_1.ApiResponseDto,
        schema: { properties: { data: { type: 'object', nullable: true } } },
    }),
    (0, swagger_1.ApiParam)({ name: 'id', type: String, description: 'ID của trang CMS' }),
    (0, swagger_1.ApiBody)({
        schema: {
            type: 'object',
            properties: {
                status: {
                    type: 'string',
                    enum: Object.values(cms_pages_entity_1.CmsPageStatus),
                    description: 'Trạng thái mới',
                },
            },
            required: ['status'],
        },
    }),
    openapi.ApiResponse({ status: 200, type: Object }),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __param(1, (0, common_1.Body)('status')),
    __param(2, (0, get_user_decorator_1.GetUser)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String, String]),
    __metadata("design:returntype", Promise)
], UpdateCmsPagesController.prototype, "updateStatus", null);
__decorate([
    (0, common_1.Patch)(':id/template'),
    (0, roles_decorator_1.Roles)('ADMIN', 'EDITOR'),
    (0, permissions_decorator_1.Permissions)('cms-page:update'),
    (0, swagger_1.ApiOperation)({ summary: 'Cập nhật template cho trang CMS' }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Template trang CMS đã được cập nhật thành công.',
        type: api_response_dto_1.ApiResponseDto,
        schema: {
            properties: {
                data: { $ref: '#/components/schemas/CmsPageDto' },
            },
        },
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.NOT_FOUND,
        description: 'Không tìm thấy trang CMS.',
        type: api_response_dto_1.ApiResponseDto,
        schema: { properties: { data: { type: 'object', nullable: true } } },
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.BAD_REQUEST,
        description: 'Template không hợp lệ.',
        type: api_response_dto_1.ApiResponseDto,
        schema: { properties: { data: { type: 'object', nullable: true } } },
    }),
    (0, swagger_1.ApiParam)({ name: 'id', type: String, description: 'ID của trang CMS' }),
    (0, swagger_1.ApiBody)({
        schema: {
            type: 'object',
            properties: {
                template: {
                    type: 'string',
                    description: 'Template mới',
                    enum: ['default', 'full-width', 'sidebar-left', 'sidebar-right', 'landing-page', 'contact', 'about', 'privacy-policy', 'terms-of-service'],
                },
            },
            required: ['template'],
        },
    }),
    openapi.ApiResponse({ status: 200, type: Object }),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __param(1, (0, common_1.Body)('template')),
    __param(2, (0, get_user_decorator_1.GetUser)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String, String]),
    __metadata("design:returntype", Promise)
], UpdateCmsPagesController.prototype, "updateTemplate", null);
__decorate([
    (0, common_1.Patch)('bulk/publish'),
    (0, roles_decorator_1.Roles)('ADMIN'),
    (0, permissions_decorator_1.Permissions)('cms-page:update'),
    (0, swagger_1.ApiOperation)({ summary: 'Xuất bản nhiều trang CMS cùng lúc' }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Các trang CMS đã được xuất bản thành công.',
        type: api_response_dto_1.ApiResponseDto,
        schema: {
            properties: {
                data: {
                    type: 'array',
                    items: { $ref: '#/components/schemas/CmsPageDto' },
                },
            },
        },
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.BAD_REQUEST,
        description: 'Dữ liệu đầu vào không hợp lệ.',
        type: api_response_dto_1.ApiResponseDto,
        schema: { properties: { data: { type: 'object', nullable: true } } },
    }),
    (0, swagger_1.ApiBody)({
        schema: {
            type: 'object',
            properties: {
                ids: {
                    type: 'array',
                    items: { type: 'string', format: 'uuid' },
                    description: 'Danh sách ID của các trang cần xuất bản',
                },
            },
            required: ['ids'],
        },
    }),
    openapi.ApiResponse({ status: 200, type: [require("../dto/cms-page.dto").CmsPageDto] }),
    __param(0, (0, common_1.Body)('ids')),
    __param(1, (0, get_user_decorator_1.GetUser)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Array, String]),
    __metadata("design:returntype", Promise)
], UpdateCmsPagesController.prototype, "bulkPublish", null);
__decorate([
    (0, common_1.Patch)('bulk/draft'),
    (0, roles_decorator_1.Roles)('ADMIN'),
    (0, permissions_decorator_1.Permissions)('cms-page:update'),
    (0, swagger_1.ApiOperation)({ summary: 'Chuyển nhiều trang CMS thành draft cùng lúc' }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Các trang CMS đã được chuyển thành draft thành công.',
        type: api_response_dto_1.ApiResponseDto,
        schema: {
            properties: {
                data: {
                    type: 'array',
                    items: { $ref: '#/components/schemas/CmsPageDto' },
                },
            },
        },
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.BAD_REQUEST,
        description: 'Dữ liệu đầu vào không hợp lệ.',
        type: api_response_dto_1.ApiResponseDto,
        schema: { properties: { data: { type: 'object', nullable: true } } },
    }),
    (0, swagger_1.ApiBody)({
        schema: {
            type: 'object',
            properties: {
                ids: {
                    type: 'array',
                    items: { type: 'string', format: 'uuid' },
                    description: 'Danh sách ID của các trang cần chuyển thành draft',
                },
            },
            required: ['ids'],
        },
    }),
    openapi.ApiResponse({ status: 200, type: [require("../dto/cms-page.dto").CmsPageDto] }),
    __param(0, (0, common_1.Body)('ids')),
    __param(1, (0, get_user_decorator_1.GetUser)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Array, String]),
    __metadata("design:returntype", Promise)
], UpdateCmsPagesController.prototype, "bulkDraft", null);
exports.UpdateCmsPagesController = UpdateCmsPagesController = __decorate([
    (0, swagger_1.ApiTags)('cms-pages'),
    (0, swagger_1.ApiBearerAuth)(),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, common_1.Controller)('cms/pages'),
    __metadata("design:paramtypes", [update_cms_pages_service_1.UpdateCmsPagesService])
], UpdateCmsPagesController);
//# sourceMappingURL=update.cms-pages.controller.js.map
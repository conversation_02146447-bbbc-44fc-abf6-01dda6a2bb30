import { UpdateCmsTagsService } from '../services/update.cms-tags.service';
import { ReadCmsTagsService } from '../services/read.cms-tags.service';
import { CmsTagDto } from '../dto/cms-tag.dto';
import { UpdateCmsTagDto } from '../dto/update.cms-tag.dto';
export declare class UpdateCmsTagsController {
    private readonly updateCmsTagsService;
    private readonly readCmsTagsService;
    constructor(updateCmsTagsService: UpdateCmsTagsService, readCmsTagsService: ReadCmsTagsService);
    update(id: string, updateCmsTagDto: UpdateCmsTagDto, userId: string): Promise<CmsTagDto | null>;
    bulkUpdate(updates: Array<{
        id: string;
        data: UpdateCmsTagDto;
    }>, userId: string): Promise<CmsTagDto[]>;
    updateSlugFromName(id: string, userId: string): Promise<CmsTagDto | null>;
    updateNameAndSlug(id: string, name: string, userId: string): Promise<CmsTagDto | null>;
}

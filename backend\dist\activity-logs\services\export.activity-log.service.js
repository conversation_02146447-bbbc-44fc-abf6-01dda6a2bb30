"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ExportActivityLogService = void 0;
const common_1 = require("@nestjs/common");
const base_activity_log_service_1 = require("./base.activity-log.service");
const read_activity_log_service_1 = require("./read.activity-log.service");
const custom_pagination_query_dto_1 = require("../../common/dto/custom-pagination-query.dto");
let ExportActivityLogService = class ExportActivityLogService extends base_activity_log_service_1.BaseActivityLogService {
    async export(format = 'json') {
        try {
            this.logger.debug(`Đang xuất dữ liệu lịch sử hoạt động với định dạng: ${format}`);
            const activityLogs = await this.activityLogRepository.find({
                where: { isDeleted: false },
                relations: ['user'],
                order: { createdAt: 'DESC' },
            });
            if (format === 'csv') {
                return this.formatCsv(activityLogs);
            }
            return activityLogs.map(log => this.toDto(log));
        }
        catch (error) {
            this.logger.error(`Lỗi khi xuất dữ liệu lịch sử hoạt động: ${error.message}`, error.stack);
            throw new common_1.InternalServerErrorException('Lỗi khi xuất dữ liệu lịch sử hoạt động');
        }
    }
    async exportByUser(userId, format = 'json') {
        try {
            this.logger.debug(`Đang xuất dữ liệu lịch sử hoạt động của người dùng ${userId} với định dạng: ${format}`);
            const activityLogs = await this.activityLogRepository.find({
                where: { userId, isDeleted: false },
                relations: ['user'],
                order: { createdAt: 'DESC' },
            });
            if (format === 'csv') {
                return this.formatCsv(activityLogs);
            }
            return activityLogs.map(log => this.toDto(log));
        }
        catch (error) {
            this.logger.error(`Lỗi khi xuất dữ liệu lịch sử hoạt động của người dùng: ${error.message}`, error.stack);
            throw new common_1.InternalServerErrorException('Lỗi khi xuất dữ liệu lịch sử hoạt động của người dùng');
        }
    }
    async exportByModule(module, format = 'json') {
        try {
            this.logger.debug(`Đang xuất dữ liệu lịch sử hoạt động của module ${module} với định dạng: ${format}`);
            const activityLogs = await this.activityLogRepository.find({
                where: { module, isDeleted: false },
                relations: ['user'],
                order: { createdAt: 'DESC' },
            });
            if (format === 'csv') {
                return this.formatCsv(activityLogs);
            }
            return activityLogs.map(log => this.toDto(log));
        }
        catch (error) {
            this.logger.error(`Lỗi khi xuất dữ liệu lịch sử hoạt động của module: ${error.message}`, error.stack);
            throw new common_1.InternalServerErrorException('Lỗi khi xuất dữ liệu lịch sử hoạt động của module');
        }
    }
    async exportWithFilter(paginationQuery, format = 'json') {
        try {
            this.logger.debug(`Đang xuất dữ liệu lịch sử hoạt động với bộ lọc: ${JSON.stringify(paginationQuery)}`);
            const modifiedQuery = new custom_pagination_query_dto_1.CustomPaginationQueryDto();
            Object.assign(modifiedQuery, {
                ...paginationQuery,
                limit: 1000000,
                page: 1
            });
            const readActivityLogService = new read_activity_log_service_1.ReadActivityLogService(this.activityLogRepository, this.dataSource, this.eventEmitter);
            const { data } = await readActivityLogService.findAll(modifiedQuery);
            if (format === 'csv') {
                return this.formatCsvFromDto(data);
            }
            return data;
        }
        catch (error) {
            this.logger.error(`Lỗi khi xuất dữ liệu lịch sử hoạt động với bộ lọc: ${error.message}`, error.stack);
            throw new common_1.InternalServerErrorException('Lỗi khi xuất dữ liệu lịch sử hoạt động với bộ lọc');
        }
    }
    formatCsv(activityLogs) {
        return activityLogs.map(log => ({
            id: log.id,
            userId: log.userId,
            userName: log.user?.email || '',
            action: log.action,
            module: log.module,
            description: log.description,
            ipAddress: log.ipAddress,
            userAgent: log.userAgent,
            createdAt: log.createdAt,
        }));
    }
    formatCsvFromDto(activityLogDtos) {
        return activityLogDtos.map(log => ({
            id: log.id,
            userId: log.userId,
            userName: log.user?.email || '',
            action: log.action,
            module: log.module,
            description: log.description,
            ipAddress: log.ipAddress,
            userAgent: log.userAgent,
            createdAt: log.createdAt,
        }));
    }
};
exports.ExportActivityLogService = ExportActivityLogService;
exports.ExportActivityLogService = ExportActivityLogService = __decorate([
    (0, common_1.Injectable)()
], ExportActivityLogService);
//# sourceMappingURL=export.activity-log.service.js.map
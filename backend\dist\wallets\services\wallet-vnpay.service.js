"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var WalletVnpayService_1;
var _a, _b;
Object.defineProperty(exports, "__esModule", { value: true });
exports.WalletVnpayService = void 0;
const common_1 = require("@nestjs/common");
const vnpay_payment_service_1 = require("../../payment-gateways/services/vnpay-payment.service");
const wallet_service_1 = require("./wallet.service");
const wallet_transaction_service_1 = require("./wallet-transaction.service");
const wallet_transaction_entity_1 = require("../entities/wallet-transaction.entity");
let WalletVnpayService = WalletVnpayService_1 = class WalletVnpayService {
    vnpayPaymentService;
    walletService;
    walletTransactionService;
    logger = new common_1.Logger(WalletVnpayService_1.name);
    constructor(vnpayPaymentService, walletService, walletTransactionService) {
        this.vnpayPaymentService = vnpayPaymentService;
        this.walletService = walletService;
        this.walletTransactionService = walletTransactionService;
        this.vnpayPaymentService.setEventHandler(this);
    }
    async createDeposit(request) {
        try {
            this.logger.log(`Creating deposit for user ${request.userId}, amount: ${request.amount}`);
            this.validateDepositRequest(request);
            const walletTransaction = await this.walletTransactionService.create({
                userId: request.userId,
                type: wallet_transaction_entity_1.WalletTransactionType.DEPOSIT,
                amount: request.amount,
                status: wallet_transaction_entity_1.WalletTransactionStatus.PENDING,
                description: request.description || `Nạp tiền qua VNPAY - ${request.amount.toLocaleString('vi-VN')} VND`,
                metadata: {
                    paymentMethod: 'VNPAY',
                    bankCode: request.bankCode,
                    clientIp: request.clientIp,
                },
            });
            const paymentResponse = await this.vnpayPaymentService.createPayment({
                externalRef: walletTransaction.id.toString(),
                amount: request.amount,
                description: `Nap tien vi - User ${request.userId} - ${request.amount.toLocaleString('vi-VN')} VND`,
                clientIp: request.clientIp,
                bankCode: request.bankCode,
                locale: 'vn',
                metadata: {
                    userId: request.userId,
                    walletTransactionId: walletTransaction.id,
                    type: 'DEPOSIT',
                },
            });
            await this.walletTransactionService.update(walletTransaction.id, {
                metadata: {
                    ...walletTransaction.metadata,
                    merchantTxnRef: paymentResponse.merchantTxnRef,
                    vnpayTransactionId: paymentResponse.transactionId,
                    paymentUrl: paymentResponse.paymentUrl,
                },
            });
            this.logger.log(`Deposit created successfully: ${paymentResponse.merchantTxnRef}`);
            return {
                paymentUrl: paymentResponse.paymentUrl,
                merchantTxnRef: paymentResponse.merchantTxnRef,
                amount: request.amount,
                expiresAt: paymentResponse.expiresAt,
            };
        }
        catch (error) {
            this.logger.error(`Error creating deposit: ${error.message}`, error.stack);
            throw error;
        }
    }
    async onPaymentCreated(data) {
        this.logger.log(`Payment created: ${data.merchantTxnRef} for wallet transaction: ${data.externalRef}`);
    }
    async onPaymentSuccess(data) {
        try {
            this.logger.log(`Payment success: ${data.merchantTxnRef} for wallet transaction: ${data.externalRef}`);
            const walletTransactionId = parseInt(data.externalRef);
            const walletTransaction = await this.walletTransactionService.findById(walletTransactionId);
            if (!walletTransaction) {
                throw new Error(`Wallet transaction not found: ${walletTransactionId}`);
            }
            await this.walletTransactionService.update(walletTransactionId, {
                status: wallet_transaction_entity_1.WalletTransactionStatus.COMPLETED,
                completedAt: new Date(),
                metadata: {
                    ...walletTransaction.metadata,
                    vnpayTxnRef: data.vnpayTxnRef,
                    vnpayTxnNo: data.vnpayTxnNo,
                    bankCode: data.bankCode,
                    payDate: data.payDate,
                    completedAt: new Date().toISOString(),
                },
            });
            await this.walletService.addBalance(walletTransaction.userId, data.amount, {
                type: 'VNPAY_DEPOSIT',
                transactionId: walletTransactionId,
                vnpayTxnRef: data.vnpayTxnRef,
                vnpayTxnNo: data.vnpayTxnNo,
            });
            this.logger.log(`Deposit completed successfully for user ${walletTransaction.userId}: +${data.amount} VND`);
        }
        catch (error) {
            this.logger.error(`Error processing payment success: ${error.message}`, error.stack);
            try {
                const walletTransactionId = parseInt(data.externalRef);
                await this.walletTransactionService.update(walletTransactionId, {
                    status: wallet_transaction_entity_1.WalletTransactionStatus.FAILED,
                    metadata: {
                        error: error.message,
                        errorAt: new Date().toISOString(),
                    },
                });
            }
            catch (updateError) {
                this.logger.error(`Error updating transaction status: ${updateError.message}`);
            }
        }
    }
    async onPaymentFailed(data) {
        try {
            this.logger.warn(`Payment failed: ${data.merchantTxnRef} - ${data.message}`);
            const walletTransactionId = parseInt(data.externalRef);
            await this.walletTransactionService.update(walletTransactionId, {
                status: wallet_transaction_entity_1.WalletTransactionStatus.FAILED,
                metadata: {
                    responseCode: data.responseCode,
                    errorMessage: data.message,
                    failedAt: new Date().toISOString(),
                },
            });
            this.logger.log(`Deposit failed for wallet transaction: ${walletTransactionId}`);
        }
        catch (error) {
            this.logger.error(`Error processing payment failure: ${error.message}`, error.stack);
        }
    }
    async onPaymentCancelled(data) {
        try {
            this.logger.log(`Payment cancelled: ${data.merchantTxnRef}`);
            const walletTransactionId = parseInt(data.externalRef);
            await this.walletTransactionService.update(walletTransactionId, {
                status: wallet_transaction_entity_1.WalletTransactionStatus.CANCELLED,
                metadata: {
                    cancelledAt: new Date().toISOString(),
                },
            });
            this.logger.log(`Deposit cancelled for wallet transaction: ${walletTransactionId}`);
        }
        catch (error) {
            this.logger.error(`Error processing payment cancellation: ${error.message}`, error.stack);
        }
    }
    validateDepositRequest(request) {
        if (!request.userId || request.userId <= 0) {
            throw new common_1.BadRequestException('User ID is required');
        }
        if (!request.amount || request.amount <= 0) {
            throw new common_1.BadRequestException('Amount must be greater than 0');
        }
        if (request.amount < 10000) {
            throw new common_1.BadRequestException('Minimum deposit amount is 10,000 VND');
        }
        if (request.amount > 50000000) {
            throw new common_1.BadRequestException('Maximum deposit amount is 50,000,000 VND');
        }
        if (!request.clientIp) {
            throw new common_1.BadRequestException('Client IP is required');
        }
    }
    async getDepositStatus(merchantTxnRef) {
        try {
            const vnpayTransaction = await this.vnpayPaymentService.getTransaction(merchantTxnRef);
            return vnpayTransaction;
        }
        catch (error) {
            this.logger.error(`Error getting deposit status: ${error.message}`, error.stack);
            throw error;
        }
    }
};
exports.WalletVnpayService = WalletVnpayService;
exports.WalletVnpayService = WalletVnpayService = WalletVnpayService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [vnpay_payment_service_1.VnpayPaymentService, typeof (_a = typeof wallet_service_1.WalletService !== "undefined" && wallet_service_1.WalletService) === "function" ? _a : Object, typeof (_b = typeof wallet_transaction_service_1.WalletTransactionService !== "undefined" && wallet_transaction_service_1.WalletTransactionService) === "function" ? _b : Object])
], WalletVnpayService);
//# sourceMappingURL=wallet-vnpay.service.js.map
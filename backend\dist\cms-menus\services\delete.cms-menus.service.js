"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.DeleteCmsMenusService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const event_emitter_1 = require("@nestjs/event-emitter");
const typeorm_transactional_1 = require("typeorm-transactional");
const base_cms_menus_service_1 = require("./base.cms-menus.service");
const cms_menus_entity_1 = require("../entity/cms-menus.entity");
let DeleteCmsMenusService = class DeleteCmsMenusService extends base_cms_menus_service_1.BaseCmsMenusService {
    menuRepository;
    dataSource;
    eventEmitter;
    constructor(menuRepository, dataSource, eventEmitter) {
        super(menuRepository, dataSource, eventEmitter);
        this.menuRepository = menuRepository;
        this.dataSource = dataSource;
        this.eventEmitter = eventEmitter;
    }
    async softDelete(id, userId) {
        try {
            this.logger.debug(`Đang xóa mềm menu CMS với ID: ${id}`);
            const menu = await this.findById(id, ['children']);
            if (!menu) {
                throw new common_1.NotFoundException(`Không tìm thấy menu với ID: ${id}`);
            }
            if (menu.children && menu.children.length > 0) {
                const activeChildren = menu.children.filter(child => !child.isDeleted);
                if (activeChildren.length > 0) {
                    throw new common_1.BadRequestException('Không thể xóa menu có menu con. Vui lòng xóa hoặc di chuyển các menu con trước.');
                }
            }
            const oldData = this.toDto(menu);
            menu.isDeleted = true;
            menu.deletedAt = new Date();
            menu.deletedBy = userId;
            const deletedMenu = await this.menuRepository.save(menu);
            const menuDto = this.toDto(deletedMenu);
            if (!menuDto) {
                throw new common_1.InternalServerErrorException('Không thể chuyển đổi entity thành DTO');
            }
            this.eventEmitter.emit(this.EVENT_MENU_DELETED, {
                menuId: menuDto.id,
                userId,
                oldData,
            });
            this.logger.debug(`Đã xóa mềm menu CMS thành công với ID: ${menuDto.id}`);
            return menuDto;
        }
        catch (error) {
            this.logger.error(`Lỗi khi xóa mềm menu CMS: ${error.message}`, error.stack);
            if (error instanceof common_1.NotFoundException || error instanceof common_1.BadRequestException) {
                throw error;
            }
            throw new common_1.InternalServerErrorException(`Không thể xóa mềm menu CMS: ${error.message}`);
        }
    }
    async restore(id, userId) {
        try {
            this.logger.debug(`Đang khôi phục menu CMS với ID: ${id}`);
            const menu = await this.menuRepository.findOne({
                where: { id },
                relations: ['parent', 'children'],
            });
            if (!menu) {
                throw new common_1.NotFoundException(`Không tìm thấy menu với ID: ${id}`);
            }
            if (!menu.isDeleted) {
                throw new common_1.BadRequestException('Menu chưa bị xóa, không thể khôi phục');
            }
            const oldData = this.toDto(menu);
            menu.isDeleted = false;
            menu.deletedAt = null;
            menu.deletedBy = null;
            menu.updatedBy = userId;
            const restoredMenu = await this.menuRepository.save(menu);
            const menuDto = this.toDto(restoredMenu);
            if (!menuDto) {
                throw new common_1.InternalServerErrorException('Không thể chuyển đổi entity thành DTO');
            }
            this.logger.debug(`Đã khôi phục menu CMS thành công với ID: ${menuDto.id}`);
            return menuDto;
        }
        catch (error) {
            this.logger.error(`Lỗi khi khôi phục menu CMS: ${error.message}`, error.stack);
            if (error instanceof common_1.NotFoundException || error instanceof common_1.BadRequestException) {
                throw error;
            }
            throw new common_1.InternalServerErrorException(`Không thể khôi phục menu CMS: ${error.message}`);
        }
    }
    async forceDelete(id, userId) {
        try {
            this.logger.debug(`Đang xóa vĩnh viễn menu CMS với ID: ${id}`);
            const menu = await this.menuRepository.findOne({
                where: { id },
                relations: ['children'],
            });
            if (!menu) {
                throw new common_1.NotFoundException(`Không tìm thấy menu với ID: ${id}`);
            }
            if (!menu.isDeleted) {
                throw new common_1.BadRequestException('Menu phải được xóa mềm trước khi xóa vĩnh viễn');
            }
            if (menu.children && menu.children.length > 0) {
                throw new common_1.BadRequestException('Không thể xóa vĩnh viễn menu có menu con. Vui lòng xóa vĩnh viễn các menu con trước.');
            }
            const oldData = this.toDto(menu);
            await this.menuRepository.remove(menu);
            this.eventEmitter.emit(this.EVENT_MENU_DELETED, {
                menuId: id,
                userId,
                oldData,
                isForceDelete: true,
            });
            this.logger.debug(`Đã xóa vĩnh viễn menu CMS thành công với ID: ${id}`);
            return true;
        }
        catch (error) {
            this.logger.error(`Lỗi khi xóa vĩnh viễn menu CMS: ${error.message}`, error.stack);
            if (error instanceof common_1.NotFoundException || error instanceof common_1.BadRequestException) {
                throw error;
            }
            throw new common_1.InternalServerErrorException(`Không thể xóa vĩnh viễn menu CMS: ${error.message}`);
        }
    }
    async bulkSoftDelete(ids, userId) {
        try {
            this.logger.debug(`Đang xóa mềm ${ids.length} menu CMS`);
            const deletedMenus = [];
            for (const id of ids) {
                try {
                    const deletedMenu = await this.softDelete(id, userId);
                    if (deletedMenu) {
                        deletedMenus.push(deletedMenu);
                    }
                }
                catch (error) {
                    this.logger.warn(`Không thể xóa menu với ID ${id}: ${error.message}`);
                }
            }
            this.logger.debug(`Đã xóa mềm ${deletedMenus.length}/${ids.length} menu CMS thành công`);
            return deletedMenus;
        }
        catch (error) {
            this.logger.error(`Lỗi khi xóa mềm nhiều menu CMS: ${error.message}`, error.stack);
            throw new common_1.InternalServerErrorException(`Không thể xóa mềm nhiều menu CMS: ${error.message}`);
        }
    }
};
exports.DeleteCmsMenusService = DeleteCmsMenusService;
__decorate([
    (0, typeorm_transactional_1.Transactional)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String]),
    __metadata("design:returntype", Promise)
], DeleteCmsMenusService.prototype, "softDelete", null);
__decorate([
    (0, typeorm_transactional_1.Transactional)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String]),
    __metadata("design:returntype", Promise)
], DeleteCmsMenusService.prototype, "restore", null);
__decorate([
    (0, typeorm_transactional_1.Transactional)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String]),
    __metadata("design:returntype", Promise)
], DeleteCmsMenusService.prototype, "forceDelete", null);
__decorate([
    (0, typeorm_transactional_1.Transactional)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Array, String]),
    __metadata("design:returntype", Promise)
], DeleteCmsMenusService.prototype, "bulkSoftDelete", null);
exports.DeleteCmsMenusService = DeleteCmsMenusService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(cms_menus_entity_1.CmsMenus)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        typeorm_2.DataSource,
        event_emitter_1.EventEmitter2])
], DeleteCmsMenusService);
//# sourceMappingURL=delete.cms-menus.service.js.map
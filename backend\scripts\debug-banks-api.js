/**
 * Script debug để kiểm tra cấu trúc API banks
 */

const axios = require('axios');

const CONFIG = {
  BACKEND_URL: 'http://localhost:3168',
  ADMIN_CREDENTIALS: {
    identity: '<EMAIL>',
    password: 'adminX@123'
  }
};

async function debugBanksAPI() {
  try {
    console.log('🔍 === DEBUG BANKS API ===');
    
    // 1. Đăng nhập
    console.log('\n1️⃣ Đăng nhập...');
    const loginResponse = await axios.post(`${CONFIG.BACKEND_URL}/api/v1/auth/login`, CONFIG.ADMIN_CREDENTIALS);
    const token = loginResponse.data?.data?.access_token;
    
    if (!token) {
      throw new Error('Không nhận được access token');
    }
    console.log('✅ Đăng nhập thành công');
    
    // 2. Test API banks với các params khác nhau
    console.log('\n2️⃣ Test API banks...');
    
    // Test 1: Không có params
    try {
      console.log('\n📋 Test 1: GET /api/v1/banks (không có params)');
      const response1 = await axios.get(`${CONFIG.BACKEND_URL}/api/v1/banks`, {
        headers: { 'Authorization': `Bearer ${token}` }
      });
      
      console.log('Response structure:', {
        status: response1.status,
        dataType: typeof response1.data,
        dataKeys: Object.keys(response1.data || {}),
        hasData: !!response1.data?.data,
        dataIsArray: Array.isArray(response1.data?.data),
        dataLength: response1.data?.data?.length || 'N/A'
      });
      
      if (response1.data?.data && Array.isArray(response1.data.data)) {
        console.log(`✅ Có ${response1.data.data.length} ngân hàng`);
        if (response1.data.data.length > 0) {
          console.log('Sample bank:', {
            id: response1.data.data[0].id,
            brandName: response1.data.data[0].brandName,
            code: response1.data.data[0].code
          });
        }
      } else {
        console.log('❌ Không có dữ liệu hoặc không phải array');
        console.log('Full response:', JSON.stringify(response1.data, null, 2));
      }
      
    } catch (error) {
      console.log('❌ Test 1 failed:', error.message);
      if (error.response) {
        console.log('Response:', JSON.stringify(error.response.data, null, 2));
      }
    }
    
    // Test 2: Với limit nhỏ
    try {
      console.log('\n📋 Test 2: GET /api/v1/banks?limit=5');
      const response2 = await axios.get(`${CONFIG.BACKEND_URL}/api/v1/banks`, {
        params: { limit: 5 },
        headers: { 'Authorization': `Bearer ${token}` }
      });
      
      console.log('Response with limit=5:', {
        status: response2.status,
        dataLength: response2.data?.data?.length || 'N/A',
        hasData: !!response2.data?.data
      });
      
    } catch (error) {
      console.log('❌ Test 2 failed:', error.message);
      if (error.response) {
        console.log('Response:', JSON.stringify(error.response.data, null, 2));
      }
    }
    
    // Test 3: Với page và limit
    try {
      console.log('\n📋 Test 3: GET /api/v1/banks?page=1&limit=10');
      const response3 = await axios.get(`${CONFIG.BACKEND_URL}/api/v1/banks`, {
        params: { page: 1, limit: 10 },
        headers: { 'Authorization': `Bearer ${token}` }
      });
      
      console.log('Response with page=1&limit=10:', {
        status: response3.status,
        dataLength: response3.data?.data?.length || 'N/A',
        hasData: !!response3.data?.data,
        fullResponse: JSON.stringify(response3.data, null, 2)
      });
      
    } catch (error) {
      console.log('❌ Test 3 failed:', error.message);
      if (error.response) {
        console.log('Response:', JSON.stringify(error.response.data, null, 2));
      }
    }
    
    // Test 4: Với limit = 100
    try {
      console.log('\n📋 Test 4: GET /api/v1/banks?page=1&limit=100');
      const response4 = await axios.get(`${CONFIG.BACKEND_URL}/api/v1/banks`, {
        params: { page: 1, limit: 100 },
        headers: { 'Authorization': `Bearer ${token}` }
      });
      
      console.log('Response with page=1&limit=100:', {
        status: response4.status,
        dataLength: response4.data?.data?.length || 'N/A',
        hasData: !!response4.data?.data
      });
      
    } catch (error) {
      console.log('❌ Test 4 failed:', error.message);
      if (error.response) {
        console.log('Response:', JSON.stringify(error.response.data, null, 2));
      }
    }
    
    // Test 5: Với limit > 100 (sẽ lỗi)
    try {
      console.log('\n📋 Test 5: GET /api/v1/banks?page=1&limit=200 (sẽ lỗi)');
      const response5 = await axios.get(`${CONFIG.BACKEND_URL}/api/v1/banks`, {
        params: { page: 1, limit: 200 },
        headers: { 'Authorization': `Bearer ${token}` }
      });
      
      console.log('❌ Test 5 không lỗi như mong đợi');
      
    } catch (error) {
      console.log('✅ Test 5 lỗi như mong đợi:', error.response?.data?.message || error.message);
    }
    
    console.log('\n🎯 === KẾT LUẬN ===');
    console.log('Hãy kiểm tra các test trên để hiểu cấu trúc response của API banks');
    
  } catch (error) {
    console.log('❌ Lỗi tổng quát:', error.message);
    if (error.response) {
      console.log('Response:', JSON.stringify(error.response.data, null, 2));
    }
  }
}

// Chạy debug
if (require.main === module) {
  debugBanksAPI();
}

module.exports = { debugBanksAPI };

"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CmsCategoryDto = void 0;
const openapi = require("@nestjs/swagger");
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
const class_transformer_1 = require("class-transformer");
const user_dto_1 = require("../../users/dto/user.dto");
const cms_categories_entity_1 = require("../entity/cms-categories.entity");
class CmsCategoryDto {
    id;
    businessCode;
    name;
    slug;
    description;
    postType;
    imageUrl;
    metaTitle;
    metaDescription;
    metaKeywords;
    status;
    parentId;
    parent;
    children;
    createdAt;
    updatedAt;
    createdBy;
    updatedBy;
    deletedBy;
    isDeleted;
    deletedAt;
    creator;
    updater;
    deleter;
    static _OPENAPI_METADATA_FACTORY() {
        return { id: { required: true, type: () => String, format: "uuid" }, businessCode: { required: true, type: () => String }, name: { required: true, type: () => String }, slug: { required: true, type: () => String }, description: { required: false, type: () => String }, postType: { required: true, enum: require("../entity/cms-categories.entity").CmsCategoryPostType }, imageUrl: { required: false, type: () => String }, metaTitle: { required: false, type: () => String }, metaDescription: { required: false, type: () => String }, metaKeywords: { required: false, type: () => String }, status: { required: true, enum: require("../entity/cms-categories.entity").CmsCategoryStatus }, parentId: { required: false, type: () => String, format: "uuid" }, parent: { required: false, type: () => require("./cms-category.dto").CmsCategoryDto }, children: { required: false, type: () => [require("./cms-category.dto").CmsCategoryDto] }, createdAt: { required: true, type: () => Date }, updatedAt: { required: true, type: () => Date }, createdBy: { required: false, type: () => String, format: "uuid" }, updatedBy: { required: false, type: () => String, format: "uuid" }, deletedBy: { required: false, type: () => String, format: "uuid" }, isDeleted: { required: true, type: () => Boolean }, deletedAt: { required: false, type: () => Date }, creator: { required: false, type: () => require("../../users/dto/user.dto").UserDto }, updater: { required: false, type: () => require("../../users/dto/user.dto").UserDto }, deleter: { required: false, type: () => require("../../users/dto/user.dto").UserDto } };
    }
}
exports.CmsCategoryDto = CmsCategoryDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'ID của chuyên mục',
        example: '550e8400-e29b-41d4-a716-************',
    }),
    (0, class_transformer_1.Expose)(),
    (0, class_validator_1.IsUUID)('4'),
    __metadata("design:type", String)
], CmsCategoryDto.prototype, "id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Mã kinh doanh của chuyên mục',
        example: 'CMC-240101-00001',
    }),
    (0, class_transformer_1.Expose)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CmsCategoryDto.prototype, "businessCode", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Tên chuyên mục',
        example: 'Tin tức công ty',
    }),
    (0, class_transformer_1.Expose)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CmsCategoryDto.prototype, "name", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Chuỗi URL thân thiện',
        example: 'tin-tuc-cong-ty',
    }),
    (0, class_transformer_1.Expose)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CmsCategoryDto.prototype, "slug", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Mô tả chuyên mục',
        example: 'Các tin tức và thông báo từ công ty',
    }),
    (0, class_transformer_1.Expose)(),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CmsCategoryDto.prototype, "description", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Loại post mà chuyên mục này áp dụng',
        example: cms_categories_entity_1.CmsCategoryPostType.POST,
        enum: cms_categories_entity_1.CmsCategoryPostType,
    }),
    (0, class_transformer_1.Expose)(),
    (0, class_validator_1.IsEnum)(cms_categories_entity_1.CmsCategoryPostType),
    __metadata("design:type", String)
], CmsCategoryDto.prototype, "postType", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'URL hình ảnh đại diện cho chuyên mục',
        example: 'https://example.com/images/category.jpg',
    }),
    (0, class_transformer_1.Expose)(),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CmsCategoryDto.prototype, "imageUrl", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Tiêu đề SEO',
        example: 'Tin tức công ty - Cập nhật mới nhất',
    }),
    (0, class_transformer_1.Expose)(),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CmsCategoryDto.prototype, "metaTitle", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Mô tả SEO',
        example: 'Theo dõi các tin tức và cập nhật mới nhất từ công ty chúng tôi',
    }),
    (0, class_transformer_1.Expose)(),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CmsCategoryDto.prototype, "metaDescription", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Từ khóa SEO',
        example: 'tin tức, công ty, cập nhật',
    }),
    (0, class_transformer_1.Expose)(),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CmsCategoryDto.prototype, "metaKeywords", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Trạng thái chuyên mục',
        example: cms_categories_entity_1.CmsCategoryStatus.ACTIVE,
        enum: cms_categories_entity_1.CmsCategoryStatus,
    }),
    (0, class_transformer_1.Expose)(),
    (0, class_validator_1.IsEnum)(cms_categories_entity_1.CmsCategoryStatus),
    __metadata("design:type", String)
], CmsCategoryDto.prototype, "status", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'ID của chuyên mục cha',
        example: '550e8400-e29b-41d4-a716-************',
    }),
    (0, class_transformer_1.Expose)(),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsUUID)('4'),
    __metadata("design:type", String)
], CmsCategoryDto.prototype, "parentId", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Thông tin chuyên mục cha',
        type: () => CmsCategoryDto,
    }),
    (0, class_transformer_1.Expose)(),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => CmsCategoryDto),
    __metadata("design:type", CmsCategoryDto)
], CmsCategoryDto.prototype, "parent", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Danh sách chuyên mục con',
        type: () => [CmsCategoryDto],
    }),
    (0, class_transformer_1.Expose)(),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => CmsCategoryDto),
    __metadata("design:type", Array)
], CmsCategoryDto.prototype, "children", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Thời gian tạo',
        example: '2023-01-01T00:00:00.000Z',
    }),
    (0, class_transformer_1.Expose)(),
    (0, class_validator_1.IsDate)(),
    __metadata("design:type", Date)
], CmsCategoryDto.prototype, "createdAt", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Thời gian cập nhật',
        example: '2023-01-01T00:00:00.000Z',
    }),
    (0, class_transformer_1.Expose)(),
    (0, class_validator_1.IsDate)(),
    __metadata("design:type", Date)
], CmsCategoryDto.prototype, "updatedAt", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'ID người tạo',
        example: '550e8400-e29b-41d4-a716-************',
    }),
    (0, class_transformer_1.Expose)(),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsUUID)('4'),
    __metadata("design:type", String)
], CmsCategoryDto.prototype, "createdBy", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'ID người cập nhật',
        example: '550e8400-e29b-41d4-a716-************',
    }),
    (0, class_transformer_1.Expose)(),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsUUID)('4'),
    __metadata("design:type", String)
], CmsCategoryDto.prototype, "updatedBy", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'ID người xóa',
        example: '550e8400-e29b-41d4-a716-************',
    }),
    (0, class_transformer_1.Expose)(),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsUUID)('4'),
    __metadata("design:type", String)
], CmsCategoryDto.prototype, "deletedBy", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Trạng thái xóa',
        example: false,
    }),
    (0, class_transformer_1.Expose)(),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], CmsCategoryDto.prototype, "isDeleted", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Thời gian xóa',
        example: '2023-01-01T00:00:00.000Z',
    }),
    (0, class_transformer_1.Expose)(),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsDate)(),
    __metadata("design:type", Date)
], CmsCategoryDto.prototype, "deletedAt", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Thông tin người tạo',
        type: () => user_dto_1.UserDto,
    }),
    (0, class_transformer_1.Expose)(),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => user_dto_1.UserDto),
    __metadata("design:type", user_dto_1.UserDto)
], CmsCategoryDto.prototype, "creator", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Thông tin người cập nhật',
        type: () => user_dto_1.UserDto,
    }),
    (0, class_transformer_1.Expose)(),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => user_dto_1.UserDto),
    __metadata("design:type", user_dto_1.UserDto)
], CmsCategoryDto.prototype, "updater", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Thông tin người xóa',
        type: () => user_dto_1.UserDto,
    }),
    (0, class_transformer_1.Expose)(),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => user_dto_1.UserDto),
    __metadata("design:type", user_dto_1.UserDto)
], CmsCategoryDto.prototype, "deleter", void 0);
//# sourceMappingURL=cms-category.dto.js.map
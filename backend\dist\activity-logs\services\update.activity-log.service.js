"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UpdateActivityLogService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_transactional_1 = require("typeorm-transactional");
const typeorm_1 = require("typeorm");
const base_activity_log_service_1 = require("./base.activity-log.service");
const update_activity_log_dto_1 = require("../dto/update-activity-log.dto");
let UpdateActivityLogService = class UpdateActivityLogService extends base_activity_log_service_1.BaseActivityLogService {
    async update(id, updateActivityLogDto, userId) {
        try {
            this.logger.debug(`Đang cập nhật lịch sử hoạt động với ID: ${id}`);
            const activityLog = await this.findByIdOrFail(id);
            if (updateActivityLogDto.action && !updateActivityLogDto.module) {
                updateActivityLogDto.module = this.determineModule(updateActivityLogDto.action);
                this.logger.debug(`Tự động xác định module: ${updateActivityLogDto.module} cho action: ${updateActivityLogDto.action}`);
            }
            const updatedActivityLog = {
                ...activityLog,
                ...updateActivityLogDto,
                updatedBy: userId || updateActivityLogDto.updatedBy,
            };
            const savedActivityLog = await this.activityLogRepository.save(updatedActivityLog);
            this.logger.debug(`Đã cập nhật lịch sử hoạt động với ID: ${savedActivityLog.id}`);
            this.eventEmitter.emit(this.EVENT_ACTIVITY_LOG_UPDATED, {
                activityLogId: savedActivityLog.id,
                userId: savedActivityLog.userId,
                action: savedActivityLog.action,
                module: savedActivityLog.module,
            });
            return this.toDto(savedActivityLog);
        }
        catch (error) {
            this.logger.error(`Lỗi khi cập nhật lịch sử hoạt động: ${error.message}`, error.stack);
            if (error instanceof common_1.NotFoundException) {
                throw error;
            }
            throw new common_1.InternalServerErrorException('Lỗi khi cập nhật lịch sử hoạt động');
        }
    }
    async bulkUpdate(updateActivityLogDtos, userId) {
        try {
            this.logger.debug(`Đang cập nhật hàng loạt ${updateActivityLogDtos.length} lịch sử hoạt động`);
            const ids = updateActivityLogDtos.map(dto => dto.id);
            const existingActivityLogs = await this.activityLogRepository.find({
                where: { id: (0, typeorm_1.In)(ids), isDeleted: false },
            });
            if (existingActivityLogs.length !== ids.length) {
                throw new common_1.NotFoundException('Một hoặc nhiều lịch sử hoạt động không tồn tại');
            }
            const updatedActivityLogs = await Promise.all(updateActivityLogDtos.map(async (dto) => {
                const activityLog = existingActivityLogs.find(log => log.id === dto.id);
                if (dto.action && !dto.module) {
                    dto.module = this.determineModule(dto.action);
                    this.logger.debug(`Tự động xác định module: ${dto.module} cho action: ${dto.action}`);
                }
                const updatedActivityLog = {
                    ...activityLog,
                    ...dto,
                    updatedBy: userId || dto.updatedBy,
                };
                return this.activityLogRepository.save(updatedActivityLog);
            }));
            this.logger.debug(`Đã cập nhật ${updatedActivityLogs.length} lịch sử hoạt động`);
            updatedActivityLogs.forEach(log => {
                this.eventEmitter.emit(this.EVENT_ACTIVITY_LOG_UPDATED, {
                    activityLogId: log.id,
                    userId: log.userId,
                    action: log.action,
                    module: log.module,
                });
            });
            return updatedActivityLogs.map(log => this.toDto(log));
        }
        catch (error) {
            this.logger.error(`Lỗi khi cập nhật hàng loạt lịch sử hoạt động: ${error.message}`, error.stack);
            if (error instanceof common_1.NotFoundException) {
                throw error;
            }
            throw new common_1.InternalServerErrorException('Lỗi khi cập nhật hàng loạt lịch sử hoạt động');
        }
    }
};
exports.UpdateActivityLogService = UpdateActivityLogService;
__decorate([
    (0, typeorm_transactional_1.Transactional)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, update_activity_log_dto_1.UpdateActivityLogDto, String]),
    __metadata("design:returntype", Promise)
], UpdateActivityLogService.prototype, "update", null);
__decorate([
    (0, typeorm_transactional_1.Transactional)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Array, String]),
    __metadata("design:returntype", Promise)
], UpdateActivityLogService.prototype, "bulkUpdate", null);
exports.UpdateActivityLogService = UpdateActivityLogService = __decorate([
    (0, common_1.Injectable)()
], UpdateActivityLogService);
//# sourceMappingURL=update.activity-log.service.js.map
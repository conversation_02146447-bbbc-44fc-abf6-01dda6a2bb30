import { Logger } from '@nestjs/common';
import { Repository, DataSource } from 'typeorm';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { SystemConfig } from '../entities/system-config.entity';
import { SystemConfigDto } from '../dto/system-config.dto';
import { BaseSystemConfigService } from './base.system-config.service';
export declare class DeleteSystemConfigService extends BaseSystemConfigService {
    protected readonly systemConfigRepository: Repository<SystemConfig>;
    protected readonly dataSource: DataSource;
    protected readonly eventEmitter: EventEmitter2;
    protected logger: Logger;
    constructor(systemConfigRepository: Repository<SystemConfig>, dataSource: DataSource, eventEmitter: EventEmitter2);
    softDelete(id: string, deletedBy?: string): Promise<SystemConfigDto>;
    softDeleteByKey(key: string, deletedBy?: string): Promise<SystemConfigDto>;
    restore(id: string): Promise<SystemConfigDto>;
    hardDelete(id: string): Promise<boolean>;
}

"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UpdateEcomProductCategoriesController = void 0;
const openapi = require("@nestjs/swagger");
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const jwt_auth_guard_1 = require("../../common/guards/jwt-auth.guard");
const update_ecom_product_categories_service_1 = require("../services/update.ecom-product-categories.service");
const read_ecom_product_categories_service_1 = require("../services/read.ecom-product-categories.service");
const update_ecom_product_category_dto_1 = require("../dto/update-ecom-product-category.dto");
const api_response_dto_1 = require("../../common/dto/api-response.dto");
const class_validator_1 = require("class-validator");
const swagger_2 = require("@nestjs/swagger");
class UpdateCategoryStatusDto {
    isActive;
}
__decorate([
    (0, swagger_2.ApiProperty)({
        description: 'Trạng thái hoạt động của danh mục',
        example: true,
    }),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], UpdateCategoryStatusDto.prototype, "isActive", void 0);
const get_user_decorator_1 = require("../../common/decorators/get-user.decorator");
const roles_decorator_1 = require("../../common/decorators/roles.decorator");
const permissions_decorator_1 = require("../../common/decorators/permissions.decorator");
let UpdateEcomProductCategoriesController = class UpdateEcomProductCategoriesController {
    ecomProductCategoriesService;
    readEcomProductCategoriesService;
    constructor(ecomProductCategoriesService, readEcomProductCategoriesService) {
        this.ecomProductCategoriesService = ecomProductCategoriesService;
        this.readEcomProductCategoriesService = readEcomProductCategoriesService;
    }
    async updatePut(id, updateEcomProductCategoryDto, userId) {
        await this.readEcomProductCategoriesService.findOneOrFail(id);
        const result = await this.ecomProductCategoriesService.update(id, updateEcomProductCategoryDto, userId);
        if (!result) {
            throw new common_1.NotFoundException(`Không thể cập nhật danh mục sản phẩm với ID: ${id}`);
        }
        return result;
    }
    async update(id, updateEcomProductCategoryDto, userId) {
        await this.readEcomProductCategoriesService.findOneOrFail(id);
        const result = await this.ecomProductCategoriesService.update(id, updateEcomProductCategoryDto, userId);
        if (!result) {
            throw new common_1.NotFoundException(`Không thể cập nhật danh mục sản phẩm với ID: ${id}`);
        }
        return result;
    }
    async updateStatus(id, updateStatusDto, userId) {
        await this.readEcomProductCategoriesService.findOneOrFail(id);
        const result = await this.ecomProductCategoriesService.update(id, { isActive: updateStatusDto.isActive }, userId);
        if (!result) {
            throw new common_1.NotFoundException(`Không thể cập nhật trạng thái danh mục sản phẩm với ID: ${id}`);
        }
        return result;
    }
};
exports.UpdateEcomProductCategoriesController = UpdateEcomProductCategoriesController;
__decorate([
    (0, common_1.Put)(':id'),
    (0, roles_decorator_1.Roles)('ADMIN'),
    (0, permissions_decorator_1.Permissions)('ecom-product-category:update'),
    (0, swagger_1.ApiOperation)({ summary: 'Cập nhật danh mục sản phẩm (PUT)' }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Danh mục sản phẩm đã được cập nhật thành công.',
        type: api_response_dto_1.ApiResponseDto,
        schema: {
            properties: {
                data: { $ref: '#/components/schemas/EcomProductCategoryDto' },
            },
        },
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.NOT_FOUND,
        description: 'Không tìm thấy danh mục sản phẩm.',
        type: api_response_dto_1.ApiResponseDto,
        schema: { properties: { data: { type: 'object', nullable: true } } },
    }),
    (0, swagger_1.ApiParam)({ name: 'id', type: String, description: 'ID của danh mục sản phẩm' }),
    (0, swagger_1.ApiBody)({ type: update_ecom_product_category_dto_1.UpdateEcomProductCategoryDto }),
    openapi.ApiResponse({ status: 200, type: require("../dto/ecom-product-category.dto").EcomProductCategoryDto }),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __param(1, (0, common_1.Body)()),
    __param(2, (0, get_user_decorator_1.GetUser)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, update_ecom_product_category_dto_1.UpdateEcomProductCategoryDto, String]),
    __metadata("design:returntype", Promise)
], UpdateEcomProductCategoriesController.prototype, "updatePut", null);
__decorate([
    (0, common_1.Patch)(':id'),
    (0, roles_decorator_1.Roles)('ADMIN'),
    (0, permissions_decorator_1.Permissions)('ecom-product-category:update'),
    (0, swagger_1.ApiOperation)({ summary: 'Cập nhật danh mục sản phẩm (PATCH)' }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Danh mục sản phẩm đã được cập nhật thành công.',
        type: api_response_dto_1.ApiResponseDto,
        schema: {
            properties: {
                data: { $ref: '#/components/schemas/EcomProductCategoryDto' },
            },
        },
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.NOT_FOUND,
        description: 'Không tìm thấy danh mục sản phẩm.',
        type: api_response_dto_1.ApiResponseDto,
        schema: { properties: { data: { type: 'object', nullable: true } } },
    }),
    (0, swagger_1.ApiParam)({ name: 'id', type: String, description: 'ID của danh mục sản phẩm' }),
    (0, swagger_1.ApiBody)({ type: update_ecom_product_category_dto_1.UpdateEcomProductCategoryDto }),
    openapi.ApiResponse({ status: 200, type: require("../dto/ecom-product-category.dto").EcomProductCategoryDto }),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __param(1, (0, common_1.Body)()),
    __param(2, (0, get_user_decorator_1.GetUser)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, update_ecom_product_category_dto_1.UpdateEcomProductCategoryDto, String]),
    __metadata("design:returntype", Promise)
], UpdateEcomProductCategoriesController.prototype, "update", null);
__decorate([
    (0, common_1.Patch)(':id/status'),
    (0, roles_decorator_1.Roles)('ADMIN'),
    (0, permissions_decorator_1.Permissions)('ecom-product-category:update'),
    (0, swagger_1.ApiOperation)({ summary: 'Cập nhật trạng thái hoạt động của danh mục sản phẩm' }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Trạng thái danh mục sản phẩm đã được cập nhật thành công.',
        type: api_response_dto_1.ApiResponseDto,
        schema: {
            properties: {
                data: { $ref: '#/components/schemas/EcomProductCategoryDto' },
            },
        },
    }),
    (0, swagger_1.ApiParam)({ name: 'id', type: String, description: 'ID của danh mục sản phẩm' }),
    (0, swagger_1.ApiBody)({ type: UpdateCategoryStatusDto }),
    openapi.ApiResponse({ status: 200, type: require("../dto/ecom-product-category.dto").EcomProductCategoryDto }),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __param(1, (0, common_1.Body)()),
    __param(2, (0, get_user_decorator_1.GetUser)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, UpdateCategoryStatusDto, String]),
    __metadata("design:returntype", Promise)
], UpdateEcomProductCategoriesController.prototype, "updateStatus", null);
exports.UpdateEcomProductCategoriesController = UpdateEcomProductCategoriesController = __decorate([
    (0, swagger_1.ApiTags)('ecom-product-categories'),
    (0, swagger_1.ApiBearerAuth)(),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, common_1.Controller)('ecom-product-categories'),
    __metadata("design:paramtypes", [update_ecom_product_categories_service_1.UpdateEcomProductCategoriesService,
        read_ecom_product_categories_service_1.ReadEcomProductCategoriesService])
], UpdateEcomProductCategoriesController);
//# sourceMappingURL=update.ecom-product-categories.controller.js.map
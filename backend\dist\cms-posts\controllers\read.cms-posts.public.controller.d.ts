import { ReadCmsPostsService } from '../services/read.cms-posts.service';
import { CmsPostPublicDto } from '../dto/cms-post.public.dto';
import { CmsPostType } from '../entity/cms-posts.entity';
import { CustomPaginationQueryDto } from '../../common/dto/custom-pagination-query.dto';
import { PaginationResponseDto } from '../../common/dto/pagination-response.dto';
export declare class ReadCmsPostsPublicController {
    private readonly cmsPostsService;
    private readonly logger;
    constructor(cmsPostsService: ReadCmsPostsService);
    private toPublicDto;
    private toPublicDtos;
    findAllPublished(paginationQuery: CustomPaginationQueryDto, postType?: CmsPostType, categoryId?: string): Promise<PaginationResponseDto<CmsPostPublicDto>>;
    findByPostType(postType: CmsPostType, paginationQuery: CustomPaginationQueryDto): Promise<PaginationResponseDto<CmsPostPublicDto>>;
    search(searchTerm: string, paginationQuery: CustomPaginationQueryDto, postType?: CmsPostType): Promise<PaginationResponseDto<CmsPostPublicDto>>;
    findBySlug(slug: string): Promise<CmsPostPublicDto | null>;
    findByCategorySlug(categorySlug: string, paginationQuery: CustomPaginationQueryDto, postType?: CmsPostType): Promise<PaginationResponseDto<CmsPostPublicDto>>;
    findByTagSlug(tagSlug: string, paginationQuery: CustomPaginationQueryDto, postType?: CmsPostType): Promise<PaginationResponseDto<CmsPostPublicDto>>;
}

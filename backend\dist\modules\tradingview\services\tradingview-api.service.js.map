{"version": 3, "file": "tradingview-api.service.js", "sourceRoot": "", "sources": ["../../../../src/modules/tradingview/services/tradingview-api.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;AAAA,2CAAkE;AAClE,2CAA+C;AAC/C,6CAAmD;AACnD,qCAAqC;AACrC,yCAA4C;AAC5C,+BAAsC;AACtC,gCAAgC;AAChC,gGAAqF;AACrF,yEAA8D;AAC9D,6EAQ0C;AAC1C,mFAA8E;AAC9E,mDAA+C;AAGxC,IAAM,qBAAqB,6BAA3B,MAAM,qBAAqB;IAeb;IACA;IACA;IACA;IAEA;IAEA;IArBF,MAAM,GAAG,IAAI,eAAM,CAAC,uBAAqB,CAAC,IAAI,CAAC,CAAC;IACzD,MAAM,CAAS;IACf,SAAS,CAAS;IAClB,OAAO,CAAS;IAChB,UAAU,CAAS;IACnB,QAAQ,CAAY;IACpB,WAAW,GAAG,KAAK,CAAC;IACpB,iBAAiB,GAAG,CAAC,CAAC;IACb,oBAAoB,GAAG,CAAC,CAAC;IAClC,iBAAiB,CAAiB;IAClC,mBAAmB,CAAiB;IACpC,aAAa,GAAG,QAAQ,CAAC;IAEjC,YACmB,aAA4B,EAC5B,WAAwB,EACxB,2BAAwD,EACxD,YAA0B,EAE1B,sBAAgD,EAEhD,qBAA8C;QAP9C,kBAAa,GAAb,aAAa,CAAe;QAC5B,gBAAW,GAAX,WAAW,CAAa;QACxB,gCAA2B,GAA3B,2BAA2B,CAA6B;QACxD,iBAAY,GAAZ,YAAY,CAAc;QAE1B,2BAAsB,GAAtB,sBAAsB,CAA0B;QAEhD,0BAAqB,GAArB,qBAAqB,CAAyB;IAC9D,CAAC;IAEJ,KAAK,CAAC,YAAY;QAChB,MAAM,IAAI,CAAC,UAAU,EAAE,CAAC;QACxB,MAAM,IAAI,CAAC,gBAAgB,EAAE,CAAC;QAC9B,IAAI,CAAC,gBAAgB,EAAE,CAAC;QACxB,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,0CAA0C,CAAC,CAAC;IAC9D,CAAC;IAKO,KAAK,CAAC,UAAU;QACtB,IAAI,CAAC;YAEH,MAAM,YAAY,GAAG,MAAM,IAAI,CAAC,iBAAiB,CAC/C,8CAAqB,CAAC,OAAO,EAC7B,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,qBAAqB,EAAE,EAAE,CAAC,EACjD,0BAA0B,CAC3B,CAAC;YAEF,MAAM,eAAe,GAAG,MAAM,IAAI,CAAC,iBAAiB,CAClD,8CAAqB,CAAC,UAAU,EAChC,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,wBAAwB,EAAE,EAAE,CAAC,EACpD,iCAAiC,CAClC,CAAC;YAEF,MAAM,aAAa,GAAG,MAAM,IAAI,CAAC,iBAAiB,CAChD,8CAAqB,CAAC,QAAQ,EAC9B,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,sBAAsB,EAAE,6BAA6B,CAAC,EAC7E,qCAAqC,CACtC,CAAC;YAEF,MAAM,gBAAgB,GAAG,MAAM,IAAI,CAAC,iBAAiB,CACnD,8CAAqB,CAAC,WAAW,EACjC,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,yBAAyB,EAAE,gDAAgD,CAAC,EACnG,yCAAyC,CAC1C,CAAC;YAEF,MAAM,mBAAmB,GAAG,MAAM,IAAI,CAAC,iBAAiB,CACtD,8CAAqB,CAAC,cAAc,EACpC,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,4BAA4B,EAAE,QAAQ,CAAC,EAC9D,2CAA2C,CAC5C,CAAC;YAEF,IAAI,CAAC,MAAM,GAAG,YAAY,CAAC,WAAW,CAAC;YACvC,IAAI,CAAC,SAAS,GAAG,eAAe,CAAC,WAAW,CAAC;YAC7C,IAAI,CAAC,OAAO,GAAG,aAAa,CAAC,WAAW,CAAC;YACzC,IAAI,CAAC,UAAU,GAAG,gBAAgB,CAAC,WAAW,CAAC;YAC/C,IAAI,CAAC,aAAa,GAAG,mBAAmB,CAAC,WAAW,CAAC;YAErD,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,sCAAsC,CAAC,CAAC;QAC1D,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,yCAAyC,KAAK,CAAC,OAAO,EAAE,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC;YACzF,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAKO,KAAK,CAAC,iBAAiB,CAAC,GAAW,EAAE,YAAoB,EAAE,WAAmB;QACpF,IAAI,MAAM,GAAG,MAAM,IAAI,CAAC,sBAAsB,CAAC,OAAO,CAAC;YACrD,KAAK,EAAE,EAAE,SAAS,EAAE,GAAG,EAAE,SAAS,EAAE,KAAK,EAAE;SAC5C,CAAC,CAAC;QAEH,IAAI,CAAC,MAAM,EAAE,CAAC;YACZ,MAAM,GAAG,IAAI,CAAC,sBAAsB,CAAC,MAAM,CAAC;gBAC1C,SAAS,EAAE,GAAG;gBACd,WAAW,EAAE,YAAY;gBACzB,WAAW,EAAE,WAAW;gBACxB,WAAW,EAAE,aAAa;gBAC1B,UAAU,EAAE,MAAM;aACnB,CAAC,CAAC;YACH,MAAM,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QACjD,CAAC;QAED,OAAO,MAAM,CAAC;IAChB,CAAC;IAKO,KAAK,CAAC,gBAAgB;QAC5B,IAAI,CAAC;YACH,IAAI,CAAC,QAAQ,GAAG,IAAI,SAAS,CAAC,IAAI,CAAC,UAAU,EAAE;gBAC7C,OAAO,EAAE;oBACP,SAAS,EAAE,IAAI,CAAC,MAAM;iBACvB;aACF,CAAC,CAAC;YAEH,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC,MAAM,EAAE,GAAG,EAAE;gBAC5B,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;gBACxB,IAAI,CAAC,iBAAiB,GAAG,CAAC,CAAC;gBAC3B,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,sCAAsC,CAAC,CAAC;gBAGxD,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;YAC7C,CAAC,CAAC,CAAC;YAEH,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC,SAAS,EAAE,CAAC,IAAoB,EAAE,EAAE;gBACnD,IAAI,CAAC;oBACH,MAAM,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC,CAAC;oBAC5C,IAAI,CAAC,sBAAsB,CAAC,OAAO,CAAC,CAAC;gBACvC,CAAC;gBAAC,OAAO,KAAK,EAAE,CAAC;oBACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,yCAAyC,KAAK,CAAC,OAAO,EAAE,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC;gBAC3F,CAAC;YACH,CAAC,CAAC,CAAC;YAEH,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC,OAAO,EAAE,CAAC,KAAK,EAAE,EAAE;gBAClC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,kBAAkB,KAAK,CAAC,OAAO,EAAE,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC;YACpE,CAAC,CAAC,CAAC;YAEH,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC,OAAO,EAAE,GAAG,EAAE;gBAC7B,IAAI,CAAC,WAAW,GAAG,KAAK,CAAC;gBACzB,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,0CAA0C,CAAC,CAAC;gBAC7D,IAAI,CAAC,gBAAgB,EAAE,CAAC;YAC1B,CAAC,CAAC,CAAC;QACL,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,8CAA8C,KAAK,CAAC,OAAO,EAAE,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC;YAC9F,IAAI,CAAC,gBAAgB,EAAE,CAAC;QAC1B,CAAC;IACH,CAAC;IAKO,gBAAgB;QACtB,IAAI,IAAI,CAAC,iBAAiB,IAAI,IAAI,CAAC,oBAAoB,EAAE,CAAC;YACxD,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,6CAA6C,IAAI,CAAC,oBAAoB,WAAW,CAAC,CAAC;YACrG,OAAO;QACT,CAAC;QAED,IAAI,CAAC,iBAAiB,EAAE,CAAC;QACzB,MAAM,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,iBAAiB,CAAC,GAAG,IAAI,CAAC;QAEzD,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,gCAAgC,KAAK,eAAe,IAAI,CAAC,iBAAiB,IAAI,IAAI,CAAC,oBAAoB,GAAG,CAAC,CAAC;QAE5H,IAAI,IAAI,CAAC,iBAAiB,EAAE,CAAC;YAC3B,YAAY,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;QACvC,CAAC;QAED,IAAI,CAAC,iBAAiB,GAAG,UAAU,CAAC,GAAG,EAAE;YACvC,IAAI,CAAC,gBAAgB,EAAE,CAAC;QAC1B,CAAC,EAAE,KAAK,CAAC,CAAC;IACZ,CAAC;IAKO,sBAAsB,CAAC,OAAY;QAIzC,IAAI,OAAO,CAAC,IAAI,KAAK,cAAc,IAAI,OAAO,CAAC,MAAM,KAAK,IAAI,CAAC,aAAa,EAAE,CAAC;YAC7E,MAAM,SAAS,GAA4B;gBACzC,MAAM,EAAE,OAAO,CAAC,MAAM;gBACtB,KAAK,EAAE,OAAO,CAAC,KAAK;gBACpB,SAAS,EAAE,OAAO,CAAC,SAAS,IAAI,IAAI,CAAC,GAAG,EAAE;gBAC1C,MAAM,EAAE,OAAO,CAAC,MAAM,IAAI,CAAC;gBAC3B,aAAa,EAAE,OAAO,CAAC,cAAc,IAAI,CAAC;aAC3C,CAAC;YAGF,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,SAAS,SAAS,CAAC,MAAM,EAAE,EAAE,SAAS,EAAE,EAAE,CAAC,CAAC;YAGlE,IAAI,CAAC,oBAAoB,CAAC,SAAS,CAAC,CAAC;YAGrC,IAAI,CAAC,aAAa,CAAC,SAAS,CAAC,CAAC;QAChC,CAAC;IACH,CAAC;IAKO,iBAAiB,CAAC,MAAc;QACtC,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC;YACtB,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,qBAAqB,MAAM,+BAA+B,CAAC,CAAC;YAC7E,OAAO;QACT,CAAC;QAED,MAAM,gBAAgB,GAAG;YACvB,IAAI,EAAE,WAAW;YACjB,MAAM,EAAE,MAAM;SACf,CAAC;QAEF,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,gBAAgB,CAAC,CAAC,CAAC;QACrD,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,cAAc,MAAM,EAAE,CAAC,CAAC;IAC1C,CAAC;IAKO,oBAAoB,CAAC,IAA6B;QACxD,MAAM,WAAW,GAAiB;YAChC,MAAM,EAAE,IAAI,CAAC,MAAM;YACnB,KAAK,EAAE,IAAI,CAAC,KAAK;YACjB,SAAS,EAAE,IAAI,CAAC,SAAS;YACzB,MAAM,EAAE,IAAI,CAAC,MAAM;YACnB,aAAa,EAAE,IAAI,CAAC,aAAa;SAClC,CAAC;QAEF,IAAI,CAAC,2BAA2B,CAAC,oBAAoB,CAAC,WAAW,CAAC,CAAC;IACrE,CAAC;IAKO,KAAK,CAAC,aAAa,CAAC,IAA6B;QACvD,IAAI,CAAC;YACH,MAAM,WAAW,GAAG,IAAI,CAAC,qBAAqB,CAAC,MAAM,CAAC;gBACpD,MAAM,EAAE,IAAI,CAAC,MAAM;gBACnB,KAAK,EAAE,IAAI,CAAC,KAAK;gBACjB,MAAM,EAAE,IAAI,CAAC,MAAM;gBACnB,aAAa,EAAE,IAAI,CAAC,aAAa;gBACjC,SAAS,EAAE,IAAI,CAAC,SAAS;aAC1B,CAAC,CAAC;YAEH,MAAM,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;YACnD,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,0BAA0B,IAAI,CAAC,MAAM,KAAK,IAAI,CAAC,KAAK,EAAE,CAAC,CAAC;QAC5E,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,4BAA4B,KAAK,CAAC,OAAO,EAAE,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC;QAC9E,CAAC;IACH,CAAC;IAKO,KAAK,CAAC,gBAAgB;QAE5B,MAAM,qBAAqB,GAAG,MAAM,IAAI,CAAC,iBAAiB,CACxD,8CAAqB,CAAC,gBAAgB,EACtC,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,8BAA8B,EAAE,OAAO,CAAC,EAC/D,0EAA0E,CAC3E,CAAC;QAEF,MAAM,eAAe,GAAG,QAAQ,CAAC,qBAAqB,CAAC,WAAW,EAAE,EAAE,CAAC,CAAC;QAExE,IAAI,CAAC,mBAAmB,GAAG,WAAW,CAAC,KAAK,IAAI,EAAE;YAChD,IAAI,CAAC;gBACH,MAAM,IAAI,CAAC,gBAAgB,EAAE,CAAC;YAChC,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,gCAAgC,KAAK,CAAC,OAAO,EAAE,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC;YAClF,CAAC;QACH,CAAC,EAAE,eAAe,CAAC,CAAC;QAEpB,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,uDAAuD,eAAe,IAAI,CAAC,CAAC;IAC9F,CAAC;IAKM,KAAK,CAAC,gBAAgB;QAC3B,IAAI,CAAC;YAEH,MAAM,UAAU,GAAG,IAAI,CAAC,YAAY,CAAC,GAAG,CAA0B,SAAS,IAAI,CAAC,aAAa,EAAE,CAAC,CAAC;YACjG,IAAI,UAAU,EAAE,CAAC;gBACf,OAAO;oBACL,MAAM,EAAE,UAAU,CAAC,MAAM;oBACzB,KAAK,EAAE,UAAU,CAAC,KAAK;oBACvB,SAAS,EAAE,UAAU,CAAC,SAAS;oBAC/B,MAAM,EAAE,UAAU,CAAC,MAAM;oBACzB,aAAa,EAAE,UAAU,CAAC,aAAa;iBACxC,CAAC;YACJ,CAAC;YAGD,MAAM,GAAG,GAAG,GAAG,IAAI,CAAC,OAAO,kBAAkB,IAAI,CAAC,aAAa,EAAE,CAAC;YAClE,MAAM,QAAQ,GAAG,MAAM,IAAA,qBAAc,EACnC,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,GAAG,EAAE;gBACxB,OAAO,EAAE;oBACP,SAAS,EAAE,IAAI,CAAC,MAAM;oBACtB,cAAc,EAAE,kBAAkB;iBACnC;aACF,CAAC,CACH,CAAC;YAEF,IAAI,QAAQ,CAAC,IAAI,IAAI,QAAQ,CAAC,IAAI,CAAC,IAAI,IAAI,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBACzE,MAAM,SAAS,GAAG,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;gBAExC,MAAM,SAAS,GAAiB;oBAC9B,MAAM,EAAE,SAAS,CAAC,MAAM;oBACxB,KAAK,EAAE,SAAS,CAAC,EAAE;oBACnB,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;oBACrB,MAAM,EAAE,SAAS,CAAC,GAAG,IAAI,CAAC;oBAC1B,aAAa,EAAE,SAAS,CAAC,GAAG,IAAI,CAAC;oBACjC,GAAG,EAAE,SAAS,CAAC,GAAG;oBAClB,GAAG,EAAE,SAAS,CAAC,GAAG;oBAClB,MAAM,EAAE,SAAS,CAAC,GAAG,GAAG,SAAS,CAAC,GAAG;iBACtC,CAAC;gBAGF,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,SAAS,SAAS,CAAC,MAAM,EAAE,EAAE,SAAS,EAAE,EAAE,CAAC,CAAC;gBAGlE,IAAI,CAAC,2BAA2B,CAAC,oBAAoB,CAAC,SAAS,CAAC,CAAC;gBAGjE,MAAM,IAAI,CAAC,aAAa,CAAC,SAAS,CAAC,CAAC;gBAEpC,OAAO,SAAS,CAAC;YACnB,CAAC;YAED,MAAM,IAAI,KAAK,CAAC,4CAA4C,CAAC,CAAC;QAChE,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,6BAA6B,KAAK,CAAC,OAAO,EAAE,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC;YAC7E,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;CACF,CAAA;AA9UY,sDAAqB;gCAArB,qBAAqB;IADjC,IAAA,mBAAU,GAAE;IAoBR,WAAA,IAAA,0BAAgB,EAAC,mCAAY,CAAC,CAAA;IAE9B,WAAA,IAAA,0BAAgB,EAAC,iCAAW,CAAC,CAAA;qCANE,sBAAa;QACf,mBAAW;QACK,2DAA2B;QAC1C,4BAAY;QAEF,oBAAU;QAEX,oBAAU;GAtBzC,qBAAqB,CA8UjC"}
import { Logger } from '@nestjs/common';
import { Repository, DataSource, FindOptionsWhere } from 'typeorm';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { EcomProductCategories } from '../entity/ecom-product-categories.entity';
import { EcomProductCategoryDto } from '../dto/ecom-product-category.dto';
export declare class BaseEcomProductCategoriesService {
    protected readonly categoryRepository: Repository<EcomProductCategories>;
    protected readonly dataSource: DataSource;
    protected readonly eventEmitter: EventEmitter2;
    protected readonly logger: Logger;
    protected readonly EVENT_CATEGORY_CREATED = "ecom-product-category.created";
    protected readonly EVENT_CATEGORY_UPDATED = "ecom-product-category.updated";
    protected readonly EVENT_CATEGORY_DELETED = "ecom-product-category.deleted";
    protected readonly validRelations: string[];
    constructor(categoryRepository: Repository<EcomProductCategories>, dataSource: DataSource, eventEmitter: EventEmitter2);
    protected getQueryRunner(): Promise<import("typeorm").QueryRunner>;
    protected validateRelations(relations: string[]): string[];
    protected buildWhereClause(filter?: string): FindOptionsWhere<EcomProductCategories> | FindOptionsWhere<EcomProductCategories>[];
    protected findById(id: string, relations?: string[], throwError?: boolean): Promise<EcomProductCategories | null>;
    protected toDto(category: EcomProductCategories | null): EcomProductCategoryDto | null;
    protected toDtos(categories: EcomProductCategories[]): EcomProductCategoryDto[];
}

import { ConfigService } from '@nestjs/config';
import { JwtService } from '@nestjs/jwt';
import { OnGatewayConnection, OnGatewayDisconnect, OnGatewayInit } from '@nestjs/websockets';
import { Server, Socket } from 'socket.io';
import { BaseGateway } from '../../common/websockets/gateways/base.gateway';
import { WebSocketEmitterService } from '../../common/websockets/services/websocket-emitter.service';
export declare enum NotificationType {
    SYSTEM = "system",
    TRANSACTION = "transaction",
    USER = "user",
    CONTRACT = "contract",
    PAYMENT = "payment"
}
export interface Notification {
    id: string;
    type: NotificationType;
    title: string;
    message: string;
    isRead: boolean;
    createdAt: string;
    data?: any;
}
export declare enum NotificationEvent {
    NEW_NOTIFICATION = "new_notification",
    READ_NOTIFICATION = "read_notification",
    DELETE_NOTIFICATION = "delete_notification",
    GET_NOTIFICATIONS = "get_notifications"
}
export declare class NotificationsGateway extends BaseGateway implements OnGatewayInit, OnGatewayConnection, OnGatewayDisconnect {
    private readonly wsEmitter;
    private readonly notificationsLogger;
    private notifications;
    constructor(configService: ConfigService, wsEmitter: WebSocketEmitterService, jwtService: JwtService);
    afterInit(server: Server): void;
    handleConnection(client: Socket, ...args: any[]): Promise<void>;
    handleDisconnect(client: Socket): void;
    handleGetNotifications(client: Socket): {
        notifications: Notification[];
    };
    handleReadNotification(client: Socket, payload: {
        id?: string;
        all?: boolean;
    }): {
        success: boolean;
        message: string;
    };
    handleDeleteNotification(client: Socket, payload: {
        id: string;
    }): {
        success: boolean;
        message: string;
    };
    sendNotificationToUser(userId: string, notification: Notification): void;
}

import { Test, TestingModule } from '@nestjs/testing';
import { ConfigService } from '@nestjs/config';
import { HttpService } from '@nestjs/axios';
import { VnpayService } from './vnpay.service';
import { CreatePaymentDto } from '../dto/create-payment.dto';
import { PaymentGatewayType } from '../enums/payment-gateway-type.enum';
import { of } from 'rxjs';

describe('VnpayService', () => {
  let service: VnpayService;
  let configService: ConfigService;
  let httpService: HttpService;

  const mockConfigService = {
    get: jest.fn((key: string) => {
      const config = {
        VNPAY_TMN_CODE: 'TEST_TMN_CODE',
        VNPAY_HASH_SECRET: 'TEST_HASH_SECRET',
        VNPAY_URL: 'https://sandbox.vnpayment.vn/paymentv2/vpcpay.html',
        VNPAY_API_URL: 'https://sandbox.vnpayment.vn/merchant_webapi/api/transaction',
        VNPAY_RETURN_URL: 'https://test.com/return',
        VNPAY_IPN_URL: 'https://test.com/ipn',
      };
      return config[key];
    }),
  };

  const mockHttpService = {
    post: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        VnpayService,
        {
          provide: ConfigService,
          useValue: mockConfigService,
        },
        {
          provide: HttpService,
          useValue: mockHttpService,
        },
      ],
    }).compile();

    service = module.get<VnpayService>(VnpayService);
    configService = module.get<ConfigService>(ConfigService);
    httpService = module.get<HttpService>(HttpService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('createPaymentUrl', () => {
    it('should create payment URL successfully', async () => {
      const paymentData: CreatePaymentDto = {
        userId: 'test-user-id',
        walletId: 'test-wallet-id',
        amount: 100000,
        gatewayType: PaymentGatewayType.VNPAY,
        description: 'Test payment',
        ipAddress: '127.0.0.1',
      };

      const result = await service.createPaymentUrl(paymentData);

      expect(result).toHaveProperty('paymentUrl');
      expect(result).toHaveProperty('transactionId');
      expect(result.paymentUrl).toContain('https://sandbox.vnpayment.vn/paymentv2/vpcpay.html');
      expect(result.paymentUrl).toContain('vnp_Amount=********'); // 100000 * 100
      expect(result.paymentUrl).toContain('vnp_TmnCode=TEST_TMN_CODE');
      expect(result.paymentUrl).toContain('vnp_SecureHash=');
    });

    it('should throw error for invalid payment data', async () => {
      const invalidPaymentData: CreatePaymentDto = {
        userId: '',
        walletId: 'test-wallet-id',
        amount: 0,
        gatewayType: PaymentGatewayType.VNPAY,
        description: 'Test payment',
        ipAddress: '127.0.0.1',
      };

      await expect(service.createPaymentUrl(invalidPaymentData)).rejects.toThrow();
    });
  });

  describe('verifyReturnUrl', () => {
    it('should verify valid return URL successfully', async () => {
      // Mock valid VNPAY return parameters
      const mockParams = {
        vnp_Amount: '********',
        vnp_BankCode: 'NCB',
        vnp_BankTranNo: 'VNP********',
        vnp_CardType: 'ATM',
        vnp_OrderInfo: 'Thanh toan don hang test',
        vnp_PayDate: '**************',
        vnp_ResponseCode: '00',
        vnp_TmnCode: 'TEST_TMN_CODE',
        vnp_TransactionNo: '********',
        vnp_TransactionStatus: '00',
        vnp_TxnRef: 'test-user-id_1701936813000',
        vnp_SecureHash: 'calculated_hash_here', // This would be calculated based on actual secret
      };

      // Note: In real test, you would need to calculate the actual hash
      // For this example, we'll mock the verification
      const result = await service.verifyReturnUrl(mockParams);

      expect(result).toHaveProperty('isValid');
      expect(result).toHaveProperty('transactionId');
      expect(result).toHaveProperty('amount');
      expect(result).toHaveProperty('responseCode');
    });

    it('should handle invalid signature', async () => {
      const mockParams = {
        vnp_Amount: '********',
        vnp_ResponseCode: '00',
        vnp_TmnCode: 'TEST_TMN_CODE',
        vnp_TxnRef: 'test-txn-ref',
        vnp_SecureHash: 'invalid_hash',
      };

      const result = await service.verifyReturnUrl(mockParams);

      expect(result.isValid).toBe(false);
      expect(result.message).toContain('Chữ ký không hợp lệ');
    });
  });

  describe('queryTransaction', () => {
    it('should query transaction successfully', async () => {
      const mockResponse = {
        vnp_ResponseCode: '00',
        vnp_Message: 'Success',
        vnp_TmnCode: 'TEST_TMN_CODE',
        vnp_TxnRef: 'test-txn-ref',
        vnp_Amount: '********',
        vnp_TransactionStatus: '00',
      };

      mockHttpService.post.mockReturnValue(of({ data: mockResponse }));

      const queryData = {
        txnRef: 'test-txn-ref',
        transactionDate: '**************',
        orderInfo: 'Test query',
        ipAddr: '127.0.0.1',
      };

      const result = await service.queryTransaction(queryData);

      expect(result).toEqual(mockResponse);
      expect(mockHttpService.post).toHaveBeenCalled();
    });

    it('should throw error for invalid query data', async () => {
      const invalidQueryData = {
        txnRef: '',
        transactionDate: 'invalid_date',
        orderInfo: '',
        ipAddr: '',
      };

      await expect(service.queryTransaction(invalidQueryData)).rejects.toThrow();
    });
  });

  describe('refundTransaction', () => {
    it('should refund transaction successfully', async () => {
      const mockResponse = {
        vnp_ResponseCode: '00',
        vnp_Message: 'Success',
        vnp_TmnCode: 'TEST_TMN_CODE',
        vnp_TxnRef: 'test-txn-ref',
        vnp_Amount: '5000000',
      };

      mockHttpService.post.mockReturnValue(of({ data: mockResponse }));

      const refundData = {
        txnRef: 'test-txn-ref',
        amount: 50000,
        orderInfo: 'Test refund',
        transactionDate: '**************',
        transactionType: '02' as '02' | '03',
        createBy: 'admin',
        ipAddr: '127.0.0.1',
      };

      const result = await service.refundTransaction(refundData);

      expect(result).toEqual(mockResponse);
      expect(mockHttpService.post).toHaveBeenCalled();
    });

    it('should throw error for invalid refund data', async () => {
      const invalidRefundData = {
        txnRef: '',
        amount: 0,
        orderInfo: '',
        transactionDate: 'invalid_date',
        transactionType: 'invalid' as '02' | '03',
        createBy: '',
        ipAddr: '',
      };

      await expect(service.refundTransaction(invalidRefundData)).rejects.toThrow();
    });
  });

  describe('helper methods', () => {
    it('should format date correctly', () => {
      // Access private method for testing (not recommended in production)
      const testDate = new Date('2023-12-07T15:33:33.000Z');
      // Note: formatDate is private, so we can't test it directly
      // In real implementation, you might want to make it protected for testing
    });

    it('should sanitize order info correctly', () => {
      // Similar to formatDate, sanitizeOrderInfo is private
      // You would need to make it protected or test it indirectly
    });
  });
});

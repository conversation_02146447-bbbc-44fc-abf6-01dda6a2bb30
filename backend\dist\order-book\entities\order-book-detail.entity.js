"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.OrderBookDetail = void 0;
const openapi = require("@nestjs/swagger");
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
const typeorm_1 = require("typeorm");
const order_book_entity_1 = require("./order-book.entity");
const base_entity_1 = require("../../common/entities/base.entity");
const ecom_products_entity_1 = require("../../ecom-products/entity/ecom-products.entity");
let OrderBookDetail = class OrderBookDetail extends base_entity_1.BaseEntity {
    orderBookId;
    productId;
    price;
    quantity;
    totalPrice;
    orderBook;
    product;
    getEntityName() {
        return 'order_book_detail';
    }
    static _OPENAPI_METADATA_FACTORY() {
        return { orderBookId: { required: true, type: () => String }, productId: { required: true, type: () => String, format: "uuid" }, price: { required: true, type: () => Number }, quantity: { required: true, type: () => Number }, totalPrice: { required: true, type: () => Number }, orderBook: { required: true, type: () => require("./order-book.entity").OrderBook }, product: { required: true, type: () => require("../../ecom-products/entity/ecom-products.entity").EcomProduct } };
    }
};
exports.OrderBookDetail = OrderBookDetail;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'ID của order book',
        example: '550e8400-e29b-41d4-a716-************',
    }),
    (0, class_validator_1.IsNotEmpty)(),
    (0, class_validator_1.IsString)(),
    (0, typeorm_1.Column)({ type: 'uuid', nullable: false, name: 'order_book_id' }),
    __metadata("design:type", String)
], OrderBookDetail.prototype, "orderBookId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'ID sản phẩm liên kết',
        example: '550e8400-e29b-41d4-a716-************',
        required: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsUUID)(),
    (0, typeorm_1.Column)({ type: 'uuid', nullable: true, name: 'product_id' }),
    __metadata("design:type", String)
], OrderBookDetail.prototype, "productId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Đơn giá', example: '1000000.00' }),
    (0, class_validator_1.IsNotEmpty)(),
    (0, class_validator_1.IsDecimal)({ decimal_digits: '2' }),
    (0, typeorm_1.Column)({
        type: 'decimal',
        precision: 20,
        scale: 2,
        nullable: false,
        name: 'price',
    }),
    __metadata("design:type", Number)
], OrderBookDetail.prototype, "price", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Số lượng', example: '1.0000' }),
    (0, class_validator_1.IsNotEmpty)(),
    (0, class_validator_1.IsDecimal)({ decimal_digits: '4' }),
    (0, typeorm_1.Column)({
        type: 'decimal',
        precision: 20,
        scale: 4,
        nullable: false,
        name: 'volume',
    }),
    __metadata("design:type", Number)
], OrderBookDetail.prototype, "quantity", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Thành tiền', example: '1000000.00' }),
    (0, class_validator_1.IsNotEmpty)(),
    (0, class_validator_1.IsDecimal)({ decimal_digits: '2' }),
    (0, typeorm_1.Column)({
        type: 'decimal',
        precision: 20,
        scale: 2,
        nullable: false,
        name: 'total_price',
    }),
    __metadata("design:type", Number)
], OrderBookDetail.prototype, "totalPrice", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'OrderBook liên kết' }),
    (0, typeorm_1.ManyToOne)(() => order_book_entity_1.OrderBook, (orderBook) => orderBook.details),
    (0, typeorm_1.JoinColumn)({ name: 'order_book_id' }),
    __metadata("design:type", order_book_entity_1.OrderBook)
], OrderBookDetail.prototype, "orderBook", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Sản phẩm liên kết' }),
    (0, typeorm_1.ManyToOne)(() => ecom_products_entity_1.EcomProduct),
    (0, typeorm_1.JoinColumn)({ name: 'product_id' }),
    __metadata("design:type", ecom_products_entity_1.EcomProduct)
], OrderBookDetail.prototype, "product", void 0);
exports.OrderBookDetail = OrderBookDetail = __decorate([
    (0, typeorm_1.Entity)('order_book_detail')
], OrderBookDetail);
//# sourceMappingURL=order-book-detail.entity.js.map
"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.EcomProductsReadController = void 0;
const openapi = require("@nestjs/swagger");
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const jwt_auth_guard_1 = require("../../common/guards/jwt-auth.guard");
const read_ecom_products_service_1 = require("../services/read.ecom-products.service");
const custom_pagination_query_dto_1 = require("../../common/dto/custom-pagination-query.dto");
const api_response_dto_1 = require("../../common/dto/api-response.dto");
const get_user_decorator_1 = require("../../common/decorators/get-user.decorator");
const roles_decorator_1 = require("../../common/decorators/roles.decorator");
const permissions_decorator_1 = require("../../common/decorators/permissions.decorator");
const pagination_response_dto_1 = require("../../common/dto/pagination-response.dto");
const custom_pagination_meta_dto_1 = require("../../common/dto/custom-pagination-meta.dto");
let EcomProductsReadController = class EcomProductsReadController {
    ecomProductsService;
    constructor(ecomProductsService) {
        this.ecomProductsService = ecomProductsService;
    }
    async findAll(paginationQuery, userId, roles) {
        const { data, total } = await this.ecomProductsService.findAll(paginationQuery);
        const meta = new custom_pagination_meta_dto_1.CustomPageMetaDto({
            pageQueryDto: paginationQuery,
            itemCount: total,
        });
        return new pagination_response_dto_1.PaginationResponseDto(data, meta);
    }
    async getStatistics() {
        return this.ecomProductsService.getStatistics();
    }
    async findById(id) {
        return this.ecomProductsService.findById(id);
    }
    async findByCode(code) {
        return this.ecomProductsService.findByCode(code);
    }
    async findBySlug(slug) {
        return this.ecomProductsService.findBySlug(slug);
    }
    async findByCategory(categoryId, paginationQuery) {
        const queryParams = {
            ...paginationQuery,
            filter: paginationQuery.filter
                ? `${paginationQuery.filter},categoryId:${categoryId}`
                : `categoryId:${categoryId}`
        };
        const { data, total } = await this.ecomProductsService.findAll(queryParams);
        const meta = new custom_pagination_meta_dto_1.CustomPageMetaDto({
            pageQueryDto: paginationQuery,
            itemCount: total,
        });
        return new pagination_response_dto_1.PaginationResponseDto(data, meta);
    }
    async search(keyword, paginationQuery) {
        const { data, total } = await this.ecomProductsService.search(keyword, paginationQuery);
        const meta = new custom_pagination_meta_dto_1.CustomPageMetaDto({
            pageQueryDto: paginationQuery,
            itemCount: total,
        });
        return new pagination_response_dto_1.PaginationResponseDto(data, meta);
    }
    async findDeleted(paginationQuery) {
        const { data, total } = await this.ecomProductsService.findDeleted(paginationQuery);
        const meta = new custom_pagination_meta_dto_1.CustomPageMetaDto({
            pageQueryDto: paginationQuery,
            itemCount: total,
        });
        return new pagination_response_dto_1.PaginationResponseDto(data, meta);
    }
};
exports.EcomProductsReadController = EcomProductsReadController;
__decorate([
    (0, common_1.Get)(),
    (0, roles_decorator_1.Roles)('ADMIN', 'USER'),
    (0, permissions_decorator_1.Permissions)('ecom-product:read'),
    (0, swagger_1.ApiOperation)({ summary: 'Lấy danh sách sản phẩm với phân trang và lọc' }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Danh sách sản phẩm.',
        type: api_response_dto_1.ApiResponseDto,
        schema: {
            properties: {
                data: {
                    type: 'array',
                    items: { $ref: '#/components/schemas/EcomProductDto' },
                },
                meta: {
                    type: 'object',
                    properties: {
                        total: { type: 'number' },
                        page: { type: 'number' },
                        limit: { type: 'number' },
                        totalPages: { type: 'number' },
                    },
                },
            },
        },
    }),
    (0, swagger_1.ApiQuery)({ name: 'page', required: false, type: Number, description: 'Số trang' }),
    (0, swagger_1.ApiQuery)({ name: 'limit', required: false, type: Number, description: 'Số lượng mục trên mỗi trang' }),
    (0, swagger_1.ApiQuery)({
        name: 'sort',
        required: false,
        description: 'Sắp xếp theo định dạng field:order (ví dụ: createdAt:DESC,productName:ASC)',
    }),
    (0, swagger_1.ApiQuery)({ name: 'filter', required: false, type: String, description: 'Lọc theo điều kiện' }),
    (0, swagger_1.ApiQuery)({ name: 'search', required: false, type: String, description: 'Tìm kiếm theo từ khóa' }),
    openapi.ApiResponse({ status: 200 }),
    __param(0, (0, common_1.Query)()),
    __param(1, (0, get_user_decorator_1.GetUser)('id')),
    __param(2, (0, get_user_decorator_1.GetUser)('roles')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [custom_pagination_query_dto_1.CustomPaginationQueryDto, String, Array]),
    __metadata("design:returntype", Promise)
], EcomProductsReadController.prototype, "findAll", null);
__decorate([
    (0, common_1.Get)('statistics'),
    (0, roles_decorator_1.Roles)('ADMIN'),
    (0, permissions_decorator_1.Permissions)('ecom-product:read'),
    (0, swagger_1.ApiOperation)({ summary: 'Lấy thống kê số lượng sản phẩm' }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Thống kê số lượng sản phẩm.',
        type: api_response_dto_1.ApiResponseDto,
        schema: {
            properties: {
                data: {
                    type: 'object',
                    properties: {
                        total: { type: 'number' },
                        active: { type: 'number' },
                        inactive: { type: 'number' },
                    },
                },
            },
        },
    }),
    openapi.ApiResponse({ status: 200 }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], EcomProductsReadController.prototype, "getStatistics", null);
__decorate([
    (0, common_1.Get)(':id'),
    (0, roles_decorator_1.Roles)('ADMIN', 'USER'),
    (0, permissions_decorator_1.Permissions)('ecom-product:read'),
    (0, swagger_1.ApiOperation)({ summary: 'Lấy thông tin sản phẩm theo ID' }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Thông tin sản phẩm.',
        type: api_response_dto_1.ApiResponseDto,
        schema: {
            properties: {
                data: { $ref: '#/components/schemas/EcomProductDto' },
            },
        },
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.NOT_FOUND,
        description: 'Không tìm thấy sản phẩm.',
        type: api_response_dto_1.ApiResponseDto,
        schema: { properties: { data: { type: 'object', nullable: true } } },
    }),
    (0, swagger_1.ApiParam)({ name: 'id', type: String, description: 'ID của sản phẩm' }),
    openapi.ApiResponse({ status: 200, type: require("../dto/ecom-product.dto").EcomProductDto }),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], EcomProductsReadController.prototype, "findById", null);
__decorate([
    (0, common_1.Get)('code/:code'),
    (0, roles_decorator_1.Roles)('ADMIN', 'USER'),
    (0, permissions_decorator_1.Permissions)('ecom-product:read'),
    (0, swagger_1.ApiOperation)({ summary: 'Lấy thông tin sản phẩm theo mã sản phẩm' }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Thông tin sản phẩm.',
        type: api_response_dto_1.ApiResponseDto,
        schema: {
            properties: {
                data: { $ref: '#/components/schemas/EcomProductDto' },
            },
        },
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.NOT_FOUND,
        description: 'Không tìm thấy sản phẩm.',
        type: api_response_dto_1.ApiResponseDto,
        schema: { properties: { data: { type: 'object', nullable: true } } },
    }),
    (0, swagger_1.ApiParam)({ name: 'code', type: String, description: 'Mã sản phẩm' }),
    openapi.ApiResponse({ status: 200, type: require("../dto/ecom-product.dto").EcomProductDto }),
    __param(0, (0, common_1.Param)('code')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], EcomProductsReadController.prototype, "findByCode", null);
__decorate([
    (0, common_1.Get)('slug/:slug'),
    (0, roles_decorator_1.Roles)('ADMIN', 'USER'),
    (0, permissions_decorator_1.Permissions)('ecom-product:read'),
    (0, swagger_1.ApiOperation)({ summary: 'Lấy thông tin sản phẩm theo slug' }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Thông tin sản phẩm.',
        type: api_response_dto_1.ApiResponseDto,
        schema: {
            properties: {
                data: { $ref: '#/components/schemas/EcomProductDto' },
            },
        },
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.NOT_FOUND,
        description: 'Không tìm thấy sản phẩm.',
        type: api_response_dto_1.ApiResponseDto,
        schema: { properties: { data: { type: 'object', nullable: true } } },
    }),
    (0, swagger_1.ApiParam)({ name: 'slug', type: String, description: 'Slug của sản phẩm' }),
    openapi.ApiResponse({ status: 200, type: require("../dto/ecom-product.dto").EcomProductDto }),
    __param(0, (0, common_1.Param)('slug')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], EcomProductsReadController.prototype, "findBySlug", null);
__decorate([
    (0, common_1.Get)('category/:categoryId'),
    (0, roles_decorator_1.Roles)('ADMIN', 'USER'),
    (0, permissions_decorator_1.Permissions)('ecom-product:read'),
    (0, swagger_1.ApiOperation)({ summary: 'Lấy danh sách sản phẩm theo ID danh mục' }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Danh sách sản phẩm theo danh mục.',
        type: api_response_dto_1.ApiResponseDto,
        schema: {
            properties: {
                data: {
                    type: 'array',
                    items: { $ref: '#/components/schemas/EcomProductDto' },
                },
                meta: {
                    type: 'object',
                    properties: {
                        total: { type: 'number' },
                        page: { type: 'number' },
                        limit: { type: 'number' },
                        totalPages: { type: 'number' },
                    },
                },
            },
        },
    }),
    (0, swagger_1.ApiParam)({ name: 'categoryId', type: String, description: 'ID của danh mục sản phẩm' }),
    (0, swagger_1.ApiQuery)({ name: 'page', required: false, type: Number, description: 'Số trang' }),
    (0, swagger_1.ApiQuery)({ name: 'limit', required: false, type: Number, description: 'Số lượng mục trên mỗi trang' }),
    (0, swagger_1.ApiQuery)({
        name: 'sort',
        required: false,
        description: 'Sắp xếp theo định dạng field:order (ví dụ: createdAt:DESC,productName:ASC)',
    }),
    openapi.ApiResponse({ status: 200 }),
    __param(0, (0, common_1.Param)('categoryId', common_1.ParseUUIDPipe)),
    __param(1, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, custom_pagination_query_dto_1.CustomPaginationQueryDto]),
    __metadata("design:returntype", Promise)
], EcomProductsReadController.prototype, "findByCategory", null);
__decorate([
    (0, common_1.Get)('search'),
    (0, roles_decorator_1.Roles)('ADMIN', 'USER'),
    (0, permissions_decorator_1.Permissions)('ecom-product:read'),
    (0, swagger_1.ApiOperation)({ summary: 'Tìm kiếm sản phẩm theo từ khóa' }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Danh sách sản phẩm phù hợp với từ khóa.',
        type: api_response_dto_1.ApiResponseDto,
        schema: {
            properties: {
                data: {
                    type: 'array',
                    items: { $ref: '#/components/schemas/EcomProductDto' },
                },
                meta: {
                    type: 'object',
                    properties: {
                        total: { type: 'number' },
                        page: { type: 'number' },
                        limit: { type: 'number' },
                        totalPages: { type: 'number' },
                    },
                },
            },
        },
    }),
    (0, swagger_1.ApiQuery)({
        name: 'keyword',
        required: true,
        description: 'Từ khóa để tìm kiếm trên các trường',
    }),
    (0, swagger_1.ApiQuery)({ name: 'page', required: false, type: Number, description: 'Số trang' }),
    (0, swagger_1.ApiQuery)({ name: 'limit', required: false, type: Number, description: 'Số lượng mục trên mỗi trang' }),
    (0, swagger_1.ApiQuery)({
        name: 'sort',
        required: false,
        description: 'Sắp xếp theo định dạng field:order (ví dụ: createdAt:DESC,productName:ASC)',
    }),
    openapi.ApiResponse({ status: 200 }),
    __param(0, (0, common_1.Query)('keyword')),
    __param(1, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, custom_pagination_query_dto_1.CustomPaginationQueryDto]),
    __metadata("design:returntype", Promise)
], EcomProductsReadController.prototype, "search", null);
__decorate([
    (0, common_1.Get)('deleted'),
    (0, roles_decorator_1.Roles)('ADMIN'),
    (0, permissions_decorator_1.Permissions)('ecom-product:read'),
    (0, swagger_1.ApiOperation)({ summary: 'Lấy danh sách sản phẩm đã xóa' }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Danh sách sản phẩm đã xóa.',
        type: api_response_dto_1.ApiResponseDto,
        schema: {
            properties: {
                data: {
                    type: 'array',
                    items: { $ref: '#/components/schemas/EcomProductDto' },
                },
                meta: {
                    type: 'object',
                    properties: {
                        total: { type: 'number' },
                        page: { type: 'number' },
                        limit: { type: 'number' },
                        totalPages: { type: 'number' },
                    },
                },
            },
        },
    }),
    (0, swagger_1.ApiQuery)({ name: 'page', required: false, type: Number, description: 'Số trang' }),
    (0, swagger_1.ApiQuery)({ name: 'limit', required: false, type: Number, description: 'Số lượng mục trên mỗi trang' }),
    (0, swagger_1.ApiQuery)({
        name: 'sort',
        required: false,
        description: 'Sắp xếp theo định dạng field:order (ví dụ: createdAt:DESC,productName:ASC)',
    }),
    openapi.ApiResponse({ status: 200 }),
    __param(0, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [custom_pagination_query_dto_1.CustomPaginationQueryDto]),
    __metadata("design:returntype", Promise)
], EcomProductsReadController.prototype, "findDeleted", null);
exports.EcomProductsReadController = EcomProductsReadController = __decorate([
    (0, swagger_1.ApiTags)('ecom-products'),
    (0, swagger_1.ApiBearerAuth)(),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, common_1.Controller)('ecom-products'),
    __metadata("design:paramtypes", [read_ecom_products_service_1.ReadEcomProductsService])
], EcomProductsReadController);
//# sourceMappingURL=ecom-products.read.controller.js.map
"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ReadEcomProductsService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("typeorm");
const base_ecom_products_service_1 = require("./base.ecom-products.service");
let ReadEcomProductsService = class ReadEcomProductsService extends base_ecom_products_service_1.BaseEcomProductsService {
    async findById(id, relations = []) {
        try {
            const requiredRelations = ['category', 'creator', 'updater', 'deleter'];
            requiredRelations.forEach(relation => {
                if (!relations.includes(relation)) {
                    relations.push(relation);
                }
            });
            const product = await this.findByIdOrFail(id, relations);
            return this.toDto(product);
        }
        catch (error) {
            this.logger.error(`Lỗi khi tìm sản phẩm theo ID: ${error.message}`, error.stack);
            if (error instanceof common_1.NotFoundException) {
                throw error;
            }
            throw new common_1.InternalServerErrorException(`Không thể tìm sản phẩm: ${error.message}`);
        }
    }
    async findByCode(code, relations = []) {
        try {
            this.logger.debug(`Đang tìm sản phẩm theo mã: ${code}`);
            const requiredRelations = ['category', 'creator', 'updater', 'deleter'];
            requiredRelations.forEach(relation => {
                if (!relations.includes(relation)) {
                    relations.push(relation);
                }
            });
            const validatedRelations = this.validateRelations(relations);
            const queryBuilder = this.ecomProductRepository
                .createQueryBuilder('product')
                .where('product.productCode = :code', { code })
                .andWhere('product.isDeleted = :isDeleted', { isDeleted: false });
            validatedRelations.forEach(relation => {
                queryBuilder.leftJoinAndSelect(`product.${relation}`, relation);
            });
            const product = await queryBuilder.getOne();
            if (!product) {
                throw new common_1.NotFoundException(`Không tìm thấy sản phẩm với mã: ${code}`);
            }
            return this.toDto(product);
        }
        catch (error) {
            this.logger.error(`Lỗi khi tìm sản phẩm theo mã: ${error.message}`, error.stack);
            if (error instanceof common_1.NotFoundException) {
                throw error;
            }
            throw new common_1.InternalServerErrorException(`Không thể tìm sản phẩm theo mã: ${error.message}`);
        }
    }
    async findBySlug(slug, relations = []) {
        try {
            const requiredRelations = ['category', 'creator', 'updater', 'deleter'];
            requiredRelations.forEach(relation => {
                if (!relations.includes(relation)) {
                    relations.push(relation);
                }
            });
            const validatedRelations = this.validateRelations(relations);
            const queryBuilder = this.ecomProductRepository
                .createQueryBuilder('product')
                .where('product.slug = :slug', { slug })
                .andWhere('product.isDeleted = :isDeleted', { isDeleted: false });
            validatedRelations.forEach(relation => {
                queryBuilder.leftJoinAndSelect(`product.${relation}`, relation);
            });
            const product = await queryBuilder.getOne();
            if (!product) {
                throw new common_1.NotFoundException(`Không tìm thấy sản phẩm với slug: ${slug}`);
            }
            return this.toDto(product);
        }
        catch (error) {
            this.logger.error(`Lỗi khi tìm sản phẩm theo slug: ${error.message}`, error.stack);
            if (error instanceof common_1.NotFoundException) {
                throw error;
            }
            throw new common_1.InternalServerErrorException(`Không thể tìm sản phẩm theo slug: ${error.message}`);
        }
    }
    async findAll(params, categoryFilter) {
        try {
            const { limit, page, search } = params;
            const skip = (page - 1) * limit;
            const filter = categoryFilter || params.filter;
            const requiredRelations = ['category', 'creator', 'updater', 'deleter'];
            let relationsArray = [...requiredRelations];
            if (params.relationsArray && params.relationsArray.length > 0) {
                params.relationsArray.forEach(relation => {
                    if (!relationsArray.includes(relation)) {
                        relationsArray.push(relation);
                    }
                });
            }
            const validatedRelations = this.validateRelations(relationsArray);
            const queryBuilder = this.ecomProductRepository
                .createQueryBuilder('product')
                .where('product.isDeleted = :isDeleted', { isDeleted: false });
            if (search) {
                queryBuilder.andWhere(new typeorm_1.Brackets(qb => {
                    qb.where('LOWER(product.productName) LIKE LOWER(:search)', { search: `%${search}%` })
                        .orWhere('LOWER(product.productCode) LIKE LOWER(:search)', { search: `%${search}%` })
                        .orWhere('LOWER(product.description) LIKE LOWER(:search)', { search: `%${search}%` });
                }));
            }
            if (filter) {
                const filters = filter.split(',');
                const filterGroups = {};
                filters.forEach(filterItem => {
                    const [field, value] = filterItem.split(':');
                    if (field && value) {
                        if (!filterGroups[field]) {
                            filterGroups[field] = [];
                        }
                        filterGroups[field].push(value);
                    }
                });
                Object.entries(filterGroups).forEach(([field, values]) => {
                    if (values.length === 1) {
                        queryBuilder.andWhere(`product.${field} = :${field}`, { [field]: values[0] });
                    }
                    else {
                        queryBuilder.andWhere(`product.${field} IN (:...${field})`, { [field]: values });
                    }
                });
            }
            validatedRelations.forEach(relation => {
                queryBuilder.leftJoinAndSelect(`product.${relation}`, relation);
            });
            const sortOptions = params.sortOptions || [];
            if (sortOptions.length > 0) {
                sortOptions.forEach((option, index) => {
                    if (option.field.includes('.')) {
                        const [relation, field] = option.field.split('.');
                        if (index === 0) {
                            queryBuilder.orderBy(`${relation}.${field}`, option.order);
                        }
                        else {
                            queryBuilder.addOrderBy(`${relation}.${field}`, option.order);
                        }
                    }
                    else {
                        if (index === 0) {
                            queryBuilder.orderBy(`product.${option.field}`, option.order);
                        }
                        else {
                            queryBuilder.addOrderBy(`product.${option.field}`, option.order);
                        }
                    }
                });
            }
            else {
                queryBuilder.orderBy('product.createdAt', 'DESC');
            }
            queryBuilder.skip(skip).take(limit);
            const [products, total] = await queryBuilder.getManyAndCount();
            const productDtos = products.map(product => this.toDto(product));
            return { data: productDtos, total };
        }
        catch (error) {
            this.logger.error(`Lỗi khi tìm tất cả sản phẩm: ${error.message}`, error.stack);
            if (error instanceof common_1.BadRequestException) {
                throw error;
            }
            throw new common_1.InternalServerErrorException(`Không thể tìm tất cả sản phẩm: ${error.message}`);
        }
    }
    async findByCategory(categoryId, params) {
        try {
            const categoryFilter = `categoryId:${categoryId}`;
            return this.findAll(params, categoryFilter);
        }
        catch (error) {
            this.logger.error(`Lỗi khi tìm sản phẩm theo danh mục: ${error.message}`, error.stack);
            throw new common_1.InternalServerErrorException(`Không thể tìm sản phẩm theo danh mục: ${error.message}`);
        }
    }
    async search(keyword, params) {
        try {
            this.logger.debug(`Đang tìm kiếm sản phẩm với từ khóa: ${keyword}`);
            const queryBuilder = this.ecomProductRepository
                .createQueryBuilder('product')
                .where('product.isDeleted = :isDeleted', { isDeleted: false });
            const requiredRelations = ['category', 'creator', 'updater', 'deleter'];
            requiredRelations.forEach(relation => {
                queryBuilder.leftJoinAndSelect(`product.${relation}`, relation);
            });
            queryBuilder.andWhere(new typeorm_1.Brackets(qb => {
                qb.where('LOWER(product.productName) LIKE LOWER(:keyword)', { keyword: `%${keyword}%` })
                    .orWhere('LOWER(product.productCode) LIKE LOWER(:keyword)', { keyword: `%${keyword}%` })
                    .orWhere('LOWER(product.description) LIKE LOWER(:keyword)', { keyword: `%${keyword}%` });
            }));
            const { limit, page } = params;
            const sortOptions = params.sortOptions || [];
            if (sortOptions.length > 0) {
                sortOptions.forEach((option, index) => {
                    if (option.field.includes('.')) {
                        const [relation, field] = option.field.split('.');
                        if (index === 0) {
                            queryBuilder.orderBy(`${relation}.${field}`, option.order);
                        }
                        else {
                            queryBuilder.addOrderBy(`${relation}.${field}`, option.order);
                        }
                    }
                    else {
                        if (index === 0) {
                            queryBuilder.orderBy(`product.${option.field}`, option.order);
                        }
                        else {
                            queryBuilder.addOrderBy(`product.${option.field}`, option.order);
                        }
                    }
                });
            }
            else {
                queryBuilder.orderBy('product.createdAt', 'DESC');
            }
            queryBuilder.skip((page - 1) * limit).take(limit);
            const [products, total] = await queryBuilder.getManyAndCount();
            const productDtos = products.map(product => this.toDto(product));
            return { data: productDtos, total };
        }
        catch (error) {
            this.logger.error(`Lỗi khi tìm kiếm sản phẩm: ${error.message}`, error.stack);
            throw new common_1.InternalServerErrorException(`Không thể tìm kiếm sản phẩm: ${error.message}`);
        }
    }
    async findDeleted(params) {
        try {
            this.logger.debug(`Đang tìm các sản phẩm đã bị xóa mềm với tham số: ${JSON.stringify(params)}`);
            const { limit, page, filter } = params;
            const requiredRelations = ['category', 'creator', 'updater', 'deleter'];
            let relationsArray = [...requiredRelations];
            if (params.relationsArray && params.relationsArray.length > 0) {
                params.relationsArray.forEach(relation => {
                    if (!relationsArray.includes(relation)) {
                        relationsArray.push(relation);
                    }
                });
            }
            const validatedRelations = this.validateRelations(relationsArray);
            const queryBuilder = this.ecomProductRepository.createQueryBuilder('product')
                .where('product.isDeleted = :isDeleted', { isDeleted: true });
            if (filter) {
                const filters = filter.split(',');
                const filterGroups = {};
                filters.forEach(filterItem => {
                    const [field, value] = filterItem.split(':');
                    if (field && value) {
                        if (!filterGroups[field]) {
                            filterGroups[field] = [];
                        }
                        filterGroups[field].push(value);
                    }
                });
                Object.entries(filterGroups).forEach(([field, values]) => {
                    if (values.length === 1) {
                        queryBuilder.andWhere(`product.${field} = :${field}`, { [field]: values[0] });
                    }
                    else {
                        queryBuilder.andWhere(`product.${field} IN (:...${field})`, { [field]: values });
                    }
                });
            }
            if (validatedRelations.length > 0) {
                validatedRelations.forEach(relation => {
                    queryBuilder.leftJoinAndSelect(`product.${relation}`, relation);
                });
            }
            const sortOptions = params.sortOptions || [];
            if (sortOptions.length > 0) {
                sortOptions.forEach((option, index) => {
                    if (index === 0) {
                        queryBuilder.orderBy(`product.${option.field}`, option.order);
                    }
                    else {
                        queryBuilder.addOrderBy(`product.${option.field}`, option.order);
                    }
                });
            }
            else {
                queryBuilder.orderBy('product.deletedAt', 'DESC');
            }
            queryBuilder.skip((page - 1) * limit).take(limit);
            const [products, total] = await queryBuilder.getManyAndCount();
            const productDtos = products.map(product => this.toDto(product));
            return { data: productDtos, total };
        }
        catch (error) {
            this.logger.error(`Lỗi khi tìm các sản phẩm đã bị xóa mềm: ${error.message}`, error.stack);
            throw new common_1.InternalServerErrorException(`Không thể tìm các sản phẩm đã bị xóa mềm: ${error.message}`);
        }
    }
    async exists(id) {
        try {
            const count = await this.ecomProductRepository.count({
                where: { id, isDeleted: false },
            });
            return count > 0;
        }
        catch (error) {
            this.logger.error(`Lỗi khi kiểm tra sản phẩm tồn tại: ${error.message}`, error.stack);
            return false;
        }
    }
    async isProductCodeDuplicated(productCode, excludeId) {
        try {
            const queryBuilder = this.ecomProductRepository.createQueryBuilder('product')
                .where('product.productCode = :productCode', { productCode })
                .andWhere('product.isDeleted = :isDeleted', { isDeleted: false });
            if (excludeId) {
                queryBuilder.andWhere('product.id != :excludeId', { excludeId });
            }
            const count = await queryBuilder.getCount();
            return count > 0;
        }
        catch (error) {
            this.logger.error(`Lỗi khi kiểm tra mã sản phẩm trùng lặp: ${error.message}`, error.stack);
            return false;
        }
    }
    async count(filter) {
        try {
            this.logger.debug(`Đang đếm số lượng sản phẩm với bộ lọc: ${filter || 'không có'}`);
            const queryBuilder = this.ecomProductRepository.createQueryBuilder('product')
                .where('product.isDeleted = :isDeleted', { isDeleted: false });
            if (filter) {
                const filters = filter.split(',');
                const filterGroups = {};
                filters.forEach(filterItem => {
                    const [field, value] = filterItem.split(':');
                    if (field && value) {
                        if (!filterGroups[field]) {
                            filterGroups[field] = [];
                        }
                        filterGroups[field].push(value);
                    }
                });
                Object.entries(filterGroups).forEach(([field, values]) => {
                    if (values.length === 1) {
                        queryBuilder.andWhere(`product.${field} = :${field}`, { [field]: values[0] });
                    }
                    else {
                        queryBuilder.andWhere(`product.${field} IN (:...${field})`, { [field]: values });
                    }
                });
            }
            return queryBuilder.getCount();
        }
        catch (error) {
            this.logger.error(`Lỗi khi đếm số lượng sản phẩm: ${error.message}`, error.stack);
            throw new common_1.InternalServerErrorException(`Không thể đếm số lượng sản phẩm: ${error.message}`);
        }
    }
    async getStatistics() {
        try {
            this.logger.debug('Đang lấy thống kê sản phẩm');
            const baseQueryBuilder = this.ecomProductRepository.createQueryBuilder('product')
                .where('product.isDeleted = :isDeleted', { isDeleted: false });
            const totalQueryBuilder = baseQueryBuilder.clone();
            const activeQueryBuilder = baseQueryBuilder.clone().andWhere('product.isActive = :isActive', { isActive: true });
            const inactiveQueryBuilder = baseQueryBuilder.clone().andWhere('product.isActive = :isActive', { isActive: false });
            const [total, active, inactive] = await Promise.all([
                totalQueryBuilder.getCount(),
                activeQueryBuilder.getCount(),
                inactiveQueryBuilder.getCount()
            ]);
            return { total, active, inactive };
        }
        catch (error) {
            this.logger.error(`Lỗi khi lấy thống kê sản phẩm: ${error.message}`, error.stack);
            throw new common_1.InternalServerErrorException(`Không thể lấy thống kê sản phẩm: ${error.message}`);
        }
    }
};
exports.ReadEcomProductsService = ReadEcomProductsService;
exports.ReadEcomProductsService = ReadEcomProductsService = __decorate([
    (0, common_1.Injectable)()
], ReadEcomProductsService);
//# sourceMappingURL=read.ecom-products.service.js.map
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreateVnpayTransactions1703000000000 = void 0;
const typeorm_1 = require("typeorm");
class CreateVnpayTransactions1703000000000 {
    name = 'CreateVnpayTransactions1703000000000';
    async up(queryRunner) {
        await queryRunner.createTable(new typeorm_1.Table({
            name: 'vnpay_transactions',
            columns: [
                {
                    name: 'id',
                    type: 'uuid',
                    isPrimary: true,
                    generationStrategy: 'uuid',
                    default: 'uuid_generate_v4()',
                },
                {
                    name: 'merchant_txn_ref',
                    type: 'varchar',
                    length: '100',
                    isUnique: true,
                    isNullable: false,
                },
                {
                    name: 'vnpay_txn_ref',
                    type: 'varchar',
                    length: '100',
                    isUnique: true,
                    isNullable: true,
                },
                {
                    name: 'vnpay_txn_no',
                    type: 'varchar',
                    length: '100',
                    isNullable: true,
                },
                {
                    name: 'type',
                    type: 'enum',
                    enum: ['PAYMENT', 'REFUND', 'QUERY'],
                    default: "'PAYMENT'",
                },
                {
                    name: 'status',
                    type: 'enum',
                    enum: ['PENDING', 'SUCCESS', 'FAILED', 'CANCELLED', 'EXPIRED'],
                    default: "'PENDING'",
                },
                {
                    name: 'amount',
                    type: 'bigint',
                    isNullable: false,
                },
                {
                    name: 'currency',
                    type: 'varchar',
                    length: '10',
                    default: "'VND'",
                },
                {
                    name: 'order_info',
                    type: 'varchar',
                    length: '500',
                    isNullable: false,
                },
                {
                    name: 'bank_code',
                    type: 'varchar',
                    length: '50',
                    isNullable: true,
                },
                {
                    name: 'card_type',
                    type: 'varchar',
                    length: '50',
                    isNullable: true,
                },
                {
                    name: 'vnpay_response_code',
                    type: 'varchar',
                    length: '10',
                    isNullable: true,
                },
                {
                    name: 'vnpay_transaction_status',
                    type: 'varchar',
                    length: '10',
                    isNullable: true,
                },
                {
                    name: 'vnpay_pay_date',
                    type: 'varchar',
                    length: '20',
                    isNullable: true,
                },
                {
                    name: 'client_ip',
                    type: 'varchar',
                    length: '50',
                    isNullable: false,
                },
                {
                    name: 'locale',
                    type: 'varchar',
                    length: '10',
                    default: "'vn'",
                },
                {
                    name: 'vnpay_request',
                    type: 'text',
                    isNullable: true,
                },
                {
                    name: 'vnpay_response',
                    type: 'text',
                    isNullable: true,
                },
                {
                    name: 'return_callback_data',
                    type: 'text',
                    isNullable: true,
                },
                {
                    name: 'ipn_callback_data',
                    type: 'text',
                    isNullable: true,
                },
                {
                    name: 'external_ref',
                    type: 'varchar',
                    length: '100',
                    isNullable: true,
                },
                {
                    name: 'external_metadata',
                    type: 'text',
                    isNullable: true,
                },
                {
                    name: 'error_message',
                    type: 'varchar',
                    length: '500',
                    isNullable: true,
                },
                {
                    name: 'retry_count',
                    type: 'int',
                    default: 0,
                },
                {
                    name: 'expires_at',
                    type: 'timestamp',
                    isNullable: true,
                },
                {
                    name: 'processed_at',
                    type: 'timestamp',
                    isNullable: true,
                },
                {
                    name: 'created_at',
                    type: 'timestamp',
                    default: 'CURRENT_TIMESTAMP',
                },
                {
                    name: 'updated_at',
                    type: 'timestamp',
                    default: 'CURRENT_TIMESTAMP',
                    onUpdate: 'CURRENT_TIMESTAMP',
                },
            ],
        }), true);
        await queryRunner.createIndex('vnpay_transactions', new typeorm_1.Index({
            name: 'IDX_vnpay_transactions_merchant_txn_ref',
            columnNames: ['merchant_txn_ref'],
            isUnique: true,
        }));
        await queryRunner.createIndex('vnpay_transactions', new typeorm_1.Index({
            name: 'IDX_vnpay_transactions_vnpay_txn_ref',
            columnNames: ['vnpay_txn_ref'],
            isUnique: true,
        }));
        await queryRunner.createIndex('vnpay_transactions', new typeorm_1.Index({
            name: 'IDX_vnpay_transactions_status',
            columnNames: ['status'],
        }));
        await queryRunner.createIndex('vnpay_transactions', new typeorm_1.Index({
            name: 'IDX_vnpay_transactions_external_ref',
            columnNames: ['external_ref'],
        }));
        await queryRunner.createIndex('vnpay_transactions', new typeorm_1.Index({
            name: 'IDX_vnpay_transactions_created_at',
            columnNames: ['created_at'],
        }));
        await queryRunner.createIndex('vnpay_transactions', new typeorm_1.Index({
            name: 'IDX_vnpay_transactions_processed_at',
            columnNames: ['processed_at'],
        }));
        await queryRunner.createIndex('vnpay_transactions', new typeorm_1.Index({
            name: 'IDX_vnpay_transactions_expires_at',
            columnNames: ['expires_at'],
        }));
        await queryRunner.createIndex('vnpay_transactions', new typeorm_1.Index({
            name: 'IDX_vnpay_transactions_external_ref_status',
            columnNames: ['external_ref', 'status'],
        }));
        await queryRunner.createIndex('vnpay_transactions', new typeorm_1.Index({
            name: 'IDX_vnpay_transactions_status_created_at',
            columnNames: ['status', 'created_at'],
        }));
        await queryRunner.createIndex('vnpay_transactions', new typeorm_1.Index({
            name: 'IDX_vnpay_transactions_type_status',
            columnNames: ['type', 'status'],
        }));
    }
    async down(queryRunner) {
        await queryRunner.dropIndex('vnpay_transactions', 'IDX_vnpay_transactions_type_status');
        await queryRunner.dropIndex('vnpay_transactions', 'IDX_vnpay_transactions_status_created_at');
        await queryRunner.dropIndex('vnpay_transactions', 'IDX_vnpay_transactions_external_ref_status');
        await queryRunner.dropIndex('vnpay_transactions', 'IDX_vnpay_transactions_expires_at');
        await queryRunner.dropIndex('vnpay_transactions', 'IDX_vnpay_transactions_processed_at');
        await queryRunner.dropIndex('vnpay_transactions', 'IDX_vnpay_transactions_created_at');
        await queryRunner.dropIndex('vnpay_transactions', 'IDX_vnpay_transactions_external_ref');
        await queryRunner.dropIndex('vnpay_transactions', 'IDX_vnpay_transactions_status');
        await queryRunner.dropIndex('vnpay_transactions', 'IDX_vnpay_transactions_vnpay_txn_ref');
        await queryRunner.dropIndex('vnpay_transactions', 'IDX_vnpay_transactions_merchant_txn_ref');
        await queryRunner.dropTable('vnpay_transactions');
    }
}
exports.CreateVnpayTransactions1703000000000 = CreateVnpayTransactions1703000000000;
//# sourceMappingURL=create-vnpay-transactions.migration.js.map
"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.OrderBookDto = void 0;
const openapi = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
const swagger_1 = require("@nestjs/swagger");
const order_type_enum_1 = require("../enums/order-type.enum");
const order_status_enum_1 = require("../enums/order-status.enum");
const business_type_enum_1 = require("../enums/business-type.enum");
const approve_status_enum_1 = require("../enums/approve-status.enum");
const base_dto_1 = require("../../common/dto/base.dto");
const user_entity_1 = require("../../users/entities/user.entity");
const class_transformer_1 = require("class-transformer");
const order_book_detail_dto_1 = require("./order-book-detail.dto");
class OrderBookDto extends base_dto_1.BaseDto {
    userId;
    orderType;
    status;
    businessType;
    totalPrice;
    depositPrice;
    storageFee;
    settlementPrice;
    totalPriceFinal;
    contractNumber;
    settlementDeadline;
    settlementAt;
    approveStatus;
    approvedAt;
    user;
    details;
    creator;
    updater;
    static _OPENAPI_METADATA_FACTORY() {
        return { userId: { required: true, type: () => String }, orderType: { required: true, enum: require("../enums/order-type.enum").OrderType }, status: { required: true, enum: require("../enums/order-status.enum").OrderStatus }, businessType: { required: true, enum: require("../enums/business-type.enum").BusinessType }, totalPrice: { required: true, type: () => Number }, depositPrice: { required: true, type: () => Number }, storageFee: { required: true, type: () => Number }, settlementPrice: { required: true, type: () => Number }, totalPriceFinal: { required: true, type: () => Number }, contractNumber: { required: true, type: () => String }, settlementDeadline: { required: true, type: () => Date }, settlementAt: { required: true, type: () => Date }, approveStatus: { required: true, enum: require("../enums/approve-status.enum").ApproveStatus }, approvedAt: { required: true, type: () => Date }, user: { required: true, type: () => require("../../users/entities/user.entity").User }, details: { required: true, type: () => [require("./order-book-detail.dto").OrderBookDetailDto] }, creator: { required: true, type: () => require("../../users/entities/user.entity").User }, updater: { required: true, type: () => require("../../users/entities/user.entity").User } };
    }
}
exports.OrderBookDto = OrderBookDto;
__decorate([
    (0, class_transformer_1.Expose)(),
    (0, swagger_1.ApiProperty)({
        description: 'ID người dùng',
        example: '550e8400-e29b-41d4-a716-446655440000',
    }),
    (0, class_validator_1.IsNotEmpty)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], OrderBookDto.prototype, "userId", void 0);
__decorate([
    (0, class_transformer_1.Expose)(),
    (0, swagger_1.ApiProperty)({
        description: 'Loại lệnh (mua/bán)',
        enum: order_type_enum_1.OrderType,
        example: order_type_enum_1.OrderType.BUY,
    }),
    (0, class_validator_1.IsNotEmpty)(),
    (0, class_validator_1.IsEnum)(order_type_enum_1.OrderType),
    __metadata("design:type", String)
], OrderBookDto.prototype, "orderType", void 0);
__decorate([
    (0, class_transformer_1.Expose)(),
    (0, swagger_1.ApiProperty)({
        description: 'Trạng thái lệnh (mặc định là đã đặt cọc)',
        enum: order_status_enum_1.OrderStatus,
        example: order_status_enum_1.OrderStatus.DEPOSITED,
    }),
    (0, class_validator_1.IsNotEmpty)(),
    (0, class_validator_1.IsEnum)(order_status_enum_1.OrderStatus),
    __metadata("design:type", String)
], OrderBookDto.prototype, "status", void 0);
__decorate([
    (0, class_transformer_1.Expose)(),
    (0, swagger_1.ApiProperty)({
        description: 'Loại hình giao dịch',
        enum: business_type_enum_1.BusinessType,
        example: business_type_enum_1.BusinessType.NORMAL,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)(business_type_enum_1.BusinessType),
    __metadata("design:type", String)
], OrderBookDto.prototype, "businessType", void 0);
__decorate([
    (0, class_transformer_1.Expose)(),
    (0, swagger_1.ApiProperty)({ description: 'Tổng tiền đơn hàng', example: '5000000.00' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsDecimal)({ decimal_digits: '2' }),
    __metadata("design:type", Number)
], OrderBookDto.prototype, "totalPrice", void 0);
__decorate([
    (0, class_transformer_1.Expose)(),
    (0, swagger_1.ApiProperty)({
        description: 'Tổng tiền cọc',
        example: '1000000.00',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsDecimal)({ decimal_digits: '2' }),
    __metadata("design:type", Number)
], OrderBookDto.prototype, "depositPrice", void 0);
__decorate([
    (0, class_transformer_1.Expose)(),
    (0, swagger_1.ApiProperty)({
        description: 'Phí lưu kho',
        example: '50000.00',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsDecimal)({ decimal_digits: '2' }),
    __metadata("design:type", Number)
], OrderBookDto.prototype, "storageFee", void 0);
__decorate([
    (0, class_transformer_1.Expose)(),
    (0, swagger_1.ApiProperty)({
        description: 'Tổng tiền tất toán',
        example: '4000000.00',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsDecimal)({ decimal_digits: '2' }),
    __metadata("design:type", Number)
], OrderBookDto.prototype, "settlementPrice", void 0);
__decorate([
    (0, class_transformer_1.Expose)(),
    (0, swagger_1.ApiProperty)({ description: 'Tổng tiền cuối', example: '5000000.00' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsDecimal)({ decimal_digits: '2' }),
    __metadata("design:type", Number)
], OrderBookDto.prototype, "totalPriceFinal", void 0);
__decorate([
    (0, class_transformer_1.Expose)(),
    (0, swagger_1.ApiProperty)({
        description: 'Số hợp đồng',
        example: 'HD-2023-001',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], OrderBookDto.prototype, "contractNumber", void 0);
__decorate([
    (0, class_transformer_1.Expose)(),
    (0, swagger_1.ApiProperty)({
        description: 'Hạn chót tất toán',
        example: '2023-12-31T23:59:59Z',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsDate)(),
    __metadata("design:type", Date)
], OrderBookDto.prototype, "settlementDeadline", void 0);
__decorate([
    (0, class_transformer_1.Expose)(),
    (0, swagger_1.ApiProperty)({
        description: 'Thời gian tất toán',
        example: '2023-12-15T10:30:00Z',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsDate)(),
    __metadata("design:type", Date)
], OrderBookDto.prototype, "settlementAt", void 0);
__decorate([
    (0, class_transformer_1.Expose)(),
    (0, swagger_1.ApiProperty)({
        description: 'Trạng thái phê duyệt',
        enum: approve_status_enum_1.ApproveStatus,
        example: approve_status_enum_1.ApproveStatus.PENDING,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)(approve_status_enum_1.ApproveStatus),
    __metadata("design:type", String)
], OrderBookDto.prototype, "approveStatus", void 0);
__decorate([
    (0, class_transformer_1.Expose)(),
    (0, swagger_1.ApiProperty)({
        description: 'Thời gian phê duyệt',
        example: '2023-12-15T10:30:00Z',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsDate)(),
    __metadata("design:type", Date)
], OrderBookDto.prototype, "approvedAt", void 0);
__decorate([
    (0, class_transformer_1.Expose)(),
    (0, swagger_1.ApiProperty)({
        type: () => user_entity_1.User,
        description: 'Người dùng liên quan đến đơn hàng',
    }),
    __metadata("design:type", user_entity_1.User)
], OrderBookDto.prototype, "user", void 0);
__decorate([
    (0, class_transformer_1.Expose)(),
    (0, swagger_1.ApiProperty)({
        type: () => order_book_detail_dto_1.OrderBookDetailDto,
        isArray: true,
        description: 'Chi tiết các sản phẩm trong đơn hàng',
    }),
    __metadata("design:type", Array)
], OrderBookDto.prototype, "details", void 0);
__decorate([
    (0, class_transformer_1.Expose)(),
    (0, swagger_1.ApiProperty)({
        type: () => user_entity_1.User,
        description: 'Người tạo',
    }),
    __metadata("design:type", user_entity_1.User)
], OrderBookDto.prototype, "creator", void 0);
__decorate([
    (0, class_transformer_1.Expose)(),
    (0, swagger_1.ApiProperty)({
        type: () => user_entity_1.User,
        description: 'Người cập nhật',
    }),
    __metadata("design:type", user_entity_1.User)
], OrderBookDto.prototype, "updater", void 0);
//# sourceMappingURL=order-book.dto.js.map
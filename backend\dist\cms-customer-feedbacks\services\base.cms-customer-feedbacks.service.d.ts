import { Logger } from '@nestjs/common';
import { Repository, DataSource, FindOptionsWhere } from 'typeorm';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { CmsCustomerFeedbacks } from '../entity/cms-customer-feedbacks.entity';
import { CmsCustomerFeedbackDto } from '../dto/cms-customer-feedback.dto';
export declare class BaseCmsCustomerFeedbacksService {
    protected readonly feedbackRepository: Repository<CmsCustomerFeedbacks>;
    protected readonly dataSource: DataSource;
    protected readonly eventEmitter: EventEmitter2;
    protected readonly logger: Logger;
    protected readonly EVENT_FEEDBACK_CREATED = "cms-customer-feedback.created";
    protected readonly EVENT_FEEDBACK_UPDATED = "cms-customer-feedback.updated";
    protected readonly EVENT_FEEDBACK_DELETED = "cms-customer-feedback.deleted";
    protected readonly EVENT_FEEDBACK_APPROVED = "cms-customer-feedback.approved";
    protected readonly EVENT_FEEDBACK_REJECTED = "cms-customer-feedback.rejected";
    protected readonly validRelations: string[];
    constructor(feedbackRepository: Repository<CmsCustomerFeedbacks>, dataSource: DataSource, eventEmitter: EventEmitter2);
    protected validateRelations(relations: string[]): string[];
    protected buildWhereClause(filter?: string): FindOptionsWhere<CmsCustomerFeedbacks> | FindOptionsWhere<CmsCustomerFeedbacks>[];
    protected findById(id: string, relations?: string[], throwError?: boolean): Promise<CmsCustomerFeedbacks | null>;
    protected findByBusinessCode(businessCode: string, throwError?: boolean): Promise<CmsCustomerFeedbacks | null>;
    protected toDto(feedback: CmsCustomerFeedbacks | null): CmsCustomerFeedbackDto | null;
    protected toDtos(feedbacks: CmsCustomerFeedbacks[]): CmsCustomerFeedbackDto[];
    protected isFeedbackApproved(feedback: CmsCustomerFeedbacks): boolean;
    protected isFeedbackPending(feedback: CmsCustomerFeedbacks): boolean;
    protected isFeedbackRejected(feedback: CmsCustomerFeedbacks): boolean;
    protected getRatingText(rating?: number): string;
    protected getRatingColor(rating?: number): string;
    protected calculateAverageRating(feedbacks: CmsCustomerFeedbacks[]): number;
    protected categorizeByRating(feedbacks: CmsCustomerFeedbacks[]): Record<string, number>;
}

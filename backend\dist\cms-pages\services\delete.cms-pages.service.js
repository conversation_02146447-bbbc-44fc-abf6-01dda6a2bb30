"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.DeleteCmsPagesService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const event_emitter_1 = require("@nestjs/event-emitter");
const typeorm_transactional_1 = require("typeorm-transactional");
const base_cms_pages_service_1 = require("./base.cms-pages.service");
const cms_pages_entity_1 = require("../entity/cms-pages.entity");
let DeleteCmsPagesService = class DeleteCmsPagesService extends base_cms_pages_service_1.BaseCmsPagesService {
    pageRepository;
    dataSource;
    eventEmitter;
    constructor(pageRepository, dataSource, eventEmitter) {
        super(pageRepository, dataSource, eventEmitter);
        this.pageRepository = pageRepository;
        this.dataSource = dataSource;
        this.eventEmitter = eventEmitter;
    }
    async softDelete(id, userId) {
        try {
            this.logger.debug(`Đang xóa mềm trang CMS với ID: ${id}`);
            const page = await this.findById(id, []);
            if (!page) {
                throw new common_1.NotFoundException(`Không tìm thấy trang với ID: ${id}`);
            }
            const oldData = this.toDto(page);
            page.isDeleted = true;
            page.deletedBy = userId;
            page.deletedAt = new Date();
            const deletedPage = await this.pageRepository.save(page);
            const pageDto = this.toDto(deletedPage);
            if (!pageDto) {
                throw new common_1.InternalServerErrorException('Không thể chuyển đổi entity thành DTO');
            }
            this.eventEmitter.emit(this.EVENT_PAGE_DELETED, {
                pageId: pageDto.id,
                userId,
                oldData,
                newData: pageDto,
            });
            return pageDto;
        }
        catch (error) {
            this.logger.error(`Lỗi khi xóa mềm trang CMS: ${error.message}`, error.stack);
            if (error instanceof common_1.BadRequestException || error instanceof common_1.NotFoundException) {
                throw error;
            }
            throw new common_1.InternalServerErrorException(`Không thể xóa trang CMS: ${error.message}`);
        }
    }
    async restore(id, userId) {
        try {
            this.logger.debug(`Đang khôi phục trang CMS với ID: ${id}`);
            const page = await this.pageRepository.findOne({
                where: { id, isDeleted: true },
            });
            if (!page) {
                throw new common_1.BadRequestException(`Không tìm thấy trang đã xóa với ID: ${id}`);
            }
            if (await this.isSlugExists(page.slug, id)) {
                const newSlug = await this.generateUniqueSlug(page.title, id);
                page.slug = newSlug;
            }
            const oldData = this.toDto(page);
            page.isDeleted = false;
            page.deletedBy = null;
            page.deletedAt = null;
            page.updatedBy = userId;
            const restoredPage = await this.pageRepository.save(page);
            const pageDto = this.toDto(restoredPage);
            if (!pageDto) {
                throw new common_1.InternalServerErrorException('Không thể chuyển đổi entity thành DTO');
            }
            this.eventEmitter.emit('cms-page.restored', {
                pageId: pageDto.id,
                userId,
                oldData,
                newData: pageDto,
            });
            return pageDto;
        }
        catch (error) {
            this.logger.error(`Lỗi khi khôi phục trang CMS: ${error.message}`, error.stack);
            if (error instanceof common_1.BadRequestException) {
                throw error;
            }
            throw new common_1.InternalServerErrorException(`Không thể khôi phục trang CMS: ${error.message}`);
        }
    }
    async remove(id) {
        try {
            this.logger.debug(`Đang xóa vĩnh viễn trang CMS với ID: ${id}`);
            const page = await this.pageRepository.findOne({
                where: { id },
            });
            if (!page) {
                throw new common_1.NotFoundException(`Không tìm thấy trang với ID: ${id}`);
            }
            const result = await this.pageRepository.delete(id);
            if (result.affected === 0) {
                throw new common_1.InternalServerErrorException(`Không thể xóa trang CMS với ID: ${id}`);
            }
            const affectedCount = result.affected ?? 0;
            return { affected: affectedCount };
        }
        catch (error) {
            this.logger.error(`Lỗi khi xóa vĩnh viễn trang CMS: ${error.message}`, error.stack);
            if (error instanceof common_1.BadRequestException || error instanceof common_1.NotFoundException) {
                throw error;
            }
            throw new common_1.InternalServerErrorException(`Không thể xóa vĩnh viễn trang CMS: ${error.message}`);
        }
    }
    async bulkSoftDelete(ids, userId) {
        try {
            this.logger.debug(`Đang xóa mềm ${ids.length} trang CMS`);
            const deletedPages = [];
            for (const id of ids) {
                try {
                    const page = await this.softDelete(id, userId);
                    if (page) {
                        deletedPages.push(page);
                    }
                }
                catch (error) {
                    this.logger.warn(`Không thể xóa trang với ID ${id}: ${error.message}`);
                }
            }
            return deletedPages;
        }
        catch (error) {
            this.logger.error(`Lỗi khi xóa mềm nhiều trang CMS: ${error.message}`, error.stack);
            throw new common_1.InternalServerErrorException(`Không thể xóa nhiều trang CMS: ${error.message}`);
        }
    }
    async bulkRestore(ids, userId) {
        try {
            this.logger.debug(`Đang khôi phục ${ids.length} trang CMS`);
            const restoredPages = [];
            for (const id of ids) {
                try {
                    const page = await this.restore(id, userId);
                    if (page) {
                        restoredPages.push(page);
                    }
                }
                catch (error) {
                    this.logger.warn(`Không thể khôi phục trang với ID ${id}: ${error.message}`);
                }
            }
            return restoredPages;
        }
        catch (error) {
            this.logger.error(`Lỗi khi khôi phục nhiều trang CMS: ${error.message}`, error.stack);
            throw new common_1.InternalServerErrorException(`Không thể khôi phục nhiều trang CMS: ${error.message}`);
        }
    }
    async bulkRemove(ids) {
        try {
            this.logger.debug(`Đang xóa vĩnh viễn ${ids.length} trang CMS`);
            let totalAffected = 0;
            for (const id of ids) {
                try {
                    const result = await this.remove(id);
                    totalAffected += result.affected;
                }
                catch (error) {
                    this.logger.warn(`Không thể xóa vĩnh viễn trang với ID ${id}: ${error.message}`);
                }
            }
            return { affected: totalAffected };
        }
        catch (error) {
            this.logger.error(`Lỗi khi xóa vĩnh viễn nhiều trang CMS: ${error.message}`, error.stack);
            throw new common_1.InternalServerErrorException(`Không thể xóa vĩnh viễn nhiều trang CMS: ${error.message}`);
        }
    }
};
exports.DeleteCmsPagesService = DeleteCmsPagesService;
__decorate([
    (0, typeorm_transactional_1.Transactional)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String]),
    __metadata("design:returntype", Promise)
], DeleteCmsPagesService.prototype, "softDelete", null);
__decorate([
    (0, typeorm_transactional_1.Transactional)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String]),
    __metadata("design:returntype", Promise)
], DeleteCmsPagesService.prototype, "restore", null);
__decorate([
    (0, typeorm_transactional_1.Transactional)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], DeleteCmsPagesService.prototype, "remove", null);
__decorate([
    (0, typeorm_transactional_1.Transactional)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Array, String]),
    __metadata("design:returntype", Promise)
], DeleteCmsPagesService.prototype, "bulkSoftDelete", null);
__decorate([
    (0, typeorm_transactional_1.Transactional)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Array, String]),
    __metadata("design:returntype", Promise)
], DeleteCmsPagesService.prototype, "bulkRestore", null);
__decorate([
    (0, typeorm_transactional_1.Transactional)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Array]),
    __metadata("design:returntype", Promise)
], DeleteCmsPagesService.prototype, "bulkRemove", null);
exports.DeleteCmsPagesService = DeleteCmsPagesService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(cms_pages_entity_1.CmsPages)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        typeorm_2.DataSource,
        event_emitter_1.EventEmitter2])
], DeleteCmsPagesService);
//# sourceMappingURL=delete.cms-pages.service.js.map
export declare enum WalletTransactionType {
    DEPOSIT = "DEPOSIT",
    WITHDRAWAL = "WITHDRAWAL",
    TRANSFER = "TRANSFER",
    PAYMENT = "PAYMENT"
}
export declare enum WalletTransactionStatus {
    PENDING = "PENDING",
    PROCESSING = "PROCESSING",
    COMPLETED = "COMPLETED",
    FAILED = "FAILED",
    CANCELLED = "CANCELLED"
}
interface CreateWalletTransactionDto {
    userId: number;
    type: WalletTransactionType;
    amount: number;
    status: WalletTransactionStatus;
    description?: string;
    metadata?: any;
}
interface UpdateWalletTransactionDto {
    status?: WalletTransactionStatus;
    completedAt?: Date;
    metadata?: any;
}
export declare class WalletTransactionService {
    private transactions;
    private nextId;
    create(data: CreateWalletTransactionDto): Promise<any>;
    update(id: number, data: UpdateWalletTransactionDto): Promise<any>;
    findById(id: number): Promise<any>;
    findByUserId(userId: number): Promise<any[]>;
}
export {};

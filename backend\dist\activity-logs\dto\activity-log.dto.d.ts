import { User } from '../../users/entities/user.entity';
export declare class ActivityLogDto {
    id: string;
    userId: string | null;
    action: string;
    module: string | null;
    description: string | null;
    ipAddress: string | null;
    userAgent: string | null;
    createdAt: Date;
    updatedAt: Date;
    createdBy: string | null;
    updatedBy: string | null;
    isDeleted: boolean;
    deletedBy: string | null;
    user?: User | null;
}

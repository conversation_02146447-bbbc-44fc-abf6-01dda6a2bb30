"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.EcomProductsUpdateController = void 0;
const openapi = require("@nestjs/swagger");
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const jwt_auth_guard_1 = require("../../common/guards/jwt-auth.guard");
const update_ecom_products_service_1 = require("../services/update.ecom-products.service");
const read_ecom_products_service_1 = require("../services/read.ecom-products.service");
const update_ecom_product_dto_1 = require("../dto/update-ecom-product.dto");
const class_validator_1 = require("class-validator");
const swagger_2 = require("@nestjs/swagger");
class UpdateProductStatusDto {
    isActive;
}
__decorate([
    (0, swagger_2.ApiProperty)({
        description: 'Trạng thái hoạt động của sản phẩm',
        example: true,
    }),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], UpdateProductStatusDto.prototype, "isActive", void 0);
const api_response_dto_1 = require("../../common/dto/api-response.dto");
const get_user_decorator_1 = require("../../common/decorators/get-user.decorator");
const roles_decorator_1 = require("../../common/decorators/roles.decorator");
const permissions_decorator_1 = require("../../common/decorators/permissions.decorator");
let EcomProductsUpdateController = class EcomProductsUpdateController {
    ecomProductsService;
    readEcomProductsService;
    constructor(ecomProductsService, readEcomProductsService) {
        this.ecomProductsService = ecomProductsService;
        this.readEcomProductsService = readEcomProductsService;
    }
    async updatePut(id, updateEcomProductDto, userId) {
        await this.readEcomProductsService.findById(id);
        const result = await this.ecomProductsService.update(id, updateEcomProductDto, userId);
        if (!result) {
            throw new common_1.NotFoundException(`Không thể cập nhật sản phẩm với ID: ${id}`);
        }
        return result;
    }
    async update(id, updateEcomProductDto, userId) {
        await this.readEcomProductsService.findById(id);
        const result = await this.ecomProductsService.update(id, updateEcomProductDto, userId);
        if (!result) {
            throw new common_1.NotFoundException(`Không thể cập nhật sản phẩm với ID: ${id}`);
        }
        return result;
    }
    async updateStatus(id, updateStatusDto, userId) {
        await this.readEcomProductsService.findById(id);
        const result = await this.ecomProductsService.update(id, { id, isActive: updateStatusDto.isActive }, userId);
        if (!result) {
            throw new common_1.NotFoundException(`Không thể cập nhật trạng thái sản phẩm với ID: ${id}`);
        }
        return result;
    }
    async bulkUpdate(updateEcomProductDtos, userId) {
        const updatedProducts = await this.ecomProductsService.bulkUpdate(updateEcomProductDtos, userId);
        let message = 'Các sản phẩm đã được cập nhật thành công.';
        if (updatedProducts.length === 0) {
            message = 'Không có sản phẩm nào được cập nhật.';
        }
        else if (updatedProducts.length < updateEcomProductDtos.length) {
            message = `Đã cập nhật ${updatedProducts.length}/${updateEcomProductDtos.length} sản phẩm. Một số ID không tồn tại.`;
        }
        return { data: updatedProducts, message };
    }
    async bulkUpdateAlternative(updateEcomProductDtos, userId) {
        const updatedProducts = await this.ecomProductsService.bulkUpdate(updateEcomProductDtos, userId);
        let message = 'Các sản phẩm đã được cập nhật thành công.';
        if (updatedProducts.length === 0) {
            message = 'Không có sản phẩm nào được cập nhật.';
        }
        else if (updatedProducts.length < updateEcomProductDtos.length) {
            message = `Đã cập nhật ${updatedProducts.length}/${updateEcomProductDtos.length} sản phẩm. Một số ID không tồn tại.`;
        }
        return { data: updatedProducts, message };
    }
    async restore(id, userId) {
        const result = await this.ecomProductsService.restore(id, userId);
        if (!result) {
            throw new common_1.NotFoundException(`Không thể khôi phục sản phẩm với ID: ${id}`);
        }
        return result;
    }
};
exports.EcomProductsUpdateController = EcomProductsUpdateController;
__decorate([
    (0, common_1.Put)(':id'),
    (0, roles_decorator_1.Roles)('ADMIN'),
    (0, permissions_decorator_1.Permissions)('ecom-product:update'),
    (0, swagger_1.ApiOperation)({ summary: 'Cập nhật sản phẩm (PUT)' }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Sản phẩm đã được cập nhật thành công.',
        type: api_response_dto_1.ApiResponseDto,
        schema: {
            properties: {
                data: { $ref: '#/components/schemas/EcomProductDto' },
            },
        },
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.NOT_FOUND,
        description: 'Không tìm thấy sản phẩm.',
        type: api_response_dto_1.ApiResponseDto,
        schema: { properties: { data: { type: 'object', nullable: true } } },
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.BAD_REQUEST,
        description: 'Dữ liệu đầu vào không hợp lệ.',
        type: api_response_dto_1.ApiResponseDto,
        schema: { properties: { data: { type: 'object', nullable: true } } },
    }),
    (0, swagger_1.ApiParam)({ name: 'id', type: String, description: 'ID của sản phẩm' }),
    (0, swagger_1.ApiBody)({ type: update_ecom_product_dto_1.UpdateEcomProductDto }),
    openapi.ApiResponse({ status: 200, type: require("../dto/ecom-product.dto").EcomProductDto }),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __param(1, (0, common_1.Body)()),
    __param(2, (0, get_user_decorator_1.GetUser)("id")),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, update_ecom_product_dto_1.UpdateEcomProductDto, String]),
    __metadata("design:returntype", Promise)
], EcomProductsUpdateController.prototype, "updatePut", null);
__decorate([
    (0, common_1.Patch)(':id'),
    (0, roles_decorator_1.Roles)('ADMIN'),
    (0, permissions_decorator_1.Permissions)('ecom-product:update'),
    (0, swagger_1.ApiOperation)({ summary: 'Cập nhật sản phẩm (PATCH)' }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Sản phẩm đã được cập nhật thành công.',
        type: api_response_dto_1.ApiResponseDto,
        schema: {
            properties: {
                data: { $ref: '#/components/schemas/EcomProductDto' },
            },
        },
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.NOT_FOUND,
        description: 'Không tìm thấy sản phẩm.',
        type: api_response_dto_1.ApiResponseDto,
        schema: { properties: { data: { type: 'object', nullable: true } } },
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.BAD_REQUEST,
        description: 'Dữ liệu đầu vào không hợp lệ.',
        type: api_response_dto_1.ApiResponseDto,
        schema: { properties: { data: { type: 'object', nullable: true } } },
    }),
    (0, swagger_1.ApiParam)({ name: 'id', type: String, description: 'ID của sản phẩm' }),
    (0, swagger_1.ApiBody)({ type: update_ecom_product_dto_1.UpdateEcomProductDto }),
    openapi.ApiResponse({ status: 200, type: require("../dto/ecom-product.dto").EcomProductDto }),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __param(1, (0, common_1.Body)()),
    __param(2, (0, get_user_decorator_1.GetUser)("id")),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, update_ecom_product_dto_1.UpdateEcomProductDto, String]),
    __metadata("design:returntype", Promise)
], EcomProductsUpdateController.prototype, "update", null);
__decorate([
    (0, common_1.Patch)(':id/status'),
    (0, roles_decorator_1.Roles)('ADMIN'),
    (0, permissions_decorator_1.Permissions)('ecom-product:update'),
    (0, swagger_1.ApiOperation)({ summary: 'Cập nhật trạng thái hoạt động của sản phẩm' }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Trạng thái sản phẩm đã được cập nhật thành công.',
        type: api_response_dto_1.ApiResponseDto,
        schema: {
            properties: {
                data: { $ref: '#/components/schemas/EcomProductDto' },
            },
        },
    }),
    (0, swagger_1.ApiParam)({ name: 'id', type: String, description: 'ID của sản phẩm' }),
    (0, swagger_1.ApiBody)({ type: UpdateProductStatusDto }),
    openapi.ApiResponse({ status: 200, type: require("../dto/ecom-product.dto").EcomProductDto }),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __param(1, (0, common_1.Body)()),
    __param(2, (0, get_user_decorator_1.GetUser)("id")),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, UpdateProductStatusDto, String]),
    __metadata("design:returntype", Promise)
], EcomProductsUpdateController.prototype, "updateStatus", null);
__decorate([
    (0, common_1.Patch)('bulk'),
    (0, roles_decorator_1.Roles)('ADMIN'),
    (0, permissions_decorator_1.Permissions)('ecom-product:update'),
    (0, swagger_1.ApiOperation)({ summary: 'Cập nhật nhiều sản phẩm cùng lúc' }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Các sản phẩm đã được cập nhật thành công.',
        type: api_response_dto_1.ApiResponseDto,
        schema: {
            properties: {
                data: {
                    type: 'array',
                    items: { $ref: '#/components/schemas/EcomProductDto' },
                },
            },
        },
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.BAD_REQUEST,
        description: 'Dữ liệu đầu vào không hợp lệ.',
        type: api_response_dto_1.ApiResponseDto,
        schema: { properties: { data: { type: 'object', nullable: true } } },
    }),
    (0, swagger_1.ApiBody)({ type: [update_ecom_product_dto_1.UpdateEcomProductDto] }),
    openapi.ApiResponse({ status: 200 }),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, get_user_decorator_1.GetUser)("id")),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Array, String]),
    __metadata("design:returntype", Promise)
], EcomProductsUpdateController.prototype, "bulkUpdate", null);
__decorate([
    (0, common_1.Post)('bulk-update'),
    (0, roles_decorator_1.Roles)('ADMIN'),
    (0, permissions_decorator_1.Permissions)('ecom-product:update'),
    (0, swagger_1.ApiOperation)({ summary: 'Cập nhật nhiều sản phẩm cùng lúc (thay thế cho PATCH /bulk)' }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Các sản phẩm đã được cập nhật thành công.',
        type: api_response_dto_1.ApiResponseDto,
        schema: {
            properties: {
                data: {
                    type: 'array',
                    items: { $ref: '#/components/schemas/EcomProductDto' },
                },
            },
        },
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.BAD_REQUEST,
        description: 'Dữ liệu đầu vào không hợp lệ.',
        type: api_response_dto_1.ApiResponseDto,
        schema: { properties: { data: { type: 'object', nullable: true } } },
    }),
    (0, swagger_1.ApiBody)({ type: [update_ecom_product_dto_1.UpdateEcomProductDto] }),
    openapi.ApiResponse({ status: 201 }),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, get_user_decorator_1.GetUser)("id")),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Array, String]),
    __metadata("design:returntype", Promise)
], EcomProductsUpdateController.prototype, "bulkUpdateAlternative", null);
__decorate([
    (0, common_1.Patch)('restore/:id'),
    (0, roles_decorator_1.Roles)('ADMIN'),
    (0, permissions_decorator_1.Permissions)('ecom-product:restore'),
    (0, swagger_1.ApiOperation)({ summary: 'Khôi phục sản phẩm đã bị xóa mềm' }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Sản phẩm đã được khôi phục thành công.',
        type: api_response_dto_1.ApiResponseDto,
        schema: {
            properties: {
                data: { $ref: '#/components/schemas/EcomProductDto' },
            },
        },
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.NOT_FOUND,
        description: 'Không tìm thấy sản phẩm.',
        type: api_response_dto_1.ApiResponseDto,
        schema: { properties: { data: { type: 'object', nullable: true } } },
    }),
    (0, swagger_1.ApiParam)({ name: 'id', type: String, description: 'ID của sản phẩm' }),
    openapi.ApiResponse({ status: 200, type: require("../dto/ecom-product.dto").EcomProductDto }),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __param(1, (0, get_user_decorator_1.GetUser)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String]),
    __metadata("design:returntype", Promise)
], EcomProductsUpdateController.prototype, "restore", null);
exports.EcomProductsUpdateController = EcomProductsUpdateController = __decorate([
    (0, swagger_1.ApiTags)('ecom-products'),
    (0, swagger_1.ApiBearerAuth)(),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, common_1.Controller)('ecom-products'),
    __metadata("design:paramtypes", [update_ecom_products_service_1.UpdateEcomProductsService,
        read_ecom_products_service_1.ReadEcomProductsService])
], EcomProductsUpdateController);
//# sourceMappingURL=ecom-products.update.controller.js.map
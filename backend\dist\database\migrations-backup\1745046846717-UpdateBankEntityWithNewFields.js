"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.UpdateBankEntityWithNewFields************* = void 0;
class UpdateBankEntityWithNewFields************* {
    name = 'UpdateBankEntityWithNewFields*************';
    async up(queryRunner) {
        await queryRunner.query(`ALTER TABLE "banks" ADD "brandName" character varying`);
        await queryRunner.query(`ALTER TABLE "banks" ADD "fullName" character varying`);
        await queryRunner.query(`ALTER TABLE "banks" ADD "shortName" character varying`);
        await queryRunner.query(`ALTER TABLE "banks" ADD "code" character varying`);
        await queryRunner.query(`ALTER TABLE "banks" ADD "bin" character varying`);
        await queryRunner.query(`ALTER TABLE "banks" ADD "logoPath" character varying`);
        await queryRunner.query(`ALTER TABLE "banks" ADD "icon" character varying`);
        await queryRunner.query(`UPDATE "banks" SET
            "brandName" = "bankName",
            "fullName" = "bankName",
            "shortName" = "bankName",
            "code" = "bankCode",
            "bin" = COALESCE("bankCode", '000000')
        `);
        await queryRunner.query(`ALTER TABLE "banks" ALTER COLUMN "brandName" SET NOT NULL`);
        await queryRunner.query(`ALTER TABLE "banks" ALTER COLUMN "fullName" SET NOT NULL`);
        await queryRunner.query(`ALTER TABLE "banks" ALTER COLUMN "shortName" SET NOT NULL`);
        await queryRunner.query(`ALTER TABLE "banks" ALTER COLUMN "code" SET NOT NULL`);
        await queryRunner.query(`ALTER TABLE "banks" ALTER COLUMN "bin" SET NOT NULL`);
        await queryRunner.query(`ALTER TABLE "banks" DROP COLUMN IF EXISTS "bankName"`);
        await queryRunner.query(`ALTER TABLE "banks" DROP COLUMN IF EXISTS "bankCode"`);
    }
    async down(queryRunner) {
        await queryRunner.query(`ALTER TABLE "banks" ADD "bankName" character varying`);
        await queryRunner.query(`ALTER TABLE "banks" ADD "bankCode" character varying`);
        await queryRunner.query(`UPDATE "banks" SET
            "bankName" = "brandName",
            "bankCode" = "code"
        `);
        await queryRunner.query(`ALTER TABLE "banks" ALTER COLUMN "bankName" SET NOT NULL`);
        await queryRunner.query(`ALTER TABLE "banks" ALTER COLUMN "bankCode" SET NOT NULL`);
        await queryRunner.query(`ALTER TABLE "banks" DROP COLUMN "brandName"`);
        await queryRunner.query(`ALTER TABLE "banks" DROP COLUMN "fullName"`);
        await queryRunner.query(`ALTER TABLE "banks" DROP COLUMN "shortName"`);
        await queryRunner.query(`ALTER TABLE "banks" DROP COLUMN "code"`);
        await queryRunner.query(`ALTER TABLE "banks" DROP COLUMN "bin"`);
        await queryRunner.query(`ALTER TABLE "banks" DROP COLUMN "logoPath"`);
        await queryRunner.query(`ALTER TABLE "banks" DROP COLUMN "icon"`);
    }
    async checkColumnExists(queryRunner, tableName, columnName) {
        const table = await queryRunner.getTable(tableName);
        return !!table?.findColumnByName(columnName);
    }
}
exports.UpdateBankEntityWithNewFields************* = UpdateBankEntityWithNewFields*************;
//# sourceMappingURL=*************-UpdateBankEntityWithNewFields.js.map
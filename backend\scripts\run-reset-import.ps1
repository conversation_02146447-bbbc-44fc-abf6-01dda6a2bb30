# Script PowerShell để reset và import lại toàn bộ ngân hàng
# Chạy từ thư mục backend: .\scripts\run-reset-import.ps1

param(
    [string]$BackendUrl = "http://localhost:3168",
    [switch]$Confirm,
    [switch]$DryRun,
    [switch]$Verbose
)

Write-Host "=== SCRIPT RESET VÀ IMPORT NGÂN HÀNG ===" -ForegroundColor Red
Write-Host "⚠️  CẢNH BÁO: Script này sẽ XÓA TẤT CẢ ngân hàng hiện có!" -ForegroundColor Yellow
Write-Host "Backend URL: $BackendUrl" -ForegroundColor Yellow

# Kiểm tra xác nhận
if (-not $Confirm -and -not $DryRun) {
    Write-Host "`n🔴 NGUY HIỂM: Thao tác này sẽ:" -ForegroundColor Red
    Write-Host "   1. XÓA TẤT CẢ ngân hàng hiện có trong database" -ForegroundColor Red
    Write-Host "   2. Import lại 59+ ngân hàng từ banklookup.net" -ForegroundColor Red
    Write-Host "   3. Không thể hoàn tác sau khi thực hiện" -ForegroundColor Red
    
    $confirmation = Read-Host "`nBạn có CHẮC CHẮN muốn tiếp tục? Gõ 'YES' để xác nhận"
    if ($confirmation -ne "YES") {
        Write-Host "❌ Đã hủy thao tác" -ForegroundColor Red
        exit 0
    }
}

if ($DryRun) {
    Write-Host "🔍 DRY RUN MODE: Chỉ kiểm tra, không thực hiện thay đổi" -ForegroundColor Blue
}

# Kiểm tra Node.js
if (-not (Get-Command node -ErrorAction SilentlyContinue)) {
    Write-Host "❌ Node.js không được cài đặt" -ForegroundColor Red
    exit 1
}

# Kiểm tra thư mục
if (-not (Test-Path "package.json")) {
    Write-Host "❌ Vui lòng chạy từ thư mục backend" -ForegroundColor Red
    exit 1
}

# Tạo thư mục output
$outputDir = "scripts/output"
if (-not (Test-Path $outputDir)) {
    New-Item -ItemType Directory -Path $outputDir -Force | Out-Null
}

# Kiểm tra axios
Write-Host "📦 Kiểm tra dependencies..." -ForegroundColor Blue
$axiosInstalled = npm list axios --depth=0 2>$null
if ($LASTEXITCODE -ne 0) {
    Write-Host "📦 Cài đặt axios..." -ForegroundColor Blue
    npm install axios
    if ($LASTEXITCODE -ne 0) {
        Write-Host "❌ Không thể cài đặt axios" -ForegroundColor Red
        exit 1
    }
}

# Kiểm tra backend
Write-Host "🔍 Kiểm tra backend..." -ForegroundColor Blue
try {
    $response = Invoke-WebRequest -Uri "$BackendUrl/api/v1/health" -Method GET -TimeoutSec 5 -ErrorAction Stop
    Write-Host "✅ Backend đang hoạt động" -ForegroundColor Green
} catch {
    Write-Host "❌ Không thể kết nối đến backend tại $BackendUrl" -ForegroundColor Red
    Write-Host "   Vui lòng đảm bảo backend đang chạy" -ForegroundColor Red
    exit 1
}

# Backup thông tin hiện tại (nếu không phải dry run)
if (-not $DryRun) {
    Write-Host "💾 Tạo backup thông tin hiện tại..." -ForegroundColor Blue
    $backupScript = @"
const axios = require('axios');
const fs = require('fs');

async function backup() {
    try {
        // Đăng nhập
        const loginResponse = await axios.post('$BackendUrl/api/v1/auth/login', {
            identity: '<EMAIL>',
            password: 'adminX@123'
        });
        
        const token = loginResponse.data?.data?.access_token;
        if (!token) throw new Error('Không thể đăng nhập');
        
        // Lấy danh sách ngân hàng hiện tại
        let allBanks = [];
        let page = 1;
        let hasMore = true;
        
        while (hasMore) {
            const response = await axios.get('$BackendUrl/api/v1/banks', {
                params: { page: page, limit: 100 },
                headers: { 'Authorization': 'Bearer ' + token }
            });
            
            const banks = response.data?.data || [];
            allBanks = allBanks.concat(banks);
            
            if (banks.length < 100) {
                hasMore = false;
            } else {
                page++;
            }
        }
        
        // Lưu backup
        const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
        const backupFile = './scripts/output/banks-backup-' + timestamp + '.json';
        fs.writeFileSync(backupFile, JSON.stringify(allBanks, null, 2));
        
        console.log('✅ Đã backup ' + allBanks.length + ' ngân hàng vào: ' + backupFile);
        
    } catch (error) {
        console.log('❌ Lỗi backup: ' + error.message);
        process.exit(1);
    }
}

backup();
"@
    
    Set-Content "temp-backup.js" -Value $backupScript -Encoding UTF8
    node temp-backup.js
    Remove-Item "temp-backup.js" -Force
    
    if ($LASTEXITCODE -ne 0) {
        Write-Host "❌ Backup thất bại" -ForegroundColor Red
        exit 1
    }
}

# Chạy script chính
Write-Host "`n🚀 Bắt đầu reset và import..." -ForegroundColor Green
Write-Host "📝 Log chi tiết: scripts/output/reset-import-banks.log" -ForegroundColor Blue

$startTime = Get-Date

if ($DryRun) {
    Write-Host "🔍 DRY RUN: Chỉ kiểm tra dữ liệu từ API..." -ForegroundColor Blue
    
    $dryRunScript = @"
const axios = require('axios');

async function dryRun() {
    try {
        console.log('🔍 Kiểm tra API banklookup.net...');
        const response = await axios.get('https://api.banklookup.net/bank/list');
        
        if (response.data.success && response.data.data) {
            console.log('✅ API hoạt động tốt');
            console.log('📊 Số ngân hàng có sẵn: ' + response.data.data.length);
            console.log('📊 Ngân hàng hỗ trợ lookup: ' + response.data.data.filter(b => b.lookup_supported === 1).length);
            console.log('📊 Ngân hàng có SWIFT: ' + response.data.data.filter(b => b.swift_code).length);
        } else {
            throw new Error('API trả về dữ liệu không hợp lệ');
        }
        
        console.log('🔍 Kiểm tra backend...');
        const healthResponse = await axios.get('$BackendUrl/api/v1/health');
        console.log('✅ Backend hoạt động tốt');
        
    } catch (error) {
        console.log('❌ Lỗi: ' + error.message);
        process.exit(1);
    }
}

dryRun();
"@
    
    Set-Content "temp-dryrun.js" -Value $dryRunScript -Encoding UTF8
    node temp-dryrun.js
    Remove-Item "temp-dryrun.js" -Force
    
} else {
    # Chạy script thực
    if ($Verbose) {
        node scripts/reset-and-import-banks.js
    } else {
        node scripts/reset-and-import-banks.js 2>&1 | Tee-Object -FilePath "scripts/output/console-reset.log"
    }
}

$exitCode = $LASTEXITCODE
$endTime = Get-Date
$duration = $endTime - $startTime

# Hiển thị kết quả
Write-Host "`n=== KẾT QUẢ ===" -ForegroundColor Green
Write-Host "⏱️  Thời gian thực hiện: $($duration.ToString('mm\:ss'))" -ForegroundColor Blue

if ($exitCode -eq 0) {
    Write-Host "✅ Script hoàn thành thành công!" -ForegroundColor Green
    
    # Hiển thị files đã tạo
    Write-Host "`n📁 Files đã tạo:" -ForegroundColor Blue
    Get-ChildItem "scripts/output" -Filter "*$(Get-Date -Format 'yyyy-MM-dd')*" | ForEach-Object {
        $size = [math]::Round($_.Length / 1KB, 2)
        Write-Host "   📄 $($_.Name) ($size KB)" -ForegroundColor White
    }
    
    # Hiển thị log cuối
    $logFile = "scripts/output/reset-import-banks.log"
    if (Test-Path $logFile) {
        Write-Host "`n📋 Log cuối cùng:" -ForegroundColor Blue
        Get-Content $logFile | Select-Object -Last 5 | ForEach-Object {
            if ($_ -match "SUCCESS|✅") {
                Write-Host "   $_" -ForegroundColor Green
            } elseif ($_ -match "ERROR|❌") {
                Write-Host "   $_" -ForegroundColor Red
            } else {
                Write-Host "   $_" -ForegroundColor White
            }
        }
    }
    
} else {
    Write-Host "❌ Script gặp lỗi (Exit code: $exitCode)" -ForegroundColor Red
    
    # Hiển thị log lỗi
    $logFile = "scripts/output/reset-import-banks.log"
    if (Test-Path $logFile) {
        Write-Host "`n📋 Log lỗi:" -ForegroundColor Yellow
        Get-Content $logFile | Select-Object -Last 10 | ForEach-Object {
            if ($_ -match "ERROR|❌") {
                Write-Host "   $_" -ForegroundColor Red
            } else {
                Write-Host "   $_" -ForegroundColor White
            }
        }
    }
}

Write-Host "`n🔍 Để xem log đầy đủ: Get-Content scripts/output/reset-import-banks.log" -ForegroundColor Blue

exit $exitCode

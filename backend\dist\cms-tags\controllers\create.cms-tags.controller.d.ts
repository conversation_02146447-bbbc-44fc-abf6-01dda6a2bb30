import { CreateCmsTagsService } from '../services/create.cms-tags.service';
import { CmsTagDto } from '../dto/cms-tag.dto';
import { CreateCmsTagDto } from '../dto/create.cms-tag.dto';
export declare class CreateCmsTagsController {
    private readonly cmsTagsService;
    constructor(cmsTagsService: CreateCmsTagsService);
    create(createCmsTagDto: CreateCmsTagDto, userId: string): Promise<CmsTagDto>;
    bulkCreate(createCmsTagDtos: CreateCmsTagDto[], userId: string): Promise<CmsTagDto[]>;
    createFromName(name: string, userId: string): Promise<CmsTagDto>;
    findOrCreate(name: string, userId: string): Promise<CmsTagDto>;
    createFromNames(names: string[], userId: string): Promise<CmsTagDto[]>;
}

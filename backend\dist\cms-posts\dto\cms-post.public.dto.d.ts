import { CmsPostType, CmsPostStatus } from '../entity/cms-posts.entity';
import { CmsCategoryDto } from '../../cms-categories/dto/cms-category.dto';
export declare class CmsPostPublicDto {
    id: string;
    businessCode: string;
    postType: CmsPostType;
    title: string;
    slug: string;
    excerpt?: string | null;
    content: string;
    featuredImageUrl?: string | null;
    status: CmsPostStatus;
    publishedAt?: Date | null;
    eventStartDate?: Date | null;
    eventEndDate?: Date | null;
    eventLocation?: string | null;
    viewCount: number;
    metaTitle?: string | null;
    metaDescription?: string | null;
    metaKeywords?: string | null;
    allowComments: boolean;
    category?: CmsCategoryDto | null;
    createdAt: Date;
    updatedAt: Date;
    canDisplayPublicly: boolean;
    hasFeaturedImage: boolean;
    hasExcerpt: boolean;
    hasCategory: boolean;
    postTypeDisplayName: string;
    statusDisplayName: string;
    key: string;
    url: string;
    isEvent: boolean;
    isEventOngoing: boolean;
    isEventUpcoming: boolean;
    isEventPast: boolean;
    estimatedReadingTime: number;
    summary: string;
}

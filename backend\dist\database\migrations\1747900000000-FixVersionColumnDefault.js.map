{"version": 3, "file": "1747900000000-FixVersionColumnDefault.js", "sourceRoot": "", "sources": ["../../../src/database/migrations/1747900000000-FixVersionColumnDefault.ts"], "names": [], "mappings": ";;;AAEA,MAAa,oCAAoC;IAEvC,MAAM,GAAG;QACf,OAAO;QACP,SAAS;QACT,gBAAgB;QAChB,cAAc;QACd,qBAAqB;QACrB,YAAY;QACZ,mBAAmB;QACnB,iBAAiB;QACjB,QAAQ;QACR,cAAc;QACd,cAAc;QACd,kBAAkB;QAClB,QAAQ;QACR,cAAc;QACd,aAAa;QACb,OAAO;QACP,OAAO;QACP,cAAc;QACd,eAAe;QACf,gBAAgB;QAChB,UAAU;QACV,aAAa;QACb,aAAa;QACb,oBAAoB;QACpB,eAAe;QACf,yBAAyB;QACzB,eAAe;KAChB,CAAC;IAEK,KAAK,CAAC,EAAE,CAAC,WAAwB;QAGtC,KAAK,MAAM,KAAK,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC;YAChC,IAAI,CAAC;gBAEH,MAAM,WAAW,GAAG,MAAM,WAAW,CAAC,KAAK,CAAC;;;;gCAIpB,KAAK;;SAE5B,CAAC,CAAC;gBAEH,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,MAAM,EAAE,CAAC;oBAE3B,SAAS;gBACX,CAAC;gBAGD,MAAM,YAAY,GAAG,MAAM,WAAW,CAAC,KAAK,CAAC;;;;gCAIrB,KAAK;;;SAG5B,CAAC,CAAC;gBAEH,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,MAAM,EAAE,CAAC;oBAI5B,MAAM,WAAW,CAAC,KAAK,CAAC;2BACP,KAAK;;WAErB,CAAC,CAAC;gBAGL,CAAC;qBAAM,CAAC;oBAIN,MAAM,WAAW,CAAC,KAAK,CAAC;sBACZ,KAAK;;;WAGhB,CAAC,CAAC;oBAGH,MAAM,WAAW,CAAC,KAAK,CAAC;2BACP,KAAK;;WAErB,CAAC,CAAC;oBAGH,MAAM,WAAW,CAAC,KAAK,CAAC;2BACP,KAAK;;WAErB,CAAC,CAAC;gBAGL,CAAC;YACH,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,OAAO,CAAC,KAAK,CAAC,wBAAwB,KAAK,KAAK,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;YAEnE,CAAC;QACH,CAAC;IAGH,CAAC;IAEM,KAAK,CAAC,IAAI,CAAC,WAAwB;QAGxC,KAAK,MAAM,KAAK,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC;YAChC,IAAI,CAAC;gBAEH,MAAM,WAAW,GAAG,MAAM,WAAW,CAAC,KAAK,CAAC;;;;gCAIpB,KAAK;;SAE5B,CAAC,CAAC;gBAEH,IAAI,WAAW,CAAC,CAAC,CAAC,CAAC,MAAM,EAAE,CAAC;oBAE1B,MAAM,WAAW,CAAC,KAAK,CAAC;2BACP,KAAK;;WAErB,CAAC,CAAC;gBAGL,CAAC;YACH,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,OAAO,CAAC,KAAK,CAAC,2BAA2B,KAAK,KAAK,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;YACtE,CAAC;QACH,CAAC;IAGH,CAAC;CACF;AAtID,oFAsIC"}
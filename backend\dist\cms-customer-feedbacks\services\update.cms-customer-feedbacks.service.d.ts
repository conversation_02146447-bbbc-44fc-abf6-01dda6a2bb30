import { Repository, DataSource } from 'typeorm';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { BaseCmsCustomerFeedbacksService } from './base.cms-customer-feedbacks.service';
import { CmsCustomerFeedbacks, CmsCustomerFeedbackStatus } from '../entity/cms-customer-feedbacks.entity';
import { UpdateCmsCustomerFeedbackDto } from '../dto/update.cms-customer-feedback.dto';
import { CmsCustomerFeedbackDto } from '../dto/cms-customer-feedback.dto';
export declare class UpdateCmsCustomerFeedbacksService extends BaseCmsCustomerFeedbacksService {
    protected readonly feedbackRepository: Repository<CmsCustomerFeedbacks>;
    protected readonly dataSource: DataSource;
    protected readonly eventEmitter: EventEmitter2;
    constructor(feedbackRepository: Repository<CmsCustomerFeedbacks>, dataSource: DataSource, eventEmitter: EventEmitter2);
    update(id: string, updateDto: UpdateCmsCustomerFeedbackDto, userId: string): Promise<CmsCustomerFeedbackDto | null>;
    bulkUpdate(updates: Array<{
        id: string;
        data: UpdateCmsCustomerFeedbackDto;
    }>, userId: string): Promise<CmsCustomerFeedbackDto[]>;
    approve(id: string, userId: string): Promise<CmsCustomerFeedbackDto | null>;
    reject(id: string, userId: string): Promise<CmsCustomerFeedbackDto | null>;
    updateStatus(id: string, status: CmsCustomerFeedbackStatus, userId: string): Promise<CmsCustomerFeedbackDto | null>;
    bulkApprove(ids: string[], userId: string): Promise<CmsCustomerFeedbackDto[]>;
    bulkReject(ids: string[], userId: string): Promise<CmsCustomerFeedbackDto[]>;
}

/**
 * 🚀 Portable VNPAY Payment Gateway - Main Exports
 * 
 * This file exports everything needed to use the portable VNPAY module
 * Copy this entire directory to any NestJS project and import what you need
 */

// 📦 Main Module
export { VnpayPaymentModule } from './vnpay-payment.module';

// 🔧 Core Services
export { VnpayPaymentService } from './services/vnpay-payment.service';
export { VnpayService } from './services/vnpay.service';

// 🗄️ Database
export { VnpayTransaction, VnpayTransactionStatus, VnpayTransactionType } from './entities/vnpay-transaction.entity';
export { VnpayTransactionRepository } from './repositories/vnpay-transaction.repository';

// 🔌 Integration Interfaces
export {
  PaymentRequest,
  PaymentResponse,
  PaymentCallback,
  PaymentQueryRequest,
  PaymentQueryResponse,
  PaymentRefundRequest,
  PaymentRefundResponse,
  PaymentEventHandler,
  VnpayConfig,
  IVnpayPaymentService,
} from './interfaces/payment-integration.interface';

// 🌐 Controller
export { VnpayPaymentController } from './controllers/vnpay-payment.controller';

// 📚 Examples
export { VnpayPortableUsageExample } from './examples/vnpay-portable-usage.example';

// 🔧 VNPAY Service Interfaces
export {
  VnpayPaymentRequest,
  VnpayCreatePaymentParams,
  VnpayQueryRequest,
  VnpayRefundRequest,
} from './services/vnpay.service';

/**
 * 🎯 Quick Start Example:
 * 
 * ```typescript
 * // 1. Import module
 * import { VnpayPaymentModule } from './payment-gateways';
 * 
 * @Module({
 *   imports: [VnpayPaymentModule],
 * })
 * export class AppModule {}
 * 
 * // 2. Use service
 * import { VnpayPaymentService, PaymentRequest } from './payment-gateways';
 * 
 * @Injectable()
 * export class YourService {
 *   constructor(private vnpayService: VnpayPaymentService) {}
 * 
 *   async createPayment(userId: string, amount: number) {
 *     const request: PaymentRequest = {
 *       externalRef: userId,
 *       amount: amount,
 *       description: 'Payment description',
 *       clientIp: '127.0.0.1',
 *     };
 * 
 *     return await this.vnpayService.createPayment(request);
 *   }
 * }
 * ```
 * 
 * 📖 See INTEGRATION_GUIDE.md for detailed instructions
 */

import { BaseEntity } from '../../common/entities/base.entity';
import { CmsPosts } from '../../cms-posts/entity/cms-posts.entity';
export declare class CmsTags extends BaseEntity {
    name: string;
    slug: string;
    description?: string | null;
    imageUrl?: string | null;
    metaTitle?: string | null;
    metaDescription?: string | null;
    metaKeywords?: string | null;
    posts?: CmsPosts[];
    getEntityName(): string;
    getSeoTitle(): string;
    getSeoDescription(): string;
    getSeoKeywordsArray(): string[];
    hasImage(): boolean;
}

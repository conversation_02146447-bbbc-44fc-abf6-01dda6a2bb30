import { BaseEntity } from '../../common/entities/base.entity';
import { User } from '../../users/entities/user.entity';
export declare enum CmsCustomerFeedbackStatus {
    PENDING = "pending",
    APPROVED = "approved",
    REJECTED = "rejected"
}
export declare class CmsCustomerFeedbacks extends BaseEntity {
    customerName: string;
    customerTitleCompany?: string | null;
    feedbackText: string;
    rating?: number | null;
    avatarUrl?: string | null;
    productServiceName?: string | null;
    status: CmsCustomerFeedbackStatus;
    approvedBy?: string | null;
    approver?: User;
    getEntityName(): string;
    isApproved(): boolean;
    isPending(): boolean;
    isRejected(): boolean;
    getRatingText(): string;
    getRatingColor(): string;
    getDisplayName(): string;
}

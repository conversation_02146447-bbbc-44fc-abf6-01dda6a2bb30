import { CreateCmsPagesService } from '../services/create.cms-pages.service';
import { CmsPageDto } from '../dto/cms-page.dto';
import { CreateCmsPageDto } from '../dto/create.cms-page.dto';
export declare class CreateCmsPagesController {
    private readonly cmsPagesService;
    constructor(cmsPagesService: CreateCmsPagesService);
    create(createCmsPageDto: CreateCmsPageDto, userId: string): Promise<CmsPageDto>;
    bulkCreate(createCmsPageDtos: CreateCmsPageDto[], userId: string): Promise<CmsPageDto[]>;
    createFromTemplate(templateName: string, title: string, userId: string): Promise<CmsPageDto>;
    duplicate(sourceId: string, newTitle: string, userId: string): Promise<CmsPageDto>;
    importPages(pagesData: any[], userId: string): Promise<CmsPageDto[]>;
}

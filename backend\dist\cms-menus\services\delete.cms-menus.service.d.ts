import { Repository, DataSource } from 'typeorm';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { BaseCmsMenusService } from './base.cms-menus.service';
import { CmsMenus } from '../entity/cms-menus.entity';
import { CmsMenuDto } from '../dto/cms-menu.dto';
export declare class DeleteCmsMenusService extends BaseCmsMenusService {
    protected readonly menuRepository: Repository<CmsMenus>;
    protected readonly dataSource: DataSource;
    protected readonly eventEmitter: EventEmitter2;
    constructor(menuRepository: Repository<CmsMenus>, dataSource: DataSource, eventEmitter: EventEmitter2);
    softDelete(id: string, userId: string): Promise<CmsMenuDto | null>;
    restore(id: string, userId: string): Promise<CmsMenuDto | null>;
    forceDelete(id: string, userId: string): Promise<boolean>;
    bulkSoftDelete(ids: string[], userId: string): Promise<CmsMenuDto[]>;
}

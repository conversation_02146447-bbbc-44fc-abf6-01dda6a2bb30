{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/bloomgoo/gold-exchange-platform/frontend/components/providers/WebSocketProvider.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const WebSocketProvider = registerClientReference(\n    function() { throw new Error(\"Attempted to call WebSocketProvider() from the server but WebSocketProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/providers/WebSocketProvider.tsx <module evaluation>\",\n    \"WebSocketProvider\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,oBAAoB,CAAA,GAAA,8VAAA,CAAA,0BAAuB,AAAD,EACnD;IAAa,MAAM,IAAI,MAAM;AAAkP,GAC/Q,4EACA", "debugId": null}}, {"offset": {"line": 21, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/bloomgoo/gold-exchange-platform/frontend/components/providers/WebSocketProvider.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const WebSocketProvider = registerClientReference(\n    function() { throw new Error(\"Attempted to call WebSocketProvider() from the server but WebSocketProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/providers/WebSocketProvider.tsx\",\n    \"WebSocketProvider\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,oBAAoB,CAAA,GAAA,8VAAA,CAAA,0BAAuB,AAAD,EACnD;IAAa,MAAM,IAAI,MAAM;AAAkP,GAC/Q,wDACA", "debugId": null}}, {"offset": {"line": 35, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 45, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/bloomgoo/gold-exchange-platform/frontend/components/ui/sonner.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const Toaster = registerClientReference(\n    function() { throw new Error(\"Attempted to call Toaster() from the server but To<PERSON> is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/ui/sonner.tsx <module evaluation>\",\n    \"Toaster\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,UAAU,CAAA,GAAA,8VAAA,CAAA,0BAAuB,AAAD,EACzC;IAAa,MAAM,IAAI,MAAM;AAA8N,GAC3P,0DACA", "debugId": null}}, {"offset": {"line": 59, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/bloomgoo/gold-exchange-platform/frontend/components/ui/sonner.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const Toaster = registerClientReference(\n    function() { throw new Error(\"Attempted to call Toaster() from the server but To<PERSON> is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/ui/sonner.tsx\",\n    \"Toaster\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,UAAU,CAAA,GAAA,8VAAA,CAAA,0BAAuB,AAAD,EACzC;IAAa,MAAM,IAAI,MAAM;AAA8N,GAC3P,sCACA", "debugId": null}}, {"offset": {"line": 73, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 83, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/bloomgoo/gold-exchange-platform/frontend/hooks/use-auth.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const AuthProvider = registerClientReference(\n    function() { throw new Error(\"Attempted to call AuthProvider() from the server but AuthProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/hooks/use-auth.tsx <module evaluation>\",\n    \"AuthProvider\",\n);\nexport const useAuth = registerClientReference(\n    function() { throw new Error(\"Attempted to call useAuth() from the server but useAuth is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/hooks/use-auth.tsx <module evaluation>\",\n    \"useAuth\",\n);\n"], "names": [], "mappings": ";;;;AAAA;;AACO,MAAM,eAAe,CAAA,GAAA,8VAAA,CAAA,0BAAuB,AAAD,EAC9C;IAAa,MAAM,IAAI,MAAM;AAAwO,GACrQ,oDACA;AAEG,MAAM,UAAU,CAAA,GAAA,8VAAA,CAAA,0BAAuB,AAAD,EACzC;IAAa,MAAM,IAAI,MAAM;AAA8N,GAC3P,oDACA", "debugId": null}}, {"offset": {"line": 101, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/bloomgoo/gold-exchange-platform/frontend/hooks/use-auth.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const AuthProvider = registerClientReference(\n    function() { throw new Error(\"Attempted to call AuthProvider() from the server but AuthProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/hooks/use-auth.tsx\",\n    \"AuthProvider\",\n);\nexport const useAuth = registerClientReference(\n    function() { throw new Error(\"Attempted to call useAuth() from the server but useAuth is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/hooks/use-auth.tsx\",\n    \"useAuth\",\n);\n"], "names": [], "mappings": ";;;;AAAA;;AACO,MAAM,eAAe,CAAA,GAAA,8VAAA,CAAA,0BAAuB,AAAD,EAC9C;IAAa,MAAM,IAAI,MAAM;AAAwO,GACrQ,gCACA;AAEG,MAAM,UAAU,CAAA,GAAA,8VAAA,CAAA,0BAAuB,AAAD,EACzC;IAAa,MAAM,IAAI,MAAM;AAA8N,GAC3P,gCACA", "debugId": null}}, {"offset": {"line": 119, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 129, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/bloomgoo/gold-exchange-platform/frontend/components/dynamic-metadata.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const DynamicMetadata = registerClientReference(\n    function() { throw new Error(\"Attempted to call DynamicMetadata() from the server but DynamicMetadata is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/dynamic-metadata.tsx <module evaluation>\",\n    \"DynamicMetadata\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,kBAAkB,CAAA,GAAA,8VAAA,CAAA,0BAAuB,AAAD,EACjD;IAAa,MAAM,IAAI,MAAM;AAA8O,GAC3Q,iEACA", "debugId": null}}, {"offset": {"line": 143, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/bloomgoo/gold-exchange-platform/frontend/components/dynamic-metadata.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const DynamicMetadata = registerClientReference(\n    function() { throw new Error(\"Attempted to call DynamicMetadata() from the server but DynamicMetadata is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/dynamic-metadata.tsx\",\n    \"DynamicMetadata\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,kBAAkB,CAAA,GAAA,8VAAA,CAAA,0BAAuB,AAAD,EACjD;IAAa,MAAM,IAAI,MAAM;AAA8O,GAC3Q,6CACA", "debugId": null}}, {"offset": {"line": 157, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 167, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/bloomgoo/gold-exchange-platform/frontend/lib/auth.ts"], "sourcesContent": ["/**\r\n * Service xử lý xác thực người dùng\r\n */\r\nimport { api } from './api';\r\n\r\n// Đ<PERSON>nh nghĩa các kiểu dữ liệu\r\nexport interface LoginCredentials {\r\n  identity: string;\r\n  password: string;\r\n}\r\n\r\nexport interface RegisterCredentials {\r\n  username: string;\r\n  email: string;\r\n  password: string;\r\n  fullName?: string;\r\n  phone: string; // Đã thay đổi từ optional thành required\r\n  address?: string;\r\n  referredByCode?: string;\r\n  firebaseToken?: string; // Token xác thực từ Firebase sau khi xác minh OTP\r\n  registerType?: 'PHONE' | 'EMAIL'; // Phương thức xác thực\r\n}\r\n\r\nexport interface User {\r\n  id: string;\r\n  username: string;\r\n  email: string;\r\n  fullName?: string;\r\n  roles?: string[];\r\n  permissions?: string[];\r\n  exp?: number; // Thời gian hết hạn của token\r\n  [key: string]: any;\r\n}\r\n\r\nexport interface AuthResponse {\r\n  access_token: string;\r\n  refresh_token: string;\r\n  user?: User;\r\n}\r\n\r\nexport interface RefreshTokenRequest {\r\n  refreshToken: string;\r\n}\r\n\r\n/**\r\n * Hàm giải mã JWT token để lấy thông tin người dùng\r\n */\r\nexport const decodeToken = (token: string): User | null => {\r\n  try {\r\n    if (!token) return null;\r\n\r\n    // JWT token có 3 phần: header.payload.signature\r\n    const base64Url = token.split('.')[1];\r\n    if (!base64Url) return null;\r\n\r\n    // Thay thế các ký tự đặc biệt trong base64Url\r\n    const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');\r\n\r\n    // Giải mã base64 thành JSON\r\n    const jsonPayload = decodeURIComponent(\r\n      atob(base64)\r\n        .split('')\r\n        .map(c => '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2))\r\n        .join('')\r\n    );\r\n\r\n    const payload = JSON.parse(jsonPayload);\r\n\r\n    // Chuyển đổi payload thành User\r\n    const user: User = {\r\n      id: payload.sub || payload.id || '',\r\n      username: payload.username || '',\r\n      email: payload.email || '',\r\n      fullName: payload.fullName || payload.name || '',\r\n      roles: payload.roles || [],\r\n      permissions: payload.permissions || [],\r\n      exp: payload.exp || 0, // Thêm trường exp (expiration time)\r\n    };\r\n\r\n    return user;\r\n  } catch (error) {\r\n    console.error('Error decoding token:', error);\r\n    return null;\r\n  }\r\n};\r\n\r\n/**\r\n * Kiểm tra xem token đã hết hạn chưa\r\n */\r\nexport const isTokenExpired = (token: string): boolean => {\r\n  try {\r\n    if (!token) return true;\r\n\r\n    const decodedToken = decodeToken(token);\r\n    if (!decodedToken || !decodedToken.exp) return true;\r\n\r\n    // exp là thời gian hết hạn tính bằng giây kể từ epoch (1/1/1970)\r\n    const currentTime = Math.floor(Date.now() / 1000); // Chuyển đổi thời gian hiện tại sang giây\r\n\r\n    // Trả về true nếu token đã hết hạn\r\n    return decodedToken.exp < currentTime;\r\n  } catch (error) {\r\n    console.error('Error checking token expiration:', error);\r\n    return true; // Nếu có lỗi, coi như token đã hết hạn\r\n  }\r\n};\r\n\r\n/**\r\n * Kiểm tra xem token có sắp hết hạn không (trong vòng 5 phút)\r\n */\r\nexport const isTokenExpiringSoon = (token: string, thresholdSeconds: number = 300): boolean => {\r\n  try {\r\n    if (!token) return true;\r\n\r\n    const decodedToken = decodeToken(token);\r\n    if (!decodedToken || !decodedToken.exp) return true;\r\n\r\n    const currentTime = Math.floor(Date.now() / 1000);\r\n\r\n    // Trả về true nếu token sắp hết hạn trong vòng thresholdSeconds giây\r\n    return decodedToken.exp - currentTime < thresholdSeconds;\r\n  } catch (error) {\r\n    console.error('Error checking token expiration:', error);\r\n    return true;\r\n  }\r\n};\r\n\r\n/**\r\n * Service xử lý xác thực\r\n */\r\nexport const authService = {\r\n  /**\r\n   * Đăng nhập người dùng\r\n   */\r\n  async login(credentials: LoginCredentials): Promise<AuthResponse> {\r\n    try {\r\n      const response = await api.post<{ data: AuthResponse }>('auth/login', credentials);\r\n\r\n      // Kiểm tra cấu trúc response\r\n      const authData = response.data || response;\r\n\r\n      // Lưu token vào localStorage và cookie\r\n      localStorage.setItem('accessToken', authData.access_token);\r\n      localStorage.setItem('refreshToken', authData.refresh_token);\r\n\r\n      // Lưu token vào cookie để middleware có thể truy cập\r\n      document.cookie = `token=${authData.access_token}; path=/; max-age=${60 * 60 * 24 * 7}; SameSite=Lax`; // 7 ngày\r\n\r\n      // Nếu không có thông tin người dùng trong response, giải mã token để lấy\r\n      if (!authData.user && authData.access_token) {\r\n        const decodedUser = decodeToken(authData.access_token);\r\n        if (decodedUser) {\r\n          authData.user = decodedUser;\r\n          this.saveUser(decodedUser);\r\n        }\r\n      } else if (authData.user) {\r\n        // Lưu thông tin người dùng\r\n        this.saveUser(authData.user);\r\n      }\r\n\r\n      return authData;\r\n    } catch (error) {\r\n      console.error('Login error:', error);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  /**\r\n   * Đăng ký người dùng mới\r\n   */\r\n  async register(credentials: RegisterCredentials): Promise<AuthResponse> {\r\n    try {\r\n      const response = await api.post<{ data: AuthResponse }>('auth/register', credentials);\r\n\r\n      // Kiểm tra cấu trúc response\r\n      const authData = response.data || response;\r\n\r\n      // Lưu token vào localStorage và cookie\r\n      localStorage.setItem('accessToken', authData.access_token);\r\n      localStorage.setItem('refreshToken', authData.refresh_token);\r\n\r\n      // Lưu token vào cookie để middleware có thể truy cập\r\n      document.cookie = `token=${authData.access_token}; path=/; max-age=${60 * 60 * 24 * 7}; SameSite=Lax`; // 7 ngày\r\n\r\n      // Nếu không có thông tin người dùng trong response, giải mã token để lấy\r\n      if (!authData.user && authData.access_token) {\r\n        const decodedUser = decodeToken(authData.access_token);\r\n        if (decodedUser) {\r\n          authData.user = decodedUser;\r\n          this.saveUser(decodedUser);\r\n        }\r\n      } else if (authData.user) {\r\n        // Lưu thông tin người dùng\r\n        this.saveUser(authData.user);\r\n      }\r\n\r\n      return authData;\r\n    } catch (error) {\r\n      console.error('Register error:', error);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  /**\r\n   * Làm mới token\r\n   */\r\n  async refreshToken(refreshToken: string): Promise<AuthResponse> {\r\n    try {\r\n      const response = await api.post<{ data: AuthResponse }>('auth/refresh-token', { refreshToken });\r\n\r\n      // Kiểm tra cấu trúc response\r\n      const authData = response.data || response;\r\n\r\n      // Cập nhật token trong localStorage và cookie\r\n      localStorage.setItem('accessToken', authData.access_token);\r\n      localStorage.setItem('refreshToken', authData.refresh_token);\r\n\r\n      // Cập nhật token trong cookie để middleware có thể truy cập\r\n      document.cookie = `token=${authData.access_token}; path=/; max-age=${60 * 60 * 24 * 7}; SameSite=Lax`; // 7 ngày\r\n\r\n      // Nếu không có thông tin người dùng trong response, giải mã token để lấy\r\n      if (!authData.user && authData.access_token) {\r\n        const decodedUser = decodeToken(authData.access_token);\r\n        if (decodedUser) {\r\n          authData.user = decodedUser;\r\n          this.saveUser(decodedUser);\r\n        }\r\n      } else if (authData.user) {\r\n        // Lưu thông tin người dùng\r\n        this.saveUser(authData.user);\r\n      }\r\n\r\n      return authData;\r\n    } catch (error) {\r\n      console.error('Refresh token error:', error);\r\n      // Nếu refresh token thất bại, đăng xuất người dùng\r\n      this.logout();\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  /**\r\n   * Thử làm mới token nếu có refresh token\r\n   */\r\n  async tryRefreshToken(): Promise<boolean> {\r\n    try {\r\n      const refreshToken = this.getRefreshToken();\r\n      if (!refreshToken) return false;\r\n\r\n      const result = await this.refreshToken(refreshToken);\r\n\r\n      // Việc điều hướng sẽ được xử lý ở handleTokenRefresh trong api.ts\r\n      // Không điều hướng ở đây để tránh điều hướng kép\r\n\r\n      return true;\r\n    } catch (error) {\r\n      console.error('Try refresh token error:', error);\r\n      return false;\r\n    }\r\n  },\r\n\r\n  /**\r\n   * Điều hướng người dùng dựa trên vai trò\r\n   */\r\n  redirectBasedOnRole(user: User): void {\r\n    if (typeof window === 'undefined') return;\r\n\r\n    const isAdmin = user.roles?.includes('ADMIN');\r\n\r\n    // Điều hướng dựa vào vai trò\r\n    if (isAdmin) {\r\n      window.location.href = '/admin/dashboard';\r\n    } else {\r\n      window.location.href = '/dashboard';\r\n    }\r\n  },\r\n\r\n  /**\r\n   * Đăng xuất người dùng\r\n   */\r\n  logout(): void {\r\n    localStorage.removeItem('accessToken');\r\n    localStorage.removeItem('refreshToken');\r\n    localStorage.removeItem('user');\r\n\r\n    // Xóa cookie token\r\n    document.cookie = 'token=; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT; SameSite=Lax';\r\n\r\n    // Chuyển hướng về trang đăng nhập nếu cần\r\n    if (typeof window !== 'undefined') {\r\n      window.location.href = '/login';\r\n    }\r\n  },\r\n\r\n  /**\r\n   * Kiểm tra người dùng đã đăng nhập chưa\r\n   */\r\n  isAuthenticated(): boolean {\r\n    if (typeof window === 'undefined') return false;\r\n\r\n    // Kiểm tra token trong localStorage hoặc cookie\r\n    const localToken = localStorage.getItem('accessToken');\r\n    if (localToken) {\r\n      // Kiểm tra xem token có hợp lệ và chưa hết hạn không\r\n      if (!isTokenExpired(localToken)) {\r\n        // Nếu token sắp hết hạn, thử refresh token\r\n        if (isTokenExpiringSoon(localToken)) {\r\n          this.tryRefreshToken();\r\n        }\r\n        return true;\r\n      } else {\r\n        // Nếu token đã hết hạn, thử refresh token\r\n        this.tryRefreshToken();\r\n        return false;\r\n      }\r\n    }\r\n\r\n    // Kiểm tra token trong cookie\r\n    const cookies = document.cookie.split(';');\r\n    for (let i = 0; i < cookies.length; i++) {\r\n      const cookie = cookies[i].trim();\r\n      if (cookie.startsWith('token=')) {\r\n        const token = cookie.substring('token='.length, cookie.length);\r\n        if (token) {\r\n          // Kiểm tra xem token có hợp lệ và chưa hết hạn không\r\n          if (!isTokenExpired(token)) {\r\n            // Nếu có token trong cookie nhưng không có trong localStorage,\r\n            // lưu vào localStorage để sử dụng\r\n            if (!localToken) {\r\n              localStorage.setItem('accessToken', token);\r\n              // Giải mã token để lấy thông tin người dùng\r\n              const user = decodeToken(token);\r\n              if (user) {\r\n                this.saveUser(user);\r\n              }\r\n            }\r\n\r\n            // Nếu token sắp hết hạn, thử refresh token\r\n            if (isTokenExpiringSoon(token)) {\r\n              this.tryRefreshToken();\r\n            }\r\n\r\n            return true;\r\n          } else {\r\n            // Nếu token đã hết hạn, thử refresh token\r\n            this.tryRefreshToken();\r\n          }\r\n        }\r\n      }\r\n    }\r\n\r\n    return false;\r\n  },\r\n\r\n  /**\r\n   * Lấy thông tin người dùng hiện tại\r\n   */\r\n  getCurrentUser(): User | null {\r\n    if (typeof window === 'undefined') return null;\r\n\r\n    const userJson = localStorage.getItem('user');\r\n    if (!userJson) return null;\r\n\r\n    try {\r\n      return JSON.parse(userJson);\r\n    } catch (error) {\r\n      console.error('Error parsing user data:', error);\r\n      return null;\r\n    }\r\n  },\r\n\r\n  /**\r\n   * Lưu thông tin người dùng\r\n   */\r\n  saveUser(user: User): void {\r\n    localStorage.setItem('user', JSON.stringify(user));\r\n  },\r\n\r\n  /**\r\n   * Lấy token hiện tại\r\n   */\r\n  getAccessToken(): string | null {\r\n    if (typeof window === 'undefined') return null;\r\n    return localStorage.getItem('accessToken');\r\n  },\r\n\r\n  /**\r\n   * Lấy refresh token\r\n   */\r\n  getRefreshToken(): string | null {\r\n    if (typeof window === 'undefined') return null;\r\n    return localStorage.getItem('refreshToken');\r\n  },\r\n\r\n  /**\r\n   * Xác minh số điện thoại cho người dùng hiện có\r\n   * @param userId ID của người dùng\r\n   * @param firebaseToken Firebase ID token\r\n   * @returns Kết quả xác minh\r\n   */\r\n  async verifyPhoneNumber(userId: string, firebaseToken: string): Promise<any> {\r\n    try {\r\n      const response = await api.post<{ data: any }>('auth/verify-phone-public', {\r\n        userId,\r\n        firebaseToken\r\n      });\r\n\r\n      return response.data || response;\r\n    } catch (error) {\r\n      console.error('Phone verification error:', error);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  /**\r\n   * Unified OTP verification for both phone and email\r\n   * @param verificationData Verification data\r\n   * @returns Verification result with tokens\r\n   */\r\n  async verifyOtp(verificationData: {\r\n    registerType: 'PHONE' | 'EMAIL';\r\n    email?: string;\r\n    otp?: string;\r\n    firebaseToken?: string;\r\n    userId?: string;\r\n  }): Promise<AuthResponse> {\r\n    try {\r\n      const response = await api.post<{ data: AuthResponse }>('auth/verify-otp', verificationData);\r\n\r\n      // Kiểm tra cấu trúc response\r\n      const authData = response.data || response;\r\n\r\n      // Lưu token vào localStorage và cookie\r\n      localStorage.setItem('accessToken', authData.access_token);\r\n      localStorage.setItem('refreshToken', authData.refresh_token);\r\n\r\n      // Lưu token vào cookie để middleware có thể truy cập\r\n      document.cookie = `token=${authData.access_token}; path=/; max-age=${60 * 60 * 24 * 7}; SameSite=Lax`; // 7 ngày\r\n\r\n      // Lưu thông tin user vào localStorage\r\n      if (authData.user) {\r\n        localStorage.setItem('user', JSON.stringify(authData.user));\r\n      }\r\n\r\n      return authData;\r\n    } catch (error) {\r\n      console.error('OTP verification error:', error);\r\n      throw error;\r\n    }\r\n  }\r\n};\r\n"], "names": [], "mappings": "AAAA;;CAEC;;;;;;AACD;;AA4CO,MAAM,cAAc,CAAC;IAC1B,IAAI;QACF,IAAI,CAAC,OAAO,OAAO;QAEnB,gDAAgD;QAChD,MAAM,YAAY,MAAM,KAAK,CAAC,IAAI,CAAC,EAAE;QACrC,IAAI,CAAC,WAAW,OAAO;QAEvB,8CAA8C;QAC9C,MAAM,SAAS,UAAU,OAAO,CAAC,MAAM,KAAK,OAAO,CAAC,MAAM;QAE1D,4BAA4B;QAC5B,MAAM,cAAc,mBAClB,KAAK,QACF,KAAK,CAAC,IACN,GAAG,CAAC,CAAA,IAAK,MAAM,CAAC,OAAO,EAAE,UAAU,CAAC,GAAG,QAAQ,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC,IAC5D,IAAI,CAAC;QAGV,MAAM,UAAU,KAAK,KAAK,CAAC;QAE3B,gCAAgC;QAChC,MAAM,OAAa;YACjB,IAAI,QAAQ,GAAG,IAAI,QAAQ,EAAE,IAAI;YACjC,UAAU,QAAQ,QAAQ,IAAI;YAC9B,OAAO,QAAQ,KAAK,IAAI;YACxB,UAAU,QAAQ,QAAQ,IAAI,QAAQ,IAAI,IAAI;YAC9C,OAAO,QAAQ,KAAK,IAAI,EAAE;YAC1B,aAAa,QAAQ,WAAW,IAAI,EAAE;YACtC,KAAK,QAAQ,GAAG,IAAI;QACtB;QAEA,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,yBAAyB;QACvC,OAAO;IACT;AACF;AAKO,MAAM,iBAAiB,CAAC;IAC7B,IAAI;QACF,IAAI,CAAC,OAAO,OAAO;QAEnB,MAAM,eAAe,YAAY;QACjC,IAAI,CAAC,gBAAgB,CAAC,aAAa,GAAG,EAAE,OAAO;QAE/C,iEAAiE;QACjE,MAAM,cAAc,KAAK,KAAK,CAAC,KAAK,GAAG,KAAK,OAAO,0CAA0C;QAE7F,mCAAmC;QACnC,OAAO,aAAa,GAAG,GAAG;IAC5B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,oCAAoC;QAClD,OAAO,MAAM,uCAAuC;IACtD;AACF;AAKO,MAAM,sBAAsB,CAAC,OAAe,mBAA2B,GAAG;IAC/E,IAAI;QACF,IAAI,CAAC,OAAO,OAAO;QAEnB,MAAM,eAAe,YAAY;QACjC,IAAI,CAAC,gBAAgB,CAAC,aAAa,GAAG,EAAE,OAAO;QAE/C,MAAM,cAAc,KAAK,KAAK,CAAC,KAAK,GAAG,KAAK;QAE5C,qEAAqE;QACrE,OAAO,aAAa,GAAG,GAAG,cAAc;IAC1C,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,oCAAoC;QAClD,OAAO;IACT;AACF;AAKO,MAAM,cAAc;IACzB;;GAEC,GACD,MAAM,OAAM,WAA6B;QACvC,IAAI;YACF,MAAM,WAAW,MAAM,0GAAA,CAAA,MAAG,CAAC,IAAI,CAAyB,cAAc;YAEtE,6BAA6B;YAC7B,MAAM,WAAW,SAAS,IAAI,IAAI;YAElC,uCAAuC;YACvC,aAAa,OAAO,CAAC,eAAe,SAAS,YAAY;YACzD,aAAa,OAAO,CAAC,gBAAgB,SAAS,aAAa;YAE3D,qDAAqD;YACrD,SAAS,MAAM,GAAG,CAAC,MAAM,EAAE,SAAS,YAAY,CAAC,kBAAkB,EAAE,KAAK,KAAK,KAAK,EAAE,cAAc,CAAC,EAAE,SAAS;YAEhH,yEAAyE;YACzE,IAAI,CAAC,SAAS,IAAI,IAAI,SAAS,YAAY,EAAE;gBAC3C,MAAM,cAAc,YAAY,SAAS,YAAY;gBACrD,IAAI,aAAa;oBACf,SAAS,IAAI,GAAG;oBAChB,IAAI,CAAC,QAAQ,CAAC;gBAChB;YACF,OAAO,IAAI,SAAS,IAAI,EAAE;gBACxB,2BAA2B;gBAC3B,IAAI,CAAC,QAAQ,CAAC,SAAS,IAAI;YAC7B;YAEA,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,gBAAgB;YAC9B,MAAM;QACR;IACF;IAEA;;GAEC,GACD,MAAM,UAAS,WAAgC;QAC7C,IAAI;YACF,MAAM,WAAW,MAAM,0GAAA,CAAA,MAAG,CAAC,IAAI,CAAyB,iBAAiB;YAEzE,6BAA6B;YAC7B,MAAM,WAAW,SAAS,IAAI,IAAI;YAElC,uCAAuC;YACvC,aAAa,OAAO,CAAC,eAAe,SAAS,YAAY;YACzD,aAAa,OAAO,CAAC,gBAAgB,SAAS,aAAa;YAE3D,qDAAqD;YACrD,SAAS,MAAM,GAAG,CAAC,MAAM,EAAE,SAAS,YAAY,CAAC,kBAAkB,EAAE,KAAK,KAAK,KAAK,EAAE,cAAc,CAAC,EAAE,SAAS;YAEhH,yEAAyE;YACzE,IAAI,CAAC,SAAS,IAAI,IAAI,SAAS,YAAY,EAAE;gBAC3C,MAAM,cAAc,YAAY,SAAS,YAAY;gBACrD,IAAI,aAAa;oBACf,SAAS,IAAI,GAAG;oBAChB,IAAI,CAAC,QAAQ,CAAC;gBAChB;YACF,OAAO,IAAI,SAAS,IAAI,EAAE;gBACxB,2BAA2B;gBAC3B,IAAI,CAAC,QAAQ,CAAC,SAAS,IAAI;YAC7B;YAEA,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,mBAAmB;YACjC,MAAM;QACR;IACF;IAEA;;GAEC,GACD,MAAM,cAAa,YAAoB;QACrC,IAAI;YACF,MAAM,WAAW,MAAM,0GAAA,CAAA,MAAG,CAAC,IAAI,CAAyB,sBAAsB;gBAAE;YAAa;YAE7F,6BAA6B;YAC7B,MAAM,WAAW,SAAS,IAAI,IAAI;YAElC,8CAA8C;YAC9C,aAAa,OAAO,CAAC,eAAe,SAAS,YAAY;YACzD,aAAa,OAAO,CAAC,gBAAgB,SAAS,aAAa;YAE3D,4DAA4D;YAC5D,SAAS,MAAM,GAAG,CAAC,MAAM,EAAE,SAAS,YAAY,CAAC,kBAAkB,EAAE,KAAK,KAAK,KAAK,EAAE,cAAc,CAAC,EAAE,SAAS;YAEhH,yEAAyE;YACzE,IAAI,CAAC,SAAS,IAAI,IAAI,SAAS,YAAY,EAAE;gBAC3C,MAAM,cAAc,YAAY,SAAS,YAAY;gBACrD,IAAI,aAAa;oBACf,SAAS,IAAI,GAAG;oBAChB,IAAI,CAAC,QAAQ,CAAC;gBAChB;YACF,OAAO,IAAI,SAAS,IAAI,EAAE;gBACxB,2BAA2B;gBAC3B,IAAI,CAAC,QAAQ,CAAC,SAAS,IAAI;YAC7B;YAEA,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,wBAAwB;YACtC,mDAAmD;YACnD,IAAI,CAAC,MAAM;YACX,MAAM;QACR;IACF;IAEA;;GAEC,GACD,MAAM;QACJ,IAAI;YACF,MAAM,eAAe,IAAI,CAAC,eAAe;YACzC,IAAI,CAAC,cAAc,OAAO;YAE1B,MAAM,SAAS,MAAM,IAAI,CAAC,YAAY,CAAC;YAEvC,kEAAkE;YAClE,iDAAiD;YAEjD,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4BAA4B;YAC1C,OAAO;QACT;IACF;IAEA;;GAEC,GACD,qBAAoB,IAAU;QAC5B,wCAAmC;;QAEnC,MAAM;IAQR;IAEA;;GAEC,GACD;QACE,aAAa,UAAU,CAAC;QACxB,aAAa,UAAU,CAAC;QACxB,aAAa,UAAU,CAAC;QAExB,mBAAmB;QACnB,SAAS,MAAM,GAAG;QAElB,0CAA0C;QAC1C,uCAAmC;;QAEnC;IACF;IAEA;;GAEC,GACD;QACE,wCAAmC,OAAO;;QAE1C,gDAAgD;QAChD,MAAM;QAgBN,8BAA8B;QAC9B,MAAM;QACD,IAAI;IAiCX;IAEA;;GAEC,GACD;QACE,wCAAmC,OAAO;;QAE1C,MAAM;IASR;IAEA;;GAEC,GACD,UAAS,IAAU;QACjB,aAAa,OAAO,CAAC,QAAQ,KAAK,SAAS,CAAC;IAC9C;IAEA;;GAEC,GACD;QACE,wCAAmC,OAAO;;IAE5C;IAEA;;GAEC,GACD;QACE,wCAAmC,OAAO;;IAE5C;IAEA;;;;;GAKC,GACD,MAAM,mBAAkB,MAAc,EAAE,aAAqB;QAC3D,IAAI;YACF,MAAM,WAAW,MAAM,0GAAA,CAAA,MAAG,CAAC,IAAI,CAAgB,4BAA4B;gBACzE;gBACA;YACF;YAEA,OAAO,SAAS,IAAI,IAAI;QAC1B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6BAA6B;YAC3C,MAAM;QACR;IACF;IAEA;;;;GAIC,GACD,MAAM,WAAU,gBAMf;QACC,IAAI;YACF,MAAM,WAAW,MAAM,0GAAA,CAAA,MAAG,CAAC,IAAI,CAAyB,mBAAmB;YAE3E,6BAA6B;YAC7B,MAAM,WAAW,SAAS,IAAI,IAAI;YAElC,uCAAuC;YACvC,aAAa,OAAO,CAAC,eAAe,SAAS,YAAY;YACzD,aAAa,OAAO,CAAC,gBAAgB,SAAS,aAAa;YAE3D,qDAAqD;YACrD,SAAS,MAAM,GAAG,CAAC,MAAM,EAAE,SAAS,YAAY,CAAC,kBAAkB,EAAE,KAAK,KAAK,KAAK,EAAE,cAAc,CAAC,EAAE,SAAS;YAEhH,sCAAsC;YACtC,IAAI,SAAS,IAAI,EAAE;gBACjB,aAAa,OAAO,CAAC,QAAQ,KAAK,SAAS,CAAC,SAAS,IAAI;YAC3D;YAEA,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2BAA2B;YACzC,MAAM;QACR;IACF;AACF", "debugId": null}}, {"offset": {"line": 441, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/bloomgoo/gold-exchange-platform/frontend/lib/api.ts"], "sourcesContent": ["/**\r\n * API client cho ứng dụng\r\n */\r\nimport { authService, isTokenExpired } from './auth';\r\n\r\nconst NEXT_PUBLIC_API_URL = process.env.NEXT_PUBLIC_API_URL || \"http://localhost:3000/api/v1\"; \r\n\r\n// Biến để theo dõi các yêu cầu đang chờ refresh token\r\nlet isRefreshing = false;\r\nlet failedQueue: { resolve: (value: unknown) => void; reject: (reason?: any) => void }[] = [];\r\n\r\n/**\r\n * Xử lý hàng đợi các yêu cầu thất bại\r\n */\r\nfunction processQueue(error: any | null, token: string | null = null) {\r\n  failedQueue.forEach(prom => {\r\n    if (error) {\r\n      prom.reject(error);\r\n    } else {\r\n      prom.resolve(token);\r\n    }\r\n  });\r\n\r\n  failedQueue = [];\r\n}\r\n\r\n/**\r\n * Hàm thử refresh token và thực hiện lại yêu cầu\r\n */\r\nasync function handleTokenRefresh() {\r\n  try {\r\n    if (!isRefreshing) {\r\n      isRefreshing = true;\r\n      \r\n      const success = await authService.tryRefreshToken();\r\n      isRefreshing = false;\r\n      \r\n\r\n      if (success) {\r\n        // Lấy token mới\r\n        const newToken = authService.getAccessToken();\r\n        // Xử lý hàng đợi với token mới\r\n        processQueue(null, newToken);\r\n\r\n        // Lấy thông tin người dùng hiện tại\r\n        const currentUser = authService.getCurrentUser();\r\n        if (currentUser) {\r\n          \r\n\r\n          // Luôn điều hướng người dùng về trang phù hợp với vai trò của họ\r\n          authService.redirectBasedOnRole(currentUser);\r\n        }\r\n\r\n        return newToken;\r\n      } else {\r\n        // Nếu refresh token thất bại, đăng xuất người dùng\r\n        \r\n        authService.logout();\r\n        processQueue(new Error('Refresh token failed'));\r\n        return null;\r\n      }\r\n    } else {\r\n      \r\n      // Nếu đang refresh token, thêm yêu cầu vào hàng đợi\r\n      return new Promise((resolve, reject) => {\r\n        failedQueue.push({ resolve, reject });\r\n      });\r\n    }\r\n  } catch (error) {\r\n    console.error(\"API: Error during token refresh:\", error);\r\n    isRefreshing = false;\r\n    processQueue(error);\r\n    throw error;\r\n  }\r\n}\r\n\r\n/**\r\n * Hàm fetch API với xử lý lỗi và headers mặc định\r\n */\r\nexport async function fetchApi<T>(\r\n  endpoint: string,\r\n  options: RequestInit = {}\r\n): Promise<T> {\r\n  const url = `${NEXT_PUBLIC_API_URL}${endpoint.startsWith('/') ? endpoint : `/${endpoint}`}`;\r\n\r\n  // Hàm để thực hiện yêu cầu API\r\n  const executeRequest = async (token?: string | null) => {\r\n    // Tạo headers mới\r\n    const headers = new Headers(options.headers);\r\n    headers.set('Content-Type', 'application/json');\r\n\r\n    // Thêm token xác thực nếu có\r\n    const accessToken = token || (typeof window !== 'undefined' ? localStorage.getItem('accessToken') : null);\r\n    if (accessToken) {\r\n      headers.set('Authorization', `Bearer ${accessToken}`);\r\n    }\r\n\r\n    const config = {\r\n      ...options,\r\n      headers,\r\n    };\r\n\r\n    const response = await fetch(url, config);\r\n\r\n    // Kiểm tra nếu response không thành công\r\n    if (!response.ok) {\r\n      const errorData = await response.json().catch(() => ({}));\r\n      // Xử lý cấu trúc lỗi từ backend\r\n      const errorMessage = errorData.message ||\r\n                         (errorData.data && errorData.data.message) ||\r\n                         `API request failed with status ${response.status}`;\r\n      console.error('API Error:', errorData);\r\n\r\n      // Tạo error object với thông tin chi tiết hơn\r\n      const error: any = new Error(errorMessage);\r\n      error.status = response.status;\r\n      error.data = errorData;\r\n      throw error;\r\n    }\r\n\r\n    // Parse JSON response\r\n    const responseData = await response.json();\r\n\r\n    // Kiểm tra cấu trúc response từ backend (ApiResponseDto)\r\n    if (responseData.data !== undefined) {\r\n      // Trường hợp response có cấu trúc ApiResponseDto\r\n      return responseData.data;\r\n    } else {\r\n      // Trường hợp response không có cấu trúc ApiResponseDto\r\n      console.warn('API response does not follow standard structure:', responseData);\r\n      return responseData;\r\n    }\r\n  };\r\n\r\n  try {\r\n    // Thử thực hiện yêu cầu\r\n    return await executeRequest();\r\n  } catch (error: any) {\r\n    // Nếu lỗi 401 Unauthorized và không phải là yêu cầu đăng nhập/đăng ký/refresh token\r\n    if (error.status === 401 &&\r\n        !endpoint.includes('auth/login') &&\r\n        !endpoint.includes('auth/register') &&\r\n        !endpoint.includes('auth/refresh-token')) {\r\n      try {\r\n        // Thử refresh token\r\n        const newToken = await handleTokenRefresh();\r\n        if (newToken) {\r\n          // Thực hiện lại yêu cầu với token mới\r\n          return await executeRequest(newToken);\r\n        }\r\n      } catch (refreshError) {\r\n        console.error('Error refreshing token:', refreshError);\r\n        // Nếu refresh token thất bại, đăng xuất người dùng\r\n        authService.logout();\r\n        throw error; // Trả về lỗi ban đầu\r\n      }\r\n    }\r\n\r\n    console.error('API request error:', error);\r\n    throw error;\r\n  }\r\n}\r\n\r\n/**\r\n * Các phương thức HTTP\r\n */\r\nexport const api = {\r\n  baseUrl: NEXT_PUBLIC_API_URL,\r\n\r\n  getToken: () => {\r\n    return typeof window !== 'undefined' ? localStorage.getItem('accessToken') : null;\r\n  },\r\n\r\n  get: <T>(endpoint: string, options?: RequestInit) =>\r\n    fetchApi<T>(endpoint, { ...options, method: 'GET' }),\r\n\r\n  post: <T>(endpoint: string, data?: any, options?: RequestInit) =>\r\n    fetchApi<T>(endpoint, {\r\n      ...options,\r\n      method: 'POST',\r\n      body: data ? JSON.stringify(data) : undefined,\r\n    }),\r\n\r\n  put: <T>(endpoint: string, data?: any, options?: RequestInit) =>\r\n    fetchApi<T>(endpoint, {\r\n      ...options,\r\n      method: 'PUT',\r\n      body: data ? JSON.stringify(data) : undefined,\r\n    }),\r\n\r\n  patch: <T>(endpoint: string, data?: any, options?: RequestInit) =>\r\n    fetchApi<T>(endpoint, {\r\n      ...options,\r\n      method: 'PATCH',\r\n      body: data ? JSON.stringify(data) : undefined,\r\n    }),\r\n\r\n  delete: <T>(endpoint: string, options?: RequestInit) =>\r\n    fetchApi<T>(endpoint, { ...options, method: 'DELETE' }),\r\n\r\n  // Phương thức download file\r\n  downloadFile: async (endpoint: string, format: string, filename: string) => {\r\n    const url = `${NEXT_PUBLIC_API_URL}${endpoint.startsWith('/') ? endpoint : `/${endpoint}`}`;\r\n    const token = typeof window !== 'undefined' ? localStorage.getItem('accessToken') : null;\r\n\r\n    try {\r\n      // Sử dụng XMLHttpRequest thay vì fetch để xử lý tải file tốt hơn\r\n      return new Promise((resolve, reject) => {\r\n        const xhr = new XMLHttpRequest();\r\n        xhr.open('GET', url, true);\r\n        xhr.responseType = 'blob';\r\n        xhr.setRequestHeader('Authorization', token ? `Bearer ${token}` : '');\r\n\r\n        xhr.onload = function() {\r\n          if (this.status === 200) {\r\n            const blob = new Blob([this.response], {\r\n              type: format === 'csv' ? 'text/csv' : 'application/json'\r\n            });\r\n            const downloadUrl = window.URL.createObjectURL(blob);\r\n\r\n            const a = document.createElement('a');\r\n            a.href = downloadUrl;\r\n            a.download = filename;\r\n            document.body.appendChild(a);\r\n            a.click();\r\n\r\n            // Cleanup\r\n            window.URL.revokeObjectURL(downloadUrl);\r\n            document.body.removeChild(a);\r\n\r\n            resolve(true);\r\n          } else {\r\n            reject(new Error(`Download failed: ${this.status} ${this.statusText}`));\r\n          }\r\n        };\r\n\r\n        xhr.onerror = function() {\r\n          reject(new Error('Network error occurred'));\r\n        };\r\n\r\n        xhr.send();\r\n      });\r\n    } catch (error) {\r\n      console.error('Download error:', error);\r\n      throw error;\r\n    }\r\n  }\r\n};\r\n"], "names": [], "mappings": "AAAA;;CAEC;;;;AACD;;AAEA,MAAM,sBAAsB,oEAAmC;AAE/D,sDAAsD;AACtD,IAAI,eAAe;AACnB,IAAI,cAAuF,EAAE;AAE7F;;CAEC,GACD,SAAS,aAAa,KAAiB,EAAE,QAAuB,IAAI;IAClE,YAAY,OAAO,CAAC,CAAA;QAClB,IAAI,OAAO;YACT,KAAK,MAAM,CAAC;QACd,OAAO;YACL,KAAK,OAAO,CAAC;QACf;IACF;IAEA,cAAc,EAAE;AAClB;AAEA;;CAEC,GACD,eAAe;IACb,IAAI;QACF,IAAI,CAAC,cAAc;YACjB,eAAe;YAEf,MAAM,UAAU,MAAM,2GAAA,CAAA,cAAW,CAAC,eAAe;YACjD,eAAe;YAGf,IAAI,SAAS;gBACX,gBAAgB;gBAChB,MAAM,WAAW,2GAAA,CAAA,cAAW,CAAC,cAAc;gBAC3C,+BAA+B;gBAC/B,aAAa,MAAM;gBAEnB,oCAAoC;gBACpC,MAAM,cAAc,2GAAA,CAAA,cAAW,CAAC,cAAc;gBAC9C,IAAI,aAAa;oBAGf,iEAAiE;oBACjE,2GAAA,CAAA,cAAW,CAAC,mBAAmB,CAAC;gBAClC;gBAEA,OAAO;YACT,OAAO;gBACL,mDAAmD;gBAEnD,2GAAA,CAAA,cAAW,CAAC,MAAM;gBAClB,aAAa,IAAI,MAAM;gBACvB,OAAO;YACT;QACF,OAAO;YAEL,oDAAoD;YACpD,OAAO,IAAI,QAAQ,CAAC,SAAS;gBAC3B,YAAY,IAAI,CAAC;oBAAE;oBAAS;gBAAO;YACrC;QACF;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,oCAAoC;QAClD,eAAe;QACf,aAAa;QACb,MAAM;IACR;AACF;AAKO,eAAe,SACpB,QAAgB,EAChB,UAAuB,CAAC,CAAC;IAEzB,MAAM,MAAM,GAAG,sBAAsB,SAAS,UAAU,CAAC,OAAO,WAAW,CAAC,CAAC,EAAE,UAAU,EAAE;IAE3F,+BAA+B;IAC/B,MAAM,iBAAiB,OAAO;QAC5B,kBAAkB;QAClB,MAAM,UAAU,IAAI,QAAQ,QAAQ,OAAO;QAC3C,QAAQ,GAAG,CAAC,gBAAgB;QAE5B,6BAA6B;QAC7B,MAAM,cAAc,SAAS,CAAC,6EAAsE,IAAI;QACxG,IAAI,aAAa;YACf,QAAQ,GAAG,CAAC,iBAAiB,CAAC,OAAO,EAAE,aAAa;QACtD;QAEA,MAAM,SAAS;YACb,GAAG,OAAO;YACV;QACF;QAEA,MAAM,WAAW,MAAM,MAAM,KAAK;QAElC,yCAAyC;QACzC,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,YAAY,MAAM,SAAS,IAAI,GAAG,KAAK,CAAC,IAAM,CAAC,CAAC,CAAC;YACvD,gCAAgC;YAChC,MAAM,eAAe,UAAU,OAAO,IAClB,UAAU,IAAI,IAAI,UAAU,IAAI,CAAC,OAAO,IACzC,CAAC,+BAA+B,EAAE,SAAS,MAAM,EAAE;YACtE,QAAQ,KAAK,CAAC,cAAc;YAE5B,8CAA8C;YAC9C,MAAM,QAAa,IAAI,MAAM;YAC7B,MAAM,MAAM,GAAG,SAAS,MAAM;YAC9B,MAAM,IAAI,GAAG;YACb,MAAM;QACR;QAEA,sBAAsB;QACtB,MAAM,eAAe,MAAM,SAAS,IAAI;QAExC,yDAAyD;QACzD,IAAI,aAAa,IAAI,KAAK,WAAW;YACnC,iDAAiD;YACjD,OAAO,aAAa,IAAI;QAC1B,OAAO;YACL,uDAAuD;YACvD,QAAQ,IAAI,CAAC,oDAAoD;YACjE,OAAO;QACT;IACF;IAEA,IAAI;QACF,wBAAwB;QACxB,OAAO,MAAM;IACf,EAAE,OAAO,OAAY;QACnB,oFAAoF;QACpF,IAAI,MAAM,MAAM,KAAK,OACjB,CAAC,SAAS,QAAQ,CAAC,iBACnB,CAAC,SAAS,QAAQ,CAAC,oBACnB,CAAC,SAAS,QAAQ,CAAC,uBAAuB;YAC5C,IAAI;gBACF,oBAAoB;gBACpB,MAAM,WAAW,MAAM;gBACvB,IAAI,UAAU;oBACZ,sCAAsC;oBACtC,OAAO,MAAM,eAAe;gBAC9B;YACF,EAAE,OAAO,cAAc;gBACrB,QAAQ,KAAK,CAAC,2BAA2B;gBACzC,mDAAmD;gBACnD,2GAAA,CAAA,cAAW,CAAC,MAAM;gBAClB,MAAM,OAAO,qBAAqB;YACpC;QACF;QAEA,QAAQ,KAAK,CAAC,sBAAsB;QACpC,MAAM;IACR;AACF;AAKO,MAAM,MAAM;IACjB,SAAS;IAET,UAAU;QACR,OAAO,6EAAsE;IAC/E;IAEA,KAAK,CAAI,UAAkB,UACzB,SAAY,UAAU;YAAE,GAAG,OAAO;YAAE,QAAQ;QAAM;IAEpD,MAAM,CAAI,UAAkB,MAAY,UACtC,SAAY,UAAU;YACpB,GAAG,OAAO;YACV,QAAQ;YACR,MAAM,OAAO,KAAK,SAAS,CAAC,QAAQ;QACtC;IAEF,KAAK,CAAI,UAAkB,MAAY,UACrC,SAAY,UAAU;YACpB,GAAG,OAAO;YACV,QAAQ;YACR,MAAM,OAAO,KAAK,SAAS,CAAC,QAAQ;QACtC;IAEF,OAAO,CAAI,UAAkB,MAAY,UACvC,SAAY,UAAU;YACpB,GAAG,OAAO;YACV,QAAQ;YACR,MAAM,OAAO,KAAK,SAAS,CAAC,QAAQ;QACtC;IAEF,QAAQ,CAAI,UAAkB,UAC5B,SAAY,UAAU;YAAE,GAAG,OAAO;YAAE,QAAQ;QAAS;IAEvD,4BAA4B;IAC5B,cAAc,OAAO,UAAkB,QAAgB;QACrD,MAAM,MAAM,GAAG,sBAAsB,SAAS,UAAU,CAAC,OAAO,WAAW,CAAC,CAAC,EAAE,UAAU,EAAE;QAC3F,MAAM,QAAQ,6EAAsE;QAEpF,IAAI;YACF,iEAAiE;YACjE,OAAO,IAAI,QAAQ,CAAC,SAAS;gBAC3B,MAAM,MAAM,IAAI;gBAChB,IAAI,IAAI,CAAC,OAAO,KAAK;gBACrB,IAAI,YAAY,GAAG;gBACnB,IAAI,gBAAgB,CAAC,iBAAiB,6EAA4B;gBAElE,IAAI,MAAM,GAAG;oBACX,IAAI,IAAI,CAAC,MAAM,KAAK,KAAK;wBACvB,MAAM,OAAO,IAAI,KAAK;4BAAC,IAAI,CAAC,QAAQ;yBAAC,EAAE;4BACrC,MAAM,WAAW,QAAQ,aAAa;wBACxC;wBACA,MAAM,cAAc,OAAO,GAAG,CAAC,eAAe,CAAC;wBAE/C,MAAM,IAAI,SAAS,aAAa,CAAC;wBACjC,EAAE,IAAI,GAAG;wBACT,EAAE,QAAQ,GAAG;wBACb,SAAS,IAAI,CAAC,WAAW,CAAC;wBAC1B,EAAE,KAAK;wBAEP,UAAU;wBACV,OAAO,GAAG,CAAC,eAAe,CAAC;wBAC3B,SAAS,IAAI,CAAC,WAAW,CAAC;wBAE1B,QAAQ;oBACV,OAAO;wBACL,OAAO,IAAI,MAAM,CAAC,iBAAiB,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC,EAAE,IAAI,CAAC,UAAU,EAAE;oBACvE;gBACF;gBAEA,IAAI,OAAO,GAAG;oBACZ,OAAO,IAAI,MAAM;gBACnB;gBAEA,IAAI,IAAI;YACV;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,mBAAmB;YACjC,MAAM;QACR;IACF;AACF", "debugId": null}}, {"offset": {"line": 649, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/bloomgoo/gold-exchange-platform/frontend/lib/local-storage-utils.ts"], "sourcesContent": ["/**\r\n * Check if cached data is still valid\r\n */\r\nexport function isCacheValid(cache: any): boolean {\r\n    if (!cache) return false;\r\n\r\n    const now = Date.now();\r\n    return now < cache.expiresAt;\r\n}\r\n\r\n/**\r\n* Clear cache\r\n*/\r\nexport function clearCacheByKey(cacheKey: string): void {\r\n    if (typeof window !== 'undefined') {\r\n        localStorage.removeItem(cacheKey);\r\n    }\r\n}\r\n\r\n/**\r\n * Clear all cache\r\n */\r\nexport function clearAllCache(): void {\r\n    if (typeof window !== 'undefined') {\r\n        localStorage.clear();\r\n    }\r\n}\r\n\r\n/**\r\n * Save cache to localStorage\r\n */\r\nexport function saveCacheToStorage(cacheKey: string, cache: any): void {\r\n    if (typeof window === 'undefined' || !cache) return;\r\n\r\n    try {\r\n        localStorage.setItem(cacheKey, JSON.stringify(cache));\r\n    } catch (error) {\r\n        console.error('Failed to save metadata cache:', error);\r\n    }\r\n}\r\n\r\n/**\r\n * Load cache from localStorage\r\n */\r\nexport function loadCacheFromStorage(cacheKey: string): void {\r\n    if (typeof window === 'undefined') return;\r\n\r\n    try {\r\n        const cached = localStorage.getItem(cacheKey);\r\n        if (cached) {\r\n            return JSON.parse(cached);\r\n        }\r\n    } catch (error) {\r\n        console.error('Failed to load metadata cache:', error);\r\n    }\r\n}\r\n\r\nexport function loadAllCache(): any[] {\r\n    if (typeof window === 'undefined') return [];\r\n    const cacheKeys = Object.keys(localStorage);\r\n    try {\r\n        const cached = cacheKeys.map(key => {\r\n            const cached = localStorage.getItem(key);\r\n            if (cached) {\r\n                return JSON.parse(cached);\r\n            }\r\n        });\r\n        return cached;\r\n    } catch (error) {\r\n        console.error('Failed to load metadata cache:', error);\r\n        return [];\r\n    }\r\n}\r\n"], "names": [], "mappings": "AAAA;;CAEC;;;;;;;;AACM,SAAS,aAAa,KAAU;IACnC,IAAI,CAAC,OAAO,OAAO;IAEnB,MAAM,MAAM,KAAK,GAAG;IACpB,OAAO,MAAM,MAAM,SAAS;AAChC;AAKO,SAAS,gBAAgB,QAAgB;IAC5C,uCAAmC;;IAEnC;AACJ;AAKO,SAAS;IACZ,uCAAmC;;IAEnC;AACJ;AAKO,SAAS,mBAAmB,QAAgB,EAAE,KAAU;IAC3D,wCAA6C;;AAOjD;AAKO,SAAS,qBAAqB,QAAgB;IACjD,wCAAmC;;AAUvC;AAEO,SAAS;IACZ,wCAAmC,OAAO,EAAE;;IAC5C,MAAM;AAaV", "debugId": null}}, {"offset": {"line": 693, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/bloomgoo/gold-exchange-platform/frontend/lib/system-metadata.ts"], "sourcesContent": ["/**\r\n * Site Metadata Service\r\n * Handles fetching, caching, and managing site metadata from system config API\r\n */\r\n\r\nimport { api } from '@/lib/api';\r\nimport { SystemConfigResponse } from '@/types/site-metadata';\r\nimport { clearAllCache, loadAllCache, saveCacheToStorage } from './local-storage-utils';\r\n\r\nconst CACHE_KEY = 'site_metadata_cache';\r\nconst DEFAULT_TTL = 24 * 60 * 60 * 1000; // 24 hours in milliseconds\r\n\r\nexport class SiteMetadataService {\r\n  private static instance: SiteMetadataService;\r\n  private cache: {\r\n    data: SystemConfigResponse;\r\n    cachedAt: number;\r\n    expiresAt: number;\r\n  } | null = null;\r\n\r\n  private constructor() {\r\n    loadAllCache();\r\n  }\r\n\r\n  public static getInstance(): SiteMetadataService {\r\n    if (!SiteMetadataService.instance) {\r\n      SiteMetadataService.instance = new SiteMetadataService();\r\n    }\r\n    return SiteMetadataService.instance;\r\n  }\r\n\r\n\r\n\r\n\r\n\r\n\r\n  /**\r\n   * Fetch system config from backend API\r\n   */\r\n  private async fetchSystemConfigFromAPI(): Promise<SystemConfigResponse> {\r\n    // Only fetch configs for SEO, header, and footer groups\r\n    return await api.get<SystemConfigResponse>(\r\n      '/public/system-configs?limit=300&filter=configGroup:website_seo|general',\r\n      {\r\n        cache: \"no-cache\",\r\n      }\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Get metadata from system config\r\n   */\r\n  public async getMetadata(): Promise<SystemConfigResponse> {\r\n    // // Check cache first\r\n    // if (this.isCacheValid() && this.cache) {\r\n    //   return this.cache.data;\r\n    // }\r\n\r\n    try {\r\n      // Always fetch from API first\r\n      const systemConfig = await this.fetchSystemConfigFromAPI();\r\n\r\n      // Cache the result\r\n      const now = Date.now();\r\n      this.cache = {\r\n        data: systemConfig,\r\n        cachedAt: now,\r\n        expiresAt: now + DEFAULT_TTL,\r\n      };\r\n\r\n      // Save to localStorage\r\n      saveCacheToStorage(CACHE_KEY, this.cache);\r\n\r\n      return systemConfig;\r\n    } catch (error) {\r\n      console.error('Failed to fetch system config:', error);\r\n\r\n      // If API call fails, return cached data if available\r\n      if (this.cache) {\r\n        return this.cache.data;\r\n      }\r\n\r\n      return {};\r\n    }\r\n  }\r\n\r\n\r\n  /**\r\n   * Refresh metadata\r\n   */\r\n  public async refreshMetadata(): Promise<SystemConfigResponse> {\r\n    clearAllCache();\r\n    return this.getMetadata();\r\n  }\r\n\r\n  /**\r\n   * Get metadata from system config by group\r\n   */\r\n  public async getMetadataByGroup(configGroup: string): Promise<SystemConfigResponse> {\r\n    const CACHE_KEY_GROUP = `site_metadata_cache_${configGroup}`;\r\n\r\n    // Check cache first\r\n    // if (this.isCacheValid() && this.cache) {\r\n    //   return this.cache.data;\r\n    // }\r\n\r\n    try {\r\n      // Always fetch from API first\r\n      const systemConfig = await api.get<SystemConfigResponse>(\r\n        `/public/system-configs?limit=100&filter=configGroup:${configGroup}`\r\n      );\r\n\r\n      // Cache the result\r\n      const now = Date.now();\r\n      const cacheObj = {\r\n        data: systemConfig,\r\n        cachedAt: now,\r\n        expiresAt: now + DEFAULT_TTL,\r\n      };\r\n\r\n      // Save to localStorage\r\n      saveCacheToStorage(CACHE_KEY_GROUP, cacheObj);\r\n\r\n      return systemConfig;\r\n    } catch (error) {\r\n\r\n      // If API call fails, try to get from cache\r\n      if (typeof window !== 'undefined') {\r\n        const cached = localStorage.getItem(CACHE_KEY_GROUP);\r\n        if (cached) {\r\n          return JSON.parse(cached).data;\r\n        }\r\n      }\r\n\r\n      return {};\r\n    }\r\n  }\r\n}\r\n\r\n// Export singleton instance\r\nexport const siteMetadataService = SiteMetadataService.getInstance();\r\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;AAED;AAEA;;;AAEA,MAAM,YAAY;AAClB,MAAM,cAAc,KAAK,KAAK,KAAK,MAAM,2BAA2B;AAE7D,MAAM;IACX,OAAe,SAA8B;IACrC,QAIG,KAAK;IAEhB,aAAsB;QACpB,CAAA,GAAA,gIAAA,CAAA,eAAY,AAAD;IACb;IAEA,OAAc,cAAmC;QAC/C,IAAI,CAAC,oBAAoB,QAAQ,EAAE;YACjC,oBAAoB,QAAQ,GAAG,IAAI;QACrC;QACA,OAAO,oBAAoB,QAAQ;IACrC;IAOA;;GAEC,GACD,MAAc,2BAA0D;QACtE,wDAAwD;QACxD,OAAO,MAAM,0GAAA,CAAA,MAAG,CAAC,GAAG,CAClB,2EACA;YACE,OAAO;QACT;IAEJ;IAEA;;GAEC,GACD,MAAa,cAA6C;QACxD,uBAAuB;QACvB,2CAA2C;QAC3C,4BAA4B;QAC5B,IAAI;QAEJ,IAAI;YACF,8BAA8B;YAC9B,MAAM,eAAe,MAAM,IAAI,CAAC,wBAAwB;YAExD,mBAAmB;YACnB,MAAM,MAAM,KAAK,GAAG;YACpB,IAAI,CAAC,KAAK,GAAG;gBACX,MAAM;gBACN,UAAU;gBACV,WAAW,MAAM;YACnB;YAEA,uBAAuB;YACvB,CAAA,GAAA,gIAAA,CAAA,qBAAkB,AAAD,EAAE,WAAW,IAAI,CAAC,KAAK;YAExC,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,kCAAkC;YAEhD,qDAAqD;YACrD,IAAI,IAAI,CAAC,KAAK,EAAE;gBACd,OAAO,IAAI,CAAC,KAAK,CAAC,IAAI;YACxB;YAEA,OAAO,CAAC;QACV;IACF;IAGA;;GAEC,GACD,MAAa,kBAAiD;QAC5D,CAAA,GAAA,gIAAA,CAAA,gBAAa,AAAD;QACZ,OAAO,IAAI,CAAC,WAAW;IACzB;IAEA;;GAEC,GACD,MAAa,mBAAmB,WAAmB,EAAiC;QAClF,MAAM,kBAAkB,CAAC,oBAAoB,EAAE,aAAa;QAE5D,oBAAoB;QACpB,2CAA2C;QAC3C,4BAA4B;QAC5B,IAAI;QAEJ,IAAI;YACF,8BAA8B;YAC9B,MAAM,eAAe,MAAM,0GAAA,CAAA,MAAG,CAAC,GAAG,CAChC,CAAC,oDAAoD,EAAE,aAAa;YAGtE,mBAAmB;YACnB,MAAM,MAAM,KAAK,GAAG;YACpB,MAAM,WAAW;gBACf,MAAM;gBACN,UAAU;gBACV,WAAW,MAAM;YACnB;YAEA,uBAAuB;YACvB,CAAA,GAAA,gIAAA,CAAA,qBAAkB,AAAD,EAAE,iBAAiB;YAEpC,OAAO;QACT,EAAE,OAAO,OAAO;YAEd,2CAA2C;YAC3C,uCAAmC;;YAKnC;YAEA,OAAO,CAAC;QACV;IACF;AACF;AAGO,MAAM,sBAAsB,oBAAoB,WAAW", "debugId": null}}, {"offset": {"line": 797, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/geist_e531dabc.module.css [app-rsc] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"className\": \"geist_e531dabc-module__QGiZLq__className\",\n  \"variable\": \"geist_e531dabc-module__QGiZLq__variable\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA", "ignoreList": [0]}}, {"offset": {"line": 807, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/geist_e531dabc.js"], "sourcesContent": ["import cssModule from \"@vercel/turbopack-next/internal/font/google/cssmodule.module.css?{%22path%22:%22layout.tsx%22,%22import%22:%22Geist%22,%22arguments%22:[{%22variable%22:%22--font-geist-sans%22,%22subsets%22:[%22latin%22]}],%22variableName%22:%22geistSans%22}\";\nconst fontData = {\n    className: cssModule.className,\n    style: {\n        fontFamily: \"'Geist', 'Geist Fallback'\",\n        fontStyle: \"normal\",\n\n    },\n};\n\nif (cssModule.variable != null) {\n    fontData.variable = cssModule.variable;\n}\n\nexport default fontData;\n"], "names": [], "mappings": ";;;AAAA;;AACA,MAAM,WAAW;IACb,WAAW,qJAAA,CAAA,UAAS,CAAC,SAAS;IAC9B,OAAO;QACH,YAAY;QACZ,WAAW;IAEf;AACJ;AAEA,IAAI,qJAAA,CAAA,UAAS,CAAC,QAAQ,IAAI,MAAM;IAC5B,SAAS,QAAQ,GAAG,qJAAA,CAAA,UAAS,CAAC,QAAQ;AAC1C;uCAEe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 828, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/geist_mono_68a01160.module.css [app-rsc] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"className\": \"geist_mono_68a01160-module__YLcDdW__className\",\n  \"variable\": \"geist_mono_68a01160-module__YLcDdW__variable\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA", "ignoreList": [0]}}, {"offset": {"line": 838, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/geist_mono_68a01160.js"], "sourcesContent": ["import cssModule from \"@vercel/turbopack-next/internal/font/google/cssmodule.module.css?{%22path%22:%22layout.tsx%22,%22import%22:%22Geist_Mono%22,%22arguments%22:[{%22variable%22:%22--font-geist-mono%22,%22subsets%22:[%22latin%22]}],%22variableName%22:%22geistMono%22}\";\nconst fontData = {\n    className: cssModule.className,\n    style: {\n        fontFamily: \"'Geist Mono', 'Geist Mono Fallback'\",\n        fontStyle: \"normal\",\n\n    },\n};\n\nif (cssModule.variable != null) {\n    fontData.variable = cssModule.variable;\n}\n\nexport default fontData;\n"], "names": [], "mappings": ";;;AAAA;;AACA,MAAM,WAAW;IACb,WAAW,0JAAA,CAAA,UAAS,CAAC,SAAS;IAC9B,OAAO;QACH,YAAY;QACZ,WAAW;IAEf;AACJ;AAEA,IAAI,0JAAA,CAAA,UAAS,CAAC,QAAQ,IAAI,MAAM;IAC5B,SAAS,QAAQ,GAAG,0JAAA,CAAA,UAAS,CAAC,QAAQ;AAC1C;uCAEe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 860, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/bloomgoo/gold-exchange-platform/frontend/app/layout.tsx"], "sourcesContent": ["import { WebSocketProvider } from \"@/components/providers/WebSocketProvider\";\r\nimport { Toaster } from \"@/components/ui/sonner\";\r\nimport { AuthProvider } from \"@/hooks/use-auth\";\r\nimport { DynamicMetadata } from \"@/components/dynamic-metadata\";\r\nimport { siteMetadataService } from \"@/lib/system-metadata\";\r\nimport \"@/styles/sticky-columns.css\";\r\nimport type { Metadata } from \"next\";\r\nimport { <PERSON><PERSON><PERSON>, Geist_Mono } from \"next/font/google\";\r\nimport \"./globals.css\";\r\n\r\nconst geistSans = Geist({\r\n  variable: \"--font-geist-sans\",\r\n  subsets: [\"latin\"],\r\n});\r\n\r\nconst geistMono = Geist_Mono({\r\n  variable: \"--font-geist-mono\",\r\n  subsets: [\"latin\"],\r\n});\r\n\r\n// Dynamic metadata will be handled by DynamicMetadata component\r\n// This is just a fallback for SSR\r\nexport async function generateMetadata(): Promise<Metadata> {\r\n  try {\r\n    // Try to get metadata from service (will use default if API fails)\r\n    const config = await siteMetadataService.getMetadata();\r\n\r\n    return {\r\n      title: config['website_seo_title'] || 'PHYGITAL-V Platform',\r\n      description: config['website_seo_description'] || 'Digital Gold Exchange Platform',\r\n      keywords: config['website_seo_keywords'] || undefined,\r\n      authors: config['website_seo_author'] ? [{ name: config['website_seo_author'] }] : undefined,\r\n      creator: config['website_seo_author'] || undefined,\r\n      publisher: config['website_company_name'] || undefined,\r\n\r\n      icons: {\r\n        icon: '/favicon_sgs.ico',\r\n        apple: config['website_apple_touch_icon'] || undefined,\r\n      },\r\n\r\n      manifest: config['website_manifest_url'] || undefined,\r\n\r\n      openGraph: {\r\n        title: config['website_seo_og_title'] || config['website_seo_title'] || 'PHYGITAL-V Platform',\r\n        description: config['website_seo_og_description'] || config['website_seo_description'] || 'Digital Gold Exchange Platform',\r\n        url: config['website_site_url'] || undefined,\r\n        siteName: config['website_site_name'] || undefined,\r\n        images: config['website_seo_og_image'] ? [\r\n          {\r\n            url: config['website_seo_og_image'],\r\n            width: 1200,\r\n            height: 630,\r\n            alt: config['website_seo_og_title'] || config['website_seo_title'] || 'PHYGITAL-V Platform',\r\n          },\r\n        ] : undefined,\r\n        locale: config['website_locale'] || undefined,\r\n        type: 'website',\r\n      },\r\n\r\n      twitter: {\r\n        card: 'summary_large_image',\r\n        title: config['website_seo_og_title'] || config['website_seo_title'] || 'PHYGITAL-V Platform',\r\n        description: config['website_seo_og_description'] || config['website_seo_description'] || 'Digital Gold Exchange Platform',\r\n        site: config['website_seo_twitter_site'] || undefined,\r\n        creator: config['website_seo_twitter_creator'] || undefined,\r\n        images: config['website_seo_twitter_image'] ? [config['website_seo_twitter_image']] : undefined,\r\n      },\r\n\r\n      robots: {\r\n        index: true,\r\n        follow: true,\r\n        googleBot: {\r\n          index: true,\r\n          follow: true,\r\n          'max-video-preview': -1,\r\n          'max-image-preview': 'large',\r\n          'max-snippet': -1,\r\n        },\r\n      },\r\n\r\n      verification: {\r\n        google: process.env.NEXT_PUBLIC_GOOGLE_VERIFICATION,\r\n      },\r\n    };\r\n  } catch (error) {\r\n    console.error('Failed to generate metadata:', error);\r\n\r\n    // Fallback metadata - minimal defaults\r\n    return {\r\n      title: \"Loading...\",\r\n      description: \"Please wait while we load the site information.\",\r\n      icons: {\r\n        icon: \"/favicon.ico\",\r\n      },\r\n    };\r\n  }\r\n}\r\n\r\nexport default function RootLayout({\r\n  children,\r\n}: Readonly<{\r\n  children: React.ReactNode;\r\n}>) {\r\n  return (\r\n    <html lang=\"en\">\r\n      <body\r\n        className={`${geistSans.variable} ${geistMono.variable} antialiased`}\r\n      >\r\n        <AuthProvider>\r\n          <WebSocketProvider>\r\n            <DynamicMetadata />\r\n            {children}\r\n            <Toaster richColors />\r\n          </WebSocketProvider>\r\n        </AuthProvider>\r\n      </body>\r\n    </html>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;AAkBO,eAAe;IACpB,IAAI;QACF,mEAAmE;QACnE,MAAM,SAAS,MAAM,yHAAA,CAAA,sBAAmB,CAAC,WAAW;QAEpD,OAAO;YACL,OAAO,MAAM,CAAC,oBAAoB,IAAI;YACtC,aAAa,MAAM,CAAC,0BAA0B,IAAI;YAClD,UAAU,MAAM,CAAC,uBAAuB,IAAI;YAC5C,SAAS,MAAM,CAAC,qBAAqB,GAAG;gBAAC;oBAAE,MAAM,MAAM,CAAC,qBAAqB;gBAAC;aAAE,GAAG;YACnF,SAAS,MAAM,CAAC,qBAAqB,IAAI;YACzC,WAAW,MAAM,CAAC,uBAAuB,IAAI;YAE7C,OAAO;gBACL,MAAM;gBACN,OAAO,MAAM,CAAC,2BAA2B,IAAI;YAC/C;YAEA,UAAU,MAAM,CAAC,uBAAuB,IAAI;YAE5C,WAAW;gBACT,OAAO,MAAM,CAAC,uBAAuB,IAAI,MAAM,CAAC,oBAAoB,IAAI;gBACxE,aAAa,MAAM,CAAC,6BAA6B,IAAI,MAAM,CAAC,0BAA0B,IAAI;gBAC1F,KAAK,MAAM,CAAC,mBAAmB,IAAI;gBACnC,UAAU,MAAM,CAAC,oBAAoB,IAAI;gBACzC,QAAQ,MAAM,CAAC,uBAAuB,GAAG;oBACvC;wBACE,KAAK,MAAM,CAAC,uBAAuB;wBACnC,OAAO;wBACP,QAAQ;wBACR,KAAK,MAAM,CAAC,uBAAuB,IAAI,MAAM,CAAC,oBAAoB,IAAI;oBACxE;iBACD,GAAG;gBACJ,QAAQ,MAAM,CAAC,iBAAiB,IAAI;gBACpC,MAAM;YACR;YAEA,SAAS;gBACP,MAAM;gBACN,OAAO,MAAM,CAAC,uBAAuB,IAAI,MAAM,CAAC,oBAAoB,IAAI;gBACxE,aAAa,MAAM,CAAC,6BAA6B,IAAI,MAAM,CAAC,0BAA0B,IAAI;gBAC1F,MAAM,MAAM,CAAC,2BAA2B,IAAI;gBAC5C,SAAS,MAAM,CAAC,8BAA8B,IAAI;gBAClD,QAAQ,MAAM,CAAC,4BAA4B,GAAG;oBAAC,MAAM,CAAC,4BAA4B;iBAAC,GAAG;YACxF;YAEA,QAAQ;gBACN,OAAO;gBACP,QAAQ;gBACR,WAAW;oBACT,OAAO;oBACP,QAAQ;oBACR,qBAAqB,CAAC;oBACtB,qBAAqB;oBACrB,eAAe,CAAC;gBAClB;YACF;YAEA,cAAc;gBACZ,QAAQ,QAAQ,GAAG,CAAC,+BAA+B;YACrD;QACF;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,gCAAgC;QAE9C,uCAAuC;QACvC,OAAO;YACL,OAAO;YACP,aAAa;YACb,OAAO;gBACL,MAAM;YACR;QACF;IACF;AACF;AAEe,SAAS,WAAW,EACjC,QAAQ,EAGR;IACA,qBACE,uVAAC;QAAK,MAAK;kBACT,cAAA,uVAAC;YACC,WAAW,GAAG,yIAAA,CAAA,UAAS,CAAC,QAAQ,CAAC,CAAC,EAAE,8IAAA,CAAA,UAAS,CAAC,QAAQ,CAAC,YAAY,CAAC;sBAEpE,cAAA,uVAAC,qHAAA,CAAA,eAAY;0BACX,cAAA,uVAAC,6IAAA,CAAA,oBAAiB;;sCAChB,uVAAC,kIAAA,CAAA,kBAAe;;;;;wBACf;sCACD,uVAAC,2HAAA,CAAA,UAAO;4BAAC,UAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;AAM/B", "debugId": null}}, {"offset": {"line": 1003, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/bloomgoo/gold-exchange-platform/frontend/node_modules/.pnpm/next%4015.3.0_%40opentelemetry%2B_ee4fdf7136c0ae57e36e5bb24f18c6a1/node_modules/next/src/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.ts"], "sourcesContent": ["module.exports = require('../../module.compiled').vendored[\n  'react-rsc'\n].ReactJsxDevRuntime\n"], "names": ["module", "exports", "require", "vendored", "ReactJsxDevRuntime"], "mappings": ";AAAAA,OAAOC,OAAO,GAAGC,QAAQ,4MAAyBC,QAAQ,CACxD,YACD,CAACC,kBAAkB", "ignoreList": [0], "debugId": null}}]}
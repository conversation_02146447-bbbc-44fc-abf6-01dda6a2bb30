import { UserDto } from '../../users/dto/user.dto';
import { CmsCustomerFeedbackStatus } from '../entity/cms-customer-feedbacks.entity';
export declare class CmsCustomerFeedbackDto {
    id: string;
    businessCode: string;
    customerName: string;
    customerTitleCompany?: string | null;
    feedbackText: string;
    rating?: number | null;
    avatarUrl?: string | null;
    productServiceName?: string | null;
    status: CmsCustomerFeedbackStatus;
    approvedBy?: string | null;
    createdAt: Date;
    updatedAt: Date;
    createdBy?: string;
    updatedBy?: string;
    deletedBy?: string;
    isDeleted: boolean;
    deletedAt?: Date;
    creator?: UserDto;
    updater?: UserDto;
    deleter?: UserDto;
    approver?: UserDto;
    ratingText?: string;
    ratingColor?: string;
    displayName?: string;
}

"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreateOrderBookService = void 0;
const common_1 = require("@nestjs/common");
const event_emitter_1 = require("@nestjs/event-emitter");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const typeorm_transactional_1 = require("typeorm-transactional");
const commission_processor_service_1 = require("../../agent-commissions/services/commission-processor.service");
const localized_http_exception_1 = require("../../common/exceptions/localized-http.exception");
const asset_service_1 = require("../../token-assets/asset.service");
const transaction_type_enum_1 = require("../../transactions/enums/transaction-type.enum");
const ecom_products_entity_1 = require("../../ecom-products/entity/ecom-products.entity");
const number_util_1 = require("../../common/utils/number.util");
const user_entity_1 = require("../../users/entities/user.entity");
const read_wallet_service_1 = require("../../wallets/services/read.wallet.service");
const update_wallet_service_1 = require("../../wallets/services/update.wallet.service");
const create_order_book_dto_1 = require("../dto/create-order-book.dto");
const order_book_detail_entity_1 = require("../entities/order-book-detail.entity");
const order_book_entity_1 = require("../entities/order-book.entity");
const approve_status_enum_1 = require("../enums/approve-status.enum");
const business_type_enum_1 = require("../enums/business-type.enum");
const order_status_enum_1 = require("../enums/order-status.enum");
const order_type_enum_1 = require("../enums/order-type.enum");
const base_order_book_service_1 = require("./base.order-book.service");
let CreateOrderBookService = class CreateOrderBookService extends base_order_book_service_1.BaseOrderBookService {
    orderBookRepository;
    orderBookDetailRepository;
    userRepository;
    ecomProductRepository;
    eventEmitter;
    readWalletService;
    updateWalletService;
    tokenAssetService;
    commissionProcessorService;
    constructor(orderBookRepository, orderBookDetailRepository, userRepository, ecomProductRepository, eventEmitter, readWalletService, updateWalletService, tokenAssetService, commissionProcessorService) {
        super(orderBookRepository, eventEmitter);
        this.orderBookRepository = orderBookRepository;
        this.orderBookDetailRepository = orderBookDetailRepository;
        this.userRepository = userRepository;
        this.ecomProductRepository = ecomProductRepository;
        this.eventEmitter = eventEmitter;
        this.readWalletService = readWalletService;
        this.updateWalletService = updateWalletService;
        this.tokenAssetService = tokenAssetService;
        this.commissionProcessorService = commissionProcessorService;
    }
    async create(createOrderBookDto, userId) {
        try {
            this.logger.log(`Đang tạo lệnh mới: ${createOrderBookDto.orderType}`);
            const validationResult = await this.validateInputAndAuthentication(createOrderBookDto, userId);
            const calculationResult = await this.calculateOrderPrices(createOrderBookDto, validationResult);
            const orderBook = await this.createOrderBookEntity(createOrderBookDto, createOrderBookDto.userId ? createOrderBookDto.userId : userId, calculationResult);
            const savedOrderBook = await this.orderBookRepository.save(orderBook);
            await this.createOrderBookDetails(createOrderBookDto.products, savedOrderBook.id, createOrderBookDto.userId ? createOrderBookDto.userId : userId);
            await this.processPaymentTransactions(createOrderBookDto, validationResult.wallet, savedOrderBook, calculationResult, createOrderBookDto.userId ? createOrderBookDto.userId : userId);
            await this.processCommissions(createOrderBookDto, calculationResult, createOrderBookDto.userId ? createOrderBookDto.userId : userId);
            return await this.finalizeOrderCreation(savedOrderBook.id, createOrderBookDto.userId ? createOrderBookDto.userId : userId);
        }
        catch (error) {
            this.logger.error(`Lỗi khi tạo lệnh: ${error.message}`, error.stack);
            if (error instanceof common_1.BadRequestException ||
                error instanceof localized_http_exception_1.InsufficientBalanceException ||
                error instanceof localized_http_exception_1.InsufficientTokenException) {
                throw error;
            }
            throw new common_1.InternalServerErrorException(`Không thể tạo lệnh: ${error.message}`);
        }
    }
    async validateInputAndAuthentication(createOrderBookDto, userId) {
        let user = null;
        if (createOrderBookDto.userId) {
            user = await this.userRepository.findOne({
                where: { id: createOrderBookDto.userId },
            });
        }
        else {
            user = await this.userRepository.findOne({
                where: { id: userId },
            });
        }
        if (!user) {
            throw new common_1.BadRequestException(`Người dùng với ID ${userId} không tồn tại`);
        }
        const wallet = await this.readWalletService.findByUserId(user.id);
        if (!wallet) {
            throw new common_1.BadRequestException(`Người dùng với ID ${user.id} không có ví`);
        }
        const productIds = createOrderBookDto.products
            .map((product) => product.productId)
            .filter((id) => Boolean(id));
        if (productIds.length === 0) {
            throw new common_1.BadRequestException('Phải có ít nhất một sản phẩm hợp lệ trong lệnh');
        }
        const products = await this.ecomProductRepository.findBy({
            id: (0, typeorm_2.In)(productIds),
        });
        if (products.length === 0) {
            throw new common_1.BadRequestException('Phải có ít nhất một sản phẩm hợp lệ trong lệnh');
        }
        return { user, wallet, products: products };
    }
    async calculateOrderPrices(createOrderBookDto, validationResult) {
        const processingPrice = this.calculateProcessingPrice(createOrderBookDto, validationResult.products);
        const totalPrice = this.calculateTotalPrice(createOrderBookDto, validationResult.products, processingPrice);
        if (createOrderBookDto.orderType === order_type_enum_1.OrderType.BUY) {
            return this.calculateBuyOrderPrices(createOrderBookDto, totalPrice, processingPrice);
        }
        else if (createOrderBookDto.orderType === order_type_enum_1.OrderType.SELL) {
            return this.calculateSellOrderPrices(totalPrice, processingPrice);
        }
        throw new common_1.BadRequestException(`Loại lệnh không hợp lệ: ${createOrderBookDto.orderType}`);
    }
    calculateTotalPrice(createOrderBookDto, products, processingPrice) {
        return createOrderBookDto.products.reduce((sum, orderProduct) => {
            const product = products.find(p => p.id === orderProduct.productId);
            if (!product) {
                this.logger.warn(`Không tìm thấy thông tin sản phẩm ${orderProduct.productId} trong database`);
                return sum;
            }
            const price = orderProduct.price != null
                ? Number(orderProduct.price)
                : Number(createOrderBookDto.price);
            const quantity = orderProduct.quantity != null ? Number(orderProduct.quantity) : 0;
            const weight = product.weight != null ? Number(product.weight) : 0;
            if (!isNaN(price) && !isNaN(quantity) && !isNaN(weight)) {
                let productTotal = (price * weight) * quantity;
                if (createOrderBookDto.businessType === business_type_enum_1.BusinessType.IMMEDIATE_DELIVERY) {
                    productTotal += processingPrice;
                    this.logger.log(`Phí gia công cho sản phẩm ${product.productName || product.id}: ${processingPrice} (${weight}oz * 50000 * ${quantity})`);
                }
                this.logger.log(`Tính giá cho sản phẩm ${product.productName || product.id}: ` +
                    `${price} * ${weight}oz * ${quantity} = ${productTotal}`);
                return sum + productTotal;
            }
            return sum;
        }, 0);
    }
    calculateProcessingPrice(createOrderBookDto, products) {
        return createOrderBookDto.products.reduce((sum, orderProduct) => {
            const product = products.find(p => p.id === orderProduct.productId);
            if (!product) {
                this.logger.warn(`Không tìm thấy thông tin sản phẩm ${orderProduct.productId} trong database`);
                return sum;
            }
            const weight = product.weight != null ? Number(product.weight) : 0;
            const quantity = orderProduct.quantity != null ? Number(orderProduct.quantity) : 0;
            const processingPrice = weight * 50000 * quantity;
            this.logger.log(`Phí gia công cho sản phẩm ${product.productName || product.id}: ${processingPrice} (${weight}oz * 50000 * ${quantity})`);
            return sum + processingPrice;
        }, 0);
    }
    calculateBuyOrderPrices(createOrderBookDto, totalPrice, processingPrice) {
        const businessType = createOrderBookDto.businessType || business_type_enum_1.BusinessType.NORMAL;
        if (businessType === business_type_enum_1.BusinessType.IMMEDIATE_DELIVERY) {
            this.logger.log(`Lệnh MUA bạc giao ngay: thanh toán 100% giá trị (${totalPrice}), phí gia công (${processingPrice})`);
            return {
                totalPrice,
                depositPrice: 0,
                processingPrice: processingPrice,
                settlementPrice: totalPrice,
                businessType: business_type_enum_1.BusinessType.IMMEDIATE_DELIVERY,
            };
        }
        else {
            const depositAmount = totalPrice * 0.1;
            this.logger.log(`Lệnh MUA thông thường: thanh toán 10% tiền cọc (${depositAmount})`);
            return {
                totalPrice,
                depositPrice: depositAmount,
                processingPrice: 0,
                settlementPrice: 0,
                businessType: business_type_enum_1.BusinessType.NORMAL,
            };
        }
    }
    calculateSellOrderPrices(totalPrice, processingPrice) {
        const depositPrice = totalPrice * 0.1;
        this.logger.log(`Lệnh BÁN khống: thanh toán 10% tiền cọc (${depositPrice})`);
        return {
            totalPrice,
            processingPrice: 0,
            depositPrice: depositPrice,
            settlementPrice: 0,
            businessType: business_type_enum_1.BusinessType.NORMAL,
        };
    }
    async createOrderBookEntity(createOrderBookDto, userId, calculationResult) {
        const orderBook = this.createBaseOrderBookEntity(createOrderBookDto, userId, calculationResult);
        if (createOrderBookDto.orderType === order_type_enum_1.OrderType.BUY) {
            this.configureBuyOrderEntity(orderBook, calculationResult);
        }
        else if (createOrderBookDto.orderType === order_type_enum_1.OrderType.SELL) {
            this.configureSellOrderEntity(orderBook, calculationResult);
        }
        else {
            throw new common_1.BadRequestException(`Loại lệnh không hợp lệ: ${createOrderBookDto.orderType}`);
        }
        return orderBook;
    }
    createBaseOrderBookEntity(createOrderBookDto, userId, calculationResult) {
        const orderBook = new order_book_entity_1.OrderBook();
        orderBook.userId = userId;
        orderBook.orderType = createOrderBookDto.orderType;
        orderBook.totalPrice = calculationResult.totalPrice;
        orderBook.businessType = calculationResult.businessType;
        orderBook.processingPrice = calculationResult.processingPrice;
        orderBook.storageFee = 0;
        orderBook.createdBy = userId;
        orderBook.updatedBy = userId;
        orderBook.contractNumber = `HD-${Date.now()}`;
        const settlementDate = new Date();
        settlementDate.setDate(settlementDate.getDate() + 15);
        orderBook.settlementDeadline = settlementDate;
        return orderBook;
    }
    configureBuyOrderEntity(orderBook, calculationResult) {
        if (calculationResult.businessType === business_type_enum_1.BusinessType.IMMEDIATE_DELIVERY) {
            orderBook.status = order_status_enum_1.OrderStatus.COMPLETED;
            orderBook.approveStatus = approve_status_enum_1.ApproveStatus.PENDING;
            orderBook.settlementAt = new Date();
            orderBook.depositPrice = 0;
            orderBook.settlementPrice = calculationResult.totalPrice;
            orderBook.processingPrice = calculationResult.processingPrice;
            this.logger.log(`Cấu hình lệnh MUA giao ngay: trạng thái COMPLETED, chờ phê duyệt, phí gia công ${calculationResult.processingPrice}`);
        }
        else {
            orderBook.status = order_status_enum_1.OrderStatus.DEPOSITED;
            orderBook.depositPrice = calculationResult.depositPrice;
            orderBook.settlementPrice = calculationResult.settlementPrice;
            orderBook.processingPrice = 0;
            this.logger.log(`Cấu hình lệnh MUA ký quỹ: trạng thái DEPOSITED, tiền cọc ${calculationResult.depositPrice}`);
        }
    }
    configureSellOrderEntity(orderBook, calculationResult) {
        orderBook.status = order_status_enum_1.OrderStatus.DEPOSITED;
        orderBook.depositPrice = calculationResult.depositPrice;
        orderBook.settlementPrice = calculationResult.settlementPrice;
        orderBook.processingPrice = 0;
        this.logger.log(`Cấu hình lệnh BÁN khống: trạng thái DEPOSITED, tiền cọc ${calculationResult.depositPrice}`);
    }
    async createOrderBookDetails(products, orderBookId, userId) {
        if (!products || products.length === 0) {
            this.logger.warn(`Không có sản phẩm để tạo chi tiết cho OrderBook ${orderBookId}`);
            return;
        }
        const orderBookDetails = products.map((product) => this.createSingleOrderBookDetail(product, orderBookId, userId));
        await this.orderBookDetailRepository.save(orderBookDetails);
        this.logger.log(`Đã tạo ${orderBookDetails.length} chi tiết cho OrderBook ${orderBookId}`);
    }
    createSingleOrderBookDetail(productInfo, orderBookId, userId) {
        const detail = new order_book_detail_entity_1.OrderBookDetail();
        detail.orderBookId = orderBookId;
        detail.productId = productInfo.productId || '';
        detail.createdBy = userId;
        detail.updatedBy = userId;
        detail.price = (0, number_util_1.parseNumericValue)(productInfo.price, {
            defaultValue: 0,
            allowNegative: false,
            fieldName: 'price',
            logger: this.logger,
        });
        detail.quantity = (0, number_util_1.parseNumericValue)(productInfo.quantity, {
            defaultValue: 0,
            allowNegative: false,
            fieldName: 'quantity',
            logger: this.logger,
        });
        detail.totalPrice = detail.price * detail.quantity;
        return detail;
    }
    async processPaymentTransactions(createOrderBookDto, wallet, orderBook, calculationResult, userId) {
        await this.validateWalletBalance(createOrderBookDto, wallet, calculationResult, userId);
        if (createOrderBookDto.orderType === order_type_enum_1.OrderType.BUY) {
            await this.processBuyOrderPayment(createOrderBookDto, wallet, orderBook, calculationResult, userId);
        }
        else if (createOrderBookDto.orderType === order_type_enum_1.OrderType.SELL) {
            await this.processSellOrderPayment(wallet, orderBook, calculationResult, userId);
        }
        else {
            throw new common_1.BadRequestException(`Loại lệnh không hợp lệ: ${createOrderBookDto.orderType}`);
        }
    }
    async processBuyOrderPayment(createOrderBookDto, wallet, orderBook, calculationResult, userId) {
        if (calculationResult.businessType === business_type_enum_1.BusinessType.IMMEDIATE_DELIVERY) {
            await this.processImmediateDeliveryPayment(createOrderBookDto, wallet, orderBook, calculationResult.totalPrice, userId);
        }
        else if (calculationResult.depositPrice > 0) {
            await this.processDepositPayment(wallet, orderBook, calculationResult.depositPrice, `Đặt cọc cho lệnh mua #${orderBook.id}`, userId);
            this.logger.log(`Đã xử lý tiền cọc cho lệnh MUA ký quỹ: ${calculationResult.depositPrice}`);
        }
    }
    async processSellOrderPayment(wallet, orderBook, calculationResult, userId) {
        if (calculationResult.depositPrice > 0) {
            await this.processDepositPayment(wallet, orderBook, calculationResult.depositPrice, `Đặt cọc cho lệnh bán khống #${orderBook.id}`, userId);
            this.logger.log(`Đã xử lý tiền cọc cho lệnh BÁN khống: ${calculationResult.depositPrice}`);
        }
    }
    async processImmediateDeliveryPayment(createOrderBookDto, wallet, orderBook, totalAmount, userId) {
        await this.updateWalletService.updateBalance(wallet.id, transaction_type_enum_1.TransactionType.DEBIT, totalAmount, `ORD-${orderBook.businessCode || orderBook.id}`, `Thanh toán 100% giá trị lệnh mua bạc giao ngay #${orderBook.id}`, userId);
        this.logger.log(`Đã trừ ${totalAmount} (100% giá trị) từ ví người dùng ${userId} cho lệnh MUA BẠC GIAO NGAY`);
        await this.processPhysicalBuyOrderEcomProductAssets(userId, createOrderBookDto.products, userId);
        orderBook.approveStatus = approve_status_enum_1.ApproveStatus.PENDING;
        await this.orderBookRepository.save(orderBook);
        this.logger.log(`Đã cập nhật trạng thái phê duyệt PENDING cho lệnh giao ngay #${orderBook.id}`);
    }
    async processDepositPayment(wallet, orderBook, depositAmount, notes, userId) {
        await this.updateWalletService.updateBalance(wallet.id, transaction_type_enum_1.TransactionType.DEBIT, depositAmount, `ORD-${orderBook.businessCode || orderBook.id}`, notes, userId);
        this.logger.log(`Đã trừ ${depositAmount} tiền đặt cọc từ ví người dùng ${userId}`);
    }
    async validateWalletBalance(createOrderBookDto, wallet, calculationResult, userId) {
        if (calculationResult.businessType === business_type_enum_1.BusinessType.IMMEDIATE_DELIVERY) {
            if (wallet.balance < calculationResult.totalPrice) {
                throw new localized_http_exception_1.InsufficientBalanceException(wallet.balance, calculationResult.totalPrice, {
                    userId: userId,
                    orderType: createOrderBookDto.orderType,
                    walletId: wallet.id,
                });
            }
        }
        else if (calculationResult.depositPrice > 0) {
            if (wallet.balance < calculationResult.depositPrice) {
                throw new localized_http_exception_1.InsufficientBalanceException(wallet.balance, calculationResult.depositPrice, {
                    userId: userId,
                    orderType: createOrderBookDto.orderType,
                    walletId: wallet.id,
                });
            }
        }
    }
    async processCommissions(createOrderBookDto, calculationResult, userId) {
        try {
            if (createOrderBookDto.orderType === order_type_enum_1.OrderType.BUY) {
                await this.processBuyOrderCommissions(calculationResult, userId);
            }
            else if (createOrderBookDto.orderType === order_type_enum_1.OrderType.SELL) {
                await this.processSellOrderCommissions(calculationResult, userId);
            }
            else {
                this.logger.log(`Loại lệnh ${createOrderBookDto.orderType} không hỗ trợ xử lý hoa hồng`);
            }
        }
        catch (commissionError) {
            this.logger.error(`Lỗi khi xử lý hoa hồng: ${commissionError.message}`, commissionError.stack);
        }
    }
    async processBuyOrderCommissions(calculationResult, userId) {
        if (calculationResult.businessType === business_type_enum_1.BusinessType.IMMEDIATE_DELIVERY) {
            await this.processSettlementCommission(calculationResult.totalPrice, userId);
        }
        else if (calculationResult.depositPrice > 0) {
            await this.processDepositCommission(calculationResult.depositPrice, userId);
        }
        else {
            this.logger.log(`Lệnh MUA không có phí để xử lý hoa hồng`);
        }
    }
    async processSellOrderCommissions(calculationResult, userId) {
        if (calculationResult.depositPrice > 0) {
            await this.processDepositCommission(calculationResult.depositPrice, userId);
        }
        else {
            this.logger.log(`Lệnh BÁN không có phí đặt cọc để xử lý hoa hồng`);
        }
    }
    async processSettlementCommission(totalAmount, userId) {
        this.logger.log(`Xử lý hoa hồng phí thanh toán cho lệnh giao ngay: ${totalAmount} (tỷ lệ 40%)`);
        await this.commissionProcessorService.processSettlementFeeCommission(userId, totalAmount, userId);
        this.logger.log(`Đã xử lý thành công hoa hồng phí thanh toán: ${totalAmount}`);
    }
    async processDepositCommission(depositAmount, userId) {
        this.logger.log(`Xử lý hoa hồng phí đặt cọc: ${depositAmount} (tỷ lệ 40%)`);
        await this.commissionProcessorService.processDepositFeeCommission(userId, depositAmount, userId);
        this.logger.log(`Đã xử lý thành công hoa hồng phí đặt cọc: ${depositAmount}`);
    }
    async finalizeOrderCreation(orderBookId, userId) {
        const orderWithDetails = await this.orderBookRepository.findOne({
            where: { id: orderBookId },
            relations: ['details', 'details.product', 'user'],
        });
        if (!orderWithDetails) {
            throw new common_1.NotFoundException(`Không tìm thấy lệnh vừa tạo với ID: ${orderBookId}`);
        }
        const dto = this.toDto(orderWithDetails);
        this.eventEmitter.emit(this.EVENT_ORDER_CREATED, {
            orderId: orderBookId,
            userId: userId,
            timestamp: new Date(),
            data: dto,
        });
        this.logger.log(`Đã tạo thành công lệnh ${orderWithDetails.orderType} với ID: ${orderBookId}`);
        return dto;
    }
    async bulkCreate(createOrderBookDtos, userId) {
        try {
            this.logger.log(`Đang tạo hàng loạt ${createOrderBookDtos.length} lệnh`);
            if (createOrderBookDtos.length === 0)
                return [];
            const userIds = [userId];
            const allEcomProductIds = new Set();
            createOrderBookDtos.forEach((dto) => {
                if (dto.products && dto.products.length > 0) {
                    dto.products.forEach((ecomProduct) => {
                        if (ecomProduct.productId) {
                            allEcomProductIds.add(ecomProduct.productId);
                        }
                    });
                }
            });
            const ecomProductIds = [...allEcomProductIds];
            const users = await this.userRepository.findBy({ id: (0, typeorm_2.In)(userIds) });
            const ecomProducts = await this.ecomProductRepository.findBy({
                id: (0, typeorm_2.In)(ecomProductIds),
            });
            if (users.length !== userIds.length) {
                const foundUserIds = users.map((u) => u.id);
                const notFoundUserIds = userIds.filter((id) => !foundUserIds.includes(id));
                throw new common_1.NotFoundException(`Không tìm thấy người dùng: ${notFoundUserIds.join(', ')}`);
            }
            if (ecomProducts.length !== ecomProductIds.length) {
                const foundEcomProductIds = ecomProducts.map((t) => t.id);
                const notFoundEcomProductIds = ecomProductIds.filter((id) => !foundEcomProductIds.includes(id));
                throw new common_1.NotFoundException(`Không tìm thấy sản phẩm: ${notFoundEcomProductIds.join(', ')}`);
            }
            const orders = [];
            for (const dto of createOrderBookDtos) {
                const order = await this.create(dto, userId);
                orders.push(order);
            }
            this.logger.log(`Đã tạo thành công ${orders.length} lệnh`);
            return orders;
        }
        catch (error) {
            this.logger.error(`Lỗi khi tạo hàng loạt lệnh: ${error.message}`, error.stack);
            if (error instanceof common_1.BadRequestException ||
                error instanceof common_1.NotFoundException ||
                error instanceof localized_http_exception_1.InsufficientBalanceException ||
                error instanceof localized_http_exception_1.InsufficientTokenException) {
                throw error;
            }
            throw new common_1.InternalServerErrorException(`Không thể tạo hàng loạt lệnh: ${error.message}`);
        }
    }
    async duplicate(id, userId) {
        try {
            this.logger.log(`Đang nhân bản lệnh với ID: ${id}`);
            const originalOrder = await this.orderBookRepository.findOne({
                where: { id, isDeleted: false },
                relations: ['details', 'details.token'],
            });
            if (!originalOrder) {
                throw new common_1.NotFoundException(`Không tìm thấy lệnh với ID ${id}`);
            }
            const newOrderBook = new order_book_entity_1.OrderBook();
            newOrderBook.userId = originalOrder.userId;
            newOrderBook.orderType = originalOrder.orderType;
            newOrderBook.totalPrice = originalOrder.totalPrice;
            if (originalOrder.businessType) {
                newOrderBook.businessType = originalOrder.businessType;
            }
            if (newOrderBook.orderType === order_type_enum_1.OrderType.BUY &&
                newOrderBook.businessType === business_type_enum_1.BusinessType.IMMEDIATE_DELIVERY) {
                newOrderBook.status = order_status_enum_1.OrderStatus.COMPLETED;
                newOrderBook.approveStatus = approve_status_enum_1.ApproveStatus.PENDING;
                newOrderBook.settlementAt = new Date();
            }
            else if (newOrderBook.orderType === order_type_enum_1.OrderType.SELL) {
                newOrderBook.status = order_status_enum_1.OrderStatus.DEPOSITED;
                newOrderBook.businessType = business_type_enum_1.BusinessType.NORMAL;
                this.logger.log(`Đặt businessType = SHORT_INVESTMENT và trạng thái WAIT_PAYMENT cho lệnh bán khống`);
            }
            else {
                newOrderBook.status = order_status_enum_1.OrderStatus.DEPOSITED;
            }
            newOrderBook.createdBy = userId;
            newOrderBook.updatedBy = userId;
            if (originalOrder.contractNumber) {
                newOrderBook.contractNumber = `COPY-${originalOrder.contractNumber}`;
            }
            else {
                newOrderBook.contractNumber = `HD-COPY-${Date.now()}`;
            }
            if (newOrderBook.orderType === order_type_enum_1.OrderType.BUY &&
                newOrderBook.businessType === business_type_enum_1.BusinessType.IMMEDIATE_DELIVERY) {
                newOrderBook.depositPrice = 0;
                newOrderBook.settlementPrice = Number(originalOrder.totalPrice);
            }
            else {
                if (originalOrder.depositPrice) {
                    newOrderBook.depositPrice = Number(originalOrder.depositPrice);
                }
                if (originalOrder.settlementPrice) {
                    newOrderBook.settlementPrice = Number(originalOrder.settlementPrice);
                }
            }
            if (originalOrder.storageFee) {
                newOrderBook.storageFee = Number(originalOrder.storageFee);
            }
            if (originalOrder.settlementDeadline) {
                newOrderBook.settlementDeadline = originalOrder.settlementDeadline;
            }
            const savedOrder = await this.orderBookRepository.save(newOrderBook);
            if (originalOrder.details && originalOrder.details.length > 0) {
                for (const detail of originalOrder.details) {
                    const newDetail = new order_book_detail_entity_1.OrderBookDetail();
                    newDetail.orderBookId = savedOrder.id;
                    newDetail.productId = detail.productId;
                    newDetail.price = detail.price || 0;
                    newDetail.quantity = detail.quantity || 0;
                    newDetail.totalPrice = (detail.price || 0) * (detail.quantity || 0);
                    newDetail.createdBy = userId;
                    newDetail.updatedBy = userId;
                    await this.orderBookDetailRepository.save(newDetail);
                }
            }
            const orderWithDetails = await this.orderBookRepository.findOne({
                where: { id: savedOrder.id },
                relations: ['details', 'details.token', 'user'],
            });
            if (!orderWithDetails) {
                throw new common_1.NotFoundException(`Không tìm thấy lệnh vừa tạo với ID: ${savedOrder.id}`);
            }
            const dto = this.toDto(orderWithDetails);
            this.eventEmitter.emit(this.EVENT_ORDER_DUPLICATED, {
                originalOrderId: id,
                newOrderId: savedOrder.id,
                userId: userId,
                timestamp: new Date(),
                data: dto,
            });
            this.logger.log(`Đã nhân bản thành công lệnh ${id} thành lệnh mới ${savedOrder.id}`);
            return dto;
        }
        catch (error) {
            this.logger.error(`Lỗi khi nhân bản lệnh: ${error.message}`, error.stack);
            if (error instanceof common_1.NotFoundException ||
                error instanceof common_1.BadRequestException ||
                error instanceof localized_http_exception_1.InsufficientBalanceException ||
                error instanceof localized_http_exception_1.InsufficientTokenException) {
                throw error;
            }
            throw new common_1.InternalServerErrorException(`Không thể nhân bản lệnh: ${error.message}`);
        }
    }
    async processPhysicalBuyOrderEcomProductAssets(userId, ecomProducts, operatorId) {
        try {
            this.logger.log(`Xử lý token assets cho lệnh mua bạc vật chất của người dùng ${userId}`);
            for (const ecomProductDetail of ecomProducts) {
                const ecomProductId = ecomProductDetail.productId;
                const volume = (0, number_util_1.parseNumericValue)(ecomProductDetail.quantity, {
                    defaultValue: 0,
                    allowNegative: false,
                    fieldName: 'quantity',
                    logger: this.logger,
                });
                this.logger.log(`🔧 [TOKEN_ASSET] Xử lý token asset cho ecomProduct ${ecomProductId}, số lượng: ${volume}`);
                this.logger.log(`🔍 [TOKEN_ASSET] Tìm kiếm token asset hiện có cho userId=${userId}, ecomProductId=${ecomProductId}`);
                const existingTokenAsset = await this.tokenAssetService.findByUserAndEcomProduct(userId, ecomProductId);
                this.logger.log(`📊 [TOKEN_ASSET] Kết quả tìm kiếm: ${existingTokenAsset ? `Found ID=${existingTokenAsset.id}` : 'Not found'}`);
                if (existingTokenAsset) {
                    const currentAmount = (0, number_util_1.parseNumericValue)(existingTokenAsset.amount, {
                        defaultValue: 0,
                        allowNegative: false,
                        fieldName: 'currentAmount',
                        logger: this.logger,
                    });
                    const newAmount = currentAmount + volume;
                    await this.tokenAssetService.update(existingTokenAsset.id, {
                        id: existingTokenAsset.id,
                        amount: newAmount,
                        updatedBy: operatorId,
                    });
                    this.logger.log(`Đã cập nhật token asset ${existingTokenAsset.id} cho token ${ecomProductId}, số lượng mới: ${newAmount}`);
                }
                else {
                    this.logger.log(`🚀 [TOKEN_ASSET] Tạo mới token asset cho ecomProduct ${ecomProductId}`);
                    try {
                        const createTokenAssetDto = {
                            userId: userId,
                            productId: ecomProductId,
                            amount: volume,
                        };
                        this.logger.log(`📝 [TOKEN_ASSET] CreateTokenAssetDto:`, JSON.stringify(createTokenAssetDto, null, 2));
                        const newTokenAsset = await this.tokenAssetService.create(createTokenAssetDto, operatorId);
                        this.logger.log(`✅ [TOKEN_ASSET] Đã tạo mới token asset ${newTokenAsset.id} cho ecomProduct ${ecomProductId}, số lượng: ${volume}`);
                    }
                    catch (createError) {
                        this.logger.error(`❌ [TOKEN_ASSET] Lỗi tạo token asset cho ecomProduct ${ecomProductId}:`);
                        this.logger.error(`   Error message: ${createError.message}`);
                        this.logger.error(`   Error stack: ${createError.stack}`);
                        this.logger.error(`   CreateTokenAssetDto:`, {
                            userId,
                            ecomProductId,
                            amount: volume,
                            operatorId,
                        });
                        continue;
                    }
                }
            }
        }
        catch (error) {
            this.logger.error(`Lỗi khi xử lý token assets cho lệnh mua bạc vật chất: ${error.message}`, error.stack);
            this.logger.error(`Chi tiết lỗi token asset processing:`, {
                userId,
                ecomProducts,
                operatorId,
                errorMessage: error.message,
                errorStack: error.stack,
            });
            this.logger.warn(`Bỏ qua lỗi token asset processing cho IMMEDIATE_DELIVERY order. Order vẫn được tạo thành công.`);
        }
    }
};
exports.CreateOrderBookService = CreateOrderBookService;
__decorate([
    (0, typeorm_transactional_1.Transactional)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [create_order_book_dto_1.CreateOrderBookDto, String]),
    __metadata("design:returntype", Promise)
], CreateOrderBookService.prototype, "create", null);
__decorate([
    (0, typeorm_transactional_1.Transactional)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Array, String]),
    __metadata("design:returntype", Promise)
], CreateOrderBookService.prototype, "bulkCreate", null);
__decorate([
    (0, typeorm_transactional_1.Transactional)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String]),
    __metadata("design:returntype", Promise)
], CreateOrderBookService.prototype, "duplicate", null);
exports.CreateOrderBookService = CreateOrderBookService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(order_book_entity_1.OrderBook)),
    __param(1, (0, typeorm_1.InjectRepository)(order_book_detail_entity_1.OrderBookDetail)),
    __param(2, (0, typeorm_1.InjectRepository)(user_entity_1.User)),
    __param(3, (0, typeorm_1.InjectRepository)(ecom_products_entity_1.EcomProduct)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository,
        event_emitter_1.EventEmitter2,
        read_wallet_service_1.ReadWalletService,
        update_wallet_service_1.UpdateWalletService,
        asset_service_1.AssetService,
        commission_processor_service_1.CommissionProcessorService])
], CreateOrderBookService);
//# sourceMappingURL=create.order-book.service.js.map
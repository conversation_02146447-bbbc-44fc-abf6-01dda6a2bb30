"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.VnpayUsageExample = void 0;
const common_1 = require("@nestjs/common");
const vnpay_service_1 = require("../services/vnpay.service");
const payment_gateway_type_enum_1 = require("../enums/payment-gateway-type.enum");
let VnpayUsageExample = class VnpayUsageExample {
    vnpayService;
    constructor(vnpayService) {
        this.vnpayService = vnpayService;
    }
    async createBasicPayment() {
        const paymentData = {
            userId: '550e8400-e29b-41d4-a716-************',
            walletId: '550e8400-e29b-41d4-a716-************',
            amount: 100000,
            gatewayType: payment_gateway_type_enum_1.PaymentGatewayType.VNPAY,
            description: 'Nap tien vao vi',
            ipAddress: '127.0.0.1',
        };
        try {
            const result = await this.vnpayService.createPaymentUrl(paymentData);
            console.log('Payment URL:', result.paymentUrl);
            console.log('Transaction ID:', result.transactionId);
            return result;
        }
        catch (error) {
            console.error('Error creating payment:', error);
            throw error;
        }
    }
    async createPaymentWithBankCode() {
        const paymentData = {
            userId: '550e8400-e29b-41d4-a716-************',
            walletId: '550e8400-e29b-41d4-a716-************',
            amount: 500000,
            gatewayType: payment_gateway_type_enum_1.PaymentGatewayType.VNPAY,
            description: 'Thanh toan don hang ABC123',
            ipAddress: '*************',
            bankCode: 'VNPAYQR',
            locale: 'vn',
            orderType: 'billpayment',
        };
        try {
            const result = await this.vnpayService.createPaymentUrl(paymentData);
            return result;
        }
        catch (error) {
            console.error('Error creating payment with bank code:', error);
            throw error;
        }
    }
    async handleReturnUrl(queryParams) {
        try {
            const result = await this.vnpayService.verifyReturnUrl(queryParams);
            if (result.isValid) {
                if (result.responseCode === '00' && result.transactionStatus === '00') {
                    console.log('✅ Thanh toán thành công!');
                    console.log('Transaction ID:', result.transactionId);
                    console.log('Amount:', result.amount);
                    console.log('VNPAY Transaction No:', result.vnpayTransactionNo);
                    console.log('Bank Code:', result.bankCode);
                    console.log('Pay Date:', result.payDate);
                    return {
                        success: true,
                        message: 'Thanh toán thành công',
                        data: result
                    };
                }
                else {
                    console.log('❌ Thanh toán thất bại!');
                    console.log('Response Code:', result.responseCode);
                    console.log('Message:', result.message);
                    return {
                        success: false,
                        message: result.message,
                        data: result
                    };
                }
            }
            else {
                console.log('🚫 Chữ ký không hợp lệ!');
                return {
                    success: false,
                    message: 'Chữ ký không hợp lệ',
                    data: null
                };
            }
        }
        catch (error) {
            console.error('Error handling return URL:', error);
            throw error;
        }
    }
    async handleIpnCallback(queryParams) {
        try {
            const result = await this.vnpayService.handleIpnCallback(queryParams);
            if (result.isValid) {
                console.log('📨 IPN callback hợp lệ');
                const ipnResponse = await this.vnpayService.createIpnResponse(queryParams);
                return ipnResponse;
            }
            else {
                console.log('🚫 IPN callback không hợp lệ');
                return {
                    RspCode: '97',
                    Message: 'Invalid signature'
                };
            }
        }
        catch (error) {
            console.error('Error handling IPN:', error);
            return {
                RspCode: '99',
                Message: 'Unknown error'
            };
        }
    }
    async queryTransactionStatus(txnRef, transactionDate) {
        try {
            const result = await this.vnpayService.queryTransaction({
                txnRef: txnRef,
                transactionDate: transactionDate,
                orderInfo: `Truy van giao dich ${txnRef}`,
                ipAddr: '127.0.0.1'
            });
            console.log('Query result:', result);
            if (result.vnp_ResponseCode === '00') {
                console.log('✅ Truy vấn thành công');
                console.log('Transaction Status:', result.vnp_TransactionStatus);
                console.log('Amount:', result.vnp_Amount);
                console.log('Bank Code:', result.vnp_BankCode);
                console.log('Pay Date:', result.vnp_PayDate);
            }
            else {
                console.log('❌ Truy vấn thất bại:', result.vnp_Message);
            }
            return result;
        }
        catch (error) {
            console.error('Error querying transaction:', error);
            throw error;
        }
    }
    async refundFullTransaction(txnRef, originalAmount, transactionDate, vnpayTransactionNo) {
        try {
            const result = await this.vnpayService.refundTransaction({
                txnRef: txnRef,
                amount: originalAmount,
                orderInfo: `Hoan tien toan phan don hang ${txnRef}`,
                transactionDate: transactionDate,
                transactionType: '02',
                createBy: 'admin_system',
                ipAddr: '127.0.0.1',
                transactionNo: vnpayTransactionNo
            });
            console.log('Refund result:', result);
            if (result.vnp_ResponseCode === '00') {
                console.log('✅ Hoàn tiền thành công');
                console.log('Refund Transaction No:', result.vnp_TransactionNo);
            }
            else {
                console.log('❌ Hoàn tiền thất bại:', result.vnp_Message);
            }
            return result;
        }
        catch (error) {
            console.error('Error refunding transaction:', error);
            throw error;
        }
    }
    async refundPartialTransaction(txnRef, refundAmount, transactionDate, vnpayTransactionNo) {
        try {
            const result = await this.vnpayService.refundTransaction({
                txnRef: txnRef,
                amount: refundAmount,
                orderInfo: `Hoan tien mot phan don hang ${txnRef}`,
                transactionDate: transactionDate,
                transactionType: '03',
                createBy: 'admin_system',
                ipAddr: '127.0.0.1',
                transactionNo: vnpayTransactionNo
            });
            console.log('Partial refund result:', result);
            return result;
        }
        catch (error) {
            console.error('Error partial refunding transaction:', error);
            throw error;
        }
    }
    async completePaymentWorkflow(userId, walletId, amount) {
        try {
            console.log('🚀 Bước 1: Tạo URL thanh toán');
            const paymentData = {
                userId,
                walletId,
                amount,
                gatewayType: payment_gateway_type_enum_1.PaymentGatewayType.VNPAY,
                description: `Nap tien ${amount} VND vao vi`,
                ipAddress: '127.0.0.1',
            };
            const paymentResult = await this.vnpayService.createPaymentUrl(paymentData);
            console.log('Payment URL created:', paymentResult.paymentUrl);
            return paymentResult;
        }
        catch (error) {
            console.error('Error in complete workflow:', error);
            throw error;
        }
    }
};
exports.VnpayUsageExample = VnpayUsageExample;
exports.VnpayUsageExample = VnpayUsageExample = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [vnpay_service_1.VnpayService])
], VnpayUsageExample);
function formatDate(date) {
    const vietnamTime = new Date(date.getTime() + (7 * 60 * 60 * 1000));
    const year = vietnamTime.getUTCFullYear();
    const month = String(vietnamTime.getUTCMonth() + 1).padStart(2, '0');
    const day = String(vietnamTime.getUTCDate()).padStart(2, '0');
    const hours = String(vietnamTime.getUTCHours()).padStart(2, '0');
    const minutes = String(vietnamTime.getUTCMinutes()).padStart(2, '0');
    const seconds = String(vietnamTime.getUTCSeconds()).padStart(2, '0');
    return `${year}${month}${day}${hours}${minutes}${seconds}`;
}
//# sourceMappingURL=vnpay-usage.example.js.map
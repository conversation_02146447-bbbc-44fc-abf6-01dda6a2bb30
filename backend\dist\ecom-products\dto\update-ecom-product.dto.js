"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UpdateEcomProductDto = void 0;
const openapi = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
const swagger_1 = require("@nestjs/swagger");
class UpdateEcomProductDto {
    id;
    productCode;
    productName;
    categoryId;
    description;
    weight;
    imageUrl;
    regularPrice;
    salePrice;
    stockQuantity;
    isActive;
    slug;
    static _OPENAPI_METADATA_FACTORY() {
        return { id: { required: true, type: () => String, format: "uuid" }, productCode: { required: false, type: () => String, maxLength: 50 }, productName: { required: false, type: () => String, maxLength: 100 }, categoryId: { required: false, type: () => String, format: "uuid" }, description: { required: false, type: () => String }, weight: { required: false, type: () => Number }, imageUrl: { required: false, type: () => String }, regularPrice: { required: false, type: () => Number }, salePrice: { required: false, type: () => Number }, stockQuantity: { required: false, type: () => Number }, isActive: { required: false, type: () => Boolean }, slug: { required: false, type: () => String } };
    }
}
exports.UpdateEcomProductDto = UpdateEcomProductDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'ID của sản phẩm',
        example: '550e8400-e29b-41d4-a716-************',
    }),
    (0, class_validator_1.IsUUID)(),
    __metadata("design:type", String)
], UpdateEcomProductDto.prototype, "id", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Mã sản phẩm',
        example: 'SP001',
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MaxLength)(50),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], UpdateEcomProductDto.prototype, "productCode", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Tên sản phẩm',
        example: 'Nhẫn bạc 925',
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MaxLength)(100),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], UpdateEcomProductDto.prototype, "productName", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'ID danh mục sản phẩm',
        example: '550e8400-e29b-41d4-a716-************',
    }),
    (0, class_validator_1.IsUUID)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], UpdateEcomProductDto.prototype, "categoryId", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Mô tả sản phẩm',
        example: 'Nhẫn bạc cao cấp với độ tinh khiết 925',
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], UpdateEcomProductDto.prototype, "description", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Trọng lượng (oz)',
        example: 5.75,
    }),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Number)
], UpdateEcomProductDto.prototype, "weight", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'URL hình ảnh',
        example: 'https://example.com/images/silver-ring.jpg',
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], UpdateEcomProductDto.prototype, "imageUrl", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Giá bán thông thường',
        example: 1500000,
    }),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Number)
], UpdateEcomProductDto.prototype, "regularPrice", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Giá khuyến mãi',
        example: 1350000,
    }),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Number)
], UpdateEcomProductDto.prototype, "salePrice", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Số lượng tồn kho',
        example: 100,
    }),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Number)
], UpdateEcomProductDto.prototype, "stockQuantity", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Trạng thái hoạt động',
        example: true,
    }),
    (0, class_validator_1.IsBoolean)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Boolean)
], UpdateEcomProductDto.prototype, "isActive", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Slug SEO-friendly (tự động tạo từ tên sản phẩm nếu không cung cấp)',
        example: 'nhan-bac-925-cao-cap',
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], UpdateEcomProductDto.prototype, "slug", void 0);
//# sourceMappingURL=update-ecom-product.dto.js.map
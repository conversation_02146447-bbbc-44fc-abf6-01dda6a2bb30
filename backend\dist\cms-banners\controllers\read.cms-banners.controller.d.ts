import { ReadCmsBannersService } from '../services/read.cms-banners.service';
import { CmsBannerDto } from '../dto/cms-banner.dto';
import { CmsBannerStatus, CmsBannerLocation } from '../entity/cms-banners.entity';
import { CustomPaginationQueryDto } from '../../common/dto/custom-pagination-query.dto';
import { PaginationResponseDto } from '../../common/dto/pagination-response.dto';
export declare class ReadCmsBannersController {
    private readonly cmsBannersService;
    constructor(cmsBannersService: ReadCmsBannersService);
    findAll(paginationQuery: CustomPaginationQueryDto): Promise<PaginationResponseDto<CmsBannerDto>>;
    findByStatus(status: CmsBannerStatus, paginationQuery: CustomPaginationQueryDto): Promise<PaginationResponseDto<CmsBannerDto>>;
    findByLocation(location: CmsBannerLocation, paginationQuery: CustomPaginationQueryDto): Promise<PaginationResponseDto<CmsBannerDto>>;
    getActiveBanners(location?: CmsBannerLocation, limit?: number): Promise<CmsBannerDto[]>;
    getStatistics(): Promise<{
        total: number;
        byStatus: Record<string, number>;
        byLocation: Record<string, number>;
        active: number;
        expired: number;
    }>;
    count(filter?: string): Promise<number>;
    search(keyword: string, paginationQuery: CustomPaginationQueryDto): Promise<PaginationResponseDto<CmsBannerDto>>;
    findDeleted(paginationQuery: CustomPaginationQueryDto): Promise<PaginationResponseDto<CmsBannerDto>>;
    findOne(id: string, relations?: string): Promise<CmsBannerDto | null>;
    debugDatabase(): Promise<any>;
    findByBusinessCode(businessCode: string): Promise<CmsBannerDto | null>;
}

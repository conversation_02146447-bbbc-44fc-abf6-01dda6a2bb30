"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreateActivityLogService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_transactional_1 = require("typeorm-transactional");
const typeorm_1 = require("typeorm");
const base_activity_log_service_1 = require("./base.activity-log.service");
const create_activity_log_dto_1 = require("../dto/create-activity-log.dto");
const user_entity_1 = require("../../users/entities/user.entity");
let CreateActivityLogService = class CreateActivityLogService extends base_activity_log_service_1.BaseActivityLogService {
    async create(createActivityLogDto, userId) {
        try {
            this.logger.debug(`Đang tạo lịch sử hoạt động mới: ${JSON.stringify(createActivityLogDto)}`);
            if (createActivityLogDto.userId) {
                const userRepository = this.dataSource.getRepository(user_entity_1.User);
                const user = await userRepository.findOne({
                    where: { id: createActivityLogDto.userId, isDeleted: false },
                });
                if (!user) {
                    throw new common_1.NotFoundException('User not found');
                }
            }
            if (!createActivityLogDto.module && createActivityLogDto.action) {
                createActivityLogDto.module = this.determineModule(createActivityLogDto.action);
                this.logger.debug(`Tự động xác định module: ${createActivityLogDto.module} cho action: ${createActivityLogDto.action}`);
            }
            const activityLog = this.activityLogRepository.create({
                ...createActivityLogDto,
                createdBy: userId || createActivityLogDto.createdBy,
            });
            const savedActivityLog = await this.activityLogRepository.save(activityLog);
            this.logger.debug(`Đã tạo lịch sử hoạt động với ID: ${savedActivityLog.id}`);
            this.eventEmitter.emit(this.EVENT_ACTIVITY_LOG_CREATED, {
                activityLogId: savedActivityLog.id,
                userId: savedActivityLog.userId,
                action: savedActivityLog.action,
                module: savedActivityLog.module,
            });
            return this.toDto(savedActivityLog);
        }
        catch (error) {
            this.logger.error(`Lỗi khi tạo lịch sử hoạt động: ${error.message}`, error.stack);
            if (error instanceof common_1.NotFoundException) {
                throw error;
            }
            if (error.code === '23505') {
                throw new common_1.BadRequestException('Lịch sử hoạt động đã tồn tại');
            }
            throw new common_1.InternalServerErrorException('Lỗi khi tạo lịch sử hoạt động');
        }
    }
    async bulkCreate(createActivityLogDtos, userId) {
        try {
            this.logger.debug(`Đang tạo hàng loạt ${createActivityLogDtos.length} lịch sử hoạt động`);
            const userIds = [
                ...new Set(createActivityLogDtos
                    .filter((dto) => dto.userId)
                    .map((dto) => dto.userId)),
            ];
            if (userIds.length > 0) {
                const userRepository = this.dataSource.getRepository(user_entity_1.User);
                const users = await userRepository.find({
                    where: { id: (0, typeorm_1.In)(userIds), isDeleted: false },
                });
                if (users.length !== userIds.length) {
                    throw new common_1.NotFoundException('One or more users not found');
                }
            }
            const processedDtos = createActivityLogDtos.map(dto => {
                if (!dto.module && dto.action) {
                    dto.module = this.determineModule(dto.action);
                    this.logger.debug(`Tự động xác định module: ${dto.module} cho action: ${dto.action}`);
                }
                return {
                    ...dto,
                    createdBy: userId || dto.createdBy,
                };
            });
            const activityLogs = processedDtos.map((dto) => this.activityLogRepository.create(dto));
            const savedActivityLogs = await this.activityLogRepository.save(activityLogs);
            this.logger.debug(`Đã tạo ${savedActivityLogs.length} lịch sử hoạt động`);
            savedActivityLogs.forEach(log => {
                this.eventEmitter.emit(this.EVENT_ACTIVITY_LOG_CREATED, {
                    activityLogId: log.id,
                    userId: log.userId,
                    action: log.action,
                    module: log.module,
                });
            });
            return savedActivityLogs.map(log => this.toDto(log));
        }
        catch (error) {
            this.logger.error(`Lỗi khi tạo hàng loạt lịch sử hoạt động: ${error.message}`, error.stack);
            if (error instanceof common_1.NotFoundException) {
                throw error;
            }
            throw new common_1.InternalServerErrorException('Lỗi khi tạo hàng loạt lịch sử hoạt động');
        }
    }
};
exports.CreateActivityLogService = CreateActivityLogService;
__decorate([
    (0, typeorm_transactional_1.Transactional)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [create_activity_log_dto_1.CreateActivityLogDto, String]),
    __metadata("design:returntype", Promise)
], CreateActivityLogService.prototype, "create", null);
__decorate([
    (0, typeorm_transactional_1.Transactional)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Array, String]),
    __metadata("design:returntype", Promise)
], CreateActivityLogService.prototype, "bulkCreate", null);
exports.CreateActivityLogService = CreateActivityLogService = __decorate([
    (0, common_1.Injectable)()
], CreateActivityLogService);
//# sourceMappingURL=create.activity-log.service.js.map
"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.EcomProductsMigrationController = void 0;
const openapi = require("@nestjs/swagger");
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const jwt_auth_guard_1 = require("../../common/guards/jwt-auth.guard");
const roles_decorator_1 = require("../../common/decorators/roles.decorator");
const permissions_decorator_1 = require("../../common/decorators/permissions.decorator");
const get_user_decorator_1 = require("../../common/decorators/get-user.decorator");
const migration_ecom_products_service_1 = require("../services/migration.ecom-products.service");
let EcomProductsMigrationController = class EcomProductsMigrationController {
    migrationService;
    constructor(migrationService) {
        this.migrationService = migrationService;
    }
    async generateSlugs(userId) {
        return this.migrationService.generateSlugsForExistingProducts(userId);
    }
};
exports.EcomProductsMigrationController = EcomProductsMigrationController;
__decorate([
    (0, common_1.Post)('generate-slugs'),
    (0, roles_decorator_1.Roles)('ADMIN'),
    (0, permissions_decorator_1.Permissions)('ecom-product:update'),
    (0, swagger_1.ApiOperation)({
        summary: 'Tạo slug cho tất cả sản phẩm chưa có slug',
        description: 'Endpoint này sẽ tạo slug cho tất cả sản phẩm hiện có trong database mà chưa có slug. Chỉ admin mới có thể thực hiện.'
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Đã tạo slug thành công cho các sản phẩm.',
        schema: {
            type: 'object',
            properties: {
                success: { type: 'boolean', example: true },
                message: { type: 'string', example: 'Đã tạo slug cho 25 sản phẩm' },
                data: {
                    type: 'object',
                    properties: {
                        totalProcessed: { type: 'number', example: 25 },
                        totalUpdated: { type: 'number', example: 25 },
                        totalSkipped: { type: 'number', example: 0 },
                        errors: { type: 'array', items: { type: 'string' } }
                    }
                }
            }
        }
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.FORBIDDEN,
        description: 'Không có quyền thực hiện thao tác này.',
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.INTERNAL_SERVER_ERROR,
        description: 'Lỗi server khi tạo slug.',
    }),
    openapi.ApiResponse({ status: 201, type: Object }),
    __param(0, (0, get_user_decorator_1.GetUser)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], EcomProductsMigrationController.prototype, "generateSlugs", null);
exports.EcomProductsMigrationController = EcomProductsMigrationController = __decorate([
    (0, swagger_1.ApiTags)('Ecom Products Migration'),
    (0, common_1.Controller)('ecom-products/migration'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, swagger_1.ApiBearerAuth)(),
    __metadata("design:paramtypes", [migration_ecom_products_service_1.EcomProductsMigrationService])
], EcomProductsMigrationController);
//# sourceMappingURL=ecom-products.migration.controller.js.map
"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.BaseActivityLogService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const event_emitter_1 = require("@nestjs/event-emitter");
const class_transformer_1 = require("class-transformer");
const activity_log_entity_1 = require("../entities/activity-log.entity");
const activity_log_dto_1 = require("../dto/activity-log.dto");
const activity_log_action_constants_1 = require("../constants/activity-log-action.constants");
let BaseActivityLogService = class BaseActivityLogService {
    activityLogRepository;
    dataSource;
    eventEmitter;
    logger = new common_1.Logger('ActivityLogService');
    validRelations = ['user', 'creator', 'updater', 'deleter'];
    EVENT_ACTIVITY_LOG_CREATED = 'activity-log.created';
    EVENT_ACTIVITY_LOG_UPDATED = 'activity-log.updated';
    EVENT_ACTIVITY_LOG_DELETED = 'activity-log.deleted';
    constructor(activityLogRepository, dataSource, eventEmitter) {
        this.activityLogRepository = activityLogRepository;
        this.dataSource = dataSource;
        this.eventEmitter = eventEmitter;
    }
    async getQueryRunner() {
        const queryRunner = this.dataSource.createQueryRunner();
        await queryRunner.connect();
        await queryRunner.startTransaction();
        return queryRunner;
    }
    validateRelations(relations) {
        return relations.filter(relation => this.validRelations.includes(relation));
    }
    async findByIdOrFail(id, relations = [], withDeleted = false) {
        this.logger.debug(`Tìm lịch sử hoạt động với ID: ${id}, withDeleted: ${withDeleted}, relations: ${JSON.stringify(relations)}`);
        const exists = await this.activityLogRepository.exists({
            where: { id },
            withDeleted: true,
        });
        this.logger.debug(`Lịch sử hoạt động với ID ${id} tồn tại: ${exists}`);
        const validatedRelations = this.validateRelations(relations);
        const activityLog = await this.activityLogRepository.findOne({
            where: { id },
            relations: validatedRelations,
            withDeleted: withDeleted,
        });
        if (!activityLog) {
            throw new common_1.NotFoundException(`Không tìm thấy lịch sử hoạt động với ID "${id}"${withDeleted ? '' : ' hoặc đã bị xóa'}`);
        }
        return activityLog;
    }
    determineModule(action) {
        const actionUpperCase = action.toUpperCase();
        const actionGroups = [
            { actions: ['LOGIN', 'LOGOUT', 'FAILED_LOGIN', 'PASSWORD_RESET', 'PASSWORD_CHANGE', 'ENABLE_2FA', 'DISABLE_2FA'], module: activity_log_action_constants_1.MODULES.AUTH },
            { actions: ['PROFILE_UPDATE', 'EMAIL_CHANGE', 'PHONE_CHANGE'], module: activity_log_action_constants_1.MODULES.PROFILE },
            { actions: ['CREATE_ORDER', 'UPDATE_ORDER', 'CANCEL_ORDER', 'COMPLETE_ORDER'], module: activity_log_action_constants_1.MODULES.ORDER },
            { actions: ['DEPOSIT', 'WITHDRAWAL', 'TRANSFER'], module: activity_log_action_constants_1.MODULES.WALLET },
            { actions: ['KYC_SUBMIT', 'KYC_APPROVED', 'KYC_REJECTED'], module: activity_log_action_constants_1.MODULES.KYC },
            { actions: ['USER_CREATE', 'USER_UPDATE', 'USER_DELETE', 'USER_RESTORE'], module: activity_log_action_constants_1.MODULES.ADMIN },
            { actions: ['SETTINGS_UPDATE', 'NOTIFICATION_UPDATE'], module: activity_log_action_constants_1.MODULES.SETTINGS },
        ];
        for (const group of actionGroups) {
            if (group.actions.includes(actionUpperCase)) {
                return group.module;
            }
        }
        return activity_log_action_constants_1.MODULES.OTHER;
    }
    toDto(activityLog) {
        return (0, class_transformer_1.plainToInstance)(activity_log_dto_1.ActivityLogDto, activityLog, {
            excludeExtraneousValues: true,
        });
    }
};
exports.BaseActivityLogService = BaseActivityLogService;
exports.BaseActivityLogService = BaseActivityLogService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(activity_log_entity_1.ActivityLog)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        typeorm_2.DataSource,
        event_emitter_1.EventEmitter2])
], BaseActivityLogService);
//# sourceMappingURL=base.activity-log.service.js.map
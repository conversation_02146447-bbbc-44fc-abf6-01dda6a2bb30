"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AddIsAgentField1746603947445 = void 0;
class AddIsAgentField1746603947445 {
    name = 'AddIsAgentField1746603947445';
    async up(queryRunner) {
        await queryRunner.query(`
            ALTER TABLE "users"
            ADD COLUMN "is_agent" boolean NOT NULL DEFAULT false
        `);
    }
    async down(queryRunner) {
        await queryRunner.query(`
            ALTER TABLE "users"
            DROP COLUMN "is_agent"
        `);
    }
}
exports.AddIsAgentField1746603947445 = AddIsAgentField1746603947445;
//# sourceMappingURL=1746603947445-addIsAgentField.js.map
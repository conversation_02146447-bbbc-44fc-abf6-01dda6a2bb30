import { Repository, DataSource } from 'typeorm';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { BaseCmsCategoriesService } from './base.cms-categories.service';
import { CmsCategories } from '../entity/cms-categories.entity';
import { CmsCategoryDto } from '../dto/cms-category.dto';
export declare class DeleteCmsCategoriesService extends BaseCmsCategoriesService {
    protected readonly categoryRepository: Repository<CmsCategories>;
    protected readonly dataSource: DataSource;
    protected readonly eventEmitter: EventEmitter2;
    constructor(categoryRepository: Repository<CmsCategories>, dataSource: DataSource, eventEmitter: EventEmitter2);
    softDelete(id: string, userId: string): Promise<CmsCategoryDto | null>;
    restore(id: string, userId: string): Promise<CmsCategoryDto | null>;
    remove(id: string): Promise<{
        affected: number | null;
    }>;
}

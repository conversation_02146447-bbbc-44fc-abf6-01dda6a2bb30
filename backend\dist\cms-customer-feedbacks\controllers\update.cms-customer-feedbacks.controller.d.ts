import { UpdateCmsCustomerFeedbacksService } from '../services/update.cms-customer-feedbacks.service';
import { CmsCustomerFeedbackDto } from '../dto/cms-customer-feedback.dto';
import { UpdateCmsCustomerFeedbackDto } from '../dto/update.cms-customer-feedback.dto';
import { CmsCustomerFeedbackStatus } from '../entity/cms-customer-feedbacks.entity';
export declare class UpdateCmsCustomerFeedbacksController {
    private readonly cmsCustomerFeedbacksService;
    constructor(cmsCustomerFeedbacksService: UpdateCmsCustomerFeedbacksService);
    update(id: string, updateCmsCustomerFeedbackDto: UpdateCmsCustomerFeedbackDto, userId: string): Promise<CmsCustomerFeedbackDto | null>;
    bulkUpdate(updates: Array<{
        id: string;
        data: UpdateCmsCustomerFeedbackDto;
    }>, userId: string): Promise<CmsCustomerFeedbackDto[]>;
    approve(id: string, userId: string): Promise<CmsCustomerFeedbackDto | null>;
    reject(id: string, userId: string): Promise<CmsCustomerFeedbackDto | null>;
    updateStatus(id: string, status: CmsCustomerFeedbackStatus, userId: string): Promise<CmsCustomerFeedbackDto | null>;
    bulkApprove(ids: string[], userId: string): Promise<CmsCustomerFeedbackDto[]>;
    bulkReject(ids: string[], userId: string): Promise<CmsCustomerFeedbackDto[]>;
}

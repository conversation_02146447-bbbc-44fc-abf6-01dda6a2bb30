"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreateSystemConfigDto = void 0;
const openapi = require("@nestjs/swagger");
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
const system_config_dto_1 = require("./system-config.dto");
class CreateSystemConfigDto {
    configKey;
    configValue;
    description;
    configGroup;
    sectionName;
    sectionDisplayName;
    sectionDescription;
    sectionOrder;
    displayOrder;
    groupDisplayName;
    groupDescription;
    groupIcon;
    groupOrder;
    isGroupConfig;
    configType;
    configOptions;
    createdBy;
    static _OPENAPI_METADATA_FACTORY() {
        return { configKey: { required: true, type: () => String }, configValue: { required: false, type: () => String }, description: { required: false, type: () => String }, configGroup: { required: false, type: () => String }, sectionName: { required: false, type: () => String }, sectionDisplayName: { required: false, type: () => String }, sectionDescription: { required: false, type: () => String }, sectionOrder: { required: false, type: () => Number }, displayOrder: { required: false, type: () => Number }, groupDisplayName: { required: false, type: () => String }, groupDescription: { required: false, type: () => String }, groupIcon: { required: false, type: () => String }, groupOrder: { required: false, type: () => Number }, isGroupConfig: { required: false, type: () => Boolean }, configType: { required: false, enum: require("./system-config.dto").ConfigType }, configOptions: { required: false, type: () => String }, createdBy: { required: false, type: () => String } };
    }
}
exports.CreateSystemConfigDto = CreateSystemConfigDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Khóa cấu hình' }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateSystemConfigDto.prototype, "configKey", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Giá trị cấu hình', required: false }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], CreateSystemConfigDto.prototype, "configValue", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Mô tả cấu hình', required: false }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], CreateSystemConfigDto.prototype, "description", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Nhóm cấu hình',
        required: false,
        example: 'general',
        default: 'general'
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], CreateSystemConfigDto.prototype, "configGroup", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Tên section của cấu hình',
        required: false,
        example: 'header'
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], CreateSystemConfigDto.prototype, "sectionName", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Tên hiển thị của section',
        required: false,
        example: 'Cấu hình Header'
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], CreateSystemConfigDto.prototype, "sectionDisplayName", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Mô tả của section',
        required: false,
        example: 'Cấu hình phần header của website'
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], CreateSystemConfigDto.prototype, "sectionDescription", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Thứ tự hiển thị của section',
        required: false,
        example: 1
    }),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Number)
], CreateSystemConfigDto.prototype, "sectionOrder", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Thứ tự hiển thị của trường cấu hình trong section',
        required: false,
        example: 1
    }),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Number)
], CreateSystemConfigDto.prototype, "displayOrder", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Tên hiển thị của nhóm cấu hình',
        required: false,
        example: 'Cấu hình chung'
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], CreateSystemConfigDto.prototype, "groupDisplayName", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Mô tả của nhóm cấu hình',
        required: false,
        example: 'Cấu hình chung của hệ thống'
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], CreateSystemConfigDto.prototype, "groupDescription", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Icon của nhóm cấu hình',
        required: false,
        example: 'settings'
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], CreateSystemConfigDto.prototype, "groupIcon", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Thứ tự hiển thị của nhóm cấu hình',
        required: false,
        example: 1
    }),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Number)
], CreateSystemConfigDto.prototype, "groupOrder", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Cờ đánh dấu đây là cấu hình nhóm',
        required: false,
        example: false
    }),
    (0, class_validator_1.IsBoolean)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Boolean)
], CreateSystemConfigDto.prototype, "isGroupConfig", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Loại cấu hình',
        required: false,
        enum: system_config_dto_1.ConfigType,
        default: system_config_dto_1.ConfigType.TEXT
    }),
    (0, class_validator_1.IsEnum)(system_config_dto_1.ConfigType),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], CreateSystemConfigDto.prototype, "configType", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Tùy chọn cho loại select',
        required: false,
        example: '["option1", "option2"]'
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], CreateSystemConfigDto.prototype, "configOptions", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'ID người tạo',
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], CreateSystemConfigDto.prototype, "createdBy", void 0);
//# sourceMappingURL=create-system-config.dto.js.map
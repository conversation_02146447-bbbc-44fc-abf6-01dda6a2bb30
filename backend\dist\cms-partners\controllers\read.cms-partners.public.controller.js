"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var ReadCmsPartnersPublicController_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.ReadCmsPartnersPublicController = void 0;
const openapi = require("@nestjs/swagger");
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const custom_pagination_meta_dto_1 = require("../../common/dto/custom-pagination-meta.dto");
const custom_pagination_query_dto_1 = require("../../common/dto/custom-pagination-query.dto");
const pagination_response_dto_1 = require("../../common/dto/pagination-response.dto");
const dto_1 = require("../dto");
const cms_partner_public_dto_1 = require("../dto/cms-partner.public.dto");
const read_cms_partners_service_1 = require("../services/read.cms-partners.service");
const class_transformer_1 = require("class-transformer");
let ReadCmsPartnersPublicController = ReadCmsPartnersPublicController_1 = class ReadCmsPartnersPublicController {
    readCmsPartnersService;
    logger = new common_1.Logger(ReadCmsPartnersPublicController_1.name);
    constructor(readCmsPartnersService) {
        this.readCmsPartnersService = readCmsPartnersService;
    }
    async getActivePartners(paginationQuery) {
        this.logger.log('Getting active CMS Partners for public display');
        const { data, total } = await this.readCmsPartnersService.getActivePartners(paginationQuery);
        const publicData = data.map(partner => (0, class_transformer_1.plainToInstance)(cms_partner_public_dto_1.CmsPartnerPublicDto, partner, {
            excludeExtraneousValues: true,
        }));
        const meta = new custom_pagination_meta_dto_1.CustomPageMetaDto({
            pageQueryDto: paginationQuery,
            itemCount: total,
        });
        return new pagination_response_dto_1.PaginationResponseDto(publicData, meta);
    }
    async getPartnersByType(type, paginationQuery) {
        this.logger.log(`Getting CMS Partners by type ${type} for public display`);
        const { data: allData } = await this.readCmsPartnersService.findByType(type, paginationQuery);
        const filteredData = allData.filter(partner => partner.canDisplayPublicly);
        const publicData = filteredData.map(partner => (0, class_transformer_1.plainToInstance)(cms_partner_public_dto_1.CmsPartnerPublicDto, partner, {
            excludeExtraneousValues: true,
        }));
        const total = publicData.length;
        const meta = new custom_pagination_meta_dto_1.CustomPageMetaDto({
            pageQueryDto: paginationQuery,
            itemCount: total,
        });
        return new pagination_response_dto_1.PaginationResponseDto(publicData, meta);
    }
    async search(searchTerm, paginationQuery) {
        this.logger.log(`Searching CMS Partners with term: ${searchTerm} for public display`);
        const { data: allData } = await this.readCmsPartnersService.search(searchTerm, paginationQuery);
        const filteredData = allData.filter(partner => partner.canDisplayPublicly);
        const publicData = filteredData.map(partner => (0, class_transformer_1.plainToInstance)(cms_partner_public_dto_1.CmsPartnerPublicDto, partner, {
            excludeExtraneousValues: true,
        }));
        const total = publicData.length;
        const meta = new custom_pagination_meta_dto_1.CustomPageMetaDto({
            pageQueryDto: paginationQuery,
            itemCount: total,
        });
        return new pagination_response_dto_1.PaginationResponseDto(publicData, meta);
    }
    async findAllPublic(paginationQuery) {
        this.logger.log('Getting all CMS Partners for public display with search and filter');
        if (paginationQuery.search) {
            const { data: allData } = await this.readCmsPartnersService.search(paginationQuery.search, paginationQuery);
            const filteredData = allData.filter(partner => partner.canDisplayPublicly);
            const publicData = filteredData.map(partner => (0, class_transformer_1.plainToInstance)(cms_partner_public_dto_1.CmsPartnerPublicDto, partner, {
                excludeExtraneousValues: true,
            }));
            const total = publicData.length;
            const meta = new custom_pagination_meta_dto_1.CustomPageMetaDto({
                pageQueryDto: paginationQuery,
                itemCount: total,
            });
            return new pagination_response_dto_1.PaginationResponseDto(publicData, meta);
        }
        const { data, total } = await this.readCmsPartnersService.getActivePartners(paginationQuery);
        const publicData = data.map(partner => (0, class_transformer_1.plainToInstance)(cms_partner_public_dto_1.CmsPartnerPublicDto, partner, {
            excludeExtraneousValues: true,
        }));
        const meta = new custom_pagination_meta_dto_1.CustomPageMetaDto({
            pageQueryDto: paginationQuery,
            itemCount: total,
        });
        return new pagination_response_dto_1.PaginationResponseDto(publicData, meta);
    }
    async findById(id) {
        this.logger.log(`Getting CMS Partner by ID: ${id} for public display`);
        const partner = await this.readCmsPartnersService.getPartnerById(id);
        if (!partner.canDisplayPublicly) {
            throw new Error('Đối tác không thể hiển thị công khai');
        }
        return (0, class_transformer_1.plainToInstance)(cms_partner_public_dto_1.CmsPartnerPublicDto, partner, {
            excludeExtraneousValues: true,
        });
    }
    async getPublicStatistics() {
        this.logger.log('Getting CMS Partners public statistics');
        const stats = await this.readCmsPartnersService.getStatistics();
        return {
            totalActive: stats.active,
            byType: {
                partner: stats.byType[dto_1.CmsPartnerType.PARTNER],
                client: stats.byType[dto_1.CmsPartnerType.CLIENT],
                supplier: stats.byType[dto_1.CmsPartnerType.SUPPLIER],
            },
            withLogo: stats.withLogo,
            canDisplayInBanner: stats.withLogo,
            clickablePartners: stats.active,
        };
    }
};
exports.ReadCmsPartnersPublicController = ReadCmsPartnersPublicController;
__decorate([
    (0, common_1.Get)('active'),
    (0, common_1.UsePipes)(new common_1.ValidationPipe({ transform: true, whitelist: true })),
    (0, swagger_1.ApiOperation)({
        summary: 'Lấy đối tác đang hoạt động (Public)',
        description: 'Lấy danh sách đối tác có trạng thái active cho hiển thị công khai',
    }),
    (0, swagger_1.ApiQuery)({
        name: 'page',
        required: false,
        type: Number,
        description: 'Số trang (mặc định: 1)',
        example: 1,
    }),
    (0, swagger_1.ApiQuery)({
        name: 'limit',
        required: false,
        type: Number,
        description: 'Số lượng mỗi trang (mặc định: 10)',
        example: 10,
    }),
    (0, swagger_1.ApiQuery)({
        name: 'sortBy',
        required: false,
        type: String,
        description: 'Trường sắp xếp (mặc định: displayOrder)',
        example: 'displayOrder',
    }),
    (0, swagger_1.ApiQuery)({
        name: 'sortOrder',
        required: false,
        enum: ['ASC', 'DESC'],
        description: 'Thứ tự sắp xếp (mặc định: ASC)',
        example: 'ASC',
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Danh sách đối tác đang hoạt động',
        type: () => pagination_response_dto_1.PaginationResponseDto,
    }),
    openapi.ApiResponse({ status: 200 }),
    __param(0, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [custom_pagination_query_dto_1.CustomPaginationQueryDto]),
    __metadata("design:returntype", Promise)
], ReadCmsPartnersPublicController.prototype, "getActivePartners", null);
__decorate([
    (0, common_1.Get)('by-type/:type'),
    (0, common_1.UsePipes)(new common_1.ValidationPipe({ transform: true, whitelist: true })),
    (0, swagger_1.ApiOperation)({
        summary: 'Lấy đối tác theo loại (Public)',
        description: 'Lấy danh sách đối tác theo loại cụ thể cho hiển thị công khai',
    }),
    (0, swagger_1.ApiParam)({
        name: 'type',
        enum: dto_1.CmsPartnerType,
        description: 'Loại đối tác',
        example: dto_1.CmsPartnerType.PARTNER,
    }),
    (0, swagger_1.ApiQuery)({
        name: 'page',
        required: false,
        type: Number,
        description: 'Số trang (mặc định: 1)',
        example: 1,
    }),
    (0, swagger_1.ApiQuery)({
        name: 'limit',
        required: false,
        type: Number,
        description: 'Số lượng mỗi trang (mặc định: 10)',
        example: 10,
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Danh sách đối tác theo loại',
        type: () => pagination_response_dto_1.PaginationResponseDto,
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.BAD_REQUEST,
        description: 'Loại đối tác không hợp lệ',
    }),
    openapi.ApiResponse({ status: 200 }),
    __param(0, (0, common_1.Param)('type')),
    __param(1, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, custom_pagination_query_dto_1.CustomPaginationQueryDto]),
    __metadata("design:returntype", Promise)
], ReadCmsPartnersPublicController.prototype, "getPartnersByType", null);
__decorate([
    (0, common_1.Get)('search'),
    (0, common_1.UsePipes)(new common_1.ValidationPipe({ transform: true, whitelist: true })),
    (0, swagger_1.ApiOperation)({
        summary: 'Tìm kiếm đối tác (Public)',
        description: 'Tìm kiếm đối tác theo tên hoặc mô tả cho hiển thị công khai',
    }),
    (0, swagger_1.ApiQuery)({
        name: 'q',
        required: true,
        type: String,
        description: 'Từ khóa tìm kiếm',
        example: 'công ty',
    }),
    (0, swagger_1.ApiQuery)({
        name: 'page',
        required: false,
        type: Number,
        description: 'Số trang (mặc định: 1)',
        example: 1,
    }),
    (0, swagger_1.ApiQuery)({
        name: 'limit',
        required: false,
        type: Number,
        description: 'Số lượng mỗi trang (mặc định: 10)',
        example: 10,
    }),
    (0, swagger_1.ApiQuery)({
        name: 'sort',
        required: false,
        type: String,
        description: 'Sắp xếp theo định dạng field:order (ví dụ: displayOrder:ASC,name:DESC)',
        example: 'displayOrder:ASC',
    }),
    (0, swagger_1.ApiQuery)({
        name: 'filter',
        required: false,
        type: String,
        description: 'Lọc theo trường (ví dụ: type:PARTNER)',
        example: 'type:PARTNER',
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Kết quả tìm kiếm đối tác',
        type: () => pagination_response_dto_1.PaginationResponseDto,
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.BAD_REQUEST,
        description: 'Từ khóa tìm kiếm không hợp lệ',
    }),
    openapi.ApiResponse({ status: 200 }),
    __param(0, (0, common_1.Query)('q')),
    __param(1, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, custom_pagination_query_dto_1.CustomPaginationQueryDto]),
    __metadata("design:returntype", Promise)
], ReadCmsPartnersPublicController.prototype, "search", null);
__decorate([
    (0, common_1.Get)('list'),
    (0, common_1.UsePipes)(new common_1.ValidationPipe({ transform: true, whitelist: true })),
    (0, swagger_1.ApiOperation)({
        summary: 'Lấy danh sách đối tác với tìm kiếm và lọc (Public)',
        description: 'Lấy danh sách đối tác với khả năng tìm kiếm, lọc và sắp xếp cho hiển thị công khai',
    }),
    (0, swagger_1.ApiQuery)({
        name: 'page',
        required: false,
        type: Number,
        description: 'Số trang (mặc định: 1)',
        example: 1,
    }),
    (0, swagger_1.ApiQuery)({
        name: 'limit',
        required: false,
        type: Number,
        description: 'Số lượng mỗi trang (mặc định: 10)',
        example: 10,
    }),
    (0, swagger_1.ApiQuery)({
        name: 'search',
        required: false,
        type: String,
        description: 'Từ khóa tìm kiếm trong tên và mô tả',
        example: 'công ty',
    }),
    (0, swagger_1.ApiQuery)({
        name: 'sort',
        required: false,
        type: String,
        description: 'Sắp xếp theo định dạng field:order (ví dụ: displayOrder:ASC,name:DESC)',
        example: 'displayOrder:ASC',
    }),
    (0, swagger_1.ApiQuery)({
        name: 'filter',
        required: false,
        type: String,
        description: 'Lọc theo trường (ví dụ: type:PARTNER)',
        example: 'type:PARTNER',
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Danh sách đối tác với phân trang',
        type: () => pagination_response_dto_1.PaginationResponseDto,
    }),
    openapi.ApiResponse({ status: 200 }),
    __param(0, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [custom_pagination_query_dto_1.CustomPaginationQueryDto]),
    __metadata("design:returntype", Promise)
], ReadCmsPartnersPublicController.prototype, "findAllPublic", null);
__decorate([
    (0, common_1.Get)(':id'),
    (0, swagger_1.ApiOperation)({
        summary: 'Lấy thông tin đối tác (Public)',
        description: 'Lấy thông tin chi tiết của một đối tác cho hiển thị công khai',
    }),
    (0, swagger_1.ApiParam)({
        name: 'id',
        description: 'ID của đối tác',
        example: '123e4567-e89b-12d3-a456-426614174000',
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Thông tin đối tác',
        type: cms_partner_public_dto_1.CmsPartnerPublicDto,
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.NOT_FOUND,
        description: 'Không tìm thấy đối tác hoặc đối tác không thể hiển thị công khai',
    }),
    openapi.ApiResponse({ status: 200, type: require("../dto/cms-partner.public.dto").CmsPartnerPublicDto }),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], ReadCmsPartnersPublicController.prototype, "findById", null);
__decorate([
    (0, common_1.Get)('statistics'),
    (0, swagger_1.ApiOperation)({
        summary: 'Lấy thống kê đối tác (Public)',
        description: 'Lấy thống kê tổng quan về đối tác cho hiển thị công khai',
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Thống kê đối tác công khai',
        schema: {
            type: 'object',
            properties: {
                totalActive: { type: 'number', description: 'Tổng số đối tác đang hoạt động' },
                byType: {
                    type: 'object',
                    description: 'Thống kê theo loại đối tác (chỉ active)',
                    properties: {
                        partner: { type: 'number' },
                        client: { type: 'number' },
                        supplier: { type: 'number' },
                    },
                },
                withLogo: { type: 'number', description: 'Số đối tác có logo' },
                canDisplayPublicly: { type: 'number', description: 'Số đối tác có thể hiển thị công khai' },
            },
        },
    }),
    openapi.ApiResponse({ status: 200, type: require("../dto/cms-partner.public.dto").CmsPartnerPublicStatisticsDto }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], ReadCmsPartnersPublicController.prototype, "getPublicStatistics", null);
exports.ReadCmsPartnersPublicController = ReadCmsPartnersPublicController = ReadCmsPartnersPublicController_1 = __decorate([
    (0, swagger_1.ApiTags)('CMS Partners - Public'),
    (0, common_1.Controller)('cms/partners/public'),
    __metadata("design:paramtypes", [read_cms_partners_service_1.ReadCmsPartnersService])
], ReadCmsPartnersPublicController);
//# sourceMappingURL=read.cms-partners.public.controller.js.map
"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ReadCmsTagsPublicController = void 0;
const openapi = require("@nestjs/swagger");
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const read_cms_tags_service_1 = require("../services/read.cms-tags.service");
const api_response_dto_1 = require("../../common/dto/api-response.dto");
const custom_pagination_query_dto_1 = require("../../common/dto/custom-pagination-query.dto");
const pagination_response_dto_1 = require("../../common/dto/pagination-response.dto");
const custom_pagination_meta_dto_1 = require("../../common/dto/custom-pagination-meta.dto");
let ReadCmsTagsPublicController = class ReadCmsTagsPublicController {
    cmsTagsService;
    constructor(cmsTagsService) {
        this.cmsTagsService = cmsTagsService;
    }
    async getTags(paginationQuery) {
        const { data, total } = await this.cmsTagsService.findAll(paginationQuery);
        const meta = new custom_pagination_meta_dto_1.CustomPageMetaDto({
            pageQueryDto: paginationQuery,
            itemCount: total,
        });
        return new pagination_response_dto_1.PaginationResponseDto(data, meta);
    }
    async getPopularTags(limit) {
        return this.cmsTagsService.getPopularTags(limit || 10);
    }
    async findBySlug(slug) {
        return this.cmsTagsService.findBySlugPublic(slug);
    }
    async searchTags(keyword, paginationQuery) {
        const { data, total } = await this.cmsTagsService.search(keyword, paginationQuery);
        const meta = new custom_pagination_meta_dto_1.CustomPageMetaDto({
            pageQueryDto: paginationQuery,
            itemCount: total,
        });
        return new pagination_response_dto_1.PaginationResponseDto(data, meta);
    }
    async getStatistics() {
        return this.cmsTagsService.getStatistics();
    }
};
exports.ReadCmsTagsPublicController = ReadCmsTagsPublicController;
__decorate([
    (0, common_1.Get)('list'),
    (0, swagger_1.ApiOperation)({ summary: 'Lấy danh sách thẻ CMS (public)' }),
    (0, swagger_1.ApiQuery)({ name: 'page', required: false, type: Number, description: 'Số trang', example: 1 }),
    (0, swagger_1.ApiQuery)({ name: 'limit', required: false, type: Number, description: 'Số lượng tag mỗi trang', example: 20 }),
    (0, swagger_1.ApiQuery)({ name: 'search', required: false, type: String, description: 'Tìm kiếm theo từ khóa' }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Danh sách thẻ CMS',
        type: pagination_response_dto_1.PaginationResponseDto,
    }),
    openapi.ApiResponse({ status: 200 }),
    __param(0, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [custom_pagination_query_dto_1.CustomPaginationQueryDto]),
    __metadata("design:returntype", Promise)
], ReadCmsTagsPublicController.prototype, "getTags", null);
__decorate([
    (0, common_1.Get)('popular'),
    (0, swagger_1.ApiOperation)({ summary: 'Lấy danh sách thẻ phổ biến (public)' }),
    (0, swagger_1.ApiQuery)({ name: 'limit', required: false, type: Number, description: 'Số lượng thẻ cần lấy', example: 10 }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Danh sách thẻ phổ biến',
        type: api_response_dto_1.ApiResponseDto,
    }),
    openapi.ApiResponse({ status: 200, type: [require("../dto/cms-tag.dto").CmsTagDto] }),
    __param(0, (0, common_1.Query)('limit')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", Promise)
], ReadCmsTagsPublicController.prototype, "getPopularTags", null);
__decorate([
    (0, common_1.Get)('slug/:slug'),
    (0, swagger_1.ApiOperation)({ summary: 'Lấy thông tin thẻ CMS theo slug (public)' }),
    (0, swagger_1.ApiParam)({ name: 'slug', type: String, description: 'Slug của thẻ CMS' }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Thông tin thẻ CMS',
        type: api_response_dto_1.ApiResponseDto,
    }),
    openapi.ApiResponse({ status: 200, type: Object }),
    __param(0, (0, common_1.Param)('slug')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], ReadCmsTagsPublicController.prototype, "findBySlug", null);
__decorate([
    (0, common_1.Get)('search'),
    (0, swagger_1.ApiOperation)({ summary: 'Tìm kiếm thẻ CMS (public)' }),
    (0, swagger_1.ApiQuery)({ name: 'keyword', required: true, type: String, description: 'Từ khóa tìm kiếm' }),
    (0, swagger_1.ApiQuery)({ name: 'page', required: false, type: Number, description: 'Số trang' }),
    (0, swagger_1.ApiQuery)({ name: 'limit', required: false, type: Number, description: 'Số lượng bản ghi mỗi trang' }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Kết quả tìm kiếm thẻ CMS',
        type: pagination_response_dto_1.PaginationResponseDto,
    }),
    openapi.ApiResponse({ status: 200 }),
    __param(0, (0, common_1.Query)('keyword')),
    __param(1, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, custom_pagination_query_dto_1.CustomPaginationQueryDto]),
    __metadata("design:returntype", Promise)
], ReadCmsTagsPublicController.prototype, "searchTags", null);
__decorate([
    (0, common_1.Get)('statistics'),
    (0, swagger_1.ApiOperation)({ summary: 'Lấy thống kê thẻ CMS (public)' }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Thống kê thẻ CMS',
        type: api_response_dto_1.ApiResponseDto,
    }),
    openapi.ApiResponse({ status: 200 }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], ReadCmsTagsPublicController.prototype, "getStatistics", null);
exports.ReadCmsTagsPublicController = ReadCmsTagsPublicController = __decorate([
    (0, swagger_1.ApiTags)('cms-tags-public'),
    (0, common_1.Controller)('cms/tags/public'),
    __metadata("design:paramtypes", [read_cms_tags_service_1.ReadCmsTagsService])
], ReadCmsTagsPublicController);
//# sourceMappingURL=read.cms-tags.public.controller.js.map
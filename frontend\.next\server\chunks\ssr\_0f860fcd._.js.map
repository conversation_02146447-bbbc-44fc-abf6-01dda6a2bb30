{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/bloomgoo/gold-exchange-platform/frontend/hooks/use-mobile.ts"], "sourcesContent": ["import * as React from \"react\"\r\n\r\nconst MO<PERSON>LE_BREAKPOINT = 768\r\n\r\nexport function useIsMobile() {\r\n  const [isMobile, setIsMobile] = React.useState<boolean | undefined>(undefined)\r\n\r\n  React.useEffect(() => {\r\n    const mql = window.matchMedia(`(max-width: ${MOBILE_BREAKPOINT - 1}px)`)\r\n    const onChange = () => {\r\n      setIsMobile(window.innerWidth < MOBILE_BREAKPOINT)\r\n    }\r\n    mql.addEventListener(\"change\", onChange)\r\n    setIsMobile(window.innerWidth < MOBILE_BREAKPOINT)\r\n    return () => mql.removeEventListener(\"change\", onChange)\r\n  }, [])\r\n\r\n  return !!isMobile\r\n}\r\n"], "names": [], "mappings": ";;;AAAA;;AAEA,MAAM,oBAAoB;AAEnB,SAAS;IACd,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,8SAAA,CAAA,WAAc,AAAD,EAAuB;IAEpE,CAAA,GAAA,8SAAA,CAAA,YAAe,AAAD,EAAE;QACd,MAAM,MAAM,OAAO,UAAU,CAAC,CAAC,YAAY,EAAE,oBAAoB,EAAE,GAAG,CAAC;QACvE,MAAM,WAAW;YACf,YAAY,OAAO,UAAU,GAAG;QAClC;QACA,IAAI,gBAAgB,CAAC,UAAU;QAC/B,YAAY,OAAO,UAAU,GAAG;QAChC,OAAO,IAAM,IAAI,mBAAmB,CAAC,UAAU;IACjD,GAAG,EAAE;IAEL,OAAO,CAAC,CAAC;AACX", "debugId": null}}, {"offset": {"line": 32, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/bloomgoo/gold-exchange-platform/frontend/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\r\nimport { Slot } from \"@radix-ui/react-slot\"\r\nimport { cva, type VariantProps } from \"class-variance-authority\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst buttonVariants = cva(\r\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\r\n  {\r\n    variants: {\r\n      variant: {\r\n        default:\r\n          \"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90\",\r\n        destructive:\r\n          \"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\r\n        outline:\r\n          \"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50\",\r\n        secondary:\r\n          \"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80\",\r\n        ghost:\r\n          \"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50\",\r\n        link: \"text-primary underline-offset-4 hover:underline\",\r\n      },\r\n      size: {\r\n        default: \"h-9 px-4 py-2 has-[>svg]:px-3\",\r\n        sm: \"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5\",\r\n        lg: \"h-10 rounded-md px-6 has-[>svg]:px-4\",\r\n        icon: \"size-9\",\r\n      },\r\n    },\r\n    defaultVariants: {\r\n      variant: \"default\",\r\n      size: \"default\",\r\n    },\r\n  }\r\n)\r\n\r\nfunction Button({\r\n  className,\r\n  variant,\r\n  size,\r\n  asChild = false,\r\n  ...props\r\n}: React.ComponentProps<\"button\"> &\r\n  VariantProps<typeof buttonVariants> & {\r\n    asChild?: boolean\r\n  }) {\r\n  const Comp = asChild ? Slot : \"button\"\r\n\r\n  return (\r\n    <Comp\r\n      data-slot=\"button\"\r\n      className={cn(buttonVariants({ variant, size, className }))}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport { Button, buttonVariants }\r\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;AAAA;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,2OAAA,CAAA,MAAG,AAAD,EACvB,+bACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OACE;YACF,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,SAAS,OAAO,EACd,SAAS,EACT,OAAO,EACP,IAAI,EACJ,UAAU,KAAK,EACf,GAAG,OAIF;IACD,MAAM,OAAO,UAAU,oSAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,uVAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACvD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 90, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/bloomgoo/gold-exchange-platform/frontend/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction Input({ className, type, ...props }: React.ComponentProps<\"input\">) {\r\n  return (\r\n    <input\r\n      type={type}\r\n      data-slot=\"input\"\r\n      className={cn(\r\n        \"file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\r\n        \"focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]\",\r\n        \"aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport { Input }\r\n"], "names": [], "mappings": ";;;;AAEA;AAAA;;;AAEA,SAAS,MAAM,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAsC;IACzE,qBACE,uVAAC;QACC,MAAM;QACN,aAAU;QACV,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EACV,mcACA,iFACA,0GACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 117, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/bloomgoo/gold-exchange-platform/frontend/components/ui/separator.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as SeparatorPrimitive from \"@radix-ui/react-separator\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction Separator({\r\n  className,\r\n  orientation = \"horizontal\",\r\n  decorative = true,\r\n  ...props\r\n}: React.ComponentProps<typeof SeparatorPrimitive.Root>) {\r\n  return (\r\n    <SeparatorPrimitive.Root\r\n      data-slot=\"separator-root\"\r\n      decorative={decorative}\r\n      orientation={orientation}\r\n      className={cn(\r\n        \"bg-border shrink-0 data-[orientation=horizontal]:h-px data-[orientation=horizontal]:w-full data-[orientation=vertical]:h-full data-[orientation=vertical]:w-px\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport { Separator }\r\n"], "names": [], "mappings": ";;;;AAGA;AAEA;AAAA;AALA;;;;AAOA,SAAS,UAAU,EACjB,SAAS,EACT,cAAc,YAAY,EAC1B,aAAa,IAAI,EACjB,GAAG,OACkD;IACrD,qBACE,uVAAC,4QAAA,CAAA,OAAuB;QACtB,aAAU;QACV,YAAY;QACZ,aAAa;QACb,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EACV,kKACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 148, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/bloomgoo/gold-exchange-platform/frontend/components/ui/sheet.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as SheetPrimitive from \"@radix-ui/react-dialog\"\r\nimport { XIcon } from \"lucide-react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction Sheet({ ...props }: React.ComponentProps<typeof SheetPrimitive.Root>) {\r\n  return <SheetPrimitive.Root data-slot=\"sheet\" {...props} />\r\n}\r\n\r\nfunction SheetTrigger({\r\n  ...props\r\n}: React.ComponentProps<typeof SheetPrimitive.Trigger>) {\r\n  return <SheetPrimitive.Trigger data-slot=\"sheet-trigger\" {...props} />\r\n}\r\n\r\nfunction SheetClose({\r\n  ...props\r\n}: React.ComponentProps<typeof SheetPrimitive.Close>) {\r\n  return <SheetPrimitive.Close data-slot=\"sheet-close\" {...props} />\r\n}\r\n\r\nfunction SheetPortal({\r\n  ...props\r\n}: React.ComponentProps<typeof SheetPrimitive.Portal>) {\r\n  return <SheetPrimitive.Portal data-slot=\"sheet-portal\" {...props} />\r\n}\r\n\r\nfunction SheetOverlay({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof SheetPrimitive.Overlay>) {\r\n  return (\r\n    <SheetPrimitive.Overlay\r\n      data-slot=\"sheet-overlay\"\r\n      className={cn(\r\n        \"data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction SheetContent({\r\n  className,\r\n  children,\r\n  side = \"right\",\r\n  ...props\r\n}: React.ComponentProps<typeof SheetPrimitive.Content> & {\r\n  side?: \"top\" | \"right\" | \"bottom\" | \"left\"\r\n}) {\r\n  return (\r\n    <SheetPortal>\r\n      <SheetOverlay />\r\n      <SheetPrimitive.Content\r\n        data-slot=\"sheet-content\"\r\n        className={cn(\r\n          \"bg-background data-[state=open]:animate-in data-[state=closed]:animate-out fixed z-50 flex flex-col gap-4 shadow-lg transition ease-in-out data-[state=closed]:duration-300 data-[state=open]:duration-500\",\r\n          side === \"right\" &&\r\n            \"data-[state=closed]:slide-out-to-right data-[state=open]:slide-in-from-right inset-y-0 right-0 h-full w-3/4 border-l sm:max-w-sm\",\r\n          side === \"left\" &&\r\n            \"data-[state=closed]:slide-out-to-left data-[state=open]:slide-in-from-left inset-y-0 left-0 h-full w-3/4 border-r sm:max-w-sm\",\r\n          side === \"top\" &&\r\n            \"data-[state=closed]:slide-out-to-top data-[state=open]:slide-in-from-top inset-x-0 top-0 h-auto border-b\",\r\n          side === \"bottom\" &&\r\n            \"data-[state=closed]:slide-out-to-bottom data-[state=open]:slide-in-from-bottom inset-x-0 bottom-0 h-auto border-t\",\r\n          className\r\n        )}\r\n        {...props}\r\n      >\r\n        {children}\r\n        <SheetPrimitive.Close className=\"ring-offset-background focus:ring-ring data-[state=open]:bg-secondary absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none\">\r\n          <XIcon className=\"size-4\" />\r\n          <span className=\"sr-only\">Close</span>\r\n        </SheetPrimitive.Close>\r\n      </SheetPrimitive.Content>\r\n    </SheetPortal>\r\n  )\r\n}\r\n\r\nfunction SheetHeader({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"sheet-header\"\r\n      className={cn(\"flex flex-col gap-1.5 p-4\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction SheetFooter({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"sheet-footer\"\r\n      className={cn(\"mt-auto flex flex-col gap-2 p-4\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction SheetTitle({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof SheetPrimitive.Title>) {\r\n  return (\r\n    <SheetPrimitive.Title\r\n      data-slot=\"sheet-title\"\r\n      className={cn(\"text-foreground font-semibold\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction SheetDescription({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof SheetPrimitive.Description>) {\r\n  return (\r\n    <SheetPrimitive.Description\r\n      data-slot=\"sheet-description\"\r\n      className={cn(\"text-muted-foreground text-sm\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport {\r\n  Sheet,\r\n  SheetTrigger,\r\n  SheetClose,\r\n  SheetContent,\r\n  SheetHeader,\r\n  SheetFooter,\r\n  SheetTitle,\r\n  SheetDescription,\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;;AAGA;AACA;AAEA;AAAA;AANA;;;;;AAQA,SAAS,MAAM,EAAE,GAAG,OAAyD;IAC3E,qBAAO,uVAAC,+QAAA,CAAA,OAAmB;QAAC,aAAU;QAAS,GAAG,KAAK;;;;;;AACzD;AAEA,SAAS,aAAa,EACpB,GAAG,OACiD;IACpD,qBAAO,uVAAC,+QAAA,CAAA,UAAsB;QAAC,aAAU;QAAiB,GAAG,KAAK;;;;;;AACpE;AAEA,SAAS,WAAW,EAClB,GAAG,OAC+C;IAClD,qBAAO,uVAAC,+QAAA,CAAA,QAAoB;QAAC,aAAU;QAAe,GAAG,KAAK;;;;;;AAChE;AAEA,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,uVAAC,+QAAA,CAAA,SAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;AAEA,SAAS,aAAa,EACpB,SAAS,EACT,GAAG,OACiD;IACpD,qBACE,uVAAC,+QAAA,CAAA,UAAsB;QACrB,aAAU;QACV,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EACV,0JACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,aAAa,EACpB,SAAS,EACT,QAAQ,EACR,OAAO,OAAO,EACd,GAAG,OAGJ;IACC,qBACE,uVAAC;;0BACC,uVAAC;;;;;0BACD,uVAAC,+QAAA,CAAA,UAAsB;gBACrB,aAAU;gBACV,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EACV,8MACA,SAAS,WACP,oIACF,SAAS,UACP,iIACF,SAAS,SACP,4GACF,SAAS,YACP,qHACF;gBAED,GAAG,KAAK;;oBAER;kCACD,uVAAC,+QAAA,CAAA,QAAoB;wBAAC,WAAU;;0CAC9B,uVAAC,oRAAA,CAAA,QAAK;gCAAC,WAAU;;;;;;0CACjB,uVAAC;gCAAK,WAAU;0CAAU;;;;;;;;;;;;;;;;;;;;;;;;AAKpC;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,uVAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EAAE,6BAA6B;QAC1C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,uVAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EAAE,mCAAmC;QAChD,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAClB,SAAS,EACT,GAAG,OAC+C;IAClD,qBACE,uVAAC,+QAAA,CAAA,QAAoB;QACnB,aAAU;QACV,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,iBAAiB,EACxB,SAAS,EACT,GAAG,OACqD;IACxD,qBACE,uVAAC,+QAAA,CAAA,cAA0B;QACzB,aAAU;QACV,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 321, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/bloomgoo/gold-exchange-platform/frontend/components/ui/skeleton.tsx"], "sourcesContent": ["import { cn } from \"@/lib/utils\"\r\n\r\nfunction Skeleton({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"skeleton\"\r\n      className={cn(\"bg-accent animate-pulse rounded-md\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport { Skeleton }\r\n"], "names": [], "mappings": ";;;;AAAA;AAAA;;;AAEA,SAAS,SAAS,EAAE,SAAS,EAAE,GAAG,OAAoC;IACpE,qBACE,uVAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QACnD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 347, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/bloomgoo/gold-exchange-platform/frontend/components/ui/tooltip.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as TooltipPrimitive from \"@radix-ui/react-tooltip\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction TooltipProvider({\r\n  delayDuration = 0,\r\n  ...props\r\n}: React.ComponentProps<typeof TooltipPrimitive.Provider>) {\r\n  return (\r\n    <TooltipPrimitive.Provider\r\n      data-slot=\"tooltip-provider\"\r\n      delayDuration={delayDuration}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction Tooltip({\r\n  ...props\r\n}: React.ComponentProps<typeof TooltipPrimitive.Root>) {\r\n  return (\r\n    <TooltipProvider>\r\n      <TooltipPrimitive.Root data-slot=\"tooltip\" {...props} />\r\n    </TooltipProvider>\r\n  )\r\n}\r\n\r\nfunction TooltipTrigger({\r\n  ...props\r\n}: React.ComponentProps<typeof TooltipPrimitive.Trigger>) {\r\n  return <TooltipPrimitive.Trigger data-slot=\"tooltip-trigger\" {...props} />\r\n}\r\n\r\nfunction TooltipContent({\r\n  className,\r\n  sideOffset = 0,\r\n  children,\r\n  ...props\r\n}: React.ComponentProps<typeof TooltipPrimitive.Content>) {\r\n  return (\r\n    <TooltipPrimitive.Portal>\r\n      <TooltipPrimitive.Content\r\n        data-slot=\"tooltip-content\"\r\n        sideOffset={sideOffset}\r\n        className={cn(\r\n          \"bg-primary text-primary-foreground animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 w-fit origin-(--radix-tooltip-content-transform-origin) rounded-md px-3 py-1.5 text-xs text-balance\",\r\n          className\r\n        )}\r\n        {...props}\r\n      >\r\n        {children}\r\n        <TooltipPrimitive.Arrow className=\"bg-primary fill-primary z-50 size-2.5 translate-y-[calc(-50%_-_2px)] rotate-45 rounded-[2px]\" />\r\n      </TooltipPrimitive.Content>\r\n    </TooltipPrimitive.Portal>\r\n  )\r\n}\r\n\r\nexport { Tooltip, TooltipTrigger, TooltipContent, TooltipProvider }\r\n"], "names": [], "mappings": ";;;;;;;AAGA;AAEA;AAAA;AALA;;;;AAOA,SAAS,gBAAgB,EACvB,gBAAgB,CAAC,EACjB,GAAG,OACoD;IACvD,qBACE,uVAAC,6QAAA,CAAA,WAAyB;QACxB,aAAU;QACV,eAAe;QACd,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,QAAQ,EACf,GAAG,OACgD;IACnD,qBACE,uVAAC;kBACC,cAAA,uVAAC,6QAAA,CAAA,OAAqB;YAAC,aAAU;YAAW,GAAG,KAAK;;;;;;;;;;;AAG1D;AAEA,SAAS,eAAe,EACtB,GAAG,OACmD;IACtD,qBAAO,uVAAC,6QAAA,CAAA,UAAwB;QAAC,aAAU;QAAmB,GAAG,KAAK;;;;;;AACxE;AAEA,SAAS,eAAe,EACtB,SAAS,EACT,aAAa,CAAC,EACd,QAAQ,EACR,GAAG,OACmD;IACtD,qBACE,uVAAC,6QAAA,CAAA,SAAuB;kBACtB,cAAA,uVAAC,6QAAA,CAAA,UAAwB;YACvB,aAAU;YACV,YAAY;YACZ,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EACV,0aACA;YAED,GAAG,KAAK;;gBAER;8BACD,uVAAC,6QAAA,CAAA,QAAsB;oBAAC,WAAU;;;;;;;;;;;;;;;;;AAI1C", "debugId": null}}, {"offset": {"line": 433, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/bloomgoo/gold-exchange-platform/frontend/components/ui/sidebar.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport { Slot } from \"@radix-ui/react-slot\"\r\nimport { VariantProps, cva } from \"class-variance-authority\"\r\nimport { PanelLeftIcon } from \"lucide-react\"\r\n\r\nimport { useIsMobile } from \"@/hooks/use-mobile\"\r\nimport { cn } from \"@/lib/utils\"\r\nimport { Button } from \"@/components/ui/button\"\r\nimport { Input } from \"@/components/ui/input\"\r\nimport { Separator } from \"@/components/ui/separator\"\r\nimport {\r\n  Sheet,\r\n  SheetContent,\r\n  SheetDescription,\r\n  SheetHeader,\r\n  SheetTitle,\r\n} from \"@/components/ui/sheet\"\r\nimport { Skeleton } from \"@/components/ui/skeleton\"\r\nimport {\r\n  Toolt<PERSON>,\r\n  TooltipContent,\r\n  TooltipProvider,\r\n  TooltipTrigger,\r\n} from \"@/components/ui/tooltip\"\r\n\r\nconst SIDEBAR_COOKIE_NAME = \"sidebar_state\"\r\nconst SIDEBAR_COOKIE_MAX_AGE = 60 * 60 * 24 * 7\r\nconst SIDEBAR_WIDTH = \"16rem\"\r\nconst SIDEBAR_WIDTH_MOBILE = \"18rem\"\r\nconst SIDEBAR_WIDTH_ICON = \"3rem\"\r\nconst SIDEBAR_KEYBOARD_SHORTCUT = \"b\"\r\n\r\ntype SidebarContextProps = {\r\n  state: \"expanded\" | \"collapsed\"\r\n  open: boolean\r\n  setOpen: (open: boolean) => void\r\n  openMobile: boolean\r\n  setOpenMobile: (open: boolean) => void\r\n  isMobile: boolean\r\n  toggleSidebar: () => void\r\n}\r\n\r\nconst SidebarContext = React.createContext<SidebarContextProps | null>(null)\r\n\r\nfunction useSidebar() {\r\n  const context = React.useContext(SidebarContext)\r\n  if (!context) {\r\n    throw new Error(\"useSidebar must be used within a SidebarProvider.\")\r\n  }\r\n\r\n  return context\r\n}\r\n\r\nfunction SidebarProvider({\r\n  defaultOpen = true,\r\n  open: openProp,\r\n  onOpenChange: setOpenProp,\r\n  className,\r\n  style,\r\n  children,\r\n  ...props\r\n}: React.ComponentProps<\"div\"> & {\r\n  defaultOpen?: boolean\r\n  open?: boolean\r\n  onOpenChange?: (open: boolean) => void\r\n}) {\r\n  const isMobile = useIsMobile()\r\n  const [openMobile, setOpenMobile] = React.useState(false)\r\n\r\n  // This is the internal state of the sidebar.\r\n  // We use openProp and setOpenProp for control from outside the component.\r\n  const [_open, _setOpen] = React.useState(defaultOpen)\r\n  const open = openProp ?? _open\r\n  const setOpen = React.useCallback(\r\n    (value: boolean | ((value: boolean) => boolean)) => {\r\n      const openState = typeof value === \"function\" ? value(open) : value\r\n      if (setOpenProp) {\r\n        setOpenProp(openState)\r\n      } else {\r\n        _setOpen(openState)\r\n      }\r\n\r\n      // This sets the cookie to keep the sidebar state.\r\n      document.cookie = `${SIDEBAR_COOKIE_NAME}=${openState}; path=/; max-age=${SIDEBAR_COOKIE_MAX_AGE}`\r\n    },\r\n    [setOpenProp, open]\r\n  )\r\n\r\n  // Helper to toggle the sidebar.\r\n  const toggleSidebar = React.useCallback(() => {\r\n    return isMobile ? setOpenMobile((open) => !open) : setOpen((open) => !open)\r\n  }, [isMobile, setOpen, setOpenMobile])\r\n\r\n  // Adds a keyboard shortcut to toggle the sidebar.\r\n  React.useEffect(() => {\r\n    const handleKeyDown = (event: KeyboardEvent) => {\r\n      if (\r\n        event.key === SIDEBAR_KEYBOARD_SHORTCUT &&\r\n        (event.metaKey || event.ctrlKey)\r\n      ) {\r\n        event.preventDefault()\r\n        toggleSidebar()\r\n      }\r\n    }\r\n\r\n    window.addEventListener(\"keydown\", handleKeyDown)\r\n    return () => window.removeEventListener(\"keydown\", handleKeyDown)\r\n  }, [toggleSidebar])\r\n\r\n  // We add a state so that we can do data-state=\"expanded\" or \"collapsed\".\r\n  // This makes it easier to style the sidebar with Tailwind classes.\r\n  const state = open ? \"expanded\" : \"collapsed\"\r\n\r\n  const contextValue = React.useMemo<SidebarContextProps>(\r\n    () => ({\r\n      state,\r\n      open,\r\n      setOpen,\r\n      isMobile,\r\n      openMobile,\r\n      setOpenMobile,\r\n      toggleSidebar,\r\n    }),\r\n    [state, open, setOpen, isMobile, openMobile, setOpenMobile, toggleSidebar]\r\n  )\r\n\r\n  return (\r\n    <SidebarContext.Provider value={contextValue}>\r\n      <TooltipProvider delayDuration={0}>\r\n        <div\r\n          data-slot=\"sidebar-wrapper\"\r\n          style={\r\n            {\r\n              \"--sidebar-width\": SIDEBAR_WIDTH,\r\n              \"--sidebar-width-icon\": SIDEBAR_WIDTH_ICON,\r\n              ...style,\r\n            } as React.CSSProperties\r\n          }\r\n          className={cn(\r\n            \"group/sidebar-wrapper has-data-[variant=inset]:bg-sidebar flex min-h-svh w-full\",\r\n            className\r\n          )}\r\n          {...props}\r\n        >\r\n          {children}\r\n        </div>\r\n      </TooltipProvider>\r\n    </SidebarContext.Provider>\r\n  )\r\n}\r\n\r\nfunction Sidebar({\r\n  side = \"left\",\r\n  variant = \"sidebar\",\r\n  collapsible = \"offcanvas\",\r\n  className,\r\n  children,\r\n  ...props\r\n}: React.ComponentProps<\"div\"> & {\r\n  side?: \"left\" | \"right\"\r\n  variant?: \"sidebar\" | \"floating\" | \"inset\"\r\n  collapsible?: \"offcanvas\" | \"icon\" | \"none\"\r\n}) {\r\n  const { isMobile, state, openMobile, setOpenMobile } = useSidebar()\r\n\r\n  if (collapsible === \"none\") {\r\n    return (\r\n      <div\r\n        data-slot=\"sidebar\"\r\n        className={cn(\r\n          \"bg-sidebar text-sidebar-foreground flex h-full w-(--sidebar-width) flex-col\",\r\n          className\r\n        )}\r\n        {...props}\r\n      >\r\n        {children}\r\n      </div>\r\n    )\r\n  }\r\n\r\n  if (isMobile) {\r\n    return (\r\n      <Sheet open={openMobile} onOpenChange={setOpenMobile} {...props}>\r\n        <SheetContent\r\n          data-sidebar=\"sidebar\"\r\n          data-slot=\"sidebar\"\r\n          data-mobile=\"true\"\r\n          className=\"bg-sidebar text-sidebar-foreground w-(--sidebar-width) p-0 [&>button]:hidden\"\r\n          style={\r\n            {\r\n              \"--sidebar-width\": SIDEBAR_WIDTH_MOBILE,\r\n            } as React.CSSProperties\r\n          }\r\n          side={side}\r\n        >\r\n          <SheetHeader className=\"sr-only\">\r\n            <SheetTitle>Sidebar</SheetTitle>\r\n            <SheetDescription>Displays the mobile sidebar.</SheetDescription>\r\n          </SheetHeader>\r\n          <div className=\"flex h-full w-full flex-col\">{children}</div>\r\n        </SheetContent>\r\n      </Sheet>\r\n    )\r\n  }\r\n\r\n  return (\r\n    <div\r\n      className=\"group peer text-sidebar-foreground hidden md:block\"\r\n      data-state={state}\r\n      data-collapsible={state === \"collapsed\" ? collapsible : \"\"}\r\n      data-variant={variant}\r\n      data-side={side}\r\n      data-slot=\"sidebar\"\r\n    >\r\n      {/* This is what handles the sidebar gap on desktop */}\r\n      <div\r\n        data-slot=\"sidebar-gap\"\r\n        className={cn(\r\n          \"relative w-(--sidebar-width) bg-transparent transition-[width] duration-200 ease-linear\",\r\n          \"group-data-[collapsible=offcanvas]:w-0\",\r\n          \"group-data-[side=right]:rotate-180\",\r\n          variant === \"floating\" || variant === \"inset\"\r\n            ? \"group-data-[collapsible=icon]:w-[calc(var(--sidebar-width-icon)+(--spacing(4)))]\"\r\n            : \"group-data-[collapsible=icon]:w-(--sidebar-width-icon)\"\r\n        )}\r\n      />\r\n      <div\r\n        data-slot=\"sidebar-container\"\r\n        className={cn(\r\n          \"fixed inset-y-0 z-10 hidden h-svh w-(--sidebar-width) transition-[left,right,width] duration-200 ease-linear md:flex\",\r\n          side === \"left\"\r\n            ? \"left-0 group-data-[collapsible=offcanvas]:left-[calc(var(--sidebar-width)*-1)]\"\r\n            : \"right-0 group-data-[collapsible=offcanvas]:right-[calc(var(--sidebar-width)*-1)]\",\r\n          // Adjust the padding for floating and inset variants.\r\n          variant === \"floating\" || variant === \"inset\"\r\n            ? \"p-2 group-data-[collapsible=icon]:w-[calc(var(--sidebar-width-icon)+(--spacing(4))+2px)]\"\r\n            : \"group-data-[collapsible=icon]:w-(--sidebar-width-icon) group-data-[side=left]:border-r group-data-[side=right]:border-l\",\r\n          className\r\n        )}\r\n        {...props}\r\n      >\r\n        <div\r\n          data-sidebar=\"sidebar\"\r\n          data-slot=\"sidebar-inner\"\r\n          className=\"bg-sidebar group-data-[variant=floating]:border-sidebar-border flex h-full w-full flex-col group-data-[variant=floating]:rounded-lg group-data-[variant=floating]:border group-data-[variant=floating]:shadow-sm\"\r\n        >\r\n          {children}\r\n        </div>\r\n      </div>\r\n    </div>\r\n  )\r\n}\r\n\r\nfunction SidebarTrigger({\r\n  className,\r\n  onClick,\r\n  ...props\r\n}: React.ComponentProps<typeof Button>) {\r\n  const { toggleSidebar } = useSidebar()\r\n\r\n  return (\r\n    <Button\r\n      data-sidebar=\"trigger\"\r\n      data-slot=\"sidebar-trigger\"\r\n      variant=\"ghost\"\r\n      size=\"icon\"\r\n      className={cn(\"size-7\", className)}\r\n      onClick={(event) => {\r\n        onClick?.(event)\r\n        toggleSidebar()\r\n      }}\r\n      {...props}\r\n    >\r\n      <PanelLeftIcon />\r\n      <span className=\"sr-only\">Toggle Sidebar</span>\r\n    </Button>\r\n  )\r\n}\r\n\r\nfunction SidebarRail({ className, ...props }: React.ComponentProps<\"button\">) {\r\n  const { toggleSidebar } = useSidebar()\r\n\r\n  return (\r\n    <button\r\n      data-sidebar=\"rail\"\r\n      data-slot=\"sidebar-rail\"\r\n      aria-label=\"Toggle Sidebar\"\r\n      tabIndex={-1}\r\n      onClick={toggleSidebar}\r\n      title=\"Toggle Sidebar\"\r\n      className={cn(\r\n        \"hover:after:bg-sidebar-border absolute inset-y-0 z-20 hidden w-4 -translate-x-1/2 transition-all ease-linear group-data-[side=left]:-right-4 group-data-[side=right]:left-0 after:absolute after:inset-y-0 after:left-1/2 after:w-[2px] sm:flex\",\r\n        \"in-data-[side=left]:cursor-w-resize in-data-[side=right]:cursor-e-resize\",\r\n        \"[[data-side=left][data-state=collapsed]_&]:cursor-e-resize [[data-side=right][data-state=collapsed]_&]:cursor-w-resize\",\r\n        \"hover:group-data-[collapsible=offcanvas]:bg-sidebar group-data-[collapsible=offcanvas]:translate-x-0 group-data-[collapsible=offcanvas]:after:left-full\",\r\n        \"[[data-side=left][data-collapsible=offcanvas]_&]:-right-2\",\r\n        \"[[data-side=right][data-collapsible=offcanvas]_&]:-left-2\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction SidebarInset({ className, ...props }: React.ComponentProps<\"main\">) {\r\n  return (\r\n    <main\r\n      data-slot=\"sidebar-inset\"\r\n      className={cn(\r\n        \"bg-background relative flex w-full flex-1 flex-col\",\r\n        \"md:peer-data-[variant=inset]:m-2 md:peer-data-[variant=inset]:ml-0 md:peer-data-[variant=inset]:rounded-xl md:peer-data-[variant=inset]:shadow-sm md:peer-data-[variant=inset]:peer-data-[state=collapsed]:ml-2\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction SidebarInput({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof Input>) {\r\n  return (\r\n    <Input\r\n      data-slot=\"sidebar-input\"\r\n      data-sidebar=\"input\"\r\n      className={cn(\"bg-background h-8 w-full shadow-none\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction SidebarHeader({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"sidebar-header\"\r\n      data-sidebar=\"header\"\r\n      className={cn(\"flex flex-col gap-2 p-2\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction SidebarFooter({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"sidebar-footer\"\r\n      data-sidebar=\"footer\"\r\n      className={cn(\"flex flex-col gap-2 p-2\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction SidebarSeparator({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof Separator>) {\r\n  return (\r\n    <Separator\r\n      data-slot=\"sidebar-separator\"\r\n      data-sidebar=\"separator\"\r\n      className={cn(\"bg-sidebar-border mx-2 w-auto\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction SidebarContent({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"sidebar-content\"\r\n      data-sidebar=\"content\"\r\n      className={cn(\r\n        \"flex min-h-0 flex-1 flex-col gap-2 overflow-auto group-data-[collapsible=icon]:overflow-hidden\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction SidebarGroup({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"sidebar-group\"\r\n      data-sidebar=\"group\"\r\n      className={cn(\"relative flex w-full min-w-0 flex-col p-2\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction SidebarGroupLabel({\r\n  className,\r\n  asChild = false,\r\n  ...props\r\n}: React.ComponentProps<\"div\"> & { asChild?: boolean }) {\r\n  const Comp = asChild ? Slot : \"div\"\r\n\r\n  return (\r\n    <Comp\r\n      data-slot=\"sidebar-group-label\"\r\n      data-sidebar=\"group-label\"\r\n      className={cn(\r\n        \"text-sidebar-foreground/70 ring-sidebar-ring flex h-8 shrink-0 items-center rounded-md px-2 text-xs font-medium outline-hidden transition-[margin,opacity] duration-200 ease-linear focus-visible:ring-2 [&>svg]:size-4 [&>svg]:shrink-0\",\r\n        \"group-data-[collapsible=icon]:-mt-8 group-data-[collapsible=icon]:opacity-0\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction SidebarGroupAction({\r\n  className,\r\n  asChild = false,\r\n  ...props\r\n}: React.ComponentProps<\"button\"> & { asChild?: boolean }) {\r\n  const Comp = asChild ? Slot : \"button\"\r\n\r\n  return (\r\n    <Comp\r\n      data-slot=\"sidebar-group-action\"\r\n      data-sidebar=\"group-action\"\r\n      className={cn(\r\n        \"text-sidebar-foreground ring-sidebar-ring hover:bg-sidebar-accent hover:text-sidebar-accent-foreground absolute top-3.5 right-3 flex aspect-square w-5 items-center justify-center rounded-md p-0 outline-hidden transition-transform focus-visible:ring-2 [&>svg]:size-4 [&>svg]:shrink-0\",\r\n        // Increases the hit area of the button on mobile.\r\n        \"after:absolute after:-inset-2 md:after:hidden\",\r\n        \"group-data-[collapsible=icon]:hidden\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction SidebarGroupContent({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"sidebar-group-content\"\r\n      data-sidebar=\"group-content\"\r\n      className={cn(\"w-full text-sm\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction SidebarMenu({ className, ...props }: React.ComponentProps<\"ul\">) {\r\n  return (\r\n    <ul\r\n      data-slot=\"sidebar-menu\"\r\n      data-sidebar=\"menu\"\r\n      className={cn(\"flex w-full min-w-0 flex-col gap-1\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction SidebarMenuItem({ className, ...props }: React.ComponentProps<\"li\">) {\r\n  return (\r\n    <li\r\n      data-slot=\"sidebar-menu-item\"\r\n      data-sidebar=\"menu-item\"\r\n      className={cn(\"group/menu-item relative\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nconst sidebarMenuButtonVariants = cva(\r\n  \"peer/menu-button flex w-full items-center gap-2 overflow-hidden rounded-md p-2 text-left text-sm outline-hidden ring-sidebar-ring transition-[width,height,padding] hover:bg-sidebar-accent hover:text-sidebar-accent-foreground focus-visible:ring-2 active:bg-sidebar-accent active:text-sidebar-accent-foreground disabled:pointer-events-none disabled:opacity-50 group-has-data-[sidebar=menu-action]/menu-item:pr-8 aria-disabled:pointer-events-none aria-disabled:opacity-50 data-[active=true]:bg-sidebar-accent data-[active=true]:font-medium data-[active=true]:text-sidebar-accent-foreground data-[state=open]:hover:bg-sidebar-accent data-[state=open]:hover:text-sidebar-accent-foreground group-data-[collapsible=icon]:size-8! group-data-[collapsible=icon]:p-2! [&>span:last-child]:truncate [&>svg]:size-4 [&>svg]:shrink-0\",\r\n  {\r\n    variants: {\r\n      variant: {\r\n        default: \"hover:bg-sidebar-accent hover:text-sidebar-accent-foreground\",\r\n        outline:\r\n          \"bg-background shadow-[0_0_0_1px_hsl(var(--sidebar-border))] hover:bg-sidebar-accent hover:text-sidebar-accent-foreground hover:shadow-[0_0_0_1px_hsl(var(--sidebar-accent))]\",\r\n      },\r\n      size: {\r\n        default: \"h-8 text-sm\",\r\n        sm: \"h-7 text-xs\",\r\n        lg: \"h-12 text-sm group-data-[collapsible=icon]:p-0!\",\r\n      },\r\n    },\r\n    defaultVariants: {\r\n      variant: \"default\",\r\n      size: \"default\",\r\n    },\r\n  }\r\n)\r\n\r\nfunction SidebarMenuButton({\r\n  asChild = false,\r\n  isActive = false,\r\n  variant = \"default\",\r\n  size = \"default\",\r\n  tooltip,\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<\"button\"> & {\r\n  asChild?: boolean\r\n  isActive?: boolean\r\n  tooltip?: string | React.ComponentProps<typeof TooltipContent>\r\n} & VariantProps<typeof sidebarMenuButtonVariants>) {\r\n  const Comp = asChild ? Slot : \"button\"\r\n  const { isMobile, state } = useSidebar()\r\n\r\n  const button = (\r\n    <Comp\r\n      data-slot=\"sidebar-menu-button\"\r\n      data-sidebar=\"menu-button\"\r\n      data-size={size}\r\n      data-active={isActive}\r\n      className={cn(sidebarMenuButtonVariants({ variant, size }), className)}\r\n      {...props}\r\n    />\r\n  )\r\n\r\n  if (!tooltip) {\r\n    return button\r\n  }\r\n\r\n  if (typeof tooltip === \"string\") {\r\n    tooltip = {\r\n      children: tooltip,\r\n    }\r\n  }\r\n\r\n  return (\r\n    <Tooltip>\r\n      <TooltipTrigger asChild>{button}</TooltipTrigger>\r\n      <TooltipContent\r\n        side=\"right\"\r\n        align=\"center\"\r\n        hidden={state !== \"collapsed\" || isMobile}\r\n        {...tooltip}\r\n      />\r\n    </Tooltip>\r\n  )\r\n}\r\n\r\nfunction SidebarMenuAction({\r\n  className,\r\n  asChild = false,\r\n  showOnHover = false,\r\n  ...props\r\n}: React.ComponentProps<\"button\"> & {\r\n  asChild?: boolean\r\n  showOnHover?: boolean\r\n}) {\r\n  const Comp = asChild ? Slot : \"button\"\r\n\r\n  return (\r\n    <Comp\r\n      data-slot=\"sidebar-menu-action\"\r\n      data-sidebar=\"menu-action\"\r\n      className={cn(\r\n        \"text-sidebar-foreground ring-sidebar-ring hover:bg-sidebar-accent hover:text-sidebar-accent-foreground peer-hover/menu-button:text-sidebar-accent-foreground absolute top-1.5 right-1 flex aspect-square w-5 items-center justify-center rounded-md p-0 outline-hidden transition-transform focus-visible:ring-2 [&>svg]:size-4 [&>svg]:shrink-0\",\r\n        // Increases the hit area of the button on mobile.\r\n        \"after:absolute after:-inset-2 md:after:hidden\",\r\n        \"peer-data-[size=sm]/menu-button:top-1\",\r\n        \"peer-data-[size=default]/menu-button:top-1.5\",\r\n        \"peer-data-[size=lg]/menu-button:top-2.5\",\r\n        \"group-data-[collapsible=icon]:hidden\",\r\n        showOnHover &&\r\n          \"peer-data-[active=true]/menu-button:text-sidebar-accent-foreground group-focus-within/menu-item:opacity-100 group-hover/menu-item:opacity-100 data-[state=open]:opacity-100 md:opacity-0\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction SidebarMenuBadge({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"sidebar-menu-badge\"\r\n      data-sidebar=\"menu-badge\"\r\n      className={cn(\r\n        \"text-sidebar-foreground pointer-events-none absolute right-1 flex h-5 min-w-5 items-center justify-center rounded-md px-1 text-xs font-medium tabular-nums select-none\",\r\n        \"peer-hover/menu-button:text-sidebar-accent-foreground peer-data-[active=true]/menu-button:text-sidebar-accent-foreground\",\r\n        \"peer-data-[size=sm]/menu-button:top-1\",\r\n        \"peer-data-[size=default]/menu-button:top-1.5\",\r\n        \"peer-data-[size=lg]/menu-button:top-2.5\",\r\n        \"group-data-[collapsible=icon]:hidden\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction SidebarMenuSkeleton({\r\n  className,\r\n  showIcon = false,\r\n  ...props\r\n}: React.ComponentProps<\"div\"> & {\r\n  showIcon?: boolean\r\n}) {\r\n  // Random width between 50 to 90%.\r\n  const width = React.useMemo(() => {\r\n    return `${Math.floor(Math.random() * 40) + 50}%`\r\n  }, [])\r\n\r\n  return (\r\n    <div\r\n      data-slot=\"sidebar-menu-skeleton\"\r\n      data-sidebar=\"menu-skeleton\"\r\n      className={cn(\"flex h-8 items-center gap-2 rounded-md px-2\", className)}\r\n      {...props}\r\n    >\r\n      {showIcon && (\r\n        <Skeleton\r\n          className=\"size-4 rounded-md\"\r\n          data-sidebar=\"menu-skeleton-icon\"\r\n        />\r\n      )}\r\n      <Skeleton\r\n        className=\"h-4 max-w-(--skeleton-width) flex-1\"\r\n        data-sidebar=\"menu-skeleton-text\"\r\n        style={\r\n          {\r\n            \"--skeleton-width\": width,\r\n          } as React.CSSProperties\r\n        }\r\n      />\r\n    </div>\r\n  )\r\n}\r\n\r\nfunction SidebarMenuSub({ className, ...props }: React.ComponentProps<\"ul\">) {\r\n  return (\r\n    <ul\r\n      data-slot=\"sidebar-menu-sub\"\r\n      data-sidebar=\"menu-sub\"\r\n      className={cn(\r\n        \"border-sidebar-border mx-3.5 flex min-w-0 translate-x-px flex-col gap-1 border-l px-2.5 py-0.5\",\r\n        \"group-data-[collapsible=icon]:hidden\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction SidebarMenuSubItem({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<\"li\">) {\r\n  return (\r\n    <li\r\n      data-slot=\"sidebar-menu-sub-item\"\r\n      data-sidebar=\"menu-sub-item\"\r\n      className={cn(\"group/menu-sub-item relative\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction SidebarMenuSubButton({\r\n  asChild = false,\r\n  size = \"md\",\r\n  isActive = false,\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<\"a\"> & {\r\n  asChild?: boolean\r\n  size?: \"sm\" | \"md\"\r\n  isActive?: boolean\r\n}) {\r\n  const Comp = asChild ? Slot : \"a\"\r\n\r\n  return (\r\n    <Comp\r\n      data-slot=\"sidebar-menu-sub-button\"\r\n      data-sidebar=\"menu-sub-button\"\r\n      data-size={size}\r\n      data-active={isActive}\r\n      className={cn(\r\n        \"text-sidebar-foreground ring-sidebar-ring hover:bg-sidebar-accent hover:text-sidebar-accent-foreground active:bg-sidebar-accent active:text-sidebar-accent-foreground [&>svg]:text-sidebar-accent-foreground flex h-7 min-w-0 -translate-x-px items-center gap-2 overflow-hidden rounded-md px-2 outline-hidden focus-visible:ring-2 disabled:pointer-events-none disabled:opacity-50 aria-disabled:pointer-events-none aria-disabled:opacity-50 [&>span:last-child]:truncate [&>svg]:size-4 [&>svg]:shrink-0\",\r\n        \"data-[active=true]:bg-sidebar-accent data-[active=true]:text-sidebar-accent-foreground\",\r\n        size === \"sm\" && \"text-xs\",\r\n        size === \"md\" && \"text-sm\",\r\n        \"group-data-[collapsible=icon]:hidden\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport {\r\n  Sidebar,\r\n  SidebarContent,\r\n  SidebarFooter,\r\n  SidebarGroup,\r\n  SidebarGroupAction,\r\n  SidebarGroupContent,\r\n  SidebarGroupLabel,\r\n  SidebarHeader,\r\n  SidebarInput,\r\n  SidebarInset,\r\n  SidebarMenu,\r\n  SidebarMenuAction,\r\n  SidebarMenuBadge,\r\n  SidebarMenuButton,\r\n  SidebarMenuItem,\r\n  SidebarMenuSkeleton,\r\n  SidebarMenuSub,\r\n  SidebarMenuSubButton,\r\n  SidebarMenuSubItem,\r\n  SidebarProvider,\r\n  SidebarRail,\r\n  SidebarSeparator,\r\n  SidebarTrigger,\r\n  useSidebar,\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;AAEA;AACA;AACA;AACA;AAEA;AACA;AAAA;AACA;AACA;AACA;AACA;AAOA;AACA;AApBA;;;;;;;;;;;;;;AA2BA,MAAM,sBAAsB;AAC5B,MAAM,yBAAyB,KAAK,KAAK,KAAK;AAC9C,MAAM,gBAAgB;AACtB,MAAM,uBAAuB;AAC7B,MAAM,qBAAqB;AAC3B,MAAM,4BAA4B;AAYlC,MAAM,+BAAiB,CAAA,GAAA,8SAAA,CAAA,gBAAmB,AAAD,EAA8B;AAEvE,SAAS;IACP,MAAM,UAAU,CAAA,GAAA,8SAAA,CAAA,aAAgB,AAAD,EAAE;IACjC,IAAI,CAAC,SAAS;QACZ,MAAM,IAAI,MAAM;IAClB;IAEA,OAAO;AACT;AAEA,SAAS,gBAAgB,EACvB,cAAc,IAAI,EAClB,MAAM,QAAQ,EACd,cAAc,WAAW,EACzB,SAAS,EACT,KAAK,EACL,QAAQ,EACR,GAAG,OAKJ;IACC,MAAM,WAAW,CAAA,GAAA,sHAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,8SAAA,CAAA,WAAc,AAAD,EAAE;IAEnD,6CAA6C;IAC7C,0EAA0E;IAC1E,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,8SAAA,CAAA,WAAc,AAAD,EAAE;IACzC,MAAM,OAAO,YAAY;IACzB,MAAM,UAAU,CAAA,GAAA,8SAAA,CAAA,cAAiB,AAAD,EAC9B,CAAC;QACC,MAAM,YAAY,OAAO,UAAU,aAAa,MAAM,QAAQ;QAC9D,IAAI,aAAa;YACf,YAAY;QACd,OAAO;YACL,SAAS;QACX;QAEA,kDAAkD;QAClD,SAAS,MAAM,GAAG,GAAG,oBAAoB,CAAC,EAAE,UAAU,kBAAkB,EAAE,wBAAwB;IACpG,GACA;QAAC;QAAa;KAAK;IAGrB,gCAAgC;IAChC,MAAM,gBAAgB,CAAA,GAAA,8SAAA,CAAA,cAAiB,AAAD,EAAE;QACtC,OAAO,WAAW,cAAc,CAAC,OAAS,CAAC,QAAQ,QAAQ,CAAC,OAAS,CAAC;IACxE,GAAG;QAAC;QAAU;QAAS;KAAc;IAErC,kDAAkD;IAClD,CAAA,GAAA,8SAAA,CAAA,YAAe,AAAD,EAAE;QACd,MAAM,gBAAgB,CAAC;YACrB,IACE,MAAM,GAAG,KAAK,6BACd,CAAC,MAAM,OAAO,IAAI,MAAM,OAAO,GAC/B;gBACA,MAAM,cAAc;gBACpB;YACF;QACF;QAEA,OAAO,gBAAgB,CAAC,WAAW;QACnC,OAAO,IAAM,OAAO,mBAAmB,CAAC,WAAW;IACrD,GAAG;QAAC;KAAc;IAElB,yEAAyE;IACzE,mEAAmE;IACnE,MAAM,QAAQ,OAAO,aAAa;IAElC,MAAM,eAAe,CAAA,GAAA,8SAAA,CAAA,UAAa,AAAD,EAC/B,IAAM,CAAC;YACL;YACA;YACA;YACA;YACA;YACA;YACA;QACF,CAAC,GACD;QAAC;QAAO;QAAM;QAAS;QAAU;QAAY;QAAe;KAAc;IAG5E,qBACE,uVAAC,eAAe,QAAQ;QAAC,OAAO;kBAC9B,cAAA,uVAAC,4HAAA,CAAA,kBAAe;YAAC,eAAe;sBAC9B,cAAA,uVAAC;gBACC,aAAU;gBACV,OACE;oBACE,mBAAmB;oBACnB,wBAAwB;oBACxB,GAAG,KAAK;gBACV;gBAEF,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EACV,mFACA;gBAED,GAAG,KAAK;0BAER;;;;;;;;;;;;;;;;AAKX;AAEA,SAAS,QAAQ,EACf,OAAO,MAAM,EACb,UAAU,SAAS,EACnB,cAAc,WAAW,EACzB,SAAS,EACT,QAAQ,EACR,GAAG,OAKJ;IACC,MAAM,EAAE,QAAQ,EAAE,KAAK,EAAE,UAAU,EAAE,aAAa,EAAE,GAAG;IAEvD,IAAI,gBAAgB,QAAQ;QAC1B,qBACE,uVAAC;YACC,aAAU;YACV,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EACV,+EACA;YAED,GAAG,KAAK;sBAER;;;;;;IAGP;IAEA,IAAI,UAAU;QACZ,qBACE,uVAAC,0HAAA,CAAA,QAAK;YAAC,MAAM;YAAY,cAAc;YAAgB,GAAG,KAAK;sBAC7D,cAAA,uVAAC,0HAAA,CAAA,eAAY;gBACX,gBAAa;gBACb,aAAU;gBACV,eAAY;gBACZ,WAAU;gBACV,OACE;oBACE,mBAAmB;gBACrB;gBAEF,MAAM;;kCAEN,uVAAC,0HAAA,CAAA,cAAW;wBAAC,WAAU;;0CACrB,uVAAC,0HAAA,CAAA,aAAU;0CAAC;;;;;;0CACZ,uVAAC,0HAAA,CAAA,mBAAgB;0CAAC;;;;;;;;;;;;kCAEpB,uVAAC;wBAAI,WAAU;kCAA+B;;;;;;;;;;;;;;;;;IAItD;IAEA,qBACE,uVAAC;QACC,WAAU;QACV,cAAY;QACZ,oBAAkB,UAAU,cAAc,cAAc;QACxD,gBAAc;QACd,aAAW;QACX,aAAU;;0BAGV,uVAAC;gBACC,aAAU;gBACV,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EACV,2FACA,0CACA,sCACA,YAAY,cAAc,YAAY,UAClC,qFACA;;;;;;0BAGR,uVAAC;gBACC,aAAU;gBACV,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EACV,wHACA,SAAS,SACL,mFACA,oFACJ,sDAAsD;gBACtD,YAAY,cAAc,YAAY,UAClC,6FACA,2HACJ;gBAED,GAAG,KAAK;0BAET,cAAA,uVAAC;oBACC,gBAAa;oBACb,aAAU;oBACV,WAAU;8BAET;;;;;;;;;;;;;;;;;AAKX;AAEA,SAAS,eAAe,EACtB,SAAS,EACT,OAAO,EACP,GAAG,OACiC;IACpC,MAAM,EAAE,aAAa,EAAE,GAAG;IAE1B,qBACE,uVAAC,2HAAA,CAAA,SAAM;QACL,gBAAa;QACb,aAAU;QACV,SAAQ;QACR,MAAK;QACL,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EAAE,UAAU;QACxB,SAAS,CAAC;YACR,UAAU;YACV;QACF;QACC,GAAG,KAAK;;0BAET,uVAAC,wSAAA,CAAA,gBAAa;;;;;0BACd,uVAAC;gBAAK,WAAU;0BAAU;;;;;;;;;;;;AAGhC;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAuC;IAC1E,MAAM,EAAE,aAAa,EAAE,GAAG;IAE1B,qBACE,uVAAC;QACC,gBAAa;QACb,aAAU;QACV,cAAW;QACX,UAAU,CAAC;QACX,SAAS;QACT,OAAM;QACN,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EACV,mPACA,4EACA,0HACA,2JACA,6DACA,6DACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,aAAa,EAAE,SAAS,EAAE,GAAG,OAAqC;IACzE,qBACE,uVAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EACV,sDACA,mNACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,aAAa,EACpB,SAAS,EACT,GAAG,OACgC;IACnC,qBACE,uVAAC,0HAAA,CAAA,QAAK;QACJ,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EAAE,wCAAwC;QACrD,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,cAAc,EAAE,SAAS,EAAE,GAAG,OAAoC;IACzE,qBACE,uVAAC;QACC,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EAAE,2BAA2B;QACxC,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,cAAc,EAAE,SAAS,EAAE,GAAG,OAAoC;IACzE,qBACE,uVAAC;QACC,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EAAE,2BAA2B;QACxC,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,iBAAiB,EACxB,SAAS,EACT,GAAG,OACoC;IACvC,qBACE,uVAAC,8HAAA,CAAA,YAAS;QACR,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,eAAe,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC1E,qBACE,uVAAC;QACC,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EACV,kGACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,aAAa,EAAE,SAAS,EAAE,GAAG,OAAoC;IACxE,qBACE,uVAAC;QACC,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EAAE,6CAA6C;QAC1D,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,kBAAkB,EACzB,SAAS,EACT,UAAU,KAAK,EACf,GAAG,OACiD;IACpD,MAAM,OAAO,UAAU,oSAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,uVAAC;QACC,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EACV,4OACA,+EACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,mBAAmB,EAC1B,SAAS,EACT,UAAU,KAAK,EACf,GAAG,OACoD;IACvD,MAAM,OAAO,UAAU,oSAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,uVAAC;QACC,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EACV,8RACA,kDAAkD;QAClD,iDACA,wCACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,oBAAoB,EAC3B,SAAS,EACT,GAAG,OACyB;IAC5B,qBACE,uVAAC;QACC,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EAAE,kBAAkB;QAC/B,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAmC;IACtE,qBACE,uVAAC;QACC,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QACnD,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAmC;IAC1E,qBACE,uVAAC;QACC,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EAAE,4BAA4B;QACzC,GAAG,KAAK;;;;;;AAGf;AAEA,MAAM,4BAA4B,CAAA,GAAA,2OAAA,CAAA,MAAG,AAAD,EAClC,qzBACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,SACE;QACJ;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;QACN;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,SAAS,kBAAkB,EACzB,UAAU,KAAK,EACf,WAAW,KAAK,EAChB,UAAU,SAAS,EACnB,OAAO,SAAS,EAChB,OAAO,EACP,SAAS,EACT,GAAG,OAK6C;IAChD,MAAM,OAAO,UAAU,oSAAA,CAAA,OAAI,GAAG;IAC9B,MAAM,EAAE,QAAQ,EAAE,KAAK,EAAE,GAAG;IAE5B,MAAM,uBACJ,uVAAC;QACC,aAAU;QACV,gBAAa;QACb,aAAW;QACX,eAAa;QACb,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EAAE,0BAA0B;YAAE;YAAS;QAAK,IAAI;QAC3D,GAAG,KAAK;;;;;;IAIb,IAAI,CAAC,SAAS;QACZ,OAAO;IACT;IAEA,IAAI,OAAO,YAAY,UAAU;QAC/B,UAAU;YACR,UAAU;QACZ;IACF;IAEA,qBACE,uVAAC,4HAAA,CAAA,UAAO;;0BACN,uVAAC,4HAAA,CAAA,iBAAc;gBAAC,OAAO;0BAAE;;;;;;0BACzB,uVAAC,4HAAA,CAAA,iBAAc;gBACb,MAAK;gBACL,OAAM;gBACN,QAAQ,UAAU,eAAe;gBAChC,GAAG,OAAO;;;;;;;;;;;;AAInB;AAEA,SAAS,kBAAkB,EACzB,SAAS,EACT,UAAU,KAAK,EACf,cAAc,KAAK,EACnB,GAAG,OAIJ;IACC,MAAM,OAAO,UAAU,oSAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,uVAAC;QACC,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EACV,oVACA,kDAAkD;QAClD,iDACA,yCACA,gDACA,2CACA,wCACA,eACE,4LACF;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,iBAAiB,EACxB,SAAS,EACT,GAAG,OACyB;IAC5B,qBACE,uVAAC;QACC,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EACV,0KACA,4HACA,yCACA,gDACA,2CACA,wCACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,oBAAoB,EAC3B,SAAS,EACT,WAAW,KAAK,EAChB,GAAG,OAGJ;IACC,kCAAkC;IAClC,MAAM,QAAQ,CAAA,GAAA,8SAAA,CAAA,UAAa,AAAD,EAAE;QAC1B,OAAO,GAAG,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,MAAM,GAAG,CAAC,CAAC;IAClD,GAAG,EAAE;IAEL,qBACE,uVAAC;QACC,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EAAE,+CAA+C;QAC5D,GAAG,KAAK;;YAER,0BACC,uVAAC,6HAAA,CAAA,WAAQ;gBACP,WAAU;gBACV,gBAAa;;;;;;0BAGjB,uVAAC,6HAAA,CAAA,WAAQ;gBACP,WAAU;gBACV,gBAAa;gBACb,OACE;oBACE,oBAAoB;gBACtB;;;;;;;;;;;;AAKV;AAEA,SAAS,eAAe,EAAE,SAAS,EAAE,GAAG,OAAmC;IACzE,qBACE,uVAAC;QACC,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EACV,kGACA,wCACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,mBAAmB,EAC1B,SAAS,EACT,GAAG,OACwB;IAC3B,qBACE,uVAAC;QACC,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EAAE,gCAAgC;QAC7C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,qBAAqB,EAC5B,UAAU,KAAK,EACf,OAAO,IAAI,EACX,WAAW,KAAK,EAChB,SAAS,EACT,GAAG,OAKJ;IACC,MAAM,OAAO,UAAU,oSAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,uVAAC;QACC,aAAU;QACV,gBAAa;QACb,aAAW;QACX,eAAa;QACb,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EACV,ifACA,0FACA,SAAS,QAAQ,WACjB,SAAS,QAAQ,WACjB,wCACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 1083, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/bloomgoo/gold-exchange-platform/frontend/components/nav-main.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport Link from \"next/link\"\r\n\r\nimport {\r\n  SidebarGroup,\r\n  SidebarGroupContent,\r\n  SidebarMenu,\r\n  SidebarMenuButton,\r\n  SidebarMenuItem,\r\n} from \"@/components/ui/sidebar\"\r\n\r\nexport function NavMain({\r\n  items,\r\n}: {\r\n  items: any[]\r\n}) {\r\n\r\n  return (\r\n    <SidebarGroup>\r\n      <SidebarGroupContent className=\"flex flex-col gap-2\">\r\n        <SidebarMenu>\r\n          {items.map((item, index) => {\r\n            if ('type' in item && item.type === 'separator') {\r\n              return (\r\n                <SidebarMenuItem key={`separator-${index}`} className=\"mt-2 mb-1\">\r\n                  {/* Separator khi expanded - hiển thị text đầy đủ */}\r\n                  <div className=\"group-data-[collapsible=icon]:hidden\">\r\n                    <p className=\"px-2 text-xs font-semibold text-muted-foreground uppercase tracking-wider\">\r\n                      {item.title}\r\n                    </p>\r\n                  </div>\r\n\r\n                  {/* <PERSON>arator khi collapsed - hiển thị divider với tooltip */}\r\n                  <div className=\"hidden group-data-[collapsible=icon]:block\">\r\n                    <SidebarMenuButton\r\n                      tooltip={{\r\n                        children: item.title,\r\n                        side: \"right\",\r\n                        align: \"center\"\r\n                      }}\r\n                      className=\"h-6 justify-center cursor-default hover:bg-transparent focus:bg-transparent active:bg-transparent pointer-events-auto\"\r\n                      onClick={(e) => e.preventDefault()}\r\n                    >\r\n                      <div className=\"w-4 h-px bg-sidebar-border pointer-events-none\" />\r\n                    </SidebarMenuButton>\r\n                  </div>\r\n                </SidebarMenuItem>\r\n              );\r\n            }\r\n\r\n            // Tạo key unique bằng cách kết hợp key có sẵn, url và index\r\n            const uniqueKey = item.key || item.url || `${item.title}-${index}`;\r\n\r\n            return (\r\n              <SidebarMenuItem key={uniqueKey}>\r\n                <SidebarMenuButton asChild tooltip={item.title}>\r\n                  <Link href={item.url}>\r\n                    {item.icon && <item.icon />}\r\n                    <span>{item.title}</span>\r\n                  </Link>\r\n                </SidebarMenuButton>\r\n              </SidebarMenuItem>\r\n            );\r\n          })}\r\n        </SidebarMenu>\r\n      </SidebarGroupContent>\r\n    </SidebarGroup>\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AAEA;AAJA;;;;AAYO,SAAS,QAAQ,EACtB,KAAK,EAGN;IAEC,qBACE,uVAAC,4HAAA,CAAA,eAAY;kBACX,cAAA,uVAAC,4HAAA,CAAA,sBAAmB;YAAC,WAAU;sBAC7B,cAAA,uVAAC,4HAAA,CAAA,cAAW;0BACT,MAAM,GAAG,CAAC,CAAC,MAAM;oBAChB,IAAI,UAAU,QAAQ,KAAK,IAAI,KAAK,aAAa;wBAC/C,qBACE,uVAAC,4HAAA,CAAA,kBAAe;4BAA4B,WAAU;;8CAEpD,uVAAC;oCAAI,WAAU;8CACb,cAAA,uVAAC;wCAAE,WAAU;kDACV,KAAK,KAAK;;;;;;;;;;;8CAKf,uVAAC;oCAAI,WAAU;8CACb,cAAA,uVAAC,4HAAA,CAAA,oBAAiB;wCAChB,SAAS;4CACP,UAAU,KAAK,KAAK;4CACpB,MAAM;4CACN,OAAO;wCACT;wCACA,WAAU;wCACV,SAAS,CAAC,IAAM,EAAE,cAAc;kDAEhC,cAAA,uVAAC;4CAAI,WAAU;;;;;;;;;;;;;;;;;2BAnBC,CAAC,UAAU,EAAE,OAAO;;;;;oBAwB9C;oBAEA,4DAA4D;oBAC5D,MAAM,YAAY,KAAK,GAAG,IAAI,KAAK,GAAG,IAAI,GAAG,KAAK,KAAK,CAAC,CAAC,EAAE,OAAO;oBAElE,qBACE,uVAAC,4HAAA,CAAA,kBAAe;kCACd,cAAA,uVAAC,4HAAA,CAAA,oBAAiB;4BAAC,OAAO;4BAAC,SAAS,KAAK,KAAK;sCAC5C,cAAA,uVAAC,qQAAA,CAAA,UAAI;gCAAC,MAAM,KAAK,GAAG;;oCACjB,KAAK,IAAI,kBAAI,uVAAC,KAAK,IAAI;;;;;kDACxB,uVAAC;kDAAM,KAAK,KAAK;;;;;;;;;;;;;;;;;uBAJD;;;;;gBAS1B;;;;;;;;;;;;;;;;AAKV", "debugId": null}}, {"offset": {"line": 1212, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/bloomgoo/gold-exchange-platform/frontend/components/nav-secondary.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport { type Icon } from \"@tabler/icons-react\"\r\n\r\nimport {\r\n  SidebarGroup,\r\n  SidebarGroupContent,\r\n  SidebarMenu,\r\n  SidebarMenuButton,\r\n  SidebarMenuItem,\r\n} from \"@/components/ui/sidebar\"\r\n\r\nexport function NavSecondary({\r\n  items,\r\n  ...props\r\n}: {\r\n  items: {\r\n    title: string\r\n    url: string\r\n    icon: Icon\r\n  }[]\r\n} & React.ComponentPropsWithoutRef<typeof SidebarGroup>) {\r\n  return (\r\n    <SidebarGroup {...props}>\r\n      <SidebarGroupContent>\r\n        <SidebarMenu>\r\n          {items.map((item) => (\r\n            <SidebarMenuItem key={item.title}>\r\n              <SidebarMenuButton asChild>\r\n                <a href={item.url}>\r\n                  <item.icon />\r\n                  <span>{item.title}</span>\r\n                </a>\r\n              </SidebarMenuButton>\r\n            </SidebarMenuItem>\r\n          ))}\r\n        </SidebarMenu>\r\n      </SidebarGroupContent>\r\n    </SidebarGroup>\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;AAKA;AALA;;;AAaO,SAAS,aAAa,EAC3B,KAAK,EACL,GAAG,OAOkD;IACrD,qBACE,uVAAC,4HAAA,CAAA,eAAY;QAAE,GAAG,KAAK;kBACrB,cAAA,uVAAC,4HAAA,CAAA,sBAAmB;sBAClB,cAAA,uVAAC,4HAAA,CAAA,cAAW;0BACT,MAAM,GAAG,CAAC,CAAC,qBACV,uVAAC,4HAAA,CAAA,kBAAe;kCACd,cAAA,uVAAC,4HAAA,CAAA,oBAAiB;4BAAC,OAAO;sCACxB,cAAA,uVAAC;gCAAE,MAAM,KAAK,GAAG;;kDACf,uVAAC,KAAK,IAAI;;;;;kDACV,uVAAC;kDAAM,KAAK,KAAK;;;;;;;;;;;;;;;;;uBAJD,KAAK,KAAK;;;;;;;;;;;;;;;;;;;;AAa5C", "debugId": null}}, {"offset": {"line": 1281, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/bloomgoo/gold-exchange-platform/frontend/components/ui/avatar.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as AvatarPrimitive from \"@radix-ui/react-avatar\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction Avatar({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof AvatarPrimitive.Root>) {\r\n  return (\r\n    <AvatarPrimitive.Root\r\n      data-slot=\"avatar\"\r\n      className={cn(\r\n        \"relative flex size-8 shrink-0 overflow-hidden rounded-full\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction AvatarImage({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof AvatarPrimitive.Image>) {\r\n  return (\r\n    <AvatarPrimitive.Image\r\n      data-slot=\"avatar-image\"\r\n      className={cn(\"aspect-square size-full\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction AvatarFallback({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof AvatarPrimitive.Fallback>) {\r\n  return (\r\n    <AvatarPrimitive.Fallback\r\n      data-slot=\"avatar-fallback\"\r\n      className={cn(\r\n        \"bg-muted flex size-full items-center justify-center rounded-full\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport { Avatar, AvatarImage, AvatarFallback }\r\n"], "names": [], "mappings": ";;;;;;AAGA;AAEA;AAAA;AALA;;;;AAOA,SAAS,OAAO,EACd,SAAS,EACT,GAAG,OAC+C;IAClD,qBACE,uVAAC,+QAAA,CAAA,OAAoB;QACnB,aAAU;QACV,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EACV,8DACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,uVAAC,+QAAA,CAAA,QAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EAAE,2BAA2B;QACxC,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,eAAe,EACtB,SAAS,EACT,GAAG,OACmD;IACtD,qBACE,uVAAC,+QAAA,CAAA,WAAwB;QACvB,aAAU;QACV,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EACV,oEACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 1334, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/bloomgoo/gold-exchange-platform/frontend/components/ui/dropdown-menu.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as DropdownMenuPrimitive from \"@radix-ui/react-dropdown-menu\"\r\nimport { CheckIcon, ChevronRightIcon, CircleIcon } from \"lucide-react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction DropdownMenu({\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Root>) {\r\n  return <DropdownMenuPrimitive.Root data-slot=\"dropdown-menu\" {...props} />\r\n}\r\n\r\nfunction DropdownMenuPortal({\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Portal>) {\r\n  return (\r\n    <DropdownMenuPrimitive.Portal data-slot=\"dropdown-menu-portal\" {...props} />\r\n  )\r\n}\r\n\r\nfunction DropdownMenuTrigger({\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Trigger>) {\r\n  return (\r\n    <DropdownMenuPrimitive.Trigger\r\n      data-slot=\"dropdown-menu-trigger\"\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction DropdownMenuContent({\r\n  className,\r\n  sideOffset = 4,\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Content>) {\r\n  return (\r\n    <DropdownMenuPrimitive.Portal>\r\n      <DropdownMenuPrimitive.Content\r\n        data-slot=\"dropdown-menu-content\"\r\n        sideOffset={sideOffset}\r\n        className={cn(\r\n          \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 max-h-(--radix-dropdown-menu-content-available-height) min-w-[8rem] origin-(--radix-dropdown-menu-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border p-1 shadow-md\",\r\n          className\r\n        )}\r\n        {...props}\r\n      />\r\n    </DropdownMenuPrimitive.Portal>\r\n  )\r\n}\r\n\r\nfunction DropdownMenuGroup({\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Group>) {\r\n  return (\r\n    <DropdownMenuPrimitive.Group data-slot=\"dropdown-menu-group\" {...props} />\r\n  )\r\n}\r\n\r\nfunction DropdownMenuItem({\r\n  className,\r\n  inset,\r\n  variant = \"default\",\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Item> & {\r\n  inset?: boolean\r\n  variant?: \"default\" | \"destructive\"\r\n}) {\r\n  return (\r\n    <DropdownMenuPrimitive.Item\r\n      data-slot=\"dropdown-menu-item\"\r\n      data-inset={inset}\r\n      data-variant={variant}\r\n      className={cn(\r\n        \"focus:bg-accent focus:text-accent-foreground data-[variant=destructive]:text-destructive data-[variant=destructive]:focus:bg-destructive/10 dark:data-[variant=destructive]:focus:bg-destructive/20 data-[variant=destructive]:focus:text-destructive data-[variant=destructive]:*:[svg]:!text-destructive [&_svg:not([class*='text-'])]:text-muted-foreground relative flex cursor-default items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 data-[inset]:pl-8 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction DropdownMenuCheckboxItem({\r\n  className,\r\n  children,\r\n  checked,\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.CheckboxItem>) {\r\n  return (\r\n    <DropdownMenuPrimitive.CheckboxItem\r\n      data-slot=\"dropdown-menu-checkbox-item\"\r\n      className={cn(\r\n        \"focus:bg-accent focus:text-accent-foreground relative flex cursor-default items-center gap-2 rounded-sm py-1.5 pr-2 pl-8 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\r\n        className\r\n      )}\r\n      checked={checked}\r\n      {...props}\r\n    >\r\n      <span className=\"pointer-events-none absolute left-2 flex size-3.5 items-center justify-center\">\r\n        <DropdownMenuPrimitive.ItemIndicator>\r\n          <CheckIcon className=\"size-4\" />\r\n        </DropdownMenuPrimitive.ItemIndicator>\r\n      </span>\r\n      {children}\r\n    </DropdownMenuPrimitive.CheckboxItem>\r\n  )\r\n}\r\n\r\nfunction DropdownMenuRadioGroup({\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.RadioGroup>) {\r\n  return (\r\n    <DropdownMenuPrimitive.RadioGroup\r\n      data-slot=\"dropdown-menu-radio-group\"\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction DropdownMenuRadioItem({\r\n  className,\r\n  children,\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.RadioItem>) {\r\n  return (\r\n    <DropdownMenuPrimitive.RadioItem\r\n      data-slot=\"dropdown-menu-radio-item\"\r\n      className={cn(\r\n        \"focus:bg-accent focus:text-accent-foreground relative flex cursor-default items-center gap-2 rounded-sm py-1.5 pr-2 pl-8 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\r\n        className\r\n      )}\r\n      {...props}\r\n    >\r\n      <span className=\"pointer-events-none absolute left-2 flex size-3.5 items-center justify-center\">\r\n        <DropdownMenuPrimitive.ItemIndicator>\r\n          <CircleIcon className=\"size-2 fill-current\" />\r\n        </DropdownMenuPrimitive.ItemIndicator>\r\n      </span>\r\n      {children}\r\n    </DropdownMenuPrimitive.RadioItem>\r\n  )\r\n}\r\n\r\nfunction DropdownMenuLabel({\r\n  className,\r\n  inset,\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Label> & {\r\n  inset?: boolean\r\n}) {\r\n  return (\r\n    <DropdownMenuPrimitive.Label\r\n      data-slot=\"dropdown-menu-label\"\r\n      data-inset={inset}\r\n      className={cn(\r\n        \"px-2 py-1.5 text-sm font-medium data-[inset]:pl-8\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction DropdownMenuSeparator({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Separator>) {\r\n  return (\r\n    <DropdownMenuPrimitive.Separator\r\n      data-slot=\"dropdown-menu-separator\"\r\n      className={cn(\"bg-border -mx-1 my-1 h-px\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction DropdownMenuShortcut({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<\"span\">) {\r\n  return (\r\n    <span\r\n      data-slot=\"dropdown-menu-shortcut\"\r\n      className={cn(\r\n        \"text-muted-foreground ml-auto text-xs tracking-widest\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction DropdownMenuSub({\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Sub>) {\r\n  return <DropdownMenuPrimitive.Sub data-slot=\"dropdown-menu-sub\" {...props} />\r\n}\r\n\r\nfunction DropdownMenuSubTrigger({\r\n  className,\r\n  inset,\r\n  children,\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.SubTrigger> & {\r\n  inset?: boolean\r\n}) {\r\n  return (\r\n    <DropdownMenuPrimitive.SubTrigger\r\n      data-slot=\"dropdown-menu-sub-trigger\"\r\n      data-inset={inset}\r\n      className={cn(\r\n        \"focus:bg-accent focus:text-accent-foreground data-[state=open]:bg-accent data-[state=open]:text-accent-foreground flex cursor-default items-center rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[inset]:pl-8\",\r\n        className\r\n      )}\r\n      {...props}\r\n    >\r\n      {children}\r\n      <ChevronRightIcon className=\"ml-auto size-4\" />\r\n    </DropdownMenuPrimitive.SubTrigger>\r\n  )\r\n}\r\n\r\nfunction DropdownMenuSubContent({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.SubContent>) {\r\n  return (\r\n    <DropdownMenuPrimitive.SubContent\r\n      data-slot=\"dropdown-menu-sub-content\"\r\n      className={cn(\r\n        \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 min-w-[8rem] origin-(--radix-dropdown-menu-content-transform-origin) overflow-hidden rounded-md border p-1 shadow-lg\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport {\r\n  DropdownMenu,\r\n  DropdownMenuPortal,\r\n  DropdownMenuTrigger,\r\n  DropdownMenuContent,\r\n  DropdownMenuGroup,\r\n  DropdownMenuLabel,\r\n  DropdownMenuItem,\r\n  DropdownMenuCheckboxItem,\r\n  DropdownMenuRadioGroup,\r\n  DropdownMenuRadioItem,\r\n  DropdownMenuSeparator,\r\n  DropdownMenuShortcut,\r\n  DropdownMenuSub,\r\n  DropdownMenuSubTrigger,\r\n  DropdownMenuSubContent,\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;AAGA;AACA;AAAA;AAAA;AAEA;AAAA;AANA;;;;;AAQA,SAAS,aAAa,EACpB,GAAG,OACqD;IACxD,qBAAO,uVAAC,mRAAA,CAAA,OAA0B;QAAC,aAAU;QAAiB,GAAG,KAAK;;;;;;AACxE;AAEA,SAAS,mBAAmB,EAC1B,GAAG,OACuD;IAC1D,qBACE,uVAAC,mRAAA,CAAA,SAA4B;QAAC,aAAU;QAAwB,GAAG,KAAK;;;;;;AAE5E;AAEA,SAAS,oBAAoB,EAC3B,GAAG,OACwD;IAC3D,qBACE,uVAAC,mRAAA,CAAA,UAA6B;QAC5B,aAAU;QACT,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,oBAAoB,EAC3B,SAAS,EACT,aAAa,CAAC,EACd,GAAG,OACwD;IAC3D,qBACE,uVAAC,mRAAA,CAAA,SAA4B;kBAC3B,cAAA,uVAAC,mRAAA,CAAA,UAA6B;YAC5B,aAAU;YACV,YAAY;YACZ,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EACV,0jBACA;YAED,GAAG,KAAK;;;;;;;;;;;AAIjB;AAEA,SAAS,kBAAkB,EACzB,GAAG,OACsD;IACzD,qBACE,uVAAC,mRAAA,CAAA,QAA2B;QAAC,aAAU;QAAuB,GAAG,KAAK;;;;;;AAE1E;AAEA,SAAS,iBAAiB,EACxB,SAAS,EACT,KAAK,EACL,UAAU,SAAS,EACnB,GAAG,OAIJ;IACC,qBACE,uVAAC,mRAAA,CAAA,OAA0B;QACzB,aAAU;QACV,cAAY;QACZ,gBAAc;QACd,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EACV,+mBACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,yBAAyB,EAChC,SAAS,EACT,QAAQ,EACR,OAAO,EACP,GAAG,OAC6D;IAChE,qBACE,uVAAC,mRAAA,CAAA,eAAkC;QACjC,aAAU;QACV,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EACV,gTACA;QAEF,SAAS;QACR,GAAG,KAAK;;0BAET,uVAAC;gBAAK,WAAU;0BACd,cAAA,uVAAC,mRAAA,CAAA,gBAAmC;8BAClC,cAAA,uVAAC,4RAAA,CAAA,YAAS;wBAAC,WAAU;;;;;;;;;;;;;;;;YAGxB;;;;;;;AAGP;AAEA,SAAS,uBAAuB,EAC9B,GAAG,OAC2D;IAC9D,qBACE,uVAAC,mRAAA,CAAA,aAAgC;QAC/B,aAAU;QACT,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,sBAAsB,EAC7B,SAAS,EACT,QAAQ,EACR,GAAG,OAC0D;IAC7D,qBACE,uVAAC,mRAAA,CAAA,YAA+B;QAC9B,aAAU;QACV,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EACV,gTACA;QAED,GAAG,KAAK;;0BAET,uVAAC;gBAAK,WAAU;0BACd,cAAA,uVAAC,mRAAA,CAAA,gBAAmC;8BAClC,cAAA,uVAAC,8RAAA,CAAA,aAAU;wBAAC,WAAU;;;;;;;;;;;;;;;;YAGzB;;;;;;;AAGP;AAEA,SAAS,kBAAkB,EACzB,SAAS,EACT,KAAK,EACL,GAAG,OAGJ;IACC,qBACE,uVAAC,mRAAA,CAAA,QAA2B;QAC1B,aAAU;QACV,cAAY;QACZ,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EACV,qDACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,sBAAsB,EAC7B,SAAS,EACT,GAAG,OAC0D;IAC7D,qBACE,uVAAC,mRAAA,CAAA,YAA+B;QAC9B,aAAU;QACV,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EAAE,6BAA6B;QAC1C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,qBAAqB,EAC5B,SAAS,EACT,GAAG,OAC0B;IAC7B,qBACE,uVAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EACV,yDACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,gBAAgB,EACvB,GAAG,OACoD;IACvD,qBAAO,uVAAC,mRAAA,CAAA,MAAyB;QAAC,aAAU;QAAqB,GAAG,KAAK;;;;;;AAC3E;AAEA,SAAS,uBAAuB,EAC9B,SAAS,EACT,KAAK,EACL,QAAQ,EACR,GAAG,OAGJ;IACC,qBACE,uVAAC,mRAAA,CAAA,aAAgC;QAC/B,aAAU;QACV,cAAY;QACZ,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EACV,kOACA;QAED,GAAG,KAAK;;YAER;0BACD,uVAAC,8SAAA,CAAA,mBAAgB;gBAAC,WAAU;;;;;;;;;;;;AAGlC;AAEA,SAAS,uBAAuB,EAC9B,SAAS,EACT,GAAG,OAC2D;IAC9D,qBACE,uVAAC,mRAAA,CAAA,aAAgC;QAC/B,aAAU;QACV,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EACV,ifACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 1597, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/bloomgoo/gold-exchange-platform/frontend/components/nav-user.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport {\r\n  IconCreditCard,\r\n  IconDotsVertical,\r\n  IconLogout,\r\n  IconNotification,\r\n  IconUserCircle,\r\n} from \"@tabler/icons-react\"\r\n\r\nimport {\r\n  Avatar,\r\n  AvatarFallback,\r\n  AvatarImage,\r\n} from \"@/components/ui/avatar\"\r\nimport {\r\n  DropdownMenu,\r\n  DropdownMenuContent,\r\n  DropdownMenuGroup,\r\n  DropdownMenuItem,\r\n  DropdownMenuLabel,\r\n  DropdownMenuSeparator,\r\n  DropdownMenuTrigger,\r\n} from \"@/components/ui/dropdown-menu\"\r\nimport {\r\n  SidebarMenu,\r\n  SidebarMenuButton,\r\n  SidebarMenuItem,\r\n  useSidebar,\r\n} from \"@/components/ui/sidebar\"\r\nimport { useAuth } from \"@/hooks/use-auth\"\r\nimport { toast } from \"sonner\"\r\n\r\nexport function NavUser() {\r\n  const { user, logout } = useAuth();\r\n  // Sử dụng toast trực tiếp từ sonner\r\n  const { isMobile } = useSidebar()\r\n\r\n  // Kiểm tra role của user\r\n  const isAdmin = user?.roles?.includes(\"ADMIN\");\r\n  const isAgent = user?.roles?.includes(\"AGENT\");\r\n\r\n  // N<PERSON>u chưa đăng nhập, hiển thị nút đăng nhập\r\n  if (!user) {\r\n    return (\r\n      <SidebarMenu>\r\n        <SidebarMenuItem>\r\n          <SidebarMenuButton\r\n            size=\"lg\"\r\n            onClick={() => window.location.href = \"/login\"}\r\n          >\r\n            <span>Đăng nhập</span>\r\n          </SidebarMenuButton>\r\n        </SidebarMenuItem>\r\n      </SidebarMenu>\r\n    )\r\n  }\r\n\r\n  return (\r\n    <SidebarMenu>\r\n      <SidebarMenuItem>\r\n        <DropdownMenu>\r\n          <DropdownMenuTrigger asChild>\r\n            <SidebarMenuButton\r\n              size=\"lg\"\r\n              className=\"data-[state=open]:bg-sidebar-accent data-[state=open]:text-sidebar-accent-foreground\"\r\n            >\r\n              <Avatar className=\"h-8 w-8 rounded-lg grayscale\">\r\n                <AvatarImage src={user.avatar || \"/avatars/default.png\"} alt={user.name || \"User\"} />\r\n                <AvatarFallback className=\"rounded-lg\">{user.name ? user.name.charAt(0) : \"U\"}</AvatarFallback>\r\n              </Avatar>\r\n              <div className=\"grid flex-1 text-left text-sm leading-tight\">\r\n                <span className=\"truncate font-medium\">{user.name || \"User\"}</span>\r\n                <span className=\"text-muted-foreground truncate text-xs\">\r\n                  {user.email}\r\n                  {isAdmin && <span className=\"bg-primary/20 text-primary px-1 py-0.5 rounded text-[10px] ml-1\">Admin</span>}\r\n                  {isAgent && <span className=\"bg-orange-100 text-orange-600 px-1 py-0.5 rounded text-[10px] ml-1\">Agent</span>}\r\n                </span>\r\n              </div>\r\n              <IconDotsVertical className=\"ml-auto size-4\" />\r\n            </SidebarMenuButton>\r\n          </DropdownMenuTrigger>\r\n          <DropdownMenuContent\r\n            className=\"w-(--radix-dropdown-menu-trigger-width) min-w-56 rounded-lg\"\r\n            side={isMobile ? \"bottom\" : \"right\"}\r\n            align=\"end\"\r\n            sideOffset={4}\r\n          >\r\n            <DropdownMenuLabel className=\"p-0 font-normal\">\r\n              <div className=\"flex items-center gap-2 px-1 py-1.5 text-left text-sm\">\r\n                <Avatar className=\"h-8 w-8 rounded-lg\">\r\n                  <AvatarImage src={user.avatar || \"/avatars/default.png\"} alt={user.name || \"User\"} />\r\n                  <AvatarFallback className=\"rounded-lg\">{user.name ? user.name.charAt(0) : \"U\"}</AvatarFallback>\r\n                </Avatar>\r\n                <div className=\"grid flex-1 text-left text-sm leading-tight\">\r\n                  <span className=\"truncate font-medium\">{user.name || \"User\"}</span>\r\n                  <span className=\"text-muted-foreground truncate text-xs\">\r\n                    {user.email}\r\n                    {isAdmin && <span className=\"bg-primary/20 text-primary px-1 py-0.5 rounded text-[10px] ml-1\">Admin</span>}\r\n                    {isAgent && <span className=\"bg-orange-100 text-orange-600 px-1 py-0.5 rounded text-[10px] ml-1\">Agent</span>}\r\n                  </span>\r\n                </div>\r\n              </div>\r\n            </DropdownMenuLabel>\r\n            <DropdownMenuSeparator />\r\n            <DropdownMenuGroup>\r\n              {isAdmin ? (\r\n                // Menu cho ADMIN\r\n                <>\r\n                  <DropdownMenuItem onClick={() => window.location.href = \"/admin/dashboard\"}>\r\n                    <IconUserCircle />\r\n                    Dashboard Admin\r\n                  </DropdownMenuItem>\r\n\r\n                  <DropdownMenuSeparator />\r\n                  <DropdownMenuLabel className=\"text-xs font-semibold text-muted-foreground\">QUẢN LÝ NGƯỜI DÙNG</DropdownMenuLabel>\r\n                  <DropdownMenuItem onClick={() => window.location.href = \"/admin/users\"}>\r\n                    <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\" className=\"mr-2\">\r\n                      <path d=\"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2\" />\r\n                      <circle cx=\"9\" cy=\"7\" r=\"4\" />\r\n                      <path d=\"M22 21v-2a4 4 0 0 0-3-3.87\" />\r\n                      <path d=\"M16 3.13a4 4 0 0 1 0 7.75\" />\r\n                    </svg>\r\n                    Quản lý người dùng\r\n                  </DropdownMenuItem>\r\n                  <DropdownMenuItem onClick={() => window.location.href = \"/admin/kyc\"}>\r\n                    <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\" className=\"mr-2\">\r\n                      <path d=\"M16 18a4 4 0 0 0-8 0\" />\r\n                      <circle cx=\"12\" cy=\"10\" r=\"4\" />\r\n                      <rect width=\"20\" height=\"16\" x=\"2\" y=\"4\" rx=\"2\" />\r\n                    </svg>\r\n                    Quản lý KYC\r\n                  </DropdownMenuItem>\r\n\r\n                  <DropdownMenuSeparator />\r\n                  <DropdownMenuLabel className=\"text-xs font-semibold text-muted-foreground\">QUẢN LÝ TÀI CHÍNH</DropdownMenuLabel>\r\n                  <DropdownMenuItem onClick={() => window.location.href = \"/admin/transactions\"}>\r\n                    <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\" className=\"mr-2\">\r\n                      <path d=\"M2 9a2 2 0 0 1 2-2h16a2 2 0 0 1 2 2v9a2 2 0 0 1-2 2H4a2 2 0 0 1-2-2V9Z\" />\r\n                      <path d=\"M2 13h20\" />\r\n                      <path d=\"M9 17h6\" />\r\n                      <path d=\"M4 9V6a2 2 0 0 1 2-2h12a2 2 0 0 1 2 2v3\" />\r\n                    </svg>\r\n                    Quản lý giao dịch\r\n                  </DropdownMenuItem>\r\n                  <DropdownMenuItem onClick={() => window.location.href = \"/admin/contracts\"}>\r\n                    <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\" className=\"mr-2\">\r\n                      <path d=\"M14 3v4a1 1 0 0 0 1 1h4\" />\r\n                      <path d=\"M17 21H7a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h7l5 5v11a2 2 0 0 1-2 2Z\" />\r\n                      <path d=\"M9 9h1\" />\r\n                      <path d=\"M9 13h6\" />\r\n                      <path d=\"M9 17h6\" />\r\n                    </svg>\r\n                    Quản lý hợp đồng\r\n                  </DropdownMenuItem>\r\n                  <DropdownMenuItem onClick={() => window.location.href = \"/admin/agents\"}>\r\n                    <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\" className=\"mr-2\">\r\n                      <path d=\"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2\" />\r\n                      <circle cx=\"9\" cy=\"7\" r=\"4\" />\r\n                      <path d=\"M22 21v-2a4 4 0 0 0-3-3.87\" />\r\n                      <path d=\"M16 3.13a4 4 0 0 1 0 7.75\" />\r\n                    </svg>\r\n                    Quản lý đại lý\r\n                  </DropdownMenuItem>\r\n                  <DropdownMenuItem onClick={() => window.location.href = \"/admin/wallets\"}>\r\n                    <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\" className=\"mr-2\">\r\n                      <path d=\"M2 9a2 2 0 0 1 2-2h16a2 2 0 0 1 2 2v9a2 2 0 0 1-2 2H4a2 2 0 0 1-2-2V9Z\" />\r\n                      <path d=\"M2 13h20\" />\r\n                      <path d=\"M9 17h6\" />\r\n                      <path d=\"M4 9V6a2 2 0 0 1 2-2h12a2 2 0 0 1 2 2v3\" />\r\n                    </svg>\r\n                    Quản lý tài khoản\r\n                  </DropdownMenuItem>\r\n                  <DropdownMenuItem onClick={() => window.location.href = \"/admin/banks\"}>\r\n                    <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\" className=\"mr-2\">\r\n                      <path d=\"M3 21h18\" />\r\n                      <path d=\"M3 7h18\" />\r\n                      <path d=\"M6 10v4\" />\r\n                      <path d=\"M10 10v4\" />\r\n                      <path d=\"M14 10v4\" />\r\n                      <path d=\"M18 10v4\" />\r\n                      <path d=\"m4 4 8-2 8 2\" />\r\n                    </svg>\r\n                    Quản lý ngân hàng\r\n                  </DropdownMenuItem>\r\n                  <DropdownMenuItem onClick={() => window.location.href = \"/admin/user-bank-accounts\"}>\r\n                    <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\" className=\"mr-2\">\r\n                      <path d=\"M3 21h18\" />\r\n                      <path d=\"M3 7h18\" />\r\n                      <path d=\"M6 10v4\" />\r\n                      <path d=\"M10 10v4\" />\r\n                      <path d=\"M14 10v4\" />\r\n                      <path d=\"M18 10v4\" />\r\n                      <path d=\"m4 4 8-2 8 2\" />\r\n                    </svg>\r\n                    Quản lý tài khoản ngân hàng\r\n                  </DropdownMenuItem>\r\n                  <DropdownMenuItem onClick={() => window.location.href = \"/admin/payment-methods\"}>\r\n                    <IconCreditCard />\r\n                    Quản lý thanh toán\r\n                  </DropdownMenuItem>\r\n\r\n                  <DropdownMenuSeparator />\r\n                  <DropdownMenuLabel className=\"text-xs font-semibold text-muted-foreground\">QUẢN LÝ GIAO DỊCH</DropdownMenuLabel>\r\n                  <DropdownMenuItem onClick={() => window.location.href = \"/admin/tokens\"}>\r\n                    <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\" className=\"mr-2\">\r\n                      <circle cx=\"8\" cy=\"8\" r=\"7\" />\r\n                      <circle cx=\"16\" cy=\"16\" r=\"7\" />\r\n                    </svg>\r\n                    Quản lý vàng\r\n                  </DropdownMenuItem>\r\n\r\n                  <DropdownMenuSeparator />\r\n                  <DropdownMenuLabel className=\"text-xs font-semibold text-muted-foreground\">HỆ THỐNG</DropdownMenuLabel>\r\n                  <DropdownMenuItem onClick={() => window.location.href = \"/admin/reports\"}>\r\n                    <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\" className=\"mr-2\">\r\n                      <path d=\"M3 3v18h18\" />\r\n                      <path d=\"m19 9-5-5-4 4-3-3\" />\r\n                    </svg>\r\n                    Báo cáo thống kê\r\n                  </DropdownMenuItem>\r\n                  <DropdownMenuItem onClick={() => window.location.href = \"/admin/roles\"}>\r\n                    <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\" className=\"mr-2\">\r\n                      <rect width=\"18\" height=\"18\" x=\"3\" y=\"3\" rx=\"2\" />\r\n                      <path d=\"M7 15h0M7 11h0M7 7h0\" />\r\n                      <path d=\"M11 15h6M11 11h6M11 7h6\" />\r\n                    </svg>\r\n                    Vai trò & quyền hạn\r\n                  </DropdownMenuItem>\r\n                  <DropdownMenuItem onClick={() => window.location.href = \"/admin/profile\"}>\r\n                    <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\" className=\"mr-2\">\r\n                      <path d=\"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2\" />\r\n                      <circle cx=\"9\" cy=\"7\" r=\"4\" />\r\n                      <path d=\"M22 21v-2a4 4 0 0 0-3-3.87\" />\r\n                      <path d=\"M16 3.13a4 4 0 0 1 0 7.75\" />\r\n                    </svg>\r\n                    Hồ sơ quản trị viên\r\n                  </DropdownMenuItem>\r\n                  <DropdownMenuItem onClick={() => window.location.href = \"/admin/settings\"}>\r\n                    <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\" className=\"mr-2\">\r\n                      <path d=\"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z\" />\r\n                      <circle cx=\"12\" cy=\"12\" r=\"3\" />\r\n                    </svg>\r\n                    Cấu hình hệ thống\r\n                  </DropdownMenuItem>\r\n                </>\r\n              ) : isAgent ? (\r\n                // Menu cho AGENT\r\n                <>\r\n                  <DropdownMenuItem onClick={() => window.location.href = \"/agent/dashboard\"}>\r\n                    <IconUserCircle />\r\n                    Dashboard Agent\r\n                  </DropdownMenuItem>\r\n\r\n                  <DropdownMenuSeparator />\r\n                  <DropdownMenuLabel className=\"text-xs font-semibold text-muted-foreground\">QUẢN LÝ NGƯỜI DÙNG</DropdownMenuLabel>\r\n                  <DropdownMenuItem onClick={() => window.location.href = \"/agent/users\"}>\r\n                    <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\" className=\"mr-2\">\r\n                      <path d=\"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2\" />\r\n                      <circle cx=\"9\" cy=\"7\" r=\"4\" />\r\n                      <path d=\"M22 21v-2a4 4 0 0 0-3-3.87\" />\r\n                      <path d=\"M16 3.13a4 4 0 0 1 0 7.75\" />\r\n                    </svg>\r\n                    Quản lý người dùng\r\n                  </DropdownMenuItem>\r\n                  <DropdownMenuItem onClick={() => window.location.href = \"/agent/kyc\"}>\r\n                    <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\" className=\"mr-2\">\r\n                      <path d=\"M16 18a4 4 0 0 0-8 0\" />\r\n                      <circle cx=\"12\" cy=\"10\" r=\"4\" />\r\n                      <rect width=\"20\" height=\"16\" x=\"2\" y=\"4\" rx=\"2\" />\r\n                    </svg>\r\n                    Quản lý KYC\r\n                  </DropdownMenuItem>\r\n\r\n                  <DropdownMenuSeparator />\r\n                  <DropdownMenuLabel className=\"text-xs font-semibold text-muted-foreground\">QUẢN LÝ TÀI CHÍNH</DropdownMenuLabel>\r\n                  <DropdownMenuItem onClick={() => window.location.href = \"/agent/transactions\"}>\r\n                    <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\" className=\"mr-2\">\r\n                      <path d=\"M2 9a2 2 0 0 1 2-2h16a2 2 0 0 1 2 2v9a2 2 0 0 1-2 2H4a2 2 0 0 1-2-2V9Z\" />\r\n                      <path d=\"M2 13h20\" />\r\n                      <path d=\"M9 17h6\" />\r\n                      <path d=\"M4 9V6a2 2 0 0 1 2-2h12a2 2 0 0 1 2 2v3\" />\r\n                    </svg>\r\n                    Quản lý giao dịch\r\n                  </DropdownMenuItem>\r\n                  <DropdownMenuItem onClick={() => window.location.href = \"/agent/agents\"}>\r\n                    <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\" className=\"mr-2\">\r\n                      <path d=\"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2\" />\r\n                      <circle cx=\"9\" cy=\"7\" r=\"4\" />\r\n                      <path d=\"M22 21v-2a4 4 0 0 0-3-3.87\" />\r\n                      <path d=\"M16 3.13a4 4 0 0 1 0 7.75\" />\r\n                    </svg>\r\n                    Quản lý đại lý\r\n                  </DropdownMenuItem>\r\n                  <DropdownMenuItem onClick={() => window.location.href = \"/agent/wallets\"}>\r\n                    <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\" className=\"mr-2\">\r\n                      <path d=\"M2 9a2 2 0 0 1 2-2h16a2 2 0 0 1 2 2v9a2 2 0 0 1-2 2H4a2 2 0 0 1-2-2V9Z\" />\r\n                      <path d=\"M2 13h20\" />\r\n                      <path d=\"M9 17h6\" />\r\n                      <path d=\"M4 9V6a2 2 0 0 1 2-2h12a2 2 0 0 1 2 2v3\" />\r\n                    </svg>\r\n                    Quản lý tài khoản\r\n                  </DropdownMenuItem>\r\n                  <DropdownMenuItem onClick={() => window.location.href = \"/agent/payment-methods\"}>\r\n                    <IconCreditCard />\r\n                    Quản lý thanh toán\r\n                  </DropdownMenuItem>\r\n\r\n                  <DropdownMenuSeparator />\r\n                  <DropdownMenuLabel className=\"text-xs font-semibold text-muted-foreground\">QUẢN LÝ GIAO DỊCH</DropdownMenuLabel>\r\n                  <DropdownMenuItem onClick={() => window.location.href = \"/agent/tokens\"}>\r\n                    <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\" className=\"mr-2\">\r\n                      <circle cx=\"8\" cy=\"8\" r=\"7\" />\r\n                      <circle cx=\"16\" cy=\"16\" r=\"7\" />\r\n                    </svg>\r\n                    Quản lý bạc\r\n                  </DropdownMenuItem>\r\n                  <DropdownMenuItem onClick={() => window.location.href = \"/agent/order-books\"}>\r\n                    <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\" className=\"mr-2\">\r\n                      <path d=\"M14 3v4a1 1 0 0 0 1 1h4\" />\r\n                      <path d=\"M17 21H7a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h7l5 5v11a2 2 0 0 1-2 2Z\" />\r\n                      <path d=\"M9 9h1\" />\r\n                      <path d=\"M9 13h6\" />\r\n                      <path d=\"M9 17h6\" />\r\n                    </svg>\r\n                    Quản lý giao dịch online\r\n                  </DropdownMenuItem>\r\n\r\n                  <DropdownMenuSeparator />\r\n                  <DropdownMenuLabel className=\"text-xs font-semibold text-muted-foreground\">HỆ THỐNG</DropdownMenuLabel>\r\n                  <DropdownMenuItem onClick={() => window.location.href = \"/agent/reports\"}>\r\n                    <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\" className=\"mr-2\">\r\n                      <path d=\"M3 3v18h18\" />\r\n                      <path d=\"m19 9-5-5-4 4-3-3\" />\r\n                    </svg>\r\n                    Báo cáo thống kê\r\n                  </DropdownMenuItem>\r\n                  <DropdownMenuItem onClick={() => window.location.href = \"/agent/roles\"}>\r\n                    <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\" className=\"mr-2\">\r\n                      <rect width=\"18\" height=\"18\" x=\"3\" y=\"3\" rx=\"2\" />\r\n                      <path d=\"M7 15h0M7 11h0M7 7h0\" />\r\n                      <path d=\"M11 15h6M11 11h6M11 7h6\" />\r\n                    </svg>\r\n                    Vai trò & quyền hạn\r\n                  </DropdownMenuItem>\r\n                </>\r\n              ) : (\r\n                // Menu cho USER\r\n                <>\r\n                  <DropdownMenuItem onClick={() => window.location.href = \"/profile\"}>\r\n                    <IconUserCircle />\r\n                    Thông tin cá nhân\r\n                  </DropdownMenuItem>\r\n                  <DropdownMenuItem onClick={() => window.location.href = \"/kyc\"}>\r\n                    <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\" className=\"mr-2\">\r\n                      <path d=\"M16 18a4 4 0 0 0-8 0\" />\r\n                      <circle cx=\"12\" cy=\"10\" r=\"4\" />\r\n                      <rect width=\"20\" height=\"16\" x=\"2\" y=\"4\" rx=\"2\" />\r\n                    </svg>\r\n                    Xác minh danh tính (KYC)\r\n                  </DropdownMenuItem>\r\n                  <DropdownMenuItem onClick={() => window.location.href = \"/wallet\"}>\r\n                    <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\" className=\"mr-2\">\r\n                      <path d=\"M2 9a2 2 0 0 1 2-2h16a2 2 0 0 1 2 2v9a2 2 0 0 1-2 2H4a2 2 0 0 1-2-2V9Z\" />\r\n                      <path d=\"M2 13h20\" />\r\n                      <path d=\"M9 17h6\" />\r\n                      <path d=\"M4 9V6a2 2 0 0 1 2-2h12a2 2 0 0 1 2 2v3\" />\r\n                    </svg>\r\n                    Ví tiền\r\n                  </DropdownMenuItem>\r\n                  <DropdownMenuItem onClick={() => window.location.href = \"/trading\"}>\r\n                    <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\" className=\"mr-2\">\r\n                      <path d=\"M3 3v18h18\" />\r\n                      <path d=\"m19 9-5-5-4 4-3-3\" />\r\n                    </svg>\r\n                    Giao dịch vàng\r\n                  </DropdownMenuItem>\r\n                  <DropdownMenuItem onClick={() => window.location.href = \"/contracts\"}>\r\n                    <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\" className=\"mr-2\">\r\n                      <path d=\"M14 3v4a1 1 0 0 0 1 1h4\" />\r\n                      <path d=\"M17 21H7a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h7l5 5v11a2 2 0 0 1-2 2Z\" />\r\n                      <path d=\"M9 9h1\" />\r\n                      <path d=\"M9 13h6\" />\r\n                      <path d=\"M9 17h6\" />\r\n                    </svg>\r\n                    Hợp đồng\r\n                  </DropdownMenuItem>\r\n                  <DropdownMenuItem onClick={() => window.location.href = \"/payment-methods\"}>\r\n                    <IconCreditCard />\r\n                    Phương thức thanh toán\r\n                  </DropdownMenuItem>\r\n                  <DropdownMenuItem>\r\n                    <IconNotification />\r\n                    Thông báo\r\n                  </DropdownMenuItem>\r\n                </>\r\n              )}\r\n            </DropdownMenuGroup>\r\n            <DropdownMenuSeparator />\r\n            <DropdownMenuItem onClick={() => {\r\n              logout();\r\n              toast.success(\"Đăng xuất thành công\", {\r\n                description: \"Bạn đã đăng xuất khỏi hệ thống.\"\r\n              });\r\n            }} className=\"text-destructive\">\r\n              <IconLogout className=\"text-destructive\"/>\r\n              Đăng xuất\r\n            </DropdownMenuItem>\r\n          </DropdownMenuContent>\r\n        </DropdownMenu>\r\n      </SidebarMenuItem>\r\n    </SidebarMenu>\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AAAA;AAAA;AAAA;AAAA;AAQA;AAKA;AASA;AAMA;AACA;AA/BA;;;;;;;;AAiCO,SAAS;IACd,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,qHAAA,CAAA,UAAO,AAAD;IAC/B,oCAAoC;IACpC,MAAM,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,4HAAA,CAAA,aAAU,AAAD;IAE9B,yBAAyB;IACzB,MAAM,UAAU,MAAM,OAAO,SAAS;IACtC,MAAM,UAAU,MAAM,OAAO,SAAS;IAEtC,6CAA6C;IAC7C,IAAI,CAAC,MAAM;QACT,qBACE,uVAAC,4HAAA,CAAA,cAAW;sBACV,cAAA,uVAAC,4HAAA,CAAA,kBAAe;0BACd,cAAA,uVAAC,4HAAA,CAAA,oBAAiB;oBAChB,MAAK;oBACL,SAAS,IAAM,OAAO,QAAQ,CAAC,IAAI,GAAG;8BAEtC,cAAA,uVAAC;kCAAK;;;;;;;;;;;;;;;;;;;;;IAKhB;IAEA,qBACE,uVAAC,4HAAA,CAAA,cAAW;kBACV,cAAA,uVAAC,4HAAA,CAAA,kBAAe;sBACd,cAAA,uVAAC,qIAAA,CAAA,eAAY;;kCACX,uVAAC,qIAAA,CAAA,sBAAmB;wBAAC,OAAO;kCAC1B,cAAA,uVAAC,4HAAA,CAAA,oBAAiB;4BAChB,MAAK;4BACL,WAAU;;8CAEV,uVAAC,2HAAA,CAAA,SAAM;oCAAC,WAAU;;sDAChB,uVAAC,2HAAA,CAAA,cAAW;4CAAC,KAAK,KAAK,MAAM,IAAI;4CAAwB,KAAK,KAAK,IAAI,IAAI;;;;;;sDAC3E,uVAAC,2HAAA,CAAA,iBAAc;4CAAC,WAAU;sDAAc,KAAK,IAAI,GAAG,KAAK,IAAI,CAAC,MAAM,CAAC,KAAK;;;;;;;;;;;;8CAE5E,uVAAC;oCAAI,WAAU;;sDACb,uVAAC;4CAAK,WAAU;sDAAwB,KAAK,IAAI,IAAI;;;;;;sDACrD,uVAAC;4CAAK,WAAU;;gDACb,KAAK,KAAK;gDACV,yBAAW,uVAAC;oDAAK,WAAU;8DAAkE;;;;;;gDAC7F,yBAAW,uVAAC;oDAAK,WAAU;8DAAqE;;;;;;;;;;;;;;;;;;8CAGrG,uVAAC,oUAAA,CAAA,mBAAgB;oCAAC,WAAU;;;;;;;;;;;;;;;;;kCAGhC,uVAAC,qIAAA,CAAA,sBAAmB;wBAClB,WAAU;wBACV,MAAM,WAAW,WAAW;wBAC5B,OAAM;wBACN,YAAY;;0CAEZ,uVAAC,qIAAA,CAAA,oBAAiB;gCAAC,WAAU;0CAC3B,cAAA,uVAAC;oCAAI,WAAU;;sDACb,uVAAC,2HAAA,CAAA,SAAM;4CAAC,WAAU;;8DAChB,uVAAC,2HAAA,CAAA,cAAW;oDAAC,KAAK,KAAK,MAAM,IAAI;oDAAwB,KAAK,KAAK,IAAI,IAAI;;;;;;8DAC3E,uVAAC,2HAAA,CAAA,iBAAc;oDAAC,WAAU;8DAAc,KAAK,IAAI,GAAG,KAAK,IAAI,CAAC,MAAM,CAAC,KAAK;;;;;;;;;;;;sDAE5E,uVAAC;4CAAI,WAAU;;8DACb,uVAAC;oDAAK,WAAU;8DAAwB,KAAK,IAAI,IAAI;;;;;;8DACrD,uVAAC;oDAAK,WAAU;;wDACb,KAAK,KAAK;wDACV,yBAAW,uVAAC;4DAAK,WAAU;sEAAkE;;;;;;wDAC7F,yBAAW,uVAAC;4DAAK,WAAU;sEAAqE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAKzG,uVAAC,qIAAA,CAAA,wBAAqB;;;;;0CACtB,uVAAC,qIAAA,CAAA,oBAAiB;0CACf,UACC,iBAAiB;8CACjB;;sDACE,uVAAC,qIAAA,CAAA,mBAAgB;4CAAC,SAAS,IAAM,OAAO,QAAQ,CAAC,IAAI,GAAG;;8DACtD,uVAAC,gUAAA,CAAA,iBAAc;;;;;gDAAG;;;;;;;sDAIpB,uVAAC,qIAAA,CAAA,wBAAqB;;;;;sDACtB,uVAAC,qIAAA,CAAA,oBAAiB;4CAAC,WAAU;sDAA8C;;;;;;sDAC3E,uVAAC,qIAAA,CAAA,mBAAgB;4CAAC,SAAS,IAAM,OAAO,QAAQ,CAAC,IAAI,GAAG;;8DACtD,uVAAC;oDAAI,OAAM;oDAA6B,OAAM;oDAAK,QAAO;oDAAK,SAAQ;oDAAY,MAAK;oDAAO,QAAO;oDAAe,aAAY;oDAAI,eAAc;oDAAQ,gBAAe;oDAAQ,WAAU;;sEAC1L,uVAAC;4DAAK,GAAE;;;;;;sEACR,uVAAC;4DAAO,IAAG;4DAAI,IAAG;4DAAI,GAAE;;;;;;sEACxB,uVAAC;4DAAK,GAAE;;;;;;sEACR,uVAAC;4DAAK,GAAE;;;;;;;;;;;;gDACJ;;;;;;;sDAGR,uVAAC,qIAAA,CAAA,mBAAgB;4CAAC,SAAS,IAAM,OAAO,QAAQ,CAAC,IAAI,GAAG;;8DACtD,uVAAC;oDAAI,OAAM;oDAA6B,OAAM;oDAAK,QAAO;oDAAK,SAAQ;oDAAY,MAAK;oDAAO,QAAO;oDAAe,aAAY;oDAAI,eAAc;oDAAQ,gBAAe;oDAAQ,WAAU;;sEAC1L,uVAAC;4DAAK,GAAE;;;;;;sEACR,uVAAC;4DAAO,IAAG;4DAAK,IAAG;4DAAK,GAAE;;;;;;sEAC1B,uVAAC;4DAAK,OAAM;4DAAK,QAAO;4DAAK,GAAE;4DAAI,GAAE;4DAAI,IAAG;;;;;;;;;;;;gDACxC;;;;;;;sDAIR,uVAAC,qIAAA,CAAA,wBAAqB;;;;;sDACtB,uVAAC,qIAAA,CAAA,oBAAiB;4CAAC,WAAU;sDAA8C;;;;;;sDAC3E,uVAAC,qIAAA,CAAA,mBAAgB;4CAAC,SAAS,IAAM,OAAO,QAAQ,CAAC,IAAI,GAAG;;8DACtD,uVAAC;oDAAI,OAAM;oDAA6B,OAAM;oDAAK,QAAO;oDAAK,SAAQ;oDAAY,MAAK;oDAAO,QAAO;oDAAe,aAAY;oDAAI,eAAc;oDAAQ,gBAAe;oDAAQ,WAAU;;sEAC1L,uVAAC;4DAAK,GAAE;;;;;;sEACR,uVAAC;4DAAK,GAAE;;;;;;sEACR,uVAAC;4DAAK,GAAE;;;;;;sEACR,uVAAC;4DAAK,GAAE;;;;;;;;;;;;gDACJ;;;;;;;sDAGR,uVAAC,qIAAA,CAAA,mBAAgB;4CAAC,SAAS,IAAM,OAAO,QAAQ,CAAC,IAAI,GAAG;;8DACtD,uVAAC;oDAAI,OAAM;oDAA6B,OAAM;oDAAK,QAAO;oDAAK,SAAQ;oDAAY,MAAK;oDAAO,QAAO;oDAAe,aAAY;oDAAI,eAAc;oDAAQ,gBAAe;oDAAQ,WAAU;;sEAC1L,uVAAC;4DAAK,GAAE;;;;;;sEACR,uVAAC;4DAAK,GAAE;;;;;;sEACR,uVAAC;4DAAK,GAAE;;;;;;sEACR,uVAAC;4DAAK,GAAE;;;;;;sEACR,uVAAC;4DAAK,GAAE;;;;;;;;;;;;gDACJ;;;;;;;sDAGR,uVAAC,qIAAA,CAAA,mBAAgB;4CAAC,SAAS,IAAM,OAAO,QAAQ,CAAC,IAAI,GAAG;;8DACtD,uVAAC;oDAAI,OAAM;oDAA6B,OAAM;oDAAK,QAAO;oDAAK,SAAQ;oDAAY,MAAK;oDAAO,QAAO;oDAAe,aAAY;oDAAI,eAAc;oDAAQ,gBAAe;oDAAQ,WAAU;;sEAC1L,uVAAC;4DAAK,GAAE;;;;;;sEACR,uVAAC;4DAAO,IAAG;4DAAI,IAAG;4DAAI,GAAE;;;;;;sEACxB,uVAAC;4DAAK,GAAE;;;;;;sEACR,uVAAC;4DAAK,GAAE;;;;;;;;;;;;gDACJ;;;;;;;sDAGR,uVAAC,qIAAA,CAAA,mBAAgB;4CAAC,SAAS,IAAM,OAAO,QAAQ,CAAC,IAAI,GAAG;;8DACtD,uVAAC;oDAAI,OAAM;oDAA6B,OAAM;oDAAK,QAAO;oDAAK,SAAQ;oDAAY,MAAK;oDAAO,QAAO;oDAAe,aAAY;oDAAI,eAAc;oDAAQ,gBAAe;oDAAQ,WAAU;;sEAC1L,uVAAC;4DAAK,GAAE;;;;;;sEACR,uVAAC;4DAAK,GAAE;;;;;;sEACR,uVAAC;4DAAK,GAAE;;;;;;sEACR,uVAAC;4DAAK,GAAE;;;;;;;;;;;;gDACJ;;;;;;;sDAGR,uVAAC,qIAAA,CAAA,mBAAgB;4CAAC,SAAS,IAAM,OAAO,QAAQ,CAAC,IAAI,GAAG;;8DACtD,uVAAC;oDAAI,OAAM;oDAA6B,OAAM;oDAAK,QAAO;oDAAK,SAAQ;oDAAY,MAAK;oDAAO,QAAO;oDAAe,aAAY;oDAAI,eAAc;oDAAQ,gBAAe;oDAAQ,WAAU;;sEAC1L,uVAAC;4DAAK,GAAE;;;;;;sEACR,uVAAC;4DAAK,GAAE;;;;;;sEACR,uVAAC;4DAAK,GAAE;;;;;;sEACR,uVAAC;4DAAK,GAAE;;;;;;sEACR,uVAAC;4DAAK,GAAE;;;;;;sEACR,uVAAC;4DAAK,GAAE;;;;;;sEACR,uVAAC;4DAAK,GAAE;;;;;;;;;;;;gDACJ;;;;;;;sDAGR,uVAAC,qIAAA,CAAA,mBAAgB;4CAAC,SAAS,IAAM,OAAO,QAAQ,CAAC,IAAI,GAAG;;8DACtD,uVAAC;oDAAI,OAAM;oDAA6B,OAAM;oDAAK,QAAO;oDAAK,SAAQ;oDAAY,MAAK;oDAAO,QAAO;oDAAe,aAAY;oDAAI,eAAc;oDAAQ,gBAAe;oDAAQ,WAAU;;sEAC1L,uVAAC;4DAAK,GAAE;;;;;;sEACR,uVAAC;4DAAK,GAAE;;;;;;sEACR,uVAAC;4DAAK,GAAE;;;;;;sEACR,uVAAC;4DAAK,GAAE;;;;;;sEACR,uVAAC;4DAAK,GAAE;;;;;;sEACR,uVAAC;4DAAK,GAAE;;;;;;sEACR,uVAAC;4DAAK,GAAE;;;;;;;;;;;;gDACJ;;;;;;;sDAGR,uVAAC,qIAAA,CAAA,mBAAgB;4CAAC,SAAS,IAAM,OAAO,QAAQ,CAAC,IAAI,GAAG;;8DACtD,uVAAC,gUAAA,CAAA,iBAAc;;;;;gDAAG;;;;;;;sDAIpB,uVAAC,qIAAA,CAAA,wBAAqB;;;;;sDACtB,uVAAC,qIAAA,CAAA,oBAAiB;4CAAC,WAAU;sDAA8C;;;;;;sDAC3E,uVAAC,qIAAA,CAAA,mBAAgB;4CAAC,SAAS,IAAM,OAAO,QAAQ,CAAC,IAAI,GAAG;;8DACtD,uVAAC;oDAAI,OAAM;oDAA6B,OAAM;oDAAK,QAAO;oDAAK,SAAQ;oDAAY,MAAK;oDAAO,QAAO;oDAAe,aAAY;oDAAI,eAAc;oDAAQ,gBAAe;oDAAQ,WAAU;;sEAC1L,uVAAC;4DAAO,IAAG;4DAAI,IAAG;4DAAI,GAAE;;;;;;sEACxB,uVAAC;4DAAO,IAAG;4DAAK,IAAG;4DAAK,GAAE;;;;;;;;;;;;gDACtB;;;;;;;sDAIR,uVAAC,qIAAA,CAAA,wBAAqB;;;;;sDACtB,uVAAC,qIAAA,CAAA,oBAAiB;4CAAC,WAAU;sDAA8C;;;;;;sDAC3E,uVAAC,qIAAA,CAAA,mBAAgB;4CAAC,SAAS,IAAM,OAAO,QAAQ,CAAC,IAAI,GAAG;;8DACtD,uVAAC;oDAAI,OAAM;oDAA6B,OAAM;oDAAK,QAAO;oDAAK,SAAQ;oDAAY,MAAK;oDAAO,QAAO;oDAAe,aAAY;oDAAI,eAAc;oDAAQ,gBAAe;oDAAQ,WAAU;;sEAC1L,uVAAC;4DAAK,GAAE;;;;;;sEACR,uVAAC;4DAAK,GAAE;;;;;;;;;;;;gDACJ;;;;;;;sDAGR,uVAAC,qIAAA,CAAA,mBAAgB;4CAAC,SAAS,IAAM,OAAO,QAAQ,CAAC,IAAI,GAAG;;8DACtD,uVAAC;oDAAI,OAAM;oDAA6B,OAAM;oDAAK,QAAO;oDAAK,SAAQ;oDAAY,MAAK;oDAAO,QAAO;oDAAe,aAAY;oDAAI,eAAc;oDAAQ,gBAAe;oDAAQ,WAAU;;sEAC1L,uVAAC;4DAAK,OAAM;4DAAK,QAAO;4DAAK,GAAE;4DAAI,GAAE;4DAAI,IAAG;;;;;;sEAC5C,uVAAC;4DAAK,GAAE;;;;;;sEACR,uVAAC;4DAAK,GAAE;;;;;;;;;;;;gDACJ;;;;;;;sDAGR,uVAAC,qIAAA,CAAA,mBAAgB;4CAAC,SAAS,IAAM,OAAO,QAAQ,CAAC,IAAI,GAAG;;8DACtD,uVAAC;oDAAI,OAAM;oDAA6B,OAAM;oDAAK,QAAO;oDAAK,SAAQ;oDAAY,MAAK;oDAAO,QAAO;oDAAe,aAAY;oDAAI,eAAc;oDAAQ,gBAAe;oDAAQ,WAAU;;sEAC1L,uVAAC;4DAAK,GAAE;;;;;;sEACR,uVAAC;4DAAO,IAAG;4DAAI,IAAG;4DAAI,GAAE;;;;;;sEACxB,uVAAC;4DAAK,GAAE;;;;;;sEACR,uVAAC;4DAAK,GAAE;;;;;;;;;;;;gDACJ;;;;;;;sDAGR,uVAAC,qIAAA,CAAA,mBAAgB;4CAAC,SAAS,IAAM,OAAO,QAAQ,CAAC,IAAI,GAAG;;8DACtD,uVAAC;oDAAI,OAAM;oDAA6B,OAAM;oDAAK,QAAO;oDAAK,SAAQ;oDAAY,MAAK;oDAAO,QAAO;oDAAe,aAAY;oDAAI,eAAc;oDAAQ,gBAAe;oDAAQ,WAAU;;sEAC1L,uVAAC;4DAAK,GAAE;;;;;;sEACR,uVAAC;4DAAO,IAAG;4DAAK,IAAG;4DAAK,GAAE;;;;;;;;;;;;gDACtB;;;;;;;;mDAIR,UACF,iBAAiB;8CACjB;;sDACE,uVAAC,qIAAA,CAAA,mBAAgB;4CAAC,SAAS,IAAM,OAAO,QAAQ,CAAC,IAAI,GAAG;;8DACtD,uVAAC,gUAAA,CAAA,iBAAc;;;;;gDAAG;;;;;;;sDAIpB,uVAAC,qIAAA,CAAA,wBAAqB;;;;;sDACtB,uVAAC,qIAAA,CAAA,oBAAiB;4CAAC,WAAU;sDAA8C;;;;;;sDAC3E,uVAAC,qIAAA,CAAA,mBAAgB;4CAAC,SAAS,IAAM,OAAO,QAAQ,CAAC,IAAI,GAAG;;8DACtD,uVAAC;oDAAI,OAAM;oDAA6B,OAAM;oDAAK,QAAO;oDAAK,SAAQ;oDAAY,MAAK;oDAAO,QAAO;oDAAe,aAAY;oDAAI,eAAc;oDAAQ,gBAAe;oDAAQ,WAAU;;sEAC1L,uVAAC;4DAAK,GAAE;;;;;;sEACR,uVAAC;4DAAO,IAAG;4DAAI,IAAG;4DAAI,GAAE;;;;;;sEACxB,uVAAC;4DAAK,GAAE;;;;;;sEACR,uVAAC;4DAAK,GAAE;;;;;;;;;;;;gDACJ;;;;;;;sDAGR,uVAAC,qIAAA,CAAA,mBAAgB;4CAAC,SAAS,IAAM,OAAO,QAAQ,CAAC,IAAI,GAAG;;8DACtD,uVAAC;oDAAI,OAAM;oDAA6B,OAAM;oDAAK,QAAO;oDAAK,SAAQ;oDAAY,MAAK;oDAAO,QAAO;oDAAe,aAAY;oDAAI,eAAc;oDAAQ,gBAAe;oDAAQ,WAAU;;sEAC1L,uVAAC;4DAAK,GAAE;;;;;;sEACR,uVAAC;4DAAO,IAAG;4DAAK,IAAG;4DAAK,GAAE;;;;;;sEAC1B,uVAAC;4DAAK,OAAM;4DAAK,QAAO;4DAAK,GAAE;4DAAI,GAAE;4DAAI,IAAG;;;;;;;;;;;;gDACxC;;;;;;;sDAIR,uVAAC,qIAAA,CAAA,wBAAqB;;;;;sDACtB,uVAAC,qIAAA,CAAA,oBAAiB;4CAAC,WAAU;sDAA8C;;;;;;sDAC3E,uVAAC,qIAAA,CAAA,mBAAgB;4CAAC,SAAS,IAAM,OAAO,QAAQ,CAAC,IAAI,GAAG;;8DACtD,uVAAC;oDAAI,OAAM;oDAA6B,OAAM;oDAAK,QAAO;oDAAK,SAAQ;oDAAY,MAAK;oDAAO,QAAO;oDAAe,aAAY;oDAAI,eAAc;oDAAQ,gBAAe;oDAAQ,WAAU;;sEAC1L,uVAAC;4DAAK,GAAE;;;;;;sEACR,uVAAC;4DAAK,GAAE;;;;;;sEACR,uVAAC;4DAAK,GAAE;;;;;;sEACR,uVAAC;4DAAK,GAAE;;;;;;;;;;;;gDACJ;;;;;;;sDAGR,uVAAC,qIAAA,CAAA,mBAAgB;4CAAC,SAAS,IAAM,OAAO,QAAQ,CAAC,IAAI,GAAG;;8DACtD,uVAAC;oDAAI,OAAM;oDAA6B,OAAM;oDAAK,QAAO;oDAAK,SAAQ;oDAAY,MAAK;oDAAO,QAAO;oDAAe,aAAY;oDAAI,eAAc;oDAAQ,gBAAe;oDAAQ,WAAU;;sEAC1L,uVAAC;4DAAK,GAAE;;;;;;sEACR,uVAAC;4DAAO,IAAG;4DAAI,IAAG;4DAAI,GAAE;;;;;;sEACxB,uVAAC;4DAAK,GAAE;;;;;;sEACR,uVAAC;4DAAK,GAAE;;;;;;;;;;;;gDACJ;;;;;;;sDAGR,uVAAC,qIAAA,CAAA,mBAAgB;4CAAC,SAAS,IAAM,OAAO,QAAQ,CAAC,IAAI,GAAG;;8DACtD,uVAAC;oDAAI,OAAM;oDAA6B,OAAM;oDAAK,QAAO;oDAAK,SAAQ;oDAAY,MAAK;oDAAO,QAAO;oDAAe,aAAY;oDAAI,eAAc;oDAAQ,gBAAe;oDAAQ,WAAU;;sEAC1L,uVAAC;4DAAK,GAAE;;;;;;sEACR,uVAAC;4DAAK,GAAE;;;;;;sEACR,uVAAC;4DAAK,GAAE;;;;;;sEACR,uVAAC;4DAAK,GAAE;;;;;;;;;;;;gDACJ;;;;;;;sDAGR,uVAAC,qIAAA,CAAA,mBAAgB;4CAAC,SAAS,IAAM,OAAO,QAAQ,CAAC,IAAI,GAAG;;8DACtD,uVAAC,gUAAA,CAAA,iBAAc;;;;;gDAAG;;;;;;;sDAIpB,uVAAC,qIAAA,CAAA,wBAAqB;;;;;sDACtB,uVAAC,qIAAA,CAAA,oBAAiB;4CAAC,WAAU;sDAA8C;;;;;;sDAC3E,uVAAC,qIAAA,CAAA,mBAAgB;4CAAC,SAAS,IAAM,OAAO,QAAQ,CAAC,IAAI,GAAG;;8DACtD,uVAAC;oDAAI,OAAM;oDAA6B,OAAM;oDAAK,QAAO;oDAAK,SAAQ;oDAAY,MAAK;oDAAO,QAAO;oDAAe,aAAY;oDAAI,eAAc;oDAAQ,gBAAe;oDAAQ,WAAU;;sEAC1L,uVAAC;4DAAO,IAAG;4DAAI,IAAG;4DAAI,GAAE;;;;;;sEACxB,uVAAC;4DAAO,IAAG;4DAAK,IAAG;4DAAK,GAAE;;;;;;;;;;;;gDACtB;;;;;;;sDAGR,uVAAC,qIAAA,CAAA,mBAAgB;4CAAC,SAAS,IAAM,OAAO,QAAQ,CAAC,IAAI,GAAG;;8DACtD,uVAAC;oDAAI,OAAM;oDAA6B,OAAM;oDAAK,QAAO;oDAAK,SAAQ;oDAAY,MAAK;oDAAO,QAAO;oDAAe,aAAY;oDAAI,eAAc;oDAAQ,gBAAe;oDAAQ,WAAU;;sEAC1L,uVAAC;4DAAK,GAAE;;;;;;sEACR,uVAAC;4DAAK,GAAE;;;;;;sEACR,uVAAC;4DAAK,GAAE;;;;;;sEACR,uVAAC;4DAAK,GAAE;;;;;;sEACR,uVAAC;4DAAK,GAAE;;;;;;;;;;;;gDACJ;;;;;;;sDAIR,uVAAC,qIAAA,CAAA,wBAAqB;;;;;sDACtB,uVAAC,qIAAA,CAAA,oBAAiB;4CAAC,WAAU;sDAA8C;;;;;;sDAC3E,uVAAC,qIAAA,CAAA,mBAAgB;4CAAC,SAAS,IAAM,OAAO,QAAQ,CAAC,IAAI,GAAG;;8DACtD,uVAAC;oDAAI,OAAM;oDAA6B,OAAM;oDAAK,QAAO;oDAAK,SAAQ;oDAAY,MAAK;oDAAO,QAAO;oDAAe,aAAY;oDAAI,eAAc;oDAAQ,gBAAe;oDAAQ,WAAU;;sEAC1L,uVAAC;4DAAK,GAAE;;;;;;sEACR,uVAAC;4DAAK,GAAE;;;;;;;;;;;;gDACJ;;;;;;;sDAGR,uVAAC,qIAAA,CAAA,mBAAgB;4CAAC,SAAS,IAAM,OAAO,QAAQ,CAAC,IAAI,GAAG;;8DACtD,uVAAC;oDAAI,OAAM;oDAA6B,OAAM;oDAAK,QAAO;oDAAK,SAAQ;oDAAY,MAAK;oDAAO,QAAO;oDAAe,aAAY;oDAAI,eAAc;oDAAQ,gBAAe;oDAAQ,WAAU;;sEAC1L,uVAAC;4DAAK,OAAM;4DAAK,QAAO;4DAAK,GAAE;4DAAI,GAAE;4DAAI,IAAG;;;;;;sEAC5C,uVAAC;4DAAK,GAAE;;;;;;sEACR,uVAAC;4DAAK,GAAE;;;;;;;;;;;;gDACJ;;;;;;;;mDAKV,gBAAgB;8CAChB;;sDACE,uVAAC,qIAAA,CAAA,mBAAgB;4CAAC,SAAS,IAAM,OAAO,QAAQ,CAAC,IAAI,GAAG;;8DACtD,uVAAC,gUAAA,CAAA,iBAAc;;;;;gDAAG;;;;;;;sDAGpB,uVAAC,qIAAA,CAAA,mBAAgB;4CAAC,SAAS,IAAM,OAAO,QAAQ,CAAC,IAAI,GAAG;;8DACtD,uVAAC;oDAAI,OAAM;oDAA6B,OAAM;oDAAK,QAAO;oDAAK,SAAQ;oDAAY,MAAK;oDAAO,QAAO;oDAAe,aAAY;oDAAI,eAAc;oDAAQ,gBAAe;oDAAQ,WAAU;;sEAC1L,uVAAC;4DAAK,GAAE;;;;;;sEACR,uVAAC;4DAAO,IAAG;4DAAK,IAAG;4DAAK,GAAE;;;;;;sEAC1B,uVAAC;4DAAK,OAAM;4DAAK,QAAO;4DAAK,GAAE;4DAAI,GAAE;4DAAI,IAAG;;;;;;;;;;;;gDACxC;;;;;;;sDAGR,uVAAC,qIAAA,CAAA,mBAAgB;4CAAC,SAAS,IAAM,OAAO,QAAQ,CAAC,IAAI,GAAG;;8DACtD,uVAAC;oDAAI,OAAM;oDAA6B,OAAM;oDAAK,QAAO;oDAAK,SAAQ;oDAAY,MAAK;oDAAO,QAAO;oDAAe,aAAY;oDAAI,eAAc;oDAAQ,gBAAe;oDAAQ,WAAU;;sEAC1L,uVAAC;4DAAK,GAAE;;;;;;sEACR,uVAAC;4DAAK,GAAE;;;;;;sEACR,uVAAC;4DAAK,GAAE;;;;;;sEACR,uVAAC;4DAAK,GAAE;;;;;;;;;;;;gDACJ;;;;;;;sDAGR,uVAAC,qIAAA,CAAA,mBAAgB;4CAAC,SAAS,IAAM,OAAO,QAAQ,CAAC,IAAI,GAAG;;8DACtD,uVAAC;oDAAI,OAAM;oDAA6B,OAAM;oDAAK,QAAO;oDAAK,SAAQ;oDAAY,MAAK;oDAAO,QAAO;oDAAe,aAAY;oDAAI,eAAc;oDAAQ,gBAAe;oDAAQ,WAAU;;sEAC1L,uVAAC;4DAAK,GAAE;;;;;;sEACR,uVAAC;4DAAK,GAAE;;;;;;;;;;;;gDACJ;;;;;;;sDAGR,uVAAC,qIAAA,CAAA,mBAAgB;4CAAC,SAAS,IAAM,OAAO,QAAQ,CAAC,IAAI,GAAG;;8DACtD,uVAAC;oDAAI,OAAM;oDAA6B,OAAM;oDAAK,QAAO;oDAAK,SAAQ;oDAAY,MAAK;oDAAO,QAAO;oDAAe,aAAY;oDAAI,eAAc;oDAAQ,gBAAe;oDAAQ,WAAU;;sEAC1L,uVAAC;4DAAK,GAAE;;;;;;sEACR,uVAAC;4DAAK,GAAE;;;;;;sEACR,uVAAC;4DAAK,GAAE;;;;;;sEACR,uVAAC;4DAAK,GAAE;;;;;;sEACR,uVAAC;4DAAK,GAAE;;;;;;;;;;;;gDACJ;;;;;;;sDAGR,uVAAC,qIAAA,CAAA,mBAAgB;4CAAC,SAAS,IAAM,OAAO,QAAQ,CAAC,IAAI,GAAG;;8DACtD,uVAAC,gUAAA,CAAA,iBAAc;;;;;gDAAG;;;;;;;sDAGpB,uVAAC,qIAAA,CAAA,mBAAgB;;8DACf,uVAAC,oUAAA,CAAA,mBAAgB;;;;;gDAAG;;;;;;;;;;;;;;0CAM5B,uVAAC,qIAAA,CAAA,wBAAqB;;;;;0CACtB,uVAAC,qIAAA,CAAA,mBAAgB;gCAAC,SAAS;oCACzB;oCACA,wQAAA,CAAA,QAAK,CAAC,OAAO,CAAC,wBAAwB;wCACpC,aAAa;oCACf;gCACF;gCAAG,WAAU;;kDACX,uVAAC,wTAAA,CAAA,aAAU;wCAAC,WAAU;;;;;;oCAAoB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQxD", "debugId": null}}, {"offset": {"line": 3577, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/bloomgoo/gold-exchange-platform/frontend/components/app-sidebar.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport { useAuth } from \"@/hooks/use-auth\"\r\nimport {\r\n  IconBuildingBank,\r\n  IconCash,\r\n  IconCategory,\r\n  IconChartCandle,\r\n  IconCoin,\r\n  IconCreditCard,\r\n  IconCurrencyDollar,\r\n  IconDashboard,\r\n  IconDeviceAnalytics,\r\n  IconExchange,\r\n  IconFileAnalytics,\r\n  IconFileDescription,\r\n  IconFolders,\r\n  IconGift,\r\n  IconHistory,\r\n  IconMoneybag,\r\n  IconPackage,\r\n  IconPackageImport,\r\n  IconReceipt,\r\n  IconRobot,\r\n  IconSettings,\r\n  IconShieldLock,\r\n  IconShoppingCart,\r\n  IconUser,\r\n  IconUserCheck,\r\n  IconUserCircle,\r\n  IconUserCog,\r\n  IconUserStar,\r\n  IconWallet,\r\n  IconNews,\r\n  IconTags,\r\n  IconPhoto,\r\n  IconMessageCircle,\r\n  IconBuilding,\r\n  IconBrandPagekit,\r\n  IconHeartHandshake,\r\n  IconWorld\r\n} from \"@tabler/icons-react\"\r\nimport Link from \"next/link\"\r\nimport * as React from \"react\"\r\n\r\nimport { NavMain } from \"@/components/nav-main\"\r\nimport { NavSecondary } from \"@/components/nav-secondary\"\r\nimport { NavUser } from \"@/components/nav-user\"\r\nimport {\r\n  Sidebar,\r\n  SidebarContent,\r\n  SidebarFooter,\r\n  SidebarHeader,\r\n  SidebarMenu,\r\n  SidebarMenuButton,\r\n  SidebarMenuItem,\r\n} from \"@/components/ui/sidebar\"\r\nimport { Avatar, AvatarFallback, AvatarImage } from \"./ui/avatar\"\r\nimport { useSiteMetadata } from \"@/hooks/use-site-metadata\"\r\n\r\n// Dữ liệu menu cho USER\r\n\r\nconst userNavData = {\r\n  navMain: [\r\n    {\r\n      key: \"user-dashboard\",\r\n      title: \"Bảng quản trị\",\r\n      url: \"/reports\",\r\n      // url: \"/dashboard\",\r\n      icon: IconDashboard,\r\n    },\r\n    // {\r\n    //   title: \"Báo cáo thống kê\",\r\n    //   url: \"/reports\",\r\n    //   icon: IconFileAnalytics,\r\n    // },\r\n    {\r\n      type: \"separator\",\r\n      title: \"QUẢN TRỊ BẠC TRỰC TUYẾN\",\r\n    },\r\n    {\r\n      key: \"user-trading\",\r\n      title: \"Giao dịch mua bán\",\r\n      url: \"/trading\",\r\n      icon: IconChartCandle,\r\n    },\r\n    {\r\n      key: \"user-order-books\",\r\n      title: \"Lịch sử giao dịch\",\r\n      url: \"/order-books\",\r\n      icon: IconReceipt,\r\n    },\r\n    {\r\n      key: \"user-token-assets\",\r\n      title: \"Tài sản\",\r\n      url: \"/token-assets\",\r\n      icon: IconCoin,\r\n    },\r\n    {\r\n      type: \"separator\",\r\n      title: \"QUẢN TRỊ SẢN PHẨM BẠC\",\r\n    },\r\n    {\r\n      key: \"user-ecom-products\",\r\n      title: \"Sản phẩm\",\r\n      url: \"/ecom-products\",\r\n      icon: IconPackageImport,\r\n    },\r\n    {\r\n      key: \"user-ecom-orders\",\r\n      title: \"Đơn hàng\",\r\n      url: \"/ecom-orders\",\r\n      icon: IconShoppingCart,\r\n    },\r\n    {\r\n      type: \"separator\",\r\n      title: \"QUẢN TRỊ TÀI CHÍNH\",\r\n    },\r\n    {\r\n      key: \"transactions\",\r\n      title: \"Lịch sử thanh toán\",\r\n      url: \"/transactions\",\r\n      icon: IconCurrencyDollar,\r\n    },\r\n    {\r\n      type: \"separator\",\r\n      title: \"QUẢN TRỊ THÔNG TIN CÁ NHÂN\",\r\n    },\r\n    {\r\n      key: \"user-profile\",\r\n      title: \"Hồ sơ cá nhân\",\r\n      url: \"/profile\",\r\n      icon: IconUser,\r\n    },\r\n    {\r\n      key: \"user-kyc\",\r\n      title: \"Xác thực danh tính\",\r\n      url: \"/kyc\",\r\n      icon: IconUserCheck,\r\n    },\r\n  ],\r\n};\r\n\r\n// Dữ liệu menu cho ADMIN\r\nconst adminNavData = {\r\n  navMain: [\r\n    {\r\n      key: \"admin-dashboard\",\r\n      title: \"Bảng quản trị\",\r\n      url: \"/admin/dashboard\",\r\n      icon: IconDashboard,\r\n    },\r\n    {\r\n      key: \"admin-reports\",\r\n      title: \"Báo cáo thống kê\",\r\n      url: \"/admin/reports\",\r\n      icon: IconDeviceAnalytics,\r\n    },\r\n    {\r\n      type: \"separator\",\r\n      title: \"QUẢN TRỊ BẠC TRỰC TUYẾN\",\r\n    },\r\n    {\r\n      key: \"admin-order-books\",\r\n      title: \"Giao dịch online\",\r\n      url: \"/admin/order-books\",\r\n      icon: IconExchange,\r\n    },\r\n    {\r\n      key: \"admin-token-assets\",\r\n      title: \"Quản lý tài sản\",\r\n      url: \"/admin/token-assets\",\r\n      icon: IconMoneybag,\r\n    },\r\n    {\r\n      key: \"tokens\",\r\n      title: \"Quản lý sản phẩm\",\r\n      url: \"/admin/tokens\",\r\n      icon: IconPackage,\r\n    },\r\n    {\r\n      key: \"token-categories\",\r\n      title: \"Danh mục sản phẩm\",\r\n      url: \"/admin/token-categories\",\r\n      icon: IconCategory,\r\n    },\r\n    {\r\n      type: \"separator\",\r\n      title: \"QUẢN TRỊ SẢN PHẨM BẠC\",\r\n    },\r\n    {\r\n      key: \"admin-ecom-orders\",\r\n      title: \"Quản lý đơn hàng\",\r\n      url: \"/admin/ecom-orders\",\r\n      icon: IconShoppingCart,\r\n    },\r\n    {\r\n      key: \"ecom-products\",\r\n      title: \"Quản lý sản phẩm\",\r\n      url: \"/admin/ecom-products\",\r\n      icon: IconPackageImport,\r\n    },\r\n    {\r\n      key: \"ecom-product-categories\",\r\n      title: \"Danh mục sản phẩm\",\r\n      url: \"/admin/ecom-product-categories\",\r\n      icon: IconFolders,\r\n    },\r\n    {\r\n      type: \"separator\",\r\n      title: \"QUẢN TRỊ TÀI CHÍNH\",\r\n    },\r\n    {\r\n      key: \"admin-wallets\",\r\n      title: \"Quản lý tài khoản\",\r\n      url: \"/admin/wallets\",\r\n      icon: IconWallet,\r\n    },\r\n    {\r\n      key: \"admin-transactions\",\r\n      title: \"Lịch sử thanh toán\",\r\n      url: \"/admin/transactions\",\r\n      icon: IconCurrencyDollar,\r\n    },\r\n    {\r\n      key: \"admin-payment-methods\",\r\n      title: \"Phương thức thanh toán\",\r\n      url: \"/admin/payment-methods\",\r\n      icon: IconCreditCard,\r\n    },\r\n    {\r\n      key: \"admin-banks\",\r\n      title: \"Quản lý ngân hàng\",\r\n      url: \"/admin/banks\",\r\n      icon: IconBuildingBank,\r\n    },\r\n    {\r\n      type: \"separator\",\r\n      title: \"QUẢN TRỊ ĐIỂM THƯỞNG\",\r\n    },\r\n    {\r\n      key: \"admin-crypto-wallets\",\r\n      title: \"Tài khoản điểm thưởng\",\r\n      url: \"/admin/crypto-wallets\",\r\n      icon: IconGift,\r\n    },\r\n    {\r\n      key: \"admin-crypto-transactions\",\r\n      title: \"Lịch sử điểm thưởng\",\r\n      url: \"/admin/crypto-transactions\",\r\n      icon: IconHistory,\r\n    },\r\n    {\r\n      type: \"separator\",\r\n      title: \"QUẢN TRỊ ĐẠI LÝ\",\r\n    },\r\n    {\r\n      key: \"admin-agents\",\r\n      title: \"Quản lý đại lý\",\r\n      url: \"/admin/agents\",\r\n      icon: IconUserStar,\r\n    },\r\n    {\r\n      key: \"admin-agent-commissions\",\r\n      title: \"Lịch sử hoa hồng\",\r\n      url: \"/admin/agent-commissions\",\r\n      icon: IconCash,\r\n    },\r\n    {\r\n      type: \"separator\",\r\n      title: \"QUẢN TRỊ NỘI DUNG\",\r\n    },\r\n    {\r\n      key: \"cms-categories\",\r\n      title: \"Chuyên mục\",\r\n      url: \"/admin/cms/categories\",\r\n      icon: IconCategory,\r\n    },\r\n    {\r\n      key: \"cms-posts\",\r\n      title: \"Bài viết\",\r\n      url: \"/admin/cms/posts\",\r\n      icon: IconNews,\r\n    },\r\n    {\r\n      key: \"cms-tags\",\r\n      title: \"Thẻ\",\r\n      url: \"/admin/cms/tags\",\r\n      icon: IconTags,\r\n    },\r\n    {\r\n      key: \"cms-pages\",\r\n      title: \"Trang\",\r\n      url: \"/admin/cms/pages\",\r\n      icon: IconBrandPagekit,\r\n    },\r\n    {\r\n      key: \"cms-banners\",\r\n      title: \"Banner\",\r\n      url: \"/admin/cms/banners\",\r\n      icon: IconPhoto,\r\n    },\r\n    {\r\n      key: \"cms-customer-feedbacks\",\r\n      title: \"Phản hồi khách hàng\",\r\n      url: \"/admin/cms/customer-feedbacks\",\r\n      icon: IconMessageCircle,\r\n    },\r\n    {\r\n      key: \"cms-showrooms\",\r\n      title: \"Cửa hàng/đại lý\",\r\n      url: \"/admin/cms/showrooms\",\r\n      icon: IconBuilding,\r\n    },\r\n    {\r\n      key: \"cms-partners\",\r\n      title: \"Đối tác\",\r\n      url: \"/admin/cms/partners\",\r\n      icon: IconHeartHandshake,\r\n    },\r\n    {\r\n      type: \"separator\",\r\n      title: \"QUẢN TRỊ NGƯỜI DÙNG\",\r\n    },\r\n    {\r\n      key: \"admin-users\",\r\n      title: \"Quản lý người dùng\",\r\n      url: \"/admin/users\",\r\n      icon: IconUserCog,\r\n    },\r\n    {\r\n      key: \"admin-roles\",\r\n      title: \"Vai trò & quyền hạn\",\r\n      url: \"/admin/roles\",\r\n      icon: IconShieldLock,\r\n    },\r\n    {\r\n      key: \"admin-kyc\",\r\n      title: \"Xác thực danh tính\",\r\n      url: \"/admin/kyc\",\r\n      icon: IconUserCheck,\r\n    },\r\n    {\r\n      type: \"separator\",\r\n      title: \"QUẢN TRỊ HỆ THỐNG\",\r\n    },\r\n    {\r\n      key: \"admin-profile\",\r\n      title: \"Hồ sơ quản trị viên\",\r\n      url: \"/admin/profile\",\r\n      icon: IconUserCircle,\r\n    },\r\n    {\r\n      key: \"admin-settings\",\r\n      title: \"Cấu hình hệ thống\",\r\n      url: \"/admin/settings\",\r\n      icon: IconSettings,\r\n    },\r\n    {\r\n      key: \"admin-website-settings\",\r\n      title: \"Cấu hình website\",\r\n      url: \"/admin/website-settings\",\r\n      icon: IconWorld,\r\n    },\r\n  ],\r\n};\r\n\r\n// Dữ liệu menu cho ADMIN\r\nconst agentNavData = {\r\n  navMain: [\r\n    {\r\n      key: \"agent-dashboard\",\r\n      title: \"Bảng quản trị\",\r\n      url: \"/agent/dashboard\",\r\n      icon: IconDashboard,\r\n    },\r\n    {\r\n      key: \"agent-reports\",\r\n      title: \"Báo cáo thống kê\",\r\n      url: \"/agent/reports\",\r\n      icon: IconDeviceAnalytics,\r\n    },\r\n    {\r\n      type: \"separator\",\r\n      title: \"QUẢN TRỊ BẠC TRỰC TUYẾN\",\r\n    },\r\n    {\r\n      key: \"agent-order-books\",\r\n      title: \"Giao dịch online\",\r\n      url: \"/agent/order-books\",\r\n      icon: IconExchange,\r\n    },\r\n    {\r\n      key: \"agent-token-assets\",\r\n      title: \"Quản lý tài sản\",\r\n      url: \"/agent/token-assets\",\r\n      icon: IconMoneybag,\r\n    },\r\n    // {\r\n    //   title: \"Quản lý sản phẩm\",\r\n    //   url: \"/agent/tokens\",\r\n    //   icon: IconPackage,\r\n    // },\r\n    // {\r\n    //   title: \"Danh mục sản phẩm\",\r\n    //   url: \"/agent/token-categories\",\r\n    //   icon: IconCategory,\r\n    // },\r\n    {\r\n      type: \"separator\",\r\n      title: \"QUẢN TRỊ SẢN PHẨM BẠC\",\r\n    },\r\n    {\r\n      key: \"agent-ecom-orders\",\r\n      title: \"Quản lý đơn hàng\",\r\n      url: \"/agent/ecom-orders\",\r\n      icon: IconShoppingCart,\r\n    },\r\n    // {\r\n    //   title: \"Quản lý sản phẩm\",\r\n    //   url: \"/agent/ecom-products\",\r\n    //   icon: IconPackageImport,\r\n    // },\r\n    // {\r\n    //   title: \"Danh mục sản phẩm\",\r\n    //   url: \"/agent/ecom-product-categories\",\r\n    //   icon: IconFolders,\r\n    // },\r\n    {\r\n      type: \"separator\",\r\n      title: \"QUẢN TRỊ TÀI CHÍNH\",\r\n    },\r\n    {\r\n      key: \"agent-wallets\",\r\n      title: \"Quản lý tài khoản\",\r\n      url: \"/agent/wallets\",\r\n      icon: IconWallet,\r\n    },\r\n    {\r\n      key: \"agent-transactions\",\r\n      title: \"Lịch sử thanh toán\",\r\n      url: \"/agent/transactions\",\r\n      icon: IconCurrencyDollar,\r\n    },\r\n    {\r\n      key: \"agent-payment-methods\",\r\n      title: \"Phương thức thanh toán\",\r\n      url: \"/agent/payment-methods\",\r\n      icon: IconCreditCard,\r\n    },\r\n    {\r\n      type: \"separator\",\r\n      title: \"QUẢN TRỊ ĐIỂM THƯỞNG\",\r\n    },\r\n    {\r\n      key: \"agent-crypto-wallets\",\r\n      title: \"Tài khoản điểm thưởng\",\r\n      url: \"/agent/crypto-wallets\",\r\n      icon: IconGift,\r\n    },\r\n    {\r\n      key: \"agent-crypto-transactions\",\r\n      title: \"Lịch sử điểm thưởng\",\r\n      url: \"/agent/crypto-transactions\",\r\n      icon: IconHistory,\r\n    },\r\n    {\r\n      type: \"separator\",\r\n      title: \"QUẢN TRỊ ĐẠI LÝ\",\r\n    },\r\n    {\r\n      key: \"agent-agents-hierarchical\",\r\n      title: \"Quản lý hoa hồng\",\r\n      url: \"/agent/agents-hierarchical\",\r\n      icon: IconUserStar,\r\n    },\r\n    {\r\n      key: \"agent-agent-commissions\",\r\n      title: \"Lịch sử hoa hồng\",\r\n      url: \"/agent/agent-commissions\",\r\n      icon: IconCash,\r\n    },\r\n    // {\r\n    //   type: \"separator\",\r\n    //   title: \"QUẢN LÝ NGƯỜI DÙNG\",\r\n    // },\r\n    // {\r\n    //   title: \"Quản lý người dùng\",\r\n    //   url: \"/agent/users\",\r\n    //   icon: IconUserCog,\r\n    // },\r\n    // {\r\n    //   title: \"Vai trò & quyền hạn\",\r\n    //   url: \"/agent/roles\",\r\n    //   icon: IconShieldLock,\r\n    // },\r\n    // {\r\n    //   title: \"Xác thực danh tính\",\r\n    //   url: \"/agent/kyc\",\r\n    //   icon: IconUserCheck,\r\n    // }\r\n  ],\r\n};\r\n\r\nconst data = {\r\n  navClouds: [\r\n    {\r\n      title: \"Capture\",\r\n      icon: IconFileAnalytics,\r\n      isActive: true,\r\n      url: \"#\",\r\n      items: [\r\n        {\r\n          title: \"Active Proposals\",\r\n          url: \"#\",\r\n        },\r\n        {\r\n          title: \"Archived\",\r\n          url: \"#\",\r\n        },\r\n      ],\r\n    },\r\n    {\r\n      title: \"Proposal\",\r\n      icon: IconFileDescription,\r\n      url: \"#\",\r\n      items: [\r\n        {\r\n          title: \"Active Proposals\",\r\n          url: \"#\",\r\n        },\r\n        {\r\n          title: \"Archived\",\r\n          url: \"#\",\r\n        },\r\n      ],\r\n    },\r\n    {\r\n      title: \"Prompts\",\r\n      icon: IconRobot,\r\n      url: \"#\",\r\n      items: [\r\n        {\r\n          title: \"Active Proposals\",\r\n          url: \"#\",\r\n        },\r\n        {\r\n          title: \"Archived\",\r\n          url: \"#\",\r\n        },\r\n      ],\r\n    },\r\n  ],\r\n  navSecondary: [\r\n  ],\r\n}\r\n\r\nexport function AppSidebar({ ...props }: React.ComponentProps<typeof Sidebar>) {\r\n  const { user, isLoading } = useAuth();\r\n  const { config } = useSiteMetadata();\r\n  const siteName = config?.['site_name'];\r\n  const siteLogo = config?.['site_logo'];\r\n\r\n  // Sử dụng ref để lưu menu cuối cùng khi user còn tồn tại\r\n  const lastValidNavDataRef = React.useRef(userNavData);\r\n  const lastValidUserRef = React.useRef<any>(null);\r\n\r\n  // Cập nhật ref khi có user hợp lệ\r\n  if (user && user.roles) {\r\n    const isAdmin = user.roles.includes(\"ADMIN\");\r\n    const isAgent = user.roles.includes(\"AGENT\");\r\n    lastValidNavDataRef.current = isAdmin ? adminNavData : isAgent ? agentNavData : userNavData;\r\n    lastValidUserRef.current = user;\r\n  }\r\n\r\n  // Nếu đang loading (logout), giữ nguyên menu cuối cùng nhưng disable interactions\r\n  if (isLoading && !user) {\r\n    return (\r\n      <Sidebar collapsible=\"icon\" {...props} className=\"pointer-events-none opacity-75\">\r\n        <SidebarHeader>\r\n          <SidebarMenu>\r\n            <SidebarMenuItem>\r\n              <SidebarMenuButton\r\n                asChild\r\n                className=\"data-[slot=sidebar-menu-button]:!p-1.5\"\r\n              >\r\n                <div className=\"flex items-center\">\r\n                  <img src={siteLogo} alt={siteName} className=\"h-10 w-[160px] min-w-[40px] object-contain object-left\" />\r\n                </div>\r\n              </SidebarMenuButton>\r\n            </SidebarMenuItem>\r\n          </SidebarMenu>\r\n        </SidebarHeader>\r\n        <SidebarContent className=\"[&_.tabler-icon]:w-5 [&_.tabler-icon]:h-5 [&_.tabler-icon]:min-w-[20px] [&_.tabler-icon]:min-h-[20px]\">\r\n          {/* Giữ nguyên menu cuối cùng */}\r\n          <NavMain items={lastValidNavDataRef.current.navMain} />\r\n          <NavSecondary items={data.navSecondary} className=\"mt-auto\" />\r\n        </SidebarContent>\r\n        <SidebarFooter>\r\n          <NavUser />\r\n        </SidebarFooter>\r\n      </Sidebar>\r\n    );\r\n  }\r\n\r\n  // Guard: Nếu không có user sau khi loading xong\r\n  if (!user) {\r\n    return (\r\n      <Sidebar collapsible=\"icon\" {...props}>\r\n        <SidebarHeader>\r\n          <SidebarMenu>\r\n            <SidebarMenuItem>\r\n              <SidebarMenuButton\r\n                asChild\r\n                className=\"data-[slot=sidebar-menu-button]:!p-1.5\"\r\n              >\r\n                <Link href=\"/dashboard\" className=\"flex items-center\">\r\n                  <img src={siteLogo} alt={siteName} className=\"h-10 w-[160px] min-w-[40px] object-contain object-left\" />\r\n                </Link>\r\n              </SidebarMenuButton>\r\n            </SidebarMenuItem>\r\n          </SidebarMenu>\r\n        </SidebarHeader>\r\n        <SidebarContent className=\"[&_.tabler-icon]:w-5 [&_.tabler-icon]:h-5 [&_.tabler-icon]:min-w-[20px] [&_.tabler-icon]:min-h-[20px]\">\r\n          <NavMain items={userNavData.navMain} />\r\n          <NavSecondary items={data.navSecondary} className=\"mt-auto\" />\r\n        </SidebarContent>\r\n        <SidebarFooter>\r\n          <NavUser />\r\n        </SidebarFooter>\r\n      </Sidebar>\r\n    );\r\n  }\r\n\r\n  const isAdmin = user.roles?.includes(\"ADMIN\");\r\n  const isAgent = user.roles?.includes(\"AGENT\");\r\n\r\n  // Chọn menu dựa trên vai trò người dùng\r\n  const navData = isAdmin ? adminNavData : isAgent ? agentNavData : userNavData;\r\n\r\n  return (\r\n    <Sidebar collapsible=\"icon\" {...props}>\r\n      <SidebarHeader>\r\n        <SidebarMenu>\r\n          <SidebarMenuItem>\r\n            <SidebarMenuButton\r\n              asChild\r\n              className=\"data-[slot=sidebar-menu-button]:!p-1.5\"\r\n            >\r\n              <Link href={isAdmin ? \"/admin/dashboard\" : isAgent ? \"/agent/dashboard\" : \"/dashboard\"} \r\n                className=\"flex items-center\">\r\n                <img src={siteLogo} alt={siteName} className=\"h-10 w-[160px] min-w-[40px] object-contain object-left\" />\r\n              </Link>\r\n            </SidebarMenuButton>\r\n          </SidebarMenuItem>\r\n        </SidebarMenu>\r\n      </SidebarHeader>\r\n      <SidebarContent className=\"[&_.tabler-icon]:w-5 [&_.tabler-icon]:h-5 [&_.tabler-icon]:min-w-[20px] [&_.tabler-icon]:min-h-[20px]\">\r\n        <NavMain items={navData.navMain} />\r\n        <NavSecondary items={data.navSecondary} className=\"mt-auto\" />\r\n      </SidebarContent>\r\n      <SidebarFooter>\r\n        <NavUser />\r\n      </SidebarFooter>\r\n    </Sidebar>\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAuCA;AACA;AAEA;AACA;AACA;AACA;AAUA;AA1DA;;;;;;;;;;;AA4DA,wBAAwB;AAExB,MAAM,cAAc;IAClB,SAAS;QACP;YACE,KAAK;YACL,OAAO;YACP,KAAK;YACL,qBAAqB;YACrB,MAAM,8TAAA,CAAA,gBAAa;QACrB;QACA,IAAI;QACJ,+BAA+B;QAC/B,qBAAqB;QACrB,6BAA6B;QAC7B,KAAK;QACL;YACE,MAAM;YACN,OAAO;QACT;QACA;YACE,KAAK;YACL,OAAO;YACP,KAAK;YACL,MAAM,kUAAA,CAAA,kBAAe;QACvB;QACA;YACE,KAAK;YACL,OAAO;YACP,KAAK;YACL,MAAM,0TAAA,CAAA,cAAW;QACnB;QACA;YACE,KAAK;YACL,OAAO;YACP,KAAK;YACL,MAAM,oTAAA,CAAA,WAAQ;QAChB;QACA;YACE,MAAM;YACN,OAAO;QACT;QACA;YACE,KAAK;YACL,OAAO;YACP,KAAK;YACL,MAAM,sUAAA,CAAA,oBAAiB;QACzB;QACA;YACE,KAAK;YACL,OAAO;YACP,KAAK;YACL,MAAM,oUAAA,CAAA,mBAAgB;QACxB;QACA;YACE,MAAM;YACN,OAAO;QACT;QACA;YACE,KAAK;YACL,OAAO;YACP,KAAK;YACL,MAAM,wUAAA,CAAA,qBAAkB;QAC1B;QACA;YACE,MAAM;YACN,OAAO;QACT;QACA;YACE,KAAK;YACL,OAAO;YACP,KAAK;YACL,MAAM,oTAAA,CAAA,WAAQ;QAChB;QACA;YACE,KAAK;YACL,OAAO;YACP,KAAK;YACL,MAAM,8TAAA,CAAA,gBAAa;QACrB;KACD;AACH;AAEA,yBAAyB;AACzB,MAAM,eAAe;IACnB,SAAS;QACP;YACE,KAAK;YACL,OAAO;YACP,KAAK;YACL,MAAM,8TAAA,CAAA,gBAAa;QACrB;QACA;YACE,KAAK;YACL,OAAO;YACP,KAAK;YACL,MAAM,0UAAA,CAAA,sBAAmB;QAC3B;QACA;YACE,MAAM;YACN,OAAO;QACT;QACA;YACE,KAAK;YACL,OAAO;YACP,KAAK;YACL,MAAM,4TAAA,CAAA,eAAY;QACpB;QACA;YACE,KAAK;YACL,OAAO;YACP,KAAK;YACL,MAAM,4TAAA,CAAA,eAAY;QACpB;QACA;YACE,KAAK;YACL,OAAO;YACP,KAAK;YACL,MAAM,0TAAA,CAAA,cAAW;QACnB;QACA;YACE,KAAK;YACL,OAAO;YACP,KAAK;YACL,MAAM,4TAAA,CAAA,eAAY;QACpB;QACA;YACE,MAAM;YACN,OAAO;QACT;QACA;YACE,KAAK;YACL,OAAO;YACP,KAAK;YACL,MAAM,oUAAA,CAAA,mBAAgB;QACxB;QACA;YACE,KAAK;YACL,OAAO;YACP,KAAK;YACL,MAAM,sUAAA,CAAA,oBAAiB;QACzB;QACA;YACE,KAAK;YACL,OAAO;YACP,KAAK;YACL,MAAM,0TAAA,CAAA,cAAW;QACnB;QACA;YACE,MAAM;YACN,OAAO;QACT;QACA;YACE,KAAK;YACL,OAAO;YACP,KAAK;YACL,MAAM,wTAAA,CAAA,aAAU;QAClB;QACA;YACE,KAAK;YACL,OAAO;YACP,KAAK;YACL,MAAM,wUAAA,CAAA,qBAAkB;QAC1B;QACA;YACE,KAAK;YACL,OAAO;YACP,KAAK;YACL,MAAM,gUAAA,CAAA,iBAAc;QACtB;QACA;YACE,KAAK;YACL,OAAO;YACP,KAAK;YACL,MAAM,oUAAA,CAAA,mBAAgB;QACxB;QACA;YACE,MAAM;YACN,OAAO;QACT;QACA;YACE,KAAK;YACL,OAAO;YACP,KAAK;YACL,MAAM,oTAAA,CAAA,WAAQ;QAChB;QACA;YACE,KAAK;YACL,OAAO;YACP,KAAK;YACL,MAAM,0TAAA,CAAA,cAAW;QACnB;QACA;YACE,MAAM;YACN,OAAO;QACT;QACA;YACE,KAAK;YACL,OAAO;YACP,KAAK;YACL,MAAM,4TAAA,CAAA,eAAY;QACpB;QACA;YACE,KAAK;YACL,OAAO;YACP,KAAK;YACL,MAAM,oTAAA,CAAA,WAAQ;QAChB;QACA;YACE,MAAM;YACN,OAAO;QACT;QACA;YACE,KAAK;YACL,OAAO;YACP,KAAK;YACL,MAAM,4TAAA,CAAA,eAAY;QACpB;QACA;YACE,KAAK;YACL,OAAO;YACP,KAAK;YACL,MAAM,oTAAA,CAAA,WAAQ;QAChB;QACA;YACE,KAAK;YACL,OAAO;YACP,KAAK;YACL,MAAM,oTAAA,CAAA,WAAQ;QAChB;QACA;YACE,KAAK;YACL,OAAO;YACP,KAAK;YACL,MAAM,oUAAA,CAAA,mBAAgB;QACxB;QACA;YACE,KAAK;YACL,OAAO;YACP,KAAK;YACL,MAAM,sTAAA,CAAA,YAAS;QACjB;QACA;YACE,KAAK;YACL,OAAO;YACP,KAAK;YACL,MAAM,sUAAA,CAAA,oBAAiB;QACzB;QACA;YACE,KAAK;YACL,OAAO;YACP,KAAK;YACL,MAAM,4TAAA,CAAA,eAAY;QACpB;QACA;YACE,KAAK;YACL,OAAO;YACP,KAAK;YACL,MAAM,wUAAA,CAAA,qBAAkB;QAC1B;QACA;YACE,MAAM;YACN,OAAO;QACT;QACA;YACE,KAAK;YACL,OAAO;YACP,KAAK;YACL,MAAM,0TAAA,CAAA,cAAW;QACnB;QACA;YACE,KAAK;YACL,OAAO;YACP,KAAK;YACL,MAAM,gUAAA,CAAA,iBAAc;QACtB;QACA;YACE,KAAK;YACL,OAAO;YACP,KAAK;YACL,MAAM,8TAAA,CAAA,gBAAa;QACrB;QACA;YACE,MAAM;YACN,OAAO;QACT;QACA;YACE,KAAK;YACL,OAAO;YACP,KAAK;YACL,MAAM,gUAAA,CAAA,iBAAc;QACtB;QACA;YACE,KAAK;YACL,OAAO;YACP,KAAK;YACL,MAAM,4TAAA,CAAA,eAAY;QACpB;QACA;YACE,KAAK;YACL,OAAO;YACP,KAAK;YACL,MAAM,sTAAA,CAAA,YAAS;QACjB;KACD;AACH;AAEA,yBAAyB;AACzB,MAAM,eAAe;IACnB,SAAS;QACP;YACE,KAAK;YACL,OAAO;YACP,KAAK;YACL,MAAM,8TAAA,CAAA,gBAAa;QACrB;QACA;YACE,KAAK;YACL,OAAO;YACP,KAAK;YACL,MAAM,0UAAA,CAAA,sBAAmB;QAC3B;QACA;YACE,MAAM;YACN,OAAO;QACT;QACA;YACE,KAAK;YACL,OAAO;YACP,KAAK;YACL,MAAM,4TAAA,CAAA,eAAY;QACpB;QACA;YACE,KAAK;YACL,OAAO;YACP,KAAK;YACL,MAAM,4TAAA,CAAA,eAAY;QACpB;QACA,IAAI;QACJ,+BAA+B;QAC/B,0BAA0B;QAC1B,uBAAuB;QACvB,KAAK;QACL,IAAI;QACJ,gCAAgC;QAChC,oCAAoC;QACpC,wBAAwB;QACxB,KAAK;QACL;YACE,MAAM;YACN,OAAO;QACT;QACA;YACE,KAAK;YACL,OAAO;YACP,KAAK;YACL,MAAM,oUAAA,CAAA,mBAAgB;QACxB;QACA,IAAI;QACJ,+BAA+B;QAC/B,iCAAiC;QACjC,6BAA6B;QAC7B,KAAK;QACL,IAAI;QACJ,gCAAgC;QAChC,2CAA2C;QAC3C,uBAAuB;QACvB,KAAK;QACL;YACE,MAAM;YACN,OAAO;QACT;QACA;YACE,KAAK;YACL,OAAO;YACP,KAAK;YACL,MAAM,wTAAA,CAAA,aAAU;QAClB;QACA;YACE,KAAK;YACL,OAAO;YACP,KAAK;YACL,MAAM,wUAAA,CAAA,qBAAkB;QAC1B;QACA;YACE,KAAK;YACL,OAAO;YACP,KAAK;YACL,MAAM,gUAAA,CAAA,iBAAc;QACtB;QACA;YACE,MAAM;YACN,OAAO;QACT;QACA;YACE,KAAK;YACL,OAAO;YACP,KAAK;YACL,MAAM,oTAAA,CAAA,WAAQ;QAChB;QACA;YACE,KAAK;YACL,OAAO;YACP,KAAK;YACL,MAAM,0TAAA,CAAA,cAAW;QACnB;QACA;YACE,MAAM;YACN,OAAO;QACT;QACA;YACE,KAAK;YACL,OAAO;YACP,KAAK;YACL,MAAM,4TAAA,CAAA,eAAY;QACpB;QACA;YACE,KAAK;YACL,OAAO;YACP,KAAK;YACL,MAAM,oTAAA,CAAA,WAAQ;QAChB;KAoBD;AACH;AAEA,MAAM,OAAO;IACX,WAAW;QACT;YACE,OAAO;YACP,MAAM,sUAAA,CAAA,oBAAiB;YACvB,UAAU;YACV,KAAK;YACL,OAAO;gBACL;oBACE,OAAO;oBACP,KAAK;gBACP;gBACA;oBACE,OAAO;oBACP,KAAK;gBACP;aACD;QACH;QACA;YACE,OAAO;YACP,MAAM,0UAAA,CAAA,sBAAmB;YACzB,KAAK;YACL,OAAO;gBACL;oBACE,OAAO;oBACP,KAAK;gBACP;gBACA;oBACE,OAAO;oBACP,KAAK;gBACP;aACD;QACH;QACA;YACE,OAAO;YACP,MAAM,sTAAA,CAAA,YAAS;YACf,KAAK;YACL,OAAO;gBACL;oBACE,OAAO;oBACP,KAAK;gBACP;gBACA;oBACE,OAAO;oBACP,KAAK;gBACP;aACD;QACH;KACD;IACD,cAAc,EACb;AACH;AAEO,SAAS,WAAW,EAAE,GAAG,OAA6C;IAC3E,MAAM,EAAE,IAAI,EAAE,SAAS,EAAE,GAAG,CAAA,GAAA,qHAAA,CAAA,UAAO,AAAD;IAClC,MAAM,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,gIAAA,CAAA,kBAAe,AAAD;IACjC,MAAM,WAAW,QAAQ,CAAC,YAAY;IACtC,MAAM,WAAW,QAAQ,CAAC,YAAY;IAEtC,yDAAyD;IACzD,MAAM,sBAAsB,CAAA,GAAA,8SAAA,CAAA,SAAY,AAAD,EAAE;IACzC,MAAM,mBAAmB,CAAA,GAAA,8SAAA,CAAA,SAAY,AAAD,EAAO;IAE3C,kCAAkC;IAClC,IAAI,QAAQ,KAAK,KAAK,EAAE;QACtB,MAAM,UAAU,KAAK,KAAK,CAAC,QAAQ,CAAC;QACpC,MAAM,UAAU,KAAK,KAAK,CAAC,QAAQ,CAAC;QACpC,oBAAoB,OAAO,GAAG,UAAU,eAAe,UAAU,eAAe;QAChF,iBAAiB,OAAO,GAAG;IAC7B;IAEA,kFAAkF;IAClF,IAAI,aAAa,CAAC,MAAM;QACtB,qBACE,uVAAC,4HAAA,CAAA,UAAO;YAAC,aAAY;YAAQ,GAAG,KAAK;YAAE,WAAU;;8BAC/C,uVAAC,4HAAA,CAAA,gBAAa;8BACZ,cAAA,uVAAC,4HAAA,CAAA,cAAW;kCACV,cAAA,uVAAC,4HAAA,CAAA,kBAAe;sCACd,cAAA,uVAAC,4HAAA,CAAA,oBAAiB;gCAChB,OAAO;gCACP,WAAU;0CAEV,cAAA,uVAAC;oCAAI,WAAU;8CACb,cAAA,uVAAC;wCAAI,KAAK;wCAAU,KAAK;wCAAU,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAMvD,uVAAC,4HAAA,CAAA,iBAAc;oBAAC,WAAU;;sCAExB,uVAAC,0HAAA,CAAA,UAAO;4BAAC,OAAO,oBAAoB,OAAO,CAAC,OAAO;;;;;;sCACnD,uVAAC,+HAAA,CAAA,eAAY;4BAAC,OAAO,KAAK,YAAY;4BAAE,WAAU;;;;;;;;;;;;8BAEpD,uVAAC,4HAAA,CAAA,gBAAa;8BACZ,cAAA,uVAAC,0HAAA,CAAA,UAAO;;;;;;;;;;;;;;;;IAIhB;IAEA,gDAAgD;IAChD,IAAI,CAAC,MAAM;QACT,qBACE,uVAAC,4HAAA,CAAA,UAAO;YAAC,aAAY;YAAQ,GAAG,KAAK;;8BACnC,uVAAC,4HAAA,CAAA,gBAAa;8BACZ,cAAA,uVAAC,4HAAA,CAAA,cAAW;kCACV,cAAA,uVAAC,4HAAA,CAAA,kBAAe;sCACd,cAAA,uVAAC,4HAAA,CAAA,oBAAiB;gCAChB,OAAO;gCACP,WAAU;0CAEV,cAAA,uVAAC,qQAAA,CAAA,UAAI;oCAAC,MAAK;oCAAa,WAAU;8CAChC,cAAA,uVAAC;wCAAI,KAAK;wCAAU,KAAK;wCAAU,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAMvD,uVAAC,4HAAA,CAAA,iBAAc;oBAAC,WAAU;;sCACxB,uVAAC,0HAAA,CAAA,UAAO;4BAAC,OAAO,YAAY,OAAO;;;;;;sCACnC,uVAAC,+HAAA,CAAA,eAAY;4BAAC,OAAO,KAAK,YAAY;4BAAE,WAAU;;;;;;;;;;;;8BAEpD,uVAAC,4HAAA,CAAA,gBAAa;8BACZ,cAAA,uVAAC,0HAAA,CAAA,UAAO;;;;;;;;;;;;;;;;IAIhB;IAEA,MAAM,UAAU,KAAK,KAAK,EAAE,SAAS;IACrC,MAAM,UAAU,KAAK,KAAK,EAAE,SAAS;IAErC,wCAAwC;IACxC,MAAM,UAAU,UAAU,eAAe,UAAU,eAAe;IAElE,qBACE,uVAAC,4HAAA,CAAA,UAAO;QAAC,aAAY;QAAQ,GAAG,KAAK;;0BACnC,uVAAC,4HAAA,CAAA,gBAAa;0BACZ,cAAA,uVAAC,4HAAA,CAAA,cAAW;8BACV,cAAA,uVAAC,4HAAA,CAAA,kBAAe;kCACd,cAAA,uVAAC,4HAAA,CAAA,oBAAiB;4BAChB,OAAO;4BACP,WAAU;sCAEV,cAAA,uVAAC,qQAAA,CAAA,UAAI;gCAAC,MAAM,UAAU,qBAAqB,UAAU,qBAAqB;gCACxE,WAAU;0CACV,cAAA,uVAAC;oCAAI,KAAK;oCAAU,KAAK;oCAAU,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAMvD,uVAAC,4HAAA,CAAA,iBAAc;gBAAC,WAAU;;kCACxB,uVAAC,0HAAA,CAAA,UAAO;wBAAC,OAAO,QAAQ,OAAO;;;;;;kCAC/B,uVAAC,+HAAA,CAAA,eAAY;wBAAC,OAAO,KAAK,YAAY;wBAAE,WAAU;;;;;;;;;;;;0BAEpD,uVAAC,4HAAA,CAAA,gBAAa;0BACZ,cAAA,uVAAC,0HAAA,CAAA,UAAO;;;;;;;;;;;;;;;;AAIhB", "debugId": null}}, {"offset": {"line": 4405, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/bloomgoo/gold-exchange-platform/frontend/components/layout/main-layout.tsx"], "sourcesContent": ["import { AppSidebar } from '@/components/app-sidebar';\r\nimport { SidebarProvider } from '@/components/ui/sidebar';\r\nimport { cn } from '@/lib/utils';\r\n\r\ninterface MainLayoutProps {\r\n   children: React.ReactNode;\r\n   header: React.ReactNode;\r\n   headersNumber?: 1 | 2;\r\n}\r\n\r\nexport default function MainLayout({ children, header, headersNumber = 2 }: MainLayoutProps) {\r\n   const height = {\r\n      1: 'h-[calc(100svh-40px)] lg:h-[calc(100svh-56px)]',\r\n      2: 'h-[calc(100svh-80px)] lg:h-[calc(100svh-96px)]',\r\n   };\r\n   return (\r\n      <SidebarProvider>\r\n         <AppSidebar variant=\"inset\" />\r\n         <div className=\"h-svh overflow-hidden lg:p-2 w-full\">\r\n            <div className=\"lg:border lg:rounded-md overflow-hidden flex flex-col items-center justify-start bg-container h-full w-full\">\r\n               {header}\r\n               <div\r\n                  className={cn(\r\n                     'overflow-auto w-full',\r\n                     height[headersNumber as keyof typeof height]\r\n                  )}\r\n               >\r\n                  {children}\r\n               </div>\r\n            </div>\r\n         </div>\r\n      </SidebarProvider>\r\n   );\r\n}\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AAAA;;;;;AAQe,SAAS,WAAW,EAAE,QAAQ,EAAE,MAAM,EAAE,gBAAgB,CAAC,EAAmB;IACxF,MAAM,SAAS;QACZ,GAAG;QACH,GAAG;IACN;IACA,qBACG,uVAAC,4HAAA,CAAA,kBAAe;;0BACb,uVAAC,6HAAA,CAAA,aAAU;gBAAC,SAAQ;;;;;;0BACpB,uVAAC;gBAAI,WAAU;0BACZ,cAAA,uVAAC;oBAAI,WAAU;;wBACX;sCACD,uVAAC;4BACE,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EACT,wBACA,MAAM,CAAC,cAAqC;sCAG9C;;;;;;;;;;;;;;;;;;;;;;;AAMnB", "debugId": null}}, {"offset": {"line": 4469, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/bloomgoo/gold-exchange-platform/frontend/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\r\nimport { Slot } from \"@radix-ui/react-slot\"\r\nimport { cva, type VariantProps } from \"class-variance-authority\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst badgeVariants = cva(\r\n  \"inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden\",\r\n  {\r\n    variants: {\r\n      variant: {\r\n        default:\r\n          \"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90\",\r\n        secondary:\r\n          \"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90\",\r\n        destructive:\r\n          \"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\r\n        outline:\r\n          \"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground\",\r\n      },\r\n    },\r\n    defaultVariants: {\r\n      variant: \"default\",\r\n    },\r\n  }\r\n)\r\n\r\nfunction Badge({\r\n  className,\r\n  variant,\r\n  asChild = false,\r\n  ...props\r\n}: React.ComponentProps<\"span\"> &\r\n  VariantProps<typeof badgeVariants> & { asChild?: boolean }) {\r\n  const Comp = asChild ? Slot : \"span\"\r\n\r\n  return (\r\n    <Comp\r\n      data-slot=\"badge\"\r\n      className={cn(badgeVariants({ variant }), className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport { Badge, badgeVariants }\r\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;AAAA;;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,2OAAA,CAAA,MAAG,AAAD,EACtB,kZACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,SAAS,MAAM,EACb,SAAS,EACT,OAAO,EACP,UAAU,KAAK,EACf,GAAG,OAEuD;IAC1D,MAAM,OAAO,UAAU,oSAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,uVAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 4516, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/bloomgoo/gold-exchange-platform/frontend/components/common/SilverPriceIndicator.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { usePolygonForex } from '@/services/websocket/polygon-forex-socket.service';\r\nimport { Badge } from '@/components/ui/badge';\r\n\r\nexport function SilverPriceIndicator() {\r\n  const {\r\n    currentQuote,\r\n    isMockData\r\n  } = usePolygonForex();\r\n\r\n  // Format giá\r\n  const formatPrice = (price?: number) => {\r\n    if (price === undefined || price === null) return '---.----';\r\n    return price.toFixed(4);\r\n  };\r\n\r\n  // Format giá VND\r\n  const formatVND = (price?: number) => {\r\n    if (price === undefined || price === null) return '---,---';\r\n    return price.toLocaleString('vi-VN');\r\n  };\r\n\r\n  // Xác định màu sắc cho chênh lệch giá\r\n  const getSpreadColor = (spread?: number) => {\r\n    if (spread === undefined || spread === null) return 'bg-gray-500';\r\n    if (spread > 0.1) return 'bg-red-500';\r\n    if (spread < 0.05) return 'bg-green-500';\r\n    return 'bg-yellow-500';\r\n  };\r\n\r\n  return (\r\n    <div className=\"flex items-center gap-2\">\r\n      <Badge variant=\"outline\" className=\"flex items-center gap-1 py-1\">\r\n        <span className=\"text-xs font-medium\">AXGUSD:</span>\r\n        <span className=\"text-xs font-bold\">{formatPrice(currentQuote?.bidPrice)}</span>\r\n        {currentQuote?.spread !== undefined && (\r\n          <span className={`inline-block w-2 h-2 rounded-full ${getSpreadColor(currentQuote?.spread)}`} />\r\n        )}\r\n      </Badge>\r\n      \r\n      {isMockData && (\r\n        <Badge variant=\"outline\" className=\"bg-yellow-50 text-yellow-700 border-yellow-200 text-xs\">\r\n          Mẫu\r\n        </Badge>\r\n      )}\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAKO,SAAS;IACd,MAAM,EACJ,YAAY,EACZ,UAAU,EACX,GAAG,CAAA,GAAA,8JAAA,CAAA,kBAAe,AAAD;IAElB,aAAa;IACb,MAAM,cAAc,CAAC;QACnB,IAAI,UAAU,aAAa,UAAU,MAAM,OAAO;QAClD,OAAO,MAAM,OAAO,CAAC;IACvB;IAEA,iBAAiB;IACjB,MAAM,YAAY,CAAC;QACjB,IAAI,UAAU,aAAa,UAAU,MAAM,OAAO;QAClD,OAAO,MAAM,cAAc,CAAC;IAC9B;IAEA,sCAAsC;IACtC,MAAM,iBAAiB,CAAC;QACtB,IAAI,WAAW,aAAa,WAAW,MAAM,OAAO;QACpD,IAAI,SAAS,KAAK,OAAO;QACzB,IAAI,SAAS,MAAM,OAAO;QAC1B,OAAO;IACT;IAEA,qBACE,uVAAC;QAAI,WAAU;;0BACb,uVAAC,0HAAA,CAAA,QAAK;gBAAC,SAAQ;gBAAU,WAAU;;kCACjC,uVAAC;wBAAK,WAAU;kCAAsB;;;;;;kCACtC,uVAAC;wBAAK,WAAU;kCAAqB,YAAY,cAAc;;;;;;oBAC9D,cAAc,WAAW,2BACxB,uVAAC;wBAAK,WAAW,CAAC,kCAAkC,EAAE,eAAe,cAAc,SAAS;;;;;;;;;;;;YAI/F,4BACC,uVAAC,0HAAA,CAAA,QAAK;gBAAC,SAAQ;gBAAU,WAAU;0BAAyD;;;;;;;;;;;;AAMpG", "debugId": null}}, {"offset": {"line": 4603, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/bloomgoo/gold-exchange-platform/frontend/components/layout/headers/page-header.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React from \"react\";\r\nimport { cn } from \"@/lib/utils\";\r\nimport { SidebarTrigger } from \"@/components/ui/sidebar\";\r\nimport { SilverPriceIndicator } from \"@/components/common/SilverPriceIndicator\";\r\n\r\ninterface PageHeaderProps {\r\n  title: string;\r\n  description?: string;\r\n  actions?: React.ReactNode;\r\n  className?: string;\r\n}\r\n\r\nexport function PageHeader({ title, description, actions, className }: PageHeaderProps) {\r\n  return (\r\n    <div className={cn(\"flex items-center justify-between gap-2 p-4 border-b w-full\", className)}>\r\n      {/* Left: SidebarTrigger and Title/Description */}\r\n      <div className=\"flex items-center gap-3 overflow-hidden\">\r\n        <SidebarTrigger className=\"-ml-1 flex-shrink-0\" />\r\n        <div className=\"flex flex-col gap-1 overflow-hidden\">\r\n          <h1 className=\"text-xl font-semibold truncate\">{title}</h1>\r\n          {description && <p className=\"text-sm text-muted-foreground truncate\">{description}</p>}\r\n        </div>\r\n      </div>\r\n\r\n      {/* Right: Price Indicator and Actions */}\r\n      <div className=\"flex items-center gap-2 ml-4\">\r\n        <SilverPriceIndicator />\r\n        {actions && (\r\n          <div className=\"flex items-center gap-2 ml-2\">\r\n            {actions}\r\n          </div>\r\n        )}\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAGA;AAAA;AACA;AACA;AALA;;;;;AAcO,SAAS,WAAW,EAAE,KAAK,EAAE,WAAW,EAAE,OAAO,EAAE,SAAS,EAAmB;IACpF,qBACE,uVAAC;QAAI,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EAAE,+DAA+D;;0BAEhF,uVAAC;gBAAI,WAAU;;kCACb,uVAAC,4HAAA,CAAA,iBAAc;wBAAC,WAAU;;;;;;kCAC1B,uVAAC;wBAAI,WAAU;;0CACb,uVAAC;gCAAG,WAAU;0CAAkC;;;;;;4BAC/C,6BAAe,uVAAC;gCAAE,WAAU;0CAA0C;;;;;;;;;;;;;;;;;;0BAK3E,uVAAC;gBAAI,WAAU;;kCACb,uVAAC,6IAAA,CAAA,uBAAoB;;;;;oBACpB,yBACC,uVAAC;wBAAI,WAAU;kCACZ;;;;;;;;;;;;;;;;;;AAMb", "debugId": null}}, {"offset": {"line": 4696, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/bloomgoo/gold-exchange-platform/frontend/components/layout-provider.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React, { createContext, useContext, ReactNode } from \"react\";\r\nimport { useAuth } from \"@/hooks/use-auth\";\r\nimport { AppLayout } from \"@/components/app-layout\";\r\nimport MainLayout from \"@/components/layout/main-layout\";\r\nimport { PageHeader } from \"@/components/layout/headers/page-header\";\r\n\r\ntype LayoutContextType = {\r\n  isAdmin: boolean;\r\n};\r\n\r\nconst LayoutContext = createContext<LayoutContextType | undefined>(undefined);\r\n\r\nexport function useLayout() {\r\n  const context = useContext(LayoutContext);\r\n  if (context === undefined) {\r\n    throw new Error(\"useLayout must be used within a LayoutProvider\");\r\n  }\r\n  return context;\r\n}\r\n\r\ninterface LayoutProviderProps {\r\n  children: ReactNode;\r\n  header?: ReactNode;\r\n  title?: string;\r\n  description?: string;\r\n  actions?: ReactNode;\r\n}\r\n\r\nexport function LayoutProvider({\r\n  children,\r\n  header,\r\n  title,\r\n  description,\r\n  actions,\r\n}: LayoutProviderProps) {\r\n  const { user } = useAuth();\r\n  const isAdmin = user?.roles.includes(\"ADMIN\");\r\n\r\n  // Nếu là admin, sử dụng layout mới\r\n  if (isAdmin) {\r\n    return (\r\n      <LayoutContext.Provider value={{ isAdmin }}>\r\n        <MainLayout\r\n          header={\r\n            header || (\r\n              <PageHeader\r\n                title={title || \"Dashboard\"}\r\n                description={description}\r\n                actions={actions}\r\n              />\r\n            )\r\n          }\r\n        >\r\n          {children}\r\n        </MainLayout>\r\n      </LayoutContext.Provider>\r\n    );\r\n  }\r\n\r\n  // Nếu là user thông thường, sử dụng layout cũ\r\n  return (\r\n    <LayoutContext.Provider value={{ isAdmin: isAdmin ?? false }}>\r\n      <MainLayout\r\n          header={\r\n            header || (\r\n              <PageHeader\r\n                title={title || \"Dashboard\"}\r\n                description={description}\r\n                actions={actions}\r\n              />\r\n            )\r\n          }\r\n        >\r\n          {children}\r\n        </MainLayout>\r\n    </LayoutContext.Provider>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AAEA;AACA;AANA;;;;;;AAYA,MAAM,8BAAgB,CAAA,GAAA,8SAAA,CAAA,gBAAa,AAAD,EAAiC;AAE5D,SAAS;IACd,MAAM,UAAU,CAAA,GAAA,8SAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,YAAY,WAAW;QACzB,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT;AAUO,SAAS,eAAe,EAC7B,QAAQ,EACR,MAAM,EACN,KAAK,EACL,WAAW,EACX,OAAO,EACa;IACpB,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,qHAAA,CAAA,UAAO,AAAD;IACvB,MAAM,UAAU,MAAM,MAAM,SAAS;IAErC,mCAAmC;IACnC,IAAI,SAAS;QACX,qBACE,uVAAC,cAAc,QAAQ;YAAC,OAAO;gBAAE;YAAQ;sBACvC,cAAA,uVAAC,uIAAA,CAAA,UAAU;gBACT,QACE,wBACE,uVAAC,kJAAA,CAAA,aAAU;oBACT,OAAO,SAAS;oBAChB,aAAa;oBACb,SAAS;;;;;;0BAKd;;;;;;;;;;;IAIT;IAEA,8CAA8C;IAC9C,qBACE,uVAAC,cAAc,QAAQ;QAAC,OAAO;YAAE,SAAS,WAAW;QAAM;kBACzD,cAAA,uVAAC,uIAAA,CAAA,UAAU;YACP,QACE,wBACE,uVAAC,kJAAA,CAAA,aAAU;gBACT,OAAO,SAAS;gBAChB,aAAa;gBACb,SAAS;;;;;;sBAKd;;;;;;;;;;;AAIX", "debugId": null}}, {"offset": {"line": 4783, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/bloomgoo/gold-exchange-platform/frontend/components/common/data-table/data-table.tsx"], "sourcesContent": ["\"use client\"\r\n\r\n/**\r\n * Component DataTable cung cấp bảng dữ liệu với các tính năng:\r\n * - Header cố định khi cuộn\r\n * - Cột cố định (sticky columns) ở bên trái và bên phải\r\n * - Hỗ trợ đầy đủ các tính năng của TanStack Table\r\n *\r\n * Để sử dụng sticky columns, hãy thêm thuộc tính meta vào định nghĩa cột:\r\n * ```\r\n * {\r\n *   id: 'actions',\r\n *   meta: {\r\n *     isSticky: true,\r\n *     position: 'right' // hoặc 'left'\r\n *   }\r\n * }\r\n * ```\r\n *\r\n * Xem thêm hướng dẫn chi tiết trong file README.md\r\n */\r\n\r\nimport { flexRender, Table as TableType } from \"@tanstack/react-table\";\r\n\r\nimport { cn } from \"@/lib/utils\";\r\n\r\n// Định nghĩa kiểu dữ liệu cho thuộc tính meta của cột\r\ndeclare module '@tanstack/react-table' {\r\n  interface ColumnMeta<TData, TValue> {\r\n    isSticky?: boolean;\r\n    position?: 'left' | 'right';\r\n    header?: string;\r\n  }\r\n}\r\n\r\ninterface DataTableProps<TData> {\r\n    table: TableType<TData>\r\n    className?: string\r\n    isLoading?: boolean\r\n}\r\n\r\nexport function DataTable<TData>({\r\n    table,\r\n    className,\r\n    isLoading = false,\r\n}: DataTableProps<TData>) {\r\n    return (\r\n        <div className={cn(\"w-full flex flex-col h-full\", className)}>\r\n            {/* Table Container with border */}\r\n            <div className=\"border flex flex-col flex-1 overflow-hidden\">\r\n                {/* Single table with sticky header */}\r\n                <div className=\"table-scroll-container\">\r\n                    <div className=\"table-viewport\">\r\n                        <table className=\"data-table\">\r\n                            <thead className=\"sticky-header\">\r\n                                {table.getHeaderGroups().map((headerGroup) => (\r\n                                    <tr key={headerGroup.id}>\r\n                                        {headerGroup.headers.map((header, index) => {\r\n                                            // Determine if this column has sticky metadata\r\n                                            const hasSticky = header.column.columnDef.meta?.isSticky;\r\n\r\n                                            return (\r\n                                                <th\r\n                                                    key={header.id}\r\n                                                    style={{\r\n                                                        width: header.getSize(),\r\n                                                        minWidth: header.getSize(),\r\n                                                        maxWidth: header.getSize(),\r\n                                                        ...(hasSticky && {\r\n                                                            position: 'sticky',\r\n                                                            [header.column.columnDef.meta?.position === 'right' ? 'right' : 'left']: 0,\r\n                                                            zIndex: 60,\r\n                                                            boxShadow: header.column.columnDef.meta?.position === 'right'\r\n                                                                ? '-5px 0 5px -5px rgba(0,0,0,0.1)'\r\n                                                                : '5px 0 5px -5px rgba(0,0,0,0.1)'\r\n                                                        }),\r\n                                                        // Không cần logic fallback cho first column nữa\r\n                                                        // Tất cả sticky columns phải được định nghĩa qua meta\r\n                                                    }}\r\n                                                    className={cn(\r\n                                                        \"bg-background py-2 text-left align-middle font-medium text-muted-foreground\",\r\n                                                        hasSticky && `sticky-${header.column.columnDef.meta?.position || 'left'}`,\r\n                                                        // Thêm class dựa trên column ID thay vì index\r\n                                                        header.column.id === 'select' && \"checkbox-header\"\r\n                                                    )}\r\n                                                >\r\n                                                    {header.isPlaceholder\r\n                                                        ? null\r\n                                                        : flexRender(\r\n                                                            header.column.columnDef.header,\r\n                                                            header.getContext()\r\n                                                        )}\r\n                                                </th>\r\n                                            )\r\n                                        })}\r\n                                    </tr>\r\n                                ))}\r\n                            </thead>\r\n                            <tbody>\r\n                                {isLoading ? (\r\n                                    <tr>\r\n                                        <td\r\n                                            colSpan={table.getAllColumns().length}\r\n                                            className=\"h-24 text-center\"\r\n                                        >\r\n                                            Đang tải dữ liệu...\r\n                                        </td>\r\n                                    </tr>\r\n                                ) : table.getRowModel().rows?.length ? (\r\n                                    table.getRowModel().rows.map((row) => (\r\n                                        <tr\r\n                                            key={row.id}\r\n                                            data-state={row.getIsSelected() && \"selected\"}\r\n                                            className=\"border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted\"\r\n                                        >\r\n                                            {row.getVisibleCells().map((cell, index) => {\r\n                                                // Determine if this column has sticky metadata\r\n                                                const hasSticky = cell.column.columnDef.meta?.isSticky;\r\n\r\n                                                return (\r\n                                                    <td\r\n                                                        key={cell.id}\r\n                                                        style={{\r\n                                                            width: cell.column.getSize(),\r\n                                                            minWidth: cell.column.getSize(),\r\n                                                            maxWidth: cell.column.getSize(),\r\n                                                            ...(hasSticky && {\r\n                                                                position: 'sticky',\r\n                                                                [cell.column.columnDef.meta?.position === 'right' ? 'right' : 'left']: 0,\r\n                                                                backgroundColor: 'var(--background)',\r\n                                                                zIndex: 30,\r\n                                                                boxShadow: cell.column.columnDef.meta?.position === 'right'\r\n                                                                    ? '-5px 0 5px -5px rgba(0,0,0,0.1)'\r\n                                                                    : '5px 0 5px -5px rgba(0,0,0,0.1)'\r\n                                                            }),\r\n                                                            // Không cần logic fallback cho first column nữa\r\n                                                            // Tất cả sticky columns phải được định nghĩa qua meta\r\n                                                        }}\r\n                                                        className={cn(\r\n                                                            \"py-1 px-2 align-middle\",\r\n                                                            hasSticky && `sticky-${cell.column.columnDef.meta?.position || 'left'}`,\r\n                                                            // Thêm class dựa trên column ID\r\n                                                            cell.column.id === 'select' && \"checkbox-cell\"\r\n                                                        )}\r\n                                                    >\r\n                                                        {flexRender(\r\n                                                            cell.column.columnDef.cell,\r\n                                                            cell.getContext()\r\n                                                        )}\r\n                                                    </td>\r\n                                                )\r\n                                            })}\r\n                                        </tr>\r\n                                    ))\r\n                                ) : (\r\n                                    <tr>\r\n                                        <td\r\n                                            colSpan={table.getAllColumns().length}\r\n                                            className=\"h-24 text-center\"\r\n                                        >\r\n                                            Không có dữ liệu\r\n                                        </td>\r\n                                    </tr>\r\n                                )}\r\n                            </tbody>\r\n                        </table>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n        </div>\r\n    )\r\n}"], "names": [], "mappings": ";;;;AAEA;;;;;;;;;;;;;;;;;;CAkBC,GAED;AAEA;AAAA;AAxBA;;;;AAyCO,SAAS,UAAiB,EAC7B,KAAK,EACL,SAAS,EACT,YAAY,KAAK,EACG;IACpB,qBACI,uVAAC;QAAI,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EAAE,+BAA+B;kBAE9C,cAAA,uVAAC;YAAI,WAAU;sBAEX,cAAA,uVAAC;gBAAI,WAAU;0BACX,cAAA,uVAAC;oBAAI,WAAU;8BACX,cAAA,uVAAC;wBAAM,WAAU;;0CACb,uVAAC;gCAAM,WAAU;0CACZ,MAAM,eAAe,GAAG,GAAG,CAAC,CAAC,4BAC1B,uVAAC;kDACI,YAAY,OAAO,CAAC,GAAG,CAAC,CAAC,QAAQ;4CAC9B,+CAA+C;4CAC/C,MAAM,YAAY,OAAO,MAAM,CAAC,SAAS,CAAC,IAAI,EAAE;4CAEhD,qBACI,uVAAC;gDAEG,OAAO;oDACH,OAAO,OAAO,OAAO;oDACrB,UAAU,OAAO,OAAO;oDACxB,UAAU,OAAO,OAAO;oDACxB,GAAI,aAAa;wDACb,UAAU;wDACV,CAAC,OAAO,MAAM,CAAC,SAAS,CAAC,IAAI,EAAE,aAAa,UAAU,UAAU,OAAO,EAAE;wDACzE,QAAQ;wDACR,WAAW,OAAO,MAAM,CAAC,SAAS,CAAC,IAAI,EAAE,aAAa,UAChD,oCACA;oDACV,CAAC;gDAGL;gDACA,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EACR,+EACA,aAAa,CAAC,OAAO,EAAE,OAAO,MAAM,CAAC,SAAS,CAAC,IAAI,EAAE,YAAY,QAAQ,EACzE,8CAA8C;gDAC9C,OAAO,MAAM,CAAC,EAAE,KAAK,YAAY;0DAGpC,OAAO,aAAa,GACf,OACA,CAAA,GAAA,gSAAA,CAAA,aAAU,AAAD,EACP,OAAO,MAAM,CAAC,SAAS,CAAC,MAAM,EAC9B,OAAO,UAAU;+CA3BpB,OAAO,EAAE;;;;;wCA+B1B;uCAtCK,YAAY,EAAE;;;;;;;;;;0CA0C/B,uVAAC;0CACI,0BACG,uVAAC;8CACG,cAAA,uVAAC;wCACG,SAAS,MAAM,aAAa,GAAG,MAAM;wCACrC,WAAU;kDACb;;;;;;;;;;2CAIL,MAAM,WAAW,GAAG,IAAI,EAAE,SAC1B,MAAM,WAAW,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,oBAC1B,uVAAC;wCAEG,cAAY,IAAI,aAAa,MAAM;wCACnC,WAAU;kDAET,IAAI,eAAe,GAAG,GAAG,CAAC,CAAC,MAAM;4CAC9B,+CAA+C;4CAC/C,MAAM,YAAY,KAAK,MAAM,CAAC,SAAS,CAAC,IAAI,EAAE;4CAE9C,qBACI,uVAAC;gDAEG,OAAO;oDACH,OAAO,KAAK,MAAM,CAAC,OAAO;oDAC1B,UAAU,KAAK,MAAM,CAAC,OAAO;oDAC7B,UAAU,KAAK,MAAM,CAAC,OAAO;oDAC7B,GAAI,aAAa;wDACb,UAAU;wDACV,CAAC,KAAK,MAAM,CAAC,SAAS,CAAC,IAAI,EAAE,aAAa,UAAU,UAAU,OAAO,EAAE;wDACvE,iBAAiB;wDACjB,QAAQ;wDACR,WAAW,KAAK,MAAM,CAAC,SAAS,CAAC,IAAI,EAAE,aAAa,UAC9C,oCACA;oDACV,CAAC;gDAGL;gDACA,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EACR,0BACA,aAAa,CAAC,OAAO,EAAE,KAAK,MAAM,CAAC,SAAS,CAAC,IAAI,EAAE,YAAY,QAAQ,EACvE,gCAAgC;gDAChC,KAAK,MAAM,CAAC,EAAE,KAAK,YAAY;0DAGlC,CAAA,GAAA,gSAAA,CAAA,aAAU,AAAD,EACN,KAAK,MAAM,CAAC,SAAS,CAAC,IAAI,EAC1B,KAAK,UAAU;+CA1Bd,KAAK,EAAE;;;;;wCA8BxB;uCAxCK,IAAI,EAAE;;;;8DA4CnB,uVAAC;8CACG,cAAA,uVAAC;wCACG,SAAS,MAAM,aAAa,GAAG,MAAM;wCACrC,WAAU;kDACb;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAYzC", "debugId": null}}, {"offset": {"line": 4961, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/bloomgoo/gold-exchange-platform/frontend/components/common/admin/transactions/type/transaction.dto.ts"], "sourcesContent": ["import { User } from \"../../users/type/user\";\r\nimport { WalletDto } from \"../../wallets/type/wallet.dto\";\r\n\r\n/**\r\n * Enum đại diện cho loại giao dịch\r\n */\r\nexport enum TransactionType {\r\n  CREDIT = 'CREDIT',  // Tăng số dư (ghi có)\r\n  DEBIT = 'DEBIT',    // Giảm số dư (ghi nợ)\r\n}\r\n\r\n/**\r\n * Enum đại diện cho trạng thái giao dịch\r\n */\r\nexport enum TransactionStatus {\r\n  PENDING = 'PENDING',\r\n  PROCESSING = 'PROCESSING',\r\n  COMPLETED = 'COMPLETED',\r\n  CANCELLED = 'CANCELLED',\r\n  FAILED = 'FAILED',\r\n}\r\n\r\n/**\r\n * Enum đại diện cho loại tham chiếu giao dịch\r\n */\r\nexport enum TransactionReferenceType {\r\n  DEPOSIT = 'DEPOSIT',                   // Nạp tiền\r\n  WITHDRAWAL = 'WITHDRAWAL',             // Rút tiền\r\n  REFUND = 'REFUND',                     // Hoàn tiền\r\n  ORDER_BOOK = 'ORDER_BOOK',             // Bạc online\r\n  SYSTEM = 'SYSTEM',                     // Phí lưu kho\r\n  SILVER_ORDER = 'SILVER_ORDER',         // Bạc truyền thống\r\n  COMMISSION = 'COMMISSION',             // Hoa hồng\r\n  OTHER = 'OTHER',                       // Khoản khác\r\n}\r\n\r\n/**\r\n * Interface đại diện cho giao dịch trong hệ thống\r\n * Chứa các thông tin cơ bản về giao dịch và các mối quan hệ liên quan\r\n */\r\nexport interface TransactionDto {\r\n  /**\r\n   * Mã định danh duy nhất của giao dịch\r\n   */\r\n  id: string;\r\n\r\n  /**\r\n   * ID của người dùng thực hiện giao dịch\r\n   */\r\n  userId: string;\r\n\r\n  /**\r\n   * ID của ví liên quan đến giao dịch\r\n   */\r\n  walletId: string;\r\n\r\n  /**\r\n   * Loại giao dịch (CREDIT, DEBIT)\r\n   */\r\n  transactionType: TransactionType;\r\n\r\n  /**\r\n   * Số tiền giao dịch\r\n   */\r\n  amount: number | string;\r\n\r\n  /**\r\n   * Số dư trước khi thực hiện giao dịch\r\n   */\r\n  balanceBefore?: number;\r\n\r\n  /**\r\n   * Số dư sau khi thực hiện giao dịch\r\n   */\r\n  balanceAfter?: number;\r\n\r\n  /**\r\n   * Trạng thái giao dịch (PENDING, COMPLETED, CANCELLED, FAILED)\r\n   */\r\n  status: TransactionStatus;\r\n\r\n  /**\r\n   * Ghi chú giao dịch\r\n   */\r\n  notes?: string;\r\n\r\n  /**\r\n   * Mã giao dịch từ cổng thanh toán\r\n   */\r\n  gatewayTransactionId?: string;\r\n\r\n  /**\r\n   * Dữ liệu phản hồi từ cổng thanh toán\r\n   */\r\n  gatewayResponse?: any;\r\n\r\n  /**\r\n   * URL thanh toán\r\n   */\r\n  paymentUrl?: string;\r\n\r\n  /**\r\n   * ID của phương thức thanh toán liên quan\r\n   */\r\n  paymentMethodId?: string;\r\n\r\n  /**\r\n   * Loại tham chiếu của giao dịch\r\n   */\r\n  referenceType?: TransactionReferenceType;\r\n\r\n  /**\r\n   * ID tham chiếu của giao dịch\r\n   */\r\n  referenceId?: string;\r\n\r\n  /**\r\n   * Thời gian tạo giao dịch\r\n   */\r\n  createdAt: Date | string;\r\n\r\n  /**\r\n   * Thời gian cập nhật cuối cùng\r\n   */\r\n  updatedAt: Date | string;\r\n\r\n  /**\r\n   * ID của người tạo giao dịch\r\n   */\r\n  createdBy?: string;\r\n\r\n  /**\r\n   * ID của người cập nhật giao dịch\r\n   */\r\n  updatedBy?: string;\r\n\r\n  /**\r\n   * Trạng thái xóa của giao dịch\r\n   */\r\n  isDeleted: boolean;\r\n\r\n  /**\r\n   * ID của người xóa giao dịch\r\n   */\r\n  deletedBy?: string;\r\n\r\n  /**\r\n   * Thời gian xóa giao dịch\r\n   */\r\n  deletedAt?: Date | string;\r\n\r\n  // Relationships\r\n  /**\r\n   * Người dùng thực hiện giao dịch\r\n   */\r\n  user?: User;\r\n\r\n  /**\r\n   * Người đã tạo giao dịch\r\n   */\r\n  creator?: User;\r\n\r\n  /**\r\n   * Người đã cập nhật giao dịch\r\n   */\r\n  updater?: User;\r\n\r\n  /**\r\n   * Người đã xóa giao dịch\r\n   */\r\n  deleter?: User;\r\n\r\n  /**\r\n   * Ví liên quan đến giao dịch\r\n   */\r\n  wallet?: WalletDto;\r\n\r\n  /**\r\n   * Phương thức thanh toán liên quan\r\n   */\r\n  paymentMethod?: any;\r\n\r\n  // UI specific fields\r\n  /**\r\n   * Tên người dùng\r\n   */\r\n  userName?: string;\r\n\r\n  /**\r\n   * Email người dùng\r\n   */\r\n  userEmail?: string;\r\n\r\n  /**\r\n   * Avatar người dùng\r\n   */\r\n  userAvatar?: string;\r\n\r\n  /**\r\n   * Thông tin tham chiếu của giao dịch\r\n   * Được trả về từ backend\r\n   */\r\n  referenceInfo?: {\r\n    id: string;\r\n    title: string;\r\n    subtitle?: string;\r\n    status?: string;\r\n    amount?: string | number;\r\n    url?: string;\r\n  };\r\n}\r\n"], "names": [], "mappings": ";;;;;AAMO,IAAA,AAAK,yCAAA;;;WAAA;;AAQL,IAAA,AAAK,2CAAA;;;;;;WAAA;;AAWL,IAAA,AAAK,kDAAA;;;;;;;;;WAAA", "debugId": null}}, {"offset": {"line": 4996, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 5004, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 5012, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/bloomgoo/gold-exchange-platform/frontend/components/common/admin/transactions/type/index.ts"], "sourcesContent": ["export * from './transaction.dto';\r\nexport * from './create-transaction.dto';\r\nexport * from './update-transaction.dto';\r\n"], "names": [], "mappings": ";AAAA;AACA;AACA", "debugId": null}}, {"offset": {"line": 5036, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/bloomgoo/gold-exchange-platform/frontend/components/ui/dialog.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as DialogPrimitive from \"@radix-ui/react-dialog\"\r\nimport { XIcon } from \"lucide-react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction Dialog({\r\n  ...props\r\n}: React.ComponentProps<typeof DialogPrimitive.Root>) {\r\n  return <DialogPrimitive.Root data-slot=\"dialog\" {...props} />\r\n}\r\n\r\nfunction DialogTrigger({\r\n  ...props\r\n}: React.ComponentProps<typeof DialogPrimitive.Trigger>) {\r\n  return <DialogPrimitive.Trigger data-slot=\"dialog-trigger\" {...props} />\r\n}\r\n\r\nfunction DialogPortal({\r\n  ...props\r\n}: React.ComponentProps<typeof DialogPrimitive.Portal>) {\r\n  return <DialogPrimitive.Portal data-slot=\"dialog-portal\" {...props} />\r\n}\r\n\r\nfunction DialogClose({\r\n  ...props\r\n}: React.ComponentProps<typeof DialogPrimitive.Close>) {\r\n  return <DialogPrimitive.Close data-slot=\"dialog-close\" {...props} />\r\n}\r\n\r\nfunction DialogOverlay({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof DialogPrimitive.Overlay>) {\r\n  return (\r\n    <DialogPrimitive.Overlay\r\n      data-slot=\"dialog-overlay\"\r\n      className={cn(\r\n        \"data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction DialogContent({\r\n  className,\r\n  children,\r\n  ...props\r\n}: React.ComponentProps<typeof DialogPrimitive.Content>) {\r\n  return (\r\n    <DialogPortal data-slot=\"dialog-portal\">\r\n      <DialogOverlay />\r\n      <DialogPrimitive.Content\r\n        data-slot=\"dialog-content\"\r\n        className={cn(\r\n          \"bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg\",\r\n          className\r\n        )}\r\n        {...props}\r\n      >\r\n        {children}\r\n        <DialogPrimitive.Close className=\"ring-offset-background focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\">\r\n          <XIcon />\r\n          <span className=\"sr-only\">Close</span>\r\n        </DialogPrimitive.Close>\r\n      </DialogPrimitive.Content>\r\n    </DialogPortal>\r\n  )\r\n}\r\n\r\nfunction DialogHeader({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"dialog-header\"\r\n      className={cn(\"flex flex-col gap-2 text-center sm:text-left\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction DialogFooter({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"dialog-footer\"\r\n      className={cn(\r\n        \"flex flex-col-reverse gap-2 sm:flex-row sm:justify-end\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction DialogTitle({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof DialogPrimitive.Title>) {\r\n  return (\r\n    <DialogPrimitive.Title\r\n      data-slot=\"dialog-title\"\r\n      className={cn(\"text-lg leading-none font-semibold\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction DialogDescription({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof DialogPrimitive.Description>) {\r\n  return (\r\n    <DialogPrimitive.Description\r\n      data-slot=\"dialog-description\"\r\n      className={cn(\"text-muted-foreground text-sm\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport {\r\n  Dialog,\r\n  DialogClose,\r\n  DialogContent,\r\n  DialogDescription,\r\n  DialogFooter,\r\n  DialogHeader,\r\n  DialogOverlay,\r\n  DialogPortal,\r\n  DialogTitle,\r\n  DialogTrigger,\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAGA;AACA;AAEA;AAAA;AANA;;;;;AAQA,SAAS,OAAO,EACd,GAAG,OAC+C;IAClD,qBAAO,uVAAC,+QAAA,CAAA,OAAoB;QAAC,aAAU;QAAU,GAAG,KAAK;;;;;;AAC3D;AAEA,SAAS,cAAc,EACrB,GAAG,OACkD;IACrD,qBAAO,uVAAC,+QAAA,CAAA,UAAuB;QAAC,aAAU;QAAkB,GAAG,KAAK;;;;;;AACtE;AAEA,SAAS,aAAa,EACpB,GAAG,OACiD;IACpD,qBAAO,uVAAC,+QAAA,CAAA,SAAsB;QAAC,aAAU;QAAiB,GAAG,KAAK;;;;;;AACpE;AAEA,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,uVAAC,+QAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;AAEA,SAAS,cAAc,EACrB,SAAS,EACT,GAAG,OACkD;IACrD,qBACE,uVAAC,+QAAA,CAAA,UAAuB;QACtB,aAAU;QACV,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EACV,0JACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,cAAc,EACrB,SAAS,EACT,QAAQ,EACR,GAAG,OACkD;IACrD,qBACE,uVAAC;QAAa,aAAU;;0BACtB,uVAAC;;;;;0BACD,uVAAC,+QAAA,CAAA,UAAuB;gBACtB,aAAU;gBACV,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EACV,+WACA;gBAED,GAAG,KAAK;;oBAER;kCACD,uVAAC,+QAAA,CAAA,QAAqB;wBAAC,WAAU;;0CAC/B,uVAAC,oRAAA,CAAA,QAAK;;;;;0CACN,uVAAC;gCAAK,WAAU;0CAAU;;;;;;;;;;;;;;;;;;;;;;;;AAKpC;AAEA,SAAS,aAAa,EAAE,SAAS,EAAE,GAAG,OAAoC;IACxE,qBACE,uVAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EAAE,gDAAgD;QAC7D,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,aAAa,EAAE,SAAS,EAAE,GAAG,OAAoC;IACxE,qBACE,uVAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EACV,0DACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,uVAAC,+QAAA,CAAA,QAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QACnD,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,kBAAkB,EACzB,SAAS,EACT,GAAG,OACsD;IACzD,qBACE,uVAAC,+QAAA,CAAA,cAA2B;QAC1B,aAAU;QACV,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 5210, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/bloomgoo/gold-exchange-platform/frontend/components/ui/currency-display.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport React from \"react\"\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nexport interface CurrencyDisplayProps {\r\n  amount: number | string | null | undefined\r\n  currency?: string\r\n  emptyValue?: string\r\n  className?: string\r\n  showSymbol?: boolean\r\n  showCode?: boolean\r\n  precision?: number\r\n}\r\n\r\n/**\r\n * Component hiển thị số tiền theo định dạng chuẩn\r\n *\r\n * @example\r\n * // Hiển thị số tiền với đơn vị tiền tệ mặc định (VND)\r\n * <CurrencyDisplay amount={1000000} />\r\n *\r\n * // Hiển thị số tiền với đơn vị tiền tệ cụ thể\r\n * <CurrencyDisplay amount={1000} currency=\"USD\" />\r\n *\r\n * // Tùy chỉnh giá trị hiển thị khi amount là null/undefined\r\n * <CurrencyDisplay amount={null} emptyValue=\"Chưa cập nhật\" />\r\n *\r\n * // Tùy chỉnh hiển thị mã tiền tệ\r\n * <CurrencyDisplay amount={1000} currency=\"USD\" showCode={true} />\r\n *\r\n * // Tùy chỉnh số chữ số thập phân\r\n * <CurrencyDisplay amount={1000.5678} currency=\"USD\" precision={3} />\r\n */\r\nexport function CurrencyDisplay({\r\n  amount,\r\n  currency = 'VND',\r\n  emptyValue = \"---\",\r\n  className,\r\n  showSymbol = true,\r\n  showCode = false,\r\n  precision\r\n}: CurrencyDisplayProps) {\r\n  // Nếu amount là null hoặc undefined, hiển thị giá trị mặc định\r\n  if (amount === null || amount === undefined) {\r\n    return <span className={cn(\"text-muted-foreground\", className)}>{emptyValue}</span>\r\n  }\r\n\r\n  // Chuyển đổi amount thành số nếu nó là chuỗi\r\n  const numericAmount = typeof amount === 'string' ? parseFloat(amount) : amount\r\n\r\n  // Nếu amount không phải là số hợp lệ, hiển thị giá trị mặc định\r\n  if (isNaN(numericAmount)) {\r\n    return <span className={cn(\"text-muted-foreground\", className)}>{emptyValue}</span>\r\n  }\r\n\r\n  // Định dạng số tiền theo tiền tệ\r\n  try {\r\n    // Xử lý đặc biệt cho các đơn vị tiền tệ tùy chỉnh\r\n    if (currency === 'REWARD' || currency === 'COMMISSION' || currency === 'CASH') {\r\n      // Định dạng số không có đơn vị tiền tệ\r\n      const formattedNumber = new Intl.NumberFormat('vi-VN', {\r\n        style: 'decimal',\r\n        minimumFractionDigits: precision !== undefined ? precision : 0,\r\n        maximumFractionDigits: precision !== undefined ? precision : 0,\r\n      }).format(numericAmount);\r\n\r\n      // Map các loại ví sang tên hiển thị tiếng Việt\r\n      const currencyDisplayMap: Record<string, string> = {\r\n        'REWARD': 'điểm',\r\n        'COMMISSION': 'hoa hồng',\r\n        'CASH': 'đ'\r\n      };\r\n\r\n      const displayUnit = currencyDisplayMap[currency] || currency;\r\n\r\n      return <span className={className}>{formattedNumber} {displayUnit}</span>;\r\n    }\r\n\r\n    // Xử lý các đơn vị tiền tệ chuẩn\r\n    const formattedAmount = new Intl.NumberFormat('vi-VN', {\r\n      style: showSymbol ? 'currency' : 'decimal',\r\n      currency: currency,\r\n      minimumFractionDigits: precision !== undefined ? precision : undefined,\r\n      maximumFractionDigits: precision !== undefined ? precision : undefined,\r\n    }).format(numericAmount)\r\n\r\n    // Nếu hiển thị mã tiền tệ và không hiển thị ký hiệu\r\n    if (showCode && !showSymbol) {\r\n      return <span className={className}>{formattedAmount} {currency}</span>\r\n    }\r\n\r\n    return <span className={className}>{formattedAmount}</span>\r\n  } catch (error) {\r\n    console.error('Error formatting currency:', error)\r\n    return <span className={cn(\"text-muted-foreground\", className)}>{emptyValue}</span>\r\n  }\r\n}\r\n\r\n/**\r\n * Component hiển thị số tiền VND theo định dạng chuẩn\r\n */\r\nexport function VndDisplay({\r\n  amount,\r\n  emptyValue = \"---\",\r\n  className,\r\n  showSymbol = true\r\n}: Omit<CurrencyDisplayProps, \"currency\" | \"showCode\" | \"precision\">) {\r\n  return (\r\n    <CurrencyDisplay\r\n      amount={amount}\r\n      currency=\"VND\"\r\n      emptyValue={emptyValue}\r\n      className={className}\r\n      showSymbol={showSymbol}\r\n      showCode={false}\r\n      precision={0}\r\n    />\r\n  )\r\n}\r\n\r\n/**\r\n * Component hiển thị số tiền USD theo định dạng chuẩn\r\n */\r\nexport function UsdDisplay({\r\n  amount,\r\n  emptyValue = \"---\",\r\n  className,\r\n  showSymbol = true,\r\n  precision = 2\r\n}: Omit<CurrencyDisplayProps, \"currency\" | \"showCode\"> & { precision?: number }) {\r\n  return (\r\n    <CurrencyDisplay\r\n      amount={amount}\r\n      currency=\"USD\"\r\n      emptyValue={emptyValue}\r\n      className={className}\r\n      showSymbol={showSymbol}\r\n      showCode={false}\r\n      precision={precision}\r\n    />\r\n  )\r\n}\r\n\r\n/**\r\n * Component hiển thị điểm thưởng theo định dạng chuẩn\r\n */\r\nexport function RewardPointsDisplay({\r\n  amount,\r\n  emptyValue = \"---\",\r\n  className,\r\n  precision = 0\r\n}: Omit<CurrencyDisplayProps, \"currency\" | \"showCode\" | \"showSymbol\"> & { precision?: number }) {\r\n  return (\r\n    <CurrencyDisplay\r\n      amount={amount}\r\n      currency=\"REWARD\"\r\n      emptyValue={emptyValue}\r\n      className={className}\r\n      showSymbol={false}\r\n      showCode={false}\r\n      precision={precision}\r\n    />\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;;;;AAGA;AAAA;AAHA;;;AAkCO,SAAS,gBAAgB,EAC9B,MAAM,EACN,WAAW,KAAK,EAChB,aAAa,KAAK,EAClB,SAAS,EACT,aAAa,IAAI,EACjB,WAAW,KAAK,EAChB,SAAS,EACY;IACrB,+DAA+D;IAC/D,IAAI,WAAW,QAAQ,WAAW,WAAW;QAC3C,qBAAO,uVAAC;YAAK,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EAAE,yBAAyB;sBAAa;;;;;;IACnE;IAEA,6CAA6C;IAC7C,MAAM,gBAAgB,OAAO,WAAW,WAAW,WAAW,UAAU;IAExE,gEAAgE;IAChE,IAAI,MAAM,gBAAgB;QACxB,qBAAO,uVAAC;YAAK,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EAAE,yBAAyB;sBAAa;;;;;;IACnE;IAEA,iCAAiC;IACjC,IAAI;QACF,kDAAkD;QAClD,IAAI,aAAa,YAAY,aAAa,gBAAgB,aAAa,QAAQ;YAC7E,uCAAuC;YACvC,MAAM,kBAAkB,IAAI,KAAK,YAAY,CAAC,SAAS;gBACrD,OAAO;gBACP,uBAAuB,cAAc,YAAY,YAAY;gBAC7D,uBAAuB,cAAc,YAAY,YAAY;YAC/D,GAAG,MAAM,CAAC;YAEV,+CAA+C;YAC/C,MAAM,qBAA6C;gBACjD,UAAU;gBACV,cAAc;gBACd,QAAQ;YACV;YAEA,MAAM,cAAc,kBAAkB,CAAC,SAAS,IAAI;YAEpD,qBAAO,uVAAC;gBAAK,WAAW;;oBAAY;oBAAgB;oBAAE;;;;;;;QACxD;QAEA,iCAAiC;QACjC,MAAM,kBAAkB,IAAI,KAAK,YAAY,CAAC,SAAS;YACrD,OAAO,aAAa,aAAa;YACjC,UAAU;YACV,uBAAuB,cAAc,YAAY,YAAY;YAC7D,uBAAuB,cAAc,YAAY,YAAY;QAC/D,GAAG,MAAM,CAAC;QAEV,oDAAoD;QACpD,IAAI,YAAY,CAAC,YAAY;YAC3B,qBAAO,uVAAC;gBAAK,WAAW;;oBAAY;oBAAgB;oBAAE;;;;;;;QACxD;QAEA,qBAAO,uVAAC;YAAK,WAAW;sBAAY;;;;;;IACtC,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,8BAA8B;QAC5C,qBAAO,uVAAC;YAAK,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EAAE,yBAAyB;sBAAa;;;;;;IACnE;AACF;AAKO,SAAS,WAAW,EACzB,MAAM,EACN,aAAa,KAAK,EAClB,SAAS,EACT,aAAa,IAAI,EACiD;IAClE,qBACE,uVAAC;QACC,QAAQ;QACR,UAAS;QACT,YAAY;QACZ,WAAW;QACX,YAAY;QACZ,UAAU;QACV,WAAW;;;;;;AAGjB;AAKO,SAAS,WAAW,EACzB,MAAM,EACN,aAAa,KAAK,EAClB,SAAS,EACT,aAAa,IAAI,EACjB,YAAY,CAAC,EACgE;IAC7E,qBACE,uVAAC;QACC,QAAQ;QACR,UAAS;QACT,YAAY;QACZ,WAAW;QACX,YAAY;QACZ,UAAU;QACV,WAAW;;;;;;AAGjB;AAKO,SAAS,oBAAoB,EAClC,MAAM,EACN,aAAa,KAAK,EAClB,SAAS,EACT,YAAY,CAAC,EAC+E;IAC5F,qBACE,uVAAC;QACC,QAAQ;QACR,UAAS;QACT,YAAY;QACZ,WAAW;QACX,YAAY;QACZ,UAAU;QACV,WAAW;;;;;;AAGjB", "debugId": null}}, {"offset": {"line": 5370, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/bloomgoo/gold-exchange-platform/frontend/components/ui/date-time-display.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport React from \"react\"\r\nimport { cn } from \"@/lib/utils\"\r\nimport { formatDate, formatDateTime, formatTime, formatFullDateTime } from \"@/lib/utils\"\r\n\r\nexport type DateTimeFormat = \"date\" | \"time\" | \"dateTime\" | \"fullDateTime\"\r\n\r\nexport interface DateTimeDisplayProps {\r\n  date: Date | string | number | null | undefined\r\n  format?: DateTimeFormat\r\n  emptyValue?: string\r\n  className?: string\r\n  withTime?: boolean // Deprecated, use format=\"dateTime\" instead\r\n  timeClassName?: string\r\n}\r\n\r\n/**\r\n * Component hiển thị ngày giờ theo định dạng chuẩn Việt Nam\r\n * \r\n * @example\r\n * // Hiển thị ngày giờ (dd/MM/yyyy HH:mm)\r\n * <DateTimeDisplay date={new Date()} format=\"dateTime\" />\r\n * \r\n * // Hiển thị chỉ ngày (dd/MM/yyyy)\r\n * <DateTimeDisplay date=\"2023-01-01T00:00:00\" format=\"date\" />\r\n * \r\n * // Hiển thị chỉ thời gian (HH:mm:ss)\r\n * <DateTimeDisplay date={1672531200000} format=\"time\" />\r\n * \r\n * // Hiển thị ngày giờ đầy đủ (dd/MM/yyyy HH:mm:ss)\r\n * <DateTimeDisplay date={new Date()} format=\"fullDateTime\" />\r\n * \r\n * // Tùy chỉnh giá trị hiển thị khi date là null/undefined\r\n * <DateTimeDisplay date={null} emptyValue=\"Chưa cập nhật\" />\r\n */\r\nexport function DateTimeDisplay({\r\n  date,\r\n  format = \"fullDateTime\",\r\n  emptyValue = \"---\",\r\n  className,\r\n  withTime, // Deprecated\r\n  timeClassName\r\n}: DateTimeDisplayProps) {\r\n  // Xử lý tương thích ngược với prop withTime\r\n  if (withTime !== undefined) {\r\n    format = withTime ? \"dateTime\" : \"date\"\r\n  }\r\n\r\n  // Nếu date là null hoặc undefined, hiển thị giá trị mặc định\r\n  if (date === null || date === undefined) {\r\n    return <span className={cn(\"text-muted-foreground\", className)}>{emptyValue}</span>\r\n  }\r\n\r\n  // Định dạng ngày giờ theo format được chọn\r\n  let formattedValue: string\r\n  switch (format) {\r\n    case \"date\":\r\n      formattedValue = formatDate(date, emptyValue)\r\n      break\r\n    case \"time\":\r\n      formattedValue = formatTime(date, emptyValue)\r\n      break\r\n    case \"fullDateTime\":\r\n      formattedValue = formatFullDateTime(date, emptyValue)\r\n      break\r\n    case \"dateTime\":\r\n    default:\r\n      formattedValue = formatDateTime(date, emptyValue)\r\n      break\r\n  }\r\n\r\n  // Nếu là định dạng dateTime và có timeClassName, hiển thị ngày và giờ riêng biệt\r\n  if ((format === \"dateTime\" || format === \"fullDateTime\") && timeClassName) {\r\n    const dateObj = typeof date === 'string' || typeof date === 'number' \r\n      ? new Date(date) \r\n      : date\r\n    \r\n    if (!dateObj || !formattedValue || formattedValue === \"Không hợp lệ\") {\r\n      return <span className={cn(\"text-muted-foreground\", className)}>{formattedValue}</span>\r\n    }\r\n\r\n    const [datePart, timePart] = formattedValue.split(\" \")\r\n    \r\n    return (\r\n      <div className={cn(\"flex flex-col\", className)}>\r\n        <span>{datePart}</span>\r\n        <span className={cn(\"text-xs text-muted-foreground\", timeClassName)}>\r\n          {timePart}\r\n        </span>\r\n      </div>\r\n    )\r\n  }\r\n\r\n  // Hiển thị giá trị đã định dạng\r\n  return <span className={className}>{formattedValue}</span>\r\n}\r\n\r\n/**\r\n * Component hiển thị ngày theo định dạng chuẩn Việt Nam (dd/MM/yyyy)\r\n */\r\nexport function DateDisplay({\r\n  date,\r\n  emptyValue = \"---\",\r\n  className\r\n}: Omit<DateTimeDisplayProps, \"format\" | \"withTime\" | \"timeClassName\">) {\r\n  return (\r\n    <DateTimeDisplay\r\n      date={date}\r\n      format=\"date\"\r\n      emptyValue={emptyValue}\r\n      className={className}\r\n    />\r\n  )\r\n}\r\n\r\n/**\r\n * Component hiển thị thời gian theo định dạng chuẩn Việt Nam (HH:mm:ss)\r\n */\r\nexport function TimeDisplay({\r\n  date,\r\n  emptyValue = \"---\",\r\n  className\r\n}: Omit<DateTimeDisplayProps, \"format\" | \"withTime\" | \"timeClassName\">) {\r\n  return (\r\n    <DateTimeDisplay\r\n      date={date}\r\n      format=\"time\"\r\n      emptyValue={emptyValue}\r\n      className={className}\r\n    />\r\n  )\r\n}\r\n\r\n/**\r\n * Component hiển thị ngày giờ đầy đủ theo định dạng chuẩn Việt Nam (dd/MM/yyyy HH:mm:ss)\r\n */\r\nexport function FullDateTimeDisplay({\r\n  date,\r\n  emptyValue = \"---\",\r\n  className\r\n}: Omit<DateTimeDisplayProps, \"format\" | \"withTime\" | \"timeClassName\">) {\r\n  return (\r\n    <DateTimeDisplay\r\n      date={date}\r\n      format=\"fullDateTime\"\r\n      emptyValue={emptyValue}\r\n      className={className}\r\n    />\r\n  )\r\n}\r\n\r\n/**\r\n * Component hiển thị ngày giờ với thời gian ở dòng riêng biệt\r\n */\r\nexport function DateTimeWithSeparateTimeDisplay({\r\n  date,\r\n  emptyValue = \"---\",\r\n  className,\r\n  timeClassName\r\n}: Omit<DateTimeDisplayProps, \"format\" | \"withTime\">) {\r\n  return (\r\n    <DateTimeDisplay\r\n      date={date}\r\n      format=\"dateTime\"\r\n      emptyValue={emptyValue}\r\n      className={className}\r\n      timeClassName={timeClassName || \"text-xs text-muted-foreground\"}\r\n    />\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;AAGA;AAAA;AAHA;;;;AAoCO,SAAS,gBAAgB,EAC9B,IAAI,EACJ,SAAS,cAAc,EACvB,aAAa,KAAK,EAClB,SAAS,EACT,QAAQ,EACR,aAAa,EACQ;IACrB,4CAA4C;IAC5C,IAAI,aAAa,WAAW;QAC1B,SAAS,WAAW,aAAa;IACnC;IAEA,6DAA6D;IAC7D,IAAI,SAAS,QAAQ,SAAS,WAAW;QACvC,qBAAO,uVAAC;YAAK,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EAAE,yBAAyB;sBAAa;;;;;;IACnE;IAEA,2CAA2C;IAC3C,IAAI;IACJ,OAAQ;QACN,KAAK;YACH,iBAAiB,CAAA,GAAA,4HAAA,CAAA,aAAU,AAAD,EAAE,MAAM;YAClC;QACF,KAAK;YACH,iBAAiB,CAAA,GAAA,4HAAA,CAAA,aAAU,AAAD,EAAE,MAAM;YAClC;QACF,KAAK;YACH,iBAAiB,CAAA,GAAA,4HAAA,CAAA,qBAAkB,AAAD,EAAE,MAAM;YAC1C;QACF,KAAK;QACL;YACE,iBAAiB,CAAA,GAAA,4HAAA,CAAA,iBAAc,AAAD,EAAE,MAAM;YACtC;IACJ;IAEA,iFAAiF;IACjF,IAAI,CAAC,WAAW,cAAc,WAAW,cAAc,KAAK,eAAe;QACzE,MAAM,UAAU,OAAO,SAAS,YAAY,OAAO,SAAS,WACxD,IAAI,KAAK,QACT;QAEJ,IAAI,CAAC,WAAW,CAAC,kBAAkB,mBAAmB,gBAAgB;YACpE,qBAAO,uVAAC;gBAAK,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EAAE,yBAAyB;0BAAa;;;;;;QACnE;QAEA,MAAM,CAAC,UAAU,SAAS,GAAG,eAAe,KAAK,CAAC;QAElD,qBACE,uVAAC;YAAI,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EAAE,iBAAiB;;8BAClC,uVAAC;8BAAM;;;;;;8BACP,uVAAC;oBAAK,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;8BAClD;;;;;;;;;;;;IAIT;IAEA,gCAAgC;IAChC,qBAAO,uVAAC;QAAK,WAAW;kBAAY;;;;;;AACtC;AAKO,SAAS,YAAY,EAC1B,IAAI,EACJ,aAAa,KAAK,EAClB,SAAS,EAC2D;IACpE,qBACE,uVAAC;QACC,MAAM;QACN,QAAO;QACP,YAAY;QACZ,WAAW;;;;;;AAGjB;AAKO,SAAS,YAAY,EAC1B,IAAI,EACJ,aAAa,KAAK,EAClB,SAAS,EAC2D;IACpE,qBACE,uVAAC;QACC,MAAM;QACN,QAAO;QACP,YAAY;QACZ,WAAW;;;;;;AAGjB;AAKO,SAAS,oBAAoB,EAClC,IAAI,EACJ,aAAa,KAAK,EAClB,SAAS,EAC2D;IACpE,qBACE,uVAAC;QACC,MAAM;QACN,QAAO;QACP,YAAY;QACZ,WAAW;;;;;;AAGjB;AAKO,SAAS,gCAAgC,EAC9C,IAAI,EACJ,aAAa,KAAK,EAClB,SAAS,EACT,aAAa,EACqC;IAClD,qBACE,uVAAC;QACC,MAAM;QACN,QAAO;QACP,YAAY;QACZ,WAAW;QACX,eAAe,iBAAiB;;;;;;AAGtC", "debugId": null}}, {"offset": {"line": 5521, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/bloomgoo/gold-exchange-platform/frontend/components/ui/hover-card.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as HoverCardPrimitive from \"@radix-ui/react-hover-card\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction HoverCard({\r\n  ...props\r\n}: React.ComponentProps<typeof HoverCardPrimitive.Root>) {\r\n  return <HoverCardPrimitive.Root data-slot=\"hover-card\" {...props} />\r\n}\r\n\r\nfunction HoverCardTrigger({\r\n  ...props\r\n}: React.ComponentProps<typeof HoverCardPrimitive.Trigger>) {\r\n  return (\r\n    <HoverCardPrimitive.Trigger data-slot=\"hover-card-trigger\" {...props} />\r\n  )\r\n}\r\n\r\nfunction HoverCardContent({\r\n  className,\r\n  align = \"center\",\r\n  sideOffset = 4,\r\n  ...props\r\n}: React.ComponentProps<typeof HoverCardPrimitive.Content>) {\r\n  return (\r\n    <HoverCardPrimitive.Portal data-slot=\"hover-card-portal\">\r\n      <HoverCardPrimitive.Content\r\n        data-slot=\"hover-card-content\"\r\n        align={align}\r\n        sideOffset={sideOffset}\r\n        className={cn(\r\n          \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 w-64 origin-(--radix-hover-card-content-transform-origin) rounded-md border p-4 shadow-md outline-hidden\",\r\n          className\r\n        )}\r\n        {...props}\r\n      />\r\n    </HoverCardPrimitive.Portal>\r\n  )\r\n}\r\n\r\nexport { HoverCard, HoverCardTrigger, HoverCardContent }\r\n"], "names": [], "mappings": ";;;;;;AAGA;AAEA;AAAA;AALA;;;;AAOA,SAAS,UAAU,EACjB,GAAG,OACkD;IACrD,qBAAO,uVAAC,mRAAA,CAAA,OAAuB;QAAC,aAAU;QAAc,GAAG,KAAK;;;;;;AAClE;AAEA,SAAS,iBAAiB,EACxB,GAAG,OACqD;IACxD,qBACE,uVAAC,mRAAA,CAAA,UAA0B;QAAC,aAAU;QAAsB,GAAG,KAAK;;;;;;AAExE;AAEA,SAAS,iBAAiB,EACxB,SAAS,EACT,QAAQ,QAAQ,EAChB,aAAa,CAAC,EACd,GAAG,OACqD;IACxD,qBACE,uVAAC,mRAAA,CAAA,SAAyB;QAAC,aAAU;kBACnC,cAAA,uVAAC,mRAAA,CAAA,UAA0B;YACzB,aAAU;YACV,OAAO;YACP,YAAY;YACZ,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EACV,qeACA;YAED,GAAG,KAAK;;;;;;;;;;;AAIjB", "debugId": null}}, {"offset": {"line": 5581, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/bloomgoo/gold-exchange-platform/frontend/components/common/admin/wallets/type/wallet-type.enum.ts"], "sourcesContent": ["/**\r\n * Enum đại diện cho các loại ví trong hệ thống\r\n */\r\nexport enum WalletType {\r\n  CASH = 'CASH',               // Ví tiền mặt/tiền chính\r\n  REWARD = 'REWARD',           // Ví điểm thưởng\r\n  COMMISSION = 'COMMISSION'    // Ví hoa hồng\r\n}\r\n"], "names": [], "mappings": "AAAA;;CAEC;;;AACM,IAAA,AAAK,oCAAA;;;6CAGmB,cAAc;WAHjC", "debugId": null}}, {"offset": {"line": 5598, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/bloomgoo/gold-exchange-platform/frontend/components/common/admin/wallets/utils/wallet-type-helper.ts"], "sourcesContent": ["import { WalletType } from \"../type/wallet-type.enum\";\r\n\r\n/**\r\n * Chuyển đổi giá trị enum WalletType sang tên hiển thị tiếng Việt\r\n * @param type Loại ví (WalletType)\r\n * @returns Tên hiển thị tiếng Việt\r\n */\r\nexport const getWalletTypeLabel = (type: WalletType | string): string => {\r\n  const walletTypeLabels: Record<string, string> = {\r\n    [WalletType.CASH]: \"Ví tiền mặt\",\r\n    [WalletType.REWARD]: \"Ví điểm thưởng\",\r\n    [WalletType.COMMISSION]: \"V<PERSON> hoa hồng\"\r\n  };\r\n\r\n  return walletTypeLabels[type as string] || type;\r\n};\r\n\r\n/**\r\n * Lấy màu hiển thị cho từng loại ví\r\n * @param type Loại ví (WalletType)\r\n * @returns Class CSS cho màu hiển thị\r\n */\r\nexport const getWalletTypeColor = (type: WalletType | string): string => {\r\n  switch (type) {\r\n    case WalletType.CASH:\r\n      return 'bg-green-100 text-green-800';\r\n    case WalletType.REWARD:\r\n      return 'bg-purple-100 text-purple-800';\r\n    case WalletType.COMMISSION:\r\n      return 'bg-blue-100 text-blue-800';\r\n    default:\r\n      return 'bg-gray-100 text-gray-800';\r\n  }\r\n};\r\n"], "names": [], "mappings": ";;;;AAAA;;AAOO,MAAM,qBAAqB,CAAC;IACjC,MAAM,mBAA2C;QAC/C,CAAC,0KAAA,CAAA,aAAU,CAAC,IAAI,CAAC,EAAE;QACnB,CAAC,0KAAA,CAAA,aAAU,CAAC,MAAM,CAAC,EAAE;QACrB,CAAC,0KAAA,CAAA,aAAU,CAAC,UAAU,CAAC,EAAE;IAC3B;IAEA,OAAO,gBAAgB,CAAC,KAAe,IAAI;AAC7C;AAOO,MAAM,qBAAqB,CAAC;IACjC,OAAQ;QACN,KAAK,0KAAA,CAAA,aAAU,CAAC,IAAI;YAClB,OAAO;QACT,KAAK,0KAAA,CAAA,aAAU,CAAC,MAAM;YACpB,OAAO;QACT,KAAK,0KAAA,CAAA,aAAU,CAAC,UAAU;YACxB,OAAO;QACT;YACE,OAAO;IACX;AACF", "debugId": null}}, {"offset": {"line": 5630, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/bloomgoo/gold-exchange-platform/frontend/components/common/user/user-hover-card.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';\r\nimport { HoverCard, HoverCardContent, HoverCardTrigger } from '@/components/ui/hover-card';\r\nimport { User } from '@/components/common/admin/users/type/user';\r\nimport { Badge } from '@/components/ui/badge';\r\nimport { format } from 'date-fns';\r\nimport { vi } from 'date-fns/locale';\r\nimport { Mail } from 'lucide-react';\r\n\r\ninterface UserHoverCardProps {\r\n  user?: Partial<User> | null;\r\n  userId?: string;\r\n  showAvatar?: boolean;\r\n  size?: 'sm' | 'md' | 'lg';\r\n  children: React.ReactNode;\r\n}\r\n\r\n/**\r\n * Component hiển thị thông tin người dùng khi hover\r\n * @param user Thông tin người dùng\r\n * @param userId ID người dùng (nếu không có thông tin đầy đủ)\r\n * @param showAvatar Có hiển thị avatar không\r\n * @param size Kích thước hiển thị (sm, md, lg)\r\n * @param children Nội dung hiển thị khi không hover\r\n */\r\nexport function UserHoverCard({ user, userId, showAvatar = true, size = 'md', children }: UserHoverCardProps) {\r\n  // Nếu không có thông tin người dùng và không có userId, hiển thị children\r\n  if (!user && !userId) {\r\n    return <>{children}</>;\r\n  }\r\n\r\n  // Lấy ID từ user hoặc từ userId\r\n  const id = user?.id || userId;\r\n\r\n  // Xác định kích thước avatar dựa trên size\r\n  const avatarSize = {\r\n    sm: 'size-5',\r\n    md: 'size-6',\r\n    lg: 'size-8',\r\n  }[size];\r\n\r\n  // Hiển thị badge trạng thái người dùng\r\n  const getStatusBadge = (isActive?: boolean) => {\r\n    if (isActive === undefined) return null;\r\n    return (\r\n      <Badge variant={isActive ? 'default' : 'secondary'} className=\"ml-2\">\r\n        {isActive ? 'Hoạt động' : 'Vô hiệu'}\r\n      </Badge>\r\n    );\r\n  };\r\n\r\n  // Format ngày tháng\r\n  const formatDate = (date?: string | Date) => {\r\n    if (!date) return 'N/A';\r\n    try {\r\n      const dateObj = typeof date === 'string' ? new Date(date) : date;\r\n      return format(dateObj, 'dd/MM/yyyy HH:mm', { locale: vi });\r\n    } catch (error) {\r\n      return 'N/A';\r\n    }\r\n  };\r\n\r\n  return (\r\n    <HoverCard>\r\n      <HoverCardTrigger asChild>\r\n        <div className=\"flex items-center gap-2 cursor-pointer\">\r\n          {showAvatar && (\r\n            <Avatar className={`shrink-0 ${avatarSize}`}>\r\n              <AvatarImage\r\n                src={user?.avatar || `https://api.dicebear.com/9.x/initials/svg?seed=${user?.fullName || user?.username || id}`}\r\n                alt={user?.fullName || user?.username || 'User'}\r\n              />\r\n              <AvatarFallback>\r\n                {user?.fullName\r\n                  ? user.fullName[0].toUpperCase()\r\n                  : (user?.username ? user.username[0].toUpperCase() : 'U')}\r\n              </AvatarFallback>\r\n            </Avatar>\r\n          )}\r\n          {children}\r\n        </div>\r\n      </HoverCardTrigger>\r\n      <HoverCardContent className=\"w-80 p-4\">\r\n        <div className=\"flex flex-col gap-4\">\r\n          {/* Header với thông tin cơ bản */}\r\n          <div className=\"flex items-center gap-3\">\r\n            <Avatar className=\"size-10 shrink-0\">\r\n              <AvatarImage\r\n                src={user?.avatar || `https://api.dicebear.com/9.x/initials/svg?seed=${user?.fullName || user?.username || id}`}\r\n                alt={user?.fullName || user?.username || 'User'}\r\n              />\r\n              <AvatarFallback>\r\n                {user?.fullName\r\n                  ? user.fullName[0].toUpperCase()\r\n                  : (user?.username ? user.username[0].toUpperCase() : 'U')}\r\n              </AvatarFallback>\r\n            </Avatar>\r\n            <div>\r\n              <div className=\"font-medium flex items-center\">\r\n                {user?.fullName || user?.username || id?.substring(0, 8)}\r\n                {getStatusBadge(user?.isActive)}\r\n              </div>\r\n              {user?.email && (\r\n                <div className=\"text-sm text-muted-foreground flex items-center gap-1 mt-1\">\r\n                  <Mail className=\"size-3\" />\r\n                  {user.email}\r\n                </div>\r\n              )}\r\n            </div>\r\n          </div>\r\n\r\n          {/* Thông tin chi tiết */}\r\n          <div className=\"grid grid-cols-2 gap-3 text-sm\">\r\n            {user?.username && (\r\n              <div>\r\n                <div className=\"text-muted-foreground\">Tên đăng nhập</div>\r\n                <div>{user.username}</div>\r\n              </div>\r\n            )}\r\n            {user?.phone && (\r\n              <div>\r\n                <div className=\"text-muted-foreground\">Số điện thoại</div>\r\n                <div>{user.phone}</div>\r\n              </div>\r\n            )}\r\n            {user?.address && (\r\n              <div className=\"col-span-2\">\r\n                <div className=\"text-muted-foreground\">Địa chỉ</div>\r\n                <div>{user.address}</div>\r\n              </div>\r\n            )}\r\n            {user?.bio && (\r\n              <div className=\"col-span-2\">\r\n                <div className=\"text-muted-foreground\">Giới thiệu</div>\r\n                <div>{user.bio}</div>\r\n              </div>\r\n            )}\r\n            {user?.birthday && (\r\n              <div>\r\n                <div className=\"text-muted-foreground\">Ngày sinh</div>\r\n                <div>{formatDate(user.birthday)}</div>\r\n              </div>\r\n            )}\r\n            {user?.createdAt && (\r\n              <div>\r\n                <div className=\"text-muted-foreground\">Ngày tạo</div>\r\n                <div>{formatDate(user.createdAt)}</div>\r\n              </div>\r\n            )}\r\n            {user?.role && (\r\n              <div>\r\n                <div className=\"text-muted-foreground\">Vai trò</div>\r\n                <div>{Array.isArray(user.role) ? user.role.join(', ') : user.role}</div>\r\n              </div>\r\n            )}\r\n          </div>\r\n        </div>\r\n      </HoverCardContent>\r\n    </HoverCard>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAEA;AACA;AACA;AACA;AARA;;;;;;;;AA0BO,SAAS,cAAc,EAAE,IAAI,EAAE,MAAM,EAAE,aAAa,IAAI,EAAE,OAAO,IAAI,EAAE,QAAQ,EAAsB;IAC1G,0EAA0E;IAC1E,IAAI,CAAC,QAAQ,CAAC,QAAQ;QACpB,qBAAO;sBAAG;;IACZ;IAEA,gCAAgC;IAChC,MAAM,KAAK,MAAM,MAAM;IAEvB,2CAA2C;IAC3C,MAAM,aAAa;QACjB,IAAI;QACJ,IAAI;QACJ,IAAI;IACN,CAAC,CAAC,KAAK;IAEP,uCAAuC;IACvC,MAAM,iBAAiB,CAAC;QACtB,IAAI,aAAa,WAAW,OAAO;QACnC,qBACE,uVAAC,0HAAA,CAAA,QAAK;YAAC,SAAS,WAAW,YAAY;YAAa,WAAU;sBAC3D,WAAW,cAAc;;;;;;IAGhC;IAEA,oBAAoB;IACpB,MAAM,aAAa,CAAC;QAClB,IAAI,CAAC,MAAM,OAAO;QAClB,IAAI;YACF,MAAM,UAAU,OAAO,SAAS,WAAW,IAAI,KAAK,QAAQ;YAC5D,OAAO,CAAA,GAAA,6MAAA,CAAA,SAAM,AAAD,EAAE,SAAS,oBAAoB;gBAAE,QAAQ,mMAAA,CAAA,KAAE;YAAC;QAC1D,EAAE,OAAO,OAAO;YACd,OAAO;QACT;IACF;IAEA,qBACE,uVAAC,kIAAA,CAAA,YAAS;;0BACR,uVAAC,kIAAA,CAAA,mBAAgB;gBAAC,OAAO;0BACvB,cAAA,uVAAC;oBAAI,WAAU;;wBACZ,4BACC,uVAAC,2HAAA,CAAA,SAAM;4BAAC,WAAW,CAAC,SAAS,EAAE,YAAY;;8CACzC,uVAAC,2HAAA,CAAA,cAAW;oCACV,KAAK,MAAM,UAAU,CAAC,+CAA+C,EAAE,MAAM,YAAY,MAAM,YAAY,IAAI;oCAC/G,KAAK,MAAM,YAAY,MAAM,YAAY;;;;;;8CAE3C,uVAAC,2HAAA,CAAA,iBAAc;8CACZ,MAAM,WACH,KAAK,QAAQ,CAAC,EAAE,CAAC,WAAW,KAC3B,MAAM,WAAW,KAAK,QAAQ,CAAC,EAAE,CAAC,WAAW,KAAK;;;;;;;;;;;;wBAI5D;;;;;;;;;;;;0BAGL,uVAAC,kIAAA,CAAA,mBAAgB;gBAAC,WAAU;0BAC1B,cAAA,uVAAC;oBAAI,WAAU;;sCAEb,uVAAC;4BAAI,WAAU;;8CACb,uVAAC,2HAAA,CAAA,SAAM;oCAAC,WAAU;;sDAChB,uVAAC,2HAAA,CAAA,cAAW;4CACV,KAAK,MAAM,UAAU,CAAC,+CAA+C,EAAE,MAAM,YAAY,MAAM,YAAY,IAAI;4CAC/G,KAAK,MAAM,YAAY,MAAM,YAAY;;;;;;sDAE3C,uVAAC,2HAAA,CAAA,iBAAc;sDACZ,MAAM,WACH,KAAK,QAAQ,CAAC,EAAE,CAAC,WAAW,KAC3B,MAAM,WAAW,KAAK,QAAQ,CAAC,EAAE,CAAC,WAAW,KAAK;;;;;;;;;;;;8CAG3D,uVAAC;;sDACC,uVAAC;4CAAI,WAAU;;gDACZ,MAAM,YAAY,MAAM,YAAY,IAAI,UAAU,GAAG;gDACrD,eAAe,MAAM;;;;;;;wCAEvB,MAAM,uBACL,uVAAC;4CAAI,WAAU;;8DACb,uVAAC,sRAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;gDACf,KAAK,KAAK;;;;;;;;;;;;;;;;;;;sCAOnB,uVAAC;4BAAI,WAAU;;gCACZ,MAAM,0BACL,uVAAC;;sDACC,uVAAC;4CAAI,WAAU;sDAAwB;;;;;;sDACvC,uVAAC;sDAAK,KAAK,QAAQ;;;;;;;;;;;;gCAGtB,MAAM,uBACL,uVAAC;;sDACC,uVAAC;4CAAI,WAAU;sDAAwB;;;;;;sDACvC,uVAAC;sDAAK,KAAK,KAAK;;;;;;;;;;;;gCAGnB,MAAM,yBACL,uVAAC;oCAAI,WAAU;;sDACb,uVAAC;4CAAI,WAAU;sDAAwB;;;;;;sDACvC,uVAAC;sDAAK,KAAK,OAAO;;;;;;;;;;;;gCAGrB,MAAM,qBACL,uVAAC;oCAAI,WAAU;;sDACb,uVAAC;4CAAI,WAAU;sDAAwB;;;;;;sDACvC,uVAAC;sDAAK,KAAK,GAAG;;;;;;;;;;;;gCAGjB,MAAM,0BACL,uVAAC;;sDACC,uVAAC;4CAAI,WAAU;sDAAwB;;;;;;sDACvC,uVAAC;sDAAK,WAAW,KAAK,QAAQ;;;;;;;;;;;;gCAGjC,MAAM,2BACL,uVAAC;;sDACC,uVAAC;4CAAI,WAAU;sDAAwB;;;;;;sDACvC,uVAAC;sDAAK,WAAW,KAAK,SAAS;;;;;;;;;;;;gCAGlC,MAAM,sBACL,uVAAC;;sDACC,uVAAC;4CAAI,WAAU;sDAAwB;;;;;;sDACvC,uVAAC;sDAAK,MAAM,OAAO,CAAC,KAAK,IAAI,IAAI,KAAK,IAAI,CAAC,IAAI,CAAC,QAAQ,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQjF", "debugId": null}}, {"offset": {"line": 6001, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/bloomgoo/gold-exchange-platform/frontend/components/common/admin/wallets/wallet-hover-card.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';\r\nimport { HoverCard, HoverCardContent, HoverCardTrigger } from '@/components/ui/hover-card';\r\nimport { WalletDto } from './type';\r\nimport { Badge } from '@/components/ui/badge';\r\nimport { format } from 'date-fns';\r\nimport { vi } from 'date-fns/locale';\r\nimport { Wallet, CreditCard } from 'lucide-react';\r\nimport { CurrencyDisplay } from '@/components/ui/currency-display';\r\nimport { getWalletTypeLabel, getWalletTypeColor } from './utils/wallet-type-helper';\r\nimport { UserHoverCard } from '@/components/common/user/user-hover-card';\r\n\r\ninterface WalletHoverCardProps {\r\n  wallet?: Partial<WalletDto> | null;\r\n  walletId?: string;\r\n  showAvatar?: boolean;\r\n  size?: 'sm' | 'md' | 'lg';\r\n  children: React.ReactNode;\r\n}\r\n\r\n/**\r\n * Component hiển thị thông tin ví khi hover\r\n * @param wallet Thông tin ví\r\n * @param walletId ID ví (nếu không có thông tin đầy đủ)\r\n * @param showAvatar Có hiển thị avatar không\r\n * @param size Kích thước hiển thị (sm, md, lg)\r\n * @param children Nội dung hiển thị khi không hover\r\n */\r\nexport function WalletHoverCard({ wallet, walletId, showAvatar = true, size = 'md', children }: WalletHoverCardProps) {\r\n  // Nếu không có thông tin ví và không có walletId, hiển thị children\r\n  if (!wallet && !walletId) {\r\n    return <>{children}</>;\r\n  }\r\n\r\n  // Lấy ID từ wallet hoặc từ walletId\r\n  const id = wallet?.id || walletId;\r\n\r\n  // Xác định kích thước avatar dựa trên size\r\n  const avatarSize = {\r\n    sm: 'size-5',\r\n    md: 'size-6',\r\n    lg: 'size-8',\r\n  }[size];\r\n\r\n  // Hiển thị badge trạng thái ví\r\n  const getStatusBadge = (status?: string) => {\r\n    if (!status) return null;\r\n\r\n    const statusMap: Record<string, { color: string, label: string }> = {\r\n      'ACTIVE': { color: 'bg-green-100 text-green-800', label: 'Hoạt động' },\r\n      'LOCKED': { color: 'bg-red-100 text-red-800', label: 'Đã khóa' },\r\n    };\r\n\r\n    const statusInfo = statusMap[status] || { color: 'bg-gray-100 text-gray-800', label: status };\r\n\r\n    return (\r\n      <Badge className={statusInfo.color}>\r\n        {statusInfo.label}\r\n      </Badge>\r\n    );\r\n  };\r\n\r\n  // Format ngày tháng\r\n  const formatDate = (date?: string | Date) => {\r\n    if (!date) return 'N/A';\r\n    try {\r\n      const dateObj = typeof date === 'string' ? new Date(date) : date;\r\n      return format(dateObj, 'dd/MM/yyyy HH:mm', { locale: vi });\r\n    } catch (error) {\r\n      return 'N/A';\r\n    }\r\n  };\r\n\r\n  // Lấy tên hiển thị cho ví\r\n  const getWalletDisplayName = () => {\r\n    if (wallet?.walletType) {\r\n      return getWalletTypeLabel(wallet.walletType);\r\n    }\r\n    return id?.substring(0, 8) || 'N/A';\r\n  };\r\n\r\n  return (\r\n    <HoverCard>\r\n      <HoverCardTrigger asChild>\r\n        <div className=\"flex items-center gap-2 cursor-pointer\">\r\n          {showAvatar && (\r\n            <Avatar className={`shrink-0 ${avatarSize}`}>\r\n              <AvatarImage src={`https://api.dicebear.com/7.x/shapes/svg?seed=${id}`} />\r\n              <AvatarFallback>\r\n                <Wallet className=\"h-4 w-4\" />\r\n              </AvatarFallback>\r\n            </Avatar>\r\n          )}\r\n          {children}\r\n        </div>\r\n      </HoverCardTrigger>\r\n      <HoverCardContent className=\"w-80 p-4\">\r\n        <div className=\"flex flex-col gap-4\">\r\n          {/* Header với thông tin cơ bản */}\r\n          <div className=\"flex items-center gap-3\">\r\n            <Avatar className=\"size-10 shrink-0\">\r\n              <AvatarImage src={`https://api.dicebear.com/7.x/shapes/svg?seed=${id}`} />\r\n              <AvatarFallback>\r\n                <Wallet className=\"h-5 w-5\" />\r\n              </AvatarFallback>\r\n            </Avatar>\r\n            <div>\r\n              <div className=\"font-medium flex items-center\">\r\n                {getWalletDisplayName()}\r\n                {wallet?.status && (\r\n                  <span className=\"ml-2\">{getStatusBadge(wallet.status)}</span>\r\n                )}\r\n              </div>\r\n              {wallet?.walletType && (\r\n                <div className=\"text-sm text-muted-foreground flex items-center gap-1 mt-1\">\r\n                  <CreditCard className=\"size-3\" />\r\n                  {id?.substring(0, 12)}...\r\n                </div>\r\n              )}\r\n            </div>\r\n          </div>\r\n\r\n          {/* Thông tin chi tiết */}\r\n          <div className=\"grid grid-cols-2 gap-3 text-sm\">\r\n            {wallet?.balance !== undefined && (\r\n              <div>\r\n                <div className=\"text-muted-foreground\">Số dư</div>\r\n                <div><CurrencyDisplay amount={wallet.balance} currency=\"VND\" /></div>\r\n              </div>\r\n            )}\r\n            {wallet?.availableBalance !== undefined && (\r\n              <div>\r\n                <div className=\"text-muted-foreground\">Số dư khả dụng</div>\r\n                <div><CurrencyDisplay amount={wallet.availableBalance} currency=\"VND\" /></div>\r\n              </div>\r\n            )}\r\n            {wallet?.user && (\r\n              <div className=\"col-span-2\">\r\n                <div className=\"text-muted-foreground\">Chủ sở hữu</div>\r\n                <div>\r\n                  <UserHoverCard user={wallet.user} userId={wallet.userId} showAvatar={false} size=\"sm\">\r\n                    <span className=\"font-medium\">{wallet.user.fullName || wallet.user.username || wallet.userId}</span>\r\n                  </UserHoverCard>\r\n                </div>\r\n              </div>\r\n            )}\r\n            {wallet?.createdAt && (\r\n              <div>\r\n                <div className=\"text-muted-foreground\">Ngày tạo</div>\r\n                <div>{formatDate(wallet.createdAt)}</div>\r\n              </div>\r\n            )}\r\n            {wallet?.updatedAt && (\r\n              <div>\r\n                <div className=\"text-muted-foreground\">Cập nhật</div>\r\n                <div>{formatDate(wallet.updatedAt)}</div>\r\n              </div>\r\n            )}\r\n          </div>\r\n        </div>\r\n      </HoverCardContent>\r\n    </HoverCard>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAEA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AAXA;;;;;;;;;;;AA6BO,SAAS,gBAAgB,EAAE,MAAM,EAAE,QAAQ,EAAE,aAAa,IAAI,EAAE,OAAO,IAAI,EAAE,QAAQ,EAAwB;IAClH,oEAAoE;IACpE,IAAI,CAAC,UAAU,CAAC,UAAU;QACxB,qBAAO;sBAAG;;IACZ;IAEA,oCAAoC;IACpC,MAAM,KAAK,QAAQ,MAAM;IAEzB,2CAA2C;IAC3C,MAAM,aAAa;QACjB,IAAI;QACJ,IAAI;QACJ,IAAI;IACN,CAAC,CAAC,KAAK;IAEP,+BAA+B;IAC/B,MAAM,iBAAiB,CAAC;QACtB,IAAI,CAAC,QAAQ,OAAO;QAEpB,MAAM,YAA8D;YAClE,UAAU;gBAAE,OAAO;gBAA+B,OAAO;YAAY;YACrE,UAAU;gBAAE,OAAO;gBAA2B,OAAO;YAAU;QACjE;QAEA,MAAM,aAAa,SAAS,CAAC,OAAO,IAAI;YAAE,OAAO;YAA6B,OAAO;QAAO;QAE5F,qBACE,uVAAC,0HAAA,CAAA,QAAK;YAAC,WAAW,WAAW,KAAK;sBAC/B,WAAW,KAAK;;;;;;IAGvB;IAEA,oBAAoB;IACpB,MAAM,aAAa,CAAC;QAClB,IAAI,CAAC,MAAM,OAAO;QAClB,IAAI;YACF,MAAM,UAAU,OAAO,SAAS,WAAW,IAAI,KAAK,QAAQ;YAC5D,OAAO,CAAA,GAAA,6MAAA,CAAA,SAAM,AAAD,EAAE,SAAS,oBAAoB;gBAAE,QAAQ,mMAAA,CAAA,KAAE;YAAC;QAC1D,EAAE,OAAO,OAAO;YACd,OAAO;QACT;IACF;IAEA,0BAA0B;IAC1B,MAAM,uBAAuB;QAC3B,IAAI,QAAQ,YAAY;YACtB,OAAO,CAAA,GAAA,6KAAA,CAAA,qBAAkB,AAAD,EAAE,OAAO,UAAU;QAC7C;QACA,OAAO,IAAI,UAAU,GAAG,MAAM;IAChC;IAEA,qBACE,uVAAC,kIAAA,CAAA,YAAS;;0BACR,uVAAC,kIAAA,CAAA,mBAAgB;gBAAC,OAAO;0BACvB,cAAA,uVAAC;oBAAI,WAAU;;wBACZ,4BACC,uVAAC,2HAAA,CAAA,SAAM;4BAAC,WAAW,CAAC,SAAS,EAAE,YAAY;;8CACzC,uVAAC,2HAAA,CAAA,cAAW;oCAAC,KAAK,CAAC,6CAA6C,EAAE,IAAI;;;;;;8CACtE,uVAAC,2HAAA,CAAA,iBAAc;8CACb,cAAA,uVAAC,0RAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;;;;;;;;;;;;wBAIvB;;;;;;;;;;;;0BAGL,uVAAC,kIAAA,CAAA,mBAAgB;gBAAC,WAAU;0BAC1B,cAAA,uVAAC;oBAAI,WAAU;;sCAEb,uVAAC;4BAAI,WAAU;;8CACb,uVAAC,2HAAA,CAAA,SAAM;oCAAC,WAAU;;sDAChB,uVAAC,2HAAA,CAAA,cAAW;4CAAC,KAAK,CAAC,6CAA6C,EAAE,IAAI;;;;;;sDACtE,uVAAC,2HAAA,CAAA,iBAAc;sDACb,cAAA,uVAAC,0RAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;;;;;;;;;;;;8CAGtB,uVAAC;;sDACC,uVAAC;4CAAI,WAAU;;gDACZ;gDACA,QAAQ,wBACP,uVAAC;oDAAK,WAAU;8DAAQ,eAAe,OAAO,MAAM;;;;;;;;;;;;wCAGvD,QAAQ,4BACP,uVAAC;4CAAI,WAAU;;8DACb,uVAAC,sSAAA,CAAA,aAAU;oDAAC,WAAU;;;;;;gDACrB,IAAI,UAAU,GAAG;gDAAI;;;;;;;;;;;;;;;;;;;sCAO9B,uVAAC;4BAAI,WAAU;;gCACZ,QAAQ,YAAY,2BACnB,uVAAC;;sDACC,uVAAC;4CAAI,WAAU;sDAAwB;;;;;;sDACvC,uVAAC;sDAAI,cAAA,uVAAC,wIAAA,CAAA,kBAAe;gDAAC,QAAQ,OAAO,OAAO;gDAAE,UAAS;;;;;;;;;;;;;;;;;gCAG1D,QAAQ,qBAAqB,2BAC5B,uVAAC;;sDACC,uVAAC;4CAAI,WAAU;sDAAwB;;;;;;sDACvC,uVAAC;sDAAI,cAAA,uVAAC,wIAAA,CAAA,kBAAe;gDAAC,QAAQ,OAAO,gBAAgB;gDAAE,UAAS;;;;;;;;;;;;;;;;;gCAGnE,QAAQ,sBACP,uVAAC;oCAAI,WAAU;;sDACb,uVAAC;4CAAI,WAAU;sDAAwB;;;;;;sDACvC,uVAAC;sDACC,cAAA,uVAAC,sJAAA,CAAA,gBAAa;gDAAC,MAAM,OAAO,IAAI;gDAAE,QAAQ,OAAO,MAAM;gDAAE,YAAY;gDAAO,MAAK;0DAC/E,cAAA,uVAAC;oDAAK,WAAU;8DAAe,OAAO,IAAI,CAAC,QAAQ,IAAI,OAAO,IAAI,CAAC,QAAQ,IAAI,OAAO,MAAM;;;;;;;;;;;;;;;;;;;;;;gCAKnG,QAAQ,2BACP,uVAAC;;sDACC,uVAAC;4CAAI,WAAU;sDAAwB;;;;;;sDACvC,uVAAC;sDAAK,WAAW,OAAO,SAAS;;;;;;;;;;;;gCAGpC,QAAQ,2BACP,uVAAC;;sDACC,uVAAC;4CAAI,WAAU;sDAAwB;;;;;;sDACvC,uVAAC;sDAAK,WAAW,OAAO,SAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQjD", "debugId": null}}, {"offset": {"line": 6401, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/bloomgoo/gold-exchange-platform/frontend/components/common/user/transactions/table/cell.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { useState } from \"react\";\r\nimport { ColumnDef, Row } from \"@tanstack/react-table\";\r\nimport { TransactionDto, TransactionType, TransactionStatus } from \"../../../admin/transactions/type\";\r\nimport { Badge } from \"@/components/ui/badge\";\r\nimport { <PERSON><PERSON> } from \"@/components/ui/button\";\r\nimport { Avatar, AvatarFallback, AvatarImage } from \"@/components/ui/avatar\";\r\nimport { Eye, ArrowDownToLine, ArrowUpFromLine, Wallet, FileText, XCircle, MoreHorizontal } from \"lucide-react\";\r\nimport { format } from \"date-fns\";\r\nimport { vi } from \"date-fns/locale\";\r\nimport { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from \"@/components/ui/dialog\";\r\nimport { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuSeparator, DropdownMenuTrigger } from \"@/components/ui/dropdown-menu\";\r\nimport { CurrencyDisplay } from \"@/components/ui/currency-display\";\r\nimport { DateTimeDisplay } from \"@/components/ui/date-time-display\";\r\nimport { WalletHoverCard } from \"../../../admin/wallets/wallet-hover-card\";\r\nimport { getWalletTypeLabel } from \"../../../admin/wallets/utils/wallet-type-helper\";\r\n\r\ninterface ActionsProps {\r\n  row: Row<TransactionDto>;\r\n  onViewDetail: (transaction: TransactionDto) => void;\r\n  onCancel?: (transaction: TransactionDto) => void;\r\n}\r\n\r\nfunction Actions({ row, onViewDetail, onCancel }: ActionsProps) {\r\n  const [showCancelDialog, setShowCancelDialog] = useState(false);\r\n  const transaction = row.original;\r\n\r\n  const handleCancel = (transaction: TransactionDto) => {\r\n    if (onCancel) {\r\n      onCancel(transaction);\r\n      setShowCancelDialog(false);\r\n    }\r\n  };\r\n\r\n  // Chỉ hiển thị nút hủy nếu giao dịch đang ở trạng thái PENDING\r\n  const canCancel = transaction.status === TransactionStatus.PENDING && onCancel;\r\n\r\n  return (\r\n    <>\r\n      {/* Dialog xác nhận hủy */}\r\n      <Dialog open={showCancelDialog} onOpenChange={setShowCancelDialog}>\r\n        <DialogContent>\r\n          <DialogHeader>\r\n            <DialogTitle>Xác nhận hủy giao dịch</DialogTitle>\r\n            <DialogDescription>\r\n              Bạn có chắc chắn muốn hủy giao dịch này không? Hành động này không thể hoàn tác.\r\n            </DialogDescription>\r\n          </DialogHeader>\r\n          <DialogFooter>\r\n            <Button variant=\"outline\" onClick={() => setShowCancelDialog(false)}>Không</Button>\r\n            <Button variant=\"destructive\" onClick={() => handleCancel(transaction)}>Hủy giao dịch</Button>\r\n          </DialogFooter>\r\n        </DialogContent>\r\n      </Dialog>\r\n\r\n      <div className=\"flex justify-end px-1 sticky-action-cell h-full items-center\">\r\n        {canCancel ? (\r\n          <DropdownMenu>\r\n            <DropdownMenuTrigger asChild>\r\n              <Button\r\n                variant=\"ghost\"\r\n                size=\"sm\"\r\n                className=\"flex h-6 w-6 p-0 data-[state=open]:bg-muted transition-colors hover:bg-muted\"\r\n              >\r\n                <MoreHorizontal className=\"h-3 w-3\" />\r\n                <span className=\"sr-only\">Mở menu</span>\r\n              </Button>\r\n            </DropdownMenuTrigger>\r\n            <DropdownMenuContent align=\"end\" className=\"p-1\">\r\n              <DropdownMenuItem onClick={() => onViewDetail(transaction)}>\r\n                <Eye className=\"mr-2 h-3.5 w-3.5\" />\r\n                <span className=\"flex-1 text-sm\">Chi tiết</span>\r\n              </DropdownMenuItem>\r\n              <DropdownMenuSeparator />\r\n              <DropdownMenuItem\r\n                onClick={() => setShowCancelDialog(true)}\r\n                className=\"text-destructive focus:text-destructive\"\r\n              >\r\n                <XCircle className=\"mr-2 h-3.5 w-3.5 text-destructive\" />\r\n                <span className=\"flex-1 text-sm text-destructive\">Hủy giao dịch</span>\r\n              </DropdownMenuItem>\r\n            </DropdownMenuContent>\r\n          </DropdownMenu>\r\n        ) : (\r\n          <Button\r\n            variant=\"ghost\"\r\n            size=\"icon\"\r\n            onClick={() => onViewDetail(transaction)}\r\n            title=\"Xem chi tiết\"\r\n          >\r\n            <Eye className=\"h-4 w-4\" />\r\n            <span className=\"sr-only\">Xem chi tiết</span>\r\n          </Button>\r\n        )}\r\n      </div>\r\n    </>\r\n  );\r\n}\r\n\r\ninterface ColumnsProps {\r\n  onViewDetail: (transaction: TransactionDto) => void;\r\n  onCancel?: (transaction: TransactionDto) => void;\r\n}\r\n\r\nexport function getUserTransactionCell({\r\n  onViewDetail,\r\n  onCancel,\r\n}: ColumnsProps): ColumnDef<TransactionDto>[] {\r\n  return [\r\n    {\r\n      accessorKey: \"id\",\r\n      header: \"Mã giao dịch\",\r\n      cell: ({ row }) => {\r\n        const transaction = row.original;\r\n        return (\r\n          <div className=\"flex items-center gap-2\">\r\n            <FileText className=\"h-4 w-4 text-muted-foreground\" />\r\n            <span className=\"font-medium max-w-[120px] truncate\" title={transaction.id}>\r\n              {transaction.id.substring(0, 8)}...\r\n            </span>\r\n          </div>\r\n        );\r\n      },\r\n      enableSorting: true,\r\n      enableHiding: false,\r\n    },\r\n    {\r\n      accessorKey: \"transactionType\",\r\n      header: \"Loại\",\r\n      cell: ({ row }) => {\r\n        const transaction = row.original;\r\n        const getTypeIcon = (type: TransactionType) => {\r\n          switch (type) {\r\n            case TransactionType.CREDIT:\r\n              return <ArrowDownToLine className=\"mr-2 h-4 w-4 text-green-500\" />;\r\n            case TransactionType.DEBIT:\r\n              return <ArrowUpFromLine className=\"mr-2 h-4 w-4 text-red-500\" />;\r\n            default:\r\n              return <Wallet className=\"mr-2 h-4 w-4\" />;\r\n          }\r\n        };\r\n\r\n        const getTypeLabel = (type: TransactionType) => {\r\n          switch (type) {\r\n            case TransactionType.CREDIT:\r\n              return \"Ghi có (Tăng)\";\r\n            case TransactionType.DEBIT:\r\n              return \"Ghi nợ (Giảm)\";\r\n            default:\r\n              return type;\r\n          }\r\n        };\r\n\r\n        return (\r\n          <div className=\"flex items-center gap-2\">\r\n            {getTypeIcon(transaction.transactionType)}\r\n            <span className=\"max-w-[100px] truncate\">\r\n              {getTypeLabel(transaction.transactionType)}\r\n            </span>\r\n          </div>\r\n        );\r\n      },\r\n      enableSorting: true,\r\n      enableHiding: true,\r\n    },\r\n    {\r\n      accessorKey: \"amount\",\r\n      header: \"Số tiền\",\r\n      cell: ({ row }) => {\r\n        const transaction = row.original;\r\n        const currency = 'VND';\r\n        const className = transaction.transactionType === TransactionType.CREDIT\r\n          ? 'text-green-600'\r\n          : transaction.transactionType === TransactionType.DEBIT\r\n            ? 'text-red-600'\r\n            : '';\r\n\r\n        return (\r\n          <CurrencyDisplay\r\n            amount={transaction.amount}\r\n            currency={currency}\r\n            className={`text-sm ${className}`}\r\n          />\r\n        );\r\n      },\r\n      enableSorting: true,\r\n      enableHiding: true,\r\n    },\r\n    {\r\n      accessorKey: \"walletId\",\r\n      header: \"Tài khoản\",\r\n      cell: ({ row }) => {\r\n        const transaction = row.original;\r\n        return (\r\n          <WalletHoverCard wallet={transaction.wallet} walletId={transaction.walletId} showAvatar={false} size=\"sm\">\r\n            <div className=\"flex items-center gap-2\">\r\n              <Wallet className=\"h-4 w-4 text-blue-500\" />\r\n              <div className=\"max-w-[150px] overflow-hidden\">\r\n                <div className=\"text-sm font-medium truncate\" title={transaction.wallet?.walletType ? getWalletTypeLabel(transaction.wallet.walletType) : transaction.walletId}>\r\n                  {transaction.wallet?.walletType\r\n                    ? getWalletTypeLabel(transaction.wallet.walletType)\r\n                    : transaction.wallet?.id\r\n                      ? transaction.wallet.id.substring(0, 8) + '...'\r\n                      : transaction.walletId.substring(0, 8) + '...'}\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </WalletHoverCard>\r\n        );\r\n      },\r\n      enableSorting: true,\r\n      enableHiding: true,\r\n    },\r\n    {\r\n      accessorKey: \"status\",\r\n      header: \"Trạng thái\",\r\n      cell: ({ row }) => {\r\n        const transaction = row.original;\r\n\r\n        // Hiển thị trạng thái dưới dạng badge mặc định\r\n        const getStatusColor = (status: TransactionStatus) => {\r\n          switch (status) {\r\n            case TransactionStatus.COMPLETED:\r\n              return 'bg-green-100 text-green-800';\r\n            case TransactionStatus.PENDING:\r\n              return 'bg-yellow-100 text-yellow-800';\r\n            case TransactionStatus.PROCESSING:\r\n              return 'bg-blue-100 text-blue-800';\r\n            case TransactionStatus.FAILED:\r\n              return 'bg-red-100 text-red-800';\r\n            case TransactionStatus.CANCELLED:\r\n              return 'bg-gray-100 text-gray-800';\r\n            default:\r\n              return 'bg-gray-100 text-gray-800';\r\n          }\r\n        };\r\n\r\n        const getStatusLabel = (status: TransactionStatus) => {\r\n          switch (status) {\r\n            case TransactionStatus.COMPLETED:\r\n              return 'Hoàn thành';\r\n            case TransactionStatus.PENDING:\r\n              return 'Đang chờ';\r\n            case TransactionStatus.PROCESSING:\r\n              return 'Đang xử lý';\r\n            case TransactionStatus.FAILED:\r\n              return 'Thất bại';\r\n            case TransactionStatus.CANCELLED:\r\n              return 'Đã hủy';\r\n            default:\r\n              return status;\r\n          }\r\n        };\r\n\r\n        return (\r\n          <Badge className={`${getStatusColor(transaction.status)}`}>\r\n            {getStatusLabel(transaction.status)}\r\n          </Badge>\r\n        );\r\n      },\r\n      enableSorting: true,\r\n      enableHiding: true,\r\n    },\r\n    {\r\n      accessorKey: \"createdAt\",\r\n      header: \"Ngày tạo\",\r\n      cell: ({ row }) => {\r\n        const transaction = row.original;\r\n        return (\r\n          <DateTimeDisplay\r\n            date={transaction.createdAt}\r\n            className=\"text-sm\"\r\n            timeClassName=\"text-xs text-muted-foreground\"\r\n          />\r\n        );\r\n      },\r\n      enableSorting: true,\r\n      enableHiding: true,\r\n    },\r\n    {\r\n      id: \"actions\",\r\n      size: 40,\r\n      enableHiding: false,\r\n      header: () => null,\r\n      cell: ({ row }) => (\r\n        <Actions\r\n          row={row}\r\n          onViewDetail={onViewDetail}\r\n          onCancel={onCancel}\r\n        />\r\n      ),\r\n      meta: {\r\n        isSticky: true,\r\n        position: 'right',\r\n        header: \"Thao tác\"\r\n      }\r\n    },\r\n  ];\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AAEA;AAAA;AACA;AACA;AAEA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAGA;AACA;AACA;AACA;AACA;AACA;AAhBA;;;;;;;;;;;;;AAwBA,SAAS,QAAQ,EAAE,GAAG,EAAE,YAAY,EAAE,QAAQ,EAAgB;IAC5D,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,8SAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,cAAc,IAAI,QAAQ;IAEhC,MAAM,eAAe,CAAC;QACpB,IAAI,UAAU;YACZ,SAAS;YACT,oBAAoB;QACtB;IACF;IAEA,+DAA+D;IAC/D,MAAM,YAAY,YAAY,MAAM,KAAK,2KAAA,CAAA,oBAAiB,CAAC,OAAO,IAAI;IAEtE,qBACE;;0BAEE,uVAAC,2HAAA,CAAA,SAAM;gBAAC,MAAM;gBAAkB,cAAc;0BAC5C,cAAA,uVAAC,2HAAA,CAAA,gBAAa;;sCACZ,uVAAC,2HAAA,CAAA,eAAY;;8CACX,uVAAC,2HAAA,CAAA,cAAW;8CAAC;;;;;;8CACb,uVAAC,2HAAA,CAAA,oBAAiB;8CAAC;;;;;;;;;;;;sCAIrB,uVAAC,2HAAA,CAAA,eAAY;;8CACX,uVAAC,2HAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAU,SAAS,IAAM,oBAAoB;8CAAQ;;;;;;8CACrE,uVAAC,2HAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAc,SAAS,IAAM,aAAa;8CAAc;;;;;;;;;;;;;;;;;;;;;;;0BAK9E,uVAAC;gBAAI,WAAU;0BACZ,0BACC,uVAAC,qIAAA,CAAA,eAAY;;sCACX,uVAAC,qIAAA,CAAA,sBAAmB;4BAAC,OAAO;sCAC1B,cAAA,uVAAC,2HAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,WAAU;;kDAEV,uVAAC,oSAAA,CAAA,iBAAc;wCAAC,WAAU;;;;;;kDAC1B,uVAAC;wCAAK,WAAU;kDAAU;;;;;;;;;;;;;;;;;sCAG9B,uVAAC,qIAAA,CAAA,sBAAmB;4BAAC,OAAM;4BAAM,WAAU;;8CACzC,uVAAC,qIAAA,CAAA,mBAAgB;oCAAC,SAAS,IAAM,aAAa;;sDAC5C,uVAAC,oRAAA,CAAA,MAAG;4CAAC,WAAU;;;;;;sDACf,uVAAC;4CAAK,WAAU;sDAAiB;;;;;;;;;;;;8CAEnC,uVAAC,qIAAA,CAAA,wBAAqB;;;;;8CACtB,uVAAC,qIAAA,CAAA,mBAAgB;oCACf,SAAS,IAAM,oBAAoB;oCACnC,WAAU;;sDAEV,uVAAC,gSAAA,CAAA,UAAO;4CAAC,WAAU;;;;;;sDACnB,uVAAC;4CAAK,WAAU;sDAAkC;;;;;;;;;;;;;;;;;;;;;;;yCAKxD,uVAAC,2HAAA,CAAA,SAAM;oBACL,SAAQ;oBACR,MAAK;oBACL,SAAS,IAAM,aAAa;oBAC5B,OAAM;;sCAEN,uVAAC,oRAAA,CAAA,MAAG;4BAAC,WAAU;;;;;;sCACf,uVAAC;4BAAK,WAAU;sCAAU;;;;;;;;;;;;;;;;;;;AAMtC;AAOO,SAAS,uBAAuB,EACrC,YAAY,EACZ,QAAQ,EACK;IACb,OAAO;QACL;YACE,aAAa;YACb,QAAQ;YACR,MAAM,CAAC,EAAE,GAAG,EAAE;gBACZ,MAAM,cAAc,IAAI,QAAQ;gBAChC,qBACE,uVAAC;oBAAI,WAAU;;sCACb,uVAAC,kSAAA,CAAA,WAAQ;4BAAC,WAAU;;;;;;sCACpB,uVAAC;4BAAK,WAAU;4BAAqC,OAAO,YAAY,EAAE;;gCACvE,YAAY,EAAE,CAAC,SAAS,CAAC,GAAG;gCAAG;;;;;;;;;;;;;YAIxC;YACA,eAAe;YACf,cAAc;QAChB;QACA;YACE,aAAa;YACb,QAAQ;YACR,MAAM,CAAC,EAAE,GAAG,EAAE;gBACZ,MAAM,cAAc,IAAI,QAAQ;gBAChC,MAAM,cAAc,CAAC;oBACnB,OAAQ;wBACN,KAAK,2KAAA,CAAA,kBAAe,CAAC,MAAM;4BACzB,qBAAO,uVAAC,wTAAA,CAAA,kBAAe;gCAAC,WAAU;;;;;;wBACpC,KAAK,2KAAA,CAAA,kBAAe,CAAC,KAAK;4BACxB,qBAAO,uVAAC,wTAAA,CAAA,kBAAe;gCAAC,WAAU;;;;;;wBACpC;4BACE,qBAAO,uVAAC,0RAAA,CAAA,SAAM;gCAAC,WAAU;;;;;;oBAC7B;gBACF;gBAEA,MAAM,eAAe,CAAC;oBACpB,OAAQ;wBACN,KAAK,2KAAA,CAAA,kBAAe,CAAC,MAAM;4BACzB,OAAO;wBACT,KAAK,2KAAA,CAAA,kBAAe,CAAC,KAAK;4BACxB,OAAO;wBACT;4BACE,OAAO;oBACX;gBACF;gBAEA,qBACE,uVAAC;oBAAI,WAAU;;wBACZ,YAAY,YAAY,eAAe;sCACxC,uVAAC;4BAAK,WAAU;sCACb,aAAa,YAAY,eAAe;;;;;;;;;;;;YAIjD;YACA,eAAe;YACf,cAAc;QAChB;QACA;YACE,aAAa;YACb,QAAQ;YACR,MAAM,CAAC,EAAE,GAAG,EAAE;gBACZ,MAAM,cAAc,IAAI,QAAQ;gBAChC,MAAM,WAAW;gBACjB,MAAM,YAAY,YAAY,eAAe,KAAK,2KAAA,CAAA,kBAAe,CAAC,MAAM,GACpE,mBACA,YAAY,eAAe,KAAK,2KAAA,CAAA,kBAAe,CAAC,KAAK,GACnD,iBACA;gBAEN,qBACE,uVAAC,wIAAA,CAAA,kBAAe;oBACd,QAAQ,YAAY,MAAM;oBAC1B,UAAU;oBACV,WAAW,CAAC,QAAQ,EAAE,WAAW;;;;;;YAGvC;YACA,eAAe;YACf,cAAc;QAChB;QACA;YACE,aAAa;YACb,QAAQ;YACR,MAAM,CAAC,EAAE,GAAG,EAAE;gBACZ,MAAM,cAAc,IAAI,QAAQ;gBAChC,qBACE,uVAAC,oKAAA,CAAA,kBAAe;oBAAC,QAAQ,YAAY,MAAM;oBAAE,UAAU,YAAY,QAAQ;oBAAE,YAAY;oBAAO,MAAK;8BACnG,cAAA,uVAAC;wBAAI,WAAU;;0CACb,uVAAC,0RAAA,CAAA,SAAM;gCAAC,WAAU;;;;;;0CAClB,uVAAC;gCAAI,WAAU;0CACb,cAAA,uVAAC;oCAAI,WAAU;oCAA+B,OAAO,YAAY,MAAM,EAAE,aAAa,CAAA,GAAA,6KAAA,CAAA,qBAAkB,AAAD,EAAE,YAAY,MAAM,CAAC,UAAU,IAAI,YAAY,QAAQ;8CAC3J,YAAY,MAAM,EAAE,aACjB,CAAA,GAAA,6KAAA,CAAA,qBAAkB,AAAD,EAAE,YAAY,MAAM,CAAC,UAAU,IAChD,YAAY,MAAM,EAAE,KAClB,YAAY,MAAM,CAAC,EAAE,CAAC,SAAS,CAAC,GAAG,KAAK,QACxC,YAAY,QAAQ,CAAC,SAAS,CAAC,GAAG,KAAK;;;;;;;;;;;;;;;;;;;;;;YAMzD;YACA,eAAe;YACf,cAAc;QAChB;QACA;YACE,aAAa;YACb,QAAQ;YACR,MAAM,CAAC,EAAE,GAAG,EAAE;gBACZ,MAAM,cAAc,IAAI,QAAQ;gBAEhC,+CAA+C;gBAC/C,MAAM,iBAAiB,CAAC;oBACtB,OAAQ;wBACN,KAAK,2KAAA,CAAA,oBAAiB,CAAC,SAAS;4BAC9B,OAAO;wBACT,KAAK,2KAAA,CAAA,oBAAiB,CAAC,OAAO;4BAC5B,OAAO;wBACT,KAAK,2KAAA,CAAA,oBAAiB,CAAC,UAAU;4BAC/B,OAAO;wBACT,KAAK,2KAAA,CAAA,oBAAiB,CAAC,MAAM;4BAC3B,OAAO;wBACT,KAAK,2KAAA,CAAA,oBAAiB,CAAC,SAAS;4BAC9B,OAAO;wBACT;4BACE,OAAO;oBACX;gBACF;gBAEA,MAAM,iBAAiB,CAAC;oBACtB,OAAQ;wBACN,KAAK,2KAAA,CAAA,oBAAiB,CAAC,SAAS;4BAC9B,OAAO;wBACT,KAAK,2KAAA,CAAA,oBAAiB,CAAC,OAAO;4BAC5B,OAAO;wBACT,KAAK,2KAAA,CAAA,oBAAiB,CAAC,UAAU;4BAC/B,OAAO;wBACT,KAAK,2KAAA,CAAA,oBAAiB,CAAC,MAAM;4BAC3B,OAAO;wBACT,KAAK,2KAAA,CAAA,oBAAiB,CAAC,SAAS;4BAC9B,OAAO;wBACT;4BACE,OAAO;oBACX;gBACF;gBAEA,qBACE,uVAAC,0HAAA,CAAA,QAAK;oBAAC,WAAW,GAAG,eAAe,YAAY,MAAM,GAAG;8BACtD,eAAe,YAAY,MAAM;;;;;;YAGxC;YACA,eAAe;YACf,cAAc;QAChB;QACA;YACE,aAAa;YACb,QAAQ;YACR,MAAM,CAAC,EAAE,GAAG,EAAE;gBACZ,MAAM,cAAc,IAAI,QAAQ;gBAChC,qBACE,uVAAC,4IAAA,CAAA,kBAAe;oBACd,MAAM,YAAY,SAAS;oBAC3B,WAAU;oBACV,eAAc;;;;;;YAGpB;YACA,eAAe;YACf,cAAc;QAChB;QACA;YACE,IAAI;YACJ,MAAM;YACN,cAAc;YACd,QAAQ,IAAM;YACd,MAAM,CAAC,EAAE,GAAG,EAAE,iBACZ,uVAAC;oBACC,KAAK;oBACL,cAAc;oBACd,UAAU;;;;;;YAGd,MAAM;gBACJ,UAAU;gBACV,UAAU;gBACV,QAAQ;YACV;QACF;KACD;AACH", "debugId": null}}, {"offset": {"line": 6925, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/bloomgoo/gold-exchange-platform/frontend/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction Card({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card\"\r\n      className={cn(\r\n        \"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CardHeader({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-header\"\r\n      className={cn(\r\n        \"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CardTitle({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-title\"\r\n      className={cn(\"leading-none font-semibold\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CardDescription({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-description\"\r\n      className={cn(\"text-muted-foreground text-sm\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CardAction({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-action\"\r\n      className={cn(\r\n        \"col-start-2 row-span-2 row-start-1 self-start justify-self-end\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CardContent({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-content\"\r\n      className={cn(\"px-6\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CardFooter({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-footer\"\r\n      className={cn(\"flex items-center px-6 [.border-t]:pt-6\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport {\r\n  Card,\r\n  CardHeader,\r\n  CardFooter,\r\n  CardTitle,\r\n  CardAction,\r\n  CardDescription,\r\n  CardContent,\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;AAAA;;;AAEA,SAAS,KAAK,EAAE,SAAS,EAAE,GAAG,OAAoC;IAChE,qBACE,uVAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EACV,qFACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,uVAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EACV,8JACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAoC;IACrE,qBACE,uVAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC3E,qBACE,uVAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,uVAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,uVAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QACrB,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,uVAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 7023, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/bloomgoo/gold-exchange-platform/frontend/components/info-card.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React from 'react';\r\nimport {\r\n  Card,\r\n  CardContent,\r\n  CardDescription,\r\n  CardHeader,\r\n  CardTitle,\r\n} from '@/components/ui/card';\r\nimport { cn } from '@/lib/utils';\r\n\r\ninterface InfoCardProps {\r\n  title: string;\r\n  description?: string;\r\n  children: React.ReactNode;\r\n  className?: string;\r\n}\r\n\r\nexport function InfoCard({\r\n  title,\r\n  description,\r\n  children,\r\n  className = '',\r\n}: InfoCardProps) {\r\n  return (\r\n    <Card className={cn(\"shadow-none border-none py-2\", className)}>\r\n      <CardHeader className=\"border-l-4 border-l-primary pl-4\">\r\n        <CardTitle className=\"text-lg\">{title}</CardTitle>\r\n        {description && <CardDescription>{description}</CardDescription>}\r\n      </CardHeader>\r\n      <CardContent className=\"space-y-4\">\r\n        {children}\r\n      </CardContent>\r\n    </Card>\r\n  );\r\n} "], "names": [], "mappings": ";;;;AAGA;AAOA;AAAA;AAVA;;;;AAmBO,SAAS,SAAS,EACvB,KAAK,EACL,WAAW,EACX,QAAQ,EACR,YAAY,EAAE,EACA;IACd,qBACE,uVAAC,yHAAA,CAAA,OAAI;QAAC,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EAAE,gCAAgC;;0BAClD,uVAAC,yHAAA,CAAA,aAAU;gBAAC,WAAU;;kCACpB,uVAAC,yHAAA,CAAA,YAAS;wBAAC,WAAU;kCAAW;;;;;;oBAC/B,6BAAe,uVAAC,yHAAA,CAAA,kBAAe;kCAAE;;;;;;;;;;;;0BAEpC,uVAAC,yHAAA,CAAA,cAAW;gBAAC,WAAU;0BACpB;;;;;;;;;;;;AAIT", "debugId": null}}, {"offset": {"line": 7083, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/bloomgoo/gold-exchange-platform/frontend/components/common/admin/transactions/reference-info.tsx"], "sourcesContent": ["import { Badge } from '@/components/ui/badge';\r\nimport { ExternalLink } from 'lucide-react';\r\nimport { TransactionReferenceType } from './type/transaction.dto';\r\nimport Link from 'next/link';\r\n\r\n// Định nghĩa interface cho thông tin tham chiếu\r\ninterface ReferenceInfo {\r\n  id: string;\r\n  title: string;\r\n  subtitle?: string;\r\n  status?: string;\r\n  amount?: string | number;\r\n  url?: string;\r\n}\r\n\r\ninterface ReferenceInfoProps {\r\n  referenceType?: TransactionReferenceType | string;\r\n  referenceId?: string;\r\n  referenceInfo?: ReferenceInfo;\r\n}\r\n\r\nexport function ReferenceInfoComponent({ referenceType, referenceId, referenceInfo }: ReferenceInfoProps) {\r\n  // Nếu không có referenceInfo, hiển thị thông báo\r\n  if (!referenceInfo) {\r\n    if (!referenceId || !referenceType) {\r\n      return (\r\n        <div className=\"text-sm text-muted-foreground min-h-[52px]\">\r\n          <div>Không có thông tin tham chiếu</div>\r\n        </div>\r\n      );\r\n    }\r\n\r\n    return (\r\n      <div className=\"text-sm text-muted-foreground min-h-[52px]\">\r\n        <div>Không có thông tin</div>\r\n        <div className=\"text-xs\">{referenceId?.substring(0, 8) || 'N/A'}</div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  const info = referenceInfo;\r\n\r\n  return (\r\n    <div className=\"space-y-1 min-h-[52px]\">\r\n      <div className=\"flex items-center gap-2\">\r\n        <div className=\"font-medium text-sm truncate max-w-[150px]\" title={info.title}>\r\n          {info.title}\r\n        </div>\r\n      </div>\r\n      {info.amount && (\r\n        <div className=\"text-xs font-medium\">\r\n          {typeof info.amount === 'number'\r\n            ? new Intl.NumberFormat('vi-VN', { style: 'currency', currency: 'VND' }).format(info.amount)\r\n            : info.amount}\r\n        </div>\r\n      )}\r\n    </div>\r\n  );\r\n}\r\n\r\n// Hàm chuyển đổi trạng thái thành className cho Badge\r\nfunction getStatusClassName(status: string): string {\r\n  const statusLower = status.toLowerCase();\r\n\r\n  if (statusLower.includes('complete') || statusLower.includes('success') || statusLower === 'active' || statusLower === 'delivered') {\r\n    return 'bg-green-100 text-green-800';\r\n  }\r\n\r\n  if (statusLower.includes('pending') || statusLower.includes('wait')) {\r\n    return 'bg-yellow-100 text-yellow-800';\r\n  }\r\n\r\n  if (statusLower.includes('process') || statusLower.includes('shipping') || statusLower.includes('shipped')) {\r\n    return 'bg-blue-100 text-blue-800';\r\n  }\r\n\r\n  if (statusLower.includes('cancel') || statusLower.includes('fail') || statusLower.includes('reject')) {\r\n    return 'bg-red-100 text-red-800';\r\n  }\r\n\r\n  if (statusLower.includes('info') || statusLower.includes('verify')) {\r\n    return 'bg-purple-100 text-purple-800';\r\n  }\r\n\r\n  return 'bg-gray-100 text-gray-800';\r\n}\r\n\r\n// Hàm chuyển đổi trạng thái thành nhãn hiển thị\r\nfunction getStatusLabel(status: string): string {\r\n  const statusMap: Record<string, string> = {\r\n    // Trạng thái giao dịch\r\n    'PENDING': 'Đang chờ',\r\n    'PROCESSING': 'Đang xử lý',\r\n    'COMPLETED': 'Hoàn thành',\r\n    'CANCELLED': 'Đã hủy',\r\n    'FAILED': 'Thất bại',\r\n\r\n    // Trạng thái đơn hàng\r\n    'WAIT_PAYMENT': 'Chờ thanh toán',\r\n    'WAIT_APPROVE': 'Chờ duyệt',\r\n    'APPROVED': 'Đã duyệt',\r\n    'REJECTED': 'Từ chối',\r\n\r\n    // Trạng thái giao hàng\r\n    'SHIPPED': 'Đã gửi hàng',\r\n    'SHIPPING': 'Đang giao hàng',\r\n    'DELIVERED': 'Đã giao hàng',\r\n\r\n    // Trạng thái chung\r\n    'ACTIVE': 'Hoạt động',\r\n    'INACTIVE': 'Không hoạt động',\r\n    'SUSPENDED': 'Tạm ngưng',\r\n    'VERIFIED': 'Đã xác minh',\r\n  };\r\n\r\n  return statusMap[status] || status;\r\n}\r\n"], "names": [], "mappings": ";;;;;AAqBO,SAAS,uBAAuB,EAAE,aAAa,EAAE,WAAW,EAAE,aAAa,EAAsB;IACtG,iDAAiD;IACjD,IAAI,CAAC,eAAe;QAClB,IAAI,CAAC,eAAe,CAAC,eAAe;YAClC,qBACE,uVAAC;gBAAI,WAAU;0BACb,cAAA,uVAAC;8BAAI;;;;;;;;;;;QAGX;QAEA,qBACE,uVAAC;YAAI,WAAU;;8BACb,uVAAC;8BAAI;;;;;;8BACL,uVAAC;oBAAI,WAAU;8BAAW,aAAa,UAAU,GAAG,MAAM;;;;;;;;;;;;IAGhE;IAEA,MAAM,OAAO;IAEb,qBACE,uVAAC;QAAI,WAAU;;0BACb,uVAAC;gBAAI,WAAU;0BACb,cAAA,uVAAC;oBAAI,WAAU;oBAA6C,OAAO,KAAK,KAAK;8BAC1E,KAAK,KAAK;;;;;;;;;;;YAGd,KAAK,MAAM,kBACV,uVAAC;gBAAI,WAAU;0BACZ,OAAO,KAAK,MAAM,KAAK,WACpB,IAAI,KAAK,YAAY,CAAC,SAAS;oBAAE,OAAO;oBAAY,UAAU;gBAAM,GAAG,MAAM,CAAC,KAAK,MAAM,IACzF,KAAK,MAAM;;;;;;;;;;;;AAKzB;AAEA,sDAAsD;AACtD,SAAS,mBAAmB,MAAc;IACxC,MAAM,cAAc,OAAO,WAAW;IAEtC,IAAI,YAAY,QAAQ,CAAC,eAAe,YAAY,QAAQ,CAAC,cAAc,gBAAgB,YAAY,gBAAgB,aAAa;QAClI,OAAO;IACT;IAEA,IAAI,YAAY,QAAQ,CAAC,cAAc,YAAY,QAAQ,CAAC,SAAS;QACnE,OAAO;IACT;IAEA,IAAI,YAAY,QAAQ,CAAC,cAAc,YAAY,QAAQ,CAAC,eAAe,YAAY,QAAQ,CAAC,YAAY;QAC1G,OAAO;IACT;IAEA,IAAI,YAAY,QAAQ,CAAC,aAAa,YAAY,QAAQ,CAAC,WAAW,YAAY,QAAQ,CAAC,WAAW;QACpG,OAAO;IACT;IAEA,IAAI,YAAY,QAAQ,CAAC,WAAW,YAAY,QAAQ,CAAC,WAAW;QAClE,OAAO;IACT;IAEA,OAAO;AACT;AAEA,gDAAgD;AAChD,SAAS,eAAe,MAAc;IACpC,MAAM,YAAoC;QACxC,uBAAuB;QACvB,WAAW;QACX,cAAc;QACd,aAAa;QACb,aAAa;QACb,UAAU;QAEV,sBAAsB;QACtB,gBAAgB;QAChB,gBAAgB;QAChB,YAAY;QACZ,YAAY;QAEZ,uBAAuB;QACvB,WAAW;QACX,YAAY;QACZ,aAAa;QAEb,mBAAmB;QACnB,UAAU;QACV,YAAY;QACZ,aAAa;QACb,YAAY;IACd;IAEA,OAAO,SAAS,CAAC,OAAO,IAAI;AAC9B", "debugId": null}}, {"offset": {"line": 7222, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/bloomgoo/gold-exchange-platform/frontend/components/common/user/transactions/detail-sheet.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { InfoCard } from '@/components/info-card';\r\nimport { But<PERSON> } from '@/components/ui/button';\r\nimport { CurrencyDisplay } from '@/components/ui/currency-display';\r\nimport { DateTimeDisplay } from '@/components/ui/date-time-display';\r\nimport { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog';\r\nimport { Sheet, SheetContent, SheetDescription, SheetHeader, SheetTitle } from '@/components/ui/sheet';\r\nimport { api } from '@/lib/api';\r\nimport {\r\n  ArrowDownToLine,\r\n  ArrowUpFromLine,\r\n  Clock,\r\n  CreditCard,\r\n  FileText,\r\n  Loader2,\r\n  Tag,\r\n  Wallet,\r\n  XCircle\r\n} from 'lucide-react';\r\nimport { useEffect, useState } from 'react';\r\nimport { toast } from 'sonner';\r\nimport { ReferenceInfoComponent } from '../../admin/transactions/reference-info';\r\nimport { TransactionDto, TransactionStatus, TransactionType } from '../../admin/transactions/type';\r\n\r\ninterface DetailSheetProps {\r\n  transaction?: TransactionDto;\r\n  isOpen: boolean;\r\n  onClose: () => void;\r\n  onCancel?: (transaction: TransactionDto) => void;\r\n}\r\n\r\nexport function DetailSheet({ transaction: initialTransaction, isOpen, onClose, onCancel }: DetailSheetProps) {\r\n  const [loading, setLoading] = useState(false);\r\n  const [transaction, setTransaction] = useState<TransactionDto | null | undefined>(initialTransaction);\r\n  const [showCancelDialog, setShowCancelDialog] = useState(false);\r\n\r\n  // Tải thông tin đầy đủ của giao dịch\r\n  useEffect(() => {\r\n    if (isOpen && initialTransaction?.id) {\r\n      setLoading(true);\r\n      api.get<TransactionDto>(`transactions/${initialTransaction.id}?relations=user,creator,updater,deleter,wallet,paymentMethod`)\r\n        .then(response => {\r\n          setTransaction(response);\r\n          setLoading(false);\r\n        })\r\n        .catch(error => {\r\n          console.error('Error fetching transaction details:', error);\r\n          toast.error('Không thể tải thông tin chi tiết giao dịch');\r\n          setLoading(false);\r\n        });\r\n    } else {\r\n      setTransaction(initialTransaction);\r\n    }\r\n  }, [isOpen, initialTransaction?.id]);\r\n\r\n  // Xử lý khi không có dữ liệu\r\n  if (!transaction) {\r\n    return null;\r\n  }\r\n\r\n  // Xử lý hủy giao dịch\r\n  const handleCancel = () => {\r\n    if (onCancel && transaction) {\r\n      onCancel(transaction);\r\n      setShowCancelDialog(false);\r\n    }\r\n  };\r\n\r\n  // Kiểm tra xem giao dịch có thể hủy không\r\n  const canCancel = transaction.status === TransactionStatus.PENDING && onCancel;\r\n\r\n  // Hàm helper để hiển thị icon loại giao dịch\r\n  const getTypeIcon = (type: TransactionType) => {\r\n    switch (type) {\r\n      case TransactionType.CREDIT:\r\n        return <ArrowDownToLine className=\"h-5 w-5 text-green-500\" />;\r\n      case TransactionType.DEBIT:\r\n        return <ArrowUpFromLine className=\"h-5 w-5 text-red-500\" />;\r\n      default:\r\n        return <Wallet className=\"h-5 w-5\" />;\r\n    }\r\n  };\r\n\r\n  // Hàm helper để hiển thị nhãn loại giao dịch\r\n  const getTypeLabel = (type: TransactionType) => {\r\n    switch (type) {\r\n      case TransactionType.CREDIT:\r\n        return 'Ghi có (Tăng)';\r\n      case TransactionType.DEBIT:\r\n        return 'Ghi nợ (Giảm)';\r\n      default:\r\n        return type;\r\n    }\r\n  };\r\n\r\n  // Hàm helper để hiển thị nhãn trạng thái\r\n  const getStatusLabel = (status: TransactionStatus) => {\r\n    switch (status) {\r\n      case TransactionStatus.COMPLETED:\r\n        return 'Hoàn thành';\r\n      case TransactionStatus.PENDING:\r\n        return 'Đang chờ';\r\n      case TransactionStatus.PROCESSING:\r\n        return 'Đang xử lý';\r\n      case TransactionStatus.FAILED:\r\n        return 'Thất bại';\r\n      case TransactionStatus.CANCELLED:\r\n        return 'Đã hủy';\r\n      default:\r\n        return status;\r\n    }\r\n  };\r\n\r\n  // Hàm helper để hiển thị màu trạng thái\r\n  const getStatusColor = (status: TransactionStatus) => {\r\n    switch (status) {\r\n      case TransactionStatus.COMPLETED:\r\n        return 'bg-green-100 text-green-800';\r\n      case TransactionStatus.PENDING:\r\n        return 'bg-yellow-100 text-yellow-800';\r\n      case TransactionStatus.PROCESSING:\r\n        return 'bg-blue-100 text-blue-800';\r\n      case TransactionStatus.FAILED:\r\n        return 'bg-red-100 text-red-800';\r\n      case TransactionStatus.CANCELLED:\r\n        return 'bg-gray-100 text-gray-800';\r\n      default:\r\n        return 'bg-gray-100 text-gray-800';\r\n    }\r\n  };\r\n\r\n  return (\r\n    <>\r\n      {/* Dialog xác nhận hủy */}\r\n      <Dialog open={showCancelDialog} onOpenChange={setShowCancelDialog}>\r\n        <DialogContent>\r\n          <DialogHeader>\r\n            <DialogTitle>Xác nhận hủy giao dịch</DialogTitle>\r\n            <DialogDescription>\r\n              Bạn có chắc chắn muốn hủy giao dịch này không? Hành động này không thể hoàn tác.\r\n            </DialogDescription>\r\n          </DialogHeader>\r\n          <DialogFooter>\r\n            <Button variant=\"outline\" onClick={() => setShowCancelDialog(false)}>Không</Button>\r\n            <Button variant=\"destructive\" onClick={handleCancel}>Hủy giao dịch</Button>\r\n          </DialogFooter>\r\n        </DialogContent>\r\n      </Dialog>\r\n\r\n      <Sheet open={isOpen} onOpenChange={(open) => !open && onClose()}>\r\n        <SheetContent className=\"w-full sm:max-w-xl md:max-w-2xl lg:max-w-3xl flex flex-col p-0\">\r\n          {/* Header cố định */}\r\n          <SheetHeader className=\"px-0 py-0 border-b\">\r\n            <div className=\"flex items-center gap-4 px-4 py-4\">\r\n              <div className=\"relative\">\r\n                <div className=\"h-16 w-16 rounded-full bg-muted flex items-center justify-center\">\r\n                  {getTypeIcon(transaction.transactionType)}\r\n                </div>\r\n              </div>\r\n              <div className=\"flex-1\">\r\n                <SheetTitle className=\"text-md font-semibold\">\r\n                  {loading ? (\r\n                    <div className=\"flex items-center gap-2\">\r\n                      <Loader2 className=\"h-4 w-4 animate-spin\" />\r\n                      <span>Đang tải...</span>\r\n                    </div>\r\n                  ) : (\r\n                    `Giao dịch ${getTypeLabel(transaction.transactionType)}`\r\n                  )}\r\n                </SheetTitle>\r\n                <SheetDescription className=\"flex items-center gap-2\">\r\n                  {loading ? (\r\n                    <div className=\"flex items-center gap-2\">\r\n                      <Loader2 className=\"h-3 w-3 animate-spin\" />\r\n                      <span>Đang tải thông tin...</span>\r\n                    </div>\r\n                  ) : (\r\n                    <>\r\n                      <span className={`px-2 py-1 rounded-full text-xs ${getStatusColor(transaction.status)}`}>\r\n                        {getStatusLabel(transaction.status)}\r\n                      </span>\r\n                      <span className=\"text-sm\">\r\n                        <DateTimeDisplay date={transaction.createdAt} format=\"dateTime\" />\r\n                      </span>\r\n                    </>\r\n                  )}\r\n                </SheetDescription>\r\n              </div>\r\n            </div>\r\n\r\n            {/* Đường kẻ phân tách */}\r\n            <div className=\"h-px w-full bg-border\"></div>\r\n\r\n            {/* Các nút chức năng */}\r\n            {canCancel && (\r\n              <div className=\"flex flex-wrap items-center gap-2 px-4 py-2\">\r\n                <Button\r\n                  variant=\"outline\"\r\n                  size=\"sm\"\r\n                  className=\"flex items-center gap-1 text-destructive border-destructive/30 hover:bg-destructive/10\"\r\n                  onClick={() => setShowCancelDialog(true)}\r\n                  disabled={loading}\r\n                >\r\n                  <XCircle className=\"h-3.5 w-3.5 text-destructive\" />\r\n                  <span>Hủy giao dịch</span>\r\n                </Button>\r\n              </div>\r\n            )}\r\n          </SheetHeader>\r\n\r\n          {/* Body có thể scroll */}\r\n          <div className=\"flex-1 overflow-y-auto px-4\">\r\n            {loading ? (\r\n              <div className=\"flex flex-col items-center justify-center h-full py-8 gap-4\">\r\n                <Loader2 className=\"h-8 w-8 animate-spin text-primary\" />\r\n                <p className=\"text-muted-foreground\">Đang tải thông tin chi tiết...</p>\r\n              </div>\r\n            ) : (\r\n              <>\r\n                {/* Thông tin cơ bản */}\r\n                <InfoCard\r\n                  title=\"Thông tin giao dịch\"\r\n                  description=\"Thông tin cơ bản của giao dịch tài chính\"\r\n                  className=\"py-4\"\r\n                >\r\n                  <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\r\n                    <div className=\"space-y-1\">\r\n                      <div className=\"text-xs font-medium text-muted-foreground flex items-center gap-1\">\r\n                        <Tag className=\"h-3.5 w-3.5\" /> Mã giao dịch\r\n                      </div>\r\n                      <div className=\"text-md\">{transaction.id}</div>\r\n                    </div>\r\n                    <div className=\"space-y-1\">\r\n                      <div className=\"text-xs font-medium text-muted-foreground flex items-center gap-1\">\r\n                        <ArrowUpFromLine className=\"h-3.5 w-3.5\" /> Loại giao dịch\r\n                      </div>\r\n                      <div className=\"text-md flex items-center gap-1\">\r\n                        {getTypeIcon(transaction.transactionType)}\r\n                        <span>{getTypeLabel(transaction.transactionType)}</span>\r\n                      </div>\r\n                    </div>\r\n                    <div className=\"space-y-1\">\r\n                      <div className=\"text-xs font-medium text-muted-foreground flex items-center gap-1\">\r\n                        <CreditCard className=\"h-3.5 w-3.5\" /> Số tiền\r\n                      </div>\r\n                      <div className={`text-md ${transaction.transactionType === TransactionType.CREDIT ? 'text-green-600' : transaction.transactionType === TransactionType.DEBIT ? 'text-red-600' : ''}`}>\r\n                        <CurrencyDisplay amount={transaction.amount} />\r\n                      </div>\r\n                    </div>\r\n                    <div className=\"space-y-1\">\r\n                      <div className=\"text-xs font-medium text-muted-foreground flex items-center gap-1\">\r\n                        <Clock className=\"h-3.5 w-3.5\" /> Thời gian tạo\r\n                      </div>\r\n                      <div className=\"text-md\">\r\n                        <DateTimeDisplay date={transaction.createdAt} format=\"dateTime\" />\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n                </InfoCard>\r\n\r\n                {/* Thông tin ví */}\r\n                <InfoCard\r\n                  title=\"Thông tin ví\"\r\n                  description=\"Thông tin về ví tài chính\"\r\n                  className=\"py-4\"\r\n                >\r\n                  <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\r\n                    <div className=\"space-y-1\">\r\n                      <div className=\"text-xs font-medium text-muted-foreground flex items-center gap-1\">\r\n                        <Wallet className=\"h-3.5 w-3.5\" /> ID ví\r\n                      </div>\r\n                      <div className=\"text-md\">{transaction.walletId || transaction.wallet?.id}</div>\r\n                    </div>\r\n                    {transaction.balanceBefore !== undefined && (\r\n                      <div className=\"space-y-1\">\r\n                        <div className=\"text-xs font-medium text-muted-foreground flex items-center gap-1\">\r\n                          <CreditCard className=\"h-3.5 w-3.5\" /> Số dư trước\r\n                        </div>\r\n                        <div className=\"text-md\">\r\n                          <CurrencyDisplay amount={transaction.balanceBefore} />\r\n                        </div>\r\n                      </div>\r\n                    )}\r\n                    {transaction.balanceAfter !== undefined && (\r\n                      <div className=\"space-y-1\">\r\n                        <div className=\"text-xs font-medium text-muted-foreground flex items-center gap-1\">\r\n                          <CreditCard className=\"h-3.5 w-3.5\" /> Số dư sau\r\n                        </div>\r\n                        <div className=\"text-md\">\r\n                          <CurrencyDisplay amount={transaction.balanceAfter} />\r\n                        </div>\r\n                      </div>\r\n                    )}\r\n                  </div>\r\n                </InfoCard>\r\n\r\n                {/* Thông tin tham chiếu */}\r\n                {transaction.referenceType && transaction.referenceId && (\r\n                  <InfoCard\r\n                    title=\"Thông tin tham chiếu\"\r\n                    description=\"Thông tin về nguồn gốc giao dịch\"\r\n                    className=\"py-4\"\r\n                  >\r\n                    <div className=\"p-3 border rounded-md\">\r\n                      <ReferenceInfoComponent\r\n                        referenceType={transaction.referenceType}\r\n                        referenceId={transaction.referenceId}\r\n                        referenceInfo={transaction.referenceInfo}\r\n                      />\r\n                    </div>\r\n                  </InfoCard>\r\n                )}\r\n\r\n                {/* Thông tin thanh toán */}\r\n                {(transaction.paymentMethodId || transaction.gatewayTransactionId) && (\r\n                  <InfoCard\r\n                    title=\"Thông tin thanh toán\"\r\n                    description=\"Thông tin về phương thức thanh toán\"\r\n                    className=\"py-4\"\r\n                  >\r\n                    <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\r\n                      {transaction.paymentMethodId && (\r\n                        <div className=\"space-y-1\">\r\n                          <div className=\"text-xs font-medium text-muted-foreground flex items-center gap-1\">\r\n                            <CreditCard className=\"h-3.5 w-3.5\" /> Phương thức thanh toán\r\n                          </div>\r\n                          <div className=\"text-md\">{transaction.paymentMethodId}</div>\r\n                        </div>\r\n                      )}\r\n                      {transaction.gatewayTransactionId && (\r\n                        <div className=\"space-y-1\">\r\n                          <div className=\"text-xs font-medium text-muted-foreground flex items-center gap-1\">\r\n                            <FileText className=\"h-3.5 w-3.5\" /> Mã giao dịch cổng\r\n                          </div>\r\n                          <div className=\"text-md\">{transaction.gatewayTransactionId}</div>\r\n                        </div>\r\n                      )}\r\n                    </div>\r\n                  </InfoCard>\r\n                )}\r\n\r\n                {/* Ghi chú */}\r\n                {transaction.notes && (\r\n                  <InfoCard\r\n                    title=\"Ghi chú\"\r\n                    description=\"Thông tin bổ sung về giao dịch\"\r\n                    className=\"py-4\"\r\n                  >\r\n                    <p className=\"text-md\">{transaction.notes}</p>\r\n                  </InfoCard>\r\n                )}\r\n              </>\r\n            )}\r\n          </div>\r\n        </SheetContent>\r\n      </Sheet>\r\n    </>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAWA;AACA;AACA;AACA;AAAA;AAvBA;;;;;;;;;;;;;;AAgCO,SAAS,YAAY,EAAE,aAAa,kBAAkB,EAAE,MAAM,EAAE,OAAO,EAAE,QAAQ,EAAoB;IAC1G,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,8SAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,8SAAA,CAAA,WAAQ,AAAD,EAAqC;IAClF,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,8SAAA,CAAA,WAAQ,AAAD,EAAE;IAEzD,qCAAqC;IACrC,CAAA,GAAA,8SAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,UAAU,oBAAoB,IAAI;YACpC,WAAW;YACX,0GAAA,CAAA,MAAG,CAAC,GAAG,CAAiB,CAAC,aAAa,EAAE,mBAAmB,EAAE,CAAC,4DAA4D,CAAC,EACxH,IAAI,CAAC,CAAA;gBACJ,eAAe;gBACf,WAAW;YACb,GACC,KAAK,CAAC,CAAA;gBACL,QAAQ,KAAK,CAAC,uCAAuC;gBACrD,wQAAA,CAAA,QAAK,CAAC,KAAK,CAAC;gBACZ,WAAW;YACb;QACJ,OAAO;YACL,eAAe;QACjB;IACF,GAAG;QAAC;QAAQ,oBAAoB;KAAG;IAEnC,6BAA6B;IAC7B,IAAI,CAAC,aAAa;QAChB,OAAO;IACT;IAEA,sBAAsB;IACtB,MAAM,eAAe;QACnB,IAAI,YAAY,aAAa;YAC3B,SAAS;YACT,oBAAoB;QACtB;IACF;IAEA,0CAA0C;IAC1C,MAAM,YAAY,YAAY,MAAM,KAAK,2KAAA,CAAA,oBAAiB,CAAC,OAAO,IAAI;IAEtE,6CAA6C;IAC7C,MAAM,cAAc,CAAC;QACnB,OAAQ;YACN,KAAK,2KAAA,CAAA,kBAAe,CAAC,MAAM;gBACzB,qBAAO,uVAAC,wTAAA,CAAA,kBAAe;oBAAC,WAAU;;;;;;YACpC,KAAK,2KAAA,CAAA,kBAAe,CAAC,KAAK;gBACxB,qBAAO,uVAAC,wTAAA,CAAA,kBAAe;oBAAC,WAAU;;;;;;YACpC;gBACE,qBAAO,uVAAC,0RAAA,CAAA,SAAM;oBAAC,WAAU;;;;;;QAC7B;IACF;IAEA,6CAA6C;IAC7C,MAAM,eAAe,CAAC;QACpB,OAAQ;YACN,KAAK,2KAAA,CAAA,kBAAe,CAAC,MAAM;gBACzB,OAAO;YACT,KAAK,2KAAA,CAAA,kBAAe,CAAC,KAAK;gBACxB,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,yCAAyC;IACzC,MAAM,iBAAiB,CAAC;QACtB,OAAQ;YACN,KAAK,2KAAA,CAAA,oBAAiB,CAAC,SAAS;gBAC9B,OAAO;YACT,KAAK,2KAAA,CAAA,oBAAiB,CAAC,OAAO;gBAC5B,OAAO;YACT,KAAK,2KAAA,CAAA,oBAAiB,CAAC,UAAU;gBAC/B,OAAO;YACT,KAAK,2KAAA,CAAA,oBAAiB,CAAC,MAAM;gBAC3B,OAAO;YACT,KAAK,2KAAA,CAAA,oBAAiB,CAAC,SAAS;gBAC9B,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,wCAAwC;IACxC,MAAM,iBAAiB,CAAC;QACtB,OAAQ;YACN,KAAK,2KAAA,CAAA,oBAAiB,CAAC,SAAS;gBAC9B,OAAO;YACT,KAAK,2KAAA,CAAA,oBAAiB,CAAC,OAAO;gBAC5B,OAAO;YACT,KAAK,2KAAA,CAAA,oBAAiB,CAAC,UAAU;gBAC/B,OAAO;YACT,KAAK,2KAAA,CAAA,oBAAiB,CAAC,MAAM;gBAC3B,OAAO;YACT,KAAK,2KAAA,CAAA,oBAAiB,CAAC,SAAS;gBAC9B,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,qBACE;;0BAEE,uVAAC,2HAAA,CAAA,SAAM;gBAAC,MAAM;gBAAkB,cAAc;0BAC5C,cAAA,uVAAC,2HAAA,CAAA,gBAAa;;sCACZ,uVAAC,2HAAA,CAAA,eAAY;;8CACX,uVAAC,2HAAA,CAAA,cAAW;8CAAC;;;;;;8CACb,uVAAC,2HAAA,CAAA,oBAAiB;8CAAC;;;;;;;;;;;;sCAIrB,uVAAC,2HAAA,CAAA,eAAY;;8CACX,uVAAC,2HAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAU,SAAS,IAAM,oBAAoB;8CAAQ;;;;;;8CACrE,uVAAC,2HAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAc,SAAS;8CAAc;;;;;;;;;;;;;;;;;;;;;;;0BAK3D,uVAAC,0HAAA,CAAA,QAAK;gBAAC,MAAM;gBAAQ,cAAc,CAAC,OAAS,CAAC,QAAQ;0BACpD,cAAA,uVAAC,0HAAA,CAAA,eAAY;oBAAC,WAAU;;sCAEtB,uVAAC,0HAAA,CAAA,cAAW;4BAAC,WAAU;;8CACrB,uVAAC;oCAAI,WAAU;;sDACb,uVAAC;4CAAI,WAAU;sDACb,cAAA,uVAAC;gDAAI,WAAU;0DACZ,YAAY,YAAY,eAAe;;;;;;;;;;;sDAG5C,uVAAC;4CAAI,WAAU;;8DACb,uVAAC,0HAAA,CAAA,aAAU;oDAAC,WAAU;8DACnB,wBACC,uVAAC;wDAAI,WAAU;;0EACb,uVAAC,qSAAA,CAAA,UAAO;gEAAC,WAAU;;;;;;0EACnB,uVAAC;0EAAK;;;;;;;;;;;+DAGR,CAAC,UAAU,EAAE,aAAa,YAAY,eAAe,GAAG;;;;;;8DAG5D,uVAAC,0HAAA,CAAA,mBAAgB;oDAAC,WAAU;8DACzB,wBACC,uVAAC;wDAAI,WAAU;;0EACb,uVAAC,qSAAA,CAAA,UAAO;gEAAC,WAAU;;;;;;0EACnB,uVAAC;0EAAK;;;;;;;;;;;6EAGR;;0EACE,uVAAC;gEAAK,WAAW,CAAC,+BAA+B,EAAE,eAAe,YAAY,MAAM,GAAG;0EACpF,eAAe,YAAY,MAAM;;;;;;0EAEpC,uVAAC;gEAAK,WAAU;0EACd,cAAA,uVAAC,4IAAA,CAAA,kBAAe;oEAAC,MAAM,YAAY,SAAS;oEAAE,QAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8CASjE,uVAAC;oCAAI,WAAU;;;;;;gCAGd,2BACC,uVAAC;oCAAI,WAAU;8CACb,cAAA,uVAAC,2HAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,WAAU;wCACV,SAAS,IAAM,oBAAoB;wCACnC,UAAU;;0DAEV,uVAAC,gSAAA,CAAA,UAAO;gDAAC,WAAU;;;;;;0DACnB,uVAAC;0DAAK;;;;;;;;;;;;;;;;;;;;;;;sCAOd,uVAAC;4BAAI,WAAU;sCACZ,wBACC,uVAAC;gCAAI,WAAU;;kDACb,uVAAC,qSAAA,CAAA,UAAO;wCAAC,WAAU;;;;;;kDACnB,uVAAC;wCAAE,WAAU;kDAAwB;;;;;;;;;;;qDAGvC;;kDAEE,uVAAC,2HAAA,CAAA,WAAQ;wCACP,OAAM;wCACN,aAAY;wCACZ,WAAU;kDAEV,cAAA,uVAAC;4CAAI,WAAU;;8DACb,uVAAC;oDAAI,WAAU;;sEACb,uVAAC;4DAAI,WAAU;;8EACb,uVAAC,oRAAA,CAAA,MAAG;oEAAC,WAAU;;;;;;gEAAgB;;;;;;;sEAEjC,uVAAC;4DAAI,WAAU;sEAAW,YAAY,EAAE;;;;;;;;;;;;8DAE1C,uVAAC;oDAAI,WAAU;;sEACb,uVAAC;4DAAI,WAAU;;8EACb,uVAAC,wTAAA,CAAA,kBAAe;oEAAC,WAAU;;;;;;gEAAgB;;;;;;;sEAE7C,uVAAC;4DAAI,WAAU;;gEACZ,YAAY,YAAY,eAAe;8EACxC,uVAAC;8EAAM,aAAa,YAAY,eAAe;;;;;;;;;;;;;;;;;;8DAGnD,uVAAC;oDAAI,WAAU;;sEACb,uVAAC;4DAAI,WAAU;;8EACb,uVAAC,sSAAA,CAAA,aAAU;oEAAC,WAAU;;;;;;gEAAgB;;;;;;;sEAExC,uVAAC;4DAAI,WAAW,CAAC,QAAQ,EAAE,YAAY,eAAe,KAAK,2KAAA,CAAA,kBAAe,CAAC,MAAM,GAAG,mBAAmB,YAAY,eAAe,KAAK,2KAAA,CAAA,kBAAe,CAAC,KAAK,GAAG,iBAAiB,IAAI;sEAClL,cAAA,uVAAC,wIAAA,CAAA,kBAAe;gEAAC,QAAQ,YAAY,MAAM;;;;;;;;;;;;;;;;;8DAG/C,uVAAC;oDAAI,WAAU;;sEACb,uVAAC;4DAAI,WAAU;;8EACb,uVAAC,wRAAA,CAAA,QAAK;oEAAC,WAAU;;;;;;gEAAgB;;;;;;;sEAEnC,uVAAC;4DAAI,WAAU;sEACb,cAAA,uVAAC,4IAAA,CAAA,kBAAe;gEAAC,MAAM,YAAY,SAAS;gEAAE,QAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;kDAO7D,uVAAC,2HAAA,CAAA,WAAQ;wCACP,OAAM;wCACN,aAAY;wCACZ,WAAU;kDAEV,cAAA,uVAAC;4CAAI,WAAU;;8DACb,uVAAC;oDAAI,WAAU;;sEACb,uVAAC;4DAAI,WAAU;;8EACb,uVAAC,0RAAA,CAAA,SAAM;oEAAC,WAAU;;;;;;gEAAgB;;;;;;;sEAEpC,uVAAC;4DAAI,WAAU;sEAAW,YAAY,QAAQ,IAAI,YAAY,MAAM,EAAE;;;;;;;;;;;;gDAEvE,YAAY,aAAa,KAAK,2BAC7B,uVAAC;oDAAI,WAAU;;sEACb,uVAAC;4DAAI,WAAU;;8EACb,uVAAC,sSAAA,CAAA,aAAU;oEAAC,WAAU;;;;;;gEAAgB;;;;;;;sEAExC,uVAAC;4DAAI,WAAU;sEACb,cAAA,uVAAC,wIAAA,CAAA,kBAAe;gEAAC,QAAQ,YAAY,aAAa;;;;;;;;;;;;;;;;;gDAIvD,YAAY,YAAY,KAAK,2BAC5B,uVAAC;oDAAI,WAAU;;sEACb,uVAAC;4DAAI,WAAU;;8EACb,uVAAC,sSAAA,CAAA,aAAU;oEAAC,WAAU;;;;;;gEAAgB;;;;;;;sEAExC,uVAAC;4DAAI,WAAU;sEACb,cAAA,uVAAC,wIAAA,CAAA,kBAAe;gEAAC,QAAQ,YAAY,YAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;;oCAQ1D,YAAY,aAAa,IAAI,YAAY,WAAW,kBACnD,uVAAC,2HAAA,CAAA,WAAQ;wCACP,OAAM;wCACN,aAAY;wCACZ,WAAU;kDAEV,cAAA,uVAAC;4CAAI,WAAU;sDACb,cAAA,uVAAC,mKAAA,CAAA,yBAAsB;gDACrB,eAAe,YAAY,aAAa;gDACxC,aAAa,YAAY,WAAW;gDACpC,eAAe,YAAY,aAAa;;;;;;;;;;;;;;;;oCAO/C,CAAC,YAAY,eAAe,IAAI,YAAY,oBAAoB,mBAC/D,uVAAC,2HAAA,CAAA,WAAQ;wCACP,OAAM;wCACN,aAAY;wCACZ,WAAU;kDAEV,cAAA,uVAAC;4CAAI,WAAU;;gDACZ,YAAY,eAAe,kBAC1B,uVAAC;oDAAI,WAAU;;sEACb,uVAAC;4DAAI,WAAU;;8EACb,uVAAC,sSAAA,CAAA,aAAU;oEAAC,WAAU;;;;;;gEAAgB;;;;;;;sEAExC,uVAAC;4DAAI,WAAU;sEAAW,YAAY,eAAe;;;;;;;;;;;;gDAGxD,YAAY,oBAAoB,kBAC/B,uVAAC;oDAAI,WAAU;;sEACb,uVAAC;4DAAI,WAAU;;8EACb,uVAAC,kSAAA,CAAA,WAAQ;oEAAC,WAAU;;;;;;gEAAgB;;;;;;;sEAEtC,uVAAC;4DAAI,WAAU;sEAAW,YAAY,oBAAoB;;;;;;;;;;;;;;;;;;;;;;;oCAQnE,YAAY,KAAK,kBAChB,uVAAC,2HAAA,CAAA,WAAQ;wCACP,OAAM;wCACN,aAAY;wCACZ,WAAU;kDAEV,cAAA,uVAAC;4CAAE,WAAU;sDAAW,YAAY,KAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAU7D", "debugId": null}}, {"offset": {"line": 8102, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/bloomgoo/gold-exchange-platform/frontend/components/ui/checkbox.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as CheckboxPrimitive from \"@radix-ui/react-checkbox\"\r\nimport { CheckIcon } from \"lucide-react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction Checkbox({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof CheckboxPrimitive.Root>) {\r\n  return (\r\n    <CheckboxPrimitive.Root\r\n      data-slot=\"checkbox\"\r\n      className={cn(\r\n        \"peer border-input dark:bg-input/30 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground dark:data-[state=checked]:bg-primary data-[state=checked]:border-primary focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive size-4 shrink-0 rounded-[4px] border shadow-xs transition-shadow outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50\",\r\n        className\r\n      )}\r\n      {...props}\r\n    >\r\n      <CheckboxPrimitive.Indicator\r\n        data-slot=\"checkbox-indicator\"\r\n        className=\"flex items-center justify-center text-current transition-none\"\r\n      >\r\n        <CheckIcon className=\"size-3.5\" />\r\n      </CheckboxPrimitive.Indicator>\r\n    </CheckboxPrimitive.Root>\r\n  )\r\n}\r\n\r\nexport { Checkbox }\r\n"], "names": [], "mappings": ";;;;AAGA;AACA;AAEA;AAAA;AANA;;;;;AAQA,SAAS,SAAS,EAChB,SAAS,EACT,GAAG,OACiD;IACpD,qBACE,uVAAC,8QAAA,CAAA,OAAsB;QACrB,aAAU;QACV,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EACV,+eACA;QAED,GAAG,KAAK;kBAET,cAAA,uVAAC,8QAAA,CAAA,YAA2B;YAC1B,aAAU;YACV,WAAU;sBAEV,cAAA,uVAAC,4RAAA,CAAA,YAAS;gBAAC,WAAU;;;;;;;;;;;;;;;;AAI7B", "debugId": null}}, {"offset": {"line": 8148, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/bloomgoo/gold-exchange-platform/frontend/components/common/selector/column-visibility-selector.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { But<PERSON> } from '@/components/ui/button';\r\nimport { Checkbox } from '@/components/ui/checkbox';\r\nimport {\r\n  DropdownMenu,\r\n  DropdownMenuContent,\r\n  DropdownMenuTrigger,\r\n} from '@/components/ui/dropdown-menu';\r\nimport { Eye, Search, Settings2 } from 'lucide-react';\r\nimport { Table } from '@tanstack/react-table';\r\nimport { Input } from '@/components/ui/input';\r\nimport { useState, useMemo } from 'react';\r\n\r\n// Định nghĩa kiểu dữ liệu cho thuộc tính meta của cột\r\ndeclare module '@tanstack/react-table' {\r\n  interface ColumnMeta<TData, TValue> {\r\n    isSticky?: boolean;\r\n    position?: 'left' | 'right';\r\n    header?: string;\r\n  }\r\n}\r\n\r\ninterface ColumnVisibilitySelectorProps<TData> {\r\n  table: Table<TData>;\r\n  className?: string;\r\n  buttonClassName?: string;\r\n  contentClassName?: string;\r\n  label?: string;\r\n}\r\n\r\nexport function ColumnVisibilitySelector<TData>({\r\n  table,\r\n  className,\r\n  buttonClassName,\r\n  contentClassName = 'w-[250px] max-h-[var(--radix-dropdown-menu-content-available-height)] overflow-y-auto',\r\n  label = 'Tất cả',\r\n}: ColumnVisibilitySelectorProps<TData>) {\r\n  // State để lưu từ khóa tìm kiếm\r\n  const [searchQuery, setSearchQuery] = useState('');\r\n  // Lấy tất cả các cột có thể ẩn/hiện\r\n  const allColumns = useMemo(() => {\r\n    return table.getAllColumns().filter(column => column.getCanHide());\r\n  }, [table]);\r\n\r\n  // Lấy các cột đang hiển thị\r\n  const visibleColumns = useMemo(() => {\r\n    return allColumns.filter(column => column.getIsVisible());\r\n  }, [allColumns, table.getState().columnVisibility]);\r\n\r\n  // Lọc các cột dựa trên từ khóa tìm kiếm\r\n  const filteredColumns = useMemo(() => {\r\n    if (!searchQuery.trim()) {\r\n      return allColumns;\r\n    }\r\n\r\n    const query = searchQuery.toLowerCase().trim();\r\n    return allColumns.filter(column => {\r\n      // Lấy tên cột thân thiện từ header\r\n      let header = column.id;\r\n\r\n      // Ưu tiên sử dụng meta.header\r\n      if (column.columnDef.meta?.header) {\r\n        header = column.columnDef.meta.header;\r\n      }\r\n      // Nếu header là string, sử dụng trực tiếp\r\n      else if (typeof column.columnDef.header === 'string') {\r\n        header = column.columnDef.header;\r\n      }\r\n      // Nếu header là function, tìm kiếm phần tử span chứa tên cột\r\n      else if (typeof column.columnDef.header === 'function') {\r\n        const headerElement = document.querySelector(`[data-column-id=\"${column.id}\"] span`);\r\n        if (headerElement && headerElement.textContent) {\r\n          header = headerElement.textContent;\r\n        }\r\n      }\r\n\r\n      return header.toString().toLowerCase().includes(query);\r\n    });\r\n  }, [allColumns, searchQuery]);\r\n\r\n  // Hàm để toggle tất cả các cột\r\n  const toggleAllColumns = (checked: boolean) => {\r\n    table.toggleAllColumnsVisible(checked);\r\n  };\r\n\r\n  return (\r\n    <div className={className}>\r\n      <DropdownMenu>\r\n        <DropdownMenuTrigger asChild>\r\n          <Button\r\n            variant=\"ghost\"\r\n            size=\"icon\"\r\n            className={`relative ${buttonClassName || ''}`}\r\n          >\r\n            <Settings2 className=\"h-4 w-4\" />\r\n            {visibleColumns.length < allColumns.length && (\r\n              <span className=\"absolute -top-1 -right-1 h-2 w-2 rounded-full bg-blue-600\" />\r\n            )}\r\n          </Button>\r\n        </DropdownMenuTrigger>\r\n        <DropdownMenuContent\r\n          align=\"end\"\r\n          className={contentClassName}\r\n        >\r\n          <div className=\"sticky top-0 bg-background z-10 border-b\">\r\n            <div className=\"p-2\">\r\n              <div className=\"flex items-center space-x-2 px-1 py-1\">\r\n                <Checkbox\r\n                  checked={visibleColumns.length === allColumns.length}\r\n                  onCheckedChange={(checked) => toggleAllColumns(!!checked)}\r\n                  id=\"all-columns\"\r\n                />\r\n                <label\r\n                  htmlFor=\"all-columns\"\r\n                  className=\"text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70\"\r\n                >\r\n                  {label}\r\n                </label>\r\n              </div>\r\n\r\n              {/* Thêm ô tìm kiếm */}\r\n              <div className=\"relative mt-2\">\r\n                <Search className=\"absolute left-2 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground\" />\r\n                <Input\r\n                  placeholder=\"Tìm kiếm cột...\"\r\n                  value={searchQuery}\r\n                  onChange={(e) => setSearchQuery(e.target.value)}\r\n                  className=\"pl-8 h-8 text-sm\"\r\n                />\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <div className=\"p-2 pt-0\">\r\n            {filteredColumns.length === 0 ? (\r\n              <div className=\"py-6 text-center text-sm text-muted-foreground\">\r\n                Không tìm thấy cột nào\r\n              </div>\r\n            ) : (\r\n              filteredColumns.map(column => {\r\n                const isVisible = column.getIsVisible();\r\n                return (\r\n                  <div\r\n                    key={column.id}\r\n                    className=\"flex items-center space-x-2 px-1 py-1.5 rounded hover:bg-accent\"\r\n                  >\r\n                    <Checkbox\r\n                      checked={isVisible}\r\n                      onCheckedChange={(checked) => column.toggleVisibility(!!checked)}\r\n                      id={column.id}\r\n                    />\r\n                    <label\r\n                      htmlFor={column.id}\r\n                      className=\"flex-1 text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70\"\r\n                    >\r\n                      {(() => {\r\n                        // Ưu tiên sử dụng meta.header\r\n                        if (column.columnDef.meta?.header) {\r\n                          return column.columnDef.meta.header;\r\n                        }\r\n\r\n                        // Nếu header là string, sử dụng trực tiếp\r\n                        if (typeof column.columnDef.header === 'string') {\r\n                          return column.columnDef.header;\r\n                        }\r\n\r\n                        // Tìm kiếm phần tử span trong header\r\n                        const headerElement = document.querySelector(`[data-column-id=\"${column.id}\"] span`);\r\n                        if (headerElement && headerElement.textContent) {\r\n                          return headerElement.textContent;\r\n                        }\r\n\r\n                        // Fallback: Sử dụng ID\r\n                        return column.id;\r\n                      })()}\r\n                    </label>\r\n                  </div>\r\n                );\r\n              })\r\n            )}\r\n          </div>\r\n        </DropdownMenuContent>\r\n      </DropdownMenu>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAKA;AAAA;AAEA;AACA;AAZA;;;;;;;;AA+BO,SAAS,yBAAgC,EAC9C,KAAK,EACL,SAAS,EACT,eAAe,EACf,mBAAmB,uFAAuF,EAC1G,QAAQ,QAAQ,EACqB;IACrC,gCAAgC;IAChC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,8SAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,oCAAoC;IACpC,MAAM,aAAa,CAAA,GAAA,8SAAA,CAAA,UAAO,AAAD,EAAE;QACzB,OAAO,MAAM,aAAa,GAAG,MAAM,CAAC,CAAA,SAAU,OAAO,UAAU;IACjE,GAAG;QAAC;KAAM;IAEV,4BAA4B;IAC5B,MAAM,iBAAiB,CAAA,GAAA,8SAAA,CAAA,UAAO,AAAD,EAAE;QAC7B,OAAO,WAAW,MAAM,CAAC,CAAA,SAAU,OAAO,YAAY;IACxD,GAAG;QAAC;QAAY,MAAM,QAAQ,GAAG,gBAAgB;KAAC;IAElD,wCAAwC;IACxC,MAAM,kBAAkB,CAAA,GAAA,8SAAA,CAAA,UAAO,AAAD,EAAE;QAC9B,IAAI,CAAC,YAAY,IAAI,IAAI;YACvB,OAAO;QACT;QAEA,MAAM,QAAQ,YAAY,WAAW,GAAG,IAAI;QAC5C,OAAO,WAAW,MAAM,CAAC,CAAA;YACvB,mCAAmC;YACnC,IAAI,SAAS,OAAO,EAAE;YAEtB,8BAA8B;YAC9B,IAAI,OAAO,SAAS,CAAC,IAAI,EAAE,QAAQ;gBACjC,SAAS,OAAO,SAAS,CAAC,IAAI,CAAC,MAAM;YACvC,OAEK,IAAI,OAAO,OAAO,SAAS,CAAC,MAAM,KAAK,UAAU;gBACpD,SAAS,OAAO,SAAS,CAAC,MAAM;YAClC,OAEK,IAAI,OAAO,OAAO,SAAS,CAAC,MAAM,KAAK,YAAY;gBACtD,MAAM,gBAAgB,SAAS,aAAa,CAAC,CAAC,iBAAiB,EAAE,OAAO,EAAE,CAAC,OAAO,CAAC;gBACnF,IAAI,iBAAiB,cAAc,WAAW,EAAE;oBAC9C,SAAS,cAAc,WAAW;gBACpC;YACF;YAEA,OAAO,OAAO,QAAQ,GAAG,WAAW,GAAG,QAAQ,CAAC;QAClD;IACF,GAAG;QAAC;QAAY;KAAY;IAE5B,+BAA+B;IAC/B,MAAM,mBAAmB,CAAC;QACxB,MAAM,uBAAuB,CAAC;IAChC;IAEA,qBACE,uVAAC;QAAI,WAAW;kBACd,cAAA,uVAAC,qIAAA,CAAA,eAAY;;8BACX,uVAAC,qIAAA,CAAA,sBAAmB;oBAAC,OAAO;8BAC1B,cAAA,uVAAC,2HAAA,CAAA,SAAM;wBACL,SAAQ;wBACR,MAAK;wBACL,WAAW,CAAC,SAAS,EAAE,mBAAmB,IAAI;;0CAE9C,uVAAC,oSAAA,CAAA,YAAS;gCAAC,WAAU;;;;;;4BACpB,eAAe,MAAM,GAAG,WAAW,MAAM,kBACxC,uVAAC;gCAAK,WAAU;;;;;;;;;;;;;;;;;8BAItB,uVAAC,qIAAA,CAAA,sBAAmB;oBAClB,OAAM;oBACN,WAAW;;sCAEX,uVAAC;4BAAI,WAAU;sCACb,cAAA,uVAAC;gCAAI,WAAU;;kDACb,uVAAC;wCAAI,WAAU;;0DACb,uVAAC,6HAAA,CAAA,WAAQ;gDACP,SAAS,eAAe,MAAM,KAAK,WAAW,MAAM;gDACpD,iBAAiB,CAAC,UAAY,iBAAiB,CAAC,CAAC;gDACjD,IAAG;;;;;;0DAEL,uVAAC;gDACC,SAAQ;gDACR,WAAU;0DAET;;;;;;;;;;;;kDAKL,uVAAC;wCAAI,WAAU;;0DACb,uVAAC,0RAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;0DAClB,uVAAC,0HAAA,CAAA,QAAK;gDACJ,aAAY;gDACZ,OAAO;gDACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;gDAC9C,WAAU;;;;;;;;;;;;;;;;;;;;;;;sCAMlB,uVAAC;4BAAI,WAAU;sCACZ,gBAAgB,MAAM,KAAK,kBAC1B,uVAAC;gCAAI,WAAU;0CAAiD;;;;;uCAIhE,gBAAgB,GAAG,CAAC,CAAA;gCAClB,MAAM,YAAY,OAAO,YAAY;gCACrC,qBACE,uVAAC;oCAEC,WAAU;;sDAEV,uVAAC,6HAAA,CAAA,WAAQ;4CACP,SAAS;4CACT,iBAAiB,CAAC,UAAY,OAAO,gBAAgB,CAAC,CAAC,CAAC;4CACxD,IAAI,OAAO,EAAE;;;;;;sDAEf,uVAAC;4CACC,SAAS,OAAO,EAAE;4CAClB,WAAU;sDAET,CAAC;gDACA,8BAA8B;gDAC9B,IAAI,OAAO,SAAS,CAAC,IAAI,EAAE,QAAQ;oDACjC,OAAO,OAAO,SAAS,CAAC,IAAI,CAAC,MAAM;gDACrC;gDAEA,0CAA0C;gDAC1C,IAAI,OAAO,OAAO,SAAS,CAAC,MAAM,KAAK,UAAU;oDAC/C,OAAO,OAAO,SAAS,CAAC,MAAM;gDAChC;gDAEA,qCAAqC;gDACrC,MAAM,gBAAgB,SAAS,aAAa,CAAC,CAAC,iBAAiB,EAAE,OAAO,EAAE,CAAC,OAAO,CAAC;gDACnF,IAAI,iBAAiB,cAAc,WAAW,EAAE;oDAC9C,OAAO,cAAc,WAAW;gDAClC;gDAEA,uBAAuB;gDACvB,OAAO,OAAO,EAAE;4CAClB,CAAC;;;;;;;mCA/BE,OAAO,EAAE;;;;;4BAmCpB;;;;;;;;;;;;;;;;;;;;;;;AAOd", "debugId": null}}, {"offset": {"line": 8406, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/bloomgoo/gold-exchange-platform/frontend/components/ui/popover.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as PopoverPrimitive from \"@radix-ui/react-popover\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction Popover({\r\n  ...props\r\n}: React.ComponentProps<typeof PopoverPrimitive.Root>) {\r\n  return <PopoverPrimitive.Root data-slot=\"popover\" {...props} />\r\n}\r\n\r\nfunction PopoverTrigger({\r\n  ...props\r\n}: React.ComponentProps<typeof PopoverPrimitive.Trigger>) {\r\n  return <PopoverPrimitive.Trigger data-slot=\"popover-trigger\" {...props} />\r\n}\r\n\r\nfunction PopoverContent({\r\n  className,\r\n  align = \"center\",\r\n  sideOffset = 4,\r\n  ...props\r\n}: React.ComponentProps<typeof PopoverPrimitive.Content>) {\r\n  return (\r\n    <PopoverPrimitive.Portal>\r\n      <PopoverPrimitive.Content\r\n        data-slot=\"popover-content\"\r\n        align={align}\r\n        sideOffset={sideOffset}\r\n        className={cn(\r\n          \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 w-72 origin-(--radix-popover-content-transform-origin) rounded-md border p-4 shadow-md outline-hidden\",\r\n          className\r\n        )}\r\n        {...props}\r\n      />\r\n    </PopoverPrimitive.Portal>\r\n  )\r\n}\r\n\r\nfunction PopoverAnchor({\r\n  ...props\r\n}: React.ComponentProps<typeof PopoverPrimitive.Anchor>) {\r\n  return <PopoverPrimitive.Anchor data-slot=\"popover-anchor\" {...props} />\r\n}\r\n\r\nexport { Popover, PopoverTrigger, PopoverContent, PopoverAnchor }\r\n"], "names": [], "mappings": ";;;;;;;AAGA;AAEA;AAAA;AALA;;;;AAOA,SAAS,QAAQ,EACf,GAAG,OACgD;IACnD,qBAAO,uVAAC,6QAAA,CAAA,OAAqB;QAAC,aAAU;QAAW,GAAG,KAAK;;;;;;AAC7D;AAEA,SAAS,eAAe,EACtB,GAAG,OACmD;IACtD,qBAAO,uVAAC,6QAAA,CAAA,UAAwB;QAAC,aAAU;QAAmB,GAAG,KAAK;;;;;;AACxE;AAEA,SAAS,eAAe,EACtB,SAAS,EACT,QAAQ,QAAQ,EAChB,aAAa,CAAC,EACd,GAAG,OACmD;IACtD,qBACE,uVAAC,6QAAA,CAAA,SAAuB;kBACtB,cAAA,uVAAC,6QAAA,CAAA,UAAwB;YACvB,aAAU;YACV,OAAO;YACP,YAAY;YACZ,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EACV,keACA;YAED,GAAG,KAAK;;;;;;;;;;;AAIjB;AAEA,SAAS,cAAc,EACrB,GAAG,OACkD;IACrD,qBAAO,uVAAC,6QAAA,CAAA,SAAuB;QAAC,aAAU;QAAkB,GAAG,KAAK;;;;;;AACtE", "debugId": null}}, {"offset": {"line": 8476, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/bloomgoo/gold-exchange-platform/frontend/components/common/selector/sort-selector.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { But<PERSON> } from '@/components/ui/button';\r\nimport {\r\n  Popover,\r\n  PopoverContent,\r\n  PopoverTrigger,\r\n} from '@/components/ui/popover';\r\nimport {\r\n  ArrowDownWideNarrow,\r\n  ArrowUpDown,\r\n  ArrowUpNarrowWide,\r\n  X,\r\n  Plus,\r\n  GripVertical,\r\n  Search\r\n} from 'lucide-react';\r\nimport { Input } from '@/components/ui/input';\r\nimport { SortingState, Table } from '@tanstack/react-table';\r\nimport { cn } from '@/lib/utils';\r\nimport { Badge } from '@/components/ui/badge';\r\n\r\nimport { useState, useMemo, useEffect } from 'react';\r\n\r\n// Định nghĩa kiểu dữ liệu cho thuộc tính meta của cột\r\ndeclare module '@tanstack/react-table' {\r\n  interface ColumnMeta<TData, TValue> {\r\n    isSticky?: boolean;\r\n    position?: 'left' | 'right';\r\n    header?: string;\r\n  }\r\n}\r\nimport {\r\n  DndContext,\r\n  closestCenter,\r\n  KeyboardSensor,\r\n  PointerSensor,\r\n  useSensor,\r\n  useSensors,\r\n  DragEndEvent\r\n} from '@dnd-kit/core';\r\nimport {\r\n  arrayMove,\r\n  SortableContext,\r\n  sortableKeyboardCoordinates,\r\n  useSortable,\r\n  verticalListSortingStrategy,\r\n} from '@dnd-kit/sortable';\r\nimport { CSS } from '@dnd-kit/utilities';\r\n\r\ninterface SortSelectorProps<TData> {\r\n  table: Table<TData>;\r\n  className?: string;\r\n  buttonClassName?: string;\r\n  contentClassName?: string;\r\n  maxSortFields?: number;\r\n}\r\n\r\n// Component cho mỗi mục sort có thể kéo thả\r\ninterface SortableItemProps {\r\n  id: string;\r\n  columnName: string;\r\n  direction: 'asc' | 'desc';\r\n  onDirectionChange: (direction: 'asc' | 'desc') => void;\r\n  onRemove: () => void;\r\n}\r\n\r\nfunction SortableItem({\r\n  id,\r\n  columnName,\r\n  direction,\r\n  onDirectionChange,\r\n  onRemove\r\n}: SortableItemProps) {\r\n  const {\r\n    attributes,\r\n    listeners,\r\n    setNodeRef,\r\n    transform,\r\n    transition,\r\n  } = useSortable({ id });\r\n\r\n  const style = {\r\n    transform: CSS.Transform.toString(transform),\r\n    transition,\r\n  };\r\n\r\n  return (\r\n    <div\r\n      ref={setNodeRef}\r\n      style={style}\r\n      className=\"flex items-center justify-between p-2 mb-1 bg-muted/50 rounded-md\"\r\n    >\r\n      <div className=\"flex items-center gap-2\">\r\n        <button\r\n          {...attributes}\r\n          {...listeners}\r\n          className=\"cursor-grab touch-none p-1 rounded hover:bg-muted\"\r\n        >\r\n          <GripVertical className=\"h-4 w-4 text-muted-foreground\" />\r\n        </button>\r\n        <span className=\"text-sm\">{columnName}</span>\r\n      </div>\r\n      <div className=\"flex items-center gap-1\">\r\n        <Button\r\n          variant=\"ghost\"\r\n          size=\"icon\"\r\n          className=\"h-7 w-7\"\r\n          onClick={() => onDirectionChange(direction === 'asc' ? 'desc' : 'asc')}\r\n        >\r\n          {direction === 'asc' ? (\r\n            <ArrowDownWideNarrow className=\"h-4 w-4\" />\r\n          ) : (\r\n            <ArrowUpNarrowWide className=\"h-4 w-4\" />\r\n          )}\r\n        </Button>\r\n        <Button\r\n          variant=\"ghost\"\r\n          size=\"icon\"\r\n          className=\"h-7 w-7 text-destructive\"\r\n          onClick={onRemove}\r\n        >\r\n          <X className=\"h-4 w-4\" />\r\n        </Button>\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n\r\nexport function SortSelector<TData>({\r\n  table,\r\n  className,\r\n  buttonClassName,\r\n  contentClassName = 'w-[300px]',\r\n  maxSortFields = 3,\r\n}: SortSelectorProps<TData>) {\r\n  const [open, setOpen] = useState(false);\r\n  const [searchQuery, setSearchQuery] = useState('');\r\n  const [tempSorting, setTempSorting] = useState<SortingState>([]);\r\n\r\n  // Lấy tất cả các cột có thể sắp xếp\r\n  const sortableColumns = useMemo(() => {\r\n    return table.getAllColumns().filter(column =>\r\n      column.getCanSort() && column.getIsVisible()\r\n    );\r\n  }, [table]);\r\n\r\n  // Lọc các cột dựa trên từ khóa tìm kiếm\r\n  const filteredColumns = useMemo(() => {\r\n    if (!searchQuery.trim()) {\r\n      return sortableColumns;\r\n    }\r\n\r\n    const query = searchQuery.toLowerCase().trim();\r\n    return sortableColumns.filter(column => {\r\n      // Lấy tên cột thân thiện từ header\r\n      let header = column.id;\r\n\r\n      // Ưu tiên sử dụng meta.header\r\n      if (column.columnDef.meta?.header) {\r\n        header = column.columnDef.meta.header;\r\n      }\r\n      // Nếu header là string, sử dụng trực tiếp\r\n      else if (typeof column.columnDef.header === 'string') {\r\n        header = column.columnDef.header;\r\n      }\r\n      // Nếu header là function, tìm kiếm phần tử span chứa tên cột\r\n      else if (typeof column.columnDef.header === 'function') {\r\n        const headerElement = document.querySelector(`[data-column-id=\"${column.id}\"] span`);\r\n        if (headerElement && headerElement.textContent) {\r\n          header = headerElement.textContent;\r\n        }\r\n      }\r\n\r\n      return header.toString().toLowerCase().includes(query);\r\n    });\r\n  }, [sortableColumns, searchQuery]);\r\n\r\n  // Khi mở dropdown, sao chép trạng thái sắp xếp hiện tại vào tempSorting\r\n  useEffect(() => {\r\n    if (open) {\r\n      setTempSorting(table.getState().sorting);\r\n    }\r\n  }, [open, table]);\r\n\r\n  // Lấy thông tin các cột đang được sắp xếp (dựa trên tempSorting)\r\n  const sortedColumns = useMemo(() => {\r\n    return tempSorting.map(sort => {\r\n      const column = table.getColumn(sort.id);\r\n\r\n      // Lấy tên cột thân thiện từ header\r\n      let columnName = sort.id;\r\n\r\n      if (column) {\r\n        // Ưu tiên sử dụng meta.header\r\n        if (column.columnDef.meta?.header) {\r\n          columnName = column.columnDef.meta.header;\r\n        }\r\n        // Nếu header là string, sử dụng trực tiếp\r\n        else if (typeof column.columnDef.header === 'string') {\r\n          columnName = column.columnDef.header;\r\n        }\r\n        // Nếu header là function, tìm kiếm phần tử span chứa tên cột\r\n        else if (typeof column.columnDef.header === 'function') {\r\n          const headerElement = document.querySelector(`[data-column-id=\"${sort.id}\"] span`);\r\n          if (headerElement && headerElement.textContent) {\r\n            columnName = headerElement.textContent;\r\n          }\r\n        }\r\n      }\r\n\r\n      return {\r\n        id: sort.id,\r\n        name: columnName,\r\n        direction: (sort.desc ? 'desc' : 'asc') as 'desc' | 'asc'\r\n      };\r\n    });\r\n  }, [tempSorting, table]);\r\n\r\n  // Sensors cho DnD\r\n  const sensors = useSensors(\r\n    useSensor(PointerSensor),\r\n    useSensor(KeyboardSensor, {\r\n      coordinateGetter: sortableKeyboardCoordinates,\r\n    })\r\n  );\r\n\r\n  // Xử lý khi kết thúc kéo thả\r\n  const handleDragEnd = (event: DragEndEvent) => {\r\n    const { active, over } = event;\r\n\r\n    if (over && active.id !== over.id) {\r\n      const oldIndex = sortedColumns.findIndex(item => item.id === active.id);\r\n      const newIndex = sortedColumns.findIndex(item => item.id === over.id);\r\n\r\n      const newSortedColumns = arrayMove(sortedColumns, oldIndex, newIndex);\r\n\r\n      // Cập nhật tempSorting\r\n      const newSorting = newSortedColumns.map(col => ({\r\n        id: col.id,\r\n        desc: col.direction === 'desc'\r\n      }));\r\n\r\n      setTempSorting(newSorting);\r\n    }\r\n  };\r\n\r\n  // Xử lý thêm tiêu chí sắp xếp mới\r\n  const handleAddSort = (columnId: string) => {\r\n    // Kiểm tra xem cột đã được sắp xếp chưa\r\n    const existingSort = tempSorting.find(sort => sort.id === columnId);\r\n\r\n    if (existingSort) {\r\n      // Nếu đã có, thay đổi hướng sắp xếp\r\n      const newSorting = tempSorting.map(sort => {\r\n        if (sort.id === columnId) {\r\n          return { ...sort, desc: !sort.desc };\r\n        }\r\n        return sort;\r\n      });\r\n\r\n      setTempSorting(newSorting);\r\n    } else if (tempSorting.length < maxSortFields) {\r\n      // Nếu chưa có và chưa đạt giới hạn, thêm mới\r\n      const newSorting = [\r\n        ...tempSorting,\r\n        { id: columnId, desc: false }\r\n      ];\r\n\r\n      setTempSorting(newSorting);\r\n    }\r\n\r\n    // Xóa từ khóa tìm kiếm sau khi chọn\r\n    setSearchQuery('');\r\n  };\r\n\r\n  // Xử lý thay đổi hướng sắp xếp\r\n  const handleDirectionChange = (columnId: string, direction: 'asc' | 'desc') => {\r\n    const newSorting = tempSorting.map(sort => {\r\n      if (sort.id === columnId) {\r\n        return { ...sort, desc: direction === 'desc' };\r\n      }\r\n      return sort;\r\n    });\r\n\r\n    setTempSorting(newSorting);\r\n  };\r\n\r\n  // Xử lý xóa tiêu chí sắp xếp\r\n  const handleRemoveSort = (columnId: string) => {\r\n    const newSorting = tempSorting.filter(sort => sort.id !== columnId);\r\n    setTempSorting(newSorting);\r\n  };\r\n\r\n  // Xử lý xóa tất cả tiêu chí sắp xếp\r\n  const handleClearAll = () => {\r\n    setTempSorting([]);\r\n  };\r\n\r\n  // Xử lý áp dụng các thay đổi\r\n  const handleApply = () => {\r\n    table.setSorting(tempSorting);\r\n    setOpen(false);\r\n  };\r\n\r\n  // Xử lý hủy thay đổi\r\n  const handleCancel = () => {\r\n    setTempSorting(table.getState().sorting);\r\n    setOpen(false);\r\n  };\r\n\r\n  // Lấy icon sắp xếp cho button\r\n  const getSortIcon = () => {\r\n    const currentSorting = table.getState().sorting;\r\n\r\n    if (currentSorting.length === 0) {\r\n      return <ArrowUpDown className=\"h-4 w-4\" />;\r\n    }\r\n\r\n    if (currentSorting.length === 1) {\r\n      return currentSorting[0].desc ?\r\n        <ArrowUpNarrowWide className=\"h-4 w-4\" /> :\r\n        <ArrowDownWideNarrow className=\"h-4 w-4\" />;\r\n    }\r\n\r\n    // Nếu có nhiều tiêu chí sắp xếp, hiển thị số lượng\r\n    return (\r\n      <div className=\"relative\">\r\n        <ArrowUpDown className=\"h-4 w-4\" />\r\n        <Badge\r\n          className=\"absolute -top-2 -right-2 h-4 w-4 flex items-center justify-center p-0 text-[10px]\"\r\n          variant=\"default\"\r\n        >\r\n          {currentSorting.length}\r\n        </Badge>\r\n      </div>\r\n    );\r\n  };\r\n\r\n  return (\r\n    <div className={className}>\r\n      <Popover open={open} onOpenChange={setOpen}>\r\n        <PopoverTrigger asChild>\r\n          <Button\r\n            variant=\"ghost\"\r\n            size=\"icon\"\r\n            className={cn(\"relative\", buttonClassName, table.getState().sorting.length > 0 && \"bg-accent\")}\r\n          >\r\n            {getSortIcon()}\r\n          </Button>\r\n        </PopoverTrigger>\r\n        <PopoverContent align=\"end\" className={contentClassName} onEscapeKeyDown={handleCancel} onInteractOutside={(e) => e.preventDefault()}>\r\n          <div className=\"p-0\">\r\n            <h4 className=\"mb-2 text-sm font-medium\">Sắp xếp theo</h4>\r\n\r\n            {/* Danh sách các tiêu chí sắp xếp hiện tại */}\r\n            {sortedColumns.length > 0 ? (\r\n              <div className=\"mb-2\">\r\n                <DndContext\r\n                  sensors={sensors}\r\n                  collisionDetection={closestCenter}\r\n                  onDragEnd={handleDragEnd}\r\n                >\r\n                  <SortableContext\r\n                    items={sortedColumns.map(col => col.id)}\r\n                    strategy={verticalListSortingStrategy}\r\n                  >\r\n                    {sortedColumns.map((column) => (\r\n                      <SortableItem\r\n                        key={column.id}\r\n                        id={column.id}\r\n                        columnName={column.name}\r\n                        direction={column.direction}\r\n                        onDirectionChange={(direction) =>\r\n                          handleDirectionChange(column.id, direction)\r\n                        }\r\n                        onRemove={() => handleRemoveSort(column.id)}\r\n                      />\r\n                    ))}\r\n                  </SortableContext>\r\n                </DndContext>\r\n              </div>\r\n            ) : (\r\n              <div className=\"text-sm text-muted-foreground mb-2 py-2 text-center\">\r\n                Chưa có tiêu chí sắp xếp nào\r\n              </div>\r\n            )}\r\n\r\n            {/* Phần chọn trường sắp xếp */}\r\n            {tempSorting.length < maxSortFields && (\r\n              <div className=\"mb-2\">\r\n                <div className=\"text-xs font-medium mb-1 text-muted-foreground\">Thêm tiêu chí sắp xếp</div>\r\n\r\n                {/* Ô tìm kiếm trường */}\r\n                <div className=\"relative mb-2\">\r\n                  <Search className=\"absolute left-2 top-1/2 h-3.5 w-3.5 -translate-y-1/2 text-muted-foreground\" />\r\n                  <Input\r\n                    placeholder=\"Tìm kiếm trường...\"\r\n                    value={searchQuery}\r\n                    onChange={(e) => setSearchQuery(e.target.value)}\r\n                    className=\"pl-7 h-8 text-sm\"\r\n                  />\r\n                </div>\r\n\r\n                {/* Danh sách các trường có thể sắp xếp */}\r\n                <div className=\"max-h-[150px] overflow-y-auto border rounded-md\">\r\n                  {filteredColumns.length === 0 ? (\r\n                    <div className=\"p-2 text-center text-sm text-muted-foreground\">\r\n                      Không tìm thấy trường nào\r\n                    </div>\r\n                  ) : (\r\n                    <div className=\"p-1\">\r\n                      {filteredColumns.map(column => {\r\n                        const isSelected = tempSorting.some(sort => sort.id === column.id);\r\n                        return (\r\n                          <button\r\n                            key={column.id}\r\n                            onClick={() => handleAddSort(column.id)}\r\n                            disabled={isSelected}\r\n                            className={cn(\r\n                              \"w-full text-left px-2 py-1.5 text-sm rounded-md hover:bg-accent flex items-center justify-between\",\r\n                              isSelected && \"opacity-50 cursor-not-allowed\"\r\n                            )}\r\n                          >\r\n                            <span>\r\n                              {(() => {\r\n                                // Ưu tiên sử dụng meta.header\r\n                                if (column.columnDef.meta?.header) {\r\n                                  return column.columnDef.meta.header;\r\n                                }\r\n\r\n                                // Nếu header là string, sử dụng trực tiếp\r\n                                if (typeof column.columnDef.header === 'string') {\r\n                                  return column.columnDef.header;\r\n                                }\r\n\r\n                                // Tìm kiếm phần tử span trong header\r\n                                const headerElement = document.querySelector(`[data-column-id=\"${column.id}\"] span`);\r\n                                if (headerElement && headerElement.textContent) {\r\n                                  return headerElement.textContent;\r\n                                }\r\n\r\n                                // Fallback: Sử dụng ID\r\n                                return column.id;\r\n                              })()}\r\n                            </span>\r\n                          </button>\r\n                        );\r\n                      })}\r\n                    </div>\r\n                  )}\r\n                </div>\r\n              </div>\r\n            )}\r\n\r\n            {/* Nút Áp dụng và Xóa tất cả */}\r\n            <div className=\"flex items-center justify-between mt-4\">\r\n              <Button\r\n                onClick={handleApply}\r\n                size=\"sm\"\r\n                className=\"flex-1 mr-1\"\r\n              >\r\n                Áp dụng\r\n              </Button>\r\n\r\n              <Button\r\n                onClick={handleClearAll}\r\n                variant=\"link\"\r\n                size=\"sm\"\r\n                className=\"flex-1 ml-1 text-destructive\"\r\n                disabled={tempSorting.length === 0}\r\n              >\r\n                Xóa tất cả\r\n              </Button>\r\n            </div>\r\n          </div>\r\n        </PopoverContent>\r\n      </Popover>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAKA;AAAA;AAAA;AAAA;AAAA;AAAA;AASA;AAEA;AAAA;AACA;AAEA;AAUA;AASA;AAOA;AAhDA;;;;;;;;;;;;AAmEA,SAAS,aAAa,EACpB,EAAE,EACF,UAAU,EACV,SAAS,EACT,iBAAiB,EACjB,QAAQ,EACU;IAClB,MAAM,EACJ,UAAU,EACV,SAAS,EACT,UAAU,EACV,SAAS,EACT,UAAU,EACX,GAAG,CAAA,GAAA,gRAAA,CAAA,cAAW,AAAD,EAAE;QAAE;IAAG;IAErB,MAAM,QAAQ;QACZ,WAAW,iQAAA,CAAA,MAAG,CAAC,SAAS,CAAC,QAAQ,CAAC;QAClC;IACF;IAEA,qBACE,uVAAC;QACC,KAAK;QACL,OAAO;QACP,WAAU;;0BAEV,uVAAC;gBAAI,WAAU;;kCACb,uVAAC;wBACE,GAAG,UAAU;wBACb,GAAG,SAAS;wBACb,WAAU;kCAEV,cAAA,uVAAC,0SAAA,CAAA,eAAY;4BAAC,WAAU;;;;;;;;;;;kCAE1B,uVAAC;wBAAK,WAAU;kCAAW;;;;;;;;;;;;0BAE7B,uVAAC;gBAAI,WAAU;;kCACb,uVAAC,2HAAA,CAAA,SAAM;wBACL,SAAQ;wBACR,MAAK;wBACL,WAAU;wBACV,SAAS,IAAM,kBAAkB,cAAc,QAAQ,SAAS;kCAE/D,cAAc,sBACb,uVAAC,gUAAA,CAAA,sBAAmB;4BAAC,WAAU;;;;;iDAE/B,uVAAC,4TAAA,CAAA,oBAAiB;4BAAC,WAAU;;;;;;;;;;;kCAGjC,uVAAC,2HAAA,CAAA,SAAM;wBACL,SAAQ;wBACR,MAAK;wBACL,WAAU;wBACV,SAAS;kCAET,cAAA,uVAAC,gRAAA,CAAA,IAAC;4BAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;AAKvB;AAEO,SAAS,aAAoB,EAClC,KAAK,EACL,SAAS,EACT,eAAe,EACf,mBAAmB,WAAW,EAC9B,gBAAgB,CAAC,EACQ;IACzB,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,8SAAA,CAAA,WAAQ,AAAD,EAAE;IACjC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,8SAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,8SAAA,CAAA,WAAQ,AAAD,EAAgB,EAAE;IAE/D,oCAAoC;IACpC,MAAM,kBAAkB,CAAA,GAAA,8SAAA,CAAA,UAAO,AAAD,EAAE;QAC9B,OAAO,MAAM,aAAa,GAAG,MAAM,CAAC,CAAA,SAClC,OAAO,UAAU,MAAM,OAAO,YAAY;IAE9C,GAAG;QAAC;KAAM;IAEV,wCAAwC;IACxC,MAAM,kBAAkB,CAAA,GAAA,8SAAA,CAAA,UAAO,AAAD,EAAE;QAC9B,IAAI,CAAC,YAAY,IAAI,IAAI;YACvB,OAAO;QACT;QAEA,MAAM,QAAQ,YAAY,WAAW,GAAG,IAAI;QAC5C,OAAO,gBAAgB,MAAM,CAAC,CAAA;YAC5B,mCAAmC;YACnC,IAAI,SAAS,OAAO,EAAE;YAEtB,8BAA8B;YAC9B,IAAI,OAAO,SAAS,CAAC,IAAI,EAAE,QAAQ;gBACjC,SAAS,OAAO,SAAS,CAAC,IAAI,CAAC,MAAM;YACvC,OAEK,IAAI,OAAO,OAAO,SAAS,CAAC,MAAM,KAAK,UAAU;gBACpD,SAAS,OAAO,SAAS,CAAC,MAAM;YAClC,OAEK,IAAI,OAAO,OAAO,SAAS,CAAC,MAAM,KAAK,YAAY;gBACtD,MAAM,gBAAgB,SAAS,aAAa,CAAC,CAAC,iBAAiB,EAAE,OAAO,EAAE,CAAC,OAAO,CAAC;gBACnF,IAAI,iBAAiB,cAAc,WAAW,EAAE;oBAC9C,SAAS,cAAc,WAAW;gBACpC;YACF;YAEA,OAAO,OAAO,QAAQ,GAAG,WAAW,GAAG,QAAQ,CAAC;QAClD;IACF,GAAG;QAAC;QAAiB;KAAY;IAEjC,wEAAwE;IACxE,CAAA,GAAA,8SAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,MAAM;YACR,eAAe,MAAM,QAAQ,GAAG,OAAO;QACzC;IACF,GAAG;QAAC;QAAM;KAAM;IAEhB,iEAAiE;IACjE,MAAM,gBAAgB,CAAA,GAAA,8SAAA,CAAA,UAAO,AAAD,EAAE;QAC5B,OAAO,YAAY,GAAG,CAAC,CAAA;YACrB,MAAM,SAAS,MAAM,SAAS,CAAC,KAAK,EAAE;YAEtC,mCAAmC;YACnC,IAAI,aAAa,KAAK,EAAE;YAExB,IAAI,QAAQ;gBACV,8BAA8B;gBAC9B,IAAI,OAAO,SAAS,CAAC,IAAI,EAAE,QAAQ;oBACjC,aAAa,OAAO,SAAS,CAAC,IAAI,CAAC,MAAM;gBAC3C,OAEK,IAAI,OAAO,OAAO,SAAS,CAAC,MAAM,KAAK,UAAU;oBACpD,aAAa,OAAO,SAAS,CAAC,MAAM;gBACtC,OAEK,IAAI,OAAO,OAAO,SAAS,CAAC,MAAM,KAAK,YAAY;oBACtD,MAAM,gBAAgB,SAAS,aAAa,CAAC,CAAC,iBAAiB,EAAE,KAAK,EAAE,CAAC,OAAO,CAAC;oBACjF,IAAI,iBAAiB,cAAc,WAAW,EAAE;wBAC9C,aAAa,cAAc,WAAW;oBACxC;gBACF;YACF;YAEA,OAAO;gBACL,IAAI,KAAK,EAAE;gBACX,MAAM;gBACN,WAAY,KAAK,IAAI,GAAG,SAAS;YACnC;QACF;IACF,GAAG;QAAC;QAAa;KAAM;IAEvB,kBAAkB;IAClB,MAAM,UAAU,CAAA,GAAA,wQAAA,CAAA,aAAU,AAAD,EACvB,CAAA,GAAA,wQAAA,CAAA,YAAS,AAAD,EAAE,wQAAA,CAAA,gBAAa,GACvB,CAAA,GAAA,wQAAA,CAAA,YAAS,AAAD,EAAE,wQAAA,CAAA,iBAAc,EAAE;QACxB,kBAAkB,gRAAA,CAAA,8BAA2B;IAC/C;IAGF,6BAA6B;IAC7B,MAAM,gBAAgB,CAAC;QACrB,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,GAAG;QAEzB,IAAI,QAAQ,OAAO,EAAE,KAAK,KAAK,EAAE,EAAE;YACjC,MAAM,WAAW,cAAc,SAAS,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK,OAAO,EAAE;YACtE,MAAM,WAAW,cAAc,SAAS,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK,KAAK,EAAE;YAEpE,MAAM,mBAAmB,CAAA,GAAA,gRAAA,CAAA,YAAS,AAAD,EAAE,eAAe,UAAU;YAE5D,uBAAuB;YACvB,MAAM,aAAa,iBAAiB,GAAG,CAAC,CAAA,MAAO,CAAC;oBAC9C,IAAI,IAAI,EAAE;oBACV,MAAM,IAAI,SAAS,KAAK;gBAC1B,CAAC;YAED,eAAe;QACjB;IACF;IAEA,kCAAkC;IAClC,MAAM,gBAAgB,CAAC;QACrB,wCAAwC;QACxC,MAAM,eAAe,YAAY,IAAI,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK;QAE1D,IAAI,cAAc;YAChB,oCAAoC;YACpC,MAAM,aAAa,YAAY,GAAG,CAAC,CAAA;gBACjC,IAAI,KAAK,EAAE,KAAK,UAAU;oBACxB,OAAO;wBAAE,GAAG,IAAI;wBAAE,MAAM,CAAC,KAAK,IAAI;oBAAC;gBACrC;gBACA,OAAO;YACT;YAEA,eAAe;QACjB,OAAO,IAAI,YAAY,MAAM,GAAG,eAAe;YAC7C,6CAA6C;YAC7C,MAAM,aAAa;mBACd;gBACH;oBAAE,IAAI;oBAAU,MAAM;gBAAM;aAC7B;YAED,eAAe;QACjB;QAEA,oCAAoC;QACpC,eAAe;IACjB;IAEA,+BAA+B;IAC/B,MAAM,wBAAwB,CAAC,UAAkB;QAC/C,MAAM,aAAa,YAAY,GAAG,CAAC,CAAA;YACjC,IAAI,KAAK,EAAE,KAAK,UAAU;gBACxB,OAAO;oBAAE,GAAG,IAAI;oBAAE,MAAM,cAAc;gBAAO;YAC/C;YACA,OAAO;QACT;QAEA,eAAe;IACjB;IAEA,6BAA6B;IAC7B,MAAM,mBAAmB,CAAC;QACxB,MAAM,aAAa,YAAY,MAAM,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK;QAC1D,eAAe;IACjB;IAEA,oCAAoC;IACpC,MAAM,iBAAiB;QACrB,eAAe,EAAE;IACnB;IAEA,6BAA6B;IAC7B,MAAM,cAAc;QAClB,MAAM,UAAU,CAAC;QACjB,QAAQ;IACV;IAEA,qBAAqB;IACrB,MAAM,eAAe;QACnB,eAAe,MAAM,QAAQ,GAAG,OAAO;QACvC,QAAQ;IACV;IAEA,8BAA8B;IAC9B,MAAM,cAAc;QAClB,MAAM,iBAAiB,MAAM,QAAQ,GAAG,OAAO;QAE/C,IAAI,eAAe,MAAM,KAAK,GAAG;YAC/B,qBAAO,uVAAC,4SAAA,CAAA,cAAW;gBAAC,WAAU;;;;;;QAChC;QAEA,IAAI,eAAe,MAAM,KAAK,GAAG;YAC/B,OAAO,cAAc,CAAC,EAAE,CAAC,IAAI,iBAC3B,uVAAC,4TAAA,CAAA,oBAAiB;gBAAC,WAAU;;;;;qCAC7B,uVAAC,gUAAA,CAAA,sBAAmB;gBAAC,WAAU;;;;;;QACnC;QAEA,mDAAmD;QACnD,qBACE,uVAAC;YAAI,WAAU;;8BACb,uVAAC,4SAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;8BACvB,uVAAC,0HAAA,CAAA,QAAK;oBACJ,WAAU;oBACV,SAAQ;8BAEP,eAAe,MAAM;;;;;;;;;;;;IAI9B;IAEA,qBACE,uVAAC;QAAI,WAAW;kBACd,cAAA,uVAAC,4HAAA,CAAA,UAAO;YAAC,MAAM;YAAM,cAAc;;8BACjC,uVAAC,4HAAA,CAAA,iBAAc;oBAAC,OAAO;8BACrB,cAAA,uVAAC,2HAAA,CAAA,SAAM;wBACL,SAAQ;wBACR,MAAK;wBACL,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EAAE,YAAY,iBAAiB,MAAM,QAAQ,GAAG,OAAO,CAAC,MAAM,GAAG,KAAK;kCAEjF;;;;;;;;;;;8BAGL,uVAAC,4HAAA,CAAA,iBAAc;oBAAC,OAAM;oBAAM,WAAW;oBAAkB,iBAAiB;oBAAc,mBAAmB,CAAC,IAAM,EAAE,cAAc;8BAChI,cAAA,uVAAC;wBAAI,WAAU;;0CACb,uVAAC;gCAAG,WAAU;0CAA2B;;;;;;4BAGxC,cAAc,MAAM,GAAG,kBACtB,uVAAC;gCAAI,WAAU;0CACb,cAAA,uVAAC,wQAAA,CAAA,aAAU;oCACT,SAAS;oCACT,oBAAoB,wQAAA,CAAA,gBAAa;oCACjC,WAAW;8CAEX,cAAA,uVAAC,gRAAA,CAAA,kBAAe;wCACd,OAAO,cAAc,GAAG,CAAC,CAAA,MAAO,IAAI,EAAE;wCACtC,UAAU,gRAAA,CAAA,8BAA2B;kDAEpC,cAAc,GAAG,CAAC,CAAC,uBAClB,uVAAC;gDAEC,IAAI,OAAO,EAAE;gDACb,YAAY,OAAO,IAAI;gDACvB,WAAW,OAAO,SAAS;gDAC3B,mBAAmB,CAAC,YAClB,sBAAsB,OAAO,EAAE,EAAE;gDAEnC,UAAU,IAAM,iBAAiB,OAAO,EAAE;+CAPrC,OAAO,EAAE;;;;;;;;;;;;;;;;;;;qDAcxB,uVAAC;gCAAI,WAAU;0CAAsD;;;;;;4BAMtE,YAAY,MAAM,GAAG,+BACpB,uVAAC;gCAAI,WAAU;;kDACb,uVAAC;wCAAI,WAAU;kDAAiD;;;;;;kDAGhE,uVAAC;wCAAI,WAAU;;0DACb,uVAAC,0RAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;0DAClB,uVAAC,0HAAA,CAAA,QAAK;gDACJ,aAAY;gDACZ,OAAO;gDACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;gDAC9C,WAAU;;;;;;;;;;;;kDAKd,uVAAC;wCAAI,WAAU;kDACZ,gBAAgB,MAAM,KAAK,kBAC1B,uVAAC;4CAAI,WAAU;sDAAgD;;;;;iEAI/D,uVAAC;4CAAI,WAAU;sDACZ,gBAAgB,GAAG,CAAC,CAAA;gDACnB,MAAM,aAAa,YAAY,IAAI,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK,OAAO,EAAE;gDACjE,qBACE,uVAAC;oDAEC,SAAS,IAAM,cAAc,OAAO,EAAE;oDACtC,UAAU;oDACV,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EACV,qGACA,cAAc;8DAGhB,cAAA,uVAAC;kEACE,CAAC;4DACA,8BAA8B;4DAC9B,IAAI,OAAO,SAAS,CAAC,IAAI,EAAE,QAAQ;gEACjC,OAAO,OAAO,SAAS,CAAC,IAAI,CAAC,MAAM;4DACrC;4DAEA,0CAA0C;4DAC1C,IAAI,OAAO,OAAO,SAAS,CAAC,MAAM,KAAK,UAAU;gEAC/C,OAAO,OAAO,SAAS,CAAC,MAAM;4DAChC;4DAEA,qCAAqC;4DACrC,MAAM,gBAAgB,SAAS,aAAa,CAAC,CAAC,iBAAiB,EAAE,OAAO,EAAE,CAAC,OAAO,CAAC;4DACnF,IAAI,iBAAiB,cAAc,WAAW,EAAE;gEAC9C,OAAO,cAAc,WAAW;4DAClC;4DAEA,uBAAuB;4DACvB,OAAO,OAAO,EAAE;wDAClB,CAAC;;;;;;mDA5BE,OAAO,EAAE;;;;;4CAgCpB;;;;;;;;;;;;;;;;;0CAQV,uVAAC;gCAAI,WAAU;;kDACb,uVAAC,2HAAA,CAAA,SAAM;wCACL,SAAS;wCACT,MAAK;wCACL,WAAU;kDACX;;;;;;kDAID,uVAAC,2HAAA,CAAA,SAAM;wCACL,SAAS;wCACT,SAAQ;wCACR,MAAK;wCACL,WAAU;wCACV,UAAU,YAAY,MAAM,KAAK;kDAClC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASf", "debugId": null}}, {"offset": {"line": 9059, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/bloomgoo/gold-exchange-platform/frontend/components/common/data-table/table-toolbar.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport { ColumnVisibilitySelector } from \"@/components/common/selector/column-visibility-selector\"\r\nimport { SortSelector } from \"@/components/common/selector/sort-selector\"\r\nimport { Button } from \"@/components/ui/button\"\r\nimport { Input } from \"@/components/ui/input\"\r\nimport { cn } from \"@/lib/utils\"\r\nimport { Table } from \"@tanstack/react-table\"\r\nimport {\r\n    RefreshCcw,\r\n    Search\r\n} from \"lucide-react\"\r\n\r\ninterface TableToolbarProps<TData> {\r\n    table: Table<TData>\r\n    globalFilter: string\r\n    setGlobalFilter: (value: string) => void\r\n    onRefresh: () => void\r\n    isRefreshing?: boolean\r\n    searchPlaceholder?: string\r\n\r\n    onDeleteSelected?: () => void\r\n    isShowSelectedRows?: boolean\r\n    onShowSelectedRows?: () => void\r\n    // Slot tùy chỉnh để thêm nội dung vào bên trái của thanh tìm kiếm\r\n    beforeSearchSlot?: React.ReactNode\r\n    // Slot tùy chỉnh để thêm nội dung vào bên phải của thanh tìm kiếm\r\n    afterSearchSlot?: React.ReactNode\r\n    // Slot tùy chỉnh để thêm nội dung vào bên trái của các nút hành động\r\n    beforeActionsSlot?: React.ReactNode\r\n    // Slot tùy chỉnh để thêm nội dung vào bên phải của các nút hành động\r\n    afterActionsSlot?: React.ReactNode\r\n    // Tùy chỉnh class cho thanh công cụ\r\n    className?: string\r\n}\r\n\r\nexport function TableToolbar<TData>({\r\n    table,\r\n    globalFilter,\r\n    setGlobalFilter,\r\n    onRefresh,\r\n    isRefreshing = false,\r\n    searchPlaceholder = \"Tìm kiếm...\",\r\n\r\n    onDeleteSelected,\r\n    isShowSelectedRows,\r\n    onShowSelectedRows,\r\n    beforeSearchSlot,\r\n    afterSearchSlot,\r\n    beforeActionsSlot,\r\n    afterActionsSlot,\r\n    className,\r\n}: TableToolbarProps<TData>) {\r\n    const selectedRows = table.getSelectedRowModel().rows\r\n\r\n    return (\r\n        <div className={cn(\"flex items-center justify-between gap-2 px-4 py-2 border-b\", className)}>\r\n            {/* Left Side */}\r\n            <div className=\"flex items-center space-x-4 flex-1\">\r\n                {/* Slot trước thanh tìm kiếm */}\r\n                {beforeSearchSlot}\r\n\r\n                <div className=\"relative w-64\">\r\n                    <Search\r\n                        className=\"h-4 w-4 absolute left-2 top-1/2 transform -translate-y-1/2 text-muted-foreground\"\r\n                    />\r\n                    <Input\r\n                        placeholder={searchPlaceholder}\r\n                        className=\"pl-8 h-9\"\r\n                        value={globalFilter}\r\n                        onChange={(e) => setGlobalFilter(e.target.value)}\r\n                    />\r\n                </div>\r\n\r\n                {/* Slot sau thanh tìm kiếm */}\r\n                {afterSearchSlot}\r\n            </div>\r\n\r\n            {/* Right Side */}\r\n            <div className=\"flex items-center space-x-2\">\r\n                {/* Slot trước các nút hành động */}\r\n                {beforeActionsSlot}\r\n                {onDeleteSelected && (<></>)}\r\n\r\n                {/* <FilterSelector table={table} /> */}\r\n\r\n                <SortSelector table={table} />\r\n\r\n                <ColumnVisibilitySelector table={table} />\r\n\r\n                <Button\r\n                    variant=\"ghost\"\r\n                    size=\"icon\"\r\n                    onClick={onRefresh}\r\n                    disabled={isRefreshing}\r\n                >\r\n                    <RefreshCcw className={cn(\"h-4 w-4\", isRefreshing && \"animate-spin\")} />\r\n                </Button>\r\n\r\n                {/* Slot sau các nút hành động */}\r\n                {afterActionsSlot}\r\n            </div>\r\n        </div>\r\n    )\r\n}"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AAAA;AAEA;AAAA;AARA;;;;;;;;AAoCO,SAAS,aAAoB,EAChC,KAAK,EACL,YAAY,EACZ,eAAe,EACf,SAAS,EACT,eAAe,KAAK,EACpB,oBAAoB,aAAa,EAEjC,gBAAgB,EAChB,kBAAkB,EAClB,kBAAkB,EAClB,gBAAgB,EAChB,eAAe,EACf,iBAAiB,EACjB,gBAAgB,EAChB,SAAS,EACc;IACvB,MAAM,eAAe,MAAM,mBAAmB,GAAG,IAAI;IAErD,qBACI,uVAAC;QAAI,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EAAE,8DAA8D;;0BAE7E,uVAAC;gBAAI,WAAU;;oBAEV;kCAED,uVAAC;wBAAI,WAAU;;0CACX,uVAAC,0RAAA,CAAA,SAAM;gCACH,WAAU;;;;;;0CAEd,uVAAC,0HAAA,CAAA,QAAK;gCACF,aAAa;gCACb,WAAU;gCACV,OAAO;gCACP,UAAU,CAAC,IAAM,gBAAgB,EAAE,MAAM,CAAC,KAAK;;;;;;;;;;;;oBAKtD;;;;;;;0BAIL,uVAAC;gBAAI,WAAU;;oBAEV;oBACA,kCAAqB;kCAItB,uVAAC,qJAAA,CAAA,eAAY;wBAAC,OAAO;;;;;;kCAErB,uVAAC,qKAAA,CAAA,2BAAwB;wBAAC,OAAO;;;;;;kCAEjC,uVAAC,2HAAA,CAAA,SAAM;wBACH,SAAQ;wBACR,MAAK;wBACL,SAAS;wBACT,UAAU;kCAEV,cAAA,uVAAC,sSAAA,CAAA,aAAU;4BAAC,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EAAE,WAAW,gBAAgB;;;;;;;;;;;oBAIxD;;;;;;;;;;;;;AAIjB", "debugId": null}}, {"offset": {"line": 9177, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/bloomgoo/gold-exchange-platform/frontend/components/ui/command.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport { Command as CommandPrimitive } from \"cmdk\"\r\nimport { SearchIcon } from \"lucide-react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\nimport {\r\n  Dialog,\r\n  DialogContent,\r\n  DialogDescription,\r\n  DialogHeader,\r\n  DialogTitle,\r\n} from \"@/components/ui/dialog\"\r\n\r\nfunction Command({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof CommandPrimitive>) {\r\n  return (\r\n    <CommandPrimitive\r\n      data-slot=\"command\"\r\n      className={cn(\r\n        \"bg-popover text-popover-foreground flex h-full w-full flex-col overflow-hidden rounded-md\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CommandDialog({\r\n  title = \"Command Palette\",\r\n  description = \"Search for a command to run...\",\r\n  children,\r\n  ...props\r\n}: React.ComponentProps<typeof Dialog> & {\r\n  title?: string\r\n  description?: string\r\n}) {\r\n  return (\r\n    <Dialog {...props}>\r\n      <DialogHeader className=\"sr-only\">\r\n        <DialogTitle>{title}</DialogTitle>\r\n        <DialogDescription>{description}</DialogDescription>\r\n      </DialogHeader>\r\n      <DialogContent className=\"overflow-hidden p-0\">\r\n        <Command className=\"[&_[cmdk-group-heading]]:text-muted-foreground **:data-[slot=command-input-wrapper]:h-12 [&_[cmdk-group-heading]]:px-2 [&_[cmdk-group-heading]]:font-medium [&_[cmdk-group]]:px-2 [&_[cmdk-group]:not([hidden])_~[cmdk-group]]:pt-0 [&_[cmdk-input-wrapper]_svg]:h-5 [&_[cmdk-input-wrapper]_svg]:w-5 [&_[cmdk-input]]:h-12 [&_[cmdk-item]]:px-2 [&_[cmdk-item]]:py-3 [&_[cmdk-item]_svg]:h-5 [&_[cmdk-item]_svg]:w-5\">\r\n          {children}\r\n        </Command>\r\n      </DialogContent>\r\n    </Dialog>\r\n  )\r\n}\r\n\r\nfunction CommandInput({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof CommandPrimitive.Input>) {\r\n  return (\r\n    <div\r\n      data-slot=\"command-input-wrapper\"\r\n      className=\"flex h-9 items-center gap-2 border-b px-3\"\r\n    >\r\n      <SearchIcon className=\"size-4 shrink-0 opacity-50\" />\r\n      <CommandPrimitive.Input\r\n        data-slot=\"command-input\"\r\n        className={cn(\r\n          \"placeholder:text-muted-foreground flex h-10 w-full rounded-md bg-transparent py-3 text-sm outline-hidden disabled:cursor-not-allowed disabled:opacity-50\",\r\n          className\r\n        )}\r\n        {...props}\r\n      />\r\n    </div>\r\n  )\r\n}\r\n\r\nfunction CommandList({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof CommandPrimitive.List>) {\r\n  return (\r\n    <CommandPrimitive.List\r\n      data-slot=\"command-list\"\r\n      className={cn(\r\n        \"max-h-[300px] scroll-py-1 overflow-x-hidden overflow-y-auto\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CommandEmpty({\r\n  ...props\r\n}: React.ComponentProps<typeof CommandPrimitive.Empty>) {\r\n  return (\r\n    <CommandPrimitive.Empty\r\n      data-slot=\"command-empty\"\r\n      className=\"py-6 text-center text-sm\"\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CommandGroup({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof CommandPrimitive.Group>) {\r\n  return (\r\n    <CommandPrimitive.Group\r\n      data-slot=\"command-group\"\r\n      className={cn(\r\n        \"text-foreground [&_[cmdk-group-heading]]:text-muted-foreground overflow-hidden p-1 [&_[cmdk-group-heading]]:px-2 [&_[cmdk-group-heading]]:py-1.5 [&_[cmdk-group-heading]]:text-xs [&_[cmdk-group-heading]]:font-medium\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CommandSeparator({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof CommandPrimitive.Separator>) {\r\n  return (\r\n    <CommandPrimitive.Separator\r\n      data-slot=\"command-separator\"\r\n      className={cn(\"bg-border -mx-1 h-px\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CommandItem({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof CommandPrimitive.Item>) {\r\n  return (\r\n    <CommandPrimitive.Item\r\n      data-slot=\"command-item\"\r\n      className={cn(\r\n        \"data-[selected=true]:bg-accent data-[selected=true]:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex cursor-default items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[disabled=true]:pointer-events-none data-[disabled=true]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CommandShortcut({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<\"span\">) {\r\n  return (\r\n    <span\r\n      data-slot=\"command-shortcut\"\r\n      className={cn(\r\n        \"text-muted-foreground ml-auto text-xs tracking-widest\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport {\r\n  Command,\r\n  CommandDialog,\r\n  CommandInput,\r\n  CommandList,\r\n  CommandEmpty,\r\n  CommandGroup,\r\n  CommandItem,\r\n  CommandShortcut,\r\n  CommandSeparator,\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;;;AAGA;AACA;AAEA;AAAA;AACA;AAPA;;;;;;AAeA,SAAS,QAAQ,EACf,SAAS,EACT,GAAG,OAC2C;IAC9C,qBACE,uVAAC,kPAAA,CAAA,UAAgB;QACf,aAAU;QACV,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EACV,6FACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,cAAc,EACrB,QAAQ,iBAAiB,EACzB,cAAc,gCAAgC,EAC9C,QAAQ,EACR,GAAG,OAIJ;IACC,qBACE,uVAAC,2HAAA,CAAA,SAAM;QAAE,GAAG,KAAK;;0BACf,uVAAC,2HAAA,CAAA,eAAY;gBAAC,WAAU;;kCACtB,uVAAC,2HAAA,CAAA,cAAW;kCAAE;;;;;;kCACd,uVAAC,2HAAA,CAAA,oBAAiB;kCAAE;;;;;;;;;;;;0BAEtB,uVAAC,2HAAA,CAAA,gBAAa;gBAAC,WAAU;0BACvB,cAAA,uVAAC;oBAAQ,WAAU;8BAChB;;;;;;;;;;;;;;;;;AAKX;AAEA,SAAS,aAAa,EACpB,SAAS,EACT,GAAG,OACiD;IACpD,qBACE,uVAAC;QACC,aAAU;QACV,WAAU;;0BAEV,uVAAC,8RAAA,CAAA,aAAU;gBAAC,WAAU;;;;;;0BACtB,uVAAC,kPAAA,CAAA,UAAgB,CAAC,KAAK;gBACrB,aAAU;gBACV,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EACV,4JACA;gBAED,GAAG,KAAK;;;;;;;;;;;;AAIjB;AAEA,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,uVAAC,kPAAA,CAAA,UAAgB,CAAC,IAAI;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EACV,+DACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,aAAa,EACpB,GAAG,OACiD;IACpD,qBACE,uVAAC,kPAAA,CAAA,UAAgB,CAAC,KAAK;QACrB,aAAU;QACV,WAAU;QACT,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,aAAa,EACpB,SAAS,EACT,GAAG,OACiD;IACpD,qBACE,uVAAC,kPAAA,CAAA,UAAgB,CAAC,KAAK;QACrB,aAAU;QACV,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EACV,0NACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,iBAAiB,EACxB,SAAS,EACT,GAAG,OACqD;IACxD,qBACE,uVAAC,kPAAA,CAAA,UAAgB,CAAC,SAAS;QACzB,aAAU;QACV,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EAAE,wBAAwB;QACrC,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,uVAAC,kPAAA,CAAA,UAAgB,CAAC,IAAI;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EACV,uYACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,gBAAgB,EACvB,SAAS,EACT,GAAG,OAC0B;IAC7B,qBACE,uVAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EACV,yDACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 9361, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/bloomgoo/gold-exchange-platform/frontend/components/common/selector/page-selector.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { Button } from '@/components/ui/button';\r\nimport {\r\n   Command,\r\n   CommandEmpty,\r\n   CommandGroup,\r\n   CommandInput,\r\n   CommandItem,\r\n   CommandList,\r\n} from '@/components/ui/command';\r\nimport { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';\r\nimport { CheckIcon, ChevronLeft, ChevronRight, ChevronsLeft, ChevronsRight } from 'lucide-react';\r\nimport { useId, useState, useEffect } from 'react';\r\nimport { Table as TableType } from \"@tanstack/react-table\";\r\n\r\n// Interface for table-based pagination\r\ninterface TablePaginationProps {\r\n   table: TableType<any>;\r\n   type: 'table';\r\n}\r\n\r\n// Interface for custom pagination\r\ninterface CustomPaginationProps {\r\n   type: 'custom';\r\n   currentPage: number;\r\n   totalPages: number;\r\n   onPageChange: (page: number) => void;\r\n}\r\n\r\n// Combined props type\r\ntype PageSelectorProps = {\r\n   maxPagesToShow?: number;\r\n   showFirstLastButtons?: boolean;\r\n   showPrevNextButtons?: boolean;\r\n   className?: string;\r\n} & (TablePaginationProps | CustomPaginationProps);\r\n\r\nexport function PageSelector(props: PageSelectorProps) {\r\n   const id = useId();\r\n   const [open, setOpen] = useState<boolean>(false);\r\n   \r\n   // Determine current page and total pages based on props type\r\n   const currentPage = props.type === 'table' \r\n      ? props.table.getState().pagination.pageIndex + 1 \r\n      : props.currentPage;\r\n   \r\n   const totalPages = props.type === 'table'\r\n      ? props.table.getPageCount()\r\n      : props.totalPages;\r\n   \r\n   // Default to showing 5 pages in the selector\r\n   const maxPagesToShow = props.maxPagesToShow || 5;\r\n   \r\n   // Generate array of page numbers to display\r\n   const getPageNumbers = () => {\r\n      if (totalPages <= maxPagesToShow) {\r\n         // If total pages is less than max to show, display all pages\r\n         return Array.from({ length: totalPages }, (_, i) => i + 1);\r\n      }\r\n      \r\n      // Calculate start and end page numbers\r\n      let startPage = Math.max(1, currentPage - Math.floor(maxPagesToShow / 2));\r\n      let endPage = startPage + maxPagesToShow - 1;\r\n      \r\n      // Adjust if end page exceeds total pages\r\n      if (endPage > totalPages) {\r\n         endPage = totalPages;\r\n         startPage = Math.max(1, endPage - maxPagesToShow + 1);\r\n      }\r\n      \r\n      return Array.from({ length: endPage - startPage + 1 }, (_, i) => startPage + i);\r\n   };\r\n   \r\n   const pageNumbers = getPageNumbers();\r\n   \r\n   // Handle page change\r\n   const handlePageChange = (page: number) => {\r\n      if (props.type === 'table') {\r\n         props.table.setPageIndex(page - 1);\r\n      } else {\r\n         props.onPageChange(page);\r\n      }\r\n      setOpen(false);\r\n   };\r\n   \r\n   // Handle navigation to first page\r\n   const goToFirstPage = () => {\r\n      if (props.type === 'table') {\r\n         props.table.setPageIndex(0);\r\n      } else {\r\n         props.onPageChange(1);\r\n      }\r\n   };\r\n   \r\n   // Handle navigation to previous page\r\n   const goToPreviousPage = () => {\r\n      if (props.type === 'table') {\r\n         props.table.previousPage();\r\n      } else {\r\n         props.onPageChange(Math.max(1, currentPage - 1));\r\n      }\r\n   };\r\n   \r\n   // Handle navigation to next page\r\n   const goToNextPage = () => {\r\n      if (props.type === 'table') {\r\n         props.table.nextPage();\r\n      } else {\r\n         props.onPageChange(Math.min(totalPages, currentPage + 1));\r\n      }\r\n   };\r\n   \r\n   // Handle navigation to last page\r\n   const goToLastPage = () => {\r\n      if (props.type === 'table') {\r\n         props.table.setPageIndex(totalPages - 1);\r\n      } else {\r\n         props.onPageChange(totalPages);\r\n      }\r\n   };\r\n   \r\n   // Check if can go to previous or next page\r\n   const canPreviousPage = props.type === 'table' \r\n      ? props.table.getCanPreviousPage()\r\n      : currentPage > 1;\r\n      \r\n   const canNextPage = props.type === 'table'\r\n      ? props.table.getCanNextPage()\r\n      : currentPage < totalPages;\r\n\r\n   return (\r\n      <div className=\"flex items-center space-x-2\">\r\n         {/* First Page Button */}\r\n         {props.showFirstLastButtons && (\r\n            <Button\r\n               variant=\"outline\"\r\n               className=\"hidden h-8 w-8 p-0 lg:flex\"\r\n               onClick={goToFirstPage}\r\n               disabled={!canPreviousPage}\r\n            >\r\n               <ChevronsLeft className=\"size-4\" />\r\n            </Button>\r\n         )}\r\n         \r\n         {/* Previous Page Button */}\r\n         {props.showPrevNextButtons && (\r\n            <Button\r\n               variant=\"outline\"\r\n               className=\"h-8 w-8 p-0\"\r\n               onClick={goToPreviousPage}\r\n               disabled={!canPreviousPage}\r\n            >\r\n               <ChevronLeft className=\"size-4\" />\r\n            </Button>\r\n         )}\r\n         \r\n         {/* Page Selector */}\r\n         <Popover open={open} onOpenChange={setOpen}>\r\n            <PopoverTrigger asChild>\r\n               <Button\r\n                  id={id}\r\n                  className=\"flex items-center justify-center h-8 px-3\"\r\n                  size=\"sm\"\r\n                  variant=\"outline\"\r\n                  role=\"combobox\"\r\n                  aria-expanded={open}\r\n               >\r\n                  <span className=\"text-sm font-medium\">\r\n                     {currentPage} / {totalPages}\r\n                  </span>\r\n               </Button>\r\n            </PopoverTrigger>\r\n            <PopoverContent className=\"border-input w-48 p-0\" align=\"center\" side=\"top\">\r\n               <Command>\r\n                  <CommandInput placeholder=\"Trang...\" />\r\n                  <CommandList>\r\n                     <CommandEmpty>Không tìm thấy trang.</CommandEmpty>\r\n                     <CommandGroup>\r\n                        {pageNumbers.map((page) => (\r\n                           <CommandItem\r\n                              key={page}\r\n                              value={page.toString()}\r\n                              onSelect={() => handlePageChange(page)}\r\n                              className=\"flex items-center justify-between\"\r\n                           >\r\n                              <div className=\"flex items-center gap-2\">\r\n                                 <span className=\"text-xs\">Trang {page}</span>\r\n                              </div>\r\n                              {currentPage === page && <CheckIcon size={14} className=\"ml-auto\" />}\r\n                           </CommandItem>\r\n                        ))}\r\n                     </CommandGroup>\r\n                  </CommandList>\r\n               </Command>\r\n            </PopoverContent>\r\n         </Popover>\r\n         \r\n         {/* Next Page Button */}\r\n         {props.showPrevNextButtons && (\r\n            <Button\r\n               variant=\"outline\"\r\n               className=\"h-8 w-8 p-0\"\r\n               onClick={goToNextPage}\r\n               disabled={!canNextPage}\r\n            >\r\n               <ChevronRight className=\"size-4\" />\r\n            </Button>\r\n         )}\r\n         \r\n         {/* Last Page Button */}\r\n         {props.showFirstLastButtons && (\r\n            <Button\r\n               variant=\"outline\"\r\n               className=\"hidden h-8 w-8 p-0 lg:flex\"\r\n               onClick={goToLastPage}\r\n               disabled={!canNextPage}\r\n            >\r\n               <ChevronsRight className=\"size-4\" />\r\n            </Button>\r\n         )}\r\n      </div>\r\n   );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAQA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AAbA;;;;;;;AAsCO,SAAS,aAAa,KAAwB;IAClD,MAAM,KAAK,CAAA,GAAA,8SAAA,CAAA,QAAK,AAAD;IACf,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,8SAAA,CAAA,WAAQ,AAAD,EAAW;IAE1C,6DAA6D;IAC7D,MAAM,cAAc,MAAM,IAAI,KAAK,UAC9B,MAAM,KAAK,CAAC,QAAQ,GAAG,UAAU,CAAC,SAAS,GAAG,IAC9C,MAAM,WAAW;IAEtB,MAAM,aAAa,MAAM,IAAI,KAAK,UAC7B,MAAM,KAAK,CAAC,YAAY,KACxB,MAAM,UAAU;IAErB,6CAA6C;IAC7C,MAAM,iBAAiB,MAAM,cAAc,IAAI;IAE/C,4CAA4C;IAC5C,MAAM,iBAAiB;QACpB,IAAI,cAAc,gBAAgB;YAC/B,6DAA6D;YAC7D,OAAO,MAAM,IAAI,CAAC;gBAAE,QAAQ;YAAW,GAAG,CAAC,GAAG,IAAM,IAAI;QAC3D;QAEA,uCAAuC;QACvC,IAAI,YAAY,KAAK,GAAG,CAAC,GAAG,cAAc,KAAK,KAAK,CAAC,iBAAiB;QACtE,IAAI,UAAU,YAAY,iBAAiB;QAE3C,yCAAyC;QACzC,IAAI,UAAU,YAAY;YACvB,UAAU;YACV,YAAY,KAAK,GAAG,CAAC,GAAG,UAAU,iBAAiB;QACtD;QAEA,OAAO,MAAM,IAAI,CAAC;YAAE,QAAQ,UAAU,YAAY;QAAE,GAAG,CAAC,GAAG,IAAM,YAAY;IAChF;IAEA,MAAM,cAAc;IAEpB,qBAAqB;IACrB,MAAM,mBAAmB,CAAC;QACvB,IAAI,MAAM,IAAI,KAAK,SAAS;YACzB,MAAM,KAAK,CAAC,YAAY,CAAC,OAAO;QACnC,OAAO;YACJ,MAAM,YAAY,CAAC;QACtB;QACA,QAAQ;IACX;IAEA,kCAAkC;IAClC,MAAM,gBAAgB;QACnB,IAAI,MAAM,IAAI,KAAK,SAAS;YACzB,MAAM,KAAK,CAAC,YAAY,CAAC;QAC5B,OAAO;YACJ,MAAM,YAAY,CAAC;QACtB;IACH;IAEA,qCAAqC;IACrC,MAAM,mBAAmB;QACtB,IAAI,MAAM,IAAI,KAAK,SAAS;YACzB,MAAM,KAAK,CAAC,YAAY;QAC3B,OAAO;YACJ,MAAM,YAAY,CAAC,KAAK,GAAG,CAAC,GAAG,cAAc;QAChD;IACH;IAEA,iCAAiC;IACjC,MAAM,eAAe;QAClB,IAAI,MAAM,IAAI,KAAK,SAAS;YACzB,MAAM,KAAK,CAAC,QAAQ;QACvB,OAAO;YACJ,MAAM,YAAY,CAAC,KAAK,GAAG,CAAC,YAAY,cAAc;QACzD;IACH;IAEA,iCAAiC;IACjC,MAAM,eAAe;QAClB,IAAI,MAAM,IAAI,KAAK,SAAS;YACzB,MAAM,KAAK,CAAC,YAAY,CAAC,aAAa;QACzC,OAAO;YACJ,MAAM,YAAY,CAAC;QACtB;IACH;IAEA,2CAA2C;IAC3C,MAAM,kBAAkB,MAAM,IAAI,KAAK,UAClC,MAAM,KAAK,CAAC,kBAAkB,KAC9B,cAAc;IAEnB,MAAM,cAAc,MAAM,IAAI,KAAK,UAC9B,MAAM,KAAK,CAAC,cAAc,KAC1B,cAAc;IAEnB,qBACG,uVAAC;QAAI,WAAU;;YAEX,MAAM,oBAAoB,kBACxB,uVAAC,2HAAA,CAAA,SAAM;gBACJ,SAAQ;gBACR,WAAU;gBACV,SAAS;gBACT,UAAU,CAAC;0BAEX,cAAA,uVAAC,0SAAA,CAAA,eAAY;oBAAC,WAAU;;;;;;;;;;;YAK7B,MAAM,mBAAmB,kBACvB,uVAAC,2HAAA,CAAA,SAAM;gBACJ,SAAQ;gBACR,WAAU;gBACV,SAAS;gBACT,UAAU,CAAC;0BAEX,cAAA,uVAAC,wSAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;;;;;;0BAK7B,uVAAC,4HAAA,CAAA,UAAO;gBAAC,MAAM;gBAAM,cAAc;;kCAChC,uVAAC,4HAAA,CAAA,iBAAc;wBAAC,OAAO;kCACpB,cAAA,uVAAC,2HAAA,CAAA,SAAM;4BACJ,IAAI;4BACJ,WAAU;4BACV,MAAK;4BACL,SAAQ;4BACR,MAAK;4BACL,iBAAe;sCAEf,cAAA,uVAAC;gCAAK,WAAU;;oCACZ;oCAAY;oCAAI;;;;;;;;;;;;;;;;;kCAI1B,uVAAC,4HAAA,CAAA,iBAAc;wBAAC,WAAU;wBAAwB,OAAM;wBAAS,MAAK;kCACnE,cAAA,uVAAC,4HAAA,CAAA,UAAO;;8CACL,uVAAC,4HAAA,CAAA,eAAY;oCAAC,aAAY;;;;;;8CAC1B,uVAAC,4HAAA,CAAA,cAAW;;sDACT,uVAAC,4HAAA,CAAA,eAAY;sDAAC;;;;;;sDACd,uVAAC,4HAAA,CAAA,eAAY;sDACT,YAAY,GAAG,CAAC,CAAC,qBACf,uVAAC,4HAAA,CAAA,cAAW;oDAET,OAAO,KAAK,QAAQ;oDACpB,UAAU,IAAM,iBAAiB;oDACjC,WAAU;;sEAEV,uVAAC;4DAAI,WAAU;sEACZ,cAAA,uVAAC;gEAAK,WAAU;;oEAAU;oEAAO;;;;;;;;;;;;wDAEnC,gBAAgB,sBAAQ,uVAAC,4RAAA,CAAA,YAAS;4DAAC,MAAM;4DAAI,WAAU;;;;;;;mDARnD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAkBzB,MAAM,mBAAmB,kBACvB,uVAAC,2HAAA,CAAA,SAAM;gBACJ,SAAQ;gBACR,WAAU;gBACV,SAAS;gBACT,UAAU,CAAC;0BAEX,cAAA,uVAAC,0SAAA,CAAA,eAAY;oBAAC,WAAU;;;;;;;;;;;YAK7B,MAAM,oBAAoB,kBACxB,uVAAC,2HAAA,CAAA,SAAM;gBACJ,SAAQ;gBACR,WAAU;gBACV,SAAS;gBACT,UAAU,CAAC;0BAEX,cAAA,uVAAC,4SAAA,CAAA,gBAAa;oBAAC,WAAU;;;;;;;;;;;;;;;;;AAKxC", "debugId": null}}, {"offset": {"line": 9661, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/bloomgoo/gold-exchange-platform/frontend/components/common/selector/page-size-selector.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { Button } from '@/components/ui/button';\r\nimport {\r\n   Command,\r\n   CommandEmpty,\r\n   CommandGroup,\r\n   CommandInput,\r\n   CommandItem,\r\n   CommandList,\r\n} from '@/components/ui/command';\r\nimport { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';\r\nimport { CheckIcon } from 'lucide-react';\r\nimport { useId, useState } from 'react';\r\nimport { Table as TableType } from \"@tanstack/react-table\";\r\n\r\n// Interface for table-based pagination\r\ninterface TablePageSizeProps {\r\n   table: TableType<any>;\r\n   type: 'table';\r\n}\r\n\r\n// Interface for custom pagination\r\ninterface CustomPageSizeProps {\r\n   type: 'custom';\r\n   pageSize: number;\r\n   onPageSizeChange: (pageSize: number) => void;\r\n}\r\n\r\n// Combined props type\r\ntype PageSizeSelectorProps = {\r\n   pageSizeOptions?: number[];\r\n   className?: string;\r\n   label?: string;\r\n   triggerClassName?: string;\r\n} & (TablePageSizeProps | CustomPageSizeProps);\r\n\r\nexport function PageSizeSelector(props: PageSizeSelectorProps) {\r\n   const id = useId();\r\n   const [open, setOpen] = useState<boolean>(false);\r\n   \r\n   // Default page size options if not provided\r\n   const pageSizeOptions = props.pageSizeOptions || [10, 20, 30, 50, 100];\r\n   \r\n   // Get current page size based on props type\r\n   const currentPageSize = props.type === 'table' \r\n      ? props.table.getState().pagination.pageSize \r\n      : props.pageSize;\r\n   \r\n   // Handle page size change\r\n   const handlePageSizeChange = (pageSize: number) => {\r\n      if (props.type === 'table') {\r\n         props.table.setPageSize(pageSize);\r\n      } else {\r\n         props.onPageSizeChange(pageSize);\r\n      }\r\n      setOpen(false);\r\n   };\r\n\r\n   return (\r\n      <div className=\"flex items-center space-x-2\">\r\n         {props.label && (\r\n            <span className=\"text-sm text-muted-foreground\">{props.label}</span>\r\n         )}\r\n         \r\n         <Popover open={open} onOpenChange={setOpen}>\r\n            <PopoverTrigger asChild>\r\n               <Button\r\n                  id={id}\r\n                  className={`flex items-center justify-center h-8 px-3 ${props.triggerClassName || ''}`}\r\n                  size=\"sm\"\r\n                  variant=\"outline\"\r\n                  role=\"combobox\"\r\n                  aria-expanded={open}\r\n               >\r\n                  <span className=\"text-sm font-medium\">\r\n                     {currentPageSize} bản ghi\r\n                  </span>\r\n               </Button>\r\n            </PopoverTrigger>\r\n            <PopoverContent className=\"border-input w-48 p-0\" align=\"start\" side=\"top\">\r\n               <Command>\r\n                  <CommandInput placeholder=\"Số bản ghi...\" />\r\n                  <CommandList>\r\n                     <CommandEmpty>Không tìm thấy tùy chọn.</CommandEmpty>\r\n                     <CommandGroup>\r\n                        {pageSizeOptions.map((size) => (\r\n                           <CommandItem\r\n                              key={size}\r\n                              value={size.toString()}\r\n                              onSelect={() => handlePageSizeChange(size)}\r\n                              className=\"flex items-center justify-between\"\r\n                           >\r\n                              <div className=\"flex items-center gap-2\">\r\n                                 <span className=\"text-xs\">{size} bản ghi</span>\r\n                              </div>\r\n                              {currentPageSize === size && <CheckIcon size={14} className=\"ml-auto\" />}\r\n                           </CommandItem>\r\n                        ))}\r\n                     </CommandGroup>\r\n                  </CommandList>\r\n               </Command>\r\n            </PopoverContent>\r\n         </Popover>\r\n      </div>\r\n   );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAQA;AACA;AACA;AAbA;;;;;;;AAqCO,SAAS,iBAAiB,KAA4B;IAC1D,MAAM,KAAK,CAAA,GAAA,8SAAA,CAAA,QAAK,AAAD;IACf,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,8SAAA,CAAA,WAAQ,AAAD,EAAW;IAE1C,4CAA4C;IAC5C,MAAM,kBAAkB,MAAM,eAAe,IAAI;QAAC;QAAI;QAAI;QAAI;QAAI;KAAI;IAEtE,4CAA4C;IAC5C,MAAM,kBAAkB,MAAM,IAAI,KAAK,UAClC,MAAM,KAAK,CAAC,QAAQ,GAAG,UAAU,CAAC,QAAQ,GAC1C,MAAM,QAAQ;IAEnB,0BAA0B;IAC1B,MAAM,uBAAuB,CAAC;QAC3B,IAAI,MAAM,IAAI,KAAK,SAAS;YACzB,MAAM,KAAK,CAAC,WAAW,CAAC;QAC3B,OAAO;YACJ,MAAM,gBAAgB,CAAC;QAC1B;QACA,QAAQ;IACX;IAEA,qBACG,uVAAC;QAAI,WAAU;;YACX,MAAM,KAAK,kBACT,uVAAC;gBAAK,WAAU;0BAAiC,MAAM,KAAK;;;;;;0BAG/D,uVAAC,4HAAA,CAAA,UAAO;gBAAC,MAAM;gBAAM,cAAc;;kCAChC,uVAAC,4HAAA,CAAA,iBAAc;wBAAC,OAAO;kCACpB,cAAA,uVAAC,2HAAA,CAAA,SAAM;4BACJ,IAAI;4BACJ,WAAW,CAAC,0CAA0C,EAAE,MAAM,gBAAgB,IAAI,IAAI;4BACtF,MAAK;4BACL,SAAQ;4BACR,MAAK;4BACL,iBAAe;sCAEf,cAAA,uVAAC;gCAAK,WAAU;;oCACZ;oCAAgB;;;;;;;;;;;;;;;;;kCAI1B,uVAAC,4HAAA,CAAA,iBAAc;wBAAC,WAAU;wBAAwB,OAAM;wBAAQ,MAAK;kCAClE,cAAA,uVAAC,4HAAA,CAAA,UAAO;;8CACL,uVAAC,4HAAA,CAAA,eAAY;oCAAC,aAAY;;;;;;8CAC1B,uVAAC,4HAAA,CAAA,cAAW;;sDACT,uVAAC,4HAAA,CAAA,eAAY;sDAAC;;;;;;sDACd,uVAAC,4HAAA,CAAA,eAAY;sDACT,gBAAgB,GAAG,CAAC,CAAC,qBACnB,uVAAC,4HAAA,CAAA,cAAW;oDAET,OAAO,KAAK,QAAQ;oDACpB,UAAU,IAAM,qBAAqB;oDACrC,WAAU;;sEAEV,uVAAC;4DAAI,WAAU;sEACZ,cAAA,uVAAC;gEAAK,WAAU;;oEAAW;oEAAK;;;;;;;;;;;;wDAElC,oBAAoB,sBAAQ,uVAAC,4RAAA,CAAA,YAAS;4DAAC,MAAM;4DAAI,WAAU;;;;;;;mDARvD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAkBnC", "debugId": null}}, {"offset": {"line": 9845, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/bloomgoo/gold-exchange-platform/frontend/components/common/data-table/table-footer.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport { Table as TableType } from \"@tanstack/react-table\"\r\nimport { But<PERSON> } from \"@/components/ui/button\"\r\nimport { ChevronLeft, ChevronRight, ChevronsLeft, ChevronsRight } from \"lucide-react\"\r\nimport { PageSelector } from \"@/components/common/selector/page-selector\"\r\nimport { PageSizeSelector } from \"@/components/common/selector/page-size-selector\"\r\n\r\ninterface TableFooterProps<TData> {\r\n  table: TableType<TData>\r\n  className?: string\r\n  totalItems?: number\r\n  isShowSelectedRows?: boolean\r\n  onShowSelectedRows?: () => void\r\n}\r\n\r\nexport function TableFooter<TData>({\r\n  table,\r\n  className,\r\n  totalItems,\r\n  isShowSelectedRows,\r\n  onShowSelectedRows,\r\n}: TableFooterProps<TData>) {\r\n  // Kiểm tra xem có hàng nào được chọn không\r\n  const selectedRowsCount = table.getFilteredSelectedRowModel().rows.length;\r\n  const hasSelectedRows = selectedRowsCount > 0;\r\n\r\n  return (\r\n    <div className=\"sticky bottom-0 bg-background border-t mt-auto\">\r\n      <div className=\"flex items-center justify-between space-x-2 px-4 py-2\">\r\n        <div className=\"flex items-center space-x-2\">\r\n          <PageSizeSelector\r\n            type=\"table\"\r\n            table={table}\r\n            pageSizeOptions={[20, 50, 100, 200]}\r\n            triggerClassName=\"w-[120px]\"\r\n          />\r\n\r\n          {/* Hiển thị thông tin về hàng đã chọn khi có hàng được chọn */}\r\n          {hasSelectedRows && (\r\n            <p className=\"text-sm text-muted-foreground\">\r\n              {selectedRowsCount} trên{\" \"}\r\n              {table.getFilteredRowModel().rows.length} bản ghi đã chọn.\r\n            </p>\r\n          )}\r\n        </div>\r\n\r\n        <div className=\"flex items-center space-x-6 lg:space-x-8\">\r\n          <div className=\"flex w-[100x] items-center justify-center text-sm font-medium\">\r\n            Hiển thị {table.getRowCount()} trên {totalItems} bản ghi\r\n          </div>\r\n          <PageSelector\r\n            type=\"table\"\r\n            table={table}\r\n            showFirstLastButtons={true}\r\n            showPrevNextButtons={true}\r\n            maxPagesToShow={7}\r\n          />\r\n        </div>\r\n      </div>\r\n    </div>\r\n  )\r\n}"], "names": [], "mappings": ";;;;AAKA;AACA;AANA;;;;AAgBO,SAAS,YAAmB,EACjC,KAAK,EACL,SAAS,EACT,UAAU,EACV,kBAAkB,EAClB,kBAAkB,EACM;IACxB,2CAA2C;IAC3C,MAAM,oBAAoB,MAAM,2BAA2B,GAAG,IAAI,CAAC,MAAM;IACzE,MAAM,kBAAkB,oBAAoB;IAE5C,qBACE,uVAAC;QAAI,WAAU;kBACb,cAAA,uVAAC;YAAI,WAAU;;8BACb,uVAAC;oBAAI,WAAU;;sCACb,uVAAC,6JAAA,CAAA,mBAAgB;4BACf,MAAK;4BACL,OAAO;4BACP,iBAAiB;gCAAC;gCAAI;gCAAI;gCAAK;6BAAI;4BACnC,kBAAiB;;;;;;wBAIlB,iCACC,uVAAC;4BAAE,WAAU;;gCACV;gCAAkB;gCAAM;gCACxB,MAAM,mBAAmB,GAAG,IAAI,CAAC,MAAM;gCAAC;;;;;;;;;;;;;8BAK/C,uVAAC;oBAAI,WAAU;;sCACb,uVAAC;4BAAI,WAAU;;gCAAgE;gCACnE,MAAM,WAAW;gCAAG;gCAAO;gCAAW;;;;;;;sCAElD,uVAAC,qJAAA,CAAA,eAAY;4BACX,MAAK;4BACL,OAAO;4BACP,sBAAsB;4BACtB,qBAAqB;4BACrB,gBAAgB;;;;;;;;;;;;;;;;;;;;;;;AAM5B", "debugId": null}}, {"offset": {"line": 9954, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/bloomgoo/gold-exchange-platform/frontend/components/common/user/transactions/components/transaction-statistics.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { ArrowDownToLine, ArrowUpFromLine, CheckCircle, Clock, XCircle, AlertCircle } from 'lucide-react';\r\nimport { Card, CardContent } from '@/components/ui/card';\r\nimport { Skeleton } from '@/components/ui/skeleton';\r\n\r\ninterface TransactionStatisticsProps {\r\n  onRefresh?: boolean;\r\n  statistics: {\r\n    all: number;\r\n    pending: number;\r\n    processing: number;\r\n    completed: number;\r\n    failed: number;\r\n    cancelled: number;\r\n  };\r\n}\r\n\r\nexport function TransactionStatistics({ onRefresh = false, statistics }: TransactionStatisticsProps) {\r\n  const items = [\r\n    {\r\n      title: 'Tổng giao dịch',\r\n      value: statistics.all,\r\n      icon: <ArrowDownToLine className=\"h-4 w-4 text-blue-500\" />,\r\n      description: 'Tổng số giao dịch',\r\n      color: 'bg-blue-50 text-blue-700 border-blue-200',\r\n    },\r\n    {\r\n      title: 'Đang chờ',\r\n      value: statistics.pending,\r\n      icon: <Clock className=\"h-4 w-4 text-yellow-500\" />,\r\n      description: 'Giao dịch đang chờ xử lý',\r\n      color: 'bg-yellow-50 text-yellow-700 border-yellow-200',\r\n    },\r\n    {\r\n      title: 'Hoàn thành',\r\n      value: statistics.completed,\r\n      icon: <CheckCircle className=\"h-4 w-4 text-green-500\" />,\r\n      description: 'Giao dịch đã hoàn thành',\r\n      color: 'bg-green-50 text-green-700 border-green-200',\r\n    },\r\n    {\r\n      title: 'Đã hủy',\r\n      value: statistics.cancelled,\r\n      icon: <XCircle className=\"h-4 w-4 text-gray-500\" />,\r\n      description: 'Giao dịch đã hủy',\r\n      color: 'bg-gray-50 text-gray-700 border-gray-200',\r\n    },\r\n    {\r\n      title: 'Thất bại',\r\n      value: statistics.failed,\r\n      icon: <AlertCircle className=\"h-4 w-4 text-red-500\" />,\r\n      description: 'Giao dịch thất bại',\r\n      color: 'bg-red-50 text-red-700 border-red-200',\r\n    },\r\n  ];\r\n\r\n  return (\r\n    <div className=\"grid grid-cols-2 md:grid-cols-5 gap-2\">\r\n      {items.map((item, index) => (\r\n        <Card key={index} className={`border ${item.color}`}>\r\n          <CardContent className=\"p-3\">\r\n            <div className=\"flex items-center justify-between\">\r\n              <div className=\"flex flex-col\">\r\n                <span className=\"text-xs font-medium\">{item.title}</span>\r\n                {onRefresh ? (\r\n                  <Skeleton className=\"h-6 w-12 mt-1\" />\r\n                ) : (\r\n                  <span className=\"text-lg font-bold\">{item.value}</span>\r\n                )}\r\n              </div>\r\n              <div className=\"rounded-full p-2 bg-white/80\">\r\n                {item.icon}\r\n              </div>\r\n            </div>\r\n          </CardContent>\r\n        </Card>\r\n      ))}\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AAJA;;;;;AAkBO,SAAS,sBAAsB,EAAE,YAAY,KAAK,EAAE,UAAU,EAA8B;IACjG,MAAM,QAAQ;QACZ;YACE,OAAO;YACP,OAAO,WAAW,GAAG;YACrB,oBAAM,uVAAC,wTAAA,CAAA,kBAAe;gBAAC,WAAU;;;;;;YACjC,aAAa;YACb,OAAO;QACT;QACA;YACE,OAAO;YACP,OAAO,WAAW,OAAO;YACzB,oBAAM,uVAAC,wRAAA,CAAA,QAAK;gBAAC,WAAU;;;;;;YACvB,aAAa;YACb,OAAO;QACT;QACA;YACE,OAAO;YACP,OAAO,WAAW,SAAS;YAC3B,oBAAM,uVAAC,+SAAA,CAAA,cAAW;gBAAC,WAAU;;;;;;YAC7B,aAAa;YACb,OAAO;QACT;QACA;YACE,OAAO;YACP,OAAO,WAAW,SAAS;YAC3B,oBAAM,uVAAC,gSAAA,CAAA,UAAO;gBAAC,WAAU;;;;;;YACzB,aAAa;YACb,OAAO;QACT;QACA;YACE,OAAO;YACP,OAAO,WAAW,MAAM;YACxB,oBAAM,uVAAC,wSAAA,CAAA,cAAW;gBAAC,WAAU;;;;;;YAC7B,aAAa;YACb,OAAO;QACT;KACD;IAED,qBACE,uVAAC;QAAI,WAAU;kBACZ,MAAM,GAAG,CAAC,CAAC,MAAM,sBAChB,uVAAC,yHAAA,CAAA,OAAI;gBAAa,WAAW,CAAC,OAAO,EAAE,KAAK,KAAK,EAAE;0BACjD,cAAA,uVAAC,yHAAA,CAAA,cAAW;oBAAC,WAAU;8BACrB,cAAA,uVAAC;wBAAI,WAAU;;0CACb,uVAAC;gCAAI,WAAU;;kDACb,uVAAC;wCAAK,WAAU;kDAAuB,KAAK,KAAK;;;;;;oCAChD,0BACC,uVAAC,6HAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;6DAEpB,uVAAC;wCAAK,WAAU;kDAAqB,KAAK,KAAK;;;;;;;;;;;;0CAGnD,uVAAC;gCAAI,WAAU;0CACZ,KAAK,IAAI;;;;;;;;;;;;;;;;;eAZP;;;;;;;;;;AAoBnB", "debugId": null}}, {"offset": {"line": 10114, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/bloomgoo/gold-exchange-platform/frontend/components/ui/calendar.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport { ChevronLeft, ChevronRight } from \"lucide-react\"\r\nimport { DayPicker } from \"react-day-picker\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\nimport { buttonVariants } from \"@/components/ui/button\"\r\n\r\nfunction Calendar({\r\n  className,\r\n  classNames,\r\n  showOutsideDays = true,\r\n  ...props\r\n}: React.ComponentProps<typeof DayPicker>) {\r\n  return (\r\n    <DayPicker\r\n      showOutsideDays={showOutsideDays}\r\n      className={cn(\"p-3\", className)}\r\n      classNames={{\r\n        months: \"flex flex-col sm:flex-row gap-2\",\r\n        month: \"flex flex-col gap-4\",\r\n        caption: \"flex justify-center pt-1 relative items-center w-full\",\r\n        caption_label: \"text-sm font-medium\",\r\n        nav: \"flex items-center gap-1\",\r\n        nav_button: cn(\r\n          buttonVariants({ variant: \"outline\" }),\r\n          \"size-7 bg-transparent p-0 opacity-50 hover:opacity-100\"\r\n        ),\r\n        nav_button_previous: \"absolute left-1\",\r\n        nav_button_next: \"absolute right-1\",\r\n        table: \"w-full border-collapse space-x-1\",\r\n        head_row: \"flex\",\r\n        head_cell:\r\n          \"text-muted-foreground rounded-md w-8 font-normal text-[0.8rem]\",\r\n        row: \"flex w-full mt-2\",\r\n        cell: cn(\r\n          \"relative p-0 text-center text-sm focus-within:relative focus-within:z-20 [&:has([aria-selected])]:bg-accent [&:has([aria-selected].day-range-end)]:rounded-r-md\",\r\n          props.mode === \"range\"\r\n            ? \"[&:has(>.day-range-end)]:rounded-r-md [&:has(>.day-range-start)]:rounded-l-md first:[&:has([aria-selected])]:rounded-l-md last:[&:has([aria-selected])]:rounded-r-md\"\r\n            : \"[&:has([aria-selected])]:rounded-md\"\r\n        ),\r\n        day: cn(\r\n          buttonVariants({ variant: \"ghost\" }),\r\n          \"size-8 p-0 font-normal aria-selected:opacity-100\"\r\n        ),\r\n        day_range_start:\r\n          \"day-range-start aria-selected:bg-primary aria-selected:text-primary-foreground\",\r\n        day_range_end:\r\n          \"day-range-end aria-selected:bg-primary aria-selected:text-primary-foreground\",\r\n        day_selected:\r\n          \"bg-primary text-primary-foreground hover:bg-primary hover:text-primary-foreground focus:bg-primary focus:text-primary-foreground\",\r\n        day_today: \"bg-accent text-accent-foreground\",\r\n        day_outside:\r\n          \"day-outside text-muted-foreground aria-selected:text-muted-foreground\",\r\n        day_disabled: \"text-muted-foreground opacity-50\",\r\n        day_range_middle:\r\n          \"aria-selected:bg-accent aria-selected:text-accent-foreground\",\r\n        day_hidden: \"invisible\",\r\n        ...classNames,\r\n      }}\r\n      components={{\r\n        IconLeft: ({ className, ...props }) => (\r\n          <ChevronLeft className={cn(\"size-4\", className)} {...props} />\r\n        ),\r\n        IconRight: ({ className, ...props }) => (\r\n          <ChevronRight className={cn(\"size-4\", className)} {...props} />\r\n        ),\r\n      }}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport { Calendar }\r\n"], "names": [], "mappings": ";;;;AAGA;AAAA;AACA;AAEA;AAAA;AACA;AAPA;;;;;;AASA,SAAS,SAAS,EAChB,SAAS,EACT,UAAU,EACV,kBAAkB,IAAI,EACtB,GAAG,OACoC;IACvC,qBACE,uVAAC,mRAAA,CAAA,YAAS;QACR,iBAAiB;QACjB,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EAAE,OAAO;QACrB,YAAY;YACV,QAAQ;YACR,OAAO;YACP,SAAS;YACT,eAAe;YACf,KAAK;YACL,YAAY,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EACX,CAAA,GAAA,2HAAA,CAAA,iBAAc,AAAD,EAAE;gBAAE,SAAS;YAAU,IACpC;YAEF,qBAAqB;YACrB,iBAAiB;YACjB,OAAO;YACP,UAAU;YACV,WACE;YACF,KAAK;YACL,MAAM,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EACL,mKACA,MAAM,IAAI,KAAK,UACX,yKACA;YAEN,KAAK,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EACJ,CAAA,GAAA,2HAAA,CAAA,iBAAc,AAAD,EAAE;gBAAE,SAAS;YAAQ,IAClC;YAEF,iBACE;YACF,eACE;YACF,cACE;YACF,WAAW;YACX,aACE;YACF,cAAc;YACd,kBACE;YACF,YAAY;YACZ,GAAG,UAAU;QACf;QACA,YAAY;YACV,UAAU,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,iBAChC,uVAAC,wSAAA,CAAA,cAAW;oBAAC,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EAAE,UAAU;oBAAa,GAAG,KAAK;;;;;;YAE5D,WAAW,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,iBACjC,uVAAC,0SAAA,CAAA,eAAY;oBAAC,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EAAE,UAAU;oBAAa,GAAG,KAAK;;;;;;QAE/D;QACC,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 10195, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/bloomgoo/gold-exchange-platform/frontend/components/common/admin/transactions/components/date-range-picker.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport * as React from 'react';\r\nimport { format } from 'date-fns';\r\nimport { vi } from 'date-fns/locale';\r\nimport { Calendar as CalendarIcon } from 'lucide-react';\r\nimport { DateRange } from 'react-day-picker';\r\n\r\nimport { cn } from '@/lib/utils';\r\nimport { Button } from '@/components/ui/button';\r\nimport { Calendar } from '@/components/ui/calendar';\r\nimport {\r\n  Popover,\r\n  PopoverContent,\r\n  PopoverTrigger,\r\n} from '@/components/ui/popover';\r\n\r\ninterface DateRangePickerProps {\r\n  dateRange: DateRange | undefined;\r\n  onDateRangeChange: (range: DateRange | undefined) => void;\r\n  className?: string;\r\n}\r\n\r\nexport function DateRangePicker({\r\n  dateRange,\r\n  onDateRangeChange,\r\n  className,\r\n}: DateRangePickerProps) {\r\n  const [isOpen, setIsOpen] = React.useState(false);\r\n\r\n  return (\r\n    <div className={cn('grid gap-2', className)}>\r\n      <Popover open={isOpen} onOpenChange={setIsOpen}>\r\n        <PopoverTrigger asChild>\r\n          <Button\r\n            id=\"date\"\r\n            variant={'outline'}\r\n            size=\"sm\"\r\n            className={cn(\r\n              'w-[300px] justify-start text-left font-normal',\r\n              !dateRange && 'text-muted-foreground'\r\n            )}\r\n          >\r\n            <CalendarIcon className=\"mr-2 h-4 w-4\" />\r\n            {dateRange?.from ? (\r\n              dateRange.to ? (\r\n                <>\r\n                  {format(dateRange.from, 'dd/MM/yyyy', { locale: vi })} -{' '}\r\n                  {format(dateRange.to, 'dd/MM/yyyy', { locale: vi })}\r\n                </>\r\n              ) : (\r\n                format(dateRange.from, 'dd/MM/yyyy', { locale: vi })\r\n              )\r\n            ) : (\r\n              <span>Chọn khoảng thời gian</span>\r\n            )}\r\n          </Button>\r\n        </PopoverTrigger>\r\n        <PopoverContent className=\"w-auto p-0\" align=\"start\">\r\n          <Calendar\r\n            initialFocus\r\n            mode=\"range\"\r\n            defaultMonth={dateRange?.from}\r\n            selected={dateRange}\r\n            onSelect={onDateRangeChange}\r\n            numberOfMonths={2}\r\n            locale={vi}\r\n          />\r\n          <div className=\"flex items-center justify-between p-3 border-t\">\r\n            <Button\r\n              variant=\"ghost\"\r\n              size=\"sm\"\r\n              onClick={() => {\r\n                onDateRangeChange(undefined);\r\n                setIsOpen(false);\r\n              }}\r\n            >\r\n              Xóa\r\n            </Button>\r\n            <Button\r\n              size=\"sm\"\r\n              onClick={() => {\r\n                setIsOpen(false);\r\n              }}\r\n            >\r\n              Áp dụng\r\n            </Button>\r\n          </div>\r\n        </PopoverContent>\r\n      </Popover>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAGA;AAAA;AACA;AACA;AACA;AAXA;;;;;;;;;;AAuBO,SAAS,gBAAgB,EAC9B,SAAS,EACT,iBAAiB,EACjB,SAAS,EACY;IACrB,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,8SAAA,CAAA,WAAc,AAAD,EAAE;IAE3C,qBACE,uVAAC;QAAI,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EAAE,cAAc;kBAC/B,cAAA,uVAAC,4HAAA,CAAA,UAAO;YAAC,MAAM;YAAQ,cAAc;;8BACnC,uVAAC,4HAAA,CAAA,iBAAc;oBAAC,OAAO;8BACrB,cAAA,uVAAC,2HAAA,CAAA,SAAM;wBACL,IAAG;wBACH,SAAS;wBACT,MAAK;wBACL,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EACV,iDACA,CAAC,aAAa;;0CAGhB,uVAAC,8RAAA,CAAA,WAAY;gCAAC,WAAU;;;;;;4BACvB,WAAW,OACV,UAAU,EAAE,iBACV;;oCACG,CAAA,GAAA,6MAAA,CAAA,SAAM,AAAD,EAAE,UAAU,IAAI,EAAE,cAAc;wCAAE,QAAQ,mMAAA,CAAA,KAAE;oCAAC;oCAAG;oCAAG;oCACxD,CAAA,GAAA,6MAAA,CAAA,SAAM,AAAD,EAAE,UAAU,EAAE,EAAE,cAAc;wCAAE,QAAQ,mMAAA,CAAA,KAAE;oCAAC;;+CAGnD,CAAA,GAAA,6MAAA,CAAA,SAAM,AAAD,EAAE,UAAU,IAAI,EAAE,cAAc;gCAAE,QAAQ,mMAAA,CAAA,KAAE;4BAAC,mBAGpD,uVAAC;0CAAK;;;;;;;;;;;;;;;;;8BAIZ,uVAAC,4HAAA,CAAA,iBAAc;oBAAC,WAAU;oBAAa,OAAM;;sCAC3C,uVAAC,6HAAA,CAAA,WAAQ;4BACP,YAAY;4BACZ,MAAK;4BACL,cAAc,WAAW;4BACzB,UAAU;4BACV,UAAU;4BACV,gBAAgB;4BAChB,QAAQ,mMAAA,CAAA,KAAE;;;;;;sCAEZ,uVAAC;4BAAI,WAAU;;8CACb,uVAAC,2HAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,MAAK;oCACL,SAAS;wCACP,kBAAkB;wCAClB,UAAU;oCACZ;8CACD;;;;;;8CAGD,uVAAC,2HAAA,CAAA,SAAM;oCACL,MAAK;oCACL,SAAS;wCACP,UAAU;oCACZ;8CACD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQb", "debugId": null}}, {"offset": {"line": 10346, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/bloomgoo/gold-exchange-platform/frontend/components/common/admin/transactions/components/status-tabs.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { cn } from '@/lib/utils';\r\nimport { Badge } from '@/components/ui/badge';\r\n\r\nexport type StatusFilter = 'all' | 'pending' | 'processing' | 'completed' | 'failed' | 'cancelled';\r\n\r\ninterface StatusTabsProps {\r\n  currentStatus: StatusFilter;\r\n  onStatusChange: (status: StatusFilter) => void;\r\n  counts: {\r\n    all: number;\r\n    pending: number;\r\n    processing: number;\r\n    completed: number;\r\n    failed: number;\r\n    cancelled: number;\r\n  };\r\n  className?: string;\r\n}\r\n\r\nexport function StatusTabs({\r\n  currentStatus,\r\n  onStatusChange,\r\n  counts,\r\n  className\r\n}: StatusTabsProps) {\r\n  const tabs = [\r\n    {\r\n      id: 'all' as const,\r\n      label: 'Tất cả',\r\n      count: counts.all,\r\n    },\r\n    {\r\n      id: 'pending' as const,\r\n      label: 'Đang chờ',\r\n      count: counts.pending,\r\n    },\r\n    {\r\n      id: 'processing' as const,\r\n      label: 'Đang xử lý',\r\n      count: counts.processing || 0,\r\n    },\r\n    {\r\n      id: 'completed' as const,\r\n      label: 'Hoàn thành',\r\n      count: counts.completed,\r\n    },\r\n    {\r\n      id: 'failed' as const,\r\n      label: 'Th<PERSON>t bại',\r\n      count: counts.failed,\r\n    },\r\n    {\r\n      id: 'cancelled' as const,\r\n      label: 'Đã hủy',\r\n      count: counts.cancelled,\r\n    }\r\n  ];\r\n\r\n  return (\r\n    <div className={cn(\"flex items-center space-x-1 bg-muted/50 p-1 rounded-md\", className)}>\r\n      {tabs.map((tab) => (\r\n        <button\r\n          key={tab.id}\r\n          onClick={() => onStatusChange(tab.id)}\r\n          className={cn(\r\n            \"flex items-center px-3 py-1.5 text-sm font-medium rounded-md transition-colors\",\r\n            currentStatus === tab.id\r\n              ? \"bg-background text-foreground shadow-sm\"\r\n              : \"text-muted-foreground hover:bg-background/50\"\r\n          )}\r\n        >\r\n          {tab.label}\r\n          <Badge\r\n            variant=\"secondary\"\r\n            className=\"ml-2 bg-muted text-muted-foreground\"\r\n          >\r\n            {tab.count}\r\n          </Badge>\r\n        </button>\r\n      ))}\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AAAA;AACA;AAHA;;;;AAqBO,SAAS,WAAW,EACzB,aAAa,EACb,cAAc,EACd,MAAM,EACN,SAAS,EACO;IAChB,MAAM,OAAO;QACX;YACE,IAAI;YACJ,OAAO;YACP,OAAO,OAAO,GAAG;QACnB;QACA;YACE,IAAI;YACJ,OAAO;YACP,OAAO,OAAO,OAAO;QACvB;QACA;YACE,IAAI;YACJ,OAAO;YACP,OAAO,OAAO,UAAU,IAAI;QAC9B;QACA;YACE,IAAI;YACJ,OAAO;YACP,OAAO,OAAO,SAAS;QACzB;QACA;YACE,IAAI;YACJ,OAAO;YACP,OAAO,OAAO,MAAM;QACtB;QACA;YACE,IAAI;YACJ,OAAO;YACP,OAAO,OAAO,SAAS;QACzB;KACD;IAED,qBACE,uVAAC;QAAI,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EAAE,0DAA0D;kBAC1E,KAAK,GAAG,CAAC,CAAC,oBACT,uVAAC;gBAEC,SAAS,IAAM,eAAe,IAAI,EAAE;gBACpC,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EACV,kFACA,kBAAkB,IAAI,EAAE,GACpB,4CACA;;oBAGL,IAAI,KAAK;kCACV,uVAAC,0HAAA,CAAA,QAAK;wBACJ,SAAQ;wBACR,WAAU;kCAET,IAAI,KAAK;;;;;;;eAdP,IAAI,EAAE;;;;;;;;;;AAoBrB", "debugId": null}}, {"offset": {"line": 10424, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/bloomgoo/gold-exchange-platform/frontend/components/common/user/transactions/user-transactions.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { useState, useEffect, useCallback } from 'react';\r\nimport { RefreshCcw } from 'lucide-react';\r\nimport { DateRange } from 'react-day-picker';\r\nimport { DataTable } from '../../data-table/data-table';\r\nimport { api } from '@/lib/api';\r\nimport { TransactionDto, TransactionStatus } from '../../admin/transactions/type';\r\nimport { getUserTransactionCell } from './table/cell';\r\nimport { DetailSheet } from './detail-sheet';\r\n\r\nimport { useAuth } from '@/hooks/use-auth';\r\nimport { TableToolbar } from '../../data-table/table-toolbar';\r\nimport { TableFooter } from '../../data-table/table-footer';\r\nimport { useReactTable, ColumnFiltersState, PaginationState, SortingState, VisibilityState, getCoreRowModel, getSortedRowModel, getFilteredRowModel } from '@tanstack/react-table';\r\nimport { PaginationResponse } from '@/lib/response';\r\nimport { toast } from 'sonner';\r\nimport { TransactionStatistics } from './components/transaction-statistics';\r\nimport { Button } from '@/components/ui/button';\r\nimport { DateRangePicker } from '../../admin/transactions/components/date-range-picker';\r\nimport { StatusTabs, StatusFilter } from '../../admin/transactions/components/status-tabs';\r\n\r\nexport default function UserTransactions() {\r\n  const { user } = useAuth();\r\n  const userId = user?.id;\r\n\r\n  // State cho dữ liệu và loading\r\n  const [transactions, setTransactions] = useState<TransactionDto[]>([]);\r\n  const [loading, setLoading] = useState(true);\r\n  const [isRefreshing, setIsRefreshing] = useState(false);\r\n  const [isUpdating, setIsUpdating] = useState(false);\r\n  const [totalRows, setTotalRows] = useState(0);\r\n  const [statusCounts, setStatusCounts] = useState({\r\n    all: 0,\r\n    pending: 0,\r\n    processing: 0,\r\n    completed: 0,\r\n    failed: 0,\r\n    cancelled: 0\r\n  });\r\n\r\n  // State cho form và detail\r\n  const [showTransactionDetailSheet, setShowTransactionDetailSheet] = useState(false);\r\n  const [selectedTransaction, setSelectedTransaction] = useState<TransactionDto | undefined>(undefined);\r\n\r\n  // State cho table\r\n  const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([]);\r\n  const [columnVisibility, setColumnVisibility] = useState<VisibilityState>({});\r\n  const [sorting, setSorting] = useState<SortingState>([]);\r\n  const [pagination, setPagination] = useState<PaginationState>({\r\n    pageIndex: 0,\r\n    pageSize: 20,\r\n  });\r\n  const [statusFilter, setStatusFilter] = useState<StatusFilter>('all');\r\n  const [dateRange, setDateRange] = useState<DateRange | undefined>(undefined);\r\n  const [globalFilter, setGlobalFilter] = useState<string>('');\r\n\r\n  // Fetch dữ liệu\r\n  const fetchTransactions = useCallback(async () => {\r\n    if (!userId) return;\r\n\r\n    setLoading(true);\r\n    setIsRefreshing(true);\r\n    try {\r\n      // Tạo query params\r\n      const params = new URLSearchParams();\r\n      params.append('page', String(pagination.pageIndex + 1));\r\n      params.append('limit', String(pagination.pageSize));\r\n\r\n      // Xây dựng filter string\r\n      let filterString = `userId:${userId}`;\r\n\r\n      // Thêm filter theo trạng thái\r\n      if (statusFilter !== 'all') {\r\n        const statusValue = statusFilter.toUpperCase();\r\n\r\n        // Kiểm tra nếu là PROCESSING, bỏ qua filter vì enum trong DB chưa được cập nhật\r\n        if (statusValue === 'PROCESSING') {\r\n           \r\n          // Không thêm filter status, chỉ giữ filter userId\r\n        } else {\r\n           \r\n          filterString += `,status:${statusValue}`;\r\n        }\r\n      }\r\n\r\n      // Thêm filter string vào params\r\n      params.append('filter', filterString);\r\n\r\n      // Thêm relations để lấy tất cả các mối quan hệ\r\n      params.append('relations', 'user,wallet,paymentMethod,creator,updater,deleter');\r\n\r\n      // Thêm filter theo khoảng thời gian\r\n      if (dateRange?.from) {\r\n        const fromDate = new Date(dateRange.from);\r\n        fromDate.setHours(0, 0, 0, 0);\r\n        params.append('fromDate', fromDate.toISOString());\r\n\r\n        if (dateRange.to) {\r\n          const toDate = new Date(dateRange.to);\r\n          toDate.setHours(23, 59, 59, 999);\r\n          params.append('toDate', toDate.toISOString());\r\n        } else {\r\n          // Nếu chỉ chọn 1 ngày, lấy cả ngày đó\r\n          const toDate = new Date(dateRange.from);\r\n          toDate.setHours(23, 59, 59, 999);\r\n          params.append('toDate', toDate.toISOString());\r\n        }\r\n      }\r\n\r\n      // Thêm tìm kiếm toàn cục\r\n      if (globalFilter) {\r\n        params.append('search', globalFilter);\r\n      }\r\n\r\n      // Thêm sorting nếu có\r\n      if (sorting.length > 0) {\r\n        params.append('sortBy', sorting[0].id);\r\n        params.append('sortOrder', sorting[0].desc ? 'DESC' : 'ASC');\r\n      }\r\n\r\n      // Gọi API\r\n      const response = await api.get<PaginationResponse<TransactionDto>>(`transactions?${params.toString()}`);\r\n\r\n      // Xử lý dữ liệu từ API theo cấu trúc chuẩn PaginationResponse\r\n      if (response && typeof response === 'object') {\r\n        // Kiểm tra nếu response là PaginationResponse (có data và meta)\r\n        if ('data' in response && Array.isArray(response.data) && 'meta' in response) {\r\n          setTransactions(response.data);\r\n          setTotalRows(response.meta?.itemCount || 0);\r\n        }\r\n        // Kiểm tra nếu response là mảng trực tiếp\r\n        else if (Array.isArray(response)) {\r\n          setTransactions(response);\r\n          setTotalRows(response.length);\r\n        }\r\n        // Trường hợp khác - không đúng cấu trúc\r\n        else {\r\n          console.error('Unexpected API response structure:', response);\r\n          toast.error('Cấu trúc dữ liệu không đúng định dạng');\r\n          setTransactions([]);\r\n          setTotalRows(0);\r\n        }\r\n      }\r\n    } catch (error) {\r\n      console.error('Lỗi khi tải dữ liệu giao dịch:', error);\r\n      toast.error('Không thể tải dữ liệu giao dịch. Vui lòng thử lại sau.');\r\n\r\n      // Đặt trạng thái về rỗng khi có lỗi\r\n      setTransactions([]);\r\n      setTotalRows(0);\r\n      // Không cập nhật statusCounts ở đây để tránh ghi đè lên dữ liệu thống kê từ API\r\n    } finally {\r\n      setLoading(false);\r\n      setIsRefreshing(false);\r\n    }\r\n  }, [pagination, statusFilter, sorting, dateRange, globalFilter, userId]);\r\n\r\n  // Fetch dữ liệu khi component mount hoặc khi các dependencies thay đổi\r\n  useEffect(() => {\r\n    if (userId) {\r\n       \r\n      fetchTransactions();\r\n    }\r\n  }, [fetchTransactions, userId]);\r\n\r\n  // Fetch thống kê số lượng giao dịch theo trạng thái cho người dùng\r\n  // Chỉ gọi khi component mount hoặc userId thay đổi, không phụ thuộc vào statusFilter\r\n  useEffect(() => {\r\n    if (userId) {\r\n       \r\n      fetchStatistics();\r\n    }\r\n  }, [userId]);\r\n\r\n  // Fetch thống kê số lượng giao dịch theo trạng thái cho người dùng\r\n  const fetchStatistics = async () => {\r\n    if (!userId) return;\r\n\r\n    try {\r\n       \r\n      // Gọi API thống kê - không cần truyền statusFilter để lấy thống kê toàn bộ\r\n      // Backend sẽ tự lọc dựa trên JWT token để chỉ lấy thống kê của người dùng hiện tại\r\n      const response = await api.get('transactions/statistics');\r\n\r\n      if (response) {\r\n        // API có thể trả về dữ liệu trong response hoặc response.data\r\n        const statisticsData = response && typeof response === 'object' && 'data' in response\r\n          ? response.data\r\n          : response;\r\n\r\n        // Cập nhật state với dữ liệu từ API\r\n        const updatedCounts = {\r\n          all: 0,\r\n          pending: 0,\r\n          processing: 0,\r\n          completed: 0,\r\n          failed: 0,\r\n          cancelled: 0\r\n        };\r\n\r\n        // Kiểm tra và trích xuất dữ liệu từ response\r\n        if (statisticsData && typeof statisticsData === 'object') {\r\n          if ('total' in statisticsData && typeof statisticsData.total === 'number') {\r\n            updatedCounts.all = statisticsData.total;\r\n          }\r\n          if ('pending' in statisticsData && typeof statisticsData.pending === 'number') {\r\n            updatedCounts.pending = statisticsData.pending;\r\n          }\r\n          if ('processing' in statisticsData && typeof statisticsData.processing === 'number') {\r\n            updatedCounts.processing = statisticsData.processing;\r\n          }\r\n          if ('completed' in statisticsData && typeof statisticsData.completed === 'number') {\r\n            updatedCounts.completed = statisticsData.completed;\r\n          }\r\n          if ('failed' in statisticsData && typeof statisticsData.failed === 'number') {\r\n            updatedCounts.failed = statisticsData.failed;\r\n          }\r\n          if ('cancelled' in statisticsData && typeof statisticsData.cancelled === 'number') {\r\n            updatedCounts.cancelled = statisticsData.cancelled;\r\n          }\r\n        }\r\n\r\n        setStatusCounts(updatedCounts);\r\n      }\r\n    } catch (error) {\r\n      console.error('Error fetching transaction statistics:', error);\r\n      toast.error('Không thể lấy thống kê giao dịch');\r\n\r\n      // Đặt giá trị mặc định khi có lỗi\r\n      setStatusCounts({\r\n        all: 0,\r\n        pending: 0,\r\n        processing: 0,\r\n        completed: 0,\r\n        failed: 0,\r\n        cancelled: 0\r\n      });\r\n    }\r\n  };\r\n\r\n  // Xử lý khi thay đổi khoảng thời gian\r\n  useEffect(() => {\r\n    if (dateRange?.from && userId) {\r\n      fetchTransactions();\r\n    }\r\n  }, [dateRange, fetchTransactions, userId]);\r\n\r\n  // Xử lý khi thay đổi tìm kiếm toàn cục\r\n  useEffect(() => {\r\n    if (!userId) return;\r\n\r\n    const handler = setTimeout(() => {\r\n      fetchTransactions();\r\n    }, 500);\r\n\r\n    return () => clearTimeout(handler);\r\n  }, [globalFilter, fetchTransactions, userId]);\r\n\r\n  // Xử lý khi thay đổi trạng thái filter\r\n  const handleStatusChange = (status: StatusFilter) => {\r\n     \r\n    setStatusFilter(status);\r\n    setPagination(prev => ({ ...prev, pageIndex: 0 }));\r\n    // fetchTransactions sẽ được gọi tự động do statusFilter nằm trong dependency array của nó\r\n  };\r\n\r\n  // Xử lý khi xem chi tiết\r\n  const handleViewDetail = (transaction: TransactionDto) => {\r\n    setSelectedTransaction(transaction);\r\n    setShowTransactionDetailSheet(true);\r\n  };\r\n\r\n  // Xử lý khi hủy giao dịch\r\n  const handleCancel = async (transaction: TransactionDto) => {\r\n    try {\r\n      setIsUpdating(true);\r\n      // Chỉ cho phép hủy giao dịch đang ở trạng thái PENDING\r\n      if (transaction.status !== TransactionStatus.PENDING) {\r\n        toast.error('Chỉ có thể hủy giao dịch đang chờ xử lý');\r\n        return;\r\n      }\r\n\r\n      // Gọi API để hủy giao dịch\r\n      await api.put(`transactions/${transaction.id}/status/${TransactionStatus.CANCELLED}`);\r\n      toast.success('Giao dịch đã được hủy thành công');\r\n\r\n      // Cập nhật trạng thái trực tiếp trong state\r\n      setTransactions(prevTransactions =>\r\n        prevTransactions.map(t =>\r\n          t.id === transaction.id\r\n            ? { ...t, status: TransactionStatus.CANCELLED }\r\n            : t\r\n        )\r\n      );\r\n\r\n      // Đóng sheet chi tiết nếu đang mở\r\n      if (showTransactionDetailSheet) {\r\n        setShowTransactionDetailSheet(false);\r\n      }\r\n\r\n      // Cập nhật thống kê\r\n      fetchStatistics();\r\n    } catch (error) {\r\n      console.error('Lỗi khi hủy giao dịch:', error);\r\n      toast.error('Không thể hủy giao dịch. Vui lòng thử lại sau.');\r\n    } finally {\r\n      setIsUpdating(false);\r\n    }\r\n  };\r\n\r\n  // Cấu hình columns cho table\r\n  const columns = getUserTransactionCell({\r\n    onViewDetail: handleViewDetail,\r\n    onCancel: handleCancel,\r\n  });\r\n\r\n  // Tạo table instance\r\n  const table = useReactTable({\r\n    data: transactions,\r\n    columns,\r\n    state: {\r\n      sorting,\r\n      columnVisibility,\r\n      columnFilters,\r\n      pagination,\r\n    },\r\n    enableRowSelection: false,\r\n    onSortingChange: setSorting,\r\n    onColumnFiltersChange: setColumnFilters,\r\n    onColumnVisibilityChange: setColumnVisibility,\r\n    onPaginationChange: setPagination,\r\n    getCoreRowModel: getCoreRowModel(),\r\n    getSortedRowModel: getSortedRowModel(),\r\n    getFilteredRowModel: getFilteredRowModel(),\r\n    manualPagination: true,\r\n    pageCount: Math.ceil(totalRows / pagination.pageSize),\r\n  });\r\n\r\n  // Xử lý refresh dữ liệu\r\n  const handleRefresh = useCallback(async () => {\r\n    setIsRefreshing(true)\r\n    try {\r\n      // Đặt lại các bộ lọc và sắp xếp\r\n      table.resetColumnFilters()\r\n      table.resetSorting()\r\n      setStatusFilter('all')\r\n      setDateRange(undefined)\r\n      setGlobalFilter('')\r\n\r\n      // Fetch dữ liệu mới và thống kê\r\n      await Promise.all([\r\n        fetchTransactions(),\r\n        fetchStatistics()\r\n      ])\r\n    } finally {\r\n      setTimeout(() => {\r\n        setIsRefreshing(false)\r\n      }, 1000)\r\n    }\r\n  }, [fetchTransactions, table])\r\n\r\n  return (\r\n    <div className=\"flex flex-col h-full\">\r\n      {/* Detail Sheet */}\r\n      <DetailSheet\r\n        transaction={selectedTransaction}\r\n        isOpen={showTransactionDetailSheet}\r\n        onClose={() => setShowTransactionDetailSheet(false)}\r\n        onCancel={handleCancel}\r\n      />\r\n\r\n      {/* Header Navigation */}\r\n      <div className=\"w-full flex justify-between items-center border-b py-1.5 px-6 h-10\">\r\n        <div className=\"flex items-center gap-2\">\r\n          <div className=\"flex items-center gap-1\">\r\n            <span className=\"text-sm font-medium\">Giao dịch của tôi</span>\r\n            <span className=\"text-xs bg-accent rounded-md px-1.5 py-1\">{totalRows}</span>\r\n          </div>\r\n        </div>\r\n        <div className=\"flex items-center gap-2\">\r\n          {isUpdating && (\r\n              <span className=\"text-xs text-muted-foreground\">Đang cập nhật...</span>\r\n          )}\r\n          <DateRangePicker\r\n            dateRange={dateRange}\r\n            onDateRangeChange={setDateRange}\r\n          />\r\n          <Button\r\n            className=\"relative\"\r\n            size=\"sm\"\r\n            variant=\"outline\"\r\n            onClick={handleRefresh}\r\n            disabled={isRefreshing}\r\n          >\r\n            <RefreshCcw className={`size-4 mr-1 ${isRefreshing ? 'animate-spin' : ''}`} />\r\n            Làm mới\r\n          </Button>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Thống kê giao dịch */}\r\n      <div className=\"p-2 border-b\">\r\n        <TransactionStatistics\r\n          onRefresh={isRefreshing}\r\n          statistics={statusCounts}\r\n        />\r\n      </div>\r\n\r\n      {/* Table Toolbar with Status Tabs */}\r\n      <TableToolbar\r\n        table={table}\r\n        globalFilter={globalFilter}\r\n        setGlobalFilter={setGlobalFilter}\r\n        onRefresh={handleRefresh}\r\n        isRefreshing={isRefreshing}\r\n        isShowSelectedRows={false}\r\n        beforeSearchSlot={\r\n          <StatusTabs\r\n            currentStatus={statusFilter}\r\n            onStatusChange={handleStatusChange}\r\n            counts={statusCounts}\r\n            className=\"w-fit\"\r\n          />\r\n        }\r\n      />\r\n\r\n      {/* Data Table */}\r\n      <div className=\"flex-1 overflow-auto\">\r\n        <DataTable\r\n          table={table}\r\n          className=\"w-full\"\r\n          isLoading={loading}\r\n        />\r\n      </div>\r\n\r\n      {/* Table Footer */}\r\n      <TableFooter\r\n        table={table}\r\n      />\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAEA;AACA;AACA;AAAA;AACA;AACA;AAEA;AACA;AACA;AACA;AAAA;AAEA;AACA;AACA;AACA;AACA;AApBA;;;;;;;;;;;;;;;;;;AAsBe,SAAS;IACtB,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,qHAAA,CAAA,UAAO,AAAD;IACvB,MAAM,SAAS,MAAM;IAErB,+BAA+B;IAC/B,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,8SAAA,CAAA,WAAQ,AAAD,EAAoB,EAAE;IACrE,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,8SAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,8SAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,8SAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,8SAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,8SAAA,CAAA,WAAQ,AAAD,EAAE;QAC/C,KAAK;QACL,SAAS;QACT,YAAY;QACZ,WAAW;QACX,QAAQ;QACR,WAAW;IACb;IAEA,2BAA2B;IAC3B,MAAM,CAAC,4BAA4B,8BAA8B,GAAG,CAAA,GAAA,8SAAA,CAAA,WAAQ,AAAD,EAAE;IAC7E,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,8SAAA,CAAA,WAAQ,AAAD,EAA8B;IAE3F,kBAAkB;IAClB,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,8SAAA,CAAA,WAAQ,AAAD,EAAsB,EAAE;IACzE,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,8SAAA,CAAA,WAAQ,AAAD,EAAmB,CAAC;IAC3E,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,8SAAA,CAAA,WAAQ,AAAD,EAAgB,EAAE;IACvD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,8SAAA,CAAA,WAAQ,AAAD,EAAmB;QAC5D,WAAW;QACX,UAAU;IACZ;IACA,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,8SAAA,CAAA,WAAQ,AAAD,EAAgB;IAC/D,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,8SAAA,CAAA,WAAQ,AAAD,EAAyB;IAClE,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,8SAAA,CAAA,WAAQ,AAAD,EAAU;IAEzD,gBAAgB;IAChB,MAAM,oBAAoB,CAAA,GAAA,8SAAA,CAAA,cAAW,AAAD,EAAE;QACpC,IAAI,CAAC,QAAQ;QAEb,WAAW;QACX,gBAAgB;QAChB,IAAI;YACF,mBAAmB;YACnB,MAAM,SAAS,IAAI;YACnB,OAAO,MAAM,CAAC,QAAQ,OAAO,WAAW,SAAS,GAAG;YACpD,OAAO,MAAM,CAAC,SAAS,OAAO,WAAW,QAAQ;YAEjD,yBAAyB;YACzB,IAAI,eAAe,CAAC,OAAO,EAAE,QAAQ;YAErC,8BAA8B;YAC9B,IAAI,iBAAiB,OAAO;gBAC1B,MAAM,cAAc,aAAa,WAAW;gBAE5C,gFAAgF;gBAChF,IAAI,gBAAgB,cAAc;gBAEhC,kDAAkD;gBACpD,OAAO;oBAEL,gBAAgB,CAAC,QAAQ,EAAE,aAAa;gBAC1C;YACF;YAEA,gCAAgC;YAChC,OAAO,MAAM,CAAC,UAAU;YAExB,+CAA+C;YAC/C,OAAO,MAAM,CAAC,aAAa;YAE3B,oCAAoC;YACpC,IAAI,WAAW,MAAM;gBACnB,MAAM,WAAW,IAAI,KAAK,UAAU,IAAI;gBACxC,SAAS,QAAQ,CAAC,GAAG,GAAG,GAAG;gBAC3B,OAAO,MAAM,CAAC,YAAY,SAAS,WAAW;gBAE9C,IAAI,UAAU,EAAE,EAAE;oBAChB,MAAM,SAAS,IAAI,KAAK,UAAU,EAAE;oBACpC,OAAO,QAAQ,CAAC,IAAI,IAAI,IAAI;oBAC5B,OAAO,MAAM,CAAC,UAAU,OAAO,WAAW;gBAC5C,OAAO;oBACL,sCAAsC;oBACtC,MAAM,SAAS,IAAI,KAAK,UAAU,IAAI;oBACtC,OAAO,QAAQ,CAAC,IAAI,IAAI,IAAI;oBAC5B,OAAO,MAAM,CAAC,UAAU,OAAO,WAAW;gBAC5C;YACF;YAEA,yBAAyB;YACzB,IAAI,cAAc;gBAChB,OAAO,MAAM,CAAC,UAAU;YAC1B;YAEA,sBAAsB;YACtB,IAAI,QAAQ,MAAM,GAAG,GAAG;gBACtB,OAAO,MAAM,CAAC,UAAU,OAAO,CAAC,EAAE,CAAC,EAAE;gBACrC,OAAO,MAAM,CAAC,aAAa,OAAO,CAAC,EAAE,CAAC,IAAI,GAAG,SAAS;YACxD;YAEA,UAAU;YACV,MAAM,WAAW,MAAM,0GAAA,CAAA,MAAG,CAAC,GAAG,CAAqC,CAAC,aAAa,EAAE,OAAO,QAAQ,IAAI;YAEtG,8DAA8D;YAC9D,IAAI,YAAY,OAAO,aAAa,UAAU;gBAC5C,gEAAgE;gBAChE,IAAI,UAAU,YAAY,MAAM,OAAO,CAAC,SAAS,IAAI,KAAK,UAAU,UAAU;oBAC5E,gBAAgB,SAAS,IAAI;oBAC7B,aAAa,SAAS,IAAI,EAAE,aAAa;gBAC3C,OAEK,IAAI,MAAM,OAAO,CAAC,WAAW;oBAChC,gBAAgB;oBAChB,aAAa,SAAS,MAAM;gBAC9B,OAEK;oBACH,QAAQ,KAAK,CAAC,sCAAsC;oBACpD,wQAAA,CAAA,QAAK,CAAC,KAAK,CAAC;oBACZ,gBAAgB,EAAE;oBAClB,aAAa;gBACf;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,kCAAkC;YAChD,wQAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YAEZ,oCAAoC;YACpC,gBAAgB,EAAE;YAClB,aAAa;QACb,gFAAgF;QAClF,SAAU;YACR,WAAW;YACX,gBAAgB;QAClB;IACF,GAAG;QAAC;QAAY;QAAc;QAAS;QAAW;QAAc;KAAO;IAEvE,uEAAuE;IACvE,CAAA,GAAA,8SAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,QAAQ;YAEV;QACF;IACF,GAAG;QAAC;QAAmB;KAAO;IAE9B,mEAAmE;IACnE,qFAAqF;IACrF,CAAA,GAAA,8SAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,QAAQ;YAEV;QACF;IACF,GAAG;QAAC;KAAO;IAEX,mEAAmE;IACnE,MAAM,kBAAkB;QACtB,IAAI,CAAC,QAAQ;QAEb,IAAI;YAEF,2EAA2E;YAC3E,mFAAmF;YACnF,MAAM,WAAW,MAAM,0GAAA,CAAA,MAAG,CAAC,GAAG,CAAC;YAE/B,IAAI,UAAU;gBACZ,8DAA8D;gBAC9D,MAAM,iBAAiB,YAAY,OAAO,aAAa,YAAY,UAAU,WACzE,SAAS,IAAI,GACb;gBAEJ,oCAAoC;gBACpC,MAAM,gBAAgB;oBACpB,KAAK;oBACL,SAAS;oBACT,YAAY;oBACZ,WAAW;oBACX,QAAQ;oBACR,WAAW;gBACb;gBAEA,6CAA6C;gBAC7C,IAAI,kBAAkB,OAAO,mBAAmB,UAAU;oBACxD,IAAI,WAAW,kBAAkB,OAAO,eAAe,KAAK,KAAK,UAAU;wBACzE,cAAc,GAAG,GAAG,eAAe,KAAK;oBAC1C;oBACA,IAAI,aAAa,kBAAkB,OAAO,eAAe,OAAO,KAAK,UAAU;wBAC7E,cAAc,OAAO,GAAG,eAAe,OAAO;oBAChD;oBACA,IAAI,gBAAgB,kBAAkB,OAAO,eAAe,UAAU,KAAK,UAAU;wBACnF,cAAc,UAAU,GAAG,eAAe,UAAU;oBACtD;oBACA,IAAI,eAAe,kBAAkB,OAAO,eAAe,SAAS,KAAK,UAAU;wBACjF,cAAc,SAAS,GAAG,eAAe,SAAS;oBACpD;oBACA,IAAI,YAAY,kBAAkB,OAAO,eAAe,MAAM,KAAK,UAAU;wBAC3E,cAAc,MAAM,GAAG,eAAe,MAAM;oBAC9C;oBACA,IAAI,eAAe,kBAAkB,OAAO,eAAe,SAAS,KAAK,UAAU;wBACjF,cAAc,SAAS,GAAG,eAAe,SAAS;oBACpD;gBACF;gBAEA,gBAAgB;YAClB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0CAA0C;YACxD,wQAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YAEZ,kCAAkC;YAClC,gBAAgB;gBACd,KAAK;gBACL,SAAS;gBACT,YAAY;gBACZ,WAAW;gBACX,QAAQ;gBACR,WAAW;YACb;QACF;IACF;IAEA,sCAAsC;IACtC,CAAA,GAAA,8SAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,WAAW,QAAQ,QAAQ;YAC7B;QACF;IACF,GAAG;QAAC;QAAW;QAAmB;KAAO;IAEzC,uCAAuC;IACvC,CAAA,GAAA,8SAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,QAAQ;QAEb,MAAM,UAAU,WAAW;YACzB;QACF,GAAG;QAEH,OAAO,IAAM,aAAa;IAC5B,GAAG;QAAC;QAAc;QAAmB;KAAO;IAE5C,uCAAuC;IACvC,MAAM,qBAAqB,CAAC;QAE1B,gBAAgB;QAChB,cAAc,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE,WAAW;YAAE,CAAC;IAChD,0FAA0F;IAC5F;IAEA,yBAAyB;IACzB,MAAM,mBAAmB,CAAC;QACxB,uBAAuB;QACvB,8BAA8B;IAChC;IAEA,0BAA0B;IAC1B,MAAM,eAAe,OAAO;QAC1B,IAAI;YACF,cAAc;YACd,uDAAuD;YACvD,IAAI,YAAY,MAAM,KAAK,2KAAA,CAAA,oBAAiB,CAAC,OAAO,EAAE;gBACpD,wQAAA,CAAA,QAAK,CAAC,KAAK,CAAC;gBACZ;YACF;YAEA,2BAA2B;YAC3B,MAAM,0GAAA,CAAA,MAAG,CAAC,GAAG,CAAC,CAAC,aAAa,EAAE,YAAY,EAAE,CAAC,QAAQ,EAAE,2KAAA,CAAA,oBAAiB,CAAC,SAAS,EAAE;YACpF,wQAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YAEd,4CAA4C;YAC5C,gBAAgB,CAAA,mBACd,iBAAiB,GAAG,CAAC,CAAA,IACnB,EAAE,EAAE,KAAK,YAAY,EAAE,GACnB;wBAAE,GAAG,CAAC;wBAAE,QAAQ,2KAAA,CAAA,oBAAiB,CAAC,SAAS;oBAAC,IAC5C;YAIR,kCAAkC;YAClC,IAAI,4BAA4B;gBAC9B,8BAA8B;YAChC;YAEA,oBAAoB;YACpB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;YACxC,wQAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd,SAAU;YACR,cAAc;QAChB;IACF;IAEA,6BAA6B;IAC7B,MAAM,UAAU,CAAA,GAAA,8JAAA,CAAA,yBAAsB,AAAD,EAAE;QACrC,cAAc;QACd,UAAU;IACZ;IAEA,qBAAqB;IACrB,MAAM,QAAQ,CAAA,GAAA,gSAAA,CAAA,gBAAa,AAAD,EAAE;QAC1B,MAAM;QACN;QACA,OAAO;YACL;YACA;YACA;YACA;QACF;QACA,oBAAoB;QACpB,iBAAiB;QACjB,uBAAuB;QACvB,0BAA0B;QAC1B,oBAAoB;QACpB,iBAAiB,CAAA,GAAA,8OAAA,CAAA,kBAAe,AAAD;QAC/B,mBAAmB,CAAA,GAAA,8OAAA,CAAA,oBAAiB,AAAD;QACnC,qBAAqB,CAAA,GAAA,8OAAA,CAAA,sBAAmB,AAAD;QACvC,kBAAkB;QAClB,WAAW,KAAK,IAAI,CAAC,YAAY,WAAW,QAAQ;IACtD;IAEA,wBAAwB;IACxB,MAAM,gBAAgB,CAAA,GAAA,8SAAA,CAAA,cAAW,AAAD,EAAE;QAChC,gBAAgB;QAChB,IAAI;YACF,gCAAgC;YAChC,MAAM,kBAAkB;YACxB,MAAM,YAAY;YAClB,gBAAgB;YAChB,aAAa;YACb,gBAAgB;YAEhB,gCAAgC;YAChC,MAAM,QAAQ,GAAG,CAAC;gBAChB;gBACA;aACD;QACH,SAAU;YACR,WAAW;gBACT,gBAAgB;YAClB,GAAG;QACL;IACF,GAAG;QAAC;QAAmB;KAAM;IAE7B,qBACE,uVAAC;QAAI,WAAU;;0BAEb,uVAAC,gKAAA,CAAA,cAAW;gBACV,aAAa;gBACb,QAAQ;gBACR,SAAS,IAAM,8BAA8B;gBAC7C,UAAU;;;;;;0BAIZ,uVAAC;gBAAI,WAAU;;kCACb,uVAAC;wBAAI,WAAU;kCACb,cAAA,uVAAC;4BAAI,WAAU;;8CACb,uVAAC;oCAAK,WAAU;8CAAsB;;;;;;8CACtC,uVAAC;oCAAK,WAAU;8CAA4C;;;;;;;;;;;;;;;;;kCAGhE,uVAAC;wBAAI,WAAU;;4BACZ,4BACG,uVAAC;gCAAK,WAAU;0CAAgC;;;;;;0CAEpD,uVAAC,uLAAA,CAAA,kBAAe;gCACd,WAAW;gCACX,mBAAmB;;;;;;0CAErB,uVAAC,2HAAA,CAAA,SAAM;gCACL,WAAU;gCACV,MAAK;gCACL,SAAQ;gCACR,SAAS;gCACT,UAAU;;kDAEV,uVAAC,sSAAA,CAAA,aAAU;wCAAC,WAAW,CAAC,YAAY,EAAE,eAAe,iBAAiB,IAAI;;;;;;oCAAI;;;;;;;;;;;;;;;;;;;0BAOpF,uVAAC;gBAAI,WAAU;0BACb,cAAA,uVAAC,wLAAA,CAAA,wBAAqB;oBACpB,WAAW;oBACX,YAAY;;;;;;;;;;;0BAKhB,uVAAC,0JAAA,CAAA,eAAY;gBACX,OAAO;gBACP,cAAc;gBACd,iBAAiB;gBACjB,WAAW;gBACX,cAAc;gBACd,oBAAoB;gBACpB,gCACE,uVAAC,8KAAA,CAAA,aAAU;oBACT,eAAe;oBACf,gBAAgB;oBAChB,QAAQ;oBACR,WAAU;;;;;;;;;;;0BAMhB,uVAAC;gBAAI,WAAU;0BACb,cAAA,uVAAC,uJAAA,CAAA,YAAS;oBACR,OAAO;oBACP,WAAU;oBACV,WAAW;;;;;;;;;;;0BAKf,uVAAC,yJAAA,CAAA,cAAW;gBACV,OAAO;;;;;;;;;;;;AAIf", "debugId": null}}]}
"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreateCmsCustomerFeedbacksService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const event_emitter_1 = require("@nestjs/event-emitter");
const typeorm_transactional_1 = require("typeorm-transactional");
const base_cms_customer_feedbacks_service_1 = require("./base.cms-customer-feedbacks.service");
const cms_customer_feedbacks_entity_1 = require("../entity/cms-customer-feedbacks.entity");
const create_cms_customer_feedback_dto_1 = require("../dto/create.cms-customer-feedback.dto");
let CreateCmsCustomerFeedbacksService = class CreateCmsCustomerFeedbacksService extends base_cms_customer_feedbacks_service_1.BaseCmsCustomerFeedbacksService {
    feedbackRepository;
    dataSource;
    eventEmitter;
    constructor(feedbackRepository, dataSource, eventEmitter) {
        super(feedbackRepository, dataSource, eventEmitter);
        this.feedbackRepository = feedbackRepository;
        this.dataSource = dataSource;
        this.eventEmitter = eventEmitter;
    }
    async create(createDto, userId) {
        try {
            this.logger.debug(`Đang tạo feedback khách hàng CMS: ${JSON.stringify(createDto)}`);
            if (createDto.rating !== undefined && (createDto.rating < 1 || createDto.rating > 5)) {
                throw new common_1.BadRequestException('Đánh giá phải từ 1 đến 5 sao');
            }
            const feedback = this.feedbackRepository.create();
            feedback.customerName = createDto.customerName;
            feedback.customerTitleCompany = createDto.customerTitleCompany || null;
            feedback.feedbackText = createDto.feedbackText;
            feedback.rating = createDto.rating || null;
            feedback.avatarUrl = createDto.avatarUrl || null;
            feedback.productServiceName = createDto.productServiceName || null;
            feedback.status = createDto.status || cms_customer_feedbacks_entity_1.CmsCustomerFeedbackStatus.PENDING;
            if (userId) {
                feedback.createdBy = userId;
                feedback.updatedBy = userId;
            }
            const savedFeedback = await this.feedbackRepository.save(feedback);
            const feedbackDto = this.toDto(savedFeedback);
            if (!feedbackDto) {
                throw new common_1.InternalServerErrorException('Không thể chuyển đổi entity thành DTO');
            }
            this.eventEmitter.emit(this.EVENT_FEEDBACK_CREATED, {
                feedbackId: feedbackDto.id,
                userId: userId || null,
                newData: feedbackDto,
            });
            return feedbackDto;
        }
        catch (error) {
            this.logger.error(`Lỗi khi tạo feedback khách hàng CMS: ${error.message}`, error.stack);
            if (error instanceof common_1.BadRequestException) {
                throw error;
            }
            throw new common_1.InternalServerErrorException(`Không thể tạo feedback khách hàng CMS: ${error.message}`);
        }
    }
    async bulkCreate(createDtos, userId) {
        try {
            this.logger.debug(`Đang tạo ${createDtos.length} feedback khách hàng CMS`);
            const feedbacks = [];
            for (const createDto of createDtos) {
                const feedback = await this.create(createDto, userId);
                feedbacks.push(feedback);
            }
            return feedbacks;
        }
        catch (error) {
            this.logger.error(`Lỗi khi tạo nhiều feedback khách hàng CMS: ${error.message}`, error.stack);
            if (error instanceof common_1.BadRequestException) {
                throw error;
            }
            throw new common_1.InternalServerErrorException(`Không thể tạo nhiều feedback khách hàng CMS: ${error.message}`);
        }
    }
    async createFromPublicForm(createDto) {
        try {
            this.logger.debug(`Đang tạo feedback từ form công khai: ${createDto.customerName}`);
            const publicCreateDto = {
                ...createDto,
                status: cms_customer_feedbacks_entity_1.CmsCustomerFeedbackStatus.PENDING,
            };
            return this.create(publicCreateDto);
        }
        catch (error) {
            this.logger.error(`Lỗi khi tạo feedback từ form công khai: ${error.message}`, error.stack);
            throw new common_1.InternalServerErrorException(`Không thể gửi feedback: ${error.message}`);
        }
    }
    async createWithRating(customerName, feedbackText, rating, productServiceName, userId) {
        try {
            this.logger.debug(`Đang tạo feedback với rating ${rating} sao cho khách hàng: ${customerName}`);
            const createDto = {
                customerName,
                feedbackText,
                rating,
                productServiceName,
                status: cms_customer_feedbacks_entity_1.CmsCustomerFeedbackStatus.PENDING,
            };
            return this.create(createDto, userId);
        }
        catch (error) {
            this.logger.error(`Lỗi khi tạo feedback với rating: ${error.message}`, error.stack);
            throw new common_1.InternalServerErrorException(`Không thể tạo feedback với rating: ${error.message}`);
        }
    }
    async createTestimonial(createDto, userId) {
        try {
            this.logger.debug(`Đang tạo testimonial cho khách hàng: ${createDto.customerName}`);
            const testimonialDto = {
                ...createDto,
                status: cms_customer_feedbacks_entity_1.CmsCustomerFeedbackStatus.APPROVED,
            };
            const feedback = await this.create(testimonialDto, userId);
            await this.feedbackRepository.update(feedback.id, {
                approvedBy: userId,
            });
            const updatedFeedback = await this.findById(feedback.id, ['approver']);
            return this.toDto(updatedFeedback);
        }
        catch (error) {
            this.logger.error(`Lỗi khi tạo testimonial: ${error.message}`, error.stack);
            throw new common_1.InternalServerErrorException(`Không thể tạo testimonial: ${error.message}`);
        }
    }
    async importFeedbacks(feedbackData, userId) {
        try {
            this.logger.debug(`Đang import ${feedbackData.length} feedback`);
            const importedFeedbacks = [];
            for (const data of feedbackData) {
                try {
                    const createDto = {
                        customerName: data.customerName || data.name,
                        customerTitleCompany: data.customerTitleCompany || data.title,
                        feedbackText: data.feedbackText || data.content || data.review,
                        rating: data.rating ? parseInt(data.rating) : undefined,
                        avatarUrl: data.avatarUrl || data.avatar,
                        productServiceName: data.productServiceName || data.product,
                        status: data.status || cms_customer_feedbacks_entity_1.CmsCustomerFeedbackStatus.PENDING,
                    };
                    const feedback = await this.create(createDto, userId);
                    importedFeedbacks.push(feedback);
                }
                catch (error) {
                    this.logger.warn(`Không thể import feedback: ${error.message}`, data);
                }
            }
            return importedFeedbacks;
        }
        catch (error) {
            this.logger.error(`Lỗi khi import feedback: ${error.message}`, error.stack);
            throw new common_1.InternalServerErrorException(`Không thể import feedback: ${error.message}`);
        }
    }
};
exports.CreateCmsCustomerFeedbacksService = CreateCmsCustomerFeedbacksService;
__decorate([
    (0, typeorm_transactional_1.Transactional)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [create_cms_customer_feedback_dto_1.CreateCmsCustomerFeedbackDto, String]),
    __metadata("design:returntype", Promise)
], CreateCmsCustomerFeedbacksService.prototype, "create", null);
__decorate([
    (0, typeorm_transactional_1.Transactional)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Array, String]),
    __metadata("design:returntype", Promise)
], CreateCmsCustomerFeedbacksService.prototype, "bulkCreate", null);
__decorate([
    (0, typeorm_transactional_1.Transactional)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [create_cms_customer_feedback_dto_1.CreateCmsCustomerFeedbackDto]),
    __metadata("design:returntype", Promise)
], CreateCmsCustomerFeedbacksService.prototype, "createFromPublicForm", null);
__decorate([
    (0, typeorm_transactional_1.Transactional)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String, Number, String, String]),
    __metadata("design:returntype", Promise)
], CreateCmsCustomerFeedbacksService.prototype, "createWithRating", null);
__decorate([
    (0, typeorm_transactional_1.Transactional)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [create_cms_customer_feedback_dto_1.CreateCmsCustomerFeedbackDto, String]),
    __metadata("design:returntype", Promise)
], CreateCmsCustomerFeedbacksService.prototype, "createTestimonial", null);
__decorate([
    (0, typeorm_transactional_1.Transactional)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Array, String]),
    __metadata("design:returntype", Promise)
], CreateCmsCustomerFeedbacksService.prototype, "importFeedbacks", null);
exports.CreateCmsCustomerFeedbacksService = CreateCmsCustomerFeedbacksService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(cms_customer_feedbacks_entity_1.CmsCustomerFeedbacks)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        typeorm_2.DataSource,
        event_emitter_1.EventEmitter2])
], CreateCmsCustomerFeedbacksService);
//# sourceMappingURL=create.cms-customer-feedbacks.service.js.map
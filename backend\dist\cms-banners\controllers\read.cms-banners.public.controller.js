"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ReadCmsBannersPublicController = void 0;
const openapi = require("@nestjs/swagger");
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const class_transformer_1 = require("class-transformer");
const read_cms_banners_service_1 = require("../services/read.cms-banners.service");
const cms_banner_public_dto_1 = require("../dto/cms-banner.public.dto");
const cms_banners_entity_1 = require("../entity/cms-banners.entity");
const custom_pagination_query_dto_1 = require("../../common/dto/custom-pagination-query.dto");
const pagination_response_dto_1 = require("../../common/dto/pagination-response.dto");
const custom_pagination_meta_dto_1 = require("../../common/dto/custom-pagination-meta.dto");
let ReadCmsBannersPublicController = class ReadCmsBannersPublicController {
    cmsBannersService;
    constructor(cmsBannersService) {
        this.cmsBannersService = cmsBannersService;
    }
    async getActiveBanners(paginationQuery, location) {
        const filters = ['status:ACTIVE'];
        if (location) {
            filters.push(`location:${location}`);
        }
        const queryWithFilter = Object.assign(new custom_pagination_query_dto_1.CustomPaginationQueryDto(), {
            ...paginationQuery,
            filter: filters.join(','),
            sort: paginationQuery.sort || 'displayOrder:ASC',
        });
        const { data, total } = await this.cmsBannersService.findAll(queryWithFilter);
        const publicData = (0, class_transformer_1.plainToInstance)(cms_banner_public_dto_1.CmsBannerPublicDto, data, {
            excludeExtraneousValues: true,
        });
        const filteredData = publicData.filter(banner => banner.canDisplayPublicly);
        const meta = new custom_pagination_meta_dto_1.CustomPageMetaDto({
            pageQueryDto: paginationQuery,
            itemCount: filteredData.length,
        });
        return new pagination_response_dto_1.PaginationResponseDto(filteredData, meta);
    }
    async getBannersByLocation(location, paginationQuery) {
        const queryWithDefaults = Object.assign(new custom_pagination_query_dto_1.CustomPaginationQueryDto(), {
            ...paginationQuery,
            sort: paginationQuery.sort || 'displayOrder:ASC',
        });
        const { data, total } = await this.cmsBannersService.findByLocation(location, queryWithDefaults);
        const publicData = (0, class_transformer_1.plainToInstance)(cms_banner_public_dto_1.CmsBannerPublicDto, data, {
            excludeExtraneousValues: true,
        });
        const filteredData = publicData.filter(banner => banner.canDisplayPublicly);
        const meta = new custom_pagination_meta_dto_1.CustomPageMetaDto({
            pageQueryDto: paginationQuery,
            itemCount: filteredData.length,
        });
        return new pagination_response_dto_1.PaginationResponseDto(filteredData, meta);
    }
    async getCarouselBanners(limit) {
        const banners = await this.cmsBannersService.getActiveBanners(cms_banners_entity_1.CmsBannerLocation.HOMEPAGE_SLIDER, limit || 5);
        const publicData = (0, class_transformer_1.plainToInstance)(cms_banner_public_dto_1.CmsBannerPublicDto, banners, {
            excludeExtraneousValues: true,
        });
        return publicData.filter(banner => banner.canDisplayInCarousel);
    }
    async searchBanners(searchTerm, paginationQuery) {
        const queryWithDefaults = Object.assign(new custom_pagination_query_dto_1.CustomPaginationQueryDto(), {
            ...paginationQuery,
            sort: paginationQuery.sort || 'displayOrder:ASC',
        });
        const { data, total } = await this.cmsBannersService.search(searchTerm, queryWithDefaults);
        const publicData = (0, class_transformer_1.plainToInstance)(cms_banner_public_dto_1.CmsBannerPublicDto, data, {
            excludeExtraneousValues: true,
        });
        const filteredData = publicData.filter(banner => banner.canDisplayPublicly);
        const meta = new custom_pagination_meta_dto_1.CustomPageMetaDto({
            pageQueryDto: paginationQuery,
            itemCount: filteredData.length,
        });
        return new pagination_response_dto_1.PaginationResponseDto(filteredData, meta);
    }
    async listBanners(paginationQuery) {
        const filters = paginationQuery.filter ?
            `status:ACTIVE,${paginationQuery.filter}` :
            'status:ACTIVE';
        const queryWithFilter = Object.assign(new custom_pagination_query_dto_1.CustomPaginationQueryDto(), {
            ...paginationQuery,
            filter: filters,
            sort: paginationQuery.sort || 'displayOrder:ASC',
        });
        const { data, total } = await this.cmsBannersService.findAll(queryWithFilter);
        const publicData = (0, class_transformer_1.plainToInstance)(cms_banner_public_dto_1.CmsBannerPublicDto, data, {
            excludeExtraneousValues: true,
        });
        const filteredData = publicData.filter(banner => banner.canDisplayPublicly);
        const meta = new custom_pagination_meta_dto_1.CustomPageMetaDto({
            pageQueryDto: paginationQuery,
            itemCount: filteredData.length,
        });
        return new pagination_response_dto_1.PaginationResponseDto(filteredData, meta);
    }
    async getStatistics() {
        const stats = await this.cmsBannersService.getStatistics();
        return {
            totalActive: stats.active,
            byLocation: stats.byLocation,
            withMobileImage: stats.byLocation[cms_banners_entity_1.CmsBannerLocation.HOMEPAGE_SLIDER] || 0,
            withLink: stats.active,
            canDisplayInCarousel: stats.byLocation[cms_banners_entity_1.CmsBannerLocation.HOMEPAGE_SLIDER] || 0,
            clickableBanners: stats.active,
        };
    }
    async getBannerById(id) {
        const banner = await this.cmsBannersService.findOneOrFail(id);
        if (!banner) {
            return null;
        }
        const publicBanner = (0, class_transformer_1.plainToInstance)(cms_banner_public_dto_1.CmsBannerPublicDto, banner, {
            excludeExtraneousValues: true,
        });
        return publicBanner.canDisplayPublicly ? publicBanner : null;
    }
};
exports.ReadCmsBannersPublicController = ReadCmsBannersPublicController;
__decorate([
    (0, common_1.Get)('active'),
    (0, swagger_1.ApiOperation)({ summary: 'Lấy danh sách banner đang hoạt động (public)' }),
    (0, swagger_1.ApiQuery)({
        name: 'page',
        required: false,
        type: Number,
        description: 'Số trang',
        example: 1,
    }),
    (0, swagger_1.ApiQuery)({
        name: 'limit',
        required: false,
        type: Number,
        description: 'Số lượng banner mỗi trang',
        example: 10,
    }),
    (0, swagger_1.ApiQuery)({
        name: 'location',
        required: false,
        enum: cms_banners_entity_1.CmsBannerLocation,
        description: 'Vị trí hiển thị (tùy chọn)',
    }),
    (0, swagger_1.ApiQuery)({
        name: 'sort',
        required: false,
        type: String,
        description: 'Sắp xếp (ví dụ: displayOrder:ASC)',
        example: 'displayOrder:ASC',
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Danh sách banner đang hoạt động',
        type: (pagination_response_dto_1.PaginationResponseDto),
    }),
    openapi.ApiResponse({ status: 200 }),
    __param(0, (0, common_1.Query)()),
    __param(1, (0, common_1.Query)('location')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [custom_pagination_query_dto_1.CustomPaginationQueryDto, String]),
    __metadata("design:returntype", Promise)
], ReadCmsBannersPublicController.prototype, "getActiveBanners", null);
__decorate([
    (0, common_1.Get)('by-location/:location'),
    (0, swagger_1.ApiOperation)({ summary: 'Lấy banner theo vị trí hiển thị (public)' }),
    (0, swagger_1.ApiParam)({
        name: 'location',
        enum: cms_banners_entity_1.CmsBannerLocation,
        description: 'Vị trí hiển thị banner',
    }),
    (0, swagger_1.ApiQuery)({
        name: 'page',
        required: false,
        type: Number,
        description: 'Số trang',
        example: 1,
    }),
    (0, swagger_1.ApiQuery)({
        name: 'limit',
        required: false,
        type: Number,
        description: 'Số lượng banner mỗi trang',
        example: 10,
    }),
    (0, swagger_1.ApiQuery)({
        name: 'sort',
        required: false,
        type: String,
        description: 'Sắp xếp',
        example: 'displayOrder:ASC',
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Danh sách banner theo vị trí',
        type: (pagination_response_dto_1.PaginationResponseDto),
    }),
    openapi.ApiResponse({ status: 200 }),
    __param(0, (0, common_1.Param)('location')),
    __param(1, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, custom_pagination_query_dto_1.CustomPaginationQueryDto]),
    __metadata("design:returntype", Promise)
], ReadCmsBannersPublicController.prototype, "getBannersByLocation", null);
__decorate([
    (0, common_1.Get)('carousel'),
    (0, swagger_1.ApiOperation)({ summary: 'Lấy banner cho hero carousel (public)' }),
    (0, swagger_1.ApiQuery)({
        name: 'limit',
        required: false,
        type: Number,
        description: 'Số lượng banner tối đa',
        example: 5,
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Danh sách banner cho carousel',
        schema: {
            type: 'array',
            items: { $ref: '#/components/schemas/CmsBannerPublicDto' },
        },
    }),
    openapi.ApiResponse({ status: 200, type: [require("../dto/cms-banner.public.dto").CmsBannerPublicDto] }),
    __param(0, (0, common_1.Query)('limit')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", Promise)
], ReadCmsBannersPublicController.prototype, "getCarouselBanners", null);
__decorate([
    (0, common_1.Get)('search'),
    (0, swagger_1.ApiOperation)({ summary: 'Tìm kiếm banner (public)' }),
    (0, swagger_1.ApiQuery)({
        name: 'q',
        required: true,
        type: String,
        description: 'Từ khóa tìm kiếm',
        example: 'khuyến mãi',
    }),
    (0, swagger_1.ApiQuery)({
        name: 'page',
        required: false,
        type: Number,
        description: 'Số trang',
        example: 1,
    }),
    (0, swagger_1.ApiQuery)({
        name: 'limit',
        required: false,
        type: Number,
        description: 'Số lượng banner mỗi trang',
        example: 10,
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Kết quả tìm kiếm banner',
        type: (pagination_response_dto_1.PaginationResponseDto),
    }),
    openapi.ApiResponse({ status: 200 }),
    __param(0, (0, common_1.Query)('q')),
    __param(1, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, custom_pagination_query_dto_1.CustomPaginationQueryDto]),
    __metadata("design:returntype", Promise)
], ReadCmsBannersPublicController.prototype, "searchBanners", null);
__decorate([
    (0, common_1.Get)('list'),
    (0, swagger_1.ApiOperation)({ summary: 'Lấy danh sách banner với filtering nâng cao (public)' }),
    (0, swagger_1.ApiQuery)({
        name: 'page',
        required: false,
        type: Number,
        description: 'Số trang',
        example: 1,
    }),
    (0, swagger_1.ApiQuery)({
        name: 'limit',
        required: false,
        type: Number,
        description: 'Số lượng banner mỗi trang',
        example: 10,
    }),
    (0, swagger_1.ApiQuery)({
        name: 'sort',
        required: false,
        type: String,
        description: 'Sắp xếp (ví dụ: displayOrder:ASC,title:DESC)',
        example: 'displayOrder:ASC',
    }),
    (0, swagger_1.ApiQuery)({
        name: 'filter',
        required: false,
        type: String,
        description: 'Bộ lọc (ví dụ: location:homepage_slider)',
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Danh sách banner',
        type: (pagination_response_dto_1.PaginationResponseDto),
    }),
    openapi.ApiResponse({ status: 200 }),
    __param(0, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [custom_pagination_query_dto_1.CustomPaginationQueryDto]),
    __metadata("design:returntype", Promise)
], ReadCmsBannersPublicController.prototype, "listBanners", null);
__decorate([
    (0, common_1.Get)('statistics'),
    (0, swagger_1.ApiOperation)({ summary: 'Lấy thống kê banner (public)' }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Thống kê banner public',
        schema: {
            type: 'object',
            properties: {
                totalActive: { type: 'number', example: 10 },
                byLocation: {
                    type: 'object',
                    additionalProperties: { type: 'number' },
                    example: {
                        homepage_slider: 5,
                        sidebar_top: 2,
                        content_top: 3
                    }
                },
                withMobileImage: { type: 'number', example: 7 },
                withLink: { type: 'number', example: 8 },
                canDisplayInCarousel: { type: 'number', example: 5 },
                clickableBanners: { type: 'number', example: 8 },
            }
        }
    }),
    openapi.ApiResponse({ status: 200 }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], ReadCmsBannersPublicController.prototype, "getStatistics", null);
__decorate([
    (0, common_1.Get)(':id'),
    (0, swagger_1.ApiOperation)({ summary: 'Lấy banner theo ID (public)' }),
    (0, swagger_1.ApiParam)({
        name: 'id',
        type: String,
        description: 'ID của banner',
        example: '123e4567-e89b-12d3-a456-426614174000',
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Thông tin banner',
        type: cms_banner_public_dto_1.CmsBannerPublicDto,
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.NOT_FOUND,
        description: 'Không tìm thấy banner',
        schema: {
            type: 'object',
            properties: {
                message: { type: 'string', example: 'Banner not found' },
                statusCode: { type: 'number', example: 404 },
            }
        }
    }),
    openapi.ApiResponse({ status: 200, type: Object }),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], ReadCmsBannersPublicController.prototype, "getBannerById", null);
exports.ReadCmsBannersPublicController = ReadCmsBannersPublicController = __decorate([
    (0, swagger_1.ApiTags)('cms-banners-public'),
    (0, common_1.Controller)('cms/banners/public'),
    __metadata("design:paramtypes", [read_cms_banners_service_1.ReadCmsBannersService])
], ReadCmsBannersPublicController);
//# sourceMappingURL=read.cms-banners.public.controller.js.map
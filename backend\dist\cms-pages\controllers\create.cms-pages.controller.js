"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreateCmsPagesController = void 0;
const openapi = require("@nestjs/swagger");
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const jwt_auth_guard_1 = require("../../common/guards/jwt-auth.guard");
const create_cms_pages_service_1 = require("../services/create.cms-pages.service");
const create_cms_page_dto_1 = require("../dto/create.cms-page.dto");
const api_response_dto_1 = require("../../common/dto/api-response.dto");
const get_user_decorator_1 = require("../../common/decorators/get-user.decorator");
const roles_decorator_1 = require("../../common/decorators/roles.decorator");
const permissions_decorator_1 = require("../../common/decorators/permissions.decorator");
let CreateCmsPagesController = class CreateCmsPagesController {
    cmsPagesService;
    constructor(cmsPagesService) {
        this.cmsPagesService = cmsPagesService;
    }
    async create(createCmsPageDto, userId) {
        return this.cmsPagesService.create(createCmsPageDto, userId);
    }
    async bulkCreate(createCmsPageDtos, userId) {
        return this.cmsPagesService.bulkCreate(createCmsPageDtos, userId);
    }
    async createFromTemplate(templateName, title, userId) {
        return this.cmsPagesService.createFromTemplate(templateName, title, userId);
    }
    async duplicate(sourceId, newTitle, userId) {
        return this.cmsPagesService.duplicate(sourceId, newTitle, userId);
    }
    async importPages(pagesData, userId) {
        return this.cmsPagesService.importPages(pagesData, userId);
    }
};
exports.CreateCmsPagesController = CreateCmsPagesController;
__decorate([
    (0, common_1.Post)(),
    (0, roles_decorator_1.Roles)('ADMIN', 'EDITOR'),
    (0, permissions_decorator_1.Permissions)('cms-page:create'),
    (0, swagger_1.ApiOperation)({ summary: 'Tạo mới trang CMS' }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.CREATED,
        description: 'Trang CMS đã được tạo thành công.',
        type: api_response_dto_1.ApiResponseDto,
        schema: {
            properties: {
                data: { $ref: '#/components/schemas/CmsPageDto' },
            },
        },
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.BAD_REQUEST,
        description: 'Dữ liệu đầu vào không hợp lệ.',
        type: api_response_dto_1.ApiResponseDto,
        schema: { properties: { data: { type: 'object', nullable: true } } },
    }),
    (0, swagger_1.ApiBody)({ type: create_cms_page_dto_1.CreateCmsPageDto }),
    openapi.ApiResponse({ status: 201, type: require("../dto/cms-page.dto").CmsPageDto }),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, get_user_decorator_1.GetUser)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [create_cms_page_dto_1.CreateCmsPageDto, String]),
    __metadata("design:returntype", Promise)
], CreateCmsPagesController.prototype, "create", null);
__decorate([
    (0, common_1.Post)('bulk'),
    (0, roles_decorator_1.Roles)('ADMIN'),
    (0, permissions_decorator_1.Permissions)('cms-page:create'),
    (0, swagger_1.ApiOperation)({ summary: 'Tạo nhiều trang CMS cùng lúc' }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.CREATED,
        description: 'Các trang CMS đã được tạo thành công.',
        type: api_response_dto_1.ApiResponseDto,
        schema: {
            properties: {
                data: {
                    type: 'array',
                    items: { $ref: '#/components/schemas/CmsPageDto' },
                },
            },
        },
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.BAD_REQUEST,
        description: 'Dữ liệu đầu vào không hợp lệ.',
        type: api_response_dto_1.ApiResponseDto,
        schema: { properties: { data: { type: 'object', nullable: true } } },
    }),
    (0, swagger_1.ApiBody)({ type: [create_cms_page_dto_1.CreateCmsPageDto] }),
    openapi.ApiResponse({ status: 201, type: [require("../dto/cms-page.dto").CmsPageDto] }),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, get_user_decorator_1.GetUser)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Array, String]),
    __metadata("design:returntype", Promise)
], CreateCmsPagesController.prototype, "bulkCreate", null);
__decorate([
    (0, common_1.Post)('from-template'),
    (0, roles_decorator_1.Roles)('ADMIN', 'EDITOR'),
    (0, permissions_decorator_1.Permissions)('cms-page:create'),
    (0, swagger_1.ApiOperation)({ summary: 'Tạo trang từ template có sẵn' }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.CREATED,
        description: 'Trang từ template đã được tạo thành công.',
        type: api_response_dto_1.ApiResponseDto,
        schema: {
            properties: { data: { $ref: '#/components/schemas/CmsPageDto' } },
        },
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.BAD_REQUEST,
        description: 'Template không hợp lệ.',
        type: api_response_dto_1.ApiResponseDto,
        schema: { properties: { data: { type: 'object', nullable: true } } },
    }),
    (0, swagger_1.ApiBody)({
        schema: {
            type: 'object',
            properties: {
                templateName: {
                    type: 'string',
                    description: 'Tên template',
                    example: 'default',
                    enum: ['default', 'full-width', 'sidebar-left', 'sidebar-right', 'landing-page', 'contact', 'about', 'privacy-policy', 'terms-of-service'],
                },
                title: {
                    type: 'string',
                    description: 'Tiêu đề trang',
                    example: 'Trang mới từ template',
                },
            },
            required: ['templateName', 'title'],
        },
    }),
    openapi.ApiResponse({ status: 201, type: require("../dto/cms-page.dto").CmsPageDto }),
    __param(0, (0, common_1.Body)('templateName')),
    __param(1, (0, common_1.Body)('title')),
    __param(2, (0, get_user_decorator_1.GetUser)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String, String]),
    __metadata("design:returntype", Promise)
], CreateCmsPagesController.prototype, "createFromTemplate", null);
__decorate([
    (0, common_1.Post)(':id/duplicate'),
    (0, roles_decorator_1.Roles)('ADMIN', 'EDITOR'),
    (0, permissions_decorator_1.Permissions)('cms-page:create'),
    (0, swagger_1.ApiOperation)({ summary: 'Sao chép trang CMS' }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.CREATED,
        description: 'Trang đã được sao chép thành công.',
        type: api_response_dto_1.ApiResponseDto,
        schema: {
            properties: { data: { $ref: '#/components/schemas/CmsPageDto' } },
        },
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.NOT_FOUND,
        description: 'Không tìm thấy trang nguồn.',
        type: api_response_dto_1.ApiResponseDto,
        schema: { properties: { data: { type: 'object', nullable: true } } },
    }),
    (0, swagger_1.ApiQuery)({
        name: 'newTitle',
        required: false,
        type: String,
        description: 'Tiêu đề mới cho trang sao chép',
        example: 'Bản sao của trang gốc',
    }),
    openapi.ApiResponse({ status: 201, type: require("../dto/cms-page.dto").CmsPageDto }),
    __param(0, (0, common_1.Body)('sourceId')),
    __param(1, (0, common_1.Query)('newTitle')),
    __param(2, (0, get_user_decorator_1.GetUser)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String, String]),
    __metadata("design:returntype", Promise)
], CreateCmsPagesController.prototype, "duplicate", null);
__decorate([
    (0, common_1.Post)('import'),
    (0, roles_decorator_1.Roles)('ADMIN'),
    (0, permissions_decorator_1.Permissions)('cms-page:create'),
    (0, swagger_1.ApiOperation)({ summary: 'Import trang từ dữ liệu bên ngoài' }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.CREATED,
        description: 'Trang đã được import thành công.',
        type: api_response_dto_1.ApiResponseDto,
        schema: {
            properties: {
                data: {
                    type: 'array',
                    items: { $ref: '#/components/schemas/CmsPageDto' },
                },
            },
        },
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.BAD_REQUEST,
        description: 'Dữ liệu import không hợp lệ.',
        type: api_response_dto_1.ApiResponseDto,
        schema: { properties: { data: { type: 'object', nullable: true } } },
    }),
    (0, swagger_1.ApiBody)({
        schema: {
            type: 'array',
            items: {
                type: 'object',
                properties: {
                    title: { type: 'string' },
                    slug: { type: 'string' },
                    content: { type: 'string' },
                    template: { type: 'string' },
                    status: { type: 'string' },
                    metaTitle: { type: 'string' },
                    metaDescription: { type: 'string' },
                    metaKeywords: { type: 'string' },
                },
                required: ['title'],
            },
        },
    }),
    openapi.ApiResponse({ status: 201, type: [require("../dto/cms-page.dto").CmsPageDto] }),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, get_user_decorator_1.GetUser)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Array, String]),
    __metadata("design:returntype", Promise)
], CreateCmsPagesController.prototype, "importPages", null);
exports.CreateCmsPagesController = CreateCmsPagesController = __decorate([
    (0, swagger_1.ApiTags)('cms-pages'),
    (0, swagger_1.ApiBearerAuth)(),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, common_1.Controller)('cms/pages'),
    __metadata("design:paramtypes", [create_cms_pages_service_1.CreateCmsPagesService])
], CreateCmsPagesController);
//# sourceMappingURL=create.cms-pages.controller.js.map
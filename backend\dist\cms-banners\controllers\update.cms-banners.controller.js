"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UpdateCmsBannersController = void 0;
const openapi = require("@nestjs/swagger");
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const jwt_auth_guard_1 = require("../../common/guards/jwt-auth.guard");
const update_cms_banners_service_1 = require("../services/update.cms-banners.service");
const read_cms_banners_service_1 = require("../services/read.cms-banners.service");
const update_cms_banner_dto_1 = require("../dto/update.cms-banner.dto");
const cms_banners_entity_1 = require("../entity/cms-banners.entity");
const api_response_dto_1 = require("../../common/dto/api-response.dto");
const get_user_decorator_1 = require("../../common/decorators/get-user.decorator");
const roles_decorator_1 = require("../../common/decorators/roles.decorator");
const permissions_decorator_1 = require("../../common/decorators/permissions.decorator");
let UpdateCmsBannersController = class UpdateCmsBannersController {
    updateCmsBannersService;
    readCmsBannersService;
    constructor(updateCmsBannersService, readCmsBannersService) {
        this.updateCmsBannersService = updateCmsBannersService;
        this.readCmsBannersService = readCmsBannersService;
    }
    async update(id, updateCmsBannerDto, userId) {
        const existingBanner = await this.readCmsBannersService.findOne(id);
        if (!existingBanner) {
            throw new common_1.NotFoundException(`Không tìm thấy banner CMS với ID: ${id}`);
        }
        const result = await this.updateCmsBannersService.update(id, updateCmsBannerDto, userId);
        if (!result) {
            throw new common_1.NotFoundException(`Không tìm thấy banner CMS với ID: ${id}`);
        }
        return result;
    }
    async bulkUpdate(updates, userId) {
        return this.updateCmsBannersService.bulkUpdate(updates, userId);
    }
    async bulkUpdateStatus(body, userId) {
        return this.updateCmsBannersService.bulkUpdateStatus(body.ids, body.status, userId);
    }
    async updateStatus(id, status, userId) {
        const result = await this.updateCmsBannersService.updateStatus(id, status, userId);
        if (!result) {
            throw new common_1.NotFoundException(`Không tìm thấy banner CMS với ID: ${id}`);
        }
        return result;
    }
    async updateLocation(id, location, userId) {
        const result = await this.updateCmsBannersService.updateLocation(id, location, userId);
        if (!result) {
            throw new common_1.NotFoundException(`Không tìm thấy banner CMS với ID: ${id}`);
        }
        return result;
    }
    async updateDisplayOrder(id, displayOrder, userId) {
        const result = await this.updateCmsBannersService.updateDisplayOrder(id, displayOrder, userId);
        if (!result) {
            throw new common_1.NotFoundException(`Không tìm thấy banner CMS với ID: ${id}`);
        }
        return result;
    }
    async updateDisplayTime(id, startDate, endDate, userId) {
        const result = await this.updateCmsBannersService.updateDisplayTime(id, startDate, endDate, userId);
        if (!result) {
            throw new common_1.NotFoundException(`Không tìm thấy banner CMS với ID: ${id}`);
        }
        return result;
    }
};
exports.UpdateCmsBannersController = UpdateCmsBannersController;
__decorate([
    (0, common_1.Put)(':id'),
    (0, roles_decorator_1.Roles)('ADMIN', 'EDITOR'),
    (0, permissions_decorator_1.Permissions)('cms-banner:update'),
    (0, swagger_1.ApiOperation)({ summary: 'Cập nhật thông tin banner CMS' }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Banner CMS đã được cập nhật thành công.',
        type: api_response_dto_1.ApiResponseDto,
        schema: {
            properties: {
                data: { $ref: '#/components/schemas/CmsBannerDto' },
            },
        },
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.NOT_FOUND,
        description: 'Không tìm thấy banner CMS.',
        type: api_response_dto_1.ApiResponseDto,
        schema: { properties: { data: { type: 'object', nullable: true } } },
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.BAD_REQUEST,
        description: 'Dữ liệu đầu vào không hợp lệ.',
        type: api_response_dto_1.ApiResponseDto,
        schema: { properties: { data: { type: 'object', nullable: true } } },
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.CONFLICT,
        description: 'Tên banner đã tồn tại.',
        type: api_response_dto_1.ApiResponseDto,
        schema: { properties: { data: { type: 'object', nullable: true } } },
    }),
    (0, swagger_1.ApiParam)({ name: 'id', type: String, description: 'ID của banner CMS' }),
    (0, swagger_1.ApiBody)({ type: update_cms_banner_dto_1.UpdateCmsBannerDto }),
    openapi.ApiResponse({ status: 200, type: Object }),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __param(1, (0, common_1.Body)()),
    __param(2, (0, get_user_decorator_1.GetUser)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, update_cms_banner_dto_1.UpdateCmsBannerDto, String]),
    __metadata("design:returntype", Promise)
], UpdateCmsBannersController.prototype, "update", null);
__decorate([
    (0, common_1.Patch)('bulk'),
    (0, roles_decorator_1.Roles)('ADMIN'),
    (0, permissions_decorator_1.Permissions)('cms-banner:update'),
    (0, swagger_1.ApiOperation)({ summary: 'Cập nhật nhiều banner CMS cùng lúc' }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Các banner CMS đã được cập nhật thành công.',
        type: api_response_dto_1.ApiResponseDto,
        schema: {
            properties: {
                data: {
                    type: 'array',
                    items: { $ref: '#/components/schemas/CmsBannerDto' },
                },
            },
        },
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.BAD_REQUEST,
        description: 'Dữ liệu đầu vào không hợp lệ.',
        type: api_response_dto_1.ApiResponseDto,
        schema: { properties: { data: { type: 'object', nullable: true } } },
    }),
    (0, swagger_1.ApiBody)({
        schema: {
            type: 'array',
            items: {
                type: 'object',
                properties: {
                    id: { type: 'string', format: 'uuid' },
                    data: { $ref: '#/components/schemas/UpdateCmsBannerDto' },
                },
                required: ['id', 'data'],
            },
        },
    }),
    openapi.ApiResponse({ status: 200, type: [require("../dto/cms-banner.dto").CmsBannerDto] }),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, get_user_decorator_1.GetUser)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Array, String]),
    __metadata("design:returntype", Promise)
], UpdateCmsBannersController.prototype, "bulkUpdate", null);
__decorate([
    (0, common_1.Put)('bulk/status'),
    (0, roles_decorator_1.Roles)('ADMIN'),
    (0, permissions_decorator_1.Permissions)('cms-banner:update'),
    (0, swagger_1.ApiOperation)({ summary: 'Cập nhật trạng thái nhiều banner CMS cùng lúc' }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Trạng thái các banner CMS đã được cập nhật thành công.',
        type: api_response_dto_1.ApiResponseDto,
        schema: {
            properties: {
                data: {
                    type: 'array',
                    items: { $ref: '#/components/schemas/CmsBannerDto' },
                },
            },
        },
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.BAD_REQUEST,
        description: 'Dữ liệu đầu vào không hợp lệ.',
        type: api_response_dto_1.ApiResponseDto,
        schema: { properties: { data: { type: 'object', nullable: true } } },
    }),
    (0, swagger_1.ApiBody)({
        schema: {
            type: 'object',
            properties: {
                ids: {
                    type: 'array',
                    items: { type: 'string', format: 'uuid' },
                    description: 'Danh sách ID của các banner cần cập nhật',
                },
                status: {
                    type: 'string',
                    enum: Object.values(cms_banners_entity_1.CmsBannerStatus),
                    description: 'Trạng thái mới',
                    example: cms_banners_entity_1.CmsBannerStatus.ACTIVE,
                },
            },
            required: ['ids', 'status'],
        },
    }),
    openapi.ApiResponse({ status: 200, type: [require("../dto/cms-banner.dto").CmsBannerDto] }),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, get_user_decorator_1.GetUser)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, String]),
    __metadata("design:returntype", Promise)
], UpdateCmsBannersController.prototype, "bulkUpdateStatus", null);
__decorate([
    (0, common_1.Put)(':id/status'),
    (0, roles_decorator_1.Roles)('ADMIN', 'EDITOR'),
    (0, permissions_decorator_1.Permissions)('cms-banner:update'),
    (0, swagger_1.ApiOperation)({ summary: 'Cập nhật trạng thái banner CMS' }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Trạng thái banner CMS đã được cập nhật thành công.',
        type: api_response_dto_1.ApiResponseDto,
        schema: {
            properties: {
                data: { $ref: '#/components/schemas/CmsBannerDto' },
            },
        },
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.NOT_FOUND,
        description: 'Không tìm thấy banner CMS.',
        type: api_response_dto_1.ApiResponseDto,
        schema: { properties: { data: { type: 'object', nullable: true } } },
    }),
    (0, swagger_1.ApiParam)({ name: 'id', type: String, description: 'ID của banner CMS' }),
    (0, swagger_1.ApiBody)({
        schema: {
            type: 'object',
            properties: {
                status: {
                    type: 'string',
                    enum: Object.values(cms_banners_entity_1.CmsBannerStatus),
                    description: 'Trạng thái mới',
                    example: cms_banners_entity_1.CmsBannerStatus.ACTIVE,
                },
            },
            required: ['status'],
        },
    }),
    openapi.ApiResponse({ status: 200, type: Object }),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __param(1, (0, common_1.Body)('status')),
    __param(2, (0, get_user_decorator_1.GetUser)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String, String]),
    __metadata("design:returntype", Promise)
], UpdateCmsBannersController.prototype, "updateStatus", null);
__decorate([
    (0, common_1.Put)(':id/location'),
    (0, roles_decorator_1.Roles)('ADMIN', 'EDITOR'),
    (0, permissions_decorator_1.Permissions)('cms-banner:update'),
    (0, swagger_1.ApiOperation)({ summary: 'Cập nhật vị trí hiển thị banner CMS' }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Vị trí hiển thị banner CMS đã được cập nhật thành công.',
        type: api_response_dto_1.ApiResponseDto,
        schema: {
            properties: {
                data: { $ref: '#/components/schemas/CmsBannerDto' },
            },
        },
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.NOT_FOUND,
        description: 'Không tìm thấy banner CMS.',
        type: api_response_dto_1.ApiResponseDto,
        schema: { properties: { data: { type: 'object', nullable: true } } },
    }),
    (0, swagger_1.ApiParam)({ name: 'id', type: String, description: 'ID của banner CMS' }),
    (0, swagger_1.ApiBody)({
        schema: {
            type: 'object',
            properties: {
                location: {
                    type: 'string',
                    enum: Object.values(cms_banners_entity_1.CmsBannerLocation),
                    description: 'Vị trí hiển thị mới',
                    example: cms_banners_entity_1.CmsBannerLocation.HOMEPAGE_SLIDER,
                    nullable: true,
                },
            },
            required: ['location'],
        },
    }),
    openapi.ApiResponse({ status: 200, type: Object }),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __param(1, (0, common_1.Body)('location')),
    __param(2, (0, get_user_decorator_1.GetUser)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object, String]),
    __metadata("design:returntype", Promise)
], UpdateCmsBannersController.prototype, "updateLocation", null);
__decorate([
    (0, common_1.Put)(':id/display-order'),
    (0, roles_decorator_1.Roles)('ADMIN', 'EDITOR'),
    (0, permissions_decorator_1.Permissions)('cms-banner:update'),
    (0, swagger_1.ApiOperation)({ summary: 'Cập nhật thứ tự hiển thị banner CMS' }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Thứ tự hiển thị banner CMS đã được cập nhật thành công.',
        type: api_response_dto_1.ApiResponseDto,
        schema: {
            properties: {
                data: { $ref: '#/components/schemas/CmsBannerDto' },
            },
        },
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.NOT_FOUND,
        description: 'Không tìm thấy banner CMS.',
        type: api_response_dto_1.ApiResponseDto,
        schema: { properties: { data: { type: 'object', nullable: true } } },
    }),
    (0, swagger_1.ApiParam)({ name: 'id', type: String, description: 'ID của banner CMS' }),
    (0, swagger_1.ApiBody)({
        schema: {
            type: 'object',
            properties: {
                displayOrder: {
                    type: 'number',
                    description: 'Thứ tự hiển thị mới',
                    example: 1,
                },
            },
            required: ['displayOrder'],
        },
    }),
    openapi.ApiResponse({ status: 200, type: Object }),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __param(1, (0, common_1.Body)('displayOrder')),
    __param(2, (0, get_user_decorator_1.GetUser)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Number, String]),
    __metadata("design:returntype", Promise)
], UpdateCmsBannersController.prototype, "updateDisplayOrder", null);
__decorate([
    (0, common_1.Patch)(':id/display-time'),
    (0, roles_decorator_1.Roles)('ADMIN', 'EDITOR'),
    (0, permissions_decorator_1.Permissions)('cms-banner:update'),
    (0, swagger_1.ApiOperation)({ summary: 'Cập nhật thời gian hiển thị banner CMS' }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Thời gian hiển thị banner CMS đã được cập nhật thành công.',
        type: api_response_dto_1.ApiResponseDto,
        schema: {
            properties: {
                data: { $ref: '#/components/schemas/CmsBannerDto' },
            },
        },
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.NOT_FOUND,
        description: 'Không tìm thấy banner CMS.',
        type: api_response_dto_1.ApiResponseDto,
        schema: { properties: { data: { type: 'object', nullable: true } } },
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.BAD_REQUEST,
        description: 'Ngày bắt đầu phải nhỏ hơn ngày kết thúc.',
        type: api_response_dto_1.ApiResponseDto,
        schema: { properties: { data: { type: 'object', nullable: true } } },
    }),
    (0, swagger_1.ApiParam)({ name: 'id', type: String, description: 'ID của banner CMS' }),
    (0, swagger_1.ApiBody)({
        schema: {
            type: 'object',
            properties: {
                startDate: {
                    type: 'string',
                    format: 'date-time',
                    description: 'Ngày bắt đầu hiển thị (null để không giới hạn)',
                    example: '2023-12-01T00:00:00.000Z',
                },
                endDate: {
                    type: 'string',
                    format: 'date-time',
                    description: 'Ngày kết thúc hiển thị (null để không giới hạn)',
                    example: '2023-12-31T23:59:59.000Z',
                },
            },
        },
    }),
    openapi.ApiResponse({ status: 200, type: Object }),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __param(1, (0, common_1.Body)('startDate')),
    __param(2, (0, common_1.Body)('endDate')),
    __param(3, (0, get_user_decorator_1.GetUser)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object, Object, String]),
    __metadata("design:returntype", Promise)
], UpdateCmsBannersController.prototype, "updateDisplayTime", null);
exports.UpdateCmsBannersController = UpdateCmsBannersController = __decorate([
    (0, swagger_1.ApiTags)('cms-banners'),
    (0, swagger_1.ApiBearerAuth)(),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, common_1.Controller)('cms/banners'),
    __metadata("design:paramtypes", [update_cms_banners_service_1.UpdateCmsBannersService,
        read_cms_banners_service_1.ReadCmsBannersService])
], UpdateCmsBannersController);
//# sourceMappingURL=update.cms-banners.controller.js.map
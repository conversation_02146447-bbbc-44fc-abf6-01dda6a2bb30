/**
 * Service xử lý xác thực người dùng
 */
import { api } from './api';

// Đ<PERSON>nh nghĩa các kiểu dữ liệu
export interface LoginCredentials {
  identity: string;
  password: string;
}

export interface RegisterCredentials {
  username: string;
  email: string;
  password: string;
  fullName?: string;
  phone: string; // Đã thay đổi từ optional thành required
  address?: string;
  referredByCode?: string;
  firebaseToken?: string; // Token xác thực từ Firebase sau khi xác minh OTP
  registerType?: 'PHONE' | 'EMAIL'; // Phương thức xác thực
}

export interface User {
  id: string;
  username: string;
  email: string;
  fullName?: string;
  roles?: string[];
  permissions?: string[];
  exp?: number; // Thời gian hết hạn của token
  [key: string]: any;
}

export interface AuthResponse {
  access_token: string;
  refresh_token: string;
  user?: User;
}

export interface RefreshTokenRequest {
  refreshToken: string;
}

/**
 * Hàm giải mã JWT token để lấy thông tin người dùng
 */
export const decodeToken = (token: string): User | null => {
  try {
    if (!token) return null;

    // JWT token có 3 phần: header.payload.signature
    const base64Url = token.split('.')[1];
    if (!base64Url) return null;

    // Thay thế các ký tự đặc biệt trong base64Url
    const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');

    // Giải mã base64 thành JSON
    const jsonPayload = decodeURIComponent(
      atob(base64)
        .split('')
        .map(c => '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2))
        .join('')
    );

    const payload = JSON.parse(jsonPayload);

    // Chuyển đổi payload thành User
    const user: User = {
      id: payload.sub || payload.id || '',
      username: payload.username || '',
      email: payload.email || '',
      fullName: payload.fullName || payload.name || '',
      roles: payload.roles || [],
      permissions: payload.permissions || [],
      exp: payload.exp || 0, // Thêm trường exp (expiration time)
    };

    return user;
  } catch (error) {
    console.error('Error decoding token:', error);
    return null;
  }
};

/**
 * Kiểm tra xem token đã hết hạn chưa
 */
export const isTokenExpired = (token: string): boolean => {
  try {
    if (!token) return true;

    const decodedToken = decodeToken(token);
    if (!decodedToken || !decodedToken.exp) return true;

    // exp là thời gian hết hạn tính bằng giây kể từ epoch (1/1/1970)
    const currentTime = Math.floor(Date.now() / 1000); // Chuyển đổi thời gian hiện tại sang giây

    // Trả về true nếu token đã hết hạn
    return decodedToken.exp < currentTime;
  } catch (error) {
    console.error('Error checking token expiration:', error);
    return true; // Nếu có lỗi, coi như token đã hết hạn
  }
};

/**
 * Kiểm tra xem token có sắp hết hạn không (trong vòng 5 phút)
 */
export const isTokenExpiringSoon = (token: string, thresholdSeconds: number = 300): boolean => {
  try {
    if (!token) return true;

    const decodedToken = decodeToken(token);
    if (!decodedToken || !decodedToken.exp) return true;

    const currentTime = Math.floor(Date.now() / 1000);

    // Trả về true nếu token sắp hết hạn trong vòng thresholdSeconds giây
    return decodedToken.exp - currentTime < thresholdSeconds;
  } catch (error) {
    console.error('Error checking token expiration:', error);
    return true;
  }
};

/**
 * Service xử lý xác thực
 */
export const authService = {
  /**
   * Đăng nhập người dùng
   */
  async login(credentials: LoginCredentials): Promise<AuthResponse> {
    try {
      const response = await api.post<{ data: AuthResponse }>('auth/login', credentials);

      // Kiểm tra cấu trúc response
      const authData = response.data || response;

      // Lưu token vào localStorage và cookie
      localStorage.setItem('accessToken', authData.access_token);
      localStorage.setItem('refreshToken', authData.refresh_token);

      // Lưu token vào cookie để middleware có thể truy cập
      document.cookie = `token=${authData.access_token}; path=/; max-age=${60 * 60 * 24 * 7}; SameSite=Lax`; // 7 ngày

      // Nếu không có thông tin người dùng trong response, giải mã token để lấy
      if (!authData.user && authData.access_token) {
        const decodedUser = decodeToken(authData.access_token);
        if (decodedUser) {
          authData.user = decodedUser;
          this.saveUser(decodedUser);
        }
      } else if (authData.user) {
        // Lưu thông tin người dùng
        this.saveUser(authData.user);
      }

      return authData;
    } catch (error) {
      console.error('Login error:', error);
      throw error;
    }
  },

  /**
   * Đăng ký người dùng mới
   */
  async register(credentials: RegisterCredentials): Promise<AuthResponse> {
    try {
      const response = await api.post<{ data: AuthResponse }>('auth/register', credentials);

      // Kiểm tra cấu trúc response
      const authData = response.data || response;

      // Lưu token vào localStorage và cookie
      localStorage.setItem('accessToken', authData.access_token);
      localStorage.setItem('refreshToken', authData.refresh_token);

      // Lưu token vào cookie để middleware có thể truy cập
      document.cookie = `token=${authData.access_token}; path=/; max-age=${60 * 60 * 24 * 7}; SameSite=Lax`; // 7 ngày

      // Nếu không có thông tin người dùng trong response, giải mã token để lấy
      if (!authData.user && authData.access_token) {
        const decodedUser = decodeToken(authData.access_token);
        if (decodedUser) {
          authData.user = decodedUser;
          this.saveUser(decodedUser);
        }
      } else if (authData.user) {
        // Lưu thông tin người dùng
        this.saveUser(authData.user);
      }

      return authData;
    } catch (error) {
      console.error('Register error:', error);
      throw error;
    }
  },

  /**
   * Làm mới token
   */
  async refreshToken(refreshToken: string): Promise<AuthResponse> {
    try {
      const response = await api.post<{ data: AuthResponse }>('auth/refresh-token', { refreshToken });

      // Kiểm tra cấu trúc response
      const authData = response.data || response;

      // Cập nhật token trong localStorage và cookie
      localStorage.setItem('accessToken', authData.access_token);
      localStorage.setItem('refreshToken', authData.refresh_token);

      // Cập nhật token trong cookie để middleware có thể truy cập
      document.cookie = `token=${authData.access_token}; path=/; max-age=${60 * 60 * 24 * 7}; SameSite=Lax`; // 7 ngày

      // Nếu không có thông tin người dùng trong response, giải mã token để lấy
      if (!authData.user && authData.access_token) {
        const decodedUser = decodeToken(authData.access_token);
        if (decodedUser) {
          authData.user = decodedUser;
          this.saveUser(decodedUser);
        }
      } else if (authData.user) {
        // Lưu thông tin người dùng
        this.saveUser(authData.user);
      }

      return authData;
    } catch (error) {
      console.error('Refresh token error:', error);
      // Nếu refresh token thất bại, đăng xuất người dùng
      this.logout();
      throw error;
    }
  },

  /**
   * Thử làm mới token nếu có refresh token
   */
  async tryRefreshToken(): Promise<boolean> {
    try {
      const refreshToken = this.getRefreshToken();
      if (!refreshToken) return false;

      const result = await this.refreshToken(refreshToken);

      // Đảm bảo user state được cập nhật sau refresh
      if (result && result.user) {
        // Trigger event để các component khác biết user đã được cập nhật
        window.dispatchEvent(new CustomEvent('auth:userUpdated', {
          detail: { user: result.user }
        }));
      }

      // Việc điều hướng sẽ được xử lý ở handleTokenRefresh trong api.ts
      // Không điều hướng ở đây để tránh điều hướng kép

      return true;
    } catch (error) {
      console.error('Try refresh token error:', error);
      return false;
    }
  },

  /**
   * Điều hướng người dùng dựa trên vai trò
   * @param user - User object
   * @param forceRedirect - Có bắt buộc redirect không (mặc định: false)
   */
  redirectBasedOnRole(user: User, forceRedirect: boolean = false): void {
    if (typeof window === 'undefined') return;

    // Ưu tiên ADMIN trước - kiểm tra roles an toàn
    const isAdmin = user.roles && Array.isArray(user.roles) && user.roles.includes('ADMIN');
    const currentPath = window.location.pathname;

    console.log('redirectBasedOnRole:', {
      userId: user.id,
      roles: user.roles,
      isAdmin,
      currentPath,
      forceRedirect
    });

    // Nếu không bắt buộc redirect, kiểm tra xem có cần redirect không
    if (!forceRedirect) {
      const isOnCorrectPath =
        (isAdmin && currentPath.startsWith('/admin')) ||
        (!isAdmin && !currentPath.startsWith('/admin') && currentPath !== '/login');

      // Nếu đã ở đúng path, không cần redirect
      if (isOnCorrectPath) {
        console.log('Already on correct path, no redirect needed');
        return;
      }
    }

    // Điều hướng dựa vào vai trò - ưu tiên ADMIN
    if (isAdmin) {
      console.log('Redirecting to admin dashboard');
      window.location.href = '/admin/dashboard';
    } else {
      console.log('Redirecting to user dashboard');
      window.location.href = '/dashboard';
    }
  },

  /**
   * Đăng xuất người dùng
   */
  logout(): void {
    localStorage.removeItem('accessToken');
    localStorage.removeItem('refreshToken');
    localStorage.removeItem('user');

    // Xóa cookie token
    document.cookie = 'token=; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT; SameSite=Lax';

    // Delay redirect một chút để UI có thời gian cập nhật
    if (typeof window !== 'undefined') {
      setTimeout(() => {
        window.location.href = '/login';
      }, 50); // 50ms delay - đủ để UI cập nhật
    }
  },

  /**
   * Kiểm tra người dùng đã đăng nhập chưa
   */
  isAuthenticated(): boolean {
    if (typeof window === 'undefined') return false;

    // Kiểm tra token trong localStorage hoặc cookie
    const localToken = localStorage.getItem('accessToken');
    if (localToken) {
      // Kiểm tra xem token có hợp lệ và chưa hết hạn không
      if (!isTokenExpired(localToken)) {
        // Nếu token sắp hết hạn, thử refresh token
        if (isTokenExpiringSoon(localToken)) {
          this.tryRefreshToken();
        }
        return true;
      } else {
        // Nếu token đã hết hạn, thử refresh token
        this.tryRefreshToken();
        return false;
      }
    }

    // Kiểm tra token trong cookie
    const cookies = document.cookie.split(';');
    for (let i = 0; i < cookies.length; i++) {
      const cookie = cookies[i].trim();
      if (cookie.startsWith('token=')) {
        const token = cookie.substring('token='.length, cookie.length);
        if (token) {
          // Kiểm tra xem token có hợp lệ và chưa hết hạn không
          if (!isTokenExpired(token)) {
            // Nếu có token trong cookie nhưng không có trong localStorage,
            // lưu vào localStorage để sử dụng
            if (!localToken) {
              localStorage.setItem('accessToken', token);
              // Giải mã token để lấy thông tin người dùng
              const user = decodeToken(token);
              if (user) {
                this.saveUser(user);
              }
            }

            // Nếu token sắp hết hạn, thử refresh token
            if (isTokenExpiringSoon(token)) {
              this.tryRefreshToken();
            }

            return true;
          } else {
            // Nếu token đã hết hạn, thử refresh token
            this.tryRefreshToken();
          }
        }
      }
    }

    return false;
  },

  /**
   * Lấy thông tin người dùng hiện tại
   */
  getCurrentUser(): User | null {
    if (typeof window === 'undefined') return null;

    const userJson = localStorage.getItem('user');
    if (!userJson) return null;

    try {
      return JSON.parse(userJson);
    } catch (error) {
      console.error('Error parsing user data:', error);
      return null;
    }
  },

  /**
   * Lưu thông tin người dùng
   */
  saveUser(user: User): void {
    localStorage.setItem('user', JSON.stringify(user));
  },

  /**
   * Lấy token hiện tại
   */
  getAccessToken(): string | null {
    if (typeof window === 'undefined') return null;
    return localStorage.getItem('accessToken');
  },

  /**
   * Lấy refresh token
   */
  getRefreshToken(): string | null {
    if (typeof window === 'undefined') return null;
    return localStorage.getItem('refreshToken');
  },

  /**
   * Xác minh số điện thoại cho người dùng hiện có
   * @param userId ID của người dùng
   * @param firebaseToken Firebase ID token
   * @returns Kết quả xác minh
   */
  async verifyPhoneNumber(userId: string, firebaseToken: string): Promise<any> {
    try {
      const response = await api.post<{ data: any }>('auth/verify-phone-public', {
        userId,
        firebaseToken
      });

      return response.data || response;
    } catch (error) {
      console.error('Phone verification error:', error);
      throw error;
    }
  },

  /**
   * Unified OTP verification for both phone and email
   * @param verificationData Verification data
   * @returns Verification result with tokens
   */
  async verifyOtp(verificationData: {
    registerType: 'PHONE' | 'EMAIL';
    email?: string;
    otp?: string;
    firebaseToken?: string;
    userId?: string;
  }): Promise<AuthResponse> {
    try {
      const response = await api.post<{ data: AuthResponse }>('auth/verify-otp', verificationData);

      // Kiểm tra cấu trúc response
      const authData = response.data || response;

      // Lưu token vào localStorage và cookie
      localStorage.setItem('accessToken', authData.access_token);
      localStorage.setItem('refreshToken', authData.refresh_token);

      // Lưu token vào cookie để middleware có thể truy cập
      document.cookie = `token=${authData.access_token}; path=/; max-age=${60 * 60 * 24 * 7}; SameSite=Lax`; // 7 ngày

      // Lưu thông tin user vào localStorage
      if (authData.user) {
        localStorage.setItem('user', JSON.stringify(authData.user));
      }

      return authData;
    } catch (error) {
      console.error('OTP verification error:', error);
      throw error;
    }
  }
};

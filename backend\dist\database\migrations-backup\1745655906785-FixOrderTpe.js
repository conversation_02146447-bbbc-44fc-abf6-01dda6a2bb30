"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.FixOrderTpe1745655906785 = void 0;
class FixOrderTpe1745655906785 {
    async up(queryRunner) {
        try {
            const enumExists = await queryRunner.query(`
                SELECT 1 FROM pg_type 
                WHERE typname = 'order_book_order_type_enum_new'
            `);
            if (enumExists && enumExists.length > 0) {
                await queryRunner.query(`
                    ALTER TYPE "public"."order_book_order_type_enum_new" ADD VALUE IF NOT EXISTS 'WITHDRAWAL'
                `);
            }
            else {
                const originalEnumExists = await queryRunner.query(`
                    SELECT 1 FROM pg_type 
                    WHERE typname = 'order_book_order_type_enum'
                `);
                if (originalEnumExists && originalEnumExists.length > 0) {
                    await queryRunner.query(`DROP TYPE IF EXISTS "public"."order_book_order_type_enum_new"`);
                    await queryRunner.query(`
                        CREATE TYPE "public"."order_book_order_type_enum_new" AS ENUM(
                            'BUY', 
                            'SELL', 
                            'WITHDRAWAL'
                        )
                    `);
                    await queryRunner.query(`ALTER TABLE "order_book" ADD "order_type_temp" varchar`);
                    await queryRunner.query(`UPDATE "order_book" SET "order_type_temp" = "order_type"::text`);
                    await queryRunner.query(`ALTER TABLE "order_book" DROP CONSTRAINT IF EXISTS "order_book_order_type_check"`);
                    await queryRunner.query(`ALTER TABLE "order_book" DROP COLUMN "order_type"`);
                    await queryRunner.query(`ALTER TABLE "order_book" ADD "order_type" "public"."order_book_order_type_enum_new" NOT NULL DEFAULT 'BUY'`);
                    await queryRunner.query(`
                        UPDATE "order_book" 
                        SET "order_type" = 
                            CASE 
                                WHEN "order_type_temp" = 'BUY' THEN 'BUY'::public.order_book_order_type_enum_new
                                WHEN "order_type_temp" = 'SELL' THEN 'SELL'::public.order_book_order_type_enum_new
                                ELSE 'BUY'::public.order_book_order_type_enum_new
                            END
                    `);
                    await queryRunner.query(`ALTER TABLE "order_book" DROP COLUMN "order_type_temp"`);
                    await queryRunner.query(`DROP TYPE IF EXISTS "public"."order_book_order_type_enum"`);
                    await queryRunner.query(`ALTER TYPE "public"."order_book_order_type_enum_new" RENAME TO "order_book_order_type_enum"`);
                }
                else {
                    await queryRunner.query(`
                        CREATE TYPE "public"."order_book_order_type_enum" AS ENUM(
                            'BUY', 
                            'SELL', 
                            'WITHDRAWAL'
                        )
                    `);
                    const columnExists = await queryRunner.query(`
                        SELECT 1 FROM information_schema.columns 
                        WHERE table_name = 'order_book' AND column_name = 'order_type'
                    `);
                    if (columnExists && columnExists.length > 0) {
                        await queryRunner.query(`ALTER TABLE "order_book" ALTER COLUMN "order_type" TYPE "public"."order_book_order_type_enum" USING "order_type"::text::"public"."order_book_order_type_enum"`);
                    }
                    else {
                        await queryRunner.query(`ALTER TABLE "order_book" ADD "order_type" "public"."order_book_order_type_enum" NOT NULL DEFAULT 'BUY'`);
                    }
                }
            }
        }
        catch (error) {
            console.error('Lỗi khi thực hiện migration FixOrderTypeEnum1745700000000:', error);
            throw error;
        }
    }
    async down(queryRunner) {
    }
}
exports.FixOrderTpe1745655906785 = FixOrderTpe1745655906785;
//# sourceMappingURL=1745655906785-FixOrderTpe.js.map
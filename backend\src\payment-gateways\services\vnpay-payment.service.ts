import { Injectable, Logger, BadRequestException } from '@nestjs/common';
import { Transactional } from 'typeorm-transactional';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { v4 as uuidv4 } from 'uuid';

import { VnpayService } from './vnpay.service';
import { VnpayTransactionRepository } from '../repositories/vnpay-transaction.repository';
import { VnpayTransaction, VnpayTransactionStatus, VnpayTransactionType } from '../entities/vnpay-transaction.entity';
import { 
  PaymentRequest, 
  PaymentResponse, 
  PaymentCallback, 
  PaymentQueryRequest, 
  PaymentQueryResponse,
  PaymentRefundRequest,
  PaymentRefundResponse,
  PaymentEventHandler,
  IVnpayPaymentService 
} from '../interfaces/payment-integration.interface';

/**
 * Portable VNPAY Payment Service
 * This service is completely independent and can be copied to any project
 * No dependencies on external entities like Wallet or Transaction
 */
@Injectable()
export class VnpayPaymentService implements IVnpayPaymentService {
  private readonly logger = new Logger(VnpayPaymentService.name);
  private eventHandler?: PaymentEventHandler;

  constructor(
    private readonly vnpayService: VnpayService,
    private readonly vnpayTransactionRepository: VnpayTransactionRepository,
    private readonly eventEmitter: EventEmitter2,
  ) {}

  /**
   * Set external event handler for payment notifications
   */
  setEventHandler(handler: PaymentEventHandler): void {
    this.eventHandler = handler;
  }

  /**
   * Create payment and get redirect URL
   */
  @Transactional()
  async createPayment(request: PaymentRequest): Promise<PaymentResponse> {
    try {
      this.logger.log(`Creating VNPAY payment for external ref: ${request.externalRef}`);

      // Validate request
      this.validatePaymentRequest(request);

      // Generate unique merchant transaction reference
      const merchantTxnRef = this.generateMerchantTxnRef();

      // Calculate expiry time (default 15 minutes)
      const expiresAt = new Date(Date.now() + 15 * 60 * 1000);

      // Create transaction record
      const transaction = await this.vnpayTransactionRepository.create({
        merchantTxnRef,
        type: VnpayTransactionType.PAYMENT,
        status: VnpayTransactionStatus.PENDING,
        amount: request.amount,
        orderInfo: request.description,
        clientIp: request.clientIp,
        bankCode: request.bankCode,
        locale: request.locale || 'vn',
        externalRef: request.externalRef,
        externalMetadata: request.metadata ? JSON.stringify(request.metadata) : null,
        expiresAt,
      });

      // Create VNPAY payment URL
      const vnpayRequest = {
        userId: request.externalRef, // Use external ref as user ID for VNPAY service
        walletId: merchantTxnRef, // Use merchant ref as wallet ID for VNPAY service
        amount: request.amount,
        gatewayType: 'VNPAY' as any,
        description: request.description,
        ipAddress: request.clientIp,
        bankCode: request.bankCode,
        locale: request.locale,
      };

      const vnpayResult = await this.vnpayService.createPaymentUrl(vnpayRequest);

      // Update transaction with VNPAY request data
      await this.vnpayTransactionRepository.update(transaction.id, {
        vnpayTxnRef: vnpayResult.transactionId,
        vnpayRequest: JSON.stringify(vnpayRequest),
      });

      // Prepare response
      const response: PaymentResponse = {
        paymentUrl: vnpayResult.paymentUrl,
        merchantTxnRef,
        transactionId: transaction.id,
        expiresAt,
        gatewayType: 'VNPAY',
      };

      // Emit event
      this.eventEmitter.emit('vnpay.payment.created', {
        merchantTxnRef,
        externalRef: request.externalRef,
        amount: request.amount,
        paymentUrl: vnpayResult.paymentUrl,
      });

      // Call external handler
      if (this.eventHandler?.onPaymentCreated) {
        await this.eventHandler.onPaymentCreated({
          merchantTxnRef,
          externalRef: request.externalRef,
          amount: request.amount,
          paymentUrl: vnpayResult.paymentUrl,
        });
      }

      this.logger.log(`VNPAY payment created successfully: ${merchantTxnRef}`);
      return response;

    } catch (error) {
      this.logger.error(`Error creating VNPAY payment: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Handle return callback from VNPAY
   */
  @Transactional()
  async handleReturnCallback(params: Record<string, string>): Promise<PaymentCallback> {
    try {
      this.logger.log(`Handling VNPAY return callback: ${JSON.stringify(params)}`);

      // Verify callback with VNPAY service
      const verifyResult = await this.vnpayService.verifyReturnUrl(params);

      // Find transaction by VNPAY reference
      const transaction = await this.vnpayTransactionRepository.findByVnpayRef(
        verifyResult.transactionId
      );

      if (!transaction) {
        this.logger.warn(`Transaction not found for VNPAY ref: ${verifyResult.transactionId}`);
        return this.createFailedCallback(params, 'Transaction not found');
      }

      // Update transaction with callback data
      await this.vnpayTransactionRepository.update(transaction.id, {
        returnCallbackData: JSON.stringify(params),
        vnpayResponse: JSON.stringify(verifyResult),
      });

      // Create callback response
      const callback: PaymentCallback = {
        isValid: verifyResult.isValid,
        isSuccess: verifyResult.responseCode === '00' && verifyResult.transactionStatus === '00',
        merchantTxnRef: transaction.merchantTxnRef,
        vnpayTxnRef: verifyResult.transactionId,
        vnpayTxnNo: verifyResult.vnpayTransactionNo,
        amount: verifyResult.amount || transaction.amount,
        responseCode: verifyResult.responseCode,
        transactionStatus: verifyResult.transactionStatus,
        bankCode: verifyResult.bankCode,
        cardType: verifyResult.cardType,
        payDate: verifyResult.payDate,
        message: verifyResult.message,
        externalRef: transaction.externalRef,
        rawData: params,
      };

      // Process based on result
      if (callback.isValid && callback.isSuccess) {
        await this.processSuccessfulPayment(transaction, callback);
      } else {
        await this.processFailedPayment(transaction, callback);
      }

      return callback;

    } catch (error) {
      this.logger.error(`Error handling VNPAY return callback: ${error.message}`, error.stack);
      return this.createFailedCallback(params, `Processing error: ${error.message}`);
    }
  }

  /**
   * Handle IPN callback from VNPAY
   */
  @Transactional()
  async handleIpnCallback(params: Record<string, string>): Promise<PaymentCallback> {
    try {
      this.logger.log(`Handling VNPAY IPN callback: ${JSON.stringify(params)}`);

      // Use same logic as return callback but update IPN data
      const callback = await this.handleReturnCallback(params);

      // Update transaction with IPN data
      if (callback.merchantTxnRef) {
        const transaction = await this.vnpayTransactionRepository.findByMerchantRef(
          callback.merchantTxnRef
        );
        
        if (transaction) {
          await this.vnpayTransactionRepository.update(transaction.id, {
            ipnCallbackData: JSON.stringify(params),
          });
        }
      }

      return callback;

    } catch (error) {
      this.logger.error(`Error handling VNPAY IPN callback: ${error.message}`, error.stack);
      return this.createFailedCallback(params, `IPN processing error: ${error.message}`);
    }
  }

  /**
   * Query transaction status from VNPAY
   */
  async queryTransaction(request: PaymentQueryRequest): Promise<PaymentQueryResponse> {
    try {
      this.logger.log(`Querying VNPAY transaction: ${request.merchantTxnRef}`);

      // Find local transaction
      const transaction = await this.vnpayTransactionRepository.findByMerchantRef(
        request.merchantTxnRef
      );

      if (!transaction) {
        throw new BadRequestException(`Transaction not found: ${request.merchantTxnRef}`);
      }

      // Query VNPAY
      const queryResult = await this.vnpayService.queryTransaction({
        txnRef: transaction.vnpayTxnRef || request.merchantTxnRef,
        transactionDate: request.transactionDate,
        orderInfo: request.orderInfo,
        ipAddr: request.ipAddr,
        transactionNo: request.vnpayTxnNo,
      });

      // Create response
      const response: PaymentQueryResponse = {
        isSuccess: queryResult.responseCode === '00',
        responseCode: queryResult.responseCode,
        message: queryResult.message,
        rawResponse: queryResult,
      };

      // Add transaction details if successful
      if (response.isSuccess && queryResult.transaction) {
        response.transaction = {
          merchantTxnRef: transaction.merchantTxnRef,
          vnpayTxnRef: queryResult.transaction.vnpayTxnRef,
          vnpayTxnNo: queryResult.transaction.vnpayTxnNo,
          amount: queryResult.transaction.amount,
          orderInfo: queryResult.transaction.orderInfo,
          responseCode: queryResult.transaction.responseCode,
          transactionStatus: queryResult.transaction.transactionStatus,
          bankCode: queryResult.transaction.bankCode,
          cardType: queryResult.transaction.cardType,
          payDate: queryResult.transaction.payDate,
        };
      }

      return response;

    } catch (error) {
      this.logger.error(`Error querying VNPAY transaction: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Refund transaction
   */
  async refundTransaction(request: PaymentRefundRequest): Promise<PaymentRefundResponse> {
    try {
      this.logger.log(`Refunding VNPAY transaction: ${request.originalMerchantTxnRef}`);

      // Find original transaction
      const originalTransaction = await this.vnpayTransactionRepository.findByMerchantRef(
        request.originalMerchantTxnRef
      );

      if (!originalTransaction) {
        throw new BadRequestException(`Original transaction not found: ${request.originalMerchantTxnRef}`);
      }

      // Create refund transaction record
      const refundMerchantRef = this.generateMerchantTxnRef();
      const refundTransaction = await this.vnpayTransactionRepository.create({
        merchantTxnRef: refundMerchantRef,
        type: VnpayTransactionType.REFUND,
        status: VnpayTransactionStatus.PENDING,
        amount: request.amount,
        orderInfo: request.orderInfo,
        clientIp: request.ipAddr,
        externalRef: originalTransaction.externalRef,
        externalMetadata: JSON.stringify({
          originalMerchantTxnRef: request.originalMerchantTxnRef,
          refundType: request.transactionType,
          createBy: request.createBy,
        }),
      });

      // Call VNPAY refund API
      const refundResult = await this.vnpayService.refundTransaction({
        txnRef: originalTransaction.vnpayTxnRef || request.originalMerchantTxnRef,
        amount: request.amount,
        orderInfo: request.orderInfo,
        transactionDate: request.transactionDate,
        transactionType: request.transactionType,
        createBy: request.createBy,
        ipAddr: request.ipAddr,
        transactionNo: request.vnpayTxnNo,
      });

      // Update refund transaction
      await this.vnpayTransactionRepository.update(refundTransaction.id, {
        status: refundResult.responseCode === '00' ? 
          VnpayTransactionStatus.SUCCESS : VnpayTransactionStatus.FAILED,
        vnpayResponse: JSON.stringify(refundResult),
        processedAt: new Date(),
      });

      // Create response
      const response: PaymentRefundResponse = {
        isSuccess: refundResult.responseCode === '00',
        responseCode: refundResult.responseCode,
        message: refundResult.message,
        rawResponse: refundResult,
      };

      // Add refund details if successful
      if (response.isSuccess) {
        response.refund = {
          merchantTxnRef: refundMerchantRef,
          vnpayTxnRef: refundResult.vnpayTxnRef,
          amount: request.amount,
          orderInfo: request.orderInfo,
          transactionType: request.transactionType,
          createBy: request.createBy,
          createDate: new Date().toISOString(),
        };
      }

      return response;

    } catch (error) {
      this.logger.error(`Error refunding VNPAY transaction: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Get transaction by merchant reference
   */
  async getTransaction(merchantTxnRef: string): Promise<VnpayTransaction | null> {
    return await this.vnpayTransactionRepository.findByMerchantRef(merchantTxnRef);
  }

  /**
   * Get transaction statistics
   */
  async getStatistics(filter?: any): Promise<any> {
    return await this.vnpayTransactionRepository.getStats(filter);
  }

  // Private helper methods

  private validatePaymentRequest(request: PaymentRequest): void {
    if (!request.externalRef) {
      throw new BadRequestException('External reference is required');
    }
    if (!request.amount || request.amount <= 0) {
      throw new BadRequestException('Amount must be greater than 0');
    }
    if (request.amount < 10000) {
      throw new BadRequestException('Minimum amount is 10,000 VND');
    }
    if (request.amount > 50000000) {
      throw new BadRequestException('Maximum amount is 50,000,000 VND');
    }
    if (!request.description) {
      throw new BadRequestException('Description is required');
    }
    if (!request.clientIp) {
      throw new BadRequestException('Client IP is required');
    }
  }

  private generateMerchantTxnRef(): string {
    const timestamp = Date.now();
    const random = Math.random().toString(36).substring(2, 8).toUpperCase();
    return `VNPAY${timestamp}${random}`;
  }

  private async processSuccessfulPayment(
    transaction: VnpayTransaction, 
    callback: PaymentCallback
  ): Promise<void> {
    // Update transaction status
    await this.vnpayTransactionRepository.updateStatus(
      transaction.id,
      VnpayTransactionStatus.SUCCESS,
      {
        vnpayTxnNo: callback.vnpayTxnNo,
        vnpayResponseCode: callback.responseCode,
        vnpayTransactionStatus: callback.transactionStatus,
        vnpayPayDate: callback.payDate,
        bankCode: callback.bankCode,
        cardType: callback.cardType,
      }
    );

    // Emit success event
    this.eventEmitter.emit('vnpay.payment.success', {
      merchantTxnRef: transaction.merchantTxnRef,
      externalRef: transaction.externalRef,
      amount: transaction.amount,
      vnpayTxnRef: callback.vnpayTxnRef,
      vnpayTxnNo: callback.vnpayTxnNo,
      bankCode: callback.bankCode,
      payDate: callback.payDate,
    });

    // Call external handler
    if (this.eventHandler?.onPaymentSuccess) {
      await this.eventHandler.onPaymentSuccess({
        merchantTxnRef: transaction.merchantTxnRef,
        externalRef: transaction.externalRef,
        amount: transaction.amount,
        vnpayTxnRef: callback.vnpayTxnRef!,
        vnpayTxnNo: callback.vnpayTxnNo!,
        bankCode: callback.bankCode,
        payDate: callback.payDate!,
      });
    }

    this.logger.log(`Payment processed successfully: ${transaction.merchantTxnRef}`);
  }

  private async processFailedPayment(
    transaction: VnpayTransaction, 
    callback: PaymentCallback
  ): Promise<void> {
    // Update transaction status
    await this.vnpayTransactionRepository.updateStatus(
      transaction.id,
      VnpayTransactionStatus.FAILED,
      {
        vnpayResponseCode: callback.responseCode,
        vnpayTransactionStatus: callback.transactionStatus,
        errorMessage: callback.message,
      }
    );

    // Emit failed event
    this.eventEmitter.emit('vnpay.payment.failed', {
      merchantTxnRef: transaction.merchantTxnRef,
      externalRef: transaction.externalRef,
      amount: transaction.amount,
      responseCode: callback.responseCode,
      message: callback.message,
    });

    // Call external handler
    if (this.eventHandler?.onPaymentFailed) {
      await this.eventHandler.onPaymentFailed({
        merchantTxnRef: transaction.merchantTxnRef,
        externalRef: transaction.externalRef,
        amount: transaction.amount,
        responseCode: callback.responseCode!,
        message: callback.message!,
      });
    }

    this.logger.log(`Payment failed: ${transaction.merchantTxnRef} - ${callback.message}`);
  }

  private createFailedCallback(params: Record<string, string>, message: string): PaymentCallback {
    return {
      isValid: false,
      isSuccess: false,
      merchantTxnRef: '',
      amount: 0,
      message,
      externalRef: '',
      rawData: params,
    };
  }
}

import { Repository, DataSource } from 'typeorm';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { BaseCmsMenusService } from './base.cms-menus.service';
import { CmsMenus, CmsMenuStatus } from '../entity/cms-menus.entity';
import { CmsMenuDto } from '../dto/cms-menu.dto';
import { CustomPaginationQueryDto } from '../../common/dto/custom-pagination-query.dto';
export declare class ReadCmsMenusService extends BaseCmsMenusService {
    protected readonly menuRepository: Repository<CmsMenus>;
    protected readonly dataSource: DataSource;
    protected readonly eventEmitter: EventEmitter2;
    constructor(menuRepository: Repository<CmsMenus>, dataSource: DataSource, eventEmitter: EventEmitter2);
    findAll(params: CustomPaginationQueryDto): Promise<{
        data: CmsMenuDto[];
        total: number;
    }>;
    findOneOrFail(id: string, relations?: string[]): Promise<CmsMenuDto | null>;
    findBySlugPublic(slug: string): Promise<CmsMenuDto | null>;
    findByPostType(postType: string, params: CustomPaginationQueryDto): Promise<{
        data: CmsMenuDto[];
        total: number;
    }>;
    findByStatus(status: CmsMenuStatus, params: CustomPaginationQueryDto): Promise<{
        data: CmsMenuDto[];
        total: number;
    }>;
    search(keyword: string, params: CustomPaginationQueryDto): Promise<{
        data: CmsMenuDto[];
        total: number;
    }>;
    findDeleted(params: CustomPaginationQueryDto): Promise<{
        data: CmsMenuDto[];
        total: number;
    }>;
    count(filter?: string): Promise<number>;
}

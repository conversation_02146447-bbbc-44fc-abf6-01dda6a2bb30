import { User } from "@/components/common/admin/users/type/user";
import { Token } from "../../tokens/type/token";
import { EcomProduct } from "../../ecom-products/type/ecom-product";
import { OrderTokenDetail } from "@/components/common/user/order-books/type/order-books";

/**
 * Enum đại diện cho loại lệnh (OrderType) từ backend
 * Đại diện cho hướng của lệnh (mua/bán/rút)
 */
export enum OrderType {
    BUY = 'BUY',
    SELL = 'SELL',
    WITHDRAWAL = 'WITHDRAWAL',
}

/**
 * Enum đại diện cho trạng thái của lệnh (OrderStatus) từ backend
 */
export enum OrderStatus {
    COMPLETED = 'COMPLETED',
    DEPOSITED = 'DEPOSITED',
    TERMINATED = 'TERMINATED',
    CANCELLED = 'CANCELLED',
}

/**
 * Enum đại diện cho loại hình kinh doanh (BusinessType) từ backend
 */
export enum BusinessType {
    NORMAL = 'NORMAL',
    IMMEDIATE_DELIVERY = 'IMMEDIATE_DELIVERY',
}

/**
 * Enum đại diện cho trạng thái phê duyệt (ApproveStatus) từ backend
 */
export enum ApproveStatus {
    PENDING = 'PENDING',
    APPROVED = 'APPROVED',
}

/**
 * Interface đại diện cho chi tiết token trong lệnh
 * Dựa trên OrderTokenDetailDto từ backend
 */
export interface OrderProductDetail {
    productId: string;
    price: number;
    quantity: number;
}

/**
 * Interface đại diện chi tiết trong sổ lệnh (OrderBookDetail) từ backend
 */
export interface OrderBookDetail {
    id: string;
    orderBookId: string;
    productId: string;
    price: number;
    quantity: number;
    totalPrice: number;
    createdAt: string;
    updatedAt: string;
    createdBy?: string;
    updatedBy?: string;
    product?: EcomProduct; 
}

/**
 * Interface đại diện cho sổ lệnh (OrderBook) từ backend
 */
export interface OrderBook {
    id: string;
    userId: string;
    orderType: OrderType;
    totalPrice?: number;
    status: OrderStatus;
    businessType?: BusinessType;
    depositPrice?: number;
    processingPrice?: number;
    settlementPrice?: number;
    contractNumber?: string;
    storageFee?: number;
    settlementDeadline?: Date;
    settlementAt?: Date;
    approveStatus?: ApproveStatus;
    approvedAt?: Date;
    createdAt: string;
    updatedAt: string;
    createdBy?: string;
    updatedBy?: string;
    isDeleted: boolean;
    deletedBy?: string;
    user?: User;
    creator?: User;
    updater?: User;
    deleter?: User;
    details: OrderBookDetail[];
}

/**
 * Interface cho dữ liệu tạo lệnh mới
 * Dựa trên CreateOrderBookDto từ backend
 */
export interface CreateOrderBookDto {
    userId: string;
    orderType: OrderType;
    businessType: BusinessType;
    products: OrderProductDetail[];
    depositPrice: number;
    settlementPrice?: number;
    contractNumber?: string;
    storageFee: number;
    settlementDeadline?: Date;
    createdBy: string;
}

/**
 * Interface cho dữ liệu cập nhật lệnh
 * Dựa trên UpdateOrderBookDto từ backend
 */
export interface UpdateOrderBookDto {
    id?: string;
    userId?: string;
    orderType?: OrderType;
    totalPrice?: number;
    status?: OrderStatus;
    businessType?: BusinessType;
    depositPrice?: number;
    processingPrice?: number;
    settlementPrice?: number;
    contractNumber?: string;
    storageFee?: number;
    settlementDeadline?: Date;
    settlementAt?: Date;
    products?: OrderProductDetail[];
    updatedBy?: string;
}

/**
 * Interface cho bộ lọc sổ lệnh
 */
export interface OrderBookFilter {
    userId?: string;
    productId?: string;
    orderType?: OrderType;
    status?: OrderStatus;
    dateFrom?: Date;
    dateTo?: Date;
    contractNumber?: string;
}

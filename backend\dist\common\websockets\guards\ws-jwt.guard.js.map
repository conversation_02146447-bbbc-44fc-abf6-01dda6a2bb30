{"version": 3, "file": "ws-jwt.guard.js", "sourceRoot": "", "sources": ["../../../../src/common/websockets/guards/ws-jwt.guard.ts"], "names": [], "mappings": ";;;;;;;;;;;;;AACA,2CAKwB;AAGxB,qCAAyC;AACzC,2CAA+C;AAKxC,IAAM,UAAU,kBAAhB,MAAM,UAAU;IAIX;IACA;IAJO,MAAM,GAAG,IAAI,eAAM,CAAC,YAAU,CAAC,IAAI,CAAC,CAAC;IAEtD,YACU,UAAsB,EACtB,aAA4B;QAD5B,eAAU,GAAV,UAAU,CAAY;QACtB,kBAAa,GAAb,aAAa,CAAe;IAGnC,CAAC;IAEJ,WAAW,CACT,OAAyB;QAEzB,MAAM,MAAM,GAAW,OAAO,CAAC,UAAU,EAAE,CAAC,SAAS,EAAU,CAAC;QAGhE,MAAM,KAAK,GAAG,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC;QAExC,IAAI,CAAC,KAAK,EAAE,CAAC;YACX,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,UAAU,MAAM,CAAC,EAAE,sBAAsB,CAAC,CAAC;YAE5D,IAAI,CAAC,gBAAgB,CAAC,MAAM,EAAE,kCAAkC,CAAC,CAAC;YAClE,OAAO,KAAK,CAAC;QACf,CAAC;QAED,IAAI,CAAC;YAEH,MAAM,OAAO,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM,CAAc,KAAK,EAAE;gBACzD,MAAM,EAAE,IAAI,CAAC,aAAa,CAAC,GAAG,CAAS,YAAY,CAAC;aACrD,CAAC,CAAC;YASH,MAAM,CAAC,IAAI,CAAC,IAAI,GAAG,OAAO,CAAC;YAG3B,MAAM,QAAQ,GAAG,QAAQ,OAAO,CAAC,MAAM,EAAE,CAAC;YAC1C,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YAEtB,IAAI,CAAC,MAAM,CAAC,OAAO,CACjB,uCAAuC,MAAM,CAAC,EAAE,aAAa,OAAO,CAAC,MAAM,WAAW,QAAQ,EAAE,CACjG,CAAC;YAEF,OAAO,IAAI,CAAC;QACd,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,IAAI,CACd,UAAU,MAAM,CAAC,EAAE,4BAA4B,KAAK,CAAC,OAAO,EAAE,CAC/D,CAAC;YACF,IAAI,CAAC,gBAAgB,CAAC,MAAM,EAAE,KAAK,CAAC,OAAO,IAAI,cAAc,CAAC,CAAC;YAC/D,OAAO,KAAK,CAAC;QAEf,CAAC;IACH,CAAC;IAGO,YAAY,CAAC,MAAc;QACjC,MAAM,UAAU,GAAG,MAAM,CAAC,SAAS,CAAC,OAAO,CAAC,aAAa,CAAC;QAC1D,IAAI,KAAK,GAAuB,SAAS,CAAC;QAE1C,IAAI,UAAU,IAAI,OAAO,UAAU,KAAK,QAAQ,EAAE,CAAC;YACjD,MAAM,KAAK,GAAG,UAAU,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;YACpC,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC,IAAI,KAAK,CAAC,CAAC,CAAC,KAAK,QAAQ,EAAE,CAAC;gBAChD,KAAK,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;YACnB,CAAC;QACH,CAAC;QAGD,IAAI,CAAC,KAAK,IAAI,MAAM,CAAC,SAAS,CAAC,IAAI,EAAE,CAAC;YACpC,KAAK,GAAG,MAAM,CAAC,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC;QACtC,CAAC;QAID,OAAO,KAAK,CAAC;IACf,CAAC;IAGO,gBAAgB,CAAC,MAAc,EAAE,OAAe;QACtD,MAAM,CAAC,IAAI,CAAC,OAAO,EAAE,EAAE,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,GAAG,EAAE,CAAC,CAAC;QACtD,MAAM,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;IAC1B,CAAC;CACF,CAAA;AAtFY,gCAAU;qBAAV,UAAU;IADtB,IAAA,mBAAU,GAAE;qCAKW,gBAAU;QACP,sBAAa;GAL3B,UAAU,CAsFtB"}
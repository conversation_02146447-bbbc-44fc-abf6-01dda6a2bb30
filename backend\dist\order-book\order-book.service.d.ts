import { Repository } from 'typeorm';
import { PaginationQueryDto } from '../common/dto/pagination-query.dto';
import { PaginationResponseDto } from '../common/dto/pagination-response.dto';
import { User } from '../users/entities/user.entity';
import { CreateOrderBookDto } from './dto/create-order-book.dto';
import { OrderBookDto } from './dto/order-book.dto';
import { UpdateOrderBookDto } from './dto/update-order-book.dto';
import { OrderBook } from './entities/order-book.entity';
import { CreateOrderBookService, DeleteOrderBookService, ReadOrderBookService, UpdateOrderBookService } from './services';
export declare class OrderBookService {
    private readonly orderBookRepository;
    private readonly createService;
    private readonly readService;
    private readonly updateService;
    private readonly deleteService;
    private readonly logger;
    private validRelations;
    constructor(orderBookRepository: Repository<OrderBook>, createService: CreateOrderBookService, readService: ReadOrderBookService, updateService: UpdateOrderBookService, deleteService: DeleteOrderBookService);
    private convertToDto;
    private convertToDtoList;
    private findById;
    create(createOrderBookDto: CreateOrderBookDto, userId: string): Promise<OrderBookDto>;
    bulkCreate(createOrderBookDtos: CreateOrderBookDto[], userId: string): Promise<OrderBookDto[]>;
    duplicate(id: string, userId: string): Promise<OrderBookDto>;
    findAll(params: PaginationQueryDto & {
        relations?: string[];
        filter?: string;
    }): Promise<PaginationResponseDto<OrderBookDto>>;
    search(user: User, keyword: string, params: PaginationQueryDto): Promise<PaginationResponseDto<OrderBookDto>>;
    count(filter?: string): Promise<number>;
    getStatistics(filter?: string): Promise<{
        total: number;
        businessTypeCounts: {
            NORMAL: number;
            IMMEDIATE_DELIVERY: number;
        };
    }>;
    findOne(id: string, relations?: string[]): Promise<OrderBookDto>;
    findDeleted(params: PaginationQueryDto): Promise<PaginationResponseDto<OrderBookDto>>;
    export(): Promise<any>;
    update(id: string, updateOrderBookDto: UpdateOrderBookDto, userId: string): Promise<OrderBookDto>;
    bulkUpdate(updateOrderBookDtos: ({
        id: string;
    } & UpdateOrderBookDto)[], userId: string): Promise<OrderBookDto[]>;
    toggleStatus(id: string, userId: string): Promise<OrderBookDto>;
    updateOrderStatusPending(orderBookId: string, userId: string): Promise<OrderBook>;
    updateOrderStatusCompleted(orderBookId: string, userId: string, settlementData?: any): Promise<OrderBook>;
    updateOrderStatusWaitPayment(orderBookId: string, userId: string): Promise<OrderBook>;
    remove(id: string): Promise<void>;
    bulkDelete(ids: string[]): Promise<{
        affected: number;
    }>;
    softDelete(id: string, userId: string): Promise<OrderBookDto>;
    bulkSoftDelete(ids: string[], userId: string): Promise<{
        affected: number;
        dtos: OrderBookDto[];
    }>;
    restore(id: string, userId: string): Promise<OrderBookDto>;
    cleanupOldRecords(days: number): Promise<number>;
    createTokenWithdrawal(createOrderBookDto: CreateOrderBookDto, userId: string): Promise<OrderBookDto>;
    updateOrderStatusCancelled(id: string, userId: string): Promise<OrderBook>;
    extendSettlement(id: string, userId: string): Promise<OrderBookDto>;
    approveOrder(id: string, userId: string): Promise<OrderBook>;
}

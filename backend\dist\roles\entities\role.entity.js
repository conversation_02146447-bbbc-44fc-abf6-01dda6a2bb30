"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.Role = void 0;
const openapi = require("@nestjs/swagger");
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
const typeorm_1 = require("typeorm");
const base_entity_1 = require("../../common/entities/base.entity");
const user_role_entity_1 = require("../../users/entities/user-role.entity");
const role_permission_entity_1 = require("./role-permission.entity");
let Role = class Role extends base_entity_1.BaseEntity {
    name;
    code;
    description;
    isPrimary;
    rolePermissions;
    userRoles;
    getEntityName() {
        return 'roles';
    }
    static _OPENAPI_METADATA_FACTORY() {
        return { name: { required: true, type: () => String, maxLength: 50 }, code: { required: true, type: () => String, maxLength: 50 }, description: { required: true, type: () => String, nullable: true }, isPrimary: { required: true, type: () => Boolean }, rolePermissions: { required: true, type: () => [require("./role-permission.entity").RolePermission] }, userRoles: { required: true, type: () => [require("../../users/entities/user-role.entity").UserRole] } };
    }
};
exports.Role = Role;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Tên định danh vai trò',
        example: 'ADMIN',
        maxLength: 50,
    }),
    (0, class_validator_1.IsNotEmpty)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MaxLength)(50),
    (0, typeorm_1.Column)({
        type: 'varchar',
        length: 50,
        unique: true,
        nullable: false,
        name: 'name',
    }),
    __metadata("design:type", String)
], Role.prototype, "name", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Mã định danh vai trò dùng để check quyền',
        example: 'ADMIN',
        maxLength: 50,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MaxLength)(50),
    (0, typeorm_1.Column)({
        type: 'varchar',
        length: 50,
        unique: true,
        nullable: true,
        name: 'code',
    }),
    __metadata("design:type", String)
], Role.prototype, "code", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Mô tả vai trò',
        example: 'Quản trị viên hệ thống',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, typeorm_1.Column)({ type: 'text', nullable: true, name: 'description' }),
    __metadata("design:type", Object)
], Role.prototype, "description", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Vai trò chính của hệ thống', example: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsBoolean)(),
    (0, typeorm_1.Column)({ type: 'boolean', default: false, name: 'is_primary' }),
    __metadata("design:type", Boolean)
], Role.prototype, "isPrimary", void 0);
__decorate([
    (0, typeorm_1.OneToMany)(() => role_permission_entity_1.RolePermission, (rolePermission) => rolePermission.role),
    (0, swagger_1.ApiProperty)({
        type: () => [role_permission_entity_1.RolePermission],
        description: 'Các quyền của vai trò',
    }),
    __metadata("design:type", Array)
], Role.prototype, "rolePermissions", void 0);
__decorate([
    (0, typeorm_1.OneToMany)(() => user_role_entity_1.UserRole, (userRole) => userRole.role),
    (0, swagger_1.ApiProperty)({
        type: () => [user_role_entity_1.UserRole],
        description: 'Các người dùng có vai trò này',
    }),
    __metadata("design:type", Array)
], Role.prototype, "userRoles", void 0);
exports.Role = Role = __decorate([
    (0, typeorm_1.Entity)('roles')
], Role);
//# sourceMappingURL=role.entity.js.map
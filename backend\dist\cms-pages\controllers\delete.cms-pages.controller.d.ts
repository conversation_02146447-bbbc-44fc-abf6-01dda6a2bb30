import { DeleteCmsPagesService } from '../services/delete.cms-pages.service';
import { CmsPageDto } from '../dto/cms-page.dto';
export declare class DeleteCmsPagesController {
    private readonly cmsPagesService;
    constructor(cmsPagesService: DeleteCmsPagesService);
    softDelete(id: string, userId: string): Promise<CmsPageDto | null>;
    restore(id: string, userId: string): Promise<CmsPageDto | null>;
    remove(id: string): Promise<{
        affected: number;
    }>;
    bulkSoftDelete(ids: string[], userId: string): Promise<CmsPageDto[]>;
    bulkRestore(ids: string[], userId: string): Promise<CmsPageDto[]>;
    bulkRemove(ids: string[]): Promise<{
        affected: number;
    }>;
}

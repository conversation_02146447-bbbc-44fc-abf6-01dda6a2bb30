"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UpdateUserDto = void 0;
const openapi = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
const swagger_1 = require("@nestjs/swagger");
const update_user_role_dto_1 = require("./update.user-role.dto");
const asset_dto_1 = require("../../token-assets/dto/asset.dto");
const class_transformer_1 = require("class-transformer");
class UpdateUserDto {
    id;
    username;
    email;
    password;
    passwordHash;
    fullName;
    phone;
    address;
    bio;
    birthday;
    isActive;
    emailVerified;
    verificationToken;
    verificationTokenExpiry;
    twoFaEnabled;
    notificationEmail;
    notificationSms;
    referralCode;
    referredByCode;
    referredById;
    parentId;
    path;
    phoneVerified;
    roles;
    tokenAssets;
    googleId;
    avatarUrl;
    version;
    static _OPENAPI_METADATA_FACTORY() {
        return { id: { required: true, type: () => String, format: "uuid" }, username: { required: false, type: () => String }, email: { required: false, type: () => String, format: "email" }, password: { required: false, type: () => String }, passwordHash: { required: false, type: () => String }, fullName: { required: false, type: () => String }, phone: { required: false, type: () => String }, address: { required: false, type: () => String }, bio: { required: false, type: () => String }, birthday: { required: false, type: () => Date }, isActive: { required: false, type: () => Boolean }, emailVerified: { required: false, type: () => Boolean }, verificationToken: { required: false, type: () => String }, verificationTokenExpiry: { required: false, type: () => Date }, twoFaEnabled: { required: false, type: () => Boolean }, notificationEmail: { required: false, type: () => Boolean }, notificationSms: { required: false, type: () => Boolean }, referralCode: { required: false, type: () => String }, referredByCode: { required: false, type: () => String }, referredById: { required: false, type: () => String, format: "uuid" }, parentId: { required: false, type: () => String, format: "uuid" }, path: { required: false, type: () => String }, phoneVerified: { required: false, type: () => Boolean }, roles: { required: false, type: () => [require("./update.user-role.dto").UpdateUserRoleDto] }, tokenAssets: { required: false, type: () => [require("../../token-assets/dto/asset.dto").AssetDto] }, googleId: { required: false, type: () => String }, avatarUrl: { required: false, type: () => String }, version: { required: false, type: () => Number } };
    }
}
exports.UpdateUserDto = UpdateUserDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Mã định danh duy nhất của người dùng' }),
    (0, class_validator_1.IsUUID)(),
    __metadata("design:type", String)
], UpdateUserDto.prototype, "id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Tên đăng nhập của người dùng', required: false }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], UpdateUserDto.prototype, "username", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Email của người dùng', required: false }),
    (0, class_validator_1.IsEmail)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], UpdateUserDto.prototype, "email", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Mật khẩu của người dùng', required: false }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], UpdateUserDto.prototype, "password", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Mật khẩu đã được mã hóa của người dùng', required: false }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], UpdateUserDto.prototype, "passwordHash", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Họ và tên đầy đủ của người dùng', required: false }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], UpdateUserDto.prototype, "fullName", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Số điện thoại của người dùng', required: false }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], UpdateUserDto.prototype, "phone", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Địa chỉ của người dùng', required: false }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], UpdateUserDto.prototype, "address", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Tiểu sử của người dùng', required: false }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], UpdateUserDto.prototype, "bio", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Ngày sinh của người dùng', required: false }),
    (0, class_validator_1.IsDate)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Date)
], UpdateUserDto.prototype, "birthday", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Trạng thái hoạt động của người dùng', required: false }),
    (0, class_validator_1.IsBoolean)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Boolean)
], UpdateUserDto.prototype, "isActive", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Email đã được xác minh hay chưa', required: false }),
    (0, class_validator_1.IsBoolean)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Boolean)
], UpdateUserDto.prototype, "emailVerified", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Token xác minh của người dùng',
        required: false,
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], UpdateUserDto.prototype, "verificationToken", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Thời gian hết hạn token xác minh của người dùng',
        required: false,
    }),
    (0, class_validator_1.IsDate)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Date)
], UpdateUserDto.prototype, "verificationTokenExpiry", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Có bật xác thực 2 yếu tố hay không', required: false }),
    (0, class_validator_1.IsBoolean)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Boolean)
], UpdateUserDto.prototype, "twoFaEnabled", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Có nhận thông báo qua email hay không', required: false }),
    (0, class_validator_1.IsBoolean)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Boolean)
], UpdateUserDto.prototype, "notificationEmail", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Có nhận thông báo qua SMS hay không', required: false }),
    (0, class_validator_1.IsBoolean)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Boolean)
], UpdateUserDto.prototype, "notificationSms", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Mã giới thiệu của người dùng', required: false }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], UpdateUserDto.prototype, "referralCode", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        example: 'REF123ABC',
        description: 'Mã giới thiệu của người giới thiệu',
        required: false
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], UpdateUserDto.prototype, "referredByCode", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'ID của người dùng cha (người giới thiệu)',
        required: false,
        example: '550e8400-e29b-41d4-a716-************',
    }),
    (0, class_validator_1.IsUUID)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], UpdateUserDto.prototype, "referredById", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'ID của người dùng cha (người giới thiệu)',
        required: false,
        example: '550e8400-e29b-41d4-a716-************',
    }),
    (0, class_validator_1.IsUUID)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], UpdateUserDto.prototype, "parentId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Đường dẫn biểu diễn cây phân cấp giới thiệu',
        required: false,
        example: '550e8400-e29b-41d4-a716-************.a1b2c3d4-e5f6-47g8-h9i0-j1k2l3m4n5o6',
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], UpdateUserDto.prototype, "path", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Số điện thoại đã được xác minh hay chưa', required: false }),
    (0, class_validator_1.IsBoolean)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Boolean)
], UpdateUserDto.prototype, "phoneVerified", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Các vai trò được gán cho người dùng', type: () => [update_user_role_dto_1.UpdateUserRoleDto], required: false }),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => update_user_role_dto_1.UpdateUserRoleDto),
    __metadata("design:type", Array)
], UpdateUserDto.prototype, "roles", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Tài sản token được gán cho người dùng',
        type: () => [asset_dto_1.AssetDto],
        required: false,
    }),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Array)
], UpdateUserDto.prototype, "tokenAssets", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Google ID cho xác thực OAuth', required: false }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], UpdateUserDto.prototype, "googleId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'URL ảnh đại diện', required: false }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], UpdateUserDto.prototype, "avatarUrl", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Phiên bản cho optimistic locking',
        required: false,
        example: 1
    }),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Number)
], UpdateUserDto.prototype, "version", void 0);
//# sourceMappingURL=update.user.dto.js.map
"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var SystemConfigReadController_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.SystemConfigReadController = void 0;
const openapi = require("@nestjs/swagger");
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const jwt_auth_guard_1 = require("../../common/guards/jwt-auth.guard");
const read_system_config_service_1 = require("../services/read.system-config.service");
const api_response_dto_1 = require("../../common/dto/api-response.dto");
const roles_decorator_1 = require("../../common/decorators/roles.decorator");
const permissions_decorator_1 = require("../../common/decorators/permissions.decorator");
const custom_pagination_query_dto_1 = require("../../common/dto/custom-pagination-query.dto");
const pagination_response_dto_1 = require("../../common/dto/pagination-response.dto");
const custom_pagination_meta_dto_1 = require("../../common/dto/custom-pagination-meta.dto");
let SystemConfigReadController = SystemConfigReadController_1 = class SystemConfigReadController {
    systemConfigService;
    logger = new common_1.Logger(SystemConfigReadController_1.name);
    constructor(systemConfigService) {
        this.systemConfigService = systemConfigService;
    }
    async findAll(paginationQuery) {
        this.logger.debug(`Đang lấy danh sách cấu hình hệ thống với tham số: ${JSON.stringify(paginationQuery)}`);
        const { data, total } = await this.systemConfigService.findAll(paginationQuery);
        const meta = new custom_pagination_meta_dto_1.CustomPageMetaDto({
            pageQueryDto: paginationQuery,
            itemCount: total,
        });
        return new pagination_response_dto_1.PaginationResponseDto(data, meta);
    }
    async search(paginationQuery) {
        const keyword = paginationQuery.search || '';
        this.logger.debug(`Đang tìm kiếm cấu hình hệ thống với từ khóa: ${keyword}`);
        const { data, total } = await this.systemConfigService.search(keyword, paginationQuery);
        const meta = new custom_pagination_meta_dto_1.CustomPageMetaDto({
            pageQueryDto: paginationQuery,
            itemCount: total,
        });
        return new pagination_response_dto_1.PaginationResponseDto(data, meta);
    }
    async findOne(id, relations) {
        this.logger.debug(`Đang lấy thông tin cấu hình hệ thống với ID: ${id}`);
        const relationsArray = relations ? relations.split(',') : [];
        return this.systemConfigService.findById(id, relationsArray);
    }
    async findByKey(key, relations) {
        this.logger.debug(`Đang lấy thông tin cấu hình hệ thống với khóa: ${key}`);
        const relationsArray = relations ? relations.split(',') : [];
        return this.systemConfigService.findByKey(key, relationsArray);
    }
    async findByGroup(group, paginationQuery) {
        this.logger.debug(`Đang lấy danh sách cấu hình hệ thống theo nhóm: ${group}`);
        const { data, total } = await this.systemConfigService.findByGroup(group, paginationQuery);
        const meta = new custom_pagination_meta_dto_1.CustomPageMetaDto({
            pageQueryDto: paginationQuery,
            itemCount: total,
        });
        return new pagination_response_dto_1.PaginationResponseDto(data, meta);
    }
    async getConfigGroups() {
        this.logger.debug('Đang lấy danh sách tất cả các nhóm cấu hình');
        return this.systemConfigService.findAllGroups();
    }
    async findDeleted(paginationQuery) {
        this.logger.debug(`Đang lấy danh sách cấu hình hệ thống đã xóa mềm với tham số: ${JSON.stringify(paginationQuery)}`);
        const { data, total } = await this.systemConfigService.findDeleted(paginationQuery);
        const meta = new custom_pagination_meta_dto_1.CustomPageMetaDto({
            pageQueryDto: paginationQuery,
            itemCount: total,
        });
        return new pagination_response_dto_1.PaginationResponseDto(data, meta);
    }
};
exports.SystemConfigReadController = SystemConfigReadController;
__decorate([
    (0, common_1.Get)(),
    (0, roles_decorator_1.Roles)('ADMIN'),
    (0, permissions_decorator_1.Permissions)('config:read'),
    (0, swagger_1.ApiOperation)({ summary: 'Lấy danh sách cấu hình hệ thống' }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Danh sách cấu hình hệ thống được lấy thành công.',
        type: api_response_dto_1.ApiResponseDto,
        schema: {
            properties: {
                data: {
                    type: 'object',
                    properties: {
                        data: {
                            type: 'array',
                            items: { $ref: '#/components/schemas/SystemConfigDto' },
                        },
                        meta: { $ref: '#/components/schemas/CustomPageMetaDto' },
                    },
                },
            },
        },
    }),
    openapi.ApiResponse({ status: 200 }),
    __param(0, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [custom_pagination_query_dto_1.CustomPaginationQueryDto]),
    __metadata("design:returntype", Promise)
], SystemConfigReadController.prototype, "findAll", null);
__decorate([
    (0, common_1.Get)('search'),
    (0, roles_decorator_1.Roles)('ADMIN'),
    (0, permissions_decorator_1.Permissions)('config:read'),
    (0, swagger_1.ApiOperation)({
        summary: 'Tìm kiếm cấu hình hệ thống',
        description: 'Tìm kiếm cấu hình hệ thống theo từ khóa. Sử dụng tham số search để cung cấp từ khóa tìm kiếm.'
    }),
    (0, swagger_1.ApiQuery)({
        name: 'search',
        required: false,
        type: String,
        description: 'Từ khóa tìm kiếm, có thể tìm theo khóa, giá trị, mô tả, v.v.'
    }),
    (0, swagger_1.ApiQuery)({
        name: 'page',
        required: false,
        type: Number,
        description: 'Số trang, mặc định là 1'
    }),
    (0, swagger_1.ApiQuery)({
        name: 'limit',
        required: false,
        type: Number,
        description: 'Số lượng kết quả trên mỗi trang, mặc định là 10'
    }),
    (0, swagger_1.ApiQuery)({
        name: 'sort',
        required: false,
        type: String,
        description: 'Sắp xếp kết quả, định dạng: field:direction (ví dụ: createdAt:DESC)'
    }),
    (0, swagger_1.ApiQuery)({
        name: 'filter',
        required: false,
        type: String,
        description: 'Lọc kết quả, định dạng: field:value (ví dụ: configGroup:general)'
    }),
    (0, swagger_1.ApiQuery)({
        name: 'relations',
        required: false,
        type: String,
        description: 'Danh sách các mối quan hệ cần tải, phân cách bằng dấu phẩy (ví dụ: creator,updater)'
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Danh sách cấu hình hệ thống phù hợp được lấy thành công.',
        type: api_response_dto_1.ApiResponseDto,
        schema: {
            properties: {
                data: {
                    type: 'object',
                    properties: {
                        data: {
                            type: 'array',
                            items: { $ref: '#/components/schemas/SystemConfigDto' },
                        },
                        meta: { $ref: '#/components/schemas/CustomPageMetaDto' },
                    },
                },
            },
        },
    }),
    openapi.ApiResponse({ status: 200 }),
    __param(0, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [custom_pagination_query_dto_1.CustomPaginationQueryDto]),
    __metadata("design:returntype", Promise)
], SystemConfigReadController.prototype, "search", null);
__decorate([
    (0, common_1.Get)(':id'),
    (0, roles_decorator_1.Roles)('ADMIN'),
    (0, permissions_decorator_1.Permissions)('config:read'),
    (0, swagger_1.ApiOperation)({ summary: 'Lấy thông tin cấu hình hệ thống theo ID' }),
    (0, swagger_1.ApiParam)({
        name: 'id',
        description: 'ID của cấu hình hệ thống cần lấy thông tin',
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Thông tin cấu hình hệ thống được lấy thành công.',
        type: api_response_dto_1.ApiResponseDto,
        schema: {
            properties: { data: { $ref: '#/components/schemas/SystemConfigDto' } },
        },
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.NOT_FOUND,
        description: 'Không tìm thấy cấu hình hệ thống.',
        type: api_response_dto_1.ApiResponseDto,
        schema: { properties: { data: { type: 'object', nullable: true } } },
    }),
    openapi.ApiResponse({ status: 200, type: require("../dto/system-config.dto").SystemConfigDto }),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __param(1, (0, common_1.Query)('relations')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String]),
    __metadata("design:returntype", Promise)
], SystemConfigReadController.prototype, "findOne", null);
__decorate([
    (0, common_1.Get)('key/:key'),
    (0, roles_decorator_1.Roles)('ADMIN'),
    (0, permissions_decorator_1.Permissions)('config:read'),
    (0, swagger_1.ApiOperation)({ summary: 'Lấy thông tin cấu hình hệ thống theo khóa' }),
    (0, swagger_1.ApiParam)({
        name: 'key',
        description: 'Khóa của cấu hình hệ thống cần lấy thông tin',
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Thông tin cấu hình hệ thống được lấy thành công.',
        type: api_response_dto_1.ApiResponseDto,
        schema: {
            properties: { data: { $ref: '#/components/schemas/SystemConfigDto' } },
        },
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.NOT_FOUND,
        description: 'Không tìm thấy cấu hình hệ thống.',
        type: api_response_dto_1.ApiResponseDto,
        schema: { properties: { data: { type: 'object', nullable: true } } },
    }),
    openapi.ApiResponse({ status: 200, type: require("../dto/system-config.dto").SystemConfigDto }),
    __param(0, (0, common_1.Param)('key')),
    __param(1, (0, common_1.Query)('relations')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String]),
    __metadata("design:returntype", Promise)
], SystemConfigReadController.prototype, "findByKey", null);
__decorate([
    (0, common_1.Get)('group/:group'),
    (0, roles_decorator_1.Roles)('ADMIN'),
    (0, permissions_decorator_1.Permissions)('config:read'),
    (0, swagger_1.ApiOperation)({ summary: 'Lấy danh sách cấu hình hệ thống theo nhóm' }),
    (0, swagger_1.ApiParam)({
        name: 'group',
        description: 'Nhóm cấu hình cần lấy danh sách',
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Danh sách cấu hình hệ thống theo nhóm được lấy thành công.',
        type: api_response_dto_1.ApiResponseDto,
        schema: {
            properties: {
                data: {
                    type: 'object',
                    properties: {
                        data: {
                            type: 'array',
                            items: { $ref: '#/components/schemas/SystemConfigDto' },
                        },
                        meta: { $ref: '#/components/schemas/CustomPageMetaDto' },
                    },
                },
            },
        },
    }),
    openapi.ApiResponse({ status: 200 }),
    __param(0, (0, common_1.Param)('group')),
    __param(1, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, custom_pagination_query_dto_1.CustomPaginationQueryDto]),
    __metadata("design:returntype", Promise)
], SystemConfigReadController.prototype, "findByGroup", null);
__decorate([
    (0, common_1.Get)('groups'),
    (0, roles_decorator_1.Roles)('ADMIN'),
    (0, permissions_decorator_1.Permissions)('config:read'),
    (0, swagger_1.ApiOperation)({ summary: 'Lấy danh sách tất cả các nhóm cấu hình' }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Danh sách các nhóm cấu hình được lấy thành công.',
        type: api_response_dto_1.ApiResponseDto,
        schema: {
            properties: {
                data: {
                    type: 'array',
                    items: { $ref: '#/components/schemas/SystemConfigDto' },
                },
            },
        },
    }),
    openapi.ApiResponse({ status: 200, type: [require("../dto/system-config.dto").SystemConfigDto] }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], SystemConfigReadController.prototype, "getConfigGroups", null);
__decorate([
    (0, common_1.Get)('deleted'),
    (0, roles_decorator_1.Roles)('ADMIN'),
    (0, permissions_decorator_1.Permissions)('config:read'),
    (0, swagger_1.ApiOperation)({ summary: 'Lấy danh sách cấu hình hệ thống đã xóa mềm' }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Danh sách cấu hình hệ thống đã xóa mềm được lấy thành công.',
        type: api_response_dto_1.ApiResponseDto,
        schema: {
            properties: {
                data: {
                    type: 'object',
                    properties: {
                        data: {
                            type: 'array',
                            items: { $ref: '#/components/schemas/SystemConfigDto' },
                        },
                        meta: { $ref: '#/components/schemas/CustomPageMetaDto' },
                    },
                },
            },
        },
    }),
    openapi.ApiResponse({ status: 200 }),
    __param(0, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [custom_pagination_query_dto_1.CustomPaginationQueryDto]),
    __metadata("design:returntype", Promise)
], SystemConfigReadController.prototype, "findDeleted", null);
exports.SystemConfigReadController = SystemConfigReadController = SystemConfigReadController_1 = __decorate([
    (0, swagger_1.ApiTags)('system-configs'),
    (0, swagger_1.ApiBearerAuth)(),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, common_1.Controller)('system-configs'),
    __metadata("design:paramtypes", [read_system_config_service_1.ReadSystemConfigService])
], SystemConfigReadController);
//# sourceMappingURL=system-config.read.controller.js.map
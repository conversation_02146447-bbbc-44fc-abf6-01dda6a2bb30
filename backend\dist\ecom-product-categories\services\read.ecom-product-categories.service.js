"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ReadEcomProductCategoriesService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const event_emitter_1 = require("@nestjs/event-emitter");
const base_ecom_product_categories_service_1 = require("./base.ecom-product-categories.service");
const ecom_product_categories_entity_1 = require("../entity/ecom-product-categories.entity");
let ReadEcomProductCategoriesService = class ReadEcomProductCategoriesService extends base_ecom_product_categories_service_1.BaseEcomProductCategoriesService {
    categoryRepository;
    dataSource;
    eventEmitter;
    shuffleArray(array) {
        if (!array || array.length <= 1) {
            return [...array];
        }
        const shuffled = [...array];
        for (let i = shuffled.length - 1; i > 0; i--) {
            const j = Math.floor(Math.random() * (i + 1));
            [shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]];
        }
        this.logger.debug(`Shuffled array from ${array.length} items`);
        return shuffled;
    }
    constructor(categoryRepository, dataSource, eventEmitter) {
        super(categoryRepository, dataSource, eventEmitter);
        this.categoryRepository = categoryRepository;
        this.dataSource = dataSource;
        this.eventEmitter = eventEmitter;
    }
    async findAll(params) {
        try {
            this.logger.debug(`Đang tìm tất cả danh mục sản phẩm với tham số: ${JSON.stringify(params)}`);
            const { limit, page, search } = params;
            const skip = (page - 1) * limit;
            let relationsArray = params.relationsArray || [];
            const requiredRelations = ['parent', 'children', 'creator', 'updater', 'deleter'];
            requiredRelations.forEach(relation => {
                if (!relationsArray.includes(relation)) {
                    relationsArray.push(relation);
                }
            });
            const validatedRelations = this.validateRelations(relationsArray);
            const queryBuilder = this.categoryRepository
                .createQueryBuilder('category')
                .where('category.isDeleted = :isDeleted', { isDeleted: false });
            if (search) {
                queryBuilder.andWhere(new typeorm_2.Brackets(qb => {
                    qb.where('LOWER(category.name) LIKE LOWER(:search)', { search: `%${search}%` })
                        .orWhere('LOWER(category.description) LIKE LOWER(:search)', { search: `%${search}%` });
                }));
            }
            if (params.filter) {
                const filters = params.filter.split(',');
                const filterGroups = {};
                filters.forEach(filterItem => {
                    const [field, value] = filterItem.split(':');
                    if (field && value) {
                        if (!filterGroups[field]) {
                            filterGroups[field] = [];
                        }
                        filterGroups[field].push(value);
                    }
                });
                Object.entries(filterGroups).forEach(([field, values]) => {
                    if (values.length === 1) {
                        queryBuilder.andWhere(`category.${field} = :${field}`, { [field]: values[0] });
                    }
                    else {
                        queryBuilder.andWhere(`category.${field} IN (:...${field})`, { [field]: values });
                    }
                });
            }
            validatedRelations.forEach(relation => {
                queryBuilder.leftJoinAndSelect(`category.${relation}`, relation);
            });
            const sortOptions = params.sortOptions || [];
            if (sortOptions.length > 0) {
                sortOptions.forEach((option, index) => {
                    if (option.field.includes('.')) {
                        const [relation, field] = option.field.split('.');
                        if (index === 0) {
                            queryBuilder.orderBy(`${relation}.${field}`, option.order);
                        }
                        else {
                            queryBuilder.addOrderBy(`${relation}.${field}`, option.order);
                        }
                    }
                    else {
                        if (index === 0) {
                            queryBuilder.orderBy(`category.${option.field}`, option.order);
                        }
                        else {
                            queryBuilder.addOrderBy(`category.${option.field}`, option.order);
                        }
                    }
                });
            }
            else {
                queryBuilder.orderBy('category.createdAt', 'DESC');
            }
            queryBuilder.skip(skip).take(limit);
            const [categories, total] = await queryBuilder.getManyAndCount();
            const categoryDtos = this.toDtos(categories);
            return {
                data: categoryDtos,
                total,
            };
        }
        catch (error) {
            this.logger.error(`Lỗi khi tìm tất cả danh mục sản phẩm: ${error.message}`, error.stack);
            if (error instanceof common_1.BadRequestException) {
                throw error;
            }
            throw new common_1.InternalServerErrorException(`Không thể lấy danh sách danh mục sản phẩm: ${error.message}`);
        }
    }
    async search(keyword, params) {
        try {
            this.logger.debug(`Đang tìm kiếm danh mục sản phẩm với từ khóa: ${keyword}`);
            const queryBuilder = this.categoryRepository
                .createQueryBuilder('category')
                .where('category.isDeleted = :isDeleted', { isDeleted: false });
            queryBuilder.andWhere(new typeorm_2.Brackets(qb => {
                qb.where('LOWER(category.name) LIKE LOWER(:keyword)', { keyword: `%${keyword}%` })
                    .orWhere('LOWER(category.description) LIKE LOWER(:keyword)', { keyword: `%${keyword}%` });
            }));
            let relationsArray = params.relationsArray || [];
            const requiredRelations = ['parent', 'children', 'creator', 'updater', 'deleter'];
            requiredRelations.forEach(relation => {
                if (!relationsArray.includes(relation)) {
                    relationsArray.push(relation);
                }
            });
            const validatedRelations = this.validateRelations(relationsArray);
            validatedRelations.forEach(relation => {
                queryBuilder.leftJoinAndSelect(`category.${relation}`, relation);
            });
            const sortOptions = params.sortOptions || [];
            if (sortOptions.length > 0) {
                sortOptions.forEach((option, index) => {
                    if (index === 0) {
                        queryBuilder.orderBy(`category.${option.field}`, option.order);
                    }
                    else {
                        queryBuilder.addOrderBy(`category.${option.field}`, option.order);
                    }
                });
            }
            else {
                queryBuilder.orderBy('category.createdAt', 'DESC');
            }
            queryBuilder.skip((params.page - 1) * params.limit).take(params.limit);
            const [categories, total] = await queryBuilder.getManyAndCount();
            const categoryDtos = this.toDtos(categories);
            return {
                data: categoryDtos,
                total,
            };
        }
        catch (error) {
            this.logger.error(`Lỗi khi tìm kiếm danh mục sản phẩm: ${error.message}`, error.stack);
            throw new common_1.InternalServerErrorException(`Không thể tìm kiếm danh mục sản phẩm: ${error.message}`);
        }
    }
    async count(filter) {
        try {
            const where = this.buildWhereClause(filter);
            return this.categoryRepository.count({ where });
        }
        catch (error) {
            this.logger.error(`Lỗi khi đếm số lượng danh mục sản phẩm: ${error.message}`, error.stack);
            throw new common_1.InternalServerErrorException(`Không thể đếm số lượng danh mục sản phẩm: ${error.message}`);
        }
    }
    async findOne(id, relations = []) {
        try {
            let relationsToLoad = [...relations];
            if (!relationsToLoad.includes('creator')) {
                relationsToLoad.push('creator');
            }
            if (!relationsToLoad.includes('updater')) {
                relationsToLoad.push('updater');
            }
            if (!relationsToLoad.includes('deleter')) {
                relationsToLoad.push('deleter');
            }
            const validRelationsToLoad = this.validateRelations(relationsToLoad);
            try {
                const category = await this.findById(id, validRelationsToLoad, false);
                if (!category) {
                    throw new common_1.NotFoundException(`Không tìm thấy danh mục với ID: ${id}`);
                }
                return this.toDto(category);
            }
            catch (notFoundError) {
                if (notFoundError instanceof common_1.NotFoundException) {
                    return null;
                }
                throw notFoundError;
            }
        }
        catch (error) {
            this.logger.error(`Lỗi khi tìm danh mục sản phẩm theo ID ${id}: ${error.message}`, error.stack);
            if (error instanceof common_1.NotFoundException) {
                return null;
            }
            throw new common_1.InternalServerErrorException(`Không thể lấy thông tin danh mục sản phẩm: ${error.message}`);
        }
    }
    async findOneOrFail(id, relations = []) {
        try {
            let relationsToLoad = [...relations];
            if (!relationsToLoad.includes('creator')) {
                relationsToLoad.push('creator');
            }
            if (!relationsToLoad.includes('updater')) {
                relationsToLoad.push('updater');
            }
            if (!relationsToLoad.includes('deleter')) {
                relationsToLoad.push('deleter');
            }
            const validRelationsToLoad = this.validateRelations(relationsToLoad);
            const category = await this.findById(id, validRelationsToLoad);
            if (!category) {
                throw new common_1.NotFoundException(`Không tìm thấy danh mục với ID: ${id}`);
            }
            return this.toDto(category);
        }
        catch (error) {
            this.logger.error(`Lỗi khi tìm danh mục sản phẩm theo ID ${id}: ${error.message}`, error.stack);
            if (error instanceof common_1.NotFoundException) {
                throw error;
            }
            throw new common_1.InternalServerErrorException(`Không thể lấy thông tin danh mục sản phẩm: ${error.message}`);
        }
    }
    async findDeleted(params) {
        try {
            this.logger.debug(`Đang tìm các danh mục sản phẩm đã bị xóa mềm với tham số: ${JSON.stringify(params)}`);
            const { limit, page } = params;
            let relationsArray = params.relationsArray || [];
            const requiredRelations = ['parent', 'children', 'creator', 'updater', 'deleter'];
            requiredRelations.forEach(relation => {
                if (!relationsArray.includes(relation)) {
                    relationsArray.push(relation);
                }
            });
            const validatedRelations = this.validateRelations(relationsArray);
            const queryBuilder = this.categoryRepository
                .createQueryBuilder('category')
                .where('category.isDeleted = :isDeleted', { isDeleted: true });
            if (params.search) {
                queryBuilder.andWhere(new typeorm_2.Brackets(qb => {
                    qb.where('LOWER(category.name) LIKE LOWER(:search)', { search: `%${params.search}%` })
                        .orWhere('LOWER(category.description) LIKE LOWER(:search)', { search: `%${params.search}%` });
                }));
            }
            validatedRelations.forEach(relation => {
                queryBuilder.leftJoinAndSelect(`category.${relation}`, relation);
            });
            const sortOptions = params.sortOptions || [];
            if (sortOptions.length > 0) {
                sortOptions.forEach((option, index) => {
                    if (index === 0) {
                        queryBuilder.orderBy(`category.${option.field}`, option.order);
                    }
                    else {
                        queryBuilder.addOrderBy(`category.${option.field}`, option.order);
                    }
                });
            }
            else {
                queryBuilder.orderBy('category.updatedAt', 'DESC');
            }
            queryBuilder.skip((page - 1) * limit).take(limit);
            const [categories, total] = await queryBuilder.getManyAndCount();
            const categoryDtos = this.toDtos(categories);
            return {
                data: categoryDtos,
                total,
            };
        }
        catch (error) {
            this.logger.error(`Lỗi khi tìm danh mục sản phẩm đã xóa: ${error.message}`, error.stack);
            throw new common_1.InternalServerErrorException(`Không thể lấy danh sách danh mục sản phẩm đã xóa: ${error.message}`);
        }
    }
    async getActiveProductCategories(params) {
        try {
            this.logger.debug(`Đang lấy danh mục sản phẩm đang hoạt động với tham số: ${JSON.stringify(params)}`);
            const { limit, page, search } = params;
            const skip = (page - 1) * limit;
            const queryBuilder = this.categoryRepository
                .createQueryBuilder('category')
                .where('category.isDeleted = :isDeleted', { isDeleted: false })
                .andWhere('category.isActive = :isActive', { isActive: true });
            if (search) {
                queryBuilder.andWhere(new typeorm_2.Brackets(qb => {
                    qb.where('LOWER(category.name) LIKE LOWER(:search)', { search: `%${search}%` })
                        .orWhere('LOWER(category.description) LIKE LOWER(:search)', { search: `%${search}%` });
                }));
            }
            const sortOptions = params.sortOptions;
            if (sortOptions && sortOptions.length > 0) {
                const { field, order } = sortOptions[0];
                queryBuilder.orderBy(`category.${field}`, order);
            }
            else {
                queryBuilder.orderBy('category.name', 'ASC');
            }
            queryBuilder.leftJoinAndSelect('category.parent', 'parent');
            queryBuilder.leftJoinAndSelect('category.children', 'children');
            queryBuilder.skip(skip).take(limit);
            const [categories, total] = await queryBuilder.getManyAndCount();
            const categoryDtos = this.toDtos(categories);
            return {
                data: categoryDtos,
                total,
            };
        }
        catch (error) {
            this.logger.error(`Lỗi khi lấy danh mục sản phẩm đang hoạt động: ${error.message}`, error.stack);
            throw new common_1.InternalServerErrorException(`Không thể lấy danh sách danh mục sản phẩm đang hoạt động: ${error.message}`);
        }
    }
    async getRandomizedActiveCategories(limit = 4) {
        try {
            this.logger.debug(`Đang lấy ${limit} danh mục sản phẩm đang hoạt động với thứ tự ngẫu nhiên`);
            const queryLimit = Math.max(limit * 3, 12);
            const queryBuilder = this.categoryRepository
                .createQueryBuilder('category')
                .where('category.isDeleted = :isDeleted', { isDeleted: false })
                .orderBy('category.createdAt', 'DESC')
                .limit(queryLimit);
            queryBuilder.leftJoinAndSelect('category.parent', 'parent');
            queryBuilder.leftJoinAndSelect('category.children', 'children');
            const categories = await queryBuilder.getMany();
            this.logger.debug(`Queried ${categories?.length || 0} categories for randomization`);
            categories.forEach((category, index) => {
                this.logger.debug(`Raw Category ${index}: id=${category.id}, name=${category.name}, isActive=${category.isActive}, isDeleted=${category.isDeleted}`);
            });
            let finalCategories = [];
            if (categories && categories.length > 0) {
                const allCategoryDtos = this.toDtos(categories);
                const shuffledCategories = this.shuffleArray(allCategoryDtos);
                finalCategories = shuffledCategories.slice(0, limit);
                this.logger.debug(`After randomization and limiting: ${finalCategories?.length || 0} categories`);
            }
            const totalActive = await this.categoryRepository.count({
                where: { isActive: true, isDeleted: false },
            });
            return {
                data: finalCategories,
                total: totalActive,
            };
        }
        catch (error) {
            this.logger.error(`Lỗi khi lấy danh mục sản phẩm ngẫu nhiên: ${error.message}`, error.stack);
            throw new common_1.InternalServerErrorException(`Không thể lấy danh sách danh mục sản phẩm ngẫu nhiên: ${error.message}`);
        }
    }
    async getStatistics() {
        try {
            const total = await this.categoryRepository.count({
                where: { isDeleted: false },
            });
            const activeCount = await this.categoryRepository.count({
                where: { isActive: true, isDeleted: false },
            });
            const inactiveCount = await this.categoryRepository.count({
                where: { isActive: false, isDeleted: false },
            });
            return {
                total,
                activeCounts: {
                    true: activeCount,
                    false: inactiveCount,
                },
            };
        }
        catch (error) {
            this.logger.error(`Lỗi khi lấy thống kê danh mục sản phẩm: ${error.message}`, error.stack);
            throw new common_1.InternalServerErrorException(`Không thể lấy thống kê danh mục sản phẩm: ${error.message}`);
        }
    }
};
exports.ReadEcomProductCategoriesService = ReadEcomProductCategoriesService;
exports.ReadEcomProductCategoriesService = ReadEcomProductCategoriesService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(ecom_product_categories_entity_1.EcomProductCategories)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        typeorm_2.DataSource,
        event_emitter_1.EventEmitter2])
], ReadEcomProductCategoriesService);
//# sourceMappingURL=read.ecom-product-categories.service.js.map
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.DeleteCmsCustomerFeedbacksController = exports.UpdateCmsCustomerFeedbacksController = exports.ReadCmsCustomerFeedbacksController = exports.CreateCmsCustomerFeedbacksController = void 0;
var create_cms_customer_feedbacks_controller_1 = require("./create.cms-customer-feedbacks.controller");
Object.defineProperty(exports, "CreateCmsCustomerFeedbacksController", { enumerable: true, get: function () { return create_cms_customer_feedbacks_controller_1.CreateCmsCustomerFeedbacksController; } });
var read_cms_customer_feedbacks_controller_1 = require("./read.cms-customer-feedbacks.controller");
Object.defineProperty(exports, "ReadCmsCustomerFeedbacksController", { enumerable: true, get: function () { return read_cms_customer_feedbacks_controller_1.ReadCmsCustomerFeedbacksController; } });
var update_cms_customer_feedbacks_controller_1 = require("./update.cms-customer-feedbacks.controller");
Object.defineProperty(exports, "UpdateCmsCustomerFeedbacksController", { enumerable: true, get: function () { return update_cms_customer_feedbacks_controller_1.UpdateCmsCustomerFeedbacksController; } });
var delete_cms_customer_feedbacks_controller_1 = require("./delete.cms-customer-feedbacks.controller");
Object.defineProperty(exports, "DeleteCmsCustomerFeedbacksController", { enumerable: true, get: function () { return delete_cms_customer_feedbacks_controller_1.DeleteCmsCustomerFeedbacksController; } });
//# sourceMappingURL=index.js.map
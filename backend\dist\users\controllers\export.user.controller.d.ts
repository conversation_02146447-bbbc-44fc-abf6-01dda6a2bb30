import { Response } from 'express';
import { ExportUserService } from '../services/export.user.service';
export declare class ExportUserController {
    private readonly exportUserService;
    constructor(exportUserService: ExportUserService);
    export(format?: 'csv' | 'json'): Promise<any>;
    exportStream(format: "csv" | "json" | undefined, batchSize: number | undefined, response: Response): Promise<void>;
    getExportEstimate(): Promise<{
        totalRecords: number;
        estimatedTimeSeconds: number;
        recommendedBatchSize: number;
        maxAllowedRecords: number;
    }>;
}

"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.BaseEcomProductsService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const event_emitter_1 = require("@nestjs/event-emitter");
const class_transformer_1 = require("class-transformer");
const ecom_products_entity_1 = require("../entity/ecom-products.entity");
const ecom_product_dto_1 = require("../dto/ecom-product.dto");
let BaseEcomProductsService = class BaseEcomProductsService {
    ecomProductRepository;
    dataSource;
    eventEmitter;
    logger = new common_1.Logger('EcomProductsService');
    validRelations = ['category', 'orderDetails', 'creator', 'updater', 'deleter'];
    EVENT_PRODUCT_CREATED = 'ecom-product.created';
    EVENT_PRODUCT_UPDATED = 'ecom-product.updated';
    EVENT_PRODUCT_DELETED = 'ecom-product.deleted';
    EVENT_PRODUCT_RESTORED = 'ecom-product.restored';
    constructor(ecomProductRepository, dataSource, eventEmitter) {
        this.ecomProductRepository = ecomProductRepository;
        this.dataSource = dataSource;
        this.eventEmitter = eventEmitter;
    }
    async getQueryRunner() {
        const queryRunner = this.dataSource.createQueryRunner();
        await queryRunner.connect();
        await queryRunner.startTransaction();
        return queryRunner;
    }
    validateRelations(relations) {
        return relations.filter(relation => this.validRelations.includes(relation));
    }
    async findByIdOrFail(id, relations = [], withDeleted = false) {
        const validatedRelations = this.validateRelations(relations);
        this.logger.debug(`Tìm sản phẩm với ID: ${id}, withDeleted: ${withDeleted}, relations: ${JSON.stringify(validatedRelations)}`);
        const product = await this.ecomProductRepository.findOne({
            where: { id },
            relations: validatedRelations,
            withDeleted: withDeleted,
        });
        if (!product) {
            throw new common_1.NotFoundException(`Không tìm thấy sản phẩm với ID "${id}"${withDeleted ? '' : ' hoặc đã bị xóa'}`);
        }
        return product;
    }
    toDto(product) {
        return (0, class_transformer_1.plainToInstance)(ecom_product_dto_1.EcomProductDto, product, {
            excludeExtraneousValues: true,
            enableImplicitConversion: true,
            exposeDefaultValues: true,
            exposeUnsetFields: false,
        });
    }
};
exports.BaseEcomProductsService = BaseEcomProductsService;
exports.BaseEcomProductsService = BaseEcomProductsService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(ecom_products_entity_1.EcomProduct)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        typeorm_2.DataSource,
        event_emitter_1.EventEmitter2])
], BaseEcomProductsService);
//# sourceMappingURL=base.ecom-products.service.js.map
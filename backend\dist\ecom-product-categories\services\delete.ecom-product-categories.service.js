"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.DeleteEcomProductCategoriesService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const event_emitter_1 = require("@nestjs/event-emitter");
const typeorm_transactional_1 = require("typeorm-transactional");
const base_ecom_product_categories_service_1 = require("./base.ecom-product-categories.service");
const ecom_product_categories_entity_1 = require("../entity/ecom-product-categories.entity");
let DeleteEcomProductCategoriesService = class DeleteEcomProductCategoriesService extends base_ecom_product_categories_service_1.BaseEcomProductCategoriesService {
    categoryRepository;
    dataSource;
    eventEmitter;
    constructor(categoryRepository, dataSource, eventEmitter) {
        super(categoryRepository, dataSource, eventEmitter);
        this.categoryRepository = categoryRepository;
        this.dataSource = dataSource;
        this.eventEmitter = eventEmitter;
    }
    async softDelete(id, userId) {
        try {
            this.logger.debug(`Đang xóa mềm danh mục sản phẩm với ID: ${id}`);
            const category = await this.findById(id, ['children', 'products']);
            if (!category) {
                throw new common_1.NotFoundException(`Không tìm thấy danh mục với ID: ${id}`);
            }
            if (category.children && category.children.length > 0) {
                throw new common_1.BadRequestException('Không thể xóa danh mục có danh mục con. Vui lòng xóa các danh mục con trước.');
            }
            if (category.products && category.products.length > 0) {
                throw new common_1.BadRequestException('Không thể xóa danh mục có sản phẩm liên quan. Vui lòng xóa các sản phẩm trước.');
            }
            const oldData = this.toDto(category);
            category.isDeleted = true;
            category.deletedBy = userId;
            category.deletedAt = new Date();
            const deletedCategory = await this.categoryRepository.save(category);
            const categoryDto = this.toDto(deletedCategory);
            if (!categoryDto) {
                throw new common_1.InternalServerErrorException('Không thể chuyển đổi entity thành DTO');
            }
            this.eventEmitter.emit(this.EVENT_CATEGORY_DELETED, {
                categoryId: categoryDto.id,
                userId,
                oldData,
                newData: categoryDto,
            });
            return categoryDto;
        }
        catch (error) {
            this.logger.error(`Lỗi khi xóa mềm danh mục sản phẩm: ${error.message}`, error.stack);
            if (error instanceof common_1.BadRequestException) {
                throw error;
            }
            throw new common_1.InternalServerErrorException(`Không thể xóa danh mục sản phẩm: ${error.message}`);
        }
    }
    async restore(id, userId) {
        try {
            this.logger.debug(`Đang khôi phục danh mục sản phẩm với ID: ${id}`);
            const category = await this.categoryRepository.findOne({
                where: { id, isDeleted: true },
            });
            if (!category) {
                throw new common_1.BadRequestException(`Không tìm thấy danh mục đã xóa với ID: ${id}`);
            }
            const oldData = this.toDto(category);
            category.isDeleted = false;
            category.deletedBy = null;
            category.deletedAt = null;
            category.updatedBy = userId;
            const restoredCategory = await this.categoryRepository.save(category);
            const categoryDto = this.toDto(restoredCategory);
            if (!categoryDto) {
                throw new common_1.InternalServerErrorException('Không thể chuyển đổi entity thành DTO');
            }
            this.eventEmitter.emit('ecom-product-category.restored', {
                categoryId: categoryDto.id,
                userId,
                oldData,
                newData: categoryDto,
            });
            return categoryDto;
        }
        catch (error) {
            this.logger.error(`Lỗi khi khôi phục danh mục sản phẩm: ${error.message}`, error.stack);
            if (error instanceof common_1.BadRequestException) {
                throw error;
            }
            throw new common_1.InternalServerErrorException(`Không thể khôi phục danh mục sản phẩm: ${error.message}`);
        }
    }
    async remove(id) {
        try {
            this.logger.debug(`Đang xóa vĩnh viễn danh mục sản phẩm với ID: ${id}`);
            const category = await this.findById(id, ['children', 'products']);
            if (!category) {
                throw new common_1.NotFoundException(`Không tìm thấy danh mục với ID: ${id}`);
            }
            if (category.children && category.children.length > 0) {
                throw new common_1.BadRequestException('Không thể xóa danh mục có danh mục con. Vui lòng xóa các danh mục con trước.');
            }
            if (category.products && category.products.length > 0) {
                throw new common_1.BadRequestException('Không thể xóa danh mục có sản phẩm liên quan. Vui lòng xóa các sản phẩm trước.');
            }
            const result = await this.categoryRepository.delete(id);
            if (result.affected === 0) {
                throw new common_1.InternalServerErrorException(`Không thể xóa danh mục sản phẩm với ID: ${id}`);
            }
            const affectedCount = result.affected !== undefined ? result.affected : 0;
            return { affected: affectedCount };
        }
        catch (error) {
            this.logger.error(`Lỗi khi xóa vĩnh viễn danh mục sản phẩm: ${error.message}`, error.stack);
            if (error instanceof common_1.BadRequestException) {
                throw error;
            }
            throw new common_1.InternalServerErrorException(`Không thể xóa vĩnh viễn danh mục sản phẩm: ${error.message}`);
        }
    }
};
exports.DeleteEcomProductCategoriesService = DeleteEcomProductCategoriesService;
__decorate([
    (0, typeorm_transactional_1.Transactional)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String]),
    __metadata("design:returntype", Promise)
], DeleteEcomProductCategoriesService.prototype, "softDelete", null);
__decorate([
    (0, typeorm_transactional_1.Transactional)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String]),
    __metadata("design:returntype", Promise)
], DeleteEcomProductCategoriesService.prototype, "restore", null);
__decorate([
    (0, typeorm_transactional_1.Transactional)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], DeleteEcomProductCategoriesService.prototype, "remove", null);
exports.DeleteEcomProductCategoriesService = DeleteEcomProductCategoriesService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(ecom_product_categories_entity_1.EcomProductCategories)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        typeorm_2.DataSource,
        event_emitter_1.EventEmitter2])
], DeleteEcomProductCategoriesService);
//# sourceMappingURL=delete.ecom-product-categories.service.js.map
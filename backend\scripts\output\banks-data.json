[{"brandName": "ABBANK", "fullName": "<PERSON><PERSON> hàng TMCP An Bình", "shortName": "ABBANK", "code": "ABB", "bin": "970425", "logoPath": "https://api.vietqr.io/img/ABB.png", "icon": "https://cdn.banklookup.net/assets/images/bank-icons/ABB.svg", "isActive": true, "metadata": {"id": "2d71f051-72fa-48bd-a362-27a08e8df3b9", "swiftCode": "ABBKVNVX", "lookupSupported": 1}}, {"brandName": "ACB", "fullName": "<PERSON>ân hàng TMCP Á Châu", "shortName": "ACB", "code": "ACB", "bin": "970416", "logoPath": "https://api.vietqr.io/img/ACB.png", "icon": "https://cdn.banklookup.net/assets/images/bank-icons/ACB.svg", "isActive": true, "metadata": {"id": "5b8bb807-6d1c-485b-986e-ad0fb2a6d4d2", "swiftCode": "ASCBVNVX", "lookupSupported": 1}}, {"brandName": "BacABank", "fullName": "<PERSON><PERSON> h<PERSON>ng TMCP Bắc Á", "shortName": "BacABank", "code": "BAB", "bin": "970409", "logoPath": "https://api.vietqr.io/img/BAB.png", "icon": "https://cdn.banklookup.net/assets/images/bank-icons/BAB.svg", "isActive": true, "metadata": {"id": "f599edf9-40c8-4bbf-87e8-230c1787a439", "swiftCode": "NASCVNVX", "lookupSupported": 1}}, {"brandName": "BIDV", "fullName": "<PERSON><PERSON> hàng TMCP <PERSON><PERSON><PERSON> tư và Phát triển Việt Nam", "shortName": "BIDV", "code": "BIDV", "bin": "970418", "logoPath": "https://api.vietqr.io/img/BIDV.png", "icon": "https://cdn.banklookup.net/assets/images/bank-icons/BIDV.svg", "isActive": true, "metadata": {"id": "f956e517-d4c1-43b1-bbbb-277bdc5f9037", "swiftCode": "BIDVVNVX", "lookupSupported": 1}}, {"brandName": "BaoVietBank", "fullName": "<PERSON><PERSON> h<PERSON>ng TMCP B<PERSON>o <PERSON>", "shortName": "BaoVietBank", "code": "BVB", "bin": "970438", "logoPath": "https://api.vietqr.io/img/BVB.png", "icon": "https://cdn.banklookup.net/assets/images/bank-icons/BVB.svg", "isActive": true, "metadata": {"id": "a4f2d7b2-7b85-4420-8273-3c5d0a69e8f1", "swiftCode": "BVBVVNVX", "lookupSupported": 1}}, {"brandName": "CAKE", "fullName": "TMCP Việt Nam Thịnh Vượng - <PERSON><PERSON> hàng số CAKE by VPBank", "shortName": "CAKE", "code": "CAKE", "bin": "546034", "logoPath": "https://api.vietqr.io/img/CAKE.png", "icon": "https://cdn.banklookup.net/assets/images/bank-icons/CAKE.svg", "isActive": true, "metadata": {"id": "66fa0378-a568-4ca0-b958-deb03de55ab4", "swiftCode": null, "lookupSupported": 1}}, {"brandName": "CBBank", "fullName": "<PERSON><PERSON> hàng Thương mại TNHH MTV Xây dựng Việt Nam", "shortName": "CBBank", "code": "CBB", "bin": "970444", "logoPath": "https://api.vietqr.io/img/CBB.png", "icon": "https://cdn.banklookup.net/assets/images/bank-icons/CBB.svg", "isActive": true, "metadata": {"id": "78d48899-8cf5-48d7-8572-645f43b0880d", "swiftCode": "GTBAVNVX", "lookupSupported": 1}}, {"brandName": "CIMB", "fullName": "<PERSON><PERSON> hàng TNHH MTV CIMB Việt Nam", "shortName": "CIMB", "code": "CIMB", "bin": "422589", "logoPath": "https://api.vietqr.io/img/CIMB.png", "icon": "https://cdn.banklookup.net/assets/images/bank-icons/CIMB.svg", "isActive": true, "metadata": {"id": "d4e933a0-e11e-470b-9634-30638e4dfa66", "swiftCode": "CIBBVNVN", "lookupSupported": 1}}, {"brandName": "Co-op Bank", "fullName": "<PERSON><PERSON> h<PERSON> tác xã Việt <PERSON>", "shortName": "Co-op Bank", "code": "COOPB", "bin": "970446", "logoPath": "https://api.vietqr.io/img/COOPBANK.png", "icon": "https://cdn.banklookup.net/assets/images/bank-icons/COOPBANK.svg", "isActive": true, "metadata": {"id": "1fe21f42-9b91-418c-a364-6e3190365009", "swiftCode": null, "lookupSupported": 1}}, {"brandName": "VikkiBank", "fullName": "<PERSON><PERSON> hàng TNHH MTV Số Vikki", "shortName": "VikkiBank", "code": "DAB", "bin": "970406", "logoPath": "https://ebanking.vikkibank.vn/khcn/resources/images/common/logo.png", "icon": "https://cdn.banklookup.net/assets/images/bank-icons/DOB.svg", "isActive": true, "metadata": {"id": "dc6920cb-8d1f-4393-a01d-db53d4b0e289", "swiftCode": "EACBVNVX", "lookupSupported": 1}}, {"brandName": "DBSBank", "fullName": "DBS Bank Ltd - <PERSON> Th<PERSON>nh p<PERSON>ố Hồ Chí Minh", "shortName": "DBSBank", "code": "DBS", "bin": "796500", "logoPath": "https://api.vietqr.io/img/DBS.png", "icon": "https://cdn.banklookup.net/assets/images/bank-icons/DBS.svg", "isActive": true, "metadata": {"id": "74664d52-e0fb-4c56-b157-7b7506b<PERSON><PERSON>", "swiftCode": "DBSSVNVX", "lookupSupported": 1}}, {"brandName": "Eximbank", "fullName": "<PERSON><PERSON> h<PERSON>ng TMCP <PERSON> Việt Nam", "shortName": "Eximbank", "code": "EIB", "bin": "970431", "logoPath": "https://api.vietqr.io/img/EIB.png", "icon": "https://cdn.banklookup.net/assets/images/bank-icons/EIB.svg", "isActive": true, "metadata": {"id": "017e81d5-2cb8-4cc6-8444-30e9479aeb9b", "swiftCode": "EBVIVNVX", "lookupSupported": 1}}, {"brandName": "GPBank", "fullName": "<PERSON><PERSON> hàng Thương mại TNHH MTV Dầu Khí Toàn Cầu", "shortName": "GPBank", "code": "GPB", "bin": "970408", "logoPath": "https://api.vietqr.io/img/GPB.png", "icon": "https://cdn.banklookup.net/assets/images/bank-icons/GPB.svg", "isActive": true, "metadata": {"id": "d935586a-92d0-423d-9623-a8567d668879", "swiftCode": "GBNKVNVX", "lookupSupported": 1}}, {"brandName": "HDBank", "fullName": "<PERSON><PERSON> hàng TMCP <PERSON><PERSON><PERSON> triển Thành phố <PERSON>", "shortName": "HDBank", "code": "HDB", "bin": "970437", "logoPath": "https://api.vietqr.io/img/HDB.png", "icon": "https://cdn.banklookup.net/assets/images/bank-icons/HDB.svg", "isActive": true, "metadata": {"id": "c8068add-c733-4f5e-a34b-c598669d9aee", "swiftCode": "HDBCVNVX", "lookupSupported": 1}}, {"brandName": "Hong Leong Bank", "fullName": "<PERSON><PERSON> hàng TNHH MTV Hong Leong Việt Nam", "shortName": "Hong Leong Bank", "code": "HLB", "bin": "970442", "logoPath": "https://api.vietqr.io/img/HLBVN.png", "icon": "https://cdn.banklookup.net/assets/images/bank-icons/HLBVN.svg", "isActive": true, "metadata": {"id": "6e0b0ed8-82b3-47fd-982a-fdae96b327a9", "swiftCode": "HLBBVNVX", "lookupSupported": 1}}, {"brandName": "HSBC", "fullName": "<PERSON><PERSON> hàng TNHH MTV HSBC (Việt Nam)", "shortName": "HSBC", "code": "HSBC", "bin": "458761", "logoPath": "https://api.vietqr.io/img/HSBC.png", "icon": "https://cdn.banklookup.net/assets/images/bank-icons/HSBC.svg", "isActive": true, "metadata": {"id": "e23cdeab-344b-4e2a-bc22-7cb2d4075166", "swiftCode": "HSBCVNVX", "lookupSupported": 1}}, {"brandName": "IBKHCM", "fullName": "Ngân hàng Công nghiệp Hàn Quốc - <PERSON> nhánh TP. Hồ Chí Minh", "shortName": "IBKHCM", "code": "IBKHCM", "bin": "970456", "logoPath": "https://api.vietqr.io/img/IBK.png", "icon": "https://cdn.banklookup.net/assets/images/bank-icons/IBK - HCM.svg", "isActive": true, "metadata": {"id": "47f8f83f-a86d-4d8f-aeab-213417b2f904", "swiftCode": null, "lookupSupported": 1}}, {"brandName": "IBKHN", "fullName": "Ngân hàng Công nghiệp Hàn Quốc - Chi nhánh Hà Nội", "shortName": "IBKHN", "code": "IBKHN", "bin": "970455", "logoPath": "https://api.vietqr.io/img/IBK.png", "icon": "https://cdn.banklookup.net/assets/images/bank-icons/IBK - HN.svg", "isActive": true, "metadata": {"id": "58f9e003-036e-4bbb-b476-8f513d5153d6", "swiftCode": null, "lookupSupported": 1}}, {"brandName": "Indovina Bank", "fullName": "<PERSON>ân hàng TNHH Indovina", "shortName": "Indovina Bank", "code": "IVB", "bin": "970434", "logoPath": "https://api.vietqr.io/img/IVB.png", "icon": "https://cdn.banklookup.net/assets/images/bank-icons/IVB.svg", "isActive": true, "metadata": {"id": "3fbc766d-9a2e-4d80-80cf-3e62ca32abbc", "swiftCode": null, "lookupSupported": 1}}, {"brandName": "Kasikornbank", "fullName": "Ngân hàng Đại chúng TNHH Kasikornbank", "shortName": "Kasikornbank", "code": "KB", "bin": "668888", "logoPath": "https://api.vietqr.io/img/KBANK.png", "icon": "https://cdn.banklookup.net/assets/images/bank-icons/KBank.svg", "isActive": true, "metadata": {"id": "712627bb-9ab3-4d9c-b300-b58bf0bb5d14", "swiftCode": "KASIVNVX", "lookupSupported": 1}}, {"brandName": "KookminHCM", "fullName": "<PERSON><PERSON> h<PERSON> - <PERSON>h<PERSON>h <PERSON>hành ph<PERSON> <PERSON>", "shortName": "KookminHCM", "code": "KBKHCM", "bin": "970463", "logoPath": "https://api.vietqr.io/img/KBHCM.png", "icon": "https://cdn.banklookup.net/assets/images/bank-icons/KBHCM.svg", "isActive": true, "metadata": {"id": "6ecbd4f5-6c85-4c07-ada4-5fa9eec49431", "swiftCode": null, "lookupSupported": 1}}, {"brandName": "KookminHN", "fullName": "<PERSON><PERSON> h<PERSON> - <PERSON>", "shortName": "KookminHN", "code": "KBKHN", "bin": "970462", "logoPath": "https://api.vietqr.io/img/KBHN.png", "icon": "https://cdn.banklookup.net/assets/images/bank-icons/KBHN.svg", "isActive": true, "metadata": {"id": "6ff67324-2611-4941-861b-abcf7b88bdee", "swiftCode": null, "lookupSupported": 1}}, {"brandName": "KienLongBank", "fullName": "<PERSON><PERSON> hàng TMCP Kiên Long", "shortName": "KienLongBank", "code": "KLB", "bin": "970452", "logoPath": "https://api.vietqr.io/img/KLB.png", "icon": "https://cdn.banklookup.net/assets/images/bank-icons/KLB.svg", "isActive": true, "metadata": {"id": "8892bfab-b050-40a4-b32b-73d11089578f", "swiftCode": "KLBKVNVX", "lookupSupported": 1}}, {"brandName": "LPBank", "fullName": "<PERSON><PERSON> h<PERSON>ng TMCP Lộc Phát Việt Nam", "shortName": "LPBank", "code": "LPB", "bin": "970449", "logoPath": "https://api.vietqr.io/img/LPB.png", "icon": "https://cdn.banklookup.net/assets/images/bank-icons/LPB.svg", "isActive": true, "metadata": {"id": "7d721f6b-504a-42b4-8405-41ca6830971d", "swiftCode": "LVBKVNVX", "lookupSupported": 1}}, {"brandName": "MBBank", "fullName": "<PERSON><PERSON> hàng TMCP <PERSON><PERSON><PERSON> đội", "shortName": "MBBank", "code": "MB", "bin": "970422", "logoPath": "https://api.vietqr.io/img/MB.png", "icon": "https://cdn.banklookup.net/assets/images/bank-icons/MB.svg", "isActive": true, "metadata": {"id": "fe05a74d-0b50-4569-8a78-d44648fc944c", "swiftCode": "MSCBVNVX", "lookupSupported": 1}}, {"brandName": "Oceanbank", "fullName": "<PERSON><PERSON> hàng TNHH MTV Việt Nam Hiện Đại", "shortName": "Oceanbank", "code": "MBV", "bin": "970414", "logoPath": "https://mkt-vc.1cdn.vn/2025/03/01/logo-mbv_cafef-600x375.jpg", "icon": "https://cdn.banklookup.net/assets/images/bank-icons/Oceanbank.svg", "isActive": true, "metadata": {"id": "d764fad1-d25a-40fa-8008-f7ce7ef2c029", "swiftCode": "OCBKUS3M", "lookupSupported": 1}}, {"brandName": "MSB", "fullName": "Ngân hàng TMCP Hàng Hải", "shortName": "MSB", "code": "MSB", "bin": "970426", "logoPath": "https://api.vietqr.io/img/MSB.png", "icon": "https://cdn.banklookup.net/assets/images/bank-icons/MSB.svg", "isActive": true, "metadata": {"id": "bf9303e0-4048-4545-92e6-0702f212d1a0", "swiftCode": "MCOBVNVX", "lookupSupported": 1}}, {"brandName": "NamABank", "fullName": "<PERSON><PERSON> hàng TMCP Nam Á", "shortName": "NamABank", "code": "NAB", "bin": "970428", "logoPath": "https://api.vietqr.io/img/NAB.png", "icon": "https://cdn.banklookup.net/assets/images/bank-icons/NAB.svg", "isActive": true, "metadata": {"id": "2a3b45ca-38bc-49cb-97c3-e1fdd8f389e9", "swiftCode": "NAMAVNVX", "lookupSupported": 1}}, {"brandName": "NCB", "fullName": "<PERSON><PERSON> hàng TMCP Quốc Dân", "shortName": "NCB", "code": "NCB", "bin": "970419", "logoPath": "https://api.vietqr.io/img/NCB.png", "icon": "https://cdn.banklookup.net/assets/images/bank-icons/NCB.svg", "isActive": true, "metadata": {"id": "428d3f77-7b99-40c5-bb07-80d34eb2d6a9", "swiftCode": "NVBAVNVX", "lookupSupported": 1}}, {"brandName": "<PERSON>gh<PERSON><PERSON>", "fullName": "<PERSON><PERSON> hàng <PERSON> - <PERSON>", "shortName": "<PERSON>gh<PERSON><PERSON>", "code": "NHB", "bin": "801011", "logoPath": "https://api.vietqr.io/img/NHB.png", "icon": "https://cdn.banklookup.net/assets/images/bank-icons/NHB HN.svg", "isActive": true, "metadata": {"id": "********-3ce0-4b6e-9b70-71004e97e4b8", "swiftCode": null, "lookupSupported": 1}}, {"brandName": "OCB", "fullName": "<PERSON><PERSON> h<PERSON>ng TMCP Phương Đông", "shortName": "OCB", "code": "OCB", "bin": "970448", "logoPath": "https://api.vietqr.io/img/OCB.png", "icon": "https://cdn.banklookup.net/assets/images/bank-icons/OCB.svg", "isActive": true, "metadata": {"id": "f7195395-0634-4ead-8773-88139abc8275", "swiftCode": "ORCOVNVX", "lookupSupported": 1}}, {"brandName": "PublicBank", "fullName": "<PERSON><PERSON> hàng TNHH MTV Public Việt Nam", "shortName": "PublicBank", "code": "PBVN", "bin": "970439", "logoPath": "https://api.vietqr.io/img/PBVN.png", "icon": "https://cdn.banklookup.net/assets/images/bank-icons/PBVN.svg", "isActive": true, "metadata": {"id": "60d111bf-c742-4a46-9990-5442c9ac44d8", "swiftCode": "VIDPVNVX", "lookupSupported": 1}}, {"brandName": "PGBank", "fullName": "<PERSON><PERSON> hàng TMCP Xăng dầu Petrolimex", "shortName": "PGBank", "code": "PGB", "bin": "970430", "logoPath": "https://api.vietqr.io/img/PGB.png", "icon": "https://cdn.banklookup.net/assets/images/bank-icons/PGB.svg", "isActive": true, "metadata": {"id": "06d27ed6-d640-4201-8f2e-ee5ff37f4580", "swiftCode": "PGBLVNVX", "lookupSupported": 1}}, {"brandName": "PVcomBank", "fullName": "Ngân hàng TMCP Đại Chúng Việt Nam", "shortName": "PVcomBank", "code": "PVCB", "bin": "970412", "logoPath": "https://api.vietqr.io/img/PVCB.png", "icon": "https://cdn.banklookup.net/assets/images/bank-icons/PVCB.svg", "isActive": true, "metadata": {"id": "df183cfc-c104-4ff7-9ab7-c164229376c1", "swiftCode": "WBVNVNVX", "lookupSupported": 1}}, {"brandName": "Sacombank", "fullName": "<PERSON>ân hàng TMCP Sài Gòn Thương Tín", "shortName": "Sacombank", "code": "SCB", "bin": "970403", "logoPath": "https://api.vietqr.io/img/STB.png", "icon": "https://cdn.banklookup.net/assets/images/bank-icons/STB.svg", "isActive": true, "metadata": {"id": "85954e77-0754-479b-aac1-15bd283ebc3e", "swiftCode": "SGTTVNVX", "lookupSupported": 1}}, {"brandName": "Standard Chartered VN", "fullName": "<PERSON><PERSON> hàng TNHH MTV Standard Chartered Bank Việt Nam", "shortName": "Standard Chartered VN", "code": "SCBVN", "bin": "970410", "logoPath": "https://api.vietqr.io/img/SCVN.png", "icon": "https://cdn.banklookup.net/assets/images/bank-icons/SCVN.svg", "isActive": true, "metadata": {"id": "793b6bfc-54c9-4012-83a4-ec5d0a4582c2", "swiftCode": "SCBLVNVX", "lookupSupported": 1}}, {"brandName": "SeABank", "fullName": "Ngân hàng TMCP Đông Nam Á", "shortName": "SeABank", "code": "SEAB", "bin": "970440", "logoPath": "https://api.vietqr.io/img/SEAB.png", "icon": "https://cdn.banklookup.net/assets/images/bank-icons/SEAB.svg", "isActive": true, "metadata": {"id": "b7af5470-9304-4878-80b4-f687c678f83b", "swiftCode": "SEAVVNVX", "lookupSupported": 1}}, {"brandName": "SaigonBank", "fullName": "<PERSON>ân hàng TMCP Sài Gòn Công Thương", "shortName": "SaigonBank", "code": "SGB", "bin": "970400", "logoPath": "https://api.vietqr.io/img/SGICB.png", "icon": "https://cdn.banklookup.net/assets/images/bank-icons/SGICB.svg", "isActive": true, "metadata": {"id": "c970732d-c1ed-4703-9f6c-b46fc84239b2", "swiftCode": "SBITVNVX", "lookupSupported": 1}}, {"brandName": "SCB", "fullName": "<PERSON><PERSON> hàng TMCP Sài Gòn", "shortName": "SCB", "code": "SGCB", "bin": "970429", "logoPath": "https://api.vietqr.io/img/SCB.png", "icon": "https://cdn.banklookup.net/assets/images/bank-icons/SCB.svg", "isActive": true, "metadata": {"id": "01ca8729-2519-46f2-8c4a-b109e7a45292", "swiftCode": "SACLVNVX", "lookupSupported": 1}}, {"brandName": "SHB", "fullName": "<PERSON><PERSON> hàng TMCP Sài Gòn - Hà Nội", "shortName": "SHB", "code": "SHB", "bin": "970443", "logoPath": "https://api.vietqr.io/img/SHB.png", "icon": "https://cdn.banklookup.net/assets/images/bank-icons/SHB.svg", "isActive": true, "metadata": {"id": "2fb52185-a3d3-4ed1-8c45-5f71a2842de1", "swiftCode": "SHBAVNVX", "lookupSupported": 1}}, {"brandName": "ShinhanBank", "fullName": "<PERSON><PERSON> hàng TNHH MTV Shinhan Việt Nam", "shortName": "ShinhanBank", "code": "SHBVN", "bin": "970424", "logoPath": "https://api.vietqr.io/img/SHBVN.png", "icon": "https://cdn.banklookup.net/assets/images/bank-icons/SHBVN.svg", "isActive": true, "metadata": {"id": "0540840b-7f2b-46c0-93d3-5040c042b737", "swiftCode": "SHBKVNVX", "lookupSupported": 1}}, {"brandName": "Shinhan Finance", "fullName": "<PERSON>ông ty Tài ch<PERSON>h TNHH Một thành viên <PERSON> Việt Nam", "shortName": "Shinhan Finance", "code": "SHFIN", "bin": "100001", "logoPath": "https://api.jobsnew.vn/public/company/coverImage_e19b9975-b19b-2228-ff4a-efa86c43a7bd_1686103724869-logo-ngang-chu-xanh-khong-nen-_website.webp", "icon": "https://www.shinhancard.com/pconts/company/images/contents/shc_symbol_ci.png", "isActive": false, "metadata": {"id": "39f3d997-94bb-477a-ab6c-0c67445192ac", "swiftCode": null, "lookupSupported": 0}}, {"brandName": "Techcombank", "fullName": "<PERSON><PERSON> hàng TMCP Kỹ thương Việt Nam", "shortName": "Techcombank", "code": "TCB", "bin": "970407", "logoPath": "https://api.vietqr.io/img/TCB.png", "icon": "https://cdn.banklookup.net/assets/images/bank-icons/TCB.svg", "isActive": true, "metadata": {"id": "d9b10c0e-e96d-4550-ac4d-1117d03c8afc", "swiftCode": "VTCBVNVX", "lookupSupported": 1}}, {"brandName": "<PERSON><PERSON>", "fullName": "<PERSON><PERSON> h<PERSON>ng s<PERSON>", "shortName": "<PERSON><PERSON>", "code": "TIMO", "bin": "963388", "logoPath": "https://vietqr.net/portal-service/resources/icons/TIMO.png", "icon": "https://cdn.banklookup.net/assets/images/bank-icons/TIMO.svg", "isActive": true, "metadata": {"id": "fa941344-6a64-4438-9bd5-11b57077da18", "swiftCode": null, "lookupSupported": 1}}, {"brandName": "TPBank", "fullName": "<PERSON><PERSON> h<PERSON>ng TMCP T<PERSON><PERSON><PERSON>", "shortName": "TPBank", "code": "TPB", "bin": "970423", "logoPath": "https://api.vietqr.io/img/TPB.png", "icon": "https://cdn.banklookup.net/assets/images/bank-icons/TPB.svg", "isActive": true, "metadata": {"id": "b468746c-1843-4c73-a0d9-70241c7cd8f6", "swiftCode": "TPBVVNVX", "lookupSupported": 1}}, {"brandName": "Ubank", "fullName": "TMCP Việt Nam Thịnh Vượng - <PERSON><PERSON> hàng số Ubank by VPBank", "shortName": "Ubank", "code": "UB", "bin": "546035", "logoPath": "https://api.vietqr.io/img/UBANK.png", "icon": "https://cdn.banklookup.net/assets/images/bank-icons/Ubank.svg", "isActive": true, "metadata": {"id": "7b2a7fe7-5477-4b62-a9dc-93e2ba899fe0", "swiftCode": null, "lookupSupported": 1}}, {"brandName": "United Overseas Bank", "fullName": "Ngân hàng United Overseas", "shortName": "United Overseas Bank", "code": "UOB", "bin": "970458", "logoPath": "https://api.vietqr.io/img/UOB.png", "icon": "https://cdn.banklookup.net/assets/images/bank-icons/UOB.svg", "isActive": true, "metadata": {"id": "913a0253-2a29-4918-8ed3-755162a73c23", "swiftCode": null, "lookupSupported": 1}}, {"brandName": "VietABank", "fullName": "<PERSON><PERSON> hàng TMCP Việt Á", "shortName": "VietABank", "code": "VAB", "bin": "970427", "logoPath": "https://api.vietqr.io/img/VAB.png", "icon": "https://cdn.banklookup.net/assets/images/bank-icons/VAB.svg", "isActive": true, "metadata": {"id": "dd586b22-8367-4439-95c8-98423a762ced", "swiftCode": "VNACVNVX", "lookupSupported": 1}}, {"brandName": "Agribank", "fullName": "<PERSON><PERSON> hàng Nông nghiệp và Phát triển Nông thôn Việt Nam", "shortName": "Agribank", "code": "VARB", "bin": "970405", "logoPath": "https://api.vietqr.io/img/VBA.png", "icon": "https://cdn.banklookup.net/assets/images/bank-icons/VBA.svg", "isActive": true, "metadata": {"id": "3cdde0d0-44f0-4cd4-9953-391fee3ec04e", "swiftCode": "VBAAVNVX", "lookupSupported": 1}}, {"brandName": "VietBank", "fullName": "<PERSON><PERSON> hàng TMCP Việt Nam Thương Tín", "shortName": "VietBank", "code": "VB", "bin": "970433", "logoPath": "https://api.vietqr.io/img/VIETBANK.png", "icon": "https://cdn.banklookup.net/assets/images/bank-icons/VIETBANK.svg", "isActive": true, "metadata": {"id": "bf313f9e-c658-46e3-8d0b-530e013bd76d", "swiftCode": "VNTTVNVX", "lookupSupported": 1}}, {"brandName": "Vietcombank", "fullName": "Ngân hàng TMCP Ngoại Thương Việt Nam", "shortName": "Vietcombank", "code": "VCB", "bin": "970436", "logoPath": "https://api.vietqr.io/img/VCB.png", "icon": "https://cdn.banklookup.net/assets/images/bank-icons/VCB.svg", "isActive": true, "metadata": {"id": "cc2fa1a9-6ad5-4cc8-aee7-84a8cde9f62e", "swiftCode": "BFTVVNVX", "lookupSupported": 1}}, {"brandName": "<PERSON><PERSON> h<PERSON>", "fullName": "Ngân hàng TMCP Bản Việt", "shortName": "<PERSON><PERSON> h<PERSON>", "code": "VCCB", "bin": "970454", "logoPath": "https://api.vietqr.io/img/VCCB.png", "icon": "https://cdn.banklookup.net/assets/images/bank-icons/VCCB.svg", "isActive": true, "metadata": {"id": "ec514a5b-1cd7-4d7c-bf97-c6c412bed60c", "swiftCode": "VCBCVNVX", "lookupSupported": 1}}, {"brandName": "VIB", "fullName": "Ngân hàng TMCP <PERSON> tế Việt Nam", "shortName": "VIB", "code": "VIB", "bin": "970441", "logoPath": "https://api.vietqr.io/img/VIB.png", "icon": "https://cdn.banklookup.net/assets/images/bank-icons/VIB.svg", "isActive": true, "metadata": {"id": "5e1b6cfa-d5a7-4aae-9e59-dccc008c4eef", "swiftCode": "VNIBVNVX", "lookupSupported": 1}}, {"brandName": "VNPTMoney", "fullName": "VNPT Money", "shortName": "VNPTMoney", "code": "VNPTMONEY", "bin": "971011", "logoPath": "https://api.vietqr.io/img/VNPTMONEY.png", "icon": "https://cdn.banklookup.net/assets/images/bank-icons/VNPTMONEY.svg", "isActive": false, "metadata": {"id": "69256b98-d6a2-46ea-a62e-90b02c8eac00", "swiftCode": null, "lookupSupported": 0}}, {"brandName": "VPBank", "fullName": "<PERSON><PERSON> hàng TMCP Việt Nam Thịnh Vượng", "shortName": "VPBank", "code": "VPB", "bin": "970432", "logoPath": "https://api.vietqr.io/img/VPB.png", "icon": "https://cdn.banklookup.net/assets/images/bank-icons/VPB.svg", "isActive": true, "metadata": {"id": "20351d8b-65ab-4cc4-90c2-b177d9c871b2", "swiftCode": "VPBKVNVX", "lookupSupported": 1}}, {"brandName": "VRB", "fullName": "<PERSON><PERSON> h<PERSON>ng <PERSON><PERSON>", "shortName": "VRB", "code": "VRB", "bin": "970421", "logoPath": "https://api.vietqr.io/img/VRB.png", "icon": "https://cdn.banklookup.net/assets/images/bank-icons/VRB.svg", "isActive": true, "metadata": {"id": "e01d061b-f468-4480-9078-0524083d3f09", "swiftCode": null, "lookupSupported": 1}}, {"brandName": "VietinBank", "fullName": "<PERSON><PERSON> hàng TMCP Công thương Việt Nam", "shortName": "VietinBank", "code": "VTB", "bin": "970415", "logoPath": "https://api.vietqr.io/img/ICB.png", "icon": "https://cdn.banklookup.net/assets/images/bank-icons/ICB.svg", "isActive": true, "metadata": {"id": "6fd6330a-2044-4254-9619-2e012c560b92", "swiftCode": "ICBVVNVX", "lookupSupported": 1}}, {"brandName": "ViettelMoney", "fullName": "Viettel Money", "shortName": "ViettelMoney", "code": "VTLMONEY", "bin": "971005", "logoPath": "https://api.vietqr.io/img/VIETTELMONEY.png", "icon": "https://cdn.banklookup.net/assets/images/bank-icons/VTLMONEY.svg", "isActive": true, "metadata": {"id": "c0da3fe8-c530-410b-97f9-4af1d0d5c1c5", "swiftCode": null, "lookupSupported": 1}}, {"brandName": "<PERSON><PERSON><PERSON>", "fullName": "<PERSON><PERSON> hàng TNHH MTV Woori Việt Nam", "shortName": "<PERSON><PERSON><PERSON>", "code": "WOO", "bin": "970457", "logoPath": "https://api.vietqr.io/img/WVN.png", "icon": "https://cdn.banklookup.net/assets/images/bank-icons/WVN.svg", "isActive": true, "metadata": {"id": "bc286db1-9f40-417b-a3d7-494153fbeb2d", "swiftCode": null, "lookupSupported": 1}}]
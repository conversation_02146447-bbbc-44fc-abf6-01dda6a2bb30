import { ReadEcomProductCategoriesService } from '../services/read.ecom-product-categories.service';
import { EcomProductCategoryDto } from '../dto/ecom-product-category.dto';
import { CustomPaginationQueryDto } from '../../common/dto/custom-pagination-query.dto';
import { PaginationResponseDto } from '../../common/dto/pagination-response.dto';
export declare class ReadEcomProductCategoriesController {
    private readonly ecomProductCategoriesService;
    constructor(ecomProductCategoriesService: ReadEcomProductCategoriesService);
    findAll(paginationQuery: CustomPaginationQueryDto): Promise<PaginationResponseDto<EcomProductCategoryDto>>;
    getStatistics(): Promise<{
        total: number;
        activeCounts: {
            true: number;
            false: number;
        };
    }>;
    count(filter?: string): Promise<number>;
    search(keyword: string, paginationQuery: CustomPaginationQueryDto): Promise<PaginationResponseDto<EcomProductCategoryDto>>;
    findDeleted(paginationQuery: CustomPaginationQueryDto): Promise<PaginationResponseDto<EcomProductCategoryDto>>;
    findById(id: string): Promise<EcomProductCategoryDto | null>;
}

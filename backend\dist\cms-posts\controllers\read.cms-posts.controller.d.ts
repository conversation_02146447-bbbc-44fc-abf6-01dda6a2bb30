import { ReadCmsPostsService } from '../services/read.cms-posts.service';
import { CmsPostDto } from '../dto/cms-post.dto';
import { CmsPostType, CmsPostStatus } from '../entity/cms-posts.entity';
import { CustomPaginationQueryDto } from '../../common/dto/custom-pagination-query.dto';
import { PaginationResponseDto } from '../../common/dto/pagination-response.dto';
export declare class ReadCmsPostsController {
    private readonly cmsPostsService;
    constructor(cmsPostsService: ReadCmsPostsService);
    findAll(paginationQuery: CustomPaginationQueryDto): Promise<PaginationResponseDto<CmsPostDto>>;
    findByPostType(postType: CmsPostType, paginationQuery: CustomPaginationQueryDto): Promise<PaginationResponseDto<CmsPostDto>>;
    findByStatus(status: CmsPostStatus, paginationQuery: CustomPaginationQueryDto): Promise<PaginationResponseDto<CmsPostDto>>;
    findPublished(paginationQuery: CustomPaginationQueryDto): Promise<PaginationResponseDto<CmsPostDto>>;
    findDrafts(paginationQuery: CustomPaginationQueryDto): Promise<PaginationResponseDto<CmsPostDto>>;
    getStatistics(): Promise<{
        total: number;
        statusCounts: Record<CmsPostStatus, number>;
        typeCounts: Record<CmsPostType, number>;
        publishedToday: number;
        totalViews: number;
    }>;
    count(filter?: string): Promise<number>;
    search(keyword: string, paginationQuery: CustomPaginationQueryDto): Promise<PaginationResponseDto<CmsPostDto>>;
    findDeleted(paginationQuery: CustomPaginationQueryDto): Promise<PaginationResponseDto<CmsPostDto>>;
    findOne(id: string, relations?: string): Promise<CmsPostDto | null>;
}

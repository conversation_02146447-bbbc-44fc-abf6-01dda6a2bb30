"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var TradingViewApiService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.TradingViewApiService = void 0;
const common_1 = require("@nestjs/common");
const config_1 = require("@nestjs/config");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const axios_1 = require("@nestjs/axios");
const rxjs_1 = require("rxjs");
const WebSocket = require("ws");
const system_config_entity_1 = require("../../../system-configs/entities/system-config.entity");
const silver_price_entity_1 = require("../entities/silver-price.entity");
const tradingview_data_model_1 = require("../models/tradingview-data.model");
const tradingview_websocket_service_1 = require("./tradingview-websocket.service");
const cache_service_1 = require("./cache.service");
let TradingViewApiService = TradingViewApiService_1 = class TradingViewApiService {
    configService;
    httpService;
    tradingViewWebsocketService;
    cacheService;
    systemConfigRepository;
    silverPriceRepository;
    logger = new common_1.Logger(TradingViewApiService_1.name);
    apiKey;
    apiSecret;
    baseUrl;
    wsEndpoint;
    wsClient;
    isConnected = false;
    reconnectAttempts = 0;
    maxReconnectAttempts = 5;
    reconnectInterval;
    dataPollingInterval;
    defaultSymbol = 'XAGUSD';
    constructor(configService, httpService, tradingViewWebsocketService, cacheService, systemConfigRepository, silverPriceRepository) {
        this.configService = configService;
        this.httpService = httpService;
        this.tradingViewWebsocketService = tradingViewWebsocketService;
        this.cacheService = cacheService;
        this.systemConfigRepository = systemConfigRepository;
        this.silverPriceRepository = silverPriceRepository;
    }
    async onModuleInit() {
        await this.loadConfig();
        await this.connectWebSocket();
        this.startDataPolling();
        this.logger.log('Dịch vụ TradingView API đã được khởi tạo');
    }
    async loadConfig() {
        try {
            const apiKeyConfig = await this.getOrCreateConfig(tradingview_data_model_1.TradingViewConfigKeys.API_KEY, this.configService.get('TRADINGVIEW_API_KEY', ''), 'Khóa API của TradingView');
            const apiSecretConfig = await this.getOrCreateConfig(tradingview_data_model_1.TradingViewConfigKeys.API_SECRET, this.configService.get('TRADINGVIEW_API_SECRET', ''), 'Khóa bí mật API của TradingView');
            const baseUrlConfig = await this.getOrCreateConfig(tradingview_data_model_1.TradingViewConfigKeys.BASE_URL, this.configService.get('TRADINGVIEW_BASE_URL', 'https://api.tradingview.com'), 'Đường dẫn cơ sở của TradingView API');
            const wsEndpointConfig = await this.getOrCreateConfig(tradingview_data_model_1.TradingViewConfigKeys.WS_ENDPOINT, this.configService.get('TRADINGVIEW_WS_ENDPOINT', 'wss://data.tradingview.com/socket.io/websocket'), 'Đường dẫn WebSocket của TradingView API');
            const defaultSymbolConfig = await this.getOrCreateConfig(tradingview_data_model_1.TradingViewConfigKeys.DEFAULT_SYMBOL, this.configService.get('TRADINGVIEW_DEFAULT_SYMBOL', 'XAGUSD'), 'Mã giao dịch mặc định cho TradingView API');
            this.apiKey = apiKeyConfig.configValue;
            this.apiSecret = apiSecretConfig.configValue;
            this.baseUrl = baseUrlConfig.configValue;
            this.wsEndpoint = wsEndpointConfig.configValue;
            this.defaultSymbol = defaultSymbolConfig.configValue;
            this.logger.log('Cấu hình TradingView API đã được tải');
        }
        catch (error) {
            this.logger.error(`Lỗi khi tải cấu hình TradingView API: ${error.message}`, error.stack);
            throw error;
        }
    }
    async getOrCreateConfig(key, defaultValue, description) {
        let config = await this.systemConfigRepository.findOne({
            where: { configKey: key, isDeleted: false },
        });
        if (!config) {
            config = this.systemConfigRepository.create({
                configKey: key,
                configValue: defaultValue,
                description: description,
                configGroup: 'tradingview',
                configType: 'text',
            });
            await this.systemConfigRepository.save(config);
        }
        return config;
    }
    async connectWebSocket() {
        try {
            this.wsClient = new WebSocket(this.wsEndpoint, {
                headers: {
                    'API-Key': this.apiKey,
                },
            });
            this.wsClient.on('open', () => {
                this.isConnected = true;
                this.reconnectAttempts = 0;
                this.logger.log('Đã kết nối đến WebSocket TradingView');
                this.subscribeToSymbol(this.defaultSymbol);
            });
            this.wsClient.on('message', (data) => {
                try {
                    const message = JSON.parse(data.toString());
                    this.handleWebSocketMessage(message);
                }
                catch (error) {
                    this.logger.error(`Lỗi khi phân tích tin nhắn WebSocket: ${error.message}`, error.stack);
                }
            });
            this.wsClient.on('error', (error) => {
                this.logger.error(`Lỗi WebSocket: ${error.message}`, error.stack);
            });
            this.wsClient.on('close', () => {
                this.isConnected = false;
                this.logger.warn('Đã ngắt kết nối từ WebSocket TradingView');
                this.attemptReconnect();
            });
        }
        catch (error) {
            this.logger.error(`Lỗi khi kết nối đến WebSocket TradingView: ${error.message}`, error.stack);
            this.attemptReconnect();
        }
    }
    attemptReconnect() {
        if (this.reconnectAttempts >= this.maxReconnectAttempts) {
            this.logger.error(`Đã đạt đến số lần thử kết nối lại tối đa (${this.maxReconnectAttempts}). Từ bỏ.`);
            return;
        }
        this.reconnectAttempts++;
        const delay = Math.pow(2, this.reconnectAttempts) * 1000;
        this.logger.log(`Đang cố gắng kết nối lại sau ${delay}ms (lần thử ${this.reconnectAttempts}/${this.maxReconnectAttempts})`);
        if (this.reconnectInterval) {
            clearTimeout(this.reconnectInterval);
        }
        this.reconnectInterval = setTimeout(() => {
            this.connectWebSocket();
        }, delay);
    }
    handleWebSocketMessage(message) {
        if (message.type === 'price_update' && message.symbol === this.defaultSymbol) {
            const priceData = {
                symbol: message.symbol,
                price: message.price,
                timestamp: message.timestamp || Date.now(),
                change: message.change || 0,
                changePercent: message.change_percent || 0,
            };
            this.cacheService.set(`price_${priceData.symbol}`, priceData, 60);
            this.broadcastPriceUpdate(priceData);
            this.savePriceData(priceData);
        }
    }
    subscribeToSymbol(symbol) {
        if (!this.isConnected) {
            this.logger.warn(`Không thể đăng ký ${symbol}: WebSocket chưa được kết nối`);
            return;
        }
        const subscribeMessage = {
            type: 'subscribe',
            symbol: symbol,
        };
        this.wsClient.send(JSON.stringify(subscribeMessage));
        this.logger.log(`Đã đăng ký ${symbol}`);
    }
    broadcastPriceUpdate(data) {
        const priceUpdate = {
            symbol: data.symbol,
            price: data.price,
            timestamp: data.timestamp,
            change: data.change,
            changePercent: data.changePercent,
        };
        this.tradingViewWebsocketService.broadcastPriceUpdate(priceUpdate);
    }
    async savePriceData(data) {
        try {
            const silverPrice = this.silverPriceRepository.create({
                symbol: data.symbol,
                price: data.price,
                change: data.change,
                changePercent: data.changePercent,
                timestamp: data.timestamp,
            });
            await this.silverPriceRepository.save(silverPrice);
            this.logger.debug(`Đã lưu dữ liệu giá cho ${data.symbol}: ${data.price}`);
        }
        catch (error) {
            this.logger.error(`Lỗi khi lưu dữ liệu giá: ${error.message}`, error.stack);
        }
    }
    async startDataPolling() {
        const pollingIntervalConfig = await this.getOrCreateConfig(tradingview_data_model_1.TradingViewConfigKeys.POLLING_INTERVAL, this.configService.get('TRADINGVIEW_POLLING_INTERVAL', '60000'), 'Khoảng thời gian (mili giây) giữa các lần lấy dữ liệu từ TradingView API');
        const pollingInterval = parseInt(pollingIntervalConfig.configValue, 10);
        this.dataPollingInterval = setInterval(async () => {
            try {
                await this.fetchLatestPrice();
            }
            catch (error) {
                this.logger.error(`Lỗi khi lấy dữ liệu định kỳ: ${error.message}`, error.stack);
            }
        }, pollingInterval);
        this.logger.log(`Đã bắt đầu lấy dữ liệu định kỳ với khoảng thời gian ${pollingInterval}ms`);
    }
    async fetchLatestPrice() {
        try {
            const cachedData = this.cacheService.get(`price_${this.defaultSymbol}`);
            if (cachedData) {
                return {
                    symbol: cachedData.symbol,
                    price: cachedData.price,
                    timestamp: cachedData.timestamp,
                    change: cachedData.change,
                    changePercent: cachedData.changePercent,
                };
            }
            const url = `${this.baseUrl}/quote?symbols=${this.defaultSymbol}`;
            const response = await (0, rxjs_1.firstValueFrom)(this.httpService.get(url, {
                headers: {
                    'API-Key': this.apiKey,
                    'Content-Type': 'application/json',
                },
            }));
            if (response.data && response.data.data && response.data.data.length > 0) {
                const quoteData = response.data.data[0];
                const priceData = {
                    symbol: quoteData.symbol,
                    price: quoteData.lp,
                    timestamp: Date.now(),
                    change: quoteData.chp || 0,
                    changePercent: quoteData.chp || 0,
                    bid: quoteData.bid,
                    ask: quoteData.ask,
                    spread: quoteData.ask - quoteData.bid,
                };
                this.cacheService.set(`price_${priceData.symbol}`, priceData, 60);
                this.tradingViewWebsocketService.broadcastPriceUpdate(priceData);
                await this.savePriceData(priceData);
                return priceData;
            }
            throw new Error('Không nhận được dữ liệu từ TradingView API');
        }
        catch (error) {
            this.logger.error(`Lỗi khi lấy giá mới nhất: ${error.message}`, error.stack);
            throw error;
        }
    }
};
exports.TradingViewApiService = TradingViewApiService;
exports.TradingViewApiService = TradingViewApiService = TradingViewApiService_1 = __decorate([
    (0, common_1.Injectable)(),
    __param(4, (0, typeorm_1.InjectRepository)(system_config_entity_1.SystemConfig)),
    __param(5, (0, typeorm_1.InjectRepository)(silver_price_entity_1.SilverPrice)),
    __metadata("design:paramtypes", [config_1.ConfigService,
        axios_1.HttpService,
        tradingview_websocket_service_1.TradingViewWebsocketService,
        cache_service_1.CacheService,
        typeorm_2.Repository,
        typeorm_2.Repository])
], TradingViewApiService);
//# sourceMappingURL=tradingview-api.service.js.map
import { CreateCmsCustomerFeedbacksService } from '../services/create.cms-customer-feedbacks.service';
import { CmsCustomerFeedbackDto } from '../dto/cms-customer-feedback.dto';
import { CreateCmsCustomerFeedbackDto } from '../dto/create.cms-customer-feedback.dto';
export declare class CreateCmsCustomerFeedbacksController {
    private readonly cmsCustomerFeedbacksService;
    constructor(cmsCustomerFeedbacksService: CreateCmsCustomerFeedbacksService);
    create(createCmsCustomerFeedbackDto: CreateCmsCustomerFeedbackDto, userId: string): Promise<CmsCustomerFeedbackDto>;
    bulkCreate(createCmsCustomerFeedbackDtos: CreateCmsCustomerFeedbackDto[], userId: string): Promise<CmsCustomerFeedbackDto[]>;
    createFromPublicForm(createCmsCustomerFeedbackDto: CreateCmsCustomerFeedbackDto): Promise<CmsCustomerFeedbackDto>;
    createWithRating(customerName: string, feedbackText: string, rating: number, productServiceName: string, userId: string): Promise<CmsCustomerFeedbackDto>;
    createTestimonial(createCmsCustomerFeedbackDto: CreateCmsCustomerFeedbackDto, userId: string): Promise<CmsCustomerFeedbackDto>;
    importFeedbacks(feedbackData: any[], userId: string): Promise<CmsCustomerFeedbackDto[]>;
}

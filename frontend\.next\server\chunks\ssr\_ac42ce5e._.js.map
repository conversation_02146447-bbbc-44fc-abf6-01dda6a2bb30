{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/bloomgoo/gold-exchange-platform/frontend/hooks/use-mobile.ts"], "sourcesContent": ["import * as React from \"react\"\r\n\r\nconst MO<PERSON>LE_BREAKPOINT = 768\r\n\r\nexport function useIsMobile() {\r\n  const [isMobile, setIsMobile] = React.useState<boolean | undefined>(undefined)\r\n\r\n  React.useEffect(() => {\r\n    const mql = window.matchMedia(`(max-width: ${MOBILE_BREAKPOINT - 1}px)`)\r\n    const onChange = () => {\r\n      setIsMobile(window.innerWidth < MOBILE_BREAKPOINT)\r\n    }\r\n    mql.addEventListener(\"change\", onChange)\r\n    setIsMobile(window.innerWidth < MOBILE_BREAKPOINT)\r\n    return () => mql.removeEventListener(\"change\", onChange)\r\n  }, [])\r\n\r\n  return !!isMobile\r\n}\r\n"], "names": [], "mappings": ";;;AAAA;;AAEA,MAAM,oBAAoB;AAEnB,SAAS;IACd,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,8SAAA,CAAA,WAAc,AAAD,EAAuB;IAEpE,CAAA,GAAA,8SAAA,CAAA,YAAe,AAAD,EAAE;QACd,MAAM,MAAM,OAAO,UAAU,CAAC,CAAC,YAAY,EAAE,oBAAoB,EAAE,GAAG,CAAC;QACvE,MAAM,WAAW;YACf,YAAY,OAAO,UAAU,GAAG;QAClC;QACA,IAAI,gBAAgB,CAAC,UAAU;QAC/B,YAAY,OAAO,UAAU,GAAG;QAChC,OAAO,IAAM,IAAI,mBAAmB,CAAC,UAAU;IACjD,GAAG,EAAE;IAEL,OAAO,CAAC,CAAC;AACX", "debugId": null}}, {"offset": {"line": 32, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/bloomgoo/gold-exchange-platform/frontend/lib/response.ts"], "sourcesContent": ["// API Response DTO\r\nexport interface ApiResponse<T> {\r\n  success: boolean;\r\n  statusCode: number;\r\n  message: string;\r\n  data?: T | T[] | null;\r\n  timestamp: string;\r\n  path?: string;\r\n}\r\n\r\n// Pagination Meta DTO\r\nexport interface PageMeta {\r\n  page: number;\r\n  limit: number;\r\n  itemCount: number;\r\n  pageCount: number;\r\n  hasPreviousPage: boolean;\r\n  hasNextPage: boolean;\r\n}\r\n\r\n// Pagination Query DTO\r\nexport enum SortOrder {\r\n  ASC = 'ASC',\r\n  DESC = 'DESC',\r\n}\r\n\r\nexport interface PaginationQuery {\r\n  page?: number;\r\n  limit?: number;\r\n  search?: string;\r\n  sortBy?: string;\r\n  sortOrder?: SortOrder;\r\n}\r\n\r\n// Pagination Response DTO\r\nexport interface PaginationResponse<T> {\r\n  data: T[];\r\n  meta: PageMeta;\r\n}\r\n\r\n// Helper function để tạo pagination query params\r\nexport const createPaginationQuery = (query: PaginationQuery): string => {\r\n  const params = new URLSearchParams();\r\n  \r\n  if (query.page) params.append('page', query.page.toString());\r\n  if (query.limit) params.append('limit', query.limit.toString());\r\n  if (query.search) params.append('search', query.search);\r\n  if (query.sortBy) params.append('sortBy', query.sortBy);\r\n  if (query.sortOrder) params.append('sortOrder', query.sortOrder);\r\n  \r\n  return params.toString();\r\n};\r\n\r\n// Helper function để parse response từ API\r\nexport const parseApiResponse = <T>(response: any): ApiResponse<T> => {\r\n  return {\r\n    success: response.success,\r\n    statusCode: response.statusCode,\r\n    message: response.message,\r\n    data: response.data,\r\n    timestamp: response.timestamp,\r\n    path: response.path,\r\n  };\r\n};\r\n\r\n// Helper function để parse pagination response\r\nexport const parsePaginationResponse = <T>(response: any): PaginationResponse<T> => {\r\n  return {\r\n    data: response.data,\r\n    meta: {\r\n      page: response.meta.page,\r\n      limit: response.meta.limit,\r\n      itemCount: response.meta.itemCount,\r\n      pageCount: response.meta.pageCount,\r\n      hasPreviousPage: response.meta.hasPreviousPage,\r\n      hasNextPage: response.meta.hasNextPage,\r\n    },\r\n  };\r\n};\r\n\r\n// Helper function để kiểm tra response có thành công không\r\nexport const isSuccessResponse = (response: ApiResponse<any>): boolean => {\r\n  return response.success === true && response.statusCode >= 200 && response.statusCode < 300;\r\n};\r\n\r\n// Helper function để lấy data từ response\r\nexport const getDataFromResponse = <T>(response: ApiResponse<T>): T | null => {\r\n  return response.data as T || null;\r\n};\r\n\r\n// Helper function để xử lý lỗi từ response\r\nexport const handleApiError = (error: any): string => {\r\n  if (error.response && error.response.data) {\r\n    const apiError = error.response.data as ApiResponse<any>;\r\n    return apiError.message || 'Có lỗi xảy ra khi gọi API';\r\n  }\r\n  \r\n  if (error.message) {\r\n    if (error.message.includes('NOT_FOUND')) {\r\n      return 'Không tìm thấy API endpoint. Vui lòng kiểm tra cấu hình API.';\r\n    } else if (error.message.includes('Network Error')) {\r\n      return 'Không thể kết nối đến máy chủ. Vui lòng kiểm tra kết nối mạng.';\r\n    }\r\n    return error.message;\r\n  }\r\n  \r\n  return 'Có lỗi xảy ra khi gọi API';\r\n};\r\n"], "names": [], "mappings": "AAAA,mBAAmB;;;;;;;;;;AAqBZ,IAAA,AAAK,mCAAA;;;WAAA;;AAoBL,MAAM,wBAAwB,CAAC;IACpC,MAAM,SAAS,IAAI;IAEnB,IAAI,MAAM,IAAI,EAAE,OAAO,MAAM,CAAC,QAAQ,MAAM,IAAI,CAAC,QAAQ;IACzD,IAAI,MAAM,KAAK,EAAE,OAAO,MAAM,CAAC,SAAS,MAAM,KAAK,CAAC,QAAQ;IAC5D,IAAI,MAAM,MAAM,EAAE,OAAO,MAAM,CAAC,UAAU,MAAM,MAAM;IACtD,IAAI,MAAM,MAAM,EAAE,OAAO,MAAM,CAAC,UAAU,MAAM,MAAM;IACtD,IAAI,MAAM,SAAS,EAAE,OAAO,MAAM,CAAC,aAAa,MAAM,SAAS;IAE/D,OAAO,OAAO,QAAQ;AACxB;AAGO,MAAM,mBAAmB,CAAI;IAClC,OAAO;QACL,SAAS,SAAS,OAAO;QACzB,YAAY,SAAS,UAAU;QAC/B,SAAS,SAAS,OAAO;QACzB,MAAM,SAAS,IAAI;QACnB,WAAW,SAAS,SAAS;QAC7B,MAAM,SAAS,IAAI;IACrB;AACF;AAGO,MAAM,0BAA0B,CAAI;IACzC,OAAO;QACL,MAAM,SAAS,IAAI;QACnB,MAAM;YACJ,MAAM,SAAS,IAAI,CAAC,IAAI;YACxB,OAAO,SAAS,IAAI,CAAC,KAAK;YAC1B,WAAW,SAAS,IAAI,CAAC,SAAS;YAClC,WAAW,SAAS,IAAI,CAAC,SAAS;YAClC,iBAAiB,SAAS,IAAI,CAAC,eAAe;YAC9C,aAAa,SAAS,IAAI,CAAC,WAAW;QACxC;IACF;AACF;AAGO,MAAM,oBAAoB,CAAC;IAChC,OAAO,SAAS,OAAO,KAAK,QAAQ,SAAS,UAAU,IAAI,OAAO,SAAS,UAAU,GAAG;AAC1F;AAGO,MAAM,sBAAsB,CAAI;IACrC,OAAO,SAAS,IAAI,IAAS;AAC/B;AAGO,MAAM,iBAAiB,CAAC;IAC7B,IAAI,MAAM,QAAQ,IAAI,MAAM,QAAQ,CAAC,IAAI,EAAE;QACzC,MAAM,WAAW,MAAM,QAAQ,CAAC,IAAI;QACpC,OAAO,SAAS,OAAO,IAAI;IAC7B;IAEA,IAAI,MAAM,OAAO,EAAE;QACjB,IAAI,MAAM,OAAO,CAAC,QAAQ,CAAC,cAAc;YACvC,OAAO;QACT,OAAO,IAAI,MAAM,OAAO,CAAC,QAAQ,CAAC,kBAAkB;YAClD,OAAO;QACT;QACA,OAAO,MAAM,OAAO;IACtB;IAEA,OAAO;AACT", "debugId": null}}]}
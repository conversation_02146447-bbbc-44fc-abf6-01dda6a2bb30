{"version": 3, "file": "tradingview-websocket.service.js", "sourceRoot": "", "sources": ["../../../../src/modules/tradingview/services/tradingview-websocket.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;;AAAA,2CAAmF;AACnF,mDAAiH;AACjH,yCAA2C;AAC3C,2CAA+C;AAC/C,6EAM0C;AAC1C,iEAA4D;AAUrD,IAAM,2BAA2B,mCAAjC,MAAM,2BAA2B;IAUnB;IACA;IATnB,MAAM,CAAS;IAEE,MAAM,GAAG,IAAI,eAAM,CAAC,6BAA2B,CAAC,IAAI,CAAC,CAAC;IAC/D,OAAO,GAAwB,IAAI,GAAG,EAAE,CAAC;IACzC,aAAa,GAA6B,IAAI,GAAG,EAAE,CAAC;IACpD,iBAAiB,CAAiB;IAE1C,YACmB,aAA4B,EAC5B,kBAAsC;QADtC,kBAAa,GAAb,aAAa,CAAe;QAC5B,uBAAkB,GAAlB,kBAAkB,CAAoB;IACtD,CAAC;IAEJ,YAAY;QACV,IAAI,CAAC,cAAc,EAAE,CAAC;QACtB,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,gDAAgD,CAAC,CAAC;QAGlE,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,4DAA4D,CAAC,CAAC;QAG9E,IAAI,CAAC,MAAM,EAAE,EAAE,CAAC,YAAY,EAAE,CAAC,MAAM,EAAE,EAAE;YACvC,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,4BAA4B,MAAM,CAAC,EAAE,EAAE,CAAC,CAAC;YACzD,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,uBAAuB,IAAI,CAAC,SAAS,CAAC;gBACpD,KAAK,EAAE,MAAM,CAAC,SAAS,CAAC,KAAK;gBAC7B,OAAO,EAAE,MAAM,CAAC,SAAS,CAAC,OAAO;gBACjC,IAAI,EAAE,MAAM,CAAC,SAAS,CAAC,IAAI;aAC5B,CAAC,EAAE,CAAC,CAAC;YAEN,MAAM,CAAC,EAAE,CAAC,YAAY,EAAE,CAAC,MAAM,EAAE,EAAE;gBACjC,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,iCAAiC,MAAM,CAAC,EAAE,YAAY,MAAM,EAAE,CAAC,CAAC;YAClF,CAAC,CAAC,CAAC;YAEH,MAAM,CAAC,EAAE,CAAC,OAAO,EAAE,CAAC,KAAK,EAAE,EAAE;gBAC3B,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,wBAAwB,MAAM,CAAC,EAAE,EAAE,EAAE,KAAK,CAAC,CAAC;YAChE,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAGH,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC;YAChB,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,kEAAkE,CAAC,CAAC;QACtF,CAAC;aAAM,CAAC;YACN,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,8CAA8C,CAAC,CAAC;QACpE,CAAC;IACH,CAAC;IAED,eAAe;QACb,IAAI,IAAI,CAAC,iBAAiB,EAAE,CAAC;YAC3B,aAAa,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;QACxC,CAAC;QACD,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,yCAAyC,CAAC,CAAC;IAC7D,CAAC;IAED,gBAAgB,CAAC,MAAc;QAC7B,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE,EAAE,MAAM,CAAC,CAAC;QACpC,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE,EAAE,IAAI,GAAG,EAAE,CAAC,CAAC;QAG7C,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,qBAAqB,MAAM,CAAC,EAAE,EAAE,CAAC,CAAC;QAClD,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,4BAA4B,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC,CAAC;QACjE,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,0BAA0B,IAAI,CAAC,SAAS,CAAC;YACvD,IAAI,EAAE,MAAM,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,YAAY;YACrD,KAAK,EAAE,MAAM,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,YAAY;YACrF,OAAO,EAAE,MAAM,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,YAAY;YAC3D,OAAO,EAAE,MAAM,CAAC,SAAS,CAAC,OAAO;SAClC,CAAC,EAAE,CAAC,CAAC;QAGN,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE;YACxB,IAAI,EAAE,6CAAoB,CAAC,IAAI;YAC/B,IAAI,EAAE,EAAE,OAAO,EAAE,8CAA8C,EAAE;SAClE,CAAC,CAAC;IACL,CAAC;IAED,gBAAgB,CAAC,MAAc;QAC7B,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;QAC/B,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;QACrC,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,wBAAwB,MAAM,CAAC,EAAE,EAAE,CAAC,CAAC;IACvD,CAAC;IAKD,KAAK,CAAC,kBAAkB,CAAC,MAAc,EAAE,OAA4B;QACnE,IAAI,CAAC;YAEH,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,kBAAkB,CAAC,aAAa,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;YAC3E,IAAI,WAAW,EAAE,CAAC;gBAChB,OAAO;oBACL,OAAO,EAAE,KAAK;oBACd,OAAO,EAAE,8CAA8C;iBACxD,CAAC;YACJ,CAAC;YAGD,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC;gBACpB,OAAO;oBACL,OAAO,EAAE,KAAK;oBACd,OAAO,EAAE,uBAAuB;iBACjC,CAAC;YACJ,CAAC;YAGD,MAAM,mBAAmB,GAAG,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;YAC9D,IAAI,mBAAmB,EAAE,CAAC;gBACxB,mBAAmB,CAAC,GAAG,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;YAC1C,CAAC;iBAAM,CAAC;gBACN,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE,EAAE,IAAI,GAAG,CAAC,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;YAC/D,CAAC;YAED,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,UAAU,MAAM,CAAC,EAAE,kBAAkB,OAAO,CAAC,MAAM,EAAE,CAAC,CAAC;YAEvE,OAAO;gBACL,OAAO,EAAE,IAAI;gBACb,OAAO,EAAE,6BAA6B,OAAO,CAAC,MAAM,EAAE;gBACtD,cAAc,EAAE,GAAG,MAAM,CAAC,EAAE,IAAI,OAAO,CAAC,MAAM,EAAE;aACjD,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,gCAAgC,KAAK,CAAC,OAAO,EAAE,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC;YAChF,OAAO;gBACL,OAAO,EAAE,KAAK;gBACd,OAAO,EAAE,iDAAiD;aAC3D,CAAC;QACJ,CAAC;IACH,CAAC;IAKD,oBAAoB,CAAC,MAAc,EAAE,MAAc;QACjD,IAAI,CAAC;YACH,MAAM,mBAAmB,GAAG,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;YAC9D,IAAI,mBAAmB,EAAE,CAAC;gBACxB,mBAAmB,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;gBACnC,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,UAAU,MAAM,CAAC,EAAE,sBAAsB,MAAM,EAAE,CAAC,CAAC;YACrE,CAAC;YAED,OAAO;gBACL,OAAO,EAAE,IAAI;gBACb,OAAO,EAAE,iCAAiC,MAAM,EAAE;aACnD,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,kCAAkC,KAAK,CAAC,OAAO,EAAE,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC;YAClF,OAAO;gBACL,OAAO,EAAE,KAAK;gBACd,OAAO,EAAE,qDAAqD;aAC/D,CAAC;QACJ,CAAC;IACH,CAAC;IAKD,oBAAoB,CAAC,SAAuB;QAC1C,MAAM,OAAO,GAAqB;YAChC,IAAI,EAAE,6CAAoB,CAAC,YAAY;YACvC,IAAI,EAAE,SAAS;SAChB,CAAC;QAGF,IAAI,SAAS,GAAG,CAAC,CAAC;QAClB,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,MAAM,EAAE,QAAQ,EAAE,EAAE;YACxC,MAAM,mBAAmB,GAAG,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;YAC7D,IAAI,mBAAmB,IAAI,mBAAmB,CAAC,GAAG,CAAC,SAAS,CAAC,MAAM,CAAC,EAAE,CAAC;gBACrE,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;gBACnC,SAAS,EAAE,CAAC;YACd,CAAC;QACH,CAAC,CAAC,CAAC;QAEH,IAAI,SAAS,GAAG,CAAC,EAAE,CAAC;YAClB,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,uBAAuB,SAAS,CAAC,MAAM,KAAK,SAAS,CAAC,KAAK,QAAQ,SAAS,SAAS,CAAC,CAAC;QACzG,CAAC;aAAM,CAAC;YACN,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,+BAA+B,SAAS,CAAC,MAAM,uBAAuB,CAAC,CAAC;QAC5F,CAAC;IACH,CAAC;IAKD,YAAY,CAAC,MAAc,EAAE,OAAyB;QACpD,MAAM,CAAC,IAAI,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC;IAClC,CAAC;IAKD,iBAAiB,CAAC,MAAc,EAAE,YAAoB;QACpD,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE;YACxB,IAAI,EAAE,6CAAoB,CAAC,KAAK;YAChC,IAAI,EAAE,EAAE,OAAO,EAAE,YAAY,EAAE;SAChC,CAAC,CAAC;IACL,CAAC;IAKO,cAAc;QACpB,MAAM,iBAAiB,GAAG,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,8BAA8B,EAAE,KAAK,CAAC,CAAC;QACxF,IAAI,CAAC,iBAAiB,GAAG,WAAW,CAAC,GAAG,EAAE;YACxC,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,MAAM,EAAE,EAAE;gBAC9B,MAAM,CAAC,IAAI,CAAC,WAAW,EAAE,EAAE,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE,EAAE,CAAC,CAAC;YACtD,CAAC,CAAC,CAAC;QACL,CAAC,EAAE,iBAAiB,CAAC,CAAC;IACxB,CAAC;CACF,CAAA;AA7MY,kEAA2B;AAEtC;IADC,IAAA,4BAAe,GAAE;8BACV,kBAAM;2DAAC;sCAFJ,2BAA2B;IARvC,IAAA,6BAAgB,EAAC;QAChB,SAAS,EAAE,aAAa;QACxB,IAAI,EAAE;YACJ,MAAM,EAAE,GAAG;YACX,WAAW,EAAE,IAAI;SAClB;KACF,CAAC;IACD,IAAA,mBAAU,GAAE;qCAWuB,sBAAa;QACR,yCAAkB;GAX9C,2BAA2B,CA6MvC"}
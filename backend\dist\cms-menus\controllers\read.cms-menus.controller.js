"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ReadCmsMenusController = void 0;
const openapi = require("@nestjs/swagger");
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const jwt_auth_guard_1 = require("../../common/guards/jwt-auth.guard");
const read_cms_menus_service_1 = require("../services/read.cms-menus.service");
const cms_menus_entity_1 = require("../entity/cms-menus.entity");
const api_response_dto_1 = require("../../common/dto/api-response.dto");
const custom_pagination_query_dto_1 = require("../../common/dto/custom-pagination-query.dto");
const pagination_response_dto_1 = require("../../common/dto/pagination-response.dto");
const custom_pagination_meta_dto_1 = require("../../common/dto/custom-pagination-meta.dto");
const roles_decorator_1 = require("../../common/decorators/roles.decorator");
const permissions_decorator_1 = require("../../common/decorators/permissions.decorator");
let ReadCmsMenusController = class ReadCmsMenusController {
    cmsMenusService;
    constructor(cmsMenusService) {
        this.cmsMenusService = cmsMenusService;
    }
    async findAll(paginationQuery) {
        const { data, total } = await this.cmsMenusService.findAll(paginationQuery);
        const meta = new custom_pagination_meta_dto_1.CustomPageMetaDto({
            pageQueryDto: paginationQuery,
            itemCount: total,
        });
        return new pagination_response_dto_1.PaginationResponseDto(data, meta);
    }
    async findByPostType(postType, paginationQuery) {
        const { data, total } = await this.cmsMenusService.findByPostType(postType, paginationQuery);
        const meta = new custom_pagination_meta_dto_1.CustomPageMetaDto({
            pageQueryDto: paginationQuery,
            itemCount: total,
        });
        return new pagination_response_dto_1.PaginationResponseDto(data, meta);
    }
    async findByStatus(status, paginationQuery) {
        const { data, total } = await this.cmsMenusService.findByStatus(status, paginationQuery);
        const meta = new custom_pagination_meta_dto_1.CustomPageMetaDto({
            pageQueryDto: paginationQuery,
            itemCount: total,
        });
        return new pagination_response_dto_1.PaginationResponseDto(data, meta);
    }
    async count(filter) {
        return this.cmsMenusService.count(filter);
    }
    async search(keyword, paginationQuery) {
        const { data, total } = await this.cmsMenusService.search(keyword, paginationQuery);
        const meta = new custom_pagination_meta_dto_1.CustomPageMetaDto({
            pageQueryDto: paginationQuery,
            itemCount: total,
        });
        return new pagination_response_dto_1.PaginationResponseDto(data, meta);
    }
    async findDeleted(paginationQuery) {
        const { data, total } = await this.cmsMenusService.findDeleted(paginationQuery);
        const meta = new custom_pagination_meta_dto_1.CustomPageMetaDto({
            pageQueryDto: paginationQuery,
            itemCount: total,
        });
        return new pagination_response_dto_1.PaginationResponseDto(data, meta);
    }
    async findOne(id, relations) {
        const relationsArray = relations ? relations.split(',').map(r => r.trim()) : [];
        return this.cmsMenusService.findOneOrFail(id, relationsArray);
    }
    async findBySlug(slug) {
        return this.cmsMenusService.findBySlugPublic(slug);
    }
};
exports.ReadCmsMenusController = ReadCmsMenusController;
__decorate([
    (0, common_1.Get)(),
    (0, roles_decorator_1.Roles)('ADMIN', 'EDITOR', 'USER'),
    (0, permissions_decorator_1.Permissions)('cms-menu:read'),
    (0, swagger_1.ApiOperation)({ summary: 'Lấy danh sách menu CMS với phân trang' }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Danh sách menu CMS.',
        type: api_response_dto_1.ApiResponseDto,
        schema: {
            properties: {
                data: {
                    type: 'object',
                    properties: {
                        data: {
                            type: 'array',
                            items: { $ref: '#/components/schemas/CmsMenuDto' },
                        },
                        meta: {
                            type: 'object',
                            properties: {
                                total: { type: 'number' },
                                page: { type: 'number' },
                                limit: { type: 'number' },
                                totalPages: { type: 'number' },
                            },
                        },
                    },
                },
            },
        },
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.BAD_REQUEST,
        description: 'Tham số không hợp lệ.',
        type: api_response_dto_1.ApiResponseDto,
        schema: { properties: { data: { type: 'object', nullable: true } } },
    }),
    (0, swagger_1.ApiQuery)({ name: 'page', required: false, type: Number, description: 'Số trang' }),
    (0, swagger_1.ApiQuery)({ name: 'limit', required: false, type: Number, description: 'Số lượng bản ghi mỗi trang' }),
    (0, swagger_1.ApiQuery)({ name: 'search', required: false, type: String, description: 'Tìm kiếm theo từ khóa' }),
    openapi.ApiResponse({ status: 200 }),
    __param(0, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [custom_pagination_query_dto_1.CustomPaginationQueryDto]),
    __metadata("design:returntype", Promise)
], ReadCmsMenusController.prototype, "findAll", null);
__decorate([
    (0, common_1.Get)('by-post-type/:postType'),
    (0, roles_decorator_1.Roles)('ADMIN', 'EDITOR', 'USER'),
    (0, permissions_decorator_1.Permissions)('cms-menu:read'),
    (0, swagger_1.ApiOperation)({ summary: 'Lấy danh sách menu CMS theo loại post' }),
    (0, swagger_1.ApiParam)({
        name: 'postType',
        enum: cms_menus_entity_1.CmsMenuPostType,
        description: 'Loại post',
    }),
    (0, swagger_1.ApiQuery)({ name: 'page', required: false, type: Number, description: 'Số trang' }),
    (0, swagger_1.ApiQuery)({ name: 'limit', required: false, type: Number, description: 'Số lượng bản ghi mỗi trang' }),
    (0, swagger_1.ApiQuery)({ name: 'search', required: false, type: String, description: 'Tìm kiếm theo từ khóa' }),
    openapi.ApiResponse({ status: 200 }),
    __param(0, (0, common_1.Param)('postType')),
    __param(1, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, custom_pagination_query_dto_1.CustomPaginationQueryDto]),
    __metadata("design:returntype", Promise)
], ReadCmsMenusController.prototype, "findByPostType", null);
__decorate([
    (0, common_1.Get)('by-status/:status'),
    (0, roles_decorator_1.Roles)('ADMIN', 'EDITOR', 'USER'),
    (0, permissions_decorator_1.Permissions)('cms-menu:read'),
    (0, swagger_1.ApiOperation)({ summary: 'Lấy danh sách menu CMS theo trạng thái' }),
    (0, swagger_1.ApiParam)({
        name: 'status',
        enum: cms_menus_entity_1.CmsMenuStatus,
        description: 'Trạng thái menu',
    }),
    (0, swagger_1.ApiQuery)({ name: 'page', required: false, type: Number, description: 'Số trang' }),
    (0, swagger_1.ApiQuery)({ name: 'limit', required: false, type: Number, description: 'Số lượng bản ghi mỗi trang' }),
    (0, swagger_1.ApiQuery)({ name: 'search', required: false, type: String, description: 'Tìm kiếm theo từ khóa' }),
    openapi.ApiResponse({ status: 200 }),
    __param(0, (0, common_1.Param)('status')),
    __param(1, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, custom_pagination_query_dto_1.CustomPaginationQueryDto]),
    __metadata("design:returntype", Promise)
], ReadCmsMenusController.prototype, "findByStatus", null);
__decorate([
    (0, common_1.Get)('count'),
    (0, roles_decorator_1.Roles)('ADMIN', 'EDITOR', 'USER'),
    (0, permissions_decorator_1.Permissions)('cms-menu:read'),
    (0, swagger_1.ApiOperation)({ summary: 'Đếm số lượng menu CMS' }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Số lượng menu CMS.',
        type: api_response_dto_1.ApiResponseDto,
        schema: {
            properties: {
                data: { type: 'number' },
            },
        },
    }),
    (0, swagger_1.ApiQuery)({ name: 'filter', required: false, type: String, description: 'Bộ lọc' }),
    openapi.ApiResponse({ status: 200, type: Number }),
    __param(0, (0, common_1.Query)('filter')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], ReadCmsMenusController.prototype, "count", null);
__decorate([
    (0, common_1.Get)('search'),
    (0, roles_decorator_1.Roles)('ADMIN', 'EDITOR', 'USER'),
    (0, permissions_decorator_1.Permissions)('cms-menu:read'),
    (0, swagger_1.ApiOperation)({ summary: 'Tìm kiếm menu CMS' }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Kết quả tìm kiếm menu CMS.',
        type: api_response_dto_1.ApiResponseDto,
        schema: {
            properties: {
                data: {
                    type: 'object',
                    properties: {
                        data: {
                            type: 'array',
                            items: { $ref: '#/components/schemas/CmsMenuDto' },
                        },
                        meta: {
                            type: 'object',
                            properties: {
                                total: { type: 'number' },
                                page: { type: 'number' },
                                limit: { type: 'number' },
                                totalPages: { type: 'number' },
                            },
                        },
                    },
                },
            },
        },
    }),
    (0, swagger_1.ApiQuery)({ name: 'keyword', required: true, type: String, description: 'Từ khóa tìm kiếm' }),
    (0, swagger_1.ApiQuery)({ name: 'page', required: false, type: Number, description: 'Số trang' }),
    (0, swagger_1.ApiQuery)({ name: 'limit', required: false, type: Number, description: 'Số lượng bản ghi mỗi trang' }),
    openapi.ApiResponse({ status: 200 }),
    __param(0, (0, common_1.Query)('keyword')),
    __param(1, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, custom_pagination_query_dto_1.CustomPaginationQueryDto]),
    __metadata("design:returntype", Promise)
], ReadCmsMenusController.prototype, "search", null);
__decorate([
    (0, common_1.Get)('deleted'),
    (0, roles_decorator_1.Roles)('ADMIN'),
    (0, permissions_decorator_1.Permissions)('cms-menu:read'),
    (0, swagger_1.ApiOperation)({ summary: 'Lấy danh sách menu CMS đã xóa' }),
    (0, swagger_1.ApiQuery)({ name: 'page', required: false, type: Number, description: 'Số trang' }),
    (0, swagger_1.ApiQuery)({ name: 'limit', required: false, type: Number, description: 'Số lượng bản ghi mỗi trang' }),
    openapi.ApiResponse({ status: 200 }),
    __param(0, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [custom_pagination_query_dto_1.CustomPaginationQueryDto]),
    __metadata("design:returntype", Promise)
], ReadCmsMenusController.prototype, "findDeleted", null);
__decorate([
    (0, common_1.Get)(':id'),
    (0, roles_decorator_1.Roles)('ADMIN', 'EDITOR', 'USER'),
    (0, permissions_decorator_1.Permissions)('cms-menu:read'),
    (0, swagger_1.ApiOperation)({ summary: 'Lấy thông tin chi tiết menu CMS' }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Thông tin chi tiết menu CMS.',
        type: api_response_dto_1.ApiResponseDto,
        schema: {
            properties: {
                data: { $ref: '#/components/schemas/CmsMenuDto' },
            },
        },
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.NOT_FOUND,
        description: 'Không tìm thấy menu CMS.',
        type: api_response_dto_1.ApiResponseDto,
        schema: { properties: { data: { type: 'object', nullable: true } } },
    }),
    (0, swagger_1.ApiParam)({ name: 'id', type: String, description: 'ID của menu CMS' }),
    openapi.ApiResponse({ status: 200, type: Object }),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __param(1, (0, common_1.Query)('relations')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String]),
    __metadata("design:returntype", Promise)
], ReadCmsMenusController.prototype, "findOne", null);
__decorate([
    (0, common_1.Get)('slug/:slug'),
    (0, roles_decorator_1.Roles)('ADMIN', 'EDITOR', 'USER'),
    (0, permissions_decorator_1.Permissions)('cms-menu:read'),
    (0, swagger_1.ApiOperation)({ summary: 'Lấy thông tin menu CMS theo slug' }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Thông tin menu CMS.',
        type: api_response_dto_1.ApiResponseDto,
        schema: {
            properties: {
                data: { $ref: '#/components/schemas/CmsMenuDto' },
            },
        },
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.NOT_FOUND,
        description: 'Không tìm thấy menu CMS.',
        type: api_response_dto_1.ApiResponseDto,
        schema: { properties: { data: { type: 'object', nullable: true } } },
    }),
    (0, swagger_1.ApiParam)({ name: 'slug', type: String, description: 'Slug của menu CMS' }),
    openapi.ApiResponse({ status: 200, type: Object }),
    __param(0, (0, common_1.Param)('slug')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], ReadCmsMenusController.prototype, "findBySlug", null);
exports.ReadCmsMenusController = ReadCmsMenusController = __decorate([
    (0, swagger_1.ApiTags)('cms-menus'),
    (0, swagger_1.ApiBearerAuth)(),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, common_1.Controller)('cms/menus'),
    __metadata("design:paramtypes", [read_cms_menus_service_1.ReadCmsMenusService])
], ReadCmsMenusController);
//# sourceMappingURL=read.cms-menus.controller.js.map
import { User } from '../../users/entities/user.entity';
import { ReadActivityLogService } from '../services/read.activity-log.service';
import { ActivityLogDto } from '../dto/activity-log.dto';
import { CustomPaginationQueryDto } from '../../common/dto/custom-pagination-query.dto';
import { PaginationResponseDto } from '../../common/dto/pagination-response.dto';
export declare class ActivityLogReadController {
    private readonly activityLogService;
    private readonly logger;
    constructor(activityLogService: ReadActivityLogService);
    findMyActivities(user: User, limit: number, page: number, sortBy?: string, sortOrder?: 'ASC' | 'DESC', module?: string): Promise<PaginationResponseDto<ActivityLogDto>>;
    findAll(paginationQuery: CustomPaginationQueryDto): Promise<PaginationResponseDto<ActivityLogDto>>;
    search(paginationQuery: CustomPaginationQueryDto): Promise<PaginationResponseDto<ActivityLogDto>>;
    findOne(id: string, relations?: string): Promise<ActivityLogDto>;
    findByUserId(userId: string, paginationQuery: CustomPaginationQueryDto): Promise<PaginationResponseDto<ActivityLogDto>>;
    findByAction(action: string, paginationQuery: CustomPaginationQueryDto): Promise<PaginationResponseDto<ActivityLogDto>>;
    findByModule(module: string, paginationQuery: CustomPaginationQueryDto): Promise<PaginationResponseDto<ActivityLogDto>>;
    count(filter?: string): Promise<{
        count: number;
    }>;
}

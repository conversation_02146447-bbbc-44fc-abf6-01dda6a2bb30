"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.VnpayTransactionRepository = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const vnpay_transaction_entity_1 = require("../entities/vnpay-transaction.entity");
let VnpayTransactionRepository = class VnpayTransactionRepository {
    repository;
    constructor(repository) {
        this.repository = repository;
    }
    async create(data) {
        const transaction = this.repository.create(data);
        return await this.repository.save(transaction);
    }
    async findByMerchantRef(merchantTxnRef) {
        return await this.repository.findOne({
            where: { merchantTxnRef },
        });
    }
    async findByVnpayRef(vnpayTxnRef) {
        return await this.repository.findOne({
            where: { vnpayTxnRef },
        });
    }
    async findById(id) {
        return await this.repository.findOne({
            where: { id },
        });
    }
    async update(id, data) {
        await this.repository.update(id, data);
        return await this.findById(id);
    }
    async updateStatus(id, status, additionalData) {
        const updateData = {
            status,
            processedAt: new Date(),
            ...additionalData,
        };
        await this.repository.update(id, updateData);
        return await this.findById(id);
    }
    async findWithFilters(filter, page = 1, limit = 20) {
        const where = {};
        if (filter.status)
            where.status = filter.status;
        if (filter.type)
            where.type = filter.type;
        if (filter.externalRef)
            where.externalRef = filter.externalRef;
        if (filter.vnpayTxnRef)
            where.vnpayTxnRef = filter.vnpayTxnRef;
        if (filter.merchantTxnRef)
            where.merchantTxnRef = filter.merchantTxnRef;
        if (filter.bankCode)
            where.bankCode = filter.bankCode;
        if (filter.fromDate || filter.toDate) {
            where.createdAt = (0, typeorm_2.Between)(filter.fromDate || new Date('1970-01-01'), filter.toDate || new Date());
        }
        const queryBuilder = this.repository
            .createQueryBuilder('transaction')
            .where(where);
        if (filter.minAmount !== undefined) {
            queryBuilder.andWhere('transaction.amount >= :minAmount', {
                minAmount: filter.minAmount,
            });
        }
        if (filter.maxAmount !== undefined) {
            queryBuilder.andWhere('transaction.amount <= :maxAmount', {
                maxAmount: filter.maxAmount,
            });
        }
        const offset = (page - 1) * limit;
        queryBuilder
            .orderBy('transaction.createdAt', 'DESC')
            .skip(offset)
            .take(limit);
        const [transactions, total] = await queryBuilder.getManyAndCount();
        return { transactions, total };
    }
    async getStats(filter) {
        const queryBuilder = this.repository.createQueryBuilder('transaction');
        if (filter) {
            if (filter.fromDate || filter.toDate) {
                queryBuilder.where('transaction.createdAt BETWEEN :fromDate AND :toDate', {
                    fromDate: filter.fromDate || new Date('1970-01-01'),
                    toDate: filter.toDate || new Date(),
                });
            }
            if (filter.externalRef) {
                queryBuilder.andWhere('transaction.externalRef = :externalRef', {
                    externalRef: filter.externalRef,
                });
            }
        }
        const [totalResult, successResult, failedResult, pendingResult, totalAmountResult, successAmountResult,] = await Promise.all([
            queryBuilder.getCount(),
            queryBuilder
                .clone()
                .andWhere('transaction.status = :status', {
                status: vnpay_transaction_entity_1.VnpayTransactionStatus.SUCCESS,
            })
                .getCount(),
            queryBuilder
                .clone()
                .andWhere('transaction.status = :status', {
                status: vnpay_transaction_entity_1.VnpayTransactionStatus.FAILED,
            })
                .getCount(),
            queryBuilder
                .clone()
                .andWhere('transaction.status = :status', {
                status: vnpay_transaction_entity_1.VnpayTransactionStatus.PENDING,
            })
                .getCount(),
            queryBuilder
                .clone()
                .select('SUM(transaction.amount)', 'total')
                .getRawOne(),
            queryBuilder
                .clone()
                .select('SUM(transaction.amount)', 'total')
                .andWhere('transaction.status = :status', {
                status: vnpay_transaction_entity_1.VnpayTransactionStatus.SUCCESS,
            })
                .getRawOne(),
        ]);
        const totalAmount = parseInt(totalAmountResult?.total || '0');
        const successfulAmount = parseInt(successAmountResult?.total || '0');
        const successRate = totalResult > 0 ? (successResult / totalResult) * 100 : 0;
        return {
            totalTransactions: totalResult,
            successfulTransactions: successResult,
            failedTransactions: failedResult,
            pendingTransactions: pendingResult,
            totalAmount,
            successfulAmount,
            successRate: Math.round(successRate * 100) / 100,
        };
    }
    async findExpiredPending() {
        return await this.repository.find({
            where: {
                status: vnpay_transaction_entity_1.VnpayTransactionStatus.PENDING,
                expiresAt: (0, typeorm_2.Between)(new Date('1970-01-01'), new Date()),
            },
        });
    }
    async markExpiredTransactions() {
        const expiredTransactions = await this.findExpiredPending();
        if (expiredTransactions.length === 0) {
            return 0;
        }
        const expiredIds = expiredTransactions.map((t) => t.id);
        await this.repository.update(expiredIds, {
            status: vnpay_transaction_entity_1.VnpayTransactionStatus.EXPIRED,
            processedAt: new Date(),
            errorMessage: 'Transaction expired',
        });
        return expiredTransactions.length;
    }
    async getDailySummary(date) {
        const startOfDay = new Date(date);
        startOfDay.setHours(0, 0, 0, 0);
        const endOfDay = new Date(date);
        endOfDay.setHours(23, 59, 59, 999);
        return await this.getStats({
            fromDate: startOfDay,
            toDate: endOfDay,
        });
    }
    async softDelete(id) {
        await this.repository.softDelete(id);
    }
    async getForReconciliation(date) {
        const startOfDay = new Date(date);
        startOfDay.setHours(0, 0, 0, 0);
        const endOfDay = new Date(date);
        endOfDay.setHours(23, 59, 59, 999);
        return await this.repository.find({
            where: {
                status: vnpay_transaction_entity_1.VnpayTransactionStatus.SUCCESS,
                processedAt: (0, typeorm_2.Between)(startOfDay, endOfDay),
            },
            order: {
                processedAt: 'ASC',
            },
        });
    }
};
exports.VnpayTransactionRepository = VnpayTransactionRepository;
exports.VnpayTransactionRepository = VnpayTransactionRepository = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(vnpay_transaction_entity_1.VnpayTransaction)),
    __metadata("design:paramtypes", [typeorm_2.Repository])
], VnpayTransactionRepository);
//# sourceMappingURL=vnpay-transaction.repository.js.map
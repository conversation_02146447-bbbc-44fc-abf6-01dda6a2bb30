"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ReadCmsBannersService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const event_emitter_1 = require("@nestjs/event-emitter");
const base_cms_banners_service_1 = require("./base.cms-banners.service");
const cms_banners_entity_1 = require("../entity/cms-banners.entity");
let ReadCmsBannersService = class ReadCmsBannersService extends base_cms_banners_service_1.BaseCmsBannersService {
    bannerRepository;
    dataSource;
    eventEmitter;
    constructor(bannerRepository, dataSource, eventEmitter) {
        super(bannerRepository, dataSource, eventEmitter);
        this.bannerRepository = bannerRepository;
        this.dataSource = dataSource;
        this.eventEmitter = eventEmitter;
    }
    async findAll(params) {
        try {
            this.logger.debug(`Đang tìm tất cả banner CMS với tham số: ${JSON.stringify(params)}`);
            const { limit, page, search, sort, filter } = params;
            const skip = (page - 1) * limit;
            let relationsArray = params.relationsArray || [];
            const requiredRelations = ['creator', 'updater', 'deleter'];
            const allRelations = [...new Set([...requiredRelations, ...relationsArray])];
            const validatedRelations = this.validateRelations(allRelations);
            const queryBuilder = this.bannerRepository.createQueryBuilder('banner');
            queryBuilder.where('banner.isDeleted = :isDeleted', { isDeleted: false });
            validatedRelations.forEach(relation => {
                this.logger.debug(`Đang tải mối quan hệ: ${relation}`);
                queryBuilder.leftJoinAndSelect(`banner.${relation}`, relation);
            });
            if (search) {
                queryBuilder.andWhere('(banner.title LIKE :search OR banner.altText LIKE :search)', { search: `%${search}%` });
            }
            if (filter) {
                try {
                    const filterObj = JSON.parse(filter);
                    if (filterObj.status) {
                        queryBuilder.andWhere('banner.status = :status', {
                            status: filterObj.status
                        });
                    }
                    if (filterObj.location) {
                        queryBuilder.andWhere('banner.location = :location', {
                            location: filterObj.location
                        });
                    }
                    if (filterObj.isActive !== undefined) {
                        const statusValue = filterObj.isActive ? cms_banners_entity_1.CmsBannerStatus.ACTIVE : cms_banners_entity_1.CmsBannerStatus.INACTIVE;
                        queryBuilder.andWhere('banner.status = :statusFromIsActive', {
                            statusFromIsActive: statusValue
                        });
                    }
                    if (filterObj.dateFrom && filterObj.dateTo) {
                        queryBuilder.andWhere('banner.createdAt BETWEEN :dateFrom AND :dateTo', {
                            dateFrom: new Date(filterObj.dateFrom),
                            dateTo: new Date(filterObj.dateTo)
                        });
                    }
                    else if (filterObj.dateFrom) {
                        queryBuilder.andWhere('banner.createdAt >= :dateFrom', {
                            dateFrom: new Date(filterObj.dateFrom)
                        });
                    }
                    else if (filterObj.dateTo) {
                        queryBuilder.andWhere('banner.createdAt <= :dateTo', {
                            dateTo: new Date(filterObj.dateTo)
                        });
                    }
                }
                catch (error) {
                    const filters = filter.split(',');
                    filters.forEach(filterItem => {
                        const [field, value] = filterItem.split(':');
                        if (field && value) {
                            queryBuilder.andWhere(`banner.${field} = :${field}`, { [field]: value });
                        }
                    });
                }
            }
            if (sort) {
                const [sortField, sortOrder] = sort.split(':');
                queryBuilder.orderBy(`banner.${sortField}`, sortOrder);
            }
            else {
                queryBuilder.orderBy('banner.displayOrder', 'ASC')
                    .addOrderBy('banner.createdAt', 'DESC');
            }
            queryBuilder.skip(skip).take(limit);
            const [banners, total] = await queryBuilder.getManyAndCount();
            const bannerDtos = banners.map(banner => this.toDto(banner))
                .filter((dto) => dto !== null);
            return { data: bannerDtos, total };
        }
        catch (error) {
            this.logger.error(`Lỗi khi tìm tất cả banner CMS: ${error.message}`, error.stack);
            throw new common_1.InternalServerErrorException(`Không thể tìm tất cả banner CMS: ${error.message}`);
        }
    }
    async findByStatus(status, params) {
        try {
            this.logger.debug(`Đang tìm banner CMS theo trạng thái: ${status}`);
            const queryBuilder = this.bannerRepository
                .createQueryBuilder('banner')
                .where('banner.isDeleted = :isDeleted', { isDeleted: false })
                .andWhere('banner.status = :status', { status });
            if (params.search) {
                queryBuilder.andWhere(new typeorm_2.Brackets(qb => {
                    qb.where('LOWER(banner.title) LIKE LOWER(:search)', { search: `%${params.search}%` })
                        .orWhere('LOWER(banner.altText) LIKE LOWER(:search)', { search: `%${params.search}%` });
                }));
            }
            const sortOptions = params.sortOptions;
            if (sortOptions && sortOptions.length > 0) {
                const { field, order } = sortOptions[0];
                queryBuilder.orderBy(`banner.${field}`, order);
            }
            else {
                queryBuilder.orderBy('banner.displayOrder', 'ASC');
            }
            const skip = (params.page - 1) * params.limit;
            queryBuilder.skip(skip).take(params.limit);
            const [banners, total] = await queryBuilder.getManyAndCount();
            const bannerDtos = this.toDtos(banners);
            return { data: bannerDtos, total };
        }
        catch (error) {
            this.logger.error(`Lỗi khi tìm banner theo trạng thái: ${error.message}`, error.stack);
            throw new common_1.InternalServerErrorException(`Không thể tìm banner theo trạng thái: ${error.message}`);
        }
    }
    async findByLocation(location, params) {
        try {
            this.logger.debug(`Đang tìm banner CMS theo vị trí: ${location}`);
            const queryBuilder = this.bannerRepository
                .createQueryBuilder('banner')
                .where('banner.isDeleted = :isDeleted', { isDeleted: false })
                .andWhere('banner.location = :location', { location });
            if (params.search) {
                queryBuilder.andWhere(new typeorm_2.Brackets(qb => {
                    qb.where('LOWER(banner.title) LIKE LOWER(:search)', { search: `%${params.search}%` })
                        .orWhere('LOWER(banner.altText) LIKE LOWER(:search)', { search: `%${params.search}%` });
                }));
            }
            queryBuilder.orderBy('banner.displayOrder', 'ASC')
                .addOrderBy('banner.createdAt', 'DESC');
            const skip = (params.page - 1) * params.limit;
            queryBuilder.skip(skip).take(params.limit);
            const [banners, total] = await queryBuilder.getManyAndCount();
            const bannerDtos = this.toDtos(banners);
            return { data: bannerDtos, total };
        }
        catch (error) {
            this.logger.error(`Lỗi khi tìm banner theo vị trí: ${error.message}`, error.stack);
            throw new common_1.InternalServerErrorException(`Không thể tìm banner theo vị trí: ${error.message}`);
        }
    }
    async getActiveBanners(location, limit = 10) {
        try {
            this.logger.debug(`Đang lấy banner hoạt động cho vị trí: ${location || 'tất cả'}`);
            const now = new Date();
            const queryBuilder = this.bannerRepository
                .createQueryBuilder('banner')
                .where('banner.isDeleted = :isDeleted', { isDeleted: false })
                .andWhere('banner.status = :status', { status: cms_banners_entity_1.CmsBannerStatus.ACTIVE })
                .andWhere(new typeorm_2.Brackets(qb => {
                qb.where('banner.startDate IS NULL OR banner.startDate <= :now', { now })
                    .andWhere('banner.endDate IS NULL OR banner.endDate >= :now', { now });
            }));
            if (location) {
                queryBuilder.andWhere('banner.location = :location', { location });
            }
            queryBuilder.orderBy('banner.displayOrder', 'ASC')
                .addOrderBy('banner.createdAt', 'DESC')
                .limit(limit);
            const banners = await queryBuilder.getMany();
            return this.toDtos(banners);
        }
        catch (error) {
            this.logger.error(`Lỗi khi lấy banner hoạt động: ${error.message}`, error.stack);
            throw new common_1.InternalServerErrorException(`Không thể lấy banner hoạt động: ${error.message}`);
        }
    }
    async count(filter) {
        try {
            const where = this.buildWhereClause(filter);
            return this.bannerRepository.count({ where });
        }
        catch (error) {
            this.logger.error(`Lỗi khi đếm số lượng banner CMS: ${error.message}`, error.stack);
            throw new common_1.InternalServerErrorException(`Không thể đếm số lượng banner CMS: ${error.message}`);
        }
    }
    async findOne(id, relations = []) {
        try {
            this.logger.debug(`Đang tìm banner CMS theo ID: ${id}`);
            const validatedRelations = this.validateRelations(relations);
            try {
                const banner = await this.findById(id, validatedRelations, true);
                if (!banner) {
                    throw new common_1.NotFoundException(`Không tìm thấy banner với ID: ${id}`);
                }
                return this.toDto(banner);
            }
            catch (notFoundError) {
                if (notFoundError instanceof common_1.NotFoundException) {
                    return null;
                }
                throw notFoundError;
            }
        }
        catch (error) {
            this.logger.error(`Lỗi khi tìm banner CMS theo ID ${id}: ${error.message}`, error.stack);
            if (error instanceof common_1.NotFoundException) {
                return null;
            }
            throw new common_1.InternalServerErrorException(`Không thể lấy thông tin banner CMS: ${error.message}`);
        }
    }
    async findOneOrFail(id, relations = []) {
        try {
            this.logger.debug(`Đang tìm banner CMS theo ID: ${id}`);
            const validatedRelations = this.validateRelations(relations);
            const validRelationsToLoad = [...validatedRelations];
            if (!validRelationsToLoad.includes('creator')) {
                validRelationsToLoad.push('creator');
            }
            if (!validRelationsToLoad.includes('updater')) {
                validRelationsToLoad.push('updater');
            }
            const banner = await this.findById(id, validRelationsToLoad);
            if (!banner) {
                throw new common_1.NotFoundException(`Không tìm thấy banner với ID: ${id}`);
            }
            return this.toDto(banner);
        }
        catch (error) {
            this.logger.error(`Lỗi khi tìm banner CMS theo ID ${id}: ${error.message}`, error.stack);
            if (error instanceof common_1.NotFoundException) {
                throw error;
            }
            throw new common_1.InternalServerErrorException(`Không thể lấy thông tin banner CMS: ${error.message}`);
        }
    }
    async findByBusinessCodePublic(businessCode) {
        try {
            this.logger.debug(`Đang tìm banner CMS theo business code: ${businessCode}`);
            const banner = await super.findByBusinessCode(businessCode, false);
            if (!banner) {
                return null;
            }
            return this.toDto(banner);
        }
        catch (error) {
            this.logger.error(`Lỗi khi tìm banner theo business code: ${error.message}`, error.stack);
            throw new common_1.InternalServerErrorException(`Không thể tìm banner theo business code: ${error.message}`);
        }
    }
    async search(keyword, params) {
        try {
            this.logger.debug(`Đang tìm kiếm banner CMS với từ khóa: ${keyword}`);
            const queryBuilder = this.bannerRepository
                .createQueryBuilder('banner')
                .where('banner.isDeleted = :isDeleted', { isDeleted: false });
            queryBuilder.andWhere(new typeorm_2.Brackets(qb => {
                qb.where('LOWER(banner.title) LIKE LOWER(:keyword)', { keyword: `%${keyword}%` })
                    .orWhere('LOWER(banner.altText) LIKE LOWER(:keyword)', { keyword: `%${keyword}%` });
            }));
            const sortOptions = params.sortOptions;
            if (sortOptions && sortOptions.length > 0) {
                const { field, order } = sortOptions[0];
                queryBuilder.orderBy(`banner.${field}`, order);
            }
            else {
                queryBuilder.orderBy('banner.displayOrder', 'ASC');
            }
            const skip = (params.page - 1) * params.limit;
            queryBuilder.skip(skip).take(params.limit);
            const [banners, total] = await queryBuilder.getManyAndCount();
            const bannerDtos = this.toDtos(banners);
            return { data: bannerDtos, total };
        }
        catch (error) {
            this.logger.error(`Lỗi khi tìm kiếm banner CMS: ${error.message}`, error.stack);
            throw new common_1.InternalServerErrorException(`Không thể tìm kiếm banner CMS: ${error.message}`);
        }
    }
    async findDeleted(params) {
        try {
            this.logger.debug(`Đang tìm các banner CMS đã bị xóa mềm với tham số: ${JSON.stringify(params)}`);
            const { limit, page } = params;
            const queryBuilder = this.bannerRepository
                .createQueryBuilder('banner')
                .where('banner.isDeleted = :isDeleted', { isDeleted: true });
            if (params.search) {
                queryBuilder.andWhere(new typeorm_2.Brackets(qb => {
                    qb.where('LOWER(banner.title) LIKE LOWER(:search)', { search: `%${params.search}%` })
                        .orWhere('LOWER(banner.altText) LIKE LOWER(:search)', { search: `%${params.search}%` });
                }));
            }
            const sortOptions = params.sortOptions;
            if (sortOptions && sortOptions.length > 0) {
                const { field, order } = sortOptions[0];
                queryBuilder.orderBy(`banner.${field}`, order);
            }
            else {
                queryBuilder.orderBy('banner.deletedAt', 'DESC');
            }
            queryBuilder.leftJoinAndSelect('banner.creator', 'creator')
                .leftJoinAndSelect('banner.updater', 'updater')
                .leftJoinAndSelect('banner.deleter', 'deleter');
            const skip = (page - 1) * limit;
            queryBuilder.skip(skip).take(limit);
            const [banners, total] = await queryBuilder.getManyAndCount();
            const bannerDtos = this.toDtos(banners);
            return { data: bannerDtos, total };
        }
        catch (error) {
            this.logger.error(`Lỗi khi tìm banner CMS đã xóa: ${error.message}`, error.stack);
            throw new common_1.InternalServerErrorException(`Không thể lấy danh sách banner CMS đã xóa: ${error.message}`);
        }
    }
    async debugDatabase() {
        try {
            this.logger.debug(`🔧 [debugDatabase] Starting database debug...`);
            const metadata = this.bannerRepository.metadata;
            const metadataInfo = {
                entityName: metadata.name,
                tableName: metadata.tableName,
                columns: metadata.columns.map(col => ({
                    propertyName: col.propertyName,
                    databaseName: col.databaseName,
                    type: col.type,
                    isNullable: col.isNullable
                }))
            };
            const totalCount = await this.bannerRepository.count();
            const totalNonDeleted = await this.bannerRepository.count({ where: { isDeleted: false } });
            const sampleBanners = await this.bannerRepository.find({
                take: 3,
                select: ['id', 'title', 'status', 'isDeleted', 'createdAt']
            });
            const qbResult = await this.bannerRepository
                .createQueryBuilder('banner')
                .select(['banner.id', 'banner.title', 'banner.status', 'banner.isDeleted'])
                .where('banner.isDeleted = :isDeleted', { isDeleted: false })
                .take(3)
                .getMany();
            return {
                metadata: metadataInfo,
                counts: {
                    total: totalCount,
                    nonDeleted: totalNonDeleted
                },
                sampleData: {
                    findMethod: sampleBanners,
                    queryBuilder: qbResult
                },
                timestamp: new Date().toISOString()
            };
        }
        catch (error) {
            this.logger.error(`❌ [debugDatabase] Error: ${error.message}`, error.stack);
            return {
                error: error.message,
                stack: error.stack,
                timestamp: new Date().toISOString()
            };
        }
    }
    async getStatistics() {
        try {
            const total = await this.bannerRepository.count({
                where: { isDeleted: false },
            });
            const byStatusQuery = await this.bannerRepository
                .createQueryBuilder('banner')
                .select('banner.status', 'status')
                .addSelect('COUNT(*)', 'count')
                .where('banner.isDeleted = :isDeleted', { isDeleted: false })
                .groupBy('banner.status')
                .getRawMany();
            const byStatus = {};
            byStatusQuery.forEach(item => {
                byStatus[item.status] = parseInt(item.count, 10);
            });
            const byLocationQuery = await this.bannerRepository
                .createQueryBuilder('banner')
                .select('banner.location', 'location')
                .addSelect('COUNT(*)', 'count')
                .where('banner.isDeleted = :isDeleted', { isDeleted: false })
                .groupBy('banner.location')
                .getRawMany();
            const byLocation = {};
            byLocationQuery.forEach(item => {
                const key = item.location || 'no_location';
                byLocation[key] = parseInt(item.count, 10);
            });
            const now = new Date();
            const active = await this.bannerRepository
                .createQueryBuilder('banner')
                .where('banner.isDeleted = :isDeleted', { isDeleted: false })
                .andWhere('banner.status = :status', { status: cms_banners_entity_1.CmsBannerStatus.ACTIVE })
                .andWhere(new typeorm_2.Brackets(qb => {
                qb.where('banner.startDate IS NULL OR banner.startDate <= :now', { now })
                    .andWhere('banner.endDate IS NULL OR banner.endDate >= :now', { now });
            }))
                .getCount();
            const expired = await this.bannerRepository
                .createQueryBuilder('banner')
                .where('banner.isDeleted = :isDeleted', { isDeleted: false })
                .andWhere('banner.endDate IS NOT NULL AND banner.endDate < :now', { now })
                .getCount();
            return {
                total,
                byStatus,
                byLocation,
                active,
                expired,
            };
        }
        catch (error) {
            this.logger.error(`Lỗi khi lấy thống kê banner CMS: ${error.message}`, error.stack);
            throw new common_1.InternalServerErrorException(`Không thể lấy thống kê banner CMS: ${error.message}`);
        }
    }
};
exports.ReadCmsBannersService = ReadCmsBannersService;
exports.ReadCmsBannersService = ReadCmsBannersService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(cms_banners_entity_1.CmsBanners)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        typeorm_2.DataSource,
        event_emitter_1.EventEmitter2])
], ReadCmsBannersService);
//# sourceMappingURL=read.cms-banners.service.js.map
import { Repository } from 'typeorm';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { CmsPartners } from '../entity/cms-partners.entity';
import { BaseCmsPartnersService } from './base.cms-partners.service';
import { BulkDeleteCmsPartnersDto, BulkRestoreCmsPartnersDto, BulkForceDeleteCmsPartnersDto, BulkOperationResponseDto, CmsPartnerDto } from '../dto';
export declare class DeleteCmsPartnersService extends BaseCmsPartnersService {
    protected readonly partnerRepository: Repository<CmsPartners>;
    protected readonly eventEmitter: EventEmitter2;
    constructor(partnerRepository: Repository<CmsPartners>, eventEmitter: EventEmitter2);
    softDelete(id: string, userId: string): Promise<CmsPartnerDto>;
    forceDelete(id: string, userId: string): Promise<void>;
    restore(id: string, userId: string): Promise<CmsPartnerDto>;
    bulkSoftDelete(bulkDeleteDto: BulkDeleteCmsPartnersDto, userId: string): Promise<BulkOperationResponseDto>;
    bulkForceDelete(bulkForceDeleteDto: BulkForceDeleteCmsPartnersDto, userId: string): Promise<BulkOperationResponseDto>;
    bulkRestore(bulkRestoreDto: BulkRestoreCmsPartnersDto, userId: string): Promise<BulkOperationResponseDto>;
}

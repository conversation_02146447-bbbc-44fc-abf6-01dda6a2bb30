"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.SeedSystemConfigData1746423000000 = void 0;
const system_config_dto_1 = require("../../system-configs/dto/system-config.dto");
class SeedSystemConfigData1746423000000 {
    name = 'SeedSystemConfigData1746423000000';
    async up(queryRunner) {
        const configs = [
            {
                configKey: 'site_name',
                configValue: 'BacSG - Nền tảng giao dịch bạc',
                description: 'Tên của trang web',
                configGroup: 'general',
                configType: system_config_dto_1.ConfigType.TEXT,
            },
            {
                configKey: 'site_description',
                configValue: 'Nền tảng giao dịch bạc trực tuyến và vật chất hàng đầu <PERSON>',
                description: '<PERSON><PERSON> tả ngắn về trang web',
                configGroup: 'general',
                configType: system_config_dto_1.ConfigType.TEXTAREA,
            },
            {
                configKey: 'maintenance_mode',
                configValue: 'false',
                description: 'Bật/tắt chế độ bảo trì',
                configGroup: 'general',
                configType: system_config_dto_1.ConfigType.BOOLEAN,
            },
            {
                configKey: 'contact_email',
                configValue: '<EMAIL>',
                description: 'Email liên hệ',
                configGroup: 'contact',
                configType: system_config_dto_1.ConfigType.TEXT,
            },
            {
                configKey: 'contact_phone',
                configValue: '0987654321',
                description: 'Số điện thoại liên hệ',
                configGroup: 'contact',
                configType: system_config_dto_1.ConfigType.TEXT,
            },
            {
                configKey: 'contact_address',
                configValue: '123 Đường ABC, Quận 1, TP.HCM',
                description: 'Địa chỉ liên hệ',
                configGroup: 'contact',
                configType: system_config_dto_1.ConfigType.TEXTAREA,
            },
            {
                configKey: 'min_deposit',
                configValue: '100000',
                description: 'Số tiền nạp tối thiểu (VND)',
                configGroup: 'payment',
                configType: system_config_dto_1.ConfigType.NUMBER,
            },
            {
                configKey: 'min_withdrawal',
                configValue: '100000',
                description: 'Số tiền rút tối thiểu (VND)',
                configGroup: 'payment',
                configType: system_config_dto_1.ConfigType.NUMBER,
            },
            {
                configKey: 'withdrawal_fee',
                configValue: '1',
                description: 'Phí rút tiền (%)',
                configGroup: 'payment',
                configType: system_config_dto_1.ConfigType.NUMBER,
            },
            {
                configKey: 'default_currency',
                configValue: 'VND',
                description: 'Đơn vị tiền tệ mặc định',
                configGroup: 'payment',
                configType: system_config_dto_1.ConfigType.SELECT,
                configOptions: JSON.stringify(['VND', 'USD', 'EUR']),
            },
            {
                configKey: 'enable_kyc',
                configValue: 'true',
                description: 'Bật/tắt xác minh KYC',
                configGroup: 'security',
                configType: system_config_dto_1.ConfigType.BOOLEAN,
            },
            {
                configKey: 'enable_2fa',
                configValue: 'true',
                description: 'Bật/tắt xác thực 2 yếu tố',
                configGroup: 'security',
                configType: system_config_dto_1.ConfigType.BOOLEAN,
            },
            {
                configKey: 'login_attempts',
                configValue: '5',
                description: 'Số lần đăng nhập thất bại tối đa',
                configGroup: 'security',
                configType: system_config_dto_1.ConfigType.NUMBER,
            },
            {
                configKey: 'session_timeout',
                configValue: '30',
                description: 'Thời gian hết hạn phiên đăng nhập (phút)',
                configGroup: 'security',
                configType: system_config_dto_1.ConfigType.NUMBER,
            },
            {
                configKey: 'social_facebook',
                configValue: 'https://facebook.com/bacsg',
                description: 'Liên kết Facebook',
                configGroup: 'social',
                configType: system_config_dto_1.ConfigType.TEXT,
            },
            {
                configKey: 'social_twitter',
                configValue: 'https://twitter.com/bacsg',
                description: 'Liên kết Twitter',
                configGroup: 'social',
                configType: system_config_dto_1.ConfigType.TEXT,
            },
            {
                configKey: 'min_trade_amount',
                configValue: '10',
                description: 'Số lượng giao dịch tối thiểu (gram)',
                configGroup: 'trading',
                configType: system_config_dto_1.ConfigType.NUMBER,
            },
            {
                configKey: 'max_trade_amount',
                configValue: '1000',
                description: 'Số lượng giao dịch tối đa (gram)',
                configGroup: 'trading',
                configType: system_config_dto_1.ConfigType.NUMBER,
            },
            {
                configKey: 'trade_fee',
                configValue: '0.5',
                description: 'Phí giao dịch (%)',
                configGroup: 'trading',
                configType: system_config_dto_1.ConfigType.NUMBER,
            },
            {
                configKey: 'price_fluctuation_limit',
                configValue: '3',
                description: 'Biên độ dao động giá tối đa (%)',
                configGroup: 'trading',
                configType: system_config_dto_1.ConfigType.NUMBER,
            },
            {
                configKey: 'max_deposit',
                configValue: '1000000000',
                description: 'Số tiền nạp tối đa (VND)',
                configGroup: 'finance',
                configType: system_config_dto_1.ConfigType.NUMBER,
            },
            {
                configKey: 'max_withdrawal',
                configValue: '500000000',
                description: 'Số tiền rút tối đa (VND)',
                configGroup: 'finance',
                configType: system_config_dto_1.ConfigType.NUMBER,
            },
            {
                configKey: 'withdrawal_processing_time',
                configValue: '24',
                description: 'Thời gian xử lý rút tiền (giờ)',
                configGroup: 'finance',
                configType: system_config_dto_1.ConfigType.NUMBER,
            },
            {
                configKey: 'smtp_host',
                configValue: 'smtp.example.com',
                description: 'SMTP Host',
                configGroup: 'notification',
                configType: system_config_dto_1.ConfigType.TEXT,
            },
            {
                configKey: 'smtp_port',
                configValue: '587',
                description: 'SMTP Port',
                configGroup: 'notification',
                configType: system_config_dto_1.ConfigType.NUMBER,
            },
            {
                configKey: 'smtp_username',
                configValue: '<EMAIL>',
                description: 'SMTP Username',
                configGroup: 'notification',
                configType: system_config_dto_1.ConfigType.TEXT,
            },
            {
                configKey: 'enable_email_notifications',
                configValue: 'true',
                description: 'Bật/tắt thông báo qua email',
                configGroup: 'notification',
                configType: system_config_dto_1.ConfigType.BOOLEAN,
            },
            {
                configKey: 'silver_measurement_unit',
                configValue: 'gram',
                description: 'Đơn vị đo lường bạc',
                configGroup: 'products',
                configType: system_config_dto_1.ConfigType.SELECT,
                configOptions: JSON.stringify(['gram', 'ounce', 'kg', 'lượng', 'chỉ']),
            },
            {
                configKey: 'silver_types',
                configValue: '999,925,900,800',
                description: 'Các loại bạc (phân cách bằng dấu phẩy)',
                configGroup: 'products',
                configType: system_config_dto_1.ConfigType.TEXT,
            },
            {
                configKey: 'silver_product_categories',
                configValue: 'Bạc miếng,Bạc nén,Trang sức bạc,Bạc nghệ thuật',
                description: 'Danh mục sản phẩm bạc (phân cách bằng dấu phẩy)',
                configGroup: 'products',
                configType: system_config_dto_1.ConfigType.TEXT,
            },
            {
                configKey: 'allow_registration',
                configValue: 'true',
                description: 'Cho phép đăng ký tài khoản mới',
                configGroup: 'users',
                configType: system_config_dto_1.ConfigType.BOOLEAN,
            },
            {
                configKey: 'require_email_verification',
                configValue: 'true',
                description: 'Yêu cầu xác minh email',
                configGroup: 'users',
                configType: system_config_dto_1.ConfigType.BOOLEAN,
            },
            {
                configKey: 'password_min_length',
                configValue: '8',
                description: 'Độ dài mật khẩu tối thiểu',
                configGroup: 'users',
                configType: system_config_dto_1.ConfigType.NUMBER,
            },
            {
                configKey: 'agent_levels',
                configValue: '3',
                description: 'Số cấp đại lý tối đa',
                configGroup: 'agents',
                configType: system_config_dto_1.ConfigType.NUMBER,
            },
            {
                configKey: 'agent_commission_rate',
                configValue: '1.5',
                description: 'Tỷ lệ hoa hồng đại lý (%)',
                configGroup: 'agents',
                configType: system_config_dto_1.ConfigType.NUMBER,
            },
            {
                configKey: 'agent_min_deposit',
                configValue: '50000000',
                description: 'Số tiền nạp tối thiểu để trở thành đại lý (VND)',
                configGroup: 'agents',
                configType: system_config_dto_1.ConfigType.NUMBER,
            },
            {
                configKey: 'report_export_formats',
                configValue: 'pdf,excel,csv',
                description: 'Định dạng xuất báo cáo (phân cách bằng dấu phẩy)',
                configGroup: 'reports',
                configType: system_config_dto_1.ConfigType.TEXT,
            },
            {
                configKey: 'daily_report_time',
                configValue: '23:00',
                description: 'Thời gian gửi báo cáo hàng ngày',
                configGroup: 'reports',
                configType: system_config_dto_1.ConfigType.TEXT,
            },
            {
                configKey: 'enable_auto_reports',
                configValue: 'true',
                description: 'Bật/tắt báo cáo tự động',
                configGroup: 'reports',
                configType: system_config_dto_1.ConfigType.BOOLEAN,
            },
            {
                configKey: 'silver_price_api_url',
                configValue: 'https://api.example.com/silver-price',
                description: 'URL API giá bạc thế giới',
                configGroup: 'integration',
                configType: system_config_dto_1.ConfigType.TEXT,
            },
            {
                configKey: 'silver_price_api_key',
                configValue: 'api_key_example',
                description: 'API Key cho API giá bạc',
                configGroup: 'integration',
                configType: system_config_dto_1.ConfigType.TEXT,
            },
            {
                configKey: 'exchange_rate_api_url',
                configValue: 'https://api.example.com/exchange-rate',
                description: 'URL API tỷ giá ngoại tệ',
                configGroup: 'integration',
                configType: system_config_dto_1.ConfigType.TEXT,
            },
            {
                configKey: 'vnpay_merchant_id',
                configValue: '',
                description: 'VNPAY Merchant ID',
                configGroup: 'payment_gateway',
                configType: system_config_dto_1.ConfigType.TEXT,
            },
            {
                configKey: 'vnpay_secure_hash',
                configValue: '',
                description: 'VNPAY Secure Hash',
                configGroup: 'payment_gateway',
                configType: system_config_dto_1.ConfigType.TEXT,
            },
            {
                configKey: 'vnpay_url',
                configValue: 'https://sandbox.vnpayment.vn/paymentv2/vpcpay.html',
                description: 'VNPAY API URL',
                configGroup: 'payment_gateway',
                configType: system_config_dto_1.ConfigType.TEXT,
            },
            {
                configKey: 'vnpay_return_url',
                configValue: 'https://yourdomain.com/payment/vnpay/return',
                description: 'VNPAY Return URL',
                configGroup: 'payment_gateway',
                configType: system_config_dto_1.ConfigType.TEXT,
            },
            {
                configKey: 'vnpay_api_version',
                configValue: '2.1.0',
                description: 'VNPAY API Version',
                configGroup: 'payment_gateway',
                configType: system_config_dto_1.ConfigType.TEXT,
            },
            {
                configKey: 'vnpay_command',
                configValue: 'pay',
                description: 'VNPAY Command',
                configGroup: 'payment_gateway',
                configType: system_config_dto_1.ConfigType.TEXT,
            },
            {
                configKey: 'vnpay_currency_code',
                configValue: 'VND',
                description: 'VNPAY Currency Code',
                configGroup: 'payment_gateway',
                configType: system_config_dto_1.ConfigType.TEXT,
            },
            {
                configKey: 'vnpay_locale',
                configValue: 'vn',
                description: 'VNPAY Locale',
                configGroup: 'payment_gateway',
                configType: system_config_dto_1.ConfigType.SELECT,
                configOptions: JSON.stringify(['vn', 'en']),
            },
            {
                configKey: 'momo_partner_code',
                configValue: '',
                description: 'MOMO Partner Code',
                configGroup: 'payment_gateway',
                configType: system_config_dto_1.ConfigType.TEXT,
            },
            {
                configKey: 'momo_access_key',
                configValue: '',
                description: 'MOMO Access Key',
                configGroup: 'payment_gateway',
                configType: system_config_dto_1.ConfigType.TEXT,
            },
            {
                configKey: 'momo_secret_key',
                configValue: '',
                description: 'MOMO Secret Key',
                configGroup: 'payment_gateway',
                configType: system_config_dto_1.ConfigType.TEXT,
            },
            {
                configKey: 'momo_api_endpoint',
                configValue: 'https://test-payment.momo.vn/v2/gateway/api/create',
                description: 'MOMO API Endpoint',
                configGroup: 'payment_gateway',
                configType: system_config_dto_1.ConfigType.TEXT,
            },
            {
                configKey: 'momo_return_url',
                configValue: 'https://yourdomain.com/payment/momo/return',
                description: 'MOMO Return URL',
                configGroup: 'payment_gateway',
                configType: system_config_dto_1.ConfigType.TEXT,
            },
            {
                configKey: 'momo_notify_url',
                configValue: 'https://yourdomain.com/payment/momo/notify',
                description: 'MOMO Notify URL',
                configGroup: 'payment_gateway',
                configType: system_config_dto_1.ConfigType.TEXT,
            },
            {
                configKey: 'email_template_welcome',
                configValue: '<h1>Chào mừng bạn đến với BacSG!</h1><p>Xin chào {name},</p><p>Cảm ơn bạn đã đăng ký tài khoản tại nền tảng giao dịch bạc hàng đầu Việt Nam.</p><p>Trân trọng,<br>Đội ngũ BacSG</p>',
                description: 'Mẫu email chào mừng',
                configGroup: 'email_template',
                configType: system_config_dto_1.ConfigType.TEXTAREA,
            },
            {
                configKey: 'email_template_deposit_success',
                configValue: '<h1>Nạp tiền thành công!</h1><p>Xin chào {name},</p><p>Bạn đã nạp thành công {amount} VND vào tài khoản.</p><p>Số dư hiện tại: {balance} VND</p><p>Trân trọng,<br>Đội ngũ BacSG</p>',
                description: 'Mẫu email nạp tiền thành công',
                configGroup: 'email_template',
                configType: system_config_dto_1.ConfigType.TEXTAREA,
            },
            {
                configKey: 'email_template_withdrawal_success',
                configValue: '<h1>Rút tiền thành công!</h1><p>Xin chào {name},</p><p>Bạn đã rút thành công {amount} VND từ tài khoản.</p><p>Số dư hiện tại: {balance} VND</p><p>Trân trọng,<br>Đội ngũ BacSG</p>',
                description: 'Mẫu email rút tiền thành công',
                configGroup: 'email_template',
                configType: system_config_dto_1.ConfigType.TEXTAREA,
            },
            {
                configKey: 'email_template_order_success',
                configValue: '<h1>Đặt lệnh thành công!</h1><p>Xin chào {name},</p><p>Bạn đã đặt lệnh thành công:</p><p>- Loại lệnh: {order_type}</p><p>- Sản phẩm: {product_name}</p><p>- Số lượng: {quantity} gram</p><p>- Giá: {price} VND</p><p>- Tổng giá trị: {total_amount} VND</p><p>Trân trọng,<br>Đội ngũ BacSG</p>',
                description: 'Mẫu email đặt lệnh thành công',
                configGroup: 'email_template',
                configType: system_config_dto_1.ConfigType.TEXTAREA,
            },
            {
                configKey: 'email_template_password_reset',
                configValue: '<h1>Đặt lại mật khẩu</h1><p>Xin chào {name},</p><p>Bạn đã yêu cầu đặt lại mật khẩu. Vui lòng nhấp vào liên kết dưới đây để đặt lại mật khẩu của bạn:</p><p><a href="{reset_link}">Đặt lại mật khẩu</a></p><p>Liên kết này sẽ hết hạn sau 24 giờ.</p><p>Trân trọng,<br>Đội ngũ BacSG</p>',
                description: 'Mẫu email đặt lại mật khẩu',
                configGroup: 'email_template',
                configType: system_config_dto_1.ConfigType.TEXTAREA,
            },
            {
                configKey: 'email_template_kyc_approved',
                configValue: '<h1>Xác minh KYC thành công!</h1><p>Xin chào {name},</p><p>Chúng tôi vui mừng thông báo rằng tài khoản của bạn đã được xác minh KYC thành công.</p><p>Bây giờ bạn có thể sử dụng đầy đủ các tính năng của nền tảng BacSG.</p><p>Trân trọng,<br>Đội ngũ BacSG</p>',
                description: 'Mẫu email KYC được duyệt',
                configGroup: 'email_template',
                configType: system_config_dto_1.ConfigType.TEXTAREA,
            },
            {
                configKey: 'email_template_kyc_rejected',
                configValue: '<h1>Xác minh KYC không thành công</h1><p>Xin chào {name},</p><p>Chúng tôi rất tiếc phải thông báo rằng yêu cầu xác minh KYC của bạn đã bị từ chối vì lý do sau:</p><p>{reason}</p><p>Vui lòng cập nhật thông tin và thử lại.</p><p>Trân trọng,<br>Đội ngũ BacSG</p>',
                description: 'Mẫu email KYC bị từ chối',
                configGroup: 'email_template',
                configType: system_config_dto_1.ConfigType.TEXTAREA,
            },
        ];
        for (const config of configs) {
            const configOptions = config.configOptions ? `'${config.configOptions}'` : 'NULL';
            const existingConfig = await queryRunner.query(`SELECT * FROM "system_configs" WHERE "config_key" = '${config.configKey}'`);
            if (existingConfig.length === 0) {
                await queryRunner.query(`
                    INSERT INTO "system_configs"
                    ("config_key", "config_value", "description", "config_group", "config_type", "config_options", "created_at", "updated_at")
                    VALUES
                    ('${config.configKey}', '${config.configValue}', '${config.description}', '${config.configGroup}', '${config.configType}', ${configOptions}, NOW(), NOW())
                `);
            }
            else {
                await queryRunner.query(`
                    UPDATE "system_configs"
                    SET
                        "config_group" = '${config.configGroup}',
                        "config_type" = '${config.configType}',
                        "config_options" = ${configOptions},
                        "description" = '${config.description}'
                    WHERE "config_key" = '${config.configKey}'
                `);
            }
        }
    }
    async down(queryRunner) {
    }
}
exports.SeedSystemConfigData1746423000000 = SeedSystemConfigData1746423000000;
//# sourceMappingURL=1746423000000-SeedSystemConfigData.js.map
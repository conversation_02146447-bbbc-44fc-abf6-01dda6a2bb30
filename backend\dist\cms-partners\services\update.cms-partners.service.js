"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UpdateCmsPartnersService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const event_emitter_1 = require("@nestjs/event-emitter");
const typeorm_transactional_1 = require("typeorm-transactional");
const cms_partners_entity_1 = require("../entity/cms-partners.entity");
const base_cms_partners_service_1 = require("./base.cms-partners.service");
const dto_1 = require("../dto");
const class_transformer_1 = require("class-transformer");
let UpdateCmsPartnersService = class UpdateCmsPartnersService extends base_cms_partners_service_1.BaseCmsPartnersService {
    partnerRepository;
    eventEmitter;
    constructor(partnerRepository, eventEmitter) {
        super(partnerRepository, eventEmitter);
        this.partnerRepository = partnerRepository;
        this.eventEmitter = eventEmitter;
        this.logger.log('UpdateCmsPartnersService đã được khởi tạo');
    }
    async update(id, updateDto, userId) {
        this.logger.log(`Attempting to update CMS Partner: ${id}`);
        try {
            const partner = await this.findById(id);
            if (!partner) {
                throw new common_1.NotFoundException(`Không tìm thấy đối tác với ID: ${id}`);
            }
            const normalizedData = this.normalizePartnerData(updateDto);
            const validationErrors = await this.validatePartnerData(normalizedData, id);
            if (validationErrors.length > 0) {
                throw new common_1.BadRequestException(`Dữ liệu không hợp lệ: ${validationErrors.join(', ')}`);
            }
            Object.assign(partner, normalizedData, {
                updatedBy: userId,
                updatedAt: new Date(),
            });
            const updatedPartner = await this.partnerRepository.save(partner);
            this.emitEvent('cms-partner.updated', {
                partnerId: updatedPartner.id,
                partnerName: updatedPartner.name,
                changes: normalizedData,
                userId,
                timestamp: new Date(),
            });
            this.logOperation('UPDATE', updatedPartner.id, userId);
            const result = (0, class_transformer_1.plainToClass)(dto_1.CmsPartnerDto, updatedPartner, {
                excludeExtraneousValues: true,
            });
            this.logger.log(`CMS Partner updated successfully: ${id}`);
            return result;
        }
        catch (error) {
            this.logger.error(`Failed to update CMS Partner ${id}: ${error.message}`, error.stack);
            if (error instanceof common_1.NotFoundException || error instanceof common_1.BadRequestException) {
                throw error;
            }
            throw new common_1.InternalServerErrorException('Không thể cập nhật đối tác. Vui lòng thử lại sau.');
        }
    }
    async updateStatus(id, updateStatusDto, userId) {
        this.logger.log(`Attempting to update CMS Partner status: ${id} to ${updateStatusDto.status}`);
        try {
            const partner = await this.findById(id);
            if (!partner) {
                throw new common_1.NotFoundException(`Không tìm thấy đối tác với ID: ${id}`);
            }
            const oldStatus = partner.status;
            partner.status = updateStatusDto.status;
            partner.updatedBy = userId;
            partner.updatedAt = new Date();
            const updatedPartner = await this.partnerRepository.save(partner);
            this.emitEvent('cms-partner.status-updated', {
                partnerId: updatedPartner.id,
                partnerName: updatedPartner.name,
                oldStatus,
                newStatus: updateStatusDto.status,
                userId,
                timestamp: new Date(),
            });
            this.logOperation('UPDATE_STATUS', updatedPartner.id, userId);
            return (0, class_transformer_1.plainToClass)(dto_1.CmsPartnerDto, updatedPartner, {
                excludeExtraneousValues: true,
            });
        }
        catch (error) {
            this.logger.error(`Failed to update CMS Partner status ${id}: ${error.message}`, error.stack);
            if (error instanceof common_1.NotFoundException) {
                throw error;
            }
            throw new common_1.InternalServerErrorException('Không thể cập nhật trạng thái đối tác. Vui lòng thử lại sau.');
        }
    }
    async activate(id, userId) {
        return this.updateStatus(id, { status: cms_partners_entity_1.CmsPartnerStatus.ACTIVE }, userId);
    }
    async deactivate(id, userId) {
        return this.updateStatus(id, { status: cms_partners_entity_1.CmsPartnerStatus.INACTIVE }, userId);
    }
    async updateDisplayOrder(id, updateDisplayOrderDto, userId) {
        this.logger.log(`Attempting to update CMS Partner display order: ${id} to ${updateDisplayOrderDto.displayOrder}`);
        try {
            const partner = await this.findById(id);
            if (!partner) {
                throw new common_1.NotFoundException(`Không tìm thấy đối tác với ID: ${id}`);
            }
            const oldDisplayOrder = partner.displayOrder;
            partner.displayOrder = updateDisplayOrderDto.displayOrder;
            partner.updatedBy = userId;
            partner.updatedAt = new Date();
            const updatedPartner = await this.partnerRepository.save(partner);
            this.emitEvent('cms-partner.display-order-updated', {
                partnerId: updatedPartner.id,
                partnerName: updatedPartner.name,
                oldDisplayOrder,
                newDisplayOrder: updateDisplayOrderDto.displayOrder,
                userId,
                timestamp: new Date(),
            });
            this.logOperation('UPDATE_DISPLAY_ORDER', updatedPartner.id, userId);
            return (0, class_transformer_1.plainToClass)(dto_1.CmsPartnerDto, updatedPartner, {
                excludeExtraneousValues: true,
            });
        }
        catch (error) {
            this.logger.error(`Failed to update CMS Partner display order ${id}: ${error.message}`, error.stack);
            if (error instanceof common_1.NotFoundException) {
                throw error;
            }
            throw new common_1.InternalServerErrorException('Không thể cập nhật thứ tự hiển thị đối tác. Vui lòng thử lại sau.');
        }
    }
    async updateType(id, updateTypeDto, userId) {
        this.logger.log(`Attempting to update CMS Partner type: ${id} to ${updateTypeDto.type}`);
        try {
            const partner = await this.findById(id);
            if (!partner) {
                throw new common_1.NotFoundException(`Không tìm thấy đối tác với ID: ${id}`);
            }
            const oldType = partner.type;
            partner.type = updateTypeDto.type;
            partner.updatedBy = userId;
            partner.updatedAt = new Date();
            const updatedPartner = await this.partnerRepository.save(partner);
            this.emitEvent('cms-partner.type-updated', {
                partnerId: updatedPartner.id,
                partnerName: updatedPartner.name,
                oldType,
                newType: updateTypeDto.type,
                userId,
                timestamp: new Date(),
            });
            this.logOperation('UPDATE_TYPE', updatedPartner.id, userId);
            return (0, class_transformer_1.plainToClass)(dto_1.CmsPartnerDto, updatedPartner, {
                excludeExtraneousValues: true,
            });
        }
        catch (error) {
            this.logger.error(`Failed to update CMS Partner type ${id}: ${error.message}`, error.stack);
            if (error instanceof common_1.NotFoundException) {
                throw error;
            }
            throw new common_1.InternalServerErrorException('Không thể cập nhật loại đối tác. Vui lòng thử lại sau.');
        }
    }
    async bulkUpdate(bulkUpdateDto, userId) {
        this.logger.log(`Attempting to bulk update ${bulkUpdateDto.ids.length} CMS Partners`);
        const startTime = Date.now();
        const result = {
            successCount: 0,
            failureCount: 0,
            totalCount: bulkUpdateDto.ids.length,
            successIds: [],
            errors: [],
            processingTime: 0,
        };
        try {
            for (let i = 0; i < bulkUpdateDto.ids.length; i++) {
                try {
                    const id = bulkUpdateDto.ids[i];
                    await this.update(id, bulkUpdateDto.updateData, userId);
                    result.successCount++;
                    result.successIds.push(id);
                }
                catch (error) {
                    result.failureCount++;
                    result.errors.push({
                        index: i,
                        error: error.message,
                        data: { id: bulkUpdateDto.ids[i] },
                    });
                }
            }
            result.processingTime = Date.now() - startTime;
            this.emitEvent('cms-partner.bulk-updated', {
                totalCount: result.totalCount,
                successCount: result.successCount,
                failureCount: result.failureCount,
                updateData: bulkUpdateDto.updateData,
                userId,
                timestamp: new Date(),
            });
            this.logger.log(`Bulk update completed: ${result.successCount}/${result.totalCount} successful`);
            return result;
        }
        catch (error) {
            this.logger.error(`Failed to bulk update CMS Partners: ${error.message}`, error.stack);
            throw new common_1.InternalServerErrorException('Không thể cập nhật nhiều đối tác. Vui lòng thử lại sau.');
        }
    }
    async bulkUpdateStatus(bulkUpdateStatusDto, userId) {
        this.logger.log(`Attempting to bulk update status for ${bulkUpdateStatusDto.ids.length} CMS Partners`);
        const startTime = Date.now();
        const result = {
            successCount: 0,
            failureCount: 0,
            totalCount: bulkUpdateStatusDto.ids.length,
            successIds: [],
            errors: [],
            processingTime: 0,
        };
        try {
            for (let i = 0; i < bulkUpdateStatusDto.ids.length; i++) {
                try {
                    const id = bulkUpdateStatusDto.ids[i];
                    await this.updateStatus(id, { status: bulkUpdateStatusDto.status }, userId);
                    result.successCount++;
                    result.successIds.push(id);
                }
                catch (error) {
                    result.failureCount++;
                    result.errors.push({
                        index: i,
                        error: error.message,
                        data: { id: bulkUpdateStatusDto.ids[i] },
                    });
                }
            }
            result.processingTime = Date.now() - startTime;
            this.emitEvent('cms-partner.bulk-status-updated', {
                totalCount: result.totalCount,
                successCount: result.successCount,
                failureCount: result.failureCount,
                status: bulkUpdateStatusDto.status,
                userId,
                timestamp: new Date(),
            });
            this.logger.log(`Bulk status update completed: ${result.successCount}/${result.totalCount} successful`);
            return result;
        }
        catch (error) {
            this.logger.error(`Failed to bulk update CMS Partners status: ${error.message}`, error.stack);
            throw new common_1.InternalServerErrorException('Không thể cập nhật trạng thái nhiều đối tác. Vui lòng thử lại sau.');
        }
    }
    async bulkActivate(ids, userId) {
        return this.bulkUpdateStatus({ ids, status: cms_partners_entity_1.CmsPartnerStatus.ACTIVE }, userId);
    }
    async bulkDeactivate(ids, userId) {
        return this.bulkUpdateStatus({ ids, status: cms_partners_entity_1.CmsPartnerStatus.INACTIVE }, userId);
    }
    async restore(id, userId) {
        this.logger.log(`Attempting to restore CMS Partner: ${id}`);
        try {
            const partner = await this.partnerRepository.findOne({
                where: { id, isDeleted: true },
            });
            if (!partner) {
                throw new common_1.NotFoundException(`Không tìm thấy đối tác đã xóa với ID: ${id}`);
            }
            partner.isDeleted = false;
            partner.deletedAt = null;
            partner.deletedBy = null;
            partner.updatedBy = userId;
            partner.updatedAt = new Date();
            const restoredPartner = await this.partnerRepository.save(partner);
            this.emitEvent('cms-partner.restored', {
                partnerId: restoredPartner.id,
                partnerName: restoredPartner.name,
                userId,
                timestamp: new Date(),
            });
            this.logOperation('RESTORE', restoredPartner.id, userId);
            return (0, class_transformer_1.plainToClass)(dto_1.CmsPartnerDto, restoredPartner, {
                excludeExtraneousValues: true,
            });
        }
        catch (error) {
            this.logger.error(`Failed to restore CMS Partner ${id}: ${error.message}`, error.stack);
            if (error instanceof common_1.NotFoundException) {
                throw error;
            }
            throw new common_1.InternalServerErrorException('Không thể khôi phục đối tác. Vui lòng thử lại sau.');
        }
    }
};
exports.UpdateCmsPartnersService = UpdateCmsPartnersService;
__decorate([
    (0, typeorm_transactional_1.Transactional)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, dto_1.UpdateCmsPartnerDto, String]),
    __metadata("design:returntype", Promise)
], UpdateCmsPartnersService.prototype, "update", null);
__decorate([
    (0, typeorm_transactional_1.Transactional)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, dto_1.UpdateCmsPartnerStatusDto, String]),
    __metadata("design:returntype", Promise)
], UpdateCmsPartnersService.prototype, "updateStatus", null);
__decorate([
    (0, typeorm_transactional_1.Transactional)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, dto_1.UpdateCmsPartnerDisplayOrderDto, String]),
    __metadata("design:returntype", Promise)
], UpdateCmsPartnersService.prototype, "updateDisplayOrder", null);
__decorate([
    (0, typeorm_transactional_1.Transactional)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, dto_1.UpdateCmsPartnerTypeDto, String]),
    __metadata("design:returntype", Promise)
], UpdateCmsPartnersService.prototype, "updateType", null);
__decorate([
    (0, typeorm_transactional_1.Transactional)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [dto_1.BulkUpdateCmsPartnersDto, String]),
    __metadata("design:returntype", Promise)
], UpdateCmsPartnersService.prototype, "bulkUpdate", null);
__decorate([
    (0, typeorm_transactional_1.Transactional)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [dto_1.BulkUpdateCmsPartnersStatusDto, String]),
    __metadata("design:returntype", Promise)
], UpdateCmsPartnersService.prototype, "bulkUpdateStatus", null);
__decorate([
    (0, typeorm_transactional_1.Transactional)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String]),
    __metadata("design:returntype", Promise)
], UpdateCmsPartnersService.prototype, "restore", null);
exports.UpdateCmsPartnersService = UpdateCmsPartnersService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(cms_partners_entity_1.CmsPartners)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        event_emitter_1.EventEmitter2])
], UpdateCmsPartnersService);
//# sourceMappingURL=update.cms-partners.service.js.map
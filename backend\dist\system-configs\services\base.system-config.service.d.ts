import { Logger } from '@nestjs/common';
import { Repository, DataSource } from 'typeorm';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { SystemConfig } from '../entities/system-config.entity';
import { SystemConfigDto } from '../dto/system-config.dto';
export declare class BaseSystemConfigService {
    protected readonly systemConfigRepository: Repository<SystemConfig>;
    protected readonly dataSource: DataSource;
    protected readonly eventEmitter: EventEmitter2;
    protected logger: Logger;
    protected readonly validRelations: string[];
    protected readonly EVENT_SYSTEM_CONFIG_CREATED = "system-config.created";
    protected readonly EVENT_SYSTEM_CONFIG_UPDATED = "system-config.updated";
    protected readonly EVENT_SYSTEM_CONFIG_DELETED = "system-config.deleted";
    protected readonly EVENT_SYSTEM_CONFIG_RESTORED = "system-config.restored";
    constructor(systemConfigRepository: Repository<SystemConfig>, dataSource: DataSource, eventEmitter: EventEmitter2);
    protected validateRelations(relations: string[]): string[];
    protected findByIdOrFail(id: string, relations?: string[], withDeleted?: boolean): Promise<SystemConfig>;
    protected findByKeyOrFail(key: string, relations?: string[], withDeleted?: boolean): Promise<SystemConfig>;
    protected toDto(systemConfig: SystemConfig): SystemConfigDto;
}

"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var ReadCmsCategoriesPublicController_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.ReadCmsCategoriesPublicController = void 0;
const openapi = require("@nestjs/swagger");
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const class_transformer_1 = require("class-transformer");
const read_cms_categories_service_1 = require("../services/read.cms-categories.service");
const cms_category_public_dto_1 = require("../dto/cms-category-public.dto");
const custom_pagination_query_dto_1 = require("../../common/dto/custom-pagination-query.dto");
const pagination_response_dto_1 = require("../../common/dto/pagination-response.dto");
const custom_pagination_meta_dto_1 = require("../../common/dto/custom-pagination-meta.dto");
const cms_categories_entity_1 = require("../entity/cms-categories.entity");
let ReadCmsCategoriesPublicController = ReadCmsCategoriesPublicController_1 = class ReadCmsCategoriesPublicController {
    cmsCategoriesService;
    logger = new common_1.Logger(ReadCmsCategoriesPublicController_1.name);
    constructor(cmsCategoriesService) {
        this.cmsCategoriesService = cmsCategoriesService;
    }
    async getActiveCategories(paginationQuery) {
        this.logger.log('Getting active CMS Categories for public display');
        const { data, total } = await this.cmsCategoriesService.getActiveCategories(paginationQuery);
        const publicData = (0, class_transformer_1.plainToInstance)(cms_category_public_dto_1.CmsCategoryPublicDto, data, {
            excludeExtraneousValues: true,
        });
        const filteredData = publicData.filter(category => category.canDisplayPublicly);
        const meta = new custom_pagination_meta_dto_1.CustomPageMetaDto({
            pageQueryDto: paginationQuery,
            itemCount: filteredData.length,
        });
        return new pagination_response_dto_1.PaginationResponseDto(filteredData, meta);
    }
    async getCarouselCategories(limit) {
        this.logger.log('Getting CMS Categories for carousel display');
        const queryWithFilter = Object.assign(new custom_pagination_query_dto_1.CustomPaginationQueryDto(), {
            page: 1,
            limit: limit || 12,
            sort: 'name:ASC',
        });
        const { data } = await this.cmsCategoriesService.getActiveCategories(queryWithFilter);
        const publicData = (0, class_transformer_1.plainToInstance)(cms_category_public_dto_1.CmsCategoryPublicDto, data, {
            excludeExtraneousValues: true,
        });
        return publicData.filter(category => category.canDisplayInCarousel);
    }
    async search(searchTerm, paginationQuery) {
        this.logger.log(`Searching CMS Categories with term: ${searchTerm} for public display`);
        const { data: allData } = await this.cmsCategoriesService.search(searchTerm, paginationQuery);
        const filteredData = allData.filter(category => category.status === cms_categories_entity_1.CmsCategoryStatus.ACTIVE && !category.isDeleted);
        const publicData = filteredData.map(category => (0, class_transformer_1.plainToInstance)(cms_category_public_dto_1.CmsCategoryPublicDto, category, {
            excludeExtraneousValues: true,
        }));
        const total = publicData.length;
        const meta = new custom_pagination_meta_dto_1.CustomPageMetaDto({
            pageQueryDto: paginationQuery,
            itemCount: total,
        });
        return new pagination_response_dto_1.PaginationResponseDto(publicData, meta);
    }
    async findAllPublic(paginationQuery) {
        this.logger.log('Getting all CMS Categories for public display with search and filter');
        if (paginationQuery.search) {
            const { data: allData } = await this.cmsCategoriesService.search(paginationQuery.search, paginationQuery);
            const filteredData = allData.filter(category => category.status === cms_categories_entity_1.CmsCategoryStatus.ACTIVE && !category.isDeleted);
            const publicData = filteredData.map(category => (0, class_transformer_1.plainToInstance)(cms_category_public_dto_1.CmsCategoryPublicDto, category, {
                excludeExtraneousValues: true,
            }));
            const total = publicData.length;
            const meta = new custom_pagination_meta_dto_1.CustomPageMetaDto({
                pageQueryDto: paginationQuery,
                itemCount: total,
            });
            return new pagination_response_dto_1.PaginationResponseDto(publicData, meta);
        }
        const { data, total } = await this.cmsCategoriesService.getActiveCategories(paginationQuery);
        const publicData = data.map(category => (0, class_transformer_1.plainToInstance)(cms_category_public_dto_1.CmsCategoryPublicDto, category, {
            excludeExtraneousValues: true,
        }));
        const meta = new custom_pagination_meta_dto_1.CustomPageMetaDto({
            pageQueryDto: paginationQuery,
            itemCount: total,
        });
        return new pagination_response_dto_1.PaginationResponseDto(publicData, meta);
    }
};
exports.ReadCmsCategoriesPublicController = ReadCmsCategoriesPublicController;
__decorate([
    (0, common_1.Get)('active'),
    (0, swagger_1.ApiOperation)({ summary: 'Lấy danh sách chuyên mục đang hoạt động (public)' }),
    (0, swagger_1.ApiQuery)({
        name: 'page',
        required: false,
        type: Number,
        description: 'Số trang',
        example: 1,
    }),
    (0, swagger_1.ApiQuery)({
        name: 'limit',
        required: false,
        type: Number,
        description: 'Số lượng chuyên mục mỗi trang',
        example: 10,
    }),
    (0, swagger_1.ApiQuery)({
        name: 'search',
        required: false,
        type: String,
        description: 'Tìm kiếm theo tên chuyên mục',
        example: 'bạc',
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Danh sách chuyên mục đang hoạt động',
        schema: {
            type: 'object',
            properties: {
                data: {
                    type: 'array',
                    items: { $ref: '#/components/schemas/CmsCategoryPublicDto' },
                },
                meta: {
                    type: 'object',
                    properties: {
                        total: { type: 'number' },
                        page: { type: 'number' },
                        limit: { type: 'number' },
                        totalPages: { type: 'number' },
                    },
                },
            },
        },
    }),
    openapi.ApiResponse({ status: 200 }),
    __param(0, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [custom_pagination_query_dto_1.CustomPaginationQueryDto]),
    __metadata("design:returntype", Promise)
], ReadCmsCategoriesPublicController.prototype, "getActiveCategories", null);
__decorate([
    (0, common_1.Get)('carousel'),
    (0, swagger_1.ApiOperation)({ summary: 'Lấy chuyên mục cho category carousel (public)' }),
    (0, swagger_1.ApiQuery)({
        name: 'limit',
        required: false,
        type: Number,
        description: 'Số lượng chuyên mục tối đa',
        example: 12,
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Danh sách chuyên mục cho carousel',
        schema: {
            type: 'array',
            items: { $ref: '#/components/schemas/CmsCategoryPublicDto' },
        },
    }),
    openapi.ApiResponse({ status: 200, type: [require("../dto/cms-category-public.dto").CmsCategoryPublicDto] }),
    __param(0, (0, common_1.Query)('limit')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", Promise)
], ReadCmsCategoriesPublicController.prototype, "getCarouselCategories", null);
__decorate([
    (0, common_1.Get)('search'),
    (0, swagger_1.ApiOperation)({ summary: 'Tìm kiếm chuyên mục (public)' }),
    (0, swagger_1.ApiQuery)({
        name: 'q',
        required: true,
        type: String,
        description: 'Từ khóa tìm kiếm',
        example: 'bạc thỏi',
    }),
    (0, swagger_1.ApiQuery)({
        name: 'page',
        required: false,
        type: Number,
        description: 'Số trang',
        example: 1,
    }),
    (0, swagger_1.ApiQuery)({
        name: 'limit',
        required: false,
        type: Number,
        description: 'Số lượng chuyên mục mỗi trang',
        example: 10,
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Kết quả tìm kiếm chuyên mục',
        schema: {
            type: 'object',
            properties: {
                data: {
                    type: 'array',
                    items: { $ref: '#/components/schemas/CmsCategoryPublicDto' },
                },
                meta: {
                    type: 'object',
                    properties: {
                        total: { type: 'number' },
                        page: { type: 'number' },
                        limit: { type: 'number' },
                        totalPages: { type: 'number' },
                    },
                },
            },
        },
    }),
    openapi.ApiResponse({ status: 200 }),
    __param(0, (0, common_1.Query)('q')),
    __param(1, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, custom_pagination_query_dto_1.CustomPaginationQueryDto]),
    __metadata("design:returntype", Promise)
], ReadCmsCategoriesPublicController.prototype, "search", null);
__decorate([
    (0, common_1.Get)(),
    (0, swagger_1.ApiOperation)({
        summary: 'Lấy tất cả chuyên mục (Public)',
        description: 'Lấy danh sách tất cả chuyên mục có thể hiển thị công khai với hỗ trợ tìm kiếm',
    }),
    (0, swagger_1.ApiQuery)({
        name: 'page',
        required: false,
        type: Number,
        description: 'Số trang',
        example: 1,
    }),
    (0, swagger_1.ApiQuery)({
        name: 'limit',
        required: false,
        type: Number,
        description: 'Số lượng chuyên mục mỗi trang',
        example: 10,
    }),
    (0, swagger_1.ApiQuery)({
        name: 'search',
        required: false,
        type: String,
        description: 'Từ khóa tìm kiếm',
        example: 'bạc',
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Danh sách chuyên mục public',
        schema: {
            type: 'object',
            properties: {
                data: {
                    type: 'array',
                    items: { $ref: '#/components/schemas/CmsCategoryPublicDto' },
                },
                meta: {
                    type: 'object',
                    properties: {
                        total: { type: 'number' },
                        page: { type: 'number' },
                        limit: { type: 'number' },
                        totalPages: { type: 'number' },
                    },
                },
            },
        },
    }),
    openapi.ApiResponse({ status: 200 }),
    __param(0, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [custom_pagination_query_dto_1.CustomPaginationQueryDto]),
    __metadata("design:returntype", Promise)
], ReadCmsCategoriesPublicController.prototype, "findAllPublic", null);
exports.ReadCmsCategoriesPublicController = ReadCmsCategoriesPublicController = ReadCmsCategoriesPublicController_1 = __decorate([
    (0, swagger_1.ApiTags)('cms-categories-public'),
    (0, common_1.Controller)('cms/categories/public'),
    __metadata("design:paramtypes", [read_cms_categories_service_1.ReadCmsCategoriesService])
], ReadCmsCategoriesPublicController);
//# sourceMappingURL=read.cms-categories.public.controller.js.map
"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var TradingViewController_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.TradingViewController = void 0;
const openapi = require("@nestjs/swagger");
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const jwt_auth_guard_1 = require("../../../common/guards/jwt-auth.guard");
const tradingview_api_service_1 = require("../services/tradingview-api.service");
const rate_limiter_service_1 = require("../services/rate-limiter.service");
const tradingview_data_model_1 = require("../models/tradingview-data.model");
let TradingViewController = TradingViewController_1 = class TradingViewController {
    tradingViewApiService;
    rateLimiterService;
    logger = new common_1.Logger(TradingViewController_1.name);
    constructor(tradingViewApiService, rateLimiterService) {
        this.tradingViewApiService = tradingViewApiService;
        this.rateLimiterService = rateLimiterService;
    }
    async getSilverPrice() {
        try {
            return await this.tradingViewApiService.fetchLatestPrice();
        }
        catch (error) {
            this.logger.error(`Lỗi khi lấy giá bạc: ${error.message}`, error.stack);
            throw new common_1.HttpException('Lỗi khi lấy giá bạc', common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async getSilverChart(interval = tradingview_data_model_1.ChartInterval.ONE_HOUR, from, to, limit) {
        try {
            return {
                symbol: 'XAGUSD',
                interval: interval,
                data: [],
            };
        }
        catch (error) {
            this.logger.error(`Lỗi khi lấy dữ liệu biểu đồ giá bạc: ${error.message}`, error.stack);
            throw new common_1.HttpException('Lỗi khi lấy dữ liệu biểu đồ giá bạc', common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
};
exports.TradingViewController = TradingViewController;
__decorate([
    (0, common_1.Get)('silver/price'),
    (0, swagger_1.ApiOperation)({ summary: 'Lấy giá bạc hiện tại' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Trả về giá bạc hiện tại' }),
    openapi.ApiResponse({ status: 200, type: Object }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], TradingViewController.prototype, "getSilverPrice", null);
__decorate([
    (0, common_1.Get)('silver/chart'),
    (0, swagger_1.ApiOperation)({ summary: 'Lấy dữ liệu biểu đồ giá bạc' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Trả về dữ liệu biểu đồ giá bạc' }),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, swagger_1.ApiBearerAuth)(),
    openapi.ApiResponse({ status: 200, type: Object }),
    __param(0, (0, common_1.Query)('interval')),
    __param(1, (0, common_1.Query)('from')),
    __param(2, (0, common_1.Query)('to')),
    __param(3, (0, common_1.Query)('limit')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Number, Number, Number]),
    __metadata("design:returntype", Promise)
], TradingViewController.prototype, "getSilverChart", null);
exports.TradingViewController = TradingViewController = TradingViewController_1 = __decorate([
    (0, swagger_1.ApiTags)('TradingView'),
    (0, common_1.Controller)('tradingview'),
    __metadata("design:paramtypes", [tradingview_api_service_1.TradingViewApiService,
        rate_limiter_service_1.RateLimiterService])
], TradingViewController);
//# sourceMappingURL=tradingview.controller.js.map
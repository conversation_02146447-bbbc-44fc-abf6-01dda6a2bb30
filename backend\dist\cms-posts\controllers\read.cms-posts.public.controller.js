"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var ReadCmsPostsPublicController_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.ReadCmsPostsPublicController = void 0;
const openapi = require("@nestjs/swagger");
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const class_transformer_1 = require("class-transformer");
const public_decorator_1 = require("../../common/decorators/public.decorator");
const read_cms_posts_service_1 = require("../services/read.cms-posts.service");
const cms_post_public_dto_1 = require("../dto/cms-post.public.dto");
const cms_posts_entity_1 = require("../entity/cms-posts.entity");
const api_response_dto_1 = require("../../common/dto/api-response.dto");
const custom_pagination_query_dto_1 = require("../../common/dto/custom-pagination-query.dto");
const pagination_response_dto_1 = require("../../common/dto/pagination-response.dto");
const custom_pagination_meta_dto_1 = require("../../common/dto/custom-pagination-meta.dto");
let ReadCmsPostsPublicController = ReadCmsPostsPublicController_1 = class ReadCmsPostsPublicController {
    cmsPostsService;
    logger = new common_1.Logger(ReadCmsPostsPublicController_1.name);
    constructor(cmsPostsService) {
        this.cmsPostsService = cmsPostsService;
    }
    toPublicDto(dto) {
        return (0, class_transformer_1.plainToInstance)(cms_post_public_dto_1.CmsPostPublicDto, dto, {
            excludeExtraneousValues: true,
        });
    }
    toPublicDtos(dtos) {
        return dtos.map(dto => this.toPublicDto(dto));
    }
    async findAllPublished(paginationQuery, postType, categoryId) {
        this.logger.log('Getting published CMS Posts for public display');
        const filters = [`status:${cms_posts_entity_1.CmsPostStatus.PUBLISHED}`];
        if (postType) {
            filters.push(`postType:${postType}`);
        }
        if (categoryId) {
            filters.push(`categoryId:${categoryId}`);
        }
        const queryWithFilter = Object.assign(new custom_pagination_query_dto_1.CustomPaginationQueryDto(), {
            ...paginationQuery,
            filter: filters.join(','),
            sort: paginationQuery.sort || 'publishedAt:DESC',
        });
        const { data, total } = await this.cmsPostsService.findAll(queryWithFilter);
        this.logger.log(`Found ${data.length} published posts from findAll`);
        const publicData = this.toPublicDtos(data);
        const filteredData = publicData.filter(post => post.canDisplayPublicly);
        this.logger.log(`Returning ${filteredData.length} published posts for public display`);
        const meta = new custom_pagination_meta_dto_1.CustomPageMetaDto({
            pageQueryDto: paginationQuery,
            itemCount: total,
        });
        return new pagination_response_dto_1.PaginationResponseDto(filteredData, meta);
    }
    async findByPostType(postType, paginationQuery) {
        this.logger.log(`Getting published CMS Posts by type: ${postType} for public display`);
        const filters = [`status:${cms_posts_entity_1.CmsPostStatus.PUBLISHED}`, `postType:${postType}`];
        const queryWithFilter = Object.assign(new custom_pagination_query_dto_1.CustomPaginationQueryDto(), {
            ...paginationQuery,
            filter: filters.join(','),
            sort: paginationQuery.sort || 'publishedAt:DESC',
        });
        const { data, total } = await this.cmsPostsService.findAll(queryWithFilter);
        const publicData = this.toPublicDtos(data);
        const filteredData = publicData.filter(post => post.canDisplayPublicly);
        const meta = new custom_pagination_meta_dto_1.CustomPageMetaDto({
            pageQueryDto: paginationQuery,
            itemCount: total,
        });
        return new pagination_response_dto_1.PaginationResponseDto(filteredData, meta);
    }
    async search(searchTerm, paginationQuery, postType) {
        this.logger.log(`Searching published CMS Posts with term: ${searchTerm} for public display`);
        const filters = [`status:${cms_posts_entity_1.CmsPostStatus.PUBLISHED}`];
        if (postType) {
            filters.push(`postType:${postType}`);
        }
        const queryWithSearchAndFilter = Object.assign(new custom_pagination_query_dto_1.CustomPaginationQueryDto(), {
            ...paginationQuery,
            search: searchTerm,
            filter: filters.join(','),
            sort: paginationQuery.sort || 'publishedAt:DESC',
        });
        const { data, total } = await this.cmsPostsService.findAll(queryWithSearchAndFilter);
        const publicData = this.toPublicDtos(data);
        const filteredData = publicData.filter(post => post.canDisplayPublicly);
        const meta = new custom_pagination_meta_dto_1.CustomPageMetaDto({
            pageQueryDto: paginationQuery,
            itemCount: total,
        });
        return new pagination_response_dto_1.PaginationResponseDto(filteredData, meta);
    }
    async findBySlug(slug) {
        this.logger.log(`Getting published CMS Post by slug: ${slug} for public display`);
        const post = await this.cmsPostsService.findBySlugPublic(slug, undefined, true);
        if (!post) {
            this.logger.warn(`Published post with slug ${slug} not found`);
            return null;
        }
        const publicPost = this.toPublicDto(post);
        this.logger.log(`Found published post: ${publicPost.title}`);
        return publicPost;
    }
    async findByCategorySlug(categorySlug, paginationQuery, postType) {
        this.logger.log(`Getting published CMS Posts by category slug: ${categorySlug} for public display`);
        const queryWithFilter = Object.assign(new custom_pagination_query_dto_1.CustomPaginationQueryDto(), {
            ...paginationQuery,
            postType,
        });
        const { data, total } = await this.cmsPostsService.findByCategorySlug(categorySlug, queryWithFilter);
        const publicData = this.toPublicDtos(data).filter(post => post.canDisplayPublicly);
        const meta = new custom_pagination_meta_dto_1.CustomPageMetaDto({ pageQueryDto: paginationQuery, itemCount: total });
        return new pagination_response_dto_1.PaginationResponseDto(publicData, meta);
    }
    async findByTagSlug(tagSlug, paginationQuery, postType) {
        this.logger.log(`Getting published CMS Posts by tag slug: ${tagSlug} for public display`);
        const queryWithFilter = Object.assign(new custom_pagination_query_dto_1.CustomPaginationQueryDto(), {
            ...paginationQuery,
            postType,
        });
        const { data, total } = await this.cmsPostsService.findByTagSlug(tagSlug, queryWithFilter);
        const publicData = this.toPublicDtos(data).filter(post => post.canDisplayPublicly);
        const meta = new custom_pagination_meta_dto_1.CustomPageMetaDto({ pageQueryDto: paginationQuery, itemCount: total });
        return new pagination_response_dto_1.PaginationResponseDto(publicData, meta);
    }
};
exports.ReadCmsPostsPublicController = ReadCmsPostsPublicController;
__decorate([
    (0, common_1.Get)('lists'),
    (0, public_decorator_1.Public)(),
    (0, swagger_1.ApiOperation)({ summary: 'Lấy danh sách bài viết đã xuất bản (Public)' }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Danh sách bài viết đã xuất bản.',
        type: pagination_response_dto_1.PaginationResponseDto,
        schema: {
            properties: {
                data: {
                    type: 'array',
                    items: { $ref: '#/components/schemas/CmsPostPublicDto' },
                },
                meta: {
                    type: 'object',
                    properties: {
                        total: { type: 'number' },
                        page: { type: 'number' },
                        limit: { type: 'number' },
                        totalPages: { type: 'number' },
                    },
                },
            },
        },
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.BAD_REQUEST,
        description: 'Tham số không hợp lệ.',
        type: api_response_dto_1.ApiResponseDto,
        schema: { properties: { data: { type: 'object', nullable: true } } },
    }),
    (0, swagger_1.ApiQuery)({ name: 'page', required: false, type: Number, description: 'Số trang' }),
    (0, swagger_1.ApiQuery)({ name: 'limit', required: false, type: Number, description: 'Số lượng bản ghi mỗi trang' }),
    (0, swagger_1.ApiQuery)({ name: 'search', required: false, type: String, description: 'Tìm kiếm theo từ khóa' }),
    (0, swagger_1.ApiQuery)({
        name: 'postType',
        required: false,
        enum: cms_posts_entity_1.CmsPostType,
        description: 'Lọc theo loại bài viết'
    }),
    (0, swagger_1.ApiQuery)({
        name: 'categoryId',
        required: false,
        type: String,
        description: 'Lọc theo ID chuyên mục'
    }),
    openapi.ApiResponse({ status: 200 }),
    __param(0, (0, common_1.Query)()),
    __param(1, (0, common_1.Query)('postType')),
    __param(2, (0, common_1.Query)('categoryId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [custom_pagination_query_dto_1.CustomPaginationQueryDto, String, String]),
    __metadata("design:returntype", Promise)
], ReadCmsPostsPublicController.prototype, "findAllPublished", null);
__decorate([
    (0, common_1.Get)('by-type/:postType'),
    (0, public_decorator_1.Public)(),
    (0, swagger_1.ApiOperation)({ summary: 'Lấy danh sách bài viết đã xuất bản theo loại (Public)' }),
    (0, swagger_1.ApiParam)({
        name: 'postType',
        enum: cms_posts_entity_1.CmsPostType,
        description: 'Loại bài viết',
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Danh sách bài viết đã xuất bản theo loại.',
        type: pagination_response_dto_1.PaginationResponseDto,
    }),
    (0, swagger_1.ApiQuery)({ name: 'page', required: false, type: Number, description: 'Số trang' }),
    (0, swagger_1.ApiQuery)({ name: 'limit', required: false, type: Number, description: 'Số lượng bản ghi mỗi trang' }),
    (0, swagger_1.ApiQuery)({ name: 'search', required: false, type: String, description: 'Tìm kiếm theo từ khóa' }),
    openapi.ApiResponse({ status: 200 }),
    __param(0, (0, common_1.Param)('postType')),
    __param(1, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, custom_pagination_query_dto_1.CustomPaginationQueryDto]),
    __metadata("design:returntype", Promise)
], ReadCmsPostsPublicController.prototype, "findByPostType", null);
__decorate([
    (0, common_1.Get)('search'),
    (0, public_decorator_1.Public)(),
    (0, swagger_1.ApiOperation)({ summary: 'Tìm kiếm bài viết đã xuất bản (Public)' }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Kết quả tìm kiếm bài viết đã xuất bản.',
        type: pagination_response_dto_1.PaginationResponseDto,
    }),
    (0, swagger_1.ApiQuery)({ name: 'q', required: true, type: String, description: 'Từ khóa tìm kiếm' }),
    (0, swagger_1.ApiQuery)({ name: 'page', required: false, type: Number, description: 'Số trang' }),
    (0, swagger_1.ApiQuery)({ name: 'limit', required: false, type: Number, description: 'Số lượng bản ghi mỗi trang' }),
    (0, swagger_1.ApiQuery)({
        name: 'postType',
        required: false,
        enum: cms_posts_entity_1.CmsPostType,
        description: 'Lọc theo loại bài viết'
    }),
    openapi.ApiResponse({ status: 200 }),
    __param(0, (0, common_1.Query)('q')),
    __param(1, (0, common_1.Query)()),
    __param(2, (0, common_1.Query)('postType')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, custom_pagination_query_dto_1.CustomPaginationQueryDto, String]),
    __metadata("design:returntype", Promise)
], ReadCmsPostsPublicController.prototype, "search", null);
__decorate([
    (0, common_1.Get)(':slug'),
    (0, public_decorator_1.Public)(),
    (0, swagger_1.ApiOperation)({ summary: 'Lấy thông tin chi tiết bài viết đã xuất bản theo slug (Public)' }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Thông tin chi tiết bài viết đã xuất bản.',
        type: api_response_dto_1.ApiResponseDto,
        schema: {
            properties: {
                data: { $ref: '#/components/schemas/CmsPostPublicDto' },
            },
        },
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.NOT_FOUND,
        description: 'Không tìm thấy bài viết hoặc bài viết chưa được xuất bản.',
        type: api_response_dto_1.ApiResponseDto,
        schema: { properties: { data: { type: 'object', nullable: true } } },
    }),
    (0, swagger_1.ApiParam)({ name: 'slug', type: String, description: 'Slug của bài viết' }),
    openapi.ApiResponse({ status: 200, type: Object }),
    __param(0, (0, common_1.Param)('slug')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], ReadCmsPostsPublicController.prototype, "findBySlug", null);
__decorate([
    (0, common_1.Get)('by-category/:categorySlug'),
    (0, public_decorator_1.Public)(),
    (0, swagger_1.ApiOperation)({ summary: 'Lấy danh sách bài viết đã xuất bản theo slug của category (Public)' }),
    (0, swagger_1.ApiParam)({ name: 'categorySlug', type: String, description: 'Slug của chuyên mục' }),
    (0, swagger_1.ApiQuery)({ name: 'page', required: false, type: Number, description: 'Số trang' }),
    (0, swagger_1.ApiQuery)({ name: 'limit', required: false, type: Number, description: 'Số lượng bản ghi mỗi trang' }),
    (0, swagger_1.ApiQuery)({ name: 'search', required: false, type: String, description: 'Tìm kiếm theo từ khóa' }),
    (0, swagger_1.ApiQuery)({ name: 'postType', required: false, enum: cms_posts_entity_1.CmsPostType, description: 'Lọc theo loại bài viết' }),
    openapi.ApiResponse({ status: 200 }),
    __param(0, (0, common_1.Param)('categorySlug')),
    __param(1, (0, common_1.Query)()),
    __param(2, (0, common_1.Query)('postType')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, custom_pagination_query_dto_1.CustomPaginationQueryDto, String]),
    __metadata("design:returntype", Promise)
], ReadCmsPostsPublicController.prototype, "findByCategorySlug", null);
__decorate([
    (0, common_1.Get)('by-tag/:tagSlug'),
    (0, public_decorator_1.Public)(),
    (0, swagger_1.ApiOperation)({ summary: 'Lấy danh sách bài viết đã xuất bản theo slug của tag (Public)' }),
    (0, swagger_1.ApiParam)({ name: 'tagSlug', type: String, description: 'Slug của tag' }),
    (0, swagger_1.ApiQuery)({ name: 'page', required: false, type: Number, description: 'Số trang' }),
    (0, swagger_1.ApiQuery)({ name: 'limit', required: false, type: Number, description: 'Số lượng bản ghi mỗi trang' }),
    (0, swagger_1.ApiQuery)({ name: 'search', required: false, type: String, description: 'Tìm kiếm theo từ khóa' }),
    (0, swagger_1.ApiQuery)({ name: 'postType', required: false, enum: cms_posts_entity_1.CmsPostType, description: 'Lọc theo loại bài viết' }),
    openapi.ApiResponse({ status: 200 }),
    __param(0, (0, common_1.Param)('tagSlug')),
    __param(1, (0, common_1.Query)()),
    __param(2, (0, common_1.Query)('postType')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, custom_pagination_query_dto_1.CustomPaginationQueryDto, String]),
    __metadata("design:returntype", Promise)
], ReadCmsPostsPublicController.prototype, "findByTagSlug", null);
exports.ReadCmsPostsPublicController = ReadCmsPostsPublicController = ReadCmsPostsPublicController_1 = __decorate([
    (0, swagger_1.ApiTags)('cms-posts-public'),
    (0, common_1.Controller)('cms/posts/public'),
    (0, public_decorator_1.Public)(),
    __metadata("design:paramtypes", [read_cms_posts_service_1.ReadCmsPostsService])
], ReadCmsPostsPublicController);
//# sourceMappingURL=read.cms-posts.public.controller.js.map
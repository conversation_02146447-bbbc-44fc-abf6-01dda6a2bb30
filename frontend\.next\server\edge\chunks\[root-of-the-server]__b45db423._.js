(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push(["chunks/[root-of-the-server]__b45db423._.js", {

"[externals]/node:buffer [external] (node:buffer, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("node:buffer", () => require("node:buffer"));

module.exports = mod;
}}),
"[externals]/node:async_hooks [external] (node:async_hooks, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("node:async_hooks", () => require("node:async_hooks"));

module.exports = mod;
}}),
"[project]/lib/constants/paths.ts [middleware-edge] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * Path constants for consistent usage across the application
 */ // Các đường dẫn không yêu cầu xác thực
__turbopack_context__.s({
    "ADMIN_PATHS": (()=>ADMIN_PATHS),
    "EXCLUDE_REDIRECT_PATHS": (()=>EXCLUDE_REDIRECT_PATHS),
    "PUBLIC_PATHS": (()=>PUBLIC_PATHS),
    "isAdminPath": (()=>isAdminPath),
    "isExcludeRedirectPath": (()=>isExcludeRedirectPath),
    "isPublicPath": (()=>isPublicPath)
});
const PUBLIC_PATHS = [
    '/login',
    '/register',
    '/forgot-password',
    '/reset-password'
];
const EXCLUDE_REDIRECT_PATHS = [
    '/login/callback' // Google OAuth callback
];
const ADMIN_PATHS = [
    '/admin'
];
const isPublicPath = (pathname)=>{
    return PUBLIC_PATHS.some((path)=>pathname === path || pathname.startsWith(`${path}/`));
};
const isAdminPath = (pathname)=>{
    return ADMIN_PATHS.some((path)=>pathname === path || pathname.startsWith(`${path}/`));
};
const isExcludeRedirectPath = (pathname)=>{
    return EXCLUDE_REDIRECT_PATHS.some((path)=>pathname === path || pathname.startsWith(`${path}/`));
};
}}),
"[project]/lib/api.ts [middleware-edge] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * API client cho ứng dụng
 */ __turbopack_context__.s({
    "api": (()=>api),
    "fetchApi": (()=>fetchApi)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$auth$2e$ts__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/auth.ts [middleware-edge] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$constants$2f$paths$2e$ts__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/constants/paths.ts [middleware-edge] (ecmascript)");
;
;
const NEXT_PUBLIC_API_URL = ("TURBOPACK compile-time value", "http://localhost:3168/api/v1") || "http://localhost:3000/api/v1";
// Biến để theo dõi các yêu cầu đang chờ refresh token
let isRefreshing = false;
let failedQueue = [];
/**
 * Xử lý hàng đợi các yêu cầu thất bại
 */ function processQueue(error, token = null) {
    failedQueue.forEach((prom)=>{
        if (error) {
            prom.reject(error);
        } else {
            prom.resolve(token);
        }
    });
    failedQueue = [];
}
/**
 * Hàm thử refresh token và thực hiện lại yêu cầu
 */ async function handleTokenRefresh() {
    try {
        if (!isRefreshing) {
            isRefreshing = true;
            const success = await __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$auth$2e$ts__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["authService"].tryRefreshToken();
            isRefreshing = false;
            if (success) {
                // Lấy token mới
                const newToken = __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$auth$2e$ts__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["authService"].getAccessToken();
                // Xử lý hàng đợi với token mới
                processQueue(null, newToken);
                // Lấy thông tin người dùng hiện tại
                const currentUser = __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$auth$2e$ts__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["authService"].getCurrentUser();
                if (currentUser) {
                    // Chỉ redirect nếu user đang ở trang không phù hợp với role
                    // Không redirect nếu user đã ở đúng trang của role họ
                    const currentPath = window.location.pathname;
                    const isAdmin = currentUser.roles?.includes('ADMIN');
                    // Chỉ redirect khi cần thiết và không phải trang public
                    const isOnPublicPath = (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$constants$2f$paths$2e$ts__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["isPublicPath"])(currentPath);
                    const shouldRedirect = !isOnPublicPath && (isAdmin && !currentPath.startsWith('/admin') || !isAdmin && currentPath.startsWith('/admin'));
                    if (shouldRedirect) {
                        __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$auth$2e$ts__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["authService"].redirectBasedOnRole(currentUser);
                    }
                }
                return newToken;
            } else {
                // Nếu refresh token thất bại, đăng xuất người dùng
                __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$auth$2e$ts__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["authService"].logout();
                processQueue(new Error('Refresh token failed'));
                return null;
            }
        } else {
            // Nếu đang refresh token, thêm yêu cầu vào hàng đợi
            return new Promise((resolve, reject)=>{
                failedQueue.push({
                    resolve,
                    reject
                });
            });
        }
    } catch (error) {
        console.error("API: Error during token refresh:", error);
        isRefreshing = false;
        processQueue(error);
        throw error;
    }
}
async function fetchApi(endpoint, options = {}) {
    const url = `${NEXT_PUBLIC_API_URL}${endpoint.startsWith('/') ? endpoint : `/${endpoint}`}`;
    // Hàm để thực hiện yêu cầu API
    const executeRequest = async (token)=>{
        // Tạo headers mới
        const headers = new Headers(options.headers);
        headers.set('Content-Type', 'application/json');
        // Thêm token xác thực nếu có
        const accessToken = token || (("TURBOPACK compile-time falsy", 0) ? ("TURBOPACK unreachable", undefined) : null);
        if (accessToken) {
            headers.set('Authorization', `Bearer ${accessToken}`);
        }
        const config = {
            ...options,
            headers
        };
        const response = await fetch(url, config);
        // Kiểm tra nếu response không thành công
        if (!response.ok) {
            const errorData = await response.json().catch(()=>({}));
            // Xử lý cấu trúc lỗi từ backend
            const errorMessage = errorData.message || errorData.data && errorData.data.message || `API request failed with status ${response.status}`;
            console.error('API Error:', errorData);
            // Tạo error object với thông tin chi tiết hơn
            const error = new Error(errorMessage);
            error.status = response.status;
            error.data = errorData;
            throw error;
        }
        // Parse JSON response
        const responseData = await response.json();
        // Kiểm tra cấu trúc response từ backend (ApiResponseDto)
        if (responseData.data !== undefined) {
            // Trường hợp response có cấu trúc ApiResponseDto
            return responseData.data;
        } else {
            // Trường hợp response không có cấu trúc ApiResponseDto
            console.warn('API response does not follow standard structure:', responseData);
            return responseData;
        }
    };
    try {
        // Thử thực hiện yêu cầu
        return await executeRequest();
    } catch (error) {
        // Nếu lỗi 401 Unauthorized và không phải là yêu cầu đăng nhập/đăng ký/refresh token
        if (error.status === 401 && !endpoint.includes('auth/login') && !endpoint.includes('auth/register') && !endpoint.includes('auth/refresh-token')) {
            try {
                // Thử refresh token
                const newToken = await handleTokenRefresh();
                if (newToken) {
                    // Thực hiện lại yêu cầu với token mới
                    return await executeRequest(newToken);
                }
            } catch (refreshError) {
                console.error('Error refreshing token:', refreshError);
                // Nếu refresh token thất bại, đăng xuất người dùng
                __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$auth$2e$ts__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["authService"].logout();
                throw error; // Trả về lỗi ban đầu
            }
        }
        console.error('API request error:', error);
        throw error;
    }
}
const api = {
    baseUrl: NEXT_PUBLIC_API_URL,
    getToken: ()=>{
        return ("TURBOPACK compile-time falsy", 0) ? ("TURBOPACK unreachable", undefined) : null;
    },
    get: (endpoint, options)=>fetchApi(endpoint, {
            ...options,
            method: 'GET'
        }),
    post: (endpoint, data, options)=>fetchApi(endpoint, {
            ...options,
            method: 'POST',
            body: data ? JSON.stringify(data) : undefined
        }),
    put: (endpoint, data, options)=>fetchApi(endpoint, {
            ...options,
            method: 'PUT',
            body: data ? JSON.stringify(data) : undefined
        }),
    patch: (endpoint, data, options)=>fetchApi(endpoint, {
            ...options,
            method: 'PATCH',
            body: data ? JSON.stringify(data) : undefined
        }),
    delete: (endpoint, options)=>fetchApi(endpoint, {
            ...options,
            method: 'DELETE'
        }),
    // Phương thức download file
    downloadFile: async (endpoint, format, filename)=>{
        const url = `${NEXT_PUBLIC_API_URL}${endpoint.startsWith('/') ? endpoint : `/${endpoint}`}`;
        const token = ("TURBOPACK compile-time falsy", 0) ? ("TURBOPACK unreachable", undefined) : null;
        try {
            // Sử dụng XMLHttpRequest thay vì fetch để xử lý tải file tốt hơn
            return new Promise((resolve, reject)=>{
                const xhr = new XMLHttpRequest();
                xhr.open('GET', url, true);
                xhr.responseType = 'blob';
                xhr.setRequestHeader('Authorization', ("TURBOPACK compile-time falsy", 0) ? ("TURBOPACK unreachable", undefined) : '');
                xhr.onload = function() {
                    if (this.status === 200) {
                        const blob = new Blob([
                            this.response
                        ], {
                            type: format === 'csv' ? 'text/csv' : 'application/json'
                        });
                        const downloadUrl = window.URL.createObjectURL(blob);
                        const a = document.createElement('a');
                        a.href = downloadUrl;
                        a.download = filename;
                        document.body.appendChild(a);
                        a.click();
                        // Cleanup
                        window.URL.revokeObjectURL(downloadUrl);
                        document.body.removeChild(a);
                        resolve(true);
                    } else {
                        reject(new Error(`Download failed: ${this.status} ${this.statusText}`));
                    }
                };
                xhr.onerror = function() {
                    reject(new Error('Network error occurred'));
                };
                xhr.send();
            });
        } catch (error) {
            console.error('Download error:', error);
            throw error;
        }
    }
};
}}),
"[project]/lib/auth.ts [middleware-edge] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * Service xử lý xác thực người dùng
 */ __turbopack_context__.s({
    "authService": (()=>authService),
    "decodeToken": (()=>decodeToken),
    "isTokenExpired": (()=>isTokenExpired),
    "isTokenExpiringSoon": (()=>isTokenExpiringSoon)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$api$2e$ts__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/api.ts [middleware-edge] (ecmascript)");
;
const decodeToken = (token)=>{
    try {
        if (!token) return null;
        // JWT token có 3 phần: header.payload.signature
        const base64Url = token.split('.')[1];
        if (!base64Url) return null;
        // Thay thế các ký tự đặc biệt trong base64Url
        const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');
        // Giải mã base64 thành JSON
        const jsonPayload = decodeURIComponent(atob(base64).split('').map((c)=>'%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2)).join(''));
        const payload = JSON.parse(jsonPayload);
        // Debug logging để kiểm tra payload
        console.log('decodeToken - JWT payload:', {
            sub: payload.sub,
            username: payload.username,
            roles: payload.roles,
            permissions: payload.permissions
        });
        // Chuyển đổi payload thành User
        const user = {
            id: payload.sub || payload.id || '',
            username: payload.username || '',
            email: payload.email || '',
            fullName: payload.fullName || payload.name || '',
            roles: payload.roles || [],
            permissions: payload.permissions || [],
            exp: payload.exp || 0
        };
        console.log('decodeToken - converted user:', {
            id: user.id,
            roles: user.roles,
            isAdmin: user.roles.includes('ADMIN')
        });
        return user;
    } catch (error) {
        console.error('Error decoding token:', error);
        return null;
    }
};
const isTokenExpired = (token)=>{
    try {
        if (!token) return true;
        const decodedToken = decodeToken(token);
        if (!decodedToken || !decodedToken.exp) return true;
        // exp là thời gian hết hạn tính bằng giây kể từ epoch (1/1/1970)
        const currentTime = Math.floor(Date.now() / 1000); // Chuyển đổi thời gian hiện tại sang giây
        // Trả về true nếu token đã hết hạn
        return decodedToken.exp < currentTime;
    } catch (error) {
        console.error('Error checking token expiration:', error);
        return true; // Nếu có lỗi, coi như token đã hết hạn
    }
};
const isTokenExpiringSoon = (token, thresholdSeconds = 300)=>{
    try {
        if (!token) return true;
        const decodedToken = decodeToken(token);
        if (!decodedToken || !decodedToken.exp) return true;
        const currentTime = Math.floor(Date.now() / 1000);
        // Trả về true nếu token sắp hết hạn trong vòng thresholdSeconds giây
        return decodedToken.exp - currentTime < thresholdSeconds;
    } catch (error) {
        console.error('Error checking token expiration:', error);
        return true;
    }
};
const authService = {
    /**
   * Đăng nhập người dùng
   */ async login (credentials) {
        try {
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$api$2e$ts__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["api"].post('auth/login', credentials);
            // Kiểm tra cấu trúc response
            const authData = response.data || response;
            // Lưu token vào localStorage và cookie
            localStorage.setItem('accessToken', authData.access_token);
            localStorage.setItem('refreshToken', authData.refresh_token);
            // Lưu token vào cookie để middleware có thể truy cập
            document.cookie = `token=${authData.access_token}; path=/; max-age=${60 * 60 * 24 * 7}; SameSite=Lax`; // 7 ngày
            // Nếu không có thông tin người dùng trong response, giải mã token để lấy
            if (!authData.user && authData.access_token) {
                const decodedUser = decodeToken(authData.access_token);
                if (decodedUser) {
                    authData.user = decodedUser;
                    this.saveUser(decodedUser);
                }
            } else if (authData.user) {
                // Lưu thông tin người dùng
                this.saveUser(authData.user);
            }
            return authData;
        } catch (error) {
            console.error('Login error:', error);
            throw error;
        }
    },
    /**
   * Đăng ký người dùng mới
   */ async register (credentials) {
        try {
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$api$2e$ts__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["api"].post('auth/register', credentials);
            // Kiểm tra cấu trúc response
            const authData = response.data || response;
            // Lưu token vào localStorage và cookie
            localStorage.setItem('accessToken', authData.access_token);
            localStorage.setItem('refreshToken', authData.refresh_token);
            // Lưu token vào cookie để middleware có thể truy cập
            document.cookie = `token=${authData.access_token}; path=/; max-age=${60 * 60 * 24 * 7}; SameSite=Lax`; // 7 ngày
            // Nếu không có thông tin người dùng trong response, giải mã token để lấy
            if (!authData.user && authData.access_token) {
                const decodedUser = decodeToken(authData.access_token);
                if (decodedUser) {
                    authData.user = decodedUser;
                    this.saveUser(decodedUser);
                }
            } else if (authData.user) {
                // Lưu thông tin người dùng
                this.saveUser(authData.user);
            }
            return authData;
        } catch (error) {
            console.error('Register error:', error);
            throw error;
        }
    },
    /**
   * Làm mới token
   */ async refreshToken (refreshToken) {
        try {
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$api$2e$ts__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["api"].post('auth/refresh-token', {
                refreshToken
            });
            // Kiểm tra cấu trúc response
            const authData = response.data || response;
            // Cập nhật token trong localStorage và cookie
            localStorage.setItem('accessToken', authData.access_token);
            localStorage.setItem('refreshToken', authData.refresh_token);
            // Cập nhật token trong cookie để middleware có thể truy cập
            document.cookie = `token=${authData.access_token}; path=/; max-age=${60 * 60 * 24 * 7}; SameSite=Lax`; // 7 ngày
            // Nếu không có thông tin người dùng trong response, giải mã token để lấy
            if (!authData.user && authData.access_token) {
                const decodedUser = decodeToken(authData.access_token);
                if (decodedUser) {
                    authData.user = decodedUser;
                    this.saveUser(decodedUser);
                }
            } else if (authData.user) {
                // Lưu thông tin người dùng
                this.saveUser(authData.user);
            }
            return authData;
        } catch (error) {
            console.error('Refresh token error:', error);
            // Nếu refresh token thất bại, đăng xuất người dùng
            this.logout();
            throw error;
        }
    },
    /**
   * Thử làm mới token nếu có refresh token
   */ async tryRefreshToken () {
        try {
            const refreshToken = this.getRefreshToken();
            if (!refreshToken) return false;
            const result = await this.refreshToken(refreshToken);
            // Đảm bảo user state được cập nhật sau refresh
            if (result && result.user) {
                // Trigger event để các component khác biết user đã được cập nhật
                window.dispatchEvent(new CustomEvent('auth:userUpdated', {
                    detail: {
                        user: result.user
                    }
                }));
            }
            // Việc điều hướng sẽ được xử lý ở handleTokenRefresh trong api.ts
            // Không điều hướng ở đây để tránh điều hướng kép
            return true;
        } catch (error) {
            console.error('Try refresh token error:', error);
            return false;
        }
    },
    /**
   * Điều hướng người dùng dựa trên vai trò
   * @param user - User object
   * @param forceRedirect - Có bắt buộc redirect không (mặc định: false)
   */ redirectBasedOnRole (user, forceRedirect = false) {
        if ("TURBOPACK compile-time truthy", 1) return;
        "TURBOPACK unreachable";
        // Ưu tiên ADMIN trước - kiểm tra roles an toàn
        const isAdmin = undefined;
        const currentPath = undefined;
    },
    /**
   * Đăng xuất người dùng
   */ logout () {
        localStorage.removeItem('accessToken');
        localStorage.removeItem('refreshToken');
        localStorage.removeItem('user');
        // Xóa cookie token
        document.cookie = 'token=; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT; SameSite=Lax';
        // Delay redirect một chút để UI có thời gian cập nhật
        if ("TURBOPACK compile-time falsy", 0) {
            "TURBOPACK unreachable";
        }
    },
    /**
   * Kiểm tra người dùng đã đăng nhập chưa
   */ isAuthenticated () {
        if ("TURBOPACK compile-time truthy", 1) return false;
        "TURBOPACK unreachable";
        // Kiểm tra token trong localStorage hoặc cookie
        const localToken = undefined;
        // Kiểm tra token trong cookie
        const cookies = undefined;
        let i;
    },
    /**
   * Lấy thông tin người dùng hiện tại
   */ getCurrentUser () {
        if ("TURBOPACK compile-time truthy", 1) return null;
        "TURBOPACK unreachable";
        const userJson = undefined;
    },
    /**
   * Lưu thông tin người dùng
   */ saveUser (user) {
        console.log('saveUser - saving to localStorage:', {
            id: user.id,
            roles: user.roles,
            hasRoles: !!user.roles
        });
        localStorage.setItem('user', JSON.stringify(user));
    },
    /**
   * Lấy token hiện tại
   */ getAccessToken () {
        if ("TURBOPACK compile-time truthy", 1) return null;
        "TURBOPACK unreachable";
    },
    /**
   * Lấy refresh token
   */ getRefreshToken () {
        if ("TURBOPACK compile-time truthy", 1) return null;
        "TURBOPACK unreachable";
    },
    /**
   * Xác minh số điện thoại cho người dùng hiện có
   * @param userId ID của người dùng
   * @param firebaseToken Firebase ID token
   * @returns Kết quả xác minh
   */ async verifyPhoneNumber (userId, firebaseToken) {
        try {
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$api$2e$ts__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["api"].post('auth/verify-phone-public', {
                userId,
                firebaseToken
            });
            return response.data || response;
        } catch (error) {
            console.error('Phone verification error:', error);
            throw error;
        }
    },
    /**
   * Unified OTP verification for both phone and email
   * @param verificationData Verification data
   * @returns Verification result with tokens
   */ async verifyOtp (verificationData) {
        try {
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$api$2e$ts__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["api"].post('auth/verify-otp', verificationData);
            // Kiểm tra cấu trúc response
            const authData = response.data || response;
            // Lưu token vào localStorage và cookie
            localStorage.setItem('accessToken', authData.access_token);
            localStorage.setItem('refreshToken', authData.refresh_token);
            // Lưu token vào cookie để middleware có thể truy cập
            document.cookie = `token=${authData.access_token}; path=/; max-age=${60 * 60 * 24 * 7}; SameSite=Lax`; // 7 ngày
            // Lưu thông tin user vào localStorage
            if (authData.user) {
                localStorage.setItem('user', JSON.stringify(authData.user));
            }
            return authData;
        } catch (error) {
            console.error('OTP verification error:', error);
            throw error;
        }
    }
};
}}),
"[project]/middleware.ts [middleware-edge] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "config": (()=>config),
    "middleware": (()=>middleware)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$opentelemetry$2b$_ee4fdf7136c0ae57e36e5bb24f18c6a1$2f$node_modules$2f$next$2f$dist$2f$esm$2f$api$2f$server$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/next@15.3.0_@opentelemetry+_ee4fdf7136c0ae57e36e5bb24f18c6a1/node_modules/next/dist/esm/api/server.js [middleware-edge] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$opentelemetry$2b$_ee4fdf7136c0ae57e36e5bb24f18c6a1$2f$node_modules$2f$next$2f$dist$2f$esm$2f$server$2f$web$2f$spec$2d$extension$2f$response$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/next@15.3.0_@opentelemetry+_ee4fdf7136c0ae57e36e5bb24f18c6a1/node_modules/next/dist/esm/server/web/spec-extension/response.js [middleware-edge] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$auth$2e$ts__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/auth.ts [middleware-edge] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$constants$2f$paths$2e$ts__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/constants/paths.ts [middleware-edge] (ecmascript)");
;
;
;
function middleware(request) {
    const { pathname } = request.nextUrl;
    // Skip middleware for static assets, API routes, and Next.js internals
    if (pathname.startsWith('/_next/') || pathname.startsWith('/api/') || pathname.startsWith('/static/') || pathname.includes('.') || // Files with extensions (images, css, js, etc.)
    pathname === '/favicon_sgs.ico') {
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$opentelemetry$2b$_ee4fdf7136c0ae57e36e5bb24f18c6a1$2f$node_modules$2f$next$2f$dist$2f$esm$2f$server$2f$web$2f$spec$2d$extension$2f$response$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["NextResponse"].next();
    }
    // Kiểm tra xem đường dẫn hiện tại có phải là public path không
    const isPublic = (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$constants$2f$paths$2e$ts__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["isPublicPath"])(pathname);
    // Kiểm tra xem đường dẫn hiện tại có phải là admin path không
    const isAdmin = (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$constants$2f$paths$2e$ts__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["isAdminPath"])(pathname);
    // Lấy token từ cookie
    const token = request.cookies.get('token')?.value;
    // Nếu không có token và đang truy cập trang yêu cầu xác thực
    if (!token && !isPublic) {
        // Chuyển hướng đến trang đăng nhập
        const loginUrl = new URL('/login', request.url);
        loginUrl.searchParams.set('callbackUrl', encodeURI(pathname));
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$opentelemetry$2b$_ee4fdf7136c0ae57e36e5bb24f18c6a1$2f$node_modules$2f$next$2f$dist$2f$esm$2f$server$2f$web$2f$spec$2d$extension$2f$response$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["NextResponse"].redirect(loginUrl);
    }
    // Nếu có token
    if (token) {
        // Kiểm tra xem đường dẫn hiện tại có nằm trong danh sách loại trừ không
        const isExcluded = (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$constants$2f$paths$2e$ts__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["isExcludeRedirectPath"])(pathname);
        // Nếu đang truy cập trang công khai (như login, register) và không phải là trang loại trừ
        if (isPublic && !isExcluded && pathname !== '/reset-password') {
            try {
                // Giải mã token để kiểm tra vai trò
                const user = (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$auth$2e$ts__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["decodeToken"])(token);
                // Nếu là admin, chuyển hướng đến trang admin dashboard
                if (user?.roles?.includes('ADMIN')) {
                    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$opentelemetry$2b$_ee4fdf7136c0ae57e36e5bb24f18c6a1$2f$node_modules$2f$next$2f$dist$2f$esm$2f$server$2f$web$2f$spec$2d$extension$2f$response$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["NextResponse"].redirect(new URL('/admin/dashboard', request.url));
                }
                // Nếu không phải admin, chuyển hướng đến trang dashboard thông thường
                return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$opentelemetry$2b$_ee4fdf7136c0ae57e36e5bb24f18c6a1$2f$node_modules$2f$next$2f$dist$2f$esm$2f$server$2f$web$2f$spec$2d$extension$2f$response$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["NextResponse"].redirect(new URL('/dashboard', request.url));
            } catch (error) {
                // Nếu có lỗi khi giải mã token, chuyển hướng đến trang dashboard mặc định
                return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$opentelemetry$2b$_ee4fdf7136c0ae57e36e5bb24f18c6a1$2f$node_modules$2f$next$2f$dist$2f$esm$2f$server$2f$web$2f$spec$2d$extension$2f$response$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["NextResponse"].redirect(new URL('/dashboard', request.url));
            }
        }
        // Nếu đang truy cập trang admin nhưng không phải là admin
        if (isAdmin) {
            try {
                // Giải mã token để kiểm tra vai trò
                const user = (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$auth$2e$ts__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["decodeToken"])(token);
                // Nếu không phải admin, chuyển hướng đến trang dashboard thông thường
                if (!user?.roles?.includes('ADMIN')) {
                    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$opentelemetry$2b$_ee4fdf7136c0ae57e36e5bb24f18c6a1$2f$node_modules$2f$next$2f$dist$2f$esm$2f$server$2f$web$2f$spec$2d$extension$2f$response$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["NextResponse"].redirect(new URL('/dashboard', request.url));
                }
            } catch (error) {
                // Nếu có lỗi khi giải mã token, chuyển hướng đến trang dashboard mặc định
                return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$opentelemetry$2b$_ee4fdf7136c0ae57e36e5bb24f18c6a1$2f$node_modules$2f$next$2f$dist$2f$esm$2f$server$2f$web$2f$spec$2d$extension$2f$response$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["NextResponse"].redirect(new URL('/dashboard', request.url));
            }
        }
    }
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$opentelemetry$2b$_ee4fdf7136c0ae57e36e5bb24f18c6a1$2f$node_modules$2f$next$2f$dist$2f$esm$2f$server$2f$web$2f$spec$2d$extension$2f$response$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["NextResponse"].next();
}
const config = {
    matcher: [
        /*
     * Match all request paths except:
     * 1. /api routes
     * 2. /_next (Next.js internals)
     * 3. /_static (inside /public)
     * 4. Static files (images, css, js, etc.)
     * 5. favicon.ico, sitemap.xml
     */ '/((?!api|_next|_static|.*\\.|favicon.ico|sitemap.xml).*)'
    ]
};
}}),
}]);

//# sourceMappingURL=%5Broot-of-the-server%5D__b45db423._.js.map
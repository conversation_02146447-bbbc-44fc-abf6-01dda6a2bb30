import { BaseActivityLogService } from './base.activity-log.service';
import { ActivityLogDto } from '../dto/activity-log.dto';
import { CustomPaginationQueryDto } from '../../common/dto/custom-pagination-query.dto';
export declare class ReadActivityLogService extends BaseActivityLogService {
    findAll(params: CustomPaginationQueryDto): Promise<{
        data: ActivityLogDto[];
        total: number;
    }>;
    findOne(id: string, relations?: string[]): Promise<ActivityLogDto>;
    findByUserId(userId: string, params: CustomPaginationQueryDto): Promise<{
        data: ActivityLogDto[];
        total: number;
    }>;
    findByAction(action: string, params: CustomPaginationQueryDto): Promise<{
        data: ActivityLogDto[];
        total: number;
    }>;
    findByModule(module: string, params: CustomPaginationQueryDto): Promise<{
        data: ActivityLogDto[];
        total: number;
    }>;
    search(keyword: string, params: CustomPaginationQueryDto): Promise<{
        data: ActivityLogDto[];
        total: number;
    }>;
    count(filter?: string): Promise<number>;
}

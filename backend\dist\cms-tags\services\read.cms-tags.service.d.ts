import { Repository, DataSource } from 'typeorm';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { BaseCmsTagsService } from './base.cms-tags.service';
import { CmsTags } from '../entity/cms-tags.entity';
import { CmsTagDto } from '../dto/cms-tag.dto';
import { CustomPaginationQueryDto } from '../../common/dto/custom-pagination-query.dto';
export declare class ReadCmsTagsService extends BaseCmsTagsService {
    protected readonly tagRepository: Repository<CmsTags>;
    protected readonly dataSource: DataSource;
    protected readonly eventEmitter: EventEmitter2;
    constructor(tagRepository: Repository<CmsTags>, dataSource: DataSource, eventEmitter: EventEmitter2);
    findAll(params: CustomPaginationQueryDto): Promise<{
        data: CmsTagDto[];
        total: number;
    }>;
    count(filter?: string): Promise<number>;
    findOne(id: string, relations?: string[]): Promise<CmsTagDto | null>;
    findOneOrFail(id: string, relations?: string[]): Promise<CmsTagDto | null>;
    findBySlugPublic(slug: string): Promise<CmsTagDto | null>;
    search(keyword: string, params: CustomPaginationQueryDto): Promise<{
        data: CmsTagDto[];
        total: number;
    }>;
    findDeleted(params: CustomPaginationQueryDto): Promise<{
        data: CmsTagDto[];
        total: number;
    }>;
    getStatistics(): Promise<{
        total: number;
        mostUsed: Array<{
            name: string;
            slug: string;
            postsCount: number;
        }>;
    }>;
    getPopularTags(limit?: number): Promise<CmsTagDto[]>;
    findByNames(names: string[]): Promise<CmsTagDto[]>;
}

import { EventEmitter2 } from '@nestjs/event-emitter';
import { VnpayService } from './vnpay.service';
import { VnpayTransactionRepository } from '../repositories/vnpay-transaction.repository';
import { VnpayTransaction } from '../entities/vnpay-transaction.entity';
import { PaymentRequest, PaymentResponse, PaymentCallback, PaymentQueryRequest, PaymentQueryResponse, PaymentRefundRequest, PaymentRefundResponse, PaymentEventHandler, IVnpayPaymentService } from '../interfaces/payment-integration.interface';
export declare class VnpayPaymentService implements IVnpayPaymentService {
    private readonly vnpayService;
    private readonly vnpayTransactionRepository;
    private readonly eventEmitter;
    private readonly logger;
    private eventHandler?;
    constructor(vnpayService: VnpayService, vnpayTransactionRepository: VnpayTransactionRepository, eventEmitter: EventEmitter2);
    setEventHandler(handler: PaymentEventHandler): void;
    createPayment(request: PaymentRequest): Promise<PaymentResponse>;
    handleReturnCallback(params: Record<string, string>): Promise<PaymentCallback>;
    handleIpnCallback(params: Record<string, string>): Promise<PaymentCallback>;
    queryTransaction(request: PaymentQueryRequest): Promise<PaymentQueryResponse>;
    refundTransaction(request: PaymentRefundRequest): Promise<PaymentRefundResponse>;
    getTransaction(merchantTxnRef: string): Promise<VnpayTransaction | null>;
    getStatistics(filter?: any): Promise<any>;
    private validatePaymentRequest;
    private generateMerchantTxnRef;
    private processSuccessfulPayment;
    private processFailedPayment;
    private createFailedCallback;
}

"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.WebsocketsModule = void 0;
const common_1 = require("@nestjs/common");
const base_gateway_1 = require("./gateways/base.gateway");
const ws_jwt_guard_1 = require("./guards/ws-jwt.guard");
const websocket_emitter_service_1 = require("./services/websocket-emitter.service");
const config_1 = require("@nestjs/config");
const jwt_1 = require("@nestjs/jwt");
const polygon_forex_service_1 = require("./services/polygon-forex.service");
const forex_quote_gateway_1 = require("./gateways/forex-quote.gateway");
let WebsocketsModule = class WebsocketsModule {
};
exports.WebsocketsModule = WebsocketsModule;
exports.WebsocketsModule = WebsocketsModule = __decorate([
    (0, common_1.Global)(),
    (0, common_1.Module)({
        imports: [
            jwt_1.JwtModule.registerAsync({
                imports: [config_1.ConfigModule],
                useFactory: async (configService) => ({
                    secret: configService.get('JWT_SECRET'),
                    signOptions: {
                        expiresIn: configService.get('JWT_EXPIRATION_TIME', '1h'),
                    },
                }),
                inject: [config_1.ConfigService],
            }),
        ],
        providers: [
            base_gateway_1.BaseGateway,
            ws_jwt_guard_1.WsJwtGuard,
            websocket_emitter_service_1.WebSocketEmitterService,
            polygon_forex_service_1.PolygonForexService,
            forex_quote_gateway_1.ForexQuoteGateway,
            common_1.Logger,
            {
                provide: 'WEBSOCKET_CONFIG',
                useFactory: (configService) => ({
                    cors: {
                        origin: configService.get('CORS_ORIGIN', '*').split(','),
                        credentials: true,
                    },
                    transports: ['websocket', 'polling'],
                    allowEIO3: true,
                    pingTimeout: 60000,
                    pingInterval: 25000,
                }),
                inject: [config_1.ConfigService],
            },
        ],
        exports: [websocket_emitter_service_1.WebSocketEmitterService, polygon_forex_service_1.PolygonForexService, 'WEBSOCKET_CONFIG', jwt_1.JwtModule],
    })
], WebsocketsModule);
//# sourceMappingURL=websockets.module.js.map
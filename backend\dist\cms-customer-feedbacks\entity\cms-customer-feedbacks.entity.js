"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CmsCustomerFeedbacks = exports.CmsCustomerFeedbackStatus = void 0;
const openapi = require("@nestjs/swagger");
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
const typeorm_1 = require("typeorm");
const base_entity_1 = require("../../common/entities/base.entity");
const user_entity_1 = require("../../users/entities/user.entity");
var CmsCustomerFeedbackStatus;
(function (CmsCustomerFeedbackStatus) {
    CmsCustomerFeedbackStatus["PENDING"] = "pending";
    CmsCustomerFeedbackStatus["APPROVED"] = "approved";
    CmsCustomerFeedbackStatus["REJECTED"] = "rejected";
})(CmsCustomerFeedbackStatus || (exports.CmsCustomerFeedbackStatus = CmsCustomerFeedbackStatus = {}));
let CmsCustomerFeedbacks = class CmsCustomerFeedbacks extends base_entity_1.BaseEntity {
    customerName;
    customerTitleCompany;
    feedbackText;
    rating;
    avatarUrl;
    productServiceName;
    status;
    approvedBy;
    approver;
    getEntityName() {
        return 'cms_customer_feedbacks';
    }
    isApproved() {
        return this.status === CmsCustomerFeedbackStatus.APPROVED;
    }
    isPending() {
        return this.status === CmsCustomerFeedbackStatus.PENDING;
    }
    isRejected() {
        return this.status === CmsCustomerFeedbackStatus.REJECTED;
    }
    getRatingText() {
        if (!this.rating)
            return 'Chưa đánh giá';
        const ratingTexts = {
            1: 'Rất không hài lòng',
            2: 'Không hài lòng',
            3: 'Bình thường',
            4: 'Hài lòng',
            5: 'Rất hài lòng'
        };
        return ratingTexts[this.rating] || 'Không xác định';
    }
    getRatingColor() {
        if (!this.rating)
            return '#gray';
        const ratingColors = {
            1: '#ff4444',
            2: '#ff8800',
            3: '#ffbb33',
            4: '#00C851',
            5: '#007E33'
        };
        return ratingColors[this.rating] || '#gray';
    }
    getDisplayName() {
        if (this.customerTitleCompany) {
            return `${this.customerName} - ${this.customerTitleCompany}`;
        }
        return this.customerName;
    }
    static _OPENAPI_METADATA_FACTORY() {
        return { customerName: { required: true, type: () => String, maxLength: 255 }, customerTitleCompany: { required: false, type: () => String, nullable: true, maxLength: 255 }, feedbackText: { required: true, type: () => String }, rating: { required: false, type: () => Number, nullable: true, minimum: 1, maximum: 5 }, avatarUrl: { required: false, type: () => String, nullable: true, format: "uri" }, productServiceName: { required: false, type: () => String, nullable: true, maxLength: 255 }, status: { required: true, enum: require("./cms-customer-feedbacks.entity").CmsCustomerFeedbackStatus }, approvedBy: { required: false, type: () => String, nullable: true, format: "uuid" }, approver: { required: false, type: () => require("../../users/entities/user.entity").User } };
    }
};
exports.CmsCustomerFeedbacks = CmsCustomerFeedbacks;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Tên khách hàng',
        example: 'Nguyễn Văn A',
    }),
    (0, class_validator_1.IsNotEmpty)({ message: 'Tên khách hàng không được để trống' }),
    (0, class_validator_1.IsString)({ message: 'Tên khách hàng phải là chuỗi' }),
    (0, class_validator_1.MaxLength)(255, { message: 'Tên khách hàng không được vượt quá 255 ký tự' }),
    (0, typeorm_1.Column)({ type: 'varchar', length: 255, nullable: false, name: 'customer_name' }),
    __metadata("design:type", String)
], CmsCustomerFeedbacks.prototype, "customerName", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Chức danh, công ty của khách hàng',
        example: 'Giám đốc, Công ty ABC',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)({ message: 'Chức danh, công ty phải là chuỗi' }),
    (0, class_validator_1.MaxLength)(255, { message: 'Chức danh, công ty không được vượt quá 255 ký tự' }),
    (0, typeorm_1.Column)({ type: 'varchar', length: 255, nullable: true, name: 'customer_title_company' }),
    __metadata("design:type", Object)
], CmsCustomerFeedbacks.prototype, "customerTitleCompany", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Nội dung phản hồi',
        example: 'Dịch vụ rất tốt, tôi rất hài lòng với chất lượng sản phẩm.',
    }),
    (0, class_validator_1.IsNotEmpty)({ message: 'Nội dung phản hồi không được để trống' }),
    (0, class_validator_1.IsString)({ message: 'Nội dung phản hồi phải là chuỗi' }),
    (0, typeorm_1.Column)({ type: 'text', nullable: false, name: 'feedback_text' }),
    __metadata("design:type", String)
], CmsCustomerFeedbacks.prototype, "feedbackText", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Đánh giá sao (1-5)',
        example: 5,
        minimum: 1,
        maximum: 5,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsInt)({ message: 'Đánh giá phải là số nguyên' }),
    (0, class_validator_1.Min)(1, { message: 'Đánh giá tối thiểu là 1 sao' }),
    (0, class_validator_1.Max)(5, { message: 'Đánh giá tối đa là 5 sao' }),
    (0, typeorm_1.Column)({ type: 'smallint', nullable: true, name: 'rating' }),
    __metadata("design:type", Object)
], CmsCustomerFeedbacks.prototype, "rating", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'URL ảnh đại diện của khách hàng',
        example: 'https://example.com/avatar.jpg',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsUrl)({}, { message: 'URL ảnh đại diện không hợp lệ' }),
    (0, typeorm_1.Column)({ type: 'text', nullable: true, name: 'avatar_url' }),
    __metadata("design:type", Object)
], CmsCustomerFeedbacks.prototype, "avatarUrl", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Tên sản phẩm/dịch vụ được đánh giá',
        example: 'Dịch vụ mua bán vàng',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)({ message: 'Tên sản phẩm/dịch vụ phải là chuỗi' }),
    (0, class_validator_1.MaxLength)(255, { message: 'Tên sản phẩm/dịch vụ không được vượt quá 255 ký tự' }),
    (0, typeorm_1.Column)({ type: 'varchar', length: 255, nullable: true, name: 'product_service_name' }),
    __metadata("design:type", Object)
], CmsCustomerFeedbacks.prototype, "productServiceName", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Trạng thái feedback',
        example: CmsCustomerFeedbackStatus.PENDING,
        enum: CmsCustomerFeedbackStatus,
        default: CmsCustomerFeedbackStatus.PENDING,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)(CmsCustomerFeedbackStatus, { message: 'Trạng thái feedback không hợp lệ' }),
    (0, typeorm_1.Column)({
        type: 'varchar',
        length: 50,
        nullable: false,
        default: CmsCustomerFeedbackStatus.PENDING,
        name: 'status'
    }),
    __metadata("design:type", String)
], CmsCustomerFeedbacks.prototype, "status", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'ID người duyệt feedback',
        example: '550e8400-e29b-41d4-a716-************',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsUUID)('4', { message: 'ID người duyệt phải là UUID hợp lệ' }),
    (0, typeorm_1.Column)({ type: 'uuid', nullable: true, name: 'approved_by' }),
    __metadata("design:type", Object)
], CmsCustomerFeedbacks.prototype, "approvedBy", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => user_entity_1.User, { nullable: true }),
    (0, typeorm_1.JoinColumn)({ name: 'approved_by' }),
    __metadata("design:type", user_entity_1.User)
], CmsCustomerFeedbacks.prototype, "approver", void 0);
exports.CmsCustomerFeedbacks = CmsCustomerFeedbacks = __decorate([
    (0, typeorm_1.Entity)('cms_customer_feedbacks'),
    (0, typeorm_1.Index)(['status']),
    (0, typeorm_1.Index)(['rating']),
    (0, typeorm_1.Index)(['productServiceName']),
    (0, typeorm_1.Index)(['approvedBy'])
], CmsCustomerFeedbacks);
//# sourceMappingURL=cms-customer-feedbacks.entity.js.map
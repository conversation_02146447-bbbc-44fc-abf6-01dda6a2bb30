import { Logger } from '@nestjs/common';
import { Repository, DataSource } from 'typeorm';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { SystemConfig } from '../entities/system-config.entity';
import { SystemConfigDto } from '../dto/system-config.dto';
import { UpdateSystemConfigDto } from '../dto/update-system-config.dto';
import { BaseSystemConfigService } from './base.system-config.service';
import { BulkUpdateSystemConfigItemDto } from '../dto/bulk-update-system-config.dto';
export declare class UpdateSystemConfigService extends BaseSystemConfigService {
    protected readonly systemConfigRepository: Repository<SystemConfig>;
    protected readonly dataSource: DataSource;
    protected readonly eventEmitter: EventEmitter2;
    protected logger: Logger;
    constructor(systemConfigRepository: Repository<SystemConfig>, dataSource: DataSource, eventEmitter: EventEmitter2);
    update(id: string, updateSystemConfigDto: UpdateSystemConfigDto): Promise<SystemConfigDto>;
    updateByKey(key: string, updateSystemConfigDto: UpdateSystemConfigDto): Promise<SystemConfigDto>;
    updateBulk(items: BulkUpdateSystemConfigItemDto[]): Promise<SystemConfigDto[]>;
}

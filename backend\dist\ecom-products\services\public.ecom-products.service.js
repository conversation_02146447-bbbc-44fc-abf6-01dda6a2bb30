"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PublicEcomProductsService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const event_emitter_1 = require("@nestjs/event-emitter");
const class_transformer_1 = require("class-transformer");
const base_ecom_products_service_1 = require("./base.ecom-products.service");
const ecom_product_public_dto_1 = require("../dto/ecom-product.public.dto");
const custom_pagination_query_dto_1 = require("../../common/dto/custom-pagination-query.dto");
const ecom_products_entity_1 = require("../entity/ecom-products.entity");
const read_ecom_product_categories_service_1 = require("../../ecom-product-categories/services/read.ecom-product-categories.service");
const ecom_product_category_public_dto_1 = require("../../ecom-product-categories/dto/ecom-product-category.public.dto");
let PublicEcomProductsService = class PublicEcomProductsService extends base_ecom_products_service_1.BaseEcomProductsService {
    ecomProductRepository;
    dataSource;
    eventEmitter;
    readEcomProductCategoriesService;
    shuffleArray(array) {
        if (!array || array.length <= 1) {
            return [...array];
        }
        const shuffled = [...array];
        for (let i = shuffled.length - 1; i > 0; i--) {
            const j = Math.floor(Math.random() * (i + 1));
            [shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]];
        }
        this.logger.debug(`Shuffled array from ${array.length} items`);
        return shuffled;
    }
    constructor(ecomProductRepository, dataSource, eventEmitter, readEcomProductCategoriesService) {
        super(ecomProductRepository, dataSource, eventEmitter);
        this.ecomProductRepository = ecomProductRepository;
        this.dataSource = dataSource;
        this.eventEmitter = eventEmitter;
        this.readEcomProductCategoriesService = readEcomProductCategoriesService;
    }
    toPublicDto(product) {
        return (0, class_transformer_1.plainToInstance)(ecom_product_public_dto_1.EcomProductPublicDto, product, {
            excludeExtraneousValues: true,
            enableImplicitConversion: true,
            exposeDefaultValues: true,
            exposeUnsetFields: false,
        });
    }
    async findById(id) {
        try {
            this.logger.debug(`Đang tìm sản phẩm public theo ID: ${id}`);
            const requiredRelations = ['category'];
            const validatedRelations = this.validateRelations(requiredRelations);
            const queryBuilder = this.ecomProductRepository
                .createQueryBuilder('product')
                .where('product.id = :id', { id })
                .andWhere('product.isActive = :isActive', { isActive: true })
                .andWhere('product.isDeleted = :isDeleted', { isDeleted: false });
            validatedRelations.forEach(relation => {
                queryBuilder.leftJoinAndSelect(`product.${relation}`, relation);
            });
            const product = await queryBuilder.getOne();
            if (!product) {
                throw new common_1.NotFoundException(`Không tìm thấy sản phẩm với ID: ${id}`);
            }
            return this.toPublicDto(product);
        }
        catch (error) {
            this.logger.error(`Lỗi khi tìm sản phẩm public theo ID: ${error.message}`, error.stack);
            if (error instanceof common_1.NotFoundException) {
                throw error;
            }
            throw new common_1.InternalServerErrorException(`Không thể tìm sản phẩm: ${error.message}`);
        }
    }
    async findByCode(code) {
        try {
            this.logger.debug(`Đang tìm sản phẩm public theo mã: ${code}`);
            const requiredRelations = ['category'];
            const validatedRelations = this.validateRelations(requiredRelations);
            const queryBuilder = this.ecomProductRepository
                .createQueryBuilder('product')
                .where('product.productCode = :code', { code })
                .andWhere('product.isActive = :isActive', { isActive: true })
                .andWhere('product.isDeleted = :isDeleted', { isDeleted: false });
            validatedRelations.forEach(relation => {
                queryBuilder.leftJoinAndSelect(`product.${relation}`, relation);
            });
            const product = await queryBuilder.getOne();
            if (!product) {
                throw new common_1.NotFoundException(`Không tìm thấy sản phẩm với mã: ${code}`);
            }
            return this.toPublicDto(product);
        }
        catch (error) {
            this.logger.error(`Lỗi khi tìm sản phẩm public theo mã: ${error.message}`, error.stack);
            if (error instanceof common_1.NotFoundException) {
                throw error;
            }
            throw new common_1.InternalServerErrorException(`Không thể tìm sản phẩm theo mã: ${error.message}`);
        }
    }
    async findBySlug(slug) {
        try {
            this.logger.debug(`Đang tìm sản phẩm public theo slug: ${slug}`);
            const requiredRelations = ['category'];
            const validatedRelations = this.validateRelations(requiredRelations);
            const queryBuilder = this.ecomProductRepository
                .createQueryBuilder('product')
                .where('product.slug = :slug', { slug })
                .andWhere('product.isActive = :isActive', { isActive: true })
                .andWhere('product.isDeleted = :isDeleted', { isDeleted: false });
            validatedRelations.forEach(relation => {
                queryBuilder.leftJoinAndSelect(`product.${relation}`, relation);
            });
            const product = await queryBuilder.getOne();
            if (!product) {
                throw new common_1.NotFoundException(`Không tìm thấy sản phẩm với slug: ${slug}`);
            }
            return this.toPublicDto(product);
        }
        catch (error) {
            this.logger.error(`Lỗi khi tìm sản phẩm public theo slug: ${error.message}`, error.stack);
            if (error instanceof common_1.NotFoundException) {
                throw error;
            }
            throw new common_1.InternalServerErrorException(`Không thể tìm sản phẩm theo slug: ${error.message}`);
        }
    }
    async findAll(params) {
        try {
            const { limit, page, search, filter } = params;
            const skip = (page - 1) * limit;
            const requiredRelations = ['category'];
            const validatedRelations = this.validateRelations(requiredRelations);
            const queryBuilder = this.ecomProductRepository
                .createQueryBuilder('product')
                .where('product.isActive = :isActive', { isActive: true })
                .andWhere('product.isDeleted = :isDeleted', { isDeleted: false });
            if (search) {
                queryBuilder.andWhere(new typeorm_2.Brackets(qb => {
                    qb.where('LOWER(product.productName) LIKE LOWER(:search)', { search: `%${search}%` })
                        .orWhere('LOWER(product.productCode) LIKE LOWER(:search)', { search: `%${search}%` })
                        .orWhere('LOWER(product.description) LIKE LOWER(:search)', { search: `%${search}%` });
                }));
            }
            if (filter) {
                const filters = filter.split(',');
                const filterGroups = {};
                filters.forEach(filterItem => {
                    const [field, value] = filterItem.split(':');
                    if (field && value) {
                        if (!filterGroups[field]) {
                            filterGroups[field] = [];
                        }
                        filterGroups[field].push(value);
                    }
                });
                Object.entries(filterGroups).forEach(([field, values]) => {
                    if (values.length === 1) {
                        queryBuilder.andWhere(`product.${field} = :${field}`, { [field]: values[0] });
                    }
                    else {
                        queryBuilder.andWhere(`product.${field} IN (:...${field})`, { [field]: values });
                    }
                });
            }
            validatedRelations.forEach(relation => {
                queryBuilder.leftJoinAndSelect(`product.${relation}`, relation);
            });
            const sortOptions = params.sortOptions || [];
            if (sortOptions.length > 0) {
                sortOptions.forEach((option, index) => {
                    if (option.field.includes('.')) {
                        const [relation, field] = option.field.split('.');
                        if (index === 0) {
                            queryBuilder.orderBy(`${relation}.${field}`, option.order);
                        }
                        else {
                            queryBuilder.addOrderBy(`${relation}.${field}`, option.order);
                        }
                    }
                    else {
                        if (index === 0) {
                            queryBuilder.orderBy(`product.${option.field}`, option.order);
                        }
                        else {
                            queryBuilder.addOrderBy(`product.${option.field}`, option.order);
                        }
                    }
                });
            }
            else {
                queryBuilder.orderBy('product.createdAt', 'DESC');
            }
            queryBuilder.skip(skip).take(limit);
            const [products, total] = await queryBuilder.getManyAndCount();
            const productDtos = products.map(product => this.toPublicDto(product));
            return { data: productDtos, total };
        }
        catch (error) {
            this.logger.error(`Lỗi khi tìm tất cả sản phẩm public: ${error.message}`, error.stack);
            throw new common_1.InternalServerErrorException(`Không thể tìm tất cả sản phẩm: ${error.message}`);
        }
    }
    async findByCategory(categorySlug, params) {
        try {
            const requiredRelations = ['category'];
            const validatedRelations = this.validateRelations(requiredRelations);
            const { limit, page, search, filter, sortOptions } = params;
            const skip = (page - 1) * limit;
            const queryBuilder = this.ecomProductRepository
                .createQueryBuilder('product')
                .leftJoinAndSelect('product.category', 'category')
                .where('product.isActive = :isActive', { isActive: true })
                .andWhere('product.isDeleted = :isDeleted', { isDeleted: false })
                .andWhere('category.slug = :categorySlug', { categorySlug });
            if (search) {
                queryBuilder.andWhere(new typeorm_2.Brackets(qb => {
                    qb.where('LOWER(product.productName) LIKE LOWER(:search)', { search: `%${search}%` })
                        .orWhere('LOWER(product.productCode) LIKE LOWER(:search)', { search: `%${search}%` })
                        .orWhere('LOWER(product.description) LIKE LOWER(:search)', { search: `%${search}%` });
                }));
            }
            if (filter) {
                const filters = filter.split(',');
                const filterGroups = {};
                filters.forEach(filterItem => {
                    const [field, value] = filterItem.split(':');
                    if (field && value && field !== 'categoryId') {
                        if (!filterGroups[field]) {
                            filterGroups[field] = [];
                        }
                        filterGroups[field].push(value);
                    }
                });
                Object.entries(filterGroups).forEach(([field, values]) => {
                    if (values.length === 1) {
                        queryBuilder.andWhere(`product.${field} = :${field}`, { [field]: values[0] });
                    }
                    else {
                        queryBuilder.andWhere(`product.${field} IN (:...${field})`, { [field]: values });
                    }
                });
            }
            validatedRelations.forEach(relation => {
                queryBuilder.leftJoinAndSelect(`product.${relation}`, relation);
            });
            if (sortOptions && sortOptions.length > 0) {
                sortOptions.forEach((option, index) => {
                    if (option.field.includes('.')) {
                        const [relation, field] = option.field.split('.');
                        if (index === 0) {
                            queryBuilder.orderBy(`${relation}.${field}`, option.order);
                        }
                        else {
                            queryBuilder.addOrderBy(`${relation}.${field}`, option.order);
                        }
                    }
                    else {
                        if (index === 0) {
                            queryBuilder.orderBy(`product.${option.field}`, option.order);
                        }
                        else {
                            queryBuilder.addOrderBy(`product.${option.field}`, option.order);
                        }
                    }
                });
            }
            else {
                queryBuilder.orderBy('product.createdAt', 'DESC');
            }
            queryBuilder.skip(skip).take(limit);
            const [products, total] = await queryBuilder.getManyAndCount();
            const productDtos = products.map(product => this.toPublicDto(product));
            return { data: productDtos, total };
        }
        catch (error) {
            this.logger.error(`Lỗi khi tìm sản phẩm public theo danh mục (slug): ${error.message}`, error.stack);
            throw new common_1.InternalServerErrorException(`Không thể tìm sản phẩm theo danh mục: ${error.message}`);
        }
    }
    async findHomepageSections(productsPerCategory = 4) {
        try {
            this.logger.debug(`Đang tìm tất cả danh mục có sản phẩm với ${productsPerCategory} sản phẩm mỗi danh mục cho trang chủ`);
            this.logger.debug('Bắt đầu lấy danh sách danh mục active');
            this.logger.debug(`productsPerCategory: ${productsPerCategory}`);
            const categoriesQuery = Object.assign(new custom_pagination_query_dto_1.CustomPaginationQueryDto(), {
                page: 1,
                limit: 1000,
                search: '',
                sort: 'createdAt:ASC',
                filter: ''
            });
            const categoriesResult = await this.readEcomProductCategoriesService.getActiveProductCategories(categoriesQuery);
            const categories = categoriesResult.data || [];
            this.logger.debug(`Tìm thấy ${categories?.length || 0} danh mục`);
            categories.forEach((cat, index) => {
                this.logger.debug(`Category ${index}: ID=${cat.id}, Name=${cat.name}, Type=${typeof cat.id}`);
            });
            if (!categories || categories.length === 0) {
                this.logger.warn('Không tìm thấy danh mục nào cho trang chủ');
                return (0, class_transformer_1.plainToInstance)(ecom_product_public_dto_1.HomepageSectionsResponseDto, {
                    sections: [],
                    totalCategories: 0,
                    returnedCategories: 0
                });
            }
            const sections = [];
            for (const category of categories) {
                this.logger.debug(`Processing category: ${category.id} - ${category.name} (Type: ${typeof category.id})`);
                this.logger.debug(`Getting products for category ${category.id} (Type: ${typeof category.id})`);
                let products = [];
                try {
                    const queryLimit = Math.max(productsPerCategory * 3, 20);
                    const queryBuilder = this.ecomProductRepository
                        .createQueryBuilder('product')
                        .leftJoinAndSelect('product.category', 'category')
                        .where('product.isActive = :isActive', { isActive: true })
                        .andWhere('product.isDeleted = :isDeleted', { isDeleted: false })
                        .andWhere('product.categoryId = :categoryId', { categoryId: category.id })
                        .orderBy('product.createdAt', 'DESC')
                        .limit(queryLimit);
                    const productEntities = await queryBuilder.getMany();
                    this.logger.debug(`Queried ${productEntities?.length || 0} products for randomization for category ${category.id}`);
                    if (productEntities && productEntities.length > 0) {
                        const allProducts = productEntities.map(product => this.toPublicDto(product));
                        const shuffledProducts = this.shuffleArray(allProducts);
                        products = shuffledProducts.slice(0, productsPerCategory);
                        this.logger.debug(`After randomization and limiting: ${products?.length || 0} products for category ${category.id}`);
                    }
                }
                catch (findAllError) {
                    this.logger.error(`Error getting products for category ${category.id}: ${findAllError.message}`);
                    this.logger.error(`Error stack: ${findAllError.stack}`);
                    throw findAllError;
                }
                this.logger.debug(`Products found for category ${category.id}: ${products?.length || 0}`);
                if (products && products.length > 0) {
                    this.logger.debug(`Adding section for category ${category.id} with ${products.length} products`);
                    const categoryPublicDto = (0, class_transformer_1.plainToInstance)(ecom_product_category_public_dto_1.EcomProductCategoryPublicDto, category, {
                        excludeExtraneousValues: true,
                        enableImplicitConversion: true,
                        exposeDefaultValues: true,
                        exposeUnsetFields: false,
                    });
                    sections.push((0, class_transformer_1.plainToInstance)(ecom_product_public_dto_1.HomepageSectionDto, {
                        category: categoryPublicDto,
                        products: products
                    }));
                    this.logger.debug(`Section added. Total sections now: ${sections.length}`);
                }
                else {
                    this.logger.debug(`No products found for category ${category.id}, skipping section`);
                }
            }
            const totalCategoriesCount = categoriesResult.total || 0;
            const result = (0, class_transformer_1.plainToInstance)(ecom_product_public_dto_1.HomepageSectionsResponseDto, {
                sections,
                totalCategories: totalCategoriesCount || 0,
                returnedCategories: sections.length
            });
            this.logger.debug(`Trả về ${sections.length} section cho trang chủ`);
            return result;
        }
        catch (error) {
            this.logger.error(`Lỗi khi tìm dữ liệu section trang chủ: ${error.message}`, error.stack);
            throw new common_1.InternalServerErrorException(`Không thể tìm dữ liệu section trang chủ: ${error.message}`);
        }
    }
};
exports.PublicEcomProductsService = PublicEcomProductsService;
exports.PublicEcomProductsService = PublicEcomProductsService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(ecom_products_entity_1.EcomProduct)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        typeorm_2.DataSource,
        event_emitter_1.EventEmitter2,
        read_ecom_product_categories_service_1.ReadEcomProductCategoriesService])
], PublicEcomProductsService);
//# sourceMappingURL=public.ecom-products.service.js.map
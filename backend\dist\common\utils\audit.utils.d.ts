import { BaseEntity } from '../entities/base.entity';
export declare class AuditUtils {
    static readonly AUDIT_RELATIONS: string[];
    static mapAuditRelationships<T extends BaseEntity, D>(entity: T, dto: D): void;
    static mergeAuditRelations(relations?: string[], includeAudit?: boolean): string[];
    static getAuditRelations(): string[];
    static isAuditRelation(relation: string): boolean;
    static filterOutAuditRelations(relations: string[]): string[];
    static filterAuditRelations(relations: string[]): string[];
    static setCreateAuditFields<T extends BaseEntity>(entity: T, userId?: string): void;
    static setUpdateAuditFields<T extends BaseEntity>(entity: T, userId?: string): void;
    static setSoftDeleteAuditFields<T extends BaseEntity>(entity: T, userId?: string): void;
    static setRestoreAuditFields<T extends BaseEntity>(entity: T, userId?: string): void;
}

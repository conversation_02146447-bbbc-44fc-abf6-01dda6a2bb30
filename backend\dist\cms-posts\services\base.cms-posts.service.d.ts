import { Logger } from '@nestjs/common';
import { Repository, DataSource, FindOptionsWhere } from 'typeorm';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { CmsPosts } from '../entity/cms-posts.entity';
import { CmsPostDto } from '../dto/cms-post.dto';
export declare class BaseCmsPostsService {
    protected readonly postRepository: Repository<CmsPosts>;
    protected readonly dataSource: DataSource;
    protected readonly eventEmitter: EventEmitter2;
    protected readonly logger: Logger;
    protected readonly EVENT_POST_CREATED = "cms-post.created";
    protected readonly EVENT_POST_UPDATED = "cms-post.updated";
    protected readonly EVENT_POST_DELETED = "cms-post.deleted";
    protected readonly EVENT_POST_PUBLISHED = "cms-post.published";
    protected readonly EVENT_POST_VIEWED = "cms-post.viewed";
    protected readonly validRelations: string[];
    constructor(postRepository: Repository<CmsPosts>, dataSource: DataSource, eventEmitter: EventEmitter2);
    protected validateRelations(relations: string[]): string[];
    protected buildWhereClause(filter?: string): FindOptionsWhere<CmsPosts> | FindOptionsWhere<CmsPosts>[];
    protected findById(id: string, relations?: string[], throwError?: boolean): Promise<CmsPosts | null>;
    protected findBySlug(slug: string, postType?: string, throwError?: boolean): Promise<CmsPosts | null>;
    protected toDto(post: CmsPosts | null): CmsPostDto | null;
    protected toDtos(posts: CmsPosts[]): CmsPostDto[];
    protected incrementViewCount(id: string): Promise<number>;
    protected canPublish(post: CmsPosts): boolean;
    protected generateSlugFromTitle(title: string): string;
    protected isSlugUnique(slug: string, postType: string, excludeId?: string): Promise<boolean>;
}

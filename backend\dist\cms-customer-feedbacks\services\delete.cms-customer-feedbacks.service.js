"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.DeleteCmsCustomerFeedbacksService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const event_emitter_1 = require("@nestjs/event-emitter");
const typeorm_transactional_1 = require("typeorm-transactional");
const base_cms_customer_feedbacks_service_1 = require("./base.cms-customer-feedbacks.service");
const cms_customer_feedbacks_entity_1 = require("../entity/cms-customer-feedbacks.entity");
let DeleteCmsCustomerFeedbacksService = class DeleteCmsCustomerFeedbacksService extends base_cms_customer_feedbacks_service_1.BaseCmsCustomerFeedbacksService {
    feedbackRepository;
    dataSource;
    eventEmitter;
    constructor(feedbackRepository, dataSource, eventEmitter) {
        super(feedbackRepository, dataSource, eventEmitter);
        this.feedbackRepository = feedbackRepository;
        this.dataSource = dataSource;
        this.eventEmitter = eventEmitter;
    }
    async softDelete(id, userId) {
        try {
            this.logger.debug(`Đang xóa mềm feedback khách hàng CMS với ID: ${id}`);
            const feedback = await this.findById(id, []);
            if (!feedback) {
                throw new common_1.NotFoundException(`Không tìm thấy feedback với ID: ${id}`);
            }
            const oldData = this.toDto(feedback);
            feedback.isDeleted = true;
            feedback.deletedBy = userId;
            feedback.deletedAt = new Date();
            const deletedFeedback = await this.feedbackRepository.save(feedback);
            const feedbackDto = this.toDto(deletedFeedback);
            if (!feedbackDto) {
                throw new common_1.InternalServerErrorException('Không thể chuyển đổi entity thành DTO');
            }
            this.eventEmitter.emit(this.EVENT_FEEDBACK_DELETED, {
                feedbackId: feedbackDto.id,
                userId,
                oldData,
                newData: feedbackDto,
            });
            return feedbackDto;
        }
        catch (error) {
            this.logger.error(`Lỗi khi xóa mềm feedback khách hàng CMS: ${error.message}`, error.stack);
            if (error instanceof common_1.BadRequestException || error instanceof common_1.NotFoundException) {
                throw error;
            }
            throw new common_1.InternalServerErrorException(`Không thể xóa feedback khách hàng CMS: ${error.message}`);
        }
    }
    async restore(id, userId) {
        try {
            this.logger.debug(`Đang khôi phục feedback khách hàng CMS với ID: ${id}`);
            const feedback = await this.feedbackRepository.findOne({
                where: { id, isDeleted: true },
            });
            if (!feedback) {
                throw new common_1.BadRequestException(`Không tìm thấy feedback đã xóa với ID: ${id}`);
            }
            const oldData = this.toDto(feedback);
            feedback.isDeleted = false;
            feedback.deletedBy = null;
            feedback.deletedAt = null;
            feedback.updatedBy = userId;
            const restoredFeedback = await this.feedbackRepository.save(feedback);
            const feedbackDto = this.toDto(restoredFeedback);
            if (!feedbackDto) {
                throw new common_1.InternalServerErrorException('Không thể chuyển đổi entity thành DTO');
            }
            this.eventEmitter.emit('cms-customer-feedback.restored', {
                feedbackId: feedbackDto.id,
                userId,
                oldData,
                newData: feedbackDto,
            });
            return feedbackDto;
        }
        catch (error) {
            this.logger.error(`Lỗi khi khôi phục feedback khách hàng CMS: ${error.message}`, error.stack);
            if (error instanceof common_1.BadRequestException) {
                throw error;
            }
            throw new common_1.InternalServerErrorException(`Không thể khôi phục feedback khách hàng CMS: ${error.message}`);
        }
    }
    async remove(id) {
        try {
            this.logger.debug(`Đang xóa vĩnh viễn feedback khách hàng CMS với ID: ${id}`);
            const feedback = await this.feedbackRepository.findOne({
                where: { id },
            });
            if (!feedback) {
                throw new common_1.NotFoundException(`Không tìm thấy feedback với ID: ${id}`);
            }
            const result = await this.feedbackRepository.delete(id);
            if (result.affected === 0) {
                throw new common_1.InternalServerErrorException(`Không thể xóa feedback khách hàng CMS với ID: ${id}`);
            }
            const affectedCount = result.affected ?? 0;
            return { affected: affectedCount };
        }
        catch (error) {
            this.logger.error(`Lỗi khi xóa vĩnh viễn feedback khách hàng CMS: ${error.message}`, error.stack);
            if (error instanceof common_1.BadRequestException || error instanceof common_1.NotFoundException) {
                throw error;
            }
            throw new common_1.InternalServerErrorException(`Không thể xóa vĩnh viễn feedback khách hàng CMS: ${error.message}`);
        }
    }
    async bulkSoftDelete(ids, userId) {
        try {
            this.logger.debug(`Đang xóa mềm ${ids.length} feedback khách hàng CMS`);
            const deletedFeedbacks = [];
            for (const id of ids) {
                try {
                    const feedback = await this.softDelete(id, userId);
                    if (feedback) {
                        deletedFeedbacks.push(feedback);
                    }
                }
                catch (error) {
                    this.logger.warn(`Không thể xóa feedback với ID ${id}: ${error.message}`);
                }
            }
            return deletedFeedbacks;
        }
        catch (error) {
            this.logger.error(`Lỗi khi xóa mềm nhiều feedback khách hàng CMS: ${error.message}`, error.stack);
            throw new common_1.InternalServerErrorException(`Không thể xóa nhiều feedback khách hàng CMS: ${error.message}`);
        }
    }
    async bulkRestore(ids, userId) {
        try {
            this.logger.debug(`Đang khôi phục ${ids.length} feedback khách hàng CMS`);
            const restoredFeedbacks = [];
            for (const id of ids) {
                try {
                    const feedback = await this.restore(id, userId);
                    if (feedback) {
                        restoredFeedbacks.push(feedback);
                    }
                }
                catch (error) {
                    this.logger.warn(`Không thể khôi phục feedback với ID ${id}: ${error.message}`);
                }
            }
            return restoredFeedbacks;
        }
        catch (error) {
            this.logger.error(`Lỗi khi khôi phục nhiều feedback khách hàng CMS: ${error.message}`, error.stack);
            throw new common_1.InternalServerErrorException(`Không thể khôi phục nhiều feedback khách hàng CMS: ${error.message}`);
        }
    }
    async bulkRemove(ids) {
        try {
            this.logger.debug(`Đang xóa vĩnh viễn ${ids.length} feedback khách hàng CMS`);
            let totalAffected = 0;
            for (const id of ids) {
                try {
                    const result = await this.remove(id);
                    totalAffected += result.affected;
                }
                catch (error) {
                    this.logger.warn(`Không thể xóa vĩnh viễn feedback với ID ${id}: ${error.message}`);
                }
            }
            return { affected: totalAffected };
        }
        catch (error) {
            this.logger.error(`Lỗi khi xóa vĩnh viễn nhiều feedback khách hàng CMS: ${error.message}`, error.stack);
            throw new common_1.InternalServerErrorException(`Không thể xóa vĩnh viễn nhiều feedback khách hàng CMS: ${error.message}`);
        }
    }
};
exports.DeleteCmsCustomerFeedbacksService = DeleteCmsCustomerFeedbacksService;
__decorate([
    (0, typeorm_transactional_1.Transactional)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String]),
    __metadata("design:returntype", Promise)
], DeleteCmsCustomerFeedbacksService.prototype, "softDelete", null);
__decorate([
    (0, typeorm_transactional_1.Transactional)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String]),
    __metadata("design:returntype", Promise)
], DeleteCmsCustomerFeedbacksService.prototype, "restore", null);
__decorate([
    (0, typeorm_transactional_1.Transactional)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], DeleteCmsCustomerFeedbacksService.prototype, "remove", null);
__decorate([
    (0, typeorm_transactional_1.Transactional)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Array, String]),
    __metadata("design:returntype", Promise)
], DeleteCmsCustomerFeedbacksService.prototype, "bulkSoftDelete", null);
__decorate([
    (0, typeorm_transactional_1.Transactional)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Array, String]),
    __metadata("design:returntype", Promise)
], DeleteCmsCustomerFeedbacksService.prototype, "bulkRestore", null);
__decorate([
    (0, typeorm_transactional_1.Transactional)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Array]),
    __metadata("design:returntype", Promise)
], DeleteCmsCustomerFeedbacksService.prototype, "bulkRemove", null);
exports.DeleteCmsCustomerFeedbacksService = DeleteCmsCustomerFeedbacksService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(cms_customer_feedbacks_entity_1.CmsCustomerFeedbacks)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        typeorm_2.DataSource,
        event_emitter_1.EventEmitter2])
], DeleteCmsCustomerFeedbacksService);
//# sourceMappingURL=delete.cms-customer-feedbacks.service.js.map
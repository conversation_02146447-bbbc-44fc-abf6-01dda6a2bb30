import { VnpayService } from '../services/vnpay.service';
export declare class VnpayUsageExample {
    private readonly vnpayService;
    constructor(vnpayService: VnpayService);
    createBasicPayment(): Promise<{
        paymentUrl: string;
        transactionId: string;
    }>;
    createPaymentWithBankCode(): Promise<{
        paymentUrl: string;
        transactionId: string;
    }>;
    handleReturnUrl(queryParams: Record<string, string>): Promise<{
        success: boolean;
        message: string | undefined;
        data: {
            isValid: boolean;
            transactionId?: string;
            amount?: number;
            responseCode?: string;
            transactionStatus?: string;
            message?: string;
            vnpayTransactionNo?: string;
            bankCode?: string;
            payDate?: string;
        };
    } | {
        success: boolean;
        message: string;
        data: null;
    }>;
    handleIpnCallback(queryParams: Record<string, string>): Promise<{
        RspCode: string;
        Message: string;
    }>;
    queryTransactionStatus(txnRef: string, transactionDate: string): Promise<any>;
    refundFullTransaction(txnRef: string, originalAmount: number, transactionDate: string, vnpayTransactionNo?: string): Promise<any>;
    refundPartialTransaction(txnRef: string, refundAmount: number, transactionDate: string, vnpayTransactionNo?: string): Promise<any>;
    completePaymentWorkflow(userId: string, walletId: string, amount: number): Promise<{
        paymentUrl: string;
        transactionId: string;
    }>;
}

module.exports = {

"[project]/components/providers/WebSocketProvider.tsx (client reference/proxy) <module evaluation>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "WebSocketProvider": (()=>WebSocketProvider)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$opentelemetry$2b$_ee4fdf7136c0ae57e36e5bb24f18c6a1$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/next@15.3.0_@opentelemetry+_ee4fdf7136c0ae57e36e5bb24f18c6a1/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-turbopack-server-edge.js [app-rsc] (ecmascript)");
;
const WebSocketProvider = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$opentelemetry$2b$_ee4fdf7136c0ae57e36e5bb24f18c6a1$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call WebSocketProvider() from the server but WebSocketProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/components/providers/WebSocketProvider.tsx <module evaluation>", "WebSocketProvider");
}}),
"[project]/components/providers/WebSocketProvider.tsx (client reference/proxy)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "WebSocketProvider": (()=>WebSocketProvider)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$opentelemetry$2b$_ee4fdf7136c0ae57e36e5bb24f18c6a1$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/next@15.3.0_@opentelemetry+_ee4fdf7136c0ae57e36e5bb24f18c6a1/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-turbopack-server-edge.js [app-rsc] (ecmascript)");
;
const WebSocketProvider = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$opentelemetry$2b$_ee4fdf7136c0ae57e36e5bb24f18c6a1$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call WebSocketProvider() from the server but WebSocketProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/components/providers/WebSocketProvider.tsx", "WebSocketProvider");
}}),
"[project]/components/providers/WebSocketProvider.tsx [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
var __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$providers$2f$WebSocketProvider$2e$tsx__$28$client__reference$2f$proxy$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/components/providers/WebSocketProvider.tsx (client reference/proxy) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$providers$2f$WebSocketProvider$2e$tsx__$28$client__reference$2f$proxy$29$__ = __turbopack_context__.i("[project]/components/providers/WebSocketProvider.tsx (client reference/proxy)");
;
__turbopack_context__.n(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$providers$2f$WebSocketProvider$2e$tsx__$28$client__reference$2f$proxy$29$__);
}}),
"[project]/components/ui/sonner.tsx (client reference/proxy) <module evaluation>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "Toaster": (()=>Toaster)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$opentelemetry$2b$_ee4fdf7136c0ae57e36e5bb24f18c6a1$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/next@15.3.0_@opentelemetry+_ee4fdf7136c0ae57e36e5bb24f18c6a1/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-turbopack-server-edge.js [app-rsc] (ecmascript)");
;
const Toaster = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$opentelemetry$2b$_ee4fdf7136c0ae57e36e5bb24f18c6a1$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call Toaster() from the server but Toaster is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/components/ui/sonner.tsx <module evaluation>", "Toaster");
}}),
"[project]/components/ui/sonner.tsx (client reference/proxy)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "Toaster": (()=>Toaster)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$opentelemetry$2b$_ee4fdf7136c0ae57e36e5bb24f18c6a1$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/next@15.3.0_@opentelemetry+_ee4fdf7136c0ae57e36e5bb24f18c6a1/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-turbopack-server-edge.js [app-rsc] (ecmascript)");
;
const Toaster = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$opentelemetry$2b$_ee4fdf7136c0ae57e36e5bb24f18c6a1$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call Toaster() from the server but Toaster is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/components/ui/sonner.tsx", "Toaster");
}}),
"[project]/components/ui/sonner.tsx [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
var __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$sonner$2e$tsx__$28$client__reference$2f$proxy$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/components/ui/sonner.tsx (client reference/proxy) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$sonner$2e$tsx__$28$client__reference$2f$proxy$29$__ = __turbopack_context__.i("[project]/components/ui/sonner.tsx (client reference/proxy)");
;
__turbopack_context__.n(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$sonner$2e$tsx__$28$client__reference$2f$proxy$29$__);
}}),
"[project]/hooks/use-auth.tsx (client reference/proxy) <module evaluation>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "AuthProvider": (()=>AuthProvider),
    "useAuth": (()=>useAuth)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$opentelemetry$2b$_ee4fdf7136c0ae57e36e5bb24f18c6a1$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/next@15.3.0_@opentelemetry+_ee4fdf7136c0ae57e36e5bb24f18c6a1/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-turbopack-server-edge.js [app-rsc] (ecmascript)");
;
const AuthProvider = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$opentelemetry$2b$_ee4fdf7136c0ae57e36e5bb24f18c6a1$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call AuthProvider() from the server but AuthProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/hooks/use-auth.tsx <module evaluation>", "AuthProvider");
const useAuth = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$opentelemetry$2b$_ee4fdf7136c0ae57e36e5bb24f18c6a1$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call useAuth() from the server but useAuth is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/hooks/use-auth.tsx <module evaluation>", "useAuth");
}}),
"[project]/hooks/use-auth.tsx (client reference/proxy)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "AuthProvider": (()=>AuthProvider),
    "useAuth": (()=>useAuth)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$opentelemetry$2b$_ee4fdf7136c0ae57e36e5bb24f18c6a1$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/next@15.3.0_@opentelemetry+_ee4fdf7136c0ae57e36e5bb24f18c6a1/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-turbopack-server-edge.js [app-rsc] (ecmascript)");
;
const AuthProvider = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$opentelemetry$2b$_ee4fdf7136c0ae57e36e5bb24f18c6a1$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call AuthProvider() from the server but AuthProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/hooks/use-auth.tsx", "AuthProvider");
const useAuth = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$opentelemetry$2b$_ee4fdf7136c0ae57e36e5bb24f18c6a1$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call useAuth() from the server but useAuth is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/hooks/use-auth.tsx", "useAuth");
}}),
"[project]/hooks/use-auth.tsx [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
var __TURBOPACK__imported__module__$5b$project$5d2f$hooks$2f$use$2d$auth$2e$tsx__$28$client__reference$2f$proxy$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/hooks/use-auth.tsx (client reference/proxy) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$hooks$2f$use$2d$auth$2e$tsx__$28$client__reference$2f$proxy$29$__ = __turbopack_context__.i("[project]/hooks/use-auth.tsx (client reference/proxy)");
;
__turbopack_context__.n(__TURBOPACK__imported__module__$5b$project$5d2f$hooks$2f$use$2d$auth$2e$tsx__$28$client__reference$2f$proxy$29$__);
}}),
"[project]/components/dynamic-metadata.tsx (client reference/proxy) <module evaluation>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "DynamicMetadata": (()=>DynamicMetadata)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$opentelemetry$2b$_ee4fdf7136c0ae57e36e5bb24f18c6a1$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/next@15.3.0_@opentelemetry+_ee4fdf7136c0ae57e36e5bb24f18c6a1/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-turbopack-server-edge.js [app-rsc] (ecmascript)");
;
const DynamicMetadata = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$opentelemetry$2b$_ee4fdf7136c0ae57e36e5bb24f18c6a1$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call DynamicMetadata() from the server but DynamicMetadata is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/components/dynamic-metadata.tsx <module evaluation>", "DynamicMetadata");
}}),
"[project]/components/dynamic-metadata.tsx (client reference/proxy)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "DynamicMetadata": (()=>DynamicMetadata)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$opentelemetry$2b$_ee4fdf7136c0ae57e36e5bb24f18c6a1$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/next@15.3.0_@opentelemetry+_ee4fdf7136c0ae57e36e5bb24f18c6a1/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-turbopack-server-edge.js [app-rsc] (ecmascript)");
;
const DynamicMetadata = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$opentelemetry$2b$_ee4fdf7136c0ae57e36e5bb24f18c6a1$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call DynamicMetadata() from the server but DynamicMetadata is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/components/dynamic-metadata.tsx", "DynamicMetadata");
}}),
"[project]/components/dynamic-metadata.tsx [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
var __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$dynamic$2d$metadata$2e$tsx__$28$client__reference$2f$proxy$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/components/dynamic-metadata.tsx (client reference/proxy) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$dynamic$2d$metadata$2e$tsx__$28$client__reference$2f$proxy$29$__ = __turbopack_context__.i("[project]/components/dynamic-metadata.tsx (client reference/proxy)");
;
__turbopack_context__.n(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$dynamic$2d$metadata$2e$tsx__$28$client__reference$2f$proxy$29$__);
}}),
"[project]/lib/auth.ts [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * Service xử lý xác thực người dùng
 */ __turbopack_context__.s({
    "authService": (()=>authService),
    "decodeToken": (()=>decodeToken),
    "isTokenExpired": (()=>isTokenExpired),
    "isTokenExpiringSoon": (()=>isTokenExpiringSoon)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$api$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/api.ts [app-rsc] (ecmascript)");
;
const decodeToken = (token)=>{
    try {
        if (!token) return null;
        // JWT token có 3 phần: header.payload.signature
        const base64Url = token.split('.')[1];
        if (!base64Url) return null;
        // Thay thế các ký tự đặc biệt trong base64Url
        const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');
        // Giải mã base64 thành JSON
        const jsonPayload = decodeURIComponent(atob(base64).split('').map((c)=>'%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2)).join(''));
        const payload = JSON.parse(jsonPayload);
        // Chuyển đổi payload thành User
        const user = {
            id: payload.sub || payload.id || '',
            username: payload.username || '',
            email: payload.email || '',
            fullName: payload.fullName || payload.name || '',
            roles: payload.roles || [],
            permissions: payload.permissions || [],
            exp: payload.exp || 0
        };
        return user;
    } catch (error) {
        console.error('Error decoding token:', error);
        return null;
    }
};
const isTokenExpired = (token)=>{
    try {
        if (!token) return true;
        const decodedToken = decodeToken(token);
        if (!decodedToken || !decodedToken.exp) return true;
        // exp là thời gian hết hạn tính bằng giây kể từ epoch (1/1/1970)
        const currentTime = Math.floor(Date.now() / 1000); // Chuyển đổi thời gian hiện tại sang giây
        // Trả về true nếu token đã hết hạn
        return decodedToken.exp < currentTime;
    } catch (error) {
        console.error('Error checking token expiration:', error);
        return true; // Nếu có lỗi, coi như token đã hết hạn
    }
};
const isTokenExpiringSoon = (token, thresholdSeconds = 300)=>{
    try {
        if (!token) return true;
        const decodedToken = decodeToken(token);
        if (!decodedToken || !decodedToken.exp) return true;
        const currentTime = Math.floor(Date.now() / 1000);
        // Trả về true nếu token sắp hết hạn trong vòng thresholdSeconds giây
        return decodedToken.exp - currentTime < thresholdSeconds;
    } catch (error) {
        console.error('Error checking token expiration:', error);
        return true;
    }
};
const authService = {
    /**
   * Đăng nhập người dùng
   */ async login (credentials) {
        try {
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$api$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["api"].post('auth/login', credentials);
            // Kiểm tra cấu trúc response
            const authData = response.data || response;
            // Lưu token vào localStorage và cookie
            localStorage.setItem('accessToken', authData.access_token);
            localStorage.setItem('refreshToken', authData.refresh_token);
            // Lưu token vào cookie để middleware có thể truy cập
            document.cookie = `token=${authData.access_token}; path=/; max-age=${60 * 60 * 24 * 7}; SameSite=Lax`; // 7 ngày
            // Nếu không có thông tin người dùng trong response, giải mã token để lấy
            if (!authData.user && authData.access_token) {
                const decodedUser = decodeToken(authData.access_token);
                if (decodedUser) {
                    authData.user = decodedUser;
                    this.saveUser(decodedUser);
                }
            } else if (authData.user) {
                // Lưu thông tin người dùng
                this.saveUser(authData.user);
            }
            return authData;
        } catch (error) {
            console.error('Login error:', error);
            throw error;
        }
    },
    /**
   * Đăng ký người dùng mới
   */ async register (credentials) {
        try {
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$api$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["api"].post('auth/register', credentials);
            // Kiểm tra cấu trúc response
            const authData = response.data || response;
            // Lưu token vào localStorage và cookie
            localStorage.setItem('accessToken', authData.access_token);
            localStorage.setItem('refreshToken', authData.refresh_token);
            // Lưu token vào cookie để middleware có thể truy cập
            document.cookie = `token=${authData.access_token}; path=/; max-age=${60 * 60 * 24 * 7}; SameSite=Lax`; // 7 ngày
            // Nếu không có thông tin người dùng trong response, giải mã token để lấy
            if (!authData.user && authData.access_token) {
                const decodedUser = decodeToken(authData.access_token);
                if (decodedUser) {
                    authData.user = decodedUser;
                    this.saveUser(decodedUser);
                }
            } else if (authData.user) {
                // Lưu thông tin người dùng
                this.saveUser(authData.user);
            }
            return authData;
        } catch (error) {
            console.error('Register error:', error);
            throw error;
        }
    },
    /**
   * Làm mới token
   */ async refreshToken (refreshToken) {
        try {
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$api$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["api"].post('auth/refresh-token', {
                refreshToken
            });
            // Kiểm tra cấu trúc response
            const authData = response.data || response;
            // Cập nhật token trong localStorage và cookie
            localStorage.setItem('accessToken', authData.access_token);
            localStorage.setItem('refreshToken', authData.refresh_token);
            // Cập nhật token trong cookie để middleware có thể truy cập
            document.cookie = `token=${authData.access_token}; path=/; max-age=${60 * 60 * 24 * 7}; SameSite=Lax`; // 7 ngày
            // Nếu không có thông tin người dùng trong response, giải mã token để lấy
            if (!authData.user && authData.access_token) {
                const decodedUser = decodeToken(authData.access_token);
                if (decodedUser) {
                    authData.user = decodedUser;
                    this.saveUser(decodedUser);
                }
            } else if (authData.user) {
                // Lưu thông tin người dùng
                this.saveUser(authData.user);
            }
            return authData;
        } catch (error) {
            console.error('Refresh token error:', error);
            // Nếu refresh token thất bại, đăng xuất người dùng
            this.logout();
            throw error;
        }
    },
    /**
   * Thử làm mới token nếu có refresh token
   */ async tryRefreshToken () {
        try {
            const refreshToken = this.getRefreshToken();
            if (!refreshToken) return false;
            const result = await this.refreshToken(refreshToken);
            // Việc điều hướng sẽ được xử lý ở handleTokenRefresh trong api.ts
            // Không điều hướng ở đây để tránh điều hướng kép
            return true;
        } catch (error) {
            console.error('Try refresh token error:', error);
            return false;
        }
    },
    /**
   * Điều hướng người dùng dựa trên vai trò
   */ redirectBasedOnRole (user) {
        if ("TURBOPACK compile-time truthy", 1) return;
        "TURBOPACK unreachable";
        const isAdmin = undefined;
    },
    /**
   * Đăng xuất người dùng
   */ logout () {
        localStorage.removeItem('accessToken');
        localStorage.removeItem('refreshToken');
        localStorage.removeItem('user');
        // Xóa cookie token
        document.cookie = 'token=; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT; SameSite=Lax';
        // Chuyển hướng về trang đăng nhập nếu cần
        if ("TURBOPACK compile-time falsy", 0) {
            "TURBOPACK unreachable";
        }
    },
    /**
   * Kiểm tra người dùng đã đăng nhập chưa
   */ isAuthenticated () {
        if ("TURBOPACK compile-time truthy", 1) return false;
        "TURBOPACK unreachable";
        // Kiểm tra token trong localStorage hoặc cookie
        const localToken = undefined;
        // Kiểm tra token trong cookie
        const cookies = undefined;
        let i;
    },
    /**
   * Lấy thông tin người dùng hiện tại
   */ getCurrentUser () {
        if ("TURBOPACK compile-time truthy", 1) return null;
        "TURBOPACK unreachable";
        const userJson = undefined;
    },
    /**
   * Lưu thông tin người dùng
   */ saveUser (user) {
        localStorage.setItem('user', JSON.stringify(user));
    },
    /**
   * Lấy token hiện tại
   */ getAccessToken () {
        if ("TURBOPACK compile-time truthy", 1) return null;
        "TURBOPACK unreachable";
    },
    /**
   * Lấy refresh token
   */ getRefreshToken () {
        if ("TURBOPACK compile-time truthy", 1) return null;
        "TURBOPACK unreachable";
    },
    /**
   * Xác minh số điện thoại cho người dùng hiện có
   * @param userId ID của người dùng
   * @param firebaseToken Firebase ID token
   * @returns Kết quả xác minh
   */ async verifyPhoneNumber (userId, firebaseToken) {
        try {
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$api$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["api"].post('auth/verify-phone-public', {
                userId,
                firebaseToken
            });
            return response.data || response;
        } catch (error) {
            console.error('Phone verification error:', error);
            throw error;
        }
    },
    /**
   * Unified OTP verification for both phone and email
   * @param verificationData Verification data
   * @returns Verification result with tokens
   */ async verifyOtp (verificationData) {
        try {
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$api$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["api"].post('auth/verify-otp', verificationData);
            // Kiểm tra cấu trúc response
            const authData = response.data || response;
            // Lưu token vào localStorage và cookie
            localStorage.setItem('accessToken', authData.access_token);
            localStorage.setItem('refreshToken', authData.refresh_token);
            // Lưu token vào cookie để middleware có thể truy cập
            document.cookie = `token=${authData.access_token}; path=/; max-age=${60 * 60 * 24 * 7}; SameSite=Lax`; // 7 ngày
            // Lưu thông tin user vào localStorage
            if (authData.user) {
                localStorage.setItem('user', JSON.stringify(authData.user));
            }
            return authData;
        } catch (error) {
            console.error('OTP verification error:', error);
            throw error;
        }
    }
};
}}),
"[project]/lib/api.ts [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * API client cho ứng dụng
 */ __turbopack_context__.s({
    "api": (()=>api),
    "fetchApi": (()=>fetchApi)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$auth$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/auth.ts [app-rsc] (ecmascript)");
;
const NEXT_PUBLIC_API_URL = ("TURBOPACK compile-time value", "http://localhost:3168/api/v1") || "http://localhost:3000/api/v1";
// Biến để theo dõi các yêu cầu đang chờ refresh token
let isRefreshing = false;
let failedQueue = [];
/**
 * Xử lý hàng đợi các yêu cầu thất bại
 */ function processQueue(error, token = null) {
    failedQueue.forEach((prom)=>{
        if (error) {
            prom.reject(error);
        } else {
            prom.resolve(token);
        }
    });
    failedQueue = [];
}
/**
 * Hàm thử refresh token và thực hiện lại yêu cầu
 */ async function handleTokenRefresh() {
    try {
        if (!isRefreshing) {
            isRefreshing = true;
            const success = await __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$auth$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["authService"].tryRefreshToken();
            isRefreshing = false;
            if (success) {
                // Lấy token mới
                const newToken = __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$auth$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["authService"].getAccessToken();
                // Xử lý hàng đợi với token mới
                processQueue(null, newToken);
                // Lấy thông tin người dùng hiện tại
                const currentUser = __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$auth$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["authService"].getCurrentUser();
                if (currentUser) {
                    // Luôn điều hướng người dùng về trang phù hợp với vai trò của họ
                    __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$auth$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["authService"].redirectBasedOnRole(currentUser);
                }
                return newToken;
            } else {
                // Nếu refresh token thất bại, đăng xuất người dùng
                __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$auth$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["authService"].logout();
                processQueue(new Error('Refresh token failed'));
                return null;
            }
        } else {
            // Nếu đang refresh token, thêm yêu cầu vào hàng đợi
            return new Promise((resolve, reject)=>{
                failedQueue.push({
                    resolve,
                    reject
                });
            });
        }
    } catch (error) {
        console.error("API: Error during token refresh:", error);
        isRefreshing = false;
        processQueue(error);
        throw error;
    }
}
async function fetchApi(endpoint, options = {}) {
    const url = `${NEXT_PUBLIC_API_URL}${endpoint.startsWith('/') ? endpoint : `/${endpoint}`}`;
    // Hàm để thực hiện yêu cầu API
    const executeRequest = async (token)=>{
        // Tạo headers mới
        const headers = new Headers(options.headers);
        headers.set('Content-Type', 'application/json');
        // Thêm token xác thực nếu có
        const accessToken = token || (("TURBOPACK compile-time falsy", 0) ? ("TURBOPACK unreachable", undefined) : null);
        if (accessToken) {
            headers.set('Authorization', `Bearer ${accessToken}`);
        }
        const config = {
            ...options,
            headers
        };
        const response = await fetch(url, config);
        // Kiểm tra nếu response không thành công
        if (!response.ok) {
            const errorData = await response.json().catch(()=>({}));
            // Xử lý cấu trúc lỗi từ backend
            const errorMessage = errorData.message || errorData.data && errorData.data.message || `API request failed with status ${response.status}`;
            console.error('API Error:', errorData);
            // Tạo error object với thông tin chi tiết hơn
            const error = new Error(errorMessage);
            error.status = response.status;
            error.data = errorData;
            throw error;
        }
        // Parse JSON response
        const responseData = await response.json();
        // Kiểm tra cấu trúc response từ backend (ApiResponseDto)
        if (responseData.data !== undefined) {
            // Trường hợp response có cấu trúc ApiResponseDto
            return responseData.data;
        } else {
            // Trường hợp response không có cấu trúc ApiResponseDto
            console.warn('API response does not follow standard structure:', responseData);
            return responseData;
        }
    };
    try {
        // Thử thực hiện yêu cầu
        return await executeRequest();
    } catch (error) {
        // Nếu lỗi 401 Unauthorized và không phải là yêu cầu đăng nhập/đăng ký/refresh token
        if (error.status === 401 && !endpoint.includes('auth/login') && !endpoint.includes('auth/register') && !endpoint.includes('auth/refresh-token')) {
            try {
                // Thử refresh token
                const newToken = await handleTokenRefresh();
                if (newToken) {
                    // Thực hiện lại yêu cầu với token mới
                    return await executeRequest(newToken);
                }
            } catch (refreshError) {
                console.error('Error refreshing token:', refreshError);
                // Nếu refresh token thất bại, đăng xuất người dùng
                __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$auth$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["authService"].logout();
                throw error; // Trả về lỗi ban đầu
            }
        }
        console.error('API request error:', error);
        throw error;
    }
}
const api = {
    baseUrl: NEXT_PUBLIC_API_URL,
    getToken: ()=>{
        return ("TURBOPACK compile-time falsy", 0) ? ("TURBOPACK unreachable", undefined) : null;
    },
    get: (endpoint, options)=>fetchApi(endpoint, {
            ...options,
            method: 'GET'
        }),
    post: (endpoint, data, options)=>fetchApi(endpoint, {
            ...options,
            method: 'POST',
            body: data ? JSON.stringify(data) : undefined
        }),
    put: (endpoint, data, options)=>fetchApi(endpoint, {
            ...options,
            method: 'PUT',
            body: data ? JSON.stringify(data) : undefined
        }),
    patch: (endpoint, data, options)=>fetchApi(endpoint, {
            ...options,
            method: 'PATCH',
            body: data ? JSON.stringify(data) : undefined
        }),
    delete: (endpoint, options)=>fetchApi(endpoint, {
            ...options,
            method: 'DELETE'
        }),
    // Phương thức download file
    downloadFile: async (endpoint, format, filename)=>{
        const url = `${NEXT_PUBLIC_API_URL}${endpoint.startsWith('/') ? endpoint : `/${endpoint}`}`;
        const token = ("TURBOPACK compile-time falsy", 0) ? ("TURBOPACK unreachable", undefined) : null;
        try {
            // Sử dụng XMLHttpRequest thay vì fetch để xử lý tải file tốt hơn
            return new Promise((resolve, reject)=>{
                const xhr = new XMLHttpRequest();
                xhr.open('GET', url, true);
                xhr.responseType = 'blob';
                xhr.setRequestHeader('Authorization', ("TURBOPACK compile-time falsy", 0) ? ("TURBOPACK unreachable", undefined) : '');
                xhr.onload = function() {
                    if (this.status === 200) {
                        const blob = new Blob([
                            this.response
                        ], {
                            type: format === 'csv' ? 'text/csv' : 'application/json'
                        });
                        const downloadUrl = window.URL.createObjectURL(blob);
                        const a = document.createElement('a');
                        a.href = downloadUrl;
                        a.download = filename;
                        document.body.appendChild(a);
                        a.click();
                        // Cleanup
                        window.URL.revokeObjectURL(downloadUrl);
                        document.body.removeChild(a);
                        resolve(true);
                    } else {
                        reject(new Error(`Download failed: ${this.status} ${this.statusText}`));
                    }
                };
                xhr.onerror = function() {
                    reject(new Error('Network error occurred'));
                };
                xhr.send();
            });
        } catch (error) {
            console.error('Download error:', error);
            throw error;
        }
    }
};
}}),
"[project]/lib/local-storage-utils.ts [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * Check if cached data is still valid
 */ __turbopack_context__.s({
    "clearAllCache": (()=>clearAllCache),
    "clearCacheByKey": (()=>clearCacheByKey),
    "isCacheValid": (()=>isCacheValid),
    "loadAllCache": (()=>loadAllCache),
    "loadCacheFromStorage": (()=>loadCacheFromStorage),
    "saveCacheToStorage": (()=>saveCacheToStorage)
});
function isCacheValid(cache) {
    if (!cache) return false;
    const now = Date.now();
    return now < cache.expiresAt;
}
function clearCacheByKey(cacheKey) {
    if ("TURBOPACK compile-time falsy", 0) {
        "TURBOPACK unreachable";
    }
}
function clearAllCache() {
    if ("TURBOPACK compile-time falsy", 0) {
        "TURBOPACK unreachable";
    }
}
function saveCacheToStorage(cacheKey, cache) {
    if ("TURBOPACK compile-time truthy", 1) return;
    "TURBOPACK unreachable";
}
function loadCacheFromStorage(cacheKey) {
    if ("TURBOPACK compile-time truthy", 1) return;
    "TURBOPACK unreachable";
}
function loadAllCache() {
    if ("TURBOPACK compile-time truthy", 1) return [];
    "TURBOPACK unreachable";
    const cacheKeys = undefined;
}
}}),
"[project]/lib/system-metadata.ts [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * Site Metadata Service
 * Handles fetching, caching, and managing site metadata from system config API
 */ __turbopack_context__.s({
    "SiteMetadataService": (()=>SiteMetadataService),
    "siteMetadataService": (()=>siteMetadataService)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$api$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/api.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$local$2d$storage$2d$utils$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/local-storage-utils.ts [app-rsc] (ecmascript)");
;
;
const CACHE_KEY = 'site_metadata_cache';
const DEFAULT_TTL = 24 * 60 * 60 * 1000; // 24 hours in milliseconds
class SiteMetadataService {
    static instance;
    cache = null;
    constructor(){
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$local$2d$storage$2d$utils$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["loadAllCache"])();
    }
    static getInstance() {
        if (!SiteMetadataService.instance) {
            SiteMetadataService.instance = new SiteMetadataService();
        }
        return SiteMetadataService.instance;
    }
    /**
   * Fetch system config from backend API
   */ async fetchSystemConfigFromAPI() {
        // Only fetch configs for SEO, header, and footer groups
        return await __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$api$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["api"].get('/public/system-configs?limit=300&filter=configGroup:website_seo|general', {
            cache: "no-cache"
        });
    }
    /**
   * Get metadata from system config
   */ async getMetadata() {
        // // Check cache first
        // if (this.isCacheValid() && this.cache) {
        //   return this.cache.data;
        // }
        try {
            // Always fetch from API first
            const systemConfig = await this.fetchSystemConfigFromAPI();
            // Cache the result
            const now = Date.now();
            this.cache = {
                data: systemConfig,
                cachedAt: now,
                expiresAt: now + DEFAULT_TTL
            };
            // Save to localStorage
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$local$2d$storage$2d$utils$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["saveCacheToStorage"])(CACHE_KEY, this.cache);
            return systemConfig;
        } catch (error) {
            console.error('Failed to fetch system config:', error);
            // If API call fails, return cached data if available
            if (this.cache) {
                return this.cache.data;
            }
            return {};
        }
    }
    /**
   * Refresh metadata
   */ async refreshMetadata() {
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$local$2d$storage$2d$utils$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["clearAllCache"])();
        return this.getMetadata();
    }
    /**
   * Get metadata from system config by group
   */ async getMetadataByGroup(configGroup) {
        const CACHE_KEY_GROUP = `site_metadata_cache_${configGroup}`;
        // Check cache first
        // if (this.isCacheValid() && this.cache) {
        //   return this.cache.data;
        // }
        try {
            // Always fetch from API first
            const systemConfig = await __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$api$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["api"].get(`/public/system-configs?limit=100&filter=configGroup:${configGroup}`);
            // Cache the result
            const now = Date.now();
            const cacheObj = {
                data: systemConfig,
                cachedAt: now,
                expiresAt: now + DEFAULT_TTL
            };
            // Save to localStorage
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$local$2d$storage$2d$utils$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["saveCacheToStorage"])(CACHE_KEY_GROUP, cacheObj);
            return systemConfig;
        } catch (error) {
            // If API call fails, try to get from cache
            if ("TURBOPACK compile-time falsy", 0) {
                "TURBOPACK unreachable";
            }
            return {};
        }
    }
}
const siteMetadataService = SiteMetadataService.getInstance();
}}),
"[next]/internal/font/google/geist_e531dabc.module.css [app-rsc] (css module)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v({
  "className": "geist_e531dabc-module__QGiZLq__className",
  "variable": "geist_e531dabc-module__QGiZLq__variable",
});
}}),
"[next]/internal/font/google/geist_e531dabc.js [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$next$5d2f$internal$2f$font$2f$google$2f$geist_e531dabc$2e$module$2e$css__$5b$app$2d$rsc$5d$__$28$css__module$29$__ = __turbopack_context__.i("[next]/internal/font/google/geist_e531dabc.module.css [app-rsc] (css module)");
;
const fontData = {
    className: __TURBOPACK__imported__module__$5b$next$5d2f$internal$2f$font$2f$google$2f$geist_e531dabc$2e$module$2e$css__$5b$app$2d$rsc$5d$__$28$css__module$29$__["default"].className,
    style: {
        fontFamily: "'Geist', 'Geist Fallback'",
        fontStyle: "normal"
    }
};
if (__TURBOPACK__imported__module__$5b$next$5d2f$internal$2f$font$2f$google$2f$geist_e531dabc$2e$module$2e$css__$5b$app$2d$rsc$5d$__$28$css__module$29$__["default"].variable != null) {
    fontData.variable = __TURBOPACK__imported__module__$5b$next$5d2f$internal$2f$font$2f$google$2f$geist_e531dabc$2e$module$2e$css__$5b$app$2d$rsc$5d$__$28$css__module$29$__["default"].variable;
}
const __TURBOPACK__default__export__ = fontData;
}}),
"[next]/internal/font/google/geist_mono_68a01160.module.css [app-rsc] (css module)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v({
  "className": "geist_mono_68a01160-module__YLcDdW__className",
  "variable": "geist_mono_68a01160-module__YLcDdW__variable",
});
}}),
"[next]/internal/font/google/geist_mono_68a01160.js [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$next$5d2f$internal$2f$font$2f$google$2f$geist_mono_68a01160$2e$module$2e$css__$5b$app$2d$rsc$5d$__$28$css__module$29$__ = __turbopack_context__.i("[next]/internal/font/google/geist_mono_68a01160.module.css [app-rsc] (css module)");
;
const fontData = {
    className: __TURBOPACK__imported__module__$5b$next$5d2f$internal$2f$font$2f$google$2f$geist_mono_68a01160$2e$module$2e$css__$5b$app$2d$rsc$5d$__$28$css__module$29$__["default"].className,
    style: {
        fontFamily: "'Geist Mono', 'Geist Mono Fallback'",
        fontStyle: "normal"
    }
};
if (__TURBOPACK__imported__module__$5b$next$5d2f$internal$2f$font$2f$google$2f$geist_mono_68a01160$2e$module$2e$css__$5b$app$2d$rsc$5d$__$28$css__module$29$__["default"].variable != null) {
    fontData.variable = __TURBOPACK__imported__module__$5b$next$5d2f$internal$2f$font$2f$google$2f$geist_mono_68a01160$2e$module$2e$css__$5b$app$2d$rsc$5d$__$28$css__module$29$__["default"].variable;
}
const __TURBOPACK__default__export__ = fontData;
}}),
"[project]/app/layout.tsx [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>RootLayout),
    "generateMetadata": (()=>generateMetadata)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$opentelemetry$2b$_ee4fdf7136c0ae57e36e5bb24f18c6a1$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/next@15.3.0_@opentelemetry+_ee4fdf7136c0ae57e36e5bb24f18c6a1/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$providers$2f$WebSocketProvider$2e$tsx__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/components/providers/WebSocketProvider.tsx [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$sonner$2e$tsx__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/components/ui/sonner.tsx [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$hooks$2f$use$2d$auth$2e$tsx__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/hooks/use-auth.tsx [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$dynamic$2d$metadata$2e$tsx__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/components/dynamic-metadata.tsx [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$system$2d$metadata$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/system-metadata.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$next$5d2f$internal$2f$font$2f$google$2f$geist_e531dabc$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[next]/internal/font/google/geist_e531dabc.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$next$5d2f$internal$2f$font$2f$google$2f$geist_mono_68a01160$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[next]/internal/font/google/geist_mono_68a01160.js [app-rsc] (ecmascript)");
;
;
;
;
;
;
;
;
;
;
async function generateMetadata() {
    try {
        // Try to get metadata from service (will use default if API fails)
        const config = await __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$system$2d$metadata$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["siteMetadataService"].getMetadata();
        return {
            title: config['website_seo_title'] || 'PHYGITAL-V Platform',
            description: config['website_seo_description'] || 'Digital Gold Exchange Platform',
            keywords: config['website_seo_keywords'] || undefined,
            authors: config['website_seo_author'] ? [
                {
                    name: config['website_seo_author']
                }
            ] : undefined,
            creator: config['website_seo_author'] || undefined,
            publisher: config['website_company_name'] || undefined,
            icons: {
                icon: '/favicon_sgs.ico',
                apple: config['website_apple_touch_icon'] || undefined
            },
            manifest: config['website_manifest_url'] || undefined,
            openGraph: {
                title: config['website_seo_og_title'] || config['website_seo_title'] || 'PHYGITAL-V Platform',
                description: config['website_seo_og_description'] || config['website_seo_description'] || 'Digital Gold Exchange Platform',
                url: config['website_site_url'] || undefined,
                siteName: config['website_site_name'] || undefined,
                images: config['website_seo_og_image'] ? [
                    {
                        url: config['website_seo_og_image'],
                        width: 1200,
                        height: 630,
                        alt: config['website_seo_og_title'] || config['website_seo_title'] || 'PHYGITAL-V Platform'
                    }
                ] : undefined,
                locale: config['website_locale'] || undefined,
                type: 'website'
            },
            twitter: {
                card: 'summary_large_image',
                title: config['website_seo_og_title'] || config['website_seo_title'] || 'PHYGITAL-V Platform',
                description: config['website_seo_og_description'] || config['website_seo_description'] || 'Digital Gold Exchange Platform',
                site: config['website_seo_twitter_site'] || undefined,
                creator: config['website_seo_twitter_creator'] || undefined,
                images: config['website_seo_twitter_image'] ? [
                    config['website_seo_twitter_image']
                ] : undefined
            },
            robots: {
                index: true,
                follow: true,
                googleBot: {
                    index: true,
                    follow: true,
                    'max-video-preview': -1,
                    'max-image-preview': 'large',
                    'max-snippet': -1
                }
            },
            verification: {
                google: process.env.NEXT_PUBLIC_GOOGLE_VERIFICATION
            }
        };
    } catch (error) {
        console.error('Failed to generate metadata:', error);
        // Fallback metadata - minimal defaults
        return {
            title: "Loading...",
            description: "Please wait while we load the site information.",
            icons: {
                icon: "/favicon.ico"
            }
        };
    }
}
function RootLayout({ children }) {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$opentelemetry$2b$_ee4fdf7136c0ae57e36e5bb24f18c6a1$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("html", {
        lang: "en",
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$opentelemetry$2b$_ee4fdf7136c0ae57e36e5bb24f18c6a1$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("body", {
            className: `${__TURBOPACK__imported__module__$5b$next$5d2f$internal$2f$font$2f$google$2f$geist_e531dabc$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["default"].variable} ${__TURBOPACK__imported__module__$5b$next$5d2f$internal$2f$font$2f$google$2f$geist_mono_68a01160$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["default"].variable} antialiased`,
            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$opentelemetry$2b$_ee4fdf7136c0ae57e36e5bb24f18c6a1$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$hooks$2f$use$2d$auth$2e$tsx__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["AuthProvider"], {
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$opentelemetry$2b$_ee4fdf7136c0ae57e36e5bb24f18c6a1$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$providers$2f$WebSocketProvider$2e$tsx__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["WebSocketProvider"], {
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$opentelemetry$2b$_ee4fdf7136c0ae57e36e5bb24f18c6a1$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$dynamic$2d$metadata$2e$tsx__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["DynamicMetadata"], {}, void 0, false, {
                            fileName: "[project]/app/layout.tsx",
                            lineNumber: 111,
                            columnNumber: 13
                        }, this),
                        children,
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$opentelemetry$2b$_ee4fdf7136c0ae57e36e5bb24f18c6a1$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$sonner$2e$tsx__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["Toaster"], {
                            richColors: true
                        }, void 0, false, {
                            fileName: "[project]/app/layout.tsx",
                            lineNumber: 113,
                            columnNumber: 13
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/app/layout.tsx",
                    lineNumber: 110,
                    columnNumber: 11
                }, this)
            }, void 0, false, {
                fileName: "[project]/app/layout.tsx",
                lineNumber: 109,
                columnNumber: 9
            }, this)
        }, void 0, false, {
            fileName: "[project]/app/layout.tsx",
            lineNumber: 106,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/app/layout.tsx",
        lineNumber: 105,
        columnNumber: 5
    }, this);
}
}}),
"[project]/node_modules/.pnpm/next@15.3.0_@opentelemetry+_ee4fdf7136c0ae57e36e5bb24f18c6a1/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js [app-rsc] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
"use strict";
module.exports = __turbopack_context__.r("[project]/node_modules/.pnpm/next@15.3.0_@opentelemetry+_ee4fdf7136c0ae57e36e5bb24f18c6a1/node_modules/next/dist/server/route-modules/app-page/module.compiled.js [app-rsc] (ecmascript)").vendored['react-rsc'].ReactJsxDevRuntime; //# sourceMappingURL=react-jsx-dev-runtime.js.map
}}),

};

//# sourceMappingURL=%5Broot-of-the-server%5D__75995dfa._.js.map
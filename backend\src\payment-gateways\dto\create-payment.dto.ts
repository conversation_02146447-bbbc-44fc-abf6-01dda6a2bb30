import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsNumber, IsOptional, IsString, IsUUID, Min } from 'class-validator';
import { PaymentGatewayType } from '../enums/payment-gateway-type.enum';

export class CreatePaymentDto {
  @ApiProperty({
    description: 'ID người dùng thực hiện thanh toán',
    example: '550e8400-e29b-41d4-a716-************',
  })
  @IsUUID()
  @IsNotEmpty()
  userId: string;

  @ApiProperty({
    description: 'ID ví nhận tiền',
    example: '550e8400-e29b-41d4-a716-************',
  })
  @IsUUID()
  @IsNotEmpty()
  walletId: string;

  @ApiProperty({
    description: 'Số tiền thanh toán (VND)',
    example: 100000,
    minimum: 10000,
  })
  @IsNumber()
  @IsNotEmpty()
  @Min(10000)
  amount: number;

  @ApiProperty({
    description: '<PERSON>ại cổng thanh toán',
    enum: PaymentGatewayType,
    example: PaymentGatewayType.VNPAY,
  })
  @IsNotEmpty()
  @IsString()
  gatewayType: PaymentGatewayType;

  @ApiProperty({
    description: 'Địa chỉ IP của người dùng',
    example: '127.0.0.1',
  })
  @IsString()
  @IsOptional()
  ipAddress?: string;

  @ApiProperty({
    description: 'Ghi chú thanh toán',
    example: 'Nạp tiền vào ví',
  })
  @IsString()
  @IsOptional()
  description?: string;

  @ApiProperty({
    description: 'Mã ngân hàng (VNPAY)',
    example: 'VNPAYQR',
    required: false,
  })
  @IsString()
  @IsOptional()
  bankCode?: string;

  @ApiProperty({
    description: 'Ngôn ngữ giao diện (vn/en)',
    example: 'vn',
    required: false,
  })
  @IsString()
  @IsOptional()
  locale?: string;

  @ApiProperty({
    description: 'Loại đơn hàng (VNPAY)',
    example: 'other',
    required: false,
  })
  @IsString()
  @IsOptional()
  orderType?: string;
}

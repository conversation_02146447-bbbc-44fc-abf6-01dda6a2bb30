import { BaseEcomProductsService } from './base.ecom-products.service';
import { EcomProductDto } from '../dto/ecom-product.dto';
import { CustomPaginationQueryDto } from '../../common/dto/custom-pagination-query.dto';
export declare class ReadEcomProductsService extends BaseEcomProductsService {
    findById(id: string, relations?: string[]): Promise<EcomProductDto>;
    findByCode(code: string, relations?: string[]): Promise<EcomProductDto>;
    findBySlug(slug: string, relations?: string[]): Promise<EcomProductDto>;
    findAll(params: CustomPaginationQueryDto, categoryFilter?: string): Promise<{
        data: EcomProductDto[];
        total: number;
    }>;
    findByCategory(categoryId: string, params: CustomPaginationQueryDto): Promise<{
        data: EcomProductDto[];
        total: number;
    }>;
    search(keyword: string, params: CustomPaginationQueryDto): Promise<{
        data: EcomProductDto[];
        total: number;
    }>;
    findDeleted(params: CustomPaginationQueryDto): Promise<{
        data: EcomProductDto[];
        total: number;
    }>;
    exists(id: string): Promise<boolean>;
    isProductCodeDuplicated(productCode: string, excludeId?: string): Promise<boolean>;
    count(filter?: string): Promise<number>;
    getStatistics(): Promise<{
        total: number;
        active: number;
        inactive: number;
    }>;
}

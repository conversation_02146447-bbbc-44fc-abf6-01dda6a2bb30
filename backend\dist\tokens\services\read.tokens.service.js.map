{"version": 3, "file": "read.tokens.service.js", "sourceRoot": "", "sources": ["../../../src/tokens/services/read.tokens.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAIA,2CAAkH;AAClH,qCAAiE;AACjE,6CAAmD;AACnD,yDAAsD;AAEtD,+DAA0D;AAC1D,2DAAiD;AAK1C,IAAM,iBAAiB,GAAvB,MAAM,iBAAkB,SAAQ,uCAAiB;IAGjC;IACA;IAHrB,YAEqB,eAAkC,EAClC,YAA2B;QAE9C,KAAK,CAAC,eAAe,EAAE,YAAY,CAAC,CAAC;QAHlB,oBAAe,GAAf,eAAe,CAAmB;QAClC,iBAAY,GAAZ,YAAY,CAAe;IAGhD,CAAC;IAeD,KAAK,CAAC,OAAO,CAAC,MAQb;QACC,MAAM,EACJ,KAAK,GAAG,EAAE,EACV,IAAI,GAAG,CAAC,EACR,MAAM,GAAG,WAAW,EACpB,SAAS,GAAG,MAAM,EAClB,MAAM,EACN,MAAM,EACN,SAAS,GAAG,EAAE,GACf,GAAG,MAAM,CAAC;QACX,MAAM,IAAI,GAAG,CAAC,IAAI,GAAG,CAAC,CAAC,GAAG,KAAK,CAAC;QAEhC,IAAI,CAAC;YACH,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,sCAAsC,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;YAGlF,MAAM,kBAAkB,GAAG,CAAC,GAAG,SAAS,CAAC,CAAC;YAC1C,IAAI,CAAC,kBAAkB,CAAC,QAAQ,CAAC,SAAS,CAAC;gBAAE,kBAAkB,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;YAChF,IAAI,CAAC,kBAAkB,CAAC,QAAQ,CAAC,SAAS,CAAC;gBAAE,kBAAkB,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;YAChF,IAAI,CAAC,kBAAkB,CAAC,QAAQ,CAAC,SAAS,CAAC;gBAAE,kBAAkB,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;YAChF,IAAI,CAAC,kBAAkB,CAAC,QAAQ,CAAC,UAAU,CAAC;gBAAE,kBAAkB,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;YAGlF,MAAM,kBAAkB,GAAG,IAAI,CAAC,iBAAiB,CAAC,kBAAkB,CAAC,CAAC;YAEtE,MAAM,KAAK,GAAG,IAAI,CAAC,eAAe;iBAC/B,kBAAkB,CAAC,OAAO,CAAC;iBAC3B,iBAAiB,CAAC,gBAAgB,EAAE,UAAU,CAAC;iBAC/C,iBAAiB,CAAC,kBAAkB,EAAE,iBAAiB,CAAC;iBACxD,iBAAiB,CAAC,kBAAkB,EAAE,iBAAiB,CAAC;iBACxD,iBAAiB,CAAC,kBAAkB,EAAE,iBAAiB,CAAC;iBACxD,iBAAiB,CAAC,eAAe,EAAE,SAAS,CAAC;iBAC7C,iBAAiB,CAAC,eAAe,EAAE,SAAS,CAAC;iBAC7C,iBAAiB,CAAC,eAAe,EAAE,SAAS,CAAC;iBAC7C,KAAK,CAAC,EAAE,SAAS,EAAE,KAAK,EAAE,CAAC,CAAC;YAG/B,IAAI,MAAM,EAAE,CAAC;gBACX,KAAK,CAAC,QAAQ,CAAC,IAAI,kBAAQ,CAAC,EAAE,CAAC,EAAE;oBAC/B,EAAE,CAAC,KAAK,CAAC,4CAA4C,EAAE,EAAE,MAAM,EAAE,IAAI,MAAM,GAAG,EAAE,CAAC;yBAC9E,OAAO,CAAC,4CAA4C,EAAE,EAAE,MAAM,EAAE,IAAI,MAAM,GAAG,EAAE,CAAC;yBAChF,OAAO,CAAC,8CAA8C,EAAE,EAAE,MAAM,EAAE,IAAI,MAAM,GAAG,EAAE,CAAC,CAAC;gBACxF,CAAC,CAAC,CAAC,CAAC;YACN,CAAC;YAGD,IAAI,MAAM,EAAE,CAAC;gBACX,MAAM,WAAW,GAAG,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,CAAC;gBAClD,MAAM,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,EAAE,KAAK,CAAC,EAAE,EAAE;oBACnD,IAAI,GAAG,KAAK,WAAW,EAAE,CAAC;wBACxB,KAAK,CAAC,QAAQ,CAAC,SAAS,GAAG,OAAO,GAAG,EAAE,EAAE,EAAE,CAAC,GAAG,CAAC,EAAE,KAAK,EAAE,CAAC,CAAC;oBAC7D,CAAC;gBACH,CAAC,CAAC,CAAC;YACL,CAAC;YAGD,IAAI,kBAAkB,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBAClC,MAAM,gBAAgB,GAAG,CAAC,UAAU,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,kBAAkB,EAAE,kBAAkB,EAAE,kBAAkB,CAAC,CAAC;gBAEnI,kBAAkB;qBACf,MAAM,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,gBAAgB,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;qBACxD,OAAO,CAAC,QAAQ,CAAC,EAAE;oBAElB,IAAI,CAAC,KAAK,CAAC,aAAa,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,gBAAgB,KAAK,SAAS,QAAQ,EAAE,CAAC,EAAE,CAAC;wBACpG,KAAK,CAAC,iBAAiB,CAAC,SAAS,QAAQ,EAAE,EAAE,QAAQ,CAAC,CAAC;oBACzD,CAAC;gBACH,CAAC,CAAC,CAAC;YACP,CAAC;YAGD,MAAM,KAAK,GAAG,MAAM,KAAK,CAAC,QAAQ,EAAE,CAAC;YAGrC,KAAK,CAAC,OAAO,CAAC,SAAS,MAAM,EAAE,EAAE,SAAS,CAAC,CAAC;YAG5C,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAE7B,MAAM,MAAM,GAAG,MAAM,KAAK,CAAC,OAAO,EAAE,CAAC;YAGrC,MAAM,SAAS,GAAG,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC;YAEzD,OAAO;gBACL,IAAI,EAAE,SAAS;gBACf,KAAK;aACN,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,sBAAsB,KAAK,CAAC,OAAO,EAAE,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC;YACtE,IAAI,KAAK,YAAY,4BAAmB,EAAE,CAAC;gBACzC,MAAM,KAAK,CAAC;YACd,CAAC;YACD,MAAM,IAAI,qCAA4B,CAAC,kCAAkC,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;QAC5F,CAAC;IACH,CAAC;IASD,KAAK,CAAC,MAAM,CACV,OAAe,EACf,MAAyC;QAEzC,IAAI,CAAC;YACH,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,oCAAoC,OAAO,EAAE,CAAC,CAAC;YACjE,MAAM,EAAE,IAAI,GAAG,CAAC,EAAE,KAAK,GAAG,EAAE,EAAE,GAAG,MAAM,CAAC;YACxC,MAAM,IAAI,GAAG,CAAC,IAAI,GAAG,CAAC,CAAC,GAAG,KAAK,CAAC;YAChC,MAAM,UAAU,GAAG,IAAI,OAAO,GAAG,CAAC;YAElC,MAAM,KAAK,GAAG,IAAI,CAAC,eAAe;iBAC/B,kBAAkB,CAAC,OAAO,CAAC;iBAC3B,iBAAiB,CAAC,gBAAgB,EAAE,UAAU,CAAC;iBAC/C,iBAAiB,CAAC,kBAAkB,EAAE,iBAAiB,CAAC;iBACxD,iBAAiB,CAAC,kBAAkB,EAAE,iBAAiB,CAAC;iBACxD,iBAAiB,CAAC,kBAAkB,EAAE,iBAAiB,CAAC;iBACxD,iBAAiB,CAAC,eAAe,EAAE,SAAS,CAAC;iBAC7C,iBAAiB,CAAC,eAAe,EAAE,SAAS,CAAC;iBAC7C,iBAAiB,CAAC,eAAe,EAAE,SAAS,CAAC;iBAC7C,KAAK,CAAC,8BAA8B,EAAE,EAAE,SAAS,EAAE,KAAK,EAAE,CAAC;iBAC3D,QAAQ,CACP,IAAI,kBAAQ,CAAC,CAAC,EAAE,EAAE,EAAE;gBAClB,EAAE,CAAC,KAAK,CAAC,gDAAgD,EAAE;oBACzD,UAAU;iBACX,CAAC;qBACC,OAAO,CAAC,gDAAgD,EAAE;oBACzD,UAAU;iBACX,CAAC;qBACD,OAAO,CAAC,sDAAsD,EAAE;oBAC/D,UAAU;iBACX,CAAC,CAAC;YACP,CAAC,CAAC,CACH;iBACA,OAAO,CAAC,iBAAiB,EAAE,MAAM,CAAC;iBAClC,IAAI,CAAC,IAAI,CAAC;iBACV,IAAI,CAAC,KAAK,CAAC,CAAC;YAEf,MAAM,CAAC,MAAM,EAAE,KAAK,CAAC,GAAG,MAAM,KAAK,CAAC,eAAe,EAAE,CAAC;YACtD,MAAM,IAAI,GAAG,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC;YAEpD,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,CAAC;QAC/B,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,2BAA2B,KAAK,CAAC,OAAO,EAAE,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC;YAC3E,MAAM,IAAI,qCAA4B,CAAC,6BAA6B,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;QACvF,CAAC;IACH,CAAC;IAQD,KAAK,CAAC,KAAK,CAAC,MAAe;QACzB,IAAI,CAAC;YACH,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,8CAA8C,MAAM,IAAI,UAAU,EAAE,CAAC,CAAC;YACxF,MAAM,KAAK,GAAG,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,CAAC;YAC5C,OAAO,MAAM,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,EAAE,KAAK,EAAE,CAAC,CAAC;QACrD,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,+BAA+B,KAAK,CAAC,OAAO,EAAE,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC;YAC/E,MAAM,IAAI,qCAA4B,CAAC,iCAAiC,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;QAC3F,CAAC;IACH,CAAC;IAUD,KAAK,CAAC,OAAO,CAAC,EAAU,EAAE,YAAsB,EAAE;QAChD,IAAI,CAAC;YAEH,MAAM,kBAAkB,GAAG,CAAC,GAAG,SAAS,CAAC,CAAC;YAC1C,IAAI,CAAC,kBAAkB,CAAC,QAAQ,CAAC,SAAS,CAAC;gBAAE,kBAAkB,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;YAChF,IAAI,CAAC,kBAAkB,CAAC,QAAQ,CAAC,SAAS,CAAC;gBAAE,kBAAkB,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;YAChF,IAAI,CAAC,kBAAkB,CAAC,QAAQ,CAAC,SAAS,CAAC;gBAAE,kBAAkB,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;YAChF,IAAI,CAAC,kBAAkB,CAAC,QAAQ,CAAC,UAAU,CAAC;gBAAE,kBAAkB,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;YAElF,MAAM,KAAK,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,EAAE,EAAE,kBAAkB,CAAC,CAAC;YAChE,OAAO,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;QAC3B,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,sBAAsB,KAAK,CAAC,OAAO,EAAE,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC;YACtE,IAAI,KAAK,YAAY,0BAAiB,EAAE,CAAC;gBACvC,MAAM,IAAI,0BAAiB,CAAC,gBAAgB,EAAE,iBAAiB,CAAC,CAAC;YACnE,CAAC;YACD,MAAM,IAAI,qCAA4B,CAAC,kCAAkC,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;QAC5F,CAAC;IACH,CAAC;IASD,KAAK,CAAC,aAAa,CAAC,EAAU,EAAE,YAAsB,EAAE;QAEtD,MAAM,kBAAkB,GAAG,CAAC,GAAG,SAAS,CAAC,CAAC;QAC1C,IAAI,CAAC,kBAAkB,CAAC,QAAQ,CAAC,SAAS,CAAC;YAAE,kBAAkB,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QAChF,IAAI,CAAC,kBAAkB,CAAC,QAAQ,CAAC,SAAS,CAAC;YAAE,kBAAkB,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QAChF,IAAI,CAAC,kBAAkB,CAAC,QAAQ,CAAC,SAAS,CAAC;YAAE,kBAAkB,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QAChF,IAAI,CAAC,kBAAkB,CAAC,QAAQ,CAAC,UAAU,CAAC;YAAE,kBAAkB,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;QAElF,MAAM,KAAK,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,EAAE,EAAE,kBAAkB,CAAC,CAAC;QAChE,OAAO,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;IAC3B,CAAC;IAQD,KAAK,CAAC,WAAW,CAAC,MAGjB;QACC,IAAI,CAAC;YACH,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,2BAA2B,CAAC,CAAC;YAC/C,MAAM,EAAE,KAAK,GAAG,EAAE,EAAE,IAAI,GAAG,CAAC,EAAE,GAAG,MAAM,CAAC;YACxC,MAAM,IAAI,GAAG,CAAC,IAAI,GAAG,CAAC,CAAC,GAAG,KAAK,CAAC;YAEhC,MAAM,CAAC,MAAM,EAAE,KAAK,CAAC,GAAG,MAAM,IAAI,CAAC,eAAe,CAAC,YAAY,CAAC;gBAC9D,KAAK,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE;gBAC1B,WAAW,EAAE,IAAI;gBACjB,SAAS,EAAE,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,UAAU,EAAE,kBAAkB,EAAE,kBAAkB,EAAE,kBAAkB,CAAC;gBACpH,IAAI;gBACJ,IAAI,EAAE,KAAK;gBACX,KAAK,EAAE,EAAE,SAAS,EAAE,MAAM,EAAE;aAC7B,CAAC,CAAC;YAEH,OAAO;gBACL,IAAI,EAAE,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;gBAC5C,KAAK;aACN,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,iCAAiC,KAAK,CAAC,OAAO,EAAE,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC;YACjF,MAAM,IAAI,qCAA4B,CAAC,yCAAyC,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;QACnG,CAAC;IACH,CAAC;IAQD,KAAK,CAAC,MAAM,CAAC,MAAsB;QACjC,IAAI,CAAC;YACH,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,+BAA+B,MAAM,EAAE,CAAC,CAAC;YAE3D,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC;gBAC7C,KAAK,EAAE,EAAE,SAAS,EAAE,KAAK,EAAE;gBAC3B,SAAS,EAAE,CAAC,UAAU,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,kBAAkB,EAAE,kBAAkB,EAAE,kBAAkB,CAAC;aACrH,CAAC,CAAC;YAEH,MAAM,IAAI,GAAG,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC;YAEpD,IAAI,MAAM,KAAK,KAAK,EAAE,CAAC;gBACrB,IAAI,IAAI,CAAC,MAAM,KAAK,CAAC;oBAAE,OAAO,EAAE,CAAC;gBACjC,IAAI,CAAC;oBACH,MAAM,OAAO,GAAG,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;oBAC/C,MAAM,OAAO,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,EAAE,CAC/B,MAAM,CAAC,MAAM,CAAC,GAAG,CAAC;yBACf,GAAG,CAAC,CAAC,KAAK,EAAE,EAAE;wBACb,MAAM,MAAM,GAAG,MAAM,CAAC,KAAK,IAAI,EAAE,CAAC,CAAC;wBACnC,IACE,MAAM,CAAC,QAAQ,CAAC,GAAG,CAAC;4BACpB,MAAM,CAAC,QAAQ,CAAC,GAAG,CAAC;4BACpB,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,EACrB,CAAC;4BACD,OAAO,IAAI,MAAM,CAAC,OAAO,CAAC,IAAI,EAAE,IAAI,CAAC,GAAG,CAAC;wBAC3C,CAAC;wBACD,OAAO,MAAM,CAAC;oBAChB,CAAC,CAAC;yBACD,IAAI,CAAC,GAAG,CAAC,CACb,CAAC;oBACF,OAAO,GAAG,OAAO,KAAK,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC;gBAC7C,CAAC;gBAAC,OAAO,QAAQ,EAAE,CAAC;oBAClB,IAAI,CAAC,MAAM,CAAC,KAAK,CACf,yBAAyB,QAAQ,CAAC,OAAO,EAAE,EAC3C,QAAQ,CAAC,KAAK,CACf,CAAC;oBACF,MAAM,IAAI,qCAA4B,CACpC,gCAAgC,CACjC,CAAC;gBACJ,CAAC;YACH,CAAC;YAED,OAAO,IAAI,CAAC;QACd,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,uBAAuB,KAAK,CAAC,OAAO,EAAE,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC;YACvE,MAAM,IAAI,qCAA4B,CAAC,iCAAiC,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;QAC3F,CAAC;IACH,CAAC;CACF,CAAA;AA5UY,8CAAiB;4BAAjB,iBAAiB;IAD7B,IAAA,mBAAU,GAAE;IAGR,WAAA,IAAA,0BAAgB,EAAC,oBAAK,CAAC,CAAA;qCACY,oBAAU;QACb,6BAAa;GAJrC,iBAAiB,CA4U7B"}
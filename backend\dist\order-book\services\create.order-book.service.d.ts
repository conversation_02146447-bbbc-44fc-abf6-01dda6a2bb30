import { EventEmitter2 } from '@nestjs/event-emitter';
import { Repository } from 'typeorm';
import { CommissionProcessorService } from '../../agent-commissions/services/commission-processor.service';
import { AssetService } from '../../token-assets/asset.service';
import { EcomProduct } from 'src/ecom-products/entity/ecom-products.entity';
import { User } from '../../users/entities/user.entity';
import { ReadWalletService } from '../../wallets/services/read.wallet.service';
import { UpdateWalletService } from '../../wallets/services/update.wallet.service';
import { CreateOrderBookDto } from '../dto/create-order-book.dto';
import { OrderBookDto } from '../dto/order-book.dto';
import { OrderBookDetail } from '../entities/order-book-detail.entity';
import { OrderBook } from '../entities/order-book.entity';
import { BaseOrderBookService } from './base.order-book.service';
export declare class CreateOrderBookService extends BaseOrderBookService {
    protected readonly orderBookRepository: Repository<OrderBook>;
    private readonly orderBookDetailRepository;
    private readonly userRepository;
    private readonly ecomProductRepository;
    protected readonly eventEmitter: EventEmitter2;
    private readonly readWalletService;
    private readonly updateWalletService;
    private readonly tokenAssetService;
    private readonly commissionProcessorService;
    constructor(orderBookRepository: Repository<OrderBook>, orderBookDetailRepository: Repository<OrderBookDetail>, userRepository: Repository<User>, ecomProductRepository: Repository<EcomProduct>, eventEmitter: EventEmitter2, readWalletService: ReadWalletService, updateWalletService: UpdateWalletService, tokenAssetService: AssetService, commissionProcessorService: CommissionProcessorService);
    create(createOrderBookDto: CreateOrderBookDto, userId: string): Promise<OrderBookDto>;
    private validateInputAndAuthentication;
    private calculateOrderPrices;
    private calculateTotalPrice;
    private calculateProcessingPrice;
    private calculateBuyOrderPrices;
    private calculateSellOrderPrices;
    private createOrderBookEntity;
    private createBaseOrderBookEntity;
    private configureBuyOrderEntity;
    private configureSellOrderEntity;
    private createOrderBookDetails;
    private createSingleOrderBookDetail;
    private processPaymentTransactions;
    private processBuyOrderPayment;
    private processSellOrderPayment;
    private processImmediateDeliveryPayment;
    private processDepositPayment;
    private validateWalletBalance;
    private processCommissions;
    private processBuyOrderCommissions;
    private processSellOrderCommissions;
    private processSettlementCommission;
    private processDepositCommission;
    private finalizeOrderCreation;
    bulkCreate(createOrderBookDtos: CreateOrderBookDto[], userId: string): Promise<OrderBookDto[]>;
    duplicate(id: string, userId: string): Promise<OrderBookDto>;
    private processPhysicalBuyOrderEcomProductAssets;
}

import { Repository, DataSource } from 'typeorm';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { BaseEcomProductsService } from './base.ecom-products.service';
import { EcomProductPublicDto, HomepageSectionsResponseDto } from '../dto/ecom-product.public.dto';
import { CustomPaginationQueryDto } from '../../common/dto/custom-pagination-query.dto';
import { EcomProduct } from '../entity/ecom-products.entity';
import { ReadEcomProductCategoriesService } from '../../ecom-product-categories/services/read.ecom-product-categories.service';
export declare class PublicEcomProductsService extends BaseEcomProductsService {
    protected readonly ecomProductRepository: Repository<EcomProduct>;
    protected readonly dataSource: DataSource;
    protected readonly eventEmitter: EventEmitter2;
    private readonly readEcomProductCategoriesService;
    private shuffleArray;
    constructor(ecomProductRepository: Repository<EcomProduct>, dataSource: DataSource, eventEmitter: EventEmitter2, readEcomProductCategoriesService: ReadEcomProductCategoriesService);
    private toPublicDto;
    findById(id: string): Promise<EcomProductPublicDto>;
    findByCode(code: string): Promise<EcomProductPublicDto>;
    findBySlug(slug: string): Promise<EcomProductPublicDto>;
    findAll(params: CustomPaginationQueryDto): Promise<{
        data: EcomProductPublicDto[];
        total: number;
    }>;
    findByCategory(categorySlug: string, params: CustomPaginationQueryDto): Promise<{
        data: EcomProductPublicDto[];
        total: number;
    }>;
    findHomepageSections(productsPerCategory?: number): Promise<HomepageSectionsResponseDto>;
}

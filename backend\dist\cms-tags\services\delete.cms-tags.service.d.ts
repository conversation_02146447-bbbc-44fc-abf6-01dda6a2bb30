import { Repository, DataSource } from 'typeorm';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { BaseCmsTagsService } from './base.cms-tags.service';
import { CmsTags } from '../entity/cms-tags.entity';
import { CmsTagDto } from '../dto/cms-tag.dto';
export declare class DeleteCmsTagsService extends BaseCmsTagsService {
    protected readonly tagRepository: Repository<CmsTags>;
    protected readonly dataSource: DataSource;
    protected readonly eventEmitter: EventEmitter2;
    constructor(tagRepository: Repository<CmsTags>, dataSource: DataSource, eventEmitter: EventEmitter2);
    softDelete(id: string, userId: string): Promise<CmsTagDto | null>;
    restore(id: string, userId: string): Promise<CmsTagDto | null>;
    remove(id: string): Promise<{
        affected: number;
    }>;
    bulkSoftDelete(ids: string[], userId: string): Promise<CmsTagDto[]>;
    bulkRestore(ids: string[], userId: string): Promise<CmsTagDto[]>;
    bulkRemove(ids: string[]): Promise<{
        affected: number;
    }>;
}

import { Response } from 'express';
import { OrderBookDto } from './dto/order-book.dto';
import { CreateOrderBookDto } from './dto/create-order-book.dto';
import { UpdateOrderBookDto } from './dto/update-order-book.dto';
import { SettlementOrderBookDto } from './dto/settlement-order-book.dto';
import { OrderBookService } from './order-book.service';
import { SortOrder } from '../common/dto/pagination-query.dto';
import { PaginationResponseDto } from '../common/dto/pagination-response.dto';
import { User } from 'src/users/entities/user.entity';
export declare class OrderBookController {
    private readonly orderBookService;
    private readonly logger;
    constructor(orderBookService: OrderBookService);
    create(createOrderBookDto: CreateOrderBookDto, userId: string): Promise<OrderBookDto>;
    bulkCreate(createOrderBookDtos: CreateOrderBookDto[], userId: string): Promise<OrderBookDto[]>;
    findAll(user: any, limit?: number, page?: number, sortBy?: string, sortOrder?: SortOrder, search?: string, relations?: string, filter?: string): Promise<PaginationResponseDto<OrderBookDto>>;
    search(user: User, keyword: string, limit?: number, page?: number, sortBy?: string, sortOrder?: SortOrder): Promise<PaginationResponseDto<OrderBookDto>>;
    getStatistics(user: any): Promise<{
        total: number;
        businessTypeCounts: {
            NORMAL: number;
            IMMEDIATE_DELIVERY: number;
        };
    }>;
    count(user: User, filter?: string): Promise<number>;
    export(res: Response): Promise<void>;
    findOne(id: string): Promise<OrderBookDto>;
    duplicate(id: string, userId: string): Promise<OrderBookDto>;
    update(id: string, updateOrderBookDto: UpdateOrderBookDto, userId: string): Promise<OrderBookDto>;
    bulkUpdate(updateOrderBookDtos: ({
        id: string;
    } & UpdateOrderBookDto)[], userId: string): Promise<OrderBookDto[]>;
    toggleStatus(id: string, userId: string): Promise<OrderBookDto>;
    remove(id: string): Promise<void>;
    bulkDelete(ids: string[]): Promise<{
        affected: number;
    }>;
    softDelete(id: string, userId: string): Promise<OrderBookDto>;
    bulkSoftDelete(ids: string[], userId: string): Promise<{
        affected: number;
        dtos: OrderBookDto[];
    }>;
    findDeleted(limit?: number, page?: number, sortBy?: string, sortOrder?: SortOrder): Promise<PaginationResponseDto<OrderBookDto>>;
    restore(id: string, userId: string): Promise<OrderBookDto>;
    updateStatusPending(id: string, userId: string): Promise<OrderBookDto>;
    updateStatusCompleted(id: string, settlementData: SettlementOrderBookDto, userId: string): Promise<OrderBookDto>;
    updateStatusWaitPayment(id: string, userId: string): Promise<OrderBookDto>;
    findMyOrders(user: User, limit?: number, page?: number, sortBy?: string, sortOrder?: SortOrder, search?: string, status?: string, relations?: string): Promise<PaginationResponseDto<OrderBookDto>>;
    createTokenWithdrawal(createOrderBookDto: CreateOrderBookDto, userId: string): Promise<OrderBookDto>;
    updateStatusCancelled(id: string, userId: string): Promise<OrderBookDto>;
    approveOrder(id: string, userId: string): Promise<OrderBookDto>;
    extendSettlement(id: string, userId: string): Promise<OrderBookDto>;
}

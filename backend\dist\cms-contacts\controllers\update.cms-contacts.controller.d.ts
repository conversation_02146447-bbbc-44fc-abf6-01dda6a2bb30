import { UpdateCmsContactsService } from '../services/update.cms-contacts.service';
import { ReadCmsContactsService } from '../services/read.cms-contacts.service';
import { CmsContactDto } from '../dto/cms-contact.dto';
import { UpdateCmsContactDto } from '../dto/update.cms-contact.dto';
export declare class UpdateCmsContactsController {
    private readonly updateCmsContactsService;
    private readonly readCmsContactsService;
    constructor(updateCmsContactsService: UpdateCmsContactsService, readCmsContactsService: ReadCmsContactsService);
    update(id: string, updateCmsContactDto: UpdateCmsContactDto, userId: string): Promise<CmsContactDto | null>;
    bulkUpdate(updates: Array<{
        id: string;
        data: UpdateCmsContactDto;
    }>, userId: string): Promise<CmsContactDto[]>;
}

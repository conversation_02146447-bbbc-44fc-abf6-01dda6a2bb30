import { Repository, DataSource } from 'typeorm';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { BaseCmsCategoriesService } from './base.cms-categories.service';
import { SlugCmsCategoriesService } from './slug.cms-categories.service';
import { CmsCategories } from '../entity/cms-categories.entity';
import { UpdateCmsCategoryDto } from '../dto/update.cms-category.dto';
import { CmsCategoryDto } from '../dto/cms-category.dto';
export declare class UpdateCmsCategoriesService extends BaseCmsCategoriesService {
    protected readonly categoryRepository: Repository<CmsCategories>;
    protected readonly dataSource: DataSource;
    protected readonly eventEmitter: EventEmitter2;
    private readonly slugService;
    constructor(categoryRepository: Repository<CmsCategories>, dataSource: DataSource, eventEmitter: EventEmitter2, slugService: SlugCmsCategoriesService);
    update(id: string, updateDto: UpdateCmsCategoryDto, userId: string): Promise<CmsCategoryDto | null>;
    bulkUpdate(updates: Array<{
        id: string;
        data: UpdateCmsCategoryDto;
    }>, userId: string): Promise<CmsCategoryDto[]>;
    updateStatus(id: string, status: string, userId: string): Promise<CmsCategoryDto | null>;
}

# 🚀 Portable VNPAY Payment Gateway Strategy

## 🎯 **Mục tiêu đã đạt được**

### ✅ **Hoàn toàn độc lập**
- **Không phụ thuộc** vào `Wallet`, `Transaction` hay bất kỳ entity nào của dự án hiện tại
- **Tự quản lý** toàn bộ dữ liệu giao dịch VNPAY trong bảng riêng `vnpay_transactions`
- **Portable 100%** - Copy thư mục là sử dụng được ngay

### ✅ **1-1 Mapping với VNPAY**
- Mỗi giao dịch VNPAY có **1 record tương ứng** trong `vnpay_transactions`
- Lưu trữ **toàn bộ thông tin** từ VNPAY (request, response, callback, IPN)
- **Đối chiếu hoàn hảo** với hệ thống VNPAY

### ✅ **Event-driven Architecture**
- **PaymentEventHandler** interface cho external integration
- **Event system** để tích hợp với business logic hiện có
- **Callbacks** linh hoạt cho custom processing

---

## 📁 **Kiến trúc mới vs Cũ**

### ❌ **Kiến trúc cũ (Không portable):**
```typescript
// Phụ thuộc vào entities bên ngoài
import { Transaction } from '../transactions/entities/transaction.entity';
import { Wallet } from '../wallets/entities/wallet.entity';
import { UpdateWalletService } from '../wallets/services/update.wallet.service';

// Business logic lẫn lộn với payment logic
await this.updateWalletService.updateBalance(walletId, amount);
```

### ✅ **Kiến trúc mới (Hoàn toàn portable):**
```typescript
// Hoàn toàn độc lập
import { VnpayTransaction } from './entities/vnpay-transaction.entity';
import { PaymentEventHandler } from './interfaces/payment-integration.interface';

// Tách biệt rõ ràng - chỉ emit events
this.eventEmitter.emit('vnpay.payment.success', paymentData);
if (this.eventHandler?.onPaymentSuccess) {
  await this.eventHandler.onPaymentSuccess(paymentData);
}
```

---

## 🗄️ **Database Schema - Hoàn chỉnh**

### **vnpay_transactions Table:**
```sql
CREATE TABLE vnpay_transactions (
  id UUID PRIMARY KEY,
  merchant_txn_ref VARCHAR(100) UNIQUE NOT NULL,  -- Mã giao dịch của chúng ta
  vnpay_txn_ref VARCHAR(100) UNIQUE,              -- Mã giao dịch VNPAY
  vnpay_txn_no VARCHAR(100),                      -- Số giao dịch VNPAY
  type ENUM('PAYMENT', 'REFUND', 'QUERY'),
  status ENUM('PENDING', 'SUCCESS', 'FAILED', 'CANCELLED', 'EXPIRED'),
  amount BIGINT NOT NULL,                         -- Số tiền (VND)
  currency VARCHAR(10) DEFAULT 'VND',
  order_info VARCHAR(500) NOT NULL,               -- Mô tả giao dịch
  bank_code VARCHAR(50),                          -- Mã ngân hàng
  card_type VARCHAR(50),                          -- Loại thẻ
  vnpay_response_code VARCHAR(10),                -- Mã phản hồi VNPAY
  vnpay_transaction_status VARCHAR(10),           -- Trạng thái giao dịch VNPAY
  vnpay_pay_date VARCHAR(20),                     -- Ngày thanh toán VNPAY
  client_ip VARCHAR(50) NOT NULL,                 -- IP khách hàng
  locale VARCHAR(10) DEFAULT 'vn',                -- Ngôn ngữ
  vnpay_request TEXT,                             -- Raw request data
  vnpay_response TEXT,                            -- Raw response data
  return_callback_data TEXT,                      -- Return URL callback data
  ipn_callback_data TEXT,                         -- IPN callback data
  external_ref VARCHAR(100),                      -- Reference từ ứng dụng (user ID, order ID)
  external_metadata TEXT,                         -- Metadata từ ứng dụng
  error_message VARCHAR(500),                     -- Lỗi nếu có
  retry_count INT DEFAULT 0,                      -- Số lần retry
  expires_at TIMESTAMP,                           -- Thời gian hết hạn
  processed_at TIMESTAMP,                         -- Thời gian xử lý
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Indexes for performance
CREATE INDEX idx_vnpay_merchant_ref ON vnpay_transactions(merchant_txn_ref);
CREATE INDEX idx_vnpay_vnpay_ref ON vnpay_transactions(vnpay_txn_ref);
CREATE INDEX idx_vnpay_external_ref ON vnpay_transactions(external_ref);
CREATE INDEX idx_vnpay_status ON vnpay_transactions(status);
CREATE INDEX idx_vnpay_created_at ON vnpay_transactions(created_at);
```

---

## 🔌 **Integration Interface**

### **PaymentEventHandler - Tích hợp với business logic:**
```typescript
export interface PaymentEventHandler {
  onPaymentCreated?(data: {
    merchantTxnRef: string;
    externalRef: string;
    amount: number;
    paymentUrl: string;
  }): Promise<void>;

  onPaymentSuccess?(data: {
    merchantTxnRef: string;
    externalRef: string;
    amount: number;
    vnpayTxnRef: string;
    vnpayTxnNo: string;
    bankCode?: string;
    payDate: string;
  }): Promise<void>;

  onPaymentFailed?(data: {
    merchantTxnRef: string;
    externalRef: string;
    amount: number;
    responseCode: string;
    message: string;
  }): Promise<void>;
}
```

### **Cách sử dụng trong dự án:**
```typescript
// your-app/payment-handler.service.ts
@Injectable()
export class YourPaymentHandler implements PaymentEventHandler {
  constructor(
    private walletService: WalletService,
    private transactionService: TransactionService,
    private notificationService: NotificationService,
  ) {}

  async onPaymentSuccess(data) {
    // Cập nhật ví của user
    await this.walletService.addBalance(data.externalRef, data.amount);
    
    // Tạo transaction record trong hệ thống
    await this.transactionService.createDeposit(data.externalRef, data.amount);
    
    // Gửi thông báo
    await this.notificationService.sendDepositSuccess(data.externalRef, data.amount);
  }
}

// your-app/app.module.ts
@Module({
  imports: [
    VnpayPaymentModule, // Import portable module
  ],
  providers: [YourPaymentHandler],
})
export class AppModule {
  constructor(
    private vnpayPaymentService: VnpayPaymentService,
    private paymentHandler: YourPaymentHandler,
  ) {
    // Set event handler
    this.vnpayPaymentService.setEventHandler(this.paymentHandler);
  }
}
```

---

## 📦 **Copy to New Project - Checklist**

### **1. Copy Files:**
```bash
# Copy toàn bộ module
cp -r backend/src/payment-gateways /new-project/backend/src/

# Hoặc chỉ copy portable parts
cp -r backend/src/payment-gateways/entities /new-project/backend/src/payment-gateways/
cp -r backend/src/payment-gateways/repositories /new-project/backend/src/payment-gateways/
cp -r backend/src/payment-gateways/interfaces /new-project/backend/src/payment-gateways/
cp -r backend/src/payment-gateways/services/vnpay*.ts /new-project/backend/src/payment-gateways/services/
cp -r backend/src/payment-gateways/controllers/vnpay-payment.controller.ts /new-project/backend/src/payment-gateways/controllers/
cp backend/src/payment-gateways/vnpay-payment.module.ts /new-project/backend/src/payment-gateways/
```

### **2. Environment Variables:**
```env
# VNPAY Configuration
VNPAY_TMN_CODE=your_merchant_code
VNPAY_HASH_SECRET=your_hash_secret
VNPAY_URL=https://sandbox.vnpayment.vn/paymentv2/vpcpay.html
VNPAY_API_URL=https://sandbox.vnpayment.vn/merchant_webapi/api/transaction
VNPAY_RETURN_URL=https://yourdomain.com/api/vnpay-payment/return
VNPAY_IPN_URL=https://yourdomain.com/api/vnpay-payment/ipn
FRONTEND_URL=https://yourdomain.com
```

### **3. Database Migration:**
```bash
# Run migration to create vnpay_transactions table
npm run migration:run
```

### **4. Module Integration:**
```typescript
// app.module.ts
import { VnpayPaymentModule } from './payment-gateways/vnpay-payment.module';

@Module({
  imports: [
    // ... other modules
    VnpayPaymentModule,
  ],
})
export class AppModule {}
```

### **5. Implement Event Handler:**
```typescript
// Implement PaymentEventHandler interface
// Set event handler in VnpayPaymentService
```

---

## 🚀 **API Endpoints - Portable**

### **Core Payment Operations:**
```
POST   /vnpay-payment/create           # Tạo thanh toán
GET    /vnpay-payment/return           # Return URL callback
POST   /vnpay-payment/ipn              # IPN callback
POST   /vnpay-payment/query            # Truy vấn giao dịch
POST   /vnpay-payment/refund           # Hoàn tiền
```

### **Management & Monitoring:**
```
GET    /vnpay-payment/transaction/:ref # Chi tiết giao dịch
GET    /vnpay-payment/transactions     # Danh sách giao dịch
GET    /vnpay-payment/statistics       # Thống kê
GET    /vnpay-payment/health           # Health check
POST   /vnpay-payment/maintenance/mark-expired # Mark expired transactions
```

---

## 📊 **Features Hoàn chỉnh**

### ✅ **Payment Processing:**
- Tạo payment URL
- Xử lý return callback
- Xử lý IPN callback
- Signature verification
- Error handling

### ✅ **Transaction Management:**
- Query transaction status
- Refund processing
- Transaction history
- Statistics & reporting
- Expired transaction handling

### ✅ **Data Integrity:**
- 1-1 mapping với VNPAY
- Complete audit trail
- Raw data storage
- Reconciliation support
- Data validation

### ✅ **Integration Support:**
- Event-driven architecture
- External reference mapping
- Metadata storage
- Flexible callbacks
- Error notifications

### ✅ **Production Ready:**
- Comprehensive logging
- Error handling
- Performance optimization
- Security best practices
- Monitoring endpoints

---

## 🎉 **Kết quả**

### **✅ Đã giải quyết hoàn toàn:**
1. **Dependency issues** - Module hoàn toàn độc lập
2. **Portability** - Copy là dùng được ngay
3. **1-1 Mapping** - Mỗi giao dịch VNPAY có 1 record
4. **Reconciliation** - Đối chiếu hoàn hảo với VNPAY
5. **Integration** - Event-driven, flexible callbacks

### **🚀 Sẵn sàng cho:**
- Copy sang bất kỳ dự án NestJS nào
- Tích hợp với bất kỳ business logic nào
- Production deployment
- Scaling và monitoring
- Compliance và audit

**Module này đã hoàn toàn portable và production-ready!** 🎯

(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/components/common/admin/payment-methods/payment-methods.tsx [app-client] (ecmascript, next/dynamic entry, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/_4d21ccbb._.js",
  "static/chunks/node_modules__pnpm_f1619b24._.js",
  "static/chunks/components_common_admin_payment-methods_payment-methods_tsx_74a54165._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/components/common/admin/payment-methods/payment-methods.tsx [app-client] (ecmascript, next/dynamic entry)");
    });
});
}}),
}]);
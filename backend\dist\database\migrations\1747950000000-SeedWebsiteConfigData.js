"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.SeedWebsiteConfigData1747950000000 = void 0;
class SeedWebsiteConfigData1747950000000 {
    name = 'SeedWebsiteConfigData1747950000000';
    async up(queryRunner) {
        const websiteConfigs = [
            {
                configKey: 'website_site_name',
                configValue: 'PHYGITAL-V | CÔNG TY CỔ PHẦN PHYGITAL-V',
                description: 'Tên website',
                configGroup: 'website_general',
                configType: 'text',
                groupDisplayName: 'Thông tin chung',
                groupDescription: 'C<PERSON>u hình thông tin cơ bản của website',
                groupIcon: 'globe',
                groupOrder: 1,
            },
            {
                configKey: 'website_site_description',
                configValue: 'Nền tảng giao dịch bạc trực tuyến và vật chất hàng đầu V<PERSON>t Nam',
                description: '<PERSON><PERSON> tả website',
                configGroup: 'website_general',
                configType: 'textarea',
                groupDisplayName: 'Thông tin chung',
                groupDescription: 'Cấu hình thông tin cơ bản của website',
                groupIcon: 'globe',
                groupOrder: 1,
            },
            {
                configKey: 'website_site_keywords',
                configValue: 'bạc, kim hoàn, trang sức, giao dịch, phygital-v',
                description: 'Từ khóa SEO',
                configGroup: 'website_general',
                configType: 'text',
                groupDisplayName: 'Thông tin chung',
                groupDescription: 'Cấu hình thông tin cơ bản của website',
                groupIcon: 'globe',
                groupOrder: 1,
            },
            {
                configKey: 'website_logo_url',
                configValue: '/phygital-v-nobg.png',
                description: 'URL logo website',
                configGroup: 'website_general',
                configType: 'url',
                groupDisplayName: 'Thông tin chung',
                groupDescription: 'Cấu hình thông tin cơ bản của website',
                groupIcon: 'globe',
                groupOrder: 1,
            },
            {
                configKey: 'website_favicon_url',
                configValue: '/favicon.ico',
                description: 'URL favicon',
                configGroup: 'website_general',
                configType: 'url',
                groupDisplayName: 'Thông tin chung',
                groupDescription: 'Cấu hình thông tin cơ bản của website',
                groupIcon: 'globe',
                groupOrder: 1,
            },
            {
                configKey: 'website_company_name',
                configValue: 'CÔNG TY CỔ PHẦN PHYGITAL-V',
                description: 'Tên công ty',
                configGroup: 'website_general',
                configType: 'text',
                groupDisplayName: 'Thông tin chung',
                groupDescription: 'Cấu hình thông tin cơ bản của website',
                groupIcon: 'globe',
                groupOrder: 1,
            },
            {
                configKey: 'website_company_slogan',
                configValue: 'Tinh xảo - Độc đáo - Chất lượng cao',
                description: 'Slogan công ty',
                configGroup: 'website_general',
                configType: 'text',
                groupDisplayName: 'Thông tin chung',
                groupDescription: 'Cấu hình thông tin cơ bản của website',
                groupIcon: 'globe',
                groupOrder: 1,
            },
            {
                configKey: 'website_company_address',
                configValue: 'Lô 20, ngõ 19 đường Duy Tân, Phường Dịch Vọng Hậu, Quận Cầu Giấy, Hà Nội',
                description: 'Địa chỉ công ty',
                configGroup: 'website_contact',
                configType: 'textarea',
                groupDisplayName: 'Thông tin liên hệ',
                groupDescription: 'Cấu hình thông tin liên hệ và địa chỉ công ty',
                groupIcon: 'contact',
                groupOrder: 2,
            },
            {
                configKey: 'website_company_phone',
                configValue: '0392 067 111',
                description: 'Số điện thoại công ty',
                configGroup: 'website_contact',
                configType: 'text',
                groupDisplayName: 'Thông tin liên hệ',
                groupDescription: 'Cấu hình thông tin liên hệ và địa chỉ công ty',
                groupIcon: 'contact',
                groupOrder: 2,
            },
            {
                configKey: 'website_company_email',
                configValue: '<EMAIL>',
                description: 'Email công ty',
                configGroup: 'website_contact',
                configType: 'email',
                groupDisplayName: 'Thông tin liên hệ',
                groupDescription: 'Cấu hình thông tin liên hệ và địa chỉ công ty',
                groupIcon: 'contact',
                groupOrder: 2,
            },
            {
                configKey: 'website_support_phone',
                configValue: '0392 067 111',
                description: 'Số điện thoại hỗ trợ',
                configGroup: 'website_contact',
                configType: 'text',
                groupDisplayName: 'Thông tin liên hệ',
                groupDescription: 'Cấu hình thông tin liên hệ và địa chỉ công ty',
                groupIcon: 'contact',
                groupOrder: 2,
            },
            {
                configKey: 'website_working_hours',
                configValue: '8:00 - 17:00 (Thứ 2 - Thứ 6)',
                description: 'Giờ làm việc',
                configGroup: 'website_contact',
                configType: 'text',
                groupDisplayName: 'Thông tin liên hệ',
                groupDescription: 'Cấu hình thông tin liên hệ và địa chỉ công ty',
                groupIcon: 'contact',
                groupOrder: 2,
            },
            {
                configKey: 'website_header_show_topbar',
                configValue: 'true',
                description: 'Hiển thị thanh topbar',
                configGroup: 'website_header',
                configType: 'boolean',
                groupDisplayName: 'Cấu hình Header',
                groupDescription: 'Cấu hình hiển thị và tính năng của header website',
                groupIcon: 'header',
                groupOrder: 3,
            },
            {
                configKey: 'website_header_topbar_text',
                configValue: 'PHYGITAL-V | CÔNG TY CỔ PHẦN PHYGITAL-V',
                description: 'Nội dung thanh topbar',
                configGroup: 'website_header',
                configType: 'text',
                groupDisplayName: 'Cấu hình Header',
                groupDescription: 'Cấu hình hiển thị và tính năng của header website',
                groupIcon: 'header',
                groupOrder: 3,
            },
            {
                configKey: 'website_header_show_search',
                configValue: 'true',
                description: 'Hiển thị ô tìm kiếm',
                configGroup: 'website_header',
                configType: 'boolean',
                groupDisplayName: 'Cấu hình Header',
                groupDescription: 'Cấu hình hiển thị và tính năng của header website',
                groupIcon: 'header',
                groupOrder: 3,
            },
            {
                configKey: 'website_header_show_cart',
                configValue: 'true',
                description: 'Hiển thị giỏ hàng',
                configGroup: 'website_header',
                configType: 'boolean',
                groupDisplayName: 'Cấu hình Header',
                groupDescription: 'Cấu hình hiển thị và tính năng của header website',
                groupIcon: 'header',
                groupOrder: 3,
            },
            {
                configKey: 'website_header_sticky',
                configValue: 'true',
                description: 'Header dính khi cuộn',
                configGroup: 'website_header',
                configType: 'boolean',
                groupDisplayName: 'Cấu hình Header',
                groupDescription: 'Cấu hình hiển thị và tính năng của header website',
                groupIcon: 'header',
                groupOrder: 3,
            },
            {
                configKey: 'website_homepage_show_hero_carousel',
                configValue: 'true',
                description: 'Hiển thị slider banner trang chủ',
                configGroup: 'website_homepage',
                configType: 'boolean',
                groupDisplayName: 'Cấu hình Trang chủ',
                groupDescription: 'Cấu hình hiển thị các phần trên trang chủ',
                groupIcon: 'home',
                groupOrder: 5,
            },
            {
                configKey: 'website_homepage_show_services',
                configValue: 'true',
                description: 'Hiển thị phần dịch vụ',
                configGroup: 'website_homepage',
                configType: 'boolean',
                groupDisplayName: 'Cấu hình Trang chủ',
                groupDescription: 'Cấu hình hiển thị các phần trên trang chủ',
                groupIcon: 'home',
                groupOrder: 5,
            },
            {
                configKey: 'website_homepage_show_featured_products',
                configValue: 'true',
                description: 'Hiển thị sản phẩm nổi bật',
                configGroup: 'website_homepage',
                configType: 'boolean',
                groupDisplayName: 'Cấu hình Trang chủ',
                groupDescription: 'Cấu hình hiển thị các phần trên trang chủ',
                groupIcon: 'home',
                groupOrder: 5,
            },
            {
                configKey: 'website_homepage_featured_products_limit',
                configValue: '8',
                description: 'Số lượng sản phẩm nổi bật hiển thị',
                configGroup: 'website_homepage',
                configType: 'number',
                groupDisplayName: 'Cấu hình Trang chủ',
                groupDescription: 'Cấu hình hiển thị các phần trên trang chủ',
                groupIcon: 'home',
                groupOrder: 5,
            },
            {
                configKey: 'website_homepage_show_testimonials',
                configValue: 'true',
                description: 'Hiển thị phản hồi khách hàng',
                configGroup: 'website_homepage',
                configType: 'boolean',
                groupDisplayName: 'Cấu hình Trang chủ',
                groupDescription: 'Cấu hình hiển thị các phần trên trang chủ',
                groupIcon: 'home',
                groupOrder: 5,
            },
            {
                configKey: 'website_homepage_show_news',
                configValue: 'true',
                description: 'Hiển thị tin tức',
                configGroup: 'website_homepage',
                configType: 'boolean',
                groupDisplayName: 'Cấu hình Trang chủ',
                groupDescription: 'Cấu hình hiển thị các phần trên trang chủ',
                groupIcon: 'home',
                groupOrder: 5,
            },
            {
                configKey: 'website_homepage_news_limit',
                configValue: '6',
                description: 'Số lượng tin tức hiển thị',
                configGroup: 'website_homepage',
                configType: 'number',
                groupDisplayName: 'Cấu hình Trang chủ',
                groupDescription: 'Cấu hình hiển thị các phần trên trang chủ',
                groupIcon: 'home',
                groupOrder: 5,
            },
            {
                configKey: 'website_homepage_show_collections',
                configValue: 'true',
                description: 'Hiển thị phần bộ sưu tập',
                configGroup: 'website_homepage',
                configType: 'boolean',
                groupDisplayName: 'Cấu hình Trang chủ',
                groupDescription: 'Cấu hình hiển thị các phần trên trang chủ',
                groupIcon: 'home',
                groupOrder: 5,
            },
            {
                configKey: 'website_homepage_show_info_section',
                configValue: 'true',
                description: 'Hiển thị phần thông tin công ty',
                configGroup: 'website_homepage',
                configType: 'boolean',
                groupDisplayName: 'Cấu hình Trang chủ',
                groupDescription: 'Cấu hình hiển thị các phần trên trang chủ',
                groupIcon: 'home',
                groupOrder: 5,
            },
            {
                configKey: 'website_services_show_hero_section',
                configValue: 'true',
                description: 'Hiển thị phần hero trên trang dịch vụ',
                configGroup: 'website_services',
                configType: 'boolean',
                groupDisplayName: 'Cấu hình Dịch vụ',
                groupDescription: 'Cấu hình trang dịch vụ và CTA',
                groupIcon: 'package',
                groupOrder: 6,
            },
            {
                configKey: 'website_services_hero_title',
                configValue: 'Dịch vụ chuyên nghiệp',
                description: 'Tiêu đề phần hero trang dịch vụ',
                configGroup: 'website_services',
                configType: 'text',
                groupDisplayName: 'Cấu hình Dịch vụ',
                groupDescription: 'Cấu hình trang dịch vụ và CTA',
                groupIcon: 'package',
                groupOrder: 6,
            },
            {
                configKey: 'website_services_hero_description',
                configValue: 'PHV cung cấp đa dạng dịch vụ về bạc cao cấp, từ chế tác theo yêu cầu đến tư vấn phát triển thương hiệu',
                description: 'Mô tả phần hero trang dịch vụ',
                configGroup: 'website_services',
                configType: 'textarea',
                groupDisplayName: 'Cấu hình Dịch vụ',
                groupDescription: 'Cấu hình trang dịch vụ và CTA',
                groupIcon: 'package',
                groupOrder: 6,
            },
            {
                configKey: 'website_services_show_cta_section',
                configValue: 'true',
                description: 'Hiển thị phần call-to-action',
                configGroup: 'website_services',
                configType: 'boolean',
                groupDisplayName: 'Cấu hình Dịch vụ',
                groupDescription: 'Cấu hình trang dịch vụ và CTA',
                groupIcon: 'package',
                groupOrder: 6,
            },
            {
                configKey: 'website_services_cta_title',
                configValue: 'Bạn cần tư vấn dịch vụ?',
                description: 'Tiêu đề phần CTA',
                configGroup: 'website_services',
                configType: 'text',
                groupDisplayName: 'Cấu hình Dịch vụ',
                groupDescription: 'Cấu hình trang dịch vụ và CTA',
                groupIcon: 'package',
                groupOrder: 6,
            },
            {
                configKey: 'website_services_cta_description',
                configValue: 'Đội ngũ chuyên gia của PHV sẵn sàng hỗ trợ bạn tìm ra giải pháp tối ưu nhất',
                description: 'Mô tả phần CTA',
                configGroup: 'website_services',
                configType: 'textarea',
                groupDisplayName: 'Cấu hình Dịch vụ',
                groupDescription: 'Cấu hình trang dịch vụ và CTA',
                groupIcon: 'package',
                groupOrder: 6,
            },
            {
                configKey: 'website_services_phone_display',
                configValue: '0392 067 111',
                description: 'Số điện thoại hiển thị trong CTA',
                configGroup: 'website_services',
                configType: 'text',
                groupDisplayName: 'Cấu hình Dịch vụ',
                groupDescription: 'Cấu hình trang dịch vụ và CTA',
                groupIcon: 'package',
                groupOrder: 6,
            },
            {
                configKey: 'website_features_enable_search',
                configValue: 'true',
                description: 'Bật tính năng tìm kiếm',
                configGroup: 'website_features',
                configType: 'boolean',
                groupDisplayName: 'Tính năng Website',
                groupDescription: 'Bật/tắt các tính năng của website',
                groupIcon: 'palette',
                groupOrder: 12,
            },
            {
                configKey: 'website_features_enable_newsletter',
                configValue: 'true',
                description: 'Bật tính năng đăng ký nhận tin',
                configGroup: 'website_features',
                configType: 'boolean',
                groupDisplayName: 'Tính năng Website',
                groupDescription: 'Bật/tắt các tính năng của website',
                groupIcon: 'palette',
                groupOrder: 12,
            },
            {
                configKey: 'website_features_enable_inquiry_form',
                configValue: 'true',
                description: 'Bật form yêu cầu thông tin',
                configGroup: 'website_features',
                configType: 'boolean',
                groupDisplayName: 'Tính năng Website',
                groupDescription: 'Bật/tắt các tính năng của website',
                groupIcon: 'palette',
                groupOrder: 12,
            },
            {
                configKey: 'website_features_enable_quote_request',
                configValue: 'true',
                description: 'Bật tính năng yêu cầu báo giá',
                configGroup: 'website_features',
                configType: 'boolean',
                groupDisplayName: 'Tính năng Website',
                groupDescription: 'Bật/tắt các tính năng của website',
                groupIcon: 'palette',
                groupOrder: 12,
            },
            {
                configKey: 'website_features_maintenance_mode',
                configValue: 'false',
                description: 'Chế độ bảo trì website',
                configGroup: 'website_features',
                configType: 'boolean',
                groupDisplayName: 'Tính năng Website',
                groupDescription: 'Bật/tắt các tính năng của website',
                groupIcon: 'palette',
                groupOrder: 12,
            },
            {
                configKey: 'website_features_maintenance_message',
                configValue: 'Website đang bảo trì, vui lòng quay lại sau.',
                description: 'Thông báo khi bảo trì',
                configGroup: 'website_features',
                configType: 'textarea',
                groupDisplayName: 'Tính năng Website',
                groupDescription: 'Bật/tắt các tính năng của website',
                groupIcon: 'palette',
                groupOrder: 12,
            },
            {
                configKey: 'website_features_show_breadcrumb',
                configValue: 'true',
                description: 'Hiển thị breadcrumb navigation',
                configGroup: 'website_features',
                configType: 'boolean',
                groupDisplayName: 'Tính năng Website',
                groupDescription: 'Bật/tắt các tính năng của website',
                groupIcon: 'palette',
                groupOrder: 12,
            },
            {
                configKey: 'website_features_enable_back_to_top',
                configValue: 'true',
                description: 'Bật nút quay lên đầu trang',
                configGroup: 'website_features',
                configType: 'boolean',
                groupDisplayName: 'Tính năng Website',
                groupDescription: 'Bật/tắt các tính năng của website',
                groupIcon: 'palette',
                groupOrder: 12,
            },
        ];
        for (const config of websiteConfigs) {
            try {
                const existingConfig = await queryRunner.query(`SELECT * FROM "system_configs" WHERE "config_key" = $1`, [config.configKey]);
                if (existingConfig.length === 0) {
                    await queryRunner.query(`
                        INSERT INTO "system_configs"
                        ("config_key", "config_value", "description", "config_group", "config_type", 
                         "group_display_name", "group_description", "group_icon", "group_order", 
                         "is_group_config", "created_at", "updated_at")
                        VALUES
                        ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, NOW(), NOW())
                    `, [
                        config.configKey,
                        config.configValue,
                        config.description,
                        config.configGroup,
                        config.configType,
                        config.groupDisplayName,
                        config.groupDescription,
                        config.groupIcon,
                        config.groupOrder,
                        false
                    ]);
                }
                else {
                }
            }
            catch (error) {
                console.error(`❌ Lỗi khi thêm cấu hình ${config.configKey}:`, error.message);
            }
        }
    }
    async down(queryRunner) {
        await queryRunner.query(`
            DELETE FROM "system_configs" 
            WHERE "config_group" LIKE 'website_%'
        `);
    }
}
exports.SeedWebsiteConfigData1747950000000 = SeedWebsiteConfigData1747950000000;
//# sourceMappingURL=1747950000000-SeedWebsiteConfigData.js.map
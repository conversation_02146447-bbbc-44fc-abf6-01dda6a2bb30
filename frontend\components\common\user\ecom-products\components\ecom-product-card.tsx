'use client';

import React from 'react';
import { Button } from '@/components/ui/button';
import { ShoppingCart } from 'lucide-react';
import { cn } from '@/lib/utils';
import { EcomProduct } from '../type/ecom-product';
import { toast } from 'sonner';

/**
 * Props interface cho EcomProductCard component
 */
export interface EcomProductCardProps {
  product: EcomProduct;
  onAddToCart: (product: EcomProduct, quantity?: number) => void;
  onProductClick?: (product: EcomProduct) => void;
  className?: string;
}

/**
 * Component hiển thị thông tin sản phẩm dưới dạng card cho giao diện user
 * Sao chép hoàn toàn thiết kế từ product-list-item.tsx:
 * - Layout y nguyên với aspect-square image container
 * - Text styling và positioning giống hệt
 * - Button layout và styling tương tự
 * - Responsive và grid-friendly cho 6 columns layout
 * - Không quản lý tồn kho - tất cả sản phẩm active đều có thể đặt hàng
 *
 * @param product - Thông tin sản phẩm từ EcomProduct interface
 * @param onAddToCart - Callback function khi user nhấn button "Giỏ hàng"
 * @param onProductClick - Callback function khi user nhấn vào sản phẩm
 * @param className - CSS classes tùy chỉnh
 */
export const EcomProductCard = React.forwardRef<HTMLDivElement, EcomProductCardProps>(
  ({ product, onAddToCart, onProductClick, className }, ref) => {
    // Xử lý khi nhấn button "Giỏ hàng"
    const handleAddToCart = (e: React.MouseEvent) => {
      e.preventDefault();
      e.stopPropagation();
      onAddToCart(product, 1); // Thêm 1 sản phẩm vào giỏ hàng
      toast.success(`Đã thêm ${product.productName} vào giỏ hàng`);
    };

    // Xử lý khi click vào sản phẩm
    const handleClick = () => {
      if (onProductClick) {
        onProductClick(product);
      }
    };

    // Xử lý lỗi khi load hình ảnh
    const handleImageError = (e: React.SyntheticEvent<HTMLImageElement>) => {
      const target = e.target as HTMLImageElement;
      target.src = 'https://placehold.co/400x400?text=No+Image';
    };

    return (
      <div
        ref={ref}
        className={cn(
          "relative flex w-64 max-w-full flex-none scroll-ml-6 flex-col gap-3 rounded-large bg-white p-4 shadow-medium cursor-pointer hover:shadow-lg transition-shadow",
          className,
        )}
        role="article"
        aria-label={`Sản phẩm ${product.productName}`}
        onClick={handleClick}
      >
        {/* Image Container */}
        <div className="relative w-full aspect-square flex items-center justify-center">
          <div className="relative w-full aspect-square flex items-center justify-center">
            <img
              alt={product.productName}
              className="z-0 object-contain object-center hover:scale-110 transition-transform duration-300"
              style={{
                width: '256px',
                height: '256px',
                maxWidth: '100%',
                maxHeight: '100%'
              }}
              src={product.imageUrl || 'https://placehold.co/400x400?text=No+Image'}
              onError={handleImageError}
              loading="lazy"
            />
          </div>
        </div>

        {/* Content Container with fixed height */}
        <div className="flex min-h-[120px] flex-col px-1">
          {/* Text Content Container with fixed height */}
          <div className="flex flex-col gap-2 min-h-[80px]">
            <div className="flex items-center justify-center text-center h-[40px]">
              <h3 
                className="text-medium font-medium text-gray-700 line-clamp-2"
                style={{
                  display: '-webkit-box',
                  WebkitLineClamp: 2,
                  WebkitBoxOrient: 'vertical',
                  overflow: 'hidden'
                }}
                title={product.productName}
              >
                {product.productName}
              </h3>
            </div>
            
            <div className="h-[40px] flex items-center justify-center">
              {product.description && (
                <div
                  className="text-small text-gray-500 text-center line-clamp-2"
                  style={{
                    display: '-webkit-box',
                    WebkitLineClamp: 2,
                    WebkitBoxOrient: 'vertical',
                    overflow: 'hidden'
                  }}
                  title={product.description.replace(/<[^>]*>/g, '')}
                  dangerouslySetInnerHTML={{ __html: product.description }}
                />
              )}
            </div>
          </div>

          {/* Button Container - Fixed at bottom */}
          <div className="flex gap-2 mt-8">
            <Button
              onClick={handleAddToCart}
              disabled={!product.isActive}
              className="w-full font-medium gap-2 h-[40px]"
              variant="default"
              aria-label={`Thêm ${product.productName} vào giỏ hàng`}
            >
              <ShoppingCart className="h-4 w-4" />
              Giỏ hàng
            </Button>
          </div>
        </div>
      </div>
    );
  }
);

EcomProductCard.displayName = 'EcomProductCard';

export default EcomProductCard;

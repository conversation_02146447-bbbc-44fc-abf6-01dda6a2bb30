import { BulkOperationResponseDto, BulkUpdateCmsPartnersDto, BulkUpdateCmsPartnersStatusDto, CmsPartnerDto, UpdateCmsPartnerDisplayOrderDto, UpdateCmsPartnerDto, UpdateCmsPartnerStatusDto, UpdateCmsPartnerTypeDto } from '../dto';
import { UpdateCmsPartnersService } from '../services/update.cms-partners.service';
export declare class UpdateCmsPartnersController {
    private readonly updateCmsPartnersService;
    private readonly logger;
    constructor(updateCmsPartnersService: UpdateCmsPartnersService);
    update(id: string, updateCmsPartnerDto: UpdateCmsPartnerDto, req: any): Promise<CmsPartnerDto>;
    bulkUpdate(bulkUpdateDto: BulkUpdateCmsPartnersDto, req: any): Promise<BulkOperationResponseDto>;
    activate(id: string, req: any): Promise<CmsPartnerDto>;
    deactivate(id: string, req: any): Promise<CmsPartnerDto>;
    updateStatus(id: string, updateStatusDto: UpdateCmsPartnerStatusDto, req: any): Promise<CmsPartnerDto>;
    updateDisplayOrder(id: string, updateDisplayOrderDto: UpdateCmsPartnerDisplayOrderDto, req: any): Promise<CmsPartnerDto>;
    updateType(id: string, updateTypeDto: UpdateCmsPartnerTypeDto, req: any): Promise<CmsPartnerDto>;
    bulkActivate(bulkUpdateStatusDto: BulkUpdateCmsPartnersStatusDto, req: any): Promise<BulkOperationResponseDto>;
    bulkDeactivate(bulkUpdateStatusDto: BulkUpdateCmsPartnersStatusDto, req: any): Promise<BulkOperationResponseDto>;
    bulkUpdateStatus(bulkUpdateStatusDto: BulkUpdateCmsPartnersStatusDto, req: any): Promise<BulkOperationResponseDto>;
    restore(id: string, req: any): Promise<CmsPartnerDto>;
}

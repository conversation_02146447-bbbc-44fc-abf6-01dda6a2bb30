import { Repository, DataSource } from 'typeorm';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { BaseCmsCustomerFeedbacksService } from './base.cms-customer-feedbacks.service';
import { CmsCustomerFeedbacks } from '../entity/cms-customer-feedbacks.entity';
import { CmsCustomerFeedbackDto } from '../dto/cms-customer-feedback.dto';
export declare class DeleteCmsCustomerFeedbacksService extends BaseCmsCustomerFeedbacksService {
    protected readonly feedbackRepository: Repository<CmsCustomerFeedbacks>;
    protected readonly dataSource: DataSource;
    protected readonly eventEmitter: EventEmitter2;
    constructor(feedbackRepository: Repository<CmsCustomerFeedbacks>, dataSource: DataSource, eventEmitter: EventEmitter2);
    softDelete(id: string, userId: string): Promise<CmsCustomerFeedbackDto | null>;
    restore(id: string, userId: string): Promise<CmsCustomerFeedbackDto | null>;
    remove(id: string): Promise<{
        affected: number;
    }>;
    bulkSoftDelete(ids: string[], userId: string): Promise<CmsCustomerFeedbackDto[]>;
    bulkRestore(ids: string[], userId: string): Promise<CmsCustomerFeedbackDto[]>;
    bulkRemove(ids: string[]): Promise<{
        affected: number;
    }>;
}

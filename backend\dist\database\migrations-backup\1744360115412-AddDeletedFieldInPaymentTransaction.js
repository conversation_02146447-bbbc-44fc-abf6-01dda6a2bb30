"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AddDeletedFieldInPaymentTransaction1744360115412 = void 0;
class AddDeletedFieldInPaymentTransaction1744360115412 {
    async up(queryRunner) {
        await queryRunner.query(`
            ALTER TABLE "payment_transactions" 
            ADD COLUMN "deleted_at" TIMESTAMP NULL
        `);
    }
    async down(queryRunner) {
        await queryRunner.query(`
            ALTER TABLE "payment_transactions" 
            DROP COLUMN "deleted_at"
        `);
    }
}
exports.AddDeletedFieldInPaymentTransaction1744360115412 = AddDeletedFieldInPaymentTransaction1744360115412;
//# sourceMappingURL=1744360115412-AddDeletedFieldInPaymentTransaction.js.map
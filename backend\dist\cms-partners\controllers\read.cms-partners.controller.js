"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var ReadCmsPartnersController_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.ReadCmsPartnersController = void 0;
const openapi = require("@nestjs/swagger");
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const jwt_auth_guard_1 = require("../../common/guards/jwt-auth.guard");
const roles_decorator_1 = require("../../common/decorators/roles.decorator");
const read_cms_partners_service_1 = require("../services/read.cms-partners.service");
const dto_1 = require("../dto");
const custom_pagination_query_dto_1 = require("../../common/dto/custom-pagination-query.dto");
const pagination_response_dto_1 = require("../../common/dto/pagination-response.dto");
const custom_pagination_meta_dto_1 = require("../../common/dto/custom-pagination-meta.dto");
let ReadCmsPartnersController = ReadCmsPartnersController_1 = class ReadCmsPartnersController {
    readCmsPartnersService;
    logger = new common_1.Logger(ReadCmsPartnersController_1.name);
    constructor(readCmsPartnersService) {
        this.readCmsPartnersService = readCmsPartnersService;
    }
    async findAll(paginationQuery) {
        this.logger.debug(`Đang lấy danh sách đối tác CMS với tham số: ${JSON.stringify(paginationQuery)}`);
        const relations = ['creator', 'updater', 'deleter'];
        const { data, total } = await this.readCmsPartnersService.findAll(paginationQuery, relations);
        const meta = new custom_pagination_meta_dto_1.CustomPageMetaDto({
            pageQueryDto: paginationQuery,
            itemCount: total,
        });
        return new pagination_response_dto_1.PaginationResponseDto(data, meta);
    }
    async findByStatus(status, paginationQuery, req) {
        this.logger.log(`Getting CMS Partners by status ${status} for user: ${req.user.id}`);
        const { data, total } = await this.readCmsPartnersService.findByStatus(status, paginationQuery);
        const meta = new custom_pagination_meta_dto_1.CustomPageMetaDto({
            pageQueryDto: paginationQuery,
            itemCount: total,
        });
        return new pagination_response_dto_1.PaginationResponseDto(data, meta);
    }
    async findByType(type, paginationQuery) {
        this.logger.debug(`Đang lấy danh sách đối tác CMS theo loại ${type}`);
        const { data, total } = await this.readCmsPartnersService.findByType(type, paginationQuery);
        const meta = new custom_pagination_meta_dto_1.CustomPageMetaDto({
            pageQueryDto: paginationQuery,
            itemCount: total,
        });
        return new pagination_response_dto_1.PaginationResponseDto(data, meta);
    }
    async getActivePartners(paginationQuery, req) {
        this.logger.log(`Getting active CMS Partners for user: ${req.user.id}`);
        const { data, total } = await this.readCmsPartnersService.getActivePartners(paginationQuery);
        const meta = new custom_pagination_meta_dto_1.CustomPageMetaDto({
            pageQueryDto: paginationQuery,
            itemCount: total,
        });
        return new pagination_response_dto_1.PaginationResponseDto(data, meta);
    }
    async getStatistics() {
        this.logger.debug('Đang lấy thống kê đối tác CMS');
        return this.readCmsPartnersService.getStatistics();
    }
    async count(status, req) {
        this.logger.log(`Counting CMS Partners for user: ${req?.user?.id}`);
        const count = await this.readCmsPartnersService.count(status);
        return { count };
    }
    async search(searchTerm, paginationQuery, req) {
        this.logger.log(`Searching CMS Partners with term: ${searchTerm} for user: ${req.user.id}`);
        const { data, total } = await this.readCmsPartnersService.search(searchTerm, paginationQuery);
        const meta = new custom_pagination_meta_dto_1.CustomPageMetaDto({
            pageQueryDto: paginationQuery,
            itemCount: total,
        });
        return new pagination_response_dto_1.PaginationResponseDto(data, meta);
    }
    async getDeleted(paginationQuery, req) {
        this.logger.log(`Getting deleted CMS Partners for user: ${req.user.id}`);
        const { data, total } = await this.readCmsPartnersService.getDeleted(paginationQuery);
        const meta = new custom_pagination_meta_dto_1.CustomPageMetaDto({
            pageQueryDto: paginationQuery,
            itemCount: total,
        });
        return new pagination_response_dto_1.PaginationResponseDto(data, meta);
    }
    async findById(id, relations) {
        this.logger.debug(`Đang lấy thông tin đối tác CMS với ID: ${id}`);
        const relationsArray = relations ? relations.split(',') : ['creator', 'updater', 'deleter'];
        return this.readCmsPartnersService.getPartnerById(id, relationsArray);
    }
};
exports.ReadCmsPartnersController = ReadCmsPartnersController;
__decorate([
    (0, common_1.Get)(),
    (0, roles_decorator_1.Roles)('ADMIN', 'EDITOR', 'USER'),
    (0, common_1.UsePipes)(new common_1.ValidationPipe({ transform: true, whitelist: true })),
    (0, swagger_1.ApiOperation)({
        summary: 'Lấy danh sách đối tác',
        description: 'Lấy danh sách đối tác với phân trang và sắp xếp',
    }),
    (0, swagger_1.ApiQuery)({
        name: 'page',
        required: false,
        type: Number,
        description: 'Số trang (mặc định: 1)',
        example: 1,
    }),
    (0, swagger_1.ApiQuery)({
        name: 'limit',
        required: false,
        type: Number,
        description: 'Số lượng mỗi trang (mặc định: 10)',
        example: 10,
    }),
    (0, swagger_1.ApiQuery)({
        name: 'sortBy',
        required: false,
        type: String,
        description: 'Trường sắp xếp (mặc định: displayOrder)',
        example: 'displayOrder',
    }),
    (0, swagger_1.ApiQuery)({
        name: 'sortOrder',
        required: false,
        enum: ['ASC', 'DESC'],
        description: 'Thứ tự sắp xếp (mặc định: ASC)',
        example: 'ASC',
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Danh sách đối tác',
        type: pagination_response_dto_1.PaginationResponseDto,
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.UNAUTHORIZED,
        description: 'Không có quyền truy cập',
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.FORBIDDEN,
        description: 'Không có quyền thực hiện thao tác này',
    }),
    openapi.ApiResponse({ status: 200 }),
    __param(0, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [custom_pagination_query_dto_1.CustomPaginationQueryDto]),
    __metadata("design:returntype", Promise)
], ReadCmsPartnersController.prototype, "findAll", null);
__decorate([
    (0, common_1.Get)('by-status/:status'),
    (0, roles_decorator_1.Roles)('ADMIN', 'EDITOR', 'USER'),
    (0, common_1.UsePipes)(new common_1.ValidationPipe({ transform: true, whitelist: true })),
    (0, swagger_1.ApiOperation)({
        summary: 'Lấy đối tác theo trạng thái',
        description: 'Lấy danh sách đối tác theo trạng thái cụ thể',
    }),
    (0, swagger_1.ApiParam)({
        name: 'status',
        enum: dto_1.CmsPartnerStatus,
        description: 'Trạng thái đối tác',
        example: dto_1.CmsPartnerStatus.ACTIVE,
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Danh sách đối tác theo trạng thái',
        type: pagination_response_dto_1.PaginationResponseDto,
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.BAD_REQUEST,
        description: 'Trạng thái không hợp lệ',
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.UNAUTHORIZED,
        description: 'Không có quyền truy cập',
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.FORBIDDEN,
        description: 'Không có quyền thực hiện thao tác này',
    }),
    openapi.ApiResponse({ status: 200 }),
    __param(0, (0, common_1.Param)('status')),
    __param(1, (0, common_1.Query)()),
    __param(2, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, custom_pagination_query_dto_1.CustomPaginationQueryDto, Object]),
    __metadata("design:returntype", Promise)
], ReadCmsPartnersController.prototype, "findByStatus", null);
__decorate([
    (0, common_1.Get)('by-type/:type'),
    (0, roles_decorator_1.Roles)('ADMIN', 'EDITOR', 'USER'),
    (0, common_1.UsePipes)(new common_1.ValidationPipe({ transform: true, whitelist: true })),
    (0, swagger_1.ApiOperation)({
        summary: 'Lấy đối tác theo loại',
        description: 'Lấy danh sách đối tác theo loại cụ thể',
    }),
    (0, swagger_1.ApiParam)({
        name: 'type',
        enum: dto_1.CmsPartnerType,
        description: 'Loại đối tác',
        example: dto_1.CmsPartnerType.PARTNER,
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Danh sách đối tác theo loại',
        type: pagination_response_dto_1.PaginationResponseDto,
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.BAD_REQUEST,
        description: 'Loại đối tác không hợp lệ',
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.UNAUTHORIZED,
        description: 'Không có quyền truy cập',
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.FORBIDDEN,
        description: 'Không có quyền thực hiện thao tác này',
    }),
    openapi.ApiResponse({ status: 200 }),
    __param(0, (0, common_1.Param)('type')),
    __param(1, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, custom_pagination_query_dto_1.CustomPaginationQueryDto]),
    __metadata("design:returntype", Promise)
], ReadCmsPartnersController.prototype, "findByType", null);
__decorate([
    (0, common_1.Get)('active'),
    (0, roles_decorator_1.Roles)('ADMIN', 'EDITOR', 'USER'),
    (0, common_1.UsePipes)(new common_1.ValidationPipe({ transform: true, whitelist: true })),
    (0, swagger_1.ApiOperation)({
        summary: 'Lấy đối tác đang hoạt động',
        description: 'Lấy danh sách đối tác có trạng thái active',
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Danh sách đối tác đang hoạt động',
        type: pagination_response_dto_1.PaginationResponseDto,
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.UNAUTHORIZED,
        description: 'Không có quyền truy cập',
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.FORBIDDEN,
        description: 'Không có quyền thực hiện thao tác này',
    }),
    openapi.ApiResponse({ status: 200 }),
    __param(0, (0, common_1.Query)()),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [custom_pagination_query_dto_1.CustomPaginationQueryDto, Object]),
    __metadata("design:returntype", Promise)
], ReadCmsPartnersController.prototype, "getActivePartners", null);
__decorate([
    (0, common_1.Get)('statistics'),
    (0, roles_decorator_1.Roles)('ADMIN', 'EDITOR', 'USER'),
    (0, swagger_1.ApiOperation)({
        summary: 'Lấy thống kê đối tác',
        description: 'Lấy thống kê tổng quan về đối tác',
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Thống kê đối tác',
        schema: {
            type: 'object',
            properties: {
                total: { type: 'number', description: 'Tổng số đối tác' },
                active: { type: 'number', description: 'Số đối tác đang hoạt động' },
                inactive: { type: 'number', description: 'Số đối tác không hoạt động' },
                byType: {
                    type: 'object',
                    description: 'Thống kê theo loại đối tác',
                    properties: {
                        partner: { type: 'number' },
                        client: { type: 'number' },
                        supplier: { type: 'number' },
                    },
                },
                withLogo: { type: 'number', description: 'Số đối tác có logo' },
                withWebsite: { type: 'number', description: 'Số đối tác có website' },
                withCompleteInfo: { type: 'number', description: 'Số đối tác có đầy đủ thông tin' },
            },
        },
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.UNAUTHORIZED,
        description: 'Không có quyền truy cập',
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.FORBIDDEN,
        description: 'Không có quyền thực hiện thao tác này',
    }),
    openapi.ApiResponse({ status: 200 }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], ReadCmsPartnersController.prototype, "getStatistics", null);
__decorate([
    (0, common_1.Get)('count'),
    (0, roles_decorator_1.Roles)('ADMIN', 'EDITOR', 'USER'),
    (0, swagger_1.ApiOperation)({
        summary: 'Đếm số lượng đối tác',
        description: 'Đếm tổng số lượng đối tác',
    }),
    (0, swagger_1.ApiQuery)({
        name: 'status',
        required: false,
        enum: dto_1.CmsPartnerStatus,
        description: 'Lọc theo trạng thái (tùy chọn)',
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Số lượng đối tác',
        schema: {
            type: 'object',
            properties: {
                count: { type: 'number', description: 'Số lượng đối tác' },
            },
        },
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.UNAUTHORIZED,
        description: 'Không có quyền truy cập',
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.FORBIDDEN,
        description: 'Không có quyền thực hiện thao tác này',
    }),
    openapi.ApiResponse({ status: 200 }),
    __param(0, (0, common_1.Query)('status')),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], ReadCmsPartnersController.prototype, "count", null);
__decorate([
    (0, common_1.Get)('search'),
    (0, roles_decorator_1.Roles)('ADMIN', 'EDITOR', 'USER'),
    (0, common_1.UsePipes)(new common_1.ValidationPipe({ transform: true, whitelist: true })),
    (0, swagger_1.ApiOperation)({
        summary: 'Tìm kiếm đối tác',
        description: 'Tìm kiếm đối tác theo tên hoặc mô tả',
    }),
    (0, swagger_1.ApiQuery)({
        name: 'q',
        required: true,
        type: String,
        description: 'Từ khóa tìm kiếm',
        example: 'công ty',
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Kết quả tìm kiếm đối tác',
        type: pagination_response_dto_1.PaginationResponseDto,
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.BAD_REQUEST,
        description: 'Từ khóa tìm kiếm không hợp lệ',
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.UNAUTHORIZED,
        description: 'Không có quyền truy cập',
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.FORBIDDEN,
        description: 'Không có quyền thực hiện thao tác này',
    }),
    openapi.ApiResponse({ status: 200 }),
    __param(0, (0, common_1.Query)('q')),
    __param(1, (0, common_1.Query)()),
    __param(2, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, custom_pagination_query_dto_1.CustomPaginationQueryDto, Object]),
    __metadata("design:returntype", Promise)
], ReadCmsPartnersController.prototype, "search", null);
__decorate([
    (0, common_1.Get)('deleted'),
    (0, roles_decorator_1.Roles)('ADMIN', 'EDITOR'),
    (0, common_1.UsePipes)(new common_1.ValidationPipe({ transform: true, whitelist: true })),
    (0, swagger_1.ApiOperation)({
        summary: 'Lấy đối tác đã xóa',
        description: 'Lấy danh sách đối tác đã bị xóa mềm',
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Danh sách đối tác đã xóa',
        type: pagination_response_dto_1.PaginationResponseDto,
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.UNAUTHORIZED,
        description: 'Không có quyền truy cập',
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.FORBIDDEN,
        description: 'Không có quyền thực hiện thao tác này',
    }),
    openapi.ApiResponse({ status: 200 }),
    __param(0, (0, common_1.Query)()),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [custom_pagination_query_dto_1.CustomPaginationQueryDto, Object]),
    __metadata("design:returntype", Promise)
], ReadCmsPartnersController.prototype, "getDeleted", null);
__decorate([
    (0, common_1.Get)(':id'),
    (0, roles_decorator_1.Roles)('ADMIN', 'EDITOR', 'USER'),
    (0, swagger_1.ApiOperation)({
        summary: 'Lấy thông tin đối tác',
        description: 'Lấy thông tin chi tiết của một đối tác',
    }),
    (0, swagger_1.ApiParam)({
        name: 'id',
        description: 'ID của đối tác',
        example: '123e4567-e89b-12d3-a456-426614174000',
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Thông tin đối tác',
        type: dto_1.CmsPartnerDto,
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.NOT_FOUND,
        description: 'Không tìm thấy đối tác',
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.UNAUTHORIZED,
        description: 'Không có quyền truy cập',
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.FORBIDDEN,
        description: 'Không có quyền thực hiện thao tác này',
    }),
    openapi.ApiResponse({ status: 200, type: require("../dto/cms-partner.dto").CmsPartnerDto }),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Query)('relations')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String]),
    __metadata("design:returntype", Promise)
], ReadCmsPartnersController.prototype, "findById", null);
exports.ReadCmsPartnersController = ReadCmsPartnersController = ReadCmsPartnersController_1 = __decorate([
    (0, swagger_1.ApiTags)('CMS Partners - Read'),
    (0, common_1.Controller)('cms/partners'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, swagger_1.ApiBearerAuth)(),
    __metadata("design:paramtypes", [read_cms_partners_service_1.ReadCmsPartnersService])
], ReadCmsPartnersController);
//# sourceMappingURL=read.cms-partners.controller.js.map
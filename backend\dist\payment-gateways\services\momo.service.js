"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var MomoService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.MomoService = void 0;
const common_1 = require("@nestjs/common");
const config_1 = require("@nestjs/config");
const axios_1 = require("@nestjs/axios");
const rxjs_1 = require("rxjs");
const crypto = require("crypto");
const random_util_1 = require("../../common/utils/random.util");
let MomoService = MomoService_1 = class MomoService {
    configService;
    httpService;
    logger = new common_1.Logger(MomoService_1.name);
    partnerCode;
    accessKey;
    secretKey;
    endpoint;
    redirectUrl;
    ipnUrl;
    constructor(configService, httpService) {
        this.configService = configService;
        this.httpService = httpService;
        this.partnerCode = this.configService.get('MOMO_PARTNER_CODE') || '';
        this.accessKey = this.configService.get('MOMO_ACCESS_KEY') || '';
        this.secretKey = this.configService.get('MOMO_SECRET_KEY') || '';
        this.endpoint = this.configService.get('MOMO_ENDPOINT') || '';
        this.redirectUrl = this.configService.get('MOMO_REDIRECT_URL') || '';
        this.ipnUrl = this.configService.get('MOMO_IPN_URL') || '';
    }
    async createPaymentUrl(paymentData) {
        try {
            const orderId = (0, random_util_1.generateTransactionId)('MOMO', 4);
            const requestId = (0, random_util_1.generateTransactionId)('REQ', 4);
            const transactionId = paymentData.userId + '_' + orderId;
            const orderInfo = paymentData.description || `Thanh toán cho đơn hàng ${orderId}`;
            const amount = paymentData.amount.toString();
            const extraData = Buffer.from(JSON.stringify({
                userId: paymentData.userId,
                walletId: paymentData.walletId,
            })).toString('base64');
            const rawSignature = `accessKey=${this.accessKey}&amount=${amount}&extraData=${extraData}&ipnUrl=${this.ipnUrl}&orderId=${orderId}&orderInfo=${orderInfo}&partnerCode=${this.partnerCode}&redirectUrl=${this.redirectUrl}&requestId=${requestId}&requestType=captureWallet`;
            const signature = crypto
                .createHmac('sha256', this.secretKey)
                .update(rawSignature)
                .digest('hex');
            const requestBody = {
                partnerCode: this.partnerCode,
                accessKey: this.accessKey,
                requestId: requestId,
                amount: amount,
                orderId: orderId,
                orderInfo: orderInfo,
                redirectUrl: this.redirectUrl,
                ipnUrl: this.ipnUrl,
                extraData: extraData,
                requestType: 'captureWallet',
                signature: signature,
                lang: 'vi',
            };
            try {
                const { data } = await (0, rxjs_1.firstValueFrom)(this.httpService.post(this.endpoint, requestBody));
                if (data.resultCode === 0) {
                    const paymentUrl = data.payUrl;
                    this.logger.log(`Đã tạo URL thanh toán MOMO: ${paymentUrl}`);
                    return { paymentUrl, transactionId };
                }
                else {
                    throw new Error(`MOMO trả về lỗi: ${data.message}`);
                }
            }
            catch (error) {
                this.logger.error(`Lỗi khi gửi yêu cầu đến MOMO: ${error.message}`, error.stack);
                throw error;
            }
        }
        catch (error) {
            this.logger.error(`Lỗi khi tạo URL thanh toán MOMO: ${error.message}`, error.stack);
            throw error;
        }
    }
    async verifyReturnUrl(params) {
        try {
            const { orderId, requestId, amount, orderInfo, orderType, transId, resultCode, message, payType, responseTime, extraData, signature } = params;
            const rawSignature = `accessKey=${this.accessKey}&amount=${amount}&extraData=${extraData}&message=${message}&orderId=${orderId}&orderInfo=${orderInfo}&orderType=${orderType}&payType=${payType}&requestId=${requestId}&responseTime=${responseTime}&resultCode=${resultCode}&transId=${transId}`;
            const checkSignature = crypto
                .createHmac('sha256', this.secretKey)
                .update(rawSignature)
                .digest('hex');
            const isValidSignature = signature === checkSignature;
            const isSuccessful = resultCode === '0';
            if (isValidSignature && isSuccessful) {
                this.logger.log(`Xác thực thanh toán MOMO thành công cho đơn hàng: ${orderId}, số tiền: ${amount}`);
                return {
                    isValid: true,
                    transactionId: orderId,
                    amount: parseInt(amount),
                    message: 'Thanh toán thành công',
                };
            }
            else if (!isValidSignature) {
                this.logger.warn(`Chữ ký không hợp lệ cho đơn hàng: ${orderId}`);
                return {
                    isValid: false,
                    message: 'Chữ ký không hợp lệ',
                };
            }
            else {
                this.logger.warn(`Thanh toán không thành công cho đơn hàng: ${orderId}, mã lỗi: ${resultCode}`);
                return {
                    isValid: false,
                    transactionId: orderId,
                    message: `Thanh toán không thành công: ${message}`,
                };
            }
        }
        catch (error) {
            this.logger.error(`Lỗi khi xác thực callback MOMO: ${error.message}`, error.stack);
            return {
                isValid: false,
                message: `Lỗi xử lý: ${error.message}`,
            };
        }
    }
    async handleIpnCallback(params) {
        return this.verifyReturnUrl(params);
    }
};
exports.MomoService = MomoService;
exports.MomoService = MomoService = MomoService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [config_1.ConfigService,
        axios_1.HttpService])
], MomoService);
//# sourceMappingURL=momo.service.js.map
"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SilverPrice = void 0;
const openapi = require("@nestjs/swagger");
const swagger_1 = require("@nestjs/swagger");
const typeorm_1 = require("typeorm");
let SilverPrice = class SilverPrice {
    id;
    symbol;
    price;
    bid;
    ask;
    high;
    low;
    change;
    changePercent;
    timestamp;
    source;
    static _OPENAPI_METADATA_FACTORY() {
        return { id: { required: true, type: () => Number }, symbol: { required: true, type: () => String }, price: { required: true, type: () => Number }, bid: { required: true, type: () => Number }, ask: { required: true, type: () => Number }, high: { required: true, type: () => Number }, low: { required: true, type: () => Number }, change: { required: true, type: () => Number }, changePercent: { required: true, type: () => Number }, timestamp: { required: true, type: () => Number }, source: { required: true, type: () => String } };
    }
};
exports.SilverPrice = SilverPrice;
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'ID duy nhất của bản ghi giá bạc' }),
    (0, typeorm_1.PrimaryGeneratedColumn)({ type: 'int', unsigned: true }),
    __metadata("design:type", Number)
], SilverPrice.prototype, "id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Mã cặp tiền tệ', example: 'XAGUSD' }),
    (0, typeorm_1.Column)({ name: 'symbol', default: 'XAGUSD' }),
    __metadata("design:type", String)
], SilverPrice.prototype, "symbol", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Giá hiện tại', example: 28.5432 }),
    (0, typeorm_1.Column)({ name: 'price', type: 'decimal', precision: 10, scale: 4 }),
    __metadata("design:type", Number)
], SilverPrice.prototype, "price", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Giá mua (bid)', example: 28.5400, required: false }),
    (0, typeorm_1.Column)({ name: 'bid', type: 'decimal', precision: 10, scale: 4, nullable: true }),
    __metadata("design:type", Number)
], SilverPrice.prototype, "bid", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Giá bán (ask)', example: 28.5500, required: false }),
    (0, typeorm_1.Column)({ name: 'ask', type: 'decimal', precision: 10, scale: 4, nullable: true }),
    __metadata("design:type", Number)
], SilverPrice.prototype, "ask", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Giá cao nhất trong ngày', example: 28.7500, required: false }),
    (0, typeorm_1.Column)({ name: 'high', type: 'decimal', precision: 10, scale: 4, nullable: true }),
    __metadata("design:type", Number)
], SilverPrice.prototype, "high", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Giá thấp nhất trong ngày', example: 28.2100, required: false }),
    (0, typeorm_1.Column)({ name: 'low', type: 'decimal', precision: 10, scale: 4, nullable: true }),
    __metadata("design:type", Number)
], SilverPrice.prototype, "low", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Thay đổi giá', example: 0.2500, required: false }),
    (0, typeorm_1.Column)({ name: 'change', type: 'decimal', precision: 10, scale: 4, nullable: true }),
    __metadata("design:type", Number)
], SilverPrice.prototype, "change", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Phần trăm thay đổi giá', example: 0.88, required: false }),
    (0, typeorm_1.Column)({ name: 'change_percent', type: 'decimal', precision: 10, scale: 4, nullable: true }),
    __metadata("design:type", Number)
], SilverPrice.prototype, "changePercent", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Thời gian cập nhật giá (timestamp)', example: 1683123456789 }),
    (0, typeorm_1.Column)({ name: 'timestamp', type: 'bigint' }),
    (0, typeorm_1.Index)(),
    __metadata("design:type", Number)
], SilverPrice.prototype, "timestamp", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Nguồn dữ liệu', example: 'TradingView' }),
    (0, typeorm_1.Column)({ name: 'source', default: 'TradingView' }),
    __metadata("design:type", String)
], SilverPrice.prototype, "source", void 0);
exports.SilverPrice = SilverPrice = __decorate([
    (0, typeorm_1.Entity)('silver_prices')
], SilverPrice);
//# sourceMappingURL=silver-price.entity.js.map
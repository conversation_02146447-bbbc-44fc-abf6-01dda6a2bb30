import { CreateCmsPostsService } from '../services/create.cms-posts.service';
import { CmsPostDto } from '../dto/cms-post.dto';
import { CreateCmsPostDto } from '../dto/create.cms-post.dto';
export declare class CreateCmsPostsController {
    private readonly cmsPostsService;
    constructor(cmsPostsService: CreateCmsPostsService);
    create(createCmsPostDto: CreateCmsPostDto, userId: string): Promise<CmsPostDto>;
    bulkCreate(createCmsPostDtos: CreateCmsPostDto[], userId: string): Promise<CmsPostDto[]>;
    duplicate(id: string, userId: string): Promise<CmsPostDto>;
    createFromTemplate(templateId: string, createCmsPostDto: CreateCmsPostDto, userId: string): Promise<CmsPostDto>;
}

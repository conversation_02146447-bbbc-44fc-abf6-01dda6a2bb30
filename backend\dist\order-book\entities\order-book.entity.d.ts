import { User } from '../../users/entities/user.entity';
import { OrderType } from '../enums/order-type.enum';
import { OrderStatus } from '../enums/order-status.enum';
import { BusinessType } from '../enums/business-type.enum';
import { ApproveStatus } from '../enums/approve-status.enum';
import { OrderBookDetail } from './order-book-detail.entity';
import { BaseEntity } from '../../common/entities/base.entity';
export declare class OrderBook extends BaseEntity {
    userId: string;
    orderType: OrderType;
    status: OrderStatus;
    businessType: BusinessType;
    totalPrice: number;
    processingPrice: number;
    depositPrice: number;
    storageFee: number;
    settlementPrice: number;
    totalPriceFinal: number;
    contractNumber: string;
    settlementDeadline: Date;
    settlementAt: Date;
    approveStatus: ApproveStatus;
    approvedAt: Date;
    user: User;
    getEntityName(): string;
    details: OrderBookDetail[];
}

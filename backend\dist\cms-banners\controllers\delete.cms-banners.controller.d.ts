import { DeleteCmsBannersService } from '../services/delete.cms-banners.service';
import { CmsBannerDto } from '../dto/cms-banner.dto';
export declare class DeleteCmsBannersController {
    private readonly cmsBannersService;
    constructor(cmsBannersService: DeleteCmsBannersService);
    softDelete(id: string, userId: string): Promise<CmsBannerDto | null>;
    restore(id: string, userId: string): Promise<CmsBannerDto | null>;
    remove(id: string): Promise<{
        affected: number;
    }>;
    bulkSoftDelete(ids: string[], userId: string): Promise<CmsBannerDto[]>;
    bulkRestore(ids: string[], userId: string): Promise<CmsBannerDto[]>;
    bulkRemove(ids: string[]): Promise<{
        affected: number;
    }>;
}

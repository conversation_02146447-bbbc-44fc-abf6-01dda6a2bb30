import { Permission } from '../../permissions/entities/permission.entity';
import { User } from '../../users/entities/user.entity';
import { Role } from './role.entity';
export declare class RolePermission {
    roleId: string;
    permissionId: string;
    createdAt: Date;
    updatedAt: Date;
    deletedAt: Date;
    isDeleted: boolean;
    createdBy: string | null;
    updatedBy: string | null;
    deletedBy: string | null;
    role: Role;
    permission: Permission;
    creator: User;
    updater: User;
    deleter: User;
}

/* <PERSON><PERSON> cục bảng thống nhất với Header c<PERSON> định */

/* Container cuộn bảng - wrapper chính */
.table-scroll-container {
  position: relative;
  height: 100%; /* S<PERSON> dụng chiều cao 100% thay vì tính toán cố định */
  min-height: 200px;
  overflow: auto; /* Cho phép cuộn cả ngang và dọc */
  scrollbar-width: thin;
  scrollbar-color: var(--border) var(--muted);
}

/* Viewport bảng - đảm bảo ngữ cảnh định vị phù hợp */
.table-viewport {
  position: relative;
  width: 100%;
  height: 100%;
}

/* Định dạng bảng dữ liệu */
.data-table {
  width: 100%;
  table-layout: fixed;
  border-collapse: collapse;
  min-width: max-content; /* Đảm bảo cuộn ngang hoạt động */
}

/* Kiểu dáng cho header cố định */
.sticky-header {
  position: sticky !important;
  top: 0 !important;
  z-index: 50 !important;
  background-color: var(--background) !important;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1) !important;
  height: auto !important;
}

/* <PERSON><PERSON><PERSON> bảo tất cả các phần tử th trong header cố định có màu nền */
.sticky-header th {
  background-color: var(--background) !important;
  height: 36px !important; /* Chiều cao đã giảm */
  padding-top: 8px !important;
  padding-bottom: 8px !important;
  vertical-align: middle !important;
}

/* Kiểu dáng cho các cột cố định */
.sticky-right {
  position: sticky !important;
  right: 0 !important;
  z-index: 40 !important;
  background-color: var(--background) !important;
  box-shadow: -5px 0 5px -5px rgba(0,0,0,0.1) !important;
}

.sticky-left {
  position: sticky !important;
  left: 0 !important;
  z-index: 40 !important;
  background-color: var(--background) !important;
  box-shadow: 5px 0 5px -5px rgba(0,0,0,0.1) !important;
}

/* Trường hợp đặc biệt cho các ô header cũng là cố định */
.sticky-header th.sticky-right {
  z-index: 60 !important;
}

.sticky-header th.sticky-left {
  z-index: 60 !important;
}

/* Định dạng cột checkbox - dựa trên class thay vì :first-child */
.checkbox-header,
.checkbox-cell {
  position: sticky !important;
  left: 0 !important;
  z-index: 40 !important;
  background-color: var(--background) !important;
  box-shadow: 5px 0 5px -5px rgba(0,0,0,0.1) !important;
  width: 40px !important;
  min-width: 40px !important;
  max-width: 40px !important;
  padding: 0 !important;
  text-align: center !important;
}

/* Đảm bảo checkbox trong header có z-index cao hơn */
.checkbox-header {
  z-index: 60 !important;
}

/* Đảm bảo các checkbox được căn giữa */
.checkbox-header > div,
.checkbox-cell > div {
  display: flex !important;
  justify-content: center !important;
  align-items: center !important;
  height: 100% !important;
  width: 100% !important;
}

/* Override :first-child styles khi không phải checkbox column */
/* Chỉ áp dụng sticky styles cho columns có meta.isSticky = true */
th:first-child:not(.checkbox-header),
td:first-child:not(.checkbox-cell) {
  position: static !important;
  left: auto !important;
  z-index: auto !important;
  background-color: transparent !important;
  box-shadow: none !important;
  width: auto !important;
  min-width: auto !important;
  max-width: none !important;
  padding: 8px 16px !important;
  text-align: left !important;
}

/* Đảm bảo first-child không phải checkbox có styling bình thường */
th:first-child:not(.checkbox-header) > div,
td:first-child:not(.checkbox-cell) > div {
  display: block !important;
  justify-content: flex-start !important;
  align-items: flex-start !important;
  height: auto !important;
  width: auto !important;
}

/* Override CSS selector [&:has([role=checkbox])] cho non-checkbox columns */
/* Chỉ checkbox cells mới có padding-right: 0 */
td:not(.checkbox-cell) {
  padding-right: 8px !important;
}

/* Đảm bảo checkbox cells có padding đúng */
.checkbox-cell {
  padding-right: 0 !important;
}

/* Kiểu dáng cho ô hành động */
.sticky-action-cell {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  height: 100%;
  transition: background-color 0.2s ease;
  padding: 0 !important;
  width: auto !important;
}

/* Kiểu dáng cụ thể cho cột Actions */
[id^="actions"] {
  position: sticky !important;
  right: 0 !important;
  z-index: 40 !important;
  background-color: var(--background) !important;
  width: auto !important;
  min-width: 80px !important;
  max-width: 100px !important;
}

/* Thêm chỉ báo trực quan cho các cột cố định */
.sticky-right::after {
  content: '';
  position: absolute;
  top: 0;
  bottom: 0;
  left: -8px;
  width: 8px;
  background: linear-gradient(to right, transparent, rgba(0,0,0,0.05));
  pointer-events: none;
}

/* Định dạng thanh cuộn cho trình duyệt WebKit */
.table-scroll-container::-webkit-scrollbar {
  height: 10px;
  width: 10px;
  background-color: var(--muted);
}

.table-scroll-container::-webkit-scrollbar-thumb {
  background-color: var(--border);
  border-radius: 5px;
}

.table-scroll-container::-webkit-scrollbar-thumb:hover {
  background-color: var(--muted-foreground);
}

/* Đảm bảo thanh cuộn luôn hiển thị trong Firefox */
@supports (scrollbar-width: thin) {
  .table-scroll-container {
    scrollbar-width: thin !important;
  }
}

/* Đảm bảo container bảng sử dụng hết không gian có sẵn */
.rounded-md.border.flex.flex-col.flex-1.overflow-hidden {
  display: flex;
  flex-direction: column;
  height: 100%;
  min-height: 0; /* Quan trọng để container flex tôn trọng chiều cao của phần tử con */
}

/* Khắc phục khoảng cách giữa bảng và phân trang */
.w-full.flex.flex-col.h-full {
  display: flex;
  flex-direction: column;
  height: 100%;
  min-height: 0; /* Quan trọng cho container flex */
}

/* Đảm bảo footer bảng gắn liền với phần dưới không có khoảng trống */
.sticky.bottom-0.bg-background.border-t.mt-auto {
  margin-top: 0 !important; /* Loại bỏ margin có thể tạo ra khoảng trống */
}

/* Tối ưu chiều cao hàng để hiển thị dữ liệu tốt hơn */
.data-table tbody tr {
  height: 32px !important;
}

/* Cải thiện hiệu ứng hover để tăng trải nghiệm người dùng */
.data-table tbody tr:hover {
  background-color: var(--muted) !important;
}

/* Cải thiện hiển thị trạng thái được chọn */
.data-table tbody tr[data-state="selected"] {
  background-color: var(--accent) !important;
}

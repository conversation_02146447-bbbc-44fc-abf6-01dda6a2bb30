import { Logger } from '@nestjs/common';
import { Repository } from 'typeorm';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { BaseOrderBookService } from './base.order-book.service';
import { OrderBook } from '../entities/order-book.entity';
import { OrderBookDto } from '../dto/order-book.dto';
import { PaginationQueryDto } from '../../common/dto/pagination-query.dto';
import { PaginationResponseDto } from '../../common/dto/pagination-response.dto';
export declare class ReadOrderBookService extends BaseOrderBookService {
    protected readonly orderBookRepository: Repository<OrderBook>;
    protected readonly eventEmitter: EventEmitter2;
    protected readonly logger: Logger;
    constructor(orderBookRepository: Repository<OrderBook>, eventEmitter: EventEmitter2);
    findAll(params: PaginationQueryDto & {
        relations?: string[];
        filter?: string;
    }): Promise<PaginationResponseDto<OrderBookDto>>;
    search(keyword: string, params: PaginationQueryDto): Promise<PaginationResponseDto<OrderBookDto>>;
    count(filter?: string): Promise<number>;
    findOne(id: string, relations?: string[]): Promise<OrderBookDto>;
    findDeleted(params: PaginationQueryDto): Promise<PaginationResponseDto<OrderBookDto>>;
    getStatistics(filter?: string): Promise<{
        total: number;
        businessTypeCounts: {
            NORMAL: number;
            IMMEDIATE_DELIVERY: number;
        };
    }>;
    export(): Promise<any>;
}

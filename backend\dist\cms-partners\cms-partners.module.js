"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CmsPartnersModule = void 0;
const common_1 = require("@nestjs/common");
const event_emitter_1 = require("@nestjs/event-emitter");
const typeorm_1 = require("@nestjs/typeorm");
const cms_partners_entity_1 = require("./entity/cms-partners.entity");
const base_cms_partners_service_1 = require("./services/base.cms-partners.service");
const create_cms_partners_service_1 = require("./services/create.cms-partners.service");
const delete_cms_partners_service_1 = require("./services/delete.cms-partners.service");
const read_cms_partners_service_1 = require("./services/read.cms-partners.service");
const update_cms_partners_service_1 = require("./services/update.cms-partners.service");
const create_cms_partners_controller_1 = require("./controllers/create.cms-partners.controller");
const delete_cms_partners_controller_1 = require("./controllers/delete.cms-partners.controller");
const read_cms_partners_controller_1 = require("./controllers/read.cms-partners.controller");
const read_cms_partners_public_controller_1 = require("./controllers/read.cms-partners.public.controller");
const update_cms_partners_controller_1 = require("./controllers/update.cms-partners.controller");
let CmsPartnersModule = class CmsPartnersModule {
};
exports.CmsPartnersModule = CmsPartnersModule;
exports.CmsPartnersModule = CmsPartnersModule = __decorate([
    (0, common_1.Module)({
        imports: [
            typeorm_1.TypeOrmModule.forFeature([cms_partners_entity_1.CmsPartners]),
            event_emitter_1.EventEmitterModule,
        ],
        controllers: [
            create_cms_partners_controller_1.CreateCmsPartnersController,
            read_cms_partners_controller_1.ReadCmsPartnersController,
            update_cms_partners_controller_1.UpdateCmsPartnersController,
            delete_cms_partners_controller_1.DeleteCmsPartnersController,
            read_cms_partners_public_controller_1.ReadCmsPartnersPublicController,
        ],
        providers: [
            base_cms_partners_service_1.BaseCmsPartnersService,
            create_cms_partners_service_1.CreateCmsPartnersService,
            read_cms_partners_service_1.ReadCmsPartnersService,
            update_cms_partners_service_1.UpdateCmsPartnersService,
            delete_cms_partners_service_1.DeleteCmsPartnersService,
        ],
        exports: [
            base_cms_partners_service_1.BaseCmsPartnersService,
            create_cms_partners_service_1.CreateCmsPartnersService,
            read_cms_partners_service_1.ReadCmsPartnersService,
            update_cms_partners_service_1.UpdateCmsPartnersService,
            delete_cms_partners_service_1.DeleteCmsPartnersService,
            typeorm_1.TypeOrmModule,
        ],
    })
], CmsPartnersModule);
//# sourceMappingURL=cms-partners.module.js.map
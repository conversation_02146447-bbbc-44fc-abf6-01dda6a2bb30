import { DeleteEcomProductCategoriesService } from '../services/delete.ecom-product-categories.service';
import { EcomProductCategoryDto } from '../dto/ecom-product-category.dto';
export declare class DeleteEcomProductCategoriesController {
    private readonly ecomProductCategoriesService;
    constructor(ecomProductCategoriesService: DeleteEcomProductCategoriesService);
    softDelete(id: string, userId: string): Promise<EcomProductCategoryDto | null>;
    restore(id: string, userId: string): Promise<EcomProductCategoryDto | null>;
    remove(id: string): Promise<{
        affected: number;
    }>;
}

"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var ReadSystemConfigService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.ReadSystemConfigService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const event_emitter_1 = require("@nestjs/event-emitter");
const typeorm_transactional_1 = require("typeorm-transactional");
const system_config_entity_1 = require("../entities/system-config.entity");
const base_system_config_service_1 = require("./base.system-config.service");
let ReadSystemConfigService = ReadSystemConfigService_1 = class ReadSystemConfigService extends base_system_config_service_1.BaseSystemConfigService {
    systemConfigRepository;
    dataSource;
    eventEmitter;
    logger = new common_1.Logger(ReadSystemConfigService_1.name);
    constructor(systemConfigRepository, dataSource, eventEmitter) {
        super(systemConfigRepository, dataSource, eventEmitter);
        this.systemConfigRepository = systemConfigRepository;
        this.dataSource = dataSource;
        this.eventEmitter = eventEmitter;
    }
    async findAll(paginationQuery) {
        this.logger.debug(`Lấy danh sách cấu hình với tham số: ${JSON.stringify(paginationQuery)}`);
        const { page = 1, limit = 100, sort, filter, search, relations } = paginationQuery;
        const skip = (page - 1) * limit;
        const query = this.systemConfigRepository.createQueryBuilder('systemConfig')
            .where('systemConfig.isDeleted = :isDeleted', { isDeleted: false });
        if (search) {
            query.andWhere(new (require('typeorm')).Brackets(qb => {
                qb.where('LOWER(systemConfig.configKey) LIKE LOWER(:search)', { search: `%${search}%` })
                    .orWhere('LOWER(systemConfig.configValue) LIKE LOWER(:search)', { search: `%${search}%` })
                    .orWhere('LOWER(systemConfig.description) LIKE LOWER(:search)', { search: `%${search}%` })
                    .orWhere('LOWER(systemConfig.configGroup) LIKE LOWER(:search)', { search: `%${search}%` })
                    .orWhere('LOWER(systemConfig.groupDisplayName) LIKE LOWER(:search)', { search: `%${search}%` });
            }));
        }
        if (filter) {
            const filters = filter.split(',');
            filters.forEach(f => {
                const [field, value] = f.split(':');
                if (field && value) {
                    if (value.includes('|')) {
                        const values = value.split('|');
                        query.andWhere(`systemConfig.${field} IN (:...${field})`, { [field]: values });
                    }
                    else {
                        query.andWhere(`systemConfig.${field} = :${field}`, { [field]: value });
                    }
                }
            });
        }
        const validatedRelations = this.validateRelations(relations ? relations.split(',') : []);
        validatedRelations.forEach(relation => {
            query.leftJoinAndSelect(`systemConfig.${relation}`, relation);
        });
        if (sort) {
            const sortParts = sort.split(',');
            sortParts.forEach(part => {
                const [field, order] = part.split(':');
                query.addOrderBy(`systemConfig.${field.trim()}`, (order && order.trim().toUpperCase() === 'DESC') ? 'DESC' : 'ASC');
            });
        }
        else {
            query.orderBy('systemConfig.sectionOrder', 'ASC')
                .addOrderBy('systemConfig.displayOrder', 'ASC')
                .addOrderBy('systemConfig.createdAt', 'DESC');
        }
        query.skip(skip).take(limit);
        const [entities, total] = await query.getManyAndCount();
        return {
            data: entities.map(e => this.toDto(e)),
            total,
        };
    }
    async search(keyword, paginationQuery) {
        this.logger.debug(`Tìm kiếm cấu hình với từ khóa: ${keyword}`);
        const { page = 1, limit = 10, sort, filter, relations } = paginationQuery;
        const skip = (page - 1) * limit;
        const order = {};
        if (sort) {
            const [field, direction] = sort.split(':');
            order[field] = direction.toUpperCase();
        }
        else {
            order.sectionOrder = 'ASC';
            order.displayOrder = 'ASC';
            order.createdAt = 'DESC';
        }
        const where = [
            { configKey: (0, typeorm_2.ILike)(`%${keyword}%`), isDeleted: false },
            { configValue: (0, typeorm_2.ILike)(`%${keyword}%`), isDeleted: false },
            { description: (0, typeorm_2.ILike)(`%${keyword}%`), isDeleted: false },
            { configGroup: (0, typeorm_2.ILike)(`%${keyword}%`), isDeleted: false },
            { groupDisplayName: (0, typeorm_2.ILike)(`%${keyword}%`), isDeleted: false },
        ];
        if (filter) {
            const filters = filter.split(',');
            filters.forEach(f => {
                const [field, value] = f.split(':');
                where.forEach(w => {
                    w[field] = value;
                });
            });
        }
        const validatedRelations = this.validateRelations(relations ? relations.split(',') : []);
        const [configs, total] = await this.systemConfigRepository.findAndCount({
            where,
            order,
            skip,
            take: limit,
            relations: validatedRelations,
        });
        return {
            data: configs.map(config => this.toDto(config)),
            total,
        };
    }
    async findById(id, relations = []) {
        this.logger.debug(`Lấy thông tin cấu hình với ID: ${id}`);
        const config = await this.findByIdOrFail(id, relations);
        return this.toDto(config);
    }
    async findByKey(key, relations = []) {
        this.logger.debug(`Lấy thông tin cấu hình với khóa: ${key}`);
        const config = await this.findByKeyOrFail(key, relations);
        return this.toDto(config);
    }
    async findByGroup(group, paginationQuery) {
        this.logger.debug(`Lấy danh sách cấu hình theo nhóm: ${group}`);
        const { page = 1, limit = 10, sort, relations } = paginationQuery;
        const skip = (page - 1) * limit;
        const order = {};
        if (sort) {
            const [field, direction] = sort.split(':');
            order[field] = direction.toUpperCase();
        }
        else {
            order.sectionOrder = 'ASC';
            order.displayOrder = 'ASC';
            order.createdAt = 'DESC';
        }
        const validatedRelations = this.validateRelations(relations ? relations.split(',') : []);
        const [configs, total] = await this.systemConfigRepository.findAndCount({
            where: { configGroup: group, isDeleted: false },
            order,
            skip,
            take: limit,
            relations: validatedRelations,
        });
        return {
            data: configs.map(config => this.toDto(config)),
            total,
        };
    }
    async findAllGroups() {
        try {
            this.logger.debug('Lấy danh sách tất cả các nhóm cấu hình');
            const groupConfigs = await this.systemConfigRepository.find({
                where: { isGroupConfig: true, isDeleted: false },
                order: { groupOrder: 'ASC' },
            });
            if (groupConfigs.length === 0) {
                return await this.createDefaultGroupConfigs();
            }
            return groupConfigs.map(config => this.toDto(config));
        }
        catch (error) {
            this.logger.error(`Lỗi khi lấy danh sách nhóm cấu hình: ${error.message}`, error.stack);
            throw new common_1.InternalServerErrorException(`Không thể lấy danh sách nhóm cấu hình: ${error.message}`);
        }
    }
    async createDefaultGroupConfigs() {
        try {
            const groups = await this.systemConfigRepository
                .createQueryBuilder('systemConfig')
                .select('DISTINCT systemConfig.configGroup', 'group')
                .where({ isDeleted: false })
                .getRawMany();
            const defaultGroupConfigs = [];
            for (const [index, item] of groups.entries()) {
                const group = item.group;
                if (!group)
                    continue;
                const groupConfig = this.systemConfigRepository.create({
                    configKey: `GROUP_${group.toUpperCase()}`,
                    configValue: group,
                    configGroup: group,
                    description: `Group configuration for ${group}`,
                    groupDisplayName: this.getDefaultGroupDisplayName(group),
                    groupDescription: this.getDefaultGroupDescription(group),
                    groupIcon: this.getDefaultGroupIcon(group),
                    groupOrder: index + 1,
                    isGroupConfig: true,
                    configType: 'text',
                });
                defaultGroupConfigs.push(groupConfig);
            }
            if (defaultGroupConfigs.length > 0) {
                const savedConfigs = await this.systemConfigRepository.save(defaultGroupConfigs);
                return savedConfigs.map(config => this.toDto(config));
            }
            return [];
        }
        catch (error) {
            this.logger.error(`Lỗi khi tạo cấu hình nhóm mặc định: ${error.message}`, error.stack);
            throw new common_1.InternalServerErrorException(`Không thể tạo cấu hình nhóm mặc định: ${error.message}`);
        }
    }
    async findDeleted(paginationQuery) {
        this.logger.debug(`Lấy danh sách cấu hình đã xóa mềm với tham số: ${JSON.stringify(paginationQuery)}`);
        const { page = 1, limit = 10, sort, filter, relations } = paginationQuery;
        const skip = (page - 1) * limit;
        const order = {};
        if (sort) {
            const [field, direction] = sort.split(':');
            order[field] = direction.toUpperCase();
        }
        else {
            order.createdAt = 'DESC';
        }
        const where = { isDeleted: true };
        if (filter) {
            const filters = filter.split(',');
            filters.forEach(f => {
                const [field, value] = f.split(':');
                where[field] = value;
            });
        }
        const validatedRelations = this.validateRelations(relations ? relations.split(',') : []);
        const [configs, total] = await this.systemConfigRepository.findAndCount({
            where,
            order,
            skip,
            take: limit,
            relations: validatedRelations,
            withDeleted: true,
        });
        return {
            data: configs.map(config => this.toDto(config)),
            total,
        };
    }
    getDefaultGroupDisplayName(group) {
        const displayNames = {
            'general': 'Cấu hình chung',
            'contact': 'Thông tin liên hệ',
            'payment': 'Cấu hình thanh toán',
            'security': 'Cấu hình bảo mật',
            'social': 'Mạng xã hội',
            'trading': 'Cấu hình giao dịch',
            'finance': 'Cấu hình tài chính',
            'notification': 'Cấu hình thông báo',
            'products': 'Cấu hình sản phẩm',
            'users': 'Cấu hình người dùng',
            'agents': 'Cấu hình đại lý',
            'reports': 'Cấu hình báo cáo',
            'integration': 'Cấu hình tích hợp',
            'payment_gateway': 'Cổng thanh toán',
            'email_template': 'Mẫu email',
        };
        return displayNames[group] || `Cấu hình ${group}`;
    }
    getDefaultGroupDescription(group) {
        const descriptions = {
            'general': 'Cấu hình chung của hệ thống',
            'contact': 'Thông tin liên hệ của hệ thống',
            'payment': 'Cấu hình thanh toán của hệ thống',
            'security': 'Cấu hình bảo mật của hệ thống',
            'social': 'Liên kết mạng xã hội',
            'trading': 'Quy định và phí giao dịch',
            'finance': 'Cấu hình tài chính và thanh toán',
            'notification': 'Cấu hình email và thông báo',
            'products': 'Cấu hình sản phẩm vàng bạc',
            'users': 'Cấu hình tài khoản người dùng',
            'agents': 'Cấu hình hệ thống đại lý',
            'reports': 'Cấu hình báo cáo và thống kê',
            'integration': 'Tích hợp với dịch vụ bên thứ ba',
            'payment_gateway': 'Cấu hình cổng thanh toán VNPAY, MOMO',
            'email_template': 'Biểu mẫu email thông báo cho người dùng',
        };
        return descriptions[group] || `Cấu hình ${group}`;
    }
    getDefaultGroupIcon(group) {
        const icons = {
            'general': 'settings',
            'contact': 'mail',
            'payment': 'credit-card',
            'security': 'shield-check',
            'social': 'share',
            'trading': 'line-chart',
            'finance': 'wallet',
            'notification': 'bell',
            'products': 'package',
            'users': 'users',
            'agents': 'network',
            'reports': 'bar-chart',
            'integration': 'link',
            'payment_gateway': 'credit-card',
            'email_template': 'mail',
        };
        return icons[group] || 'settings';
    }
};
exports.ReadSystemConfigService = ReadSystemConfigService;
__decorate([
    (0, typeorm_transactional_1.Transactional)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], ReadSystemConfigService.prototype, "createDefaultGroupConfigs", null);
exports.ReadSystemConfigService = ReadSystemConfigService = ReadSystemConfigService_1 = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(system_config_entity_1.SystemConfig)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        typeorm_2.DataSource,
        event_emitter_1.EventEmitter2])
], ReadSystemConfigService);
//# sourceMappingURL=read.system-config.service.js.map
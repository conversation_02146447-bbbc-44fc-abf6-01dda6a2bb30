import { Repository, DataSource } from 'typeorm';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { BaseEcomProductCategoriesService } from './base.ecom-product-categories.service';
import { EcomProductCategories } from '../entity/ecom-product-categories.entity';
import { EcomProductCategoryDto } from '../dto/ecom-product-category.dto';
export declare class DeleteEcomProductCategoriesService extends BaseEcomProductCategoriesService {
    protected readonly categoryRepository: Repository<EcomProductCategories>;
    protected readonly dataSource: DataSource;
    protected readonly eventEmitter: EventEmitter2;
    constructor(categoryRepository: Repository<EcomProductCategories>, dataSource: DataSource, eventEmitter: EventEmitter2);
    softDelete(id: string, userId: string): Promise<EcomProductCategoryDto | null>;
    restore(id: string, userId: string): Promise<EcomProductCategoryDto | null>;
    remove(id: string): Promise<{
        affected: number | null;
    }>;
}

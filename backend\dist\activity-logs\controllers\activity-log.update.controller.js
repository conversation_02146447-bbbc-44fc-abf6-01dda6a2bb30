"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var ActivityLogUpdateController_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.ActivityLogUpdateController = void 0;
const openapi = require("@nestjs/swagger");
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const jwt_auth_guard_1 = require("../../common/guards/jwt-auth.guard");
const roles_guard_1 = require("../../common/guards/roles.guard");
const roles_decorator_1 = require("../../common/decorators/roles.decorator");
const get_user_decorator_1 = require("../../common/decorators/get-user.decorator");
const default_user_role_enum_1 = require("../../users/enums/default-user-role.enum");
const update_activity_log_service_1 = require("../services/update.activity-log.service");
const activity_log_dto_1 = require("../dto/activity-log.dto");
const update_activity_log_dto_1 = require("../dto/update-activity-log.dto");
let ActivityLogUpdateController = ActivityLogUpdateController_1 = class ActivityLogUpdateController {
    activityLogService;
    logger = new common_1.Logger(ActivityLogUpdateController_1.name);
    constructor(activityLogService) {
        this.activityLogService = activityLogService;
    }
    async update(id, updateActivityLogDto, userId) {
        this.logger.debug(`Đang cập nhật lịch sử hoạt động với ID: ${id}`);
        return this.activityLogService.update(id, updateActivityLogDto, userId);
    }
    async bulkUpdate(updateActivityLogDtos, userId) {
        this.logger.debug(`Đang cập nhật hàng loạt ${updateActivityLogDtos.length} lịch sử hoạt động`);
        return this.activityLogService.bulkUpdate(updateActivityLogDtos, userId);
    }
};
exports.ActivityLogUpdateController = ActivityLogUpdateController;
__decorate([
    (0, common_1.Patch)(':id'),
    (0, roles_decorator_1.Roles)(default_user_role_enum_1.PrimaryUserRole.ADMIN),
    (0, swagger_1.ApiOperation)({ summary: 'Cập nhật lịch sử hoạt động' }),
    (0, swagger_1.ApiParam)({
        name: 'id',
        description: 'ID của lịch sử hoạt động cần cập nhật',
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Lịch sử hoạt động đã được cập nhật thành công',
        type: activity_log_dto_1.ActivityLogDto,
    }),
    (0, swagger_1.ApiResponse)({
        status: 404,
        description: 'Không tìm thấy lịch sử hoạt động',
    }),
    (0, swagger_1.ApiBody)({ type: update_activity_log_dto_1.UpdateActivityLogDto }),
    openapi.ApiResponse({ status: 200, type: require("../dto/activity-log.dto").ActivityLogDto }),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __param(1, (0, common_1.Body)()),
    __param(2, (0, get_user_decorator_1.GetUser)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, update_activity_log_dto_1.UpdateActivityLogDto, String]),
    __metadata("design:returntype", Promise)
], ActivityLogUpdateController.prototype, "update", null);
__decorate([
    (0, common_1.Patch)('bulk'),
    (0, roles_decorator_1.Roles)(default_user_role_enum_1.PrimaryUserRole.ADMIN),
    (0, swagger_1.ApiOperation)({ summary: 'Cập nhật hàng loạt lịch sử hoạt động' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Các lịch sử hoạt động đã được cập nhật thành công',
        type: [activity_log_dto_1.ActivityLogDto],
    }),
    (0, swagger_1.ApiResponse)({
        status: 404,
        description: 'Không tìm thấy một hoặc nhiều lịch sử hoạt động',
    }),
    (0, swagger_1.ApiBody)({ type: [update_activity_log_dto_1.UpdateActivityLogDto] }),
    openapi.ApiResponse({ status: 200, type: [require("../dto/activity-log.dto").ActivityLogDto] }),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, get_user_decorator_1.GetUser)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Array, String]),
    __metadata("design:returntype", Promise)
], ActivityLogUpdateController.prototype, "bulkUpdate", null);
exports.ActivityLogUpdateController = ActivityLogUpdateController = ActivityLogUpdateController_1 = __decorate([
    (0, swagger_1.ApiTags)('activity-logs'),
    (0, swagger_1.ApiBearerAuth)(),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard, roles_guard_1.RolesGuard),
    (0, common_1.Controller)('activity-logs'),
    __metadata("design:paramtypes", [update_activity_log_service_1.UpdateActivityLogService])
], ActivityLogUpdateController);
//# sourceMappingURL=activity-log.update.controller.js.map
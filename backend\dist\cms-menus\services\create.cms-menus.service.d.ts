import { Repository, DataSource } from 'typeorm';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { BaseCmsMenusService } from './base.cms-menus.service';
import { CmsMenus } from '../entity/cms-menus.entity';
import { CreateCmsMenuDto } from '../dto/create.cms-menu.dto';
import { CmsMenuDto } from '../dto/cms-menu.dto';
export declare class CreateCmsMenusService extends BaseCmsMenusService {
    protected readonly menuRepository: Repository<CmsMenus>;
    protected readonly dataSource: DataSource;
    protected readonly eventEmitter: EventEmitter2;
    constructor(menuRepository: Repository<CmsMenus>, dataSource: DataSource, eventEmitter: EventEmitter2);
    create(createDto: CreateCmsMenuDto, userId: string): Promise<CmsMenuDto>;
    bulkCreate(createDtos: CreateCmsMenuDto[], userId: string): Promise<CmsMenuDto[]>;
    duplicate(id: string, userId: string): Promise<CmsMenuDto>;
}

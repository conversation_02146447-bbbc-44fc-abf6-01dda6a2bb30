"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.BankDto = void 0;
const openapi = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
const swagger_1 = require("@nestjs/swagger");
const class_transformer_1 = require("class-transformer");
const user_dto_1 = require("../../users/dto/user.dto");
class BankDto {
    id;
    brandName;
    fullName;
    shortName;
    code;
    bin;
    logoPath;
    icon;
    isActive;
    createdAt;
    updatedAt;
    createdBy;
    updatedBy;
    isDeleted;
    deletedBy;
    deletedAt;
    creator;
    updater;
    deleter;
    static _OPENAPI_METADATA_FACTORY() {
        return { id: { required: true, type: () => String, format: "uuid" }, brandName: { required: true, type: () => String }, fullName: { required: true, type: () => String }, shortName: { required: true, type: () => String }, code: { required: true, type: () => String }, bin: { required: true, type: () => String }, logoPath: { required: false, type: () => String }, icon: { required: false, type: () => String }, isActive: { required: true, type: () => Boolean }, createdAt: { required: true, type: () => Date }, updatedAt: { required: true, type: () => Date }, createdBy: { required: false, type: () => String, format: "uuid" }, updatedBy: { required: false, type: () => String, format: "uuid" }, isDeleted: { required: true, type: () => Boolean }, deletedBy: { required: false, type: () => String, format: "uuid" }, deletedAt: { required: false, type: () => Date }, creator: { required: false, type: () => require("../../users/dto/user.dto").UserDto }, updater: { required: false, type: () => require("../../users/dto/user.dto").UserDto }, deleter: { required: false, type: () => require("../../users/dto/user.dto").UserDto } };
    }
}
exports.BankDto = BankDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Mã định danh duy nhất của ngân hàng' }),
    (0, class_validator_1.IsUUID)(),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", String)
], BankDto.prototype, "id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Tên thương hiệu ngân hàng' }),
    (0, class_validator_1.IsString)(),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", String)
], BankDto.prototype, "brandName", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Tên đầy đủ ngân hàng' }),
    (0, class_validator_1.IsString)(),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", String)
], BankDto.prototype, "fullName", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Tên ngắn gọn ngân hàng' }),
    (0, class_validator_1.IsString)(),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", String)
], BankDto.prototype, "shortName", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Mã ngân hàng' }),
    (0, class_validator_1.IsString)(),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", String)
], BankDto.prototype, "code", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Bin ngân hàng' }),
    (0, class_validator_1.IsString)(),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", String)
], BankDto.prototype, "bin", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'URL Logo ngân hàng' }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", String)
], BankDto.prototype, "logoPath", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'URL Icon ngân hàng' }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", String)
], BankDto.prototype, "icon", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Trạng thái hoạt động của ngân hàng' }),
    (0, class_validator_1.IsBoolean)(),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", Boolean)
], BankDto.prototype, "isActive", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Thời gian tạo' }),
    (0, class_validator_1.IsDate)(),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", Date)
], BankDto.prototype, "createdAt", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Thời gian cập nhật cuối cùng' }),
    (0, class_validator_1.IsDate)(),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", Date)
], BankDto.prototype, "updatedAt", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'ID của người tạo ngân hàng' }),
    (0, class_validator_1.IsUUID)(),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", String)
], BankDto.prototype, "createdBy", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'ID của người cập nhật ngân hàng' }),
    (0, class_validator_1.IsUUID)(),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", String)
], BankDto.prototype, "updatedBy", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Trạng thái xóa của ngân hàng' }),
    (0, class_validator_1.IsBoolean)(),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", Boolean)
], BankDto.prototype, "isDeleted", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'ID của người xóa ngân hàng' }),
    (0, class_validator_1.IsUUID)(),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", String)
], BankDto.prototype, "deletedBy", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Thời gian xóa' }),
    (0, class_validator_1.IsDate)(),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", Date)
], BankDto.prototype, "deletedAt", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Người đã tạo ngân hàng' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => user_dto_1.UserDto),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", user_dto_1.UserDto)
], BankDto.prototype, "creator", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Người đã cập nhật ngân hàng' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => user_dto_1.UserDto),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", user_dto_1.UserDto)
], BankDto.prototype, "updater", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Người đã xóa ngân hàng' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => user_dto_1.UserDto),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", user_dto_1.UserDto)
], BankDto.prototype, "deleter", void 0);
//# sourceMappingURL=bank.dto.js.map
import { EventEmitter2 } from '@nestjs/event-emitter';
import { Repository } from 'typeorm';
import { CommissionProcessorService } from '../../agent-commissions/services/commission-processor.service';
import { AssetService } from '../../token-assets/asset.service';
import { ReadWalletService } from '../../wallets/services/read.wallet.service';
import { UpdateWalletService } from '../../wallets/services/update.wallet.service';
import { OrderBookDto } from '../dto/order-book.dto';
import { SettlementOrderBookDto } from '../dto/settlement-order-book.dto';
import { UpdateOrderBookDto } from '../dto/update-order-book.dto';
import { OrderBookDetail } from '../entities/order-book-detail.entity';
import { OrderBook } from '../entities/order-book.entity';
import { BaseOrderBookService } from './base.order-book.service';
export declare class UpdateOrderBookService extends BaseOrderBookService {
    protected readonly orderBookRepository: Repository<OrderBook>;
    private readonly orderBookDetailRepository;
    protected readonly eventEmitter: EventEmitter2;
    private readonly tokenAssetService;
    private readonly updateWalletService;
    private readonly readWalletService;
    private readonly commissionProcessorService;
    constructor(orderBookRepository: Repository<OrderBook>, orderBookDetailRepository: Repository<OrderBookDetail>, eventEmitter: EventEmitter2, tokenAssetService: AssetService, updateWalletService: UpdateWalletService, readWalletService: ReadWalletService, commissionProcessorService: CommissionProcessorService);
    update(id: string, updateOrderBookDto: UpdateOrderBookDto, userId: string): Promise<OrderBookDto>;
    private validateUpdatePermissions;
    private updateAllowedFields;
    bulkUpdate(updateOrderBookDtos: ({
        id: string;
    } & UpdateOrderBookDto)[], userId: string): Promise<OrderBookDto[]>;
    toggleStatus(id: string, userId: string): Promise<OrderBookDto>;
    updateOrderStatusPending(orderBookId: string, userId: string): Promise<OrderBook>;
    updateOrderStatusCompleted(orderBookId: string, userId: string, settlementData?: SettlementOrderBookDto): Promise<OrderBook>;
    private validateSettlementInput;
    private calculateSettlementAmounts;
    private updateOrderForSettlement;
    private processSettlementPayments;
    private validateSettlementWalletBalance;
    private processBuySettlementPayment;
    private processSellSettlementPayment;
    private processSettlementCommissions;
    private finalizeSettlement;
    private processAssetManagement;
    updateOrderStatusWaitPayment(orderBookId: string, userId: string): Promise<OrderBook>;
    private processOrderStatusUpdate;
    private handleBuyOrderTokenAsset;
    private handleSellOrderTokenAsset;
    extendSettlement(id: string, userId: string): Promise<OrderBookDto>;
    updateOrderStatusCancelled(id: string, userId: string): Promise<OrderBook>;
    approveOrder(id: string, userId: string): Promise<OrderBook>;
    duplicate(id: string, userId: string): Promise<OrderBookDto>;
}

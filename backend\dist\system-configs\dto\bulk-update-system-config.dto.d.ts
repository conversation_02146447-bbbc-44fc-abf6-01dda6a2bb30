import { ConfigType } from './system-config.dto';
export declare class BulkUpdateSystemConfigItemDto {
    configKey: string;
    configValue?: string;
    description?: string;
    configGroup?: string;
    sectionName?: string;
    sectionDisplayName?: string;
    sectionDescription?: string;
    sectionOrder?: number;
    displayOrder?: number;
    groupDisplayName?: string;
    groupDescription?: string;
    groupIcon?: string;
    groupOrder?: number;
    isGroupConfig?: boolean;
    configType?: ConfigType;
    configOptions?: string;
    updatedBy?: string;
}
export declare class BulkUpdateSystemConfigDto {
    configs: BulkUpdateSystemConfigItemDto[];
}

import { Logger } from '@nestjs/common';
import { Repository, FindOptionsWhere } from 'typeorm';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { User } from '../entities/user.entity';
import { UserDto } from '../dto/user.dto';
import { Role } from '../../roles/entities/role.entity';
export declare const EVENT_USER_CREATED = "user.created";
export declare const EVENT_USER_UPDATED = "user.updated";
export declare const EVENT_USER_DELETED = "user.deleted";
export declare const EVENT_USER_RESTORED = "user.restored";
export declare const EVENT_USER_STATUS_TOGGLED = "user.statusToggled";
export declare const EVENT_USER_DUPLICATED = "user.duplicated";
export declare const EVENT_USER_CLEANUP = "user.cleanup";
export declare class BaseUserService {
    protected readonly userRepository: Repository<User>;
    protected readonly roleRepository: Repository<Role>;
    protected readonly eventEmitter: EventEmitter2;
    protected readonly logger: Logger;
    protected readonly validRelations: string[];
    constructor(userRepository: Repository<User>, roleRepository: Repository<Role>, eventEmitter: EventEmitter2);
    protected convertToDto(user: User): UserDto;
    protected convertToDtoArray(users: User[]): UserDto[];
    protected findByIdOrFail(id: string, relations?: string[], withDeleted?: boolean): Promise<User>;
    protected findRolesByIdsOrFail(roleIds: string[]): Promise<Role[]>;
    protected preloadRolesMap(roleIds: string[]): Promise<Map<string, Role>>;
    protected buildWhereClause(filter?: string): FindOptionsWhere<User>;
}

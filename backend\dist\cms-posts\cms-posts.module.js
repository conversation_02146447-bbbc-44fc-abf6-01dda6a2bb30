"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CmsPostsModule = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const cms_posts_entity_1 = require("./entity/cms-posts.entity");
const cms_categories_entity_1 = require("../cms-categories/entity/cms-categories.entity");
const user_entity_1 = require("../users/entities/user.entity");
const base_cms_posts_service_1 = require("./services/base.cms-posts.service");
const slug_cms_posts_service_1 = require("./services/slug.cms-posts.service");
const create_cms_posts_service_1 = require("./services/create.cms-posts.service");
const read_cms_posts_service_1 = require("./services/read.cms-posts.service");
const update_cms_posts_service_1 = require("./services/update.cms-posts.service");
const delete_cms_posts_service_1 = require("./services/delete.cms-posts.service");
const unified_slug_service_1 = require("../common/services/unified-slug.service");
const controllers_1 = require("./controllers");
let CmsPostsModule = class CmsPostsModule {
};
exports.CmsPostsModule = CmsPostsModule;
exports.CmsPostsModule = CmsPostsModule = __decorate([
    (0, common_1.Module)({
        imports: [
            typeorm_1.TypeOrmModule.forFeature([cms_posts_entity_1.CmsPosts, cms_categories_entity_1.CmsCategories, user_entity_1.User]),
        ],
        controllers: [
            controllers_1.CreateCmsPostsController,
            controllers_1.ReadCmsPostsController,
            controllers_1.ReadCmsPostsPublicController,
            controllers_1.UpdateCmsPostsController,
            controllers_1.DeleteCmsPostsController,
        ],
        providers: [
            unified_slug_service_1.UnifiedSlugService,
            base_cms_posts_service_1.BaseCmsPostsService,
            slug_cms_posts_service_1.SlugCmsPostsService,
            create_cms_posts_service_1.CreateCmsPostsService,
            read_cms_posts_service_1.ReadCmsPostsService,
            update_cms_posts_service_1.UpdateCmsPostsService,
            delete_cms_posts_service_1.DeleteCmsPostsService,
        ],
        exports: [
            typeorm_1.TypeOrmModule,
            base_cms_posts_service_1.BaseCmsPostsService,
            slug_cms_posts_service_1.SlugCmsPostsService,
            create_cms_posts_service_1.CreateCmsPostsService,
            read_cms_posts_service_1.ReadCmsPostsService,
            update_cms_posts_service_1.UpdateCmsPostsService,
            delete_cms_posts_service_1.DeleteCmsPostsService,
        ],
    })
], CmsPostsModule);
//# sourceMappingURL=cms-posts.module.js.map
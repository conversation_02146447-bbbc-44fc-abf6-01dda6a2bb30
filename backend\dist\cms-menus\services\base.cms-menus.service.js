"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var BaseCmsMenusService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.BaseCmsMenusService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const event_emitter_1 = require("@nestjs/event-emitter");
const class_transformer_1 = require("class-transformer");
const cms_menus_entity_1 = require("../entity/cms-menus.entity");
const cms_menu_dto_1 = require("../dto/cms-menu.dto");
let BaseCmsMenusService = BaseCmsMenusService_1 = class BaseCmsMenusService {
    menuRepository;
    dataSource;
    eventEmitter;
    logger = new common_1.Logger(BaseCmsMenusService_1.name);
    EVENT_MENU_CREATED = 'cms-menu.created';
    EVENT_MENU_UPDATED = 'cms-menu.updated';
    EVENT_MENU_DELETED = 'cms-menu.deleted';
    validRelations = [
        'parent',
        'children',
        'creator',
        'updater',
        'deleter'
    ];
    constructor(menuRepository, dataSource, eventEmitter) {
        this.menuRepository = menuRepository;
        this.dataSource = dataSource;
        this.eventEmitter = eventEmitter;
    }
    toDto(menu) {
        if (!menu) {
            return null;
        }
        return (0, class_transformer_1.plainToInstance)(cms_menu_dto_1.CmsMenuDto, {
            id: menu.id,
            businessCode: menu.businessCode,
            name: menu.name,
            slug: menu.slug,
            description: menu.description,
            postType: menu.postType,
            imageUrl: menu.imageUrl,
            metaTitle: menu.metaTitle,
            metaDescription: menu.metaDescription,
            metaKeywords: menu.metaKeywords,
            status: menu.status,
            parentId: menu.parent?.id,
            parent: menu.parent ? this.toDto(menu.parent) : undefined,
            children: menu.children ? menu.children.map(child => this.toDto(child)).filter(Boolean) : undefined,
            creator: menu.creator,
            updater: menu.updater,
            deleter: menu.deleter,
            createdAt: menu.createdAt,
            updatedAt: menu.updatedAt,
            deletedAt: menu.deletedAt,
            isDeleted: menu.isDeleted,
        }, { excludeExtraneousValues: true });
    }
    toDtos(menus) {
        return menus.map(menu => this.toDto(menu)).filter(Boolean);
    }
    async findById(id, relations = [], throwError = true) {
        const validatedRelations = this.validateRelations(relations);
        const menu = await this.menuRepository.findOne({
            where: { id, isDeleted: false },
            relations: validatedRelations,
        });
        if (!menu && throwError) {
            throw new common_1.NotFoundException(`Không tìm thấy menu với ID: ${id}`);
        }
        return menu;
    }
    async findBySlug(slug, postType, throwError = true) {
        const menu = await this.menuRepository.findOne({
            where: { slug, postType: postType, isDeleted: false },
        });
        if (!menu && throwError) {
            throw new common_1.NotFoundException(`Không tìm thấy menu với slug: ${slug} và loại post: ${postType}`);
        }
        return menu;
    }
    async findByName(name, postType, throwError = true) {
        const menu = await this.menuRepository.findOne({
            where: { name, postType: postType, isDeleted: false },
        });
        if (!menu && throwError) {
            throw new common_1.NotFoundException(`Không tìm thấy menu với tên: ${name} và loại post: ${postType}`);
        }
        return menu;
    }
    validateRelations(relations) {
        return relations.filter(relation => this.validRelations.includes(relation));
    }
    generateSlugFromName(name) {
        return name
            .toLowerCase()
            .normalize('NFD')
            .replace(/[\u0300-\u036f]/g, '')
            .replace(/[đĐ]/g, 'd')
            .replace(/[^a-z0-9\s-]/g, '')
            .trim()
            .replace(/\s+/g, '-')
            .replace(/-+/g, '-');
    }
    async isSlugUnique(slug, postType, excludeId) {
        const whereCondition = {
            slug,
            postType: postType,
            isDeleted: false
        };
        if (excludeId) {
            whereCondition.id = { $ne: excludeId };
        }
        const existingMenu = await this.menuRepository.findOne({
            where: whereCondition,
        });
        return !existingMenu;
    }
    async isNameUnique(name, postType, excludeId) {
        const whereCondition = {
            name,
            postType: postType,
            isDeleted: false
        };
        if (excludeId) {
            whereCondition.id = { $ne: excludeId };
        }
        const existingMenu = await this.menuRepository.findOne({
            where: whereCondition,
        });
        return !existingMenu;
    }
};
exports.BaseCmsMenusService = BaseCmsMenusService;
exports.BaseCmsMenusService = BaseCmsMenusService = BaseCmsMenusService_1 = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(cms_menus_entity_1.CmsMenus)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        typeorm_2.DataSource,
        event_emitter_1.EventEmitter2])
], BaseCmsMenusService);
//# sourceMappingURL=base.cms-menus.service.js.map
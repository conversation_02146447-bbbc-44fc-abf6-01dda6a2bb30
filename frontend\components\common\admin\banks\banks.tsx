'use client';

import { useState, useEffect, useCallback } from 'react';
import { Plus } from 'lucide-react';
import { toast } from 'sonner';
import { FormModal } from './form-modal';
import { DetailSheet } from './detail-sheet';
import { DataTable } from '../../data-table/data-table';
import { api } from '@/lib/api';
import { BankDto as Bank } from './type';
import { BankStatisticsDto } from './type/bank-statistics.dto';
import { getBankCell } from './table/cell';
import { Checkbox } from '@/components/ui/checkbox';
import { useReactTable, ColumnFiltersState, PaginationState, RowSelectionState, SortingState, VisibilityState, getCoreRowModel, getSortedRowModel, getFilteredRowModel } from '@tanstack/react-table';
import { TableToolbar } from '../../data-table/table-toolbar';
import { TableFooter } from '../../data-table/table-footer';
import { Button } from '@/components/ui/button';
// import { FilterRequest } from '@/lib/search-criteria';
import { PaginationResponse, ApiResponse } from '@/lib/response';
// import { toast } from "sonner" // Đã import ở trên

import { StatusTabs, StatusFilter } from './components/status-tabs';
import { FloatDeleteButton } from './components/float-delete-button';

export default function Banks() {
  const [banks, setBanks] = useState<Bank[]>([]);
  // loading state được sử dụng để hiển thị trạng thái loading
  const [loading, setLoading] = useState(true)

  // Hiển thị loading state trong UI
  useEffect(() => {
    if (loading) {
      // Có thể thêm code hiển thị loading indicator ở đây
       
    }
  }, [loading])
  const [sorting, setSorting] = useState<SortingState>([])
  const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([])
  const [pagination, setPagination] = useState<PaginationState>({
    pageIndex: 0,
    pageSize: 20,
  })
  const [totalRows, setTotalRows] = useState(0)
  const [globalFilter, setGlobalFilter] = useState("")


  const [rowSelection, setRowSelection] = useState<RowSelectionState>({})
  // Chỉ ẩn mặc định hai cột: người xóa và ngày xóa
  const [columnVisibility, setColumnVisibility] = useState<VisibilityState>({
    deleter: false,
    deletedAt: false,
  })
  const [isRefreshing, setIsRefreshing] = useState(false)
  const [selectedBank, setSelectedBank] = useState<Bank | null>(null)
  // Đã loại bỏ các state không sử dụng: showDetail, showDeleteDialog, banksToDelete
  const [isShowSelectedRows, setIsShowSelectedRows] = useState(false)
  const [isUpdating, setIsUpdating] = useState(false)
  const [statusFilter, setStatusFilter] = useState<StatusFilter>('all')
  const [statusCounts, setStatusCounts] = useState({ all: 0, active: 0, inactive: 0 })
  const [showBankFormModal, setShowBankFormModal] = useState(false)
  const [showBankDetailSheet, setShowBankDetailSheet] = useState(false)
  const [bankFormMode, setBankFormMode] = useState<'create' | 'update' | 'view'>('create')

  // Xử lý xem chi tiết ngân hàng
  const handleViewDetail = (bank: Bank) => {
    setSelectedBank(bank);
    setShowBankDetailSheet(true);
    toast.info(`Đang xem thông tin của ngân hàng ${bank.brandName}`);
  };

  // Xử lý chỉnh sửa ngân hàng
  const handleEdit = (bank: Bank) => {
    setSelectedBank(bank);
    setBankFormMode('update');
    setShowBankFormModal(true);
    toast.info(`Đang chỉnh sửa thông tin của ngân hàng ${bank.brandName}`);
  };

  // Xử lý xóa ngân hàng
  const handleDelete = async (bank: Bank) => {
    try {
      setIsUpdating(true);
      // Gọi API để soft delete bank
      await api.post(`banks/${bank.id}/soft-delete`);

      // Cập nhật dữ liệu local
      const updatedBanks = banks.map(b => {
        if (b.id === bank.id) {
          return {
            ...b,
            isDeleted: true,
            deletedAt: new Date().toISOString()
          };
        }
        return b;
      });

      setBanks(updatedBanks);
      toast.success(`Đã xóa ngân hàng ${bank.brandName}`);
    } catch (error) {
      console.error('Error deleting bank:', error);
      toast.error("Không thể xóa ngân hàng");
    } finally {
      setIsUpdating(false);
      fetchBanks();
    }
  };

  // Xử lý thay đổi trạng thái ngân hàng (kích hoạt/vô hiệu hóa)
  const handleToggleStatus = async (bank: Bank) => {
    try {
      setIsUpdating(true);
      // Gọi API để thay đổi trạng thái
      await api.put(`banks/${bank.id}/toggle-status`);

      // Cập nhật dữ liệu local
      const updatedBanks = banks.map(b => {
        if (b.id === bank.id) {
          return {
            ...b,
            isActive: !b.isActive,
            status: b.isActive ? 'INACTIVE' : 'ACTIVE'
          };
        }
        return b;
      });

      setBanks(updatedBanks);
      toast.success(`Đã ${bank.isActive ? 'vô hiệu hóa' : 'kích hoạt'} ngân hàng ${bank.brandName}`);
    } catch (error) {
      console.error('Error toggling bank status:', error);
      toast.error("Không thể thay đổi trạng thái ngân hàng");
    } finally {
      setIsUpdating(false);
      fetchBanks();
    }
  };

  // Xử lý nhân bản ngân hàng
  const handleDuplicate = async (bank: Bank) => {
    try {
      setIsUpdating(true);
      // Gọi API để nhân bản ngân hàng
      const response = await api.post<Bank>(`banks/${bank.id}/duplicate`);

      if (response) {
        toast.success(`Đã nhân bản ngân hàng ${bank.brandName} thành công`);
        // Refresh dữ liệu
        fetchBanks();
      }
    } catch (error) {
      console.error('Error duplicating bank:', error);
      toast.error("Không thể nhân bản ngân hàng");
    } finally {
      setIsUpdating(false);
    }
  };

  // Xử lý cập nhật trạng thái ngân hàng (isActive)
  const handleUpdateBankStatus = async (bankId: string, isActive: boolean) => {
    try {
      setIsUpdating(true);
      // Gọi API để cập nhật trạng thái
      await api.put(`banks/${bankId}/toggle-status`);

      // Cập nhật dữ liệu local
      const updatedBanks = banks.map(bank => {
        if (bank.id === bankId) {
          return {
            ...bank,
            isActive,
            status: isActive ? 'ACTIVE' : 'INACTIVE'
          };
        }
        return bank;
      });

      setBanks(updatedBanks);

      toast.success(`Đã ${isActive ? 'kích hoạt' : 'vô hiệu hóa'} ngân hàng`);
    } catch (error) {
      console.error('Error updating bank status:', error);
      toast.error("Không thể cập nhật trạng thái ngân hàng");
    } finally {
      setIsUpdating(false);
      fetchBanks();
    }
  };

  const table = useReactTable({
    data: banks,
    columns: [
      {
        id: 'select',
        size: 40,
        header: ({ table }) => (
          <div className="px-1">
            <Checkbox
              checked={
                table.getIsAllPageRowsSelected() ||
                (table.getIsSomePageRowsSelected() && "indeterminate")
              }
              onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
              aria-label="Select all"
              className="translate-y-[2px]"
            />
          </div>
        ),
        cell: ({ row }) => (
          <div className="px-1">
            <Checkbox
              checked={row.getIsSelected()}
              onCheckedChange={(value) => row.toggleSelected(!!value)}
              onClick={(e) => e.stopPropagation()}
              aria-label="Select row"
              className="translate-y-[2px]"
            />
          </div>
        ),
        enableSorting: false,
        enableHiding: false,
      },
      ...getBankCell({
        onViewDetail: handleViewDetail,
        onDelete: handleDelete,
        onEdit: handleEdit,
        onToggleStatus: handleToggleStatus,
        onDuplicate: handleDuplicate,
      })
    ],
    state: {
      sorting,
      columnFilters,
      pagination,
      rowSelection,
      columnVisibility,
      globalFilter,
    },
    pageCount: Math.ceil(totalRows / pagination.pageSize),
    onSortingChange: setSorting,
    onColumnFiltersChange: setColumnFilters,
    onPaginationChange: setPagination,
    onRowSelectionChange: setRowSelection,
    onColumnVisibilityChange: setColumnVisibility,
    onGlobalFilterChange: setGlobalFilter,
    getCoreRowModel: getCoreRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    manualPagination: true,
    enableSorting: true,
    enableColumnFilters: true,
    enableRowSelection: true,
    enableMultiSort: true,
    manualSorting: true,
    manualFiltering: true,
  })

  useEffect(() => {
    fetchBanks();
    // Gọi fetchStatistics để lấy số liệu chính xác cho StatusTabs
    fetchStatistics();
  }, [pagination.pageIndex, pagination.pageSize, statusFilter, table.getState().sorting, globalFilter]);

  // Fetch thống kê số lượng ngân hàng theo trạng thái cho toàn bộ hệ thống
  const fetchStatistics = async (force = false) => {
    // Sử dụng biến tĩnh để theo dõi xem đã gọi API chưa
    if (!fetchStatistics.hasRun || force) {
      try {
        // Sử dụng endpoint statistics để lấy thống kê trong một lần gọi API
        const response = await api.get<ApiResponse<BankStatisticsDto>>('banks/statistics');

        if (response && response.data) {
          // Xử lý đúng cấu trúc response: { success: true, data: { total, active, inactive } }
          const statsData = response.data as BankStatisticsDto;
          const counts = {
            all: statsData.total || 0,
            active: statsData.active || 0,
            inactive: statsData.inactive || 0
          };

          setStatusCounts(counts);

        }

        // Đánh dấu đã gọi API
        fetchStatistics.hasRun = true;
      } catch (error) {
        console.error('Error fetching bank statistics:', error);
        toast.error('Không thể lấy thống kê ngân hàng');

        // Đặt giá trị mặc định khi có lỗi
        setStatusCounts({
          all: 0,
          active: 0,
          inactive: 0
        });
      }
    }
  };

  // Thêm thuộc tính tĩnh cho hàm
  fetchStatistics.hasRun = false;

  const fetchBanks = async () => {
    setLoading(true);
    try {
      // const filter: FilterRequest = {
      //   searchCriteria: []
      // }
      // Hiện tại không sử dụng FilterRequest vì backend chưa hỗ trợ

      // Thêm điều kiện lọc theo trạng thái
      // Đã xử lý trong phần tạo filterParams

      // Thêm điều kiện tìm kiếm nếu có
      // Hiện tại backend chưa hỗ trợ tìm kiếm phức tạp, chỉ hỗ trợ tìm kiếm đơn giản
      // Sẽ cập nhật sau khi backend hỗ trợ tìm kiếm phức tạp hơn

      // Xử lý sorting
      let sortByParam = '';
      let sortOrderParam = '';
      const currentSorting = table.getState().sorting;
      if (currentSorting.length > 0) {
        // Backend chỉ hỗ trợ sắp xếp theo một trường, lấy trường đầu tiên
        const firstSort = currentSorting[0];
        sortByParam = `&sortBy=${firstSort.id}`;
        sortOrderParam = `&sortOrder=${firstSort.desc ? 'DESC' : 'ASC'}`;
      }

      // Xử lý column filters
      // const columnFilters = table.getState().columnFilters;
      // Hiện tại backend chưa hỗ trợ xử lý column filters phức tạp

      // Tạo query string cho search
      let searchParam = '';

      // Chỉ sử dụng globalFilter cho tìm kiếm
      if (globalFilter) {
        searchParam = `&search=${encodeURIComponent(globalFilter)}`;
      }

      // Xử lý các column filter khác (chưa hỗ trợ trong backend hiện tại)

      const response = await api.get<PaginationResponse<Bank>>(
        `banks?page=${pagination.pageIndex + 1}&limit=${pagination.pageSize}${sortByParam}${sortOrderParam}${searchParam}`
      );

      if (response && response.data) {
        // Map backend data to our Bank interface
        const mappedBanks = response.data.map((bank: any) => {
          return {
            ...bank,
            status: bank.isActive ? 'ACTIVE' : 'INACTIVE',
          };
        });

        // Lọc dữ liệu theo trạng thái ở phía client
        let filteredBanks = mappedBanks;
        if (statusFilter === 'active') {
          filteredBanks = mappedBanks.filter((bank: Bank) => bank.isActive);
        } else if (statusFilter === 'inactive') {
          filteredBanks = mappedBanks.filter((bank: Bank) => !bank.isActive);
        }

        setBanks(filteredBanks);
        setTotalRows(filteredBanks.length);

        // Không tính statusCounts từ pagination data vì không chính xác
        // statusCounts sẽ được lấy từ fetchStatistics() API riêng biệt
      } else {
        setTotalRows(0);
      }
    } catch (error: any) {
      console.error('Error fetching banks:', error);

      // Kiểm tra lỗi 401/403
      if (error.status === 401) {
        toast.error("Bạn chưa đăng nhập hoặc phiên đăng nhập đã hết hạn");
      } else if (error.status === 403) {
        toast.error("Bạn không có quyền truy cập vào tài nguyên này");
      } else if (error.status === 404) {
        toast.error("Không tìm thấy tài nguyên");
      } else {
        toast.error("Không thể tải danh sách ngân hàng");
      }

      // Đặt giá trị mặc định khi có lỗi
      setBanks([]);
      setTotalRows(0);
      setStatusCounts({
        all: 0,
        active: 0,
        inactive: 0
      });
    } finally {
      setLoading(false);
      setIsRefreshing(false);
    }
  };

  // Xử lý export dữ liệu
  const handleExport = async (format: 'csv' | 'json' = 'csv') => {
    try {
      setIsUpdating(true);
      // Gọi API để export dữ liệu
      await api.downloadFile(`banks/export?format=${format}`, format, `banks-export.${format}`);
      toast.success(`Đã xuất dữ liệu thành công dưới dạng ${format.toUpperCase()}`);
    } catch (error) {
      console.error(`Error exporting banks as ${format}:`, error);
      toast.error(`Không thể xuất dữ liệu dưới dạng ${format.toUpperCase()}`);
    } finally {
      setIsUpdating(false);
    }
  };

  // Xử lý import dữ liệu
  const handleImport = async () => {
    try {
      // Tạo input file ẩn
      const input = document.createElement('input');
      input.type = 'file';
      input.accept = '.csv,.json';

      // Xử lý sự kiện khi người dùng chọn file
      input.onchange = async (e: Event) => {
        const target = e.target as HTMLInputElement;
        if (!target.files || target.files.length === 0) return;

        const file = target.files[0];
        const fileType = file.name.endsWith('.csv') ? 'csv' : 'json';

        setIsUpdating(true);
        try {
          // Đọc file
          const fileContent = await file.text();
          let banksData: any[] = [];

          // Parse dữ liệu từ file
          if (fileType === 'csv') {
            // Parse CSV
            const lines = fileContent.split('\n');
            const headers = lines[0].split(',');

            for (let i = 1; i < lines.length; i++) {
              if (!lines[i].trim()) continue;

              const values = lines[i].split(',');
              const bank: any = {};

              headers.forEach((header, index) => {
                bank[header.trim()] = values[index]?.trim() || '';
              });

              banksData.push(bank);
            }
          } else {
            // Parse JSON
            banksData = JSON.parse(fileContent);
          }

          // Gọi API để tạo hàng loạt ngân hàng
          await api.post('banks/bulk', banksData);

          toast.success(`Đã import ${banksData.length} ngân hàng thành công`);
          fetchBanks();
        } catch (error) {
          console.error('Error importing banks:', error);
          toast.error('Không thể import dữ liệu ngân hàng');
        } finally {
          setIsUpdating(false);
        }
      };

      // Click để mở hộp thoại chọn file
      input.click();
    } catch (error) {
      console.error('Error setting up import:', error);
      toast.error('Không thể khởi tạo chức năng import');
    }
  };

  // Đã loại bỏ các hàm không sử dụng: handleFilter, handleDisplayChange

  const handleShowSelectedRows = () => {
    setIsShowSelectedRows(!isShowSelectedRows)
  }

  const handleAddBank = () => {
    setSelectedBank(null);
    setBankFormMode('create');
    setShowBankFormModal(true);
  };

  const handleRefresh = useCallback(async () => {
    setIsRefreshing(true)
    try {
      // Đặt lại các bộ lọc và sắp xếp
      table.resetColumnFilters()
      table.resetSorting()
      setStatusFilter('all')
      setGlobalFilter('')

      // Fetch dữ liệu mới và thống kê (force=true để bắt buộc cập nhật thống kê)
      await Promise.all([
        fetchBanks(),
        fetchStatistics(true)
      ])
    } finally {
      setTimeout(() => {
        setIsRefreshing(false)
      }, 1000)
    }
  }, [fetchBanks, table])

  const handleDeleteSelected = async () => {
    try {
      setIsUpdating(true);
      const selectedRows = table.getSelectedRowModel().rows;
      const selectedBanks = selectedRows.map(row => row.original);

      // Thực hiện xóa từng ngân hàng đã chọn
      const deletePromises = selectedBanks.map(bank =>
        api.post(`banks/${bank.id}/soft-delete`)
      );

      // Hoặc sử dụng endpoint bulk-soft-delete
      // const bankIds = selectedBanks.map(bank => bank.id);
      // await api.post('banks/bulk-soft-delete', bankIds);

      await Promise.all(deletePromises);

      // Cập nhật UI
      toast.success(`Đã xóa ${selectedBanks.length} ngân hàng thành công`);

      // Reset selection
      table.resetRowSelection();

      // Refresh data
      await fetchBanks();
    } catch (error) {
      console.error('Error deleting banks:', error);
      toast.error("Không thể xóa các ngân hàng đã chọn");
    } finally {
      setIsUpdating(false);
    }
  };

  return (
    <div className="w-full flex flex-col h-full">
      {/* Header Navigation */}
      <div className="w-full flex justify-between items-center border-b py-1.5 px-6 h-10">
        <div className="flex items-center gap-2">
          <div className="flex items-center gap-1">
            <span className="text-sm font-medium">Ngân hàng</span>
            <span className="text-xs bg-accent rounded-md px-1.5 py-1">{totalRows}</span>
          </div>
        </div>
        <div className="flex items-center gap-2">
          {isUpdating && (
            <span className="text-xs text-muted-foreground">Đang cập nhật...</span>
          )}
          {/* <Button className="relative" size="sm" variant="outline" onClick={handleImport}>
            <Upload className="size-4 mr-1" />
            Import
          </Button>
          <Button className="relative" size="sm" variant="outline" onClick={() => handleExport('csv')}>
            <Download className="size-4 mr-1" />
            Export CSV
          </Button>
          <Button className="relative" size="sm" variant="outline" onClick={() => handleExport('json')}>
            <Download className="size-4 mr-1" />
            Export JSON
          </Button> */}
          <Button className="relative" size="sm" variant="secondary" onClick={handleAddBank}>
            <Plus className="size-4 mr-1" />
            Thêm mới
          </Button>
        </div>
      </div>

      {/* Table Toolbar with Status Tabs */}
      <TableToolbar
        table={table}
        globalFilter={globalFilter}
        setGlobalFilter={setGlobalFilter}
        onRefresh={handleRefresh}
        isRefreshing={isRefreshing}
        isShowSelectedRows={isShowSelectedRows}
        onShowSelectedRows={handleShowSelectedRows}
        beforeSearchSlot={
          <StatusTabs
            currentStatus={statusFilter}
            onStatusChange={setStatusFilter}
            counts={statusCounts}
            className="w-fit"
          />
        }
      />

      {/* Data Table */}
      <div className="flex-1 overflow-auto">
        <DataTable
          table={table}
          className="w-full"
          title=""
          totalItems={totalRows}
        />
      </div>

      {/* Fixed Pagination Footer */}
      <TableFooter table={table} totalItems={totalRows} isShowSelectedRows={isShowSelectedRows} onShowSelectedRows={handleShowSelectedRows} />

      {/* Float Delete Button */}
      <FloatDeleteButton
        selectedCount={table.getFilteredSelectedRowModel().rows.length}
        onDelete={handleDeleteSelected}
      />

      {/* Bank Form Modal */}
      <FormModal
        isOpen={showBankFormModal}
        onClose={() => setShowBankFormModal(false)}
        bank={selectedBank}
        mode={bankFormMode}
        onSuccess={handleRefresh}
      />

      {/* Bank Detail Sheet */}
      <DetailSheet
        isOpen={showBankDetailSheet}
        onClose={() => setShowBankDetailSheet(false)}
        bank={selectedBank}
        onEdit={(bank) => {
          setSelectedBank(bank);
          setBankFormMode('update');
          setShowBankFormModal(true);
          setShowBankDetailSheet(false);
        }}
      />
    </div>
  );
}

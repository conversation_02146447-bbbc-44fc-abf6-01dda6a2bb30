"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ReadAssetsService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("typeorm");
const base_assets_service_1 = require("./base-assets.service");
const pagination_query_dto_1 = require("../../common/dto/pagination-query.dto");
let ReadAssetsService = class ReadAssetsService extends base_assets_service_1.BaseAssetsService {
    async findAll(params) {
        try {
            const { limit, page, sortBy, sortOrder = 'DESC', filter, search, relations = [], } = params;
            const validatedRelations = this.validateRelations([
                ...relations,
                'token',
                'user',
                'creator',
                'updater',
                'deleter',
            ]);
            this.logger.debug(`Đang tìm tài sản token với params: ${JSON.stringify(params)}`);
            const query = this.assetRepository
                .createQueryBuilder('tokenAsset')
                .where({ isDeleted: false });
            if (validatedRelations.includes('product')) {
                query.leftJoinAndSelect('tokenAsset.product', 'product');
            }
            if (validatedRelations.includes('user')) {
                query.leftJoinAndSelect('tokenAsset.user', 'user');
            }
            if (validatedRelations.includes('creator')) {
                query.leftJoinAndSelect('tokenAsset.creator', 'creator');
            }
            if (validatedRelations.includes('updater')) {
                query.leftJoinAndSelect('tokenAsset.updater', 'updater');
            }
            if (validatedRelations.includes('deleter')) {
                query.leftJoinAndSelect('tokenAsset.deleter', 'deleter');
            }
            if (filter) {
                try {
                    const filters = filter.split(',');
                    filters.forEach((filterItem, index) => {
                        const [field, value] = filterItem.split(':');
                        const paramName = `value${index}`;
                        if (field.includes('.')) {
                            const [relation, relationField] = field.split('.');
                            query.andWhere(`${relation}.${relationField} = :${paramName}`, {
                                [paramName]: value,
                            });
                        }
                        else {
                            query.andWhere(`tokenAsset.${field} = :${paramName}`, {
                                [paramName]: value,
                            });
                        }
                    });
                }
                catch (error) {
                    this.logger.warn(`Định dạng lọc không hợp lệ: ${filter}`);
                }
            }
            if (search) {
                query.andWhere(new typeorm_1.Brackets((qb) => {
                    qb.where('product.name ILIKE :search', {
                        search: `%${search}%`,
                    })
                        .orWhere('product.code ILIKE :search', {
                        search: `%${search}%`,
                    })
                        .orWhere('user.email ILIKE :search', { search: `%${search}%` })
                        .orWhere('user.fullName ILIKE :search', { search: `%${search}%` })
                        .orWhere('creator.email ILIKE :search', { search: `%${search}%` })
                        .orWhere('creator.fullName ILIKE :search', {
                        search: `%${search}%`,
                    })
                        .orWhere('updater.email ILIKE :search', { search: `%${search}%` })
                        .orWhere('updater.fullName ILIKE :search', {
                        search: `%${search}%`,
                    })
                        .orWhere('deleter.email ILIKE :search', { search: `%${search}%` })
                        .orWhere('deleter.fullName ILIKE :search', {
                        search: `%${search}%`,
                    });
                }));
            }
            if (sortBy) {
                if (sortBy.includes('.')) {
                    const [relation, relationField] = sortBy.split('.');
                    query.orderBy(`${relation}.${relationField}`, sortOrder);
                }
                else {
                    query.orderBy(`tokenAsset.${sortBy}`, sortOrder);
                }
            }
            else {
                query.orderBy('tokenAsset.createdAt', 'DESC');
            }
            query.skip((page - 1) * limit).take(limit);
            const total = await query.getCount();
            const [tokenAssets] = await query.getManyAndCount();
            this.logger.debug(`Đã tìm thấy ${tokenAssets.length} tài sản token (tổng: ${total})`);
            const userIds = tokenAssets.map((asset) => asset.userId).filter(Boolean);
            const wallets = userIds.length > 0
                ? await this.walletRepository.find({ where: { userId: (0, typeorm_1.In)(userIds) } })
                : [];
            const walletMap = new Map();
            wallets.forEach((wallet) => {
                walletMap.set(wallet.userId, wallet);
            });
            const dtos = tokenAssets.map((tokenAsset) => {
                const dto = this.toDto(tokenAsset);
                const wallet = walletMap.get(tokenAsset.userId);
                if (wallet) {
                    dto.walletBalance = wallet.balance;
                }
                return dto;
            });
            return {
                data: dtos,
                total,
            };
        }
        catch (error) {
            this.logger.error(`Lỗi khi tìm tài sản token: ${error.message}`, error.stack);
            throw new common_1.InternalServerErrorException(`Không thể tìm tài sản token: ${error.message}`);
        }
    }
    async search(keyword, params, filter) {
        try {
            const { limit, page, sortOrder = pagination_query_dto_1.SortOrder.DESC } = params;
            this.logger.debug(`Đang tìm kiếm tài sản token với từ khóa: ${keyword}`);
            const query = this.assetRepository
                .createQueryBuilder('tokenAsset')
                .leftJoinAndSelect('tokenAsset.token', 'token')
                .leftJoinAndSelect('tokenAsset.user', 'user')
                .leftJoinAndSelect('tokenAsset.creator', 'creator')
                .leftJoinAndSelect('tokenAsset.updater', 'updater')
                .leftJoinAndSelect('tokenAsset.deleter', 'deleter')
                .where('tokenAsset.isDeleted = :isDeleted', { isDeleted: false })
                .andWhere(new typeorm_1.Brackets((qb) => {
                qb.where('token.tokenName ILIKE :keyword', {
                    keyword: `%${keyword}%`,
                })
                    .orWhere('token.tokenCode ILIKE :keyword', {
                    keyword: `%${keyword}%`,
                })
                    .orWhere('user.email ILIKE :keyword', { keyword: `%${keyword}%` })
                    .orWhere('user.fullName ILIKE :keyword', {
                    keyword: `%${keyword}%`,
                })
                    .orWhere('creator.email ILIKE :keyword', {
                    keyword: `%${keyword}%`,
                })
                    .orWhere('creator.fullName ILIKE :keyword', {
                    keyword: `%${keyword}%`,
                })
                    .orWhere('updater.email ILIKE :keyword', {
                    keyword: `%${keyword}%`,
                })
                    .orWhere('updater.fullName ILIKE :keyword', {
                    keyword: `%${keyword}%`,
                })
                    .orWhere('deleter.email ILIKE :keyword', {
                    keyword: `%${keyword}%`,
                })
                    .orWhere('deleter.fullName ILIKE :keyword', {
                    keyword: `%${keyword}%`,
                });
            }));
            if (filter) {
                try {
                    const filters = filter.split(',');
                    filters.forEach((filterItem, index) => {
                        const [field, value] = filterItem.split(':');
                        const paramName = `value${index}`;
                        if (field.includes('.')) {
                            const [relation, relationField] = field.split('.');
                            query.andWhere(`${relation}.${relationField} = :${paramName}`, {
                                [paramName]: value,
                            });
                        }
                        else {
                            query.andWhere(`tokenAsset.${field} = :${paramName}`, {
                                [paramName]: value,
                            });
                        }
                    });
                }
                catch (error) {
                    this.logger.warn(`Định dạng lọc không hợp lệ: ${filter}`);
                }
            }
            query
                .skip((page - 1) * limit)
                .take(limit)
                .orderBy('tokenAsset.createdAt', sortOrder);
            const [tokenAssets, total] = await query.getManyAndCount();
            this.logger.debug(`Đã tìm thấy ${tokenAssets.length} tài sản token (tổng: ${total})`);
            const userIds = tokenAssets.map((asset) => asset.userId).filter(Boolean);
            const wallets = userIds.length > 0
                ? await this.walletRepository.find({ where: { userId: (0, typeorm_1.In)(userIds) } })
                : [];
            const walletMap = new Map();
            wallets.forEach((wallet) => {
                walletMap.set(wallet.userId, wallet);
            });
            const dtos = tokenAssets.map((tokenAsset) => {
                const dto = this.toDto(tokenAsset);
                const wallet = walletMap.get(tokenAsset.userId);
                if (wallet) {
                    dto.walletBalance = wallet.balance;
                }
                return dto;
            });
            return {
                data: dtos,
                total,
            };
        }
        catch (error) {
            this.logger.error(`Lỗi khi tìm kiếm tài sản token: ${error.message}`, error.stack);
            throw new common_1.InternalServerErrorException(`Không thể tìm kiếm tài sản token: ${error.message}`);
        }
    }
    async findOne(id, relations = []) {
        try {
            this.logger.debug(`Đang tìm tài sản với ID: ${id}`);
            const validatedRelations = this.validateRelations([
                ...relations,
                'product',
                'user',
                'creator',
                'updater',
                'deleter',
            ]);
            const tokenAsset = await this.findByIdOrFail(id, validatedRelations);
            const dto = this.toDto(tokenAsset);
            if (tokenAsset.userId) {
                try {
                    const wallet = await this.walletRepository.findOne({
                        where: { userId: tokenAsset.userId },
                    });
                    if (wallet) {
                        dto.walletBalance = wallet.balance;
                    }
                }
                catch (error) {
                    this.logger.warn(`Không thể lấy thông tin ví của người dùng ${tokenAsset.userId}: ${error.message}`);
                }
            }
            return dto;
        }
        catch (error) {
            this.logger.error(`Lỗi khi tìm tài sản token: ${error.message}`, error.stack);
            if (error instanceof common_1.NotFoundException) {
                throw error;
            }
            throw new common_1.InternalServerErrorException(`Không thể tìm tài sản token: ${error.message}`);
        }
    }
    async findDeleted(params) {
        try {
            const { limit, page } = params;
            this.logger.debug(`Đang tìm tài sản token đã xóa`);
            const query = this.assetRepository
                .createQueryBuilder('tokenAsset')
                .leftJoinAndSelect('tokenAsset.token', 'token')
                .leftJoinAndSelect('tokenAsset.user', 'user')
                .leftJoinAndSelect('tokenAsset.creator', 'creator')
                .leftJoinAndSelect('tokenAsset.updater', 'updater')
                .leftJoinAndSelect('tokenAsset.deleter', 'deleter')
                .where('tokenAsset.isDeleted = :isDeleted', { isDeleted: true })
                .skip((page - 1) * limit)
                .take(limit)
                .orderBy('tokenAsset.createdAt', 'DESC');
            const [tokenAssets, total] = await query.getManyAndCount();
            this.logger.debug(`Đã tìm thấy ${tokenAssets.length} tài sản token đã xóa (tổng: ${total})`);
            const userIds = tokenAssets.map((asset) => asset.userId).filter(Boolean);
            const wallets = userIds.length > 0
                ? await this.walletRepository.find({ where: { userId: (0, typeorm_1.In)(userIds) } })
                : [];
            const walletMap = new Map();
            wallets.forEach((wallet) => {
                walletMap.set(wallet.userId, wallet);
            });
            const dtos = tokenAssets.map((tokenAsset) => {
                const dto = this.toDto(tokenAsset);
                const wallet = walletMap.get(tokenAsset.userId);
                if (wallet) {
                    dto.walletBalance = wallet.balance;
                }
                return dto;
            });
            return {
                data: dtos,
                total,
            };
        }
        catch (error) {
            this.logger.error(`Lỗi khi tìm tài sản token đã xóa: ${error.message}`, error.stack);
            throw new common_1.InternalServerErrorException(`Không thể tìm tài sản token đã xóa: ${error.message}`);
        }
    }
    async count(filter) {
        try {
            this.logger.debug(`Đang đếm tài sản token với filter: ${filter || 'không có'}`);
            const query = this.assetRepository
                .createQueryBuilder('tokenAsset')
                .where('tokenAsset.isDeleted = :isDeleted', { isDeleted: false });
            if (filter) {
                try {
                    const filters = filter.split(',');
                    filters.forEach((filterItem, index) => {
                        const [field, value] = filterItem.split(':');
                        const paramName = `value${index}`;
                        if (field.includes('.')) {
                            const [relation, relationField] = field.split('.');
                            query
                                .leftJoin(`tokenAsset.${relation}`, relation)
                                .andWhere(`${relation}.${relationField} = :${paramName}`, {
                                [paramName]: value,
                            });
                        }
                        else {
                            query.andWhere(`tokenAsset.${field} = :${paramName}`, {
                                [paramName]: value,
                            });
                        }
                    });
                }
                catch (error) {
                    this.logger.warn(`Định dạng lọc không hợp lệ: ${filter}`);
                }
            }
            const count = await query.getCount();
            this.logger.debug(`Đã đếm được ${count} tài sản token`);
            return count;
        }
        catch (error) {
            this.logger.error(`Lỗi khi đếm tài sản token: ${error.message}`, error.stack);
            throw new common_1.InternalServerErrorException(`Không thể đếm tài sản token: ${error.message}`);
        }
    }
    async export(format) {
        try {
            this.logger.debug(`Đang xuất dữ liệu tài sản token với định dạng: ${format}`);
            const tokenAssets = await this.assetRepository.find({
                where: { isDeleted: false },
                relations: ['token', 'user', 'creator', 'updater', 'deleter'],
            });
            const data = tokenAssets.map((tokenAsset) => ({
                id: tokenAsset.id,
                tokenId: tokenAsset.product?.id,
                productName: tokenAsset.product?.productName,
                productCode: tokenAsset.product?.productName,
                userId: tokenAsset.user?.id,
                userEmail: tokenAsset.user?.email,
                userFullName: tokenAsset.user?.fullName,
                amount: tokenAsset.amount,
                createdAt: tokenAsset.createdAt,
                updatedAt: tokenAsset.updatedAt,
                createdBy: tokenAsset.createdBy,
                creatorEmail: tokenAsset.creator?.email,
                creatorFullName: tokenAsset.creator?.fullName,
                updatedBy: tokenAsset.updatedBy,
                updaterEmail: tokenAsset.updater?.email,
                updaterFullName: tokenAsset.updater?.fullName,
                isDeleted: tokenAsset.isDeleted,
                deletedBy: tokenAsset.deletedBy,
                deleterEmail: tokenAsset.deleter?.email,
                deleterFullName: tokenAsset.deleter?.fullName,
            }));
            if (format === 'csv') {
                return data;
            }
            return data;
        }
        catch (error) {
            this.logger.error(`Lỗi khi xuất dữ liệu tài sản token: ${error.message}`, error.stack);
            throw new common_1.InternalServerErrorException(`Không thể xuất dữ liệu tài sản token: ${error.message}`);
        }
    }
};
exports.ReadAssetsService = ReadAssetsService;
exports.ReadAssetsService = ReadAssetsService = __decorate([
    (0, common_1.Injectable)()
], ReadAssetsService);
//# sourceMappingURL=read-assets.service.js.map
import { Logger } from '@nestjs/common';
import { Repository, FindOptionsWhere } from 'typeorm';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { Token } from '../entities/token.entity';
import { TokenDto } from '../dto/token.dto';
export declare class BaseTokensService {
    protected readonly tokenRepository: Repository<Token>;
    protected readonly eventEmitter: EventEmitter2;
    protected readonly logger: Logger;
    protected readonly validRelations: string[];
    protected readonly EVENT_TOKEN_CREATED = "token.created";
    protected readonly EVENT_TOKEN_UPDATED = "token.updated";
    protected readonly EVENT_TOKEN_DELETED = "token.deleted";
    protected readonly EVENT_TOKEN_RESTORED = "token.restored";
    protected readonly EVENT_TOKEN_STATUS_TOGGLED = "token.statusToggled";
    protected readonly EVENT_TOKEN_DUPLICATED = "token.duplicated";
    protected readonly EVENT_TOKEN_CLEANUP = "token.cleanup";
    constructor(tokenRepository: Repository<Token>, eventEmitter: EventEmitter2);
    protected toDto(token: Token): TokenDto;
    protected validateRelations(relations: string[]): string[];
    protected findByIdOrFail(id: string, relations?: string[], withDeleted?: boolean): Promise<Token>;
    protected buildWhereClause(filter?: string): FindOptionsWhere<Token>;
}

"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SlugEcomProductsService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const ecom_products_entity_1 = require("../entity/ecom-products.entity");
const base_slug_service_1 = require("../../common/services/base-slug.service");
const unified_slug_service_1 = require("../../common/services/unified-slug.service");
let SlugEcomProductsService = class SlugEcomProductsService extends base_slug_service_1.BaseSlugService {
    unifiedSlugService;
    constructor(productRepository, unifiedSlugService) {
        super(productRepository, unifiedSlugService);
        this.unifiedSlugService = unifiedSlugService;
    }
    getSlugFieldName() {
        return 'slug';
    }
    getTextFieldName() {
        return 'productName';
    }
    getWhereConditions(excludeId) {
        return { isDeleted: false };
    }
    generateFallbackSlug() {
        return `product-${Date.now()}`;
    }
    generateSlugFromProductName(productName) {
        return this.generateSlugFromText(productName);
    }
    async generateUniqueSlugForCreate(productName, providedSlug) {
        return super.generateUniqueSlugForCreate(productName, providedSlug);
    }
    async generateUniqueSlugForUpdate(productName, currentId, providedSlug, currentSlug) {
        return super.generateUniqueSlugForUpdate(productName, currentId, providedSlug, currentSlug);
    }
    async isSlugExists(slug, excludeId) {
        return super.isSlugExists(slug, excludeId);
    }
    async generateBatchSlugsForProducts(products) {
        return super.generateBatchSlugs(products, (product) => product.productName);
    }
    generateUniqueSlugFromText(text, existingSlugs) {
        const baseSlug = this.generateSlugFromProductName(text);
        return this.unifiedSlugService.generateUniqueSlug(baseSlug, existingSlugs);
    }
    async getExistingSlugs(excludeId) {
        const queryBuilder = this.repository
            .createQueryBuilder('product')
            .select('product.slug')
            .where('product.slug IS NOT NULL')
            .andWhere('product.isDeleted = :isDeleted', { isDeleted: false });
        if (excludeId) {
            queryBuilder.andWhere('product.id != :excludeId', { excludeId });
        }
        const products = await queryBuilder.getMany();
        return products
            .map((product) => product.slug)
            .filter((slug) => Boolean(slug));
    }
    validateSlug(slug) {
        return super.validateSlug(slug);
    }
    sanitizeSlug(slug) {
        return super.sanitizeSlug(slug);
    }
    ensureValidSlug(slug, fallbackText) {
        return super.ensureValidSlug(slug, fallbackText);
    }
    async generateDuplicateSlug(originalProductName, suffix = 'Copy') {
        const newProductName = `${originalProductName} (${suffix})`;
        return this.generateUniqueSlugForCreate(newProductName);
    }
    generateSlugFromProductCode(productCode) {
        if (!productCode || productCode.trim() === '') {
            return this.generateFallbackSlug();
        }
        const baseSlug = this.unifiedSlugService.generateBasicSlug(productCode);
        if (!baseSlug) {
            return this.generateFallbackSlug();
        }
        return baseSlug;
    }
    generateSmartSlug(productName, productCode) {
        if (productName && productName.trim() !== '') {
            return this.generateSlugFromProductName(productName);
        }
        if (productCode && productCode.trim() !== '') {
            return this.generateSlugFromProductCode(productCode);
        }
        return this.generateFallbackSlug();
    }
    async updateMissingSlugs() {
        const productsWithoutSlug = await this.repository
            .createQueryBuilder('product')
            .where('(product.slug IS NULL OR product.slug = :empty)', { empty: '' })
            .andWhere('product.isDeleted = :isDeleted', { isDeleted: false })
            .getMany();
        if (productsWithoutSlug.length === 0) {
            return 0;
        }
        const existingSlugs = await this.getExistingSlugs();
        let updatedCount = 0;
        for (const product of productsWithoutSlug) {
            const newSlug = this.unifiedSlugService.generateUniqueSlug(this.generateSmartSlug(product.productName, product.productCode), [...existingSlugs, ...Array.from({ length: updatedCount }, (_, i) => `temp-${i}`)]);
            await this.repository.update(product.id, { slug: newSlug });
            existingSlugs.push(newSlug);
            updatedCount++;
        }
        return updatedCount;
    }
};
exports.SlugEcomProductsService = SlugEcomProductsService;
exports.SlugEcomProductsService = SlugEcomProductsService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(ecom_products_entity_1.EcomProduct)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        unified_slug_service_1.UnifiedSlugService])
], SlugEcomProductsService);
//# sourceMappingURL=slug.ecom-products.service.js.map
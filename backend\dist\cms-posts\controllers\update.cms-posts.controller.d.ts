import { UpdateCmsPostsService } from '../services/update.cms-posts.service';
import { ReadCmsPostsService } from '../services/read.cms-posts.service';
import { CmsPostDto } from '../dto/cms-post.dto';
import { UpdateCmsPostDto } from '../dto/update.cms-post.dto';
import { CmsPostStatus } from '../entity/cms-posts.entity';
export declare class UpdateCmsPostsController {
    private readonly updateCmsPostsService;
    private readonly readCmsPostsService;
    constructor(updateCmsPostsService: UpdateCmsPostsService, readCmsPostsService: ReadCmsPostsService);
    update(id: string, updateCmsPostDto: UpdateCmsPostDto, userId: string): Promise<CmsPostDto | null>;
    bulkUpdate(updates: Array<{
        id: string;
        data: UpdateCmsPostDto;
    }>, userId: string): Promise<CmsPostDto[]>;
    updateStatus(id: string, status: CmsPostStatus, userId: string): Promise<CmsPostDto | null>;
    publish(id: string, userId: string): Promise<CmsPostDto | null>;
    unpublish(id: string, userId: string): Promise<CmsPostDto | null>;
}

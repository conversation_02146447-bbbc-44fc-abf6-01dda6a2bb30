import { Repository, DataSource } from 'typeorm';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { BaseCmsCustomerFeedbacksService } from './base.cms-customer-feedbacks.service';
import { CmsCustomerFeedbacks } from '../entity/cms-customer-feedbacks.entity';
import { CreateCmsCustomerFeedbackDto } from '../dto/create.cms-customer-feedback.dto';
import { CmsCustomerFeedbackDto } from '../dto/cms-customer-feedback.dto';
export declare class CreateCmsCustomerFeedbacksService extends BaseCmsCustomerFeedbacksService {
    protected readonly feedbackRepository: Repository<CmsCustomerFeedbacks>;
    protected readonly dataSource: DataSource;
    protected readonly eventEmitter: EventEmitter2;
    constructor(feedbackRepository: Repository<CmsCustomerFeedbacks>, dataSource: DataSource, eventEmitter: EventEmitter2);
    create(createDto: CreateCmsCustomerFeedbackDto, userId?: string): Promise<CmsCustomerFeedbackDto>;
    bulkCreate(createDtos: CreateCmsCustomerFeedbackDto[], userId?: string): Promise<CmsCustomerFeedbackDto[]>;
    createFromPublicForm(createDto: CreateCmsCustomerFeedbackDto): Promise<CmsCustomerFeedbackDto>;
    createWithRating(customerName: string, feedbackText: string, rating: number, productServiceName?: string, userId?: string): Promise<CmsCustomerFeedbackDto>;
    createTestimonial(createDto: CreateCmsCustomerFeedbackDto, userId: string): Promise<CmsCustomerFeedbackDto>;
    importFeedbacks(feedbackData: any[], userId: string): Promise<CmsCustomerFeedbackDto[]>;
}

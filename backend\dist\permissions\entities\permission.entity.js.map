{"version": 3, "file": "permission.entity.js", "sourceRoot": "", "sources": ["../../../src/permissions/entities/permission.entity.ts"], "names": [], "mappings": ";;;;;;;;;;;;;AACA,6CAAmE;AAEnE,qDAA8E;AAC9E,qCAKiB;AACjB,wFAA6E;AAC7E,mEAA+D;AAGxD,IAAM,UAAU,GAAhB,MAAM,UAAW,SAAQ,wBAAU;IAiBxC,IAAI,CAAS;IASb,WAAW,CAAgB;IAU3B,WAAW,CAAgB;IAS3B,QAAQ,CAAgB;IAYxB,eAAe,CAAmB;IAKlC,aAAa;QACX,OAAO,aAAa,CAAC;IACvB,CAAC;;;;CAGF,CAAA;AAnEY,gCAAU;AAiBrB;IAfC,IAAA,qBAAW,EAAC;QACX,WAAW,EAAE,uCAAuC;QACpD,OAAO,EAAE,aAAa;QACtB,SAAS,EAAE,GAAG;KACf,CAAC;IACD,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;IACV,IAAA,2BAAS,EAAC,GAAG,CAAC;IACd,IAAA,gBAAM,EAAC;QACN,IAAI,EAAE,SAAS;QACf,MAAM,EAAE,GAAG;QACX,MAAM,EAAE,IAAI;QACZ,QAAQ,EAAE,KAAK;QACf,IAAI,EAAE,MAAM;KACb,CAAC;;wCACW;AASb;IAPC,IAAA,6BAAmB,EAAC;QACnB,WAAW,EAAE,aAAa;QAC1B,OAAO,EAAE,2BAA2B;KACrC,CAAC;IACD,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;IACV,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAE,IAAI,EAAE,aAAa,EAAE,CAAC;;+CACnC;AAU3B;IARC,IAAA,6BAAmB,EAAC;QACnB,WAAW,EAAE,qCAAqC;QAClD,OAAO,EAAE,cAAc;KACxB,CAAC;IACD,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;IACV,IAAA,2BAAS,EAAC,GAAG,CAAC;IACd,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,SAAS,EAAE,MAAM,EAAE,GAAG,EAAE,QAAQ,EAAE,IAAI,EAAE,IAAI,EAAE,cAAc,EAAE,CAAC;;+CACpD;AAS3B;IAPC,IAAA,6BAAmB,EAAC;QACnB,WAAW,EAAE,kBAAkB;QAC/B,OAAO,EAAE,sCAAsC;KAChD,CAAC;IACD,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;IACV,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAE,IAAI,EAAE,WAAW,EAAE,CAAC;;4CACpC;AAYxB;IARC,IAAA,mBAAS,EACR,GAAG,EAAE,CAAC,uCAAc,EACpB,CAAC,cAAc,EAAE,EAAE,CAAC,cAAc,CAAC,UAAU,CAC9C;IACA,IAAA,qBAAW,EAAC;QACX,IAAI,EAAE,GAAG,EAAE,CAAC,CAAC,uCAAc,CAAC;QAC5B,WAAW,EAAE,0BAA0B;KACxC,CAAC;;mDACgC;qBAzDvB,UAAU;IADtB,IAAA,gBAAM,EAAC,aAAa,CAAC;GACT,UAAU,CAmEtB"}
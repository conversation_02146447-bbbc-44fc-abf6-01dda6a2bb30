import { UserDto } from '../../users/dto/user.dto';
import { CmsBannerStatus, CmsBannerLocation } from '../entity/cms-banners.entity';
export declare class CmsBannerDto {
    id: string;
    businessCode: string;
    title: string;
    imageUrlDesktop: string;
    imageUrlMobile?: string | null;
    linkUrl?: string | null;
    altText?: string | null;
    displayOrder: number;
    status: CmsBannerStatus;
    location?: CmsBannerLocation | null;
    startDate?: Date | null;
    endDate?: Date | null;
    createdAt: Date;
    updatedAt: Date;
    createdBy?: string;
    updatedBy?: string;
    deletedBy?: string;
    isDeleted: boolean;
    deletedAt?: Date;
    creator?: UserDto;
    updater?: UserDto;
    deleter?: UserDto;
    isActive?: boolean;
}

"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UpdateCmsTagsController = void 0;
const openapi = require("@nestjs/swagger");
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const jwt_auth_guard_1 = require("../../common/guards/jwt-auth.guard");
const update_cms_tags_service_1 = require("../services/update.cms-tags.service");
const read_cms_tags_service_1 = require("../services/read.cms-tags.service");
const update_cms_tag_dto_1 = require("../dto/update.cms-tag.dto");
const api_response_dto_1 = require("../../common/dto/api-response.dto");
const get_user_decorator_1 = require("../../common/decorators/get-user.decorator");
const roles_decorator_1 = require("../../common/decorators/roles.decorator");
const permissions_decorator_1 = require("../../common/decorators/permissions.decorator");
let UpdateCmsTagsController = class UpdateCmsTagsController {
    updateCmsTagsService;
    readCmsTagsService;
    constructor(updateCmsTagsService, readCmsTagsService) {
        this.updateCmsTagsService = updateCmsTagsService;
        this.readCmsTagsService = readCmsTagsService;
    }
    async update(id, updateCmsTagDto, userId) {
        const existingTag = await this.readCmsTagsService.findOne(id);
        if (!existingTag) {
            throw new common_1.NotFoundException(`Không tìm thấy thẻ CMS với ID: ${id}`);
        }
        const result = await this.updateCmsTagsService.update(id, updateCmsTagDto, userId);
        if (!result) {
            throw new common_1.NotFoundException(`Không tìm thấy thẻ CMS với ID: ${id}`);
        }
        return result;
    }
    async bulkUpdate(updates, userId) {
        return this.updateCmsTagsService.bulkUpdate(updates, userId);
    }
    async updateSlugFromName(id, userId) {
        const result = await this.updateCmsTagsService.updateSlugFromName(id, userId);
        if (!result) {
            throw new common_1.NotFoundException(`Không tìm thấy thẻ CMS với ID: ${id}`);
        }
        return result;
    }
    async updateNameAndSlug(id, name, userId) {
        const result = await this.updateCmsTagsService.updateNameAndSlug(id, name, userId);
        if (!result) {
            throw new common_1.NotFoundException(`Không tìm thấy thẻ CMS với ID: ${id}`);
        }
        return result;
    }
};
exports.UpdateCmsTagsController = UpdateCmsTagsController;
__decorate([
    (0, common_1.Patch)(':id'),
    (0, roles_decorator_1.Roles)('ADMIN', 'EDITOR'),
    (0, permissions_decorator_1.Permissions)('cms-tag:update'),
    (0, swagger_1.ApiOperation)({ summary: 'Cập nhật thông tin thẻ CMS' }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Thẻ CMS đã được cập nhật thành công.',
        type: api_response_dto_1.ApiResponseDto,
        schema: {
            properties: {
                data: { $ref: '#/components/schemas/CmsTagDto' },
            },
        },
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.NOT_FOUND,
        description: 'Không tìm thấy thẻ CMS.',
        type: api_response_dto_1.ApiResponseDto,
        schema: { properties: { data: { type: 'object', nullable: true } } },
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.BAD_REQUEST,
        description: 'Dữ liệu đầu vào không hợp lệ.',
        type: api_response_dto_1.ApiResponseDto,
        schema: { properties: { data: { type: 'object', nullable: true } } },
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.CONFLICT,
        description: 'Tên hoặc slug đã tồn tại.',
        type: api_response_dto_1.ApiResponseDto,
        schema: { properties: { data: { type: 'object', nullable: true } } },
    }),
    (0, swagger_1.ApiParam)({ name: 'id', type: String, description: 'ID của thẻ CMS' }),
    (0, swagger_1.ApiBody)({ type: update_cms_tag_dto_1.UpdateCmsTagDto }),
    openapi.ApiResponse({ status: 200, type: Object }),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __param(1, (0, common_1.Body)()),
    __param(2, (0, get_user_decorator_1.GetUser)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, update_cms_tag_dto_1.UpdateCmsTagDto, String]),
    __metadata("design:returntype", Promise)
], UpdateCmsTagsController.prototype, "update", null);
__decorate([
    (0, common_1.Patch)('bulk'),
    (0, roles_decorator_1.Roles)('ADMIN'),
    (0, permissions_decorator_1.Permissions)('cms-tag:update'),
    (0, swagger_1.ApiOperation)({ summary: 'Cập nhật nhiều thẻ CMS cùng lúc' }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Các thẻ CMS đã được cập nhật thành công.',
        type: api_response_dto_1.ApiResponseDto,
        schema: {
            properties: {
                data: {
                    type: 'array',
                    items: { $ref: '#/components/schemas/CmsTagDto' },
                },
            },
        },
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.BAD_REQUEST,
        description: 'Dữ liệu đầu vào không hợp lệ.',
        type: api_response_dto_1.ApiResponseDto,
        schema: { properties: { data: { type: 'object', nullable: true } } },
    }),
    (0, swagger_1.ApiBody)({
        schema: {
            type: 'array',
            items: {
                type: 'object',
                properties: {
                    id: { type: 'string', format: 'uuid' },
                    data: { $ref: '#/components/schemas/UpdateCmsTagDto' },
                },
                required: ['id', 'data'],
            },
        },
    }),
    openapi.ApiResponse({ status: 200, type: [require("../dto/cms-tag.dto").CmsTagDto] }),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, get_user_decorator_1.GetUser)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Array, String]),
    __metadata("design:returntype", Promise)
], UpdateCmsTagsController.prototype, "bulkUpdate", null);
__decorate([
    (0, common_1.Patch)(':id/slug-from-name'),
    (0, roles_decorator_1.Roles)('ADMIN', 'EDITOR'),
    (0, permissions_decorator_1.Permissions)('cms-tag:update'),
    (0, swagger_1.ApiOperation)({ summary: 'Cập nhật slug từ tên thẻ CMS' }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Slug thẻ CMS đã được cập nhật từ tên thành công.',
        type: api_response_dto_1.ApiResponseDto,
        schema: {
            properties: {
                data: { $ref: '#/components/schemas/CmsTagDto' },
            },
        },
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.NOT_FOUND,
        description: 'Không tìm thấy thẻ CMS.',
        type: api_response_dto_1.ApiResponseDto,
        schema: { properties: { data: { type: 'object', nullable: true } } },
    }),
    (0, swagger_1.ApiParam)({ name: 'id', type: String, description: 'ID của thẻ CMS' }),
    openapi.ApiResponse({ status: 200, type: Object }),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __param(1, (0, get_user_decorator_1.GetUser)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String]),
    __metadata("design:returntype", Promise)
], UpdateCmsTagsController.prototype, "updateSlugFromName", null);
__decorate([
    (0, common_1.Patch)(':id/name-and-slug'),
    (0, roles_decorator_1.Roles)('ADMIN', 'EDITOR'),
    (0, permissions_decorator_1.Permissions)('cms-tag:update'),
    (0, swagger_1.ApiOperation)({ summary: 'Cập nhật tên và tự động tạo slug mới cho thẻ CMS' }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Tên và slug của thẻ CMS đã được cập nhật thành công.',
        type: api_response_dto_1.ApiResponseDto,
        schema: {
            properties: {
                data: { $ref: '#/components/schemas/CmsTagDto' },
            },
        },
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.NOT_FOUND,
        description: 'Không tìm thấy thẻ CMS.',
        type: api_response_dto_1.ApiResponseDto,
        schema: { properties: { data: { type: 'object', nullable: true } } },
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.CONFLICT,
        description: 'Tên thẻ đã tồn tại.',
        type: api_response_dto_1.ApiResponseDto,
        schema: { properties: { data: { type: 'object', nullable: true } } },
    }),
    (0, swagger_1.ApiParam)({ name: 'id', type: String, description: 'ID của thẻ CMS' }),
    (0, swagger_1.ApiBody)({
        schema: {
            type: 'object',
            properties: {
                name: {
                    type: 'string',
                    description: 'Tên mới của thẻ',
                    example: 'Thị trường vàng cập nhật',
                },
            },
            required: ['name'],
        },
    }),
    openapi.ApiResponse({ status: 200, type: Object }),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __param(1, (0, common_1.Body)('name')),
    __param(2, (0, get_user_decorator_1.GetUser)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String, String]),
    __metadata("design:returntype", Promise)
], UpdateCmsTagsController.prototype, "updateNameAndSlug", null);
exports.UpdateCmsTagsController = UpdateCmsTagsController = __decorate([
    (0, swagger_1.ApiTags)('cms-tags'),
    (0, swagger_1.ApiBearerAuth)(),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, common_1.Controller)('cms/tags'),
    __metadata("design:paramtypes", [update_cms_tags_service_1.UpdateCmsTagsService,
        read_cms_tags_service_1.ReadCmsTagsService])
], UpdateCmsTagsController);
//# sourceMappingURL=update.cms-tags.controller.js.map
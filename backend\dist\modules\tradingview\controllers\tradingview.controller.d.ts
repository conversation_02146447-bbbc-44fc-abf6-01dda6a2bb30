import { TradingViewApiService } from '../services/tradingview-api.service';
import { RateLimiterService } from '../services/rate-limiter.service';
import { CurrentPrice, ChartData, ChartInterval } from '../models/tradingview-data.model';
export declare class TradingViewController {
    private readonly tradingViewApiService;
    private readonly rateLimiterService;
    private readonly logger;
    constructor(tradingViewApiService: TradingViewApiService, rateLimiterService: RateLimiterService);
    getSilverPrice(): Promise<CurrentPrice>;
    getSilverChart(interval?: ChartInterval, from?: number, to?: number, limit?: number): Promise<ChartData>;
}

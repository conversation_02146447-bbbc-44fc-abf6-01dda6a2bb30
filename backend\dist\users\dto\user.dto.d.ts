import { RoleDto } from '../../roles/dto/role.dto';
import { AssetDto } from '../../token-assets/dto/asset.dto';
import { User } from '../entities/user.entity';
export declare class UserDto {
    id: string;
    username: string;
    email: string;
    fullName?: string;
    phone?: string;
    address?: string;
    bio?: string;
    birthday?: Date;
    isActive: boolean;
    isKycVerified: boolean;
    twoFaEnabled: boolean;
    notificationEmail: boolean;
    notificationSms: boolean;
    createdAt: Date;
    updatedAt: Date;
    referralCode?: string;
    parentId?: string;
    path?: string;
    isDeleted: boolean;
    emailVerified: boolean;
    isAgent: boolean;
    roles?: RoleDto[];
    tokenAssets?: AssetDto[];
    createdBy?: string;
    creator?: User;
    updatedBy?: string;
    updater?: User;
    deletedBy?: string;
    deleter?: User;
}

import { OnGatewayConnection, OnGatewayDisconnect, OnGatewayInit } from '@nestjs/websockets';
import { Server, Socket } from 'socket.io';
import { ConfigService } from '@nestjs/config';
import { JwtService } from '@nestjs/jwt';
import { WebSocketEmitterService } from '../services/websocket-emitter.service';
export declare class BaseGateway implements OnGatewayInit, OnGatewayConnection, OnGatewayDisconnect {
    private readonly configService;
    private readonly wsEmitterService;
    private readonly jwtService;
    server: Server;
    private readonly logger;
    constructor(configService: ConfigService, wsEmitterService: WebSocketEmitterService, jwtService: JwtService);
    afterInit(server: Server): void;
    handleConnection(client: Socket, ...args: any[]): Promise<void>;
    handleDisconnect(client: Socket): void;
    handlePrivateMessage(client: Socket, payload: any): void;
    handlePublicPing(client: Socket, payload: any): {
        event: string;
        data: any;
    };
    handleJoinMyRoom(client: Socket): void;
    private extractToken;
}

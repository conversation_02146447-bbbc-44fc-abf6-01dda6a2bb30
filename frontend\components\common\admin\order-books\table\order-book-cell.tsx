import { UserHoverCard } from "@/components/common/user/user-hover-card"
import { Badge } from "@/components/ui/badge"
import { But<PERSON> } from "@/components/ui/button"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle
} from "@/components/ui/dialog"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuShortcut,
  DropdownMenuTrigger
} from "@/components/ui/dropdown-menu"
import {
  Popover,
  PopoverContent,
  PopoverTrigger
} from "@/components/ui/popover"

import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
} from "@/components/ui/command"
import { DateTimeDisplay } from "@/components/ui/date-time-display"
import { api } from '@/lib/api'
import { cn } from "@/lib/utils"
import { ForexQuote } from '@/services/websocket/polygon-forex-socket.service'
import { type ColumnDef, Row } from "@tanstack/react-table"
import { format, isValid } from "date-fns"
import { vi } from "date-fns/locale"
import {
  ArrowUpDown,
  CalendarPlus,
  Check,
  ChevronsUpDown,
  Edit,
  Eye,
  MoreVertical,
  Trash2,
  User,
  X
} from "lucide-react"
import { useState } from "react"
import { toast } from "sonner"
import { ApproveStatus, BusinessType, OrderBook, OrderStatus, OrderType } from "../type/order-books"

// Format currency with VND
const formatCurrency = (amount: number | undefined) => {
  if (amount === undefined || amount === null) return "---"
  return new Intl.NumberFormat('vi-VN', {
    maximumFractionDigits: 0
  }).format(amount)
}

// Format date from ISO string
const formatDate = (dateStr: string | null) => {
  if (!dateStr) return "---"
  const date = new Date(dateStr)
  if (!isValid(date)) return "Không hợp lệ"
  try {
    return format(date, "dd/MM/yyyy HH:mm", { locale: vi })
  } catch (error) {
    return "Không hợp lệ"
  }
}

// Get color for order status badge
const getStatusColor = (status: OrderStatus) => {
  switch (status) {
    case OrderStatus.COMPLETED:
      return 'bg-green-100 text-green-800 font-normal';
    case OrderStatus.DEPOSITED:
      return 'bg-purple-100 text-purple-800 font-normal';
    case OrderStatus.TERMINATED:
      return 'bg-red-100 text-red-800 font-normal';
    case OrderStatus.CANCELLED:
      return 'bg-orange-100 text-orange-800 font-normal';
    default:
      return 'bg-gray-100 text-gray-800 font-normal';
  }
};

// Order status in Vietnamese
const getStatusName = (status: OrderStatus) => {
  switch (status) {
    case OrderStatus.COMPLETED:
      return 'Đã tất toán';
    case OrderStatus.DEPOSITED:
      return 'Đã ký quỹ';
    case OrderStatus.TERMINATED:
      return 'Đã cắt hợp đồng';
    case OrderStatus.CANCELLED:
      return 'Đã hủy';
    default:
      return status;
  }
};

// Order type in Vietnamese
const getOrderTypeName = (type: OrderType) => {
  switch (type) {
    case OrderType.BUY:
      return 'Mua';
    case OrderType.SELL:
      return 'Bán';
    default:
      return type;
  }
};

// Get color for order type
const getOrderTypeColor = (type: OrderType) => {
  switch (type) {
    case OrderType.BUY:
      return 'bg-green-100 text-green-800 font-normal';
    case OrderType.SELL:
      return 'bg-red-100 text-red-800 font-normal';
    default:
      return 'bg-gray-100 text-gray-800 font-normal';
  }
};

// Get color for business type
const getBusinessTypeColor = (type: BusinessType | undefined) => {
  switch (type) {
    case BusinessType.NORMAL:
      return 'bg-orange-100 text-orange-800 font-normal';
    case BusinessType.IMMEDIATE_DELIVERY:
      return 'bg-blue-100 text-blue-800 font-normal';
    default:
      return 'bg-gray-100 text-gray-800 font-normal';
  }
};

// Get color for approve status badge
const getApproveStatusColor = (status: ApproveStatus | undefined) => {
  switch (status) {
    case ApproveStatus.PENDING:
      return 'bg-yellow-100 text-yellow-800 font-normal';
    case ApproveStatus.APPROVED:
      return 'bg-green-100 text-green-800 font-normal';
    default:
      return 'bg-gray-100 text-gray-800 font-normal';
  }
};

// Approve status in Vietnamese
const getApproveStatusName = (status: ApproveStatus | undefined) => {
  switch (status) {
    case ApproveStatus.PENDING:
      return 'Chờ phê duyệt';
    case ApproveStatus.APPROVED:
      return 'Đã phê duyệt';
    default:
      return '---';
  }
};

// Add helper function to calculate real-time total price
const calculateRealTimePrice = (order: OrderBook, currentQuote: ForexQuote | null) => {
  if (!currentQuote || order.status === OrderStatus.COMPLETED) {
    return order.totalPrice || 0;
  }

  // Get the appropriate price based on order type
  const price = order.orderType === OrderType.BUY ? currentQuote.askPrice : currentQuote.bidPrice;
  
  // Calculate total volume and price
  const totalVolume = order.details?.reduce((sum, detail) => {
    const volume = detail.quantity || 0;
    const weight = detail.product?.weight || 1;
    return sum + (volume * weight);
  }, 0) || 0;

  // Calculate new total price
  const basePrice = price * totalVolume * 1.2056 * 27000;
  return Math.round(basePrice + (basePrice * 0.11)); // Add 11% VAT
};

// Add helper function to calculate real-time settlement amount
const calculateRealTimeSettlement = (order: OrderBook, currentQuote: ForexQuote | null) => {
  if (!currentQuote || order.status === OrderStatus.COMPLETED) {
    return order.settlementPrice;
  }

  const totalPrice = calculateRealTimePrice(order, currentQuote);
  
  // For physical orders, settlement is 100%
  if (order.businessType === BusinessType.IMMEDIATE_DELIVERY) {
    return totalPrice;
  }
  
  // For short investment, settlement is remaining balance (90% if not paid)
  return Math.round(totalPrice * 0.9);
};

// Add helper function to get price color based on order type
const getPriceColor = (order: OrderBook) => {
  if (order.status === OrderStatus.COMPLETED) return "";
  return order.orderType === OrderType.BUY ? "text-green-500" : "text-red-500";
};

interface ActionsProps {
  row: Row<OrderBook>
  onViewDetail: (order: OrderBook) => void
  onDelete: (order: OrderBook) => void
  onEdit: (order: OrderBook) => void
  onChangeStatus: (order: OrderBook, status: OrderStatus) => void
  onExtendSettlement?: (order: OrderBook) => void
}

function Actions({ row, onViewDetail, onDelete, onEdit, onChangeStatus, onExtendSettlement }: ActionsProps) {
  const [showDeleteDialog, setShowDeleteDialog] = useState(false)
  const [showExtendDialog, setShowExtendDialog] = useState(false)
  const order = row.original

  const handleDelete = (order: OrderBook) => {
    onDelete(order)
    setShowDeleteDialog(false)
  }

  // Xử lý khi click vào nút xem chi tiết
  const handleViewDetail = async (order: OrderBook) => {
    try {
      // Sử dụng api client để gọi API
      const response = await api.get<OrderBook>(`order-books/${order.id}`);

      // Chỉ truyền dữ liệu chi tiết nếu API trả về thành công
      if (response) {
        onViewDetail(response);
      } else {
        // Nếu không có response, hiển thị thông báo lỗi
        toast.error("Không thể tải thông tin chi tiết lệnh. Vui lòng thử lại sau.");
      }
    } catch (error) {
      console.error('Error fetching order detail:', error);
      // Hiển thị thông báo lỗi cho người dùng
      toast.error("Có lỗi xảy ra khi tải thông tin lệnh. Vui lòng thử lại sau.");
    }
  }

  // Xử lý khi click vào nút chỉnh sửa
  const handleEdit = async (order: OrderBook) => {
    try {
      // Sử dụng api client để gọi API lấy dữ liệu đầy đủ giống như chi tiết
      const response = await api.get<OrderBook>(`order-books/${order.id}`);

      // Chỉ truyền dữ liệu chi tiết nếu API trả về thành công
      if (response) {
        // Chuẩn bị dữ liệu cho form chỉnh sửa
        // Chuyển đổi OrderType sang type cho form
        let formType = 'buy';
        if (response.orderType === OrderType.SELL) {
          formType = 'sell';
        } else if (response.orderType === OrderType.WITHDRAWAL) {
          formType = 'withdrawal';
        }

        // Gán type vào response để form có thể sử dụng
        const enhancedResponse = {
          ...response,
          type: formType,
          // Không gửi remainingVolume để lược bỏ trường này theo yêu cầu
          remainingVolume: null,
          // Để trống giá thị trường theo yêu cầu
          price: null
        };

        onEdit(enhancedResponse);
      } else {
        // Nếu không có response, hiển thị thông báo lỗi
        toast.error("Không thể tải thông tin chi tiết đơn hàng. Vui lòng thử lại sau.");
      }
    } catch (error) {
      console.error('Error fetching order detail for edit:', error);
      // Hiển thị thông báo lỗi cho người dùng
      toast.error("Có lỗi xảy ra khi tải thông tin đơn hàng. Vui lòng thử lại sau.");
    }
  }

  // Kiểm tra xem lệnh có thể gia hạn hay không
  const canExtend = () => {

    // Chỉ cho phép gia hạn lệnh mua/bán đang ở trạng thái WAIT_PAYMENT
    return (
      order.status === OrderStatus.DEPOSITED &&
      order.settlementDeadline // Phải có hạn tất toán
    )
  }

  return (
    <>
      {/* Dialog xác nhận xóa */}
      <Dialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Xác nhận xóa</DialogTitle>
            <DialogDescription>
              Bạn có chắc chắn muốn xóa lệnh <span className="font-medium">{order.id}</span>?
              <p className="mt-2">Hành động này không thể hoàn tác.</p>
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowDeleteDialog(false)}>Hủy</Button>
            <Button variant="destructive" onClick={() => handleDelete(order)}>Xóa</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Dialog xác nhận gia hạn */}
      <Dialog open={showExtendDialog} onOpenChange={setShowExtendDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Xác nhận gia hạn</DialogTitle>
            <DialogDescription>
              Bạn có chắc chắn muốn gia hạn thời gian tất toán thêm 15 ngày cho lệnh này?
              <p className="mt-2">Phí gia hạn là 100,000 VND và sẽ được trừ từ số dư của người dùng.</p>
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowExtendDialog(false)}>Hủy</Button>
            <Button
              onClick={() => {
                if (onExtendSettlement) {
                  onExtendSettlement(order);
                }
                setShowExtendDialog(false);
              }}
            >
              Xác nhận gia hạn
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      <div className="flex justify-end px-1 sticky-action-cell h-full items-center">
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button
              variant="ghost"
              size="sm"
              className="flex h-6 w-6 p-0 data-[state=open]:bg-muted transition-colors hover:bg-muted"
            >
              <MoreVertical className="h-3 w-3" />
              <span className="sr-only">Mở menu</span>
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end" className="p-1">
            <DropdownMenuItem onClick={() => handleViewDetail(order)}>
              <Eye className="mr-2 h-3.5 w-3.5" />
              <span className="flex-1 text-sm">Chi tiết</span>
              <DropdownMenuShortcut className="text-sm">⌘V</DropdownMenuShortcut>
            </DropdownMenuItem>
            <DropdownMenuItem onClick={() => handleEdit(order)}>
              <Edit className="mr-2 h-3.5 w-3.5" />
              <span className="flex-1 text-sm">Chỉnh sửa</span>
              <DropdownMenuShortcut className="text-sm">⌘E</DropdownMenuShortcut>
            </DropdownMenuItem>

            <DropdownMenuSeparator />

            {/* Status change options */}
            {order.status !== OrderStatus.COMPLETED && order.status !== OrderStatus.TERMINATED && (
              <>
                {/* Tất toán hợp đồng - Hiển thị modal tất toán cho tất cả các loại */}
                <DropdownMenuItem onClick={() => onChangeStatus(order, OrderStatus.COMPLETED)}>
                  <Check className="mr-2 h-3.5 w-3.5 text-green-500" />
                  <span className="flex-1 text-sm text-green-500">Tất toán hợp đồng</span>
                </DropdownMenuItem>

                <DropdownMenuItem
                  onClick={() => onChangeStatus(order, OrderStatus.TERMINATED)}
                >
                  <X className="mr-2 h-3.5 w-3.5 text-red-500" />
                  <span className="flex-1 text-sm text-red-500">Cắt hợp đồng</span>
                </DropdownMenuItem>

                {/* Gia hạn option */}
                {canExtend() && onExtendSettlement && (
                  <DropdownMenuItem onClick={() => setShowExtendDialog(true)}>
                    <CalendarPlus className="mr-2 h-3.5 w-3.5 text-blue-500" />
                    <span className="flex-1 text-sm text-blue-500">Gia hạn thời gian</span>
                  </DropdownMenuItem>
                )}

                <DropdownMenuSeparator />
              </>
            )}

            <DropdownMenuItem
              onClick={() => setShowDeleteDialog(true)}
              className="text-destructive focus:text-destructive"
            >
              <Trash2 className="mr-2 h-3.5 w-3.5 text-destructive" />
              <span className="flex-1 text-sm text-destructive">Xóa</span>
              <DropdownMenuShortcut className="text-sm text-destructive">⌫</DropdownMenuShortcut>
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>
    </>
  )
}

// Status selector for changing order status
interface OrderStatusSelectorProps {
  status: OrderStatus;
  onStatusChange: (status: OrderStatus) => void;
  disabled?: boolean;
}

export function OrderStatusSelector({ status, onStatusChange, disabled = false }: OrderStatusSelectorProps) {
  const [open, setOpen] = useState<boolean>(false);

  // Tất cả các trạng thái có thể có
  const statusOptions = [
    { value: OrderStatus.COMPLETED as string, label: 'Đã tất toán', color: 'bg-green-100 text-green-800 font-normal' },
    { value: OrderStatus.TERMINATED as string, label: 'Đã cắt hợp đồng', color: 'bg-red-100 text-red-800 font-normal' },
    { value: OrderStatus.DEPOSITED as string, label: 'Đã ký quỹ', color: 'bg-purple-100 text-purple-800 font-normal' },
    { value: OrderStatus.CANCELLED as string, label: 'Đã hủy', color: 'bg-gray-100 text-gray-800 font-normal' },

  ];

  const currentStatus = statusOptions.find((option) => option.value === status) || statusOptions[0];

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <Button
          variant="ghost"
          role="combobox"
          aria-expanded={open}
          disabled={disabled}
          className="justify-between h-auto p-1 hover:bg-transparent"
        >
          <Badge className={currentStatus.color}>
            {currentStatus.label}
          </Badge>
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-[200px] p-0">
        <Command>
          <CommandInput placeholder="Tìm trạng thái..." />
          <CommandEmpty>Không tìm thấy trạng thái</CommandEmpty>
          <CommandGroup>
            {statusOptions.map((option) => (
              <CommandItem
                key={option.value}
                value={option.value}
                onSelect={() => {
                  onStatusChange(option.value as OrderStatus);
                  setOpen(false);
                }}
              >
                <Check
                  className={cn(
                    "mr-2 h-4 w-4",
                    status === option.value ? "opacity-100" : "opacity-0"
                  )}
                />
                <Badge className={option.color}>
                  {option.label}
                </Badge>
              </CommandItem>
            ))}
          </CommandGroup>
        </Command>
      </PopoverContent>
    </Popover>
  );
}

// Approve status selector for changing approve status
interface ApproveStatusSelectorProps {
  approveStatus: ApproveStatus | undefined;
  onApproveStatusChange: (status: ApproveStatus) => void;
  disabled?: boolean;
}

export function ApproveStatusSelector({ approveStatus, onApproveStatusChange, disabled = false }: ApproveStatusSelectorProps) {
  const [open, setOpen] = useState<boolean>(false);

  // Các trạng thái phê duyệt có thể có
  const approveStatusOptions = [
    { value: ApproveStatus.PENDING as string, label: 'Chờ phê duyệt', color: 'bg-yellow-100 text-yellow-800 font-normal' },
    { value: ApproveStatus.APPROVED as string, label: 'Đã phê duyệt', color: 'bg-green-100 text-green-800 font-normal' },
  ];

  const currentStatus = approveStatusOptions.find((option) => option.value === approveStatus) ||
    { value: undefined, label: '---', color: 'bg-gray-100 text-gray-800 font-normal' };

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <Button
          variant="ghost"
          role="combobox"
          aria-expanded={open}
          disabled={disabled}
          className="justify-between h-auto p-1 hover:bg-transparent"
        >
          <Badge className={currentStatus.color}>
            {currentStatus.label}
          </Badge>
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-[200px] p-0">
        <Command>
          <CommandInput placeholder="Tìm trạng thái..." />
          <CommandEmpty>Không tìm thấy trạng thái</CommandEmpty>
          <CommandGroup>
            {approveStatusOptions.map((option) => (
              <CommandItem
                key={option.value}
                value={option.value}
                onSelect={() => {
                  onApproveStatusChange(option.value as ApproveStatus);
                  setOpen(false);
                }}
              >
                <Check
                  className={cn(
                    "mr-2 h-4 w-4",
                    approveStatus === option.value ? "opacity-100" : "opacity-0"
                  )}
                />
                <Badge className={option.color}>
                  {option.label}
                </Badge>
              </CommandItem>
            ))}
          </CommandGroup>
        </Command>
      </PopoverContent>
    </Popover>
  );
}


interface OrderBookColumnsProps {
  onViewDetail: (order: OrderBook) => void
  onDelete: (order: OrderBook) => void
  onEdit: (order: OrderBook) => void
  onChangeStatus: (order: OrderBook, status: OrderStatus) => void
  onExtendSettlement?: (order: OrderBook) => void
  onChangeApproveStatus?: (order: OrderBook, approveStatus: ApproveStatus) => void
  currentQuote?: ForexQuote | null
}

export function getOrderBookColumns({
  onViewDetail,
  onDelete,
  onEdit,
  onChangeStatus,
  onExtendSettlement,
  onChangeApproveStatus,
  currentQuote
}: OrderBookColumnsProps): ColumnDef<OrderBook>[] {
  return [
    {
      accessorKey: "contractNumber",
      header: ({ column }) => {
        return (
          <Button
            variant="ghost"
            size="sm"
            onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
            className="flex items-center gap-1 px-0 h-6 py-0"
            data-column-id="contractNumber"
          >
            <span className="text-xs">Số hợp đồng</span>
            <ArrowUpDown className="ml-1 h-3 w-3" />
          </Button>
        )
      },
      meta: {
        header: "Số hợp đồng"
      },
      cell: ({ row }) => {
        const contractNumber = row.original.contractNumber;
        return (
          <div className="text-sm">
            {contractNumber || "---"}
          </div>
        )
      },
      enableSorting: true,
    },
    {
      accessorKey: "orderType",
      header: ({ column }) => {
        return (
          <Button
            variant="ghost"
            size="sm"
            onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
            className="flex items-center gap-1 px-0 h-6 py-0"
            data-column-id="orderType"
          >
            <span className="text-xs">Loại lệnh</span>
            <ArrowUpDown className="ml-1 h-3 w-3" />
          </Button>
        )
      },
      meta: {
        header: "Loại lệnh"
      },
      cell: ({ row }) => {
        const type = row.getValue("orderType") as OrderType
        return (
          <Badge className={getOrderTypeColor(type)}>
            {getOrderTypeName(type)}
          </Badge>
        )
      },
      enableSorting: true,
    },
    {
      accessorKey: "businessType",
      header: ({ column }) => {
        return (
          <Button
            variant="ghost"
            size="sm"
            onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
            className="flex items-center gap-1 px-0 h-6 py-0"
            data-column-id="businessType"
          >
            <span className="text-xs">Loại hình giao dịch</span>
            <ArrowUpDown className="ml-1 h-3 w-3" />
          </Button>
        )
      },
      meta: {
        header: "Loại hình giao dịch"
      },
      cell: ({ row }) => {
        const businessType = row.getValue("businessType") as BusinessType | undefined

        if (!businessType || row.original.orderType !== OrderType.BUY) {
          return <span className="text-xs text-muted-foreground">---</span>
        }

        return (
          <Badge className={getBusinessTypeColor(businessType)}>
            {businessType === BusinessType.NORMAL ? "Bạc online ký quỹ" : "Bạc giao ngay"}
          </Badge>
        )
      },
      enableSorting: true,
    },
    {
      accessorKey: "totalPrice",
      header: ({ column }) => {
        return (
          <Button
            variant="ghost"
            size="sm"
            onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
            className="flex items-center gap-1 px-0 h-6 py-0"
            data-column-id="totalPrice"
          >
            <span className="text-xs">Tổng giá trị</span>
            <ArrowUpDown className="ml-1 h-3 w-3" />
          </Button>
        )
      },
      meta: {
        header: "Tổng giá trị"
      },
      cell: ({ row }) => {
        const order = row.original;
        const totalPrice = calculateRealTimePrice(order, currentQuote || null);

        return (
          <div className="flex items-center gap-1 text-sm">
            <span className={getPriceColor(order)}>
              {formatCurrency(totalPrice)} VND
            </span>
          </div>
        )
      },
      enableSorting: true,
    },
    {
      accessorKey: "processingPrice",
      header: ({ column }) => {
        return (
          <Button
            variant="ghost"
            size="sm"
            onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
            className="flex items-center gap-1 px-0 h-6 py-0"
            data-column-id="processingPrice"
          >
            <span className="text-xs">Phí gia công</span>
            <ArrowUpDown className="ml-1 h-3 w-3" />
          </Button>
        )
      },
      meta: {
        header: "Phí gia công"
      },
      cell: ({ row }) => {
        const order = row.original;
        const processingPrice = order.processingPrice;

        return (
          <div className="flex items-center gap-1 text-sm">
            <span>
              {processingPrice ? formatCurrency(processingPrice) : "0"} VND
            </span>
          </div>
        )
      },
      enableSorting: true,
    },
    {
      id: "totalVolume",
      header: ({ column }) => {
        return (
          <Button
            variant="ghost"
            size="sm"
            className="flex items-center gap-1 px-0 h-6 py-0"
            data-column-id="totalVolume"
          >
            <span className="text-xs">Tổng khối lượng</span>
          </Button>
        )
      },
      meta: {
        header: "Tổng khối lượng"
      },
      cell: ({ row }) => {
        const order = row.original;
        // Tính tổng khối lượng bằng cách nhân số lượng với oz của token
        const totalVolume = order.details?.reduce((sum, detail) => {
          const volume = detail.quantity || 0;
          const weight = detail.product?.weight || 1;
          return sum + (volume * weight);
        }, 0) || 0;
        return (
          <div className="text-sm">
            {totalVolume.toLocaleString()}
          </div>
        )
      },
      enableSorting: false,
    },
    {
      accessorKey: "depositPrice",
      header: ({ column }) => {
        return (
          <Button
            variant="ghost"
            size="sm"
            onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
            className="flex items-center gap-1 px-0 h-6 py-0"
            data-column-id="depositPrice"
          >
            <span className="text-xs">Tiền ký quỹ</span>
            <ArrowUpDown className="ml-1 h-3 w-3" />
          </Button>
        )
      },
      meta: {
        header: "Tiền ký quỹ"
      },
      cell: ({ row }) => {
        const depositPrice = row.getValue("depositPrice") as number | undefined

        return (
          <div className="flex items-center gap-1 text-sm">
            <span>{formatCurrency(depositPrice)} VND</span>
          </div>
        )
      },
      enableSorting: true,
    },
    {
      accessorKey: "settlementPrice",
      header: ({ column }) => {
        return (
          <Button
            variant="ghost"
            size="sm"
            onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
            className="flex items-center gap-1 px-0 h-6 py-0"
            data-column-id="settlementPrice"
          >
            <span className="text-xs">Tiền tất toán</span>
            <ArrowUpDown className="ml-1 h-3 w-3" />
          </Button>
        )
      },
      meta: {
        header: "Tiền tất toán"
      },
      cell: ({ row }) => {
        const order = row.original;
        const settlementPrice = calculateRealTimeSettlement(order, currentQuote || null);

        return (
          <div className="flex items-center gap-1 text-sm">
            <span className={getPriceColor(order)}>
              {formatCurrency(settlementPrice)} VND
            </span>
          </div>
        )
      },
      enableSorting: true,
    },
    {
      accessorKey: "storageFee",
      header: ({ column }) => {
        return (
          <Button
            variant="ghost"
            size="sm"
            onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
            className="flex items-center gap-1 px-0 h-6 py-0"
            data-column-id="storageFee"
          >
            <span className="text-xs">Phí lưu kho</span>
            <ArrowUpDown className="ml-1 h-3 w-3" />
          </Button>
        )
      },
      meta: {
        header: "Phí lưu kho"
      },
      cell: ({ row }) => {
        const storageFee = row.getValue("storageFee") as number | undefined

        return (
          <div className="flex items-center gap-1 text-sm">
            <span>{formatCurrency(storageFee)} VND</span>
          </div>
        )
      },
      enableSorting: true,
    },
    {
      accessorKey: "settlementDeadline",
      header: ({ column }) => {
        return (
          <Button
            variant="ghost"
            size="sm"
            onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
            className="flex items-center gap-1 px-0 h-6 py-0"
            data-column-id="settlementDeadline"
          >
            <span className="text-xs">Hạn tất toán</span>
            <ArrowUpDown className="ml-1 h-3 w-3" />
          </Button>
        )
      },
      meta: {
        header: "Hạn tất toán"
      },
      cell: ({ row }) => {
        const date = row.original
        return (
          <DateTimeDisplay 
            date={date.settlementDeadline}
            className="text-sm"
            timeClassName="text-xs text-muted-foreground" 
          />
        )
      },
      enableSorting: true,
      size: 150,
    },
    {
      accessorKey: "settlementAt",
      header: ({ column }) => {
        return (
          <Button
            variant="ghost"
            size="sm"
            onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
            className="flex items-center gap-1 px-0 h-6 py-0"
            data-column-id="settlementAt"
          >
            <span className="text-xs">Ngày tất toán</span>
            <ArrowUpDown className="ml-1 h-3 w-3" />
          </Button>
        )
      },
      meta: {
        header: "Ngày tất toán"
      },
      cell: ({ row }) => {
        const date = row.original
        return (
          <DateTimeDisplay 
            date={date.settlementAt}
            className="text-sm"
            timeClassName="text-xs text-muted-foreground" 
          />
        )
      },
      enableSorting: true,
      size: 150,
    },
    {
      accessorKey: "approveStatus",
      header: ({ column }) => {
        return (
          <Button
            variant="ghost"
            size="sm"
            onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
            className="flex items-center gap-1 px-0 h-6 py-0"
            data-column-id="approveStatus"
          >
            <span className="text-xs">Phê duyệt</span>
            <ArrowUpDown className="ml-1 h-3 w-3" />
          </Button>
        )
      },
      meta: {
        header: "Phê duyệt"
      },
      cell: ({ row }) => {
        const approveStatus = row.original.approveStatus
        const order = row.original

        const handleApproveStatusChange = (newStatus: ApproveStatus) => {
          if (onChangeApproveStatus) {
            onChangeApproveStatus(order, newStatus);
          }
        };

        return (
          <ApproveStatusSelector
            approveStatus={approveStatus}
            onApproveStatusChange={handleApproveStatusChange}
          />
        )
      },
      enableSorting: true,
      size: 120,
    },
    {
      accessorKey: "status",
      header: ({ column }) => {
        return (
          <Button
            variant="ghost"
            size="sm"
            onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
            className="flex items-center gap-1 px-0 h-6 py-0"
            data-column-id="status"
          >
            <span className="text-xs">Trạng thái</span>
            <ArrowUpDown className="ml-1 h-3 w-3" />
          </Button>
        )
      },
      meta: {
        header: "Trạng thái"
      },
      cell: ({ row }) => {
        const status = row.getValue("status") as OrderStatus
        const order = row.original

        return (
          <Badge className={getStatusColor(status)}>
            {getStatusName(status)}
          </Badge>
        )
      },
      enableSorting: true,
      size: 120,
    },
    {
      accessorKey: "creator",
      header: ({ column }) => {
        return (
          <Button
            variant="ghost"
            size="sm"
            onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
            className="flex items-center gap-1 px-0 h-6 py-0"
            data-column-id="creator"
          >
            <span className="text-xs">Người tạo</span>
            <ArrowUpDown className="ml-1 h-3 w-3" />
          </Button>
        )
      },
      meta: {
        header: "Người tạo"
      },
      cell: ({ row }) => {
        const order = row.original;

        // Kiểm tra cả hai trường hợp: creator là object hoặc createdBy là string
        const hasCreator = order.creator && typeof order.creator === 'object';
        const hasCreatedBy = order.createdBy && typeof order.createdBy === 'string';

        // Nếu có thông tin user, hiển thị thông tin chi tiết
        if (hasCreator) {
          return (
            <UserHoverCard user={order.creator} showAvatar={true} size="sm">
              <div className="max-w-[120px] overflow-hidden">
                <div className="text-xs font-normal truncate">{order.creator?.fullName || order.creator?.username || 'User'}</div>
                <div className="text-xs text-muted-foreground truncate">{order.creator?.email || ''}</div>
              </div>
            </UserHoverCard>
          );
        }

        // Nếu chỉ có ID, hiển thị ID rút gọn
        if (hasCreatedBy) {
          return (
            <UserHoverCard userId={order.createdBy} showAvatar={true} size="sm">
              <div className="flex items-center gap-1">
                <User className="h-4 w-4 text-blue-500" />
                <span className="text-xs text-muted-foreground">
                  {order.createdBy?.substring(0, 8)}...
                </span>
              </div>
            </UserHoverCard>
          );
        }

        // Nếu không có thông tin, hiển thị ---
        return (
          <span className="text-xs text-muted-foreground">---</span>
        );
      },
      enableSorting: true,
    },
    {
      accessorKey: "createdAt",
      header: ({ column }) => {
        return (
          <Button
            variant="ghost"
            size="sm"
            onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
            className="flex items-center gap-1 px-0 h-6 py-0"
            data-column-id="createdAt"
          >
            <span className="text-xs">Ngày tạo</span>
            <ArrowUpDown className="ml-1 h-3 w-3" />
          </Button>
        )
      },
      meta: {
        header: "Ngày tạo"
      },
      cell: ({ row }) => {
        const date = row.original
        return (
          <DateTimeDisplay 
            date={date.createdAt}
            className="text-sm"
            timeClassName="text-xs text-muted-foreground" 
          />
        )
      },
      enableSorting: true,
      size: 150,
    },
    {
      accessorKey: "updater",
      header: ({ column }) => {
        return (
          <Button
            variant="ghost"
            size="sm"
            onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
            className="flex items-center gap-1 px-0 h-6 py-0"
            data-column-id="updater"
          >
            <span className="text-xs">Người cập nhật</span>
            <ArrowUpDown className="ml-1 h-3 w-3" />
          </Button>
        )
      },
      meta: {
        header: "Người cập nhật"
      },
      cell: ({ row }) => {
        const order = row.original;

        // Kiểm tra cả hai trường hợp: updater là object hoặc updatedBy là string
        const hasUpdater = order.updater && typeof order.updater === 'object';
        const hasUpdatedBy = order.updatedBy && typeof order.updatedBy === 'string';

        // Nếu có thông tin user, hiển thị thông tin chi tiết
        if (hasUpdater) {
          return (
            <UserHoverCard user={order.updater} showAvatar={true} size="sm">
              <div className="max-w-[120px] overflow-hidden">
                <div className="text-xs font-normal truncate">{order.updater?.fullName || order.updater?.username || 'User'}</div>
                <div className="text-xs text-muted-foreground truncate">{order.updater?.email || ''}</div>
              </div>
            </UserHoverCard>
          );
        }

        // Nếu chỉ có ID, hiển thị ID rút gọn
        if (hasUpdatedBy) {
          return (
            <UserHoverCard userId={order.updatedBy} showAvatar={true} size="sm">
              <div className="flex items-center gap-1">
                <User className="h-4 w-4 text-blue-500" />
                <span className="text-xs text-muted-foreground">
                  {order.updatedBy?.substring(0, 8)}...
                </span>
              </div>
            </UserHoverCard>
          );
        }

        // Nếu không có thông tin, hiển thị ---
        return (
          <span className="text-xs text-muted-foreground">---</span>
        );
      },
      enableSorting: true,
      enableHiding: true,
    },
    {
      id: 'actions',
      size: 40,
      enableHiding: false,
      header: () => <div data-column-id="actions"></div>,
      cell: ({ row }) => (
        <Actions
          row={row}
          onViewDetail={onViewDetail}
          onDelete={onDelete}
          onEdit={onEdit}
          onChangeStatus={onChangeStatus}
          onExtendSettlement={onExtendSettlement}
        />
      ),
      meta: {
        isSticky: true, // Đánh dấu cột này là cố định
        position: 'right', // Vị trí cố định (right hoặc left)
        header: "Thao tác"
      }
    }
  ]
}
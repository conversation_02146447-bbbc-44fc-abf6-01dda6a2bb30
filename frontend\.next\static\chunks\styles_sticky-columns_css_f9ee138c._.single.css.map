{"version": 3, "sources": [], "sections": [{"offset": {"line": 1, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/styles/sticky-columns.css"], "sourcesContent": ["/* <PERSON><PERSON> cục bảng thống nhất với Header c<PERSON> định */\r\n\r\n/* Container cuộn bảng - wrapper chính */\r\n.table-scroll-container {\r\n  position: relative;\r\n  height: 100%; /* S<PERSON> dụng chiều cao 100% thay vì tính toán cố định */\r\n  min-height: 200px;\r\n  overflow: auto; /* Cho phép cuộn cả ngang và dọc */\r\n  scrollbar-width: thin;\r\n  scrollbar-color: var(--border) var(--muted);\r\n}\r\n\r\n/* Viewport bảng - đảm bảo ngữ cảnh định vị phù hợp */\r\n.table-viewport {\r\n  position: relative;\r\n  width: 100%;\r\n  height: 100%;\r\n}\r\n\r\n/* Định dạng bảng dữ liệu */\r\n.data-table {\r\n  width: 100%;\r\n  table-layout: fixed;\r\n  border-collapse: collapse;\r\n  min-width: max-content; /* Đảm bảo cuộn ngang hoạt động */\r\n}\r\n\r\n/* Kiểu dáng cho header cố định */\r\n.sticky-header {\r\n  position: sticky !important;\r\n  top: 0 !important;\r\n  z-index: 50 !important;\r\n  background-color: var(--background) !important;\r\n  box-shadow: 0 2px 4px rgba(0,0,0,0.1) !important;\r\n  height: auto !important;\r\n}\r\n\r\n/* <PERSON><PERSON><PERSON> bảo tất cả các phần tử th trong header cố định có màu nền */\r\n.sticky-header th {\r\n  background-color: var(--background) !important;\r\n  height: 36px !important; /* Chiều cao đã giảm */\r\n  padding-top: 8px !important;\r\n  padding-bottom: 8px !important;\r\n  vertical-align: middle !important;\r\n}\r\n\r\n/* Kiểu dáng cho các cột cố định */\r\n.sticky-right {\r\n  position: sticky !important;\r\n  right: 0 !important;\r\n  z-index: 40 !important;\r\n  background-color: var(--background) !important;\r\n  box-shadow: -5px 0 5px -5px rgba(0,0,0,0.1) !important;\r\n}\r\n\r\n.sticky-left {\r\n  position: sticky !important;\r\n  left: 0 !important;\r\n  z-index: 40 !important;\r\n  background-color: var(--background) !important;\r\n  box-shadow: 5px 0 5px -5px rgba(0,0,0,0.1) !important;\r\n}\r\n\r\n/* Trường hợp đặc biệt cho các ô header cũng là cố định */\r\n.sticky-header th.sticky-right {\r\n  z-index: 60 !important;\r\n}\r\n\r\n.sticky-header th.sticky-left {\r\n  z-index: 60 !important;\r\n}\r\n\r\n/* Định dạng cột checkbox - dựa trên class thay vì :first-child */\r\n/* Tăng specificity để đảm bảo không bị override */\r\n.data-table .checkbox-header,\r\n.data-table .checkbox-cell,\r\nth.checkbox-header,\r\ntd.checkbox-cell {\r\n  position: sticky !important;\r\n  left: 0 !important;\r\n  z-index: 40 !important;\r\n  background-color: var(--background) !important;\r\n  box-shadow: 5px 0 5px -5px rgba(0,0,0,0.1) !important;\r\n  width: 40px !important;\r\n  min-width: 40px !important;\r\n  max-width: 40px !important;\r\n  padding: 0 !important;\r\n  text-align: center !important;\r\n}\r\n\r\n/* Đảm bảo checkbox trong header có z-index cao hơn */\r\n.data-table .checkbox-header,\r\nth.checkbox-header,\r\n.sticky-header .checkbox-header {\r\n  z-index: 60 !important;\r\n}\r\n\r\n/* Đảm bảo các checkbox được căn giữa */\r\n.checkbox-header > div,\r\n.checkbox-cell > div {\r\n  display: flex !important;\r\n  justify-content: center !important;\r\n  align-items: center !important;\r\n  height: 100% !important;\r\n  width: 100% !important;\r\n}\r\n\r\n/* Override :first-child styles khi không phải checkbox column */\r\n/* Chỉ áp dụng sticky styles cho columns có meta.isSticky = true */\r\nth:first-child:not(.checkbox-header),\r\ntd:first-child:not(.checkbox-cell) {\r\n  position: static !important;\r\n  left: auto !important;\r\n  z-index: auto !important;\r\n  background-color: transparent !important;\r\n  box-shadow: none !important;\r\n  width: auto !important;\r\n  min-width: auto !important;\r\n  max-width: none !important;\r\n  padding: 8px 16px !important;\r\n  text-align: left !important;\r\n}\r\n\r\n/* Đảm bảo first-child không phải checkbox có styling bình thường */\r\nth:first-child:not(.checkbox-header) > div,\r\ntd:first-child:not(.checkbox-cell) > div {\r\n  display: block !important;\r\n  justify-content: flex-start !important;\r\n  align-items: flex-start !important;\r\n  height: auto !important;\r\n  width: auto !important;\r\n}\r\n\r\n/* Override CSS selector [&:has([role=checkbox])] cho non-checkbox columns */\r\n/* Chỉ checkbox cells mới có padding-right: 0 */\r\ntd:not(.checkbox-cell) {\r\n  padding-right: 8px !important;\r\n}\r\n\r\n/* Đảm bảo checkbox cells có padding đúng */\r\n.checkbox-cell {\r\n  padding-right: 0 !important;\r\n}\r\n\r\n/* Đảm bảo checkbox columns luôn visible và sticky */\r\n/* Override mọi CSS có thể conflict */\r\n.data-table th[data-column-id=\"select\"],\r\n.data-table td[data-column-id=\"select\"],\r\n.data-table th.checkbox-header,\r\n.data-table td.checkbox-cell {\r\n  position: sticky !important;\r\n  left: 0 !important;\r\n  z-index: 50 !important;\r\n  background-color: var(--background) !important;\r\n  box-shadow: 5px 0 5px -5px rgba(0,0,0,0.1) !important;\r\n  width: 40px !important;\r\n  min-width: 40px !important;\r\n  max-width: 40px !important;\r\n}\r\n\r\n/* Checkbox header có z-index cao nhất */\r\n.data-table th[data-column-id=\"select\"],\r\n.data-table th.checkbox-header {\r\n  z-index: 70 !important;\r\n}\r\n\r\n/* Debug: Thêm border để dễ nhận biết checkbox columns */\r\n.data-table th[data-column-id=\"select\"],\r\n.data-table td[data-column-id=\"select\"] {\r\n  border-right: 2px solid var(--primary) !important;\r\n}\r\n\r\n/* Đảm bảo sticky columns không bị ẩn bởi overflow */\r\n.table-scroll-container {\r\n  position: relative;\r\n}\r\n\r\n/* CSS cho checkbox column (luôn ở vị trí đầu tiên) */\r\n.data-table th[data-column-id=\"select\"],\r\n.data-table td[data-column-id=\"select\"] {\r\n  position: sticky !important;\r\n  left: 0 !important;\r\n  z-index: 999 !important; /* Z-index rất cao để debug */\r\n}\r\n\r\n/* CSS cho sticky left columns khác */\r\n.data-table th.sticky-left,\r\n.data-table td.sticky-left {\r\n  position: sticky !important;\r\n  z-index: 50 !important;\r\n  background-color: var(--background) !important;\r\n  box-shadow: 5px 0 5px -5px rgba(0,0,0,0.1) !important;\r\n}\r\n\r\n/* Đảm bảo sticky left columns có border để debug */\r\n.data-table th.sticky-left,\r\n.data-table td.sticky-left {\r\n  border-right: 1px solid var(--border) !important;\r\n}\r\n\r\n/* Kiểu dáng cho ô hành động */\r\n.sticky-action-cell {\r\n  display: flex;\r\n  justify-content: flex-end;\r\n  align-items: center;\r\n  height: 100%;\r\n  transition: background-color 0.2s ease;\r\n  padding: 0 !important;\r\n  width: auto !important;\r\n}\r\n\r\n/* Kiểu dáng cụ thể cho cột Actions */\r\n[id^=\"actions\"] {\r\n  position: sticky !important;\r\n  right: 0 !important;\r\n  z-index: 40 !important;\r\n  background-color: var(--background) !important;\r\n  width: auto !important;\r\n  min-width: 80px !important;\r\n  max-width: 100px !important;\r\n}\r\n\r\n/* Thêm chỉ báo trực quan cho các cột cố định */\r\n.sticky-right::after {\r\n  content: '';\r\n  position: absolute;\r\n  top: 0;\r\n  bottom: 0;\r\n  left: -8px;\r\n  width: 8px;\r\n  background: linear-gradient(to right, transparent, rgba(0,0,0,0.05));\r\n  pointer-events: none;\r\n}\r\n\r\n/* Định dạng thanh cuộn cho trình duyệt WebKit */\r\n.table-scroll-container::-webkit-scrollbar {\r\n  height: 10px;\r\n  width: 10px;\r\n  background-color: var(--muted);\r\n}\r\n\r\n.table-scroll-container::-webkit-scrollbar-thumb {\r\n  background-color: var(--border);\r\n  border-radius: 5px;\r\n}\r\n\r\n.table-scroll-container::-webkit-scrollbar-thumb:hover {\r\n  background-color: var(--muted-foreground);\r\n}\r\n\r\n/* Đảm bảo thanh cuộn luôn hiển thị trong Firefox */\r\n@supports (scrollbar-width: thin) {\r\n  .table-scroll-container {\r\n    scrollbar-width: thin !important;\r\n  }\r\n}\r\n\r\n/* Đảm bảo container bảng sử dụng hết không gian có sẵn */\r\n.rounded-md.border.flex.flex-col.flex-1.overflow-hidden {\r\n  display: flex;\r\n  flex-direction: column;\r\n  height: 100%;\r\n  min-height: 0; /* Quan trọng để container flex tôn trọng chiều cao của phần tử con */\r\n}\r\n\r\n/* Khắc phục khoảng cách giữa bảng và phân trang */\r\n.w-full.flex.flex-col.h-full {\r\n  display: flex;\r\n  flex-direction: column;\r\n  height: 100%;\r\n  min-height: 0; /* Quan trọng cho container flex */\r\n}\r\n\r\n/* Đảm bảo footer bảng gắn liền với phần dưới không có khoảng trống */\r\n.sticky.bottom-0.bg-background.border-t.mt-auto {\r\n  margin-top: 0 !important; /* Loại bỏ margin có thể tạo ra khoảng trống */\r\n}\r\n\r\n/* Tối ưu chiều cao hàng để hiển thị dữ liệu tốt hơn */\r\n.data-table tbody tr {\r\n  height: 32px !important;\r\n}\r\n\r\n/* Cải thiện hiệu ứng hover để tăng trải nghiệm người dùng */\r\n.data-table tbody tr:hover {\r\n  background-color: var(--muted) !important;\r\n}\r\n\r\n/* Cải thiện hiển thị trạng thái được chọn */\r\n.data-table tbody tr[data-state=\"selected\"] {\r\n  background-color: var(--accent) !important;\r\n}\r\n"], "names": [], "mappings": "AAGA;;;;;;;;;AAUA;;;;;;AAOA;;;;;;;AAQA;;;;;;;;;AAUA;;;;;;;;AASA;;;;;;;;AAQA;;;;;;;;AASA;;;;AAUA;;;;;;;;;;;;;AAiBA;;;;AAOA;;;;;;;;AAWA;;;;;;;;;;;;;AAeA;;;;;;;;AAWA;;;;AAKA;;;;AAMA;;;;;;;;;;;AAeA;;;;AAMA;;;;AAMA;;;;AAKA;;;;;;AAQA;;;;;;;;AAeA;;;;;;;;;;AAWA;;;;;;;;;;AAWA;;;;;;;;;;;AAYA;;;;;;AAMA;;;;;AAKA;;;;AAKA;EACE;;;;;AAMF;;;;;;;AAgBA;;;;AAKA;;;;AAKA;;;;AAKA"}}]}
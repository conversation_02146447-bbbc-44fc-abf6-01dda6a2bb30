"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UpdateRoleService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("typeorm");
const typeorm_transactional_1 = require("typeorm-transactional");
const update_role_dto_1 = require("../dto/update-role.dto");
const base_role_service_1 = require("./base.role.service");
let UpdateRoleService = class UpdateRoleService extends base_role_service_1.BaseRoleService {
    async findPermissionsByIdsOrFail(permissionIds) {
        if (!permissionIds || permissionIds.length === 0)
            return [];
        const permissions = await this.permissionRepository.findBy({
            id: (0, typeorm_1.In)(permissionIds),
        });
        if (permissions.length !== permissionIds.length) {
            const foundIds = permissions.map((p) => p.id);
            const notFoundIds = permissionIds.filter((id) => !foundIds.includes(id));
            this.logger.warn(`Permissions not found: ${notFoundIds.join(', ')}`);
            throw new common_1.NotFoundException(`One or more permissions not found: ${notFoundIds.join(', ')}`);
        }
        return permissions;
    }
    async preloadPermissionsMap(permissionIds) {
        const uniqueIds = [...new Set(permissionIds.filter((id) => id))];
        if (uniqueIds.length === 0)
            return new Map();
        const permissions = await this.findPermissionsByIdsOrFail(uniqueIds);
        return new Map(permissions.map((permission) => [permission.id, permission]));
    }
    async update(id, updateDto, userId) {
        this.logger.log(`Attempting to update Role ID: ${id}`);
        try {
            const roleToUpdate = await this.findByIdOrFail(id);
            Object.assign(roleToUpdate, {
                ...updateDto,
                updatedBy: userId,
            });
            const updatedRole = await this.roleRepository.save(roleToUpdate);
            if (updateDto.permissionIds !== undefined) {
                await this.roleRepository.manager
                    .createQueryBuilder()
                    .update('role_permissions')
                    .set({
                    isDeleted: true,
                    deletedAt: new Date(),
                })
                    .where('roleId = :roleId', { roleId: id })
                    .execute();
                if (updateDto.permissionIds.length > 0) {
                    const permissions = await this.findPermissionsByIdsOrFail(updateDto.permissionIds);
                    const rolePermissions = permissions.map(permission => ({
                        roleId: id,
                        permissionId: permission.id,
                        isDeleted: false,
                    }));
                    await this.roleRepository.manager
                        .createQueryBuilder()
                        .insert()
                        .into('role_permissions')
                        .values(rolePermissions)
                        .execute();
                }
            }
            const dto = this.convertToDto(updatedRole);
            if (dto) {
                this.eventsGateway.emitRoleUpdated(dto);
                this.eventEmitter.emit(this.EVENT_ROLE_UPDATED, {
                    roleId: dto.id,
                    userId,
                    newData: dto,
                    oldData: roleToUpdate,
                });
            }
            else {
                this.logger.warn('Failed to update Role DTO');
            }
            this.logger.log(`Successfully updated Role with ID: ${id}`);
            return dto;
        }
        catch (error) {
            this.logger.error(`Failed to update Role ID ${id}: ${error.message}`, error.stack);
            if (error instanceof common_1.NotFoundException ||
                error instanceof common_1.BadRequestException)
                throw error;
            throw new common_1.InternalServerErrorException('Failed to update record.');
        }
    }
    async bulkUpdate(updateDtos, userId) {
        this.logger.log(`Attempting to bulk update ${updateDtos.length} Role(s)`);
        if (updateDtos.length === 0)
            return [];
        const updatedDtosResult = [];
        try {
            const allPermissionIds = [
                ...new Set(updateDtos.flatMap((dto) => dto.permissionIds || [])),
            ];
            await this.preloadPermissionsMap(allPermissionIds);
            for (const dto of updateDtos) {
                if (!dto.id) {
                    this.logger.warn('Skipping bulk update item due to missing ID');
                    continue;
                }
                const updatedDto = await this.update(dto.id, dto, userId);
                updatedDtosResult.push(updatedDto);
            }
            this.logger.log(`Successfully processed bulk update for ${updatedDtosResult.length} Role(s)`);
            return updatedDtosResult;
        }
        catch (error) {
            this.logger.error(`Failed to bulk update Role: ${error.message}`, error.stack);
            if (error instanceof common_1.NotFoundException ||
                error instanceof common_1.BadRequestException)
                throw error;
            throw new common_1.InternalServerErrorException('Failed during bulk update.');
        }
    }
    async duplicate(id, userId) {
        this.logger.log(`Attempting to duplicate Role ID: ${id}`);
        try {
            const role = await this.findByIdOrFail(id, ['rolePermissions', 'rolePermissions.permission']);
            const duplicateData = {
                ...role,
                id: undefined,
                createdAt: undefined,
                updatedAt: undefined,
                version: undefined,
                name: `${role.name} (Copy ${Date.now()})`,
                rolePermissions: undefined,
                userRoles: undefined,
                createdBy: userId,
                updatedBy: userId,
            };
            delete duplicateData.id;
            const duplicate = this.roleRepository.create(duplicateData);
            const savedRole = await this.roleRepository.save(duplicate);
            if (role.rolePermissions && role.rolePermissions.length > 0) {
                const activeRolePermissions = role.rolePermissions.filter(rp => !rp.isDeleted && !rp.deletedAt);
                if (activeRolePermissions.length > 0) {
                    const rolePermissions = activeRolePermissions.map(rp => ({
                        roleId: savedRole.id,
                        permissionId: rp.permissionId,
                        isDeleted: false,
                    }));
                    await this.roleRepository.manager
                        .createQueryBuilder()
                        .insert()
                        .into('role_permissions')
                        .values(rolePermissions)
                        .execute();
                }
            }
            const dto = this.convertToDto(savedRole);
            if (dto) {
                this.eventsGateway.emitRoleDuplicated(dto);
                this.eventEmitter.emit(this.EVENT_ROLE_DUPLICATED, {
                    originalId: id,
                    newId: dto.id,
                    userId,
                    newData: dto,
                });
                this.logger.log(`Successfully duplicated Role ID: ${id} into new ID: ${savedRole.id}`);
                return dto;
            }
            else {
                throw new common_1.InternalServerErrorException('Failed to duplicate role.');
            }
        }
        catch (error) {
            this.logger.error(`Failed to duplicate Role ID ${id}: ${error.message}`, error.stack);
            if (error instanceof common_1.NotFoundException)
                throw error;
            throw new common_1.InternalServerErrorException('Failed to duplicate record.');
        }
    }
    async restore(id, userId) {
        this.logger.log(`Attempting to restore Role ID: ${id}`);
        try {
            const role = await this.findByIdOrFail(id, [], true);
            if (!role.isDeleted) {
                throw new common_1.BadRequestException(`Role ID ${id} is not deleted.`);
            }
            const result = await this.roleRepository.update(id, {
                isDeleted: false,
                deletedBy: null,
                deletedAt: null,
                updatedBy: userId,
            });
            if (result.affected === 0) {
                throw new common_1.InternalServerErrorException('Restore failed unexpectedly.');
            }
            const restoredRole = await this.findByIdOrFail(id);
            const dto = this.convertToDto(restoredRole);
            if (dto) {
                this.eventsGateway.emitRoleUpdated(dto);
                this.eventEmitter.emit(this.EVENT_ROLE_RESTORED, {
                    roleId: id,
                    userId,
                    newData: dto,
                });
                this.logger.log(`Successfully restored Role ID: ${id}`);
                return dto;
            }
            else {
                throw new common_1.InternalServerErrorException('Failed to convert restored role to DTO');
            }
        }
        catch (error) {
            this.logger.error(`Failed to restore Role ID ${id}: ${error.message}`, error.stack);
            if (error instanceof common_1.NotFoundException ||
                error instanceof common_1.BadRequestException)
                throw error;
            throw new common_1.InternalServerErrorException('Failed to restore record.');
        }
    }
};
exports.UpdateRoleService = UpdateRoleService;
__decorate([
    (0, typeorm_transactional_1.Transactional)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, update_role_dto_1.UpdateRoleDto, String]),
    __metadata("design:returntype", Promise)
], UpdateRoleService.prototype, "update", null);
__decorate([
    (0, typeorm_transactional_1.Transactional)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Array, String]),
    __metadata("design:returntype", Promise)
], UpdateRoleService.prototype, "bulkUpdate", null);
__decorate([
    (0, typeorm_transactional_1.Transactional)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String]),
    __metadata("design:returntype", Promise)
], UpdateRoleService.prototype, "duplicate", null);
__decorate([
    (0, typeorm_transactional_1.Transactional)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String]),
    __metadata("design:returntype", Promise)
], UpdateRoleService.prototype, "restore", null);
exports.UpdateRoleService = UpdateRoleService = __decorate([
    (0, common_1.Injectable)()
], UpdateRoleService);
//# sourceMappingURL=update.role.service.js.map
"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.DeleteCmsPostsService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const event_emitter_1 = require("@nestjs/event-emitter");
const typeorm_transactional_1 = require("typeorm-transactional");
const base_cms_posts_service_1 = require("./base.cms-posts.service");
const cms_posts_entity_1 = require("../entity/cms-posts.entity");
let DeleteCmsPostsService = class DeleteCmsPostsService extends base_cms_posts_service_1.BaseCmsPostsService {
    postRepository;
    dataSource;
    eventEmitter;
    constructor(postRepository, dataSource, eventEmitter) {
        super(postRepository, dataSource, eventEmitter);
        this.postRepository = postRepository;
        this.dataSource = dataSource;
        this.eventEmitter = eventEmitter;
    }
    async softDelete(id, userId) {
        try {
            this.logger.debug(`Đang xóa mềm bài viết CMS với ID: ${id}`);
            const post = await this.findById(id, ['category', 'author']);
            if (!post) {
                throw new common_1.NotFoundException(`Không tìm thấy bài viết với ID: ${id}`);
            }
            const oldData = this.toDto(post);
            post.isDeleted = true;
            post.deletedBy = userId;
            post.deletedAt = new Date();
            const deletedPost = await this.postRepository.save(post);
            const postDto = this.toDto(deletedPost);
            if (!postDto) {
                throw new common_1.InternalServerErrorException('Không thể chuyển đổi entity thành DTO');
            }
            this.eventEmitter.emit(this.EVENT_POST_DELETED, {
                postId: postDto.id,
                userId,
                oldData,
                newData: postDto,
            });
            return postDto;
        }
        catch (error) {
            this.logger.error(`Lỗi khi xóa mềm bài viết CMS: ${error.message}`, error.stack);
            if (error instanceof common_1.BadRequestException || error instanceof common_1.NotFoundException) {
                throw error;
            }
            throw new common_1.InternalServerErrorException(`Không thể xóa bài viết CMS: ${error.message}`);
        }
    }
    async restore(id, userId) {
        try {
            this.logger.debug(`Đang khôi phục bài viết CMS với ID: ${id}`);
            const post = await this.postRepository.findOne({
                where: { id, isDeleted: true },
                relations: ['category', 'author'],
            });
            if (!post) {
                throw new common_1.BadRequestException(`Không tìm thấy bài viết đã xóa với ID: ${id}`);
            }
            const oldData = this.toDto(post);
            post.isDeleted = false;
            post.deletedBy = null;
            post.deletedAt = null;
            post.updatedBy = userId;
            const restoredPost = await this.postRepository.save(post);
            const postDto = this.toDto(restoredPost);
            if (!postDto) {
                throw new common_1.InternalServerErrorException('Không thể chuyển đổi entity thành DTO');
            }
            this.eventEmitter.emit('cms-post.restored', {
                postId: postDto.id,
                userId,
                oldData,
                newData: postDto,
            });
            return postDto;
        }
        catch (error) {
            this.logger.error(`Lỗi khi khôi phục bài viết CMS: ${error.message}`, error.stack);
            if (error instanceof common_1.BadRequestException) {
                throw error;
            }
            throw new common_1.InternalServerErrorException(`Không thể khôi phục bài viết CMS: ${error.message}`);
        }
    }
    async remove(id) {
        try {
            this.logger.debug(`Đang xóa vĩnh viễn bài viết CMS với ID: ${id}`);
            const post = await this.postRepository.findOne({
                where: { id },
            });
            if (!post) {
                throw new common_1.NotFoundException(`Không tìm thấy bài viết với ID: ${id}`);
            }
            const result = await this.postRepository.delete(id);
            if (result.affected === 0) {
                throw new common_1.InternalServerErrorException(`Không thể xóa bài viết CMS với ID: ${id}`);
            }
            const affectedCount = result.affected ?? 0;
            return { affected: affectedCount };
        }
        catch (error) {
            this.logger.error(`Lỗi khi xóa vĩnh viễn bài viết CMS: ${error.message}`, error.stack);
            if (error instanceof common_1.BadRequestException || error instanceof common_1.NotFoundException) {
                throw error;
            }
            throw new common_1.InternalServerErrorException(`Không thể xóa vĩnh viễn bài viết CMS: ${error.message}`);
        }
    }
    async bulkSoftDelete(ids, userId) {
        try {
            this.logger.debug(`Đang xóa mềm ${ids.length} bài viết CMS`);
            const deletedPosts = [];
            for (const id of ids) {
                try {
                    const post = await this.softDelete(id, userId);
                    if (post) {
                        deletedPosts.push(post);
                    }
                }
                catch (error) {
                    this.logger.warn(`Không thể xóa bài viết với ID ${id}: ${error.message}`);
                }
            }
            return deletedPosts;
        }
        catch (error) {
            this.logger.error(`Lỗi khi xóa mềm nhiều bài viết CMS: ${error.message}`, error.stack);
            throw new common_1.InternalServerErrorException(`Không thể xóa nhiều bài viết CMS: ${error.message}`);
        }
    }
    async bulkRestore(ids, userId) {
        try {
            this.logger.debug(`Đang khôi phục ${ids.length} bài viết CMS`);
            const restoredPosts = [];
            for (const id of ids) {
                try {
                    const post = await this.restore(id, userId);
                    if (post) {
                        restoredPosts.push(post);
                    }
                }
                catch (error) {
                    this.logger.warn(`Không thể khôi phục bài viết với ID ${id}: ${error.message}`);
                }
            }
            return restoredPosts;
        }
        catch (error) {
            this.logger.error(`Lỗi khi khôi phục nhiều bài viết CMS: ${error.message}`, error.stack);
            throw new common_1.InternalServerErrorException(`Không thể khôi phục nhiều bài viết CMS: ${error.message}`);
        }
    }
    async bulkRemove(ids) {
        try {
            this.logger.debug(`Đang xóa vĩnh viễn ${ids.length} bài viết CMS`);
            let totalAffected = 0;
            for (const id of ids) {
                try {
                    const result = await this.remove(id);
                    totalAffected += result.affected;
                }
                catch (error) {
                    this.logger.warn(`Không thể xóa vĩnh viễn bài viết với ID ${id}: ${error.message}`);
                }
            }
            return { affected: totalAffected };
        }
        catch (error) {
            this.logger.error(`Lỗi khi xóa vĩnh viễn nhiều bài viết CMS: ${error.message}`, error.stack);
            throw new common_1.InternalServerErrorException(`Không thể xóa vĩnh viễn nhiều bài viết CMS: ${error.message}`);
        }
    }
};
exports.DeleteCmsPostsService = DeleteCmsPostsService;
__decorate([
    (0, typeorm_transactional_1.Transactional)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String]),
    __metadata("design:returntype", Promise)
], DeleteCmsPostsService.prototype, "softDelete", null);
__decorate([
    (0, typeorm_transactional_1.Transactional)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String]),
    __metadata("design:returntype", Promise)
], DeleteCmsPostsService.prototype, "restore", null);
__decorate([
    (0, typeorm_transactional_1.Transactional)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], DeleteCmsPostsService.prototype, "remove", null);
__decorate([
    (0, typeorm_transactional_1.Transactional)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Array, String]),
    __metadata("design:returntype", Promise)
], DeleteCmsPostsService.prototype, "bulkSoftDelete", null);
__decorate([
    (0, typeorm_transactional_1.Transactional)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Array, String]),
    __metadata("design:returntype", Promise)
], DeleteCmsPostsService.prototype, "bulkRestore", null);
__decorate([
    (0, typeorm_transactional_1.Transactional)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Array]),
    __metadata("design:returntype", Promise)
], DeleteCmsPostsService.prototype, "bulkRemove", null);
exports.DeleteCmsPostsService = DeleteCmsPostsService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(cms_posts_entity_1.CmsPosts)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        typeorm_2.DataSource,
        event_emitter_1.EventEmitter2])
], DeleteCmsPostsService);
//# sourceMappingURL=delete.cms-posts.service.js.map
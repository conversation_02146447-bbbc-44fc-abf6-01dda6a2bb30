"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ActivityLogsModule = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const event_emitter_1 = require("@nestjs/event-emitter");
const database_module_1 = require("../database/database.module");
const activity_log_entity_1 = require("./entities/activity-log.entity");
const user_entity_1 = require("../users/entities/user.entity");
const services_1 = require("./services");
const activity_log_listener_1 = require("./listeners/activity-log.listener");
const controllers_1 = require("./controllers");
let ActivityLogsModule = class ActivityLogsModule {
};
exports.ActivityLogsModule = ActivityLogsModule;
exports.ActivityLogsModule = ActivityLogsModule = __decorate([
    (0, common_1.Module)({
        imports: [
            database_module_1.DatabaseModule,
            typeorm_1.TypeOrmModule.forFeature([activity_log_entity_1.ActivityLog, user_entity_1.User]),
            event_emitter_1.EventEmitterModule.forRoot(),
        ],
        controllers: [
            controllers_1.ActivityLogReadController,
            controllers_1.ActivityLogCreateController,
            controllers_1.ActivityLogUpdateController,
            controllers_1.ActivityLogDeleteController,
            controllers_1.ActivityLogExportController,
        ],
        providers: [
            services_1.BaseActivityLogService,
            services_1.CreateActivityLogService,
            services_1.ReadActivityLogService,
            services_1.UpdateActivityLogService,
            services_1.DeleteActivityLogService,
            services_1.ExportActivityLogService,
            activity_log_listener_1.ActivityLogListener,
        ],
        exports: [
            services_1.CreateActivityLogService,
            services_1.ReadActivityLogService,
            services_1.UpdateActivityLogService,
            services_1.DeleteActivityLogService,
            services_1.ExportActivityLogService,
        ],
    })
], ActivityLogsModule);
//# sourceMappingURL=activity-logs.module.js.map
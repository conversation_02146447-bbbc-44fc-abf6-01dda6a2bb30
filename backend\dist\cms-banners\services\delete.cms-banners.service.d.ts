import { Repository, DataSource } from 'typeorm';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { BaseCmsBannersService } from './base.cms-banners.service';
import { CmsBanners } from '../entity/cms-banners.entity';
import { CmsBannerDto } from '../dto/cms-banner.dto';
export declare class DeleteCmsBannersService extends BaseCmsBannersService {
    protected readonly bannerRepository: Repository<CmsBanners>;
    protected readonly dataSource: DataSource;
    protected readonly eventEmitter: EventEmitter2;
    constructor(bannerRepository: Repository<CmsBanners>, dataSource: DataSource, eventEmitter: EventEmitter2);
    softDelete(id: string, userId: string): Promise<CmsBannerDto | null>;
    restore(id: string, userId: string): Promise<CmsBannerDto | null>;
    remove(id: string): Promise<{
        affected: number;
    }>;
    bulkSoftDelete(ids: string[], userId: string): Promise<CmsBannerDto[]>;
    bulkRestore(ids: string[], userId: string): Promise<CmsBannerDto[]>;
    bulkRemove(ids: string[]): Promise<{
        affected: number;
    }>;
}

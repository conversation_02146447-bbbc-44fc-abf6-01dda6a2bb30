"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.TokenCategoryDto = void 0;
const openapi = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
const swagger_1 = require("@nestjs/swagger");
const user_dto_1 = require("../../users/dto/user.dto");
const token_dto_1 = require("../../tokens/dto/token.dto");
const class_transformer_1 = require("class-transformer");
class TokenCategoryDto {
    id;
    categoryName;
    description;
    isActive;
    createdAt;
    updatedAt;
    createdBy;
    updatedBy;
    isDeleted;
    deletedBy;
    tokens;
    creator;
    updater;
    deleter;
    static _OPENAPI_METADATA_FACTORY() {
        return { id: { required: true, type: () => String, format: "uuid" }, categoryName: { required: true, type: () => String }, description: { required: false, type: () => String, nullable: true }, isActive: { required: true, type: () => Boolean }, createdAt: { required: true, type: () => Date }, updatedAt: { required: true, type: () => Date }, createdBy: { required: false, type: () => String, nullable: true, format: "uuid" }, updatedBy: { required: false, type: () => String, nullable: true, format: "uuid" }, isDeleted: { required: true, type: () => Boolean }, deletedBy: { required: false, type: () => String, nullable: true, format: "uuid" }, tokens: { required: false, type: () => [require("../../tokens/dto/token.dto").TokenDto] }, creator: { required: false, type: () => require("../../users/dto/user.dto").UserDto }, updater: { required: false, type: () => require("../../users/dto/user.dto").UserDto }, deleter: { required: false, type: () => require("../../users/dto/user.dto").UserDto } };
    }
}
exports.TokenCategoryDto = TokenCategoryDto;
__decorate([
    (0, class_transformer_1.Expose)(),
    (0, swagger_1.ApiProperty)({ description: 'Unique identifier of the token category' }),
    (0, class_validator_1.IsUUID)(),
    __metadata("design:type", String)
], TokenCategoryDto.prototype, "id", void 0);
__decorate([
    (0, class_transformer_1.Expose)(),
    (0, swagger_1.ApiProperty)({ description: 'Name of the token category', maxLength: 100 }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], TokenCategoryDto.prototype, "categoryName", void 0);
__decorate([
    (0, class_transformer_1.Expose)(),
    (0, swagger_1.ApiPropertyOptional)({ description: 'Description of the token category' }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Object)
], TokenCategoryDto.prototype, "description", void 0);
__decorate([
    (0, class_transformer_1.Expose)(),
    (0, swagger_1.ApiProperty)({ description: 'Active status of the token category' }),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], TokenCategoryDto.prototype, "isActive", void 0);
__decorate([
    (0, class_transformer_1.Expose)(),
    (0, swagger_1.ApiProperty)({ description: 'Creation date of the token category' }),
    (0, class_validator_1.IsDate)(),
    __metadata("design:type", Date)
], TokenCategoryDto.prototype, "createdAt", void 0);
__decorate([
    (0, class_transformer_1.Expose)(),
    (0, swagger_1.ApiProperty)({ description: 'Last update date of the token category' }),
    (0, class_validator_1.IsDate)(),
    __metadata("design:type", Date)
], TokenCategoryDto.prototype, "updatedAt", void 0);
__decorate([
    (0, class_transformer_1.Expose)(),
    (0, swagger_1.ApiPropertyOptional)({
        description: 'ID of the user who created the token category',
    }),
    (0, class_validator_1.IsUUID)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Object)
], TokenCategoryDto.prototype, "createdBy", void 0);
__decorate([
    (0, class_transformer_1.Expose)(),
    (0, swagger_1.ApiPropertyOptional)({
        description: 'ID of the user who updated the token category',
    }),
    (0, class_validator_1.IsUUID)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Object)
], TokenCategoryDto.prototype, "updatedBy", void 0);
__decorate([
    (0, class_transformer_1.Expose)(),
    (0, swagger_1.ApiProperty)({ description: 'Deletion status of the token category' }),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], TokenCategoryDto.prototype, "isDeleted", void 0);
__decorate([
    (0, class_transformer_1.Expose)(),
    (0, swagger_1.ApiPropertyOptional)({
        description: 'ID of the user who deleted the token category',
    }),
    (0, class_validator_1.IsUUID)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Object)
], TokenCategoryDto.prototype, "deletedBy", void 0);
__decorate([
    (0, class_transformer_1.Expose)(),
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Tokens associated with the token category',
        type: () => [token_dto_1.TokenDto],
    }),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Array)
], TokenCategoryDto.prototype, "tokens", void 0);
__decorate([
    (0, class_transformer_1.Expose)(),
    (0, swagger_1.ApiPropertyOptional)({ description: 'Người đã tạo danh mục token' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => user_dto_1.UserDto),
    __metadata("design:type", user_dto_1.UserDto)
], TokenCategoryDto.prototype, "creator", void 0);
__decorate([
    (0, class_transformer_1.Expose)(),
    (0, swagger_1.ApiPropertyOptional)({ description: 'Người đã cập nhật danh mục token' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => user_dto_1.UserDto),
    __metadata("design:type", user_dto_1.UserDto)
], TokenCategoryDto.prototype, "updater", void 0);
__decorate([
    (0, class_transformer_1.Expose)(),
    (0, swagger_1.ApiPropertyOptional)({ description: 'Người đã xóa danh mục token' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => user_dto_1.UserDto),
    __metadata("design:type", user_dto_1.UserDto)
], TokenCategoryDto.prototype, "deleter", void 0);
//# sourceMappingURL=token-category.dto.js.map
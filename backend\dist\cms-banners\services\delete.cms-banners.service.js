"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.DeleteCmsBannersService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const event_emitter_1 = require("@nestjs/event-emitter");
const typeorm_transactional_1 = require("typeorm-transactional");
const base_cms_banners_service_1 = require("./base.cms-banners.service");
const cms_banners_entity_1 = require("../entity/cms-banners.entity");
let DeleteCmsBannersService = class DeleteCmsBannersService extends base_cms_banners_service_1.BaseCmsBannersService {
    bannerRepository;
    dataSource;
    eventEmitter;
    constructor(bannerRepository, dataSource, eventEmitter) {
        super(bannerRepository, dataSource, eventEmitter);
        this.bannerRepository = bannerRepository;
        this.dataSource = dataSource;
        this.eventEmitter = eventEmitter;
    }
    async softDelete(id, userId) {
        try {
            this.logger.debug(`Đang xóa mềm banner CMS với ID: ${id}`);
            const banner = await this.findById(id, []);
            if (!banner) {
                throw new common_1.NotFoundException(`Không tìm thấy banner với ID: ${id}`);
            }
            const oldData = this.toDto(banner);
            await this.updateDisplayOrders(banner.location || null, banner.displayOrder + 1, -1);
            banner.isDeleted = true;
            banner.deletedBy = userId;
            banner.deletedAt = new Date();
            const deletedBanner = await this.bannerRepository.save(banner);
            const bannerDto = this.toDto(deletedBanner);
            if (!bannerDto) {
                throw new common_1.InternalServerErrorException('Không thể chuyển đổi entity thành DTO');
            }
            this.eventEmitter.emit(this.EVENT_BANNER_DELETED, {
                bannerId: bannerDto.id,
                userId,
                oldData,
                newData: bannerDto,
            });
            return bannerDto;
        }
        catch (error) {
            this.logger.error(`Lỗi khi xóa mềm banner CMS: ${error.message}`, error.stack);
            if (error instanceof common_1.BadRequestException || error instanceof common_1.NotFoundException) {
                throw error;
            }
            throw new common_1.InternalServerErrorException(`Không thể xóa banner CMS: ${error.message}`);
        }
    }
    async restore(id, userId) {
        try {
            this.logger.debug(`Đang khôi phục banner CMS với ID: ${id}`);
            const banner = await this.bannerRepository.findOne({
                where: { id, isDeleted: true },
            });
            if (!banner) {
                throw new common_1.BadRequestException(`Không tìm thấy banner đã xóa với ID: ${id}`);
            }
            const oldData = this.toDto(banner);
            const newDisplayOrder = await this.getNextDisplayOrder(banner.location || undefined);
            banner.isDeleted = false;
            banner.deletedBy = null;
            banner.deletedAt = null;
            banner.updatedBy = userId;
            banner.displayOrder = newDisplayOrder;
            const restoredBanner = await this.bannerRepository.save(banner);
            const bannerDto = this.toDto(restoredBanner);
            if (!bannerDto) {
                throw new common_1.InternalServerErrorException('Không thể chuyển đổi entity thành DTO');
            }
            this.eventEmitter.emit('cms-banner.restored', {
                bannerId: bannerDto.id,
                userId,
                oldData,
                newData: bannerDto,
            });
            return bannerDto;
        }
        catch (error) {
            this.logger.error(`Lỗi khi khôi phục banner CMS: ${error.message}`, error.stack);
            if (error instanceof common_1.BadRequestException) {
                throw error;
            }
            throw new common_1.InternalServerErrorException(`Không thể khôi phục banner CMS: ${error.message}`);
        }
    }
    async remove(id) {
        try {
            this.logger.debug(`Đang xóa vĩnh viễn banner CMS với ID: ${id}`);
            const banner = await this.bannerRepository.findOne({
                where: { id },
            });
            if (!banner) {
                throw new common_1.NotFoundException(`Không tìm thấy banner với ID: ${id}`);
            }
            if (!banner.isDeleted) {
                await this.updateDisplayOrders(banner.location || null, banner.displayOrder + 1, -1);
            }
            const result = await this.bannerRepository.delete(id);
            if (result.affected === 0) {
                throw new common_1.InternalServerErrorException(`Không thể xóa banner CMS với ID: ${id}`);
            }
            const affectedCount = result.affected ?? 0;
            return { affected: affectedCount };
        }
        catch (error) {
            this.logger.error(`Lỗi khi xóa vĩnh viễn banner CMS: ${error.message}`, error.stack);
            if (error instanceof common_1.BadRequestException || error instanceof common_1.NotFoundException) {
                throw error;
            }
            throw new common_1.InternalServerErrorException(`Không thể xóa vĩnh viễn banner CMS: ${error.message}`);
        }
    }
    async bulkSoftDelete(ids, userId) {
        try {
            this.logger.debug(`Đang xóa mềm ${ids.length} banner CMS`);
            const deletedBanners = [];
            for (const id of ids) {
                try {
                    const banner = await this.softDelete(id, userId);
                    if (banner) {
                        deletedBanners.push(banner);
                    }
                }
                catch (error) {
                    this.logger.warn(`Không thể xóa banner với ID ${id}: ${error.message}`);
                }
            }
            return deletedBanners;
        }
        catch (error) {
            this.logger.error(`Lỗi khi xóa mềm nhiều banner CMS: ${error.message}`, error.stack);
            throw new common_1.InternalServerErrorException(`Không thể xóa nhiều banner CMS: ${error.message}`);
        }
    }
    async bulkRestore(ids, userId) {
        try {
            this.logger.debug(`Đang khôi phục ${ids.length} banner CMS`);
            const restoredBanners = [];
            for (const id of ids) {
                try {
                    const banner = await this.restore(id, userId);
                    if (banner) {
                        restoredBanners.push(banner);
                    }
                }
                catch (error) {
                    this.logger.warn(`Không thể khôi phục banner với ID ${id}: ${error.message}`);
                }
            }
            return restoredBanners;
        }
        catch (error) {
            this.logger.error(`Lỗi khi khôi phục nhiều banner CMS: ${error.message}`, error.stack);
            throw new common_1.InternalServerErrorException(`Không thể khôi phục nhiều banner CMS: ${error.message}`);
        }
    }
    async bulkRemove(ids) {
        try {
            this.logger.debug(`Đang xóa vĩnh viễn ${ids.length} banner CMS`);
            let totalAffected = 0;
            for (const id of ids) {
                try {
                    const result = await this.remove(id);
                    totalAffected += result.affected;
                }
                catch (error) {
                    this.logger.warn(`Không thể xóa vĩnh viễn banner với ID ${id}: ${error.message}`);
                }
            }
            return { affected: totalAffected };
        }
        catch (error) {
            this.logger.error(`Lỗi khi xóa vĩnh viễn nhiều banner CMS: ${error.message}`, error.stack);
            throw new common_1.InternalServerErrorException(`Không thể xóa vĩnh viễn nhiều banner CMS: ${error.message}`);
        }
    }
};
exports.DeleteCmsBannersService = DeleteCmsBannersService;
__decorate([
    (0, typeorm_transactional_1.Transactional)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String]),
    __metadata("design:returntype", Promise)
], DeleteCmsBannersService.prototype, "softDelete", null);
__decorate([
    (0, typeorm_transactional_1.Transactional)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String]),
    __metadata("design:returntype", Promise)
], DeleteCmsBannersService.prototype, "restore", null);
__decorate([
    (0, typeorm_transactional_1.Transactional)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], DeleteCmsBannersService.prototype, "remove", null);
__decorate([
    (0, typeorm_transactional_1.Transactional)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Array, String]),
    __metadata("design:returntype", Promise)
], DeleteCmsBannersService.prototype, "bulkSoftDelete", null);
__decorate([
    (0, typeorm_transactional_1.Transactional)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Array, String]),
    __metadata("design:returntype", Promise)
], DeleteCmsBannersService.prototype, "bulkRestore", null);
__decorate([
    (0, typeorm_transactional_1.Transactional)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Array]),
    __metadata("design:returntype", Promise)
], DeleteCmsBannersService.prototype, "bulkRemove", null);
exports.DeleteCmsBannersService = DeleteCmsBannersService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(cms_banners_entity_1.CmsBanners)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        typeorm_2.DataSource,
        event_emitter_1.EventEmitter2])
], DeleteCmsBannersService);
//# sourceMappingURL=delete.cms-banners.service.js.map
import { EcomOrderDto } from '../../ecom-orders/dto/ecom-order.dto';
import { EcomProductDto } from '../../ecom-products/dto/ecom-product.dto';
import { UserDto } from '../../users/dto/user.dto';
export declare class EcomOrderDetailDto {
    id: string;
    orderId: string;
    productId: string;
    quantity: number;
    unitPrice: number;
    totalPrice: number;
    createdAt: Date;
    updatedAt: Date;
    createdBy?: string | null;
    updatedBy?: string | null;
    deletedBy?: string | null;
    deletedAt?: Date | null;
    isDeleted: boolean;
    order?: EcomOrderDto;
    product?: EcomProductDto;
    creator?: UserDto | null;
    updater?: UserDto | null;
    deleter?: UserDto | null;
}

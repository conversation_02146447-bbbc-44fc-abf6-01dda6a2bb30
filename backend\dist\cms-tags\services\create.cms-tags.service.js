"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreateCmsTagsService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const event_emitter_1 = require("@nestjs/event-emitter");
const typeorm_transactional_1 = require("typeorm-transactional");
const base_cms_tags_service_1 = require("./base.cms-tags.service");
const slug_cms_tags_service_1 = require("./slug.cms-tags.service");
const cms_tags_entity_1 = require("../entity/cms-tags.entity");
const create_cms_tag_dto_1 = require("../dto/create.cms-tag.dto");
let CreateCmsTagsService = class CreateCmsTagsService extends base_cms_tags_service_1.BaseCmsTagsService {
    tagRepository;
    dataSource;
    eventEmitter;
    slugService;
    constructor(tagRepository, dataSource, eventEmitter, slugService) {
        super(tagRepository, dataSource, eventEmitter);
        this.tagRepository = tagRepository;
        this.dataSource = dataSource;
        this.eventEmitter = eventEmitter;
        this.slugService = slugService;
    }
    async create(createDto, userId) {
        try {
            this.logger.debug(`Đang tạo thẻ CMS: ${JSON.stringify(createDto)}`);
            const existingTagByName = await this.findByName(createDto.name, false);
            if (existingTagByName) {
                throw new common_1.ConflictException(`Tên thẻ "${createDto.name}" đã tồn tại`);
            }
            const uniqueSlug = await this.slugService.generateUniqueSlugForCreate(createDto.name, createDto.slug);
            const tag = this.tagRepository.create();
            tag.name = createDto.name;
            tag.slug = uniqueSlug;
            tag.description = createDto.description || null;
            tag.imageUrl = createDto.imageUrl || null;
            tag.metaTitle = createDto.metaTitle || null;
            tag.metaDescription = createDto.metaDescription || null;
            tag.metaKeywords = createDto.metaKeywords || null;
            tag.createdBy = userId;
            tag.updatedBy = userId;
            const savedTag = await this.tagRepository.save(tag);
            const tagDto = this.toDto(savedTag);
            if (!tagDto) {
                throw new common_1.InternalServerErrorException('Không thể chuyển đổi entity thành DTO');
            }
            this.eventEmitter.emit(this.EVENT_TAG_CREATED, {
                tagId: tagDto.id,
                userId,
                newData: tagDto,
            });
            return tagDto;
        }
        catch (error) {
            this.logger.error(`Lỗi khi tạo thẻ CMS: ${error.message}`, error.stack);
            if (error instanceof common_1.BadRequestException || error instanceof common_1.ConflictException) {
                throw error;
            }
            throw new common_1.InternalServerErrorException(`Không thể tạo thẻ CMS: ${error.message}`);
        }
    }
    async bulkCreate(createDtos, userId) {
        try {
            this.logger.debug(`Đang tạo ${createDtos.length} thẻ CMS`);
            const tags = [];
            for (const createDto of createDtos) {
                const tag = await this.create(createDto, userId);
                tags.push(tag);
            }
            return tags;
        }
        catch (error) {
            this.logger.error(`Lỗi khi tạo nhiều thẻ CMS: ${error.message}`, error.stack);
            if (error instanceof common_1.BadRequestException || error instanceof common_1.ConflictException) {
                throw error;
            }
            throw new common_1.InternalServerErrorException(`Không thể tạo nhiều thẻ CMS: ${error.message}`);
        }
    }
    async createFromName(name, userId) {
        try {
            this.logger.debug(`Đang tạo thẻ CMS từ tên: ${name}`);
            const createDto = {
                name,
                description: null,
                imageUrl: null,
                metaTitle: null,
                metaDescription: null,
                metaKeywords: null,
            };
            return this.create(createDto, userId);
        }
        catch (error) {
            this.logger.error(`Lỗi khi tạo thẻ từ tên: ${error.message}`, error.stack);
            if (error instanceof common_1.ConflictException) {
                throw error;
            }
            throw new common_1.InternalServerErrorException(`Không thể tạo thẻ từ tên: ${error.message}`);
        }
    }
    async findOrCreate(name, userId) {
        try {
            this.logger.debug(`Đang tìm hoặc tạo thẻ CMS: ${name}`);
            const existingTag = await this.findByName(name, false);
            if (existingTag) {
                const tagDto = this.toDto(existingTag);
                if (tagDto) {
                    return tagDto;
                }
            }
            return this.createFromName(name, userId);
        }
        catch (error) {
            this.logger.error(`Lỗi khi tìm hoặc tạo thẻ: ${error.message}`, error.stack);
            throw new common_1.InternalServerErrorException(`Không thể tìm hoặc tạo thẻ: ${error.message}`);
        }
    }
    async createFromNames(names, userId) {
        try {
            this.logger.debug(`Đang tạo ${names.length} thẻ CMS từ danh sách tên`);
            const tags = [];
            for (const name of names) {
                const tag = await this.findOrCreate(name.trim(), userId);
                tags.push(tag);
            }
            return tags;
        }
        catch (error) {
            this.logger.error(`Lỗi khi tạo thẻ từ danh sách tên: ${error.message}`, error.stack);
            throw new common_1.InternalServerErrorException(`Không thể tạo thẻ từ danh sách tên: ${error.message}`);
        }
    }
};
exports.CreateCmsTagsService = CreateCmsTagsService;
__decorate([
    (0, typeorm_transactional_1.Transactional)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [create_cms_tag_dto_1.CreateCmsTagDto, String]),
    __metadata("design:returntype", Promise)
], CreateCmsTagsService.prototype, "create", null);
__decorate([
    (0, typeorm_transactional_1.Transactional)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Array, String]),
    __metadata("design:returntype", Promise)
], CreateCmsTagsService.prototype, "bulkCreate", null);
__decorate([
    (0, typeorm_transactional_1.Transactional)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String]),
    __metadata("design:returntype", Promise)
], CreateCmsTagsService.prototype, "createFromName", null);
__decorate([
    (0, typeorm_transactional_1.Transactional)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String]),
    __metadata("design:returntype", Promise)
], CreateCmsTagsService.prototype, "findOrCreate", null);
__decorate([
    (0, typeorm_transactional_1.Transactional)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Array, String]),
    __metadata("design:returntype", Promise)
], CreateCmsTagsService.prototype, "createFromNames", null);
exports.CreateCmsTagsService = CreateCmsTagsService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(cms_tags_entity_1.CmsTags)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        typeorm_2.DataSource,
        event_emitter_1.EventEmitter2,
        slug_cms_tags_service_1.SlugCmsTagsService])
], CreateCmsTagsService);
//# sourceMappingURL=create.cms-tags.service.js.map
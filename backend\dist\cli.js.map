{"version": 3, "file": "cli.js", "sourceRoot": "", "sources": ["../src/cli.ts"], "names": [], "mappings": ";;AAAA,6CAAyC;AACzC,2CAAwC;AACxC,qCAAqC;AACrC,iEAG+B;AAC/B,uCAA2C;AAE3C,KAAK,UAAU,SAAS;IACtB,MAAM,MAAM,GAAG,IAAI,eAAM,CAAC,KAAK,CAAC,CAAC;IAEjC,IAAI,CAAC;QAEH,MAAM,CAAC,GAAG,CAAC,uCAAuC,CAAC,CAAC;QACpD,IAAA,sDAA8B,GAAE,CAAC;QAGjC,MAAM,GAAG,GAAG,MAAM,kBAAW,CAAC,MAAM,CAAC,sBAAS,EAAE;YAC9C,MAAM,EAAE,CAAC,OAAO,EAAE,MAAM,EAAE,KAAK,EAAE,OAAO,CAAC;SAC1C,CAAC,CAAC;QAGH,MAAM,UAAU,GAAG,GAAG,CAAC,GAAG,CAAC,oBAAU,CAAC,CAAC;QAGvC,IAAI,CAAC,UAAU,EAAE,CAAC;YAChB,MAAM,CAAC,KAAK,CAAC,4EAA4E,CAAC,CAAC;YAC3F,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QAClB,CAAC;QAGD,IAAI,CAAC,UAAU,CAAC,aAAa,EAAE,CAAC;YAC9B,MAAM,CAAC,GAAG,CAAC,6DAA6D,CAAC,CAAC;YAC1E,MAAM,UAAU,CAAC,UAAU,EAAE,CAAC;YAC9B,MAAM,CAAC,GAAG,CAAC,sCAAsC,CAAC,CAAC;QACrD,CAAC;QAGD,IAAA,kDAA0B,EAAC,UAAU,CAAC,CAAC;QACvC,MAAM,CAAC,GAAG,CAAC,iDAAiD,CAAC,CAAC;QAI9D,IAAI,CAAC;YAEH,MAAM,EAAE,0BAA0B,EAAE,GAAG,2CAAa,0CAA0C,EAAC,CAAC;YAGhG,MAAM,eAAe,GAAG,GAAG,CAAC,GAAG,CAAC,0BAA0B,CAAC,CAAC;YAG5D,MAAM,CAAC,GAAG,CAAC,4CAA4C,CAAC,CAAC;YACzD,MAAM,eAAe,CAAC,GAAG,EAAE,CAAC;YAC5B,MAAM,CAAC,GAAG,CAAC,8BAA8B,CAAC,CAAC;QAC7C,CAAC;gBAAS,CAAC;YAET,MAAM,GAAG,CAAC,KAAK,EAAE,CAAC;QACpB,CAAC;IACH,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,MAAM,CAAC,KAAK,CAAC,yBAAyB,KAAK,CAAC,OAAO,EAAE,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC;QACpE,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;IAClB,CAAC;AACH,CAAC;AAED,SAAS,EAAE,CAAC"}
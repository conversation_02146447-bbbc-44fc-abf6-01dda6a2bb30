import { OnModuleDestroy } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
export declare class CacheService implements OnModuleDestroy {
    private readonly configService;
    private readonly logger;
    private cache;
    private readonly defaultTTL;
    private cleanupInterval;
    constructor(configService: ConfigService);
    set<T>(key: string, value: T, ttl?: number): void;
    get<T>(key: string): T | null;
    delete(key: string): void;
    clear(): void;
    has(key: string): boolean;
    private startCleanupInterval;
    private cleanup;
    onModuleDestroy(): void;
}

"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var MockTradingViewService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.MockTradingViewService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const silver_price_entity_1 = require("../entities/silver-price.entity");
const tradingview_websocket_service_1 = require("./tradingview-websocket.service");
const cache_service_1 = require("./cache.service");
let MockTradingViewService = MockTradingViewService_1 = class MockTradingViewService {
    tradingViewWebsocketService;
    cacheService;
    silverPriceRepository;
    logger = new common_1.Logger(MockTradingViewService_1.name);
    dataGenerationInterval;
    defaultSymbol = 'XAGUSD';
    lastPrice = 28.5;
    volatility = 0.05;
    updateInterval = 5000;
    constructor(tradingViewWebsocketService, cacheService, silverPriceRepository) {
        this.tradingViewWebsocketService = tradingViewWebsocketService;
        this.cacheService = cacheService;
        this.silverPriceRepository = silverPriceRepository;
    }
    async onModuleInit() {
        this.logger.log('Dịch vụ giả lập TradingView đã được khởi tạo');
        this.logger.log(`Giá bạc khởi tạo: ${this.lastPrice}`);
        this.startDataGeneration();
    }
    startDataGeneration() {
        this.dataGenerationInterval = setInterval(() => {
            this.generateAndBroadcastPrice();
        }, this.updateInterval);
        this.logger.log(`Đã bắt đầu tạo dữ liệu giả lập với khoảng thời gian ${this.updateInterval}ms`);
        this.generateAndBroadcastPrice();
        setInterval(() => {
            const clientCount = this.tradingViewWebsocketService['clients']?.size || 0;
            const subscriptionCount = this.tradingViewWebsocketService['subscriptions']?.size || 0;
            this.logger.log(`Thống kê WebSocket: ${clientCount} client kết nối, ${subscriptionCount} đăng ký`);
        }, 10000);
    }
    generateAndBroadcastPrice() {
        try {
            const priceData = this.generateMockPriceData();
            this.cacheService.set(`price_${priceData.symbol}`, priceData, 60);
            this.broadcastPriceUpdate(priceData);
            this.savePriceData(priceData);
            this.logger.debug(`Đã tạo dữ liệu giả lập cho ${priceData.symbol}: ${priceData.price}`);
        }
        catch (error) {
            this.logger.error(`Lỗi khi tạo dữ liệu giả lập: ${error.message}`, error.stack);
        }
    }
    generateMockPriceData() {
        const change = this.lastPrice * this.volatility * (Math.random() * 2 - 1);
        const newPrice = Math.max(0, this.lastPrice + change);
        const changePercent = (change / this.lastPrice) * 100;
        this.lastPrice = newPrice;
        const spread = newPrice * 0.001;
        const bid = newPrice - spread / 2;
        const ask = newPrice + spread / 2;
        const dayHigh = newPrice * (1 + Math.random() * 0.02);
        const dayLow = newPrice * (1 - Math.random() * 0.02);
        return {
            symbol: this.defaultSymbol,
            price: parseFloat(newPrice.toFixed(4)),
            timestamp: Date.now(),
            change: parseFloat(change.toFixed(4)),
            changePercent: parseFloat(changePercent.toFixed(2)),
            bid: parseFloat(bid.toFixed(4)),
            ask: parseFloat(ask.toFixed(4)),
            high: parseFloat(dayHigh.toFixed(4)),
            low: parseFloat(dayLow.toFixed(4)),
        };
    }
    broadcastPriceUpdate(data) {
        const priceUpdate = {
            symbol: data.symbol,
            price: data.price,
            timestamp: data.timestamp,
            change: data.change,
            changePercent: data.changePercent,
            bid: data.bid,
            ask: data.ask,
            high: data.high,
            low: data.low,
        };
        this.tradingViewWebsocketService.broadcastPriceUpdate(priceUpdate);
    }
    async savePriceData(data) {
        try {
            const silverPrice = this.silverPriceRepository.create({
                symbol: data.symbol,
                price: data.price,
                bid: data.bid,
                ask: data.ask,
                high: data.high,
                low: data.low,
                change: data.change,
                changePercent: data.changePercent,
                timestamp: data.timestamp,
                source: 'Mock',
            });
            await this.silverPriceRepository.save(silverPrice);
        }
        catch (error) {
            this.logger.error(`Lỗi khi lưu dữ liệu giá giả lập: ${error.message}`, error.stack);
        }
    }
    async fetchLatestPrice() {
        try {
            const cachedData = this.cacheService.get(`price_${this.defaultSymbol}`);
            if (cachedData) {
                return {
                    symbol: cachedData.symbol,
                    price: cachedData.price,
                    timestamp: cachedData.timestamp,
                    change: cachedData.change,
                    changePercent: cachedData.changePercent,
                    bid: cachedData.bid,
                    ask: cachedData.ask,
                    high: cachedData.high,
                    low: cachedData.low,
                };
            }
            const priceData = this.generateMockPriceData();
            this.cacheService.set(`price_${priceData.symbol}`, priceData, 60);
            return priceData;
        }
        catch (error) {
            this.logger.error(`Lỗi khi lấy giá mới nhất: ${error.message}`, error.stack);
            throw error;
        }
    }
};
exports.MockTradingViewService = MockTradingViewService;
exports.MockTradingViewService = MockTradingViewService = MockTradingViewService_1 = __decorate([
    (0, common_1.Injectable)(),
    __param(2, (0, typeorm_1.InjectRepository)(silver_price_entity_1.SilverPrice)),
    __metadata("design:paramtypes", [tradingview_websocket_service_1.TradingViewWebsocketService,
        cache_service_1.CacheService,
        typeorm_2.Repository])
], MockTradingViewService);
//# sourceMappingURL=mock-tradingview.service.js.map
"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var ReadEcomProductCategoriesPublicController_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.ReadEcomProductCategoriesPublicController = void 0;
const openapi = require("@nestjs/swagger");
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const class_transformer_1 = require("class-transformer");
const read_ecom_product_categories_service_1 = require("../services/read.ecom-product-categories.service");
const ecom_product_category_public_dto_1 = require("../dto/ecom-product-category.public.dto");
const custom_pagination_query_dto_1 = require("../../common/dto/custom-pagination-query.dto");
const pagination_response_dto_1 = require("../../common/dto/pagination-response.dto");
const custom_pagination_meta_dto_1 = require("../../common/dto/custom-pagination-meta.dto");
let ReadEcomProductCategoriesPublicController = ReadEcomProductCategoriesPublicController_1 = class ReadEcomProductCategoriesPublicController {
    ecomProductCategoriesService;
    logger = new common_1.Logger(ReadEcomProductCategoriesPublicController_1.name);
    constructor(ecomProductCategoriesService) {
        this.ecomProductCategoriesService = ecomProductCategoriesService;
    }
    toPublicDto(categoryDto) {
        const plainObj = {
            ...categoryDto,
            parentId: categoryDto.parent?.id || categoryDto.parentId || null,
        };
        return (0, class_transformer_1.plainToInstance)(ecom_product_category_public_dto_1.EcomProductCategoryPublicDto, plainObj, {
            excludeExtraneousValues: true,
            enableImplicitConversion: true,
            exposeDefaultValues: true,
            exposeUnsetFields: false,
        });
    }
    toPublicDtos(categoryDtos) {
        if (!categoryDtos || !Array.isArray(categoryDtos)) {
            return [];
        }
        return categoryDtos.map(dto => this.toPublicDto(dto))
            .filter((publicDto) => publicDto !== null);
    }
    async getActiveProductCategories(paginationQuery) {
        this.logger.log('Getting active Ecom Product Categories for public display');
        const queryWithActiveFilter = Object.assign(new custom_pagination_query_dto_1.CustomPaginationQueryDto(), {
            ...paginationQuery,
            filter: 'isActive:true',
        });
        const { data, total } = await this.ecomProductCategoriesService.findAll(queryWithActiveFilter);
        this.logger.log(`Found ${data.length} active categories from findAll`);
        const publicData = this.toPublicDtos(data);
        const filteredData = publicData;
        this.logger.log(`Returning ${filteredData.length} active categories (removed restrictive canDisplayPublicly filter)`);
        const meta = new custom_pagination_meta_dto_1.CustomPageMetaDto({
            pageQueryDto: paginationQuery,
            itemCount: total,
        });
        return new pagination_response_dto_1.PaginationResponseDto(filteredData, meta);
    }
    async getCarouselProductCategories(limit) {
        this.logger.log('Getting Ecom Product Categories for carousel display');
        const queryWithFilter = Object.assign(new custom_pagination_query_dto_1.CustomPaginationQueryDto(), {
            page: 1,
            limit: limit || 12,
            sort: 'name:ASC',
            filter: 'isActive:true',
        });
        const { data } = await this.ecomProductCategoriesService.findAll(queryWithFilter);
        this.logger.log(`Found ${data.length} active categories from findAll`);
        const publicData = this.toPublicDtos(data);
        this.logger.log(`Transformed to ${publicData.length} public DTOs`);
        return publicData;
    }
    async getRandomizedProductCategories(limit) {
        this.logger.log(`Getting randomized Ecom Product Categories with limit: ${limit || 4}`);
        const { data, total } = await this.ecomProductCategoriesService.getRandomizedActiveCategories(limit || 4);
        this.logger.log(`Found ${data.length} randomized categories from service`);
        data.forEach((category, index) => {
            this.logger.debug(`Category ${index}: id=${category.id}, name=${category.name}, isActive=${category.isActive}, isDeleted=${category.isDeleted}`);
        });
        const publicData = this.toPublicDtos(data);
        this.logger.log(`Transformed to ${publicData.length} public DTOs`);
        publicData.forEach((category, index) => {
            this.logger.debug(`Public DTO ${index}: id=${category.id}, name=${category.name}, canDisplayPublicly=${category.canDisplayPublicly}, isActive=${category.isActive}`);
        });
        const filteredData = publicData;
        this.logger.log(`Returning ${filteredData.length} randomized active categories (removed restrictive canDisplayPublicly filter)`);
        return {
            data: filteredData,
            meta: {
                total,
                returned: filteredData.length,
                limit: limit || 4,
            },
        };
    }
    async search(searchTerm, paginationQuery) {
        this.logger.log(`Searching Ecom Product Categories with term: ${searchTerm} for public display`);
        const queryWithSearchAndFilter = Object.assign(new custom_pagination_query_dto_1.CustomPaginationQueryDto(), {
            ...paginationQuery,
            search: searchTerm,
            filter: 'isActive:true',
        });
        const { data, total } = await this.ecomProductCategoriesService.findAll(queryWithSearchAndFilter);
        this.logger.log(`Found ${data.length} categories matching search term: ${searchTerm}`);
        const publicData = this.toPublicDtos(data);
        const filteredData = publicData;
        this.logger.log(`Returning ${filteredData.length} active categories from search (removed restrictive canDisplayPublicly filter)`);
        const meta = new custom_pagination_meta_dto_1.CustomPageMetaDto({
            pageQueryDto: paginationQuery,
            itemCount: total,
        });
        return new pagination_response_dto_1.PaginationResponseDto(filteredData, meta);
    }
    async findAllPublic(paginationQuery) {
        this.logger.log('Getting all Ecom Product Categories for public display with search and filter');
        const queryWithActiveFilter = Object.assign(new custom_pagination_query_dto_1.CustomPaginationQueryDto(), {
            ...paginationQuery,
            filter: 'isActive:true',
        });
        const { data, total } = await this.ecomProductCategoriesService.findAll(queryWithActiveFilter);
        this.logger.log(`Found ${data.length} active categories from findAll`);
        const publicData = this.toPublicDtos(data);
        const filteredData = publicData;
        this.logger.log(`Returning ${filteredData.length} active categories from findAllPublic (removed restrictive canDisplayPublicly filter)`);
        const meta = new custom_pagination_meta_dto_1.CustomPageMetaDto({
            pageQueryDto: paginationQuery,
            itemCount: total,
        });
        return new pagination_response_dto_1.PaginationResponseDto(filteredData, meta);
    }
};
exports.ReadEcomProductCategoriesPublicController = ReadEcomProductCategoriesPublicController;
__decorate([
    (0, common_1.Get)('active'),
    (0, swagger_1.ApiOperation)({ summary: 'Lấy danh sách danh mục sản phẩm đang hoạt động (public)' }),
    (0, swagger_1.ApiQuery)({
        name: 'page',
        required: false,
        type: Number,
        description: 'Số trang',
        example: 1,
    }),
    (0, swagger_1.ApiQuery)({
        name: 'limit',
        required: false,
        type: Number,
        description: 'Số lượng danh mục mỗi trang',
        example: 10,
    }),
    (0, swagger_1.ApiQuery)({
        name: 'search',
        required: false,
        type: String,
        description: 'Tìm kiếm theo tên danh mục',
        example: 'nhẫn',
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Danh sách danh mục sản phẩm đang hoạt động',
        schema: {
            type: 'object',
            properties: {
                data: {
                    type: 'array',
                    items: { $ref: '#/components/schemas/EcomProductCategoryPublicDto' },
                },
                meta: {
                    type: 'object',
                    properties: {
                        total: { type: 'number' },
                        page: { type: 'number' },
                        limit: { type: 'number' },
                        totalPages: { type: 'number' },
                    },
                },
            },
        },
    }),
    openapi.ApiResponse({ status: 200 }),
    __param(0, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [custom_pagination_query_dto_1.CustomPaginationQueryDto]),
    __metadata("design:returntype", Promise)
], ReadEcomProductCategoriesPublicController.prototype, "getActiveProductCategories", null);
__decorate([
    (0, common_1.Get)('carousel'),
    (0, swagger_1.ApiOperation)({ summary: 'Lấy danh mục sản phẩm cho category carousel (public)' }),
    (0, swagger_1.ApiQuery)({
        name: 'limit',
        required: false,
        type: Number,
        description: 'Số lượng danh mục tối đa',
        example: 12,
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Danh sách danh mục sản phẩm cho carousel',
        schema: {
            type: 'array',
            items: { $ref: '#/components/schemas/EcomProductCategoryPublicDto' },
        },
    }),
    openapi.ApiResponse({ status: 200, type: [require("../dto/ecom-product-category.public.dto").EcomProductCategoryPublicDto] }),
    __param(0, (0, common_1.Query)('limit')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", Promise)
], ReadEcomProductCategoriesPublicController.prototype, "getCarouselProductCategories", null);
__decorate([
    (0, common_1.Get)('collection'),
    (0, swagger_1.ApiOperation)({ summary: 'Lấy danh mục sản phẩm với thứ tự ngẫu nhiên (public)' }),
    (0, swagger_1.ApiQuery)({
        name: 'limit',
        required: false,
        type: Number,
        description: 'Số lượng danh mục tối đa',
        example: 4,
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Danh sách danh mục sản phẩm với thứ tự ngẫu nhiên. Mỗi lần gọi API sẽ trả về thứ tự khác nhau.',
        schema: {
            type: 'object',
            properties: {
                data: {
                    type: 'array',
                    items: { $ref: '#/components/schemas/EcomProductCategoryPublicDto' },
                },
                meta: {
                    type: 'object',
                    properties: {
                        total: { type: 'number' },
                        returned: { type: 'number' },
                        limit: { type: 'number' },
                    },
                },
            },
        },
    }),
    openapi.ApiResponse({ status: 200 }),
    __param(0, (0, common_1.Query)('limit')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", Promise)
], ReadEcomProductCategoriesPublicController.prototype, "getRandomizedProductCategories", null);
__decorate([
    (0, common_1.Get)('search'),
    (0, swagger_1.ApiOperation)({ summary: 'Tìm kiếm danh mục sản phẩm (public)' }),
    (0, swagger_1.ApiQuery)({
        name: 'q',
        required: true,
        type: String,
        description: 'Từ khóa tìm kiếm',
        example: 'nhẫn bạc',
    }),
    (0, swagger_1.ApiQuery)({
        name: 'page',
        required: false,
        type: Number,
        description: 'Số trang',
        example: 1,
    }),
    (0, swagger_1.ApiQuery)({
        name: 'limit',
        required: false,
        type: Number,
        description: 'Số lượng danh mục mỗi trang',
        example: 10,
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Kết quả tìm kiếm danh mục sản phẩm',
        schema: {
            type: 'object',
            properties: {
                data: {
                    type: 'array',
                    items: { $ref: '#/components/schemas/EcomProductCategoryPublicDto' },
                },
                meta: {
                    type: 'object',
                    properties: {
                        total: { type: 'number' },
                        page: { type: 'number' },
                        limit: { type: 'number' },
                        totalPages: { type: 'number' },
                    },
                },
            },
        },
    }),
    openapi.ApiResponse({ status: 200 }),
    __param(0, (0, common_1.Query)('q')),
    __param(1, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, custom_pagination_query_dto_1.CustomPaginationQueryDto]),
    __metadata("design:returntype", Promise)
], ReadEcomProductCategoriesPublicController.prototype, "search", null);
__decorate([
    (0, common_1.Get)(),
    (0, swagger_1.ApiOperation)({
        summary: 'Lấy tất cả danh mục sản phẩm (Public)',
        description: 'Lấy danh sách tất cả danh mục sản phẩm có thể hiển thị công khai với hỗ trợ tìm kiếm',
    }),
    (0, swagger_1.ApiQuery)({
        name: 'page',
        required: false,
        type: Number,
        description: 'Số trang',
        example: 1,
    }),
    (0, swagger_1.ApiQuery)({
        name: 'limit',
        required: false,
        type: Number,
        description: 'Số lượng danh mục mỗi trang',
        example: 10,
    }),
    (0, swagger_1.ApiQuery)({
        name: 'search',
        required: false,
        type: String,
        description: 'Từ khóa tìm kiếm',
        example: 'nhẫn',
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Danh sách danh mục sản phẩm public',
        schema: {
            type: 'object',
            properties: {
                data: {
                    type: 'array',
                    items: { $ref: '#/components/schemas/EcomProductCategoryPublicDto' },
                },
                meta: {
                    type: 'object',
                    properties: {
                        total: { type: 'number' },
                        page: { type: 'number' },
                        limit: { type: 'number' },
                        totalPages: { type: 'number' },
                    },
                },
            },
        },
    }),
    openapi.ApiResponse({ status: 200 }),
    __param(0, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [custom_pagination_query_dto_1.CustomPaginationQueryDto]),
    __metadata("design:returntype", Promise)
], ReadEcomProductCategoriesPublicController.prototype, "findAllPublic", null);
exports.ReadEcomProductCategoriesPublicController = ReadEcomProductCategoriesPublicController = ReadEcomProductCategoriesPublicController_1 = __decorate([
    (0, swagger_1.ApiTags)('ecom-product-categories-public'),
    (0, common_1.Controller)('ecom/product-categories/public'),
    __metadata("design:paramtypes", [read_ecom_product_categories_service_1.ReadEcomProductCategoriesService])
], ReadEcomProductCategoriesPublicController);
//# sourceMappingURL=read.ecom-product-categories.public.controller.js.map
import { OnGatewayConnection, OnGatewayDisconnect } from '@nestjs/websockets';
import { Server, Socket } from 'socket.io';
import { OrderBookDto } from '../dto/order-book.dto';
import { OrderStatus } from '../enums/order-status.enum';
export declare class OrderBookEventsGateway implements OnGatewayConnection, OnGatewayDisconnect {
    server: Server;
    private readonly logger;
    private clientsByEntityId;
    handleConnection(client: Socket): void;
    handleDisconnect(client: Socket): void;
    subscribeToEntity(clientId: string, entityId: string): void;
    unsubscribeFromEntity(clientId: string, entityId: string): void;
    emitEntityCreated(entity: OrderBookDto): void;
    emitEntityUpdated(entity: OrderBookDto): void;
    emitStatusToggled(id: string, status: OrderStatus): void;
    emitEntityDeleted(id: string, isSoftDelete: boolean): void;
    emitEntityDuplicated(entity: OrderBookDto): void;
    emitEntityRestored(entity: OrderBookDto): void;
}

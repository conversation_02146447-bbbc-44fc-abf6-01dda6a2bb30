{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/bloomgoo/gold-exchange-platform/frontend/node_modules/.pnpm/xmlhttprequest-ssl%402.1.2/node_modules/xmlhttprequest-ssl/lib/XMLHttpRequest.js"], "sourcesContent": ["/**\n * Wrapper for built-in http.js to emulate the browser XMLHttpRequest object.\n *\n * This can be used with JS designed for browsers to improve reuse of code and\n * allow the use of existing libraries.\n *\n * Usage: include(\"XMLHttpRequest.js\") and use XMLHttpRequest per W3C specs.\n *\n * <AUTHOR> <<EMAIL>>\n * @contributor <PERSON> <<EMAIL>>\n * @license MIT\n */\n\nvar fs = require('fs');\nvar Url = require('url');\nvar spawn = require('child_process').spawn;\n\n/**\n * Module exports.\n */\n\nmodule.exports = XMLHttpRequest;\n\n// backwards-compat\nXMLHttpRequest.XMLHttpRequest = XMLHttpRequest;\n\n/**\n * `XMLHttpRequest` constructor.\n *\n * Supported options for the `opts` object are:\n *\n *  - `agent`: An http.Agent instance; http.globalAgent may be used; if 'undefined', agent usage is disabled\n *\n * @param {Object} opts optional \"options\" object\n */\n\nfunction XMLHttpRequest(opts) {\n  \"use strict\";\n\n  opts = opts || {};\n\n  /**\n   * Private variables\n   */\n  var self = this;\n  var http = require('http');\n  var https = require('https');\n\n  // Holds http.js objects\n  var request;\n  var response;\n\n  // Request settings\n  var settings = {};\n\n  // Disable header blacklist.\n  // Not part of XHR specs.\n  var disableHeaderCheck = false;\n\n  // Set some default headers\n  var defaultHeaders = {\n    \"User-Agent\": \"node-XMLHttpRequest\",\n    \"Accept\": \"*/*\"\n  };\n\n  var headers = Object.assign({}, defaultHeaders);\n\n  // These headers are not user setable.\n  // The following are allowed but banned in the spec:\n  // * user-agent\n  var forbiddenRequestHeaders = [\n    \"accept-charset\",\n    \"accept-encoding\",\n    \"access-control-request-headers\",\n    \"access-control-request-method\",\n    \"connection\",\n    \"content-length\",\n    \"content-transfer-encoding\",\n    \"cookie\",\n    \"cookie2\",\n    \"date\",\n    \"expect\",\n    \"host\",\n    \"keep-alive\",\n    \"origin\",\n    \"referer\",\n    \"te\",\n    \"trailer\",\n    \"transfer-encoding\",\n    \"upgrade\",\n    \"via\"\n  ];\n\n  // These request methods are not allowed\n  var forbiddenRequestMethods = [\n    \"TRACE\",\n    \"TRACK\",\n    \"CONNECT\"\n  ];\n\n  // Send flag\n  var sendFlag = false;\n  // Error flag, used when errors occur or abort is called\n  var errorFlag = false;\n  var abortedFlag = false;\n\n  // Event listeners\n  var listeners = {};\n\n  /**\n   * Constants\n   */\n\n  this.UNSENT = 0;\n  this.OPENED = 1;\n  this.HEADERS_RECEIVED = 2;\n  this.LOADING = 3;\n  this.DONE = 4;\n\n  /**\n   * Public vars\n   */\n\n  // Current state\n  this.readyState = this.UNSENT;\n\n  // default ready state change handler in case one is not set or is set late\n  this.onreadystatechange = null;\n\n  // Result & response\n  this.responseText = \"\";\n  this.responseXML = \"\";\n  this.response = Buffer.alloc(0);\n  this.status = null;\n  this.statusText = null;\n\n  /**\n   * Private methods\n   */\n\n  /**\n   * Check if the specified header is allowed.\n   *\n   * @param string header Header to validate\n   * @return boolean False if not allowed, otherwise true\n   */\n  var isAllowedHttpHeader = function(header) {\n    return disableHeaderCheck || (header && forbiddenRequestHeaders.indexOf(header.toLowerCase()) === -1);\n  };\n\n  /**\n   * Check if the specified method is allowed.\n   *\n   * @param string method Request method to validate\n   * @return boolean False if not allowed, otherwise true\n   */\n  var isAllowedHttpMethod = function(method) {\n    return (method && forbiddenRequestMethods.indexOf(method) === -1);\n  };\n\n  /**\n   * Public methods\n   */\n\n  /**\n   * Open the connection. Currently supports local server requests.\n   *\n   * @param string method Connection method (eg GET, POST)\n   * @param string url URL for the connection.\n   * @param boolean async Asynchronous connection. Default is true.\n   * @param string user Username for basic authentication (optional)\n   * @param string password Password for basic authentication (optional)\n   */\n  this.open = function(method, url, async, user, password) {\n    this.abort();\n    errorFlag = false;\n    abortedFlag = false;\n\n    // Check for valid request method\n    if (!isAllowedHttpMethod(method)) {\n      throw new Error(\"SecurityError: Request method not allowed\");\n    }\n\n    settings = {\n      \"method\": method,\n      \"url\": url.toString(),\n      \"async\": (typeof async !== \"boolean\" ? true : async),\n      \"user\": user || null,\n      \"password\": password || null\n    };\n\n    setState(this.OPENED);\n  };\n\n  /**\n   * Disables or enables isAllowedHttpHeader() check the request. Enabled by default.\n   * This does not conform to the W3C spec.\n   *\n   * @param boolean state Enable or disable header checking.\n   */\n  this.setDisableHeaderCheck = function(state) {\n    disableHeaderCheck = state;\n  };\n\n  /**\n   * Sets a header for the request.\n   *\n   * @param string header Header name\n   * @param string value Header value\n   * @return boolean Header added\n   */\n  this.setRequestHeader = function(header, value) {\n    if (this.readyState != this.OPENED) {\n      throw new Error(\"INVALID_STATE_ERR: setRequestHeader can only be called when state is OPEN\");\n    }\n    if (!isAllowedHttpHeader(header)) {\n      console.warn('Refused to set unsafe header \"' + header + '\"');\n      return false;\n    }\n    if (sendFlag) {\n      throw new Error(\"INVALID_STATE_ERR: send flag is true\");\n    }\n    headers[header] = value;\n    return true;\n  };\n\n  /**\n   * Gets a header from the server response.\n   *\n   * @param string header Name of header to get.\n   * @return string Text of the header or null if it doesn't exist.\n   */\n  this.getResponseHeader = function(header) {\n    if (typeof header === \"string\"\n      && this.readyState > this.OPENED\n      && response.headers[header.toLowerCase()]\n      && !errorFlag\n    ) {\n      return response.headers[header.toLowerCase()];\n    }\n\n    return null;\n  };\n\n  /**\n   * Gets all the response headers.\n   *\n   * @return string A string with all response headers separated by CR+LF\n   */\n  this.getAllResponseHeaders = function() {\n    if (this.readyState < this.HEADERS_RECEIVED || errorFlag) {\n      return \"\";\n    }\n    var result = \"\";\n\n    for (var i in response.headers) {\n      // Cookie headers are excluded\n      if (i !== \"set-cookie\" && i !== \"set-cookie2\") {\n        result += i + \": \" + response.headers[i] + \"\\r\\n\";\n      }\n    }\n    return result.substr(0, result.length - 2);\n  };\n\n  /**\n   * Gets a request header\n   *\n   * @param string name Name of header to get\n   * @return string Returns the request header or empty string if not set\n   */\n  this.getRequestHeader = function(name) {\n    // @TODO Make this case insensitive\n    if (typeof name === \"string\" && headers[name]) {\n      return headers[name];\n    }\n\n    return \"\";\n  };\n\n  /**\n   * Sends the request to the server.\n   *\n   * @param string data Optional data to send as request body.\n   */\n  this.send = function(data) {\n    if (this.readyState != this.OPENED) {\n      throw new Error(\"INVALID_STATE_ERR: connection must be opened before send() is called\");\n    }\n\n    if (sendFlag) {\n      throw new Error(\"INVALID_STATE_ERR: send has already been called\");\n    }\n\n    var ssl = false, local = false;\n    var url = Url.parse(settings.url);\n    var host;\n    // Determine the server\n    switch (url.protocol) {\n      case 'https:':\n        ssl = true;\n        // SSL & non-SSL both need host, no break here.\n      case 'http:':\n        host = url.hostname;\n        break;\n\n      case 'file:':\n        local = true;\n        break;\n\n      case undefined:\n      case '':\n        host = \"localhost\";\n        break;\n\n      default:\n        throw new Error(\"Protocol not supported.\");\n    }\n\n    // Load files off the local filesystem (file://)\n    if (local) {\n      if (settings.method !== \"GET\") {\n        throw new Error(\"XMLHttpRequest: Only GET method is supported\");\n      }\n\n      if (settings.async) {\n        fs.readFile(unescape(url.pathname), function(error, data) {\n          if (error) {\n            self.handleError(error, error.errno || -1);\n          } else {\n            self.status = 200;\n            self.responseText = data.toString('utf8');\n            self.response = data;\n            setState(self.DONE);\n          }\n        });\n      } else {\n        try {\n          this.response = fs.readFileSync(unescape(url.pathname));\n          this.responseText = this.response.toString('utf8');\n          this.status = 200;\n          setState(self.DONE);\n        } catch(e) {\n          this.handleError(e, e.errno || -1);\n        }\n      }\n\n      return;\n    }\n\n    // Default to port 80. If accessing localhost on another port be sure\n    // to use http://localhost:port/path\n    var port = url.port || (ssl ? 443 : 80);\n    // Add query string if one is used\n    var uri = url.pathname + (url.search ? url.search : '');\n\n    // Set the Host header or the server may reject the request\n    headers[\"Host\"] = host;\n    if (!((ssl && port === 443) || port === 80)) {\n      headers[\"Host\"] += ':' + url.port;\n    }\n\n    // Set Basic Auth if necessary\n    if (settings.user) {\n      if (typeof settings.password == \"undefined\") {\n        settings.password = \"\";\n      }\n      var authBuf = new Buffer(settings.user + \":\" + settings.password);\n      headers[\"Authorization\"] = \"Basic \" + authBuf.toString(\"base64\");\n    }\n\n    // Set content length header\n    if (settings.method === \"GET\" || settings.method === \"HEAD\") {\n      data = null;\n    } else if (data) {\n      headers[\"Content-Length\"] = Buffer.isBuffer(data) ? data.length : Buffer.byteLength(data);\n\n      var headersKeys = Object.keys(headers);\n      if (!headersKeys.some(function (h) { return h.toLowerCase() === 'content-type' })) {\n        headers[\"Content-Type\"] = \"text/plain;charset=UTF-8\";\n      }\n    } else if (settings.method === \"POST\") {\n      // For a post with no data set Content-Length: 0.\n      // This is required by buggy servers that don't meet the specs.\n      headers[\"Content-Length\"] = 0;\n    }\n\n    var agent = opts.agent || false;\n    var options = {\n      host: host,\n      port: port,\n      path: uri,\n      method: settings.method,\n      headers: headers,\n      agent: agent\n    };\n\n    if (ssl) {\n      options.pfx = opts.pfx;\n      options.key = opts.key;\n      options.passphrase = opts.passphrase;\n      options.cert = opts.cert;\n      options.ca = opts.ca;\n      options.ciphers = opts.ciphers;\n      options.rejectUnauthorized = opts.rejectUnauthorized === false ? false : true;\n    }\n\n    // Reset error flag\n    errorFlag = false;\n    // Handle async requests\n    if (settings.async) {\n      // Use the proper protocol\n      var doRequest = ssl ? https.request : http.request;\n\n      // Request is being sent, set send flag\n      sendFlag = true;\n\n      // As per spec, this is called here for historical reasons.\n      self.dispatchEvent(\"readystatechange\");\n\n      // Handler for the response\n      var responseHandler = function(resp) {\n        // Set response var to the response we got back\n        // This is so it remains accessable outside this scope\n        response = resp;\n        // Check for redirect\n        // @TODO Prevent looped redirects\n        if (response.statusCode === 302 || response.statusCode === 303 || response.statusCode === 307) {\n          // Change URL to the redirect location\n          settings.url = response.headers.location;\n          var url = Url.parse(settings.url);\n          // Set host var in case it's used later\n          host = url.hostname;\n          // Options for the new request\n          var newOptions = {\n            hostname: url.hostname,\n            port: url.port,\n            path: url.path,\n            method: response.statusCode === 303 ? 'GET' : settings.method,\n            headers: headers\n          };\n\n          if (ssl) {\n            newOptions.pfx = opts.pfx;\n            newOptions.key = opts.key;\n            newOptions.passphrase = opts.passphrase;\n            newOptions.cert = opts.cert;\n            newOptions.ca = opts.ca;\n            newOptions.ciphers = opts.ciphers;\n            newOptions.rejectUnauthorized = opts.rejectUnauthorized === false ? false : true;\n          }\n\n          // Issue the new request\n          request = doRequest(newOptions, responseHandler).on('error', errorHandler);\n          request.end();\n          // @TODO Check if an XHR event needs to be fired here\n          return;\n        }\n\n        setState(self.HEADERS_RECEIVED);\n        self.status = response.statusCode;\n\n        response.on('data', function(chunk) {\n          // Make sure there's some data\n          if (chunk) {\n            var data = Buffer.from(chunk);\n            self.response = Buffer.concat([self.response, data]);\n          }\n          // Don't emit state changes if the connection has been aborted.\n          if (sendFlag) {\n            setState(self.LOADING);\n          }\n        });\n\n        response.on('end', function() {\n          if (sendFlag) {\n            // The sendFlag needs to be set before setState is called.  Otherwise if we are chaining callbacks\n            // there can be a timing issue (the callback is called and a new call is made before the flag is reset).\n            sendFlag = false;\n            // Discard the 'end' event if the connection has been aborted\n            setState(self.DONE);\n            // Construct responseText from response\n            self.responseText = self.response.toString('utf8');\n          }\n        });\n\n        response.on('error', function(error) {\n          self.handleError(error);\n        });\n      }\n\n      // Error handler for the request\n      var errorHandler = function(error) {\n        // In the case of https://nodejs.org/api/http.html#requestreusedsocket triggering an ECONNRESET,\n        // don't fail the xhr request, attempt again.\n        if (request.reusedSocket && error.code === 'ECONNRESET')\n          return doRequest(options, responseHandler).on('error', errorHandler);\n        self.handleError(error);\n      }\n\n      // Create the request\n      request = doRequest(options, responseHandler).on('error', errorHandler);\n\n      if (opts.autoUnref) {\n        request.on('socket', (socket) => {\n          socket.unref();\n        });\n      }\n\n      // Node 0.4 and later won't accept empty data. Make sure it's needed.\n      if (data) {\n        request.write(data);\n      }\n\n      request.end();\n\n      self.dispatchEvent(\"loadstart\");\n    } else { // Synchronous\n      // Create a temporary file for communication with the other Node process\n      var contentFile = \".node-xmlhttprequest-content-\" + process.pid;\n      var syncFile = \".node-xmlhttprequest-sync-\" + process.pid;\n      fs.writeFileSync(syncFile, \"\", \"utf8\");\n      // The async request the other Node process executes\n      var execString = \"var http = require('http'), https = require('https'), fs = require('fs');\"\n        + \"var doRequest = http\" + (ssl ? \"s\" : \"\") + \".request;\"\n        + \"var options = \" + JSON.stringify(options) + \";\"\n        + \"var responseText = '';\"\n        + \"var responseData = Buffer.alloc(0);\"\n        + \"var req = doRequest(options, function(response) {\"\n        + \"response.on('data', function(chunk) {\"\n        + \"  var data = Buffer.from(chunk);\"\n        + \"  responseText += data.toString('utf8');\"\n        + \"  responseData = Buffer.concat([responseData, data]);\"\n        + \"});\"\n        + \"response.on('end', function() {\"\n        + \"fs.writeFileSync('\" + contentFile + \"', JSON.stringify({err: null, data: {statusCode: response.statusCode, headers: response.headers, text: responseText, data: responseData.toString('base64')}}), 'utf8');\"\n        + \"fs.unlinkSync('\" + syncFile + \"');\"\n        + \"});\"\n        + \"response.on('error', function(error) {\"\n        + \"fs.writeFileSync('\" + contentFile + \"', 'NODE-XMLHTTPREQUEST-ERROR:' + JSON.stringify(error), 'utf8');\"\n        + \"fs.unlinkSync('\" + syncFile + \"');\"\n        + \"});\"\n        + \"}).on('error', function(error) {\"\n        + \"fs.writeFileSync('\" + contentFile + \"', 'NODE-XMLHTTPREQUEST-ERROR:' + JSON.stringify(error), 'utf8');\"\n        + \"fs.unlinkSync('\" + syncFile + \"');\"\n        + \"});\"\n        + (data ? \"req.write('\" + JSON.stringify(data).slice(1,-1).replace(/'/g, \"\\\\'\") + \"');\":\"\")\n        + \"req.end();\";\n      // Start the other Node Process, executing this string\n      var syncProc = spawn(process.argv[0], [\"-e\", execString]);\n      var statusText;\n      while(fs.existsSync(syncFile)) {\n        // Wait while the sync file is empty\n      }\n      self.responseText = fs.readFileSync(contentFile, 'utf8');\n      // Kill the child process once the file has data\n      syncProc.stdin.end();\n      // Remove the temporary file\n      fs.unlinkSync(contentFile);\n      if (self.responseText.match(/^NODE-XMLHTTPREQUEST-ERROR:/)) {\n        // If the file returned an error, handle it\n        var errorObj = JSON.parse(self.responseText.replace(/^NODE-XMLHTTPREQUEST-ERROR:/, \"\"));\n        self.handleError(errorObj, 503);\n      } else {\n        // If the file returned okay, parse its data and move to the DONE state\n        self.status = self.responseText.replace(/^NODE-XMLHTTPREQUEST-STATUS:([0-9]*),.*/, \"$1\");\n        var resp = JSON.parse(self.responseText.replace(/^NODE-XMLHTTPREQUEST-STATUS:[0-9]*,(.*)/, \"$1\"));\n        response = {\n          statusCode: self.status,\n          headers: resp.data.headers\n        };\n        self.responseText = resp.data.text;\n        self.response = Buffer.from(resp.data.data, 'base64');\n        setState(self.DONE, true);\n      }\n    }\n  };\n\n  /**\n   * Called when an error is encountered to deal with it.\n   * @param  status  {number}    HTTP status code to use rather than the default (0) for XHR errors.\n   */\n  this.handleError = function(error, status) {\n    this.status = status || 0;\n    this.statusText = error;\n    this.responseText = error.stack;\n    errorFlag = true;\n    setState(this.DONE);\n  };\n\n  /**\n   * Aborts a request.\n   */\n  this.abort = function() {\n    if (request) {\n      request.abort();\n      request = null;\n    }\n\n    headers = Object.assign({}, defaultHeaders);\n    this.responseText = \"\";\n    this.responseXML = \"\";\n    this.response = Buffer.alloc(0);\n\n    errorFlag = abortedFlag = true\n    if (this.readyState !== this.UNSENT\n        && (this.readyState !== this.OPENED || sendFlag)\n        && this.readyState !== this.DONE) {\n      sendFlag = false;\n      setState(this.DONE);\n    }\n    this.readyState = this.UNSENT;\n  };\n\n  /**\n   * Adds an event listener. Preferred method of binding to events.\n   */\n  this.addEventListener = function(event, callback) {\n    if (!(event in listeners)) {\n      listeners[event] = [];\n    }\n    // Currently allows duplicate callbacks. Should it?\n    listeners[event].push(callback);\n  };\n\n  /**\n   * Remove an event callback that has already been bound.\n   * Only works on the matching funciton, cannot be a copy.\n   */\n  this.removeEventListener = function(event, callback) {\n    if (event in listeners) {\n      // Filter will return a new array with the callback removed\n      listeners[event] = listeners[event].filter(function(ev) {\n        return ev !== callback;\n      });\n    }\n  };\n\n  /**\n   * Dispatch any events, including both \"on\" methods and events attached using addEventListener.\n   */\n  this.dispatchEvent = function (event) {\n    if (typeof self[\"on\" + event] === \"function\") {\n      if (this.readyState === this.DONE && settings.async)\n        setTimeout(function() { self[\"on\" + event]() }, 0)\n      else\n        self[\"on\" + event]()\n    }\n    if (event in listeners) {\n      for (let i = 0, len = listeners[event].length; i < len; i++) {\n        if (this.readyState === this.DONE)\n          setTimeout(function() { listeners[event][i].call(self) }, 0)\n        else\n          listeners[event][i].call(self)\n      }\n    }\n  };\n\n  /**\n   * Changes readyState and calls onreadystatechange.\n   *\n   * @param int state New state\n   */\n  var setState = function(state) {\n    if ((self.readyState === state) || (self.readyState === self.UNSENT && abortedFlag))\n      return\n\n    self.readyState = state;\n\n    if (settings.async || self.readyState < self.OPENED || self.readyState === self.DONE) {\n      self.dispatchEvent(\"readystatechange\");\n    }\n\n    if (self.readyState === self.DONE) {\n      let fire\n\n      if (abortedFlag)\n        fire = \"abort\"\n      else if (errorFlag)\n        fire = \"error\"\n      else\n        fire = \"load\"\n\n      self.dispatchEvent(fire)\n\n      // @TODO figure out InspectorInstrumentation::didLoadXHR(cookie)\n      self.dispatchEvent(\"loadend\");\n    }\n  };\n};\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;CAWC,GAED,IAAI;AACJ,IAAI;AACJ,IAAI,QAAQ,qFAAyB,KAAK;AAE1C;;CAEC,GAED,OAAO,OAAO,GAAG;AAEjB,mBAAmB;AACnB,eAAe,cAAc,GAAG;AAEhC;;;;;;;;CAQC,GAED,SAAS,eAAe,IAAI;IAC1B;IAEA,OAAO,QAAQ,CAAC;IAEhB;;GAEC,GACD,IAAI,OAAO,IAAI;IACf,IAAI;IACJ,IAAI;IAEJ,wBAAwB;IACxB,IAAI;IACJ,IAAI;IAEJ,mBAAmB;IACnB,IAAI,WAAW,CAAC;IAEhB,4BAA4B;IAC5B,yBAAyB;IACzB,IAAI,qBAAqB;IAEzB,2BAA2B;IAC3B,IAAI,iBAAiB;QACnB,cAAc;QACd,UAAU;IACZ;IAEA,IAAI,UAAU,OAAO,MAAM,CAAC,CAAC,GAAG;IAEhC,sCAAsC;IACtC,oDAAoD;IACpD,eAAe;IACf,IAAI,0BAA0B;QAC5B;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACD;IAED,wCAAwC;IACxC,IAAI,0BAA0B;QAC5B;QACA;QACA;KACD;IAED,YAAY;IACZ,IAAI,WAAW;IACf,wDAAwD;IACxD,IAAI,YAAY;IAChB,IAAI,cAAc;IAElB,kBAAkB;IAClB,IAAI,YAAY,CAAC;IAEjB;;GAEC,GAED,IAAI,CAAC,MAAM,GAAG;IACd,IAAI,CAAC,MAAM,GAAG;IACd,IAAI,CAAC,gBAAgB,GAAG;IACxB,IAAI,CAAC,OAAO,GAAG;IACf,IAAI,CAAC,IAAI,GAAG;IAEZ;;GAEC,GAED,gBAAgB;IAChB,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,MAAM;IAE7B,2EAA2E;IAC3E,IAAI,CAAC,kBAAkB,GAAG;IAE1B,oBAAoB;IACpB,IAAI,CAAC,YAAY,GAAG;IACpB,IAAI,CAAC,WAAW,GAAG;IACnB,IAAI,CAAC,QAAQ,GAAG,OAAO,KAAK,CAAC;IAC7B,IAAI,CAAC,MAAM,GAAG;IACd,IAAI,CAAC,UAAU,GAAG;IAElB;;GAEC,GAED;;;;;GAKC,GACD,IAAI,sBAAsB,SAAS,MAAM;QACvC,OAAO,sBAAuB,UAAU,wBAAwB,OAAO,CAAC,OAAO,WAAW,QAAQ,CAAC;IACrG;IAEA;;;;;GAKC,GACD,IAAI,sBAAsB,SAAS,MAAM;QACvC,OAAQ,UAAU,wBAAwB,OAAO,CAAC,YAAY,CAAC;IACjE;IAEA;;GAEC,GAED;;;;;;;;GAQC,GACD,IAAI,CAAC,IAAI,GAAG,SAAS,MAAM,EAAE,GAAG,EAAE,KAAK,EAAE,IAAI,EAAE,QAAQ;QACrD,IAAI,CAAC,KAAK;QACV,YAAY;QACZ,cAAc;QAEd,iCAAiC;QACjC,IAAI,CAAC,oBAAoB,SAAS;YAChC,MAAM,IAAI,MAAM;QAClB;QAEA,WAAW;YACT,UAAU;YACV,OAAO,IAAI,QAAQ;YACnB,SAAU,OAAO,UAAU,YAAY,OAAO;YAC9C,QAAQ,QAAQ;YAChB,YAAY,YAAY;QAC1B;QAEA,SAAS,IAAI,CAAC,MAAM;IACtB;IAEA;;;;;GAKC,GACD,IAAI,CAAC,qBAAqB,GAAG,SAAS,KAAK;QACzC,qBAAqB;IACvB;IAEA;;;;;;GAMC,GACD,IAAI,CAAC,gBAAgB,GAAG,SAAS,MAAM,EAAE,KAAK;QAC5C,IAAI,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,MAAM,EAAE;YAClC,MAAM,IAAI,MAAM;QAClB;QACA,IAAI,CAAC,oBAAoB,SAAS;YAChC,QAAQ,IAAI,CAAC,mCAAmC,SAAS;YACzD,OAAO;QACT;QACA,IAAI,UAAU;YACZ,MAAM,IAAI,MAAM;QAClB;QACA,OAAO,CAAC,OAAO,GAAG;QAClB,OAAO;IACT;IAEA;;;;;GAKC,GACD,IAAI,CAAC,iBAAiB,GAAG,SAAS,MAAM;QACtC,IAAI,OAAO,WAAW,YACjB,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,MAAM,IAC7B,SAAS,OAAO,CAAC,OAAO,WAAW,GAAG,IACtC,CAAC,WACJ;YACA,OAAO,SAAS,OAAO,CAAC,OAAO,WAAW,GAAG;QAC/C;QAEA,OAAO;IACT;IAEA;;;;GAIC,GACD,IAAI,CAAC,qBAAqB,GAAG;QAC3B,IAAI,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,gBAAgB,IAAI,WAAW;YACxD,OAAO;QACT;QACA,IAAI,SAAS;QAEb,IAAK,IAAI,KAAK,SAAS,OAAO,CAAE;YAC9B,8BAA8B;YAC9B,IAAI,MAAM,gBAAgB,MAAM,eAAe;gBAC7C,UAAU,IAAI,OAAO,SAAS,OAAO,CAAC,EAAE,GAAG;YAC7C;QACF;QACA,OAAO,OAAO,MAAM,CAAC,GAAG,OAAO,MAAM,GAAG;IAC1C;IAEA;;;;;GAKC,GACD,IAAI,CAAC,gBAAgB,GAAG,SAAS,IAAI;QACnC,mCAAmC;QACnC,IAAI,OAAO,SAAS,YAAY,OAAO,CAAC,KAAK,EAAE;YAC7C,OAAO,OAAO,CAAC,KAAK;QACtB;QAEA,OAAO;IACT;IAEA;;;;GAIC,GACD,IAAI,CAAC,IAAI,GAAG,SAAS,IAAI;QACvB,IAAI,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,MAAM,EAAE;YAClC,MAAM,IAAI,MAAM;QAClB;QAEA,IAAI,UAAU;YACZ,MAAM,IAAI,MAAM;QAClB;QAEA,IAAI,MAAM,OAAO,QAAQ;QACzB,IAAI,MAAM,IAAI,KAAK,CAAC,SAAS,GAAG;QAChC,IAAI;QACJ,uBAAuB;QACvB,OAAQ,IAAI,QAAQ;YAClB,KAAK;gBACH,MAAM;YACN,+CAA+C;YACjD,KAAK;gBACH,OAAO,IAAI,QAAQ;gBACnB;YAEF,KAAK;gBACH,QAAQ;gBACR;YAEF,KAAK;YACL,KAAK;gBACH,OAAO;gBACP;YAEF;gBACE,MAAM,IAAI,MAAM;QACpB;QAEA,gDAAgD;QAChD,IAAI,OAAO;YACT,IAAI,SAAS,MAAM,KAAK,OAAO;gBAC7B,MAAM,IAAI,MAAM;YAClB;YAEA,IAAI,SAAS,KAAK,EAAE;gBAClB,GAAG,QAAQ,CAAC,SAAS,IAAI,QAAQ,GAAG,SAAS,KAAK,EAAE,IAAI;oBACtD,IAAI,OAAO;wBACT,KAAK,WAAW,CAAC,OAAO,MAAM,KAAK,IAAI,CAAC;oBAC1C,OAAO;wBACL,KAAK,MAAM,GAAG;wBACd,KAAK,YAAY,GAAG,KAAK,QAAQ,CAAC;wBAClC,KAAK,QAAQ,GAAG;wBAChB,SAAS,KAAK,IAAI;oBACpB;gBACF;YACF,OAAO;gBACL,IAAI;oBACF,IAAI,CAAC,QAAQ,GAAG,GAAG,YAAY,CAAC,SAAS,IAAI,QAAQ;oBACrD,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC;oBAC3C,IAAI,CAAC,MAAM,GAAG;oBACd,SAAS,KAAK,IAAI;gBACpB,EAAE,OAAM,GAAG;oBACT,IAAI,CAAC,WAAW,CAAC,GAAG,EAAE,KAAK,IAAI,CAAC;gBAClC;YACF;YAEA;QACF;QAEA,qEAAqE;QACrE,oCAAoC;QACpC,IAAI,OAAO,IAAI,IAAI,IAAI,CAAC,MAAM,MAAM,EAAE;QACtC,kCAAkC;QAClC,IAAI,MAAM,IAAI,QAAQ,GAAG,CAAC,IAAI,MAAM,GAAG,IAAI,MAAM,GAAG,EAAE;QAEtD,2DAA2D;QAC3D,OAAO,CAAC,OAAO,GAAG;QAClB,IAAI,CAAC,CAAC,AAAC,OAAO,SAAS,OAAQ,SAAS,EAAE,GAAG;YAC3C,OAAO,CAAC,OAAO,IAAI,MAAM,IAAI,IAAI;QACnC;QAEA,8BAA8B;QAC9B,IAAI,SAAS,IAAI,EAAE;YACjB,IAAI,OAAO,SAAS,QAAQ,IAAI,aAAa;gBAC3C,SAAS,QAAQ,GAAG;YACtB;YACA,IAAI,UAAU,IAAI,OAAO,SAAS,IAAI,GAAG,MAAM,SAAS,QAAQ;YAChE,OAAO,CAAC,gBAAgB,GAAG,WAAW,QAAQ,QAAQ,CAAC;QACzD;QAEA,4BAA4B;QAC5B,IAAI,SAAS,MAAM,KAAK,SAAS,SAAS,MAAM,KAAK,QAAQ;YAC3D,OAAO;QACT,OAAO,IAAI,MAAM;YACf,OAAO,CAAC,iBAAiB,GAAG,OAAO,QAAQ,CAAC,QAAQ,KAAK,MAAM,GAAG,OAAO,UAAU,CAAC;YAEpF,IAAI,cAAc,OAAO,IAAI,CAAC;YAC9B,IAAI,CAAC,YAAY,IAAI,CAAC,SAAU,CAAC;gBAAI,OAAO,EAAE,WAAW,OAAO;YAAe,IAAI;gBACjF,OAAO,CAAC,eAAe,GAAG;YAC5B;QACF,OAAO,IAAI,SAAS,MAAM,KAAK,QAAQ;YACrC,iDAAiD;YACjD,+DAA+D;YAC/D,OAAO,CAAC,iBAAiB,GAAG;QAC9B;QAEA,IAAI,QAAQ,KAAK,KAAK,IAAI;QAC1B,IAAI,UAAU;YACZ,MAAM;YACN,MAAM;YACN,MAAM;YACN,QAAQ,SAAS,MAAM;YACvB,SAAS;YACT,OAAO;QACT;QAEA,IAAI,KAAK;YACP,QAAQ,GAAG,GAAG,KAAK,GAAG;YACtB,QAAQ,GAAG,GAAG,KAAK,GAAG;YACtB,QAAQ,UAAU,GAAG,KAAK,UAAU;YACpC,QAAQ,IAAI,GAAG,KAAK,IAAI;YACxB,QAAQ,EAAE,GAAG,KAAK,EAAE;YACpB,QAAQ,OAAO,GAAG,KAAK,OAAO;YAC9B,QAAQ,kBAAkB,GAAG,KAAK,kBAAkB,KAAK,QAAQ,QAAQ;QAC3E;QAEA,mBAAmB;QACnB,YAAY;QACZ,wBAAwB;QACxB,IAAI,SAAS,KAAK,EAAE;YAClB,0BAA0B;YAC1B,IAAI,YAAY,MAAM,MAAM,OAAO,GAAG,KAAK,OAAO;YAElD,uCAAuC;YACvC,WAAW;YAEX,2DAA2D;YAC3D,KAAK,aAAa,CAAC;YAEnB,2BAA2B;YAC3B,IAAI,kBAAkB,SAAS,IAAI;gBACjC,+CAA+C;gBAC/C,sDAAsD;gBACtD,WAAW;gBACX,qBAAqB;gBACrB,iCAAiC;gBACjC,IAAI,SAAS,UAAU,KAAK,OAAO,SAAS,UAAU,KAAK,OAAO,SAAS,UAAU,KAAK,KAAK;oBAC7F,sCAAsC;oBACtC,SAAS,GAAG,GAAG,SAAS,OAAO,CAAC,QAAQ;oBACxC,IAAI,MAAM,IAAI,KAAK,CAAC,SAAS,GAAG;oBAChC,uCAAuC;oBACvC,OAAO,IAAI,QAAQ;oBACnB,8BAA8B;oBAC9B,IAAI,aAAa;wBACf,UAAU,IAAI,QAAQ;wBACtB,MAAM,IAAI,IAAI;wBACd,MAAM,IAAI,IAAI;wBACd,QAAQ,SAAS,UAAU,KAAK,MAAM,QAAQ,SAAS,MAAM;wBAC7D,SAAS;oBACX;oBAEA,IAAI,KAAK;wBACP,WAAW,GAAG,GAAG,KAAK,GAAG;wBACzB,WAAW,GAAG,GAAG,KAAK,GAAG;wBACzB,WAAW,UAAU,GAAG,KAAK,UAAU;wBACvC,WAAW,IAAI,GAAG,KAAK,IAAI;wBAC3B,WAAW,EAAE,GAAG,KAAK,EAAE;wBACvB,WAAW,OAAO,GAAG,KAAK,OAAO;wBACjC,WAAW,kBAAkB,GAAG,KAAK,kBAAkB,KAAK,QAAQ,QAAQ;oBAC9E;oBAEA,wBAAwB;oBACxB,UAAU,UAAU,YAAY,iBAAiB,EAAE,CAAC,SAAS;oBAC7D,QAAQ,GAAG;oBACX,qDAAqD;oBACrD;gBACF;gBAEA,SAAS,KAAK,gBAAgB;gBAC9B,KAAK,MAAM,GAAG,SAAS,UAAU;gBAEjC,SAAS,EAAE,CAAC,QAAQ,SAAS,KAAK;oBAChC,8BAA8B;oBAC9B,IAAI,OAAO;wBACT,IAAI,OAAO,OAAO,IAAI,CAAC;wBACvB,KAAK,QAAQ,GAAG,OAAO,MAAM,CAAC;4BAAC,KAAK,QAAQ;4BAAE;yBAAK;oBACrD;oBACA,+DAA+D;oBAC/D,IAAI,UAAU;wBACZ,SAAS,KAAK,OAAO;oBACvB;gBACF;gBAEA,SAAS,EAAE,CAAC,OAAO;oBACjB,IAAI,UAAU;wBACZ,kGAAkG;wBAClG,wGAAwG;wBACxG,WAAW;wBACX,6DAA6D;wBAC7D,SAAS,KAAK,IAAI;wBAClB,uCAAuC;wBACvC,KAAK,YAAY,GAAG,KAAK,QAAQ,CAAC,QAAQ,CAAC;oBAC7C;gBACF;gBAEA,SAAS,EAAE,CAAC,SAAS,SAAS,KAAK;oBACjC,KAAK,WAAW,CAAC;gBACnB;YACF;YAEA,gCAAgC;YAChC,IAAI,eAAe,SAAS,KAAK;gBAC/B,gGAAgG;gBAChG,6CAA6C;gBAC7C,IAAI,QAAQ,YAAY,IAAI,MAAM,IAAI,KAAK,cACzC,OAAO,UAAU,SAAS,iBAAiB,EAAE,CAAC,SAAS;gBACzD,KAAK,WAAW,CAAC;YACnB;YAEA,qBAAqB;YACrB,UAAU,UAAU,SAAS,iBAAiB,EAAE,CAAC,SAAS;YAE1D,IAAI,KAAK,SAAS,EAAE;gBAClB,QAAQ,EAAE,CAAC,UAAU,CAAC;oBACpB,OAAO,KAAK;gBACd;YACF;YAEA,qEAAqE;YACrE,IAAI,MAAM;gBACR,QAAQ,KAAK,CAAC;YAChB;YAEA,QAAQ,GAAG;YAEX,KAAK,aAAa,CAAC;QACrB,OAAO;YACL,wEAAwE;YACxE,IAAI,cAAc,kCAAkC,QAAQ,GAAG;YAC/D,IAAI,WAAW,+BAA+B,QAAQ,GAAG;YACzD,GAAG,aAAa,CAAC,UAAU,IAAI;YAC/B,oDAAoD;YACpD,IAAI,aAAa,8EACb,yBAAyB,CAAC,MAAM,MAAM,EAAE,IAAI,cAC5C,mBAAmB,KAAK,SAAS,CAAC,WAAW,MAC7C,2BACA,wCACA,sDACA,0CACA,qCACA,6CACA,0DACA,QACA,oCACA,uBAAuB,cAAc,4KACrC,oBAAoB,WAAW,QAC/B,QACA,2CACA,uBAAuB,cAAc,sEACrC,oBAAoB,WAAW,QAC/B,QACA,qCACA,uBAAuB,cAAc,sEACrC,oBAAoB,WAAW,QAC/B,QACA,CAAC,OAAO,gBAAgB,KAAK,SAAS,CAAC,MAAM,KAAK,CAAC,GAAE,CAAC,GAAG,OAAO,CAAC,MAAM,SAAS,QAAM,EAAE,IACxF;YACJ,sDAAsD;YACtD,IAAI,WAAW,MAAM,QAAQ,IAAI,CAAC,EAAE,EAAE;gBAAC;gBAAM;aAAW;YACxD,IAAI;YACJ,MAAM,GAAG,UAAU,CAAC,UAAW;YAC7B,oCAAoC;YACtC;YACA,KAAK,YAAY,GAAG,GAAG,YAAY,CAAC,aAAa;YACjD,gDAAgD;YAChD,SAAS,KAAK,CAAC,GAAG;YAClB,4BAA4B;YAC5B,GAAG,UAAU,CAAC;YACd,IAAI,KAAK,YAAY,CAAC,KAAK,CAAC,gCAAgC;gBAC1D,2CAA2C;gBAC3C,IAAI,WAAW,KAAK,KAAK,CAAC,KAAK,YAAY,CAAC,OAAO,CAAC,+BAA+B;gBACnF,KAAK,WAAW,CAAC,UAAU;YAC7B,OAAO;gBACL,uEAAuE;gBACvE,KAAK,MAAM,GAAG,KAAK,YAAY,CAAC,OAAO,CAAC,2CAA2C;gBACnF,IAAI,OAAO,KAAK,KAAK,CAAC,KAAK,YAAY,CAAC,OAAO,CAAC,2CAA2C;gBAC3F,WAAW;oBACT,YAAY,KAAK,MAAM;oBACvB,SAAS,KAAK,IAAI,CAAC,OAAO;gBAC5B;gBACA,KAAK,YAAY,GAAG,KAAK,IAAI,CAAC,IAAI;gBAClC,KAAK,QAAQ,GAAG,OAAO,IAAI,CAAC,KAAK,IAAI,CAAC,IAAI,EAAE;gBAC5C,SAAS,KAAK,IAAI,EAAE;YACtB;QACF;IACF;IAEA;;;GAGC,GACD,IAAI,CAAC,WAAW,GAAG,SAAS,KAAK,EAAE,MAAM;QACvC,IAAI,CAAC,MAAM,GAAG,UAAU;QACxB,IAAI,CAAC,UAAU,GAAG;QAClB,IAAI,CAAC,YAAY,GAAG,MAAM,KAAK;QAC/B,YAAY;QACZ,SAAS,IAAI,CAAC,IAAI;IACpB;IAEA;;GAEC,GACD,IAAI,CAAC,KAAK,GAAG;QACX,IAAI,SAAS;YACX,QAAQ,KAAK;YACb,UAAU;QACZ;QAEA,UAAU,OAAO,MAAM,CAAC,CAAC,GAAG;QAC5B,IAAI,CAAC,YAAY,GAAG;QACpB,IAAI,CAAC,WAAW,GAAG;QACnB,IAAI,CAAC,QAAQ,GAAG,OAAO,KAAK,CAAC;QAE7B,YAAY,cAAc;QAC1B,IAAI,IAAI,CAAC,UAAU,KAAK,IAAI,CAAC,MAAM,IAC5B,CAAC,IAAI,CAAC,UAAU,KAAK,IAAI,CAAC,MAAM,IAAI,QAAQ,KAC5C,IAAI,CAAC,UAAU,KAAK,IAAI,CAAC,IAAI,EAAE;YACpC,WAAW;YACX,SAAS,IAAI,CAAC,IAAI;QACpB;QACA,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,MAAM;IAC/B;IAEA;;GAEC,GACD,IAAI,CAAC,gBAAgB,GAAG,SAAS,KAAK,EAAE,QAAQ;QAC9C,IAAI,CAAC,CAAC,SAAS,SAAS,GAAG;YACzB,SAAS,CAAC,MAAM,GAAG,EAAE;QACvB;QACA,mDAAmD;QACnD,SAAS,CAAC,MAAM,CAAC,IAAI,CAAC;IACxB;IAEA;;;GAGC,GACD,IAAI,CAAC,mBAAmB,GAAG,SAAS,KAAK,EAAE,QAAQ;QACjD,IAAI,SAAS,WAAW;YACtB,2DAA2D;YAC3D,SAAS,CAAC,MAAM,GAAG,SAAS,CAAC,MAAM,CAAC,MAAM,CAAC,SAAS,EAAE;gBACpD,OAAO,OAAO;YAChB;QACF;IACF;IAEA;;GAEC,GACD,IAAI,CAAC,aAAa,GAAG,SAAU,KAAK;QAClC,IAAI,OAAO,IAAI,CAAC,OAAO,MAAM,KAAK,YAAY;YAC5C,IAAI,IAAI,CAAC,UAAU,KAAK,IAAI,CAAC,IAAI,IAAI,SAAS,KAAK,EACjD,WAAW;gBAAa,IAAI,CAAC,OAAO,MAAM;YAAG,GAAG;iBAEhD,IAAI,CAAC,OAAO,MAAM;QACtB;QACA,IAAI,SAAS,WAAW;YACtB,IAAK,IAAI,IAAI,GAAG,MAAM,SAAS,CAAC,MAAM,CAAC,MAAM,EAAE,IAAI,KAAK,IAAK;gBAC3D,IAAI,IAAI,CAAC,UAAU,KAAK,IAAI,CAAC,IAAI,EAC/B,WAAW;oBAAa,SAAS,CAAC,MAAM,CAAC,EAAE,CAAC,IAAI,CAAC;gBAAM,GAAG;qBAE1D,SAAS,CAAC,MAAM,CAAC,EAAE,CAAC,IAAI,CAAC;YAC7B;QACF;IACF;IAEA;;;;GAIC,GACD,IAAI,WAAW,SAAS,KAAK;QAC3B,IAAI,AAAC,KAAK,UAAU,KAAK,SAAW,KAAK,UAAU,KAAK,KAAK,MAAM,IAAI,aACrE;QAEF,KAAK,UAAU,GAAG;QAElB,IAAI,SAAS,KAAK,IAAI,KAAK,UAAU,GAAG,KAAK,MAAM,IAAI,KAAK,UAAU,KAAK,KAAK,IAAI,EAAE;YACpF,KAAK,aAAa,CAAC;QACrB;QAEA,IAAI,KAAK,UAAU,KAAK,KAAK,IAAI,EAAE;YACjC,IAAI;YAEJ,IAAI,aACF,OAAO;iBACJ,IAAI,WACP,OAAO;iBAEP,OAAO;YAET,KAAK,aAAa,CAAC;YAEnB,gEAAgE;YAChE,KAAK,aAAa,CAAC;QACrB;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 567, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/bloomgoo/gold-exchange-platform/frontend/node_modules/.pnpm/engine.io-parser%405.2.3/node_modules/engine.io-parser/build/esm/commons.js"], "sourcesContent": ["const PACKET_TYPES = Object.create(null); // no Map = no polyfill\nPACKET_TYPES[\"open\"] = \"0\";\nPACKET_TYPES[\"close\"] = \"1\";\nPACKET_TYPES[\"ping\"] = \"2\";\nPACKET_TYPES[\"pong\"] = \"3\";\nPACKET_TYPES[\"message\"] = \"4\";\nPACKET_TYPES[\"upgrade\"] = \"5\";\nPACKET_TYPES[\"noop\"] = \"6\";\nconst PACKET_TYPES_REVERSE = Object.create(null);\nObject.keys(PACKET_TYPES).forEach((key) => {\n    PACKET_TYPES_REVERSE[PACKET_TYPES[key]] = key;\n});\nconst ERROR_PACKET = { type: \"error\", data: \"parser error\" };\nexport { PACKET_TYPES, PACKET_TYPES_REVERSE, ERROR_PACKET };\n"], "names": [], "mappings": ";;;;;AAAA,MAAM,eAAe,OAAO,MAAM,CAAC,OAAO,uBAAuB;AACjE,YAAY,CAAC,OAAO,GAAG;AACvB,YAAY,CAAC,QAAQ,GAAG;AACxB,YAAY,CAAC,OAAO,GAAG;AACvB,YAAY,CAAC,OAAO,GAAG;AACvB,YAAY,CAAC,UAAU,GAAG;AAC1B,YAAY,CAAC,UAAU,GAAG;AAC1B,YAAY,CAAC,OAAO,GAAG;AACvB,MAAM,uBAAuB,OAAO,MAAM,CAAC;AAC3C,OAAO,IAAI,CAAC,cAAc,OAAO,CAAC,CAAC;IAC/B,oBAAoB,CAAC,YAAY,CAAC,IAAI,CAAC,GAAG;AAC9C;AACA,MAAM,eAAe;IAAE,MAAM;IAAS,MAAM;AAAe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 595, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/bloomgoo/gold-exchange-platform/frontend/node_modules/.pnpm/engine.io-parser%405.2.3/node_modules/engine.io-parser/build/esm/encodePacket.js"], "sourcesContent": ["import { PACKET_TYPES } from \"./commons.js\";\nexport const encodePacket = ({ type, data }, supportsBinary, callback) => {\n    if (data instanceof ArrayBuffer || ArrayBuffer.isView(data)) {\n        return callback(supportsBinary ? data : \"b\" + toBuffer(data, true).toString(\"base64\"));\n    }\n    // plain string\n    return callback(PACKET_TYPES[type] + (data || \"\"));\n};\nconst toBuffer = (data, forceBufferConversion) => {\n    if (Buffer.isBuffer(data) ||\n        (data instanceof Uint8Array && !forceBufferConversion)) {\n        return data;\n    }\n    else if (data instanceof ArrayBuffer) {\n        return Buffer.from(data);\n    }\n    else {\n        return Buffer.from(data.buffer, data.byteOffset, data.byteLength);\n    }\n};\nlet TEXT_ENCODER;\nexport function encodePacketToBinary(packet, callback) {\n    if (packet.data instanceof ArrayBuffer || ArrayBuffer.isView(packet.data)) {\n        return callback(toBuffer(packet.data, false));\n    }\n    encodePacket(packet, true, (encoded) => {\n        if (!TEXT_ENCODER) {\n            // lazily created for compatibility with Node.js 10\n            TEXT_ENCODER = new TextEncoder();\n        }\n        callback(TEXT_ENCODER.encode(encoded));\n    });\n}\n"], "names": [], "mappings": ";;;;AAAA;;AACO,MAAM,eAAe,CAAC,EAAE,IAAI,EAAE,IAAI,EAAE,EAAE,gBAAgB;IACzD,IAAI,gBAAgB,eAAe,YAAY,MAAM,CAAC,OAAO;QACzD,OAAO,SAAS,iBAAiB,OAAO,MAAM,SAAS,MAAM,MAAM,QAAQ,CAAC;IAChF;IACA,eAAe;IACf,OAAO,SAAS,oOAAA,CAAA,eAAY,CAAC,KAAK,GAAG,CAAC,QAAQ,EAAE;AACpD;AACA,MAAM,WAAW,CAAC,MAAM;IACpB,IAAI,OAAO,QAAQ,CAAC,SACf,gBAAgB,cAAc,CAAC,uBAAwB;QACxD,OAAO;IACX,OACK,IAAI,gBAAgB,aAAa;QAClC,OAAO,OAAO,IAAI,CAAC;IACvB,OACK;QACD,OAAO,OAAO,IAAI,CAAC,KAAK,MAAM,EAAE,KAAK,UAAU,EAAE,KAAK,UAAU;IACpE;AACJ;AACA,IAAI;AACG,SAAS,qBAAqB,MAAM,EAAE,QAAQ;IACjD,IAAI,OAAO,IAAI,YAAY,eAAe,YAAY,MAAM,CAAC,OAAO,IAAI,GAAG;QACvE,OAAO,SAAS,SAAS,OAAO,IAAI,EAAE;IAC1C;IACA,aAAa,QAAQ,MAAM,CAAC;QACxB,IAAI,CAAC,cAAc;YACf,mDAAmD;YACnD,eAAe,IAAI;QACvB;QACA,SAAS,aAAa,MAAM,CAAC;IACjC;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 636, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/bloomgoo/gold-exchange-platform/frontend/node_modules/.pnpm/engine.io-parser%405.2.3/node_modules/engine.io-parser/build/esm/decodePacket.js"], "sourcesContent": ["import { ERROR_PACKET, PACKET_TYPES_REVERSE, } from \"./commons.js\";\nexport const decodePacket = (encodedPacket, binaryType) => {\n    if (typeof encodedPacket !== \"string\") {\n        return {\n            type: \"message\",\n            data: mapBinary(encodedPacket, binaryType),\n        };\n    }\n    const type = encodedPacket.charAt(0);\n    if (type === \"b\") {\n        const buffer = Buffer.from(encodedPacket.substring(1), \"base64\");\n        return {\n            type: \"message\",\n            data: mapBinary(buffer, binaryType),\n        };\n    }\n    if (!PACKET_TYPES_REVERSE[type]) {\n        return ERROR_PACKET;\n    }\n    return encodedPacket.length > 1\n        ? {\n            type: PACKET_TYPES_REVERSE[type],\n            data: encodedPacket.substring(1),\n        }\n        : {\n            type: PACKET_TYPES_REVERSE[type],\n        };\n};\nconst mapBinary = (data, binaryType) => {\n    switch (binaryType) {\n        case \"arraybuffer\":\n            if (data instanceof ArrayBuffer) {\n                // from WebSocket & binaryType \"arraybuffer\"\n                return data;\n            }\n            else if (Buffer.isBuffer(data)) {\n                // from HTTP long-polling\n                return data.buffer.slice(data.byteOffset, data.byteOffset + data.byteLength);\n            }\n            else {\n                // from WebTransport (Uint8Array)\n                return data.buffer;\n            }\n        case \"nodebuffer\":\n        default:\n            if (Buffer.isBuffer(data)) {\n                // from HTTP long-polling or WebSocket & binaryType \"nodebuffer\" (default)\n                return data;\n            }\n            else {\n                // from WebTransport (Uint8Array)\n                return Buffer.from(data);\n            }\n    }\n};\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,eAAe,CAAC,eAAe;IACxC,IAAI,OAAO,kBAAkB,UAAU;QACnC,OAAO;YACH,MAAM;YACN,MAAM,UAAU,eAAe;QACnC;IACJ;IACA,MAAM,OAAO,cAAc,MAAM,CAAC;IAClC,IAAI,SAAS,KAAK;QACd,MAAM,SAAS,OAAO,IAAI,CAAC,cAAc,SAAS,CAAC,IAAI;QACvD,OAAO;YACH,MAAM;YACN,MAAM,UAAU,QAAQ;QAC5B;IACJ;IACA,IAAI,CAAC,oOAAA,CAAA,uBAAoB,CAAC,KAAK,EAAE;QAC7B,OAAO,oOAAA,CAAA,eAAY;IACvB;IACA,OAAO,cAAc,MAAM,GAAG,IACxB;QACE,MAAM,oOAAA,CAAA,uBAAoB,CAAC,KAAK;QAChC,MAAM,cAAc,SAAS,CAAC;IAClC,IACE;QACE,MAAM,oOAAA,CAAA,uBAAoB,CAAC,KAAK;IACpC;AACR;AACA,MAAM,YAAY,CAAC,MAAM;IACrB,OAAQ;QACJ,KAAK;YACD,IAAI,gBAAgB,aAAa;gBAC7B,4CAA4C;gBAC5C,OAAO;YACX,OACK,IAAI,OAAO,QAAQ,CAAC,OAAO;gBAC5B,yBAAyB;gBACzB,OAAO,KAAK,MAAM,CAAC,KAAK,CAAC,KAAK,UAAU,EAAE,KAAK,UAAU,GAAG,KAAK,UAAU;YAC/E,OACK;gBACD,iCAAiC;gBACjC,OAAO,KAAK,MAAM;YACtB;QACJ,KAAK;QACL;YACI,IAAI,OAAO,QAAQ,CAAC,OAAO;gBACvB,0EAA0E;gBAC1E,OAAO;YACX,OACK;gBACD,iCAAiC;gBACjC,OAAO,OAAO,IAAI,CAAC;YACvB;IACR;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 696, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/bloomgoo/gold-exchange-platform/frontend/node_modules/.pnpm/engine.io-parser%405.2.3/node_modules/engine.io-parser/build/esm/index.js"], "sourcesContent": ["import { encodePacket, encodePacketToBinary } from \"./encodePacket.js\";\nimport { decodePacket } from \"./decodePacket.js\";\nimport { ERROR_PACKET, } from \"./commons.js\";\nconst SEPARATOR = String.fromCharCode(30); // see https://en.wikipedia.org/wiki/Delimiter#ASCII_delimited_text\nconst encodePayload = (packets, callback) => {\n    // some packets may be added to the array while encoding, so the initial length must be saved\n    const length = packets.length;\n    const encodedPackets = new Array(length);\n    let count = 0;\n    packets.forEach((packet, i) => {\n        // force base64 encoding for binary packets\n        encodePacket(packet, false, (encodedPacket) => {\n            encodedPackets[i] = encodedPacket;\n            if (++count === length) {\n                callback(encodedPackets.join(SEPARATOR));\n            }\n        });\n    });\n};\nconst decodePayload = (encodedPayload, binaryType) => {\n    const encodedPackets = encodedPayload.split(SEPARATOR);\n    const packets = [];\n    for (let i = 0; i < encodedPackets.length; i++) {\n        const decodedPacket = decodePacket(encodedPackets[i], binaryType);\n        packets.push(decodedPacket);\n        if (decodedPacket.type === \"error\") {\n            break;\n        }\n    }\n    return packets;\n};\nexport function createPacketEncoderStream() {\n    return new TransformStream({\n        transform(packet, controller) {\n            encodePacketToBinary(packet, (encodedPacket) => {\n                const payloadLength = encodedPacket.length;\n                let header;\n                // inspired by the WebSocket format: https://developer.mozilla.org/en-US/docs/Web/API/WebSockets_API/Writing_WebSocket_servers#decoding_payload_length\n                if (payloadLength < 126) {\n                    header = new Uint8Array(1);\n                    new DataView(header.buffer).setUint8(0, payloadLength);\n                }\n                else if (payloadLength < 65536) {\n                    header = new Uint8Array(3);\n                    const view = new DataView(header.buffer);\n                    view.setUint8(0, 126);\n                    view.setUint16(1, payloadLength);\n                }\n                else {\n                    header = new Uint8Array(9);\n                    const view = new DataView(header.buffer);\n                    view.setUint8(0, 127);\n                    view.setBigUint64(1, BigInt(payloadLength));\n                }\n                // first bit indicates whether the payload is plain text (0) or binary (1)\n                if (packet.data && typeof packet.data !== \"string\") {\n                    header[0] |= 0x80;\n                }\n                controller.enqueue(header);\n                controller.enqueue(encodedPacket);\n            });\n        },\n    });\n}\nlet TEXT_DECODER;\nfunction totalLength(chunks) {\n    return chunks.reduce((acc, chunk) => acc + chunk.length, 0);\n}\nfunction concatChunks(chunks, size) {\n    if (chunks[0].length === size) {\n        return chunks.shift();\n    }\n    const buffer = new Uint8Array(size);\n    let j = 0;\n    for (let i = 0; i < size; i++) {\n        buffer[i] = chunks[0][j++];\n        if (j === chunks[0].length) {\n            chunks.shift();\n            j = 0;\n        }\n    }\n    if (chunks.length && j < chunks[0].length) {\n        chunks[0] = chunks[0].slice(j);\n    }\n    return buffer;\n}\nexport function createPacketDecoderStream(maxPayload, binaryType) {\n    if (!TEXT_DECODER) {\n        TEXT_DECODER = new TextDecoder();\n    }\n    const chunks = [];\n    let state = 0 /* State.READ_HEADER */;\n    let expectedLength = -1;\n    let isBinary = false;\n    return new TransformStream({\n        transform(chunk, controller) {\n            chunks.push(chunk);\n            while (true) {\n                if (state === 0 /* State.READ_HEADER */) {\n                    if (totalLength(chunks) < 1) {\n                        break;\n                    }\n                    const header = concatChunks(chunks, 1);\n                    isBinary = (header[0] & 0x80) === 0x80;\n                    expectedLength = header[0] & 0x7f;\n                    if (expectedLength < 126) {\n                        state = 3 /* State.READ_PAYLOAD */;\n                    }\n                    else if (expectedLength === 126) {\n                        state = 1 /* State.READ_EXTENDED_LENGTH_16 */;\n                    }\n                    else {\n                        state = 2 /* State.READ_EXTENDED_LENGTH_64 */;\n                    }\n                }\n                else if (state === 1 /* State.READ_EXTENDED_LENGTH_16 */) {\n                    if (totalLength(chunks) < 2) {\n                        break;\n                    }\n                    const headerArray = concatChunks(chunks, 2);\n                    expectedLength = new DataView(headerArray.buffer, headerArray.byteOffset, headerArray.length).getUint16(0);\n                    state = 3 /* State.READ_PAYLOAD */;\n                }\n                else if (state === 2 /* State.READ_EXTENDED_LENGTH_64 */) {\n                    if (totalLength(chunks) < 8) {\n                        break;\n                    }\n                    const headerArray = concatChunks(chunks, 8);\n                    const view = new DataView(headerArray.buffer, headerArray.byteOffset, headerArray.length);\n                    const n = view.getUint32(0);\n                    if (n > Math.pow(2, 53 - 32) - 1) {\n                        // the maximum safe integer in JavaScript is 2^53 - 1\n                        controller.enqueue(ERROR_PACKET);\n                        break;\n                    }\n                    expectedLength = n * Math.pow(2, 32) + view.getUint32(4);\n                    state = 3 /* State.READ_PAYLOAD */;\n                }\n                else {\n                    if (totalLength(chunks) < expectedLength) {\n                        break;\n                    }\n                    const data = concatChunks(chunks, expectedLength);\n                    controller.enqueue(decodePacket(isBinary ? data : TEXT_DECODER.decode(data), binaryType));\n                    state = 0 /* State.READ_HEADER */;\n                }\n                if (expectedLength === 0 || expectedLength > maxPayload) {\n                    controller.enqueue(ERROR_PACKET);\n                    break;\n                }\n            }\n        },\n    });\n}\nexport const protocol = 4;\nexport { encodePacket, encodePayload, decodePacket, decodePayload, };\n"], "names": [], "mappings": ";;;;;;;AAAA;AACA;AACA;;;;AACA,MAAM,YAAY,OAAO,YAAY,CAAC,KAAK,mEAAmE;AAC9G,MAAM,gBAAgB,CAAC,SAAS;IAC5B,6FAA6F;IAC7F,MAAM,SAAS,QAAQ,MAAM;IAC7B,MAAM,iBAAiB,IAAI,MAAM;IACjC,IAAI,QAAQ;IACZ,QAAQ,OAAO,CAAC,CAAC,QAAQ;QACrB,2CAA2C;QAC3C,CAAA,GAAA,yOAAA,CAAA,eAAY,AAAD,EAAE,QAAQ,OAAO,CAAC;YACzB,cAAc,CAAC,EAAE,GAAG;YACpB,IAAI,EAAE,UAAU,QAAQ;gBACpB,SAAS,eAAe,IAAI,CAAC;YACjC;QACJ;IACJ;AACJ;AACA,MAAM,gBAAgB,CAAC,gBAAgB;IACnC,MAAM,iBAAiB,eAAe,KAAK,CAAC;IAC5C,MAAM,UAAU,EAAE;IAClB,IAAK,IAAI,IAAI,GAAG,IAAI,eAAe,MAAM,EAAE,IAAK;QAC5C,MAAM,gBAAgB,CAAA,GAAA,yOAAA,CAAA,eAAY,AAAD,EAAE,cAAc,CAAC,EAAE,EAAE;QACtD,QAAQ,IAAI,CAAC;QACb,IAAI,cAAc,IAAI,KAAK,SAAS;YAChC;QACJ;IACJ;IACA,OAAO;AACX;AACO,SAAS;IACZ,OAAO,IAAI,gBAAgB;QACvB,WAAU,MAAM,EAAE,UAAU;YACxB,CAAA,GAAA,yOAAA,CAAA,uBAAoB,AAAD,EAAE,QAAQ,CAAC;gBAC1B,MAAM,gBAAgB,cAAc,MAAM;gBAC1C,IAAI;gBACJ,sJAAsJ;gBACtJ,IAAI,gBAAgB,KAAK;oBACrB,SAAS,IAAI,WAAW;oBACxB,IAAI,SAAS,OAAO,MAAM,EAAE,QAAQ,CAAC,GAAG;gBAC5C,OACK,IAAI,gBAAgB,OAAO;oBAC5B,SAAS,IAAI,WAAW;oBACxB,MAAM,OAAO,IAAI,SAAS,OAAO,MAAM;oBACvC,KAAK,QAAQ,CAAC,GAAG;oBACjB,KAAK,SAAS,CAAC,GAAG;gBACtB,OACK;oBACD,SAAS,IAAI,WAAW;oBACxB,MAAM,OAAO,IAAI,SAAS,OAAO,MAAM;oBACvC,KAAK,QAAQ,CAAC,GAAG;oBACjB,KAAK,YAAY,CAAC,GAAG,OAAO;gBAChC;gBACA,0EAA0E;gBAC1E,IAAI,OAAO,IAAI,IAAI,OAAO,OAAO,IAAI,KAAK,UAAU;oBAChD,MAAM,CAAC,EAAE,IAAI;gBACjB;gBACA,WAAW,OAAO,CAAC;gBACnB,WAAW,OAAO,CAAC;YACvB;QACJ;IACJ;AACJ;AACA,IAAI;AACJ,SAAS,YAAY,MAAM;IACvB,OAAO,OAAO,MAAM,CAAC,CAAC,KAAK,QAAU,MAAM,MAAM,MAAM,EAAE;AAC7D;AACA,SAAS,aAAa,MAAM,EAAE,IAAI;IAC9B,IAAI,MAAM,CAAC,EAAE,CAAC,MAAM,KAAK,MAAM;QAC3B,OAAO,OAAO,KAAK;IACvB;IACA,MAAM,SAAS,IAAI,WAAW;IAC9B,IAAI,IAAI;IACR,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,IAAK;QAC3B,MAAM,CAAC,EAAE,GAAG,MAAM,CAAC,EAAE,CAAC,IAAI;QAC1B,IAAI,MAAM,MAAM,CAAC,EAAE,CAAC,MAAM,EAAE;YACxB,OAAO,KAAK;YACZ,IAAI;QACR;IACJ;IACA,IAAI,OAAO,MAAM,IAAI,IAAI,MAAM,CAAC,EAAE,CAAC,MAAM,EAAE;QACvC,MAAM,CAAC,EAAE,GAAG,MAAM,CAAC,EAAE,CAAC,KAAK,CAAC;IAChC;IACA,OAAO;AACX;AACO,SAAS,0BAA0B,UAAU,EAAE,UAAU;IAC5D,IAAI,CAAC,cAAc;QACf,eAAe,IAAI;IACvB;IACA,MAAM,SAAS,EAAE;IACjB,IAAI,QAAQ,EAAE,qBAAqB;IACnC,IAAI,iBAAiB,CAAC;IACtB,IAAI,WAAW;IACf,OAAO,IAAI,gBAAgB;QACvB,WAAU,KAAK,EAAE,UAAU;YACvB,OAAO,IAAI,CAAC;YACZ,MAAO,KAAM;gBACT,IAAI,UAAU,EAAE,qBAAqB,KAAI;oBACrC,IAAI,YAAY,UAAU,GAAG;wBACzB;oBACJ;oBACA,MAAM,SAAS,aAAa,QAAQ;oBACpC,WAAW,CAAC,MAAM,CAAC,EAAE,GAAG,IAAI,MAAM;oBAClC,iBAAiB,MAAM,CAAC,EAAE,GAAG;oBAC7B,IAAI,iBAAiB,KAAK;wBACtB,QAAQ,EAAE,sBAAsB;oBACpC,OACK,IAAI,mBAAmB,KAAK;wBAC7B,QAAQ,EAAE,iCAAiC;oBAC/C,OACK;wBACD,QAAQ,EAAE,iCAAiC;oBAC/C;gBACJ,OACK,IAAI,UAAU,EAAE,iCAAiC,KAAI;oBACtD,IAAI,YAAY,UAAU,GAAG;wBACzB;oBACJ;oBACA,MAAM,cAAc,aAAa,QAAQ;oBACzC,iBAAiB,IAAI,SAAS,YAAY,MAAM,EAAE,YAAY,UAAU,EAAE,YAAY,MAAM,EAAE,SAAS,CAAC;oBACxG,QAAQ,EAAE,sBAAsB;gBACpC,OACK,IAAI,UAAU,EAAE,iCAAiC,KAAI;oBACtD,IAAI,YAAY,UAAU,GAAG;wBACzB;oBACJ;oBACA,MAAM,cAAc,aAAa,QAAQ;oBACzC,MAAM,OAAO,IAAI,SAAS,YAAY,MAAM,EAAE,YAAY,UAAU,EAAE,YAAY,MAAM;oBACxF,MAAM,IAAI,KAAK,SAAS,CAAC;oBACzB,IAAI,IAAI,KAAK,GAAG,CAAC,GAAG,KAAK,MAAM,GAAG;wBAC9B,qDAAqD;wBACrD,WAAW,OAAO,CAAC,oOAAA,CAAA,eAAY;wBAC/B;oBACJ;oBACA,iBAAiB,IAAI,KAAK,GAAG,CAAC,GAAG,MAAM,KAAK,SAAS,CAAC;oBACtD,QAAQ,EAAE,sBAAsB;gBACpC,OACK;oBACD,IAAI,YAAY,UAAU,gBAAgB;wBACtC;oBACJ;oBACA,MAAM,OAAO,aAAa,QAAQ;oBAClC,WAAW,OAAO,CAAC,CAAA,GAAA,yOAAA,CAAA,eAAY,AAAD,EAAE,WAAW,OAAO,aAAa,MAAM,CAAC,OAAO;oBAC7E,QAAQ,EAAE,qBAAqB;gBACnC;gBACA,IAAI,mBAAmB,KAAK,iBAAiB,YAAY;oBACrD,WAAW,OAAO,CAAC,oOAAA,CAAA,eAAY;oBAC/B;gBACJ;YACJ;QACJ;IACJ;AACJ;AACO,MAAM,WAAW", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 872, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/bloomgoo/gold-exchange-platform/frontend/node_modules/.pnpm/%40socket.io%2Bcomponent-emitter%403.1.2/node_modules/%40socket.io/component-emitter/lib/esm/index.js"], "sourcesContent": ["/**\n * Initialize a new `Emitter`.\n *\n * @api public\n */\n\nexport function Emitter(obj) {\n  if (obj) return mixin(obj);\n}\n\n/**\n * Mixin the emitter properties.\n *\n * @param {Object} obj\n * @return {Object}\n * @api private\n */\n\nfunction mixin(obj) {\n  for (var key in Emitter.prototype) {\n    obj[key] = Emitter.prototype[key];\n  }\n  return obj;\n}\n\n/**\n * Listen on the given `event` with `fn`.\n *\n * @param {String} event\n * @param {Function} fn\n * @return {Emitter}\n * @api public\n */\n\nEmitter.prototype.on =\nEmitter.prototype.addEventListener = function(event, fn){\n  this._callbacks = this._callbacks || {};\n  (this._callbacks['$' + event] = this._callbacks['$' + event] || [])\n    .push(fn);\n  return this;\n};\n\n/**\n * Adds an `event` listener that will be invoked a single\n * time then automatically removed.\n *\n * @param {String} event\n * @param {Function} fn\n * @return {Emitter}\n * @api public\n */\n\nEmitter.prototype.once = function(event, fn){\n  function on() {\n    this.off(event, on);\n    fn.apply(this, arguments);\n  }\n\n  on.fn = fn;\n  this.on(event, on);\n  return this;\n};\n\n/**\n * Remove the given callback for `event` or all\n * registered callbacks.\n *\n * @param {String} event\n * @param {Function} fn\n * @return {Emitter}\n * @api public\n */\n\nEmitter.prototype.off =\nEmitter.prototype.removeListener =\nEmitter.prototype.removeAllListeners =\nEmitter.prototype.removeEventListener = function(event, fn){\n  this._callbacks = this._callbacks || {};\n\n  // all\n  if (0 == arguments.length) {\n    this._callbacks = {};\n    return this;\n  }\n\n  // specific event\n  var callbacks = this._callbacks['$' + event];\n  if (!callbacks) return this;\n\n  // remove all handlers\n  if (1 == arguments.length) {\n    delete this._callbacks['$' + event];\n    return this;\n  }\n\n  // remove specific handler\n  var cb;\n  for (var i = 0; i < callbacks.length; i++) {\n    cb = callbacks[i];\n    if (cb === fn || cb.fn === fn) {\n      callbacks.splice(i, 1);\n      break;\n    }\n  }\n\n  // Remove event specific arrays for event types that no\n  // one is subscribed for to avoid memory leak.\n  if (callbacks.length === 0) {\n    delete this._callbacks['$' + event];\n  }\n\n  return this;\n};\n\n/**\n * Emit `event` with the given args.\n *\n * @param {String} event\n * @param {Mixed} ...\n * @return {Emitter}\n */\n\nEmitter.prototype.emit = function(event){\n  this._callbacks = this._callbacks || {};\n\n  var args = new Array(arguments.length - 1)\n    , callbacks = this._callbacks['$' + event];\n\n  for (var i = 1; i < arguments.length; i++) {\n    args[i - 1] = arguments[i];\n  }\n\n  if (callbacks) {\n    callbacks = callbacks.slice(0);\n    for (var i = 0, len = callbacks.length; i < len; ++i) {\n      callbacks[i].apply(this, args);\n    }\n  }\n\n  return this;\n};\n\n// alias used for reserved events (protected method)\nEmitter.prototype.emitReserved = Emitter.prototype.emit;\n\n/**\n * Return array of callbacks for `event`.\n *\n * @param {String} event\n * @return {Array}\n * @api public\n */\n\nEmitter.prototype.listeners = function(event){\n  this._callbacks = this._callbacks || {};\n  return this._callbacks['$' + event] || [];\n};\n\n/**\n * Check if this emitter has `event` handlers.\n *\n * @param {String} event\n * @return {Boolean}\n * @api public\n */\n\nEmitter.prototype.hasListeners = function(event){\n  return !! this.listeners(event).length;\n};\n"], "names": [], "mappings": "AAAA;;;;CAIC;;;AAEM,SAAS,QAAQ,GAAG;IACzB,IAAI,KAAK,OAAO,MAAM;AACxB;AAEA;;;;;;CAMC,GAED,SAAS,MAAM,GAAG;IAChB,IAAK,IAAI,OAAO,QAAQ,SAAS,CAAE;QACjC,GAAG,CAAC,IAAI,GAAG,QAAQ,SAAS,CAAC,IAAI;IACnC;IACA,OAAO;AACT;AAEA;;;;;;;CAOC,GAED,QAAQ,SAAS,CAAC,EAAE,GACpB,QAAQ,SAAS,CAAC,gBAAgB,GAAG,SAAS,KAAK,EAAE,EAAE;IACrD,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,UAAU,IAAI,CAAC;IACtC,CAAC,IAAI,CAAC,UAAU,CAAC,MAAM,MAAM,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM,MAAM,IAAI,EAAE,EAC/D,IAAI,CAAC;IACR,OAAO,IAAI;AACb;AAEA;;;;;;;;CAQC,GAED,QAAQ,SAAS,CAAC,IAAI,GAAG,SAAS,KAAK,EAAE,EAAE;IACzC,SAAS;QACP,IAAI,CAAC,GAAG,CAAC,OAAO;QAChB,GAAG,KAAK,CAAC,IAAI,EAAE;IACjB;IAEA,GAAG,EAAE,GAAG;IACR,IAAI,CAAC,EAAE,CAAC,OAAO;IACf,OAAO,IAAI;AACb;AAEA;;;;;;;;CAQC,GAED,QAAQ,SAAS,CAAC,GAAG,GACrB,QAAQ,SAAS,CAAC,cAAc,GAChC,QAAQ,SAAS,CAAC,kBAAkB,GACpC,QAAQ,SAAS,CAAC,mBAAmB,GAAG,SAAS,KAAK,EAAE,EAAE;IACxD,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,UAAU,IAAI,CAAC;IAEtC,MAAM;IACN,IAAI,KAAK,UAAU,MAAM,EAAE;QACzB,IAAI,CAAC,UAAU,GAAG,CAAC;QACnB,OAAO,IAAI;IACb;IAEA,iBAAiB;IACjB,IAAI,YAAY,IAAI,CAAC,UAAU,CAAC,MAAM,MAAM;IAC5C,IAAI,CAAC,WAAW,OAAO,IAAI;IAE3B,sBAAsB;IACtB,IAAI,KAAK,UAAU,MAAM,EAAE;QACzB,OAAO,IAAI,CAAC,UAAU,CAAC,MAAM,MAAM;QACnC,OAAO,IAAI;IACb;IAEA,0BAA0B;IAC1B,IAAI;IACJ,IAAK,IAAI,IAAI,GAAG,IAAI,UAAU,MAAM,EAAE,IAAK;QACzC,KAAK,SAAS,CAAC,EAAE;QACjB,IAAI,OAAO,MAAM,GAAG,EAAE,KAAK,IAAI;YAC7B,UAAU,MAAM,CAAC,GAAG;YACpB;QACF;IACF;IAEA,uDAAuD;IACvD,8CAA8C;IAC9C,IAAI,UAAU,MAAM,KAAK,GAAG;QAC1B,OAAO,IAAI,CAAC,UAAU,CAAC,MAAM,MAAM;IACrC;IAEA,OAAO,IAAI;AACb;AAEA;;;;;;CAMC,GAED,QAAQ,SAAS,CAAC,IAAI,GAAG,SAAS,KAAK;IACrC,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,UAAU,IAAI,CAAC;IAEtC,IAAI,OAAO,IAAI,MAAM,UAAU,MAAM,GAAG,IACpC,YAAY,IAAI,CAAC,UAAU,CAAC,MAAM,MAAM;IAE5C,IAAK,IAAI,IAAI,GAAG,IAAI,UAAU,MAAM,EAAE,IAAK;QACzC,IAAI,CAAC,IAAI,EAAE,GAAG,SAAS,CAAC,EAAE;IAC5B;IAEA,IAAI,WAAW;QACb,YAAY,UAAU,KAAK,CAAC;QAC5B,IAAK,IAAI,IAAI,GAAG,MAAM,UAAU,MAAM,EAAE,IAAI,KAAK,EAAE,EAAG;YACpD,SAAS,CAAC,EAAE,CAAC,KAAK,CAAC,IAAI,EAAE;QAC3B;IACF;IAEA,OAAO,IAAI;AACb;AAEA,oDAAoD;AACpD,QAAQ,SAAS,CAAC,YAAY,GAAG,QAAQ,SAAS,CAAC,IAAI;AAEvD;;;;;;CAMC,GAED,QAAQ,SAAS,CAAC,SAAS,GAAG,SAAS,KAAK;IAC1C,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,UAAU,IAAI,CAAC;IACtC,OAAO,IAAI,CAAC,UAAU,CAAC,MAAM,MAAM,IAAI,EAAE;AAC3C;AAEA;;;;;;CAMC,GAED,QAAQ,SAAS,CAAC,YAAY,GAAG,SAAS,KAAK;IAC7C,OAAO,CAAC,CAAE,IAAI,CAAC,SAAS,CAAC,OAAO,MAAM;AACxC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1009, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/bloomgoo/gold-exchange-platform/frontend/node_modules/.pnpm/engine.io-client%406.6.3/node_modules/engine.io-client/build/esm-debug/globals.node.js"], "sourcesContent": ["export const nextTick = process.nextTick;\nexport const globalThisShim = global;\nexport const defaultBinaryType = \"nodebuffer\";\nexport function createCookieJar() {\n    return new CookieJar();\n}\n/**\n * @see https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/Set-Cookie\n */\nexport function parse(setCookieString) {\n    const parts = setCookieString.split(\"; \");\n    const i = parts[0].indexOf(\"=\");\n    if (i === -1) {\n        return;\n    }\n    const name = parts[0].substring(0, i).trim();\n    if (!name.length) {\n        return;\n    }\n    let value = parts[0].substring(i + 1).trim();\n    if (value.charCodeAt(0) === 0x22) {\n        // remove double quotes\n        value = value.slice(1, -1);\n    }\n    const cookie = {\n        name,\n        value,\n    };\n    for (let j = 1; j < parts.length; j++) {\n        const subParts = parts[j].split(\"=\");\n        if (subParts.length !== 2) {\n            continue;\n        }\n        const key = subParts[0].trim();\n        const value = subParts[1].trim();\n        switch (key) {\n            case \"Expires\":\n                cookie.expires = new Date(value);\n                break;\n            case \"Max-Age\":\n                const expiration = new Date();\n                expiration.setUTCSeconds(expiration.getUTCSeconds() + parseInt(value, 10));\n                cookie.expires = expiration;\n                break;\n            default:\n            // ignore other keys\n        }\n    }\n    return cookie;\n}\nexport class CookieJar {\n    constructor() {\n        this._cookies = new Map();\n    }\n    parseCookies(values) {\n        if (!values) {\n            return;\n        }\n        values.forEach((value) => {\n            const parsed = parse(value);\n            if (parsed) {\n                this._cookies.set(parsed.name, parsed);\n            }\n        });\n    }\n    get cookies() {\n        const now = Date.now();\n        this._cookies.forEach((cookie, name) => {\n            var _a;\n            if (((_a = cookie.expires) === null || _a === void 0 ? void 0 : _a.getTime()) < now) {\n                this._cookies.delete(name);\n            }\n        });\n        return this._cookies.entries();\n    }\n    addCookies(xhr) {\n        const cookies = [];\n        for (const [name, cookie] of this.cookies) {\n            cookies.push(`${name}=${cookie.value}`);\n        }\n        if (cookies.length) {\n            xhr.setDisableHeaderCheck(true);\n            xhr.setRequestHeader(\"cookie\", cookies.join(\"; \"));\n        }\n    }\n    appendCookies(headers) {\n        for (const [name, cookie] of this.cookies) {\n            headers.append(\"cookie\", `${name}=${cookie.value}`);\n        }\n    }\n}\n"], "names": [], "mappings": ";;;;;;;;AAAO,MAAM,WAAW,QAAQ,QAAQ;AACjC,MAAM,iBAAiB;AACvB,MAAM,oBAAoB;AAC1B,SAAS;IACZ,OAAO,IAAI;AACf;AAIO,SAAS,MAAM,eAAe;IACjC,MAAM,QAAQ,gBAAgB,KAAK,CAAC;IACpC,MAAM,IAAI,KAAK,CAAC,EAAE,CAAC,OAAO,CAAC;IAC3B,IAAI,MAAM,CAAC,GAAG;QACV;IACJ;IACA,MAAM,OAAO,KAAK,CAAC,EAAE,CAAC,SAAS,CAAC,GAAG,GAAG,IAAI;IAC1C,IAAI,CAAC,KAAK,MAAM,EAAE;QACd;IACJ;IACA,IAAI,QAAQ,KAAK,CAAC,EAAE,CAAC,SAAS,CAAC,IAAI,GAAG,IAAI;IAC1C,IAAI,MAAM,UAAU,CAAC,OAAO,MAAM;QAC9B,uBAAuB;QACvB,QAAQ,MAAM,KAAK,CAAC,GAAG,CAAC;IAC5B;IACA,MAAM,SAAS;QACX;QACA;IACJ;IACA,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,MAAM,EAAE,IAAK;QACnC,MAAM,WAAW,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC;QAChC,IAAI,SAAS,MAAM,KAAK,GAAG;YACvB;QACJ;QACA,MAAM,MAAM,QAAQ,CAAC,EAAE,CAAC,IAAI;QAC5B,MAAM,QAAQ,QAAQ,CAAC,EAAE,CAAC,IAAI;QAC9B,OAAQ;YACJ,KAAK;gBACD,OAAO,OAAO,GAAG,IAAI,KAAK;gBAC1B;YACJ,KAAK;gBACD,MAAM,aAAa,IAAI;gBACvB,WAAW,aAAa,CAAC,WAAW,aAAa,KAAK,SAAS,OAAO;gBACtE,OAAO,OAAO,GAAG;gBACjB;YACJ;QAEJ;IACJ;IACA,OAAO;AACX;AACO,MAAM;IACT,aAAc;QACV,IAAI,CAAC,QAAQ,GAAG,IAAI;IACxB;IACA,aAAa,MAAM,EAAE;QACjB,IAAI,CAAC,QAAQ;YACT;QACJ;QACA,OAAO,OAAO,CAAC,CAAC;YACZ,MAAM,SAAS,MAAM;YACrB,IAAI,QAAQ;gBACR,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,OAAO,IAAI,EAAE;YACnC;QACJ;IACJ;IACA,IAAI,UAAU;QACV,MAAM,MAAM,KAAK,GAAG;QACpB,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,QAAQ;YAC3B,IAAI;YACJ,IAAI,CAAC,CAAC,KAAK,OAAO,OAAO,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,IAAI,GAAG,OAAO,EAAE,IAAI,KAAK;gBACjF,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC;YACzB;QACJ;QACA,OAAO,IAAI,CAAC,QAAQ,CAAC,OAAO;IAChC;IACA,WAAW,GAAG,EAAE;QACZ,MAAM,UAAU,EAAE;QAClB,KAAK,MAAM,CAAC,MAAM,OAAO,IAAI,IAAI,CAAC,OAAO,CAAE;YACvC,QAAQ,IAAI,CAAC,GAAG,KAAK,CAAC,EAAE,OAAO,KAAK,EAAE;QAC1C;QACA,IAAI,QAAQ,MAAM,EAAE;YAChB,IAAI,qBAAqB,CAAC;YAC1B,IAAI,gBAAgB,CAAC,UAAU,QAAQ,IAAI,CAAC;QAChD;IACJ;IACA,cAAc,OAAO,EAAE;QACnB,KAAK,MAAM,CAAC,MAAM,OAAO,IAAI,IAAI,CAAC,OAAO,CAAE;YACvC,QAAQ,MAAM,CAAC,UAAU,GAAG,KAAK,CAAC,EAAE,OAAO,KAAK,EAAE;QACtD;IACJ;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1110, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/bloomgoo/gold-exchange-platform/frontend/node_modules/.pnpm/engine.io-client%406.6.3/node_modules/engine.io-client/build/esm-debug/util.js"], "sourcesContent": ["import { globalThisShim as globalThis } from \"./globals.node.js\";\nexport function pick(obj, ...attr) {\n    return attr.reduce((acc, k) => {\n        if (obj.hasOwnProperty(k)) {\n            acc[k] = obj[k];\n        }\n        return acc;\n    }, {});\n}\n// Keep a reference to the real timeout functions so they can be used when overridden\nconst NATIVE_SET_TIMEOUT = globalThis.setTimeout;\nconst NATIVE_CLEAR_TIMEOUT = globalThis.clearTimeout;\nexport function installTimerFunctions(obj, opts) {\n    if (opts.useNativeTimers) {\n        obj.setTimeoutFn = NATIVE_SET_TIMEOUT.bind(globalThis);\n        obj.clearTimeoutFn = NATIVE_CLEAR_TIMEOUT.bind(globalThis);\n    }\n    else {\n        obj.setTimeoutFn = globalThis.setTimeout.bind(globalThis);\n        obj.clearTimeoutFn = globalThis.clearTimeout.bind(globalThis);\n    }\n}\n// base64 encoded buffers are about 33% bigger (https://en.wikipedia.org/wiki/Base64)\nconst BASE64_OVERHEAD = 1.33;\n// we could also have used `new Blob([obj]).size`, but it isn't supported in IE9\nexport function byteLength(obj) {\n    if (typeof obj === \"string\") {\n        return utf8Length(obj);\n    }\n    // arraybuffer or blob\n    return Math.ceil((obj.byteLength || obj.size) * BASE64_OVERHEAD);\n}\nfunction utf8Length(str) {\n    let c = 0, length = 0;\n    for (let i = 0, l = str.length; i < l; i++) {\n        c = str.charCodeAt(i);\n        if (c < 0x80) {\n            length += 1;\n        }\n        else if (c < 0x800) {\n            length += 2;\n        }\n        else if (c < 0xd800 || c >= 0xe000) {\n            length += 3;\n        }\n        else {\n            i++;\n            length += 4;\n        }\n    }\n    return length;\n}\n/**\n * Generates a random 8-characters string.\n */\nexport function randomString() {\n    return (Date.now().toString(36).substring(3) +\n        Math.random().toString(36).substring(2, 5));\n}\n"], "names": [], "mappings": ";;;;;;AAAA;;AACO,SAAS,KAAK,GAAG,EAAE,GAAG,IAAI;IAC7B,OAAO,KAAK,MAAM,CAAC,CAAC,KAAK;QACrB,IAAI,IAAI,cAAc,CAAC,IAAI;YACvB,GAAG,CAAC,EAAE,GAAG,GAAG,CAAC,EAAE;QACnB;QACA,OAAO;IACX,GAAG,CAAC;AACR;AACA,qFAAqF;AACrF,MAAM,qBAAqB,qPAAA,CAAA,iBAAU,CAAC,UAAU;AAChD,MAAM,uBAAuB,qPAAA,CAAA,iBAAU,CAAC,YAAY;AAC7C,SAAS,sBAAsB,GAAG,EAAE,IAAI;IAC3C,IAAI,KAAK,eAAe,EAAE;QACtB,IAAI,YAAY,GAAG,mBAAmB,IAAI,CAAC,qPAAA,CAAA,iBAAU;QACrD,IAAI,cAAc,GAAG,qBAAqB,IAAI,CAAC,qPAAA,CAAA,iBAAU;IAC7D,OACK;QACD,IAAI,YAAY,GAAG,qPAAA,CAAA,iBAAU,CAAC,UAAU,CAAC,IAAI,CAAC,qPAAA,CAAA,iBAAU;QACxD,IAAI,cAAc,GAAG,qPAAA,CAAA,iBAAU,CAAC,YAAY,CAAC,IAAI,CAAC,qPAAA,CAAA,iBAAU;IAChE;AACJ;AACA,qFAAqF;AACrF,MAAM,kBAAkB;AAEjB,SAAS,WAAW,GAAG;IAC1B,IAAI,OAAO,QAAQ,UAAU;QACzB,OAAO,WAAW;IACtB;IACA,sBAAsB;IACtB,OAAO,KAAK,IAAI,CAAC,CAAC,IAAI,UAAU,IAAI,IAAI,IAAI,IAAI;AACpD;AACA,SAAS,WAAW,GAAG;IACnB,IAAI,IAAI,GAAG,SAAS;IACpB,IAAK,IAAI,IAAI,GAAG,IAAI,IAAI,MAAM,EAAE,IAAI,GAAG,IAAK;QACxC,IAAI,IAAI,UAAU,CAAC;QACnB,IAAI,IAAI,MAAM;YACV,UAAU;QACd,OACK,IAAI,IAAI,OAAO;YAChB,UAAU;QACd,OACK,IAAI,IAAI,UAAU,KAAK,QAAQ;YAChC,UAAU;QACd,OACK;YACD;YACA,UAAU;QACd;IACJ;IACA,OAAO;AACX;AAIO,SAAS;IACZ,OAAQ,KAAK,GAAG,GAAG,QAAQ,CAAC,IAAI,SAAS,CAAC,KACtC,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,SAAS,CAAC,GAAG;AAChD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1173, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/bloomgoo/gold-exchange-platform/frontend/node_modules/.pnpm/engine.io-client%406.6.3/node_modules/engine.io-client/build/esm-debug/contrib/parseqs.js"], "sourcesContent": ["// imported from https://github.com/galkn/querystring\n/**\n * Compiles a querystring\n * Returns string representation of the object\n *\n * @param {Object}\n * @api private\n */\nexport function encode(obj) {\n    let str = '';\n    for (let i in obj) {\n        if (obj.hasOwnProperty(i)) {\n            if (str.length)\n                str += '&';\n            str += encodeURIComponent(i) + '=' + encodeURIComponent(obj[i]);\n        }\n    }\n    return str;\n}\n/**\n * Parses a simple querystring into an object\n *\n * @param {String} qs\n * @api private\n */\nexport function decode(qs) {\n    let qry = {};\n    let pairs = qs.split('&');\n    for (let i = 0, l = pairs.length; i < l; i++) {\n        let pair = pairs[i].split('=');\n        qry[decodeURIComponent(pair[0])] = decodeURIComponent(pair[1]);\n    }\n    return qry;\n}\n"], "names": [], "mappings": "AAAA,qDAAqD;AACrD;;;;;;CAMC;;;;AACM,SAAS,OAAO,GAAG;IACtB,IAAI,MAAM;IACV,IAAK,IAAI,KAAK,IAAK;QACf,IAAI,IAAI,cAAc,CAAC,IAAI;YACvB,IAAI,IAAI,MAAM,EACV,OAAO;YACX,OAAO,mBAAmB,KAAK,MAAM,mBAAmB,GAAG,CAAC,EAAE;QAClE;IACJ;IACA,OAAO;AACX;AAOO,SAAS,OAAO,EAAE;IACrB,IAAI,MAAM,CAAC;IACX,IAAI,QAAQ,GAAG,KAAK,CAAC;IACrB,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,MAAM,EAAE,IAAI,GAAG,IAAK;QAC1C,IAAI,OAAO,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC;QAC1B,GAAG,CAAC,mBAAmB,IAAI,CAAC,EAAE,EAAE,GAAG,mBAAmB,IAAI,CAAC,EAAE;IACjE;IACA,OAAO;AACX", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1209, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/bloomgoo/gold-exchange-platform/frontend/node_modules/.pnpm/engine.io-client%406.6.3/node_modules/engine.io-client/build/esm-debug/transport.js"], "sourcesContent": ["import { decodePacket } from \"engine.io-parser\";\nimport { Emitter } from \"@socket.io/component-emitter\";\nimport { installTimerFunctions } from \"./util.js\";\nimport { encode } from \"./contrib/parseqs.js\";\nimport debugModule from \"debug\"; // debug()\nconst debug = debugModule(\"engine.io-client:transport\"); // debug()\nexport class TransportError extends Error {\n    constructor(reason, description, context) {\n        super(reason);\n        this.description = description;\n        this.context = context;\n        this.type = \"TransportError\";\n    }\n}\nexport class Transport extends Emitter {\n    /**\n     * Transport abstract constructor.\n     *\n     * @param {Object} opts - options\n     * @protected\n     */\n    constructor(opts) {\n        super();\n        this.writable = false;\n        installTimerFunctions(this, opts);\n        this.opts = opts;\n        this.query = opts.query;\n        this.socket = opts.socket;\n        this.supportsBinary = !opts.forceBase64;\n    }\n    /**\n     * Emits an error.\n     *\n     * @param {String} reason\n     * @param description\n     * @param context - the error context\n     * @return {Transport} for chaining\n     * @protected\n     */\n    onError(reason, description, context) {\n        super.emitReserved(\"error\", new TransportError(reason, description, context));\n        return this;\n    }\n    /**\n     * Opens the transport.\n     */\n    open() {\n        this.readyState = \"opening\";\n        this.doOpen();\n        return this;\n    }\n    /**\n     * Closes the transport.\n     */\n    close() {\n        if (this.readyState === \"opening\" || this.readyState === \"open\") {\n            this.doClose();\n            this.onClose();\n        }\n        return this;\n    }\n    /**\n     * Sends multiple packets.\n     *\n     * @param {Array} packets\n     */\n    send(packets) {\n        if (this.readyState === \"open\") {\n            this.write(packets);\n        }\n        else {\n            // this might happen if the transport was silently closed in the beforeunload event handler\n            debug(\"transport is not open, discarding packets\");\n        }\n    }\n    /**\n     * Called upon open\n     *\n     * @protected\n     */\n    onOpen() {\n        this.readyState = \"open\";\n        this.writable = true;\n        super.emitReserved(\"open\");\n    }\n    /**\n     * Called with data.\n     *\n     * @param {String} data\n     * @protected\n     */\n    onData(data) {\n        const packet = decodePacket(data, this.socket.binaryType);\n        this.onPacket(packet);\n    }\n    /**\n     * Called with a decoded packet.\n     *\n     * @protected\n     */\n    onPacket(packet) {\n        super.emitReserved(\"packet\", packet);\n    }\n    /**\n     * Called upon close.\n     *\n     * @protected\n     */\n    onClose(details) {\n        this.readyState = \"closed\";\n        super.emitReserved(\"close\", details);\n    }\n    /**\n     * Pauses the transport, in order not to lose packets during an upgrade.\n     *\n     * @param onPause\n     */\n    pause(onPause) { }\n    createUri(schema, query = {}) {\n        return (schema +\n            \"://\" +\n            this._hostname() +\n            this._port() +\n            this.opts.path +\n            this._query(query));\n    }\n    _hostname() {\n        const hostname = this.opts.hostname;\n        return hostname.indexOf(\":\") === -1 ? hostname : \"[\" + hostname + \"]\";\n    }\n    _port() {\n        if (this.opts.port &&\n            ((this.opts.secure && Number(this.opts.port !== 443)) ||\n                (!this.opts.secure && Number(this.opts.port) !== 80))) {\n            return \":\" + this.opts.port;\n        }\n        else {\n            return \"\";\n        }\n    }\n    _query(query) {\n        const encodedQuery = encode(query);\n        return encodedQuery.length ? \"?\" + encodedQuery : \"\";\n    }\n}\n"], "names": [], "mappings": ";;;;AAAA;AAAA;AACA;AACA;AACA;AACA,0TAAiC,UAAU;;;;;;AAC3C,MAAM,QAAQ,CAAA,GAAA,uLAAA,CAAA,UAAW,AAAD,EAAE,+BAA+B,UAAU;AAC5D,MAAM,uBAAuB;IAChC,YAAY,MAAM,EAAE,WAAW,EAAE,OAAO,CAAE;QACtC,KAAK,CAAC;QACN,IAAI,CAAC,WAAW,GAAG;QACnB,IAAI,CAAC,OAAO,GAAG;QACf,IAAI,CAAC,IAAI,GAAG;IAChB;AACJ;AACO,MAAM,kBAAkB,gQAAA,CAAA,UAAO;IAClC;;;;;KAKC,GACD,YAAY,IAAI,CAAE;QACd,KAAK;QACL,IAAI,CAAC,QAAQ,GAAG;QAChB,CAAA,GAAA,0OAAA,CAAA,wBAAqB,AAAD,EAAE,IAAI,EAAE;QAC5B,IAAI,CAAC,IAAI,GAAG;QACZ,IAAI,CAAC,KAAK,GAAG,KAAK,KAAK;QACvB,IAAI,CAAC,MAAM,GAAG,KAAK,MAAM;QACzB,IAAI,CAAC,cAAc,GAAG,CAAC,KAAK,WAAW;IAC3C;IACA;;;;;;;;KAQC,GACD,QAAQ,MAAM,EAAE,WAAW,EAAE,OAAO,EAAE;QAClC,KAAK,CAAC,aAAa,SAAS,IAAI,eAAe,QAAQ,aAAa;QACpE,OAAO,IAAI;IACf;IACA;;KAEC,GACD,OAAO;QACH,IAAI,CAAC,UAAU,GAAG;QAClB,IAAI,CAAC,MAAM;QACX,OAAO,IAAI;IACf;IACA;;KAEC,GACD,QAAQ;QACJ,IAAI,IAAI,CAAC,UAAU,KAAK,aAAa,IAAI,CAAC,UAAU,KAAK,QAAQ;YAC7D,IAAI,CAAC,OAAO;YACZ,IAAI,CAAC,OAAO;QAChB;QACA,OAAO,IAAI;IACf;IACA;;;;KAIC,GACD,KAAK,OAAO,EAAE;QACV,IAAI,IAAI,CAAC,UAAU,KAAK,QAAQ;YAC5B,IAAI,CAAC,KAAK,CAAC;QACf,OACK;YACD,2FAA2F;YAC3F,MAAM;QACV;IACJ;IACA;;;;KAIC,GACD,SAAS;QACL,IAAI,CAAC,UAAU,GAAG;QAClB,IAAI,CAAC,QAAQ,GAAG;QAChB,KAAK,CAAC,aAAa;IACvB;IACA;;;;;KAKC,GACD,OAAO,IAAI,EAAE;QACT,MAAM,SAAS,CAAA,GAAA,yOAAA,CAAA,eAAY,AAAD,EAAE,MAAM,IAAI,CAAC,MAAM,CAAC,UAAU;QACxD,IAAI,CAAC,QAAQ,CAAC;IAClB;IACA;;;;KAIC,GACD,SAAS,MAAM,EAAE;QACb,KAAK,CAAC,aAAa,UAAU;IACjC;IACA;;;;KAIC,GACD,QAAQ,OAAO,EAAE;QACb,IAAI,CAAC,UAAU,GAAG;QAClB,KAAK,CAAC,aAAa,SAAS;IAChC;IACA;;;;KAIC,GACD,MAAM,OAAO,EAAE,CAAE;IACjB,UAAU,MAAM,EAAE,QAAQ,CAAC,CAAC,EAAE;QAC1B,OAAQ,SACJ,QACA,IAAI,CAAC,SAAS,KACd,IAAI,CAAC,KAAK,KACV,IAAI,CAAC,IAAI,CAAC,IAAI,GACd,IAAI,CAAC,MAAM,CAAC;IACpB;IACA,YAAY;QACR,MAAM,WAAW,IAAI,CAAC,IAAI,CAAC,QAAQ;QACnC,OAAO,SAAS,OAAO,CAAC,SAAS,CAAC,IAAI,WAAW,MAAM,WAAW;IACtE;IACA,QAAQ;QACJ,IAAI,IAAI,CAAC,IAAI,CAAC,IAAI,IACd,CAAC,AAAC,IAAI,CAAC,IAAI,CAAC,MAAM,IAAI,OAAO,IAAI,CAAC,IAAI,CAAC,IAAI,KAAK,QAC3C,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,IAAI,OAAO,IAAI,CAAC,IAAI,CAAC,IAAI,MAAM,EAAG,GAAG;YAC3D,OAAO,MAAM,IAAI,CAAC,IAAI,CAAC,IAAI;QAC/B,OACK;YACD,OAAO;QACX;IACJ;IACA,OAAO,KAAK,EAAE;QACV,MAAM,eAAe,CAAA,GAAA,wPAAA,CAAA,SAAM,AAAD,EAAE;QAC5B,OAAO,aAAa,MAAM,GAAG,MAAM,eAAe;IACtD;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1351, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/bloomgoo/gold-exchange-platform/frontend/node_modules/.pnpm/engine.io-client%406.6.3/node_modules/engine.io-client/build/esm-debug/transports/polling.js"], "sourcesContent": ["import { Transport } from \"../transport.js\";\nimport { randomString } from \"../util.js\";\nimport { encodePayload, decodePayload } from \"engine.io-parser\";\nimport debugModule from \"debug\"; // debug()\nconst debug = debugModule(\"engine.io-client:polling\"); // debug()\nexport class Polling extends Transport {\n    constructor() {\n        super(...arguments);\n        this._polling = false;\n    }\n    get name() {\n        return \"polling\";\n    }\n    /**\n     * Opens the socket (triggers polling). We write a PING message to determine\n     * when the transport is open.\n     *\n     * @protected\n     */\n    doOpen() {\n        this._poll();\n    }\n    /**\n     * Pauses polling.\n     *\n     * @param {Function} onPause - callback upon buffers are flushed and transport is paused\n     * @package\n     */\n    pause(onPause) {\n        this.readyState = \"pausing\";\n        const pause = () => {\n            debug(\"paused\");\n            this.readyState = \"paused\";\n            onPause();\n        };\n        if (this._polling || !this.writable) {\n            let total = 0;\n            if (this._polling) {\n                debug(\"we are currently polling - waiting to pause\");\n                total++;\n                this.once(\"pollComplete\", function () {\n                    debug(\"pre-pause polling complete\");\n                    --total || pause();\n                });\n            }\n            if (!this.writable) {\n                debug(\"we are currently writing - waiting to pause\");\n                total++;\n                this.once(\"drain\", function () {\n                    debug(\"pre-pause writing complete\");\n                    --total || pause();\n                });\n            }\n        }\n        else {\n            pause();\n        }\n    }\n    /**\n     * Starts polling cycle.\n     *\n     * @private\n     */\n    _poll() {\n        debug(\"polling\");\n        this._polling = true;\n        this.doPoll();\n        this.emitReserved(\"poll\");\n    }\n    /**\n     * Overloads onData to detect payloads.\n     *\n     * @protected\n     */\n    onData(data) {\n        debug(\"polling got data %s\", data);\n        const callback = (packet) => {\n            // if its the first message we consider the transport open\n            if (\"opening\" === this.readyState && packet.type === \"open\") {\n                this.onOpen();\n            }\n            // if its a close packet, we close the ongoing requests\n            if (\"close\" === packet.type) {\n                this.onClose({ description: \"transport closed by the server\" });\n                return false;\n            }\n            // otherwise bypass onData and handle the message\n            this.onPacket(packet);\n        };\n        // decode payload\n        decodePayload(data, this.socket.binaryType).forEach(callback);\n        // if an event did not trigger closing\n        if (\"closed\" !== this.readyState) {\n            // if we got data we're not polling\n            this._polling = false;\n            this.emitReserved(\"pollComplete\");\n            if (\"open\" === this.readyState) {\n                this._poll();\n            }\n            else {\n                debug('ignoring poll - transport state \"%s\"', this.readyState);\n            }\n        }\n    }\n    /**\n     * For polling, send a close packet.\n     *\n     * @protected\n     */\n    doClose() {\n        const close = () => {\n            debug(\"writing close packet\");\n            this.write([{ type: \"close\" }]);\n        };\n        if (\"open\" === this.readyState) {\n            debug(\"transport open - closing\");\n            close();\n        }\n        else {\n            // in case we're trying to close while\n            // handshaking is in progress (GH-164)\n            debug(\"transport not open - deferring close\");\n            this.once(\"open\", close);\n        }\n    }\n    /**\n     * Writes a packets payload.\n     *\n     * @param {Array} packets - data packets\n     * @protected\n     */\n    write(packets) {\n        this.writable = false;\n        encodePayload(packets, (data) => {\n            this.doWrite(data, () => {\n                this.writable = true;\n                this.emitReserved(\"drain\");\n            });\n        });\n    }\n    /**\n     * Generates uri for connection.\n     *\n     * @private\n     */\n    uri() {\n        const schema = this.opts.secure ? \"https\" : \"http\";\n        const query = this.query || {};\n        // cache busting is forced\n        if (false !== this.opts.timestampRequests) {\n            query[this.opts.timestampParam] = randomString();\n        }\n        if (!this.supportsBinary && !query.sid) {\n            query.b64 = 1;\n        }\n        return this.createUri(schema, query);\n    }\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AAAA;AACA,0TAAiC,UAAU;;;;;AAC3C,MAAM,QAAQ,CAAA,GAAA,uLAAA,CAAA,UAAW,AAAD,EAAE,6BAA6B,UAAU;AAC1D,MAAM,gBAAgB,+OAAA,CAAA,YAAS;IAClC,aAAc;QACV,KAAK,IAAI;QACT,IAAI,CAAC,QAAQ,GAAG;IACpB;IACA,IAAI,OAAO;QACP,OAAO;IACX;IACA;;;;;KAKC,GACD,SAAS;QACL,IAAI,CAAC,KAAK;IACd;IACA;;;;;KAKC,GACD,MAAM,OAAO,EAAE;QACX,IAAI,CAAC,UAAU,GAAG;QAClB,MAAM,QAAQ;YACV,MAAM;YACN,IAAI,CAAC,UAAU,GAAG;YAClB;QACJ;QACA,IAAI,IAAI,CAAC,QAAQ,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;YACjC,IAAI,QAAQ;YACZ,IAAI,IAAI,CAAC,QAAQ,EAAE;gBACf,MAAM;gBACN;gBACA,IAAI,CAAC,IAAI,CAAC,gBAAgB;oBACtB,MAAM;oBACN,EAAE,SAAS;gBACf;YACJ;YACA,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;gBAChB,MAAM;gBACN;gBACA,IAAI,CAAC,IAAI,CAAC,SAAS;oBACf,MAAM;oBACN,EAAE,SAAS;gBACf;YACJ;QACJ,OACK;YACD;QACJ;IACJ;IACA;;;;KAIC,GACD,QAAQ;QACJ,MAAM;QACN,IAAI,CAAC,QAAQ,GAAG;QAChB,IAAI,CAAC,MAAM;QACX,IAAI,CAAC,YAAY,CAAC;IACtB;IACA;;;;KAIC,GACD,OAAO,IAAI,EAAE;QACT,MAAM,uBAAuB;QAC7B,MAAM,WAAW,CAAC;YACd,0DAA0D;YAC1D,IAAI,cAAc,IAAI,CAAC,UAAU,IAAI,OAAO,IAAI,KAAK,QAAQ;gBACzD,IAAI,CAAC,MAAM;YACf;YACA,uDAAuD;YACvD,IAAI,YAAY,OAAO,IAAI,EAAE;gBACzB,IAAI,CAAC,OAAO,CAAC;oBAAE,aAAa;gBAAiC;gBAC7D,OAAO;YACX;YACA,iDAAiD;YACjD,IAAI,CAAC,QAAQ,CAAC;QAClB;QACA,iBAAiB;QACjB,CAAA,GAAA,kPAAA,CAAA,gBAAa,AAAD,EAAE,MAAM,IAAI,CAAC,MAAM,CAAC,UAAU,EAAE,OAAO,CAAC;QACpD,sCAAsC;QACtC,IAAI,aAAa,IAAI,CAAC,UAAU,EAAE;YAC9B,mCAAmC;YACnC,IAAI,CAAC,QAAQ,GAAG;YAChB,IAAI,CAAC,YAAY,CAAC;YAClB,IAAI,WAAW,IAAI,CAAC,UAAU,EAAE;gBAC5B,IAAI,CAAC,KAAK;YACd,OACK;gBACD,MAAM,wCAAwC,IAAI,CAAC,UAAU;YACjE;QACJ;IACJ;IACA;;;;KAIC,GACD,UAAU;QACN,MAAM,QAAQ;YACV,MAAM;YACN,IAAI,CAAC,KAAK,CAAC;gBAAC;oBAAE,MAAM;gBAAQ;aAAE;QAClC;QACA,IAAI,WAAW,IAAI,CAAC,UAAU,EAAE;YAC5B,MAAM;YACN;QACJ,OACK;YACD,sCAAsC;YACtC,sCAAsC;YACtC,MAAM;YACN,IAAI,CAAC,IAAI,CAAC,QAAQ;QACtB;IACJ;IACA;;;;;KAKC,GACD,MAAM,OAAO,EAAE;QACX,IAAI,CAAC,QAAQ,GAAG;QAChB,CAAA,GAAA,kPAAA,CAAA,gBAAa,AAAD,EAAE,SAAS,CAAC;YACpB,IAAI,CAAC,OAAO,CAAC,MAAM;gBACf,IAAI,CAAC,QAAQ,GAAG;gBAChB,IAAI,CAAC,YAAY,CAAC;YACtB;QACJ;IACJ;IACA;;;;KAIC,GACD,MAAM;QACF,MAAM,SAAS,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,UAAU;QAC5C,MAAM,QAAQ,IAAI,CAAC,KAAK,IAAI,CAAC;QAC7B,0BAA0B;QAC1B,IAAI,UAAU,IAAI,CAAC,IAAI,CAAC,iBAAiB,EAAE;YACvC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,GAAG,CAAA,GAAA,0OAAA,CAAA,eAAY,AAAD;QACjD;QACA,IAAI,CAAC,IAAI,CAAC,cAAc,IAAI,CAAC,MAAM,GAAG,EAAE;YACpC,MAAM,GAAG,GAAG;QAChB;QACA,OAAO,IAAI,CAAC,SAAS,CAAC,QAAQ;IAClC;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1519, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/bloomgoo/gold-exchange-platform/frontend/node_modules/.pnpm/engine.io-client%406.6.3/node_modules/engine.io-client/build/esm-debug/contrib/has-cors.js"], "sourcesContent": ["// imported from https://github.com/component/has-cors\nlet value = false;\ntry {\n    value = typeof XMLHttpRequest !== 'undefined' &&\n        'withCredentials' in new XMLHttpRequest();\n}\ncatch (err) {\n    // if XMLHttp support is disabled in IE then it will throw\n    // when trying to create\n}\nexport const hasCORS = value;\n"], "names": [], "mappings": "AAAA,sDAAsD;;;;AACtD,IAAI,QAAQ;AACZ,IAAI;IACA,QAAQ,OAAO,mBAAmB,eAC9B,qBAAqB,IAAI;AACjC,EACA,OAAO,KAAK;AACR,0DAA0D;AAC1D,wBAAwB;AAC5B;AACO,MAAM,UAAU", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1537, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/bloomgoo/gold-exchange-platform/frontend/node_modules/.pnpm/engine.io-client%406.6.3/node_modules/engine.io-client/build/esm-debug/transports/polling-xhr.js"], "sourcesContent": ["import { Polling } from \"./polling.js\";\nimport { Emitter } from \"@socket.io/component-emitter\";\nimport { installTimerFunctions, pick } from \"../util.js\";\nimport { globalThisShim as globalThis } from \"../globals.node.js\";\nimport { hasCORS } from \"../contrib/has-cors.js\";\nimport debugModule from \"debug\"; // debug()\nconst debug = debugModule(\"engine.io-client:polling\"); // debug()\nfunction empty() { }\nexport class BaseXHR extends Polling {\n    /**\n     * XHR Polling constructor.\n     *\n     * @param {Object} opts\n     * @package\n     */\n    constructor(opts) {\n        super(opts);\n        if (typeof location !== \"undefined\") {\n            const isSSL = \"https:\" === location.protocol;\n            let port = location.port;\n            // some user agents have empty `location.port`\n            if (!port) {\n                port = isSSL ? \"443\" : \"80\";\n            }\n            this.xd =\n                (typeof location !== \"undefined\" &&\n                    opts.hostname !== location.hostname) ||\n                    port !== opts.port;\n        }\n    }\n    /**\n     * Sends data.\n     *\n     * @param {String} data to send.\n     * @param {Function} called upon flush.\n     * @private\n     */\n    doWrite(data, fn) {\n        const req = this.request({\n            method: \"POST\",\n            data: data,\n        });\n        req.on(\"success\", fn);\n        req.on(\"error\", (xhrStatus, context) => {\n            this.onError(\"xhr post error\", xhrStatus, context);\n        });\n    }\n    /**\n     * Starts a poll cycle.\n     *\n     * @private\n     */\n    doPoll() {\n        debug(\"xhr poll\");\n        const req = this.request();\n        req.on(\"data\", this.onData.bind(this));\n        req.on(\"error\", (xhrStatus, context) => {\n            this.onError(\"xhr poll error\", xhrStatus, context);\n        });\n        this.pollXhr = req;\n    }\n}\nexport class Request extends Emitter {\n    /**\n     * Request constructor\n     *\n     * @param {Object} options\n     * @package\n     */\n    constructor(createRequest, uri, opts) {\n        super();\n        this.createRequest = createRequest;\n        installTimerFunctions(this, opts);\n        this._opts = opts;\n        this._method = opts.method || \"GET\";\n        this._uri = uri;\n        this._data = undefined !== opts.data ? opts.data : null;\n        this._create();\n    }\n    /**\n     * Creates the XHR object and sends the request.\n     *\n     * @private\n     */\n    _create() {\n        var _a;\n        const opts = pick(this._opts, \"agent\", \"pfx\", \"key\", \"passphrase\", \"cert\", \"ca\", \"ciphers\", \"rejectUnauthorized\", \"autoUnref\");\n        opts.xdomain = !!this._opts.xd;\n        const xhr = (this._xhr = this.createRequest(opts));\n        try {\n            debug(\"xhr open %s: %s\", this._method, this._uri);\n            xhr.open(this._method, this._uri, true);\n            try {\n                if (this._opts.extraHeaders) {\n                    // @ts-ignore\n                    xhr.setDisableHeaderCheck && xhr.setDisableHeaderCheck(true);\n                    for (let i in this._opts.extraHeaders) {\n                        if (this._opts.extraHeaders.hasOwnProperty(i)) {\n                            xhr.setRequestHeader(i, this._opts.extraHeaders[i]);\n                        }\n                    }\n                }\n            }\n            catch (e) { }\n            if (\"POST\" === this._method) {\n                try {\n                    xhr.setRequestHeader(\"Content-type\", \"text/plain;charset=UTF-8\");\n                }\n                catch (e) { }\n            }\n            try {\n                xhr.setRequestHeader(\"Accept\", \"*/*\");\n            }\n            catch (e) { }\n            (_a = this._opts.cookieJar) === null || _a === void 0 ? void 0 : _a.addCookies(xhr);\n            // ie6 check\n            if (\"withCredentials\" in xhr) {\n                xhr.withCredentials = this._opts.withCredentials;\n            }\n            if (this._opts.requestTimeout) {\n                xhr.timeout = this._opts.requestTimeout;\n            }\n            xhr.onreadystatechange = () => {\n                var _a;\n                if (xhr.readyState === 3) {\n                    (_a = this._opts.cookieJar) === null || _a === void 0 ? void 0 : _a.parseCookies(\n                    // @ts-ignore\n                    xhr.getResponseHeader(\"set-cookie\"));\n                }\n                if (4 !== xhr.readyState)\n                    return;\n                if (200 === xhr.status || 1223 === xhr.status) {\n                    this._onLoad();\n                }\n                else {\n                    // make sure the `error` event handler that's user-set\n                    // does not throw in the same tick and gets caught here\n                    this.setTimeoutFn(() => {\n                        this._onError(typeof xhr.status === \"number\" ? xhr.status : 0);\n                    }, 0);\n                }\n            };\n            debug(\"xhr data %s\", this._data);\n            xhr.send(this._data);\n        }\n        catch (e) {\n            // Need to defer since .create() is called directly from the constructor\n            // and thus the 'error' event can only be only bound *after* this exception\n            // occurs.  Therefore, also, we cannot throw here at all.\n            this.setTimeoutFn(() => {\n                this._onError(e);\n            }, 0);\n            return;\n        }\n        if (typeof document !== \"undefined\") {\n            this._index = Request.requestsCount++;\n            Request.requests[this._index] = this;\n        }\n    }\n    /**\n     * Called upon error.\n     *\n     * @private\n     */\n    _onError(err) {\n        this.emitReserved(\"error\", err, this._xhr);\n        this._cleanup(true);\n    }\n    /**\n     * Cleans up house.\n     *\n     * @private\n     */\n    _cleanup(fromError) {\n        if (\"undefined\" === typeof this._xhr || null === this._xhr) {\n            return;\n        }\n        this._xhr.onreadystatechange = empty;\n        if (fromError) {\n            try {\n                this._xhr.abort();\n            }\n            catch (e) { }\n        }\n        if (typeof document !== \"undefined\") {\n            delete Request.requests[this._index];\n        }\n        this._xhr = null;\n    }\n    /**\n     * Called upon load.\n     *\n     * @private\n     */\n    _onLoad() {\n        const data = this._xhr.responseText;\n        if (data !== null) {\n            this.emitReserved(\"data\", data);\n            this.emitReserved(\"success\");\n            this._cleanup();\n        }\n    }\n    /**\n     * Aborts the request.\n     *\n     * @package\n     */\n    abort() {\n        this._cleanup();\n    }\n}\nRequest.requestsCount = 0;\nRequest.requests = {};\n/**\n * Aborts pending requests when unloading the window. This is needed to prevent\n * memory leaks (e.g. when using IE) and to ensure that no spurious error is\n * emitted.\n */\nif (typeof document !== \"undefined\") {\n    // @ts-ignore\n    if (typeof attachEvent === \"function\") {\n        // @ts-ignore\n        attachEvent(\"onunload\", unloadHandler);\n    }\n    else if (typeof addEventListener === \"function\") {\n        const terminationEvent = \"onpagehide\" in globalThis ? \"pagehide\" : \"unload\";\n        addEventListener(terminationEvent, unloadHandler, false);\n    }\n}\nfunction unloadHandler() {\n    for (let i in Request.requests) {\n        if (Request.requests.hasOwnProperty(i)) {\n            Request.requests[i].abort();\n        }\n    }\n}\nconst hasXHR2 = (function () {\n    const xhr = newRequest({\n        xdomain: false,\n    });\n    return xhr && xhr.responseType !== null;\n})();\n/**\n * HTTP long-polling based on the built-in `XMLHttpRequest` object.\n *\n * Usage: browser\n *\n * @see https://developer.mozilla.org/en-US/docs/Web/API/XMLHttpRequest\n */\nexport class XHR extends BaseXHR {\n    constructor(opts) {\n        super(opts);\n        const forceBase64 = opts && opts.forceBase64;\n        this.supportsBinary = hasXHR2 && !forceBase64;\n    }\n    request(opts = {}) {\n        Object.assign(opts, { xd: this.xd }, this.opts);\n        return new Request(newRequest, this.uri(), opts);\n    }\n}\nfunction newRequest(opts) {\n    const xdomain = opts.xdomain;\n    // XMLHttpRequest can be disabled on IE\n    try {\n        if (\"undefined\" !== typeof XMLHttpRequest && (!xdomain || hasCORS)) {\n            return new XMLHttpRequest();\n        }\n    }\n    catch (e) { }\n    if (!xdomain) {\n        try {\n            return new globalThis[[\"Active\"].concat(\"Object\").join(\"X\")](\"Microsoft.XMLHTTP\");\n        }\n        catch (e) { }\n    }\n}\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AACA;AACA;AACA,0TAAiC,UAAU;;;;;;;AAC3C,MAAM,QAAQ,CAAA,GAAA,uLAAA,CAAA,UAAW,AAAD,EAAE,6BAA6B,UAAU;AACjE,SAAS,SAAU;AACZ,MAAM,gBAAgB,2PAAA,CAAA,UAAO;IAChC;;;;;KAKC,GACD,YAAY,IAAI,CAAE;QACd,KAAK,CAAC;QACN,IAAI,OAAO,aAAa,aAAa;YACjC,MAAM,QAAQ,aAAa,SAAS,QAAQ;YAC5C,IAAI,OAAO,SAAS,IAAI;YACxB,8CAA8C;YAC9C,IAAI,CAAC,MAAM;gBACP,OAAO,QAAQ,QAAQ;YAC3B;YACA,IAAI,CAAC,EAAE,GACH,AAAC,OAAO,aAAa,eACjB,KAAK,QAAQ,KAAK,SAAS,QAAQ,IACnC,SAAS,KAAK,IAAI;QAC9B;IACJ;IACA;;;;;;KAMC,GACD,QAAQ,IAAI,EAAE,EAAE,EAAE;QACd,MAAM,MAAM,IAAI,CAAC,OAAO,CAAC;YACrB,QAAQ;YACR,MAAM;QACV;QACA,IAAI,EAAE,CAAC,WAAW;QAClB,IAAI,EAAE,CAAC,SAAS,CAAC,WAAW;YACxB,IAAI,CAAC,OAAO,CAAC,kBAAkB,WAAW;QAC9C;IACJ;IACA;;;;KAIC,GACD,SAAS;QACL,MAAM;QACN,MAAM,MAAM,IAAI,CAAC,OAAO;QACxB,IAAI,EAAE,CAAC,QAAQ,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI;QACpC,IAAI,EAAE,CAAC,SAAS,CAAC,WAAW;YACxB,IAAI,CAAC,OAAO,CAAC,kBAAkB,WAAW;QAC9C;QACA,IAAI,CAAC,OAAO,GAAG;IACnB;AACJ;AACO,MAAM,gBAAgB,gQAAA,CAAA,UAAO;IAChC;;;;;KAKC,GACD,YAAY,aAAa,EAAE,GAAG,EAAE,IAAI,CAAE;QAClC,KAAK;QACL,IAAI,CAAC,aAAa,GAAG;QACrB,CAAA,GAAA,0OAAA,CAAA,wBAAqB,AAAD,EAAE,IAAI,EAAE;QAC5B,IAAI,CAAC,KAAK,GAAG;QACb,IAAI,CAAC,OAAO,GAAG,KAAK,MAAM,IAAI;QAC9B,IAAI,CAAC,IAAI,GAAG;QACZ,IAAI,CAAC,KAAK,GAAG,cAAc,KAAK,IAAI,GAAG,KAAK,IAAI,GAAG;QACnD,IAAI,CAAC,OAAO;IAChB;IACA;;;;KAIC,GACD,UAAU;QACN,IAAI;QACJ,MAAM,OAAO,CAAA,GAAA,0OAAA,CAAA,OAAI,AAAD,EAAE,IAAI,CAAC,KAAK,EAAE,SAAS,OAAO,OAAO,cAAc,QAAQ,MAAM,WAAW,sBAAsB;QAClH,KAAK,OAAO,GAAG,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE;QAC9B,MAAM,MAAO,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,aAAa,CAAC;QAC5C,IAAI;YACA,MAAM,mBAAmB,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,IAAI;YAChD,IAAI,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,IAAI,EAAE;YAClC,IAAI;gBACA,IAAI,IAAI,CAAC,KAAK,CAAC,YAAY,EAAE;oBACzB,aAAa;oBACb,IAAI,qBAAqB,IAAI,IAAI,qBAAqB,CAAC;oBACvD,IAAK,IAAI,KAAK,IAAI,CAAC,KAAK,CAAC,YAAY,CAAE;wBACnC,IAAI,IAAI,CAAC,KAAK,CAAC,YAAY,CAAC,cAAc,CAAC,IAAI;4BAC3C,IAAI,gBAAgB,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,YAAY,CAAC,EAAE;wBACtD;oBACJ;gBACJ;YACJ,EACA,OAAO,GAAG,CAAE;YACZ,IAAI,WAAW,IAAI,CAAC,OAAO,EAAE;gBACzB,IAAI;oBACA,IAAI,gBAAgB,CAAC,gBAAgB;gBACzC,EACA,OAAO,GAAG,CAAE;YAChB;YACA,IAAI;gBACA,IAAI,gBAAgB,CAAC,UAAU;YACnC,EACA,OAAO,GAAG,CAAE;YACZ,CAAC,KAAK,IAAI,CAAC,KAAK,CAAC,SAAS,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,IAAI,GAAG,UAAU,CAAC;YAC/E,YAAY;YACZ,IAAI,qBAAqB,KAAK;gBAC1B,IAAI,eAAe,GAAG,IAAI,CAAC,KAAK,CAAC,eAAe;YACpD;YACA,IAAI,IAAI,CAAC,KAAK,CAAC,cAAc,EAAE;gBAC3B,IAAI,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,cAAc;YAC3C;YACA,IAAI,kBAAkB,GAAG;gBACrB,IAAI;gBACJ,IAAI,IAAI,UAAU,KAAK,GAAG;oBACtB,CAAC,KAAK,IAAI,CAAC,KAAK,CAAC,SAAS,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,IAAI,GAAG,YAAY,CAChF,aAAa;oBACb,IAAI,iBAAiB,CAAC;gBAC1B;gBACA,IAAI,MAAM,IAAI,UAAU,EACpB;gBACJ,IAAI,QAAQ,IAAI,MAAM,IAAI,SAAS,IAAI,MAAM,EAAE;oBAC3C,IAAI,CAAC,OAAO;gBAChB,OACK;oBACD,sDAAsD;oBACtD,uDAAuD;oBACvD,IAAI,CAAC,YAAY,CAAC;wBACd,IAAI,CAAC,QAAQ,CAAC,OAAO,IAAI,MAAM,KAAK,WAAW,IAAI,MAAM,GAAG;oBAChE,GAAG;gBACP;YACJ;YACA,MAAM,eAAe,IAAI,CAAC,KAAK;YAC/B,IAAI,IAAI,CAAC,IAAI,CAAC,KAAK;QACvB,EACA,OAAO,GAAG;YACN,wEAAwE;YACxE,2EAA2E;YAC3E,yDAAyD;YACzD,IAAI,CAAC,YAAY,CAAC;gBACd,IAAI,CAAC,QAAQ,CAAC;YAClB,GAAG;YACH;QACJ;QACA,IAAI,OAAO,aAAa,aAAa;YACjC,IAAI,CAAC,MAAM,GAAG,QAAQ,aAAa;YACnC,QAAQ,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,GAAG,IAAI;QACxC;IACJ;IACA;;;;KAIC,GACD,SAAS,GAAG,EAAE;QACV,IAAI,CAAC,YAAY,CAAC,SAAS,KAAK,IAAI,CAAC,IAAI;QACzC,IAAI,CAAC,QAAQ,CAAC;IAClB;IACA;;;;KAIC,GACD,SAAS,SAAS,EAAE;QAChB,IAAI,gBAAgB,OAAO,IAAI,CAAC,IAAI,IAAI,SAAS,IAAI,CAAC,IAAI,EAAE;YACxD;QACJ;QACA,IAAI,CAAC,IAAI,CAAC,kBAAkB,GAAG;QAC/B,IAAI,WAAW;YACX,IAAI;gBACA,IAAI,CAAC,IAAI,CAAC,KAAK;YACnB,EACA,OAAO,GAAG,CAAE;QAChB;QACA,IAAI,OAAO,aAAa,aAAa;YACjC,OAAO,QAAQ,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC;QACxC;QACA,IAAI,CAAC,IAAI,GAAG;IAChB;IACA;;;;KAIC,GACD,UAAU;QACN,MAAM,OAAO,IAAI,CAAC,IAAI,CAAC,YAAY;QACnC,IAAI,SAAS,MAAM;YACf,IAAI,CAAC,YAAY,CAAC,QAAQ;YAC1B,IAAI,CAAC,YAAY,CAAC;YAClB,IAAI,CAAC,QAAQ;QACjB;IACJ;IACA;;;;KAIC,GACD,QAAQ;QACJ,IAAI,CAAC,QAAQ;IACjB;AACJ;AACA,QAAQ,aAAa,GAAG;AACxB,QAAQ,QAAQ,GAAG,CAAC;AACpB;;;;CAIC,GACD,IAAI,OAAO,aAAa,aAAa;IACjC,aAAa;IACb,IAAI,OAAO,gBAAgB,YAAY;QACnC,aAAa;QACb,YAAY,YAAY;IAC5B,OACK,IAAI,OAAO,qBAAqB,YAAY;QAC7C,MAAM,mBAAmB,gBAAgB,qPAAA,CAAA,iBAAU,GAAG,aAAa;QACnE,iBAAiB,kBAAkB,eAAe;IACtD;AACJ;AACA,SAAS;IACL,IAAK,IAAI,KAAK,QAAQ,QAAQ,CAAE;QAC5B,IAAI,QAAQ,QAAQ,CAAC,cAAc,CAAC,IAAI;YACpC,QAAQ,QAAQ,CAAC,EAAE,CAAC,KAAK;QAC7B;IACJ;AACJ;AACA,MAAM,UAAU,AAAC;IACb,MAAM,MAAM,WAAW;QACnB,SAAS;IACb;IACA,OAAO,OAAO,IAAI,YAAY,KAAK;AACvC;AAQO,MAAM,YAAY;IACrB,YAAY,IAAI,CAAE;QACd,KAAK,CAAC;QACN,MAAM,cAAc,QAAQ,KAAK,WAAW;QAC5C,IAAI,CAAC,cAAc,GAAG,WAAW,CAAC;IACtC;IACA,QAAQ,OAAO,CAAC,CAAC,EAAE;QACf,OAAO,MAAM,CAAC,MAAM;YAAE,IAAI,IAAI,CAAC,EAAE;QAAC,GAAG,IAAI,CAAC,IAAI;QAC9C,OAAO,IAAI,QAAQ,YAAY,IAAI,CAAC,GAAG,IAAI;IAC/C;AACJ;AACA,SAAS,WAAW,IAAI;IACpB,MAAM,UAAU,KAAK,OAAO;IAC5B,uCAAuC;IACvC,IAAI;QACA,IAAI,gBAAgB,OAAO,kBAAkB,CAAC,CAAC,WAAW,4PAAA,CAAA,UAAO,GAAG;YAChE,OAAO,IAAI;QACf;IACJ,EACA,OAAO,GAAG,CAAE;IACZ,IAAI,CAAC,SAAS;QACV,IAAI;YACA,OAAO,IAAI,qPAAA,CAAA,iBAAU,CAAC;gBAAC;aAAS,CAAC,MAAM,CAAC,UAAU,IAAI,CAAC,KAAK,CAAC;QACjE,EACA,OAAO,GAAG,CAAE;IAChB;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1803, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/bloomgoo/gold-exchange-platform/frontend/node_modules/.pnpm/engine.io-client%406.6.3/node_modules/engine.io-client/build/esm-debug/transports/polling-xhr.node.js"], "sourcesContent": ["import * as XMLHttpRequestModule from \"xmlhttprequest-ssl\";\nimport { BaseXHR, Request } from \"./polling-xhr.js\";\nconst XMLHttpRequest = XMLHttpRequestModule.default || XMLHttpRequestModule;\n/**\n * HTTP long-polling based on the `XMLHttpRequest` object provided by the `xmlhttprequest-ssl` package.\n *\n * Usage: Node.js, <PERSON>o (compat), <PERSON><PERSON> (compat)\n *\n * @see https://developer.mozilla.org/en-US/docs/Web/API/XMLHttpRequest\n */\nexport class XHR extends BaseXHR {\n    request(opts = {}) {\n        var _a;\n        Object.assign(opts, { xd: this.xd, cookieJar: (_a = this.socket) === null || _a === void 0 ? void 0 : _a._cookieJar }, this.opts);\n        return new Request((opts) => new XMLHttpRequest(opts), this.uri(), opts);\n    }\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AACA,MAAM,iBAAiB,iOAAqB,OAAO,IAAI;AAQhD,MAAM,YAAY,kQAAA,CAAA,UAAO;IAC5B,QAAQ,OAAO,CAAC,CAAC,EAAE;QACf,IAAI;QACJ,OAAO,MAAM,CAAC,MAAM;YAAE,IAAI,IAAI,CAAC,EAAE;YAAE,WAAW,CAAC,KAAK,IAAI,CAAC,MAAM,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,IAAI,GAAG,UAAU;QAAC,GAAG,IAAI,CAAC,IAAI;QAChI,OAAO,IAAI,kQAAA,CAAA,UAAO,CAAC,CAAC,OAAS,IAAI,eAAe,OAAO,IAAI,CAAC,GAAG,IAAI;IACvE;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1827, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/bloomgoo/gold-exchange-platform/frontend/node_modules/.pnpm/engine.io-client%406.6.3/node_modules/engine.io-client/build/esm-debug/transports/websocket.js"], "sourcesContent": ["import { Transport } from \"../transport.js\";\nimport { pick, randomString } from \"../util.js\";\nimport { encodePacket } from \"engine.io-parser\";\nimport { globalThisShim as globalThis, nextTick } from \"../globals.node.js\";\nimport debugModule from \"debug\"; // debug()\nconst debug = debugModule(\"engine.io-client:websocket\"); // debug()\n// detect ReactNative environment\nconst isReactNative = typeof navigator !== \"undefined\" &&\n    typeof navigator.product === \"string\" &&\n    navigator.product.toLowerCase() === \"reactnative\";\nexport class BaseWS extends Transport {\n    get name() {\n        return \"websocket\";\n    }\n    doOpen() {\n        const uri = this.uri();\n        const protocols = this.opts.protocols;\n        // React Native only supports the 'headers' option, and will print a warning if anything else is passed\n        const opts = isReactNative\n            ? {}\n            : pick(this.opts, \"agent\", \"perMessageDeflate\", \"pfx\", \"key\", \"passphrase\", \"cert\", \"ca\", \"ciphers\", \"rejectUnauthorized\", \"localAddress\", \"protocolVersion\", \"origin\", \"maxPayload\", \"family\", \"checkServerIdentity\");\n        if (this.opts.extraHeaders) {\n            opts.headers = this.opts.extraHeaders;\n        }\n        try {\n            this.ws = this.createSocket(uri, protocols, opts);\n        }\n        catch (err) {\n            return this.emitReserved(\"error\", err);\n        }\n        this.ws.binaryType = this.socket.binaryType;\n        this.addEventListeners();\n    }\n    /**\n     * Adds event listeners to the socket\n     *\n     * @private\n     */\n    addEventListeners() {\n        this.ws.onopen = () => {\n            if (this.opts.autoUnref) {\n                this.ws._socket.unref();\n            }\n            this.onOpen();\n        };\n        this.ws.onclose = (closeEvent) => this.onClose({\n            description: \"websocket connection closed\",\n            context: closeEvent,\n        });\n        this.ws.onmessage = (ev) => this.onData(ev.data);\n        this.ws.onerror = (e) => this.onError(\"websocket error\", e);\n    }\n    write(packets) {\n        this.writable = false;\n        // encodePacket efficient as it uses WS framing\n        // no need for encodePayload\n        for (let i = 0; i < packets.length; i++) {\n            const packet = packets[i];\n            const lastPacket = i === packets.length - 1;\n            encodePacket(packet, this.supportsBinary, (data) => {\n                // Sometimes the websocket has already been closed but the browser didn't\n                // have a chance of informing us about it yet, in that case send will\n                // throw an error\n                try {\n                    this.doWrite(packet, data);\n                }\n                catch (e) {\n                    debug(\"websocket closed before onclose event\");\n                }\n                if (lastPacket) {\n                    // fake drain\n                    // defer to next tick to allow Socket to clear writeBuffer\n                    nextTick(() => {\n                        this.writable = true;\n                        this.emitReserved(\"drain\");\n                    }, this.setTimeoutFn);\n                }\n            });\n        }\n    }\n    doClose() {\n        if (typeof this.ws !== \"undefined\") {\n            this.ws.onerror = () => { };\n            this.ws.close();\n            this.ws = null;\n        }\n    }\n    /**\n     * Generates uri for connection.\n     *\n     * @private\n     */\n    uri() {\n        const schema = this.opts.secure ? \"wss\" : \"ws\";\n        const query = this.query || {};\n        // append timestamp to URI\n        if (this.opts.timestampRequests) {\n            query[this.opts.timestampParam] = randomString();\n        }\n        // communicate binary support capabilities\n        if (!this.supportsBinary) {\n            query.b64 = 1;\n        }\n        return this.createUri(schema, query);\n    }\n}\nconst WebSocketCtor = globalThis.WebSocket || globalThis.MozWebSocket;\n/**\n * WebSocket transport based on the built-in `WebSocket` object.\n *\n * Usage: browser, Node.js (since v21), Deno, Bun\n *\n * @see https://developer.mozilla.org/en-US/docs/Web/API/WebSocket\n * @see https://caniuse.com/mdn-api_websocket\n * @see https://nodejs.org/api/globals.html#websocket\n */\nexport class WS extends BaseWS {\n    createSocket(uri, protocols, opts) {\n        return !isReactNative\n            ? protocols\n                ? new WebSocketCtor(uri, protocols)\n                : new WebSocketCtor(uri)\n            : new WebSocketCtor(uri, protocols, opts);\n    }\n    doWrite(_packet, data) {\n        this.ws.send(data);\n    }\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AAAA;AACA;AACA,0TAAiC,UAAU;;;;;;AAC3C,MAAM,QAAQ,CAAA,GAAA,uLAAA,CAAA,UAAW,AAAD,EAAE,+BAA+B,UAAU;AACnE,iCAAiC;AACjC,MAAM,gBAAgB,OAAO,cAAc,eACvC,OAAO,UAAU,OAAO,KAAK,YAC7B,UAAU,OAAO,CAAC,WAAW,OAAO;AACjC,MAAM,eAAe,+OAAA,CAAA,YAAS;IACjC,IAAI,OAAO;QACP,OAAO;IACX;IACA,SAAS;QACL,MAAM,MAAM,IAAI,CAAC,GAAG;QACpB,MAAM,YAAY,IAAI,CAAC,IAAI,CAAC,SAAS;QACrC,uGAAuG;QACvG,MAAM,OAAO,gBACP,CAAC,IACD,CAAA,GAAA,0OAAA,CAAA,OAAI,AAAD,EAAE,IAAI,CAAC,IAAI,EAAE,SAAS,qBAAqB,OAAO,OAAO,cAAc,QAAQ,MAAM,WAAW,sBAAsB,gBAAgB,mBAAmB,UAAU,cAAc,UAAU;QACpM,IAAI,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE;YACxB,KAAK,OAAO,GAAG,IAAI,CAAC,IAAI,CAAC,YAAY;QACzC;QACA,IAAI;YACA,IAAI,CAAC,EAAE,GAAG,IAAI,CAAC,YAAY,CAAC,KAAK,WAAW;QAChD,EACA,OAAO,KAAK;YACR,OAAO,IAAI,CAAC,YAAY,CAAC,SAAS;QACtC;QACA,IAAI,CAAC,EAAE,CAAC,UAAU,GAAG,IAAI,CAAC,MAAM,CAAC,UAAU;QAC3C,IAAI,CAAC,iBAAiB;IAC1B;IACA;;;;KAIC,GACD,oBAAoB;QAChB,IAAI,CAAC,EAAE,CAAC,MAAM,GAAG;YACb,IAAI,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;gBACrB,IAAI,CAAC,EAAE,CAAC,OAAO,CAAC,KAAK;YACzB;YACA,IAAI,CAAC,MAAM;QACf;QACA,IAAI,CAAC,EAAE,CAAC,OAAO,GAAG,CAAC,aAAe,IAAI,CAAC,OAAO,CAAC;gBAC3C,aAAa;gBACb,SAAS;YACb;QACA,IAAI,CAAC,EAAE,CAAC,SAAS,GAAG,CAAC,KAAO,IAAI,CAAC,MAAM,CAAC,GAAG,IAAI;QAC/C,IAAI,CAAC,EAAE,CAAC,OAAO,GAAG,CAAC,IAAM,IAAI,CAAC,OAAO,CAAC,mBAAmB;IAC7D;IACA,MAAM,OAAO,EAAE;QACX,IAAI,CAAC,QAAQ,GAAG;QAChB,+CAA+C;QAC/C,4BAA4B;QAC5B,IAAK,IAAI,IAAI,GAAG,IAAI,QAAQ,MAAM,EAAE,IAAK;YACrC,MAAM,SAAS,OAAO,CAAC,EAAE;YACzB,MAAM,aAAa,MAAM,QAAQ,MAAM,GAAG;YAC1C,CAAA,GAAA,yOAAA,CAAA,eAAY,AAAD,EAAE,QAAQ,IAAI,CAAC,cAAc,EAAE,CAAC;gBACvC,yEAAyE;gBACzE,qEAAqE;gBACrE,iBAAiB;gBACjB,IAAI;oBACA,IAAI,CAAC,OAAO,CAAC,QAAQ;gBACzB,EACA,OAAO,GAAG;oBACN,MAAM;gBACV;gBACA,IAAI,YAAY;oBACZ,aAAa;oBACb,0DAA0D;oBAC1D,CAAA,GAAA,qPAAA,CAAA,WAAQ,AAAD,EAAE;wBACL,IAAI,CAAC,QAAQ,GAAG;wBAChB,IAAI,CAAC,YAAY,CAAC;oBACtB,GAAG,IAAI,CAAC,YAAY;gBACxB;YACJ;QACJ;IACJ;IACA,UAAU;QACN,IAAI,OAAO,IAAI,CAAC,EAAE,KAAK,aAAa;YAChC,IAAI,CAAC,EAAE,CAAC,OAAO,GAAG,KAAQ;YAC1B,IAAI,CAAC,EAAE,CAAC,KAAK;YACb,IAAI,CAAC,EAAE,GAAG;QACd;IACJ;IACA;;;;KAIC,GACD,MAAM;QACF,MAAM,SAAS,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,QAAQ;QAC1C,MAAM,QAAQ,IAAI,CAAC,KAAK,IAAI,CAAC;QAC7B,0BAA0B;QAC1B,IAAI,IAAI,CAAC,IAAI,CAAC,iBAAiB,EAAE;YAC7B,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,GAAG,CAAA,GAAA,0OAAA,CAAA,eAAY,AAAD;QACjD;QACA,0CAA0C;QAC1C,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE;YACtB,MAAM,GAAG,GAAG;QAChB;QACA,OAAO,IAAI,CAAC,SAAS,CAAC,QAAQ;IAClC;AACJ;AACA,MAAM,gBAAgB,qPAAA,CAAA,iBAAU,CAAC,SAAS,IAAI,qPAAA,CAAA,iBAAU,CAAC,YAAY;AAU9D,MAAM,WAAW;IACpB,aAAa,GAAG,EAAE,SAAS,EAAE,IAAI,EAAE;QAC/B,OAAO,CAAC,gBACF,YACI,IAAI,cAAc,KAAK,aACvB,IAAI,cAAc,OACtB,IAAI,cAAc,KAAK,WAAW;IAC5C;IACA,QAAQ,OAAO,EAAE,IAAI,EAAE;QACnB,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC;IACjB;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1950, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/bloomgoo/gold-exchange-platform/frontend/node_modules/.pnpm/engine.io-client%406.6.3/node_modules/engine.io-client/build/esm-debug/transports/websocket.node.js"], "sourcesContent": ["import * as ws from \"ws\";\nimport { BaseWS } from \"./websocket.js\";\n/**\n * WebSocket transport based on the `WebSocket` object provided by the `ws` package.\n *\n * Usage: Node.js, <PERSON><PERSON> (compat), <PERSON>un (compat)\n *\n * @see https://developer.mozilla.org/en-US/docs/Web/API/WebSocket\n * @see https://caniuse.com/mdn-api_websocket\n */\nexport class WS extends BaseWS {\n    createSocket(uri, protocols, opts) {\n        var _a;\n        if ((_a = this.socket) === null || _a === void 0 ? void 0 : _a._cookieJar) {\n            opts.headers = opts.headers || {};\n            opts.headers.cookie =\n                typeof opts.headers.cookie === \"string\"\n                    ? [opts.headers.cookie]\n                    : opts.headers.cookie || [];\n            for (const [name, cookie] of this.socket._cookieJar.cookies) {\n                opts.headers.cookie.push(`${name}=${cookie.value}`);\n            }\n        }\n        return new ws.WebSocket(uri, protocols, opts);\n    }\n    doWrite(packet, data) {\n        const opts = {};\n        if (packet.options) {\n            opts.compress = packet.options.compress;\n        }\n        if (this.opts.perMessageDeflate) {\n            const len = \n            // @ts-ignore\n            \"string\" === typeof data ? Buffer.byteLength(data) : data.length;\n            if (len < this.opts.perMessageDeflate.threshold) {\n                opts.compress = false;\n            }\n        }\n        this.ws.send(data, opts);\n    }\n}\n"], "names": [], "mappings": ";;;AAAA;AAAA;AACA;;;AASO,MAAM,WAAW,6PAAA,CAAA,SAAM;IAC1B,aAAa,GAAG,EAAE,SAAS,EAAE,IAAI,EAAE;QAC/B,IAAI;QACJ,IAAI,CAAC,KAAK,IAAI,CAAC,MAAM,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,IAAI,GAAG,UAAU,EAAE;YACvE,KAAK,OAAO,GAAG,KAAK,OAAO,IAAI,CAAC;YAChC,KAAK,OAAO,CAAC,MAAM,GACf,OAAO,KAAK,OAAO,CAAC,MAAM,KAAK,WACzB;gBAAC,KAAK,OAAO,CAAC,MAAM;aAAC,GACrB,KAAK,OAAO,CAAC,MAAM,IAAI,EAAE;YACnC,KAAK,MAAM,CAAC,MAAM,OAAO,IAAI,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,OAAO,CAAE;gBACzD,KAAK,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,GAAG,KAAK,CAAC,EAAE,OAAO,KAAK,EAAE;YACtD;QACJ;QACA,OAAO,IAAI,8NAAA,CAAA,YAAY,CAAC,KAAK,WAAW;IAC5C;IACA,QAAQ,MAAM,EAAE,IAAI,EAAE;QAClB,MAAM,OAAO,CAAC;QACd,IAAI,OAAO,OAAO,EAAE;YAChB,KAAK,QAAQ,GAAG,OAAO,OAAO,CAAC,QAAQ;QAC3C;QACA,IAAI,IAAI,CAAC,IAAI,CAAC,iBAAiB,EAAE;YAC7B,MAAM,MACN,aAAa;YACb,aAAa,OAAO,OAAO,OAAO,UAAU,CAAC,QAAQ,KAAK,MAAM;YAChE,IAAI,MAAM,IAAI,CAAC,IAAI,CAAC,iBAAiB,CAAC,SAAS,EAAE;gBAC7C,KAAK,QAAQ,GAAG;YACpB;QACJ;QACA,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,MAAM;IACvB;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1993, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/bloomgoo/gold-exchange-platform/frontend/node_modules/.pnpm/engine.io-client%406.6.3/node_modules/engine.io-client/build/esm-debug/transports/webtransport.js"], "sourcesContent": ["import { Transport } from \"../transport.js\";\nimport { nextTick } from \"../globals.node.js\";\nimport { createPacketDecoderStream, createPacketEncoderStream, } from \"engine.io-parser\";\nimport debugModule from \"debug\"; // debug()\nconst debug = debugModule(\"engine.io-client:webtransport\"); // debug()\n/**\n * WebTransport transport based on the built-in `WebTransport` object.\n *\n * Usage: browser, Node.js (with the `@fails-components/webtransport` package)\n *\n * @see https://developer.mozilla.org/en-US/docs/Web/API/WebTransport\n * @see https://caniuse.com/webtransport\n */\nexport class WT extends Transport {\n    get name() {\n        return \"webtransport\";\n    }\n    doOpen() {\n        try {\n            // @ts-ignore\n            this._transport = new WebTransport(this.createUri(\"https\"), this.opts.transportOptions[this.name]);\n        }\n        catch (err) {\n            return this.emitReserved(\"error\", err);\n        }\n        this._transport.closed\n            .then(() => {\n            debug(\"transport closed gracefully\");\n            this.onClose();\n        })\n            .catch((err) => {\n            debug(\"transport closed due to %s\", err);\n            this.onError(\"webtransport error\", err);\n        });\n        // note: we could have used async/await, but that would require some additional polyfills\n        this._transport.ready.then(() => {\n            this._transport.createBidirectionalStream().then((stream) => {\n                const decoderStream = createPacketDecoderStream(Number.MAX_SAFE_INTEGER, this.socket.binaryType);\n                const reader = stream.readable.pipeThrough(decoderStream).getReader();\n                const encoderStream = createPacketEncoderStream();\n                encoderStream.readable.pipeTo(stream.writable);\n                this._writer = encoderStream.writable.getWriter();\n                const read = () => {\n                    reader\n                        .read()\n                        .then(({ done, value }) => {\n                        if (done) {\n                            debug(\"session is closed\");\n                            return;\n                        }\n                        debug(\"received chunk: %o\", value);\n                        this.onPacket(value);\n                        read();\n                    })\n                        .catch((err) => {\n                        debug(\"an error occurred while reading: %s\", err);\n                    });\n                };\n                read();\n                const packet = { type: \"open\" };\n                if (this.query.sid) {\n                    packet.data = `{\"sid\":\"${this.query.sid}\"}`;\n                }\n                this._writer.write(packet).then(() => this.onOpen());\n            });\n        });\n    }\n    write(packets) {\n        this.writable = false;\n        for (let i = 0; i < packets.length; i++) {\n            const packet = packets[i];\n            const lastPacket = i === packets.length - 1;\n            this._writer.write(packet).then(() => {\n                if (lastPacket) {\n                    nextTick(() => {\n                        this.writable = true;\n                        this.emitReserved(\"drain\");\n                    }, this.setTimeoutFn);\n                }\n            });\n        }\n    }\n    doClose() {\n        var _a;\n        (_a = this._transport) === null || _a === void 0 ? void 0 : _a.close();\n    }\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AAAA;AACA,0TAAiC,UAAU;;;;;AAC3C,MAAM,QAAQ,CAAA,GAAA,uLAAA,CAAA,UAAW,AAAD,EAAE,kCAAkC,UAAU;AAS/D,MAAM,WAAW,+OAAA,CAAA,YAAS;IAC7B,IAAI,OAAO;QACP,OAAO;IACX;IACA,SAAS;QACL,IAAI;YACA,aAAa;YACb,IAAI,CAAC,UAAU,GAAG,IAAI,aAAa,IAAI,CAAC,SAAS,CAAC,UAAU,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,IAAI,CAAC;QACrG,EACA,OAAO,KAAK;YACR,OAAO,IAAI,CAAC,YAAY,CAAC,SAAS;QACtC;QACA,IAAI,CAAC,UAAU,CAAC,MAAM,CACjB,IAAI,CAAC;YACN,MAAM;YACN,IAAI,CAAC,OAAO;QAChB,GACK,KAAK,CAAC,CAAC;YACR,MAAM,8BAA8B;YACpC,IAAI,CAAC,OAAO,CAAC,sBAAsB;QACvC;QACA,yFAAyF;QACzF,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,IAAI,CAAC;YACvB,IAAI,CAAC,UAAU,CAAC,yBAAyB,GAAG,IAAI,CAAC,CAAC;gBAC9C,MAAM,gBAAgB,CAAA,GAAA,kPAAA,CAAA,4BAAyB,AAAD,EAAE,OAAO,gBAAgB,EAAE,IAAI,CAAC,MAAM,CAAC,UAAU;gBAC/F,MAAM,SAAS,OAAO,QAAQ,CAAC,WAAW,CAAC,eAAe,SAAS;gBACnE,MAAM,gBAAgB,CAAA,GAAA,kPAAA,CAAA,4BAAyB,AAAD;gBAC9C,cAAc,QAAQ,CAAC,MAAM,CAAC,OAAO,QAAQ;gBAC7C,IAAI,CAAC,OAAO,GAAG,cAAc,QAAQ,CAAC,SAAS;gBAC/C,MAAM,OAAO;oBACT,OACK,IAAI,GACJ,IAAI,CAAC,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE;wBACtB,IAAI,MAAM;4BACN,MAAM;4BACN;wBACJ;wBACA,MAAM,sBAAsB;wBAC5B,IAAI,CAAC,QAAQ,CAAC;wBACd;oBACJ,GACK,KAAK,CAAC,CAAC;wBACR,MAAM,uCAAuC;oBACjD;gBACJ;gBACA;gBACA,MAAM,SAAS;oBAAE,MAAM;gBAAO;gBAC9B,IAAI,IAAI,CAAC,KAAK,CAAC,GAAG,EAAE;oBAChB,OAAO,IAAI,GAAG,CAAC,QAAQ,EAAE,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC;gBAC/C;gBACA,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,QAAQ,IAAI,CAAC,IAAM,IAAI,CAAC,MAAM;YACrD;QACJ;IACJ;IACA,MAAM,OAAO,EAAE;QACX,IAAI,CAAC,QAAQ,GAAG;QAChB,IAAK,IAAI,IAAI,GAAG,IAAI,QAAQ,MAAM,EAAE,IAAK;YACrC,MAAM,SAAS,OAAO,CAAC,EAAE;YACzB,MAAM,aAAa,MAAM,QAAQ,MAAM,GAAG;YAC1C,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,QAAQ,IAAI,CAAC;gBAC5B,IAAI,YAAY;oBACZ,CAAA,GAAA,qPAAA,CAAA,WAAQ,AAAD,EAAE;wBACL,IAAI,CAAC,QAAQ,GAAG;wBAChB,IAAI,CAAC,YAAY,CAAC;oBACtB,GAAG,IAAI,CAAC,YAAY;gBACxB;YACJ;QACJ;IACJ;IACA,UAAU;QACN,IAAI;QACJ,CAAC,KAAK,IAAI,CAAC,UAAU,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,IAAI,GAAG,KAAK;IACxE;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2082, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/bloomgoo/gold-exchange-platform/frontend/node_modules/.pnpm/engine.io-client%406.6.3/node_modules/engine.io-client/build/esm-debug/transports/index.js"], "sourcesContent": ["import { XHR } from \"./polling-xhr.node.js\";\nimport { WS } from \"./websocket.node.js\";\nimport { WT } from \"./webtransport.js\";\nexport const transports = {\n    websocket: WS,\n    webtransport: WT,\n    polling: XHR,\n};\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;;;;AACO,MAAM,aAAa;IACtB,WAAW,qQAAA,CAAA,KAAE;IACb,cAAc,gQAAA,CAAA,KAAE;IAChB,SAAS,0QAAA,CAAA,MAAG;AAChB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2102, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/bloomgoo/gold-exchange-platform/frontend/node_modules/.pnpm/engine.io-client%406.6.3/node_modules/engine.io-client/build/esm-debug/contrib/parseuri.js"], "sourcesContent": ["// imported from https://github.com/galkn/parseuri\n/**\n * Parses a URI\n *\n * Note: we could also have used the built-in URL object, but it isn't supported on all platforms.\n *\n * See:\n * - https://developer.mozilla.org/en-US/docs/Web/API/URL\n * - https://caniuse.com/url\n * - https://www.rfc-editor.org/rfc/rfc3986#appendix-B\n *\n * History of the parse() method:\n * - first commit: https://github.com/socketio/socket.io-client/commit/4ee1d5d94b3906a9c052b459f1a818b15f38f91c\n * - export into its own module: https://github.com/socketio/engine.io-client/commit/de2c561e4564efeb78f1bdb1ba39ef81b2822cb3\n * - reimport: https://github.com/socketio/engine.io-client/commit/df32277c3f6d622eec5ed09f493cae3f3391d242\n *\n * <AUTHOR> <stevenlevithan.com> (MIT license)\n * @api private\n */\nconst re = /^(?:(?![^:@\\/?#]+:[^:@\\/]*@)(http|https|ws|wss):\\/\\/)?((?:(([^:@\\/?#]*)(?::([^:@\\/?#]*))?)?@)?((?:[a-f0-9]{0,4}:){2,7}[a-f0-9]{0,4}|[^:\\/?#]*)(?::(\\d*))?)(((\\/(?:[^?#](?![^?#\\/]*\\.[^?#\\/.]+(?:[?#]|$)))*\\/?)?([^?#\\/]*))(?:\\?([^#]*))?(?:#(.*))?)/;\nconst parts = [\n    'source', 'protocol', 'authority', 'userInfo', 'user', 'password', 'host', 'port', 'relative', 'path', 'directory', 'file', 'query', 'anchor'\n];\nexport function parse(str) {\n    if (str.length > 8000) {\n        throw \"URI too long\";\n    }\n    const src = str, b = str.indexOf('['), e = str.indexOf(']');\n    if (b != -1 && e != -1) {\n        str = str.substring(0, b) + str.substring(b, e).replace(/:/g, ';') + str.substring(e, str.length);\n    }\n    let m = re.exec(str || ''), uri = {}, i = 14;\n    while (i--) {\n        uri[parts[i]] = m[i] || '';\n    }\n    if (b != -1 && e != -1) {\n        uri.source = src;\n        uri.host = uri.host.substring(1, uri.host.length - 1).replace(/;/g, ':');\n        uri.authority = uri.authority.replace('[', '').replace(']', '').replace(/;/g, ':');\n        uri.ipv6uri = true;\n    }\n    uri.pathNames = pathNames(uri, uri['path']);\n    uri.queryKey = queryKey(uri, uri['query']);\n    return uri;\n}\nfunction pathNames(obj, path) {\n    const regx = /\\/{2,9}/g, names = path.replace(regx, \"/\").split(\"/\");\n    if (path.slice(0, 1) == '/' || path.length === 0) {\n        names.splice(0, 1);\n    }\n    if (path.slice(-1) == '/') {\n        names.splice(names.length - 1, 1);\n    }\n    return names;\n}\nfunction queryKey(uri, query) {\n    const data = {};\n    query.replace(/(?:^|&)([^&=]*)=?([^&]*)/g, function ($0, $1, $2) {\n        if ($1) {\n            data[$1] = $2;\n        }\n    });\n    return data;\n}\n"], "names": [], "mappings": "AAAA,kDAAkD;AAClD;;;;;;;;;;;;;;;;;CAiBC;;;AACD,MAAM,KAAK;AACX,MAAM,QAAQ;IACV;IAAU;IAAY;IAAa;IAAY;IAAQ;IAAY;IAAQ;IAAQ;IAAY;IAAQ;IAAa;IAAQ;IAAS;CACxI;AACM,SAAS,MAAM,GAAG;IACrB,IAAI,IAAI,MAAM,GAAG,MAAM;QACnB,MAAM;IACV;IACA,MAAM,MAAM,KAAK,IAAI,IAAI,OAAO,CAAC,MAAM,IAAI,IAAI,OAAO,CAAC;IACvD,IAAI,KAAK,CAAC,KAAK,KAAK,CAAC,GAAG;QACpB,MAAM,IAAI,SAAS,CAAC,GAAG,KAAK,IAAI,SAAS,CAAC,GAAG,GAAG,OAAO,CAAC,MAAM,OAAO,IAAI,SAAS,CAAC,GAAG,IAAI,MAAM;IACpG;IACA,IAAI,IAAI,GAAG,IAAI,CAAC,OAAO,KAAK,MAAM,CAAC,GAAG,IAAI;IAC1C,MAAO,IAAK;QACR,GAAG,CAAC,KAAK,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI;IAC5B;IACA,IAAI,KAAK,CAAC,KAAK,KAAK,CAAC,GAAG;QACpB,IAAI,MAAM,GAAG;QACb,IAAI,IAAI,GAAG,IAAI,IAAI,CAAC,SAAS,CAAC,GAAG,IAAI,IAAI,CAAC,MAAM,GAAG,GAAG,OAAO,CAAC,MAAM;QACpE,IAAI,SAAS,GAAG,IAAI,SAAS,CAAC,OAAO,CAAC,KAAK,IAAI,OAAO,CAAC,KAAK,IAAI,OAAO,CAAC,MAAM;QAC9E,IAAI,OAAO,GAAG;IAClB;IACA,IAAI,SAAS,GAAG,UAAU,KAAK,GAAG,CAAC,OAAO;IAC1C,IAAI,QAAQ,GAAG,SAAS,KAAK,GAAG,CAAC,QAAQ;IACzC,OAAO;AACX;AACA,SAAS,UAAU,GAAG,EAAE,IAAI;IACxB,MAAM,OAAO,YAAY,QAAQ,KAAK,OAAO,CAAC,MAAM,KAAK,KAAK,CAAC;IAC/D,IAAI,KAAK,KAAK,CAAC,GAAG,MAAM,OAAO,KAAK,MAAM,KAAK,GAAG;QAC9C,MAAM,MAAM,CAAC,GAAG;IACpB;IACA,IAAI,KAAK,KAAK,CAAC,CAAC,MAAM,KAAK;QACvB,MAAM,MAAM,CAAC,MAAM,MAAM,GAAG,GAAG;IACnC;IACA,OAAO;AACX;AACA,SAAS,SAAS,GAAG,EAAE,KAAK;IACxB,MAAM,OAAO,CAAC;IACd,MAAM,OAAO,CAAC,6BAA6B,SAAU,EAAE,EAAE,EAAE,EAAE,EAAE;QAC3D,IAAI,IAAI;YACJ,IAAI,CAAC,GAAG,GAAG;QACf;IACJ;IACA,OAAO;AACX", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2187, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/bloomgoo/gold-exchange-platform/frontend/node_modules/.pnpm/engine.io-client%406.6.3/node_modules/engine.io-client/build/esm-debug/socket.js"], "sourcesContent": ["import { transports as DEFAULT_TRANSPORTS } from \"./transports/index.js\";\nimport { installTimerFunctions, byteLength } from \"./util.js\";\nimport { decode } from \"./contrib/parseqs.js\";\nimport { parse } from \"./contrib/parseuri.js\";\nimport { Emitter } from \"@socket.io/component-emitter\";\nimport { protocol } from \"engine.io-parser\";\nimport { createCookieJar, defaultBinaryType, nextTick, } from \"./globals.node.js\";\nimport debugModule from \"debug\"; // debug()\nconst debug = debugModule(\"engine.io-client:socket\"); // debug()\nconst withEventListeners = typeof addEventListener === \"function\" &&\n    typeof removeEventListener === \"function\";\nconst OFFLINE_EVENT_LISTENERS = [];\nif (withEventListeners) {\n    // within a ServiceWorker, any event handler for the 'offline' event must be added on the initial evaluation of the\n    // script, so we create one single event listener here which will forward the event to the socket instances\n    addEventListener(\"offline\", () => {\n        debug(\"closing %d connection(s) because the network was lost\", OFFLINE_EVENT_LISTENERS.length);\n        OFFLINE_EVENT_LISTENERS.forEach((listener) => listener());\n    }, false);\n}\n/**\n * This class provides a WebSocket-like interface to connect to an Engine.IO server. The connection will be established\n * with one of the available low-level transports, like HTTP long-polling, WebSocket or WebTransport.\n *\n * This class comes without upgrade mechanism, which means that it will keep the first low-level transport that\n * successfully establishes the connection.\n *\n * In order to allow tree-shaking, there are no transports included, that's why the `transports` option is mandatory.\n *\n * @example\n * import { SocketWithoutUpgrade, WebSocket } from \"engine.io-client\";\n *\n * const socket = new SocketWithoutUpgrade({\n *   transports: [WebSocket]\n * });\n *\n * socket.on(\"open\", () => {\n *   socket.send(\"hello\");\n * });\n *\n * @see SocketWithUpgrade\n * @see Socket\n */\nexport class SocketWithoutUpgrade extends Emitter {\n    /**\n     * Socket constructor.\n     *\n     * @param {String|Object} uri - uri or options\n     * @param {Object} opts - options\n     */\n    constructor(uri, opts) {\n        super();\n        this.binaryType = defaultBinaryType;\n        this.writeBuffer = [];\n        this._prevBufferLen = 0;\n        this._pingInterval = -1;\n        this._pingTimeout = -1;\n        this._maxPayload = -1;\n        /**\n         * The expiration timestamp of the {@link _pingTimeoutTimer} object is tracked, in case the timer is throttled and the\n         * callback is not fired on time. This can happen for example when a laptop is suspended or when a phone is locked.\n         */\n        this._pingTimeoutTime = Infinity;\n        if (uri && \"object\" === typeof uri) {\n            opts = uri;\n            uri = null;\n        }\n        if (uri) {\n            const parsedUri = parse(uri);\n            opts.hostname = parsedUri.host;\n            opts.secure =\n                parsedUri.protocol === \"https\" || parsedUri.protocol === \"wss\";\n            opts.port = parsedUri.port;\n            if (parsedUri.query)\n                opts.query = parsedUri.query;\n        }\n        else if (opts.host) {\n            opts.hostname = parse(opts.host).host;\n        }\n        installTimerFunctions(this, opts);\n        this.secure =\n            null != opts.secure\n                ? opts.secure\n                : typeof location !== \"undefined\" && \"https:\" === location.protocol;\n        if (opts.hostname && !opts.port) {\n            // if no port is specified manually, use the protocol default\n            opts.port = this.secure ? \"443\" : \"80\";\n        }\n        this.hostname =\n            opts.hostname ||\n                (typeof location !== \"undefined\" ? location.hostname : \"localhost\");\n        this.port =\n            opts.port ||\n                (typeof location !== \"undefined\" && location.port\n                    ? location.port\n                    : this.secure\n                        ? \"443\"\n                        : \"80\");\n        this.transports = [];\n        this._transportsByName = {};\n        opts.transports.forEach((t) => {\n            const transportName = t.prototype.name;\n            this.transports.push(transportName);\n            this._transportsByName[transportName] = t;\n        });\n        this.opts = Object.assign({\n            path: \"/engine.io\",\n            agent: false,\n            withCredentials: false,\n            upgrade: true,\n            timestampParam: \"t\",\n            rememberUpgrade: false,\n            addTrailingSlash: true,\n            rejectUnauthorized: true,\n            perMessageDeflate: {\n                threshold: 1024,\n            },\n            transportOptions: {},\n            closeOnBeforeunload: false,\n        }, opts);\n        this.opts.path =\n            this.opts.path.replace(/\\/$/, \"\") +\n                (this.opts.addTrailingSlash ? \"/\" : \"\");\n        if (typeof this.opts.query === \"string\") {\n            this.opts.query = decode(this.opts.query);\n        }\n        if (withEventListeners) {\n            if (this.opts.closeOnBeforeunload) {\n                // Firefox closes the connection when the \"beforeunload\" event is emitted but not Chrome. This event listener\n                // ensures every browser behaves the same (no \"disconnect\" event at the Socket.IO level when the page is\n                // closed/reloaded)\n                this._beforeunloadEventListener = () => {\n                    if (this.transport) {\n                        // silently close the transport\n                        this.transport.removeAllListeners();\n                        this.transport.close();\n                    }\n                };\n                addEventListener(\"beforeunload\", this._beforeunloadEventListener, false);\n            }\n            if (this.hostname !== \"localhost\") {\n                debug(\"adding listener for the 'offline' event\");\n                this._offlineEventListener = () => {\n                    this._onClose(\"transport close\", {\n                        description: \"network connection lost\",\n                    });\n                };\n                OFFLINE_EVENT_LISTENERS.push(this._offlineEventListener);\n            }\n        }\n        if (this.opts.withCredentials) {\n            this._cookieJar = createCookieJar();\n        }\n        this._open();\n    }\n    /**\n     * Creates transport of the given type.\n     *\n     * @param {String} name - transport name\n     * @return {Transport}\n     * @private\n     */\n    createTransport(name) {\n        debug('creating transport \"%s\"', name);\n        const query = Object.assign({}, this.opts.query);\n        // append engine.io protocol identifier\n        query.EIO = protocol;\n        // transport name\n        query.transport = name;\n        // session id if we already have one\n        if (this.id)\n            query.sid = this.id;\n        const opts = Object.assign({}, this.opts, {\n            query,\n            socket: this,\n            hostname: this.hostname,\n            secure: this.secure,\n            port: this.port,\n        }, this.opts.transportOptions[name]);\n        debug(\"options: %j\", opts);\n        return new this._transportsByName[name](opts);\n    }\n    /**\n     * Initializes transport to use and starts probe.\n     *\n     * @private\n     */\n    _open() {\n        if (this.transports.length === 0) {\n            // Emit error on next tick so it can be listened to\n            this.setTimeoutFn(() => {\n                this.emitReserved(\"error\", \"No transports available\");\n            }, 0);\n            return;\n        }\n        const transportName = this.opts.rememberUpgrade &&\n            SocketWithoutUpgrade.priorWebsocketSuccess &&\n            this.transports.indexOf(\"websocket\") !== -1\n            ? \"websocket\"\n            : this.transports[0];\n        this.readyState = \"opening\";\n        const transport = this.createTransport(transportName);\n        transport.open();\n        this.setTransport(transport);\n    }\n    /**\n     * Sets the current transport. Disables the existing one (if any).\n     *\n     * @private\n     */\n    setTransport(transport) {\n        debug(\"setting transport %s\", transport.name);\n        if (this.transport) {\n            debug(\"clearing existing transport %s\", this.transport.name);\n            this.transport.removeAllListeners();\n        }\n        // set up transport\n        this.transport = transport;\n        // set up transport listeners\n        transport\n            .on(\"drain\", this._onDrain.bind(this))\n            .on(\"packet\", this._onPacket.bind(this))\n            .on(\"error\", this._onError.bind(this))\n            .on(\"close\", (reason) => this._onClose(\"transport close\", reason));\n    }\n    /**\n     * Called when connection is deemed open.\n     *\n     * @private\n     */\n    onOpen() {\n        debug(\"socket open\");\n        this.readyState = \"open\";\n        SocketWithoutUpgrade.priorWebsocketSuccess =\n            \"websocket\" === this.transport.name;\n        this.emitReserved(\"open\");\n        this.flush();\n    }\n    /**\n     * Handles a packet.\n     *\n     * @private\n     */\n    _onPacket(packet) {\n        if (\"opening\" === this.readyState ||\n            \"open\" === this.readyState ||\n            \"closing\" === this.readyState) {\n            debug('socket receive: type \"%s\", data \"%s\"', packet.type, packet.data);\n            this.emitReserved(\"packet\", packet);\n            // Socket is live - any packet counts\n            this.emitReserved(\"heartbeat\");\n            switch (packet.type) {\n                case \"open\":\n                    this.onHandshake(JSON.parse(packet.data));\n                    break;\n                case \"ping\":\n                    this._sendPacket(\"pong\");\n                    this.emitReserved(\"ping\");\n                    this.emitReserved(\"pong\");\n                    this._resetPingTimeout();\n                    break;\n                case \"error\":\n                    const err = new Error(\"server error\");\n                    // @ts-ignore\n                    err.code = packet.data;\n                    this._onError(err);\n                    break;\n                case \"message\":\n                    this.emitReserved(\"data\", packet.data);\n                    this.emitReserved(\"message\", packet.data);\n                    break;\n            }\n        }\n        else {\n            debug('packet received with socket readyState \"%s\"', this.readyState);\n        }\n    }\n    /**\n     * Called upon handshake completion.\n     *\n     * @param {Object} data - handshake obj\n     * @private\n     */\n    onHandshake(data) {\n        this.emitReserved(\"handshake\", data);\n        this.id = data.sid;\n        this.transport.query.sid = data.sid;\n        this._pingInterval = data.pingInterval;\n        this._pingTimeout = data.pingTimeout;\n        this._maxPayload = data.maxPayload;\n        this.onOpen();\n        // In case open handler closes socket\n        if (\"closed\" === this.readyState)\n            return;\n        this._resetPingTimeout();\n    }\n    /**\n     * Sets and resets ping timeout timer based on server pings.\n     *\n     * @private\n     */\n    _resetPingTimeout() {\n        this.clearTimeoutFn(this._pingTimeoutTimer);\n        const delay = this._pingInterval + this._pingTimeout;\n        this._pingTimeoutTime = Date.now() + delay;\n        this._pingTimeoutTimer = this.setTimeoutFn(() => {\n            this._onClose(\"ping timeout\");\n        }, delay);\n        if (this.opts.autoUnref) {\n            this._pingTimeoutTimer.unref();\n        }\n    }\n    /**\n     * Called on `drain` event\n     *\n     * @private\n     */\n    _onDrain() {\n        this.writeBuffer.splice(0, this._prevBufferLen);\n        // setting prevBufferLen = 0 is very important\n        // for example, when upgrading, upgrade packet is sent over,\n        // and a nonzero prevBufferLen could cause problems on `drain`\n        this._prevBufferLen = 0;\n        if (0 === this.writeBuffer.length) {\n            this.emitReserved(\"drain\");\n        }\n        else {\n            this.flush();\n        }\n    }\n    /**\n     * Flush write buffers.\n     *\n     * @private\n     */\n    flush() {\n        if (\"closed\" !== this.readyState &&\n            this.transport.writable &&\n            !this.upgrading &&\n            this.writeBuffer.length) {\n            const packets = this._getWritablePackets();\n            debug(\"flushing %d packets in socket\", packets.length);\n            this.transport.send(packets);\n            // keep track of current length of writeBuffer\n            // splice writeBuffer and callbackBuffer on `drain`\n            this._prevBufferLen = packets.length;\n            this.emitReserved(\"flush\");\n        }\n    }\n    /**\n     * Ensure the encoded size of the writeBuffer is below the maxPayload value sent by the server (only for HTTP\n     * long-polling)\n     *\n     * @private\n     */\n    _getWritablePackets() {\n        const shouldCheckPayloadSize = this._maxPayload &&\n            this.transport.name === \"polling\" &&\n            this.writeBuffer.length > 1;\n        if (!shouldCheckPayloadSize) {\n            return this.writeBuffer;\n        }\n        let payloadSize = 1; // first packet type\n        for (let i = 0; i < this.writeBuffer.length; i++) {\n            const data = this.writeBuffer[i].data;\n            if (data) {\n                payloadSize += byteLength(data);\n            }\n            if (i > 0 && payloadSize > this._maxPayload) {\n                debug(\"only send %d out of %d packets\", i, this.writeBuffer.length);\n                return this.writeBuffer.slice(0, i);\n            }\n            payloadSize += 2; // separator + packet type\n        }\n        debug(\"payload size is %d (max: %d)\", payloadSize, this._maxPayload);\n        return this.writeBuffer;\n    }\n    /**\n     * Checks whether the heartbeat timer has expired but the socket has not yet been notified.\n     *\n     * Note: this method is private for now because it does not really fit the WebSocket API, but if we put it in the\n     * `write()` method then the message would not be buffered by the Socket.IO client.\n     *\n     * @return {boolean}\n     * @private\n     */\n    /* private */ _hasPingExpired() {\n        if (!this._pingTimeoutTime)\n            return true;\n        const hasExpired = Date.now() > this._pingTimeoutTime;\n        if (hasExpired) {\n            debug(\"throttled timer detected, scheduling connection close\");\n            this._pingTimeoutTime = 0;\n            nextTick(() => {\n                this._onClose(\"ping timeout\");\n            }, this.setTimeoutFn);\n        }\n        return hasExpired;\n    }\n    /**\n     * Sends a message.\n     *\n     * @param {String} msg - message.\n     * @param {Object} options.\n     * @param {Function} fn - callback function.\n     * @return {Socket} for chaining.\n     */\n    write(msg, options, fn) {\n        this._sendPacket(\"message\", msg, options, fn);\n        return this;\n    }\n    /**\n     * Sends a message. Alias of {@link Socket#write}.\n     *\n     * @param {String} msg - message.\n     * @param {Object} options.\n     * @param {Function} fn - callback function.\n     * @return {Socket} for chaining.\n     */\n    send(msg, options, fn) {\n        this._sendPacket(\"message\", msg, options, fn);\n        return this;\n    }\n    /**\n     * Sends a packet.\n     *\n     * @param {String} type: packet type.\n     * @param {String} data.\n     * @param {Object} options.\n     * @param {Function} fn - callback function.\n     * @private\n     */\n    _sendPacket(type, data, options, fn) {\n        if (\"function\" === typeof data) {\n            fn = data;\n            data = undefined;\n        }\n        if (\"function\" === typeof options) {\n            fn = options;\n            options = null;\n        }\n        if (\"closing\" === this.readyState || \"closed\" === this.readyState) {\n            return;\n        }\n        options = options || {};\n        options.compress = false !== options.compress;\n        const packet = {\n            type: type,\n            data: data,\n            options: options,\n        };\n        this.emitReserved(\"packetCreate\", packet);\n        this.writeBuffer.push(packet);\n        if (fn)\n            this.once(\"flush\", fn);\n        this.flush();\n    }\n    /**\n     * Closes the connection.\n     */\n    close() {\n        const close = () => {\n            this._onClose(\"forced close\");\n            debug(\"socket closing - telling transport to close\");\n            this.transport.close();\n        };\n        const cleanupAndClose = () => {\n            this.off(\"upgrade\", cleanupAndClose);\n            this.off(\"upgradeError\", cleanupAndClose);\n            close();\n        };\n        const waitForUpgrade = () => {\n            // wait for upgrade to finish since we can't send packets while pausing a transport\n            this.once(\"upgrade\", cleanupAndClose);\n            this.once(\"upgradeError\", cleanupAndClose);\n        };\n        if (\"opening\" === this.readyState || \"open\" === this.readyState) {\n            this.readyState = \"closing\";\n            if (this.writeBuffer.length) {\n                this.once(\"drain\", () => {\n                    if (this.upgrading) {\n                        waitForUpgrade();\n                    }\n                    else {\n                        close();\n                    }\n                });\n            }\n            else if (this.upgrading) {\n                waitForUpgrade();\n            }\n            else {\n                close();\n            }\n        }\n        return this;\n    }\n    /**\n     * Called upon transport error\n     *\n     * @private\n     */\n    _onError(err) {\n        debug(\"socket error %j\", err);\n        SocketWithoutUpgrade.priorWebsocketSuccess = false;\n        if (this.opts.tryAllTransports &&\n            this.transports.length > 1 &&\n            this.readyState === \"opening\") {\n            debug(\"trying next transport\");\n            this.transports.shift();\n            return this._open();\n        }\n        this.emitReserved(\"error\", err);\n        this._onClose(\"transport error\", err);\n    }\n    /**\n     * Called upon transport close.\n     *\n     * @private\n     */\n    _onClose(reason, description) {\n        if (\"opening\" === this.readyState ||\n            \"open\" === this.readyState ||\n            \"closing\" === this.readyState) {\n            debug('socket close with reason: \"%s\"', reason);\n            // clear timers\n            this.clearTimeoutFn(this._pingTimeoutTimer);\n            // stop event from firing again for transport\n            this.transport.removeAllListeners(\"close\");\n            // ensure transport won't stay open\n            this.transport.close();\n            // ignore further transport communication\n            this.transport.removeAllListeners();\n            if (withEventListeners) {\n                if (this._beforeunloadEventListener) {\n                    removeEventListener(\"beforeunload\", this._beforeunloadEventListener, false);\n                }\n                if (this._offlineEventListener) {\n                    const i = OFFLINE_EVENT_LISTENERS.indexOf(this._offlineEventListener);\n                    if (i !== -1) {\n                        debug(\"removing listener for the 'offline' event\");\n                        OFFLINE_EVENT_LISTENERS.splice(i, 1);\n                    }\n                }\n            }\n            // set ready state\n            this.readyState = \"closed\";\n            // clear session id\n            this.id = null;\n            // emit close event\n            this.emitReserved(\"close\", reason, description);\n            // clean buffers after, so users can still\n            // grab the buffers on `close` event\n            this.writeBuffer = [];\n            this._prevBufferLen = 0;\n        }\n    }\n}\nSocketWithoutUpgrade.protocol = protocol;\n/**\n * This class provides a WebSocket-like interface to connect to an Engine.IO server. The connection will be established\n * with one of the available low-level transports, like HTTP long-polling, WebSocket or WebTransport.\n *\n * This class comes with an upgrade mechanism, which means that once the connection is established with the first\n * low-level transport, it will try to upgrade to a better transport.\n *\n * In order to allow tree-shaking, there are no transports included, that's why the `transports` option is mandatory.\n *\n * @example\n * import { SocketWithUpgrade, WebSocket } from \"engine.io-client\";\n *\n * const socket = new SocketWithUpgrade({\n *   transports: [WebSocket]\n * });\n *\n * socket.on(\"open\", () => {\n *   socket.send(\"hello\");\n * });\n *\n * @see SocketWithoutUpgrade\n * @see Socket\n */\nexport class SocketWithUpgrade extends SocketWithoutUpgrade {\n    constructor() {\n        super(...arguments);\n        this._upgrades = [];\n    }\n    onOpen() {\n        super.onOpen();\n        if (\"open\" === this.readyState && this.opts.upgrade) {\n            debug(\"starting upgrade probes\");\n            for (let i = 0; i < this._upgrades.length; i++) {\n                this._probe(this._upgrades[i]);\n            }\n        }\n    }\n    /**\n     * Probes a transport.\n     *\n     * @param {String} name - transport name\n     * @private\n     */\n    _probe(name) {\n        debug('probing transport \"%s\"', name);\n        let transport = this.createTransport(name);\n        let failed = false;\n        SocketWithoutUpgrade.priorWebsocketSuccess = false;\n        const onTransportOpen = () => {\n            if (failed)\n                return;\n            debug('probe transport \"%s\" opened', name);\n            transport.send([{ type: \"ping\", data: \"probe\" }]);\n            transport.once(\"packet\", (msg) => {\n                if (failed)\n                    return;\n                if (\"pong\" === msg.type && \"probe\" === msg.data) {\n                    debug('probe transport \"%s\" pong', name);\n                    this.upgrading = true;\n                    this.emitReserved(\"upgrading\", transport);\n                    if (!transport)\n                        return;\n                    SocketWithoutUpgrade.priorWebsocketSuccess =\n                        \"websocket\" === transport.name;\n                    debug('pausing current transport \"%s\"', this.transport.name);\n                    this.transport.pause(() => {\n                        if (failed)\n                            return;\n                        if (\"closed\" === this.readyState)\n                            return;\n                        debug(\"changing transport and sending upgrade packet\");\n                        cleanup();\n                        this.setTransport(transport);\n                        transport.send([{ type: \"upgrade\" }]);\n                        this.emitReserved(\"upgrade\", transport);\n                        transport = null;\n                        this.upgrading = false;\n                        this.flush();\n                    });\n                }\n                else {\n                    debug('probe transport \"%s\" failed', name);\n                    const err = new Error(\"probe error\");\n                    // @ts-ignore\n                    err.transport = transport.name;\n                    this.emitReserved(\"upgradeError\", err);\n                }\n            });\n        };\n        function freezeTransport() {\n            if (failed)\n                return;\n            // Any callback called by transport should be ignored since now\n            failed = true;\n            cleanup();\n            transport.close();\n            transport = null;\n        }\n        // Handle any error that happens while probing\n        const onerror = (err) => {\n            const error = new Error(\"probe error: \" + err);\n            // @ts-ignore\n            error.transport = transport.name;\n            freezeTransport();\n            debug('probe transport \"%s\" failed because of error: %s', name, err);\n            this.emitReserved(\"upgradeError\", error);\n        };\n        function onTransportClose() {\n            onerror(\"transport closed\");\n        }\n        // When the socket is closed while we're probing\n        function onclose() {\n            onerror(\"socket closed\");\n        }\n        // When the socket is upgraded while we're probing\n        function onupgrade(to) {\n            if (transport && to.name !== transport.name) {\n                debug('\"%s\" works - aborting \"%s\"', to.name, transport.name);\n                freezeTransport();\n            }\n        }\n        // Remove all listeners on the transport and on self\n        const cleanup = () => {\n            transport.removeListener(\"open\", onTransportOpen);\n            transport.removeListener(\"error\", onerror);\n            transport.removeListener(\"close\", onTransportClose);\n            this.off(\"close\", onclose);\n            this.off(\"upgrading\", onupgrade);\n        };\n        transport.once(\"open\", onTransportOpen);\n        transport.once(\"error\", onerror);\n        transport.once(\"close\", onTransportClose);\n        this.once(\"close\", onclose);\n        this.once(\"upgrading\", onupgrade);\n        if (this._upgrades.indexOf(\"webtransport\") !== -1 &&\n            name !== \"webtransport\") {\n            // favor WebTransport\n            this.setTimeoutFn(() => {\n                if (!failed) {\n                    transport.open();\n                }\n            }, 200);\n        }\n        else {\n            transport.open();\n        }\n    }\n    onHandshake(data) {\n        this._upgrades = this._filterUpgrades(data.upgrades);\n        super.onHandshake(data);\n    }\n    /**\n     * Filters upgrades, returning only those matching client transports.\n     *\n     * @param {Array} upgrades - server upgrades\n     * @private\n     */\n    _filterUpgrades(upgrades) {\n        const filteredUpgrades = [];\n        for (let i = 0; i < upgrades.length; i++) {\n            if (~this.transports.indexOf(upgrades[i]))\n                filteredUpgrades.push(upgrades[i]);\n        }\n        return filteredUpgrades;\n    }\n}\n/**\n * This class provides a WebSocket-like interface to connect to an Engine.IO server. The connection will be established\n * with one of the available low-level transports, like HTTP long-polling, WebSocket or WebTransport.\n *\n * This class comes with an upgrade mechanism, which means that once the connection is established with the first\n * low-level transport, it will try to upgrade to a better transport.\n *\n * @example\n * import { Socket } from \"engine.io-client\";\n *\n * const socket = new Socket();\n *\n * socket.on(\"open\", () => {\n *   socket.send(\"hello\");\n * });\n *\n * @see SocketWithoutUpgrade\n * @see SocketWithUpgrade\n */\nexport class Socket extends SocketWithUpgrade {\n    constructor(uri, opts = {}) {\n        const o = typeof uri === \"object\" ? uri : opts;\n        if (!o.transports ||\n            (o.transports && typeof o.transports[0] === \"string\")) {\n            o.transports = (o.transports || [\"polling\", \"websocket\", \"webtransport\"])\n                .map((transportName) => DEFAULT_TRANSPORTS[transportName])\n                .filter((t) => !!t);\n        }\n        super(uri, o);\n    }\n}\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA,0TAAiC,UAAU;;;;;;;;;AAC3C,MAAM,QAAQ,CAAA,GAAA,uLAAA,CAAA,UAAW,AAAD,EAAE,4BAA4B,UAAU;AAChE,MAAM,qBAAqB,OAAO,qBAAqB,cACnD,OAAO,wBAAwB;AACnC,MAAM,0BAA0B,EAAE;AAClC,IAAI,oBAAoB;IACpB,mHAAmH;IACnH,2GAA2G;IAC3G,iBAAiB,WAAW;QACxB,MAAM,yDAAyD,wBAAwB,MAAM;QAC7F,wBAAwB,OAAO,CAAC,CAAC,WAAa;IAClD,GAAG;AACP;AAwBO,MAAM,6BAA6B,gQAAA,CAAA,UAAO;IAC7C;;;;;KAKC,GACD,YAAY,GAAG,EAAE,IAAI,CAAE;QACnB,KAAK;QACL,IAAI,CAAC,UAAU,GAAG,qPAAA,CAAA,oBAAiB;QACnC,IAAI,CAAC,WAAW,GAAG,EAAE;QACrB,IAAI,CAAC,cAAc,GAAG;QACtB,IAAI,CAAC,aAAa,GAAG,CAAC;QACtB,IAAI,CAAC,YAAY,GAAG,CAAC;QACrB,IAAI,CAAC,WAAW,GAAG,CAAC;QACpB;;;SAGC,GACD,IAAI,CAAC,gBAAgB,GAAG;QACxB,IAAI,OAAO,aAAa,OAAO,KAAK;YAChC,OAAO;YACP,MAAM;QACV;QACA,IAAI,KAAK;YACL,MAAM,YAAY,CAAA,GAAA,yPAAA,CAAA,QAAK,AAAD,EAAE;YACxB,KAAK,QAAQ,GAAG,UAAU,IAAI;YAC9B,KAAK,MAAM,GACP,UAAU,QAAQ,KAAK,WAAW,UAAU,QAAQ,KAAK;YAC7D,KAAK,IAAI,GAAG,UAAU,IAAI;YAC1B,IAAI,UAAU,KAAK,EACf,KAAK,KAAK,GAAG,UAAU,KAAK;QACpC,OACK,IAAI,KAAK,IAAI,EAAE;YAChB,KAAK,QAAQ,GAAG,CAAA,GAAA,yPAAA,CAAA,QAAK,AAAD,EAAE,KAAK,IAAI,EAAE,IAAI;QACzC;QACA,CAAA,GAAA,0OAAA,CAAA,wBAAqB,AAAD,EAAE,IAAI,EAAE;QAC5B,IAAI,CAAC,MAAM,GACP,QAAQ,KAAK,MAAM,GACb,KAAK,MAAM,GACX,OAAO,aAAa,eAAe,aAAa,SAAS,QAAQ;QAC3E,IAAI,KAAK,QAAQ,IAAI,CAAC,KAAK,IAAI,EAAE;YAC7B,6DAA6D;YAC7D,KAAK,IAAI,GAAG,IAAI,CAAC,MAAM,GAAG,QAAQ;QACtC;QACA,IAAI,CAAC,QAAQ,GACT,KAAK,QAAQ,IACT,CAAC,OAAO,aAAa,cAAc,SAAS,QAAQ,GAAG,WAAW;QAC1E,IAAI,CAAC,IAAI,GACL,KAAK,IAAI,IACL,CAAC,OAAO,aAAa,eAAe,SAAS,IAAI,GAC3C,SAAS,IAAI,GACb,IAAI,CAAC,MAAM,GACP,QACA,IAAI;QACtB,IAAI,CAAC,UAAU,GAAG,EAAE;QACpB,IAAI,CAAC,iBAAiB,GAAG,CAAC;QAC1B,KAAK,UAAU,CAAC,OAAO,CAAC,CAAC;YACrB,MAAM,gBAAgB,EAAE,SAAS,CAAC,IAAI;YACtC,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC;YACrB,IAAI,CAAC,iBAAiB,CAAC,cAAc,GAAG;QAC5C;QACA,IAAI,CAAC,IAAI,GAAG,OAAO,MAAM,CAAC;YACtB,MAAM;YACN,OAAO;YACP,iBAAiB;YACjB,SAAS;YACT,gBAAgB;YAChB,iBAAiB;YACjB,kBAAkB;YAClB,oBAAoB;YACpB,mBAAmB;gBACf,WAAW;YACf;YACA,kBAAkB,CAAC;YACnB,qBAAqB;QACzB,GAAG;QACH,IAAI,CAAC,IAAI,CAAC,IAAI,GACV,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO,MAC1B,CAAC,IAAI,CAAC,IAAI,CAAC,gBAAgB,GAAG,MAAM,EAAE;QAC9C,IAAI,OAAO,IAAI,CAAC,IAAI,CAAC,KAAK,KAAK,UAAU;YACrC,IAAI,CAAC,IAAI,CAAC,KAAK,GAAG,CAAA,GAAA,wPAAA,CAAA,SAAM,AAAD,EAAE,IAAI,CAAC,IAAI,CAAC,KAAK;QAC5C;QACA,IAAI,oBAAoB;YACpB,IAAI,IAAI,CAAC,IAAI,CAAC,mBAAmB,EAAE;gBAC/B,6GAA6G;gBAC7G,wGAAwG;gBACxG,mBAAmB;gBACnB,IAAI,CAAC,0BAA0B,GAAG;oBAC9B,IAAI,IAAI,CAAC,SAAS,EAAE;wBAChB,+BAA+B;wBAC/B,IAAI,CAAC,SAAS,CAAC,kBAAkB;wBACjC,IAAI,CAAC,SAAS,CAAC,KAAK;oBACxB;gBACJ;gBACA,iBAAiB,gBAAgB,IAAI,CAAC,0BAA0B,EAAE;YACtE;YACA,IAAI,IAAI,CAAC,QAAQ,KAAK,aAAa;gBAC/B,MAAM;gBACN,IAAI,CAAC,qBAAqB,GAAG;oBACzB,IAAI,CAAC,QAAQ,CAAC,mBAAmB;wBAC7B,aAAa;oBACjB;gBACJ;gBACA,wBAAwB,IAAI,CAAC,IAAI,CAAC,qBAAqB;YAC3D;QACJ;QACA,IAAI,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE;YAC3B,IAAI,CAAC,UAAU,GAAG,CAAA,GAAA,qPAAA,CAAA,kBAAe,AAAD;QACpC;QACA,IAAI,CAAC,KAAK;IACd;IACA;;;;;;KAMC,GACD,gBAAgB,IAAI,EAAE;QAClB,MAAM,2BAA2B;QACjC,MAAM,QAAQ,OAAO,MAAM,CAAC,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,KAAK;QAC/C,uCAAuC;QACvC,MAAM,GAAG,GAAG,kPAAA,CAAA,WAAQ;QACpB,iBAAiB;QACjB,MAAM,SAAS,GAAG;QAClB,oCAAoC;QACpC,IAAI,IAAI,CAAC,EAAE,EACP,MAAM,GAAG,GAAG,IAAI,CAAC,EAAE;QACvB,MAAM,OAAO,OAAO,MAAM,CAAC,CAAC,GAAG,IAAI,CAAC,IAAI,EAAE;YACtC;YACA,QAAQ,IAAI;YACZ,UAAU,IAAI,CAAC,QAAQ;YACvB,QAAQ,IAAI,CAAC,MAAM;YACnB,MAAM,IAAI,CAAC,IAAI;QACnB,GAAG,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,KAAK;QACnC,MAAM,eAAe;QACrB,OAAO,IAAI,IAAI,CAAC,iBAAiB,CAAC,KAAK,CAAC;IAC5C;IACA;;;;KAIC,GACD,QAAQ;QACJ,IAAI,IAAI,CAAC,UAAU,CAAC,MAAM,KAAK,GAAG;YAC9B,mDAAmD;YACnD,IAAI,CAAC,YAAY,CAAC;gBACd,IAAI,CAAC,YAAY,CAAC,SAAS;YAC/B,GAAG;YACH;QACJ;QACA,MAAM,gBAAgB,IAAI,CAAC,IAAI,CAAC,eAAe,IAC3C,qBAAqB,qBAAqB,IAC1C,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,iBAAiB,CAAC,IACxC,cACA,IAAI,CAAC,UAAU,CAAC,EAAE;QACxB,IAAI,CAAC,UAAU,GAAG;QAClB,MAAM,YAAY,IAAI,CAAC,eAAe,CAAC;QACvC,UAAU,IAAI;QACd,IAAI,CAAC,YAAY,CAAC;IACtB;IACA;;;;KAIC,GACD,aAAa,SAAS,EAAE;QACpB,MAAM,wBAAwB,UAAU,IAAI;QAC5C,IAAI,IAAI,CAAC,SAAS,EAAE;YAChB,MAAM,kCAAkC,IAAI,CAAC,SAAS,CAAC,IAAI;YAC3D,IAAI,CAAC,SAAS,CAAC,kBAAkB;QACrC;QACA,mBAAmB;QACnB,IAAI,CAAC,SAAS,GAAG;QACjB,6BAA6B;QAC7B,UACK,EAAE,CAAC,SAAS,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,GACnC,EAAE,CAAC,UAAU,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,GACrC,EAAE,CAAC,SAAS,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,GACnC,EAAE,CAAC,SAAS,CAAC,SAAW,IAAI,CAAC,QAAQ,CAAC,mBAAmB;IAClE;IACA;;;;KAIC,GACD,SAAS;QACL,MAAM;QACN,IAAI,CAAC,UAAU,GAAG;QAClB,qBAAqB,qBAAqB,GACtC,gBAAgB,IAAI,CAAC,SAAS,CAAC,IAAI;QACvC,IAAI,CAAC,YAAY,CAAC;QAClB,IAAI,CAAC,KAAK;IACd;IACA;;;;KAIC,GACD,UAAU,MAAM,EAAE;QACd,IAAI,cAAc,IAAI,CAAC,UAAU,IAC7B,WAAW,IAAI,CAAC,UAAU,IAC1B,cAAc,IAAI,CAAC,UAAU,EAAE;YAC/B,MAAM,wCAAwC,OAAO,IAAI,EAAE,OAAO,IAAI;YACtE,IAAI,CAAC,YAAY,CAAC,UAAU;YAC5B,qCAAqC;YACrC,IAAI,CAAC,YAAY,CAAC;YAClB,OAAQ,OAAO,IAAI;gBACf,KAAK;oBACD,IAAI,CAAC,WAAW,CAAC,KAAK,KAAK,CAAC,OAAO,IAAI;oBACvC;gBACJ,KAAK;oBACD,IAAI,CAAC,WAAW,CAAC;oBACjB,IAAI,CAAC,YAAY,CAAC;oBAClB,IAAI,CAAC,YAAY,CAAC;oBAClB,IAAI,CAAC,iBAAiB;oBACtB;gBACJ,KAAK;oBACD,MAAM,MAAM,IAAI,MAAM;oBACtB,aAAa;oBACb,IAAI,IAAI,GAAG,OAAO,IAAI;oBACtB,IAAI,CAAC,QAAQ,CAAC;oBACd;gBACJ,KAAK;oBACD,IAAI,CAAC,YAAY,CAAC,QAAQ,OAAO,IAAI;oBACrC,IAAI,CAAC,YAAY,CAAC,WAAW,OAAO,IAAI;oBACxC;YACR;QACJ,OACK;YACD,MAAM,+CAA+C,IAAI,CAAC,UAAU;QACxE;IACJ;IACA;;;;;KAKC,GACD,YAAY,IAAI,EAAE;QACd,IAAI,CAAC,YAAY,CAAC,aAAa;QAC/B,IAAI,CAAC,EAAE,GAAG,KAAK,GAAG;QAClB,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,GAAG,GAAG,KAAK,GAAG;QACnC,IAAI,CAAC,aAAa,GAAG,KAAK,YAAY;QACtC,IAAI,CAAC,YAAY,GAAG,KAAK,WAAW;QACpC,IAAI,CAAC,WAAW,GAAG,KAAK,UAAU;QAClC,IAAI,CAAC,MAAM;QACX,qCAAqC;QACrC,IAAI,aAAa,IAAI,CAAC,UAAU,EAC5B;QACJ,IAAI,CAAC,iBAAiB;IAC1B;IACA;;;;KAIC,GACD,oBAAoB;QAChB,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,iBAAiB;QAC1C,MAAM,QAAQ,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,YAAY;QACpD,IAAI,CAAC,gBAAgB,GAAG,KAAK,GAAG,KAAK;QACrC,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC,YAAY,CAAC;YACvC,IAAI,CAAC,QAAQ,CAAC;QAClB,GAAG;QACH,IAAI,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;YACrB,IAAI,CAAC,iBAAiB,CAAC,KAAK;QAChC;IACJ;IACA;;;;KAIC,GACD,WAAW;QACP,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,GAAG,IAAI,CAAC,cAAc;QAC9C,8CAA8C;QAC9C,4DAA4D;QAC5D,8DAA8D;QAC9D,IAAI,CAAC,cAAc,GAAG;QACtB,IAAI,MAAM,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE;YAC/B,IAAI,CAAC,YAAY,CAAC;QACtB,OACK;YACD,IAAI,CAAC,KAAK;QACd;IACJ;IACA;;;;KAIC,GACD,QAAQ;QACJ,IAAI,aAAa,IAAI,CAAC,UAAU,IAC5B,IAAI,CAAC,SAAS,CAAC,QAAQ,IACvB,CAAC,IAAI,CAAC,SAAS,IACf,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE;YACzB,MAAM,UAAU,IAAI,CAAC,mBAAmB;YACxC,MAAM,iCAAiC,QAAQ,MAAM;YACrD,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC;YACpB,8CAA8C;YAC9C,mDAAmD;YACnD,IAAI,CAAC,cAAc,GAAG,QAAQ,MAAM;YACpC,IAAI,CAAC,YAAY,CAAC;QACtB;IACJ;IACA;;;;;KAKC,GACD,sBAAsB;QAClB,MAAM,yBAAyB,IAAI,CAAC,WAAW,IAC3C,IAAI,CAAC,SAAS,CAAC,IAAI,KAAK,aACxB,IAAI,CAAC,WAAW,CAAC,MAAM,GAAG;QAC9B,IAAI,CAAC,wBAAwB;YACzB,OAAO,IAAI,CAAC,WAAW;QAC3B;QACA,IAAI,cAAc,GAAG,oBAAoB;QACzC,IAAK,IAAI,IAAI,GAAG,IAAI,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,IAAK;YAC9C,MAAM,OAAO,IAAI,CAAC,WAAW,CAAC,EAAE,CAAC,IAAI;YACrC,IAAI,MAAM;gBACN,eAAe,CAAA,GAAA,0OAAA,CAAA,aAAU,AAAD,EAAE;YAC9B;YACA,IAAI,IAAI,KAAK,cAAc,IAAI,CAAC,WAAW,EAAE;gBACzC,MAAM,kCAAkC,GAAG,IAAI,CAAC,WAAW,CAAC,MAAM;gBAClE,OAAO,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,GAAG;YACrC;YACA,eAAe,GAAG,0BAA0B;QAChD;QACA,MAAM,gCAAgC,aAAa,IAAI,CAAC,WAAW;QACnE,OAAO,IAAI,CAAC,WAAW;IAC3B;IACA;;;;;;;;KAQC,GACD,WAAW,GAAG,kBAAkB;QAC5B,IAAI,CAAC,IAAI,CAAC,gBAAgB,EACtB,OAAO;QACX,MAAM,aAAa,KAAK,GAAG,KAAK,IAAI,CAAC,gBAAgB;QACrD,IAAI,YAAY;YACZ,MAAM;YACN,IAAI,CAAC,gBAAgB,GAAG;YACxB,CAAA,GAAA,qPAAA,CAAA,WAAQ,AAAD,EAAE;gBACL,IAAI,CAAC,QAAQ,CAAC;YAClB,GAAG,IAAI,CAAC,YAAY;QACxB;QACA,OAAO;IACX;IACA;;;;;;;KAOC,GACD,MAAM,GAAG,EAAE,OAAO,EAAE,EAAE,EAAE;QACpB,IAAI,CAAC,WAAW,CAAC,WAAW,KAAK,SAAS;QAC1C,OAAO,IAAI;IACf;IACA;;;;;;;KAOC,GACD,KAAK,GAAG,EAAE,OAAO,EAAE,EAAE,EAAE;QACnB,IAAI,CAAC,WAAW,CAAC,WAAW,KAAK,SAAS;QAC1C,OAAO,IAAI;IACf;IACA;;;;;;;;KAQC,GACD,YAAY,IAAI,EAAE,IAAI,EAAE,OAAO,EAAE,EAAE,EAAE;QACjC,IAAI,eAAe,OAAO,MAAM;YAC5B,KAAK;YACL,OAAO;QACX;QACA,IAAI,eAAe,OAAO,SAAS;YAC/B,KAAK;YACL,UAAU;QACd;QACA,IAAI,cAAc,IAAI,CAAC,UAAU,IAAI,aAAa,IAAI,CAAC,UAAU,EAAE;YAC/D;QACJ;QACA,UAAU,WAAW,CAAC;QACtB,QAAQ,QAAQ,GAAG,UAAU,QAAQ,QAAQ;QAC7C,MAAM,SAAS;YACX,MAAM;YACN,MAAM;YACN,SAAS;QACb;QACA,IAAI,CAAC,YAAY,CAAC,gBAAgB;QAClC,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC;QACtB,IAAI,IACA,IAAI,CAAC,IAAI,CAAC,SAAS;QACvB,IAAI,CAAC,KAAK;IACd;IACA;;KAEC,GACD,QAAQ;QACJ,MAAM,QAAQ;YACV,IAAI,CAAC,QAAQ,CAAC;YACd,MAAM;YACN,IAAI,CAAC,SAAS,CAAC,KAAK;QACxB;QACA,MAAM,kBAAkB;YACpB,IAAI,CAAC,GAAG,CAAC,WAAW;YACpB,IAAI,CAAC,GAAG,CAAC,gBAAgB;YACzB;QACJ;QACA,MAAM,iBAAiB;YACnB,mFAAmF;YACnF,IAAI,CAAC,IAAI,CAAC,WAAW;YACrB,IAAI,CAAC,IAAI,CAAC,gBAAgB;QAC9B;QACA,IAAI,cAAc,IAAI,CAAC,UAAU,IAAI,WAAW,IAAI,CAAC,UAAU,EAAE;YAC7D,IAAI,CAAC,UAAU,GAAG;YAClB,IAAI,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE;gBACzB,IAAI,CAAC,IAAI,CAAC,SAAS;oBACf,IAAI,IAAI,CAAC,SAAS,EAAE;wBAChB;oBACJ,OACK;wBACD;oBACJ;gBACJ;YACJ,OACK,IAAI,IAAI,CAAC,SAAS,EAAE;gBACrB;YACJ,OACK;gBACD;YACJ;QACJ;QACA,OAAO,IAAI;IACf;IACA;;;;KAIC,GACD,SAAS,GAAG,EAAE;QACV,MAAM,mBAAmB;QACzB,qBAAqB,qBAAqB,GAAG;QAC7C,IAAI,IAAI,CAAC,IAAI,CAAC,gBAAgB,IAC1B,IAAI,CAAC,UAAU,CAAC,MAAM,GAAG,KACzB,IAAI,CAAC,UAAU,KAAK,WAAW;YAC/B,MAAM;YACN,IAAI,CAAC,UAAU,CAAC,KAAK;YACrB,OAAO,IAAI,CAAC,KAAK;QACrB;QACA,IAAI,CAAC,YAAY,CAAC,SAAS;QAC3B,IAAI,CAAC,QAAQ,CAAC,mBAAmB;IACrC;IACA;;;;KAIC,GACD,SAAS,MAAM,EAAE,WAAW,EAAE;QAC1B,IAAI,cAAc,IAAI,CAAC,UAAU,IAC7B,WAAW,IAAI,CAAC,UAAU,IAC1B,cAAc,IAAI,CAAC,UAAU,EAAE;YAC/B,MAAM,kCAAkC;YACxC,eAAe;YACf,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,iBAAiB;YAC1C,6CAA6C;YAC7C,IAAI,CAAC,SAAS,CAAC,kBAAkB,CAAC;YAClC,mCAAmC;YACnC,IAAI,CAAC,SAAS,CAAC,KAAK;YACpB,yCAAyC;YACzC,IAAI,CAAC,SAAS,CAAC,kBAAkB;YACjC,IAAI,oBAAoB;gBACpB,IAAI,IAAI,CAAC,0BAA0B,EAAE;oBACjC,oBAAoB,gBAAgB,IAAI,CAAC,0BAA0B,EAAE;gBACzE;gBACA,IAAI,IAAI,CAAC,qBAAqB,EAAE;oBAC5B,MAAM,IAAI,wBAAwB,OAAO,CAAC,IAAI,CAAC,qBAAqB;oBACpE,IAAI,MAAM,CAAC,GAAG;wBACV,MAAM;wBACN,wBAAwB,MAAM,CAAC,GAAG;oBACtC;gBACJ;YACJ;YACA,kBAAkB;YAClB,IAAI,CAAC,UAAU,GAAG;YAClB,mBAAmB;YACnB,IAAI,CAAC,EAAE,GAAG;YACV,mBAAmB;YACnB,IAAI,CAAC,YAAY,CAAC,SAAS,QAAQ;YACnC,0CAA0C;YAC1C,oCAAoC;YACpC,IAAI,CAAC,WAAW,GAAG,EAAE;YACrB,IAAI,CAAC,cAAc,GAAG;QAC1B;IACJ;AACJ;AACA,qBAAqB,QAAQ,GAAG,kPAAA,CAAA,WAAQ;AAwBjC,MAAM,0BAA0B;IACnC,aAAc;QACV,KAAK,IAAI;QACT,IAAI,CAAC,SAAS,GAAG,EAAE;IACvB;IACA,SAAS;QACL,KAAK,CAAC;QACN,IAAI,WAAW,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE;YACjD,MAAM;YACN,IAAK,IAAI,IAAI,GAAG,IAAI,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,IAAK;gBAC5C,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE;YACjC;QACJ;IACJ;IACA;;;;;KAKC,GACD,OAAO,IAAI,EAAE;QACT,MAAM,0BAA0B;QAChC,IAAI,YAAY,IAAI,CAAC,eAAe,CAAC;QACrC,IAAI,SAAS;QACb,qBAAqB,qBAAqB,GAAG;QAC7C,MAAM,kBAAkB;YACpB,IAAI,QACA;YACJ,MAAM,+BAA+B;YACrC,UAAU,IAAI,CAAC;gBAAC;oBAAE,MAAM;oBAAQ,MAAM;gBAAQ;aAAE;YAChD,UAAU,IAAI,CAAC,UAAU,CAAC;gBACtB,IAAI,QACA;gBACJ,IAAI,WAAW,IAAI,IAAI,IAAI,YAAY,IAAI,IAAI,EAAE;oBAC7C,MAAM,6BAA6B;oBACnC,IAAI,CAAC,SAAS,GAAG;oBACjB,IAAI,CAAC,YAAY,CAAC,aAAa;oBAC/B,IAAI,CAAC,WACD;oBACJ,qBAAqB,qBAAqB,GACtC,gBAAgB,UAAU,IAAI;oBAClC,MAAM,kCAAkC,IAAI,CAAC,SAAS,CAAC,IAAI;oBAC3D,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC;wBACjB,IAAI,QACA;wBACJ,IAAI,aAAa,IAAI,CAAC,UAAU,EAC5B;wBACJ,MAAM;wBACN;wBACA,IAAI,CAAC,YAAY,CAAC;wBAClB,UAAU,IAAI,CAAC;4BAAC;gCAAE,MAAM;4BAAU;yBAAE;wBACpC,IAAI,CAAC,YAAY,CAAC,WAAW;wBAC7B,YAAY;wBACZ,IAAI,CAAC,SAAS,GAAG;wBACjB,IAAI,CAAC,KAAK;oBACd;gBACJ,OACK;oBACD,MAAM,+BAA+B;oBACrC,MAAM,MAAM,IAAI,MAAM;oBACtB,aAAa;oBACb,IAAI,SAAS,GAAG,UAAU,IAAI;oBAC9B,IAAI,CAAC,YAAY,CAAC,gBAAgB;gBACtC;YACJ;QACJ;QACA,SAAS;YACL,IAAI,QACA;YACJ,+DAA+D;YAC/D,SAAS;YACT;YACA,UAAU,KAAK;YACf,YAAY;QAChB;QACA,8CAA8C;QAC9C,MAAM,UAAU,CAAC;YACb,MAAM,QAAQ,IAAI,MAAM,kBAAkB;YAC1C,aAAa;YACb,MAAM,SAAS,GAAG,UAAU,IAAI;YAChC;YACA,MAAM,oDAAoD,MAAM;YAChE,IAAI,CAAC,YAAY,CAAC,gBAAgB;QACtC;QACA,SAAS;YACL,QAAQ;QACZ;QACA,gDAAgD;QAChD,SAAS;YACL,QAAQ;QACZ;QACA,kDAAkD;QAClD,SAAS,UAAU,EAAE;YACjB,IAAI,aAAa,GAAG,IAAI,KAAK,UAAU,IAAI,EAAE;gBACzC,MAAM,8BAA8B,GAAG,IAAI,EAAE,UAAU,IAAI;gBAC3D;YACJ;QACJ;QACA,oDAAoD;QACpD,MAAM,UAAU;YACZ,UAAU,cAAc,CAAC,QAAQ;YACjC,UAAU,cAAc,CAAC,SAAS;YAClC,UAAU,cAAc,CAAC,SAAS;YAClC,IAAI,CAAC,GAAG,CAAC,SAAS;YAClB,IAAI,CAAC,GAAG,CAAC,aAAa;QAC1B;QACA,UAAU,IAAI,CAAC,QAAQ;QACvB,UAAU,IAAI,CAAC,SAAS;QACxB,UAAU,IAAI,CAAC,SAAS;QACxB,IAAI,CAAC,IAAI,CAAC,SAAS;QACnB,IAAI,CAAC,IAAI,CAAC,aAAa;QACvB,IAAI,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,oBAAoB,CAAC,KAC5C,SAAS,gBAAgB;YACzB,qBAAqB;YACrB,IAAI,CAAC,YAAY,CAAC;gBACd,IAAI,CAAC,QAAQ;oBACT,UAAU,IAAI;gBAClB;YACJ,GAAG;QACP,OACK;YACD,UAAU,IAAI;QAClB;IACJ;IACA,YAAY,IAAI,EAAE;QACd,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,eAAe,CAAC,KAAK,QAAQ;QACnD,KAAK,CAAC,YAAY;IACtB;IACA;;;;;KAKC,GACD,gBAAgB,QAAQ,EAAE;QACtB,MAAM,mBAAmB,EAAE;QAC3B,IAAK,IAAI,IAAI,GAAG,IAAI,SAAS,MAAM,EAAE,IAAK;YACtC,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE,GACpC,iBAAiB,IAAI,CAAC,QAAQ,CAAC,EAAE;QACzC;QACA,OAAO;IACX;AACJ;AAoBO,MAAM,eAAe;IACxB,YAAY,GAAG,EAAE,OAAO,CAAC,CAAC,CAAE;QACxB,MAAM,IAAI,OAAO,QAAQ,WAAW,MAAM;QAC1C,IAAI,CAAC,EAAE,UAAU,IACZ,EAAE,UAAU,IAAI,OAAO,EAAE,UAAU,CAAC,EAAE,KAAK,UAAW;YACvD,EAAE,UAAU,GAAG,CAAC,EAAE,UAAU,IAAI;gBAAC;gBAAW;gBAAa;aAAe,EACnE,GAAG,CAAC,CAAC,gBAAkB,yPAAA,CAAA,aAAkB,CAAC,cAAc,EACxD,MAAM,CAAC,CAAC,IAAM,CAAC,CAAC;QACzB;QACA,KAAK,CAAC,KAAK;IACf;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2830, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/bloomgoo/gold-exchange-platform/frontend/node_modules/.pnpm/engine.io-client%406.6.3/node_modules/engine.io-client/build/esm-debug/transports/polling-fetch.js"], "sourcesContent": ["import { Polling } from \"./polling.js\";\n/**\n * HTTP long-polling based on the built-in `fetch()` method.\n *\n * Usage: browser, Node.js (since v18), <PERSON><PERSON>, <PERSON>un\n *\n * @see https://developer.mozilla.org/en-US/docs/Web/API/fetch\n * @see https://caniuse.com/fetch\n * @see https://nodejs.org/api/globals.html#fetch\n */\nexport class Fetch extends Polling {\n    doPoll() {\n        this._fetch()\n            .then((res) => {\n            if (!res.ok) {\n                return this.onError(\"fetch read error\", res.status, res);\n            }\n            res.text().then((data) => this.onData(data));\n        })\n            .catch((err) => {\n            this.onError(\"fetch read error\", err);\n        });\n    }\n    doWrite(data, callback) {\n        this._fetch(data)\n            .then((res) => {\n            if (!res.ok) {\n                return this.onError(\"fetch write error\", res.status, res);\n            }\n            callback();\n        })\n            .catch((err) => {\n            this.onError(\"fetch write error\", err);\n        });\n    }\n    _fetch(data) {\n        var _a;\n        const isPost = data !== undefined;\n        const headers = new Headers(this.opts.extraHeaders);\n        if (isPost) {\n            headers.set(\"content-type\", \"text/plain;charset=UTF-8\");\n        }\n        (_a = this.socket._cookieJar) === null || _a === void 0 ? void 0 : _a.appendCookies(headers);\n        return fetch(this.uri(), {\n            method: isPost ? \"POST\" : \"GET\",\n            body: isPost ? data : null,\n            headers,\n            credentials: this.opts.withCredentials ? \"include\" : \"omit\",\n        }).then((res) => {\n            var _a;\n            // @ts-ignore getSetCookie() was added in Node.js v19.7.0\n            (_a = this.socket._cookieJar) === null || _a === void 0 ? void 0 : _a.parseCookies(res.headers.getSetCookie());\n            return res;\n        });\n    }\n}\n"], "names": [], "mappings": ";;;AAAA;;AAUO,MAAM,cAAc,2PAAA,CAAA,UAAO;IAC9B,SAAS;QACL,IAAI,CAAC,MAAM,GACN,IAAI,CAAC,CAAC;YACP,IAAI,CAAC,IAAI,EAAE,EAAE;gBACT,OAAO,IAAI,CAAC,OAAO,CAAC,oBAAoB,IAAI,MAAM,EAAE;YACxD;YACA,IAAI,IAAI,GAAG,IAAI,CAAC,CAAC,OAAS,IAAI,CAAC,MAAM,CAAC;QAC1C,GACK,KAAK,CAAC,CAAC;YACR,IAAI,CAAC,OAAO,CAAC,oBAAoB;QACrC;IACJ;IACA,QAAQ,IAAI,EAAE,QAAQ,EAAE;QACpB,IAAI,CAAC,MAAM,CAAC,MACP,IAAI,CAAC,CAAC;YACP,IAAI,CAAC,IAAI,EAAE,EAAE;gBACT,OAAO,IAAI,CAAC,OAAO,CAAC,qBAAqB,IAAI,MAAM,EAAE;YACzD;YACA;QACJ,GACK,KAAK,CAAC,CAAC;YACR,IAAI,CAAC,OAAO,CAAC,qBAAqB;QACtC;IACJ;IACA,OAAO,IAAI,EAAE;QACT,IAAI;QACJ,MAAM,SAAS,SAAS;QACxB,MAAM,UAAU,IAAI,QAAQ,IAAI,CAAC,IAAI,CAAC,YAAY;QAClD,IAAI,QAAQ;YACR,QAAQ,GAAG,CAAC,gBAAgB;QAChC;QACA,CAAC,KAAK,IAAI,CAAC,MAAM,CAAC,UAAU,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,IAAI,GAAG,aAAa,CAAC;QACpF,OAAO,MAAM,IAAI,CAAC,GAAG,IAAI;YACrB,QAAQ,SAAS,SAAS;YAC1B,MAAM,SAAS,OAAO;YACtB;YACA,aAAa,IAAI,CAAC,IAAI,CAAC,eAAe,GAAG,YAAY;QACzD,GAAG,IAAI,CAAC,CAAC;YACL,IAAI;YACJ,yDAAyD;YACzD,CAAC,KAAK,IAAI,CAAC,MAAM,CAAC,UAAU,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,IAAI,GAAG,YAAY,CAAC,IAAI,OAAO,CAAC,YAAY;YAC3G,OAAO;QACX;IACJ;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2883, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/bloomgoo/gold-exchange-platform/frontend/node_modules/.pnpm/engine.io-client%406.6.3/node_modules/engine.io-client/build/esm-debug/index.js"], "sourcesContent": ["import { Socket } from \"./socket.js\";\nexport { Socket };\nexport { SocketWithoutUpgrade, SocketWithUpgrade, } from \"./socket.js\";\nexport const protocol = Socket.protocol;\nexport { Transport, TransportError } from \"./transport.js\";\nexport { transports } from \"./transports/index.js\";\nexport { installTimerFunctions } from \"./util.js\";\nexport { parse } from \"./contrib/parseuri.js\";\nexport { nextTick } from \"./globals.node.js\";\nexport { Fetch } from \"./transports/polling-fetch.js\";\nexport { XHR as NodeXHR } from \"./transports/polling-xhr.node.js\";\nexport { XHR } from \"./transports/polling-xhr.js\";\nexport { WS as NodeWebSocket } from \"./transports/websocket.node.js\";\nexport { WS as WebSocket } from \"./transports/websocket.js\";\nexport { WT as WebTransport } from \"./transports/webtransport.js\";\n"], "names": [], "mappings": ";;;AAAA;AAIA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;AAXO,MAAM,WAAW,4OAAA,CAAA,SAAM,CAAC,QAAQ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2938, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/bloomgoo/gold-exchange-platform/frontend/node_modules/.pnpm/ms%402.1.3/node_modules/ms/index.js"], "sourcesContent": ["/**\n * Helpers.\n */\n\nvar s = 1000;\nvar m = s * 60;\nvar h = m * 60;\nvar d = h * 24;\nvar w = d * 7;\nvar y = d * 365.25;\n\n/**\n * Parse or format the given `val`.\n *\n * Options:\n *\n *  - `long` verbose formatting [false]\n *\n * @param {String|Number} val\n * @param {Object} [options]\n * @throws {Error} throw an error if val is not a non-empty string or a number\n * @return {String|Number}\n * @api public\n */\n\nmodule.exports = function (val, options) {\n  options = options || {};\n  var type = typeof val;\n  if (type === 'string' && val.length > 0) {\n    return parse(val);\n  } else if (type === 'number' && isFinite(val)) {\n    return options.long ? fmtLong(val) : fmtShort(val);\n  }\n  throw new Error(\n    'val is not a non-empty string or a valid number. val=' +\n      JSON.stringify(val)\n  );\n};\n\n/**\n * Parse the given `str` and return milliseconds.\n *\n * @param {String} str\n * @return {Number}\n * @api private\n */\n\nfunction parse(str) {\n  str = String(str);\n  if (str.length > 100) {\n    return;\n  }\n  var match = /^(-?(?:\\d+)?\\.?\\d+) *(milliseconds?|msecs?|ms|seconds?|secs?|s|minutes?|mins?|m|hours?|hrs?|h|days?|d|weeks?|w|years?|yrs?|y)?$/i.exec(\n    str\n  );\n  if (!match) {\n    return;\n  }\n  var n = parseFloat(match[1]);\n  var type = (match[2] || 'ms').toLowerCase();\n  switch (type) {\n    case 'years':\n    case 'year':\n    case 'yrs':\n    case 'yr':\n    case 'y':\n      return n * y;\n    case 'weeks':\n    case 'week':\n    case 'w':\n      return n * w;\n    case 'days':\n    case 'day':\n    case 'd':\n      return n * d;\n    case 'hours':\n    case 'hour':\n    case 'hrs':\n    case 'hr':\n    case 'h':\n      return n * h;\n    case 'minutes':\n    case 'minute':\n    case 'mins':\n    case 'min':\n    case 'm':\n      return n * m;\n    case 'seconds':\n    case 'second':\n    case 'secs':\n    case 'sec':\n    case 's':\n      return n * s;\n    case 'milliseconds':\n    case 'millisecond':\n    case 'msecs':\n    case 'msec':\n    case 'ms':\n      return n;\n    default:\n      return undefined;\n  }\n}\n\n/**\n * Short format for `ms`.\n *\n * @param {Number} ms\n * @return {String}\n * @api private\n */\n\nfunction fmtShort(ms) {\n  var msAbs = Math.abs(ms);\n  if (msAbs >= d) {\n    return Math.round(ms / d) + 'd';\n  }\n  if (msAbs >= h) {\n    return Math.round(ms / h) + 'h';\n  }\n  if (msAbs >= m) {\n    return Math.round(ms / m) + 'm';\n  }\n  if (msAbs >= s) {\n    return Math.round(ms / s) + 's';\n  }\n  return ms + 'ms';\n}\n\n/**\n * Long format for `ms`.\n *\n * @param {Number} ms\n * @return {String}\n * @api private\n */\n\nfunction fmtLong(ms) {\n  var msAbs = Math.abs(ms);\n  if (msAbs >= d) {\n    return plural(ms, msAbs, d, 'day');\n  }\n  if (msAbs >= h) {\n    return plural(ms, msAbs, h, 'hour');\n  }\n  if (msAbs >= m) {\n    return plural(ms, msAbs, m, 'minute');\n  }\n  if (msAbs >= s) {\n    return plural(ms, msAbs, s, 'second');\n  }\n  return ms + ' ms';\n}\n\n/**\n * Pluralization helper.\n */\n\nfunction plural(ms, msAbs, n, name) {\n  var isPlural = msAbs >= n * 1.5;\n  return Math.round(ms / n) + ' ' + name + (isPlural ? 's' : '');\n}\n"], "names": [], "mappings": "AAAA;;CAEC,GAED,IAAI,IAAI;AACR,IAAI,IAAI,IAAI;AACZ,IAAI,IAAI,IAAI;AACZ,IAAI,IAAI,IAAI;AACZ,IAAI,IAAI,IAAI;AACZ,IAAI,IAAI,IAAI;AAEZ;;;;;;;;;;;;CAYC,GAED,OAAO,OAAO,GAAG,SAAU,GAAG,EAAE,OAAO;IACrC,UAAU,WAAW,CAAC;IACtB,IAAI,OAAO,OAAO;IAClB,IAAI,SAAS,YAAY,IAAI,MAAM,GAAG,GAAG;QACvC,OAAO,MAAM;IACf,OAAO,IAAI,SAAS,YAAY,SAAS,MAAM;QAC7C,OAAO,QAAQ,IAAI,GAAG,QAAQ,OAAO,SAAS;IAChD;IACA,MAAM,IAAI,MACR,0DACE,KAAK,SAAS,CAAC;AAErB;AAEA;;;;;;CAMC,GAED,SAAS,MAAM,GAAG;IAChB,MAAM,OAAO;IACb,IAAI,IAAI,MAAM,GAAG,KAAK;QACpB;IACF;IACA,IAAI,QAAQ,mIAAmI,IAAI,CACjJ;IAEF,IAAI,CAAC,OAAO;QACV;IACF;IACA,IAAI,IAAI,WAAW,KAAK,CAAC,EAAE;IAC3B,IAAI,OAAO,CAAC,KAAK,CAAC,EAAE,IAAI,IAAI,EAAE,WAAW;IACzC,OAAQ;QACN,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;YACH,OAAO,IAAI;QACb,KAAK;QACL,KAAK;QACL,KAAK;YACH,OAAO,IAAI;QACb,KAAK;QACL,KAAK;QACL,KAAK;YACH,OAAO,IAAI;QACb,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;YACH,OAAO,IAAI;QACb,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;YACH,OAAO,IAAI;QACb,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;YACH,OAAO,IAAI;QACb,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;YACH,OAAO;QACT;YACE,OAAO;IACX;AACF;AAEA;;;;;;CAMC,GAED,SAAS,SAAS,EAAE;IAClB,IAAI,QAAQ,KAAK,GAAG,CAAC;IACrB,IAAI,SAAS,GAAG;QACd,OAAO,KAAK,KAAK,CAAC,KAAK,KAAK;IAC9B;IACA,IAAI,SAAS,GAAG;QACd,OAAO,KAAK,KAAK,CAAC,KAAK,KAAK;IAC9B;IACA,IAAI,SAAS,GAAG;QACd,OAAO,KAAK,KAAK,CAAC,KAAK,KAAK;IAC9B;IACA,IAAI,SAAS,GAAG;QACd,OAAO,KAAK,KAAK,CAAC,KAAK,KAAK;IAC9B;IACA,OAAO,KAAK;AACd;AAEA;;;;;;CAMC,GAED,SAAS,QAAQ,EAAE;IACjB,IAAI,QAAQ,KAAK,GAAG,CAAC;IACrB,IAAI,SAAS,GAAG;QACd,OAAO,OAAO,IAAI,OAAO,GAAG;IAC9B;IACA,IAAI,SAAS,GAAG;QACd,OAAO,OAAO,IAAI,OAAO,GAAG;IAC9B;IACA,IAAI,SAAS,GAAG;QACd,OAAO,OAAO,IAAI,OAAO,GAAG;IAC9B;IACA,IAAI,SAAS,GAAG;QACd,OAAO,OAAO,IAAI,OAAO,GAAG;IAC9B;IACA,OAAO,KAAK;AACd;AAEA;;CAEC,GAED,SAAS,OAAO,EAAE,EAAE,KAAK,EAAE,CAAC,EAAE,IAAI;IAChC,IAAI,WAAW,SAAS,IAAI;IAC5B,OAAO,KAAK,KAAK,CAAC,KAAK,KAAK,MAAM,OAAO,CAAC,WAAW,MAAM,EAAE;AAC/D", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3083, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/bloomgoo/gold-exchange-platform/frontend/node_modules/.pnpm/debug%404.3.7/node_modules/debug/src/common.js"], "sourcesContent": ["\n/**\n * This is the common logic for both the Node.js and web browser\n * implementations of `debug()`.\n */\n\nfunction setup(env) {\n\tcreateDebug.debug = createDebug;\n\tcreateDebug.default = createDebug;\n\tcreateDebug.coerce = coerce;\n\tcreateDebug.disable = disable;\n\tcreateDebug.enable = enable;\n\tcreateDebug.enabled = enabled;\n\tcreateDebug.humanize = require('ms');\n\tcreateDebug.destroy = destroy;\n\n\tObject.keys(env).forEach(key => {\n\t\tcreateDebug[key] = env[key];\n\t});\n\n\t/**\n\t* The currently active debug mode names, and names to skip.\n\t*/\n\n\tcreateDebug.names = [];\n\tcreateDebug.skips = [];\n\n\t/**\n\t* Map of special \"%n\" handling functions, for the debug \"format\" argument.\n\t*\n\t* Valid key names are a single, lower or upper-case letter, i.e. \"n\" and \"N\".\n\t*/\n\tcreateDebug.formatters = {};\n\n\t/**\n\t* Selects a color for a debug namespace\n\t* @param {String} namespace The namespace string for the debug instance to be colored\n\t* @return {Number|String} An ANSI color code for the given namespace\n\t* @api private\n\t*/\n\tfunction selectColor(namespace) {\n\t\tlet hash = 0;\n\n\t\tfor (let i = 0; i < namespace.length; i++) {\n\t\t\thash = ((hash << 5) - hash) + namespace.charCodeAt(i);\n\t\t\thash |= 0; // Convert to 32bit integer\n\t\t}\n\n\t\treturn createDebug.colors[Math.abs(hash) % createDebug.colors.length];\n\t}\n\tcreateDebug.selectColor = selectColor;\n\n\t/**\n\t* Create a debugger with the given `namespace`.\n\t*\n\t* @param {String} namespace\n\t* @return {Function}\n\t* @api public\n\t*/\n\tfunction createDebug(namespace) {\n\t\tlet prevTime;\n\t\tlet enableOverride = null;\n\t\tlet namespacesCache;\n\t\tlet enabledCache;\n\n\t\tfunction debug(...args) {\n\t\t\t// Disabled?\n\t\t\tif (!debug.enabled) {\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\tconst self = debug;\n\n\t\t\t// Set `diff` timestamp\n\t\t\tconst curr = Number(new Date());\n\t\t\tconst ms = curr - (prevTime || curr);\n\t\t\tself.diff = ms;\n\t\t\tself.prev = prevTime;\n\t\t\tself.curr = curr;\n\t\t\tprevTime = curr;\n\n\t\t\targs[0] = createDebug.coerce(args[0]);\n\n\t\t\tif (typeof args[0] !== 'string') {\n\t\t\t\t// Anything else let's inspect with %O\n\t\t\t\targs.unshift('%O');\n\t\t\t}\n\n\t\t\t// Apply any `formatters` transformations\n\t\t\tlet index = 0;\n\t\t\targs[0] = args[0].replace(/%([a-zA-Z%])/g, (match, format) => {\n\t\t\t\t// If we encounter an escaped % then don't increase the array index\n\t\t\t\tif (match === '%%') {\n\t\t\t\t\treturn '%';\n\t\t\t\t}\n\t\t\t\tindex++;\n\t\t\t\tconst formatter = createDebug.formatters[format];\n\t\t\t\tif (typeof formatter === 'function') {\n\t\t\t\t\tconst val = args[index];\n\t\t\t\t\tmatch = formatter.call(self, val);\n\n\t\t\t\t\t// Now we need to remove `args[index]` since it's inlined in the `format`\n\t\t\t\t\targs.splice(index, 1);\n\t\t\t\t\tindex--;\n\t\t\t\t}\n\t\t\t\treturn match;\n\t\t\t});\n\n\t\t\t// Apply env-specific formatting (colors, etc.)\n\t\t\tcreateDebug.formatArgs.call(self, args);\n\n\t\t\tconst logFn = self.log || createDebug.log;\n\t\t\tlogFn.apply(self, args);\n\t\t}\n\n\t\tdebug.namespace = namespace;\n\t\tdebug.useColors = createDebug.useColors();\n\t\tdebug.color = createDebug.selectColor(namespace);\n\t\tdebug.extend = extend;\n\t\tdebug.destroy = createDebug.destroy; // XXX Temporary. Will be removed in the next major release.\n\n\t\tObject.defineProperty(debug, 'enabled', {\n\t\t\tenumerable: true,\n\t\t\tconfigurable: false,\n\t\t\tget: () => {\n\t\t\t\tif (enableOverride !== null) {\n\t\t\t\t\treturn enableOverride;\n\t\t\t\t}\n\t\t\t\tif (namespacesCache !== createDebug.namespaces) {\n\t\t\t\t\tnamespacesCache = createDebug.namespaces;\n\t\t\t\t\tenabledCache = createDebug.enabled(namespace);\n\t\t\t\t}\n\n\t\t\t\treturn enabledCache;\n\t\t\t},\n\t\t\tset: v => {\n\t\t\t\tenableOverride = v;\n\t\t\t}\n\t\t});\n\n\t\t// Env-specific initialization logic for debug instances\n\t\tif (typeof createDebug.init === 'function') {\n\t\t\tcreateDebug.init(debug);\n\t\t}\n\n\t\treturn debug;\n\t}\n\n\tfunction extend(namespace, delimiter) {\n\t\tconst newDebug = createDebug(this.namespace + (typeof delimiter === 'undefined' ? ':' : delimiter) + namespace);\n\t\tnewDebug.log = this.log;\n\t\treturn newDebug;\n\t}\n\n\t/**\n\t* Enables a debug mode by namespaces. This can include modes\n\t* separated by a colon and wildcards.\n\t*\n\t* @param {String} namespaces\n\t* @api public\n\t*/\n\tfunction enable(namespaces) {\n\t\tcreateDebug.save(namespaces);\n\t\tcreateDebug.namespaces = namespaces;\n\n\t\tcreateDebug.names = [];\n\t\tcreateDebug.skips = [];\n\n\t\tlet i;\n\t\tconst split = (typeof namespaces === 'string' ? namespaces : '').split(/[\\s,]+/);\n\t\tconst len = split.length;\n\n\t\tfor (i = 0; i < len; i++) {\n\t\t\tif (!split[i]) {\n\t\t\t\t// ignore empty strings\n\t\t\t\tcontinue;\n\t\t\t}\n\n\t\t\tnamespaces = split[i].replace(/\\*/g, '.*?');\n\n\t\t\tif (namespaces[0] === '-') {\n\t\t\t\tcreateDebug.skips.push(new RegExp('^' + namespaces.slice(1) + '$'));\n\t\t\t} else {\n\t\t\t\tcreateDebug.names.push(new RegExp('^' + namespaces + '$'));\n\t\t\t}\n\t\t}\n\t}\n\n\t/**\n\t* Disable debug output.\n\t*\n\t* @return {String} namespaces\n\t* @api public\n\t*/\n\tfunction disable() {\n\t\tconst namespaces = [\n\t\t\t...createDebug.names.map(toNamespace),\n\t\t\t...createDebug.skips.map(toNamespace).map(namespace => '-' + namespace)\n\t\t].join(',');\n\t\tcreateDebug.enable('');\n\t\treturn namespaces;\n\t}\n\n\t/**\n\t* Returns true if the given mode name is enabled, false otherwise.\n\t*\n\t* @param {String} name\n\t* @return {Boolean}\n\t* @api public\n\t*/\n\tfunction enabled(name) {\n\t\tif (name[name.length - 1] === '*') {\n\t\t\treturn true;\n\t\t}\n\n\t\tlet i;\n\t\tlet len;\n\n\t\tfor (i = 0, len = createDebug.skips.length; i < len; i++) {\n\t\t\tif (createDebug.skips[i].test(name)) {\n\t\t\t\treturn false;\n\t\t\t}\n\t\t}\n\n\t\tfor (i = 0, len = createDebug.names.length; i < len; i++) {\n\t\t\tif (createDebug.names[i].test(name)) {\n\t\t\t\treturn true;\n\t\t\t}\n\t\t}\n\n\t\treturn false;\n\t}\n\n\t/**\n\t* Convert regexp to namespace\n\t*\n\t* @param {RegExp} regxep\n\t* @return {String} namespace\n\t* @api private\n\t*/\n\tfunction toNamespace(regexp) {\n\t\treturn regexp.toString()\n\t\t\t.substring(2, regexp.toString().length - 2)\n\t\t\t.replace(/\\.\\*\\?$/, '*');\n\t}\n\n\t/**\n\t* Coerce `val`.\n\t*\n\t* @param {Mixed} val\n\t* @return {Mixed}\n\t* @api private\n\t*/\n\tfunction coerce(val) {\n\t\tif (val instanceof Error) {\n\t\t\treturn val.stack || val.message;\n\t\t}\n\t\treturn val;\n\t}\n\n\t/**\n\t* XXX DO NOT USE. This is a temporary stub function.\n\t* XXX It WILL be removed in the next major release.\n\t*/\n\tfunction destroy() {\n\t\tconsole.warn('Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`.');\n\t}\n\n\tcreateDebug.enable(createDebug.load());\n\n\treturn createDebug;\n}\n\nmodule.exports = setup;\n"], "names": [], "mappings": "AACA;;;CAGC,GAED,SAAS,MAAM,GAAG;IACjB,YAAY,KAAK,GAAG;IACpB,YAAY,OAAO,GAAG;IACtB,YAAY,MAAM,GAAG;IACrB,YAAY,OAAO,GAAG;IACtB,YAAY,MAAM,GAAG;IACrB,YAAY,OAAO,GAAG;IACtB,YAAY,QAAQ;IACpB,YAAY,OAAO,GAAG;IAEtB,OAAO,IAAI,CAAC,KAAK,OAAO,CAAC,CAAA;QACxB,WAAW,CAAC,IAAI,GAAG,GAAG,CAAC,IAAI;IAC5B;IAEA;;CAEA,GAEA,YAAY,KAAK,GAAG,EAAE;IACtB,YAAY,KAAK,GAAG,EAAE;IAEtB;;;;CAIA,GACA,YAAY,UAAU,GAAG,CAAC;IAE1B;;;;;CAKA,GACA,SAAS,YAAY,SAAS;QAC7B,IAAI,OAAO;QAEX,IAAK,IAAI,IAAI,GAAG,IAAI,UAAU,MAAM,EAAE,IAAK;YAC1C,OAAO,AAAC,CAAC,QAAQ,CAAC,IAAI,OAAQ,UAAU,UAAU,CAAC;YACnD,QAAQ,GAAG,2BAA2B;QACvC;QAEA,OAAO,YAAY,MAAM,CAAC,KAAK,GAAG,CAAC,QAAQ,YAAY,MAAM,CAAC,MAAM,CAAC;IACtE;IACA,YAAY,WAAW,GAAG;IAE1B;;;;;;CAMA,GACA,SAAS,YAAY,SAAS;QAC7B,IAAI;QACJ,IAAI,iBAAiB;QACrB,IAAI;QACJ,IAAI;QAEJ,SAAS,MAAM,GAAG,IAAI;YACrB,YAAY;YACZ,IAAI,CAAC,MAAM,OAAO,EAAE;gBACnB;YACD;YAEA,MAAM,OAAO;YAEb,uBAAuB;YACvB,MAAM,OAAO,OAAO,IAAI;YACxB,MAAM,KAAK,OAAO,CAAC,YAAY,IAAI;YACnC,KAAK,IAAI,GAAG;YACZ,KAAK,IAAI,GAAG;YACZ,KAAK,IAAI,GAAG;YACZ,WAAW;YAEX,IAAI,CAAC,EAAE,GAAG,YAAY,MAAM,CAAC,IAAI,CAAC,EAAE;YAEpC,IAAI,OAAO,IAAI,CAAC,EAAE,KAAK,UAAU;gBAChC,sCAAsC;gBACtC,KAAK,OAAO,CAAC;YACd;YAEA,yCAAyC;YACzC,IAAI,QAAQ;YACZ,IAAI,CAAC,EAAE,GAAG,IAAI,CAAC,EAAE,CAAC,OAAO,CAAC,iBAAiB,CAAC,OAAO;gBAClD,mEAAmE;gBACnE,IAAI,UAAU,MAAM;oBACnB,OAAO;gBACR;gBACA;gBACA,MAAM,YAAY,YAAY,UAAU,CAAC,OAAO;gBAChD,IAAI,OAAO,cAAc,YAAY;oBACpC,MAAM,MAAM,IAAI,CAAC,MAAM;oBACvB,QAAQ,UAAU,IAAI,CAAC,MAAM;oBAE7B,yEAAyE;oBACzE,KAAK,MAAM,CAAC,OAAO;oBACnB;gBACD;gBACA,OAAO;YACR;YAEA,+CAA+C;YAC/C,YAAY,UAAU,CAAC,IAAI,CAAC,MAAM;YAElC,MAAM,QAAQ,KAAK,GAAG,IAAI,YAAY,GAAG;YACzC,MAAM,KAAK,CAAC,MAAM;QACnB;QAEA,MAAM,SAAS,GAAG;QAClB,MAAM,SAAS,GAAG,YAAY,SAAS;QACvC,MAAM,KAAK,GAAG,YAAY,WAAW,CAAC;QACtC,MAAM,MAAM,GAAG;QACf,MAAM,OAAO,GAAG,YAAY,OAAO,EAAE,4DAA4D;QAEjG,OAAO,cAAc,CAAC,OAAO,WAAW;YACvC,YAAY;YACZ,cAAc;YACd,KAAK;gBACJ,IAAI,mBAAmB,MAAM;oBAC5B,OAAO;gBACR;gBACA,IAAI,oBAAoB,YAAY,UAAU,EAAE;oBAC/C,kBAAkB,YAAY,UAAU;oBACxC,eAAe,YAAY,OAAO,CAAC;gBACpC;gBAEA,OAAO;YACR;YACA,KAAK,CAAA;gBACJ,iBAAiB;YAClB;QACD;QAEA,wDAAwD;QACxD,IAAI,OAAO,YAAY,IAAI,KAAK,YAAY;YAC3C,YAAY,IAAI,CAAC;QAClB;QAEA,OAAO;IACR;IAEA,SAAS,OAAO,SAAS,EAAE,SAAS;QACnC,MAAM,WAAW,YAAY,IAAI,CAAC,SAAS,GAAG,CAAC,OAAO,cAAc,cAAc,MAAM,SAAS,IAAI;QACrG,SAAS,GAAG,GAAG,IAAI,CAAC,GAAG;QACvB,OAAO;IACR;IAEA;;;;;;CAMA,GACA,SAAS,OAAO,UAAU;QACzB,YAAY,IAAI,CAAC;QACjB,YAAY,UAAU,GAAG;QAEzB,YAAY,KAAK,GAAG,EAAE;QACtB,YAAY,KAAK,GAAG,EAAE;QAEtB,IAAI;QACJ,MAAM,QAAQ,CAAC,OAAO,eAAe,WAAW,aAAa,EAAE,EAAE,KAAK,CAAC;QACvE,MAAM,MAAM,MAAM,MAAM;QAExB,IAAK,IAAI,GAAG,IAAI,KAAK,IAAK;YACzB,IAAI,CAAC,KAAK,CAAC,EAAE,EAAE;gBAEd;YACD;YAEA,aAAa,KAAK,CAAC,EAAE,CAAC,OAAO,CAAC,OAAO;YAErC,IAAI,UAAU,CAAC,EAAE,KAAK,KAAK;gBAC1B,YAAY,KAAK,CAAC,IAAI,CAAC,IAAI,OAAO,MAAM,WAAW,KAAK,CAAC,KAAK;YAC/D,OAAO;gBACN,YAAY,KAAK,CAAC,IAAI,CAAC,IAAI,OAAO,MAAM,aAAa;YACtD;QACD;IACD;IAEA;;;;;CAKA,GACA,SAAS;QACR,MAAM,aAAa;eACf,YAAY,KAAK,CAAC,GAAG,CAAC;eACtB,YAAY,KAAK,CAAC,GAAG,CAAC,aAAa,GAAG,CAAC,CAAA,YAAa,MAAM;SAC7D,CAAC,IAAI,CAAC;QACP,YAAY,MAAM,CAAC;QACnB,OAAO;IACR;IAEA;;;;;;CAMA,GACA,SAAS,QAAQ,IAAI;QACpB,IAAI,IAAI,CAAC,KAAK,MAAM,GAAG,EAAE,KAAK,KAAK;YAClC,OAAO;QACR;QAEA,IAAI;QACJ,IAAI;QAEJ,IAAK,IAAI,GAAG,MAAM,YAAY,KAAK,CAAC,MAAM,EAAE,IAAI,KAAK,IAAK;YACzD,IAAI,YAAY,KAAK,CAAC,EAAE,CAAC,IAAI,CAAC,OAAO;gBACpC,OAAO;YACR;QACD;QAEA,IAAK,IAAI,GAAG,MAAM,YAAY,KAAK,CAAC,MAAM,EAAE,IAAI,KAAK,IAAK;YACzD,IAAI,YAAY,KAAK,CAAC,EAAE,CAAC,IAAI,CAAC,OAAO;gBACpC,OAAO;YACR;QACD;QAEA,OAAO;IACR;IAEA;;;;;;CAMA,GACA,SAAS,YAAY,MAAM;QAC1B,OAAO,OAAO,QAAQ,GACpB,SAAS,CAAC,GAAG,OAAO,QAAQ,GAAG,MAAM,GAAG,GACxC,OAAO,CAAC,WAAW;IACtB;IAEA;;;;;;CAMA,GACA,SAAS,OAAO,GAAG;QAClB,IAAI,eAAe,OAAO;YACzB,OAAO,IAAI,KAAK,IAAI,IAAI,OAAO;QAChC;QACA,OAAO;IACR;IAEA;;;CAGA,GACA,SAAS;QACR,QAAQ,IAAI,CAAC;IACd;IAEA,YAAY,MAAM,CAAC,YAAY,IAAI;IAEnC,OAAO;AACR;AAEA,OAAO,OAAO,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3305, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/bloomgoo/gold-exchange-platform/frontend/node_modules/.pnpm/debug%404.3.7/node_modules/debug/src/node.js"], "sourcesContent": ["/**\n * Module dependencies.\n */\n\nconst tty = require('tty');\nconst util = require('util');\n\n/**\n * This is the Node.js implementation of `debug()`.\n */\n\nexports.init = init;\nexports.log = log;\nexports.formatArgs = formatArgs;\nexports.save = save;\nexports.load = load;\nexports.useColors = useColors;\nexports.destroy = util.deprecate(\n\t() => {},\n\t'Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`.'\n);\n\n/**\n * Colors.\n */\n\nexports.colors = [6, 2, 3, 4, 5, 1];\n\ntry {\n\t// Optional dependency (as in, doesn't need to be installed, NOT like optionalDependencies in package.json)\n\t// eslint-disable-next-line import/no-extraneous-dependencies\n\tconst supportsColor = require('supports-color');\n\n\tif (supportsColor && (supportsColor.stderr || supportsColor).level >= 2) {\n\t\texports.colors = [\n\t\t\t20,\n\t\t\t21,\n\t\t\t26,\n\t\t\t27,\n\t\t\t32,\n\t\t\t33,\n\t\t\t38,\n\t\t\t39,\n\t\t\t40,\n\t\t\t41,\n\t\t\t42,\n\t\t\t43,\n\t\t\t44,\n\t\t\t45,\n\t\t\t56,\n\t\t\t57,\n\t\t\t62,\n\t\t\t63,\n\t\t\t68,\n\t\t\t69,\n\t\t\t74,\n\t\t\t75,\n\t\t\t76,\n\t\t\t77,\n\t\t\t78,\n\t\t\t79,\n\t\t\t80,\n\t\t\t81,\n\t\t\t92,\n\t\t\t93,\n\t\t\t98,\n\t\t\t99,\n\t\t\t112,\n\t\t\t113,\n\t\t\t128,\n\t\t\t129,\n\t\t\t134,\n\t\t\t135,\n\t\t\t148,\n\t\t\t149,\n\t\t\t160,\n\t\t\t161,\n\t\t\t162,\n\t\t\t163,\n\t\t\t164,\n\t\t\t165,\n\t\t\t166,\n\t\t\t167,\n\t\t\t168,\n\t\t\t169,\n\t\t\t170,\n\t\t\t171,\n\t\t\t172,\n\t\t\t173,\n\t\t\t178,\n\t\t\t179,\n\t\t\t184,\n\t\t\t185,\n\t\t\t196,\n\t\t\t197,\n\t\t\t198,\n\t\t\t199,\n\t\t\t200,\n\t\t\t201,\n\t\t\t202,\n\t\t\t203,\n\t\t\t204,\n\t\t\t205,\n\t\t\t206,\n\t\t\t207,\n\t\t\t208,\n\t\t\t209,\n\t\t\t214,\n\t\t\t215,\n\t\t\t220,\n\t\t\t221\n\t\t];\n\t}\n} catch (error) {\n\t// Swallow - we only care if `supports-color` is available; it doesn't have to be.\n}\n\n/**\n * Build up the default `inspectOpts` object from the environment variables.\n *\n *   $ DEBUG_COLORS=no DEBUG_DEPTH=10 DEBUG_SHOW_HIDDEN=enabled node script.js\n */\n\nexports.inspectOpts = Object.keys(process.env).filter(key => {\n\treturn /^debug_/i.test(key);\n}).reduce((obj, key) => {\n\t// Camel-case\n\tconst prop = key\n\t\t.substring(6)\n\t\t.toLowerCase()\n\t\t.replace(/_([a-z])/g, (_, k) => {\n\t\t\treturn k.toUpperCase();\n\t\t});\n\n\t// Coerce string value into JS value\n\tlet val = process.env[key];\n\tif (/^(yes|on|true|enabled)$/i.test(val)) {\n\t\tval = true;\n\t} else if (/^(no|off|false|disabled)$/i.test(val)) {\n\t\tval = false;\n\t} else if (val === 'null') {\n\t\tval = null;\n\t} else {\n\t\tval = Number(val);\n\t}\n\n\tobj[prop] = val;\n\treturn obj;\n}, {});\n\n/**\n * Is stdout a TTY? Colored output is enabled when `true`.\n */\n\nfunction useColors() {\n\treturn 'colors' in exports.inspectOpts ?\n\t\tBoolean(exports.inspectOpts.colors) :\n\t\ttty.isatty(process.stderr.fd);\n}\n\n/**\n * Adds ANSI color escape codes if enabled.\n *\n * @api public\n */\n\nfunction formatArgs(args) {\n\tconst {namespace: name, useColors} = this;\n\n\tif (useColors) {\n\t\tconst c = this.color;\n\t\tconst colorCode = '\\u001B[3' + (c < 8 ? c : '8;5;' + c);\n\t\tconst prefix = `  ${colorCode};1m${name} \\u001B[0m`;\n\n\t\targs[0] = prefix + args[0].split('\\n').join('\\n' + prefix);\n\t\targs.push(colorCode + 'm+' + module.exports.humanize(this.diff) + '\\u001B[0m');\n\t} else {\n\t\targs[0] = getDate() + name + ' ' + args[0];\n\t}\n}\n\nfunction getDate() {\n\tif (exports.inspectOpts.hideDate) {\n\t\treturn '';\n\t}\n\treturn new Date().toISOString() + ' ';\n}\n\n/**\n * Invokes `util.formatWithOptions()` with the specified arguments and writes to stderr.\n */\n\nfunction log(...args) {\n\treturn process.stderr.write(util.formatWithOptions(exports.inspectOpts, ...args) + '\\n');\n}\n\n/**\n * Save `namespaces`.\n *\n * @param {String} namespaces\n * @api private\n */\nfunction save(namespaces) {\n\tif (namespaces) {\n\t\tprocess.env.DEBUG = namespaces;\n\t} else {\n\t\t// If you set a process.env field to null or undefined, it gets cast to the\n\t\t// string 'null' or 'undefined'. Just delete instead.\n\t\tdelete process.env.DEBUG;\n\t}\n}\n\n/**\n * Load `namespaces`.\n *\n * @return {String} returns the previously persisted debug modes\n * @api private\n */\n\nfunction load() {\n\treturn process.env.DEBUG;\n}\n\n/**\n * Init logic for `debug` instances.\n *\n * Create a new `inspectOpts` object in case `useColors` is set\n * differently for a particular `debug` instance.\n */\n\nfunction init(debug) {\n\tdebug.inspectOpts = {};\n\n\tconst keys = Object.keys(exports.inspectOpts);\n\tfor (let i = 0; i < keys.length; i++) {\n\t\tdebug.inspectOpts[keys[i]] = exports.inspectOpts[keys[i]];\n\t}\n}\n\nmodule.exports = require('./common')(exports);\n\nconst {formatters} = module.exports;\n\n/**\n * Map %o to `util.inspect()`, all on a single line.\n */\n\nformatters.o = function (v) {\n\tthis.inspectOpts.colors = this.useColors;\n\treturn util.inspect(v, this.inspectOpts)\n\t\t.split('\\n')\n\t\t.map(str => str.trim())\n\t\t.join(' ');\n};\n\n/**\n * Map %O to `util.inspect()`, allowing multiple lines if needed.\n */\n\nformatters.O = function (v) {\n\tthis.inspectOpts.colors = this.useColors;\n\treturn util.inspect(v, this.inspectOpts);\n};\n"], "names": [], "mappings": "AAAA;;CAEC,GAED,MAAM;AACN,MAAM;AAEN;;CAEC,GAED,QAAQ,IAAI,GAAG;AACf,QAAQ,GAAG,GAAG;AACd,QAAQ,UAAU,GAAG;AACrB,QAAQ,IAAI,GAAG;AACf,QAAQ,IAAI,GAAG;AACf,QAAQ,SAAS,GAAG;AACpB,QAAQ,OAAO,GAAG,KAAK,SAAS,CAC/B,KAAO,GACP;AAGD;;CAEC,GAED,QAAQ,MAAM,GAAG;IAAC;IAAG;IAAG;IAAG;IAAG;IAAG;CAAE;AAEnC,IAAI;IACH,2GAA2G;IAC3G,6DAA6D;IAC7D,MAAM;IAEN,IAAI,iBAAiB,CAAC,cAAc,MAAM,IAAI,aAAa,EAAE,KAAK,IAAI,GAAG;QACxE,QAAQ,MAAM,GAAG;YAChB;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;SACA;IACF;AACD,EAAE,OAAO,OAAO;AACf,kFAAkF;AACnF;AAEA;;;;CAIC,GAED,QAAQ,WAAW,GAAG,OAAO,IAAI,CAAC,QAAQ,GAAG,EAAE,MAAM,CAAC,CAAA;IACrD,OAAO,WAAW,IAAI,CAAC;AACxB,GAAG,MAAM,CAAC,CAAC,KAAK;IACf,aAAa;IACb,MAAM,OAAO,IACX,SAAS,CAAC,GACV,WAAW,GACX,OAAO,CAAC,aAAa,CAAC,GAAG;QACzB,OAAO,EAAE,WAAW;IACrB;IAED,oCAAoC;IACpC,IAAI,MAAM,QAAQ,GAAG,CAAC,IAAI;IAC1B,IAAI,2BAA2B,IAAI,CAAC,MAAM;QACzC,MAAM;IACP,OAAO,IAAI,6BAA6B,IAAI,CAAC,MAAM;QAClD,MAAM;IACP,OAAO,IAAI,QAAQ,QAAQ;QAC1B,MAAM;IACP,OAAO;QACN,MAAM,OAAO;IACd;IAEA,GAAG,CAAC,KAAK,GAAG;IACZ,OAAO;AACR,GAAG,CAAC;AAEJ;;CAEC,GAED,SAAS;IACR,OAAO,YAAY,QAAQ,WAAW,GACrC,QAAQ,QAAQ,WAAW,CAAC,MAAM,IAClC,IAAI,MAAM,CAAC,QAAQ,MAAM,CAAC,EAAE;AAC9B;AAEA;;;;CAIC,GAED,SAAS,WAAW,IAAI;IACvB,MAAM,EAAC,WAAW,IAAI,EAAE,SAAS,EAAC,GAAG,IAAI;IAEzC,IAAI,WAAW;QACd,MAAM,IAAI,IAAI,CAAC,KAAK;QACpB,MAAM,YAAY,aAAa,CAAC,IAAI,IAAI,IAAI,SAAS,CAAC;QACtD,MAAM,SAAS,CAAC,EAAE,EAAE,UAAU,GAAG,EAAE,KAAK,UAAU,CAAC;QAEnD,IAAI,CAAC,EAAE,GAAG,SAAS,IAAI,CAAC,EAAE,CAAC,KAAK,CAAC,MAAM,IAAI,CAAC,OAAO;QACnD,KAAK,IAAI,CAAC,YAAY,OAAO,OAAO,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,IAAI;IACnE,OAAO;QACN,IAAI,CAAC,EAAE,GAAG,YAAY,OAAO,MAAM,IAAI,CAAC,EAAE;IAC3C;AACD;AAEA,SAAS;IACR,IAAI,QAAQ,WAAW,CAAC,QAAQ,EAAE;QACjC,OAAO;IACR;IACA,OAAO,IAAI,OAAO,WAAW,KAAK;AACnC;AAEA;;CAEC,GAED,SAAS,IAAI,GAAG,IAAI;IACnB,OAAO,QAAQ,MAAM,CAAC,KAAK,CAAC,KAAK,iBAAiB,CAAC,QAAQ,WAAW,KAAK,QAAQ;AACpF;AAEA;;;;;CAKC,GACD,SAAS,KAAK,UAAU;IACvB,IAAI,YAAY;QACf,QAAQ,GAAG,CAAC,KAAK,GAAG;IACrB,OAAO;QACN,2EAA2E;QAC3E,qDAAqD;QACrD,OAAO,QAAQ,GAAG,CAAC,KAAK;IACzB;AACD;AAEA;;;;;CAKC,GAED,SAAS;IACR,OAAO,QAAQ,GAAG,CAAC,KAAK;AACzB;AAEA;;;;;CAKC,GAED,SAAS,KAAK,KAAK;IAClB,MAAM,WAAW,GAAG,CAAC;IAErB,MAAM,OAAO,OAAO,IAAI,CAAC,QAAQ,WAAW;IAC5C,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,MAAM,EAAE,IAAK;QACrC,MAAM,WAAW,CAAC,IAAI,CAAC,EAAE,CAAC,GAAG,QAAQ,WAAW,CAAC,IAAI,CAAC,EAAE,CAAC;IAC1D;AACD;AAEA,OAAO,OAAO,GAAG,4HAAoB;AAErC,MAAM,EAAC,UAAU,EAAC,GAAG,OAAO,OAAO;AAEnC;;CAEC,GAED,WAAW,CAAC,GAAG,SAAU,CAAC;IACzB,IAAI,CAAC,WAAW,CAAC,MAAM,GAAG,IAAI,CAAC,SAAS;IACxC,OAAO,KAAK,OAAO,CAAC,GAAG,IAAI,CAAC,WAAW,EACrC,KAAK,CAAC,MACN,GAAG,CAAC,CAAA,MAAO,IAAI,IAAI,IACnB,IAAI,CAAC;AACR;AAEA;;CAEC,GAED,WAAW,CAAC,GAAG,SAAU,CAAC;IACzB,IAAI,CAAC,WAAW,CAAC,MAAM,GAAG,IAAI,CAAC,SAAS;IACxC,OAAO,KAAK,OAAO,CAAC,GAAG,IAAI,CAAC,WAAW;AACxC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3525, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/bloomgoo/gold-exchange-platform/frontend/node_modules/.pnpm/debug%404.3.7/node_modules/debug/src/browser.js"], "sourcesContent": ["/* eslint-env browser */\n\n/**\n * This is the web browser implementation of `debug()`.\n */\n\nexports.formatArgs = formatArgs;\nexports.save = save;\nexports.load = load;\nexports.useColors = useColors;\nexports.storage = localstorage();\nexports.destroy = (() => {\n\tlet warned = false;\n\n\treturn () => {\n\t\tif (!warned) {\n\t\t\twarned = true;\n\t\t\tconsole.warn('Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`.');\n\t\t}\n\t};\n})();\n\n/**\n * Colors.\n */\n\nexports.colors = [\n\t'#0000CC',\n\t'#0000FF',\n\t'#0033CC',\n\t'#0033FF',\n\t'#0066CC',\n\t'#0066FF',\n\t'#0099CC',\n\t'#0099FF',\n\t'#00CC00',\n\t'#00CC33',\n\t'#00CC66',\n\t'#00CC99',\n\t'#00CCCC',\n\t'#00CCFF',\n\t'#3300CC',\n\t'#3300FF',\n\t'#3333CC',\n\t'#3333FF',\n\t'#3366CC',\n\t'#3366FF',\n\t'#3399CC',\n\t'#3399FF',\n\t'#33CC00',\n\t'#33CC33',\n\t'#33CC66',\n\t'#33CC99',\n\t'#33CCCC',\n\t'#33CCFF',\n\t'#6600CC',\n\t'#6600FF',\n\t'#6633CC',\n\t'#6633FF',\n\t'#66CC00',\n\t'#66CC33',\n\t'#9900CC',\n\t'#9900FF',\n\t'#9933CC',\n\t'#9933FF',\n\t'#99CC00',\n\t'#99CC33',\n\t'#CC0000',\n\t'#CC0033',\n\t'#CC0066',\n\t'#CC0099',\n\t'#CC00CC',\n\t'#CC00FF',\n\t'#CC3300',\n\t'#CC3333',\n\t'#CC3366',\n\t'#CC3399',\n\t'#CC33CC',\n\t'#CC33FF',\n\t'#CC6600',\n\t'#CC6633',\n\t'#CC9900',\n\t'#CC9933',\n\t'#CCCC00',\n\t'#CCCC33',\n\t'#FF0000',\n\t'#FF0033',\n\t'#FF0066',\n\t'#FF0099',\n\t'#FF00CC',\n\t'#FF00FF',\n\t'#FF3300',\n\t'#FF3333',\n\t'#FF3366',\n\t'#FF3399',\n\t'#FF33CC',\n\t'#FF33FF',\n\t'#FF6600',\n\t'#FF6633',\n\t'#FF9900',\n\t'#FF9933',\n\t'#FFCC00',\n\t'#FFCC33'\n];\n\n/**\n * Currently only WebKit-based Web Inspectors, Firefox >= v31,\n * and the Firebug extension (any Firefox version) are known\n * to support \"%c\" CSS customizations.\n *\n * TODO: add a `localStorage` variable to explicitly enable/disable colors\n */\n\n// eslint-disable-next-line complexity\nfunction useColors() {\n\t// NB: In an Electron preload script, document will be defined but not fully\n\t// initialized. Since we know we're in Chrome, we'll just detect this case\n\t// explicitly\n\tif (typeof window !== 'undefined' && window.process && (window.process.type === 'renderer' || window.process.__nwjs)) {\n\t\treturn true;\n\t}\n\n\t// Internet Explorer and Edge do not support colors.\n\tif (typeof navigator !== 'undefined' && navigator.userAgent && navigator.userAgent.toLowerCase().match(/(edge|trident)\\/(\\d+)/)) {\n\t\treturn false;\n\t}\n\n\tlet m;\n\n\t// Is webkit? http://stackoverflow.com/a/16459606/376773\n\t// document is undefined in react-native: https://github.com/facebook/react-native/pull/1632\n\treturn (typeof document !== 'undefined' && document.documentElement && document.documentElement.style && document.documentElement.style.WebkitAppearance) ||\n\t\t// Is firebug? http://stackoverflow.com/a/398120/376773\n\t\t(typeof window !== 'undefined' && window.console && (window.console.firebug || (window.console.exception && window.console.table))) ||\n\t\t// Is firefox >= v31?\n\t\t// https://developer.mozilla.org/en-US/docs/Tools/Web_Console#Styling_messages\n\t\t(typeof navigator !== 'undefined' && navigator.userAgent && (m = navigator.userAgent.toLowerCase().match(/firefox\\/(\\d+)/)) && parseInt(m[1], 10) >= 31) ||\n\t\t// Double check webkit in userAgent just in case we are in a worker\n\t\t(typeof navigator !== 'undefined' && navigator.userAgent && navigator.userAgent.toLowerCase().match(/applewebkit\\/(\\d+)/));\n}\n\n/**\n * Colorize log arguments if enabled.\n *\n * @api public\n */\n\nfunction formatArgs(args) {\n\targs[0] = (this.useColors ? '%c' : '') +\n\t\tthis.namespace +\n\t\t(this.useColors ? ' %c' : ' ') +\n\t\targs[0] +\n\t\t(this.useColors ? '%c ' : ' ') +\n\t\t'+' + module.exports.humanize(this.diff);\n\n\tif (!this.useColors) {\n\t\treturn;\n\t}\n\n\tconst c = 'color: ' + this.color;\n\targs.splice(1, 0, c, 'color: inherit');\n\n\t// The final \"%c\" is somewhat tricky, because there could be other\n\t// arguments passed either before or after the %c, so we need to\n\t// figure out the correct index to insert the CSS into\n\tlet index = 0;\n\tlet lastC = 0;\n\targs[0].replace(/%[a-zA-Z%]/g, match => {\n\t\tif (match === '%%') {\n\t\t\treturn;\n\t\t}\n\t\tindex++;\n\t\tif (match === '%c') {\n\t\t\t// We only are interested in the *last* %c\n\t\t\t// (the user may have provided their own)\n\t\t\tlastC = index;\n\t\t}\n\t});\n\n\targs.splice(lastC, 0, c);\n}\n\n/**\n * Invokes `console.debug()` when available.\n * No-op when `console.debug` is not a \"function\".\n * If `console.debug` is not available, falls back\n * to `console.log`.\n *\n * @api public\n */\nexports.log = console.debug || console.log || (() => {});\n\n/**\n * Save `namespaces`.\n *\n * @param {String} namespaces\n * @api private\n */\nfunction save(namespaces) {\n\ttry {\n\t\tif (namespaces) {\n\t\t\texports.storage.setItem('debug', namespaces);\n\t\t} else {\n\t\t\texports.storage.removeItem('debug');\n\t\t}\n\t} catch (error) {\n\t\t// Swallow\n\t\t// XXX (@Qix-) should we be logging these?\n\t}\n}\n\n/**\n * Load `namespaces`.\n *\n * @return {String} returns the previously persisted debug modes\n * @api private\n */\nfunction load() {\n\tlet r;\n\ttry {\n\t\tr = exports.storage.getItem('debug');\n\t} catch (error) {\n\t\t// Swallow\n\t\t// XXX (@Qix-) should we be logging these?\n\t}\n\n\t// If debug isn't set in LS, and we're in Electron, try to load $DEBUG\n\tif (!r && typeof process !== 'undefined' && 'env' in process) {\n\t\tr = process.env.DEBUG;\n\t}\n\n\treturn r;\n}\n\n/**\n * Localstorage attempts to return the localstorage.\n *\n * This is necessary because safari throws\n * when a user disables cookies/localstorage\n * and you attempt to access it.\n *\n * @return {LocalStorage}\n * @api private\n */\n\nfunction localstorage() {\n\ttry {\n\t\t// TVMLKit (Apple TV JS Runtime) does not have a window object, just localStorage in the global context\n\t\t// The Browser also has localStorage in the global context.\n\t\treturn localStorage;\n\t} catch (error) {\n\t\t// Swallow\n\t\t// XXX (@Qix-) should we be logging these?\n\t}\n}\n\nmodule.exports = require('./common')(exports);\n\nconst {formatters} = module.exports;\n\n/**\n * Map %j to `JSON.stringify()`, since no Web Inspectors do that by default.\n */\n\nformatters.j = function (v) {\n\ttry {\n\t\treturn JSON.stringify(v);\n\t} catch (error) {\n\t\treturn '[UnexpectedJSONParseError]: ' + error.message;\n\t}\n};\n"], "names": [], "mappings": "AAAA,sBAAsB,GAEtB;;CAEC,GAED,QAAQ,UAAU,GAAG;AACrB,QAAQ,IAAI,GAAG;AACf,QAAQ,IAAI,GAAG;AACf,QAAQ,SAAS,GAAG;AACpB,QAAQ,OAAO,GAAG;AAClB,QAAQ,OAAO,GAAG,CAAC;IAClB,IAAI,SAAS;IAEb,OAAO;QACN,IAAI,CAAC,QAAQ;YACZ,SAAS;YACT,QAAQ,IAAI,CAAC;QACd;IACD;AACD,CAAC;AAED;;CAEC,GAED,QAAQ,MAAM,GAAG;IAChB;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACA;AAED;;;;;;CAMC,GAED,sCAAsC;AACtC,SAAS;IACR,4EAA4E;IAC5E,0EAA0E;IAC1E,aAAa;IACb,IAAI,OAAO,WAAW,eAAe,OAAO,OAAO,IAAI,CAAC,OAAO,OAAO,CAAC,IAAI,KAAK,cAAc,OAAO,OAAO,CAAC,MAAM,GAAG;QACrH,OAAO;IACR;IAEA,oDAAoD;IACpD,IAAI,OAAO,cAAc,eAAe,UAAU,SAAS,IAAI,UAAU,SAAS,CAAC,WAAW,GAAG,KAAK,CAAC,0BAA0B;QAChI,OAAO;IACR;IAEA,IAAI;IAEJ,wDAAwD;IACxD,4FAA4F;IAC5F,OAAO,AAAC,OAAO,aAAa,eAAe,SAAS,eAAe,IAAI,SAAS,eAAe,CAAC,KAAK,IAAI,SAAS,eAAe,CAAC,KAAK,CAAC,gBAAgB,IAEtJ,OAAO,WAAW,eAAe,OAAO,OAAO,IAAI,CAAC,OAAO,OAAO,CAAC,OAAO,IAAK,OAAO,OAAO,CAAC,SAAS,IAAI,OAAO,OAAO,CAAC,KAAK,AAAC,KAGhI,OAAO,cAAc,eAAe,UAAU,SAAS,IAAI,CAAC,IAAI,UAAU,SAAS,CAAC,WAAW,GAAG,KAAK,CAAC,iBAAiB,KAAK,SAAS,CAAC,CAAC,EAAE,EAAE,OAAO,MAEpJ,OAAO,cAAc,eAAe,UAAU,SAAS,IAAI,UAAU,SAAS,CAAC,WAAW,GAAG,KAAK,CAAC;AACtG;AAEA;;;;CAIC,GAED,SAAS,WAAW,IAAI;IACvB,IAAI,CAAC,EAAE,GAAG,CAAC,IAAI,CAAC,SAAS,GAAG,OAAO,EAAE,IACpC,IAAI,CAAC,SAAS,GACd,CAAC,IAAI,CAAC,SAAS,GAAG,QAAQ,GAAG,IAC7B,IAAI,CAAC,EAAE,GACP,CAAC,IAAI,CAAC,SAAS,GAAG,QAAQ,GAAG,IAC7B,MAAM,OAAO,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI;IAExC,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;QACpB;IACD;IAEA,MAAM,IAAI,YAAY,IAAI,CAAC,KAAK;IAChC,KAAK,MAAM,CAAC,GAAG,GAAG,GAAG;IAErB,kEAAkE;IAClE,gEAAgE;IAChE,sDAAsD;IACtD,IAAI,QAAQ;IACZ,IAAI,QAAQ;IACZ,IAAI,CAAC,EAAE,CAAC,OAAO,CAAC,eAAe,CAAA;QAC9B,IAAI,UAAU,MAAM;YACnB;QACD;QACA;QACA,IAAI,UAAU,MAAM;YACnB,0CAA0C;YAC1C,yCAAyC;YACzC,QAAQ;QACT;IACD;IAEA,KAAK,MAAM,CAAC,OAAO,GAAG;AACvB;AAEA;;;;;;;CAOC,GACD,QAAQ,GAAG,GAAG,QAAQ,KAAK,IAAI,QAAQ,GAAG,IAAI,CAAC,KAAO,CAAC;AAEvD;;;;;CAKC,GACD,SAAS,KAAK,UAAU;IACvB,IAAI;QACH,IAAI,YAAY;YACf,QAAQ,OAAO,CAAC,OAAO,CAAC,SAAS;QAClC,OAAO;YACN,QAAQ,OAAO,CAAC,UAAU,CAAC;QAC5B;IACD,EAAE,OAAO,OAAO;IACf,UAAU;IACV,0CAA0C;IAC3C;AACD;AAEA;;;;;CAKC,GACD,SAAS;IACR,IAAI;IACJ,IAAI;QACH,IAAI,QAAQ,OAAO,CAAC,OAAO,CAAC;IAC7B,EAAE,OAAO,OAAO;IACf,UAAU;IACV,0CAA0C;IAC3C;IAEA,sEAAsE;IACtE,IAAI,CAAC,KAAK,OAAO,YAAY,eAAe,SAAS,SAAS;QAC7D,IAAI,QAAQ,GAAG,CAAC,KAAK;IACtB;IAEA,OAAO;AACR;AAEA;;;;;;;;;CASC,GAED,SAAS;IACR,IAAI;QACH,uGAAuG;QACvG,2DAA2D;QAC3D,OAAO;IACR,EAAE,OAAO,OAAO;IACf,UAAU;IACV,0CAA0C;IAC3C;AACD;AAEA,OAAO,OAAO,GAAG,4HAAoB;AAErC,MAAM,EAAC,UAAU,EAAC,GAAG,OAAO,OAAO;AAEnC;;CAEC,GAED,WAAW,CAAC,GAAG,SAAU,CAAC;IACzB,IAAI;QACH,OAAO,KAAK,SAAS,CAAC;IACvB,EAAE,OAAO,OAAO;QACf,OAAO,iCAAiC,MAAM,OAAO;IACtD;AACD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3752, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/bloomgoo/gold-exchange-platform/frontend/node_modules/.pnpm/debug%404.3.7/node_modules/debug/src/index.js"], "sourcesContent": ["/**\n * Detect Electron renderer / nwjs process, which is node, but we should\n * treat as a browser.\n */\n\nif (typeof process === 'undefined' || process.type === 'renderer' || process.browser === true || process.__nwjs) {\n\tmodule.exports = require('./browser.js');\n} else {\n\tmodule.exports = require('./node.js');\n}\n"], "names": [], "mappings": "AAAA;;;CAGC,GAED,IAAI,OAAO,YAAY,eAAe,QAAQ,IAAI,KAAK,cAAc,4CAAoB,QAAQ,QAAQ,MAAM,EAAE;IAChH,OAAO,OAAO;AACf,OAAO;IACN,OAAO,OAAO;AACf", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3765, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/bloomgoo/gold-exchange-platform/frontend/node_modules/.pnpm/has-flag%404.0.0/node_modules/has-flag/index.js"], "sourcesContent": ["'use strict';\n\nmodule.exports = (flag, argv = process.argv) => {\n\tconst prefix = flag.startsWith('-') ? '' : (flag.length === 1 ? '-' : '--');\n\tconst position = argv.indexOf(prefix + flag);\n\tconst terminatorPosition = argv.indexOf('--');\n\treturn position !== -1 && (terminatorPosition === -1 || position < terminatorPosition);\n};\n"], "names": [], "mappings": "AAAA;AAEA,OAAO,OAAO,GAAG,CAAC,MAAM,OAAO,QAAQ,IAAI;IAC1C,MAAM,SAAS,KAAK,UAAU,CAAC,OAAO,KAAM,KAAK,MAAM,KAAK,IAAI,MAAM;IACtE,MAAM,WAAW,KAAK,OAAO,CAAC,SAAS;IACvC,MAAM,qBAAqB,KAAK,OAAO,CAAC;IACxC,OAAO,aAAa,CAAC,KAAK,CAAC,uBAAuB,CAAC,KAAK,WAAW,kBAAkB;AACtF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3777, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/bloomgoo/gold-exchange-platform/frontend/node_modules/.pnpm/supports-color%407.2.0/node_modules/supports-color/index.js"], "sourcesContent": ["'use strict';\nconst os = require('os');\nconst tty = require('tty');\nconst hasFlag = require('has-flag');\n\nconst {env} = process;\n\nlet forceColor;\nif (hasFlag('no-color') ||\n\thasFlag('no-colors') ||\n\thasFlag('color=false') ||\n\thasFlag('color=never')) {\n\tforceColor = 0;\n} else if (hasFlag('color') ||\n\thasFlag('colors') ||\n\thasFlag('color=true') ||\n\thasFlag('color=always')) {\n\tforceColor = 1;\n}\n\nif ('FORCE_COLOR' in env) {\n\tif (env.FORCE_COLOR === 'true') {\n\t\tforceColor = 1;\n\t} else if (env.FORCE_COLOR === 'false') {\n\t\tforceColor = 0;\n\t} else {\n\t\tforceColor = env.FORCE_COLOR.length === 0 ? 1 : Math.min(parseInt(env.FORCE_COLOR, 10), 3);\n\t}\n}\n\nfunction translateLevel(level) {\n\tif (level === 0) {\n\t\treturn false;\n\t}\n\n\treturn {\n\t\tlevel,\n\t\thasBasic: true,\n\t\thas256: level >= 2,\n\t\thas16m: level >= 3\n\t};\n}\n\nfunction supportsColor(haveStream, streamIsTTY) {\n\tif (forceColor === 0) {\n\t\treturn 0;\n\t}\n\n\tif (hasFlag('color=16m') ||\n\t\thasFlag('color=full') ||\n\t\thasFlag('color=truecolor')) {\n\t\treturn 3;\n\t}\n\n\tif (hasFlag('color=256')) {\n\t\treturn 2;\n\t}\n\n\tif (haveStream && !streamIsTTY && forceColor === undefined) {\n\t\treturn 0;\n\t}\n\n\tconst min = forceColor || 0;\n\n\tif (env.TERM === 'dumb') {\n\t\treturn min;\n\t}\n\n\tif (process.platform === 'win32') {\n\t\t// Windows 10 build 10586 is the first Windows release that supports 256 colors.\n\t\t// Windows 10 build 14931 is the first release that supports 16m/TrueColor.\n\t\tconst osRelease = os.release().split('.');\n\t\tif (\n\t\t\tNumber(osRelease[0]) >= 10 &&\n\t\t\tNumber(osRelease[2]) >= 10586\n\t\t) {\n\t\t\treturn Number(osRelease[2]) >= 14931 ? 3 : 2;\n\t\t}\n\n\t\treturn 1;\n\t}\n\n\tif ('CI' in env) {\n\t\tif (['TRAVIS', 'CIRCLECI', 'APPVEYOR', 'GITLAB_CI', 'GITHUB_ACTIONS', 'BUILDKITE'].some(sign => sign in env) || env.CI_NAME === 'codeship') {\n\t\t\treturn 1;\n\t\t}\n\n\t\treturn min;\n\t}\n\n\tif ('TEAMCITY_VERSION' in env) {\n\t\treturn /^(9\\.(0*[1-9]\\d*)\\.|\\d{2,}\\.)/.test(env.TEAMCITY_VERSION) ? 1 : 0;\n\t}\n\n\tif (env.COLORTERM === 'truecolor') {\n\t\treturn 3;\n\t}\n\n\tif ('TERM_PROGRAM' in env) {\n\t\tconst version = parseInt((env.TERM_PROGRAM_VERSION || '').split('.')[0], 10);\n\n\t\tswitch (env.TERM_PROGRAM) {\n\t\t\tcase 'iTerm.app':\n\t\t\t\treturn version >= 3 ? 3 : 2;\n\t\t\tcase 'Apple_Terminal':\n\t\t\t\treturn 2;\n\t\t\t// No default\n\t\t}\n\t}\n\n\tif (/-256(color)?$/i.test(env.TERM)) {\n\t\treturn 2;\n\t}\n\n\tif (/^screen|^xterm|^vt100|^vt220|^rxvt|color|ansi|cygwin|linux/i.test(env.TERM)) {\n\t\treturn 1;\n\t}\n\n\tif ('COLORTERM' in env) {\n\t\treturn 1;\n\t}\n\n\treturn min;\n}\n\nfunction getSupportLevel(stream) {\n\tconst level = supportsColor(stream, stream && stream.isTTY);\n\treturn translateLevel(level);\n}\n\nmodule.exports = {\n\tsupportsColor: getSupportLevel,\n\tstdout: translateLevel(supportsColor(true, tty.isatty(1))),\n\tstderr: translateLevel(supportsColor(true, tty.isatty(2)))\n};\n"], "names": [], "mappings": "AAAA;AACA,MAAM;AACN,MAAM;AACN,MAAM;AAEN,MAAM,EAAC,GAAG,EAAC,GAAG;AAEd,IAAI;AACJ,IAAI,QAAQ,eACX,QAAQ,gBACR,QAAQ,kBACR,QAAQ,gBAAgB;IACxB,aAAa;AACd,OAAO,IAAI,QAAQ,YAClB,QAAQ,aACR,QAAQ,iBACR,QAAQ,iBAAiB;IACzB,aAAa;AACd;AAEA,IAAI,iBAAiB,KAAK;IACzB,IAAI,IAAI,WAAW,KAAK,QAAQ;QAC/B,aAAa;IACd,OAAO,IAAI,IAAI,WAAW,KAAK,SAAS;QACvC,aAAa;IACd,OAAO;QACN,aAAa,IAAI,WAAW,CAAC,MAAM,KAAK,IAAI,IAAI,KAAK,GAAG,CAAC,SAAS,IAAI,WAAW,EAAE,KAAK;IACzF;AACD;AAEA,SAAS,eAAe,KAAK;IAC5B,IAAI,UAAU,GAAG;QAChB,OAAO;IACR;IAEA,OAAO;QACN;QACA,UAAU;QACV,QAAQ,SAAS;QACjB,QAAQ,SAAS;IAClB;AACD;AAEA,SAAS,cAAc,UAAU,EAAE,WAAW;IAC7C,IAAI,eAAe,GAAG;QACrB,OAAO;IACR;IAEA,IAAI,QAAQ,gBACX,QAAQ,iBACR,QAAQ,oBAAoB;QAC5B,OAAO;IACR;IAEA,IAAI,QAAQ,cAAc;QACzB,OAAO;IACR;IAEA,IAAI,cAAc,CAAC,eAAe,eAAe,WAAW;QAC3D,OAAO;IACR;IAEA,MAAM,MAAM,cAAc;IAE1B,IAAI,IAAI,IAAI,KAAK,QAAQ;QACxB,OAAO;IACR;IAEA,wCAAkC;QACjC,gFAAgF;QAChF,2EAA2E;QAC3E,MAAM,YAAY,GAAG,OAAO,GAAG,KAAK,CAAC;QACrC,IACC,OAAO,SAAS,CAAC,EAAE,KAAK,MACxB,OAAO,SAAS,CAAC,EAAE,KAAK,OACvB;YACD,OAAO,OAAO,SAAS,CAAC,EAAE,KAAK,QAAQ,IAAI;QAC5C;QAEA,OAAO;IACR;;AA2CD;AAEA,SAAS,gBAAgB,MAAM;IAC9B,MAAM,QAAQ,cAAc,QAAQ,UAAU,OAAO,KAAK;IAC1D,OAAO,eAAe;AACvB;AAEA,OAAO,OAAO,GAAG;IAChB,eAAe;IACf,QAAQ,eAAe,cAAc,MAAM,IAAI,MAAM,CAAC;IACtD,QAAQ,eAAe,cAAc,MAAM,IAAI,MAAM,CAAC;AACvD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3851, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/bloomgoo/gold-exchange-platform/frontend/node_modules/.pnpm/socket.io-client%404.8.1/node_modules/socket.io-client/build/esm-debug/url.js"], "sourcesContent": ["import { parse } from \"engine.io-client\";\nimport debugModule from \"debug\"; // debug()\nconst debug = debugModule(\"socket.io-client:url\"); // debug()\n/**\n * URL parser.\n *\n * @param uri - url\n * @param path - the request path of the connection\n * @param loc - An object meant to mimic window.location.\n *        Defaults to window.location.\n * @public\n */\nexport function url(uri, path = \"\", loc) {\n    let obj = uri;\n    // default to window.location\n    loc = loc || (typeof location !== \"undefined\" && location);\n    if (null == uri)\n        uri = loc.protocol + \"//\" + loc.host;\n    // relative path support\n    if (typeof uri === \"string\") {\n        if (\"/\" === uri.charAt(0)) {\n            if (\"/\" === uri.charAt(1)) {\n                uri = loc.protocol + uri;\n            }\n            else {\n                uri = loc.host + uri;\n            }\n        }\n        if (!/^(https?|wss?):\\/\\//.test(uri)) {\n            debug(\"protocol-less url %s\", uri);\n            if (\"undefined\" !== typeof loc) {\n                uri = loc.protocol + \"//\" + uri;\n            }\n            else {\n                uri = \"https://\" + uri;\n            }\n        }\n        // parse\n        debug(\"parse %s\", uri);\n        obj = parse(uri);\n    }\n    // make sure we treat `localhost:80` and `localhost` equally\n    if (!obj.port) {\n        if (/^(http|ws)$/.test(obj.protocol)) {\n            obj.port = \"80\";\n        }\n        else if (/^(http|ws)s$/.test(obj.protocol)) {\n            obj.port = \"443\";\n        }\n    }\n    obj.path = obj.path || \"/\";\n    const ipv6 = obj.host.indexOf(\":\") !== -1;\n    const host = ipv6 ? \"[\" + obj.host + \"]\" : obj.host;\n    // define unique id\n    obj.id = obj.protocol + \"://\" + host + \":\" + obj.port + path;\n    // define href\n    obj.href =\n        obj.protocol +\n            \"://\" +\n            host +\n            (loc && loc.port === obj.port ? \"\" : \":\" + obj.port);\n    return obj;\n}\n"], "names": [], "mappings": ";;;AAAA;AAAA;AACA,0TAAiC,UAAU;;;AAC3C,MAAM,QAAQ,CAAA,GAAA,uLAAA,CAAA,UAAW,AAAD,EAAE,yBAAyB,UAAU;AAUtD,SAAS,IAAI,GAAG,EAAE,OAAO,EAAE,EAAE,GAAG;IACnC,IAAI,MAAM;IACV,6BAA6B;IAC7B,MAAM,OAAQ,OAAO,aAAa,eAAe;IACjD,IAAI,QAAQ,KACR,MAAM,IAAI,QAAQ,GAAG,OAAO,IAAI,IAAI;IACxC,wBAAwB;IACxB,IAAI,OAAO,QAAQ,UAAU;QACzB,IAAI,QAAQ,IAAI,MAAM,CAAC,IAAI;YACvB,IAAI,QAAQ,IAAI,MAAM,CAAC,IAAI;gBACvB,MAAM,IAAI,QAAQ,GAAG;YACzB,OACK;gBACD,MAAM,IAAI,IAAI,GAAG;YACrB;QACJ;QACA,IAAI,CAAC,sBAAsB,IAAI,CAAC,MAAM;YAClC,MAAM,wBAAwB;YAC9B,IAAI,gBAAgB,OAAO,KAAK;gBAC5B,MAAM,IAAI,QAAQ,GAAG,OAAO;YAChC,OACK;gBACD,MAAM,aAAa;YACvB;QACJ;QACA,QAAQ;QACR,MAAM,YAAY;QAClB,MAAM,CAAA,GAAA,yPAAA,CAAA,QAAK,AAAD,EAAE;IAChB;IACA,4DAA4D;IAC5D,IAAI,CAAC,IAAI,IAAI,EAAE;QACX,IAAI,cAAc,IAAI,CAAC,IAAI,QAAQ,GAAG;YAClC,IAAI,IAAI,GAAG;QACf,OACK,IAAI,eAAe,IAAI,CAAC,IAAI,QAAQ,GAAG;YACxC,IAAI,IAAI,GAAG;QACf;IACJ;IACA,IAAI,IAAI,GAAG,IAAI,IAAI,IAAI;IACvB,MAAM,OAAO,IAAI,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC;IACxC,MAAM,OAAO,OAAO,MAAM,IAAI,IAAI,GAAG,MAAM,IAAI,IAAI;IACnD,mBAAmB;IACnB,IAAI,EAAE,GAAG,IAAI,QAAQ,GAAG,QAAQ,OAAO,MAAM,IAAI,IAAI,GAAG;IACxD,cAAc;IACd,IAAI,IAAI,GACJ,IAAI,QAAQ,GACR,QACA,OACA,CAAC,OAAO,IAAI,IAAI,KAAK,IAAI,IAAI,GAAG,KAAK,MAAM,IAAI,IAAI;IAC3D,OAAO;AACX", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3909, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/bloomgoo/gold-exchange-platform/frontend/node_modules/.pnpm/socket.io-client%404.8.1/node_modules/socket.io-client/build/esm-debug/on.js"], "sourcesContent": ["export function on(obj, ev, fn) {\n    obj.on(ev, fn);\n    return function subDestroy() {\n        obj.off(ev, fn);\n    };\n}\n"], "names": [], "mappings": ";;;AAAO,SAAS,GAAG,GAAG,EAAE,EAAE,EAAE,EAAE;IAC1B,IAAI,EAAE,CAAC,IAAI;IACX,OAAO,SAAS;QACZ,IAAI,GAAG,CAAC,IAAI;IAChB;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3924, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/bloomgoo/gold-exchange-platform/frontend/node_modules/.pnpm/socket.io-client%404.8.1/node_modules/socket.io-client/build/esm-debug/socket.js"], "sourcesContent": ["import { PacketType } from \"socket.io-parser\";\nimport { on } from \"./on.js\";\nimport { Emitter, } from \"@socket.io/component-emitter\";\nimport debugModule from \"debug\"; // debug()\nconst debug = debugModule(\"socket.io-client:socket\"); // debug()\n/**\n * Internal events.\n * These events can't be emitted by the user.\n */\nconst RESERVED_EVENTS = Object.freeze({\n    connect: 1,\n    connect_error: 1,\n    disconnect: 1,\n    disconnecting: 1,\n    // EventEmitter reserved events: https://nodejs.org/api/events.html#events_event_newlistener\n    newListener: 1,\n    removeListener: 1,\n});\n/**\n * A Socket is the fundamental class for interacting with the server.\n *\n * A Socket belongs to a certain Namespace (by default /) and uses an underlying {@link Manager} to communicate.\n *\n * @example\n * const socket = io();\n *\n * socket.on(\"connect\", () => {\n *   console.log(\"connected\");\n * });\n *\n * // send an event to the server\n * socket.emit(\"foo\", \"bar\");\n *\n * socket.on(\"foobar\", () => {\n *   // an event was received from the server\n * });\n *\n * // upon disconnection\n * socket.on(\"disconnect\", (reason) => {\n *   console.log(`disconnected due to ${reason}`);\n * });\n */\nexport class Socket extends Emitter {\n    /**\n     * `Socket` constructor.\n     */\n    constructor(io, nsp, opts) {\n        super();\n        /**\n         * Whether the socket is currently connected to the server.\n         *\n         * @example\n         * const socket = io();\n         *\n         * socket.on(\"connect\", () => {\n         *   console.log(socket.connected); // true\n         * });\n         *\n         * socket.on(\"disconnect\", () => {\n         *   console.log(socket.connected); // false\n         * });\n         */\n        this.connected = false;\n        /**\n         * Whether the connection state was recovered after a temporary disconnection. In that case, any missed packets will\n         * be transmitted by the server.\n         */\n        this.recovered = false;\n        /**\n         * Buffer for packets received before the CONNECT packet\n         */\n        this.receiveBuffer = [];\n        /**\n         * Buffer for packets that will be sent once the socket is connected\n         */\n        this.sendBuffer = [];\n        /**\n         * The queue of packets to be sent with retry in case of failure.\n         *\n         * Packets are sent one by one, each waiting for the server acknowledgement, in order to guarantee the delivery order.\n         * @private\n         */\n        this._queue = [];\n        /**\n         * A sequence to generate the ID of the {@link QueuedPacket}.\n         * @private\n         */\n        this._queueSeq = 0;\n        this.ids = 0;\n        /**\n         * A map containing acknowledgement handlers.\n         *\n         * The `withError` attribute is used to differentiate handlers that accept an error as first argument:\n         *\n         * - `socket.emit(\"test\", (err, value) => { ... })` with `ackTimeout` option\n         * - `socket.timeout(5000).emit(\"test\", (err, value) => { ... })`\n         * - `const value = await socket.emitWithAck(\"test\")`\n         *\n         * From those that don't:\n         *\n         * - `socket.emit(\"test\", (value) => { ... });`\n         *\n         * In the first case, the handlers will be called with an error when:\n         *\n         * - the timeout is reached\n         * - the socket gets disconnected\n         *\n         * In the second case, the handlers will be simply discarded upon disconnection, since the client will never receive\n         * an acknowledgement from the server.\n         *\n         * @private\n         */\n        this.acks = {};\n        this.flags = {};\n        this.io = io;\n        this.nsp = nsp;\n        if (opts && opts.auth) {\n            this.auth = opts.auth;\n        }\n        this._opts = Object.assign({}, opts);\n        if (this.io._autoConnect)\n            this.open();\n    }\n    /**\n     * Whether the socket is currently disconnected\n     *\n     * @example\n     * const socket = io();\n     *\n     * socket.on(\"connect\", () => {\n     *   console.log(socket.disconnected); // false\n     * });\n     *\n     * socket.on(\"disconnect\", () => {\n     *   console.log(socket.disconnected); // true\n     * });\n     */\n    get disconnected() {\n        return !this.connected;\n    }\n    /**\n     * Subscribe to open, close and packet events\n     *\n     * @private\n     */\n    subEvents() {\n        if (this.subs)\n            return;\n        const io = this.io;\n        this.subs = [\n            on(io, \"open\", this.onopen.bind(this)),\n            on(io, \"packet\", this.onpacket.bind(this)),\n            on(io, \"error\", this.onerror.bind(this)),\n            on(io, \"close\", this.onclose.bind(this)),\n        ];\n    }\n    /**\n     * Whether the Socket will try to reconnect when its Manager connects or reconnects.\n     *\n     * @example\n     * const socket = io();\n     *\n     * console.log(socket.active); // true\n     *\n     * socket.on(\"disconnect\", (reason) => {\n     *   if (reason === \"io server disconnect\") {\n     *     // the disconnection was initiated by the server, you need to manually reconnect\n     *     console.log(socket.active); // false\n     *   }\n     *   // else the socket will automatically try to reconnect\n     *   console.log(socket.active); // true\n     * });\n     */\n    get active() {\n        return !!this.subs;\n    }\n    /**\n     * \"Opens\" the socket.\n     *\n     * @example\n     * const socket = io({\n     *   autoConnect: false\n     * });\n     *\n     * socket.connect();\n     */\n    connect() {\n        if (this.connected)\n            return this;\n        this.subEvents();\n        if (!this.io[\"_reconnecting\"])\n            this.io.open(); // ensure open\n        if (\"open\" === this.io._readyState)\n            this.onopen();\n        return this;\n    }\n    /**\n     * Alias for {@link connect()}.\n     */\n    open() {\n        return this.connect();\n    }\n    /**\n     * Sends a `message` event.\n     *\n     * This method mimics the WebSocket.send() method.\n     *\n     * @see https://developer.mozilla.org/en-US/docs/Web/API/WebSocket/send\n     *\n     * @example\n     * socket.send(\"hello\");\n     *\n     * // this is equivalent to\n     * socket.emit(\"message\", \"hello\");\n     *\n     * @return self\n     */\n    send(...args) {\n        args.unshift(\"message\");\n        this.emit.apply(this, args);\n        return this;\n    }\n    /**\n     * Override `emit`.\n     * If the event is in `events`, it's emitted normally.\n     *\n     * @example\n     * socket.emit(\"hello\", \"world\");\n     *\n     * // all serializable datastructures are supported (no need to call JSON.stringify)\n     * socket.emit(\"hello\", 1, \"2\", { 3: [\"4\"], 5: Uint8Array.from([6]) });\n     *\n     * // with an acknowledgement from the server\n     * socket.emit(\"hello\", \"world\", (val) => {\n     *   // ...\n     * });\n     *\n     * @return self\n     */\n    emit(ev, ...args) {\n        var _a, _b, _c;\n        if (RESERVED_EVENTS.hasOwnProperty(ev)) {\n            throw new Error('\"' + ev.toString() + '\" is a reserved event name');\n        }\n        args.unshift(ev);\n        if (this._opts.retries && !this.flags.fromQueue && !this.flags.volatile) {\n            this._addToQueue(args);\n            return this;\n        }\n        const packet = {\n            type: PacketType.EVENT,\n            data: args,\n        };\n        packet.options = {};\n        packet.options.compress = this.flags.compress !== false;\n        // event ack callback\n        if (\"function\" === typeof args[args.length - 1]) {\n            const id = this.ids++;\n            debug(\"emitting packet with ack id %d\", id);\n            const ack = args.pop();\n            this._registerAckCallback(id, ack);\n            packet.id = id;\n        }\n        const isTransportWritable = (_b = (_a = this.io.engine) === null || _a === void 0 ? void 0 : _a.transport) === null || _b === void 0 ? void 0 : _b.writable;\n        const isConnected = this.connected && !((_c = this.io.engine) === null || _c === void 0 ? void 0 : _c._hasPingExpired());\n        const discardPacket = this.flags.volatile && !isTransportWritable;\n        if (discardPacket) {\n            debug(\"discard packet as the transport is not currently writable\");\n        }\n        else if (isConnected) {\n            this.notifyOutgoingListeners(packet);\n            this.packet(packet);\n        }\n        else {\n            this.sendBuffer.push(packet);\n        }\n        this.flags = {};\n        return this;\n    }\n    /**\n     * @private\n     */\n    _registerAckCallback(id, ack) {\n        var _a;\n        const timeout = (_a = this.flags.timeout) !== null && _a !== void 0 ? _a : this._opts.ackTimeout;\n        if (timeout === undefined) {\n            this.acks[id] = ack;\n            return;\n        }\n        // @ts-ignore\n        const timer = this.io.setTimeoutFn(() => {\n            delete this.acks[id];\n            for (let i = 0; i < this.sendBuffer.length; i++) {\n                if (this.sendBuffer[i].id === id) {\n                    debug(\"removing packet with ack id %d from the buffer\", id);\n                    this.sendBuffer.splice(i, 1);\n                }\n            }\n            debug(\"event with ack id %d has timed out after %d ms\", id, timeout);\n            ack.call(this, new Error(\"operation has timed out\"));\n        }, timeout);\n        const fn = (...args) => {\n            // @ts-ignore\n            this.io.clearTimeoutFn(timer);\n            ack.apply(this, args);\n        };\n        fn.withError = true;\n        this.acks[id] = fn;\n    }\n    /**\n     * Emits an event and waits for an acknowledgement\n     *\n     * @example\n     * // without timeout\n     * const response = await socket.emitWithAck(\"hello\", \"world\");\n     *\n     * // with a specific timeout\n     * try {\n     *   const response = await socket.timeout(1000).emitWithAck(\"hello\", \"world\");\n     * } catch (err) {\n     *   // the server did not acknowledge the event in the given delay\n     * }\n     *\n     * @return a Promise that will be fulfilled when the server acknowledges the event\n     */\n    emitWithAck(ev, ...args) {\n        return new Promise((resolve, reject) => {\n            const fn = (arg1, arg2) => {\n                return arg1 ? reject(arg1) : resolve(arg2);\n            };\n            fn.withError = true;\n            args.push(fn);\n            this.emit(ev, ...args);\n        });\n    }\n    /**\n     * Add the packet to the queue.\n     * @param args\n     * @private\n     */\n    _addToQueue(args) {\n        let ack;\n        if (typeof args[args.length - 1] === \"function\") {\n            ack = args.pop();\n        }\n        const packet = {\n            id: this._queueSeq++,\n            tryCount: 0,\n            pending: false,\n            args,\n            flags: Object.assign({ fromQueue: true }, this.flags),\n        };\n        args.push((err, ...responseArgs) => {\n            if (packet !== this._queue[0]) {\n                // the packet has already been acknowledged\n                return;\n            }\n            const hasError = err !== null;\n            if (hasError) {\n                if (packet.tryCount > this._opts.retries) {\n                    debug(\"packet [%d] is discarded after %d tries\", packet.id, packet.tryCount);\n                    this._queue.shift();\n                    if (ack) {\n                        ack(err);\n                    }\n                }\n            }\n            else {\n                debug(\"packet [%d] was successfully sent\", packet.id);\n                this._queue.shift();\n                if (ack) {\n                    ack(null, ...responseArgs);\n                }\n            }\n            packet.pending = false;\n            return this._drainQueue();\n        });\n        this._queue.push(packet);\n        this._drainQueue();\n    }\n    /**\n     * Send the first packet of the queue, and wait for an acknowledgement from the server.\n     * @param force - whether to resend a packet that has not been acknowledged yet\n     *\n     * @private\n     */\n    _drainQueue(force = false) {\n        debug(\"draining queue\");\n        if (!this.connected || this._queue.length === 0) {\n            return;\n        }\n        const packet = this._queue[0];\n        if (packet.pending && !force) {\n            debug(\"packet [%d] has already been sent and is waiting for an ack\", packet.id);\n            return;\n        }\n        packet.pending = true;\n        packet.tryCount++;\n        debug(\"sending packet [%d] (try n°%d)\", packet.id, packet.tryCount);\n        this.flags = packet.flags;\n        this.emit.apply(this, packet.args);\n    }\n    /**\n     * Sends a packet.\n     *\n     * @param packet\n     * @private\n     */\n    packet(packet) {\n        packet.nsp = this.nsp;\n        this.io._packet(packet);\n    }\n    /**\n     * Called upon engine `open`.\n     *\n     * @private\n     */\n    onopen() {\n        debug(\"transport is open - connecting\");\n        if (typeof this.auth == \"function\") {\n            this.auth((data) => {\n                this._sendConnectPacket(data);\n            });\n        }\n        else {\n            this._sendConnectPacket(this.auth);\n        }\n    }\n    /**\n     * Sends a CONNECT packet to initiate the Socket.IO session.\n     *\n     * @param data\n     * @private\n     */\n    _sendConnectPacket(data) {\n        this.packet({\n            type: PacketType.CONNECT,\n            data: this._pid\n                ? Object.assign({ pid: this._pid, offset: this._lastOffset }, data)\n                : data,\n        });\n    }\n    /**\n     * Called upon engine or manager `error`.\n     *\n     * @param err\n     * @private\n     */\n    onerror(err) {\n        if (!this.connected) {\n            this.emitReserved(\"connect_error\", err);\n        }\n    }\n    /**\n     * Called upon engine `close`.\n     *\n     * @param reason\n     * @param description\n     * @private\n     */\n    onclose(reason, description) {\n        debug(\"close (%s)\", reason);\n        this.connected = false;\n        delete this.id;\n        this.emitReserved(\"disconnect\", reason, description);\n        this._clearAcks();\n    }\n    /**\n     * Clears the acknowledgement handlers upon disconnection, since the client will never receive an acknowledgement from\n     * the server.\n     *\n     * @private\n     */\n    _clearAcks() {\n        Object.keys(this.acks).forEach((id) => {\n            const isBuffered = this.sendBuffer.some((packet) => String(packet.id) === id);\n            if (!isBuffered) {\n                // note: handlers that do not accept an error as first argument are ignored here\n                const ack = this.acks[id];\n                delete this.acks[id];\n                if (ack.withError) {\n                    ack.call(this, new Error(\"socket has been disconnected\"));\n                }\n            }\n        });\n    }\n    /**\n     * Called with socket packet.\n     *\n     * @param packet\n     * @private\n     */\n    onpacket(packet) {\n        const sameNamespace = packet.nsp === this.nsp;\n        if (!sameNamespace)\n            return;\n        switch (packet.type) {\n            case PacketType.CONNECT:\n                if (packet.data && packet.data.sid) {\n                    this.onconnect(packet.data.sid, packet.data.pid);\n                }\n                else {\n                    this.emitReserved(\"connect_error\", new Error(\"It seems you are trying to reach a Socket.IO server in v2.x with a v3.x client, but they are not compatible (more information here: https://socket.io/docs/v3/migrating-from-2-x-to-3-0/)\"));\n                }\n                break;\n            case PacketType.EVENT:\n            case PacketType.BINARY_EVENT:\n                this.onevent(packet);\n                break;\n            case PacketType.ACK:\n            case PacketType.BINARY_ACK:\n                this.onack(packet);\n                break;\n            case PacketType.DISCONNECT:\n                this.ondisconnect();\n                break;\n            case PacketType.CONNECT_ERROR:\n                this.destroy();\n                const err = new Error(packet.data.message);\n                // @ts-ignore\n                err.data = packet.data.data;\n                this.emitReserved(\"connect_error\", err);\n                break;\n        }\n    }\n    /**\n     * Called upon a server event.\n     *\n     * @param packet\n     * @private\n     */\n    onevent(packet) {\n        const args = packet.data || [];\n        debug(\"emitting event %j\", args);\n        if (null != packet.id) {\n            debug(\"attaching ack callback to event\");\n            args.push(this.ack(packet.id));\n        }\n        if (this.connected) {\n            this.emitEvent(args);\n        }\n        else {\n            this.receiveBuffer.push(Object.freeze(args));\n        }\n    }\n    emitEvent(args) {\n        if (this._anyListeners && this._anyListeners.length) {\n            const listeners = this._anyListeners.slice();\n            for (const listener of listeners) {\n                listener.apply(this, args);\n            }\n        }\n        super.emit.apply(this, args);\n        if (this._pid && args.length && typeof args[args.length - 1] === \"string\") {\n            this._lastOffset = args[args.length - 1];\n        }\n    }\n    /**\n     * Produces an ack callback to emit with an event.\n     *\n     * @private\n     */\n    ack(id) {\n        const self = this;\n        let sent = false;\n        return function (...args) {\n            // prevent double callbacks\n            if (sent)\n                return;\n            sent = true;\n            debug(\"sending ack %j\", args);\n            self.packet({\n                type: PacketType.ACK,\n                id: id,\n                data: args,\n            });\n        };\n    }\n    /**\n     * Called upon a server acknowledgement.\n     *\n     * @param packet\n     * @private\n     */\n    onack(packet) {\n        const ack = this.acks[packet.id];\n        if (typeof ack !== \"function\") {\n            debug(\"bad ack %s\", packet.id);\n            return;\n        }\n        delete this.acks[packet.id];\n        debug(\"calling ack %s with %j\", packet.id, packet.data);\n        // @ts-ignore FIXME ack is incorrectly inferred as 'never'\n        if (ack.withError) {\n            packet.data.unshift(null);\n        }\n        // @ts-ignore\n        ack.apply(this, packet.data);\n    }\n    /**\n     * Called upon server connect.\n     *\n     * @private\n     */\n    onconnect(id, pid) {\n        debug(\"socket connected with id %s\", id);\n        this.id = id;\n        this.recovered = pid && this._pid === pid;\n        this._pid = pid; // defined only if connection state recovery is enabled\n        this.connected = true;\n        this.emitBuffered();\n        this.emitReserved(\"connect\");\n        this._drainQueue(true);\n    }\n    /**\n     * Emit buffered events (received and emitted).\n     *\n     * @private\n     */\n    emitBuffered() {\n        this.receiveBuffer.forEach((args) => this.emitEvent(args));\n        this.receiveBuffer = [];\n        this.sendBuffer.forEach((packet) => {\n            this.notifyOutgoingListeners(packet);\n            this.packet(packet);\n        });\n        this.sendBuffer = [];\n    }\n    /**\n     * Called upon server disconnect.\n     *\n     * @private\n     */\n    ondisconnect() {\n        debug(\"server disconnect (%s)\", this.nsp);\n        this.destroy();\n        this.onclose(\"io server disconnect\");\n    }\n    /**\n     * Called upon forced client/server side disconnections,\n     * this method ensures the manager stops tracking us and\n     * that reconnections don't get triggered for this.\n     *\n     * @private\n     */\n    destroy() {\n        if (this.subs) {\n            // clean subscriptions to avoid reconnections\n            this.subs.forEach((subDestroy) => subDestroy());\n            this.subs = undefined;\n        }\n        this.io[\"_destroy\"](this);\n    }\n    /**\n     * Disconnects the socket manually. In that case, the socket will not try to reconnect.\n     *\n     * If this is the last active Socket instance of the {@link Manager}, the low-level connection will be closed.\n     *\n     * @example\n     * const socket = io();\n     *\n     * socket.on(\"disconnect\", (reason) => {\n     *   // console.log(reason); prints \"io client disconnect\"\n     * });\n     *\n     * socket.disconnect();\n     *\n     * @return self\n     */\n    disconnect() {\n        if (this.connected) {\n            debug(\"performing disconnect (%s)\", this.nsp);\n            this.packet({ type: PacketType.DISCONNECT });\n        }\n        // remove socket from pool\n        this.destroy();\n        if (this.connected) {\n            // fire events\n            this.onclose(\"io client disconnect\");\n        }\n        return this;\n    }\n    /**\n     * Alias for {@link disconnect()}.\n     *\n     * @return self\n     */\n    close() {\n        return this.disconnect();\n    }\n    /**\n     * Sets the compress flag.\n     *\n     * @example\n     * socket.compress(false).emit(\"hello\");\n     *\n     * @param compress - if `true`, compresses the sending data\n     * @return self\n     */\n    compress(compress) {\n        this.flags.compress = compress;\n        return this;\n    }\n    /**\n     * Sets a modifier for a subsequent event emission that the event message will be dropped when this socket is not\n     * ready to send messages.\n     *\n     * @example\n     * socket.volatile.emit(\"hello\"); // the server may or may not receive it\n     *\n     * @returns self\n     */\n    get volatile() {\n        this.flags.volatile = true;\n        return this;\n    }\n    /**\n     * Sets a modifier for a subsequent event emission that the callback will be called with an error when the\n     * given number of milliseconds have elapsed without an acknowledgement from the server:\n     *\n     * @example\n     * socket.timeout(5000).emit(\"my-event\", (err) => {\n     *   if (err) {\n     *     // the server did not acknowledge the event in the given delay\n     *   }\n     * });\n     *\n     * @returns self\n     */\n    timeout(timeout) {\n        this.flags.timeout = timeout;\n        return this;\n    }\n    /**\n     * Adds a listener that will be fired when any event is emitted. The event name is passed as the first argument to the\n     * callback.\n     *\n     * @example\n     * socket.onAny((event, ...args) => {\n     *   console.log(`got ${event}`);\n     * });\n     *\n     * @param listener\n     */\n    onAny(listener) {\n        this._anyListeners = this._anyListeners || [];\n        this._anyListeners.push(listener);\n        return this;\n    }\n    /**\n     * Adds a listener that will be fired when any event is emitted. The event name is passed as the first argument to the\n     * callback. The listener is added to the beginning of the listeners array.\n     *\n     * @example\n     * socket.prependAny((event, ...args) => {\n     *   console.log(`got event ${event}`);\n     * });\n     *\n     * @param listener\n     */\n    prependAny(listener) {\n        this._anyListeners = this._anyListeners || [];\n        this._anyListeners.unshift(listener);\n        return this;\n    }\n    /**\n     * Removes the listener that will be fired when any event is emitted.\n     *\n     * @example\n     * const catchAllListener = (event, ...args) => {\n     *   console.log(`got event ${event}`);\n     * }\n     *\n     * socket.onAny(catchAllListener);\n     *\n     * // remove a specific listener\n     * socket.offAny(catchAllListener);\n     *\n     * // or remove all listeners\n     * socket.offAny();\n     *\n     * @param listener\n     */\n    offAny(listener) {\n        if (!this._anyListeners) {\n            return this;\n        }\n        if (listener) {\n            const listeners = this._anyListeners;\n            for (let i = 0; i < listeners.length; i++) {\n                if (listener === listeners[i]) {\n                    listeners.splice(i, 1);\n                    return this;\n                }\n            }\n        }\n        else {\n            this._anyListeners = [];\n        }\n        return this;\n    }\n    /**\n     * Returns an array of listeners that are listening for any event that is specified. This array can be manipulated,\n     * e.g. to remove listeners.\n     */\n    listenersAny() {\n        return this._anyListeners || [];\n    }\n    /**\n     * Adds a listener that will be fired when any event is emitted. The event name is passed as the first argument to the\n     * callback.\n     *\n     * Note: acknowledgements sent to the server are not included.\n     *\n     * @example\n     * socket.onAnyOutgoing((event, ...args) => {\n     *   console.log(`sent event ${event}`);\n     * });\n     *\n     * @param listener\n     */\n    onAnyOutgoing(listener) {\n        this._anyOutgoingListeners = this._anyOutgoingListeners || [];\n        this._anyOutgoingListeners.push(listener);\n        return this;\n    }\n    /**\n     * Adds a listener that will be fired when any event is emitted. The event name is passed as the first argument to the\n     * callback. The listener is added to the beginning of the listeners array.\n     *\n     * Note: acknowledgements sent to the server are not included.\n     *\n     * @example\n     * socket.prependAnyOutgoing((event, ...args) => {\n     *   console.log(`sent event ${event}`);\n     * });\n     *\n     * @param listener\n     */\n    prependAnyOutgoing(listener) {\n        this._anyOutgoingListeners = this._anyOutgoingListeners || [];\n        this._anyOutgoingListeners.unshift(listener);\n        return this;\n    }\n    /**\n     * Removes the listener that will be fired when any event is emitted.\n     *\n     * @example\n     * const catchAllListener = (event, ...args) => {\n     *   console.log(`sent event ${event}`);\n     * }\n     *\n     * socket.onAnyOutgoing(catchAllListener);\n     *\n     * // remove a specific listener\n     * socket.offAnyOutgoing(catchAllListener);\n     *\n     * // or remove all listeners\n     * socket.offAnyOutgoing();\n     *\n     * @param [listener] - the catch-all listener (optional)\n     */\n    offAnyOutgoing(listener) {\n        if (!this._anyOutgoingListeners) {\n            return this;\n        }\n        if (listener) {\n            const listeners = this._anyOutgoingListeners;\n            for (let i = 0; i < listeners.length; i++) {\n                if (listener === listeners[i]) {\n                    listeners.splice(i, 1);\n                    return this;\n                }\n            }\n        }\n        else {\n            this._anyOutgoingListeners = [];\n        }\n        return this;\n    }\n    /**\n     * Returns an array of listeners that are listening for any event that is specified. This array can be manipulated,\n     * e.g. to remove listeners.\n     */\n    listenersAnyOutgoing() {\n        return this._anyOutgoingListeners || [];\n    }\n    /**\n     * Notify the listeners for each packet sent\n     *\n     * @param packet\n     *\n     * @private\n     */\n    notifyOutgoingListeners(packet) {\n        if (this._anyOutgoingListeners && this._anyOutgoingListeners.length) {\n            const listeners = this._anyOutgoingListeners.slice();\n            for (const listener of listeners) {\n                listener.apply(this, packet.data);\n            }\n        }\n    }\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AACA,0TAAiC,UAAU;;;;;AAC3C,MAAM,QAAQ,CAAA,GAAA,uLAAA,CAAA,UAAW,AAAD,EAAE,4BAA4B,UAAU;AAChE;;;CAGC,GACD,MAAM,kBAAkB,OAAO,MAAM,CAAC;IAClC,SAAS;IACT,eAAe;IACf,YAAY;IACZ,eAAe;IACf,4FAA4F;IAC5F,aAAa;IACb,gBAAgB;AACpB;AAyBO,MAAM,eAAe,gQAAA,CAAA,UAAO;IAC/B;;KAEC,GACD,YAAY,EAAE,EAAE,GAAG,EAAE,IAAI,CAAE;QACvB,KAAK;QACL;;;;;;;;;;;;;SAaC,GACD,IAAI,CAAC,SAAS,GAAG;QACjB;;;SAGC,GACD,IAAI,CAAC,SAAS,GAAG;QACjB;;SAEC,GACD,IAAI,CAAC,aAAa,GAAG,EAAE;QACvB;;SAEC,GACD,IAAI,CAAC,UAAU,GAAG,EAAE;QACpB;;;;;SAKC,GACD,IAAI,CAAC,MAAM,GAAG,EAAE;QAChB;;;SAGC,GACD,IAAI,CAAC,SAAS,GAAG;QACjB,IAAI,CAAC,GAAG,GAAG;QACX;;;;;;;;;;;;;;;;;;;;;;SAsBC,GACD,IAAI,CAAC,IAAI,GAAG,CAAC;QACb,IAAI,CAAC,KAAK,GAAG,CAAC;QACd,IAAI,CAAC,EAAE,GAAG;QACV,IAAI,CAAC,GAAG,GAAG;QACX,IAAI,QAAQ,KAAK,IAAI,EAAE;YACnB,IAAI,CAAC,IAAI,GAAG,KAAK,IAAI;QACzB;QACA,IAAI,CAAC,KAAK,GAAG,OAAO,MAAM,CAAC,CAAC,GAAG;QAC/B,IAAI,IAAI,CAAC,EAAE,CAAC,YAAY,EACpB,IAAI,CAAC,IAAI;IACjB;IACA;;;;;;;;;;;;;KAaC,GACD,IAAI,eAAe;QACf,OAAO,CAAC,IAAI,CAAC,SAAS;IAC1B;IACA;;;;KAIC,GACD,YAAY;QACR,IAAI,IAAI,CAAC,IAAI,EACT;QACJ,MAAM,KAAK,IAAI,CAAC,EAAE;QAClB,IAAI,CAAC,IAAI,GAAG;YACR,CAAA,GAAA,wOAAA,CAAA,KAAE,AAAD,EAAE,IAAI,QAAQ,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI;YACpC,CAAA,GAAA,wOAAA,CAAA,KAAE,AAAD,EAAE,IAAI,UAAU,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI;YACxC,CAAA,GAAA,wOAAA,CAAA,KAAE,AAAD,EAAE,IAAI,SAAS,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI;YACtC,CAAA,GAAA,wOAAA,CAAA,KAAE,AAAD,EAAE,IAAI,SAAS,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI;SACzC;IACL;IACA;;;;;;;;;;;;;;;;KAgBC,GACD,IAAI,SAAS;QACT,OAAO,CAAC,CAAC,IAAI,CAAC,IAAI;IACtB;IACA;;;;;;;;;KASC,GACD,UAAU;QACN,IAAI,IAAI,CAAC,SAAS,EACd,OAAO,IAAI;QACf,IAAI,CAAC,SAAS;QACd,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,gBAAgB,EACzB,IAAI,CAAC,EAAE,CAAC,IAAI,IAAI,cAAc;QAClC,IAAI,WAAW,IAAI,CAAC,EAAE,CAAC,WAAW,EAC9B,IAAI,CAAC,MAAM;QACf,OAAO,IAAI;IACf;IACA;;KAEC,GACD,OAAO;QACH,OAAO,IAAI,CAAC,OAAO;IACvB;IACA;;;;;;;;;;;;;;KAcC,GACD,KAAK,GAAG,IAAI,EAAE;QACV,KAAK,OAAO,CAAC;QACb,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE;QACtB,OAAO,IAAI;IACf;IACA;;;;;;;;;;;;;;;;KAgBC,GACD,KAAK,EAAE,EAAE,GAAG,IAAI,EAAE;QACd,IAAI,IAAI,IAAI;QACZ,IAAI,gBAAgB,cAAc,CAAC,KAAK;YACpC,MAAM,IAAI,MAAM,MAAM,GAAG,QAAQ,KAAK;QAC1C;QACA,KAAK,OAAO,CAAC;QACb,IAAI,IAAI,CAAC,KAAK,CAAC,OAAO,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,SAAS,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE;YACrE,IAAI,CAAC,WAAW,CAAC;YACjB,OAAO,IAAI;QACf;QACA,MAAM,SAAS;YACX,MAAM,2OAAA,CAAA,aAAU,CAAC,KAAK;YACtB,MAAM;QACV;QACA,OAAO,OAAO,GAAG,CAAC;QAClB,OAAO,OAAO,CAAC,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC,QAAQ,KAAK;QAClD,qBAAqB;QACrB,IAAI,eAAe,OAAO,IAAI,CAAC,KAAK,MAAM,GAAG,EAAE,EAAE;YAC7C,MAAM,KAAK,IAAI,CAAC,GAAG;YACnB,MAAM,kCAAkC;YACxC,MAAM,MAAM,KAAK,GAAG;YACpB,IAAI,CAAC,oBAAoB,CAAC,IAAI;YAC9B,OAAO,EAAE,GAAG;QAChB;QACA,MAAM,sBAAsB,CAAC,KAAK,CAAC,KAAK,IAAI,CAAC,EAAE,CAAC,MAAM,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,IAAI,GAAG,SAAS,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,IAAI,GAAG,QAAQ;QAC3J,MAAM,cAAc,IAAI,CAAC,SAAS,IAAI,CAAC,CAAC,CAAC,KAAK,IAAI,CAAC,EAAE,CAAC,MAAM,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,IAAI,GAAG,eAAe,EAAE;QACvH,MAAM,gBAAgB,IAAI,CAAC,KAAK,CAAC,QAAQ,IAAI,CAAC;QAC9C,IAAI,eAAe;YACf,MAAM;QACV,OACK,IAAI,aAAa;YAClB,IAAI,CAAC,uBAAuB,CAAC;YAC7B,IAAI,CAAC,MAAM,CAAC;QAChB,OACK;YACD,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC;QACzB;QACA,IAAI,CAAC,KAAK,GAAG,CAAC;QACd,OAAO,IAAI;IACf;IACA;;KAEC,GACD,qBAAqB,EAAE,EAAE,GAAG,EAAE;QAC1B,IAAI;QACJ,MAAM,UAAU,CAAC,KAAK,IAAI,CAAC,KAAK,CAAC,OAAO,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,IAAI,CAAC,KAAK,CAAC,UAAU;QAChG,IAAI,YAAY,WAAW;YACvB,IAAI,CAAC,IAAI,CAAC,GAAG,GAAG;YAChB;QACJ;QACA,aAAa;QACb,MAAM,QAAQ,IAAI,CAAC,EAAE,CAAC,YAAY,CAAC;YAC/B,OAAO,IAAI,CAAC,IAAI,CAAC,GAAG;YACpB,IAAK,IAAI,IAAI,GAAG,IAAI,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE,IAAK;gBAC7C,IAAI,IAAI,CAAC,UAAU,CAAC,EAAE,CAAC,EAAE,KAAK,IAAI;oBAC9B,MAAM,kDAAkD;oBACxD,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,GAAG;gBAC9B;YACJ;YACA,MAAM,kDAAkD,IAAI;YAC5D,IAAI,IAAI,CAAC,IAAI,EAAE,IAAI,MAAM;QAC7B,GAAG;QACH,MAAM,KAAK,CAAC,GAAG;YACX,aAAa;YACb,IAAI,CAAC,EAAE,CAAC,cAAc,CAAC;YACvB,IAAI,KAAK,CAAC,IAAI,EAAE;QACpB;QACA,GAAG,SAAS,GAAG;QACf,IAAI,CAAC,IAAI,CAAC,GAAG,GAAG;IACpB;IACA;;;;;;;;;;;;;;;KAeC,GACD,YAAY,EAAE,EAAE,GAAG,IAAI,EAAE;QACrB,OAAO,IAAI,QAAQ,CAAC,SAAS;YACzB,MAAM,KAAK,CAAC,MAAM;gBACd,OAAO,OAAO,OAAO,QAAQ,QAAQ;YACzC;YACA,GAAG,SAAS,GAAG;YACf,KAAK,IAAI,CAAC;YACV,IAAI,CAAC,IAAI,CAAC,OAAO;QACrB;IACJ;IACA;;;;KAIC,GACD,YAAY,IAAI,EAAE;QACd,IAAI;QACJ,IAAI,OAAO,IAAI,CAAC,KAAK,MAAM,GAAG,EAAE,KAAK,YAAY;YAC7C,MAAM,KAAK,GAAG;QAClB;QACA,MAAM,SAAS;YACX,IAAI,IAAI,CAAC,SAAS;YAClB,UAAU;YACV,SAAS;YACT;YACA,OAAO,OAAO,MAAM,CAAC;gBAAE,WAAW;YAAK,GAAG,IAAI,CAAC,KAAK;QACxD;QACA,KAAK,IAAI,CAAC,CAAC,KAAK,GAAG;YACf,IAAI,WAAW,IAAI,CAAC,MAAM,CAAC,EAAE,EAAE;gBAC3B,2CAA2C;gBAC3C;YACJ;YACA,MAAM,WAAW,QAAQ;YACzB,IAAI,UAAU;gBACV,IAAI,OAAO,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE;oBACtC,MAAM,2CAA2C,OAAO,EAAE,EAAE,OAAO,QAAQ;oBAC3E,IAAI,CAAC,MAAM,CAAC,KAAK;oBACjB,IAAI,KAAK;wBACL,IAAI;oBACR;gBACJ;YACJ,OACK;gBACD,MAAM,qCAAqC,OAAO,EAAE;gBACpD,IAAI,CAAC,MAAM,CAAC,KAAK;gBACjB,IAAI,KAAK;oBACL,IAAI,SAAS;gBACjB;YACJ;YACA,OAAO,OAAO,GAAG;YACjB,OAAO,IAAI,CAAC,WAAW;QAC3B;QACA,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC;QACjB,IAAI,CAAC,WAAW;IACpB;IACA;;;;;KAKC,GACD,YAAY,QAAQ,KAAK,EAAE;QACvB,MAAM;QACN,IAAI,CAAC,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,MAAM,CAAC,MAAM,KAAK,GAAG;YAC7C;QACJ;QACA,MAAM,SAAS,IAAI,CAAC,MAAM,CAAC,EAAE;QAC7B,IAAI,OAAO,OAAO,IAAI,CAAC,OAAO;YAC1B,MAAM,+DAA+D,OAAO,EAAE;YAC9E;QACJ;QACA,OAAO,OAAO,GAAG;QACjB,OAAO,QAAQ;QACf,MAAM,kCAAkC,OAAO,EAAE,EAAE,OAAO,QAAQ;QAClE,IAAI,CAAC,KAAK,GAAG,OAAO,KAAK;QACzB,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE,OAAO,IAAI;IACrC;IACA;;;;;KAKC,GACD,OAAO,MAAM,EAAE;QACX,OAAO,GAAG,GAAG,IAAI,CAAC,GAAG;QACrB,IAAI,CAAC,EAAE,CAAC,OAAO,CAAC;IACpB;IACA;;;;KAIC,GACD,SAAS;QACL,MAAM;QACN,IAAI,OAAO,IAAI,CAAC,IAAI,IAAI,YAAY;YAChC,IAAI,CAAC,IAAI,CAAC,CAAC;gBACP,IAAI,CAAC,kBAAkB,CAAC;YAC5B;QACJ,OACK;YACD,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,IAAI;QACrC;IACJ;IACA;;;;;KAKC,GACD,mBAAmB,IAAI,EAAE;QACrB,IAAI,CAAC,MAAM,CAAC;YACR,MAAM,2OAAA,CAAA,aAAU,CAAC,OAAO;YACxB,MAAM,IAAI,CAAC,IAAI,GACT,OAAO,MAAM,CAAC;gBAAE,KAAK,IAAI,CAAC,IAAI;gBAAE,QAAQ,IAAI,CAAC,WAAW;YAAC,GAAG,QAC5D;QACV;IACJ;IACA;;;;;KAKC,GACD,QAAQ,GAAG,EAAE;QACT,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;YACjB,IAAI,CAAC,YAAY,CAAC,iBAAiB;QACvC;IACJ;IACA;;;;;;KAMC,GACD,QAAQ,MAAM,EAAE,WAAW,EAAE;QACzB,MAAM,cAAc;QACpB,IAAI,CAAC,SAAS,GAAG;QACjB,OAAO,IAAI,CAAC,EAAE;QACd,IAAI,CAAC,YAAY,CAAC,cAAc,QAAQ;QACxC,IAAI,CAAC,UAAU;IACnB;IACA;;;;;KAKC,GACD,aAAa;QACT,OAAO,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;YAC5B,MAAM,aAAa,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,SAAW,OAAO,OAAO,EAAE,MAAM;YAC1E,IAAI,CAAC,YAAY;gBACb,gFAAgF;gBAChF,MAAM,MAAM,IAAI,CAAC,IAAI,CAAC,GAAG;gBACzB,OAAO,IAAI,CAAC,IAAI,CAAC,GAAG;gBACpB,IAAI,IAAI,SAAS,EAAE;oBACf,IAAI,IAAI,CAAC,IAAI,EAAE,IAAI,MAAM;gBAC7B;YACJ;QACJ;IACJ;IACA;;;;;KAKC,GACD,SAAS,MAAM,EAAE;QACb,MAAM,gBAAgB,OAAO,GAAG,KAAK,IAAI,CAAC,GAAG;QAC7C,IAAI,CAAC,eACD;QACJ,OAAQ,OAAO,IAAI;YACf,KAAK,2OAAA,CAAA,aAAU,CAAC,OAAO;gBACnB,IAAI,OAAO,IAAI,IAAI,OAAO,IAAI,CAAC,GAAG,EAAE;oBAChC,IAAI,CAAC,SAAS,CAAC,OAAO,IAAI,CAAC,GAAG,EAAE,OAAO,IAAI,CAAC,GAAG;gBACnD,OACK;oBACD,IAAI,CAAC,YAAY,CAAC,iBAAiB,IAAI,MAAM;gBACjD;gBACA;YACJ,KAAK,2OAAA,CAAA,aAAU,CAAC,KAAK;YACrB,KAAK,2OAAA,CAAA,aAAU,CAAC,YAAY;gBACxB,IAAI,CAAC,OAAO,CAAC;gBACb;YACJ,KAAK,2OAAA,CAAA,aAAU,CAAC,GAAG;YACnB,KAAK,2OAAA,CAAA,aAAU,CAAC,UAAU;gBACtB,IAAI,CAAC,KAAK,CAAC;gBACX;YACJ,KAAK,2OAAA,CAAA,aAAU,CAAC,UAAU;gBACtB,IAAI,CAAC,YAAY;gBACjB;YACJ,KAAK,2OAAA,CAAA,aAAU,CAAC,aAAa;gBACzB,IAAI,CAAC,OAAO;gBACZ,MAAM,MAAM,IAAI,MAAM,OAAO,IAAI,CAAC,OAAO;gBACzC,aAAa;gBACb,IAAI,IAAI,GAAG,OAAO,IAAI,CAAC,IAAI;gBAC3B,IAAI,CAAC,YAAY,CAAC,iBAAiB;gBACnC;QACR;IACJ;IACA;;;;;KAKC,GACD,QAAQ,MAAM,EAAE;QACZ,MAAM,OAAO,OAAO,IAAI,IAAI,EAAE;QAC9B,MAAM,qBAAqB;QAC3B,IAAI,QAAQ,OAAO,EAAE,EAAE;YACnB,MAAM;YACN,KAAK,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,OAAO,EAAE;QAChC;QACA,IAAI,IAAI,CAAC,SAAS,EAAE;YAChB,IAAI,CAAC,SAAS,CAAC;QACnB,OACK;YACD,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,OAAO,MAAM,CAAC;QAC1C;IACJ;IACA,UAAU,IAAI,EAAE;QACZ,IAAI,IAAI,CAAC,aAAa,IAAI,IAAI,CAAC,aAAa,CAAC,MAAM,EAAE;YACjD,MAAM,YAAY,IAAI,CAAC,aAAa,CAAC,KAAK;YAC1C,KAAK,MAAM,YAAY,UAAW;gBAC9B,SAAS,KAAK,CAAC,IAAI,EAAE;YACzB;QACJ;QACA,KAAK,CAAC,KAAK,KAAK,CAAC,IAAI,EAAE;QACvB,IAAI,IAAI,CAAC,IAAI,IAAI,KAAK,MAAM,IAAI,OAAO,IAAI,CAAC,KAAK,MAAM,GAAG,EAAE,KAAK,UAAU;YACvE,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,KAAK,MAAM,GAAG,EAAE;QAC5C;IACJ;IACA;;;;KAIC,GACD,IAAI,EAAE,EAAE;QACJ,MAAM,OAAO,IAAI;QACjB,IAAI,OAAO;QACX,OAAO,SAAU,GAAG,IAAI;YACpB,2BAA2B;YAC3B,IAAI,MACA;YACJ,OAAO;YACP,MAAM,kBAAkB;YACxB,KAAK,MAAM,CAAC;gBACR,MAAM,2OAAA,CAAA,aAAU,CAAC,GAAG;gBACpB,IAAI;gBACJ,MAAM;YACV;QACJ;IACJ;IACA;;;;;KAKC,GACD,MAAM,MAAM,EAAE;QACV,MAAM,MAAM,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC;QAChC,IAAI,OAAO,QAAQ,YAAY;YAC3B,MAAM,cAAc,OAAO,EAAE;YAC7B;QACJ;QACA,OAAO,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC;QAC3B,MAAM,0BAA0B,OAAO,EAAE,EAAE,OAAO,IAAI;QACtD,0DAA0D;QAC1D,IAAI,IAAI,SAAS,EAAE;YACf,OAAO,IAAI,CAAC,OAAO,CAAC;QACxB;QACA,aAAa;QACb,IAAI,KAAK,CAAC,IAAI,EAAE,OAAO,IAAI;IAC/B;IACA;;;;KAIC,GACD,UAAU,EAAE,EAAE,GAAG,EAAE;QACf,MAAM,+BAA+B;QACrC,IAAI,CAAC,EAAE,GAAG;QACV,IAAI,CAAC,SAAS,GAAG,OAAO,IAAI,CAAC,IAAI,KAAK;QACtC,IAAI,CAAC,IAAI,GAAG,KAAK,uDAAuD;QACxE,IAAI,CAAC,SAAS,GAAG;QACjB,IAAI,CAAC,YAAY;QACjB,IAAI,CAAC,YAAY,CAAC;QAClB,IAAI,CAAC,WAAW,CAAC;IACrB;IACA;;;;KAIC,GACD,eAAe;QACX,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC,OAAS,IAAI,CAAC,SAAS,CAAC;QACpD,IAAI,CAAC,aAAa,GAAG,EAAE;QACvB,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC;YACrB,IAAI,CAAC,uBAAuB,CAAC;YAC7B,IAAI,CAAC,MAAM,CAAC;QAChB;QACA,IAAI,CAAC,UAAU,GAAG,EAAE;IACxB;IACA;;;;KAIC,GACD,eAAe;QACX,MAAM,0BAA0B,IAAI,CAAC,GAAG;QACxC,IAAI,CAAC,OAAO;QACZ,IAAI,CAAC,OAAO,CAAC;IACjB;IACA;;;;;;KAMC,GACD,UAAU;QACN,IAAI,IAAI,CAAC,IAAI,EAAE;YACX,6CAA6C;YAC7C,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,aAAe;YAClC,IAAI,CAAC,IAAI,GAAG;QAChB;QACA,IAAI,CAAC,EAAE,CAAC,WAAW,CAAC,IAAI;IAC5B;IACA;;;;;;;;;;;;;;;KAeC,GACD,aAAa;QACT,IAAI,IAAI,CAAC,SAAS,EAAE;YAChB,MAAM,8BAA8B,IAAI,CAAC,GAAG;YAC5C,IAAI,CAAC,MAAM,CAAC;gBAAE,MAAM,2OAAA,CAAA,aAAU,CAAC,UAAU;YAAC;QAC9C;QACA,0BAA0B;QAC1B,IAAI,CAAC,OAAO;QACZ,IAAI,IAAI,CAAC,SAAS,EAAE;YAChB,cAAc;YACd,IAAI,CAAC,OAAO,CAAC;QACjB;QACA,OAAO,IAAI;IACf;IACA;;;;KAIC,GACD,QAAQ;QACJ,OAAO,IAAI,CAAC,UAAU;IAC1B;IACA;;;;;;;;KAQC,GACD,SAAS,QAAQ,EAAE;QACf,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG;QACtB,OAAO,IAAI;IACf;IACA;;;;;;;;KAQC,GACD,IAAI,WAAW;QACX,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG;QACtB,OAAO,IAAI;IACf;IACA;;;;;;;;;;;;KAYC,GACD,QAAQ,OAAO,EAAE;QACb,IAAI,CAAC,KAAK,CAAC,OAAO,GAAG;QACrB,OAAO,IAAI;IACf;IACA;;;;;;;;;;KAUC,GACD,MAAM,QAAQ,EAAE;QACZ,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,aAAa,IAAI,EAAE;QAC7C,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC;QACxB,OAAO,IAAI;IACf;IACA;;;;;;;;;;KAUC,GACD,WAAW,QAAQ,EAAE;QACjB,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,aAAa,IAAI,EAAE;QAC7C,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC;QAC3B,OAAO,IAAI;IACf;IACA;;;;;;;;;;;;;;;;;KAiBC,GACD,OAAO,QAAQ,EAAE;QACb,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE;YACrB,OAAO,IAAI;QACf;QACA,IAAI,UAAU;YACV,MAAM,YAAY,IAAI,CAAC,aAAa;YACpC,IAAK,IAAI,IAAI,GAAG,IAAI,UAAU,MAAM,EAAE,IAAK;gBACvC,IAAI,aAAa,SAAS,CAAC,EAAE,EAAE;oBAC3B,UAAU,MAAM,CAAC,GAAG;oBACpB,OAAO,IAAI;gBACf;YACJ;QACJ,OACK;YACD,IAAI,CAAC,aAAa,GAAG,EAAE;QAC3B;QACA,OAAO,IAAI;IACf;IACA;;;KAGC,GACD,eAAe;QACX,OAAO,IAAI,CAAC,aAAa,IAAI,EAAE;IACnC;IACA;;;;;;;;;;;;KAYC,GACD,cAAc,QAAQ,EAAE;QACpB,IAAI,CAAC,qBAAqB,GAAG,IAAI,CAAC,qBAAqB,IAAI,EAAE;QAC7D,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC;QAChC,OAAO,IAAI;IACf;IACA;;;;;;;;;;;;KAYC,GACD,mBAAmB,QAAQ,EAAE;QACzB,IAAI,CAAC,qBAAqB,GAAG,IAAI,CAAC,qBAAqB,IAAI,EAAE;QAC7D,IAAI,CAAC,qBAAqB,CAAC,OAAO,CAAC;QACnC,OAAO,IAAI;IACf;IACA;;;;;;;;;;;;;;;;;KAiBC,GACD,eAAe,QAAQ,EAAE;QACrB,IAAI,CAAC,IAAI,CAAC,qBAAqB,EAAE;YAC7B,OAAO,IAAI;QACf;QACA,IAAI,UAAU;YACV,MAAM,YAAY,IAAI,CAAC,qBAAqB;YAC5C,IAAK,IAAI,IAAI,GAAG,IAAI,UAAU,MAAM,EAAE,IAAK;gBACvC,IAAI,aAAa,SAAS,CAAC,EAAE,EAAE;oBAC3B,UAAU,MAAM,CAAC,GAAG;oBACpB,OAAO,IAAI;gBACf;YACJ;QACJ,OACK;YACD,IAAI,CAAC,qBAAqB,GAAG,EAAE;QACnC;QACA,OAAO,IAAI;IACf;IACA;;;KAGC,GACD,uBAAuB;QACnB,OAAO,IAAI,CAAC,qBAAqB,IAAI,EAAE;IAC3C;IACA;;;;;;KAMC,GACD,wBAAwB,MAAM,EAAE;QAC5B,IAAI,IAAI,CAAC,qBAAqB,IAAI,IAAI,CAAC,qBAAqB,CAAC,MAAM,EAAE;YACjE,MAAM,YAAY,IAAI,CAAC,qBAAqB,CAAC,KAAK;YAClD,KAAK,MAAM,YAAY,UAAW;gBAC9B,SAAS,KAAK,CAAC,IAAI,EAAE,OAAO,IAAI;YACpC;QACJ;IACJ;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4758, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/bloomgoo/gold-exchange-platform/frontend/node_modules/.pnpm/socket.io-client%404.8.1/node_modules/socket.io-client/build/esm-debug/contrib/backo2.js"], "sourcesContent": ["/**\n * Initialize backoff timer with `opts`.\n *\n * - `min` initial timeout in milliseconds [100]\n * - `max` max timeout [10000]\n * - `jitter` [0]\n * - `factor` [2]\n *\n * @param {Object} opts\n * @api public\n */\nexport function Backoff(opts) {\n    opts = opts || {};\n    this.ms = opts.min || 100;\n    this.max = opts.max || 10000;\n    this.factor = opts.factor || 2;\n    this.jitter = opts.jitter > 0 && opts.jitter <= 1 ? opts.jitter : 0;\n    this.attempts = 0;\n}\n/**\n * Return the backoff duration.\n *\n * @return {Number}\n * @api public\n */\nBackoff.prototype.duration = function () {\n    var ms = this.ms * Math.pow(this.factor, this.attempts++);\n    if (this.jitter) {\n        var rand = Math.random();\n        var deviation = Math.floor(rand * this.jitter * ms);\n        ms = (Math.floor(rand * 10) & 1) == 0 ? ms - deviation : ms + deviation;\n    }\n    return Math.min(ms, this.max) | 0;\n};\n/**\n * Reset the number of attempts.\n *\n * @api public\n */\nBackoff.prototype.reset = function () {\n    this.attempts = 0;\n};\n/**\n * Set the minimum duration\n *\n * @api public\n */\nBackoff.prototype.setMin = function (min) {\n    this.ms = min;\n};\n/**\n * Set the maximum duration\n *\n * @api public\n */\nBackoff.prototype.setMax = function (max) {\n    this.max = max;\n};\n/**\n * Set the jitter\n *\n * @api public\n */\nBackoff.prototype.setJitter = function (jitter) {\n    this.jitter = jitter;\n};\n"], "names": [], "mappings": "AAAA;;;;;;;;;;CAUC;;;AACM,SAAS,QAAQ,IAAI;IACxB,OAAO,QAAQ,CAAC;IAChB,IAAI,CAAC,EAAE,GAAG,KAAK,GAAG,IAAI;IACtB,IAAI,CAAC,GAAG,GAAG,KAAK,GAAG,IAAI;IACvB,IAAI,CAAC,MAAM,GAAG,KAAK,MAAM,IAAI;IAC7B,IAAI,CAAC,MAAM,GAAG,KAAK,MAAM,GAAG,KAAK,KAAK,MAAM,IAAI,IAAI,KAAK,MAAM,GAAG;IAClE,IAAI,CAAC,QAAQ,GAAG;AACpB;AACA;;;;;CAKC,GACD,QAAQ,SAAS,CAAC,QAAQ,GAAG;IACzB,IAAI,KAAK,IAAI,CAAC,EAAE,GAAG,KAAK,GAAG,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,QAAQ;IACtD,IAAI,IAAI,CAAC,MAAM,EAAE;QACb,IAAI,OAAO,KAAK,MAAM;QACtB,IAAI,YAAY,KAAK,KAAK,CAAC,OAAO,IAAI,CAAC,MAAM,GAAG;QAChD,KAAK,CAAC,KAAK,KAAK,CAAC,OAAO,MAAM,CAAC,KAAK,IAAI,KAAK,YAAY,KAAK;IAClE;IACA,OAAO,KAAK,GAAG,CAAC,IAAI,IAAI,CAAC,GAAG,IAAI;AACpC;AACA;;;;CAIC,GACD,QAAQ,SAAS,CAAC,KAAK,GAAG;IACtB,IAAI,CAAC,QAAQ,GAAG;AACpB;AACA;;;;CAIC,GACD,QAAQ,SAAS,CAAC,MAAM,GAAG,SAAU,GAAG;IACpC,IAAI,CAAC,EAAE,GAAG;AACd;AACA;;;;CAIC,GACD,QAAQ,SAAS,CAAC,MAAM,GAAG,SAAU,GAAG;IACpC,IAAI,CAAC,GAAG,GAAG;AACf;AACA;;;;CAIC,GACD,QAAQ,SAAS,CAAC,SAAS,GAAG,SAAU,MAAM;IAC1C,IAAI,CAAC,MAAM,GAAG;AAClB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4827, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/bloomgoo/gold-exchange-platform/frontend/node_modules/.pnpm/socket.io-client%404.8.1/node_modules/socket.io-client/build/esm-debug/manager.js"], "sourcesContent": ["import { Socket as Engine, installTimerFunctions, nextTick, } from \"engine.io-client\";\nimport { Socket } from \"./socket.js\";\nimport * as parser from \"socket.io-parser\";\nimport { on } from \"./on.js\";\nimport { Backoff } from \"./contrib/backo2.js\";\nimport { Emitter, } from \"@socket.io/component-emitter\";\nimport debugModule from \"debug\"; // debug()\nconst debug = debugModule(\"socket.io-client:manager\"); // debug()\nexport class Manager extends Emitter {\n    constructor(uri, opts) {\n        var _a;\n        super();\n        this.nsps = {};\n        this.subs = [];\n        if (uri && \"object\" === typeof uri) {\n            opts = uri;\n            uri = undefined;\n        }\n        opts = opts || {};\n        opts.path = opts.path || \"/socket.io\";\n        this.opts = opts;\n        installTimerFunctions(this, opts);\n        this.reconnection(opts.reconnection !== false);\n        this.reconnectionAttempts(opts.reconnectionAttempts || Infinity);\n        this.reconnectionDelay(opts.reconnectionDelay || 1000);\n        this.reconnectionDelayMax(opts.reconnectionDelayMax || 5000);\n        this.randomizationFactor((_a = opts.randomizationFactor) !== null && _a !== void 0 ? _a : 0.5);\n        this.backoff = new Backoff({\n            min: this.reconnectionDelay(),\n            max: this.reconnectionDelayMax(),\n            jitter: this.randomizationFactor(),\n        });\n        this.timeout(null == opts.timeout ? 20000 : opts.timeout);\n        this._readyState = \"closed\";\n        this.uri = uri;\n        const _parser = opts.parser || parser;\n        this.encoder = new _parser.Encoder();\n        this.decoder = new _parser.Decoder();\n        this._autoConnect = opts.autoConnect !== false;\n        if (this._autoConnect)\n            this.open();\n    }\n    reconnection(v) {\n        if (!arguments.length)\n            return this._reconnection;\n        this._reconnection = !!v;\n        if (!v) {\n            this.skipReconnect = true;\n        }\n        return this;\n    }\n    reconnectionAttempts(v) {\n        if (v === undefined)\n            return this._reconnectionAttempts;\n        this._reconnectionAttempts = v;\n        return this;\n    }\n    reconnectionDelay(v) {\n        var _a;\n        if (v === undefined)\n            return this._reconnectionDelay;\n        this._reconnectionDelay = v;\n        (_a = this.backoff) === null || _a === void 0 ? void 0 : _a.setMin(v);\n        return this;\n    }\n    randomizationFactor(v) {\n        var _a;\n        if (v === undefined)\n            return this._randomizationFactor;\n        this._randomizationFactor = v;\n        (_a = this.backoff) === null || _a === void 0 ? void 0 : _a.setJitter(v);\n        return this;\n    }\n    reconnectionDelayMax(v) {\n        var _a;\n        if (v === undefined)\n            return this._reconnectionDelayMax;\n        this._reconnectionDelayMax = v;\n        (_a = this.backoff) === null || _a === void 0 ? void 0 : _a.setMax(v);\n        return this;\n    }\n    timeout(v) {\n        if (!arguments.length)\n            return this._timeout;\n        this._timeout = v;\n        return this;\n    }\n    /**\n     * Starts trying to reconnect if reconnection is enabled and we have not\n     * started reconnecting yet\n     *\n     * @private\n     */\n    maybeReconnectOnOpen() {\n        // Only try to reconnect if it's the first time we're connecting\n        if (!this._reconnecting &&\n            this._reconnection &&\n            this.backoff.attempts === 0) {\n            // keeps reconnection from firing twice for the same reconnection loop\n            this.reconnect();\n        }\n    }\n    /**\n     * Sets the current transport `socket`.\n     *\n     * @param {Function} fn - optional, callback\n     * @return self\n     * @public\n     */\n    open(fn) {\n        debug(\"readyState %s\", this._readyState);\n        if (~this._readyState.indexOf(\"open\"))\n            return this;\n        debug(\"opening %s\", this.uri);\n        this.engine = new Engine(this.uri, this.opts);\n        const socket = this.engine;\n        const self = this;\n        this._readyState = \"opening\";\n        this.skipReconnect = false;\n        // emit `open`\n        const openSubDestroy = on(socket, \"open\", function () {\n            self.onopen();\n            fn && fn();\n        });\n        const onError = (err) => {\n            debug(\"error\");\n            this.cleanup();\n            this._readyState = \"closed\";\n            this.emitReserved(\"error\", err);\n            if (fn) {\n                fn(err);\n            }\n            else {\n                // Only do this if there is no fn to handle the error\n                this.maybeReconnectOnOpen();\n            }\n        };\n        // emit `error`\n        const errorSub = on(socket, \"error\", onError);\n        if (false !== this._timeout) {\n            const timeout = this._timeout;\n            debug(\"connect attempt will timeout after %d\", timeout);\n            // set timer\n            const timer = this.setTimeoutFn(() => {\n                debug(\"connect attempt timed out after %d\", timeout);\n                openSubDestroy();\n                onError(new Error(\"timeout\"));\n                socket.close();\n            }, timeout);\n            if (this.opts.autoUnref) {\n                timer.unref();\n            }\n            this.subs.push(() => {\n                this.clearTimeoutFn(timer);\n            });\n        }\n        this.subs.push(openSubDestroy);\n        this.subs.push(errorSub);\n        return this;\n    }\n    /**\n     * Alias for open()\n     *\n     * @return self\n     * @public\n     */\n    connect(fn) {\n        return this.open(fn);\n    }\n    /**\n     * Called upon transport open.\n     *\n     * @private\n     */\n    onopen() {\n        debug(\"open\");\n        // clear old subs\n        this.cleanup();\n        // mark as open\n        this._readyState = \"open\";\n        this.emitReserved(\"open\");\n        // add new subs\n        const socket = this.engine;\n        this.subs.push(on(socket, \"ping\", this.onping.bind(this)), on(socket, \"data\", this.ondata.bind(this)), on(socket, \"error\", this.onerror.bind(this)), on(socket, \"close\", this.onclose.bind(this)), \n        // @ts-ignore\n        on(this.decoder, \"decoded\", this.ondecoded.bind(this)));\n    }\n    /**\n     * Called upon a ping.\n     *\n     * @private\n     */\n    onping() {\n        this.emitReserved(\"ping\");\n    }\n    /**\n     * Called with data.\n     *\n     * @private\n     */\n    ondata(data) {\n        try {\n            this.decoder.add(data);\n        }\n        catch (e) {\n            this.onclose(\"parse error\", e);\n        }\n    }\n    /**\n     * Called when parser fully decodes a packet.\n     *\n     * @private\n     */\n    ondecoded(packet) {\n        // the nextTick call prevents an exception in a user-provided event listener from triggering a disconnection due to a \"parse error\"\n        nextTick(() => {\n            this.emitReserved(\"packet\", packet);\n        }, this.setTimeoutFn);\n    }\n    /**\n     * Called upon socket error.\n     *\n     * @private\n     */\n    onerror(err) {\n        debug(\"error\", err);\n        this.emitReserved(\"error\", err);\n    }\n    /**\n     * Creates a new socket for the given `nsp`.\n     *\n     * @return {Socket}\n     * @public\n     */\n    socket(nsp, opts) {\n        let socket = this.nsps[nsp];\n        if (!socket) {\n            socket = new Socket(this, nsp, opts);\n            this.nsps[nsp] = socket;\n        }\n        else if (this._autoConnect && !socket.active) {\n            socket.connect();\n        }\n        return socket;\n    }\n    /**\n     * Called upon a socket close.\n     *\n     * @param socket\n     * @private\n     */\n    _destroy(socket) {\n        const nsps = Object.keys(this.nsps);\n        for (const nsp of nsps) {\n            const socket = this.nsps[nsp];\n            if (socket.active) {\n                debug(\"socket %s is still active, skipping close\", nsp);\n                return;\n            }\n        }\n        this._close();\n    }\n    /**\n     * Writes a packet.\n     *\n     * @param packet\n     * @private\n     */\n    _packet(packet) {\n        debug(\"writing packet %j\", packet);\n        const encodedPackets = this.encoder.encode(packet);\n        for (let i = 0; i < encodedPackets.length; i++) {\n            this.engine.write(encodedPackets[i], packet.options);\n        }\n    }\n    /**\n     * Clean up transport subscriptions and packet buffer.\n     *\n     * @private\n     */\n    cleanup() {\n        debug(\"cleanup\");\n        this.subs.forEach((subDestroy) => subDestroy());\n        this.subs.length = 0;\n        this.decoder.destroy();\n    }\n    /**\n     * Close the current socket.\n     *\n     * @private\n     */\n    _close() {\n        debug(\"disconnect\");\n        this.skipReconnect = true;\n        this._reconnecting = false;\n        this.onclose(\"forced close\");\n    }\n    /**\n     * Alias for close()\n     *\n     * @private\n     */\n    disconnect() {\n        return this._close();\n    }\n    /**\n     * Called when:\n     *\n     * - the low-level engine is closed\n     * - the parser encountered a badly formatted packet\n     * - all sockets are disconnected\n     *\n     * @private\n     */\n    onclose(reason, description) {\n        var _a;\n        debug(\"closed due to %s\", reason);\n        this.cleanup();\n        (_a = this.engine) === null || _a === void 0 ? void 0 : _a.close();\n        this.backoff.reset();\n        this._readyState = \"closed\";\n        this.emitReserved(\"close\", reason, description);\n        if (this._reconnection && !this.skipReconnect) {\n            this.reconnect();\n        }\n    }\n    /**\n     * Attempt a reconnection.\n     *\n     * @private\n     */\n    reconnect() {\n        if (this._reconnecting || this.skipReconnect)\n            return this;\n        const self = this;\n        if (this.backoff.attempts >= this._reconnectionAttempts) {\n            debug(\"reconnect failed\");\n            this.backoff.reset();\n            this.emitReserved(\"reconnect_failed\");\n            this._reconnecting = false;\n        }\n        else {\n            const delay = this.backoff.duration();\n            debug(\"will wait %dms before reconnect attempt\", delay);\n            this._reconnecting = true;\n            const timer = this.setTimeoutFn(() => {\n                if (self.skipReconnect)\n                    return;\n                debug(\"attempting reconnect\");\n                this.emitReserved(\"reconnect_attempt\", self.backoff.attempts);\n                // check again for the case socket closed in above events\n                if (self.skipReconnect)\n                    return;\n                self.open((err) => {\n                    if (err) {\n                        debug(\"reconnect attempt error\");\n                        self._reconnecting = false;\n                        self.reconnect();\n                        this.emitReserved(\"reconnect_error\", err);\n                    }\n                    else {\n                        debug(\"reconnect success\");\n                        self.onreconnect();\n                    }\n                });\n            }, delay);\n            if (this.opts.autoUnref) {\n                timer.unref();\n            }\n            this.subs.push(() => {\n                this.clearTimeoutFn(timer);\n            });\n        }\n    }\n    /**\n     * Called upon successful reconnect.\n     *\n     * @private\n     */\n    onreconnect() {\n        const attempt = this.backoff.attempts;\n        this._reconnecting = false;\n        this.backoff.reset();\n        this.emitReserved(\"reconnect\", attempt);\n    }\n}\n"], "names": [], "mappings": ";;;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA,0TAAiC,UAAU;;;;;;;;AAC3C,MAAM,QAAQ,CAAA,GAAA,uLAAA,CAAA,UAAW,AAAD,EAAE,6BAA6B,UAAU;AAC1D,MAAM,gBAAgB,gQAAA,CAAA,UAAO;IAChC,YAAY,GAAG,EAAE,IAAI,CAAE;QACnB,IAAI;QACJ,KAAK;QACL,IAAI,CAAC,IAAI,GAAG,CAAC;QACb,IAAI,CAAC,IAAI,GAAG,EAAE;QACd,IAAI,OAAO,aAAa,OAAO,KAAK;YAChC,OAAO;YACP,MAAM;QACV;QACA,OAAO,QAAQ,CAAC;QAChB,KAAK,IAAI,GAAG,KAAK,IAAI,IAAI;QACzB,IAAI,CAAC,IAAI,GAAG;QACZ,CAAA,GAAA,0OAAA,CAAA,wBAAqB,AAAD,EAAE,IAAI,EAAE;QAC5B,IAAI,CAAC,YAAY,CAAC,KAAK,YAAY,KAAK;QACxC,IAAI,CAAC,oBAAoB,CAAC,KAAK,oBAAoB,IAAI;QACvD,IAAI,CAAC,iBAAiB,CAAC,KAAK,iBAAiB,IAAI;QACjD,IAAI,CAAC,oBAAoB,CAAC,KAAK,oBAAoB,IAAI;QACvD,IAAI,CAAC,mBAAmB,CAAC,CAAC,KAAK,KAAK,mBAAmB,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK;QAC1F,IAAI,CAAC,OAAO,GAAG,IAAI,uPAAA,CAAA,UAAO,CAAC;YACvB,KAAK,IAAI,CAAC,iBAAiB;YAC3B,KAAK,IAAI,CAAC,oBAAoB;YAC9B,QAAQ,IAAI,CAAC,mBAAmB;QACpC;QACA,IAAI,CAAC,OAAO,CAAC,QAAQ,KAAK,OAAO,GAAG,QAAQ,KAAK,OAAO;QACxD,IAAI,CAAC,WAAW,GAAG;QACnB,IAAI,CAAC,GAAG,GAAG;QACX,MAAM,UAAU,KAAK,MAAM,IAAI;QAC/B,IAAI,CAAC,OAAO,GAAG,IAAI,QAAQ,OAAO;QAClC,IAAI,CAAC,OAAO,GAAG,IAAI,QAAQ,OAAO;QAClC,IAAI,CAAC,YAAY,GAAG,KAAK,WAAW,KAAK;QACzC,IAAI,IAAI,CAAC,YAAY,EACjB,IAAI,CAAC,IAAI;IACjB;IACA,aAAa,CAAC,EAAE;QACZ,IAAI,CAAC,UAAU,MAAM,EACjB,OAAO,IAAI,CAAC,aAAa;QAC7B,IAAI,CAAC,aAAa,GAAG,CAAC,CAAC;QACvB,IAAI,CAAC,GAAG;YACJ,IAAI,CAAC,aAAa,GAAG;QACzB;QACA,OAAO,IAAI;IACf;IACA,qBAAqB,CAAC,EAAE;QACpB,IAAI,MAAM,WACN,OAAO,IAAI,CAAC,qBAAqB;QACrC,IAAI,CAAC,qBAAqB,GAAG;QAC7B,OAAO,IAAI;IACf;IACA,kBAAkB,CAAC,EAAE;QACjB,IAAI;QACJ,IAAI,MAAM,WACN,OAAO,IAAI,CAAC,kBAAkB;QAClC,IAAI,CAAC,kBAAkB,GAAG;QAC1B,CAAC,KAAK,IAAI,CAAC,OAAO,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,IAAI,GAAG,MAAM,CAAC;QACnE,OAAO,IAAI;IACf;IACA,oBAAoB,CAAC,EAAE;QACnB,IAAI;QACJ,IAAI,MAAM,WACN,OAAO,IAAI,CAAC,oBAAoB;QACpC,IAAI,CAAC,oBAAoB,GAAG;QAC5B,CAAC,KAAK,IAAI,CAAC,OAAO,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,IAAI,GAAG,SAAS,CAAC;QACtE,OAAO,IAAI;IACf;IACA,qBAAqB,CAAC,EAAE;QACpB,IAAI;QACJ,IAAI,MAAM,WACN,OAAO,IAAI,CAAC,qBAAqB;QACrC,IAAI,CAAC,qBAAqB,GAAG;QAC7B,CAAC,KAAK,IAAI,CAAC,OAAO,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,IAAI,GAAG,MAAM,CAAC;QACnE,OAAO,IAAI;IACf;IACA,QAAQ,CAAC,EAAE;QACP,IAAI,CAAC,UAAU,MAAM,EACjB,OAAO,IAAI,CAAC,QAAQ;QACxB,IAAI,CAAC,QAAQ,GAAG;QAChB,OAAO,IAAI;IACf;IACA;;;;;KAKC,GACD,uBAAuB;QACnB,gEAAgE;QAChE,IAAI,CAAC,IAAI,CAAC,aAAa,IACnB,IAAI,CAAC,aAAa,IAClB,IAAI,CAAC,OAAO,CAAC,QAAQ,KAAK,GAAG;YAC7B,sEAAsE;YACtE,IAAI,CAAC,SAAS;QAClB;IACJ;IACA;;;;;;KAMC,GACD,KAAK,EAAE,EAAE;QACL,MAAM,iBAAiB,IAAI,CAAC,WAAW;QACvC,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,SAC1B,OAAO,IAAI;QACf,MAAM,cAAc,IAAI,CAAC,GAAG;QAC5B,IAAI,CAAC,MAAM,GAAG,IAAI,4OAAA,CAAA,SAAM,CAAC,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,IAAI;QAC5C,MAAM,SAAS,IAAI,CAAC,MAAM;QAC1B,MAAM,OAAO,IAAI;QACjB,IAAI,CAAC,WAAW,GAAG;QACnB,IAAI,CAAC,aAAa,GAAG;QACrB,cAAc;QACd,MAAM,iBAAiB,CAAA,GAAA,wOAAA,CAAA,KAAE,AAAD,EAAE,QAAQ,QAAQ;YACtC,KAAK,MAAM;YACX,MAAM;QACV;QACA,MAAM,UAAU,CAAC;YACb,MAAM;YACN,IAAI,CAAC,OAAO;YACZ,IAAI,CAAC,WAAW,GAAG;YACnB,IAAI,CAAC,YAAY,CAAC,SAAS;YAC3B,IAAI,IAAI;gBACJ,GAAG;YACP,OACK;gBACD,qDAAqD;gBACrD,IAAI,CAAC,oBAAoB;YAC7B;QACJ;QACA,eAAe;QACf,MAAM,WAAW,CAAA,GAAA,wOAAA,CAAA,KAAE,AAAD,EAAE,QAAQ,SAAS;QACrC,IAAI,UAAU,IAAI,CAAC,QAAQ,EAAE;YACzB,MAAM,UAAU,IAAI,CAAC,QAAQ;YAC7B,MAAM,yCAAyC;YAC/C,YAAY;YACZ,MAAM,QAAQ,IAAI,CAAC,YAAY,CAAC;gBAC5B,MAAM,sCAAsC;gBAC5C;gBACA,QAAQ,IAAI,MAAM;gBAClB,OAAO,KAAK;YAChB,GAAG;YACH,IAAI,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;gBACrB,MAAM,KAAK;YACf;YACA,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC;gBACX,IAAI,CAAC,cAAc,CAAC;YACxB;QACJ;QACA,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC;QACf,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC;QACf,OAAO,IAAI;IACf;IACA;;;;;KAKC,GACD,QAAQ,EAAE,EAAE;QACR,OAAO,IAAI,CAAC,IAAI,CAAC;IACrB;IACA;;;;KAIC,GACD,SAAS;QACL,MAAM;QACN,iBAAiB;QACjB,IAAI,CAAC,OAAO;QACZ,eAAe;QACf,IAAI,CAAC,WAAW,GAAG;QACnB,IAAI,CAAC,YAAY,CAAC;QAClB,eAAe;QACf,MAAM,SAAS,IAAI,CAAC,MAAM;QAC1B,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA,GAAA,wOAAA,CAAA,KAAE,AAAD,EAAE,QAAQ,QAAQ,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,IAAI,CAAA,GAAA,wOAAA,CAAA,KAAE,AAAD,EAAE,QAAQ,QAAQ,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,IAAI,CAAA,GAAA,wOAAA,CAAA,KAAE,AAAD,EAAE,QAAQ,SAAS,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,IAAI,CAAA,GAAA,wOAAA,CAAA,KAAE,AAAD,EAAE,QAAQ,SAAS,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,IAC/L,aAAa;QACb,CAAA,GAAA,wOAAA,CAAA,KAAE,AAAD,EAAE,IAAI,CAAC,OAAO,EAAE,WAAW,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI;IACxD;IACA;;;;KAIC,GACD,SAAS;QACL,IAAI,CAAC,YAAY,CAAC;IACtB;IACA;;;;KAIC,GACD,OAAO,IAAI,EAAE;QACT,IAAI;YACA,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC;QACrB,EACA,OAAO,GAAG;YACN,IAAI,CAAC,OAAO,CAAC,eAAe;QAChC;IACJ;IACA;;;;KAIC,GACD,UAAU,MAAM,EAAE;QACd,mIAAmI;QACnI,CAAA,GAAA,qPAAA,CAAA,WAAQ,AAAD,EAAE;YACL,IAAI,CAAC,YAAY,CAAC,UAAU;QAChC,GAAG,IAAI,CAAC,YAAY;IACxB;IACA;;;;KAIC,GACD,QAAQ,GAAG,EAAE;QACT,MAAM,SAAS;QACf,IAAI,CAAC,YAAY,CAAC,SAAS;IAC/B;IACA;;;;;KAKC,GACD,OAAO,GAAG,EAAE,IAAI,EAAE;QACd,IAAI,SAAS,IAAI,CAAC,IAAI,CAAC,IAAI;QAC3B,IAAI,CAAC,QAAQ;YACT,SAAS,IAAI,4OAAA,CAAA,SAAM,CAAC,IAAI,EAAE,KAAK;YAC/B,IAAI,CAAC,IAAI,CAAC,IAAI,GAAG;QACrB,OACK,IAAI,IAAI,CAAC,YAAY,IAAI,CAAC,OAAO,MAAM,EAAE;YAC1C,OAAO,OAAO;QAClB;QACA,OAAO;IACX;IACA;;;;;KAKC,GACD,SAAS,MAAM,EAAE;QACb,MAAM,OAAO,OAAO,IAAI,CAAC,IAAI,CAAC,IAAI;QAClC,KAAK,MAAM,OAAO,KAAM;YACpB,MAAM,SAAS,IAAI,CAAC,IAAI,CAAC,IAAI;YAC7B,IAAI,OAAO,MAAM,EAAE;gBACf,MAAM,6CAA6C;gBACnD;YACJ;QACJ;QACA,IAAI,CAAC,MAAM;IACf;IACA;;;;;KAKC,GACD,QAAQ,MAAM,EAAE;QACZ,MAAM,qBAAqB;QAC3B,MAAM,iBAAiB,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC;QAC3C,IAAK,IAAI,IAAI,GAAG,IAAI,eAAe,MAAM,EAAE,IAAK;YAC5C,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,cAAc,CAAC,EAAE,EAAE,OAAO,OAAO;QACvD;IACJ;IACA;;;;KAIC,GACD,UAAU;QACN,MAAM;QACN,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,aAAe;QAClC,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG;QACnB,IAAI,CAAC,OAAO,CAAC,OAAO;IACxB;IACA;;;;KAIC,GACD,SAAS;QACL,MAAM;QACN,IAAI,CAAC,aAAa,GAAG;QACrB,IAAI,CAAC,aAAa,GAAG;QACrB,IAAI,CAAC,OAAO,CAAC;IACjB;IACA;;;;KAIC,GACD,aAAa;QACT,OAAO,IAAI,CAAC,MAAM;IACtB;IACA;;;;;;;;KAQC,GACD,QAAQ,MAAM,EAAE,WAAW,EAAE;QACzB,IAAI;QACJ,MAAM,oBAAoB;QAC1B,IAAI,CAAC,OAAO;QACZ,CAAC,KAAK,IAAI,CAAC,MAAM,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,IAAI,GAAG,KAAK;QAChE,IAAI,CAAC,OAAO,CAAC,KAAK;QAClB,IAAI,CAAC,WAAW,GAAG;QACnB,IAAI,CAAC,YAAY,CAAC,SAAS,QAAQ;QACnC,IAAI,IAAI,CAAC,aAAa,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE;YAC3C,IAAI,CAAC,SAAS;QAClB;IACJ;IACA;;;;KAIC,GACD,YAAY;QACR,IAAI,IAAI,CAAC,aAAa,IAAI,IAAI,CAAC,aAAa,EACxC,OAAO,IAAI;QACf,MAAM,OAAO,IAAI;QACjB,IAAI,IAAI,CAAC,OAAO,CAAC,QAAQ,IAAI,IAAI,CAAC,qBAAqB,EAAE;YACrD,MAAM;YACN,IAAI,CAAC,OAAO,CAAC,KAAK;YAClB,IAAI,CAAC,YAAY,CAAC;YAClB,IAAI,CAAC,aAAa,GAAG;QACzB,OACK;YACD,MAAM,QAAQ,IAAI,CAAC,OAAO,CAAC,QAAQ;YACnC,MAAM,2CAA2C;YACjD,IAAI,CAAC,aAAa,GAAG;YACrB,MAAM,QAAQ,IAAI,CAAC,YAAY,CAAC;gBAC5B,IAAI,KAAK,aAAa,EAClB;gBACJ,MAAM;gBACN,IAAI,CAAC,YAAY,CAAC,qBAAqB,KAAK,OAAO,CAAC,QAAQ;gBAC5D,yDAAyD;gBACzD,IAAI,KAAK,aAAa,EAClB;gBACJ,KAAK,IAAI,CAAC,CAAC;oBACP,IAAI,KAAK;wBACL,MAAM;wBACN,KAAK,aAAa,GAAG;wBACrB,KAAK,SAAS;wBACd,IAAI,CAAC,YAAY,CAAC,mBAAmB;oBACzC,OACK;wBACD,MAAM;wBACN,KAAK,WAAW;oBACpB;gBACJ;YACJ,GAAG;YACH,IAAI,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;gBACrB,MAAM,KAAK;YACf;YACA,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC;gBACX,IAAI,CAAC,cAAc,CAAC;YACxB;QACJ;IACJ;IACA;;;;KAIC,GACD,cAAc;QACV,MAAM,UAAU,IAAI,CAAC,OAAO,CAAC,QAAQ;QACrC,IAAI,CAAC,aAAa,GAAG;QACrB,IAAI,CAAC,OAAO,CAAC,KAAK;QAClB,IAAI,CAAC,YAAY,CAAC,aAAa;IACnC;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5196, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/bloomgoo/gold-exchange-platform/frontend/node_modules/.pnpm/socket.io-client%404.8.1/node_modules/socket.io-client/build/esm-debug/index.js"], "sourcesContent": ["import { url } from \"./url.js\";\nimport { Manager } from \"./manager.js\";\nimport { Socket } from \"./socket.js\";\nimport debugModule from \"debug\"; // debug()\nconst debug = debugModule(\"socket.io-client\"); // debug()\n/**\n * Managers cache.\n */\nconst cache = {};\nfunction lookup(uri, opts) {\n    if (typeof uri === \"object\") {\n        opts = uri;\n        uri = undefined;\n    }\n    opts = opts || {};\n    const parsed = url(uri, opts.path || \"/socket.io\");\n    const source = parsed.source;\n    const id = parsed.id;\n    const path = parsed.path;\n    const sameNamespace = cache[id] && path in cache[id][\"nsps\"];\n    const newConnection = opts.forceNew ||\n        opts[\"force new connection\"] ||\n        false === opts.multiplex ||\n        sameNamespace;\n    let io;\n    if (newConnection) {\n        debug(\"ignoring socket cache for %s\", source);\n        io = new Manager(source, opts);\n    }\n    else {\n        if (!cache[id]) {\n            debug(\"new io instance for %s\", source);\n            cache[id] = new Manager(source, opts);\n        }\n        io = cache[id];\n    }\n    if (parsed.query && !opts.query) {\n        opts.query = parsed.queryKey;\n    }\n    return io.socket(parsed.path, opts);\n}\n// so that \"lookup\" can be used both as a function (e.g. `io(...)`) and as a\n// namespace (e.g. `io.connect(...)`), for backward compatibility\nObject.assign(lookup, {\n    Manager,\n    Socket,\n    io: lookup,\n    connect: lookup,\n});\n/**\n * Protocol version.\n *\n * @public\n */\nexport { protocol } from \"socket.io-parser\";\n/**\n * Expose constructors for standalone build.\n *\n * @public\n */\nexport { Manager, Socket, lookup as io, lookup as connect, lookup as default, };\nexport { Fetch, NodeXHR, XHR, NodeWebSocket, WebSocket, WebTransport, } from \"engine.io-client\";\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AACA,0TAAiC,UAAU;AA8C3C;;;;CAIC,GACD;AAOA;;;;;AAzDA,MAAM,QAAQ,CAAA,GAAA,uLAAA,CAAA,UAAW,AAAD,EAAE,qBAAqB,UAAU;AACzD;;CAEC,GACD,MAAM,QAAQ,CAAC;AACf,SAAS,OAAO,GAAG,EAAE,IAAI;IACrB,IAAI,OAAO,QAAQ,UAAU;QACzB,OAAO;QACP,MAAM;IACV;IACA,OAAO,QAAQ,CAAC;IAChB,MAAM,SAAS,CAAA,GAAA,yOAAA,CAAA,MAAG,AAAD,EAAE,KAAK,KAAK,IAAI,IAAI;IACrC,MAAM,SAAS,OAAO,MAAM;IAC5B,MAAM,KAAK,OAAO,EAAE;IACpB,MAAM,OAAO,OAAO,IAAI;IACxB,MAAM,gBAAgB,KAAK,CAAC,GAAG,IAAI,QAAQ,KAAK,CAAC,GAAG,CAAC,OAAO;IAC5D,MAAM,gBAAgB,KAAK,QAAQ,IAC/B,IAAI,CAAC,uBAAuB,IAC5B,UAAU,KAAK,SAAS,IACxB;IACJ,IAAI;IACJ,IAAI,eAAe;QACf,MAAM,gCAAgC;QACtC,KAAK,IAAI,6OAAA,CAAA,UAAO,CAAC,QAAQ;IAC7B,OACK;QACD,IAAI,CAAC,KAAK,CAAC,GAAG,EAAE;YACZ,MAAM,0BAA0B;YAChC,KAAK,CAAC,GAAG,GAAG,IAAI,6OAAA,CAAA,UAAO,CAAC,QAAQ;QACpC;QACA,KAAK,KAAK,CAAC,GAAG;IAClB;IACA,IAAI,OAAO,KAAK,IAAI,CAAC,KAAK,KAAK,EAAE;QAC7B,KAAK,KAAK,GAAG,OAAO,QAAQ;IAChC;IACA,OAAO,GAAG,MAAM,CAAC,OAAO,IAAI,EAAE;AAClC;AACA,4EAA4E;AAC5E,iEAAiE;AACjE,OAAO,MAAM,CAAC,QAAQ;IAClB,SAAA,6OAAA,CAAA,UAAO;IACP,QAAA,4OAAA,CAAA,SAAM;IACN,IAAI;IACJ,SAAS;AACb", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5278, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/bloomgoo/gold-exchange-platform/frontend/node_modules/.pnpm/socket.io-parser%404.2.4/node_modules/socket.io-parser/build/esm-debug/is-binary.js"], "sourcesContent": ["const withNativeArrayBuffer = typeof ArrayBuffer === \"function\";\nconst isView = (obj) => {\n    return typeof ArrayBuffer.isView === \"function\"\n        ? ArrayBuffer.isView(obj)\n        : obj.buffer instanceof ArrayBuffer;\n};\nconst toString = Object.prototype.toString;\nconst withNativeBlob = typeof Blob === \"function\" ||\n    (typeof Blob !== \"undefined\" &&\n        toString.call(Blob) === \"[object BlobConstructor]\");\nconst withNativeFile = typeof File === \"function\" ||\n    (typeof File !== \"undefined\" &&\n        toString.call(File) === \"[object FileConstructor]\");\n/**\n * Returns true if obj is a Buffer, an ArrayBuffer, a Blob or a File.\n *\n * @private\n */\nexport function isBinary(obj) {\n    return ((withNativeArrayBuffer && (obj instanceof ArrayBuffer || isView(obj))) ||\n        (withNativeBlob && obj instanceof Blob) ||\n        (withNativeFile && obj instanceof File));\n}\nexport function hasBinary(obj, toJSON) {\n    if (!obj || typeof obj !== \"object\") {\n        return false;\n    }\n    if (Array.isArray(obj)) {\n        for (let i = 0, l = obj.length; i < l; i++) {\n            if (hasBinary(obj[i])) {\n                return true;\n            }\n        }\n        return false;\n    }\n    if (isBinary(obj)) {\n        return true;\n    }\n    if (obj.toJSON &&\n        typeof obj.toJSON === \"function\" &&\n        arguments.length === 1) {\n        return hasBinary(obj.toJSON(), true);\n    }\n    for (const key in obj) {\n        if (Object.prototype.hasOwnProperty.call(obj, key) && hasBinary(obj[key])) {\n            return true;\n        }\n    }\n    return false;\n}\n"], "names": [], "mappings": ";;;;AAAA,MAAM,wBAAwB,OAAO,gBAAgB;AACrD,MAAM,SAAS,CAAC;IACZ,OAAO,OAAO,YAAY,MAAM,KAAK,aAC/B,YAAY,MAAM,CAAC,OACnB,IAAI,MAAM,YAAY;AAChC;AACA,MAAM,WAAW,OAAO,SAAS,CAAC,QAAQ;AAC1C,MAAM,iBAAiB,OAAO,SAAS,cAClC,OAAO,SAAS,eACb,SAAS,IAAI,CAAC,UAAU;AAChC,MAAM,iBAAiB,OAAO,SAAS,cAClC,OAAO,SAAS,eACb,SAAS,IAAI,CAAC,UAAU;AAMzB,SAAS,SAAS,GAAG;IACxB,OAAQ,AAAC,yBAAyB,CAAC,eAAe,eAAe,OAAO,IAAI,KACvE,kBAAkB,eAAe,QACjC,kBAAkB,eAAe;AAC1C;AACO,SAAS,UAAU,GAAG,EAAE,MAAM;IACjC,IAAI,CAAC,OAAO,OAAO,QAAQ,UAAU;QACjC,OAAO;IACX;IACA,IAAI,MAAM,OAAO,CAAC,MAAM;QACpB,IAAK,IAAI,IAAI,GAAG,IAAI,IAAI,MAAM,EAAE,IAAI,GAAG,IAAK;YACxC,IAAI,UAAU,GAAG,CAAC,EAAE,GAAG;gBACnB,OAAO;YACX;QACJ;QACA,OAAO;IACX;IACA,IAAI,SAAS,MAAM;QACf,OAAO;IACX;IACA,IAAI,IAAI,MAAM,IACV,OAAO,IAAI,MAAM,KAAK,cACtB,UAAU,MAAM,KAAK,GAAG;QACxB,OAAO,UAAU,IAAI,MAAM,IAAI;IACnC;IACA,IAAK,MAAM,OAAO,IAAK;QACnB,IAAI,OAAO,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,KAAK,QAAQ,UAAU,GAAG,CAAC,IAAI,GAAG;YACvE,OAAO;QACX;IACJ;IACA,OAAO;AACX", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5323, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/bloomgoo/gold-exchange-platform/frontend/node_modules/.pnpm/socket.io-parser%404.2.4/node_modules/socket.io-parser/build/esm-debug/binary.js"], "sourcesContent": ["import { isBinary } from \"./is-binary.js\";\n/**\n * Replaces every Buffer | ArrayBuffer | Blob | File in packet with a numbered placeholder.\n *\n * @param {Object} packet - socket.io event packet\n * @return {Object} with deconstructed packet and list of buffers\n * @public\n */\nexport function deconstructPacket(packet) {\n    const buffers = [];\n    const packetData = packet.data;\n    const pack = packet;\n    pack.data = _deconstructPacket(packetData, buffers);\n    pack.attachments = buffers.length; // number of binary 'attachments'\n    return { packet: pack, buffers: buffers };\n}\nfunction _deconstructPacket(data, buffers) {\n    if (!data)\n        return data;\n    if (isBinary(data)) {\n        const placeholder = { _placeholder: true, num: buffers.length };\n        buffers.push(data);\n        return placeholder;\n    }\n    else if (Array.isArray(data)) {\n        const newData = new Array(data.length);\n        for (let i = 0; i < data.length; i++) {\n            newData[i] = _deconstructPacket(data[i], buffers);\n        }\n        return newData;\n    }\n    else if (typeof data === \"object\" && !(data instanceof Date)) {\n        const newData = {};\n        for (const key in data) {\n            if (Object.prototype.hasOwnProperty.call(data, key)) {\n                newData[key] = _deconstructPacket(data[key], buffers);\n            }\n        }\n        return newData;\n    }\n    return data;\n}\n/**\n * Reconstructs a binary packet from its placeholder packet and buffers\n *\n * @param {Object} packet - event packet with placeholders\n * @param {Array} buffers - binary buffers to put in placeholder positions\n * @return {Object} reconstructed packet\n * @public\n */\nexport function reconstructPacket(packet, buffers) {\n    packet.data = _reconstructPacket(packet.data, buffers);\n    delete packet.attachments; // no longer useful\n    return packet;\n}\nfunction _reconstructPacket(data, buffers) {\n    if (!data)\n        return data;\n    if (data && data._placeholder === true) {\n        const isIndexValid = typeof data.num === \"number\" &&\n            data.num >= 0 &&\n            data.num < buffers.length;\n        if (isIndexValid) {\n            return buffers[data.num]; // appropriate buffer (should be natural order anyway)\n        }\n        else {\n            throw new Error(\"illegal attachments\");\n        }\n    }\n    else if (Array.isArray(data)) {\n        for (let i = 0; i < data.length; i++) {\n            data[i] = _reconstructPacket(data[i], buffers);\n        }\n    }\n    else if (typeof data === \"object\") {\n        for (const key in data) {\n            if (Object.prototype.hasOwnProperty.call(data, key)) {\n                data[key] = _reconstructPacket(data[key], buffers);\n            }\n        }\n    }\n    return data;\n}\n"], "names": [], "mappings": ";;;;AAAA;;AAQO,SAAS,kBAAkB,MAAM;IACpC,MAAM,UAAU,EAAE;IAClB,MAAM,aAAa,OAAO,IAAI;IAC9B,MAAM,OAAO;IACb,KAAK,IAAI,GAAG,mBAAmB,YAAY;IAC3C,KAAK,WAAW,GAAG,QAAQ,MAAM,EAAE,iCAAiC;IACpE,OAAO;QAAE,QAAQ;QAAM,SAAS;IAAQ;AAC5C;AACA,SAAS,mBAAmB,IAAI,EAAE,OAAO;IACrC,IAAI,CAAC,MACD,OAAO;IACX,IAAI,CAAA,GAAA,kPAAA,CAAA,WAAQ,AAAD,EAAE,OAAO;QAChB,MAAM,cAAc;YAAE,cAAc;YAAM,KAAK,QAAQ,MAAM;QAAC;QAC9D,QAAQ,IAAI,CAAC;QACb,OAAO;IACX,OACK,IAAI,MAAM,OAAO,CAAC,OAAO;QAC1B,MAAM,UAAU,IAAI,MAAM,KAAK,MAAM;QACrC,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,MAAM,EAAE,IAAK;YAClC,OAAO,CAAC,EAAE,GAAG,mBAAmB,IAAI,CAAC,EAAE,EAAE;QAC7C;QACA,OAAO;IACX,OACK,IAAI,OAAO,SAAS,YAAY,CAAC,CAAC,gBAAgB,IAAI,GAAG;QAC1D,MAAM,UAAU,CAAC;QACjB,IAAK,MAAM,OAAO,KAAM;YACpB,IAAI,OAAO,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,MAAM,MAAM;gBACjD,OAAO,CAAC,IAAI,GAAG,mBAAmB,IAAI,CAAC,IAAI,EAAE;YACjD;QACJ;QACA,OAAO;IACX;IACA,OAAO;AACX;AASO,SAAS,kBAAkB,MAAM,EAAE,OAAO;IAC7C,OAAO,IAAI,GAAG,mBAAmB,OAAO,IAAI,EAAE;IAC9C,OAAO,OAAO,WAAW,EAAE,mBAAmB;IAC9C,OAAO;AACX;AACA,SAAS,mBAAmB,IAAI,EAAE,OAAO;IACrC,IAAI,CAAC,MACD,OAAO;IACX,IAAI,QAAQ,KAAK,YAAY,KAAK,MAAM;QACpC,MAAM,eAAe,OAAO,KAAK,GAAG,KAAK,YACrC,KAAK,GAAG,IAAI,KACZ,KAAK,GAAG,GAAG,QAAQ,MAAM;QAC7B,IAAI,cAAc;YACd,OAAO,OAAO,CAAC,KAAK,GAAG,CAAC,EAAE,sDAAsD;QACpF,OACK;YACD,MAAM,IAAI,MAAM;QACpB;IACJ,OACK,IAAI,MAAM,OAAO,CAAC,OAAO;QAC1B,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,MAAM,EAAE,IAAK;YAClC,IAAI,CAAC,EAAE,GAAG,mBAAmB,IAAI,CAAC,EAAE,EAAE;QAC1C;IACJ,OACK,IAAI,OAAO,SAAS,UAAU;QAC/B,IAAK,MAAM,OAAO,KAAM;YACpB,IAAI,OAAO,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,MAAM,MAAM;gBACjD,IAAI,CAAC,IAAI,GAAG,mBAAmB,IAAI,CAAC,IAAI,EAAE;YAC9C;QACJ;IACJ;IACA,OAAO;AACX", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5399, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/bloomgoo/gold-exchange-platform/frontend/node_modules/.pnpm/socket.io-parser%404.2.4/node_modules/socket.io-parser/build/esm-debug/index.js"], "sourcesContent": ["import { Emitter } from \"@socket.io/component-emitter\";\nimport { deconstructPacket, reconstructPacket } from \"./binary.js\";\nimport { isBinary, hasBinary } from \"./is-binary.js\";\nimport debugModule from \"debug\"; // debug()\nconst debug = debugModule(\"socket.io-parser\"); // debug()\n/**\n * These strings must not be used as event names, as they have a special meaning.\n */\nconst RESERVED_EVENTS = [\n    \"connect\",\n    \"connect_error\",\n    \"disconnect\",\n    \"disconnecting\",\n    \"newListener\",\n    \"removeListener\", // used by the Node.js EventEmitter\n];\n/**\n * Protocol version.\n *\n * @public\n */\nexport const protocol = 5;\nexport var PacketType;\n(function (PacketType) {\n    PacketType[PacketType[\"CONNECT\"] = 0] = \"CONNECT\";\n    PacketType[PacketType[\"DISCONNECT\"] = 1] = \"DISCONNECT\";\n    PacketType[PacketType[\"EVENT\"] = 2] = \"EVENT\";\n    PacketType[PacketType[\"ACK\"] = 3] = \"ACK\";\n    PacketType[PacketType[\"CONNECT_ERROR\"] = 4] = \"CONNECT_ERROR\";\n    PacketType[PacketType[\"BINARY_EVENT\"] = 5] = \"BINARY_EVENT\";\n    PacketType[PacketType[\"BINARY_ACK\"] = 6] = \"BINARY_ACK\";\n})(PacketType || (PacketType = {}));\n/**\n * A socket.io Encoder instance\n */\nexport class Encoder {\n    /**\n     * Encoder constructor\n     *\n     * @param {function} replacer - custom replacer to pass down to JSON.parse\n     */\n    constructor(replacer) {\n        this.replacer = replacer;\n    }\n    /**\n     * Encode a packet as a single string if non-binary, or as a\n     * buffer sequence, depending on packet type.\n     *\n     * @param {Object} obj - packet object\n     */\n    encode(obj) {\n        debug(\"encoding packet %j\", obj);\n        if (obj.type === PacketType.EVENT || obj.type === PacketType.ACK) {\n            if (hasBinary(obj)) {\n                return this.encodeAsBinary({\n                    type: obj.type === PacketType.EVENT\n                        ? PacketType.BINARY_EVENT\n                        : PacketType.BINARY_ACK,\n                    nsp: obj.nsp,\n                    data: obj.data,\n                    id: obj.id,\n                });\n            }\n        }\n        return [this.encodeAsString(obj)];\n    }\n    /**\n     * Encode packet as string.\n     */\n    encodeAsString(obj) {\n        // first is type\n        let str = \"\" + obj.type;\n        // attachments if we have them\n        if (obj.type === PacketType.BINARY_EVENT ||\n            obj.type === PacketType.BINARY_ACK) {\n            str += obj.attachments + \"-\";\n        }\n        // if we have a namespace other than `/`\n        // we append it followed by a comma `,`\n        if (obj.nsp && \"/\" !== obj.nsp) {\n            str += obj.nsp + \",\";\n        }\n        // immediately followed by the id\n        if (null != obj.id) {\n            str += obj.id;\n        }\n        // json data\n        if (null != obj.data) {\n            str += JSON.stringify(obj.data, this.replacer);\n        }\n        debug(\"encoded %j as %s\", obj, str);\n        return str;\n    }\n    /**\n     * Encode packet as 'buffer sequence' by removing blobs, and\n     * deconstructing packet into object with placeholders and\n     * a list of buffers.\n     */\n    encodeAsBinary(obj) {\n        const deconstruction = deconstructPacket(obj);\n        const pack = this.encodeAsString(deconstruction.packet);\n        const buffers = deconstruction.buffers;\n        buffers.unshift(pack); // add packet info to beginning of data list\n        return buffers; // write all the buffers\n    }\n}\n// see https://stackoverflow.com/questions/8511281/check-if-a-value-is-an-object-in-javascript\nfunction isObject(value) {\n    return Object.prototype.toString.call(value) === \"[object Object]\";\n}\n/**\n * A socket.io Decoder instance\n *\n * @return {Object} decoder\n */\nexport class Decoder extends Emitter {\n    /**\n     * Decoder constructor\n     *\n     * @param {function} reviver - custom reviver to pass down to JSON.stringify\n     */\n    constructor(reviver) {\n        super();\n        this.reviver = reviver;\n    }\n    /**\n     * Decodes an encoded packet string into packet JSON.\n     *\n     * @param {String} obj - encoded packet\n     */\n    add(obj) {\n        let packet;\n        if (typeof obj === \"string\") {\n            if (this.reconstructor) {\n                throw new Error(\"got plaintext data when reconstructing a packet\");\n            }\n            packet = this.decodeString(obj);\n            const isBinaryEvent = packet.type === PacketType.BINARY_EVENT;\n            if (isBinaryEvent || packet.type === PacketType.BINARY_ACK) {\n                packet.type = isBinaryEvent ? PacketType.EVENT : PacketType.ACK;\n                // binary packet's json\n                this.reconstructor = new BinaryReconstructor(packet);\n                // no attachments, labeled binary but no binary data to follow\n                if (packet.attachments === 0) {\n                    super.emitReserved(\"decoded\", packet);\n                }\n            }\n            else {\n                // non-binary full packet\n                super.emitReserved(\"decoded\", packet);\n            }\n        }\n        else if (isBinary(obj) || obj.base64) {\n            // raw binary data\n            if (!this.reconstructor) {\n                throw new Error(\"got binary data when not reconstructing a packet\");\n            }\n            else {\n                packet = this.reconstructor.takeBinaryData(obj);\n                if (packet) {\n                    // received final buffer\n                    this.reconstructor = null;\n                    super.emitReserved(\"decoded\", packet);\n                }\n            }\n        }\n        else {\n            throw new Error(\"Unknown type: \" + obj);\n        }\n    }\n    /**\n     * Decode a packet String (JSON data)\n     *\n     * @param {String} str\n     * @return {Object} packet\n     */\n    decodeString(str) {\n        let i = 0;\n        // look up type\n        const p = {\n            type: Number(str.charAt(0)),\n        };\n        if (PacketType[p.type] === undefined) {\n            throw new Error(\"unknown packet type \" + p.type);\n        }\n        // look up attachments if type binary\n        if (p.type === PacketType.BINARY_EVENT ||\n            p.type === PacketType.BINARY_ACK) {\n            const start = i + 1;\n            while (str.charAt(++i) !== \"-\" && i != str.length) { }\n            const buf = str.substring(start, i);\n            if (buf != Number(buf) || str.charAt(i) !== \"-\") {\n                throw new Error(\"Illegal attachments\");\n            }\n            p.attachments = Number(buf);\n        }\n        // look up namespace (if any)\n        if (\"/\" === str.charAt(i + 1)) {\n            const start = i + 1;\n            while (++i) {\n                const c = str.charAt(i);\n                if (\",\" === c)\n                    break;\n                if (i === str.length)\n                    break;\n            }\n            p.nsp = str.substring(start, i);\n        }\n        else {\n            p.nsp = \"/\";\n        }\n        // look up id\n        const next = str.charAt(i + 1);\n        if (\"\" !== next && Number(next) == next) {\n            const start = i + 1;\n            while (++i) {\n                const c = str.charAt(i);\n                if (null == c || Number(c) != c) {\n                    --i;\n                    break;\n                }\n                if (i === str.length)\n                    break;\n            }\n            p.id = Number(str.substring(start, i + 1));\n        }\n        // look up json data\n        if (str.charAt(++i)) {\n            const payload = this.tryParse(str.substr(i));\n            if (Decoder.isPayloadValid(p.type, payload)) {\n                p.data = payload;\n            }\n            else {\n                throw new Error(\"invalid payload\");\n            }\n        }\n        debug(\"decoded %s as %j\", str, p);\n        return p;\n    }\n    tryParse(str) {\n        try {\n            return JSON.parse(str, this.reviver);\n        }\n        catch (e) {\n            return false;\n        }\n    }\n    static isPayloadValid(type, payload) {\n        switch (type) {\n            case PacketType.CONNECT:\n                return isObject(payload);\n            case PacketType.DISCONNECT:\n                return payload === undefined;\n            case PacketType.CONNECT_ERROR:\n                return typeof payload === \"string\" || isObject(payload);\n            case PacketType.EVENT:\n            case PacketType.BINARY_EVENT:\n                return (Array.isArray(payload) &&\n                    (typeof payload[0] === \"number\" ||\n                        (typeof payload[0] === \"string\" &&\n                            RESERVED_EVENTS.indexOf(payload[0]) === -1)));\n            case PacketType.ACK:\n            case PacketType.BINARY_ACK:\n                return Array.isArray(payload);\n        }\n    }\n    /**\n     * Deallocates a parser's resources\n     */\n    destroy() {\n        if (this.reconstructor) {\n            this.reconstructor.finishedReconstruction();\n            this.reconstructor = null;\n        }\n    }\n}\n/**\n * A manager of a binary event's 'buffer sequence'. Should\n * be constructed whenever a packet of type BINARY_EVENT is\n * decoded.\n *\n * @param {Object} packet\n * @return {BinaryReconstructor} initialized reconstructor\n */\nclass BinaryReconstructor {\n    constructor(packet) {\n        this.packet = packet;\n        this.buffers = [];\n        this.reconPack = packet;\n    }\n    /**\n     * Method to be called when binary data received from connection\n     * after a BINARY_EVENT packet.\n     *\n     * @param {Buffer | ArrayBuffer} binData - the raw binary data received\n     * @return {null | Object} returns null if more binary data is expected or\n     *   a reconstructed packet object if all buffers have been received.\n     */\n    takeBinaryData(binData) {\n        this.buffers.push(binData);\n        if (this.buffers.length === this.reconPack.attachments) {\n            // done with buffer list\n            const packet = reconstructPacket(this.reconPack, this.buffers);\n            this.finishedReconstruction();\n            return packet;\n        }\n        return null;\n    }\n    /**\n     * Cleans up binary packet reconstruction variables.\n     */\n    finishedReconstruction() {\n        this.reconPack = null;\n        this.buffers = [];\n    }\n}\n"], "names": [], "mappings": ";;;;;;AAAA;AACA;AACA;AACA,0TAAiC,UAAU;;;;;AAC3C,MAAM,QAAQ,CAAA,GAAA,uLAAA,CAAA,UAAW,AAAD,EAAE,qBAAqB,UAAU;AACzD;;CAEC,GACD,MAAM,kBAAkB;IACpB;IACA;IACA;IACA;IACA;IACA;CACH;AAMM,MAAM,WAAW;AACjB,IAAI;AACX,CAAC,SAAU,UAAU;IACjB,UAAU,CAAC,UAAU,CAAC,UAAU,GAAG,EAAE,GAAG;IACxC,UAAU,CAAC,UAAU,CAAC,aAAa,GAAG,EAAE,GAAG;IAC3C,UAAU,CAAC,UAAU,CAAC,QAAQ,GAAG,EAAE,GAAG;IACtC,UAAU,CAAC,UAAU,CAAC,MAAM,GAAG,EAAE,GAAG;IACpC,UAAU,CAAC,UAAU,CAAC,gBAAgB,GAAG,EAAE,GAAG;IAC9C,UAAU,CAAC,UAAU,CAAC,eAAe,GAAG,EAAE,GAAG;IAC7C,UAAU,CAAC,UAAU,CAAC,aAAa,GAAG,EAAE,GAAG;AAC/C,CAAC,EAAE,cAAc,CAAC,aAAa,CAAC,CAAC;AAI1B,MAAM;IACT;;;;KAIC,GACD,YAAY,QAAQ,CAAE;QAClB,IAAI,CAAC,QAAQ,GAAG;IACpB;IACA;;;;;KAKC,GACD,OAAO,GAAG,EAAE;QACR,MAAM,sBAAsB;QAC5B,IAAI,IAAI,IAAI,KAAK,WAAW,KAAK,IAAI,IAAI,IAAI,KAAK,WAAW,GAAG,EAAE;YAC9D,IAAI,CAAA,GAAA,kPAAA,CAAA,YAAS,AAAD,EAAE,MAAM;gBAChB,OAAO,IAAI,CAAC,cAAc,CAAC;oBACvB,MAAM,IAAI,IAAI,KAAK,WAAW,KAAK,GAC7B,WAAW,YAAY,GACvB,WAAW,UAAU;oBAC3B,KAAK,IAAI,GAAG;oBACZ,MAAM,IAAI,IAAI;oBACd,IAAI,IAAI,EAAE;gBACd;YACJ;QACJ;QACA,OAAO;YAAC,IAAI,CAAC,cAAc,CAAC;SAAK;IACrC;IACA;;KAEC,GACD,eAAe,GAAG,EAAE;QAChB,gBAAgB;QAChB,IAAI,MAAM,KAAK,IAAI,IAAI;QACvB,8BAA8B;QAC9B,IAAI,IAAI,IAAI,KAAK,WAAW,YAAY,IACpC,IAAI,IAAI,KAAK,WAAW,UAAU,EAAE;YACpC,OAAO,IAAI,WAAW,GAAG;QAC7B;QACA,wCAAwC;QACxC,uCAAuC;QACvC,IAAI,IAAI,GAAG,IAAI,QAAQ,IAAI,GAAG,EAAE;YAC5B,OAAO,IAAI,GAAG,GAAG;QACrB;QACA,iCAAiC;QACjC,IAAI,QAAQ,IAAI,EAAE,EAAE;YAChB,OAAO,IAAI,EAAE;QACjB;QACA,YAAY;QACZ,IAAI,QAAQ,IAAI,IAAI,EAAE;YAClB,OAAO,KAAK,SAAS,CAAC,IAAI,IAAI,EAAE,IAAI,CAAC,QAAQ;QACjD;QACA,MAAM,oBAAoB,KAAK;QAC/B,OAAO;IACX;IACA;;;;KAIC,GACD,eAAe,GAAG,EAAE;QAChB,MAAM,iBAAiB,CAAA,GAAA,4OAAA,CAAA,oBAAiB,AAAD,EAAE;QACzC,MAAM,OAAO,IAAI,CAAC,cAAc,CAAC,eAAe,MAAM;QACtD,MAAM,UAAU,eAAe,OAAO;QACtC,QAAQ,OAAO,CAAC,OAAO,4CAA4C;QACnE,OAAO,SAAS,wBAAwB;IAC5C;AACJ;AACA,8FAA8F;AAC9F,SAAS,SAAS,KAAK;IACnB,OAAO,OAAO,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAC,WAAW;AACrD;AAMO,MAAM,gBAAgB,gQAAA,CAAA,UAAO;IAChC;;;;KAIC,GACD,YAAY,OAAO,CAAE;QACjB,KAAK;QACL,IAAI,CAAC,OAAO,GAAG;IACnB;IACA;;;;KAIC,GACD,IAAI,GAAG,EAAE;QACL,IAAI;QACJ,IAAI,OAAO,QAAQ,UAAU;YACzB,IAAI,IAAI,CAAC,aAAa,EAAE;gBACpB,MAAM,IAAI,MAAM;YACpB;YACA,SAAS,IAAI,CAAC,YAAY,CAAC;YAC3B,MAAM,gBAAgB,OAAO,IAAI,KAAK,WAAW,YAAY;YAC7D,IAAI,iBAAiB,OAAO,IAAI,KAAK,WAAW,UAAU,EAAE;gBACxD,OAAO,IAAI,GAAG,gBAAgB,WAAW,KAAK,GAAG,WAAW,GAAG;gBAC/D,uBAAuB;gBACvB,IAAI,CAAC,aAAa,GAAG,IAAI,oBAAoB;gBAC7C,8DAA8D;gBAC9D,IAAI,OAAO,WAAW,KAAK,GAAG;oBAC1B,KAAK,CAAC,aAAa,WAAW;gBAClC;YACJ,OACK;gBACD,yBAAyB;gBACzB,KAAK,CAAC,aAAa,WAAW;YAClC;QACJ,OACK,IAAI,CAAA,GAAA,kPAAA,CAAA,WAAQ,AAAD,EAAE,QAAQ,IAAI,MAAM,EAAE;YAClC,kBAAkB;YAClB,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE;gBACrB,MAAM,IAAI,MAAM;YACpB,OACK;gBACD,SAAS,IAAI,CAAC,aAAa,CAAC,cAAc,CAAC;gBAC3C,IAAI,QAAQ;oBACR,wBAAwB;oBACxB,IAAI,CAAC,aAAa,GAAG;oBACrB,KAAK,CAAC,aAAa,WAAW;gBAClC;YACJ;QACJ,OACK;YACD,MAAM,IAAI,MAAM,mBAAmB;QACvC;IACJ;IACA;;;;;KAKC,GACD,aAAa,GAAG,EAAE;QACd,IAAI,IAAI;QACR,eAAe;QACf,MAAM,IAAI;YACN,MAAM,OAAO,IAAI,MAAM,CAAC;QAC5B;QACA,IAAI,UAAU,CAAC,EAAE,IAAI,CAAC,KAAK,WAAW;YAClC,MAAM,IAAI,MAAM,yBAAyB,EAAE,IAAI;QACnD;QACA,qCAAqC;QACrC,IAAI,EAAE,IAAI,KAAK,WAAW,YAAY,IAClC,EAAE,IAAI,KAAK,WAAW,UAAU,EAAE;YAClC,MAAM,QAAQ,IAAI;YAClB,MAAO,IAAI,MAAM,CAAC,EAAE,OAAO,OAAO,KAAK,IAAI,MAAM,CAAE,CAAE;YACrD,MAAM,MAAM,IAAI,SAAS,CAAC,OAAO;YACjC,IAAI,OAAO,OAAO,QAAQ,IAAI,MAAM,CAAC,OAAO,KAAK;gBAC7C,MAAM,IAAI,MAAM;YACpB;YACA,EAAE,WAAW,GAAG,OAAO;QAC3B;QACA,6BAA6B;QAC7B,IAAI,QAAQ,IAAI,MAAM,CAAC,IAAI,IAAI;YAC3B,MAAM,QAAQ,IAAI;YAClB,MAAO,EAAE,EAAG;gBACR,MAAM,IAAI,IAAI,MAAM,CAAC;gBACrB,IAAI,QAAQ,GACR;gBACJ,IAAI,MAAM,IAAI,MAAM,EAChB;YACR;YACA,EAAE,GAAG,GAAG,IAAI,SAAS,CAAC,OAAO;QACjC,OACK;YACD,EAAE,GAAG,GAAG;QACZ;QACA,aAAa;QACb,MAAM,OAAO,IAAI,MAAM,CAAC,IAAI;QAC5B,IAAI,OAAO,QAAQ,OAAO,SAAS,MAAM;YACrC,MAAM,QAAQ,IAAI;YAClB,MAAO,EAAE,EAAG;gBACR,MAAM,IAAI,IAAI,MAAM,CAAC;gBACrB,IAAI,QAAQ,KAAK,OAAO,MAAM,GAAG;oBAC7B,EAAE;oBACF;gBACJ;gBACA,IAAI,MAAM,IAAI,MAAM,EAChB;YACR;YACA,EAAE,EAAE,GAAG,OAAO,IAAI,SAAS,CAAC,OAAO,IAAI;QAC3C;QACA,oBAAoB;QACpB,IAAI,IAAI,MAAM,CAAC,EAAE,IAAI;YACjB,MAAM,UAAU,IAAI,CAAC,QAAQ,CAAC,IAAI,MAAM,CAAC;YACzC,IAAI,QAAQ,cAAc,CAAC,EAAE,IAAI,EAAE,UAAU;gBACzC,EAAE,IAAI,GAAG;YACb,OACK;gBACD,MAAM,IAAI,MAAM;YACpB;QACJ;QACA,MAAM,oBAAoB,KAAK;QAC/B,OAAO;IACX;IACA,SAAS,GAAG,EAAE;QACV,IAAI;YACA,OAAO,KAAK,KAAK,CAAC,KAAK,IAAI,CAAC,OAAO;QACvC,EACA,OAAO,GAAG;YACN,OAAO;QACX;IACJ;IACA,OAAO,eAAe,IAAI,EAAE,OAAO,EAAE;QACjC,OAAQ;YACJ,KAAK,WAAW,OAAO;gBACnB,OAAO,SAAS;YACpB,KAAK,WAAW,UAAU;gBACtB,OAAO,YAAY;YACvB,KAAK,WAAW,aAAa;gBACzB,OAAO,OAAO,YAAY,YAAY,SAAS;YACnD,KAAK,WAAW,KAAK;YACrB,KAAK,WAAW,YAAY;gBACxB,OAAQ,MAAM,OAAO,CAAC,YAClB,CAAC,OAAO,OAAO,CAAC,EAAE,KAAK,YAClB,OAAO,OAAO,CAAC,EAAE,KAAK,YACnB,gBAAgB,OAAO,CAAC,OAAO,CAAC,EAAE,MAAM,CAAC,CAAE;YAC3D,KAAK,WAAW,GAAG;YACnB,KAAK,WAAW,UAAU;gBACtB,OAAO,MAAM,OAAO,CAAC;QAC7B;IACJ;IACA;;KAEC,GACD,UAAU;QACN,IAAI,IAAI,CAAC,aAAa,EAAE;YACpB,IAAI,CAAC,aAAa,CAAC,sBAAsB;YACzC,IAAI,CAAC,aAAa,GAAG;QACzB;IACJ;AACJ;AACA;;;;;;;CAOC,GACD,MAAM;IACF,YAAY,MAAM,CAAE;QAChB,IAAI,CAAC,MAAM,GAAG;QACd,IAAI,CAAC,OAAO,GAAG,EAAE;QACjB,IAAI,CAAC,SAAS,GAAG;IACrB;IACA;;;;;;;KAOC,GACD,eAAe,OAAO,EAAE;QACpB,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC;QAClB,IAAI,IAAI,CAAC,OAAO,CAAC,MAAM,KAAK,IAAI,CAAC,SAAS,CAAC,WAAW,EAAE;YACpD,wBAAwB;YACxB,MAAM,SAAS,CAAA,GAAA,4OAAA,CAAA,oBAAiB,AAAD,EAAE,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,OAAO;YAC7D,IAAI,CAAC,sBAAsB;YAC3B,OAAO;QACX;QACA,OAAO;IACX;IACA;;KAEC,GACD,yBAAyB;QACrB,IAAI,CAAC,SAAS,GAAG;QACjB,IAAI,CAAC,OAAO,GAAG,EAAE;IACrB;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5691, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/bloomgoo/gold-exchange-platform/frontend/node_modules/.pnpm/zustand%405.0.3_%40types%2Breact%40_922ca76f2c6c3e669063fe7f86bd14e7/node_modules/zustand/esm/vanilla.mjs"], "sourcesContent": ["const createStoreImpl = (createState) => {\n  let state;\n  const listeners = /* @__PURE__ */ new Set();\n  const setState = (partial, replace) => {\n    const nextState = typeof partial === \"function\" ? partial(state) : partial;\n    if (!Object.is(nextState, state)) {\n      const previousState = state;\n      state = (replace != null ? replace : typeof nextState !== \"object\" || nextState === null) ? nextState : Object.assign({}, state, nextState);\n      listeners.forEach((listener) => listener(state, previousState));\n    }\n  };\n  const getState = () => state;\n  const getInitialState = () => initialState;\n  const subscribe = (listener) => {\n    listeners.add(listener);\n    return () => listeners.delete(listener);\n  };\n  const api = { setState, getState, getInitialState, subscribe };\n  const initialState = state = createState(setState, getState, api);\n  return api;\n};\nconst createStore = (createState) => createState ? createStoreImpl(createState) : createStoreImpl;\n\nexport { createStore };\n"], "names": [], "mappings": ";;;AAAA,MAAM,kBAAkB,CAAC;IACvB,IAAI;IACJ,MAAM,YAAY,aAAa,GAAG,IAAI;IACtC,MAAM,WAAW,CAAC,SAAS;QACzB,MAAM,YAAY,OAAO,YAAY,aAAa,QAAQ,SAAS;QACnE,IAAI,CAAC,OAAO,EAAE,CAAC,WAAW,QAAQ;YAChC,MAAM,gBAAgB;YACtB,QAAQ,CAAC,WAAW,OAAO,UAAU,OAAO,cAAc,YAAY,cAAc,IAAI,IAAI,YAAY,OAAO,MAAM,CAAC,CAAC,GAAG,OAAO;YACjI,UAAU,OAAO,CAAC,CAAC,WAAa,SAAS,OAAO;QAClD;IACF;IACA,MAAM,WAAW,IAAM;IACvB,MAAM,kBAAkB,IAAM;IAC9B,MAAM,YAAY,CAAC;QACjB,UAAU,GAAG,CAAC;QACd,OAAO,IAAM,UAAU,MAAM,CAAC;IAChC;IACA,MAAM,MAAM;QAAE;QAAU;QAAU;QAAiB;IAAU;IAC7D,MAAM,eAAe,QAAQ,YAAY,UAAU,UAAU;IAC7D,OAAO;AACT;AACA,MAAM,cAAc,CAAC,cAAgB,cAAc,gBAAgB,eAAe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5728, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/bloomgoo/gold-exchange-platform/frontend/node_modules/.pnpm/zustand%405.0.3_%40types%2Breact%40_922ca76f2c6c3e669063fe7f86bd14e7/node_modules/zustand/esm/react.mjs"], "sourcesContent": ["import React from 'react';\nimport { createStore } from 'zustand/vanilla';\n\nconst identity = (arg) => arg;\nfunction useStore(api, selector = identity) {\n  const slice = React.useSyncExternalStore(\n    api.subscribe,\n    () => selector(api.getState()),\n    () => selector(api.getInitialState())\n  );\n  React.useDebugValue(slice);\n  return slice;\n}\nconst createImpl = (createState) => {\n  const api = createStore(createState);\n  const useBoundStore = (selector) => useStore(api, selector);\n  Object.assign(useBoundStore, api);\n  return useBoundStore;\n};\nconst create = (createState) => createState ? createImpl(createState) : createImpl;\n\nexport { create, useStore };\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;AAEA,MAAM,WAAW,CAAC,MAAQ;AAC1B,SAAS,SAAS,GAAG,EAAE,WAAW,QAAQ;IACxC,MAAM,QAAQ,8SAAA,CAAA,UAAK,CAAC,oBAAoB,CACtC,IAAI,SAAS,EACb,IAAM,SAAS,IAAI,QAAQ,KAC3B,IAAM,SAAS,IAAI,eAAe;IAEpC,8SAAA,CAAA,UAAK,CAAC,aAAa,CAAC;IACpB,OAAO;AACT;AACA,MAAM,aAAa,CAAC;IAClB,MAAM,MAAM,CAAA,GAAA,sPAAA,CAAA,cAAW,AAAD,EAAE;IACxB,MAAM,gBAAgB,CAAC,WAAa,SAAS,KAAK;IAClD,OAAO,MAAM,CAAC,eAAe;IAC7B,OAAO;AACT;AACA,MAAM,SAAS,CAAC,cAAgB,cAAc,WAAW,eAAe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5756, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/bloomgoo/gold-exchange-platform/frontend/node_modules/.pnpm/zustand%405.0.3_%40types%2Breact%40_922ca76f2c6c3e669063fe7f86bd14e7/node_modules/zustand/esm/middleware.mjs"], "sourcesContent": ["const reduxImpl = (reducer, initial) => (set, _get, api) => {\n  api.dispatch = (action) => {\n    set((state) => reducer(state, action), false, action);\n    return action;\n  };\n  api.dispatchFromDevtools = true;\n  return { dispatch: (...a) => api.dispatch(...a), ...initial };\n};\nconst redux = reduxImpl;\n\nconst trackedConnections = /* @__PURE__ */ new Map();\nconst getTrackedConnectionState = (name) => {\n  const api = trackedConnections.get(name);\n  if (!api) return {};\n  return Object.fromEntries(\n    Object.entries(api.stores).map(([key, api2]) => [key, api2.getState()])\n  );\n};\nconst extractConnectionInformation = (store, extensionConnector, options) => {\n  if (store === undefined) {\n    return {\n      type: \"untracked\",\n      connection: extensionConnector.connect(options)\n    };\n  }\n  const existingConnection = trackedConnections.get(options.name);\n  if (existingConnection) {\n    return { type: \"tracked\", store, ...existingConnection };\n  }\n  const newConnection = {\n    connection: extensionConnector.connect(options),\n    stores: {}\n  };\n  trackedConnections.set(options.name, newConnection);\n  return { type: \"tracked\", store, ...newConnection };\n};\nconst devtoolsImpl = (fn, devtoolsOptions = {}) => (set, get, api) => {\n  const { enabled, anonymousActionType, store, ...options } = devtoolsOptions;\n  let extensionConnector;\n  try {\n    extensionConnector = (enabled != null ? enabled : (import.meta.env ? import.meta.env.MODE : void 0) !== \"production\") && window.__REDUX_DEVTOOLS_EXTENSION__;\n  } catch (e) {\n  }\n  if (!extensionConnector) {\n    return fn(set, get, api);\n  }\n  const { connection, ...connectionInformation } = extractConnectionInformation(store, extensionConnector, options);\n  let isRecording = true;\n  api.setState = (state, replace, nameOrAction) => {\n    const r = set(state, replace);\n    if (!isRecording) return r;\n    const action = nameOrAction === undefined ? { type: anonymousActionType || \"anonymous\" } : typeof nameOrAction === \"string\" ? { type: nameOrAction } : nameOrAction;\n    if (store === undefined) {\n      connection == null ? undefined : connection.send(action, get());\n      return r;\n    }\n    connection == null ? undefined : connection.send(\n      {\n        ...action,\n        type: `${store}/${action.type}`\n      },\n      {\n        ...getTrackedConnectionState(options.name),\n        [store]: api.getState()\n      }\n    );\n    return r;\n  };\n  const setStateFromDevtools = (...a) => {\n    const originalIsRecording = isRecording;\n    isRecording = false;\n    set(...a);\n    isRecording = originalIsRecording;\n  };\n  const initialState = fn(api.setState, get, api);\n  if (connectionInformation.type === \"untracked\") {\n    connection == null ? undefined : connection.init(initialState);\n  } else {\n    connectionInformation.stores[connectionInformation.store] = api;\n    connection == null ? undefined : connection.init(\n      Object.fromEntries(\n        Object.entries(connectionInformation.stores).map(([key, store2]) => [\n          key,\n          key === connectionInformation.store ? initialState : store2.getState()\n        ])\n      )\n    );\n  }\n  if (api.dispatchFromDevtools && typeof api.dispatch === \"function\") {\n    let didWarnAboutReservedActionType = false;\n    const originalDispatch = api.dispatch;\n    api.dispatch = (...a) => {\n      if ((import.meta.env ? import.meta.env.MODE : undefined) !== \"production\" && a[0].type === \"__setState\" && !didWarnAboutReservedActionType) {\n        console.warn(\n          '[zustand devtools middleware] \"__setState\" action type is reserved to set state from the devtools. Avoid using it.'\n        );\n        didWarnAboutReservedActionType = true;\n      }\n      originalDispatch(...a);\n    };\n  }\n  connection.subscribe((message) => {\n    var _a;\n    switch (message.type) {\n      case \"ACTION\":\n        if (typeof message.payload !== \"string\") {\n          console.error(\n            \"[zustand devtools middleware] Unsupported action format\"\n          );\n          return;\n        }\n        return parseJsonThen(\n          message.payload,\n          (action) => {\n            if (action.type === \"__setState\") {\n              if (store === undefined) {\n                setStateFromDevtools(action.state);\n                return;\n              }\n              if (Object.keys(action.state).length !== 1) {\n                console.error(\n                  `\n                    [zustand devtools middleware] Unsupported __setState action format.\n                    When using 'store' option in devtools(), the 'state' should have only one key, which is a value of 'store' that was passed in devtools(),\n                    and value of this only key should be a state object. Example: { \"type\": \"__setState\", \"state\": { \"abc123Store\": { \"foo\": \"bar\" } } }\n                    `\n                );\n              }\n              const stateFromDevtools = action.state[store];\n              if (stateFromDevtools === undefined || stateFromDevtools === null) {\n                return;\n              }\n              if (JSON.stringify(api.getState()) !== JSON.stringify(stateFromDevtools)) {\n                setStateFromDevtools(stateFromDevtools);\n              }\n              return;\n            }\n            if (!api.dispatchFromDevtools) return;\n            if (typeof api.dispatch !== \"function\") return;\n            api.dispatch(action);\n          }\n        );\n      case \"DISPATCH\":\n        switch (message.payload.type) {\n          case \"RESET\":\n            setStateFromDevtools(initialState);\n            if (store === undefined) {\n              return connection == null ? undefined : connection.init(api.getState());\n            }\n            return connection == null ? undefined : connection.init(getTrackedConnectionState(options.name));\n          case \"COMMIT\":\n            if (store === undefined) {\n              connection == null ? undefined : connection.init(api.getState());\n              return;\n            }\n            return connection == null ? undefined : connection.init(getTrackedConnectionState(options.name));\n          case \"ROLLBACK\":\n            return parseJsonThen(message.state, (state) => {\n              if (store === undefined) {\n                setStateFromDevtools(state);\n                connection == null ? undefined : connection.init(api.getState());\n                return;\n              }\n              setStateFromDevtools(state[store]);\n              connection == null ? undefined : connection.init(getTrackedConnectionState(options.name));\n            });\n          case \"JUMP_TO_STATE\":\n          case \"JUMP_TO_ACTION\":\n            return parseJsonThen(message.state, (state) => {\n              if (store === undefined) {\n                setStateFromDevtools(state);\n                return;\n              }\n              if (JSON.stringify(api.getState()) !== JSON.stringify(state[store])) {\n                setStateFromDevtools(state[store]);\n              }\n            });\n          case \"IMPORT_STATE\": {\n            const { nextLiftedState } = message.payload;\n            const lastComputedState = (_a = nextLiftedState.computedStates.slice(-1)[0]) == null ? undefined : _a.state;\n            if (!lastComputedState) return;\n            if (store === undefined) {\n              setStateFromDevtools(lastComputedState);\n            } else {\n              setStateFromDevtools(lastComputedState[store]);\n            }\n            connection == null ? undefined : connection.send(\n              null,\n              // FIXME no-any\n              nextLiftedState\n            );\n            return;\n          }\n          case \"PAUSE_RECORDING\":\n            return isRecording = !isRecording;\n        }\n        return;\n    }\n  });\n  return initialState;\n};\nconst devtools = devtoolsImpl;\nconst parseJsonThen = (stringified, f) => {\n  let parsed;\n  try {\n    parsed = JSON.parse(stringified);\n  } catch (e) {\n    console.error(\n      \"[zustand devtools middleware] Could not parse the received json\",\n      e\n    );\n  }\n  if (parsed !== undefined) f(parsed);\n};\n\nconst subscribeWithSelectorImpl = (fn) => (set, get, api) => {\n  const origSubscribe = api.subscribe;\n  api.subscribe = (selector, optListener, options) => {\n    let listener = selector;\n    if (optListener) {\n      const equalityFn = (options == null ? undefined : options.equalityFn) || Object.is;\n      let currentSlice = selector(api.getState());\n      listener = (state) => {\n        const nextSlice = selector(state);\n        if (!equalityFn(currentSlice, nextSlice)) {\n          const previousSlice = currentSlice;\n          optListener(currentSlice = nextSlice, previousSlice);\n        }\n      };\n      if (options == null ? undefined : options.fireImmediately) {\n        optListener(currentSlice, currentSlice);\n      }\n    }\n    return origSubscribe(listener);\n  };\n  const initialState = fn(set, get, api);\n  return initialState;\n};\nconst subscribeWithSelector = subscribeWithSelectorImpl;\n\nconst combine = (initialState, create) => (...a) => Object.assign({}, initialState, create(...a));\n\nfunction createJSONStorage(getStorage, options) {\n  let storage;\n  try {\n    storage = getStorage();\n  } catch (e) {\n    return;\n  }\n  const persistStorage = {\n    getItem: (name) => {\n      var _a;\n      const parse = (str2) => {\n        if (str2 === null) {\n          return null;\n        }\n        return JSON.parse(str2, options == null ? undefined : options.reviver);\n      };\n      const str = (_a = storage.getItem(name)) != null ? _a : null;\n      if (str instanceof Promise) {\n        return str.then(parse);\n      }\n      return parse(str);\n    },\n    setItem: (name, newValue) => storage.setItem(\n      name,\n      JSON.stringify(newValue, options == null ? undefined : options.replacer)\n    ),\n    removeItem: (name) => storage.removeItem(name)\n  };\n  return persistStorage;\n}\nconst toThenable = (fn) => (input) => {\n  try {\n    const result = fn(input);\n    if (result instanceof Promise) {\n      return result;\n    }\n    return {\n      then(onFulfilled) {\n        return toThenable(onFulfilled)(result);\n      },\n      catch(_onRejected) {\n        return this;\n      }\n    };\n  } catch (e) {\n    return {\n      then(_onFulfilled) {\n        return this;\n      },\n      catch(onRejected) {\n        return toThenable(onRejected)(e);\n      }\n    };\n  }\n};\nconst persistImpl = (config, baseOptions) => (set, get, api) => {\n  let options = {\n    storage: createJSONStorage(() => localStorage),\n    partialize: (state) => state,\n    version: 0,\n    merge: (persistedState, currentState) => ({\n      ...currentState,\n      ...persistedState\n    }),\n    ...baseOptions\n  };\n  let hasHydrated = false;\n  const hydrationListeners = /* @__PURE__ */ new Set();\n  const finishHydrationListeners = /* @__PURE__ */ new Set();\n  let storage = options.storage;\n  if (!storage) {\n    return config(\n      (...args) => {\n        console.warn(\n          `[zustand persist middleware] Unable to update item '${options.name}', the given storage is currently unavailable.`\n        );\n        set(...args);\n      },\n      get,\n      api\n    );\n  }\n  const setItem = () => {\n    const state = options.partialize({ ...get() });\n    return storage.setItem(options.name, {\n      state,\n      version: options.version\n    });\n  };\n  const savedSetState = api.setState;\n  api.setState = (state, replace) => {\n    savedSetState(state, replace);\n    void setItem();\n  };\n  const configResult = config(\n    (...args) => {\n      set(...args);\n      void setItem();\n    },\n    get,\n    api\n  );\n  api.getInitialState = () => configResult;\n  let stateFromStorage;\n  const hydrate = () => {\n    var _a, _b;\n    if (!storage) return;\n    hasHydrated = false;\n    hydrationListeners.forEach((cb) => {\n      var _a2;\n      return cb((_a2 = get()) != null ? _a2 : configResult);\n    });\n    const postRehydrationCallback = ((_b = options.onRehydrateStorage) == null ? undefined : _b.call(options, (_a = get()) != null ? _a : configResult)) || undefined;\n    return toThenable(storage.getItem.bind(storage))(options.name).then((deserializedStorageValue) => {\n      if (deserializedStorageValue) {\n        if (typeof deserializedStorageValue.version === \"number\" && deserializedStorageValue.version !== options.version) {\n          if (options.migrate) {\n            const migration = options.migrate(\n              deserializedStorageValue.state,\n              deserializedStorageValue.version\n            );\n            if (migration instanceof Promise) {\n              return migration.then((result) => [true, result]);\n            }\n            return [true, migration];\n          }\n          console.error(\n            `State loaded from storage couldn't be migrated since no migrate function was provided`\n          );\n        } else {\n          return [false, deserializedStorageValue.state];\n        }\n      }\n      return [false, undefined];\n    }).then((migrationResult) => {\n      var _a2;\n      const [migrated, migratedState] = migrationResult;\n      stateFromStorage = options.merge(\n        migratedState,\n        (_a2 = get()) != null ? _a2 : configResult\n      );\n      set(stateFromStorage, true);\n      if (migrated) {\n        return setItem();\n      }\n    }).then(() => {\n      postRehydrationCallback == null ? undefined : postRehydrationCallback(stateFromStorage, undefined);\n      stateFromStorage = get();\n      hasHydrated = true;\n      finishHydrationListeners.forEach((cb) => cb(stateFromStorage));\n    }).catch((e) => {\n      postRehydrationCallback == null ? undefined : postRehydrationCallback(undefined, e);\n    });\n  };\n  api.persist = {\n    setOptions: (newOptions) => {\n      options = {\n        ...options,\n        ...newOptions\n      };\n      if (newOptions.storage) {\n        storage = newOptions.storage;\n      }\n    },\n    clearStorage: () => {\n      storage == null ? undefined : storage.removeItem(options.name);\n    },\n    getOptions: () => options,\n    rehydrate: () => hydrate(),\n    hasHydrated: () => hasHydrated,\n    onHydrate: (cb) => {\n      hydrationListeners.add(cb);\n      return () => {\n        hydrationListeners.delete(cb);\n      };\n    },\n    onFinishHydration: (cb) => {\n      finishHydrationListeners.add(cb);\n      return () => {\n        finishHydrationListeners.delete(cb);\n      };\n    }\n  };\n  if (!options.skipHydration) {\n    hydrate();\n  }\n  return stateFromStorage || configResult;\n};\nconst persist = persistImpl;\n\nexport { combine, createJSONStorage, devtools, persist, redux, subscribeWithSelector };\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAAA,MAAM,YAAY,CAAC,SAAS,UAAY,CAAC,KAAK,MAAM;QAClD,IAAI,QAAQ,GAAG,CAAC;YACd,IAAI,CAAC,QAAU,QAAQ,OAAO,SAAS,OAAO;YAC9C,OAAO;QACT;QACA,IAAI,oBAAoB,GAAG;QAC3B,OAAO;YAAE,UAAU,CAAC,GAAG,IAAM,IAAI,QAAQ,IAAI;YAAI,GAAG,OAAO;QAAC;IAC9D;AACA,MAAM,QAAQ;AAEd,MAAM,qBAAqB,aAAa,GAAG,IAAI;AAC/C,MAAM,4BAA4B,CAAC;IACjC,MAAM,MAAM,mBAAmB,GAAG,CAAC;IACnC,IAAI,CAAC,KAAK,OAAO,CAAC;IAClB,OAAO,OAAO,WAAW,CACvB,OAAO,OAAO,CAAC,IAAI,MAAM,EAAE,GAAG,CAAC,CAAC,CAAC,KAAK,KAAK,GAAK;YAAC;YAAK,KAAK,QAAQ;SAAG;AAE1E;AACA,MAAM,+BAA+B,CAAC,OAAO,oBAAoB;IAC/D,IAAI,UAAU,WAAW;QACvB,OAAO;YACL,MAAM;YACN,YAAY,mBAAmB,OAAO,CAAC;QACzC;IACF;IACA,MAAM,qBAAqB,mBAAmB,GAAG,CAAC,QAAQ,IAAI;IAC9D,IAAI,oBAAoB;QACtB,OAAO;YAAE,MAAM;YAAW;YAAO,GAAG,kBAAkB;QAAC;IACzD;IACA,MAAM,gBAAgB;QACpB,YAAY,mBAAmB,OAAO,CAAC;QACvC,QAAQ,CAAC;IACX;IACA,mBAAmB,GAAG,CAAC,QAAQ,IAAI,EAAE;IACrC,OAAO;QAAE,MAAM;QAAW;QAAO,GAAG,aAAa;IAAC;AACpD;AACA,MAAM,eAAe,CAAC,IAAI,kBAAkB,CAAC,CAAC,GAAK,CAAC,KAAK,KAAK;QAC5D,MAAM,EAAE,OAAO,EAAE,mBAAmB,EAAE,KAAK,EAAE,GAAG,SAAS,GAAG;QAC5D,IAAI;QACJ,IAAI;YACF,qBAAqB,CAAC,WAAW,OAAO,UAAU,CAAC,8BAAY,GAAG,GAAG,8BAAY,GAAG,CAAC,IAAI,GAAG,KAAK,CAAC,MAAM,YAAY,KAAK,OAAO,4BAA4B;QAC9J,EAAE,OAAO,GAAG,CACZ;QACA,IAAI,CAAC,oBAAoB;YACvB,OAAO,GAAG,KAAK,KAAK;QACtB;QACA,MAAM,EAAE,UAAU,EAAE,GAAG,uBAAuB,GAAG,6BAA6B,OAAO,oBAAoB;QACzG,IAAI,cAAc;QAClB,IAAI,QAAQ,GAAG,CAAC,OAAO,SAAS;YAC9B,MAAM,IAAI,IAAI,OAAO;YACrB,IAAI,CAAC,aAAa,OAAO;YACzB,MAAM,SAAS,iBAAiB,YAAY;gBAAE,MAAM,uBAAuB;YAAY,IAAI,OAAO,iBAAiB,WAAW;gBAAE,MAAM;YAAa,IAAI;YACvJ,IAAI,UAAU,WAAW;gBACvB,cAAc,OAAO,YAAY,WAAW,IAAI,CAAC,QAAQ;gBACzD,OAAO;YACT;YACA,cAAc,OAAO,YAAY,WAAW,IAAI,CAC9C;gBACE,GAAG,MAAM;gBACT,MAAM,GAAG,MAAM,CAAC,EAAE,OAAO,IAAI,EAAE;YACjC,GACA;gBACE,GAAG,0BAA0B,QAAQ,IAAI,CAAC;gBAC1C,CAAC,MAAM,EAAE,IAAI,QAAQ;YACvB;YAEF,OAAO;QACT;QACA,MAAM,uBAAuB,CAAC,GAAG;YAC/B,MAAM,sBAAsB;YAC5B,cAAc;YACd,OAAO;YACP,cAAc;QAChB;QACA,MAAM,eAAe,GAAG,IAAI,QAAQ,EAAE,KAAK;QAC3C,IAAI,sBAAsB,IAAI,KAAK,aAAa;YAC9C,cAAc,OAAO,YAAY,WAAW,IAAI,CAAC;QACnD,OAAO;YACL,sBAAsB,MAAM,CAAC,sBAAsB,KAAK,CAAC,GAAG;YAC5D,cAAc,OAAO,YAAY,WAAW,IAAI,CAC9C,OAAO,WAAW,CAChB,OAAO,OAAO,CAAC,sBAAsB,MAAM,EAAE,GAAG,CAAC,CAAC,CAAC,KAAK,OAAO,GAAK;oBAClE;oBACA,QAAQ,sBAAsB,KAAK,GAAG,eAAe,OAAO,QAAQ;iBACrE;QAGP;QACA,IAAI,IAAI,oBAAoB,IAAI,OAAO,IAAI,QAAQ,KAAK,YAAY;YAClE,IAAI,iCAAiC;YACrC,MAAM,mBAAmB,IAAI,QAAQ;YACrC,IAAI,QAAQ,GAAG,CAAC,GAAG;gBACjB,IAAI,CAAC,8BAAY,GAAG,GAAG,8BAAY,GAAG,CAAC,IAAI,GAAG,SAAS,MAAM,gBAAgB,CAAC,CAAC,EAAE,CAAC,IAAI,KAAK,gBAAgB,CAAC,gCAAgC;oBAC1I,QAAQ,IAAI,CACV;oBAEF,iCAAiC;gBACnC;gBACA,oBAAoB;YACtB;QACF;QACA,WAAW,SAAS,CAAC,CAAC;YACpB,IAAI;YACJ,OAAQ,QAAQ,IAAI;gBAClB,KAAK;oBACH,IAAI,OAAO,QAAQ,OAAO,KAAK,UAAU;wBACvC,QAAQ,KAAK,CACX;wBAEF;oBACF;oBACA,OAAO,cACL,QAAQ,OAAO,EACf,CAAC;wBACC,IAAI,OAAO,IAAI,KAAK,cAAc;4BAChC,IAAI,UAAU,WAAW;gCACvB,qBAAqB,OAAO,KAAK;gCACjC;4BACF;4BACA,IAAI,OAAO,IAAI,CAAC,OAAO,KAAK,EAAE,MAAM,KAAK,GAAG;gCAC1C,QAAQ,KAAK,CACX,CAAC;;;;oBAIC,CAAC;4BAEP;4BACA,MAAM,oBAAoB,OAAO,KAAK,CAAC,MAAM;4BAC7C,IAAI,sBAAsB,aAAa,sBAAsB,MAAM;gCACjE;4BACF;4BACA,IAAI,KAAK,SAAS,CAAC,IAAI,QAAQ,QAAQ,KAAK,SAAS,CAAC,oBAAoB;gCACxE,qBAAqB;4BACvB;4BACA;wBACF;wBACA,IAAI,CAAC,IAAI,oBAAoB,EAAE;wBAC/B,IAAI,OAAO,IAAI,QAAQ,KAAK,YAAY;wBACxC,IAAI,QAAQ,CAAC;oBACf;gBAEJ,KAAK;oBACH,OAAQ,QAAQ,OAAO,CAAC,IAAI;wBAC1B,KAAK;4BACH,qBAAqB;4BACrB,IAAI,UAAU,WAAW;gCACvB,OAAO,cAAc,OAAO,YAAY,WAAW,IAAI,CAAC,IAAI,QAAQ;4BACtE;4BACA,OAAO,cAAc,OAAO,YAAY,WAAW,IAAI,CAAC,0BAA0B,QAAQ,IAAI;wBAChG,KAAK;4BACH,IAAI,UAAU,WAAW;gCACvB,cAAc,OAAO,YAAY,WAAW,IAAI,CAAC,IAAI,QAAQ;gCAC7D;4BACF;4BACA,OAAO,cAAc,OAAO,YAAY,WAAW,IAAI,CAAC,0BAA0B,QAAQ,IAAI;wBAChG,KAAK;4BACH,OAAO,cAAc,QAAQ,KAAK,EAAE,CAAC;gCACnC,IAAI,UAAU,WAAW;oCACvB,qBAAqB;oCACrB,cAAc,OAAO,YAAY,WAAW,IAAI,CAAC,IAAI,QAAQ;oCAC7D;gCACF;gCACA,qBAAqB,KAAK,CAAC,MAAM;gCACjC,cAAc,OAAO,YAAY,WAAW,IAAI,CAAC,0BAA0B,QAAQ,IAAI;4BACzF;wBACF,KAAK;wBACL,KAAK;4BACH,OAAO,cAAc,QAAQ,KAAK,EAAE,CAAC;gCACnC,IAAI,UAAU,WAAW;oCACvB,qBAAqB;oCACrB;gCACF;gCACA,IAAI,KAAK,SAAS,CAAC,IAAI,QAAQ,QAAQ,KAAK,SAAS,CAAC,KAAK,CAAC,MAAM,GAAG;oCACnE,qBAAqB,KAAK,CAAC,MAAM;gCACnC;4BACF;wBACF,KAAK;4BAAgB;gCACnB,MAAM,EAAE,eAAe,EAAE,GAAG,QAAQ,OAAO;gCAC3C,MAAM,oBAAoB,CAAC,KAAK,gBAAgB,cAAc,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,EAAE,KAAK,OAAO,YAAY,GAAG,KAAK;gCAC3G,IAAI,CAAC,mBAAmB;gCACxB,IAAI,UAAU,WAAW;oCACvB,qBAAqB;gCACvB,OAAO;oCACL,qBAAqB,iBAAiB,CAAC,MAAM;gCAC/C;gCACA,cAAc,OAAO,YAAY,WAAW,IAAI,CAC9C,MACA,eAAe;gCACf;gCAEF;4BACF;wBACA,KAAK;4BACH,OAAO,cAAc,CAAC;oBAC1B;oBACA;YACJ;QACF;QACA,OAAO;IACT;AACA,MAAM,WAAW;AACjB,MAAM,gBAAgB,CAAC,aAAa;IAClC,IAAI;IACJ,IAAI;QACF,SAAS,KAAK,KAAK,CAAC;IACtB,EAAE,OAAO,GAAG;QACV,QAAQ,KAAK,CACX,mEACA;IAEJ;IACA,IAAI,WAAW,WAAW,EAAE;AAC9B;AAEA,MAAM,4BAA4B,CAAC,KAAO,CAAC,KAAK,KAAK;QACnD,MAAM,gBAAgB,IAAI,SAAS;QACnC,IAAI,SAAS,GAAG,CAAC,UAAU,aAAa;YACtC,IAAI,WAAW;YACf,IAAI,aAAa;gBACf,MAAM,aAAa,CAAC,WAAW,OAAO,YAAY,QAAQ,UAAU,KAAK,OAAO,EAAE;gBAClF,IAAI,eAAe,SAAS,IAAI,QAAQ;gBACxC,WAAW,CAAC;oBACV,MAAM,YAAY,SAAS;oBAC3B,IAAI,CAAC,WAAW,cAAc,YAAY;wBACxC,MAAM,gBAAgB;wBACtB,YAAY,eAAe,WAAW;oBACxC;gBACF;gBACA,IAAI,WAAW,OAAO,YAAY,QAAQ,eAAe,EAAE;oBACzD,YAAY,cAAc;gBAC5B;YACF;YACA,OAAO,cAAc;QACvB;QACA,MAAM,eAAe,GAAG,KAAK,KAAK;QAClC,OAAO;IACT;AACA,MAAM,wBAAwB;AAE9B,MAAM,UAAU,CAAC,cAAc,SAAW,CAAC,GAAG,IAAM,OAAO,MAAM,CAAC,CAAC,GAAG,cAAc,UAAU;AAE9F,SAAS,kBAAkB,UAAU,EAAE,OAAO;IAC5C,IAAI;IACJ,IAAI;QACF,UAAU;IACZ,EAAE,OAAO,GAAG;QACV;IACF;IACA,MAAM,iBAAiB;QACrB,SAAS,CAAC;YACR,IAAI;YACJ,MAAM,QAAQ,CAAC;gBACb,IAAI,SAAS,MAAM;oBACjB,OAAO;gBACT;gBACA,OAAO,KAAK,KAAK,CAAC,MAAM,WAAW,OAAO,YAAY,QAAQ,OAAO;YACvE;YACA,MAAM,MAAM,CAAC,KAAK,QAAQ,OAAO,CAAC,KAAK,KAAK,OAAO,KAAK;YACxD,IAAI,eAAe,SAAS;gBAC1B,OAAO,IAAI,IAAI,CAAC;YAClB;YACA,OAAO,MAAM;QACf;QACA,SAAS,CAAC,MAAM,WAAa,QAAQ,OAAO,CAC1C,MACA,KAAK,SAAS,CAAC,UAAU,WAAW,OAAO,YAAY,QAAQ,QAAQ;QAEzE,YAAY,CAAC,OAAS,QAAQ,UAAU,CAAC;IAC3C;IACA,OAAO;AACT;AACA,MAAM,aAAa,CAAC,KAAO,CAAC;QAC1B,IAAI;YACF,MAAM,SAAS,GAAG;YAClB,IAAI,kBAAkB,SAAS;gBAC7B,OAAO;YACT;YACA,OAAO;gBACL,MAAK,WAAW;oBACd,OAAO,WAAW,aAAa;gBACjC;gBACA,OAAM,WAAW;oBACf,OAAO,IAAI;gBACb;YACF;QACF,EAAE,OAAO,GAAG;YACV,OAAO;gBACL,MAAK,YAAY;oBACf,OAAO,IAAI;gBACb;gBACA,OAAM,UAAU;oBACd,OAAO,WAAW,YAAY;gBAChC;YACF;QACF;IACF;AACA,MAAM,cAAc,CAAC,QAAQ,cAAgB,CAAC,KAAK,KAAK;QACtD,IAAI,UAAU;YACZ,SAAS,kBAAkB,IAAM;YACjC,YAAY,CAAC,QAAU;YACvB,SAAS;YACT,OAAO,CAAC,gBAAgB,eAAiB,CAAC;oBACxC,GAAG,YAAY;oBACf,GAAG,cAAc;gBACnB,CAAC;YACD,GAAG,WAAW;QAChB;QACA,IAAI,cAAc;QAClB,MAAM,qBAAqB,aAAa,GAAG,IAAI;QAC/C,MAAM,2BAA2B,aAAa,GAAG,IAAI;QACrD,IAAI,UAAU,QAAQ,OAAO;QAC7B,IAAI,CAAC,SAAS;YACZ,OAAO,OACL,CAAC,GAAG;gBACF,QAAQ,IAAI,CACV,CAAC,oDAAoD,EAAE,QAAQ,IAAI,CAAC,8CAA8C,CAAC;gBAErH,OAAO;YACT,GACA,KACA;QAEJ;QACA,MAAM,UAAU;YACd,MAAM,QAAQ,QAAQ,UAAU,CAAC;gBAAE,GAAG,KAAK;YAAC;YAC5C,OAAO,QAAQ,OAAO,CAAC,QAAQ,IAAI,EAAE;gBACnC;gBACA,SAAS,QAAQ,OAAO;YAC1B;QACF;QACA,MAAM,gBAAgB,IAAI,QAAQ;QAClC,IAAI,QAAQ,GAAG,CAAC,OAAO;YACrB,cAAc,OAAO;YACrB,KAAK;QACP;QACA,MAAM,eAAe,OACnB,CAAC,GAAG;YACF,OAAO;YACP,KAAK;QACP,GACA,KACA;QAEF,IAAI,eAAe,GAAG,IAAM;QAC5B,IAAI;QACJ,MAAM,UAAU;YACd,IAAI,IAAI;YACR,IAAI,CAAC,SAAS;YACd,cAAc;YACd,mBAAmB,OAAO,CAAC,CAAC;gBAC1B,IAAI;gBACJ,OAAO,GAAG,CAAC,MAAM,KAAK,KAAK,OAAO,MAAM;YAC1C;YACA,MAAM,0BAA0B,CAAC,CAAC,KAAK,QAAQ,kBAAkB,KAAK,OAAO,YAAY,GAAG,IAAI,CAAC,SAAS,CAAC,KAAK,KAAK,KAAK,OAAO,KAAK,aAAa,KAAK;YACxJ,OAAO,WAAW,QAAQ,OAAO,CAAC,IAAI,CAAC,UAAU,QAAQ,IAAI,EAAE,IAAI,CAAC,CAAC;gBACnE,IAAI,0BAA0B;oBAC5B,IAAI,OAAO,yBAAyB,OAAO,KAAK,YAAY,yBAAyB,OAAO,KAAK,QAAQ,OAAO,EAAE;wBAChH,IAAI,QAAQ,OAAO,EAAE;4BACnB,MAAM,YAAY,QAAQ,OAAO,CAC/B,yBAAyB,KAAK,EAC9B,yBAAyB,OAAO;4BAElC,IAAI,qBAAqB,SAAS;gCAChC,OAAO,UAAU,IAAI,CAAC,CAAC,SAAW;wCAAC;wCAAM;qCAAO;4BAClD;4BACA,OAAO;gCAAC;gCAAM;6BAAU;wBAC1B;wBACA,QAAQ,KAAK,CACX,CAAC,qFAAqF,CAAC;oBAE3F,OAAO;wBACL,OAAO;4BAAC;4BAAO,yBAAyB,KAAK;yBAAC;oBAChD;gBACF;gBACA,OAAO;oBAAC;oBAAO;iBAAU;YAC3B,GAAG,IAAI,CAAC,CAAC;gBACP,IAAI;gBACJ,MAAM,CAAC,UAAU,cAAc,GAAG;gBAClC,mBAAmB,QAAQ,KAAK,CAC9B,eACA,CAAC,MAAM,KAAK,KAAK,OAAO,MAAM;gBAEhC,IAAI,kBAAkB;gBACtB,IAAI,UAAU;oBACZ,OAAO;gBACT;YACF,GAAG,IAAI,CAAC;gBACN,2BAA2B,OAAO,YAAY,wBAAwB,kBAAkB;gBACxF,mBAAmB;gBACnB,cAAc;gBACd,yBAAyB,OAAO,CAAC,CAAC,KAAO,GAAG;YAC9C,GAAG,KAAK,CAAC,CAAC;gBACR,2BAA2B,OAAO,YAAY,wBAAwB,WAAW;YACnF;QACF;QACA,IAAI,OAAO,GAAG;YACZ,YAAY,CAAC;gBACX,UAAU;oBACR,GAAG,OAAO;oBACV,GAAG,UAAU;gBACf;gBACA,IAAI,WAAW,OAAO,EAAE;oBACtB,UAAU,WAAW,OAAO;gBAC9B;YACF;YACA,cAAc;gBACZ,WAAW,OAAO,YAAY,QAAQ,UAAU,CAAC,QAAQ,IAAI;YAC/D;YACA,YAAY,IAAM;YAClB,WAAW,IAAM;YACjB,aAAa,IAAM;YACnB,WAAW,CAAC;gBACV,mBAAmB,GAAG,CAAC;gBACvB,OAAO;oBACL,mBAAmB,MAAM,CAAC;gBAC5B;YACF;YACA,mBAAmB,CAAC;gBAClB,yBAAyB,GAAG,CAAC;gBAC7B,OAAO;oBACL,yBAAyB,MAAM,CAAC;gBAClC;YACF;QACF;QACA,IAAI,CAAC,QAAQ,aAAa,EAAE;YAC1B;QACF;QACA,OAAO,oBAAoB;IAC7B;AACA,MAAM,UAAU", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 6190, "column": 0}, "map": {"version": 3, "file": "utils.js", "sources": ["file://C%3A/bloomgoo/gold-exchange-platform/frontend/node_modules/.pnpm/lucide-react%400.487.0_react%4019.1.0/node_modules/shared/src/utils.ts"], "sourcesContent": ["import { CamelToPascal } from './utility-types';\n\n/**\n * Converts string to kebab case\n *\n * @param {string} string\n * @returns {string} A kebabized string\n */\nexport const toKebabCase = (string: string) =>\n  string.replace(/([a-z0-9])([A-Z])/g, '$1-$2').toLowerCase();\n\n/**\n * Converts string to camel case\n *\n * @param {string} string\n * @returns {string} A camelized string\n */\nexport const toCamelCase = <T extends string>(string: T) =>\n  string.replace(/^([A-Z])|[\\s-_]+(\\w)/g, (match, p1, p2) =>\n    p2 ? p2.toUpperCase() : p1.toLowerCase(),\n  );\n\n/**\n * Converts string to pascal case\n *\n * @param {string} string\n * @returns {string} A pascalized string\n */\nexport const toPascalCase = <T extends string>(string: T): CamelToPascal<T> => {\n  const camelCase = toCamelCase(string);\n\n  return (camelCase.charAt(0).toUpperCase() + camelCase.slice(1)) as CamelToPascal<T>;\n};\n\n/**\n * Merges classes into a single string\n *\n * @param {array} classes\n * @returns {string} A string of classes\n */\nexport const mergeClasses = <ClassType = string | undefined | null>(...classes: ClassType[]) =>\n  classes\n    .filter((className, index, array) => {\n      return (\n        Boolean(className) &&\n        (className as string).trim() !== '' &&\n        array.indexOf(className) === index\n      );\n    })\n    .join(' ')\n    .trim();\n"], "names": [], "mappings": ";;;;;;;;;;;AAQa,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAc,CAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAC1B,GAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAO,OAAA,CAAQ,oBAAsB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAO,EAAE,WAAY,CAAA,CAAA,CAAA;AAQ/C,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,WAAA,CAAc,CAAA,CAAA,CAAmB,MAAA,CAC5C,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAyB,CAAC,OAAO,CAAI,CAAA,CAAA,CAAA,EAAA,CAClD,CAAA,CAAA,CAAA,EAAK,CAAA,CAAA,CAAA,CAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAgB,GAAA,CAAA,CAAA,CAAG,WAAY,CAAA,CAAA;AAS9B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAe,CAAmB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAgC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;IACvE,MAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAY,YAAY,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,CAAA,CAAA;IAE5B,OAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAU,MAAA,CAAO,CAAC,CAAA,CAAE,WAAA,EAAgB,GAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAU,KAAA,CAAM,CAAC,CAAA,CAAA;AAC/D,CAAA,CAAA;AAQa,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,YAAA,CAAe,CAAA,CAAA,CAAA,CAA2C,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CACrE,GAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CACG,MAAA,CAAO,CAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAW,OAAO,KAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAEjC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,OAAA,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAS,CAAA,CAChB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAqB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,MAAW,CACjC,CAAA,CAAA,CAAA,CAAA,CAAA,KAAA,CAAM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA,CAAS,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;IAEjC,CAAC,CACA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAK,CAAG,CAAA,CAAA,CAAA,CACR,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 6218, "column": 0}, "map": {"version": 3, "file": "defaultAttributes.js", "sources": ["file://C%3A/bloomgoo/gold-exchange-platform/frontend/node_modules/.pnpm/lucide-react%400.487.0_react%4019.1.0/node_modules/lucide-react/src/defaultAttributes.ts"], "sourcesContent": ["export default {\n  xmlns: 'http://www.w3.org/2000/svg',\n  width: 24,\n  height: 24,\n  viewBox: '0 0 24 24',\n  fill: 'none',\n  stroke: 'currentColor',\n  strokeWidth: 2,\n  strokeLinecap: 'round',\n  strokeLinejoin: 'round',\n};\n"], "names": [], "mappings": ";;;;;;;;AAAA,CAAe,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;IACb,CAAA,CAAA,CAAA,CAAA,CAAO,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;IACP,CAAA,CAAA,CAAA,CAAA,CAAO,EAAA,CAAA,CAAA,CAAA;IACP,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,EAAA,CAAA,CAAA,CAAA;IACR,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAS,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;IACT,CAAA,CAAA,CAAA,CAAM,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;IACN,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;IACR,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAa,EAAA,CAAA,CAAA;IACb,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAe,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;IACf,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAgB,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;AAClB,CAAA,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 6245, "column": 0}, "map": {"version": 3, "file": "Icon.js", "sources": ["file://C%3A/bloomgoo/gold-exchange-platform/frontend/node_modules/.pnpm/lucide-react%400.487.0_react%4019.1.0/node_modules/lucide-react/src/Icon.ts"], "sourcesContent": ["import { createElement, forwardRef } from 'react';\nimport defaultAttributes from './defaultAttributes';\nimport { IconNode, LucideProps } from './types';\nimport { mergeClasses } from '@lucide/shared';\n\ninterface IconComponentProps extends LucideProps {\n  iconNode: IconNode;\n}\n\n/**\n * Lucide icon component\n *\n * @component Icon\n * @param {object} props\n * @param {string} props.color - The color of the icon\n * @param {number} props.size - The size of the icon\n * @param {number} props.strokeWidth - The stroke width of the icon\n * @param {boolean} props.absoluteStrokeWidth - Whether to use absolute stroke width\n * @param {string} props.className - The class name of the icon\n * @param {IconNode} props.children - The children of the icon\n * @param {IconNode} props.iconNode - The icon node of the icon\n *\n * @returns {ForwardRefExoticComponent} LucideIcon\n */\nconst Icon = forwardRef<SVGSVGElement, IconComponentProps>(\n  (\n    {\n      color = 'currentColor',\n      size = 24,\n      strokeWidth = 2,\n      absoluteStrokeWidth,\n      className = '',\n      children,\n      iconNode,\n      ...rest\n    },\n    ref,\n  ) => {\n    return createElement(\n      'svg',\n      {\n        ref,\n        ...defaultAttributes,\n        width: size,\n        height: size,\n        stroke: color,\n        strokeWidth: absoluteStrokeWidth ? (Number(strokeWidth) * 24) / Number(size) : strokeWidth,\n        className: mergeClasses('lucide', className),\n        ...rest,\n      },\n      [\n        ...iconNode.map(([tag, attrs]) => createElement(tag, attrs)),\n        ...(Array.isArray(children) ? children : [children]),\n      ],\n    );\n  },\n);\n\nexport default Icon;\n"], "names": [], "mappings": ";;;;;;;;;;;;;;AAwBA,CAAM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAO,sTAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,KAAA,CAAA,CACX,CACE,CAAA,CACE,CAAA,CAAA,CAAA,CAAA,CAAQ,GAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CACR,CAAA,CAAA,CAAA,CAAO,GAAA,CAAA,CAAA,CAAA,CACP,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAc,GAAA,CAAA,CAAA,CACd,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CACA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAY,GAAA,CAAA,CAAA,CAAA,CACZ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CACA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CACA,GAAG,CAAA,CAAA,CAAA,CAAA,EAAA,EAEL,GACG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;IACI,0TAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,KAAA,CAAA,CACL,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CACA,CAAA;QACE,CAAA,CAAA,CAAA,CAAA;QACA,2PAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QACH,CAAA,CAAA,CAAA,CAAA,CAAO,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QACP,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QACR,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QACR,WAAA,CAAa,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAuB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAO,CAAA,CAAW,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAI,CAAA,CAAA,CAAA,CAAM,GAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAO,CAAI,CAAA,CAAA,CAAA,CAAI,GAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAC/E,SAAA,CAAW,kQAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,KAAA,AAAa,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAU,SAAS,CAAA,CAAA;QAC3C,GAAG,CAAA,CAAA,CAAA,CAAA;IACL,CAAA,CAAA,CACA,CAAA;WACK,CAAS,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,GAAA,CAAI,CAAC,CAAC,CAAK,CAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAM,sTAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,KAAA,EAAc,GAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAK,CAAC,CAAA,CAAA;WACvD,CAAA,CAAA,CAAA,CAAA,CAAM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAI,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAW;YAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ;SAAA;KACpD;AAEJ,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 6284, "column": 0}, "map": {"version": 3, "file": "createLucideIcon.js", "sources": ["file://C%3A/bloomgoo/gold-exchange-platform/frontend/node_modules/.pnpm/lucide-react%400.487.0_react%4019.1.0/node_modules/lucide-react/src/createLucideIcon.ts"], "sourcesContent": ["import { createElement, forwardRef } from 'react';\nimport { mergeClasses, toKebabCase, toPascalCase } from '@lucide/shared';\nimport { IconNode, LucideProps } from './types';\nimport Icon from './Icon';\n\n/**\n * Create a Lucide icon component\n * @param {string} iconName\n * @param {array} iconNode\n * @returns {ForwardRefExoticComponent} LucideIcon\n */\nconst createLucideIcon = (iconName: string, iconNode: IconNode) => {\n  const Component = forwardRef<SVGSVGElement, LucideProps>(({ className, ...props }, ref) =>\n    createElement(Icon, {\n      ref,\n      iconNode,\n      className: mergeClasses(\n        `lucide-${toKebabCase(toPascalCase(iconName))}`,\n        `lucide-${iconName}`,\n        className,\n      ),\n      ...props,\n    }),\n  );\n\n  Component.displayName = toPascalCase(iconName);\n\n  return Component;\n};\n\nexport default createLucideIcon;\n"], "names": [], "mappings": ";;;;;;;;;;;;;;AAWM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,gBAAA,CAAmB,CAAA,CAAA,CAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAkB,QAAuB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;IACjE,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,CAAY,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,oTAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,KAAA,CAAA,CAAuC,CAAC,CAAA,CAAE,CAAW,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAS,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,kTACjF,gBAAA,6OAAc,UAAM,CAAA,CAAA,CAAA;YAClB,CAAA,CAAA,CAAA,CAAA;YACA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YACA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAW,mQAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,KAAA,CAAA,CACT,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,kQAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,KAAA,mQAAY,CAAa,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,KAAA,EAAA,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAC,CAAC,CAAA,CAAA,CAAA,CAC7C,CAAA,OAAA,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA,CAAA,CAAA,CAClB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAEF,GAAG,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CACJ,CAAA;IAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,kQAAc,eAAA,EAAa,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA,CAAA;IAEtC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,SAAA,CAAA;AACT,CAAA,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 6316, "column": 0}, "map": {"version": 3, "file": "loader-circle.js", "sources": ["file://C%3A/bloomgoo/gold-exchange-platform/frontend/node_modules/.pnpm/lucide-react%400.487.0_react%4019.1.0/node_modules/lucide-react/src/icons/loader-circle.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [['path', { d: 'M21 12a9 9 0 1 1-6.219-8.56', key: '13zald' }]];\n\n/**\n * @component @name LoaderCircle\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMjEgMTJhOSA5IDAgMSAxLTYuMjE5LTguNTYiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/loader-circle\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst LoaderCircle = createLucideIcon('loader-circle', __iconNode);\n\nexport default LoaderCircle;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGa,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,UAAA,CAAuB,CAAA,CAAA;IAAC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ;QAAA,CAAE;YAAA,EAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA+B;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK,CAAS,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAC;KAAC;CAAA,CAAA;AAa5F,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,YAAA,CAAe,CAAA,4PAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAiB,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 6355, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/bloomgoo/gold-exchange-platform/frontend/node_modules/.pnpm/next-themes%400.4.6_react-dom_e207e685aa9cc81adf4eaedb8666d505/node_modules/next-themes/dist/index.mjs"], "sourcesContent": ["\"use client\";import*as t from\"react\";var M=(e,i,s,u,m,a,l,h)=>{let d=document.documentElement,w=[\"light\",\"dark\"];function p(n){(Array.isArray(e)?e:[e]).forEach(y=>{let k=y===\"class\",S=k&&a?m.map(f=>a[f]||f):m;k?(d.classList.remove(...S),d.classList.add(a&&a[n]?a[n]:n)):d.setAttribute(y,n)}),R(n)}function R(n){h&&w.includes(n)&&(d.style.colorScheme=n)}function c(){return window.matchMedia(\"(prefers-color-scheme: dark)\").matches?\"dark\":\"light\"}if(u)p(u);else try{let n=localStorage.getItem(i)||s,y=l&&n===\"system\"?c():n;p(y)}catch(n){}};var b=[\"light\",\"dark\"],I=\"(prefers-color-scheme: dark)\",O=typeof window==\"undefined\",x=t.createContext(void 0),U={setTheme:e=>{},themes:[]},z=()=>{var e;return(e=t.useContext(x))!=null?e:U},J=e=>t.useContext(x)?t.createElement(t.Fragment,null,e.children):t.createElement(V,{...e}),N=[\"light\",\"dark\"],V=({forcedTheme:e,disableTransitionOnChange:i=!1,enableSystem:s=!0,enableColorScheme:u=!0,storageKey:m=\"theme\",themes:a=N,defaultTheme:l=s?\"system\":\"light\",attribute:h=\"data-theme\",value:d,children:w,nonce:p,scriptProps:R})=>{let[c,n]=t.useState(()=>H(m,l)),[T,y]=t.useState(()=>c===\"system\"?E():c),k=d?Object.values(d):a,S=t.useCallback(o=>{let r=o;if(!r)return;o===\"system\"&&s&&(r=E());let v=d?d[r]:r,C=i?W(p):null,P=document.documentElement,L=g=>{g===\"class\"?(P.classList.remove(...k),v&&P.classList.add(v)):g.startsWith(\"data-\")&&(v?P.setAttribute(g,v):P.removeAttribute(g))};if(Array.isArray(h)?h.forEach(L):L(h),u){let g=b.includes(l)?l:null,D=b.includes(r)?r:g;P.style.colorScheme=D}C==null||C()},[p]),f=t.useCallback(o=>{let r=typeof o==\"function\"?o(c):o;n(r);try{localStorage.setItem(m,r)}catch(v){}},[c]),A=t.useCallback(o=>{let r=E(o);y(r),c===\"system\"&&s&&!e&&S(\"system\")},[c,e]);t.useEffect(()=>{let o=window.matchMedia(I);return o.addListener(A),A(o),()=>o.removeListener(A)},[A]),t.useEffect(()=>{let o=r=>{r.key===m&&(r.newValue?n(r.newValue):f(l))};return window.addEventListener(\"storage\",o),()=>window.removeEventListener(\"storage\",o)},[f]),t.useEffect(()=>{S(e!=null?e:c)},[e,c]);let Q=t.useMemo(()=>({theme:c,setTheme:f,forcedTheme:e,resolvedTheme:c===\"system\"?T:c,themes:s?[...a,\"system\"]:a,systemTheme:s?T:void 0}),[c,f,e,T,s,a]);return t.createElement(x.Provider,{value:Q},t.createElement(_,{forcedTheme:e,storageKey:m,attribute:h,enableSystem:s,enableColorScheme:u,defaultTheme:l,value:d,themes:a,nonce:p,scriptProps:R}),w)},_=t.memo(({forcedTheme:e,storageKey:i,attribute:s,enableSystem:u,enableColorScheme:m,defaultTheme:a,value:l,themes:h,nonce:d,scriptProps:w})=>{let p=JSON.stringify([s,i,a,e,h,l,u,m]).slice(1,-1);return t.createElement(\"script\",{...w,suppressHydrationWarning:!0,nonce:typeof window==\"undefined\"?d:\"\",dangerouslySetInnerHTML:{__html:`(${M.toString()})(${p})`}})}),H=(e,i)=>{if(O)return;let s;try{s=localStorage.getItem(e)||void 0}catch(u){}return s||i},W=e=>{let i=document.createElement(\"style\");return e&&i.setAttribute(\"nonce\",e),i.appendChild(document.createTextNode(\"*,*::before,*::after{-webkit-transition:none!important;-moz-transition:none!important;-o-transition:none!important;-ms-transition:none!important;transition:none!important}\")),document.head.appendChild(i),()=>{window.getComputedStyle(document.body),setTimeout(()=>{document.head.removeChild(i)},1)}},E=e=>(e||(e=window.matchMedia(I)),e.matches?\"dark\":\"light\");export{J as ThemeProvider,z as useTheme};\n"], "names": [], "mappings": ";;;;AAAa;AAAb;;AAAqC,IAAI,IAAE,CAAC,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE;IAAK,IAAI,IAAE,SAAS,eAAe,EAAC,IAAE;QAAC;QAAQ;KAAO;IAAC,SAAS,EAAE,CAAC;QAAE,CAAC,MAAM,OAAO,CAAC,KAAG,IAAE;YAAC;SAAE,EAAE,OAAO,CAAC,CAAA;YAAI,IAAI,IAAE,MAAI,SAAQ,IAAE,KAAG,IAAE,EAAE,GAAG,CAAC,CAAA,IAAG,CAAC,CAAC,EAAE,IAAE,KAAG;YAAE,IAAE,CAAC,EAAE,SAAS,CAAC,MAAM,IAAI,IAAG,EAAE,SAAS,CAAC,GAAG,CAAC,KAAG,CAAC,CAAC,EAAE,GAAC,CAAC,CAAC,EAAE,GAAC,EAAE,IAAE,EAAE,YAAY,CAAC,GAAE;QAAE,IAAG,EAAE;IAAE;IAAC,SAAS,EAAE,CAAC;QAAE,KAAG,EAAE,QAAQ,CAAC,MAAI,CAAC,EAAE,KAAK,CAAC,WAAW,GAAC,CAAC;IAAC;IAAC,SAAS;QAAI,OAAO,OAAO,UAAU,CAAC,gCAAgC,OAAO,GAAC,SAAO;IAAO;IAAC,IAAG,GAAE,EAAE;SAAQ,IAAG;QAAC,IAAI,IAAE,aAAa,OAAO,CAAC,MAAI,GAAE,IAAE,KAAG,MAAI,WAAS,MAAI;QAAE,EAAE;IAAE,EAAC,OAAM,GAAE,CAAC;AAAC;AAAE,IAAI,IAAE;IAAC;IAAQ;CAAO,EAAC,IAAE,gCAA+B,IAAE,OAAO,UAAQ,aAAY,IAAE,CAAA,GAAA,8SAAA,CAAA,gBAAe,AAAD,EAAE,KAAK,IAAG,IAAE;IAAC,UAAS,CAAA,KAAI;IAAE,QAAO,EAAE;AAAA,GAAE,IAAE;IAAK,IAAI;IAAE,OAAM,CAAC,IAAE,CAAA,GAAA,8SAAA,CAAA,aAAY,AAAD,EAAE,EAAE,KAAG,OAAK,IAAE;AAAC,GAAE,IAAE,CAAA,IAAG,CAAA,GAAA,8SAAA,CAAA,aAAY,AAAD,EAAE,KAAG,CAAA,GAAA,8SAAA,CAAA,gBAAe,AAAD,EAAE,8SAAA,CAAA,WAAU,EAAC,MAAK,EAAE,QAAQ,IAAE,CAAA,GAAA,8SAAA,CAAA,gBAAe,AAAD,EAAE,GAAE;QAAC,GAAG,CAAC;IAAA,IAAG,IAAE;IAAC;IAAQ;CAAO,EAAC,IAAE,CAAC,EAAC,aAAY,CAAC,EAAC,2BAA0B,IAAE,CAAC,CAAC,EAAC,cAAa,IAAE,CAAC,CAAC,EAAC,mBAAkB,IAAE,CAAC,CAAC,EAAC,YAAW,IAAE,OAAO,EAAC,QAAO,IAAE,CAAC,EAAC,cAAa,IAAE,IAAE,WAAS,OAAO,EAAC,WAAU,IAAE,YAAY,EAAC,OAAM,CAAC,EAAC,UAAS,CAAC,EAAC,OAAM,CAAC,EAAC,aAAY,CAAC,EAAC;IAAI,IAAG,CAAC,GAAE,EAAE,GAAC,CAAA,GAAA,8SAAA,CAAA,WAAU,AAAD,EAAE,IAAI,EAAE,GAAE,KAAI,CAAC,GAAE,EAAE,GAAC,CAAA,GAAA,8SAAA,CAAA,WAAU,AAAD,EAAE,IAAI,MAAI,WAAS,MAAI,IAAG,IAAE,IAAE,OAAO,MAAM,CAAC,KAAG,GAAE,IAAE,CAAA,GAAA,8SAAA,CAAA,cAAa,AAAD,EAAE,CAAA;QAAI,IAAI,IAAE;QAAE,IAAG,CAAC,GAAE;QAAO,MAAI,YAAU,KAAG,CAAC,IAAE,GAAG;QAAE,IAAI,IAAE,IAAE,CAAC,CAAC,EAAE,GAAC,GAAE,IAAE,IAAE,EAAE,KAAG,MAAK,IAAE,SAAS,eAAe,EAAC,IAAE,CAAA;YAAI,MAAI,UAAQ,CAAC,EAAE,SAAS,CAAC,MAAM,IAAI,IAAG,KAAG,EAAE,SAAS,CAAC,GAAG,CAAC,EAAE,IAAE,EAAE,UAAU,CAAC,YAAU,CAAC,IAAE,EAAE,YAAY,CAAC,GAAE,KAAG,EAAE,eAAe,CAAC,EAAE;QAAC;QAAE,IAAG,MAAM,OAAO,CAAC,KAAG,EAAE,OAAO,CAAC,KAAG,EAAE,IAAG,GAAE;YAAC,IAAI,IAAE,EAAE,QAAQ,CAAC,KAAG,IAAE,MAAK,IAAE,EAAE,QAAQ,CAAC,KAAG,IAAE;YAAE,EAAE,KAAK,CAAC,WAAW,GAAC;QAAC;QAAC,KAAG,QAAM;IAAG,GAAE;QAAC;KAAE,GAAE,IAAE,CAAA,GAAA,8SAAA,CAAA,cAAa,AAAD,EAAE,CAAA;QAAI,IAAI,IAAE,OAAO,KAAG,aAAW,EAAE,KAAG;QAAE,EAAE;QAAG,IAAG;YAAC,aAAa,OAAO,CAAC,GAAE;QAAE,EAAC,OAAM,GAAE,CAAC;IAAC,GAAE;QAAC;KAAE,GAAE,IAAE,CAAA,GAAA,8SAAA,CAAA,cAAa,AAAD,EAAE,CAAA;QAAI,IAAI,IAAE,EAAE;QAAG,EAAE,IAAG,MAAI,YAAU,KAAG,CAAC,KAAG,EAAE;IAAS,GAAE;QAAC;QAAE;KAAE;IAAE,CAAA,GAAA,8SAAA,CAAA,YAAW,AAAD,EAAE;QAAK,IAAI,IAAE,OAAO,UAAU,CAAC;QAAG,OAAO,EAAE,WAAW,CAAC,IAAG,EAAE,IAAG,IAAI,EAAE,cAAc,CAAC;IAAE,GAAE;QAAC;KAAE,GAAE,CAAA,GAAA,8SAAA,CAAA,YAAW,AAAD,EAAE;QAAK,IAAI,IAAE,CAAA;YAAI,EAAE,GAAG,KAAG,KAAG,CAAC,EAAE,QAAQ,GAAC,EAAE,EAAE,QAAQ,IAAE,EAAE,EAAE;QAAC;QAAE,OAAO,OAAO,gBAAgB,CAAC,WAAU,IAAG,IAAI,OAAO,mBAAmB,CAAC,WAAU;IAAE,GAAE;QAAC;KAAE,GAAE,CAAA,GAAA,8SAAA,CAAA,YAAW,AAAD,EAAE;QAAK,EAAE,KAAG,OAAK,IAAE;IAAE,GAAE;QAAC;QAAE;KAAE;IAAE,IAAI,IAAE,CAAA,GAAA,8SAAA,CAAA,UAAS,AAAD,EAAE,IAAI,CAAC;YAAC,OAAM;YAAE,UAAS;YAAE,aAAY;YAAE,eAAc,MAAI,WAAS,IAAE;YAAE,QAAO,IAAE;mBAAI;gBAAE;aAAS,GAAC;YAAE,aAAY,IAAE,IAAE,KAAK;QAAC,CAAC,GAAE;QAAC;QAAE;QAAE;QAAE;QAAE;QAAE;KAAE;IAAE,OAAO,CAAA,GAAA,8SAAA,CAAA,gBAAe,AAAD,EAAE,EAAE,QAAQ,EAAC;QAAC,OAAM;IAAC,GAAE,CAAA,GAAA,8SAAA,CAAA,gBAAe,AAAD,EAAE,GAAE;QAAC,aAAY;QAAE,YAAW;QAAE,WAAU;QAAE,cAAa;QAAE,mBAAkB;QAAE,cAAa;QAAE,OAAM;QAAE,QAAO;QAAE,OAAM;QAAE,aAAY;IAAC,IAAG;AAAE,GAAE,IAAE,CAAA,GAAA,8SAAA,CAAA,OAAM,AAAD,EAAE,CAAC,EAAC,aAAY,CAAC,EAAC,YAAW,CAAC,EAAC,WAAU,CAAC,EAAC,cAAa,CAAC,EAAC,mBAAkB,CAAC,EAAC,cAAa,CAAC,EAAC,OAAM,CAAC,EAAC,QAAO,CAAC,EAAC,OAAM,CAAC,EAAC,aAAY,CAAC,EAAC;IAAI,IAAI,IAAE,KAAK,SAAS,CAAC;QAAC;QAAE;QAAE;QAAE;QAAE;QAAE;QAAE;QAAE;KAAE,EAAE,KAAK,CAAC,GAAE,CAAC;IAAG,OAAO,CAAA,GAAA,8SAAA,CAAA,gBAAe,AAAD,EAAE,UAAS;QAAC,GAAG,CAAC;QAAC,0BAAyB,CAAC;QAAE,OAAM,OAAO,UAAQ,cAAY,IAAE;QAAG,yBAAwB;YAAC,QAAO,CAAC,CAAC,EAAE,EAAE,QAAQ,GAAG,EAAE,EAAE,EAAE,CAAC,CAAC;QAAA;IAAC;AAAE,IAAG,IAAE,CAAC,GAAE;IAAK,IAAG,GAAE;IAAO,IAAI;IAAE,IAAG;QAAC,IAAE,aAAa,OAAO,CAAC,MAAI,KAAK;IAAC,EAAC,OAAM,GAAE,CAAC;IAAC,OAAO,KAAG;AAAC,GAAE,IAAE,CAAA;IAAI,IAAI,IAAE,SAAS,aAAa,CAAC;IAAS,OAAO,KAAG,EAAE,YAAY,CAAC,SAAQ,IAAG,EAAE,WAAW,CAAC,SAAS,cAAc,CAAC,iLAAgL,SAAS,IAAI,CAAC,WAAW,CAAC,IAAG;QAAK,OAAO,gBAAgB,CAAC,SAAS,IAAI,GAAE,WAAW;YAAK,SAAS,IAAI,CAAC,WAAW,CAAC;QAAE,GAAE;IAAE;AAAC,GAAE,IAAE,CAAA,IAAG,CAAC,KAAG,CAAC,IAAE,OAAO,UAAU,CAAC,EAAE,GAAE,EAAE,OAAO,GAAC,SAAO,OAAO", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 6522, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/bloomgoo/gold-exchange-platform/frontend/node_modules/.pnpm/sonner%402.0.3_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0/node_modules/sonner/dist/index.mjs"], "sourcesContent": ["'use client';\nfunction __insertCSS(code) {\n  if (!code || typeof document == 'undefined') return\n  let head = document.head || document.getElementsByTagName('head')[0]\n  let style = document.createElement('style')\n  style.type = 'text/css'\n  head.appendChild(style)\n  ;style.styleSheet ? (style.styleSheet.cssText = code) : style.appendChild(document.createTextNode(code))\n}\n\nimport React from 'react';\nimport ReactDOM from 'react-dom';\n\nconst getAsset = (type)=>{\n    switch(type){\n        case 'success':\n            return SuccessIcon;\n        case 'info':\n            return InfoIcon;\n        case 'warning':\n            return WarningIcon;\n        case 'error':\n            return ErrorIcon;\n        default:\n            return null;\n    }\n};\nconst bars = Array(12).fill(0);\nconst Loader = ({ visible, className })=>{\n    return /*#__PURE__*/ React.createElement(\"div\", {\n        className: [\n            'sonner-loading-wrapper',\n            className\n        ].filter(Boolean).join(' '),\n        \"data-visible\": visible\n    }, /*#__PURE__*/ React.createElement(\"div\", {\n        className: \"sonner-spinner\"\n    }, bars.map((_, i)=>/*#__PURE__*/ React.createElement(\"div\", {\n            className: \"sonner-loading-bar\",\n            key: `spinner-bar-${i}`\n        }))));\n};\nconst SuccessIcon = /*#__PURE__*/ React.createElement(\"svg\", {\n    xmlns: \"http://www.w3.org/2000/svg\",\n    viewBox: \"0 0 20 20\",\n    fill: \"currentColor\",\n    height: \"20\",\n    width: \"20\"\n}, /*#__PURE__*/ React.createElement(\"path\", {\n    fillRule: \"evenodd\",\n    d: \"M10 18a8 8 0 100-16 8 8 0 000 16zm3.857-9.809a.75.75 0 00-1.214-.882l-3.483 4.79-1.88-1.88a.75.75 0 10-1.06 1.061l2.5 2.5a.75.75 0 001.137-.089l4-5.5z\",\n    clipRule: \"evenodd\"\n}));\nconst WarningIcon = /*#__PURE__*/ React.createElement(\"svg\", {\n    xmlns: \"http://www.w3.org/2000/svg\",\n    viewBox: \"0 0 24 24\",\n    fill: \"currentColor\",\n    height: \"20\",\n    width: \"20\"\n}, /*#__PURE__*/ React.createElement(\"path\", {\n    fillRule: \"evenodd\",\n    d: \"M9.401 3.003c1.155-2 4.043-2 5.197 0l7.355 12.748c1.154 2-.29 4.5-2.599 4.5H4.645c-2.309 0-3.752-2.5-2.598-4.5L9.4 3.003zM12 8.25a.75.75 0 01.75.75v3.75a.75.75 0 01-1.5 0V9a.75.75 0 01.75-.75zm0 8.25a.75.75 0 100-********* 0 000 1.5z\",\n    clipRule: \"evenodd\"\n}));\nconst InfoIcon = /*#__PURE__*/ React.createElement(\"svg\", {\n    xmlns: \"http://www.w3.org/2000/svg\",\n    viewBox: \"0 0 20 20\",\n    fill: \"currentColor\",\n    height: \"20\",\n    width: \"20\"\n}, /*#__PURE__*/ React.createElement(\"path\", {\n    fillRule: \"evenodd\",\n    d: \"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a.75.75 0 000 1.5h.253a.25.25 0 01.244.304l-.459 2.066A1.75 1.75 0 0010.747 15H11a.75.75 0 000-1.5h-.253a.25.25 0 01-.244-.304l.459-2.066A1.75 1.75 0 009.253 9H9z\",\n    clipRule: \"evenodd\"\n}));\nconst ErrorIcon = /*#__PURE__*/ React.createElement(\"svg\", {\n    xmlns: \"http://www.w3.org/2000/svg\",\n    viewBox: \"0 0 20 20\",\n    fill: \"currentColor\",\n    height: \"20\",\n    width: \"20\"\n}, /*#__PURE__*/ React.createElement(\"path\", {\n    fillRule: \"evenodd\",\n    d: \"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-8-5a.75.75 0 01.75.75v4.5a.75.75 0 01-1.5 0v-4.5A.75.75 0 0110 5zm0 10a1 1 0 100-2 1 1 0 000 2z\",\n    clipRule: \"evenodd\"\n}));\nconst CloseIcon = /*#__PURE__*/ React.createElement(\"svg\", {\n    xmlns: \"http://www.w3.org/2000/svg\",\n    width: \"12\",\n    height: \"12\",\n    viewBox: \"0 0 24 24\",\n    fill: \"none\",\n    stroke: \"currentColor\",\n    strokeWidth: \"1.5\",\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\"\n}, /*#__PURE__*/ React.createElement(\"line\", {\n    x1: \"18\",\n    y1: \"6\",\n    x2: \"6\",\n    y2: \"18\"\n}), /*#__PURE__*/ React.createElement(\"line\", {\n    x1: \"6\",\n    y1: \"6\",\n    x2: \"18\",\n    y2: \"18\"\n}));\n\nconst useIsDocumentHidden = ()=>{\n    const [isDocumentHidden, setIsDocumentHidden] = React.useState(document.hidden);\n    React.useEffect(()=>{\n        const callback = ()=>{\n            setIsDocumentHidden(document.hidden);\n        };\n        document.addEventListener('visibilitychange', callback);\n        return ()=>window.removeEventListener('visibilitychange', callback);\n    }, []);\n    return isDocumentHidden;\n};\n\nlet toastsCounter = 1;\nclass Observer {\n    constructor(){\n        // We use arrow functions to maintain the correct `this` reference\n        this.subscribe = (subscriber)=>{\n            this.subscribers.push(subscriber);\n            return ()=>{\n                const index = this.subscribers.indexOf(subscriber);\n                this.subscribers.splice(index, 1);\n            };\n        };\n        this.publish = (data)=>{\n            this.subscribers.forEach((subscriber)=>subscriber(data));\n        };\n        this.addToast = (data)=>{\n            this.publish(data);\n            this.toasts = [\n                ...this.toasts,\n                data\n            ];\n        };\n        this.create = (data)=>{\n            var _data_id;\n            const { message, ...rest } = data;\n            const id = typeof (data == null ? void 0 : data.id) === 'number' || ((_data_id = data.id) == null ? void 0 : _data_id.length) > 0 ? data.id : toastsCounter++;\n            const alreadyExists = this.toasts.find((toast)=>{\n                return toast.id === id;\n            });\n            const dismissible = data.dismissible === undefined ? true : data.dismissible;\n            if (this.dismissedToasts.has(id)) {\n                this.dismissedToasts.delete(id);\n            }\n            if (alreadyExists) {\n                this.toasts = this.toasts.map((toast)=>{\n                    if (toast.id === id) {\n                        this.publish({\n                            ...toast,\n                            ...data,\n                            id,\n                            title: message\n                        });\n                        return {\n                            ...toast,\n                            ...data,\n                            id,\n                            dismissible,\n                            title: message\n                        };\n                    }\n                    return toast;\n                });\n            } else {\n                this.addToast({\n                    title: message,\n                    ...rest,\n                    dismissible,\n                    id\n                });\n            }\n            return id;\n        };\n        this.dismiss = (id)=>{\n            if (id) {\n                this.dismissedToasts.add(id);\n                requestAnimationFrame(()=>this.subscribers.forEach((subscriber)=>subscriber({\n                            id,\n                            dismiss: true\n                        })));\n            } else {\n                this.toasts.forEach((toast)=>{\n                    this.subscribers.forEach((subscriber)=>subscriber({\n                            id: toast.id,\n                            dismiss: true\n                        }));\n                });\n            }\n            return id;\n        };\n        this.message = (message, data)=>{\n            return this.create({\n                ...data,\n                message\n            });\n        };\n        this.error = (message, data)=>{\n            return this.create({\n                ...data,\n                message,\n                type: 'error'\n            });\n        };\n        this.success = (message, data)=>{\n            return this.create({\n                ...data,\n                type: 'success',\n                message\n            });\n        };\n        this.info = (message, data)=>{\n            return this.create({\n                ...data,\n                type: 'info',\n                message\n            });\n        };\n        this.warning = (message, data)=>{\n            return this.create({\n                ...data,\n                type: 'warning',\n                message\n            });\n        };\n        this.loading = (message, data)=>{\n            return this.create({\n                ...data,\n                type: 'loading',\n                message\n            });\n        };\n        this.promise = (promise, data)=>{\n            if (!data) {\n                // Nothing to show\n                return;\n            }\n            let id = undefined;\n            if (data.loading !== undefined) {\n                id = this.create({\n                    ...data,\n                    promise,\n                    type: 'loading',\n                    message: data.loading,\n                    description: typeof data.description !== 'function' ? data.description : undefined\n                });\n            }\n            const p = Promise.resolve(promise instanceof Function ? promise() : promise);\n            let shouldDismiss = id !== undefined;\n            let result;\n            const originalPromise = p.then(async (response)=>{\n                result = [\n                    'resolve',\n                    response\n                ];\n                const isReactElementResponse = React.isValidElement(response);\n                if (isReactElementResponse) {\n                    shouldDismiss = false;\n                    this.create({\n                        id,\n                        type: 'default',\n                        message: response\n                    });\n                } else if (isHttpResponse(response) && !response.ok) {\n                    shouldDismiss = false;\n                    const promiseData = typeof data.error === 'function' ? await data.error(`HTTP error! status: ${response.status}`) : data.error;\n                    const description = typeof data.description === 'function' ? await data.description(`HTTP error! status: ${response.status}`) : data.description;\n                    const isExtendedResult = typeof promiseData === 'object' && !React.isValidElement(promiseData);\n                    const toastSettings = isExtendedResult ? promiseData : {\n                        message: promiseData\n                    };\n                    this.create({\n                        id,\n                        type: 'error',\n                        description,\n                        ...toastSettings\n                    });\n                } else if (response instanceof Error) {\n                    shouldDismiss = false;\n                    const promiseData = typeof data.error === 'function' ? await data.error(response) : data.error;\n                    const description = typeof data.description === 'function' ? await data.description(response) : data.description;\n                    const isExtendedResult = typeof promiseData === 'object' && !React.isValidElement(promiseData);\n                    const toastSettings = isExtendedResult ? promiseData : {\n                        message: promiseData\n                    };\n                    this.create({\n                        id,\n                        type: 'error',\n                        description,\n                        ...toastSettings\n                    });\n                } else if (data.success !== undefined) {\n                    shouldDismiss = false;\n                    const promiseData = typeof data.success === 'function' ? await data.success(response) : data.success;\n                    const description = typeof data.description === 'function' ? await data.description(response) : data.description;\n                    const isExtendedResult = typeof promiseData === 'object' && !React.isValidElement(promiseData);\n                    const toastSettings = isExtendedResult ? promiseData : {\n                        message: promiseData\n                    };\n                    this.create({\n                        id,\n                        type: 'success',\n                        description,\n                        ...toastSettings\n                    });\n                }\n            }).catch(async (error)=>{\n                result = [\n                    'reject',\n                    error\n                ];\n                if (data.error !== undefined) {\n                    shouldDismiss = false;\n                    const promiseData = typeof data.error === 'function' ? await data.error(error) : data.error;\n                    const description = typeof data.description === 'function' ? await data.description(error) : data.description;\n                    const isExtendedResult = typeof promiseData === 'object' && !React.isValidElement(promiseData);\n                    const toastSettings = isExtendedResult ? promiseData : {\n                        message: promiseData\n                    };\n                    this.create({\n                        id,\n                        type: 'error',\n                        description,\n                        ...toastSettings\n                    });\n                }\n            }).finally(()=>{\n                if (shouldDismiss) {\n                    // Toast is still in load state (and will be indefinitely — dismiss it)\n                    this.dismiss(id);\n                    id = undefined;\n                }\n                data.finally == null ? void 0 : data.finally.call(data);\n            });\n            const unwrap = ()=>new Promise((resolve, reject)=>originalPromise.then(()=>result[0] === 'reject' ? reject(result[1]) : resolve(result[1])).catch(reject));\n            if (typeof id !== 'string' && typeof id !== 'number') {\n                // cannot Object.assign on undefined\n                return {\n                    unwrap\n                };\n            } else {\n                return Object.assign(id, {\n                    unwrap\n                });\n            }\n        };\n        this.custom = (jsx, data)=>{\n            const id = (data == null ? void 0 : data.id) || toastsCounter++;\n            this.create({\n                jsx: jsx(id),\n                id,\n                ...data\n            });\n            return id;\n        };\n        this.getActiveToasts = ()=>{\n            return this.toasts.filter((toast)=>!this.dismissedToasts.has(toast.id));\n        };\n        this.subscribers = [];\n        this.toasts = [];\n        this.dismissedToasts = new Set();\n    }\n}\nconst ToastState = new Observer();\n// bind this to the toast function\nconst toastFunction = (message, data)=>{\n    const id = (data == null ? void 0 : data.id) || toastsCounter++;\n    ToastState.addToast({\n        title: message,\n        ...data,\n        id\n    });\n    return id;\n};\nconst isHttpResponse = (data)=>{\n    return data && typeof data === 'object' && 'ok' in data && typeof data.ok === 'boolean' && 'status' in data && typeof data.status === 'number';\n};\nconst basicToast = toastFunction;\nconst getHistory = ()=>ToastState.toasts;\nconst getToasts = ()=>ToastState.getActiveToasts();\n// We use `Object.assign` to maintain the correct types as we would lose them otherwise\nconst toast = Object.assign(basicToast, {\n    success: ToastState.success,\n    info: ToastState.info,\n    warning: ToastState.warning,\n    error: ToastState.error,\n    custom: ToastState.custom,\n    message: ToastState.message,\n    promise: ToastState.promise,\n    dismiss: ToastState.dismiss,\n    loading: ToastState.loading\n}, {\n    getHistory,\n    getToasts\n});\n\n__insertCSS(\"[data-sonner-toaster][dir=ltr],html[dir=ltr]{--toast-icon-margin-start:-3px;--toast-icon-margin-end:4px;--toast-svg-margin-start:-1px;--toast-svg-margin-end:0px;--toast-button-margin-start:auto;--toast-button-margin-end:0;--toast-close-button-start:0;--toast-close-button-end:unset;--toast-close-button-transform:translate(-35%, -35%)}[data-sonner-toaster][dir=rtl],html[dir=rtl]{--toast-icon-margin-start:4px;--toast-icon-margin-end:-3px;--toast-svg-margin-start:0px;--toast-svg-margin-end:-1px;--toast-button-margin-start:0;--toast-button-margin-end:auto;--toast-close-button-start:unset;--toast-close-button-end:0;--toast-close-button-transform:translate(35%, -35%)}[data-sonner-toaster]{position:fixed;width:var(--width);font-family:ui-sans-serif,system-ui,-apple-system,BlinkMacSystemFont,Segoe UI,Roboto,Helvetica Neue,Arial,Noto Sans,sans-serif,Apple Color Emoji,Segoe UI Emoji,Segoe UI Symbol,Noto Color Emoji;--gray1:hsl(0, 0%, 99%);--gray2:hsl(0, 0%, 97.3%);--gray3:hsl(0, 0%, 95.1%);--gray4:hsl(0, 0%, 93%);--gray5:hsl(0, 0%, 90.9%);--gray6:hsl(0, 0%, 88.7%);--gray7:hsl(0, 0%, 85.8%);--gray8:hsl(0, 0%, 78%);--gray9:hsl(0, 0%, 56.1%);--gray10:hsl(0, 0%, 52.3%);--gray11:hsl(0, 0%, 43.5%);--gray12:hsl(0, 0%, 9%);--border-radius:8px;box-sizing:border-box;padding:0;margin:0;list-style:none;outline:0;z-index:999999999;transition:transform .4s ease}[data-sonner-toaster][data-lifted=true]{transform:translateY(-8px)}@media (hover:none) and (pointer:coarse){[data-sonner-toaster][data-lifted=true]{transform:none}}[data-sonner-toaster][data-x-position=right]{right:var(--offset-right)}[data-sonner-toaster][data-x-position=left]{left:var(--offset-left)}[data-sonner-toaster][data-x-position=center]{left:50%;transform:translateX(-50%)}[data-sonner-toaster][data-y-position=top]{top:var(--offset-top)}[data-sonner-toaster][data-y-position=bottom]{bottom:var(--offset-bottom)}[data-sonner-toast]{--y:translateY(100%);--lift-amount:calc(var(--lift) * var(--gap));z-index:var(--z-index);position:absolute;opacity:0;transform:var(--y);touch-action:none;transition:transform .4s,opacity .4s,height .4s,box-shadow .2s;box-sizing:border-box;outline:0;overflow-wrap:anywhere}[data-sonner-toast][data-styled=true]{padding:16px;background:var(--normal-bg);border:1px solid var(--normal-border);color:var(--normal-text);border-radius:var(--border-radius);box-shadow:0 4px 12px rgba(0,0,0,.1);width:var(--width);font-size:13px;display:flex;align-items:center;gap:6px}[data-sonner-toast]:focus-visible{box-shadow:0 4px 12px rgba(0,0,0,.1),0 0 0 2px rgba(0,0,0,.2)}[data-sonner-toast][data-y-position=top]{top:0;--y:translateY(-100%);--lift:1;--lift-amount:calc(1 * var(--gap))}[data-sonner-toast][data-y-position=bottom]{bottom:0;--y:translateY(100%);--lift:-1;--lift-amount:calc(var(--lift) * var(--gap))}[data-sonner-toast][data-styled=true] [data-description]{font-weight:400;line-height:1.4;color:#3f3f3f}[data-rich-colors=true][data-sonner-toast][data-styled=true] [data-description]{color:inherit}[data-sonner-toaster][data-sonner-theme=dark] [data-description]{color:#e8e8e8}[data-sonner-toast][data-styled=true] [data-title]{font-weight:500;line-height:1.5;color:inherit}[data-sonner-toast][data-styled=true] [data-icon]{display:flex;height:16px;width:16px;position:relative;justify-content:flex-start;align-items:center;flex-shrink:0;margin-left:var(--toast-icon-margin-start);margin-right:var(--toast-icon-margin-end)}[data-sonner-toast][data-promise=true] [data-icon]>svg{opacity:0;transform:scale(.8);transform-origin:center;animation:sonner-fade-in .3s ease forwards}[data-sonner-toast][data-styled=true] [data-icon]>*{flex-shrink:0}[data-sonner-toast][data-styled=true] [data-icon] svg{margin-left:var(--toast-svg-margin-start);margin-right:var(--toast-svg-margin-end)}[data-sonner-toast][data-styled=true] [data-content]{display:flex;flex-direction:column;gap:2px}[data-sonner-toast][data-styled=true] [data-button]{border-radius:4px;padding-left:8px;padding-right:8px;height:24px;font-size:12px;color:var(--normal-bg);background:var(--normal-text);margin-left:var(--toast-button-margin-start);margin-right:var(--toast-button-margin-end);border:none;font-weight:500;cursor:pointer;outline:0;display:flex;align-items:center;flex-shrink:0;transition:opacity .4s,box-shadow .2s}[data-sonner-toast][data-styled=true] [data-button]:focus-visible{box-shadow:0 0 0 2px rgba(0,0,0,.4)}[data-sonner-toast][data-styled=true] [data-button]:first-of-type{margin-left:var(--toast-button-margin-start);margin-right:var(--toast-button-margin-end)}[data-sonner-toast][data-styled=true] [data-cancel]{color:var(--normal-text);background:rgba(0,0,0,.08)}[data-sonner-toaster][data-sonner-theme=dark] [data-sonner-toast][data-styled=true] [data-cancel]{background:rgba(255,255,255,.3)}[data-sonner-toast][data-styled=true] [data-close-button]{position:absolute;left:var(--toast-close-button-start);right:var(--toast-close-button-end);top:0;height:20px;width:20px;display:flex;justify-content:center;align-items:center;padding:0;color:var(--gray12);background:var(--normal-bg);border:1px solid var(--gray4);transform:var(--toast-close-button-transform);border-radius:50%;cursor:pointer;z-index:1;transition:opacity .1s,background .2s,border-color .2s}[data-sonner-toast][data-styled=true] [data-close-button]:focus-visible{box-shadow:0 4px 12px rgba(0,0,0,.1),0 0 0 2px rgba(0,0,0,.2)}[data-sonner-toast][data-styled=true] [data-disabled=true]{cursor:not-allowed}[data-sonner-toast][data-styled=true]:hover [data-close-button]:hover{background:var(--gray2);border-color:var(--gray5)}[data-sonner-toast][data-swiping=true]::before{content:'';position:absolute;left:-100%;right:-100%;height:100%;z-index:-1}[data-sonner-toast][data-y-position=top][data-swiping=true]::before{bottom:50%;transform:scaleY(3) translateY(50%)}[data-sonner-toast][data-y-position=bottom][data-swiping=true]::before{top:50%;transform:scaleY(3) translateY(-50%)}[data-sonner-toast][data-swiping=false][data-removed=true]::before{content:'';position:absolute;inset:0;transform:scaleY(2)}[data-sonner-toast][data-expanded=true]::after{content:'';position:absolute;left:0;height:calc(var(--gap) + 1px);bottom:100%;width:100%}[data-sonner-toast][data-mounted=true]{--y:translateY(0);opacity:1}[data-sonner-toast][data-expanded=false][data-front=false]{--scale:var(--toasts-before) * 0.05 + 1;--y:translateY(calc(var(--lift-amount) * var(--toasts-before))) scale(calc(-1 * var(--scale)));height:var(--front-toast-height)}[data-sonner-toast]>*{transition:opacity .4s}[data-sonner-toast][data-x-position=right]{right:0}[data-sonner-toast][data-x-position=left]{left:0}[data-sonner-toast][data-expanded=false][data-front=false][data-styled=true]>*{opacity:0}[data-sonner-toast][data-visible=false]{opacity:0;pointer-events:none}[data-sonner-toast][data-mounted=true][data-expanded=true]{--y:translateY(calc(var(--lift) * var(--offset)));height:var(--initial-height)}[data-sonner-toast][data-removed=true][data-front=true][data-swipe-out=false]{--y:translateY(calc(var(--lift) * -100%));opacity:0}[data-sonner-toast][data-removed=true][data-front=false][data-swipe-out=false][data-expanded=true]{--y:translateY(calc(var(--lift) * var(--offset) + var(--lift) * -100%));opacity:0}[data-sonner-toast][data-removed=true][data-front=false][data-swipe-out=false][data-expanded=false]{--y:translateY(40%);opacity:0;transition:transform .5s,opacity .2s}[data-sonner-toast][data-removed=true][data-front=false]::before{height:calc(var(--initial-height) + 20%)}[data-sonner-toast][data-swiping=true]{transform:var(--y) translateY(var(--swipe-amount-y,0)) translateX(var(--swipe-amount-x,0));transition:none}[data-sonner-toast][data-swiped=true]{user-select:none}[data-sonner-toast][data-swipe-out=true][data-y-position=bottom],[data-sonner-toast][data-swipe-out=true][data-y-position=top]{animation-duration:.2s;animation-timing-function:ease-out;animation-fill-mode:forwards}[data-sonner-toast][data-swipe-out=true][data-swipe-direction=left]{animation-name:swipe-out-left}[data-sonner-toast][data-swipe-out=true][data-swipe-direction=right]{animation-name:swipe-out-right}[data-sonner-toast][data-swipe-out=true][data-swipe-direction=up]{animation-name:swipe-out-up}[data-sonner-toast][data-swipe-out=true][data-swipe-direction=down]{animation-name:swipe-out-down}@keyframes swipe-out-left{from{transform:var(--y) translateX(var(--swipe-amount-x));opacity:1}to{transform:var(--y) translateX(calc(var(--swipe-amount-x) - 100%));opacity:0}}@keyframes swipe-out-right{from{transform:var(--y) translateX(var(--swipe-amount-x));opacity:1}to{transform:var(--y) translateX(calc(var(--swipe-amount-x) + 100%));opacity:0}}@keyframes swipe-out-up{from{transform:var(--y) translateY(var(--swipe-amount-y));opacity:1}to{transform:var(--y) translateY(calc(var(--swipe-amount-y) - 100%));opacity:0}}@keyframes swipe-out-down{from{transform:var(--y) translateY(var(--swipe-amount-y));opacity:1}to{transform:var(--y) translateY(calc(var(--swipe-amount-y) + 100%));opacity:0}}@media (max-width:600px){[data-sonner-toaster]{position:fixed;right:var(--mobile-offset-right);left:var(--mobile-offset-left);width:100%}[data-sonner-toaster][dir=rtl]{left:calc(var(--mobile-offset-left) * -1)}[data-sonner-toaster] [data-sonner-toast]{left:0;right:0;width:calc(100% - var(--mobile-offset-left) * 2)}[data-sonner-toaster][data-x-position=left]{left:var(--mobile-offset-left)}[data-sonner-toaster][data-y-position=bottom]{bottom:var(--mobile-offset-bottom)}[data-sonner-toaster][data-y-position=top]{top:var(--mobile-offset-top)}[data-sonner-toaster][data-x-position=center]{left:var(--mobile-offset-left);right:var(--mobile-offset-right);transform:none}}[data-sonner-toaster][data-sonner-theme=light]{--normal-bg:#fff;--normal-border:var(--gray4);--normal-text:var(--gray12);--success-bg:hsl(143, 85%, 96%);--success-border:hsl(145, 92%, 87%);--success-text:hsl(140, 100%, 27%);--info-bg:hsl(208, 100%, 97%);--info-border:hsl(221, 91%, 93%);--info-text:hsl(210, 92%, 45%);--warning-bg:hsl(49, 100%, 97%);--warning-border:hsl(49, 91%, 84%);--warning-text:hsl(31, 92%, 45%);--error-bg:hsl(359, 100%, 97%);--error-border:hsl(359, 100%, 94%);--error-text:hsl(360, 100%, 45%)}[data-sonner-toaster][data-sonner-theme=light] [data-sonner-toast][data-invert=true]{--normal-bg:#000;--normal-border:hsl(0, 0%, 20%);--normal-text:var(--gray1)}[data-sonner-toaster][data-sonner-theme=dark] [data-sonner-toast][data-invert=true]{--normal-bg:#fff;--normal-border:var(--gray3);--normal-text:var(--gray12)}[data-sonner-toaster][data-sonner-theme=dark]{--normal-bg:#000;--normal-bg-hover:hsl(0, 0%, 12%);--normal-border:hsl(0, 0%, 20%);--normal-border-hover:hsl(0, 0%, 25%);--normal-text:var(--gray1);--success-bg:hsl(150, 100%, 6%);--success-border:hsl(147, 100%, 12%);--success-text:hsl(150, 86%, 65%);--info-bg:hsl(215, 100%, 6%);--info-border:hsl(223, 43%, 17%);--info-text:hsl(216, 87%, 65%);--warning-bg:hsl(64, 100%, 6%);--warning-border:hsl(60, 100%, 9%);--warning-text:hsl(46, 87%, 65%);--error-bg:hsl(358, 76%, 10%);--error-border:hsl(357, 89%, 16%);--error-text:hsl(358, 100%, 81%)}[data-sonner-toaster][data-sonner-theme=dark] [data-sonner-toast] [data-close-button]{background:var(--normal-bg);border-color:var(--normal-border);color:var(--normal-text)}[data-sonner-toaster][data-sonner-theme=dark] [data-sonner-toast] [data-close-button]:hover{background:var(--normal-bg-hover);border-color:var(--normal-border-hover)}[data-rich-colors=true][data-sonner-toast][data-type=success]{background:var(--success-bg);border-color:var(--success-border);color:var(--success-text)}[data-rich-colors=true][data-sonner-toast][data-type=success] [data-close-button]{background:var(--success-bg);border-color:var(--success-border);color:var(--success-text)}[data-rich-colors=true][data-sonner-toast][data-type=info]{background:var(--info-bg);border-color:var(--info-border);color:var(--info-text)}[data-rich-colors=true][data-sonner-toast][data-type=info] [data-close-button]{background:var(--info-bg);border-color:var(--info-border);color:var(--info-text)}[data-rich-colors=true][data-sonner-toast][data-type=warning]{background:var(--warning-bg);border-color:var(--warning-border);color:var(--warning-text)}[data-rich-colors=true][data-sonner-toast][data-type=warning] [data-close-button]{background:var(--warning-bg);border-color:var(--warning-border);color:var(--warning-text)}[data-rich-colors=true][data-sonner-toast][data-type=error]{background:var(--error-bg);border-color:var(--error-border);color:var(--error-text)}[data-rich-colors=true][data-sonner-toast][data-type=error] [data-close-button]{background:var(--error-bg);border-color:var(--error-border);color:var(--error-text)}.sonner-loading-wrapper{--size:16px;height:var(--size);width:var(--size);position:absolute;inset:0;z-index:10}.sonner-loading-wrapper[data-visible=false]{transform-origin:center;animation:sonner-fade-out .2s ease forwards}.sonner-spinner{position:relative;top:50%;left:50%;height:var(--size);width:var(--size)}.sonner-loading-bar{animation:sonner-spin 1.2s linear infinite;background:var(--gray11);border-radius:6px;height:8%;left:-10%;position:absolute;top:-3.9%;width:24%}.sonner-loading-bar:first-child{animation-delay:-1.2s;transform:rotate(.0001deg) translate(146%)}.sonner-loading-bar:nth-child(2){animation-delay:-1.1s;transform:rotate(30deg) translate(146%)}.sonner-loading-bar:nth-child(3){animation-delay:-1s;transform:rotate(60deg) translate(146%)}.sonner-loading-bar:nth-child(4){animation-delay:-.9s;transform:rotate(90deg) translate(146%)}.sonner-loading-bar:nth-child(5){animation-delay:-.8s;transform:rotate(120deg) translate(146%)}.sonner-loading-bar:nth-child(6){animation-delay:-.7s;transform:rotate(150deg) translate(146%)}.sonner-loading-bar:nth-child(7){animation-delay:-.6s;transform:rotate(180deg) translate(146%)}.sonner-loading-bar:nth-child(8){animation-delay:-.5s;transform:rotate(210deg) translate(146%)}.sonner-loading-bar:nth-child(9){animation-delay:-.4s;transform:rotate(240deg) translate(146%)}.sonner-loading-bar:nth-child(10){animation-delay:-.3s;transform:rotate(270deg) translate(146%)}.sonner-loading-bar:nth-child(11){animation-delay:-.2s;transform:rotate(300deg) translate(146%)}.sonner-loading-bar:nth-child(12){animation-delay:-.1s;transform:rotate(330deg) translate(146%)}@keyframes sonner-fade-in{0%{opacity:0;transform:scale(.8)}100%{opacity:1;transform:scale(1)}}@keyframes sonner-fade-out{0%{opacity:1;transform:scale(1)}100%{opacity:0;transform:scale(.8)}}@keyframes sonner-spin{0%{opacity:1}100%{opacity:.15}}@media (prefers-reduced-motion){.sonner-loading-bar,[data-sonner-toast],[data-sonner-toast]>*{transition:none!important;animation:none!important}}.sonner-loader{position:absolute;top:50%;left:50%;transform:translate(-50%,-50%);transform-origin:center;transition:opacity .2s,transform .2s}.sonner-loader[data-visible=false]{opacity:0;transform:scale(.8) translate(-50%,-50%)}\");\n\nfunction isAction(action) {\n    return action.label !== undefined;\n}\n\n// Visible toasts amount\nconst VISIBLE_TOASTS_AMOUNT = 3;\n// Viewport padding\nconst VIEWPORT_OFFSET = '24px';\n// Mobile viewport padding\nconst MOBILE_VIEWPORT_OFFSET = '16px';\n// Default lifetime of a toasts (in ms)\nconst TOAST_LIFETIME = 4000;\n// Default toast width\nconst TOAST_WIDTH = 356;\n// Default gap between toasts\nconst GAP = 14;\n// Threshold to dismiss a toast\nconst SWIPE_THRESHOLD = 45;\n// Equal to exit animation duration\nconst TIME_BEFORE_UNMOUNT = 200;\nfunction cn(...classes) {\n    return classes.filter(Boolean).join(' ');\n}\nfunction getDefaultSwipeDirections(position) {\n    const [y, x] = position.split('-');\n    const directions = [];\n    if (y) {\n        directions.push(y);\n    }\n    if (x) {\n        directions.push(x);\n    }\n    return directions;\n}\nconst Toast = (props)=>{\n    var _toast_classNames, _toast_classNames1, _toast_classNames2, _toast_classNames3, _toast_classNames4, _toast_classNames5, _toast_classNames6, _toast_classNames7, _toast_classNames8;\n    const { invert: ToasterInvert, toast, unstyled, interacting, setHeights, visibleToasts, heights, index, toasts, expanded, removeToast, defaultRichColors, closeButton: closeButtonFromToaster, style, cancelButtonStyle, actionButtonStyle, className = '', descriptionClassName = '', duration: durationFromToaster, position, gap, expandByDefault, classNames, icons, closeButtonAriaLabel = 'Close toast' } = props;\n    const [swipeDirection, setSwipeDirection] = React.useState(null);\n    const [swipeOutDirection, setSwipeOutDirection] = React.useState(null);\n    const [mounted, setMounted] = React.useState(false);\n    const [removed, setRemoved] = React.useState(false);\n    const [swiping, setSwiping] = React.useState(false);\n    const [swipeOut, setSwipeOut] = React.useState(false);\n    const [isSwiped, setIsSwiped] = React.useState(false);\n    const [offsetBeforeRemove, setOffsetBeforeRemove] = React.useState(0);\n    const [initialHeight, setInitialHeight] = React.useState(0);\n    const remainingTime = React.useRef(toast.duration || durationFromToaster || TOAST_LIFETIME);\n    const dragStartTime = React.useRef(null);\n    const toastRef = React.useRef(null);\n    const isFront = index === 0;\n    const isVisible = index + 1 <= visibleToasts;\n    const toastType = toast.type;\n    const dismissible = toast.dismissible !== false;\n    const toastClassname = toast.className || '';\n    const toastDescriptionClassname = toast.descriptionClassName || '';\n    // Height index is used to calculate the offset as it gets updated before the toast array, which means we can calculate the new layout faster.\n    const heightIndex = React.useMemo(()=>heights.findIndex((height)=>height.toastId === toast.id) || 0, [\n        heights,\n        toast.id\n    ]);\n    const closeButton = React.useMemo(()=>{\n        var _toast_closeButton;\n        return (_toast_closeButton = toast.closeButton) != null ? _toast_closeButton : closeButtonFromToaster;\n    }, [\n        toast.closeButton,\n        closeButtonFromToaster\n    ]);\n    const duration = React.useMemo(()=>toast.duration || durationFromToaster || TOAST_LIFETIME, [\n        toast.duration,\n        durationFromToaster\n    ]);\n    const closeTimerStartTimeRef = React.useRef(0);\n    const offset = React.useRef(0);\n    const lastCloseTimerStartTimeRef = React.useRef(0);\n    const pointerStartRef = React.useRef(null);\n    const [y, x] = position.split('-');\n    const toastsHeightBefore = React.useMemo(()=>{\n        return heights.reduce((prev, curr, reducerIndex)=>{\n            // Calculate offset up until current toast\n            if (reducerIndex >= heightIndex) {\n                return prev;\n            }\n            return prev + curr.height;\n        }, 0);\n    }, [\n        heights,\n        heightIndex\n    ]);\n    const isDocumentHidden = useIsDocumentHidden();\n    const invert = toast.invert || ToasterInvert;\n    const disabled = toastType === 'loading';\n    offset.current = React.useMemo(()=>heightIndex * gap + toastsHeightBefore, [\n        heightIndex,\n        toastsHeightBefore\n    ]);\n    React.useEffect(()=>{\n        remainingTime.current = duration;\n    }, [\n        duration\n    ]);\n    React.useEffect(()=>{\n        // Trigger enter animation without using CSS animation\n        setMounted(true);\n    }, []);\n    React.useEffect(()=>{\n        const toastNode = toastRef.current;\n        if (toastNode) {\n            const height = toastNode.getBoundingClientRect().height;\n            // Add toast height to heights array after the toast is mounted\n            setInitialHeight(height);\n            setHeights((h)=>[\n                    {\n                        toastId: toast.id,\n                        height,\n                        position: toast.position\n                    },\n                    ...h\n                ]);\n            return ()=>setHeights((h)=>h.filter((height)=>height.toastId !== toast.id));\n        }\n    }, [\n        setHeights,\n        toast.id\n    ]);\n    React.useLayoutEffect(()=>{\n        if (!mounted) return;\n        const toastNode = toastRef.current;\n        const originalHeight = toastNode.style.height;\n        toastNode.style.height = 'auto';\n        const newHeight = toastNode.getBoundingClientRect().height;\n        toastNode.style.height = originalHeight;\n        setInitialHeight(newHeight);\n        setHeights((heights)=>{\n            const alreadyExists = heights.find((height)=>height.toastId === toast.id);\n            if (!alreadyExists) {\n                return [\n                    {\n                        toastId: toast.id,\n                        height: newHeight,\n                        position: toast.position\n                    },\n                    ...heights\n                ];\n            } else {\n                return heights.map((height)=>height.toastId === toast.id ? {\n                        ...height,\n                        height: newHeight\n                    } : height);\n            }\n        });\n    }, [\n        mounted,\n        toast.title,\n        toast.description,\n        setHeights,\n        toast.id\n    ]);\n    const deleteToast = React.useCallback(()=>{\n        // Save the offset for the exit swipe animation\n        setRemoved(true);\n        setOffsetBeforeRemove(offset.current);\n        setHeights((h)=>h.filter((height)=>height.toastId !== toast.id));\n        setTimeout(()=>{\n            removeToast(toast);\n        }, TIME_BEFORE_UNMOUNT);\n    }, [\n        toast,\n        removeToast,\n        setHeights,\n        offset\n    ]);\n    React.useEffect(()=>{\n        if (toast.promise && toastType === 'loading' || toast.duration === Infinity || toast.type === 'loading') return;\n        let timeoutId;\n        // Pause the timer on each hover\n        const pauseTimer = ()=>{\n            if (lastCloseTimerStartTimeRef.current < closeTimerStartTimeRef.current) {\n                // Get the elapsed time since the timer started\n                const elapsedTime = new Date().getTime() - closeTimerStartTimeRef.current;\n                remainingTime.current = remainingTime.current - elapsedTime;\n            }\n            lastCloseTimerStartTimeRef.current = new Date().getTime();\n        };\n        const startTimer = ()=>{\n            // setTimeout(, Infinity) behaves as if the delay is 0.\n            // As a result, the toast would be closed immediately, giving the appearance that it was never rendered.\n            // See: https://github.com/denysdovhan/wtfjs?tab=readme-ov-file#an-infinite-timeout\n            if (remainingTime.current === Infinity) return;\n            closeTimerStartTimeRef.current = new Date().getTime();\n            // Let the toast know it has started\n            timeoutId = setTimeout(()=>{\n                toast.onAutoClose == null ? void 0 : toast.onAutoClose.call(toast, toast);\n                deleteToast();\n            }, remainingTime.current);\n        };\n        if (expanded || interacting || isDocumentHidden) {\n            pauseTimer();\n        } else {\n            startTimer();\n        }\n        return ()=>clearTimeout(timeoutId);\n    }, [\n        expanded,\n        interacting,\n        toast,\n        toastType,\n        isDocumentHidden,\n        deleteToast\n    ]);\n    React.useEffect(()=>{\n        if (toast.delete) {\n            deleteToast();\n        }\n    }, [\n        deleteToast,\n        toast.delete\n    ]);\n    function getLoadingIcon() {\n        var _toast_classNames;\n        if (icons == null ? void 0 : icons.loading) {\n            var _toast_classNames1;\n            return /*#__PURE__*/ React.createElement(\"div\", {\n                className: cn(classNames == null ? void 0 : classNames.loader, toast == null ? void 0 : (_toast_classNames1 = toast.classNames) == null ? void 0 : _toast_classNames1.loader, 'sonner-loader'),\n                \"data-visible\": toastType === 'loading'\n            }, icons.loading);\n        }\n        return /*#__PURE__*/ React.createElement(Loader, {\n            className: cn(classNames == null ? void 0 : classNames.loader, toast == null ? void 0 : (_toast_classNames = toast.classNames) == null ? void 0 : _toast_classNames.loader),\n            visible: toastType === 'loading'\n        });\n    }\n    const icon = toast.icon || (icons == null ? void 0 : icons[toastType]) || getAsset(toastType);\n    var _toast_richColors, _icons_close;\n    return /*#__PURE__*/ React.createElement(\"li\", {\n        tabIndex: 0,\n        ref: toastRef,\n        className: cn(className, toastClassname, classNames == null ? void 0 : classNames.toast, toast == null ? void 0 : (_toast_classNames = toast.classNames) == null ? void 0 : _toast_classNames.toast, classNames == null ? void 0 : classNames.default, classNames == null ? void 0 : classNames[toastType], toast == null ? void 0 : (_toast_classNames1 = toast.classNames) == null ? void 0 : _toast_classNames1[toastType]),\n        \"data-sonner-toast\": \"\",\n        \"data-rich-colors\": (_toast_richColors = toast.richColors) != null ? _toast_richColors : defaultRichColors,\n        \"data-styled\": !Boolean(toast.jsx || toast.unstyled || unstyled),\n        \"data-mounted\": mounted,\n        \"data-promise\": Boolean(toast.promise),\n        \"data-swiped\": isSwiped,\n        \"data-removed\": removed,\n        \"data-visible\": isVisible,\n        \"data-y-position\": y,\n        \"data-x-position\": x,\n        \"data-index\": index,\n        \"data-front\": isFront,\n        \"data-swiping\": swiping,\n        \"data-dismissible\": dismissible,\n        \"data-type\": toastType,\n        \"data-invert\": invert,\n        \"data-swipe-out\": swipeOut,\n        \"data-swipe-direction\": swipeOutDirection,\n        \"data-expanded\": Boolean(expanded || expandByDefault && mounted),\n        style: {\n            '--index': index,\n            '--toasts-before': index,\n            '--z-index': toasts.length - index,\n            '--offset': `${removed ? offsetBeforeRemove : offset.current}px`,\n            '--initial-height': expandByDefault ? 'auto' : `${initialHeight}px`,\n            ...style,\n            ...toast.style\n        },\n        onDragEnd: ()=>{\n            setSwiping(false);\n            setSwipeDirection(null);\n            pointerStartRef.current = null;\n        },\n        onPointerDown: (event)=>{\n            if (disabled || !dismissible) return;\n            dragStartTime.current = new Date();\n            setOffsetBeforeRemove(offset.current);\n            // Ensure we maintain correct pointer capture even when going outside of the toast (e.g. when swiping)\n            event.target.setPointerCapture(event.pointerId);\n            if (event.target.tagName === 'BUTTON') return;\n            setSwiping(true);\n            pointerStartRef.current = {\n                x: event.clientX,\n                y: event.clientY\n            };\n        },\n        onPointerUp: ()=>{\n            var _toastRef_current, _toastRef_current1, _dragStartTime_current;\n            if (swipeOut || !dismissible) return;\n            pointerStartRef.current = null;\n            const swipeAmountX = Number(((_toastRef_current = toastRef.current) == null ? void 0 : _toastRef_current.style.getPropertyValue('--swipe-amount-x').replace('px', '')) || 0);\n            const swipeAmountY = Number(((_toastRef_current1 = toastRef.current) == null ? void 0 : _toastRef_current1.style.getPropertyValue('--swipe-amount-y').replace('px', '')) || 0);\n            const timeTaken = new Date().getTime() - ((_dragStartTime_current = dragStartTime.current) == null ? void 0 : _dragStartTime_current.getTime());\n            const swipeAmount = swipeDirection === 'x' ? swipeAmountX : swipeAmountY;\n            const velocity = Math.abs(swipeAmount) / timeTaken;\n            if (Math.abs(swipeAmount) >= SWIPE_THRESHOLD || velocity > 0.11) {\n                setOffsetBeforeRemove(offset.current);\n                toast.onDismiss == null ? void 0 : toast.onDismiss.call(toast, toast);\n                if (swipeDirection === 'x') {\n                    setSwipeOutDirection(swipeAmountX > 0 ? 'right' : 'left');\n                } else {\n                    setSwipeOutDirection(swipeAmountY > 0 ? 'down' : 'up');\n                }\n                deleteToast();\n                setSwipeOut(true);\n                return;\n            } else {\n                var _toastRef_current2, _toastRef_current3;\n                (_toastRef_current2 = toastRef.current) == null ? void 0 : _toastRef_current2.style.setProperty('--swipe-amount-x', `0px`);\n                (_toastRef_current3 = toastRef.current) == null ? void 0 : _toastRef_current3.style.setProperty('--swipe-amount-y', `0px`);\n            }\n            setIsSwiped(false);\n            setSwiping(false);\n            setSwipeDirection(null);\n        },\n        onPointerMove: (event)=>{\n            var _window_getSelection, // Apply transform using both x and y values\n            _toastRef_current, _toastRef_current1;\n            if (!pointerStartRef.current || !dismissible) return;\n            const isHighlighted = ((_window_getSelection = window.getSelection()) == null ? void 0 : _window_getSelection.toString().length) > 0;\n            if (isHighlighted) return;\n            const yDelta = event.clientY - pointerStartRef.current.y;\n            const xDelta = event.clientX - pointerStartRef.current.x;\n            var _props_swipeDirections;\n            const swipeDirections = (_props_swipeDirections = props.swipeDirections) != null ? _props_swipeDirections : getDefaultSwipeDirections(position);\n            // Determine swipe direction if not already locked\n            if (!swipeDirection && (Math.abs(xDelta) > 1 || Math.abs(yDelta) > 1)) {\n                setSwipeDirection(Math.abs(xDelta) > Math.abs(yDelta) ? 'x' : 'y');\n            }\n            let swipeAmount = {\n                x: 0,\n                y: 0\n            };\n            const getDampening = (delta)=>{\n                const factor = Math.abs(delta) / 20;\n                return 1 / (1.5 + factor);\n            };\n            // Only apply swipe in the locked direction\n            if (swipeDirection === 'y') {\n                // Handle vertical swipes\n                if (swipeDirections.includes('top') || swipeDirections.includes('bottom')) {\n                    if (swipeDirections.includes('top') && yDelta < 0 || swipeDirections.includes('bottom') && yDelta > 0) {\n                        swipeAmount.y = yDelta;\n                    } else {\n                        // Smoothly transition to dampened movement\n                        const dampenedDelta = yDelta * getDampening(yDelta);\n                        // Ensure we don't jump when transitioning to dampened movement\n                        swipeAmount.y = Math.abs(dampenedDelta) < Math.abs(yDelta) ? dampenedDelta : yDelta;\n                    }\n                }\n            } else if (swipeDirection === 'x') {\n                // Handle horizontal swipes\n                if (swipeDirections.includes('left') || swipeDirections.includes('right')) {\n                    if (swipeDirections.includes('left') && xDelta < 0 || swipeDirections.includes('right') && xDelta > 0) {\n                        swipeAmount.x = xDelta;\n                    } else {\n                        // Smoothly transition to dampened movement\n                        const dampenedDelta = xDelta * getDampening(xDelta);\n                        // Ensure we don't jump when transitioning to dampened movement\n                        swipeAmount.x = Math.abs(dampenedDelta) < Math.abs(xDelta) ? dampenedDelta : xDelta;\n                    }\n                }\n            }\n            if (Math.abs(swipeAmount.x) > 0 || Math.abs(swipeAmount.y) > 0) {\n                setIsSwiped(true);\n            }\n            (_toastRef_current = toastRef.current) == null ? void 0 : _toastRef_current.style.setProperty('--swipe-amount-x', `${swipeAmount.x}px`);\n            (_toastRef_current1 = toastRef.current) == null ? void 0 : _toastRef_current1.style.setProperty('--swipe-amount-y', `${swipeAmount.y}px`);\n        }\n    }, closeButton && !toast.jsx && toastType !== 'loading' ? /*#__PURE__*/ React.createElement(\"button\", {\n        \"aria-label\": closeButtonAriaLabel,\n        \"data-disabled\": disabled,\n        \"data-close-button\": true,\n        onClick: disabled || !dismissible ? ()=>{} : ()=>{\n            deleteToast();\n            toast.onDismiss == null ? void 0 : toast.onDismiss.call(toast, toast);\n        },\n        className: cn(classNames == null ? void 0 : classNames.closeButton, toast == null ? void 0 : (_toast_classNames2 = toast.classNames) == null ? void 0 : _toast_classNames2.closeButton)\n    }, (_icons_close = icons == null ? void 0 : icons.close) != null ? _icons_close : CloseIcon) : null, (toastType || toast.icon || toast.promise) && toast.icon !== null && ((icons == null ? void 0 : icons[toastType]) !== null || toast.icon) ? /*#__PURE__*/ React.createElement(\"div\", {\n        \"data-icon\": \"\",\n        className: cn(classNames == null ? void 0 : classNames.icon, toast == null ? void 0 : (_toast_classNames3 = toast.classNames) == null ? void 0 : _toast_classNames3.icon)\n    }, toast.promise || toast.type === 'loading' && !toast.icon ? toast.icon || getLoadingIcon() : null, toast.type !== 'loading' ? icon : null) : null, /*#__PURE__*/ React.createElement(\"div\", {\n        \"data-content\": \"\",\n        className: cn(classNames == null ? void 0 : classNames.content, toast == null ? void 0 : (_toast_classNames4 = toast.classNames) == null ? void 0 : _toast_classNames4.content)\n    }, /*#__PURE__*/ React.createElement(\"div\", {\n        \"data-title\": \"\",\n        className: cn(classNames == null ? void 0 : classNames.title, toast == null ? void 0 : (_toast_classNames5 = toast.classNames) == null ? void 0 : _toast_classNames5.title)\n    }, toast.jsx ? toast.jsx : typeof toast.title === 'function' ? toast.title() : toast.title), toast.description ? /*#__PURE__*/ React.createElement(\"div\", {\n        \"data-description\": \"\",\n        className: cn(descriptionClassName, toastDescriptionClassname, classNames == null ? void 0 : classNames.description, toast == null ? void 0 : (_toast_classNames6 = toast.classNames) == null ? void 0 : _toast_classNames6.description)\n    }, typeof toast.description === 'function' ? toast.description() : toast.description) : null), /*#__PURE__*/ React.isValidElement(toast.cancel) ? toast.cancel : toast.cancel && isAction(toast.cancel) ? /*#__PURE__*/ React.createElement(\"button\", {\n        \"data-button\": true,\n        \"data-cancel\": true,\n        style: toast.cancelButtonStyle || cancelButtonStyle,\n        onClick: (event)=>{\n            // We need to check twice because typescript\n            if (!isAction(toast.cancel)) return;\n            if (!dismissible) return;\n            toast.cancel.onClick == null ? void 0 : toast.cancel.onClick.call(toast.cancel, event);\n            deleteToast();\n        },\n        className: cn(classNames == null ? void 0 : classNames.cancelButton, toast == null ? void 0 : (_toast_classNames7 = toast.classNames) == null ? void 0 : _toast_classNames7.cancelButton)\n    }, toast.cancel.label) : null, /*#__PURE__*/ React.isValidElement(toast.action) ? toast.action : toast.action && isAction(toast.action) ? /*#__PURE__*/ React.createElement(\"button\", {\n        \"data-button\": true,\n        \"data-action\": true,\n        style: toast.actionButtonStyle || actionButtonStyle,\n        onClick: (event)=>{\n            // We need to check twice because typescript\n            if (!isAction(toast.action)) return;\n            toast.action.onClick == null ? void 0 : toast.action.onClick.call(toast.action, event);\n            if (event.defaultPrevented) return;\n            deleteToast();\n        },\n        className: cn(classNames == null ? void 0 : classNames.actionButton, toast == null ? void 0 : (_toast_classNames8 = toast.classNames) == null ? void 0 : _toast_classNames8.actionButton)\n    }, toast.action.label) : null);\n};\nfunction getDocumentDirection() {\n    if (typeof window === 'undefined') return 'ltr';\n    if (typeof document === 'undefined') return 'ltr'; // For Fresh purpose\n    const dirAttribute = document.documentElement.getAttribute('dir');\n    if (dirAttribute === 'auto' || !dirAttribute) {\n        return window.getComputedStyle(document.documentElement).direction;\n    }\n    return dirAttribute;\n}\nfunction assignOffset(defaultOffset, mobileOffset) {\n    const styles = {};\n    [\n        defaultOffset,\n        mobileOffset\n    ].forEach((offset, index)=>{\n        const isMobile = index === 1;\n        const prefix = isMobile ? '--mobile-offset' : '--offset';\n        const defaultValue = isMobile ? MOBILE_VIEWPORT_OFFSET : VIEWPORT_OFFSET;\n        function assignAll(offset) {\n            [\n                'top',\n                'right',\n                'bottom',\n                'left'\n            ].forEach((key)=>{\n                styles[`${prefix}-${key}`] = typeof offset === 'number' ? `${offset}px` : offset;\n            });\n        }\n        if (typeof offset === 'number' || typeof offset === 'string') {\n            assignAll(offset);\n        } else if (typeof offset === 'object') {\n            [\n                'top',\n                'right',\n                'bottom',\n                'left'\n            ].forEach((key)=>{\n                if (offset[key] === undefined) {\n                    styles[`${prefix}-${key}`] = defaultValue;\n                } else {\n                    styles[`${prefix}-${key}`] = typeof offset[key] === 'number' ? `${offset[key]}px` : offset[key];\n                }\n            });\n        } else {\n            assignAll(defaultValue);\n        }\n    });\n    return styles;\n}\nfunction useSonner() {\n    const [activeToasts, setActiveToasts] = React.useState([]);\n    React.useEffect(()=>{\n        return ToastState.subscribe((toast)=>{\n            if (toast.dismiss) {\n                setTimeout(()=>{\n                    ReactDOM.flushSync(()=>{\n                        setActiveToasts((toasts)=>toasts.filter((t)=>t.id !== toast.id));\n                    });\n                });\n                return;\n            }\n            // Prevent batching, temp solution.\n            setTimeout(()=>{\n                ReactDOM.flushSync(()=>{\n                    setActiveToasts((toasts)=>{\n                        const indexOfExistingToast = toasts.findIndex((t)=>t.id === toast.id);\n                        // Update the toast if it already exists\n                        if (indexOfExistingToast !== -1) {\n                            return [\n                                ...toasts.slice(0, indexOfExistingToast),\n                                {\n                                    ...toasts[indexOfExistingToast],\n                                    ...toast\n                                },\n                                ...toasts.slice(indexOfExistingToast + 1)\n                            ];\n                        }\n                        return [\n                            toast,\n                            ...toasts\n                        ];\n                    });\n                });\n            });\n        });\n    }, []);\n    return {\n        toasts: activeToasts\n    };\n}\nconst Toaster = /*#__PURE__*/ React.forwardRef(function Toaster(props, ref) {\n    const { invert, position = 'bottom-right', hotkey = [\n        'altKey',\n        'KeyT'\n    ], expand, closeButton, className, offset, mobileOffset, theme = 'light', richColors, duration, style, visibleToasts = VISIBLE_TOASTS_AMOUNT, toastOptions, dir = getDocumentDirection(), gap = GAP, icons, containerAriaLabel = 'Notifications' } = props;\n    const [toasts, setToasts] = React.useState([]);\n    const possiblePositions = React.useMemo(()=>{\n        return Array.from(new Set([\n            position\n        ].concat(toasts.filter((toast)=>toast.position).map((toast)=>toast.position))));\n    }, [\n        toasts,\n        position\n    ]);\n    const [heights, setHeights] = React.useState([]);\n    const [expanded, setExpanded] = React.useState(false);\n    const [interacting, setInteracting] = React.useState(false);\n    const [actualTheme, setActualTheme] = React.useState(theme !== 'system' ? theme : typeof window !== 'undefined' ? window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light' : 'light');\n    const listRef = React.useRef(null);\n    const hotkeyLabel = hotkey.join('+').replace(/Key/g, '').replace(/Digit/g, '');\n    const lastFocusedElementRef = React.useRef(null);\n    const isFocusWithinRef = React.useRef(false);\n    const removeToast = React.useCallback((toastToRemove)=>{\n        setToasts((toasts)=>{\n            var _toasts_find;\n            if (!((_toasts_find = toasts.find((toast)=>toast.id === toastToRemove.id)) == null ? void 0 : _toasts_find.delete)) {\n                ToastState.dismiss(toastToRemove.id);\n            }\n            return toasts.filter(({ id })=>id !== toastToRemove.id);\n        });\n    }, []);\n    React.useEffect(()=>{\n        return ToastState.subscribe((toast)=>{\n            if (toast.dismiss) {\n                // Prevent batching of other state updates\n                requestAnimationFrame(()=>{\n                    setToasts((toasts)=>toasts.map((t)=>t.id === toast.id ? {\n                                ...t,\n                                delete: true\n                            } : t));\n                });\n                return;\n            }\n            // Prevent batching, temp solution.\n            setTimeout(()=>{\n                ReactDOM.flushSync(()=>{\n                    setToasts((toasts)=>{\n                        const indexOfExistingToast = toasts.findIndex((t)=>t.id === toast.id);\n                        // Update the toast if it already exists\n                        if (indexOfExistingToast !== -1) {\n                            return [\n                                ...toasts.slice(0, indexOfExistingToast),\n                                {\n                                    ...toasts[indexOfExistingToast],\n                                    ...toast\n                                },\n                                ...toasts.slice(indexOfExistingToast + 1)\n                            ];\n                        }\n                        return [\n                            toast,\n                            ...toasts\n                        ];\n                    });\n                });\n            });\n        });\n    }, [\n        toasts\n    ]);\n    React.useEffect(()=>{\n        if (theme !== 'system') {\n            setActualTheme(theme);\n            return;\n        }\n        if (theme === 'system') {\n            // check if current preference is dark\n            if (window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches) {\n                // it's currently dark\n                setActualTheme('dark');\n            } else {\n                // it's not dark\n                setActualTheme('light');\n            }\n        }\n        if (typeof window === 'undefined') return;\n        const darkMediaQuery = window.matchMedia('(prefers-color-scheme: dark)');\n        try {\n            // Chrome & Firefox\n            darkMediaQuery.addEventListener('change', ({ matches })=>{\n                if (matches) {\n                    setActualTheme('dark');\n                } else {\n                    setActualTheme('light');\n                }\n            });\n        } catch (error) {\n            // Safari < 14\n            darkMediaQuery.addListener(({ matches })=>{\n                try {\n                    if (matches) {\n                        setActualTheme('dark');\n                    } else {\n                        setActualTheme('light');\n                    }\n                } catch (e) {\n                    console.error(e);\n                }\n            });\n        }\n    }, [\n        theme\n    ]);\n    React.useEffect(()=>{\n        // Ensure expanded is always false when no toasts are present / only one left\n        if (toasts.length <= 1) {\n            setExpanded(false);\n        }\n    }, [\n        toasts\n    ]);\n    React.useEffect(()=>{\n        const handleKeyDown = (event)=>{\n            var _listRef_current;\n            const isHotkeyPressed = hotkey.every((key)=>event[key] || event.code === key);\n            if (isHotkeyPressed) {\n                var _listRef_current1;\n                setExpanded(true);\n                (_listRef_current1 = listRef.current) == null ? void 0 : _listRef_current1.focus();\n            }\n            if (event.code === 'Escape' && (document.activeElement === listRef.current || ((_listRef_current = listRef.current) == null ? void 0 : _listRef_current.contains(document.activeElement)))) {\n                setExpanded(false);\n            }\n        };\n        document.addEventListener('keydown', handleKeyDown);\n        return ()=>document.removeEventListener('keydown', handleKeyDown);\n    }, [\n        hotkey\n    ]);\n    React.useEffect(()=>{\n        if (listRef.current) {\n            return ()=>{\n                if (lastFocusedElementRef.current) {\n                    lastFocusedElementRef.current.focus({\n                        preventScroll: true\n                    });\n                    lastFocusedElementRef.current = null;\n                    isFocusWithinRef.current = false;\n                }\n            };\n        }\n    }, [\n        listRef.current\n    ]);\n    return(// Remove item from normal navigation flow, only available via hotkey\n    /*#__PURE__*/ React.createElement(\"section\", {\n        ref: ref,\n        \"aria-label\": `${containerAriaLabel} ${hotkeyLabel}`,\n        tabIndex: -1,\n        \"aria-live\": \"polite\",\n        \"aria-relevant\": \"additions text\",\n        \"aria-atomic\": \"false\",\n        suppressHydrationWarning: true\n    }, possiblePositions.map((position, index)=>{\n        var _heights_;\n        const [y, x] = position.split('-');\n        if (!toasts.length) return null;\n        return /*#__PURE__*/ React.createElement(\"ol\", {\n            key: position,\n            dir: dir === 'auto' ? getDocumentDirection() : dir,\n            tabIndex: -1,\n            ref: listRef,\n            className: className,\n            \"data-sonner-toaster\": true,\n            \"data-sonner-theme\": actualTheme,\n            \"data-y-position\": y,\n            \"data-lifted\": expanded && toasts.length > 1 && !expand,\n            \"data-x-position\": x,\n            style: {\n                '--front-toast-height': `${((_heights_ = heights[0]) == null ? void 0 : _heights_.height) || 0}px`,\n                '--width': `${TOAST_WIDTH}px`,\n                '--gap': `${gap}px`,\n                ...style,\n                ...assignOffset(offset, mobileOffset)\n            },\n            onBlur: (event)=>{\n                if (isFocusWithinRef.current && !event.currentTarget.contains(event.relatedTarget)) {\n                    isFocusWithinRef.current = false;\n                    if (lastFocusedElementRef.current) {\n                        lastFocusedElementRef.current.focus({\n                            preventScroll: true\n                        });\n                        lastFocusedElementRef.current = null;\n                    }\n                }\n            },\n            onFocus: (event)=>{\n                const isNotDismissible = event.target instanceof HTMLElement && event.target.dataset.dismissible === 'false';\n                if (isNotDismissible) return;\n                if (!isFocusWithinRef.current) {\n                    isFocusWithinRef.current = true;\n                    lastFocusedElementRef.current = event.relatedTarget;\n                }\n            },\n            onMouseEnter: ()=>setExpanded(true),\n            onMouseMove: ()=>setExpanded(true),\n            onMouseLeave: ()=>{\n                // Avoid setting expanded to false when interacting with a toast, e.g. swiping\n                if (!interacting) {\n                    setExpanded(false);\n                }\n            },\n            onDragEnd: ()=>setExpanded(false),\n            onPointerDown: (event)=>{\n                const isNotDismissible = event.target instanceof HTMLElement && event.target.dataset.dismissible === 'false';\n                if (isNotDismissible) return;\n                setInteracting(true);\n            },\n            onPointerUp: ()=>setInteracting(false)\n        }, toasts.filter((toast)=>!toast.position && index === 0 || toast.position === position).map((toast, index)=>{\n            var _toastOptions_duration, _toastOptions_closeButton;\n            return /*#__PURE__*/ React.createElement(Toast, {\n                key: toast.id,\n                icons: icons,\n                index: index,\n                toast: toast,\n                defaultRichColors: richColors,\n                duration: (_toastOptions_duration = toastOptions == null ? void 0 : toastOptions.duration) != null ? _toastOptions_duration : duration,\n                className: toastOptions == null ? void 0 : toastOptions.className,\n                descriptionClassName: toastOptions == null ? void 0 : toastOptions.descriptionClassName,\n                invert: invert,\n                visibleToasts: visibleToasts,\n                closeButton: (_toastOptions_closeButton = toastOptions == null ? void 0 : toastOptions.closeButton) != null ? _toastOptions_closeButton : closeButton,\n                interacting: interacting,\n                position: position,\n                style: toastOptions == null ? void 0 : toastOptions.style,\n                unstyled: toastOptions == null ? void 0 : toastOptions.unstyled,\n                classNames: toastOptions == null ? void 0 : toastOptions.classNames,\n                cancelButtonStyle: toastOptions == null ? void 0 : toastOptions.cancelButtonStyle,\n                actionButtonStyle: toastOptions == null ? void 0 : toastOptions.actionButtonStyle,\n                closeButtonAriaLabel: toastOptions == null ? void 0 : toastOptions.closeButtonAriaLabel,\n                removeToast: removeToast,\n                toasts: toasts.filter((t)=>t.position == toast.position),\n                heights: heights.filter((h)=>h.position == toast.position),\n                setHeights: setHeights,\n                expandByDefault: expand,\n                gap: gap,\n                expanded: expanded,\n                swipeDirections: props.swipeDirections\n            });\n        }));\n    })));\n});\n\nexport { Toaster, toast, useSonner };\n"], "names": [], "mappings": ";;;;;AAUA;AACA;AAXA;AACA,SAAS,YAAY,IAAI;IACvB,IAAI,CAAC,QAAQ,OAAO,YAAY,aAAa;IAC7C,IAAI,OAAO,SAAS,IAAI,IAAI,SAAS,oBAAoB,CAAC,OAAO,CAAC,EAAE;IACpE,IAAI,QAAQ,SAAS,aAAa,CAAC;IACnC,MAAM,IAAI,GAAG;IACb,KAAK,WAAW,CAAC;IAChB,MAAM,UAAU,GAAI,MAAM,UAAU,CAAC,OAAO,GAAG,OAAQ,MAAM,WAAW,CAAC,SAAS,cAAc,CAAC;AACpG;;;AAKA,MAAM,WAAW,CAAC;IACd,OAAO;QACH,KAAK;YACD,OAAO;QACX,KAAK;YACD,OAAO;QACX,KAAK;YACD,OAAO;QACX,KAAK;YACD,OAAO;QACX;YACI,OAAO;IACf;AACJ;AACA,MAAM,OAAO,MAAM,IAAI,IAAI,CAAC;AAC5B,MAAM,SAAS,CAAC,EAAE,OAAO,EAAE,SAAS,EAAE;IAClC,OAAO,WAAW,GAAG,8SAAA,CAAA,UAAK,CAAC,aAAa,CAAC,OAAO;QAC5C,WAAW;YACP;YACA;SACH,CAAC,MAAM,CAAC,SAAS,IAAI,CAAC;QACvB,gBAAgB;IACpB,GAAG,WAAW,GAAG,8SAAA,CAAA,UAAK,CAAC,aAAa,CAAC,OAAO;QACxC,WAAW;IACf,GAAG,KAAK,GAAG,CAAC,CAAC,GAAG,IAAI,WAAW,GAAG,8SAAA,CAAA,UAAK,CAAC,aAAa,CAAC,OAAO;YACrD,WAAW;YACX,KAAK,CAAC,YAAY,EAAE,GAAG;QAC3B;AACR;AACA,MAAM,cAAc,WAAW,GAAG,8SAAA,CAAA,UAAK,CAAC,aAAa,CAAC,OAAO;IACzD,OAAO;IACP,SAAS;IACT,MAAM;IACN,QAAQ;IACR,OAAO;AACX,GAAG,WAAW,GAAG,8SAAA,CAAA,UAAK,CAAC,aAAa,CAAC,QAAQ;IACzC,UAAU;IACV,GAAG;IACH,UAAU;AACd;AACA,MAAM,cAAc,WAAW,GAAG,8SAAA,CAAA,UAAK,CAAC,aAAa,CAAC,OAAO;IACzD,OAAO;IACP,SAAS;IACT,MAAM;IACN,QAAQ;IACR,OAAO;AACX,GAAG,WAAW,GAAG,8SAAA,CAAA,UAAK,CAAC,aAAa,CAAC,QAAQ;IACzC,UAAU;IACV,GAAG;IACH,UAAU;AACd;AACA,MAAM,WAAW,WAAW,GAAG,8SAAA,CAAA,UAAK,CAAC,aAAa,CAAC,OAAO;IACtD,OAAO;IACP,SAAS;IACT,MAAM;IACN,QAAQ;IACR,OAAO;AACX,GAAG,WAAW,GAAG,8SAAA,CAAA,UAAK,CAAC,aAAa,CAAC,QAAQ;IACzC,UAAU;IACV,GAAG;IACH,UAAU;AACd;AACA,MAAM,YAAY,WAAW,GAAG,8SAAA,CAAA,UAAK,CAAC,aAAa,CAAC,OAAO;IACvD,OAAO;IACP,SAAS;IACT,MAAM;IACN,QAAQ;IACR,OAAO;AACX,GAAG,WAAW,GAAG,8SAAA,CAAA,UAAK,CAAC,aAAa,CAAC,QAAQ;IACzC,UAAU;IACV,GAAG;IACH,UAAU;AACd;AACA,MAAM,YAAY,WAAW,GAAG,8SAAA,CAAA,UAAK,CAAC,aAAa,CAAC,OAAO;IACvD,OAAO;IACP,OAAO;IACP,QAAQ;IACR,SAAS;IACT,MAAM;IACN,QAAQ;IACR,aAAa;IACb,eAAe;IACf,gBAAgB;AACpB,GAAG,WAAW,GAAG,8SAAA,CAAA,UAAK,CAAC,aAAa,CAAC,QAAQ;IACzC,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;AACR,IAAI,WAAW,GAAG,8SAAA,CAAA,UAAK,CAAC,aAAa,CAAC,QAAQ;IAC1C,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;AACR;AAEA,MAAM,sBAAsB;IACxB,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,8SAAA,CAAA,UAAK,CAAC,QAAQ,CAAC,SAAS,MAAM;IAC9E,8SAAA,CAAA,UAAK,CAAC,SAAS,CAAC;QACZ,MAAM,WAAW;YACb,oBAAoB,SAAS,MAAM;QACvC;QACA,SAAS,gBAAgB,CAAC,oBAAoB;QAC9C,OAAO,IAAI,OAAO,mBAAmB,CAAC,oBAAoB;IAC9D,GAAG,EAAE;IACL,OAAO;AACX;AAEA,IAAI,gBAAgB;AACpB,MAAM;IACF,aAAa;QACT,kEAAkE;QAClE,IAAI,CAAC,SAAS,GAAG,CAAC;YACd,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC;YACtB,OAAO;gBACH,MAAM,QAAQ,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC;gBACvC,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,OAAO;YACnC;QACJ;QACA,IAAI,CAAC,OAAO,GAAG,CAAC;YACZ,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,aAAa,WAAW;QACtD;QACA,IAAI,CAAC,QAAQ,GAAG,CAAC;YACb,IAAI,CAAC,OAAO,CAAC;YACb,IAAI,CAAC,MAAM,GAAG;mBACP,IAAI,CAAC,MAAM;gBACd;aACH;QACL;QACA,IAAI,CAAC,MAAM,GAAG,CAAC;YACX,IAAI;YACJ,MAAM,EAAE,OAAO,EAAE,GAAG,MAAM,GAAG;YAC7B,MAAM,KAAK,OAAO,CAAC,QAAQ,OAAO,KAAK,IAAI,KAAK,EAAE,MAAM,YAAY,CAAC,CAAC,WAAW,KAAK,EAAE,KAAK,OAAO,KAAK,IAAI,SAAS,MAAM,IAAI,IAAI,KAAK,EAAE,GAAG;YAC9I,MAAM,gBAAgB,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;gBACpC,OAAO,MAAM,EAAE,KAAK;YACxB;YACA,MAAM,cAAc,KAAK,WAAW,KAAK,YAAY,OAAO,KAAK,WAAW;YAC5E,IAAI,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,KAAK;gBAC9B,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC;YAChC;YACA,IAAI,eAAe;gBACf,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;oBAC3B,IAAI,MAAM,EAAE,KAAK,IAAI;wBACjB,IAAI,CAAC,OAAO,CAAC;4BACT,GAAG,KAAK;4BACR,GAAG,IAAI;4BACP;4BACA,OAAO;wBACX;wBACA,OAAO;4BACH,GAAG,KAAK;4BACR,GAAG,IAAI;4BACP;4BACA;4BACA,OAAO;wBACX;oBACJ;oBACA,OAAO;gBACX;YACJ,OAAO;gBACH,IAAI,CAAC,QAAQ,CAAC;oBACV,OAAO;oBACP,GAAG,IAAI;oBACP;oBACA;gBACJ;YACJ;YACA,OAAO;QACX;QACA,IAAI,CAAC,OAAO,GAAG,CAAC;YACZ,IAAI,IAAI;gBACJ,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC;gBACzB,sBAAsB,IAAI,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,aAAa,WAAW;4BAChE;4BACA,SAAS;wBACb;YACZ,OAAO;gBACH,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;oBACjB,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,aAAa,WAAW;4BAC1C,IAAI,MAAM,EAAE;4BACZ,SAAS;wBACb;gBACR;YACJ;YACA,OAAO;QACX;QACA,IAAI,CAAC,OAAO,GAAG,CAAC,SAAS;YACrB,OAAO,IAAI,CAAC,MAAM,CAAC;gBACf,GAAG,IAAI;gBACP;YACJ;QACJ;QACA,IAAI,CAAC,KAAK,GAAG,CAAC,SAAS;YACnB,OAAO,IAAI,CAAC,MAAM,CAAC;gBACf,GAAG,IAAI;gBACP;gBACA,MAAM;YACV;QACJ;QACA,IAAI,CAAC,OAAO,GAAG,CAAC,SAAS;YACrB,OAAO,IAAI,CAAC,MAAM,CAAC;gBACf,GAAG,IAAI;gBACP,MAAM;gBACN;YACJ;QACJ;QACA,IAAI,CAAC,IAAI,GAAG,CAAC,SAAS;YAClB,OAAO,IAAI,CAAC,MAAM,CAAC;gBACf,GAAG,IAAI;gBACP,MAAM;gBACN;YACJ;QACJ;QACA,IAAI,CAAC,OAAO,GAAG,CAAC,SAAS;YACrB,OAAO,IAAI,CAAC,MAAM,CAAC;gBACf,GAAG,IAAI;gBACP,MAAM;gBACN;YACJ;QACJ;QACA,IAAI,CAAC,OAAO,GAAG,CAAC,SAAS;YACrB,OAAO,IAAI,CAAC,MAAM,CAAC;gBACf,GAAG,IAAI;gBACP,MAAM;gBACN;YACJ;QACJ;QACA,IAAI,CAAC,OAAO,GAAG,CAAC,SAAS;YACrB,IAAI,CAAC,MAAM;gBACP,kBAAkB;gBAClB;YACJ;YACA,IAAI,KAAK;YACT,IAAI,KAAK,OAAO,KAAK,WAAW;gBAC5B,KAAK,IAAI,CAAC,MAAM,CAAC;oBACb,GAAG,IAAI;oBACP;oBACA,MAAM;oBACN,SAAS,KAAK,OAAO;oBACrB,aAAa,OAAO,KAAK,WAAW,KAAK,aAAa,KAAK,WAAW,GAAG;gBAC7E;YACJ;YACA,MAAM,IAAI,QAAQ,OAAO,CAAC,mBAAmB,WAAW,YAAY;YACpE,IAAI,gBAAgB,OAAO;YAC3B,IAAI;YACJ,MAAM,kBAAkB,EAAE,IAAI,CAAC,OAAO;gBAClC,SAAS;oBACL;oBACA;iBACH;gBACD,MAAM,yBAAyB,8SAAA,CAAA,UAAK,CAAC,cAAc,CAAC;gBACpD,IAAI,wBAAwB;oBACxB,gBAAgB;oBAChB,IAAI,CAAC,MAAM,CAAC;wBACR;wBACA,MAAM;wBACN,SAAS;oBACb;gBACJ,OAAO,IAAI,eAAe,aAAa,CAAC,SAAS,EAAE,EAAE;oBACjD,gBAAgB;oBAChB,MAAM,cAAc,OAAO,KAAK,KAAK,KAAK,aAAa,MAAM,KAAK,KAAK,CAAC,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE,IAAI,KAAK,KAAK;oBAC9H,MAAM,cAAc,OAAO,KAAK,WAAW,KAAK,aAAa,MAAM,KAAK,WAAW,CAAC,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE,IAAI,KAAK,WAAW;oBAChJ,MAAM,mBAAmB,OAAO,gBAAgB,YAAY,CAAC,8SAAA,CAAA,UAAK,CAAC,cAAc,CAAC;oBAClF,MAAM,gBAAgB,mBAAmB,cAAc;wBACnD,SAAS;oBACb;oBACA,IAAI,CAAC,MAAM,CAAC;wBACR;wBACA,MAAM;wBACN;wBACA,GAAG,aAAa;oBACpB;gBACJ,OAAO,IAAI,oBAAoB,OAAO;oBAClC,gBAAgB;oBAChB,MAAM,cAAc,OAAO,KAAK,KAAK,KAAK,aAAa,MAAM,KAAK,KAAK,CAAC,YAAY,KAAK,KAAK;oBAC9F,MAAM,cAAc,OAAO,KAAK,WAAW,KAAK,aAAa,MAAM,KAAK,WAAW,CAAC,YAAY,KAAK,WAAW;oBAChH,MAAM,mBAAmB,OAAO,gBAAgB,YAAY,CAAC,8SAAA,CAAA,UAAK,CAAC,cAAc,CAAC;oBAClF,MAAM,gBAAgB,mBAAmB,cAAc;wBACnD,SAAS;oBACb;oBACA,IAAI,CAAC,MAAM,CAAC;wBACR;wBACA,MAAM;wBACN;wBACA,GAAG,aAAa;oBACpB;gBACJ,OAAO,IAAI,KAAK,OAAO,KAAK,WAAW;oBACnC,gBAAgB;oBAChB,MAAM,cAAc,OAAO,KAAK,OAAO,KAAK,aAAa,MAAM,KAAK,OAAO,CAAC,YAAY,KAAK,OAAO;oBACpG,MAAM,cAAc,OAAO,KAAK,WAAW,KAAK,aAAa,MAAM,KAAK,WAAW,CAAC,YAAY,KAAK,WAAW;oBAChH,MAAM,mBAAmB,OAAO,gBAAgB,YAAY,CAAC,8SAAA,CAAA,UAAK,CAAC,cAAc,CAAC;oBAClF,MAAM,gBAAgB,mBAAmB,cAAc;wBACnD,SAAS;oBACb;oBACA,IAAI,CAAC,MAAM,CAAC;wBACR;wBACA,MAAM;wBACN;wBACA,GAAG,aAAa;oBACpB;gBACJ;YACJ,GAAG,KAAK,CAAC,OAAO;gBACZ,SAAS;oBACL;oBACA;iBACH;gBACD,IAAI,KAAK,KAAK,KAAK,WAAW;oBAC1B,gBAAgB;oBAChB,MAAM,cAAc,OAAO,KAAK,KAAK,KAAK,aAAa,MAAM,KAAK,KAAK,CAAC,SAAS,KAAK,KAAK;oBAC3F,MAAM,cAAc,OAAO,KAAK,WAAW,KAAK,aAAa,MAAM,KAAK,WAAW,CAAC,SAAS,KAAK,WAAW;oBAC7G,MAAM,mBAAmB,OAAO,gBAAgB,YAAY,CAAC,8SAAA,CAAA,UAAK,CAAC,cAAc,CAAC;oBAClF,MAAM,gBAAgB,mBAAmB,cAAc;wBACnD,SAAS;oBACb;oBACA,IAAI,CAAC,MAAM,CAAC;wBACR;wBACA,MAAM;wBACN;wBACA,GAAG,aAAa;oBACpB;gBACJ;YACJ,GAAG,OAAO,CAAC;gBACP,IAAI,eAAe;oBACf,uEAAuE;oBACvE,IAAI,CAAC,OAAO,CAAC;oBACb,KAAK;gBACT;gBACA,KAAK,OAAO,IAAI,OAAO,KAAK,IAAI,KAAK,OAAO,CAAC,IAAI,CAAC;YACtD;YACA,MAAM,SAAS,IAAI,IAAI,QAAQ,CAAC,SAAS,SAAS,gBAAgB,IAAI,CAAC,IAAI,MAAM,CAAC,EAAE,KAAK,WAAW,OAAO,MAAM,CAAC,EAAE,IAAI,QAAQ,MAAM,CAAC,EAAE,GAAG,KAAK,CAAC;YAClJ,IAAI,OAAO,OAAO,YAAY,OAAO,OAAO,UAAU;gBAClD,oCAAoC;gBACpC,OAAO;oBACH;gBACJ;YACJ,OAAO;gBACH,OAAO,OAAO,MAAM,CAAC,IAAI;oBACrB;gBACJ;YACJ;QACJ;QACA,IAAI,CAAC,MAAM,GAAG,CAAC,KAAK;YAChB,MAAM,KAAK,CAAC,QAAQ,OAAO,KAAK,IAAI,KAAK,EAAE,KAAK;YAChD,IAAI,CAAC,MAAM,CAAC;gBACR,KAAK,IAAI;gBACT;gBACA,GAAG,IAAI;YACX;YACA,OAAO;QACX;QACA,IAAI,CAAC,eAAe,GAAG;YACnB,OAAO,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,MAAM,EAAE;QACzE;QACA,IAAI,CAAC,WAAW,GAAG,EAAE;QACrB,IAAI,CAAC,MAAM,GAAG,EAAE;QAChB,IAAI,CAAC,eAAe,GAAG,IAAI;IAC/B;AACJ;AACA,MAAM,aAAa,IAAI;AACvB,kCAAkC;AAClC,MAAM,gBAAgB,CAAC,SAAS;IAC5B,MAAM,KAAK,CAAC,QAAQ,OAAO,KAAK,IAAI,KAAK,EAAE,KAAK;IAChD,WAAW,QAAQ,CAAC;QAChB,OAAO;QACP,GAAG,IAAI;QACP;IACJ;IACA,OAAO;AACX;AACA,MAAM,iBAAiB,CAAC;IACpB,OAAO,QAAQ,OAAO,SAAS,YAAY,QAAQ,QAAQ,OAAO,KAAK,EAAE,KAAK,aAAa,YAAY,QAAQ,OAAO,KAAK,MAAM,KAAK;AAC1I;AACA,MAAM,aAAa;AACnB,MAAM,aAAa,IAAI,WAAW,MAAM;AACxC,MAAM,YAAY,IAAI,WAAW,eAAe;AAChD,uFAAuF;AACvF,MAAM,QAAQ,OAAO,MAAM,CAAC,YAAY;IACpC,SAAS,WAAW,OAAO;IAC3B,MAAM,WAAW,IAAI;IACrB,SAAS,WAAW,OAAO;IAC3B,OAAO,WAAW,KAAK;IACvB,QAAQ,WAAW,MAAM;IACzB,SAAS,WAAW,OAAO;IAC3B,SAAS,WAAW,OAAO;IAC3B,SAAS,WAAW,OAAO;IAC3B,SAAS,WAAW,OAAO;AAC/B,GAAG;IACC;IACA;AACJ;AAEA,YAAY;AAEZ,SAAS,SAAS,MAAM;IACpB,OAAO,OAAO,KAAK,KAAK;AAC5B;AAEA,wBAAwB;AACxB,MAAM,wBAAwB;AAC9B,mBAAmB;AACnB,MAAM,kBAAkB;AACxB,0BAA0B;AAC1B,MAAM,yBAAyB;AAC/B,uCAAuC;AACvC,MAAM,iBAAiB;AACvB,sBAAsB;AACtB,MAAM,cAAc;AACpB,6BAA6B;AAC7B,MAAM,MAAM;AACZ,+BAA+B;AAC/B,MAAM,kBAAkB;AACxB,mCAAmC;AACnC,MAAM,sBAAsB;AAC5B,SAAS,GAAG,GAAG,OAAO;IAClB,OAAO,QAAQ,MAAM,CAAC,SAAS,IAAI,CAAC;AACxC;AACA,SAAS,0BAA0B,QAAQ;IACvC,MAAM,CAAC,GAAG,EAAE,GAAG,SAAS,KAAK,CAAC;IAC9B,MAAM,aAAa,EAAE;IACrB,IAAI,GAAG;QACH,WAAW,IAAI,CAAC;IACpB;IACA,IAAI,GAAG;QACH,WAAW,IAAI,CAAC;IACpB;IACA,OAAO;AACX;AACA,MAAM,QAAQ,CAAC;IACX,IAAI,mBAAmB,oBAAoB,oBAAoB,oBAAoB,oBAAoB,oBAAoB,oBAAoB,oBAAoB;IACnK,MAAM,EAAE,QAAQ,aAAa,EAAE,KAAK,EAAE,QAAQ,EAAE,WAAW,EAAE,UAAU,EAAE,aAAa,EAAE,OAAO,EAAE,KAAK,EAAE,MAAM,EAAE,QAAQ,EAAE,WAAW,EAAE,iBAAiB,EAAE,aAAa,sBAAsB,EAAE,KAAK,EAAE,iBAAiB,EAAE,iBAAiB,EAAE,YAAY,EAAE,EAAE,uBAAuB,EAAE,EAAE,UAAU,mBAAmB,EAAE,QAAQ,EAAE,GAAG,EAAE,eAAe,EAAE,UAAU,EAAE,KAAK,EAAE,uBAAuB,aAAa,EAAE,GAAG;IAClZ,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,8SAAA,CAAA,UAAK,CAAC,QAAQ,CAAC;IAC3D,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,8SAAA,CAAA,UAAK,CAAC,QAAQ,CAAC;IACjE,MAAM,CAAC,SAAS,WAAW,GAAG,8SAAA,CAAA,UAAK,CAAC,QAAQ,CAAC;IAC7C,MAAM,CAAC,SAAS,WAAW,GAAG,8SAAA,CAAA,UAAK,CAAC,QAAQ,CAAC;IAC7C,MAAM,CAAC,SAAS,WAAW,GAAG,8SAAA,CAAA,UAAK,CAAC,QAAQ,CAAC;IAC7C,MAAM,CAAC,UAAU,YAAY,GAAG,8SAAA,CAAA,UAAK,CAAC,QAAQ,CAAC;IAC/C,MAAM,CAAC,UAAU,YAAY,GAAG,8SAAA,CAAA,UAAK,CAAC,QAAQ,CAAC;IAC/C,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,8SAAA,CAAA,UAAK,CAAC,QAAQ,CAAC;IACnE,MAAM,CAAC,eAAe,iBAAiB,GAAG,8SAAA,CAAA,UAAK,CAAC,QAAQ,CAAC;IACzD,MAAM,gBAAgB,8SAAA,CAAA,UAAK,CAAC,MAAM,CAAC,MAAM,QAAQ,IAAI,uBAAuB;IAC5E,MAAM,gBAAgB,8SAAA,CAAA,UAAK,CAAC,MAAM,CAAC;IACnC,MAAM,WAAW,8SAAA,CAAA,UAAK,CAAC,MAAM,CAAC;IAC9B,MAAM,UAAU,UAAU;IAC1B,MAAM,YAAY,QAAQ,KAAK;IAC/B,MAAM,YAAY,MAAM,IAAI;IAC5B,MAAM,cAAc,MAAM,WAAW,KAAK;IAC1C,MAAM,iBAAiB,MAAM,SAAS,IAAI;IAC1C,MAAM,4BAA4B,MAAM,oBAAoB,IAAI;IAChE,8IAA8I;IAC9I,MAAM,cAAc,8SAAA,CAAA,UAAK,CAAC,OAAO,CAAC,IAAI,QAAQ,SAAS,CAAC,CAAC,SAAS,OAAO,OAAO,KAAK,MAAM,EAAE,KAAK,GAAG;QACjG;QACA,MAAM,EAAE;KACX;IACD,MAAM,cAAc,8SAAA,CAAA,UAAK,CAAC,OAAO,CAAC;QAC9B,IAAI;QACJ,OAAO,CAAC,qBAAqB,MAAM,WAAW,KAAK,OAAO,qBAAqB;IACnF,GAAG;QACC,MAAM,WAAW;QACjB;KACH;IACD,MAAM,WAAW,8SAAA,CAAA,UAAK,CAAC,OAAO,CAAC,IAAI,MAAM,QAAQ,IAAI,uBAAuB,gBAAgB;QACxF,MAAM,QAAQ;QACd;KACH;IACD,MAAM,yBAAyB,8SAAA,CAAA,UAAK,CAAC,MAAM,CAAC;IAC5C,MAAM,SAAS,8SAAA,CAAA,UAAK,CAAC,MAAM,CAAC;IAC5B,MAAM,6BAA6B,8SAAA,CAAA,UAAK,CAAC,MAAM,CAAC;IAChD,MAAM,kBAAkB,8SAAA,CAAA,UAAK,CAAC,MAAM,CAAC;IACrC,MAAM,CAAC,GAAG,EAAE,GAAG,SAAS,KAAK,CAAC;IAC9B,MAAM,qBAAqB,8SAAA,CAAA,UAAK,CAAC,OAAO,CAAC;QACrC,OAAO,QAAQ,MAAM,CAAC,CAAC,MAAM,MAAM;YAC/B,0CAA0C;YAC1C,IAAI,gBAAgB,aAAa;gBAC7B,OAAO;YACX;YACA,OAAO,OAAO,KAAK,MAAM;QAC7B,GAAG;IACP,GAAG;QACC;QACA;KACH;IACD,MAAM,mBAAmB;IACzB,MAAM,SAAS,MAAM,MAAM,IAAI;IAC/B,MAAM,WAAW,cAAc;IAC/B,OAAO,OAAO,GAAG,8SAAA,CAAA,UAAK,CAAC,OAAO,CAAC,IAAI,cAAc,MAAM,oBAAoB;QACvE;QACA;KACH;IACD,8SAAA,CAAA,UAAK,CAAC,SAAS,CAAC;QACZ,cAAc,OAAO,GAAG;IAC5B,GAAG;QACC;KACH;IACD,8SAAA,CAAA,UAAK,CAAC,SAAS,CAAC;QACZ,sDAAsD;QACtD,WAAW;IACf,GAAG,EAAE;IACL,8SAAA,CAAA,UAAK,CAAC,SAAS,CAAC;QACZ,MAAM,YAAY,SAAS,OAAO;QAClC,IAAI,WAAW;YACX,MAAM,SAAS,UAAU,qBAAqB,GAAG,MAAM;YACvD,+DAA+D;YAC/D,iBAAiB;YACjB,WAAW,CAAC,IAAI;oBACR;wBACI,SAAS,MAAM,EAAE;wBACjB;wBACA,UAAU,MAAM,QAAQ;oBAC5B;uBACG;iBACN;YACL,OAAO,IAAI,WAAW,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC,SAAS,OAAO,OAAO,KAAK,MAAM,EAAE;QAC7E;IACJ,GAAG;QACC;QACA,MAAM,EAAE;KACX;IACD,8SAAA,CAAA,UAAK,CAAC,eAAe,CAAC;QAClB,IAAI,CAAC,SAAS;QACd,MAAM,YAAY,SAAS,OAAO;QAClC,MAAM,iBAAiB,UAAU,KAAK,CAAC,MAAM;QAC7C,UAAU,KAAK,CAAC,MAAM,GAAG;QACzB,MAAM,YAAY,UAAU,qBAAqB,GAAG,MAAM;QAC1D,UAAU,KAAK,CAAC,MAAM,GAAG;QACzB,iBAAiB;QACjB,WAAW,CAAC;YACR,MAAM,gBAAgB,QAAQ,IAAI,CAAC,CAAC,SAAS,OAAO,OAAO,KAAK,MAAM,EAAE;YACxE,IAAI,CAAC,eAAe;gBAChB,OAAO;oBACH;wBACI,SAAS,MAAM,EAAE;wBACjB,QAAQ;wBACR,UAAU,MAAM,QAAQ;oBAC5B;uBACG;iBACN;YACL,OAAO;gBACH,OAAO,QAAQ,GAAG,CAAC,CAAC,SAAS,OAAO,OAAO,KAAK,MAAM,EAAE,GAAG;wBACnD,GAAG,MAAM;wBACT,QAAQ;oBACZ,IAAI;YACZ;QACJ;IACJ,GAAG;QACC;QACA,MAAM,KAAK;QACX,MAAM,WAAW;QACjB;QACA,MAAM,EAAE;KACX;IACD,MAAM,cAAc,8SAAA,CAAA,UAAK,CAAC,WAAW,CAAC;QAClC,+CAA+C;QAC/C,WAAW;QACX,sBAAsB,OAAO,OAAO;QACpC,WAAW,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC,SAAS,OAAO,OAAO,KAAK,MAAM,EAAE;QAC9D,WAAW;YACP,YAAY;QAChB,GAAG;IACP,GAAG;QACC;QACA;QACA;QACA;KACH;IACD,8SAAA,CAAA,UAAK,CAAC,SAAS,CAAC;QACZ,IAAI,MAAM,OAAO,IAAI,cAAc,aAAa,MAAM,QAAQ,KAAK,YAAY,MAAM,IAAI,KAAK,WAAW;QACzG,IAAI;QACJ,gCAAgC;QAChC,MAAM,aAAa;YACf,IAAI,2BAA2B,OAAO,GAAG,uBAAuB,OAAO,EAAE;gBACrE,+CAA+C;gBAC/C,MAAM,cAAc,IAAI,OAAO,OAAO,KAAK,uBAAuB,OAAO;gBACzE,cAAc,OAAO,GAAG,cAAc,OAAO,GAAG;YACpD;YACA,2BAA2B,OAAO,GAAG,IAAI,OAAO,OAAO;QAC3D;QACA,MAAM,aAAa;YACf,uDAAuD;YACvD,wGAAwG;YACxG,mFAAmF;YACnF,IAAI,cAAc,OAAO,KAAK,UAAU;YACxC,uBAAuB,OAAO,GAAG,IAAI,OAAO,OAAO;YACnD,oCAAoC;YACpC,YAAY,WAAW;gBACnB,MAAM,WAAW,IAAI,OAAO,KAAK,IAAI,MAAM,WAAW,CAAC,IAAI,CAAC,OAAO;gBACnE;YACJ,GAAG,cAAc,OAAO;QAC5B;QACA,IAAI,YAAY,eAAe,kBAAkB;YAC7C;QACJ,OAAO;YACH;QACJ;QACA,OAAO,IAAI,aAAa;IAC5B,GAAG;QACC;QACA;QACA;QACA;QACA;QACA;KACH;IACD,8SAAA,CAAA,UAAK,CAAC,SAAS,CAAC;QACZ,IAAI,MAAM,MAAM,EAAE;YACd;QACJ;IACJ,GAAG;QACC;QACA,MAAM,MAAM;KACf;IACD,SAAS;QACL,IAAI;QACJ,IAAI,SAAS,OAAO,KAAK,IAAI,MAAM,OAAO,EAAE;YACxC,IAAI;YACJ,OAAO,WAAW,GAAG,8SAAA,CAAA,UAAK,CAAC,aAAa,CAAC,OAAO;gBAC5C,WAAW,GAAG,cAAc,OAAO,KAAK,IAAI,WAAW,MAAM,EAAE,SAAS,OAAO,KAAK,IAAI,CAAC,qBAAqB,MAAM,UAAU,KAAK,OAAO,KAAK,IAAI,mBAAmB,MAAM,EAAE;gBAC9K,gBAAgB,cAAc;YAClC,GAAG,MAAM,OAAO;QACpB;QACA,OAAO,WAAW,GAAG,8SAAA,CAAA,UAAK,CAAC,aAAa,CAAC,QAAQ;YAC7C,WAAW,GAAG,cAAc,OAAO,KAAK,IAAI,WAAW,MAAM,EAAE,SAAS,OAAO,KAAK,IAAI,CAAC,oBAAoB,MAAM,UAAU,KAAK,OAAO,KAAK,IAAI,kBAAkB,MAAM;YAC1K,SAAS,cAAc;QAC3B;IACJ;IACA,MAAM,OAAO,MAAM,IAAI,IAAI,CAAC,SAAS,OAAO,KAAK,IAAI,KAAK,CAAC,UAAU,KAAK,SAAS;IACnF,IAAI,mBAAmB;IACvB,OAAO,WAAW,GAAG,8SAAA,CAAA,UAAK,CAAC,aAAa,CAAC,MAAM;QAC3C,UAAU;QACV,KAAK;QACL,WAAW,GAAG,WAAW,gBAAgB,cAAc,OAAO,KAAK,IAAI,WAAW,KAAK,EAAE,SAAS,OAAO,KAAK,IAAI,CAAC,oBAAoB,MAAM,UAAU,KAAK,OAAO,KAAK,IAAI,kBAAkB,KAAK,EAAE,cAAc,OAAO,KAAK,IAAI,WAAW,OAAO,EAAE,cAAc,OAAO,KAAK,IAAI,UAAU,CAAC,UAAU,EAAE,SAAS,OAAO,KAAK,IAAI,CAAC,qBAAqB,MAAM,UAAU,KAAK,OAAO,KAAK,IAAI,kBAAkB,CAAC,UAAU;QAC7Z,qBAAqB;QACrB,oBAAoB,CAAC,oBAAoB,MAAM,UAAU,KAAK,OAAO,oBAAoB;QACzF,eAAe,CAAC,QAAQ,MAAM,GAAG,IAAI,MAAM,QAAQ,IAAI;QACvD,gBAAgB;QAChB,gBAAgB,QAAQ,MAAM,OAAO;QACrC,eAAe;QACf,gBAAgB;QAChB,gBAAgB;QAChB,mBAAmB;QACnB,mBAAmB;QACnB,cAAc;QACd,cAAc;QACd,gBAAgB;QAChB,oBAAoB;QACpB,aAAa;QACb,eAAe;QACf,kBAAkB;QAClB,wBAAwB;QACxB,iBAAiB,QAAQ,YAAY,mBAAmB;QACxD,OAAO;YACH,WAAW;YACX,mBAAmB;YACnB,aAAa,OAAO,MAAM,GAAG;YAC7B,YAAY,GAAG,UAAU,qBAAqB,OAAO,OAAO,CAAC,EAAE,CAAC;YAChE,oBAAoB,kBAAkB,SAAS,GAAG,cAAc,EAAE,CAAC;YACnE,GAAG,KAAK;YACR,GAAG,MAAM,KAAK;QAClB;QACA,WAAW;YACP,WAAW;YACX,kBAAkB;YAClB,gBAAgB,OAAO,GAAG;QAC9B;QACA,eAAe,CAAC;YACZ,IAAI,YAAY,CAAC,aAAa;YAC9B,cAAc,OAAO,GAAG,IAAI;YAC5B,sBAAsB,OAAO,OAAO;YACpC,sGAAsG;YACtG,MAAM,MAAM,CAAC,iBAAiB,CAAC,MAAM,SAAS;YAC9C,IAAI,MAAM,MAAM,CAAC,OAAO,KAAK,UAAU;YACvC,WAAW;YACX,gBAAgB,OAAO,GAAG;gBACtB,GAAG,MAAM,OAAO;gBAChB,GAAG,MAAM,OAAO;YACpB;QACJ;QACA,aAAa;YACT,IAAI,mBAAmB,oBAAoB;YAC3C,IAAI,YAAY,CAAC,aAAa;YAC9B,gBAAgB,OAAO,GAAG;YAC1B,MAAM,eAAe,OAAO,CAAC,CAAC,oBAAoB,SAAS,OAAO,KAAK,OAAO,KAAK,IAAI,kBAAkB,KAAK,CAAC,gBAAgB,CAAC,oBAAoB,OAAO,CAAC,MAAM,GAAG,KAAK;YAC1K,MAAM,eAAe,OAAO,CAAC,CAAC,qBAAqB,SAAS,OAAO,KAAK,OAAO,KAAK,IAAI,mBAAmB,KAAK,CAAC,gBAAgB,CAAC,oBAAoB,OAAO,CAAC,MAAM,GAAG,KAAK;YAC5K,MAAM,YAAY,IAAI,OAAO,OAAO,KAAK,CAAC,CAAC,yBAAyB,cAAc,OAAO,KAAK,OAAO,KAAK,IAAI,uBAAuB,OAAO,EAAE;YAC9I,MAAM,cAAc,mBAAmB,MAAM,eAAe;YAC5D,MAAM,WAAW,KAAK,GAAG,CAAC,eAAe;YACzC,IAAI,KAAK,GAAG,CAAC,gBAAgB,mBAAmB,WAAW,MAAM;gBAC7D,sBAAsB,OAAO,OAAO;gBACpC,MAAM,SAAS,IAAI,OAAO,KAAK,IAAI,MAAM,SAAS,CAAC,IAAI,CAAC,OAAO;gBAC/D,IAAI,mBAAmB,KAAK;oBACxB,qBAAqB,eAAe,IAAI,UAAU;gBACtD,OAAO;oBACH,qBAAqB,eAAe,IAAI,SAAS;gBACrD;gBACA;gBACA,YAAY;gBACZ;YACJ,OAAO;gBACH,IAAI,oBAAoB;gBACxB,CAAC,qBAAqB,SAAS,OAAO,KAAK,OAAO,KAAK,IAAI,mBAAmB,KAAK,CAAC,WAAW,CAAC,oBAAoB,CAAC,GAAG,CAAC;gBACzH,CAAC,qBAAqB,SAAS,OAAO,KAAK,OAAO,KAAK,IAAI,mBAAmB,KAAK,CAAC,WAAW,CAAC,oBAAoB,CAAC,GAAG,CAAC;YAC7H;YACA,YAAY;YACZ,WAAW;YACX,kBAAkB;QACtB;QACA,eAAe,CAAC;YACZ,IAAI,sBACJ,mBAAmB;YACnB,IAAI,CAAC,gBAAgB,OAAO,IAAI,CAAC,aAAa;YAC9C,MAAM,gBAAgB,CAAC,CAAC,uBAAuB,OAAO,YAAY,EAAE,KAAK,OAAO,KAAK,IAAI,qBAAqB,QAAQ,GAAG,MAAM,IAAI;YACnI,IAAI,eAAe;YACnB,MAAM,SAAS,MAAM,OAAO,GAAG,gBAAgB,OAAO,CAAC,CAAC;YACxD,MAAM,SAAS,MAAM,OAAO,GAAG,gBAAgB,OAAO,CAAC,CAAC;YACxD,IAAI;YACJ,MAAM,kBAAkB,CAAC,yBAAyB,MAAM,eAAe,KAAK,OAAO,yBAAyB,0BAA0B;YACtI,kDAAkD;YAClD,IAAI,CAAC,kBAAkB,CAAC,KAAK,GAAG,CAAC,UAAU,KAAK,KAAK,GAAG,CAAC,UAAU,CAAC,GAAG;gBACnE,kBAAkB,KAAK,GAAG,CAAC,UAAU,KAAK,GAAG,CAAC,UAAU,MAAM;YAClE;YACA,IAAI,cAAc;gBACd,GAAG;gBACH,GAAG;YACP;YACA,MAAM,eAAe,CAAC;gBAClB,MAAM,SAAS,KAAK,GAAG,CAAC,SAAS;gBACjC,OAAO,IAAI,CAAC,MAAM,MAAM;YAC5B;YACA,2CAA2C;YAC3C,IAAI,mBAAmB,KAAK;gBACxB,yBAAyB;gBACzB,IAAI,gBAAgB,QAAQ,CAAC,UAAU,gBAAgB,QAAQ,CAAC,WAAW;oBACvE,IAAI,gBAAgB,QAAQ,CAAC,UAAU,SAAS,KAAK,gBAAgB,QAAQ,CAAC,aAAa,SAAS,GAAG;wBACnG,YAAY,CAAC,GAAG;oBACpB,OAAO;wBACH,2CAA2C;wBAC3C,MAAM,gBAAgB,SAAS,aAAa;wBAC5C,+DAA+D;wBAC/D,YAAY,CAAC,GAAG,KAAK,GAAG,CAAC,iBAAiB,KAAK,GAAG,CAAC,UAAU,gBAAgB;oBACjF;gBACJ;YACJ,OAAO,IAAI,mBAAmB,KAAK;gBAC/B,2BAA2B;gBAC3B,IAAI,gBAAgB,QAAQ,CAAC,WAAW,gBAAgB,QAAQ,CAAC,UAAU;oBACvE,IAAI,gBAAgB,QAAQ,CAAC,WAAW,SAAS,KAAK,gBAAgB,QAAQ,CAAC,YAAY,SAAS,GAAG;wBACnG,YAAY,CAAC,GAAG;oBACpB,OAAO;wBACH,2CAA2C;wBAC3C,MAAM,gBAAgB,SAAS,aAAa;wBAC5C,+DAA+D;wBAC/D,YAAY,CAAC,GAAG,KAAK,GAAG,CAAC,iBAAiB,KAAK,GAAG,CAAC,UAAU,gBAAgB;oBACjF;gBACJ;YACJ;YACA,IAAI,KAAK,GAAG,CAAC,YAAY,CAAC,IAAI,KAAK,KAAK,GAAG,CAAC,YAAY,CAAC,IAAI,GAAG;gBAC5D,YAAY;YAChB;YACA,CAAC,oBAAoB,SAAS,OAAO,KAAK,OAAO,KAAK,IAAI,kBAAkB,KAAK,CAAC,WAAW,CAAC,oBAAoB,GAAG,YAAY,CAAC,CAAC,EAAE,CAAC;YACtI,CAAC,qBAAqB,SAAS,OAAO,KAAK,OAAO,KAAK,IAAI,mBAAmB,KAAK,CAAC,WAAW,CAAC,oBAAoB,GAAG,YAAY,CAAC,CAAC,EAAE,CAAC;QAC5I;IACJ,GAAG,eAAe,CAAC,MAAM,GAAG,IAAI,cAAc,YAAY,WAAW,GAAG,8SAAA,CAAA,UAAK,CAAC,aAAa,CAAC,UAAU;QAClG,cAAc;QACd,iBAAiB;QACjB,qBAAqB;QACrB,SAAS,YAAY,CAAC,cAAc,KAAK,IAAI;YACzC;YACA,MAAM,SAAS,IAAI,OAAO,KAAK,IAAI,MAAM,SAAS,CAAC,IAAI,CAAC,OAAO;QACnE;QACA,WAAW,GAAG,cAAc,OAAO,KAAK,IAAI,WAAW,WAAW,EAAE,SAAS,OAAO,KAAK,IAAI,CAAC,qBAAqB,MAAM,UAAU,KAAK,OAAO,KAAK,IAAI,mBAAmB,WAAW;IAC1L,GAAG,CAAC,eAAe,SAAS,OAAO,KAAK,IAAI,MAAM,KAAK,KAAK,OAAO,eAAe,aAAa,MAAM,CAAC,aAAa,MAAM,IAAI,IAAI,MAAM,OAAO,KAAK,MAAM,IAAI,KAAK,QAAQ,CAAC,CAAC,SAAS,OAAO,KAAK,IAAI,KAAK,CAAC,UAAU,MAAM,QAAQ,MAAM,IAAI,IAAI,WAAW,GAAG,8SAAA,CAAA,UAAK,CAAC,aAAa,CAAC,OAAO;QACtR,aAAa;QACb,WAAW,GAAG,cAAc,OAAO,KAAK,IAAI,WAAW,IAAI,EAAE,SAAS,OAAO,KAAK,IAAI,CAAC,qBAAqB,MAAM,UAAU,KAAK,OAAO,KAAK,IAAI,mBAAmB,IAAI;IAC5K,GAAG,MAAM,OAAO,IAAI,MAAM,IAAI,KAAK,aAAa,CAAC,MAAM,IAAI,GAAG,MAAM,IAAI,IAAI,mBAAmB,MAAM,MAAM,IAAI,KAAK,YAAY,OAAO,QAAQ,MAAM,WAAW,GAAG,8SAAA,CAAA,UAAK,CAAC,aAAa,CAAC,OAAO;QAC1L,gBAAgB;QAChB,WAAW,GAAG,cAAc,OAAO,KAAK,IAAI,WAAW,OAAO,EAAE,SAAS,OAAO,KAAK,IAAI,CAAC,qBAAqB,MAAM,UAAU,KAAK,OAAO,KAAK,IAAI,mBAAmB,OAAO;IAClL,GAAG,WAAW,GAAG,8SAAA,CAAA,UAAK,CAAC,aAAa,CAAC,OAAO;QACxC,cAAc;QACd,WAAW,GAAG,cAAc,OAAO,KAAK,IAAI,WAAW,KAAK,EAAE,SAAS,OAAO,KAAK,IAAI,CAAC,qBAAqB,MAAM,UAAU,KAAK,OAAO,KAAK,IAAI,mBAAmB,KAAK;IAC9K,GAAG,MAAM,GAAG,GAAG,MAAM,GAAG,GAAG,OAAO,MAAM,KAAK,KAAK,aAAa,MAAM,KAAK,KAAK,MAAM,KAAK,GAAG,MAAM,WAAW,GAAG,WAAW,GAAG,8SAAA,CAAA,UAAK,CAAC,aAAa,CAAC,OAAO;QACtJ,oBAAoB;QACpB,WAAW,GAAG,sBAAsB,2BAA2B,cAAc,OAAO,KAAK,IAAI,WAAW,WAAW,EAAE,SAAS,OAAO,KAAK,IAAI,CAAC,qBAAqB,MAAM,UAAU,KAAK,OAAO,KAAK,IAAI,mBAAmB,WAAW;IAC3O,GAAG,OAAO,MAAM,WAAW,KAAK,aAAa,MAAM,WAAW,KAAK,MAAM,WAAW,IAAI,OAAO,WAAW,GAAG,8SAAA,CAAA,UAAK,CAAC,cAAc,CAAC,MAAM,MAAM,IAAI,MAAM,MAAM,GAAG,MAAM,MAAM,IAAI,SAAS,MAAM,MAAM,IAAI,WAAW,GAAG,8SAAA,CAAA,UAAK,CAAC,aAAa,CAAC,UAAU;QAClP,eAAe;QACf,eAAe;QACf,OAAO,MAAM,iBAAiB,IAAI;QAClC,SAAS,CAAC;YACN,4CAA4C;YAC5C,IAAI,CAAC,SAAS,MAAM,MAAM,GAAG;YAC7B,IAAI,CAAC,aAAa;YAClB,MAAM,MAAM,CAAC,OAAO,IAAI,OAAO,KAAK,IAAI,MAAM,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,MAAM,MAAM,EAAE;YAChF;QACJ;QACA,WAAW,GAAG,cAAc,OAAO,KAAK,IAAI,WAAW,YAAY,EAAE,SAAS,OAAO,KAAK,IAAI,CAAC,qBAAqB,MAAM,UAAU,KAAK,OAAO,KAAK,IAAI,mBAAmB,YAAY;IAC5L,GAAG,MAAM,MAAM,CAAC,KAAK,IAAI,MAAM,WAAW,GAAG,8SAAA,CAAA,UAAK,CAAC,cAAc,CAAC,MAAM,MAAM,IAAI,MAAM,MAAM,GAAG,MAAM,MAAM,IAAI,SAAS,MAAM,MAAM,IAAI,WAAW,GAAG,8SAAA,CAAA,UAAK,CAAC,aAAa,CAAC,UAAU;QAClL,eAAe;QACf,eAAe;QACf,OAAO,MAAM,iBAAiB,IAAI;QAClC,SAAS,CAAC;YACN,4CAA4C;YAC5C,IAAI,CAAC,SAAS,MAAM,MAAM,GAAG;YAC7B,MAAM,MAAM,CAAC,OAAO,IAAI,OAAO,KAAK,IAAI,MAAM,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,MAAM,MAAM,EAAE;YAChF,IAAI,MAAM,gBAAgB,EAAE;YAC5B;QACJ;QACA,WAAW,GAAG,cAAc,OAAO,KAAK,IAAI,WAAW,YAAY,EAAE,SAAS,OAAO,KAAK,IAAI,CAAC,qBAAqB,MAAM,UAAU,KAAK,OAAO,KAAK,IAAI,mBAAmB,YAAY;IAC5L,GAAG,MAAM,MAAM,CAAC,KAAK,IAAI;AAC7B;AACA,SAAS;IACL,IAAI,OAAO,WAAW,aAAa,OAAO;IAC1C,IAAI,OAAO,aAAa,aAAa,OAAO,OAAO,oBAAoB;IACvE,MAAM,eAAe,SAAS,eAAe,CAAC,YAAY,CAAC;IAC3D,IAAI,iBAAiB,UAAU,CAAC,cAAc;QAC1C,OAAO,OAAO,gBAAgB,CAAC,SAAS,eAAe,EAAE,SAAS;IACtE;IACA,OAAO;AACX;AACA,SAAS,aAAa,aAAa,EAAE,YAAY;IAC7C,MAAM,SAAS,CAAC;IAChB;QACI;QACA;KACH,CAAC,OAAO,CAAC,CAAC,QAAQ;QACf,MAAM,WAAW,UAAU;QAC3B,MAAM,SAAS,WAAW,oBAAoB;QAC9C,MAAM,eAAe,WAAW,yBAAyB;QACzD,SAAS,UAAU,MAAM;YACrB;gBACI;gBACA;gBACA;gBACA;aACH,CAAC,OAAO,CAAC,CAAC;gBACP,MAAM,CAAC,GAAG,OAAO,CAAC,EAAE,KAAK,CAAC,GAAG,OAAO,WAAW,WAAW,GAAG,OAAO,EAAE,CAAC,GAAG;YAC9E;QACJ;QACA,IAAI,OAAO,WAAW,YAAY,OAAO,WAAW,UAAU;YAC1D,UAAU;QACd,OAAO,IAAI,OAAO,WAAW,UAAU;YACnC;gBACI;gBACA;gBACA;gBACA;aACH,CAAC,OAAO,CAAC,CAAC;gBACP,IAAI,MAAM,CAAC,IAAI,KAAK,WAAW;oBAC3B,MAAM,CAAC,GAAG,OAAO,CAAC,EAAE,KAAK,CAAC,GAAG;gBACjC,OAAO;oBACH,MAAM,CAAC,GAAG,OAAO,CAAC,EAAE,KAAK,CAAC,GAAG,OAAO,MAAM,CAAC,IAAI,KAAK,WAAW,GAAG,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC,IAAI;gBACnG;YACJ;QACJ,OAAO;YACH,UAAU;QACd;IACJ;IACA,OAAO;AACX;AACA,SAAS;IACL,MAAM,CAAC,cAAc,gBAAgB,GAAG,8SAAA,CAAA,UAAK,CAAC,QAAQ,CAAC,EAAE;IACzD,8SAAA,CAAA,UAAK,CAAC,SAAS,CAAC;QACZ,OAAO,WAAW,SAAS,CAAC,CAAC;YACzB,IAAI,MAAM,OAAO,EAAE;gBACf,WAAW;oBACP,qTAAA,CAAA,UAAQ,CAAC,SAAS,CAAC;wBACf,gBAAgB,CAAC,SAAS,OAAO,MAAM,CAAC,CAAC,IAAI,EAAE,EAAE,KAAK,MAAM,EAAE;oBAClE;gBACJ;gBACA;YACJ;YACA,mCAAmC;YACnC,WAAW;gBACP,qTAAA,CAAA,UAAQ,CAAC,SAAS,CAAC;oBACf,gBAAgB,CAAC;wBACb,MAAM,uBAAuB,OAAO,SAAS,CAAC,CAAC,IAAI,EAAE,EAAE,KAAK,MAAM,EAAE;wBACpE,wCAAwC;wBACxC,IAAI,yBAAyB,CAAC,GAAG;4BAC7B,OAAO;mCACA,OAAO,KAAK,CAAC,GAAG;gCACnB;oCACI,GAAG,MAAM,CAAC,qBAAqB;oCAC/B,GAAG,KAAK;gCACZ;mCACG,OAAO,KAAK,CAAC,uBAAuB;6BAC1C;wBACL;wBACA,OAAO;4BACH;+BACG;yBACN;oBACL;gBACJ;YACJ;QACJ;IACJ,GAAG,EAAE;IACL,OAAO;QACH,QAAQ;IACZ;AACJ;AACA,MAAM,UAAU,WAAW,GAAG,8SAAA,CAAA,UAAK,CAAC,UAAU,CAAC,SAAS,QAAQ,KAAK,EAAE,GAAG;IACtE,MAAM,EAAE,MAAM,EAAE,WAAW,cAAc,EAAE,SAAS;QAChD;QACA;KACH,EAAE,MAAM,EAAE,WAAW,EAAE,SAAS,EAAE,MAAM,EAAE,YAAY,EAAE,QAAQ,OAAO,EAAE,UAAU,EAAE,QAAQ,EAAE,KAAK,EAAE,gBAAgB,qBAAqB,EAAE,YAAY,EAAE,MAAM,sBAAsB,EAAE,MAAM,GAAG,EAAE,KAAK,EAAE,qBAAqB,eAAe,EAAE,GAAG;IACrP,MAAM,CAAC,QAAQ,UAAU,GAAG,8SAAA,CAAA,UAAK,CAAC,QAAQ,CAAC,EAAE;IAC7C,MAAM,oBAAoB,8SAAA,CAAA,UAAK,CAAC,OAAO,CAAC;QACpC,OAAO,MAAM,IAAI,CAAC,IAAI,IAAI;YACtB;SACH,CAAC,MAAM,CAAC,OAAO,MAAM,CAAC,CAAC,QAAQ,MAAM,QAAQ,EAAE,GAAG,CAAC,CAAC,QAAQ,MAAM,QAAQ;IAC/E,GAAG;QACC;QACA;KACH;IACD,MAAM,CAAC,SAAS,WAAW,GAAG,8SAAA,CAAA,UAAK,CAAC,QAAQ,CAAC,EAAE;IAC/C,MAAM,CAAC,UAAU,YAAY,GAAG,8SAAA,CAAA,UAAK,CAAC,QAAQ,CAAC;IAC/C,MAAM,CAAC,aAAa,eAAe,GAAG,8SAAA,CAAA,UAAK,CAAC,QAAQ,CAAC;IACrD,MAAM,CAAC,aAAa,eAAe,GAAG,8SAAA,CAAA,UAAK,CAAC,QAAQ,CAAC,UAAU,WAAW,QAAQ,OAAO,WAAW,cAAc,OAAO,UAAU,IAAI,OAAO,UAAU,CAAC,gCAAgC,OAAO,GAAG,SAAS,UAAU;IACtN,MAAM,UAAU,8SAAA,CAAA,UAAK,CAAC,MAAM,CAAC;IAC7B,MAAM,cAAc,OAAO,IAAI,CAAC,KAAK,OAAO,CAAC,QAAQ,IAAI,OAAO,CAAC,UAAU;IAC3E,MAAM,wBAAwB,8SAAA,CAAA,UAAK,CAAC,MAAM,CAAC;IAC3C,MAAM,mBAAmB,8SAAA,CAAA,UAAK,CAAC,MAAM,CAAC;IACtC,MAAM,cAAc,8SAAA,CAAA,UAAK,CAAC,WAAW,CAAC,CAAC;QACnC,UAAU,CAAC;YACP,IAAI;YACJ,IAAI,CAAC,CAAC,CAAC,eAAe,OAAO,IAAI,CAAC,CAAC,QAAQ,MAAM,EAAE,KAAK,cAAc,EAAE,CAAC,KAAK,OAAO,KAAK,IAAI,aAAa,MAAM,GAAG;gBAChH,WAAW,OAAO,CAAC,cAAc,EAAE;YACvC;YACA,OAAO,OAAO,MAAM,CAAC,CAAC,EAAE,EAAE,EAAE,GAAG,OAAO,cAAc,EAAE;QAC1D;IACJ,GAAG,EAAE;IACL,8SAAA,CAAA,UAAK,CAAC,SAAS,CAAC;QACZ,OAAO,WAAW,SAAS,CAAC,CAAC;YACzB,IAAI,MAAM,OAAO,EAAE;gBACf,0CAA0C;gBAC1C,sBAAsB;oBAClB,UAAU,CAAC,SAAS,OAAO,GAAG,CAAC,CAAC,IAAI,EAAE,EAAE,KAAK,MAAM,EAAE,GAAG;gCAC5C,GAAG,CAAC;gCACJ,QAAQ;4BACZ,IAAI;gBAChB;gBACA;YACJ;YACA,mCAAmC;YACnC,WAAW;gBACP,qTAAA,CAAA,UAAQ,CAAC,SAAS,CAAC;oBACf,UAAU,CAAC;wBACP,MAAM,uBAAuB,OAAO,SAAS,CAAC,CAAC,IAAI,EAAE,EAAE,KAAK,MAAM,EAAE;wBACpE,wCAAwC;wBACxC,IAAI,yBAAyB,CAAC,GAAG;4BAC7B,OAAO;mCACA,OAAO,KAAK,CAAC,GAAG;gCACnB;oCACI,GAAG,MAAM,CAAC,qBAAqB;oCAC/B,GAAG,KAAK;gCACZ;mCACG,OAAO,KAAK,CAAC,uBAAuB;6BAC1C;wBACL;wBACA,OAAO;4BACH;+BACG;yBACN;oBACL;gBACJ;YACJ;QACJ;IACJ,GAAG;QACC;KACH;IACD,8SAAA,CAAA,UAAK,CAAC,SAAS,CAAC;QACZ,IAAI,UAAU,UAAU;YACpB,eAAe;YACf;QACJ;QACA,IAAI,UAAU,UAAU;YACpB,sCAAsC;YACtC,IAAI,OAAO,UAAU,IAAI,OAAO,UAAU,CAAC,gCAAgC,OAAO,EAAE;gBAChF,sBAAsB;gBACtB,eAAe;YACnB,OAAO;gBACH,gBAAgB;gBAChB,eAAe;YACnB;QACJ;QACA,IAAI,OAAO,WAAW,aAAa;QACnC,MAAM,iBAAiB,OAAO,UAAU,CAAC;QACzC,IAAI;YACA,mBAAmB;YACnB,eAAe,gBAAgB,CAAC,UAAU,CAAC,EAAE,OAAO,EAAE;gBAClD,IAAI,SAAS;oBACT,eAAe;gBACnB,OAAO;oBACH,eAAe;gBACnB;YACJ;QACJ,EAAE,OAAO,OAAO;YACZ,cAAc;YACd,eAAe,WAAW,CAAC,CAAC,EAAE,OAAO,EAAE;gBACnC,IAAI;oBACA,IAAI,SAAS;wBACT,eAAe;oBACnB,OAAO;wBACH,eAAe;oBACnB;gBACJ,EAAE,OAAO,GAAG;oBACR,QAAQ,KAAK,CAAC;gBAClB;YACJ;QACJ;IACJ,GAAG;QACC;KACH;IACD,8SAAA,CAAA,UAAK,CAAC,SAAS,CAAC;QACZ,6EAA6E;QAC7E,IAAI,OAAO,MAAM,IAAI,GAAG;YACpB,YAAY;QAChB;IACJ,GAAG;QACC;KACH;IACD,8SAAA,CAAA,UAAK,CAAC,SAAS,CAAC;QACZ,MAAM,gBAAgB,CAAC;YACnB,IAAI;YACJ,MAAM,kBAAkB,OAAO,KAAK,CAAC,CAAC,MAAM,KAAK,CAAC,IAAI,IAAI,MAAM,IAAI,KAAK;YACzE,IAAI,iBAAiB;gBACjB,IAAI;gBACJ,YAAY;gBACZ,CAAC,oBAAoB,QAAQ,OAAO,KAAK,OAAO,KAAK,IAAI,kBAAkB,KAAK;YACpF;YACA,IAAI,MAAM,IAAI,KAAK,YAAY,CAAC,SAAS,aAAa,KAAK,QAAQ,OAAO,IAAI,CAAC,CAAC,mBAAmB,QAAQ,OAAO,KAAK,OAAO,KAAK,IAAI,iBAAiB,QAAQ,CAAC,SAAS,aAAa,CAAC,CAAC,GAAG;gBACxL,YAAY;YAChB;QACJ;QACA,SAAS,gBAAgB,CAAC,WAAW;QACrC,OAAO,IAAI,SAAS,mBAAmB,CAAC,WAAW;IACvD,GAAG;QACC;KACH;IACD,8SAAA,CAAA,UAAK,CAAC,SAAS,CAAC;QACZ,IAAI,QAAQ,OAAO,EAAE;YACjB,OAAO;gBACH,IAAI,sBAAsB,OAAO,EAAE;oBAC/B,sBAAsB,OAAO,CAAC,KAAK,CAAC;wBAChC,eAAe;oBACnB;oBACA,sBAAsB,OAAO,GAAG;oBAChC,iBAAiB,OAAO,GAAG;gBAC/B;YACJ;QACJ;IACJ,GAAG;QACC,QAAQ,OAAO;KAClB;IACD,OACA,WAAW,GAAG,8SAAA,CAAA,UAAK,CAAC,aAAa,CAAC,WAAW;QACzC,KAAK;QACL,cAAc,GAAG,mBAAmB,CAAC,EAAE,aAAa;QACpD,UAAU,CAAC;QACX,aAAa;QACb,iBAAiB;QACjB,eAAe;QACf,0BAA0B;IAC9B,GAAG,kBAAkB,GAAG,CAAC,CAAC,UAAU;QAChC,IAAI;QACJ,MAAM,CAAC,GAAG,EAAE,GAAG,SAAS,KAAK,CAAC;QAC9B,IAAI,CAAC,OAAO,MAAM,EAAE,OAAO;QAC3B,OAAO,WAAW,GAAG,8SAAA,CAAA,UAAK,CAAC,aAAa,CAAC,MAAM;YAC3C,KAAK;YACL,KAAK,QAAQ,SAAS,yBAAyB;YAC/C,UAAU,CAAC;YACX,KAAK;YACL,WAAW;YACX,uBAAuB;YACvB,qBAAqB;YACrB,mBAAmB;YACnB,eAAe,YAAY,OAAO,MAAM,GAAG,KAAK,CAAC;YACjD,mBAAmB;YACnB,OAAO;gBACH,wBAAwB,GAAG,CAAC,CAAC,YAAY,OAAO,CAAC,EAAE,KAAK,OAAO,KAAK,IAAI,UAAU,MAAM,KAAK,EAAE,EAAE,CAAC;gBAClG,WAAW,GAAG,YAAY,EAAE,CAAC;gBAC7B,SAAS,GAAG,IAAI,EAAE,CAAC;gBACnB,GAAG,KAAK;gBACR,GAAG,aAAa,QAAQ,aAAa;YACzC;YACA,QAAQ,CAAC;gBACL,IAAI,iBAAiB,OAAO,IAAI,CAAC,MAAM,aAAa,CAAC,QAAQ,CAAC,MAAM,aAAa,GAAG;oBAChF,iBAAiB,OAAO,GAAG;oBAC3B,IAAI,sBAAsB,OAAO,EAAE;wBAC/B,sBAAsB,OAAO,CAAC,KAAK,CAAC;4BAChC,eAAe;wBACnB;wBACA,sBAAsB,OAAO,GAAG;oBACpC;gBACJ;YACJ;YACA,SAAS,CAAC;gBACN,MAAM,mBAAmB,MAAM,MAAM,YAAY,eAAe,MAAM,MAAM,CAAC,OAAO,CAAC,WAAW,KAAK;gBACrG,IAAI,kBAAkB;gBACtB,IAAI,CAAC,iBAAiB,OAAO,EAAE;oBAC3B,iBAAiB,OAAO,GAAG;oBAC3B,sBAAsB,OAAO,GAAG,MAAM,aAAa;gBACvD;YACJ;YACA,cAAc,IAAI,YAAY;YAC9B,aAAa,IAAI,YAAY;YAC7B,cAAc;gBACV,8EAA8E;gBAC9E,IAAI,CAAC,aAAa;oBACd,YAAY;gBAChB;YACJ;YACA,WAAW,IAAI,YAAY;YAC3B,eAAe,CAAC;gBACZ,MAAM,mBAAmB,MAAM,MAAM,YAAY,eAAe,MAAM,MAAM,CAAC,OAAO,CAAC,WAAW,KAAK;gBACrG,IAAI,kBAAkB;gBACtB,eAAe;YACnB;YACA,aAAa,IAAI,eAAe;QACpC,GAAG,OAAO,MAAM,CAAC,CAAC,QAAQ,CAAC,MAAM,QAAQ,IAAI,UAAU,KAAK,MAAM,QAAQ,KAAK,UAAU,GAAG,CAAC,CAAC,OAAO;YACjG,IAAI,wBAAwB;YAC5B,OAAO,WAAW,GAAG,8SAAA,CAAA,UAAK,CAAC,aAAa,CAAC,OAAO;gBAC5C,KAAK,MAAM,EAAE;gBACb,OAAO;gBACP,OAAO;gBACP,OAAO;gBACP,mBAAmB;gBACnB,UAAU,CAAC,yBAAyB,gBAAgB,OAAO,KAAK,IAAI,aAAa,QAAQ,KAAK,OAAO,yBAAyB;gBAC9H,WAAW,gBAAgB,OAAO,KAAK,IAAI,aAAa,SAAS;gBACjE,sBAAsB,gBAAgB,OAAO,KAAK,IAAI,aAAa,oBAAoB;gBACvF,QAAQ;gBACR,eAAe;gBACf,aAAa,CAAC,4BAA4B,gBAAgB,OAAO,KAAK,IAAI,aAAa,WAAW,KAAK,OAAO,4BAA4B;gBAC1I,aAAa;gBACb,UAAU;gBACV,OAAO,gBAAgB,OAAO,KAAK,IAAI,aAAa,KAAK;gBACzD,UAAU,gBAAgB,OAAO,KAAK,IAAI,aAAa,QAAQ;gBAC/D,YAAY,gBAAgB,OAAO,KAAK,IAAI,aAAa,UAAU;gBACnE,mBAAmB,gBAAgB,OAAO,KAAK,IAAI,aAAa,iBAAiB;gBACjF,mBAAmB,gBAAgB,OAAO,KAAK,IAAI,aAAa,iBAAiB;gBACjF,sBAAsB,gBAAgB,OAAO,KAAK,IAAI,aAAa,oBAAoB;gBACvF,aAAa;gBACb,QAAQ,OAAO,MAAM,CAAC,CAAC,IAAI,EAAE,QAAQ,IAAI,MAAM,QAAQ;gBACvD,SAAS,QAAQ,MAAM,CAAC,CAAC,IAAI,EAAE,QAAQ,IAAI,MAAM,QAAQ;gBACzD,YAAY;gBACZ,iBAAiB;gBACjB,KAAK;gBACL,UAAU;gBACV,iBAAiB,MAAM,eAAe;YAC1C;QACJ;IACJ;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 7687, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/bloomgoo/gold-exchange-platform/frontend/node_modules/.pnpm/%40kayron013%2Blexorank%402.0.0/node_modules/%40kayron013/lexorank/lib/index.js"], "sourcesContent": ["\"use strict\";\r\nObject.defineProperty(exports, \"__esModule\", { value: true });\r\nvar LexoRank = /** @class */ (function () {\r\n    function LexoRank(value, bucket) {\r\n        if (bucket === void 0) { bucket = '0'; }\r\n        this.value = value;\r\n        this.bucket = bucket;\r\n        if (!LexoRank.isValidLexValue(value)) {\r\n            throw \"Invalid lex value \\\"\" + value + \"\\\"\";\r\n        }\r\n        if (!LexoRank.isValidLexBucket(bucket)) {\r\n            throw \"Invalid lex bucket \\\"\" + bucket + \"\\\"\";\r\n        }\r\n        this.value = value;\r\n        this.bucket = bucket;\r\n    }\r\n    LexoRank.from = function (lex) {\r\n        if (lex instanceof LexoRank) {\r\n            return new LexoRank(lex.value, lex.bucket);\r\n        }\r\n        var _a = this.parse(lex), value = _a.value, bucket = _a.bucket;\r\n        return new LexoRank(value, bucket);\r\n    };\r\n    LexoRank.parse = function (lex) {\r\n        var regex = /^(?<bucket>[0-2])\\|(?<value>[0-9a-z]*[1-9a-z])$/;\r\n        var match = regex.exec(lex);\r\n        if (!match) {\r\n            throw 'Invalid lex string';\r\n        }\r\n        return { value: match.groups.value, bucket: match.groups.bucket };\r\n    };\r\n    LexoRank.prototype.toString = function () {\r\n        return this.bucket + \"|\" + this.value;\r\n    };\r\n    LexoRank.nextBucket = function (bucket) {\r\n        if (!this.isValidLexBucket(bucket)) {\r\n            throw \"Invalid lex bucket \\\"\" + bucket + \"\\\"\";\r\n        }\r\n        if (bucket === '2')\r\n            return '0';\r\n        return String.fromCharCode(bucket.charCodeAt(0) + 1);\r\n    };\r\n    LexoRank.prevBucket = function (bucket) {\r\n        if (!this.isValidLexBucket(bucket)) {\r\n            throw \"Invalid lex bucket \\\"\" + bucket + \"\\\"\";\r\n        }\r\n        if (bucket === '0')\r\n            return '2';\r\n        return String.fromCharCode(bucket.charCodeAt(0) - 1);\r\n    };\r\n    LexoRank.isValidLexValue = function (value) {\r\n        var regex = /^[0-9a-z]*[1-9a-z]$/;\r\n        return regex.test(value);\r\n    };\r\n    LexoRank.isValidLexBucket = function (bucket) {\r\n        var regex = /^[0-2]$/;\r\n        return regex.test(bucket);\r\n    };\r\n    LexoRank.prototype.lessThan = function (lex) {\r\n        var other = LexoRank.from(lex);\r\n        var len = Math.max(this.value.length, other.value.length);\r\n        for (var idx = 0; idx < len; idx++) {\r\n            var charA = this.value[idx];\r\n            var charB = other.value[idx];\r\n            if (!charB)\r\n                return false; // a is more specific\r\n            if (!charA)\r\n                return true; // b is more specific\r\n            if (charA < charB)\r\n                return true;\r\n            if (charA > charB)\r\n                return false;\r\n        }\r\n        return false;\r\n    };\r\n    LexoRank.prototype.increment = function () {\r\n        for (var idx = this.value.length - 1; idx >= 0; idx--) {\r\n            var char = this.value[idx];\r\n            if (char === 'z')\r\n                continue;\r\n            var newVal_1 = this.value.substring(0, idx) + LexoRank.incrementChar(char);\r\n            return new LexoRank(newVal_1, this.bucket);\r\n        }\r\n        var newVal = this.value + '1';\r\n        return new LexoRank(newVal, this.bucket);\r\n    };\r\n    LexoRank.prototype.decrement = function () {\r\n        var length = this.value.length;\r\n        var char = this.value[length - 1];\r\n        if (char !== '1') {\r\n            var newVal_2 = this.value.substring(0, length - 1) + LexoRank.decrementChar(char);\r\n            return new LexoRank(newVal_2, this.bucket);\r\n        }\r\n        if (this.hasNonZeroLeadingChars()) {\r\n            var newVal_3 = LexoRank.cleanTrailingZeros(this.value.substring(0, length - 1));\r\n            return new LexoRank(newVal_3, this.bucket);\r\n        }\r\n        var newVal = '0' + this.value;\r\n        return new LexoRank(newVal, this.bucket);\r\n    };\r\n    LexoRank.prototype.hasNonZeroLeadingChars = function () {\r\n        return this.value.length > 1 && !this.value.substr(0, this.value.length - 1).match(/^0+$/);\r\n    };\r\n    LexoRank.cleanTrailingZeros = function (str) {\r\n        var regex = /^(?<value>[0-9a-z]*[1-9a-z])0*$/;\r\n        var match = regex.exec(str);\r\n        if (!match) {\r\n            throw 'Invalid lex string';\r\n        }\r\n        return match.groups.value;\r\n    };\r\n    LexoRank.prototype.append = function (str) {\r\n        return new LexoRank(this.value + str, this.bucket);\r\n    };\r\n    LexoRank.incrementChar = function (char) {\r\n        if (char === 'z')\r\n            return '-1';\r\n        if (char === '9')\r\n            return 'a';\r\n        return String.fromCharCode(char.charCodeAt(0) + 1);\r\n    };\r\n    LexoRank.decrementChar = function (char) {\r\n        if (char === '1')\r\n            return '-1';\r\n        if (char === 'a')\r\n            return '9';\r\n        return String.fromCharCode(char.charCodeAt(0) - 1);\r\n    };\r\n    LexoRank.between = function (lexBefore, lexAfter) {\r\n        if (!lexBefore && !lexAfter) {\r\n            throw 'Only one argument may be null';\r\n        }\r\n        if (!lexAfter) {\r\n            return LexoRank.from(lexBefore).increment();\r\n        }\r\n        if (!lexBefore) {\r\n            return LexoRank.from(lexAfter).decrement();\r\n        }\r\n        var before = LexoRank.from(lexBefore);\r\n        var after = LexoRank.from(lexAfter);\r\n        if (before.bucket !== after.bucket) {\r\n            throw 'Lex buckets must be the same';\r\n        }\r\n        if (!before.lessThan(after)) {\r\n            throw before.value + \" is not less than \" + after.value;\r\n        }\r\n        var incremented = before.increment();\r\n        if (incremented.lessThan(after))\r\n            return incremented;\r\n        var plus1 = before.append('1');\r\n        if (plus1.lessThan(after))\r\n            return plus1;\r\n        var pre = '0';\r\n        var plus01 = before.append(pre + \"1\");\r\n        while (!plus01.lessThan(after)) {\r\n            pre += '0';\r\n            plus01 = before.append(pre + \"1\");\r\n        }\r\n        return plus01;\r\n    };\r\n    return LexoRank;\r\n}());\r\nexports.default = LexoRank;\r\n"], "names": [], "mappings": "AAAA;AACA,OAAO,cAAc,CAAC,SAAS,cAAc;IAAE,OAAO;AAAK;AAC3D,IAAI,WAA0B;IAC1B,SAAS,SAAS,KAAK,EAAE,MAAM;QAC3B,IAAI,WAAW,KAAK,GAAG;YAAE,SAAS;QAAK;QACvC,IAAI,CAAC,KAAK,GAAG;QACb,IAAI,CAAC,MAAM,GAAG;QACd,IAAI,CAAC,SAAS,eAAe,CAAC,QAAQ;YAClC,MAAM,yBAAyB,QAAQ;QAC3C;QACA,IAAI,CAAC,SAAS,gBAAgB,CAAC,SAAS;YACpC,MAAM,0BAA0B,SAAS;QAC7C;QACA,IAAI,CAAC,KAAK,GAAG;QACb,IAAI,CAAC,MAAM,GAAG;IAClB;IACA,SAAS,IAAI,GAAG,SAAU,GAAG;QACzB,IAAI,eAAe,UAAU;YACzB,OAAO,IAAI,SAAS,IAAI,KAAK,EAAE,IAAI,MAAM;QAC7C;QACA,IAAI,KAAK,IAAI,CAAC,KAAK,CAAC,MAAM,QAAQ,GAAG,KAAK,EAAE,SAAS,GAAG,MAAM;QAC9D,OAAO,IAAI,SAAS,OAAO;IAC/B;IACA,SAAS,KAAK,GAAG,SAAU,GAAG;QAC1B,IAAI,QAAQ;QACZ,IAAI,QAAQ,MAAM,IAAI,CAAC;QACvB,IAAI,CAAC,OAAO;YACR,MAAM;QACV;QACA,OAAO;YAAE,OAAO,MAAM,MAAM,CAAC,KAAK;YAAE,QAAQ,MAAM,MAAM,CAAC,MAAM;QAAC;IACpE;IACA,SAAS,SAAS,CAAC,QAAQ,GAAG;QAC1B,OAAO,IAAI,CAAC,MAAM,GAAG,MAAM,IAAI,CAAC,KAAK;IACzC;IACA,SAAS,UAAU,GAAG,SAAU,MAAM;QAClC,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,SAAS;YAChC,MAAM,0BAA0B,SAAS;QAC7C;QACA,IAAI,WAAW,KACX,OAAO;QACX,OAAO,OAAO,YAAY,CAAC,OAAO,UAAU,CAAC,KAAK;IACtD;IACA,SAAS,UAAU,GAAG,SAAU,MAAM;QAClC,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,SAAS;YAChC,MAAM,0BAA0B,SAAS;QAC7C;QACA,IAAI,WAAW,KACX,OAAO;QACX,OAAO,OAAO,YAAY,CAAC,OAAO,UAAU,CAAC,KAAK;IACtD;IACA,SAAS,eAAe,GAAG,SAAU,KAAK;QACtC,IAAI,QAAQ;QACZ,OAAO,MAAM,IAAI,CAAC;IACtB;IACA,SAAS,gBAAgB,GAAG,SAAU,MAAM;QACxC,IAAI,QAAQ;QACZ,OAAO,MAAM,IAAI,CAAC;IACtB;IACA,SAAS,SAAS,CAAC,QAAQ,GAAG,SAAU,GAAG;QACvC,IAAI,QAAQ,SAAS,IAAI,CAAC;QAC1B,IAAI,MAAM,KAAK,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,MAAM,KAAK,CAAC,MAAM;QACxD,IAAK,IAAI,MAAM,GAAG,MAAM,KAAK,MAAO;YAChC,IAAI,QAAQ,IAAI,CAAC,KAAK,CAAC,IAAI;YAC3B,IAAI,QAAQ,MAAM,KAAK,CAAC,IAAI;YAC5B,IAAI,CAAC,OACD,OAAO,OAAO,qBAAqB;YACvC,IAAI,CAAC,OACD,OAAO,MAAM,qBAAqB;YACtC,IAAI,QAAQ,OACR,OAAO;YACX,IAAI,QAAQ,OACR,OAAO;QACf;QACA,OAAO;IACX;IACA,SAAS,SAAS,CAAC,SAAS,GAAG;QAC3B,IAAK,IAAI,MAAM,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,GAAG,OAAO,GAAG,MAAO;YACnD,IAAI,OAAO,IAAI,CAAC,KAAK,CAAC,IAAI;YAC1B,IAAI,SAAS,KACT;YACJ,IAAI,WAAW,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,GAAG,OAAO,SAAS,aAAa,CAAC;YACrE,OAAO,IAAI,SAAS,UAAU,IAAI,CAAC,MAAM;QAC7C;QACA,IAAI,SAAS,IAAI,CAAC,KAAK,GAAG;QAC1B,OAAO,IAAI,SAAS,QAAQ,IAAI,CAAC,MAAM;IAC3C;IACA,SAAS,SAAS,CAAC,SAAS,GAAG;QAC3B,IAAI,SAAS,IAAI,CAAC,KAAK,CAAC,MAAM;QAC9B,IAAI,OAAO,IAAI,CAAC,KAAK,CAAC,SAAS,EAAE;QACjC,IAAI,SAAS,KAAK;YACd,IAAI,WAAW,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,GAAG,SAAS,KAAK,SAAS,aAAa,CAAC;YAC5E,OAAO,IAAI,SAAS,UAAU,IAAI,CAAC,MAAM;QAC7C;QACA,IAAI,IAAI,CAAC,sBAAsB,IAAI;YAC/B,IAAI,WAAW,SAAS,kBAAkB,CAAC,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,GAAG,SAAS;YAC5E,OAAO,IAAI,SAAS,UAAU,IAAI,CAAC,MAAM;QAC7C;QACA,IAAI,SAAS,MAAM,IAAI,CAAC,KAAK;QAC7B,OAAO,IAAI,SAAS,QAAQ,IAAI,CAAC,MAAM;IAC3C;IACA,SAAS,SAAS,CAAC,sBAAsB,GAAG;QACxC,OAAO,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,GAAG,KAAK,CAAC;IACvF;IACA,SAAS,kBAAkB,GAAG,SAAU,GAAG;QACvC,IAAI,QAAQ;QACZ,IAAI,QAAQ,MAAM,IAAI,CAAC;QACvB,IAAI,CAAC,OAAO;YACR,MAAM;QACV;QACA,OAAO,MAAM,MAAM,CAAC,KAAK;IAC7B;IACA,SAAS,SAAS,CAAC,MAAM,GAAG,SAAU,GAAG;QACrC,OAAO,IAAI,SAAS,IAAI,CAAC,KAAK,GAAG,KAAK,IAAI,CAAC,MAAM;IACrD;IACA,SAAS,aAAa,GAAG,SAAU,IAAI;QACnC,IAAI,SAAS,KACT,OAAO;QACX,IAAI,SAAS,KACT,OAAO;QACX,OAAO,OAAO,YAAY,CAAC,KAAK,UAAU,CAAC,KAAK;IACpD;IACA,SAAS,aAAa,GAAG,SAAU,IAAI;QACnC,IAAI,SAAS,KACT,OAAO;QACX,IAAI,SAAS,KACT,OAAO;QACX,OAAO,OAAO,YAAY,CAAC,KAAK,UAAU,CAAC,KAAK;IACpD;IACA,SAAS,OAAO,GAAG,SAAU,SAAS,EAAE,QAAQ;QAC5C,IAAI,CAAC,aAAa,CAAC,UAAU;YACzB,MAAM;QACV;QACA,IAAI,CAAC,UAAU;YACX,OAAO,SAAS,IAAI,CAAC,WAAW,SAAS;QAC7C;QACA,IAAI,CAAC,WAAW;YACZ,OAAO,SAAS,IAAI,CAAC,UAAU,SAAS;QAC5C;QACA,IAAI,SAAS,SAAS,IAAI,CAAC;QAC3B,IAAI,QAAQ,SAAS,IAAI,CAAC;QAC1B,IAAI,OAAO,MAAM,KAAK,MAAM,MAAM,EAAE;YAChC,MAAM;QACV;QACA,IAAI,CAAC,OAAO,QAAQ,CAAC,QAAQ;YACzB,MAAM,OAAO,KAAK,GAAG,uBAAuB,MAAM,KAAK;QAC3D;QACA,IAAI,cAAc,OAAO,SAAS;QAClC,IAAI,YAAY,QAAQ,CAAC,QACrB,OAAO;QACX,IAAI,QAAQ,OAAO,MAAM,CAAC;QAC1B,IAAI,MAAM,QAAQ,CAAC,QACf,OAAO;QACX,IAAI,MAAM;QACV,IAAI,SAAS,OAAO,MAAM,CAAC,MAAM;QACjC,MAAO,CAAC,OAAO,QAAQ,CAAC,OAAQ;YAC5B,OAAO;YACP,SAAS,OAAO,MAAM,CAAC,MAAM;QACjC;QACA,OAAO;IACX;IACA,OAAO;AACX;AACA,QAAQ,OAAO,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 7850, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/bloomgoo/gold-exchange-platform/frontend/node_modules/.pnpm/clsx%402.1.1/node_modules/clsx/dist/clsx.mjs"], "sourcesContent": ["function r(e){var t,f,n=\"\";if(\"string\"==typeof e||\"number\"==typeof e)n+=e;else if(\"object\"==typeof e)if(Array.isArray(e)){var o=e.length;for(t=0;t<o;t++)e[t]&&(f=r(e[t]))&&(n&&(n+=\" \"),n+=f)}else for(f in e)e[f]&&(n&&(n+=\" \"),n+=f);return n}export function clsx(){for(var e,t,f=0,n=\"\",o=arguments.length;f<o;f++)(e=arguments[f])&&(t=r(e))&&(n&&(n+=\" \"),n+=t);return n}export default clsx;"], "names": [], "mappings": ";;;;AAAA,SAAS,EAAE,CAAC;IAAE,IAAI,GAAE,GAAE,IAAE;IAAG,IAAG,YAAU,OAAO,KAAG,YAAU,OAAO,GAAE,KAAG;SAAO,IAAG,YAAU,OAAO,GAAE,IAAG,MAAM,OAAO,CAAC,IAAG;QAAC,IAAI,IAAE,EAAE,MAAM;QAAC,IAAI,IAAE,GAAE,IAAE,GAAE,IAAI,CAAC,CAAC,EAAE,IAAE,CAAC,IAAE,EAAE,CAAC,CAAC,EAAE,CAAC,KAAG,CAAC,KAAG,CAAC,KAAG,GAAG,GAAE,KAAG,CAAC;IAAC,OAAM,IAAI,KAAK,EAAE,CAAC,CAAC,EAAE,IAAE,CAAC,KAAG,CAAC,KAAG,GAAG,GAAE,KAAG,CAAC;IAAE,OAAO;AAAC;AAAQ,SAAS;IAAO,IAAI,IAAI,GAAE,GAAE,IAAE,GAAE,IAAE,IAAG,IAAE,UAAU,MAAM,EAAC,IAAE,GAAE,IAAI,CAAC,IAAE,SAAS,CAAC,EAAE,KAAG,CAAC,IAAE,EAAE,EAAE,KAAG,CAAC,KAAG,CAAC,KAAG,GAAG,GAAE,KAAG,CAAC;IAAE,OAAO;AAAC;uCAAgB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 7873, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/bloomgoo/gold-exchange-platform/frontend/node_modules/.pnpm/%40swc%2Bhelpers%400.5.15/node_modules/%40swc/helpers/cjs/_interop_require_default.cjs"], "sourcesContent": ["\"use strict\";\n\nfunction _interop_require_default(obj) {\n    return obj && obj.__esModule ? obj : { default: obj };\n}\nexports._ = _interop_require_default;\n"], "names": [], "mappings": "AAAA;AAEA,SAAS,yBAAyB,GAAG;IACjC,OAAO,OAAO,IAAI,UAAU,GAAG,MAAM;QAAE,SAAS;IAAI;AACxD;AACA,QAAQ,CAAC,GAAG", "ignoreList": [0], "debugId": null}}]}
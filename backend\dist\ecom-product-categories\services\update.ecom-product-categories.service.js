"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UpdateEcomProductCategoriesService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const event_emitter_1 = require("@nestjs/event-emitter");
const typeorm_transactional_1 = require("typeorm-transactional");
const base_ecom_product_categories_service_1 = require("./base.ecom-product-categories.service");
const slug_ecom_product_categories_service_1 = require("./slug.ecom-product-categories.service");
const ecom_product_categories_entity_1 = require("../entity/ecom-product-categories.entity");
const update_ecom_product_category_dto_1 = require("../dto/update-ecom-product-category.dto");
let UpdateEcomProductCategoriesService = class UpdateEcomProductCategoriesService extends base_ecom_product_categories_service_1.BaseEcomProductCategoriesService {
    categoryRepository;
    dataSource;
    eventEmitter;
    slugService;
    constructor(categoryRepository, dataSource, eventEmitter, slugService) {
        super(categoryRepository, dataSource, eventEmitter);
        this.categoryRepository = categoryRepository;
        this.dataSource = dataSource;
        this.eventEmitter = eventEmitter;
        this.slugService = slugService;
    }
    async update(id, updateDto, userId) {
        try {
            this.logger.debug(`Đang cập nhật danh mục sản phẩm với ID: ${id}`);
            const category = await this.findById(id, ['parent']);
            if (!category) {
                throw new common_1.NotFoundException(`Không tìm thấy danh mục với ID: ${id}`);
            }
            const oldData = this.toDto(category);
            let newSlug = category.slug;
            const nameChanged = updateDto.name !== undefined && updateDto.name !== category.name;
            if (nameChanged) {
                const nameToUse = updateDto.name !== undefined ? updateDto.name : category.name;
                newSlug = await this.slugService.generateUniqueSlugForUpdate(nameToUse, id);
            }
            if (updateDto.name !== undefined) {
                category.name = updateDto.name;
            }
            if (nameChanged) {
                category.slug = newSlug;
            }
            if (updateDto.description !== undefined) {
                category.description = updateDto.description;
            }
            if (updateDto.isActive !== undefined) {
                category.isActive = updateDto.isActive;
            }
            if (updateDto.imageUrl !== undefined) {
                category.imageUrl = updateDto.imageUrl;
            }
            if (updateDto.parentId !== undefined) {
                if (updateDto.parentId === null) {
                    category.parent = null;
                }
                else if (updateDto.parentId !== category.parent?.id) {
                    const parent = await this.findById(updateDto.parentId);
                    if (!parent) {
                        throw new common_1.NotFoundException(`Không tìm thấy danh mục cha với ID: ${updateDto.parentId}`);
                    }
                    if (parent.id === id) {
                        throw new common_1.BadRequestException('Danh mục không thể là danh mục cha của chính nó');
                    }
                    const isChild = await this.isChildOf(parent.id, id);
                    if (isChild) {
                        throw new common_1.BadRequestException('Không thể chọn danh mục con làm danh mục cha');
                    }
                    category.parent = parent;
                }
            }
            category.updatedBy = userId;
            const updatedCategory = await this.categoryRepository.save(category);
            const categoryDto = this.toDto(updatedCategory);
            if (!categoryDto) {
                throw new common_1.InternalServerErrorException('Không thể chuyển đổi entity thành DTO');
            }
            this.eventEmitter.emit(this.EVENT_CATEGORY_UPDATED, {
                categoryId: categoryDto.id,
                userId,
                oldData,
                newData: categoryDto,
            });
            return categoryDto;
        }
        catch (error) {
            this.logger.error(`Lỗi khi cập nhật danh mục sản phẩm: ${error.message}`, error.stack);
            if (error instanceof common_1.BadRequestException) {
                throw error;
            }
            throw new common_1.InternalServerErrorException(`Không thể cập nhật danh mục sản phẩm: ${error.message}`);
        }
    }
    async isChildOf(childId, parentId) {
        const child = await this.findById(childId, ['parent']);
        if (!child?.parent) {
            return false;
        }
        if (child.parent.id === parentId) {
            return true;
        }
        return this.isChildOf(child.parent.id, parentId);
    }
};
exports.UpdateEcomProductCategoriesService = UpdateEcomProductCategoriesService;
__decorate([
    (0, typeorm_transactional_1.Transactional)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, update_ecom_product_category_dto_1.UpdateEcomProductCategoryDto, String]),
    __metadata("design:returntype", Promise)
], UpdateEcomProductCategoriesService.prototype, "update", null);
exports.UpdateEcomProductCategoriesService = UpdateEcomProductCategoriesService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(ecom_product_categories_entity_1.EcomProductCategories)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        typeorm_2.DataSource,
        event_emitter_1.EventEmitter2,
        slug_ecom_product_categories_service_1.SlugEcomProductCategoriesService])
], UpdateEcomProductCategoriesService);
//# sourceMappingURL=update.ecom-product-categories.service.js.map
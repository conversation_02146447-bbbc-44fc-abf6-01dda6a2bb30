import { Repository, DataSource } from 'typeorm';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { BaseCmsCategoriesService } from './base.cms-categories.service';
import { CmsCategories } from '../entity/cms-categories.entity';
import { CmsCategoryDto } from '../dto/cms-category.dto';
import { CustomPaginationQueryDto } from '../../common/dto/custom-pagination-query.dto';
export declare class ReadCmsCategoriesService extends BaseCmsCategoriesService {
    protected readonly categoryRepository: Repository<CmsCategories>;
    protected readonly dataSource: DataSource;
    protected readonly eventEmitter: EventEmitter2;
    constructor(categoryRepository: Repository<CmsCategories>, dataSource: DataSource, eventEmitter: EventEmitter2);
    findAll(params: CustomPaginationQueryDto): Promise<{
        data: CmsCategoryDto[];
        total: number;
    }>;
    search(keyword: string, params: CustomPaginationQueryDto): Promise<{
        data: CmsCategoryDto[];
        total: number;
    }>;
    count(filter?: string): Promise<number>;
    findOne(id: string, relations?: string[]): Promise<CmsCategoryDto | null>;
    findOneOrFail(id: string, relations?: string[]): Promise<CmsCategoryDto | null>;
    findDeleted(params: CustomPaginationQueryDto): Promise<{
        data: CmsCategoryDto[];
        total: number;
    }>;
    getActiveCategories(params: CustomPaginationQueryDto): Promise<{
        data: CmsCategoryDto[];
        total: number;
    }>;
    getStatistics(): Promise<{
        total: number;
        statusCounts: {
            active: number;
            inactive: number;
        };
    }>;
}

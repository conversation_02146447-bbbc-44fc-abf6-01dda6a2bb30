"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.BaseOrderBookService = void 0;
const common_1 = require("@nestjs/common");
const event_emitter_1 = require("@nestjs/event-emitter");
const typeorm_1 = require("@nestjs/typeorm");
const class_transformer_1 = require("class-transformer");
const typeorm_2 = require("typeorm");
const ecom_products_entity_1 = require("../../ecom-products/entity/ecom-products.entity");
const user_entity_1 = require("../../users/entities/user.entity");
const order_book_detail_dto_1 = require("../dto/order-book-detail.dto");
const order_book_dto_1 = require("../dto/order-book.dto");
const order_book_entity_1 = require("../entities/order-book.entity");
let BaseOrderBookService = class BaseOrderBookService {
    orderBookRepository;
    eventEmitter;
    logger = new common_1.Logger('OrderBookService');
    validRelations = [
        'user',
        'details',
        'details.token',
        'details.product',
        'contract',
        'creator',
        'updater',
        'deleter',
    ];
    EVENT_ORDER_CREATED = 'order-book.created';
    EVENT_ORDER_UPDATED = 'order-book.updated';
    EVENT_ORDER_DELETED = 'order-book.deleted';
    EVENT_ORDER_RESTORED = 'order-book.restored';
    EVENT_ORDER_STATUS_TOGGLED = 'order-book.statusToggled';
    EVENT_ORDER_DUPLICATED = 'order-book.duplicated';
    EVENT_ORDER_CLEANUP = 'order-book.cleanup';
    constructor(orderBookRepository, eventEmitter) {
        this.orderBookRepository = orderBookRepository;
        this.eventEmitter = eventEmitter;
    }
    toDto(orderBook, relations = []) {
        const dto = (0, class_transformer_1.plainToInstance)(order_book_dto_1.OrderBookDto, orderBook, {
            excludeExtraneousValues: true,
            enableImplicitConversion: true,
            exposeDefaultValues: true,
            exposeUnsetFields: false,
        });
        if (orderBook.details && orderBook.details.length > 0) {
            dto.details = orderBook.details.map((detail) => {
                const detailDto = (0, class_transformer_1.plainToInstance)(order_book_detail_dto_1.OrderBookDetailDto, detail, {
                    excludeExtraneousValues: true,
                    enableImplicitConversion: true,
                    exposeDefaultValues: true,
                    exposeUnsetFields: false,
                });
                if (detail.product) {
                    detailDto.product = (0, class_transformer_1.plainToInstance)(ecom_products_entity_1.EcomProduct, detail.product, {
                        excludeExtraneousValues: true,
                        enableImplicitConversion: true,
                        exposeDefaultValues: true,
                        exposeUnsetFields: false,
                    });
                }
                return detailDto;
            });
        }
        else {
            dto.details = [];
        }
        if (dto.user) {
            dto.user = (0, class_transformer_1.plainToInstance)(user_entity_1.User, dto.user, {
                excludeExtraneousValues: true,
                enableImplicitConversion: true,
            });
        }
        if (dto.creator) {
            dto.creator = (0, class_transformer_1.plainToInstance)(user_entity_1.User, dto.creator, {
                excludeExtraneousValues: true,
                enableImplicitConversion: true,
            });
        }
        if (dto.updater) {
            dto.updater = (0, class_transformer_1.plainToInstance)(user_entity_1.User, dto.updater, {
                excludeExtraneousValues: true,
                enableImplicitConversion: true,
            });
        }
        return dto;
    }
    validateRelations(relations) {
        if (!relations || !relations.length)
            return [];
        return relations.filter((rel) => {
            const basePath = rel.split('.')[0];
            return this.validRelations.some((vr) => vr.startsWith(basePath));
        });
    }
    async findByIdOrFail(id, relations = [], withDeleted = false) {
        let relationsToLoad = [
            ...new Set([...relations, 'details', 'details.token']),
        ];
        relationsToLoad = this.validateRelations(relationsToLoad);
        const orderBook = await this.orderBookRepository.findOne({
            where: { id, ...(withDeleted ? {} : { isDeleted: false }) },
            relations: relationsToLoad,
        });
        if (!orderBook) {
            throw new common_1.NotFoundException(`Không tìm thấy lệnh với ID "${id}"${withDeleted ? '' : ' hoặc đã bị xóa'}`);
        }
        return orderBook;
    }
    buildWhereClause(filter) {
        const whereClause = { isDeleted: false };
        if (filter) {
            try {
                const [field, value] = filter.split(':');
                whereClause[field] = value;
            }
            catch (error) {
                this.logger.warn(`Định dạng lọc không hợp lệ: ${filter}`);
            }
        }
        return whereClause;
    }
};
exports.BaseOrderBookService = BaseOrderBookService;
exports.BaseOrderBookService = BaseOrderBookService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(order_book_entity_1.OrderBook)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        event_emitter_1.EventEmitter2])
], BaseOrderBookService);
//# sourceMappingURL=base.order-book.service.js.map
{"version": 3, "sources": [], "sections": [{"offset": {"line": 135, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/bloomgoo/gold-exchange-platform/frontend/services/websocket/websocket-config.ts"], "sourcesContent": ["// Cấu hình cho WebSocket\r\nimport { create } from 'zustand';\r\nimport { persist } from 'zustand/middleware';\r\n\r\ninterface WebSocketConfigState {\r\n  // Cờ để bật/tắt kết nối WebSocket\r\n  enabled: boolean;\r\n\r\n  // Hàm để bật WebSocket\r\n  enableWebSocket: () => void;\r\n\r\n  // Hàm để tắt WebSocket\r\n  disableWebSocket: () => void;\r\n\r\n  // Hàm để toggle trạng thái WebSocket\r\n  toggleWebSocket: () => void;\r\n}\r\n\r\n// Tạo store để lưu trạng thái cấu hình WebSocket\r\nexport const useWebSocketConfig = create<WebSocketConfigState>()(\r\n  persist(\r\n    (set) => ({\r\n      // Mặc định là bật\r\n      enabled: true,\r\n\r\n      // Hàm để bật WebSocket\r\n      enableWebSocket: () => set({ enabled: true }),\r\n\r\n      // Hàm để tắt WebSocket\r\n      disableWebSocket: () => set({ enabled: false }),\r\n\r\n      // Hàm để toggle trạng thái WebSocket\r\n      toggleWebSocket: () => set((state) => ({ enabled: !state.enabled })),\r\n    }),\r\n    {\r\n      name: 'websocket-config', // Tên của key trong localStorage\r\n    }\r\n  )\r\n);\r\n"], "names": [], "mappings": "AAAA,yBAAyB;;;;AACzB;AACA;;;AAiBO,MAAM,qBAAqB,CAAA,GAAA,oPAAA,CAAA,SAAM,AAAD,IACrC,CAAA,GAAA,yPAAA,CAAA,UAAO,AAAD,EACJ,CAAC,MAAQ,CAAC;QACR,kBAAkB;QAClB,SAAS;QAET,uBAAuB;QACvB,iBAAiB,IAAM,IAAI;gBAAE,SAAS;YAAK;QAE3C,uBAAuB;QACvB,kBAAkB,IAAM,IAAI;gBAAE,SAAS;YAAM;QAE7C,qCAAqC;QACrC,iBAAiB,IAAM,IAAI,CAAC,QAAU,CAAC;oBAAE,SAAS,CAAC,MAAM,OAAO;gBAAC,CAAC;IACpE,CAAC,GACD;IACE,MAAM;AACR", "debugId": null}}, {"offset": {"line": 167, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/bloomgoo/gold-exchange-platform/frontend/services/websocket/websocket.service.ts"], "sourcesContent": ["import { io, Socket } from 'socket.io-client';\r\nimport { create } from 'zustand';\r\nimport { useEffect } from 'react';\r\nimport { useWebSocketConfig } from './websocket-config';\r\n\r\n// <PERSON><PERSON><PERSON> nghĩa các namespace\r\nexport enum WebSocketNamespace {\r\n  FOREX = 'forex', // Thay thế TRADINGVIEW bằng FOREX\r\n  NOTIFICATIONS = 'notifications'\r\n}\r\n\r\n// Định nghĩa trạng thái của WebSocket\r\ninterface WebSocketState {\r\n  sockets: Record<WebSocketNamespace, Socket | null>;\r\n  isConnected: Record<WebSocketNamespace, boolean>;\r\n  lastError: Record<WebSocketNamespace, string | null>;\r\n\r\n  // Phương thức kết nối\r\n  connect: (namespace: WebSocketNamespace) => void;\r\n\r\n  // Phương thức ngắt kết nối\r\n  disconnect: (namespace: WebSocketNamespace) => void;\r\n\r\n  // Ph<PERSON>ơng thức gửi tin nhắn\r\n  emit: <T>(namespace: WebSocketNamespace, event: string, data?: any) => Promise<T>;\r\n\r\n  // <PERSON><PERSON><PERSON>ng thức đăng ký lắng nghe sự kiện\r\n  subscribe: <T>(namespace: WebSocketNamespace, event: string, callback: (data: T) => void) => () => void;\r\n\r\n  // Phương thức cập nhật trạng thái kết nối\r\n  setConnected: (namespace: WebSocketNamespace, isConnected: boolean) => void;\r\n\r\n  // Phương thức cập nhật lỗi\r\n  setError: (namespace: WebSocketNamespace, error: string | null) => void;\r\n}\r\n\r\n// Tạo store để quản lý trạng thái WebSocket\r\nexport const useWebSocket = create<WebSocketState>((set, get) => ({\r\n  sockets: {\r\n    [WebSocketNamespace.FOREX]: null,\r\n    [WebSocketNamespace.NOTIFICATIONS]: null,\r\n  },\r\n  isConnected: {\r\n    [WebSocketNamespace.FOREX]: false,\r\n    [WebSocketNamespace.NOTIFICATIONS]: false,\r\n  },\r\n  lastError: {\r\n    [WebSocketNamespace.FOREX]: null,\r\n    [WebSocketNamespace.NOTIFICATIONS]: null,\r\n  },\r\n\r\n  // Kết nối đến WebSocket server\r\n  connect: (namespace: WebSocketNamespace) => {\r\n    const { sockets } = get();\r\n\r\n    // Kiểm tra xem WebSocket có được bật không\r\n    const { enabled } = useWebSocketConfig.getState();\r\n    \r\n    if (!enabled) {\r\n      \r\n      return;\r\n    }\r\n\r\n    \r\n\r\n    // Nếu đã kết nối, không làm gì cả\r\n    if (sockets[namespace] && sockets[namespace]?.connected) return;\r\n\r\n    // Ngắt kết nối cũ nếu có\r\n    if (sockets[namespace]) {\r\n      sockets[namespace]?.disconnect();\r\n    }\r\n\r\n    // Lấy token từ localStorage\r\n    const token = localStorage.getItem('accessToken');\r\n    if (!token) {\r\n      set((state) => ({\r\n        lastError: {\r\n          ...state.lastError,\r\n          [namespace]: 'Không tìm thấy token xác thực'\r\n        }\r\n      }));\r\n      return;\r\n    }\r\n\r\n    // Tạo URL WebSocket từ NEXT_PUBLIC_API_URL\r\n    let wsUrl;\r\n\r\n    // Lấy base URL từ NEXT_PUBLIC_API_URL và loại bỏ /api/v1\r\n    const apiUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3168/api/v1';\r\n    wsUrl = apiUrl.replace('/api/v1', ''); // Loại bỏ /api/v1 để có base URL\r\n\r\n    \r\n\r\n    // Trong Socket.IO, namespace được thêm vào URL\r\n    \r\n\r\n    // Tạo kết nối mới - Chỉ sử dụng websocket, không sử dụng polling\r\n    const newSocket = io(`${wsUrl}/${namespace}`, {\r\n      auth: {\r\n        token: `Bearer ${token}` // Thêm 'Bearer ' prefix cho token\r\n      },\r\n      transports: ['websocket', 'polling'], // Cho phép cả websocket và polling\r\n      reconnection: true,\r\n      reconnectionAttempts: 5,\r\n      reconnectionDelay: 1000,\r\n      timeout: 20000\r\n    });\r\n\r\n    // Xử lý sự kiện kết nối\r\n    newSocket.on('connect', () => {\r\n      set((state) => ({\r\n        sockets: {\r\n          ...state.sockets,\r\n          [namespace]: newSocket\r\n        },\r\n        isConnected: {\r\n          ...state.isConnected,\r\n          [namespace]: true\r\n        },\r\n        lastError: {\r\n          ...state.lastError,\r\n          [namespace]: null\r\n        }\r\n      }));\r\n    });\r\n\r\n    // Xử lý sự kiện ngắt kết nối\r\n    newSocket.on('disconnect', () => {\r\n      set((state) => ({\r\n        isConnected: {\r\n          ...state.isConnected,\r\n          [namespace]: false\r\n        }\r\n      }));\r\n    });\r\n\r\n    // Xử lý lỗi kết nối\r\n    newSocket.on('connect_error', (error) => {\r\n      set((state) => ({\r\n        lastError: {\r\n          ...state.lastError,\r\n          [namespace]: `Lỗi kết nối: ${error.message}`\r\n        },\r\n        isConnected: {\r\n          ...state.isConnected,\r\n          [namespace]: false\r\n        }\r\n      }));\r\n    });\r\n\r\n    // Xử lý lỗi Socket.IO\r\n    newSocket.on('error', (error) => {\r\n      // Nếu lỗi là do xác thực, thử kết nối đến namespace public\r\n      if (error.code === 401 && namespace === WebSocketNamespace.FOREX) {\r\n        // Kết nối đến namespace public-forex không yêu cầu xác thực\r\n        const publicSocket = io(`${wsUrl}/public-forex`, {\r\n          transports: ['websocket', 'polling'],\r\n          reconnection: true,\r\n          reconnectionAttempts: 5,\r\n          reconnectionDelay: 1000,\r\n          timeout: 20000\r\n        });\r\n\r\n        publicSocket.on('connect', () => {\r\n          // Cập nhật state với public socket\r\n          set((state) => ({\r\n            sockets: {\r\n              ...state.sockets,\r\n              [namespace]: publicSocket\r\n            },\r\n            isConnected: {\r\n              ...state.isConnected,\r\n              [namespace]: true\r\n            }\r\n          }));\r\n        });\r\n      }\r\n    });\r\n\r\n    // Lưu socket vào state\r\n    set((state) => ({\r\n      sockets: {\r\n        ...state.sockets,\r\n        [namespace]: newSocket\r\n      }\r\n    }));\r\n  },\r\n\r\n  // Ngắt kết nối\r\n  disconnect: (namespace: WebSocketNamespace) => {\r\n    const { sockets } = get();\r\n    if (sockets[namespace]) {\r\n      sockets[namespace]?.disconnect();\r\n      set((state) => ({\r\n        sockets: {\r\n          ...state.sockets,\r\n          [namespace]: null\r\n        },\r\n        isConnected: {\r\n          ...state.isConnected,\r\n          [namespace]: false\r\n        }\r\n      }));\r\n    }\r\n  },\r\n\r\n  // Gửi tin nhắn (không chờ response)\r\n  emit: (namespace: WebSocketNamespace, event: string, data?: any): void => {\r\n    const { sockets, isConnected } = get();\r\n\r\n    if (!sockets[namespace] || !isConnected[namespace]) {\r\n      return;\r\n    }\r\n\r\n    sockets[namespace]?.emit(event, data);\r\n  },\r\n\r\n  // Gửi tin nhắn và chờ phản hồi (chỉ dùng khi cần thiết)\r\n  emitWithResponse: async <T>(namespace: WebSocketNamespace, event: string, data?: any): Promise<T> => {\r\n    const { sockets, isConnected } = get();\r\n\r\n    if (!sockets[namespace] || !isConnected[namespace]) {\r\n      throw new Error(`Chưa kết nối đến WebSocket ${namespace}`);\r\n    }\r\n\r\n    return new Promise<T>((resolve, reject) => {\r\n      const timeout = setTimeout(() => {\r\n        reject(new Error('Timeout waiting for response'));\r\n      }, 10000); // Tăng timeout lên 10 giây\r\n\r\n      sockets[namespace]?.emit(event, data, (response: T) => {\r\n        clearTimeout(timeout);\r\n        resolve(response);\r\n      });\r\n    });\r\n  },\r\n\r\n  // Đăng ký lắng nghe sự kiện\r\n  subscribe: <T>(namespace: WebSocketNamespace, event: string, callback: (data: T) => void) => {\r\n    const { sockets } = get();\r\n\r\n    if (!sockets[namespace]) {\r\n      return () => {};\r\n    }\r\n\r\n    sockets[namespace]?.on(event, callback);\r\n\r\n    // Trả về hàm để hủy đăng ký\r\n    return () => {\r\n      sockets[namespace]?.off(event, callback);\r\n    };\r\n  },\r\n\r\n  // Cập nhật trạng thái kết nối\r\n  setConnected: (namespace: WebSocketNamespace, isConnected: boolean) => {\r\n    set((state) => ({\r\n      isConnected: {\r\n        ...state.isConnected,\r\n        [namespace]: isConnected\r\n      }\r\n    }));\r\n  },\r\n\r\n  // Cập nhật lỗi\r\n  setError: (namespace: WebSocketNamespace, error: string | null) => {\r\n    set((state) => ({\r\n      lastError: {\r\n        ...state.lastError,\r\n        [namespace]: error\r\n      }\r\n    }));\r\n  }\r\n}));\r\n\r\n// Hook để tự động kết nối khi đăng nhập\r\nexport const useAutoConnectWebSocket = () => {\r\n  const { connect, isConnected } = useWebSocket();\r\n  const { enabled } = useWebSocketConfig();\r\n\r\n  // Kết nối khi component mount\r\n  useEffect(() => {\r\n    const token = localStorage.getItem('accessToken');\r\n\r\n    if (token) {\r\n      // Kích hoạt WebSocket nếu chưa được kích hoạt\r\n      if (!enabled) {\r\n        // Sử dụng getState() để lấy state và actions từ store\r\n        const webSocketConfig = useWebSocketConfig.getState();\r\n        webSocketConfig.enableWebSocket();\r\n      }\r\n\r\n      // Kết nối đến tất cả các namespace\r\n      Object.values(WebSocketNamespace).forEach(namespace => {\r\n        if (!isConnected[namespace]) {\r\n          connect(namespace);\r\n        }\r\n      });\r\n    }\r\n\r\n    // Cleanup khi unmount\r\n    return () => {\r\n      // Không ngắt kết nối khi unmount component để giữ kết nối liên tục\r\n    };\r\n  }, [connect, isConnected, enabled]);\r\n\r\n  return useWebSocket();\r\n};\r\n"], "names": [], "mappings": ";;;;;AAAA;AAAA;AACA;AACA;AACA;;;;;AAGO,IAAA,AAAK,4CAAA;;;WAAA;;AA+BL,MAAM,eAAe,CAAA,GAAA,oPAAA,CAAA,SAAM,AAAD,EAAkB,CAAC,KAAK,MAAQ,CAAC;QAChE,SAAS;YACP,SAA0B,EAAE;YAC5B,iBAAkC,EAAE;QACtC;QACA,aAAa;YACX,SAA0B,EAAE;YAC5B,iBAAkC,EAAE;QACtC;QACA,WAAW;YACT,SAA0B,EAAE;YAC5B,iBAAkC,EAAE;QACtC;QAEA,+BAA+B;QAC/B,SAAS,CAAC;YACR,MAAM,EAAE,OAAO,EAAE,GAAG;YAEpB,2CAA2C;YAC3C,MAAM,EAAE,OAAO,EAAE,GAAG,4IAAA,CAAA,qBAAkB,CAAC,QAAQ;YAE/C,IAAI,CAAC,SAAS;gBAEZ;YACF;YAIA,kCAAkC;YAClC,IAAI,OAAO,CAAC,UAAU,IAAI,OAAO,CAAC,UAAU,EAAE,WAAW;YAEzD,yBAAyB;YACzB,IAAI,OAAO,CAAC,UAAU,EAAE;gBACtB,OAAO,CAAC,UAAU,EAAE;YACtB;YAEA,4BAA4B;YAC5B,MAAM,QAAQ,aAAa,OAAO,CAAC;YACnC,IAAI,CAAC,OAAO;gBACV,IAAI,CAAC,QAAU,CAAC;wBACd,WAAW;4BACT,GAAG,MAAM,SAAS;4BAClB,CAAC,UAAU,EAAE;wBACf;oBACF,CAAC;gBACD;YACF;YAEA,2CAA2C;YAC3C,IAAI;YAEJ,yDAAyD;YACzD,MAAM,SAAS,oEAAmC;YAClD,QAAQ,OAAO,OAAO,CAAC,WAAW,KAAK,iCAAiC;YAIxE,+CAA+C;YAG/C,iEAAiE;YACjE,MAAM,YAAY,CAAA,GAAA,2PAAA,CAAA,KAAE,AAAD,EAAE,GAAG,MAAM,CAAC,EAAE,WAAW,EAAE;gBAC5C,MAAM;oBACJ,OAAO,CAAC,OAAO,EAAE,OAAO,CAAC,kCAAkC;gBAC7D;gBACA,YAAY;oBAAC;oBAAa;iBAAU;gBACpC,cAAc;gBACd,sBAAsB;gBACtB,mBAAmB;gBACnB,SAAS;YACX;YAEA,wBAAwB;YACxB,UAAU,EAAE,CAAC,WAAW;gBACtB,IAAI,CAAC,QAAU,CAAC;wBACd,SAAS;4BACP,GAAG,MAAM,OAAO;4BAChB,CAAC,UAAU,EAAE;wBACf;wBACA,aAAa;4BACX,GAAG,MAAM,WAAW;4BACpB,CAAC,UAAU,EAAE;wBACf;wBACA,WAAW;4BACT,GAAG,MAAM,SAAS;4BAClB,CAAC,UAAU,EAAE;wBACf;oBACF,CAAC;YACH;YAEA,6BAA6B;YAC7B,UAAU,EAAE,CAAC,cAAc;gBACzB,IAAI,CAAC,QAAU,CAAC;wBACd,aAAa;4BACX,GAAG,MAAM,WAAW;4BACpB,CAAC,UAAU,EAAE;wBACf;oBACF,CAAC;YACH;YAEA,oBAAoB;YACpB,UAAU,EAAE,CAAC,iBAAiB,CAAC;gBAC7B,IAAI,CAAC,QAAU,CAAC;wBACd,WAAW;4BACT,GAAG,MAAM,SAAS;4BAClB,CAAC,UAAU,EAAE,CAAC,aAAa,EAAE,MAAM,OAAO,EAAE;wBAC9C;wBACA,aAAa;4BACX,GAAG,MAAM,WAAW;4BACpB,CAAC,UAAU,EAAE;wBACf;oBACF,CAAC;YACH;YAEA,sBAAsB;YACtB,UAAU,EAAE,CAAC,SAAS,CAAC;gBACrB,2DAA2D;gBAC3D,IAAI,MAAM,IAAI,KAAK,OAAO,uBAAwC;oBAChE,4DAA4D;oBAC5D,MAAM,eAAe,CAAA,GAAA,2PAAA,CAAA,KAAE,AAAD,EAAE,GAAG,MAAM,aAAa,CAAC,EAAE;wBAC/C,YAAY;4BAAC;4BAAa;yBAAU;wBACpC,cAAc;wBACd,sBAAsB;wBACtB,mBAAmB;wBACnB,SAAS;oBACX;oBAEA,aAAa,EAAE,CAAC,WAAW;wBACzB,mCAAmC;wBACnC,IAAI,CAAC,QAAU,CAAC;gCACd,SAAS;oCACP,GAAG,MAAM,OAAO;oCAChB,CAAC,UAAU,EAAE;gCACf;gCACA,aAAa;oCACX,GAAG,MAAM,WAAW;oCACpB,CAAC,UAAU,EAAE;gCACf;4BACF,CAAC;oBACH;gBACF;YACF;YAEA,uBAAuB;YACvB,IAAI,CAAC,QAAU,CAAC;oBACd,SAAS;wBACP,GAAG,MAAM,OAAO;wBAChB,CAAC,UAAU,EAAE;oBACf;gBACF,CAAC;QACH;QAEA,eAAe;QACf,YAAY,CAAC;YACX,MAAM,EAAE,OAAO,EAAE,GAAG;YACpB,IAAI,OAAO,CAAC,UAAU,EAAE;gBACtB,OAAO,CAAC,UAAU,EAAE;gBACpB,IAAI,CAAC,QAAU,CAAC;wBACd,SAAS;4BACP,GAAG,MAAM,OAAO;4BAChB,CAAC,UAAU,EAAE;wBACf;wBACA,aAAa;4BACX,GAAG,MAAM,WAAW;4BACpB,CAAC,UAAU,EAAE;wBACf;oBACF,CAAC;YACH;QACF;QAEA,oCAAoC;QACpC,MAAM,CAAC,WAA+B,OAAe;YACnD,MAAM,EAAE,OAAO,EAAE,WAAW,EAAE,GAAG;YAEjC,IAAI,CAAC,OAAO,CAAC,UAAU,IAAI,CAAC,WAAW,CAAC,UAAU,EAAE;gBAClD;YACF;YAEA,OAAO,CAAC,UAAU,EAAE,KAAK,OAAO;QAClC;QAEA,wDAAwD;QACxD,kBAAkB,OAAU,WAA+B,OAAe;YACxE,MAAM,EAAE,OAAO,EAAE,WAAW,EAAE,GAAG;YAEjC,IAAI,CAAC,OAAO,CAAC,UAAU,IAAI,CAAC,WAAW,CAAC,UAAU,EAAE;gBAClD,MAAM,IAAI,MAAM,CAAC,2BAA2B,EAAE,WAAW;YAC3D;YAEA,OAAO,IAAI,QAAW,CAAC,SAAS;gBAC9B,MAAM,UAAU,WAAW;oBACzB,OAAO,IAAI,MAAM;gBACnB,GAAG,QAAQ,2BAA2B;gBAEtC,OAAO,CAAC,UAAU,EAAE,KAAK,OAAO,MAAM,CAAC;oBACrC,aAAa;oBACb,QAAQ;gBACV;YACF;QACF;QAEA,4BAA4B;QAC5B,WAAW,CAAI,WAA+B,OAAe;YAC3D,MAAM,EAAE,OAAO,EAAE,GAAG;YAEpB,IAAI,CAAC,OAAO,CAAC,UAAU,EAAE;gBACvB,OAAO,KAAO;YAChB;YAEA,OAAO,CAAC,UAAU,EAAE,GAAG,OAAO;YAE9B,4BAA4B;YAC5B,OAAO;gBACL,OAAO,CAAC,UAAU,EAAE,IAAI,OAAO;YACjC;QACF;QAEA,8BAA8B;QAC9B,cAAc,CAAC,WAA+B;YAC5C,IAAI,CAAC,QAAU,CAAC;oBACd,aAAa;wBACX,GAAG,MAAM,WAAW;wBACpB,CAAC,UAAU,EAAE;oBACf;gBACF,CAAC;QACH;QAEA,eAAe;QACf,UAAU,CAAC,WAA+B;YACxC,IAAI,CAAC,QAAU,CAAC;oBACd,WAAW;wBACT,GAAG,MAAM,SAAS;wBAClB,CAAC,UAAU,EAAE;oBACf;gBACF,CAAC;QACH;IACF,CAAC;AAGM,MAAM,0BAA0B;IACrC,MAAM,EAAE,OAAO,EAAE,WAAW,EAAE,GAAG;IACjC,MAAM,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,4IAAA,CAAA,qBAAkB,AAAD;IAErC,8BAA8B;IAC9B,CAAA,GAAA,8SAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,QAAQ,aAAa,OAAO,CAAC;QAEnC,IAAI,OAAO;YACT,8CAA8C;YAC9C,IAAI,CAAC,SAAS;gBACZ,sDAAsD;gBACtD,MAAM,kBAAkB,4IAAA,CAAA,qBAAkB,CAAC,QAAQ;gBACnD,gBAAgB,eAAe;YACjC;YAEA,mCAAmC;YACnC,OAAO,MAAM,CAAC,oBAAoB,OAAO,CAAC,CAAA;gBACxC,IAAI,CAAC,WAAW,CAAC,UAAU,EAAE;oBAC3B,QAAQ;gBACV;YACF;QACF;QAEA,sBAAsB;QACtB,OAAO;QACL,mEAAmE;QACrE;IACF,GAAG;QAAC;QAAS;QAAa;KAAQ;IAElC,OAAO;AACT", "debugId": null}}, {"offset": {"line": 430, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/bloomgoo/gold-exchange-platform/frontend/services/websocket/polygon-forex-socket.service.ts"], "sourcesContent": ["import { create } from 'zustand';\r\nimport { useEffect } from 'react';\r\nimport { useWebSocket, WebSocketNamespace } from './websocket.service';\r\n\r\n// Định nghĩa các kiểu dữ liệu\r\nexport enum ForexEvent {\r\n  FOREX_QUOTE = 'forex-quote',\r\n  CONNECTION_STATUS = 'connection-status',\r\n  SUBSCRIPTION_STATUS = 'subscription-status',\r\n  ERROR = 'error',\r\n  SUBSCRIBE = 'subscribe-forex',\r\n}\r\n\r\nexport interface ForexQuote {\r\n  pair: string;        // Cặp tiền tệ (AXGUSD)\r\n  bidPrice: number;    // Giá chào mua (USD)\r\n  askPrice: number;    // Gi<PERSON> chào bán (USD)\r\n  timestamp: Date;     // Thời gian của quote\r\n  averagePrice: number; // Giá trung bình (USD)\r\n  spread: number;      // Chênh lệch giữa giá mua và bán (USD)\r\n  spreadPercentage: number; // Phần trăm chênh lệch (%)\r\n  buyPriceVND: number; // <PERSON><PERSON><PERSON> mua quy đổi (VND)\r\n  sellPriceVND: number; // <PERSON><PERSON><PERSON> bán quy đổi (VND)\r\n}\r\n\r\nexport interface ConnectionStatus {\r\n  connected: boolean;\r\n  usingMockData: boolean;\r\n  timestamp: Date;\r\n}\r\n\r\nexport interface SubscriptionStatus {\r\n  status: 'success' | 'error';\r\n  message: string;\r\n  timestamp: Date;\r\n}\r\n\r\n// Định nghĩa trạng thái của Polygon Forex\r\ninterface PolygonForexState {\r\n  currentQuote: ForexQuote | null;\r\n  connectionStatus: ConnectionStatus | null;\r\n  isSubscribed: boolean;\r\n  lastError: string | null;\r\n\r\n  // Phương thức đăng ký nhận dữ liệu\r\n  subscribe: () => void;\r\n\r\n  // Phương thức cập nhật quote hiện tại\r\n  updateQuote: (quote: ForexQuote) => void;\r\n\r\n  // Phương thức cập nhật trạng thái kết nối\r\n  updateConnectionStatus: (status: ConnectionStatus) => void;\r\n\r\n  // Phương thức cập nhật trạng thái đăng ký\r\n  setSubscribed: (isSubscribed: boolean) => void;\r\n\r\n  // Phương thức cập nhật lỗi\r\n  setError: (error: string | null) => void;\r\n}\r\n\r\n// Tạo store để quản lý trạng thái Polygon Forex\r\nexport const usePolygonForexSocket = create<PolygonForexState>((set) => ({\r\n  currentQuote: null,\r\n  connectionStatus: null,\r\n  isSubscribed: false,\r\n  lastError: null,\r\n\r\n  // Đăng ký nhận dữ liệu\r\n  subscribe: () => {\r\n    const { emit, isConnected } = useWebSocket.getState();\r\n\r\n    if (!isConnected[WebSocketNamespace.FOREX]) {\r\n      set({ lastError: 'Chưa kết nối đến WebSocket Forex' });\r\n      return;\r\n    }\r\n\r\n    try {\r\n      emit(WebSocketNamespace.FOREX, ForexEvent.SUBSCRIBE);\r\n      set({ isSubscribed: true, lastError: null });\r\n    } catch (error) {\r\n      const errorMessage = error instanceof Error ? error.message : 'Lỗi không xác định';\r\n      set({ lastError: errorMessage });\r\n    }\r\n  },\r\n\r\n  // Cập nhật quote hiện tại\r\n  updateQuote: (quote: ForexQuote) => {\r\n    set({ currentQuote: quote });\r\n  },\r\n\r\n  // Cập nhật trạng thái kết nối\r\n  updateConnectionStatus: (status: ConnectionStatus) => {\r\n    set({ connectionStatus: status });\r\n  },\r\n\r\n  // Cập nhật trạng thái đăng ký\r\n  setSubscribed: (isSubscribed: boolean) => {\r\n    set({ isSubscribed });\r\n  },\r\n\r\n  // Cập nhật lỗi\r\n  setError: (error: string | null) => {\r\n    set({ lastError: error });\r\n  }\r\n}));\r\n\r\n// Hook để tự động kết nối và đăng ký khi đăng nhập\r\nexport const usePolygonForex = () => {\r\n  const {\r\n    currentQuote,\r\n    connectionStatus,\r\n    isSubscribed,\r\n    lastError,\r\n    subscribe,\r\n    updateQuote,\r\n    updateConnectionStatus,\r\n    setError\r\n  } = usePolygonForexSocket();\r\n\r\n  const { connect, subscribe: subscribeToEvent, isConnected } = useWebSocket();\r\n\r\n  // Kết nối đến namespace Forex khi component mount\r\n  useEffect(() => {\r\n    const token = localStorage.getItem('accessToken');\r\n\r\n    if (token && !isConnected[WebSocketNamespace.FOREX]) {\r\n      connect(WebSocketNamespace.FOREX);\r\n    }\r\n  }, [connect, isConnected]);\r\n\r\n  // Lắng nghe cập nhật quote\r\n  useEffect(() => {\r\n    \r\n\r\n    if (!isConnected[WebSocketNamespace.FOREX]) {\r\n      \r\n      return;\r\n    }\r\n\r\n    \r\n\r\n    // Đăng ký lắng nghe sự kiện cập nhật quote\r\n    const unsubscribeFromQuote = subscribeToEvent<ForexQuote>(\r\n      WebSocketNamespace.FOREX,\r\n      ForexEvent.FOREX_QUOTE,\r\n      (quote) => {\r\n        updateQuote(quote);\r\n      }\r\n    );\r\n\r\n    // Đăng ký lắng nghe sự kiện trạng thái kết nối\r\n    const unsubscribeFromStatus = subscribeToEvent<ConnectionStatus>(\r\n      WebSocketNamespace.FOREX,\r\n      ForexEvent.CONNECTION_STATUS,\r\n      (status) => {\r\n        updateConnectionStatus(status);\r\n      }\r\n    );\r\n\r\n    // Đăng ký lắng nghe sự kiện trạng thái đăng ký\r\n    const unsubscribeFromSubscription = subscribeToEvent<SubscriptionStatus>(\r\n      WebSocketNamespace.FOREX,\r\n      ForexEvent.SUBSCRIPTION_STATUS,\r\n      (status) => {\r\n        if (status.status === 'success') {\r\n          setError(null);\r\n        } else {\r\n          setError(status.message);\r\n        }\r\n      }\r\n    );\r\n\r\n    // Hủy đăng ký khi unmount\r\n    return () => {\r\n      unsubscribeFromQuote();\r\n      unsubscribeFromStatus();\r\n      unsubscribeFromSubscription();\r\n    };\r\n  }, [subscribeToEvent, isConnected, updateQuote, updateConnectionStatus, setError]);\r\n\r\n  // Đăng ký nhận dữ liệu khi đã kết nối nhưng chưa đăng ký\r\n  useEffect(() => {\r\n    \r\n    \r\n\r\n    if (isConnected[WebSocketNamespace.FOREX] && !isSubscribed) {\r\n      \r\n      subscribe();\r\n    }\r\n  }, [isConnected, isSubscribed, subscribe]);\r\n\r\n  return {\r\n    currentQuote,\r\n    connectionStatus,\r\n    isSubscribed,\r\n    lastError,\r\n    isConnected: isConnected[WebSocketNamespace.FOREX],\r\n    isMockData: connectionStatus?.usingMockData || false,\r\n    subscribe\r\n  };\r\n};\r\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;;;;AAGO,IAAA,AAAK,oCAAA;;;;;;WAAA;;AAwDL,MAAM,wBAAwB,CAAA,GAAA,oPAAA,CAAA,SAAM,AAAD,EAAqB,CAAC,MAAQ,CAAC;QACvE,cAAc;QACd,kBAAkB;QAClB,cAAc;QACd,WAAW;QAEX,uBAAuB;QACvB,WAAW;YACT,MAAM,EAAE,IAAI,EAAE,WAAW,EAAE,GAAG,6IAAA,CAAA,eAAY,CAAC,QAAQ;YAEnD,IAAI,CAAC,WAAW,CAAC,6IAAA,CAAA,qBAAkB,CAAC,KAAK,CAAC,EAAE;gBAC1C,IAAI;oBAAE,WAAW;gBAAmC;gBACpD;YACF;YAEA,IAAI;gBACF,KAAK,6IAAA,CAAA,qBAAkB,CAAC,KAAK;gBAC7B,IAAI;oBAAE,cAAc;oBAAM,WAAW;gBAAK;YAC5C,EAAE,OAAO,OAAO;gBACd,MAAM,eAAe,iBAAiB,QAAQ,MAAM,OAAO,GAAG;gBAC9D,IAAI;oBAAE,WAAW;gBAAa;YAChC;QACF;QAEA,0BAA0B;QAC1B,aAAa,CAAC;YACZ,IAAI;gBAAE,cAAc;YAAM;QAC5B;QAEA,8BAA8B;QAC9B,wBAAwB,CAAC;YACvB,IAAI;gBAAE,kBAAkB;YAAO;QACjC;QAEA,8BAA8B;QAC9B,eAAe,CAAC;YACd,IAAI;gBAAE;YAAa;QACrB;QAEA,eAAe;QACf,UAAU,CAAC;YACT,IAAI;gBAAE,WAAW;YAAM;QACzB;IACF,CAAC;AAGM,MAAM,kBAAkB;IAC7B,MAAM,EACJ,YAAY,EACZ,gBAAgB,EAChB,YAAY,EACZ,SAAS,EACT,SAAS,EACT,WAAW,EACX,sBAAsB,EACtB,QAAQ,EACT,GAAG;IAEJ,MAAM,EAAE,OAAO,EAAE,WAAW,gBAAgB,EAAE,WAAW,EAAE,GAAG,CAAA,GAAA,6IAAA,CAAA,eAAY,AAAD;IAEzE,kDAAkD;IAClD,CAAA,GAAA,8SAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,QAAQ,aAAa,OAAO,CAAC;QAEnC,IAAI,SAAS,CAAC,WAAW,CAAC,6IAAA,CAAA,qBAAkB,CAAC,KAAK,CAAC,EAAE;YACnD,QAAQ,6IAAA,CAAA,qBAAkB,CAAC,KAAK;QAClC;IACF,GAAG;QAAC;QAAS;KAAY;IAEzB,2BAA2B;IAC3B,CAAA,GAAA,8SAAA,CAAA,YAAS,AAAD,EAAE;QAGR,IAAI,CAAC,WAAW,CAAC,6IAAA,CAAA,qBAAkB,CAAC,KAAK,CAAC,EAAE;YAE1C;QACF;QAIA,2CAA2C;QAC3C,MAAM,uBAAuB,iBAC3B,6IAAA,CAAA,qBAAkB,CAAC,KAAK,iBAExB,CAAC;YACC,YAAY;QACd;QAGF,+CAA+C;QAC/C,MAAM,wBAAwB,iBAC5B,6IAAA,CAAA,qBAAkB,CAAC,KAAK,uBAExB,CAAC;YACC,uBAAuB;QACzB;QAGF,+CAA+C;QAC/C,MAAM,8BAA8B,iBAClC,6IAAA,CAAA,qBAAkB,CAAC,KAAK,yBAExB,CAAC;YACC,IAAI,OAAO,MAAM,KAAK,WAAW;gBAC/B,SAAS;YACX,OAAO;gBACL,SAAS,OAAO,OAAO;YACzB;QACF;QAGF,0BAA0B;QAC1B,OAAO;YACL;YACA;YACA;QACF;IACF,GAAG;QAAC;QAAkB;QAAa;QAAa;QAAwB;KAAS;IAEjF,yDAAyD;IACzD,CAAA,GAAA,8SAAA,CAAA,YAAS,AAAD,EAAE;QAIR,IAAI,WAAW,CAAC,6IAAA,CAAA,qBAAkB,CAAC,KAAK,CAAC,IAAI,CAAC,cAAc;YAE1D;QACF;IACF,GAAG;QAAC;QAAa;QAAc;KAAU;IAEzC,OAAO;QACL;QACA;QACA;QACA;QACA,aAAa,WAAW,CAAC,6IAAA,CAAA,qBAAkB,CAAC,KAAK,CAAC;QAClD,YAAY,kBAAkB,iBAAiB;QAC/C;IACF;AACF", "debugId": null}}, {"offset": {"line": 574, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/bloomgoo/gold-exchange-platform/frontend/services/websocket/notification-socket.service.ts"], "sourcesContent": ["import { create } from 'zustand';\r\nimport { useEffect } from 'react';\r\nimport { useWebSocket, WebSocketNamespace } from './websocket.service';\r\nimport { useWebSocketConfig } from './websocket-config';\r\n\r\n// Đ<PERSON>nh nghĩa các loại thông báo\r\nexport enum NotificationType {\r\n  SYSTEM = 'system',\r\n  TRANSACTION = 'transaction',\r\n  USER = 'user',\r\n  CONTRACT = 'contract',\r\n  PAYMENT = 'payment'\r\n}\r\n\r\n// Định nghĩa cấu trúc thông báo\r\nexport interface Notification {\r\n  id: string;\r\n  type: NotificationType;\r\n  title: string;\r\n  message: string;\r\n  isRead: boolean;\r\n  createdAt: string;\r\n  data?: any;\r\n}\r\n\r\n// Định nghĩa các sự kiện WebSocket cho thông báo\r\nexport enum NotificationEvent {\r\n  NEW_NOTIFICATION = 'new_notification',\r\n  READ_NOTIFICATION = 'read_notification',\r\n  DELETE_NOTIFICATION = 'delete_notification',\r\n  GET_NOTIFICATIONS = 'get_notifications'\r\n}\r\n\r\n// Đ<PERSON><PERSON> nghĩa trạng thái của thông báo\r\ninterface NotificationState {\r\n  notifications: Notification[];\r\n  unreadCount: number;\r\n  isLoading: boolean;\r\n  lastError: string | null;\r\n\r\n  // Phương thức lấy danh sách thông báo\r\n  fetchNotifications: () => Promise<void>;\r\n\r\n  // Phương thức đánh dấu thông báo đã đọc\r\n  markAsRead: (id: string) => Promise<void>;\r\n\r\n  // Phương thức đánh dấu tất cả thông báo đã đọc\r\n  markAllAsRead: () => Promise<void>;\r\n\r\n  // Phương thức xóa thông báo\r\n  deleteNotification: (id: string) => Promise<void>;\r\n\r\n  // Phương thức thêm thông báo mới\r\n  addNotification: (notification: Notification) => void;\r\n\r\n  // Phương thức cập nhật số lượng thông báo chưa đọc\r\n  updateUnreadCount: () => void;\r\n}\r\n\r\n// Tạo store để quản lý trạng thái thông báo\r\nexport const useNotificationSocket = create<NotificationState>((set, get) => ({\r\n  notifications: [],\r\n  unreadCount: 0,\r\n  isLoading: false,\r\n  lastError: null,\r\n\r\n  // Lấy danh sách thông báo\r\n  fetchNotifications: async () => {\r\n    const { emit } = useWebSocket.getState();\r\n    set({ isLoading: true });\r\n\r\n    try {\r\n      const response = await emit<{ notifications: Notification[] }>(\r\n        WebSocketNamespace.NOTIFICATIONS,\r\n        NotificationEvent.GET_NOTIFICATIONS\r\n      );\r\n\r\n      set({\r\n        notifications: response.notifications,\r\n        isLoading: false,\r\n        lastError: null\r\n      });\r\n\r\n      // Cập nhật số lượng thông báo chưa đọc\r\n      get().updateUnreadCount();\r\n    } catch (error) {\r\n      set({\r\n        isLoading: false,\r\n        lastError: error instanceof Error ? error.message : 'Lỗi không xác định'\r\n      });\r\n    }\r\n  },\r\n\r\n  // Đánh dấu thông báo đã đọc\r\n  markAsRead: async (id: string) => {\r\n    const { emit } = useWebSocket.getState();\r\n\r\n    try {\r\n      await emit(\r\n        WebSocketNamespace.NOTIFICATIONS,\r\n        NotificationEvent.READ_NOTIFICATION,\r\n        { id }\r\n      );\r\n\r\n      set((state) => ({\r\n        notifications: state.notifications.map(notification =>\r\n          notification.id === id\r\n            ? { ...notification, isRead: true }\r\n            : notification\r\n        ),\r\n        lastError: null\r\n      }));\r\n\r\n      // Cập nhật số lượng thông báo chưa đọc\r\n      get().updateUnreadCount();\r\n    } catch (error) {\r\n      set({\r\n        lastError: error instanceof Error ? error.message : 'Lỗi không xác định'\r\n      });\r\n    }\r\n  },\r\n\r\n  // Đánh dấu tất cả thông báo đã đọc\r\n  markAllAsRead: async () => {\r\n    const { emit } = useWebSocket.getState();\r\n\r\n    try {\r\n      await emit(\r\n        WebSocketNamespace.NOTIFICATIONS,\r\n        NotificationEvent.READ_NOTIFICATION,\r\n        { all: true }\r\n      );\r\n\r\n      set((state) => ({\r\n        notifications: state.notifications.map(notification => ({\r\n          ...notification,\r\n          isRead: true\r\n        })),\r\n        unreadCount: 0,\r\n        lastError: null\r\n      }));\r\n    } catch (error) {\r\n      set({\r\n        lastError: error instanceof Error ? error.message : 'Lỗi không xác định'\r\n      });\r\n    }\r\n  },\r\n\r\n  // Xóa thông báo\r\n  deleteNotification: async (id: string) => {\r\n    const { emit } = useWebSocket.getState();\r\n\r\n    try {\r\n      await emit(\r\n        WebSocketNamespace.NOTIFICATIONS,\r\n        NotificationEvent.DELETE_NOTIFICATION,\r\n        { id }\r\n      );\r\n\r\n      set((state) => ({\r\n        notifications: state.notifications.filter(notification => notification.id !== id),\r\n        lastError: null\r\n      }));\r\n\r\n      // Cập nhật số lượng thông báo chưa đọc\r\n      get().updateUnreadCount();\r\n    } catch (error) {\r\n      set({\r\n        lastError: error instanceof Error ? error.message : 'Lỗi không xác định'\r\n      });\r\n    }\r\n  },\r\n\r\n  // Thêm thông báo mới\r\n  addNotification: (notification: Notification) => {\r\n    set((state) => ({\r\n      notifications: [notification, ...state.notifications],\r\n      lastError: null\r\n    }));\r\n\r\n    // Cập nhật số lượng thông báo chưa đọc\r\n    get().updateUnreadCount();\r\n  },\r\n\r\n  // Cập nhật số lượng thông báo chưa đọc\r\n  updateUnreadCount: () => {\r\n    const { notifications } = get();\r\n    const unreadCount = notifications.filter(notification => !notification.isRead).length;\r\n    set({ unreadCount });\r\n  }\r\n}));\r\n\r\n// Hook để tự động kết nối và lắng nghe thông báo mới\r\nexport const useNotifications = () => {\r\n  const {\r\n    notifications,\r\n    unreadCount,\r\n    isLoading,\r\n    lastError,\r\n    fetchNotifications,\r\n    markAsRead,\r\n    markAllAsRead,\r\n    deleteNotification,\r\n    addNotification\r\n  } = useNotificationSocket();\r\n\r\n  const { connect, subscribe, isConnected } = useWebSocket();\r\n  const { enabled } = useWebSocketConfig();\r\n\r\n  // Kết nối đến namespace thông báo khi component mount\r\n  useEffect(() => {\r\n    const token = localStorage.getItem('accessToken');\r\n\r\n    if (token && enabled && !isConnected[WebSocketNamespace.NOTIFICATIONS]) {\r\n      connect(WebSocketNamespace.NOTIFICATIONS);\r\n    }\r\n  }, [connect, isConnected, enabled]);\r\n\r\n  // Lắng nghe thông báo mới\r\n  useEffect(() => {\r\n    if (!enabled || !isConnected[WebSocketNamespace.NOTIFICATIONS]) return;\r\n\r\n    // Đăng ký lắng nghe sự kiện thông báo mới\r\n    const unsubscribe = subscribe<Notification>(\r\n      WebSocketNamespace.NOTIFICATIONS,\r\n      NotificationEvent.NEW_NOTIFICATION,\r\n      (notification) => {\r\n        addNotification(notification);\r\n      }\r\n    );\r\n\r\n    // Lấy danh sách thông báo\r\n    fetchNotifications();\r\n\r\n    // Hủy đăng ký khi unmount\r\n    return unsubscribe;\r\n  }, [subscribe, isConnected, fetchNotifications, addNotification, enabled]);\r\n\r\n  return {\r\n    notifications,\r\n    unreadCount,\r\n    isLoading,\r\n    lastError,\r\n    markAsRead,\r\n    markAllAsRead,\r\n    deleteNotification\r\n  };\r\n};\r\n"], "names": [], "mappings": ";;;;;;AAAA;AACA;AACA;AACA;;;;;AAGO,IAAA,AAAK,0CAAA;;;;;;WAAA;;AAoBL,IAAA,AAAK,2CAAA;;;;;WAAA;;AAkCL,MAAM,wBAAwB,CAAA,GAAA,oPAAA,CAAA,SAAM,AAAD,EAAqB,CAAC,KAAK,MAAQ,CAAC;QAC5E,eAAe,EAAE;QACjB,aAAa;QACb,WAAW;QACX,WAAW;QAEX,0BAA0B;QAC1B,oBAAoB;YAClB,MAAM,EAAE,IAAI,EAAE,GAAG,6IAAA,CAAA,eAAY,CAAC,QAAQ;YACtC,IAAI;gBAAE,WAAW;YAAK;YAEtB,IAAI;gBACF,MAAM,WAAW,MAAM,KACrB,6IAAA,CAAA,qBAAkB,CAAC,aAAa;gBAIlC,IAAI;oBACF,eAAe,SAAS,aAAa;oBACrC,WAAW;oBACX,WAAW;gBACb;gBAEA,uCAAuC;gBACvC,MAAM,iBAAiB;YACzB,EAAE,OAAO,OAAO;gBACd,IAAI;oBACF,WAAW;oBACX,WAAW,iBAAiB,QAAQ,MAAM,OAAO,GAAG;gBACtD;YACF;QACF;QAEA,4BAA4B;QAC5B,YAAY,OAAO;YACjB,MAAM,EAAE,IAAI,EAAE,GAAG,6IAAA,CAAA,eAAY,CAAC,QAAQ;YAEtC,IAAI;gBACF,MAAM,KACJ,6IAAA,CAAA,qBAAkB,CAAC,aAAa,uBAEhC;oBAAE;gBAAG;gBAGP,IAAI,CAAC,QAAU,CAAC;wBACd,eAAe,MAAM,aAAa,CAAC,GAAG,CAAC,CAAA,eACrC,aAAa,EAAE,KAAK,KAChB;gCAAE,GAAG,YAAY;gCAAE,QAAQ;4BAAK,IAChC;wBAEN,WAAW;oBACb,CAAC;gBAED,uCAAuC;gBACvC,MAAM,iBAAiB;YACzB,EAAE,OAAO,OAAO;gBACd,IAAI;oBACF,WAAW,iBAAiB,QAAQ,MAAM,OAAO,GAAG;gBACtD;YACF;QACF;QAEA,mCAAmC;QACnC,eAAe;YACb,MAAM,EAAE,IAAI,EAAE,GAAG,6IAAA,CAAA,eAAY,CAAC,QAAQ;YAEtC,IAAI;gBACF,MAAM,KACJ,6IAAA,CAAA,qBAAkB,CAAC,aAAa,uBAEhC;oBAAE,KAAK;gBAAK;gBAGd,IAAI,CAAC,QAAU,CAAC;wBACd,eAAe,MAAM,aAAa,CAAC,GAAG,CAAC,CAAA,eAAgB,CAAC;gCACtD,GAAG,YAAY;gCACf,QAAQ;4BACV,CAAC;wBACD,aAAa;wBACb,WAAW;oBACb,CAAC;YACH,EAAE,OAAO,OAAO;gBACd,IAAI;oBACF,WAAW,iBAAiB,QAAQ,MAAM,OAAO,GAAG;gBACtD;YACF;QACF;QAEA,gBAAgB;QAChB,oBAAoB,OAAO;YACzB,MAAM,EAAE,IAAI,EAAE,GAAG,6IAAA,CAAA,eAAY,CAAC,QAAQ;YAEtC,IAAI;gBACF,MAAM,KACJ,6IAAA,CAAA,qBAAkB,CAAC,aAAa,yBAEhC;oBAAE;gBAAG;gBAGP,IAAI,CAAC,QAAU,CAAC;wBACd,eAAe,MAAM,aAAa,CAAC,MAAM,CAAC,CAAA,eAAgB,aAAa,EAAE,KAAK;wBAC9E,WAAW;oBACb,CAAC;gBAED,uCAAuC;gBACvC,MAAM,iBAAiB;YACzB,EAAE,OAAO,OAAO;gBACd,IAAI;oBACF,WAAW,iBAAiB,QAAQ,MAAM,OAAO,GAAG;gBACtD;YACF;QACF;QAEA,qBAAqB;QACrB,iBAAiB,CAAC;YAChB,IAAI,CAAC,QAAU,CAAC;oBACd,eAAe;wBAAC;2BAAiB,MAAM,aAAa;qBAAC;oBACrD,WAAW;gBACb,CAAC;YAED,uCAAuC;YACvC,MAAM,iBAAiB;QACzB;QAEA,uCAAuC;QACvC,mBAAmB;YACjB,MAAM,EAAE,aAAa,EAAE,GAAG;YAC1B,MAAM,cAAc,cAAc,MAAM,CAAC,CAAA,eAAgB,CAAC,aAAa,MAAM,EAAE,MAAM;YACrF,IAAI;gBAAE;YAAY;QACpB;IACF,CAAC;AAGM,MAAM,mBAAmB;IAC9B,MAAM,EACJ,aAAa,EACb,WAAW,EACX,SAAS,EACT,SAAS,EACT,kBAAkB,EAClB,UAAU,EACV,aAAa,EACb,kBAAkB,EAClB,eAAe,EAChB,GAAG;IAEJ,MAAM,EAAE,OAAO,EAAE,SAAS,EAAE,WAAW,EAAE,GAAG,CAAA,GAAA,6IAAA,CAAA,eAAY,AAAD;IACvD,MAAM,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,4IAAA,CAAA,qBAAkB,AAAD;IAErC,sDAAsD;IACtD,CAAA,GAAA,8SAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,QAAQ,aAAa,OAAO,CAAC;QAEnC,IAAI,SAAS,WAAW,CAAC,WAAW,CAAC,6IAAA,CAAA,qBAAkB,CAAC,aAAa,CAAC,EAAE;YACtE,QAAQ,6IAAA,CAAA,qBAAkB,CAAC,aAAa;QAC1C;IACF,GAAG;QAAC;QAAS;QAAa;KAAQ;IAElC,0BAA0B;IAC1B,CAAA,GAAA,8SAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,WAAW,CAAC,WAAW,CAAC,6IAAA,CAAA,qBAAkB,CAAC,aAAa,CAAC,EAAE;QAEhE,0CAA0C;QAC1C,MAAM,cAAc,UAClB,6IAAA,CAAA,qBAAkB,CAAC,aAAa,sBAEhC,CAAC;YACC,gBAAgB;QAClB;QAGF,0BAA0B;QAC1B;QAEA,0BAA0B;QAC1B,OAAO;IACT,GAAG;QAAC;QAAW;QAAa;QAAoB;QAAiB;KAAQ;IAEzE,OAAO;QACL;QACA;QACA;QACA;QACA;QACA;QACA;IACF;AACF", "debugId": null}}, {"offset": {"line": 762, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/bloomgoo/gold-exchange-platform/frontend/components/providers/WebSocketProvider.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { ReactNode, useEffect } from 'react';\r\nimport { useAutoConnectWebSocket, WebSocketNamespace } from '@/services/websocket/websocket.service';\r\nimport { usePolygonForex } from '@/services/websocket/polygon-forex-socket.service';\r\nimport { useNotifications } from '@/services/websocket/notification-socket.service';\r\nimport { useWebSocketConfig } from '@/services/websocket/websocket-config';\r\n\r\ninterface WebSocketProviderProps {\r\n  children: ReactNode;\r\n}\r\n\r\n/**\r\n * Provider để quản lý kết nối WebSocket\r\n * Đặt provider này bên trong AuthProvider để đảm bảo có thể truy cập thông tin xác thực\r\n */\r\nexport function WebSocketProvider({ children }: WebSocketProviderProps) {\r\n  // Sử dụng hook để tự động kết nối WebSocket\r\n  const { isConnected, connect } = useAutoConnectWebSocket();\r\n\r\n  // Khởi tạo các service\r\n  const forexPrice = usePolygonForex();\r\n  const notifications = useNotifications();\r\n\r\n  // Lấy cấu hình WebSocket ở cấp cao nhất của component\r\n  const { enabled, enableWebSocket } = useWebSocketConfig();\r\n\r\n  // Kích hoạt WebSocket\r\n  useEffect(() => {\r\n    // Đảm bảo WebSocket được bật\r\n    if (!enabled) {\r\n       \r\n      enableWebSocket();\r\n    }\r\n  }, [enabled, enableWebSocket]);\r\n\r\n  // Log trạng thái kết nối khi thay đổi (chỉ để debug)\r\n  useEffect(() => {\r\n     \r\n     \r\n\r\n    // Kiểm tra token\r\n    const token = localStorage.getItem('accessToken');\r\n     \r\n\r\n    // Sử dụng biến enabled từ cấp cao nhất của component\r\n     \r\n\r\n    // Thử kết nối lại nếu chưa kết nối\r\n    if (!isConnected[WebSocketNamespace.FOREX] && token && enabled) {\r\n       \r\n      connect(WebSocketNamespace.FOREX);\r\n    }\r\n\r\n    if (forexPrice.isSubscribed) {\r\n       \r\n    }\r\n\r\n    if (notifications.unreadCount > 0) {\r\n       \r\n    }\r\n\r\n    // Log lỗi nếu có\r\n    if (forexPrice.lastError) {\r\n      console.error(`Forex error: ${forexPrice.lastError}`);\r\n    }\r\n  }, [\r\n    isConnected,\r\n    forexPrice.isSubscribed,\r\n    forexPrice.lastError,\r\n    notifications.unreadCount,\r\n    enabled,\r\n    connect\r\n  ]);\r\n\r\n  return <>{children}</>;\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AANA;;;;;;;AAgBO,SAAS,kBAAkB,EAAE,QAAQ,EAA0B;IACpE,4CAA4C;IAC5C,MAAM,EAAE,WAAW,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,6IAAA,CAAA,0BAAuB,AAAD;IAEvD,uBAAuB;IACvB,MAAM,aAAa,CAAA,GAAA,8JAAA,CAAA,kBAAe,AAAD;IACjC,MAAM,gBAAgB,CAAA,GAAA,0JAAA,CAAA,mBAAgB,AAAD;IAErC,sDAAsD;IACtD,MAAM,EAAE,OAAO,EAAE,eAAe,EAAE,GAAG,CAAA,GAAA,4IAAA,CAAA,qBAAkB,AAAD;IAEtD,sBAAsB;IACtB,CAAA,GAAA,8SAAA,CAAA,YAAS,AAAD,EAAE;QACR,6BAA6B;QAC7B,IAAI,CAAC,SAAS;YAEZ;QACF;IACF,GAAG;QAAC;QAAS;KAAgB;IAE7B,qDAAqD;IACrD,CAAA,GAAA,8SAAA,CAAA,YAAS,AAAD,EAAE;QAIR,iBAAiB;QACjB,MAAM,QAAQ,aAAa,OAAO,CAAC;QAGnC,qDAAqD;QAGrD,mCAAmC;QACnC,IAAI,CAAC,WAAW,CAAC,6IAAA,CAAA,qBAAkB,CAAC,KAAK,CAAC,IAAI,SAAS,SAAS;YAE9D,QAAQ,6IAAA,CAAA,qBAAkB,CAAC,KAAK;QAClC;QAEA,IAAI,WAAW,YAAY,EAAE,CAE7B;QAEA,IAAI,cAAc,WAAW,GAAG,GAAG,CAEnC;QAEA,iBAAiB;QACjB,IAAI,WAAW,SAAS,EAAE;YACxB,QAAQ,KAAK,CAAC,CAAC,aAAa,EAAE,WAAW,SAAS,EAAE;QACtD;IACF,GAAG;QACD;QACA,WAAW,YAAY;QACvB,WAAW,SAAS;QACpB,cAAc,WAAW;QACzB;QACA;KACD;IAED,qBAAO;kBAAG;;AACZ", "debugId": null}}, {"offset": {"line": 829, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/bloomgoo/gold-exchange-platform/frontend/components/ui/sonner.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport { useTheme } from \"next-themes\"\r\nimport { Toaster as Son<PERSON>, ToasterP<PERSON> } from \"sonner\"\r\n\r\nconst Toaster = ({ ...props }: ToasterProps) => {\r\n  const { theme = \"system\" } = useTheme()\r\n\r\n  return (\r\n    <Sonner\r\n      theme={theme as ToasterProps[\"theme\"]}\r\n      className=\"toaster group\"\r\n      style={\r\n        {\r\n          \"--normal-bg\": \"var(--popover)\",\r\n          \"--normal-text\": \"var(--popover-foreground)\",\r\n          \"--normal-border\": \"var(--border)\",\r\n        } as React.CSSProperties\r\n      }\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport { Toaster }\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAKA,MAAM,UAAU,CAAC,EAAE,GAAG,OAAqB;IACzC,MAAM,EAAE,QAAQ,QAAQ,EAAE,GAAG,CAAA,GAAA,yPAAA,CAAA,WAAQ,AAAD;IAEpC,qBACE,uVAAC,wQAAA,CAAA,UAAM;QACL,OAAO;QACP,WAAU;QACV,OACE;YACE,eAAe;YACf,iBAAiB;YACjB,mBAAmB;QACrB;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 863, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/bloomgoo/gold-exchange-platform/frontend/lib/constants/paths.ts"], "sourcesContent": ["/**\n * Path constants for consistent usage across the application\n */\n\n// <PERSON><PERSON><PERSON> đường dẫn không yêu cầu xác thực\nexport const PUBLIC_PATHS = [\n  '/login',\n  '/register', \n  '/forgot-password',\n  '/reset-password'\n] as const;\n\n// <PERSON><PERSON><PERSON> đường dẫn không nên tự động chuyển hướng ngay cả khi đã xác thực\n// Bao gồm các callback từ OAuth providers\nexport const EXCLUDE_REDIRECT_PATHS = [\n  '/login/callback'  // Google OAuth callback\n] as const;\n\n// Các đường dẫn dành riêng cho admin\nexport const ADMIN_PATHS = [\n  '/admin'\n] as const;\n\n// Helper functions\nexport const isPublicPath = (pathname: string): boolean => {\n  return PUBLIC_PATHS.some(path => \n    pathname === path || pathname.startsWith(`${path}/`)\n  );\n};\n\nexport const isAdminPath = (pathname: string): boolean => {\n  return ADMIN_PATHS.some(path =>\n    pathname === path || pathname.startsWith(`${path}/`)\n  );\n};\n\nexport const isExcludeRedirectPath = (pathname: string): boolean => {\n  return EXCLUDE_REDIRECT_PATHS.some(path =>\n    pathname === path || pathname.startsWith(`${path}/`)\n  );\n};\n"], "names": [], "mappings": "AAAA;;CAEC,GAED,uCAAuC;;;;;;;;;AAChC,MAAM,eAAe;IAC1B;IACA;IACA;IACA;CACD;AAIM,MAAM,yBAAyB;IACpC,kBAAmB,wBAAwB;CAC5C;AAGM,MAAM,cAAc;IACzB;CACD;AAGM,MAAM,eAAe,CAAC;IAC3B,OAAO,aAAa,IAAI,CAAC,CAAA,OACvB,aAAa,QAAQ,SAAS,UAAU,CAAC,GAAG,KAAK,CAAC,CAAC;AAEvD;AAEO,MAAM,cAAc,CAAC;IAC1B,OAAO,YAAY,IAAI,CAAC,CAAA,OACtB,aAAa,QAAQ,SAAS,UAAU,CAAC,GAAG,KAAK,CAAC,CAAC;AAEvD;AAEO,MAAM,wBAAwB,CAAC;IACpC,OAAO,uBAAuB,IAAI,CAAC,CAAA,OACjC,aAAa,QAAQ,SAAS,UAAU,CAAC,GAAG,KAAK,CAAC,CAAC;AAEvD", "debugId": null}}, {"offset": {"line": 901, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/bloomgoo/gold-exchange-platform/frontend/lib/api.ts"], "sourcesContent": ["/**\r\n * API client cho ứng dụng\r\n */\r\nimport { authService, isTokenExpired } from './auth';\r\nimport { isPublicPath } from './constants/paths';\r\n\r\nconst NEXT_PUBLIC_API_URL = process.env.NEXT_PUBLIC_API_URL || \"http://localhost:3000/api/v1\"; \r\n\r\n// Biến để theo dõi các yêu cầu đang chờ refresh token\r\nlet isRefreshing = false;\r\nlet failedQueue: { resolve: (value: unknown) => void; reject: (reason?: any) => void }[] = [];\r\n\r\n/**\r\n * Xử lý hàng đợi các yêu cầu thất bại\r\n */\r\nfunction processQueue(error: any | null, token: string | null = null) {\r\n  failedQueue.forEach(prom => {\r\n    if (error) {\r\n      prom.reject(error);\r\n    } else {\r\n      prom.resolve(token);\r\n    }\r\n  });\r\n\r\n  failedQueue = [];\r\n}\r\n\r\n/**\r\n * Hàm thử refresh token và thực hiện lại yêu cầu\r\n */\r\nasync function handleTokenRefresh() {\r\n  try {\r\n    if (!isRefreshing) {\r\n      isRefreshing = true;\r\n      \r\n      const success = await authService.tryRefreshToken();\r\n      isRefreshing = false;\r\n      \r\n\r\n      if (success) {\r\n        // Lấy token mới\r\n        const newToken = authService.getAccessToken();\r\n        // Xử lý hàng đợi với token mới\r\n        processQueue(null, newToken);\r\n\r\n        // Lấy thông tin người dùng hiện tại\r\n        const currentUser = authService.getCurrentUser();\r\n        if (currentUser) {\r\n          // Chỉ redirect nếu user đang ở trang không phù hợp với role\r\n          // Không redirect nếu user đã ở đúng trang của role họ\r\n          const currentPath = window.location.pathname;\r\n          const isAdmin = currentUser.roles?.includes('ADMIN');\r\n\r\n          // Chỉ redirect khi cần thiết và không phải trang public\r\n          const isOnPublicPath = isPublicPath(currentPath);\r\n\r\n          const shouldRedirect = !isOnPublicPath && (\r\n            (isAdmin && !currentPath.startsWith('/admin')) ||\r\n            (!isAdmin && currentPath.startsWith('/admin'))\r\n          );\r\n\r\n          if (shouldRedirect) {\r\n            authService.redirectBasedOnRole(currentUser);\r\n          }\r\n        }\r\n\r\n        return newToken;\r\n      } else {\r\n        // Nếu refresh token thất bại, đăng xuất người dùng\r\n        \r\n        authService.logout();\r\n        processQueue(new Error('Refresh token failed'));\r\n        return null;\r\n      }\r\n    } else {\r\n      \r\n      // Nếu đang refresh token, thêm yêu cầu vào hàng đợi\r\n      return new Promise((resolve, reject) => {\r\n        failedQueue.push({ resolve, reject });\r\n      });\r\n    }\r\n  } catch (error) {\r\n    console.error(\"API: Error during token refresh:\", error);\r\n    isRefreshing = false;\r\n    processQueue(error);\r\n    throw error;\r\n  }\r\n}\r\n\r\n/**\r\n * Hàm fetch API với xử lý lỗi và headers mặc định\r\n */\r\nexport async function fetchApi<T>(\r\n  endpoint: string,\r\n  options: RequestInit = {}\r\n): Promise<T> {\r\n  const url = `${NEXT_PUBLIC_API_URL}${endpoint.startsWith('/') ? endpoint : `/${endpoint}`}`;\r\n\r\n  // Hàm để thực hiện yêu cầu API\r\n  const executeRequest = async (token?: string | null) => {\r\n    // Tạo headers mới\r\n    const headers = new Headers(options.headers);\r\n    headers.set('Content-Type', 'application/json');\r\n\r\n    // Thêm token xác thực nếu có\r\n    const accessToken = token || (typeof window !== 'undefined' ? localStorage.getItem('accessToken') : null);\r\n    if (accessToken) {\r\n      headers.set('Authorization', `Bearer ${accessToken}`);\r\n    }\r\n\r\n    const config = {\r\n      ...options,\r\n      headers,\r\n    };\r\n\r\n    const response = await fetch(url, config);\r\n\r\n    // Kiểm tra nếu response không thành công\r\n    if (!response.ok) {\r\n      const errorData = await response.json().catch(() => ({}));\r\n      // Xử lý cấu trúc lỗi từ backend\r\n      const errorMessage = errorData.message ||\r\n                         (errorData.data && errorData.data.message) ||\r\n                         `API request failed with status ${response.status}`;\r\n      console.error('API Error:', errorData);\r\n\r\n      // Tạo error object với thông tin chi tiết hơn\r\n      const error: any = new Error(errorMessage);\r\n      error.status = response.status;\r\n      error.data = errorData;\r\n      throw error;\r\n    }\r\n\r\n    // Parse JSON response\r\n    const responseData = await response.json();\r\n\r\n    // Kiểm tra cấu trúc response từ backend (ApiResponseDto)\r\n    if (responseData.data !== undefined) {\r\n      // Trường hợp response có cấu trúc ApiResponseDto\r\n      return responseData.data;\r\n    } else {\r\n      // Trường hợp response không có cấu trúc ApiResponseDto\r\n      console.warn('API response does not follow standard structure:', responseData);\r\n      return responseData;\r\n    }\r\n  };\r\n\r\n  try {\r\n    // Thử thực hiện yêu cầu\r\n    return await executeRequest();\r\n  } catch (error: any) {\r\n    // Nếu lỗi 401 Unauthorized và không phải là yêu cầu đăng nhập/đăng ký/refresh token\r\n    if (error.status === 401 &&\r\n        !endpoint.includes('auth/login') &&\r\n        !endpoint.includes('auth/register') &&\r\n        !endpoint.includes('auth/refresh-token')) {\r\n      try {\r\n        // Thử refresh token\r\n        const newToken = await handleTokenRefresh();\r\n        if (newToken) {\r\n          // Thực hiện lại yêu cầu với token mới\r\n          return await executeRequest(newToken);\r\n        }\r\n      } catch (refreshError) {\r\n        console.error('Error refreshing token:', refreshError);\r\n        // Nếu refresh token thất bại, đăng xuất người dùng\r\n        authService.logout();\r\n        throw error; // Trả về lỗi ban đầu\r\n      }\r\n    }\r\n\r\n    console.error('API request error:', error);\r\n    throw error;\r\n  }\r\n}\r\n\r\n/**\r\n * Các phương thức HTTP\r\n */\r\nexport const api = {\r\n  baseUrl: NEXT_PUBLIC_API_URL,\r\n\r\n  getToken: () => {\r\n    return typeof window !== 'undefined' ? localStorage.getItem('accessToken') : null;\r\n  },\r\n\r\n  get: <T>(endpoint: string, options?: RequestInit) =>\r\n    fetchApi<T>(endpoint, { ...options, method: 'GET' }),\r\n\r\n  post: <T>(endpoint: string, data?: any, options?: RequestInit) =>\r\n    fetchApi<T>(endpoint, {\r\n      ...options,\r\n      method: 'POST',\r\n      body: data ? JSON.stringify(data) : undefined,\r\n    }),\r\n\r\n  put: <T>(endpoint: string, data?: any, options?: RequestInit) =>\r\n    fetchApi<T>(endpoint, {\r\n      ...options,\r\n      method: 'PUT',\r\n      body: data ? JSON.stringify(data) : undefined,\r\n    }),\r\n\r\n  patch: <T>(endpoint: string, data?: any, options?: RequestInit) =>\r\n    fetchApi<T>(endpoint, {\r\n      ...options,\r\n      method: 'PATCH',\r\n      body: data ? JSON.stringify(data) : undefined,\r\n    }),\r\n\r\n  delete: <T>(endpoint: string, options?: RequestInit) =>\r\n    fetchApi<T>(endpoint, { ...options, method: 'DELETE' }),\r\n\r\n  // Phương thức download file\r\n  downloadFile: async (endpoint: string, format: string, filename: string) => {\r\n    const url = `${NEXT_PUBLIC_API_URL}${endpoint.startsWith('/') ? endpoint : `/${endpoint}`}`;\r\n    const token = typeof window !== 'undefined' ? localStorage.getItem('accessToken') : null;\r\n\r\n    try {\r\n      // Sử dụng XMLHttpRequest thay vì fetch để xử lý tải file tốt hơn\r\n      return new Promise((resolve, reject) => {\r\n        const xhr = new XMLHttpRequest();\r\n        xhr.open('GET', url, true);\r\n        xhr.responseType = 'blob';\r\n        xhr.setRequestHeader('Authorization', token ? `Bearer ${token}` : '');\r\n\r\n        xhr.onload = function() {\r\n          if (this.status === 200) {\r\n            const blob = new Blob([this.response], {\r\n              type: format === 'csv' ? 'text/csv' : 'application/json'\r\n            });\r\n            const downloadUrl = window.URL.createObjectURL(blob);\r\n\r\n            const a = document.createElement('a');\r\n            a.href = downloadUrl;\r\n            a.download = filename;\r\n            document.body.appendChild(a);\r\n            a.click();\r\n\r\n            // Cleanup\r\n            window.URL.revokeObjectURL(downloadUrl);\r\n            document.body.removeChild(a);\r\n\r\n            resolve(true);\r\n          } else {\r\n            reject(new Error(`Download failed: ${this.status} ${this.statusText}`));\r\n          }\r\n        };\r\n\r\n        xhr.onerror = function() {\r\n          reject(new Error('Network error occurred'));\r\n        };\r\n\r\n        xhr.send();\r\n      });\r\n    } catch (error) {\r\n      console.error('Download error:', error);\r\n      throw error;\r\n    }\r\n  }\r\n};\r\n"], "names": [], "mappings": "AAAA;;CAEC;;;;AACD;AACA;;;AAEA,MAAM,sBAAsB,oEAAmC;AAE/D,sDAAsD;AACtD,IAAI,eAAe;AACnB,IAAI,cAAuF,EAAE;AAE7F;;CAEC,GACD,SAAS,aAAa,KAAiB,EAAE,QAAuB,IAAI;IAClE,YAAY,OAAO,CAAC,CAAA;QAClB,IAAI,OAAO;YACT,KAAK,MAAM,CAAC;QACd,OAAO;YACL,KAAK,OAAO,CAAC;QACf;IACF;IAEA,cAAc,EAAE;AAClB;AAEA;;CAEC,GACD,eAAe;IACb,IAAI;QACF,IAAI,CAAC,cAAc;YACjB,eAAe;YAEf,MAAM,UAAU,MAAM,2GAAA,CAAA,cAAW,CAAC,eAAe;YACjD,eAAe;YAGf,IAAI,SAAS;gBACX,gBAAgB;gBAChB,MAAM,WAAW,2GAAA,CAAA,cAAW,CAAC,cAAc;gBAC3C,+BAA+B;gBAC/B,aAAa,MAAM;gBAEnB,oCAAoC;gBACpC,MAAM,cAAc,2GAAA,CAAA,cAAW,CAAC,cAAc;gBAC9C,IAAI,aAAa;oBACf,4DAA4D;oBAC5D,sDAAsD;oBACtD,MAAM,cAAc,OAAO,QAAQ,CAAC,QAAQ;oBAC5C,MAAM,UAAU,YAAY,KAAK,EAAE,SAAS;oBAE5C,wDAAwD;oBACxD,MAAM,iBAAiB,CAAA,GAAA,yHAAA,CAAA,eAAY,AAAD,EAAE;oBAEpC,MAAM,iBAAiB,CAAC,kBAAkB,CACxC,AAAC,WAAW,CAAC,YAAY,UAAU,CAAC,aACnC,CAAC,WAAW,YAAY,UAAU,CAAC,SACtC;oBAEA,IAAI,gBAAgB;wBAClB,2GAAA,CAAA,cAAW,CAAC,mBAAmB,CAAC;oBAClC;gBACF;gBAEA,OAAO;YACT,OAAO;gBACL,mDAAmD;gBAEnD,2GAAA,CAAA,cAAW,CAAC,MAAM;gBAClB,aAAa,IAAI,MAAM;gBACvB,OAAO;YACT;QACF,OAAO;YAEL,oDAAoD;YACpD,OAAO,IAAI,QAAQ,CAAC,SAAS;gBAC3B,YAAY,IAAI,CAAC;oBAAE;oBAAS;gBAAO;YACrC;QACF;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,oCAAoC;QAClD,eAAe;QACf,aAAa;QACb,MAAM;IACR;AACF;AAKO,eAAe,SACpB,QAAgB,EAChB,UAAuB,CAAC,CAAC;IAEzB,MAAM,MAAM,GAAG,sBAAsB,SAAS,UAAU,CAAC,OAAO,WAAW,CAAC,CAAC,EAAE,UAAU,EAAE;IAE3F,+BAA+B;IAC/B,MAAM,iBAAiB,OAAO;QAC5B,kBAAkB;QAClB,MAAM,UAAU,IAAI,QAAQ,QAAQ,OAAO;QAC3C,QAAQ,GAAG,CAAC,gBAAgB;QAE5B,6BAA6B;QAC7B,MAAM,cAAc,SAAS,CAAC,6EAAsE,IAAI;QACxG,IAAI,aAAa;YACf,QAAQ,GAAG,CAAC,iBAAiB,CAAC,OAAO,EAAE,aAAa;QACtD;QAEA,MAAM,SAAS;YACb,GAAG,OAAO;YACV;QACF;QAEA,MAAM,WAAW,MAAM,MAAM,KAAK;QAElC,yCAAyC;QACzC,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,YAAY,MAAM,SAAS,IAAI,GAAG,KAAK,CAAC,IAAM,CAAC,CAAC,CAAC;YACvD,gCAAgC;YAChC,MAAM,eAAe,UAAU,OAAO,IAClB,UAAU,IAAI,IAAI,UAAU,IAAI,CAAC,OAAO,IACzC,CAAC,+BAA+B,EAAE,SAAS,MAAM,EAAE;YACtE,QAAQ,KAAK,CAAC,cAAc;YAE5B,8CAA8C;YAC9C,MAAM,QAAa,IAAI,MAAM;YAC7B,MAAM,MAAM,GAAG,SAAS,MAAM;YAC9B,MAAM,IAAI,GAAG;YACb,MAAM;QACR;QAEA,sBAAsB;QACtB,MAAM,eAAe,MAAM,SAAS,IAAI;QAExC,yDAAyD;QACzD,IAAI,aAAa,IAAI,KAAK,WAAW;YACnC,iDAAiD;YACjD,OAAO,aAAa,IAAI;QAC1B,OAAO;YACL,uDAAuD;YACvD,QAAQ,IAAI,CAAC,oDAAoD;YACjE,OAAO;QACT;IACF;IAEA,IAAI;QACF,wBAAwB;QACxB,OAAO,MAAM;IACf,EAAE,OAAO,OAAY;QACnB,oFAAoF;QACpF,IAAI,MAAM,MAAM,KAAK,OACjB,CAAC,SAAS,QAAQ,CAAC,iBACnB,CAAC,SAAS,QAAQ,CAAC,oBACnB,CAAC,SAAS,QAAQ,CAAC,uBAAuB;YAC5C,IAAI;gBACF,oBAAoB;gBACpB,MAAM,WAAW,MAAM;gBACvB,IAAI,UAAU;oBACZ,sCAAsC;oBACtC,OAAO,MAAM,eAAe;gBAC9B;YACF,EAAE,OAAO,cAAc;gBACrB,QAAQ,KAAK,CAAC,2BAA2B;gBACzC,mDAAmD;gBACnD,2GAAA,CAAA,cAAW,CAAC,MAAM;gBAClB,MAAM,OAAO,qBAAqB;YACpC;QACF;QAEA,QAAQ,KAAK,CAAC,sBAAsB;QACpC,MAAM;IACR;AACF;AAKO,MAAM,MAAM;IACjB,SAAS;IAET,UAAU;QACR,OAAO,6EAAsE;IAC/E;IAEA,KAAK,CAAI,UAAkB,UACzB,SAAY,UAAU;YAAE,GAAG,OAAO;YAAE,QAAQ;QAAM;IAEpD,MAAM,CAAI,UAAkB,MAAY,UACtC,SAAY,UAAU;YACpB,GAAG,OAAO;YACV,QAAQ;YACR,MAAM,OAAO,KAAK,SAAS,CAAC,QAAQ;QACtC;IAEF,KAAK,CAAI,UAAkB,MAAY,UACrC,SAAY,UAAU;YACpB,GAAG,OAAO;YACV,QAAQ;YACR,MAAM,OAAO,KAAK,SAAS,CAAC,QAAQ;QACtC;IAEF,OAAO,CAAI,UAAkB,MAAY,UACvC,SAAY,UAAU;YACpB,GAAG,OAAO;YACV,QAAQ;YACR,MAAM,OAAO,KAAK,SAAS,CAAC,QAAQ;QACtC;IAEF,QAAQ,CAAI,UAAkB,UAC5B,SAAY,UAAU;YAAE,GAAG,OAAO;YAAE,QAAQ;QAAS;IAEvD,4BAA4B;IAC5B,cAAc,OAAO,UAAkB,QAAgB;QACrD,MAAM,MAAM,GAAG,sBAAsB,SAAS,UAAU,CAAC,OAAO,WAAW,CAAC,CAAC,EAAE,UAAU,EAAE;QAC3F,MAAM,QAAQ,6EAAsE;QAEpF,IAAI;YACF,iEAAiE;YACjE,OAAO,IAAI,QAAQ,CAAC,SAAS;gBAC3B,MAAM,MAAM,IAAI;gBAChB,IAAI,IAAI,CAAC,OAAO,KAAK;gBACrB,IAAI,YAAY,GAAG;gBACnB,IAAI,gBAAgB,CAAC,iBAAiB,6EAA4B;gBAElE,IAAI,MAAM,GAAG;oBACX,IAAI,IAAI,CAAC,MAAM,KAAK,KAAK;wBACvB,MAAM,OAAO,IAAI,KAAK;4BAAC,IAAI,CAAC,QAAQ;yBAAC,EAAE;4BACrC,MAAM,WAAW,QAAQ,aAAa;wBACxC;wBACA,MAAM,cAAc,OAAO,GAAG,CAAC,eAAe,CAAC;wBAE/C,MAAM,IAAI,SAAS,aAAa,CAAC;wBACjC,EAAE,IAAI,GAAG;wBACT,EAAE,QAAQ,GAAG;wBACb,SAAS,IAAI,CAAC,WAAW,CAAC;wBAC1B,EAAE,KAAK;wBAEP,UAAU;wBACV,OAAO,GAAG,CAAC,eAAe,CAAC;wBAC3B,SAAS,IAAI,CAAC,WAAW,CAAC;wBAE1B,QAAQ;oBACV,OAAO;wBACL,OAAO,IAAI,MAAM,CAAC,iBAAiB,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC,EAAE,IAAI,CAAC,UAAU,EAAE;oBACvE;gBACF;gBAEA,IAAI,OAAO,GAAG;oBACZ,OAAO,IAAI,MAAM;gBACnB;gBAEA,IAAI,IAAI;YACV;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,mBAAmB;YACjC,MAAM;QACR;IACF;AACF", "debugId": null}}, {"offset": {"line": 1119, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/bloomgoo/gold-exchange-platform/frontend/lib/auth.ts"], "sourcesContent": ["/**\r\n * Service xử lý xác thực người dùng\r\n */\r\nimport { api } from './api';\r\n\r\n// Đ<PERSON>nh nghĩa các kiểu dữ liệu\r\nexport interface LoginCredentials {\r\n  identity: string;\r\n  password: string;\r\n}\r\n\r\nexport interface RegisterCredentials {\r\n  username: string;\r\n  email: string;\r\n  password: string;\r\n  fullName?: string;\r\n  phone: string; // Đã thay đổi từ optional thành required\r\n  address?: string;\r\n  referredByCode?: string;\r\n  firebaseToken?: string; // Token xác thực từ Firebase sau khi xác minh OTP\r\n  registerType?: 'PHONE' | 'EMAIL'; // Phương thức xác thực\r\n}\r\n\r\nexport interface User {\r\n  id: string;\r\n  username: string;\r\n  email: string;\r\n  fullName?: string;\r\n  roles?: string[];\r\n  permissions?: string[];\r\n  exp?: number; // Thời gian hết hạn của token\r\n  [key: string]: any;\r\n}\r\n\r\nexport interface AuthResponse {\r\n  access_token: string;\r\n  refresh_token: string;\r\n  user?: User;\r\n}\r\n\r\nexport interface RefreshTokenRequest {\r\n  refreshToken: string;\r\n}\r\n\r\n/**\r\n * Hàm giải mã JWT token để lấy thông tin người dùng\r\n */\r\nexport const decodeToken = (token: string): User | null => {\r\n  try {\r\n    if (!token) return null;\r\n\r\n    // JWT token có 3 phần: header.payload.signature\r\n    const base64Url = token.split('.')[1];\r\n    if (!base64Url) return null;\r\n\r\n    // Thay thế các ký tự đặc biệt trong base64Url\r\n    const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');\r\n\r\n    // Giải mã base64 thành JSON\r\n    const jsonPayload = decodeURIComponent(\r\n      atob(base64)\r\n        .split('')\r\n        .map(c => '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2))\r\n        .join('')\r\n    );\r\n\r\n    const payload = JSON.parse(jsonPayload);\r\n\r\n    // Chuyển đổi payload thành User\r\n    const user: User = {\r\n      id: payload.sub || payload.id || '',\r\n      username: payload.username || '',\r\n      email: payload.email || '',\r\n      fullName: payload.fullName || payload.name || '',\r\n      roles: payload.roles || [],\r\n      permissions: payload.permissions || [],\r\n      exp: payload.exp || 0, // Thêm trường exp (expiration time)\r\n    };\r\n\r\n    return user;\r\n  } catch (error) {\r\n    console.error('Error decoding token:', error);\r\n    return null;\r\n  }\r\n};\r\n\r\n/**\r\n * Kiểm tra xem token đã hết hạn chưa\r\n */\r\nexport const isTokenExpired = (token: string): boolean => {\r\n  try {\r\n    if (!token) return true;\r\n\r\n    const decodedToken = decodeToken(token);\r\n    if (!decodedToken || !decodedToken.exp) return true;\r\n\r\n    // exp là thời gian hết hạn tính bằng giây kể từ epoch (1/1/1970)\r\n    const currentTime = Math.floor(Date.now() / 1000); // Chuyển đổi thời gian hiện tại sang giây\r\n\r\n    // Trả về true nếu token đã hết hạn\r\n    return decodedToken.exp < currentTime;\r\n  } catch (error) {\r\n    console.error('Error checking token expiration:', error);\r\n    return true; // Nếu có lỗi, coi như token đã hết hạn\r\n  }\r\n};\r\n\r\n/**\r\n * Kiểm tra xem token có sắp hết hạn không (trong vòng 5 phút)\r\n */\r\nexport const isTokenExpiringSoon = (token: string, thresholdSeconds: number = 300): boolean => {\r\n  try {\r\n    if (!token) return true;\r\n\r\n    const decodedToken = decodeToken(token);\r\n    if (!decodedToken || !decodedToken.exp) return true;\r\n\r\n    const currentTime = Math.floor(Date.now() / 1000);\r\n\r\n    // Trả về true nếu token sắp hết hạn trong vòng thresholdSeconds giây\r\n    return decodedToken.exp - currentTime < thresholdSeconds;\r\n  } catch (error) {\r\n    console.error('Error checking token expiration:', error);\r\n    return true;\r\n  }\r\n};\r\n\r\n/**\r\n * Service xử lý xác thực\r\n */\r\nexport const authService = {\r\n  /**\r\n   * Đăng nhập người dùng\r\n   */\r\n  async login(credentials: LoginCredentials): Promise<AuthResponse> {\r\n    try {\r\n      const response = await api.post<{ data: AuthResponse }>('auth/login', credentials);\r\n\r\n      // Kiểm tra cấu trúc response\r\n      const authData = response.data || response;\r\n\r\n      // Lưu token vào localStorage và cookie\r\n      localStorage.setItem('accessToken', authData.access_token);\r\n      localStorage.setItem('refreshToken', authData.refresh_token);\r\n\r\n      // Lưu token vào cookie để middleware có thể truy cập\r\n      document.cookie = `token=${authData.access_token}; path=/; max-age=${60 * 60 * 24 * 7}; SameSite=Lax`; // 7 ngày\r\n\r\n      // Nếu không có thông tin người dùng trong response, giải mã token để lấy\r\n      if (!authData.user && authData.access_token) {\r\n        const decodedUser = decodeToken(authData.access_token);\r\n        if (decodedUser) {\r\n          authData.user = decodedUser;\r\n          this.saveUser(decodedUser);\r\n        }\r\n      } else if (authData.user) {\r\n        // Lưu thông tin người dùng\r\n        this.saveUser(authData.user);\r\n      }\r\n\r\n      return authData;\r\n    } catch (error) {\r\n      console.error('Login error:', error);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  /**\r\n   * Đăng ký người dùng mới\r\n   */\r\n  async register(credentials: RegisterCredentials): Promise<AuthResponse> {\r\n    try {\r\n      const response = await api.post<{ data: AuthResponse }>('auth/register', credentials);\r\n\r\n      // Kiểm tra cấu trúc response\r\n      const authData = response.data || response;\r\n\r\n      // Lưu token vào localStorage và cookie\r\n      localStorage.setItem('accessToken', authData.access_token);\r\n      localStorage.setItem('refreshToken', authData.refresh_token);\r\n\r\n      // Lưu token vào cookie để middleware có thể truy cập\r\n      document.cookie = `token=${authData.access_token}; path=/; max-age=${60 * 60 * 24 * 7}; SameSite=Lax`; // 7 ngày\r\n\r\n      // Nếu không có thông tin người dùng trong response, giải mã token để lấy\r\n      if (!authData.user && authData.access_token) {\r\n        const decodedUser = decodeToken(authData.access_token);\r\n        if (decodedUser) {\r\n          authData.user = decodedUser;\r\n          this.saveUser(decodedUser);\r\n        }\r\n      } else if (authData.user) {\r\n        // Lưu thông tin người dùng\r\n        this.saveUser(authData.user);\r\n      }\r\n\r\n      return authData;\r\n    } catch (error) {\r\n      console.error('Register error:', error);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  /**\r\n   * Làm mới token\r\n   */\r\n  async refreshToken(refreshToken: string): Promise<AuthResponse> {\r\n    try {\r\n      const response = await api.post<{ data: AuthResponse }>('auth/refresh-token', { refreshToken });\r\n\r\n      // Kiểm tra cấu trúc response\r\n      const authData = response.data || response;\r\n\r\n      // Cập nhật token trong localStorage và cookie\r\n      localStorage.setItem('accessToken', authData.access_token);\r\n      localStorage.setItem('refreshToken', authData.refresh_token);\r\n\r\n      // Cập nhật token trong cookie để middleware có thể truy cập\r\n      document.cookie = `token=${authData.access_token}; path=/; max-age=${60 * 60 * 24 * 7}; SameSite=Lax`; // 7 ngày\r\n\r\n      // Nếu không có thông tin người dùng trong response, giải mã token để lấy\r\n      if (!authData.user && authData.access_token) {\r\n        const decodedUser = decodeToken(authData.access_token);\r\n        if (decodedUser) {\r\n          authData.user = decodedUser;\r\n          this.saveUser(decodedUser);\r\n        }\r\n      } else if (authData.user) {\r\n        // Lưu thông tin người dùng\r\n        this.saveUser(authData.user);\r\n      }\r\n\r\n      return authData;\r\n    } catch (error) {\r\n      console.error('Refresh token error:', error);\r\n      // Nếu refresh token thất bại, đăng xuất người dùng\r\n      this.logout();\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  /**\r\n   * Thử làm mới token nếu có refresh token\r\n   */\r\n  async tryRefreshToken(): Promise<boolean> {\r\n    try {\r\n      const refreshToken = this.getRefreshToken();\r\n      if (!refreshToken) return false;\r\n\r\n      const result = await this.refreshToken(refreshToken);\r\n\r\n      // Đảm bảo user state được cập nhật sau refresh\r\n      if (result && result.user) {\r\n        // Trigger event để các component khác biết user đã được cập nhật\r\n        window.dispatchEvent(new CustomEvent('auth:userUpdated', {\r\n          detail: { user: result.user }\r\n        }));\r\n      }\r\n\r\n      // Việc điều hướng sẽ được xử lý ở handleTokenRefresh trong api.ts\r\n      // Không điều hướng ở đây để tránh điều hướng kép\r\n\r\n      return true;\r\n    } catch (error) {\r\n      console.error('Try refresh token error:', error);\r\n      return false;\r\n    }\r\n  },\r\n\r\n  /**\r\n   * Điều hướng người dùng dựa trên vai trò\r\n   * @param user - User object\r\n   * @param forceRedirect - Có bắt buộc redirect không (mặc định: false)\r\n   */\r\n  redirectBasedOnRole(user: User, forceRedirect: boolean = false): void {\r\n    if (typeof window === 'undefined') return;\r\n\r\n    // Ưu tiên ADMIN trước - kiểm tra roles an toàn\r\n    const isAdmin = user.roles && Array.isArray(user.roles) && user.roles.includes('ADMIN');\r\n    const currentPath = window.location.pathname;\r\n\r\n    console.log('redirectBasedOnRole:', {\r\n      userId: user.id,\r\n      roles: user.roles,\r\n      isAdmin,\r\n      currentPath,\r\n      forceRedirect\r\n    });\r\n\r\n    // Nếu không bắt buộc redirect, kiểm tra xem có cần redirect không\r\n    if (!forceRedirect) {\r\n      const isOnCorrectPath =\r\n        (isAdmin && currentPath.startsWith('/admin')) ||\r\n        (!isAdmin && !currentPath.startsWith('/admin') && currentPath !== '/login');\r\n\r\n      // Nếu đã ở đúng path, không cần redirect\r\n      if (isOnCorrectPath) {\r\n        console.log('Already on correct path, no redirect needed');\r\n        return;\r\n      }\r\n    }\r\n\r\n    // Điều hướng dựa vào vai trò - ưu tiên ADMIN\r\n    if (isAdmin) {\r\n      console.log('Redirecting to admin dashboard');\r\n      window.location.href = '/admin/dashboard';\r\n    } else {\r\n      console.log('Redirecting to user dashboard');\r\n      window.location.href = '/dashboard';\r\n    }\r\n  },\r\n\r\n  /**\r\n   * Đăng xuất người dùng\r\n   */\r\n  logout(): void {\r\n    localStorage.removeItem('accessToken');\r\n    localStorage.removeItem('refreshToken');\r\n    localStorage.removeItem('user');\r\n\r\n    // Xóa cookie token\r\n    document.cookie = 'token=; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT; SameSite=Lax';\r\n\r\n    // Delay redirect một chút để UI có thời gian cập nhật\r\n    if (typeof window !== 'undefined') {\r\n      setTimeout(() => {\r\n        window.location.href = '/login';\r\n      }, 50); // 50ms delay - đủ để UI cập nhật\r\n    }\r\n  },\r\n\r\n  /**\r\n   * Kiểm tra người dùng đã đăng nhập chưa\r\n   */\r\n  isAuthenticated(): boolean {\r\n    if (typeof window === 'undefined') return false;\r\n\r\n    // Kiểm tra token trong localStorage hoặc cookie\r\n    const localToken = localStorage.getItem('accessToken');\r\n    if (localToken) {\r\n      // Kiểm tra xem token có hợp lệ và chưa hết hạn không\r\n      if (!isTokenExpired(localToken)) {\r\n        // Nếu token sắp hết hạn, thử refresh token\r\n        if (isTokenExpiringSoon(localToken)) {\r\n          this.tryRefreshToken();\r\n        }\r\n        return true;\r\n      } else {\r\n        // Nếu token đã hết hạn, thử refresh token\r\n        this.tryRefreshToken();\r\n        return false;\r\n      }\r\n    }\r\n\r\n    // Kiểm tra token trong cookie\r\n    const cookies = document.cookie.split(';');\r\n    for (let i = 0; i < cookies.length; i++) {\r\n      const cookie = cookies[i].trim();\r\n      if (cookie.startsWith('token=')) {\r\n        const token = cookie.substring('token='.length, cookie.length);\r\n        if (token) {\r\n          // Kiểm tra xem token có hợp lệ và chưa hết hạn không\r\n          if (!isTokenExpired(token)) {\r\n            // Nếu có token trong cookie nhưng không có trong localStorage,\r\n            // lưu vào localStorage để sử dụng\r\n            if (!localToken) {\r\n              localStorage.setItem('accessToken', token);\r\n              // Giải mã token để lấy thông tin người dùng\r\n              const user = decodeToken(token);\r\n              if (user) {\r\n                this.saveUser(user);\r\n              }\r\n            }\r\n\r\n            // Nếu token sắp hết hạn, thử refresh token\r\n            if (isTokenExpiringSoon(token)) {\r\n              this.tryRefreshToken();\r\n            }\r\n\r\n            return true;\r\n          } else {\r\n            // Nếu token đã hết hạn, thử refresh token\r\n            this.tryRefreshToken();\r\n          }\r\n        }\r\n      }\r\n    }\r\n\r\n    return false;\r\n  },\r\n\r\n  /**\r\n   * Lấy thông tin người dùng hiện tại\r\n   */\r\n  getCurrentUser(): User | null {\r\n    if (typeof window === 'undefined') return null;\r\n\r\n    const userJson = localStorage.getItem('user');\r\n    if (!userJson) return null;\r\n\r\n    try {\r\n      return JSON.parse(userJson);\r\n    } catch (error) {\r\n      console.error('Error parsing user data:', error);\r\n      return null;\r\n    }\r\n  },\r\n\r\n  /**\r\n   * Lưu thông tin người dùng\r\n   */\r\n  saveUser(user: User): void {\r\n    localStorage.setItem('user', JSON.stringify(user));\r\n  },\r\n\r\n  /**\r\n   * Lấy token hiện tại\r\n   */\r\n  getAccessToken(): string | null {\r\n    if (typeof window === 'undefined') return null;\r\n    return localStorage.getItem('accessToken');\r\n  },\r\n\r\n  /**\r\n   * Lấy refresh token\r\n   */\r\n  getRefreshToken(): string | null {\r\n    if (typeof window === 'undefined') return null;\r\n    return localStorage.getItem('refreshToken');\r\n  },\r\n\r\n  /**\r\n   * Xác minh số điện thoại cho người dùng hiện có\r\n   * @param userId ID của người dùng\r\n   * @param firebaseToken Firebase ID token\r\n   * @returns Kết quả xác minh\r\n   */\r\n  async verifyPhoneNumber(userId: string, firebaseToken: string): Promise<any> {\r\n    try {\r\n      const response = await api.post<{ data: any }>('auth/verify-phone-public', {\r\n        userId,\r\n        firebaseToken\r\n      });\r\n\r\n      return response.data || response;\r\n    } catch (error) {\r\n      console.error('Phone verification error:', error);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  /**\r\n   * Unified OTP verification for both phone and email\r\n   * @param verificationData Verification data\r\n   * @returns Verification result with tokens\r\n   */\r\n  async verifyOtp(verificationData: {\r\n    registerType: 'PHONE' | 'EMAIL';\r\n    email?: string;\r\n    otp?: string;\r\n    firebaseToken?: string;\r\n    userId?: string;\r\n  }): Promise<AuthResponse> {\r\n    try {\r\n      const response = await api.post<{ data: AuthResponse }>('auth/verify-otp', verificationData);\r\n\r\n      // Kiểm tra cấu trúc response\r\n      const authData = response.data || response;\r\n\r\n      // Lưu token vào localStorage và cookie\r\n      localStorage.setItem('accessToken', authData.access_token);\r\n      localStorage.setItem('refreshToken', authData.refresh_token);\r\n\r\n      // Lưu token vào cookie để middleware có thể truy cập\r\n      document.cookie = `token=${authData.access_token}; path=/; max-age=${60 * 60 * 24 * 7}; SameSite=Lax`; // 7 ngày\r\n\r\n      // Lưu thông tin user vào localStorage\r\n      if (authData.user) {\r\n        localStorage.setItem('user', JSON.stringify(authData.user));\r\n      }\r\n\r\n      return authData;\r\n    } catch (error) {\r\n      console.error('OTP verification error:', error);\r\n      throw error;\r\n    }\r\n  }\r\n};\r\n"], "names": [], "mappings": "AAAA;;CAEC;;;;;;AACD;;AA4CO,MAAM,cAAc,CAAC;IAC1B,IAAI;QACF,IAAI,CAAC,OAAO,OAAO;QAEnB,gDAAgD;QAChD,MAAM,YAAY,MAAM,KAAK,CAAC,IAAI,CAAC,EAAE;QACrC,IAAI,CAAC,WAAW,OAAO;QAEvB,8CAA8C;QAC9C,MAAM,SAAS,UAAU,OAAO,CAAC,MAAM,KAAK,OAAO,CAAC,MAAM;QAE1D,4BAA4B;QAC5B,MAAM,cAAc,mBAClB,KAAK,QACF,KAAK,CAAC,IACN,GAAG,CAAC,CAAA,IAAK,MAAM,CAAC,OAAO,EAAE,UAAU,CAAC,GAAG,QAAQ,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC,IAC5D,IAAI,CAAC;QAGV,MAAM,UAAU,KAAK,KAAK,CAAC;QAE3B,gCAAgC;QAChC,MAAM,OAAa;YACjB,IAAI,QAAQ,GAAG,IAAI,QAAQ,EAAE,IAAI;YACjC,UAAU,QAAQ,QAAQ,IAAI;YAC9B,OAAO,QAAQ,KAAK,IAAI;YACxB,UAAU,QAAQ,QAAQ,IAAI,QAAQ,IAAI,IAAI;YAC9C,OAAO,QAAQ,KAAK,IAAI,EAAE;YAC1B,aAAa,QAAQ,WAAW,IAAI,EAAE;YACtC,KAAK,QAAQ,GAAG,IAAI;QACtB;QAEA,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,yBAAyB;QACvC,OAAO;IACT;AACF;AAKO,MAAM,iBAAiB,CAAC;IAC7B,IAAI;QACF,IAAI,CAAC,OAAO,OAAO;QAEnB,MAAM,eAAe,YAAY;QACjC,IAAI,CAAC,gBAAgB,CAAC,aAAa,GAAG,EAAE,OAAO;QAE/C,iEAAiE;QACjE,MAAM,cAAc,KAAK,KAAK,CAAC,KAAK,GAAG,KAAK,OAAO,0CAA0C;QAE7F,mCAAmC;QACnC,OAAO,aAAa,GAAG,GAAG;IAC5B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,oCAAoC;QAClD,OAAO,MAAM,uCAAuC;IACtD;AACF;AAKO,MAAM,sBAAsB,CAAC,OAAe,mBAA2B,GAAG;IAC/E,IAAI;QACF,IAAI,CAAC,OAAO,OAAO;QAEnB,MAAM,eAAe,YAAY;QACjC,IAAI,CAAC,gBAAgB,CAAC,aAAa,GAAG,EAAE,OAAO;QAE/C,MAAM,cAAc,KAAK,KAAK,CAAC,KAAK,GAAG,KAAK;QAE5C,qEAAqE;QACrE,OAAO,aAAa,GAAG,GAAG,cAAc;IAC1C,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,oCAAoC;QAClD,OAAO;IACT;AACF;AAKO,MAAM,cAAc;IACzB;;GAEC,GACD,MAAM,OAAM,WAA6B;QACvC,IAAI;YACF,MAAM,WAAW,MAAM,0GAAA,CAAA,MAAG,CAAC,IAAI,CAAyB,cAAc;YAEtE,6BAA6B;YAC7B,MAAM,WAAW,SAAS,IAAI,IAAI;YAElC,uCAAuC;YACvC,aAAa,OAAO,CAAC,eAAe,SAAS,YAAY;YACzD,aAAa,OAAO,CAAC,gBAAgB,SAAS,aAAa;YAE3D,qDAAqD;YACrD,SAAS,MAAM,GAAG,CAAC,MAAM,EAAE,SAAS,YAAY,CAAC,kBAAkB,EAAE,KAAK,KAAK,KAAK,EAAE,cAAc,CAAC,EAAE,SAAS;YAEhH,yEAAyE;YACzE,IAAI,CAAC,SAAS,IAAI,IAAI,SAAS,YAAY,EAAE;gBAC3C,MAAM,cAAc,YAAY,SAAS,YAAY;gBACrD,IAAI,aAAa;oBACf,SAAS,IAAI,GAAG;oBAChB,IAAI,CAAC,QAAQ,CAAC;gBAChB;YACF,OAAO,IAAI,SAAS,IAAI,EAAE;gBACxB,2BAA2B;gBAC3B,IAAI,CAAC,QAAQ,CAAC,SAAS,IAAI;YAC7B;YAEA,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,gBAAgB;YAC9B,MAAM;QACR;IACF;IAEA;;GAEC,GACD,MAAM,UAAS,WAAgC;QAC7C,IAAI;YACF,MAAM,WAAW,MAAM,0GAAA,CAAA,MAAG,CAAC,IAAI,CAAyB,iBAAiB;YAEzE,6BAA6B;YAC7B,MAAM,WAAW,SAAS,IAAI,IAAI;YAElC,uCAAuC;YACvC,aAAa,OAAO,CAAC,eAAe,SAAS,YAAY;YACzD,aAAa,OAAO,CAAC,gBAAgB,SAAS,aAAa;YAE3D,qDAAqD;YACrD,SAAS,MAAM,GAAG,CAAC,MAAM,EAAE,SAAS,YAAY,CAAC,kBAAkB,EAAE,KAAK,KAAK,KAAK,EAAE,cAAc,CAAC,EAAE,SAAS;YAEhH,yEAAyE;YACzE,IAAI,CAAC,SAAS,IAAI,IAAI,SAAS,YAAY,EAAE;gBAC3C,MAAM,cAAc,YAAY,SAAS,YAAY;gBACrD,IAAI,aAAa;oBACf,SAAS,IAAI,GAAG;oBAChB,IAAI,CAAC,QAAQ,CAAC;gBAChB;YACF,OAAO,IAAI,SAAS,IAAI,EAAE;gBACxB,2BAA2B;gBAC3B,IAAI,CAAC,QAAQ,CAAC,SAAS,IAAI;YAC7B;YAEA,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,mBAAmB;YACjC,MAAM;QACR;IACF;IAEA;;GAEC,GACD,MAAM,cAAa,YAAoB;QACrC,IAAI;YACF,MAAM,WAAW,MAAM,0GAAA,CAAA,MAAG,CAAC,IAAI,CAAyB,sBAAsB;gBAAE;YAAa;YAE7F,6BAA6B;YAC7B,MAAM,WAAW,SAAS,IAAI,IAAI;YAElC,8CAA8C;YAC9C,aAAa,OAAO,CAAC,eAAe,SAAS,YAAY;YACzD,aAAa,OAAO,CAAC,gBAAgB,SAAS,aAAa;YAE3D,4DAA4D;YAC5D,SAAS,MAAM,GAAG,CAAC,MAAM,EAAE,SAAS,YAAY,CAAC,kBAAkB,EAAE,KAAK,KAAK,KAAK,EAAE,cAAc,CAAC,EAAE,SAAS;YAEhH,yEAAyE;YACzE,IAAI,CAAC,SAAS,IAAI,IAAI,SAAS,YAAY,EAAE;gBAC3C,MAAM,cAAc,YAAY,SAAS,YAAY;gBACrD,IAAI,aAAa;oBACf,SAAS,IAAI,GAAG;oBAChB,IAAI,CAAC,QAAQ,CAAC;gBAChB;YACF,OAAO,IAAI,SAAS,IAAI,EAAE;gBACxB,2BAA2B;gBAC3B,IAAI,CAAC,QAAQ,CAAC,SAAS,IAAI;YAC7B;YAEA,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,wBAAwB;YACtC,mDAAmD;YACnD,IAAI,CAAC,MAAM;YACX,MAAM;QACR;IACF;IAEA;;GAEC,GACD,MAAM;QACJ,IAAI;YACF,MAAM,eAAe,IAAI,CAAC,eAAe;YACzC,IAAI,CAAC,cAAc,OAAO;YAE1B,MAAM,SAAS,MAAM,IAAI,CAAC,YAAY,CAAC;YAEvC,+CAA+C;YAC/C,IAAI,UAAU,OAAO,IAAI,EAAE;gBACzB,iEAAiE;gBACjE,OAAO,aAAa,CAAC,IAAI,YAAY,oBAAoB;oBACvD,QAAQ;wBAAE,MAAM,OAAO,IAAI;oBAAC;gBAC9B;YACF;YAEA,kEAAkE;YAClE,iDAAiD;YAEjD,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4BAA4B;YAC1C,OAAO;QACT;IACF;IAEA;;;;GAIC,GACD,qBAAoB,IAAU,EAAE,gBAAyB,KAAK;QAC5D,wCAAmC;;QAEnC,+CAA+C;QAC/C,MAAM;QACN,MAAM;IA+BR;IAEA;;GAEC,GACD;QACE,aAAa,UAAU,CAAC;QACxB,aAAa,UAAU,CAAC;QACxB,aAAa,UAAU,CAAC;QAExB,mBAAmB;QACnB,SAAS,MAAM,GAAG;QAElB,sDAAsD;QACtD,uCAAmC;;QAInC;IACF;IAEA;;GAEC,GACD;QACE,wCAAmC,OAAO;;QAE1C,gDAAgD;QAChD,MAAM;QAgBN,8BAA8B;QAC9B,MAAM;QACD,IAAI;IAiCX;IAEA;;GAEC,GACD;QACE,wCAAmC,OAAO;;QAE1C,MAAM;IASR;IAEA;;GAEC,GACD,UAAS,IAAU;QACjB,aAAa,OAAO,CAAC,QAAQ,KAAK,SAAS,CAAC;IAC9C;IAEA;;GAEC,GACD;QACE,wCAAmC,OAAO;;IAE5C;IAEA;;GAEC,GACD;QACE,wCAAmC,OAAO;;IAE5C;IAEA;;;;;GAKC,GACD,MAAM,mBAAkB,MAAc,EAAE,aAAqB;QAC3D,IAAI;YACF,MAAM,WAAW,MAAM,0GAAA,CAAA,MAAG,CAAC,IAAI,CAAgB,4BAA4B;gBACzE;gBACA;YACF;YAEA,OAAO,SAAS,IAAI,IAAI;QAC1B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6BAA6B;YAC3C,MAAM;QACR;IACF;IAEA;;;;GAIC,GACD,MAAM,WAAU,gBAMf;QACC,IAAI;YACF,MAAM,WAAW,MAAM,0GAAA,CAAA,MAAG,CAAC,IAAI,CAAyB,mBAAmB;YAE3E,6BAA6B;YAC7B,MAAM,WAAW,SAAS,IAAI,IAAI;YAElC,uCAAuC;YACvC,aAAa,OAAO,CAAC,eAAe,SAAS,YAAY;YACzD,aAAa,OAAO,CAAC,gBAAgB,SAAS,aAAa;YAE3D,qDAAqD;YACrD,SAAS,MAAM,GAAG,CAAC,MAAM,EAAE,SAAS,YAAY,CAAC,kBAAkB,EAAE,KAAK,KAAK,KAAK,EAAE,cAAc,CAAC,EAAE,SAAS;YAEhH,sCAAsC;YACtC,IAAI,SAAS,IAAI,EAAE;gBACjB,aAAa,OAAO,CAAC,QAAQ,KAAK,SAAS,CAAC,SAAS,IAAI;YAC3D;YAEA,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2BAA2B;YACzC,MAAM;QACR;IACF;AACF", "debugId": null}}, {"offset": {"line": 1406, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/bloomgoo/gold-exchange-platform/frontend/hooks/use-auth.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React, { createContext, useContext, useState, useEffect, ReactNode, useRef } from \"react\";\r\nimport { authService, User as ApiUser, LoginCredentials, RegisterCredentials, decodeToken, isTokenExpired, isTokenExpiringSoon } from \"@/lib/auth\";\r\n\r\n// Định nghĩa kiểu dữ liệu cho User\r\nexport interface User {\r\n  id: string;\r\n  name: string;\r\n  email: string;\r\n  roles: string[];\r\n  avatar?: string;\r\n  fullName?: string;\r\n  username?: string;\r\n  phone?: string;\r\n  address?: string;\r\n}\r\n\r\n// Định nghĩa kiểu dữ liệu cho AuthContext\r\ninterface AuthContextType {\r\n  user: User | null;\r\n  isLoading: boolean;\r\n  login: (credentials: LoginCredentials) => Promise<void>;\r\n  logout: () => void;\r\n  register: (credentials: RegisterCredentials) => Promise<void>;\r\n  hasRole: (roleName: string) => boolean;\r\n  isAdmin: () => boolean;\r\n  isAgent: () => boolean;\r\n  autoLogin: () => Promise<boolean>;\r\n}\r\n\r\n// Tạo AuthContext\r\nconst AuthContext = createContext<AuthContextType | undefined>(undefined);\r\n\r\n// Provider component\r\nexport const AuthProvider = ({ children }: { children: ReactNode }) => {\r\n  const [user, setUser] = useState<User | null>(null);\r\n  const [isLoading, setIsLoading] = useState(true);\r\n\r\n  // Hàm tự động đăng nhập với token\r\n  const autoLogin = async (): Promise<boolean> => {\r\n    try {\r\n      // Kiểm tra xác thực từ localStorage hoặc cookie\r\n      if (authService.isAuthenticated()) {\r\n        // Thử lấy thông tin người dùng từ localStorage\r\n        let currentUser = authService.getCurrentUser();\r\n\r\n        // Nếu không có thông tin người dùng trong localStorage, thử giải mã token\r\n        if (!currentUser) {\r\n          const accessToken = authService.getAccessToken();\r\n          if (accessToken) {\r\n            currentUser = decodeToken(accessToken);\r\n            if (currentUser) {\r\n              authService.saveUser(currentUser);\r\n            }\r\n          }\r\n        }\r\n\r\n        if (currentUser) {\r\n          // Chuyển đổi từ ApiUser sang User\r\n          const userData: User = {\r\n            id: currentUser.id,\r\n            name: currentUser.fullName || currentUser.username || '',\r\n            email: currentUser.email,\r\n            roles: currentUser.roles || [],\r\n            fullName: currentUser.fullName,\r\n            username: currentUser.username,\r\n            phone: currentUser.phone,\r\n            address: currentUser.address,\r\n          };\r\n          setUser(userData);\r\n          return true;\r\n        }\r\n      }\r\n      return false;\r\n    } catch (error) {\r\n      console.error(\"Error in auto login:\", error);\r\n      return false;\r\n    } finally {\r\n      setIsLoading(false);\r\n    }\r\n  };\r\n\r\n  // Tham chiếu đến interval để có thể xóa khi component unmount\r\n  const tokenCheckIntervalRef = useRef<NodeJS.Timeout | null>(null);\r\n\r\n  // Hàm kiểm tra token định kỳ\r\n  const setupTokenCheck = () => {\r\n    // Xóa interval cũ nếu có\r\n    if (tokenCheckIntervalRef.current) {\r\n      clearInterval(tokenCheckIntervalRef.current);\r\n    }\r\n\r\n    // Tạo interval mới để kiểm tra token mỗi phút\r\n    tokenCheckIntervalRef.current = setInterval(() => {\r\n      const token = authService.getAccessToken();\r\n      if (token) {\r\n        // Nếu token đã hết hạn, thử refresh token\r\n        if (isTokenExpired(token)) {\r\n           \r\n          authService.tryRefreshToken().catch(() => {\r\n            // Nếu refresh token thất bại, đăng xuất người dùng\r\n            logout();\r\n          });\r\n        }\r\n        // Nếu token sắp hết hạn, thử refresh token\r\n        else if (isTokenExpiringSoon(token)) {\r\n           \r\n          authService.tryRefreshToken().catch(error => {\r\n            console.error('Error refreshing token:', error);\r\n          });\r\n        }\r\n      }\r\n    }, 60000); // Kiểm tra mỗi phút\r\n  };\r\n\r\n  // Kiểm tra xem người dùng đã đăng nhập chưa khi component được mount\r\n  useEffect(() => {\r\n    const checkAuth = async () => {\r\n      try {\r\n        const success = await autoLogin();\r\n        if (success) {\r\n          // Nếu đăng nhập thành công, thiết lập kiểm tra token định kỳ\r\n          setupTokenCheck();\r\n        }\r\n      } catch (error) {\r\n        console.error(\"Error checking auth:\", error);\r\n      } finally {\r\n        setIsLoading(false);\r\n      }\r\n    };\r\n\r\n    // Lắng nghe user update events từ refresh token\r\n    const handleUserUpdate = (event: CustomEvent) => {\r\n      const updatedUser = event.detail.user;\r\n      if (updatedUser) {\r\n        const userData: User = {\r\n          id: updatedUser.id,\r\n          name: updatedUser.fullName || updatedUser.username || '',\r\n          email: updatedUser.email,\r\n          roles: updatedUser.roles || [],\r\n          fullName: updatedUser.fullName,\r\n          username: updatedUser.username,\r\n          phone: updatedUser.phone,\r\n          address: updatedUser.address,\r\n        };\r\n        setUser(userData);\r\n      }\r\n    };\r\n\r\n    window.addEventListener('auth:userUpdated', handleUserUpdate as EventListener);\r\n    checkAuth();\r\n\r\n    // Cleanup khi component unmount\r\n    return () => {\r\n      if (tokenCheckIntervalRef.current) {\r\n        clearInterval(tokenCheckIntervalRef.current);\r\n      }\r\n      window.removeEventListener('auth:userUpdated', handleUserUpdate as EventListener);\r\n    };\r\n  }, []);\r\n\r\n  // Hàm kiểm tra xem người dùng có role cụ thể không\r\n  const hasRole = (roleName: string): boolean => {\r\n    if (!user || !user.roles) return false;\r\n    return user.roles.includes(roleName);\r\n  };\r\n\r\n  // Hàm kiểm tra xem người dùng có phải là admin không\r\n  const isAdmin = (): boolean => {\r\n    return hasRole(\"ADMIN\");\r\n  };\r\n\r\n  // Hàm kiểm tra xem người dùng có phải là agent không\r\n  const isAgent = (): boolean => {\r\n    return hasRole(\"AGENT\");\r\n  };\r\n\r\n  // Hàm đăng nhập\r\n  const login = async (credentials: LoginCredentials) => {\r\n    setIsLoading(true);\r\n    try {\r\n       \r\n\r\n      // Gọi API đăng nhập thực tế\r\n      const response = await authService.login(credentials);\r\n       \r\n\r\n      // Lấy thông tin người dùng từ response hoặc từ token\r\n      let userData: User | null = null;\r\n\r\n      if (response.user) {\r\n        // Chuyển đổi từ ApiUser sang User\r\n        userData = {\r\n          id: response.user.id,\r\n          name: response.user.fullName || response.user.username || '',\r\n          email: response.user.email,\r\n          roles: response.user.roles || [],\r\n          fullName: response.user.fullName,\r\n          username: response.user.username,\r\n          phone: response.user.phone,\r\n          address: response.user.address,\r\n        };\r\n      } else if (response.access_token) {\r\n        // Nếu không có thông tin người dùng trong response, giải mã token\r\n        const decodedUser = decodeToken(response.access_token);\r\n        if (decodedUser) {\r\n          userData = {\r\n            id: decodedUser.id,\r\n            name: decodedUser.fullName || decodedUser.username || '',\r\n            email: decodedUser.email,\r\n            roles: decodedUser.roles || [],\r\n            fullName: decodedUser.fullName,\r\n            username: decodedUser.username,\r\n            phone: decodedUser.phone,\r\n            address: decodedUser.address,\r\n          };\r\n        }\r\n      }\r\n\r\n      if (userData) {\r\n         \r\n        setUser(userData);\r\n\r\n        // Thiết lập kiểm tra token định kỳ sau khi đăng nhập thành công\r\n        setupTokenCheck();\r\n      }\r\n    } catch (error) {\r\n      console.error(\"Login error:\", error);\r\n      throw error;\r\n    } finally {\r\n      setIsLoading(false);\r\n    }\r\n  };\r\n\r\n  // Hàm đăng xuất\r\n  const logout = () => {\r\n    // Xóa interval kiểm tra token\r\n    if (tokenCheckIntervalRef.current) {\r\n      clearInterval(tokenCheckIntervalRef.current);\r\n      tokenCheckIntervalRef.current = null;\r\n    }\r\n\r\n    // Set loading state để tránh UI flicker\r\n    setIsLoading(true);\r\n\r\n    // Xóa user state\r\n    setUser(null);\r\n\r\n    // Sau đó mới gọi authService.logout() (sẽ redirect)\r\n    authService.logout();\r\n  };\r\n\r\n  // Hàm đăng ký\r\n  const register = async (credentials: RegisterCredentials) => {\r\n    setIsLoading(true);\r\n    try {\r\n       \r\n\r\n      // Gọi API đăng ký thực tế\r\n      const response = await authService.register(credentials);\r\n       \r\n\r\n      // Lấy thông tin người dùng từ response hoặc từ token\r\n      let userData: User | null = null;\r\n\r\n      if (response.user) {\r\n        // Chuyển đổi từ ApiUser sang User\r\n        userData = {\r\n          id: response.user.id,\r\n          name: response.user.fullName || response.user.username || '',\r\n          email: response.user.email,\r\n          roles: response.user.roles || [],\r\n          fullName: response.user.fullName,\r\n          username: response.user.username,\r\n          phone: response.user.phone,\r\n          address: response.user.address,\r\n        };\r\n      } else if (response.access_token) {\r\n        // Nếu không có thông tin người dùng trong response, giải mã token\r\n        const decodedUser = decodeToken(response.access_token);\r\n        if (decodedUser) {\r\n          userData = {\r\n            id: decodedUser.id,\r\n            name: decodedUser.fullName || decodedUser.username || '',\r\n            email: decodedUser.email,\r\n            roles: decodedUser.roles || [],\r\n            fullName: decodedUser.fullName,\r\n            username: decodedUser.username,\r\n            phone: decodedUser.phone,\r\n            address: decodedUser.address,\r\n          };\r\n        }\r\n      }\r\n\r\n      if (userData) {\r\n         \r\n        setUser(userData);\r\n\r\n        // Thiết lập kiểm tra token định kỳ sau khi đăng ký thành công\r\n        setupTokenCheck();\r\n      }\r\n    } catch (error) {\r\n      console.error(\"Register error:\", error);\r\n      throw error;\r\n    } finally {\r\n      setIsLoading(false);\r\n    }\r\n  };\r\n\r\n  const value = {\r\n    user,\r\n    isLoading,\r\n    login,\r\n    logout,\r\n    register,\r\n    hasRole,\r\n    isAdmin,\r\n    isAgent,\r\n    autoLogin,\r\n  };\r\n\r\n  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;\r\n}\r\n\r\n// Hook để sử dụng AuthContext\r\nexport function useAuth() {\r\n  const context = useContext(AuthContext);\r\n  if (context === undefined) {\r\n    throw new Error(\"useAuth must be used within an AuthProvider\");\r\n  }\r\n  return context;\r\n}\r\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AAHA;;;;AA+BA,kBAAkB;AAClB,MAAM,4BAAc,CAAA,GAAA,8SAAA,CAAA,gBAAa,AAAD,EAA+B;AAGxD,MAAM,eAAe,CAAC,EAAE,QAAQ,EAA2B;IAChE,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,8SAAA,CAAA,WAAQ,AAAD,EAAe;IAC9C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,8SAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,kCAAkC;IAClC,MAAM,YAAY;QAChB,IAAI;YACF,gDAAgD;YAChD,IAAI,2GAAA,CAAA,cAAW,CAAC,eAAe,IAAI;gBACjC,+CAA+C;gBAC/C,IAAI,cAAc,2GAAA,CAAA,cAAW,CAAC,cAAc;gBAE5C,0EAA0E;gBAC1E,IAAI,CAAC,aAAa;oBAChB,MAAM,cAAc,2GAAA,CAAA,cAAW,CAAC,cAAc;oBAC9C,IAAI,aAAa;wBACf,cAAc,CAAA,GAAA,2GAAA,CAAA,cAAW,AAAD,EAAE;wBAC1B,IAAI,aAAa;4BACf,2GAAA,CAAA,cAAW,CAAC,QAAQ,CAAC;wBACvB;oBACF;gBACF;gBAEA,IAAI,aAAa;oBACf,kCAAkC;oBAClC,MAAM,WAAiB;wBACrB,IAAI,YAAY,EAAE;wBAClB,MAAM,YAAY,QAAQ,IAAI,YAAY,QAAQ,IAAI;wBACtD,OAAO,YAAY,KAAK;wBACxB,OAAO,YAAY,KAAK,IAAI,EAAE;wBAC9B,UAAU,YAAY,QAAQ;wBAC9B,UAAU,YAAY,QAAQ;wBAC9B,OAAO,YAAY,KAAK;wBACxB,SAAS,YAAY,OAAO;oBAC9B;oBACA,QAAQ;oBACR,OAAO;gBACT;YACF;YACA,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,wBAAwB;YACtC,OAAO;QACT,SAAU;YACR,aAAa;QACf;IACF;IAEA,8DAA8D;IAC9D,MAAM,wBAAwB,CAAA,GAAA,8SAAA,CAAA,SAAM,AAAD,EAAyB;IAE5D,6BAA6B;IAC7B,MAAM,kBAAkB;QACtB,yBAAyB;QACzB,IAAI,sBAAsB,OAAO,EAAE;YACjC,cAAc,sBAAsB,OAAO;QAC7C;QAEA,8CAA8C;QAC9C,sBAAsB,OAAO,GAAG,YAAY;YAC1C,MAAM,QAAQ,2GAAA,CAAA,cAAW,CAAC,cAAc;YACxC,IAAI,OAAO;gBACT,0CAA0C;gBAC1C,IAAI,CAAA,GAAA,2GAAA,CAAA,iBAAc,AAAD,EAAE,QAAQ;oBAEzB,2GAAA,CAAA,cAAW,CAAC,eAAe,GAAG,KAAK,CAAC;wBAClC,mDAAmD;wBACnD;oBACF;gBACF,OAEK,IAAI,CAAA,GAAA,2GAAA,CAAA,sBAAmB,AAAD,EAAE,QAAQ;oBAEnC,2GAAA,CAAA,cAAW,CAAC,eAAe,GAAG,KAAK,CAAC,CAAA;wBAClC,QAAQ,KAAK,CAAC,2BAA2B;oBAC3C;gBACF;YACF;QACF,GAAG,QAAQ,oBAAoB;IACjC;IAEA,qEAAqE;IACrE,CAAA,GAAA,8SAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,YAAY;YAChB,IAAI;gBACF,MAAM,UAAU,MAAM;gBACtB,IAAI,SAAS;oBACX,6DAA6D;oBAC7D;gBACF;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,wBAAwB;YACxC,SAAU;gBACR,aAAa;YACf;QACF;QAEA,gDAAgD;QAChD,MAAM,mBAAmB,CAAC;YACxB,MAAM,cAAc,MAAM,MAAM,CAAC,IAAI;YACrC,IAAI,aAAa;gBACf,MAAM,WAAiB;oBACrB,IAAI,YAAY,EAAE;oBAClB,MAAM,YAAY,QAAQ,IAAI,YAAY,QAAQ,IAAI;oBACtD,OAAO,YAAY,KAAK;oBACxB,OAAO,YAAY,KAAK,IAAI,EAAE;oBAC9B,UAAU,YAAY,QAAQ;oBAC9B,UAAU,YAAY,QAAQ;oBAC9B,OAAO,YAAY,KAAK;oBACxB,SAAS,YAAY,OAAO;gBAC9B;gBACA,QAAQ;YACV;QACF;QAEA,OAAO,gBAAgB,CAAC,oBAAoB;QAC5C;QAEA,gCAAgC;QAChC,OAAO;YACL,IAAI,sBAAsB,OAAO,EAAE;gBACjC,cAAc,sBAAsB,OAAO;YAC7C;YACA,OAAO,mBAAmB,CAAC,oBAAoB;QACjD;IACF,GAAG,EAAE;IAEL,mDAAmD;IACnD,MAAM,UAAU,CAAC;QACf,IAAI,CAAC,QAAQ,CAAC,KAAK,KAAK,EAAE,OAAO;QACjC,OAAO,KAAK,KAAK,CAAC,QAAQ,CAAC;IAC7B;IAEA,qDAAqD;IACrD,MAAM,UAAU;QACd,OAAO,QAAQ;IACjB;IAEA,qDAAqD;IACrD,MAAM,UAAU;QACd,OAAO,QAAQ;IACjB;IAEA,gBAAgB;IAChB,MAAM,QAAQ,OAAO;QACnB,aAAa;QACb,IAAI;YAGF,4BAA4B;YAC5B,MAAM,WAAW,MAAM,2GAAA,CAAA,cAAW,CAAC,KAAK,CAAC;YAGzC,qDAAqD;YACrD,IAAI,WAAwB;YAE5B,IAAI,SAAS,IAAI,EAAE;gBACjB,kCAAkC;gBAClC,WAAW;oBACT,IAAI,SAAS,IAAI,CAAC,EAAE;oBACpB,MAAM,SAAS,IAAI,CAAC,QAAQ,IAAI,SAAS,IAAI,CAAC,QAAQ,IAAI;oBAC1D,OAAO,SAAS,IAAI,CAAC,KAAK;oBAC1B,OAAO,SAAS,IAAI,CAAC,KAAK,IAAI,EAAE;oBAChC,UAAU,SAAS,IAAI,CAAC,QAAQ;oBAChC,UAAU,SAAS,IAAI,CAAC,QAAQ;oBAChC,OAAO,SAAS,IAAI,CAAC,KAAK;oBAC1B,SAAS,SAAS,IAAI,CAAC,OAAO;gBAChC;YACF,OAAO,IAAI,SAAS,YAAY,EAAE;gBAChC,kEAAkE;gBAClE,MAAM,cAAc,CAAA,GAAA,2GAAA,CAAA,cAAW,AAAD,EAAE,SAAS,YAAY;gBACrD,IAAI,aAAa;oBACf,WAAW;wBACT,IAAI,YAAY,EAAE;wBAClB,MAAM,YAAY,QAAQ,IAAI,YAAY,QAAQ,IAAI;wBACtD,OAAO,YAAY,KAAK;wBACxB,OAAO,YAAY,KAAK,IAAI,EAAE;wBAC9B,UAAU,YAAY,QAAQ;wBAC9B,UAAU,YAAY,QAAQ;wBAC9B,OAAO,YAAY,KAAK;wBACxB,SAAS,YAAY,OAAO;oBAC9B;gBACF;YACF;YAEA,IAAI,UAAU;gBAEZ,QAAQ;gBAER,gEAAgE;gBAChE;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,gBAAgB;YAC9B,MAAM;QACR,SAAU;YACR,aAAa;QACf;IACF;IAEA,gBAAgB;IAChB,MAAM,SAAS;QACb,8BAA8B;QAC9B,IAAI,sBAAsB,OAAO,EAAE;YACjC,cAAc,sBAAsB,OAAO;YAC3C,sBAAsB,OAAO,GAAG;QAClC;QAEA,wCAAwC;QACxC,aAAa;QAEb,iBAAiB;QACjB,QAAQ;QAER,oDAAoD;QACpD,2GAAA,CAAA,cAAW,CAAC,MAAM;IACpB;IAEA,cAAc;IACd,MAAM,WAAW,OAAO;QACtB,aAAa;QACb,IAAI;YAGF,0BAA0B;YAC1B,MAAM,WAAW,MAAM,2GAAA,CAAA,cAAW,CAAC,QAAQ,CAAC;YAG5C,qDAAqD;YACrD,IAAI,WAAwB;YAE5B,IAAI,SAAS,IAAI,EAAE;gBACjB,kCAAkC;gBAClC,WAAW;oBACT,IAAI,SAAS,IAAI,CAAC,EAAE;oBACpB,MAAM,SAAS,IAAI,CAAC,QAAQ,IAAI,SAAS,IAAI,CAAC,QAAQ,IAAI;oBAC1D,OAAO,SAAS,IAAI,CAAC,KAAK;oBAC1B,OAAO,SAAS,IAAI,CAAC,KAAK,IAAI,EAAE;oBAChC,UAAU,SAAS,IAAI,CAAC,QAAQ;oBAChC,UAAU,SAAS,IAAI,CAAC,QAAQ;oBAChC,OAAO,SAAS,IAAI,CAAC,KAAK;oBAC1B,SAAS,SAAS,IAAI,CAAC,OAAO;gBAChC;YACF,OAAO,IAAI,SAAS,YAAY,EAAE;gBAChC,kEAAkE;gBAClE,MAAM,cAAc,CAAA,GAAA,2GAAA,CAAA,cAAW,AAAD,EAAE,SAAS,YAAY;gBACrD,IAAI,aAAa;oBACf,WAAW;wBACT,IAAI,YAAY,EAAE;wBAClB,MAAM,YAAY,QAAQ,IAAI,YAAY,QAAQ,IAAI;wBACtD,OAAO,YAAY,KAAK;wBACxB,OAAO,YAAY,KAAK,IAAI,EAAE;wBAC9B,UAAU,YAAY,QAAQ;wBAC9B,UAAU,YAAY,QAAQ;wBAC9B,OAAO,YAAY,KAAK;wBACxB,SAAS,YAAY,OAAO;oBAC9B;gBACF;YACF;YAEA,IAAI,UAAU;gBAEZ,QAAQ;gBAER,8DAA8D;gBAC9D;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,mBAAmB;YACjC,MAAM;QACR,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,QAAQ;QACZ;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;IACF;IAEA,qBAAO,uVAAC,YAAY,QAAQ;QAAC,OAAO;kBAAQ;;;;;;AAC9C;AAGO,SAAS;IACd,MAAM,UAAU,CAAA,GAAA,8SAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,YAAY,WAAW;QACzB,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT", "debugId": null}}, {"offset": {"line": 1687, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/bloomgoo/gold-exchange-platform/frontend/lib/local-storage-utils.ts"], "sourcesContent": ["/**\r\n * Check if cached data is still valid\r\n */\r\nexport function isCacheValid(cache: any): boolean {\r\n    if (!cache) return false;\r\n\r\n    const now = Date.now();\r\n    return now < cache.expiresAt;\r\n}\r\n\r\n/**\r\n* Clear cache\r\n*/\r\nexport function clearCacheByKey(cacheKey: string): void {\r\n    if (typeof window !== 'undefined') {\r\n        localStorage.removeItem(cacheKey);\r\n    }\r\n}\r\n\r\n/**\r\n * Clear all cache\r\n */\r\nexport function clearAllCache(): void {\r\n    if (typeof window !== 'undefined') {\r\n        localStorage.clear();\r\n    }\r\n}\r\n\r\n/**\r\n * Save cache to localStorage\r\n */\r\nexport function saveCacheToStorage(cacheKey: string, cache: any): void {\r\n    if (typeof window === 'undefined' || !cache) return;\r\n\r\n    try {\r\n        localStorage.setItem(cacheKey, JSON.stringify(cache));\r\n    } catch (error) {\r\n        console.error('Failed to save metadata cache:', error);\r\n    }\r\n}\r\n\r\n/**\r\n * Load cache from localStorage\r\n */\r\nexport function loadCacheFromStorage(cacheKey: string): void {\r\n    if (typeof window === 'undefined') return;\r\n\r\n    try {\r\n        const cached = localStorage.getItem(cacheKey);\r\n        if (cached) {\r\n            return JSON.parse(cached);\r\n        }\r\n    } catch (error) {\r\n        console.error('Failed to load metadata cache:', error);\r\n    }\r\n}\r\n\r\nexport function loadAllCache(): any[] {\r\n    if (typeof window === 'undefined') return [];\r\n    const cacheKeys = Object.keys(localStorage);\r\n    const cached: any[] = [];\r\n\r\n    for (const key of cacheKeys) {\r\n        try {\r\n            const value = localStorage.getItem(key);\r\n            if (value) {\r\n                // Kiểm tra nếu giá trị bắt đầu bằng { hoặc [ thì mới parse JSON\r\n                if (value.startsWith('{') || value.startsWith('[')) {\r\n                    const parsedValue = JSON.parse(value);\r\n                    cached.push(parsedValue);\r\n                }\r\n            }\r\n        } catch (error) {\r\n            // Bỏ qua các giá trị không phải JSON\r\n            console.debug(`Skipping non-JSON value for key: ${key}`);\r\n        }\r\n    }\r\n\r\n    return cached;\r\n}\r\n"], "names": [], "mappings": "AAAA;;CAEC;;;;;;;;AACM,SAAS,aAAa,KAAU;IACnC,IAAI,CAAC,OAAO,OAAO;IAEnB,MAAM,MAAM,KAAK,GAAG;IACpB,OAAO,MAAM,MAAM,SAAS;AAChC;AAKO,SAAS,gBAAgB,QAAgB;IAC5C,uCAAmC;;IAEnC;AACJ;AAKO,SAAS;IACZ,uCAAmC;;IAEnC;AACJ;AAKO,SAAS,mBAAmB,QAAgB,EAAE,KAAU;IAC3D,wCAA6C;;AAOjD;AAKO,SAAS,qBAAqB,QAAgB;IACjD,wCAAmC;;AAUvC;AAEO,SAAS;IACZ,wCAAmC,OAAO,EAAE;;IAC5C,MAAM;IACN,MAAM;IAED,MAAM;AAiBf", "debugId": null}}, {"offset": {"line": 1733, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/bloomgoo/gold-exchange-platform/frontend/lib/system-metadata.ts"], "sourcesContent": ["/**\r\n * Site Metadata Service\r\n * Handles fetching, caching, and managing site metadata from system config API\r\n */\r\n\r\nimport { api } from '@/lib/api';\r\nimport { SystemConfigResponse } from '@/types/site-metadata';\r\nimport { clearAllCache, loadAllCache, saveCacheToStorage } from './local-storage-utils';\r\n\r\nconst CACHE_KEY = 'site_metadata_cache';\r\nconst DEFAULT_TTL = 24 * 60 * 60 * 1000; // 24 hours in milliseconds\r\n\r\nexport class SiteMetadataService {\r\n  private static instance: SiteMetadataService;\r\n  private cache: {\r\n    data: SystemConfigResponse;\r\n    cachedAt: number;\r\n    expiresAt: number;\r\n  } | null = null;\r\n\r\n  private constructor() {\r\n    loadAllCache();\r\n  }\r\n\r\n  public static getInstance(): SiteMetadataService {\r\n    if (!SiteMetadataService.instance) {\r\n      SiteMetadataService.instance = new SiteMetadataService();\r\n    }\r\n    return SiteMetadataService.instance;\r\n  }\r\n\r\n\r\n\r\n\r\n\r\n\r\n  /**\r\n   * Fetch system config from backend API\r\n   */\r\n  private async fetchSystemConfigFromAPI(): Promise<SystemConfigResponse> {\r\n    // Only fetch configs for SEO, header, and footer groups\r\n    return await api.get<SystemConfigResponse>(\r\n      '/public/system-configs?limit=300&filter=configGroup:website_seo|general',\r\n      {\r\n        cache: \"no-cache\",\r\n      }\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Get metadata from system config\r\n   */\r\n  public async getMetadata(): Promise<SystemConfigResponse> {\r\n    // // Check cache first\r\n    // if (this.isCacheValid() && this.cache) {\r\n    //   return this.cache.data;\r\n    // }\r\n\r\n    try {\r\n      // Always fetch from API first\r\n      const systemConfig = await this.fetchSystemConfigFromAPI();\r\n\r\n      // Cache the result\r\n      const now = Date.now();\r\n      this.cache = {\r\n        data: systemConfig,\r\n        cachedAt: now,\r\n        expiresAt: now + DEFAULT_TTL,\r\n      };\r\n\r\n      // Save to localStorage\r\n      saveCacheToStorage(CACHE_KEY, this.cache);\r\n\r\n      return systemConfig;\r\n    } catch (error) {\r\n      console.error('Failed to fetch system config:', error);\r\n\r\n      // If API call fails, return cached data if available\r\n      if (this.cache) {\r\n        return this.cache.data;\r\n      }\r\n\r\n      return {};\r\n    }\r\n  }\r\n\r\n\r\n  /**\r\n   * Refresh metadata\r\n   */\r\n  public async refreshMetadata(): Promise<SystemConfigResponse> {\r\n    clearAllCache();\r\n    return this.getMetadata();\r\n  }\r\n\r\n  /**\r\n   * Get metadata from system config by group\r\n   */\r\n  public async getMetadataByGroup(configGroup: string): Promise<SystemConfigResponse> {\r\n    const CACHE_KEY_GROUP = `site_metadata_cache_${configGroup}`;\r\n\r\n    // Check cache first\r\n    // if (this.isCacheValid() && this.cache) {\r\n    //   return this.cache.data;\r\n    // }\r\n\r\n    try {\r\n      // Always fetch from API first\r\n      const systemConfig = await api.get<SystemConfigResponse>(\r\n        `/public/system-configs?limit=100&filter=configGroup:${configGroup}`\r\n      );\r\n\r\n      // Cache the result\r\n      const now = Date.now();\r\n      const cacheObj = {\r\n        data: systemConfig,\r\n        cachedAt: now,\r\n        expiresAt: now + DEFAULT_TTL,\r\n      };\r\n\r\n      // Save to localStorage\r\n      saveCacheToStorage(CACHE_KEY_GROUP, cacheObj);\r\n\r\n      return systemConfig;\r\n    } catch (error) {\r\n\r\n      // If API call fails, try to get from cache\r\n      if (typeof window !== 'undefined') {\r\n        const cached = localStorage.getItem(CACHE_KEY_GROUP);\r\n        if (cached) {\r\n          return JSON.parse(cached).data;\r\n        }\r\n      }\r\n\r\n      return {};\r\n    }\r\n  }\r\n}\r\n\r\n// Export singleton instance\r\nexport const siteMetadataService = SiteMetadataService.getInstance();\r\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;AAED;AAEA;;;AAEA,MAAM,YAAY;AAClB,MAAM,cAAc,KAAK,KAAK,KAAK,MAAM,2BAA2B;AAE7D,MAAM;IACX,OAAe,SAA8B;IACrC,QAIG,KAAK;IAEhB,aAAsB;QACpB,CAAA,GAAA,gIAAA,CAAA,eAAY,AAAD;IACb;IAEA,OAAc,cAAmC;QAC/C,IAAI,CAAC,oBAAoB,QAAQ,EAAE;YACjC,oBAAoB,QAAQ,GAAG,IAAI;QACrC;QACA,OAAO,oBAAoB,QAAQ;IACrC;IAOA;;GAEC,GACD,MAAc,2BAA0D;QACtE,wDAAwD;QACxD,OAAO,MAAM,0GAAA,CAAA,MAAG,CAAC,GAAG,CAClB,2EACA;YACE,OAAO;QACT;IAEJ;IAEA;;GAEC,GACD,MAAa,cAA6C;QACxD,uBAAuB;QACvB,2CAA2C;QAC3C,4BAA4B;QAC5B,IAAI;QAEJ,IAAI;YACF,8BAA8B;YAC9B,MAAM,eAAe,MAAM,IAAI,CAAC,wBAAwB;YAExD,mBAAmB;YACnB,MAAM,MAAM,KAAK,GAAG;YACpB,IAAI,CAAC,KAAK,GAAG;gBACX,MAAM;gBACN,UAAU;gBACV,WAAW,MAAM;YACnB;YAEA,uBAAuB;YACvB,CAAA,GAAA,gIAAA,CAAA,qBAAkB,AAAD,EAAE,WAAW,IAAI,CAAC,KAAK;YAExC,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,kCAAkC;YAEhD,qDAAqD;YACrD,IAAI,IAAI,CAAC,KAAK,EAAE;gBACd,OAAO,IAAI,CAAC,KAAK,CAAC,IAAI;YACxB;YAEA,OAAO,CAAC;QACV;IACF;IAGA;;GAEC,GACD,MAAa,kBAAiD;QAC5D,CAAA,GAAA,gIAAA,CAAA,gBAAa,AAAD;QACZ,OAAO,IAAI,CAAC,WAAW;IACzB;IAEA;;GAEC,GACD,MAAa,mBAAmB,WAAmB,EAAiC;QAClF,MAAM,kBAAkB,CAAC,oBAAoB,EAAE,aAAa;QAE5D,oBAAoB;QACpB,2CAA2C;QAC3C,4BAA4B;QAC5B,IAAI;QAEJ,IAAI;YACF,8BAA8B;YAC9B,MAAM,eAAe,MAAM,0GAAA,CAAA,MAAG,CAAC,GAAG,CAChC,CAAC,oDAAoD,EAAE,aAAa;YAGtE,mBAAmB;YACnB,MAAM,MAAM,KAAK,GAAG;YACpB,MAAM,WAAW;gBACf,MAAM;gBACN,UAAU;gBACV,WAAW,MAAM;YACnB;YAEA,uBAAuB;YACvB,CAAA,GAAA,gIAAA,CAAA,qBAAkB,AAAD,EAAE,iBAAiB;YAEpC,OAAO;QACT,EAAE,OAAO,OAAO;YAEd,2CAA2C;YAC3C,uCAAmC;;YAKnC;YAEA,OAAO,CAAC;QACV;IACF;AACF;AAGO,MAAM,sBAAsB,oBAAoB,WAAW", "debugId": null}}, {"offset": {"line": 1838, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/bloomgoo/gold-exchange-platform/frontend/lib/utils.ts"], "sourcesContent": ["import LexoRank from '@kayron013/lexorank'\r\nimport { clsx, type ClassValue } from \"clsx\"\r\nimport { format, isValid } from \"date-fns\"\r\nimport { vi } from \"date-fns/locale\"\r\nimport { twMerge } from \"tailwind-merge\"\r\n\r\nexport type OrderType = 'buy' | 'sell';\r\nexport type BusinessType = 'NORMAL' | 'IMMEDIATE_DELIVERY';\r\n\r\nexport interface ProductItem {\r\n  productId: string;\r\n  volume: number;\r\n  productInfo?: {\r\n    weight: number;\r\n    [key: string]: any;\r\n  };\r\n  [key: string]: any;\r\n}\r\n\r\nexport function cn(...inputs: ClassValue[]) {\r\n  return twMerge(clsx(inputs))\r\n}\r\n\r\n/**\r\n * Format a number as currency in VND (đồng bộ với backend)\r\n * @param amount The amount to format\r\n * @param options Additional formatting options\r\n * @returns Formatted currency string\r\n */\r\nexport function formatCurrency(\r\n  amount: number | undefined | null,\r\n  options?: Intl.NumberFormatOptions\r\n) {\r\n  if (amount === undefined || amount === null) return \"---\"\r\n  return new Intl.NumberFormat('vi-VN', {\r\n    minimumFractionDigits: 0,\r\n    maximumFractionDigits: 0,\r\n    ...options\r\n  }).format(amount)\r\n}\r\n\r\n/**\r\n * Format number with thousand separators (đồng bộ với backend)\r\n * @param value Number to format\r\n * @param decimalPlaces Number of decimal places\r\n * @returns Formatted number string\r\n */\r\nexport function formatNumber(value: number | undefined | null, decimalPlaces: number = 0): string {\r\n  if (value === undefined || value === null) return \"---\"\r\n  return new Intl.NumberFormat('vi-VN', {\r\n    minimumFractionDigits: decimalPlaces,\r\n    maximumFractionDigits: decimalPlaces,\r\n  }).format(value)\r\n}\r\n\r\n/**\r\n * Format percentage (đồng bộ với backend)\r\n * @param value Value (0.15 = 15%)\r\n * @param decimalPlaces Number of decimal places\r\n * @returns Formatted percentage string\r\n */\r\nexport function formatPercentage(value: number | undefined | null, decimalPlaces: number = 2): string {\r\n  if (value === undefined || value === null) return \"---\"\r\n  return new Intl.NumberFormat('vi-VN', {\r\n    style: 'percent',\r\n    minimumFractionDigits: decimalPlaces,\r\n    maximumFractionDigits: decimalPlaces,\r\n  }).format(value)\r\n}\r\n\r\n/**\r\n * Tính giá quy đổi đơn giản từ USD sang VND\r\n * @param usdPrice Giá USD\r\n * @param exchangeRate Tỷ giá USD/VND (mặc định: 27000)\r\n * @returns Giá quy đổi sang VND\r\n */\r\nexport function calculateExchangePrice(usdPrice: number, exchangeRate: number = 27000) {\r\n  return Math.round(usdPrice * exchangeRate);\r\n}\r\n\r\n/**\r\n * Format giá quy đổi từ USD sang VND\r\n * @param usdPrice Giá USD\r\n * @param exchangeRate Tỷ giá USD/VND (mặc định: 27000)\r\n * @returns Giá quy đổi sang VND đã định dạng\r\n */\r\nexport function formatExchangePrice(usdPrice: number | undefined | null, exchangeRate: number = 27000) {\r\n  if (usdPrice === undefined || usdPrice === null) return \"---\";\r\n\r\n  const vndPrice = calculateExchangePrice(usdPrice, exchangeRate);\r\n\r\n  return new Intl.NumberFormat('vi-VN', {\r\n    style: 'currency',\r\n    currency: 'VND',\r\n    maximumFractionDigits: 0\r\n  }).format(vndPrice);\r\n}\r\n\r\n/**\r\n * Tính giá product cho bạc ký quỹ online và bán\r\n * @param priceVND Giá bạc (VND) từ websocket\r\n * @param oz Trọng lượng bạc (oz)\r\n * @param quantity Số lượng product (mặc định là 1)\r\n * @returns Giá product đã tính toán (VND)\r\n */\r\nexport function calculateNormalProductPrice(priceVND: number, oz: number = 1, quantity: number = 1) {\r\n  if (isNaN(priceVND) || priceVND <= 0 || isNaN(oz) || oz <= 0) {\r\n    return 0;\r\n  }\r\n  return Math.round((priceVND * oz) * quantity);\r\n}\r\n\r\n/**\r\n * Tính giá product cho bạc giao ngay\r\n * @param priceVND Giá bạc (VND) từ websocket\r\n * @param oz Trọng lượng bạc (oz)\r\n * @param quantity Số lượng product (mặc định là 1)\r\n * @returns Giá product đã tính toán (VND)\r\n */\r\nexport function calculateDeliveryProductPrice(priceVND: number, oz: number = 1, quantity: number = 1) {\r\n  if (isNaN(priceVND) || priceVND <= 0 || isNaN(oz) || oz <= 0) {\r\n    return 0;\r\n  }\r\n  // Giá = (giá * oz * số lượng) + phí gia công\r\n  // Phí gia công = (oz * số lượng) * 50,000\r\n  // Ví dụ: sản phẩm 3oz, số lượng 2\r\n  // - Giá = 761,000 * 3 * 2 = 4,566,000\r\n  // - Phí gia công = (3 * 2) * 50,000 = 300,000\r\n  // - Tổng = 4,566,000 + 300,000 = 4,866,000\r\n  const totalOz = oz * quantity;\r\n  return Math.round((priceVND * oz * quantity) + calculateProcessingFee(totalOz));\r\n}\r\n\r\n/**\r\n * Tính phí gia công cho bạc giao ngay\r\n * @param oz Tổng trọng lượng bạc (oz) = weight * volume\r\n * @returns Phí gia công (VND)\r\n */\r\nexport function calculateProcessingFee(oz: number = 1) {\r\n  if (isNaN(oz) || oz <= 0) {\r\n    return 0;\r\n  }\r\n  // Phí gia công = tổng oz * 50,000\r\n  return Math.round(oz * 50000);\r\n}\r\n\r\n/**\r\n * Format giá token dựa trên giá VND từ websocket\r\n * @param priceVND Giá bạc (VND) từ websocket\r\n * @param oz Trọng lượng bạc (oz)\r\n * @param volume Số lượng token (mặc định là 1)\r\n * @param isDelivery Có phải là bạc giao ngay không\r\n * @returns Giá token đã định dạng (VND)\r\n */\r\nexport function formatTokenPrice(priceVND: number | undefined | null, oz: number = 1, volume: number = 1, isDelivery: boolean = false) {\r\n  if (priceVND === undefined || priceVND === null) return \"---\";\r\n\r\n  const price = isDelivery \r\n    ? calculateDeliveryProductPrice(priceVND, oz, volume)\r\n    : calculateNormalProductPrice(priceVND, oz, volume);\r\n\r\n  return formatCurrency(price);\r\n}\r\n\r\n/**\r\n * Tính tổng giá trị của các product items\r\n * @param items Danh sách các product items\r\n * @param priceVND Giá bạc (VND) từ websocket\r\n * @param orderType Loại lệnh ('buy' hoặc 'sell')\r\n * @param businessType Loại hình giao dịch (chỉ áp dụng cho lệnh mua)\r\n * @returns Tổng giá trị (VND)\r\n */\r\nexport function calculateTotal(\r\n  items: ProductItem[], \r\n  priceVND: number, \r\n  orderType: OrderType,\r\n  businessType?: BusinessType\r\n): number {\r\n  // Kiểm tra input\r\n  if (!items?.length || priceVND <= 0) return 0;\r\n\r\n  return items.reduce((total, item) => {\r\n    // Bỏ qua nếu không có thông tin sản phẩm hoặc số lượng\r\n    if (!item.productId || !item.volume || !item.productInfo?.weight) return total;\r\n\r\n    const weight = item.productInfo.weight;\r\n    const volume = item.volume;\r\n\r\n    // Xử lý theo loại lệnh\r\n    if (orderType === 'buy') {\r\n      // Lệnh mua: phân biệt theo businessType\r\n      if (businessType === 'IMMEDIATE_DELIVERY') {\r\n        // Bạc giao ngay: giá = (giá * oz * số lượng) + phí gia công\r\n        return total + calculateDeliveryProductPrice(priceVND, weight, volume);\r\n      } else {\r\n        // Bạc ký quỹ online: giá = giá * oz * số lượng\r\n        return total + calculateNormalProductPrice(priceVND, weight, volume);\r\n      }\r\n    } else {\r\n      // Lệnh bán: luôn tính như bạc ký quỹ\r\n      return total + calculateNormalProductPrice(priceVND, weight, volume);\r\n    }\r\n  }, 0);\r\n}\r\n\r\n/**\r\n * Tính tiền đặt cọc (10% tổng giá trị)\r\n * @param items Danh sách các product items\r\n * @param priceVND Giá bạc (VND) từ websocket\r\n * @param orderType Loại lệnh ('buy' hoặc 'sell')\r\n * @param businessType Loại hình giao dịch (chỉ áp dụng cho lệnh mua)\r\n * @returns Số tiền đặt cọc (VND)\r\n */\r\nexport function calculateDeposit(\r\n  items: ProductItem[], \r\n  priceVND: number, \r\n  orderType: OrderType,\r\n  businessType?: BusinessType\r\n): number {\r\n  const total = calculateTotal(items, priceVND, orderType, businessType);\r\n  return total * 0.1; // 10% tổng giá trị\r\n}\r\n\r\n/**\r\n * Tính tiền tất toán (90% tổng giá trị)\r\n * @param items Danh sách các token items\r\n * @param worldPrice Giá bạc thế giới (USD/oz)\r\n * @returns Số tiền tất toán (VND)\r\n */\r\nexport function calculateSettlement(items: any[], worldPrice: number): number {\r\n  return calculateTotal(items, worldPrice) * 0.9;\r\n}\r\n\r\n/**\r\n * Định dạng ngày giờ theo chuẩn Việt Nam (dd/MM/yyyy HH:mm)\r\n * @param date Đối tượng Date, chuỗi ISO hoặc timestamp\r\n * @param emptyValue Giá trị trả về khi date là null hoặc undefined\r\n * @returns Chuỗi ngày giờ đã định dạng\r\n */\r\nexport function formatDateTime(date: Date | string | number | null | undefined, emptyValue: string = \"---\"): string {\r\n  if (date === null || date === undefined) return emptyValue;\r\n\r\n  const dateObj = typeof date === 'string' || typeof date === 'number'\r\n    ? new Date(date)\r\n    : date;\r\n\r\n  if (!isValid(dateObj)) return \"Không hợp lệ\";\r\n\r\n  try {\r\n    return format(dateObj, \"dd/MM/yyyy HH:mm\", { locale: vi });\r\n  } catch (error) {\r\n    return \"Không hợp lệ\";\r\n  }\r\n}\r\n\r\n/**\r\n * Định dạng chỉ ngày theo chuẩn Việt Nam (dd/MM/yyyy)\r\n * @param date Đối tượng Date, chuỗi ISO hoặc timestamp\r\n * @param emptyValue Giá trị trả về khi date là null hoặc undefined\r\n * @returns Chuỗi ngày đã định dạng\r\n */\r\nexport function formatDate(date: Date | string | number | null | undefined, emptyValue: string = \"---\"): string {\r\n  if (date === null || date === undefined) return emptyValue;\r\n\r\n  const dateObj = typeof date === 'string' || typeof date === 'number'\r\n    ? new Date(date)\r\n    : date;\r\n\r\n  if (!isValid(dateObj)) return \"Không hợp lệ\";\r\n\r\n  try {\r\n    return format(dateObj, \"dd/MM/yyyy\", { locale: vi });\r\n  } catch (error) {\r\n    return \"Không hợp lệ\";\r\n  }\r\n}\r\n\r\n/**\r\n * Định dạng chỉ thời gian theo chuẩn Việt Nam (HH:mm:ss)\r\n * @param date Đối tượng Date, chuỗi ISO hoặc timestamp\r\n * @param emptyValue Giá trị trả về khi date là null hoặc undefined\r\n * @returns Chuỗi thời gian đã định dạng\r\n */\r\nexport function formatTime(date: Date | string | number | null | undefined, emptyValue: string = \"---\"): string {\r\n  if (date === null || date === undefined) return emptyValue;\r\n\r\n  const dateObj = typeof date === 'string' || typeof date === 'number'\r\n    ? new Date(date)\r\n    : date;\r\n\r\n  if (!isValid(dateObj)) return \"Không hợp lệ\";\r\n\r\n  try {\r\n    return format(dateObj, \"HH:mm:ss\", { locale: vi });\r\n  } catch (error) {\r\n    return \"Không hợp lệ\";\r\n  }\r\n}\r\n\r\n/**\r\n * Định dạng ngày giờ đầy đủ theo chuẩn Việt Nam (dd/MM/yyyy HH:mm:ss)\r\n * @param date Đối tượng Date, chuỗi ISO hoặc timestamp\r\n * @param emptyValue Giá trị trả về khi date là null hoặc undefined\r\n * @returns Chuỗi ngày giờ đầy đủ đã định dạng\r\n */\r\nexport function formatFullDateTime(date: Date | string | number | null | undefined, emptyValue: string = \"---\"): string {\r\n  if (date === null || date === undefined) return emptyValue;\r\n\r\n  const dateObj = typeof date === 'string' || typeof date === 'number'\r\n    ? new Date(date)\r\n    : date;\r\n\r\n  if (!isValid(dateObj)) return \"Không hợp lệ\";\r\n\r\n  try {\r\n    return format(dateObj, \"dd/MM/yyyy HH:mm:ss\", { locale: vi });\r\n  } catch (error) {\r\n    return \"Không hợp lệ\";\r\n  }\r\n}\r\n\r\nexport function censorPhone(phone: string): string {\r\n  if (!phone || phone.length < 4) return phone;\r\n  const visiblePart = phone.slice(-3);\r\n  const hiddenPart = \"*\".repeat(Math.max(0, phone.length - 3));\r\n  return hiddenPart + visiblePart;\r\n}\r\n\r\nexport function censorEmail(email: string): string {\r\n  if (!email || !email.includes('@')) return email;\r\n  const [localPart, domain] = email.split('@');\r\n  if (localPart.length <= 2) return email;\r\n\r\n  const visibleStart = localPart.slice(0, 2);\r\n  const visibleEnd = localPart.slice(-1);\r\n  const hiddenPart = \"*\".repeat(Math.max(0, localPart.length - 3));\r\n\r\n  return `${visibleStart}${hiddenPart}${visibleEnd}@${domain}`;\r\n};\r\n\r\n// JSON parsing utility\r\nexport function safeParseJSON<T>(jsonString: string | null | undefined, fallback: T): T {\r\n  if (!jsonString) return fallback;\r\n\r\n  try {\r\n    return JSON.parse(jsonString) as T;\r\n  } catch (error) {\r\n    console.warn('Failed to parse JSON:', error);\r\n    return fallback;\r\n  }\r\n}\r\n\r\n\r\nexport { LexoRank }\r\n\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;AAAA;AACA;AACA;AAAA;AACA;AACA;;;;;;AAeO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,yNAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,sLAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAQO,SAAS,eACd,MAAiC,EACjC,OAAkC;IAElC,IAAI,WAAW,aAAa,WAAW,MAAM,OAAO;IACpD,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;QACpC,uBAAuB;QACvB,uBAAuB;QACvB,GAAG,OAAO;IACZ,GAAG,MAAM,CAAC;AACZ;AAQO,SAAS,aAAa,KAAgC,EAAE,gBAAwB,CAAC;IACtF,IAAI,UAAU,aAAa,UAAU,MAAM,OAAO;IAClD,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;QACpC,uBAAuB;QACvB,uBAAuB;IACzB,GAAG,MAAM,CAAC;AACZ;AAQO,SAAS,iBAAiB,KAAgC,EAAE,gBAAwB,CAAC;IAC1F,IAAI,UAAU,aAAa,UAAU,MAAM,OAAO;IAClD,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;QACpC,OAAO;QACP,uBAAuB;QACvB,uBAAuB;IACzB,GAAG,MAAM,CAAC;AACZ;AAQO,SAAS,uBAAuB,QAAgB,EAAE,eAAuB,KAAK;IACnF,OAAO,KAAK,KAAK,CAAC,WAAW;AAC/B;AAQO,SAAS,oBAAoB,QAAmC,EAAE,eAAuB,KAAK;IACnG,IAAI,aAAa,aAAa,aAAa,MAAM,OAAO;IAExD,MAAM,WAAW,uBAAuB,UAAU;IAElD,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;QACpC,OAAO;QACP,UAAU;QACV,uBAAuB;IACzB,GAAG,MAAM,CAAC;AACZ;AASO,SAAS,4BAA4B,QAAgB,EAAE,KAAa,CAAC,EAAE,WAAmB,CAAC;IAChG,IAAI,MAAM,aAAa,YAAY,KAAK,MAAM,OAAO,MAAM,GAAG;QAC5D,OAAO;IACT;IACA,OAAO,KAAK,KAAK,CAAC,AAAC,WAAW,KAAM;AACtC;AASO,SAAS,8BAA8B,QAAgB,EAAE,KAAa,CAAC,EAAE,WAAmB,CAAC;IAClG,IAAI,MAAM,aAAa,YAAY,KAAK,MAAM,OAAO,MAAM,GAAG;QAC5D,OAAO;IACT;IACA,6CAA6C;IAC7C,0CAA0C;IAC1C,kCAAkC;IAClC,sCAAsC;IACtC,8CAA8C;IAC9C,2CAA2C;IAC3C,MAAM,UAAU,KAAK;IACrB,OAAO,KAAK,KAAK,CAAC,AAAC,WAAW,KAAK,WAAY,uBAAuB;AACxE;AAOO,SAAS,uBAAuB,KAAa,CAAC;IACnD,IAAI,MAAM,OAAO,MAAM,GAAG;QACxB,OAAO;IACT;IACA,kCAAkC;IAClC,OAAO,KAAK,KAAK,CAAC,KAAK;AACzB;AAUO,SAAS,iBAAiB,QAAmC,EAAE,KAAa,CAAC,EAAE,SAAiB,CAAC,EAAE,aAAsB,KAAK;IACnI,IAAI,aAAa,aAAa,aAAa,MAAM,OAAO;IAExD,MAAM,QAAQ,aACV,8BAA8B,UAAU,IAAI,UAC5C,4BAA4B,UAAU,IAAI;IAE9C,OAAO,eAAe;AACxB;AAUO,SAAS,eACd,KAAoB,EACpB,QAAgB,EAChB,SAAoB,EACpB,YAA2B;IAE3B,iBAAiB;IACjB,IAAI,CAAC,OAAO,UAAU,YAAY,GAAG,OAAO;IAE5C,OAAO,MAAM,MAAM,CAAC,CAAC,OAAO;QAC1B,uDAAuD;QACvD,IAAI,CAAC,KAAK,SAAS,IAAI,CAAC,KAAK,MAAM,IAAI,CAAC,KAAK,WAAW,EAAE,QAAQ,OAAO;QAEzE,MAAM,SAAS,KAAK,WAAW,CAAC,MAAM;QACtC,MAAM,SAAS,KAAK,MAAM;QAE1B,uBAAuB;QACvB,IAAI,cAAc,OAAO;YACvB,wCAAwC;YACxC,IAAI,iBAAiB,sBAAsB;gBACzC,4DAA4D;gBAC5D,OAAO,QAAQ,8BAA8B,UAAU,QAAQ;YACjE,OAAO;gBACL,+CAA+C;gBAC/C,OAAO,QAAQ,4BAA4B,UAAU,QAAQ;YAC/D;QACF,OAAO;YACL,qCAAqC;YACrC,OAAO,QAAQ,4BAA4B,UAAU,QAAQ;QAC/D;IACF,GAAG;AACL;AAUO,SAAS,iBACd,KAAoB,EACpB,QAAgB,EAChB,SAAoB,EACpB,YAA2B;IAE3B,MAAM,QAAQ,eAAe,OAAO,UAAU,WAAW;IACzD,OAAO,QAAQ,KAAK,mBAAmB;AACzC;AAQO,SAAS,oBAAoB,KAAY,EAAE,UAAkB;IAClE,OAAO,eAAe,OAAO,cAAc;AAC7C;AAQO,SAAS,eAAe,IAA+C,EAAE,aAAqB,KAAK;IACxG,IAAI,SAAS,QAAQ,SAAS,WAAW,OAAO;IAEhD,MAAM,UAAU,OAAO,SAAS,YAAY,OAAO,SAAS,WACxD,IAAI,KAAK,QACT;IAEJ,IAAI,CAAC,CAAA,GAAA,8LAAA,CAAA,UAAO,AAAD,EAAE,UAAU,OAAO;IAE9B,IAAI;QACF,OAAO,CAAA,GAAA,6MAAA,CAAA,SAAM,AAAD,EAAE,SAAS,oBAAoB;YAAE,QAAQ,mMAAA,CAAA,KAAE;QAAC;IAC1D,EAAE,OAAO,OAAO;QACd,OAAO;IACT;AACF;AAQO,SAAS,WAAW,IAA+C,EAAE,aAAqB,KAAK;IACpG,IAAI,SAAS,QAAQ,SAAS,WAAW,OAAO;IAEhD,MAAM,UAAU,OAAO,SAAS,YAAY,OAAO,SAAS,WACxD,IAAI,KAAK,QACT;IAEJ,IAAI,CAAC,CAAA,GAAA,8LAAA,CAAA,UAAO,AAAD,EAAE,UAAU,OAAO;IAE9B,IAAI;QACF,OAAO,CAAA,GAAA,6MAAA,CAAA,SAAM,AAAD,EAAE,SAAS,cAAc;YAAE,QAAQ,mMAAA,CAAA,KAAE;QAAC;IACpD,EAAE,OAAO,OAAO;QACd,OAAO;IACT;AACF;AAQO,SAAS,WAAW,IAA+C,EAAE,aAAqB,KAAK;IACpG,IAAI,SAAS,QAAQ,SAAS,WAAW,OAAO;IAEhD,MAAM,UAAU,OAAO,SAAS,YAAY,OAAO,SAAS,WACxD,IAAI,KAAK,QACT;IAEJ,IAAI,CAAC,CAAA,GAAA,8LAAA,CAAA,UAAO,AAAD,EAAE,UAAU,OAAO;IAE9B,IAAI;QACF,OAAO,CAAA,GAAA,6MAAA,CAAA,SAAM,AAAD,EAAE,SAAS,YAAY;YAAE,QAAQ,mMAAA,CAAA,KAAE;QAAC;IAClD,EAAE,OAAO,OAAO;QACd,OAAO;IACT;AACF;AAQO,SAAS,mBAAmB,IAA+C,EAAE,aAAqB,KAAK;IAC5G,IAAI,SAAS,QAAQ,SAAS,WAAW,OAAO;IAEhD,MAAM,UAAU,OAAO,SAAS,YAAY,OAAO,SAAS,WACxD,IAAI,KAAK,QACT;IAEJ,IAAI,CAAC,CAAA,GAAA,8LAAA,CAAA,UAAO,AAAD,EAAE,UAAU,OAAO;IAE9B,IAAI;QACF,OAAO,CAAA,GAAA,6MAAA,CAAA,SAAM,AAAD,EAAE,SAAS,uBAAuB;YAAE,QAAQ,mMAAA,CAAA,KAAE;QAAC;IAC7D,EAAE,OAAO,OAAO;QACd,OAAO;IACT;AACF;AAEO,SAAS,YAAY,KAAa;IACvC,IAAI,CAAC,SAAS,MAAM,MAAM,GAAG,GAAG,OAAO;IACvC,MAAM,cAAc,MAAM,KAAK,CAAC,CAAC;IACjC,MAAM,aAAa,IAAI,MAAM,CAAC,KAAK,GAAG,CAAC,GAAG,MAAM,MAAM,GAAG;IACzD,OAAO,aAAa;AACtB;AAEO,SAAS,YAAY,KAAa;IACvC,IAAI,CAAC,SAAS,CAAC,MAAM,QAAQ,CAAC,MAAM,OAAO;IAC3C,MAAM,CAAC,WAAW,OAAO,GAAG,MAAM,KAAK,CAAC;IACxC,IAAI,UAAU,MAAM,IAAI,GAAG,OAAO;IAElC,MAAM,eAAe,UAAU,KAAK,CAAC,GAAG;IACxC,MAAM,aAAa,UAAU,KAAK,CAAC,CAAC;IACpC,MAAM,aAAa,IAAI,MAAM,CAAC,KAAK,GAAG,CAAC,GAAG,UAAU,MAAM,GAAG;IAE7D,OAAO,GAAG,eAAe,aAAa,WAAW,CAAC,EAAE,QAAQ;AAC9D;AAGO,SAAS,cAAiB,UAAqC,EAAE,QAAW;IACjF,IAAI,CAAC,YAAY,OAAO;IAExB,IAAI;QACF,OAAO,KAAK,KAAK,CAAC;IACpB,EAAE,OAAO,OAAO;QACd,QAAQ,IAAI,CAAC,yBAAyB;QACtC,OAAO;IACT;AACF", "debugId": null}}, {"offset": {"line": 2060, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/bloomgoo/gold-exchange-platform/frontend/hooks/use-site-metadata.ts"], "sourcesContent": ["/**\r\n * React Hook for Site Metadata\r\n */\r\n\r\nimport { clearAllCache } from '@/lib/local-storage-utils';\r\nimport { siteMetadataService } from '@/lib/system-metadata';\r\nimport { safeParseJSON } from '@/lib/utils';\r\nimport { SystemConfigResponse } from '@/types/site-metadata';\r\nimport { useCallback, useEffect, useState } from 'react';\r\n\r\ninterface UseSiteMetadataReturn {\r\n  config: SystemConfigResponse | null;\r\n  loading: boolean;\r\n  error: string | null;\r\n  refresh: () => Promise<void>;\r\n  clearCache: () => void;\r\n}\r\n\r\nexport function useSiteMetadata(): UseSiteMetadataReturn {\r\n  const [config, setConfig] = useState<SystemConfigResponse | null>(null);\r\n  const [loading, setLoading] = useState(true);\r\n  const [error, setError] = useState<string | null>(null);\r\n\r\n  const loadMetadata = useCallback(async () => {\r\n    try {\r\n      setLoading(true);\r\n      setError(null);\r\n      const data = await siteMetadataService.getMetadata();\r\n      setConfig(data);\r\n    } catch (err) {\r\n      console.error('Failed to load metadata:', err);\r\n      setError(err instanceof Error ? err.message : 'Failed to load metadata');\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  }, []);\r\n\r\n  const refresh = useCallback(async () => {\r\n    try {\r\n      setLoading(true);\r\n      setError(null);\r\n      const data = await siteMetadataService.refreshMetadata();\r\n      setConfig(data);\r\n    } catch (err) {\r\n      setError(err instanceof Error ? err.message : 'Failed to refresh metadata');\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  }, []);\r\n\r\n  const clearCache = useCallback(() => {\r\n    clearAllCache();\r\n  }, []);\r\n\r\n  useEffect(() => {\r\n    loadMetadata();\r\n  }, [loadMetadata]);\r\n\r\n  return {\r\n    config,\r\n    loading,\r\n    error,\r\n    refresh,\r\n    clearCache,\r\n  };\r\n}\r\n\r\n/**\r\n * Hook for getting specific config value\r\n */\r\nexport function useConfigValue(key: string): string | null {\r\n  const { config } = useSiteMetadata();\r\n  return config ? config[key] : null;\r\n}\r\n\r\n/**\r\n * Hook for getting SEO metadata\r\n */\r\nexport function useSEOMetadata() {\r\n  const { config, loading, error } = useSiteMetadata();\r\n\r\n  const seoData = config ? {\r\n    title: config['website_seo_title'] || '',\r\n    description: config['website_seo_description'] || '',\r\n    keywords: safeParseJSON(config['website_seo_keywords'], []),\r\n    author: config['website_seo_author'] || '',\r\n    canonical: config['website_site_url'] || '',\r\n    openGraph: {\r\n      title: config['website_seo_og_title'] || '',\r\n      description: config['website_seo_og_description'] || '',\r\n      image: config['website_seo_og_image'] || '',\r\n      type: config['website_seo_og_type'] || '',\r\n      url: config['website_site_url'] || '',\r\n    },\r\n    twitter: {\r\n      card: config['website_seo_twitter_card'] || '',\r\n      site: config['website_seo_twitter_site'] || '',\r\n      creator: config['website_seo_twitter_creator'] || '',\r\n      image: config['website_seo_twitter_image'] || '',\r\n    },\r\n    icons: {\r\n      favicon: config['website_favicon_url'] || '/favicon.ico',\r\n      appleTouchIcon: config['website_apple_touch_icon'] || '',\r\n      icon192: config['website_icon_192'] || '',\r\n      icon512: config['website_icon_512'] || '',\r\n    },\r\n    manifest: config['website_manifest_url'] || '',\r\n    themeColor: config['website_theme_color'] || '',\r\n  } : null;\r\n\r\n  return {\r\n    seoData,\r\n    loading,\r\n    error,\r\n  };\r\n}\r\n\r\n/**\r\n * Hook for getting dynamic metadata by group\r\n */\r\nexport function useDynamicMetadata(configGroup: string) {\r\n  const [config, setConfig] = useState<SystemConfigResponse | null>(null);\r\n  const [loading, setLoading] = useState(true);\r\n  const [error, setError] = useState<string | null>(null);\r\n\r\n  useEffect(() => {\r\n    const loadData = async () => {\r\n      try {\r\n        setLoading(true);\r\n        setError(null);\r\n        const data = await siteMetadataService.getMetadataByGroup(configGroup);\r\n        setConfig(data);\r\n      } catch (err) {\r\n        console.error(`Failed to load ${configGroup} metadata:`, err);\r\n        setError(err instanceof Error ? err.message : `Failed to load ${configGroup} metadata`);\r\n      } finally {\r\n        setLoading(false);\r\n      }\r\n    };\r\n\r\n    loadData();\r\n  }, [configGroup]);\r\n\r\n  return { config, loading, error };\r\n}\r\n"], "names": [], "mappings": "AAAA;;CAEC;;;;;;AAED;AACA;AACA;AAAA;AAEA;;;;;AAUO,SAAS;IACd,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,8SAAA,CAAA,WAAQ,AAAD,EAA+B;IAClE,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,8SAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,8SAAA,CAAA,WAAQ,AAAD,EAAiB;IAElD,MAAM,eAAe,CAAA,GAAA,8SAAA,CAAA,cAAW,AAAD,EAAE;QAC/B,IAAI;YACF,WAAW;YACX,SAAS;YACT,MAAM,OAAO,MAAM,yHAAA,CAAA,sBAAmB,CAAC,WAAW;YAClD,UAAU;QACZ,EAAE,OAAO,KAAK;YACZ,QAAQ,KAAK,CAAC,4BAA4B;YAC1C,SAAS,eAAe,QAAQ,IAAI,OAAO,GAAG;QAChD,SAAU;YACR,WAAW;QACb;IACF,GAAG,EAAE;IAEL,MAAM,UAAU,CAAA,GAAA,8SAAA,CAAA,cAAW,AAAD,EAAE;QAC1B,IAAI;YACF,WAAW;YACX,SAAS;YACT,MAAM,OAAO,MAAM,yHAAA,CAAA,sBAAmB,CAAC,eAAe;YACtD,UAAU;QACZ,EAAE,OAAO,KAAK;YACZ,SAAS,eAAe,QAAQ,IAAI,OAAO,GAAG;QAChD,SAAU;YACR,WAAW;QACb;IACF,GAAG,EAAE;IAEL,MAAM,aAAa,CAAA,GAAA,8SAAA,CAAA,cAAW,AAAD,EAAE;QAC7B,CAAA,GAAA,gIAAA,CAAA,gBAAa,AAAD;IACd,GAAG,EAAE;IAEL,CAAA,GAAA,8SAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG;QAAC;KAAa;IAEjB,OAAO;QACL;QACA;QACA;QACA;QACA;IACF;AACF;AAKO,SAAS,eAAe,GAAW;IACxC,MAAM,EAAE,MAAM,EAAE,GAAG;IACnB,OAAO,SAAS,MAAM,CAAC,IAAI,GAAG;AAChC;AAKO,SAAS;IACd,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,KAAK,EAAE,GAAG;IAEnC,MAAM,UAAU,SAAS;QACvB,OAAO,MAAM,CAAC,oBAAoB,IAAI;QACtC,aAAa,MAAM,CAAC,0BAA0B,IAAI;QAClD,UAAU,CAAA,GAAA,4HAAA,CAAA,gBAAa,AAAD,EAAE,MAAM,CAAC,uBAAuB,EAAE,EAAE;QAC1D,QAAQ,MAAM,CAAC,qBAAqB,IAAI;QACxC,WAAW,MAAM,CAAC,mBAAmB,IAAI;QACzC,WAAW;YACT,OAAO,MAAM,CAAC,uBAAuB,IAAI;YACzC,aAAa,MAAM,CAAC,6BAA6B,IAAI;YACrD,OAAO,MAAM,CAAC,uBAAuB,IAAI;YACzC,MAAM,MAAM,CAAC,sBAAsB,IAAI;YACvC,KAAK,MAAM,CAAC,mBAAmB,IAAI;QACrC;QACA,SAAS;YACP,MAAM,MAAM,CAAC,2BAA2B,IAAI;YAC5C,MAAM,MAAM,CAAC,2BAA2B,IAAI;YAC5C,SAAS,MAAM,CAAC,8BAA8B,IAAI;YAClD,OAAO,MAAM,CAAC,4BAA4B,IAAI;QAChD;QACA,OAAO;YACL,SAAS,MAAM,CAAC,sBAAsB,IAAI;YAC1C,gBAAgB,MAAM,CAAC,2BAA2B,IAAI;YACtD,SAAS,MAAM,CAAC,mBAAmB,IAAI;YACvC,SAAS,MAAM,CAAC,mBAAmB,IAAI;QACzC;QACA,UAAU,MAAM,CAAC,uBAAuB,IAAI;QAC5C,YAAY,MAAM,CAAC,sBAAsB,IAAI;IAC/C,IAAI;IAEJ,OAAO;QACL;QACA;QACA;IACF;AACF;AAKO,SAAS,mBAAmB,WAAmB;IACpD,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,8SAAA,CAAA,WAAQ,AAAD,EAA+B;IAClE,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,8SAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,8SAAA,CAAA,WAAQ,AAAD,EAAiB;IAElD,CAAA,GAAA,8SAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,WAAW;YACf,IAAI;gBACF,WAAW;gBACX,SAAS;gBACT,MAAM,OAAO,MAAM,yHAAA,CAAA,sBAAmB,CAAC,kBAAkB,CAAC;gBAC1D,UAAU;YACZ,EAAE,OAAO,KAAK;gBACZ,QAAQ,KAAK,CAAC,CAAC,eAAe,EAAE,YAAY,UAAU,CAAC,EAAE;gBACzD,SAAS,eAAe,QAAQ,IAAI,OAAO,GAAG,CAAC,eAAe,EAAE,YAAY,SAAS,CAAC;YACxF,SAAU;gBACR,WAAW;YACb;QACF;QAEA;IACF,GAAG;QAAC;KAAY;IAEhB,OAAO;QAAE;QAAQ;QAAS;IAAM;AAClC", "debugId": null}}, {"offset": {"line": 2196, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/bloomgoo/gold-exchange-platform/frontend/lib/assets.ts"], "sourcesContent": ["/**\r\n * Asset path utilities - simplified without basePath\r\n */\r\n\r\n/**\r\n * Get the correct asset path\r\n * @param path - The asset path (should start with /)\r\n * @returns The normalized asset path\r\n */\r\nexport function getAssetPath(path: string): string {\r\n  // Ensure path starts with /\r\n  return path.startsWith('/') ? path : `/${path}`;\r\n}\r\n\r\n/**\r\n * Create a URL path\r\n * @param path - The path (should start with /)\r\n * @returns The normalized URL path\r\n */\r\nexport function createUrl(path: string): string {\r\n  return path.startsWith('/') ? path : `/${path}`;\r\n}\r\n\r\n/**\r\n * Get image URL for use in components\r\n * @param src - The image source path\r\n * @returns The processed image URL\r\n */\r\nexport function getImageUrl(src: string): string {\r\n  // If it's already a full URL, return as is\r\n  if (src.startsWith('http://') || src.startsWith('https://')) {\r\n    return src;\r\n  }\r\n\r\n  // If it's a data URL, return as is\r\n  if (src.startsWith('data:')) {\r\n    return src;\r\n  }\r\n\r\n  // For relative URLs, just normalize the path\r\n  return getAssetPath(src);\r\n}\r\n\r\n/**\r\n * Get CSS image URL for use in stylesheets\r\n * @param imagePath - The image path\r\n * @returns The CSS url() value\r\n */\r\nexport function getCSSImageUrl(imagePath: string): string {\r\n  return `url(${getImageUrl(imagePath)})`;\r\n}\r\n\r\n\r\n"], "names": [], "mappings": "AAAA;;CAEC,GAED;;;;CAIC;;;;;;AACM,SAAS,aAAa,IAAY;IACvC,4BAA4B;IAC5B,OAAO,KAAK,UAAU,CAAC,OAAO,OAAO,CAAC,CAAC,EAAE,MAAM;AACjD;AAOO,SAAS,UAAU,IAAY;IACpC,OAAO,KAAK,UAAU,CAAC,OAAO,OAAO,CAAC,CAAC,EAAE,MAAM;AACjD;AAOO,SAAS,YAAY,GAAW;IACrC,2CAA2C;IAC3C,IAAI,IAAI,UAAU,CAAC,cAAc,IAAI,UAAU,CAAC,aAAa;QAC3D,OAAO;IACT;IAEA,mCAAmC;IACnC,IAAI,IAAI,UAAU,CAAC,UAAU;QAC3B,OAAO;IACT;IAEA,6CAA6C;IAC7C,OAAO,aAAa;AACtB;AAOO,SAAS,eAAe,SAAiB;IAC9C,OAAO,CAAC,IAAI,EAAE,YAAY,WAAW,CAAC,CAAC;AACzC", "debugId": null}}, {"offset": {"line": 2236, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/bloomgoo/gold-exchange-platform/frontend/components/dynamic-metadata.tsx"], "sourcesContent": ["/**\r\n * Dynamic Metadata Component\r\n * Generates metadata based on current domain and backend data\r\n */\r\n\r\n'use client';\r\n\r\nimport { useEffect } from 'react';\r\nimport { useSEOMetadata } from '@/hooks/use-site-metadata';\r\nimport { getImageUrl } from '@/lib/assets';\r\n\r\ninterface DynamicMetadataProps {\r\n  pageTitle?: string;\r\n  pageDescription?: string;\r\n  pageImage?: string;\r\n  pageType?: string;\r\n}\r\n\r\nexport function DynamicMetadata({\r\n  pageTitle,\r\n  pageDescription,\r\n  pageImage,\r\n  pageType = 'website'\r\n}: DynamicMetadataProps) {\r\n  const { seoData, loading, error } = useSEOMetadata();\r\n\r\n  useEffect(() => {\r\n    if (!seoData || loading) return;\r\n\r\n    // Update document title\r\n    const title = pageTitle ? `${pageTitle} | ${seoData.title}` : seoData.title;\r\n    if (title) {\r\n      document.title = title;\r\n    }\r\n\r\n    // Update meta tags (only if values exist)\r\n    if (pageDescription || seoData.description) {\r\n      updateMetaTag('description', pageDescription || seoData.description);\r\n    }\r\n    if (seoData.keywords.length > 0) {\r\n      updateMetaTag('keywords', seoData.keywords.join(', '));\r\n    }\r\n    if (seoData.author) {\r\n      updateMetaTag('author', seoData.author);\r\n    }\r\n    if (seoData.themeColor) {\r\n      updateMetaTag('theme-color', seoData.themeColor);\r\n    }\r\n\r\n    // Update Open Graph tags (only if values exist)\r\n    const ogTitle = pageTitle || seoData.openGraph.title;\r\n    if (ogTitle) {\r\n      updateMetaTag('og:title', ogTitle, 'property');\r\n    }\r\n    const ogDescription = pageDescription || seoData.openGraph.description;\r\n    if (ogDescription) {\r\n      updateMetaTag('og:description', ogDescription, 'property');\r\n    }\r\n    const ogImage = pageImage || seoData.openGraph.image;\r\n    if (ogImage) {\r\n      updateMetaTag('og:image', getImageUrl(ogImage), 'property');\r\n    }\r\n    if (pageType) {\r\n      updateMetaTag('og:type', pageType, 'property');\r\n    }\r\n    if (seoData.openGraph.url) {\r\n      updateMetaTag('og:url', seoData.openGraph.url, 'property');\r\n    }\r\n\r\n    // Update Twitter Card tags (only if values exist)\r\n    if (seoData.twitter.card) {\r\n      updateMetaTag('twitter:card', seoData.twitter.card, 'name');\r\n    }\r\n    if (seoData.twitter.site) {\r\n      updateMetaTag('twitter:site', seoData.twitter.site, 'name');\r\n    }\r\n    if (seoData.twitter.creator) {\r\n      updateMetaTag('twitter:creator', seoData.twitter.creator, 'name');\r\n    }\r\n    if (ogTitle) {\r\n      updateMetaTag('twitter:title', ogTitle, 'name');\r\n    }\r\n    if (ogDescription) {\r\n      updateMetaTag('twitter:description', ogDescription, 'name');\r\n    }\r\n    const twitterImage = pageImage || seoData.twitter.image;\r\n    if (twitterImage) {\r\n      updateMetaTag('twitter:image', getImageUrl(twitterImage), 'name');\r\n    }\r\n\r\n    // Update canonical link (only if value exists)\r\n    if (seoData.canonical) {\r\n      updateCanonicalLink(seoData.canonical);\r\n    }\r\n\r\n    // Update favicon (only if value exists)\r\n    if (seoData.icons.favicon) {\r\n      updateFavicon(getImageUrl(seoData.icons.favicon));\r\n    }\r\n\r\n    // Update manifest (only if value exists)\r\n    if (seoData.manifest) {\r\n      updateManifest(getImageUrl(seoData.manifest));\r\n    }\r\n\r\n  }, [seoData, loading, pageTitle, pageDescription, pageImage, pageType]);\r\n\r\n  if (error) {\r\n    console.error('Failed to load metadata:', error);\r\n  }\r\n\r\n  return null; // This component doesn't render anything\r\n}\r\n\r\n/**\r\n * Update or create meta tag\r\n */\r\nfunction updateMetaTag(name: string, content: string, attribute: 'name' | 'property' = 'name') {\r\n  let meta = document.querySelector(`meta[${attribute}=\"${name}\"]`) as HTMLMetaElement;\r\n  \r\n  if (!meta) {\r\n    meta = document.createElement('meta');\r\n    meta.setAttribute(attribute, name);\r\n    document.head.appendChild(meta);\r\n  }\r\n  \r\n  meta.content = content;\r\n}\r\n\r\n/**\r\n * Update canonical link\r\n */\r\nfunction updateCanonicalLink(href: string) {\r\n  let link = document.querySelector('link[rel=\"canonical\"]') as HTMLLinkElement;\r\n  \r\n  if (!link) {\r\n    link = document.createElement('link');\r\n    link.rel = 'canonical';\r\n    document.head.appendChild(link);\r\n  }\r\n  \r\n  link.href = href;\r\n}\r\n\r\n/**\r\n * Update favicon\r\n */\r\nfunction updateFavicon(href: string) {\r\n  let link = document.querySelector('link[rel=\"icon\"]') as HTMLLinkElement;\r\n  \r\n  if (!link) {\r\n    link = document.createElement('link');\r\n    link.rel = 'icon';\r\n    document.head.appendChild(link);\r\n  }\r\n  \r\n  link.href = href;\r\n}\r\n\r\n/**\r\n * Update manifest link\r\n */\r\nfunction updateManifest(href: string) {\r\n  let link = document.querySelector('link[rel=\"manifest\"]') as HTMLLinkElement;\r\n  \r\n  if (!link) {\r\n    link = document.createElement('link');\r\n    link.rel = 'manifest';\r\n    document.head.appendChild(link);\r\n  }\r\n  \r\n  link.href = href;\r\n}\r\n"], "names": [], "mappings": "AAAA;;;CAGC;;;AAID;AACA;AACA;AAJA;;;;AAaO,SAAS,gBAAgB,EAC9B,SAAS,EACT,eAAe,EACf,SAAS,EACT,WAAW,SAAS,EACC;IACrB,MAAM,EAAE,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,gIAAA,CAAA,iBAAc,AAAD;IAEjD,CAAA,GAAA,8SAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,WAAW,SAAS;QAEzB,wBAAwB;QACxB,MAAM,QAAQ,YAAY,GAAG,UAAU,GAAG,EAAE,QAAQ,KAAK,EAAE,GAAG,QAAQ,KAAK;QAC3E,IAAI,OAAO;YACT,SAAS,KAAK,GAAG;QACnB;QAEA,0CAA0C;QAC1C,IAAI,mBAAmB,QAAQ,WAAW,EAAE;YAC1C,cAAc,eAAe,mBAAmB,QAAQ,WAAW;QACrE;QACA,IAAI,QAAQ,QAAQ,CAAC,MAAM,GAAG,GAAG;YAC/B,cAAc,YAAY,QAAQ,QAAQ,CAAC,IAAI,CAAC;QAClD;QACA,IAAI,QAAQ,MAAM,EAAE;YAClB,cAAc,UAAU,QAAQ,MAAM;QACxC;QACA,IAAI,QAAQ,UAAU,EAAE;YACtB,cAAc,eAAe,QAAQ,UAAU;QACjD;QAEA,gDAAgD;QAChD,MAAM,UAAU,aAAa,QAAQ,SAAS,CAAC,KAAK;QACpD,IAAI,SAAS;YACX,cAAc,YAAY,SAAS;QACrC;QACA,MAAM,gBAAgB,mBAAmB,QAAQ,SAAS,CAAC,WAAW;QACtE,IAAI,eAAe;YACjB,cAAc,kBAAkB,eAAe;QACjD;QACA,MAAM,UAAU,aAAa,QAAQ,SAAS,CAAC,KAAK;QACpD,IAAI,SAAS;YACX,cAAc,YAAY,CAAA,GAAA,6GAAA,CAAA,cAAW,AAAD,EAAE,UAAU;QAClD;QACA,IAAI,UAAU;YACZ,cAAc,WAAW,UAAU;QACrC;QACA,IAAI,QAAQ,SAAS,CAAC,GAAG,EAAE;YACzB,cAAc,UAAU,QAAQ,SAAS,CAAC,GAAG,EAAE;QACjD;QAEA,kDAAkD;QAClD,IAAI,QAAQ,OAAO,CAAC,IAAI,EAAE;YACxB,cAAc,gBAAgB,QAAQ,OAAO,CAAC,IAAI,EAAE;QACtD;QACA,IAAI,QAAQ,OAAO,CAAC,IAAI,EAAE;YACxB,cAAc,gBAAgB,QAAQ,OAAO,CAAC,IAAI,EAAE;QACtD;QACA,IAAI,QAAQ,OAAO,CAAC,OAAO,EAAE;YAC3B,cAAc,mBAAmB,QAAQ,OAAO,CAAC,OAAO,EAAE;QAC5D;QACA,IAAI,SAAS;YACX,cAAc,iBAAiB,SAAS;QAC1C;QACA,IAAI,eAAe;YACjB,cAAc,uBAAuB,eAAe;QACtD;QACA,MAAM,eAAe,aAAa,QAAQ,OAAO,CAAC,KAAK;QACvD,IAAI,cAAc;YAChB,cAAc,iBAAiB,CAAA,GAAA,6GAAA,CAAA,cAAW,AAAD,EAAE,eAAe;QAC5D;QAEA,+CAA+C;QAC/C,IAAI,QAAQ,SAAS,EAAE;YACrB,oBAAoB,QAAQ,SAAS;QACvC;QAEA,wCAAwC;QACxC,IAAI,QAAQ,KAAK,CAAC,OAAO,EAAE;YACzB,cAAc,CAAA,GAAA,6GAAA,CAAA,cAAW,AAAD,EAAE,QAAQ,KAAK,CAAC,OAAO;QACjD;QAEA,yCAAyC;QACzC,IAAI,QAAQ,QAAQ,EAAE;YACpB,eAAe,CAAA,GAAA,6GAAA,CAAA,cAAW,AAAD,EAAE,QAAQ,QAAQ;QAC7C;IAEF,GAAG;QAAC;QAAS;QAAS;QAAW;QAAiB;QAAW;KAAS;IAEtE,IAAI,OAAO;QACT,QAAQ,KAAK,CAAC,4BAA4B;IAC5C;IAEA,OAAO,MAAM,yCAAyC;AACxD;AAEA;;CAEC,GACD,SAAS,cAAc,IAAY,EAAE,OAAe,EAAE,YAAiC,MAAM;IAC3F,IAAI,OAAO,SAAS,aAAa,CAAC,CAAC,KAAK,EAAE,UAAU,EAAE,EAAE,KAAK,EAAE,CAAC;IAEhE,IAAI,CAAC,MAAM;QACT,OAAO,SAAS,aAAa,CAAC;QAC9B,KAAK,YAAY,CAAC,WAAW;QAC7B,SAAS,IAAI,CAAC,WAAW,CAAC;IAC5B;IAEA,KAAK,OAAO,GAAG;AACjB;AAEA;;CAEC,GACD,SAAS,oBAAoB,IAAY;IACvC,IAAI,OAAO,SAAS,aAAa,CAAC;IAElC,IAAI,CAAC,MAAM;QACT,OAAO,SAAS,aAAa,CAAC;QAC9B,KAAK,GAAG,GAAG;QACX,SAAS,IAAI,CAAC,WAAW,CAAC;IAC5B;IAEA,KAAK,IAAI,GAAG;AACd;AAEA;;CAEC,GACD,SAAS,cAAc,IAAY;IACjC,IAAI,OAAO,SAAS,aAAa,CAAC;IAElC,IAAI,CAAC,MAAM;QACT,OAAO,SAAS,aAAa,CAAC;QAC9B,KAAK,GAAG,GAAG;QACX,SAAS,IAAI,CAAC,WAAW,CAAC;IAC5B;IAEA,KAAK,IAAI,GAAG;AACd;AAEA;;CAEC,GACD,SAAS,eAAe,IAAY;IAClC,IAAI,OAAO,SAAS,aAAa,CAAC;IAElC,IAAI,CAAC,MAAM;QACT,OAAO,SAAS,aAAa,CAAC;QAC9B,KAAK,GAAG,GAAG;QACX,SAAS,IAAI,CAAC,WAAW,CAAC;IAC5B;IAEA,KAAK,IAAI,GAAG;AACd", "debugId": null}}]}
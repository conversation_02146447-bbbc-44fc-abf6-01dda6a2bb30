"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.TradingViewModule = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const axios_1 = require("@nestjs/axios");
const config_1 = require("@nestjs/config");
const jwt_1 = require("@nestjs/jwt");
const tradingview_controller_1 = require("./controllers/tradingview.controller");
const tradingview_api_service_1 = require("./services/tradingview-api.service");
const tradingview_websocket_service_1 = require("./services/tradingview-websocket.service");
const rate_limiter_service_1 = require("./services/rate-limiter.service");
const cache_service_1 = require("./services/cache.service");
const tradingview_gateway_1 = require("./gateways/tradingview.gateway");
const mock_tradingview_service_1 = require("./services/mock-tradingview.service");
const silver_price_entity_1 = require("./entities/silver-price.entity");
const system_config_entity_1 = require("../../system-configs/entities/system-config.entity");
let TradingViewModule = class TradingViewModule {
};
exports.TradingViewModule = TradingViewModule;
exports.TradingViewModule = TradingViewModule = __decorate([
    (0, common_1.Module)({
        imports: [
            typeorm_1.TypeOrmModule.forFeature([silver_price_entity_1.SilverPrice, system_config_entity_1.SystemConfig]),
            axios_1.HttpModule.register({
                timeout: 5000,
                maxRedirects: 5,
            }),
            config_1.ConfigModule,
            jwt_1.JwtModule.registerAsync({
                imports: [config_1.ConfigModule],
                useFactory: async (configService) => ({
                    secret: configService.get('JWT_SECRET'),
                    signOptions: {
                        expiresIn: configService.get('JWT_EXPIRES_IN', '1h'),
                    },
                }),
                inject: [config_1.ConfigService],
            }),
        ],
        controllers: [tradingview_controller_1.TradingViewController],
        providers: [
            {
                provide: tradingview_api_service_1.TradingViewApiService,
                useClass: mock_tradingview_service_1.MockTradingViewService,
            },
            tradingview_websocket_service_1.TradingViewWebsocketService,
            rate_limiter_service_1.RateLimiterService,
            cache_service_1.CacheService,
            tradingview_gateway_1.TradingViewGateway,
            mock_tradingview_service_1.MockTradingViewService,
        ],
        exports: [
            tradingview_api_service_1.TradingViewApiService,
            tradingview_websocket_service_1.TradingViewWebsocketService,
        ],
    })
], TradingViewModule);
//# sourceMappingURL=tradingview.module.js.map
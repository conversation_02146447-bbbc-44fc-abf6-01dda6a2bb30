import { Repository } from 'typeorm';
import { CmsTags } from '../entity/cms-tags.entity';
import { BaseSlugService } from '../../common/services/base-slug.service';
import { UnifiedSlugService } from '../../common/services/unified-slug.service';
export declare class SlugCmsTagsService extends BaseSlugService<CmsTags> {
    protected readonly unifiedSlugService: UnifiedSlugService;
    constructor(tagRepository: Repository<CmsTags>, unifiedSlugService: UnifiedSlugService);
    protected getSlugFieldName(): string;
    protected getTextFieldName(): string;
    protected getWhereConditions(excludeId?: string): any;
    protected generateFallbackSlug(): string;
    generateSlugFromName(name: string): string;
    validateSlug(slug: string): boolean;
    generateUniqueSlugForCreate(name: string, providedSlug?: string): Promise<string>;
    generateUniqueSlugForUpdate(name: string, tagId: string, providedSlug?: string): Promise<string>;
    getExistingSlugs(excludeId?: string): Promise<string[]>;
    isSlugExists(slug: string, excludeId?: string): Promise<boolean>;
}

"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ReadCmsMenusService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const event_emitter_1 = require("@nestjs/event-emitter");
const base_cms_menus_service_1 = require("./base.cms-menus.service");
const cms_menus_entity_1 = require("../entity/cms-menus.entity");
let ReadCmsMenusService = class ReadCmsMenusService extends base_cms_menus_service_1.BaseCmsMenusService {
    menuRepository;
    dataSource;
    eventEmitter;
    constructor(menuRepository, dataSource, eventEmitter) {
        super(menuRepository, dataSource, eventEmitter);
        this.menuRepository = menuRepository;
        this.dataSource = dataSource;
        this.eventEmitter = eventEmitter;
    }
    async findAll(params) {
        try {
            this.logger.debug(`Đang lấy danh sách menu CMS với tham số: ${JSON.stringify(params)}`);
            const { limit, page, search } = params;
            const queryBuilder = this.menuRepository
                .createQueryBuilder('menu')
                .leftJoinAndSelect('menu.parent', 'parent')
                .leftJoinAndSelect('menu.creator', 'creator')
                .leftJoinAndSelect('menu.updater', 'updater')
                .where('menu.isDeleted = :isDeleted', { isDeleted: false });
            if (search) {
                queryBuilder.andWhere(new typeorm_2.Brackets(qb => {
                    qb.where('LOWER(menu.name) LIKE LOWER(:search)', { search: `%${search}%` })
                        .orWhere('LOWER(menu.slug) LIKE LOWER(:search)', { search: `%${search}%` })
                        .orWhere('LOWER(menu.description) LIKE LOWER(:search)', { search: `%${search}%` });
                }));
            }
            const sortOptions = params.sortOptions;
            if (sortOptions && sortOptions.length > 0) {
                const { field, order } = sortOptions[0];
                queryBuilder.orderBy(`menu.${field}`, order);
            }
            else {
                queryBuilder.orderBy('menu.name', 'ASC');
            }
            const skip = (page - 1) * limit;
            queryBuilder.skip(skip).take(limit);
            const [menus, total] = await queryBuilder.getManyAndCount();
            const menuDtos = this.toDtos(menus);
            return { data: menuDtos, total };
        }
        catch (error) {
            this.logger.error(`Lỗi khi lấy danh sách menu CMS: ${error.message}`, error.stack);
            throw new common_1.InternalServerErrorException(`Không thể lấy danh sách menu CMS: ${error.message}`);
        }
    }
    async findOneOrFail(id, relations = []) {
        try {
            this.logger.debug(`Đang lấy thông tin menu CMS với ID: ${id}`);
            const menu = await this.findById(id, relations, true);
            return this.toDto(menu);
        }
        catch (error) {
            this.logger.error(`Lỗi khi lấy thông tin menu CMS: ${error.message}`, error.stack);
            if (error instanceof common_1.NotFoundException) {
                throw error;
            }
            throw new common_1.InternalServerErrorException(`Không thể lấy thông tin menu CMS: ${error.message}`);
        }
    }
    async findBySlugPublic(slug) {
        try {
            this.logger.debug(`Đang lấy menu CMS public với slug: ${slug}`);
            const menu = await this.menuRepository.findOne({
                where: {
                    slug,
                    isDeleted: false,
                    status: cms_menus_entity_1.CmsMenuStatus.ACTIVE
                },
                relations: ['parent', 'children'],
            });
            if (!menu) {
                throw new common_1.NotFoundException(`Không tìm thấy menu với slug: ${slug}`);
            }
            return this.toDto(menu);
        }
        catch (error) {
            this.logger.error(`Lỗi khi lấy menu CMS public: ${error.message}`, error.stack);
            if (error instanceof common_1.NotFoundException) {
                throw error;
            }
            throw new common_1.InternalServerErrorException(`Không thể lấy menu CMS public: ${error.message}`);
        }
    }
    async findByPostType(postType, params) {
        try {
            this.logger.debug(`Đang lấy menu CMS theo loại post: ${postType}`);
            const queryBuilder = this.menuRepository
                .createQueryBuilder('menu')
                .leftJoinAndSelect('menu.parent', 'parent')
                .leftJoinAndSelect('menu.creator', 'creator')
                .leftJoinAndSelect('menu.updater', 'updater')
                .where('menu.isDeleted = :isDeleted', { isDeleted: false })
                .andWhere('menu.postType = :postType', { postType });
            if (params.search) {
                queryBuilder.andWhere(new typeorm_2.Brackets(qb => {
                    qb.where('LOWER(menu.name) LIKE LOWER(:search)', { search: `%${params.search}%` })
                        .orWhere('LOWER(menu.slug) LIKE LOWER(:search)', { search: `%${params.search}%` });
                }));
            }
            const sortOptions = params.sortOptions;
            if (sortOptions && sortOptions.length > 0) {
                const { field, order } = sortOptions[0];
                queryBuilder.orderBy(`menu.${field}`, order);
            }
            else {
                queryBuilder.orderBy('menu.name', 'ASC');
            }
            const skip = (params.page - 1) * params.limit;
            queryBuilder.skip(skip).take(params.limit);
            const [menus, total] = await queryBuilder.getManyAndCount();
            const menuDtos = this.toDtos(menus);
            return { data: menuDtos, total };
        }
        catch (error) {
            this.logger.error(`Lỗi khi lấy menu CMS theo loại post: ${error.message}`, error.stack);
            throw new common_1.InternalServerErrorException(`Không thể lấy menu CMS theo loại post: ${error.message}`);
        }
    }
    async findByStatus(status, params) {
        try {
            this.logger.debug(`Đang lấy menu CMS theo trạng thái: ${status}`);
            const queryBuilder = this.menuRepository
                .createQueryBuilder('menu')
                .leftJoinAndSelect('menu.parent', 'parent')
                .leftJoinAndSelect('menu.creator', 'creator')
                .leftJoinAndSelect('menu.updater', 'updater')
                .where('menu.isDeleted = :isDeleted', { isDeleted: false })
                .andWhere('menu.status = :status', { status });
            if (params.search) {
                queryBuilder.andWhere(new typeorm_2.Brackets(qb => {
                    qb.where('LOWER(menu.name) LIKE LOWER(:search)', { search: `%${params.search}%` })
                        .orWhere('LOWER(menu.slug) LIKE LOWER(:search)', { search: `%${params.search}%` });
                }));
            }
            const sortOptions = params.sortOptions;
            if (sortOptions && sortOptions.length > 0) {
                const { field, order } = sortOptions[0];
                queryBuilder.orderBy(`menu.${field}`, order);
            }
            else {
                queryBuilder.orderBy('menu.name', 'ASC');
            }
            const skip = (params.page - 1) * params.limit;
            queryBuilder.skip(skip).take(params.limit);
            const [menus, total] = await queryBuilder.getManyAndCount();
            const menuDtos = this.toDtos(menus);
            return { data: menuDtos, total };
        }
        catch (error) {
            this.logger.error(`Lỗi khi lấy menu CMS theo trạng thái: ${error.message}`, error.stack);
            throw new common_1.InternalServerErrorException(`Không thể lấy menu CMS theo trạng thái: ${error.message}`);
        }
    }
    async search(keyword, params) {
        try {
            this.logger.debug(`Đang tìm kiếm menu CMS với từ khóa: ${keyword}`);
            const queryBuilder = this.menuRepository
                .createQueryBuilder('menu')
                .leftJoinAndSelect('menu.parent', 'parent')
                .leftJoinAndSelect('menu.creator', 'creator')
                .leftJoinAndSelect('menu.updater', 'updater')
                .where('menu.isDeleted = :isDeleted', { isDeleted: false });
            queryBuilder.andWhere(new typeorm_2.Brackets(qb => {
                qb.where('LOWER(menu.name) LIKE LOWER(:keyword)', { keyword: `%${keyword}%` })
                    .orWhere('LOWER(menu.slug) LIKE LOWER(:keyword)', { keyword: `%${keyword}%` })
                    .orWhere('LOWER(menu.description) LIKE LOWER(:keyword)', { keyword: `%${keyword}%` });
            }));
            const sortOptions = params.sortOptions;
            if (sortOptions && sortOptions.length > 0) {
                const { field, order } = sortOptions[0];
                queryBuilder.orderBy(`menu.${field}`, order);
            }
            else {
                queryBuilder.orderBy('menu.name', 'ASC');
            }
            const skip = (params.page - 1) * params.limit;
            queryBuilder.skip(skip).take(params.limit);
            const [menus, total] = await queryBuilder.getManyAndCount();
            const menuDtos = this.toDtos(menus);
            return { data: menuDtos, total };
        }
        catch (error) {
            this.logger.error(`Lỗi khi tìm kiếm menu CMS: ${error.message}`, error.stack);
            throw new common_1.InternalServerErrorException(`Không thể tìm kiếm menu CMS: ${error.message}`);
        }
    }
    async findDeleted(params) {
        try {
            this.logger.debug(`Đang tìm các menu CMS đã bị xóa mềm với tham số: ${JSON.stringify(params)}`);
            const { limit, page } = params;
            const queryBuilder = this.menuRepository
                .createQueryBuilder('menu')
                .leftJoinAndSelect('menu.parent', 'parent')
                .leftJoinAndSelect('menu.creator', 'creator')
                .leftJoinAndSelect('menu.updater', 'updater')
                .leftJoinAndSelect('menu.deleter', 'deleter')
                .where('menu.isDeleted = :isDeleted', { isDeleted: true });
            const sortOptions = params.sortOptions;
            if (sortOptions && sortOptions.length > 0) {
                const { field, order } = sortOptions[0];
                queryBuilder.orderBy(`menu.${field}`, order);
            }
            else {
                queryBuilder.orderBy('menu.deletedAt', 'DESC');
            }
            const skip = (page - 1) * limit;
            queryBuilder.skip(skip).take(limit);
            const [menus, total] = await queryBuilder.getManyAndCount();
            const menuDtos = this.toDtos(menus);
            return { data: menuDtos, total };
        }
        catch (error) {
            this.logger.error(`Lỗi khi tìm menu CMS đã xóa: ${error.message}`, error.stack);
            throw new common_1.InternalServerErrorException(`Không thể tìm menu CMS đã xóa: ${error.message}`);
        }
    }
    async count(filter) {
        try {
            this.logger.debug(`Đang đếm số lượng menu CMS với filter: ${filter}`);
            const queryBuilder = this.menuRepository
                .createQueryBuilder('menu')
                .where('menu.isDeleted = :isDeleted', { isDeleted: false });
            if (filter) {
                queryBuilder.andWhere('menu.status = :status', { status: filter });
            }
            const count = await queryBuilder.getCount();
            return count;
        }
        catch (error) {
            this.logger.error(`Lỗi khi đếm menu CMS: ${error.message}`, error.stack);
            throw new common_1.InternalServerErrorException(`Không thể đếm menu CMS: ${error.message}`);
        }
    }
};
exports.ReadCmsMenusService = ReadCmsMenusService;
exports.ReadCmsMenusService = ReadCmsMenusService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(cms_menus_entity_1.CmsMenus)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        typeorm_2.DataSource,
        event_emitter_1.EventEmitter2])
], ReadCmsMenusService);
//# sourceMappingURL=read.cms-menus.service.js.map
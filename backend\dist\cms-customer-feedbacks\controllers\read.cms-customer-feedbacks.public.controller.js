"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var ReadCmsCustomerFeedbacksPublicController_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.ReadCmsCustomerFeedbacksPublicController = void 0;
const openapi = require("@nestjs/swagger");
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const class_transformer_1 = require("class-transformer");
const read_cms_customer_feedbacks_service_1 = require("../services/read.cms-customer-feedbacks.service");
const cms_customer_feedback_public_dto_1 = require("../dto/cms-customer-feedback-public.dto");
const custom_pagination_query_dto_1 = require("../../common/dto/custom-pagination-query.dto");
const pagination_response_dto_1 = require("../../common/dto/pagination-response.dto");
const custom_pagination_meta_dto_1 = require("../../common/dto/custom-pagination-meta.dto");
const cms_customer_feedbacks_entity_1 = require("../entity/cms-customer-feedbacks.entity");
const public_decorator_1 = require("../../common/decorators/public.decorator");
let ReadCmsCustomerFeedbacksPublicController = ReadCmsCustomerFeedbacksPublicController_1 = class ReadCmsCustomerFeedbacksPublicController {
    cmsCustomerFeedbacksService;
    logger = new common_1.Logger(ReadCmsCustomerFeedbacksPublicController_1.name);
    constructor(cmsCustomerFeedbacksService) {
        this.cmsCustomerFeedbacksService = cmsCustomerFeedbacksService;
    }
    async getActiveFeedbacks(paginationQuery, rating, productService) {
        this.logger.log('Getting active CMS Customer Feedbacks for public display');
        const { data, total } = await this.cmsCustomerFeedbacksService.findByStatus(cms_customer_feedbacks_entity_1.CmsCustomerFeedbackStatus.APPROVED, paginationQuery);
        let filteredData = data;
        if (rating !== undefined) {
            filteredData = data.filter(feedback => feedback.rating === rating);
        }
        if (productService) {
            filteredData = filteredData.filter(feedback => feedback.productServiceName?.toLowerCase().includes(productService.toLowerCase()));
        }
        const publicData = (0, class_transformer_1.plainToInstance)(cms_customer_feedback_public_dto_1.CmsCustomerFeedbackPublicDto, filteredData, {
            excludeExtraneousValues: true,
        });
        publicData.forEach(feedback => {
            if (feedback.rating) {
                feedback.ratingText = this.getRatingText(feedback.rating);
                feedback.ratingColor = this.getRatingColor(feedback.rating);
            }
            feedback.displayName = this.getDisplayName(feedback.customerName, feedback.customerTitleCompany);
        });
        const meta = new custom_pagination_meta_dto_1.CustomPageMetaDto({
            pageQueryDto: paginationQuery,
            itemCount: publicData.length,
        });
        return new pagination_response_dto_1.PaginationResponseDto(publicData, meta);
    }
    async getActiveFeedbackById(id) {
        this.logger.log(`Getting CMS Customer Feedback by ID: ${id} for public display`);
        const feedback = await this.cmsCustomerFeedbacksService.findOne(id);
        if (!feedback || feedback.status !== cms_customer_feedbacks_entity_1.CmsCustomerFeedbackStatus.APPROVED) {
            return null;
        }
        const publicData = (0, class_transformer_1.plainToInstance)(cms_customer_feedback_public_dto_1.CmsCustomerFeedbackPublicDto, feedback, {
            excludeExtraneousValues: true,
        });
        if (publicData.rating) {
            publicData.ratingText = this.getRatingText(publicData.rating);
            publicData.ratingColor = this.getRatingColor(publicData.rating);
        }
        publicData.displayName = this.getDisplayName(publicData.customerName, publicData.customerTitleCompany);
        return publicData;
    }
    async getTestimonials(limit) {
        this.logger.log('Getting CMS Customer Feedbacks for testimonials display');
        const feedbacks = await this.cmsCustomerFeedbacksService.getHighRatingFeedbacks(limit || 6);
        const publicData = (0, class_transformer_1.plainToInstance)(cms_customer_feedback_public_dto_1.CmsCustomerFeedbackPublicDto, feedbacks, {
            excludeExtraneousValues: true,
        });
        publicData.forEach(feedback => {
            if (feedback.rating) {
                feedback.ratingText = this.getRatingText(feedback.rating);
                feedback.ratingColor = this.getRatingColor(feedback.rating);
            }
            feedback.displayName = this.getDisplayName(feedback.customerName, feedback.customerTitleCompany);
        });
        return publicData.filter(feedback => feedback.canDisplayInTestimonials);
    }
    async search(searchTerm, paginationQuery) {
        this.logger.log(`Searching CMS Customer Feedbacks with term: ${searchTerm} for public display`);
        const { data: allData } = await this.cmsCustomerFeedbacksService.search(searchTerm, paginationQuery);
        const filteredData = allData.filter(feedback => feedback.status === cms_customer_feedbacks_entity_1.CmsCustomerFeedbackStatus.APPROVED && !feedback.isDeleted);
        const publicData = filteredData.map(feedback => (0, class_transformer_1.plainToInstance)(cms_customer_feedback_public_dto_1.CmsCustomerFeedbackPublicDto, feedback, {
            excludeExtraneousValues: true,
        }));
        publicData.forEach(feedback => {
            if (feedback.rating) {
                feedback.ratingText = this.getRatingText(feedback.rating);
                feedback.ratingColor = this.getRatingColor(feedback.rating);
            }
            feedback.displayName = this.getDisplayName(feedback.customerName, feedback.customerTitleCompany);
        });
        const total = publicData.length;
        const meta = new custom_pagination_meta_dto_1.CustomPageMetaDto({
            pageQueryDto: paginationQuery,
            itemCount: total,
        });
        return new pagination_response_dto_1.PaginationResponseDto(publicData, meta);
    }
    async findAllPublic(paginationQuery) {
        this.logger.log('Getting all CMS Customer Feedbacks for public display with search and filter');
        if (paginationQuery.search) {
            const { data: allData } = await this.cmsCustomerFeedbacksService.search(paginationQuery.search, paginationQuery);
            const filteredData = allData.filter(feedback => feedback.status === cms_customer_feedbacks_entity_1.CmsCustomerFeedbackStatus.APPROVED && !feedback.isDeleted);
            const publicData = filteredData.map(feedback => (0, class_transformer_1.plainToInstance)(cms_customer_feedback_public_dto_1.CmsCustomerFeedbackPublicDto, feedback, {
                excludeExtraneousValues: true,
            }));
            publicData.forEach(feedback => {
                if (feedback.rating) {
                    feedback.ratingText = this.getRatingText(feedback.rating);
                    feedback.ratingColor = this.getRatingColor(feedback.rating);
                }
                feedback.displayName = this.getDisplayName(feedback.customerName, feedback.customerTitleCompany);
            });
            const total = publicData.length;
            const meta = new custom_pagination_meta_dto_1.CustomPageMetaDto({
                pageQueryDto: paginationQuery,
                itemCount: total,
            });
            return new pagination_response_dto_1.PaginationResponseDto(publicData, meta);
        }
        const { data, total } = await this.cmsCustomerFeedbacksService.findByStatus(cms_customer_feedbacks_entity_1.CmsCustomerFeedbackStatus.APPROVED, paginationQuery);
        const publicData = data.map(feedback => (0, class_transformer_1.plainToInstance)(cms_customer_feedback_public_dto_1.CmsCustomerFeedbackPublicDto, feedback, {
            excludeExtraneousValues: true,
        }));
        publicData.forEach(feedback => {
            if (feedback.rating) {
                feedback.ratingText = this.getRatingText(feedback.rating);
                feedback.ratingColor = this.getRatingColor(feedback.rating);
            }
            feedback.displayName = this.getDisplayName(feedback.customerName, feedback.customerTitleCompany);
        });
        const meta = new custom_pagination_meta_dto_1.CustomPageMetaDto({
            pageQueryDto: paginationQuery,
            itemCount: total,
        });
        return new pagination_response_dto_1.PaginationResponseDto(publicData, meta);
    }
    getRatingText(rating) {
        const ratingTexts = {
            1: 'Rất không hài lòng',
            2: 'Không hài lòng',
            3: 'Bình thường',
            4: 'Hài lòng',
            5: 'Rất hài lòng'
        };
        return ratingTexts[rating] || 'Không xác định';
    }
    getRatingColor(rating) {
        const ratingColors = {
            1: '#ff4444',
            2: '#ff8800',
            3: '#ffbb33',
            4: '#00C851',
            5: '#007E33'
        };
        return ratingColors[rating] || '#gray';
    }
    getDisplayName(customerName, customerTitleCompany) {
        if (customerTitleCompany) {
            return `${customerName} - ${customerTitleCompany}`;
        }
        return customerName;
    }
};
exports.ReadCmsCustomerFeedbacksPublicController = ReadCmsCustomerFeedbacksPublicController;
__decorate([
    (0, common_1.Get)('active'),
    (0, swagger_1.ApiOperation)({ summary: 'Lấy danh sách feedback đang hoạt động (public)' }),
    (0, swagger_1.ApiQuery)({
        name: 'page',
        required: false,
        type: Number,
        description: 'Số trang',
        example: 1,
    }),
    (0, swagger_1.ApiQuery)({
        name: 'limit',
        required: false,
        type: Number,
        description: 'Số lượng feedback mỗi trang',
        example: 10,
    }),
    (0, swagger_1.ApiQuery)({
        name: 'search',
        required: false,
        type: String,
        description: 'Tìm kiếm theo tên khách hàng hoặc nội dung feedback',
        example: 'dịch vụ tốt',
    }),
    (0, swagger_1.ApiQuery)({
        name: 'rating',
        required: false,
        type: Number,
        description: 'Lọc theo rating (1-5)',
        example: 5,
    }),
    (0, swagger_1.ApiQuery)({
        name: 'productService',
        required: false,
        type: String,
        description: 'Lọc theo sản phẩm/dịch vụ',
        example: 'dịch vụ mua bán vàng',
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Danh sách feedback đang hoạt động',
        schema: {
            type: 'object',
            properties: {
                data: {
                    type: 'array',
                    items: { $ref: '#/components/schemas/CmsCustomerFeedbackPublicDto' },
                },
                meta: {
                    type: 'object',
                    properties: {
                        total: { type: 'number' },
                        page: { type: 'number' },
                        limit: { type: 'number' },
                        totalPages: { type: 'number' },
                    },
                },
            },
        },
    }),
    openapi.ApiResponse({ status: 200 }),
    __param(0, (0, common_1.Query)()),
    __param(1, (0, common_1.Query)('rating')),
    __param(2, (0, common_1.Query)('productService')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [custom_pagination_query_dto_1.CustomPaginationQueryDto, Number, String]),
    __metadata("design:returntype", Promise)
], ReadCmsCustomerFeedbacksPublicController.prototype, "getActiveFeedbacks", null);
__decorate([
    (0, common_1.Get)('active/:id'),
    (0, swagger_1.ApiOperation)({ summary: 'Lấy chi tiết feedback theo ID (public)' }),
    (0, swagger_1.ApiParam)({
        name: 'id',
        description: 'ID của feedback',
        example: '550e8400-e29b-41d4-a716-446655440000',
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Chi tiết feedback',
        type: cms_customer_feedback_public_dto_1.CmsCustomerFeedbackPublicDto,
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.NOT_FOUND,
        description: 'Không tìm thấy feedback',
    }),
    openapi.ApiResponse({ status: 200, type: Object }),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], ReadCmsCustomerFeedbacksPublicController.prototype, "getActiveFeedbackById", null);
__decorate([
    (0, common_1.Get)('testimonials'),
    (0, swagger_1.ApiOperation)({ summary: 'Lấy feedback cho testimonials (rating cao 4-5 sao)' }),
    (0, swagger_1.ApiQuery)({
        name: 'limit',
        required: false,
        type: Number,
        description: 'Số lượng testimonials tối đa',
        example: 6,
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Danh sách testimonials',
        schema: {
            type: 'array',
            items: { $ref: '#/components/schemas/CmsCustomerFeedbackPublicDto' },
        },
    }),
    openapi.ApiResponse({ status: 200, type: [require("../dto/cms-customer-feedback-public.dto").CmsCustomerFeedbackPublicDto] }),
    __param(0, (0, common_1.Query)('limit')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", Promise)
], ReadCmsCustomerFeedbacksPublicController.prototype, "getTestimonials", null);
__decorate([
    (0, common_1.Get)('search'),
    (0, swagger_1.ApiOperation)({ summary: 'Tìm kiếm feedback (public)' }),
    (0, swagger_1.ApiQuery)({
        name: 'q',
        required: true,
        type: String,
        description: 'Từ khóa tìm kiếm',
        example: 'dịch vụ tốt',
    }),
    (0, swagger_1.ApiQuery)({
        name: 'page',
        required: false,
        type: Number,
        description: 'Số trang',
        example: 1,
    }),
    (0, swagger_1.ApiQuery)({
        name: 'limit',
        required: false,
        type: Number,
        description: 'Số lượng feedback mỗi trang',
        example: 10,
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Kết quả tìm kiếm feedback',
        schema: {
            type: 'object',
            properties: {
                data: {
                    type: 'array',
                    items: { $ref: '#/components/schemas/CmsCustomerFeedbackPublicDto' },
                },
                meta: {
                    type: 'object',
                    properties: {
                        total: { type: 'number' },
                        page: { type: 'number' },
                        limit: { type: 'number' },
                        totalPages: { type: 'number' },
                    },
                },
            },
        },
    }),
    openapi.ApiResponse({ status: 200 }),
    __param(0, (0, common_1.Query)('q')),
    __param(1, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, custom_pagination_query_dto_1.CustomPaginationQueryDto]),
    __metadata("design:returntype", Promise)
], ReadCmsCustomerFeedbacksPublicController.prototype, "search", null);
__decorate([
    (0, common_1.Get)(),
    (0, swagger_1.ApiOperation)({
        summary: 'Lấy tất cả feedback (Public)',
        description: 'Lấy danh sách tất cả feedback có thể hiển thị công khai với hỗ trợ tìm kiếm',
    }),
    (0, swagger_1.ApiQuery)({
        name: 'page',
        required: false,
        type: Number,
        description: 'Số trang',
        example: 1,
    }),
    (0, swagger_1.ApiQuery)({
        name: 'limit',
        required: false,
        type: Number,
        description: 'Số lượng feedback mỗi trang',
        example: 10,
    }),
    (0, swagger_1.ApiQuery)({
        name: 'search',
        required: false,
        type: String,
        description: 'Từ khóa tìm kiếm',
        example: 'dịch vụ',
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Danh sách feedback public',
        schema: {
            type: 'object',
            properties: {
                data: {
                    type: 'array',
                    items: { $ref: '#/components/schemas/CmsCustomerFeedbackPublicDto' },
                },
                meta: {
                    type: 'object',
                    properties: {
                        total: { type: 'number' },
                        page: { type: 'number' },
                        limit: { type: 'number' },
                        totalPages: { type: 'number' },
                    },
                },
            },
        },
    }),
    openapi.ApiResponse({ status: 200 }),
    __param(0, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [custom_pagination_query_dto_1.CustomPaginationQueryDto]),
    __metadata("design:returntype", Promise)
], ReadCmsCustomerFeedbacksPublicController.prototype, "findAllPublic", null);
exports.ReadCmsCustomerFeedbacksPublicController = ReadCmsCustomerFeedbacksPublicController = ReadCmsCustomerFeedbacksPublicController_1 = __decorate([
    (0, swagger_1.ApiTags)('cms-customer-feedbacks-public'),
    (0, common_1.Controller)('cms/customer-feedbacks/public'),
    (0, public_decorator_1.Public)(),
    __metadata("design:paramtypes", [read_cms_customer_feedbacks_service_1.ReadCmsCustomerFeedbacksService])
], ReadCmsCustomerFeedbacksPublicController);
//# sourceMappingURL=read.cms-customer-feedbacks.public.controller.js.map
"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CmsCategoriesModule = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const cms_categories_entity_1 = require("./entity/cms-categories.entity");
const base_cms_categories_service_1 = require("./services/base.cms-categories.service");
const slug_cms_categories_service_1 = require("./services/slug.cms-categories.service");
const create_cms_categories_service_1 = require("./services/create.cms-categories.service");
const read_cms_categories_service_1 = require("./services/read.cms-categories.service");
const update_cms_categories_service_1 = require("./services/update.cms-categories.service");
const delete_cms_categories_service_1 = require("./services/delete.cms-categories.service");
const unified_slug_service_1 = require("../common/services/unified-slug.service");
const controllers_1 = require("./controllers");
let CmsCategoriesModule = class CmsCategoriesModule {
};
exports.CmsCategoriesModule = CmsCategoriesModule;
exports.CmsCategoriesModule = CmsCategoriesModule = __decorate([
    (0, common_1.Module)({
        imports: [
            typeorm_1.TypeOrmModule.forFeature([cms_categories_entity_1.CmsCategories]),
        ],
        controllers: [
            controllers_1.CreateCmsCategoriesController,
            controllers_1.ReadCmsCategoriesController,
            controllers_1.ReadCmsCategoriesPublicController,
            controllers_1.UpdateCmsCategoriesController,
            controllers_1.DeleteCmsCategoriesController,
        ],
        providers: [
            unified_slug_service_1.UnifiedSlugService,
            base_cms_categories_service_1.BaseCmsCategoriesService,
            slug_cms_categories_service_1.SlugCmsCategoriesService,
            create_cms_categories_service_1.CreateCmsCategoriesService,
            read_cms_categories_service_1.ReadCmsCategoriesService,
            update_cms_categories_service_1.UpdateCmsCategoriesService,
            delete_cms_categories_service_1.DeleteCmsCategoriesService,
        ],
        exports: [
            typeorm_1.TypeOrmModule,
            base_cms_categories_service_1.BaseCmsCategoriesService,
            slug_cms_categories_service_1.SlugCmsCategoriesService,
            create_cms_categories_service_1.CreateCmsCategoriesService,
            read_cms_categories_service_1.ReadCmsCategoriesService,
            update_cms_categories_service_1.UpdateCmsCategoriesService,
            delete_cms_categories_service_1.DeleteCmsCategoriesService,
        ],
    })
], CmsCategoriesModule);
//# sourceMappingURL=cms-categories.module.js.map
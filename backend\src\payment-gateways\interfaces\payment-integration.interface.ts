// Interface yêu cầu tạo thanh toán
export interface PaymentRequest {
  externalRef: string; // Tham chiếu từ ứng dụng (user ID, order ID)
  amount: number; // Số tiền thanh toán (VND)
  description: string; // Mô tả giao dịch
  clientIp: string; // Địa chỉ IP khách hàng
  bankCode?: string; // Mã ngân hàng (tùy chọn)
  locale?: string; // Ngôn ngữ (vn/en)
  metadata?: Record<string, any>; // Dữ liệu tùy chỉnh
}

// Interface phản hồi tạo thanh toán
export interface PaymentResponse {
  paymentUrl: string; // URL chuyển hướng đến VNPAY
  merchantTxnRef: string; // Mã giao dịch merchant
  transactionId: string; // ID giao dịch trong hệ thống
  expiresAt: Date; // Thời gian hết hạn
  gatewayType: 'VNPAY'; // Loại cổng thanh toán
}

// Interface callback từ VNPAY
export interface PaymentCallback {
  isValid: boolean; // Chữ ký hợp lệ
  isSuccess: boolean; // Thanh toán thành công
  merchantTxnRef: string; // Mã giao dịch merchant
  vnpayTxnRef?: string; // Mã giao dịch VNPAY
  vnpayTxnNo?: string; // Số giao dịch VNPAY
  amount: number; // Số tiền đã thanh toán
  responseCode?: string; // Mã phản hồi VNPAY
  transactionStatus?: string; // Trạng thái giao dịch VNPAY
  bankCode?: string; // Mã ngân hàng sử dụng
  cardType?: string; // Loại thẻ
  payDate?: string; // Thời gian thanh toán
  message?: string; // Thông báo lỗi (nếu có)
  externalRef: string; // Tham chiếu ngoài
  rawData: Record<string, any>; // Dữ liệu thô từ VNPAY
}

// Interface yêu cầu truy vấn giao dịch
export interface PaymentQueryRequest {
  merchantTxnRef: string; // Mã giao dịch merchant
  transactionDate: string; // Ngày giao dịch (yyyyMMddHHmmss)
  orderInfo: string; // Thông tin đơn hàng
  ipAddr: string; // Địa chỉ IP
  vnpayTxnNo?: string; // Số giao dịch VNPAY (tùy chọn)
}

// Interface phản hồi truy vấn giao dịch
export interface PaymentQueryResponse {
  isSuccess: boolean; // Truy vấn thành công
  responseCode: string; // Mã phản hồi
  message: string; // Thông báo
  transaction?: {
    // Thông tin giao dịch (nếu tìm thấy)
    merchantTxnRef: string; // Mã giao dịch merchant
    vnpayTxnRef: string; // Mã giao dịch VNPAY
    vnpayTxnNo: string; // Số giao dịch VNPAY
    amount: number; // Số tiền
    orderInfo: string; // Thông tin đơn hàng
    responseCode: string; // Mã phản hồi
    transactionStatus: string; // Trạng thái giao dịch
    bankCode?: string; // Mã ngân hàng
    cardType?: string; // Loại thẻ
    payDate?: string; // Ngày thanh toán
  };
  rawResponse: Record<string, any>; // Dữ liệu thô từ VNPAY
}

// Interface yêu cầu hoàn tiền
export interface PaymentRefundRequest {
  originalMerchantTxnRef: string; // Mã giao dịch gốc
  amount: number; // Số tiền hoàn (có thể hoàn một phần)
  orderInfo: string; // Lý do hoàn tiền
  transactionDate: string; // Ngày giao dịch gốc
  transactionType: '02' | '03'; // Loại hoàn tiền (02: một phần, 03: toàn bộ)
  createBy: string; // Người tạo yêu cầu hoàn tiền
  ipAddr: string; // Địa chỉ IP
  vnpayTxnNo?: string; // Số giao dịch VNPAY gốc (tùy chọn)
}

// Interface phản hồi hoàn tiền
export interface PaymentRefundResponse {
  isSuccess: boolean; // Hoàn tiền thành công
  responseCode: string; // Mã phản hồi
  message: string; // Thông báo
  refund?: {
    // Thông tin hoàn tiền (nếu thành công)
    merchantTxnRef: string; // Mã giao dịch hoàn tiền
    vnpayTxnRef: string; // Mã giao dịch VNPAY
    amount: number; // Số tiền hoàn
    orderInfo: string; // Lý do hoàn tiền
    transactionType: string; // Loại hoàn tiền
    createBy: string; // Người tạo
    createDate: string; // Ngày tạo
  };
  rawResponse: Record<string, any>; // Dữ liệu thô từ VNPAY
}

// Interface xử lý sự kiện thanh toán (cho ứng dụng tích hợp)
export interface PaymentEventHandler {
  // Sự kiện khi tạo thanh toán thành công
  onPaymentCreated?(data: {
    merchantTxnRef: string; // Mã giao dịch merchant
    externalRef: string; // Tham chiếu ngoài
    amount: number; // Số tiền
    paymentUrl: string; // URL thanh toán
  }): Promise<void>;

  // Sự kiện khi thanh toán thành công
  onPaymentSuccess?(data: {
    merchantTxnRef: string; // Mã giao dịch merchant
    externalRef: string; // Tham chiếu ngoài
    amount: number; // Số tiền
    vnpayTxnRef: string; // Mã giao dịch VNPAY
    vnpayTxnNo: string; // Số giao dịch VNPAY
    bankCode?: string; // Mã ngân hàng
    payDate: string; // Ngày thanh toán
  }): Promise<void>;

  // Sự kiện khi thanh toán thất bại
  onPaymentFailed?(data: {
    merchantTxnRef: string; // Mã giao dịch merchant
    externalRef: string; // Tham chiếu ngoài
    amount: number; // Số tiền
    responseCode: string; // Mã lỗi
    message: string; // Thông báo lỗi
  }): Promise<void>;

  // Sự kiện khi thanh toán bị hủy
  onPaymentCancelled?(data: {
    merchantTxnRef: string; // Mã giao dịch merchant
    externalRef: string; // Tham chiếu ngoài
    amount: number; // Số tiền
  }): Promise<void>;

  // Sự kiện khi thanh toán hết hạn
  onPaymentExpired?(data: {
    merchantTxnRef: string; // Mã giao dịch merchant
    externalRef: string; // Tham chiếu ngoài
    amount: number; // Số tiền
  }): Promise<void>;
}

// Interface cấu hình VNPAY
export interface VnpayConfig {
  tmnCode: string; // Mã merchant VNPAY
  hashSecret: string; // Khóa bí mật VNPAY
  paymentUrl: string; // URL thanh toán VNPAY
  apiUrl: string; // URL API VNPAY
  returnUrl: string; // URL trả về sau thanh toán
  ipnUrl?: string; // URL nhận thông báo IPN
  defaultLocale?: string; // Ngôn ngữ mặc định
  paymentTimeoutMinutes?: number; // Thời gian hết hạn thanh toán (phút)
}

// Interface service chính cho tích hợp VNPAY
export interface IVnpayPaymentService {
  createPayment(request: PaymentRequest): Promise<PaymentResponse>; // Tạo thanh toán
  handleReturnCallback(
    params: Record<string, string>,
  ): Promise<PaymentCallback>; // Xử lý callback return
  handleIpnCallback(params: Record<string, string>): Promise<PaymentCallback>; // Xử lý callback IPN
  queryTransaction(request: PaymentQueryRequest): Promise<PaymentQueryResponse>; // Truy vấn giao dịch
  refundTransaction(
    request: PaymentRefundRequest,
  ): Promise<PaymentRefundResponse>; // Hoàn tiền
  getTransaction(merchantTxnRef: string): Promise<any>; // Lấy thông tin giao dịch
  getStatistics(filter?: any): Promise<any>; // Lấy thống kê
}

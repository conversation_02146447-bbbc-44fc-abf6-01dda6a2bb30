export interface PaymentRequest {
  externalRef: string;
  amount: number;
  description: string;
  clientIp: string;
  bankCode?: string;
  locale?: string;
  metadata?: Record<string, any>;
}

export interface PaymentResponse {
  paymentUrl: string;
  merchantTxnRef: string;
  transactionId: string;
  expiresAt: Date;
  gatewayType: 'VNPAY';
}

export interface PaymentCallback {
  isValid: boolean;
  isSuccess: boolean;
  merchantTxnRef: string;
  vnpayTxnRef?: string;
  vnpayTxnNo?: string;
  amount: number;
  responseCode?: string;
  transactionStatus?: string;
  bankCode?: string;
  cardType?: string;
  payDate?: string;
  message?: string;
  externalRef: string;
  rawData: Record<string, any>;
}

export interface PaymentQueryRequest {
  merchantTxnRef: string;
  transactionDate: string;
  orderInfo: string;
  ipAddr: string;
  vnpayTxnNo?: string;
}

export interface PaymentQueryResponse {
  isSuccess: boolean;
  responseCode: string;
  message: string;
  transaction?: {
    merchantTxnRef: string;
    vnpayTxnRef: string;
    vnpayTxnNo: string;
    amount: number;
    orderInfo: string;
    responseCode: string;
    transactionStatus: string;
    bankCode?: string;
    cardType?: string;
    payDate?: string;
  };
  rawResponse: Record<string, any>;
}

export interface PaymentRefundRequest {
  originalMerchantTxnRef: string;
  amount: number;
  orderInfo: string;
  transactionDate: string;
  transactionType: '02' | '03';
  createBy: string;
  ipAddr: string;
  vnpayTxnNo?: string;
}

export interface PaymentRefundResponse {
  isSuccess: boolean;
  responseCode: string;
  message: string;
  refund?: {
    merchantTxnRef: string;
    vnpayTxnRef: string;
    amount: number;
    orderInfo: string;
    transactionType: string;
    createBy: string;
    createDate: string;
  };
  rawResponse: Record<string, any>;
}

export interface PaymentEventHandler {
  onPaymentCreated?(data: {
    merchantTxnRef: string;
    externalRef: string;
    amount: number;
    paymentUrl: string;
  }): Promise<void>;

  onPaymentSuccess?(data: {
    merchantTxnRef: string;
    externalRef: string;
    amount: number;
    vnpayTxnRef: string;
    vnpayTxnNo: string;
    bankCode?: string;
    payDate: string;
  }): Promise<void>;

  onPaymentFailed?(data: {
    merchantTxnRef: string;
    externalRef: string;
    amount: number;
    responseCode: string;
    message: string;
  }): Promise<void>;

  onPaymentCancelled?(data: {
    merchantTxnRef: string;
    externalRef: string;
    amount: number;
  }): Promise<void>;

  onPaymentExpired?(data: {
    merchantTxnRef: string;
    externalRef: string;
    amount: number;
  }): Promise<void>;
}

export interface VnpayConfig {
  tmnCode: string;
  hashSecret: string;
  paymentUrl: string;
  apiUrl: string;
  returnUrl: string;
  ipnUrl?: string;
  defaultLocale?: string;
  paymentTimeoutMinutes?: number;
}

export interface IVnpayPaymentService {
  createPayment(request: PaymentRequest): Promise<PaymentResponse>;
  handleReturnCallback(params: Record<string, string>): Promise<PaymentCallback>;
  handleIpnCallback(params: Record<string, string>): Promise<PaymentCallback>;
  queryTransaction(request: PaymentQueryRequest): Promise<PaymentQueryResponse>;
  refundTransaction(request: PaymentRefundRequest): Promise<PaymentRefundResponse>;
  getTransaction(merchantTxnRef: string): Promise<any>;
  getStatistics(filter?: any): Promise<any>;
}

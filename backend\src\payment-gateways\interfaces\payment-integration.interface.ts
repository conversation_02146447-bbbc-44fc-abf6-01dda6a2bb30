/**
 * Interface for external applications to integrate with VNPAY Payment Gateway
 * This allows the payment module to be completely portable across projects
 */

export interface PaymentRequest {
  // External reference (user ID, order ID, etc.)
  externalRef: string;
  
  // Amount in VND
  amount: number;
  
  // Payment description
  description: string;
  
  // Client IP address
  clientIp: string;
  
  // Optional: Bank code for direct bank payment
  bankCode?: string;
  
  // Optional: Locale (vn/en)
  locale?: string;
  
  // Optional: Additional metadata from calling application
  metadata?: Record<string, any>;
}

export interface PaymentResponse {
  // Payment URL to redirect user
  paymentUrl: string;
  
  // Merchant transaction reference
  merchantTxnRef: string;
  
  // Transaction ID in our system
  transactionId: string;
  
  // Expiry time
  expiresAt: Date;
  
  // Gateway type
  gatewayType: 'VNPAY';
}

export interface PaymentCallback {
  // Whether the callback is valid (signature verified)
  isValid: boolean;
  
  // Whether the payment was successful
  isSuccess: boolean;
  
  // Merchant transaction reference
  merchantTxnRef: string;
  
  // VNPAY transaction reference
  vnpayTxnRef?: string;
  
  // VNPAY transaction number
  vnpayTxnNo?: string;
  
  // Amount paid
  amount: number;
  
  // VNPAY response code
  responseCode?: string;
  
  // VNPAY transaction status
  transactionStatus?: string;
  
  // Bank code used
  bankCode?: string;
  
  // Card type
  cardType?: string;
  
  // Payment date from VNPAY
  payDate?: string;
  
  // Error message if failed
  message?: string;
  
  // External reference
  externalRef: string;
  
  // Raw callback data
  rawData: Record<string, any>;
}

export interface PaymentQueryRequest {
  // Merchant transaction reference
  merchantTxnRef: string;
  
  // Transaction date (yyyyMMddHHmmss)
  transactionDate: string;
  
  // Order info for verification
  orderInfo: string;
  
  // Client IP
  ipAddr: string;
  
  // Optional: VNPAY transaction number
  vnpayTxnNo?: string;
}

export interface PaymentQueryResponse {
  // Whether query was successful
  isSuccess: boolean;
  
  // Response code from VNPAY
  responseCode: string;
  
  // Message from VNPAY
  message: string;
  
  // Transaction details if found
  transaction?: {
    merchantTxnRef: string;
    vnpayTxnRef: string;
    vnpayTxnNo: string;
    amount: number;
    orderInfo: string;
    responseCode: string;
    transactionStatus: string;
    bankCode?: string;
    cardType?: string;
    payDate?: string;
  };
  
  // Raw response from VNPAY
  rawResponse: Record<string, any>;
}

export interface PaymentRefundRequest {
  // Original merchant transaction reference
  originalMerchantTxnRef: string;
  
  // Refund amount (can be partial)
  amount: number;
  
  // Refund reason
  orderInfo: string;
  
  // Original transaction date
  transactionDate: string;
  
  // Refund type: '02' = partial, '03' = full
  transactionType: '02' | '03';
  
  // User who initiated refund
  createBy: string;
  
  // Client IP
  ipAddr: string;
  
  // Optional: Original VNPAY transaction number
  vnpayTxnNo?: string;
}

export interface PaymentRefundResponse {
  // Whether refund was successful
  isSuccess: boolean;
  
  // Response code from VNPAY
  responseCode: string;
  
  // Message from VNPAY
  message: string;
  
  // Refund transaction details if successful
  refund?: {
    merchantTxnRef: string;
    vnpayTxnRef: string;
    amount: number;
    orderInfo: string;
    transactionType: string;
    createBy: string;
    createDate: string;
  };
  
  // Raw response from VNPAY
  rawResponse: Record<string, any>;
}

/**
 * Interface for external applications to handle payment events
 * Implement this interface in your application to receive payment notifications
 */
export interface PaymentEventHandler {
  /**
   * Called when payment is created and user is redirected to VNPAY
   */
  onPaymentCreated?(data: {
    merchantTxnRef: string;
    externalRef: string;
    amount: number;
    paymentUrl: string;
  }): Promise<void>;

  /**
   * Called when payment is successful
   */
  onPaymentSuccess?(data: {
    merchantTxnRef: string;
    externalRef: string;
    amount: number;
    vnpayTxnRef: string;
    vnpayTxnNo: string;
    bankCode?: string;
    payDate: string;
  }): Promise<void>;

  /**
   * Called when payment fails
   */
  onPaymentFailed?(data: {
    merchantTxnRef: string;
    externalRef: string;
    amount: number;
    responseCode: string;
    message: string;
  }): Promise<void>;

  /**
   * Called when payment is cancelled by user
   */
  onPaymentCancelled?(data: {
    merchantTxnRef: string;
    externalRef: string;
    amount: number;
  }): Promise<void>;

  /**
   * Called when payment expires
   */
  onPaymentExpired?(data: {
    merchantTxnRef: string;
    externalRef: string;
    amount: number;
  }): Promise<void>;
}

/**
 * Configuration interface for VNPAY integration
 */
export interface VnpayConfig {
  // VNPAY merchant code
  tmnCode: string;
  
  // VNPAY hash secret
  hashSecret: string;
  
  // VNPAY payment URL
  paymentUrl: string;
  
  // VNPAY API URL for query/refund
  apiUrl: string;
  
  // Return URL after payment
  returnUrl: string;
  
  // IPN URL for payment notification
  ipnUrl?: string;
  
  // Default locale
  defaultLocale?: string;
  
  // Payment timeout in minutes
  paymentTimeoutMinutes?: number;
}

/**
 * Main service interface for external integration
 */
export interface IVnpayPaymentService {
  /**
   * Create payment and get redirect URL
   */
  createPayment(request: PaymentRequest): Promise<PaymentResponse>;

  /**
   * Handle return callback from VNPAY
   */
  handleReturnCallback(params: Record<string, string>): Promise<PaymentCallback>;

  /**
   * Handle IPN callback from VNPAY
   */
  handleIpnCallback(params: Record<string, string>): Promise<PaymentCallback>;

  /**
   * Query transaction status from VNPAY
   */
  queryTransaction(request: PaymentQueryRequest): Promise<PaymentQueryResponse>;

  /**
   * Refund transaction
   */
  refundTransaction(request: PaymentRefundRequest): Promise<PaymentRefundResponse>;

  /**
   * Get transaction by merchant reference
   */
  getTransaction(merchantTxnRef: string): Promise<any>;

  /**
   * Get transaction statistics
   */
  getStatistics(filter?: any): Promise<any>;
}

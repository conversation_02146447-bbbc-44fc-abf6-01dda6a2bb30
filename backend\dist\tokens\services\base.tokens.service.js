"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.BaseTokensService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const event_emitter_1 = require("@nestjs/event-emitter");
const class_transformer_1 = require("class-transformer");
const token_entity_1 = require("../entities/token.entity");
const token_dto_1 = require("../dto/token.dto");
let BaseTokensService = class BaseTokensService {
    tokenRepository;
    eventEmitter;
    logger = new common_1.Logger('TokensService');
    validRelations = [
        'category', 'tokenPrices', 'tokenAssets', 'contractMetadata', 'orderBook', 'priceAlerts', 'transactions',
        'creator', 'updater', 'deleter',
        'category.creator', 'category.updater', 'category.deleter'
    ];
    EVENT_TOKEN_CREATED = 'token.created';
    EVENT_TOKEN_UPDATED = 'token.updated';
    EVENT_TOKEN_DELETED = 'token.deleted';
    EVENT_TOKEN_RESTORED = 'token.restored';
    EVENT_TOKEN_STATUS_TOGGLED = 'token.statusToggled';
    EVENT_TOKEN_DUPLICATED = 'token.duplicated';
    EVENT_TOKEN_CLEANUP = 'token.cleanup';
    constructor(tokenRepository, eventEmitter) {
        this.tokenRepository = tokenRepository;
        this.eventEmitter = eventEmitter;
    }
    toDto(token) {
        return (0, class_transformer_1.plainToInstance)(token_dto_1.TokenDto, token, {
            excludeExtraneousValues: true,
            enableImplicitConversion: true,
            enableCircularCheck: false,
        });
    }
    validateRelations(relations) {
        if (!relations || !relations.length)
            return [];
        const validatedRelations = relations.filter(rel => {
            if (this.validRelations.includes(rel)) {
                return true;
            }
            if (rel.includes('.')) {
                const parts = rel.split('.');
                const parentRel = parts[0];
                if (this.validRelations.includes(parentRel)) {
                    return true;
                }
            }
            return false;
        });
        return validatedRelations;
    }
    async findByIdOrFail(id, relations = [], withDeleted = false) {
        const validatedRelations = this.validateRelations(relations);
        const token = await this.tokenRepository.findOne({
            where: { id, ...(withDeleted ? {} : { isDeleted: false }) },
            relations: validatedRelations,
        });
        if (!token) {
            throw new common_1.NotFoundException(`Không tìm thấy token với ID "${id}"${withDeleted ? '' : ' hoặc đã bị xóa'}`);
        }
        return token;
    }
    buildWhereClause(filter) {
        const whereClause = { isDeleted: false };
        if (filter) {
            try {
                const [field, value] = filter.split(':');
                whereClause[field] = value;
            }
            catch (error) {
                this.logger.warn(`Định dạng lọc không hợp lệ: ${filter}`);
            }
        }
        return whereClause;
    }
};
exports.BaseTokensService = BaseTokensService;
exports.BaseTokensService = BaseTokensService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(token_entity_1.Token)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        event_emitter_1.EventEmitter2])
], BaseTokensService);
//# sourceMappingURL=base.tokens.service.js.map
import { DeleteCmsCustomerFeedbacksService } from '../services/delete.cms-customer-feedbacks.service';
import { CmsCustomerFeedbackDto } from '../dto/cms-customer-feedback.dto';
export declare class DeleteCmsCustomerFeedbacksController {
    private readonly cmsCustomerFeedbacksService;
    constructor(cmsCustomerFeedbacksService: DeleteCmsCustomerFeedbacksService);
    softDelete(id: string, userId: string): Promise<CmsCustomerFeedbackDto | null>;
    restore(id: string, userId: string): Promise<CmsCustomerFeedbackDto | null>;
    remove(id: string): Promise<{
        affected: number;
    }>;
    bulkSoftDelete(ids: string[], userId: string): Promise<CmsCustomerFeedbackDto[]>;
    bulkRestore(ids: string[], userId: string): Promise<CmsCustomerFeedbackDto[]>;
    bulkRemove(ids: string[]): Promise<{
        affected: number;
    }>;
}

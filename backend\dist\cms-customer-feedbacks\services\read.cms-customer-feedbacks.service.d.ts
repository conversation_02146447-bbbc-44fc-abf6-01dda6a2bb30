import { Repository, DataSource } from 'typeorm';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { BaseCmsCustomerFeedbacksService } from './base.cms-customer-feedbacks.service';
import { CmsCustomerFeedbacks, CmsCustomerFeedbackStatus } from '../entity/cms-customer-feedbacks.entity';
import { CmsCustomerFeedbackDto } from '../dto/cms-customer-feedback.dto';
import { CustomPaginationQueryDto } from '../../common/dto/custom-pagination-query.dto';
export declare class ReadCmsCustomerFeedbacksService extends BaseCmsCustomerFeedbacksService {
    protected readonly feedbackRepository: Repository<CmsCustomerFeedbacks>;
    protected readonly dataSource: DataSource;
    protected readonly eventEmitter: EventEmitter2;
    constructor(feedbackRepository: Repository<CmsCustomerFeedbacks>, dataSource: DataSource, eventEmitter: EventEmitter2);
    findAll(params: CustomPaginationQueryDto): Promise<{
        data: CmsCustomerFeedbackDto[];
        total: number;
    }>;
    findByStatus(status: CmsCustomerFeedbackStatus, params: CustomPaginationQueryDto): Promise<{
        data: CmsCustomerFeedbackDto[];
        total: number;
    }>;
    findByRating(rating: number, params: CustomPaginationQueryDto): Promise<{
        data: CmsCustomerFeedbackDto[];
        total: number;
    }>;
    getApprovedFeedbacks(params: CustomPaginationQueryDto): Promise<{
        data: CmsCustomerFeedbackDto[];
        total: number;
    }>;
    getPendingFeedbacks(params: CustomPaginationQueryDto): Promise<{
        data: CmsCustomerFeedbackDto[];
        total: number;
    }>;
    findByProductService(productServiceName: string, params: CustomPaginationQueryDto): Promise<{
        data: CmsCustomerFeedbackDto[];
        total: number;
    }>;
    count(filter?: string): Promise<number>;
    findOne(id: string, relations?: string[]): Promise<CmsCustomerFeedbackDto | null>;
    findOneOrFail(id: string, relations?: string[]): Promise<CmsCustomerFeedbackDto | null>;
    findByBusinessCodePublic(businessCode: string): Promise<CmsCustomerFeedbackDto | null>;
    search(keyword: string, params: CustomPaginationQueryDto): Promise<{
        data: CmsCustomerFeedbackDto[];
        total: number;
    }>;
    findDeleted(params: CustomPaginationQueryDto): Promise<{
        data: CmsCustomerFeedbackDto[];
        total: number;
    }>;
    getStatistics(): Promise<{
        total: number;
        byStatus: Record<string, number>;
        byRating: Record<string, number>;
        averageRating: number;
        totalWithRating: number;
    }>;
    getHighRatingFeedbacks(limit?: number): Promise<CmsCustomerFeedbackDto[]>;
}

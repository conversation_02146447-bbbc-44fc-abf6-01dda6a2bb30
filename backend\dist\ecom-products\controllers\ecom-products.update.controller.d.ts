import { UpdateEcomProductsService } from '../services/update.ecom-products.service';
import { ReadEcomProductsService } from '../services/read.ecom-products.service';
import { EcomProductDto } from '../dto/ecom-product.dto';
import { UpdateEcomProductDto } from '../dto/update-ecom-product.dto';
declare class UpdateProductStatusDto {
    isActive: boolean;
}
export declare class EcomProductsUpdateController {
    private readonly ecomProductsService;
    private readonly readEcomProductsService;
    constructor(ecomProductsService: UpdateEcomProductsService, readEcomProductsService: ReadEcomProductsService);
    updatePut(id: string, updateEcomProductDto: UpdateEcomProductDto, userId: string): Promise<EcomProductDto>;
    update(id: string, updateEcomProductDto: UpdateEcomProductDto, userId: string): Promise<EcomProductDto>;
    updateStatus(id: string, updateStatusDto: UpdateProductStatusDto, userId: string): Promise<EcomProductDto>;
    bulkUpdate(updateEcomProductDtos: UpdateEcomProductDto[], userId: string): Promise<{
        data: EcomProductDto[];
        message: string;
    }>;
    bulkUpdateAlternative(updateEcomProductDtos: UpdateEcomProductDto[], userId: string): Promise<{
        data: EcomProductDto[];
        message: string;
    }>;
    restore(id: string, userId: string): Promise<EcomProductDto>;
}
export {};

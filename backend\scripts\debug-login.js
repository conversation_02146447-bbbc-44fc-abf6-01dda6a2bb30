/**
 * Script debug để kiểm tra đăng nhập và API
 */

const axios = require('axios');

const CONFIG = {
  BACKEND_URL: 'http://localhost:3168',
  ADMIN_EMAIL: '<EMAIL>',
  ADMIN_PASSWORD: 'adminX@123'
};

async function debugBackend() {
  console.log('🔍 === DEBUG BACKEND CONNECTION ===');
  console.log(`Backend URL: ${CONFIG.BACKEND_URL}`);
  
  // 1. Kiểm tra health endpoint
  try {
    console.log('\n1️⃣ Kiểm tra health endpoint...');
    const healthResponse = await axios.get(`${CONFIG.BACKEND_URL}/api/v1/health`);
    console.log('✅ Health check thành công');
    console.log('Response:', healthResponse.data);
  } catch (error) {
    console.log('❌ Health check thất bại');
    console.log('Error:', error.message);
    if (error.response) {
      console.log('Status:', error.response.status);
      console.log('Data:', error.response.data);
    }
    return;
  }

  // 2. Kiểm tra auth endpoint có tồn tại không
  try {
    console.log('\n2️⃣ Kiểm tra auth endpoint...');
    const authResponse = await axios.post(`${CONFIG.BACKEND_URL}/api/v1/auth/login`, {
      identity: '<EMAIL>',
      password: 'wrongpassword'
    });
    console.log('Response:', authResponse.data);
  } catch (error) {
    console.log('Auth endpoint response:');
    if (error.response) {
      console.log('Status:', error.response.status);
      console.log('Data:', JSON.stringify(error.response.data, null, 2));
      
      if (error.response.status === 401 || error.response.status === 400) {
        console.log('✅ Auth endpoint hoạt động (trả về lỗi như mong đợi)');
      }
    } else {
      console.log('❌ Không thể kết nối đến auth endpoint');
      console.log('Error:', error.message);
      return;
    }
  }

  // 3. Test với thông tin đăng nhập thực
  try {
    console.log('\n3️⃣ Test đăng nhập với thông tin thực...');
    console.log(`Email: ${CONFIG.ADMIN_EMAIL}`);
    console.log(`Password: ${CONFIG.ADMIN_PASSWORD}`);
    
    const loginResponse = await axios.post(`${CONFIG.BACKEND_URL}/api/v1/auth/login`, {
      identity: CONFIG.ADMIN_EMAIL,
      password: CONFIG.ADMIN_PASSWORD
    });
    
    console.log('✅ Đăng nhập thành công!');
    console.log('Full response:', JSON.stringify(loginResponse.data, null, 2));
    
    // Kiểm tra các field có thể có
    const data = loginResponse.data;
    if (data.access_token) {
      console.log('✅ Có access_token:', data.access_token.substring(0, 50) + '...');
    } else if (data.accessToken) {
      console.log('✅ Có accessToken:', data.accessToken.substring(0, 50) + '...');
    } else if (data.token) {
      console.log('✅ Có token:', data.token.substring(0, 50) + '...');
    } else {
      console.log('⚠️ Không tìm thấy token trong response');
      console.log('Available fields:', Object.keys(data));
    }
    
    return data;
    
  } catch (error) {
    console.log('❌ Đăng nhập thất bại');
    console.log('Error message:', error.message);
    if (error.response) {
      console.log('Status:', error.response.status);
      console.log('Response data:', JSON.stringify(error.response.data, null, 2));
      
      if (error.response.status === 401) {
        console.log('💡 Gợi ý: Thông tin đăng nhập không đúng');
      } else if (error.response.status === 404) {
        console.log('💡 Gợi ý: Endpoint auth không tồn tại');
      }
    }
  }
}

async function debugBankAPI(token) {
  if (!token) {
    console.log('\n⏭️ Bỏ qua test Bank API vì không có token');
    return;
  }
  
  console.log('\n4️⃣ Test Bank API...');
  
  // Test GET banks
  try {
    console.log('Test GET /api/v1/banks...');
    const response = await axios.get(`${CONFIG.BACKEND_URL}/api/v1/banks`, {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });
    console.log('✅ GET banks thành công');
    console.log('Response structure:', {
      status: response.status,
      dataType: typeof response.data,
      dataKeys: Object.keys(response.data || {}),
      dataLength: response.data?.data?.length || 'N/A'
    });
  } catch (error) {
    console.log('❌ GET banks thất bại');
    if (error.response) {
      console.log('Status:', error.response.status);
      console.log('Data:', JSON.stringify(error.response.data, null, 2));
    } else {
      console.log('Error:', error.message);
    }
  }
  
  // Test POST bank với dữ liệu mẫu
  try {
    console.log('\nTest POST /api/v1/banks...');
    const testBank = {
      brandName: 'Test Bank',
      fullName: 'Ngân hàng Test',
      shortName: 'TestBank',
      code: 'TEST',
      bin: '999999',
      logoPath: 'https://example.com/logo.png',
      icon: 'https://example.com/icon.svg',
      isActive: true
    };
    
    const response = await axios.post(`${CONFIG.BACKEND_URL}/api/v1/banks`, testBank, {
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    });
    
    console.log('✅ POST bank thành công');
    console.log('Created bank:', {
      id: response.data.id,
      brandName: response.data.brandName,
      code: response.data.code
    });
    
  } catch (error) {
    console.log('❌ POST bank thất bại');
    if (error.response) {
      console.log('Status:', error.response.status);
      console.log('Data:', JSON.stringify(error.response.data, null, 2));
      
      if (error.response.status === 403) {
        console.log('💡 Gợi ý: Không có quyền tạo bank (cần role ADMIN)');
      } else if (error.response.status === 400) {
        console.log('💡 Gợi ý: Dữ liệu không hợp lệ hoặc thiếu field bắt buộc');
      }
    } else {
      console.log('Error:', error.message);
    }
  }
}

async function main() {
  try {
    const loginData = await debugBackend();
    
    // Thử các cách lấy token khác nhau
    let token = null;
    if (loginData) {
      token = loginData.access_token || loginData.accessToken || loginData.token;
    }
    
    await debugBankAPI(token);
    
    console.log('\n🎯 === KẾT LUẬN ===');
    if (token) {
      console.log('✅ Đăng nhập thành công, có thể tiếp tục test import banks');
      console.log('💡 Sử dụng token field:', token ? 'access_token/accessToken/token' : 'unknown');
    } else {
      console.log('❌ Cần khắc phục vấn đề đăng nhập trước khi import banks');
      console.log('💡 Kiểm tra:');
      console.log('   - Backend có đang chạy tại port 3188?');
      console.log('   - Thông tin đăng nhập admin có đúng?');
      console.log('   - Database có user admin không?');
      console.log('   - Cấu trúc response auth có đúng không?');
    }
    
  } catch (error) {
    console.log('❌ Lỗi không mong đợi:', error.message);
  }
}

// Chạy debug
if (require.main === module) {
  main();
}

module.exports = { debugBackend, debugBankAPI };

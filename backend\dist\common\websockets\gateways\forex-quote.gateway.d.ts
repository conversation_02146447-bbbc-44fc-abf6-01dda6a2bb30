import { ConfigService } from '@nestjs/config';
import { JwtService } from '@nestjs/jwt';
import { OnGatewayConnection, OnGatewayDisconnect, OnGatewayInit } from '@nestjs/websockets';
import { Server, Socket } from 'socket.io';
import { BaseGateway } from './base.gateway';
import { WebSocketEmitterService } from '../services/websocket-emitter.service';
import { PolygonForexService } from '../services/polygon-forex.service';
export declare class ForexQuoteGateway extends BaseGateway implements OnGatewayInit, OnGatewayConnection, OnGatewayDisconnect {
    private readonly wsEmitter;
    private readonly polygonForexService;
    private readonly forexLogger;
    constructor(configService: ConfigService, wsEmitter: WebSocketEmitterService, polygonForexService: PolygonForexService, jwtService: JwtService);
    afterInit(server: Server): void;
    handleConnection(client: Socket, ...args: any[]): Promise<void>;
    handleDisconnect(client: Socket): void;
    handleSubscribeForex(client: Socket, _payload: any): void;
}

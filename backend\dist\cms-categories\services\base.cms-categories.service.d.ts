import { Logger } from '@nestjs/common';
import { Repository, DataSource, FindOptionsWhere } from 'typeorm';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { CmsCategories } from '../entity/cms-categories.entity';
import { CmsCategoryDto } from '../dto/cms-category.dto';
export declare class BaseCmsCategoriesService {
    protected readonly categoryRepository: Repository<CmsCategories>;
    protected readonly dataSource: DataSource;
    protected readonly eventEmitter: EventEmitter2;
    protected readonly logger: Logger;
    protected readonly EVENT_CATEGORY_CREATED = "cms-category.created";
    protected readonly EVENT_CATEGORY_UPDATED = "cms-category.updated";
    protected readonly EVENT_CATEGORY_DELETED = "cms-category.deleted";
    protected readonly validRelations: string[];
    constructor(categoryRepository: Repository<CmsCategories>, dataSource: DataSource, eventEmitter: EventEmitter2);
    protected validateRelations(relations: string[]): string[];
    protected buildWhereClause(filter?: string): FindOptionsWhere<CmsCategories> | FindOptionsWhere<CmsCategories>[];
    protected findById(id: string, relations?: string[], throwError?: boolean): Promise<CmsCategories | null>;
    protected findBySlug(slug: string, postType?: string, throwError?: boolean): Promise<CmsCategories | null>;
    protected toDto(category: CmsCategories | null): CmsCategoryDto | null;
    protected toDtos(categories: CmsCategories[]): CmsCategoryDto[];
    protected isChildOf(childId: string, parentId: string): Promise<boolean>;
    protected isDescendantOf(descendantId: string, ancestorId: string): Promise<boolean>;
}

import { ReadCmsCustomerFeedbacksService } from '../services/read.cms-customer-feedbacks.service';
import { CmsCustomerFeedbackPublicDto } from '../dto/cms-customer-feedback-public.dto';
import { CustomPaginationQueryDto } from '../../common/dto/custom-pagination-query.dto';
import { PaginationResponseDto } from '../../common/dto/pagination-response.dto';
export declare class ReadCmsCustomerFeedbacksPublicController {
    private readonly cmsCustomerFeedbacksService;
    private readonly logger;
    constructor(cmsCustomerFeedbacksService: ReadCmsCustomerFeedbacksService);
    getActiveFeedbacks(paginationQuery: CustomPaginationQueryDto, rating?: number, productService?: string): Promise<PaginationResponseDto<CmsCustomerFeedbackPublicDto>>;
    getActiveFeedbackById(id: string): Promise<CmsCustomerFeedbackPublicDto | null>;
    getTestimonials(limit?: number): Promise<CmsCustomerFeedbackPublicDto[]>;
    search(searchTerm: string, paginationQuery: CustomPaginationQueryDto): Promise<PaginationResponseDto<CmsCustomerFeedbackPublicDto>>;
    findAllPublic(paginationQuery: CustomPaginationQueryDto): Promise<PaginationResponseDto<CmsCustomerFeedbackPublicDto>>;
    private getRatingText;
    private getRatingColor;
    private getDisplayName;
}

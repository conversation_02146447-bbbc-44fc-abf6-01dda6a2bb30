"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.BaseUserService = exports.EVENT_USER_CLEANUP = exports.EVENT_USER_DUPLICATED = exports.EVENT_USER_STATUS_TOGGLED = exports.EVENT_USER_RESTORED = exports.EVENT_USER_DELETED = exports.EVENT_USER_UPDATED = exports.EVENT_USER_CREATED = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const event_emitter_1 = require("@nestjs/event-emitter");
const class_transformer_1 = require("class-transformer");
const user_entity_1 = require("../entities/user.entity");
const user_dto_1 = require("../dto/user.dto");
const role_entity_1 = require("../../roles/entities/role.entity");
exports.EVENT_USER_CREATED = 'user.created';
exports.EVENT_USER_UPDATED = 'user.updated';
exports.EVENT_USER_DELETED = 'user.deleted';
exports.EVENT_USER_RESTORED = 'user.restored';
exports.EVENT_USER_STATUS_TOGGLED = 'user.statusToggled';
exports.EVENT_USER_DUPLICATED = 'user.duplicated';
exports.EVENT_USER_CLEANUP = 'user.cleanup';
let BaseUserService = class BaseUserService {
    userRepository;
    roleRepository;
    eventEmitter;
    logger = new common_1.Logger('UserService');
    validRelations = [
        'userRoles',
        'userRoles.role',
        'tokenAssets',
        'creator',
        'updater',
        'deleter',
        'referredBy',
        'referrals'
    ];
    constructor(userRepository, roleRepository, eventEmitter) {
        this.userRepository = userRepository;
        this.roleRepository = roleRepository;
        this.eventEmitter = eventEmitter;
    }
    convertToDto(user) {
        const dto = (0, class_transformer_1.plainToInstance)(user_dto_1.UserDto, user, {
            excludeExtraneousValues: true,
            enableImplicitConversion: true,
            exposeDefaultValues: true,
            exposeUnsetFields: false,
        });
        if (user.userRoles) {
            dto.roles = user.userRoles
                .filter((ur) => !ur.isDeleted)
                .map((ur) => {
                const roleDto = {
                    id: ur.role.id,
                    name: ur.role.name,
                    description: ur.role.description || undefined,
                    createdAt: ur.role.createdAt,
                    updatedAt: ur.role.updatedAt,
                    permissions: ur.role.rolePermissions
                        ?.filter((rp) => !rp.isDeleted)
                        ?.map((rp) => ({
                        id: rp.permission.id,
                        name: rp.permission.name,
                        description: rp.permission.description || undefined,
                        createdAt: rp.permission.createdAt,
                        updatedAt: rp.permission.updatedAt,
                        deletedAt: rp.permission.deletedAt,
                        isDeleted: rp.permission.isDeleted,
                    })) || [],
                };
                return roleDto;
            });
        }
        if (user.createdBy) {
            dto.createdBy = user.createdBy;
            if (user.creator) {
                dto.creator = user.creator;
            }
        }
        if (user.updatedBy) {
            dto.updatedBy = user.updatedBy;
            if (user.updater) {
                dto.updater = user.updater;
            }
        }
        if (user.deletedBy) {
            dto.deletedBy = user.deletedBy;
            if (user.deleter) {
                dto.deleter = user.deleter;
            }
        }
        return dto;
    }
    convertToDtoArray(users) {
        return users.map((user) => this.convertToDto(user));
    }
    async findByIdOrFail(id, relations = [], withDeleted = false) {
        const validRelationsToLoad = relations.includes('userRoles')
            ? relations
            : [...relations, 'userRoles'];
        if (validRelationsToLoad.includes('userRoles') && !validRelationsToLoad.includes('userRoles.role')) {
            validRelationsToLoad.push('userRoles.role');
        }
        const user = await this.userRepository.findOne({
            where: { id },
            relations: validRelationsToLoad,
            withDeleted: withDeleted,
        });
        if (!user) {
            const scope = withDeleted ? '(including deleted) ' : '';
            this.logger.warn(`[findByIdOrFail] User with ID ${id} ${scope}not found`);
            throw new common_1.NotFoundException(`User with ID ${id} ${scope}not found`);
        }
        return user;
    }
    async findRolesByIdsOrFail(roleIds) {
        if (!roleIds || roleIds.length === 0)
            return [];
        const validRoleIds = roleIds.filter((id) => id && id.trim() !== '');
        if (validRoleIds.length === 0)
            return [];
        try {
            const roles = await this.roleRepository.findBy({ id: (0, typeorm_2.In)(validRoleIds) });
            if (roles.length === 0) {
                this.logger.warn(`No roles found for IDs: ${validRoleIds.join(', ')}`);
                return [];
            }
            if (roles.length !== validRoleIds.length) {
                const foundIds = roles.map((r) => r.id);
                const notFoundIds = validRoleIds.filter((id) => !foundIds.includes(id));
                this.logger.warn(`Some roles not found: ${notFoundIds.join(', ')}`);
            }
            return roles;
        }
        catch (error) {
            this.logger.error(`Error finding roles by IDs: ${error.message}`, error.stack);
            return [];
        }
    }
    async preloadRolesMap(roleIds) {
        const uniqueIds = [...new Set(roleIds.filter((id) => id))];
        if (uniqueIds.length === 0)
            return new Map();
        const roles = await this.findRolesByIdsOrFail(uniqueIds);
        return new Map(roles.map((role) => [role.id, role]));
    }
    buildWhereClause(filter) {
        const whereClause = { isDeleted: false };
        if (filter) {
            try {
                const [field, value] = filter.split(':');
                if (this.userRepository.metadata.ownColumns.some((col) => col.propertyName === field)) {
                    whereClause[field] = value;
                }
                else {
                    this.logger.warn(`Ignoring invalid filter field: ${field}`);
                }
            }
            catch (e) {
                this.logger.warn(`Ignoring invalid filter format: ${filter}`);
            }
        }
        return whereClause;
    }
};
exports.BaseUserService = BaseUserService;
exports.BaseUserService = BaseUserService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(user_entity_1.User)),
    __param(1, (0, typeorm_1.InjectRepository)(role_entity_1.Role)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        typeorm_2.Repository,
        event_emitter_1.EventEmitter2])
], BaseUserService);
//# sourceMappingURL=base.user.service.js.map
"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var ReadOrderBookService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.ReadOrderBookService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const event_emitter_1 = require("@nestjs/event-emitter");
const ExcelJS = require("exceljs");
const base_order_book_service_1 = require("./base.order-book.service");
const order_book_entity_1 = require("../entities/order-book.entity");
const pagination_query_dto_1 = require("../../common/dto/pagination-query.dto");
const pagination_response_dto_1 = require("../../common/dto/pagination-response.dto");
const pagination_meta_dto_1 = require("../../common/dto/pagination-meta.dto");
let ReadOrderBookService = ReadOrderBookService_1 = class ReadOrderBookService extends base_order_book_service_1.BaseOrderBookService {
    orderBookRepository;
    eventEmitter;
    logger = new common_1.Logger(ReadOrderBookService_1.name);
    constructor(orderBookRepository, eventEmitter) {
        super(orderBookRepository, eventEmitter);
        this.orderBookRepository = orderBookRepository;
        this.eventEmitter = eventEmitter;
    }
    async findAll(params) {
        try {
            const { limit, sortBy, sortOrder, filter } = params;
            this.logger.log(`Bắt đầu tìm kiếm với filter: ${filter} - Raw params: ${JSON.stringify(params)}`);
            const query = this.orderBookRepository
                .createQueryBuilder('orderBook')
                .leftJoinAndSelect('orderBook.details', 'details')
                .leftJoinAndSelect('details.product', 'product')
                .leftJoinAndSelect('orderBook.user', 'user')
                .leftJoinAndSelect('orderBook.creator', 'creator')
                .leftJoinAndSelect('orderBook.updater', 'updater')
                .leftJoinAndSelect('orderBook.deleter', 'deleter')
                .where({ isDeleted: false })
                .skip(params.skip)
                .take(limit);
            let businessTypeFilter = null;
            if (filter) {
                try {
                    const filterConditions = filter.split(',');
                    for (const condition of filterConditions) {
                        const [field, value] = condition.split(':');
                        const metadata = this.orderBookRepository.metadata;
                        if (metadata.columns.some((col) => col.propertyName === field)) {
                            const paramName = field;
                            const paramValue = value;
                            const paramObject = { [paramName]: paramValue };
                            query.andWhere(`orderBook.${field} = :${field}`, paramObject);
                            if (field === 'businessType') {
                                businessTypeFilter = value;
                            }
                        }
                        else if (field === 'productId') {
                            query.andWhere('details.productId = :productId', { productId: value });
                        }
                        else if (field === 'userId') {
                            query.andWhere('orderBook.userId = :userId', { userId: value });
                        }
                        else {
                            this.logger.warn(`Bỏ qua trường lọc không hợp lệ: ${field}`);
                        }
                    }
                }
                catch (e) {
                    this.logger.error(`Lỗi khi xử lý điều kiện lọc: ${e.message}`, e.stack);
                    this.logger.warn(`Bỏ qua định dạng lọc không hợp lệ: ${filter}`);
                }
            }
            if (sortBy) {
                if (['price', 'volume'].includes(sortBy)) {
                    query.orderBy(`details.${sortBy}`, sortOrder || pagination_query_dto_1.SortOrder.DESC);
                }
                else {
                    query.orderBy(`orderBook.${sortBy}`, sortOrder || pagination_query_dto_1.SortOrder.DESC);
                }
            }
            else {
                query.orderBy('orderBook.createdAt', pagination_query_dto_1.SortOrder.DESC);
            }
            const [entities, total] = await query.getManyAndCount();
            this.logger.log(`Kết quả truy vấn: ${entities.length}/${total} bản ghi`);
            const countQuery = this.orderBookRepository
                .createQueryBuilder('orderBook')
                .select('orderBook.businessType', 'businessType')
                .addSelect('COUNT(orderBook.id)', 'count')
                .where({ isDeleted: false });
            if (filter) {
                try {
                    const filterConditions = filter.split(',');
                    for (const condition of filterConditions) {
                        const [field, value] = condition.split(':');
                        if (field !== 'businessType' && field !== 'productId') {
                            const metadata = this.orderBookRepository.metadata;
                            if (metadata.columns.some((col) => col.propertyName === field)) {
                                countQuery.andWhere(`orderBook.${field} = :${field}`, { [field]: value });
                            }
                            else if (field === 'userId') {
                                countQuery.andWhere('orderBook.userId = :userId', { userId: value });
                            }
                        }
                    }
                }
                catch (e) {
                    this.logger.error(`Lỗi khi xử lý điều kiện lọc trong đếm: ${e.message}`, e.stack);
                    this.logger.warn(`Bỏ qua định dạng lọc không hợp lệ khi đếm: ${filter}`);
                }
            }
            countQuery.groupBy('orderBook.businessType');
            const businessTypeCounts = await countQuery.getRawMany();
            const businessTypeCountsObj = {
                SHORT_INVESTMENT: 0,
                PHYSICAL: 0,
                total: total
            };
            businessTypeCounts.forEach(item => {
                businessTypeCountsObj[item.businessType] = parseInt(item.count);
            });
            const pageMetaDto = new pagination_meta_dto_1.PageMetaDto({
                pageQueryDto: params,
                itemCount: total,
            });
            const dtos = entities.map(entity => this.toDto(entity));
            const response = new pagination_response_dto_1.PaginationResponseDto(dtos, pageMetaDto);
            response.businessTypeCounts = businessTypeCountsObj;
            return response;
        }
        catch (error) {
            this.logger.error(`Lỗi khi tìm kiếm lệnh: ${error.message}`, error.stack);
            throw new common_1.InternalServerErrorException(`Không thể lấy danh sách lệnh: ${error.message}`);
        }
    }
    async search(keyword, params) {
        try {
            const { limit, sortBy, sortOrder } = params;
            const query = this.orderBookRepository
                .createQueryBuilder('orderBook')
                .leftJoinAndSelect('orderBook.details', 'details')
                .leftJoinAndSelect('details.product', 'product')
                .leftJoinAndSelect('orderBook.user', 'user')
                .leftJoinAndSelect('orderBook.creator', 'creator')
                .leftJoinAndSelect('orderBook.updater', 'updater')
                .leftJoinAndSelect('orderBook.deleter', 'deleter')
                .where('(orderBook.id LIKE :keyword OR user.username LIKE :keyword OR product.productName LIKE :keyword)', { keyword: `%${keyword}%` })
                .andWhere({ isDeleted: false })
                .skip(params.skip)
                .take(limit);
            if (sortBy) {
                query.orderBy(`orderBook.${sortBy}`, sortOrder || pagination_query_dto_1.SortOrder.DESC);
            }
            else {
                query.orderBy('orderBook.createdAt', pagination_query_dto_1.SortOrder.DESC);
            }
            const [entities, total] = await query.getManyAndCount();
            const pageMetaDto = new pagination_meta_dto_1.PageMetaDto({
                pageQueryDto: params,
                itemCount: total,
            });
            const dtos = entities.map(entity => this.toDto(entity));
            return new pagination_response_dto_1.PaginationResponseDto(dtos, pageMetaDto);
        }
        catch (error) {
            this.logger.error(`Lỗi khi tìm kiếm lệnh: ${error.message}`, error.stack);
            throw new common_1.InternalServerErrorException(`Không thể tìm kiếm lệnh: ${error.message}`);
        }
    }
    async count(filter) {
        try {
            const query = this.orderBookRepository
                .createQueryBuilder('orderBook')
                .leftJoinAndSelect('orderBook.details', 'details')
                .leftJoinAndSelect('orderBook.creator', 'creator')
                .leftJoinAndSelect('orderBook.updater', 'updater')
                .leftJoinAndSelect('orderBook.deleter', 'deleter')
                .where({ isDeleted: false });
            if (filter) {
                try {
                    const filterConditions = filter.split(',');
                    for (const condition of filterConditions) {
                        const [field, value] = condition.split(':');
                        const metadata = this.orderBookRepository.metadata;
                        if (metadata.columns.some((col) => col.propertyName === field)) {
                            query.andWhere(`orderBook.${field} = :${field}`, { [field]: value });
                        }
                        else if (field === 'productId') {
                            query.andWhere('details.productId = :productId', { productId: value });
                        }
                        else if (field === 'userId') {
                            query.andWhere('orderBook.userId = :userId', { userId: value });
                        }
                        else {
                            this.logger.warn(`Ignoring invalid filter field: ${field}`);
                        }
                    }
                }
                catch (e) {
                    this.logger.error(`Lỗi khi xử lý điều kiện lọc trong count: ${e.message}`, e.stack);
                    this.logger.warn(`Ignoring invalid filter format: ${filter}`);
                }
            }
            return await query.getCount();
        }
        catch (error) {
            this.logger.error(`Lỗi khi đếm số lượng lệnh: ${error.message}`, error.stack);
            throw new common_1.InternalServerErrorException(`Không thể đếm số lượng lệnh: ${error.message}`);
        }
    }
    async findOne(id, relations = []) {
        try {
            const relationsToLoad = [...new Set([...relations, 'details', 'details.product', 'user', 'creator', 'updater', 'deleter'])];
            const orderBook = await this.orderBookRepository.findOne({
                where: { id, isDeleted: false },
                relations: relationsToLoad,
            });
            if (!orderBook) {
                throw new common_1.NotFoundException(`Không tìm thấy lệnh với ID ${id}`);
            }
            return this.toDto(orderBook);
        }
        catch (error) {
            this.logger.error(`Lỗi khi tìm lệnh: ${error.message}`, error.stack);
            if (error instanceof common_1.NotFoundException) {
                throw error;
            }
            throw new common_1.InternalServerErrorException(`Không thể lấy thông tin lệnh: ${error.message}`);
        }
    }
    async findDeleted(params) {
        try {
            const { limit, sortBy, sortOrder } = params;
            const query = this.orderBookRepository
                .createQueryBuilder('orderBook')
                .leftJoinAndSelect('orderBook.details', 'details')
                .leftJoinAndSelect('details.product', 'product')
                .leftJoinAndSelect('orderBook.user', 'user')
                .leftJoinAndSelect('orderBook.creator', 'creator')
                .leftJoinAndSelect('orderBook.updater', 'updater')
                .leftJoinAndSelect('orderBook.deleter', 'deleter')
                .where({ isDeleted: true })
                .skip(params.skip)
                .take(limit);
            if (sortBy) {
                query.orderBy(`orderBook.${sortBy}`, sortOrder || pagination_query_dto_1.SortOrder.DESC);
            }
            else {
                query.orderBy('orderBook.updatedAt', pagination_query_dto_1.SortOrder.DESC);
            }
            const [entities, total] = await query.getManyAndCount();
            const pageMetaDto = new pagination_meta_dto_1.PageMetaDto({
                pageQueryDto: params,
                itemCount: total,
            });
            const dtos = entities.map(entity => this.toDto(entity));
            return new pagination_response_dto_1.PaginationResponseDto(dtos, pageMetaDto);
        }
        catch (error) {
            this.logger.error(`Lỗi khi tìm lệnh đã xóa: ${error.message}`, error.stack);
            throw new common_1.InternalServerErrorException(`Không thể lấy danh sách lệnh đã xóa: ${error.message}`);
        }
    }
    async getStatistics(filter) {
        try {
            this.logger.debug(`Đang lấy thống kê order books theo loại hình kinh doanh${filter ? ` với filter: ${filter}` : ''}`);
            const baseQueryBuilder = this.orderBookRepository.createQueryBuilder('orderBook')
                .leftJoin('orderBook.details', 'details')
                .leftJoin('details.product', 'product')
                .where('orderBook.isDeleted = :isDeleted', { isDeleted: false });
            if (filter) {
                try {
                    const filterConditions = filter.split(',');
                    for (const condition of filterConditions) {
                        const [field, value] = condition.split(':');
                        const metadata = this.orderBookRepository.metadata;
                        if (metadata.columns.some((col) => col.propertyName === field)) {
                            const paramName = field;
                            const paramValue = value;
                            const paramObject = { [paramName]: paramValue };
                            baseQueryBuilder.andWhere(`orderBook.${field} = :${field}`, paramObject);
                        }
                        else if (field === 'productId') {
                            baseQueryBuilder.andWhere('details.productId = :productId', { productId: value });
                        }
                        else if (field === 'userId') {
                            baseQueryBuilder.andWhere('orderBook.userId = :userId', { userId: value });
                        }
                        else {
                            this.logger.warn(`Bỏ qua trường lọc không hợp lệ: ${field}`);
                        }
                    }
                }
                catch (e) {
                    this.logger.error(`Lỗi khi xử lý điều kiện lọc: ${e.message}`, e.stack);
                    this.logger.warn(`Bỏ qua định dạng lọc không hợp lệ: ${filter}`);
                }
            }
            const totalQueryBuilder = baseQueryBuilder.clone();
            const normalQueryBuilder = baseQueryBuilder.clone()
                .andWhere('orderBook.businessType = :businessType', { businessType: 'NORMAL' });
            const immediateDeliveryQueryBuilder = baseQueryBuilder.clone()
                .andWhere('orderBook.businessType = :businessType', { businessType: 'IMMEDIATE_DELIVERY' });
            const [total, normal, immediateDelivery] = await Promise.all([
                totalQueryBuilder.getCount(),
                normalQueryBuilder.getCount(),
                immediateDeliveryQueryBuilder.getCount()
            ]);
            return {
                total,
                businessTypeCounts: {
                    NORMAL: normal,
                    IMMEDIATE_DELIVERY: immediateDelivery
                }
            };
        }
        catch (error) {
            this.logger.error(`Lỗi khi lấy thống kê order books: ${error.message}`, error.stack);
            throw new common_1.InternalServerErrorException(`Không thể lấy thống kê order books: ${error.message}`);
        }
    }
    async export() {
        try {
            const data = await this.orderBookRepository.find({
                where: { isDeleted: false },
                relations: ['user', 'details', 'details.product', 'creator', 'updater', 'deleter'],
                order: { createdAt: 'DESC' }
            });
            const workbook = new ExcelJS.Workbook();
            workbook.creator = 'Gold Exchange Platform';
            workbook.created = new Date();
            const worksheet = workbook.addWorksheet('Sổ lệnh', {
                properties: { tabColor: { argb: 'FFC0C0C0' } }
            });
            const headers = [
                { header: 'ID', key: 'id', width: 36 },
                { header: 'Mã hợp đồng', key: 'contractNumber', width: 15 },
                { header: 'Người dùng', key: 'userName', width: 20 },
                { header: 'Loại lệnh', key: 'orderType', width: 12 },
                { header: 'Hình thức giao dịch', key: 'businessType', width: 20 },
                { header: 'Tổng giá trị', key: 'totalPrice', width: 15 },
                { header: 'Tiền ký quỹ', key: 'depositAmount', width: 15 },
                { header: 'Tiền tất toán', key: 'settlementAmount', width: 15 },
                { header: 'Phí lưu kho', key: 'storageFee', width: 15 },
                { header: 'Hạn tất toán', key: 'settlementDeadline', width: 15 },
                { header: 'Ngày tất toán', key: 'settlementAt', width: 15 },
                { header: 'Trạng thái', key: 'status', width: 15 },
                { header: 'Trạng thái phê duyệt', key: 'approveStatus', width: 20 },
                { header: 'Ngày tạo', key: 'createdAt', width: 15 },
                { header: 'Người tạo', key: 'creatorName', width: 20 }
            ];
            worksheet.columns = headers;
            const orderTypeMap = {
                'BUY': 'Mua',
                'SELL': 'Bán',
                'WITHDRAWAL': 'Rút'
            };
            const businessTypeMap = {
                'SHORT_INVESTMENT': 'Bạc online ký quỹ',
                'PHYSICAL': 'Bạc giao ngay'
            };
            const statusMap = {
                'COMPLETED': 'Đã tất toán',
                'WAIT_PAYMENT': 'Đã ký quỹ',
                'TERMINATED': 'Đã cắt hợp đồng',
                'WAIT_WITHDRAWAL': 'Chờ rút',
                'WITHDRAWN': 'Đã rút',
                'CANCELLED': 'Đã hủy'
            };
            const approveStatusMap = {
                'WAIT_APPROVE': 'Chờ phê duyệt',
                'APPROVED': 'Đã phê duyệt'
            };
            const formatDate = (date) => {
                if (!date)
                    return '';
                try {
                    return new Date(date).toLocaleDateString('vi-VN', {
                        day: '2-digit',
                        month: '2-digit',
                        year: 'numeric'
                    });
                }
                catch (e) {
                    return '';
                }
            };
            data.forEach(item => {
                worksheet.addRow({
                    id: item.id || '',
                    contractNumber: item.contractNumber || '',
                    userName: item.user ? (item.user.fullName || item.user.username || '') : '',
                    orderType: orderTypeMap[item.orderType] || item.orderType || '',
                    businessType: businessTypeMap[item.businessType] || '',
                    totalPrice: item.totalPrice || 0,
                    depositAmount: item.depositPrice || 0,
                    settlementAmount: item.settlementPrice || 0,
                    storageFee: item.storageFee || 0,
                    settlementDeadline: formatDate(item.settlementDeadline),
                    settlementAt: formatDate(item.settlementAt),
                    status: statusMap[item.status] || item.status || '',
                    approveStatus: approveStatusMap[item.approveStatus] || '',
                    createdAt: formatDate(item.createdAt),
                    creatorName: item.creator ? (item.creator.fullName || item.creator.username || '') : ''
                });
            });
            worksheet.getRow(1).font = { bold: true, size: 12 };
            worksheet.getRow(1).fill = {
                type: 'pattern',
                pattern: 'solid',
                fgColor: { argb: 'FFD3D3D3' }
            };
            worksheet.getRow(1).alignment = { vertical: 'middle', horizontal: 'center' };
            ['totalPrice', 'depositAmount', 'settlementAmount', 'storageFee'].forEach(key => {
                const col = worksheet.getColumn(key);
                col.numFmt = '#,##0';
                col.alignment = { horizontal: 'right' };
            });
            ['settlementDeadline', 'settlementAt', 'createdAt'].forEach(key => {
                const col = worksheet.getColumn(key);
                col.alignment = { horizontal: 'center' };
            });
            const lastRow = worksheet.lastRow?.number || 1;
            const lastCol = worksheet.columnCount;
            for (let i = 1; i <= lastRow; i++) {
                for (let j = 1; j <= lastCol; j++) {
                    const cell = worksheet.getCell(i, j);
                    cell.border = {
                        top: { style: 'thin' },
                        left: { style: 'thin' },
                        bottom: { style: 'thin' },
                        right: { style: 'thin' }
                    };
                }
            }
            const buffer = await workbook.xlsx.writeBuffer();
            return buffer;
        }
        catch (error) {
            this.logger.error(`Lỗi khi xuất dữ liệu lệnh: ${error.message}`, error.stack);
            throw new common_1.InternalServerErrorException(`Không thể xuất dữ liệu lệnh: ${error.message}`);
        }
    }
};
exports.ReadOrderBookService = ReadOrderBookService;
exports.ReadOrderBookService = ReadOrderBookService = ReadOrderBookService_1 = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(order_book_entity_1.OrderBook)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        event_emitter_1.EventEmitter2])
], ReadOrderBookService);
//# sourceMappingURL=read.order-book.service.js.map
import { Request, Response } from 'express';
import { PaymentGatewaysService } from './payment-gateways.service';
import { CreatePaymentDto } from './dto/create-payment.dto';
import { PaymentResponseDto } from './dto/payment-response.dto';
export declare class PaymentGatewaysController {
    private readonly paymentGatewaysService;
    private readonly logger;
    constructor(paymentGatewaysService: PaymentGatewaysService);
    createPayment(createPaymentDto: CreatePaymentDto, userId: string, req: Request): Promise<PaymentResponseDto>;
    handleVnpayReturn(query: Record<string, string>, res: Response): Promise<void>;
    handleMomoReturn(query: Record<string, string>, res: Response): Promise<void>;
    handleVnpayIpn(body: Record<string, string>, res: Response): Promise<void>;
    handleMomoIpn(body: Record<string, string>, res: Response): Promise<void>;
}

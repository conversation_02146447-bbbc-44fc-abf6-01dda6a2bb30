"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.User = void 0;
const openapi = require("@nestjs/swagger");
const swagger_1 = require("@nestjs/swagger");
const typeorm_1 = require("typeorm");
const activity_log_entity_1 = require("../../activity-logs/entities/activity-log.entity");
const agent_entity_1 = require("../../agents/entities/agent.entity");
const base_entity_1 = require("../../common/entities/base.entity");
const payment_method_entity_1 = require("../../payment-methods/entities/payment-method.entity");
const price_alert_entity_1 = require("../../price-alerts/entities/price-alert.entity");
const asset_entity_1 = require("../../token-assets/entities/asset.entity");
const transaction_entity_1 = require("../../transactions/entities/transaction.entity");
const user_kyc_entity_1 = require("../../user-kyc/entities/user-kyc.entity");
const crypto_transaction_entity_1 = require("../../crypto-transactions/entities/crypto-transaction.entity");
const crypto_wallet_entity_1 = require("../../crypto-wallets/entities/crypto-wallet.entity");
const order_book_entity_1 = require("../../order-book/entities/order-book.entity");
const user_role_entity_1 = require("../../users/entities/user-role.entity");
const wallet_entity_1 = require("../../wallets/entities/wallet.entity");
let User = class User extends base_entity_1.BaseEntity {
    username;
    email;
    passwordHash;
    fullName;
    phone;
    address;
    bio;
    birthday;
    isActive;
    isKycVerified;
    isAgent;
    twoFaEnabled;
    twoFaSecret;
    notificationEmail;
    notificationSms;
    referralCode;
    parentId;
    path;
    referredBy;
    referrals;
    emailVerified;
    verificationToken;
    resetToken;
    resetTokenExpiry;
    verificationTokenExpiry;
    googleId;
    phoneVerified;
    phoneVerificationToken;
    phoneVerificationTokenExpiry;
    avatarUrl;
    userRoles;
    tokenAssets;
    activityLogs;
    agent;
    approvedAgents;
    orderBooks;
    paymentMethods;
    priceAlerts;
    transactions;
    cryptoTransactions;
    userKycs;
    wallets;
    cryptoWallets;
    getEntityName() {
        return 'users';
    }
    static _OPENAPI_METADATA_FACTORY() {
        return { username: { required: true, type: () => String }, email: { required: true, type: () => String }, passwordHash: { required: true, type: () => String }, fullName: { required: true, type: () => String }, phone: { required: true, type: () => String }, address: { required: true, type: () => String }, bio: { required: true, type: () => String }, birthday: { required: true, type: () => Date }, isActive: { required: true, type: () => Boolean }, isKycVerified: { required: true, type: () => Boolean }, isAgent: { required: true, type: () => Boolean }, twoFaEnabled: { required: true, type: () => Boolean }, twoFaSecret: { required: true, type: () => String }, notificationEmail: { required: true, type: () => Boolean }, notificationSms: { required: true, type: () => Boolean }, referralCode: { required: true, type: () => String }, parentId: { required: true, type: () => String, nullable: true }, path: { required: true, type: () => String, nullable: true }, referredBy: { required: true, type: () => require("./user.entity").User }, referrals: { required: true, type: () => [require("./user.entity").User] }, emailVerified: { required: true, type: () => Boolean }, verificationToken: { required: true, type: () => String }, resetToken: { required: true, type: () => String }, resetTokenExpiry: { required: true, type: () => Date }, verificationTokenExpiry: { required: true, type: () => Date }, googleId: { required: true, type: () => String }, phoneVerified: { required: true, type: () => Boolean }, phoneVerificationToken: { required: true, type: () => String }, phoneVerificationTokenExpiry: { required: true, type: () => Date }, avatarUrl: { required: true, type: () => String }, userRoles: { required: true, type: () => [require("./user-role.entity").UserRole] }, tokenAssets: { required: true, type: () => [require("../../token-assets/entities/asset.entity").Asset] }, activityLogs: { required: true, type: () => [require("../../activity-logs/entities/activity-log.entity").ActivityLog] }, agent: { required: true, type: () => require("../../agents/entities/agent.entity").Agent }, approvedAgents: { required: true, type: () => [require("../../agents/entities/agent.entity").Agent] }, orderBooks: { required: true, type: () => [require("../../order-book/entities/order-book.entity").OrderBook] }, paymentMethods: { required: true, type: () => [require("../../payment-methods/entities/payment-method.entity").PaymentMethod] }, priceAlerts: { required: true, type: () => [require("../../price-alerts/entities/price-alert.entity").PriceAlert] }, transactions: { required: true, type: () => [require("../../transactions/entities/transaction.entity").Transaction] }, cryptoTransactions: { required: true, type: () => [require("../../crypto-transactions/entities/crypto-transaction.entity").CryptoTransaction] }, userKycs: { required: true, type: () => [require("../../user-kyc/entities/user-kyc.entity").UserKyc] }, wallets: { required: true, type: () => [require("../../wallets/entities/wallet.entity").Wallet] }, cryptoWallets: { required: true, type: () => [require("../../crypto-wallets/entities/crypto-wallet.entity").CryptoWallet] } };
    }
};
exports.User = User;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Tên đăng nhập duy nhất của người dùng',
        example: 'johndoe123'
    }),
    (0, typeorm_1.Column)({ unique: true, name: 'username' }),
    __metadata("design:type", String)
], User.prototype, "username", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Địa chỉ email duy nhất của người dùng',
        example: '<EMAIL>'
    }),
    (0, typeorm_1.Column)({ unique: true, name: 'email' }),
    __metadata("design:type", String)
], User.prototype, "email", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'password_hash' }),
    __metadata("design:type", String)
], User.prototype, "passwordHash", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Họ và tên đầy đủ của người dùng',
        example: 'Nguyễn Văn A',
        required: false
    }),
    (0, typeorm_1.Column)({ nullable: true, name: 'full_name' }),
    __metadata("design:type", String)
], User.prototype, "fullName", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Số điện thoại của người dùng',
        example: '+84901234567',
        required: false
    }),
    (0, typeorm_1.Column)({ nullable: true, name: 'phone' }),
    __metadata("design:type", String)
], User.prototype, "phone", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Địa chỉ thường trú của người dùng',
        example: '123 Đường ABC, Quận 1, TP.HCM',
        required: false
    }),
    (0, typeorm_1.Column)({ type: 'text', nullable: true, name: 'address' }),
    __metadata("design:type", String)
], User.prototype, "address", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Giới thiệu ngắn về bản thân của người dùng',
        example: 'Tôi là một nhà đầu tư crypto có kinh nghiệm 5 năm',
        required: false
    }),
    (0, typeorm_1.Column)({ type: 'text', nullable: true, name: 'bio' }),
    __metadata("design:type", String)
], User.prototype, "bio", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Ngày sinh của người dùng',
        example: '1990-01-01',
        required: false
    }),
    (0, typeorm_1.Column)({ type: 'date', nullable: true, name: 'birthday' }),
    __metadata("design:type", Date)
], User.prototype, "birthday", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Trạng thái kích hoạt của tài khoản',
        example: true
    }),
    (0, typeorm_1.Column)({ default: true, name: 'is_active' }),
    __metadata("design:type", Boolean)
], User.prototype, "isActive", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Trạng thái xác minh KYC của người dùng',
        example: false
    }),
    (0, typeorm_1.Column)({ default: false, name: 'is_kyc_verified' }),
    __metadata("design:type", Boolean)
], User.prototype, "isKycVerified", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Trạng thái đại lý của người dùng',
        example: false
    }),
    (0, typeorm_1.Column)({ default: false, name: 'is_agent' }),
    __metadata("design:type", Boolean)
], User.prototype, "isAgent", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Trạng thái kích hoạt xác thực 2 yếu tố (2FA)',
        example: false
    }),
    (0, typeorm_1.Column)({ default: false, name: 'two_fa_enabled' }),
    __metadata("design:type", Boolean)
], User.prototype, "twoFaEnabled", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true, name: 'two_fa_secret' }),
    __metadata("design:type", String)
], User.prototype, "twoFaSecret", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Cài đặt nhận thông báo qua email',
        example: true
    }),
    (0, typeorm_1.Column)({ default: true, name: 'notification_email' }),
    __metadata("design:type", Boolean)
], User.prototype, "notificationEmail", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Cài đặt nhận thông báo qua SMS',
        example: false
    }),
    (0, typeorm_1.Column)({ default: false, name: 'notification_sms' }),
    __metadata("design:type", Boolean)
], User.prototype, "notificationSms", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Mã giới thiệu duy nhất của người dùng',
        example: 'REF123ABC',
        required: false
    }),
    (0, typeorm_1.Column)({ unique: true, nullable: true, name: 'referral_code' }),
    __metadata("design:type", String)
], User.prototype, "referralCode", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'ID của người dùng cha (người giới thiệu trực tiếp)',
        example: '550e8400-e29b-41d4-a716-************',
    }),
    (0, typeorm_1.Column)({ type: 'uuid', nullable: true, name: 'parent_id' }),
    __metadata("design:type", Object)
], User.prototype, "parentId", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Đường dẫn cây phả hệ giới thiệu (dạng: id1.id2.id3)',
        example: '550e8400-e29b-41d4-a716-************.a1b2c3d4-e5f6-47g8-h9i0-j1k2l3m4n5o6',
    }),
    (0, typeorm_1.Column)({ type: 'text', nullable: true, name: 'path' }),
    __metadata("design:type", Object)
], User.prototype, "path", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => User, { nullable: true }),
    (0, typeorm_1.JoinColumn)({ name: 'referred_by' }),
    (0, swagger_1.ApiProperty)({
        type: () => User,
        description: 'Người dùng đã giới thiệu người dùng này',
        required: false
    }),
    __metadata("design:type", User)
], User.prototype, "referredBy", void 0);
__decorate([
    (0, typeorm_1.OneToMany)(() => User, (user) => user.referredBy),
    (0, swagger_1.ApiProperty)({
        type: () => [User],
        description: 'Danh sách người dùng đã được giới thiệu'
    }),
    __metadata("design:type", Array)
], User.prototype, "referrals", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Trạng thái xác minh email',
        example: false
    }),
    (0, typeorm_1.Column)({ default: false, name: 'email_verified' }),
    __metadata("design:type", Boolean)
], User.prototype, "emailVerified", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true, name: 'verification_token' }),
    __metadata("design:type", String)
], User.prototype, "verificationToken", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true, name: 'reset_token' }),
    __metadata("design:type", String)
], User.prototype, "resetToken", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true, name: 'reset_token_expiry' }),
    __metadata("design:type", Date)
], User.prototype, "resetTokenExpiry", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true, name: 'verification_token_expiry' }),
    __metadata("design:type", Date)
], User.prototype, "verificationTokenExpiry", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Google ID cho xác thực OAuth',
        example: 'google_123456789',
        required: false
    }),
    (0, typeorm_1.Column)({ nullable: true, name: 'google_id' }),
    __metadata("design:type", String)
], User.prototype, "googleId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Trạng thái xác minh số điện thoại',
        example: false
    }),
    (0, typeorm_1.Column)({ default: false, name: 'phone_verified' }),
    __metadata("design:type", Boolean)
], User.prototype, "phoneVerified", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Token xác minh số điện thoại',
        example: '123456',
        required: false
    }),
    (0, typeorm_1.Column)({ nullable: true, name: 'phone_verification_token' }),
    __metadata("design:type", String)
], User.prototype, "phoneVerificationToken", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Thời hạn hết hạn của token xác minh số điện thoại',
        example: '2024-03-24T00:00:00Z',
        required: false
    }),
    (0, typeorm_1.Column)({ nullable: true, name: 'phone_verification_token_expiry' }),
    __metadata("design:type", Date)
], User.prototype, "phoneVerificationTokenExpiry", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'URL ảnh đại diện của người dùng',
        example: 'https://example.com/avatars/user123.jpg',
        required: false
    }),
    (0, typeorm_1.Column)({ nullable: true, name: 'avatar_url' }),
    __metadata("design:type", String)
], User.prototype, "avatarUrl", void 0);
__decorate([
    (0, typeorm_1.OneToMany)(() => user_role_entity_1.UserRole, (userRole) => userRole.user),
    (0, swagger_1.ApiProperty)({
        type: () => [user_role_entity_1.UserRole],
        description: 'Danh sách vai trò của người dùng (ADMIN, USER, AGENT, v.v.)',
    }),
    __metadata("design:type", Array)
], User.prototype, "userRoles", void 0);
__decorate([
    (0, typeorm_1.OneToMany)(() => asset_entity_1.Asset, (tokenAsset) => tokenAsset.user),
    (0, swagger_1.ApiProperty)({
        type: () => [asset_entity_1.Asset],
        description: 'Danh sách tài sản token/coin của người dùng',
    }),
    __metadata("design:type", Array)
], User.prototype, "tokenAssets", void 0);
__decorate([
    (0, typeorm_1.OneToMany)(() => activity_log_entity_1.ActivityLog, (activityLog) => activityLog.user),
    (0, swagger_1.ApiProperty)({
        type: () => [activity_log_entity_1.ActivityLog],
        description: 'Nhật ký hoạt động của người dùng trong hệ thống',
    }),
    __metadata("design:type", Array)
], User.prototype, "activityLogs", void 0);
__decorate([
    (0, typeorm_1.OneToOne)(() => agent_entity_1.Agent, (agent) => agent.user),
    (0, typeorm_1.JoinColumn)({ name: 'agent_id' }),
    (0, swagger_1.ApiProperty)({
        type: () => agent_entity_1.Agent,
        description: 'Thông tin đại lý của người dùng (nếu là đại lý)',
        required: false
    }),
    __metadata("design:type", agent_entity_1.Agent)
], User.prototype, "agent", void 0);
__decorate([
    (0, typeorm_1.OneToMany)(() => agent_entity_1.Agent, (agent) => agent.approvedBy),
    (0, swagger_1.ApiProperty)({
        type: () => [agent_entity_1.Agent],
        description: 'Danh sách đại lý được phê duyệt bởi người dùng này',
    }),
    __metadata("design:type", Array)
], User.prototype, "approvedAgents", void 0);
__decorate([
    (0, typeorm_1.OneToMany)(() => order_book_entity_1.OrderBook, (orderBook) => orderBook.user),
    (0, swagger_1.ApiProperty)({
        type: () => [order_book_entity_1.OrderBook],
        description: 'Danh sách lệnh giao dịch của người dùng',
    }),
    __metadata("design:type", Array)
], User.prototype, "orderBooks", void 0);
__decorate([
    (0, typeorm_1.OneToMany)(() => payment_method_entity_1.PaymentMethod, (paymentMethod) => paymentMethod.user),
    (0, swagger_1.ApiProperty)({
        type: () => [payment_method_entity_1.PaymentMethod],
        description: 'Danh sách phương thức thanh toán của người dùng',
    }),
    __metadata("design:type", Array)
], User.prototype, "paymentMethods", void 0);
__decorate([
    (0, typeorm_1.OneToMany)(() => price_alert_entity_1.PriceAlert, (priceAlert) => priceAlert.user),
    (0, swagger_1.ApiProperty)({
        type: () => [price_alert_entity_1.PriceAlert],
        description: 'Danh sách cảnh báo giá được tạo bởi người dùng',
    }),
    __metadata("design:type", Array)
], User.prototype, "priceAlerts", void 0);
__decorate([
    (0, typeorm_1.OneToMany)(() => transaction_entity_1.Transaction, (transaction) => transaction.user),
    (0, swagger_1.ApiProperty)({
        type: () => [transaction_entity_1.Transaction],
        description: 'Lịch sử giao dịch tài chính của người dùng',
    }),
    __metadata("design:type", Array)
], User.prototype, "transactions", void 0);
__decorate([
    (0, typeorm_1.OneToMany)(() => crypto_transaction_entity_1.CryptoTransaction, (cryptoTransaction) => cryptoTransaction.user),
    (0, swagger_1.ApiProperty)({
        type: () => [crypto_transaction_entity_1.CryptoTransaction],
        description: 'Lịch sử giao dịch tiền điện tử của người dùng',
    }),
    __metadata("design:type", Array)
], User.prototype, "cryptoTransactions", void 0);
__decorate([
    (0, typeorm_1.OneToMany)(() => user_kyc_entity_1.UserKyc, (userKyc) => userKyc.user),
    (0, swagger_1.ApiProperty)({
        type: () => [user_kyc_entity_1.UserKyc],
        description: 'Thông tin xác minh danh tính (KYC) của người dùng',
    }),
    __metadata("design:type", Array)
], User.prototype, "userKycs", void 0);
__decorate([
    (0, typeorm_1.OneToMany)(() => wallet_entity_1.Wallet, (wallet) => wallet.user),
    (0, swagger_1.ApiProperty)({
        type: () => [wallet_entity_1.Wallet],
        description: 'Danh sách ví tiền tệ thông thường của người dùng',
    }),
    __metadata("design:type", Array)
], User.prototype, "wallets", void 0);
__decorate([
    (0, typeorm_1.OneToMany)(() => crypto_wallet_entity_1.CryptoWallet, (cryptoWallet) => cryptoWallet.user),
    (0, swagger_1.ApiProperty)({
        type: () => [crypto_wallet_entity_1.CryptoWallet],
        description: 'Danh sách ví tiền điện tử của người dùng',
    }),
    __metadata("design:type", Array)
], User.prototype, "cryptoWallets", void 0);
exports.User = User = __decorate([
    (0, typeorm_1.Entity)('users')
], User);
//# sourceMappingURL=user.entity.js.map
import { NotificationsService } from '../services/notifications.service';
import { NotificationType } from '../gateways/notifications.gateway';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { UserRegisteredPayload, UserUpdatedPayload } from '../listeners/user-event.listener';
export declare class NotificationsController {
    private readonly notificationsService;
    private readonly eventEmitter;
    private readonly logger;
    constructor(notificationsService: NotificationsService, eventEmitter: EventEmitter2);
    sendNotification(userId: string, body: {
        type: NotificationType;
        title: string;
        message: string;
        data?: any;
    }): {
        success: boolean;
        notification: import("../gateways/notifications.gateway").Notification;
    };
    testUserRegisteredEvent(body: {
        userId: string;
        username: string;
        email: string;
    }): {
        success: boolean;
        message: string;
        payload: UserRegisteredPayload;
    };
    testUserUpdatedEvent(body: {
        userId: string;
        username: string;
        updatedFields: string[];
    }): {
        success: boolean;
        message: string;
        payload: UserUpdatedPayload;
    };
}

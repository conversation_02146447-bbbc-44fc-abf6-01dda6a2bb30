import { Repository, DataSource } from 'typeorm';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { BaseCmsBannersService } from './base.cms-banners.service';
import { CmsBanners, CmsBannerStatus, CmsBannerLocation } from '../entity/cms-banners.entity';
import { UpdateCmsBannerDto } from '../dto/update.cms-banner.dto';
import { CmsBannerDto } from '../dto/cms-banner.dto';
export declare class UpdateCmsBannersService extends BaseCmsBannersService {
    protected readonly bannerRepository: Repository<CmsBanners>;
    protected readonly dataSource: DataSource;
    protected readonly eventEmitter: EventEmitter2;
    constructor(bannerRepository: Repository<CmsBanners>, dataSource: DataSource, eventEmitter: EventEmitter2);
    update(id: string, updateDto: UpdateCmsBannerDto, userId: string): Promise<CmsBannerDto | null>;
    bulkUpdate(updates: Array<{
        id: string;
        data: UpdateCmsBannerDto;
    }>, userId: string): Promise<CmsBannerDto[]>;
    updateStatus(id: string, status: CmsBannerStatus, userId: string): Promise<CmsBannerDto | null>;
    bulkUpdateStatus(ids: string[], status: CmsBannerStatus, userId: string): Promise<CmsBannerDto[]>;
    updateLocation(id: string, location: CmsBannerLocation | null, userId: string): Promise<CmsBannerDto | null>;
    updateDisplayOrder(id: string, displayOrder: number, userId: string): Promise<CmsBannerDto | null>;
    updateDisplayTime(id: string, startDate: string | null, endDate: string | null, userId: string): Promise<CmsBannerDto | null>;
}

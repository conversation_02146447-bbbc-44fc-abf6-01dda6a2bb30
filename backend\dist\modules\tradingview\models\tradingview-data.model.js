"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.TradingViewConfigKeys = exports.WebSocketMessageType = exports.ChartInterval = void 0;
var ChartInterval;
(function (ChartInterval) {
    ChartInterval["ONE_MINUTE"] = "1m";
    ChartInterval["FIVE_MINUTES"] = "5m";
    ChartInterval["FIFTEEN_MINUTES"] = "15m";
    ChartInterval["THIRTY_MINUTES"] = "30m";
    ChartInterval["ONE_HOUR"] = "1h";
    ChartInterval["FOUR_HOURS"] = "4h";
    ChartInterval["ONE_DAY"] = "1d";
    ChartInterval["ONE_WEEK"] = "1w";
    ChartInterval["ONE_MONTH"] = "1M";
})(ChartInterval || (exports.ChartInterval = ChartInterval = {}));
var WebSocketMessageType;
(function (WebSocketMessageType) {
    WebSocketMessageType["PRICE_UPDATE"] = "price_update";
    WebSocketMessageType["CHART_DATA"] = "chart_data";
    WebSocketMessageType["ERROR"] = "error";
    WebSocketMessageType["AUTH"] = "auth";
    WebSocketMessageType["SUBSCRIBE"] = "subscribe";
    WebSocketMessageType["UNSUBSCRIBE"] = "unsubscribe";
})(WebSocketMessageType || (exports.WebSocketMessageType = WebSocketMessageType = {}));
var TradingViewConfigKeys;
(function (TradingViewConfigKeys) {
    TradingViewConfigKeys["API_KEY"] = "TRADINGVIEW_API_KEY";
    TradingViewConfigKeys["API_SECRET"] = "TRADINGVIEW_API_SECRET";
    TradingViewConfigKeys["BASE_URL"] = "TRADINGVIEW_BASE_URL";
    TradingViewConfigKeys["WS_ENDPOINT"] = "TRADINGVIEW_WS_ENDPOINT";
    TradingViewConfigKeys["RATE_LIMIT"] = "TRADINGVIEW_RATE_LIMIT";
    TradingViewConfigKeys["RATE_LIMIT_WINDOW"] = "TRADINGVIEW_RATE_LIMIT_WINDOW";
    TradingViewConfigKeys["CACHE_TTL"] = "TRADINGVIEW_CACHE_TTL";
    TradingViewConfigKeys["POLLING_INTERVAL"] = "TRADINGVIEW_POLLING_INTERVAL";
    TradingViewConfigKeys["DEFAULT_SYMBOL"] = "TRADINGVIEW_DEFAULT_SYMBOL";
})(TradingViewConfigKeys || (exports.TradingViewConfigKeys = TradingViewConfigKeys = {}));
//# sourceMappingURL=tradingview-data.model.js.map
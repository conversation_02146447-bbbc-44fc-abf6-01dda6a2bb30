"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.DeleteCmsMenusController = void 0;
const openapi = require("@nestjs/swagger");
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const jwt_auth_guard_1 = require("../../common/guards/jwt-auth.guard");
const roles_decorator_1 = require("../../common/decorators/roles.decorator");
const permissions_decorator_1 = require("../../common/decorators/permissions.decorator");
const get_user_decorator_1 = require("../../common/decorators/get-user.decorator");
const delete_cms_menus_service_1 = require("../services/delete.cms-menus.service");
const api_response_dto_1 = require("../../common/dto/api-response.dto");
let DeleteCmsMenusController = class DeleteCmsMenusController {
    cmsMenusService;
    constructor(cmsMenusService) {
        this.cmsMenusService = cmsMenusService;
    }
    async softDelete(id, userId) {
        const result = await this.cmsMenusService.softDelete(id, userId);
        if (!result) {
            throw new common_1.NotFoundException(`Không tìm thấy menu CMS với ID: ${id}`);
        }
        return result;
    }
    async restore(id, userId) {
        const result = await this.cmsMenusService.restore(id, userId);
        if (!result) {
            throw new common_1.NotFoundException(`Không tìm thấy menu CMS với ID: ${id}`);
        }
        return result;
    }
    async forceDelete(id, userId) {
        return this.cmsMenusService.forceDelete(id, userId);
    }
    async bulkSoftDelete(ids, userId) {
        return this.cmsMenusService.bulkSoftDelete(ids, userId);
    }
};
exports.DeleteCmsMenusController = DeleteCmsMenusController;
__decorate([
    (0, common_1.Delete)(':id'),
    (0, roles_decorator_1.Roles)('ADMIN'),
    (0, permissions_decorator_1.Permissions)('cms-menu:delete'),
    (0, swagger_1.ApiOperation)({ summary: 'Xóa mềm menu CMS' }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Menu CMS đã được xóa mềm thành công.',
        type: api_response_dto_1.ApiResponseDto,
        schema: {
            properties: {
                data: { $ref: '#/components/schemas/CmsMenuDto' },
            },
        },
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.NOT_FOUND,
        description: 'Không tìm thấy menu CMS.',
        type: api_response_dto_1.ApiResponseDto,
        schema: { properties: { data: { type: 'object', nullable: true } } },
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.BAD_REQUEST,
        description: 'Không thể xóa menu có menu con.',
        type: api_response_dto_1.ApiResponseDto,
        schema: { properties: { data: { type: 'object', nullable: true } } },
    }),
    (0, swagger_1.ApiParam)({ name: 'id', type: String, description: 'ID của menu CMS' }),
    openapi.ApiResponse({ status: 200, type: Object }),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __param(1, (0, get_user_decorator_1.GetUser)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String]),
    __metadata("design:returntype", Promise)
], DeleteCmsMenusController.prototype, "softDelete", null);
__decorate([
    (0, common_1.Delete)(':id/restore'),
    (0, roles_decorator_1.Roles)('ADMIN'),
    (0, permissions_decorator_1.Permissions)('cms-menu:delete'),
    (0, swagger_1.ApiOperation)({ summary: 'Khôi phục menu CMS đã xóa mềm' }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Menu CMS đã được khôi phục thành công.',
        type: api_response_dto_1.ApiResponseDto,
        schema: {
            properties: {
                data: { $ref: '#/components/schemas/CmsMenuDto' },
            },
        },
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.NOT_FOUND,
        description: 'Không tìm thấy menu CMS.',
        type: api_response_dto_1.ApiResponseDto,
        schema: { properties: { data: { type: 'object', nullable: true } } },
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.BAD_REQUEST,
        description: 'Menu chưa bị xóa, không thể khôi phục.',
        type: api_response_dto_1.ApiResponseDto,
        schema: { properties: { data: { type: 'object', nullable: true } } },
    }),
    (0, swagger_1.ApiParam)({ name: 'id', type: String, description: 'ID của menu CMS' }),
    openapi.ApiResponse({ status: 200, type: Object }),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __param(1, (0, get_user_decorator_1.GetUser)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String]),
    __metadata("design:returntype", Promise)
], DeleteCmsMenusController.prototype, "restore", null);
__decorate([
    (0, common_1.Delete)(':id/force'),
    (0, roles_decorator_1.Roles)('ADMIN'),
    (0, permissions_decorator_1.Permissions)('cms-menu:delete'),
    (0, swagger_1.ApiOperation)({ summary: 'Xóa vĩnh viễn menu CMS' }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Menu CMS đã được xóa vĩnh viễn thành công.',
        type: api_response_dto_1.ApiResponseDto,
        schema: {
            properties: {
                data: { type: 'boolean' },
            },
        },
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.NOT_FOUND,
        description: 'Không tìm thấy menu CMS.',
        type: api_response_dto_1.ApiResponseDto,
        schema: { properties: { data: { type: 'object', nullable: true } } },
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.BAD_REQUEST,
        description: 'Menu phải được xóa mềm trước khi xóa vĩnh viễn.',
        type: api_response_dto_1.ApiResponseDto,
        schema: { properties: { data: { type: 'object', nullable: true } } },
    }),
    (0, swagger_1.ApiParam)({ name: 'id', type: String, description: 'ID của menu CMS' }),
    openapi.ApiResponse({ status: 200, type: Boolean }),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __param(1, (0, get_user_decorator_1.GetUser)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String]),
    __metadata("design:returntype", Promise)
], DeleteCmsMenusController.prototype, "forceDelete", null);
__decorate([
    (0, common_1.Delete)('bulk'),
    (0, roles_decorator_1.Roles)('ADMIN'),
    (0, permissions_decorator_1.Permissions)('cms-menu:delete'),
    (0, swagger_1.ApiOperation)({ summary: 'Xóa mềm nhiều menu CMS cùng lúc' }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Các menu CMS đã được xóa mềm thành công.',
        type: api_response_dto_1.ApiResponseDto,
        schema: {
            properties: {
                data: {
                    type: 'array',
                    items: { $ref: '#/components/schemas/CmsMenuDto' },
                },
            },
        },
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.BAD_REQUEST,
        description: 'Dữ liệu đầu vào không hợp lệ.',
        type: api_response_dto_1.ApiResponseDto,
        schema: { properties: { data: { type: 'object', nullable: true } } },
    }),
    (0, swagger_1.ApiBody)({
        schema: {
            type: 'object',
            properties: {
                ids: {
                    type: 'array',
                    items: { type: 'string' },
                    description: 'Danh sách ID của các menu CMS cần xóa',
                },
            },
            required: ['ids'],
        },
    }),
    openapi.ApiResponse({ status: 200, type: [require("../dto/cms-menu.dto").CmsMenuDto] }),
    __param(0, (0, common_1.Body)('ids')),
    __param(1, (0, get_user_decorator_1.GetUser)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Array, String]),
    __metadata("design:returntype", Promise)
], DeleteCmsMenusController.prototype, "bulkSoftDelete", null);
exports.DeleteCmsMenusController = DeleteCmsMenusController = __decorate([
    (0, swagger_1.ApiTags)('cms-menus'),
    (0, swagger_1.ApiBearerAuth)(),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, common_1.Controller)('cms/menus'),
    __metadata("design:paramtypes", [delete_cms_menus_service_1.DeleteCmsMenusService])
], DeleteCmsMenusController);
//# sourceMappingURL=delete.cms-menus.controller.js.map
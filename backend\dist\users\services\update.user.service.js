"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UpdateUserService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const event_emitter_1 = require("@nestjs/event-emitter");
const typeorm_transactional_1 = require("typeorm-transactional");
const bcrypt = require("bcryptjs");
const base_user_service_1 = require("./base.user.service");
const user_entity_1 = require("../entities/user.entity");
const role_entity_1 = require("../../roles/entities/role.entity");
const user_role_entity_1 = require("../entities/user-role.entity");
const update_user_dto_1 = require("../dto/update.user.dto");
const update_referral_code_user_dto_1 = require("../dto/update-referral-code.user.dto");
const default_user_role_enum_1 = require("../enums/default-user-role.enum");
const events_user_gateway_1 = require("../events/events.user.gateway");
const optimistic_locking_service_1 = require("../../common/services/optimistic-locking.service");
const connection_pool_service_1 = require("../../common/services/connection-pool.service");
let UpdateUserService = class UpdateUserService extends base_user_service_1.BaseUserService {
    userRepository;
    roleRepository;
    userRoleRepository;
    eventEmitter;
    userEventsGateway;
    optimisticLockingService;
    connectionPoolService;
    constructor(userRepository, roleRepository, userRoleRepository, eventEmitter, userEventsGateway, optimisticLockingService, connectionPoolService) {
        super(userRepository, roleRepository, eventEmitter);
        this.userRepository = userRepository;
        this.roleRepository = roleRepository;
        this.userRoleRepository = userRoleRepository;
        this.eventEmitter = eventEmitter;
        this.userEventsGateway = userEventsGateway;
        this.optimisticLockingService = optimisticLockingService;
        this.connectionPoolService = connectionPoolService;
    }
    async update(id, updateUserDto, userId) {
        this.logger.log(`Attempting to update user ID: ${id} with version: ${updateUserDto.version}`);
        if (!userId || userId === 'undefined') {
            this.logger.error(`Invalid userId provided: ${userId}`);
            throw new common_1.BadRequestException('Valid user ID is required for update operation');
        }
        return this.connectionPoolService.executeWithPoolManagement(async () => {
            try {
                const user = await this.findByIdOrFail(id, ['userRoles']);
                if (updateUserDto.version !== undefined) {
                    await this.optimisticLockingService.validateVersion(this.userRepository, id, updateUserDto.version);
                }
                if (updateUserDto.username && updateUserDto.username !== user.username) {
                    const existingUser = await this.userRepository.findOne({
                        where: { username: updateUserDto.username },
                    });
                    if (existingUser) {
                        throw new common_1.BadRequestException('Username already exists');
                    }
                }
                if (updateUserDto.email && updateUserDto.email !== user.email) {
                    const existingUser = await this.userRepository.findOne({
                        where: { email: updateUserDto.email },
                    });
                    if (existingUser) {
                        throw new common_1.BadRequestException('Email already exists');
                    }
                }
                if (updateUserDto.password) {
                    const salt = await bcrypt.genSalt();
                    const passwordHash = await bcrypt.hash(updateUserDto.password, salt);
                    delete updateUserDto.password;
                    updateUserDto.passwordHash = passwordHash;
                }
                if (updateUserDto.roles) {
                    const existingUserRoles = await this.userRoleRepository.find({
                        where: { userId: id, isDeleted: false },
                    });
                    if (existingUserRoles.length > 0) {
                        for (const userRole of existingUserRoles) {
                            userRole.isDeleted = true;
                            userRole.deletedBy = userId;
                            userRole.deletedAt = new Date();
                        }
                        await this.userRoleRepository.save(existingUserRoles);
                        this.logger.log(`Soft deleted ${existingUserRoles.length} existing user roles for user ${id}`);
                    }
                    let roles = [];
                    if (updateUserDto.roles && updateUserDto.roles.length > 0) {
                        const uniqueRoleIds = [...new Set(updateUserDto.roles.map((role) => role.id))];
                        roles = await this.findRolesByIdsOrFail(uniqueRoleIds);
                    }
                    else {
                        const userRole = await this.roleRepository.findOne({
                            where: { name: default_user_role_enum_1.PrimaryUserRole.USER, isDeleted: false },
                        });
                        if (!userRole) {
                            throw new common_1.BadRequestException('Default user role not found in the system');
                        }
                        roles = [userRole];
                    }
                    for (const role of roles) {
                        try {
                            const existingUserRole = await this.userRoleRepository.findOne({
                                where: { userId: id, roleId: role.id },
                                withDeleted: true,
                            });
                            if (existingUserRole) {
                                if (existingUserRole.isDeleted) {
                                    existingUserRole.isDeleted = false;
                                    existingUserRole.deletedBy = null;
                                    existingUserRole.deletedAt = null;
                                    existingUserRole.updatedBy = userId;
                                    await this.userRoleRepository.save(existingUserRole);
                                    this.logger.log(`Restored role ${role.name} for user ${id}`);
                                }
                            }
                            else {
                                const userRole = this.userRoleRepository.create({
                                    userId: id,
                                    roleId: role.id,
                                    createdBy: userId,
                                    updatedBy: userId,
                                    isDeleted: false,
                                });
                                await this.userRoleRepository.save(userRole);
                                this.logger.log(`Assigned new role ${role.name} to user ${id}`);
                            }
                        }
                        catch (error) {
                            this.logger.warn(`Failed to assign role ${role.name} to user ${id}: ${error.message}`);
                        }
                    }
                }
                if (updateUserDto.referredByCode && !user.parentId) {
                    const referrer = await this.userRepository.findOne({
                        where: { referralCode: updateUserDto.referredByCode },
                    });
                    if (!referrer) {
                        throw new common_1.BadRequestException(`Invalid referral code: ${updateUserDto.referredByCode}`);
                    }
                    if (referrer.id === id) {
                        throw new common_1.BadRequestException('User cannot refer themselves');
                    }
                    updateUserDto.parentId = referrer.id;
                    updateUserDto.path = `${referrer.id}.${id}`;
                }
                else if (updateUserDto.referredById && !user.parentId) {
                    const referrer = await this.userRepository.findOne({
                        where: { id: updateUserDto.referredById },
                    });
                    if (!referrer) {
                        throw new common_1.BadRequestException(`Invalid referrer ID: ${updateUserDto.referredById}`);
                    }
                    if (referrer.id === id) {
                        throw new common_1.BadRequestException('User cannot refer themselves');
                    }
                    updateUserDto.path = `${updateUserDto.referredById}.${id}`;
                }
                delete updateUserDto.referredByCode;
                delete updateUserDto.referredById;
                const { version, tokenAssets, roles, referredByCode, referredById, ...updateFields } = updateUserDto;
                const filteredUpdateFields = Object.fromEntries(Object.entries(updateFields).filter(([_, value]) => value !== undefined));
                const updateData = {
                    ...filteredUpdateFields,
                    updatedBy: userId,
                };
                let savedUser;
                if (updateUserDto.version !== undefined) {
                    savedUser = await this.optimisticLockingService.updateWithOptimisticLock(this.userRepository, id, updateData, updateUserDto.version, userId);
                }
                else {
                    Object.assign(user, updateData);
                    savedUser = await this.userRepository.save(user);
                }
                if (savedUser.parentId && (!user.parentId || user.parentId !== savedUser.parentId)) {
                    try {
                        const referrer = await this.userRepository.findOne({
                            where: { id: savedUser.parentId },
                        });
                        if (referrer) {
                            await this.userRepository.manager
                                .createQueryBuilder()
                                .relation(user_entity_1.User, 'referredBy')
                                .of(savedUser.id)
                                .set(referrer);
                            this.logger.log(`Set referredBy relationship for user ${savedUser.id} to ${savedUser.parentId}`);
                        }
                    }
                    catch (error) {
                        this.logger.error(`Failed to set referredBy relationship: ${error.message}`, error.stack);
                    }
                }
                const dto = this.convertToDto(savedUser);
                this.userEventsGateway.emitUserUpdated(dto);
                this.eventEmitter.emit(base_user_service_1.EVENT_USER_UPDATED, {
                    userId: dto.id,
                    newData: dto,
                    oldData: user,
                });
                this.logger.log(`Successfully updated user with ID: ${id}`);
                return dto;
            }
            catch (error) {
                this.logger.error(`Failed to update user ID ${id}: ${error.message}`, error.stack);
                if (error instanceof common_1.NotFoundException ||
                    error instanceof common_1.BadRequestException)
                    throw error;
                throw new common_1.InternalServerErrorException('Failed to update user.');
            }
        });
    }
    async toggleStatus(id, userId) {
        this.logger.log(`Attempting to toggle status for user ID: ${id}`);
        try {
            const user = await this.findByIdOrFail(id);
            if (typeof user.isActive === 'undefined') {
                throw new common_1.BadRequestException('Status field is missing on this user.');
            }
            user.isActive = !user.isActive;
            const updatedUser = await this.userRepository.save(user);
            const dto = this.convertToDto(updatedUser);
            this.userEventsGateway.emitStatusToggled(dto.id, dto.isActive);
            this.eventEmitter.emit(base_user_service_1.EVENT_USER_STATUS_TOGGLED, {
                userId: dto.id,
                oldStatus: !dto.isActive,
                newStatus: dto.isActive,
            });
            this.logger.log(`Successfully toggled status for user ID: ${id} to ${dto.isActive}`);
            return dto;
        }
        catch (error) {
            this.logger.error(`Failed to toggle status for user ID ${id}: ${error.message}`, error.stack);
            if (error instanceof common_1.NotFoundException ||
                error instanceof common_1.BadRequestException)
                throw error;
            throw new common_1.InternalServerErrorException('Failed to toggle status.');
        }
    }
    async updateReferralCode(id, updateReferralCodeDto) {
        this.logger.log(`Attempting to update referral code for user ID: ${id}`);
        try {
            const user = await this.findByIdOrFail(id);
            if (user.parentId) {
                throw new common_1.BadRequestException('User already has a referrer');
            }
            const referrer = await this.userRepository.findOne({
                where: { referralCode: updateReferralCodeDto.referredByCode },
            });
            if (!referrer) {
                throw new common_1.BadRequestException(`Invalid referral code: ${updateReferralCodeDto.referredByCode}`);
            }
            if (referrer.id === id) {
                throw new common_1.BadRequestException('User cannot refer themselves');
            }
            user.parentId = referrer.id;
            user.path = `${referrer.id}.${user.id}`;
            const savedUser = await this.userRepository.save(user);
            try {
                await this.userRepository.manager
                    .createQueryBuilder()
                    .relation(user_entity_1.User, 'referredBy')
                    .of(savedUser.id)
                    .set(referrer);
                this.logger.log(`Set referredBy relationship for user ${savedUser.id} to ${referrer.id}`);
            }
            catch (error) {
                this.logger.error(`Failed to set referredBy relationship: ${error.message}`, error.stack);
            }
            const dto = this.convertToDto(savedUser);
            this.userEventsGateway.emitUserUpdated(dto);
            this.eventEmitter.emit(base_user_service_1.EVENT_USER_UPDATED, {
                userId: dto.id,
                newData: dto,
                oldData: user,
            });
            this.logger.log(`Successfully updated referral code for user ID: ${id}`);
            return dto;
        }
        catch (error) {
            this.logger.error(`Failed to update referral code for user ID ${id}: ${error.message}`, error.stack);
            if (error instanceof common_1.NotFoundException ||
                error instanceof common_1.BadRequestException)
                throw error;
            throw new common_1.InternalServerErrorException('Failed to update referral code.');
        }
    }
};
exports.UpdateUserService = UpdateUserService;
__decorate([
    (0, typeorm_transactional_1.Transactional)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, update_user_dto_1.UpdateUserDto, String]),
    __metadata("design:returntype", Promise)
], UpdateUserService.prototype, "update", null);
__decorate([
    (0, typeorm_transactional_1.Transactional)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String]),
    __metadata("design:returntype", Promise)
], UpdateUserService.prototype, "toggleStatus", null);
__decorate([
    (0, typeorm_transactional_1.Transactional)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, update_referral_code_user_dto_1.UpdateReferralCodeUserDto]),
    __metadata("design:returntype", Promise)
], UpdateUserService.prototype, "updateReferralCode", null);
exports.UpdateUserService = UpdateUserService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(user_entity_1.User)),
    __param(1, (0, typeorm_1.InjectRepository)(role_entity_1.Role)),
    __param(2, (0, typeorm_1.InjectRepository)(user_role_entity_1.UserRole)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository,
        event_emitter_1.EventEmitter2,
        events_user_gateway_1.EventsUserGateway,
        optimistic_locking_service_1.OptimisticLockingService,
        connection_pool_service_1.ConnectionPoolService])
], UpdateUserService);
//# sourceMappingURL=update.user.service.js.map
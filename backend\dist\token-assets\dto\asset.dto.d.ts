import { UserDto } from '../../users/dto/user.dto';
import { EcomProduct } from '../../ecom-products/entity/ecom-products.entity';
export declare class AssetDto {
    id: string;
    productId: string;
    userId: string;
    amount: number;
    createdAt: Date;
    updatedAt: Date;
    createdBy?: string;
    updatedBy?: string;
    isDeleted: boolean;
    deletedBy?: string;
    deletedAt?: Date;
    product?: EcomProduct;
    user: UserDto;
    walletBalance?: number;
    creator: UserDto;
    updater: UserDto;
    deleter: UserDto;
}

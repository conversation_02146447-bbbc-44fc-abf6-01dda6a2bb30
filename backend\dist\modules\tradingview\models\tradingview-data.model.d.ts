export interface TradingViewData {
    symbol: string;
    timestamp: number;
    open: number;
    high: number;
    low: number;
    close: number;
    volume: number;
}
export interface TradingViewRealtimeData {
    symbol: string;
    price: number;
    timestamp: number;
    change: number;
    changePercent: number;
    bid?: number;
    ask?: number;
    high?: number;
    low?: number;
}
export interface OHLCData {
    time: number;
    open: number;
    high: number;
    low: number;
    close: number;
    volume?: number;
}
export interface ChartData {
    symbol: string;
    interval: string;
    data: OHLCData[];
}
export declare enum ChartInterval {
    ONE_MINUTE = "1m",
    FIVE_MINUTES = "5m",
    FIFTEEN_MINUTES = "15m",
    THIRTY_MINUTES = "30m",
    ONE_HOUR = "1h",
    FOUR_HOURS = "4h",
    ONE_DAY = "1d",
    ONE_WEEK = "1w",
    ONE_MONTH = "1M"
}
export interface CurrentPrice {
    symbol: string;
    price: number;
    timestamp: number;
    change: number;
    changePercent: number;
    bid?: number;
    ask?: number;
    spread?: number;
    high?: number;
    low?: number;
}
export interface WebSocketMessage {
    type: WebSocketMessageType;
    data: any;
}
export declare enum WebSocketMessageType {
    PRICE_UPDATE = "price_update",
    CHART_DATA = "chart_data",
    ERROR = "error",
    AUTH = "auth",
    SUBSCRIBE = "subscribe",
    UNSUBSCRIBE = "unsubscribe"
}
export interface SubscriptionRequest {
    symbol: string;
    interval?: ChartInterval;
}
export interface SubscriptionResponse {
    success: boolean;
    message?: string;
    subscriptionId?: string;
}
export declare enum TradingViewConfigKeys {
    API_KEY = "TRADINGVIEW_API_KEY",
    API_SECRET = "TRADINGVIEW_API_SECRET",
    BASE_URL = "TRADINGVIEW_BASE_URL",
    WS_ENDPOINT = "TRADINGVIEW_WS_ENDPOINT",
    RATE_LIMIT = "TRADINGVIEW_RATE_LIMIT",
    RATE_LIMIT_WINDOW = "TRADINGVIEW_RATE_LIMIT_WINDOW",
    CACHE_TTL = "TRADINGVIEW_CACHE_TTL",
    POLLING_INTERVAL = "TRADINGVIEW_POLLING_INTERVAL",
    DEFAULT_SYMBOL = "TRADINGVIEW_DEFAULT_SYMBOL"
}

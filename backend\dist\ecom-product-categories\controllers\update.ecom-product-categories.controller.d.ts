import { UpdateEcomProductCategoriesService } from '../services/update.ecom-product-categories.service';
import { ReadEcomProductCategoriesService } from '../services/read.ecom-product-categories.service';
import { EcomProductCategoryDto } from '../dto/ecom-product-category.dto';
import { UpdateEcomProductCategoryDto } from '../dto/update-ecom-product-category.dto';
declare class UpdateCategoryStatusDto {
    isActive: boolean;
}
export declare class UpdateEcomProductCategoriesController {
    private readonly ecomProductCategoriesService;
    private readonly readEcomProductCategoriesService;
    constructor(ecomProductCategoriesService: UpdateEcomProductCategoriesService, readEcomProductCategoriesService: ReadEcomProductCategoriesService);
    updatePut(id: string, updateEcomProductCategoryDto: UpdateEcomProductCategoryDto, userId: string): Promise<EcomProductCategoryDto>;
    update(id: string, updateEcomProductCategoryDto: UpdateEcomProductCategoryDto, userId: string): Promise<EcomProductCategoryDto>;
    updateStatus(id: string, updateStatusDto: UpdateCategoryStatusDto, userId: string): Promise<EcomProductCategoryDto>;
}
export {};

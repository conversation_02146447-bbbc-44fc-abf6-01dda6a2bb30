"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UpdateCmsCategoriesService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const event_emitter_1 = require("@nestjs/event-emitter");
const typeorm_transactional_1 = require("typeorm-transactional");
const base_cms_categories_service_1 = require("./base.cms-categories.service");
const slug_cms_categories_service_1 = require("./slug.cms-categories.service");
const cms_categories_entity_1 = require("../entity/cms-categories.entity");
const update_cms_category_dto_1 = require("../dto/update.cms-category.dto");
let UpdateCmsCategoriesService = class UpdateCmsCategoriesService extends base_cms_categories_service_1.BaseCmsCategoriesService {
    categoryRepository;
    dataSource;
    eventEmitter;
    slugService;
    constructor(categoryRepository, dataSource, eventEmitter, slugService) {
        super(categoryRepository, dataSource, eventEmitter);
        this.categoryRepository = categoryRepository;
        this.dataSource = dataSource;
        this.eventEmitter = eventEmitter;
        this.slugService = slugService;
    }
    async update(id, updateDto, userId) {
        try {
            this.logger.debug(`Đang cập nhật chuyên mục CMS với ID: ${id}`);
            const category = await this.findById(id, ['parent']);
            if (!category) {
                throw new common_1.NotFoundException(`Không tìm thấy chuyên mục với ID: ${id}`);
            }
            const oldData = this.toDto(category);
            let newSlug = category.slug;
            const nameChanged = updateDto.name !== undefined && updateDto.name !== category.name;
            const slugChanged = updateDto.slug !== undefined && updateDto.slug !== category.slug;
            if (nameChanged || slugChanged) {
                const postType = updateDto.postType || category.postType;
                const nameToUse = updateDto.name !== undefined ? updateDto.name : category.name;
                newSlug = await this.slugService.generateUniqueSlugForUpdate(nameToUse, postType, id, updateDto.slug);
            }
            if (updateDto.name !== undefined) {
                category.name = updateDto.name;
            }
            if (nameChanged || slugChanged) {
                category.slug = newSlug;
            }
            if (updateDto.description !== undefined) {
                category.description = updateDto.description;
            }
            if (updateDto.postType !== undefined) {
                category.postType = updateDto.postType;
            }
            if (updateDto.imageUrl !== undefined) {
                category.imageUrl = updateDto.imageUrl;
            }
            if (updateDto.metaTitle !== undefined) {
                category.metaTitle = updateDto.metaTitle;
            }
            if (updateDto.metaDescription !== undefined) {
                category.metaDescription = updateDto.metaDescription;
            }
            if (updateDto.metaKeywords !== undefined) {
                category.metaKeywords = updateDto.metaKeywords;
            }
            if (updateDto.status !== undefined) {
                category.status = updateDto.status;
            }
            if (updateDto.parentId !== undefined) {
                if (updateDto.parentId === null) {
                    category.parent = null;
                }
                else if (updateDto.parentId !== category.parent?.id) {
                    const parent = await this.findById(updateDto.parentId);
                    if (!parent) {
                        throw new common_1.NotFoundException(`Không tìm thấy chuyên mục cha với ID: ${updateDto.parentId}`);
                    }
                    if (parent.id === id) {
                        throw new common_1.BadRequestException('Chuyên mục không thể là chuyên mục cha của chính nó');
                    }
                    const isChild = await this.isChildOf(parent.id, id);
                    if (isChild) {
                        throw new common_1.BadRequestException('Không thể chọn chuyên mục con làm chuyên mục cha');
                    }
                    category.parent = parent;
                }
            }
            category.updatedBy = userId;
            const updatedCategory = await this.categoryRepository.save(category);
            const categoryDto = this.toDto(updatedCategory);
            if (!categoryDto) {
                throw new common_1.InternalServerErrorException('Không thể chuyển đổi entity thành DTO');
            }
            this.eventEmitter.emit(this.EVENT_CATEGORY_UPDATED, {
                categoryId: categoryDto.id,
                userId,
                oldData,
                newData: categoryDto,
            });
            return categoryDto;
        }
        catch (error) {
            this.logger.error(`Lỗi khi cập nhật chuyên mục CMS: ${error.message}`, error.stack);
            if (error instanceof common_1.BadRequestException || error instanceof common_1.NotFoundException || error instanceof common_1.ConflictException) {
                throw error;
            }
            throw new common_1.InternalServerErrorException(`Không thể cập nhật chuyên mục CMS: ${error.message}`);
        }
    }
    async bulkUpdate(updates, userId) {
        try {
            this.logger.debug(`Đang cập nhật ${updates.length} chuyên mục CMS`);
            const updatedCategories = [];
            for (const update of updates) {
                const category = await this.update(update.id, update.data, userId);
                if (category) {
                    updatedCategories.push(category);
                }
            }
            return updatedCategories;
        }
        catch (error) {
            this.logger.error(`Lỗi khi cập nhật nhiều chuyên mục CMS: ${error.message}`, error.stack);
            throw new common_1.InternalServerErrorException(`Không thể cập nhật nhiều chuyên mục CMS: ${error.message}`);
        }
    }
    async updateStatus(id, status, userId) {
        try {
            this.logger.debug(`Đang cập nhật trạng thái chuyên mục CMS với ID: ${id} thành ${status}`);
            const updateDto = { status: status };
            return this.update(id, updateDto, userId);
        }
        catch (error) {
            this.logger.error(`Lỗi khi cập nhật trạng thái chuyên mục CMS: ${error.message}`, error.stack);
            if (error instanceof common_1.NotFoundException) {
                throw error;
            }
            throw new common_1.InternalServerErrorException(`Không thể cập nhật trạng thái chuyên mục CMS: ${error.message}`);
        }
    }
};
exports.UpdateCmsCategoriesService = UpdateCmsCategoriesService;
__decorate([
    (0, typeorm_transactional_1.Transactional)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, update_cms_category_dto_1.UpdateCmsCategoryDto, String]),
    __metadata("design:returntype", Promise)
], UpdateCmsCategoriesService.prototype, "update", null);
__decorate([
    (0, typeorm_transactional_1.Transactional)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Array, String]),
    __metadata("design:returntype", Promise)
], UpdateCmsCategoriesService.prototype, "bulkUpdate", null);
__decorate([
    (0, typeorm_transactional_1.Transactional)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String, String]),
    __metadata("design:returntype", Promise)
], UpdateCmsCategoriesService.prototype, "updateStatus", null);
exports.UpdateCmsCategoriesService = UpdateCmsCategoriesService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(cms_categories_entity_1.CmsCategories)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        typeorm_2.DataSource,
        event_emitter_1.EventEmitter2,
        slug_cms_categories_service_1.SlugCmsCategoriesService])
], UpdateCmsCategoriesService);
//# sourceMappingURL=update.cms-categories.service.js.map
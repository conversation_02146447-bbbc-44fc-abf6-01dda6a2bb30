'use client';

import { type ColumnDef, Row } from "@tanstack/react-table";
import { EcomProduct } from "../type/ecom-product";
import { Badge } from "@/components/ui/badge";
import { Eye, ArrowUpDown, User, Edit, Trash2, Check, CheckIcon, Image } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuSeparator, DropdownMenuShortcut, DropdownMenuTrigger } from "@/components/ui/dropdown-menu";
import { IconDotsVertical } from "@tabler/icons-react";
import { UserHoverCard } from "@/components/common/user/user-hover-card";
import { DateTimeDisplay } from "@/components/ui/date-time-display";
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { useState } from "react";
import { api } from '@/lib/api';
import { toast } from "sonner";
import {
    Command,
    CommandEmpty,
    CommandGroup,
    CommandInput,
    CommandItem,
    CommandList,
} from '@/components/ui/command';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";

/**
 * Hàm lấy màu cho trạng thái sản phẩm
 */
const getStatusColor = (isActive: boolean) => {
    return isActive ? 'bg-green-100 text-green-800 font-normal' : 'bg-gray-100 text-gray-800 font-normal';
};

// Status options for the dropdown
const statusOptions = [
    { id: 'true', name: 'Hoạt động', className: 'bg-green-100 text-green-800 font-normal' },
    { id: 'false', name: 'Vô hiệu hóa', className: 'bg-gray-100 text-gray-800 font-normal' },
];

/**
 * Component hiển thị và chọn trạng thái sản phẩm
 */
function ProductStatusSelector({
    isActive,
    onStatusChange
}: {
    isActive: boolean;
    onStatusChange: (newStatus: boolean) => void;
}) {
    const [open, setOpen] = useState<boolean>(false);
    const currentValue = isActive.toString();

    const currentOption = statusOptions.find(option => option.id === currentValue);

    const handleStatusChange = (value: string) => {
        onStatusChange(value === 'true');
        setOpen(false);
    };

    return (
        <Popover open={open} onOpenChange={setOpen}>
            <PopoverTrigger asChild>
                <Button
                    className="p-0 h-auto"
                    variant="ghost"
                    role="combobox"
                    aria-expanded={open}
                >
                    {currentOption ? (
                        <Badge className={currentOption.className}>
                            {currentOption.name}
                        </Badge>
                    ) : (
                        <Badge className="bg-gray-100 text-gray-800">
                            ---
                        </Badge>
                    )}
                </Button>
            </PopoverTrigger>
            <PopoverContent className="p-0 w-48" align="center">
                <Command>
                    <CommandInput placeholder="Tìm trạng thái..." />
                    <CommandList>
                        <CommandEmpty>Không tìm thấy trạng thái.</CommandEmpty>
                        <CommandGroup>
                            {statusOptions.map((option) => (
                                <CommandItem
                                    key={option.id}
                                    value={option.id}
                                    onSelect={() => handleStatusChange(option.id)}
                                    className="flex items-center justify-between"
                                >
                                    <div className="flex items-center gap-2">
                                        <Badge className={option.className}>
                                            {option.name}
                                        </Badge>
                                    </div>
                                    {option.id === currentValue && (
                                        <Check size={14} className="ml-auto" />
                                    )}
                                </CommandItem>
                            ))}
                        </CommandGroup>
                    </CommandList>
                </Command>
            </PopoverContent>
        </Popover>
    );
}

/**
 * Component hiển thị các hành động cho mỗi dòng
 */
interface ActionsProps {
    row: Row<EcomProduct>;
    onViewDetail: (product: EcomProduct) => void;
    onDelete: (product: EcomProduct) => void;
    onEdit: (product: EcomProduct) => void;
}

function Actions({ row, onViewDetail, onDelete, onEdit }: ActionsProps) {
    const [showDeleteDialog, setShowDeleteDialog] = useState(false);
    const product = row.original;

    // Xử lý khi xác nhận xóa
    const handleDelete = (product: EcomProduct) => {
        onDelete(product);
        setShowDeleteDialog(false);
    };

    // Xử lý khi click vào nút xem chi tiết
    const handleViewDetail = async (product: EcomProduct) => {
        try {
            // Sử dụng api client để gọi API lấy thông tin chi tiết
            const response = await api.get<EcomProduct>(`ecom-products/${product.id}?relations=category,creator,updater,deleter`);

            // Chỉ truyền dữ liệu chi tiết nếu API trả về thành công
            if (response) {
                onViewDetail(response);
            } else {
                // Nếu không có response, hiển thị thông báo lỗi
                toast.error("Không thể tải thông tin chi tiết sản phẩm. Vui lòng thử lại sau.");
            }
        } catch (error) {
            console.error('Error fetching product detail:', error);
            // Hiển thị thông báo lỗi cho người dùng
            toast.error("Có lỗi xảy ra khi tải thông tin sản phẩm. Vui lòng thử lại sau.");
        }
    };

    // Xử lý khi click vào nút chỉnh sửa
    const handleEdit = async (product: EcomProduct) => {
        try {
            // Sử dụng api client để gọi API lấy thông tin chi tiết
            const response = await api.get<EcomProduct>(`ecom-products/${product.id}?relations=category,creator,updater,deleter`);

            // Chỉ truyền dữ liệu chi tiết nếu API trả về thành công
            if (response) {
                onEdit(response);
            } else {
                // Nếu không có response, hiển thị thông báo lỗi
                toast.error("Không thể tải thông tin chi tiết sản phẩm. Vui lòng thử lại sau.");
            }
        } catch (error) {
            console.error('Error fetching product detail for edit:', error);
            // Hiển thị thông báo lỗi cho người dùng
            toast.error("Có lỗi xảy ra khi tải thông tin sản phẩm. Vui lòng thử lại sau.");
        }
    };

    return (
        <>
            {/* Dialog xác nhận xóa */}
            <Dialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
                <DialogContent>
                    <DialogHeader>
                        <DialogTitle>Xác nhận xóa</DialogTitle>
                        <DialogDescription>
                            Bạn có chắc chắn muốn xóa sản phẩm <span className="font-medium">{product.productName}</span>?
                            <p className="mt-2">Hành động này không thể hoàn tác.</p>
                        </DialogDescription>
                    </DialogHeader>
                    <DialogFooter>
                        <Button variant="outline" onClick={() => setShowDeleteDialog(false)}>Hủy</Button>
                        <Button variant="destructive" onClick={() => handleDelete(product)}>Xóa</Button>
                    </DialogFooter>
                </DialogContent>
            </Dialog>
            <div className="flex justify-end px-1 sticky-action-cell h-full items-center">
                <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                        <Button
                            variant="ghost"
                            size="sm"
                            className="flex h-6 w-6 p-0 data-[state=open]:bg-muted transition-colors hover:bg-muted"
                        >
                            <IconDotsVertical className="h-3 w-3" />
                            <span className="sr-only">Mở menu</span>
                        </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end" className="p-1">
                        <DropdownMenuItem onClick={() => handleViewDetail(product)}>
                            <Eye className="mr-2 h-3.5 w-3.5" />
                            <span className="flex-1 text-sm">Chi tiết</span>
                            <DropdownMenuShortcut className="text-sm">⌘V</DropdownMenuShortcut>
                        </DropdownMenuItem>
                        <DropdownMenuItem onClick={() => handleEdit(product)}>
                            <Edit className="mr-2 h-3.5 w-3.5" />
                            <span className="flex-1 text-sm">Chỉnh sửa</span>
                            <DropdownMenuShortcut className="text-sm">⌘E</DropdownMenuShortcut>
                        </DropdownMenuItem>
                        <DropdownMenuSeparator />
                        <DropdownMenuItem
                            onClick={() => setShowDeleteDialog(true)}
                            className="text-destructive focus:text-destructive"
                        >
                            <Trash2 className="mr-2 h-3.5 w-3.5 text-destructive" />
                            <span className="flex-1 text-sm text-destructive">Xóa</span>
                            <DropdownMenuShortcut className="text-sm text-destructive">⌫</DropdownMenuShortcut>
                        </DropdownMenuItem>
                    </DropdownMenuContent>
                </DropdownMenu>
            </div>
        </>
    );
}

/**
 * Props cho component cột
 */
interface ColumnsProps {
    onViewDetail: (product: EcomProduct) => void;
    onDelete: (product: EcomProduct) => void;
    onEdit: (product: EcomProduct) => void;
    onUpdateProductStatus?: (productId: string, isActive: boolean) => void;
}

/**
 * Hàm tạo cột cho bảng sản phẩm
 */
export function getEcomProductCell({
    onViewDetail,
    onDelete,
    onEdit,
    onUpdateProductStatus,
}: ColumnsProps): ColumnDef<EcomProduct>[] {
    return [
        {
            accessorKey: "productName",
            header: ({ column }) => {
                return (
                    <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
                        className="flex items-center gap-1 px-0 h-6 py-0"
                        data-column-id="productName"
                    >
                        <span className="text-xs">Tên sản phẩm</span>
                        <ArrowUpDown className="ml-1 h-3 w-3" />
                    </Button>
                )
            },
            meta: {
                header: "Tên sản phẩm"
            },
            cell: ({ row }) => {
                const product = row.original;
                return (
                    <TooltipProvider>
                        <Tooltip>
                            <TooltipTrigger asChild>
                                <div className="flex flex-col max-w-[250px]">
                                    <span className="text-sm font-medium truncate">{product.productName || '---'}</span>
                                    <span className="text-xs text-muted-foreground truncate">{product.productCode || '---'}</span>
                                </div>
                            </TooltipTrigger>
                            <TooltipContent>
                                <p>{product.productName}</p>
                                <p className="text-xs text-muted-foreground">{product.productCode}</p>
                            </TooltipContent>
                        </Tooltip>
                    </TooltipProvider>
                );
            },
            enableSorting: true,
            size: 300,
            sortingFn: (rowA, rowB) => {
                const productNameA = rowA.original.productName || '';
                const productNameB = rowB.original.productName || '';
                return productNameA.localeCompare(productNameB);
            },
        },
        {
            accessorKey: "productCode",
            header: ({ column }) => {
                return (
                    <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
                        className="flex items-center gap-1 px-0 h-6 py-0"
                        data-column-id="productCode"
                    >
                        <span className="text-xs">Mã sản phẩm</span>
                        <ArrowUpDown className="ml-1 h-3 w-3" />
                    </Button>
                )
            },
            meta: {
                header: "Mã sản phẩm"
            },
            cell: ({ row }) => {
                const productCode = row.getValue("productCode") as string;
                return (
                    <span className="text-sm">{productCode || '---'}</span>
                );
            },
            enableSorting: true,
            size: 150,
        },
        {
            accessorKey: "category.name",
            header: ({ column }) => {
                return (
                    <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
                        className="flex items-center gap-1 px-0 h-6 py-0"
                        data-column-id="category"
                    >
                        <span className="text-xs">Danh mục</span>
                        <ArrowUpDown className="ml-1 h-3 w-3" />
                    </Button>
                )
            },
            meta: {
                header: "Danh mục"
            },
            cell: ({ row }) => {
                const product = row.original;
                const category = product.category;
                if (!category || !category.name) return <span className="text-sm text-muted-foreground">---</span>;

                return (
                    <span className="text-sm">{category.name}</span>
                );
            },
            enableSorting: true,
            size: 200,
        },
        {
            accessorKey: "imageUrl",
            header: ({ column }) => {
                return (
                    <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
                        className="flex items-center gap-1 px-0 h-6 py-0"
                        data-column-id="imageUrl"
                    >
                        <span className="text-xs">Hình ảnh</span>
                        <ArrowUpDown className="ml-1 h-3 w-3" />
                    </Button>
                )
            },
            meta: {
                header: "Hình ảnh"
            },
            cell: ({ row }) => {
                const product = row.original;
                const imageUrl = product.imageUrl;

                if (imageUrl) {
                    return (
                        <div className="w-10 h-10 overflow-hidden rounded">
                            <img
                                src={imageUrl}
                                alt={product.productName}
                                className="w-full h-full object-cover"
                            />
                        </div>
                    );
                }
                return <div className="text-sm text-muted-foreground">---</div>;
            },
            enableSorting: true,
            size: 100,
        },
        {
            accessorKey: "weight",
            header: ({ column }) => {
                return (
                    <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
                        className="flex items-center gap-1 px-0 h-6 py-0"
                        data-column-id="weight"
                    >
                        <span className="text-xs">Oz</span>
                        <ArrowUpDown className="ml-1 h-3 w-3" />
                    </Button>
                )
            },
            meta: {
                header: "Oz"
            },
            cell: ({ row }) => {
                const weight = row.getValue("weight") as number;
                return (
                    <span className="text-sm">{weight || '---'}</span>
                );
            },
            enableSorting: true,
            size: 50,
        },
        {
            accessorKey: "isActive",
            header: ({ column }) => {
                return (
                    <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
                        className="flex items-center gap-1 px-0 h-6 py-0"
                        data-column-id="isActive"
                    >
                        <span className="text-xs">Trạng thái</span>
                        <ArrowUpDown className="ml-1 h-3 w-3" />
                    </Button>
                )
            },
            meta: {
                header: "Trạng thái"
            },
            cell: ({ row }) => {
                const isActive = row.getValue("isActive") as boolean;
                const productId = row.original.id;

                if (onUpdateProductStatus) {
                    return (
                        <ProductStatusSelector
                            isActive={isActive}
                            onStatusChange={(newStatus: boolean) => onUpdateProductStatus(productId, newStatus)}
                        />
                    );
                }

                return <Badge className={getStatusColor(isActive)}>{isActive ? 'Hoạt động' : 'Không hoạt động'}</Badge>;
            },
            enableSorting: true,
            size: 120,
        },
        {
            accessorKey: "createdAt",
            header: ({ column }) => {
                return (
                    <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
                        className="flex items-center gap-1 px-0 h-6 py-0"
                        data-column-id="createdAt"
                    >
                        <span className="text-xs">Ngày tạo</span>
                        <ArrowUpDown className="ml-1 h-3 w-3" />
                    </Button>
                )
            },
            meta: {
                header: "Ngày tạo"
            },
            cell: ({ row }) => {
                const date = row.getValue("createdAt") as string
                return (
                    <DateTimeDisplay
                        date={date}
                        className="text-sm"
                        timeClassName="text-xs text-muted-foreground"
                    />
                )
            },
            enableSorting: true,
            size: 150,
        },
        {
            accessorKey: "creator",
            header: ({ column }) => {
                return (
                    <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
                        className="flex items-center gap-1 px-0 h-6 py-0"
                        data-column-id="creator"
                    >
                        <span className="text-xs">Người tạo</span>
                        <ArrowUpDown className="ml-1 h-3 w-3" />
                    </Button>
                )
            },
            meta: {
                header: "Người tạo"
            },
            cell: ({ row }) => {
                const product = row.original;

                // Kiểm tra cả hai trường hợp: creator là object hoặc createdBy là string
                const hasCreator = product.creator && typeof product.creator === 'object';
                const hasCreatedBy = product.createdBy && typeof product.createdBy === 'string';

                // Nếu có thông tin user, hiển thị thông tin chi tiết
                if (hasCreator) {
                    return (
                        <UserHoverCard user={product.creator} showAvatar={true} size="sm">
                            <div className="max-w-[120px] overflow-hidden">
                                <div className="text-sm truncate">{product.creator?.fullName || product.creator?.username || 'User'}</div>
                                <div className="text-xs text-muted-foreground truncate">{product.creator?.email || ''}</div>
                            </div>
                        </UserHoverCard>
                    );
                }

                // Nếu chỉ có ID, hiển thị ID rút gọn
                if (hasCreatedBy) {
                    return (
                        <UserHoverCard userId={product.createdBy as string} showAvatar={true} size="sm">
                            <div className="flex items-center gap-1">
                                <User className="h-4 w-4 text-blue-500" />
                                <span className="text-sm text-muted-foreground">
                                    {product.createdBy?.substring(0, 8)}...
                                </span>
                            </div>
                        </UserHoverCard>
                    );
                }

                // Nếu không có thông tin, hiển thị ---
                return (
                    <span className="text-xs text-muted-foreground">---</span>
                );
            },
            enableSorting: true,
            enableHiding: true,
        },
        {
            accessorKey: "updatedAt",
            header: ({ column }) => {
                return (
                    <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
                        className="flex items-center gap-1 px-0 h-6 py-0"
                        data-column-id="updatedAt"
                    >
                        <span className="text-xs">Ngày cập nhật</span>
                        <ArrowUpDown className="ml-1 h-3 w-3" />
                    </Button>
                )
            },
            meta: {
                header: "Ngày cập nhật"
            },
            cell: ({ row }) => {
                const token = row.original;
                return (
                    <DateTimeDisplay
                        date={token.updatedAt}
                        className="text-sm"
                        timeClassName="text-xs text-muted-foreground"
                    />
                );
            },
            enableSorting: true,
            size: 150,
        },
        {
            accessorKey: "updater",
            header: ({ column }) => {
                return (
                    <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
                        className="flex items-center gap-1 px-0 h-6 py-0"
                        data-column-id="updater"
                    >
                        <span className="text-xs">Người cập nhật</span>
                        <ArrowUpDown className="ml-1 h-3 w-3" />
                    </Button>
                )
            },
            meta: {
                header: "Người cập nhật"
            },
            cell: ({ row }) => {
                const product = row.original;

                // Kiểm tra cả hai trường hợp: updater là object hoặc updatedBy là string
                const hasUpdater = product.updater && typeof product.updater === 'object';
                const hasUpdatedBy = product.updatedBy && typeof product.updatedBy === 'string';

                // Nếu có thông tin user, hiển thị thông tin chi tiết
                if (hasUpdater) {
                    return (
                        <UserHoverCard user={product.updater} showAvatar={true} size="sm">
                            <div className="max-w-[120px] overflow-hidden">
                                <div className="text-sm truncate">{product.updater?.fullName || product.updater?.username || 'User'}</div>
                                <div className="text-xs text-muted-foreground truncate">{product.updater?.email || ''}</div>
                            </div>
                        </UserHoverCard>
                    );
                }

                // Nếu chỉ có ID, hiển thị ID rút gọn
                if (hasUpdatedBy) {
                    return (
                        <UserHoverCard userId={product.updatedBy as string} showAvatar={true} size="sm">
                            <div className="flex items-center gap-1">
                                <User className="h-4 w-4 text-blue-500" />
                                <span className="text-sm text-muted-foreground">
                                    {product.updatedBy?.substring(0, 8)}...
                                </span>
                            </div>
                        </UserHoverCard>
                    );
                }

                // Nếu không có thông tin, hiển thị ---
                return (
                    <span className="text-xs text-muted-foreground">---</span>
                );
            },
            enableSorting: true,
            enableHiding: true,
        },

        {
            id: 'actions',
            size: 40,
            enableHiding: false,
            header: () => <div data-column-id="actions"></div>,
            cell: ({ row }) => <Actions row={row} onViewDetail={onViewDetail} onDelete={onDelete} onEdit={onEdit} />,
            meta: {
                isSticky: true, // Đánh dấu cột này là cố định
                position: 'right', // Vị trí cố định (right hoặc left)
                header: "Thao tác"
            }
        }
    ];
}
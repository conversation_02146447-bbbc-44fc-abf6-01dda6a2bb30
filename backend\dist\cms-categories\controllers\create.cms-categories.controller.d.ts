import { CreateCmsCategoriesService } from '../services/create.cms-categories.service';
import { CmsCategoryDto } from '../dto/cms-category.dto';
import { CreateCmsCategoryDto } from '../dto/create.cms-category.dto';
export declare class CreateCmsCategoriesController {
    private readonly cmsCategoriesService;
    constructor(cmsCategoriesService: CreateCmsCategoriesService);
    create(createCmsCategoryDto: CreateCmsCategoryDto, userId: string): Promise<CmsCategoryDto>;
    bulkCreate(createCmsCategoryDtos: CreateCmsCategoryDto[], userId: string): Promise<CmsCategoryDto[]>;
    duplicate(id: string, userId: string): Promise<CmsCategoryDto>;
}

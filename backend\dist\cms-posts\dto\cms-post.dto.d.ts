import { UserDto } from '../../users/dto/user.dto';
import { CmsCategoryDto } from '../../cms-categories/dto/cms-category.dto';
import { CmsPostType, CmsPostStatus } from '../entity/cms-posts.entity';
export declare class CmsPostDto {
    id: string;
    businessCode: string;
    postType: CmsPostType;
    title: string;
    slug: string;
    excerpt?: string;
    content: string;
    featuredImageUrl?: string;
    status: CmsPostStatus;
    publishedAt?: Date;
    eventStartDate?: Date;
    eventEndDate?: Date;
    eventLocation?: string;
    viewCount: number;
    metaTitle?: string;
    metaDescription?: string;
    metaKeywords?: string;
    allowComments: boolean;
    categoryId?: string;
    category?: CmsCategoryDto;
    authorId: string;
    author: UserDto;
    createdAt: Date;
    updatedAt: Date;
    createdBy?: string;
    updatedBy?: string;
    deletedBy?: string;
    isDeleted: boolean;
    deletedAt?: Date;
    creator?: UserDto;
    updater?: UserDto;
    deleter?: UserDto;
}

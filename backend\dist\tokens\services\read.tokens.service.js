"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ReadTokensService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("typeorm");
const typeorm_2 = require("@nestjs/typeorm");
const event_emitter_1 = require("@nestjs/event-emitter");
const base_tokens_service_1 = require("./base.tokens.service");
const token_entity_1 = require("../entities/token.entity");
let ReadTokensService = class ReadTokensService extends base_tokens_service_1.BaseTokensService {
    tokenRepository;
    eventEmitter;
    constructor(tokenRepository, eventEmitter) {
        super(tokenRepository, eventEmitter);
        this.tokenRepository = tokenRepository;
        this.eventEmitter = eventEmitter;
    }
    async findAll(params) {
        const { limit = 10, page = 1, sortBy = 'createdAt', sortOrder = 'DESC', filter, search, relations = [], } = params;
        const skip = (page - 1) * limit;
        try {
            this.logger.debug(`Đang tìm tất cả token với tham số: ${JSON.stringify(params)}`);
            const relationsWithUsers = [...relations];
            if (!relationsWithUsers.includes('creator'))
                relationsWithUsers.push('creator');
            if (!relationsWithUsers.includes('updater'))
                relationsWithUsers.push('updater');
            if (!relationsWithUsers.includes('deleter'))
                relationsWithUsers.push('deleter');
            if (!relationsWithUsers.includes('category'))
                relationsWithUsers.push('category');
            const validatedRelations = this.validateRelations(relationsWithUsers);
            const query = this.tokenRepository
                .createQueryBuilder('token')
                .leftJoinAndSelect('token.category', 'category')
                .leftJoinAndSelect('category.creator', 'categoryCreator')
                .leftJoinAndSelect('category.updater', 'categoryUpdater')
                .leftJoinAndSelect('category.deleter', 'categoryDeleter')
                .leftJoinAndSelect('token.creator', 'creator')
                .leftJoinAndSelect('token.updater', 'updater')
                .leftJoinAndSelect('token.deleter', 'deleter')
                .where({ isDeleted: false });
            if (search) {
                query.andWhere(new typeorm_1.Brackets(qb => {
                    qb.where('LOWER(token.tokenName) LIKE LOWER(:search)', { search: `%${search}%` })
                        .orWhere('LOWER(token.tokenCode) LIKE LOWER(:search)', { search: `%${search}%` })
                        .orWhere('LOWER(token.description) LIKE LOWER(:search)', { search: `%${search}%` });
                }));
            }
            if (filter) {
                const whereClause = this.buildWhereClause(filter);
                Object.entries(whereClause).forEach(([key, value]) => {
                    if (key !== 'isDeleted') {
                        query.andWhere(`token.${key} = :${key}`, { [key]: value });
                    }
                });
            }
            if (validatedRelations.length > 0) {
                const defaultRelations = ['category', 'creator', 'updater', 'deleter', 'category.creator', 'category.updater', 'category.deleter'];
                validatedRelations
                    .filter(relation => !defaultRelations.includes(relation))
                    .forEach(relation => {
                    if (!query.expressionMap.joinAttributes.some(join => join.entityOrProperty === `token.${relation}`)) {
                        query.leftJoinAndSelect(`token.${relation}`, relation);
                    }
                });
            }
            const total = await query.getCount();
            query.orderBy(`token.${sortBy}`, sortOrder);
            query.skip(skip).take(limit);
            const tokens = await query.getMany();
            const tokenDtos = tokens.map(token => this.toDto(token));
            return {
                data: tokenDtos,
                total,
            };
        }
        catch (error) {
            this.logger.error(`Lỗi khi tìm token: ${error.message}`, error.stack);
            if (error instanceof common_1.BadRequestException) {
                throw error;
            }
            throw new common_1.InternalServerErrorException(`Không thể lấy danh sách token: ${error.message}`);
        }
    }
    async search(keyword, params) {
        try {
            this.logger.debug(`Đang tìm kiếm token với từ khóa: ${keyword}`);
            const { page = 1, limit = 10 } = params;
            const skip = (page - 1) * limit;
            const searchTerm = `%${keyword}%`;
            const query = this.tokenRepository
                .createQueryBuilder('token')
                .leftJoinAndSelect('token.category', 'category')
                .leftJoinAndSelect('category.creator', 'categoryCreator')
                .leftJoinAndSelect('category.updater', 'categoryUpdater')
                .leftJoinAndSelect('category.deleter', 'categoryDeleter')
                .leftJoinAndSelect('token.creator', 'creator')
                .leftJoinAndSelect('token.updater', 'updater')
                .leftJoinAndSelect('token.deleter', 'deleter')
                .where('token.isDeleted = :isDeleted', { isDeleted: false })
                .andWhere(new typeorm_1.Brackets((qb) => {
                qb.where('LOWER(token.tokenCode) LIKE LOWER(:searchTerm)', {
                    searchTerm,
                })
                    .orWhere('LOWER(token.tokenName) LIKE LOWER(:searchTerm)', {
                    searchTerm,
                })
                    .orWhere('LOWER(category.categoryName) LIKE LOWER(:searchTerm)', {
                    searchTerm,
                });
            }))
                .orderBy('token.createdAt', 'DESC')
                .skip(skip)
                .take(limit);
            const [tokens, total] = await query.getManyAndCount();
            const dtos = tokens.map(token => this.toDto(token));
            return { data: dtos, total };
        }
        catch (error) {
            this.logger.error(`Lỗi khi tìm kiếm token: ${error.message}`, error.stack);
            throw new common_1.InternalServerErrorException(`Không thể tìm kiếm token: ${error.message}`);
        }
    }
    async count(filter) {
        try {
            this.logger.debug(`Đang đếm số lượng token với điều kiện lọc: ${filter || 'không có'}`);
            const where = this.buildWhereClause(filter);
            return await this.tokenRepository.count({ where });
        }
        catch (error) {
            this.logger.error(`Lỗi khi đếm số lượng token: ${error.message}`, error.stack);
            throw new common_1.InternalServerErrorException(`Không thể đếm số lượng token: ${error.message}`);
        }
    }
    async findOne(id, relations = []) {
        try {
            const relationsWithUsers = [...relations];
            if (!relationsWithUsers.includes('creator'))
                relationsWithUsers.push('creator');
            if (!relationsWithUsers.includes('updater'))
                relationsWithUsers.push('updater');
            if (!relationsWithUsers.includes('deleter'))
                relationsWithUsers.push('deleter');
            if (!relationsWithUsers.includes('category'))
                relationsWithUsers.push('category');
            const token = await this.findByIdOrFail(id, relationsWithUsers);
            return this.toDto(token);
        }
        catch (error) {
            this.logger.error(`Lỗi khi tìm token: ${error.message}`, error.stack);
            if (error instanceof common_1.NotFoundException) {
                throw new common_1.NotFoundException(`Token với ID ${id} không tìm thấy`);
            }
            throw new common_1.InternalServerErrorException(`Không thể lấy thông tin token: ${error.message}`);
        }
    }
    async findOneOrFail(id, relations = []) {
        const relationsWithUsers = [...relations];
        if (!relationsWithUsers.includes('creator'))
            relationsWithUsers.push('creator');
        if (!relationsWithUsers.includes('updater'))
            relationsWithUsers.push('updater');
        if (!relationsWithUsers.includes('deleter'))
            relationsWithUsers.push('deleter');
        if (!relationsWithUsers.includes('category'))
            relationsWithUsers.push('category');
        const token = await this.findByIdOrFail(id, relationsWithUsers);
        return this.toDto(token);
    }
    async findDeleted(params) {
        try {
            this.logger.debug(`Đang tìm các token đã xóa`);
            const { limit = 10, page = 1 } = params;
            const skip = (page - 1) * limit;
            const [tokens, total] = await this.tokenRepository.findAndCount({
                where: { isDeleted: true },
                withDeleted: true,
                relations: ['creator', 'updater', 'deleter', 'category', 'category.creator', 'category.updater', 'category.deleter'],
                skip,
                take: limit,
                order: { updatedAt: 'DESC' },
            });
            return {
                data: tokens.map(token => this.toDto(token)),
                total,
            };
        }
        catch (error) {
            this.logger.error(`Lỗi khi tìm các token đã xóa: ${error.message}`, error.stack);
            throw new common_1.InternalServerErrorException(`Không thể lấy danh sách token đã xóa: ${error.message}`);
        }
    }
    async export(format) {
        try {
            this.logger.debug(`Đang xuất token ở định dạng ${format}`);
            const tokens = await this.tokenRepository.find({
                where: { isDeleted: false },
                relations: ['category', 'creator', 'updater', 'deleter', 'category.creator', 'category.updater', 'category.deleter'],
            });
            const dtos = tokens.map(token => this.toDto(token));
            if (format === 'csv') {
                if (dtos.length === 0)
                    return '';
                try {
                    const headers = Object.keys(dtos[0]).join(',');
                    const csvRows = dtos.map((row) => Object.values(row)
                        .map((value) => {
                        const strVal = String(value ?? '');
                        if (strVal.includes(',') ||
                            strVal.includes('"') ||
                            strVal.includes('\n')) {
                            return `"${strVal.replace(/"/g, '""')}"`;
                        }
                        return strVal;
                    })
                        .join(','));
                    return `${headers}\n${csvRows.join('\n')}`;
                }
                catch (csvError) {
                    this.logger.error(`Error generating CSV: ${csvError.message}`, csvError.stack);
                    throw new common_1.InternalServerErrorException('Failed to generate CSV export.');
                }
            }
            return dtos;
        }
        catch (error) {
            this.logger.error(`Lỗi khi xuất token: ${error.message}`, error.stack);
            throw new common_1.InternalServerErrorException(`Không thể xuất dữ liệu token: ${error.message}`);
        }
    }
};
exports.ReadTokensService = ReadTokensService;
exports.ReadTokensService = ReadTokensService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_2.InjectRepository)(token_entity_1.Token)),
    __metadata("design:paramtypes", [typeorm_1.Repository,
        event_emitter_1.EventEmitter2])
], ReadTokensService);
//# sourceMappingURL=read.tokens.service.js.map
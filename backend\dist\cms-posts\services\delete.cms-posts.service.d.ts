import { Repository, DataSource } from 'typeorm';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { BaseCmsPostsService } from './base.cms-posts.service';
import { CmsPosts } from '../entity/cms-posts.entity';
import { CmsPostDto } from '../dto/cms-post.dto';
export declare class DeleteCmsPostsService extends BaseCmsPostsService {
    protected readonly postRepository: Repository<CmsPosts>;
    protected readonly dataSource: DataSource;
    protected readonly eventEmitter: EventEmitter2;
    constructor(postRepository: Repository<CmsPosts>, dataSource: DataSource, eventEmitter: EventEmitter2);
    softDelete(id: string, userId: string): Promise<CmsPostDto | null>;
    restore(id: string, userId: string): Promise<CmsPostDto | null>;
    remove(id: string): Promise<{
        affected: number;
    }>;
    bulkSoftDelete(ids: string[], userId: string): Promise<CmsPostDto[]>;
    bulkRestore(ids: string[], userId: string): Promise<CmsPostDto[]>;
    bulkRemove(ids: string[]): Promise<{
        affected: number;
    }>;
}

import { CustomPaginationQueryDto } from 'src/common/dto/custom-pagination-query.dto';
import { PaginationResponseDto } from 'src/common/dto/pagination-response.dto';
import { BulkDeleteCmsPartnersDto, BulkForceDeleteCmsPartnersDto, BulkOperationResponseDto, BulkRestoreCmsPartnersDto, CmsPartnerDto } from '../dto';
import { DeleteCmsPartnersService } from '../services/delete.cms-partners.service';
import { ReadCmsPartnersService } from '../services/read.cms-partners.service';
export declare class DeleteCmsPartnersController {
    private readonly deleteCmsPartnersService;
    private readonly readCmsPartnersService;
    private readonly logger;
    constructor(deleteCmsPartnersService: DeleteCmsPartnersService, readCmsPartnersService: ReadCmsPartnersService);
    softDelete(id: string, req: any): Promise<CmsPartnerDto>;
    restore(id: string, req: any): Promise<CmsPartnerDto>;
    forceDelete(id: string, req: any): Promise<void>;
    bulkSoftDelete(bulkDeleteDto: BulkDeleteCmsPartnersDto, req: any): Promise<BulkOperationResponseDto>;
    bulkRestore(bulkRestoreDto: BulkRestoreCmsPartnersDto, req: any): Promise<BulkOperationResponseDto>;
    bulkForceDelete(bulkForceDeleteDto: BulkForceDeleteCmsPartnersDto, req: any): Promise<BulkOperationResponseDto>;
    getDeleted(paginationQuery: CustomPaginationQueryDto, req: any): Promise<PaginationResponseDto<CmsPartnerDto>>;
}

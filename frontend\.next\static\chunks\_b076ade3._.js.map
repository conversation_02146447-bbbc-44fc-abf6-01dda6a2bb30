{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/bloomgoo/gold-exchange-platform/frontend/components/common/data-table/data-table.tsx"], "sourcesContent": ["\"use client\"\r\n\r\n/**\r\n * Component DataTable cung cấp bảng dữ liệu với các tính năng:\r\n * - Header cố định khi cuộn\r\n * - Cột cố định (sticky columns) ở bên trái và bên phải\r\n * - Hỗ trợ đầy đủ các tính năng của TanStack Table\r\n *\r\n * Để sử dụng sticky columns, hãy thêm thuộc tính meta vào định nghĩa cột:\r\n * ```\r\n * {\r\n *   id: 'actions',\r\n *   meta: {\r\n *     isSticky: true,\r\n *     position: 'right' // hoặc 'left'\r\n *   }\r\n * }\r\n * ```\r\n *\r\n * Xem thêm hướng dẫn chi tiết trong file README.md\r\n */\r\n\r\nimport { flexRender, Table as TableType } from \"@tanstack/react-table\";\r\n\r\nimport { cn } from \"@/lib/utils\";\r\n\r\n// Định nghĩa kiểu dữ liệu cho thuộc tính meta của cột\r\ndeclare module '@tanstack/react-table' {\r\n  interface ColumnMeta<TData, TValue> {\r\n    isSticky?: boolean;\r\n    position?: 'left' | 'right';\r\n    header?: string;\r\n  }\r\n}\r\n\r\ninterface DataTableProps<TData> {\r\n    table: TableType<TData>\r\n    className?: string\r\n    isLoading?: boolean\r\n}\r\n\r\nexport function DataTable<TData>({\r\n    table,\r\n    className,\r\n    isLoading = false,\r\n}: DataTableProps<TData>) {\r\n    return (\r\n        <div className={cn(\"w-full flex flex-col h-full\", className)}>\r\n            {/* Table Container with border */}\r\n            <div className=\"border flex flex-col flex-1 overflow-hidden\">\r\n                {/* Single table with sticky header */}\r\n                <div className=\"table-scroll-container\">\r\n                    <div className=\"table-viewport\">\r\n                        <table className=\"data-table\">\r\n                            <thead className=\"sticky-header\">\r\n                                {table.getHeaderGroups().map((headerGroup) => (\r\n                                    <tr key={headerGroup.id}>\r\n                                        {headerGroup.headers.map((header, index) => {\r\n                                            // Determine if this is the first column (checkbox)\r\n                                            const isFirstColumn = index === 0;\r\n                                            // Determine if this column has sticky metadata\r\n                                            const hasSticky = header.column.columnDef.meta?.isSticky;\r\n\r\n                                            return (\r\n                                                <th\r\n                                                    key={header.id}\r\n                                                    style={{\r\n                                                        width: header.getSize(),\r\n                                                        minWidth: header.getSize(),\r\n                                                        maxWidth: header.getSize(),\r\n                                                        ...(hasSticky && {\r\n                                                            position: 'sticky',\r\n                                                            [header.column.columnDef.meta?.position === 'right' ? 'right' : 'left']: 0,\r\n                                                            zIndex: 60,\r\n                                                            boxShadow: header.column.columnDef.meta?.position === 'right'\r\n                                                                ? '-5px 0 5px -5px rgba(0,0,0,0.1)'\r\n                                                                : '5px 0 5px -5px rgba(0,0,0,0.1)'\r\n                                                        }),\r\n                                                        // Áp dụng kiểu dáng sticky cho cột đầu tiên (checkbox)\r\n                                                        ...(isFirstColumn && !hasSticky && {\r\n                                                            position: 'sticky',\r\n                                                            left: 0,\r\n                                                            zIndex: 60,\r\n                                                            boxShadow: '5px 0 5px -5px rgba(0,0,0,0.1)'\r\n                                                        })\r\n                                                    }}\r\n                                                    className={cn(\r\n                                                        \"bg-background py-2 text-left align-middle font-medium text-muted-foreground\",\r\n                                                        hasSticky && `sticky-${header.column.columnDef.meta?.position || 'left'}`,\r\n                                                        isFirstColumn && !hasSticky && \"sticky-left\",\r\n                                                        isFirstColumn && \"checkbox-header\"\r\n                                                    )}\r\n                                                >\r\n                                                    {header.isPlaceholder\r\n                                                        ? null\r\n                                                        : flexRender(\r\n                                                            header.column.columnDef.header,\r\n                                                            header.getContext()\r\n                                                        )}\r\n                                                </th>\r\n                                            )\r\n                                        })}\r\n                                    </tr>\r\n                                ))}\r\n                            </thead>\r\n                            <tbody>\r\n                                {isLoading ? (\r\n                                    <tr>\r\n                                        <td\r\n                                            colSpan={table.getAllColumns().length}\r\n                                            className=\"h-24 text-center\"\r\n                                        >\r\n                                            Đang tải dữ liệu...\r\n                                        </td>\r\n                                    </tr>\r\n                                ) : table.getRowModel().rows?.length ? (\r\n                                    table.getRowModel().rows.map((row) => (\r\n                                        <tr\r\n                                            key={row.id}\r\n                                            data-state={row.getIsSelected() && \"selected\"}\r\n                                            className=\"border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted\"\r\n                                        >\r\n                                            {row.getVisibleCells().map((cell, index) => {\r\n                                                // Determine if this is the first column (checkbox)\r\n                                                const isFirstColumn = index === 0;\r\n                                                // Determine if this column has sticky metadata\r\n                                                const hasSticky = cell.column.columnDef.meta?.isSticky;\r\n\r\n                                                return (\r\n                                                    <td\r\n                                                        key={cell.id}\r\n                                                        style={{\r\n                                                            width: cell.column.getSize(),\r\n                                                            minWidth: cell.column.getSize(),\r\n                                                            maxWidth: cell.column.getSize(),\r\n                                                            ...(hasSticky && {\r\n                                                                position: 'sticky',\r\n                                                                [cell.column.columnDef.meta?.position === 'right' ? 'right' : 'left']: 0,\r\n                                                                backgroundColor: 'var(--background)',\r\n                                                                zIndex: 30,\r\n                                                                boxShadow: cell.column.columnDef.meta?.position === 'right'\r\n                                                                    ? '-5px 0 5px -5px rgba(0,0,0,0.1)'\r\n                                                                    : '5px 0 5px -5px rgba(0,0,0,0.1)'\r\n                                                            }),\r\n                                                            // Áp dụng kiểu dáng sticky cho cột đầu tiên (checkbox)\r\n                                                            ...(isFirstColumn && !hasSticky && {\r\n                                                                position: 'sticky',\r\n                                                                left: 0,\r\n                                                                backgroundColor: 'var(--background)',\r\n                                                                zIndex: 30,\r\n                                                                boxShadow: '5px 0 5px -5px rgba(0,0,0,0.1)'\r\n                                                            })\r\n                                                        }}\r\n                                                        className={cn(\r\n                                                            \"py-1 px-2 align-middle [&:has([role=checkbox])]:pr-0\",\r\n                                                            hasSticky && `sticky-${cell.column.columnDef.meta?.position || 'left'}`,\r\n                                                            isFirstColumn && !hasSticky && \"sticky-left\"\r\n                                                        )}\r\n                                                    >\r\n                                                        {flexRender(\r\n                                                            cell.column.columnDef.cell,\r\n                                                            cell.getContext()\r\n                                                        )}\r\n                                                    </td>\r\n                                                )\r\n                                            })}\r\n                                        </tr>\r\n                                    ))\r\n                                ) : (\r\n                                    <tr>\r\n                                        <td\r\n                                            colSpan={table.getAllColumns().length}\r\n                                            className=\"h-24 text-center\"\r\n                                        >\r\n                                            Không có dữ liệu\r\n                                        </td>\r\n                                    </tr>\r\n                                )}\r\n                            </tbody>\r\n                        </table>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n        </div>\r\n    )\r\n}"], "names": [], "mappings": ";;;;AAEA;;;;;;;;;;;;;;;;;;CAkBC,GAED;AAEA;AAAA;AAxBA;;;;AAyCO,SAAS,UAAiB,EAC7B,KAAK,EACL,SAAS,EACT,YAAY,KAAK,EACG;IACpB,qBACI,sSAAC;QAAI,WAAW,CAAA,GAAA,+HAAA,CAAA,KAAE,AAAD,EAAE,+BAA+B;kBAE9C,cAAA,sSAAC;YAAI,WAAU;sBAEX,cAAA,sSAAC;gBAAI,WAAU;0BACX,cAAA,sSAAC;oBAAI,WAAU;8BACX,cAAA,sSAAC;wBAAM,WAAU;;0CACb,sSAAC;gCAAM,WAAU;0CACZ,MAAM,eAAe,GAAG,GAAG,CAAC,CAAC,4BAC1B,sSAAC;kDACI,YAAY,OAAO,CAAC,GAAG,CAAC,CAAC,QAAQ;4CAC9B,mDAAmD;4CACnD,MAAM,gBAAgB,UAAU;4CAChC,+CAA+C;4CAC/C,MAAM,YAAY,OAAO,MAAM,CAAC,SAAS,CAAC,IAAI,EAAE;4CAEhD,qBACI,sSAAC;gDAEG,OAAO;oDACH,OAAO,OAAO,OAAO;oDACrB,UAAU,OAAO,OAAO;oDACxB,UAAU,OAAO,OAAO;oDACxB,GAAI,aAAa;wDACb,UAAU;wDACV,CAAC,OAAO,MAAM,CAAC,SAAS,CAAC,IAAI,EAAE,aAAa,UAAU,UAAU,OAAO,EAAE;wDACzE,QAAQ;wDACR,WAAW,OAAO,MAAM,CAAC,SAAS,CAAC,IAAI,EAAE,aAAa,UAChD,oCACA;oDACV,CAAC;oDACD,uDAAuD;oDACvD,GAAI,iBAAiB,CAAC,aAAa;wDAC/B,UAAU;wDACV,MAAM;wDACN,QAAQ;wDACR,WAAW;oDACf,CAAC;gDACL;gDACA,WAAW,CAAA,GAAA,+HAAA,CAAA,KAAE,AAAD,EACR,+EACA,aAAa,CAAC,OAAO,EAAE,OAAO,MAAM,CAAC,SAAS,CAAC,IAAI,EAAE,YAAY,QAAQ,EACzE,iBAAiB,CAAC,aAAa,eAC/B,iBAAiB;0DAGpB,OAAO,aAAa,GACf,OACA,CAAA,GAAA,mSAAA,CAAA,aAAU,AAAD,EACP,OAAO,MAAM,CAAC,SAAS,CAAC,MAAM,EAC9B,OAAO,UAAU;+CAhCpB,OAAO,EAAE;;;;;wCAoC1B;uCA7CK,YAAY,EAAE;;;;;;;;;;0CAiD/B,sSAAC;0CACI,0BACG,sSAAC;8CACG,cAAA,sSAAC;wCACG,SAAS,MAAM,aAAa,GAAG,MAAM;wCACrC,WAAU;kDACb;;;;;;;;;;2CAIL,MAAM,WAAW,GAAG,IAAI,EAAE,SAC1B,MAAM,WAAW,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,oBAC1B,sSAAC;wCAEG,cAAY,IAAI,aAAa,MAAM;wCACnC,WAAU;kDAET,IAAI,eAAe,GAAG,GAAG,CAAC,CAAC,MAAM;4CAC9B,mDAAmD;4CACnD,MAAM,gBAAgB,UAAU;4CAChC,+CAA+C;4CAC/C,MAAM,YAAY,KAAK,MAAM,CAAC,SAAS,CAAC,IAAI,EAAE;4CAE9C,qBACI,sSAAC;gDAEG,OAAO;oDACH,OAAO,KAAK,MAAM,CAAC,OAAO;oDAC1B,UAAU,KAAK,MAAM,CAAC,OAAO;oDAC7B,UAAU,KAAK,MAAM,CAAC,OAAO;oDAC7B,GAAI,aAAa;wDACb,UAAU;wDACV,CAAC,KAAK,MAAM,CAAC,SAAS,CAAC,IAAI,EAAE,aAAa,UAAU,UAAU,OAAO,EAAE;wDACvE,iBAAiB;wDACjB,QAAQ;wDACR,WAAW,KAAK,MAAM,CAAC,SAAS,CAAC,IAAI,EAAE,aAAa,UAC9C,oCACA;oDACV,CAAC;oDACD,uDAAuD;oDACvD,GAAI,iBAAiB,CAAC,aAAa;wDAC/B,UAAU;wDACV,MAAM;wDACN,iBAAiB;wDACjB,QAAQ;wDACR,WAAW;oDACf,CAAC;gDACL;gDACA,WAAW,CAAA,GAAA,+HAAA,CAAA,KAAE,AAAD,EACR,wDACA,aAAa,CAAC,OAAO,EAAE,KAAK,MAAM,CAAC,SAAS,CAAC,IAAI,EAAE,YAAY,QAAQ,EACvE,iBAAiB,CAAC,aAAa;0DAGlC,CAAA,GAAA,mSAAA,CAAA,aAAU,AAAD,EACN,KAAK,MAAM,CAAC,SAAS,CAAC,IAAI,EAC1B,KAAK,UAAU;+CA/Bd,KAAK,EAAE;;;;;wCAmCxB;uCA/CK,IAAI,EAAE;;;;8DAmDnB,sSAAC;8CACG,cAAA,sSAAC;wCACG,SAAS,MAAM,aAAa,GAAG,MAAM;wCACrC,WAAU;kDACb;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAYzC;KAhJgB", "debugId": null}}, {"offset": {"line": 208, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/bloomgoo/gold-exchange-platform/frontend/components/common/admin/ecom-orders/type/ecom-orders.ts"], "sourcesContent": ["import { User } from \"@/components/common/admin/users/type/user\";\r\nimport { EcomProduct } from \"../../ecom-products/type/ecom-product\";\r\n\r\n/**\r\n * Enum đại diện cho trạng thái đơn hàng (OrderStatus) từ backend\r\n */\r\nexport enum OrderStatus {\r\n    PENDING = 'PENDING',\r\n    PROCESSING = 'PROCESSING',\r\n    COMPLETED = 'COMPLETED',\r\n    CANCELLED = 'CANCELLED',\r\n}\r\n\r\n/**\r\n * Enum đại diện cho trạng thái thanh toán (PaymentStatus) từ backend\r\n */\r\nexport enum PaymentStatus {\r\n    UNPAID = 'UNPAID',\r\n    PAID = 'PAID',\r\n}\r\n\r\n/**\r\n * Interface đại diện cho chi tiết đơn hàng\r\n * Dựa trên EcomOrderDetailDto từ backend\r\n */\r\nexport interface EcomOrderDetail {\r\n    id: string;\r\n    orderId: string;\r\n    productId: string;\r\n    quantity: number;\r\n    unitPrice: number;\r\n    totalPrice: number;\r\n    createdAt: string;\r\n    updatedAt: string;\r\n    createdBy?: string | null;\r\n    updatedBy?: string | null;\r\n    deletedAt?: string | null;\r\n    deletedBy?: string | null;\r\n    product?: EcomProduct;\r\n}\r\n\r\n/**\r\n * Interface đại diện cho đơn hàng (EcomOrder) từ backend\r\n */\r\nexport interface EcomOrder {\r\n    id: string;\r\n    userId: string;\r\n    orderNumber: string;\r\n    totalAmount: number;\r\n    status: OrderStatus;\r\n    paymentStatus: PaymentStatus;\r\n    notes?: string;\r\n    isDeleted: boolean;\r\n    createdAt: string;\r\n    updatedAt: string;\r\n    createdBy?: string | null;\r\n    updatedBy?: string | null;\r\n    deletedAt?: string | null;\r\n    deletedBy?: string | null;\r\n    user?: User;\r\n    creator?: User | null;\r\n    updater?: User | null;\r\n    deleter?: User | null;\r\n    orderDetails?: EcomOrderDetail[];\r\n}\r\n\r\n/**\r\n * Interface cho dữ liệu tạo đơn hàng mới\r\n * Dựa trên CreateEcomOrderDto từ backend\r\n */\r\nexport interface CreateEcomOrderDto {\r\n    userId: string;\r\n    orderNumber?: string;\r\n    totalAmount: number;\r\n    status?: OrderStatus;\r\n    paymentStatus?: PaymentStatus;\r\n    notes?: string;\r\n    orderDetails?: {\r\n        productId: string;\r\n        quantity: number;\r\n        price: number;\r\n    }[];\r\n}\r\n\r\n/**\r\n * Interface cho dữ liệu cập nhật đơn hàng\r\n * Dựa trên UpdateEcomOrderDto từ backend\r\n */\r\nexport interface UpdateEcomOrderDto {\r\n    id: string;\r\n    userId?: string;\r\n    orderNumber?: string;\r\n    totalAmount?: number;\r\n    status?: OrderStatus;\r\n    paymentStatus?: PaymentStatus;\r\n    notes?: string;\r\n}\r\n\r\n/**\r\n * Interface cho bộ lọc đơn hàng\r\n */\r\nexport interface EcomOrderFilter {\r\n    userId?: string;\r\n    orderNumber?: string;\r\n    status?: OrderStatus;\r\n    paymentStatus?: PaymentStatus;\r\n    dateFrom?: Date;\r\n    dateTo?: Date;\r\n}\r\n"], "names": [], "mappings": ";;;;AAMO,IAAA,AAAK,qCAAA;;;;;WAAA;;AAUL,IAAA,AAAK,uCAAA;;;WAAA", "debugId": null}}, {"offset": {"line": 233, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/bloomgoo/gold-exchange-platform/frontend/components/ui/dialog.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as DialogPrimitive from \"@radix-ui/react-dialog\"\r\nimport { XIcon } from \"lucide-react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction Dialog({\r\n  ...props\r\n}: React.ComponentProps<typeof DialogPrimitive.Root>) {\r\n  return <DialogPrimitive.Root data-slot=\"dialog\" {...props} />\r\n}\r\n\r\nfunction DialogTrigger({\r\n  ...props\r\n}: React.ComponentProps<typeof DialogPrimitive.Trigger>) {\r\n  return <DialogPrimitive.Trigger data-slot=\"dialog-trigger\" {...props} />\r\n}\r\n\r\nfunction DialogPortal({\r\n  ...props\r\n}: React.ComponentProps<typeof DialogPrimitive.Portal>) {\r\n  return <DialogPrimitive.Portal data-slot=\"dialog-portal\" {...props} />\r\n}\r\n\r\nfunction DialogClose({\r\n  ...props\r\n}: React.ComponentProps<typeof DialogPrimitive.Close>) {\r\n  return <DialogPrimitive.Close data-slot=\"dialog-close\" {...props} />\r\n}\r\n\r\nfunction DialogOverlay({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof DialogPrimitive.Overlay>) {\r\n  return (\r\n    <DialogPrimitive.Overlay\r\n      data-slot=\"dialog-overlay\"\r\n      className={cn(\r\n        \"data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction DialogContent({\r\n  className,\r\n  children,\r\n  ...props\r\n}: React.ComponentProps<typeof DialogPrimitive.Content>) {\r\n  return (\r\n    <DialogPortal data-slot=\"dialog-portal\">\r\n      <DialogOverlay />\r\n      <DialogPrimitive.Content\r\n        data-slot=\"dialog-content\"\r\n        className={cn(\r\n          \"bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg\",\r\n          className\r\n        )}\r\n        {...props}\r\n      >\r\n        {children}\r\n        <DialogPrimitive.Close className=\"ring-offset-background focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\">\r\n          <XIcon />\r\n          <span className=\"sr-only\">Close</span>\r\n        </DialogPrimitive.Close>\r\n      </DialogPrimitive.Content>\r\n    </DialogPortal>\r\n  )\r\n}\r\n\r\nfunction DialogHeader({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"dialog-header\"\r\n      className={cn(\"flex flex-col gap-2 text-center sm:text-left\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction DialogFooter({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"dialog-footer\"\r\n      className={cn(\r\n        \"flex flex-col-reverse gap-2 sm:flex-row sm:justify-end\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction DialogTitle({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof DialogPrimitive.Title>) {\r\n  return (\r\n    <DialogPrimitive.Title\r\n      data-slot=\"dialog-title\"\r\n      className={cn(\"text-lg leading-none font-semibold\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction DialogDescription({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof DialogPrimitive.Description>) {\r\n  return (\r\n    <DialogPrimitive.Description\r\n      data-slot=\"dialog-description\"\r\n      className={cn(\"text-muted-foreground text-sm\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport {\r\n  Dialog,\r\n  DialogClose,\r\n  DialogContent,\r\n  DialogDescription,\r\n  DialogFooter,\r\n  DialogHeader,\r\n  DialogOverlay,\r\n  DialogPortal,\r\n  DialogTitle,\r\n  DialogTrigger,\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAGA;AACA;AAEA;AAAA;AANA;;;;;AAQA,SAAS,OAAO,EACd,GAAG,OAC+C;IAClD,qBAAO,sSAAC,kRAAA,CAAA,OAAoB;QAAC,aAAU;QAAU,GAAG,KAAK;;;;;;AAC3D;KAJS;AAMT,SAAS,cAAc,EACrB,GAAG,OACkD;IACrD,qBAAO,sSAAC,kRAAA,CAAA,UAAuB;QAAC,aAAU;QAAkB,GAAG,KAAK;;;;;;AACtE;MAJS;AAMT,SAAS,aAAa,EACpB,GAAG,OACiD;IACpD,qBAAO,sSAAC,kRAAA,CAAA,SAAsB;QAAC,aAAU;QAAiB,GAAG,KAAK;;;;;;AACpE;MAJS;AAMT,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,sSAAC,kRAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;MAJS;AAMT,SAAS,cAAc,EACrB,SAAS,EACT,GAAG,OACkD;IACrD,qBACE,sSAAC,kRAAA,CAAA,UAAuB;QACtB,aAAU;QACV,WAAW,CAAA,GAAA,+HAAA,CAAA,KAAE,AAAD,EACV,0JACA;QAED,GAAG,KAAK;;;;;;AAGf;MAdS;AAgBT,SAAS,cAAc,EACrB,SAAS,EACT,QAAQ,EACR,GAAG,OACkD;IACrD,qBACE,sSAAC;QAAa,aAAU;;0BACtB,sSAAC;;;;;0BACD,sSAAC,kRAAA,CAAA,UAAuB;gBACtB,aAAU;gBACV,WAAW,CAAA,GAAA,+HAAA,CAAA,KAAE,AAAD,EACV,+WACA;gBAED,GAAG,KAAK;;oBAER;kCACD,sSAAC,kRAAA,CAAA,QAAqB;wBAAC,WAAU;;0CAC/B,sSAAC,uRAAA,CAAA,QAAK;;;;;0CACN,sSAAC;gCAAK,WAAU;0CAAU;;;;;;;;;;;;;;;;;;;;;;;;AAKpC;MAxBS;AA0BT,SAAS,aAAa,EAAE,SAAS,EAAE,GAAG,OAAoC;IACxE,qBACE,sSAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,+HAAA,CAAA,KAAE,AAAD,EAAE,gDAAgD;QAC7D,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,aAAa,EAAE,SAAS,EAAE,GAAG,OAAoC;IACxE,qBACE,sSAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,+HAAA,CAAA,KAAE,AAAD,EACV,0DACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,sSAAC,kRAAA,CAAA,QAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,+HAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QACnD,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,kBAAkB,EACzB,SAAS,EACT,GAAG,OACsD;IACzD,qBACE,sSAAC,kRAAA,CAAA,cAA2B;QAC1B,aAAU;QACV,WAAW,CAAA,GAAA,+HAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;MAXS", "debugId": null}}, {"offset": {"line": 431, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/bloomgoo/gold-exchange-platform/frontend/components/ui/popover.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as PopoverPrimitive from \"@radix-ui/react-popover\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction Popover({\r\n  ...props\r\n}: React.ComponentProps<typeof PopoverPrimitive.Root>) {\r\n  return <PopoverPrimitive.Root data-slot=\"popover\" {...props} />\r\n}\r\n\r\nfunction PopoverTrigger({\r\n  ...props\r\n}: React.ComponentProps<typeof PopoverPrimitive.Trigger>) {\r\n  return <PopoverPrimitive.Trigger data-slot=\"popover-trigger\" {...props} />\r\n}\r\n\r\nfunction PopoverContent({\r\n  className,\r\n  align = \"center\",\r\n  sideOffset = 4,\r\n  ...props\r\n}: React.ComponentProps<typeof PopoverPrimitive.Content>) {\r\n  return (\r\n    <PopoverPrimitive.Portal>\r\n      <PopoverPrimitive.Content\r\n        data-slot=\"popover-content\"\r\n        align={align}\r\n        sideOffset={sideOffset}\r\n        className={cn(\r\n          \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 w-72 origin-(--radix-popover-content-transform-origin) rounded-md border p-4 shadow-md outline-hidden\",\r\n          className\r\n        )}\r\n        {...props}\r\n      />\r\n    </PopoverPrimitive.Portal>\r\n  )\r\n}\r\n\r\nfunction PopoverAnchor({\r\n  ...props\r\n}: React.ComponentProps<typeof PopoverPrimitive.Anchor>) {\r\n  return <PopoverPrimitive.Anchor data-slot=\"popover-anchor\" {...props} />\r\n}\r\n\r\nexport { Popover, PopoverTrigger, PopoverContent, PopoverAnchor }\r\n"], "names": [], "mappings": ";;;;;;;AAGA;AAEA;AAAA;AALA;;;;AAOA,SAAS,QAAQ,EACf,GAAG,OACgD;IACnD,qBAAO,sSAAC,gRAAA,CAAA,OAAqB;QAAC,aAAU;QAAW,GAAG,KAAK;;;;;;AAC7D;KAJS;AAMT,SAAS,eAAe,EACtB,GAAG,OACmD;IACtD,qBAAO,sSAAC,gRAAA,CAAA,UAAwB;QAAC,aAAU;QAAmB,GAAG,KAAK;;;;;;AACxE;MAJS;AAMT,SAAS,eAAe,EACtB,SAAS,EACT,QAAQ,QAAQ,EAChB,aAAa,CAAC,EACd,GAAG,OACmD;IACtD,qBACE,sSAAC,gRAAA,CAAA,SAAuB;kBACtB,cAAA,sSAAC,gRAAA,CAAA,UAAwB;YACvB,aAAU;YACV,OAAO;YACP,YAAY;YACZ,WAAW,CAAA,GAAA,+HAAA,CAAA,KAAE,AAAD,EACV,keACA;YAED,GAAG,KAAK;;;;;;;;;;;AAIjB;MApBS;AAsBT,SAAS,cAAc,EACrB,GAAG,OACkD;IACrD,qBAAO,sSAAC,gRAAA,CAAA,SAAuB;QAAC,aAAU;QAAkB,GAAG,KAAK;;;;;;AACtE;MAJS", "debugId": null}}, {"offset": {"line": 513, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/bloomgoo/gold-exchange-platform/frontend/components/ui/hover-card.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as HoverCardPrimitive from \"@radix-ui/react-hover-card\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction HoverCard({\r\n  ...props\r\n}: React.ComponentProps<typeof HoverCardPrimitive.Root>) {\r\n  return <HoverCardPrimitive.Root data-slot=\"hover-card\" {...props} />\r\n}\r\n\r\nfunction HoverCardTrigger({\r\n  ...props\r\n}: React.ComponentProps<typeof HoverCardPrimitive.Trigger>) {\r\n  return (\r\n    <HoverCardPrimitive.Trigger data-slot=\"hover-card-trigger\" {...props} />\r\n  )\r\n}\r\n\r\nfunction HoverCardContent({\r\n  className,\r\n  align = \"center\",\r\n  sideOffset = 4,\r\n  ...props\r\n}: React.ComponentProps<typeof HoverCardPrimitive.Content>) {\r\n  return (\r\n    <HoverCardPrimitive.Portal data-slot=\"hover-card-portal\">\r\n      <HoverCardPrimitive.Content\r\n        data-slot=\"hover-card-content\"\r\n        align={align}\r\n        sideOffset={sideOffset}\r\n        className={cn(\r\n          \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 w-64 origin-(--radix-hover-card-content-transform-origin) rounded-md border p-4 shadow-md outline-hidden\",\r\n          className\r\n        )}\r\n        {...props}\r\n      />\r\n    </HoverCardPrimitive.Portal>\r\n  )\r\n}\r\n\r\nexport { HoverCard, HoverCardTrigger, HoverCardContent }\r\n"], "names": [], "mappings": ";;;;;;AAGA;AAEA;AAAA;AALA;;;;AAOA,SAAS,UAAU,EACjB,GAAG,OACkD;IACrD,qBAAO,sSAAC,sRAAA,CAAA,OAAuB;QAAC,aAAU;QAAc,GAAG,KAAK;;;;;;AAClE;KAJS;AAMT,SAAS,iBAAiB,EACxB,GAAG,OACqD;IACxD,qBACE,sSAAC,sRAAA,CAAA,UAA0B;QAAC,aAAU;QAAsB,GAAG,KAAK;;;;;;AAExE;MANS;AAQT,SAAS,iBAAiB,EACxB,SAAS,EACT,QAAQ,QAAQ,EAChB,aAAa,CAAC,EACd,GAAG,OACqD;IACxD,qBACE,sSAAC,sRAAA,CAAA,SAAyB;QAAC,aAAU;kBACnC,cAAA,sSAAC,sRAAA,CAAA,UAA0B;YACzB,aAAU;YACV,OAAO;YACP,YAAY;YACZ,WAAW,CAAA,GAAA,+HAAA,CAAA,KAAE,AAAD,EACV,qeACA;YAED,GAAG,KAAK;;;;;;;;;;;AAIjB;MApBS", "debugId": null}}, {"offset": {"line": 583, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/bloomgoo/gold-exchange-platform/frontend/components/common/user/user-hover-card.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';\r\nimport { HoverCard, HoverCardContent, HoverCardTrigger } from '@/components/ui/hover-card';\r\nimport { User } from '@/components/common/admin/users/type/user';\r\nimport { Badge } from '@/components/ui/badge';\r\nimport { format } from 'date-fns';\r\nimport { vi } from 'date-fns/locale';\r\nimport { Mail } from 'lucide-react';\r\n\r\ninterface UserHoverCardProps {\r\n  user?: Partial<User> | null;\r\n  userId?: string;\r\n  showAvatar?: boolean;\r\n  size?: 'sm' | 'md' | 'lg';\r\n  children: React.ReactNode;\r\n}\r\n\r\n/**\r\n * Component hiển thị thông tin người dùng khi hover\r\n * @param user Thông tin người dùng\r\n * @param userId ID người dùng (nếu không có thông tin đầy đủ)\r\n * @param showAvatar Có hiển thị avatar không\r\n * @param size Kích thước hiển thị (sm, md, lg)\r\n * @param children Nội dung hiển thị khi không hover\r\n */\r\nexport function UserHoverCard({ user, userId, showAvatar = true, size = 'md', children }: UserHoverCardProps) {\r\n  // Nếu không có thông tin người dùng và không có userId, hiển thị children\r\n  if (!user && !userId) {\r\n    return <>{children}</>;\r\n  }\r\n\r\n  // Lấy ID từ user hoặc từ userId\r\n  const id = user?.id || userId;\r\n\r\n  // Xác định kích thước avatar dựa trên size\r\n  const avatarSize = {\r\n    sm: 'size-5',\r\n    md: 'size-6',\r\n    lg: 'size-8',\r\n  }[size];\r\n\r\n  // Hiển thị badge trạng thái người dùng\r\n  const getStatusBadge = (isActive?: boolean) => {\r\n    if (isActive === undefined) return null;\r\n    return (\r\n      <Badge variant={isActive ? 'default' : 'secondary'} className=\"ml-2\">\r\n        {isActive ? 'Hoạt động' : 'Vô hiệu'}\r\n      </Badge>\r\n    );\r\n  };\r\n\r\n  // Format ngày tháng\r\n  const formatDate = (date?: string | Date) => {\r\n    if (!date) return 'N/A';\r\n    try {\r\n      const dateObj = typeof date === 'string' ? new Date(date) : date;\r\n      return format(dateObj, 'dd/MM/yyyy HH:mm', { locale: vi });\r\n    } catch (error) {\r\n      return 'N/A';\r\n    }\r\n  };\r\n\r\n  return (\r\n    <HoverCard>\r\n      <HoverCardTrigger asChild>\r\n        <div className=\"flex items-center gap-2 cursor-pointer\">\r\n          {showAvatar && (\r\n            <Avatar className={`shrink-0 ${avatarSize}`}>\r\n              <AvatarImage\r\n                src={user?.avatar || `https://api.dicebear.com/9.x/initials/svg?seed=${user?.fullName || user?.username || id}`}\r\n                alt={user?.fullName || user?.username || 'User'}\r\n              />\r\n              <AvatarFallback>\r\n                {user?.fullName\r\n                  ? user.fullName[0].toUpperCase()\r\n                  : (user?.username ? user.username[0].toUpperCase() : 'U')}\r\n              </AvatarFallback>\r\n            </Avatar>\r\n          )}\r\n          {children}\r\n        </div>\r\n      </HoverCardTrigger>\r\n      <HoverCardContent className=\"w-80 p-4\">\r\n        <div className=\"flex flex-col gap-4\">\r\n          {/* Header với thông tin cơ bản */}\r\n          <div className=\"flex items-center gap-3\">\r\n            <Avatar className=\"size-10 shrink-0\">\r\n              <AvatarImage\r\n                src={user?.avatar || `https://api.dicebear.com/9.x/initials/svg?seed=${user?.fullName || user?.username || id}`}\r\n                alt={user?.fullName || user?.username || 'User'}\r\n              />\r\n              <AvatarFallback>\r\n                {user?.fullName\r\n                  ? user.fullName[0].toUpperCase()\r\n                  : (user?.username ? user.username[0].toUpperCase() : 'U')}\r\n              </AvatarFallback>\r\n            </Avatar>\r\n            <div>\r\n              <div className=\"font-medium flex items-center\">\r\n                {user?.fullName || user?.username || id?.substring(0, 8)}\r\n                {getStatusBadge(user?.isActive)}\r\n              </div>\r\n              {user?.email && (\r\n                <div className=\"text-sm text-muted-foreground flex items-center gap-1 mt-1\">\r\n                  <Mail className=\"size-3\" />\r\n                  {user.email}\r\n                </div>\r\n              )}\r\n            </div>\r\n          </div>\r\n\r\n          {/* Thông tin chi tiết */}\r\n          <div className=\"grid grid-cols-2 gap-3 text-sm\">\r\n            {user?.username && (\r\n              <div>\r\n                <div className=\"text-muted-foreground\">Tên đăng nhập</div>\r\n                <div>{user.username}</div>\r\n              </div>\r\n            )}\r\n            {user?.phone && (\r\n              <div>\r\n                <div className=\"text-muted-foreground\">Số điện thoại</div>\r\n                <div>{user.phone}</div>\r\n              </div>\r\n            )}\r\n            {user?.address && (\r\n              <div className=\"col-span-2\">\r\n                <div className=\"text-muted-foreground\">Địa chỉ</div>\r\n                <div>{user.address}</div>\r\n              </div>\r\n            )}\r\n            {user?.bio && (\r\n              <div className=\"col-span-2\">\r\n                <div className=\"text-muted-foreground\">Giới thiệu</div>\r\n                <div>{user.bio}</div>\r\n              </div>\r\n            )}\r\n            {user?.birthday && (\r\n              <div>\r\n                <div className=\"text-muted-foreground\">Ngày sinh</div>\r\n                <div>{formatDate(user.birthday)}</div>\r\n              </div>\r\n            )}\r\n            {user?.createdAt && (\r\n              <div>\r\n                <div className=\"text-muted-foreground\">Ngày tạo</div>\r\n                <div>{formatDate(user.createdAt)}</div>\r\n              </div>\r\n            )}\r\n            {user?.role && (\r\n              <div>\r\n                <div className=\"text-muted-foreground\">Vai trò</div>\r\n                <div>{Array.isArray(user.role) ? user.role.join(', ') : user.role}</div>\r\n              </div>\r\n            )}\r\n          </div>\r\n        </div>\r\n      </HoverCardContent>\r\n    </HoverCard>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAEA;AACA;AACA;AACA;AARA;;;;;;;;AA0BO,SAAS,cAAc,EAAE,IAAI,EAAE,MAAM,EAAE,aAAa,IAAI,EAAE,OAAO,IAAI,EAAE,QAAQ,EAAsB;IAC1G,0EAA0E;IAC1E,IAAI,CAAC,QAAQ,CAAC,QAAQ;QACpB,qBAAO;sBAAG;;IACZ;IAEA,gCAAgC;IAChC,MAAM,KAAK,MAAM,MAAM;IAEvB,2CAA2C;IAC3C,MAAM,aAAa;QACjB,IAAI;QACJ,IAAI;QACJ,IAAI;IACN,CAAC,CAAC,KAAK;IAEP,uCAAuC;IACvC,MAAM,iBAAiB,CAAC;QACtB,IAAI,aAAa,WAAW,OAAO;QACnC,qBACE,sSAAC,6HAAA,CAAA,QAAK;YAAC,SAAS,WAAW,YAAY;YAAa,WAAU;sBAC3D,WAAW,cAAc;;;;;;IAGhC;IAEA,oBAAoB;IACpB,MAAM,aAAa,CAAC;QAClB,IAAI,CAAC,MAAM,OAAO;QAClB,IAAI;YACF,MAAM,UAAU,OAAO,SAAS,WAAW,IAAI,KAAK,QAAQ;YAC5D,OAAO,CAAA,GAAA,gNAAA,CAAA,SAAM,AAAD,EAAE,SAAS,oBAAoB;gBAAE,QAAQ,sMAAA,CAAA,KAAE;YAAC;QAC1D,EAAE,OAAO,OAAO;YACd,OAAO;QACT;IACF;IAEA,qBACE,sSAAC,qIAAA,CAAA,YAAS;;0BACR,sSAAC,qIAAA,CAAA,mBAAgB;gBAAC,OAAO;0BACvB,cAAA,sSAAC;oBAAI,WAAU;;wBACZ,4BACC,sSAAC,8HAAA,CAAA,SAAM;4BAAC,WAAW,CAAC,SAAS,EAAE,YAAY;;8CACzC,sSAAC,8HAAA,CAAA,cAAW;oCACV,KAAK,MAAM,UAAU,CAAC,+CAA+C,EAAE,MAAM,YAAY,MAAM,YAAY,IAAI;oCAC/G,KAAK,MAAM,YAAY,MAAM,YAAY;;;;;;8CAE3C,sSAAC,8HAAA,CAAA,iBAAc;8CACZ,MAAM,WACH,KAAK,QAAQ,CAAC,EAAE,CAAC,WAAW,KAC3B,MAAM,WAAW,KAAK,QAAQ,CAAC,EAAE,CAAC,WAAW,KAAK;;;;;;;;;;;;wBAI5D;;;;;;;;;;;;0BAGL,sSAAC,qIAAA,CAAA,mBAAgB;gBAAC,WAAU;0BAC1B,cAAA,sSAAC;oBAAI,WAAU;;sCAEb,sSAAC;4BAAI,WAAU;;8CACb,sSAAC,8HAAA,CAAA,SAAM;oCAAC,WAAU;;sDAChB,sSAAC,8HAAA,CAAA,cAAW;4CACV,KAAK,MAAM,UAAU,CAAC,+CAA+C,EAAE,MAAM,YAAY,MAAM,YAAY,IAAI;4CAC/G,KAAK,MAAM,YAAY,MAAM,YAAY;;;;;;sDAE3C,sSAAC,8HAAA,CAAA,iBAAc;sDACZ,MAAM,WACH,KAAK,QAAQ,CAAC,EAAE,CAAC,WAAW,KAC3B,MAAM,WAAW,KAAK,QAAQ,CAAC,EAAE,CAAC,WAAW,KAAK;;;;;;;;;;;;8CAG3D,sSAAC;;sDACC,sSAAC;4CAAI,WAAU;;gDACZ,MAAM,YAAY,MAAM,YAAY,IAAI,UAAU,GAAG;gDACrD,eAAe,MAAM;;;;;;;wCAEvB,MAAM,uBACL,sSAAC;4CAAI,WAAU;;8DACb,sSAAC,yRAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;gDACf,KAAK,KAAK;;;;;;;;;;;;;;;;;;;sCAOnB,sSAAC;4BAAI,WAAU;;gCACZ,MAAM,0BACL,sSAAC;;sDACC,sSAAC;4CAAI,WAAU;sDAAwB;;;;;;sDACvC,sSAAC;sDAAK,KAAK,QAAQ;;;;;;;;;;;;gCAGtB,MAAM,uBACL,sSAAC;;sDACC,sSAAC;4CAAI,WAAU;sDAAwB;;;;;;sDACvC,sSAAC;sDAAK,KAAK,KAAK;;;;;;;;;;;;gCAGnB,MAAM,yBACL,sSAAC;oCAAI,WAAU;;sDACb,sSAAC;4CAAI,WAAU;sDAAwB;;;;;;sDACvC,sSAAC;sDAAK,KAAK,OAAO;;;;;;;;;;;;gCAGrB,MAAM,qBACL,sSAAC;oCAAI,WAAU;;sDACb,sSAAC;4CAAI,WAAU;sDAAwB;;;;;;sDACvC,sSAAC;sDAAK,KAAK,GAAG;;;;;;;;;;;;gCAGjB,MAAM,0BACL,sSAAC;;sDACC,sSAAC;4CAAI,WAAU;sDAAwB;;;;;;sDACvC,sSAAC;sDAAK,WAAW,KAAK,QAAQ;;;;;;;;;;;;gCAGjC,MAAM,2BACL,sSAAC;;sDACC,sSAAC;4CAAI,WAAU;sDAAwB;;;;;;sDACvC,sSAAC;sDAAK,WAAW,KAAK,SAAS;;;;;;;;;;;;gCAGlC,MAAM,sBACL,sSAAC;;sDACC,sSAAC;4CAAI,WAAU;sDAAwB;;;;;;sDACvC,sSAAC;sDAAK,MAAM,OAAO,CAAC,KAAK,IAAI,IAAI,KAAK,IAAI,CAAC,IAAI,CAAC,QAAQ,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQjF;KAvIgB", "debugId": null}}, {"offset": {"line": 960, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/bloomgoo/gold-exchange-platform/frontend/components/ui/command.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport { Command as CommandPrimitive } from \"cmdk\"\r\nimport { SearchIcon } from \"lucide-react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\nimport {\r\n  Dialog,\r\n  DialogContent,\r\n  DialogDescription,\r\n  DialogHeader,\r\n  DialogTitle,\r\n} from \"@/components/ui/dialog\"\r\n\r\nfunction Command({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof CommandPrimitive>) {\r\n  return (\r\n    <CommandPrimitive\r\n      data-slot=\"command\"\r\n      className={cn(\r\n        \"bg-popover text-popover-foreground flex h-full w-full flex-col overflow-hidden rounded-md\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CommandDialog({\r\n  title = \"Command Palette\",\r\n  description = \"Search for a command to run...\",\r\n  children,\r\n  ...props\r\n}: React.ComponentProps<typeof Dialog> & {\r\n  title?: string\r\n  description?: string\r\n}) {\r\n  return (\r\n    <Dialog {...props}>\r\n      <DialogHeader className=\"sr-only\">\r\n        <DialogTitle>{title}</DialogTitle>\r\n        <DialogDescription>{description}</DialogDescription>\r\n      </DialogHeader>\r\n      <DialogContent className=\"overflow-hidden p-0\">\r\n        <Command className=\"[&_[cmdk-group-heading]]:text-muted-foreground **:data-[slot=command-input-wrapper]:h-12 [&_[cmdk-group-heading]]:px-2 [&_[cmdk-group-heading]]:font-medium [&_[cmdk-group]]:px-2 [&_[cmdk-group]:not([hidden])_~[cmdk-group]]:pt-0 [&_[cmdk-input-wrapper]_svg]:h-5 [&_[cmdk-input-wrapper]_svg]:w-5 [&_[cmdk-input]]:h-12 [&_[cmdk-item]]:px-2 [&_[cmdk-item]]:py-3 [&_[cmdk-item]_svg]:h-5 [&_[cmdk-item]_svg]:w-5\">\r\n          {children}\r\n        </Command>\r\n      </DialogContent>\r\n    </Dialog>\r\n  )\r\n}\r\n\r\nfunction CommandInput({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof CommandPrimitive.Input>) {\r\n  return (\r\n    <div\r\n      data-slot=\"command-input-wrapper\"\r\n      className=\"flex h-9 items-center gap-2 border-b px-3\"\r\n    >\r\n      <SearchIcon className=\"size-4 shrink-0 opacity-50\" />\r\n      <CommandPrimitive.Input\r\n        data-slot=\"command-input\"\r\n        className={cn(\r\n          \"placeholder:text-muted-foreground flex h-10 w-full rounded-md bg-transparent py-3 text-sm outline-hidden disabled:cursor-not-allowed disabled:opacity-50\",\r\n          className\r\n        )}\r\n        {...props}\r\n      />\r\n    </div>\r\n  )\r\n}\r\n\r\nfunction CommandList({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof CommandPrimitive.List>) {\r\n  return (\r\n    <CommandPrimitive.List\r\n      data-slot=\"command-list\"\r\n      className={cn(\r\n        \"max-h-[300px] scroll-py-1 overflow-x-hidden overflow-y-auto\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CommandEmpty({\r\n  ...props\r\n}: React.ComponentProps<typeof CommandPrimitive.Empty>) {\r\n  return (\r\n    <CommandPrimitive.Empty\r\n      data-slot=\"command-empty\"\r\n      className=\"py-6 text-center text-sm\"\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CommandGroup({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof CommandPrimitive.Group>) {\r\n  return (\r\n    <CommandPrimitive.Group\r\n      data-slot=\"command-group\"\r\n      className={cn(\r\n        \"text-foreground [&_[cmdk-group-heading]]:text-muted-foreground overflow-hidden p-1 [&_[cmdk-group-heading]]:px-2 [&_[cmdk-group-heading]]:py-1.5 [&_[cmdk-group-heading]]:text-xs [&_[cmdk-group-heading]]:font-medium\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CommandSeparator({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof CommandPrimitive.Separator>) {\r\n  return (\r\n    <CommandPrimitive.Separator\r\n      data-slot=\"command-separator\"\r\n      className={cn(\"bg-border -mx-1 h-px\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CommandItem({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof CommandPrimitive.Item>) {\r\n  return (\r\n    <CommandPrimitive.Item\r\n      data-slot=\"command-item\"\r\n      className={cn(\r\n        \"data-[selected=true]:bg-accent data-[selected=true]:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex cursor-default items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[disabled=true]:pointer-events-none data-[disabled=true]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CommandShortcut({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<\"span\">) {\r\n  return (\r\n    <span\r\n      data-slot=\"command-shortcut\"\r\n      className={cn(\r\n        \"text-muted-foreground ml-auto text-xs tracking-widest\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport {\r\n  Command,\r\n  CommandDialog,\r\n  CommandInput,\r\n  CommandList,\r\n  CommandEmpty,\r\n  CommandGroup,\r\n  CommandItem,\r\n  CommandShortcut,\r\n  CommandSeparator,\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;;;AAGA;AACA;AAEA;AAAA;AACA;AAPA;;;;;;AAeA,SAAS,QAAQ,EACf,SAAS,EACT,GAAG,OAC2C;IAC9C,qBACE,sSAAC,qPAAA,CAAA,UAAgB;QACf,aAAU;QACV,WAAW,CAAA,GAAA,+HAAA,CAAA,KAAE,AAAD,EACV,6FACA;QAED,GAAG,KAAK;;;;;;AAGf;KAdS;AAgBT,SAAS,cAAc,EACrB,QAAQ,iBAAiB,EACzB,cAAc,gCAAgC,EAC9C,QAAQ,EACR,GAAG,OAIJ;IACC,qBACE,sSAAC,8HAAA,CAAA,SAAM;QAAE,GAAG,KAAK;;0BACf,sSAAC,8HAAA,CAAA,eAAY;gBAAC,WAAU;;kCACtB,sSAAC,8HAAA,CAAA,cAAW;kCAAE;;;;;;kCACd,sSAAC,8HAAA,CAAA,oBAAiB;kCAAE;;;;;;;;;;;;0BAEtB,sSAAC,8HAAA,CAAA,gBAAa;gBAAC,WAAU;0BACvB,cAAA,sSAAC;oBAAQ,WAAU;8BAChB;;;;;;;;;;;;;;;;;AAKX;MAtBS;AAwBT,SAAS,aAAa,EACpB,SAAS,EACT,GAAG,OACiD;IACpD,qBACE,sSAAC;QACC,aAAU;QACV,WAAU;;0BAEV,sSAAC,iSAAA,CAAA,aAAU;gBAAC,WAAU;;;;;;0BACtB,sSAAC,qPAAA,CAAA,UAAgB,CAAC,KAAK;gBACrB,aAAU;gBACV,WAAW,CAAA,GAAA,+HAAA,CAAA,KAAE,AAAD,EACV,4JACA;gBAED,GAAG,KAAK;;;;;;;;;;;;AAIjB;MApBS;AAsBT,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,sSAAC,qPAAA,CAAA,UAAgB,CAAC,IAAI;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,+HAAA,CAAA,KAAE,AAAD,EACV,+DACA;QAED,GAAG,KAAK;;;;;;AAGf;MAdS;AAgBT,SAAS,aAAa,EACpB,GAAG,OACiD;IACpD,qBACE,sSAAC,qPAAA,CAAA,UAAgB,CAAC,KAAK;QACrB,aAAU;QACV,WAAU;QACT,GAAG,KAAK;;;;;;AAGf;MAVS;AAYT,SAAS,aAAa,EACpB,SAAS,EACT,GAAG,OACiD;IACpD,qBACE,sSAAC,qPAAA,CAAA,UAAgB,CAAC,KAAK;QACrB,aAAU;QACV,WAAW,CAAA,GAAA,+HAAA,CAAA,KAAE,AAAD,EACV,0NACA;QAED,GAAG,KAAK;;;;;;AAGf;MAdS;AAgBT,SAAS,iBAAiB,EACxB,SAAS,EACT,GAAG,OACqD;IACxD,qBACE,sSAAC,qPAAA,CAAA,UAAgB,CAAC,SAAS;QACzB,aAAU;QACV,WAAW,CAAA,GAAA,+HAAA,CAAA,KAAE,AAAD,EAAE,wBAAwB;QACrC,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,sSAAC,qPAAA,CAAA,UAAgB,CAAC,IAAI;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,+HAAA,CAAA,KAAE,AAAD,EACV,uYACA;QAED,GAAG,KAAK;;;;;;AAGf;MAdS;AAgBT,SAAS,gBAAgB,EACvB,SAAS,EACT,GAAG,OAC0B;IAC7B,qBACE,sSAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,+HAAA,CAAA,KAAE,AAAD,EACV,yDACA;QAED,GAAG,KAAK;;;;;;AAGf;MAdS", "debugId": null}}, {"offset": {"line": 1166, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/bloomgoo/gold-exchange-platform/frontend/components/ui/date-time-display.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport React from \"react\"\r\nimport { cn } from \"@/lib/utils\"\r\nimport { formatDate, formatDateTime, formatTime, formatFullDateTime } from \"@/lib/utils\"\r\n\r\nexport type DateTimeFormat = \"date\" | \"time\" | \"dateTime\" | \"fullDateTime\"\r\n\r\nexport interface DateTimeDisplayProps {\r\n  date: Date | string | number | null | undefined\r\n  format?: DateTimeFormat\r\n  emptyValue?: string\r\n  className?: string\r\n  withTime?: boolean // Deprecated, use format=\"dateTime\" instead\r\n  timeClassName?: string\r\n}\r\n\r\n/**\r\n * Component hiển thị ngày giờ theo định dạng chuẩn Việt Nam\r\n * \r\n * @example\r\n * // Hiển thị ngày giờ (dd/MM/yyyy HH:mm)\r\n * <DateTimeDisplay date={new Date()} format=\"dateTime\" />\r\n * \r\n * // Hiển thị chỉ ngày (dd/MM/yyyy)\r\n * <DateTimeDisplay date=\"2023-01-01T00:00:00\" format=\"date\" />\r\n * \r\n * // Hiển thị chỉ thời gian (HH:mm:ss)\r\n * <DateTimeDisplay date={1672531200000} format=\"time\" />\r\n * \r\n * // Hiển thị ngày giờ đầy đủ (dd/MM/yyyy HH:mm:ss)\r\n * <DateTimeDisplay date={new Date()} format=\"fullDateTime\" />\r\n * \r\n * // Tùy chỉnh giá trị hiển thị khi date là null/undefined\r\n * <DateTimeDisplay date={null} emptyValue=\"Chưa cập nhật\" />\r\n */\r\nexport function DateTimeDisplay({\r\n  date,\r\n  format = \"fullDateTime\",\r\n  emptyValue = \"---\",\r\n  className,\r\n  withTime, // Deprecated\r\n  timeClassName\r\n}: DateTimeDisplayProps) {\r\n  // Xử lý tương thích ngược với prop withTime\r\n  if (withTime !== undefined) {\r\n    format = withTime ? \"dateTime\" : \"date\"\r\n  }\r\n\r\n  // Nếu date là null hoặc undefined, hiển thị giá trị mặc định\r\n  if (date === null || date === undefined) {\r\n    return <span className={cn(\"text-muted-foreground\", className)}>{emptyValue}</span>\r\n  }\r\n\r\n  // Định dạng ngày giờ theo format được chọn\r\n  let formattedValue: string\r\n  switch (format) {\r\n    case \"date\":\r\n      formattedValue = formatDate(date, emptyValue)\r\n      break\r\n    case \"time\":\r\n      formattedValue = formatTime(date, emptyValue)\r\n      break\r\n    case \"fullDateTime\":\r\n      formattedValue = formatFullDateTime(date, emptyValue)\r\n      break\r\n    case \"dateTime\":\r\n    default:\r\n      formattedValue = formatDateTime(date, emptyValue)\r\n      break\r\n  }\r\n\r\n  // Nếu là định dạng dateTime và có timeClassName, hiển thị ngày và giờ riêng biệt\r\n  if ((format === \"dateTime\" || format === \"fullDateTime\") && timeClassName) {\r\n    const dateObj = typeof date === 'string' || typeof date === 'number' \r\n      ? new Date(date) \r\n      : date\r\n    \r\n    if (!dateObj || !formattedValue || formattedValue === \"Không hợp lệ\") {\r\n      return <span className={cn(\"text-muted-foreground\", className)}>{formattedValue}</span>\r\n    }\r\n\r\n    const [datePart, timePart] = formattedValue.split(\" \")\r\n    \r\n    return (\r\n      <div className={cn(\"flex flex-col\", className)}>\r\n        <span>{datePart}</span>\r\n        <span className={cn(\"text-xs text-muted-foreground\", timeClassName)}>\r\n          {timePart}\r\n        </span>\r\n      </div>\r\n    )\r\n  }\r\n\r\n  // Hiển thị giá trị đã định dạng\r\n  return <span className={className}>{formattedValue}</span>\r\n}\r\n\r\n/**\r\n * Component hiển thị ngày theo định dạng chuẩn Việt Nam (dd/MM/yyyy)\r\n */\r\nexport function DateDisplay({\r\n  date,\r\n  emptyValue = \"---\",\r\n  className\r\n}: Omit<DateTimeDisplayProps, \"format\" | \"withTime\" | \"timeClassName\">) {\r\n  return (\r\n    <DateTimeDisplay\r\n      date={date}\r\n      format=\"date\"\r\n      emptyValue={emptyValue}\r\n      className={className}\r\n    />\r\n  )\r\n}\r\n\r\n/**\r\n * Component hiển thị thời gian theo định dạng chuẩn Việt Nam (HH:mm:ss)\r\n */\r\nexport function TimeDisplay({\r\n  date,\r\n  emptyValue = \"---\",\r\n  className\r\n}: Omit<DateTimeDisplayProps, \"format\" | \"withTime\" | \"timeClassName\">) {\r\n  return (\r\n    <DateTimeDisplay\r\n      date={date}\r\n      format=\"time\"\r\n      emptyValue={emptyValue}\r\n      className={className}\r\n    />\r\n  )\r\n}\r\n\r\n/**\r\n * Component hiển thị ngày giờ đầy đủ theo định dạng chuẩn Việt Nam (dd/MM/yyyy HH:mm:ss)\r\n */\r\nexport function FullDateTimeDisplay({\r\n  date,\r\n  emptyValue = \"---\",\r\n  className\r\n}: Omit<DateTimeDisplayProps, \"format\" | \"withTime\" | \"timeClassName\">) {\r\n  return (\r\n    <DateTimeDisplay\r\n      date={date}\r\n      format=\"fullDateTime\"\r\n      emptyValue={emptyValue}\r\n      className={className}\r\n    />\r\n  )\r\n}\r\n\r\n/**\r\n * Component hiển thị ngày giờ với thời gian ở dòng riêng biệt\r\n */\r\nexport function DateTimeWithSeparateTimeDisplay({\r\n  date,\r\n  emptyValue = \"---\",\r\n  className,\r\n  timeClassName\r\n}: Omit<DateTimeDisplayProps, \"format\" | \"withTime\">) {\r\n  return (\r\n    <DateTimeDisplay\r\n      date={date}\r\n      format=\"dateTime\"\r\n      emptyValue={emptyValue}\r\n      className={className}\r\n      timeClassName={timeClassName || \"text-xs text-muted-foreground\"}\r\n    />\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;AAGA;AAAA;AAHA;;;;AAoCO,SAAS,gBAAgB,EAC9B,IAAI,EACJ,SAAS,cAAc,EACvB,aAAa,KAAK,EAClB,SAAS,EACT,QAAQ,EACR,aAAa,EACQ;IACrB,4CAA4C;IAC5C,IAAI,aAAa,WAAW;QAC1B,SAAS,WAAW,aAAa;IACnC;IAEA,6DAA6D;IAC7D,IAAI,SAAS,QAAQ,SAAS,WAAW;QACvC,qBAAO,sSAAC;YAAK,WAAW,CAAA,GAAA,+HAAA,CAAA,KAAE,AAAD,EAAE,yBAAyB;sBAAa;;;;;;IACnE;IAEA,2CAA2C;IAC3C,IAAI;IACJ,OAAQ;QACN,KAAK;YACH,iBAAiB,CAAA,GAAA,+HAAA,CAAA,aAAU,AAAD,EAAE,MAAM;YAClC;QACF,KAAK;YACH,iBAAiB,CAAA,GAAA,+HAAA,CAAA,aAAU,AAAD,EAAE,MAAM;YAClC;QACF,KAAK;YACH,iBAAiB,CAAA,GAAA,+HAAA,CAAA,qBAAkB,AAAD,EAAE,MAAM;YAC1C;QACF,KAAK;QACL;YACE,iBAAiB,CAAA,GAAA,+HAAA,CAAA,iBAAc,AAAD,EAAE,MAAM;YACtC;IACJ;IAEA,iFAAiF;IACjF,IAAI,CAAC,WAAW,cAAc,WAAW,cAAc,KAAK,eAAe;QACzE,MAAM,UAAU,OAAO,SAAS,YAAY,OAAO,SAAS,WACxD,IAAI,KAAK,QACT;QAEJ,IAAI,CAAC,WAAW,CAAC,kBAAkB,mBAAmB,gBAAgB;YACpE,qBAAO,sSAAC;gBAAK,WAAW,CAAA,GAAA,+HAAA,CAAA,KAAE,AAAD,EAAE,yBAAyB;0BAAa;;;;;;QACnE;QAEA,MAAM,CAAC,UAAU,SAAS,GAAG,eAAe,KAAK,CAAC;QAElD,qBACE,sSAAC;YAAI,WAAW,CAAA,GAAA,+HAAA,CAAA,KAAE,AAAD,EAAE,iBAAiB;;8BAClC,sSAAC;8BAAM;;;;;;8BACP,sSAAC;oBAAK,WAAW,CAAA,GAAA,+HAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;8BAClD;;;;;;;;;;;;IAIT;IAEA,gCAAgC;IAChC,qBAAO,sSAAC;QAAK,WAAW;kBAAY;;;;;;AACtC;KA5DgB;AAiET,SAAS,YAAY,EAC1B,IAAI,EACJ,aAAa,KAAK,EAClB,SAAS,EAC2D;IACpE,qBACE,sSAAC;QACC,MAAM;QACN,QAAO;QACP,YAAY;QACZ,WAAW;;;;;;AAGjB;MAbgB;AAkBT,SAAS,YAAY,EAC1B,IAAI,EACJ,aAAa,KAAK,EAClB,SAAS,EAC2D;IACpE,qBACE,sSAAC;QACC,MAAM;QACN,QAAO;QACP,YAAY;QACZ,WAAW;;;;;;AAGjB;MAbgB;AAkBT,SAAS,oBAAoB,EAClC,IAAI,EACJ,aAAa,KAAK,EAClB,SAAS,EAC2D;IACpE,qBACE,sSAAC;QACC,MAAM;QACN,QAAO;QACP,YAAY;QACZ,WAAW;;;;;;AAGjB;MAbgB;AAkBT,SAAS,gCAAgC,EAC9C,IAAI,EACJ,aAAa,KAAK,EAClB,SAAS,EACT,aAAa,EACqC;IAClD,qBACE,sSAAC;QACC,MAAM;QACN,QAAO;QACP,YAAY;QACZ,WAAW;QACX,eAAe,iBAAiB;;;;;;AAGtC;MAfgB", "debugId": null}}, {"offset": {"line": 1331, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/bloomgoo/gold-exchange-platform/frontend/components/common/admin/ecom-orders/table/ecom-order-cell.tsx"], "sourcesContent": ["import { Badge } from \"@/components/ui/badge\"\r\nimport { <PERSON><PERSON> } from \"@/components/ui/button\"\r\nimport {\r\n  Dialog,\r\n  DialogContent,\r\n  DialogDescription,\r\n  DialogFooter,\r\n  DialogHeader,\r\n  DialogTitle\r\n} from \"@/components/ui/dialog\"\r\nimport {\r\n  DropdownMenu,\r\n  DropdownMenuContent,\r\n  DropdownMenuItem,\r\n  DropdownMenuSeparator,\r\n  DropdownMenuShortcut,\r\n  DropdownMenuTrigger\r\n} from \"@/components/ui/dropdown-menu\"\r\nimport {\r\n  Popover,\r\n  PopoverContent,\r\n  PopoverTrigger\r\n} from \"@/components/ui/popover\"\r\nimport { Avatar, AvatarFallback, AvatarImage } from \"@/components/ui/avatar\"\r\nimport { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from \"@/components/ui/tooltip\"\r\nimport { UserHoverCard } from \"@/components/common/user/user-hover-card\"\r\n\r\nimport { api } from '@/lib/api'\r\nimport { cn } from \"@/lib/utils\"\r\nimport { type ColumnDef, Row } from \"@tanstack/react-table\"\r\nimport { format, isValid } from \"date-fns\"\r\nimport { vi } from \"date-fns/locale\"\r\nimport {\r\n  Calendar,\r\n  CalendarPlus,\r\n  Check,\r\n  Edit,\r\n  Eye,\r\n  ArrowUpDown,\r\n  MoreVertical,\r\n  Trash2,\r\n  X,\r\n  User,\r\n  ChevronsUpDown,\r\n  CreditCard,\r\n  Package\r\n} from \"lucide-react\"\r\nimport { IconDotsVertical } from \"@tabler/icons-react\"\r\nimport { useId, useState } from \"react\"\r\nimport { toast } from \"sonner\"\r\nimport { EcomOrder, OrderStatus, PaymentStatus, EcomOrderDetail } from \"../type/ecom-orders\"\r\nimport {\r\n  Command,\r\n  CommandEmpty,\r\n  CommandGroup,\r\n  CommandInput,\r\n  CommandItem,\r\n} from \"@/components/ui/command\"\r\nimport { DateTimeDisplay } from \"@/components/ui/date-time-display\"\r\n\r\n// Format currency with VND\r\nconst formatCurrency = (amount: number | undefined) => {\r\n  if (amount === undefined || amount === null) return \"---\"\r\n  return new Intl.NumberFormat('vi-VN', {\r\n    maximumFractionDigits: 0\r\n  }).format(amount)\r\n}\r\n\r\n// Format date from ISO string\r\nconst formatDate = (dateStr: string | null) => {\r\n  if (!dateStr) return \"---\"\r\n  const date = new Date(dateStr)\r\n  if (!isValid(date)) return \"Không hợp lệ\"\r\n  try {\r\n    return format(date, \"dd/MM/yyyy HH:mm\", { locale: vi })\r\n  } catch (error) {\r\n    return \"Không hợp lệ\"\r\n  }\r\n}\r\n\r\n// Get color for order status badge\r\nconst getStatusColor = (status: OrderStatus) => {\r\n  switch (status) {\r\n    case OrderStatus.PENDING:\r\n      return 'bg-yellow-100 text-yellow-800 font-normal';\r\n    case OrderStatus.PROCESSING:\r\n      return 'bg-blue-100 text-blue-800 font-normal';\r\n    case OrderStatus.COMPLETED:\r\n      return 'bg-green-100 text-green-800 font-normal';\r\n    case OrderStatus.CANCELLED:\r\n      return 'bg-red-100 text-red-800 font-normal';\r\n    default:\r\n      return 'bg-gray-100 text-gray-800 font-normal';\r\n  }\r\n};\r\n\r\n// Order status in Vietnamese\r\nconst getStatusName = (status: OrderStatus) => {\r\n  switch (status) {\r\n    case OrderStatus.PENDING:\r\n      return 'Chờ xử lý';\r\n    case OrderStatus.PROCESSING:\r\n      return 'Đang xử lý';\r\n    case OrderStatus.COMPLETED:\r\n      return 'Hoàn thành';\r\n    case OrderStatus.CANCELLED:\r\n      return 'Đã hủy';\r\n    default:\r\n      return status;\r\n  }\r\n};\r\n\r\n// Get color for payment status badge\r\nconst getPaymentStatusColor = (status: PaymentStatus) => {\r\n  switch (status) {\r\n    case PaymentStatus.PAID:\r\n      return 'bg-green-100 text-green-800 font-normal';\r\n    case PaymentStatus.UNPAID:\r\n      return 'bg-red-100 text-red-800 font-normal';\r\n    default:\r\n      return 'bg-gray-100 text-gray-800 font-normal';\r\n  }\r\n};\r\n\r\n// Payment status in Vietnamese\r\nconst getPaymentStatusName = (status: PaymentStatus) => {\r\n  switch (status) {\r\n    case PaymentStatus.PAID:\r\n      return 'Đã thanh toán';\r\n    case PaymentStatus.UNPAID:\r\n      return 'Chưa thanh toán';\r\n    default:\r\n      return status;\r\n  }\r\n};\r\n\r\ninterface ActionsProps {\r\n  row: Row<EcomOrder>\r\n  onViewDetail: (order: EcomOrder) => void\r\n  onDelete: (order: EcomOrder) => void\r\n  onEdit: (order: EcomOrder) => void\r\n  onChangeStatus?: (order: EcomOrder, status: OrderStatus) => void\r\n}\r\n\r\nfunction Actions({ row, onViewDetail, onDelete, onEdit, onChangeStatus }: ActionsProps) {\r\n  const [showDeleteDialog, setShowDeleteDialog] = useState(false)\r\n  const order = row.original\r\n\r\n  const handleDelete = (order: EcomOrder) => {\r\n    onDelete(order)\r\n    setShowDeleteDialog(false)\r\n  }\r\n\r\n  // Xử lý khi click vào nút xem chi tiết\r\n  const handleViewDetail = async (order: EcomOrder) => {\r\n    try {\r\n      // Sử dụng api client để gọi API\r\n      const response = await api.get<EcomOrder>(`ecom-orders/${order.id}`);\r\n\r\n      // Chỉ truyền dữ liệu chi tiết nếu API trả về thành công\r\n      if (response) {\r\n        onViewDetail(response);\r\n      } else {\r\n        // Nếu không có response, hiển thị thông báo lỗi\r\n        toast.error(\"Không thể tải thông tin chi tiết đơn hàng. Vui lòng thử lại sau.\");\r\n      }\r\n    } catch (error) {\r\n      console.error('Error fetching order detail:', error);\r\n      // Hiển thị thông báo lỗi cho người dùng\r\n      toast.error(\"Có lỗi xảy ra khi tải thông tin đơn hàng. Vui lòng thử lại sau.\");\r\n    }\r\n  }\r\n\r\n  // Xử lý khi click vào nút chỉnh sửa\r\n  const handleEdit = async (order: EcomOrder) => {\r\n    try {\r\n      // Sử dụng api client để gọi API lấy dữ liệu đầy đủ giống như chi tiết\r\n      const response = await api.get<EcomOrder>(`ecom-orders/${order.id}`);\r\n\r\n      // Chỉ truyền dữ liệu chi tiết nếu API trả về thành công\r\n      if (response) {\r\n        onEdit(response);\r\n      } else {\r\n        // Nếu không có response, hiển thị thông báo lỗi\r\n        toast.error(\"Không thể tải thông tin chi tiết đơn hàng. Vui lòng thử lại sau.\");\r\n      }\r\n    } catch (error) {\r\n      console.error('Error fetching order detail for edit:', error);\r\n      // Hiển thị thông báo lỗi cho người dùng\r\n      toast.error(\"Có lỗi xảy ra khi tải thông tin đơn hàng. Vui lòng thử lại sau.\");\r\n    }\r\n  }\r\n\r\n  return (\r\n    <>\r\n      {/* Dialog xác nhận xóa */}\r\n      <Dialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>\r\n        <DialogContent>\r\n          <DialogHeader>\r\n            <DialogTitle>Xác nhận xóa</DialogTitle>\r\n            <DialogDescription>\r\n              Bạn có chắc chắn muốn xóa đơn hàng <span className=\"font-medium\">#{order.orderNumber}</span>?\r\n              <p className=\"mt-2\">Hành động này không thể hoàn tác.</p>\r\n            </DialogDescription>\r\n          </DialogHeader>\r\n          <DialogFooter>\r\n            <Button variant=\"outline\" onClick={() => setShowDeleteDialog(false)}>Hủy</Button>\r\n            <Button variant=\"destructive\" onClick={() => handleDelete(order)}>Xóa</Button>\r\n          </DialogFooter>\r\n        </DialogContent>\r\n      </Dialog>\r\n\r\n      <div className=\"flex justify-end px-1 sticky-action-cell h-full items-center\">\r\n        <DropdownMenu>\r\n          <DropdownMenuTrigger asChild>\r\n            <Button\r\n              variant=\"ghost\"\r\n              size=\"sm\"\r\n              className=\"flex h-6 w-6 p-0 data-[state=open]:bg-muted transition-colors hover:bg-muted\"\r\n            >\r\n              <MoreVertical className=\"h-3 w-3\" />\r\n              <span className=\"sr-only\">Mở menu</span>\r\n            </Button>\r\n          </DropdownMenuTrigger>\r\n          <DropdownMenuContent align=\"end\" className=\"p-1\">\r\n            <DropdownMenuItem onClick={() => handleViewDetail(order)}>\r\n              <Eye className=\"mr-2 h-3.5 w-3.5\" />\r\n              <span className=\"flex-1 text-sm\">Chi tiết</span>\r\n              <DropdownMenuShortcut className=\"text-sm\">⌘V</DropdownMenuShortcut>\r\n            </DropdownMenuItem>\r\n            <DropdownMenuItem onClick={() => handleEdit(order)}>\r\n              <Edit className=\"mr-2 h-3.5 w-3.5\" />\r\n              <span className=\"flex-1 text-sm\">Chỉnh sửa</span>\r\n              <DropdownMenuShortcut className=\"text-sm\">⌘E</DropdownMenuShortcut>\r\n            </DropdownMenuItem>\r\n\r\n            <DropdownMenuSeparator />\r\n\r\n            {/* Status change options */}\r\n            {onChangeStatus && order.status !== OrderStatus.COMPLETED && order.status !== OrderStatus.CANCELLED && (\r\n              <>\r\n                <DropdownMenuItem onClick={() => onChangeStatus(order, OrderStatus.COMPLETED)}>\r\n                  <Check className=\"mr-2 h-3.5 w-3.5 text-green-500\" />\r\n                  <span className=\"flex-1 text-sm text-green-500\">Đánh dấu hoàn thành</span>\r\n                </DropdownMenuItem>\r\n\r\n                <DropdownMenuItem\r\n                  onClick={() => onChangeStatus(order, OrderStatus.CANCELLED)}\r\n                >\r\n                  <X className=\"mr-2 h-3.5 w-3.5 text-red-500\" />\r\n                  <span className=\"flex-1 text-sm text-red-500\">Hủy đơn hàng</span>\r\n                </DropdownMenuItem>\r\n\r\n                <DropdownMenuSeparator />\r\n              </>\r\n            )}\r\n\r\n            <DropdownMenuItem\r\n              onClick={() => setShowDeleteDialog(true)}\r\n              className=\"text-destructive focus:text-destructive\"\r\n            >\r\n              <Trash2 className=\"mr-2 h-3.5 w-3.5 text-destructive\" />\r\n              <span className=\"flex-1 text-sm text-destructive\">Xóa</span>\r\n              <DropdownMenuShortcut className=\"text-sm text-destructive\">⌫</DropdownMenuShortcut>\r\n            </DropdownMenuItem>\r\n          </DropdownMenuContent>\r\n        </DropdownMenu>\r\n      </div>\r\n    </>\r\n  )\r\n}\r\n\r\n// Status selector for changing order status\r\ninterface OrderStatusSelectorProps {\r\n  status: OrderStatus;\r\n  onStatusChange: (status: OrderStatus) => void;\r\n  disabled?: boolean;\r\n}\r\n\r\nexport function OrderStatusSelector({ status, onStatusChange, disabled = false }: OrderStatusSelectorProps) {\r\n  const [open, setOpen] = useState<boolean>(false);\r\n\r\n  // Tất cả các trạng thái có thể có\r\n  const statusOptions = [\r\n    { value: OrderStatus.PENDING as string, label: 'Chờ xử lý', color: 'bg-yellow-100 text-yellow-800 font-normal' },\r\n    { value: OrderStatus.PROCESSING as string, label: 'Đang xử lý', color: 'bg-blue-100 text-blue-800 font-normal' },\r\n    { value: OrderStatus.COMPLETED as string, label: 'Hoàn thành', color: 'bg-green-100 text-green-800 font-normal' },\r\n    { value: OrderStatus.CANCELLED as string, label: 'Đã hủy', color: 'bg-red-100 text-red-800 font-normal' },\r\n  ];\r\n\r\n  const currentStatus = statusOptions.find((option) => option.value === status) || statusOptions[0];\r\n\r\n  return (\r\n    <Popover open={open} onOpenChange={setOpen}>\r\n      <PopoverTrigger asChild>\r\n        <Button\r\n          variant=\"ghost\"\r\n          role=\"combobox\"\r\n          aria-expanded={open}\r\n          disabled={disabled}\r\n          className=\"justify-between h-auto p-1 hover:bg-transparent\"\r\n        >\r\n          <Badge className={currentStatus.color}>\r\n            {currentStatus.label}\r\n          </Badge>\r\n        </Button>\r\n      </PopoverTrigger>\r\n      <PopoverContent className=\"w-[200px] p-0\">\r\n        <Command>\r\n          <CommandInput placeholder=\"Tìm trạng thái...\" />\r\n          <CommandEmpty>Không tìm thấy trạng thái</CommandEmpty>\r\n          <CommandGroup>\r\n            {statusOptions.map((option) => (\r\n              <CommandItem\r\n                key={option.value}\r\n                value={option.value}\r\n                onSelect={() => {\r\n                  onStatusChange(option.value as OrderStatus);\r\n                  setOpen(false);\r\n                }}\r\n              >\r\n                <Check\r\n                  className={cn(\r\n                    \"mr-2 h-4 w-4\",\r\n                    status === option.value ? \"opacity-100\" : \"opacity-0\"\r\n                  )}\r\n                />\r\n                <Badge className={option.color}>\r\n                  {option.label}\r\n                </Badge>\r\n              </CommandItem>\r\n            ))}\r\n          </CommandGroup>\r\n        </Command>\r\n      </PopoverContent>\r\n    </Popover>\r\n  );\r\n}\r\n\r\n// Payment status selector for changing payment status\r\ninterface PaymentStatusSelectorProps {\r\n  status: PaymentStatus;\r\n  onStatusChange: (status: PaymentStatus) => void;\r\n  disabled?: boolean;\r\n}\r\n\r\nexport function PaymentStatusSelector({ status, onStatusChange, disabled = false }: PaymentStatusSelectorProps) {\r\n  const [open, setOpen] = useState<boolean>(false);\r\n\r\n  // Tất cả các trạng thái thanh toán có thể có\r\n  const statusOptions = [\r\n    { value: PaymentStatus.PAID as string, label: 'Đã thanh toán', color: 'bg-green-100 text-green-800 font-normal' },\r\n    { value: PaymentStatus.UNPAID as string, label: 'Chưa thanh toán', color: 'bg-red-100 text-red-800 font-normal' },\r\n  ];\r\n\r\n  const currentStatus = statusOptions.find((option) => option.value === status) || statusOptions[0];\r\n\r\n  return (\r\n    <Popover open={open} onOpenChange={setOpen}>\r\n      <PopoverTrigger asChild>\r\n        <Button\r\n          variant=\"ghost\"\r\n          role=\"combobox\"\r\n          aria-expanded={open}\r\n          disabled={disabled}\r\n          className=\"justify-between h-auto p-1 hover:bg-transparent\"\r\n        >\r\n          <Badge className={currentStatus.color}>\r\n            {currentStatus.label}\r\n          </Badge>\r\n        </Button>\r\n      </PopoverTrigger>\r\n      <PopoverContent className=\"w-[200px] p-0\">\r\n        <Command>\r\n          <CommandInput placeholder=\"Tìm trạng thái...\" />\r\n          <CommandEmpty>Không tìm thấy trạng thái</CommandEmpty>\r\n          <CommandGroup>\r\n            {statusOptions.map((option) => (\r\n              <CommandItem\r\n                key={option.value}\r\n                value={option.value}\r\n                onSelect={() => {\r\n                  onStatusChange(option.value as PaymentStatus);\r\n                  setOpen(false);\r\n                }}\r\n              >\r\n                <Check\r\n                  className={cn(\r\n                    \"mr-2 h-4 w-4\",\r\n                    status === option.value ? \"opacity-100\" : \"opacity-0\"\r\n                  )}\r\n                />\r\n                <Badge className={option.color}>\r\n                  {option.label}\r\n                </Badge>\r\n              </CommandItem>\r\n            ))}\r\n          </CommandGroup>\r\n        </Command>\r\n      </PopoverContent>\r\n    </Popover>\r\n  );\r\n}\r\n\r\ninterface EcomOrderColumnsProps {\r\n  onViewDetail: (order: EcomOrder) => void\r\n  onDelete: (order: EcomOrder) => void\r\n  onEdit: (order: EcomOrder) => void\r\n  onChangeStatus?: (order: EcomOrder, status: OrderStatus) => void\r\n  onChangePaymentStatus?: (order: EcomOrder, status: PaymentStatus) => void\r\n}\r\n\r\nexport function getEcomOrderColumns({\r\n  onViewDetail,\r\n  onDelete,\r\n  onEdit,\r\n  onChangeStatus,\r\n  onChangePaymentStatus\r\n}: EcomOrderColumnsProps): ColumnDef<EcomOrder>[] {\r\n  return [\r\n    {\r\n      accessorKey: \"orderNumber\",\r\n      header: ({ column }) => {\r\n        return (\r\n          <Button\r\n            variant=\"ghost\"\r\n            size=\"sm\"\r\n            onClick={() => column.toggleSorting(column.getIsSorted() === \"asc\")}\r\n            className=\"flex items-center gap-1 px-0 h-6 py-0\"\r\n            data-column-id=\"orderNumber\"\r\n          >\r\n            <span className=\"text-xs\">Số đơn hàng</span>\r\n            <ArrowUpDown className=\"ml-1 h-3 w-3\" />\r\n          </Button>\r\n        )\r\n      },\r\n      meta: {\r\n        header: \"Số đơn hàng\"\r\n      },\r\n      cell: ({ row }) => {\r\n        const orderNumber = row.original.orderNumber;\r\n        return (\r\n          <div className=\"text-sm\">\r\n            {orderNumber || \"---\"}\r\n          </div>\r\n        )\r\n      },\r\n      enableSorting: true,\r\n    },\r\n    // Đã loại bỏ cột orderType và businessType vì không tồn tại trong EcomOrder\r\n\r\n    {\r\n      id: \"totalQuantity\",\r\n      header: () => {\r\n        return (\r\n          <Button\r\n            variant=\"ghost\"\r\n            size=\"sm\"\r\n            className=\"flex items-center gap-1 px-0 h-6 py-0\"\r\n            data-column-id=\"totalQuantity\"\r\n          >\r\n            <span className=\"text-xs\">Tổng số lượng</span>\r\n          </Button>\r\n        )\r\n      },\r\n      meta: {\r\n        header: \"Tổng số lượng\"\r\n      },\r\n      cell: ({ row }) => {\r\n        const order = row.original;\r\n        // Tính tổng số lượng sản phẩm\r\n        const totalQuantity = order.orderDetails?.reduce((sum: number, detail: EcomOrderDetail) => {\r\n          return sum + (detail.quantity || 0);\r\n        }, 0) || 0;\r\n        return (\r\n          <div className=\"text-sm\">\r\n            {totalQuantity.toLocaleString()}\r\n          </div>\r\n        )\r\n      },\r\n      enableSorting: false,\r\n    },\r\n    {\r\n      accessorKey: \"totalAmount\",\r\n      header: ({ column }) => {\r\n        return (\r\n          <Button\r\n            variant=\"ghost\"\r\n            size=\"sm\"\r\n            onClick={() => column.toggleSorting(column.getIsSorted() === \"asc\")}\r\n            className=\"flex items-center gap-1 px-0 h-6 py-0\"\r\n            data-column-id=\"totalAmount\"\r\n          >\r\n            <span className=\"text-xs\">Tổng giá trị</span>\r\n            <ArrowUpDown className=\"ml-1 h-3 w-3\" />\r\n          </Button>\r\n        )\r\n      },\r\n      meta: {\r\n        header: \"Tổng giá trị\"\r\n      },\r\n      cell: ({ row }) => {\r\n        const totalAmount = row.getValue(\"totalAmount\") as number | undefined\r\n\r\n        return (\r\n          <div className=\"flex items-center gap-1 text-sm\">\r\n            <span>{formatCurrency(totalAmount)} VND</span>\r\n          </div>\r\n        )\r\n      },\r\n      enableSorting: true,\r\n    },\r\n    {\r\n      accessorKey: \"paymentStatus\",\r\n      header: ({ column }) => {\r\n        return (\r\n          <Button\r\n            variant=\"ghost\"\r\n            size=\"sm\"\r\n            onClick={() => column.toggleSorting(column.getIsSorted() === \"asc\")}\r\n            className=\"flex items-center gap-1 px-0 h-6 py-0\"\r\n            data-column-id=\"paymentStatus\"\r\n          >\r\n            <span className=\"text-xs\">Trạng thái thanh toán</span>\r\n            <ArrowUpDown className=\"ml-1 h-3 w-3\" />\r\n          </Button>\r\n        )\r\n      },\r\n      meta: {\r\n        header: \"Trạng thái thanh toán\"\r\n      },\r\n      cell: ({ row }) => {\r\n        const paymentStatus = row.getValue(\"paymentStatus\") as PaymentStatus\r\n        const order = row.original\r\n\r\n        // Sử dụng PaymentStatusSelector nếu có callback onChangePaymentStatus\r\n        return onChangePaymentStatus ? (\r\n          <PaymentStatusSelector\r\n            status={paymentStatus}\r\n            onStatusChange={(newStatus) => onChangePaymentStatus(order, newStatus)}\r\n          />\r\n        ) : (\r\n          <Badge className={paymentStatus === PaymentStatus.PAID ? 'bg-green-100 text-green-800 font-normal' : 'bg-red-100 text-red-800 font-normal'}>\r\n            {paymentStatus === PaymentStatus.PAID ? 'Đã thanh toán' : 'Chưa thanh toán'}\r\n          </Badge>\r\n        )\r\n      },\r\n      enableSorting: true,\r\n      size: 150,\r\n    },\r\n    // Đã loại bỏ các cột liên quan đến tất toán và phê duyệt vì không tồn tại trong EcomOrder\r\n    {\r\n      accessorKey: \"status\",\r\n      header: ({ column }) => {\r\n        return (\r\n          <Button\r\n            variant=\"ghost\"\r\n            size=\"sm\"\r\n            onClick={() => column.toggleSorting(column.getIsSorted() === \"asc\")}\r\n            className=\"flex items-center gap-1 px-0 h-6 py-0\"\r\n            data-column-id=\"status\"\r\n          >\r\n            <span className=\"text-xs\">Trạng thái</span>\r\n            <ArrowUpDown className=\"ml-1 h-3 w-3\" />\r\n          </Button>\r\n        )\r\n      },\r\n      meta: {\r\n        header: \"Trạng thái\"\r\n      },\r\n      cell: ({ row }) => {\r\n        const status = row.getValue(\"status\") as OrderStatus\r\n        const order = row.original\r\n\r\n        return onChangeStatus ? (\r\n          <OrderStatusSelector\r\n            status={status}\r\n            onStatusChange={(newStatus) => onChangeStatus(order, newStatus)}\r\n          />\r\n        ) : (\r\n          <Badge className={getStatusColor(status)}>\r\n            {getStatusName(status)}\r\n          </Badge>\r\n        )\r\n      },\r\n      enableSorting: true,\r\n      size: 120,\r\n    },\r\n    {\r\n      accessorKey: \"creator\",\r\n      header: ({ column }) => {\r\n        return (\r\n          <Button\r\n            variant=\"ghost\"\r\n            size=\"sm\"\r\n            onClick={() => column.toggleSorting(column.getIsSorted() === \"asc\")}\r\n            className=\"flex items-center gap-1 px-0 h-6 py-0\"\r\n            data-column-id=\"creator\"\r\n          >\r\n            <span className=\"text-xs\">Người tạo</span>\r\n            <ArrowUpDown className=\"ml-1 h-3 w-3\" />\r\n          </Button>\r\n        )\r\n      },\r\n      meta: {\r\n        header: \"Người tạo\"\r\n      },\r\n      cell: ({ row }) => {\r\n        const order = row.original;\r\n\r\n        // Kiểm tra cả hai trường hợp: creator là object hoặc createdBy là string\r\n        const hasCreator = order.creator && typeof order.creator === 'object';\r\n        const hasCreatedBy = order.createdBy && typeof order.createdBy === 'string';\r\n\r\n        // Nếu có thông tin user, hiển thị thông tin chi tiết\r\n        if (hasCreator) {\r\n          return (\r\n            <UserHoverCard user={order.creator} showAvatar={true} size=\"sm\">\r\n              <div className=\"max-w-[120px] overflow-hidden\">\r\n                <div className=\"text-xs font-normal truncate\">{order.creator?.fullName || order.creator?.username || 'User'}</div>\r\n                <div className=\"text-xs text-muted-foreground truncate\">{order.creator?.email || ''}</div>\r\n              </div>\r\n            </UserHoverCard>\r\n          );\r\n        }\r\n\r\n        // Nếu chỉ có ID, hiển thị ID rút gọn\r\n        if (hasCreatedBy) {\r\n          return (\r\n            <UserHoverCard userId={order.createdBy || ''} showAvatar={true} size=\"sm\">\r\n              <div className=\"flex items-center gap-1\">\r\n                <User className=\"h-4 w-4 text-blue-500\" />\r\n                <span className=\"text-xs text-muted-foreground\">\r\n                  {order.createdBy?.substring(0, 8)}...\r\n                </span>\r\n              </div>\r\n            </UserHoverCard>\r\n          );\r\n        }\r\n\r\n        // Nếu không có thông tin, hiển thị ---\r\n        return (\r\n          <span className=\"text-xs text-muted-foreground\">---</span>\r\n        );\r\n      },\r\n      enableSorting: true,\r\n    },\r\n    {\r\n      accessorKey: \"createdAt\",\r\n      header: ({ column }) => {\r\n        return (\r\n          <Button\r\n            variant=\"ghost\"\r\n            size=\"sm\"\r\n            onClick={() => column.toggleSorting(column.getIsSorted() === \"asc\")}\r\n            className=\"flex items-center gap-1 px-0 h-6 py-0\"\r\n            data-column-id=\"createdAt\"\r\n          >\r\n            <span className=\"text-xs\">Ngày tạo</span>\r\n            <ArrowUpDown className=\"ml-1 h-3 w-3\" />\r\n          </Button>\r\n        )\r\n      },\r\n      meta: {\r\n        header: \"Ngày tạo\"\r\n      },\r\n      cell: ({ row }) => {\r\n        const date = row.original\r\n        return (\r\n          <DateTimeDisplay\r\n            date={date.createdAt}\r\n            className=\"text-sm\"\r\n            timeClassName=\"text-xs text-muted-foreground\"\r\n          />\r\n        )\r\n      },\r\n      enableSorting: true,\r\n      size: 150,\r\n    },\r\n    {\r\n      accessorKey: \"updater\",\r\n      header: ({ column }) => {\r\n        return (\r\n          <Button\r\n            variant=\"ghost\"\r\n            size=\"sm\"\r\n            onClick={() => column.toggleSorting(column.getIsSorted() === \"asc\")}\r\n            className=\"flex items-center gap-1 px-0 h-6 py-0\"\r\n            data-column-id=\"updater\"\r\n          >\r\n            <span className=\"text-xs\">Người cập nhật</span>\r\n            <ArrowUpDown className=\"ml-1 h-3 w-3\" />\r\n          </Button>\r\n        )\r\n      },\r\n      meta: {\r\n        header: \"Người cập nhật\"\r\n      },\r\n      cell: ({ row }) => {\r\n        const order = row.original;\r\n\r\n        // Kiểm tra cả hai trường hợp: updater là object hoặc updatedBy là string\r\n        const hasUpdater = order.updater && typeof order.updater === 'object';\r\n        const hasUpdatedBy = order.updatedBy && typeof order.updatedBy === 'string';\r\n\r\n        // Nếu có thông tin user, hiển thị thông tin chi tiết\r\n        if (hasUpdater) {\r\n          return (\r\n            <UserHoverCard user={order.updater} showAvatar={true} size=\"sm\">\r\n              <div className=\"max-w-[120px] overflow-hidden\">\r\n                <div className=\"text-xs font-normal truncate\">{order.updater?.fullName || order.updater?.username || 'User'}</div>\r\n                <div className=\"text-xs text-muted-foreground truncate\">{order.updater?.email || ''}</div>\r\n              </div>\r\n            </UserHoverCard>\r\n          );\r\n        }\r\n\r\n        // Nếu chỉ có ID, hiển thị ID rút gọn\r\n        if (hasUpdatedBy) {\r\n          return (\r\n            <UserHoverCard userId={order.updatedBy || ''} showAvatar={true} size=\"sm\">\r\n              <div className=\"flex items-center gap-1\">\r\n                <User className=\"h-4 w-4 text-blue-500\" />\r\n                <span className=\"text-xs text-muted-foreground\">\r\n                  {order.updatedBy?.substring(0, 8)}...\r\n                </span>\r\n              </div>\r\n            </UserHoverCard>\r\n          );\r\n        }\r\n\r\n        // Nếu không có thông tin, hiển thị ---\r\n        return (\r\n          <span className=\"text-xs text-muted-foreground\">---</span>\r\n        );\r\n      },\r\n      enableSorting: true,\r\n      enableHiding: true,\r\n    },\r\n    {\r\n      id: 'actions',\r\n      size: 40,\r\n      enableHiding: false,\r\n      header: () => <div data-column-id=\"actions\"></div>,\r\n      cell: ({ row }) => (\r\n        <Actions\r\n          row={row}\r\n          onViewDetail={onViewDetail}\r\n          onDelete={onDelete}\r\n          onEdit={onEdit}\r\n          onChangeStatus={onChangeStatus}\r\n        />\r\n      ),\r\n      meta: {\r\n        isSticky: true, // Đánh dấu cột này là cố định\r\n        position: 'right', // Vị trí cố định (right hoặc left)\r\n        header: \"Thao tác\"\r\n      }\r\n    }\r\n  ]\r\n}"], "names": [], "mappings": ";;;;;;AAAA;AACA;AACA;AAQA;AAQA;AAOA;AAEA;AACA;AAAA;AAEA;AAAA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAgBA;AACA;AACA;AACA;AAOA;;;;;;;;;;;;;;;;;;;AAEA,2BAA2B;AAC3B,MAAM,iBAAiB,CAAC;IACtB,IAAI,WAAW,aAAa,WAAW,MAAM,OAAO;IACpD,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;QACpC,uBAAuB;IACzB,GAAG,MAAM,CAAC;AACZ;AAEA,8BAA8B;AAC9B,MAAM,aAAa,CAAC;IAClB,IAAI,CAAC,SAAS,OAAO;IACrB,MAAM,OAAO,IAAI,KAAK;IACtB,IAAI,CAAC,CAAA,GAAA,iMAAA,CAAA,UAAO,AAAD,EAAE,OAAO,OAAO;IAC3B,IAAI;QACF,OAAO,CAAA,GAAA,gNAAA,CAAA,SAAM,AAAD,EAAE,MAAM,oBAAoB;YAAE,QAAQ,sMAAA,CAAA,KAAE;QAAC;IACvD,EAAE,OAAO,OAAO;QACd,OAAO;IACT;AACF;AAEA,mCAAmC;AACnC,MAAM,iBAAiB,CAAC;IACtB,OAAQ;QACN,KAAK,4KAAA,CAAA,cAAW,CAAC,OAAO;YACtB,OAAO;QACT,KAAK,4KAAA,CAAA,cAAW,CAAC,UAAU;YACzB,OAAO;QACT,KAAK,4KAAA,CAAA,cAAW,CAAC,SAAS;YACxB,OAAO;QACT,KAAK,4KAAA,CAAA,cAAW,CAAC,SAAS;YACxB,OAAO;QACT;YACE,OAAO;IACX;AACF;AAEA,6BAA6B;AAC7B,MAAM,gBAAgB,CAAC;IACrB,OAAQ;QACN,KAAK,4KAAA,CAAA,cAAW,CAAC,OAAO;YACtB,OAAO;QACT,KAAK,4KAAA,CAAA,cAAW,CAAC,UAAU;YACzB,OAAO;QACT,KAAK,4KAAA,CAAA,cAAW,CAAC,SAAS;YACxB,OAAO;QACT,KAAK,4KAAA,CAAA,cAAW,CAAC,SAAS;YACxB,OAAO;QACT;YACE,OAAO;IACX;AACF;AAEA,qCAAqC;AACrC,MAAM,wBAAwB,CAAC;IAC7B,OAAQ;QACN,KAAK,4KAAA,CAAA,gBAAa,CAAC,IAAI;YACrB,OAAO;QACT,KAAK,4KAAA,CAAA,gBAAa,CAAC,MAAM;YACvB,OAAO;QACT;YACE,OAAO;IACX;AACF;AAEA,+BAA+B;AAC/B,MAAM,uBAAuB,CAAC;IAC5B,OAAQ;QACN,KAAK,4KAAA,CAAA,gBAAa,CAAC,IAAI;YACrB,OAAO;QACT,KAAK,4KAAA,CAAA,gBAAa,CAAC,MAAM;YACvB,OAAO;QACT;YACE,OAAO;IACX;AACF;AAUA,SAAS,QAAQ,EAAE,GAAG,EAAE,YAAY,EAAE,QAAQ,EAAE,MAAM,EAAE,cAAc,EAAgB;;IACpF,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,sQAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,QAAQ,IAAI,QAAQ;IAE1B,MAAM,eAAe,CAAC;QACpB,SAAS;QACT,oBAAoB;IACtB;IAEA,uCAAuC;IACvC,MAAM,mBAAmB,OAAO;QAC9B,IAAI;YACF,gCAAgC;YAChC,MAAM,WAAW,MAAM,6GAAA,CAAA,MAAG,CAAC,GAAG,CAAY,CAAC,YAAY,EAAE,MAAM,EAAE,EAAE;YAEnE,wDAAwD;YACxD,IAAI,UAAU;gBACZ,aAAa;YACf,OAAO;gBACL,gDAAgD;gBAChD,2QAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACd;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,gCAAgC;YAC9C,wCAAwC;YACxC,2QAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd;IACF;IAEA,oCAAoC;IACpC,MAAM,aAAa,OAAO;QACxB,IAAI;YACF,sEAAsE;YACtE,MAAM,WAAW,MAAM,6GAAA,CAAA,MAAG,CAAC,GAAG,CAAY,CAAC,YAAY,EAAE,MAAM,EAAE,EAAE;YAEnE,wDAAwD;YACxD,IAAI,UAAU;gBACZ,OAAO;YACT,OAAO;gBACL,gDAAgD;gBAChD,2QAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACd;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yCAAyC;YACvD,wCAAwC;YACxC,2QAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd;IACF;IAEA,qBACE;;0BAEE,sSAAC,8HAAA,CAAA,SAAM;gBAAC,MAAM;gBAAkB,cAAc;0BAC5C,cAAA,sSAAC,8HAAA,CAAA,gBAAa;;sCACZ,sSAAC,8HAAA,CAAA,eAAY;;8CACX,sSAAC,8HAAA,CAAA,cAAW;8CAAC;;;;;;8CACb,sSAAC,8HAAA,CAAA,oBAAiB;;wCAAC;sDACkB,sSAAC;4CAAK,WAAU;;gDAAc;gDAAE,MAAM,WAAW;;;;;;;wCAAQ;sDAC5F,sSAAC;4CAAE,WAAU;sDAAO;;;;;;;;;;;;;;;;;;sCAGxB,sSAAC,8HAAA,CAAA,eAAY;;8CACX,sSAAC,8HAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAU,SAAS,IAAM,oBAAoB;8CAAQ;;;;;;8CACrE,sSAAC,8HAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAc,SAAS,IAAM,aAAa;8CAAQ;;;;;;;;;;;;;;;;;;;;;;;0BAKxE,sSAAC;gBAAI,WAAU;0BACb,cAAA,sSAAC,wIAAA,CAAA,eAAY;;sCACX,sSAAC,wIAAA,CAAA,sBAAmB;4BAAC,OAAO;sCAC1B,cAAA,sSAAC,8HAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,WAAU;;kDAEV,sSAAC,iTAAA,CAAA,eAAY;wCAAC,WAAU;;;;;;kDACxB,sSAAC;wCAAK,WAAU;kDAAU;;;;;;;;;;;;;;;;;sCAG9B,sSAAC,wIAAA,CAAA,sBAAmB;4BAAC,OAAM;4BAAM,WAAU;;8CACzC,sSAAC,wIAAA,CAAA,mBAAgB;oCAAC,SAAS,IAAM,iBAAiB;;sDAChD,sSAAC,uRAAA,CAAA,MAAG;4CAAC,WAAU;;;;;;sDACf,sSAAC;4CAAK,WAAU;sDAAiB;;;;;;sDACjC,sSAAC,wIAAA,CAAA,uBAAoB;4CAAC,WAAU;sDAAU;;;;;;;;;;;;8CAE5C,sSAAC,wIAAA,CAAA,mBAAgB;oCAAC,SAAS,IAAM,WAAW;;sDAC1C,sSAAC,kSAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;sDAChB,sSAAC;4CAAK,WAAU;sDAAiB;;;;;;sDACjC,sSAAC,wIAAA,CAAA,uBAAoB;4CAAC,WAAU;sDAAU;;;;;;;;;;;;8CAG5C,sSAAC,wIAAA,CAAA,wBAAqB;;;;;gCAGrB,kBAAkB,MAAM,MAAM,KAAK,4KAAA,CAAA,cAAW,CAAC,SAAS,IAAI,MAAM,MAAM,KAAK,4KAAA,CAAA,cAAW,CAAC,SAAS,kBACjG;;sDACE,sSAAC,wIAAA,CAAA,mBAAgB;4CAAC,SAAS,IAAM,eAAe,OAAO,4KAAA,CAAA,cAAW,CAAC,SAAS;;8DAC1E,sSAAC,2RAAA,CAAA,QAAK;oDAAC,WAAU;;;;;;8DACjB,sSAAC;oDAAK,WAAU;8DAAgC;;;;;;;;;;;;sDAGlD,sSAAC,wIAAA,CAAA,mBAAgB;4CACf,SAAS,IAAM,eAAe,OAAO,4KAAA,CAAA,cAAW,CAAC,SAAS;;8DAE1D,sSAAC,mRAAA,CAAA,IAAC;oDAAC,WAAU;;;;;;8DACb,sSAAC;oDAAK,WAAU;8DAA8B;;;;;;;;;;;;sDAGhD,sSAAC,wIAAA,CAAA,wBAAqB;;;;;;;8CAI1B,sSAAC,wIAAA,CAAA,mBAAgB;oCACf,SAAS,IAAM,oBAAoB;oCACnC,WAAU;;sDAEV,sSAAC,iSAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;sDAClB,sSAAC;4CAAK,WAAU;sDAAkC;;;;;;sDAClD,sSAAC,wIAAA,CAAA,uBAAoB;4CAAC,WAAU;sDAA2B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOzE;GA9HS;KAAA;AAuIF,SAAS,oBAAoB,EAAE,MAAM,EAAE,cAAc,EAAE,WAAW,KAAK,EAA4B;;IACxG,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,sQAAA,CAAA,WAAQ,AAAD,EAAW;IAE1C,kCAAkC;IAClC,MAAM,gBAAgB;QACpB;YAAE,OAAO,4KAAA,CAAA,cAAW,CAAC,OAAO;YAAY,OAAO;YAAa,OAAO;QAA4C;QAC/G;YAAE,OAAO,4KAAA,CAAA,cAAW,CAAC,UAAU;YAAY,OAAO;YAAc,OAAO;QAAwC;QAC/G;YAAE,OAAO,4KAAA,CAAA,cAAW,CAAC,SAAS;YAAY,OAAO;YAAc,OAAO;QAA0C;QAChH;YAAE,OAAO,4KAAA,CAAA,cAAW,CAAC,SAAS;YAAY,OAAO;YAAU,OAAO;QAAsC;KACzG;IAED,MAAM,gBAAgB,cAAc,IAAI,CAAC,CAAC,SAAW,OAAO,KAAK,KAAK,WAAW,aAAa,CAAC,EAAE;IAEjG,qBACE,sSAAC,+HAAA,CAAA,UAAO;QAAC,MAAM;QAAM,cAAc;;0BACjC,sSAAC,+HAAA,CAAA,iBAAc;gBAAC,OAAO;0BACrB,cAAA,sSAAC,8HAAA,CAAA,SAAM;oBACL,SAAQ;oBACR,MAAK;oBACL,iBAAe;oBACf,UAAU;oBACV,WAAU;8BAEV,cAAA,sSAAC,6HAAA,CAAA,QAAK;wBAAC,WAAW,cAAc,KAAK;kCAClC,cAAc,KAAK;;;;;;;;;;;;;;;;0BAI1B,sSAAC,+HAAA,CAAA,iBAAc;gBAAC,WAAU;0BACxB,cAAA,sSAAC,+HAAA,CAAA,UAAO;;sCACN,sSAAC,+HAAA,CAAA,eAAY;4BAAC,aAAY;;;;;;sCAC1B,sSAAC,+HAAA,CAAA,eAAY;sCAAC;;;;;;sCACd,sSAAC,+HAAA,CAAA,eAAY;sCACV,cAAc,GAAG,CAAC,CAAC,uBAClB,sSAAC,+HAAA,CAAA,cAAW;oCAEV,OAAO,OAAO,KAAK;oCACnB,UAAU;wCACR,eAAe,OAAO,KAAK;wCAC3B,QAAQ;oCACV;;sDAEA,sSAAC,2RAAA,CAAA,QAAK;4CACJ,WAAW,CAAA,GAAA,+HAAA,CAAA,KAAE,AAAD,EACV,gBACA,WAAW,OAAO,KAAK,GAAG,gBAAgB;;;;;;sDAG9C,sSAAC,6HAAA,CAAA,QAAK;4CAAC,WAAW,OAAO,KAAK;sDAC3B,OAAO,KAAK;;;;;;;mCAdV,OAAO,KAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;AAuBjC;IA1DgB;MAAA;AAmET,SAAS,sBAAsB,EAAE,MAAM,EAAE,cAAc,EAAE,WAAW,KAAK,EAA8B;;IAC5G,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,sQAAA,CAAA,WAAQ,AAAD,EAAW;IAE1C,6CAA6C;IAC7C,MAAM,gBAAgB;QACpB;YAAE,OAAO,4KAAA,CAAA,gBAAa,CAAC,IAAI;YAAY,OAAO;YAAiB,OAAO;QAA0C;QAChH;YAAE,OAAO,4KAAA,CAAA,gBAAa,CAAC,MAAM;YAAY,OAAO;YAAmB,OAAO;QAAsC;KACjH;IAED,MAAM,gBAAgB,cAAc,IAAI,CAAC,CAAC,SAAW,OAAO,KAAK,KAAK,WAAW,aAAa,CAAC,EAAE;IAEjG,qBACE,sSAAC,+HAAA,CAAA,UAAO;QAAC,MAAM;QAAM,cAAc;;0BACjC,sSAAC,+HAAA,CAAA,iBAAc;gBAAC,OAAO;0BACrB,cAAA,sSAAC,8HAAA,CAAA,SAAM;oBACL,SAAQ;oBACR,MAAK;oBACL,iBAAe;oBACf,UAAU;oBACV,WAAU;8BAEV,cAAA,sSAAC,6HAAA,CAAA,QAAK;wBAAC,WAAW,cAAc,KAAK;kCAClC,cAAc,KAAK;;;;;;;;;;;;;;;;0BAI1B,sSAAC,+HAAA,CAAA,iBAAc;gBAAC,WAAU;0BACxB,cAAA,sSAAC,+HAAA,CAAA,UAAO;;sCACN,sSAAC,+HAAA,CAAA,eAAY;4BAAC,aAAY;;;;;;sCAC1B,sSAAC,+HAAA,CAAA,eAAY;sCAAC;;;;;;sCACd,sSAAC,+HAAA,CAAA,eAAY;sCACV,cAAc,GAAG,CAAC,CAAC,uBAClB,sSAAC,+HAAA,CAAA,cAAW;oCAEV,OAAO,OAAO,KAAK;oCACnB,UAAU;wCACR,eAAe,OAAO,KAAK;wCAC3B,QAAQ;oCACV;;sDAEA,sSAAC,2RAAA,CAAA,QAAK;4CACJ,WAAW,CAAA,GAAA,+HAAA,CAAA,KAAE,AAAD,EACV,gBACA,WAAW,OAAO,KAAK,GAAG,gBAAgB;;;;;;sDAG9C,sSAAC,6HAAA,CAAA,QAAK;4CAAC,WAAW,OAAO,KAAK;sDAC3B,OAAO,KAAK;;;;;;;mCAdV,OAAO,KAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;AAuBjC;IAxDgB;MAAA;AAkET,SAAS,oBAAoB,EAClC,YAAY,EACZ,QAAQ,EACR,MAAM,EACN,cAAc,EACd,qBAAqB,EACC;IACtB,OAAO;QACL;YACE,aAAa;YACb,QAAQ,CAAC,EAAE,MAAM,EAAE;gBACjB,qBACE,sSAAC,8HAAA,CAAA,SAAM;oBACL,SAAQ;oBACR,MAAK;oBACL,SAAS,IAAM,OAAO,aAAa,CAAC,OAAO,WAAW,OAAO;oBAC7D,WAAU;oBACV,kBAAe;;sCAEf,sSAAC;4BAAK,WAAU;sCAAU;;;;;;sCAC1B,sSAAC,+SAAA,CAAA,cAAW;4BAAC,WAAU;;;;;;;;;;;;YAG7B;YACA,MAAM;gBACJ,QAAQ;YACV;YACA,MAAM,CAAC,EAAE,GAAG,EAAE;gBACZ,MAAM,cAAc,IAAI,QAAQ,CAAC,WAAW;gBAC5C,qBACE,sSAAC;oBAAI,WAAU;8BACZ,eAAe;;;;;;YAGtB;YACA,eAAe;QACjB;QACA,4EAA4E;QAE5E;YACE,IAAI;YACJ,QAAQ;gBACN,qBACE,sSAAC,8HAAA,CAAA,SAAM;oBACL,SAAQ;oBACR,MAAK;oBACL,WAAU;oBACV,kBAAe;8BAEf,cAAA,sSAAC;wBAAK,WAAU;kCAAU;;;;;;;;;;;YAGhC;YACA,MAAM;gBACJ,QAAQ;YACV;YACA,MAAM,CAAC,EAAE,GAAG,EAAE;gBACZ,MAAM,QAAQ,IAAI,QAAQ;gBAC1B,8BAA8B;gBAC9B,MAAM,gBAAgB,MAAM,YAAY,EAAE,OAAO,CAAC,KAAa;oBAC7D,OAAO,MAAM,CAAC,OAAO,QAAQ,IAAI,CAAC;gBACpC,GAAG,MAAM;gBACT,qBACE,sSAAC;oBAAI,WAAU;8BACZ,cAAc,cAAc;;;;;;YAGnC;YACA,eAAe;QACjB;QACA;YACE,aAAa;YACb,QAAQ,CAAC,EAAE,MAAM,EAAE;gBACjB,qBACE,sSAAC,8HAAA,CAAA,SAAM;oBACL,SAAQ;oBACR,MAAK;oBACL,SAAS,IAAM,OAAO,aAAa,CAAC,OAAO,WAAW,OAAO;oBAC7D,WAAU;oBACV,kBAAe;;sCAEf,sSAAC;4BAAK,WAAU;sCAAU;;;;;;sCAC1B,sSAAC,+SAAA,CAAA,cAAW;4BAAC,WAAU;;;;;;;;;;;;YAG7B;YACA,MAAM;gBACJ,QAAQ;YACV;YACA,MAAM,CAAC,EAAE,GAAG,EAAE;gBACZ,MAAM,cAAc,IAAI,QAAQ,CAAC;gBAEjC,qBACE,sSAAC;oBAAI,WAAU;8BACb,cAAA,sSAAC;;4BAAM,eAAe;4BAAa;;;;;;;;;;;;YAGzC;YACA,eAAe;QACjB;QACA;YACE,aAAa;YACb,QAAQ,CAAC,EAAE,MAAM,EAAE;gBACjB,qBACE,sSAAC,8HAAA,CAAA,SAAM;oBACL,SAAQ;oBACR,MAAK;oBACL,SAAS,IAAM,OAAO,aAAa,CAAC,OAAO,WAAW,OAAO;oBAC7D,WAAU;oBACV,kBAAe;;sCAEf,sSAAC;4BAAK,WAAU;sCAAU;;;;;;sCAC1B,sSAAC,+SAAA,CAAA,cAAW;4BAAC,WAAU;;;;;;;;;;;;YAG7B;YACA,MAAM;gBACJ,QAAQ;YACV;YACA,MAAM,CAAC,EAAE,GAAG,EAAE;gBACZ,MAAM,gBAAgB,IAAI,QAAQ,CAAC;gBACnC,MAAM,QAAQ,IAAI,QAAQ;gBAE1B,sEAAsE;gBACtE,OAAO,sCACL,sSAAC;oBACC,QAAQ;oBACR,gBAAgB,CAAC,YAAc,sBAAsB,OAAO;;;;;yCAG9D,sSAAC,6HAAA,CAAA,QAAK;oBAAC,WAAW,kBAAkB,4KAAA,CAAA,gBAAa,CAAC,IAAI,GAAG,4CAA4C;8BAClG,kBAAkB,4KAAA,CAAA,gBAAa,CAAC,IAAI,GAAG,kBAAkB;;;;;;YAGhE;YACA,eAAe;YACf,MAAM;QACR;QACA,0FAA0F;QAC1F;YACE,aAAa;YACb,QAAQ,CAAC,EAAE,MAAM,EAAE;gBACjB,qBACE,sSAAC,8HAAA,CAAA,SAAM;oBACL,SAAQ;oBACR,MAAK;oBACL,SAAS,IAAM,OAAO,aAAa,CAAC,OAAO,WAAW,OAAO;oBAC7D,WAAU;oBACV,kBAAe;;sCAEf,sSAAC;4BAAK,WAAU;sCAAU;;;;;;sCAC1B,sSAAC,+SAAA,CAAA,cAAW;4BAAC,WAAU;;;;;;;;;;;;YAG7B;YACA,MAAM;gBACJ,QAAQ;YACV;YACA,MAAM,CAAC,EAAE,GAAG,EAAE;gBACZ,MAAM,SAAS,IAAI,QAAQ,CAAC;gBAC5B,MAAM,QAAQ,IAAI,QAAQ;gBAE1B,OAAO,+BACL,sSAAC;oBACC,QAAQ;oBACR,gBAAgB,CAAC,YAAc,eAAe,OAAO;;;;;yCAGvD,sSAAC,6HAAA,CAAA,QAAK;oBAAC,WAAW,eAAe;8BAC9B,cAAc;;;;;;YAGrB;YACA,eAAe;YACf,MAAM;QACR;QACA;YACE,aAAa;YACb,QAAQ,CAAC,EAAE,MAAM,EAAE;gBACjB,qBACE,sSAAC,8HAAA,CAAA,SAAM;oBACL,SAAQ;oBACR,MAAK;oBACL,SAAS,IAAM,OAAO,aAAa,CAAC,OAAO,WAAW,OAAO;oBAC7D,WAAU;oBACV,kBAAe;;sCAEf,sSAAC;4BAAK,WAAU;sCAAU;;;;;;sCAC1B,sSAAC,+SAAA,CAAA,cAAW;4BAAC,WAAU;;;;;;;;;;;;YAG7B;YACA,MAAM;gBACJ,QAAQ;YACV;YACA,MAAM,CAAC,EAAE,GAAG,EAAE;gBACZ,MAAM,QAAQ,IAAI,QAAQ;gBAE1B,yEAAyE;gBACzE,MAAM,aAAa,MAAM,OAAO,IAAI,OAAO,MAAM,OAAO,KAAK;gBAC7D,MAAM,eAAe,MAAM,SAAS,IAAI,OAAO,MAAM,SAAS,KAAK;gBAEnE,qDAAqD;gBACrD,IAAI,YAAY;oBACd,qBACE,sSAAC,yJAAA,CAAA,gBAAa;wBAAC,MAAM,MAAM,OAAO;wBAAE,YAAY;wBAAM,MAAK;kCACzD,cAAA,sSAAC;4BAAI,WAAU;;8CACb,sSAAC;oCAAI,WAAU;8CAAgC,MAAM,OAAO,EAAE,YAAY,MAAM,OAAO,EAAE,YAAY;;;;;;8CACrG,sSAAC;oCAAI,WAAU;8CAA0C,MAAM,OAAO,EAAE,SAAS;;;;;;;;;;;;;;;;;gBAIzF;gBAEA,qCAAqC;gBACrC,IAAI,cAAc;oBAChB,qBACE,sSAAC,yJAAA,CAAA,gBAAa;wBAAC,QAAQ,MAAM,SAAS,IAAI;wBAAI,YAAY;wBAAM,MAAK;kCACnE,cAAA,sSAAC;4BAAI,WAAU;;8CACb,sSAAC,yRAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;8CAChB,sSAAC;oCAAK,WAAU;;wCACb,MAAM,SAAS,EAAE,UAAU,GAAG;wCAAG;;;;;;;;;;;;;;;;;;gBAK5C;gBAEA,uCAAuC;gBACvC,qBACE,sSAAC;oBAAK,WAAU;8BAAgC;;;;;;YAEpD;YACA,eAAe;QACjB;QACA;YACE,aAAa;YACb,QAAQ,CAAC,EAAE,MAAM,EAAE;gBACjB,qBACE,sSAAC,8HAAA,CAAA,SAAM;oBACL,SAAQ;oBACR,MAAK;oBACL,SAAS,IAAM,OAAO,aAAa,CAAC,OAAO,WAAW,OAAO;oBAC7D,WAAU;oBACV,kBAAe;;sCAEf,sSAAC;4BAAK,WAAU;sCAAU;;;;;;sCAC1B,sSAAC,+SAAA,CAAA,cAAW;4BAAC,WAAU;;;;;;;;;;;;YAG7B;YACA,MAAM;gBACJ,QAAQ;YACV;YACA,MAAM,CAAC,EAAE,GAAG,EAAE;gBACZ,MAAM,OAAO,IAAI,QAAQ;gBACzB,qBACE,sSAAC,+IAAA,CAAA,kBAAe;oBACd,MAAM,KAAK,SAAS;oBACpB,WAAU;oBACV,eAAc;;;;;;YAGpB;YACA,eAAe;YACf,MAAM;QACR;QACA;YACE,aAAa;YACb,QAAQ,CAAC,EAAE,MAAM,EAAE;gBACjB,qBACE,sSAAC,8HAAA,CAAA,SAAM;oBACL,SAAQ;oBACR,MAAK;oBACL,SAAS,IAAM,OAAO,aAAa,CAAC,OAAO,WAAW,OAAO;oBAC7D,WAAU;oBACV,kBAAe;;sCAEf,sSAAC;4BAAK,WAAU;sCAAU;;;;;;sCAC1B,sSAAC,+SAAA,CAAA,cAAW;4BAAC,WAAU;;;;;;;;;;;;YAG7B;YACA,MAAM;gBACJ,QAAQ;YACV;YACA,MAAM,CAAC,EAAE,GAAG,EAAE;gBACZ,MAAM,QAAQ,IAAI,QAAQ;gBAE1B,yEAAyE;gBACzE,MAAM,aAAa,MAAM,OAAO,IAAI,OAAO,MAAM,OAAO,KAAK;gBAC7D,MAAM,eAAe,MAAM,SAAS,IAAI,OAAO,MAAM,SAAS,KAAK;gBAEnE,qDAAqD;gBACrD,IAAI,YAAY;oBACd,qBACE,sSAAC,yJAAA,CAAA,gBAAa;wBAAC,MAAM,MAAM,OAAO;wBAAE,YAAY;wBAAM,MAAK;kCACzD,cAAA,sSAAC;4BAAI,WAAU;;8CACb,sSAAC;oCAAI,WAAU;8CAAgC,MAAM,OAAO,EAAE,YAAY,MAAM,OAAO,EAAE,YAAY;;;;;;8CACrG,sSAAC;oCAAI,WAAU;8CAA0C,MAAM,OAAO,EAAE,SAAS;;;;;;;;;;;;;;;;;gBAIzF;gBAEA,qCAAqC;gBACrC,IAAI,cAAc;oBAChB,qBACE,sSAAC,yJAAA,CAAA,gBAAa;wBAAC,QAAQ,MAAM,SAAS,IAAI;wBAAI,YAAY;wBAAM,MAAK;kCACnE,cAAA,sSAAC;4BAAI,WAAU;;8CACb,sSAAC,yRAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;8CAChB,sSAAC;oCAAK,WAAU;;wCACb,MAAM,SAAS,EAAE,UAAU,GAAG;wCAAG;;;;;;;;;;;;;;;;;;gBAK5C;gBAEA,uCAAuC;gBACvC,qBACE,sSAAC;oBAAK,WAAU;8BAAgC;;;;;;YAEpD;YACA,eAAe;YACf,cAAc;QAChB;QACA;YACE,IAAI;YACJ,MAAM;YACN,cAAc;YACd,QAAQ,kBAAM,sSAAC;oBAAI,kBAAe;;;;;;YAClC,MAAM,CAAC,EAAE,GAAG,EAAE,iBACZ,sSAAC;oBACC,KAAK;oBACL,cAAc;oBACd,UAAU;oBACV,QAAQ;oBACR,gBAAgB;;;;;;YAGpB,MAAM;gBACJ,UAAU;gBACV,UAAU;gBACV,QAAQ;YACV;QACF;KACD;AACH", "debugId": null}}, {"offset": {"line": 2670, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/bloomgoo/gold-exchange-platform/frontend/components/ui/checkbox.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as CheckboxPrimitive from \"@radix-ui/react-checkbox\"\r\nimport { CheckIcon } from \"lucide-react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction Checkbox({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof CheckboxPrimitive.Root>) {\r\n  return (\r\n    <CheckboxPrimitive.Root\r\n      data-slot=\"checkbox\"\r\n      className={cn(\r\n        \"peer border-input dark:bg-input/30 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground dark:data-[state=checked]:bg-primary data-[state=checked]:border-primary focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive size-4 shrink-0 rounded-[4px] border shadow-xs transition-shadow outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50\",\r\n        className\r\n      )}\r\n      {...props}\r\n    >\r\n      <CheckboxPrimitive.Indicator\r\n        data-slot=\"checkbox-indicator\"\r\n        className=\"flex items-center justify-center text-current transition-none\"\r\n      >\r\n        <CheckIcon className=\"size-3.5\" />\r\n      </CheckboxPrimitive.Indicator>\r\n    </CheckboxPrimitive.Root>\r\n  )\r\n}\r\n\r\nexport { Checkbox }\r\n"], "names": [], "mappings": ";;;;AAGA;AACA;AAEA;AAAA;AANA;;;;;AAQA,SAAS,SAAS,EAChB,SAAS,EACT,GAAG,OACiD;IACpD,qBACE,sSAAC,iRAAA,CAAA,OAAsB;QACrB,aAAU;QACV,WAAW,CAAA,GAAA,+HAAA,CAAA,KAAE,AAAD,EACV,+eACA;QAED,GAAG,KAAK;kBAET,cAAA,sSAAC,iRAAA,CAAA,YAA2B;YAC1B,aAAU;YACV,WAAU;sBAEV,cAAA,sSAAC,+RAAA,CAAA,YAAS;gBAAC,WAAU;;;;;;;;;;;;;;;;AAI7B;KArBS", "debugId": null}}, {"offset": {"line": 2722, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/bloomgoo/gold-exchange-platform/frontend/components/common/selector/column-visibility-selector.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { But<PERSON> } from '@/components/ui/button';\r\nimport { Checkbox } from '@/components/ui/checkbox';\r\nimport {\r\n  DropdownMenu,\r\n  DropdownMenuContent,\r\n  DropdownMenuTrigger,\r\n} from '@/components/ui/dropdown-menu';\r\nimport { Eye, Search, Settings2 } from 'lucide-react';\r\nimport { Table } from '@tanstack/react-table';\r\nimport { Input } from '@/components/ui/input';\r\nimport { useState, useMemo } from 'react';\r\n\r\n// Định nghĩa kiểu dữ liệu cho thuộc tính meta của cột\r\ndeclare module '@tanstack/react-table' {\r\n  interface ColumnMeta<TData, TValue> {\r\n    isSticky?: boolean;\r\n    position?: 'left' | 'right';\r\n    header?: string;\r\n  }\r\n}\r\n\r\ninterface ColumnVisibilitySelectorProps<TData> {\r\n  table: Table<TData>;\r\n  className?: string;\r\n  buttonClassName?: string;\r\n  contentClassName?: string;\r\n  label?: string;\r\n}\r\n\r\nexport function ColumnVisibilitySelector<TData>({\r\n  table,\r\n  className,\r\n  buttonClassName,\r\n  contentClassName = 'w-[250px] max-h-[var(--radix-dropdown-menu-content-available-height)] overflow-y-auto',\r\n  label = 'Tất cả',\r\n}: ColumnVisibilitySelectorProps<TData>) {\r\n  // State để lưu từ khóa tìm kiếm\r\n  const [searchQuery, setSearchQuery] = useState('');\r\n  // Lấy tất cả các cột có thể ẩn/hiện\r\n  const allColumns = useMemo(() => {\r\n    return table.getAllColumns().filter(column => column.getCanHide());\r\n  }, [table]);\r\n\r\n  // Lấy các cột đang hiển thị\r\n  const visibleColumns = useMemo(() => {\r\n    return allColumns.filter(column => column.getIsVisible());\r\n  }, [allColumns, table.getState().columnVisibility]);\r\n\r\n  // Lọc các cột dựa trên từ khóa tìm kiếm\r\n  const filteredColumns = useMemo(() => {\r\n    if (!searchQuery.trim()) {\r\n      return allColumns;\r\n    }\r\n\r\n    const query = searchQuery.toLowerCase().trim();\r\n    return allColumns.filter(column => {\r\n      // Lấy tên cột thân thiện từ header\r\n      let header = column.id;\r\n\r\n      // Ưu tiên sử dụng meta.header\r\n      if (column.columnDef.meta?.header) {\r\n        header = column.columnDef.meta.header;\r\n      }\r\n      // Nếu header là string, sử dụng trực tiếp\r\n      else if (typeof column.columnDef.header === 'string') {\r\n        header = column.columnDef.header;\r\n      }\r\n      // Nếu header là function, tìm kiếm phần tử span chứa tên cột\r\n      else if (typeof column.columnDef.header === 'function') {\r\n        const headerElement = document.querySelector(`[data-column-id=\"${column.id}\"] span`);\r\n        if (headerElement && headerElement.textContent) {\r\n          header = headerElement.textContent;\r\n        }\r\n      }\r\n\r\n      return header.toString().toLowerCase().includes(query);\r\n    });\r\n  }, [allColumns, searchQuery]);\r\n\r\n  // Hàm để toggle tất cả các cột\r\n  const toggleAllColumns = (checked: boolean) => {\r\n    table.toggleAllColumnsVisible(checked);\r\n  };\r\n\r\n  return (\r\n    <div className={className}>\r\n      <DropdownMenu>\r\n        <DropdownMenuTrigger asChild>\r\n          <Button\r\n            variant=\"ghost\"\r\n            size=\"icon\"\r\n            className={`relative ${buttonClassName || ''}`}\r\n          >\r\n            <Settings2 className=\"h-4 w-4\" />\r\n            {visibleColumns.length < allColumns.length && (\r\n              <span className=\"absolute -top-1 -right-1 h-2 w-2 rounded-full bg-blue-600\" />\r\n            )}\r\n          </Button>\r\n        </DropdownMenuTrigger>\r\n        <DropdownMenuContent\r\n          align=\"end\"\r\n          className={contentClassName}\r\n        >\r\n          <div className=\"sticky top-0 bg-background z-10 border-b\">\r\n            <div className=\"p-2\">\r\n              <div className=\"flex items-center space-x-2 px-1 py-1\">\r\n                <Checkbox\r\n                  checked={visibleColumns.length === allColumns.length}\r\n                  onCheckedChange={(checked) => toggleAllColumns(!!checked)}\r\n                  id=\"all-columns\"\r\n                />\r\n                <label\r\n                  htmlFor=\"all-columns\"\r\n                  className=\"text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70\"\r\n                >\r\n                  {label}\r\n                </label>\r\n              </div>\r\n\r\n              {/* Thêm ô tìm kiếm */}\r\n              <div className=\"relative mt-2\">\r\n                <Search className=\"absolute left-2 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground\" />\r\n                <Input\r\n                  placeholder=\"Tìm kiếm cột...\"\r\n                  value={searchQuery}\r\n                  onChange={(e) => setSearchQuery(e.target.value)}\r\n                  className=\"pl-8 h-8 text-sm\"\r\n                />\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <div className=\"p-2 pt-0\">\r\n            {filteredColumns.length === 0 ? (\r\n              <div className=\"py-6 text-center text-sm text-muted-foreground\">\r\n                Không tìm thấy cột nào\r\n              </div>\r\n            ) : (\r\n              filteredColumns.map(column => {\r\n                const isVisible = column.getIsVisible();\r\n                return (\r\n                  <div\r\n                    key={column.id}\r\n                    className=\"flex items-center space-x-2 px-1 py-1.5 rounded hover:bg-accent\"\r\n                  >\r\n                    <Checkbox\r\n                      checked={isVisible}\r\n                      onCheckedChange={(checked) => column.toggleVisibility(!!checked)}\r\n                      id={column.id}\r\n                    />\r\n                    <label\r\n                      htmlFor={column.id}\r\n                      className=\"flex-1 text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70\"\r\n                    >\r\n                      {(() => {\r\n                        // Ưu tiên sử dụng meta.header\r\n                        if (column.columnDef.meta?.header) {\r\n                          return column.columnDef.meta.header;\r\n                        }\r\n\r\n                        // Nếu header là string, sử dụng trực tiếp\r\n                        if (typeof column.columnDef.header === 'string') {\r\n                          return column.columnDef.header;\r\n                        }\r\n\r\n                        // Tìm kiếm phần tử span trong header\r\n                        const headerElement = document.querySelector(`[data-column-id=\"${column.id}\"] span`);\r\n                        if (headerElement && headerElement.textContent) {\r\n                          return headerElement.textContent;\r\n                        }\r\n\r\n                        // Fallback: Sử dụng ID\r\n                        return column.id;\r\n                      })()}\r\n                    </label>\r\n                  </div>\r\n                );\r\n              })\r\n            )}\r\n          </div>\r\n        </DropdownMenuContent>\r\n      </DropdownMenu>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAKA;AAAA;AAEA;AACA;;;AAZA;;;;;;;AA+BO,SAAS,yBAAgC,EAC9C,KAAK,EACL,SAAS,EACT,eAAe,EACf,mBAAmB,uFAAuF,EAC1G,QAAQ,QAAQ,EACqB;;IACrC,gCAAgC;IAChC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,sQAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,oCAAoC;IACpC,MAAM,aAAa,CAAA,GAAA,sQAAA,CAAA,UAAO,AAAD;wDAAE;YACzB,OAAO,MAAM,aAAa,GAAG,MAAM;gEAAC,CAAA,SAAU,OAAO,UAAU;;QACjE;uDAAG;QAAC;KAAM;IAEV,4BAA4B;IAC5B,MAAM,iBAAiB,CAAA,GAAA,sQAAA,CAAA,UAAO,AAAD;4DAAE;YAC7B,OAAO,WAAW,MAAM;oEAAC,CAAA,SAAU,OAAO,YAAY;;QACxD;2DAAG;QAAC;QAAY,MAAM,QAAQ,GAAG,gBAAgB;KAAC;IAElD,wCAAwC;IACxC,MAAM,kBAAkB,CAAA,GAAA,sQAAA,CAAA,UAAO,AAAD;6DAAE;YAC9B,IAAI,CAAC,YAAY,IAAI,IAAI;gBACvB,OAAO;YACT;YAEA,MAAM,QAAQ,YAAY,WAAW,GAAG,IAAI;YAC5C,OAAO,WAAW,MAAM;qEAAC,CAAA;oBACvB,mCAAmC;oBACnC,IAAI,SAAS,OAAO,EAAE;oBAEtB,8BAA8B;oBAC9B,IAAI,OAAO,SAAS,CAAC,IAAI,EAAE,QAAQ;wBACjC,SAAS,OAAO,SAAS,CAAC,IAAI,CAAC,MAAM;oBACvC,OAEK,IAAI,OAAO,OAAO,SAAS,CAAC,MAAM,KAAK,UAAU;wBACpD,SAAS,OAAO,SAAS,CAAC,MAAM;oBAClC,OAEK,IAAI,OAAO,OAAO,SAAS,CAAC,MAAM,KAAK,YAAY;wBACtD,MAAM,gBAAgB,SAAS,aAAa,CAAC,CAAC,iBAAiB,EAAE,OAAO,EAAE,CAAC,OAAO,CAAC;wBACnF,IAAI,iBAAiB,cAAc,WAAW,EAAE;4BAC9C,SAAS,cAAc,WAAW;wBACpC;oBACF;oBAEA,OAAO,OAAO,QAAQ,GAAG,WAAW,GAAG,QAAQ,CAAC;gBAClD;;QACF;4DAAG;QAAC;QAAY;KAAY;IAE5B,+BAA+B;IAC/B,MAAM,mBAAmB,CAAC;QACxB,MAAM,uBAAuB,CAAC;IAChC;IAEA,qBACE,sSAAC;QAAI,WAAW;kBACd,cAAA,sSAAC,wIAAA,CAAA,eAAY;;8BACX,sSAAC,wIAAA,CAAA,sBAAmB;oBAAC,OAAO;8BAC1B,cAAA,sSAAC,8HAAA,CAAA,SAAM;wBACL,SAAQ;wBACR,MAAK;wBACL,WAAW,CAAC,SAAS,EAAE,mBAAmB,IAAI;;0CAE9C,sSAAC,uSAAA,CAAA,YAAS;gCAAC,WAAU;;;;;;4BACpB,eAAe,MAAM,GAAG,WAAW,MAAM,kBACxC,sSAAC;gCAAK,WAAU;;;;;;;;;;;;;;;;;8BAItB,sSAAC,wIAAA,CAAA,sBAAmB;oBAClB,OAAM;oBACN,WAAW;;sCAEX,sSAAC;4BAAI,WAAU;sCACb,cAAA,sSAAC;gCAAI,WAAU;;kDACb,sSAAC;wCAAI,WAAU;;0DACb,sSAAC,gIAAA,CAAA,WAAQ;gDACP,SAAS,eAAe,MAAM,KAAK,WAAW,MAAM;gDACpD,iBAAiB,CAAC,UAAY,iBAAiB,CAAC,CAAC;gDACjD,IAAG;;;;;;0DAEL,sSAAC;gDACC,SAAQ;gDACR,WAAU;0DAET;;;;;;;;;;;;kDAKL,sSAAC;wCAAI,WAAU;;0DACb,sSAAC,6RAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;0DAClB,sSAAC,6HAAA,CAAA,QAAK;gDACJ,aAAY;gDACZ,OAAO;gDACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;gDAC9C,WAAU;;;;;;;;;;;;;;;;;;;;;;;sCAMlB,sSAAC;4BAAI,WAAU;sCACZ,gBAAgB,MAAM,KAAK,kBAC1B,sSAAC;gCAAI,WAAU;0CAAiD;;;;;uCAIhE,gBAAgB,GAAG,CAAC,CAAA;gCAClB,MAAM,YAAY,OAAO,YAAY;gCACrC,qBACE,sSAAC;oCAEC,WAAU;;sDAEV,sSAAC,gIAAA,CAAA,WAAQ;4CACP,SAAS;4CACT,iBAAiB,CAAC,UAAY,OAAO,gBAAgB,CAAC,CAAC,CAAC;4CACxD,IAAI,OAAO,EAAE;;;;;;sDAEf,sSAAC;4CACC,SAAS,OAAO,EAAE;4CAClB,WAAU;sDAET,CAAC;gDACA,8BAA8B;gDAC9B,IAAI,OAAO,SAAS,CAAC,IAAI,EAAE,QAAQ;oDACjC,OAAO,OAAO,SAAS,CAAC,IAAI,CAAC,MAAM;gDACrC;gDAEA,0CAA0C;gDAC1C,IAAI,OAAO,OAAO,SAAS,CAAC,MAAM,KAAK,UAAU;oDAC/C,OAAO,OAAO,SAAS,CAAC,MAAM;gDAChC;gDAEA,qCAAqC;gDACrC,MAAM,gBAAgB,SAAS,aAAa,CAAC,CAAC,iBAAiB,EAAE,OAAO,EAAE,CAAC,OAAO,CAAC;gDACnF,IAAI,iBAAiB,cAAc,WAAW,EAAE;oDAC9C,OAAO,cAAc,WAAW;gDAClC;gDAEA,uBAAuB;gDACvB,OAAO,OAAO,EAAE;4CAClB,CAAC;;;;;;;mCA/BE,OAAO,EAAE;;;;;4BAmCpB;;;;;;;;;;;;;;;;;;;;;;;AAOd;GA3JgB;KAAA", "debugId": null}}, {"offset": {"line": 3001, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/bloomgoo/gold-exchange-platform/frontend/components/common/selector/sort-selector.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { But<PERSON> } from '@/components/ui/button';\r\nimport {\r\n  Popover,\r\n  PopoverContent,\r\n  PopoverTrigger,\r\n} from '@/components/ui/popover';\r\nimport {\r\n  ArrowDownWideNarrow,\r\n  ArrowUpDown,\r\n  ArrowUpNarrowWide,\r\n  X,\r\n  Plus,\r\n  GripVertical,\r\n  Search\r\n} from 'lucide-react';\r\nimport { Input } from '@/components/ui/input';\r\nimport { SortingState, Table } from '@tanstack/react-table';\r\nimport { cn } from '@/lib/utils';\r\nimport { Badge } from '@/components/ui/badge';\r\n\r\nimport { useState, useMemo, useEffect } from 'react';\r\n\r\n// Định nghĩa kiểu dữ liệu cho thuộc tính meta của cột\r\ndeclare module '@tanstack/react-table' {\r\n  interface ColumnMeta<TData, TValue> {\r\n    isSticky?: boolean;\r\n    position?: 'left' | 'right';\r\n    header?: string;\r\n  }\r\n}\r\nimport {\r\n  DndContext,\r\n  closestCenter,\r\n  KeyboardSensor,\r\n  PointerSensor,\r\n  useSensor,\r\n  useSensors,\r\n  DragEndEvent\r\n} from '@dnd-kit/core';\r\nimport {\r\n  arrayMove,\r\n  SortableContext,\r\n  sortableKeyboardCoordinates,\r\n  useSortable,\r\n  verticalListSortingStrategy,\r\n} from '@dnd-kit/sortable';\r\nimport { CSS } from '@dnd-kit/utilities';\r\n\r\ninterface SortSelectorProps<TData> {\r\n  table: Table<TData>;\r\n  className?: string;\r\n  buttonClassName?: string;\r\n  contentClassName?: string;\r\n  maxSortFields?: number;\r\n}\r\n\r\n// Component cho mỗi mục sort có thể kéo thả\r\ninterface SortableItemProps {\r\n  id: string;\r\n  columnName: string;\r\n  direction: 'asc' | 'desc';\r\n  onDirectionChange: (direction: 'asc' | 'desc') => void;\r\n  onRemove: () => void;\r\n}\r\n\r\nfunction SortableItem({\r\n  id,\r\n  columnName,\r\n  direction,\r\n  onDirectionChange,\r\n  onRemove\r\n}: SortableItemProps) {\r\n  const {\r\n    attributes,\r\n    listeners,\r\n    setNodeRef,\r\n    transform,\r\n    transition,\r\n  } = useSortable({ id });\r\n\r\n  const style = {\r\n    transform: CSS.Transform.toString(transform),\r\n    transition,\r\n  };\r\n\r\n  return (\r\n    <div\r\n      ref={setNodeRef}\r\n      style={style}\r\n      className=\"flex items-center justify-between p-2 mb-1 bg-muted/50 rounded-md\"\r\n    >\r\n      <div className=\"flex items-center gap-2\">\r\n        <button\r\n          {...attributes}\r\n          {...listeners}\r\n          className=\"cursor-grab touch-none p-1 rounded hover:bg-muted\"\r\n        >\r\n          <GripVertical className=\"h-4 w-4 text-muted-foreground\" />\r\n        </button>\r\n        <span className=\"text-sm\">{columnName}</span>\r\n      </div>\r\n      <div className=\"flex items-center gap-1\">\r\n        <Button\r\n          variant=\"ghost\"\r\n          size=\"icon\"\r\n          className=\"h-7 w-7\"\r\n          onClick={() => onDirectionChange(direction === 'asc' ? 'desc' : 'asc')}\r\n        >\r\n          {direction === 'asc' ? (\r\n            <ArrowDownWideNarrow className=\"h-4 w-4\" />\r\n          ) : (\r\n            <ArrowUpNarrowWide className=\"h-4 w-4\" />\r\n          )}\r\n        </Button>\r\n        <Button\r\n          variant=\"ghost\"\r\n          size=\"icon\"\r\n          className=\"h-7 w-7 text-destructive\"\r\n          onClick={onRemove}\r\n        >\r\n          <X className=\"h-4 w-4\" />\r\n        </Button>\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n\r\nexport function SortSelector<TData>({\r\n  table,\r\n  className,\r\n  buttonClassName,\r\n  contentClassName = 'w-[300px]',\r\n  maxSortFields = 3,\r\n}: SortSelectorProps<TData>) {\r\n  const [open, setOpen] = useState(false);\r\n  const [searchQuery, setSearchQuery] = useState('');\r\n  const [tempSorting, setTempSorting] = useState<SortingState>([]);\r\n\r\n  // Lấy tất cả các cột có thể sắp xếp\r\n  const sortableColumns = useMemo(() => {\r\n    return table.getAllColumns().filter(column =>\r\n      column.getCanSort() && column.getIsVisible()\r\n    );\r\n  }, [table]);\r\n\r\n  // Lọc các cột dựa trên từ khóa tìm kiếm\r\n  const filteredColumns = useMemo(() => {\r\n    if (!searchQuery.trim()) {\r\n      return sortableColumns;\r\n    }\r\n\r\n    const query = searchQuery.toLowerCase().trim();\r\n    return sortableColumns.filter(column => {\r\n      // Lấy tên cột thân thiện từ header\r\n      let header = column.id;\r\n\r\n      // Ưu tiên sử dụng meta.header\r\n      if (column.columnDef.meta?.header) {\r\n        header = column.columnDef.meta.header;\r\n      }\r\n      // Nếu header là string, sử dụng trực tiếp\r\n      else if (typeof column.columnDef.header === 'string') {\r\n        header = column.columnDef.header;\r\n      }\r\n      // Nếu header là function, tìm kiếm phần tử span chứa tên cột\r\n      else if (typeof column.columnDef.header === 'function') {\r\n        const headerElement = document.querySelector(`[data-column-id=\"${column.id}\"] span`);\r\n        if (headerElement && headerElement.textContent) {\r\n          header = headerElement.textContent;\r\n        }\r\n      }\r\n\r\n      return header.toString().toLowerCase().includes(query);\r\n    });\r\n  }, [sortableColumns, searchQuery]);\r\n\r\n  // Khi mở dropdown, sao chép trạng thái sắp xếp hiện tại vào tempSorting\r\n  useEffect(() => {\r\n    if (open) {\r\n      setTempSorting(table.getState().sorting);\r\n    }\r\n  }, [open, table]);\r\n\r\n  // Lấy thông tin các cột đang được sắp xếp (dựa trên tempSorting)\r\n  const sortedColumns = useMemo(() => {\r\n    return tempSorting.map(sort => {\r\n      const column = table.getColumn(sort.id);\r\n\r\n      // Lấy tên cột thân thiện từ header\r\n      let columnName = sort.id;\r\n\r\n      if (column) {\r\n        // Ưu tiên sử dụng meta.header\r\n        if (column.columnDef.meta?.header) {\r\n          columnName = column.columnDef.meta.header;\r\n        }\r\n        // Nếu header là string, sử dụng trực tiếp\r\n        else if (typeof column.columnDef.header === 'string') {\r\n          columnName = column.columnDef.header;\r\n        }\r\n        // Nếu header là function, tìm kiếm phần tử span chứa tên cột\r\n        else if (typeof column.columnDef.header === 'function') {\r\n          const headerElement = document.querySelector(`[data-column-id=\"${sort.id}\"] span`);\r\n          if (headerElement && headerElement.textContent) {\r\n            columnName = headerElement.textContent;\r\n          }\r\n        }\r\n      }\r\n\r\n      return {\r\n        id: sort.id,\r\n        name: columnName,\r\n        direction: (sort.desc ? 'desc' : 'asc') as 'desc' | 'asc'\r\n      };\r\n    });\r\n  }, [tempSorting, table]);\r\n\r\n  // Sensors cho DnD\r\n  const sensors = useSensors(\r\n    useSensor(PointerSensor),\r\n    useSensor(KeyboardSensor, {\r\n      coordinateGetter: sortableKeyboardCoordinates,\r\n    })\r\n  );\r\n\r\n  // Xử lý khi kết thúc kéo thả\r\n  const handleDragEnd = (event: DragEndEvent) => {\r\n    const { active, over } = event;\r\n\r\n    if (over && active.id !== over.id) {\r\n      const oldIndex = sortedColumns.findIndex(item => item.id === active.id);\r\n      const newIndex = sortedColumns.findIndex(item => item.id === over.id);\r\n\r\n      const newSortedColumns = arrayMove(sortedColumns, oldIndex, newIndex);\r\n\r\n      // Cập nhật tempSorting\r\n      const newSorting = newSortedColumns.map(col => ({\r\n        id: col.id,\r\n        desc: col.direction === 'desc'\r\n      }));\r\n\r\n      setTempSorting(newSorting);\r\n    }\r\n  };\r\n\r\n  // Xử lý thêm tiêu chí sắp xếp mới\r\n  const handleAddSort = (columnId: string) => {\r\n    // Kiểm tra xem cột đã được sắp xếp chưa\r\n    const existingSort = tempSorting.find(sort => sort.id === columnId);\r\n\r\n    if (existingSort) {\r\n      // Nếu đã có, thay đổi hướng sắp xếp\r\n      const newSorting = tempSorting.map(sort => {\r\n        if (sort.id === columnId) {\r\n          return { ...sort, desc: !sort.desc };\r\n        }\r\n        return sort;\r\n      });\r\n\r\n      setTempSorting(newSorting);\r\n    } else if (tempSorting.length < maxSortFields) {\r\n      // Nếu chưa có và chưa đạt giới hạn, thêm mới\r\n      const newSorting = [\r\n        ...tempSorting,\r\n        { id: columnId, desc: false }\r\n      ];\r\n\r\n      setTempSorting(newSorting);\r\n    }\r\n\r\n    // Xóa từ khóa tìm kiếm sau khi chọn\r\n    setSearchQuery('');\r\n  };\r\n\r\n  // Xử lý thay đổi hướng sắp xếp\r\n  const handleDirectionChange = (columnId: string, direction: 'asc' | 'desc') => {\r\n    const newSorting = tempSorting.map(sort => {\r\n      if (sort.id === columnId) {\r\n        return { ...sort, desc: direction === 'desc' };\r\n      }\r\n      return sort;\r\n    });\r\n\r\n    setTempSorting(newSorting);\r\n  };\r\n\r\n  // Xử lý xóa tiêu chí sắp xếp\r\n  const handleRemoveSort = (columnId: string) => {\r\n    const newSorting = tempSorting.filter(sort => sort.id !== columnId);\r\n    setTempSorting(newSorting);\r\n  };\r\n\r\n  // Xử lý xóa tất cả tiêu chí sắp xếp\r\n  const handleClearAll = () => {\r\n    setTempSorting([]);\r\n  };\r\n\r\n  // Xử lý áp dụng các thay đổi\r\n  const handleApply = () => {\r\n    table.setSorting(tempSorting);\r\n    setOpen(false);\r\n  };\r\n\r\n  // Xử lý hủy thay đổi\r\n  const handleCancel = () => {\r\n    setTempSorting(table.getState().sorting);\r\n    setOpen(false);\r\n  };\r\n\r\n  // Lấy icon sắp xếp cho button\r\n  const getSortIcon = () => {\r\n    const currentSorting = table.getState().sorting;\r\n\r\n    if (currentSorting.length === 0) {\r\n      return <ArrowUpDown className=\"h-4 w-4\" />;\r\n    }\r\n\r\n    if (currentSorting.length === 1) {\r\n      return currentSorting[0].desc ?\r\n        <ArrowUpNarrowWide className=\"h-4 w-4\" /> :\r\n        <ArrowDownWideNarrow className=\"h-4 w-4\" />;\r\n    }\r\n\r\n    // Nếu có nhiều tiêu chí sắp xếp, hiển thị số lượng\r\n    return (\r\n      <div className=\"relative\">\r\n        <ArrowUpDown className=\"h-4 w-4\" />\r\n        <Badge\r\n          className=\"absolute -top-2 -right-2 h-4 w-4 flex items-center justify-center p-0 text-[10px]\"\r\n          variant=\"default\"\r\n        >\r\n          {currentSorting.length}\r\n        </Badge>\r\n      </div>\r\n    );\r\n  };\r\n\r\n  return (\r\n    <div className={className}>\r\n      <Popover open={open} onOpenChange={setOpen}>\r\n        <PopoverTrigger asChild>\r\n          <Button\r\n            variant=\"ghost\"\r\n            size=\"icon\"\r\n            className={cn(\"relative\", buttonClassName, table.getState().sorting.length > 0 && \"bg-accent\")}\r\n          >\r\n            {getSortIcon()}\r\n          </Button>\r\n        </PopoverTrigger>\r\n        <PopoverContent align=\"end\" className={contentClassName} onEscapeKeyDown={handleCancel} onInteractOutside={(e) => e.preventDefault()}>\r\n          <div className=\"p-0\">\r\n            <h4 className=\"mb-2 text-sm font-medium\">Sắp xếp theo</h4>\r\n\r\n            {/* Danh sách các tiêu chí sắp xếp hiện tại */}\r\n            {sortedColumns.length > 0 ? (\r\n              <div className=\"mb-2\">\r\n                <DndContext\r\n                  sensors={sensors}\r\n                  collisionDetection={closestCenter}\r\n                  onDragEnd={handleDragEnd}\r\n                >\r\n                  <SortableContext\r\n                    items={sortedColumns.map(col => col.id)}\r\n                    strategy={verticalListSortingStrategy}\r\n                  >\r\n                    {sortedColumns.map((column) => (\r\n                      <SortableItem\r\n                        key={column.id}\r\n                        id={column.id}\r\n                        columnName={column.name}\r\n                        direction={column.direction}\r\n                        onDirectionChange={(direction) =>\r\n                          handleDirectionChange(column.id, direction)\r\n                        }\r\n                        onRemove={() => handleRemoveSort(column.id)}\r\n                      />\r\n                    ))}\r\n                  </SortableContext>\r\n                </DndContext>\r\n              </div>\r\n            ) : (\r\n              <div className=\"text-sm text-muted-foreground mb-2 py-2 text-center\">\r\n                Chưa có tiêu chí sắp xếp nào\r\n              </div>\r\n            )}\r\n\r\n            {/* Phần chọn trường sắp xếp */}\r\n            {tempSorting.length < maxSortFields && (\r\n              <div className=\"mb-2\">\r\n                <div className=\"text-xs font-medium mb-1 text-muted-foreground\">Thêm tiêu chí sắp xếp</div>\r\n\r\n                {/* Ô tìm kiếm trường */}\r\n                <div className=\"relative mb-2\">\r\n                  <Search className=\"absolute left-2 top-1/2 h-3.5 w-3.5 -translate-y-1/2 text-muted-foreground\" />\r\n                  <Input\r\n                    placeholder=\"Tìm kiếm trường...\"\r\n                    value={searchQuery}\r\n                    onChange={(e) => setSearchQuery(e.target.value)}\r\n                    className=\"pl-7 h-8 text-sm\"\r\n                  />\r\n                </div>\r\n\r\n                {/* Danh sách các trường có thể sắp xếp */}\r\n                <div className=\"max-h-[150px] overflow-y-auto border rounded-md\">\r\n                  {filteredColumns.length === 0 ? (\r\n                    <div className=\"p-2 text-center text-sm text-muted-foreground\">\r\n                      Không tìm thấy trường nào\r\n                    </div>\r\n                  ) : (\r\n                    <div className=\"p-1\">\r\n                      {filteredColumns.map(column => {\r\n                        const isSelected = tempSorting.some(sort => sort.id === column.id);\r\n                        return (\r\n                          <button\r\n                            key={column.id}\r\n                            onClick={() => handleAddSort(column.id)}\r\n                            disabled={isSelected}\r\n                            className={cn(\r\n                              \"w-full text-left px-2 py-1.5 text-sm rounded-md hover:bg-accent flex items-center justify-between\",\r\n                              isSelected && \"opacity-50 cursor-not-allowed\"\r\n                            )}\r\n                          >\r\n                            <span>\r\n                              {(() => {\r\n                                // Ưu tiên sử dụng meta.header\r\n                                if (column.columnDef.meta?.header) {\r\n                                  return column.columnDef.meta.header;\r\n                                }\r\n\r\n                                // Nếu header là string, sử dụng trực tiếp\r\n                                if (typeof column.columnDef.header === 'string') {\r\n                                  return column.columnDef.header;\r\n                                }\r\n\r\n                                // Tìm kiếm phần tử span trong header\r\n                                const headerElement = document.querySelector(`[data-column-id=\"${column.id}\"] span`);\r\n                                if (headerElement && headerElement.textContent) {\r\n                                  return headerElement.textContent;\r\n                                }\r\n\r\n                                // Fallback: Sử dụng ID\r\n                                return column.id;\r\n                              })()}\r\n                            </span>\r\n                          </button>\r\n                        );\r\n                      })}\r\n                    </div>\r\n                  )}\r\n                </div>\r\n              </div>\r\n            )}\r\n\r\n            {/* Nút Áp dụng và Xóa tất cả */}\r\n            <div className=\"flex items-center justify-between mt-4\">\r\n              <Button\r\n                onClick={handleApply}\r\n                size=\"sm\"\r\n                className=\"flex-1 mr-1\"\r\n              >\r\n                Áp dụng\r\n              </Button>\r\n\r\n              <Button\r\n                onClick={handleClearAll}\r\n                variant=\"link\"\r\n                size=\"sm\"\r\n                className=\"flex-1 ml-1 text-destructive\"\r\n                disabled={tempSorting.length === 0}\r\n              >\r\n                Xóa tất cả\r\n              </Button>\r\n            </div>\r\n          </div>\r\n        </PopoverContent>\r\n      </Popover>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAKA;AAAA;AAAA;AAAA;AAAA;AAAA;AASA;AAEA;AAAA;AACA;AAEA;AAUA;AASA;AAOA;;;AAhDA;;;;;;;;;;;AAmEA,SAAS,aAAa,EACpB,EAAE,EACF,UAAU,EACV,SAAS,EACT,iBAAiB,EACjB,QAAQ,EACU;;IAClB,MAAM,EACJ,UAAU,EACV,SAAS,EACT,UAAU,EACV,SAAS,EACT,UAAU,EACX,GAAG,CAAA,GAAA,mRAAA,CAAA,cAAW,AAAD,EAAE;QAAE;IAAG;IAErB,MAAM,QAAQ;QACZ,WAAW,oQAAA,CAAA,MAAG,CAAC,SAAS,CAAC,QAAQ,CAAC;QAClC;IACF;IAEA,qBACE,sSAAC;QACC,KAAK;QACL,OAAO;QACP,WAAU;;0BAEV,sSAAC;gBAAI,WAAU;;kCACb,sSAAC;wBACE,GAAG,UAAU;wBACb,GAAG,SAAS;wBACb,WAAU;kCAEV,cAAA,sSAAC,6SAAA,CAAA,eAAY;4BAAC,WAAU;;;;;;;;;;;kCAE1B,sSAAC;wBAAK,WAAU;kCAAW;;;;;;;;;;;;0BAE7B,sSAAC;gBAAI,WAAU;;kCACb,sSAAC,8HAAA,CAAA,SAAM;wBACL,SAAQ;wBACR,MAAK;wBACL,WAAU;wBACV,SAAS,IAAM,kBAAkB,cAAc,QAAQ,SAAS;kCAE/D,cAAc,sBACb,sSAAC,mUAAA,CAAA,sBAAmB;4BAAC,WAAU;;;;;iDAE/B,sSAAC,+TAAA,CAAA,oBAAiB;4BAAC,WAAU;;;;;;;;;;;kCAGjC,sSAAC,8HAAA,CAAA,SAAM;wBACL,SAAQ;wBACR,MAAK;wBACL,WAAU;wBACV,SAAS;kCAET,cAAA,sSAAC,mRAAA,CAAA,IAAC;4BAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;AAKvB;GA5DS;;QAaH,mRAAA,CAAA,cAAW;;;KAbR;AA8DF,SAAS,aAAoB,EAClC,KAAK,EACL,SAAS,EACT,eAAe,EACf,mBAAmB,WAAW,EAC9B,gBAAgB,CAAC,EACQ;;IACzB,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,sQAAA,CAAA,WAAQ,AAAD,EAAE;IACjC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,sQAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,sQAAA,CAAA,WAAQ,AAAD,EAAgB,EAAE;IAE/D,oCAAoC;IACpC,MAAM,kBAAkB,CAAA,GAAA,sQAAA,CAAA,UAAO,AAAD;iDAAE;YAC9B,OAAO,MAAM,aAAa,GAAG,MAAM;yDAAC,CAAA,SAClC,OAAO,UAAU,MAAM,OAAO,YAAY;;QAE9C;gDAAG;QAAC;KAAM;IAEV,wCAAwC;IACxC,MAAM,kBAAkB,CAAA,GAAA,sQAAA,CAAA,UAAO,AAAD;iDAAE;YAC9B,IAAI,CAAC,YAAY,IAAI,IAAI;gBACvB,OAAO;YACT;YAEA,MAAM,QAAQ,YAAY,WAAW,GAAG,IAAI;YAC5C,OAAO,gBAAgB,MAAM;yDAAC,CAAA;oBAC5B,mCAAmC;oBACnC,IAAI,SAAS,OAAO,EAAE;oBAEtB,8BAA8B;oBAC9B,IAAI,OAAO,SAAS,CAAC,IAAI,EAAE,QAAQ;wBACjC,SAAS,OAAO,SAAS,CAAC,IAAI,CAAC,MAAM;oBACvC,OAEK,IAAI,OAAO,OAAO,SAAS,CAAC,MAAM,KAAK,UAAU;wBACpD,SAAS,OAAO,SAAS,CAAC,MAAM;oBAClC,OAEK,IAAI,OAAO,OAAO,SAAS,CAAC,MAAM,KAAK,YAAY;wBACtD,MAAM,gBAAgB,SAAS,aAAa,CAAC,CAAC,iBAAiB,EAAE,OAAO,EAAE,CAAC,OAAO,CAAC;wBACnF,IAAI,iBAAiB,cAAc,WAAW,EAAE;4BAC9C,SAAS,cAAc,WAAW;wBACpC;oBACF;oBAEA,OAAO,OAAO,QAAQ,GAAG,WAAW,GAAG,QAAQ,CAAC;gBAClD;;QACF;gDAAG;QAAC;QAAiB;KAAY;IAEjC,wEAAwE;IACxE,CAAA,GAAA,sQAAA,CAAA,YAAS,AAAD;kCAAE;YACR,IAAI,MAAM;gBACR,eAAe,MAAM,QAAQ,GAAG,OAAO;YACzC;QACF;iCAAG;QAAC;QAAM;KAAM;IAEhB,iEAAiE;IACjE,MAAM,gBAAgB,CAAA,GAAA,sQAAA,CAAA,UAAO,AAAD;+CAAE;YAC5B,OAAO,YAAY,GAAG;uDAAC,CAAA;oBACrB,MAAM,SAAS,MAAM,SAAS,CAAC,KAAK,EAAE;oBAEtC,mCAAmC;oBACnC,IAAI,aAAa,KAAK,EAAE;oBAExB,IAAI,QAAQ;wBACV,8BAA8B;wBAC9B,IAAI,OAAO,SAAS,CAAC,IAAI,EAAE,QAAQ;4BACjC,aAAa,OAAO,SAAS,CAAC,IAAI,CAAC,MAAM;wBAC3C,OAEK,IAAI,OAAO,OAAO,SAAS,CAAC,MAAM,KAAK,UAAU;4BACpD,aAAa,OAAO,SAAS,CAAC,MAAM;wBACtC,OAEK,IAAI,OAAO,OAAO,SAAS,CAAC,MAAM,KAAK,YAAY;4BACtD,MAAM,gBAAgB,SAAS,aAAa,CAAC,CAAC,iBAAiB,EAAE,KAAK,EAAE,CAAC,OAAO,CAAC;4BACjF,IAAI,iBAAiB,cAAc,WAAW,EAAE;gCAC9C,aAAa,cAAc,WAAW;4BACxC;wBACF;oBACF;oBAEA,OAAO;wBACL,IAAI,KAAK,EAAE;wBACX,MAAM;wBACN,WAAY,KAAK,IAAI,GAAG,SAAS;oBACnC;gBACF;;QACF;8CAAG;QAAC;QAAa;KAAM;IAEvB,kBAAkB;IAClB,MAAM,UAAU,CAAA,GAAA,2QAAA,CAAA,aAAU,AAAD,EACvB,CAAA,GAAA,2QAAA,CAAA,YAAS,AAAD,EAAE,2QAAA,CAAA,gBAAa,GACvB,CAAA,GAAA,2QAAA,CAAA,YAAS,AAAD,EAAE,2QAAA,CAAA,iBAAc,EAAE;QACxB,kBAAkB,mRAAA,CAAA,8BAA2B;IAC/C;IAGF,6BAA6B;IAC7B,MAAM,gBAAgB,CAAC;QACrB,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,GAAG;QAEzB,IAAI,QAAQ,OAAO,EAAE,KAAK,KAAK,EAAE,EAAE;YACjC,MAAM,WAAW,cAAc,SAAS,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK,OAAO,EAAE;YACtE,MAAM,WAAW,cAAc,SAAS,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK,KAAK,EAAE;YAEpE,MAAM,mBAAmB,CAAA,GAAA,mRAAA,CAAA,YAAS,AAAD,EAAE,eAAe,UAAU;YAE5D,uBAAuB;YACvB,MAAM,aAAa,iBAAiB,GAAG,CAAC,CAAA,MAAO,CAAC;oBAC9C,IAAI,IAAI,EAAE;oBACV,MAAM,IAAI,SAAS,KAAK;gBAC1B,CAAC;YAED,eAAe;QACjB;IACF;IAEA,kCAAkC;IAClC,MAAM,gBAAgB,CAAC;QACrB,wCAAwC;QACxC,MAAM,eAAe,YAAY,IAAI,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK;QAE1D,IAAI,cAAc;YAChB,oCAAoC;YACpC,MAAM,aAAa,YAAY,GAAG,CAAC,CAAA;gBACjC,IAAI,KAAK,EAAE,KAAK,UAAU;oBACxB,OAAO;wBAAE,GAAG,IAAI;wBAAE,MAAM,CAAC,KAAK,IAAI;oBAAC;gBACrC;gBACA,OAAO;YACT;YAEA,eAAe;QACjB,OAAO,IAAI,YAAY,MAAM,GAAG,eAAe;YAC7C,6CAA6C;YAC7C,MAAM,aAAa;mBACd;gBACH;oBAAE,IAAI;oBAAU,MAAM;gBAAM;aAC7B;YAED,eAAe;QACjB;QAEA,oCAAoC;QACpC,eAAe;IACjB;IAEA,+BAA+B;IAC/B,MAAM,wBAAwB,CAAC,UAAkB;QAC/C,MAAM,aAAa,YAAY,GAAG,CAAC,CAAA;YACjC,IAAI,KAAK,EAAE,KAAK,UAAU;gBACxB,OAAO;oBAAE,GAAG,IAAI;oBAAE,MAAM,cAAc;gBAAO;YAC/C;YACA,OAAO;QACT;QAEA,eAAe;IACjB;IAEA,6BAA6B;IAC7B,MAAM,mBAAmB,CAAC;QACxB,MAAM,aAAa,YAAY,MAAM,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK;QAC1D,eAAe;IACjB;IAEA,oCAAoC;IACpC,MAAM,iBAAiB;QACrB,eAAe,EAAE;IACnB;IAEA,6BAA6B;IAC7B,MAAM,cAAc;QAClB,MAAM,UAAU,CAAC;QACjB,QAAQ;IACV;IAEA,qBAAqB;IACrB,MAAM,eAAe;QACnB,eAAe,MAAM,QAAQ,GAAG,OAAO;QACvC,QAAQ;IACV;IAEA,8BAA8B;IAC9B,MAAM,cAAc;QAClB,MAAM,iBAAiB,MAAM,QAAQ,GAAG,OAAO;QAE/C,IAAI,eAAe,MAAM,KAAK,GAAG;YAC/B,qBAAO,sSAAC,+SAAA,CAAA,cAAW;gBAAC,WAAU;;;;;;QAChC;QAEA,IAAI,eAAe,MAAM,KAAK,GAAG;YAC/B,OAAO,cAAc,CAAC,EAAE,CAAC,IAAI,iBAC3B,sSAAC,+TAAA,CAAA,oBAAiB;gBAAC,WAAU;;;;;qCAC7B,sSAAC,mUAAA,CAAA,sBAAmB;gBAAC,WAAU;;;;;;QACnC;QAEA,mDAAmD;QACnD,qBACE,sSAAC;YAAI,WAAU;;8BACb,sSAAC,+SAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;8BACvB,sSAAC,6HAAA,CAAA,QAAK;oBACJ,WAAU;oBACV,SAAQ;8BAEP,eAAe,MAAM;;;;;;;;;;;;IAI9B;IAEA,qBACE,sSAAC;QAAI,WAAW;kBACd,cAAA,sSAAC,+HAAA,CAAA,UAAO;YAAC,MAAM;YAAM,cAAc;;8BACjC,sSAAC,+HAAA,CAAA,iBAAc;oBAAC,OAAO;8BACrB,cAAA,sSAAC,8HAAA,CAAA,SAAM;wBACL,SAAQ;wBACR,MAAK;wBACL,WAAW,CAAA,GAAA,+HAAA,CAAA,KAAE,AAAD,EAAE,YAAY,iBAAiB,MAAM,QAAQ,GAAG,OAAO,CAAC,MAAM,GAAG,KAAK;kCAEjF;;;;;;;;;;;8BAGL,sSAAC,+HAAA,CAAA,iBAAc;oBAAC,OAAM;oBAAM,WAAW;oBAAkB,iBAAiB;oBAAc,mBAAmB,CAAC,IAAM,EAAE,cAAc;8BAChI,cAAA,sSAAC;wBAAI,WAAU;;0CACb,sSAAC;gCAAG,WAAU;0CAA2B;;;;;;4BAGxC,cAAc,MAAM,GAAG,kBACtB,sSAAC;gCAAI,WAAU;0CACb,cAAA,sSAAC,2QAAA,CAAA,aAAU;oCACT,SAAS;oCACT,oBAAoB,2QAAA,CAAA,gBAAa;oCACjC,WAAW;8CAEX,cAAA,sSAAC,mRAAA,CAAA,kBAAe;wCACd,OAAO,cAAc,GAAG,CAAC,CAAA,MAAO,IAAI,EAAE;wCACtC,UAAU,mRAAA,CAAA,8BAA2B;kDAEpC,cAAc,GAAG,CAAC,CAAC,uBAClB,sSAAC;gDAEC,IAAI,OAAO,EAAE;gDACb,YAAY,OAAO,IAAI;gDACvB,WAAW,OAAO,SAAS;gDAC3B,mBAAmB,CAAC,YAClB,sBAAsB,OAAO,EAAE,EAAE;gDAEnC,UAAU,IAAM,iBAAiB,OAAO,EAAE;+CAPrC,OAAO,EAAE;;;;;;;;;;;;;;;;;;;qDAcxB,sSAAC;gCAAI,WAAU;0CAAsD;;;;;;4BAMtE,YAAY,MAAM,GAAG,+BACpB,sSAAC;gCAAI,WAAU;;kDACb,sSAAC;wCAAI,WAAU;kDAAiD;;;;;;kDAGhE,sSAAC;wCAAI,WAAU;;0DACb,sSAAC,6RAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;0DAClB,sSAAC,6HAAA,CAAA,QAAK;gDACJ,aAAY;gDACZ,OAAO;gDACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;gDAC9C,WAAU;;;;;;;;;;;;kDAKd,sSAAC;wCAAI,WAAU;kDACZ,gBAAgB,MAAM,KAAK,kBAC1B,sSAAC;4CAAI,WAAU;sDAAgD;;;;;iEAI/D,sSAAC;4CAAI,WAAU;sDACZ,gBAAgB,GAAG,CAAC,CAAA;gDACnB,MAAM,aAAa,YAAY,IAAI,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK,OAAO,EAAE;gDACjE,qBACE,sSAAC;oDAEC,SAAS,IAAM,cAAc,OAAO,EAAE;oDACtC,UAAU;oDACV,WAAW,CAAA,GAAA,+HAAA,CAAA,KAAE,AAAD,EACV,qGACA,cAAc;8DAGhB,cAAA,sSAAC;kEACE,CAAC;4DACA,8BAA8B;4DAC9B,IAAI,OAAO,SAAS,CAAC,IAAI,EAAE,QAAQ;gEACjC,OAAO,OAAO,SAAS,CAAC,IAAI,CAAC,MAAM;4DACrC;4DAEA,0CAA0C;4DAC1C,IAAI,OAAO,OAAO,SAAS,CAAC,MAAM,KAAK,UAAU;gEAC/C,OAAO,OAAO,SAAS,CAAC,MAAM;4DAChC;4DAEA,qCAAqC;4DACrC,MAAM,gBAAgB,SAAS,aAAa,CAAC,CAAC,iBAAiB,EAAE,OAAO,EAAE,CAAC,OAAO,CAAC;4DACnF,IAAI,iBAAiB,cAAc,WAAW,EAAE;gEAC9C,OAAO,cAAc,WAAW;4DAClC;4DAEA,uBAAuB;4DACvB,OAAO,OAAO,EAAE;wDAClB,CAAC;;;;;;mDA5BE,OAAO,EAAE;;;;;4CAgCpB;;;;;;;;;;;;;;;;;0CAQV,sSAAC;gCAAI,WAAU;;kDACb,sSAAC,8HAAA,CAAA,SAAM;wCACL,SAAS;wCACT,MAAK;wCACL,WAAU;kDACX;;;;;;kDAID,sSAAC,8HAAA,CAAA,SAAM;wCACL,SAAS;wCACT,SAAQ;wCACR,MAAK;wCACL,WAAU;wCACV,UAAU,YAAY,MAAM,KAAK;kDAClC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASf;IA/VgB;;QA2FE,2QAAA,CAAA,aAAU;;;MA3FZ", "debugId": null}}, {"offset": {"line": 3619, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/bloomgoo/gold-exchange-platform/frontend/components/common/data-table/table-toolbar.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport { ColumnVisibilitySelector } from \"@/components/common/selector/column-visibility-selector\"\r\nimport { SortSelector } from \"@/components/common/selector/sort-selector\"\r\nimport { Button } from \"@/components/ui/button\"\r\nimport { Input } from \"@/components/ui/input\"\r\nimport { cn } from \"@/lib/utils\"\r\nimport { Table } from \"@tanstack/react-table\"\r\nimport {\r\n    RefreshCcw,\r\n    Search\r\n} from \"lucide-react\"\r\n\r\ninterface TableToolbarProps<TData> {\r\n    table: Table<TData>\r\n    globalFilter: string\r\n    setGlobalFilter: (value: string) => void\r\n    onRefresh: () => void\r\n    isRefreshing?: boolean\r\n    searchPlaceholder?: string\r\n\r\n    onDeleteSelected?: () => void\r\n    isShowSelectedRows?: boolean\r\n    onShowSelectedRows?: () => void\r\n    // Slot tùy chỉnh để thêm nội dung vào bên trái của thanh tìm kiếm\r\n    beforeSearchSlot?: React.ReactNode\r\n    // Slot tùy chỉnh để thêm nội dung vào bên phải của thanh tìm kiếm\r\n    afterSearchSlot?: React.ReactNode\r\n    // Slot tùy chỉnh để thêm nội dung vào bên trái của các nút hành động\r\n    beforeActionsSlot?: React.ReactNode\r\n    // Slot tùy chỉnh để thêm nội dung vào bên phải của các nút hành động\r\n    afterActionsSlot?: React.ReactNode\r\n    // Tùy chỉnh class cho thanh công cụ\r\n    className?: string\r\n}\r\n\r\nexport function TableToolbar<TData>({\r\n    table,\r\n    globalFilter,\r\n    setGlobalFilter,\r\n    onRefresh,\r\n    isRefreshing = false,\r\n    searchPlaceholder = \"Tìm kiếm...\",\r\n\r\n    onDeleteSelected,\r\n    isShowSelectedRows,\r\n    onShowSelectedRows,\r\n    beforeSearchSlot,\r\n    afterSearchSlot,\r\n    beforeActionsSlot,\r\n    afterActionsSlot,\r\n    className,\r\n}: TableToolbarProps<TData>) {\r\n    const selectedRows = table.getSelectedRowModel().rows\r\n\r\n    return (\r\n        <div className={cn(\"flex items-center justify-between gap-2 px-4 py-2 border-b\", className)}>\r\n            {/* Left Side */}\r\n            <div className=\"flex items-center space-x-4 flex-1\">\r\n                {/* Slot trước thanh tìm kiếm */}\r\n                {beforeSearchSlot}\r\n\r\n                <div className=\"relative w-64\">\r\n                    <Search\r\n                        className=\"h-4 w-4 absolute left-2 top-1/2 transform -translate-y-1/2 text-muted-foreground\"\r\n                    />\r\n                    <Input\r\n                        placeholder={searchPlaceholder}\r\n                        className=\"pl-8 h-9\"\r\n                        value={globalFilter}\r\n                        onChange={(e) => setGlobalFilter(e.target.value)}\r\n                    />\r\n                </div>\r\n\r\n                {/* Slot sau thanh tìm kiếm */}\r\n                {afterSearchSlot}\r\n            </div>\r\n\r\n            {/* Right Side */}\r\n            <div className=\"flex items-center space-x-2\">\r\n                {/* Slot trước các nút hành động */}\r\n                {beforeActionsSlot}\r\n                {onDeleteSelected && (<></>)}\r\n\r\n                {/* <FilterSelector table={table} /> */}\r\n\r\n                <SortSelector table={table} />\r\n\r\n                <ColumnVisibilitySelector table={table} />\r\n\r\n                <Button\r\n                    variant=\"ghost\"\r\n                    size=\"icon\"\r\n                    onClick={onRefresh}\r\n                    disabled={isRefreshing}\r\n                >\r\n                    <RefreshCcw className={cn(\"h-4 w-4\", isRefreshing && \"animate-spin\")} />\r\n                </Button>\r\n\r\n                {/* Slot sau các nút hành động */}\r\n                {afterActionsSlot}\r\n            </div>\r\n        </div>\r\n    )\r\n}"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AAAA;AAEA;AAAA;AARA;;;;;;;;AAoCO,SAAS,aAAoB,EAChC,KAAK,EACL,YAAY,EACZ,eAAe,EACf,SAAS,EACT,eAAe,KAAK,EACpB,oBAAoB,aAAa,EAEjC,gBAAgB,EAChB,kBAAkB,EAClB,kBAAkB,EAClB,gBAAgB,EAChB,eAAe,EACf,iBAAiB,EACjB,gBAAgB,EAChB,SAAS,EACc;IACvB,MAAM,eAAe,MAAM,mBAAmB,GAAG,IAAI;IAErD,qBACI,sSAAC;QAAI,WAAW,CAAA,GAAA,+HAAA,CAAA,KAAE,AAAD,EAAE,8DAA8D;;0BAE7E,sSAAC;gBAAI,WAAU;;oBAEV;kCAED,sSAAC;wBAAI,WAAU;;0CACX,sSAAC,6RAAA,CAAA,SAAM;gCACH,WAAU;;;;;;0CAEd,sSAAC,6HAAA,CAAA,QAAK;gCACF,aAAa;gCACb,WAAU;gCACV,OAAO;gCACP,UAAU,CAAC,IAAM,gBAAgB,EAAE,MAAM,CAAC,KAAK;;;;;;;;;;;;oBAKtD;;;;;;;0BAIL,sSAAC;gBAAI,WAAU;;oBAEV;oBACA,kCAAqB;kCAItB,sSAAC,wJAAA,CAAA,eAAY;wBAAC,OAAO;;;;;;kCAErB,sSAAC,wKAAA,CAAA,2BAAwB;wBAAC,OAAO;;;;;;kCAEjC,sSAAC,8HAAA,CAAA,SAAM;wBACH,SAAQ;wBACR,MAAK;wBACL,SAAS;wBACT,UAAU;kCAEV,cAAA,sSAAC,ySAAA,CAAA,aAAU;4BAAC,WAAW,CAAA,GAAA,+HAAA,CAAA,KAAE,AAAD,EAAE,WAAW,gBAAgB;;;;;;;;;;;oBAIxD;;;;;;;;;;;;;AAIjB;KApEgB", "debugId": null}}, {"offset": {"line": 3743, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/bloomgoo/gold-exchange-platform/frontend/components/common/selector/page-selector.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { Button } from '@/components/ui/button';\r\nimport {\r\n   Command,\r\n   CommandEmpty,\r\n   CommandGroup,\r\n   CommandInput,\r\n   CommandItem,\r\n   CommandList,\r\n} from '@/components/ui/command';\r\nimport { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';\r\nimport { CheckIcon, ChevronLeft, ChevronRight, ChevronsLeft, ChevronsRight } from 'lucide-react';\r\nimport { useId, useState, useEffect } from 'react';\r\nimport { Table as TableType } from \"@tanstack/react-table\";\r\n\r\n// Interface for table-based pagination\r\ninterface TablePaginationProps {\r\n   table: TableType<any>;\r\n   type: 'table';\r\n}\r\n\r\n// Interface for custom pagination\r\ninterface CustomPaginationProps {\r\n   type: 'custom';\r\n   currentPage: number;\r\n   totalPages: number;\r\n   onPageChange: (page: number) => void;\r\n}\r\n\r\n// Combined props type\r\ntype PageSelectorProps = {\r\n   maxPagesToShow?: number;\r\n   showFirstLastButtons?: boolean;\r\n   showPrevNextButtons?: boolean;\r\n   className?: string;\r\n} & (TablePaginationProps | CustomPaginationProps);\r\n\r\nexport function PageSelector(props: PageSelectorProps) {\r\n   const id = useId();\r\n   const [open, setOpen] = useState<boolean>(false);\r\n   \r\n   // Determine current page and total pages based on props type\r\n   const currentPage = props.type === 'table' \r\n      ? props.table.getState().pagination.pageIndex + 1 \r\n      : props.currentPage;\r\n   \r\n   const totalPages = props.type === 'table'\r\n      ? props.table.getPageCount()\r\n      : props.totalPages;\r\n   \r\n   // Default to showing 5 pages in the selector\r\n   const maxPagesToShow = props.maxPagesToShow || 5;\r\n   \r\n   // Generate array of page numbers to display\r\n   const getPageNumbers = () => {\r\n      if (totalPages <= maxPagesToShow) {\r\n         // If total pages is less than max to show, display all pages\r\n         return Array.from({ length: totalPages }, (_, i) => i + 1);\r\n      }\r\n      \r\n      // Calculate start and end page numbers\r\n      let startPage = Math.max(1, currentPage - Math.floor(maxPagesToShow / 2));\r\n      let endPage = startPage + maxPagesToShow - 1;\r\n      \r\n      // Adjust if end page exceeds total pages\r\n      if (endPage > totalPages) {\r\n         endPage = totalPages;\r\n         startPage = Math.max(1, endPage - maxPagesToShow + 1);\r\n      }\r\n      \r\n      return Array.from({ length: endPage - startPage + 1 }, (_, i) => startPage + i);\r\n   };\r\n   \r\n   const pageNumbers = getPageNumbers();\r\n   \r\n   // Handle page change\r\n   const handlePageChange = (page: number) => {\r\n      if (props.type === 'table') {\r\n         props.table.setPageIndex(page - 1);\r\n      } else {\r\n         props.onPageChange(page);\r\n      }\r\n      setOpen(false);\r\n   };\r\n   \r\n   // Handle navigation to first page\r\n   const goToFirstPage = () => {\r\n      if (props.type === 'table') {\r\n         props.table.setPageIndex(0);\r\n      } else {\r\n         props.onPageChange(1);\r\n      }\r\n   };\r\n   \r\n   // Handle navigation to previous page\r\n   const goToPreviousPage = () => {\r\n      if (props.type === 'table') {\r\n         props.table.previousPage();\r\n      } else {\r\n         props.onPageChange(Math.max(1, currentPage - 1));\r\n      }\r\n   };\r\n   \r\n   // Handle navigation to next page\r\n   const goToNextPage = () => {\r\n      if (props.type === 'table') {\r\n         props.table.nextPage();\r\n      } else {\r\n         props.onPageChange(Math.min(totalPages, currentPage + 1));\r\n      }\r\n   };\r\n   \r\n   // Handle navigation to last page\r\n   const goToLastPage = () => {\r\n      if (props.type === 'table') {\r\n         props.table.setPageIndex(totalPages - 1);\r\n      } else {\r\n         props.onPageChange(totalPages);\r\n      }\r\n   };\r\n   \r\n   // Check if can go to previous or next page\r\n   const canPreviousPage = props.type === 'table' \r\n      ? props.table.getCanPreviousPage()\r\n      : currentPage > 1;\r\n      \r\n   const canNextPage = props.type === 'table'\r\n      ? props.table.getCanNextPage()\r\n      : currentPage < totalPages;\r\n\r\n   return (\r\n      <div className=\"flex items-center space-x-2\">\r\n         {/* First Page Button */}\r\n         {props.showFirstLastButtons && (\r\n            <Button\r\n               variant=\"outline\"\r\n               className=\"hidden h-8 w-8 p-0 lg:flex\"\r\n               onClick={goToFirstPage}\r\n               disabled={!canPreviousPage}\r\n            >\r\n               <ChevronsLeft className=\"size-4\" />\r\n            </Button>\r\n         )}\r\n         \r\n         {/* Previous Page Button */}\r\n         {props.showPrevNextButtons && (\r\n            <Button\r\n               variant=\"outline\"\r\n               className=\"h-8 w-8 p-0\"\r\n               onClick={goToPreviousPage}\r\n               disabled={!canPreviousPage}\r\n            >\r\n               <ChevronLeft className=\"size-4\" />\r\n            </Button>\r\n         )}\r\n         \r\n         {/* Page Selector */}\r\n         <Popover open={open} onOpenChange={setOpen}>\r\n            <PopoverTrigger asChild>\r\n               <Button\r\n                  id={id}\r\n                  className=\"flex items-center justify-center h-8 px-3\"\r\n                  size=\"sm\"\r\n                  variant=\"outline\"\r\n                  role=\"combobox\"\r\n                  aria-expanded={open}\r\n               >\r\n                  <span className=\"text-sm font-medium\">\r\n                     {currentPage} / {totalPages}\r\n                  </span>\r\n               </Button>\r\n            </PopoverTrigger>\r\n            <PopoverContent className=\"border-input w-48 p-0\" align=\"center\" side=\"top\">\r\n               <Command>\r\n                  <CommandInput placeholder=\"Trang...\" />\r\n                  <CommandList>\r\n                     <CommandEmpty>Không tìm thấy trang.</CommandEmpty>\r\n                     <CommandGroup>\r\n                        {pageNumbers.map((page) => (\r\n                           <CommandItem\r\n                              key={page}\r\n                              value={page.toString()}\r\n                              onSelect={() => handlePageChange(page)}\r\n                              className=\"flex items-center justify-between\"\r\n                           >\r\n                              <div className=\"flex items-center gap-2\">\r\n                                 <span className=\"text-xs\">Trang {page}</span>\r\n                              </div>\r\n                              {currentPage === page && <CheckIcon size={14} className=\"ml-auto\" />}\r\n                           </CommandItem>\r\n                        ))}\r\n                     </CommandGroup>\r\n                  </CommandList>\r\n               </Command>\r\n            </PopoverContent>\r\n         </Popover>\r\n         \r\n         {/* Next Page Button */}\r\n         {props.showPrevNextButtons && (\r\n            <Button\r\n               variant=\"outline\"\r\n               className=\"h-8 w-8 p-0\"\r\n               onClick={goToNextPage}\r\n               disabled={!canNextPage}\r\n            >\r\n               <ChevronRight className=\"size-4\" />\r\n            </Button>\r\n         )}\r\n         \r\n         {/* Last Page Button */}\r\n         {props.showFirstLastButtons && (\r\n            <Button\r\n               variant=\"outline\"\r\n               className=\"hidden h-8 w-8 p-0 lg:flex\"\r\n               onClick={goToLastPage}\r\n               disabled={!canNextPage}\r\n            >\r\n               <ChevronsRight className=\"size-4\" />\r\n            </Button>\r\n         )}\r\n      </div>\r\n   );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAQA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;;;AAbA;;;;;;AAsCO,SAAS,aAAa,KAAwB;;IAClD,MAAM,KAAK,CAAA,GAAA,sQAAA,CAAA,QAAK,AAAD;IACf,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,sQAAA,CAAA,WAAQ,AAAD,EAAW;IAE1C,6DAA6D;IAC7D,MAAM,cAAc,MAAM,IAAI,KAAK,UAC9B,MAAM,KAAK,CAAC,QAAQ,GAAG,UAAU,CAAC,SAAS,GAAG,IAC9C,MAAM,WAAW;IAEtB,MAAM,aAAa,MAAM,IAAI,KAAK,UAC7B,MAAM,KAAK,CAAC,YAAY,KACxB,MAAM,UAAU;IAErB,6CAA6C;IAC7C,MAAM,iBAAiB,MAAM,cAAc,IAAI;IAE/C,4CAA4C;IAC5C,MAAM,iBAAiB;QACpB,IAAI,cAAc,gBAAgB;YAC/B,6DAA6D;YAC7D,OAAO,MAAM,IAAI,CAAC;gBAAE,QAAQ;YAAW,GAAG,CAAC,GAAG,IAAM,IAAI;QAC3D;QAEA,uCAAuC;QACvC,IAAI,YAAY,KAAK,GAAG,CAAC,GAAG,cAAc,KAAK,KAAK,CAAC,iBAAiB;QACtE,IAAI,UAAU,YAAY,iBAAiB;QAE3C,yCAAyC;QACzC,IAAI,UAAU,YAAY;YACvB,UAAU;YACV,YAAY,KAAK,GAAG,CAAC,GAAG,UAAU,iBAAiB;QACtD;QAEA,OAAO,MAAM,IAAI,CAAC;YAAE,QAAQ,UAAU,YAAY;QAAE,GAAG,CAAC,GAAG,IAAM,YAAY;IAChF;IAEA,MAAM,cAAc;IAEpB,qBAAqB;IACrB,MAAM,mBAAmB,CAAC;QACvB,IAAI,MAAM,IAAI,KAAK,SAAS;YACzB,MAAM,KAAK,CAAC,YAAY,CAAC,OAAO;QACnC,OAAO;YACJ,MAAM,YAAY,CAAC;QACtB;QACA,QAAQ;IACX;IAEA,kCAAkC;IAClC,MAAM,gBAAgB;QACnB,IAAI,MAAM,IAAI,KAAK,SAAS;YACzB,MAAM,KAAK,CAAC,YAAY,CAAC;QAC5B,OAAO;YACJ,MAAM,YAAY,CAAC;QACtB;IACH;IAEA,qCAAqC;IACrC,MAAM,mBAAmB;QACtB,IAAI,MAAM,IAAI,KAAK,SAAS;YACzB,MAAM,KAAK,CAAC,YAAY;QAC3B,OAAO;YACJ,MAAM,YAAY,CAAC,KAAK,GAAG,CAAC,GAAG,cAAc;QAChD;IACH;IAEA,iCAAiC;IACjC,MAAM,eAAe;QAClB,IAAI,MAAM,IAAI,KAAK,SAAS;YACzB,MAAM,KAAK,CAAC,QAAQ;QACvB,OAAO;YACJ,MAAM,YAAY,CAAC,KAAK,GAAG,CAAC,YAAY,cAAc;QACzD;IACH;IAEA,iCAAiC;IACjC,MAAM,eAAe;QAClB,IAAI,MAAM,IAAI,KAAK,SAAS;YACzB,MAAM,KAAK,CAAC,YAAY,CAAC,aAAa;QACzC,OAAO;YACJ,MAAM,YAAY,CAAC;QACtB;IACH;IAEA,2CAA2C;IAC3C,MAAM,kBAAkB,MAAM,IAAI,KAAK,UAClC,MAAM,KAAK,CAAC,kBAAkB,KAC9B,cAAc;IAEnB,MAAM,cAAc,MAAM,IAAI,KAAK,UAC9B,MAAM,KAAK,CAAC,cAAc,KAC1B,cAAc;IAEnB,qBACG,sSAAC;QAAI,WAAU;;YAEX,MAAM,oBAAoB,kBACxB,sSAAC,8HAAA,CAAA,SAAM;gBACJ,SAAQ;gBACR,WAAU;gBACV,SAAS;gBACT,UAAU,CAAC;0BAEX,cAAA,sSAAC,6SAAA,CAAA,eAAY;oBAAC,WAAU;;;;;;;;;;;YAK7B,MAAM,mBAAmB,kBACvB,sSAAC,8HAAA,CAAA,SAAM;gBACJ,SAAQ;gBACR,WAAU;gBACV,SAAS;gBACT,UAAU,CAAC;0BAEX,cAAA,sSAAC,2SAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;;;;;;0BAK7B,sSAAC,+HAAA,CAAA,UAAO;gBAAC,MAAM;gBAAM,cAAc;;kCAChC,sSAAC,+HAAA,CAAA,iBAAc;wBAAC,OAAO;kCACpB,cAAA,sSAAC,8HAAA,CAAA,SAAM;4BACJ,IAAI;4BACJ,WAAU;4BACV,MAAK;4BACL,SAAQ;4BACR,MAAK;4BACL,iBAAe;sCAEf,cAAA,sSAAC;gCAAK,WAAU;;oCACZ;oCAAY;oCAAI;;;;;;;;;;;;;;;;;kCAI1B,sSAAC,+HAAA,CAAA,iBAAc;wBAAC,WAAU;wBAAwB,OAAM;wBAAS,MAAK;kCACnE,cAAA,sSAAC,+HAAA,CAAA,UAAO;;8CACL,sSAAC,+HAAA,CAAA,eAAY;oCAAC,aAAY;;;;;;8CAC1B,sSAAC,+HAAA,CAAA,cAAW;;sDACT,sSAAC,+HAAA,CAAA,eAAY;sDAAC;;;;;;sDACd,sSAAC,+HAAA,CAAA,eAAY;sDACT,YAAY,GAAG,CAAC,CAAC,qBACf,sSAAC,+HAAA,CAAA,cAAW;oDAET,OAAO,KAAK,QAAQ;oDACpB,UAAU,IAAM,iBAAiB;oDACjC,WAAU;;sEAEV,sSAAC;4DAAI,WAAU;sEACZ,cAAA,sSAAC;gEAAK,WAAU;;oEAAU;oEAAO;;;;;;;;;;;;wDAEnC,gBAAgB,sBAAQ,sSAAC,+RAAA,CAAA,YAAS;4DAAC,MAAM;4DAAI,WAAU;;;;;;;mDARnD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAkBzB,MAAM,mBAAmB,kBACvB,sSAAC,8HAAA,CAAA,SAAM;gBACJ,SAAQ;gBACR,WAAU;gBACV,SAAS;gBACT,UAAU,CAAC;0BAEX,cAAA,sSAAC,6SAAA,CAAA,eAAY;oBAAC,WAAU;;;;;;;;;;;YAK7B,MAAM,oBAAoB,kBACxB,sSAAC,8HAAA,CAAA,SAAM;gBACJ,SAAQ;gBACR,WAAU;gBACV,SAAS;gBACT,UAAU,CAAC;0BAEX,cAAA,sSAAC,+SAAA,CAAA,gBAAa;oBAAC,WAAU;;;;;;;;;;;;;;;;;AAKxC;GAzLgB;;QACF,sQAAA,CAAA,QAAK;;;KADH", "debugId": null}}, {"offset": {"line": 4056, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/bloomgoo/gold-exchange-platform/frontend/components/common/selector/page-size-selector.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { Button } from '@/components/ui/button';\r\nimport {\r\n   Command,\r\n   CommandEmpty,\r\n   CommandGroup,\r\n   CommandInput,\r\n   CommandItem,\r\n   CommandList,\r\n} from '@/components/ui/command';\r\nimport { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';\r\nimport { CheckIcon } from 'lucide-react';\r\nimport { useId, useState } from 'react';\r\nimport { Table as TableType } from \"@tanstack/react-table\";\r\n\r\n// Interface for table-based pagination\r\ninterface TablePageSizeProps {\r\n   table: TableType<any>;\r\n   type: 'table';\r\n}\r\n\r\n// Interface for custom pagination\r\ninterface CustomPageSizeProps {\r\n   type: 'custom';\r\n   pageSize: number;\r\n   onPageSizeChange: (pageSize: number) => void;\r\n}\r\n\r\n// Combined props type\r\ntype PageSizeSelectorProps = {\r\n   pageSizeOptions?: number[];\r\n   className?: string;\r\n   label?: string;\r\n   triggerClassName?: string;\r\n} & (TablePageSizeProps | CustomPageSizeProps);\r\n\r\nexport function PageSizeSelector(props: PageSizeSelectorProps) {\r\n   const id = useId();\r\n   const [open, setOpen] = useState<boolean>(false);\r\n   \r\n   // Default page size options if not provided\r\n   const pageSizeOptions = props.pageSizeOptions || [10, 20, 30, 50, 100];\r\n   \r\n   // Get current page size based on props type\r\n   const currentPageSize = props.type === 'table' \r\n      ? props.table.getState().pagination.pageSize \r\n      : props.pageSize;\r\n   \r\n   // Handle page size change\r\n   const handlePageSizeChange = (pageSize: number) => {\r\n      if (props.type === 'table') {\r\n         props.table.setPageSize(pageSize);\r\n      } else {\r\n         props.onPageSizeChange(pageSize);\r\n      }\r\n      setOpen(false);\r\n   };\r\n\r\n   return (\r\n      <div className=\"flex items-center space-x-2\">\r\n         {props.label && (\r\n            <span className=\"text-sm text-muted-foreground\">{props.label}</span>\r\n         )}\r\n         \r\n         <Popover open={open} onOpenChange={setOpen}>\r\n            <PopoverTrigger asChild>\r\n               <Button\r\n                  id={id}\r\n                  className={`flex items-center justify-center h-8 px-3 ${props.triggerClassName || ''}`}\r\n                  size=\"sm\"\r\n                  variant=\"outline\"\r\n                  role=\"combobox\"\r\n                  aria-expanded={open}\r\n               >\r\n                  <span className=\"text-sm font-medium\">\r\n                     {currentPageSize} bản ghi\r\n                  </span>\r\n               </Button>\r\n            </PopoverTrigger>\r\n            <PopoverContent className=\"border-input w-48 p-0\" align=\"start\" side=\"top\">\r\n               <Command>\r\n                  <CommandInput placeholder=\"Số bản ghi...\" />\r\n                  <CommandList>\r\n                     <CommandEmpty>Không tìm thấy tùy chọn.</CommandEmpty>\r\n                     <CommandGroup>\r\n                        {pageSizeOptions.map((size) => (\r\n                           <CommandItem\r\n                              key={size}\r\n                              value={size.toString()}\r\n                              onSelect={() => handlePageSizeChange(size)}\r\n                              className=\"flex items-center justify-between\"\r\n                           >\r\n                              <div className=\"flex items-center gap-2\">\r\n                                 <span className=\"text-xs\">{size} bản ghi</span>\r\n                              </div>\r\n                              {currentPageSize === size && <CheckIcon size={14} className=\"ml-auto\" />}\r\n                           </CommandItem>\r\n                        ))}\r\n                     </CommandGroup>\r\n                  </CommandList>\r\n               </Command>\r\n            </PopoverContent>\r\n         </Popover>\r\n      </div>\r\n   );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAQA;AACA;AACA;;;AAbA;;;;;;AAqCO,SAAS,iBAAiB,KAA4B;;IAC1D,MAAM,KAAK,CAAA,GAAA,sQAAA,CAAA,QAAK,AAAD;IACf,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,sQAAA,CAAA,WAAQ,AAAD,EAAW;IAE1C,4CAA4C;IAC5C,MAAM,kBAAkB,MAAM,eAAe,IAAI;QAAC;QAAI;QAAI;QAAI;QAAI;KAAI;IAEtE,4CAA4C;IAC5C,MAAM,kBAAkB,MAAM,IAAI,KAAK,UAClC,MAAM,KAAK,CAAC,QAAQ,GAAG,UAAU,CAAC,QAAQ,GAC1C,MAAM,QAAQ;IAEnB,0BAA0B;IAC1B,MAAM,uBAAuB,CAAC;QAC3B,IAAI,MAAM,IAAI,KAAK,SAAS;YACzB,MAAM,KAAK,CAAC,WAAW,CAAC;QAC3B,OAAO;YACJ,MAAM,gBAAgB,CAAC;QAC1B;QACA,QAAQ;IACX;IAEA,qBACG,sSAAC;QAAI,WAAU;;YACX,MAAM,KAAK,kBACT,sSAAC;gBAAK,WAAU;0BAAiC,MAAM,KAAK;;;;;;0BAG/D,sSAAC,+HAAA,CAAA,UAAO;gBAAC,MAAM;gBAAM,cAAc;;kCAChC,sSAAC,+HAAA,CAAA,iBAAc;wBAAC,OAAO;kCACpB,cAAA,sSAAC,8HAAA,CAAA,SAAM;4BACJ,IAAI;4BACJ,WAAW,CAAC,0CAA0C,EAAE,MAAM,gBAAgB,IAAI,IAAI;4BACtF,MAAK;4BACL,SAAQ;4BACR,MAAK;4BACL,iBAAe;sCAEf,cAAA,sSAAC;gCAAK,WAAU;;oCACZ;oCAAgB;;;;;;;;;;;;;;;;;kCAI1B,sSAAC,+HAAA,CAAA,iBAAc;wBAAC,WAAU;wBAAwB,OAAM;wBAAQ,MAAK;kCAClE,cAAA,sSAAC,+HAAA,CAAA,UAAO;;8CACL,sSAAC,+HAAA,CAAA,eAAY;oCAAC,aAAY;;;;;;8CAC1B,sSAAC,+HAAA,CAAA,cAAW;;sDACT,sSAAC,+HAAA,CAAA,eAAY;sDAAC;;;;;;sDACd,sSAAC,+HAAA,CAAA,eAAY;sDACT,gBAAgB,GAAG,CAAC,CAAC,qBACnB,sSAAC,+HAAA,CAAA,cAAW;oDAET,OAAO,KAAK,QAAQ;oDACpB,UAAU,IAAM,qBAAqB;oDACrC,WAAU;;sEAEV,sSAAC;4DAAI,WAAU;sEACZ,cAAA,sSAAC;gEAAK,WAAU;;oEAAW;oEAAK;;;;;;;;;;;;wDAElC,oBAAoB,sBAAQ,sSAAC,+RAAA,CAAA,YAAS;4DAAC,MAAM;4DAAI,WAAU;;;;;;;mDARvD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAkBnC;GArEgB;;QACF,sQAAA,CAAA,QAAK;;;KADH", "debugId": null}}, {"offset": {"line": 4253, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/bloomgoo/gold-exchange-platform/frontend/components/common/data-table/table-footer.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport { Table as TableType } from \"@tanstack/react-table\"\r\nimport { But<PERSON> } from \"@/components/ui/button\"\r\nimport { ChevronLeft, ChevronRight, ChevronsLeft, ChevronsRight } from \"lucide-react\"\r\nimport { PageSelector } from \"@/components/common/selector/page-selector\"\r\nimport { PageSizeSelector } from \"@/components/common/selector/page-size-selector\"\r\n\r\ninterface TableFooterProps<TData> {\r\n  table: TableType<TData>\r\n  className?: string\r\n  totalItems?: number\r\n  isShowSelectedRows?: boolean\r\n  onShowSelectedRows?: () => void\r\n}\r\n\r\nexport function TableFooter<TData>({\r\n  table,\r\n  className,\r\n  totalItems,\r\n  isShowSelectedRows,\r\n  onShowSelectedRows,\r\n}: TableFooterProps<TData>) {\r\n  // Kiểm tra xem có hàng nào được chọn không\r\n  const selectedRowsCount = table.getFilteredSelectedRowModel().rows.length;\r\n  const hasSelectedRows = selectedRowsCount > 0;\r\n\r\n  return (\r\n    <div className=\"sticky bottom-0 bg-background border-t mt-auto\">\r\n      <div className=\"flex items-center justify-between space-x-2 px-4 py-2\">\r\n        <div className=\"flex items-center space-x-2\">\r\n          <PageSizeSelector\r\n            type=\"table\"\r\n            table={table}\r\n            pageSizeOptions={[20, 50, 100, 200]}\r\n            triggerClassName=\"w-[120px]\"\r\n          />\r\n\r\n          {/* Hiển thị thông tin về hàng đã chọn khi có hàng được chọn */}\r\n          {hasSelectedRows && (\r\n            <p className=\"text-sm text-muted-foreground\">\r\n              {selectedRowsCount} trên{\" \"}\r\n              {table.getFilteredRowModel().rows.length} bản ghi đã chọn.\r\n            </p>\r\n          )}\r\n        </div>\r\n\r\n        <div className=\"flex items-center space-x-6 lg:space-x-8\">\r\n          <div className=\"flex w-[100x] items-center justify-center text-sm font-medium\">\r\n            Hiển thị {table.getRowCount()} trên {totalItems} bản ghi\r\n          </div>\r\n          <PageSelector\r\n            type=\"table\"\r\n            table={table}\r\n            showFirstLastButtons={true}\r\n            showPrevNextButtons={true}\r\n            maxPagesToShow={7}\r\n          />\r\n        </div>\r\n      </div>\r\n    </div>\r\n  )\r\n}"], "names": [], "mappings": ";;;;AAKA;AACA;AANA;;;;AAgBO,SAAS,YAAmB,EACjC,KAAK,EACL,SAAS,EACT,UAAU,EACV,kBAAkB,EAClB,kBAAkB,EACM;IACxB,2CAA2C;IAC3C,MAAM,oBAAoB,MAAM,2BAA2B,GAAG,IAAI,CAAC,MAAM;IACzE,MAAM,kBAAkB,oBAAoB;IAE5C,qBACE,sSAAC;QAAI,WAAU;kBACb,cAAA,sSAAC;YAAI,WAAU;;8BACb,sSAAC;oBAAI,WAAU;;sCACb,sSAAC,gKAAA,CAAA,mBAAgB;4BACf,MAAK;4BACL,OAAO;4BACP,iBAAiB;gCAAC;gCAAI;gCAAI;gCAAK;6BAAI;4BACnC,kBAAiB;;;;;;wBAIlB,iCACC,sSAAC;4BAAE,WAAU;;gCACV;gCAAkB;gCAAM;gCACxB,MAAM,mBAAmB,GAAG,IAAI,CAAC,MAAM;gCAAC;;;;;;;;;;;;;8BAK/C,sSAAC;oBAAI,WAAU;;sCACb,sSAAC;4BAAI,WAAU;;gCAAgE;gCACnE,MAAM,WAAW;gCAAG;gCAAO;gCAAW;;;;;;;sCAElD,sSAAC,wJAAA,CAAA,eAAY;4BACX,MAAK;4BACL,OAAO;4BACP,sBAAsB;4BACtB,qBAAqB;4BACrB,gBAAgB;;;;;;;;;;;;;;;;;;;;;;;AAM5B;KA9CgB", "debugId": null}}, {"offset": {"line": 4368, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/bloomgoo/gold-exchange-platform/frontend/components/ui/label.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as LabelPrimitive from \"@radix-ui/react-label\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction Label({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof LabelPrimitive.Root>) {\r\n  return (\r\n    <LabelPrimitive.Root\r\n      data-slot=\"label\"\r\n      className={cn(\r\n        \"flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport { Label }\r\n"], "names": [], "mappings": ";;;;AAGA;AAEA;AAAA;AALA;;;;AAOA,SAAS,MAAM,EACb,SAAS,EACT,GAAG,OAC8C;IACjD,qBACE,sSAAC,iRAAA,CAAA,OAAmB;QAClB,aAAU;QACV,WAAW,CAAA,GAAA,+HAAA,CAAA,KAAE,AAAD,EACV,uNACA;QAED,GAAG,KAAK;;;;;;AAGf;KAdS", "debugId": null}}, {"offset": {"line": 4403, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/bloomgoo/gold-exchange-platform/frontend/components/ui/form.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as LabelPrimitive from \"@radix-ui/react-label\"\r\nimport { Slot } from \"@radix-ui/react-slot\"\r\nimport {\r\n  Controller,\r\n  FormProvider,\r\n  useFormContext,\r\n  useFormState,\r\n  type ControllerProps,\r\n  type FieldPath,\r\n  type FieldValues,\r\n} from \"react-hook-form\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\nimport { Label } from \"@/components/ui/label\"\r\n\r\nconst Form = FormProvider\r\n\r\ntype FormFieldContextValue<\r\n  TFieldValues extends FieldValues = FieldValues,\r\n  TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>,\r\n> = {\r\n  name: TName\r\n}\r\n\r\nconst FormFieldContext = React.createContext<FormFieldContextValue>(\r\n  {} as FormFieldContextValue\r\n)\r\n\r\nconst FormField = <\r\n  TFieldValues extends FieldValues = FieldValues,\r\n  TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>,\r\n>({\r\n  ...props\r\n}: ControllerProps<TFieldValues, TName>) => {\r\n  return (\r\n    <FormFieldContext.Provider value={{ name: props.name }}>\r\n      <Controller {...props} />\r\n    </FormFieldContext.Provider>\r\n  )\r\n}\r\n\r\nconst useFormField = () => {\r\n  const fieldContext = React.useContext(FormFieldContext)\r\n  const itemContext = React.useContext(FormItemContext)\r\n  const { getFieldState } = useFormContext()\r\n  const formState = useFormState({ name: fieldContext.name })\r\n  const fieldState = getFieldState(fieldContext.name, formState)\r\n\r\n  if (!fieldContext) {\r\n    throw new Error(\"useFormField should be used within <FormField>\")\r\n  }\r\n\r\n  const { id } = itemContext\r\n\r\n  return {\r\n    id,\r\n    name: fieldContext.name,\r\n    formItemId: `${id}-form-item`,\r\n    formDescriptionId: `${id}-form-item-description`,\r\n    formMessageId: `${id}-form-item-message`,\r\n    ...fieldState,\r\n  }\r\n}\r\n\r\ntype FormItemContextValue = {\r\n  id: string\r\n}\r\n\r\nconst FormItemContext = React.createContext<FormItemContextValue>(\r\n  {} as FormItemContextValue\r\n)\r\n\r\nfunction FormItem({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  const id = React.useId()\r\n\r\n  return (\r\n    <FormItemContext.Provider value={{ id }}>\r\n      <div\r\n        data-slot=\"form-item\"\r\n        className={cn(\"grid gap-2\", className)}\r\n        {...props}\r\n      />\r\n    </FormItemContext.Provider>\r\n  )\r\n}\r\n\r\nfunction FormLabel({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof LabelPrimitive.Root>) {\r\n  const { error, formItemId } = useFormField()\r\n\r\n  return (\r\n    <Label\r\n      data-slot=\"form-label\"\r\n      data-error={!!error}\r\n      className={cn(\"data-[error=true]:text-destructive\", className)}\r\n      htmlFor={formItemId}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction FormControl({ ...props }: React.ComponentProps<typeof Slot>) {\r\n  const { error, formItemId, formDescriptionId, formMessageId } = useFormField()\r\n\r\n  return (\r\n    <Slot\r\n      data-slot=\"form-control\"\r\n      id={formItemId}\r\n      aria-describedby={\r\n        !error\r\n          ? `${formDescriptionId}`\r\n          : `${formDescriptionId} ${formMessageId}`\r\n      }\r\n      aria-invalid={!!error}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction FormDescription({ className, ...props }: React.ComponentProps<\"p\">) {\r\n  const { formDescriptionId } = useFormField()\r\n\r\n  return (\r\n    <p\r\n      data-slot=\"form-description\"\r\n      id={formDescriptionId}\r\n      className={cn(\"text-muted-foreground text-sm\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction FormMessage({ className, ...props }: React.ComponentProps<\"p\">) {\r\n  const { error, formMessageId } = useFormField()\r\n  const body = error ? String(error?.message ?? \"\") : props.children\r\n\r\n  if (!body) {\r\n    return null\r\n  }\r\n\r\n  return (\r\n    <p\r\n      data-slot=\"form-message\"\r\n      id={formMessageId}\r\n      className={cn(\"text-destructive text-sm\", className)}\r\n      {...props}\r\n    >\r\n      {body}\r\n    </p>\r\n  )\r\n}\r\n\r\nexport {\r\n  useFormField,\r\n  Form,\r\n  FormItem,\r\n  FormLabel,\r\n  FormControl,\r\n  FormDescription,\r\n  FormMessage,\r\n  FormField,\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;;AAEA;AAEA;AACA;AAUA;AAAA;AACA;;;AAhBA;;;;;;AAkBA,MAAM,OAAO,0PAAA,CAAA,eAAY;AASzB,MAAM,iCAAmB,CAAA,GAAA,sQAAA,CAAA,gBAAmB,AAAD,EACzC,CAAC;AAGH,MAAM,YAAY,CAGhB,EACA,GAAG,OACkC;IACrC,qBACE,sSAAC,iBAAiB,QAAQ;QAAC,OAAO;YAAE,MAAM,MAAM,IAAI;QAAC;kBACnD,cAAA,sSAAC,0PAAA,CAAA,aAAU;YAAE,GAAG,KAAK;;;;;;;;;;;AAG3B;KAXM;AAaN,MAAM,eAAe;;IACnB,MAAM,eAAe,CAAA,GAAA,sQAAA,CAAA,aAAgB,AAAD,EAAE;IACtC,MAAM,cAAc,CAAA,GAAA,sQAAA,CAAA,aAAgB,AAAD,EAAE;IACrC,MAAM,EAAE,aAAa,EAAE,GAAG,CAAA,GAAA,0PAAA,CAAA,iBAAc,AAAD;IACvC,MAAM,YAAY,CAAA,GAAA,0PAAA,CAAA,eAAY,AAAD,EAAE;QAAE,MAAM,aAAa,IAAI;IAAC;IACzD,MAAM,aAAa,cAAc,aAAa,IAAI,EAAE;IAEpD,IAAI,CAAC,cAAc;QACjB,MAAM,IAAI,MAAM;IAClB;IAEA,MAAM,EAAE,EAAE,EAAE,GAAG;IAEf,OAAO;QACL;QACA,MAAM,aAAa,IAAI;QACvB,YAAY,GAAG,GAAG,UAAU,CAAC;QAC7B,mBAAmB,GAAG,GAAG,sBAAsB,CAAC;QAChD,eAAe,GAAG,GAAG,kBAAkB,CAAC;QACxC,GAAG,UAAU;IACf;AACF;GArBM;;QAGsB,0PAAA,CAAA,iBAAc;QACtB,0PAAA,CAAA,eAAY;;;AAuBhC,MAAM,gCAAkB,CAAA,GAAA,sQAAA,CAAA,gBAAmB,AAAD,EACxC,CAAC;AAGH,SAAS,SAAS,EAAE,SAAS,EAAE,GAAG,OAAoC;;IACpE,MAAM,KAAK,CAAA,GAAA,sQAAA,CAAA,QAAW,AAAD;IAErB,qBACE,sSAAC,gBAAgB,QAAQ;QAAC,OAAO;YAAE;QAAG;kBACpC,cAAA,sSAAC;YACC,aAAU;YACV,WAAW,CAAA,GAAA,+HAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAC3B,GAAG,KAAK;;;;;;;;;;;AAIjB;IAZS;MAAA;AAcT,SAAS,UAAU,EACjB,SAAS,EACT,GAAG,OAC8C;;IACjD,MAAM,EAAE,KAAK,EAAE,UAAU,EAAE,GAAG;IAE9B,qBACE,sSAAC,6HAAA,CAAA,QAAK;QACJ,aAAU;QACV,cAAY,CAAC,CAAC;QACd,WAAW,CAAA,GAAA,+HAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QACpD,SAAS;QACR,GAAG,KAAK;;;;;;AAGf;IAfS;;QAIuB;;;MAJvB;AAiBT,SAAS,YAAY,EAAE,GAAG,OAA0C;;IAClE,MAAM,EAAE,KAAK,EAAE,UAAU,EAAE,iBAAiB,EAAE,aAAa,EAAE,GAAG;IAEhE,qBACE,sSAAC,uSAAA,CAAA,OAAI;QACH,aAAU;QACV,IAAI;QACJ,oBACE,CAAC,QACG,GAAG,mBAAmB,GACtB,GAAG,kBAAkB,CAAC,EAAE,eAAe;QAE7C,gBAAc,CAAC,CAAC;QACf,GAAG,KAAK;;;;;;AAGf;IAhBS;;QACyD;;;MADzD;AAkBT,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAkC;;IACzE,MAAM,EAAE,iBAAiB,EAAE,GAAG;IAE9B,qBACE,sSAAC;QACC,aAAU;QACV,IAAI;QACJ,WAAW,CAAA,GAAA,+HAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;IAXS;;QACuB;;;MADvB;AAaT,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAkC;;IACrE,MAAM,EAAE,KAAK,EAAE,aAAa,EAAE,GAAG;IACjC,MAAM,OAAO,QAAQ,OAAO,OAAO,WAAW,MAAM,MAAM,QAAQ;IAElE,IAAI,CAAC,MAAM;QACT,OAAO;IACT;IAEA,qBACE,sSAAC;QACC,aAAU;QACV,IAAI;QACJ,WAAW,CAAA,GAAA,+HAAA,CAAA,KAAE,AAAD,EAAE,4BAA4B;QACzC,GAAG,KAAK;kBAER;;;;;;AAGP;IAlBS;;QAC0B;;;MAD1B", "debugId": null}}, {"offset": {"line": 4606, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/bloomgoo/gold-exchange-platform/frontend/components/ui/select.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as SelectPrimitive from \"@radix-ui/react-select\"\r\nimport { Check<PERSON><PERSON>, ChevronDownIcon, ChevronUpIcon } from \"lucide-react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction Select({\r\n  ...props\r\n}: React.ComponentProps<typeof SelectPrimitive.Root>) {\r\n  return <SelectPrimitive.Root data-slot=\"select\" {...props} />\r\n}\r\n\r\nfunction SelectGroup({\r\n  ...props\r\n}: React.ComponentProps<typeof SelectPrimitive.Group>) {\r\n  return <SelectPrimitive.Group data-slot=\"select-group\" {...props} />\r\n}\r\n\r\nfunction SelectValue({\r\n  ...props\r\n}: React.ComponentProps<typeof SelectPrimitive.Value>) {\r\n  return <SelectPrimitive.Value data-slot=\"select-value\" {...props} />\r\n}\r\n\r\nfunction SelectTrigger({\r\n  className,\r\n  size = \"default\",\r\n  children,\r\n  ...props\r\n}: React.ComponentProps<typeof SelectPrimitive.Trigger> & {\r\n  size?: \"sm\" | \"default\"\r\n}) {\r\n  return (\r\n    <SelectPrimitive.Trigger\r\n      data-slot=\"select-trigger\"\r\n      data-size={size}\r\n      className={cn(\r\n        \"border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-9 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\r\n        className\r\n      )}\r\n      {...props}\r\n    >\r\n      {children}\r\n      <SelectPrimitive.Icon asChild>\r\n        <ChevronDownIcon className=\"size-4 opacity-50\" />\r\n      </SelectPrimitive.Icon>\r\n    </SelectPrimitive.Trigger>\r\n  )\r\n}\r\n\r\nfunction SelectContent({\r\n  className,\r\n  children,\r\n  position = \"popper\",\r\n  ...props\r\n}: React.ComponentProps<typeof SelectPrimitive.Content>) {\r\n  return (\r\n    <SelectPrimitive.Portal>\r\n      <SelectPrimitive.Content\r\n        data-slot=\"select-content\"\r\n        className={cn(\r\n          \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border shadow-md\",\r\n          position === \"popper\" &&\r\n            \"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1\",\r\n          className\r\n        )}\r\n        position={position}\r\n        {...props}\r\n      >\r\n        <SelectScrollUpButton />\r\n        <SelectPrimitive.Viewport\r\n          className={cn(\r\n            \"p-1\",\r\n            position === \"popper\" &&\r\n              \"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1\"\r\n          )}\r\n        >\r\n          {children}\r\n        </SelectPrimitive.Viewport>\r\n        <SelectScrollDownButton />\r\n      </SelectPrimitive.Content>\r\n    </SelectPrimitive.Portal>\r\n  )\r\n}\r\n\r\nfunction SelectLabel({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof SelectPrimitive.Label>) {\r\n  return (\r\n    <SelectPrimitive.Label\r\n      data-slot=\"select-label\"\r\n      className={cn(\"text-muted-foreground px-2 py-1.5 text-xs\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction SelectItem({\r\n  className,\r\n  children,\r\n  ...props\r\n}: React.ComponentProps<typeof SelectPrimitive.Item>) {\r\n  return (\r\n    <SelectPrimitive.Item\r\n      data-slot=\"select-item\"\r\n      className={cn(\r\n        \"focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2\",\r\n        className\r\n      )}\r\n      {...props}\r\n    >\r\n      <span className=\"absolute right-2 flex size-3.5 items-center justify-center\">\r\n        <SelectPrimitive.ItemIndicator>\r\n          <CheckIcon className=\"size-4\" />\r\n        </SelectPrimitive.ItemIndicator>\r\n      </span>\r\n      <SelectPrimitive.ItemText>{children}</SelectPrimitive.ItemText>\r\n    </SelectPrimitive.Item>\r\n  )\r\n}\r\n\r\nfunction SelectSeparator({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof SelectPrimitive.Separator>) {\r\n  return (\r\n    <SelectPrimitive.Separator\r\n      data-slot=\"select-separator\"\r\n      className={cn(\"bg-border pointer-events-none -mx-1 my-1 h-px\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction SelectScrollUpButton({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof SelectPrimitive.ScrollUpButton>) {\r\n  return (\r\n    <SelectPrimitive.ScrollUpButton\r\n      data-slot=\"select-scroll-up-button\"\r\n      className={cn(\r\n        \"flex cursor-default items-center justify-center py-1\",\r\n        className\r\n      )}\r\n      {...props}\r\n    >\r\n      <ChevronUpIcon className=\"size-4\" />\r\n    </SelectPrimitive.ScrollUpButton>\r\n  )\r\n}\r\n\r\nfunction SelectScrollDownButton({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof SelectPrimitive.ScrollDownButton>) {\r\n  return (\r\n    <SelectPrimitive.ScrollDownButton\r\n      data-slot=\"select-scroll-down-button\"\r\n      className={cn(\r\n        \"flex cursor-default items-center justify-center py-1\",\r\n        className\r\n      )}\r\n      {...props}\r\n    >\r\n      <ChevronDownIcon className=\"size-4\" />\r\n    </SelectPrimitive.ScrollDownButton>\r\n  )\r\n}\r\n\r\nexport {\r\n  Select,\r\n  SelectContent,\r\n  SelectGroup,\r\n  SelectItem,\r\n  SelectLabel,\r\n  SelectScrollDownButton,\r\n  SelectScrollUpButton,\r\n  SelectSeparator,\r\n  SelectTrigger,\r\n  SelectValue,\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAGA;AACA;AAAA;AAAA;AAEA;AAAA;AANA;;;;;AAQA,SAAS,OAAO,EACd,GAAG,OAC+C;IAClD,qBAAO,sSAAC,kRAAA,CAAA,OAAoB;QAAC,aAAU;QAAU,GAAG,KAAK;;;;;;AAC3D;KAJS;AAMT,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,sSAAC,kRAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;MAJS;AAMT,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,sSAAC,kRAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;MAJS;AAMT,SAAS,cAAc,EACrB,SAAS,EACT,OAAO,SAAS,EAChB,QAAQ,EACR,GAAG,OAGJ;IACC,qBACE,sSAAC,kRAAA,CAAA,UAAuB;QACtB,aAAU;QACV,aAAW;QACX,WAAW,CAAA,GAAA,+HAAA,CAAA,KAAE,AAAD,EACV,gzBACA;QAED,GAAG,KAAK;;YAER;0BACD,sSAAC,kRAAA,CAAA,OAAoB;gBAAC,OAAO;0BAC3B,cAAA,sSAAC,+SAAA,CAAA,kBAAe;oBAAC,WAAU;;;;;;;;;;;;;;;;;AAInC;MAxBS;AA0BT,SAAS,cAAc,EACrB,SAAS,EACT,QAAQ,EACR,WAAW,QAAQ,EACnB,GAAG,OACkD;IACrD,qBACE,sSAAC,kRAAA,CAAA,SAAsB;kBACrB,cAAA,sSAAC,kRAAA,CAAA,UAAuB;YACtB,aAAU;YACV,WAAW,CAAA,GAAA,+HAAA,CAAA,KAAE,AAAD,EACV,ijBACA,aAAa,YACX,mIACF;YAEF,UAAU;YACT,GAAG,KAAK;;8BAET,sSAAC;;;;;8BACD,sSAAC,kRAAA,CAAA,WAAwB;oBACvB,WAAW,CAAA,GAAA,+HAAA,CAAA,KAAE,AAAD,EACV,OACA,aAAa,YACX;8BAGH;;;;;;8BAEH,sSAAC;;;;;;;;;;;;;;;;AAIT;MAjCS;AAmCT,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,sSAAC,kRAAA,CAAA,QAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,+HAAA,CAAA,KAAE,AAAD,EAAE,6CAA6C;QAC1D,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,WAAW,EAClB,SAAS,EACT,QAAQ,EACR,GAAG,OAC+C;IAClD,qBACE,sSAAC,kRAAA,CAAA,OAAoB;QACnB,aAAU;QACV,WAAW,CAAA,GAAA,+HAAA,CAAA,KAAE,AAAD,EACV,6aACA;QAED,GAAG,KAAK;;0BAET,sSAAC;gBAAK,WAAU;0BACd,cAAA,sSAAC,kRAAA,CAAA,gBAA6B;8BAC5B,cAAA,sSAAC,+RAAA,CAAA,YAAS;wBAAC,WAAU;;;;;;;;;;;;;;;;0BAGzB,sSAAC,kRAAA,CAAA,WAAwB;0BAAE;;;;;;;;;;;;AAGjC;MAtBS;AAwBT,SAAS,gBAAgB,EACvB,SAAS,EACT,GAAG,OACoD;IACvD,qBACE,sSAAC,kRAAA,CAAA,YAAyB;QACxB,aAAU;QACV,WAAW,CAAA,GAAA,+HAAA,CAAA,KAAE,AAAD,EAAE,iDAAiD;QAC9D,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,qBAAqB,EAC5B,SAAS,EACT,GAAG,OACyD;IAC5D,qBACE,sSAAC,kRAAA,CAAA,iBAA8B;QAC7B,aAAU;QACV,WAAW,CAAA,GAAA,+HAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,sSAAC,2SAAA,CAAA,gBAAa;YAAC,WAAU;;;;;;;;;;;AAG/B;MAhBS;AAkBT,SAAS,uBAAuB,EAC9B,SAAS,EACT,GAAG,OAC2D;IAC9D,qBACE,sSAAC,kRAAA,CAAA,mBAAgC;QAC/B,aAAU;QACV,WAAW,CAAA,GAAA,+HAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,sSAAC,+SAAA,CAAA,kBAAe;YAAC,WAAU;;;;;;;;;;;AAGjC;MAhBS", "debugId": null}}, {"offset": {"line": 4856, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/bloomgoo/gold-exchange-platform/frontend/components/ui/textarea.tsx"], "sourcesContent": ["import * as React from \"react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction Textarea({ className, ...props }: React.ComponentProps<\"textarea\">) {\r\n  return (\r\n    <textarea\r\n      data-slot=\"textarea\"\r\n      className={cn(\r\n        \"border-input placeholder:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 flex field-sizing-content min-h-16 w-full rounded-md border bg-transparent px-3 py-2 text-base shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport { Textarea }\r\n"], "names": [], "mappings": ";;;;AAEA;AAAA;;;AAEA,SAAS,SAAS,EAAE,SAAS,EAAE,GAAG,OAAyC;IACzE,qBACE,sSAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,+HAAA,CAAA,KAAE,AAAD,EACV,ucACA;QAED,GAAG,KAAK;;;;;;AAGf;KAXS", "debugId": null}}, {"offset": {"line": 4888, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/bloomgoo/gold-exchange-platform/frontend/components/common/admin/ecom-products/ecom-product-search-select.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { useState, useEffect } from 'react';\r\nimport { Check, ChevronsUpDown, Loader2 } from 'lucide-react';\r\nimport { cn } from '@/lib/utils';\r\nimport { Button } from '@/components/ui/button';\r\nimport {\r\n  Command,\r\n  CommandEmpty,\r\n  CommandGroup,\r\n  CommandInput,\r\n  CommandItem,\r\n  CommandList,\r\n} from '@/components/ui/command';\r\nimport {\r\n  Popover,\r\n  PopoverContent,\r\n  PopoverTrigger,\r\n} from '@/components/ui/popover';\r\nimport { api } from '@/lib/api';\r\nimport { PaginationResponse } from '@/lib/response';\r\n\r\ninterface EcomProduct {\r\n  id: string;\r\n  productCode: string;\r\n  productName: string;\r\n  regularPrice: number;\r\n  salePrice?: number;\r\n  imageUrl?: string;\r\n}\r\n\r\ninterface EcomProductSearchSelectProps {\r\n  value: string;\r\n  onChange: (value: string) => void;\r\n  placeholder?: string;\r\n  disabled?: boolean;\r\n}\r\n\r\nexport function EcomProductSearchSelect({\r\n  value,\r\n  onChange,\r\n  placeholder = \"Chọn sản phẩm\",\r\n  disabled = false,\r\n}: EcomProductSearchSelectProps) {\r\n  const [open, setOpen] = useState(false);\r\n  const [searchTerm, setSearchTerm] = useState('');\r\n  const [products, setProducts] = useState<EcomProduct[]>([]);\r\n  const [loading, setLoading] = useState(false);\r\n  const [selectedProduct, setSelectedProduct] = useState<EcomProduct | null>(null);\r\n\r\n  // Fetch products based on search term\r\n  const fetchProducts = async (search: string) => {\r\n    setLoading(true);\r\n    try {\r\n      const response = await api.get<PaginationResponse<EcomProduct>>(\r\n        `ecom-products?page=1&limit=10&search=${encodeURIComponent(search)}`\r\n      );\r\n      setProducts(response.data || []);\r\n    } catch (error) {\r\n      console.error('Error fetching products:', error);\r\n      setProducts([]);\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  // Fetch product by ID when value changes\r\n  useEffect(() => {\r\n    if (value && !selectedProduct) {\r\n      const fetchProductById = async () => {\r\n        setLoading(true);\r\n        try {\r\n          const response = await api.get<EcomProduct>(`ecom-products/${value}`);\r\n          if (response) {\r\n            setSelectedProduct(response);\r\n          }\r\n        } catch (error) {\r\n          console.error('Error fetching product by ID:', error);\r\n        } finally {\r\n          setLoading(false);\r\n        }\r\n      };\r\n\r\n      fetchProductById();\r\n    } else if (!value) {\r\n      setSelectedProduct(null);\r\n    }\r\n  }, [value, selectedProduct]);\r\n\r\n  // Fetch products when search term changes\r\n  useEffect(() => {\r\n    const delayDebounceFn = setTimeout(() => {\r\n      if (open) {\r\n        fetchProducts(searchTerm);\r\n      }\r\n    }, 300);\r\n\r\n    return () => clearTimeout(delayDebounceFn);\r\n  }, [searchTerm, open]);\r\n\r\n  // Fetch products when popover opens\r\n  useEffect(() => {\r\n    if (open) {\r\n      fetchProducts(searchTerm);\r\n    }\r\n  }, [open]);\r\n\r\n  // Format price with VND\r\n  const formatPrice = (price: number) => {\r\n    return new Intl.NumberFormat('vi-VN', {\r\n      style: 'currency',\r\n      currency: 'VND',\r\n      maximumFractionDigits: 0\r\n    }).format(price);\r\n  };\r\n\r\n  return (\r\n    <Popover open={open && !disabled} onOpenChange={setOpen}>\r\n      <PopoverTrigger asChild>\r\n        <Button\r\n          variant=\"outline\"\r\n          role=\"combobox\"\r\n          aria-expanded={open}\r\n          className=\"w-full justify-between\"\r\n          disabled={disabled}\r\n        >\r\n          {loading ? (\r\n            <div className=\"flex items-center gap-2\">\r\n              <Loader2 className=\"h-4 w-4 animate-spin\" />\r\n              <span>Đang tải...</span>\r\n            </div>\r\n          ) : value && selectedProduct ? (\r\n            <div className=\"flex items-center gap-2 truncate\">\r\n              <span className=\"truncate\">{selectedProduct.productName}</span>\r\n              <span className=\"text-xs text-muted-foreground\">\r\n                ({formatPrice(selectedProduct.regularPrice)})\r\n              </span>\r\n            </div>\r\n          ) : (\r\n            placeholder\r\n          )}\r\n          <ChevronsUpDown className=\"ml-2 h-4 w-4 shrink-0 opacity-50\" />\r\n        </Button>\r\n      </PopoverTrigger>\r\n      <PopoverContent className=\"p-0 w-[300px]\">\r\n        <Command>\r\n          <CommandInput\r\n            placeholder=\"Tìm kiếm sản phẩm...\"\r\n            value={searchTerm}\r\n            onValueChange={setSearchTerm}\r\n          />\r\n          {loading && (\r\n            <div className=\"flex items-center justify-center py-6\">\r\n              <Loader2 className=\"h-6 w-6 animate-spin text-primary\" />\r\n            </div>\r\n          )}\r\n          {!loading && (\r\n            <CommandList>\r\n              <CommandEmpty>Không tìm thấy sản phẩm</CommandEmpty>\r\n              <CommandGroup>\r\n                {products.map((product) => (\r\n                  <CommandItem\r\n                    key={product.id}\r\n                    value={product.id}\r\n                    onSelect={(currentValue) => {\r\n                      onChange(currentValue === value ? '' : currentValue);\r\n                      setSelectedProduct(product);\r\n                      setOpen(false);\r\n                    }}\r\n                  >\r\n                    <Check\r\n                      className={cn(\r\n                        \"mr-2 h-4 w-4\",\r\n                        value === product.id ? \"opacity-100\" : \"opacity-0\"\r\n                      )}\r\n                    />\r\n                    <div className=\"flex flex-col\">\r\n                      <span>{product.productName}</span>\r\n                      <span className=\"text-xs text-muted-foreground\">\r\n                        {product.productCode} - {formatPrice(product.regularPrice)}\r\n                      </span>\r\n                    </div>\r\n                  </CommandItem>\r\n                ))}\r\n              </CommandGroup>\r\n            </CommandList>\r\n          )}\r\n        </Command>\r\n      </PopoverContent>\r\n    </Popover>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AACA;AAAA;AACA;AACA;AAQA;AAKA;;;AAnBA;;;;;;;;AAsCO,SAAS,wBAAwB,EACtC,KAAK,EACL,QAAQ,EACR,cAAc,eAAe,EAC7B,WAAW,KAAK,EACa;;IAC7B,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,sQAAA,CAAA,WAAQ,AAAD,EAAE;IACjC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,sQAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,sQAAA,CAAA,WAAQ,AAAD,EAAiB,EAAE;IAC1D,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,sQAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,sQAAA,CAAA,WAAQ,AAAD,EAAsB;IAE3E,sCAAsC;IACtC,MAAM,gBAAgB,OAAO;QAC3B,WAAW;QACX,IAAI;YACF,MAAM,WAAW,MAAM,6GAAA,CAAA,MAAG,CAAC,GAAG,CAC5B,CAAC,qCAAqC,EAAE,mBAAmB,SAAS;YAEtE,YAAY,SAAS,IAAI,IAAI,EAAE;QACjC,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4BAA4B;YAC1C,YAAY,EAAE;QAChB,SAAU;YACR,WAAW;QACb;IACF;IAEA,yCAAyC;IACzC,CAAA,GAAA,sQAAA,CAAA,YAAS,AAAD;6CAAE;YACR,IAAI,SAAS,CAAC,iBAAiB;gBAC7B,MAAM;0EAAmB;wBACvB,WAAW;wBACX,IAAI;4BACF,MAAM,WAAW,MAAM,6GAAA,CAAA,MAAG,CAAC,GAAG,CAAc,CAAC,cAAc,EAAE,OAAO;4BACpE,IAAI,UAAU;gCACZ,mBAAmB;4BACrB;wBACF,EAAE,OAAO,OAAO;4BACd,QAAQ,KAAK,CAAC,iCAAiC;wBACjD,SAAU;4BACR,WAAW;wBACb;oBACF;;gBAEA;YACF,OAAO,IAAI,CAAC,OAAO;gBACjB,mBAAmB;YACrB;QACF;4CAAG;QAAC;QAAO;KAAgB;IAE3B,0CAA0C;IAC1C,CAAA,GAAA,sQAAA,CAAA,YAAS,AAAD;6CAAE;YACR,MAAM,kBAAkB;qEAAW;oBACjC,IAAI,MAAM;wBACR,cAAc;oBAChB;gBACF;oEAAG;YAEH;qDAAO,IAAM,aAAa;;QAC5B;4CAAG;QAAC;QAAY;KAAK;IAErB,oCAAoC;IACpC,CAAA,GAAA,sQAAA,CAAA,YAAS,AAAD;6CAAE;YACR,IAAI,MAAM;gBACR,cAAc;YAChB;QACF;4CAAG;QAAC;KAAK;IAET,wBAAwB;IACxB,MAAM,cAAc,CAAC;QACnB,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;YACpC,OAAO;YACP,UAAU;YACV,uBAAuB;QACzB,GAAG,MAAM,CAAC;IACZ;IAEA,qBACE,sSAAC,+HAAA,CAAA,UAAO;QAAC,MAAM,QAAQ,CAAC;QAAU,cAAc;;0BAC9C,sSAAC,+HAAA,CAAA,iBAAc;gBAAC,OAAO;0BACrB,cAAA,sSAAC,8HAAA,CAAA,SAAM;oBACL,SAAQ;oBACR,MAAK;oBACL,iBAAe;oBACf,WAAU;oBACV,UAAU;;wBAET,wBACC,sSAAC;4BAAI,WAAU;;8CACb,sSAAC,wSAAA,CAAA,UAAO;oCAAC,WAAU;;;;;;8CACnB,sSAAC;8CAAK;;;;;;;;;;;mCAEN,SAAS,gCACX,sSAAC;4BAAI,WAAU;;8CACb,sSAAC;oCAAK,WAAU;8CAAY,gBAAgB,WAAW;;;;;;8CACvD,sSAAC;oCAAK,WAAU;;wCAAgC;wCAC5C,YAAY,gBAAgB,YAAY;wCAAE;;;;;;;;;;;;mCAIhD;sCAEF,sSAAC,qTAAA,CAAA,iBAAc;4BAAC,WAAU;;;;;;;;;;;;;;;;;0BAG9B,sSAAC,+HAAA,CAAA,iBAAc;gBAAC,WAAU;0BACxB,cAAA,sSAAC,+HAAA,CAAA,UAAO;;sCACN,sSAAC,+HAAA,CAAA,eAAY;4BACX,aAAY;4BACZ,OAAO;4BACP,eAAe;;;;;;wBAEhB,yBACC,sSAAC;4BAAI,WAAU;sCACb,cAAA,sSAAC,wSAAA,CAAA,UAAO;gCAAC,WAAU;;;;;;;;;;;wBAGtB,CAAC,yBACA,sSAAC,+HAAA,CAAA,cAAW;;8CACV,sSAAC,+HAAA,CAAA,eAAY;8CAAC;;;;;;8CACd,sSAAC,+HAAA,CAAA,eAAY;8CACV,SAAS,GAAG,CAAC,CAAC,wBACb,sSAAC,+HAAA,CAAA,cAAW;4CAEV,OAAO,QAAQ,EAAE;4CACjB,UAAU,CAAC;gDACT,SAAS,iBAAiB,QAAQ,KAAK;gDACvC,mBAAmB;gDACnB,QAAQ;4CACV;;8DAEA,sSAAC,2RAAA,CAAA,QAAK;oDACJ,WAAW,CAAA,GAAA,+HAAA,CAAA,KAAE,AAAD,EACV,gBACA,UAAU,QAAQ,EAAE,GAAG,gBAAgB;;;;;;8DAG3C,sSAAC;oDAAI,WAAU;;sEACb,sSAAC;sEAAM,QAAQ,WAAW;;;;;;sEAC1B,sSAAC;4DAAK,WAAU;;gEACb,QAAQ,WAAW;gEAAC;gEAAI,YAAY,QAAQ,YAAY;;;;;;;;;;;;;;2CAjBxD,QAAQ,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA6BnC;GAzJgB;KAAA", "debugId": null}}, {"offset": {"line": 5206, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/bloomgoo/gold-exchange-platform/frontend/hooks/use-debounce.ts"], "sourcesContent": ["import { useState, useEffect } from 'react';\r\n\r\n/**\r\n * Hook để debounce giá trị, hữu ích cho các tr<PERSON><PERSON><PERSON> hợp như tìm kiếm\r\n * @param value Giá trị cần debounce\r\n * @param delay Thời gian delay tính bằng milliseconds\r\n * @returns Giá trị đã được debounce\r\n */\r\nexport function useDebounce<T>(value: T, delay: number): T {\r\n  const [debouncedValue, setDebouncedValue] = useState<T>(value);\r\n\r\n  useEffect(() => {\r\n    // Tạo timeout để cập nhật giá trị debounced sau delay\r\n    const timer = setTimeout(() => {\r\n      setDebouncedValue(value);\r\n    }, delay);\r\n\r\n    // Xóa timeout nếu giá trị thay đổi hoặc component unmount\r\n    return () => {\r\n      clearTimeout(timer);\r\n    };\r\n  }, [value, delay]);\r\n\r\n  return debouncedValue;\r\n}\r\n"], "names": [], "mappings": ";;;AAAA;;;AAQO,SAAS,YAAe,KAAQ,EAAE,KAAa;;IACpD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,sQAAA,CAAA,WAAQ,AAAD,EAAK;IAExD,CAAA,GAAA,sQAAA,CAAA,YAAS,AAAD;iCAAE;YACR,sDAAsD;YACtD,MAAM,QAAQ;+CAAW;oBACvB,kBAAkB;gBACpB;8CAAG;YAEH,0DAA0D;YAC1D;yCAAO;oBACL,aAAa;gBACf;;QACF;gCAAG;QAAC;QAAO;KAAM;IAEjB,OAAO;AACT;GAhBgB", "debugId": null}}, {"offset": {"line": 5246, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/bloomgoo/gold-exchange-platform/frontend/components/ui/searchable-select.tsx"], "sourcesContent": ["import { useState, useEffect, useRef } from 'react';\r\nimport { useDebounce } from '@/hooks/use-debounce';\r\nimport { Command, CommandEmpty, CommandGroup, CommandInput, CommandItem } from '@/components/ui/command';\r\nimport { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';\r\nimport { Button } from '@/components/ui/button';\r\nimport { Check, ChevronsUpDown, Loader2 } from 'lucide-react';\r\nimport { cn } from '@/lib/utils';\r\n\r\nexport interface SearchableSelectOption {\r\n  id: string;\r\n  [key: string]: any;\r\n}\r\n\r\nexport interface SearchableSelectProps {\r\n  /**\r\n   * Giá trị hiện tại của select (id của option được chọn)\r\n   */\r\n  value?: string;\r\n\r\n  /**\r\n   * Callback khi giá trị thay đổi\r\n   */\r\n  onChange: (value: string) => void;\r\n\r\n  /**\r\n   * Placeholder khi không có giá trị nào được chọn\r\n   */\r\n  placeholder?: string;\r\n\r\n  /**\r\n   * Placeholder cho ô tìm kiếm\r\n   */\r\n  searchPlaceholder?: string;\r\n\r\n  /**\r\n   * Hàm để tải dữ liệu options dựa trên từ khóa tìm kiếm\r\n   * @param search Từ khóa tìm kiếm\r\n   * @returns Promise trả về mảng các option\r\n   */\r\n  fetchOptions: (search: string) => Promise<SearchableSelectOption[]>;\r\n\r\n  /**\r\n   * Hàm để render text hiển thị cho mỗi option\r\n   * @param option Option cần render\r\n   * @returns Text hiển thị\r\n   */\r\n  renderOption: (option: SearchableSelectOption | undefined) => React.ReactNode;\r\n\r\n  /**\r\n   * Có disable select hay không\r\n   */\r\n  disabled?: boolean;\r\n\r\n  /**\r\n   * Thời gian debounce cho tìm kiếm (ms)\r\n   */\r\n  debounceTime?: number;\r\n\r\n  /**\r\n   * CSS class bổ sung cho button\r\n   */\r\n  className?: string;\r\n\r\n  /**\r\n   * Chiều rộng của popover content\r\n   */\r\n  popoverWidth?: string;\r\n\r\n  /**\r\n   * Chiều cao tối đa của danh sách options\r\n   */\r\n  maxHeight?: string;\r\n\r\n  /**\r\n   * Có tải dữ liệu ngay khi component mount không\r\n   */\r\n  loadOnMount?: boolean;\r\n}\r\n\r\n/**\r\n * Component Select với khả năng tìm kiếm và tải dữ liệu theo yêu cầu\r\n */\r\nexport function SearchableSelect({\r\n  value,\r\n  onChange,\r\n  placeholder = 'Chọn một giá trị',\r\n  searchPlaceholder = 'Tìm kiếm...',\r\n  fetchOptions,\r\n  renderOption,\r\n  disabled = false,\r\n  debounceTime = 300,\r\n  className = '',\r\n  popoverWidth = 'w-[var(--radix-popover-trigger-width)]',\r\n  maxHeight = 'max-h-60',\r\n  loadOnMount = false,\r\n}: SearchableSelectProps) {\r\n  const [open, setOpen] = useState(false);\r\n  const [options, setOptions] = useState<SearchableSelectOption[]>([]);\r\n  const [loading, setLoading] = useState(false);\r\n  const [search, setSearch] = useState('');\r\n  const [selectedOption, setSelectedOption] = useState<SearchableSelectOption | undefined>(undefined);\r\n  const debouncedSearch = useDebounce(search, debounceTime);\r\n  const initialLoadDone = useRef(false);\r\n\r\n  // Tải dữ liệu khi mở dropdown hoặc khi tìm kiếm thay đổi\r\n  useEffect(() => {\r\n    const loadOptions = async () => {\r\n      // Chỉ tải khi mở dropdown hoặc có tìm kiếm\r\n      if (open && fetchOptions) {\r\n        setLoading(true);\r\n        try {\r\n          const data = await fetchOptions(debouncedSearch);\r\n          setOptions(data);\r\n\r\n          // Nếu có value nhưng chưa có selectedOption, tìm option tương ứng\r\n          if (value && !selectedOption) {\r\n            const found = data.find(option => option.id === value);\r\n            if (found) {\r\n              setSelectedOption(found);\r\n            }\r\n          }\r\n        } catch (error) {\r\n          console.error('Error fetching options:', error);\r\n        } finally {\r\n          setLoading(false);\r\n        }\r\n      }\r\n    };\r\n\r\n    // Chỉ tải khi mở dropdown hoặc có tìm kiếm\r\n    if (open || debouncedSearch) {\r\n      loadOptions();\r\n    }\r\n  }, [open, debouncedSearch, value, selectedOption]); // Loại bỏ fetchOptions khỏi dependencies\r\n\r\n  // Tải dữ liệu ban đầu để hiển thị giá trị đã chọn (chỉ chạy 1 lần)\r\n  useEffect(() => {\r\n    // Chỉ tải dữ liệu ban đầu nếu có value và loadOnMount=true\r\n    if (loadOnMount && value && !selectedOption && fetchOptions && !initialLoadDone.current) {\r\n      initialLoadDone.current = true; // Đánh dấu đã tải\r\n\r\n      const loadInitialOption = async () => {\r\n        setLoading(true);\r\n        try {\r\n          const data = await fetchOptions('');\r\n          const found = data.find(option => option.id === value);\r\n          if (found) {\r\n            setSelectedOption(found);\r\n          }\r\n        } catch (error) {\r\n          console.error('Error fetching initial option:', error);\r\n        } finally {\r\n          setLoading(false);\r\n        }\r\n      };\r\n\r\n      loadInitialOption();\r\n    }\r\n  }, [value, selectedOption, loadOnMount]); // Loại bỏ fetchOptions khỏi dependencies\r\n\r\n  return (\r\n\r\n      <Popover open={open} onOpenChange={setOpen}>\r\n        <PopoverTrigger asChild>\r\n          <Button\r\n            variant=\"outline\"\r\n            role=\"combobox\"\r\n            aria-expanded={open}\r\n            disabled={disabled}\r\n            className={cn(\r\n              \"w-full justify-between overflow-hidden text-ellipsis\",\r\n              className\r\n            )}\r\n          >\r\n            <div className=\"truncate text-left mr-2 flex-1\">\r\n              {value && selectedOption\r\n                ? renderOption(selectedOption)\r\n                : placeholder}\r\n            </div>\r\n            {loading && !open ? (\r\n              <Loader2 className=\"ml-2 h-4 w-4 shrink-0 flex-none animate-spin\" />\r\n            ) : (\r\n              <ChevronsUpDown className=\"ml-2 h-4 w-4 shrink-0 flex-none opacity-50\" />\r\n            )}\r\n          </Button>\r\n        </PopoverTrigger>\r\n        <PopoverContent className={cn(\"p-0\", popoverWidth)} align=\"start\">\r\n          <Command className=\"w-full\">\r\n            <CommandInput\r\n              placeholder={searchPlaceholder}\r\n              value={search}\r\n              onValueChange={setSearch}\r\n              className=\"w-full\"\r\n            />\r\n            {loading ? (\r\n              <div className=\"py-6 text-center w-full\">\r\n                <Loader2 className=\"h-6 w-6 animate-spin mx-auto\" />\r\n                <p className=\"text-sm text-muted-foreground mt-2\">Đang tải...</p>\r\n              </div>\r\n            ) : (\r\n              <>\r\n                <CommandEmpty className=\"py-6 text-center w-full\">\r\n                  <p className=\"text-sm text-muted-foreground\">Không tìm thấy kết quả</p>\r\n                </CommandEmpty>\r\n                <CommandGroup className={cn(\"overflow-auto w-full\", maxHeight)}>\r\n                  {options && options.length > 0 ? options.map(option => (\r\n                    <CommandItem\r\n                      key={option.id}\r\n                      value={option.id}\r\n                      onSelect={() => {\r\n                        onChange(option.id);\r\n                        setSelectedOption(option);\r\n                        setOpen(false);\r\n                      }}\r\n                    >\r\n                      <Check\r\n                        className={cn(\r\n                          \"mr-2 h-4 w-4\",\r\n                          value === option.id ? \"opacity-100\" : \"opacity-0\"\r\n                        )}\r\n                      />\r\n                      {renderOption(option)}\r\n                    </CommandItem>\r\n                  )) : null}\r\n                </CommandGroup>\r\n              </>\r\n            )}\r\n          </Command>\r\n        </PopoverContent>\r\n    </Popover>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AACA;AAAA;;;;;;;;;;AA4EO,SAAS,iBAAiB,EAC/B,KAAK,EACL,QAAQ,EACR,cAAc,kBAAkB,EAChC,oBAAoB,aAAa,EACjC,YAAY,EACZ,YAAY,EACZ,WAAW,KAAK,EAChB,eAAe,GAAG,EAClB,YAAY,EAAE,EACd,eAAe,wCAAwC,EACvD,YAAY,UAAU,EACtB,cAAc,KAAK,EACG;;IACtB,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,sQAAA,CAAA,WAAQ,AAAD,EAAE;IACjC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,sQAAA,CAAA,WAAQ,AAAD,EAA4B,EAAE;IACnE,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,sQAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,sQAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,sQAAA,CAAA,WAAQ,AAAD,EAAsC;IACzF,MAAM,kBAAkB,CAAA,GAAA,2HAAA,CAAA,cAAW,AAAD,EAAE,QAAQ;IAC5C,MAAM,kBAAkB,CAAA,GAAA,sQAAA,CAAA,SAAM,AAAD,EAAE;IAE/B,yDAAyD;IACzD,CAAA,GAAA,sQAAA,CAAA,YAAS,AAAD;sCAAE;YACR,MAAM;0DAAc;oBAClB,2CAA2C;oBAC3C,IAAI,QAAQ,cAAc;wBACxB,WAAW;wBACX,IAAI;4BACF,MAAM,OAAO,MAAM,aAAa;4BAChC,WAAW;4BAEX,kEAAkE;4BAClE,IAAI,SAAS,CAAC,gBAAgB;gCAC5B,MAAM,QAAQ,KAAK,IAAI;oFAAC,CAAA,SAAU,OAAO,EAAE,KAAK;;gCAChD,IAAI,OAAO;oCACT,kBAAkB;gCACpB;4BACF;wBACF,EAAE,OAAO,OAAO;4BACd,QAAQ,KAAK,CAAC,2BAA2B;wBAC3C,SAAU;4BACR,WAAW;wBACb;oBACF;gBACF;;YAEA,2CAA2C;YAC3C,IAAI,QAAQ,iBAAiB;gBAC3B;YACF;QACF;qCAAG;QAAC;QAAM;QAAiB;QAAO;KAAe,GAAG,yCAAyC;IAE7F,mEAAmE;IACnE,CAAA,GAAA,sQAAA,CAAA,YAAS,AAAD;sCAAE;YACR,2DAA2D;YAC3D,IAAI,eAAe,SAAS,CAAC,kBAAkB,gBAAgB,CAAC,gBAAgB,OAAO,EAAE;gBACvF,gBAAgB,OAAO,GAAG,MAAM,kBAAkB;gBAElD,MAAM;oEAAoB;wBACxB,WAAW;wBACX,IAAI;4BACF,MAAM,OAAO,MAAM,aAAa;4BAChC,MAAM,QAAQ,KAAK,IAAI;sFAAC,CAAA,SAAU,OAAO,EAAE,KAAK;;4BAChD,IAAI,OAAO;gCACT,kBAAkB;4BACpB;wBACF,EAAE,OAAO,OAAO;4BACd,QAAQ,KAAK,CAAC,kCAAkC;wBAClD,SAAU;4BACR,WAAW;wBACb;oBACF;;gBAEA;YACF;QACF;qCAAG;QAAC;QAAO;QAAgB;KAAY,GAAG,yCAAyC;IAEnF,qBAEI,sSAAC,+HAAA,CAAA,UAAO;QAAC,MAAM;QAAM,cAAc;;0BACjC,sSAAC,+HAAA,CAAA,iBAAc;gBAAC,OAAO;0BACrB,cAAA,sSAAC,8HAAA,CAAA,SAAM;oBACL,SAAQ;oBACR,MAAK;oBACL,iBAAe;oBACf,UAAU;oBACV,WAAW,CAAA,GAAA,+HAAA,CAAA,KAAE,AAAD,EACV,wDACA;;sCAGF,sSAAC;4BAAI,WAAU;sCACZ,SAAS,iBACN,aAAa,kBACb;;;;;;wBAEL,WAAW,CAAC,qBACX,sSAAC,wSAAA,CAAA,UAAO;4BAAC,WAAU;;;;;iDAEnB,sSAAC,qTAAA,CAAA,iBAAc;4BAAC,WAAU;;;;;;;;;;;;;;;;;0BAIhC,sSAAC,+HAAA,CAAA,iBAAc;gBAAC,WAAW,CAAA,GAAA,+HAAA,CAAA,KAAE,AAAD,EAAE,OAAO;gBAAe,OAAM;0BACxD,cAAA,sSAAC,+HAAA,CAAA,UAAO;oBAAC,WAAU;;sCACjB,sSAAC,+HAAA,CAAA,eAAY;4BACX,aAAa;4BACb,OAAO;4BACP,eAAe;4BACf,WAAU;;;;;;wBAEX,wBACC,sSAAC;4BAAI,WAAU;;8CACb,sSAAC,wSAAA,CAAA,UAAO;oCAAC,WAAU;;;;;;8CACnB,sSAAC;oCAAE,WAAU;8CAAqC;;;;;;;;;;;iDAGpD;;8CACE,sSAAC,+HAAA,CAAA,eAAY;oCAAC,WAAU;8CACtB,cAAA,sSAAC;wCAAE,WAAU;kDAAgC;;;;;;;;;;;8CAE/C,sSAAC,+HAAA,CAAA,eAAY;oCAAC,WAAW,CAAA,GAAA,+HAAA,CAAA,KAAE,AAAD,EAAE,wBAAwB;8CACjD,WAAW,QAAQ,MAAM,GAAG,IAAI,QAAQ,GAAG,CAAC,CAAA,uBAC3C,sSAAC,+HAAA,CAAA,cAAW;4CAEV,OAAO,OAAO,EAAE;4CAChB,UAAU;gDACR,SAAS,OAAO,EAAE;gDAClB,kBAAkB;gDAClB,QAAQ;4CACV;;8DAEA,sSAAC,2RAAA,CAAA,QAAK;oDACJ,WAAW,CAAA,GAAA,+HAAA,CAAA,KAAE,AAAD,EACV,gBACA,UAAU,OAAO,EAAE,GAAG,gBAAgB;;;;;;gDAGzC,aAAa;;2CAdT,OAAO,EAAE;;;;oDAgBb;;;;;;;;;;;;;;;;;;;;;;;;;AAQvB;GArJgB;;QAmBU,2HAAA,CAAA,cAAW;;;KAnBrB", "debugId": null}}, {"offset": {"line": 5516, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/bloomgoo/gold-exchange-platform/frontend/components/ui/alert-dialog.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as AlertDialogPrimitive from \"@radix-ui/react-alert-dialog\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\nimport { buttonVariants } from \"@/components/ui/button\"\r\n\r\nfunction AlertDialog({\r\n  ...props\r\n}: React.ComponentProps<typeof AlertDialogPrimitive.Root>) {\r\n  return <AlertDialogPrimitive.Root data-slot=\"alert-dialog\" {...props} />\r\n}\r\n\r\nfunction AlertDialogTrigger({\r\n  ...props\r\n}: React.ComponentProps<typeof AlertDialogPrimitive.Trigger>) {\r\n  return (\r\n    <AlertDialogPrimitive.Trigger data-slot=\"alert-dialog-trigger\" {...props} />\r\n  )\r\n}\r\n\r\nfunction AlertDialogPortal({\r\n  ...props\r\n}: React.ComponentProps<typeof AlertDialogPrimitive.Portal>) {\r\n  return (\r\n    <AlertDialogPrimitive.Portal data-slot=\"alert-dialog-portal\" {...props} />\r\n  )\r\n}\r\n\r\nfunction AlertDialogOverlay({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof AlertDialogPrimitive.Overlay>) {\r\n  return (\r\n    <AlertDialogPrimitive.Overlay\r\n      data-slot=\"alert-dialog-overlay\"\r\n      className={cn(\r\n        \"data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction AlertDialogContent({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof AlertDialogPrimitive.Content>) {\r\n  return (\r\n    <AlertDialogPortal>\r\n      <AlertDialogOverlay />\r\n      <AlertDialogPrimitive.Content\r\n        data-slot=\"alert-dialog-content\"\r\n        className={cn(\r\n          \"bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg\",\r\n          className\r\n        )}\r\n        {...props}\r\n      />\r\n    </AlertDialogPortal>\r\n  )\r\n}\r\n\r\nfunction AlertDialogHeader({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"alert-dialog-header\"\r\n      className={cn(\"flex flex-col gap-2 text-center sm:text-left\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction AlertDialogFooter({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"alert-dialog-footer\"\r\n      className={cn(\r\n        \"flex flex-col-reverse gap-2 sm:flex-row sm:justify-end\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction AlertDialogTitle({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof AlertDialogPrimitive.Title>) {\r\n  return (\r\n    <AlertDialogPrimitive.Title\r\n      data-slot=\"alert-dialog-title\"\r\n      className={cn(\"text-lg font-semibold\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction AlertDialogDescription({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof AlertDialogPrimitive.Description>) {\r\n  return (\r\n    <AlertDialogPrimitive.Description\r\n      data-slot=\"alert-dialog-description\"\r\n      className={cn(\"text-muted-foreground text-sm\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction AlertDialogAction({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof AlertDialogPrimitive.Action>) {\r\n  return (\r\n    <AlertDialogPrimitive.Action\r\n      className={cn(buttonVariants(), className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction AlertDialogCancel({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof AlertDialogPrimitive.Cancel>) {\r\n  return (\r\n    <AlertDialogPrimitive.Cancel\r\n      className={cn(buttonVariants({ variant: \"outline\" }), className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport {\r\n  AlertDialog,\r\n  AlertDialogPortal,\r\n  AlertDialogOverlay,\r\n  AlertDialogTrigger,\r\n  AlertDialogContent,\r\n  AlertDialogHeader,\r\n  AlertDialogFooter,\r\n  AlertDialogTitle,\r\n  AlertDialogDescription,\r\n  AlertDialogAction,\r\n  AlertDialogCancel,\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;;AAGA;AAEA;AAAA;AACA;AANA;;;;;AAQA,SAAS,YAAY,EACnB,GAAG,OACoD;IACvD,qBAAO,sSAAC,qRAAA,CAAA,OAAyB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AACtE;KAJS;AAMT,SAAS,mBAAmB,EAC1B,GAAG,OACuD;IAC1D,qBACE,sSAAC,qRAAA,CAAA,UAA4B;QAAC,aAAU;QAAwB,GAAG,KAAK;;;;;;AAE5E;MANS;AAQT,SAAS,kBAAkB,EACzB,GAAG,OACsD;IACzD,qBACE,sSAAC,qRAAA,CAAA,SAA2B;QAAC,aAAU;QAAuB,GAAG,KAAK;;;;;;AAE1E;MANS;AAQT,SAAS,mBAAmB,EAC1B,SAAS,EACT,GAAG,OACuD;IAC1D,qBACE,sSAAC,qRAAA,CAAA,UAA4B;QAC3B,aAAU;QACV,WAAW,CAAA,GAAA,+HAAA,CAAA,KAAE,AAAD,EACV,0JACA;QAED,GAAG,KAAK;;;;;;AAGf;MAdS;AAgBT,SAAS,mBAAmB,EAC1B,SAAS,EACT,GAAG,OACuD;IAC1D,qBACE,sSAAC;;0BACC,sSAAC;;;;;0BACD,sSAAC,qRAAA,CAAA,UAA4B;gBAC3B,aAAU;gBACV,WAAW,CAAA,GAAA,+HAAA,CAAA,KAAE,AAAD,EACV,+WACA;gBAED,GAAG,KAAK;;;;;;;;;;;;AAIjB;MAjBS;AAmBT,SAAS,kBAAkB,EACzB,SAAS,EACT,GAAG,OACyB;IAC5B,qBACE,sSAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,+HAAA,CAAA,KAAE,AAAD,EAAE,gDAAgD;QAC7D,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,kBAAkB,EACzB,SAAS,EACT,GAAG,OACyB;IAC5B,qBACE,sSAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,+HAAA,CAAA,KAAE,AAAD,EACV,0DACA;QAED,GAAG,KAAK;;;;;;AAGf;MAdS;AAgBT,SAAS,iBAAiB,EACxB,SAAS,EACT,GAAG,OACqD;IACxD,qBACE,sSAAC,qRAAA,CAAA,QAA0B;QACzB,aAAU;QACV,WAAW,CAAA,GAAA,+HAAA,CAAA,KAAE,AAAD,EAAE,yBAAyB;QACtC,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,uBAAuB,EAC9B,SAAS,EACT,GAAG,OAC2D;IAC9D,qBACE,sSAAC,qRAAA,CAAA,cAAgC;QAC/B,aAAU;QACV,WAAW,CAAA,GAAA,+HAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,kBAAkB,EACzB,SAAS,EACT,GAAG,OACsD;IACzD,qBACE,sSAAC,qRAAA,CAAA,SAA2B;QAC1B,WAAW,CAAA,GAAA,+HAAA,CAAA,KAAE,AAAD,EAAE,CAAA,GAAA,8HAAA,CAAA,iBAAc,AAAD,KAAK;QAC/B,GAAG,KAAK;;;;;;AAGf;MAVS;AAYT,SAAS,kBAAkB,EACzB,SAAS,EACT,GAAG,OACsD;IACzD,qBACE,sSAAC,qRAAA,CAAA,SAA2B;QAC1B,WAAW,CAAA,GAAA,+HAAA,CAAA,KAAE,AAAD,EAAE,CAAA,GAAA,8HAAA,CAAA,iBAAc,AAAD,EAAE;YAAE,SAAS;QAAU,IAAI;QACrD,GAAG,KAAK;;;;;;AAGf;OAVS", "debugId": null}}, {"offset": {"line": 5703, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/bloomgoo/gold-exchange-platform/frontend/components/common/admin/ecom-orders/ecom-order-form-modal.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { useState, useEffect } from 'react';\r\nimport { useForm } from 'react-hook-form';\r\nimport { zodResolver } from '@hookform/resolvers/zod';\r\nimport { toast } from 'sonner';\r\nimport { z } from 'zod';\r\nimport {\r\n  Dialog,\r\n  DialogContent,\r\n  DialogDescription,\r\n  DialogFooter,\r\n  DialogHeader,\r\n  DialogTitle,\r\n} from '@/components/ui/dialog';\r\nimport {\r\n  Form,\r\n  FormControl,\r\n  FormField,\r\n  FormItem,\r\n  FormLabel,\r\n  FormMessage,\r\n  FormDescription,\r\n} from '@/components/ui/form';\r\nimport {\r\n  Select,\r\n  SelectContent,\r\n  SelectItem,\r\n  SelectTrigger,\r\n  SelectValue,\r\n} from '@/components/ui/select';\r\nimport { Input } from '@/components/ui/input';\r\nimport { Button } from '@/components/ui/button';\r\nimport { Textarea } from '@/components/ui/textarea';\r\nimport { Loader2, X, User, ShoppingCart } from 'lucide-react';\r\nimport { api } from '@/lib/api';\r\nimport { EcomOrder, OrderStatus, PaymentStatus } from './type/ecom-orders';\r\nimport { EcomProductSearchSelect } from '../ecom-products/ecom-product-search-select';\r\nimport { SearchableSelect, SearchableSelectOption } from '@/components/ui/searchable-select';\r\nimport { PaginationResponse } from '@/lib/response';\r\nimport { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle } from '@/components/ui/alert-dialog';\r\n\r\n// Define interfaces for product information\r\ninterface ProductInfo {\r\n  id: string;\r\n  productCode: string;\r\n  productName: string;\r\n  regularPrice: number;\r\n  salePrice?: number;\r\n  imageUrl?: string;\r\n  stockQuantity?: number;\r\n}\r\n\r\n// Define form schema\r\nconst formSchema = z.object({\r\n  userId: z.string().min(1, \"Vui lòng chọn khách hàng\"),\r\n  status: z.nativeEnum(OrderStatus).default(OrderStatus.PENDING),\r\n  paymentStatus: z.nativeEnum(PaymentStatus).default(PaymentStatus.UNPAID),\r\n  notes: z.string().optional(),\r\n  orderDetails: z.array(z.object({\r\n    productId: z.string().min(1, \"Vui lòng chọn sản phẩm\"),\r\n    quantity: z.coerce.number().min(1, \"Số lượng phải lớn hơn 0\"),\r\n    price: z.coerce.number().min(0, \"Giá không được âm\"),\r\n    productInfo: z.any().optional() // Store product information for display\r\n  })).min(1, \"Vui lòng thêm ít nhất một sản phẩm\")\r\n});\r\n\r\ntype FormValues = z.infer<typeof formSchema>;\r\n\r\ninterface EcomOrderFormModalProps {\r\n  isOpen: boolean;\r\n  onClose: () => void;\r\n  order: EcomOrder | null;\r\n  mode: 'create' | 'update' | 'view';\r\n  onSuccess: () => void;\r\n}\r\n\r\nexport function EcomOrderFormModal({ isOpen, onClose, order, mode, onSuccess }: EcomOrderFormModalProps) {\r\n  const [isSubmitting, setIsSubmitting] = useState(false);\r\n  const [isFormReady, setIsFormReady] = useState(false);\r\n  const [showConfirmDialog, setShowConfirmDialog] = useState(false);\r\n  const [productCache, setProductCache] = useState<Record<string, ProductInfo>>({});\r\n  const formIsDirty = useState(false);\r\n\r\n  const isViewMode = mode === 'view';\r\n  const isCreateMode = mode === 'create';\r\n  const isEditMode = mode === 'update';\r\n\r\n  // Initialize form with react-hook-form and zod validation\r\n  const form = useForm<FormValues>({\r\n    resolver: zodResolver(formSchema) as any,\r\n    defaultValues: {\r\n      userId: '',\r\n      status: OrderStatus.PENDING,\r\n      paymentStatus: PaymentStatus.UNPAID,\r\n      notes: '',\r\n      orderDetails: [\r\n        {\r\n          productId: '',\r\n          quantity: 1,\r\n          price: 0,\r\n          productInfo: undefined\r\n        }\r\n      ]\r\n    }\r\n  });\r\n\r\n  // Xử lý đóng form\r\n  const handleClose = () => {\r\n    if (mode !== 'view' && form.formState.isDirty) {\r\n      setShowConfirmDialog(true);\r\n    } else {\r\n      onClose();\r\n    }\r\n  };\r\n\r\n  // Fetch users for the searchable select\r\n  const fetchUsers = async (search: string): Promise<SearchableSelectOption[]> => {\r\n    try {\r\n      const response = await api.get<PaginationResponse<any>>(`users?page=1&limit=10&search=${encodeURIComponent(search)}`);\r\n      const users = response?.data || [];\r\n      return users.map(user => ({\r\n        id: user.id,\r\n        fullName: user.fullName,\r\n        username: user.username,\r\n        email: user.email\r\n      }));\r\n    } catch (error) {\r\n      return [];\r\n    }\r\n  };\r\n\r\n  // Fetch product by ID and update the form with product information\r\n  const fetchProductById = async (productId: string): Promise<ProductInfo | null> => {\r\n    // Check if product is already in cache\r\n    if (productCache[productId]) {\r\n      return productCache[productId];\r\n    }\r\n\r\n    try {\r\n      const response = await api.get<ProductInfo>(`ecom-products/${productId}`);\r\n      if (response) {\r\n        // Add to cache\r\n        setProductCache(prev => ({\r\n          ...prev,\r\n          [productId]: response\r\n        }));\r\n        return response;\r\n      }\r\n    } catch (error) {\r\n      console.error('Error fetching product:', error);\r\n    }\r\n    return null;\r\n  };\r\n\r\n  // Update product price when product is selected\r\n  const updateProductInfo = async (productId: string, index: number) => {\r\n    if (!productId) return;\r\n\r\n    const product = await fetchProductById(productId);\r\n    if (product) {\r\n      const currentDetails = [...form.getValues('orderDetails')];\r\n\r\n      // Use sale price if available, otherwise use regular price\r\n      const price = product.salePrice || product.regularPrice;\r\n\r\n      // Update the product info and price\r\n      currentDetails[index] = {\r\n        ...currentDetails[index],\r\n        price: price,\r\n        productInfo: product\r\n      };\r\n\r\n      form.setValue('orderDetails', currentDetails);\r\n    }\r\n  };\r\n\r\n  // Load order data when editing or viewing\r\n  useEffect(() => {\r\n    if (!isOpen) {\r\n      // Reset form when modal closes\r\n      setTimeout(() => {\r\n        form.reset();\r\n        setIsFormReady(false);\r\n      }, 300);\r\n      return;\r\n    }\r\n\r\n    // Set loading state when modal opens\r\n    setIsFormReady(false);\r\n\r\n    setTimeout(async () => {\r\n      if (order && (isEditMode || isViewMode)) {\r\n        // For each order detail, fetch the product info\r\n        const orderDetails = order.orderDetails || [];\r\n        const detailsWithProductInfo = await Promise.all(\r\n          orderDetails.map(async (detail) => {\r\n            const productInfo = await fetchProductById(detail.productId);\r\n            return {\r\n              productId: detail.productId || '',\r\n              quantity: detail.quantity || 1,\r\n              price: detail.unitPrice || 0,\r\n              productInfo: productInfo || undefined\r\n            };\r\n          })\r\n        );\r\n\r\n        form.reset({\r\n          userId: order.userId || '',\r\n          status: order.status || OrderStatus.PENDING,\r\n          paymentStatus: order.paymentStatus || PaymentStatus.UNPAID,\r\n          notes: order.notes || '',\r\n          orderDetails: detailsWithProductInfo\r\n        });\r\n      } else if (isCreateMode) {\r\n        form.reset({\r\n          userId: '',\r\n          status: OrderStatus.PENDING,\r\n          paymentStatus: PaymentStatus.UNPAID,\r\n          notes: '',\r\n          orderDetails: [\r\n            {\r\n              productId: '',\r\n              quantity: 1,\r\n              price: 0,\r\n              productInfo: undefined\r\n            }\r\n          ]\r\n        });\r\n      }\r\n\r\n      setIsFormReady(true);\r\n    }, 300);\r\n  }, [isOpen, order, mode, form, isEditMode, isViewMode, isCreateMode]);\r\n\r\n  // Add a new product to the order\r\n  const addProduct = () => {\r\n    const currentDetails = form.getValues('orderDetails');\r\n    form.setValue('orderDetails', [\r\n      ...currentDetails,\r\n      {\r\n        productId: '',\r\n        quantity: 1,\r\n        price: 0,\r\n        productInfo: undefined\r\n      }\r\n    ], { shouldDirty: true });\r\n  };\r\n\r\n  // Remove a product from the order\r\n  const removeProduct = (index: number) => {\r\n    const currentDetails = form.getValues('orderDetails');\r\n    if (currentDetails.length > 1) {\r\n      form.setValue('orderDetails', currentDetails.filter((_, i) => i !== index), { shouldDirty: true });\r\n    } else {\r\n      toast.error(\"Đơn hàng phải có ít nhất một sản phẩm\");\r\n    }\r\n  };\r\n\r\n  // Calculate total amount for the order\r\n  const calculateTotal = () => {\r\n    const orderDetails = form.watch('orderDetails');\r\n    return orderDetails.reduce((sum, detail) => {\r\n      return sum + (detail.price * detail.quantity);\r\n    }, 0);\r\n  };\r\n\r\n  // Handle form submission\r\n  const onSubmit = async (data: FormValues) => {\r\n    try {\r\n      setIsSubmitting(true);\r\n\r\n      // Validate that all products have been selected\r\n      const invalidProducts = data.orderDetails.filter(detail => !detail.productId);\r\n      if (invalidProducts.length > 0) {\r\n        toast.error(\"Vui lòng chọn sản phẩm cho tất cả các mục\");\r\n        setIsSubmitting(false);\r\n        return;\r\n      }\r\n\r\n      // Calculate total amount\r\n      const totalAmount = calculateTotal();\r\n\r\n      if (isCreateMode) {\r\n        // Create new order with default status values\r\n        const createPayload = {\r\n          userId: data.userId,\r\n          status: OrderStatus.PENDING,\r\n          paymentStatus: PaymentStatus.UNPAID,\r\n          notes: data.notes,\r\n          totalAmount: totalAmount.toFixed(2), // Format as string with 2 decimal places\r\n          orderDetails: data.orderDetails.map(detail => ({\r\n            productId: detail.productId,\r\n            quantity: detail.quantity,\r\n            unitPrice: detail.price.toFixed(2), // Format as string with 2 decimal places\r\n            totalPrice: (detail.price * detail.quantity).toFixed(2) // Format as string with 2 decimal places\r\n          }))\r\n        };\r\n\r\n        await api.post('ecom-orders', createPayload);\r\n        toast.success(\"Đã tạo đơn hàng mới thành công\");\r\n      } else if (isEditMode && order?.id) {\r\n        // Update existing order with submitted status values\r\n        const updatePayload = {\r\n          userId: data.userId,\r\n          status: data.status,\r\n          paymentStatus: data.paymentStatus,\r\n          notes: data.notes,\r\n          totalAmount: totalAmount.toFixed(2), // Format as string with 2 decimal places\r\n          orderDetails: data.orderDetails.map(detail => ({\r\n            productId: detail.productId,\r\n            quantity: detail.quantity,\r\n            unitPrice: detail.price.toFixed(2), // Format as string with 2 decimal places\r\n            totalPrice: (detail.price * detail.quantity).toFixed(2) // Format as string with 2 decimal places\r\n          }))\r\n        };\r\n\r\n        await api.patch(`ecom-orders/${order.id}`, updatePayload);\r\n        toast.success(\"Đã cập nhật đơn hàng thành công\");\r\n      }\r\n\r\n      // Close modal and refresh data\r\n      onClose();\r\n      onSuccess();\r\n    } catch (error) {\r\n      console.error('Error submitting order:', error);\r\n      toast.error(\"Không thể lưu đơn hàng. Vui lòng thử lại sau.\");\r\n    } finally {\r\n      setIsSubmitting(false);\r\n    }\r\n  };\r\n\r\n  // Format currency function\r\n  const formatCurrency = (value: number | null | undefined): string => {\r\n    if (value == null) return \"0 ₫\";\r\n    return new Intl.NumberFormat('vi-VN', {\r\n      style: 'currency',\r\n      currency: 'VND',\r\n      maximumFractionDigits: 0\r\n    }).format(value);\r\n  };\r\n\r\n  const title = isCreateMode ? \"Tạo đơn hàng mới\" : isEditMode ? \"Chỉnh sửa đơn hàng\" : \"Chi tiết đơn hàng\";\r\n  const description = isCreateMode ? \"Điền thông tin để tạo đơn hàng mới\" : isEditMode ? \"Chỉnh sửa thông tin đơn hàng\" : \"Xem thông tin chi tiết đơn hàng\";\r\n\r\n  return (\r\n    <>\r\n      <Dialog open={isOpen} onOpenChange={(open) => !open && handleClose()}>\r\n        <DialogContent className=\"sm:max-w-[600px] max-h-[90vh] overflow-hidden flex flex-col\">\r\n          <DialogHeader>\r\n            <DialogTitle>{title}</DialogTitle>\r\n            <DialogDescription>{description}</DialogDescription>\r\n          </DialogHeader>\r\n\r\n          {!isFormReady ? (\r\n            <div className=\"flex flex-col items-center justify-center py-8\">\r\n              <Loader2 className=\"h-8 w-8 animate-spin text-primary mb-4\" />\r\n              <p className=\"text-sm text-muted-foreground\">Đang tải dữ liệu...</p>\r\n            </div>\r\n          ) : (\r\n            <Form {...form}>\r\n              <form onSubmit={form.handleSubmit(onSubmit)} className=\"space-y-6 overflow-y-auto pr-2 flex-1\">\r\n                {/* User selection */}\r\n                <FormField\r\n                  control={form.control}\r\n                  name=\"userId\"\r\n                  render={({ field }) => (\r\n                    <FormItem>\r\n                      <FormLabel>Khách hàng</FormLabel>\r\n                      <FormControl>\r\n                        <SearchableSelect\r\n                          value={field.value}\r\n                          onChange={field.onChange}\r\n                          disabled={isViewMode}\r\n                          placeholder=\"Tìm kiếm khách hàng\"\r\n                          searchPlaceholder=\"Tìm kiếm khách hàng...\"\r\n                          fetchOptions={fetchUsers}\r\n                          renderOption={(option) => option ?\r\n                            `${option.fullName || option.username} - (${option.email})` :\r\n                            \"Chọn người dùng\"\r\n                          }\r\n                        />\r\n                      </FormControl>\r\n                      <FormMessage />\r\n                    </FormItem>\r\n                  )}\r\n                />\r\n\r\n                {/* Order & Payment status - Only show in edit/view mode */}\r\n                {(isEditMode || isViewMode) && (\r\n                  <div className=\"grid grid-cols-2 gap-4\">\r\n                    {/* Order status */}\r\n                    <FormField\r\n                      control={form.control}\r\n                      name=\"status\"\r\n                      render={({ field }) => (\r\n                        <FormItem>\r\n                          <FormLabel>Trạng thái đơn hàng</FormLabel>\r\n                          <Select\r\n                            disabled={isViewMode}\r\n                            onValueChange={field.onChange}\r\n                            defaultValue={field.value}\r\n                            value={field.value}\r\n                          >\r\n                            <FormControl>\r\n                              <SelectTrigger>\r\n                                <SelectValue placeholder=\"Chọn trạng thái\" />\r\n                              </SelectTrigger>\r\n                            </FormControl>\r\n                            <SelectContent>\r\n                              <SelectItem value={OrderStatus.PENDING}>Chờ xử lý</SelectItem>\r\n                              <SelectItem value={OrderStatus.PROCESSING}>Đang xử lý</SelectItem>\r\n                              <SelectItem value={OrderStatus.COMPLETED}>Hoàn thành</SelectItem>\r\n                              <SelectItem value={OrderStatus.CANCELLED}>Đã hủy</SelectItem>\r\n                            </SelectContent>\r\n                          </Select>\r\n                          <FormMessage />\r\n                        </FormItem>\r\n                      )}\r\n                    />\r\n\r\n                    {/* Payment status */}\r\n                    <FormField\r\n                      control={form.control}\r\n                      name=\"paymentStatus\"\r\n                      render={({ field }) => (\r\n                        <FormItem>\r\n                          <FormLabel>Trạng thái thanh toán</FormLabel>\r\n                          <Select\r\n                            disabled={isViewMode}\r\n                            onValueChange={field.onChange}\r\n                            defaultValue={field.value}\r\n                            value={field.value}\r\n                          >\r\n                            <FormControl>\r\n                              <SelectTrigger>\r\n                                <SelectValue placeholder=\"Chọn trạng thái thanh toán\" />\r\n                              </SelectTrigger>\r\n                            </FormControl>\r\n                            <SelectContent>\r\n                              <SelectItem value={PaymentStatus.PAID}>Đã thanh toán</SelectItem>\r\n                              <SelectItem value={PaymentStatus.UNPAID}>Chưa thanh toán</SelectItem>\r\n                            </SelectContent>\r\n                          </Select>\r\n                          <FormMessage />\r\n                        </FormItem>\r\n                      )}\r\n                    />\r\n                  </div>\r\n                )}\r\n\r\n                {/* Notes */}\r\n                <FormField\r\n                  control={form.control}\r\n                  name=\"notes\"\r\n                  render={({ field }) => (\r\n                    <FormItem>\r\n                      <FormLabel>Ghi chú</FormLabel>\r\n                      <FormControl>\r\n                        <Textarea\r\n                          placeholder=\"Nhập ghi chú cho đơn hàng\"\r\n                          className=\"resize-none\"\r\n                          {...field}\r\n                          disabled={isViewMode}\r\n                          value={field.value || ''}\r\n                        />\r\n                      </FormControl>\r\n                      <FormMessage />\r\n                    </FormItem>\r\n                  )}\r\n                />\r\n\r\n                {/* Order details */}\r\n                <div>\r\n                  <div className=\"flex justify-between items-center mb-3\">\r\n                    <h3 className=\"text-lg font-medium\">Danh sách sản phẩm</h3>\r\n                    {!isViewMode && (\r\n                      <Button\r\n                        type=\"button\"\r\n                        variant=\"outline\"\r\n                        size=\"sm\"\r\n                        onClick={addProduct}\r\n                      >\r\n                        + Thêm sản phẩm\r\n                      </Button>\r\n                    )}\r\n                  </div>\r\n\r\n                  <div className=\"space-y-4\">\r\n                    {form.watch('orderDetails').map((_, index) => (\r\n                      <div key={index} className=\"border rounded-md p-4 relative\">\r\n                        {!isViewMode && (\r\n                          <Button\r\n                            type=\"button\"\r\n                            variant=\"ghost\"\r\n                            size=\"icon\"\r\n                            className=\"absolute top-2 right-2 h-6 w-6\"\r\n                            onClick={() => removeProduct(index)}\r\n                          >\r\n                            <X className=\"h-4 w-4\" />\r\n                          </Button>\r\n                        )}\r\n                        <div className=\"grid grid-cols-1 gap-4\">\r\n                          <FormField\r\n                            control={form.control}\r\n                            name={`orderDetails.${index}.productId`}\r\n                            render={({ field }) => (\r\n                              <FormItem>\r\n                                <FormLabel>Sản phẩm</FormLabel>\r\n                                <FormControl>\r\n                                  <EcomProductSearchSelect\r\n                                    value={field.value}\r\n                                    onChange={(value) => {\r\n                                      field.onChange(value);\r\n                                      // Update product info when product is selected\r\n                                      updateProductInfo(value, index);\r\n                                    }}\r\n                                    disabled={isViewMode}\r\n                                  />\r\n                                </FormControl>\r\n                                <FormMessage />\r\n                              </FormItem>\r\n                            )}\r\n                          />\r\n                          <div className=\"grid grid-cols-2 gap-4\">\r\n                            <FormField\r\n                              control={form.control}\r\n                              name={`orderDetails.${index}.quantity`}\r\n                              render={({ field: { value, onChange, ...field } }) => (\r\n                                <FormItem>\r\n                                  <FormLabel>Số lượng</FormLabel>\r\n                                  <FormControl>\r\n                                    <div className=\"flex items-center space-x-2\">\r\n                                      <Input\r\n                                        {...field}\r\n                                        disabled={isViewMode}\r\n                                        type=\"number\"\r\n                                        min=\"1\"\r\n                                        placeholder=\"Nhập số lượng\"\r\n                                        value={value === null ? '' : value}\r\n                                        onChange={(e) => {\r\n                                          const val = e.target.value === '' ? 0 : Number(e.target.value);\r\n                                          onChange(val);\r\n\r\n                                          // Check if quantity exceeds stock\r\n                                          const productInfo = form.getValues(`orderDetails.${index}.productInfo`);\r\n                                          if (productInfo && productInfo.stockQuantity && val > productInfo.stockQuantity) {\r\n                                            toast.warning(`Số lượng vượt quá tồn kho (${productInfo.stockQuantity})`);\r\n                                          }\r\n                                        }}\r\n                                      />\r\n                                    </div>\r\n                                  </FormControl>\r\n                                  {form.watch(`orderDetails.${index}.productInfo`)?.stockQuantity && (\r\n                                    <FormDescription>\r\n                                      Tồn kho: {form.watch(`orderDetails.${index}.productInfo`)?.stockQuantity}\r\n                                    </FormDescription>\r\n                                  )}\r\n                                  <FormMessage />\r\n                                </FormItem>\r\n                              )}\r\n                            />\r\n                            <FormField\r\n                              control={form.control}\r\n                              name={`orderDetails.${index}.price`}\r\n                              render={({ field: { value, onChange, ...field } }) => (\r\n                                <FormItem>\r\n                                  <FormLabel>Đơn giá (VND)</FormLabel>\r\n                                  <FormControl>\r\n                                    <Input\r\n                                      {...field}\r\n                                      type=\"number\"\r\n                                      min=\"0\"\r\n                                      step=\"1000\"\r\n                                      placeholder=\"Đơn giá\"\r\n                                      value={value === null ? '' : value}\r\n                                      disabled={true} // Always disabled as price is auto-populated\r\n                                      className=\"bg-muted\"\r\n                                    />\r\n                                  </FormControl>\r\n                                  <FormDescription>\r\n                                    Giá sản phẩm: {formatCurrency(value)}\r\n                                  </FormDescription>\r\n                                  <FormMessage />\r\n                                </FormItem>\r\n                              )}\r\n                            />\r\n                          </div>\r\n\r\n                          {/* Product Info & Line Total */}\r\n                          {form.watch(`orderDetails.${index}.productId`) && (\r\n                            <div className=\"mt-2 flex justify-between items-center border-t pt-2\">\r\n                              <div className=\"flex items-center gap-2\">\r\n                                {form.watch(`orderDetails.${index}.productInfo`)?.imageUrl && (\r\n                                  <div className=\"w-12 h-12 rounded overflow-hidden\">\r\n                                    <img\r\n                                      src={form.watch(`orderDetails.${index}.productInfo`)?.imageUrl}\r\n                                      alt=\"Product\"\r\n                                      className=\"object-cover w-full h-full\"\r\n                                      onError={(e) => {\r\n                                        (e.target as HTMLImageElement).src = 'https://placehold.co/100?text=No+Image';\r\n                                      }}\r\n                                    />\r\n                                  </div>\r\n                                )}\r\n                                <div>\r\n                                  <div className=\"text-sm font-medium\">\r\n                                    {form.watch(`orderDetails.${index}.productInfo`)?.productName || 'Sản phẩm'}\r\n                                  </div>\r\n                                  <div className=\"text-xs text-muted-foreground\">\r\n                                    {form.watch(`orderDetails.${index}.productInfo`)?.productCode || ''}\r\n                                  </div>\r\n                                </div>\r\n                              </div>\r\n                              <div className=\"text-right\">\r\n                                <div className=\"text-sm text-muted-foreground\">Thành tiền:</div>\r\n                                <div className=\"font-medium\">\r\n                                  {formatCurrency(\r\n                                    form.watch(`orderDetails.${index}.price`) *\r\n                                    form.watch(`orderDetails.${index}.quantity`)\r\n                                  )}\r\n                                </div>\r\n                              </div>\r\n                            </div>\r\n                          )}\r\n                        </div>\r\n                      </div>\r\n                    ))}\r\n\r\n                    {(!form.watch('orderDetails') || form.watch('orderDetails').length === 0) && (\r\n                      <div className=\"text-center p-4 border border-dashed rounded-md\">\r\n                        <p className=\"text-muted-foreground\">Chưa có sản phẩm nào được thêm vào đơn hàng</p>\r\n                        {mode !== 'view' && (\r\n                          <Button\r\n                            type=\"button\"\r\n                            variant=\"outline\"\r\n                            size=\"sm\"\r\n                            className=\"mt-2\"\r\n                            onClick={addProduct}\r\n                          >\r\n                            Thêm sản phẩm\r\n                          </Button>\r\n                        )}\r\n                      </div>\r\n                    )}\r\n                  </div>\r\n                </div>\r\n\r\n                {/* Order Summary */}\r\n                <div className=\"bg-muted p-4 rounded-md\">\r\n                  <h3 className=\"font-medium mb-2\">Tóm tắt đơn hàng</h3>\r\n                  <div className=\"space-y-2 text-sm\">\r\n                    {/* Customer Info */}\r\n                    <div className=\"grid grid-cols-2 gap-2\">\r\n                      <div className=\"text-muted-foreground\">Khách hàng:</div>\r\n                      <div className=\"font-medium flex items-center gap-1\">\r\n                        <User className=\"h-3.5 w-3.5 text-blue-500\" />\r\n                        {form.watch('userId') ? 'Đã chọn' : 'Chưa chọn khách hàng'}\r\n                      </div>\r\n                    </div>\r\n\r\n                    {/* Order Type */}\r\n                    <div className=\"grid grid-cols-2 gap-2\">\r\n                      <div className=\"text-muted-foreground\">Loại đơn hàng:</div>\r\n                      <div className=\"font-medium flex items-center gap-1\">\r\n                        <ShoppingCart className=\"h-3.5 w-3.5 text-green-500\" />\r\n                        Đơn hàng sản phẩm\r\n                      </div>\r\n                    </div>\r\n\r\n                    {/* Status Fields - Only show in update/view mode */}\r\n                    {(isEditMode || isViewMode) && (\r\n                      <>\r\n                        <div className=\"grid grid-cols-2 gap-2\">\r\n                          <div className=\"text-muted-foreground\">Trạng thái đơn hàng:</div>\r\n                          <div className=\"font-medium\">\r\n                            {form.watch('status') === OrderStatus.PENDING ? 'Chờ xử lý' :\r\n                             form.watch('status') === OrderStatus.PROCESSING ? 'Đang xử lý' :\r\n                             form.watch('status') === OrderStatus.COMPLETED ? 'Hoàn thành' : 'Đã hủy'}\r\n                          </div>\r\n                        </div>\r\n\r\n                        <div className=\"grid grid-cols-2 gap-2\">\r\n                          <div className=\"text-muted-foreground\">Trạng thái thanh toán:</div>\r\n                          <div className=\"font-medium\">\r\n                            {form.watch('paymentStatus') === PaymentStatus.PAID ? 'Đã thanh toán' : 'Chưa thanh toán'}\r\n                          </div>\r\n                        </div>\r\n                      </>\r\n                    )}\r\n\r\n                    {/* Product List */}\r\n                    {form.watch('orderDetails')?.length > 0 && (\r\n                      <div className=\"mt-2\">\r\n                        <div className=\"text-muted-foreground mb-1\">Danh sách sản phẩm:</div>\r\n                        <div className=\"pl-4 space-y-1\">\r\n                          {form.watch('orderDetails').map((detail, index) => {\r\n                            const productInfo = detail.productInfo;\r\n                            const productName = productInfo ? productInfo.productName : 'Sản phẩm chưa chọn';\r\n                            return (\r\n                              <div key={index} className=\"flex justify-between\">\r\n                                <div className=\"truncate max-w-[200px]\">\r\n                                  {productName} x {detail.quantity}\r\n                                </div>\r\n                                <div>\r\n                                  {formatCurrency(detail.price * detail.quantity)}\r\n                                </div>\r\n                              </div>\r\n                            );\r\n                          })}\r\n                        </div>\r\n                      </div>\r\n                    )}\r\n\r\n                    {/* Total Amount */}\r\n                    <div className=\"grid grid-cols-2 gap-2 pt-2 border-t mt-2\">\r\n                      <div className=\"text-muted-foreground font-medium\">Tổng tiền:</div>\r\n                      <div className=\"font-bold text-base\">\r\n                        {formatCurrency(calculateTotal())}\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n\r\n                <DialogFooter>\r\n                  <Button type=\"button\" variant=\"outline\" onClick={handleClose} disabled={isSubmitting}>\r\n                    {isViewMode ? 'Đóng' : 'Hủy'}\r\n                  </Button>\r\n                  {!isViewMode && (\r\n                    <Button type=\"submit\" disabled={isSubmitting}>\r\n                      {isSubmitting && <Loader2 className=\"mr-2 h-4 w-4 animate-spin\" />}\r\n                      {isCreateMode ? \"Tạo đơn hàng\" : \"Cập nhật\"}\r\n                    </Button>\r\n                  )}\r\n                </DialogFooter>\r\n              </form>\r\n            </Form>\r\n          )}\r\n        </DialogContent>\r\n      </Dialog>\r\n\r\n      {/* Confirm Dialog */}\r\n      <AlertDialog open={showConfirmDialog} onOpenChange={setShowConfirmDialog}>\r\n        <AlertDialogContent>\r\n          <AlertDialogHeader>\r\n            <AlertDialogTitle>Xác nhận hủy</AlertDialogTitle>\r\n            <AlertDialogDescription>\r\n              Các thay đổi của bạn sẽ không được lưu. Bạn có chắc muốn hủy bỏ không?\r\n            </AlertDialogDescription>\r\n          </AlertDialogHeader>\r\n          <AlertDialogFooter>\r\n            <AlertDialogCancel>Tiếp tục chỉnh sửa</AlertDialogCancel>\r\n            <AlertDialogAction\r\n              onClick={() => {\r\n                onClose();\r\n                setShowConfirmDialog(false);\r\n              }}\r\n              className='bg-destructive hover:bg-destructive/90'\r\n            >\r\n              Hủy thay đổi\r\n            </AlertDialogAction>\r\n          </AlertDialogFooter>\r\n        </AlertDialogContent>\r\n      </AlertDialog>\r\n    </>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAQA;AASA;AAOA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AAEA;;;AAxCA;;;;;;;;;;;;;;;;;;AAqDA,qBAAqB;AACrB,MAAM,aAAa,wLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IAC1B,QAAQ,wLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IAC1B,QAAQ,wLAAA,CAAA,IAAC,CAAC,UAAU,CAAC,4KAAA,CAAA,cAAW,EAAE,OAAO,CAAC,4KAAA,CAAA,cAAW,CAAC,OAAO;IAC7D,eAAe,wLAAA,CAAA,IAAC,CAAC,UAAU,CAAC,4KAAA,CAAA,gBAAa,EAAE,OAAO,CAAC,4KAAA,CAAA,gBAAa,CAAC,MAAM;IACvE,OAAO,wLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAC1B,cAAc,wLAAA,CAAA,IAAC,CAAC,KAAK,CAAC,wLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;QAC7B,WAAW,wLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;QAC7B,UAAU,wLAAA,CAAA,IAAC,CAAC,MAAM,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;QACnC,OAAO,wLAAA,CAAA,IAAC,CAAC,MAAM,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;QAChC,aAAa,wLAAA,CAAA,IAAC,CAAC,GAAG,GAAG,QAAQ,GAAG,wCAAwC;IAC1E,IAAI,GAAG,CAAC,GAAG;AACb;AAYO,SAAS,mBAAmB,EAAE,MAAM,EAAE,OAAO,EAAE,KAAK,EAAE,IAAI,EAAE,SAAS,EAA2B;;IACrG,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,sQAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,sQAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,sQAAA,CAAA,WAAQ,AAAD,EAAE;IAC3D,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,sQAAA,CAAA,WAAQ,AAAD,EAA+B,CAAC;IAC/E,MAAM,cAAc,CAAA,GAAA,sQAAA,CAAA,WAAQ,AAAD,EAAE;IAE7B,MAAM,aAAa,SAAS;IAC5B,MAAM,eAAe,SAAS;IAC9B,MAAM,aAAa,SAAS;IAE5B,0DAA0D;IAC1D,MAAM,OAAO,CAAA,GAAA,0PAAA,CAAA,UAAO,AAAD,EAAc;QAC/B,UAAU,CAAA,GAAA,wQAAA,CAAA,cAAW,AAAD,EAAE;QACtB,eAAe;YACb,QAAQ;YACR,QAAQ,4KAAA,CAAA,cAAW,CAAC,OAAO;YAC3B,eAAe,4KAAA,CAAA,gBAAa,CAAC,MAAM;YACnC,OAAO;YACP,cAAc;gBACZ;oBACE,WAAW;oBACX,UAAU;oBACV,OAAO;oBACP,aAAa;gBACf;aACD;QACH;IACF;IAEA,kBAAkB;IAClB,MAAM,cAAc;QAClB,IAAI,SAAS,UAAU,KAAK,SAAS,CAAC,OAAO,EAAE;YAC7C,qBAAqB;QACvB,OAAO;YACL;QACF;IACF;IAEA,wCAAwC;IACxC,MAAM,aAAa,OAAO;QACxB,IAAI;YACF,MAAM,WAAW,MAAM,6GAAA,CAAA,MAAG,CAAC,GAAG,CAA0B,CAAC,6BAA6B,EAAE,mBAAmB,SAAS;YACpH,MAAM,QAAQ,UAAU,QAAQ,EAAE;YAClC,OAAO,MAAM,GAAG,CAAC,CAAA,OAAQ,CAAC;oBACxB,IAAI,KAAK,EAAE;oBACX,UAAU,KAAK,QAAQ;oBACvB,UAAU,KAAK,QAAQ;oBACvB,OAAO,KAAK,KAAK;gBACnB,CAAC;QACH,EAAE,OAAO,OAAO;YACd,OAAO,EAAE;QACX;IACF;IAEA,mEAAmE;IACnE,MAAM,mBAAmB,OAAO;QAC9B,uCAAuC;QACvC,IAAI,YAAY,CAAC,UAAU,EAAE;YAC3B,OAAO,YAAY,CAAC,UAAU;QAChC;QAEA,IAAI;YACF,MAAM,WAAW,MAAM,6GAAA,CAAA,MAAG,CAAC,GAAG,CAAc,CAAC,cAAc,EAAE,WAAW;YACxE,IAAI,UAAU;gBACZ,eAAe;gBACf,gBAAgB,CAAA,OAAQ,CAAC;wBACvB,GAAG,IAAI;wBACP,CAAC,UAAU,EAAE;oBACf,CAAC;gBACD,OAAO;YACT;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2BAA2B;QAC3C;QACA,OAAO;IACT;IAEA,gDAAgD;IAChD,MAAM,oBAAoB,OAAO,WAAmB;QAClD,IAAI,CAAC,WAAW;QAEhB,MAAM,UAAU,MAAM,iBAAiB;QACvC,IAAI,SAAS;YACX,MAAM,iBAAiB;mBAAI,KAAK,SAAS,CAAC;aAAgB;YAE1D,2DAA2D;YAC3D,MAAM,QAAQ,QAAQ,SAAS,IAAI,QAAQ,YAAY;YAEvD,oCAAoC;YACpC,cAAc,CAAC,MAAM,GAAG;gBACtB,GAAG,cAAc,CAAC,MAAM;gBACxB,OAAO;gBACP,aAAa;YACf;YAEA,KAAK,QAAQ,CAAC,gBAAgB;QAChC;IACF;IAEA,0CAA0C;IAC1C,CAAA,GAAA,sQAAA,CAAA,YAAS,AAAD;wCAAE;YACR,IAAI,CAAC,QAAQ;gBACX,+BAA+B;gBAC/B;oDAAW;wBACT,KAAK,KAAK;wBACV,eAAe;oBACjB;mDAAG;gBACH;YACF;YAEA,qCAAqC;YACrC,eAAe;YAEf;gDAAW;oBACT,IAAI,SAAS,CAAC,cAAc,UAAU,GAAG;wBACvC,gDAAgD;wBAChD,MAAM,eAAe,MAAM,YAAY,IAAI,EAAE;wBAC7C,MAAM,yBAAyB,MAAM,QAAQ,GAAG,CAC9C,aAAa,GAAG;4DAAC,OAAO;gCACtB,MAAM,cAAc,MAAM,iBAAiB,OAAO,SAAS;gCAC3D,OAAO;oCACL,WAAW,OAAO,SAAS,IAAI;oCAC/B,UAAU,OAAO,QAAQ,IAAI;oCAC7B,OAAO,OAAO,SAAS,IAAI;oCAC3B,aAAa,eAAe;gCAC9B;4BACF;;wBAGF,KAAK,KAAK,CAAC;4BACT,QAAQ,MAAM,MAAM,IAAI;4BACxB,QAAQ,MAAM,MAAM,IAAI,4KAAA,CAAA,cAAW,CAAC,OAAO;4BAC3C,eAAe,MAAM,aAAa,IAAI,4KAAA,CAAA,gBAAa,CAAC,MAAM;4BAC1D,OAAO,MAAM,KAAK,IAAI;4BACtB,cAAc;wBAChB;oBACF,OAAO,IAAI,cAAc;wBACvB,KAAK,KAAK,CAAC;4BACT,QAAQ;4BACR,QAAQ,4KAAA,CAAA,cAAW,CAAC,OAAO;4BAC3B,eAAe,4KAAA,CAAA,gBAAa,CAAC,MAAM;4BACnC,OAAO;4BACP,cAAc;gCACZ;oCACE,WAAW;oCACX,UAAU;oCACV,OAAO;oCACP,aAAa;gCACf;6BACD;wBACH;oBACF;oBAEA,eAAe;gBACjB;+CAAG;QACL;uCAAG;QAAC;QAAQ;QAAO;QAAM;QAAM;QAAY;QAAY;KAAa;IAEpE,iCAAiC;IACjC,MAAM,aAAa;QACjB,MAAM,iBAAiB,KAAK,SAAS,CAAC;QACtC,KAAK,QAAQ,CAAC,gBAAgB;eACzB;YACH;gBACE,WAAW;gBACX,UAAU;gBACV,OAAO;gBACP,aAAa;YACf;SACD,EAAE;YAAE,aAAa;QAAK;IACzB;IAEA,kCAAkC;IAClC,MAAM,gBAAgB,CAAC;QACrB,MAAM,iBAAiB,KAAK,SAAS,CAAC;QACtC,IAAI,eAAe,MAAM,GAAG,GAAG;YAC7B,KAAK,QAAQ,CAAC,gBAAgB,eAAe,MAAM,CAAC,CAAC,GAAG,IAAM,MAAM,QAAQ;gBAAE,aAAa;YAAK;QAClG,OAAO;YACL,2QAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd;IACF;IAEA,uCAAuC;IACvC,MAAM,iBAAiB;QACrB,MAAM,eAAe,KAAK,KAAK,CAAC;QAChC,OAAO,aAAa,MAAM,CAAC,CAAC,KAAK;YAC/B,OAAO,MAAO,OAAO,KAAK,GAAG,OAAO,QAAQ;QAC9C,GAAG;IACL;IAEA,yBAAyB;IACzB,MAAM,WAAW,OAAO;QACtB,IAAI;YACF,gBAAgB;YAEhB,gDAAgD;YAChD,MAAM,kBAAkB,KAAK,YAAY,CAAC,MAAM,CAAC,CAAA,SAAU,CAAC,OAAO,SAAS;YAC5E,IAAI,gBAAgB,MAAM,GAAG,GAAG;gBAC9B,2QAAA,CAAA,QAAK,CAAC,KAAK,CAAC;gBACZ,gBAAgB;gBAChB;YACF;YAEA,yBAAyB;YACzB,MAAM,cAAc;YAEpB,IAAI,cAAc;gBAChB,8CAA8C;gBAC9C,MAAM,gBAAgB;oBACpB,QAAQ,KAAK,MAAM;oBACnB,QAAQ,4KAAA,CAAA,cAAW,CAAC,OAAO;oBAC3B,eAAe,4KAAA,CAAA,gBAAa,CAAC,MAAM;oBACnC,OAAO,KAAK,KAAK;oBACjB,aAAa,YAAY,OAAO,CAAC;oBACjC,cAAc,KAAK,YAAY,CAAC,GAAG,CAAC,CAAA,SAAU,CAAC;4BAC7C,WAAW,OAAO,SAAS;4BAC3B,UAAU,OAAO,QAAQ;4BACzB,WAAW,OAAO,KAAK,CAAC,OAAO,CAAC;4BAChC,YAAY,CAAC,OAAO,KAAK,GAAG,OAAO,QAAQ,EAAE,OAAO,CAAC,GAAG,yCAAyC;wBACnG,CAAC;gBACH;gBAEA,MAAM,6GAAA,CAAA,MAAG,CAAC,IAAI,CAAC,eAAe;gBAC9B,2QAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YAChB,OAAO,IAAI,cAAc,OAAO,IAAI;gBAClC,qDAAqD;gBACrD,MAAM,gBAAgB;oBACpB,QAAQ,KAAK,MAAM;oBACnB,QAAQ,KAAK,MAAM;oBACnB,eAAe,KAAK,aAAa;oBACjC,OAAO,KAAK,KAAK;oBACjB,aAAa,YAAY,OAAO,CAAC;oBACjC,cAAc,KAAK,YAAY,CAAC,GAAG,CAAC,CAAA,SAAU,CAAC;4BAC7C,WAAW,OAAO,SAAS;4BAC3B,UAAU,OAAO,QAAQ;4BACzB,WAAW,OAAO,KAAK,CAAC,OAAO,CAAC;4BAChC,YAAY,CAAC,OAAO,KAAK,GAAG,OAAO,QAAQ,EAAE,OAAO,CAAC,GAAG,yCAAyC;wBACnG,CAAC;gBACH;gBAEA,MAAM,6GAAA,CAAA,MAAG,CAAC,KAAK,CAAC,CAAC,YAAY,EAAE,MAAM,EAAE,EAAE,EAAE;gBAC3C,2QAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YAChB;YAEA,+BAA+B;YAC/B;YACA;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2BAA2B;YACzC,2QAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd,SAAU;YACR,gBAAgB;QAClB;IACF;IAEA,2BAA2B;IAC3B,MAAM,iBAAiB,CAAC;QACtB,IAAI,SAAS,MAAM,OAAO;QAC1B,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;YACpC,OAAO;YACP,UAAU;YACV,uBAAuB;QACzB,GAAG,MAAM,CAAC;IACZ;IAEA,MAAM,QAAQ,eAAe,qBAAqB,aAAa,uBAAuB;IACtF,MAAM,cAAc,eAAe,uCAAuC,aAAa,iCAAiC;IAExH,qBACE;;0BACE,sSAAC,8HAAA,CAAA,SAAM;gBAAC,MAAM;gBAAQ,cAAc,CAAC,OAAS,CAAC,QAAQ;0BACrD,cAAA,sSAAC,8HAAA,CAAA,gBAAa;oBAAC,WAAU;;sCACvB,sSAAC,8HAAA,CAAA,eAAY;;8CACX,sSAAC,8HAAA,CAAA,cAAW;8CAAE;;;;;;8CACd,sSAAC,8HAAA,CAAA,oBAAiB;8CAAE;;;;;;;;;;;;wBAGrB,CAAC,4BACA,sSAAC;4BAAI,WAAU;;8CACb,sSAAC,wSAAA,CAAA,UAAO;oCAAC,WAAU;;;;;;8CACnB,sSAAC;oCAAE,WAAU;8CAAgC;;;;;;;;;;;iDAG/C,sSAAC,4HAAA,CAAA,OAAI;4BAAE,GAAG,IAAI;sCACZ,cAAA,sSAAC;gCAAK,UAAU,KAAK,YAAY,CAAC;gCAAW,WAAU;;kDAErD,sSAAC,4HAAA,CAAA,YAAS;wCACR,SAAS,KAAK,OAAO;wCACrB,MAAK;wCACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,sSAAC,4HAAA,CAAA,WAAQ;;kEACP,sSAAC,4HAAA,CAAA,YAAS;kEAAC;;;;;;kEACX,sSAAC,4HAAA,CAAA,cAAW;kEACV,cAAA,sSAAC,4IAAA,CAAA,mBAAgB;4DACf,OAAO,MAAM,KAAK;4DAClB,UAAU,MAAM,QAAQ;4DACxB,UAAU;4DACV,aAAY;4DACZ,mBAAkB;4DAClB,cAAc;4DACd,cAAc,CAAC,SAAW,SACxB,GAAG,OAAO,QAAQ,IAAI,OAAO,QAAQ,CAAC,IAAI,EAAE,OAAO,KAAK,CAAC,CAAC,CAAC,GAC3D;;;;;;;;;;;kEAIN,sSAAC,4HAAA,CAAA,cAAW;;;;;;;;;;;;;;;;oCAMjB,CAAC,cAAc,UAAU,mBACxB,sSAAC;wCAAI,WAAU;;0DAEb,sSAAC,4HAAA,CAAA,YAAS;gDACR,SAAS,KAAK,OAAO;gDACrB,MAAK;gDACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,sSAAC,4HAAA,CAAA,WAAQ;;0EACP,sSAAC,4HAAA,CAAA,YAAS;0EAAC;;;;;;0EACX,sSAAC,8HAAA,CAAA,SAAM;gEACL,UAAU;gEACV,eAAe,MAAM,QAAQ;gEAC7B,cAAc,MAAM,KAAK;gEACzB,OAAO,MAAM,KAAK;;kFAElB,sSAAC,4HAAA,CAAA,cAAW;kFACV,cAAA,sSAAC,8HAAA,CAAA,gBAAa;sFACZ,cAAA,sSAAC,8HAAA,CAAA,cAAW;gFAAC,aAAY;;;;;;;;;;;;;;;;kFAG7B,sSAAC,8HAAA,CAAA,gBAAa;;0FACZ,sSAAC,8HAAA,CAAA,aAAU;gFAAC,OAAO,4KAAA,CAAA,cAAW,CAAC,OAAO;0FAAE;;;;;;0FACxC,sSAAC,8HAAA,CAAA,aAAU;gFAAC,OAAO,4KAAA,CAAA,cAAW,CAAC,UAAU;0FAAE;;;;;;0FAC3C,sSAAC,8HAAA,CAAA,aAAU;gFAAC,OAAO,4KAAA,CAAA,cAAW,CAAC,SAAS;0FAAE;;;;;;0FAC1C,sSAAC,8HAAA,CAAA,aAAU;gFAAC,OAAO,4KAAA,CAAA,cAAW,CAAC,SAAS;0FAAE;;;;;;;;;;;;;;;;;;0EAG9C,sSAAC,4HAAA,CAAA,cAAW;;;;;;;;;;;;;;;;0DAMlB,sSAAC,4HAAA,CAAA,YAAS;gDACR,SAAS,KAAK,OAAO;gDACrB,MAAK;gDACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,sSAAC,4HAAA,CAAA,WAAQ;;0EACP,sSAAC,4HAAA,CAAA,YAAS;0EAAC;;;;;;0EACX,sSAAC,8HAAA,CAAA,SAAM;gEACL,UAAU;gEACV,eAAe,MAAM,QAAQ;gEAC7B,cAAc,MAAM,KAAK;gEACzB,OAAO,MAAM,KAAK;;kFAElB,sSAAC,4HAAA,CAAA,cAAW;kFACV,cAAA,sSAAC,8HAAA,CAAA,gBAAa;sFACZ,cAAA,sSAAC,8HAAA,CAAA,cAAW;gFAAC,aAAY;;;;;;;;;;;;;;;;kFAG7B,sSAAC,8HAAA,CAAA,gBAAa;;0FACZ,sSAAC,8HAAA,CAAA,aAAU;gFAAC,OAAO,4KAAA,CAAA,gBAAa,CAAC,IAAI;0FAAE;;;;;;0FACvC,sSAAC,8HAAA,CAAA,aAAU;gFAAC,OAAO,4KAAA,CAAA,gBAAa,CAAC,MAAM;0FAAE;;;;;;;;;;;;;;;;;;0EAG7C,sSAAC,4HAAA,CAAA,cAAW;;;;;;;;;;;;;;;;;;;;;;kDAQtB,sSAAC,4HAAA,CAAA,YAAS;wCACR,SAAS,KAAK,OAAO;wCACrB,MAAK;wCACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,sSAAC,4HAAA,CAAA,WAAQ;;kEACP,sSAAC,4HAAA,CAAA,YAAS;kEAAC;;;;;;kEACX,sSAAC,4HAAA,CAAA,cAAW;kEACV,cAAA,sSAAC,gIAAA,CAAA,WAAQ;4DACP,aAAY;4DACZ,WAAU;4DACT,GAAG,KAAK;4DACT,UAAU;4DACV,OAAO,MAAM,KAAK,IAAI;;;;;;;;;;;kEAG1B,sSAAC,4HAAA,CAAA,cAAW;;;;;;;;;;;;;;;;kDAMlB,sSAAC;;0DACC,sSAAC;gDAAI,WAAU;;kEACb,sSAAC;wDAAG,WAAU;kEAAsB;;;;;;oDACnC,CAAC,4BACA,sSAAC,8HAAA,CAAA,SAAM;wDACL,MAAK;wDACL,SAAQ;wDACR,MAAK;wDACL,SAAS;kEACV;;;;;;;;;;;;0DAML,sSAAC;gDAAI,WAAU;;oDACZ,KAAK,KAAK,CAAC,gBAAgB,GAAG,CAAC,CAAC,GAAG,sBAClC,sSAAC;4DAAgB,WAAU;;gEACxB,CAAC,4BACA,sSAAC,8HAAA,CAAA,SAAM;oEACL,MAAK;oEACL,SAAQ;oEACR,MAAK;oEACL,WAAU;oEACV,SAAS,IAAM,cAAc;8EAE7B,cAAA,sSAAC,mRAAA,CAAA,IAAC;wEAAC,WAAU;;;;;;;;;;;8EAGjB,sSAAC;oEAAI,WAAU;;sFACb,sSAAC,4HAAA,CAAA,YAAS;4EACR,SAAS,KAAK,OAAO;4EACrB,MAAM,CAAC,aAAa,EAAE,MAAM,UAAU,CAAC;4EACvC,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,sSAAC,4HAAA,CAAA,WAAQ;;sGACP,sSAAC,4HAAA,CAAA,YAAS;sGAAC;;;;;;sGACX,sSAAC,4HAAA,CAAA,cAAW;sGACV,cAAA,sSAAC,4LAAA,CAAA,0BAAuB;gGACtB,OAAO,MAAM,KAAK;gGAClB,UAAU,CAAC;oGACT,MAAM,QAAQ,CAAC;oGACf,+CAA+C;oGAC/C,kBAAkB,OAAO;gGAC3B;gGACA,UAAU;;;;;;;;;;;sGAGd,sSAAC,4HAAA,CAAA,cAAW;;;;;;;;;;;;;;;;sFAIlB,sSAAC;4EAAI,WAAU;;8FACb,sSAAC,4HAAA,CAAA,YAAS;oFACR,SAAS,KAAK,OAAO;oFACrB,MAAM,CAAC,aAAa,EAAE,MAAM,SAAS,CAAC;oFACtC,QAAQ,CAAC,EAAE,OAAO,EAAE,KAAK,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,iBAC/C,sSAAC,4HAAA,CAAA,WAAQ;;8GACP,sSAAC,4HAAA,CAAA,YAAS;8GAAC;;;;;;8GACX,sSAAC,4HAAA,CAAA,cAAW;8GACV,cAAA,sSAAC;wGAAI,WAAU;kHACb,cAAA,sSAAC,6HAAA,CAAA,QAAK;4GACH,GAAG,KAAK;4GACT,UAAU;4GACV,MAAK;4GACL,KAAI;4GACJ,aAAY;4GACZ,OAAO,UAAU,OAAO,KAAK;4GAC7B,UAAU,CAAC;gHACT,MAAM,MAAM,EAAE,MAAM,CAAC,KAAK,KAAK,KAAK,IAAI,OAAO,EAAE,MAAM,CAAC,KAAK;gHAC7D,SAAS;gHAET,kCAAkC;gHAClC,MAAM,cAAc,KAAK,SAAS,CAAC,CAAC,aAAa,EAAE,MAAM,YAAY,CAAC;gHACtE,IAAI,eAAe,YAAY,aAAa,IAAI,MAAM,YAAY,aAAa,EAAE;oHAC/E,2QAAA,CAAA,QAAK,CAAC,OAAO,CAAC,CAAC,2BAA2B,EAAE,YAAY,aAAa,CAAC,CAAC,CAAC;gHAC1E;4GACF;;;;;;;;;;;;;;;;gGAIL,KAAK,KAAK,CAAC,CAAC,aAAa,EAAE,MAAM,YAAY,CAAC,GAAG,+BAChD,sSAAC,4HAAA,CAAA,kBAAe;;wGAAC;wGACL,KAAK,KAAK,CAAC,CAAC,aAAa,EAAE,MAAM,YAAY,CAAC,GAAG;;;;;;;8GAG/D,sSAAC,4HAAA,CAAA,cAAW;;;;;;;;;;;;;;;;8FAIlB,sSAAC,4HAAA,CAAA,YAAS;oFACR,SAAS,KAAK,OAAO;oFACrB,MAAM,CAAC,aAAa,EAAE,MAAM,MAAM,CAAC;oFACnC,QAAQ,CAAC,EAAE,OAAO,EAAE,KAAK,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,iBAC/C,sSAAC,4HAAA,CAAA,WAAQ;;8GACP,sSAAC,4HAAA,CAAA,YAAS;8GAAC;;;;;;8GACX,sSAAC,4HAAA,CAAA,cAAW;8GACV,cAAA,sSAAC,6HAAA,CAAA,QAAK;wGACH,GAAG,KAAK;wGACT,MAAK;wGACL,KAAI;wGACJ,MAAK;wGACL,aAAY;wGACZ,OAAO,UAAU,OAAO,KAAK;wGAC7B,UAAU;wGACV,WAAU;;;;;;;;;;;8GAGd,sSAAC,4HAAA,CAAA,kBAAe;;wGAAC;wGACA,eAAe;;;;;;;8GAEhC,sSAAC,4HAAA,CAAA,cAAW;;;;;;;;;;;;;;;;;;;;;;wEAOnB,KAAK,KAAK,CAAC,CAAC,aAAa,EAAE,MAAM,UAAU,CAAC,mBAC3C,sSAAC;4EAAI,WAAU;;8FACb,sSAAC;oFAAI,WAAU;;wFACZ,KAAK,KAAK,CAAC,CAAC,aAAa,EAAE,MAAM,YAAY,CAAC,GAAG,0BAChD,sSAAC;4FAAI,WAAU;sGACb,cAAA,sSAAC;gGACC,KAAK,KAAK,KAAK,CAAC,CAAC,aAAa,EAAE,MAAM,YAAY,CAAC,GAAG;gGACtD,KAAI;gGACJ,WAAU;gGACV,SAAS,CAAC;oGACP,EAAE,MAAM,CAAsB,GAAG,GAAG;gGACvC;;;;;;;;;;;sGAIN,sSAAC;;8GACC,sSAAC;oGAAI,WAAU;8GACZ,KAAK,KAAK,CAAC,CAAC,aAAa,EAAE,MAAM,YAAY,CAAC,GAAG,eAAe;;;;;;8GAEnE,sSAAC;oGAAI,WAAU;8GACZ,KAAK,KAAK,CAAC,CAAC,aAAa,EAAE,MAAM,YAAY,CAAC,GAAG,eAAe;;;;;;;;;;;;;;;;;;8FAIvE,sSAAC;oFAAI,WAAU;;sGACb,sSAAC;4FAAI,WAAU;sGAAgC;;;;;;sGAC/C,sSAAC;4FAAI,WAAU;sGACZ,eACC,KAAK,KAAK,CAAC,CAAC,aAAa,EAAE,MAAM,MAAM,CAAC,IACxC,KAAK,KAAK,CAAC,CAAC,aAAa,EAAE,MAAM,SAAS,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;2DAjI/C;;;;;oDA2IX,CAAC,CAAC,KAAK,KAAK,CAAC,mBAAmB,KAAK,KAAK,CAAC,gBAAgB,MAAM,KAAK,CAAC,mBACtE,sSAAC;wDAAI,WAAU;;0EACb,sSAAC;gEAAE,WAAU;0EAAwB;;;;;;4DACpC,SAAS,wBACR,sSAAC,8HAAA,CAAA,SAAM;gEACL,MAAK;gEACL,SAAQ;gEACR,MAAK;gEACL,WAAU;gEACV,SAAS;0EACV;;;;;;;;;;;;;;;;;;;;;;;;kDAUX,sSAAC;wCAAI,WAAU;;0DACb,sSAAC;gDAAG,WAAU;0DAAmB;;;;;;0DACjC,sSAAC;gDAAI,WAAU;;kEAEb,sSAAC;wDAAI,WAAU;;0EACb,sSAAC;gEAAI,WAAU;0EAAwB;;;;;;0EACvC,sSAAC;gEAAI,WAAU;;kFACb,sSAAC,yRAAA,CAAA,OAAI;wEAAC,WAAU;;;;;;oEACf,KAAK,KAAK,CAAC,YAAY,YAAY;;;;;;;;;;;;;kEAKxC,sSAAC;wDAAI,WAAU;;0EACb,sSAAC;gEAAI,WAAU;0EAAwB;;;;;;0EACvC,sSAAC;gEAAI,WAAU;;kFACb,sSAAC,6SAAA,CAAA,eAAY;wEAAC,WAAU;;;;;;oEAA+B;;;;;;;;;;;;;oDAM1D,CAAC,cAAc,UAAU,mBACxB;;0EACE,sSAAC;gEAAI,WAAU;;kFACb,sSAAC;wEAAI,WAAU;kFAAwB;;;;;;kFACvC,sSAAC;wEAAI,WAAU;kFACZ,KAAK,KAAK,CAAC,cAAc,4KAAA,CAAA,cAAW,CAAC,OAAO,GAAG,cAC/C,KAAK,KAAK,CAAC,cAAc,4KAAA,CAAA,cAAW,CAAC,UAAU,GAAG,eAClD,KAAK,KAAK,CAAC,cAAc,4KAAA,CAAA,cAAW,CAAC,SAAS,GAAG,eAAe;;;;;;;;;;;;0EAIrE,sSAAC;gEAAI,WAAU;;kFACb,sSAAC;wEAAI,WAAU;kFAAwB;;;;;;kFACvC,sSAAC;wEAAI,WAAU;kFACZ,KAAK,KAAK,CAAC,qBAAqB,4KAAA,CAAA,gBAAa,CAAC,IAAI,GAAG,kBAAkB;;;;;;;;;;;;;;oDAO/E,KAAK,KAAK,CAAC,iBAAiB,SAAS,mBACpC,sSAAC;wDAAI,WAAU;;0EACb,sSAAC;gEAAI,WAAU;0EAA6B;;;;;;0EAC5C,sSAAC;gEAAI,WAAU;0EACZ,KAAK,KAAK,CAAC,gBAAgB,GAAG,CAAC,CAAC,QAAQ;oEACvC,MAAM,cAAc,OAAO,WAAW;oEACtC,MAAM,cAAc,cAAc,YAAY,WAAW,GAAG;oEAC5D,qBACE,sSAAC;wEAAgB,WAAU;;0FACzB,sSAAC;gFAAI,WAAU;;oFACZ;oFAAY;oFAAI,OAAO,QAAQ;;;;;;;0FAElC,sSAAC;0FACE,eAAe,OAAO,KAAK,GAAG,OAAO,QAAQ;;;;;;;uEALxC;;;;;gEASd;;;;;;;;;;;;kEAMN,sSAAC;wDAAI,WAAU;;0EACb,sSAAC;gEAAI,WAAU;0EAAoC;;;;;;0EACnD,sSAAC;gEAAI,WAAU;0EACZ,eAAe;;;;;;;;;;;;;;;;;;;;;;;;kDAMxB,sSAAC,8HAAA,CAAA,eAAY;;0DACX,sSAAC,8HAAA,CAAA,SAAM;gDAAC,MAAK;gDAAS,SAAQ;gDAAU,SAAS;gDAAa,UAAU;0DACrE,aAAa,SAAS;;;;;;4CAExB,CAAC,4BACA,sSAAC,8HAAA,CAAA,SAAM;gDAAC,MAAK;gDAAS,UAAU;;oDAC7B,8BAAgB,sSAAC,wSAAA,CAAA,UAAO;wDAAC,WAAU;;;;;;oDACnC,eAAe,iBAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAWjD,sSAAC,uIAAA,CAAA,cAAW;gBAAC,MAAM;gBAAmB,cAAc;0BAClD,cAAA,sSAAC,uIAAA,CAAA,qBAAkB;;sCACjB,sSAAC,uIAAA,CAAA,oBAAiB;;8CAChB,sSAAC,uIAAA,CAAA,mBAAgB;8CAAC;;;;;;8CAClB,sSAAC,uIAAA,CAAA,yBAAsB;8CAAC;;;;;;;;;;;;sCAI1B,sSAAC,uIAAA,CAAA,oBAAiB;;8CAChB,sSAAC,uIAAA,CAAA,oBAAiB;8CAAC;;;;;;8CACnB,sSAAC,uIAAA,CAAA,oBAAiB;oCAChB,SAAS;wCACP;wCACA,qBAAqB;oCACvB;oCACA,WAAU;8CACX;;;;;;;;;;;;;;;;;;;;;;;;;AAQb;GAjrBgB;;QAYD,0PAAA,CAAA,UAAO;;;KAZN", "debugId": null}}, {"offset": {"line": 7117, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/bloomgoo/gold-exchange-platform/frontend/components/common/admin/ecom-orders/components/float-delete-button.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { useState } from 'react';\r\nimport { Trash2 } from 'lucide-react';\r\nimport { Button } from '@/components/ui/button';\r\nimport {\r\n  AlertDialog,\r\n  AlertDialogAction,\r\n  AlertDialogCancel,\r\n  AlertDialogContent,\r\n  AlertDialogDescription,\r\n  AlertDialogFooter,\r\n  AlertDialogHeader,\r\n  AlertDialogTitle,\r\n} from '@/components/ui/alert-dialog';\r\n\r\ninterface FloatDeleteButtonProps {\r\n  selectedCount: number;\r\n  onDelete: () => void;\r\n}\r\n\r\nexport function FloatDeleteButton({ selectedCount, onDelete }: FloatDeleteButtonProps) {\r\n  const [showConfirmDialog, setShowConfirmDialog] = useState(false);\r\n\r\n  if (selectedCount === 0) {\r\n    return null;\r\n  }\r\n\r\n  return (\r\n    <>\r\n      <div className=\"fixed bottom-20 right-6 z-50 animate-in fade-in slide-in-from-bottom-5 duration-300\">\r\n        <Button\r\n          onClick={() => setShowConfirmDialog(true)}\r\n          className=\"rounded-full shadow-lg px-2 py-2 h-auto bg-destructive hover:bg-destructive/90\"\r\n        >\r\n          <Trash2 className=\"mr-1 h-5 w-5\" />\r\n          Xóa {selectedCount} lệnh đã chọn\r\n        </Button>\r\n      </div>\r\n\r\n      <AlertDialog open={showConfirmDialog} onOpenChange={setShowConfirmDialog}>\r\n        <AlertDialogContent>\r\n          <AlertDialogHeader>\r\n            <AlertDialogTitle>Xác nhận xóa</AlertDialogTitle>\r\n            <AlertDialogDescription>\r\n              Bạn có chắc chắn muốn xóa {selectedCount} lệnh đã chọn không? Hành động này không thể hoàn tác.\r\n            </AlertDialogDescription>\r\n          </AlertDialogHeader>\r\n          <AlertDialogFooter>\r\n            <AlertDialogCancel>Hủy</AlertDialogCancel>\r\n            <AlertDialogAction\r\n              onClick={() => {\r\n                onDelete();\r\n                setShowConfirmDialog(false);\r\n              }}\r\n              className=\"bg-destructive hover:bg-destructive/90\"\r\n            >\r\n              Xóa\r\n            </AlertDialogAction>\r\n          </AlertDialogFooter>\r\n        </AlertDialogContent>\r\n      </AlertDialog>\r\n    </>\r\n  );\r\n} "], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;;;AALA;;;;;AAqBO,SAAS,kBAAkB,EAAE,aAAa,EAAE,QAAQ,EAA0B;;IACnF,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,sQAAA,CAAA,WAAQ,AAAD,EAAE;IAE3D,IAAI,kBAAkB,GAAG;QACvB,OAAO;IACT;IAEA,qBACE;;0BACE,sSAAC;gBAAI,WAAU;0BACb,cAAA,sSAAC,8HAAA,CAAA,SAAM;oBACL,SAAS,IAAM,qBAAqB;oBACpC,WAAU;;sCAEV,sSAAC,iSAAA,CAAA,SAAM;4BAAC,WAAU;;;;;;wBAAiB;wBAC9B;wBAAc;;;;;;;;;;;;0BAIvB,sSAAC,uIAAA,CAAA,cAAW;gBAAC,MAAM;gBAAmB,cAAc;0BAClD,cAAA,sSAAC,uIAAA,CAAA,qBAAkB;;sCACjB,sSAAC,uIAAA,CAAA,oBAAiB;;8CAChB,sSAAC,uIAAA,CAAA,mBAAgB;8CAAC;;;;;;8CAClB,sSAAC,uIAAA,CAAA,yBAAsB;;wCAAC;wCACK;wCAAc;;;;;;;;;;;;;sCAG7C,sSAAC,uIAAA,CAAA,oBAAiB;;8CAChB,sSAAC,uIAAA,CAAA,oBAAiB;8CAAC;;;;;;8CACnB,sSAAC,uIAAA,CAAA,oBAAiB;oCAChB,SAAS;wCACP;wCACA,qBAAqB;oCACvB;oCACA,WAAU;8CACX;;;;;;;;;;;;;;;;;;;;;;;;;AAQb;GA3CgB;KAAA", "debugId": null}}, {"offset": {"line": 7252, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/bloomgoo/gold-exchange-platform/frontend/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction Card({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card\"\r\n      className={cn(\r\n        \"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CardHeader({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-header\"\r\n      className={cn(\r\n        \"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CardTitle({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-title\"\r\n      className={cn(\"leading-none font-semibold\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CardDescription({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-description\"\r\n      className={cn(\"text-muted-foreground text-sm\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CardAction({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-action\"\r\n      className={cn(\r\n        \"col-start-2 row-span-2 row-start-1 self-start justify-self-end\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CardContent({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-content\"\r\n      className={cn(\"px-6\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CardFooter({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-footer\"\r\n      className={cn(\"flex items-center px-6 [.border-t]:pt-6\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport {\r\n  Card,\r\n  CardHeader,\r\n  CardFooter,\r\n  CardTitle,\r\n  CardAction,\r\n  CardDescription,\r\n  CardContent,\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;AAAA;;;AAEA,SAAS,KAAK,EAAE,SAAS,EAAE,GAAG,OAAoC;IAChE,qBACE,sSAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,+HAAA,CAAA,KAAE,AAAD,EACV,qFACA;QAED,GAAG,KAAK;;;;;;AAGf;KAXS;AAaT,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,sSAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,+HAAA,CAAA,KAAE,AAAD,EACV,8JACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAoC;IACrE,qBACE,sSAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,+HAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC3E,qBACE,sSAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,+HAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,sSAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,+HAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,sSAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,+HAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QACrB,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,sSAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,+HAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf;MARS", "debugId": null}}, {"offset": {"line": 7368, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/bloomgoo/gold-exchange-platform/frontend/components/info-card.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React from 'react';\r\nimport {\r\n  Card,\r\n  CardContent,\r\n  CardDescription,\r\n  CardHeader,\r\n  CardTitle,\r\n} from '@/components/ui/card';\r\nimport { cn } from '@/lib/utils';\r\n\r\ninterface InfoCardProps {\r\n  title: string;\r\n  description?: string;\r\n  children: React.ReactNode;\r\n  className?: string;\r\n}\r\n\r\nexport function InfoCard({\r\n  title,\r\n  description,\r\n  children,\r\n  className = '',\r\n}: InfoCardProps) {\r\n  return (\r\n    <Card className={cn(\"shadow-none border-none py-2\", className)}>\r\n      <CardHeader className=\"border-l-4 border-l-primary pl-4\">\r\n        <CardTitle className=\"text-lg\">{title}</CardTitle>\r\n        {description && <CardDescription>{description}</CardDescription>}\r\n      </CardHeader>\r\n      <CardContent className=\"space-y-4\">\r\n        {children}\r\n      </CardContent>\r\n    </Card>\r\n  );\r\n} "], "names": [], "mappings": ";;;;AAGA;AAOA;AAAA;AAVA;;;;AAmBO,SAAS,SAAS,EACvB,KAAK,EACL,WAAW,EACX,QAAQ,EACR,YAAY,EAAE,EACA;IACd,qBACE,sSAAC,4HAAA,CAAA,OAAI;QAAC,WAAW,CAAA,GAAA,+HAAA,CAAA,KAAE,AAAD,EAAE,gCAAgC;;0BAClD,sSAAC,4HAAA,CAAA,aAAU;gBAAC,WAAU;;kCACpB,sSAAC,4HAAA,CAAA,YAAS;wBAAC,WAAU;kCAAW;;;;;;oBAC/B,6BAAe,sSAAC,4HAAA,CAAA,kBAAe;kCAAE;;;;;;;;;;;;0BAEpC,sSAAC,4HAAA,CAAA,cAAW;gBAAC,WAAU;0BACpB;;;;;;;;;;;;AAIT;KAjBgB", "debugId": null}}, {"offset": {"line": 7434, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/bloomgoo/gold-exchange-platform/frontend/components/ui/table.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction Table({ className, ...props }: React.ComponentProps<\"table\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"table-container\"\r\n      className=\"relative w-full overflow-x-auto\"\r\n    >\r\n      <table\r\n        data-slot=\"table\"\r\n        className={cn(\"w-full caption-bottom text-sm\", className)}\r\n        {...props}\r\n      />\r\n    </div>\r\n  )\r\n}\r\n\r\nfunction TableHeader({ className, ...props }: React.ComponentProps<\"thead\">) {\r\n  return (\r\n    <thead\r\n      data-slot=\"table-header\"\r\n      className={cn(\"[&_tr]:border-b\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction TableBody({ className, ...props }: React.ComponentProps<\"tbody\">) {\r\n  return (\r\n    <tbody\r\n      data-slot=\"table-body\"\r\n      className={cn(\"[&_tr:last-child]:border-0\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction TableFooter({ className, ...props }: React.ComponentProps<\"tfoot\">) {\r\n  return (\r\n    <tfoot\r\n      data-slot=\"table-footer\"\r\n      className={cn(\r\n        \"bg-muted/50 border-t font-medium [&>tr]:last:border-b-0\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction TableRow({ className, ...props }: React.ComponentProps<\"tr\">) {\r\n  return (\r\n    <tr\r\n      data-slot=\"table-row\"\r\n      className={cn(\r\n        \"hover:bg-muted/50 data-[state=selected]:bg-muted border-b transition-colors\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction TableHead({ className, ...props }: React.ComponentProps<\"th\">) {\r\n  return (\r\n    <th\r\n      data-slot=\"table-head\"\r\n      className={cn(\r\n        \"text-foreground h-10 px-2 text-left align-middle font-medium whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction TableCell({ className, ...props }: React.ComponentProps<\"td\">) {\r\n  return (\r\n    <td\r\n      data-slot=\"table-cell\"\r\n      className={cn(\r\n        \"p-2 align-middle whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction TableCaption({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<\"caption\">) {\r\n  return (\r\n    <caption\r\n      data-slot=\"table-caption\"\r\n      className={cn(\"text-muted-foreground mt-4 text-sm\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport {\r\n  Table,\r\n  TableHeader,\r\n  TableBody,\r\n  TableFooter,\r\n  TableHead,\r\n  TableRow,\r\n  TableCell,\r\n  TableCaption,\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;;AAIA;AAAA;AAJA;;;AAMA,SAAS,MAAM,EAAE,SAAS,EAAE,GAAG,OAAsC;IACnE,qBACE,sSAAC;QACC,aAAU;QACV,WAAU;kBAEV,cAAA,sSAAC;YACC,aAAU;YACV,WAAW,CAAA,GAAA,+HAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;YAC9C,GAAG,KAAK;;;;;;;;;;;AAIjB;KAbS;AAeT,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAsC;IACzE,qBACE,sSAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,+HAAA,CAAA,KAAE,AAAD,EAAE,mBAAmB;QAChC,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAsC;IACvE,qBACE,sSAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,+HAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAsC;IACzE,qBACE,sSAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,+HAAA,CAAA,KAAE,AAAD,EACV,2DACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,SAAS,EAAE,SAAS,EAAE,GAAG,OAAmC;IACnE,qBACE,sSAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,+HAAA,CAAA,KAAE,AAAD,EACV,+EACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAmC;IACpE,qBACE,sSAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,+HAAA,CAAA,KAAE,AAAD,EACV,sJACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAmC;IACpE,qBACE,sSAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,+HAAA,CAAA,KAAE,AAAD,EACV,0GACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,aAAa,EACpB,SAAS,EACT,GAAG,OAC6B;IAChC,qBACE,sSAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,+HAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QACnD,GAAG,KAAK;;;;;;AAGf;MAXS", "debugId": null}}, {"offset": {"line": 7573, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/bloomgoo/gold-exchange-platform/frontend/components/common/admin/ecom-orders/ecom-order-detail-sheet.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { useState, useEffect } from 'react';\r\nimport { But<PERSON> } from \"@/components/ui/button\";\r\nimport {\r\n  Sheet,\r\n  SheetContent,\r\n  SheetDescription,\r\n  SheetFooter,\r\n  Sheet<PERSON>eader,\r\n  SheetTitle,\r\n} from \"@/components/ui/sheet\";\r\nimport { Separator } from \"@/components/ui/separator\";\r\nimport { Badge } from \"@/components/ui/badge\";\r\nimport { ScrollArea } from \"@/components/ui/scroll-area\";\r\nimport { UserHoverCard } from \"@/components/common/user/user-hover-card\";\r\nimport { Edit, Package, User, Pencil, Loader2, FileText, Calendar, DollarSign, BarChart2, Clock } from \"lucide-react\";\r\nimport { format, isValid } from \"date-fns\";\r\nimport { vi } from \"date-fns/locale\";\r\nimport { EcomOrder, OrderStatus, PaymentStatus } from \"./type/ecom-orders\";\r\nimport { api } from '@/lib/api';\r\nimport { toast } from 'sonner';\r\nimport { InfoCard } from '@/components/info-card';\r\nimport {\r\n  Table,\r\n  TableBody,\r\n  TableCaption,\r\n  TableCell,\r\n  TableHead,\r\n  TableHeader,\r\n  TableRow,\r\n} from \"@/components/ui/table\";\r\n\r\n// Format currency with VND\r\nconst formatCurrency = (amount: number | undefined) => {\r\n  if (amount === undefined || amount === null) return \"---\";\r\n  return new Intl.NumberFormat('vi-VN', {\r\n    maximumFractionDigits: 0\r\n  }).format(amount);\r\n};\r\n\r\n// Format date from ISO string\r\nconst formatDate = (dateStr: string | null | undefined) => {\r\n  if (!dateStr) return \"---\";\r\n  const date = new Date(dateStr);\r\n  if (!isValid(date)) return \"Không hợp lệ\";\r\n  try {\r\n    return format(date, \"dd/MM/yyyy HH:mm\", { locale: vi });\r\n  } catch (error) {\r\n    return \"Không hợp lệ\";\r\n  }\r\n};\r\n\r\n// Get color for order status badge\r\nconst getStatusColor = (status: OrderStatus) => {\r\n  switch (status) {\r\n    case OrderStatus.PENDING:\r\n      return 'bg-yellow-100 text-yellow-800 hover:bg-yellow-200';\r\n    case OrderStatus.PROCESSING:\r\n      return 'bg-blue-100 text-blue-800 hover:bg-blue-200';\r\n    case OrderStatus.COMPLETED:\r\n      return 'bg-green-100 text-green-800 hover:bg-green-200';\r\n    case OrderStatus.CANCELLED:\r\n      return 'bg-red-100 text-red-800 hover:bg-red-200';\r\n    default:\r\n      return 'bg-gray-100 text-gray-800 hover:bg-gray-200';\r\n  }\r\n};\r\n\r\n// Order status in Vietnamese\r\nconst getStatusName = (status: OrderStatus) => {\r\n  switch (status) {\r\n    case OrderStatus.PENDING:\r\n      return 'Chờ xử lý';\r\n    case OrderStatus.PROCESSING:\r\n      return 'Đang xử lý';\r\n    case OrderStatus.COMPLETED:\r\n      return 'Hoàn thành';\r\n    case OrderStatus.CANCELLED:\r\n      return 'Đã hủy';\r\n    default:\r\n      return status;\r\n  }\r\n};\r\n\r\n// Get color for payment status badge\r\nconst getPaymentStatusColor = (status: PaymentStatus) => {\r\n  switch (status) {\r\n    case PaymentStatus.PAID:\r\n      return 'bg-green-100 text-green-800 hover:bg-green-200';\r\n    case PaymentStatus.UNPAID:\r\n      return 'bg-red-100 text-red-800 hover:bg-red-200';\r\n    default:\r\n      return 'bg-gray-100 text-gray-800 hover:bg-gray-200';\r\n  }\r\n};\r\n\r\n// Payment status in Vietnamese\r\nconst getPaymentStatusName = (status: PaymentStatus) => {\r\n  switch (status) {\r\n    case PaymentStatus.PAID:\r\n      return 'Đã thanh toán';\r\n    case PaymentStatus.UNPAID:\r\n      return 'Chưa thanh toán';\r\n    default:\r\n      return status;\r\n  }\r\n};\r\n\r\n// Define InfoCardItem component\r\ninterface InfoCardItemProps {\r\n  label: React.ReactNode;\r\n  value: React.ReactNode;\r\n  className?: string;\r\n}\r\n\r\nfunction InfoCardItem({ label, value, className }: InfoCardItemProps) {\r\n  return (\r\n    <div className={`space-y-1 ${className}`}>\r\n      <div className=\"text-xs font-medium text-muted-foreground\">{label}</div>\r\n      <div className=\"text-md\">{value}</div>\r\n    </div>\r\n  );\r\n}\r\n\r\ninterface EcomOrderDetailSheetProps {\r\n  order: EcomOrder | null;\r\n  isOpen: boolean;\r\n  onClose: () => void;\r\n  onEdit?: (order: EcomOrder) => void;\r\n}\r\n\r\nexport function EcomOrderDetailSheet({ order: initialOrder, isOpen, onClose, onEdit }: EcomOrderDetailSheetProps) {\r\n  const [order, setOrder] = useState<EcomOrder | null>(initialOrder);\r\n  const [loading, setLoading] = useState<boolean>(false);\r\n\r\n  // Fetch order details when sheet is opened\r\n  useEffect(() => {\r\n    if (isOpen && initialOrder?.id) {\r\n      setLoading(true);\r\n      api.get<EcomOrder>(`ecom-orders/${initialOrder.id}`)\r\n        .then(response => {\r\n          setOrder(response);\r\n          setLoading(false);\r\n        })\r\n        .catch(error => {\r\n          console.error('Error fetching order details:', error);\r\n          toast.error('Không thể tải thông tin chi tiết đơn hàng');\r\n          setLoading(false);\r\n        });\r\n    }\r\n  }, [isOpen, initialOrder?.id]);\r\n\r\n  if (!order) return null;\r\n\r\n  // Calculate total products\r\n  const getTotalProducts = () => {\r\n    return order.orderDetails?.length || 0;\r\n  };\r\n\r\n  // Calculate total quantity\r\n  const getTotalQuantity = () => {\r\n    if (!order.orderDetails || order.orderDetails.length === 0) return 0;\r\n    return order.orderDetails.reduce((sum, detail) => sum + (detail.quantity || 0), 0);\r\n  };\r\n\r\n  return (\r\n    <Sheet open={isOpen} onOpenChange={(open) => !open && onClose()}>\r\n      <SheetContent className=\"w-full sm:max-w-xl md:max-w-2xl lg:max-w-3xl flex flex-col p-0\">\r\n        {/* Header cố định */}\r\n        <SheetHeader className=\"px-0 py-0 border-b\">\r\n          <div className=\"flex items-center gap-4 px-4 py-2\">\r\n            <div className=\"h-16 w-16 shrink-0 flex items-center justify-center\">\r\n              {loading ? (\r\n                <Loader2 className=\"h-8 w-8 animate-spin text-muted-foreground\" />\r\n              ) : (\r\n                <Package className=\"h-8 w-8 text-primary\" />\r\n              )}\r\n            </div>\r\n            <div className=\"flex-1\">\r\n              <SheetTitle className=\"text-md font-semibold\">\r\n                {loading ? (\r\n                  <div className=\"flex items-center gap-2\">\r\n                    <Loader2 className=\"h-4 w-4 animate-spin\" />\r\n                    <span>Đang tải...</span>\r\n                  </div>\r\n                ) : (\r\n                  `Đơn hàng #${order.orderNumber || order.id?.substring(0, 8)}`\r\n                )}\r\n              </SheetTitle>\r\n              <SheetDescription>\r\n                {loading ? (\r\n                  <span className=\"flex items-center gap-2\">\r\n                    <Loader2 className=\"h-3 w-3 animate-spin\" />\r\n                    <span>Đang tải thông tin...</span>\r\n                  </span>\r\n                ) : (\r\n                  <span className=\"flex items-center gap-2\">\r\n                    <Badge className={getStatusColor(order.status)}>\r\n                      {getStatusName(order.status)}\r\n                    </Badge>\r\n                    <Badge className={getPaymentStatusColor(order.paymentStatus)}>\r\n                      {getPaymentStatusName(order.paymentStatus)}\r\n                    </Badge>\r\n                  </span>\r\n                )}\r\n              </SheetDescription>\r\n            </div>\r\n          </div>\r\n\r\n          {/* Đường kẻ phân tách */}\r\n          <div className=\"h-px w-full bg-border\"></div>\r\n\r\n          {/* Các nút chức năng */}\r\n          {onEdit && (\r\n            <div className=\"flex items-center gap-2 px-4 mb-2\">\r\n              <Button\r\n                variant=\"outline\"\r\n                size=\"sm\"\r\n                className=\"flex items-center gap-1\"\r\n                onClick={() => onEdit(order)}\r\n              >\r\n                <Pencil className=\"h-3.5 w-3.5\" />\r\n                <span>Chỉnh sửa</span>\r\n              </Button>\r\n            </div>\r\n          )}\r\n        </SheetHeader>\r\n\r\n        {/* Body có thể scroll */}\r\n        <div className=\"flex-1 overflow-y-auto px-4\">\r\n          {loading ? (\r\n            <div className=\"flex flex-col items-center justify-center h-full py-8 gap-4\">\r\n              <Loader2 className=\"h-8 w-8 animate-spin text-primary\" />\r\n              <p className=\"text-muted-foreground\">Đang tải thông tin chi tiết...</p>\r\n            </div>\r\n          ) : (\r\n            <>\r\n              {/* Thông tin cơ bản */}\r\n              <InfoCard\r\n                title=\"Thông tin đơn hàng\"\r\n                description=\"Thông tin chi tiết về đơn hàng\"\r\n                className=\"py-4\"\r\n              >\r\n                <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\r\n                  <InfoCardItem\r\n                    label={<div className=\"flex items-center gap-1\"><User className=\"h-3.5 w-3.5\" /> Khách hàng</div>}\r\n                    value={order.user\r\n                      ? (\r\n                        <UserHoverCard user={order.user} showAvatar={true} size=\"sm\">\r\n                          <div className=\"max-w-[120px] overflow-hidden\">\r\n                            <div className=\"truncate\">{order.user.fullName || order.user.username || order.user.email}</div>\r\n                          </div>\r\n                        </UserHoverCard>\r\n                      )\r\n                      : `User ID: ${order.userId ? order.userId.substring(0, 8) + '...' : 'Không xác định'}`}\r\n                  />\r\n                  <InfoCardItem\r\n                    label={<div className=\"flex items-center gap-1\"><FileText className=\"h-3.5 w-3.5\" /> Mã đơn hàng</div>}\r\n                    value={order.orderNumber || '---'}\r\n                  />\r\n                  <InfoCardItem\r\n                    label={<div className=\"flex items-center gap-1\"><DollarSign className=\"h-3.5 w-3.5\" /> Tổng giá trị</div>}\r\n                    value={<div className=\"flex items-center gap-1\">\r\n                      {formatCurrency(order.totalAmount)} VND\r\n                    </div>}\r\n                  />\r\n                  <InfoCardItem\r\n                    label={<div className=\"flex items-center gap-1\"><BarChart2 className=\"h-3.5 w-3.5\" /> Số lượng sản phẩm</div>}\r\n                    value={getTotalProducts()}\r\n                  />\r\n                  <InfoCardItem\r\n                    label={<div className=\"flex items-center gap-1\"><BarChart2 className=\"h-3.5 w-3.5\" /> Tổng số lượng</div>}\r\n                    value={getTotalQuantity()}\r\n                  />\r\n                  {order.notes && (\r\n                    <InfoCardItem\r\n                      label={<div className=\"flex items-center gap-1\"><FileText className=\"h-3.5 w-3.5\" /> Ghi chú</div>}\r\n                      value={order.notes}\r\n                      className=\"col-span-2\"\r\n                    />\r\n                  )}\r\n                </div>\r\n              </InfoCard>\r\n\r\n              {/* Thông tin thời gian */}\r\n              <InfoCard\r\n                title=\"Thông tin thời gian\"\r\n                description=\"Thời gian tạo và cập nhật đơn hàng\"\r\n                className=\"py-4\"\r\n              >\r\n                <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\r\n                  <InfoCardItem\r\n                    label={<div className=\"flex items-center gap-1\"><Calendar className=\"h-3.5 w-3.5\" /> Ngày tạo</div>}\r\n                    value={formatDate(order.createdAt)}\r\n                  />\r\n                  <InfoCardItem\r\n                    label={<div className=\"flex items-center gap-1\"><Clock className=\"h-3.5 w-3.5\" /> Cập nhật lần cuối</div>}\r\n                    value={formatDate(order.updatedAt)}\r\n                  />\r\n                  <InfoCardItem\r\n                    label={<div className=\"flex items-center gap-1\"><User className=\"h-3.5 w-3.5\" /> Người tạo</div>}\r\n                    value={order.creator ? (\r\n                      <UserHoverCard user={order.creator} showAvatar={true} size=\"sm\">\r\n                        <div className=\"max-w-[120px] overflow-hidden\">\r\n                          <div className=\"truncate\">{order.creator.fullName || order.creator.username || order.creator.email || 'Không xác định'}</div>\r\n                        </div>\r\n                      </UserHoverCard>\r\n                    ) : (\r\n                      <span>{order.createdBy || 'Không có thông tin'}</span>\r\n                    )}\r\n                  />\r\n                  <InfoCardItem\r\n                    label={<div className=\"flex items-center gap-1\"><User className=\"h-3.5 w-3.5\" /> Người cập nhật</div>}\r\n                    value={order.updater ? (\r\n                      <UserHoverCard user={order.updater} showAvatar={true} size=\"sm\">\r\n                        <div className=\"max-w-[120px] overflow-hidden\">\r\n                          <div className=\"truncate\">{order.updater.fullName || order.updater.username || order.updater.email || 'Không xác định'}</div>\r\n                        </div>\r\n                      </UserHoverCard>\r\n                    ) : (\r\n                      <span>{order.updatedBy || 'Không có thông tin'}</span>\r\n                    )}\r\n                  />\r\n                </div>\r\n              </InfoCard>\r\n\r\n              {/* Chi tiết sản phẩm */}\r\n              {order.orderDetails && order.orderDetails.length > 0 && (\r\n                <InfoCard\r\n                  title=\"Chi tiết sản phẩm\"\r\n                  description={`${order.orderDetails.length} sản phẩm trong đơn hàng này`}\r\n                  className=\"py-4\"\r\n                >\r\n                  <div className=\"overflow-x-auto\">\r\n                    <Table>\r\n                      <TableCaption>Tổng cộng: {order.orderDetails.length} sản phẩm</TableCaption>\r\n                      <TableHeader>\r\n                        <TableRow>\r\n                          <TableHead className=\"w-[60px]\">STT</TableHead>\r\n                          <TableHead>Sản phẩm</TableHead>\r\n                          <TableHead className=\"text-right\">Đơn giá (VND)</TableHead>\r\n                          <TableHead className=\"text-right\">Số lượng</TableHead>\r\n                          <TableHead className=\"text-right\">Thành tiền (VND)</TableHead>\r\n                        </TableRow>\r\n                      </TableHeader>\r\n                      <TableBody>\r\n                        {order.orderDetails.map((detail, index) => (\r\n                          <TableRow key={detail.id || `product-${index}`}>\r\n                            <TableCell>{index + 1}</TableCell>\r\n                            <TableCell className=\"font-medium\">\r\n                              {detail.product?.productName || `Sản phẩm #${index + 1}`}\r\n                            </TableCell>\r\n                            <TableCell className=\"text-right\">\r\n                              {formatCurrency(detail.unitPrice)}\r\n                            </TableCell>\r\n                            <TableCell className=\"text-right\">\r\n                              {detail.quantity.toLocaleString()}\r\n                            </TableCell>\r\n                            <TableCell className=\"text-right\">\r\n                              {formatCurrency(detail.totalPrice)}\r\n                            </TableCell>\r\n                          </TableRow>\r\n                        ))}\r\n                        {order.orderDetails.length > 1 && (\r\n                          <TableRow className=\"font-medium bg-muted/50\">\r\n                            <TableCell colSpan={3} className=\"text-right\">Tổng cộng:</TableCell>\r\n                            <TableCell className=\"text-right\">\r\n                              {order.orderDetails.reduce((sum, detail) => sum + detail.quantity, 0).toLocaleString()}\r\n                            </TableCell>\r\n                            <TableCell className=\"text-right\">\r\n                              {formatCurrency(order.totalAmount)}\r\n                            </TableCell>\r\n                          </TableRow>\r\n                        )}\r\n                      </TableBody>\r\n                    </Table>\r\n                  </div>\r\n                </InfoCard>\r\n              )}\r\n            </>\r\n          )}\r\n        </div>\r\n\r\n      \r\n      </SheetContent>\r\n    </Sheet>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AASA;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAvBA;;;;;;;;;;;;;;AAiCA,2BAA2B;AAC3B,MAAM,iBAAiB,CAAC;IACtB,IAAI,WAAW,aAAa,WAAW,MAAM,OAAO;IACpD,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;QACpC,uBAAuB;IACzB,GAAG,MAAM,CAAC;AACZ;AAEA,8BAA8B;AAC9B,MAAM,aAAa,CAAC;IAClB,IAAI,CAAC,SAAS,OAAO;IACrB,MAAM,OAAO,IAAI,KAAK;IACtB,IAAI,CAAC,CAAA,GAAA,iMAAA,CAAA,UAAO,AAAD,EAAE,OAAO,OAAO;IAC3B,IAAI;QACF,OAAO,CAAA,GAAA,gNAAA,CAAA,SAAM,AAAD,EAAE,MAAM,oBAAoB;YAAE,QAAQ,sMAAA,CAAA,KAAE;QAAC;IACvD,EAAE,OAAO,OAAO;QACd,OAAO;IACT;AACF;AAEA,mCAAmC;AACnC,MAAM,iBAAiB,CAAC;IACtB,OAAQ;QACN,KAAK,4KAAA,CAAA,cAAW,CAAC,OAAO;YACtB,OAAO;QACT,KAAK,4KAAA,CAAA,cAAW,CAAC,UAAU;YACzB,OAAO;QACT,KAAK,4KAAA,CAAA,cAAW,CAAC,SAAS;YACxB,OAAO;QACT,KAAK,4KAAA,CAAA,cAAW,CAAC,SAAS;YACxB,OAAO;QACT;YACE,OAAO;IACX;AACF;AAEA,6BAA6B;AAC7B,MAAM,gBAAgB,CAAC;IACrB,OAAQ;QACN,KAAK,4KAAA,CAAA,cAAW,CAAC,OAAO;YACtB,OAAO;QACT,KAAK,4KAAA,CAAA,cAAW,CAAC,UAAU;YACzB,OAAO;QACT,KAAK,4KAAA,CAAA,cAAW,CAAC,SAAS;YACxB,OAAO;QACT,KAAK,4KAAA,CAAA,cAAW,CAAC,SAAS;YACxB,OAAO;QACT;YACE,OAAO;IACX;AACF;AAEA,qCAAqC;AACrC,MAAM,wBAAwB,CAAC;IAC7B,OAAQ;QACN,KAAK,4KAAA,CAAA,gBAAa,CAAC,IAAI;YACrB,OAAO;QACT,KAAK,4KAAA,CAAA,gBAAa,CAAC,MAAM;YACvB,OAAO;QACT;YACE,OAAO;IACX;AACF;AAEA,+BAA+B;AAC/B,MAAM,uBAAuB,CAAC;IAC5B,OAAQ;QACN,KAAK,4KAAA,CAAA,gBAAa,CAAC,IAAI;YACrB,OAAO;QACT,KAAK,4KAAA,CAAA,gBAAa,CAAC,MAAM;YACvB,OAAO;QACT;YACE,OAAO;IACX;AACF;AASA,SAAS,aAAa,EAAE,KAAK,EAAE,KAAK,EAAE,SAAS,EAAqB;IAClE,qBACE,sSAAC;QAAI,WAAW,CAAC,UAAU,EAAE,WAAW;;0BACtC,sSAAC;gBAAI,WAAU;0BAA6C;;;;;;0BAC5D,sSAAC;gBAAI,WAAU;0BAAW;;;;;;;;;;;;AAGhC;KAPS;AAgBF,SAAS,qBAAqB,EAAE,OAAO,YAAY,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAA6B;;IAC9G,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,sQAAA,CAAA,WAAQ,AAAD,EAAoB;IACrD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,sQAAA,CAAA,WAAQ,AAAD,EAAW;IAEhD,2CAA2C;IAC3C,CAAA,GAAA,sQAAA,CAAA,YAAS,AAAD;0CAAE;YACR,IAAI,UAAU,cAAc,IAAI;gBAC9B,WAAW;gBACX,6GAAA,CAAA,MAAG,CAAC,GAAG,CAAY,CAAC,YAAY,EAAE,aAAa,EAAE,EAAE,EAChD,IAAI;sDAAC,CAAA;wBACJ,SAAS;wBACT,WAAW;oBACb;qDACC,KAAK;sDAAC,CAAA;wBACL,QAAQ,KAAK,CAAC,iCAAiC;wBAC/C,2QAAA,CAAA,QAAK,CAAC,KAAK,CAAC;wBACZ,WAAW;oBACb;;YACJ;QACF;yCAAG;QAAC;QAAQ,cAAc;KAAG;IAE7B,IAAI,CAAC,OAAO,OAAO;IAEnB,2BAA2B;IAC3B,MAAM,mBAAmB;QACvB,OAAO,MAAM,YAAY,EAAE,UAAU;IACvC;IAEA,2BAA2B;IAC3B,MAAM,mBAAmB;QACvB,IAAI,CAAC,MAAM,YAAY,IAAI,MAAM,YAAY,CAAC,MAAM,KAAK,GAAG,OAAO;QACnE,OAAO,MAAM,YAAY,CAAC,MAAM,CAAC,CAAC,KAAK,SAAW,MAAM,CAAC,OAAO,QAAQ,IAAI,CAAC,GAAG;IAClF;IAEA,qBACE,sSAAC,6HAAA,CAAA,QAAK;QAAC,MAAM;QAAQ,cAAc,CAAC,OAAS,CAAC,QAAQ;kBACpD,cAAA,sSAAC,6HAAA,CAAA,eAAY;YAAC,WAAU;;8BAEtB,sSAAC,6HAAA,CAAA,cAAW;oBAAC,WAAU;;sCACrB,sSAAC;4BAAI,WAAU;;8CACb,sSAAC;oCAAI,WAAU;8CACZ,wBACC,sSAAC,wSAAA,CAAA,UAAO;wCAAC,WAAU;;;;;6DAEnB,sSAAC,+RAAA,CAAA,UAAO;wCAAC,WAAU;;;;;;;;;;;8CAGvB,sSAAC;oCAAI,WAAU;;sDACb,sSAAC,6HAAA,CAAA,aAAU;4CAAC,WAAU;sDACnB,wBACC,sSAAC;gDAAI,WAAU;;kEACb,sSAAC,wSAAA,CAAA,UAAO;wDAAC,WAAU;;;;;;kEACnB,sSAAC;kEAAK;;;;;;;;;;;uDAGR,CAAC,UAAU,EAAE,MAAM,WAAW,IAAI,MAAM,EAAE,EAAE,UAAU,GAAG,IAAI;;;;;;sDAGjE,sSAAC,6HAAA,CAAA,mBAAgB;sDACd,wBACC,sSAAC;gDAAK,WAAU;;kEACd,sSAAC,wSAAA,CAAA,UAAO;wDAAC,WAAU;;;;;;kEACnB,sSAAC;kEAAK;;;;;;;;;;;qEAGR,sSAAC;gDAAK,WAAU;;kEACd,sSAAC,6HAAA,CAAA,QAAK;wDAAC,WAAW,eAAe,MAAM,MAAM;kEAC1C,cAAc,MAAM,MAAM;;;;;;kEAE7B,sSAAC,6HAAA,CAAA,QAAK;wDAAC,WAAW,sBAAsB,MAAM,aAAa;kEACxD,qBAAqB,MAAM,aAAa;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCASrD,sSAAC;4BAAI,WAAU;;;;;;wBAGd,wBACC,sSAAC;4BAAI,WAAU;sCACb,cAAA,sSAAC,8HAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,WAAU;gCACV,SAAS,IAAM,OAAO;;kDAEtB,sSAAC,6RAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;kDAClB,sSAAC;kDAAK;;;;;;;;;;;;;;;;;;;;;;;8BAOd,sSAAC;oBAAI,WAAU;8BACZ,wBACC,sSAAC;wBAAI,WAAU;;0CACb,sSAAC,wSAAA,CAAA,UAAO;gCAAC,WAAU;;;;;;0CACnB,sSAAC;gCAAE,WAAU;0CAAwB;;;;;;;;;;;6CAGvC;;0CAEE,sSAAC,8HAAA,CAAA,WAAQ;gCACP,OAAM;gCACN,aAAY;gCACZ,WAAU;0CAEV,cAAA,sSAAC;oCAAI,WAAU;;sDACb,sSAAC;4CACC,qBAAO,sSAAC;gDAAI,WAAU;;kEAA0B,sSAAC,yRAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;oDAAgB;;;;;;;4CAChF,OAAO,MAAM,IAAI,iBAEb,sSAAC,yJAAA,CAAA,gBAAa;gDAAC,MAAM,MAAM,IAAI;gDAAE,YAAY;gDAAM,MAAK;0DACtD,cAAA,sSAAC;oDAAI,WAAU;8DACb,cAAA,sSAAC;wDAAI,WAAU;kEAAY,MAAM,IAAI,CAAC,QAAQ,IAAI,MAAM,IAAI,CAAC,QAAQ,IAAI,MAAM,IAAI,CAAC,KAAK;;;;;;;;;;;;;;;yDAI7F,CAAC,SAAS,EAAE,MAAM,MAAM,GAAG,MAAM,MAAM,CAAC,SAAS,CAAC,GAAG,KAAK,QAAQ,kBAAkB;;;;;;sDAE1F,sSAAC;4CACC,qBAAO,sSAAC;gDAAI,WAAU;;kEAA0B,sSAAC,qSAAA,CAAA,WAAQ;wDAAC,WAAU;;;;;;oDAAgB;;;;;;;4CACpF,OAAO,MAAM,WAAW,IAAI;;;;;;sDAE9B,sSAAC;4CACC,qBAAO,sSAAC;gDAAI,WAAU;;kEAA0B,sSAAC,ySAAA,CAAA,aAAU;wDAAC,WAAU;;;;;;oDAAgB;;;;;;;4CACtF,qBAAO,sSAAC;gDAAI,WAAU;;oDACnB,eAAe,MAAM,WAAW;oDAAE;;;;;;;;;;;;sDAGvC,sSAAC;4CACC,qBAAO,sSAAC;gDAAI,WAAU;;kEAA0B,sSAAC,uTAAA,CAAA,YAAS;wDAAC,WAAU;;;;;;oDAAgB;;;;;;;4CACrF,OAAO;;;;;;sDAET,sSAAC;4CACC,qBAAO,sSAAC;gDAAI,WAAU;;kEAA0B,sSAAC,uTAAA,CAAA,YAAS;wDAAC,WAAU;;;;;;oDAAgB;;;;;;;4CACrF,OAAO;;;;;;wCAER,MAAM,KAAK,kBACV,sSAAC;4CACC,qBAAO,sSAAC;gDAAI,WAAU;;kEAA0B,sSAAC,qSAAA,CAAA,WAAQ;wDAAC,WAAU;;;;;;oDAAgB;;;;;;;4CACpF,OAAO,MAAM,KAAK;4CAClB,WAAU;;;;;;;;;;;;;;;;;0CAOlB,sSAAC,8HAAA,CAAA,WAAQ;gCACP,OAAM;gCACN,aAAY;gCACZ,WAAU;0CAEV,cAAA,sSAAC;oCAAI,WAAU;;sDACb,sSAAC;4CACC,qBAAO,sSAAC;gDAAI,WAAU;;kEAA0B,sSAAC,iSAAA,CAAA,WAAQ;wDAAC,WAAU;;;;;;oDAAgB;;;;;;;4CACpF,OAAO,WAAW,MAAM,SAAS;;;;;;sDAEnC,sSAAC;4CACC,qBAAO,sSAAC;gDAAI,WAAU;;kEAA0B,sSAAC,2RAAA,CAAA,QAAK;wDAAC,WAAU;;;;;;oDAAgB;;;;;;;4CACjF,OAAO,WAAW,MAAM,SAAS;;;;;;sDAEnC,sSAAC;4CACC,qBAAO,sSAAC;gDAAI,WAAU;;kEAA0B,sSAAC,yRAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;oDAAgB;;;;;;;4CAChF,OAAO,MAAM,OAAO,iBAClB,sSAAC,yJAAA,CAAA,gBAAa;gDAAC,MAAM,MAAM,OAAO;gDAAE,YAAY;gDAAM,MAAK;0DACzD,cAAA,sSAAC;oDAAI,WAAU;8DACb,cAAA,sSAAC;wDAAI,WAAU;kEAAY,MAAM,OAAO,CAAC,QAAQ,IAAI,MAAM,OAAO,CAAC,QAAQ,IAAI,MAAM,OAAO,CAAC,KAAK,IAAI;;;;;;;;;;;;;;;uEAI1G,sSAAC;0DAAM,MAAM,SAAS,IAAI;;;;;;;;;;;sDAG9B,sSAAC;4CACC,qBAAO,sSAAC;gDAAI,WAAU;;kEAA0B,sSAAC,yRAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;oDAAgB;;;;;;;4CAChF,OAAO,MAAM,OAAO,iBAClB,sSAAC,yJAAA,CAAA,gBAAa;gDAAC,MAAM,MAAM,OAAO;gDAAE,YAAY;gDAAM,MAAK;0DACzD,cAAA,sSAAC;oDAAI,WAAU;8DACb,cAAA,sSAAC;wDAAI,WAAU;kEAAY,MAAM,OAAO,CAAC,QAAQ,IAAI,MAAM,OAAO,CAAC,QAAQ,IAAI,MAAM,OAAO,CAAC,KAAK,IAAI;;;;;;;;;;;;;;;uEAI1G,sSAAC;0DAAM,MAAM,SAAS,IAAI;;;;;;;;;;;;;;;;;;;;;;4BAOjC,MAAM,YAAY,IAAI,MAAM,YAAY,CAAC,MAAM,GAAG,mBACjD,sSAAC,8HAAA,CAAA,WAAQ;gCACP,OAAM;gCACN,aAAa,GAAG,MAAM,YAAY,CAAC,MAAM,CAAC,4BAA4B,CAAC;gCACvE,WAAU;0CAEV,cAAA,sSAAC;oCAAI,WAAU;8CACb,cAAA,sSAAC,6HAAA,CAAA,QAAK;;0DACJ,sSAAC,6HAAA,CAAA,eAAY;;oDAAC;oDAAY,MAAM,YAAY,CAAC,MAAM;oDAAC;;;;;;;0DACpD,sSAAC,6HAAA,CAAA,cAAW;0DACV,cAAA,sSAAC,6HAAA,CAAA,WAAQ;;sEACP,sSAAC,6HAAA,CAAA,YAAS;4DAAC,WAAU;sEAAW;;;;;;sEAChC,sSAAC,6HAAA,CAAA,YAAS;sEAAC;;;;;;sEACX,sSAAC,6HAAA,CAAA,YAAS;4DAAC,WAAU;sEAAa;;;;;;sEAClC,sSAAC,6HAAA,CAAA,YAAS;4DAAC,WAAU;sEAAa;;;;;;sEAClC,sSAAC,6HAAA,CAAA,YAAS;4DAAC,WAAU;sEAAa;;;;;;;;;;;;;;;;;0DAGtC,sSAAC,6HAAA,CAAA,YAAS;;oDACP,MAAM,YAAY,CAAC,GAAG,CAAC,CAAC,QAAQ,sBAC/B,sSAAC,6HAAA,CAAA,WAAQ;;8EACP,sSAAC,6HAAA,CAAA,YAAS;8EAAE,QAAQ;;;;;;8EACpB,sSAAC,6HAAA,CAAA,YAAS;oEAAC,WAAU;8EAClB,OAAO,OAAO,EAAE,eAAe,CAAC,UAAU,EAAE,QAAQ,GAAG;;;;;;8EAE1D,sSAAC,6HAAA,CAAA,YAAS;oEAAC,WAAU;8EAClB,eAAe,OAAO,SAAS;;;;;;8EAElC,sSAAC,6HAAA,CAAA,YAAS;oEAAC,WAAU;8EAClB,OAAO,QAAQ,CAAC,cAAc;;;;;;8EAEjC,sSAAC,6HAAA,CAAA,YAAS;oEAAC,WAAU;8EAClB,eAAe,OAAO,UAAU;;;;;;;2DAZtB,OAAO,EAAE,IAAI,CAAC,QAAQ,EAAE,OAAO;;;;;oDAgB/C,MAAM,YAAY,CAAC,MAAM,GAAG,mBAC3B,sSAAC,6HAAA,CAAA,WAAQ;wDAAC,WAAU;;0EAClB,sSAAC,6HAAA,CAAA,YAAS;gEAAC,SAAS;gEAAG,WAAU;0EAAa;;;;;;0EAC9C,sSAAC,6HAAA,CAAA,YAAS;gEAAC,WAAU;0EAClB,MAAM,YAAY,CAAC,MAAM,CAAC,CAAC,KAAK,SAAW,MAAM,OAAO,QAAQ,EAAE,GAAG,cAAc;;;;;;0EAEtF,sSAAC,6HAAA,CAAA,YAAS;gEAAC,WAAU;0EAClB,eAAe,MAAM,WAAW;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAiB/D;GAhQgB;MAAA", "debugId": null}}, {"offset": {"line": 8533, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/bloomgoo/gold-exchange-platform/frontend/components/ui/tabs.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as TabsPrimitive from \"@radix-ui/react-tabs\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction Tabs({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof TabsPrimitive.Root>) {\r\n  return (\r\n    <TabsPrimitive.Root\r\n      data-slot=\"tabs\"\r\n      className={cn(\"flex flex-col gap-2\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction TabsList({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof TabsPrimitive.List>) {\r\n  return (\r\n    <TabsPrimitive.List\r\n      data-slot=\"tabs-list\"\r\n      className={cn(\r\n        \"bg-muted text-muted-foreground inline-flex h-9 w-fit items-center justify-center rounded-lg p-[3px]\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction TabsTrigger({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof TabsPrimitive.Trigger>) {\r\n  return (\r\n    <TabsPrimitive.Trigger\r\n      data-slot=\"tabs-trigger\"\r\n      className={cn(\r\n        \"data-[state=active]:bg-background dark:data-[state=active]:text-foreground focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:outline-ring dark:data-[state=active]:border-input dark:data-[state=active]:bg-input/30 text-foreground dark:text-muted-foreground inline-flex h-[calc(100%-1px)] flex-1 items-center justify-center gap-1.5 rounded-md border border-transparent px-2 py-1 text-sm font-medium whitespace-nowrap transition-[color,box-shadow] focus-visible:ring-[3px] focus-visible:outline-1 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:shadow-sm [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction TabsContent({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof TabsPrimitive.Content>) {\r\n  return (\r\n    <TabsPrimitive.Content\r\n      data-slot=\"tabs-content\"\r\n      className={cn(\"flex-1 outline-none\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport { Tabs, TabsList, TabsTrigger, TabsContent }\r\n"], "names": [], "mappings": ";;;;;;;AAGA;AAEA;AAAA;AALA;;;;AAOA,SAAS,KAAK,EACZ,SAAS,EACT,GAAG,OAC6C;IAChD,qBACE,sSAAC,mRAAA,CAAA,OAAkB;QACjB,aAAU;QACV,WAAW,CAAA,GAAA,+HAAA,CAAA,KAAE,AAAD,EAAE,uBAAuB;QACpC,GAAG,KAAK;;;;;;AAGf;KAXS;AAaT,SAAS,SAAS,EAChB,SAAS,EACT,GAAG,OAC6C;IAChD,qBACE,sSAAC,mRAAA,CAAA,OAAkB;QACjB,aAAU;QACV,WAAW,CAAA,GAAA,+HAAA,CAAA,KAAE,AAAD,EACV,uGACA;QAED,GAAG,KAAK;;;;;;AAGf;MAdS;AAgBT,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,sSAAC,mRAAA,CAAA,UAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,+HAAA,CAAA,KAAE,AAAD,EACV,mqBACA;QAED,GAAG,KAAK;;;;;;AAGf;MAdS;AAgBT,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,sSAAC,mRAAA,CAAA,UAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,+HAAA,CAAA,KAAE,AAAD,EAAE,uBAAuB;QACpC,GAAG,KAAK;;;;;;AAGf;MAXS", "debugId": null}}, {"offset": {"line": 8610, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/bloomgoo/gold-exchange-platform/frontend/components/common/admin/ecom-orders/ecom-orders.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { useState, useEffect, useCallback, useRef } from 'react';\r\nimport { Plus, FileDown } from 'lucide-react';\r\nimport { DataTable } from '../../data-table/data-table';\r\nimport { api } from '@/lib/api';\r\nimport { EcomOrder, OrderStatus, PaymentStatus } from './type/ecom-orders';\r\nimport { getEcomOrderColumns } from './table/ecom-order-cell';\r\nimport { Checkbox } from '@/components/ui/checkbox';\r\nimport { useReactTable, ColumnFiltersState, PaginationState, RowSelectionState, SortingState, VisibilityState, getCoreRowModel, getSortedRowModel, getFilteredRowModel, Updater } from '@tanstack/react-table';\r\nimport { TableToolbar } from '../../data-table/table-toolbar';\r\nimport { TableFooter } from '../../data-table/table-footer';\r\nimport { But<PERSON> } from '@/components/ui/button';\r\nimport { PaginationResponse } from '@/lib/response';\r\nimport { toast } from \"sonner\";\r\nimport { EcomOrderFormModal } from './ecom-order-form-modal';\r\nimport { FloatDeleteButton } from './components/float-delete-button';\r\nimport { EcomOrderDetailSheet } from './ecom-order-detail-sheet';\r\nimport {\r\n  Tabs,\r\n  TabsList,\r\n  TabsTrigger,\r\n} from \"@/components/ui/tabs\";\r\nimport { Badge } from '@/components/ui/badge';\r\n\r\n// Define interface for ecom order statistics response\r\ninterface EcomOrderStatistics {\r\n  totalOrders: number;\r\n  byStatus: {\r\n    PENDING: number;\r\n    PROCESSING: number;\r\n    COMPLETED: number;\r\n    CANCELLED: number;\r\n  };\r\n  byPaymentStatus: {\r\n    PAID: number;\r\n    UNPAID: number;\r\n  };\r\n}\r\n\r\n// Define type for status filter\r\ntype OrderStatusFilter = 'all' | OrderStatus;\r\ntype PaymentStatusFilter = 'all' | PaymentStatus;\r\n\r\nexport default function EcomOrders() {\r\n  const [orders, setOrders] = useState<EcomOrder[]>([]);\r\n  const [loading, setLoading] = useState(true);\r\n  const [sorting, setSorting] = useState<SortingState>([]);\r\n  const sortingRef = useRef<SortingState>([]);\r\n  const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([]);\r\n  const [pagination, setPagination] = useState<PaginationState>({\r\n    pageIndex: 0,\r\n    pageSize: 20,\r\n  });\r\n  const [meta, setMeta] = useState({\r\n    page: 1,\r\n    limit: 10,\r\n    itemCount: 0,\r\n    pageCount: 0,\r\n    hasPreviousPage: false,\r\n    hasNextPage: false\r\n  });\r\n  const [globalFilter, setGlobalFilter] = useState(\"\");\r\n  const [rowSelection, setRowSelection] = useState<RowSelectionState>({});\r\n  const [columnVisibility, setColumnVisibility] = useState<VisibilityState>({});\r\n  const [isRefreshing, setIsRefreshing] = useState(false);\r\n  const [selectedOrder, setSelectedOrder] = useState<EcomOrder | null>(null);\r\n  const [showDetail, setShowDetail] = useState(false);\r\n  const [isUpdating, setIsUpdating] = useState(false);\r\n  const [statusFilter, setStatusFilter] = useState<OrderStatusFilter>('all');\r\n  const [paymentStatusFilter, setPaymentStatusFilter] = useState<PaymentStatusFilter>('all');\r\n\r\n  // State để lưu trữ số lượng thống kê từ API statistics\r\n  const [statisticsCounts, setStatisticsCounts] = useState({\r\n    all: 0,\r\n    pending: 0,\r\n    processing: 0,\r\n    completed: 0,\r\n    cancelled: 0,\r\n    paid: 0,\r\n    unpaid: 0\r\n  });\r\n  const [showOrderFormModal, setShowOrderFormModal] = useState(false)\r\n  const [formMode, setFormMode] = useState<'create' | 'update' | 'view'>('create')\r\n\r\n  // Custom sorting state handler that updates both state and ref\r\n  const handleSortingChange = useCallback((updaterOrValue: Updater<SortingState>) => {\r\n    // Handle both direct value and updater function\r\n    setSorting(updaterOrValue);\r\n\r\n    // Update ref with the new value\r\n    if (typeof updaterOrValue === 'function') {\r\n      // If it's an updater function, call it with current state to get new value\r\n      const newValue = updaterOrValue(sorting);\r\n      sortingRef.current = newValue;\r\n    } else {\r\n      // If it's a direct value\r\n      sortingRef.current = updaterOrValue;\r\n    }\r\n  }, [sorting]);\r\n\r\n  // Xử lý xem chi tiết đơn hàng\r\n  const handleViewDetail = (order: EcomOrder) => {\r\n    setSelectedOrder(order);\r\n    setShowDetail(true);\r\n    toast.info(`Đang xem chi tiết đơn hàng #${order.orderNumber}`);\r\n  };\r\n\r\n  // Xử lý chỉnh sửa đơn hàng\r\n  const handleEdit = (order: EcomOrder) => {\r\n    setSelectedOrder(order);\r\n    setFormMode('update');\r\n    setShowOrderFormModal(true);\r\n    toast.info(`Đang chỉnh sửa đơn hàng #${order.orderNumber}`);\r\n  };\r\n\r\n  // Xử lý tạo đơn hàng mới\r\n  const handleCreateNew = () => {\r\n    setSelectedOrder(null);\r\n    setFormMode('create');\r\n    setShowOrderFormModal(true);\r\n  };\r\n\r\n  // Xử lý xóa đơn hàng\r\n  const handleDelete = async (order: EcomOrder) => {\r\n    try {\r\n      setIsUpdating(true);\r\n      await api.delete(`ecom-orders/${order.id}`);\r\n      toast.success(`Đã xóa đơn hàng #${order.orderNumber}`);\r\n      fetchOrders();\r\n    } catch (error) {\r\n      console.error('Error deleting order:', error);\r\n      toast.error(\"Không thể xóa đơn hàng\");\r\n    } finally {\r\n      setIsUpdating(false);\r\n    }\r\n  };\r\n\r\n  // Xử lý thay đổi trạng thái đơn hàng\r\n  const handleChangeStatus = async (order: EcomOrder, status: OrderStatus) => {\r\n    try {\r\n      setIsUpdating(true);\r\n\r\n      // Gọi API để cập nhật trạng thái\r\n      await api.patch(`ecom-orders/${order.id}/status`, { status });\r\n\r\n      // Cập nhật dữ liệu local\r\n      const updatedOrders = orders.map(o => {\r\n        if (o.id === order.id) {\r\n          return {\r\n            ...o,\r\n            status\r\n          };\r\n        }\r\n        return o;\r\n      });\r\n\r\n      setOrders(updatedOrders);\r\n      toast.success(`Đã cập nhật trạng thái đơn hàng #${order.orderNumber} thành ${status}`);\r\n\r\n      // Refresh để lấy dữ liệu mới nhất\r\n      fetchOrders();\r\n    } catch (error) {\r\n      console.error('Error changing order status:', error);\r\n      toast.error(\"Không thể thay đổi trạng thái đơn hàng\");\r\n    } finally {\r\n      setIsUpdating(false);\r\n    }\r\n  };\r\n\r\n  // Xử lý thay đổi trạng thái thanh toán\r\n  const handleChangePaymentStatus = async (order: EcomOrder, paymentStatus: PaymentStatus) => {\r\n    try {\r\n      setIsUpdating(true);\r\n\r\n      // Gọi API để cập nhật trạng thái thanh toán\r\n      await api.patch(`ecom-orders/${order.id}/payment-status`, { paymentStatus });\r\n\r\n      // Cập nhật dữ liệu local\r\n      const updatedOrders = orders.map(o => {\r\n        if (o.id === order.id) {\r\n          return {\r\n            ...o,\r\n            paymentStatus\r\n          };\r\n        }\r\n        return o;\r\n      });\r\n\r\n      setOrders(updatedOrders);\r\n      toast.success(`Đã cập nhật trạng thái thanh toán đơn hàng #${order.orderNumber} thành ${paymentStatus}`);\r\n\r\n      // Refresh để lấy dữ liệu mới nhất\r\n      fetchOrders();\r\n    } catch (error) {\r\n      console.error('Error changing payment status:', error);\r\n      toast.error(\"Không thể thay đổi trạng thái thanh toán\");\r\n    } finally {\r\n      setIsUpdating(false);\r\n    }\r\n  };\r\n\r\n  const fetchOrders = async () => {\r\n    setLoading(true);\r\n    try {\r\n      // Use the ref for sorting\r\n      let sortByParam = '';\r\n      let sortOrderParam = '';\r\n      if (sortingRef.current.length > 0) {\r\n        const firstSort = sortingRef.current[0];\r\n        sortByParam = `&sortBy=${firstSort.id}`;\r\n        sortOrderParam = `&sortOrder=${firstSort.desc ? 'DESC' : 'ASC'}`;\r\n      }\r\n\r\n      // Tạo query string cho search\r\n      let searchParam = '';\r\n\r\n      // Chỉ sử dụng globalFilter cho tìm kiếm\r\n      if (globalFilter) {\r\n        searchParam = `&search=${encodeURIComponent(globalFilter)}`;\r\n      }\r\n\r\n      // Xử lý filter theo trạng thái đơn hàng\r\n      let filterParams = [];\r\n\r\n      // Thêm filter theo trạng thái đơn hàng\r\n      if (statusFilter !== 'all') {\r\n        filterParams.push(`status:${statusFilter}`);\r\n      }\r\n\r\n      // Thêm filter theo trạng thái thanh toán\r\n      if (paymentStatusFilter !== 'all') {\r\n        filterParams.push(`paymentStatus:${paymentStatusFilter}`);\r\n      }\r\n\r\n      // Tạo chuỗi filter từ mảng filterParams\r\n      const filterParam = filterParams.length > 0\r\n        ? `&filter=${filterParams.join(',')}`\r\n        : '';\r\n\r\n      const response = await api.get<PaginationResponse<EcomOrder>>(\r\n        `ecom-orders?page=${pagination.pageIndex + 1}&limit=${pagination.pageSize}${sortByParam}${sortOrderParam}${searchParam}${filterParam}`\r\n      );\r\n\r\n      if (response && response.data) {\r\n        // Sử dụng dữ liệu trực tiếp từ API mà không cần lọc ở client\r\n        setOrders(response.data);\r\n\r\n        if (response.meta) {\r\n          setMeta(response.meta);\r\n        }\r\n      } else {\r\n        setOrders([]);\r\n        setMeta({\r\n          page: 1,\r\n          limit: pagination.pageSize,\r\n          itemCount: 0,\r\n          pageCount: 0,\r\n          hasPreviousPage: false,\r\n          hasNextPage: false\r\n        });\r\n      }\r\n    } catch (error: any) {\r\n      console.error('Error fetching orders:', error);\r\n\r\n      // Kiểm tra lỗi 401/403\r\n      if (error.status === 401) {\r\n        toast.error(\"Bạn chưa đăng nhập hoặc phiên đăng nhập đã hết hạn\");\r\n      } else if (error.status === 403) {\r\n        toast.error(\"Bạn không có quyền truy cập vào tài nguyên này\");\r\n      } else if (error.status === 404) {\r\n        toast.error(\"Không tìm thấy tài nguyên\");\r\n      } else {\r\n        toast.error(\"Không thể tải dữ liệu đơn hàng\");\r\n      }\r\n\r\n      // Đặt giá trị mặc định khi có lỗi\r\n      setOrders([]);\r\n      setMeta({\r\n        page: 1,\r\n        limit: pagination.pageSize,\r\n        itemCount: 0,\r\n        pageCount: 0,\r\n        hasPreviousPage: false,\r\n        hasNextPage: false\r\n      });\r\n    } finally {\r\n      setLoading(false);\r\n      setIsRefreshing(false);\r\n    }\r\n  };\r\n\r\n  const handleDeleteSelected = async () => {\r\n    try {\r\n      setIsUpdating(true);\r\n      const selectedRows = table.getSelectedRowModel().rows;\r\n      const selectedOrders = selectedRows.map(row => row.original);\r\n\r\n      if (selectedOrders.length === 0) {\r\n        toast.error(\"Vui lòng chọn ít nhất một đơn hàng để xóa\");\r\n        return;\r\n      }\r\n\r\n      // Thực hiện xóa từng đơn hàng đã chọn\r\n      const deletePromises = selectedOrders.map(order =>\r\n        api.delete(`ecom-orders/${order.id}`)\r\n      );\r\n\r\n      await Promise.all(deletePromises);\r\n\r\n      // Cập nhật UI\r\n      toast.success(`Đã xóa ${selectedOrders.length} đơn hàng thành công`);\r\n\r\n      // Reset selection\r\n      table.resetRowSelection();\r\n\r\n      // Refresh data\r\n      await fetchOrders();\r\n    } catch (error) {\r\n      console.error('Error deleting orders:', error);\r\n      toast.error(\"Không thể xóa các đơn hàng đã chọn\");\r\n    } finally {\r\n      setIsUpdating(false);\r\n    }\r\n  };\r\n\r\n  const table = useReactTable({\r\n    data: orders,\r\n    columns: [\r\n      {\r\n        id: 'select',\r\n        size: 40,\r\n        header: ({ table }) => (\r\n          <div className=\"px-1\">\r\n            <Checkbox\r\n              checked={\r\n                table.getIsAllPageRowsSelected() ||\r\n                (table.getIsSomePageRowsSelected() && \"indeterminate\")\r\n              }\r\n              onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}\r\n              aria-label=\"Select all\"\r\n              className=\"translate-y-[2px]\"\r\n            />\r\n          </div>\r\n        ),\r\n        cell: ({ row }) => (\r\n          <div className=\"px-1\">\r\n            <Checkbox\r\n              checked={row.getIsSelected()}\r\n              onCheckedChange={(value) => row.toggleSelected(!!value)}\r\n              onClick={(e) => e.stopPropagation()}\r\n              aria-label=\"Select row\"\r\n              className=\"translate-y-[2px]\"\r\n            />\r\n          </div>\r\n        ),\r\n        enableSorting: false,\r\n        enableHiding: false,\r\n      },\r\n      ...getEcomOrderColumns({\r\n        onViewDetail: handleViewDetail,\r\n        onDelete: handleDelete,\r\n        onEdit: handleEdit,\r\n        onChangeStatus: handleChangeStatus,\r\n        onChangePaymentStatus: handleChangePaymentStatus\r\n      })\r\n    ],\r\n    state: {\r\n      sorting,\r\n      columnFilters,\r\n      pagination,\r\n      rowSelection,\r\n      columnVisibility,\r\n      globalFilter,\r\n    },\r\n    pageCount: meta.pageCount,\r\n    onSortingChange: handleSortingChange,\r\n    onColumnFiltersChange: setColumnFilters,\r\n    onPaginationChange: setPagination,\r\n    onRowSelectionChange: setRowSelection,\r\n    onColumnVisibilityChange: setColumnVisibility,\r\n    onGlobalFilterChange: setGlobalFilter,\r\n    getCoreRowModel: getCoreRowModel(),\r\n    getSortedRowModel: getSortedRowModel(),\r\n    getFilteredRowModel: getFilteredRowModel(),\r\n    manualPagination: true,\r\n    enableSorting: true,\r\n    enableColumnFilters: true,\r\n    enableRowSelection: true,\r\n    enableMultiSort: false,\r\n    manualSorting: false,\r\n    manualFiltering: false,\r\n  });\r\n\r\n  const handleRefresh = useCallback(async () => {\r\n    setIsRefreshing(true);\r\n    try {\r\n      // Đặt lại các bộ lọc và sắp xếp\r\n      table.resetColumnFilters();\r\n      table.resetSorting();\r\n      setStatusFilter('all');\r\n      setPaymentStatusFilter('all');\r\n      setGlobalFilter('');\r\n\r\n      // Fetch dữ liệu mới và thống kê\r\n      await Promise.all([\r\n        fetchOrders(),\r\n        fetchStatistics()\r\n      ]);\r\n    } finally {\r\n      setTimeout(() => {\r\n        setIsRefreshing(false);\r\n      }, 1000);\r\n    }\r\n  }, [table]);\r\n\r\n  // Xử lý xuất dữ liệu\r\n  const handleExport = async () => {\r\n    try {\r\n      // Hiển thị thông báo đang xuất dữ liệu\r\n      toast.info(`Đang xuất dữ liệu đơn hàng...`);\r\n\r\n      // Lấy token xác thực từ localStorage\r\n      const token = localStorage.getItem('accessToken');\r\n\r\n      // Tạo URL API\r\n      const apiUrl = `${api.baseUrl}/ecom-orders/export`;\r\n\r\n      // Sử dụng XMLHttpRequest để tải file\r\n      const xhr = new XMLHttpRequest();\r\n      xhr.open('GET', apiUrl, true);\r\n      xhr.responseType = 'blob';\r\n\r\n      // Thêm token vào header\r\n      if (token) {\r\n        xhr.setRequestHeader('Authorization', `Bearer ${token}`);\r\n      }\r\n\r\n      // Xử lý khi tải xong\r\n      xhr.onload = function() {\r\n        if (this.status === 200) {\r\n          // Tạo blob từ response\r\n          const blob = new Blob([this.response], {\r\n            type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'\r\n          });\r\n\r\n          // Tạo URL cho blob\r\n          const url = window.URL.createObjectURL(blob);\r\n\r\n          // Tạo thẻ a để tải xuống\r\n          const a = document.createElement('a');\r\n          a.href = url;\r\n          a.download = `ecom-orders-${new Date().toISOString().split('T')[0]}.xlsx`;\r\n          document.body.appendChild(a);\r\n          a.click();\r\n\r\n          // Dọn dẹp\r\n          setTimeout(() => {\r\n            document.body.removeChild(a);\r\n            window.URL.revokeObjectURL(url);\r\n          }, 0);\r\n\r\n          toast.success(`Đã xuất dữ liệu đơn hàng thành công`);\r\n        } else {\r\n          toast.error(`Không thể xuất dữ liệu đơn hàng: ${this.statusText}`);\r\n        }\r\n      };\r\n\r\n      // Xử lý lỗi\r\n      xhr.onerror = function() {\r\n        toast.error(\"Không thể xuất dữ liệu đơn hàng: Lỗi kết nối\");\r\n      };\r\n\r\n      // Gửi request\r\n      xhr.send();\r\n    } catch (error) {\r\n      console.error('Error exporting orders:', error);\r\n      toast.error(\"Không thể xuất dữ liệu đơn hàng\");\r\n    }\r\n  };\r\n\r\n  // Hàm lấy thống kê số lượng theo trạng thái đơn hàng\r\n  const fetchStatistics = async () => {\r\n    try {\r\n      const response = await api.get<EcomOrderStatistics>('ecom-orders/statistics');\r\n      if (response) {\r\n        // Lưu trữ số lượng thống kê tổng cộng\r\n        const statsData = {\r\n          all: response.totalOrders || 0,\r\n          pending: response.byStatus?.PENDING || 0,\r\n          processing: response.byStatus?.PROCESSING || 0,\r\n          completed: response.byStatus?.COMPLETED || 0,\r\n          cancelled: response.byStatus?.CANCELLED || 0,\r\n          paid: response.byPaymentStatus?.PAID || 0,\r\n          unpaid: response.byPaymentStatus?.UNPAID || 0\r\n        };\r\n\r\n        // Cập nhật state thống kê\r\n        setStatisticsCounts(statsData);\r\n\r\n        // Log thông tin để debug\r\n         \r\n      }\r\n    } catch (error) {\r\n      console.error('Error fetching statistics:', error);\r\n      toast.error('Không thể tải thống kê. Vui lòng thử lại sau.');\r\n\r\n      // Đặt giá trị mặc định khi có lỗi\r\n      const defaultStats = {\r\n        all: 0,\r\n        pending: 0,\r\n        processing: 0,\r\n        completed: 0,\r\n        cancelled: 0,\r\n        paid: 0,\r\n        unpaid: 0\r\n      };\r\n\r\n      setStatisticsCounts(defaultStats);\r\n    }\r\n  };\r\n\r\n  useEffect(() => {\r\n    fetchOrders();\r\n  }, [pagination.pageIndex, pagination.pageSize, statusFilter, paymentStatusFilter, sorting, globalFilter]);\r\n\r\n  useEffect(() => {\r\n    fetchStatistics();\r\n  }, []);\r\n\r\n  return (\r\n    <div className=\"w-full flex flex-col h-full\">\r\n      {/* Header Navigation */}\r\n      <div className=\"w-full flex justify-between items-center border-b py-1.5 px-6 h-10\">\r\n        <div className=\"flex items-center gap-2\">\r\n          <div className=\"flex items-center gap-1\">\r\n            <span className=\"text-sm font-medium\">Đơn Hàng</span>\r\n            <span className=\"text-xs bg-accent rounded-md px-1.5 py-1\">{statisticsCounts.all}</span>\r\n          </div>\r\n        </div>\r\n        <div className=\"flex items-center gap-2\">\r\n          {isUpdating && (\r\n            <span className=\"text-xs text-muted-foreground\">Đang cập nhật...</span>\r\n          )}\r\n          <div className=\"flex items-center gap-2\">\r\n            <Button className=\"relative\" size=\"sm\" variant=\"secondary\" onClick={handleExport}>\r\n              <FileDown className=\"size-4 mr-1\" />\r\n              Xuất Dữ Liệu\r\n            </Button>\r\n            <Button className=\"relative\" size=\"sm\" variant=\"secondary\" onClick={handleCreateNew}>\r\n              <Plus className=\"size-4 mr-1\" />\r\n              Tạo Đơn Hàng Mới\r\n            </Button>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Table Toolbar with Status Tabs */}\r\n      <TableToolbar\r\n        table={table}\r\n        globalFilter={globalFilter}\r\n        setGlobalFilter={setGlobalFilter}\r\n        onRefresh={handleRefresh}\r\n        isRefreshing={isRefreshing}\r\n        beforeSearchSlot={\r\n          <Tabs defaultValue=\"all\" className=\"w-fit\">\r\n            <TabsList>\r\n              <TabsTrigger\r\n                value=\"all\"\r\n                onClick={() => setStatusFilter('all')}\r\n                className=\"flex items-center gap-1\"\r\n              >\r\n                Tất cả\r\n                <Badge variant=\"secondary\" className=\"ml-1\">{statisticsCounts.all}</Badge>\r\n              </TabsTrigger>\r\n              <TabsTrigger\r\n                value=\"pending\"\r\n                onClick={() => setStatusFilter(OrderStatus.PENDING)}\r\n                className=\"flex items-center gap-1\"\r\n              >\r\n                Chờ xử lý\r\n                <Badge variant=\"secondary\" className=\"ml-1\">{statisticsCounts.pending}</Badge>\r\n              </TabsTrigger>\r\n              <TabsTrigger\r\n                value=\"processing\"\r\n                onClick={() => setStatusFilter(OrderStatus.PROCESSING)}\r\n                className=\"flex items-center gap-1\"\r\n              >\r\n                Đang xử lý\r\n                <Badge variant=\"secondary\" className=\"ml-1\">{statisticsCounts.processing}</Badge>\r\n              </TabsTrigger>\r\n              <TabsTrigger\r\n                value=\"completed\"\r\n                onClick={() => setStatusFilter(OrderStatus.COMPLETED)}\r\n                className=\"flex items-center gap-1\"\r\n              >\r\n                Hoàn thành\r\n                <Badge variant=\"secondary\" className=\"ml-1\">{statisticsCounts.completed}</Badge>\r\n              </TabsTrigger>\r\n              <TabsTrigger\r\n                value=\"cancelled\"\r\n                onClick={() => setStatusFilter(OrderStatus.CANCELLED)}\r\n                className=\"flex items-center gap-1\"\r\n              >\r\n                Đã hủy\r\n                <Badge variant=\"secondary\" className=\"ml-1\">{statisticsCounts.cancelled}</Badge>\r\n              </TabsTrigger>\r\n            </TabsList>\r\n          </Tabs>\r\n        }\r\n      />\r\n\r\n      {/* Data Table */}\r\n      <div className=\"flex-1 overflow-auto\">\r\n        <DataTable\r\n          table={table}\r\n          className=\"w-full\"\r\n          isLoading={loading}\r\n        />\r\n      </div>\r\n\r\n      {/* Fixed Pagination Footer */}\r\n      <TableFooter\r\n        table={table}\r\n        totalItems={meta.itemCount}\r\n      />\r\n\r\n      {/* Float Delete Button */}\r\n      <FloatDeleteButton\r\n        selectedCount={table.getFilteredSelectedRowModel().rows.length}\r\n        onDelete={handleDeleteSelected}\r\n      />\r\n\r\n      <EcomOrderDetailSheet\r\n        order={selectedOrder}\r\n        isOpen={showDetail}\r\n        onClose={() => setShowDetail(false)}\r\n        onEdit={handleEdit}\r\n      />\r\n\r\n      <EcomOrderFormModal\r\n        isOpen={showOrderFormModal}\r\n        onClose={() => setShowOrderFormModal(false)}\r\n        order={selectedOrder}\r\n        mode={formMode}\r\n        onSuccess={handleRefresh}\r\n      />\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AAKA;;;AAvBA;;;;;;;;;;;;;;;;;;AA4Ce,SAAS;;IACtB,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,sQAAA,CAAA,WAAQ,AAAD,EAAe,EAAE;IACpD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,sQAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,sQAAA,CAAA,WAAQ,AAAD,EAAgB,EAAE;IACvD,MAAM,aAAa,CAAA,GAAA,sQAAA,CAAA,SAAM,AAAD,EAAgB,EAAE;IAC1C,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,sQAAA,CAAA,WAAQ,AAAD,EAAsB,EAAE;IACzE,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,sQAAA,CAAA,WAAQ,AAAD,EAAmB;QAC5D,WAAW;QACX,UAAU;IACZ;IACA,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,sQAAA,CAAA,WAAQ,AAAD,EAAE;QAC/B,MAAM;QACN,OAAO;QACP,WAAW;QACX,WAAW;QACX,iBAAiB;QACjB,aAAa;IACf;IACA,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,sQAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,sQAAA,CAAA,WAAQ,AAAD,EAAqB,CAAC;IACrE,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,sQAAA,CAAA,WAAQ,AAAD,EAAmB,CAAC;IAC3E,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,sQAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,sQAAA,CAAA,WAAQ,AAAD,EAAoB;IACrE,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,sQAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,sQAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,sQAAA,CAAA,WAAQ,AAAD,EAAqB;IACpE,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,sQAAA,CAAA,WAAQ,AAAD,EAAuB;IAEpF,uDAAuD;IACvD,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,sQAAA,CAAA,WAAQ,AAAD,EAAE;QACvD,KAAK;QACL,SAAS;QACT,YAAY;QACZ,WAAW;QACX,WAAW;QACX,MAAM;QACN,QAAQ;IACV;IACA,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,sQAAA,CAAA,WAAQ,AAAD,EAAE;IAC7D,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,sQAAA,CAAA,WAAQ,AAAD,EAAgC;IAEvE,+DAA+D;IAC/D,MAAM,sBAAsB,CAAA,GAAA,sQAAA,CAAA,cAAW,AAAD;uDAAE,CAAC;YACvC,gDAAgD;YAChD,WAAW;YAEX,gCAAgC;YAChC,IAAI,OAAO,mBAAmB,YAAY;gBACxC,2EAA2E;gBAC3E,MAAM,WAAW,eAAe;gBAChC,WAAW,OAAO,GAAG;YACvB,OAAO;gBACL,yBAAyB;gBACzB,WAAW,OAAO,GAAG;YACvB;QACF;sDAAG;QAAC;KAAQ;IAEZ,8BAA8B;IAC9B,MAAM,mBAAmB,CAAC;QACxB,iBAAiB;QACjB,cAAc;QACd,2QAAA,CAAA,QAAK,CAAC,IAAI,CAAC,CAAC,4BAA4B,EAAE,MAAM,WAAW,EAAE;IAC/D;IAEA,2BAA2B;IAC3B,MAAM,aAAa,CAAC;QAClB,iBAAiB;QACjB,YAAY;QACZ,sBAAsB;QACtB,2QAAA,CAAA,QAAK,CAAC,IAAI,CAAC,CAAC,yBAAyB,EAAE,MAAM,WAAW,EAAE;IAC5D;IAEA,yBAAyB;IACzB,MAAM,kBAAkB;QACtB,iBAAiB;QACjB,YAAY;QACZ,sBAAsB;IACxB;IAEA,qBAAqB;IACrB,MAAM,eAAe,OAAO;QAC1B,IAAI;YACF,cAAc;YACd,MAAM,6GAAA,CAAA,MAAG,CAAC,MAAM,CAAC,CAAC,YAAY,EAAE,MAAM,EAAE,EAAE;YAC1C,2QAAA,CAAA,QAAK,CAAC,OAAO,CAAC,CAAC,iBAAiB,EAAE,MAAM,WAAW,EAAE;YACrD;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yBAAyB;YACvC,2QAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd,SAAU;YACR,cAAc;QAChB;IACF;IAEA,qCAAqC;IACrC,MAAM,qBAAqB,OAAO,OAAkB;QAClD,IAAI;YACF,cAAc;YAEd,iCAAiC;YACjC,MAAM,6GAAA,CAAA,MAAG,CAAC,KAAK,CAAC,CAAC,YAAY,EAAE,MAAM,EAAE,CAAC,OAAO,CAAC,EAAE;gBAAE;YAAO;YAE3D,yBAAyB;YACzB,MAAM,gBAAgB,OAAO,GAAG,CAAC,CAAA;gBAC/B,IAAI,EAAE,EAAE,KAAK,MAAM,EAAE,EAAE;oBACrB,OAAO;wBACL,GAAG,CAAC;wBACJ;oBACF;gBACF;gBACA,OAAO;YACT;YAEA,UAAU;YACV,2QAAA,CAAA,QAAK,CAAC,OAAO,CAAC,CAAC,iCAAiC,EAAE,MAAM,WAAW,CAAC,OAAO,EAAE,QAAQ;YAErF,kCAAkC;YAClC;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,gCAAgC;YAC9C,2QAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd,SAAU;YACR,cAAc;QAChB;IACF;IAEA,uCAAuC;IACvC,MAAM,4BAA4B,OAAO,OAAkB;QACzD,IAAI;YACF,cAAc;YAEd,4CAA4C;YAC5C,MAAM,6GAAA,CAAA,MAAG,CAAC,KAAK,CAAC,CAAC,YAAY,EAAE,MAAM,EAAE,CAAC,eAAe,CAAC,EAAE;gBAAE;YAAc;YAE1E,yBAAyB;YACzB,MAAM,gBAAgB,OAAO,GAAG,CAAC,CAAA;gBAC/B,IAAI,EAAE,EAAE,KAAK,MAAM,EAAE,EAAE;oBACrB,OAAO;wBACL,GAAG,CAAC;wBACJ;oBACF;gBACF;gBACA,OAAO;YACT;YAEA,UAAU;YACV,2QAAA,CAAA,QAAK,CAAC,OAAO,CAAC,CAAC,4CAA4C,EAAE,MAAM,WAAW,CAAC,OAAO,EAAE,eAAe;YAEvG,kCAAkC;YAClC;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,kCAAkC;YAChD,2QAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd,SAAU;YACR,cAAc;QAChB;IACF;IAEA,MAAM,cAAc;QAClB,WAAW;QACX,IAAI;YACF,0BAA0B;YAC1B,IAAI,cAAc;YAClB,IAAI,iBAAiB;YACrB,IAAI,WAAW,OAAO,CAAC,MAAM,GAAG,GAAG;gBACjC,MAAM,YAAY,WAAW,OAAO,CAAC,EAAE;gBACvC,cAAc,CAAC,QAAQ,EAAE,UAAU,EAAE,EAAE;gBACvC,iBAAiB,CAAC,WAAW,EAAE,UAAU,IAAI,GAAG,SAAS,OAAO;YAClE;YAEA,8BAA8B;YAC9B,IAAI,cAAc;YAElB,wCAAwC;YACxC,IAAI,cAAc;gBAChB,cAAc,CAAC,QAAQ,EAAE,mBAAmB,eAAe;YAC7D;YAEA,wCAAwC;YACxC,IAAI,eAAe,EAAE;YAErB,uCAAuC;YACvC,IAAI,iBAAiB,OAAO;gBAC1B,aAAa,IAAI,CAAC,CAAC,OAAO,EAAE,cAAc;YAC5C;YAEA,yCAAyC;YACzC,IAAI,wBAAwB,OAAO;gBACjC,aAAa,IAAI,CAAC,CAAC,cAAc,EAAE,qBAAqB;YAC1D;YAEA,wCAAwC;YACxC,MAAM,cAAc,aAAa,MAAM,GAAG,IACtC,CAAC,QAAQ,EAAE,aAAa,IAAI,CAAC,MAAM,GACnC;YAEJ,MAAM,WAAW,MAAM,6GAAA,CAAA,MAAG,CAAC,GAAG,CAC5B,CAAC,iBAAiB,EAAE,WAAW,SAAS,GAAG,EAAE,OAAO,EAAE,WAAW,QAAQ,GAAG,cAAc,iBAAiB,cAAc,aAAa;YAGxI,IAAI,YAAY,SAAS,IAAI,EAAE;gBAC7B,6DAA6D;gBAC7D,UAAU,SAAS,IAAI;gBAEvB,IAAI,SAAS,IAAI,EAAE;oBACjB,QAAQ,SAAS,IAAI;gBACvB;YACF,OAAO;gBACL,UAAU,EAAE;gBACZ,QAAQ;oBACN,MAAM;oBACN,OAAO,WAAW,QAAQ;oBAC1B,WAAW;oBACX,WAAW;oBACX,iBAAiB;oBACjB,aAAa;gBACf;YACF;QACF,EAAE,OAAO,OAAY;YACnB,QAAQ,KAAK,CAAC,0BAA0B;YAExC,uBAAuB;YACvB,IAAI,MAAM,MAAM,KAAK,KAAK;gBACxB,2QAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACd,OAAO,IAAI,MAAM,MAAM,KAAK,KAAK;gBAC/B,2QAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACd,OAAO,IAAI,MAAM,MAAM,KAAK,KAAK;gBAC/B,2QAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACd,OAAO;gBACL,2QAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACd;YAEA,kCAAkC;YAClC,UAAU,EAAE;YACZ,QAAQ;gBACN,MAAM;gBACN,OAAO,WAAW,QAAQ;gBAC1B,WAAW;gBACX,WAAW;gBACX,iBAAiB;gBACjB,aAAa;YACf;QACF,SAAU;YACR,WAAW;YACX,gBAAgB;QAClB;IACF;IAEA,MAAM,uBAAuB;QAC3B,IAAI;YACF,cAAc;YACd,MAAM,eAAe,MAAM,mBAAmB,GAAG,IAAI;YACrD,MAAM,iBAAiB,aAAa,GAAG,CAAC,CAAA,MAAO,IAAI,QAAQ;YAE3D,IAAI,eAAe,MAAM,KAAK,GAAG;gBAC/B,2QAAA,CAAA,QAAK,CAAC,KAAK,CAAC;gBACZ;YACF;YAEA,sCAAsC;YACtC,MAAM,iBAAiB,eAAe,GAAG,CAAC,CAAA,QACxC,6GAAA,CAAA,MAAG,CAAC,MAAM,CAAC,CAAC,YAAY,EAAE,MAAM,EAAE,EAAE;YAGtC,MAAM,QAAQ,GAAG,CAAC;YAElB,cAAc;YACd,2QAAA,CAAA,QAAK,CAAC,OAAO,CAAC,CAAC,OAAO,EAAE,eAAe,MAAM,CAAC,oBAAoB,CAAC;YAEnE,kBAAkB;YAClB,MAAM,iBAAiB;YAEvB,eAAe;YACf,MAAM;QACR,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;YACxC,2QAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd,SAAU;YACR,cAAc;QAChB;IACF;IAEA,MAAM,QAAQ,CAAA,GAAA,mSAAA,CAAA,gBAAa,AAAD,EAAE;QAC1B,MAAM;QACN,SAAS;YACP;gBACE,IAAI;gBACJ,MAAM;gBACN,MAAM;uDAAE,CAAC,EAAE,KAAK,EAAE,iBAChB,sSAAC;4BAAI,WAAU;sCACb,cAAA,sSAAC,gIAAA,CAAA,WAAQ;gCACP,SACE,MAAM,wBAAwB,MAC7B,MAAM,yBAAyB,MAAM;gCAExC,eAAe;uEAAE,CAAC,QAAU,MAAM,yBAAyB,CAAC,CAAC,CAAC;;gCAC9D,cAAW;gCACX,WAAU;;;;;;;;;;;;gBAIhB,IAAI;uDAAE,CAAC,EAAE,GAAG,EAAE,iBACZ,sSAAC;4BAAI,WAAU;sCACb,cAAA,sSAAC,gIAAA,CAAA,WAAQ;gCACP,SAAS,IAAI,aAAa;gCAC1B,eAAe;uEAAE,CAAC,QAAU,IAAI,cAAc,CAAC,CAAC,CAAC;;gCACjD,OAAO;uEAAE,CAAC,IAAM,EAAE,eAAe;;gCACjC,cAAW;gCACX,WAAU;;;;;;;;;;;;gBAIhB,eAAe;gBACf,cAAc;YAChB;eACG,CAAA,GAAA,qLAAA,CAAA,sBAAmB,AAAD,EAAE;gBACrB,cAAc;gBACd,UAAU;gBACV,QAAQ;gBACR,gBAAgB;gBAChB,uBAAuB;YACzB;SACD;QACD,OAAO;YACL;YACA;YACA;YACA;YACA;YACA;QACF;QACA,WAAW,KAAK,SAAS;QACzB,iBAAiB;QACjB,uBAAuB;QACvB,oBAAoB;QACpB,sBAAsB;QACtB,0BAA0B;QAC1B,sBAAsB;QACtB,iBAAiB,CAAA,GAAA,iPAAA,CAAA,kBAAe,AAAD;QAC/B,mBAAmB,CAAA,GAAA,iPAAA,CAAA,oBAAiB,AAAD;QACnC,qBAAqB,CAAA,GAAA,iPAAA,CAAA,sBAAmB,AAAD;QACvC,kBAAkB;QAClB,eAAe;QACf,qBAAqB;QACrB,oBAAoB;QACpB,iBAAiB;QACjB,eAAe;QACf,iBAAiB;IACnB;IAEA,MAAM,gBAAgB,CAAA,GAAA,sQAAA,CAAA,cAAW,AAAD;iDAAE;YAChC,gBAAgB;YAChB,IAAI;gBACF,gCAAgC;gBAChC,MAAM,kBAAkB;gBACxB,MAAM,YAAY;gBAClB,gBAAgB;gBAChB,uBAAuB;gBACvB,gBAAgB;gBAEhB,gCAAgC;gBAChC,MAAM,QAAQ,GAAG,CAAC;oBAChB;oBACA;iBACD;YACH,SAAU;gBACR;6DAAW;wBACT,gBAAgB;oBAClB;4DAAG;YACL;QACF;gDAAG;QAAC;KAAM;IAEV,qBAAqB;IACrB,MAAM,eAAe;QACnB,IAAI;YACF,uCAAuC;YACvC,2QAAA,CAAA,QAAK,CAAC,IAAI,CAAC,CAAC,6BAA6B,CAAC;YAE1C,qCAAqC;YACrC,MAAM,QAAQ,aAAa,OAAO,CAAC;YAEnC,cAAc;YACd,MAAM,SAAS,GAAG,6GAAA,CAAA,MAAG,CAAC,OAAO,CAAC,mBAAmB,CAAC;YAElD,qCAAqC;YACrC,MAAM,MAAM,IAAI;YAChB,IAAI,IAAI,CAAC,OAAO,QAAQ;YACxB,IAAI,YAAY,GAAG;YAEnB,wBAAwB;YACxB,IAAI,OAAO;gBACT,IAAI,gBAAgB,CAAC,iBAAiB,CAAC,OAAO,EAAE,OAAO;YACzD;YAEA,qBAAqB;YACrB,IAAI,MAAM,GAAG;gBACX,IAAI,IAAI,CAAC,MAAM,KAAK,KAAK;oBACvB,uBAAuB;oBACvB,MAAM,OAAO,IAAI,KAAK;wBAAC,IAAI,CAAC,QAAQ;qBAAC,EAAE;wBACrC,MAAM;oBACR;oBAEA,mBAAmB;oBACnB,MAAM,MAAM,OAAO,GAAG,CAAC,eAAe,CAAC;oBAEvC,yBAAyB;oBACzB,MAAM,IAAI,SAAS,aAAa,CAAC;oBACjC,EAAE,IAAI,GAAG;oBACT,EAAE,QAAQ,GAAG,CAAC,YAAY,EAAE,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,KAAK,CAAC;oBACzE,SAAS,IAAI,CAAC,WAAW,CAAC;oBAC1B,EAAE,KAAK;oBAEP,UAAU;oBACV,WAAW;wBACT,SAAS,IAAI,CAAC,WAAW,CAAC;wBAC1B,OAAO,GAAG,CAAC,eAAe,CAAC;oBAC7B,GAAG;oBAEH,2QAAA,CAAA,QAAK,CAAC,OAAO,CAAC,CAAC,mCAAmC,CAAC;gBACrD,OAAO;oBACL,2QAAA,CAAA,QAAK,CAAC,KAAK,CAAC,CAAC,iCAAiC,EAAE,IAAI,CAAC,UAAU,EAAE;gBACnE;YACF;YAEA,YAAY;YACZ,IAAI,OAAO,GAAG;gBACZ,2QAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACd;YAEA,cAAc;YACd,IAAI,IAAI;QACV,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2BAA2B;YACzC,2QAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd;IACF;IAEA,qDAAqD;IACrD,MAAM,kBAAkB;QACtB,IAAI;YACF,MAAM,WAAW,MAAM,6GAAA,CAAA,MAAG,CAAC,GAAG,CAAsB;YACpD,IAAI,UAAU;gBACZ,sCAAsC;gBACtC,MAAM,YAAY;oBAChB,KAAK,SAAS,WAAW,IAAI;oBAC7B,SAAS,SAAS,QAAQ,EAAE,WAAW;oBACvC,YAAY,SAAS,QAAQ,EAAE,cAAc;oBAC7C,WAAW,SAAS,QAAQ,EAAE,aAAa;oBAC3C,WAAW,SAAS,QAAQ,EAAE,aAAa;oBAC3C,MAAM,SAAS,eAAe,EAAE,QAAQ;oBACxC,QAAQ,SAAS,eAAe,EAAE,UAAU;gBAC9C;gBAEA,0BAA0B;gBAC1B,oBAAoB;YAEpB,yBAAyB;YAE3B;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,8BAA8B;YAC5C,2QAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YAEZ,kCAAkC;YAClC,MAAM,eAAe;gBACnB,KAAK;gBACL,SAAS;gBACT,YAAY;gBACZ,WAAW;gBACX,WAAW;gBACX,MAAM;gBACN,QAAQ;YACV;YAEA,oBAAoB;QACtB;IACF;IAEA,CAAA,GAAA,sQAAA,CAAA,YAAS,AAAD;gCAAE;YACR;QACF;+BAAG;QAAC,WAAW,SAAS;QAAE,WAAW,QAAQ;QAAE;QAAc;QAAqB;QAAS;KAAa;IAExG,CAAA,GAAA,sQAAA,CAAA,YAAS,AAAD;gCAAE;YACR;QACF;+BAAG,EAAE;IAEL,qBACE,sSAAC;QAAI,WAAU;;0BAEb,sSAAC;gBAAI,WAAU;;kCACb,sSAAC;wBAAI,WAAU;kCACb,cAAA,sSAAC;4BAAI,WAAU;;8CACb,sSAAC;oCAAK,WAAU;8CAAsB;;;;;;8CACtC,sSAAC;oCAAK,WAAU;8CAA4C,iBAAiB,GAAG;;;;;;;;;;;;;;;;;kCAGpF,sSAAC;wBAAI,WAAU;;4BACZ,4BACC,sSAAC;gCAAK,WAAU;0CAAgC;;;;;;0CAElD,sSAAC;gCAAI,WAAU;;kDACb,sSAAC,8HAAA,CAAA,SAAM;wCAAC,WAAU;wCAAW,MAAK;wCAAK,SAAQ;wCAAY,SAAS;;0DAClE,sSAAC,qSAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;4CAAgB;;;;;;;kDAGtC,sSAAC,8HAAA,CAAA,SAAM;wCAAC,WAAU;wCAAW,MAAK;wCAAK,SAAQ;wCAAY,SAAS;;0DAClE,sSAAC,yRAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;4CAAgB;;;;;;;;;;;;;;;;;;;;;;;;;0BAQxC,sSAAC,6JAAA,CAAA,eAAY;gBACX,OAAO;gBACP,cAAc;gBACd,iBAAiB;gBACjB,WAAW;gBACX,cAAc;gBACd,gCACE,sSAAC,4HAAA,CAAA,OAAI;oBAAC,cAAa;oBAAM,WAAU;8BACjC,cAAA,sSAAC,4HAAA,CAAA,WAAQ;;0CACP,sSAAC,4HAAA,CAAA,cAAW;gCACV,OAAM;gCACN,SAAS,IAAM,gBAAgB;gCAC/B,WAAU;;oCACX;kDAEC,sSAAC,6HAAA,CAAA,QAAK;wCAAC,SAAQ;wCAAY,WAAU;kDAAQ,iBAAiB,GAAG;;;;;;;;;;;;0CAEnE,sSAAC,4HAAA,CAAA,cAAW;gCACV,OAAM;gCACN,SAAS,IAAM,gBAAgB,4KAAA,CAAA,cAAW,CAAC,OAAO;gCAClD,WAAU;;oCACX;kDAEC,sSAAC,6HAAA,CAAA,QAAK;wCAAC,SAAQ;wCAAY,WAAU;kDAAQ,iBAAiB,OAAO;;;;;;;;;;;;0CAEvE,sSAAC,4HAAA,CAAA,cAAW;gCACV,OAAM;gCACN,SAAS,IAAM,gBAAgB,4KAAA,CAAA,cAAW,CAAC,UAAU;gCACrD,WAAU;;oCACX;kDAEC,sSAAC,6HAAA,CAAA,QAAK;wCAAC,SAAQ;wCAAY,WAAU;kDAAQ,iBAAiB,UAAU;;;;;;;;;;;;0CAE1E,sSAAC,4HAAA,CAAA,cAAW;gCACV,OAAM;gCACN,SAAS,IAAM,gBAAgB,4KAAA,CAAA,cAAW,CAAC,SAAS;gCACpD,WAAU;;oCACX;kDAEC,sSAAC,6HAAA,CAAA,QAAK;wCAAC,SAAQ;wCAAY,WAAU;kDAAQ,iBAAiB,SAAS;;;;;;;;;;;;0CAEzE,sSAAC,4HAAA,CAAA,cAAW;gCACV,OAAM;gCACN,SAAS,IAAM,gBAAgB,4KAAA,CAAA,cAAW,CAAC,SAAS;gCACpD,WAAU;;oCACX;kDAEC,sSAAC,6HAAA,CAAA,QAAK;wCAAC,SAAQ;wCAAY,WAAU;kDAAQ,iBAAiB,SAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQjF,sSAAC;gBAAI,WAAU;0BACb,cAAA,sSAAC,0JAAA,CAAA,YAAS;oBACR,OAAO;oBACP,WAAU;oBACV,WAAW;;;;;;;;;;;0BAKf,sSAAC,4JAAA,CAAA,cAAW;gBACV,OAAO;gBACP,YAAY,KAAK,SAAS;;;;;;0BAI5B,sSAAC,8LAAA,CAAA,oBAAiB;gBAChB,eAAe,MAAM,2BAA2B,GAAG,IAAI,CAAC,MAAM;gBAC9D,UAAU;;;;;;0BAGZ,sSAAC,uLAAA,CAAA,uBAAoB;gBACnB,OAAO;gBACP,QAAQ;gBACR,SAAS,IAAM,cAAc;gBAC7B,QAAQ;;;;;;0BAGV,sSAAC,qLAAA,CAAA,qBAAkB;gBACjB,QAAQ;gBACR,SAAS,IAAM,sBAAsB;gBACrC,OAAO;gBACP,MAAM;gBACN,WAAW;;;;;;;;;;;;AAInB;GA7lBwB;;QA0RR,mSAAA,CAAA,gBAAa;;;KA1RL", "debugId": null}}]}
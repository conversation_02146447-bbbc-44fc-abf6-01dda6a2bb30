import { UserDto } from '../../users/dto/user.dto';
export declare class EcomProductCategoryDto {
    id: string;
    name: string;
    description?: string;
    isActive: boolean;
    parentId?: string;
    parent?: EcomProductCategoryDto;
    children?: EcomProductCategoryDto[];
    imageUrl?: string;
    createdAt: Date;
    updatedAt: Date;
    createdBy?: string;
    updatedBy?: string;
    deletedBy?: string;
    isDeleted: boolean;
    deletedAt?: Date;
    creator?: UserDto;
    updater?: UserDto;
    deleter?: UserDto;
}

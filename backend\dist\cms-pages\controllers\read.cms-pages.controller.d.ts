import { ReadCmsPagesService } from '../services/read.cms-pages.service';
import { CmsPageDto } from '../dto/cms-page.dto';
import { CmsPageStatus } from '../entity/cms-pages.entity';
import { CustomPaginationQueryDto } from '../../common/dto/custom-pagination-query.dto';
import { PaginationResponseDto } from '../../common/dto/pagination-response.dto';
export declare class ReadCmsPagesController {
    private readonly cmsPagesService;
    constructor(cmsPagesService: ReadCmsPagesService);
    findAll(paginationQuery: CustomPaginationQueryDto): Promise<PaginationResponseDto<CmsPageDto>>;
    findByStatus(status: CmsPageStatus, paginationQuery: CustomPaginationQueryDto): Promise<PaginationResponseDto<CmsPageDto>>;
    findByTemplate(template: string, paginationQuery: CustomPaginationQueryDto): Promise<PaginationResponseDto<CmsPageDto>>;
    getPublishedPages(paginationQuery: CustomPaginationQueryDto): Promise<PaginationResponseDto<CmsPageDto>>;
    getDraftPages(paginationQuery: CustomPaginationQueryDto): Promise<PaginationResponseDto<CmsPageDto>>;
    getStatistics(): Promise<{
        total: number;
        byStatus: Record<string, number>;
        byTemplate: Record<string, number>;
        totalWordCount: number;
        averageWordCount: number;
    }>;
    getAvailableTemplates(): string[];
    getPopularTemplatePages(limit?: number): Promise<CmsPageDto[]>;
    count(filter?: string): Promise<number>;
    search(keyword: string, paginationQuery: CustomPaginationQueryDto): Promise<PaginationResponseDto<CmsPageDto>>;
    findDeleted(paginationQuery: CustomPaginationQueryDto): Promise<PaginationResponseDto<CmsPageDto>>;
    findOne(id: string, relations?: string): Promise<CmsPageDto | null>;
    findByBusinessCode(businessCode: string): Promise<CmsPageDto | null>;
    findBySlug(slug: string): Promise<CmsPageDto | null>;
}
export declare class ReadCmsPagesPublicController {
    private readonly cmsPagesService;
    constructor(cmsPagesService: ReadCmsPagesService);
    test(): Promise<{
        message: string;
        timestamp: string;
    }>;
    getPublishedPages(paginationQuery: CustomPaginationQueryDto): Promise<PaginationResponseDto<CmsPageDto>>;
}

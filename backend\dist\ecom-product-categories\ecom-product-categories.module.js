"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.EcomProductCategoriesModule = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const ecom_product_categories_entity_1 = require("./entity/ecom-product-categories.entity");
const base_ecom_product_categories_service_1 = require("./services/base.ecom-product-categories.service");
const slug_ecom_product_categories_service_1 = require("./services/slug.ecom-product-categories.service");
const create_ecom_product_categories_service_1 = require("./services/create.ecom-product-categories.service");
const read_ecom_product_categories_service_1 = require("./services/read.ecom-product-categories.service");
const update_ecom_product_categories_service_1 = require("./services/update.ecom-product-categories.service");
const delete_ecom_product_categories_service_1 = require("./services/delete.ecom-product-categories.service");
const unified_slug_service_1 = require("../common/services/unified-slug.service");
const controllers_1 = require("./controllers");
let EcomProductCategoriesModule = class EcomProductCategoriesModule {
};
exports.EcomProductCategoriesModule = EcomProductCategoriesModule;
exports.EcomProductCategoriesModule = EcomProductCategoriesModule = __decorate([
    (0, common_1.Module)({
        imports: [
            typeorm_1.TypeOrmModule.forFeature([ecom_product_categories_entity_1.EcomProductCategories]),
        ],
        controllers: [
            controllers_1.CreateEcomProductCategoriesController,
            controllers_1.ReadEcomProductCategoriesController,
            controllers_1.ReadEcomProductCategoriesPublicController,
            controllers_1.UpdateEcomProductCategoriesController,
            controllers_1.DeleteEcomProductCategoriesController,
        ],
        providers: [
            unified_slug_service_1.UnifiedSlugService,
            base_ecom_product_categories_service_1.BaseEcomProductCategoriesService,
            slug_ecom_product_categories_service_1.SlugEcomProductCategoriesService,
            create_ecom_product_categories_service_1.CreateEcomProductCategoriesService,
            read_ecom_product_categories_service_1.ReadEcomProductCategoriesService,
            update_ecom_product_categories_service_1.UpdateEcomProductCategoriesService,
            delete_ecom_product_categories_service_1.DeleteEcomProductCategoriesService,
        ],
        exports: [
            typeorm_1.TypeOrmModule,
            base_ecom_product_categories_service_1.BaseEcomProductCategoriesService,
            slug_ecom_product_categories_service_1.SlugEcomProductCategoriesService,
            create_ecom_product_categories_service_1.CreateEcomProductCategoriesService,
            read_ecom_product_categories_service_1.ReadEcomProductCategoriesService,
            update_ecom_product_categories_service_1.UpdateEcomProductCategoriesService,
            delete_ecom_product_categories_service_1.DeleteEcomProductCategoriesService,
        ],
    })
], EcomProductCategoriesModule);
//# sourceMappingURL=ecom-product-categories.module.js.map
import { Repository } from 'typeorm';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { BaseTokensService } from './base.tokens.service';
import { Token } from '../entities/token.entity';
import { TokenDto } from '../dto/token.dto';
export declare class ReadTokensService extends BaseTokensService {
    protected readonly tokenRepository: Repository<Token>;
    protected readonly eventEmitter: EventEmitter2;
    constructor(tokenRepository: Repository<Token>, eventEmitter: EventEmitter2);
    findAll(params: {
        limit?: number;
        page?: number;
        sortBy?: string;
        sortOrder?: 'ASC' | 'DESC';
        filter?: string;
        search?: string;
        relations?: string[];
    }): Promise<{
        data: TokenDto[];
        total: number;
    }>;
    search(keyword: string, params: {
        limit?: number;
        page?: number;
    }): Promise<{
        data: TokenDto[];
        total: number;
    }>;
    count(filter?: string): Promise<number>;
    findOne(id: string, relations?: string[]): Promise<TokenDto>;
    findOneOrFail(id: string, relations?: string[]): Promise<TokenDto>;
    findDeleted(params: {
        limit?: number;
        page?: number;
    }): Promise<{
        data: TokenDto[];
        total: number;
    }>;
    export(format: 'csv' | 'json'): Promise<string | TokenDto[]>;
}

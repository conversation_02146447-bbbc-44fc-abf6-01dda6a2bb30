'use client';
/**
 * useBankData Hook - Quản lý data và API calls cho ngân hàng
 */

import { useState, useEffect, useCallback } from 'react';
import { toast } from 'sonner';
import { api } from '@/lib/api';
import { PaginationResponse, createPaginationQuery, PaginationQuery, ApiResponse } from '@/lib/response';
import type { BankDto, BankStatisticsDto, StatusCounts, StatusFilter } from '../types';
import { BANK_ENDPOINTS, TOAST_MESSAGES } from '../utils/bank-constants';
import { mapBankData, calculateStatusCounts, filterBanksByStatus } from '../utils/bank-helpers';

interface UseBankDataProps {
  pagination: {
    pageIndex: number;
    pageSize: number;
  };
  statusFilter: StatusFilter;
  globalFilter: string;
  sorting: Array<{
    id: string;
    desc: boolean;
  }>;
}

export function useBankData({ pagination, statusFilter, globalFilter, sorting }: UseBankDataProps) {
  const [banks, setBanks] = useState<BankDto[]>([]);
  const [loading, setLoading] = useState(true);
  const [totalRows, setTotalRows] = useState(0);
  const [statusCounts, setStatusCounts] = useState<StatusCounts>({ all: 0, active: 0, inactive: 0 });

  // Fetch banks data
  const fetchBanks = useCallback(async () => {
    setLoading(true);
    try {
      // Xử lý sorting
      let sortByParam = '';
      let sortOrderParam = '';
      if (sorting.length > 0) {
        const firstSort = sorting[0];
        sortByParam = `&sortBy=${firstSort.id}`;
        sortOrderParam = `&sortOrder=${firstSort.desc ? 'DESC' : 'ASC'}`;
      }

      // Xử lý search
      let searchParam = '';
      if (globalFilter) {
        searchParam = `&search=${encodeURIComponent(globalFilter)}`;
      }

      const url = `${BANK_ENDPOINTS.LIST}?page=${pagination.pageIndex + 1}&limit=${pagination.pageSize}${sortByParam}${sortOrderParam}${searchParam}`;

      const response = await api.get<PaginationResponse<BankDto>>(url);

      if (response && response.data) {
        // Map backend data to frontend DTO
        const mappedBanks = response.data.map(mapBankData);

        // Lọc dữ liệu theo trạng thái ở phía client
        const filteredBanks = filterBanksByStatus(mappedBanks, statusFilter);

        setBanks(filteredBanks);

        // Sử dụng filtered length cho pagination (giống module gốc)
        setTotalRows(filteredBanks.length);

        // Tổng số từ server để tính status counts
        const totalBanks = response.meta?.itemCount || mappedBanks.length;

        // Cập nhật số lượng cho các tab trạng thái
        const counts = calculateStatusCounts(mappedBanks);
        setStatusCounts({
          all: totalBanks,
          active: counts.active,
          inactive: counts.inactive,
        });
      } else {
        setBanks([]);
        setTotalRows(0);
        setStatusCounts({ all: 0, active: 0, inactive: 0 });
      }
    } catch (error: any) {
      console.error('Error fetching banks:', error);

      // Xử lý các loại lỗi khác nhau
      if (error.status === 401) {
        toast.error(TOAST_MESSAGES.UNAUTHORIZED);
      } else if (error.status === 403) {
        toast.error(TOAST_MESSAGES.FORBIDDEN);
      } else if (error.status === 404) {
        toast.error(TOAST_MESSAGES.NOT_FOUND);
      } else {
        toast.error(TOAST_MESSAGES.FETCH_ERROR);
      }

      // Đặt giá trị mặc định khi có lỗi
      setBanks([]);
      setTotalRows(0);
      setStatusCounts({ all: 0, active: 0, inactive: 0 });
    } finally {
      setLoading(false);
    }
  }, [pagination.pageIndex, pagination.pageSize, statusFilter, globalFilter, sorting]);

  // Fetch statistics
  const fetchStatistics = useCallback(async (force = false) => {
    // Sử dụng closure để theo dõi xem đã gọi API chưa
    if (!(fetchStatistics as any).hasRun || force) {
      try {
        const response = await api.get<ApiResponse<BankStatisticsDto>>(BANK_ENDPOINTS.STATISTICS);

        if (response && response.data) {
          // Xử lý đúng cấu trúc response: { success: true, data: { total, active, inactive } }
          const statsData = response.data as BankStatisticsDto;
          const counts = {
            all: statsData.total || 0,
            active: statsData.active || 0,
            inactive: statsData.inactive || 0,
          };

          setStatusCounts(counts);

        }

        // Đánh dấu đã gọi API
        (fetchStatistics as any).hasRun = true;
      } catch (error) {
        console.error('Error fetching bank statistics:', error);
        toast.error(TOAST_MESSAGES.FETCH_STATISTICS_ERROR);

        // Đặt giá trị mặc định khi có lỗi
        setStatusCounts({ all: 0, active: 0, inactive: 0 });
      }
    }
  }, []);

  // Fetch bank detail
  const fetchBankDetail = useCallback(async (bankId: string): Promise<BankDto | null> => {
    try {
      const response = await api.get<BankDto>(`${BANK_ENDPOINTS.LIST}/${bankId}`);
      return response ? mapBankData(response) : null;
    } catch (error) {
      console.error('Error fetching bank detail:', error);
      toast.error(TOAST_MESSAGES.FETCH_DETAIL_ERROR);
      return null;
    }
  }, []);

  // Update local bank data
  const updateLocalBank = useCallback((bankId: string, updates: Partial<BankDto>) => {
    setBanks(prevBanks =>
      prevBanks.map(bank =>
        bank.id === bankId ? { ...bank, ...updates } : bank
      )
    );
  }, []);

  // Remove local bank
  const removeLocalBank = useCallback((bankId: string) => {
    setBanks(prevBanks => prevBanks.filter(bank => bank.id !== bankId));
    setTotalRows(prev => prev - 1);
  }, []);

  // Add local bank
  const addLocalBank = useCallback((newBank: BankDto) => {
    setBanks(prevBanks => [newBank, ...prevBanks]);
    setTotalRows(prev => prev + 1);
  }, []);

  // Effect để fetch data khi dependencies thay đổi
  useEffect(() => {
    fetchBanks();
  }, [fetchBanks]);

  return {
    // Data
    banks,
    loading,
    totalRows,
    statusCounts,

    // Actions
    fetchBanks,
    fetchStatistics,
    fetchBankDetail,
    updateLocalBank,
    removeLocalBank,
    addLocalBank,
  };
}

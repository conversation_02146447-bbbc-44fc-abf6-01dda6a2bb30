"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var BaseGateway_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.BaseGateway = void 0;
const websockets_1 = require("@nestjs/websockets");
const socket_io_1 = require("socket.io");
const common_1 = require("@nestjs/common");
const config_1 = require("@nestjs/config");
const jwt_1 = require("@nestjs/jwt");
const ws_jwt_guard_1 = require("../guards/ws-jwt.guard");
const ws_exception_filter_1 = require("../filters/ws-exception.filter");
const websocket_emitter_service_1 = require("../services/websocket-emitter.service");
let BaseGateway = BaseGateway_1 = class BaseGateway {
    configService;
    wsEmitterService;
    jwtService;
    server;
    logger = new common_1.Logger(BaseGateway_1.name);
    constructor(configService, wsEmitterService, jwtService) {
        this.configService = configService;
        this.wsEmitterService = wsEmitterService;
        this.jwtService = jwtService;
    }
    afterInit(server) {
        this.wsEmitterService.setServer(server);
    }
    async handleConnection(client, ...args) {
        this.logger.log(`Client connected: ${client.id} from IP: ${client.handshake.address}`);
        try {
            this.logger.log(`WebSocket handshake: ${JSON.stringify({
                auth: client.handshake?.auth,
                headers: client.handshake?.headers,
                query: client.handshake?.query
            })}`);
            const token = this.extractToken(client);
            if (!token) {
                this.logger.warn(`Client ${client.id}: No token provided.`);
                client.emit('error', { message: 'Unauthorized: No token provided.', code: 401 });
                client.disconnect(true);
                return;
            }
            this.logger.log(`Client ${client.id}: Token found, verifying...`);
            const jwtSecret = this.configService.get('JWT_SECRET');
            this.logger.log(`JWT_SECRET exists: ${!!jwtSecret}`);
            const payload = this.jwtService.verify(token, {
                secret: jwtSecret,
            });
            this.logger.log(`Token verified successfully for client ${client.id}`);
            this.logger.verbose(`Token payload: ${JSON.stringify(payload)}`);
            client.data = client.data || {};
            client.data.user = payload;
            const userRoom = `user_${payload.userId}`;
            client.join(userRoom);
            this.logger.verbose(`Client Authenticated & Joined Room: ${client.id}, UserID: ${payload.userId}, Room: ${userRoom}`);
        }
        catch (error) {
            this.logger.warn(`Client ${client.id}: WebSocket Auth Error - ${error.message}`);
            client.emit('error', { message: error.message || 'Unauthorized', code: 401 });
            client.disconnect(true);
        }
    }
    handleDisconnect(client) {
        const user = client.data.user;
        this.logger.log(`Client disconnected: ${client.id} ${user ? `(User: ${user.userId})` : ''}`);
    }
    handlePrivateMessage(client, payload) {
        const user = client.data.user;
        this.logger.log(`Received privateMessage from User ${user.userId} (${client.id}):`, payload);
        this.wsEmitterService.emitToUser(user.userId, 'privateReply', {
            message: 'Your private message was received!',
            original: payload,
        });
    }
    handlePublicPing(client, payload) {
        this.logger.log(`Received publicPing from ${client.id}:`, payload);
        return {
            event: 'pong',
            data: { message: 'Public Pong!', timestamp: new Date() },
        };
    }
    handleJoinMyRoom(client) {
        const user = client.data.user;
        const userRoom = `user_${user.userId}`;
        if (!client.rooms.has(userRoom)) {
            client.join(userRoom);
            this.logger.log(`Client ${client.id} manually joined room: ${userRoom}`);
        }
        else {
            this.logger.log(`Client ${client.id} was already in room: ${userRoom}`);
        }
        client.emit('roomJoined', { room: userRoom });
    }
    extractToken(client) {
        const authHeader = client.handshake.headers.authorization;
        let token = undefined;
        this.logger.log(`Extracting token for client ${client.id}`);
        if (authHeader && typeof authHeader === 'string') {
            this.logger.log(`Found authorization header: ${authHeader.substring(0, 15)}...`);
            const parts = authHeader.split(' ');
            if (parts.length === 2 && parts[0] === 'Bearer') {
                token = parts[1];
                this.logger.log(`Extracted token from authorization header`);
            }
        }
        if (!token && client.handshake.auth) {
            const authToken = client.handshake.auth.token;
            if (authToken) {
                this.logger.log(`Found token in handshake.auth: ${typeof authToken === 'string' ? authToken.substring(0, 15) + '...' : 'not a string'}`);
                if (typeof authToken === 'string' && authToken.startsWith('Bearer ')) {
                    token = authToken.substring(7);
                    this.logger.log(`Extracted token from handshake.auth (with Bearer prefix)`);
                }
                else {
                    token = authToken;
                    this.logger.log(`Extracted token from handshake.auth (without Bearer prefix)`);
                }
            }
        }
        if (!token && client.handshake.query && client.handshake.query.token) {
            const queryToken = client.handshake.query.token;
            this.logger.log(`Found token in query: ${queryToken.substring(0, 15)}...`);
            if (queryToken.startsWith('Bearer ')) {
                token = queryToken.substring(7);
                this.logger.log(`Extracted token from query (with Bearer prefix)`);
            }
            else {
                token = queryToken;
                this.logger.log(`Extracted token from query (without Bearer prefix)`);
            }
        }
        if (token) {
            this.logger.log(`Successfully extracted token for client ${client.id}`);
        }
        else {
            this.logger.warn(`No token found for client ${client.id}`);
        }
        return token;
    }
};
exports.BaseGateway = BaseGateway;
__decorate([
    (0, websockets_1.WebSocketServer)(),
    __metadata("design:type", socket_io_1.Server)
], BaseGateway.prototype, "server", void 0);
__decorate([
    (0, common_1.UseGuards)(ws_jwt_guard_1.WsJwtGuard),
    (0, websockets_1.SubscribeMessage)('privateMessage'),
    __param(0, (0, websockets_1.ConnectedSocket)()),
    __param(1, (0, websockets_1.MessageBody)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [socket_io_1.Socket, Object]),
    __metadata("design:returntype", void 0)
], BaseGateway.prototype, "handlePrivateMessage", null);
__decorate([
    (0, websockets_1.SubscribeMessage)('publicPing'),
    __param(0, (0, websockets_1.ConnectedSocket)()),
    __param(1, (0, websockets_1.MessageBody)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [socket_io_1.Socket, Object]),
    __metadata("design:returntype", Object)
], BaseGateway.prototype, "handlePublicPing", null);
__decorate([
    (0, common_1.UseGuards)(ws_jwt_guard_1.WsJwtGuard),
    (0, websockets_1.SubscribeMessage)('joinMyRoom'),
    __param(0, (0, websockets_1.ConnectedSocket)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [socket_io_1.Socket]),
    __metadata("design:returntype", void 0)
], BaseGateway.prototype, "handleJoinMyRoom", null);
exports.BaseGateway = BaseGateway = BaseGateway_1 = __decorate([
    (0, websockets_1.WebSocketGateway)({
        namespace: process.env.WS_NAMESPACE || 'events',
        cors: {
            origin: process.env.CORS_ORIGIN?.split(',') || '*',
        },
    }),
    (0, common_1.UseFilters)(new ws_exception_filter_1.WsExceptionFilter()),
    (0, common_1.UsePipes)(new common_1.ValidationPipe({
        transform: true,
        whitelist: true,
        exceptionFactory: (errors) => new websockets_1.WsException(errors),
    })),
    __metadata("design:paramtypes", [config_1.ConfigService,
        websocket_emitter_service_1.WebSocketEmitterService,
        jwt_1.JwtService])
], BaseGateway);
//# sourceMappingURL=base.gateway.js.map
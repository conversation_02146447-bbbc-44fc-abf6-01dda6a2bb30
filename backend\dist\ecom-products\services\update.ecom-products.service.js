"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UpdateEcomProductsService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_transactional_1 = require("typeorm-transactional");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const event_emitter_1 = require("@nestjs/event-emitter");
const base_ecom_products_service_1 = require("./base.ecom-products.service");
const update_ecom_product_dto_1 = require("../dto/update-ecom-product.dto");
const ecom_product_categories_entity_1 = require("../../ecom-product-categories/entity/ecom-product-categories.entity");
const read_ecom_products_service_1 = require("./read.ecom-products.service");
const ecom_products_entity_1 = require("../entity/ecom-products.entity");
const vietnamese_slug_util_1 = require("../../common/utils/vietnamese-slug.util");
let UpdateEcomProductsService = class UpdateEcomProductsService extends base_ecom_products_service_1.BaseEcomProductsService {
    ecomProductRepository;
    dataSource;
    eventEmitter;
    readEcomProductsService;
    constructor(ecomProductRepository, dataSource, eventEmitter, readEcomProductsService) {
        super(ecomProductRepository, dataSource, eventEmitter);
        this.ecomProductRepository = ecomProductRepository;
        this.dataSource = dataSource;
        this.eventEmitter = eventEmitter;
        this.readEcomProductsService = readEcomProductsService;
    }
    async update(productId, updateEcomProductDto, userId) {
        try {
            const { id: dtoId, ...updateData } = updateEcomProductDto;
            const idToUpdate = productId || dtoId;
            this.logger.debug(`Đang cập nhật sản phẩm với ID: ${idToUpdate}`);
            let product;
            try {
                product = await this.findByIdOrFail(idToUpdate, ['category']);
            }
            catch (error) {
                if (error instanceof common_1.NotFoundException) {
                    throw new common_1.NotFoundException(`Không tìm thấy sản phẩm với ID "${idToUpdate}" hoặc đã bị xóa. Vui lòng kiểm tra lại ID.`);
                }
                throw error;
            }
            if (updateData.productCode && updateData.productCode !== product.productCode) {
                const isDuplicated = await this.readEcomProductsService.isProductCodeDuplicated(updateData.productCode, idToUpdate);
                if (isDuplicated) {
                    throw new common_1.BadRequestException(`Mã sản phẩm ${updateData.productCode} đã tồn tại`);
                }
            }
            if (updateData.categoryId && updateData.categoryId !== product.categoryId) {
                const categoryExists = await this.dataSource.getRepository(ecom_product_categories_entity_1.EcomProductCategories).findOne({
                    where: { id: updateData.categoryId },
                });
                if (!categoryExists) {
                    throw new common_1.NotFoundException(`Không tìm thấy danh mục sản phẩm với ID ${updateData.categoryId}`);
                }
            }
            let slugToUpdate = updateData.slug;
            if (updateData.productName && updateData.productName !== product.productName && !updateData.slug) {
                const existingSlugs = await this.ecomProductRepository
                    .createQueryBuilder('product')
                    .select('product.slug')
                    .where('product.slug IS NOT NULL')
                    .andWhere('product.isDeleted = :isDeleted', { isDeleted: false })
                    .andWhere('product.id != :currentId', { currentId: idToUpdate })
                    .getRawMany()
                    .then(results => results.map(r => r.product_slug).filter(Boolean));
                slugToUpdate = (0, vietnamese_slug_util_1.generateProductSlug)(updateData.productName, existingSlugs);
            }
            else if (updateData.slug && updateData.slug !== product.slug) {
                const existingSlugProduct = await this.ecomProductRepository.findOne({
                    where: { slug: updateData.slug, isDeleted: false },
                });
                if (existingSlugProduct && existingSlugProduct.id !== idToUpdate) {
                    throw new common_1.BadRequestException(`Slug "${updateData.slug}" đã được sử dụng`);
                }
            }
            const now = new Date();
            Object.assign(product, {
                ...updateData,
                slug: slugToUpdate !== undefined ? slugToUpdate : product.slug,
                updatedAt: now,
                updatedBy: userId || product.updatedBy,
            });
            const savedProduct = await this.ecomProductRepository.save(product);
            try {
                this.eventEmitter.emit(this.EVENT_PRODUCT_UPDATED, {
                    product: savedProduct,
                    userId,
                    previousState: product,
                });
            }
            catch (emitError) {
                this.logger.warn(`Không thể phát sự kiện cập nhật sản phẩm: ${emitError.message}`);
            }
            const requiredRelations = ['category', 'creator', 'updater', 'deleter'];
            const productWithRelations = await this.findByIdOrFail(savedProduct.id, requiredRelations);
            return this.toDto(productWithRelations);
        }
        catch (error) {
            this.logger.error(`Lỗi khi cập nhật sản phẩm: ${error.message}`, error.stack);
            if (error instanceof common_1.NotFoundException || error instanceof common_1.BadRequestException) {
                throw error;
            }
            throw new common_1.InternalServerErrorException(`Không thể cập nhật sản phẩm: ${error.message}`);
        }
    }
    async updateStock(id, quantity, userId) {
        try {
            this.logger.debug(`Đang cập nhật số lượng tồn kho sản phẩm với ID: ${id} thành ${quantity}`);
            const product = await this.findByIdOrFail(id);
            if (quantity < 0) {
                throw new common_1.BadRequestException('Số lượng tồn kho không thể âm');
            }
            const oldQuantity = product.stockQuantity;
            const now = new Date();
            product.stockQuantity = quantity;
            product.updatedAt = now;
            product.updatedBy = userId || product.updatedBy;
            const savedProduct = await this.ecomProductRepository.save(product);
            try {
                this.eventEmitter.emit(this.EVENT_PRODUCT_UPDATED, {
                    product: savedProduct,
                    userId,
                    previousState: { ...product, stockQuantity: oldQuantity },
                    updateType: 'STOCK',
                    oldQuantity,
                    newQuantity: quantity,
                });
            }
            catch (emitError) {
                this.logger.warn(`Không thể phát sự kiện cập nhật số lượng tồn kho: ${emitError.message}`);
            }
            const requiredRelations = ['category', 'creator', 'updater', 'deleter'];
            const productWithRelations = await this.findByIdOrFail(savedProduct.id, requiredRelations);
            return this.toDto(productWithRelations);
        }
        catch (error) {
            this.logger.error(`Lỗi khi cập nhật số lượng tồn kho: ${error.message}`, error.stack);
            if (error instanceof common_1.NotFoundException || error instanceof common_1.BadRequestException) {
                throw error;
            }
            throw new common_1.InternalServerErrorException(`Không thể cập nhật số lượng tồn kho: ${error.message}`);
        }
    }
    async updateStatus(id, isActive, userId) {
        try {
            this.logger.debug(`Đang cập nhật trạng thái sản phẩm với ID: ${id} thành ${isActive ? 'hoạt động' : 'không hoạt động'}`);
            const product = await this.findByIdOrFail(id);
            if (product.isActive === isActive) {
                this.logger.debug(`Sản phẩm với ID ${id} đã ở trạng thái ${isActive ? 'hoạt động' : 'không hoạt động'}`);
                return this.toDto(product);
            }
            const oldStatus = product.isActive;
            const now = new Date();
            product.isActive = isActive;
            product.updatedAt = now;
            product.updatedBy = userId || product.updatedBy;
            const savedProduct = await this.ecomProductRepository.save(product);
            try {
                this.eventEmitter.emit(this.EVENT_PRODUCT_UPDATED, {
                    product: savedProduct,
                    userId,
                    previousState: { ...product, isActive: oldStatus },
                    updateType: 'STATUS',
                    oldStatus,
                    newStatus: isActive,
                });
            }
            catch (emitError) {
                this.logger.warn(`Không thể phát sự kiện cập nhật trạng thái: ${emitError.message}`);
            }
            const requiredRelations = ['category', 'creator', 'updater', 'deleter'];
            const productWithRelations = await this.findByIdOrFail(savedProduct.id, requiredRelations);
            return this.toDto(productWithRelations);
        }
        catch (error) {
            this.logger.error(`Lỗi khi cập nhật trạng thái: ${error.message}`, error.stack);
            if (error instanceof common_1.NotFoundException) {
                throw error;
            }
            throw new common_1.InternalServerErrorException(`Không thể cập nhật trạng thái: ${error.message}`);
        }
    }
    async bulkUpdate(updateEcomProductDtos, userId) {
        try {
            this.logger.debug(`Đang cập nhật ${updateEcomProductDtos.length} sản phẩm`);
            if (!updateEcomProductDtos.length) {
                return [];
            }
            const updatedProducts = [];
            for (const updateDto of updateEcomProductDtos) {
                try {
                    if (!updateDto.id) {
                        this.logger.warn(`Bỏ qua cập nhật sản phẩm không có ID`);
                        continue;
                    }
                    const updatedProduct = await this.update(updateDto.id, updateDto, userId);
                    if (updatedProduct) {
                        updatedProducts.push(updatedProduct);
                    }
                }
                catch (error) {
                    this.logger.warn(`Không thể cập nhật sản phẩm với ID ${updateDto.id}: ${error.message}`);
                    continue;
                }
            }
            return updatedProducts;
        }
        catch (error) {
            this.logger.error(`Lỗi khi cập nhật nhiều sản phẩm: ${error.message}`, error.stack);
            throw new common_1.InternalServerErrorException(`Không thể cập nhật nhiều sản phẩm: ${error.message}`);
        }
    }
    async restore(id, userId) {
        try {
            this.logger.debug(`Đang khôi phục sản phẩm với ID: ${id}`);
            const product = await this.findByIdOrFail(id, [], true);
            if (!product.isDeleted) {
                throw new common_1.BadRequestException(`Sản phẩm với ID ${id} chưa bị xóa`);
            }
            const now = new Date();
            product.isDeleted = false;
            product.deletedAt = null;
            product.deletedBy = null;
            product.updatedBy = userId || product.updatedBy;
            product.updatedAt = now;
            const restoredProduct = await this.ecomProductRepository.save(product);
            try {
                this.eventEmitter.emit('ecom-product.restored', {
                    product: restoredProduct,
                    userId,
                });
            }
            catch (emitError) {
                this.logger.warn(`Không thể phát sự kiện khôi phục sản phẩm: ${emitError.message}`);
            }
            const requiredRelations = ['category', 'creator', 'updater'];
            const productWithRelations = await this.findByIdOrFail(restoredProduct.id, requiredRelations);
            return this.toDto(productWithRelations);
        }
        catch (error) {
            this.logger.error(`Lỗi khi khôi phục sản phẩm: ${error.message}`, error.stack);
            if (error instanceof common_1.NotFoundException || error instanceof common_1.BadRequestException) {
                throw error;
            }
            throw new common_1.InternalServerErrorException(`Không thể khôi phục sản phẩm: ${error.message}`);
        }
    }
};
exports.UpdateEcomProductsService = UpdateEcomProductsService;
__decorate([
    (0, typeorm_transactional_1.Transactional)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, update_ecom_product_dto_1.UpdateEcomProductDto, String]),
    __metadata("design:returntype", Promise)
], UpdateEcomProductsService.prototype, "update", null);
__decorate([
    (0, typeorm_transactional_1.Transactional)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Number, String]),
    __metadata("design:returntype", Promise)
], UpdateEcomProductsService.prototype, "updateStock", null);
__decorate([
    (0, typeorm_transactional_1.Transactional)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Boolean, String]),
    __metadata("design:returntype", Promise)
], UpdateEcomProductsService.prototype, "updateStatus", null);
__decorate([
    (0, typeorm_transactional_1.Transactional)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Array, String]),
    __metadata("design:returntype", Promise)
], UpdateEcomProductsService.prototype, "bulkUpdate", null);
__decorate([
    (0, typeorm_transactional_1.Transactional)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String]),
    __metadata("design:returntype", Promise)
], UpdateEcomProductsService.prototype, "restore", null);
exports.UpdateEcomProductsService = UpdateEcomProductsService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(ecom_products_entity_1.EcomProduct)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        typeorm_2.DataSource,
        event_emitter_1.EventEmitter2,
        read_ecom_products_service_1.ReadEcomProductsService])
], UpdateEcomProductsService);
//# sourceMappingURL=update.ecom-products.service.js.map
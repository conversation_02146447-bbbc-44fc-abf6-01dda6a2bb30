"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const app_module_1 = require("./app.module");
const common_1 = require("@nestjs/common");
const typeorm_1 = require("typeorm");
const typeorm_transactional_1 = require("typeorm-transactional");
const core_1 = require("@nestjs/core");
async function bootstrap() {
    const logger = new common_1.Logger('CLI');
    try {
        logger.log('Initializing transactional context...');
        (0, typeorm_transactional_1.initializeTransactionalContext)();
        const app = await core_1.NestFactory.create(app_module_1.AppModule, {
            logger: ['error', 'warn', 'log', 'debug'],
        });
        const dataSource = app.get(typeorm_1.DataSource);
        if (!dataSource) {
            logger.error('FATAL ERROR: Could not retrieve DataSource instance from NestJS container.');
            process.exit(1);
        }
        if (!dataSource.isInitialized) {
            logger.log('DataSource not initialized yet, attempting to initialize...');
            await dataSource.initialize();
            logger.log('DataSource initialized successfully.');
        }
        (0, typeorm_transactional_1.addTransactionalDataSource)(dataSource);
        logger.log('Transactional context initialized successfully.');
        try {
            const { CreateRewardWalletsCommand } = await Promise.resolve().then(() => require('./commands/create-reward-wallets.command'));
            const commandInstance = app.get(CreateRewardWalletsCommand);
            logger.log('Executing create-reward-wallets command...');
            await commandInstance.run();
            logger.log('Command execution completed.');
        }
        finally {
            await app.close();
        }
    }
    catch (error) {
        logger.error(`CLI execution failed: ${error.message}`, error.stack);
        process.exit(1);
    }
}
bootstrap();
//# sourceMappingURL=cli.js.map
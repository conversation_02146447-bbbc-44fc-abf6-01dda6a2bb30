"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.OrderBookDetailDto = void 0;
const openapi = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
const swagger_1 = require("@nestjs/swagger");
const class_transformer_1 = require("class-transformer");
const ecom_products_entity_1 = require("../../ecom-products/entity/ecom-products.entity");
class OrderBookDetailDto {
    id;
    orderBookId;
    productId;
    price;
    quantity;
    totalPrice;
    product;
    static _OPENAPI_METADATA_FACTORY() {
        return { id: { required: true, type: () => String, format: "uuid" }, orderBookId: { required: true, type: () => String }, productId: { required: true, type: () => String, format: "uuid" }, price: { required: true, type: () => Number }, quantity: { required: true, type: () => Number }, totalPrice: { required: true, type: () => Number }, product: { required: true, type: () => require("../../ecom-products/entity/ecom-products.entity").EcomProduct } };
    }
}
exports.OrderBookDetailDto = OrderBookDetailDto;
__decorate([
    (0, class_transformer_1.Expose)(),
    (0, swagger_1.ApiProperty)({ description: 'ID duy nhất của chi tiết lệnh' }),
    (0, class_validator_1.IsUUID)(),
    __metadata("design:type", String)
], OrderBookDetailDto.prototype, "id", void 0);
__decorate([
    (0, class_transformer_1.Expose)(),
    (0, swagger_1.ApiProperty)({
        description: 'ID của order book',
        example: '550e8400-e29b-41d4-a716-************',
    }),
    (0, class_validator_1.IsNotEmpty)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], OrderBookDetailDto.prototype, "orderBookId", void 0);
__decorate([
    (0, class_transformer_1.Expose)(),
    (0, swagger_1.ApiProperty)({
        description: 'ID sản phẩm liên kết',
        example: '550e8400-e29b-41d4-a716-************',
        required: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsUUID)(),
    __metadata("design:type", String)
], OrderBookDetailDto.prototype, "productId", void 0);
__decorate([
    (0, class_transformer_1.Expose)(),
    (0, swagger_1.ApiProperty)({ description: 'Đơn giá', example: '1000000.00' }),
    (0, class_validator_1.IsNotEmpty)(),
    (0, class_validator_1.IsDecimal)({ decimal_digits: '2' }),
    __metadata("design:type", Number)
], OrderBookDetailDto.prototype, "price", void 0);
__decorate([
    (0, class_transformer_1.Expose)(),
    (0, swagger_1.ApiProperty)({ description: 'Số lượng', example: '1.0000' }),
    (0, class_validator_1.IsNotEmpty)(),
    (0, class_validator_1.IsDecimal)({ decimal_digits: '4' }),
    __metadata("design:type", Number)
], OrderBookDetailDto.prototype, "quantity", void 0);
__decorate([
    (0, class_transformer_1.Expose)(),
    (0, swagger_1.ApiProperty)({ description: 'Thành tiền', example: '1000000.00' }),
    (0, class_validator_1.IsNotEmpty)(),
    (0, class_validator_1.IsDecimal)({ decimal_digits: '2' }),
    __metadata("design:type", Number)
], OrderBookDetailDto.prototype, "totalPrice", void 0);
__decorate([
    (0, class_transformer_1.Expose)(),
    (0, swagger_1.ApiProperty)({ description: 'Sản phẩm liên kết' }),
    __metadata("design:type", ecom_products_entity_1.EcomProduct)
], OrderBookDetailDto.prototype, "product", void 0);
//# sourceMappingURL=order-book-detail.dto.js.map
"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.EcomOrderDetailDto = void 0;
const openapi = require("@nestjs/swagger");
const swagger_1 = require("@nestjs/swagger");
const class_transformer_1 = require("class-transformer");
const class_validator_1 = require("class-validator");
const ecom_order_dto_1 = require("../../ecom-orders/dto/ecom-order.dto");
const ecom_product_dto_1 = require("../../ecom-products/dto/ecom-product.dto");
const user_dto_1 = require("../../users/dto/user.dto");
class EcomOrderDetailDto {
    id;
    orderId;
    productId;
    quantity;
    unitPrice;
    totalPrice;
    createdAt;
    updatedAt;
    createdBy;
    updatedBy;
    deletedBy;
    deletedAt;
    isDeleted;
    order;
    product;
    creator;
    updater;
    deleter;
    static _OPENAPI_METADATA_FACTORY() {
        return { id: { required: true, type: () => String, format: "uuid" }, orderId: { required: true, type: () => String, format: "uuid" }, productId: { required: true, type: () => String, format: "uuid" }, quantity: { required: true, type: () => Number, minimum: 1 }, unitPrice: { required: true, type: () => Number }, totalPrice: { required: true, type: () => Number }, createdAt: { required: true, type: () => Date }, updatedAt: { required: true, type: () => Date }, createdBy: { required: false, type: () => String, nullable: true, format: "uuid" }, updatedBy: { required: false, type: () => String, nullable: true, format: "uuid" }, deletedBy: { required: false, type: () => String, nullable: true, format: "uuid" }, deletedAt: { required: false, type: () => Date, nullable: true }, isDeleted: { required: true, type: () => Boolean }, order: { required: false, type: () => require("../../ecom-orders/dto/ecom-order.dto").EcomOrderDto }, product: { required: false, type: () => require("../../ecom-products/dto/ecom-product.dto").EcomProductDto }, creator: { required: false, type: () => require("../../users/dto/user.dto").UserDto, nullable: true }, updater: { required: false, type: () => require("../../users/dto/user.dto").UserDto, nullable: true }, deleter: { required: false, type: () => require("../../users/dto/user.dto").UserDto, nullable: true } };
    }
}
exports.EcomOrderDetailDto = EcomOrderDetailDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'ID của chi tiết đơn hàng',
        example: '550e8400-e29b-41d4-a716-************',
    }),
    (0, class_validator_1.IsUUID)(),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", String)
], EcomOrderDetailDto.prototype, "id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'ID đơn hàng',
        example: '550e8400-e29b-41d4-a716-************',
    }),
    (0, class_validator_1.IsNotEmpty)(),
    (0, class_validator_1.IsUUID)(),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", String)
], EcomOrderDetailDto.prototype, "orderId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'ID sản phẩm',
        example: '550e8400-e29b-41d4-a716-************',
    }),
    (0, class_validator_1.IsNotEmpty)(),
    (0, class_validator_1.IsUUID)(),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", String)
], EcomOrderDetailDto.prototype, "productId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Số lượng',
        example: 2,
    }),
    (0, class_validator_1.IsNotEmpty)(),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(1),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", Number)
], EcomOrderDetailDto.prototype, "quantity", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Đơn giá',
        example: 1500000,
    }),
    (0, class_validator_1.IsNotEmpty)(),
    (0, class_validator_1.IsDecimal)({ decimal_digits: '2' }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", Number)
], EcomOrderDetailDto.prototype, "unitPrice", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Tổng giá trị',
        example: 3000000,
    }),
    (0, class_validator_1.IsNotEmpty)(),
    (0, class_validator_1.IsDecimal)({ decimal_digits: '2' }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", Number)
], EcomOrderDetailDto.prototype, "totalPrice", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Thời gian tạo',
        example: '2021-01-01T00:00:00.000Z',
    }),
    (0, class_validator_1.IsDate)(),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", Date)
], EcomOrderDetailDto.prototype, "createdAt", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Thời gian cập nhật',
        example: '2021-01-01T00:00:00.000Z',
    }),
    (0, class_validator_1.IsDate)(),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", Date)
], EcomOrderDetailDto.prototype, "updatedAt", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'ID người tạo',
        example: '550e8400-e29b-41d4-a716-************',
    }),
    (0, class_validator_1.IsUUID)(),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", Object)
], EcomOrderDetailDto.prototype, "createdBy", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'ID người cập nhật',
        example: '550e8400-e29b-41d4-a716-************',
    }),
    (0, class_validator_1.IsUUID)(),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", Object)
], EcomOrderDetailDto.prototype, "updatedBy", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'ID người xóa',
        example: '550e8400-e29b-41d4-a716-************',
    }),
    (0, class_validator_1.IsUUID)(),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", Object)
], EcomOrderDetailDto.prototype, "deletedBy", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Thời gian xóa',
        example: '2021-01-01T00:00:00.000Z',
    }),
    (0, class_validator_1.IsDate)(),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", Object)
], EcomOrderDetailDto.prototype, "deletedAt", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Trạng thái xóa',
        example: false,
    }),
    (0, class_validator_1.IsBoolean)(),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", Boolean)
], EcomOrderDetailDto.prototype, "isDeleted", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Đơn hàng',
        type: () => ecom_order_dto_1.EcomOrderDto,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Expose)(),
    (0, class_transformer_1.Type)(() => ecom_order_dto_1.EcomOrderDto),
    __metadata("design:type", ecom_order_dto_1.EcomOrderDto)
], EcomOrderDetailDto.prototype, "order", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Sản phẩm',
        type: () => ecom_product_dto_1.EcomProductDto,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Expose)(),
    (0, class_transformer_1.Type)(() => ecom_product_dto_1.EcomProductDto),
    __metadata("design:type", ecom_product_dto_1.EcomProductDto)
], EcomOrderDetailDto.prototype, "product", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Người tạo',
        type: () => user_dto_1.UserDto,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Expose)(),
    (0, class_transformer_1.Type)(() => user_dto_1.UserDto),
    __metadata("design:type", Object)
], EcomOrderDetailDto.prototype, "creator", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Người cập nhật',
        type: () => user_dto_1.UserDto,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Expose)(),
    (0, class_transformer_1.Type)(() => user_dto_1.UserDto),
    __metadata("design:type", Object)
], EcomOrderDetailDto.prototype, "updater", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Người xóa',
        type: () => user_dto_1.UserDto,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Expose)(),
    (0, class_transformer_1.Type)(() => user_dto_1.UserDto),
    __metadata("design:type", Object)
], EcomOrderDetailDto.prototype, "deleter", void 0);
//# sourceMappingURL=ecom-order-detail.dto.js.map
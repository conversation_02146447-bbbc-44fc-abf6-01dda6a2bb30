import { Module } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { ScheduleModule } from '@nestjs/schedule'; // Import nếu dùng task scheduling
import { ThrottlerModule } from '@nestjs/throttler'; // Import nếu dùng rate limiting
import { CommonModule } from './common/common.module';

import { ActivityLogsModule } from './activity-logs/activity-logs.module';
import { AgentCommissionModule } from './agent-commissions/agent-commission.module';
import { AgentModule } from './agents/agent.module';
import { AttachmentModule } from './attachments/attachment.module';
import { AuthModule } from './auth/auth.module';
import { BanksModule } from './banks/banks.module';

import { DatabaseModule } from './database/database.module';
import { MailerModule } from './mailer/mailer.module';
import { OrderBookModule } from './order-book/order-book.module';
import { VnpayPaymentModule } from './payment-gateways/vnpay-payment.module';
import { PaymentMethodsModule } from './payment-methods/payment-methods.module';
import { PermissionsModule } from './permissions/permissions.module';
import { PriceAlertsModule } from './price-alerts/price-alerts.module';
import { RoleModule } from './roles/role.module';
import { SystemConfigModule } from './system-configs/system-config.module';
import { AssetModule } from './token-assets/asset.module';
import { TokenCategoryModule } from './token-categories/token-category.module';
import { TokenPriceModule } from './token-prices/token-price.module';
import { TokenModule } from './tokens/token.module';
// import { TradingViewModule } from './modules/tradingview/tradingview.module'; // Đã được thay thế bằng Polygon.io
import { CommandsModule } from './commands/commands.module';
import { CryptoTransactionsModule } from './crypto-transactions/crypto-transactions.module';
import { CryptoWalletsModule } from './crypto-wallets/crypto-wallets.module';
import { TransactionsModule } from './transactions/transactions.module';
import { UserKycModule } from './user-kyc/user-kyc.module';
import { UserModule } from './users/user.module';
import { WalletsModule } from './wallets/wallets.module';
import { CmsContactsModule } from './cms-contacts/cms-contacts.module';

import {
  AcceptLanguageResolver,
  HeaderResolver,
  I18nModule
} from 'nestjs-i18n';
import * as path from 'path';

// Import các module tính năng khác của bạn ở đây
// ------
// ... các module khác (SystemConfigs, ActivityLogs, PriceAlerts...)

import { CacheModule } from '@nestjs/cache-manager';
import { EventEmitterModule } from '@nestjs/event-emitter';
import { redisStore } from 'cache-manager-redis-store';
import * as Joi from 'joi'; // Import Joi để validation .env
import { LoggerModule } from 'nestjs-pino';
import { CmsBannersModule } from './cms-banners/cms-banners.module';
import { CmsCategoriesModule } from './cms-categories/cms-categories.module';
import { CmsCustomerFeedbacksModule } from './cms-customer-feedbacks/cms-customer-feedbacks.module';
import { CmsMenusModule } from './cms-menus/cms-menus.module';
import { CmsPagesModule } from './cms-pages/cms-pages.module';
import { CmsPartnersModule } from './cms-partners/cms-partners.module';
import { CmsPostsModule } from './cms-posts/cms-posts.module';
import { CmsShowroomsModule } from './cms-showrooms/cms-showrooms.module';
import { CmsTagsModule } from './cms-tags/cms-tags.module';
import { MinioModule } from './common/minio';
import { WebsocketsModule } from './common/websockets/websockets.module';
import { EcomOrderDetailsModule } from './ecom-order-details/ecom-order-details.module';
import { EcomOrdersModule } from './ecom-orders/ecom-orders.module';
import { EcomProductCategoriesModule } from './ecom-product-categories/ecom-product-categories.module';
import { EcomProductsModule } from './ecom-products/ecom-products.module';
import { HealthModule } from './health/health.module';
import { NotificationsModule } from './notifications/notifications.module';
import { ReportsModule } from './reports/reports.module';
import { ScheduleJobsModule } from './schedules/schedule-jobs.module';
@Module({
  imports: [
    // --- Cấu hình ---
    ConfigModule.forRoot({
      isGlobal: true, // Làm cho ConfigModule và ConfigService có sẵn toàn cục
      envFilePath: process.env.NODE_ENV === 'test' ? '.env.test' : '.env', // Hỗ trợ các file .env khác nhau
      validationSchema: Joi.object({
        // Validate các biến môi trường cần thiết
        NODE_ENV: Joi.string()
          .valid('development', 'production', 'test')
          .default('development'),
        PORT: Joi.number().default(3168),
        API_PREFIX: Joi.string().default('api'),
        API_DEFAULT_VERSION: Joi.string().default('1'),
        CORS_ORIGIN: Joi.string().default('*'),

        DATABASE_TYPE: Joi.string().required(),
        DATABASE_HOST: Joi.string().required(),
        DATABASE_PORT: Joi.number().required(),
        DATABASE_USERNAME: Joi.string().required(),
        DATABASE_PASSWORD: Joi.string().required(),
        DATABASE_NAME: Joi.string().required(),
        DATABASE_POOL_MAX: Joi.number().default(10),
        DATABASE_POOL_IDLE_TIMEOUT: Joi.number().default(30000),

        JWT_SECRET: Joi.string().required(),
        JWT_EXPIRATION_TIME: Joi.string().required(),

        SWAGGER_TITLE: Joi.string().default('API Docs'),
        SWAGGER_DESC: Joi.string().default('API Documentation'),
        SWAGGER_VERSION: Joi.string().default('1.0'),
        SWAGGER_PATH: Joi.string().default('api-docs'),
        PROD_URL: Joi.string().uri().optional(), // URL production là tùy chọn

        LOG_LEVEL: Joi.string()
          .valid('debug', 'info', 'warn', 'error')
          .default('debug'),

        THROTTLE_TTL: Joi.number().default(60000),
        THROTTLE_LIMIT: Joi.number().default(20),

        // MinIO Configuration
        MINIO_ENDPOINT: Joi.string().default('localhost'),
        MINIO_PORT: Joi.number().default(9000),
        MINIO_USE_SSL: Joi.boolean().default(false),
        MINIO_ACCESS_KEY: Joi.string().default('minioadmin'),
        MINIO_SECRET_KEY: Joi.string().default('minioadmin'),
        MINIO_REGION: Joi.string().default('us-east-1'),
        MINIO_DEFAULT_BUCKET: Joi.string().default('uploads'),

        // Thêm các biến môi trường khác cần validate
      }),
      validationOptions: {
        allowUnknown: true, // Cho phép các biến môi trường không được định nghĩa trong schema
        abortEarly: false, // Báo cáo tất cả các lỗi validation cùng lúc
      },
    }),

    // --- Logger ---
    LoggerModule.forRootAsync({
      imports: [ConfigModule], // Import ConfigModule để dùng ConfigService
      inject: [ConfigService],
      useFactory: (configService: ConfigService) => {
        const nodeEnv = configService.get<string>('NODE_ENV', 'development');
        const logLevel = configService.get<string>('LOG_LEVEL', 'info');
        const redactHeaders =
          configService
            .get<string>(
              'LOG_REDACT_HEADERS',
              'Authorization,Cookie,Set-Cookie',
            )
            ?.split(',') ?? [];

        return {
          pinoHttp: {
            level: logLevel,
            // Chỉ dùng pino-pretty ở môi trường development
            transport:
              nodeEnv !== 'production'
                ? {
                  target: 'pino-pretty',
                  options: {
                    singleLine: true, // Hiển thị log trên một dòng
                    colorize: true, // Thêm màu sắc
                    levelFirst: true, // Hiển thị level log trước
                    translateTime: 'SYS:yyyy-mm-dd HH:MM:ss.l', // Định dạng thời gian dễ đọc
                    ignore: 'pid,hostname', // Bỏ qua các trường ít quan trọng trong dev
                  },
                }
                : undefined, // Production: Ghi JSON ra stdout/stderr (tốt cho container/log collectors)

            // Tự động log request/response
            // autoLogging: true, // Mặc định là true

            // Che dấu thông tin nhạy cảm trong headers
            redact: {
              paths: [
                'req.headers.authorization',
                'req.headers.cookie',
                'res.headers["set-cookie"]',
                // Thêm các đường dẫn khác cần redact nếu có (vd: req.body.password)
              ].concat(
                redactHeaders.map(
                  (h) => `req.headers["${h.toLowerCase().trim()}"]`,
                ),
              ), // Thêm header từ env
              censor: '** REDACTED **', // Chuỗi thay thế
            },

            // (Tùy chọn) Custom serializers để thay đổi cách log req/res
            // serializers: {
            //    req(req) {
            //       req.body = req.raw.body; // Đưa body vào log (cẩn thận với dữ liệu lớn/nhạy cảm)
            //       return req;
            //    },
            //    // res(res) { return res; } // Mặc định đã khá tốt
            // },

            // (Tùy chọn) Custom log message cho request/response
            // customSuccessMessage: function (req, res) { return `${req.method} ${req.url} - ${res.statusCode}`; },
            // customErrorMessage: function (req, res, err) { return `${req.method} ${req.url} - ${res.statusCode} - ${err.message}`; },
          },
        };
      },
    }),

    // --- Cache ---
    CacheModule.registerAsync({
      isGlobal: true, // Đặt cache là global để dễ inject
      imports: [ConfigModule], // Import ConfigModule để dùng ConfigService
      useFactory: async (configService: ConfigService) => ({
        store: redisStore, // Chỉ định store là redis
        // Lấy cấu hình từ .env thông qua ConfigService
        host: configService.get<string>('REDIS_HOST', 'localhost'),
        port: configService.get<number>('REDIS_PORT', 6379),
        password: configService.get<string>('REDIS_PASSWORD'), // Để trống nếu không có pass
        ttl: configService.get<number>('CACHE_TTL', 60), // Time-to-live mặc định (giây)
        // Các tùy chọn khác của redisStore nếu cần
        db: configService.get<number>('REDIS_DB', 0),
      }),
      inject: [ConfigService], // Inject ConfigService vào factory
    }),

    // --- Database ---
    DatabaseModule, // Import DatabaseModule (đã là Global)

    // --- Common Services ---
    CommonModule, // Import CommonModule (Global) - Contains all common services including ConnectionPoolService, OptimisticLockingService, BusinessCodeGeneratorService, etc.

    // --- I18n ---
    I18nModule.forRoot({
      fallbackLanguage: 'en', // Ngôn ngữ mặc định nếu không tìm thấy hoặc không hỗ trợ
      loaderOptions: {
        path: path.join(__dirname, '/i18n/'), // Đường dẫn tới thư mục chứa các file ngôn ngữ
        watch: false, // Tự động reload khi file ngôn ngữ thay đổi (chỉ trong development)
      },
      resolvers: [
        // { use: QueryResolver, options: ['lang'] }, // Ưu tiên 1: Lấy từ query param ?lang=vi
        new HeaderResolver(['x-custom-lang']), // Ưu tiên 2: Lấy từ header tùy chỉnh 'x-custom-lang'
        AcceptLanguageResolver, // Ưu tiên 3: Lấy từ header 'Accept-Language' (phổ biến nhất)
        // { use: CookieResolver }, // Có thể lấy từ cookie nếu cần
      ],
      // Tùy chọn: Chỉ định các ngôn ngữ được hỗ trợ để tối ưu
      // supportedLanguages: ['en', 'vi'],
    }),

    // --- Rate Limiting (Tùy chọn) ---
    ThrottlerModule.forRootAsync({
      imports: [ConfigModule],
      inject: [ConfigService],
      useFactory: (config: ConfigService) => [
        {
          ttl: config.get<number>('THROTTLE_TTL', 60000),
          limit: config.get<number>('THROTTLE_LIMIT', 20),
          // ignoreUserAgents: [/googlebot/i, /bingbot/i], // Bỏ qua các bot nếu cần
        },
      ],
    }),

    // --- Task Scheduling (Tùy chọn) ---
    ScheduleModule.forRoot(),
    ScheduleJobsModule,

    // --- Event Emitter ---
    EventEmitterModule.forRoot({
      // Kích hoạt EventEmitterModule
      // Tùy chọn cấu hình:
      // wildcard: false, // Cho phép listener lắng nghe nhiều event bằng wildcard (vd: 'order.*')
      // delimiter: '.', // Ký tự phân tách cho wildcard
      // newListener: false, // Emit event khi có listener mới được thêm
      // removeListener: false, // Emit event khi listener bị xóa
      // maxListeners: 10, // Số listener tối đa cho một event (mặc định của Node.js)
      // verboseMemoryLeak: false, // Cảnh báo chi tiết về memory leak
      // ignoreErrors: false, // Bỏ qua lỗi từ listener (không khuyến khích)
    }),

    // --- Module Tính Năng ---
    AuthModule,
    UserModule,
    RoleModule,
    PermissionsModule,
    TokenModule,
    TokenCategoryModule,
    TokenPriceModule,
    AssetModule,
    OrderBookModule,
    TransactionsModule,
    PriceAlertsModule,
    WalletsModule,
    CryptoWalletsModule,
    CryptoTransactionsModule,
    PaymentMethodsModule,
    VnpayPaymentModule,
    BanksModule,
    AgentModule,
    AgentCommissionModule,
    // TradingViewModule, // Đã được thay thế bằng Polygon.io

    UserKycModule,
    AttachmentModule,
    ActivityLogsModule,
    SystemConfigModule,
    MailerModule,

    // --- Module Notifications ---
    NotificationsModule,

    // --- Module Websockets ---
    WebsocketsModule,

    // --- Module Health ---
    HealthModule,

    // --- Module Reports ---
    ReportsModule,

    // --- Module MinIO ---
    MinioModule,

    // --- Module Commands ---
    CommandsModule,

    // --- Module CMS & Ecommerce ---
    EcomProductCategoriesModule,
    CmsCategoriesModule,
    CmsMenusModule,
    CmsPostsModule,
    CmsTagsModule,
    CmsBannersModule,
    CmsCustomerFeedbacksModule,
    CmsPagesModule,
    CmsShowroomsModule,
    CmsPartnersModule,
    EcomProductsModule,
    EcomOrdersModule,
    EcomOrderDetailsModule,
    CmsContactsModule,
  ],
  controllers: [
    // AppController thường không cần thiết nếu bạn có các controller theo feature
    // AppController,
  ],
  providers: [
    // AppService thường không cần thiết
    // AppService,
    // (Tùy chọn) Cung cấp ThrottlerGuard làm Guard global
    // Nếu bạn bỏ comment dòng này, tất cả các endpoint sẽ bị giới hạn tỷ lệ
    // {
    //   provide: APP_GUARD,
    //   useClass: ThrottlerGuard,
    // },
    // Lưu ý: Không nên cung cấp JwtAuthGuard làm APP_GUARD ở đây
    // vì nó sẽ yêu cầu xác thực cho TẤT CẢ các route, bao gồm cả login/register.
  ],
})
export class AppModule {
  constructor() {
    const i18nPath = path.join(__dirname, '/i18n/');
    
    // Thêm kiểm tra xem thư mục có tồn tại không (chỉ mang tính tham khảo)
    try {
      const fs = require('fs');
      const exists = fs.existsSync(i18nPath);
      
      if (exists) {
        const files = fs.readdirSync(i18nPath);
        
        const enPath = path.join(i18nPath, 'en', 'translation.json');
        const enExists = fs.existsSync(enPath);
        
    
      }
    } catch (err) {
      console.error('[AppModule] Lỗi khi kiểm tra thư mục i18n:', err);
    }
  }
}

"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ReadCmsCustomerFeedbacksService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const event_emitter_1 = require("@nestjs/event-emitter");
const base_cms_customer_feedbacks_service_1 = require("./base.cms-customer-feedbacks.service");
const cms_customer_feedbacks_entity_1 = require("../entity/cms-customer-feedbacks.entity");
let ReadCmsCustomerFeedbacksService = class ReadCmsCustomerFeedbacksService extends base_cms_customer_feedbacks_service_1.BaseCmsCustomerFeedbacksService {
    feedbackRepository;
    dataSource;
    eventEmitter;
    constructor(feedbackRepository, dataSource, eventEmitter) {
        super(feedbackRepository, dataSource, eventEmitter);
        this.feedbackRepository = feedbackRepository;
        this.dataSource = dataSource;
        this.eventEmitter = eventEmitter;
    }
    async findAll(params) {
        try {
            this.logger.debug(`Đang tìm tất cả feedback khách hàng CMS với tham số: ${JSON.stringify(params)}`);
            const { limit, page, search } = params;
            const skip = (page - 1) * limit;
            let relationsArray = params.relationsArray || [];
            const requiredRelations = ['creator', 'updater', 'deleter', 'approver'];
            requiredRelations.forEach(relation => {
                if (!relationsArray.includes(relation)) {
                    relationsArray.push(relation);
                }
            });
            const validatedRelations = this.validateRelations(relationsArray);
            const queryBuilder = this.feedbackRepository
                .createQueryBuilder('feedback')
                .where('feedback.isDeleted = :isDeleted', { isDeleted: false });
            if (search) {
                queryBuilder.andWhere(new typeorm_2.Brackets(qb => {
                    qb.where('LOWER(feedback.customerName) LIKE LOWER(:search)', { search: `%${search}%` })
                        .orWhere('LOWER(feedback.feedbackText) LIKE LOWER(:search)', { search: `%${search}%` })
                        .orWhere('LOWER(feedback.productServiceName) LIKE LOWER(:search)', { search: `%${search}%` })
                        .orWhere('LOWER(feedback.customerTitleCompany) LIKE LOWER(:search)', { search: `%${search}%` });
                }));
            }
            const sortOptions = params.sortOptions;
            if (sortOptions && sortOptions.length > 0) {
                const { field, order } = sortOptions[0];
                queryBuilder.orderBy(`feedback.${field}`, order);
            }
            else {
                queryBuilder.orderBy('feedback.createdAt', 'DESC');
            }
            validatedRelations.forEach(relation => {
                queryBuilder.leftJoinAndSelect(`feedback.${relation}`, relation);
            });
            queryBuilder.skip(skip).take(limit);
            const [feedbacks, total] = await queryBuilder.getManyAndCount();
            const feedbackDtos = this.toDtos(feedbacks);
            return {
                data: feedbackDtos,
                total,
            };
        }
        catch (error) {
            this.logger.error(`Lỗi khi tìm tất cả feedback khách hàng CMS: ${error.message}`, error.stack);
            if (error instanceof common_1.BadRequestException) {
                throw error;
            }
            throw new common_1.InternalServerErrorException(`Không thể lấy danh sách feedback khách hàng CMS: ${error.message}`);
        }
    }
    async findByStatus(status, params) {
        try {
            this.logger.debug(`Đang tìm feedback CMS theo trạng thái: ${status}`);
            const queryBuilder = this.feedbackRepository
                .createQueryBuilder('feedback')
                .where('feedback.isDeleted = :isDeleted', { isDeleted: false })
                .andWhere('feedback.status = :status', { status });
            if (params.search) {
                queryBuilder.andWhere(new typeorm_2.Brackets(qb => {
                    qb.where('LOWER(feedback.customerName) LIKE LOWER(:search)', { search: `%${params.search}%` })
                        .orWhere('LOWER(feedback.feedbackText) LIKE LOWER(:search)', { search: `%${params.search}%` })
                        .orWhere('LOWER(feedback.productServiceName) LIKE LOWER(:search)', { search: `%${params.search}%` });
                }));
            }
            const sortOptions = params.sortOptions;
            if (sortOptions && sortOptions.length > 0) {
                const { field, order } = sortOptions[0];
                queryBuilder.orderBy(`feedback.${field}`, order);
            }
            else {
                queryBuilder.orderBy('feedback.createdAt', 'DESC');
            }
            const skip = (params.page - 1) * params.limit;
            queryBuilder.skip(skip).take(params.limit);
            const [feedbacks, total] = await queryBuilder.getManyAndCount();
            const feedbackDtos = this.toDtos(feedbacks);
            return { data: feedbackDtos, total };
        }
        catch (error) {
            this.logger.error(`Lỗi khi tìm feedback theo trạng thái: ${error.message}`, error.stack);
            throw new common_1.InternalServerErrorException(`Không thể tìm feedback theo trạng thái: ${error.message}`);
        }
    }
    async findByRating(rating, params) {
        try {
            this.logger.debug(`Đang tìm feedback CMS theo rating: ${rating}`);
            const queryBuilder = this.feedbackRepository
                .createQueryBuilder('feedback')
                .where('feedback.isDeleted = :isDeleted', { isDeleted: false })
                .andWhere('feedback.rating = :rating', { rating });
            if (params.search) {
                queryBuilder.andWhere(new typeorm_2.Brackets(qb => {
                    qb.where('LOWER(feedback.customerName) LIKE LOWER(:search)', { search: `%${params.search}%` })
                        .orWhere('LOWER(feedback.feedbackText) LIKE LOWER(:search)', { search: `%${params.search}%` });
                }));
            }
            queryBuilder.orderBy('feedback.createdAt', 'DESC');
            const skip = (params.page - 1) * params.limit;
            queryBuilder.skip(skip).take(params.limit);
            const [feedbacks, total] = await queryBuilder.getManyAndCount();
            const feedbackDtos = this.toDtos(feedbacks);
            return { data: feedbackDtos, total };
        }
        catch (error) {
            this.logger.error(`Lỗi khi tìm feedback theo rating: ${error.message}`, error.stack);
            throw new common_1.InternalServerErrorException(`Không thể tìm feedback theo rating: ${error.message}`);
        }
    }
    async getApprovedFeedbacks(params) {
        try {
            this.logger.debug(`Đang lấy feedback đã được duyệt`);
            return this.findByStatus(cms_customer_feedbacks_entity_1.CmsCustomerFeedbackStatus.APPROVED, params);
        }
        catch (error) {
            this.logger.error(`Lỗi khi lấy feedback đã duyệt: ${error.message}`, error.stack);
            throw new common_1.InternalServerErrorException(`Không thể lấy feedback đã duyệt: ${error.message}`);
        }
    }
    async getPendingFeedbacks(params) {
        try {
            this.logger.debug(`Đang lấy feedback chờ duyệt`);
            return this.findByStatus(cms_customer_feedbacks_entity_1.CmsCustomerFeedbackStatus.PENDING, params);
        }
        catch (error) {
            this.logger.error(`Lỗi khi lấy feedback chờ duyệt: ${error.message}`, error.stack);
            throw new common_1.InternalServerErrorException(`Không thể lấy feedback chờ duyệt: ${error.message}`);
        }
    }
    async findByProductService(productServiceName, params) {
        try {
            this.logger.debug(`Đang tìm feedback theo sản phẩm/dịch vụ: ${productServiceName}`);
            const queryBuilder = this.feedbackRepository
                .createQueryBuilder('feedback')
                .where('feedback.isDeleted = :isDeleted', { isDeleted: false })
                .andWhere('LOWER(feedback.productServiceName) LIKE LOWER(:productServiceName)', {
                productServiceName: `%${productServiceName}%`
            });
            queryBuilder.orderBy('feedback.createdAt', 'DESC');
            const skip = (params.page - 1) * params.limit;
            queryBuilder.skip(skip).take(params.limit);
            const [feedbacks, total] = await queryBuilder.getManyAndCount();
            const feedbackDtos = this.toDtos(feedbacks);
            return { data: feedbackDtos, total };
        }
        catch (error) {
            this.logger.error(`Lỗi khi tìm feedback theo sản phẩm/dịch vụ: ${error.message}`, error.stack);
            throw new common_1.InternalServerErrorException(`Không thể tìm feedback theo sản phẩm/dịch vụ: ${error.message}`);
        }
    }
    async count(filter) {
        try {
            const where = this.buildWhereClause(filter);
            return this.feedbackRepository.count({ where });
        }
        catch (error) {
            this.logger.error(`Lỗi khi đếm số lượng feedback CMS: ${error.message}`, error.stack);
            throw new common_1.InternalServerErrorException(`Không thể đếm số lượng feedback CMS: ${error.message}`);
        }
    }
    async findOne(id, relations = []) {
        try {
            this.logger.debug(`Đang tìm feedback CMS theo ID: ${id}`);
            const validatedRelations = this.validateRelations(relations);
            try {
                const feedback = await this.findById(id, validatedRelations, true);
                if (!feedback) {
                    throw new common_1.NotFoundException(`Không tìm thấy feedback với ID: ${id}`);
                }
                return this.toDto(feedback);
            }
            catch (notFoundError) {
                if (notFoundError instanceof common_1.NotFoundException) {
                    return null;
                }
                throw notFoundError;
            }
        }
        catch (error) {
            this.logger.error(`Lỗi khi tìm feedback CMS theo ID ${id}: ${error.message}`, error.stack);
            if (error instanceof common_1.NotFoundException) {
                return null;
            }
            throw new common_1.InternalServerErrorException(`Không thể lấy thông tin feedback CMS: ${error.message}`);
        }
    }
    async findOneOrFail(id, relations = []) {
        try {
            this.logger.debug(`Đang tìm feedback CMS theo ID: ${id}`);
            const validatedRelations = this.validateRelations(relations);
            const validRelationsToLoad = [...validatedRelations];
            if (!validRelationsToLoad.includes('creator')) {
                validRelationsToLoad.push('creator');
            }
            if (!validRelationsToLoad.includes('updater')) {
                validRelationsToLoad.push('updater');
            }
            if (!validRelationsToLoad.includes('approver')) {
                validRelationsToLoad.push('approver');
            }
            const feedback = await this.findById(id, validRelationsToLoad);
            if (!feedback) {
                throw new common_1.NotFoundException(`Không tìm thấy feedback với ID: ${id}`);
            }
            return this.toDto(feedback);
        }
        catch (error) {
            this.logger.error(`Lỗi khi tìm feedback CMS theo ID ${id}: ${error.message}`, error.stack);
            if (error instanceof common_1.NotFoundException) {
                throw error;
            }
            throw new common_1.InternalServerErrorException(`Không thể lấy thông tin feedback CMS: ${error.message}`);
        }
    }
    async findByBusinessCodePublic(businessCode) {
        try {
            this.logger.debug(`Đang tìm feedback CMS theo business code: ${businessCode}`);
            const feedback = await super.findByBusinessCode(businessCode, false);
            if (!feedback) {
                return null;
            }
            return this.toDto(feedback);
        }
        catch (error) {
            this.logger.error(`Lỗi khi tìm feedback theo business code: ${error.message}`, error.stack);
            throw new common_1.InternalServerErrorException(`Không thể tìm feedback theo business code: ${error.message}`);
        }
    }
    async search(keyword, params) {
        try {
            this.logger.debug(`Đang tìm kiếm feedback CMS với từ khóa: ${keyword}`);
            const queryBuilder = this.feedbackRepository
                .createQueryBuilder('feedback')
                .where('feedback.isDeleted = :isDeleted', { isDeleted: false });
            queryBuilder.andWhere(new typeorm_2.Brackets(qb => {
                qb.where('LOWER(feedback.customerName) LIKE LOWER(:keyword)', { keyword: `%${keyword}%` })
                    .orWhere('LOWER(feedback.feedbackText) LIKE LOWER(:keyword)', { keyword: `%${keyword}%` })
                    .orWhere('LOWER(feedback.productServiceName) LIKE LOWER(:keyword)', { keyword: `%${keyword}%` })
                    .orWhere('LOWER(feedback.customerTitleCompany) LIKE LOWER(:keyword)', { keyword: `%${keyword}%` });
            }));
            const sortOptions = params.sortOptions;
            if (sortOptions && sortOptions.length > 0) {
                const { field, order } = sortOptions[0];
                queryBuilder.orderBy(`feedback.${field}`, order);
            }
            else {
                queryBuilder.orderBy('feedback.createdAt', 'DESC');
            }
            const skip = (params.page - 1) * params.limit;
            queryBuilder.skip(skip).take(params.limit);
            const [feedbacks, total] = await queryBuilder.getManyAndCount();
            const feedbackDtos = this.toDtos(feedbacks);
            return { data: feedbackDtos, total };
        }
        catch (error) {
            this.logger.error(`Lỗi khi tìm kiếm feedback CMS: ${error.message}`, error.stack);
            throw new common_1.InternalServerErrorException(`Không thể tìm kiếm feedback CMS: ${error.message}`);
        }
    }
    async findDeleted(params) {
        try {
            this.logger.debug(`Đang tìm các feedback CMS đã bị xóa mềm với tham số: ${JSON.stringify(params)}`);
            const { limit, page } = params;
            const queryBuilder = this.feedbackRepository
                .createQueryBuilder('feedback')
                .where('feedback.isDeleted = :isDeleted', { isDeleted: true });
            if (params.search) {
                queryBuilder.andWhere(new typeorm_2.Brackets(qb => {
                    qb.where('LOWER(feedback.customerName) LIKE LOWER(:search)', { search: `%${params.search}%` })
                        .orWhere('LOWER(feedback.feedbackText) LIKE LOWER(:search)', { search: `%${params.search}%` });
                }));
            }
            const sortOptions = params.sortOptions;
            if (sortOptions && sortOptions.length > 0) {
                const { field, order } = sortOptions[0];
                queryBuilder.orderBy(`feedback.${field}`, order);
            }
            else {
                queryBuilder.orderBy('feedback.deletedAt', 'DESC');
            }
            queryBuilder.leftJoinAndSelect('feedback.creator', 'creator')
                .leftJoinAndSelect('feedback.updater', 'updater')
                .leftJoinAndSelect('feedback.deleter', 'deleter')
                .leftJoinAndSelect('feedback.approver', 'approver');
            const skip = (page - 1) * limit;
            queryBuilder.skip(skip).take(limit);
            const [feedbacks, total] = await queryBuilder.getManyAndCount();
            const feedbackDtos = this.toDtos(feedbacks);
            return { data: feedbackDtos, total };
        }
        catch (error) {
            this.logger.error(`Lỗi khi tìm feedback CMS đã xóa: ${error.message}`, error.stack);
            throw new common_1.InternalServerErrorException(`Không thể lấy danh sách feedback CMS đã xóa: ${error.message}`);
        }
    }
    async getStatistics() {
        try {
            const total = await this.feedbackRepository.count({
                where: { isDeleted: false },
            });
            const byStatusQuery = await this.feedbackRepository
                .createQueryBuilder('feedback')
                .select('feedback.status', 'status')
                .addSelect('COUNT(*)', 'count')
                .where('feedback.isDeleted = :isDeleted', { isDeleted: false })
                .groupBy('feedback.status')
                .getRawMany();
            const byStatus = {};
            byStatusQuery.forEach(item => {
                byStatus[item.status] = parseInt(item.count, 10);
            });
            const byRatingQuery = await this.feedbackRepository
                .createQueryBuilder('feedback')
                .select('feedback.rating', 'rating')
                .addSelect('COUNT(*)', 'count')
                .where('feedback.isDeleted = :isDeleted', { isDeleted: false })
                .andWhere('feedback.rating IS NOT NULL')
                .groupBy('feedback.rating')
                .getRawMany();
            const byRating = {
                '1': 0, '2': 0, '3': 0, '4': 0, '5': 0
            };
            byRatingQuery.forEach(item => {
                if (item.rating >= 1 && item.rating <= 5) {
                    byRating[item.rating.toString()] = parseInt(item.count, 10);
                }
            });
            const avgQuery = await this.feedbackRepository
                .createQueryBuilder('feedback')
                .select('AVG(feedback.rating)', 'average')
                .addSelect('COUNT(feedback.rating)', 'count')
                .where('feedback.isDeleted = :isDeleted', { isDeleted: false })
                .andWhere('feedback.rating IS NOT NULL')
                .getRawOne();
            const averageRating = avgQuery?.average ? parseFloat(avgQuery.average) : 0;
            const totalWithRating = avgQuery?.count ? parseInt(avgQuery.count, 10) : 0;
            return {
                total,
                byStatus,
                byRating,
                averageRating: Math.round(averageRating * 10) / 10,
                totalWithRating,
            };
        }
        catch (error) {
            this.logger.error(`Lỗi khi lấy thống kê feedback CMS: ${error.message}`, error.stack);
            throw new common_1.InternalServerErrorException(`Không thể lấy thống kê feedback CMS: ${error.message}`);
        }
    }
    async getHighRatingFeedbacks(limit = 10) {
        try {
            this.logger.debug(`Đang lấy ${limit} feedback rating cao`);
            const feedbacks = await this.feedbackRepository.find({
                where: {
                    isDeleted: false,
                    status: cms_customer_feedbacks_entity_1.CmsCustomerFeedbackStatus.APPROVED,
                    rating: 4
                },
                order: { rating: 'DESC', createdAt: 'DESC' },
                take: limit,
                relations: ['approver'],
            });
            const highRatingFeedbacks = await this.feedbackRepository
                .createQueryBuilder('feedback')
                .where('feedback.isDeleted = :isDeleted', { isDeleted: false })
                .andWhere('feedback.status = :status', { status: cms_customer_feedbacks_entity_1.CmsCustomerFeedbackStatus.APPROVED })
                .andWhere('feedback.rating >= :rating', { rating: 4 })
                .orderBy('feedback.rating', 'DESC')
                .addOrderBy('feedback.createdAt', 'DESC')
                .limit(limit)
                .getMany();
            return this.toDtos(highRatingFeedbacks);
        }
        catch (error) {
            this.logger.error(`Lỗi khi lấy feedback rating cao: ${error.message}`, error.stack);
            throw new common_1.InternalServerErrorException(`Không thể lấy feedback rating cao: ${error.message}`);
        }
    }
};
exports.ReadCmsCustomerFeedbacksService = ReadCmsCustomerFeedbacksService;
exports.ReadCmsCustomerFeedbacksService = ReadCmsCustomerFeedbacksService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(cms_customer_feedbacks_entity_1.CmsCustomerFeedbacks)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        typeorm_2.DataSource,
        event_emitter_1.EventEmitter2])
], ReadCmsCustomerFeedbacksService);
//# sourceMappingURL=read.cms-customer-feedbacks.service.js.map
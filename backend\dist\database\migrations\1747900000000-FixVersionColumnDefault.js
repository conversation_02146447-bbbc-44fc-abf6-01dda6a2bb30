"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.FixVersionColumnDefault1747900000000 = void 0;
class FixVersionColumnDefault1747900000000 {
    tables = [
        'users',
        'wallets',
        'crypto_wallets',
        'transactions',
        'crypto_transactions',
        'order_book',
        'order_book_detail',
        'payment_methods',
        'tokens',
        'token_prices',
        'token_assets',
        'token_categories',
        'agents',
        'agent_levels',
        'permissions',
        'roles',
        'banks',
        'price_alerts',
        'activity_logs',
        'system_configs',
        'user_kyc',
        'attachments',
        'ecom_orders',
        'ecom_order_details',
        'ecom_products',
        'ecom_product_categories',
        'silver_prices'
    ];
    async up(queryRunner) {
        for (const table of this.tables) {
            try {
                const tableExists = await queryRunner.query(`
          SELECT EXISTS (
            SELECT FROM information_schema.tables
            WHERE table_schema = 'public'
            AND table_name = '${table}'
          );
        `);
                if (!tableExists[0].exists) {
                    continue;
                }
                const columnExists = await queryRunner.query(`
          SELECT EXISTS (
            SELECT FROM information_schema.columns
            WHERE table_schema = 'public'
            AND table_name = '${table}'
            AND column_name = 'version'
          );
        `);
                if (!columnExists[0].exists) {
                    await queryRunner.query(`
            ALTER TABLE "${table}"
            ADD COLUMN "version" integer NOT NULL DEFAULT 0
          `);
                }
                else {
                    await queryRunner.query(`
            UPDATE "${table}"
            SET "version" = 0
            WHERE "version" IS NULL
          `);
                    await queryRunner.query(`
            ALTER TABLE "${table}"
            ALTER COLUMN "version" SET DEFAULT 0
          `);
                    await queryRunner.query(`
            ALTER TABLE "${table}"
            ALTER COLUMN "version" SET NOT NULL
          `);
                }
            }
            catch (error) {
                console.error(`❌ Lỗi khi xử lý bảng ${table}: ${error.message}`);
            }
        }
    }
    async down(queryRunner) {
        for (const table of this.tables) {
            try {
                const tableExists = await queryRunner.query(`
          SELECT EXISTS (
            SELECT FROM information_schema.tables
            WHERE table_schema = 'public'
            AND table_name = '${table}'
          );
        `);
                if (tableExists[0].exists) {
                    await queryRunner.query(`
            ALTER TABLE "${table}"
            ALTER COLUMN "version" DROP DEFAULT
          `);
                }
            }
            catch (error) {
                console.error(`❌ Lỗi khi rollback bảng ${table}: ${error.message}`);
            }
        }
    }
}
exports.FixVersionColumnDefault1747900000000 = FixVersionColumnDefault1747900000000;
//# sourceMappingURL=1747900000000-FixVersionColumnDefault.js.map
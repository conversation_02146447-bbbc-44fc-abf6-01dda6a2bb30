"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreateCmsPagesService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const event_emitter_1 = require("@nestjs/event-emitter");
const typeorm_transactional_1 = require("typeorm-transactional");
const base_cms_pages_service_1 = require("./base.cms-pages.service");
const cms_pages_entity_1 = require("../entity/cms-pages.entity");
const create_cms_page_dto_1 = require("../dto/create.cms-page.dto");
let CreateCmsPagesService = class CreateCmsPagesService extends base_cms_pages_service_1.BaseCmsPagesService {
    pageRepository;
    dataSource;
    eventEmitter;
    constructor(pageRepository, dataSource, eventEmitter) {
        super(pageRepository, dataSource, eventEmitter);
        this.pageRepository = pageRepository;
        this.dataSource = dataSource;
        this.eventEmitter = eventEmitter;
    }
    async create(createDto, userId) {
        try {
            this.logger.debug(`Đang tạo trang CMS: ${JSON.stringify(createDto)}`);
            if (await this.isSlugExists(createDto.slug)) {
                throw new common_1.BadRequestException(`Slug "${createDto.slug}" đã tồn tại`);
            }
            if (createDto.template && !this.isValidTemplate(createDto.template)) {
                throw new common_1.BadRequestException(`Template "${createDto.template}" không hợp lệ`);
            }
            const page = this.pageRepository.create();
            page.title = createDto.title;
            page.slug = createDto.slug;
            page.content = createDto.content;
            page.template = createDto.template || null;
            page.status = createDto.status || cms_pages_entity_1.CmsPageStatus.PUBLISHED;
            page.metaTitle = createDto.metaTitle || null;
            page.metaDescription = createDto.metaDescription || null;
            page.metaKeywords = createDto.metaKeywords || null;
            page.createdBy = userId;
            page.updatedBy = userId;
            const savedPage = await this.pageRepository.save(page);
            const pageDto = this.toDto(savedPage);
            if (!pageDto) {
                throw new common_1.InternalServerErrorException('Không thể chuyển đổi entity thành DTO');
            }
            this.eventEmitter.emit(this.EVENT_PAGE_CREATED, {
                pageId: pageDto.id,
                userId,
                newData: pageDto,
            });
            return pageDto;
        }
        catch (error) {
            this.logger.error(`Lỗi khi tạo trang CMS: ${error.message}`, error.stack);
            if (error instanceof common_1.BadRequestException) {
                throw error;
            }
            throw new common_1.InternalServerErrorException(`Không thể tạo trang CMS: ${error.message}`);
        }
    }
    async bulkCreate(createDtos, userId) {
        try {
            this.logger.debug(`Đang tạo ${createDtos.length} trang CMS`);
            const pages = [];
            for (const createDto of createDtos) {
                const page = await this.create(createDto, userId);
                pages.push(page);
            }
            return pages;
        }
        catch (error) {
            this.logger.error(`Lỗi khi tạo nhiều trang CMS: ${error.message}`, error.stack);
            if (error instanceof common_1.BadRequestException) {
                throw error;
            }
            throw new common_1.InternalServerErrorException(`Không thể tạo nhiều trang CMS: ${error.message}`);
        }
    }
    async createFromTemplate(templateName, title, userId) {
        try {
            this.logger.debug(`Đang tạo trang từ template: ${templateName}`);
            if (!this.isValidTemplate(templateName)) {
                throw new common_1.BadRequestException(`Template "${templateName}" không hợp lệ`);
            }
            const slug = await this.generateUniqueSlug(title);
            const content = this.getTemplateContent(templateName, title);
            const createDto = {
                title,
                slug,
                content,
                template: templateName,
                status: cms_pages_entity_1.CmsPageStatus.DRAFT,
                metaTitle: title,
                metaDescription: `Trang ${title}`,
            };
            return this.create(createDto, userId);
        }
        catch (error) {
            this.logger.error(`Lỗi khi tạo trang từ template: ${error.message}`, error.stack);
            if (error instanceof common_1.BadRequestException) {
                throw error;
            }
            throw new common_1.InternalServerErrorException(`Không thể tạo trang từ template: ${error.message}`);
        }
    }
    async duplicate(sourceId, newTitle, userId) {
        try {
            this.logger.debug(`Đang sao chép trang với ID: ${sourceId}`);
            const sourcePage = await this.findById(sourceId);
            if (!sourcePage) {
                throw new common_1.BadRequestException(`Không tìm thấy trang với ID: ${sourceId}`);
            }
            const title = newTitle || `${sourcePage.title} (Bản sao)`;
            const slug = await this.generateUniqueSlug(title);
            const createDto = {
                title,
                slug,
                content: sourcePage.content,
                template: sourcePage.template || undefined,
                status: cms_pages_entity_1.CmsPageStatus.DRAFT,
                metaTitle: sourcePage.metaTitle ? `${sourcePage.metaTitle} (Bản sao)` : undefined,
                metaDescription: sourcePage.metaDescription || undefined,
                metaKeywords: sourcePage.metaKeywords || undefined,
            };
            return this.create(createDto, userId);
        }
        catch (error) {
            this.logger.error(`Lỗi khi sao chép trang: ${error.message}`, error.stack);
            if (error instanceof common_1.BadRequestException) {
                throw error;
            }
            throw new common_1.InternalServerErrorException(`Không thể sao chép trang: ${error.message}`);
        }
    }
    async importPages(pagesData, userId) {
        try {
            this.logger.debug(`Đang import ${pagesData.length} trang`);
            const importedPages = [];
            for (const data of pagesData) {
                try {
                    let slug = data.slug || this.generateSlugFromTitle(data.title);
                    if (await this.isSlugExists(slug)) {
                        slug = await this.generateUniqueSlug(data.title);
                    }
                    const createDto = {
                        title: data.title,
                        slug,
                        content: data.content || '<p>Nội dung đang được cập nhật...</p>',
                        template: data.template && this.isValidTemplate(data.template) ? data.template : undefined,
                        status: data.status || cms_pages_entity_1.CmsPageStatus.DRAFT,
                        metaTitle: data.metaTitle,
                        metaDescription: data.metaDescription,
                        metaKeywords: data.metaKeywords,
                    };
                    const page = await this.create(createDto, userId);
                    importedPages.push(page);
                }
                catch (error) {
                    this.logger.warn(`Không thể import trang: ${error.message}`, data);
                }
            }
            return importedPages;
        }
        catch (error) {
            this.logger.error(`Lỗi khi import trang: ${error.message}`, error.stack);
            throw new common_1.InternalServerErrorException(`Không thể import trang: ${error.message}`);
        }
    }
    getTemplateContent(templateName, title) {
        const templates = {
            'default': `<h1>${title}</h1><p>Nội dung trang đang được cập nhật...</p>`,
            'full-width': `<div class="full-width"><h1>${title}</h1><p>Nội dung trang full width...</p></div>`,
            'sidebar-left': `<div class="sidebar-left"><h1>${title}</h1><p>Nội dung trang với sidebar bên trái...</p></div>`,
            'sidebar-right': `<div class="sidebar-right"><h1>${title}</h1><p>Nội dung trang với sidebar bên phải...</p></div>`,
            'landing-page': `<div class="landing-page"><h1>${title}</h1><p>Trang landing page...</p></div>`,
            'contact': `<h1>${title}</h1><div class="contact-form"><p>Thông tin liên hệ...</p></div>`,
            'about': `<h1>${title}</h1><div class="about-content"><p>Thông tin giới thiệu...</p></div>`,
            'privacy-policy': `<h1>${title}</h1><div class="policy-content"><p>Nội dung chính sách...</p></div>`,
            'terms-of-service': `<h1>${title}</h1><div class="terms-content"><p>Điều khoản dịch vụ...</p></div>`,
        };
        return templates[templateName] || templates['default'];
    }
};
exports.CreateCmsPagesService = CreateCmsPagesService;
__decorate([
    (0, typeorm_transactional_1.Transactional)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [create_cms_page_dto_1.CreateCmsPageDto, String]),
    __metadata("design:returntype", Promise)
], CreateCmsPagesService.prototype, "create", null);
__decorate([
    (0, typeorm_transactional_1.Transactional)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Array, String]),
    __metadata("design:returntype", Promise)
], CreateCmsPagesService.prototype, "bulkCreate", null);
__decorate([
    (0, typeorm_transactional_1.Transactional)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String, String]),
    __metadata("design:returntype", Promise)
], CreateCmsPagesService.prototype, "createFromTemplate", null);
__decorate([
    (0, typeorm_transactional_1.Transactional)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object, String]),
    __metadata("design:returntype", Promise)
], CreateCmsPagesService.prototype, "duplicate", null);
__decorate([
    (0, typeorm_transactional_1.Transactional)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Array, String]),
    __metadata("design:returntype", Promise)
], CreateCmsPagesService.prototype, "importPages", null);
exports.CreateCmsPagesService = CreateCmsPagesService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(cms_pages_entity_1.CmsPages)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        typeorm_2.DataSource,
        event_emitter_1.EventEmitter2])
], CreateCmsPagesService);
//# sourceMappingURL=create.cms-pages.service.js.map
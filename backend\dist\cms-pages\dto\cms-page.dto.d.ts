import { UserDto } from '../../users/dto/user.dto';
import { CmsPageStatus } from '../entity/cms-pages.entity';
export declare class CmsPageDto {
    id: string;
    businessCode: string;
    title: string;
    slug: string;
    content: string;
    template?: string | null;
    status: CmsPageStatus;
    metaTitle?: string | null;
    metaDescription?: string | null;
    metaKeywords?: string | null;
    createdAt: Date;
    updatedAt: Date;
    createdBy?: string;
    updatedBy?: string;
    deletedBy?: string;
    isDeleted: boolean;
    deletedAt?: Date;
    creator?: UserDto;
    updater?: UserDto;
    deleter?: UserDto;
    seoTitle?: string;
    seoDescription?: string;
    seoKeywordsArray?: string[];
    usedTemplate?: string;
    fullUrl?: string;
    excerpt?: string;
    wordCount?: number;
    estimatedReadingTime?: number;
}

"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.VnpayTransaction = exports.VnpayTransactionType = exports.VnpayTransactionStatus = void 0;
const openapi = require("@nestjs/swagger");
const typeorm_1 = require("typeorm");
var VnpayTransactionStatus;
(function (VnpayTransactionStatus) {
    VnpayTransactionStatus["PENDING"] = "PENDING";
    VnpayTransactionStatus["SUCCESS"] = "SUCCESS";
    VnpayTransactionStatus["FAILED"] = "FAILED";
    VnpayTransactionStatus["CANCELLED"] = "CANCELLED";
    VnpayTransactionStatus["EXPIRED"] = "EXPIRED";
})(VnpayTransactionStatus || (exports.VnpayTransactionStatus = VnpayTransactionStatus = {}));
var VnpayTransactionType;
(function (VnpayTransactionType) {
    VnpayTransactionType["PAYMENT"] = "PAYMENT";
    VnpayTransactionType["REFUND"] = "REFUND";
    VnpayTransactionType["QUERY"] = "QUERY";
})(VnpayTransactionType || (exports.VnpayTransactionType = VnpayTransactionType = {}));
let VnpayTransaction = class VnpayTransaction {
    id;
    merchantTxnRef;
    vnpayTxnRef;
    vnpayTxnNo;
    type;
    status;
    amount;
    currency;
    orderInfo;
    bankCode;
    cardType;
    vnpayResponseCode;
    vnpayTransactionStatus;
    vnpayPayDate;
    clientIp;
    locale;
    vnpayRequest;
    vnpayResponse;
    returnCallbackData;
    ipnCallbackData;
    externalRef;
    externalMetadata;
    errorMessage;
    retryCount;
    expiresAt;
    processedAt;
    createdAt;
    updatedAt;
    isSuccess() {
        return this.status === VnpayTransactionStatus.SUCCESS;
    }
    isPending() {
        return this.status === VnpayTransactionStatus.PENDING;
    }
    isFailed() {
        return this.status === VnpayTransactionStatus.FAILED;
    }
    isExpired() {
        return this.expiresAt && new Date() > this.expiresAt;
    }
    getFormattedAmount() {
        return this.amount.toLocaleString('vi-VN') + ' VND';
    }
    getVnpayAmount() {
        return this.amount * 100;
    }
    setFromVnpayAmount(vnpayAmount) {
        this.amount = vnpayAmount / 100;
    }
    static _OPENAPI_METADATA_FACTORY() {
        return { id: { required: true, type: () => String }, merchantTxnRef: { required: true, type: () => String }, vnpayTxnRef: { required: true, type: () => String }, vnpayTxnNo: { required: true, type: () => String }, type: { required: true, enum: require("./vnpay-transaction.entity").VnpayTransactionType }, status: { required: true, enum: require("./vnpay-transaction.entity").VnpayTransactionStatus }, amount: { required: true, type: () => Number }, currency: { required: true, type: () => String }, orderInfo: { required: true, type: () => String }, bankCode: { required: true, type: () => String }, cardType: { required: true, type: () => String }, vnpayResponseCode: { required: true, type: () => String }, vnpayTransactionStatus: { required: true, type: () => String }, vnpayPayDate: { required: true, type: () => String }, clientIp: { required: true, type: () => String }, locale: { required: true, type: () => String }, vnpayRequest: { required: true, type: () => String }, vnpayResponse: { required: true, type: () => String }, returnCallbackData: { required: true, type: () => String }, ipnCallbackData: { required: true, type: () => String }, externalRef: { required: true, type: () => String }, externalMetadata: { required: true, type: () => String }, errorMessage: { required: true, type: () => String }, retryCount: { required: true, type: () => Number }, expiresAt: { required: true, type: () => Date }, processedAt: { required: true, type: () => Date }, createdAt: { required: true, type: () => Date }, updatedAt: { required: true, type: () => Date } };
    }
};
exports.VnpayTransaction = VnpayTransaction;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)('uuid'),
    __metadata("design:type", String)
], VnpayTransaction.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'merchant_txn_ref', unique: true }),
    __metadata("design:type", String)
], VnpayTransaction.prototype, "merchantTxnRef", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'vnpay_txn_ref', unique: true, nullable: true }),
    __metadata("design:type", String)
], VnpayTransaction.prototype, "vnpayTxnRef", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'vnpay_txn_no', nullable: true }),
    __metadata("design:type", String)
], VnpayTransaction.prototype, "vnpayTxnNo", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'enum',
        enum: VnpayTransactionType,
        default: VnpayTransactionType.PAYMENT,
    }),
    __metadata("design:type", String)
], VnpayTransaction.prototype, "type", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'enum',
        enum: VnpayTransactionStatus,
        default: VnpayTransactionStatus.PENDING,
    }),
    __metadata("design:type", String)
], VnpayTransaction.prototype, "status", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'bigint' }),
    __metadata("design:type", Number)
], VnpayTransaction.prototype, "amount", void 0);
__decorate([
    (0, typeorm_1.Column)({ default: 'VND' }),
    __metadata("design:type", String)
], VnpayTransaction.prototype, "currency", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'order_info' }),
    __metadata("design:type", String)
], VnpayTransaction.prototype, "orderInfo", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'bank_code', nullable: true }),
    __metadata("design:type", String)
], VnpayTransaction.prototype, "bankCode", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'card_type', nullable: true }),
    __metadata("design:type", String)
], VnpayTransaction.prototype, "cardType", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'vnpay_response_code', nullable: true }),
    __metadata("design:type", String)
], VnpayTransaction.prototype, "vnpayResponseCode", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'vnpay_transaction_status', nullable: true }),
    __metadata("design:type", String)
], VnpayTransaction.prototype, "vnpayTransactionStatus", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'vnpay_pay_date', nullable: true }),
    __metadata("design:type", String)
], VnpayTransaction.prototype, "vnpayPayDate", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'client_ip' }),
    __metadata("design:type", String)
], VnpayTransaction.prototype, "clientIp", void 0);
__decorate([
    (0, typeorm_1.Column)({ default: 'vn' }),
    __metadata("design:type", String)
], VnpayTransaction.prototype, "locale", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'vnpay_request', type: 'text', nullable: true }),
    __metadata("design:type", String)
], VnpayTransaction.prototype, "vnpayRequest", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'vnpay_response', type: 'text', nullable: true }),
    __metadata("design:type", String)
], VnpayTransaction.prototype, "vnpayResponse", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'return_callback_data', type: 'text', nullable: true }),
    __metadata("design:type", String)
], VnpayTransaction.prototype, "returnCallbackData", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'ipn_callback_data', type: 'text', nullable: true }),
    __metadata("design:type", String)
], VnpayTransaction.prototype, "ipnCallbackData", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'external_ref', nullable: true }),
    __metadata("design:type", String)
], VnpayTransaction.prototype, "externalRef", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'external_metadata', type: 'text', nullable: true }),
    __metadata("design:type", String)
], VnpayTransaction.prototype, "externalMetadata", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'error_message', nullable: true }),
    __metadata("design:type", String)
], VnpayTransaction.prototype, "errorMessage", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'retry_count', default: 0 }),
    __metadata("design:type", Number)
], VnpayTransaction.prototype, "retryCount", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'expires_at', nullable: true }),
    __metadata("design:type", Date)
], VnpayTransaction.prototype, "expiresAt", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'processed_at', nullable: true }),
    __metadata("design:type", Date)
], VnpayTransaction.prototype, "processedAt", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)({ name: 'created_at' }),
    __metadata("design:type", Date)
], VnpayTransaction.prototype, "createdAt", void 0);
__decorate([
    (0, typeorm_1.UpdateDateColumn)({ name: 'updated_at' }),
    __metadata("design:type", Date)
], VnpayTransaction.prototype, "updatedAt", void 0);
exports.VnpayTransaction = VnpayTransaction = __decorate([
    (0, typeorm_1.Entity)('vnpay_transactions'),
    (0, typeorm_1.Index)(['vnpayTxnRef'], { unique: true }),
    (0, typeorm_1.Index)(['merchantTxnRef'], { unique: true }),
    (0, typeorm_1.Index)(['status']),
    (0, typeorm_1.Index)(['createdAt'])
], VnpayTransaction);
//# sourceMappingURL=vnpay-transaction.entity.js.map
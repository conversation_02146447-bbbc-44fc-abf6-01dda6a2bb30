import { Repository, DataSource } from 'typeorm';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { BaseCmsPostsService } from './base.cms-posts.service';
import { SlugCmsPostsService } from './slug.cms-posts.service';
import { CmsPosts } from '../entity/cms-posts.entity';
import { CreateCmsPostDto } from '../dto/create.cms-post.dto';
import { CmsPostDto } from '../dto/cms-post.dto';
import { CmsCategories } from '../../cms-categories/entity/cms-categories.entity';
import { User } from '../../users/entities/user.entity';
export declare class CreateCmsPostsService extends BaseCmsPostsService {
    protected readonly postRepository: Repository<CmsPosts>;
    private readonly categoryRepository;
    private readonly userRepository;
    private readonly slugService;
    protected readonly dataSource: DataSource;
    protected readonly eventEmitter: EventEmitter2;
    constructor(postRepository: Repository<CmsPosts>, categoryRepository: Repository<CmsCategories>, userRepository: Repository<User>, slugService: SlugCmsPostsService, dataSource: DataSource, eventEmitter: EventEmitter2);
    create(createDto: CreateCmsPostDto, userId: string): Promise<CmsPostDto>;
    bulkCreate(createDtos: CreateCmsPostDto[], userId: string): Promise<CmsPostDto[]>;
    duplicate(id: string, userId: string): Promise<CmsPostDto>;
    createFromTemplate(templateId: string, createDto: CreateCmsPostDto, userId: string): Promise<CmsPostDto>;
}

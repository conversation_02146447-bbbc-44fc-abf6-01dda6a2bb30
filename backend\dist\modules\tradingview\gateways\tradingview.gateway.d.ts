import { Socket } from 'socket.io';
import { TradingViewWebsocketService } from '../services/tradingview-websocket.service';
import { RateLimiterService } from '../services/rate-limiter.service';
import { SubscriptionRequest, SubscriptionResponse } from '../models/tradingview-data.model';
export declare class TradingViewGateway {
    private readonly tradingViewWebsocketService;
    private readonly rateLimiterService;
    private readonly logger;
    constructor(tradingViewWebsocketService: TradingViewWebsocketService, rateLimiterService: RateLimiterService);
    handleConnection(client: Socket): void;
    handleDisconnect(client: Socket): void;
    handleSubscribe(client: Socket, data: SubscriptionRequest): Promise<SubscriptionResponse>;
    handleUnsubscribe(client: Socket, data: {
        symbol: string;
    }): SubscriptionResponse;
}

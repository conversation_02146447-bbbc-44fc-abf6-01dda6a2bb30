import { OnModuleInit } from '@nestjs/common';
import { Repository } from 'typeorm';
import { SilverPrice } from '../entities/silver-price.entity';
import { CurrentPrice } from '../models/tradingview-data.model';
import { TradingViewWebsocketService } from './tradingview-websocket.service';
import { CacheService } from './cache.service';
export declare class MockTradingViewService implements OnModuleInit {
    private readonly tradingViewWebsocketService;
    private readonly cacheService;
    private readonly silverPriceRepository;
    private readonly logger;
    private dataGenerationInterval;
    private readonly defaultSymbol;
    private lastPrice;
    private readonly volatility;
    private readonly updateInterval;
    constructor(tradingViewWebsocketService: TradingViewWebsocketService, cacheService: CacheService, silverPriceRepository: Repository<SilverPrice>);
    onModuleInit(): Promise<void>;
    private startDataGeneration;
    private generateAndBroadcastPrice;
    private generateMockPriceData;
    private broadcastPriceUpdate;
    private savePriceData;
    fetchLatestPrice(): Promise<CurrentPrice>;
}

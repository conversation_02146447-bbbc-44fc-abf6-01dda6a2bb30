import { ReadCmsTagsService } from '../services/read.cms-tags.service';
import { CmsTagDto } from '../dto/cms-tag.dto';
import { CustomPaginationQueryDto } from '../../common/dto/custom-pagination-query.dto';
import { PaginationResponseDto } from '../../common/dto/pagination-response.dto';
export declare class ReadCmsTagsController {
    private readonly cmsTagsService;
    constructor(cmsTagsService: ReadCmsTagsService);
    findAll(paginationQuery: CustomPaginationQueryDto): Promise<PaginationResponseDto<CmsTagDto>>;
    getStatistics(): Promise<{
        total: number;
        mostUsed: Array<{
            name: string;
            slug: string;
            postsCount: number;
        }>;
    }>;
    getPopularTags(limit?: number): Promise<CmsTagDto[]>;
    count(filter?: string): Promise<number>;
    search(keyword: string, paginationQuery: CustomPaginationQueryDto): Promise<PaginationResponseDto<CmsTagDto>>;
    findDeleted(paginationQuery: CustomPaginationQueryDto): Promise<PaginationResponseDto<CmsTagDto>>;
    findByNames(names: string): Promise<CmsTagDto[]>;
    findOne(id: string, relations?: string): Promise<CmsTagDto | null>;
    findBySlug(slug: string): Promise<CmsTagDto | null>;
}

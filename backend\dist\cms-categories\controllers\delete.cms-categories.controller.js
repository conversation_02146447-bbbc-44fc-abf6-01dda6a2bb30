"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.DeleteCmsCategoriesController = void 0;
const openapi = require("@nestjs/swagger");
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const jwt_auth_guard_1 = require("../../common/guards/jwt-auth.guard");
const delete_cms_categories_service_1 = require("../services/delete.cms-categories.service");
const api_response_dto_1 = require("../../common/dto/api-response.dto");
const get_user_decorator_1 = require("../../common/decorators/get-user.decorator");
const roles_decorator_1 = require("../../common/decorators/roles.decorator");
const permissions_decorator_1 = require("../../common/decorators/permissions.decorator");
let DeleteCmsCategoriesController = class DeleteCmsCategoriesController {
    cmsCategoriesService;
    constructor(cmsCategoriesService) {
        this.cmsCategoriesService = cmsCategoriesService;
    }
    async softDelete(id, userId) {
        const result = await this.cmsCategoriesService.softDelete(id, userId);
        if (!result) {
            throw new common_1.NotFoundException(`Không tìm thấy chuyên mục CMS với ID: ${id}`);
        }
        return result;
    }
    async restore(id, userId) {
        const result = await this.cmsCategoriesService.restore(id, userId);
        if (!result) {
            throw new common_1.NotFoundException(`Không tìm thấy chuyên mục CMS đã xóa với ID: ${id}`);
        }
        return result;
    }
    async remove(id) {
        const result = await this.cmsCategoriesService.remove(id);
        if (result.affected === null) {
            throw new common_1.NotFoundException(`Không tìm thấy chuyên mục CMS với ID: ${id}`);
        }
        return { affected: result.affected };
    }
};
exports.DeleteCmsCategoriesController = DeleteCmsCategoriesController;
__decorate([
    (0, common_1.Delete)(':id'),
    (0, roles_decorator_1.Roles)('ADMIN'),
    (0, permissions_decorator_1.Permissions)('cms-category:delete'),
    (0, swagger_1.ApiOperation)({ summary: 'Xóa mềm chuyên mục CMS' }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Chuyên mục CMS đã được xóa thành công.',
        type: api_response_dto_1.ApiResponseDto,
        schema: {
            properties: {
                data: { $ref: '#/components/schemas/CmsCategoryDto' },
            },
        },
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.NOT_FOUND,
        description: 'Không tìm thấy chuyên mục CMS.',
        type: api_response_dto_1.ApiResponseDto,
        schema: { properties: { data: { type: 'object', nullable: true } } },
    }),
    (0, swagger_1.ApiParam)({ name: 'id', type: String, description: 'ID của chuyên mục CMS' }),
    openapi.ApiResponse({ status: 200, type: Object }),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __param(1, (0, get_user_decorator_1.GetUser)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String]),
    __metadata("design:returntype", Promise)
], DeleteCmsCategoriesController.prototype, "softDelete", null);
__decorate([
    (0, common_1.Patch)(':id/restore'),
    (0, roles_decorator_1.Roles)('ADMIN'),
    (0, permissions_decorator_1.Permissions)('cms-category:delete'),
    (0, swagger_1.ApiOperation)({ summary: 'Khôi phục chuyên mục CMS đã xóa' }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Chuyên mục CMS đã được khôi phục thành công.',
        type: api_response_dto_1.ApiResponseDto,
        schema: {
            properties: {
                data: { $ref: '#/components/schemas/CmsCategoryDto' },
            },
        },
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.NOT_FOUND,
        description: 'Không tìm thấy chuyên mục CMS đã xóa.',
        type: api_response_dto_1.ApiResponseDto,
        schema: { properties: { data: { type: 'object', nullable: true } } },
    }),
    (0, swagger_1.ApiParam)({ name: 'id', type: String, description: 'ID của chuyên mục CMS đã xóa' }),
    openapi.ApiResponse({ status: 200, type: Object }),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __param(1, (0, get_user_decorator_1.GetUser)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String]),
    __metadata("design:returntype", Promise)
], DeleteCmsCategoriesController.prototype, "restore", null);
__decorate([
    (0, common_1.Delete)(':id/force'),
    (0, roles_decorator_1.Roles)('ADMIN'),
    (0, permissions_decorator_1.Permissions)('cms-category:delete'),
    (0, swagger_1.ApiOperation)({ summary: 'Xóa vĩnh viễn chuyên mục CMS' }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Chuyên mục CMS đã được xóa vĩnh viễn thành công.',
        type: api_response_dto_1.ApiResponseDto,
        schema: {
            properties: {
                data: {
                    type: 'object',
                    properties: {
                        affected: { type: 'number' },
                    },
                },
            },
        },
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.NOT_FOUND,
        description: 'Không tìm thấy chuyên mục CMS.',
        type: api_response_dto_1.ApiResponseDto,
        schema: { properties: { data: { type: 'object', nullable: true } } },
    }),
    (0, swagger_1.ApiParam)({ name: 'id', type: String, description: 'ID của chuyên mục CMS' }),
    openapi.ApiResponse({ status: 200 }),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], DeleteCmsCategoriesController.prototype, "remove", null);
exports.DeleteCmsCategoriesController = DeleteCmsCategoriesController = __decorate([
    (0, swagger_1.ApiTags)('cms-categories'),
    (0, swagger_1.ApiBearerAuth)(),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, common_1.Controller)('cms/categories'),
    __metadata("design:paramtypes", [delete_cms_categories_service_1.DeleteCmsCategoriesService])
], DeleteCmsCategoriesController);
//# sourceMappingURL=delete.cms-categories.controller.js.map
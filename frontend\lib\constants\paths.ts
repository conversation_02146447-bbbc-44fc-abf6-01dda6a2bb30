/**
 * Path constants for consistent usage across the application
 */

// <PERSON><PERSON><PERSON> đường dẫn không yêu cầu xác thực
export const PUBLIC_PATHS = [
  '/login',
  '/register', 
  '/forgot-password',
  '/reset-password'
] as const;

// <PERSON><PERSON><PERSON> đường dẫn không nên tự động chuyển hướng ngay cả khi đã xác thực
export const EXCLUDE_REDIRECT_PATHS = [
  '/login/callback'
] as const;

// Các đường dẫn dành riêng cho admin
export const ADMIN_PATHS = [
  '/admin'
] as const;

// Helper functions
export const isPublicPath = (pathname: string): boolean => {
  return PUBLIC_PATHS.some(path => 
    pathname === path || pathname.startsWith(`${path}/`)
  );
};

export const isAdminPath = (pathname: string): boolean => {
  return ADMIN_PATHS.some(path =>
    pathname === path || pathname.startsWith(`${path}/`)
  );
};

export const isExcludeRedirectPath = (pathname: string): boolean => {
  return EXCLUDE_REDIRECT_PATHS.some(path =>
    pathname === path || pathname.startsWith(`${path}/`)
  );
};

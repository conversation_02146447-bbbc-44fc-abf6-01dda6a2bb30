"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UpdateCmsMenusService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const event_emitter_1 = require("@nestjs/event-emitter");
const typeorm_transactional_1 = require("typeorm-transactional");
const base_cms_menus_service_1 = require("./base.cms-menus.service");
const cms_menus_entity_1 = require("../entity/cms-menus.entity");
const update_cms_menu_dto_1 = require("../dto/update.cms-menu.dto");
let UpdateCmsMenusService = class UpdateCmsMenusService extends base_cms_menus_service_1.BaseCmsMenusService {
    menuRepository;
    dataSource;
    eventEmitter;
    constructor(menuRepository, dataSource, eventEmitter) {
        super(menuRepository, dataSource, eventEmitter);
        this.menuRepository = menuRepository;
        this.dataSource = dataSource;
        this.eventEmitter = eventEmitter;
    }
    async update(id, updateDto, userId) {
        try {
            this.logger.debug(`Đang cập nhật menu CMS với ID: ${id}, dữ liệu: ${JSON.stringify(updateDto)}`);
            const menu = await this.findById(id, []);
            if (!menu) {
                throw new common_1.NotFoundException(`Không tìm thấy menu với ID: ${id}`);
            }
            const oldData = this.toDto(menu);
            if (updateDto.name && updateDto.name !== menu.name) {
                const existingMenuByName = await this.isNameUnique(updateDto.name, menu.postType, id);
                if (!existingMenuByName) {
                    throw new common_1.ConflictException(`Tên menu "${updateDto.name}" đã tồn tại cho loại post "${menu.postType}"`);
                }
            }
            if (updateDto.slug && updateDto.slug !== menu.slug) {
                const existingMenuBySlug = await this.isSlugUnique(updateDto.slug, menu.postType, id);
                if (!existingMenuBySlug) {
                    throw new common_1.ConflictException(`Slug "${updateDto.slug}" đã tồn tại cho loại post "${menu.postType}"`);
                }
            }
            if (updateDto.name !== undefined)
                menu.name = updateDto.name;
            if (updateDto.slug !== undefined)
                menu.slug = updateDto.slug;
            if (updateDto.description !== undefined)
                menu.description = updateDto.description;
            if (updateDto.postType !== undefined)
                menu.postType = updateDto.postType;
            if (updateDto.imageUrl !== undefined)
                menu.imageUrl = updateDto.imageUrl;
            if (updateDto.metaTitle !== undefined)
                menu.metaTitle = updateDto.metaTitle;
            if (updateDto.metaDescription !== undefined)
                menu.metaDescription = updateDto.metaDescription;
            if (updateDto.metaKeywords !== undefined)
                menu.metaKeywords = updateDto.metaKeywords;
            if (updateDto.status !== undefined)
                menu.status = updateDto.status;
            if (updateDto.parentId !== undefined) {
                if (updateDto.parentId === null) {
                    menu.parent = null;
                }
                else {
                    if (updateDto.parentId === id) {
                        throw new common_1.BadRequestException('Menu không thể đặt chính nó làm menu cha');
                    }
                    const isCircular = await this.checkCircularReference(id, updateDto.parentId);
                    if (isCircular) {
                        throw new common_1.BadRequestException('Không thể đặt menu cha vì sẽ tạo vòng lặp');
                    }
                    const parent = await this.findById(updateDto.parentId);
                    if (parent) {
                        menu.parent = parent;
                    }
                }
            }
            menu.updatedBy = userId;
            const updatedMenu = await this.menuRepository.save(menu);
            const menuDto = this.toDto(updatedMenu);
            if (!menuDto) {
                throw new common_1.InternalServerErrorException('Không thể chuyển đổi entity thành DTO');
            }
            this.eventEmitter.emit(this.EVENT_MENU_UPDATED, {
                menuId: menuDto.id,
                userId,
                oldData,
                newData: menuDto,
            });
            this.logger.debug(`Đã cập nhật menu CMS thành công với ID: ${menuDto.id}`);
            return menuDto;
        }
        catch (error) {
            this.logger.error(`Lỗi khi cập nhật menu CMS: ${error.message}`, error.stack);
            if (error instanceof common_1.NotFoundException || error instanceof common_1.ConflictException || error instanceof common_1.BadRequestException) {
                throw error;
            }
            throw new common_1.InternalServerErrorException(`Không thể cập nhật menu CMS: ${error.message}`);
        }
    }
    async updateStatus(id, status, userId) {
        try {
            this.logger.debug(`Đang cập nhật trạng thái menu CMS với ID: ${id} thành: ${status}`);
            const updateDto = { status };
            return this.update(id, updateDto, userId);
        }
        catch (error) {
            this.logger.error(`Lỗi khi cập nhật trạng thái menu CMS: ${error.message}`, error.stack);
            throw new common_1.InternalServerErrorException(`Không thể cập nhật trạng thái menu CMS: ${error.message}`);
        }
    }
    async updateSlugFromName(id, userId) {
        try {
            this.logger.debug(`Đang cập nhật slug từ tên cho menu CMS với ID: ${id}`);
            const menu = await this.findById(id, []);
            if (!menu) {
                throw new common_1.NotFoundException(`Không tìm thấy menu với ID: ${id}`);
            }
            let newSlug = this.generateSlugFromName(menu.name);
            let counter = 1;
            while (!(await this.isSlugUnique(newSlug, menu.postType, id))) {
                newSlug = `${this.generateSlugFromName(menu.name)}-${counter}`;
                counter++;
            }
            const updateDto = {
                slug: newSlug,
            };
            return this.update(id, updateDto, userId);
        }
        catch (error) {
            this.logger.error(`Lỗi khi cập nhật slug menu CMS: ${error.message}`, error.stack);
            throw new common_1.InternalServerErrorException(`Không thể cập nhật slug menu CMS: ${error.message}`);
        }
    }
    async checkCircularReference(menuId, parentId) {
        let currentParentId = parentId;
        const visited = new Set();
        while (currentParentId) {
            if (visited.has(currentParentId)) {
                return true;
            }
            if (currentParentId === menuId) {
                return true;
            }
            visited.add(currentParentId);
            const parent = await this.menuRepository.findOne({
                where: { id: currentParentId, isDeleted: false },
                relations: ['parent'],
            });
            currentParentId = parent?.parent?.id || null;
        }
        return false;
    }
};
exports.UpdateCmsMenusService = UpdateCmsMenusService;
__decorate([
    (0, typeorm_transactional_1.Transactional)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, update_cms_menu_dto_1.UpdateCmsMenuDto, String]),
    __metadata("design:returntype", Promise)
], UpdateCmsMenusService.prototype, "update", null);
__decorate([
    (0, typeorm_transactional_1.Transactional)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String, String]),
    __metadata("design:returntype", Promise)
], UpdateCmsMenusService.prototype, "updateStatus", null);
__decorate([
    (0, typeorm_transactional_1.Transactional)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String]),
    __metadata("design:returntype", Promise)
], UpdateCmsMenusService.prototype, "updateSlugFromName", null);
exports.UpdateCmsMenusService = UpdateCmsMenusService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(cms_menus_entity_1.CmsMenus)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        typeorm_2.DataSource,
        event_emitter_1.EventEmitter2])
], UpdateCmsMenusService);
//# sourceMappingURL=update.cms-menus.service.js.map
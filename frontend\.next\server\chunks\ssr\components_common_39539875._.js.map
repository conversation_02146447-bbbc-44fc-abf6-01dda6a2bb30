{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/bloomgoo/gold-exchange-platform/frontend/components/common/SilverPriceIndicator.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { usePolygonForex } from '@/services/websocket/polygon-forex-socket.service';\r\nimport { Badge } from '@/components/ui/badge';\r\n\r\nexport function SilverPriceIndicator() {\r\n  const {\r\n    currentQuote,\r\n    isMockData\r\n  } = usePolygonForex();\r\n\r\n  // Format giá\r\n  const formatPrice = (price?: number) => {\r\n    if (price === undefined || price === null) return '---.----';\r\n    return price.toFixed(4);\r\n  };\r\n\r\n  // Format giá VND\r\n  const formatVND = (price?: number) => {\r\n    if (price === undefined || price === null) return '---,---';\r\n    return price.toLocaleString('vi-VN');\r\n  };\r\n\r\n  // Xác định màu sắc cho chênh lệch giá\r\n  const getSpreadColor = (spread?: number) => {\r\n    if (spread === undefined || spread === null) return 'bg-gray-500';\r\n    if (spread > 0.1) return 'bg-red-500';\r\n    if (spread < 0.05) return 'bg-green-500';\r\n    return 'bg-yellow-500';\r\n  };\r\n\r\n  return (\r\n    <div className=\"flex items-center gap-2\">\r\n      <Badge variant=\"outline\" className=\"flex items-center gap-1 py-1\">\r\n        <span className=\"text-xs font-medium\">AXGUSD:</span>\r\n        <span className=\"text-xs font-bold\">{formatPrice(currentQuote?.bidPrice)}</span>\r\n        {currentQuote?.spread !== undefined && (\r\n          <span className={`inline-block w-2 h-2 rounded-full ${getSpreadColor(currentQuote?.spread)}`} />\r\n        )}\r\n      </Badge>\r\n      \r\n      {isMockData && (\r\n        <Badge variant=\"outline\" className=\"bg-yellow-50 text-yellow-700 border-yellow-200 text-xs\">\r\n          Mẫu\r\n        </Badge>\r\n      )}\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAKO,SAAS;IACd,MAAM,EACJ,YAAY,EACZ,UAAU,EACX,GAAG,CAAA,GAAA,8JAAA,CAAA,kBAAe,AAAD;IAElB,aAAa;IACb,MAAM,cAAc,CAAC;QACnB,IAAI,UAAU,aAAa,UAAU,MAAM,OAAO;QAClD,OAAO,MAAM,OAAO,CAAC;IACvB;IAEA,iBAAiB;IACjB,MAAM,YAAY,CAAC;QACjB,IAAI,UAAU,aAAa,UAAU,MAAM,OAAO;QAClD,OAAO,MAAM,cAAc,CAAC;IAC9B;IAEA,sCAAsC;IACtC,MAAM,iBAAiB,CAAC;QACtB,IAAI,WAAW,aAAa,WAAW,MAAM,OAAO;QACpD,IAAI,SAAS,KAAK,OAAO;QACzB,IAAI,SAAS,MAAM,OAAO;QAC1B,OAAO;IACT;IAEA,qBACE,uVAAC;QAAI,WAAU;;0BACb,uVAAC,0HAAA,CAAA,QAAK;gBAAC,SAAQ;gBAAU,WAAU;;kCACjC,uVAAC;wBAAK,WAAU;kCAAsB;;;;;;kCACtC,uVAAC;wBAAK,WAAU;kCAAqB,YAAY,cAAc;;;;;;oBAC9D,cAAc,WAAW,2BACxB,uVAAC;wBAAK,WAAW,CAAC,kCAAkC,EAAE,eAAe,cAAc,SAAS;;;;;;;;;;;;YAI/F,4BACC,uVAAC,0HAAA,CAAA,QAAK;gBAAC,SAAQ;gBAAU,WAAU;0BAAyD;;;;;;;;;;;;AAMpG", "debugId": null}}, {"offset": {"line": 94, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/bloomgoo/gold-exchange-platform/frontend/components/common/admin/ebanks/utils/bank-constants.ts"], "sourcesContent": ["/**\r\n * Bank Constants - Các hằng số và enums cho module ngân hàng\r\n */\r\n\r\n// API endpoints\r\nexport const BANK_ENDPOINTS = {\r\n  LIST: 'banks',\r\n  STATISTICS: 'banks/statistics',\r\n  CREATE: 'banks',\r\n  UPDATE: (id: string) => `banks/${id}`,\r\n  DELETE: (id: string) => `banks/${id}/soft-delete`,\r\n  TOGGLE_STATUS: (id: string) => `banks/${id}/toggle-status`,\r\n  DUPLICATE: (id: string) => `banks/${id}/duplicate`,\r\n  BULK_DELETE: 'banks/bulk-soft-delete',\r\n  EXPORT: 'banks/export',\r\n  BULK_CREATE: 'banks/bulk',\r\n} as const;\r\n\r\n// Default pagination\r\nexport const DEFAULT_PAGINATION = {\r\n  pageIndex: 0,\r\n  pageSize: 20,\r\n} as const;\r\n\r\n// Default column visibility\r\nexport const DEFAULT_COLUMN_VISIBILITY = {\r\n  deletedAt: false,\r\n  deletedBy: false,\r\n} as const;\r\n\r\n// Status options\r\nexport const STATUS_OPTIONS = [\r\n  { value: 'all', label: 'Tất cả' },\r\n  { value: 'active', label: 'Đã kích hoạt' },\r\n  { value: 'inactive', label: 'V<PERSON> hiệu hóa' },\r\n] as const;\r\n\r\n// Form default values\r\nexport const FORM_DEFAULT_VALUES = {\r\n  brandName: '',\r\n  fullName: '',\r\n  shortName: '',\r\n  code: '',\r\n  bin: '',\r\n  logoPath: '',\r\n  icon: '',\r\n  isActive: true,\r\n} as const;\r\n\r\n// Toast messages\r\nexport const TOAST_MESSAGES = {\r\n  CREATE_SUCCESS: 'Tạo ngân hàng thành công',\r\n  UPDATE_SUCCESS: 'Cập nhật ngân hàng thành công',\r\n  DELETE_SUCCESS: (name: string) => `Đã xóa ngân hàng ${name}`,\r\n  TOGGLE_SUCCESS: (name: string, isActive: boolean) =>\r\n    `Đã ${isActive ? 'kích hoạt' : 'vô hiệu hóa'} ngân hàng ${name}`,\r\n  DUPLICATE_SUCCESS: (name: string) => `Đã nhân bản ngân hàng ${name} thành công`,\r\n  BULK_DELETE_SUCCESS: (count: number) => `Đã xóa ${count} ngân hàng thành công`,\r\n  EXPORT_SUCCESS: (format: string) => `Đã xuất dữ liệu thành công dưới dạng ${format.toUpperCase()}`,\r\n  IMPORT_SUCCESS: (count: number) => `Đã import ${count} ngân hàng thành công`,\r\n\r\n  // Error messages\r\n  CREATE_ERROR: 'Không thể tạo ngân hàng',\r\n  UPDATE_ERROR: 'Không thể cập nhật ngân hàng',\r\n  DELETE_ERROR: 'Không thể xóa ngân hàng',\r\n  TOGGLE_ERROR: 'Không thể thay đổi trạng thái ngân hàng',\r\n  DUPLICATE_ERROR: 'Không thể nhân bản ngân hàng',\r\n  BULK_DELETE_ERROR: 'Không thể xóa các ngân hàng đã chọn',\r\n  EXPORT_ERROR: (format: string) => `Không thể xuất dữ liệu dưới dạng ${format.toUpperCase()}`,\r\n  IMPORT_ERROR: 'Không thể import dữ liệu ngân hàng',\r\n  FETCH_ERROR: 'Không thể tải danh sách ngân hàng',\r\n  FETCH_DETAIL_ERROR: 'Không thể tải thông tin chi tiết ngân hàng',\r\n  FETCH_STATISTICS_ERROR: 'Không thể lấy thống kê ngân hàng',\r\n\r\n  // Auth errors\r\n  UNAUTHORIZED: 'Bạn chưa đăng nhập hoặc phiên đăng nhập đã hết hạn',\r\n  FORBIDDEN: 'Bạn không có quyền truy cập vào tài nguyên này',\r\n  NOT_FOUND: 'Không tìm thấy tài nguyên',\r\n} as const;\r\n\r\n// Loading messages\r\nexport const LOADING_MESSAGES = {\r\n  FETCHING: 'Đang tải dữ liệu...',\r\n  CREATING: 'Đang tạo ngân hàng...',\r\n  UPDATING: 'Đang cập nhật...',\r\n  DELETING: 'Đang xóa...',\r\n  EXPORTING: 'Đang xuất dữ liệu...',\r\n  IMPORTING: 'Đang import dữ liệu...',\r\n} as const;\r\n"], "names": [], "mappings": "AAAA;;CAEC,GAED,gBAAgB;;;;;;;;;;AACT,MAAM,iBAAiB;IAC5B,MAAM;IACN,YAAY;IACZ,QAAQ;IACR,QAAQ,CAAC,KAAe,CAAC,MAAM,EAAE,IAAI;IACrC,QAAQ,CAAC,KAAe,CAAC,MAAM,EAAE,GAAG,YAAY,CAAC;IACjD,eAAe,CAAC,KAAe,CAAC,MAAM,EAAE,GAAG,cAAc,CAAC;IAC1D,WAAW,CAAC,KAAe,CAAC,MAAM,EAAE,GAAG,UAAU,CAAC;IAClD,aAAa;IACb,QAAQ;IACR,aAAa;AACf;AAGO,MAAM,qBAAqB;IAChC,WAAW;IACX,UAAU;AACZ;AAGO,MAAM,4BAA4B;IACvC,WAAW;IACX,WAAW;AACb;AAGO,MAAM,iBAAiB;IAC5B;QAAE,OAAO;QAAO,OAAO;IAAS;IAChC;QAAE,OAAO;QAAU,OAAO;IAAe;IACzC;QAAE,OAAO;QAAY,OAAO;IAAc;CAC3C;AAGM,MAAM,sBAAsB;IACjC,WAAW;IACX,UAAU;IACV,WAAW;IACX,MAAM;IACN,KAAK;IACL,UAAU;IACV,MAAM;IACN,UAAU;AACZ;AAGO,MAAM,iBAAiB;IAC5B,gBAAgB;IAChB,gBAAgB;IAChB,gBAAgB,CAAC,OAAiB,CAAC,iBAAiB,EAAE,MAAM;IAC5D,gBAAgB,CAAC,MAAc,WAC7B,CAAC,GAAG,EAAE,WAAW,cAAc,cAAc,WAAW,EAAE,MAAM;IAClE,mBAAmB,CAAC,OAAiB,CAAC,sBAAsB,EAAE,KAAK,WAAW,CAAC;IAC/E,qBAAqB,CAAC,QAAkB,CAAC,OAAO,EAAE,MAAM,qBAAqB,CAAC;IAC9E,gBAAgB,CAAC,SAAmB,CAAC,qCAAqC,EAAE,OAAO,WAAW,IAAI;IAClG,gBAAgB,CAAC,QAAkB,CAAC,UAAU,EAAE,MAAM,qBAAqB,CAAC;IAE5E,iBAAiB;IACjB,cAAc;IACd,cAAc;IACd,cAAc;IACd,cAAc;IACd,iBAAiB;IACjB,mBAAmB;IACnB,cAAc,CAAC,SAAmB,CAAC,iCAAiC,EAAE,OAAO,WAAW,IAAI;IAC5F,cAAc;IACd,aAAa;IACb,oBAAoB;IACpB,wBAAwB;IAExB,cAAc;IACd,cAAc;IACd,WAAW;IACX,WAAW;AACb;AAGO,MAAM,mBAAmB;IAC9B,UAAU;IACV,UAAU;IACV,UAAU;IACV,UAAU;IACV,WAAW;IACX,WAAW;AACb", "debugId": null}}, {"offset": {"line": 190, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/bloomgoo/gold-exchange-platform/frontend/components/common/admin/ebanks/utils/bank-helpers.ts"], "sourcesContent": ["/**\r\n * Bank Helpers - <PERSON><PERSON><PERSON> hàm tiện ích cho module ngân hàng\r\n */\r\n\r\nimport { format } from 'date-fns';\r\nimport { vi } from 'date-fns/locale';\r\nimport type { BankDto, StatusFilter } from '../types';\r\n\r\n/**\r\n * Format date string theo định dạng Việt Nam\r\n */\r\nexport const formatDate = (dateStr: string | null | undefined): string => {\r\n  if (!dateStr) return '---';\r\n  try {\r\n    return format(new Date(dateStr), 'dd/MM/yyyy HH:mm', { locale: vi });\r\n  } catch (error) {\r\n    return 'Không hợp lệ';\r\n  }\r\n};\r\n\r\n/**\r\n * Lấy màu sắc cho status badge\r\n */\r\nexport const getStatusColor = (isActive: boolean): string => {\r\n  if (isActive) {\r\n    return 'bg-green-100 text-green-800 hover:bg-green-100/80';\r\n  } else {\r\n    return 'bg-gray-100 text-gray-800 hover:bg-gray-100/80';\r\n  }\r\n};\r\n\r\n/**\r\n * Lấy text cho status\r\n */\r\nexport const getStatusText = (isActive: boolean): string => {\r\n  return isActive ? 'Kích hoạt' : 'Vô hiệu hóa';\r\n};\r\n\r\n/**\r\n * Lấy variant cho status badge\r\n */\r\nexport const getStatusVariant = (isActive: boolean): 'success' | 'secondary' => {\r\n  return isActive ? 'success' : 'secondary';\r\n};\r\n\r\n/**\r\n * Filter banks theo status\r\n */\r\nexport const filterBanksByStatus = (banks: BankDto[], statusFilter: StatusFilter): BankDto[] => {\r\n  if (statusFilter === 'all') return banks;\r\n  if (statusFilter === 'active') return banks.filter(bank => bank.isActive);\r\n  if (statusFilter === 'inactive') return banks.filter(bank => !bank.isActive);\r\n  return banks;\r\n};\r\n\r\n/**\r\n * Tính toán status counts từ danh sách banks\r\n */\r\nexport const calculateStatusCounts = (banks: BankDto[]) => {\r\n  const active = banks.filter(bank => bank.isActive).length;\r\n  const inactive = banks.filter(bank => !bank.isActive).length;\r\n  \r\n  return {\r\n    all: banks.length,\r\n    active,\r\n    inactive,\r\n  };\r\n};\r\n\r\n/**\r\n * Tạo avatar fallback từ tên ngân hàng\r\n */\r\nexport const getBankAvatarFallback = (brandName: string): string => {\r\n  return brandName ? brandName[0].toUpperCase() : 'B';\r\n};\r\n\r\n/**\r\n * Tạo avatar URL từ tên ngân hàng\r\n */\r\nexport const getBankAvatarUrl = (brandName: string, logoPath?: string): string => {\r\n  if (logoPath) return logoPath;\r\n  return `https://api.dicebear.com/9.x/initials/svg?seed=${brandName}`;\r\n};\r\n\r\n/**\r\n * Validate URL\r\n */\r\nexport const isValidUrl = (url: string): boolean => {\r\n  try {\r\n    new URL(url);\r\n    return true;\r\n  } catch {\r\n    return false;\r\n  }\r\n};\r\n\r\n/**\r\n * Truncate text với tooltip\r\n */\r\nexport const truncateText = (text: string, maxLength: number): string => {\r\n  if (text.length <= maxLength) return text;\r\n  return text.substring(0, maxLength) + '...';\r\n};\r\n\r\n/**\r\n * Parse CSV content\r\n */\r\nexport const parseCSV = (content: string): any[] => {\r\n  const lines = content.split('\\n');\r\n  const headers = lines[0].split(',');\r\n  const data: any[] = [];\r\n\r\n  for (let i = 1; i < lines.length; i++) {\r\n    if (!lines[i].trim()) continue;\r\n\r\n    const values = lines[i].split(',');\r\n    const row: any = {};\r\n\r\n    headers.forEach((header, index) => {\r\n      row[header.trim()] = values[index]?.trim() || '';\r\n    });\r\n\r\n    data.push(row);\r\n  }\r\n\r\n  return data;\r\n};\r\n\r\n/**\r\n * Generate export filename\r\n */\r\nexport const generateExportFilename = (format: 'csv' | 'json'): string => {\r\n  const timestamp = format(new Date(), 'yyyy-MM-dd_HH-mm-ss');\r\n  return `banks-export_${timestamp}.${format}`;\r\n};\r\n\r\n/**\r\n * Map backend data to frontend DTO\r\n */\r\nexport const mapBankData = (backendData: any): BankDto => {\r\n  return {\r\n    ...backendData,\r\n    status: backendData.isActive ? 'ACTIVE' : 'INACTIVE',\r\n  };\r\n};\r\n\r\n/**\r\n * Prepare form data for API\r\n */\r\nexport const prepareFormData = (formData: any, mode: 'create' | 'update', bankId?: string) => {\r\n  const baseData = {\r\n    brandName: formData.brandName,\r\n    fullName: formData.fullName,\r\n    shortName: formData.shortName,\r\n    code: formData.code,\r\n    bin: formData.bin,\r\n    logoPath: formData.logoPath || undefined,\r\n    icon: formData.icon || undefined,\r\n    isActive: formData.isActive,\r\n  };\r\n\r\n  if (mode === 'update' && bankId) {\r\n    return {\r\n      id: bankId,\r\n      ...baseData,\r\n    };\r\n  }\r\n\r\n  return baseData;\r\n};\r\n"], "names": [], "mappings": "AAAA;;CAEC;;;;;;;;;;;;;;;;AAED;AACA;;;AAMO,MAAM,aAAa,CAAC;IACzB,IAAI,CAAC,SAAS,OAAO;IACrB,IAAI;QACF,OAAO,CAAA,GAAA,6MAAA,CAAA,SAAM,AAAD,EAAE,IAAI,KAAK,UAAU,oBAAoB;YAAE,QAAQ,mMAAA,CAAA,KAAE;QAAC;IACpE,EAAE,OAAO,OAAO;QACd,OAAO;IACT;AACF;AAKO,MAAM,iBAAiB,CAAC;IAC7B,IAAI,UAAU;QACZ,OAAO;IACT,OAAO;QACL,OAAO;IACT;AACF;AAKO,MAAM,gBAAgB,CAAC;IAC5B,OAAO,WAAW,cAAc;AAClC;AAKO,MAAM,mBAAmB,CAAC;IAC/B,OAAO,WAAW,YAAY;AAChC;AAKO,MAAM,sBAAsB,CAAC,OAAkB;IACpD,IAAI,iBAAiB,OAAO,OAAO;IACnC,IAAI,iBAAiB,UAAU,OAAO,MAAM,MAAM,CAAC,CAAA,OAAQ,KAAK,QAAQ;IACxE,IAAI,iBAAiB,YAAY,OAAO,MAAM,MAAM,CAAC,CAAA,OAAQ,CAAC,KAAK,QAAQ;IAC3E,OAAO;AACT;AAKO,MAAM,wBAAwB,CAAC;IACpC,MAAM,SAAS,MAAM,MAAM,CAAC,CAAA,OAAQ,KAAK,QAAQ,EAAE,MAAM;IACzD,MAAM,WAAW,MAAM,MAAM,CAAC,CAAA,OAAQ,CAAC,KAAK,QAAQ,EAAE,MAAM;IAE5D,OAAO;QACL,KAAK,MAAM,MAAM;QACjB;QACA;IACF;AACF;AAKO,MAAM,wBAAwB,CAAC;IACpC,OAAO,YAAY,SAAS,CAAC,EAAE,CAAC,WAAW,KAAK;AAClD;AAKO,MAAM,mBAAmB,CAAC,WAAmB;IAClD,IAAI,UAAU,OAAO;IACrB,OAAO,CAAC,+CAA+C,EAAE,WAAW;AACtE;AAKO,MAAM,aAAa,CAAC;IACzB,IAAI;QACF,IAAI,IAAI;QACR,OAAO;IACT,EAAE,OAAM;QACN,OAAO;IACT;AACF;AAKO,MAAM,eAAe,CAAC,MAAc;IACzC,IAAI,KAAK,MAAM,IAAI,WAAW,OAAO;IACrC,OAAO,KAAK,SAAS,CAAC,GAAG,aAAa;AACxC;AAKO,MAAM,WAAW,CAAC;IACvB,MAAM,QAAQ,QAAQ,KAAK,CAAC;IAC5B,MAAM,UAAU,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC;IAC/B,MAAM,OAAc,EAAE;IAEtB,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,MAAM,EAAE,IAAK;QACrC,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC,IAAI,IAAI;QAEtB,MAAM,SAAS,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC;QAC9B,MAAM,MAAW,CAAC;QAElB,QAAQ,OAAO,CAAC,CAAC,QAAQ;YACvB,GAAG,CAAC,OAAO,IAAI,GAAG,GAAG,MAAM,CAAC,MAAM,EAAE,UAAU;QAChD;QAEA,KAAK,IAAI,CAAC;IACZ;IAEA,OAAO;AACT;AAKO,MAAM,yBAAyB,CAAC;IACrC,MAAM,YAAY,OAAO,IAAI,QAAQ;IACrC,OAAO,CAAC,aAAa,EAAE,UAAU,CAAC,EAAE,QAAQ;AAC9C;AAKO,MAAM,cAAc,CAAC;IAC1B,OAAO;QACL,GAAG,WAAW;QACd,QAAQ,YAAY,QAAQ,GAAG,WAAW;IAC5C;AACF;AAKO,MAAM,kBAAkB,CAAC,UAAe,MAA2B;IACxE,MAAM,WAAW;QACf,WAAW,SAAS,SAAS;QAC7B,UAAU,SAAS,QAAQ;QAC3B,WAAW,SAAS,SAAS;QAC7B,MAAM,SAAS,IAAI;QACnB,KAAK,SAAS,GAAG;QACjB,UAAU,SAAS,QAAQ,IAAI;QAC/B,MAAM,SAAS,IAAI,IAAI;QACvB,UAAU,SAAS,QAAQ;IAC7B;IAEA,IAAI,SAAS,YAAY,QAAQ;QAC/B,OAAO;YACL,IAAI;YACJ,GAAG,QAAQ;QACb;IACF;IAEA,OAAO;AACT", "debugId": null}}, {"offset": {"line": 319, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/bloomgoo/gold-exchange-platform/frontend/components/common/admin/ebanks/hooks/use-bank-data.ts"], "sourcesContent": ["'use client';\r\n/**\r\n * useBankData Hook - Quản lý data và API calls cho ngân hàng\r\n */\r\n\r\nimport { useState, useEffect, useCallback } from 'react';\r\nimport { toast } from 'sonner';\r\nimport { api } from '@/lib/api';\r\nimport { PaginationResponse } from '@/lib/response';\r\nimport type { BankDto, BankStatisticsDto, StatusCounts, StatusFilter } from '../types';\r\nimport { BANK_ENDPOINTS, TOAST_MESSAGES } from '../utils/bank-constants';\r\nimport { mapBankData, calculateStatusCounts, filterBanksByStatus } from '../utils/bank-helpers';\r\n\r\ninterface UseBankDataProps {\r\n  pagination: {\r\n    pageIndex: number;\r\n    pageSize: number;\r\n  };\r\n  statusFilter: StatusFilter;\r\n  globalFilter: string;\r\n  sorting: Array<{\r\n    id: string;\r\n    desc: boolean;\r\n  }>;\r\n}\r\n\r\nexport function useBankData({ pagination, statusFilter, globalFilter, sorting }: UseBankDataProps) {\r\n  const [banks, setBanks] = useState<BankDto[]>([]);\r\n  const [loading, setLoading] = useState(true);\r\n  const [totalRows, setTotalRows] = useState(0);\r\n  const [statusCounts, setStatusCounts] = useState<StatusCounts>({ all: 0, active: 0, inactive: 0 });\r\n\r\n  // Fetch banks data\r\n  const fetchBanks = useCallback(async () => {\r\n    setLoading(true);\r\n    try {\r\n      // Xử lý sorting\r\n      let sortByParam = '';\r\n      let sortOrderParam = '';\r\n      if (sorting.length > 0) {\r\n        const firstSort = sorting[0];\r\n        sortByParam = `&sortBy=${firstSort.id}`;\r\n        sortOrderParam = `&sortOrder=${firstSort.desc ? 'DESC' : 'ASC'}`;\r\n      }\r\n\r\n      // Xử lý search\r\n      let searchParam = '';\r\n      if (globalFilter) {\r\n        searchParam = `&search=${encodeURIComponent(globalFilter)}`;\r\n      }\r\n\r\n      const url = `${BANK_ENDPOINTS.LIST}?page=${pagination.pageIndex + 1}&limit=${pagination.pageSize}${sortByParam}${sortOrderParam}${searchParam}`;\r\n\r\n      const response = await api.get<PaginationResponse<BankDto>>(url);\r\n\r\n      if (response && response.data) {\r\n        // Map backend data to frontend DTO\r\n        const mappedBanks = response.data.map(mapBankData);\r\n\r\n        // Lọc dữ liệu theo trạng thái ở phía client\r\n        const filteredBanks = filterBanksByStatus(mappedBanks, statusFilter);\r\n\r\n        setBanks(filteredBanks);\r\n\r\n        // Sử dụng filtered length cho pagination (giống module gốc)\r\n        setTotalRows(filteredBanks.length);\r\n\r\n        // Tổng số từ server để tính status counts\r\n        const totalBanks = response.meta?.itemCount || mappedBanks.length;\r\n\r\n        // Cập nhật số lượng cho các tab trạng thái\r\n        const counts = calculateStatusCounts(mappedBanks);\r\n        setStatusCounts({\r\n          all: totalBanks,\r\n          active: counts.active,\r\n          inactive: counts.inactive,\r\n        });\r\n      } else {\r\n        setBanks([]);\r\n        setTotalRows(0);\r\n        setStatusCounts({ all: 0, active: 0, inactive: 0 });\r\n      }\r\n    } catch (error: any) {\r\n      console.error('Error fetching banks:', error);\r\n\r\n      // Xử lý các loại lỗi khác nhau\r\n      if (error.status === 401) {\r\n        toast.error(TOAST_MESSAGES.UNAUTHORIZED);\r\n      } else if (error.status === 403) {\r\n        toast.error(TOAST_MESSAGES.FORBIDDEN);\r\n      } else if (error.status === 404) {\r\n        toast.error(TOAST_MESSAGES.NOT_FOUND);\r\n      } else {\r\n        toast.error(TOAST_MESSAGES.FETCH_ERROR);\r\n      }\r\n\r\n      // Đặt giá trị mặc định khi có lỗi\r\n      setBanks([]);\r\n      setTotalRows(0);\r\n      setStatusCounts({ all: 0, active: 0, inactive: 0 });\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  }, [pagination.pageIndex, pagination.pageSize, statusFilter, globalFilter, sorting]);\r\n\r\n  // Fetch statistics\r\n  const fetchStatistics = useCallback(async (force = false) => {\r\n    // Sử dụng closure để theo dõi xem đã gọi API chưa\r\n    if (!(fetchStatistics as any).hasRun || force) {\r\n      try {\r\n        const response = await api.get<BankStatisticsDto>(BANK_ENDPOINTS.STATISTICS);\r\n\r\n        if (response) {\r\n          const counts = {\r\n            all: response.total || 0,\r\n            active: response.active || 0,\r\n            inactive: response.inactive || 0,\r\n          };\r\n\r\n          setStatusCounts(counts);\r\n          \r\n        }\r\n\r\n        // Đánh dấu đã gọi API\r\n        (fetchStatistics as any).hasRun = true;\r\n      } catch (error) {\r\n        console.error('Error fetching bank statistics:', error);\r\n        toast.error(TOAST_MESSAGES.FETCH_STATISTICS_ERROR);\r\n\r\n        // Đặt giá trị mặc định khi có lỗi\r\n        setStatusCounts({ all: 0, active: 0, inactive: 0 });\r\n      }\r\n    }\r\n  }, []);\r\n\r\n  // Fetch bank detail\r\n  const fetchBankDetail = useCallback(async (bankId: string): Promise<BankDto | null> => {\r\n    try {\r\n      const response = await api.get<BankDto>(`${BANK_ENDPOINTS.LIST}/${bankId}`);\r\n      return response ? mapBankData(response) : null;\r\n    } catch (error) {\r\n      console.error('Error fetching bank detail:', error);\r\n      toast.error(TOAST_MESSAGES.FETCH_DETAIL_ERROR);\r\n      return null;\r\n    }\r\n  }, []);\r\n\r\n  // Update local bank data\r\n  const updateLocalBank = useCallback((bankId: string, updates: Partial<BankDto>) => {\r\n    setBanks(prevBanks =>\r\n      prevBanks.map(bank =>\r\n        bank.id === bankId ? { ...bank, ...updates } : bank\r\n      )\r\n    );\r\n  }, []);\r\n\r\n  // Remove local bank\r\n  const removeLocalBank = useCallback((bankId: string) => {\r\n    setBanks(prevBanks => prevBanks.filter(bank => bank.id !== bankId));\r\n    setTotalRows(prev => prev - 1);\r\n  }, []);\r\n\r\n  // Add local bank\r\n  const addLocalBank = useCallback((newBank: BankDto) => {\r\n    setBanks(prevBanks => [newBank, ...prevBanks]);\r\n    setTotalRows(prev => prev + 1);\r\n  }, []);\r\n\r\n  // Effect để fetch data khi dependencies thay đổi\r\n  useEffect(() => {\r\n    fetchBanks();\r\n  }, [fetchBanks]);\r\n\r\n  return {\r\n    // Data\r\n    banks,\r\n    loading,\r\n    totalRows,\r\n    statusCounts,\r\n\r\n    // Actions\r\n    fetchBanks,\r\n    fetchStatistics,\r\n    fetchBankDetail,\r\n    updateLocalBank,\r\n    removeLocalBank,\r\n    addLocalBank,\r\n  };\r\n}\r\n"], "names": [], "mappings": ";;;AACA;;CAEC,GAED;AACA;AACA;AAGA;AACA;AAXA;;;;;;AA0BO,SAAS,YAAY,EAAE,UAAU,EAAE,YAAY,EAAE,YAAY,EAAE,OAAO,EAAoB;IAC/F,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,8SAAA,CAAA,WAAQ,AAAD,EAAa,EAAE;IAChD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,8SAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,8SAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,8SAAA,CAAA,WAAQ,AAAD,EAAgB;QAAE,KAAK;QAAG,QAAQ;QAAG,UAAU;IAAE;IAEhG,mBAAmB;IACnB,MAAM,aAAa,CAAA,GAAA,8SAAA,CAAA,cAAW,AAAD,EAAE;QAC7B,WAAW;QACX,IAAI;YACF,gBAAgB;YAChB,IAAI,cAAc;YAClB,IAAI,iBAAiB;YACrB,IAAI,QAAQ,MAAM,GAAG,GAAG;gBACtB,MAAM,YAAY,OAAO,CAAC,EAAE;gBAC5B,cAAc,CAAC,QAAQ,EAAE,UAAU,EAAE,EAAE;gBACvC,iBAAiB,CAAC,WAAW,EAAE,UAAU,IAAI,GAAG,SAAS,OAAO;YAClE;YAEA,eAAe;YACf,IAAI,cAAc;YAClB,IAAI,cAAc;gBAChB,cAAc,CAAC,QAAQ,EAAE,mBAAmB,eAAe;YAC7D;YAEA,MAAM,MAAM,GAAG,qKAAA,CAAA,iBAAc,CAAC,IAAI,CAAC,MAAM,EAAE,WAAW,SAAS,GAAG,EAAE,OAAO,EAAE,WAAW,QAAQ,GAAG,cAAc,iBAAiB,aAAa;YAE/I,MAAM,WAAW,MAAM,0GAAA,CAAA,MAAG,CAAC,GAAG,CAA8B;YAE5D,IAAI,YAAY,SAAS,IAAI,EAAE;gBAC7B,mCAAmC;gBACnC,MAAM,cAAc,SAAS,IAAI,CAAC,GAAG,CAAC,mKAAA,CAAA,cAAW;gBAEjD,4CAA4C;gBAC5C,MAAM,gBAAgB,CAAA,GAAA,mKAAA,CAAA,sBAAmB,AAAD,EAAE,aAAa;gBAEvD,SAAS;gBAET,4DAA4D;gBAC5D,aAAa,cAAc,MAAM;gBAEjC,0CAA0C;gBAC1C,MAAM,aAAa,SAAS,IAAI,EAAE,aAAa,YAAY,MAAM;gBAEjE,2CAA2C;gBAC3C,MAAM,SAAS,CAAA,GAAA,mKAAA,CAAA,wBAAqB,AAAD,EAAE;gBACrC,gBAAgB;oBACd,KAAK;oBACL,QAAQ,OAAO,MAAM;oBACrB,UAAU,OAAO,QAAQ;gBAC3B;YACF,OAAO;gBACL,SAAS,EAAE;gBACX,aAAa;gBACb,gBAAgB;oBAAE,KAAK;oBAAG,QAAQ;oBAAG,UAAU;gBAAE;YACnD;QACF,EAAE,OAAO,OAAY;YACnB,QAAQ,KAAK,CAAC,yBAAyB;YAEvC,+BAA+B;YAC/B,IAAI,MAAM,MAAM,KAAK,KAAK;gBACxB,wQAAA,CAAA,QAAK,CAAC,KAAK,CAAC,qKAAA,CAAA,iBAAc,CAAC,YAAY;YACzC,OAAO,IAAI,MAAM,MAAM,KAAK,KAAK;gBAC/B,wQAAA,CAAA,QAAK,CAAC,KAAK,CAAC,qKAAA,CAAA,iBAAc,CAAC,SAAS;YACtC,OAAO,IAAI,MAAM,MAAM,KAAK,KAAK;gBAC/B,wQAAA,CAAA,QAAK,CAAC,KAAK,CAAC,qKAAA,CAAA,iBAAc,CAAC,SAAS;YACtC,OAAO;gBACL,wQAAA,CAAA,QAAK,CAAC,KAAK,CAAC,qKAAA,CAAA,iBAAc,CAAC,WAAW;YACxC;YAEA,kCAAkC;YAClC,SAAS,EAAE;YACX,aAAa;YACb,gBAAgB;gBAAE,KAAK;gBAAG,QAAQ;gBAAG,UAAU;YAAE;QACnD,SAAU;YACR,WAAW;QACb;IACF,GAAG;QAAC,WAAW,SAAS;QAAE,WAAW,QAAQ;QAAE;QAAc;QAAc;KAAQ;IAEnF,mBAAmB;IACnB,MAAM,kBAAkB,CAAA,GAAA,8SAAA,CAAA,cAAW,AAAD,EAAE,OAAO,QAAQ,KAAK;QACtD,kDAAkD;QAClD,IAAI,CAAC,AAAC,gBAAwB,MAAM,IAAI,OAAO;YAC7C,IAAI;gBACF,MAAM,WAAW,MAAM,0GAAA,CAAA,MAAG,CAAC,GAAG,CAAoB,qKAAA,CAAA,iBAAc,CAAC,UAAU;gBAE3E,IAAI,UAAU;oBACZ,MAAM,SAAS;wBACb,KAAK,SAAS,KAAK,IAAI;wBACvB,QAAQ,SAAS,MAAM,IAAI;wBAC3B,UAAU,SAAS,QAAQ,IAAI;oBACjC;oBAEA,gBAAgB;gBAElB;gBAEA,sBAAsB;gBACrB,gBAAwB,MAAM,GAAG;YACpC,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,mCAAmC;gBACjD,wQAAA,CAAA,QAAK,CAAC,KAAK,CAAC,qKAAA,CAAA,iBAAc,CAAC,sBAAsB;gBAEjD,kCAAkC;gBAClC,gBAAgB;oBAAE,KAAK;oBAAG,QAAQ;oBAAG,UAAU;gBAAE;YACnD;QACF;IACF,GAAG,EAAE;IAEL,oBAAoB;IACpB,MAAM,kBAAkB,CAAA,GAAA,8SAAA,CAAA,cAAW,AAAD,EAAE,OAAO;QACzC,IAAI;YACF,MAAM,WAAW,MAAM,0GAAA,CAAA,MAAG,CAAC,GAAG,CAAU,GAAG,qKAAA,CAAA,iBAAc,CAAC,IAAI,CAAC,CAAC,EAAE,QAAQ;YAC1E,OAAO,WAAW,CAAA,GAAA,mKAAA,CAAA,cAAW,AAAD,EAAE,YAAY;QAC5C,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,+BAA+B;YAC7C,wQAAA,CAAA,QAAK,CAAC,KAAK,CAAC,qKAAA,CAAA,iBAAc,CAAC,kBAAkB;YAC7C,OAAO;QACT;IACF,GAAG,EAAE;IAEL,yBAAyB;IACzB,MAAM,kBAAkB,CAAA,GAAA,8SAAA,CAAA,cAAW,AAAD,EAAE,CAAC,QAAgB;QACnD,SAAS,CAAA,YACP,UAAU,GAAG,CAAC,CAAA,OACZ,KAAK,EAAE,KAAK,SAAS;oBAAE,GAAG,IAAI;oBAAE,GAAG,OAAO;gBAAC,IAAI;IAGrD,GAAG,EAAE;IAEL,oBAAoB;IACpB,MAAM,kBAAkB,CAAA,GAAA,8SAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QACnC,SAAS,CAAA,YAAa,UAAU,MAAM,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK;QAC3D,aAAa,CAAA,OAAQ,OAAO;IAC9B,GAAG,EAAE;IAEL,iBAAiB;IACjB,MAAM,eAAe,CAAA,GAAA,8SAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QAChC,SAAS,CAAA,YAAa;gBAAC;mBAAY;aAAU;QAC7C,aAAa,CAAA,OAAQ,OAAO;IAC9B,GAAG,EAAE;IAEL,iDAAiD;IACjD,CAAA,GAAA,8SAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG;QAAC;KAAW;IAEf,OAAO;QACL,OAAO;QACP;QACA;QACA;QACA;QAEA,UAAU;QACV;QACA;QACA;QACA;QACA;QACA;IACF;AACF", "debugId": null}}, {"offset": {"line": 505, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/bloomgoo/gold-exchange-platform/frontend/components/common/admin/ebanks/hooks/use-bank-table.ts"], "sourcesContent": ["'use client';\r\n/**\r\n * useBankTable Hook - Quản lý table state và logic\r\n */\r\n\r\nimport { useState } from 'react';\r\nimport { useReactTable, getCoreRowModel, getSortedRowModel, getFilteredRowModel } from '@tanstack/react-table';\r\nimport type {\r\n  ColumnFiltersState,\r\n  PaginationState,\r\n  RowSelectionState,\r\n  SortingState,\r\n  VisibilityState\r\n} from '@tanstack/react-table';\r\nimport type { BankDto } from '../types';\r\nimport { DEFAULT_PAGINATION, DEFAULT_COLUMN_VISIBILITY } from '../utils/bank-constants';\r\n\r\ninterface UseBankTableProps {\r\n  banks: BankDto[];\r\n  totalRows: number;\r\n  columns: any[];\r\n  onViewDetail: (bank: BankDto) => void;\r\n  onEdit: (bank: BankDto) => void;\r\n  onDelete: (bank: BankDto) => void;\r\n  onToggleStatus: (bank: BankDto) => void;\r\n  onDuplicate?: (bank: BankDto) => void;\r\n}\r\n\r\nexport function useBankTable({\r\n  banks,\r\n  totalRows,\r\n  columns,\r\n  onViewDetail,\r\n  onEdit,\r\n  onDelete,\r\n  onToggleStatus,\r\n  onDuplicate,\r\n}: UseBankTableProps) {\r\n  // Table states\r\n  const [sorting, setSorting] = useState<SortingState>([]);\r\n  const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([]);\r\n  const [pagination, setPagination] = useState<PaginationState>(DEFAULT_PAGINATION);\r\n  const [rowSelection, setRowSelection] = useState<RowSelectionState>({});\r\n  const [columnVisibility, setColumnVisibility] = useState<VisibilityState>(DEFAULT_COLUMN_VISIBILITY);\r\n  const [globalFilter, setGlobalFilter] = useState(\"\");\r\n\r\n  // React Table instance\r\n  const table = useReactTable({\r\n    data: banks,\r\n    columns,\r\n    state: {\r\n      sorting,\r\n      columnFilters,\r\n      pagination,\r\n      rowSelection,\r\n      columnVisibility,\r\n      globalFilter,\r\n    },\r\n    pageCount: Math.ceil(totalRows / pagination.pageSize),\r\n    onSortingChange: setSorting,\r\n    onColumnFiltersChange: setColumnFilters,\r\n    onPaginationChange: setPagination,\r\n    onRowSelectionChange: setRowSelection,\r\n    onColumnVisibilityChange: setColumnVisibility,\r\n    onGlobalFilterChange: setGlobalFilter,\r\n    getCoreRowModel: getCoreRowModel(),\r\n    getSortedRowModel: getSortedRowModel(),\r\n    getFilteredRowModel: getFilteredRowModel(),\r\n    manualPagination: true,\r\n    enableSorting: true,\r\n    enableColumnFilters: true,\r\n    enableRowSelection: true,\r\n    enableMultiSort: true,\r\n    manualSorting: true,\r\n    manualFiltering: true,\r\n  });\r\n\r\n  // Helper functions\r\n  const getSelectedBanks = () => {\r\n    return table.getSelectedRowModel().rows.map(row => row.original);\r\n  };\r\n\r\n  const getSelectedCount = () => {\r\n    return table.getFilteredSelectedRowModel().rows.length;\r\n  };\r\n\r\n  const resetSelection = () => {\r\n    table.resetRowSelection();\r\n  };\r\n\r\n  const resetFilters = () => {\r\n    table.resetColumnFilters();\r\n    table.resetSorting();\r\n    setGlobalFilter('');\r\n  };\r\n\r\n  return {\r\n    // Table instance\r\n    table,\r\n\r\n    // States\r\n    sorting,\r\n    columnFilters,\r\n    pagination,\r\n    rowSelection,\r\n    columnVisibility,\r\n    globalFilter,\r\n\r\n    // Setters\r\n    setSorting,\r\n    setColumnFilters,\r\n    setPagination,\r\n    setRowSelection,\r\n    setColumnVisibility,\r\n    setGlobalFilter,\r\n\r\n    // Helper functions\r\n    getSelectedBanks,\r\n    getSelectedCount,\r\n    resetSelection,\r\n    resetFilters,\r\n  };\r\n}\r\n"], "names": [], "mappings": ";;;AACA;;CAEC,GAED;AACA;AAAA;AASA;AAfA;;;;AA4BO,SAAS,aAAa,EAC3B,KAAK,EACL,SAAS,EACT,OAAO,EACP,YAAY,EACZ,MAAM,EACN,QAAQ,EACR,cAAc,EACd,WAAW,EACO;IAClB,eAAe;IACf,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,8SAAA,CAAA,WAAQ,AAAD,EAAgB,EAAE;IACvD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,8SAAA,CAAA,WAAQ,AAAD,EAAsB,EAAE;IACzE,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,8SAAA,CAAA,WAAQ,AAAD,EAAmB,qKAAA,CAAA,qBAAkB;IAChF,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,8SAAA,CAAA,WAAQ,AAAD,EAAqB,CAAC;IACrE,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,8SAAA,CAAA,WAAQ,AAAD,EAAmB,qKAAA,CAAA,4BAAyB;IACnG,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,8SAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,uBAAuB;IACvB,MAAM,QAAQ,CAAA,GAAA,gSAAA,CAAA,gBAAa,AAAD,EAAE;QAC1B,MAAM;QACN;QACA,OAAO;YACL;YACA;YACA;YACA;YACA;YACA;QACF;QACA,WAAW,KAAK,IAAI,CAAC,YAAY,WAAW,QAAQ;QACpD,iBAAiB;QACjB,uBAAuB;QACvB,oBAAoB;QACpB,sBAAsB;QACtB,0BAA0B;QAC1B,sBAAsB;QACtB,iBAAiB,CAAA,GAAA,8OAAA,CAAA,kBAAe,AAAD;QAC/B,mBAAmB,CAAA,GAAA,8OAAA,CAAA,oBAAiB,AAAD;QACnC,qBAAqB,CAAA,GAAA,8OAAA,CAAA,sBAAmB,AAAD;QACvC,kBAAkB;QAClB,eAAe;QACf,qBAAqB;QACrB,oBAAoB;QACpB,iBAAiB;QACjB,eAAe;QACf,iBAAiB;IACnB;IAEA,mBAAmB;IACnB,MAAM,mBAAmB;QACvB,OAAO,MAAM,mBAAmB,GAAG,IAAI,CAAC,GAAG,CAAC,CAAA,MAAO,IAAI,QAAQ;IACjE;IAEA,MAAM,mBAAmB;QACvB,OAAO,MAAM,2BAA2B,GAAG,IAAI,CAAC,MAAM;IACxD;IAEA,MAAM,iBAAiB;QACrB,MAAM,iBAAiB;IACzB;IAEA,MAAM,eAAe;QACnB,MAAM,kBAAkB;QACxB,MAAM,YAAY;QAClB,gBAAgB;IAClB;IAEA,OAAO;QACL,iBAAiB;QACjB;QAEA,SAAS;QACT;QACA;QACA;QACA;QACA;QACA;QAEA,UAAU;QACV;QACA;QACA;QACA;QACA;QACA;QAEA,mBAAmB;QACnB;QACA;QACA;QACA;IACF;AACF", "debugId": null}}, {"offset": {"line": 601, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/bloomgoo/gold-exchange-platform/frontend/components/common/admin/ebanks/hooks/use-bank-actions.ts"], "sourcesContent": ["\"use client\";\r\n\r\n/**\r\n * useBankActions Hook - Quản lý các actions CRUD cho ngân hàng\r\n */\r\n\r\nimport { useState, useCallback } from 'react';\r\nimport { toast } from 'sonner';\r\nimport { api } from '@/lib/api';\r\nimport { ApiResponse } from '@/lib/response';\r\nimport type { BankDto, CreateBankDto, UpdateBankDto } from '../types';\r\nimport { BANK_ENDPOINTS, TOAST_MESSAGES } from '../utils/bank-constants';\r\nimport { prepareFormData, parseCSV } from '../utils/bank-helpers';\r\n\r\ninterface UseBankActionsProps {\r\n  onSuccess?: () => void;\r\n  updateLocalBank?: (bankId: string, updates: Partial<BankDto>) => void;\r\n  removeLocalBank?: (bankId: string) => void;\r\n  addLocalBank?: (bank: BankDto) => void;\r\n}\r\n\r\nexport function useBankActions({\r\n  onSuccess,\r\n  updateLocalBank,\r\n  removeLocalBank,\r\n  addLocalBank,\r\n}: UseBankActionsProps = {}) {\r\n  const [isUpdating, setIsUpdating] = useState(false);\r\n\r\n  // Create bank\r\n  const createBank = useCallback(async (formData: any): Promise<boolean> => {\r\n    setIsUpdating(true);\r\n    try {\r\n      const createPayload = prepareFormData(formData, 'create') as CreateBankDto;\r\n      \r\n      const response = await api.post<ApiResponse<BankDto>>(BANK_ENDPOINTS.CREATE, createPayload);\r\n      \r\n      if (response) {\r\n        toast.success(TOAST_MESSAGES.CREATE_SUCCESS);\r\n        if (addLocalBank && response.data) {\r\n          addLocalBank(response.data);\r\n        }\r\n        if (onSuccess) onSuccess();\r\n        return true;\r\n      }\r\n      return false;\r\n    } catch (error: any) {\r\n      console.error('Error creating bank:', error);\r\n      toast.error(`${TOAST_MESSAGES.CREATE_ERROR}: ${error.message || 'Không xác định'}`);\r\n      return false;\r\n    } finally {\r\n      setIsUpdating(false);\r\n    }\r\n  }, [addLocalBank, onSuccess]);\r\n\r\n  // Update bank\r\n  const updateBank = useCallback(async (bankId: string, formData: any): Promise<boolean> => {\r\n    setIsUpdating(true);\r\n    try {\r\n      const updatePayload = prepareFormData(formData, 'update', bankId) as UpdateBankDto;\r\n      \r\n      const response = await api.patch<ApiResponse<BankDto>>(\r\n        BANK_ENDPOINTS.UPDATE(bankId), \r\n        updatePayload\r\n      );\r\n      \r\n      if (response) {\r\n        toast.success(TOAST_MESSAGES.UPDATE_SUCCESS);\r\n        if (updateLocalBank && response.data) {\r\n          updateLocalBank(bankId, response.data);\r\n        }\r\n        if (onSuccess) onSuccess();\r\n        return true;\r\n      }\r\n      return false;\r\n    } catch (error: any) {\r\n      console.error('Error updating bank:', error);\r\n      toast.error(`${TOAST_MESSAGES.UPDATE_ERROR}: ${error.message || 'Không xác định'}`);\r\n      return false;\r\n    } finally {\r\n      setIsUpdating(false);\r\n    }\r\n  }, [updateLocalBank, onSuccess]);\r\n\r\n  // Delete bank\r\n  const deleteBank = useCallback(async (bank: BankDto): Promise<boolean> => {\r\n    setIsUpdating(true);\r\n    try {\r\n      await api.post(BANK_ENDPOINTS.DELETE(bank.id));\r\n      \r\n      toast.success(TOAST_MESSAGES.DELETE_SUCCESS(bank.brandName));\r\n      \r\n      // Cập nhật dữ liệu local\r\n      if (updateLocalBank) {\r\n        updateLocalBank(bank.id, {\r\n          isDeleted: true,\r\n          deletedAt: new Date().toISOString()\r\n        });\r\n      }\r\n      \r\n      if (onSuccess) onSuccess();\r\n      return true;\r\n    } catch (error: any) {\r\n      console.error('Error deleting bank:', error);\r\n      toast.error(TOAST_MESSAGES.DELETE_ERROR);\r\n      return false;\r\n    } finally {\r\n      setIsUpdating(false);\r\n    }\r\n  }, [updateLocalBank, onSuccess]);\r\n\r\n  // Toggle bank status\r\n  const toggleBankStatus = useCallback(async (bank: BankDto): Promise<boolean> => {\r\n    setIsUpdating(true);\r\n    try {\r\n      await api.put(BANK_ENDPOINTS.TOGGLE_STATUS(bank.id));\r\n      \r\n      const newStatus = !bank.isActive;\r\n      toast.success(TOAST_MESSAGES.TOGGLE_SUCCESS(bank.brandName, newStatus));\r\n      \r\n      // Cập nhật dữ liệu local\r\n      if (updateLocalBank) {\r\n        updateLocalBank(bank.id, {\r\n          isActive: newStatus,\r\n          status: newStatus ? 'ACTIVE' : 'INACTIVE'\r\n        });\r\n      }\r\n      \r\n      if (onSuccess) onSuccess();\r\n      return true;\r\n    } catch (error: any) {\r\n      console.error('Error toggling bank status:', error);\r\n      toast.error(TOAST_MESSAGES.TOGGLE_ERROR);\r\n      return false;\r\n    } finally {\r\n      setIsUpdating(false);\r\n    }\r\n  }, [updateLocalBank, onSuccess]);\r\n\r\n  // Duplicate bank\r\n  const duplicateBank = useCallback(async (bank: BankDto): Promise<boolean> => {\r\n    setIsUpdating(true);\r\n    try {\r\n      const response = await api.post<BankDto>(BANK_ENDPOINTS.DUPLICATE(bank.id));\r\n      \r\n      if (response) {\r\n        toast.success(TOAST_MESSAGES.DUPLICATE_SUCCESS(bank.brandName));\r\n        if (addLocalBank) {\r\n          addLocalBank(response);\r\n        }\r\n        if (onSuccess) onSuccess();\r\n        return true;\r\n      }\r\n      return false;\r\n    } catch (error: any) {\r\n      console.error('Error duplicating bank:', error);\r\n      toast.error(TOAST_MESSAGES.DUPLICATE_ERROR);\r\n      return false;\r\n    } finally {\r\n      setIsUpdating(false);\r\n    }\r\n  }, [addLocalBank, onSuccess]);\r\n\r\n  // Bulk delete banks\r\n  const bulkDeleteBanks = useCallback(async (banks: BankDto[]): Promise<boolean> => {\r\n    setIsUpdating(true);\r\n    try {\r\n      // Thực hiện xóa từng ngân hàng\r\n      const deletePromises = banks.map(bank =>\r\n        api.post(BANK_ENDPOINTS.DELETE(bank.id))\r\n      );\r\n\r\n      await Promise.all(deletePromises);\r\n      \r\n      toast.success(TOAST_MESSAGES.BULK_DELETE_SUCCESS(banks.length));\r\n      \r\n      // Cập nhật dữ liệu local\r\n      if (removeLocalBank) {\r\n        banks.forEach(bank => removeLocalBank(bank.id));\r\n      }\r\n      \r\n      if (onSuccess) onSuccess();\r\n      return true;\r\n    } catch (error: any) {\r\n      console.error('Error bulk deleting banks:', error);\r\n      toast.error(TOAST_MESSAGES.BULK_DELETE_ERROR);\r\n      return false;\r\n    } finally {\r\n      setIsUpdating(false);\r\n    }\r\n  }, [removeLocalBank, onSuccess]);\r\n\r\n  // Export banks\r\n  const exportBanks = useCallback(async (format: 'csv' | 'json' = 'csv'): Promise<boolean> => {\r\n    setIsUpdating(true);\r\n    try {\r\n      await api.downloadFile(\r\n        `${BANK_ENDPOINTS.EXPORT}?format=${format}`, \r\n        format, \r\n        `banks-export.${format}`\r\n      );\r\n      toast.success(TOAST_MESSAGES.EXPORT_SUCCESS(format));\r\n      return true;\r\n    } catch (error: any) {\r\n      console.error(`Error exporting banks as ${format}:`, error);\r\n      toast.error(TOAST_MESSAGES.EXPORT_ERROR(format));\r\n      return false;\r\n    } finally {\r\n      setIsUpdating(false);\r\n    }\r\n  }, []);\r\n\r\n  // Import banks\r\n  const importBanks = useCallback(async (): Promise<boolean> => {\r\n    return new Promise((resolve) => {\r\n      try {\r\n        // Tạo input file ẩn\r\n        const input = document.createElement('input');\r\n        input.type = 'file';\r\n        input.accept = '.csv,.json';\r\n\r\n        // Xử lý sự kiện khi người dùng chọn file\r\n        input.onchange = async (e: Event) => {\r\n          const target = e.target as HTMLInputElement;\r\n          if (!target.files || target.files.length === 0) {\r\n            resolve(false);\r\n            return;\r\n          }\r\n\r\n          const file = target.files[0];\r\n          const fileType = file.name.endsWith('.csv') ? 'csv' : 'json';\r\n\r\n          setIsUpdating(true);\r\n          try {\r\n            // Đọc file\r\n            const fileContent = await file.text();\r\n            let banksData: any[] = [];\r\n\r\n            // Parse dữ liệu từ file\r\n            if (fileType === 'csv') {\r\n              banksData = parseCSV(fileContent);\r\n            } else {\r\n              banksData = JSON.parse(fileContent);\r\n            }\r\n\r\n            // Gọi API để tạo hàng loạt ngân hàng\r\n            await api.post(BANK_ENDPOINTS.BULK_CREATE, banksData);\r\n\r\n            toast.success(TOAST_MESSAGES.IMPORT_SUCCESS(banksData.length));\r\n            if (onSuccess) onSuccess();\r\n            resolve(true);\r\n          } catch (error: any) {\r\n            console.error('Error importing banks:', error);\r\n            toast.error(TOAST_MESSAGES.IMPORT_ERROR);\r\n            resolve(false);\r\n          } finally {\r\n            setIsUpdating(false);\r\n          }\r\n        };\r\n\r\n        // Click để mở hộp thoại chọn file\r\n        input.click();\r\n      } catch (error: any) {\r\n        console.error('Error setting up import:', error);\r\n        toast.error(TOAST_MESSAGES.IMPORT_ERROR);\r\n        resolve(false);\r\n      }\r\n    });\r\n  }, [onSuccess]);\r\n\r\n  return {\r\n    // State\r\n    isUpdating,\r\n\r\n    // Actions\r\n    createBank,\r\n    updateBank,\r\n    deleteBank,\r\n    toggleBankStatus,\r\n    duplicateBank,\r\n    bulkDeleteBanks,\r\n    exportBanks,\r\n    importBanks,\r\n  };\r\n}\r\n"], "names": [], "mappings": ";;;AAEA;;CAEC,GAED;AACA;AACA;AAGA;AACA;AAZA;;;;;;AAqBO,SAAS,eAAe,EAC7B,SAAS,EACT,eAAe,EACf,eAAe,EACf,YAAY,EACQ,GAAG,CAAC,CAAC;IACzB,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,8SAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,cAAc;IACd,MAAM,aAAa,CAAA,GAAA,8SAAA,CAAA,cAAW,AAAD,EAAE,OAAO;QACpC,cAAc;QACd,IAAI;YACF,MAAM,gBAAgB,CAAA,GAAA,mKAAA,CAAA,kBAAe,AAAD,EAAE,UAAU;YAEhD,MAAM,WAAW,MAAM,0GAAA,CAAA,MAAG,CAAC,IAAI,CAAuB,qKAAA,CAAA,iBAAc,CAAC,MAAM,EAAE;YAE7E,IAAI,UAAU;gBACZ,wQAAA,CAAA,QAAK,CAAC,OAAO,CAAC,qKAAA,CAAA,iBAAc,CAAC,cAAc;gBAC3C,IAAI,gBAAgB,SAAS,IAAI,EAAE;oBACjC,aAAa,SAAS,IAAI;gBAC5B;gBACA,IAAI,WAAW;gBACf,OAAO;YACT;YACA,OAAO;QACT,EAAE,OAAO,OAAY;YACnB,QAAQ,KAAK,CAAC,wBAAwB;YACtC,wQAAA,CAAA,QAAK,CAAC,KAAK,CAAC,GAAG,qKAAA,CAAA,iBAAc,CAAC,YAAY,CAAC,EAAE,EAAE,MAAM,OAAO,IAAI,kBAAkB;YAClF,OAAO;QACT,SAAU;YACR,cAAc;QAChB;IACF,GAAG;QAAC;QAAc;KAAU;IAE5B,cAAc;IACd,MAAM,aAAa,CAAA,GAAA,8SAAA,CAAA,cAAW,AAAD,EAAE,OAAO,QAAgB;QACpD,cAAc;QACd,IAAI;YACF,MAAM,gBAAgB,CAAA,GAAA,mKAAA,CAAA,kBAAe,AAAD,EAAE,UAAU,UAAU;YAE1D,MAAM,WAAW,MAAM,0GAAA,CAAA,MAAG,CAAC,KAAK,CAC9B,qKAAA,CAAA,iBAAc,CAAC,MAAM,CAAC,SACtB;YAGF,IAAI,UAAU;gBACZ,wQAAA,CAAA,QAAK,CAAC,OAAO,CAAC,qKAAA,CAAA,iBAAc,CAAC,cAAc;gBAC3C,IAAI,mBAAmB,SAAS,IAAI,EAAE;oBACpC,gBAAgB,QAAQ,SAAS,IAAI;gBACvC;gBACA,IAAI,WAAW;gBACf,OAAO;YACT;YACA,OAAO;QACT,EAAE,OAAO,OAAY;YACnB,QAAQ,KAAK,CAAC,wBAAwB;YACtC,wQAAA,CAAA,QAAK,CAAC,KAAK,CAAC,GAAG,qKAAA,CAAA,iBAAc,CAAC,YAAY,CAAC,EAAE,EAAE,MAAM,OAAO,IAAI,kBAAkB;YAClF,OAAO;QACT,SAAU;YACR,cAAc;QAChB;IACF,GAAG;QAAC;QAAiB;KAAU;IAE/B,cAAc;IACd,MAAM,aAAa,CAAA,GAAA,8SAAA,CAAA,cAAW,AAAD,EAAE,OAAO;QACpC,cAAc;QACd,IAAI;YACF,MAAM,0GAAA,CAAA,MAAG,CAAC,IAAI,CAAC,qKAAA,CAAA,iBAAc,CAAC,MAAM,CAAC,KAAK,EAAE;YAE5C,wQAAA,CAAA,QAAK,CAAC,OAAO,CAAC,qKAAA,CAAA,iBAAc,CAAC,cAAc,CAAC,KAAK,SAAS;YAE1D,yBAAyB;YACzB,IAAI,iBAAiB;gBACnB,gBAAgB,KAAK,EAAE,EAAE;oBACvB,WAAW;oBACX,WAAW,IAAI,OAAO,WAAW;gBACnC;YACF;YAEA,IAAI,WAAW;YACf,OAAO;QACT,EAAE,OAAO,OAAY;YACnB,QAAQ,KAAK,CAAC,wBAAwB;YACtC,wQAAA,CAAA,QAAK,CAAC,KAAK,CAAC,qKAAA,CAAA,iBAAc,CAAC,YAAY;YACvC,OAAO;QACT,SAAU;YACR,cAAc;QAChB;IACF,GAAG;QAAC;QAAiB;KAAU;IAE/B,qBAAqB;IACrB,MAAM,mBAAmB,CAAA,GAAA,8SAAA,CAAA,cAAW,AAAD,EAAE,OAAO;QAC1C,cAAc;QACd,IAAI;YACF,MAAM,0GAAA,CAAA,MAAG,CAAC,GAAG,CAAC,qKAAA,CAAA,iBAAc,CAAC,aAAa,CAAC,KAAK,EAAE;YAElD,MAAM,YAAY,CAAC,KAAK,QAAQ;YAChC,wQAAA,CAAA,QAAK,CAAC,OAAO,CAAC,qKAAA,CAAA,iBAAc,CAAC,cAAc,CAAC,KAAK,SAAS,EAAE;YAE5D,yBAAyB;YACzB,IAAI,iBAAiB;gBACnB,gBAAgB,KAAK,EAAE,EAAE;oBACvB,UAAU;oBACV,QAAQ,YAAY,WAAW;gBACjC;YACF;YAEA,IAAI,WAAW;YACf,OAAO;QACT,EAAE,OAAO,OAAY;YACnB,QAAQ,KAAK,CAAC,+BAA+B;YAC7C,wQAAA,CAAA,QAAK,CAAC,KAAK,CAAC,qKAAA,CAAA,iBAAc,CAAC,YAAY;YACvC,OAAO;QACT,SAAU;YACR,cAAc;QAChB;IACF,GAAG;QAAC;QAAiB;KAAU;IAE/B,iBAAiB;IACjB,MAAM,gBAAgB,CAAA,GAAA,8SAAA,CAAA,cAAW,AAAD,EAAE,OAAO;QACvC,cAAc;QACd,IAAI;YACF,MAAM,WAAW,MAAM,0GAAA,CAAA,MAAG,CAAC,IAAI,CAAU,qKAAA,CAAA,iBAAc,CAAC,SAAS,CAAC,KAAK,EAAE;YAEzE,IAAI,UAAU;gBACZ,wQAAA,CAAA,QAAK,CAAC,OAAO,CAAC,qKAAA,CAAA,iBAAc,CAAC,iBAAiB,CAAC,KAAK,SAAS;gBAC7D,IAAI,cAAc;oBAChB,aAAa;gBACf;gBACA,IAAI,WAAW;gBACf,OAAO;YACT;YACA,OAAO;QACT,EAAE,OAAO,OAAY;YACnB,QAAQ,KAAK,CAAC,2BAA2B;YACzC,wQAAA,CAAA,QAAK,CAAC,KAAK,CAAC,qKAAA,CAAA,iBAAc,CAAC,eAAe;YAC1C,OAAO;QACT,SAAU;YACR,cAAc;QAChB;IACF,GAAG;QAAC;QAAc;KAAU;IAE5B,oBAAoB;IACpB,MAAM,kBAAkB,CAAA,GAAA,8SAAA,CAAA,cAAW,AAAD,EAAE,OAAO;QACzC,cAAc;QACd,IAAI;YACF,+BAA+B;YAC/B,MAAM,iBAAiB,MAAM,GAAG,CAAC,CAAA,OAC/B,0GAAA,CAAA,MAAG,CAAC,IAAI,CAAC,qKAAA,CAAA,iBAAc,CAAC,MAAM,CAAC,KAAK,EAAE;YAGxC,MAAM,QAAQ,GAAG,CAAC;YAElB,wQAAA,CAAA,QAAK,CAAC,OAAO,CAAC,qKAAA,CAAA,iBAAc,CAAC,mBAAmB,CAAC,MAAM,MAAM;YAE7D,yBAAyB;YACzB,IAAI,iBAAiB;gBACnB,MAAM,OAAO,CAAC,CAAA,OAAQ,gBAAgB,KAAK,EAAE;YAC/C;YAEA,IAAI,WAAW;YACf,OAAO;QACT,EAAE,OAAO,OAAY;YACnB,QAAQ,KAAK,CAAC,8BAA8B;YAC5C,wQAAA,CAAA,QAAK,CAAC,KAAK,CAAC,qKAAA,CAAA,iBAAc,CAAC,iBAAiB;YAC5C,OAAO;QACT,SAAU;YACR,cAAc;QAChB;IACF,GAAG;QAAC;QAAiB;KAAU;IAE/B,eAAe;IACf,MAAM,cAAc,CAAA,GAAA,8SAAA,CAAA,cAAW,AAAD,EAAE,OAAO,SAAyB,KAAK;QACnE,cAAc;QACd,IAAI;YACF,MAAM,0GAAA,CAAA,MAAG,CAAC,YAAY,CACpB,GAAG,qKAAA,CAAA,iBAAc,CAAC,MAAM,CAAC,QAAQ,EAAE,QAAQ,EAC3C,QACA,CAAC,aAAa,EAAE,QAAQ;YAE1B,wQAAA,CAAA,QAAK,CAAC,OAAO,CAAC,qKAAA,CAAA,iBAAc,CAAC,cAAc,CAAC;YAC5C,OAAO;QACT,EAAE,OAAO,OAAY;YACnB,QAAQ,KAAK,CAAC,CAAC,yBAAyB,EAAE,OAAO,CAAC,CAAC,EAAE;YACrD,wQAAA,CAAA,QAAK,CAAC,KAAK,CAAC,qKAAA,CAAA,iBAAc,CAAC,YAAY,CAAC;YACxC,OAAO;QACT,SAAU;YACR,cAAc;QAChB;IACF,GAAG,EAAE;IAEL,eAAe;IACf,MAAM,cAAc,CAAA,GAAA,8SAAA,CAAA,cAAW,AAAD,EAAE;QAC9B,OAAO,IAAI,QAAQ,CAAC;YAClB,IAAI;gBACF,oBAAoB;gBACpB,MAAM,QAAQ,SAAS,aAAa,CAAC;gBACrC,MAAM,IAAI,GAAG;gBACb,MAAM,MAAM,GAAG;gBAEf,yCAAyC;gBACzC,MAAM,QAAQ,GAAG,OAAO;oBACtB,MAAM,SAAS,EAAE,MAAM;oBACvB,IAAI,CAAC,OAAO,KAAK,IAAI,OAAO,KAAK,CAAC,MAAM,KAAK,GAAG;wBAC9C,QAAQ;wBACR;oBACF;oBAEA,MAAM,OAAO,OAAO,KAAK,CAAC,EAAE;oBAC5B,MAAM,WAAW,KAAK,IAAI,CAAC,QAAQ,CAAC,UAAU,QAAQ;oBAEtD,cAAc;oBACd,IAAI;wBACF,WAAW;wBACX,MAAM,cAAc,MAAM,KAAK,IAAI;wBACnC,IAAI,YAAmB,EAAE;wBAEzB,wBAAwB;wBACxB,IAAI,aAAa,OAAO;4BACtB,YAAY,CAAA,GAAA,mKAAA,CAAA,WAAQ,AAAD,EAAE;wBACvB,OAAO;4BACL,YAAY,KAAK,KAAK,CAAC;wBACzB;wBAEA,qCAAqC;wBACrC,MAAM,0GAAA,CAAA,MAAG,CAAC,IAAI,CAAC,qKAAA,CAAA,iBAAc,CAAC,WAAW,EAAE;wBAE3C,wQAAA,CAAA,QAAK,CAAC,OAAO,CAAC,qKAAA,CAAA,iBAAc,CAAC,cAAc,CAAC,UAAU,MAAM;wBAC5D,IAAI,WAAW;wBACf,QAAQ;oBACV,EAAE,OAAO,OAAY;wBACnB,QAAQ,KAAK,CAAC,0BAA0B;wBACxC,wQAAA,CAAA,QAAK,CAAC,KAAK,CAAC,qKAAA,CAAA,iBAAc,CAAC,YAAY;wBACvC,QAAQ;oBACV,SAAU;wBACR,cAAc;oBAChB;gBACF;gBAEA,kCAAkC;gBAClC,MAAM,KAAK;YACb,EAAE,OAAO,OAAY;gBACnB,QAAQ,KAAK,CAAC,4BAA4B;gBAC1C,wQAAA,CAAA,QAAK,CAAC,KAAK,CAAC,qKAAA,CAAA,iBAAc,CAAC,YAAY;gBACvC,QAAQ;YACV;QACF;IACF,GAAG;QAAC;KAAU;IAEd,OAAO;QACL,QAAQ;QACR;QAEA,UAAU;QACV;QACA;QACA;QACA;QACA;QACA;QACA;QACA;IACF;AACF", "debugId": null}}, {"offset": {"line": 861, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/bloomgoo/gold-exchange-platform/frontend/components/common/user/user-hover-card.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';\r\nimport { HoverCard, HoverCardContent, HoverCardTrigger } from '@/components/ui/hover-card';\r\nimport { User } from '@/components/common/admin/users/type/user';\r\nimport { Badge } from '@/components/ui/badge';\r\nimport { format } from 'date-fns';\r\nimport { vi } from 'date-fns/locale';\r\nimport { Mail } from 'lucide-react';\r\n\r\ninterface UserHoverCardProps {\r\n  user?: Partial<User> | null;\r\n  userId?: string;\r\n  showAvatar?: boolean;\r\n  size?: 'sm' | 'md' | 'lg';\r\n  children: React.ReactNode;\r\n}\r\n\r\n/**\r\n * Component hiển thị thông tin người dùng khi hover\r\n * @param user Thông tin người dùng\r\n * @param userId ID người dùng (nếu không có thông tin đầy đủ)\r\n * @param showAvatar Có hiển thị avatar không\r\n * @param size Kích thước hiển thị (sm, md, lg)\r\n * @param children Nội dung hiển thị khi không hover\r\n */\r\nexport function UserHoverCard({ user, userId, showAvatar = true, size = 'md', children }: UserHoverCardProps) {\r\n  // Nếu không có thông tin người dùng và không có userId, hiển thị children\r\n  if (!user && !userId) {\r\n    return <>{children}</>;\r\n  }\r\n\r\n  // Lấy ID từ user hoặc từ userId\r\n  const id = user?.id || userId;\r\n\r\n  // Xác định kích thước avatar dựa trên size\r\n  const avatarSize = {\r\n    sm: 'size-5',\r\n    md: 'size-6',\r\n    lg: 'size-8',\r\n  }[size];\r\n\r\n  // Hiển thị badge trạng thái người dùng\r\n  const getStatusBadge = (isActive?: boolean) => {\r\n    if (isActive === undefined) return null;\r\n    return (\r\n      <Badge variant={isActive ? 'default' : 'secondary'} className=\"ml-2\">\r\n        {isActive ? 'Hoạt động' : 'Vô hiệu'}\r\n      </Badge>\r\n    );\r\n  };\r\n\r\n  // Format ngày tháng\r\n  const formatDate = (date?: string | Date) => {\r\n    if (!date) return 'N/A';\r\n    try {\r\n      const dateObj = typeof date === 'string' ? new Date(date) : date;\r\n      return format(dateObj, 'dd/MM/yyyy HH:mm', { locale: vi });\r\n    } catch (error) {\r\n      return 'N/A';\r\n    }\r\n  };\r\n\r\n  return (\r\n    <HoverCard>\r\n      <HoverCardTrigger asChild>\r\n        <div className=\"flex items-center gap-2 cursor-pointer\">\r\n          {showAvatar && (\r\n            <Avatar className={`shrink-0 ${avatarSize}`}>\r\n              <AvatarImage\r\n                src={user?.avatar || `https://api.dicebear.com/9.x/initials/svg?seed=${user?.fullName || user?.username || id}`}\r\n                alt={user?.fullName || user?.username || 'User'}\r\n              />\r\n              <AvatarFallback>\r\n                {user?.fullName\r\n                  ? user.fullName[0].toUpperCase()\r\n                  : (user?.username ? user.username[0].toUpperCase() : 'U')}\r\n              </AvatarFallback>\r\n            </Avatar>\r\n          )}\r\n          {children}\r\n        </div>\r\n      </HoverCardTrigger>\r\n      <HoverCardContent className=\"w-80 p-4\">\r\n        <div className=\"flex flex-col gap-4\">\r\n          {/* Header với thông tin cơ bản */}\r\n          <div className=\"flex items-center gap-3\">\r\n            <Avatar className=\"size-10 shrink-0\">\r\n              <AvatarImage\r\n                src={user?.avatar || `https://api.dicebear.com/9.x/initials/svg?seed=${user?.fullName || user?.username || id}`}\r\n                alt={user?.fullName || user?.username || 'User'}\r\n              />\r\n              <AvatarFallback>\r\n                {user?.fullName\r\n                  ? user.fullName[0].toUpperCase()\r\n                  : (user?.username ? user.username[0].toUpperCase() : 'U')}\r\n              </AvatarFallback>\r\n            </Avatar>\r\n            <div>\r\n              <div className=\"font-medium flex items-center\">\r\n                {user?.fullName || user?.username || id?.substring(0, 8)}\r\n                {getStatusBadge(user?.isActive)}\r\n              </div>\r\n              {user?.email && (\r\n                <div className=\"text-sm text-muted-foreground flex items-center gap-1 mt-1\">\r\n                  <Mail className=\"size-3\" />\r\n                  {user.email}\r\n                </div>\r\n              )}\r\n            </div>\r\n          </div>\r\n\r\n          {/* Thông tin chi tiết */}\r\n          <div className=\"grid grid-cols-2 gap-3 text-sm\">\r\n            {user?.username && (\r\n              <div>\r\n                <div className=\"text-muted-foreground\">Tên đăng nhập</div>\r\n                <div>{user.username}</div>\r\n              </div>\r\n            )}\r\n            {user?.phone && (\r\n              <div>\r\n                <div className=\"text-muted-foreground\">Số điện thoại</div>\r\n                <div>{user.phone}</div>\r\n              </div>\r\n            )}\r\n            {user?.address && (\r\n              <div className=\"col-span-2\">\r\n                <div className=\"text-muted-foreground\">Địa chỉ</div>\r\n                <div>{user.address}</div>\r\n              </div>\r\n            )}\r\n            {user?.bio && (\r\n              <div className=\"col-span-2\">\r\n                <div className=\"text-muted-foreground\">Giới thiệu</div>\r\n                <div>{user.bio}</div>\r\n              </div>\r\n            )}\r\n            {user?.birthday && (\r\n              <div>\r\n                <div className=\"text-muted-foreground\">Ngày sinh</div>\r\n                <div>{formatDate(user.birthday)}</div>\r\n              </div>\r\n            )}\r\n            {user?.createdAt && (\r\n              <div>\r\n                <div className=\"text-muted-foreground\">Ngày tạo</div>\r\n                <div>{formatDate(user.createdAt)}</div>\r\n              </div>\r\n            )}\r\n            {user?.role && (\r\n              <div>\r\n                <div className=\"text-muted-foreground\">Vai trò</div>\r\n                <div>{Array.isArray(user.role) ? user.role.join(', ') : user.role}</div>\r\n              </div>\r\n            )}\r\n          </div>\r\n        </div>\r\n      </HoverCardContent>\r\n    </HoverCard>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAEA;AACA;AACA;AACA;AARA;;;;;;;;AA0BO,SAAS,cAAc,EAAE,IAAI,EAAE,MAAM,EAAE,aAAa,IAAI,EAAE,OAAO,IAAI,EAAE,QAAQ,EAAsB;IAC1G,0EAA0E;IAC1E,IAAI,CAAC,QAAQ,CAAC,QAAQ;QACpB,qBAAO;sBAAG;;IACZ;IAEA,gCAAgC;IAChC,MAAM,KAAK,MAAM,MAAM;IAEvB,2CAA2C;IAC3C,MAAM,aAAa;QACjB,IAAI;QACJ,IAAI;QACJ,IAAI;IACN,CAAC,CAAC,KAAK;IAEP,uCAAuC;IACvC,MAAM,iBAAiB,CAAC;QACtB,IAAI,aAAa,WAAW,OAAO;QACnC,qBACE,uVAAC,0HAAA,CAAA,QAAK;YAAC,SAAS,WAAW,YAAY;YAAa,WAAU;sBAC3D,WAAW,cAAc;;;;;;IAGhC;IAEA,oBAAoB;IACpB,MAAM,aAAa,CAAC;QAClB,IAAI,CAAC,MAAM,OAAO;QAClB,IAAI;YACF,MAAM,UAAU,OAAO,SAAS,WAAW,IAAI,KAAK,QAAQ;YAC5D,OAAO,CAAA,GAAA,6MAAA,CAAA,SAAM,AAAD,EAAE,SAAS,oBAAoB;gBAAE,QAAQ,mMAAA,CAAA,KAAE;YAAC;QAC1D,EAAE,OAAO,OAAO;YACd,OAAO;QACT;IACF;IAEA,qBACE,uVAAC,kIAAA,CAAA,YAAS;;0BACR,uVAAC,kIAAA,CAAA,mBAAgB;gBAAC,OAAO;0BACvB,cAAA,uVAAC;oBAAI,WAAU;;wBACZ,4BACC,uVAAC,2HAAA,CAAA,SAAM;4BAAC,WAAW,CAAC,SAAS,EAAE,YAAY;;8CACzC,uVAAC,2HAAA,CAAA,cAAW;oCACV,KAAK,MAAM,UAAU,CAAC,+CAA+C,EAAE,MAAM,YAAY,MAAM,YAAY,IAAI;oCAC/G,KAAK,MAAM,YAAY,MAAM,YAAY;;;;;;8CAE3C,uVAAC,2HAAA,CAAA,iBAAc;8CACZ,MAAM,WACH,KAAK,QAAQ,CAAC,EAAE,CAAC,WAAW,KAC3B,MAAM,WAAW,KAAK,QAAQ,CAAC,EAAE,CAAC,WAAW,KAAK;;;;;;;;;;;;wBAI5D;;;;;;;;;;;;0BAGL,uVAAC,kIAAA,CAAA,mBAAgB;gBAAC,WAAU;0BAC1B,cAAA,uVAAC;oBAAI,WAAU;;sCAEb,uVAAC;4BAAI,WAAU;;8CACb,uVAAC,2HAAA,CAAA,SAAM;oCAAC,WAAU;;sDAChB,uVAAC,2HAAA,CAAA,cAAW;4CACV,KAAK,MAAM,UAAU,CAAC,+CAA+C,EAAE,MAAM,YAAY,MAAM,YAAY,IAAI;4CAC/G,KAAK,MAAM,YAAY,MAAM,YAAY;;;;;;sDAE3C,uVAAC,2HAAA,CAAA,iBAAc;sDACZ,MAAM,WACH,KAAK,QAAQ,CAAC,EAAE,CAAC,WAAW,KAC3B,MAAM,WAAW,KAAK,QAAQ,CAAC,EAAE,CAAC,WAAW,KAAK;;;;;;;;;;;;8CAG3D,uVAAC;;sDACC,uVAAC;4CAAI,WAAU;;gDACZ,MAAM,YAAY,MAAM,YAAY,IAAI,UAAU,GAAG;gDACrD,eAAe,MAAM;;;;;;;wCAEvB,MAAM,uBACL,uVAAC;4CAAI,WAAU;;8DACb,uVAAC,sRAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;gDACf,KAAK,KAAK;;;;;;;;;;;;;;;;;;;sCAOnB,uVAAC;4BAAI,WAAU;;gCACZ,MAAM,0BACL,uVAAC;;sDACC,uVAAC;4CAAI,WAAU;sDAAwB;;;;;;sDACvC,uVAAC;sDAAK,KAAK,QAAQ;;;;;;;;;;;;gCAGtB,MAAM,uBACL,uVAAC;;sDACC,uVAAC;4CAAI,WAAU;sDAAwB;;;;;;sDACvC,uVAAC;sDAAK,KAAK,KAAK;;;;;;;;;;;;gCAGnB,MAAM,yBACL,uVAAC;oCAAI,WAAU;;sDACb,uVAAC;4CAAI,WAAU;sDAAwB;;;;;;sDACvC,uVAAC;sDAAK,KAAK,OAAO;;;;;;;;;;;;gCAGrB,MAAM,qBACL,uVAAC;oCAAI,WAAU;;sDACb,uVAAC;4CAAI,WAAU;sDAAwB;;;;;;sDACvC,uVAAC;sDAAK,KAAK,GAAG;;;;;;;;;;;;gCAGjB,MAAM,0BACL,uVAAC;;sDACC,uVAAC;4CAAI,WAAU;sDAAwB;;;;;;sDACvC,uVAAC;sDAAK,WAAW,KAAK,QAAQ;;;;;;;;;;;;gCAGjC,MAAM,2BACL,uVAAC;;sDACC,uVAAC;4CAAI,WAAU;sDAAwB;;;;;;sDACvC,uVAAC;sDAAK,WAAW,KAAK,SAAS;;;;;;;;;;;;gCAGlC,MAAM,sBACL,uVAAC;;sDACC,uVAAC;4CAAI,WAAU;sDAAwB;;;;;;sDACvC,uVAAC;sDAAK,MAAM,OAAO,CAAC,KAAK,IAAI,IAAI,KAAK,IAAI,CAAC,IAAI,CAAC,QAAQ,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQjF", "debugId": null}}, {"offset": {"line": 1232, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/bloomgoo/gold-exchange-platform/frontend/components/common/admin/ebanks/table/bank-table-cells.tsx"], "sourcesContent": ["/**\r\n * BankTableCells - Các cell components cho table\r\n */\r\n\r\nimport { ImageIcon, User } from 'lucide-react';\r\nimport { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';\r\nimport { Badge } from '@/components/ui/badge';\r\nimport { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';\r\nimport { UserHoverCard } from '@/components/common/user/user-hover-card';\r\nimport { DateTimeDisplay } from '@/components/ui/date-time-display';\r\nimport type { BankDto } from '../types';\r\nimport { getBankAvatarUrl, getBankAvatarFallback, getStatusVariant } from '../utils/bank-helpers';\r\n\r\n// Bank name cell với avatar\r\nexport function BankNameCell({ bank }: { bank: BankDto }) {\r\n  return (\r\n    <div className=\"flex items-center gap-2\">\r\n      <div className=\"relative\">\r\n        <Avatar className=\"size-8 shrink-0\">\r\n          <AvatarImage \r\n            src={getBankAvatarUrl(bank.brandName, bank.logoPath)} \r\n            alt={bank.brandName} \r\n          />\r\n          <AvatarFallback>{getBankAvatarFallback(bank.brandName)}</AvatarFallback>\r\n        </Avatar>\r\n        <span\r\n          className={`border-background absolute -end-0.5 -bottom-0.5 size-2.5 rounded-full border-2 ${\r\n            bank.isActive ? 'bg-green-500' : 'bg-gray-500'\r\n          }`}\r\n        >\r\n          <span className=\"sr-only\">{bank.isActive ? 'Active' : 'Inactive'}</span>\r\n        </span>\r\n      </div>\r\n      <div className=\"flex flex-col items-start overflow-hidden\">\r\n        <span className=\"text-sm font-medium truncate w-full\">{bank.brandName}</span>\r\n        <span className=\"text-xs text-muted-foreground truncate w-full\">\r\n          Mã: {bank.code}\r\n        </span>\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n\r\n// Text cell với tooltip\r\nexport function TextCell({ value, maxWidth = 200 }: { value: string; maxWidth?: number }) {\r\n  if (!value) return <span className=\"text-xs text-muted-foreground\">---</span>;\r\n\r\n  return (\r\n    <TooltipProvider>\r\n      <Tooltip>\r\n        <TooltipTrigger asChild>\r\n          <span \r\n            className={`truncate block text-sm`}\r\n            style={{ maxWidth: `${maxWidth}px` }}\r\n          >\r\n            {value}\r\n          </span>\r\n        </TooltipTrigger>\r\n        <TooltipContent>\r\n          <p>{value}</p>\r\n        </TooltipContent>\r\n      </Tooltip>\r\n    </TooltipProvider>\r\n  );\r\n}\r\n\r\n// Image cell với preview\r\nexport function ImageCell({ url, alt }: { url?: string; alt: string }) {\r\n  if (!url) return <span className=\"text-xs text-muted-foreground\">---</span>;\r\n\r\n  return (\r\n    <div className=\"flex items-center gap-2\">\r\n      <ImageIcon className=\"h-3.5 w-3.5 text-muted-foreground\" />\r\n      <TooltipProvider>\r\n        <Tooltip>\r\n          <TooltipTrigger asChild>\r\n            <a \r\n              href={url} \r\n              target=\"_blank\" \r\n              rel=\"noopener noreferrer\" \r\n              className=\"text-blue-500 hover:underline truncate max-w-[120px] inline-block text-sm\"\r\n            >\r\n              Xem {alt}\r\n            </a>\r\n          </TooltipTrigger>\r\n          <TooltipContent side=\"bottom\">\r\n            <div className=\"flex flex-col items-center gap-2\">\r\n              <img \r\n                src={url} \r\n                alt={alt} \r\n                className=\"max-w-[200px] max-h-[100px] object-contain\" \r\n              />\r\n              <p className=\"text-xs\">{url}</p>\r\n            </div>\r\n          </TooltipContent>\r\n        </Tooltip>\r\n      </TooltipProvider>\r\n    </div>\r\n  );\r\n}\r\n\r\n// Status cell\r\nexport function StatusCell({ isActive }: { isActive: boolean }) {\r\n  return (\r\n    <Badge variant={getStatusVariant(isActive)} className=\"whitespace-nowrap\">\r\n      {isActive ? 'Kích hoạt' : 'Vô hiệu hóa'}\r\n    </Badge>\r\n  );\r\n}\r\n\r\n// Date cell\r\nexport function DateCell({ date }: { date?: string }) {\r\n  if (!date) return <span className=\"text-xs text-muted-foreground\">---</span>;\r\n\r\n  return (\r\n    <DateTimeDisplay\r\n      date={date}\r\n      className=\"text-sm\"\r\n      timeClassName=\"text-xs text-muted-foreground\"\r\n    />\r\n  );\r\n}\r\n\r\n// User cell\r\nexport function UserCell({ \r\n  user, \r\n  userId, \r\n  fallbackText = \"---\" \r\n}: { \r\n  user?: BankDto['creator']; \r\n  userId?: string; \r\n  fallbackText?: string;\r\n}) {\r\n  // Nếu có thông tin user object\r\n  if (user && typeof user === 'object') {\r\n    return (\r\n      <UserHoverCard user={user} showAvatar={true} size=\"sm\">\r\n        <div className=\"max-w-[120px] overflow-hidden\">\r\n          <div className=\"text-sm truncate\">\r\n            {user.fullName || user.username || 'User'}\r\n          </div>\r\n          <div className=\"text-xs text-muted-foreground truncate\">\r\n            {user.email || ''}\r\n          </div>\r\n        </div>\r\n      </UserHoverCard>\r\n    );\r\n  }\r\n\r\n  // Nếu chỉ có ID\r\n  if (userId && typeof userId === 'string') {\r\n    return (\r\n      <UserHoverCard userId={userId} showAvatar={true} size=\"sm\">\r\n        <div className=\"flex items-center gap-1\">\r\n          <User className=\"h-4 w-4 text-blue-500\" />\r\n          <span className=\"text-sm text-muted-foreground\">\r\n            {userId.substring(0, 8)}...\r\n          </span>\r\n        </div>\r\n      </UserHoverCard>\r\n    );\r\n  }\r\n\r\n  // Fallback\r\n  return <span className=\"text-xs text-muted-foreground\">{fallbackText}</span>;\r\n}\r\n"], "names": [], "mappings": "AAAA;;CAEC;;;;;;;;;AAED;AAAA;AACA;AACA;AACA;AACA;AACA;AAEA;;;;;;;;;AAGO,SAAS,aAAa,EAAE,IAAI,EAAqB;IACtD,qBACE,uVAAC;QAAI,WAAU;;0BACb,uVAAC;gBAAI,WAAU;;kCACb,uVAAC,2HAAA,CAAA,SAAM;wBAAC,WAAU;;0CAChB,uVAAC,2HAAA,CAAA,cAAW;gCACV,KAAK,CAAA,GAAA,mKAAA,CAAA,mBAAgB,AAAD,EAAE,KAAK,SAAS,EAAE,KAAK,QAAQ;gCACnD,KAAK,KAAK,SAAS;;;;;;0CAErB,uVAAC,2HAAA,CAAA,iBAAc;0CAAE,CAAA,GAAA,mKAAA,CAAA,wBAAqB,AAAD,EAAE,KAAK,SAAS;;;;;;;;;;;;kCAEvD,uVAAC;wBACC,WAAW,CAAC,+EAA+E,EACzF,KAAK,QAAQ,GAAG,iBAAiB,eACjC;kCAEF,cAAA,uVAAC;4BAAK,WAAU;sCAAW,KAAK,QAAQ,GAAG,WAAW;;;;;;;;;;;;;;;;;0BAG1D,uVAAC;gBAAI,WAAU;;kCACb,uVAAC;wBAAK,WAAU;kCAAuC,KAAK,SAAS;;;;;;kCACrE,uVAAC;wBAAK,WAAU;;4BAAgD;4BACzD,KAAK,IAAI;;;;;;;;;;;;;;;;;;;AAKxB;AAGO,SAAS,SAAS,EAAE,KAAK,EAAE,WAAW,GAAG,EAAwC;IACtF,IAAI,CAAC,OAAO,qBAAO,uVAAC;QAAK,WAAU;kBAAgC;;;;;;IAEnE,qBACE,uVAAC,4HAAA,CAAA,kBAAe;kBACd,cAAA,uVAAC,4HAAA,CAAA,UAAO;;8BACN,uVAAC,4HAAA,CAAA,iBAAc;oBAAC,OAAO;8BACrB,cAAA,uVAAC;wBACC,WAAW,CAAC,sBAAsB,CAAC;wBACnC,OAAO;4BAAE,UAAU,GAAG,SAAS,EAAE,CAAC;wBAAC;kCAElC;;;;;;;;;;;8BAGL,uVAAC,4HAAA,CAAA,iBAAc;8BACb,cAAA,uVAAC;kCAAG;;;;;;;;;;;;;;;;;;;;;;AAKd;AAGO,SAAS,UAAU,EAAE,GAAG,EAAE,GAAG,EAAiC;IACnE,IAAI,CAAC,KAAK,qBAAO,uVAAC;QAAK,WAAU;kBAAgC;;;;;;IAEjE,qBACE,uVAAC;QAAI,WAAU;;0BACb,uVAAC,4RAAA,CAAA,YAAS;gBAAC,WAAU;;;;;;0BACrB,uVAAC,4HAAA,CAAA,kBAAe;0BACd,cAAA,uVAAC,4HAAA,CAAA,UAAO;;sCACN,uVAAC,4HAAA,CAAA,iBAAc;4BAAC,OAAO;sCACrB,cAAA,uVAAC;gCACC,MAAM;gCACN,QAAO;gCACP,KAAI;gCACJ,WAAU;;oCACX;oCACM;;;;;;;;;;;;sCAGT,uVAAC,4HAAA,CAAA,iBAAc;4BAAC,MAAK;sCACnB,cAAA,uVAAC;gCAAI,WAAU;;kDACb,uVAAC;wCACC,KAAK;wCACL,KAAK;wCACL,WAAU;;;;;;kDAEZ,uVAAC;wCAAE,WAAU;kDAAW;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOtC;AAGO,SAAS,WAAW,EAAE,QAAQ,EAAyB;IAC5D,qBACE,uVAAC,0HAAA,CAAA,QAAK;QAAC,SAAS,CAAA,GAAA,mKAAA,CAAA,mBAAgB,AAAD,EAAE;QAAW,WAAU;kBACnD,WAAW,cAAc;;;;;;AAGhC;AAGO,SAAS,SAAS,EAAE,IAAI,EAAqB;IAClD,IAAI,CAAC,MAAM,qBAAO,uVAAC;QAAK,WAAU;kBAAgC;;;;;;IAElE,qBACE,uVAAC,4IAAA,CAAA,kBAAe;QACd,MAAM;QACN,WAAU;QACV,eAAc;;;;;;AAGpB;AAGO,SAAS,SAAS,EACvB,IAAI,EACJ,MAAM,EACN,eAAe,KAAK,EAKrB;IACC,+BAA+B;IAC/B,IAAI,QAAQ,OAAO,SAAS,UAAU;QACpC,qBACE,uVAAC,sJAAA,CAAA,gBAAa;YAAC,MAAM;YAAM,YAAY;YAAM,MAAK;sBAChD,cAAA,uVAAC;gBAAI,WAAU;;kCACb,uVAAC;wBAAI,WAAU;kCACZ,KAAK,QAAQ,IAAI,KAAK,QAAQ,IAAI;;;;;;kCAErC,uVAAC;wBAAI,WAAU;kCACZ,KAAK,KAAK,IAAI;;;;;;;;;;;;;;;;;IAKzB;IAEA,gBAAgB;IAChB,IAAI,UAAU,OAAO,WAAW,UAAU;QACxC,qBACE,uVAAC,sJAAA,CAAA,gBAAa;YAAC,QAAQ;YAAQ,YAAY;YAAM,MAAK;sBACpD,cAAA,uVAAC;gBAAI,WAAU;;kCACb,uVAAC,sRAAA,CAAA,OAAI;wBAAC,WAAU;;;;;;kCAChB,uVAAC;wBAAK,WAAU;;4BACb,OAAO,SAAS,CAAC,GAAG;4BAAG;;;;;;;;;;;;;;;;;;IAKlC;IAEA,WAAW;IACX,qBAAO,uVAAC;QAAK,WAAU;kBAAiC;;;;;;AAC1D", "debugId": null}}, {"offset": {"line": 1618, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/bloomgoo/gold-exchange-platform/frontend/components/common/admin/ebanks/table/bank-table-actions.tsx"], "sourcesContent": ["'use client';\r\n/**\r\n * BankTableActions - Component dropdown actions cho table\r\n */\r\n\r\nimport { useState } from 'react';\r\nimport { Row } from '@tanstack/react-table';\r\nimport { <PERSON><PERSON>, Edit, Eye, Trash2, UserCheck, Ban } from 'lucide-react';\r\nimport { But<PERSON> } from '@/components/ui/button';\r\nimport {\r\n  Dialog,\r\n  DialogContent,\r\n  DialogDescription,\r\n  DialogFooter,\r\n  DialogHeader,\r\n  DialogTitle,\r\n} from '@/components/ui/dialog';\r\nimport {\r\n  DropdownMenu,\r\n  DropdownMenuContent,\r\n  DropdownMenuItem,\r\n  DropdownMenuSeparator,\r\n  DropdownMenuShortcut,\r\n  DropdownMenuTrigger,\r\n} from '@/components/ui/dropdown-menu';\r\nimport { IconDotsVertical } from '@tabler/icons-react';\r\nimport type { BankDto } from '../types';\r\n\r\ninterface BankTableActionsProps {\r\n  row: Row<BankDto>;\r\n  onViewDetail: (bank: BankDto) => void;\r\n  onDelete: (bank: BankDto) => void;\r\n  onEdit: (bank: BankDto) => void;\r\n  onToggleStatus: (bank: BankDto) => void;\r\n  onDuplicate?: (bank: BankDto) => void;\r\n}\r\n\r\nexport function BankTableActions({\r\n  row,\r\n  onViewDetail,\r\n  onDelete,\r\n  onEdit,\r\n  onToggleStatus,\r\n  onDuplicate,\r\n}: BankTableActionsProps) {\r\n  const [showDeleteDialog, setShowDeleteDialog] = useState(false);\r\n  const bank = row.original;\r\n\r\n  const handleDelete = () => {\r\n    onDelete(bank);\r\n    setShowDeleteDialog(false);\r\n  };\r\n\r\n  return (\r\n    <>\r\n      {/* Dialog xác nhận xóa */}\r\n      <Dialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>\r\n        <DialogContent>\r\n          <DialogHeader>\r\n            <DialogTitle>Xác nhận xóa</DialogTitle>\r\n            <DialogDescription>\r\n              Bạn có chắc chắn muốn xóa ngân hàng{' '}\r\n              <span className=\"font-medium\">{bank.brandName}</span>?\r\n              <p className=\"mt-2\">Hành động này không thể hoàn tác.</p>\r\n            </DialogDescription>\r\n          </DialogHeader>\r\n          <DialogFooter>\r\n            <Button variant=\"outline\" onClick={() => setShowDeleteDialog(false)}>\r\n              Hủy\r\n            </Button>\r\n            <Button variant=\"destructive\" onClick={handleDelete}>\r\n              Xóa\r\n            </Button>\r\n          </DialogFooter>\r\n        </DialogContent>\r\n      </Dialog>\r\n\r\n      {/* Actions dropdown */}\r\n      <div className=\"flex justify-end px-1 sticky-action-cell h-full items-center\">\r\n        <DropdownMenu>\r\n          <DropdownMenuTrigger asChild>\r\n            <Button\r\n              variant=\"ghost\"\r\n              size=\"sm\"\r\n              className=\"flex h-6 w-6 p-0 data-[state=open]:bg-muted transition-colors hover:bg-muted\"\r\n            >\r\n              <IconDotsVertical className=\"h-3 w-3\" />\r\n              <span className=\"sr-only\">Mở menu</span>\r\n            </Button>\r\n          </DropdownMenuTrigger>\r\n          <DropdownMenuContent align=\"end\" className=\"p-1\">\r\n            <DropdownMenuItem onClick={() => onViewDetail(bank)}>\r\n              <Eye className=\"mr-2 h-3.5 w-3.5\" />\r\n              <span className=\"flex-1 text-sm\">Chi tiết</span>\r\n              <DropdownMenuShortcut className=\"text-sm\">⌘V</DropdownMenuShortcut>\r\n            </DropdownMenuItem>\r\n            \r\n            <DropdownMenuItem onClick={() => onEdit(bank)}>\r\n              <Edit className=\"mr-2 h-3.5 w-3.5\" />\r\n              <span className=\"flex-1 text-sm\">Chỉnh sửa</span>\r\n              <DropdownMenuShortcut className=\"text-sm\">⌘E</DropdownMenuShortcut>\r\n            </DropdownMenuItem>\r\n            \r\n            <DropdownMenuItem onClick={() => onToggleStatus(bank)}>\r\n              {bank.status === 'ACTIVE' ? (\r\n                <>\r\n                  <Ban className=\"mr-2 h-3.5 w-3.5 text-destructive\" />\r\n                  <span className=\"flex-1 text-sm text-destructive\">Vô hiệu hóa</span>\r\n                  <DropdownMenuShortcut className=\"text-sm text-destructive\">\r\n                    ⌘D\r\n                  </DropdownMenuShortcut>\r\n                </>\r\n              ) : (\r\n                <>\r\n                  <UserCheck className=\"mr-2 h-3.5 w-3.5 text-green-500\" />\r\n                  <span className=\"flex-1 text-sm text-green-500\">Kích hoạt</span>\r\n                  <DropdownMenuShortcut className=\"text-sm text-green-500\">\r\n                    ⌘A\r\n                  </DropdownMenuShortcut>\r\n                </>\r\n              )}\r\n            </DropdownMenuItem>\r\n            \r\n            {onDuplicate && (\r\n              <DropdownMenuItem onClick={() => onDuplicate(bank)}>\r\n                <Copy className=\"mr-2 h-3.5 w-3.5\" />\r\n                <span className=\"flex-1 text-sm\">Nhân bản</span>\r\n                <DropdownMenuShortcut className=\"text-sm\">⌘C</DropdownMenuShortcut>\r\n              </DropdownMenuItem>\r\n            )}\r\n            \r\n            <DropdownMenuSeparator />\r\n            \r\n            <DropdownMenuItem\r\n              onClick={() => setShowDeleteDialog(true)}\r\n              className=\"text-destructive focus:text-destructive\"\r\n            >\r\n              <Trash2 className=\"mr-2 h-3.5 w-3.5 text-destructive\" />\r\n              <span className=\"flex-1 text-sm text-destructive\">Xóa</span>\r\n              <DropdownMenuShortcut className=\"text-sm text-destructive\">\r\n                ⌫\r\n              </DropdownMenuShortcut>\r\n            </DropdownMenuItem>\r\n          </DropdownMenuContent>\r\n        </DropdownMenu>\r\n      </div>\r\n    </>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AACA;;CAEC,GAED;AAEA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AAQA;AAQA;AAzBA;;;;;;;;AAqCO,SAAS,iBAAiB,EAC/B,GAAG,EACH,YAAY,EACZ,QAAQ,EACR,MAAM,EACN,cAAc,EACd,WAAW,EACW;IACtB,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,8SAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,OAAO,IAAI,QAAQ;IAEzB,MAAM,eAAe;QACnB,SAAS;QACT,oBAAoB;IACtB;IAEA,qBACE;;0BAEE,uVAAC,2HAAA,CAAA,SAAM;gBAAC,MAAM;gBAAkB,cAAc;0BAC5C,cAAA,uVAAC,2HAAA,CAAA,gBAAa;;sCACZ,uVAAC,2HAAA,CAAA,eAAY;;8CACX,uVAAC,2HAAA,CAAA,cAAW;8CAAC;;;;;;8CACb,uVAAC,2HAAA,CAAA,oBAAiB;;wCAAC;wCACmB;sDACpC,uVAAC;4CAAK,WAAU;sDAAe,KAAK,SAAS;;;;;;wCAAQ;sDACrD,uVAAC;4CAAE,WAAU;sDAAO;;;;;;;;;;;;;;;;;;sCAGxB,uVAAC,2HAAA,CAAA,eAAY;;8CACX,uVAAC,2HAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAU,SAAS,IAAM,oBAAoB;8CAAQ;;;;;;8CAGrE,uVAAC,2HAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAc,SAAS;8CAAc;;;;;;;;;;;;;;;;;;;;;;;0BAQ3D,uVAAC;gBAAI,WAAU;0BACb,cAAA,uVAAC,qIAAA,CAAA,eAAY;;sCACX,uVAAC,qIAAA,CAAA,sBAAmB;4BAAC,OAAO;sCAC1B,cAAA,uVAAC,2HAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,WAAU;;kDAEV,uVAAC,oUAAA,CAAA,mBAAgB;wCAAC,WAAU;;;;;;kDAC5B,uVAAC;wCAAK,WAAU;kDAAU;;;;;;;;;;;;;;;;;sCAG9B,uVAAC,qIAAA,CAAA,sBAAmB;4BAAC,OAAM;4BAAM,WAAU;;8CACzC,uVAAC,qIAAA,CAAA,mBAAgB;oCAAC,SAAS,IAAM,aAAa;;sDAC5C,uVAAC,oRAAA,CAAA,MAAG;4CAAC,WAAU;;;;;;sDACf,uVAAC;4CAAK,WAAU;sDAAiB;;;;;;sDACjC,uVAAC,qIAAA,CAAA,uBAAoB;4CAAC,WAAU;sDAAU;;;;;;;;;;;;8CAG5C,uVAAC,qIAAA,CAAA,mBAAgB;oCAAC,SAAS,IAAM,OAAO;;sDACtC,uVAAC,+RAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;sDAChB,uVAAC;4CAAK,WAAU;sDAAiB;;;;;;sDACjC,uVAAC,qIAAA,CAAA,uBAAoB;4CAAC,WAAU;sDAAU;;;;;;;;;;;;8CAG5C,uVAAC,qIAAA,CAAA,mBAAgB;oCAAC,SAAS,IAAM,eAAe;8CAC7C,KAAK,MAAM,KAAK,yBACf;;0DACE,uVAAC,oRAAA,CAAA,MAAG;gDAAC,WAAU;;;;;;0DACf,uVAAC;gDAAK,WAAU;0DAAkC;;;;;;0DAClD,uVAAC,qIAAA,CAAA,uBAAoB;gDAAC,WAAU;0DAA2B;;;;;;;qEAK7D;;0DACE,uVAAC,oSAAA,CAAA,YAAS;gDAAC,WAAU;;;;;;0DACrB,uVAAC;gDAAK,WAAU;0DAAgC;;;;;;0DAChD,uVAAC,qIAAA,CAAA,uBAAoB;gDAAC,WAAU;0DAAyB;;;;;;;;;;;;;gCAO9D,6BACC,uVAAC,qIAAA,CAAA,mBAAgB;oCAAC,SAAS,IAAM,YAAY;;sDAC3C,uVAAC,sRAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;sDAChB,uVAAC;4CAAK,WAAU;sDAAiB;;;;;;sDACjC,uVAAC,qIAAA,CAAA,uBAAoB;4CAAC,WAAU;sDAAU;;;;;;;;;;;;8CAI9C,uVAAC,qIAAA,CAAA,wBAAqB;;;;;8CAEtB,uVAAC,qIAAA,CAAA,mBAAgB;oCACf,SAAS,IAAM,oBAAoB;oCACnC,WAAU;;sDAEV,uVAAC,8RAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;sDAClB,uVAAC;4CAAK,WAAU;sDAAkC;;;;;;sDAClD,uVAAC,qIAAA,CAAA,uBAAoB;4CAAC,WAAU;sDAA2B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASzE", "debugId": null}}, {"offset": {"line": 1997, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/bloomgoo/gold-exchange-platform/frontend/components/common/admin/ebanks/table/bank-table-columns.tsx"], "sourcesContent": ["/**\r\n * BankTableColumns - Định nghĩa columns cho table ngân hàng\r\n */\r\n\r\nimport { type ColumnDef } from '@tanstack/react-table';\r\nimport { ArrowUpDown } from 'lucide-react';\r\nimport { Button } from '@/components/ui/button';\r\nimport type { BankDto } from '../types';\r\nimport { \r\n  BankNameCell, \r\n  TextCell, \r\n  ImageCell, \r\n  StatusCell, \r\n  DateCell, \r\n  UserCell \r\n} from './bank-table-cells';\r\nimport { BankTableActions } from './bank-table-actions';\r\n\r\ninterface ColumnsProps {\r\n  onViewDetail: (bank: BankDto) => void;\r\n  onDelete: (bank: BankDto) => void;\r\n  onEdit: (bank: BankDto) => void;\r\n  onToggleStatus: (bank: BankDto) => void;\r\n  onDuplicate?: (bank: BankDto) => void;\r\n}\r\n\r\nexport function getBankTableColumns({\r\n  onViewDetail,\r\n  onDelete,\r\n  onEdit,\r\n  onToggleStatus,\r\n  onDuplicate,\r\n}: ColumnsProps): ColumnDef<BankDto>[] {\r\n  return [\r\n    // Tên ngân hàng\r\n    {\r\n      accessorKey: 'brandName',\r\n      header: ({ column }) => (\r\n        <Button\r\n          variant=\"ghost\"\r\n          size=\"sm\"\r\n          onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}\r\n          className=\"flex items-center gap-1 px-0 h-6 py-0\"\r\n          data-column-id=\"brandName\"\r\n        >\r\n          <span className=\"text-xs\">Tên ngân hàng</span>\r\n          <ArrowUpDown className=\"ml-1 h-3 w-3\" />\r\n        </Button>\r\n      ),\r\n      meta: {\r\n        header: 'Tên ngân hàng',\r\n        isSticky: true,\r\n        position: 'left',\r\n      },\r\n      cell: ({ row }) => <BankNameCell bank={row.original} />,\r\n      enableSorting: true,\r\n      size: 200,\r\n    },\r\n\r\n    // Tên đầy đủ\r\n    {\r\n      accessorKey: 'fullName',\r\n      header: ({ column }) => (\r\n        <Button\r\n          variant=\"ghost\"\r\n          size=\"sm\"\r\n          onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}\r\n          className=\"flex items-center gap-1 px-0 h-6 py-0\"\r\n          data-column-id=\"fullName\"\r\n        >\r\n          <span className=\"text-xs\">Tên đầy đủ</span>\r\n          <ArrowUpDown className=\"ml-1 h-3 w-3\" />\r\n        </Button>\r\n      ),\r\n      meta: {\r\n        header: 'Tên đầy đủ',\r\n        isSticky: false,\r\n        position: 'left',\r\n      },\r\n      cell: ({ row }) => <TextCell value={row.getValue('fullName')} maxWidth={200} />,\r\n      enableSorting: true,\r\n      size: 180,\r\n    },\r\n\r\n    // Tên ngắn gọn\r\n    {\r\n      accessorKey: 'shortName',\r\n      header: ({ column }) => (\r\n        <Button\r\n          variant=\"ghost\"\r\n          size=\"sm\"\r\n          onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}\r\n          className=\"flex items-center gap-1 px-0 h-6 py-0\"\r\n          data-column-id=\"shortName\"\r\n        >\r\n          <span className=\"text-xs\">Tên ngắn gọn</span>\r\n          <ArrowUpDown className=\"ml-1 h-3 w-3\" />\r\n        </Button>\r\n      ),\r\n      meta: {\r\n        header: 'Tên ngắn gọn',\r\n        isSticky: false,\r\n        position: 'left',\r\n      },\r\n      cell: ({ row }) => <TextCell value={row.getValue('shortName')} maxWidth={150} />,\r\n      enableSorting: true,\r\n      size: 150,\r\n    },\r\n\r\n    // Bin\r\n    {\r\n      accessorKey: 'bin',\r\n      header: ({ column }) => (\r\n        <Button\r\n          variant=\"ghost\"\r\n          size=\"sm\"\r\n          onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}\r\n          className=\"flex items-center gap-1 px-0 h-6 py-0\"\r\n          data-column-id=\"bin\"\r\n        >\r\n          <span className=\"text-xs\">Bin</span>\r\n          <ArrowUpDown className=\"ml-1 h-3 w-3\" />\r\n        </Button>\r\n      ),\r\n      meta: {\r\n        header: 'Bin',\r\n        isSticky: false,\r\n        position: 'left',\r\n      },\r\n      cell: ({ row }) => {\r\n        const bin = row.getValue('bin') as string;\r\n        return bin ? (\r\n          <span className=\"text-sm\">{bin}</span>\r\n        ) : (\r\n          <span className=\"text-xs text-muted-foreground\">---</span>\r\n        );\r\n      },\r\n      enableSorting: true,\r\n      size: 100,\r\n    },\r\n\r\n    // Logo\r\n    {\r\n      accessorKey: 'logoPath',\r\n      header: () => (\r\n        <Button\r\n          variant=\"ghost\"\r\n          size=\"sm\"\r\n          className=\"flex items-center gap-1 px-0 h-6 py-0\"\r\n          data-column-id=\"logoPath\"\r\n        >\r\n          <span className=\"text-xs\">Logo</span>\r\n        </Button>\r\n      ),\r\n      meta: {\r\n        header: 'Logo',\r\n        isSticky: false,\r\n        position: 'left',\r\n      },\r\n      cell: ({ row }) => (\r\n        <ImageCell url={row.getValue('logoPath')} alt=\"logo\" />\r\n      ),\r\n      enableSorting: false,\r\n      size: 120,\r\n    },\r\n\r\n    // Icon\r\n    {\r\n      accessorKey: 'icon',\r\n      header: () => (\r\n        <Button\r\n          variant=\"ghost\"\r\n          size=\"sm\"\r\n          className=\"flex items-center gap-1 px-0 h-6 py-0\"\r\n          data-column-id=\"icon\"\r\n        >\r\n          <span className=\"text-xs\">Icon</span>\r\n        </Button>\r\n      ),\r\n      meta: {\r\n        header: 'Icon',\r\n        isSticky: false,\r\n        position: 'left',\r\n      },\r\n      cell: ({ row }) => (\r\n        <ImageCell url={row.getValue('icon')} alt=\"icon\" />\r\n      ),\r\n      enableSorting: false,\r\n      size: 120,\r\n    },\r\n\r\n    // Trạng thái\r\n    {\r\n      accessorKey: 'isActive',\r\n      header: ({ column }) => (\r\n        <Button\r\n          variant=\"ghost\"\r\n          size=\"sm\"\r\n          onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}\r\n          className=\"flex items-center gap-1 px-0 h-6 py-0\"\r\n          data-column-id=\"isActive\"\r\n        >\r\n          <span className=\"text-xs\">Trạng thái</span>\r\n          <ArrowUpDown className=\"ml-1 h-3 w-3\" />\r\n        </Button>\r\n      ),\r\n      meta: {\r\n        header: 'Trạng thái',\r\n        isSticky: false,\r\n        position: 'right',\r\n      },\r\n      cell: ({ row }) => <StatusCell isActive={row.getValue('isActive')} />,\r\n      enableSorting: true,\r\n      size: 120,\r\n    },\r\n\r\n    // Ngày tạo\r\n    {\r\n      accessorKey: 'createdAt',\r\n      header: ({ column }) => (\r\n        <Button\r\n          variant=\"ghost\"\r\n          size=\"sm\"\r\n          onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}\r\n          className=\"flex items-center gap-1 px-0 h-6 py-0\"\r\n          data-column-id=\"createdAt\"\r\n        >\r\n          <span className=\"text-xs\">Ngày tạo</span>\r\n          <ArrowUpDown className=\"ml-1 h-3 w-3\" />\r\n        </Button>\r\n      ),\r\n      meta: {\r\n        header: 'Ngày tạo',\r\n        isSticky: false,\r\n        position: 'right',\r\n      },\r\n      cell: ({ row }) => <DateCell date={row.getValue('createdAt')} />,\r\n      enableSorting: true,\r\n      size: 150,\r\n    },\r\n\r\n    // Cập nhật lần cuối\r\n    {\r\n      accessorKey: 'updatedAt',\r\n      header: ({ column }) => (\r\n        <Button\r\n          variant=\"ghost\"\r\n          size=\"sm\"\r\n          onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}\r\n          className=\"flex items-center gap-1 px-0 h-6 py-0\"\r\n          data-column-id=\"updatedAt\"\r\n        >\r\n          <span className=\"text-xs\">Cập nhật lần cuối</span>\r\n          <ArrowUpDown className=\"ml-1 h-3 w-3\" />\r\n        </Button>\r\n      ),\r\n      meta: {\r\n        header: 'Cập nhật lần cuối',\r\n        isSticky: false,\r\n        position: 'right',\r\n      },\r\n      cell: ({ row }) => <DateCell date={row.getValue('updatedAt')} />,\r\n      enableSorting: true,\r\n      size: 150,\r\n    },\r\n\r\n    // Ngày xóa\r\n    {\r\n      accessorKey: 'deletedAt',\r\n      header: ({ column }) => (\r\n        <Button\r\n          variant=\"ghost\"\r\n          size=\"sm\"\r\n          onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}\r\n          className=\"flex items-center gap-1 px-0 h-6 py-0\"\r\n          data-column-id=\"deletedAt\"\r\n        >\r\n          <span className=\"text-xs\">Ngày xóa</span>\r\n          <ArrowUpDown className=\"ml-1 h-3 w-3\" />\r\n        </Button>\r\n      ),\r\n      meta: {\r\n        header: 'Ngày xóa',\r\n        isSticky: false,\r\n        position: 'right',\r\n      },\r\n      cell: ({ row }) => <DateCell date={row.getValue('deletedAt')} />,\r\n      enableSorting: true,\r\n      size: 150,\r\n    },\r\n\r\n    // Người tạo\r\n    {\r\n      accessorKey: 'createdBy',\r\n      header: ({ column }) => (\r\n        <Button\r\n          variant=\"ghost\"\r\n          size=\"sm\"\r\n          onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}\r\n          className=\"flex items-center gap-1 px-0 h-6 py-0\"\r\n          data-column-id=\"createdBy\"\r\n        >\r\n          <span className=\"text-xs\">Người tạo</span>\r\n          <ArrowUpDown className=\"ml-1 h-3 w-3\" />\r\n        </Button>\r\n      ),\r\n      meta: {\r\n        header: 'Người tạo',\r\n        isSticky: false,\r\n        position: 'right',\r\n      },\r\n      cell: ({ row }) => {\r\n        const bank = row.original;\r\n        return <UserCell user={bank.creator} userId={bank.createdBy} />;\r\n      },\r\n      enableSorting: true,\r\n      enableHiding: true,\r\n      size: 180,\r\n    },\r\n\r\n    // Người cập nhật\r\n    {\r\n      accessorKey: 'updatedBy',\r\n      header: ({ column }) => (\r\n        <Button\r\n          variant=\"ghost\"\r\n          size=\"sm\"\r\n          onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}\r\n          className=\"flex items-center gap-1 px-0 h-6 py-0\"\r\n          data-column-id=\"updatedBy\"\r\n        >\r\n          <span className=\"text-xs\">Người cập nhật</span>\r\n          <ArrowUpDown className=\"ml-1 h-3 w-3\" />\r\n        </Button>\r\n      ),\r\n      meta: {\r\n        header: 'Người cập nhật',\r\n        isSticky: false,\r\n        position: 'right',\r\n      },\r\n      cell: ({ row }) => {\r\n        const bank = row.original;\r\n        return <UserCell user={bank.updater} userId={bank.updatedBy} />;\r\n      },\r\n      enableSorting: true,\r\n      enableHiding: true,\r\n      size: 180,\r\n    },\r\n\r\n    // Người xóa\r\n    {\r\n      accessorKey: 'deletedBy',\r\n      header: ({ column }) => (\r\n        <Button\r\n          variant=\"ghost\"\r\n          size=\"sm\"\r\n          onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}\r\n          className=\"flex items-center gap-1 px-0 h-6 py-0\"\r\n          data-column-id=\"deletedBy\"\r\n        >\r\n          <span className=\"text-xs\">Người xóa</span>\r\n          <ArrowUpDown className=\"ml-1 h-3 w-3\" />\r\n        </Button>\r\n      ),\r\n      meta: {\r\n        header: 'Người xóa',\r\n        isSticky: false,\r\n        position: 'right',\r\n      },\r\n      cell: ({ row }) => {\r\n        const bank = row.original;\r\n        return <UserCell user={bank.deleter} userId={bank.deletedBy} />;\r\n      },\r\n      enableSorting: true,\r\n      enableHiding: true,\r\n      size: 180,\r\n    },\r\n\r\n    // Actions\r\n    {\r\n      id: 'actions',\r\n      size: 50,\r\n      enableHiding: false,\r\n      enableSorting: false,\r\n      header: () => <div className=\"text-right\"></div>,\r\n      cell: ({ row }) => (\r\n        <BankTableActions\r\n          row={row}\r\n          onViewDetail={onViewDetail}\r\n          onDelete={onDelete}\r\n          onEdit={onEdit}\r\n          onToggleStatus={onToggleStatus}\r\n          onDuplicate={onDuplicate}\r\n        />\r\n      ),\r\n      meta: {\r\n        header: 'Thao tác',\r\n        isSticky: true,\r\n        position: 'right',\r\n      },\r\n    },\r\n  ];\r\n}\r\n"], "names": [], "mappings": "AAAA;;CAEC;;;;AAGD;AACA;AAEA;AAQA;;;;;;AAUO,SAAS,oBAAoB,EAClC,YAAY,EACZ,QAAQ,EACR,MAAM,EACN,cAAc,EACd,WAAW,EACE;IACb,OAAO;QACL,gBAAgB;QAChB;YACE,aAAa;YACb,QAAQ,CAAC,EAAE,MAAM,EAAE,iBACjB,uVAAC,2HAAA,CAAA,SAAM;oBACL,SAAQ;oBACR,MAAK;oBACL,SAAS,IAAM,OAAO,aAAa,CAAC,OAAO,WAAW,OAAO;oBAC7D,WAAU;oBACV,kBAAe;;sCAEf,uVAAC;4BAAK,WAAU;sCAAU;;;;;;sCAC1B,uVAAC,4SAAA,CAAA,cAAW;4BAAC,WAAU;;;;;;;;;;;;YAG3B,MAAM;gBACJ,QAAQ;gBACR,UAAU;gBACV,UAAU;YACZ;YACA,MAAM,CAAC,EAAE,GAAG,EAAE,iBAAK,uVAAC,2KAAA,CAAA,eAAY;oBAAC,MAAM,IAAI,QAAQ;;;;;;YACnD,eAAe;YACf,MAAM;QACR;QAEA,aAAa;QACb;YACE,aAAa;YACb,QAAQ,CAAC,EAAE,MAAM,EAAE,iBACjB,uVAAC,2HAAA,CAAA,SAAM;oBACL,SAAQ;oBACR,MAAK;oBACL,SAAS,IAAM,OAAO,aAAa,CAAC,OAAO,WAAW,OAAO;oBAC7D,WAAU;oBACV,kBAAe;;sCAEf,uVAAC;4BAAK,WAAU;sCAAU;;;;;;sCAC1B,uVAAC,4SAAA,CAAA,cAAW;4BAAC,WAAU;;;;;;;;;;;;YAG3B,MAAM;gBACJ,QAAQ;gBACR,UAAU;gBACV,UAAU;YACZ;YACA,MAAM,CAAC,EAAE,GAAG,EAAE,iBAAK,uVAAC,2KAAA,CAAA,WAAQ;oBAAC,OAAO,IAAI,QAAQ,CAAC;oBAAa,UAAU;;;;;;YACxE,eAAe;YACf,MAAM;QACR;QAEA,eAAe;QACf;YACE,aAAa;YACb,QAAQ,CAAC,EAAE,MAAM,EAAE,iBACjB,uVAAC,2HAAA,CAAA,SAAM;oBACL,SAAQ;oBACR,MAAK;oBACL,SAAS,IAAM,OAAO,aAAa,CAAC,OAAO,WAAW,OAAO;oBAC7D,WAAU;oBACV,kBAAe;;sCAEf,uVAAC;4BAAK,WAAU;sCAAU;;;;;;sCAC1B,uVAAC,4SAAA,CAAA,cAAW;4BAAC,WAAU;;;;;;;;;;;;YAG3B,MAAM;gBACJ,QAAQ;gBACR,UAAU;gBACV,UAAU;YACZ;YACA,MAAM,CAAC,EAAE,GAAG,EAAE,iBAAK,uVAAC,2KAAA,CAAA,WAAQ;oBAAC,OAAO,IAAI,QAAQ,CAAC;oBAAc,UAAU;;;;;;YACzE,eAAe;YACf,MAAM;QACR;QAEA,MAAM;QACN;YACE,aAAa;YACb,QAAQ,CAAC,EAAE,MAAM,EAAE,iBACjB,uVAAC,2HAAA,CAAA,SAAM;oBACL,SAAQ;oBACR,MAAK;oBACL,SAAS,IAAM,OAAO,aAAa,CAAC,OAAO,WAAW,OAAO;oBAC7D,WAAU;oBACV,kBAAe;;sCAEf,uVAAC;4BAAK,WAAU;sCAAU;;;;;;sCAC1B,uVAAC,4SAAA,CAAA,cAAW;4BAAC,WAAU;;;;;;;;;;;;YAG3B,MAAM;gBACJ,QAAQ;gBACR,UAAU;gBACV,UAAU;YACZ;YACA,MAAM,CAAC,EAAE,GAAG,EAAE;gBACZ,MAAM,MAAM,IAAI,QAAQ,CAAC;gBACzB,OAAO,oBACL,uVAAC;oBAAK,WAAU;8BAAW;;;;;yCAE3B,uVAAC;oBAAK,WAAU;8BAAgC;;;;;;YAEpD;YACA,eAAe;YACf,MAAM;QACR;QAEA,OAAO;QACP;YACE,aAAa;YACb,QAAQ,kBACN,uVAAC,2HAAA,CAAA,SAAM;oBACL,SAAQ;oBACR,MAAK;oBACL,WAAU;oBACV,kBAAe;8BAEf,cAAA,uVAAC;wBAAK,WAAU;kCAAU;;;;;;;;;;;YAG9B,MAAM;gBACJ,QAAQ;gBACR,UAAU;gBACV,UAAU;YACZ;YACA,MAAM,CAAC,EAAE,GAAG,EAAE,iBACZ,uVAAC,2KAAA,CAAA,YAAS;oBAAC,KAAK,IAAI,QAAQ,CAAC;oBAAa,KAAI;;;;;;YAEhD,eAAe;YACf,MAAM;QACR;QAEA,OAAO;QACP;YACE,aAAa;YACb,QAAQ,kBACN,uVAAC,2HAAA,CAAA,SAAM;oBACL,SAAQ;oBACR,MAAK;oBACL,WAAU;oBACV,kBAAe;8BAEf,cAAA,uVAAC;wBAAK,WAAU;kCAAU;;;;;;;;;;;YAG9B,MAAM;gBACJ,QAAQ;gBACR,UAAU;gBACV,UAAU;YACZ;YACA,MAAM,CAAC,EAAE,GAAG,EAAE,iBACZ,uVAAC,2KAAA,CAAA,YAAS;oBAAC,KAAK,IAAI,QAAQ,CAAC;oBAAS,KAAI;;;;;;YAE5C,eAAe;YACf,MAAM;QACR;QAEA,aAAa;QACb;YACE,aAAa;YACb,QAAQ,CAAC,EAAE,MAAM,EAAE,iBACjB,uVAAC,2HAAA,CAAA,SAAM;oBACL,SAAQ;oBACR,MAAK;oBACL,SAAS,IAAM,OAAO,aAAa,CAAC,OAAO,WAAW,OAAO;oBAC7D,WAAU;oBACV,kBAAe;;sCAEf,uVAAC;4BAAK,WAAU;sCAAU;;;;;;sCAC1B,uVAAC,4SAAA,CAAA,cAAW;4BAAC,WAAU;;;;;;;;;;;;YAG3B,MAAM;gBACJ,QAAQ;gBACR,UAAU;gBACV,UAAU;YACZ;YACA,MAAM,CAAC,EAAE,GAAG,EAAE,iBAAK,uVAAC,2KAAA,CAAA,aAAU;oBAAC,UAAU,IAAI,QAAQ,CAAC;;;;;;YACtD,eAAe;YACf,MAAM;QACR;QAEA,WAAW;QACX;YACE,aAAa;YACb,QAAQ,CAAC,EAAE,MAAM,EAAE,iBACjB,uVAAC,2HAAA,CAAA,SAAM;oBACL,SAAQ;oBACR,MAAK;oBACL,SAAS,IAAM,OAAO,aAAa,CAAC,OAAO,WAAW,OAAO;oBAC7D,WAAU;oBACV,kBAAe;;sCAEf,uVAAC;4BAAK,WAAU;sCAAU;;;;;;sCAC1B,uVAAC,4SAAA,CAAA,cAAW;4BAAC,WAAU;;;;;;;;;;;;YAG3B,MAAM;gBACJ,QAAQ;gBACR,UAAU;gBACV,UAAU;YACZ;YACA,MAAM,CAAC,EAAE,GAAG,EAAE,iBAAK,uVAAC,2KAAA,CAAA,WAAQ;oBAAC,MAAM,IAAI,QAAQ,CAAC;;;;;;YAChD,eAAe;YACf,MAAM;QACR;QAEA,oBAAoB;QACpB;YACE,aAAa;YACb,QAAQ,CAAC,EAAE,MAAM,EAAE,iBACjB,uVAAC,2HAAA,CAAA,SAAM;oBACL,SAAQ;oBACR,MAAK;oBACL,SAAS,IAAM,OAAO,aAAa,CAAC,OAAO,WAAW,OAAO;oBAC7D,WAAU;oBACV,kBAAe;;sCAEf,uVAAC;4BAAK,WAAU;sCAAU;;;;;;sCAC1B,uVAAC,4SAAA,CAAA,cAAW;4BAAC,WAAU;;;;;;;;;;;;YAG3B,MAAM;gBACJ,QAAQ;gBACR,UAAU;gBACV,UAAU;YACZ;YACA,MAAM,CAAC,EAAE,GAAG,EAAE,iBAAK,uVAAC,2KAAA,CAAA,WAAQ;oBAAC,MAAM,IAAI,QAAQ,CAAC;;;;;;YAChD,eAAe;YACf,MAAM;QACR;QAEA,WAAW;QACX;YACE,aAAa;YACb,QAAQ,CAAC,EAAE,MAAM,EAAE,iBACjB,uVAAC,2HAAA,CAAA,SAAM;oBACL,SAAQ;oBACR,MAAK;oBACL,SAAS,IAAM,OAAO,aAAa,CAAC,OAAO,WAAW,OAAO;oBAC7D,WAAU;oBACV,kBAAe;;sCAEf,uVAAC;4BAAK,WAAU;sCAAU;;;;;;sCAC1B,uVAAC,4SAAA,CAAA,cAAW;4BAAC,WAAU;;;;;;;;;;;;YAG3B,MAAM;gBACJ,QAAQ;gBACR,UAAU;gBACV,UAAU;YACZ;YACA,MAAM,CAAC,EAAE,GAAG,EAAE,iBAAK,uVAAC,2KAAA,CAAA,WAAQ;oBAAC,MAAM,IAAI,QAAQ,CAAC;;;;;;YAChD,eAAe;YACf,MAAM;QACR;QAEA,YAAY;QACZ;YACE,aAAa;YACb,QAAQ,CAAC,EAAE,MAAM,EAAE,iBACjB,uVAAC,2HAAA,CAAA,SAAM;oBACL,SAAQ;oBACR,MAAK;oBACL,SAAS,IAAM,OAAO,aAAa,CAAC,OAAO,WAAW,OAAO;oBAC7D,WAAU;oBACV,kBAAe;;sCAEf,uVAAC;4BAAK,WAAU;sCAAU;;;;;;sCAC1B,uVAAC,4SAAA,CAAA,cAAW;4BAAC,WAAU;;;;;;;;;;;;YAG3B,MAAM;gBACJ,QAAQ;gBACR,UAAU;gBACV,UAAU;YACZ;YACA,MAAM,CAAC,EAAE,GAAG,EAAE;gBACZ,MAAM,OAAO,IAAI,QAAQ;gBACzB,qBAAO,uVAAC,2KAAA,CAAA,WAAQ;oBAAC,MAAM,KAAK,OAAO;oBAAE,QAAQ,KAAK,SAAS;;;;;;YAC7D;YACA,eAAe;YACf,cAAc;YACd,MAAM;QACR;QAEA,iBAAiB;QACjB;YACE,aAAa;YACb,QAAQ,CAAC,EAAE,MAAM,EAAE,iBACjB,uVAAC,2HAAA,CAAA,SAAM;oBACL,SAAQ;oBACR,MAAK;oBACL,SAAS,IAAM,OAAO,aAAa,CAAC,OAAO,WAAW,OAAO;oBAC7D,WAAU;oBACV,kBAAe;;sCAEf,uVAAC;4BAAK,WAAU;sCAAU;;;;;;sCAC1B,uVAAC,4SAAA,CAAA,cAAW;4BAAC,WAAU;;;;;;;;;;;;YAG3B,MAAM;gBACJ,QAAQ;gBACR,UAAU;gBACV,UAAU;YACZ;YACA,MAAM,CAAC,EAAE,GAAG,EAAE;gBACZ,MAAM,OAAO,IAAI,QAAQ;gBACzB,qBAAO,uVAAC,2KAAA,CAAA,WAAQ;oBAAC,MAAM,KAAK,OAAO;oBAAE,QAAQ,KAAK,SAAS;;;;;;YAC7D;YACA,eAAe;YACf,cAAc;YACd,MAAM;QACR;QAEA,YAAY;QACZ;YACE,aAAa;YACb,QAAQ,CAAC,EAAE,MAAM,EAAE,iBACjB,uVAAC,2HAAA,CAAA,SAAM;oBACL,SAAQ;oBACR,MAAK;oBACL,SAAS,IAAM,OAAO,aAAa,CAAC,OAAO,WAAW,OAAO;oBAC7D,WAAU;oBACV,kBAAe;;sCAEf,uVAAC;4BAAK,WAAU;sCAAU;;;;;;sCAC1B,uVAAC,4SAAA,CAAA,cAAW;4BAAC,WAAU;;;;;;;;;;;;YAG3B,MAAM;gBACJ,QAAQ;gBACR,UAAU;gBACV,UAAU;YACZ;YACA,MAAM,CAAC,EAAE,GAAG,EAAE;gBACZ,MAAM,OAAO,IAAI,QAAQ;gBACzB,qBAAO,uVAAC,2KAAA,CAAA,WAAQ;oBAAC,MAAM,KAAK,OAAO;oBAAE,QAAQ,KAAK,SAAS;;;;;;YAC7D;YACA,eAAe;YACf,cAAc;YACd,MAAM;QACR;QAEA,UAAU;QACV;YACE,IAAI;YACJ,MAAM;YACN,cAAc;YACd,eAAe;YACf,QAAQ,kBAAM,uVAAC;oBAAI,WAAU;;;;;;YAC7B,MAAM,CAAC,EAAE,GAAG,EAAE,iBACZ,uVAAC,6KAAA,CAAA,mBAAgB;oBACf,KAAK;oBACL,cAAc;oBACd,UAAU;oBACV,QAAQ;oBACR,gBAAgB;oBAChB,aAAa;;;;;;YAGjB,MAAM;gBACJ,QAAQ;gBACR,UAAU;gBACV,UAAU;YACZ;QACF;KACD;AACH", "debugId": null}}, {"offset": {"line": 2661, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/bloomgoo/gold-exchange-platform/frontend/components/common/admin/ebanks/components/bank-header.tsx"], "sourcesContent": ["/**\r\n * BankHeader - Header component với title và action buttons\r\n */\r\n\r\n'use client';\r\n\r\nimport { Plus } from 'lucide-react';\r\nimport { Button } from '@/components/ui/button';\r\n\r\ninterface BankHeaderProps {\r\n  totalRows: number;\r\n  isUpdating: boolean;\r\n  onAddBank: () => void;\r\n  onExport?: (format: 'csv' | 'json') => void;\r\n  onImport?: () => void;\r\n}\r\n\r\nexport function BankHeader({\r\n  totalRows,\r\n  isUpdating,\r\n  onAddBank,\r\n  onExport,\r\n  onImport,\r\n}: BankHeaderProps) {\r\n  return (\r\n    <div className=\"w-full flex justify-between items-center border-b py-1.5 px-6 h-10\">\r\n      {/* Left side - Title và count */}\r\n      <div className=\"flex items-center gap-2\">\r\n        <div className=\"flex items-center gap-1\">\r\n          <span className=\"text-sm font-medium\">Ngân hàng</span>\r\n          <span className=\"text-xs bg-accent rounded-md px-1.5 py-1\">\r\n            {totalRows}\r\n          </span>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Right side - Action buttons */}\r\n      <div className=\"flex items-center gap-2\">\r\n        {isUpdating && (\r\n          <span className=\"text-xs text-muted-foreground\">Đang cập nhật...</span>\r\n        )}\r\n        \r\n        {/* Import/Export buttons - commented out for now */}\r\n        {/* {onImport && (\r\n          <Button \r\n            className=\"relative\" \r\n            size=\"sm\" \r\n            variant=\"outline\" \r\n            onClick={onImport}\r\n          >\r\n            <Upload className=\"size-4 mr-1\" />\r\n            Import\r\n          </Button>\r\n        )}\r\n        \r\n        {onExport && (\r\n          <>\r\n            <Button \r\n              className=\"relative\" \r\n              size=\"sm\" \r\n              variant=\"outline\" \r\n              onClick={() => onExport('csv')}\r\n            >\r\n              <Download className=\"size-4 mr-1\" />\r\n              Export CSV\r\n            </Button>\r\n            <Button \r\n              className=\"relative\" \r\n              size=\"sm\" \r\n              variant=\"outline\" \r\n              onClick={() => onExport('json')}\r\n            >\r\n              <Download className=\"size-4 mr-1\" />\r\n              Export JSON\r\n            </Button>\r\n          </>\r\n        )} */}\r\n        \r\n        <Button \r\n          className=\"relative\" \r\n          size=\"sm\" \r\n          variant=\"secondary\" \r\n          onClick={onAddBank}\r\n        >\r\n          <Plus className=\"size-4 mr-1\" />\r\n          Thêm mới\r\n        </Button>\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": "AAAA;;CAEC;;;;AAID;AACA;AAHA;;;;AAaO,SAAS,WAAW,EACzB,SAAS,EACT,UAAU,EACV,SAAS,EACT,QAAQ,EACR,QAAQ,EACQ;IAChB,qBACE,uVAAC;QAAI,WAAU;;0BAEb,uVAAC;gBAAI,WAAU;0BACb,cAAA,uVAAC;oBAAI,WAAU;;sCACb,uVAAC;4BAAK,WAAU;sCAAsB;;;;;;sCACtC,uVAAC;4BAAK,WAAU;sCACb;;;;;;;;;;;;;;;;;0BAMP,uVAAC;gBAAI,WAAU;;oBACZ,4BACC,uVAAC;wBAAK,WAAU;kCAAgC;;;;;;kCAuClD,uVAAC,2HAAA,CAAA,SAAM;wBACL,WAAU;wBACV,MAAK;wBACL,SAAQ;wBACR,SAAS;;0CAET,uVAAC,sRAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;4BAAgB;;;;;;;;;;;;;;;;;;;AAM1C", "debugId": null}}, {"offset": {"line": 2759, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/bloomgoo/gold-exchange-platform/frontend/components/common/selector/column-visibility-selector.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { But<PERSON> } from '@/components/ui/button';\r\nimport { Checkbox } from '@/components/ui/checkbox';\r\nimport {\r\n  DropdownMenu,\r\n  DropdownMenuContent,\r\n  DropdownMenuTrigger,\r\n} from '@/components/ui/dropdown-menu';\r\nimport { Eye, Search, Settings2 } from 'lucide-react';\r\nimport { Table } from '@tanstack/react-table';\r\nimport { Input } from '@/components/ui/input';\r\nimport { useState, useMemo } from 'react';\r\n\r\n// Định nghĩa kiểu dữ liệu cho thuộc tính meta của cột\r\ndeclare module '@tanstack/react-table' {\r\n  interface ColumnMeta<TData, TValue> {\r\n    isSticky?: boolean;\r\n    position?: 'left' | 'right';\r\n    header?: string;\r\n  }\r\n}\r\n\r\ninterface ColumnVisibilitySelectorProps<TData> {\r\n  table: Table<TData>;\r\n  className?: string;\r\n  buttonClassName?: string;\r\n  contentClassName?: string;\r\n  label?: string;\r\n}\r\n\r\nexport function ColumnVisibilitySelector<TData>({\r\n  table,\r\n  className,\r\n  buttonClassName,\r\n  contentClassName = 'w-[250px] max-h-[var(--radix-dropdown-menu-content-available-height)] overflow-y-auto',\r\n  label = 'Tất cả',\r\n}: ColumnVisibilitySelectorProps<TData>) {\r\n  // State để lưu từ khóa tìm kiếm\r\n  const [searchQuery, setSearchQuery] = useState('');\r\n  // Lấy tất cả các cột có thể ẩn/hiện\r\n  const allColumns = useMemo(() => {\r\n    return table.getAllColumns().filter(column => column.getCanHide());\r\n  }, [table]);\r\n\r\n  // Lấy các cột đang hiển thị\r\n  const visibleColumns = useMemo(() => {\r\n    return allColumns.filter(column => column.getIsVisible());\r\n  }, [allColumns, table.getState().columnVisibility]);\r\n\r\n  // Lọc các cột dựa trên từ khóa tìm kiếm\r\n  const filteredColumns = useMemo(() => {\r\n    if (!searchQuery.trim()) {\r\n      return allColumns;\r\n    }\r\n\r\n    const query = searchQuery.toLowerCase().trim();\r\n    return allColumns.filter(column => {\r\n      // Lấy tên cột thân thiện từ header\r\n      let header = column.id;\r\n\r\n      // Ưu tiên sử dụng meta.header\r\n      if (column.columnDef.meta?.header) {\r\n        header = column.columnDef.meta.header;\r\n      }\r\n      // Nếu header là string, sử dụng trực tiếp\r\n      else if (typeof column.columnDef.header === 'string') {\r\n        header = column.columnDef.header;\r\n      }\r\n      // Nếu header là function, tìm kiếm phần tử span chứa tên cột\r\n      else if (typeof column.columnDef.header === 'function') {\r\n        const headerElement = document.querySelector(`[data-column-id=\"${column.id}\"] span`);\r\n        if (headerElement && headerElement.textContent) {\r\n          header = headerElement.textContent;\r\n        }\r\n      }\r\n\r\n      return header.toString().toLowerCase().includes(query);\r\n    });\r\n  }, [allColumns, searchQuery]);\r\n\r\n  // Hàm để toggle tất cả các cột\r\n  const toggleAllColumns = (checked: boolean) => {\r\n    table.toggleAllColumnsVisible(checked);\r\n  };\r\n\r\n  return (\r\n    <div className={className}>\r\n      <DropdownMenu>\r\n        <DropdownMenuTrigger asChild>\r\n          <Button\r\n            variant=\"ghost\"\r\n            size=\"icon\"\r\n            className={`relative ${buttonClassName || ''}`}\r\n          >\r\n            <Settings2 className=\"h-4 w-4\" />\r\n            {visibleColumns.length < allColumns.length && (\r\n              <span className=\"absolute -top-1 -right-1 h-2 w-2 rounded-full bg-blue-600\" />\r\n            )}\r\n          </Button>\r\n        </DropdownMenuTrigger>\r\n        <DropdownMenuContent\r\n          align=\"end\"\r\n          className={contentClassName}\r\n        >\r\n          <div className=\"sticky top-0 bg-background z-10 border-b\">\r\n            <div className=\"p-2\">\r\n              <div className=\"flex items-center space-x-2 px-1 py-1\">\r\n                <Checkbox\r\n                  checked={visibleColumns.length === allColumns.length}\r\n                  onCheckedChange={(checked) => toggleAllColumns(!!checked)}\r\n                  id=\"all-columns\"\r\n                />\r\n                <label\r\n                  htmlFor=\"all-columns\"\r\n                  className=\"text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70\"\r\n                >\r\n                  {label}\r\n                </label>\r\n              </div>\r\n\r\n              {/* Thêm ô tìm kiếm */}\r\n              <div className=\"relative mt-2\">\r\n                <Search className=\"absolute left-2 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground\" />\r\n                <Input\r\n                  placeholder=\"Tìm kiếm cột...\"\r\n                  value={searchQuery}\r\n                  onChange={(e) => setSearchQuery(e.target.value)}\r\n                  className=\"pl-8 h-8 text-sm\"\r\n                />\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <div className=\"p-2 pt-0\">\r\n            {filteredColumns.length === 0 ? (\r\n              <div className=\"py-6 text-center text-sm text-muted-foreground\">\r\n                Không tìm thấy cột nào\r\n              </div>\r\n            ) : (\r\n              filteredColumns.map(column => {\r\n                const isVisible = column.getIsVisible();\r\n                return (\r\n                  <div\r\n                    key={column.id}\r\n                    className=\"flex items-center space-x-2 px-1 py-1.5 rounded hover:bg-accent\"\r\n                  >\r\n                    <Checkbox\r\n                      checked={isVisible}\r\n                      onCheckedChange={(checked) => column.toggleVisibility(!!checked)}\r\n                      id={column.id}\r\n                    />\r\n                    <label\r\n                      htmlFor={column.id}\r\n                      className=\"flex-1 text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70\"\r\n                    >\r\n                      {(() => {\r\n                        // Ưu tiên sử dụng meta.header\r\n                        if (column.columnDef.meta?.header) {\r\n                          return column.columnDef.meta.header;\r\n                        }\r\n\r\n                        // Nếu header là string, sử dụng trực tiếp\r\n                        if (typeof column.columnDef.header === 'string') {\r\n                          return column.columnDef.header;\r\n                        }\r\n\r\n                        // Tìm kiếm phần tử span trong header\r\n                        const headerElement = document.querySelector(`[data-column-id=\"${column.id}\"] span`);\r\n                        if (headerElement && headerElement.textContent) {\r\n                          return headerElement.textContent;\r\n                        }\r\n\r\n                        // Fallback: Sử dụng ID\r\n                        return column.id;\r\n                      })()}\r\n                    </label>\r\n                  </div>\r\n                );\r\n              })\r\n            )}\r\n          </div>\r\n        </DropdownMenuContent>\r\n      </DropdownMenu>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAKA;AAAA;AAEA;AACA;AAZA;;;;;;;;AA+BO,SAAS,yBAAgC,EAC9C,KAAK,EACL,SAAS,EACT,eAAe,EACf,mBAAmB,uFAAuF,EAC1G,QAAQ,QAAQ,EACqB;IACrC,gCAAgC;IAChC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,8SAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,oCAAoC;IACpC,MAAM,aAAa,CAAA,GAAA,8SAAA,CAAA,UAAO,AAAD,EAAE;QACzB,OAAO,MAAM,aAAa,GAAG,MAAM,CAAC,CAAA,SAAU,OAAO,UAAU;IACjE,GAAG;QAAC;KAAM;IAEV,4BAA4B;IAC5B,MAAM,iBAAiB,CAAA,GAAA,8SAAA,CAAA,UAAO,AAAD,EAAE;QAC7B,OAAO,WAAW,MAAM,CAAC,CAAA,SAAU,OAAO,YAAY;IACxD,GAAG;QAAC;QAAY,MAAM,QAAQ,GAAG,gBAAgB;KAAC;IAElD,wCAAwC;IACxC,MAAM,kBAAkB,CAAA,GAAA,8SAAA,CAAA,UAAO,AAAD,EAAE;QAC9B,IAAI,CAAC,YAAY,IAAI,IAAI;YACvB,OAAO;QACT;QAEA,MAAM,QAAQ,YAAY,WAAW,GAAG,IAAI;QAC5C,OAAO,WAAW,MAAM,CAAC,CAAA;YACvB,mCAAmC;YACnC,IAAI,SAAS,OAAO,EAAE;YAEtB,8BAA8B;YAC9B,IAAI,OAAO,SAAS,CAAC,IAAI,EAAE,QAAQ;gBACjC,SAAS,OAAO,SAAS,CAAC,IAAI,CAAC,MAAM;YACvC,OAEK,IAAI,OAAO,OAAO,SAAS,CAAC,MAAM,KAAK,UAAU;gBACpD,SAAS,OAAO,SAAS,CAAC,MAAM;YAClC,OAEK,IAAI,OAAO,OAAO,SAAS,CAAC,MAAM,KAAK,YAAY;gBACtD,MAAM,gBAAgB,SAAS,aAAa,CAAC,CAAC,iBAAiB,EAAE,OAAO,EAAE,CAAC,OAAO,CAAC;gBACnF,IAAI,iBAAiB,cAAc,WAAW,EAAE;oBAC9C,SAAS,cAAc,WAAW;gBACpC;YACF;YAEA,OAAO,OAAO,QAAQ,GAAG,WAAW,GAAG,QAAQ,CAAC;QAClD;IACF,GAAG;QAAC;QAAY;KAAY;IAE5B,+BAA+B;IAC/B,MAAM,mBAAmB,CAAC;QACxB,MAAM,uBAAuB,CAAC;IAChC;IAEA,qBACE,uVAAC;QAAI,WAAW;kBACd,cAAA,uVAAC,qIAAA,CAAA,eAAY;;8BACX,uVAAC,qIAAA,CAAA,sBAAmB;oBAAC,OAAO;8BAC1B,cAAA,uVAAC,2HAAA,CAAA,SAAM;wBACL,SAAQ;wBACR,MAAK;wBACL,WAAW,CAAC,SAAS,EAAE,mBAAmB,IAAI;;0CAE9C,uVAAC,oSAAA,CAAA,YAAS;gCAAC,WAAU;;;;;;4BACpB,eAAe,MAAM,GAAG,WAAW,MAAM,kBACxC,uVAAC;gCAAK,WAAU;;;;;;;;;;;;;;;;;8BAItB,uVAAC,qIAAA,CAAA,sBAAmB;oBAClB,OAAM;oBACN,WAAW;;sCAEX,uVAAC;4BAAI,WAAU;sCACb,cAAA,uVAAC;gCAAI,WAAU;;kDACb,uVAAC;wCAAI,WAAU;;0DACb,uVAAC,6HAAA,CAAA,WAAQ;gDACP,SAAS,eAAe,MAAM,KAAK,WAAW,MAAM;gDACpD,iBAAiB,CAAC,UAAY,iBAAiB,CAAC,CAAC;gDACjD,IAAG;;;;;;0DAEL,uVAAC;gDACC,SAAQ;gDACR,WAAU;0DAET;;;;;;;;;;;;kDAKL,uVAAC;wCAAI,WAAU;;0DACb,uVAAC,0RAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;0DAClB,uVAAC,0HAAA,CAAA,QAAK;gDACJ,aAAY;gDACZ,OAAO;gDACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;gDAC9C,WAAU;;;;;;;;;;;;;;;;;;;;;;;sCAMlB,uVAAC;4BAAI,WAAU;sCACZ,gBAAgB,MAAM,KAAK,kBAC1B,uVAAC;gCAAI,WAAU;0CAAiD;;;;;uCAIhE,gBAAgB,GAAG,CAAC,CAAA;gCAClB,MAAM,YAAY,OAAO,YAAY;gCACrC,qBACE,uVAAC;oCAEC,WAAU;;sDAEV,uVAAC,6HAAA,CAAA,WAAQ;4CACP,SAAS;4CACT,iBAAiB,CAAC,UAAY,OAAO,gBAAgB,CAAC,CAAC,CAAC;4CACxD,IAAI,OAAO,EAAE;;;;;;sDAEf,uVAAC;4CACC,SAAS,OAAO,EAAE;4CAClB,WAAU;sDAET,CAAC;gDACA,8BAA8B;gDAC9B,IAAI,OAAO,SAAS,CAAC,IAAI,EAAE,QAAQ;oDACjC,OAAO,OAAO,SAAS,CAAC,IAAI,CAAC,MAAM;gDACrC;gDAEA,0CAA0C;gDAC1C,IAAI,OAAO,OAAO,SAAS,CAAC,MAAM,KAAK,UAAU;oDAC/C,OAAO,OAAO,SAAS,CAAC,MAAM;gDAChC;gDAEA,qCAAqC;gDACrC,MAAM,gBAAgB,SAAS,aAAa,CAAC,CAAC,iBAAiB,EAAE,OAAO,EAAE,CAAC,OAAO,CAAC;gDACnF,IAAI,iBAAiB,cAAc,WAAW,EAAE;oDAC9C,OAAO,cAAc,WAAW;gDAClC;gDAEA,uBAAuB;gDACvB,OAAO,OAAO,EAAE;4CAClB,CAAC;;;;;;;mCA/BE,OAAO,EAAE;;;;;4BAmCpB;;;;;;;;;;;;;;;;;;;;;;;AAOd", "debugId": null}}, {"offset": {"line": 3017, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/bloomgoo/gold-exchange-platform/frontend/components/common/selector/sort-selector.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { But<PERSON> } from '@/components/ui/button';\r\nimport {\r\n  Popover,\r\n  PopoverContent,\r\n  PopoverTrigger,\r\n} from '@/components/ui/popover';\r\nimport {\r\n  ArrowDownWideNarrow,\r\n  ArrowUpDown,\r\n  ArrowUpNarrowWide,\r\n  X,\r\n  Plus,\r\n  GripVertical,\r\n  Search\r\n} from 'lucide-react';\r\nimport { Input } from '@/components/ui/input';\r\nimport { SortingState, Table } from '@tanstack/react-table';\r\nimport { cn } from '@/lib/utils';\r\nimport { Badge } from '@/components/ui/badge';\r\n\r\nimport { useState, useMemo, useEffect } from 'react';\r\n\r\n// Định nghĩa kiểu dữ liệu cho thuộc tính meta của cột\r\ndeclare module '@tanstack/react-table' {\r\n  interface ColumnMeta<TData, TValue> {\r\n    isSticky?: boolean;\r\n    position?: 'left' | 'right';\r\n    header?: string;\r\n  }\r\n}\r\nimport {\r\n  DndContext,\r\n  closestCenter,\r\n  KeyboardSensor,\r\n  PointerSensor,\r\n  useSensor,\r\n  useSensors,\r\n  DragEndEvent\r\n} from '@dnd-kit/core';\r\nimport {\r\n  arrayMove,\r\n  SortableContext,\r\n  sortableKeyboardCoordinates,\r\n  useSortable,\r\n  verticalListSortingStrategy,\r\n} from '@dnd-kit/sortable';\r\nimport { CSS } from '@dnd-kit/utilities';\r\n\r\ninterface SortSelectorProps<TData> {\r\n  table: Table<TData>;\r\n  className?: string;\r\n  buttonClassName?: string;\r\n  contentClassName?: string;\r\n  maxSortFields?: number;\r\n}\r\n\r\n// Component cho mỗi mục sort có thể kéo thả\r\ninterface SortableItemProps {\r\n  id: string;\r\n  columnName: string;\r\n  direction: 'asc' | 'desc';\r\n  onDirectionChange: (direction: 'asc' | 'desc') => void;\r\n  onRemove: () => void;\r\n}\r\n\r\nfunction SortableItem({\r\n  id,\r\n  columnName,\r\n  direction,\r\n  onDirectionChange,\r\n  onRemove\r\n}: SortableItemProps) {\r\n  const {\r\n    attributes,\r\n    listeners,\r\n    setNodeRef,\r\n    transform,\r\n    transition,\r\n  } = useSortable({ id });\r\n\r\n  const style = {\r\n    transform: CSS.Transform.toString(transform),\r\n    transition,\r\n  };\r\n\r\n  return (\r\n    <div\r\n      ref={setNodeRef}\r\n      style={style}\r\n      className=\"flex items-center justify-between p-2 mb-1 bg-muted/50 rounded-md\"\r\n    >\r\n      <div className=\"flex items-center gap-2\">\r\n        <button\r\n          {...attributes}\r\n          {...listeners}\r\n          className=\"cursor-grab touch-none p-1 rounded hover:bg-muted\"\r\n        >\r\n          <GripVertical className=\"h-4 w-4 text-muted-foreground\" />\r\n        </button>\r\n        <span className=\"text-sm\">{columnName}</span>\r\n      </div>\r\n      <div className=\"flex items-center gap-1\">\r\n        <Button\r\n          variant=\"ghost\"\r\n          size=\"icon\"\r\n          className=\"h-7 w-7\"\r\n          onClick={() => onDirectionChange(direction === 'asc' ? 'desc' : 'asc')}\r\n        >\r\n          {direction === 'asc' ? (\r\n            <ArrowDownWideNarrow className=\"h-4 w-4\" />\r\n          ) : (\r\n            <ArrowUpNarrowWide className=\"h-4 w-4\" />\r\n          )}\r\n        </Button>\r\n        <Button\r\n          variant=\"ghost\"\r\n          size=\"icon\"\r\n          className=\"h-7 w-7 text-destructive\"\r\n          onClick={onRemove}\r\n        >\r\n          <X className=\"h-4 w-4\" />\r\n        </Button>\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n\r\nexport function SortSelector<TData>({\r\n  table,\r\n  className,\r\n  buttonClassName,\r\n  contentClassName = 'w-[300px]',\r\n  maxSortFields = 3,\r\n}: SortSelectorProps<TData>) {\r\n  const [open, setOpen] = useState(false);\r\n  const [searchQuery, setSearchQuery] = useState('');\r\n  const [tempSorting, setTempSorting] = useState<SortingState>([]);\r\n\r\n  // Lấy tất cả các cột có thể sắp xếp\r\n  const sortableColumns = useMemo(() => {\r\n    return table.getAllColumns().filter(column =>\r\n      column.getCanSort() && column.getIsVisible()\r\n    );\r\n  }, [table]);\r\n\r\n  // Lọc các cột dựa trên từ khóa tìm kiếm\r\n  const filteredColumns = useMemo(() => {\r\n    if (!searchQuery.trim()) {\r\n      return sortableColumns;\r\n    }\r\n\r\n    const query = searchQuery.toLowerCase().trim();\r\n    return sortableColumns.filter(column => {\r\n      // Lấy tên cột thân thiện từ header\r\n      let header = column.id;\r\n\r\n      // Ưu tiên sử dụng meta.header\r\n      if (column.columnDef.meta?.header) {\r\n        header = column.columnDef.meta.header;\r\n      }\r\n      // Nếu header là string, sử dụng trực tiếp\r\n      else if (typeof column.columnDef.header === 'string') {\r\n        header = column.columnDef.header;\r\n      }\r\n      // Nếu header là function, tìm kiếm phần tử span chứa tên cột\r\n      else if (typeof column.columnDef.header === 'function') {\r\n        const headerElement = document.querySelector(`[data-column-id=\"${column.id}\"] span`);\r\n        if (headerElement && headerElement.textContent) {\r\n          header = headerElement.textContent;\r\n        }\r\n      }\r\n\r\n      return header.toString().toLowerCase().includes(query);\r\n    });\r\n  }, [sortableColumns, searchQuery]);\r\n\r\n  // Khi mở dropdown, sao chép trạng thái sắp xếp hiện tại vào tempSorting\r\n  useEffect(() => {\r\n    if (open) {\r\n      setTempSorting(table.getState().sorting);\r\n    }\r\n  }, [open, table]);\r\n\r\n  // Lấy thông tin các cột đang được sắp xếp (dựa trên tempSorting)\r\n  const sortedColumns = useMemo(() => {\r\n    return tempSorting.map(sort => {\r\n      const column = table.getColumn(sort.id);\r\n\r\n      // Lấy tên cột thân thiện từ header\r\n      let columnName = sort.id;\r\n\r\n      if (column) {\r\n        // Ưu tiên sử dụng meta.header\r\n        if (column.columnDef.meta?.header) {\r\n          columnName = column.columnDef.meta.header;\r\n        }\r\n        // Nếu header là string, sử dụng trực tiếp\r\n        else if (typeof column.columnDef.header === 'string') {\r\n          columnName = column.columnDef.header;\r\n        }\r\n        // Nếu header là function, tìm kiếm phần tử span chứa tên cột\r\n        else if (typeof column.columnDef.header === 'function') {\r\n          const headerElement = document.querySelector(`[data-column-id=\"${sort.id}\"] span`);\r\n          if (headerElement && headerElement.textContent) {\r\n            columnName = headerElement.textContent;\r\n          }\r\n        }\r\n      }\r\n\r\n      return {\r\n        id: sort.id,\r\n        name: columnName,\r\n        direction: (sort.desc ? 'desc' : 'asc') as 'desc' | 'asc'\r\n      };\r\n    });\r\n  }, [tempSorting, table]);\r\n\r\n  // Sensors cho DnD\r\n  const sensors = useSensors(\r\n    useSensor(PointerSensor),\r\n    useSensor(KeyboardSensor, {\r\n      coordinateGetter: sortableKeyboardCoordinates,\r\n    })\r\n  );\r\n\r\n  // Xử lý khi kết thúc kéo thả\r\n  const handleDragEnd = (event: DragEndEvent) => {\r\n    const { active, over } = event;\r\n\r\n    if (over && active.id !== over.id) {\r\n      const oldIndex = sortedColumns.findIndex(item => item.id === active.id);\r\n      const newIndex = sortedColumns.findIndex(item => item.id === over.id);\r\n\r\n      const newSortedColumns = arrayMove(sortedColumns, oldIndex, newIndex);\r\n\r\n      // Cập nhật tempSorting\r\n      const newSorting = newSortedColumns.map(col => ({\r\n        id: col.id,\r\n        desc: col.direction === 'desc'\r\n      }));\r\n\r\n      setTempSorting(newSorting);\r\n    }\r\n  };\r\n\r\n  // Xử lý thêm tiêu chí sắp xếp mới\r\n  const handleAddSort = (columnId: string) => {\r\n    // Kiểm tra xem cột đã được sắp xếp chưa\r\n    const existingSort = tempSorting.find(sort => sort.id === columnId);\r\n\r\n    if (existingSort) {\r\n      // Nếu đã có, thay đổi hướng sắp xếp\r\n      const newSorting = tempSorting.map(sort => {\r\n        if (sort.id === columnId) {\r\n          return { ...sort, desc: !sort.desc };\r\n        }\r\n        return sort;\r\n      });\r\n\r\n      setTempSorting(newSorting);\r\n    } else if (tempSorting.length < maxSortFields) {\r\n      // Nếu chưa có và chưa đạt giới hạn, thêm mới\r\n      const newSorting = [\r\n        ...tempSorting,\r\n        { id: columnId, desc: false }\r\n      ];\r\n\r\n      setTempSorting(newSorting);\r\n    }\r\n\r\n    // Xóa từ khóa tìm kiếm sau khi chọn\r\n    setSearchQuery('');\r\n  };\r\n\r\n  // Xử lý thay đổi hướng sắp xếp\r\n  const handleDirectionChange = (columnId: string, direction: 'asc' | 'desc') => {\r\n    const newSorting = tempSorting.map(sort => {\r\n      if (sort.id === columnId) {\r\n        return { ...sort, desc: direction === 'desc' };\r\n      }\r\n      return sort;\r\n    });\r\n\r\n    setTempSorting(newSorting);\r\n  };\r\n\r\n  // Xử lý xóa tiêu chí sắp xếp\r\n  const handleRemoveSort = (columnId: string) => {\r\n    const newSorting = tempSorting.filter(sort => sort.id !== columnId);\r\n    setTempSorting(newSorting);\r\n  };\r\n\r\n  // Xử lý xóa tất cả tiêu chí sắp xếp\r\n  const handleClearAll = () => {\r\n    setTempSorting([]);\r\n  };\r\n\r\n  // Xử lý áp dụng các thay đổi\r\n  const handleApply = () => {\r\n    table.setSorting(tempSorting);\r\n    setOpen(false);\r\n  };\r\n\r\n  // Xử lý hủy thay đổi\r\n  const handleCancel = () => {\r\n    setTempSorting(table.getState().sorting);\r\n    setOpen(false);\r\n  };\r\n\r\n  // Lấy icon sắp xếp cho button\r\n  const getSortIcon = () => {\r\n    const currentSorting = table.getState().sorting;\r\n\r\n    if (currentSorting.length === 0) {\r\n      return <ArrowUpDown className=\"h-4 w-4\" />;\r\n    }\r\n\r\n    if (currentSorting.length === 1) {\r\n      return currentSorting[0].desc ?\r\n        <ArrowUpNarrowWide className=\"h-4 w-4\" /> :\r\n        <ArrowDownWideNarrow className=\"h-4 w-4\" />;\r\n    }\r\n\r\n    // Nếu có nhiều tiêu chí sắp xếp, hiển thị số lượng\r\n    return (\r\n      <div className=\"relative\">\r\n        <ArrowUpDown className=\"h-4 w-4\" />\r\n        <Badge\r\n          className=\"absolute -top-2 -right-2 h-4 w-4 flex items-center justify-center p-0 text-[10px]\"\r\n          variant=\"default\"\r\n        >\r\n          {currentSorting.length}\r\n        </Badge>\r\n      </div>\r\n    );\r\n  };\r\n\r\n  return (\r\n    <div className={className}>\r\n      <Popover open={open} onOpenChange={setOpen}>\r\n        <PopoverTrigger asChild>\r\n          <Button\r\n            variant=\"ghost\"\r\n            size=\"icon\"\r\n            className={cn(\"relative\", buttonClassName, table.getState().sorting.length > 0 && \"bg-accent\")}\r\n          >\r\n            {getSortIcon()}\r\n          </Button>\r\n        </PopoverTrigger>\r\n        <PopoverContent align=\"end\" className={contentClassName} onEscapeKeyDown={handleCancel} onInteractOutside={(e) => e.preventDefault()}>\r\n          <div className=\"p-0\">\r\n            <h4 className=\"mb-2 text-sm font-medium\">Sắp xếp theo</h4>\r\n\r\n            {/* Danh sách các tiêu chí sắp xếp hiện tại */}\r\n            {sortedColumns.length > 0 ? (\r\n              <div className=\"mb-2\">\r\n                <DndContext\r\n                  sensors={sensors}\r\n                  collisionDetection={closestCenter}\r\n                  onDragEnd={handleDragEnd}\r\n                >\r\n                  <SortableContext\r\n                    items={sortedColumns.map(col => col.id)}\r\n                    strategy={verticalListSortingStrategy}\r\n                  >\r\n                    {sortedColumns.map((column) => (\r\n                      <SortableItem\r\n                        key={column.id}\r\n                        id={column.id}\r\n                        columnName={column.name}\r\n                        direction={column.direction}\r\n                        onDirectionChange={(direction) =>\r\n                          handleDirectionChange(column.id, direction)\r\n                        }\r\n                        onRemove={() => handleRemoveSort(column.id)}\r\n                      />\r\n                    ))}\r\n                  </SortableContext>\r\n                </DndContext>\r\n              </div>\r\n            ) : (\r\n              <div className=\"text-sm text-muted-foreground mb-2 py-2 text-center\">\r\n                Chưa có tiêu chí sắp xếp nào\r\n              </div>\r\n            )}\r\n\r\n            {/* Phần chọn trường sắp xếp */}\r\n            {tempSorting.length < maxSortFields && (\r\n              <div className=\"mb-2\">\r\n                <div className=\"text-xs font-medium mb-1 text-muted-foreground\">Thêm tiêu chí sắp xếp</div>\r\n\r\n                {/* Ô tìm kiếm trường */}\r\n                <div className=\"relative mb-2\">\r\n                  <Search className=\"absolute left-2 top-1/2 h-3.5 w-3.5 -translate-y-1/2 text-muted-foreground\" />\r\n                  <Input\r\n                    placeholder=\"Tìm kiếm trường...\"\r\n                    value={searchQuery}\r\n                    onChange={(e) => setSearchQuery(e.target.value)}\r\n                    className=\"pl-7 h-8 text-sm\"\r\n                  />\r\n                </div>\r\n\r\n                {/* Danh sách các trường có thể sắp xếp */}\r\n                <div className=\"max-h-[150px] overflow-y-auto border rounded-md\">\r\n                  {filteredColumns.length === 0 ? (\r\n                    <div className=\"p-2 text-center text-sm text-muted-foreground\">\r\n                      Không tìm thấy trường nào\r\n                    </div>\r\n                  ) : (\r\n                    <div className=\"p-1\">\r\n                      {filteredColumns.map(column => {\r\n                        const isSelected = tempSorting.some(sort => sort.id === column.id);\r\n                        return (\r\n                          <button\r\n                            key={column.id}\r\n                            onClick={() => handleAddSort(column.id)}\r\n                            disabled={isSelected}\r\n                            className={cn(\r\n                              \"w-full text-left px-2 py-1.5 text-sm rounded-md hover:bg-accent flex items-center justify-between\",\r\n                              isSelected && \"opacity-50 cursor-not-allowed\"\r\n                            )}\r\n                          >\r\n                            <span>\r\n                              {(() => {\r\n                                // Ưu tiên sử dụng meta.header\r\n                                if (column.columnDef.meta?.header) {\r\n                                  return column.columnDef.meta.header;\r\n                                }\r\n\r\n                                // Nếu header là string, sử dụng trực tiếp\r\n                                if (typeof column.columnDef.header === 'string') {\r\n                                  return column.columnDef.header;\r\n                                }\r\n\r\n                                // Tìm kiếm phần tử span trong header\r\n                                const headerElement = document.querySelector(`[data-column-id=\"${column.id}\"] span`);\r\n                                if (headerElement && headerElement.textContent) {\r\n                                  return headerElement.textContent;\r\n                                }\r\n\r\n                                // Fallback: Sử dụng ID\r\n                                return column.id;\r\n                              })()}\r\n                            </span>\r\n                          </button>\r\n                        );\r\n                      })}\r\n                    </div>\r\n                  )}\r\n                </div>\r\n              </div>\r\n            )}\r\n\r\n            {/* Nút Áp dụng và Xóa tất cả */}\r\n            <div className=\"flex items-center justify-between mt-4\">\r\n              <Button\r\n                onClick={handleApply}\r\n                size=\"sm\"\r\n                className=\"flex-1 mr-1\"\r\n              >\r\n                Áp dụng\r\n              </Button>\r\n\r\n              <Button\r\n                onClick={handleClearAll}\r\n                variant=\"link\"\r\n                size=\"sm\"\r\n                className=\"flex-1 ml-1 text-destructive\"\r\n                disabled={tempSorting.length === 0}\r\n              >\r\n                Xóa tất cả\r\n              </Button>\r\n            </div>\r\n          </div>\r\n        </PopoverContent>\r\n      </Popover>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAKA;AAAA;AAAA;AAAA;AAAA;AAAA;AASA;AAEA;AAAA;AACA;AAEA;AAUA;AASA;AAOA;AAhDA;;;;;;;;;;;;AAmEA,SAAS,aAAa,EACpB,EAAE,EACF,UAAU,EACV,SAAS,EACT,iBAAiB,EACjB,QAAQ,EACU;IAClB,MAAM,EACJ,UAAU,EACV,SAAS,EACT,UAAU,EACV,SAAS,EACT,UAAU,EACX,GAAG,CAAA,GAAA,gRAAA,CAAA,cAAW,AAAD,EAAE;QAAE;IAAG;IAErB,MAAM,QAAQ;QACZ,WAAW,iQAAA,CAAA,MAAG,CAAC,SAAS,CAAC,QAAQ,CAAC;QAClC;IACF;IAEA,qBACE,uVAAC;QACC,KAAK;QACL,OAAO;QACP,WAAU;;0BAEV,uVAAC;gBAAI,WAAU;;kCACb,uVAAC;wBACE,GAAG,UAAU;wBACb,GAAG,SAAS;wBACb,WAAU;kCAEV,cAAA,uVAAC,0SAAA,CAAA,eAAY;4BAAC,WAAU;;;;;;;;;;;kCAE1B,uVAAC;wBAAK,WAAU;kCAAW;;;;;;;;;;;;0BAE7B,uVAAC;gBAAI,WAAU;;kCACb,uVAAC,2HAAA,CAAA,SAAM;wBACL,SAAQ;wBACR,MAAK;wBACL,WAAU;wBACV,SAAS,IAAM,kBAAkB,cAAc,QAAQ,SAAS;kCAE/D,cAAc,sBACb,uVAAC,gUAAA,CAAA,sBAAmB;4BAAC,WAAU;;;;;iDAE/B,uVAAC,4TAAA,CAAA,oBAAiB;4BAAC,WAAU;;;;;;;;;;;kCAGjC,uVAAC,2HAAA,CAAA,SAAM;wBACL,SAAQ;wBACR,MAAK;wBACL,WAAU;wBACV,SAAS;kCAET,cAAA,uVAAC,gRAAA,CAAA,IAAC;4BAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;AAKvB;AAEO,SAAS,aAAoB,EAClC,KAAK,EACL,SAAS,EACT,eAAe,EACf,mBAAmB,WAAW,EAC9B,gBAAgB,CAAC,EACQ;IACzB,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,8SAAA,CAAA,WAAQ,AAAD,EAAE;IACjC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,8SAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,8SAAA,CAAA,WAAQ,AAAD,EAAgB,EAAE;IAE/D,oCAAoC;IACpC,MAAM,kBAAkB,CAAA,GAAA,8SAAA,CAAA,UAAO,AAAD,EAAE;QAC9B,OAAO,MAAM,aAAa,GAAG,MAAM,CAAC,CAAA,SAClC,OAAO,UAAU,MAAM,OAAO,YAAY;IAE9C,GAAG;QAAC;KAAM;IAEV,wCAAwC;IACxC,MAAM,kBAAkB,CAAA,GAAA,8SAAA,CAAA,UAAO,AAAD,EAAE;QAC9B,IAAI,CAAC,YAAY,IAAI,IAAI;YACvB,OAAO;QACT;QAEA,MAAM,QAAQ,YAAY,WAAW,GAAG,IAAI;QAC5C,OAAO,gBAAgB,MAAM,CAAC,CAAA;YAC5B,mCAAmC;YACnC,IAAI,SAAS,OAAO,EAAE;YAEtB,8BAA8B;YAC9B,IAAI,OAAO,SAAS,CAAC,IAAI,EAAE,QAAQ;gBACjC,SAAS,OAAO,SAAS,CAAC,IAAI,CAAC,MAAM;YACvC,OAEK,IAAI,OAAO,OAAO,SAAS,CAAC,MAAM,KAAK,UAAU;gBACpD,SAAS,OAAO,SAAS,CAAC,MAAM;YAClC,OAEK,IAAI,OAAO,OAAO,SAAS,CAAC,MAAM,KAAK,YAAY;gBACtD,MAAM,gBAAgB,SAAS,aAAa,CAAC,CAAC,iBAAiB,EAAE,OAAO,EAAE,CAAC,OAAO,CAAC;gBACnF,IAAI,iBAAiB,cAAc,WAAW,EAAE;oBAC9C,SAAS,cAAc,WAAW;gBACpC;YACF;YAEA,OAAO,OAAO,QAAQ,GAAG,WAAW,GAAG,QAAQ,CAAC;QAClD;IACF,GAAG;QAAC;QAAiB;KAAY;IAEjC,wEAAwE;IACxE,CAAA,GAAA,8SAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,MAAM;YACR,eAAe,MAAM,QAAQ,GAAG,OAAO;QACzC;IACF,GAAG;QAAC;QAAM;KAAM;IAEhB,iEAAiE;IACjE,MAAM,gBAAgB,CAAA,GAAA,8SAAA,CAAA,UAAO,AAAD,EAAE;QAC5B,OAAO,YAAY,GAAG,CAAC,CAAA;YACrB,MAAM,SAAS,MAAM,SAAS,CAAC,KAAK,EAAE;YAEtC,mCAAmC;YACnC,IAAI,aAAa,KAAK,EAAE;YAExB,IAAI,QAAQ;gBACV,8BAA8B;gBAC9B,IAAI,OAAO,SAAS,CAAC,IAAI,EAAE,QAAQ;oBACjC,aAAa,OAAO,SAAS,CAAC,IAAI,CAAC,MAAM;gBAC3C,OAEK,IAAI,OAAO,OAAO,SAAS,CAAC,MAAM,KAAK,UAAU;oBACpD,aAAa,OAAO,SAAS,CAAC,MAAM;gBACtC,OAEK,IAAI,OAAO,OAAO,SAAS,CAAC,MAAM,KAAK,YAAY;oBACtD,MAAM,gBAAgB,SAAS,aAAa,CAAC,CAAC,iBAAiB,EAAE,KAAK,EAAE,CAAC,OAAO,CAAC;oBACjF,IAAI,iBAAiB,cAAc,WAAW,EAAE;wBAC9C,aAAa,cAAc,WAAW;oBACxC;gBACF;YACF;YAEA,OAAO;gBACL,IAAI,KAAK,EAAE;gBACX,MAAM;gBACN,WAAY,KAAK,IAAI,GAAG,SAAS;YACnC;QACF;IACF,GAAG;QAAC;QAAa;KAAM;IAEvB,kBAAkB;IAClB,MAAM,UAAU,CAAA,GAAA,wQAAA,CAAA,aAAU,AAAD,EACvB,CAAA,GAAA,wQAAA,CAAA,YAAS,AAAD,EAAE,wQAAA,CAAA,gBAAa,GACvB,CAAA,GAAA,wQAAA,CAAA,YAAS,AAAD,EAAE,wQAAA,CAAA,iBAAc,EAAE;QACxB,kBAAkB,gRAAA,CAAA,8BAA2B;IAC/C;IAGF,6BAA6B;IAC7B,MAAM,gBAAgB,CAAC;QACrB,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,GAAG;QAEzB,IAAI,QAAQ,OAAO,EAAE,KAAK,KAAK,EAAE,EAAE;YACjC,MAAM,WAAW,cAAc,SAAS,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK,OAAO,EAAE;YACtE,MAAM,WAAW,cAAc,SAAS,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK,KAAK,EAAE;YAEpE,MAAM,mBAAmB,CAAA,GAAA,gRAAA,CAAA,YAAS,AAAD,EAAE,eAAe,UAAU;YAE5D,uBAAuB;YACvB,MAAM,aAAa,iBAAiB,GAAG,CAAC,CAAA,MAAO,CAAC;oBAC9C,IAAI,IAAI,EAAE;oBACV,MAAM,IAAI,SAAS,KAAK;gBAC1B,CAAC;YAED,eAAe;QACjB;IACF;IAEA,kCAAkC;IAClC,MAAM,gBAAgB,CAAC;QACrB,wCAAwC;QACxC,MAAM,eAAe,YAAY,IAAI,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK;QAE1D,IAAI,cAAc;YAChB,oCAAoC;YACpC,MAAM,aAAa,YAAY,GAAG,CAAC,CAAA;gBACjC,IAAI,KAAK,EAAE,KAAK,UAAU;oBACxB,OAAO;wBAAE,GAAG,IAAI;wBAAE,MAAM,CAAC,KAAK,IAAI;oBAAC;gBACrC;gBACA,OAAO;YACT;YAEA,eAAe;QACjB,OAAO,IAAI,YAAY,MAAM,GAAG,eAAe;YAC7C,6CAA6C;YAC7C,MAAM,aAAa;mBACd;gBACH;oBAAE,IAAI;oBAAU,MAAM;gBAAM;aAC7B;YAED,eAAe;QACjB;QAEA,oCAAoC;QACpC,eAAe;IACjB;IAEA,+BAA+B;IAC/B,MAAM,wBAAwB,CAAC,UAAkB;QAC/C,MAAM,aAAa,YAAY,GAAG,CAAC,CAAA;YACjC,IAAI,KAAK,EAAE,KAAK,UAAU;gBACxB,OAAO;oBAAE,GAAG,IAAI;oBAAE,MAAM,cAAc;gBAAO;YAC/C;YACA,OAAO;QACT;QAEA,eAAe;IACjB;IAEA,6BAA6B;IAC7B,MAAM,mBAAmB,CAAC;QACxB,MAAM,aAAa,YAAY,MAAM,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK;QAC1D,eAAe;IACjB;IAEA,oCAAoC;IACpC,MAAM,iBAAiB;QACrB,eAAe,EAAE;IACnB;IAEA,6BAA6B;IAC7B,MAAM,cAAc;QAClB,MAAM,UAAU,CAAC;QACjB,QAAQ;IACV;IAEA,qBAAqB;IACrB,MAAM,eAAe;QACnB,eAAe,MAAM,QAAQ,GAAG,OAAO;QACvC,QAAQ;IACV;IAEA,8BAA8B;IAC9B,MAAM,cAAc;QAClB,MAAM,iBAAiB,MAAM,QAAQ,GAAG,OAAO;QAE/C,IAAI,eAAe,MAAM,KAAK,GAAG;YAC/B,qBAAO,uVAAC,4SAAA,CAAA,cAAW;gBAAC,WAAU;;;;;;QAChC;QAEA,IAAI,eAAe,MAAM,KAAK,GAAG;YAC/B,OAAO,cAAc,CAAC,EAAE,CAAC,IAAI,iBAC3B,uVAAC,4TAAA,CAAA,oBAAiB;gBAAC,WAAU;;;;;qCAC7B,uVAAC,gUAAA,CAAA,sBAAmB;gBAAC,WAAU;;;;;;QACnC;QAEA,mDAAmD;QACnD,qBACE,uVAAC;YAAI,WAAU;;8BACb,uVAAC,4SAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;8BACvB,uVAAC,0HAAA,CAAA,QAAK;oBACJ,WAAU;oBACV,SAAQ;8BAEP,eAAe,MAAM;;;;;;;;;;;;IAI9B;IAEA,qBACE,uVAAC;QAAI,WAAW;kBACd,cAAA,uVAAC,4HAAA,CAAA,UAAO;YAAC,MAAM;YAAM,cAAc;;8BACjC,uVAAC,4HAAA,CAAA,iBAAc;oBAAC,OAAO;8BACrB,cAAA,uVAAC,2HAAA,CAAA,SAAM;wBACL,SAAQ;wBACR,MAAK;wBACL,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EAAE,YAAY,iBAAiB,MAAM,QAAQ,GAAG,OAAO,CAAC,MAAM,GAAG,KAAK;kCAEjF;;;;;;;;;;;8BAGL,uVAAC,4HAAA,CAAA,iBAAc;oBAAC,OAAM;oBAAM,WAAW;oBAAkB,iBAAiB;oBAAc,mBAAmB,CAAC,IAAM,EAAE,cAAc;8BAChI,cAAA,uVAAC;wBAAI,WAAU;;0CACb,uVAAC;gCAAG,WAAU;0CAA2B;;;;;;4BAGxC,cAAc,MAAM,GAAG,kBACtB,uVAAC;gCAAI,WAAU;0CACb,cAAA,uVAAC,wQAAA,CAAA,aAAU;oCACT,SAAS;oCACT,oBAAoB,wQAAA,CAAA,gBAAa;oCACjC,WAAW;8CAEX,cAAA,uVAAC,gRAAA,CAAA,kBAAe;wCACd,OAAO,cAAc,GAAG,CAAC,CAAA,MAAO,IAAI,EAAE;wCACtC,UAAU,gRAAA,CAAA,8BAA2B;kDAEpC,cAAc,GAAG,CAAC,CAAC,uBAClB,uVAAC;gDAEC,IAAI,OAAO,EAAE;gDACb,YAAY,OAAO,IAAI;gDACvB,WAAW,OAAO,SAAS;gDAC3B,mBAAmB,CAAC,YAClB,sBAAsB,OAAO,EAAE,EAAE;gDAEnC,UAAU,IAAM,iBAAiB,OAAO,EAAE;+CAPrC,OAAO,EAAE;;;;;;;;;;;;;;;;;;;qDAcxB,uVAAC;gCAAI,WAAU;0CAAsD;;;;;;4BAMtE,YAAY,MAAM,GAAG,+BACpB,uVAAC;gCAAI,WAAU;;kDACb,uVAAC;wCAAI,WAAU;kDAAiD;;;;;;kDAGhE,uVAAC;wCAAI,WAAU;;0DACb,uVAAC,0RAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;0DAClB,uVAAC,0HAAA,CAAA,QAAK;gDACJ,aAAY;gDACZ,OAAO;gDACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;gDAC9C,WAAU;;;;;;;;;;;;kDAKd,uVAAC;wCAAI,WAAU;kDACZ,gBAAgB,MAAM,KAAK,kBAC1B,uVAAC;4CAAI,WAAU;sDAAgD;;;;;iEAI/D,uVAAC;4CAAI,WAAU;sDACZ,gBAAgB,GAAG,CAAC,CAAA;gDACnB,MAAM,aAAa,YAAY,IAAI,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK,OAAO,EAAE;gDACjE,qBACE,uVAAC;oDAEC,SAAS,IAAM,cAAc,OAAO,EAAE;oDACtC,UAAU;oDACV,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EACV,qGACA,cAAc;8DAGhB,cAAA,uVAAC;kEACE,CAAC;4DACA,8BAA8B;4DAC9B,IAAI,OAAO,SAAS,CAAC,IAAI,EAAE,QAAQ;gEACjC,OAAO,OAAO,SAAS,CAAC,IAAI,CAAC,MAAM;4DACrC;4DAEA,0CAA0C;4DAC1C,IAAI,OAAO,OAAO,SAAS,CAAC,MAAM,KAAK,UAAU;gEAC/C,OAAO,OAAO,SAAS,CAAC,MAAM;4DAChC;4DAEA,qCAAqC;4DACrC,MAAM,gBAAgB,SAAS,aAAa,CAAC,CAAC,iBAAiB,EAAE,OAAO,EAAE,CAAC,OAAO,CAAC;4DACnF,IAAI,iBAAiB,cAAc,WAAW,EAAE;gEAC9C,OAAO,cAAc,WAAW;4DAClC;4DAEA,uBAAuB;4DACvB,OAAO,OAAO,EAAE;wDAClB,CAAC;;;;;;mDA5BE,OAAO,EAAE;;;;;4CAgCpB;;;;;;;;;;;;;;;;;0CAQV,uVAAC;gCAAI,WAAU;;kDACb,uVAAC,2HAAA,CAAA,SAAM;wCACL,SAAS;wCACT,MAAK;wCACL,WAAU;kDACX;;;;;;kDAID,uVAAC,2HAAA,CAAA,SAAM;wCACL,SAAS;wCACT,SAAQ;wCACR,MAAK;wCACL,WAAU;wCACV,UAAU,YAAY,MAAM,KAAK;kDAClC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASf", "debugId": null}}, {"offset": {"line": 3600, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/bloomgoo/gold-exchange-platform/frontend/components/common/data-table/table-toolbar.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport { ColumnVisibilitySelector } from \"@/components/common/selector/column-visibility-selector\"\r\nimport { SortSelector } from \"@/components/common/selector/sort-selector\"\r\nimport { Button } from \"@/components/ui/button\"\r\nimport { Input } from \"@/components/ui/input\"\r\nimport { cn } from \"@/lib/utils\"\r\nimport { Table } from \"@tanstack/react-table\"\r\nimport {\r\n    RefreshCcw,\r\n    Search\r\n} from \"lucide-react\"\r\n\r\ninterface TableToolbarProps<TData> {\r\n    table: Table<TData>\r\n    globalFilter: string\r\n    setGlobalFilter: (value: string) => void\r\n    onRefresh: () => void\r\n    isRefreshing?: boolean\r\n    searchPlaceholder?: string\r\n\r\n    onDeleteSelected?: () => void\r\n    isShowSelectedRows?: boolean\r\n    onShowSelectedRows?: () => void\r\n    // Slot tùy chỉnh để thêm nội dung vào bên trái của thanh tìm kiếm\r\n    beforeSearchSlot?: React.ReactNode\r\n    // Slot tùy chỉnh để thêm nội dung vào bên phải của thanh tìm kiếm\r\n    afterSearchSlot?: React.ReactNode\r\n    // Slot tùy chỉnh để thêm nội dung vào bên trái của các nút hành động\r\n    beforeActionsSlot?: React.ReactNode\r\n    // Slot tùy chỉnh để thêm nội dung vào bên phải của các nút hành động\r\n    afterActionsSlot?: React.ReactNode\r\n    // Tùy chỉnh class cho thanh công cụ\r\n    className?: string\r\n}\r\n\r\nexport function TableToolbar<TData>({\r\n    table,\r\n    globalFilter,\r\n    setGlobalFilter,\r\n    onRefresh,\r\n    isRefreshing = false,\r\n    searchPlaceholder = \"Tìm kiếm...\",\r\n\r\n    onDeleteSelected,\r\n    isShowSelectedRows,\r\n    onShowSelectedRows,\r\n    beforeSearchSlot,\r\n    afterSearchSlot,\r\n    beforeActionsSlot,\r\n    afterActionsSlot,\r\n    className,\r\n}: TableToolbarProps<TData>) {\r\n    const selectedRows = table.getSelectedRowModel().rows\r\n\r\n    return (\r\n        <div className={cn(\"flex items-center justify-between gap-2 px-4 py-2 border-b\", className)}>\r\n            {/* Left Side */}\r\n            <div className=\"flex items-center space-x-4 flex-1\">\r\n                {/* Slot trước thanh tìm kiếm */}\r\n                {beforeSearchSlot}\r\n\r\n                <div className=\"relative w-64\">\r\n                    <Search\r\n                        className=\"h-4 w-4 absolute left-2 top-1/2 transform -translate-y-1/2 text-muted-foreground\"\r\n                    />\r\n                    <Input\r\n                        placeholder={searchPlaceholder}\r\n                        className=\"pl-8 h-9\"\r\n                        value={globalFilter}\r\n                        onChange={(e) => setGlobalFilter(e.target.value)}\r\n                    />\r\n                </div>\r\n\r\n                {/* Slot sau thanh tìm kiếm */}\r\n                {afterSearchSlot}\r\n            </div>\r\n\r\n            {/* Right Side */}\r\n            <div className=\"flex items-center space-x-2\">\r\n                {/* Slot trước các nút hành động */}\r\n                {beforeActionsSlot}\r\n                {onDeleteSelected && (<></>)}\r\n\r\n                {/* <FilterSelector table={table} /> */}\r\n\r\n                <SortSelector table={table} />\r\n\r\n                <ColumnVisibilitySelector table={table} />\r\n\r\n                <Button\r\n                    variant=\"ghost\"\r\n                    size=\"icon\"\r\n                    onClick={onRefresh}\r\n                    disabled={isRefreshing}\r\n                >\r\n                    <RefreshCcw className={cn(\"h-4 w-4\", isRefreshing && \"animate-spin\")} />\r\n                </Button>\r\n\r\n                {/* Slot sau các nút hành động */}\r\n                {afterActionsSlot}\r\n            </div>\r\n        </div>\r\n    )\r\n}"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AAAA;AAEA;AAAA;AARA;;;;;;;;AAoCO,SAAS,aAAoB,EAChC,KAAK,EACL,YAAY,EACZ,eAAe,EACf,SAAS,EACT,eAAe,KAAK,EACpB,oBAAoB,aAAa,EAEjC,gBAAgB,EAChB,kBAAkB,EAClB,kBAAkB,EAClB,gBAAgB,EAChB,eAAe,EACf,iBAAiB,EACjB,gBAAgB,EAChB,SAAS,EACc;IACvB,MAAM,eAAe,MAAM,mBAAmB,GAAG,IAAI;IAErD,qBACI,uVAAC;QAAI,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EAAE,8DAA8D;;0BAE7E,uVAAC;gBAAI,WAAU;;oBAEV;kCAED,uVAAC;wBAAI,WAAU;;0CACX,uVAAC,0RAAA,CAAA,SAAM;gCACH,WAAU;;;;;;0CAEd,uVAAC,0HAAA,CAAA,QAAK;gCACF,aAAa;gCACb,WAAU;gCACV,OAAO;gCACP,UAAU,CAAC,IAAM,gBAAgB,EAAE,MAAM,CAAC,KAAK;;;;;;;;;;;;oBAKtD;;;;;;;0BAIL,uVAAC;gBAAI,WAAU;;oBAEV;oBACA,kCAAqB;kCAItB,uVAAC,qJAAA,CAAA,eAAY;wBAAC,OAAO;;;;;;kCAErB,uVAAC,qKAAA,CAAA,2BAAwB;wBAAC,OAAO;;;;;;kCAEjC,uVAAC,2HAAA,CAAA,SAAM;wBACH,SAAQ;wBACR,MAAK;wBACL,SAAS;wBACT,UAAU;kCAEV,cAAA,uVAAC,sSAAA,CAAA,aAAU;4BAAC,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EAAE,WAAW,gBAAgB;;;;;;;;;;;oBAIxD;;;;;;;;;;;;;AAIjB", "debugId": null}}, {"offset": {"line": 3718, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/bloomgoo/gold-exchange-platform/frontend/components/common/admin/ebanks/components/status-tabs.tsx"], "sourcesContent": ["/**\r\n * StatusTabs - Component tabs để filter theo trạng thái\r\n */\r\n\r\n'use client';\r\n\r\nimport { cn } from '@/lib/utils';\r\nimport { Badge } from '@/components/ui/badge';\r\nimport type { StatusFilter, StatusCounts } from '../types';\r\n\r\ninterface StatusTabsProps {\r\n  currentStatus: StatusFilter;\r\n  onStatusChange: (status: StatusFilter) => void;\r\n  counts: StatusCounts;\r\n  className?: string;\r\n}\r\n\r\nexport function StatusTabs({\r\n  currentStatus,\r\n  onStatusChange,\r\n  counts,\r\n  className\r\n}: StatusTabsProps) {\r\n  const tabs = [\r\n    {\r\n      id: 'all' as const,\r\n      label: 'Tất cả',\r\n      count: counts.all,\r\n    },\r\n    {\r\n      id: 'active' as const,\r\n      label: 'Đ<PERSON> kích hoạt',\r\n      count: counts.active,\r\n    },\r\n    {\r\n      id: 'inactive' as const,\r\n      label: 'Vô hiệu hóa',\r\n      count: counts.inactive,\r\n    }\r\n  ];\r\n\r\n  return (\r\n    <div className={cn(\"flex items-center space-x-1 bg-muted/50 p-1 rounded-md\", className)}>\r\n      {tabs.map((tab) => (\r\n        <button\r\n          key={tab.id}\r\n          onClick={() => onStatusChange(tab.id)}\r\n          className={cn(\r\n            \"flex items-center px-3 py-1.5 text-sm font-medium rounded-md transition-colors\",\r\n            currentStatus === tab.id\r\n              ? \"bg-background text-foreground shadow-sm\"\r\n              : \"text-muted-foreground hover:bg-background/50\"\r\n          )}\r\n        >\r\n          {tab.label}\r\n          <Badge\r\n            variant=\"secondary\"\r\n            className={cn(\r\n              \"ml-2 transition-colors\",\r\n              currentStatus === tab.id\r\n                ? \"bg-primary text-primary-foreground font-semibold\"\r\n                : \"bg-muted text-muted-foreground\"\r\n            )}\r\n          >\r\n            {tab.count}\r\n          </Badge>\r\n        </button>\r\n      ))}\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": "AAAA;;CAEC;;;;AAID;AAAA;AACA;AAHA;;;;AAaO,SAAS,WAAW,EACzB,aAAa,EACb,cAAc,EACd,MAAM,EACN,SAAS,EACO;IAChB,MAAM,OAAO;QACX;YACE,IAAI;YACJ,OAAO;YACP,OAAO,OAAO,GAAG;QACnB;QACA;YACE,IAAI;YACJ,OAAO;YACP,OAAO,OAAO,MAAM;QACtB;QACA;YACE,IAAI;YACJ,OAAO;YACP,OAAO,OAAO,QAAQ;QACxB;KACD;IAED,qBACE,uVAAC;QAAI,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EAAE,0DAA0D;kBAC1E,KAAK,GAAG,CAAC,CAAC,oBACT,uVAAC;gBAEC,SAAS,IAAM,eAAe,IAAI,EAAE;gBACpC,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EACV,kFACA,kBAAkB,IAAI,EAAE,GACpB,4CACA;;oBAGL,IAAI,KAAK;kCACV,uVAAC,0HAAA,CAAA,QAAK;wBACJ,SAAQ;wBACR,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EACV,0BACA,kBAAkB,IAAI,EAAE,GACpB,qDACA;kCAGL,IAAI,KAAK;;;;;;;eAnBP,IAAI,EAAE;;;;;;;;;;AAyBrB", "debugId": null}}, {"offset": {"line": 3783, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/bloomgoo/gold-exchange-platform/frontend/components/common/admin/ebanks/components/bank-toolbar.tsx"], "sourcesContent": ["/**\r\n * BankToolbar - Toolbar component với search và filters\r\n */\r\n\r\n'use client';\r\n\r\nimport { Table } from '@tanstack/react-table';\r\nimport { TableToolbar } from '../../../data-table/table-toolbar';\r\nimport { StatusTabs } from './status-tabs';\r\nimport type { BankDto, StatusFilter, StatusCounts } from '../types';\r\n\r\ninterface BankToolbarProps {\r\n  table: Table<BankDto>;\r\n  globalFilter: string;\r\n  setGlobalFilter: (value: string) => void;\r\n  onRefresh: () => void;\r\n  isRefreshing: boolean;\r\n  isShowSelectedRows: boolean;\r\n  onShowSelectedRows: () => void;\r\n  statusFilter: StatusFilter;\r\n  onStatusChange: (status: StatusFilter) => void;\r\n  statusCounts: StatusCounts;\r\n}\r\n\r\nexport function BankToolbar({\r\n  table,\r\n  globalFilter,\r\n  setGlobalFilter,\r\n  onRefresh,\r\n  isRefreshing,\r\n  isShowSelectedRows,\r\n  onShowSelectedRows,\r\n  statusFilter,\r\n  onStatusChange,\r\n  statusCounts,\r\n}: BankToolbarProps) {\r\n  return (\r\n    <TableToolbar\r\n      table={table}\r\n      globalFilter={globalFilter}\r\n      setGlobalFilter={setGlobalFilter}\r\n      onRefresh={onRefresh}\r\n      isRefreshing={isRefreshing}\r\n      isShowSelectedRows={isShowSelectedRows}\r\n      onShowSelectedRows={onShowSelectedRows}\r\n      beforeSearchSlot={\r\n        <StatusTabs\r\n          currentStatus={statusFilter}\r\n          onStatusChange={onStatusChange}\r\n          counts={statusCounts}\r\n          className=\"w-fit\"\r\n        />\r\n      }\r\n    />\r\n  );\r\n}\r\n"], "names": [], "mappings": "AAAA;;CAEC;;;;AAKD;AACA;AAJA;;;;AAoBO,SAAS,YAAY,EAC1B,KAAK,EACL,YAAY,EACZ,eAAe,EACf,SAAS,EACT,YAAY,EACZ,kBAAkB,EAClB,kBAAkB,EAClB,YAAY,EACZ,cAAc,EACd,YAAY,EACK;IACjB,qBACE,uVAAC,0JAAA,CAAA,eAAY;QACX,OAAO;QACP,cAAc;QACd,iBAAiB;QACjB,WAAW;QACX,cAAc;QACd,oBAAoB;QACpB,oBAAoB;QACpB,gCACE,uVAAC,wKAAA,CAAA,aAAU;YACT,eAAe;YACf,gBAAgB;YAChB,QAAQ;YACR,WAAU;;;;;;;;;;;AAKpB", "debugId": null}}, {"offset": {"line": 3826, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/bloomgoo/gold-exchange-platform/frontend/components/common/data-table/data-table.tsx"], "sourcesContent": ["\"use client\"\r\n\r\n/**\r\n * Component DataTable cung cấp bảng dữ liệu với các tính năng:\r\n * - Header cố định khi cuộn\r\n * - Cột cố định (sticky columns) ở bên trái và bên phải\r\n * - Hỗ trợ đầy đủ các tính năng của TanStack Table\r\n *\r\n * Để sử dụng sticky columns, hãy thêm thuộc tính meta vào định nghĩa cột:\r\n * ```\r\n * {\r\n *   id: 'actions',\r\n *   meta: {\r\n *     isSticky: true,\r\n *     position: 'right' // hoặc 'left'\r\n *   }\r\n * }\r\n * ```\r\n *\r\n * Xem thêm hướng dẫn chi tiết trong file README.md\r\n */\r\n\r\nimport { flexRender, Table as TableType } from \"@tanstack/react-table\";\r\n\r\nimport { cn } from \"@/lib/utils\";\r\nimport { NoData } from \"./no-data\";\r\n\r\n// Định nghĩa kiểu dữ liệu cho thuộc tính meta của cột\r\ndeclare module '@tanstack/react-table' {\r\n  interface ColumnMeta<TData, TValue> {\r\n    isSticky?: boolean;\r\n    position?: 'left' | 'right';\r\n    header?: string;\r\n  }\r\n}\r\n\r\ninterface DataTableProps<TData> {\r\n    table: TableType<TData>\r\n    className?: string\r\n    isLoading?: boolean\r\n    noDataTitle?: string\r\n    noDataDescription?: string\r\n    noDataIcon?: 'no-data' | 'empty'\r\n}\r\n\r\nexport function DataTable<TData>({\r\n    table,\r\n    className,\r\n    isLoading = false,\r\n    noDataTitle,\r\n    noDataDescription,\r\n    noDataIcon = 'no-data',\r\n}: DataTableProps<TData>) {\r\n    return (\r\n        <div className={cn(\"w-full flex flex-col h-full\", className)}>\r\n            {/* Table Container with border */}\r\n            <div className=\"border flex flex-col flex-1 overflow-hidden\">\r\n                {/* Single table with sticky header */}\r\n                <div className=\"table-scroll-container\">\r\n                    <div className=\"table-viewport\">\r\n                        <table className=\"data-table\">\r\n                            <thead className=\"sticky-header\">\r\n                                {table.getHeaderGroups().map((headerGroup) => (\r\n                                    <tr key={headerGroup.id}>\r\n                                        {headerGroup.headers.map((header, index) => {\r\n                                            // Determine if this column has sticky metadata\r\n                                            const hasSticky = header.column.columnDef.meta?.isSticky;\r\n\r\n                                            return (\r\n                                                <th\r\n                                                    key={header.id}\r\n                                                    data-column-id={header.column.id}\r\n                                                    style={{\r\n                                                        width: header.getSize(),\r\n                                                        minWidth: header.getSize(),\r\n                                                        maxWidth: header.getSize(),\r\n                                                        ...(hasSticky && {\r\n                                                            position: 'sticky',\r\n                                                            [header.column.columnDef.meta?.position === 'right' ? 'right' : 'left']: 0,\r\n                                                            zIndex: 60,\r\n                                                            boxShadow: header.column.columnDef.meta?.position === 'right'\r\n                                                                ? '-5px 0 5px -5px rgba(0,0,0,0.1)'\r\n                                                                : '5px 0 5px -5px rgba(0,0,0,0.1)'\r\n                                                        }),\r\n                                                        // Không cần logic fallback cho first column nữa\r\n                                                        // Tất cả sticky columns phải được định nghĩa qua meta\r\n                                                    }}\r\n                                                    className={cn(\r\n                                                        \"bg-background py-2 text-left align-middle font-medium text-muted-foreground\",\r\n                                                        hasSticky && `sticky-${header.column.columnDef.meta?.position || 'left'}`,\r\n                                                        // Thêm class dựa trên column ID thay vì index\r\n                                                        header.column.id === 'select' && \"checkbox-header\"\r\n                                                    )}\r\n                                                >\r\n                                                    {header.isPlaceholder\r\n                                                        ? null\r\n                                                        : flexRender(\r\n                                                            header.column.columnDef.header,\r\n                                                            header.getContext()\r\n                                                        )}\r\n                                                </th>\r\n                                            )\r\n                                        })}\r\n                                    </tr>\r\n                                ))}\r\n                            </thead>\r\n                            <tbody>\r\n                                {isLoading ? (\r\n                                    <tr>\r\n                                        <td\r\n                                            colSpan={table.getAllColumns().length}\r\n                                            className=\"h-24 text-center\"\r\n                                        >\r\n                                            Đang tải dữ liệu...\r\n                                        </td>\r\n                                    </tr>\r\n                                ) : table.getRowModel().rows?.length ? (\r\n                                    table.getRowModel().rows.map((row) => (\r\n                                        <tr\r\n                                            key={row.id}\r\n                                            data-state={row.getIsSelected() && \"selected\"}\r\n                                            className=\"border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted\"\r\n                                        >\r\n                                            {row.getVisibleCells().map((cell, index) => {\r\n                                                // Determine if this column has sticky metadata\r\n                                                const hasSticky = cell.column.columnDef.meta?.isSticky;\r\n\r\n                                                return (\r\n                                                    <td\r\n                                                        key={cell.id}\r\n                                                        data-column-id={cell.column.id}\r\n                                                        style={{\r\n                                                            width: cell.column.getSize(),\r\n                                                            minWidth: cell.column.getSize(),\r\n                                                            maxWidth: cell.column.getSize(),\r\n                                                            ...(hasSticky && {\r\n                                                                position: 'sticky',\r\n                                                                [cell.column.columnDef.meta?.position === 'right' ? 'right' : 'left']: 0,\r\n                                                                backgroundColor: 'var(--background)',\r\n                                                                zIndex: 30,\r\n                                                                boxShadow: cell.column.columnDef.meta?.position === 'right'\r\n                                                                    ? '-5px 0 5px -5px rgba(0,0,0,0.1)'\r\n                                                                    : '5px 0 5px -5px rgba(0,0,0,0.1)'\r\n                                                            }),\r\n                                                            // Không cần logic fallback cho first column nữa\r\n                                                            // Tất cả sticky columns phải được định nghĩa qua meta\r\n                                                        }}\r\n                                                        className={cn(\r\n                                                            \"py-1 px-2 align-middle\",\r\n                                                            hasSticky && `sticky-${cell.column.columnDef.meta?.position || 'left'}`,\r\n                                                            // Thêm class dựa trên column ID\r\n                                                            cell.column.id === 'select' && \"checkbox-cell\"\r\n                                                        )}\r\n                                                    >\r\n                                                        {flexRender(\r\n                                                            cell.column.columnDef.cell,\r\n                                                            cell.getContext()\r\n                                                        )}\r\n                                                    </td>\r\n                                                )\r\n                                            })}\r\n                                        </tr>\r\n                                    ))\r\n                                ) : (\r\n                                    <tr>\r\n                                        <td\r\n                                            colSpan={table.getAllColumns().length}\r\n                                            className=\"h-24 text-center\"\r\n                                        >\r\n                                            Không có dữ liệu\r\n                                        </td>\r\n                                    </tr>\r\n                                )}\r\n                            </tbody>\r\n                        </table>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n        </div>\r\n    )\r\n}"], "names": [], "mappings": ";;;;AAEA;;;;;;;;;;;;;;;;;;CAkBC,GAED;AAEA;AAAA;AAxBA;;;;AA6CO,SAAS,UAAiB,EAC7B,KAAK,EACL,SAAS,EACT,YAAY,KAAK,EACjB,WAAW,EACX,iBAAiB,EACjB,aAAa,SAAS,EACF;IACpB,qBACI,uVAAC;QAAI,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EAAE,+BAA+B;kBAE9C,cAAA,uVAAC;YAAI,WAAU;sBAEX,cAAA,uVAAC;gBAAI,WAAU;0BACX,cAAA,uVAAC;oBAAI,WAAU;8BACX,cAAA,uVAAC;wBAAM,WAAU;;0CACb,uVAAC;gCAAM,WAAU;0CACZ,MAAM,eAAe,GAAG,GAAG,CAAC,CAAC,4BAC1B,uVAAC;kDACI,YAAY,OAAO,CAAC,GAAG,CAAC,CAAC,QAAQ;4CAC9B,+CAA+C;4CAC/C,MAAM,YAAY,OAAO,MAAM,CAAC,SAAS,CAAC,IAAI,EAAE;4CAEhD,qBACI,uVAAC;gDAEG,kBAAgB,OAAO,MAAM,CAAC,EAAE;gDAChC,OAAO;oDACH,OAAO,OAAO,OAAO;oDACrB,UAAU,OAAO,OAAO;oDACxB,UAAU,OAAO,OAAO;oDACxB,GAAI,aAAa;wDACb,UAAU;wDACV,CAAC,OAAO,MAAM,CAAC,SAAS,CAAC,IAAI,EAAE,aAAa,UAAU,UAAU,OAAO,EAAE;wDACzE,QAAQ;wDACR,WAAW,OAAO,MAAM,CAAC,SAAS,CAAC,IAAI,EAAE,aAAa,UAChD,oCACA;oDACV,CAAC;gDAGL;gDACA,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EACR,+EACA,aAAa,CAAC,OAAO,EAAE,OAAO,MAAM,CAAC,SAAS,CAAC,IAAI,EAAE,YAAY,QAAQ,EACzE,8CAA8C;gDAC9C,OAAO,MAAM,CAAC,EAAE,KAAK,YAAY;0DAGpC,OAAO,aAAa,GACf,OACA,CAAA,GAAA,gSAAA,CAAA,aAAU,AAAD,EACP,OAAO,MAAM,CAAC,SAAS,CAAC,MAAM,EAC9B,OAAO,UAAU;+CA5BpB,OAAO,EAAE;;;;;wCAgC1B;uCAvCK,YAAY,EAAE;;;;;;;;;;0CA2C/B,uVAAC;0CACI,0BACG,uVAAC;8CACG,cAAA,uVAAC;wCACG,SAAS,MAAM,aAAa,GAAG,MAAM;wCACrC,WAAU;kDACb;;;;;;;;;;2CAIL,MAAM,WAAW,GAAG,IAAI,EAAE,SAC1B,MAAM,WAAW,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,oBAC1B,uVAAC;wCAEG,cAAY,IAAI,aAAa,MAAM;wCACnC,WAAU;kDAET,IAAI,eAAe,GAAG,GAAG,CAAC,CAAC,MAAM;4CAC9B,+CAA+C;4CAC/C,MAAM,YAAY,KAAK,MAAM,CAAC,SAAS,CAAC,IAAI,EAAE;4CAE9C,qBACI,uVAAC;gDAEG,kBAAgB,KAAK,MAAM,CAAC,EAAE;gDAC9B,OAAO;oDACH,OAAO,KAAK,MAAM,CAAC,OAAO;oDAC1B,UAAU,KAAK,MAAM,CAAC,OAAO;oDAC7B,UAAU,KAAK,MAAM,CAAC,OAAO;oDAC7B,GAAI,aAAa;wDACb,UAAU;wDACV,CAAC,KAAK,MAAM,CAAC,SAAS,CAAC,IAAI,EAAE,aAAa,UAAU,UAAU,OAAO,EAAE;wDACvE,iBAAiB;wDACjB,QAAQ;wDACR,WAAW,KAAK,MAAM,CAAC,SAAS,CAAC,IAAI,EAAE,aAAa,UAC9C,oCACA;oDACV,CAAC;gDAGL;gDACA,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EACR,0BACA,aAAa,CAAC,OAAO,EAAE,KAAK,MAAM,CAAC,SAAS,CAAC,IAAI,EAAE,YAAY,QAAQ,EACvE,gCAAgC;gDAChC,KAAK,MAAM,CAAC,EAAE,KAAK,YAAY;0DAGlC,CAAA,GAAA,gSAAA,CAAA,aAAU,AAAD,EACN,KAAK,MAAM,CAAC,SAAS,CAAC,IAAI,EAC1B,KAAK,UAAU;+CA3Bd,KAAK,EAAE;;;;;wCA+BxB;uCAzCK,IAAI,EAAE;;;;8DA6CnB,uVAAC;8CACG,cAAA,uVAAC;wCACG,SAAS,MAAM,aAAa,GAAG,MAAM;wCACrC,WAAU;kDACb;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAYzC", "debugId": null}}, {"offset": {"line": 4006, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/bloomgoo/gold-exchange-platform/frontend/components/common/selector/page-selector.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { Button } from '@/components/ui/button';\r\nimport {\r\n   Command,\r\n   CommandEmpty,\r\n   CommandGroup,\r\n   CommandInput,\r\n   CommandItem,\r\n   CommandList,\r\n} from '@/components/ui/command';\r\nimport { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';\r\nimport { CheckIcon, ChevronLeft, ChevronRight, ChevronsLeft, ChevronsRight } from 'lucide-react';\r\nimport { useId, useState, useEffect } from 'react';\r\nimport { Table as TableType } from \"@tanstack/react-table\";\r\n\r\n// Interface for table-based pagination\r\ninterface TablePaginationProps {\r\n   table: TableType<any>;\r\n   type: 'table';\r\n}\r\n\r\n// Interface for custom pagination\r\ninterface CustomPaginationProps {\r\n   type: 'custom';\r\n   currentPage: number;\r\n   totalPages: number;\r\n   onPageChange: (page: number) => void;\r\n}\r\n\r\n// Combined props type\r\ntype PageSelectorProps = {\r\n   maxPagesToShow?: number;\r\n   showFirstLastButtons?: boolean;\r\n   showPrevNextButtons?: boolean;\r\n   className?: string;\r\n} & (TablePaginationProps | CustomPaginationProps);\r\n\r\nexport function PageSelector(props: PageSelectorProps) {\r\n   const id = useId();\r\n   const [open, setOpen] = useState<boolean>(false);\r\n   \r\n   // Determine current page and total pages based on props type\r\n   const currentPage = props.type === 'table' \r\n      ? props.table.getState().pagination.pageIndex + 1 \r\n      : props.currentPage;\r\n   \r\n   const totalPages = props.type === 'table'\r\n      ? props.table.getPageCount()\r\n      : props.totalPages;\r\n   \r\n   // Default to showing 5 pages in the selector\r\n   const maxPagesToShow = props.maxPagesToShow || 5;\r\n   \r\n   // Generate array of page numbers to display\r\n   const getPageNumbers = () => {\r\n      if (totalPages <= maxPagesToShow) {\r\n         // If total pages is less than max to show, display all pages\r\n         return Array.from({ length: totalPages }, (_, i) => i + 1);\r\n      }\r\n      \r\n      // Calculate start and end page numbers\r\n      let startPage = Math.max(1, currentPage - Math.floor(maxPagesToShow / 2));\r\n      let endPage = startPage + maxPagesToShow - 1;\r\n      \r\n      // Adjust if end page exceeds total pages\r\n      if (endPage > totalPages) {\r\n         endPage = totalPages;\r\n         startPage = Math.max(1, endPage - maxPagesToShow + 1);\r\n      }\r\n      \r\n      return Array.from({ length: endPage - startPage + 1 }, (_, i) => startPage + i);\r\n   };\r\n   \r\n   const pageNumbers = getPageNumbers();\r\n   \r\n   // Handle page change\r\n   const handlePageChange = (page: number) => {\r\n      if (props.type === 'table') {\r\n         props.table.setPageIndex(page - 1);\r\n      } else {\r\n         props.onPageChange(page);\r\n      }\r\n      setOpen(false);\r\n   };\r\n   \r\n   // Handle navigation to first page\r\n   const goToFirstPage = () => {\r\n      if (props.type === 'table') {\r\n         props.table.setPageIndex(0);\r\n      } else {\r\n         props.onPageChange(1);\r\n      }\r\n   };\r\n   \r\n   // Handle navigation to previous page\r\n   const goToPreviousPage = () => {\r\n      if (props.type === 'table') {\r\n         props.table.previousPage();\r\n      } else {\r\n         props.onPageChange(Math.max(1, currentPage - 1));\r\n      }\r\n   };\r\n   \r\n   // Handle navigation to next page\r\n   const goToNextPage = () => {\r\n      if (props.type === 'table') {\r\n         props.table.nextPage();\r\n      } else {\r\n         props.onPageChange(Math.min(totalPages, currentPage + 1));\r\n      }\r\n   };\r\n   \r\n   // Handle navigation to last page\r\n   const goToLastPage = () => {\r\n      if (props.type === 'table') {\r\n         props.table.setPageIndex(totalPages - 1);\r\n      } else {\r\n         props.onPageChange(totalPages);\r\n      }\r\n   };\r\n   \r\n   // Check if can go to previous or next page\r\n   const canPreviousPage = props.type === 'table' \r\n      ? props.table.getCanPreviousPage()\r\n      : currentPage > 1;\r\n      \r\n   const canNextPage = props.type === 'table'\r\n      ? props.table.getCanNextPage()\r\n      : currentPage < totalPages;\r\n\r\n   return (\r\n      <div className=\"flex items-center space-x-2\">\r\n         {/* First Page Button */}\r\n         {props.showFirstLastButtons && (\r\n            <Button\r\n               variant=\"outline\"\r\n               className=\"hidden h-8 w-8 p-0 lg:flex\"\r\n               onClick={goToFirstPage}\r\n               disabled={!canPreviousPage}\r\n            >\r\n               <ChevronsLeft className=\"size-4\" />\r\n            </Button>\r\n         )}\r\n         \r\n         {/* Previous Page Button */}\r\n         {props.showPrevNextButtons && (\r\n            <Button\r\n               variant=\"outline\"\r\n               className=\"h-8 w-8 p-0\"\r\n               onClick={goToPreviousPage}\r\n               disabled={!canPreviousPage}\r\n            >\r\n               <ChevronLeft className=\"size-4\" />\r\n            </Button>\r\n         )}\r\n         \r\n         {/* Page Selector */}\r\n         <Popover open={open} onOpenChange={setOpen}>\r\n            <PopoverTrigger asChild>\r\n               <Button\r\n                  id={id}\r\n                  className=\"flex items-center justify-center h-8 px-3\"\r\n                  size=\"sm\"\r\n                  variant=\"outline\"\r\n                  role=\"combobox\"\r\n                  aria-expanded={open}\r\n               >\r\n                  <span className=\"text-sm font-medium\">\r\n                     {currentPage} / {totalPages}\r\n                  </span>\r\n               </Button>\r\n            </PopoverTrigger>\r\n            <PopoverContent className=\"border-input w-48 p-0\" align=\"center\" side=\"top\">\r\n               <Command>\r\n                  <CommandInput placeholder=\"Trang...\" />\r\n                  <CommandList>\r\n                     <CommandEmpty>Không tìm thấy trang.</CommandEmpty>\r\n                     <CommandGroup>\r\n                        {pageNumbers.map((page) => (\r\n                           <CommandItem\r\n                              key={page}\r\n                              value={page.toString()}\r\n                              onSelect={() => handlePageChange(page)}\r\n                              className=\"flex items-center justify-between\"\r\n                           >\r\n                              <div className=\"flex items-center gap-2\">\r\n                                 <span className=\"text-xs\">Trang {page}</span>\r\n                              </div>\r\n                              {currentPage === page && <CheckIcon size={14} className=\"ml-auto\" />}\r\n                           </CommandItem>\r\n                        ))}\r\n                     </CommandGroup>\r\n                  </CommandList>\r\n               </Command>\r\n            </PopoverContent>\r\n         </Popover>\r\n         \r\n         {/* Next Page Button */}\r\n         {props.showPrevNextButtons && (\r\n            <Button\r\n               variant=\"outline\"\r\n               className=\"h-8 w-8 p-0\"\r\n               onClick={goToNextPage}\r\n               disabled={!canNextPage}\r\n            >\r\n               <ChevronRight className=\"size-4\" />\r\n            </Button>\r\n         )}\r\n         \r\n         {/* Last Page Button */}\r\n         {props.showFirstLastButtons && (\r\n            <Button\r\n               variant=\"outline\"\r\n               className=\"hidden h-8 w-8 p-0 lg:flex\"\r\n               onClick={goToLastPage}\r\n               disabled={!canNextPage}\r\n            >\r\n               <ChevronsRight className=\"size-4\" />\r\n            </Button>\r\n         )}\r\n      </div>\r\n   );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAQA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AAbA;;;;;;;AAsCO,SAAS,aAAa,KAAwB;IAClD,MAAM,KAAK,CAAA,GAAA,8SAAA,CAAA,QAAK,AAAD;IACf,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,8SAAA,CAAA,WAAQ,AAAD,EAAW;IAE1C,6DAA6D;IAC7D,MAAM,cAAc,MAAM,IAAI,KAAK,UAC9B,MAAM,KAAK,CAAC,QAAQ,GAAG,UAAU,CAAC,SAAS,GAAG,IAC9C,MAAM,WAAW;IAEtB,MAAM,aAAa,MAAM,IAAI,KAAK,UAC7B,MAAM,KAAK,CAAC,YAAY,KACxB,MAAM,UAAU;IAErB,6CAA6C;IAC7C,MAAM,iBAAiB,MAAM,cAAc,IAAI;IAE/C,4CAA4C;IAC5C,MAAM,iBAAiB;QACpB,IAAI,cAAc,gBAAgB;YAC/B,6DAA6D;YAC7D,OAAO,MAAM,IAAI,CAAC;gBAAE,QAAQ;YAAW,GAAG,CAAC,GAAG,IAAM,IAAI;QAC3D;QAEA,uCAAuC;QACvC,IAAI,YAAY,KAAK,GAAG,CAAC,GAAG,cAAc,KAAK,KAAK,CAAC,iBAAiB;QACtE,IAAI,UAAU,YAAY,iBAAiB;QAE3C,yCAAyC;QACzC,IAAI,UAAU,YAAY;YACvB,UAAU;YACV,YAAY,KAAK,GAAG,CAAC,GAAG,UAAU,iBAAiB;QACtD;QAEA,OAAO,MAAM,IAAI,CAAC;YAAE,QAAQ,UAAU,YAAY;QAAE,GAAG,CAAC,GAAG,IAAM,YAAY;IAChF;IAEA,MAAM,cAAc;IAEpB,qBAAqB;IACrB,MAAM,mBAAmB,CAAC;QACvB,IAAI,MAAM,IAAI,KAAK,SAAS;YACzB,MAAM,KAAK,CAAC,YAAY,CAAC,OAAO;QACnC,OAAO;YACJ,MAAM,YAAY,CAAC;QACtB;QACA,QAAQ;IACX;IAEA,kCAAkC;IAClC,MAAM,gBAAgB;QACnB,IAAI,MAAM,IAAI,KAAK,SAAS;YACzB,MAAM,KAAK,CAAC,YAAY,CAAC;QAC5B,OAAO;YACJ,MAAM,YAAY,CAAC;QACtB;IACH;IAEA,qCAAqC;IACrC,MAAM,mBAAmB;QACtB,IAAI,MAAM,IAAI,KAAK,SAAS;YACzB,MAAM,KAAK,CAAC,YAAY;QAC3B,OAAO;YACJ,MAAM,YAAY,CAAC,KAAK,GAAG,CAAC,GAAG,cAAc;QAChD;IACH;IAEA,iCAAiC;IACjC,MAAM,eAAe;QAClB,IAAI,MAAM,IAAI,KAAK,SAAS;YACzB,MAAM,KAAK,CAAC,QAAQ;QACvB,OAAO;YACJ,MAAM,YAAY,CAAC,KAAK,GAAG,CAAC,YAAY,cAAc;QACzD;IACH;IAEA,iCAAiC;IACjC,MAAM,eAAe;QAClB,IAAI,MAAM,IAAI,KAAK,SAAS;YACzB,MAAM,KAAK,CAAC,YAAY,CAAC,aAAa;QACzC,OAAO;YACJ,MAAM,YAAY,CAAC;QACtB;IACH;IAEA,2CAA2C;IAC3C,MAAM,kBAAkB,MAAM,IAAI,KAAK,UAClC,MAAM,KAAK,CAAC,kBAAkB,KAC9B,cAAc;IAEnB,MAAM,cAAc,MAAM,IAAI,KAAK,UAC9B,MAAM,KAAK,CAAC,cAAc,KAC1B,cAAc;IAEnB,qBACG,uVAAC;QAAI,WAAU;;YAEX,MAAM,oBAAoB,kBACxB,uVAAC,2HAAA,CAAA,SAAM;gBACJ,SAAQ;gBACR,WAAU;gBACV,SAAS;gBACT,UAAU,CAAC;0BAEX,cAAA,uVAAC,0SAAA,CAAA,eAAY;oBAAC,WAAU;;;;;;;;;;;YAK7B,MAAM,mBAAmB,kBACvB,uVAAC,2HAAA,CAAA,SAAM;gBACJ,SAAQ;gBACR,WAAU;gBACV,SAAS;gBACT,UAAU,CAAC;0BAEX,cAAA,uVAAC,wSAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;;;;;;0BAK7B,uVAAC,4HAAA,CAAA,UAAO;gBAAC,MAAM;gBAAM,cAAc;;kCAChC,uVAAC,4HAAA,CAAA,iBAAc;wBAAC,OAAO;kCACpB,cAAA,uVAAC,2HAAA,CAAA,SAAM;4BACJ,IAAI;4BACJ,WAAU;4BACV,MAAK;4BACL,SAAQ;4BACR,MAAK;4BACL,iBAAe;sCAEf,cAAA,uVAAC;gCAAK,WAAU;;oCACZ;oCAAY;oCAAI;;;;;;;;;;;;;;;;;kCAI1B,uVAAC,4HAAA,CAAA,iBAAc;wBAAC,WAAU;wBAAwB,OAAM;wBAAS,MAAK;kCACnE,cAAA,uVAAC,4HAAA,CAAA,UAAO;;8CACL,uVAAC,4HAAA,CAAA,eAAY;oCAAC,aAAY;;;;;;8CAC1B,uVAAC,4HAAA,CAAA,cAAW;;sDACT,uVAAC,4HAAA,CAAA,eAAY;sDAAC;;;;;;sDACd,uVAAC,4HAAA,CAAA,eAAY;sDACT,YAAY,GAAG,CAAC,CAAC,qBACf,uVAAC,4HAAA,CAAA,cAAW;oDAET,OAAO,KAAK,QAAQ;oDACpB,UAAU,IAAM,iBAAiB;oDACjC,WAAU;;sEAEV,uVAAC;4DAAI,WAAU;sEACZ,cAAA,uVAAC;gEAAK,WAAU;;oEAAU;oEAAO;;;;;;;;;;;;wDAEnC,gBAAgB,sBAAQ,uVAAC,4RAAA,CAAA,YAAS;4DAAC,MAAM;4DAAI,WAAU;;;;;;;mDARnD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAkBzB,MAAM,mBAAmB,kBACvB,uVAAC,2HAAA,CAAA,SAAM;gBACJ,SAAQ;gBACR,WAAU;gBACV,SAAS;gBACT,UAAU,CAAC;0BAEX,cAAA,uVAAC,0SAAA,CAAA,eAAY;oBAAC,WAAU;;;;;;;;;;;YAK7B,MAAM,oBAAoB,kBACxB,uVAAC,2HAAA,CAAA,SAAM;gBACJ,SAAQ;gBACR,WAAU;gBACV,SAAS;gBACT,UAAU,CAAC;0BAEX,cAAA,uVAAC,4SAAA,CAAA,gBAAa;oBAAC,WAAU;;;;;;;;;;;;;;;;;AAKxC", "debugId": null}}, {"offset": {"line": 4306, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/bloomgoo/gold-exchange-platform/frontend/components/common/selector/page-size-selector.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { Button } from '@/components/ui/button';\r\nimport {\r\n   Command,\r\n   CommandEmpty,\r\n   CommandGroup,\r\n   CommandInput,\r\n   CommandItem,\r\n   CommandList,\r\n} from '@/components/ui/command';\r\nimport { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';\r\nimport { CheckIcon } from 'lucide-react';\r\nimport { useId, useState } from 'react';\r\nimport { Table as TableType } from \"@tanstack/react-table\";\r\n\r\n// Interface for table-based pagination\r\ninterface TablePageSizeProps {\r\n   table: TableType<any>;\r\n   type: 'table';\r\n}\r\n\r\n// Interface for custom pagination\r\ninterface CustomPageSizeProps {\r\n   type: 'custom';\r\n   pageSize: number;\r\n   onPageSizeChange: (pageSize: number) => void;\r\n}\r\n\r\n// Combined props type\r\ntype PageSizeSelectorProps = {\r\n   pageSizeOptions?: number[];\r\n   className?: string;\r\n   label?: string;\r\n   triggerClassName?: string;\r\n} & (TablePageSizeProps | CustomPageSizeProps);\r\n\r\nexport function PageSizeSelector(props: PageSizeSelectorProps) {\r\n   const id = useId();\r\n   const [open, setOpen] = useState<boolean>(false);\r\n   \r\n   // Default page size options if not provided\r\n   const pageSizeOptions = props.pageSizeOptions || [10, 20, 30, 50, 100];\r\n   \r\n   // Get current page size based on props type\r\n   const currentPageSize = props.type === 'table' \r\n      ? props.table.getState().pagination.pageSize \r\n      : props.pageSize;\r\n   \r\n   // Handle page size change\r\n   const handlePageSizeChange = (pageSize: number) => {\r\n      if (props.type === 'table') {\r\n         props.table.setPageSize(pageSize);\r\n      } else {\r\n         props.onPageSizeChange(pageSize);\r\n      }\r\n      setOpen(false);\r\n   };\r\n\r\n   return (\r\n      <div className=\"flex items-center space-x-2\">\r\n         {props.label && (\r\n            <span className=\"text-sm text-muted-foreground\">{props.label}</span>\r\n         )}\r\n         \r\n         <Popover open={open} onOpenChange={setOpen}>\r\n            <PopoverTrigger asChild>\r\n               <Button\r\n                  id={id}\r\n                  className={`flex items-center justify-center h-8 px-3 ${props.triggerClassName || ''}`}\r\n                  size=\"sm\"\r\n                  variant=\"outline\"\r\n                  role=\"combobox\"\r\n                  aria-expanded={open}\r\n               >\r\n                  <span className=\"text-sm font-medium\">\r\n                     {currentPageSize} bản ghi\r\n                  </span>\r\n               </Button>\r\n            </PopoverTrigger>\r\n            <PopoverContent className=\"border-input w-48 p-0\" align=\"start\" side=\"top\">\r\n               <Command>\r\n                  <CommandInput placeholder=\"Số bản ghi...\" />\r\n                  <CommandList>\r\n                     <CommandEmpty>Không tìm thấy tùy chọn.</CommandEmpty>\r\n                     <CommandGroup>\r\n                        {pageSizeOptions.map((size) => (\r\n                           <CommandItem\r\n                              key={size}\r\n                              value={size.toString()}\r\n                              onSelect={() => handlePageSizeChange(size)}\r\n                              className=\"flex items-center justify-between\"\r\n                           >\r\n                              <div className=\"flex items-center gap-2\">\r\n                                 <span className=\"text-xs\">{size} bản ghi</span>\r\n                              </div>\r\n                              {currentPageSize === size && <CheckIcon size={14} className=\"ml-auto\" />}\r\n                           </CommandItem>\r\n                        ))}\r\n                     </CommandGroup>\r\n                  </CommandList>\r\n               </Command>\r\n            </PopoverContent>\r\n         </Popover>\r\n      </div>\r\n   );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAQA;AACA;AACA;AAbA;;;;;;;AAqCO,SAAS,iBAAiB,KAA4B;IAC1D,MAAM,KAAK,CAAA,GAAA,8SAAA,CAAA,QAAK,AAAD;IACf,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,8SAAA,CAAA,WAAQ,AAAD,EAAW;IAE1C,4CAA4C;IAC5C,MAAM,kBAAkB,MAAM,eAAe,IAAI;QAAC;QAAI;QAAI;QAAI;QAAI;KAAI;IAEtE,4CAA4C;IAC5C,MAAM,kBAAkB,MAAM,IAAI,KAAK,UAClC,MAAM,KAAK,CAAC,QAAQ,GAAG,UAAU,CAAC,QAAQ,GAC1C,MAAM,QAAQ;IAEnB,0BAA0B;IAC1B,MAAM,uBAAuB,CAAC;QAC3B,IAAI,MAAM,IAAI,KAAK,SAAS;YACzB,MAAM,KAAK,CAAC,WAAW,CAAC;QAC3B,OAAO;YACJ,MAAM,gBAAgB,CAAC;QAC1B;QACA,QAAQ;IACX;IAEA,qBACG,uVAAC;QAAI,WAAU;;YACX,MAAM,KAAK,kBACT,uVAAC;gBAAK,WAAU;0BAAiC,MAAM,KAAK;;;;;;0BAG/D,uVAAC,4HAAA,CAAA,UAAO;gBAAC,MAAM;gBAAM,cAAc;;kCAChC,uVAAC,4HAAA,CAAA,iBAAc;wBAAC,OAAO;kCACpB,cAAA,uVAAC,2HAAA,CAAA,SAAM;4BACJ,IAAI;4BACJ,WAAW,CAAC,0CAA0C,EAAE,MAAM,gBAAgB,IAAI,IAAI;4BACtF,MAAK;4BACL,SAAQ;4BACR,MAAK;4BACL,iBAAe;sCAEf,cAAA,uVAAC;gCAAK,WAAU;;oCACZ;oCAAgB;;;;;;;;;;;;;;;;;kCAI1B,uVAAC,4HAAA,CAAA,iBAAc;wBAAC,WAAU;wBAAwB,OAAM;wBAAQ,MAAK;kCAClE,cAAA,uVAAC,4HAAA,CAAA,UAAO;;8CACL,uVAAC,4HAAA,CAAA,eAAY;oCAAC,aAAY;;;;;;8CAC1B,uVAAC,4HAAA,CAAA,cAAW;;sDACT,uVAAC,4HAAA,CAAA,eAAY;sDAAC;;;;;;sDACd,uVAAC,4HAAA,CAAA,eAAY;sDACT,gBAAgB,GAAG,CAAC,CAAC,qBACnB,uVAAC,4HAAA,CAAA,cAAW;oDAET,OAAO,KAAK,QAAQ;oDACpB,UAAU,IAAM,qBAAqB;oDACrC,WAAU;;sEAEV,uVAAC;4DAAI,WAAU;sEACZ,cAAA,uVAAC;gEAAK,WAAU;;oEAAW;oEAAK;;;;;;;;;;;;wDAElC,oBAAoB,sBAAQ,uVAAC,4RAAA,CAAA,YAAS;4DAAC,MAAM;4DAAI,WAAU;;;;;;;mDARvD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAkBnC", "debugId": null}}, {"offset": {"line": 4490, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/bloomgoo/gold-exchange-platform/frontend/components/common/data-table/table-footer.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport { Table as TableType } from \"@tanstack/react-table\"\r\nimport { But<PERSON> } from \"@/components/ui/button\"\r\nimport { ChevronLeft, ChevronRight, ChevronsLeft, ChevronsRight } from \"lucide-react\"\r\nimport { PageSelector } from \"@/components/common/selector/page-selector\"\r\nimport { PageSizeSelector } from \"@/components/common/selector/page-size-selector\"\r\n\r\ninterface TableFooterProps<TData> {\r\n  table: TableType<TData>\r\n  className?: string\r\n  totalItems?: number\r\n  isShowSelectedRows?: boolean\r\n  onShowSelectedRows?: () => void\r\n}\r\n\r\nexport function TableFooter<TData>({\r\n  table,\r\n  className,\r\n  totalItems,\r\n  isShowSelectedRows,\r\n  onShowSelectedRows,\r\n}: TableFooterProps<TData>) {\r\n  // Kiểm tra xem có hàng nào được chọn không\r\n  const selectedRowsCount = table.getFilteredSelectedRowModel().rows.length;\r\n  const hasSelectedRows = selectedRowsCount > 0;\r\n\r\n  return (\r\n    <div className=\"sticky bottom-0 bg-background border-t mt-auto\">\r\n      <div className=\"flex items-center justify-between space-x-2 px-4 py-2\">\r\n        <div className=\"flex items-center space-x-2\">\r\n          <PageSizeSelector\r\n            type=\"table\"\r\n            table={table}\r\n            pageSizeOptions={[20, 50, 100, 200]}\r\n            triggerClassName=\"w-[120px]\"\r\n          />\r\n\r\n          {/* Hiển thị thông tin về hàng đã chọn khi có hàng được chọn */}\r\n          {hasSelectedRows && (\r\n            <p className=\"text-sm text-muted-foreground\">\r\n              {selectedRowsCount} trên{\" \"}\r\n              {table.getFilteredRowModel().rows.length} bản ghi đã chọn.\r\n            </p>\r\n          )}\r\n        </div>\r\n\r\n        <div className=\"flex items-center space-x-6 lg:space-x-8\">\r\n          <div className=\"flex w-[100x] items-center justify-center text-sm font-medium\">\r\n            Hiển thị {table.getRowCount()} trên {totalItems} bản ghi\r\n          </div>\r\n          <PageSelector\r\n            type=\"table\"\r\n            table={table}\r\n            showFirstLastButtons={true}\r\n            showPrevNextButtons={true}\r\n            maxPagesToShow={7}\r\n          />\r\n        </div>\r\n      </div>\r\n    </div>\r\n  )\r\n}"], "names": [], "mappings": ";;;;AAKA;AACA;AANA;;;;AAgBO,SAAS,YAAmB,EACjC,KAAK,EACL,SAAS,EACT,UAAU,EACV,kBAAkB,EAClB,kBAAkB,EACM;IACxB,2CAA2C;IAC3C,MAAM,oBAAoB,MAAM,2BAA2B,GAAG,IAAI,CAAC,MAAM;IACzE,MAAM,kBAAkB,oBAAoB;IAE5C,qBACE,uVAAC;QAAI,WAAU;kBACb,cAAA,uVAAC;YAAI,WAAU;;8BACb,uVAAC;oBAAI,WAAU;;sCACb,uVAAC,6JAAA,CAAA,mBAAgB;4BACf,MAAK;4BACL,OAAO;4BACP,iBAAiB;gCAAC;gCAAI;gCAAI;gCAAK;6BAAI;4BACnC,kBAAiB;;;;;;wBAIlB,iCACC,uVAAC;4BAAE,WAAU;;gCACV;gCAAkB;gCAAM;gCACxB,MAAM,mBAAmB,GAAG,IAAI,CAAC,MAAM;gCAAC;;;;;;;;;;;;;8BAK/C,uVAAC;oBAAI,WAAU;;sCACb,uVAAC;4BAAI,WAAU;;gCAAgE;gCACnE,MAAM,WAAW;gCAAG;gCAAO;gCAAW;;;;;;;sCAElD,uVAAC,qJAAA,CAAA,eAAY;4BACX,MAAK;4BACL,OAAO;4BACP,sBAAsB;4BACtB,qBAAqB;4BACrB,gBAAgB;;;;;;;;;;;;;;;;;;;;;;;AAM5B", "debugId": null}}, {"offset": {"line": 4599, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/bloomgoo/gold-exchange-platform/frontend/components/common/admin/ebanks/components/bank-table.tsx"], "sourcesContent": ["/**\r\n * BankTable - Table component wrapper\r\n */\r\n\r\n'use client';\r\n\r\nimport { Table } from '@tanstack/react-table';\r\nimport { DataTable } from '../../../data-table/data-table';\r\nimport { TableFooter } from '../../../data-table/table-footer';\r\nimport type { BankDto } from '../types';\r\n\r\ninterface BankTableProps {\r\n  table: Table<BankDto>;\r\n  totalItems: number;\r\n  isShowSelectedRows: boolean;\r\n  onShowSelectedRows: () => void;\r\n}\r\n\r\nexport function BankTable({\r\n  table,\r\n  totalItems,\r\n  isShowSelectedRows,\r\n  onShowSelectedRows,\r\n}: BankTableProps) {\r\n  return (\r\n    <>\r\n      {/* Data Table */}\r\n      <div className=\"flex-1 overflow-auto\">\r\n        <DataTable\r\n          table={table}\r\n          className=\"w-full\"\r\n          title=\"\"\r\n          totalItems={totalItems}\r\n        />\r\n      </div>\r\n\r\n      {/* Fixed Pagination Footer */}\r\n      <TableFooter \r\n        table={table} \r\n        totalItems={totalItems} \r\n        isShowSelectedRows={isShowSelectedRows} \r\n        onShowSelectedRows={onShowSelectedRows} \r\n      />\r\n    </>\r\n  );\r\n}\r\n"], "names": [], "mappings": "AAAA;;CAEC;;;;AAKD;AACA;AAJA;;;;AAcO,SAAS,UAAU,EACxB,KAAK,EACL,UAAU,EACV,kBAAkB,EAClB,kBAAkB,EACH;IACf,qBACE;;0BAEE,uVAAC;gBAAI,WAAU;0BACb,cAAA,uVAAC,uJAAA,CAAA,YAAS;oBACR,OAAO;oBACP,WAAU;oBACV,OAAM;oBACN,YAAY;;;;;;;;;;;0BAKhB,uVAAC,yJAAA,CAAA,cAAW;gBACV,OAAO;gBACP,YAAY;gBACZ,oBAAoB;gBACpB,oBAAoB;;;;;;;;AAI5B", "debugId": null}}, {"offset": {"line": 4650, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/bloomgoo/gold-exchange-platform/frontend/components/common/admin/ebanks/components/float-delete-button.tsx"], "sourcesContent": ["/**\r\n * FloatDeleteButton - Button floating để xóa nhiều items\r\n */\r\n\r\n'use client';\r\n\r\nimport { useState } from 'react';\r\nimport { Trash2 } from 'lucide-react';\r\nimport { Button } from '@/components/ui/button';\r\nimport {\r\n  AlertDialog,\r\n  AlertDialogAction,\r\n  AlertDialogCancel,\r\n  AlertDialogContent,\r\n  AlertDialogDescription,\r\n  AlertDialogFooter,\r\n  AlertDialogHeader,\r\n  AlertDialogTitle,\r\n} from '@/components/ui/alert-dialog';\r\nimport { cn } from '@/lib/utils';\r\n\r\ninterface FloatDeleteButtonProps {\r\n  selectedCount: number;\r\n  onDelete: () => void;\r\n  className?: string;\r\n}\r\n\r\nexport function FloatDeleteButton({\r\n  selectedCount,\r\n  onDelete,\r\n  className\r\n}: FloatDeleteButtonProps) {\r\n  const [showConfirmDialog, setShowConfirmDialog] = useState(false);\r\n\r\n  if (selectedCount === 0) return null;\r\n\r\n  const handleConfirmDelete = () => {\r\n    onDelete();\r\n    setShowConfirmDialog(false);\r\n  };\r\n\r\n  return (\r\n    <>\r\n      <div\r\n        className={cn(\r\n          \"fixed bottom-20 right-6 z-50 transition-all duration-300 ease-in-out\",\r\n          selectedCount > 0 ? \"translate-y-0 opacity-100\" : \"translate-y-16 opacity-0\",\r\n          className\r\n        )}\r\n      >\r\n        <Button\r\n          variant=\"destructive\"\r\n          size=\"lg\"\r\n          onClick={() => setShowConfirmDialog(true)}\r\n          className=\"shadow-lg hover:shadow-xl transition-shadow rounded-full px-6 py-3\"\r\n        >\r\n          <Trash2 className=\"mr-2 h-4 w-4\" />\r\n          Xóa {selectedCount} mục đã chọn\r\n        </Button>\r\n      </div>\r\n\r\n      <AlertDialog open={showConfirmDialog} onOpenChange={setShowConfirmDialog}>\r\n        <AlertDialogContent>\r\n          <AlertDialogHeader>\r\n            <AlertDialogTitle>Xác nhận xóa</AlertDialogTitle>\r\n            <AlertDialogDescription>\r\n              Bạn có chắc chắn muốn xóa {selectedCount} ngân hàng đã chọn?\r\n              <br />\r\n              Hành động này không thể hoàn tác.\r\n            </AlertDialogDescription>\r\n          </AlertDialogHeader>\r\n          <AlertDialogFooter>\r\n            <AlertDialogCancel>Hủy</AlertDialogCancel>\r\n            <AlertDialogAction\r\n              onClick={handleConfirmDelete}\r\n              className=\"bg-destructive hover:bg-destructive/90\"\r\n            >\r\n              Xóa tất cả\r\n            </AlertDialogAction>\r\n          </AlertDialogFooter>\r\n        </AlertDialogContent>\r\n      </AlertDialog>\r\n    </>\r\n  );\r\n}\r\n"], "names": [], "mappings": "AAAA;;CAEC;;;;AAID;AACA;AACA;AACA;AAUA;AAAA;AAfA;;;;;;;AAuBO,SAAS,kBAAkB,EAChC,aAAa,EACb,QAAQ,EACR,SAAS,EACc;IACvB,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,8SAAA,CAAA,WAAQ,AAAD,EAAE;IAE3D,IAAI,kBAAkB,GAAG,OAAO;IAEhC,MAAM,sBAAsB;QAC1B;QACA,qBAAqB;IACvB;IAEA,qBACE;;0BACE,uVAAC;gBACC,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EACV,wEACA,gBAAgB,IAAI,8BAA8B,4BAClD;0BAGF,cAAA,uVAAC,2HAAA,CAAA,SAAM;oBACL,SAAQ;oBACR,MAAK;oBACL,SAAS,IAAM,qBAAqB;oBACpC,WAAU;;sCAEV,uVAAC,8RAAA,CAAA,SAAM;4BAAC,WAAU;;;;;;wBAAiB;wBAC9B;wBAAc;;;;;;;;;;;;0BAIvB,uVAAC,oIAAA,CAAA,cAAW;gBAAC,MAAM;gBAAmB,cAAc;0BAClD,cAAA,uVAAC,oIAAA,CAAA,qBAAkB;;sCACjB,uVAAC,oIAAA,CAAA,oBAAiB;;8CAChB,uVAAC,oIAAA,CAAA,mBAAgB;8CAAC;;;;;;8CAClB,uVAAC,oIAAA,CAAA,yBAAsB;;wCAAC;wCACK;wCAAc;sDACzC,uVAAC;;;;;wCAAK;;;;;;;;;;;;;sCAIV,uVAAC,oIAAA,CAAA,oBAAiB;;8CAChB,uVAAC,oIAAA,CAAA,oBAAiB;8CAAC;;;;;;8CACnB,uVAAC,oIAAA,CAAA,oBAAiB;oCAChB,SAAS;oCACT,WAAU;8CACX;;;;;;;;;;;;;;;;;;;;;;;;;AAQb", "debugId": null}}, {"offset": {"line": 4788, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/bloomgoo/gold-exchange-platform/frontend/components/common/admin/ebanks/form/bank-form-fields.tsx"], "sourcesContent": ["/**\r\n * BankFormFields - Form fields component\r\n */\r\n\r\n'use client';\r\n\r\nimport { UseFormReturn } from 'react-hook-form';\r\nimport {\r\n  FormControl,\r\n  FormDescription,\r\n  FormField,\r\n  FormItem,\r\n  FormLabel,\r\n  FormMessage,\r\n} from '@/components/ui/form';\r\nimport { Input } from '@/components/ui/input';\r\nimport { Checkbox } from '@/components/ui/checkbox';\r\nimport type { BankFormValues } from '../schemas/form-schema';\r\n\r\ntype BankFormMode = 'create' | 'update' | 'view';\r\n\r\ninterface BankFormFieldsProps {\r\n  form: UseFormReturn<BankFormValues>;\r\n  mode: BankFormMode;\r\n}\r\n\r\nexport function BankFormFields({ form, mode }: BankFormFieldsProps) {\r\n  const isDisabled = mode === 'view';\r\n\r\n  return (\r\n    <div className=\"space-y-4\">\r\n      {/* Row 1: Brand Name và Full Name */}\r\n      <div className=\"grid grid-cols-2 gap-4\">\r\n        <FormField\r\n          control={form.control}\r\n          name=\"brandName\"\r\n          render={({ field }) => (\r\n            <FormItem>\r\n              <FormLabel>Tên thương hiệu</FormLabel>\r\n              <FormControl>\r\n                <Input\r\n                  {...field}\r\n                  disabled={isDisabled}\r\n                  placeholder=\"Nhập tên thương hiệu\"\r\n                />\r\n              </FormControl>\r\n              <FormMessage />\r\n            </FormItem>\r\n          )}\r\n        />\r\n\r\n        <FormField\r\n          control={form.control}\r\n          name=\"fullName\"\r\n          render={({ field }) => (\r\n            <FormItem>\r\n              <FormLabel>Tên đầy đủ</FormLabel>\r\n              <FormControl>\r\n                <Input\r\n                  {...field}\r\n                  disabled={isDisabled}\r\n                  placeholder=\"Nhập tên đầy đủ\"\r\n                />\r\n              </FormControl>\r\n              <FormMessage />\r\n            </FormItem>\r\n          )}\r\n        />\r\n      </div>\r\n\r\n      {/* Row 2: Short Name và Code */}\r\n      <div className=\"grid grid-cols-2 gap-4\">\r\n        <FormField\r\n          control={form.control}\r\n          name=\"shortName\"\r\n          render={({ field }) => (\r\n            <FormItem>\r\n              <FormLabel>Tên ngắn gọn</FormLabel>\r\n              <FormControl>\r\n                <Input\r\n                  {...field}\r\n                  disabled={isDisabled}\r\n                  placeholder=\"Nhập tên ngắn gọn\"\r\n                />\r\n              </FormControl>\r\n              <FormMessage />\r\n            </FormItem>\r\n          )}\r\n        />\r\n\r\n        <FormField\r\n          control={form.control}\r\n          name=\"code\"\r\n          render={({ field }) => (\r\n            <FormItem>\r\n              <FormLabel>Mã ngân hàng</FormLabel>\r\n              <FormControl>\r\n                <Input\r\n                  {...field}\r\n                  disabled={isDisabled}\r\n                  placeholder=\"VCB, TCB, ...\"\r\n                />\r\n              </FormControl>\r\n              <FormMessage />\r\n            </FormItem>\r\n          )}\r\n        />\r\n      </div>\r\n\r\n      {/* Row 3: Bin và Logo Path */}\r\n      <div className=\"grid grid-cols-2 gap-4\">\r\n        <FormField\r\n          control={form.control}\r\n          name=\"bin\"\r\n          render={({ field }) => (\r\n            <FormItem>\r\n              <FormLabel>Bin ngân hàng</FormLabel>\r\n              <FormControl>\r\n                <Input\r\n                  {...field}\r\n                  disabled={isDisabled}\r\n                  placeholder=\"Nhập bin ngân hàng\"\r\n                />\r\n              </FormControl>\r\n              <FormMessage />\r\n            </FormItem>\r\n          )}\r\n        />\r\n\r\n        <FormField\r\n          control={form.control}\r\n          name=\"logoPath\"\r\n          render={({ field }) => (\r\n            <FormItem>\r\n              <FormLabel>URL Logo</FormLabel>\r\n              <FormControl>\r\n                <Input\r\n                  {...field}\r\n                  disabled={isDisabled}\r\n                  placeholder=\"https://example.com/logo.png\"\r\n                />\r\n              </FormControl>\r\n              <FormMessage />\r\n            </FormItem>\r\n          )}\r\n        />\r\n      </div>\r\n\r\n      {/* Row 4: Icon */}\r\n      <FormField\r\n        control={form.control}\r\n        name=\"icon\"\r\n        render={({ field }) => (\r\n          <FormItem>\r\n            <FormLabel>URL Icon</FormLabel>\r\n            <FormControl>\r\n              <Input\r\n                {...field}\r\n                disabled={isDisabled}\r\n                placeholder=\"https://example.com/icon.png\"\r\n              />\r\n            </FormControl>\r\n            <FormMessage />\r\n          </FormItem>\r\n        )}\r\n      />\r\n\r\n      {/* Row 5: Active Status */}\r\n      <FormField\r\n        control={form.control}\r\n        name=\"isActive\"\r\n        render={({ field }) => (\r\n          <FormItem className=\"flex flex-row items-start space-x-3 space-y-0 rounded-md border p-4\">\r\n            <FormControl>\r\n              <Checkbox\r\n                checked={field.value}\r\n                onCheckedChange={field.onChange}\r\n                disabled={isDisabled}\r\n              />\r\n            </FormControl>\r\n            <div className=\"space-y-1 leading-none\">\r\n              <FormLabel>Kích hoạt</FormLabel>\r\n              <FormDescription>\r\n                Ngân hàng có thể được sử dụng nếu được kích hoạt\r\n              </FormDescription>\r\n            </div>\r\n          </FormItem>\r\n        )}\r\n      />\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": "AAAA;;CAEC;;;;AAKD;AAQA;AACA;AAZA;;;;;AAsBO,SAAS,eAAe,EAAE,IAAI,EAAE,IAAI,EAAuB;IAChE,MAAM,aAAa,SAAS;IAE5B,qBACE,uVAAC;QAAI,WAAU;;0BAEb,uVAAC;gBAAI,WAAU;;kCACb,uVAAC,yHAAA,CAAA,YAAS;wBACR,SAAS,KAAK,OAAO;wBACrB,MAAK;wBACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,uVAAC,yHAAA,CAAA,WAAQ;;kDACP,uVAAC,yHAAA,CAAA,YAAS;kDAAC;;;;;;kDACX,uVAAC,yHAAA,CAAA,cAAW;kDACV,cAAA,uVAAC,0HAAA,CAAA,QAAK;4CACH,GAAG,KAAK;4CACT,UAAU;4CACV,aAAY;;;;;;;;;;;kDAGhB,uVAAC,yHAAA,CAAA,cAAW;;;;;;;;;;;;;;;;kCAKlB,uVAAC,yHAAA,CAAA,YAAS;wBACR,SAAS,KAAK,OAAO;wBACrB,MAAK;wBACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,uVAAC,yHAAA,CAAA,WAAQ;;kDACP,uVAAC,yHAAA,CAAA,YAAS;kDAAC;;;;;;kDACX,uVAAC,yHAAA,CAAA,cAAW;kDACV,cAAA,uVAAC,0HAAA,CAAA,QAAK;4CACH,GAAG,KAAK;4CACT,UAAU;4CACV,aAAY;;;;;;;;;;;kDAGhB,uVAAC,yHAAA,CAAA,cAAW;;;;;;;;;;;;;;;;;;;;;;0BAOpB,uVAAC;gBAAI,WAAU;;kCACb,uVAAC,yHAAA,CAAA,YAAS;wBACR,SAAS,KAAK,OAAO;wBACrB,MAAK;wBACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,uVAAC,yHAAA,CAAA,WAAQ;;kDACP,uVAAC,yHAAA,CAAA,YAAS;kDAAC;;;;;;kDACX,uVAAC,yHAAA,CAAA,cAAW;kDACV,cAAA,uVAAC,0HAAA,CAAA,QAAK;4CACH,GAAG,KAAK;4CACT,UAAU;4CACV,aAAY;;;;;;;;;;;kDAGhB,uVAAC,yHAAA,CAAA,cAAW;;;;;;;;;;;;;;;;kCAKlB,uVAAC,yHAAA,CAAA,YAAS;wBACR,SAAS,KAAK,OAAO;wBACrB,MAAK;wBACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,uVAAC,yHAAA,CAAA,WAAQ;;kDACP,uVAAC,yHAAA,CAAA,YAAS;kDAAC;;;;;;kDACX,uVAAC,yHAAA,CAAA,cAAW;kDACV,cAAA,uVAAC,0HAAA,CAAA,QAAK;4CACH,GAAG,KAAK;4CACT,UAAU;4CACV,aAAY;;;;;;;;;;;kDAGhB,uVAAC,yHAAA,CAAA,cAAW;;;;;;;;;;;;;;;;;;;;;;0BAOpB,uVAAC;gBAAI,WAAU;;kCACb,uVAAC,yHAAA,CAAA,YAAS;wBACR,SAAS,KAAK,OAAO;wBACrB,MAAK;wBACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,uVAAC,yHAAA,CAAA,WAAQ;;kDACP,uVAAC,yHAAA,CAAA,YAAS;kDAAC;;;;;;kDACX,uVAAC,yHAAA,CAAA,cAAW;kDACV,cAAA,uVAAC,0HAAA,CAAA,QAAK;4CACH,GAAG,KAAK;4CACT,UAAU;4CACV,aAAY;;;;;;;;;;;kDAGhB,uVAAC,yHAAA,CAAA,cAAW;;;;;;;;;;;;;;;;kCAKlB,uVAAC,yHAAA,CAAA,YAAS;wBACR,SAAS,KAAK,OAAO;wBACrB,MAAK;wBACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,uVAAC,yHAAA,CAAA,WAAQ;;kDACP,uVAAC,yHAAA,CAAA,YAAS;kDAAC;;;;;;kDACX,uVAAC,yHAAA,CAAA,cAAW;kDACV,cAAA,uVAAC,0HAAA,CAAA,QAAK;4CACH,GAAG,KAAK;4CACT,UAAU;4CACV,aAAY;;;;;;;;;;;kDAGhB,uVAAC,yHAAA,CAAA,cAAW;;;;;;;;;;;;;;;;;;;;;;0BAOpB,uVAAC,yHAAA,CAAA,YAAS;gBACR,SAAS,KAAK,OAAO;gBACrB,MAAK;gBACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,uVAAC,yHAAA,CAAA,WAAQ;;0CACP,uVAAC,yHAAA,CAAA,YAAS;0CAAC;;;;;;0CACX,uVAAC,yHAAA,CAAA,cAAW;0CACV,cAAA,uVAAC,0HAAA,CAAA,QAAK;oCACH,GAAG,KAAK;oCACT,UAAU;oCACV,aAAY;;;;;;;;;;;0CAGhB,uVAAC,yHAAA,CAAA,cAAW;;;;;;;;;;;;;;;;0BAMlB,uVAAC,yHAAA,CAAA,YAAS;gBACR,SAAS,KAAK,OAAO;gBACrB,MAAK;gBACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,uVAAC,yHAAA,CAAA,WAAQ;wBAAC,WAAU;;0CAClB,uVAAC,yHAAA,CAAA,cAAW;0CACV,cAAA,uVAAC,6HAAA,CAAA,WAAQ;oCACP,SAAS,MAAM,KAAK;oCACpB,iBAAiB,MAAM,QAAQ;oCAC/B,UAAU;;;;;;;;;;;0CAGd,uVAAC;gCAAI,WAAU;;kDACb,uVAAC,yHAAA,CAAA,YAAS;kDAAC;;;;;;kDACX,uVAAC,yHAAA,CAAA,kBAAe;kDAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAS/B", "debugId": null}}, {"offset": {"line": 5203, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/bloomgoo/gold-exchange-platform/frontend/components/common/admin/ebanks/schemas/form-schema.ts"], "sourcesContent": ["/**\r\n * Form Schema - Validation schemas cho form ngân hàng\r\n * Sử dụng Zod để validate dữ liệu form\r\n */\r\n\r\nimport { z } from \"zod\";\r\n\r\nexport const formSchema = z.object({\r\n  brandName: z\r\n    .string()\r\n    .min(2, { message: \"Tên thương hiệu ngân hàng phải có ít nhất 2 ký tự\" })\r\n    .max(100, { message: \"Tên thương hiệu ngân hàng không được vượt quá 100 ký tự\" }),\r\n  fullName: z\r\n    .string()\r\n    .min(2, { message: \"Tên đầy đủ ngân hàng phải có ít nhất 2 ký tự\" })\r\n    .max(200, { message: \"Tên đầy đủ ngân hàng không được vượt quá 200 ký tự\" }),\r\n  shortName: z\r\n    .string()\r\n    .min(2, { message: \"Tên ngắn gọn ngân hàng phải có ít nhất 2 ký tự\" })\r\n    .max(50, { message: \"Tên ngắn gọn ngân hàng không được vượt quá 50 ký tự\" }),\r\n  code: z\r\n    .string()\r\n    .min(2, { message: \"<PERSON>ã ngân hàng phải có ít nhất 2 ký tự\" })\r\n    .max(20, { message: \"Mã ngân hàng không được vượt quá 20 ký tự\" }),\r\n  bin: z\r\n    .string()\r\n    .min(2, { message: \"Bin ngân hàng phải có ít nhất 2 ký tự\" })\r\n    .max(20, { message: \"Bin ngân hàng không được vượt quá 20 ký tự\" }),\r\n  logoPath: z\r\n    .string()\r\n    .url({ message: \"URL Logo không hợp lệ\" })\r\n    .optional()\r\n    .or(z.literal(\"\")),\r\n  icon: z\r\n    .string()\r\n    .url({ message: \"URL Icon không hợp lệ\" })\r\n    .optional()\r\n    .or(z.literal(\"\")),\r\n  isActive: z.boolean().default(true),\r\n});\r\n\r\nexport type BankFormValues = z.infer<typeof formSchema>;\r\n\r\n// Schema cho form cập nhật (giống với create)\r\nexport const bankUpdateFormSchema = formSchema;\r\n\r\nexport type BankUpdateFormValues = z.infer<typeof bankUpdateFormSchema>;\r\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;AAED;;AAEO,MAAM,aAAa,qLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IACjC,WAAW,qLAAA,CAAA,IAAC,CACT,MAAM,GACN,GAAG,CAAC,GAAG;QAAE,SAAS;IAAoD,GACtE,GAAG,CAAC,KAAK;QAAE,SAAS;IAA0D;IACjF,UAAU,qLAAA,CAAA,IAAC,CACR,MAAM,GACN,GAAG,CAAC,GAAG;QAAE,SAAS;IAA+C,GACjE,GAAG,CAAC,KAAK;QAAE,SAAS;IAAqD;IAC5E,WAAW,qLAAA,CAAA,IAAC,CACT,MAAM,GACN,GAAG,CAAC,GAAG;QAAE,SAAS;IAAiD,GACnE,GAAG,CAAC,IAAI;QAAE,SAAS;IAAsD;IAC5E,MAAM,qLAAA,CAAA,IAAC,CACJ,MAAM,GACN,GAAG,CAAC,GAAG;QAAE,SAAS;IAAuC,GACzD,GAAG,CAAC,IAAI;QAAE,SAAS;IAA4C;IAClE,KAAK,qLAAA,CAAA,IAAC,CACH,MAAM,GACN,GAAG,CAAC,GAAG;QAAE,SAAS;IAAwC,GAC1D,GAAG,CAAC,IAAI;QAAE,SAAS;IAA6C;IACnE,UAAU,qLAAA,CAAA,IAAC,CACR,MAAM,GACN,GAAG,CAAC;QAAE,SAAS;IAAwB,GACvC,QAAQ,GACR,EAAE,CAAC,qLAAA,CAAA,IAAC,CAAC,OAAO,CAAC;IAChB,MAAM,qLAAA,CAAA,IAAC,CACJ,MAAM,GACN,GAAG,CAAC;QAAE,SAAS;IAAwB,GACvC,QAAQ,GACR,EAAE,CAAC,qLAAA,CAAA,IAAC,CAAC,OAAO,CAAC;IAChB,UAAU,qLAAA,CAAA,IAAC,CAAC,OAAO,GAAG,OAAO,CAAC;AAChC;AAKO,MAAM,uBAAuB", "debugId": null}}, {"offset": {"line": 5253, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/bloomgoo/gold-exchange-platform/frontend/components/common/admin/ebanks/form/bank-form-hooks.ts"], "sourcesContent": ["'use client';\r\n/**\r\n * Bank Form Hooks - Custom hooks cho form logic\r\n */\r\n\r\nimport { useState, useEffect, useRef } from 'react';\r\nimport { useForm } from 'react-hook-form';\r\nimport { zodResolver } from '@hookform/resolvers/zod';\r\nimport { toast } from 'sonner';\r\nimport { api } from '@/lib/api';\r\nimport type { BankDto } from '../types';\r\n\r\ntype BankFormMode = 'create' | 'update' | 'view';\r\nimport { formSchema, bankUpdateFormSchema, type BankFormValues } from '../schemas/form-schema';\r\nimport { FORM_DEFAULT_VALUES, TOAST_MESSAGES } from '../utils/bank-constants';\r\n\r\ninterface UseBankFormProps {\r\n  isOpen: boolean;\r\n  bank?: BankDto | null;\r\n  mode: BankFormMode;\r\n  onSuccess?: () => void;\r\n}\r\n\r\nexport function useBankForm({ isOpen, bank, mode, onSuccess }: UseBankFormProps) {\r\n  const [isLoading, setIsLoading] = useState(false);\r\n  const [isFormReady, setIsFormReady] = useState(false);\r\n  const formIsDirty = useRef(false);\r\n\r\n  // Khởi tạo form với schema phù hợp với mode\r\n  const form = useForm<BankFormValues>({\r\n    resolver: zodResolver(mode === 'create' ? formSchema : bankUpdateFormSchema),\r\n    defaultValues: FORM_DEFAULT_VALUES,\r\n  });\r\n\r\n  // Theo dõi thay đổi của form\r\n  useEffect(() => {\r\n    const subscription = form.watch(() => {\r\n      formIsDirty.current = form.formState.isDirty;\r\n    });\r\n    return () => subscription.unsubscribe();\r\n  }, [form]);\r\n\r\n  // Cập nhật form khi có dữ liệu ngân hàng hoặc khi modal mở/đóng\r\n  useEffect(() => {\r\n    if (!isOpen) {\r\n      // Reset form khi modal đóng\r\n      setTimeout(() => {\r\n        form.reset();\r\n        formIsDirty.current = false;\r\n        setIsFormReady(false);\r\n        setIsLoading(false);\r\n      }, 300); // Đợi animation đóng hoàn tất\r\n    } else {\r\n      // Đặt trạng thái đang tải khi mở modal\r\n      setIsFormReady(false);\r\n      setIsLoading(true);\r\n\r\n      if (mode === 'update' || mode === 'view') {\r\n        if (bank?.id) {\r\n          // Gọi API để lấy thông tin chi tiết của ngân hàng\r\n          api.get<BankDto>(`banks/${bank.id}`)\r\n            .then(response => {\r\n              // Chuyển đổi dữ liệu ngân hàng để phù hợp với form\r\n              const formData = {\r\n                brandName: response.brandName || '',\r\n                fullName: response.fullName || '',\r\n                shortName: response.shortName || '',\r\n                code: response.code || '',\r\n                bin: response.bin || '',\r\n                logoPath: response.logoPath || '',\r\n                icon: response.icon || '',\r\n                isActive: response.isActive !== undefined ? response.isActive : true,\r\n              };\r\n\r\n              // Reset form với dữ liệu ngân hàng\r\n              form.reset(formData);\r\n              formIsDirty.current = false;\r\n              setIsFormReady(true);\r\n              setIsLoading(false);\r\n            })\r\n            .catch(error => {\r\n              console.error('Error fetching bank details:', error);\r\n              toast.error(TOAST_MESSAGES.FETCH_DETAIL_ERROR);\r\n              setIsLoading(false);\r\n              setIsFormReady(true);\r\n\r\n              // Sử dụng dữ liệu ban đầu nếu không thể lấy dữ liệu mới\r\n              if (bank) {\r\n                const fallbackFormData = {\r\n                  brandName: bank.brandName || '',\r\n                  fullName: bank.fullName || '',\r\n                  shortName: bank.shortName || '',\r\n                  code: bank.code || '',\r\n                  bin: bank.bin || '',\r\n                  logoPath: bank.logoPath || '',\r\n                  icon: bank.icon || '',\r\n                  isActive: bank.isActive !== undefined ? bank.isActive : true,\r\n                };\r\n                form.reset(fallbackFormData);\r\n              }\r\n            });\r\n        } else {\r\n          // Không có ID ngân hàng\r\n          setIsFormReady(true);\r\n          setIsLoading(false);\r\n        }\r\n      } else if (mode === 'create') {\r\n        // Reset form với giá trị mặc định khi tạo mới\r\n        setTimeout(() => {\r\n          form.reset(FORM_DEFAULT_VALUES);\r\n          formIsDirty.current = false;\r\n          setIsFormReady(true);\r\n          setIsLoading(false);\r\n        }, 300);\r\n      }\r\n    }\r\n  }, [isOpen, bank?.id, mode, form]);\r\n\r\n  // Submit handler\r\n  const handleSubmit = async (data: BankFormValues): Promise<boolean> => {\r\n    if (mode === 'view') return false;\r\n\r\n    setIsLoading(true);\r\n    try {\r\n      // Logic submit sẽ được handle bởi parent component\r\n      // Hook này chỉ quản lý form state\r\n      if (onSuccess) {\r\n        onSuccess();\r\n      }\r\n      return true;\r\n    } catch (error) {\r\n      console.error('Form submission error:', error);\r\n      return false;\r\n    } finally {\r\n      setIsLoading(false);\r\n    }\r\n  };\r\n\r\n  // Reset form\r\n  const resetForm = () => {\r\n    form.reset();\r\n    formIsDirty.current = false;\r\n    setIsFormReady(false);\r\n    setIsLoading(false);\r\n  };\r\n\r\n  return {\r\n    // Form instance\r\n    form,\r\n\r\n    // States\r\n    isLoading,\r\n    isFormReady,\r\n    formIsDirty: formIsDirty.current,\r\n\r\n    // Actions\r\n    handleSubmit: form.handleSubmit(handleSubmit),\r\n    resetForm,\r\n  };\r\n}\r\n"], "names": [], "mappings": ";;;AACA;;CAEC,GAED;AACA;AACA;AACA;AACA;AAIA;AACA;AAdA;;;;;;;;AAuBO,SAAS,YAAY,EAAE,MAAM,EAAE,IAAI,EAAE,IAAI,EAAE,SAAS,EAAoB;IAC7E,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,8SAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,8SAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,cAAc,CAAA,GAAA,8SAAA,CAAA,SAAM,AAAD,EAAE;IAE3B,4CAA4C;IAC5C,MAAM,OAAO,CAAA,GAAA,uPAAA,CAAA,UAAO,AAAD,EAAkB;QACnC,UAAU,CAAA,GAAA,qQAAA,CAAA,cAAW,AAAD,EAAE,SAAS,WAAW,oKAAA,CAAA,aAAU,GAAG,oKAAA,CAAA,uBAAoB;QAC3E,eAAe,qKAAA,CAAA,sBAAmB;IACpC;IAEA,6BAA6B;IAC7B,CAAA,GAAA,8SAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,eAAe,KAAK,KAAK,CAAC;YAC9B,YAAY,OAAO,GAAG,KAAK,SAAS,CAAC,OAAO;QAC9C;QACA,OAAO,IAAM,aAAa,WAAW;IACvC,GAAG;QAAC;KAAK;IAET,gEAAgE;IAChE,CAAA,GAAA,8SAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,QAAQ;YACX,4BAA4B;YAC5B,WAAW;gBACT,KAAK,KAAK;gBACV,YAAY,OAAO,GAAG;gBACtB,eAAe;gBACf,aAAa;YACf,GAAG,MAAM,8BAA8B;QACzC,OAAO;YACL,uCAAuC;YACvC,eAAe;YACf,aAAa;YAEb,IAAI,SAAS,YAAY,SAAS,QAAQ;gBACxC,IAAI,MAAM,IAAI;oBACZ,kDAAkD;oBAClD,0GAAA,CAAA,MAAG,CAAC,GAAG,CAAU,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE,EAChC,IAAI,CAAC,CAAA;wBACJ,mDAAmD;wBACnD,MAAM,WAAW;4BACf,WAAW,SAAS,SAAS,IAAI;4BACjC,UAAU,SAAS,QAAQ,IAAI;4BAC/B,WAAW,SAAS,SAAS,IAAI;4BACjC,MAAM,SAAS,IAAI,IAAI;4BACvB,KAAK,SAAS,GAAG,IAAI;4BACrB,UAAU,SAAS,QAAQ,IAAI;4BAC/B,MAAM,SAAS,IAAI,IAAI;4BACvB,UAAU,SAAS,QAAQ,KAAK,YAAY,SAAS,QAAQ,GAAG;wBAClE;wBAEA,mCAAmC;wBACnC,KAAK,KAAK,CAAC;wBACX,YAAY,OAAO,GAAG;wBACtB,eAAe;wBACf,aAAa;oBACf,GACC,KAAK,CAAC,CAAA;wBACL,QAAQ,KAAK,CAAC,gCAAgC;wBAC9C,wQAAA,CAAA,QAAK,CAAC,KAAK,CAAC,qKAAA,CAAA,iBAAc,CAAC,kBAAkB;wBAC7C,aAAa;wBACb,eAAe;wBAEf,wDAAwD;wBACxD,IAAI,MAAM;4BACR,MAAM,mBAAmB;gCACvB,WAAW,KAAK,SAAS,IAAI;gCAC7B,UAAU,KAAK,QAAQ,IAAI;gCAC3B,WAAW,KAAK,SAAS,IAAI;gCAC7B,MAAM,KAAK,IAAI,IAAI;gCACnB,KAAK,KAAK,GAAG,IAAI;gCACjB,UAAU,KAAK,QAAQ,IAAI;gCAC3B,MAAM,KAAK,IAAI,IAAI;gCACnB,UAAU,KAAK,QAAQ,KAAK,YAAY,KAAK,QAAQ,GAAG;4BAC1D;4BACA,KAAK,KAAK,CAAC;wBACb;oBACF;gBACJ,OAAO;oBACL,wBAAwB;oBACxB,eAAe;oBACf,aAAa;gBACf;YACF,OAAO,IAAI,SAAS,UAAU;gBAC5B,8CAA8C;gBAC9C,WAAW;oBACT,KAAK,KAAK,CAAC,qKAAA,CAAA,sBAAmB;oBAC9B,YAAY,OAAO,GAAG;oBACtB,eAAe;oBACf,aAAa;gBACf,GAAG;YACL;QACF;IACF,GAAG;QAAC;QAAQ,MAAM;QAAI;QAAM;KAAK;IAEjC,iBAAiB;IACjB,MAAM,eAAe,OAAO;QAC1B,IAAI,SAAS,QAAQ,OAAO;QAE5B,aAAa;QACb,IAAI;YACF,mDAAmD;YACnD,kCAAkC;YAClC,IAAI,WAAW;gBACb;YACF;YACA,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;YACxC,OAAO;QACT,SAAU;YACR,aAAa;QACf;IACF;IAEA,aAAa;IACb,MAAM,YAAY;QAChB,KAAK,KAAK;QACV,YAAY,OAAO,GAAG;QACtB,eAAe;QACf,aAAa;IACf;IAEA,OAAO;QACL,gBAAgB;QAChB;QAEA,SAAS;QACT;QACA;QACA,aAAa,YAAY,OAAO;QAEhC,UAAU;QACV,cAAc,KAAK,YAAY,CAAC;QAChC;IACF;AACF", "debugId": null}}, {"offset": {"line": 5409, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/bloomgoo/gold-exchange-platform/frontend/components/common/admin/ebanks/form/bank-form-modal.tsx"], "sourcesContent": ["/**\r\n * BankFormModal - Modal wrapper cho form ngân hàng\r\n */\r\n\r\n'use client';\r\n\r\nimport { useState } from 'react';\r\nimport { Loader2 } from 'lucide-react';\r\nimport {\r\n  Dialog,\r\n  DialogContent,\r\n  DialogDescription,\r\n  DialogFooter,\r\n  DialogHeader,\r\n  DialogTitle,\r\n} from '@/components/ui/dialog';\r\nimport {\r\n  AlertDialog,\r\n  AlertDialogAction,\r\n  AlertDialogCancel,\r\n  AlertDialogContent,\r\n  AlertDialogDescription,\r\n  AlertDialogFooter,\r\n  AlertDialogHeader,\r\n  AlertDialogTitle,\r\n} from '@/components/ui/alert-dialog';\r\nimport { Form } from '@/components/ui/form';\r\nimport { Button } from '@/components/ui/button';\r\nimport type { BankDto } from '../types';\r\n\r\ntype BankFormMode = 'create' | 'update' | 'view';\r\nimport { BankFormFields } from './bank-form-fields';\r\nimport { useBankForm } from './bank-form-hooks';\r\n\r\ninterface BankFormModalProps {\r\n  isOpen: boolean;\r\n  onClose: () => void;\r\n  bank?: BankDto | null;\r\n  mode: BankFormMode;\r\n  onSubmit: (data: any) => Promise<boolean>;\r\n  onSuccess?: () => void;\r\n}\r\n\r\nexport function BankFormModal({\r\n  isOpen,\r\n  onClose,\r\n  bank,\r\n  mode,\r\n  onSubmit,\r\n  onSuccess,\r\n}: BankFormModalProps) {\r\n  const [showConfirmDialog, setShowConfirmDialog] = useState(false);\r\n\r\n  const { form, isLoading, isFormReady, formIsDirty } = useBankForm({\r\n    isOpen,\r\n    bank,\r\n    mode,\r\n    onSuccess,\r\n  });\r\n\r\n  // Xử lý đóng form\r\n  const handleClose = () => {\r\n    if (mode !== 'view' && formIsDirty) {\r\n      setShowConfirmDialog(true);\r\n    } else {\r\n      onClose();\r\n    }\r\n  };\r\n\r\n  // Xử lý submit form\r\n  const handleFormSubmit = async (data: any) => {\r\n    const success = await onSubmit(data);\r\n    if (success) {\r\n      onClose();\r\n    }\r\n  };\r\n\r\n  // Titles và descriptions\r\n  const title = {\r\n    create: 'Thêm ngân hàng mới',\r\n    update: 'Cập nhật ngân hàng',\r\n    view: 'Thông tin ngân hàng',\r\n  }[mode];\r\n\r\n  const description = {\r\n    create: 'Điền thông tin để tạo ngân hàng mới',\r\n    update: 'Cập nhật thông tin ngân hàng',\r\n    view: 'Xem chi tiết thông tin ngân hàng',\r\n  }[mode];\r\n\r\n  return (\r\n    <>\r\n      <Dialog open={isOpen} onOpenChange={(open) => !open && handleClose()}>\r\n        <DialogContent className=\"sm:max-w-[600px]\">\r\n          <DialogHeader>\r\n            <DialogTitle>{title}</DialogTitle>\r\n            <DialogDescription>{description}</DialogDescription>\r\n          </DialogHeader>\r\n\r\n          {!isFormReady ? (\r\n            <div className=\"flex flex-col items-center justify-center py-8\">\r\n              <Loader2 className=\"h-8 w-8 animate-spin text-primary mb-4\" />\r\n              <p className=\"text-sm text-muted-foreground\">Đang tải dữ liệu...</p>\r\n            </div>\r\n          ) : (\r\n            <Form {...form}>\r\n              <form onSubmit={form.handleSubmit(handleFormSubmit)} className=\"space-y-4\">\r\n                <BankFormFields form={form} mode={mode} />\r\n\r\n                <DialogFooter>\r\n                  <Button\r\n                    type=\"button\"\r\n                    variant=\"outline\"\r\n                    onClick={handleClose}\r\n                  >\r\n                    {mode === 'view' ? 'Đóng' : 'Hủy'}\r\n                  </Button>\r\n\r\n                  {mode !== 'view' && (\r\n                    <Button type=\"submit\" disabled={isLoading}>\r\n                      {isLoading && <Loader2 className=\"mr-2 h-4 w-4 animate-spin\" />}\r\n                      {mode === 'create' ? 'Tạo ngân hàng' : 'Cập nhật'}\r\n                    </Button>\r\n                  )}\r\n                </DialogFooter>\r\n              </form>\r\n            </Form>\r\n          )}\r\n        </DialogContent>\r\n      </Dialog>\r\n\r\n      <AlertDialog open={showConfirmDialog} onOpenChange={setShowConfirmDialog}>\r\n        <AlertDialogContent>\r\n          <AlertDialogHeader>\r\n            <AlertDialogTitle>Bạn có chắc muốn hủy?</AlertDialogTitle>\r\n            <AlertDialogDescription>\r\n              Các thay đổi của bạn sẽ không được lưu. Bạn có chắc muốn hủy bỏ không?\r\n            </AlertDialogDescription>\r\n          </AlertDialogHeader>\r\n          <AlertDialogFooter>\r\n            <AlertDialogCancel>Tiếp tục chỉnh sửa</AlertDialogCancel>\r\n            <AlertDialogAction\r\n              onClick={() => {\r\n                setShowConfirmDialog(false);\r\n                onClose();\r\n              }}\r\n              className=\"bg-destructive hover:bg-destructive/90\"\r\n            >\r\n              Hủy bỏ\r\n            </AlertDialogAction>\r\n          </AlertDialogFooter>\r\n        </AlertDialogContent>\r\n      </AlertDialog>\r\n    </>\r\n  );\r\n}\r\n"], "names": [], "mappings": "AAAA;;CAEC;;;;AAID;AACA;AACA;AAQA;AAUA;AACA;AAIA;AACA;AA5BA;;;;;;;;;;AAuCO,SAAS,cAAc,EAC5B,MAAM,EACN,OAAO,EACP,IAAI,EACJ,IAAI,EACJ,QAAQ,EACR,SAAS,EACU;IACnB,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,8SAAA,CAAA,WAAQ,AAAD,EAAE;IAE3D,MAAM,EAAE,IAAI,EAAE,SAAS,EAAE,WAAW,EAAE,WAAW,EAAE,GAAG,CAAA,GAAA,wKAAA,CAAA,cAAW,AAAD,EAAE;QAChE;QACA;QACA;QACA;IACF;IAEA,kBAAkB;IAClB,MAAM,cAAc;QAClB,IAAI,SAAS,UAAU,aAAa;YAClC,qBAAqB;QACvB,OAAO;YACL;QACF;IACF;IAEA,oBAAoB;IACpB,MAAM,mBAAmB,OAAO;QAC9B,MAAM,UAAU,MAAM,SAAS;QAC/B,IAAI,SAAS;YACX;QACF;IACF;IAEA,yBAAyB;IACzB,MAAM,QAAQ;QACZ,QAAQ;QACR,QAAQ;QACR,MAAM;IACR,CAAC,CAAC,KAAK;IAEP,MAAM,cAAc;QAClB,QAAQ;QACR,QAAQ;QACR,MAAM;IACR,CAAC,CAAC,KAAK;IAEP,qBACE;;0BACE,uVAAC,2HAAA,CAAA,SAAM;gBAAC,MAAM;gBAAQ,cAAc,CAAC,OAAS,CAAC,QAAQ;0BACrD,cAAA,uVAAC,2HAAA,CAAA,gBAAa;oBAAC,WAAU;;sCACvB,uVAAC,2HAAA,CAAA,eAAY;;8CACX,uVAAC,2HAAA,CAAA,cAAW;8CAAE;;;;;;8CACd,uVAAC,2HAAA,CAAA,oBAAiB;8CAAE;;;;;;;;;;;;wBAGrB,CAAC,4BACA,uVAAC;4BAAI,WAAU;;8CACb,uVAAC,qSAAA,CAAA,UAAO;oCAAC,WAAU;;;;;;8CACnB,uVAAC;oCAAE,WAAU;8CAAgC;;;;;;;;;;;iDAG/C,uVAAC,yHAAA,CAAA,OAAI;4BAAE,GAAG,IAAI;sCACZ,cAAA,uVAAC;gCAAK,UAAU,KAAK,YAAY,CAAC;gCAAmB,WAAU;;kDAC7D,uVAAC,0KAAA,CAAA,iBAAc;wCAAC,MAAM;wCAAM,MAAM;;;;;;kDAElC,uVAAC,2HAAA,CAAA,eAAY;;0DACX,uVAAC,2HAAA,CAAA,SAAM;gDACL,MAAK;gDACL,SAAQ;gDACR,SAAS;0DAER,SAAS,SAAS,SAAS;;;;;;4CAG7B,SAAS,wBACR,uVAAC,2HAAA,CAAA,SAAM;gDAAC,MAAK;gDAAS,UAAU;;oDAC7B,2BAAa,uVAAC,qSAAA,CAAA,UAAO;wDAAC,WAAU;;;;;;oDAChC,SAAS,WAAW,kBAAkB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAUvD,uVAAC,oIAAA,CAAA,cAAW;gBAAC,MAAM;gBAAmB,cAAc;0BAClD,cAAA,uVAAC,oIAAA,CAAA,qBAAkB;;sCACjB,uVAAC,oIAAA,CAAA,oBAAiB;;8CAChB,uVAAC,oIAAA,CAAA,mBAAgB;8CAAC;;;;;;8CAClB,uVAAC,oIAAA,CAAA,yBAAsB;8CAAC;;;;;;;;;;;;sCAI1B,uVAAC,oIAAA,CAAA,oBAAiB;;8CAChB,uVAAC,oIAAA,CAAA,oBAAiB;8CAAC;;;;;;8CACnB,uVAAC,oIAAA,CAAA,oBAAiB;oCAChB,SAAS;wCACP,qBAAqB;wCACrB;oCACF;oCACA,WAAU;8CACX;;;;;;;;;;;;;;;;;;;;;;;;;AAQb", "debugId": null}}, {"offset": {"line": 5666, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/bloomgoo/gold-exchange-platform/frontend/components/common/admin/ebanks/detail/bank-detail-header.tsx"], "sourcesContent": ["/**\r\n * BankDetailHeader - Header section cho detail sheet\r\n */\r\n\r\n'use client';\r\n\r\nimport { <PERSON>cil, Loader2 } from 'lucide-react';\r\nimport { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';\r\nimport { Badge } from '@/components/ui/badge';\r\nimport { Button } from '@/components/ui/button';\r\nimport { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';\r\nimport {\r\n  SheetDescription,\r\n  SheetTitle\r\n} from '@/components/ui/sheet';\r\nimport type { BankDto } from '../types';\r\nimport { getBankAvatarUrl, getBankAvatarFallback, getStatusVariant } from '../utils/bank-helpers';\r\n\r\ninterface BankDetailHeaderProps {\r\n  bank: BankDto;\r\n  loading: boolean;\r\n  onEdit: (bank: BankDto) => void;\r\n}\r\n\r\nexport function BankDetailHeader({ bank, loading, onEdit }: BankDetailHeaderProps) {\r\n  const getStatusBadge = (isActive: boolean | undefined) => {\r\n    if (isActive === undefined) return null;\r\n    return (\r\n      <Badge variant={getStatusVariant(isActive)}>\r\n        {isActive ? 'Đang hoạt động' : 'Vô hiệu hóa'}\r\n      </Badge>\r\n    );\r\n  };\r\n\r\n  return (\r\n    <div className=\"px-0 py-0 border-b\">\r\n      {/* Avatar và thông tin cơ bản */}\r\n      <div className=\"flex items-center gap-4 px-2\">\r\n        <Avatar className=\"h-16 w-16 shrink-0\">\r\n          {loading ? (\r\n            <div className=\"flex items-center justify-center w-full h-full bg-muted\">\r\n              <Loader2 className=\"h-8 w-8 animate-spin text-muted-foreground\" />\r\n            </div>\r\n          ) : (\r\n            <>\r\n              <AvatarImage \r\n                src={getBankAvatarUrl(bank.brandName, bank.logoPath)} \r\n                alt={bank.brandName} \r\n              />\r\n              <AvatarFallback>{getBankAvatarFallback(bank.brandName)}</AvatarFallback>\r\n            </>\r\n          )}\r\n        </Avatar>\r\n        \r\n        <div className=\"flex-1\">\r\n          <SheetTitle className=\"text-md font-semibold\">\r\n            {loading ? (\r\n              <div className=\"flex items-center gap-2\">\r\n                <Loader2 className=\"h-4 w-4 animate-spin\" />\r\n                <span>Đang tải...</span>\r\n              </div>\r\n            ) : (\r\n              bank.brandName\r\n            )}\r\n          </SheetTitle>\r\n          \r\n          <SheetDescription className=\"flex items-center gap-2\">\r\n            {loading ? (\r\n              <div className=\"flex items-center gap-2\">\r\n                <Loader2 className=\"h-3 w-3 animate-spin\" />\r\n                <span>Đang tải thông tin...</span>\r\n              </div>\r\n            ) : (\r\n              <>\r\n                <span>Mã: {bank.code}</span>\r\n                {getStatusBadge(bank.isActive)}\r\n              </>\r\n            )}\r\n          </SheetDescription>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Đường kẻ phân tách */}\r\n      <div className=\"h-px w-full bg-border\"></div>\r\n\r\n      {/* Các nút chức năng */}\r\n      <div className=\"flex items-center gap-2 px-4 mb-2\">\r\n        <TooltipProvider>\r\n          <Tooltip>\r\n            <TooltipTrigger asChild>\r\n              <Button \r\n                variant=\"outline\" \r\n                size=\"icon\" \r\n                className=\"h-8 w-8\" \r\n                onClick={() => onEdit(bank)}\r\n                disabled={loading}\r\n              >\r\n                <Pencil className=\"h-4 w-4\" />\r\n              </Button>\r\n            </TooltipTrigger>\r\n            <TooltipContent>\r\n              <p>Chỉnh sửa</p>\r\n            </TooltipContent>\r\n          </Tooltip>\r\n        </TooltipProvider>\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": "AAAA;;CAEC;;;;AAID;AAAA;AACA;AACA;AACA;AACA;AACA;AAKA;AAZA;;;;;;;;;AAoBO,SAAS,iBAAiB,EAAE,IAAI,EAAE,OAAO,EAAE,MAAM,EAAyB;IAC/E,MAAM,iBAAiB,CAAC;QACtB,IAAI,aAAa,WAAW,OAAO;QACnC,qBACE,uVAAC,0HAAA,CAAA,QAAK;YAAC,SAAS,CAAA,GAAA,mKAAA,CAAA,mBAAgB,AAAD,EAAE;sBAC9B,WAAW,mBAAmB;;;;;;IAGrC;IAEA,qBACE,uVAAC;QAAI,WAAU;;0BAEb,uVAAC;gBAAI,WAAU;;kCACb,uVAAC,2HAAA,CAAA,SAAM;wBAAC,WAAU;kCACf,wBACC,uVAAC;4BAAI,WAAU;sCACb,cAAA,uVAAC,qSAAA,CAAA,UAAO;gCAAC,WAAU;;;;;;;;;;iDAGrB;;8CACE,uVAAC,2HAAA,CAAA,cAAW;oCACV,KAAK,CAAA,GAAA,mKAAA,CAAA,mBAAgB,AAAD,EAAE,KAAK,SAAS,EAAE,KAAK,QAAQ;oCACnD,KAAK,KAAK,SAAS;;;;;;8CAErB,uVAAC,2HAAA,CAAA,iBAAc;8CAAE,CAAA,GAAA,mKAAA,CAAA,wBAAqB,AAAD,EAAE,KAAK,SAAS;;;;;;;;;;;;;kCAK3D,uVAAC;wBAAI,WAAU;;0CACb,uVAAC,0HAAA,CAAA,aAAU;gCAAC,WAAU;0CACnB,wBACC,uVAAC;oCAAI,WAAU;;sDACb,uVAAC,qSAAA,CAAA,UAAO;4CAAC,WAAU;;;;;;sDACnB,uVAAC;sDAAK;;;;;;;;;;;2CAGR,KAAK,SAAS;;;;;;0CAIlB,uVAAC,0HAAA,CAAA,mBAAgB;gCAAC,WAAU;0CACzB,wBACC,uVAAC;oCAAI,WAAU;;sDACb,uVAAC,qSAAA,CAAA,UAAO;4CAAC,WAAU;;;;;;sDACnB,uVAAC;sDAAK;;;;;;;;;;;yDAGR;;sDACE,uVAAC;;gDAAK;gDAAK,KAAK,IAAI;;;;;;;wCACnB,eAAe,KAAK,QAAQ;;;;;;;;;;;;;;;;;;;;0BAQvC,uVAAC;gBAAI,WAAU;;;;;;0BAGf,uVAAC;gBAAI,WAAU;0BACb,cAAA,uVAAC,4HAAA,CAAA,kBAAe;8BACd,cAAA,uVAAC,4HAAA,CAAA,UAAO;;0CACN,uVAAC,4HAAA,CAAA,iBAAc;gCAAC,OAAO;0CACrB,cAAA,uVAAC,2HAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,MAAK;oCACL,WAAU;oCACV,SAAS,IAAM,OAAO;oCACtB,UAAU;8CAEV,cAAA,uVAAC,0RAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;;;;;;;;;;;0CAGtB,uVAAC,4HAAA,CAAA,iBAAc;0CACb,cAAA,uVAAC;8CAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOjB", "debugId": null}}, {"offset": {"line": 5914, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/bloomgoo/gold-exchange-platform/frontend/components/common/admin/ebanks/detail/bank-detail-info.tsx"], "sourcesContent": ["/**\r\n * BankDetailInfo - Basic info section cho detail sheet\r\n */\r\n\r\n'use client';\r\n\r\nimport { Building2, Hash, CreditCard, UserCheck } from 'lucide-react';\r\nimport { Badge } from '@/components/ui/badge';\r\nimport { InfoCard } from '@/components/info-card';\r\nimport type { BankDto } from '../types';\r\nimport { getStatusVariant } from '../utils/bank-helpers';\r\n\r\ninterface BankDetailInfoProps {\r\n  bank: BankDto;\r\n}\r\n\r\nexport function BankDetailInfo({ bank }: BankDetailInfoProps) {\r\n  const getStatusBadge = (isActive: boolean | undefined) => {\r\n    if (isActive === undefined) return null;\r\n    return (\r\n      <Badge variant={getStatusVariant(isActive)}>\r\n        {isActive ? 'Đang hoạt động' : 'Vô hiệu hóa'}\r\n      </Badge>\r\n    );\r\n  };\r\n\r\n  return (\r\n    <InfoCard\r\n      title=\"Thông tin ngân hàng\"\r\n      description=\"Thông tin cơ bản của ngân hàng\"\r\n      className=\"py-4\"\r\n    >\r\n      <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\r\n        <div className=\"space-y-1\">\r\n          <div className=\"text-xs font-medium text-muted-foreground flex items-center gap-1\">\r\n            <Building2 className=\"h-3.5 w-3.5\" /> Tên thương hiệu\r\n          </div>\r\n          <div className=\"text-md\">{bank.brandName}</div>\r\n        </div>\r\n        \r\n        <div className=\"space-y-1\">\r\n          <div className=\"text-xs font-medium text-muted-foreground flex items-center gap-1\">\r\n            <Building2 className=\"h-3.5 w-3.5\" /> Tên đầy đủ\r\n          </div>\r\n          <div className=\"text-md\">{bank.fullName}</div>\r\n        </div>\r\n        \r\n        <div className=\"space-y-1\">\r\n          <div className=\"text-xs font-medium text-muted-foreground flex items-center gap-1\">\r\n            <Building2 className=\"h-3.5 w-3.5\" /> Tên ngắn gọn\r\n          </div>\r\n          <div className=\"text-md\">{bank.shortName}</div>\r\n        </div>\r\n        \r\n        <div className=\"space-y-1\">\r\n          <div className=\"text-xs font-medium text-muted-foreground flex items-center gap-1\">\r\n            <Hash className=\"h-3.5 w-3.5\" /> Mã ngân hàng\r\n          </div>\r\n          <div className=\"text-md\">{bank.code}</div>\r\n        </div>\r\n        \r\n        <div className=\"space-y-1\">\r\n          <div className=\"text-xs font-medium text-muted-foreground flex items-center gap-1\">\r\n            <CreditCard className=\"h-3.5 w-3.5\" /> Bin ngân hàng\r\n          </div>\r\n          <div className=\"text-md\">{bank.bin}</div>\r\n        </div>\r\n        \r\n        <div className=\"space-y-1\">\r\n          <div className=\"text-xs font-medium text-muted-foreground flex items-center gap-1\">\r\n            <UserCheck className=\"h-3.5 w-3.5\" /> Trạng thái\r\n          </div>\r\n          <div className=\"text-md\">{getStatusBadge(bank.isActive)}</div>\r\n        </div>\r\n      </div>\r\n    </InfoCard>\r\n  );\r\n}\r\n"], "names": [], "mappings": "AAAA;;CAEC;;;;AAID;AAAA;AAAA;AAAA;AACA;AACA;AAEA;AANA;;;;;;AAYO,SAAS,eAAe,EAAE,IAAI,EAAuB;IAC1D,MAAM,iBAAiB,CAAC;QACtB,IAAI,aAAa,WAAW,OAAO;QACnC,qBACE,uVAAC,0HAAA,CAAA,QAAK;YAAC,SAAS,CAAA,GAAA,mKAAA,CAAA,mBAAgB,AAAD,EAAE;sBAC9B,WAAW,mBAAmB;;;;;;IAGrC;IAEA,qBACE,uVAAC,2HAAA,CAAA,WAAQ;QACP,OAAM;QACN,aAAY;QACZ,WAAU;kBAEV,cAAA,uVAAC;YAAI,WAAU;;8BACb,uVAAC;oBAAI,WAAU;;sCACb,uVAAC;4BAAI,WAAU;;8CACb,uVAAC,oSAAA,CAAA,YAAS;oCAAC,WAAU;;;;;;gCAAgB;;;;;;;sCAEvC,uVAAC;4BAAI,WAAU;sCAAW,KAAK,SAAS;;;;;;;;;;;;8BAG1C,uVAAC;oBAAI,WAAU;;sCACb,uVAAC;4BAAI,WAAU;;8CACb,uVAAC,oSAAA,CAAA,YAAS;oCAAC,WAAU;;;;;;gCAAgB;;;;;;;sCAEvC,uVAAC;4BAAI,WAAU;sCAAW,KAAK,QAAQ;;;;;;;;;;;;8BAGzC,uVAAC;oBAAI,WAAU;;sCACb,uVAAC;4BAAI,WAAU;;8CACb,uVAAC,oSAAA,CAAA,YAAS;oCAAC,WAAU;;;;;;gCAAgB;;;;;;;sCAEvC,uVAAC;4BAAI,WAAU;sCAAW,KAAK,SAAS;;;;;;;;;;;;8BAG1C,uVAAC;oBAAI,WAAU;;sCACb,uVAAC;4BAAI,WAAU;;8CACb,uVAAC,sRAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;gCAAgB;;;;;;;sCAElC,uVAAC;4BAAI,WAAU;sCAAW,KAAK,IAAI;;;;;;;;;;;;8BAGrC,uVAAC;oBAAI,WAAU;;sCACb,uVAAC;4BAAI,WAAU;;8CACb,uVAAC,sSAAA,CAAA,aAAU;oCAAC,WAAU;;;;;;gCAAgB;;;;;;;sCAExC,uVAAC;4BAAI,WAAU;sCAAW,KAAK,GAAG;;;;;;;;;;;;8BAGpC,uVAAC;oBAAI,WAAU;;sCACb,uVAAC;4BAAI,WAAU;;8CACb,uVAAC,oSAAA,CAAA,YAAS;oCAAC,WAAU;;;;;;gCAAgB;;;;;;;sCAEvC,uVAAC;4BAAI,WAAU;sCAAW,eAAe,KAAK,QAAQ;;;;;;;;;;;;;;;;;;;;;;;AAKhE", "debugId": null}}, {"offset": {"line": 6174, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/bloomgoo/gold-exchange-platform/frontend/components/common/admin/ebanks/detail/bank-detail-images.tsx"], "sourcesContent": ["/**\r\n * BankDetailImages - Images section cho detail sheet\r\n */\r\n\r\n'use client';\r\n\r\nimport { Image as ImageIcon } from 'lucide-react';\r\nimport { InfoCard } from '@/components/info-card';\r\nimport type { BankDto } from '../types';\r\n\r\ninterface BankDetailImagesProps {\r\n  bank: BankDto;\r\n}\r\n\r\nexport function BankDetailImages({ bank }: BankDetailImagesProps) {\r\n  return (\r\n    <InfoCard\r\n      title=\"Thông tin hình ảnh\"\r\n      description=\"Logo và icon của ngân hàng\"\r\n      className=\"py-4\"\r\n    >\r\n      <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\r\n        <div className=\"space-y-1\">\r\n          <div className=\"text-xs font-medium text-muted-foreground flex items-center gap-1\">\r\n            <ImageIcon className=\"h-3.5 w-3.5\" /> Logo\r\n          </div>\r\n          <div>\r\n            {bank.logoPath ? (\r\n              <div className=\"flex flex-col gap-1\">\r\n                <img \r\n                  src={bank.logoPath} \r\n                  alt=\"Logo\" \r\n                  className=\"max-w-[120px] max-h-[60px] object-contain border rounded p-1\" \r\n                />\r\n                <a \r\n                  href={bank.logoPath} \r\n                  target=\"_blank\" \r\n                  rel=\"noopener noreferrer\" \r\n                  className=\"text-blue-500 hover:underline text-xs\"\r\n                >\r\n                  Xem logo\r\n                </a>\r\n              </div>\r\n            ) : (\r\n              <span className=\"text-xs text-muted-foreground\">Không có thông tin</span>\r\n            )}\r\n          </div>\r\n        </div>\r\n        \r\n        <div className=\"space-y-1\">\r\n          <div className=\"text-xs font-medium text-muted-foreground flex items-center gap-1\">\r\n            <ImageIcon className=\"h-3.5 w-3.5\" /> Icon\r\n          </div>\r\n          <div>\r\n            {bank.icon ? (\r\n              <div className=\"flex flex-col gap-1\">\r\n                <img \r\n                  src={bank.icon} \r\n                  alt=\"Icon\" \r\n                  className=\"max-w-[120px] max-h-[60px] object-contain border rounded p-1\" \r\n                />\r\n                <a \r\n                  href={bank.icon} \r\n                  target=\"_blank\" \r\n                  rel=\"noopener noreferrer\" \r\n                  className=\"text-blue-500 hover:underline text-xs\"\r\n                >\r\n                  Xem icon\r\n                </a>\r\n              </div>\r\n            ) : (\r\n              <span className=\"text-xs text-muted-foreground\">Không có thông tin</span>\r\n            )}\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </InfoCard>\r\n  );\r\n}\r\n"], "names": [], "mappings": "AAAA;;CAEC;;;;AAID;AACA;AAHA;;;;AAUO,SAAS,iBAAiB,EAAE,IAAI,EAAyB;IAC9D,qBACE,uVAAC,2HAAA,CAAA,WAAQ;QACP,OAAM;QACN,aAAY;QACZ,WAAU;kBAEV,cAAA,uVAAC;YAAI,WAAU;;8BACb,uVAAC;oBAAI,WAAU;;sCACb,uVAAC;4BAAI,WAAU;;8CACb,uVAAC,wRAAA,CAAA,QAAS;oCAAC,WAAU;;;;;;gCAAgB;;;;;;;sCAEvC,uVAAC;sCACE,KAAK,QAAQ,iBACZ,uVAAC;gCAAI,WAAU;;kDACb,uVAAC;wCACC,KAAK,KAAK,QAAQ;wCAClB,KAAI;wCACJ,WAAU;;;;;;kDAEZ,uVAAC;wCACC,MAAM,KAAK,QAAQ;wCACnB,QAAO;wCACP,KAAI;wCACJ,WAAU;kDACX;;;;;;;;;;;qDAKH,uVAAC;gCAAK,WAAU;0CAAgC;;;;;;;;;;;;;;;;;8BAKtD,uVAAC;oBAAI,WAAU;;sCACb,uVAAC;4BAAI,WAAU;;8CACb,uVAAC,wRAAA,CAAA,QAAS;oCAAC,WAAU;;;;;;gCAAgB;;;;;;;sCAEvC,uVAAC;sCACE,KAAK,IAAI,iBACR,uVAAC;gCAAI,WAAU;;kDACb,uVAAC;wCACC,KAAK,KAAK,IAAI;wCACd,KAAI;wCACJ,WAAU;;;;;;kDAEZ,uVAAC;wCACC,MAAM,KAAK,IAAI;wCACf,QAAO;wCACP,KAAI;wCACJ,WAAU;kDACX;;;;;;;;;;;qDAKH,uVAAC;gCAAK,WAAU;0CAAgC;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAO9D", "debugId": null}}, {"offset": {"line": 6348, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/bloomgoo/gold-exchange-platform/frontend/components/common/admin/ebanks/detail/bank-detail-system.tsx"], "sourcesContent": ["/**\r\n * BankDetailSystem - System info section cho detail sheet\r\n */\r\n\r\n'use client';\r\n\r\nimport { Calendar, Clock, User } from 'lucide-react';\r\nimport { InfoCard } from '@/components/info-card';\r\nimport { UserHoverCard } from '@/components/common/user/user-hover-card';\r\nimport type { BankDto } from '../types';\r\nimport { formatDate } from '../utils/bank-helpers';\r\n\r\ninterface BankDetailSystemProps {\r\n  bank: BankDto;\r\n}\r\n\r\nexport function BankDetailSystem({ bank }: BankDetailSystemProps) {\r\n  return (\r\n    <InfoCard\r\n      title=\"Thông tin hệ thống\"\r\n      description=\"Thông tin về ngân hàng trong hệ thống\"\r\n      className=\"py-4\"\r\n    >\r\n      <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\r\n        <div className=\"space-y-1\">\r\n          <div className=\"text-xs font-medium text-muted-foreground flex items-center gap-1\">\r\n            <Calendar className=\"h-3.5 w-3.5\" /> <PERSON><PERSON><PERSON> t<PERSON>\r\n          </div>\r\n          <div className=\"text-md\">{formatDate(bank.createdAt)}</div>\r\n        </div>\r\n        \r\n        <div className=\"space-y-1\">\r\n          <div className=\"text-xs font-medium text-muted-foreground flex items-center gap-1\">\r\n            <Clock className=\"h-3.5 w-3.5\" /> Cập nhật lần cuối\r\n          </div>\r\n          <div className=\"text-md\">{formatDate(bank.updatedAt)}</div>\r\n        </div>\r\n        \r\n        <div className=\"space-y-1\">\r\n          <div className=\"text-xs font-medium text-muted-foreground flex items-center gap-1\">\r\n            <User className=\"h-3.5 w-3.5\" /> Người tạo\r\n          </div>\r\n          <div>\r\n            {bank.creator ? (\r\n              <UserHoverCard user={bank.creator} showAvatar={true} size=\"sm\">\r\n                <div className=\"max-w-[120px] overflow-hidden\">\r\n                  <div className=\"text-md truncate\">\r\n                    {bank.creator.fullName || bank.creator.username || 'User'}\r\n                  </div>\r\n                </div>\r\n              </UserHoverCard>\r\n            ) : (\r\n              <span className=\"text-md\">Không có thông tin</span>\r\n            )}\r\n          </div>\r\n        </div>\r\n        \r\n        <div className=\"space-y-1\">\r\n          <div className=\"text-xs font-medium text-muted-foreground flex items-center gap-1\">\r\n            <User className=\"h-3.5 w-3.5\" /> Người cập nhật\r\n          </div>\r\n          <div>\r\n            {bank.updater ? (\r\n              <UserHoverCard user={bank.updater} showAvatar={true} size=\"sm\">\r\n                <div className=\"max-w-[120px] overflow-hidden\">\r\n                  <div className=\"text-md truncate\">\r\n                    {bank.updater.fullName || bank.updater.username || 'User'}\r\n                  </div>\r\n                </div>\r\n              </UserHoverCard>\r\n            ) : (\r\n              <span className=\"text-md\">Không có thông tin</span>\r\n            )}\r\n          </div>\r\n        </div>\r\n        \r\n        {bank.isDeleted && (\r\n          <>\r\n            <div className=\"space-y-1\">\r\n              <div className=\"text-xs font-medium text-muted-foreground flex items-center gap-1\">\r\n                <Calendar className=\"h-3.5 w-3.5\" /> Ngày xóa\r\n              </div>\r\n              <div className=\"text-md\">{formatDate(bank.deletedAt)}</div>\r\n            </div>\r\n            \r\n            <div className=\"space-y-1\">\r\n              <div className=\"text-xs font-medium text-muted-foreground flex items-center gap-1\">\r\n                <User className=\"h-3.5 w-3.5\" /> Người xóa\r\n              </div>\r\n              <div>\r\n                {bank.deleter ? (\r\n                  <UserHoverCard user={bank.deleter} showAvatar={true} size=\"sm\">\r\n                    <div className=\"max-w-[120px] overflow-hidden\">\r\n                      <div className=\"text-md truncate\">\r\n                        {bank.deleter.fullName || bank.deleter.username || 'User'}\r\n                      </div>\r\n                    </div>\r\n                  </UserHoverCard>\r\n                ) : (\r\n                  <span className=\"text-md\">Không có thông tin</span>\r\n                )}\r\n              </div>\r\n            </div>\r\n          </>\r\n        )}\r\n      </div>\r\n    </InfoCard>\r\n  );\r\n}\r\n"], "names": [], "mappings": "AAAA;;CAEC;;;;AAID;AAAA;AAAA;AACA;AACA;AAEA;AANA;;;;;;AAYO,SAAS,iBAAiB,EAAE,IAAI,EAAyB;IAC9D,qBACE,uVAAC,2HAAA,CAAA,WAAQ;QACP,OAAM;QACN,aAAY;QACZ,WAAU;kBAEV,cAAA,uVAAC;YAAI,WAAU;;8BACb,uVAAC;oBAAI,WAAU;;sCACb,uVAAC;4BAAI,WAAU;;8CACb,uVAAC,8RAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;gCAAgB;;;;;;;sCAEtC,uVAAC;4BAAI,WAAU;sCAAW,CAAA,GAAA,mKAAA,CAAA,aAAU,AAAD,EAAE,KAAK,SAAS;;;;;;;;;;;;8BAGrD,uVAAC;oBAAI,WAAU;;sCACb,uVAAC;4BAAI,WAAU;;8CACb,uVAAC,wRAAA,CAAA,QAAK;oCAAC,WAAU;;;;;;gCAAgB;;;;;;;sCAEnC,uVAAC;4BAAI,WAAU;sCAAW,CAAA,GAAA,mKAAA,CAAA,aAAU,AAAD,EAAE,KAAK,SAAS;;;;;;;;;;;;8BAGrD,uVAAC;oBAAI,WAAU;;sCACb,uVAAC;4BAAI,WAAU;;8CACb,uVAAC,sRAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;gCAAgB;;;;;;;sCAElC,uVAAC;sCACE,KAAK,OAAO,iBACX,uVAAC,sJAAA,CAAA,gBAAa;gCAAC,MAAM,KAAK,OAAO;gCAAE,YAAY;gCAAM,MAAK;0CACxD,cAAA,uVAAC;oCAAI,WAAU;8CACb,cAAA,uVAAC;wCAAI,WAAU;kDACZ,KAAK,OAAO,CAAC,QAAQ,IAAI,KAAK,OAAO,CAAC,QAAQ,IAAI;;;;;;;;;;;;;;;qDAKzD,uVAAC;gCAAK,WAAU;0CAAU;;;;;;;;;;;;;;;;;8BAKhC,uVAAC;oBAAI,WAAU;;sCACb,uVAAC;4BAAI,WAAU;;8CACb,uVAAC,sRAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;gCAAgB;;;;;;;sCAElC,uVAAC;sCACE,KAAK,OAAO,iBACX,uVAAC,sJAAA,CAAA,gBAAa;gCAAC,MAAM,KAAK,OAAO;gCAAE,YAAY;gCAAM,MAAK;0CACxD,cAAA,uVAAC;oCAAI,WAAU;8CACb,cAAA,uVAAC;wCAAI,WAAU;kDACZ,KAAK,OAAO,CAAC,QAAQ,IAAI,KAAK,OAAO,CAAC,QAAQ,IAAI;;;;;;;;;;;;;;;qDAKzD,uVAAC;gCAAK,WAAU;0CAAU;;;;;;;;;;;;;;;;;gBAK/B,KAAK,SAAS,kBACb;;sCACE,uVAAC;4BAAI,WAAU;;8CACb,uVAAC;oCAAI,WAAU;;sDACb,uVAAC,8RAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;wCAAgB;;;;;;;8CAEtC,uVAAC;oCAAI,WAAU;8CAAW,CAAA,GAAA,mKAAA,CAAA,aAAU,AAAD,EAAE,KAAK,SAAS;;;;;;;;;;;;sCAGrD,uVAAC;4BAAI,WAAU;;8CACb,uVAAC;oCAAI,WAAU;;sDACb,uVAAC,sRAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;wCAAgB;;;;;;;8CAElC,uVAAC;8CACE,KAAK,OAAO,iBACX,uVAAC,sJAAA,CAAA,gBAAa;wCAAC,MAAM,KAAK,OAAO;wCAAE,YAAY;wCAAM,MAAK;kDACxD,cAAA,uVAAC;4CAAI,WAAU;sDACb,cAAA,uVAAC;gDAAI,WAAU;0DACZ,KAAK,OAAO,CAAC,QAAQ,IAAI,KAAK,OAAO,CAAC,QAAQ,IAAI;;;;;;;;;;;;;;;6DAKzD,uVAAC;wCAAK,WAAU;kDAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAS5C", "debugId": null}}, {"offset": {"line": 6687, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/bloomgoo/gold-exchange-platform/frontend/components/common/admin/ebanks/detail/bank-detail-sheet.tsx"], "sourcesContent": ["/**\r\n * BankDetailSheet - Sheet wrapper cho detail view\r\n */\r\n\r\n'use client';\r\n\r\nimport { useState, useEffect } from 'react';\r\nimport { Loader2 } from 'lucide-react';\r\nimport { toast } from 'sonner';\r\nimport { api } from '@/lib/api';\r\nimport {\r\n  Sheet,\r\n  SheetContent,\r\n  SheetHeader,\r\n} from '@/components/ui/sheet';\r\nimport type { BankDto } from '../types';\r\nimport { BankDetailHeader } from './bank-detail-header';\r\nimport { BankDetailInfo } from './bank-detail-info';\r\nimport { BankDetailImages } from './bank-detail-images';\r\nimport { BankDetailSystem } from './bank-detail-system';\r\nimport { TOAST_MESSAGES } from '../utils/bank-constants';\r\nimport { mapBankData } from '../utils/bank-helpers';\r\n\r\ninterface BankDetailSheetProps {\r\n  bank: BankDto | null;\r\n  isOpen: boolean;\r\n  onClose: () => void;\r\n  onEdit: (bank: BankDto) => void;\r\n}\r\n\r\nexport function BankDetailSheet({ \r\n  bank: initialBank, \r\n  isOpen, \r\n  onClose, \r\n  onEdit \r\n}: BankDetailSheetProps) {\r\n  const [bank, setBank] = useState<BankDto | null>(initialBank);\r\n  const [loading, setLoading] = useState<boolean>(false);\r\n\r\n  // Gọi API để lấy thông tin chi tiết của ngân hàng khi mở sheet\r\n  useEffect(() => {\r\n    if (isOpen && initialBank?.id) {\r\n      setLoading(true);\r\n      api.get<BankDto>(`banks/${initialBank.id}`)\r\n        .then(response => {\r\n          setBank(mapBankData(response));\r\n          setLoading(false);\r\n        })\r\n        .catch(error => {\r\n          console.error('Error fetching bank details:', error);\r\n          toast.error(TOAST_MESSAGES.FETCH_DETAIL_ERROR);\r\n          setLoading(false);\r\n        });\r\n    }\r\n  }, [isOpen, initialBank?.id]);\r\n\r\n  if (!bank) return null;\r\n\r\n  return (\r\n    <Sheet open={isOpen} onOpenChange={(open) => !open && onClose()}>\r\n      <SheetContent className=\"w-full sm:max-w-xl md:max-w-2xl lg:max-w-3xl flex flex-col p-0\">\r\n        {/* Header cố định */}\r\n        <SheetHeader className=\"px-0 py-0 border-b\">\r\n          <BankDetailHeader \r\n            bank={bank} \r\n            loading={loading} \r\n            onEdit={onEdit} \r\n          />\r\n        </SheetHeader>\r\n\r\n        {/* Body có thể scroll */}\r\n        <div className=\"flex-1 overflow-y-auto px-4\">\r\n          {loading ? (\r\n            <div className=\"flex flex-col items-center justify-center h-full py-8 gap-4\">\r\n              <Loader2 className=\"h-8 w-8 animate-spin text-primary\" />\r\n              <p className=\"text-muted-foreground\">Đang tải thông tin chi tiết...</p>\r\n            </div>\r\n          ) : (\r\n            <>\r\n              {/* Thông tin cơ bản */}\r\n              <BankDetailInfo bank={bank} />\r\n\r\n              {/* Thông tin hình ảnh */}\r\n              <BankDetailImages bank={bank} />\r\n\r\n              {/* Thông tin hệ thống */}\r\n              <BankDetailSystem bank={bank} />\r\n            </>\r\n          )}\r\n        </div>\r\n      </SheetContent>\r\n    </Sheet>\r\n  );\r\n}\r\n"], "names": [], "mappings": "AAAA;;CAEC;;;;AAID;AACA;AACA;AACA;AACA;AAMA;AACA;AACA;AACA;AACA;AACA;AAjBA;;;;;;;;;;;;;AA0BO,SAAS,gBAAgB,EAC9B,MAAM,WAAW,EACjB,MAAM,EACN,OAAO,EACP,MAAM,EACe;IACrB,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,8SAAA,CAAA,WAAQ,AAAD,EAAkB;IACjD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,8SAAA,CAAA,WAAQ,AAAD,EAAW;IAEhD,+DAA+D;IAC/D,CAAA,GAAA,8SAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,UAAU,aAAa,IAAI;YAC7B,WAAW;YACX,0GAAA,CAAA,MAAG,CAAC,GAAG,CAAU,CAAC,MAAM,EAAE,YAAY,EAAE,EAAE,EACvC,IAAI,CAAC,CAAA;gBACJ,QAAQ,CAAA,GAAA,mKAAA,CAAA,cAAW,AAAD,EAAE;gBACpB,WAAW;YACb,GACC,KAAK,CAAC,CAAA;gBACL,QAAQ,KAAK,CAAC,gCAAgC;gBAC9C,wQAAA,CAAA,QAAK,CAAC,KAAK,CAAC,qKAAA,CAAA,iBAAc,CAAC,kBAAkB;gBAC7C,WAAW;YACb;QACJ;IACF,GAAG;QAAC;QAAQ,aAAa;KAAG;IAE5B,IAAI,CAAC,MAAM,OAAO;IAElB,qBACE,uVAAC,0HAAA,CAAA,QAAK;QAAC,MAAM;QAAQ,cAAc,CAAC,OAAS,CAAC,QAAQ;kBACpD,cAAA,uVAAC,0HAAA,CAAA,eAAY;YAAC,WAAU;;8BAEtB,uVAAC,0HAAA,CAAA,cAAW;oBAAC,WAAU;8BACrB,cAAA,uVAAC,8KAAA,CAAA,mBAAgB;wBACf,MAAM;wBACN,SAAS;wBACT,QAAQ;;;;;;;;;;;8BAKZ,uVAAC;oBAAI,WAAU;8BACZ,wBACC,uVAAC;wBAAI,WAAU;;0CACb,uVAAC,qSAAA,CAAA,UAAO;gCAAC,WAAU;;;;;;0CACnB,uVAAC;gCAAE,WAAU;0CAAwB;;;;;;;;;;;6CAGvC;;0CAEE,uVAAC,4KAAA,CAAA,iBAAc;gCAAC,MAAM;;;;;;0CAGtB,uVAAC,8KAAA,CAAA,mBAAgB;gCAAC,MAAM;;;;;;0CAGxB,uVAAC,8KAAA,CAAA,mBAAgB;gCAAC,MAAM;;;;;;;;;;;;;;;;;;;;;;;;AAOtC", "debugId": null}}, {"offset": {"line": 6833, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/bloomgoo/gold-exchange-platform/frontend/components/common/admin/ebanks/ebanks.tsx"], "sourcesContent": ["/**\r\n * EBanks - Component chính cho module ngân hàng\r\n * Orchestrates tất cả các sub-components và business logic\r\n */\r\n\r\n'use client';\r\n\r\nimport { useState, useCallback, useEffect, useMemo, useRef } from 'react';\r\nimport { toast } from 'sonner';\r\nimport { Checkbox } from '@/components/ui/checkbox';\r\nimport type { BankDto, BankFormMode, StatusFilter } from './types';\r\nimport { useBankData } from './hooks/use-bank-data';\r\nimport { useBankTable } from './hooks/use-bank-table';\r\nimport { useBankActions } from './hooks/use-bank-actions';\r\nimport { getBankTableColumns } from './table/bank-table-columns';\r\nimport { BankHeader } from './components/bank-header';\r\nimport { BankToolbar } from './components/bank-toolbar';\r\nimport { BankTable } from './components/bank-table';\r\nimport { FloatDeleteButton } from './components/float-delete-button';\r\nimport { BankFormModal } from './form/bank-form-modal';\r\nimport { BankDetailSheet } from './detail/bank-detail-sheet';\r\nimport { TOAST_MESSAGES } from './utils/bank-constants';\r\n\r\nexport default function EBanks() {\r\n  // UI States\r\n  const [selectedBank, setSelectedBank] = useState<BankDto | null>(null);\r\n  const [isShowSelectedRows, setIsShowSelectedRows] = useState(false);\r\n  const [statusFilter, setStatusFilter] = useState<StatusFilter>('all');\r\n  const [showBankFormModal, setShowBankFormModal] = useState(false);\r\n  const [showBankDetailSheet, setShowBankDetailSheet] = useState(false);\r\n  const [bankFormMode, setBankFormMode] = useState<BankFormMode>('create');\r\n  const [isRefreshing, setIsRefreshing] = useState(false);\r\n\r\n  // Event Handlers - định nghĩa trước để sử dụng trong hooks\r\n  const handleViewDetail = useCallback((bank: BankDto) => {\r\n    setSelectedBank(bank);\r\n    setShowBankDetailSheet(true);\r\n    toast.info(`Đang xem thông tin của ngân hàng ${bank.brandName}`);\r\n  }, []);\r\n\r\n  const handleEdit = useCallback((bank: BankDto) => {\r\n    setSelectedBank(bank);\r\n    setBankFormMode('update');\r\n    setShowBankFormModal(true);\r\n    toast.info(`Đang chỉnh sửa thông tin của ngân hàng ${bank.brandName}`);\r\n  }, []);\r\n\r\n  const handleDelete = useCallback(async (bank: BankDto) => {\r\n    // Will be implemented with actions hook\r\n  }, []);\r\n\r\n  const handleToggleStatus = useCallback(async (bank: BankDto) => {\r\n    // Will be implemented with actions hook\r\n  }, []);\r\n\r\n  const handleDuplicate = useCallback(async (bank: BankDto) => {\r\n    // Will be implemented with actions hook\r\n  }, []);\r\n\r\n  // Refs for functions that will be defined later\r\n  const fetchBanksRef = useRef<() => Promise<void>>();\r\n  const fetchStatisticsRef = useRef<(force?: boolean) => Promise<void>>();\r\n\r\n  // Actions hook\r\n  const {\r\n    isUpdating,\r\n    createBank,\r\n    updateBank,\r\n    deleteBank,\r\n    toggleBankStatus,\r\n    duplicateBank,\r\n    bulkDeleteBanks,\r\n    exportBanks,\r\n    importBanks,\r\n  } = useBankActions({\r\n    onSuccess: () => {\r\n      // Fetch lại data từ server để đảm bảo đồng bộ với một chút delay\r\n      setTimeout(() => {\r\n        fetchBanksRef.current?.();\r\n        fetchStatisticsRef.current?.(true);\r\n      }, 100);\r\n    },\r\n    updateLocalBank: () => {\r\n      // Không cần update local state, sẽ fetch lại từ server\r\n    },\r\n    removeLocalBank: () => {\r\n      // Không cần update local state, sẽ fetch lại từ server\r\n    },\r\n    addLocalBank: () => {\r\n      // Không cần update local state, sẽ fetch lại từ server\r\n    },\r\n  });\r\n\r\n  // Actual handlers with implementations\r\n  const actualHandleDelete = useCallback(async (bank: BankDto) => {\r\n    await deleteBank(bank);\r\n  }, [deleteBank]);\r\n\r\n  const actualHandleToggleStatus = useCallback(async (bank: BankDto) => {\r\n    await toggleBankStatus(bank);\r\n  }, [toggleBankStatus]);\r\n\r\n  const actualHandleDuplicate = useCallback(async (bank: BankDto) => {\r\n    await duplicateBank(bank);\r\n  }, [duplicateBank]);\r\n\r\n  // Memoized columns với selection column\r\n  const columns = useMemo(() => [\r\n    // Selection column\r\n    {\r\n      id: 'select',\r\n      size: 40,\r\n      header: ({ table }: any) => (\r\n        <div className=\"px-1\">\r\n          <Checkbox\r\n            checked={\r\n              table.getIsAllPageRowsSelected() ||\r\n              (table.getIsSomePageRowsSelected() && \"indeterminate\")\r\n            }\r\n            onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}\r\n            aria-label=\"Select all\"\r\n            className=\"translate-y-[2px]\"\r\n          />\r\n        </div>\r\n      ),\r\n      cell: ({ row }: any) => (\r\n        <div className=\"px-1\">\r\n          <Checkbox\r\n            checked={row.getIsSelected()}\r\n            onCheckedChange={(value) => row.toggleSelected(!!value)}\r\n            onClick={(e) => e.stopPropagation()}\r\n            aria-label=\"Select row\"\r\n            className=\"translate-y-[2px]\"\r\n          />\r\n        </div>\r\n      ),\r\n      enableSorting: false,\r\n      enableHiding: false,\r\n    },\r\n    // Bank columns\r\n    ...getBankTableColumns({\r\n      onViewDetail: handleViewDetail,\r\n      onEdit: handleEdit,\r\n      onDelete: actualHandleDelete,\r\n      onToggleStatus: actualHandleToggleStatus,\r\n      onDuplicate: actualHandleDuplicate,\r\n    }),\r\n  ], [handleViewDetail, handleEdit, actualHandleDelete, actualHandleToggleStatus, actualHandleDuplicate]);\r\n\r\n  // Data hook trước\r\n  const [banks, setBanks] = useState<BankDto[]>([]);\r\n  const [totalRows, setTotalRows] = useState(0);\r\n\r\n  // Table hook với columns và data\r\n  const {\r\n    table,\r\n    globalFilter,\r\n    setGlobalFilter,\r\n    getSelectedBanks,\r\n    getSelectedCount,\r\n    resetSelection,\r\n    resetFilters,\r\n    pagination,\r\n    sorting,\r\n  } = useBankTable({\r\n    banks,\r\n    totalRows,\r\n    columns,\r\n    onViewDetail: handleViewDetail,\r\n    onEdit: handleEdit,\r\n    onDelete: handleDelete,\r\n    onToggleStatus: handleToggleStatus,\r\n    onDuplicate: handleDuplicate,\r\n  });\r\n\r\n  // Data hook sử dụng table states và update local states\r\n  const {\r\n    banks: fetchedBanks,\r\n    totalRows: fetchedTotalRows,\r\n    statusCounts,\r\n    fetchBanks,\r\n    fetchStatistics,\r\n  } = useBankData({\r\n    pagination,\r\n    statusFilter,\r\n    globalFilter,\r\n    sorting,\r\n  });\r\n\r\n  // Set refs for actions hook\r\n  fetchBanksRef.current = fetchBanks;\r\n  fetchStatisticsRef.current = fetchStatistics;\r\n\r\n  // Sync fetched data với local states\r\n  useEffect(() => {\r\n    setBanks(fetchedBanks);\r\n    setTotalRows(fetchedTotalRows);\r\n  }, [fetchedBanks, fetchedTotalRows]);\r\n\r\n  const handleRefresh = useCallback(async () => {\r\n    setIsRefreshing(true);\r\n    try {\r\n      // Đặt lại các bộ lọc và sắp xếp\r\n      resetFilters();\r\n      setStatusFilter('all');\r\n\r\n      // Fetch dữ liệu mới và thống kê\r\n      await Promise.all([\r\n        fetchBanks(),\r\n        fetchStatistics(true)\r\n      ]);\r\n    } finally {\r\n      setTimeout(() => {\r\n        setIsRefreshing(false);\r\n      }, 1000);\r\n    }\r\n  }, [fetchBanks, fetchStatistics, resetFilters]);\r\n\r\n\r\n\r\n  function handleAddBank() {\r\n    setSelectedBank(null);\r\n    setBankFormMode('create');\r\n    setShowBankFormModal(true);\r\n  }\r\n\r\n  async function handleDeleteSelected() {\r\n    const selectedBanks = getSelectedBanks();\r\n    const success = await bulkDeleteBanks(selectedBanks);\r\n    if (success) {\r\n      resetSelection();\r\n    }\r\n  }\r\n\r\n  function handleShowSelectedRows() {\r\n    setIsShowSelectedRows(!isShowSelectedRows);\r\n  }\r\n\r\n  // Form submission handlers\r\n  async function handleFormSubmit(data: any): Promise<boolean> {\r\n    if (bankFormMode === 'create') {\r\n      return await createBank(data);\r\n    } else if (bankFormMode === 'update' && selectedBank?.id) {\r\n      return await updateBank(selectedBank.id, data);\r\n    }\r\n    return false;\r\n  }\r\n\r\n  function handleFormSuccess() {\r\n    setShowBankFormModal(false);\r\n    handleRefresh();\r\n  }\r\n\r\n  return (\r\n    <div className=\"w-full flex flex-col h-full\">\r\n      {/* Header Navigation */}\r\n      <BankHeader\r\n        totalRows={totalRows}\r\n        isUpdating={isUpdating}\r\n        onAddBank={handleAddBank}\r\n        onExport={exportBanks}\r\n        onImport={importBanks}\r\n      />\r\n\r\n      {/* Table Toolbar with Status Tabs */}\r\n      <BankToolbar\r\n        table={table}\r\n        globalFilter={globalFilter}\r\n        setGlobalFilter={setGlobalFilter}\r\n        onRefresh={handleRefresh}\r\n        isRefreshing={isRefreshing}\r\n        isShowSelectedRows={isShowSelectedRows}\r\n        onShowSelectedRows={handleShowSelectedRows}\r\n        statusFilter={statusFilter}\r\n        onStatusChange={setStatusFilter}\r\n        statusCounts={statusCounts}\r\n      />\r\n\r\n      {/* Data Table */}\r\n      <BankTable\r\n        table={table}\r\n        totalItems={totalRows}\r\n        isShowSelectedRows={isShowSelectedRows}\r\n        onShowSelectedRows={handleShowSelectedRows}\r\n      />\r\n\r\n      {/* Float Delete Button */}\r\n      <FloatDeleteButton\r\n        selectedCount={getSelectedCount()}\r\n        onDelete={handleDeleteSelected}\r\n      />\r\n\r\n      {/* Bank Form Modal */}\r\n      <BankFormModal\r\n        isOpen={showBankFormModal}\r\n        onClose={() => setShowBankFormModal(false)}\r\n        bank={selectedBank}\r\n        mode={bankFormMode}\r\n        onSubmit={handleFormSubmit}\r\n        onSuccess={handleFormSuccess}\r\n      />\r\n\r\n      {/* Bank Detail Sheet */}\r\n      <BankDetailSheet\r\n        isOpen={showBankDetailSheet}\r\n        onClose={() => setShowBankDetailSheet(false)}\r\n        bank={selectedBank}\r\n        onEdit={(bank) => {\r\n          setSelectedBank(bank);\r\n          setBankFormMode('update');\r\n          setShowBankFormModal(true);\r\n          setShowBankDetailSheet(false);\r\n        }}\r\n      />\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;AAID;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAfA;;;;;;;;;;;;;;;AAkBe,SAAS;IACtB,YAAY;IACZ,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,8SAAA,CAAA,WAAQ,AAAD,EAAkB;IACjE,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,8SAAA,CAAA,WAAQ,AAAD,EAAE;IAC7D,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,8SAAA,CAAA,WAAQ,AAAD,EAAgB;IAC/D,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,8SAAA,CAAA,WAAQ,AAAD,EAAE;IAC3D,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,8SAAA,CAAA,WAAQ,AAAD,EAAE;IAC/D,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,8SAAA,CAAA,WAAQ,AAAD,EAAgB;IAC/D,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,8SAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,2DAA2D;IAC3D,MAAM,mBAAmB,CAAA,GAAA,8SAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QACpC,gBAAgB;QAChB,uBAAuB;QACvB,wQAAA,CAAA,QAAK,CAAC,IAAI,CAAC,CAAC,iCAAiC,EAAE,KAAK,SAAS,EAAE;IACjE,GAAG,EAAE;IAEL,MAAM,aAAa,CAAA,GAAA,8SAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QAC9B,gBAAgB;QAChB,gBAAgB;QAChB,qBAAqB;QACrB,wQAAA,CAAA,QAAK,CAAC,IAAI,CAAC,CAAC,uCAAuC,EAAE,KAAK,SAAS,EAAE;IACvE,GAAG,EAAE;IAEL,MAAM,eAAe,CAAA,GAAA,8SAAA,CAAA,cAAW,AAAD,EAAE,OAAO;IACtC,wCAAwC;IAC1C,GAAG,EAAE;IAEL,MAAM,qBAAqB,CAAA,GAAA,8SAAA,CAAA,cAAW,AAAD,EAAE,OAAO;IAC5C,wCAAwC;IAC1C,GAAG,EAAE;IAEL,MAAM,kBAAkB,CAAA,GAAA,8SAAA,CAAA,cAAW,AAAD,EAAE,OAAO;IACzC,wCAAwC;IAC1C,GAAG,EAAE;IAEL,gDAAgD;IAChD,MAAM,gBAAgB,CAAA,GAAA,8SAAA,CAAA,SAAM,AAAD;IAC3B,MAAM,qBAAqB,CAAA,GAAA,8SAAA,CAAA,SAAM,AAAD;IAEhC,eAAe;IACf,MAAM,EACJ,UAAU,EACV,UAAU,EACV,UAAU,EACV,UAAU,EACV,gBAAgB,EAChB,aAAa,EACb,eAAe,EACf,WAAW,EACX,WAAW,EACZ,GAAG,CAAA,GAAA,0KAAA,CAAA,iBAAc,AAAD,EAAE;QACjB,WAAW;YACT,iEAAiE;YACjE,WAAW;gBACT,cAAc,OAAO;gBACrB,mBAAmB,OAAO,GAAG;YAC/B,GAAG;QACL;QACA,iBAAiB;QACf,uDAAuD;QACzD;QACA,iBAAiB;QACf,uDAAuD;QACzD;QACA,cAAc;QACZ,uDAAuD;QACzD;IACF;IAEA,uCAAuC;IACvC,MAAM,qBAAqB,CAAA,GAAA,8SAAA,CAAA,cAAW,AAAD,EAAE,OAAO;QAC5C,MAAM,WAAW;IACnB,GAAG;QAAC;KAAW;IAEf,MAAM,2BAA2B,CAAA,GAAA,8SAAA,CAAA,cAAW,AAAD,EAAE,OAAO;QAClD,MAAM,iBAAiB;IACzB,GAAG;QAAC;KAAiB;IAErB,MAAM,wBAAwB,CAAA,GAAA,8SAAA,CAAA,cAAW,AAAD,EAAE,OAAO;QAC/C,MAAM,cAAc;IACtB,GAAG;QAAC;KAAc;IAElB,wCAAwC;IACxC,MAAM,UAAU,CAAA,GAAA,8SAAA,CAAA,UAAO,AAAD,EAAE,IAAM;YAC5B,mBAAmB;YACnB;gBACE,IAAI;gBACJ,MAAM;gBACN,QAAQ,CAAC,EAAE,KAAK,EAAO,iBACrB,uVAAC;wBAAI,WAAU;kCACb,cAAA,uVAAC,6HAAA,CAAA,WAAQ;4BACP,SACE,MAAM,wBAAwB,MAC7B,MAAM,yBAAyB,MAAM;4BAExC,iBAAiB,CAAC,QAAU,MAAM,yBAAyB,CAAC,CAAC,CAAC;4BAC9D,cAAW;4BACX,WAAU;;;;;;;;;;;gBAIhB,MAAM,CAAC,EAAE,GAAG,EAAO,iBACjB,uVAAC;wBAAI,WAAU;kCACb,cAAA,uVAAC,6HAAA,CAAA,WAAQ;4BACP,SAAS,IAAI,aAAa;4BAC1B,iBAAiB,CAAC,QAAU,IAAI,cAAc,CAAC,CAAC,CAAC;4BACjD,SAAS,CAAC,IAAM,EAAE,eAAe;4BACjC,cAAW;4BACX,WAAU;;;;;;;;;;;gBAIhB,eAAe;gBACf,cAAc;YAChB;YACA,eAAe;eACZ,CAAA,GAAA,6KAAA,CAAA,sBAAmB,AAAD,EAAE;gBACrB,cAAc;gBACd,QAAQ;gBACR,UAAU;gBACV,gBAAgB;gBAChB,aAAa;YACf;SACD,EAAE;QAAC;QAAkB;QAAY;QAAoB;QAA0B;KAAsB;IAEtG,kBAAkB;IAClB,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,8SAAA,CAAA,WAAQ,AAAD,EAAa,EAAE;IAChD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,8SAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,iCAAiC;IACjC,MAAM,EACJ,KAAK,EACL,YAAY,EACZ,eAAe,EACf,gBAAgB,EAChB,gBAAgB,EAChB,cAAc,EACd,YAAY,EACZ,UAAU,EACV,OAAO,EACR,GAAG,CAAA,GAAA,wKAAA,CAAA,eAAY,AAAD,EAAE;QACf;QACA;QACA;QACA,cAAc;QACd,QAAQ;QACR,UAAU;QACV,gBAAgB;QAChB,aAAa;IACf;IAEA,wDAAwD;IACxD,MAAM,EACJ,OAAO,YAAY,EACnB,WAAW,gBAAgB,EAC3B,YAAY,EACZ,UAAU,EACV,eAAe,EAChB,GAAG,CAAA,GAAA,uKAAA,CAAA,cAAW,AAAD,EAAE;QACd;QACA;QACA;QACA;IACF;IAEA,4BAA4B;IAC5B,cAAc,OAAO,GAAG;IACxB,mBAAmB,OAAO,GAAG;IAE7B,qCAAqC;IACrC,CAAA,GAAA,8SAAA,CAAA,YAAS,AAAD,EAAE;QACR,SAAS;QACT,aAAa;IACf,GAAG;QAAC;QAAc;KAAiB;IAEnC,MAAM,gBAAgB,CAAA,GAAA,8SAAA,CAAA,cAAW,AAAD,EAAE;QAChC,gBAAgB;QAChB,IAAI;YACF,gCAAgC;YAChC;YACA,gBAAgB;YAEhB,gCAAgC;YAChC,MAAM,QAAQ,GAAG,CAAC;gBAChB;gBACA,gBAAgB;aACjB;QACH,SAAU;YACR,WAAW;gBACT,gBAAgB;YAClB,GAAG;QACL;IACF,GAAG;QAAC;QAAY;QAAiB;KAAa;IAI9C,SAAS;QACP,gBAAgB;QAChB,gBAAgB;QAChB,qBAAqB;IACvB;IAEA,eAAe;QACb,MAAM,gBAAgB;QACtB,MAAM,UAAU,MAAM,gBAAgB;QACtC,IAAI,SAAS;YACX;QACF;IACF;IAEA,SAAS;QACP,sBAAsB,CAAC;IACzB;IAEA,2BAA2B;IAC3B,eAAe,iBAAiB,IAAS;QACvC,IAAI,iBAAiB,UAAU;YAC7B,OAAO,MAAM,WAAW;QAC1B,OAAO,IAAI,iBAAiB,YAAY,cAAc,IAAI;YACxD,OAAO,MAAM,WAAW,aAAa,EAAE,EAAE;QAC3C;QACA,OAAO;IACT;IAEA,SAAS;QACP,qBAAqB;QACrB;IACF;IAEA,qBACE,uVAAC;QAAI,WAAU;;0BAEb,uVAAC,wKAAA,CAAA,aAAU;gBACT,WAAW;gBACX,YAAY;gBACZ,WAAW;gBACX,UAAU;gBACV,UAAU;;;;;;0BAIZ,uVAAC,yKAAA,CAAA,cAAW;gBACV,OAAO;gBACP,cAAc;gBACd,iBAAiB;gBACjB,WAAW;gBACX,cAAc;gBACd,oBAAoB;gBACpB,oBAAoB;gBACpB,cAAc;gBACd,gBAAgB;gBAChB,cAAc;;;;;;0BAIhB,uVAAC,uKAAA,CAAA,YAAS;gBACR,OAAO;gBACP,YAAY;gBACZ,oBAAoB;gBACpB,oBAAoB;;;;;;0BAItB,uVAAC,mLAAA,CAAA,oBAAiB;gBAChB,eAAe;gBACf,UAAU;;;;;;0BAIZ,uVAAC,yKAAA,CAAA,gBAAa;gBACZ,QAAQ;gBACR,SAAS,IAAM,qBAAqB;gBACpC,MAAM;gBACN,MAAM;gBACN,UAAU;gBACV,WAAW;;;;;;0BAIb,uVAAC,6KAAA,CAAA,kBAAe;gBACd,QAAQ;gBACR,SAAS,IAAM,uBAAuB;gBACtC,MAAM;gBACN,QAAQ,CAAC;oBACP,gBAAgB;oBAChB,gBAAgB;oBAChB,qBAAqB;oBACrB,uBAAuB;gBACzB;;;;;;;;;;;;AAIR", "debugId": null}}]}
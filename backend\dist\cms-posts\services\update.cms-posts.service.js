"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UpdateCmsPostsService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const event_emitter_1 = require("@nestjs/event-emitter");
const typeorm_transactional_1 = require("typeorm-transactional");
const base_cms_posts_service_1 = require("./base.cms-posts.service");
const slug_cms_posts_service_1 = require("./slug.cms-posts.service");
const cms_posts_entity_1 = require("../entity/cms-posts.entity");
const update_cms_post_dto_1 = require("../dto/update.cms-post.dto");
const cms_categories_entity_1 = require("../../cms-categories/entity/cms-categories.entity");
const user_entity_1 = require("../../users/entities/user.entity");
let UpdateCmsPostsService = class UpdateCmsPostsService extends base_cms_posts_service_1.BaseCmsPostsService {
    postRepository;
    categoryRepository;
    userRepository;
    slugService;
    dataSource;
    eventEmitter;
    constructor(postRepository, categoryRepository, userRepository, slugService, dataSource, eventEmitter) {
        super(postRepository, dataSource, eventEmitter);
        this.postRepository = postRepository;
        this.categoryRepository = categoryRepository;
        this.userRepository = userRepository;
        this.slugService = slugService;
        this.dataSource = dataSource;
        this.eventEmitter = eventEmitter;
    }
    async update(id, updateDto, userId) {
        try {
            this.logger.debug(`Đang cập nhật bài viết CMS với ID: ${id}`);
            const post = await this.findById(id, ['category', 'author']);
            if (!post) {
                throw new common_1.NotFoundException(`Không tìm thấy bài viết với ID: ${id}`);
            }
            const oldData = this.toDto(post);
            const oldStatus = post.status;
            if (updateDto.postType !== undefined) {
                post.postType = updateDto.postType;
            }
            if (updateDto.title !== undefined) {
                post.title = updateDto.title;
            }
            if (updateDto.slug !== undefined || updateDto.title !== undefined) {
                const newTitle = updateDto.title !== undefined ? updateDto.title : post.title;
                const newPostType = updateDto.postType !== undefined ? updateDto.postType : post.postType;
                const newSlug = await this.slugService.generateUniqueSlugForUpdate(newTitle, newPostType, id, updateDto.slug, post.slug);
                if (newSlug !== null) {
                    post.slug = newSlug;
                }
            }
            if (updateDto.excerpt !== undefined) {
                post.excerpt = updateDto.excerpt;
            }
            if (updateDto.content !== undefined) {
                post.content = updateDto.content;
            }
            if (updateDto.featuredImageUrl !== undefined) {
                post.featuredImageUrl = updateDto.featuredImageUrl;
            }
            if (updateDto.status !== undefined) {
                post.status = updateDto.status;
            }
            if (updateDto.publishedAt !== undefined) {
                post.publishedAt = updateDto.publishedAt ? new Date(updateDto.publishedAt) : null;
            }
            if (updateDto.eventStartDate !== undefined) {
                post.eventStartDate = updateDto.eventStartDate ? new Date(updateDto.eventStartDate) : null;
            }
            if (updateDto.eventEndDate !== undefined) {
                post.eventEndDate = updateDto.eventEndDate ? new Date(updateDto.eventEndDate) : null;
            }
            if (updateDto.eventLocation !== undefined) {
                post.eventLocation = updateDto.eventLocation;
            }
            if (updateDto.metaTitle !== undefined) {
                post.metaTitle = updateDto.metaTitle;
            }
            if (updateDto.metaDescription !== undefined) {
                post.metaDescription = updateDto.metaDescription;
            }
            if (updateDto.metaKeywords !== undefined) {
                post.metaKeywords = updateDto.metaKeywords;
            }
            if (updateDto.allowComments !== undefined) {
                post.allowComments = updateDto.allowComments;
            }
            if (updateDto.categoryId !== undefined) {
                if (updateDto.categoryId === null || updateDto.categoryId === '') {
                    post.category = null;
                }
                else if (updateDto.categoryId !== post.category?.id) {
                    const category = await this.categoryRepository.findOne({
                        where: { id: updateDto.categoryId, isDeleted: false },
                    });
                    if (!category) {
                        throw new common_1.NotFoundException(`Không tìm thấy chuyên mục với ID: ${updateDto.categoryId}`);
                    }
                    post.category = category;
                }
            }
            if (updateDto.authorId !== undefined && updateDto.authorId !== post.author.id) {
                const author = await this.userRepository.findOne({
                    where: { id: updateDto.authorId, isDeleted: false },
                });
                if (!author) {
                    throw new common_1.NotFoundException(`Không tìm thấy tác giả với ID: ${updateDto.authorId}`);
                }
                post.author = author;
            }
            if (updateDto.status === cms_posts_entity_1.CmsPostStatus.PUBLISHED &&
                oldStatus !== cms_posts_entity_1.CmsPostStatus.PUBLISHED &&
                !post.publishedAt) {
                post.publishedAt = new Date();
            }
            post.updatedBy = userId;
            const updatedPost = await this.postRepository.save(post);
            const postDto = this.toDto(updatedPost);
            if (!postDto) {
                throw new common_1.InternalServerErrorException('Không thể chuyển đổi entity thành DTO');
            }
            this.eventEmitter.emit(this.EVENT_POST_UPDATED, {
                postId: postDto.id,
                userId,
                oldData,
                newData: postDto,
            });
            if (updateDto.status === cms_posts_entity_1.CmsPostStatus.PUBLISHED && oldStatus !== cms_posts_entity_1.CmsPostStatus.PUBLISHED) {
                this.eventEmitter.emit(this.EVENT_POST_PUBLISHED, {
                    postId: postDto.id,
                    userId,
                    publishedAt: post.publishedAt,
                });
            }
            return postDto;
        }
        catch (error) {
            this.logger.error(`Lỗi khi cập nhật bài viết CMS: ${error.message}`, error.stack);
            if (error instanceof common_1.BadRequestException || error instanceof common_1.NotFoundException || error instanceof common_1.ConflictException) {
                throw error;
            }
            throw new common_1.InternalServerErrorException(`Không thể cập nhật bài viết CMS: ${error.message}`);
        }
    }
    async bulkUpdate(updates, userId) {
        try {
            this.logger.debug(`Đang cập nhật ${updates.length} bài viết CMS`);
            const updatedPosts = [];
            for (const update of updates) {
                const post = await this.update(update.id, update.data, userId);
                if (post) {
                    updatedPosts.push(post);
                }
            }
            return updatedPosts;
        }
        catch (error) {
            this.logger.error(`Lỗi khi cập nhật nhiều bài viết CMS: ${error.message}`, error.stack);
            throw new common_1.InternalServerErrorException(`Không thể cập nhật nhiều bài viết CMS: ${error.message}`);
        }
    }
    async updateStatus(id, status, userId) {
        try {
            this.logger.debug(`Đang cập nhật trạng thái bài viết CMS với ID: ${id} thành ${status}`);
            const updateDto = { status };
            return this.update(id, updateDto, userId);
        }
        catch (error) {
            this.logger.error(`Lỗi khi cập nhật trạng thái bài viết CMS: ${error.message}`, error.stack);
            if (error instanceof common_1.NotFoundException) {
                throw error;
            }
            throw new common_1.InternalServerErrorException(`Không thể cập nhật trạng thái bài viết CMS: ${error.message}`);
        }
    }
    async publish(id, userId) {
        try {
            this.logger.debug(`Đang xuất bản bài viết CMS với ID: ${id}`);
            const post = await this.findById(id, []);
            if (!post) {
                throw new common_1.NotFoundException(`Không tìm thấy bài viết với ID: ${id}`);
            }
            if (!this.canPublish(post)) {
                throw new common_1.BadRequestException('Bài viết chưa đủ điều kiện để xuất bản');
            }
            const updateDto = {
                status: cms_posts_entity_1.CmsPostStatus.PUBLISHED,
                publishedAt: new Date().toISOString(),
            };
            return this.update(id, updateDto, userId);
        }
        catch (error) {
            this.logger.error(`Lỗi khi xuất bản bài viết CMS: ${error.message}`, error.stack);
            if (error instanceof common_1.NotFoundException || error instanceof common_1.BadRequestException) {
                throw error;
            }
            throw new common_1.InternalServerErrorException(`Không thể xuất bản bài viết CMS: ${error.message}`);
        }
    }
    async unpublish(id, userId) {
        try {
            this.logger.debug(`Đang hủy xuất bản bài viết CMS với ID: ${id}`);
            const updateDto = {
                status: cms_posts_entity_1.CmsPostStatus.DRAFT,
            };
            return this.update(id, updateDto, userId);
        }
        catch (error) {
            this.logger.error(`Lỗi khi hủy xuất bản bài viết CMS: ${error.message}`, error.stack);
            if (error instanceof common_1.NotFoundException) {
                throw error;
            }
            throw new common_1.InternalServerErrorException(`Không thể hủy xuất bản bài viết CMS: ${error.message}`);
        }
    }
};
exports.UpdateCmsPostsService = UpdateCmsPostsService;
__decorate([
    (0, typeorm_transactional_1.Transactional)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, update_cms_post_dto_1.UpdateCmsPostDto, String]),
    __metadata("design:returntype", Promise)
], UpdateCmsPostsService.prototype, "update", null);
__decorate([
    (0, typeorm_transactional_1.Transactional)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Array, String]),
    __metadata("design:returntype", Promise)
], UpdateCmsPostsService.prototype, "bulkUpdate", null);
__decorate([
    (0, typeorm_transactional_1.Transactional)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String, String]),
    __metadata("design:returntype", Promise)
], UpdateCmsPostsService.prototype, "updateStatus", null);
__decorate([
    (0, typeorm_transactional_1.Transactional)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String]),
    __metadata("design:returntype", Promise)
], UpdateCmsPostsService.prototype, "publish", null);
__decorate([
    (0, typeorm_transactional_1.Transactional)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String]),
    __metadata("design:returntype", Promise)
], UpdateCmsPostsService.prototype, "unpublish", null);
exports.UpdateCmsPostsService = UpdateCmsPostsService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(cms_posts_entity_1.CmsPosts)),
    __param(1, (0, typeorm_1.InjectRepository)(cms_categories_entity_1.CmsCategories)),
    __param(2, (0, typeorm_1.InjectRepository)(user_entity_1.User)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository,
        slug_cms_posts_service_1.SlugCmsPostsService,
        typeorm_2.DataSource,
        event_emitter_1.EventEmitter2])
], UpdateCmsPostsService);
//# sourceMappingURL=update.cms-posts.service.js.map
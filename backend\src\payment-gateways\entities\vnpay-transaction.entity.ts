import { Entity, PrimaryGeneratedColumn, Column, CreateDateColumn, UpdateDateColumn, Index } from 'typeorm';

export enum VnpayTransactionStatus {
  PENDING = 'PENDING',
  SUCCESS = 'SUCCESS',
  FAILED = 'FAILED',
  CANCELLED = 'CANCELLED',
  EXPIRED = 'EXPIRED',
}

export enum VnpayTransactionType {
  PAYMENT = 'PAYMENT',
  REFUND = 'REFUND',
  QUERY = 'QUERY',
}

@Entity('vnpay_transactions')
@Index(['vnpayTxnRef'], { unique: true })
@Index(['merchantTxnRef'], { unique: true })
@Index(['status'])
@Index(['createdAt'])
export class VnpayTransaction {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  // Merchant transaction reference (generated by us)
  @Column({ name: 'merchant_txn_ref', unique: true })
  merchantTxnRef: string;

  // VNPAY transaction reference
  @Column({ name: 'vnpay_txn_ref', unique: true, nullable: true })
  vnpayTxnRef: string;

  // VNPAY transaction number (from VNPAY response)
  @Column({ name: 'vnpay_txn_no', nullable: true })
  vnpayTxnNo: string;

  // Transaction type
  @Column({
    type: 'enum',
    enum: VnpayTransactionType,
    default: VnpayTransactionType.PAYMENT,
  })
  type: VnpayTransactionType;

  // Transaction status
  @Column({
    type: 'enum',
    enum: VnpayTransactionStatus,
    default: VnpayTransactionStatus.PENDING,
  })
  status: VnpayTransactionStatus;

  // Amount in VND (stored as integer, no decimal)
  @Column({ type: 'bigint' })
  amount: number;

  // Currency (always VND for VNPAY)
  @Column({ default: 'VND' })
  currency: string;

  // Order info/description
  @Column({ name: 'order_info' })
  orderInfo: string;

  // Bank code used for payment
  @Column({ name: 'bank_code', nullable: true })
  bankCode: string;

  // Card type (ATM, QRCODE, etc.)
  @Column({ name: 'card_type', nullable: true })
  cardType: string;

  // VNPAY response code
  @Column({ name: 'vnpay_response_code', nullable: true })
  vnpayResponseCode: string;

  // VNPAY transaction status
  @Column({ name: 'vnpay_transaction_status', nullable: true })
  vnpayTransactionStatus: string;

  // Payment date from VNPAY (format: yyyyMMddHHmmss)
  @Column({ name: 'vnpay_pay_date', nullable: true })
  vnpayPayDate: string;

  // Client IP address
  @Column({ name: 'client_ip' })
  clientIp: string;

  // Locale (vn/en)
  @Column({ default: 'vn' })
  locale: string;

  // VNPAY raw request data (JSON)
  @Column({ name: 'vnpay_request', type: 'text', nullable: true })
  vnpayRequest: string;

  // VNPAY raw response data (JSON)
  @Column({ name: 'vnpay_response', type: 'text', nullable: true })
  vnpayResponse: string;

  // Return URL callback data (JSON)
  @Column({ name: 'return_callback_data', type: 'text', nullable: true })
  returnCallbackData: string;

  // IPN callback data (JSON)
  @Column({ name: 'ipn_callback_data', type: 'text', nullable: true })
  ipnCallbackData: string;

  // External reference (user ID, order ID, etc. from calling application)
  @Column({ name: 'external_ref', nullable: true })
  externalRef: string;

  // External metadata (JSON for additional data from calling application)
  @Column({ name: 'external_metadata', type: 'text', nullable: true })
  externalMetadata: string;

  // Error message if failed
  @Column({ name: 'error_message', nullable: true })
  errorMessage: string;

  // Retry count for failed transactions
  @Column({ name: 'retry_count', default: 0 })
  retryCount: number;

  // Expiry time for pending transactions
  @Column({ name: 'expires_at', nullable: true })
  expiresAt: Date;

  // Processed at (when transaction was completed/failed)
  @Column({ name: 'processed_at', nullable: true })
  processedAt: Date;

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;

  // Helper methods
  isSuccess(): boolean {
    return this.status === VnpayTransactionStatus.SUCCESS;
  }

  isPending(): boolean {
    return this.status === VnpayTransactionStatus.PENDING;
  }

  isFailed(): boolean {
    return this.status === VnpayTransactionStatus.FAILED;
  }

  isExpired(): boolean {
    return this.expiresAt && new Date() > this.expiresAt;
  }

  // Get formatted amount for display
  getFormattedAmount(): string {
    return this.amount.toLocaleString('vi-VN') + ' VND';
  }

  // Get VNPAY amount (multiply by 100)
  getVnpayAmount(): number {
    return this.amount * 100;
  }

  // Set VNPAY amount (divide by 100)
  setFromVnpayAmount(vnpayAmount: number): void {
    this.amount = vnpayAmount / 100;
  }
}

import {
  Column,
  CreateDateColumn,
  Entity,
  Index,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';

export enum VnpayTransactionStatus {
  PENDING = 'PENDING',
  SUCCESS = 'SUCCESS',
  FAILED = 'FAILED',
  CANCELLED = 'CANCELLED',
  EXPIRED = 'EXPIRED',
}

export enum VnpayTransactionType {
  PAYMENT = 'PAYMENT',
  REFUND = 'REFUND',
  QUERY = 'QUERY',
}

@Entity('vnpay_transactions')
@Index(['vnpayTxnRef'], { unique: true })
@Index(['merchantTxnRef'], { unique: true })
@Index(['status'])
@Index(['createdAt'])
export class VnpayTransaction {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ name: 'merchant_txn_ref', unique: true })
  merchantTxnRef: string;

  @Column({ name: 'vnpay_txn_ref', unique: true, nullable: true })
  vnpayTxnRef: string;

  @Column({ name: 'vnpay_txn_no', nullable: true })
  vnpayTxnNo: string;

  @Column({
    type: 'enum',
    enum: VnpayTransactionType,
    default: VnpayTransactionType.PAYMENT,
  })
  type: VnpayTransactionType;

  @Column({
    type: 'enum',
    enum: VnpayTransactionStatus,
    default: VnpayTransactionStatus.PENDING,
  })
  status: VnpayTransactionStatus;

  @Column({ type: 'bigint' })
  amount: number;

  @Column({ default: 'VND' })
  currency: string;

  @Column({ name: 'order_info' })
  orderInfo: string;

  @Column({ name: 'bank_code', nullable: true })
  bankCode: string;

  @Column({ name: 'card_type', nullable: true })
  cardType: string;

  @Column({ name: 'vnpay_response_code', nullable: true })
  vnpayResponseCode: string;

  @Column({ name: 'vnpay_transaction_status', nullable: true })
  vnpayTransactionStatus: string;

  @Column({ name: 'vnpay_pay_date', nullable: true })
  vnpayPayDate: string;

  @Column({ name: 'client_ip' })
  clientIp: string;

  @Column({ default: 'vn' })
  locale: string;

  @Column({ name: 'vnpay_request', type: 'text', nullable: true })
  vnpayRequest: string;

  @Column({ name: 'vnpay_response', type: 'text', nullable: true })
  vnpayResponse: string;

  @Column({ name: 'return_callback_data', type: 'text', nullable: true })
  returnCallbackData: string;

  @Column({ name: 'ipn_callback_data', type: 'text', nullable: true })
  ipnCallbackData: string;

  @Column({ name: 'external_ref', nullable: true })
  externalRef: string;

  @Column({ name: 'external_metadata', type: 'text', nullable: true })
  externalMetadata: string;

  @Column({ name: 'error_message', nullable: true })
  errorMessage: string;

  @Column({ name: 'retry_count', default: 0 })
  retryCount: number;

  @Column({ name: 'expires_at', nullable: true })
  expiresAt: Date;

  @Column({ name: 'processed_at', nullable: true })
  processedAt: Date;

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;
}

import {
  Column,
  CreateDateColumn,
  Entity,
  Index,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';

// Trạng thái giao dịch VNPAY
export enum VnpayTransactionStatus {
  PENDING = 'PENDING', // Đang chờ xử lý
  SUCCESS = 'SUCCESS', // Thành công
  FAILED = 'FAILED', // Thất bại
  CANCELLED = 'CANCELLED', // Đã hủy
  EXPIRED = 'EXPIRED', // Đã hết hạn
}

// Loại giao dịch VNPAY
export enum VnpayTransactionType {
  PAYMENT = 'PAYMENT', // Thanh toán
  REFUND = 'REFUND', // Hoàn tiền
  QUERY = 'QUERY', // Truy vấn
}

// Entity lưu trữ giao dịch VNPAY với mapping 1-1
@Entity('vnpay_transactions')
@Index(['vnpayTxnRef'], { unique: true }) // Index cho mã giao dịch VNPAY
@Index(['merchantTxnRef'], { unique: true }) // Index cho mã giao dịch merchant
@Index(['status']) // Index cho trạng thái
@Index(['createdAt']) // Index cho thời gian tạo
export class VnpayTransaction {
  @PrimaryGeneratedColumn('uuid')
  id: string; // ID chính của bản ghi

  @Column({ name: 'merchant_txn_ref', unique: true })
  merchantTxnRef: string; // Mã giao dịch do hệ thống tạo ra (vnp_TxnRef)

  @Column({ name: 'vnpay_txn_ref', unique: true, nullable: true })
  vnpayTxnRef: string; // Mã giao dịch VNPAY trả về

  @Column({ name: 'vnpay_txn_no', nullable: true })
  vnpayTxnNo: string; // Số giao dịch VNPAY (vnp_TransactionNo)

  @Column({
    type: 'enum',
    enum: VnpayTransactionType,
    default: VnpayTransactionType.PAYMENT,
  })
  type: VnpayTransactionType; // Loại giao dịch

  @Column({
    type: 'enum',
    enum: VnpayTransactionStatus,
    default: VnpayTransactionStatus.PENDING,
  })
  status: VnpayTransactionStatus; // Trạng thái giao dịch

  @Column({ type: 'bigint' })
  amount: number; // Số tiền (VND, không có phần thập phân)

  @Column({ default: 'VND' })
  currency: string; // Đơn vị tiền tệ (luôn là VND với VNPAY)

  @Column({ name: 'order_info' })
  orderInfo: string; // Thông tin đơn hàng

  @Column({ name: 'bank_code', nullable: true })
  bankCode: string; // Mã ngân hàng được sử dụng thanh toán

  @Column({ name: 'card_type', nullable: true })
  cardType: string; // Loại thẻ (ATM, QRCODE, v.v.)

  @Column({ name: 'vnpay_response_code', nullable: true })
  vnpayResponseCode: string; // Mã phản hồi từ VNPAY (00 = thành công)

  @Column({ name: 'vnpay_transaction_status', nullable: true })
  vnpayTransactionStatus: string; // Trạng thái giao dịch từ VNPAY

  @Column({ name: 'vnpay_pay_date', nullable: true })
  vnpayPayDate: string; // Thời gian thanh toán từ VNPAY (yyyyMMddHHmmss)

  @Column({ name: 'client_ip' })
  clientIp: string; // Địa chỉ IP của khách hàng

  @Column({ default: 'vn' })
  locale: string; // Ngôn ngữ hiển thị (vn/en)

  @Column({ name: 'vnpay_request', type: 'text', nullable: true })
  vnpayRequest: string; // Dữ liệu request gửi đến VNPAY (JSON)

  @Column({ name: 'vnpay_response', type: 'text', nullable: true })
  vnpayResponse: string; // Dữ liệu response từ VNPAY (JSON)

  @Column({ name: 'return_callback_data', type: 'text', nullable: true })
  returnCallbackData: string; // Dữ liệu callback từ Return URL (JSON)

  @Column({ name: 'ipn_callback_data', type: 'text', nullable: true })
  ipnCallbackData: string; // Dữ liệu callback từ IPN (JSON)

  @Column({ name: 'external_ref', nullable: true })
  externalRef: string; // Tham chiếu từ ứng dụng gọi (user ID, order ID, v.v.)

  @Column({ name: 'external_metadata', type: 'text', nullable: true })
  externalMetadata: string; // Metadata tùy chỉnh từ ứng dụng gọi (JSON)

  @Column({ name: 'error_message', nullable: true })
  errorMessage: string; // Thông báo lỗi nếu giao dịch thất bại

  @Column({ name: 'retry_count', default: 0 })
  retryCount: number; // Số lần thử lại giao dịch

  @Column({ name: 'expires_at', nullable: true })
  expiresAt: Date; // Thời gian hết hạn giao dịch

  @Column({ name: 'processed_at', nullable: true })
  processedAt: Date; // Thời gian xử lý hoàn tất giao dịch

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date; // Thời gian tạo bản ghi

  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date; // Thời gian cập nhật bản ghi
}

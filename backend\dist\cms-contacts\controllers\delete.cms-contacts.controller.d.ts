import { CmsContactDto } from '../dto/cms-contact.dto';
import { DeleteCmsContactsService } from '../services/delete.cms-contacts.service';
export declare class DeleteCmsContactsController {
    private readonly cmsContactsService;
    constructor(cmsContactsService: DeleteCmsContactsService);
    softDelete(id: string, userId: string): Promise<CmsContactDto | null>;
    restore(id: string, userId: string): Promise<CmsContactDto | null>;
    remove(id: string): Promise<{
        affected: number;
    }>;
    bulkSoftDelete(ids: string[], userId: string): Promise<CmsContactDto[]>;
    bulkRestore(ids: string[], userId: string): Promise<CmsContactDto[]>;
    bulkRemove(ids: string[]): Promise<{
        affected: number;
    }>;
}

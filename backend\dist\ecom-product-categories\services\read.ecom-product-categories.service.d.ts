import { Repository, DataSource } from 'typeorm';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { BaseEcomProductCategoriesService } from './base.ecom-product-categories.service';
import { EcomProductCategories } from '../entity/ecom-product-categories.entity';
import { EcomProductCategoryDto } from '../dto/ecom-product-category.dto';
import { CustomPaginationQueryDto } from '../../common/dto/custom-pagination-query.dto';
export declare class ReadEcomProductCategoriesService extends BaseEcomProductCategoriesService {
    protected readonly categoryRepository: Repository<EcomProductCategories>;
    protected readonly dataSource: DataSource;
    protected readonly eventEmitter: EventEmitter2;
    private shuffleArray;
    constructor(categoryRepository: Repository<EcomProductCategories>, dataSource: DataSource, eventEmitter: EventEmitter2);
    findAll(params: CustomPaginationQueryDto): Promise<{
        data: EcomProductCategoryDto[];
        total: number;
    }>;
    search(keyword: string, params: CustomPaginationQueryDto): Promise<{
        data: EcomProductCategoryDto[];
        total: number;
    }>;
    count(filter?: string): Promise<number>;
    findOne(id: string, relations?: string[]): Promise<EcomProductCategoryDto | null>;
    findOneOrFail(id: string, relations?: string[]): Promise<EcomProductCategoryDto | null>;
    findDeleted(params: CustomPaginationQueryDto): Promise<{
        data: EcomProductCategoryDto[];
        total: number;
    }>;
    getActiveProductCategories(params: CustomPaginationQueryDto): Promise<{
        data: EcomProductCategoryDto[];
        total: number;
    }>;
    getRandomizedActiveCategories(limit?: number): Promise<{
        data: EcomProductCategoryDto[];
        total: number;
    }>;
    getStatistics(): Promise<{
        total: number;
        activeCounts: {
            true: number;
            false: number;
        };
    }>;
}

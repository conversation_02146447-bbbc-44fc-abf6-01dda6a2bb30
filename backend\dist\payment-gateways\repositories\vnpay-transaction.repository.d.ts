import { Repository } from 'typeorm';
import { VnpayTransaction, VnpayTransactionStatus, VnpayTransactionType } from '../entities/vnpay-transaction.entity';
export interface VnpayTransactionFilter {
    status?: VnpayTransactionStatus;
    type?: VnpayTransactionType;
    externalRef?: string;
    vnpayTxnRef?: string;
    merchantTxnRef?: string;
    bankCode?: string;
    fromDate?: Date;
    toDate?: Date;
    minAmount?: number;
    maxAmount?: number;
}
export interface VnpayTransactionStats {
    totalTransactions: number;
    successfulTransactions: number;
    failedTransactions: number;
    pendingTransactions: number;
    totalAmount: number;
    successfulAmount: number;
    successRate: number;
}
export declare class VnpayTransactionRepository {
    private readonly repository;
    constructor(repository: Repository<VnpayTransaction>);
    create(data: Partial<VnpayTransaction>): Promise<VnpayTransaction>;
    findByMerchantRef(merchantTxnRef: string): Promise<VnpayTransaction | null>;
    findByVnpayRef(vnpayTxnRef: string): Promise<VnpayTransaction | null>;
    findById(id: string): Promise<VnpayTransaction | null>;
    update(id: string, data: Partial<VnpayTransaction>): Promise<VnpayTransaction>;
    updateStatus(id: string, status: VnpayTransactionStatus, additionalData?: Partial<VnpayTransaction>): Promise<VnpayTransaction>;
    findWithFilters(filter: VnpayTransactionFilter, page?: number, limit?: number): Promise<{
        transactions: VnpayTransaction[];
        total: number;
    }>;
    getStats(filter?: VnpayTransactionFilter): Promise<VnpayTransactionStats>;
}

import { Repository, DataSource } from 'typeorm';
import { EcomProduct } from '../entity/ecom-products.entity';
import { SlugEcomProductsService } from './slug.ecom-products.service';
export interface MigrationResult {
    success: boolean;
    message: string;
    data: {
        totalProcessed: number;
        totalUpdated: number;
        totalSkipped: number;
        errors: string[];
    };
}
export declare class EcomProductsMigrationService {
    private readonly ecomProductRepository;
    private readonly dataSource;
    private readonly slugService;
    private readonly logger;
    constructor(ecomProductRepository: Repository<EcomProduct>, dataSource: DataSource, slugService: SlugEcomProductsService);
    generateSlugsForExistingProducts(userId: string): Promise<MigrationResult>;
    getSlugStatistics(): Promise<{
        totalProducts: number;
        productsWithSlug: number;
        productsWithoutSlug: number;
        duplicateSlugs: string[];
        invalidSlugs: string[];
    }>;
    fixInvalidSlugs(userId: string): Promise<MigrationResult>;
    generateSlugsForProducts(productIds: string[], userId: string): Promise<MigrationResult>;
}

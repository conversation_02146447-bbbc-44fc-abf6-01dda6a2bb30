"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UpdateCmsContactsController = void 0;
const openapi = require("@nestjs/swagger");
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const jwt_auth_guard_1 = require("../../common/guards/jwt-auth.guard");
const update_cms_contacts_service_1 = require("../services/update.cms-contacts.service");
const read_cms_contacts_service_1 = require("../services/read.cms-contacts.service");
const update_cms_contact_dto_1 = require("../dto/update.cms-contact.dto");
const api_response_dto_1 = require("../../common/dto/api-response.dto");
const get_user_decorator_1 = require("../../common/decorators/get-user.decorator");
const roles_decorator_1 = require("../../common/decorators/roles.decorator");
const permissions_decorator_1 = require("../../common/decorators/permissions.decorator");
let UpdateCmsContactsController = class UpdateCmsContactsController {
    updateCmsContactsService;
    readCmsContactsService;
    constructor(updateCmsContactsService, readCmsContactsService) {
        this.updateCmsContactsService = updateCmsContactsService;
        this.readCmsContactsService = readCmsContactsService;
    }
    async update(id, updateCmsContactDto, userId) {
        const existingContact = await this.readCmsContactsService.findOne(id);
        if (!existingContact) {
            throw new common_1.NotFoundException(`Không tìm thấy liên hệ khách hàng với ID: ${id}`);
        }
        const result = await this.updateCmsContactsService.update(id, updateCmsContactDto, userId);
        if (!result) {
            throw new common_1.NotFoundException(`Không tìm thấy liên hệ khách hàng với ID: ${id}`);
        }
        return result;
    }
    async bulkUpdate(updates, userId) {
        return this.updateCmsContactsService.bulkUpdate(updates, userId);
    }
};
exports.UpdateCmsContactsController = UpdateCmsContactsController;
__decorate([
    (0, common_1.Put)(':id'),
    (0, roles_decorator_1.Roles)('ADMIN', 'EDITOR'),
    (0, permissions_decorator_1.Permissions)('cms-contact:update'),
    (0, swagger_1.ApiOperation)({ summary: 'Cập nhật thông tin liên hệ khách hàng' }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Liên hệ khách hàng đã được cập nhật thành công.',
        type: api_response_dto_1.ApiResponseDto,
        schema: {
            properties: {
                data: { $ref: '#/components/schemas/CmsContactDto' },
            },
        },
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.NOT_FOUND,
        description: 'Không tìm thấy liên hệ khách hàng.',
        type: api_response_dto_1.ApiResponseDto,
        schema: { properties: { data: { type: 'object', nullable: true } } },
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.BAD_REQUEST,
        description: 'Dữ liệu đầu vào không hợp lệ.',
        type: api_response_dto_1.ApiResponseDto,
        schema: { properties: { data: { type: 'object', nullable: true } } },
    }),
    (0, swagger_1.ApiParam)({ name: 'id', type: String, description: 'ID của liên hệ khách hàng' }),
    (0, swagger_1.ApiBody)({ type: update_cms_contact_dto_1.UpdateCmsContactDto }),
    openapi.ApiResponse({ status: 200, type: Object }),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __param(1, (0, common_1.Body)()),
    __param(2, (0, get_user_decorator_1.GetUser)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, update_cms_contact_dto_1.UpdateCmsContactDto, String]),
    __metadata("design:returntype", Promise)
], UpdateCmsContactsController.prototype, "update", null);
__decorate([
    (0, common_1.Patch)('bulk'),
    (0, roles_decorator_1.Roles)('ADMIN'),
    (0, permissions_decorator_1.Permissions)('cms-contact:update'),
    (0, swagger_1.ApiOperation)({ summary: 'Cập nhật nhiều liên hệ khách hàng cùng lúc' }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Các liên hệ khách hàng đã được cập nhật thành công.',
        type: api_response_dto_1.ApiResponseDto,
        schema: {
            properties: {
                data: {
                    type: 'array',
                    items: { $ref: '#/components/schemas/CmsContactDto' },
                },
            },
        },
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.BAD_REQUEST,
        description: 'Dữ liệu đầu vào không hợp lệ.',
        type: api_response_dto_1.ApiResponseDto,
        schema: { properties: { data: { type: 'object', nullable: true } } },
    }),
    (0, swagger_1.ApiBody)({
        schema: {
            type: 'array',
            items: {
                type: 'object',
                properties: {
                    id: { type: 'string', format: 'uuid' },
                    data: { $ref: '#/components/schemas/UpdateCmsContactDto' },
                },
                required: ['id', 'data'],
            },
        },
    }),
    openapi.ApiResponse({ status: 200, type: [require("../dto/cms-contact.dto").CmsContactDto] }),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, get_user_decorator_1.GetUser)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Array, String]),
    __metadata("design:returntype", Promise)
], UpdateCmsContactsController.prototype, "bulkUpdate", null);
exports.UpdateCmsContactsController = UpdateCmsContactsController = __decorate([
    (0, swagger_1.ApiTags)('cms-contacts'),
    (0, swagger_1.ApiBearerAuth)(),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, common_1.Controller)('cms/contacts'),
    __metadata("design:paramtypes", [update_cms_contacts_service_1.UpdateCmsContactsService,
        read_cms_contacts_service_1.ReadCmsContactsService])
], UpdateCmsContactsController);
//# sourceMappingURL=update.cms-contacts.controller.js.map
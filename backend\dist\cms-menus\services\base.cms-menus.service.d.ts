import { Logger } from '@nestjs/common';
import { Repository, DataSource } from 'typeorm';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { CmsMenus } from '../entity/cms-menus.entity';
import { CmsMenuDto } from '../dto/cms-menu.dto';
export declare class BaseCmsMenusService {
    protected readonly menuRepository: Repository<CmsMenus>;
    protected readonly dataSource: DataSource;
    protected readonly eventEmitter: EventEmitter2;
    protected readonly logger: Logger;
    protected readonly EVENT_MENU_CREATED = "cms-menu.created";
    protected readonly EVENT_MENU_UPDATED = "cms-menu.updated";
    protected readonly EVENT_MENU_DELETED = "cms-menu.deleted";
    protected readonly validRelations: string[];
    constructor(menuRepository: Repository<CmsMenus>, dataSource: DataSource, eventEmitter: EventEmitter2);
    protected toDto(menu: CmsMenus | null): CmsMenuDto | null;
    protected toDtos(menus: CmsMenus[]): CmsMenuDto[];
    protected findById(id: string, relations?: string[], throwError?: boolean): Promise<CmsMenus | null>;
    protected findBySlug(slug: string, postType: string, throwError?: boolean): Promise<CmsMenus | null>;
    protected findByName(name: string, postType: string, throwError?: boolean): Promise<CmsMenus | null>;
    protected validateRelations(relations: string[]): string[];
    protected generateSlugFromName(name: string): string;
    protected isSlugUnique(slug: string, postType: string, excludeId?: string): Promise<boolean>;
    protected isNameUnique(name: string, postType: string, excludeId?: string): Promise<boolean>;
}

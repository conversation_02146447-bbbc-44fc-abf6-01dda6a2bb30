{"name": "silver-exchange-platform-backend", "version": "0.0.1", "description": "", "author": "", "private": true, "license": "UNLICENSED", "scripts": {"build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "nest start", "start:dev": "nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "pm2 start dist/main.js --name silver-exchange-platform-backend", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "typecheck": "tsc --noEmit", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json", "typeorm": "typeorm-ts-node-commonjs", "migration:create": "typeorm-ts-node-commonjs migration:create", "migration:generate": "typeorm-ts-node-commonjs migration:generate -d src/config/typeorm.config.ts", "migration:run": "typeorm-ts-node-commonjs migration:run -d src/config/typeorm.config.ts", "migration:revert": "typeorm-ts-node-commonjs migration:revert -d src/config/typeorm.config.ts", "typeorm:prod": "typeorm -d dist/config/typeorm.config.js", "migration:run:prod": "npm run build && npm run typeorm:prod migration:run", "cli": "ts-node -r tsconfig-paths/register src/cli.ts"}, "dependencies": {"@nestjs-modules/mailer": "^2.0.2", "@nestjs/axios": "^4.0.0", "@nestjs/cache-manager": "^3.0.1", "@nestjs/common": "^11.0.1", "@nestjs/config": "^4.0.1", "@nestjs/core": "^11.0.1", "@nestjs/event-emitter": "^3.0.1", "@nestjs/jwt": "^10.2.0", "@nestjs/passport": "^10.0.3", "@nestjs/platform-express": "^11.0.1", "@nestjs/platform-socket.io": "^11.0.12", "@nestjs/schedule": "^4.1.2", "@nestjs/swagger": "^11.0.6", "@nestjs/terminus": "^11.0.0", "@nestjs/throttler": "^5.1.2", "@nestjs/typeorm": "^10.0.2", "@nestjs/websockets": "^11.0.12", "@types/multer": "^1.4.12", "@types/ws": "^8.18.1", "axios": "^1.10.0", "bcryptjs": "^2.4.3", "cache-manager": "^5.7.6", "cache-manager-redis-store": "^3.0.1", "class-transformer": "^0.5.1", "class-validator": "^0.14.1", "crypto": "^1.0.1", "csv-stringify": "^6.5.2", "date-fns": "^4.1.0", "decimal.js": "^10.5.0", "dotenv": "^16.4.7", "ethers": "^6.13.5", "exceljs": "^4.4.0", "firebase": "^11.7.3", "firebase-admin": "^13.4.0", "helmet": "^7.1.0", "joi": "^17.13.3", "madge": "^8.0.0", "minio": "^8.0.5", "moment": "^2.30.1", "multer": "1.4.5-lts.2", "nest-commander": "^3.17.0", "nestjs-i18n": "^10.5.1", "nestjs-pino": "^4.4.0", "passport": "^0.7.0", "passport-google-oauth20": "^2.0.0", "passport-jwt": "^4.0.1", "pg": "^8.12.0", "pino-http": "^10.4.0", "pino-pretty": "^13.0.0", "redis": "^4.7.0", "reflect-metadata": "^0.2.2", "rxjs": "^7.8.1", "slugify": "^1.6.6", "socket.io": "^4.8.1", "typeorm": "^0.3.20", "typeorm-transactional": "^0.5.0", "uuid": "^11.1.0", "ws": "^8.18.2"}, "devDependencies": {"@eslint/eslintrc": "^3.2.0", "@eslint/js": "^9.18.0", "@nestjs/cli": "^11.0.0", "@nestjs/schematics": "^11.0.0", "@nestjs/testing": "^11.0.1", "@swc/cli": "^0.6.0", "@swc/core": "^1.10.7", "@types/bcryptjs": "^2.4.6", "@types/cache-manager": "^4.0.6", "@types/express": "^4.17.21", "@types/jest": "^29.5.14", "@types/joi": "^17.2.3", "@types/node": "^22.10.7", "@types/passport-jwt": "^4.0.1", "@types/supertest": "^6.0.2", "@types/uuid": "^10.0.0", "eslint": "^9.18.0", "eslint-config-prettier": "^10.0.1", "eslint-plugin-prettier": "^5.2.2", "globals": "^16.0.0", "jest": "^29.7.0", "prettier": "^3.4.2", "source-map-support": "^0.5.21", "supertest": "^7.0.0", "ts-jest": "^29.2.5", "ts-node": "^10.9.2", "tsconfig-paths": "^4.2.0", "typescript": "^5.7.3", "typescript-eslint": "^8.20.0"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node"}}
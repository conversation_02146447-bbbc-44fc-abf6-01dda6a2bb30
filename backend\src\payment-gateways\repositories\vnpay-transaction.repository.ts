import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Between, FindOptionsWhere, Repository } from 'typeorm';
import {
  VnpayTransaction,
  VnpayTransactionStatus,
  VnpayTransactionType,
} from '../entities/vnpay-transaction.entity';

// Interface cho bộ lọc giao dịch VNPAY
export interface VnpayTransactionFilter {
  status?: VnpayTransactionStatus; // Lọc theo trạng thái
  type?: VnpayTransactionType; // Lọc theo loại giao dịch
  externalRef?: string; // Lọc theo tham chiếu ngoài
  vnpayTxnRef?: string; // Lọc theo mã giao dịch VNPAY
  merchantTxnRef?: string; // Lọc theo mã giao dịch merchant
  bankCode?: string; // Lọc theo mã ngân hàng
  fromDate?: Date; // Lọ<PERSON> từ ngày
  toDate?: Date; // Lọc đến ngày
  minAmount?: number; // Lọc số tiền tối thiểu
  maxAmount?: number; // Lọc số tiền tối đa
}

// Interface cho thống kê giao dịch VNPAY
export interface VnpayTransactionStats {
  totalTransactions: number; // Tổng số giao dịch
  successfulTransactions: number; // Số giao dịch thành công
  failedTransactions: number; // Số giao dịch thất bại
  pendingTransactions: number; // Số giao dịch đang chờ
  totalAmount: number; // Tổng số tiền
  successfulAmount: number; // Tổng số tiền thành công
  successRate: number; // Tỷ lệ thành công (%)
}

// Repository xử lý dữ liệu giao dịch VNPAY
@Injectable()
export class VnpayTransactionRepository {
  constructor(
    @InjectRepository(VnpayTransaction)
    private readonly repository: Repository<VnpayTransaction>,
  ) {}

  // Tạo giao dịch mới
  async create(data: Partial<VnpayTransaction>): Promise<VnpayTransaction> {
    const transaction = this.repository.create(data);
    return await this.repository.save(transaction);
  }

  // Tìm giao dịch theo mã merchant
  async findByMerchantRef(
    merchantTxnRef: string,
  ): Promise<VnpayTransaction | null> {
    return await this.repository.findOne({
      where: { merchantTxnRef },
    });
  }

  // Tìm giao dịch theo mã VNPAY
  async findByVnpayRef(vnpayTxnRef: string): Promise<VnpayTransaction | null> {
    return await this.repository.findOne({
      where: { vnpayTxnRef },
    });
  }

  // Tìm giao dịch theo ID
  async findById(id: string): Promise<VnpayTransaction | null> {
    return await this.repository.findOne({
      where: { id },
    });
  }

  // Cập nhật giao dịch
  async update(
    id: string,
    data: Partial<VnpayTransaction>,
  ): Promise<VnpayTransaction | null> {
    await this.repository.update(id, data);
    return await this.findById(id);
  }

  // Cập nhật trạng thái giao dịch
  async updateStatus(
    id: string,
    status: VnpayTransactionStatus,
    additionalData?: Partial<VnpayTransaction>,
  ): Promise<VnpayTransaction | null> {
    const updateData: Partial<VnpayTransaction> = {
      status,
      processedAt: new Date(), // Cập nhật thời gian xử lý
      ...additionalData,
    };

    await this.repository.update(id, updateData);
    return await this.findById(id);
  }

  // Tìm giao dịch với bộ lọc và phân trang
  async findWithFilters(
    filter: VnpayTransactionFilter,
    page: number = 1,
    limit: number = 20,
  ): Promise<{ transactions: VnpayTransaction[]; total: number }> {
    const where: FindOptionsWhere<VnpayTransaction> = {};

    // Áp dụng các bộ lọc cơ bản
    if (filter.status) where.status = filter.status;
    if (filter.type) where.type = filter.type;
    if (filter.externalRef) where.externalRef = filter.externalRef;
    if (filter.vnpayTxnRef) where.vnpayTxnRef = filter.vnpayTxnRef;
    if (filter.merchantTxnRef) where.merchantTxnRef = filter.merchantTxnRef;
    if (filter.bankCode) where.bankCode = filter.bankCode;

    // Bộ lọc theo khoảng thời gian
    if (filter.fromDate || filter.toDate) {
      where.createdAt = Between(
        filter.fromDate || new Date('1970-01-01'),
        filter.toDate || new Date(),
      );
    }

    const queryBuilder = this.repository
      .createQueryBuilder('transaction')
      .where(where);

    // Bộ lọc theo khoảng số tiền
    if (filter.minAmount !== undefined) {
      queryBuilder.andWhere('transaction.amount >= :minAmount', {
        minAmount: filter.minAmount,
      });
    }
    if (filter.maxAmount !== undefined) {
      queryBuilder.andWhere('transaction.amount <= :maxAmount', {
        maxAmount: filter.maxAmount,
      });
    }

    // Phân trang và sắp xếp
    const offset = (page - 1) * limit;
    queryBuilder
      .orderBy('transaction.createdAt', 'DESC') // Sắp xếp theo thời gian tạo mới nhất
      .skip(offset)
      .take(limit);

    const [transactions, total] = await queryBuilder.getManyAndCount();

    return { transactions, total };
  }

  // Lấy thống kê giao dịch
  async getStats(
    filter?: VnpayTransactionFilter,
  ): Promise<VnpayTransactionStats> {
    const queryBuilder = this.repository.createQueryBuilder('transaction');

    // Áp dụng bộ lọc nếu có
    if (filter) {
      if (filter.fromDate || filter.toDate) {
        queryBuilder.where(
          'transaction.createdAt BETWEEN :fromDate AND :toDate',
          {
            fromDate: filter.fromDate || new Date('1970-01-01'),
            toDate: filter.toDate || new Date(),
          },
        );
      }
      if (filter.externalRef) {
        queryBuilder.andWhere('transaction.externalRef = :externalRef', {
          externalRef: filter.externalRef,
        });
      }
    }

    // Thực hiện các truy vấn thống kê song song
    const [
      totalResult,
      successResult,
      failedResult,
      pendingResult,
      totalAmountResult,
      successAmountResult,
    ] = await Promise.all([
      // Tổng số giao dịch
      queryBuilder.getCount(),
      // Số giao dịch thành công
      queryBuilder
        .clone()
        .andWhere('transaction.status = :status', {
          status: VnpayTransactionStatus.SUCCESS,
        })
        .getCount(),
      // Số giao dịch thất bại
      queryBuilder
        .clone()
        .andWhere('transaction.status = :status', {
          status: VnpayTransactionStatus.FAILED,
        })
        .getCount(),
      // Số giao dịch đang chờ
      queryBuilder
        .clone()
        .andWhere('transaction.status = :status', {
          status: VnpayTransactionStatus.PENDING,
        })
        .getCount(),
      // Tổng số tiền
      queryBuilder
        .clone()
        .select('SUM(transaction.amount)', 'total')
        .getRawOne(),
      // Tổng số tiền thành công
      queryBuilder
        .clone()
        .select('SUM(transaction.amount)', 'total')
        .andWhere('transaction.status = :status', {
          status: VnpayTransactionStatus.SUCCESS,
        })
        .getRawOne(),
    ]);

    // Tính toán kết quả
    const totalAmount = parseInt(totalAmountResult?.total || '0');
    const successfulAmount = parseInt(successAmountResult?.total || '0');
    const successRate =
      totalResult > 0 ? (successResult / totalResult) * 100 : 0;

    return {
      totalTransactions: totalResult,
      successfulTransactions: successResult,
      failedTransactions: failedResult,
      pendingTransactions: pendingResult,
      totalAmount,
      successfulAmount,
      successRate: Math.round(successRate * 100) / 100, // Làm tròn 2 chữ số thập phân
    };
  }
}

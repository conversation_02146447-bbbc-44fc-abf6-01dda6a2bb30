import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, FindOptionsWhere, Between } from 'typeorm';
import { VnpayTransaction, VnpayTransactionStatus, VnpayTransactionType } from '../entities/vnpay-transaction.entity';

export interface VnpayTransactionFilter {
  status?: VnpayTransactionStatus;
  type?: VnpayTransactionType;
  externalRef?: string;
  vnpayTxnRef?: string;
  merchantTxnRef?: string;
  bankCode?: string;
  fromDate?: Date;
  toDate?: Date;
  minAmount?: number;
  maxAmount?: number;
}

export interface VnpayTransactionStats {
  totalTransactions: number;
  successfulTransactions: number;
  failedTransactions: number;
  pendingTransactions: number;
  totalAmount: number;
  successfulAmount: number;
  successRate: number;
}

@Injectable()
export class VnpayTransactionRepository {
  constructor(
    @InjectRepository(VnpayTransaction)
    private readonly repository: Repository<VnpayTransaction>,
  ) {}

  /**
   * Create new VNPAY transaction
   */
  async create(data: Partial<VnpayTransaction>): Promise<VnpayTransaction> {
    const transaction = this.repository.create(data);
    return await this.repository.save(transaction);
  }

  /**
   * Find transaction by merchant reference
   */
  async findByMerchantRef(merchantTxnRef: string): Promise<VnpayTransaction | null> {
    return await this.repository.findOne({
      where: { merchantTxnRef },
    });
  }

  /**
   * Find transaction by VNPAY reference
   */
  async findByVnpayRef(vnpayTxnRef: string): Promise<VnpayTransaction | null> {
    return await this.repository.findOne({
      where: { vnpayTxnRef },
    });
  }

  /**
   * Find transaction by ID
   */
  async findById(id: string): Promise<VnpayTransaction | null> {
    return await this.repository.findOne({
      where: { id },
    });
  }

  /**
   * Update transaction
   */
  async update(id: string, data: Partial<VnpayTransaction>): Promise<VnpayTransaction> {
    await this.repository.update(id, data);
    return await this.findById(id);
  }

  /**
   * Update transaction status
   */
  async updateStatus(
    id: string, 
    status: VnpayTransactionStatus, 
    additionalData?: Partial<VnpayTransaction>
  ): Promise<VnpayTransaction> {
    const updateData: Partial<VnpayTransaction> = {
      status,
      processedAt: new Date(),
      ...additionalData,
    };

    await this.repository.update(id, updateData);
    return await this.findById(id);
  }

  /**
   * Find transactions with filters
   */
  async findWithFilters(
    filter: VnpayTransactionFilter,
    page: number = 1,
    limit: number = 20,
  ): Promise<{ transactions: VnpayTransaction[]; total: number }> {
    const where: FindOptionsWhere<VnpayTransaction> = {};

    // Apply filters
    if (filter.status) where.status = filter.status;
    if (filter.type) where.type = filter.type;
    if (filter.externalRef) where.externalRef = filter.externalRef;
    if (filter.vnpayTxnRef) where.vnpayTxnRef = filter.vnpayTxnRef;
    if (filter.merchantTxnRef) where.merchantTxnRef = filter.merchantTxnRef;
    if (filter.bankCode) where.bankCode = filter.bankCode;

    // Date range filter
    if (filter.fromDate || filter.toDate) {
      where.createdAt = Between(
        filter.fromDate || new Date('1970-01-01'),
        filter.toDate || new Date(),
      );
    }

    const queryBuilder = this.repository.createQueryBuilder('transaction')
      .where(where);

    // Amount range filter
    if (filter.minAmount !== undefined) {
      queryBuilder.andWhere('transaction.amount >= :minAmount', { minAmount: filter.minAmount });
    }
    if (filter.maxAmount !== undefined) {
      queryBuilder.andWhere('transaction.amount <= :maxAmount', { maxAmount: filter.maxAmount });
    }

    // Pagination
    const offset = (page - 1) * limit;
    queryBuilder
      .orderBy('transaction.createdAt', 'DESC')
      .skip(offset)
      .take(limit);

    const [transactions, total] = await queryBuilder.getManyAndCount();

    return { transactions, total };
  }

  /**
   * Get transaction statistics
   */
  async getStats(filter?: VnpayTransactionFilter): Promise<VnpayTransactionStats> {
    const queryBuilder = this.repository.createQueryBuilder('transaction');

    // Apply filters if provided
    if (filter) {
      if (filter.fromDate || filter.toDate) {
        queryBuilder.where('transaction.createdAt BETWEEN :fromDate AND :toDate', {
          fromDate: filter.fromDate || new Date('1970-01-01'),
          toDate: filter.toDate || new Date(),
        });
      }
      if (filter.externalRef) {
        queryBuilder.andWhere('transaction.externalRef = :externalRef', { 
          externalRef: filter.externalRef 
        });
      }
    }

    const [
      totalResult,
      successResult,
      failedResult,
      pendingResult,
      totalAmountResult,
      successAmountResult,
    ] = await Promise.all([
      // Total transactions
      queryBuilder.getCount(),
      
      // Successful transactions
      queryBuilder.clone().andWhere('transaction.status = :status', { 
        status: VnpayTransactionStatus.SUCCESS 
      }).getCount(),
      
      // Failed transactions
      queryBuilder.clone().andWhere('transaction.status = :status', { 
        status: VnpayTransactionStatus.FAILED 
      }).getCount(),
      
      // Pending transactions
      queryBuilder.clone().andWhere('transaction.status = :status', { 
        status: VnpayTransactionStatus.PENDING 
      }).getCount(),
      
      // Total amount
      queryBuilder.clone()
        .select('SUM(transaction.amount)', 'total')
        .getRawOne(),
      
      // Successful amount
      queryBuilder.clone()
        .select('SUM(transaction.amount)', 'total')
        .andWhere('transaction.status = :status', { 
          status: VnpayTransactionStatus.SUCCESS 
        })
        .getRawOne(),
    ]);

    const totalAmount = parseInt(totalAmountResult?.total || '0');
    const successfulAmount = parseInt(successAmountResult?.total || '0');
    const successRate = totalResult > 0 ? (successResult / totalResult) * 100 : 0;

    return {
      totalTransactions: totalResult,
      successfulTransactions: successResult,
      failedTransactions: failedResult,
      pendingTransactions: pendingResult,
      totalAmount,
      successfulAmount,
      successRate: Math.round(successRate * 100) / 100, // Round to 2 decimal places
    };
  }

  /**
   * Find expired pending transactions
   */
  async findExpiredPending(): Promise<VnpayTransaction[]> {
    return await this.repository.find({
      where: {
        status: VnpayTransactionStatus.PENDING,
        expiresAt: Between(new Date('1970-01-01'), new Date()),
      },
    });
  }

  /**
   * Mark expired transactions as expired
   */
  async markExpiredTransactions(): Promise<number> {
    const expiredTransactions = await this.findExpiredPending();
    
    if (expiredTransactions.length === 0) {
      return 0;
    }

    const expiredIds = expiredTransactions.map(t => t.id);
    
    await this.repository.update(expiredIds, {
      status: VnpayTransactionStatus.EXPIRED,
      processedAt: new Date(),
      errorMessage: 'Transaction expired',
    });

    return expiredTransactions.length;
  }

  /**
   * Get daily transaction summary
   */
  async getDailySummary(date: Date): Promise<VnpayTransactionStats> {
    const startOfDay = new Date(date);
    startOfDay.setHours(0, 0, 0, 0);
    
    const endOfDay = new Date(date);
    endOfDay.setHours(23, 59, 59, 999);

    return await this.getStats({
      fromDate: startOfDay,
      toDate: endOfDay,
    });
  }

  /**
   * Soft delete transaction (for compliance)
   */
  async softDelete(id: string): Promise<void> {
    await this.repository.softDelete(id);
  }

  /**
   * Get transactions for reconciliation
   */
  async getForReconciliation(date: Date): Promise<VnpayTransaction[]> {
    const startOfDay = new Date(date);
    startOfDay.setHours(0, 0, 0, 0);
    
    const endOfDay = new Date(date);
    endOfDay.setHours(23, 59, 59, 999);

    return await this.repository.find({
      where: {
        status: VnpayTransactionStatus.SUCCESS,
        processedAt: Between(startOfDay, endOfDay),
      },
      order: {
        processedAt: 'ASC',
      },
    });
  }
}

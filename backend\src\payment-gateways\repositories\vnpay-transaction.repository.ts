import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Between, FindOptionsWhere, Repository } from 'typeorm';
import {
  VnpayTransaction,
  VnpayTransactionStatus,
  VnpayTransactionType,
} from '../entities/vnpay-transaction.entity';

export interface VnpayTransactionFilter {
  status?: VnpayTransactionStatus;
  type?: VnpayTransactionType;
  externalRef?: string;
  vnpayTxnRef?: string;
  merchantTxnRef?: string;
  bankCode?: string;
  fromDate?: Date;
  toDate?: Date;
  minAmount?: number;
  maxAmount?: number;
}

export interface VnpayTransactionStats {
  totalTransactions: number;
  successfulTransactions: number;
  failedTransactions: number;
  pendingTransactions: number;
  totalAmount: number;
  successfulAmount: number;
  successRate: number;
}

@Injectable()
export class VnpayTransactionRepository {
  constructor(
    @InjectRepository(VnpayTransaction)
    private readonly repository: Repository<VnpayTransaction>,
  ) {}

  async create(data: Partial<VnpayTransaction>): Promise<VnpayTransaction> {
    const transaction = this.repository.create(data);
    return await this.repository.save(transaction);
  }

  async findByMerchantRef(
    merchantTxnRef: string,
  ): Promise<VnpayTransaction | null> {
    return await this.repository.findOne({
      where: { merchantTxnRef },
    });
  }

  async findByVnpayRef(vnpayTxnRef: string): Promise<VnpayTransaction | null> {
    return await this.repository.findOne({
      where: { vnpayTxnRef },
    });
  }

  async findById(id: string): Promise<VnpayTransaction | null> {
    return await this.repository.findOne({
      where: { id },
    });
  }

  async update(
    id: string,
    data: Partial<VnpayTransaction>,
  ): Promise<VnpayTransaction> {
    await this.repository.update(id, data);
    return await this.findById(id);
  }

  async updateStatus(
    id: string,
    status: VnpayTransactionStatus,
    additionalData?: Partial<VnpayTransaction>,
  ): Promise<VnpayTransaction> {
    const updateData: Partial<VnpayTransaction> = {
      status,
      processedAt: new Date(),
      ...additionalData,
    };

    await this.repository.update(id, updateData);
    return await this.findById(id);
  }

  async findWithFilters(
    filter: VnpayTransactionFilter,
    page: number = 1,
    limit: number = 20,
  ): Promise<{ transactions: VnpayTransaction[]; total: number }> {
    const where: FindOptionsWhere<VnpayTransaction> = {};

    if (filter.status) where.status = filter.status;
    if (filter.type) where.type = filter.type;
    if (filter.externalRef) where.externalRef = filter.externalRef;
    if (filter.vnpayTxnRef) where.vnpayTxnRef = filter.vnpayTxnRef;
    if (filter.merchantTxnRef) where.merchantTxnRef = filter.merchantTxnRef;
    if (filter.bankCode) where.bankCode = filter.bankCode;

    if (filter.fromDate || filter.toDate) {
      where.createdAt = Between(
        filter.fromDate || new Date('1970-01-01'),
        filter.toDate || new Date(),
      );
    }

    const queryBuilder = this.repository
      .createQueryBuilder('transaction')
      .where(where);

    if (filter.minAmount !== undefined) {
      queryBuilder.andWhere('transaction.amount >= :minAmount', {
        minAmount: filter.minAmount,
      });
    }
    if (filter.maxAmount !== undefined) {
      queryBuilder.andWhere('transaction.amount <= :maxAmount', {
        maxAmount: filter.maxAmount,
      });
    }

    const offset = (page - 1) * limit;
    queryBuilder
      .orderBy('transaction.createdAt', 'DESC')
      .skip(offset)
      .take(limit);

    const [transactions, total] = await queryBuilder.getManyAndCount();

    return { transactions, total };
  }

  async getStats(
    filter?: VnpayTransactionFilter,
  ): Promise<VnpayTransactionStats> {
    const queryBuilder = this.repository.createQueryBuilder('transaction');

    if (filter) {
      if (filter.fromDate || filter.toDate) {
        queryBuilder.where(
          'transaction.createdAt BETWEEN :fromDate AND :toDate',
          {
            fromDate: filter.fromDate || new Date('1970-01-01'),
            toDate: filter.toDate || new Date(),
          },
        );
      }
      if (filter.externalRef) {
        queryBuilder.andWhere('transaction.externalRef = :externalRef', {
          externalRef: filter.externalRef,
        });
      }
    }

    const [
      totalResult,
      successResult,
      failedResult,
      pendingResult,
      totalAmountResult,
      successAmountResult,
    ] = await Promise.all([
      queryBuilder.getCount(),
      queryBuilder
        .clone()
        .andWhere('transaction.status = :status', {
          status: VnpayTransactionStatus.SUCCESS,
        })
        .getCount(),
      queryBuilder
        .clone()
        .andWhere('transaction.status = :status', {
          status: VnpayTransactionStatus.FAILED,
        })
        .getCount(),
      queryBuilder
        .clone()
        .andWhere('transaction.status = :status', {
          status: VnpayTransactionStatus.PENDING,
        })
        .getCount(),
      queryBuilder
        .clone()
        .select('SUM(transaction.amount)', 'total')
        .getRawOne(),
      queryBuilder
        .clone()
        .select('SUM(transaction.amount)', 'total')
        .andWhere('transaction.status = :status', {
          status: VnpayTransactionStatus.SUCCESS,
        })
        .getRawOne(),
    ]);

    const totalAmount = parseInt(totalAmountResult?.total || '0');
    const successfulAmount = parseInt(successAmountResult?.total || '0');
    const successRate =
      totalResult > 0 ? (successResult / totalResult) * 100 : 0;

    return {
      totalTransactions: totalResult,
      successfulTransactions: successResult,
      failedTransactions: failedResult,
      pendingTransactions: pendingResult,
      totalAmount,
      successfulAmount,
      successRate: Math.round(successRate * 100) / 100,
    };
  }
}

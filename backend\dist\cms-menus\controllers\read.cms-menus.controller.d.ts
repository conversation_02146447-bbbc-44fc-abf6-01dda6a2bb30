import { ReadCmsMenusService } from '../services/read.cms-menus.service';
import { CmsMenuDto } from '../dto/cms-menu.dto';
import { CmsMenuStatus, CmsMenuPostType } from '../entity/cms-menus.entity';
import { CustomPaginationQueryDto } from '../../common/dto/custom-pagination-query.dto';
import { PaginationResponseDto } from '../../common/dto/pagination-response.dto';
export declare class ReadCmsMenusController {
    private readonly cmsMenusService;
    constructor(cmsMenusService: ReadCmsMenusService);
    findAll(paginationQuery: CustomPaginationQueryDto): Promise<PaginationResponseDto<CmsMenuDto>>;
    findByPostType(postType: CmsMenuPostType, paginationQuery: CustomPaginationQueryDto): Promise<PaginationResponseDto<CmsMenuDto>>;
    findByStatus(status: CmsMenuStatus, paginationQuery: CustomPaginationQueryDto): Promise<PaginationResponseDto<CmsMenuDto>>;
    count(filter?: string): Promise<number>;
    search(keyword: string, paginationQuery: CustomPaginationQueryDto): Promise<PaginationResponseDto<CmsMenuDto>>;
    findDeleted(paginationQuery: CustomPaginationQueryDto): Promise<PaginationResponseDto<CmsMenuDto>>;
    findOne(id: string, relations?: string): Promise<CmsMenuDto | null>;
    findBySlug(slug: string): Promise<CmsMenuDto | null>;
}

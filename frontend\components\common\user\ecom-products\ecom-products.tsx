'use client';

import { But<PERSON> } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';
import { api } from '@/lib/api';
import { PaginationResponse } from '@/lib/response';
import { ColumnFiltersState, PaginationState, RowSelectionState, SortingState, VisibilityState, getCoreRowModel, getFilteredRowModel, getSortedRowModel, useReactTable } from '@tanstack/react-table';
import { ShoppingCart } from 'lucide-react';
import { useCallback, useEffect, useState } from 'react';
import { toast } from "sonner";

import { TableFooter } from '../../data-table/table-footer';

import { FloatDeleteButton } from './components/float-delete-button';
import { StatusFilter, StatusTabs } from './components/status-tabs';
import { GridToolbar } from './components/grid-toolbar';
import { EcomOrderFormModal } from './ecom-order-form-modal';
import { EcomProductDetailSheet } from './ecom-product-detail-sheet';
import { EcomCartSheet } from './ecom-cart-sheet';
import { getEcomProductCell } from './table/ecom-product-cell';
import { EcomProduct } from './type/ecom-product';
import { EcomProductCardExample } from './components';
import { EcomProductsGrid } from './ecom-products-grid';
import { EcomProductCategory } from '@/components/common/admin/ecom-product-categories/type/ecom-product-category';
import { CartProvider, useCart } from '@/contexts/cart-context';

// Define interface for statistics response
interface ProductStatistics {
  total: number;
  active: number;
  inactive: number;
}

// Component chính sử dụng cart context
function EcomProductsContent() {
  const [products, setProducts] = useState<EcomProduct[]>([]);
  const [loading, setLoading] = useState(true)
  const [sorting, setSorting] = useState<SortingState>([])
  const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([])
  const [pagination, setPagination] = useState<PaginationState>({
    pageIndex: 0,
    pageSize: 20,
  })
  const [totalRows, setTotalRows] = useState(0)
  const [globalFilter, setGlobalFilter] = useState("")

  const [rowSelection, setRowSelection] = useState<RowSelectionState>({})
  const [columnVisibility, setColumnVisibility] = useState<VisibilityState>({})
  const [isRefreshing, setIsRefreshing] = useState(false)
  const [selectedProduct, setSelectedProduct] = useState<EcomProduct | null>(null)
  const [isShowSelectedRows, setIsShowSelectedRows] = useState(false)
  const [isUpdating, setIsUpdating] = useState(false)
  const [statusCounts, setStatusCounts] = useState({ all: 0, active: 0, inactive: 0 })
  const [showOrderFormModal, setShowOrderFormModal] = useState(false)
  const [showProductDetailSheet, setShowProductDetailSheet] = useState(false)
  const [selectedProductForOrder, setSelectedProductForOrder] = useState<EcomProduct | null>(null)
  const [statusFilter, setStatusFilter] = useState<StatusFilter>('all')

  // Sử dụng cart context
  const { state: cartState, openCart, addItem, closeCart } = useCart();

  // Grid toolbar states
  const [categories, setCategories] = useState<EcomProductCategory[]>([])
  const [selectedCategoryId, setSelectedCategoryId] = useState<string | undefined>(undefined)
  const [searchValue, setSearchValue] = useState("")

  // Grid toolbar handlers
  const handleSearchChange = (value: string) => {
    setSearchValue(value);
    setGlobalFilter(value);
    setPagination(prev => ({ ...prev, pageIndex: 0 })); // Reset về trang đầu tiên
  };

  const handleCategoryChange = (categoryId: string | undefined) => {
    setSelectedCategoryId(categoryId);
    setPagination(prev => ({ ...prev, pageIndex: 0 })); // Reset về trang đầu tiên
  };

  // Xử lý hiển thị các hàng đã chọn
  const handleShowSelectedRows = () => {
    setIsShowSelectedRows(!isShowSelectedRows)
  }

  // Xử lý khi thay đổi tab
  const handleStatusChange = (value: StatusFilter) => {
    setStatusFilter(value);
    let newFilters = [...columnFilters];

    // Xóa filter cũ về isActive nếu có
    newFilters = newFilters.filter(filter => filter.id !== 'isActive');

    // Thêm filter mới nếu không phải tab 'all'
    if (value !== 'all') {
      newFilters.push({
        id: 'isActive',
        value: value === 'active',
      });
    }

    setColumnFilters(newFilters);
    setPagination(prev => ({ ...prev, pageIndex: 0 })); // Reset về trang đầu tiên
  };

  // Xử lý xem chi tiết sản phẩm
  const handleViewDetail = (product: EcomProduct) => {
    setSelectedProduct(product);
    setShowProductDetailSheet(true);
    toast.info(`Đang xem thông tin của ${product.productName}`);
  };

  // Xử lý đặt hàng sản phẩm
  const handleOrderProduct = (product: EcomProduct) => {
    setSelectedProductForOrder(product);
    setShowOrderFormModal(true);
    toast.info(`Đang tạo đơn hàng cho ${product.productName}`);
  };

  // Xử lý xóa sản phẩm
  const handleDelete = async (product: EcomProduct) => {
    try {
      setIsUpdating(true);
      // Gọi API để soft delete sản phẩm
      await api.put(`ecom-products/${product.id}/soft-delete`);

      // Cập nhật dữ liệu local
      const updatedProducts = products.map(p => {
        if (p.id === product.id) {
          return {
            ...p,
            isDeleted: true,
            deletedAt: new Date().toISOString()
          };
        }
        return p;
      });

      setProducts(updatedProducts);
      toast.success(`Đã xóa sản phẩm ${product.productName}`);
    } catch (error) {
      console.error('Error deleting product:', error);
      toast.error("Không thể xóa sản phẩm");
    } finally {
      setIsUpdating(false);
      fetchProducts();
    }
  };

  // Xử lý cập nhật trạng thái sản phẩm (isActive)
  const handleUpdateProductStatus = async (productId: string, isActive: boolean) => {
    try {
      setIsUpdating(true);
      // Gọi API để cập nhật trạng thái
      await api.patch(`ecom-products/${productId}/status`, { isActive });

      // Cập nhật dữ liệu local
      const updatedProducts = products.map(product => {
        if (product.id === productId) {
          return {
            ...product,
            isActive
          };
        }
        return product;
      });

      setProducts(updatedProducts);

      toast.success(`Đã ${isActive ? 'kích hoạt' : 'vô hiệu hóa'} sản phẩm`);
    } catch (error) {
      console.error('Error updating product status:', error);
      toast.error("Không thể cập nhật trạng thái sản phẩm");
    } finally {
      setIsUpdating(false);
    }
  };

  const table = useReactTable({
    data: products,
    columns: [
      {
        id: 'select',
        size: 40,
        header: ({ table }) => (
          <div className="px-1">
            <Checkbox
              checked={
                table.getIsAllPageRowsSelected() ||
                (table.getIsSomePageRowsSelected() && "indeterminate")
              }
              onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
              aria-label="Select all"
              className="translate-y-[2px]"
            />
          </div>
        ),
        cell: ({ row }) => (
          <div className="px-1">
            <Checkbox
              checked={row.getIsSelected()}
              onCheckedChange={(value) => row.toggleSelected(!!value)}
              onClick={(e) => e.stopPropagation()}
              aria-label="Select row"
              className="translate-y-[2px]"
            />
          </div>
        ),
        enableSorting: false,
        enableHiding: false,
      },
      ...getEcomProductCell({
        onViewDetail: handleViewDetail,
        onDelete: handleDelete,
        onOrderProduct: handleOrderProduct,
        onUpdateProductStatus: handleUpdateProductStatus
      })
    ],
    state: {
      sorting,
      columnFilters,
      pagination,
      rowSelection,
      columnVisibility,
      globalFilter,
    },
    pageCount: Math.ceil(totalRows / pagination.pageSize),
    onSortingChange: setSorting,
    onColumnFiltersChange: setColumnFilters,
    onPaginationChange: setPagination,
    onRowSelectionChange: setRowSelection,
    onColumnVisibilityChange: setColumnVisibility,
    onGlobalFilterChange: setGlobalFilter,
    getCoreRowModel: getCoreRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    manualPagination: true,
    enableSorting: true,
    enableColumnFilters: true,
    enableRowSelection: true,
    enableMultiSort: false,
    manualSorting: false,
    manualFiltering: false,
  })

  useEffect(() => {
    fetchProducts();
  }, [pagination.pageIndex, pagination.pageSize, statusFilter, searchValue, selectedCategoryId]);

  useEffect(() => {
    // Fetch statistics and categories when component mounts
    fetchStatistics();
    fetchCategories();
  }, []);

  const fetchProducts = async () => {
    setLoading(true);
    try {
      // Build query parameters
      const params = new URLSearchParams({
        page: (pagination.pageIndex + 1).toString(),
        limit: pagination.pageSize.toString()
      });

      // Add search filter
      if (searchValue.trim()) {
        params.append('search', searchValue.trim());
      }

      // Add category filter
      if (selectedCategoryId) {
        params.append('filter', `categoryId:${selectedCategoryId}`);
      }

      // Add status filter if not 'all'
      if (statusFilter !== 'all') {
        const statusFilterValue = statusFilter === 'active' ? 'isActive:true' : 'isActive:false';
        if (selectedCategoryId) {
          // Combine with category filter
          params.set('filter', `categoryId:${selectedCategoryId},${statusFilterValue}`);
        } else {
          params.append('filter', statusFilterValue);
        }
      }

      const response = await api.get<PaginationResponse<EcomProduct>>(`ecom-products?${params.toString()}`);

      if (response && response.data) {
        setProducts(response.data);
        setTotalRows(response.meta?.itemCount || response.data.length);

        // Fetch statistics separately
        await fetchStatistics();
      }
    } catch (error) {
      console.error('Error fetching products:', error);
      toast.error("Không thể tải danh sách sản phẩm")
    } finally {
      setLoading(false);
      setIsRefreshing(false);
    }
  };

  // Hàm lấy thống kê số lượng sản phẩm theo trạng thái
  const fetchStatistics = async () => {
    try {
      const response = await api.get<ProductStatistics>('ecom-products/statistics');
      if (response) {
        // Cập nhật số lượng theo trạng thái
        setStatusCounts({
          all: response.total || 0,
          active: response.active || 0,
          inactive: response.inactive || 0
        });
      }
    } catch (error) {
      console.error('Error fetching product statistics:', error);

      // Đặt giá trị mặc định khi có lỗi
      setStatusCounts({
        all: 0,
        active: 0,
        inactive: 0
      });
    }
  };

  // Hàm lấy danh sách categories
  const fetchCategories = async () => {
    try {
      const response = await api.get<PaginationResponse<EcomProductCategory>>('ecom-product-categories?filter=isActive:true&limit=100');
      if (response && response.data) {
        setCategories(response.data);
      }
    } catch (error) {
      console.error('Error fetching categories:', error);
      setCategories([]);
    }
  };

  const handleRefresh = useCallback(async () => {
    setIsRefreshing(true)
    try {
      await fetchProducts()
    } finally {
      setTimeout(() => {
        setIsRefreshing(false)
      }, 1000)
    }
  }, [pagination.pageIndex, pagination.pageSize, statusFilter, searchValue, selectedCategoryId])

  const handleDeleteSelected = async () => {
    try {
      setIsUpdating(true);
      const selectedRows = table.getSelectedRowModel().rows;
      const selectedProducts = selectedRows.map(row => row.original);
      const ids = selectedProducts.map(product => product.id);

      // Gọi API để xóa hàng loạt
      await api.put('ecom-products/bulk/soft-delete', ids);

      toast.success(`Đã xóa ${selectedProducts.length} sản phẩm đã chọn`);
      // Cập nhật dữ liệu
      await fetchProducts();
      // Reset lựa chọn
      setRowSelection({});
    } catch (error) {
      console.error('Error deleting selected products:', error);
      toast.error("Không thể xóa các sản phẩm đã chọn");
    } finally {
      setIsUpdating(false);
    }
  };

  // Loại bỏ function handleAddProduct vì không cần thiết cho user interface

  // Function dummy cho onEdit vì user không được phép chỉnh sửa sản phẩm
  const handleEditDummy = () => {
    toast.info("Bạn không có quyền chỉnh sửa sản phẩm");
  };

  // Xử lý khi nhấn button "Giỏ hàng" - mở cart sheet
  const handleOpenCart = () => {
    openCart();
  };

  // Xử lý checkout từ cart sheet
  const handleCartCheckout = () => {
    if (cartState.items.length === 0) {
      toast.error("Giỏ hàng trống, không thể đặt hàng");
      return;
    }

    // Đóng cart sheet và mở modal đặt hàng
    closeCart();
    setSelectedProductForOrder(null); // Không có sản phẩm được chọn trước
    setShowOrderFormModal(true);
    toast.info("Mở form đặt hàng từ giỏ hàng");
  };

  return (
    <div className="w-full flex flex-col h-full">
      {/* Header Navigation */}
      <div className="w-full flex justify-between items-center border-b py-1.5 px-6 h-10">
        <div className="flex items-center gap-2">
          <div className="flex items-center gap-1">
            <span className="text-sm font-medium">Sản phẩm</span>
            <span className="text-xs bg-accent rounded-md px-1.5 py-1">{totalRows}</span>
          </div>
        </div>
        <div className="flex items-center gap-2">
          {isUpdating && (
            <span className="text-xs text-muted-foreground">Đang cập nhật...</span>
          )}

          <Button size="sm" variant="secondary" onClick={handleOpenCart}>
            <ShoppingCart className="size-4 mr-1" />
            Giỏ hàng
            {cartState.totalItems > 0 && (
              <span className="ml-1 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center">
                {cartState.totalItems}
              </span>
            )}
          </Button>
        </div>

      </div>

  

      {/* Grid Toolbar */}
      <GridToolbar
        searchValue={searchValue}
        onSearchChange={handleSearchChange}
        onRefresh={handleRefresh}
        isRefreshing={isRefreshing}
        categories={categories}
        selectedCategoryId={selectedCategoryId}
        onCategoryChange={handleCategoryChange}
      />

      {/* Products Grid */}
      <div className="flex-1 overflow-auto">
        <EcomProductsGrid
          products={products}
          onAddToCart={addItem}
          onProductClick={(product) => {
            setSelectedProduct(product);
            setShowProductDetailSheet(true);
          }}
          loading={loading}
          className="w-full"
        />
      </div>


      {/* Fixed Pagination Footer */}
      <TableFooter table={table} totalItems={totalRows} isShowSelectedRows={isShowSelectedRows} onShowSelectedRows={handleShowSelectedRows} />

      {/* Order Form Modal */}
      <EcomOrderFormModal
        isOpen={showOrderFormModal}
        onClose={() => setShowOrderFormModal(false)}
        order={null}
        mode="create"
        onSuccess={handleRefresh}
        selectedProduct={selectedProductForOrder}
      />

      {/* Product Detail Sheet */}
      <EcomProductDetailSheet
        isOpen={showProductDetailSheet}
        onClose={() => setShowProductDetailSheet(false)}
        product={selectedProduct}
        onEdit={handleEditDummy}
      />

      {/* Float Delete Button */}
      <FloatDeleteButton
        selectedCount={Object.keys(rowSelection).length}
        onDelete={handleDeleteSelected}
      />

      {/* Cart Sheet */}
      <EcomCartSheet onCheckout={handleCartCheckout} />
    </div>
  );
}

// Component wrapper với CartProvider
export default function EcomProducts() {
  return (
    <CartProvider>
      <EcomProductsContent />
    </CartProvider>
  );
}

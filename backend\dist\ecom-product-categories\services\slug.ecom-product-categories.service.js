"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SlugEcomProductCategoriesService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const ecom_product_categories_entity_1 = require("../entity/ecom-product-categories.entity");
const base_slug_service_1 = require("../../common/services/base-slug.service");
const unified_slug_service_1 = require("../../common/services/unified-slug.service");
let SlugEcomProductCategoriesService = class SlugEcomProductCategoriesService extends base_slug_service_1.BaseSlugService {
    unifiedSlugService;
    constructor(categoryRepository, unifiedSlugService) {
        super(categoryRepository, unifiedSlugService);
        this.unifiedSlugService = unifiedSlugService;
    }
    getSlugFieldName() {
        return 'slug';
    }
    getTextFieldName() {
        return 'name';
    }
    getWhereConditions(excludeId) {
        return { isDeleted: false };
    }
    generateFallbackSlug() {
        return `danh-muc-sp-${Date.now()}`;
    }
    generateSlugFromName(name) {
        return this.unifiedSlugService.generateCategorySlug(name);
    }
    validateSlug(slug) {
        if (!slug || slug.trim() === '') {
            return false;
        }
        const slugPattern = /^[a-z0-9]+(?:-[a-z0-9]+)*$/;
        return slugPattern.test(slug.trim());
    }
    async generateUniqueSlugForCreate(name, providedSlug) {
        let baseSlug;
        if (providedSlug && providedSlug.trim() !== '') {
            if (!this.validateSlug(providedSlug)) {
                baseSlug = this.generateSlugFromName(name);
            }
            else {
                baseSlug = providedSlug.toLowerCase().trim();
            }
        }
        else {
            baseSlug = this.generateSlugFromName(name);
        }
        const existingSlugs = await this.getExistingSlugs();
        return this.unifiedSlugService.generateUniqueSlug(baseSlug, existingSlugs);
    }
    async generateUniqueSlugForUpdate(name, categoryId, providedSlug) {
        let baseSlug;
        if (providedSlug && providedSlug.trim() !== '') {
            if (!this.validateSlug(providedSlug)) {
                baseSlug = this.generateSlugFromName(name);
            }
            else {
                baseSlug = providedSlug.toLowerCase().trim();
            }
        }
        else {
            baseSlug = this.generateSlugFromName(name);
        }
        const existingSlugs = await this.getExistingSlugs(categoryId);
        return this.unifiedSlugService.generateUniqueSlug(baseSlug, existingSlugs);
    }
    async getExistingSlugs(excludeId) {
        const queryBuilder = this.repository
            .createQueryBuilder('category')
            .select('category.slug')
            .where('category.isDeleted = :isDeleted', { isDeleted: false });
        if (excludeId) {
            queryBuilder.andWhere('category.id != :excludeId', { excludeId });
        }
        const results = await queryBuilder.getRawMany();
        return results.map(result => result.category_slug).filter(slug => slug);
    }
    async isSlugExists(slug, excludeId) {
        const queryBuilder = this.repository
            .createQueryBuilder('category')
            .where('category.slug = :slug', { slug })
            .andWhere('category.isDeleted = :isDeleted', { isDeleted: false });
        if (excludeId) {
            queryBuilder.andWhere('category.id != :excludeId', { excludeId });
        }
        const count = await queryBuilder.getCount();
        return count > 0;
    }
};
exports.SlugEcomProductCategoriesService = SlugEcomProductCategoriesService;
exports.SlugEcomProductCategoriesService = SlugEcomProductCategoriesService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(ecom_product_categories_entity_1.EcomProductCategories)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        unified_slug_service_1.UnifiedSlugService])
], SlugEcomProductCategoriesService);
//# sourceMappingURL=slug.ecom-product-categories.service.js.map
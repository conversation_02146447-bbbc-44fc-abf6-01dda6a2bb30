import { DeleteCmsTagsService } from '../services/delete.cms-tags.service';
import { CmsTagDto } from '../dto/cms-tag.dto';
export declare class DeleteCmsTagsController {
    private readonly cmsTagsService;
    constructor(cmsTagsService: DeleteCmsTagsService);
    softDelete(id: string, userId: string): Promise<CmsTagDto | null>;
    restore(id: string, userId: string): Promise<CmsTagDto | null>;
    remove(id: string): Promise<{
        affected: number;
    }>;
    bulkSoftDelete(ids: string[], userId: string): Promise<CmsTagDto[]>;
    bulkRestore(ids: string[], userId: string): Promise<CmsTagDto[]>;
    bulkRemove(ids: string[]): Promise<{
        affected: number;
    }>;
}

"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UpdateCmsBannersService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const event_emitter_1 = require("@nestjs/event-emitter");
const typeorm_transactional_1 = require("typeorm-transactional");
const base_cms_banners_service_1 = require("./base.cms-banners.service");
const cms_banners_entity_1 = require("../entity/cms-banners.entity");
const update_cms_banner_dto_1 = require("../dto/update.cms-banner.dto");
let UpdateCmsBannersService = class UpdateCmsBannersService extends base_cms_banners_service_1.BaseCmsBannersService {
    bannerRepository;
    dataSource;
    eventEmitter;
    constructor(bannerRepository, dataSource, eventEmitter) {
        super(bannerRepository, dataSource, eventEmitter);
        this.bannerRepository = bannerRepository;
        this.dataSource = dataSource;
        this.eventEmitter = eventEmitter;
    }
    async update(id, updateDto, userId) {
        try {
            this.logger.debug(`Đang cập nhật banner CMS với ID: ${id}`);
            const banner = await this.findById(id, []);
            if (!banner) {
                throw new common_1.NotFoundException(`Không tìm thấy banner với ID: ${id}`);
            }
            if (updateDto.title && updateDto.title !== banner.title) {
                const existingBannerByTitle = await this.bannerRepository.findOne({
                    where: { title: updateDto.title, isDeleted: false },
                });
                if (existingBannerByTitle && existingBannerByTitle.id !== id) {
                    throw new common_1.ConflictException(`Tên banner "${updateDto.title}" đã tồn tại`);
                }
            }
            const startDate = updateDto.startDate ? new Date(updateDto.startDate) : banner.startDate;
            const endDate = updateDto.endDate ? new Date(updateDto.endDate) : banner.endDate;
            if (startDate && endDate && startDate >= endDate) {
                throw new common_1.BadRequestException('Ngày bắt đầu phải nhỏ hơn ngày kết thúc');
            }
            const oldData = this.toDto(banner);
            if (updateDto.title !== undefined) {
                banner.title = updateDto.title;
            }
            if (updateDto.imageUrlDesktop !== undefined) {
                banner.imageUrlDesktop = updateDto.imageUrlDesktop;
            }
            if (updateDto.imageUrlMobile !== undefined) {
                banner.imageUrlMobile = updateDto.imageUrlMobile || null;
            }
            if (updateDto.linkUrl !== undefined) {
                banner.linkUrl = updateDto.linkUrl || null;
            }
            if (updateDto.altText !== undefined) {
                banner.altText = updateDto.altText || null;
            }
            if (updateDto.status !== undefined) {
                banner.status = updateDto.status;
            }
            if (updateDto.location !== undefined) {
                banner.location = updateDto.location || null;
            }
            if (updateDto.displayOrder !== undefined && updateDto.displayOrder !== banner.displayOrder) {
                const oldOrder = banner.displayOrder;
                const newOrder = updateDto.displayOrder;
                if (newOrder > oldOrder) {
                    await this.bannerRepository
                        .createQueryBuilder()
                        .update(cms_banners_entity_1.CmsBanners)
                        .set({ displayOrder: () => 'display_order - 1' })
                        .where('display_order > :oldOrder AND display_order <= :newOrder', { oldOrder, newOrder })
                        .andWhere('location = :location OR (location IS NULL AND :location IS NULL)', { location: banner.location })
                        .andWhere('is_deleted = :isDeleted', { isDeleted: false })
                        .andWhere('id != :id', { id })
                        .execute();
                }
                else {
                    await this.bannerRepository
                        .createQueryBuilder()
                        .update(cms_banners_entity_1.CmsBanners)
                        .set({ displayOrder: () => 'display_order + 1' })
                        .where('display_order >= :newOrder AND display_order < :oldOrder', { newOrder, oldOrder })
                        .andWhere('location = :location OR (location IS NULL AND :location IS NULL)', { location: banner.location })
                        .andWhere('is_deleted = :isDeleted', { isDeleted: false })
                        .andWhere('id != :id', { id })
                        .execute();
                }
                banner.displayOrder = newOrder;
            }
            if (updateDto.startDate !== undefined) {
                banner.startDate = updateDto.startDate ? new Date(updateDto.startDate) : null;
            }
            if (updateDto.endDate !== undefined) {
                banner.endDate = updateDto.endDate ? new Date(updateDto.endDate) : null;
            }
            banner.updatedBy = userId;
            const updatedBanner = await this.bannerRepository.save(banner);
            const bannerDto = this.toDto(updatedBanner);
            if (!bannerDto) {
                throw new common_1.InternalServerErrorException('Không thể chuyển đổi entity thành DTO');
            }
            this.eventEmitter.emit(this.EVENT_BANNER_UPDATED, {
                bannerId: bannerDto.id,
                userId,
                oldData,
                newData: bannerDto,
            });
            return bannerDto;
        }
        catch (error) {
            this.logger.error(`Lỗi khi cập nhật banner CMS: ${error.message}`, error.stack);
            if (error instanceof common_1.BadRequestException || error instanceof common_1.NotFoundException || error instanceof common_1.ConflictException) {
                throw error;
            }
            throw new common_1.InternalServerErrorException(`Không thể cập nhật banner CMS: ${error.message}`);
        }
    }
    async bulkUpdate(updates, userId) {
        try {
            this.logger.debug(`Đang cập nhật ${updates.length} banner CMS`);
            const updatedBanners = [];
            for (const update of updates) {
                const banner = await this.update(update.id, update.data, userId);
                if (banner) {
                    updatedBanners.push(banner);
                }
            }
            return updatedBanners;
        }
        catch (error) {
            this.logger.error(`Lỗi khi cập nhật nhiều banner CMS: ${error.message}`, error.stack);
            throw new common_1.InternalServerErrorException(`Không thể cập nhật nhiều banner CMS: ${error.message}`);
        }
    }
    async updateStatus(id, status, userId) {
        try {
            this.logger.debug(`Đang cập nhật trạng thái banner CMS với ID: ${id} thành ${status}`);
            const updateDto = { status };
            return this.update(id, updateDto, userId);
        }
        catch (error) {
            this.logger.error(`Lỗi khi cập nhật trạng thái banner CMS: ${error.message}`, error.stack);
            if (error instanceof common_1.NotFoundException) {
                throw error;
            }
            throw new common_1.InternalServerErrorException(`Không thể cập nhật trạng thái banner CMS: ${error.message}`);
        }
    }
    async bulkUpdateStatus(ids, status, userId) {
        try {
            this.logger.debug(`Đang cập nhật trạng thái ${ids.length} banner CMS thành ${status}`);
            const updatedBanners = [];
            for (const id of ids) {
                const banner = await this.updateStatus(id, status, userId);
                if (banner) {
                    updatedBanners.push(banner);
                }
            }
            return updatedBanners;
        }
        catch (error) {
            this.logger.error(`Lỗi khi cập nhật trạng thái nhiều banner CMS: ${error.message}`, error.stack);
            throw new common_1.InternalServerErrorException(`Không thể cập nhật trạng thái nhiều banner CMS: ${error.message}`);
        }
    }
    async updateLocation(id, location, userId) {
        try {
            this.logger.debug(`Đang cập nhật vị trí hiển thị banner CMS với ID: ${id} thành ${location}`);
            const updateDto = { location };
            return this.update(id, updateDto, userId);
        }
        catch (error) {
            this.logger.error(`Lỗi khi cập nhật vị trí hiển thị banner CMS: ${error.message}`, error.stack);
            if (error instanceof common_1.NotFoundException) {
                throw error;
            }
            throw new common_1.InternalServerErrorException(`Không thể cập nhật vị trí hiển thị banner CMS: ${error.message}`);
        }
    }
    async updateDisplayOrder(id, displayOrder, userId) {
        try {
            this.logger.debug(`Đang cập nhật thứ tự hiển thị banner CMS với ID: ${id}`);
            const updateDto = { displayOrder };
            return this.update(id, updateDto, userId);
        }
        catch (error) {
            this.logger.error(`Lỗi khi cập nhật thứ tự hiển thị banner CMS: ${error.message}`, error.stack);
            if (error instanceof common_1.NotFoundException) {
                throw error;
            }
            throw new common_1.InternalServerErrorException(`Không thể cập nhật thứ tự hiển thị banner CMS: ${error.message}`);
        }
    }
    async updateDisplayTime(id, startDate, endDate, userId) {
        try {
            this.logger.debug(`Đang cập nhật thời gian hiển thị banner CMS với ID: ${id}`);
            const updateDto = {
                startDate: startDate || undefined,
                endDate: endDate || undefined,
            };
            return this.update(id, updateDto, userId);
        }
        catch (error) {
            this.logger.error(`Lỗi khi cập nhật thời gian hiển thị banner CMS: ${error.message}`, error.stack);
            if (error instanceof common_1.NotFoundException || error instanceof common_1.BadRequestException) {
                throw error;
            }
            throw new common_1.InternalServerErrorException(`Không thể cập nhật thời gian hiển thị banner CMS: ${error.message}`);
        }
    }
};
exports.UpdateCmsBannersService = UpdateCmsBannersService;
__decorate([
    (0, typeorm_transactional_1.Transactional)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, update_cms_banner_dto_1.UpdateCmsBannerDto, String]),
    __metadata("design:returntype", Promise)
], UpdateCmsBannersService.prototype, "update", null);
__decorate([
    (0, typeorm_transactional_1.Transactional)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Array, String]),
    __metadata("design:returntype", Promise)
], UpdateCmsBannersService.prototype, "bulkUpdate", null);
__decorate([
    (0, typeorm_transactional_1.Transactional)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String, String]),
    __metadata("design:returntype", Promise)
], UpdateCmsBannersService.prototype, "updateStatus", null);
__decorate([
    (0, typeorm_transactional_1.Transactional)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Array, String, String]),
    __metadata("design:returntype", Promise)
], UpdateCmsBannersService.prototype, "bulkUpdateStatus", null);
__decorate([
    (0, typeorm_transactional_1.Transactional)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object, String]),
    __metadata("design:returntype", Promise)
], UpdateCmsBannersService.prototype, "updateLocation", null);
__decorate([
    (0, typeorm_transactional_1.Transactional)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Number, String]),
    __metadata("design:returntype", Promise)
], UpdateCmsBannersService.prototype, "updateDisplayOrder", null);
__decorate([
    (0, typeorm_transactional_1.Transactional)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object, Object, String]),
    __metadata("design:returntype", Promise)
], UpdateCmsBannersService.prototype, "updateDisplayTime", null);
exports.UpdateCmsBannersService = UpdateCmsBannersService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(cms_banners_entity_1.CmsBanners)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        typeorm_2.DataSource,
        event_emitter_1.EventEmitter2])
], UpdateCmsBannersService);
//# sourceMappingURL=update.cms-banners.service.js.map
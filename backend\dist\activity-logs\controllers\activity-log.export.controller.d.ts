import { User } from '../../users/entities/user.entity';
import { ExportActivityLogService } from '../services/export.activity-log.service';
import { CustomPaginationQueryDto } from '../../common/dto/custom-pagination-query.dto';
export declare class ActivityLogExportController {
    private readonly activityLogService;
    private readonly logger;
    constructor(activityLogService: ExportActivityLogService);
    export(format?: 'csv' | 'json'): Promise<any>;
    exportByUser(userId: string, format: "csv" | "json" | undefined, currentUserId: string, roles: string[]): Promise<any>;
    exportByModule(module: string, format?: 'csv' | 'json'): Promise<any>;
    exportWithFilter(paginationQuery: CustomPaginationQueryDto, format?: 'csv' | 'json'): Promise<any>;
    exportMyActivities(user: User, format?: 'csv' | 'json'): Promise<any>;
}

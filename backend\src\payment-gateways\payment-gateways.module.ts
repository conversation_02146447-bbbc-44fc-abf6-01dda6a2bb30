import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { TypeOrmModule } from '@nestjs/typeorm';
import { EventEmitterModule } from '@nestjs/event-emitter';
import { HttpModule } from '@nestjs/axios';

import { PaymentGatewaysController } from './payment-gateways.controller';
import { VnpayService } from './services/vnpay.service';
import { MomoService } from './services/momo.service';
import { PaymentGatewaysService } from './payment-gateways.service';
import { Transaction } from '../transactions/entities/transaction.entity';
import { Wallet } from '../wallets/entities/wallet.entity';
import { WalletsModule } from '../wallets/wallets.module';
import { TransactionsModule } from '../transactions/transactions.module';

@Module({
  imports: [
    ConfigModule,
    TypeOrmModule.forFeature([Transaction, Wallet]),
    EventEmitterModule.forRoot(),
    HttpModule,
    WalletsModule,
    TransactionsModule,
  ],
  controllers: [PaymentGatewaysController],
  providers: [PaymentGatewaysService, VnpayService, MomoService],
  exports: [PaymentGatewaysService, VnpayService, MomoService],
})
export class PaymentGatewaysModule {}

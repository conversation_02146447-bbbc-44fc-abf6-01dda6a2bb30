import { 
  Controller, 
  Post, 
  Get, 
  Body, 
  Query, 
  Param, 
  HttpCode, 
  HttpStatus, 
  Logger,
  Req,
  <PERSON>s,
  BadRequestException
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBody, ApiQuery } from '@nestjs/swagger';
import { Request, Response } from 'express';
import { PaymentGatewaysService, PaymentResult, CallbackResult, IpnResponse } from '../payment-gateways.service';
import { CreatePaymentDto } from '../dto/create-payment.dto';
import { PaymentGatewayType } from '../enums/payment-gateway-type.enum';

@ApiTags('Payment Gateways')
@Controller('payment-gateways')
export class PaymentGatewaysController {
  private readonly logger = new Logger(PaymentGatewaysController.name);

  constructor(
    private readonly paymentGatewaysService: PaymentGatewaysService,
  ) {}

  /**
   * Tạo URL thanh toán
   */
  @Post('create-payment')
  @ApiOperation({ summary: 'Tạo URL thanh toán' })
  @ApiResponse({ status: 201, description: 'Tạo thành công' })
  @ApiBody({ type: CreatePaymentDto })
  async createPayment(@Body() createPaymentDto: CreatePaymentDto): Promise<PaymentResult> {
    try {
      // Callbacks tùy chọn để xử lý sự kiện
      const callbacks = {
        onPaymentCreated: async (result: PaymentResult) => {
          this.logger.log(`Payment created: ${result.transactionId}`);
          // TODO: Có thể gửi email, SMS, notification, etc.
        },
        onPaymentSuccess: async (result: CallbackResult) => {
          this.logger.log(`Payment success: ${result.transactionId}`);
          // TODO: Xử lý logic business sau khi thanh toán thành công
        },
        onPaymentFailed: async (result: CallbackResult) => {
          this.logger.log(`Payment failed: ${result.transactionId} - ${result.message}`);
          // TODO: Xử lý logic business khi thanh toán thất bại
        },
      };

      return await this.paymentGatewaysService.createPayment(createPaymentDto, callbacks);
    } catch (error) {
      this.logger.error(`Error creating payment: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Xử lý VNPAY Return URL
   */
  @Get('vnpay/return')
  @ApiOperation({ summary: 'Xử lý VNPAY Return URL' })
  @ApiResponse({ status: 200, description: 'Xử lý thành công' })
  async handleVnpayReturn(
    @Query() query: Record<string, string>,
    @Res() res: Response
  ): Promise<void> {
    try {
      const result = await this.paymentGatewaysService.handleVnpayCallback(query);
      
      // Redirect user dựa trên kết quả
      if (result.isSuccess) {
        // Redirect đến trang thành công
        const successUrl = `${process.env.FRONTEND_URL}/payment/success?transactionId=${result.transactionId}&amount=${result.amount}`;
        res.redirect(successUrl);
      } else {
        // Redirect đến trang thất bại
        const failureUrl = `${process.env.FRONTEND_URL}/payment/failure?message=${encodeURIComponent(result.message || 'Payment failed')}`;
        res.redirect(failureUrl);
      }
    } catch (error) {
      this.logger.error(`Error handling VNPAY return: ${error.message}`, error.stack);
      const errorUrl = `${process.env.FRONTEND_URL}/payment/error?message=${encodeURIComponent('System error')}`;
      res.redirect(errorUrl);
    }
  }

  /**
   * Xử lý VNPAY IPN
   */
  @Post('vnpay/ipn')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: 'Xử lý VNPAY IPN' })
  @ApiResponse({ status: 200, description: 'IPN processed' })
  async handleVnpayIpn(
    @Query() query: Record<string, string>
  ): Promise<{ RspCode: string; Message: string }> {
    try {
      const result = await this.paymentGatewaysService.handleVnpayIpn(query);
      
      return {
        RspCode: result.code,
        Message: result.message,
      };
    } catch (error) {
      this.logger.error(`Error handling VNPAY IPN: ${error.message}`, error.stack);
      return {
        RspCode: '99',
        Message: 'Unknown error',
      };
    }
  }

  /**
   * Truy vấn giao dịch
   */
  @Post('query/:gatewayType')
  @ApiOperation({ summary: 'Truy vấn giao dịch từ gateway' })
  @ApiResponse({ status: 200, description: 'Truy vấn thành công' })
  async queryTransaction(
    @Param('gatewayType') gatewayType: PaymentGatewayType,
    @Body() queryData: {
      txnRef: string;
      transactionDate: string;
      orderInfo: string;
      ipAddr: string;
      transactionNo?: string;
    }
  ): Promise<any> {
    try {
      return await this.paymentGatewaysService.queryTransaction(gatewayType, queryData);
    } catch (error) {
      this.logger.error(`Error querying transaction: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Hoàn tiền
   */
  @Post('refund/:gatewayType')
  @ApiOperation({ summary: 'Hoàn tiền từ gateway' })
  @ApiResponse({ status: 200, description: 'Hoàn tiền thành công' })
  async refundTransaction(
    @Param('gatewayType') gatewayType: PaymentGatewayType,
    @Body() refundData: {
      txnRef: string;
      amount: number;
      orderInfo: string;
      transactionDate: string;
      transactionType: '02' | '03';
      createBy: string;
      ipAddr: string;
      transactionNo?: string;
    }
  ): Promise<any> {
    try {
      return await this.paymentGatewaysService.refundTransaction(gatewayType, refundData);
    } catch (error) {
      this.logger.error(`Error refunding transaction: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Lấy trạng thái giao dịch từ database
   */
  @Get('transaction/:transactionId/status')
  @ApiOperation({ summary: 'Lấy trạng thái giao dịch' })
  @ApiResponse({ status: 200, description: 'Lấy thành công' })
  async getTransactionStatus(
    @Param('transactionId') transactionId: string
  ): Promise<any> {
    try {
      // TODO: Implement get transaction status from database
      // const transaction = await this.transactionService.findByGatewayTransactionId(transactionId);
      // return transaction;
      
      return {
        message: 'Feature not implemented yet',
        transactionId,
      };
    } catch (error) {
      this.logger.error(`Error getting transaction status: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Webhook test endpoint
   */
  @Post('webhook/test')
  @ApiOperation({ summary: 'Test webhook endpoint' })
  @ApiResponse({ status: 200, description: 'Test successful' })
  async testWebhook(
    @Body() body: any,
    @Req() req: Request
  ): Promise<any> {
    this.logger.log(`Webhook test received: ${JSON.stringify(body)}`);
    this.logger.log(`Headers: ${JSON.stringify(req.headers)}`);
    
    return {
      success: true,
      message: 'Webhook received successfully',
      timestamp: new Date().toISOString(),
      data: body,
    };
  }

  /**
   * Health check cho payment gateways
   */
  @Get('health')
  @ApiOperation({ summary: 'Health check' })
  @ApiResponse({ status: 200, description: 'Service healthy' })
  async healthCheck(): Promise<any> {
    return {
      status: 'healthy',
      timestamp: new Date().toISOString(),
      services: {
        vnpay: 'configured',
        momo: 'configured',
        database: 'connected',
      },
    };
  }
}

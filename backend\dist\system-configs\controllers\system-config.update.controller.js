"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var SystemConfigUpdateController_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.SystemConfigUpdateController = void 0;
const openapi = require("@nestjs/swagger");
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const jwt_auth_guard_1 = require("../../common/guards/jwt-auth.guard");
const update_system_config_service_1 = require("../services/update.system-config.service");
const update_system_config_dto_1 = require("../dto/update-system-config.dto");
const api_response_dto_1 = require("../../common/dto/api-response.dto");
const get_user_decorator_1 = require("../../common/decorators/get-user.decorator");
const roles_decorator_1 = require("../../common/decorators/roles.decorator");
const permissions_decorator_1 = require("../../common/decorators/permissions.decorator");
let SystemConfigUpdateController = SystemConfigUpdateController_1 = class SystemConfigUpdateController {
    systemConfigService;
    logger = new common_1.Logger(SystemConfigUpdateController_1.name);
    constructor(systemConfigService) {
        this.systemConfigService = systemConfigService;
    }
    async update(id, updateSystemConfigDto, userId) {
        this.logger.debug(`Đang cập nhật cấu hình hệ thống với ID: ${id}`);
        updateSystemConfigDto.updatedBy = userId;
        return this.systemConfigService.update(id, updateSystemConfigDto);
    }
    async updateByKey(key, updateSystemConfigDto, userId) {
        this.logger.debug(`Đang cập nhật cấu hình hệ thống với khóa: ${key}`);
        updateSystemConfigDto.updatedBy = userId;
        return this.systemConfigService.updateByKey(key, updateSystemConfigDto);
    }
    async updateBulk(bulkDto, userId) {
        this.logger.debug(`Đang cập nhật ${bulkDto.length} cấu hình hệ thống`);
        bulkDto.forEach(item => {
            item.updatedBy = userId;
        });
        return this.systemConfigService.updateBulk(bulkDto);
    }
};
exports.SystemConfigUpdateController = SystemConfigUpdateController;
__decorate([
    (0, common_1.Put)(':id'),
    (0, roles_decorator_1.Roles)('ADMIN'),
    (0, permissions_decorator_1.Permissions)('config:update'),
    (0, swagger_1.ApiOperation)({ summary: 'Cập nhật cấu hình hệ thống theo ID' }),
    (0, swagger_1.ApiParam)({
        name: 'id',
        description: 'ID của cấu hình hệ thống cần cập nhật',
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Cấu hình hệ thống được cập nhật thành công.',
        type: api_response_dto_1.ApiResponseDto,
        schema: {
            properties: { data: { $ref: '#/components/schemas/SystemConfigDto' } },
        },
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.NOT_FOUND,
        description: 'Không tìm thấy cấu hình hệ thống.',
        type: api_response_dto_1.ApiResponseDto,
        schema: { properties: { data: { type: 'object', nullable: true } } },
    }),
    openapi.ApiResponse({ status: 200, type: require("../dto/system-config.dto").SystemConfigDto }),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __param(1, (0, common_1.Body)()),
    __param(2, (0, get_user_decorator_1.GetUser)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, update_system_config_dto_1.UpdateSystemConfigDto, String]),
    __metadata("design:returntype", Promise)
], SystemConfigUpdateController.prototype, "update", null);
__decorate([
    (0, common_1.Put)('key/:key'),
    (0, roles_decorator_1.Roles)('ADMIN'),
    (0, permissions_decorator_1.Permissions)('config:update'),
    (0, swagger_1.ApiOperation)({ summary: 'Cập nhật cấu hình hệ thống theo khóa' }),
    (0, swagger_1.ApiParam)({
        name: 'key',
        description: 'Khóa của cấu hình hệ thống cần cập nhật',
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Cấu hình hệ thống được cập nhật thành công.',
        type: api_response_dto_1.ApiResponseDto,
        schema: {
            properties: { data: { $ref: '#/components/schemas/SystemConfigDto' } },
        },
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.NOT_FOUND,
        description: 'Không tìm thấy cấu hình hệ thống.',
        type: api_response_dto_1.ApiResponseDto,
        schema: { properties: { data: { type: 'object', nullable: true } } },
    }),
    openapi.ApiResponse({ status: 200, type: require("../dto/system-config.dto").SystemConfigDto }),
    __param(0, (0, common_1.Param)('key')),
    __param(1, (0, common_1.Body)()),
    __param(2, (0, get_user_decorator_1.GetUser)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, update_system_config_dto_1.UpdateSystemConfigDto, String]),
    __metadata("design:returntype", Promise)
], SystemConfigUpdateController.prototype, "updateByKey", null);
__decorate([
    (0, common_1.Put)('bulk'),
    (0, roles_decorator_1.Roles)('ADMIN'),
    (0, permissions_decorator_1.Permissions)('config:update'),
    (0, swagger_1.ApiOperation)({ summary: 'Cập nhật nhiều cấu hình hệ thống cùng lúc' }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Các cấu hình hệ thống được cập nhật thành công.',
        type: api_response_dto_1.ApiResponseDto,
        schema: {
            properties: {
                data: {
                    type: 'array',
                    items: { $ref: '#/components/schemas/SystemConfigDto' },
                },
            },
        },
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.NOT_FOUND,
        description: 'Không tìm thấy một hoặc nhiều cấu hình hệ thống.',
        type: api_response_dto_1.ApiResponseDto,
        schema: { properties: { data: { type: 'object', nullable: true } } },
    }),
    openapi.ApiResponse({ status: 200, type: [require("../dto/system-config.dto").SystemConfigDto] }),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, get_user_decorator_1.GetUser)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Array, String]),
    __metadata("design:returntype", Promise)
], SystemConfigUpdateController.prototype, "updateBulk", null);
exports.SystemConfigUpdateController = SystemConfigUpdateController = SystemConfigUpdateController_1 = __decorate([
    (0, swagger_1.ApiTags)('system-configs'),
    (0, swagger_1.ApiBearerAuth)(),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, common_1.Controller)('system-configs'),
    __metadata("design:paramtypes", [update_system_config_service_1.UpdateSystemConfigService])
], SystemConfigUpdateController);
//# sourceMappingURL=system-config.update.controller.js.map
import { ReadCmsBannersService } from '../services/read.cms-banners.service';
import { CmsBannerPublicDto } from '../dto/cms-banner.public.dto';
import { CmsBannerLocation } from '../entity/cms-banners.entity';
import { CustomPaginationQueryDto } from '../../common/dto/custom-pagination-query.dto';
import { PaginationResponseDto } from '../../common/dto/pagination-response.dto';
export declare class ReadCmsBannersPublicController {
    private readonly cmsBannersService;
    constructor(cmsBannersService: ReadCmsBannersService);
    getActiveBanners(paginationQuery: CustomPaginationQueryDto, location?: CmsBannerLocation): Promise<PaginationResponseDto<CmsBannerPublicDto>>;
    getBannersByLocation(location: CmsBannerLocation, paginationQuery: CustomPaginationQueryDto): Promise<PaginationResponseDto<CmsBannerPublicDto>>;
    getCarouselBanners(limit?: number): Promise<CmsBannerPublicDto[]>;
    searchBanners(searchTerm: string, paginationQuery: CustomPaginationQueryDto): Promise<PaginationResponseDto<CmsBannerPublicDto>>;
    listBanners(paginationQuery: CustomPaginationQueryDto): Promise<PaginationResponseDto<CmsBannerPublicDto>>;
    getStatistics(): Promise<{
        totalActive: number;
        byLocation: Record<string, number>;
        withMobileImage: number;
        withLink: number;
        canDisplayInCarousel: number;
        clickableBanners: number;
    }>;
    getBannerById(id: string): Promise<CmsBannerPublicDto | null>;
}

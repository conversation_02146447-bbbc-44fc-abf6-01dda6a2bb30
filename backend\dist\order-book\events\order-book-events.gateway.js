"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var OrderBookEventsGateway_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.OrderBookEventsGateway = void 0;
const websockets_1 = require("@nestjs/websockets");
const socket_io_1 = require("socket.io");
const common_1 = require("@nestjs/common");
let OrderBookEventsGateway = OrderBookEventsGateway_1 = class OrderBookEventsGateway {
    server;
    logger = new common_1.Logger(OrderBookEventsGateway_1.name);
    clientsByEntityId = new Map();
    handleConnection(client) {
        this.logger.log(`Client connected: ${client.id}`);
    }
    handleDisconnect(client) {
        this.logger.log(`Client disconnected: ${client.id}`);
        for (const [entityId, clients] of this.clientsByEntityId.entries()) {
            clients.delete(client.id);
            if (clients.size === 0) {
                this.clientsByEntityId.delete(entityId);
            }
        }
    }
    subscribeToEntity(clientId, entityId) {
        let clients = this.clientsByEntityId.get(entityId);
        if (!clients) {
            clients = new Set();
            this.clientsByEntityId.set(entityId, clients);
        }
        clients.add(clientId);
        this.logger.log(`Client ${clientId} subscribed to entity ${entityId}`);
    }
    unsubscribeFromEntity(clientId, entityId) {
        const clients = this.clientsByEntityId.get(entityId);
        if (clients) {
            clients.delete(clientId);
            if (clients.size === 0) {
                this.clientsByEntityId.delete(entityId);
            }
            this.logger.log(`Client ${clientId} unsubscribed from entity ${entityId}`);
        }
    }
    emitEntityCreated(entity) {
        this.server.emit('orderCreated', entity);
        this.logger.log(`Emitted orderCreated event for order ${entity.id}`);
    }
    emitEntityUpdated(entity) {
        this.server.emit('orderUpdated', entity);
        const clients = this.clientsByEntityId.get(entity.id);
        if (clients && clients.size > 0) {
            this.server.to(Array.from(clients)).emit('orderUpdated', entity);
        }
        this.logger.log(`Emitted orderUpdated event for order ${entity.id}`);
    }
    emitStatusToggled(id, status) {
        this.server.emit('orderStatusToggled', { id, status });
        const clients = this.clientsByEntityId.get(id);
        if (clients && clients.size > 0) {
            this.server
                .to(Array.from(clients))
                .emit('orderStatusToggled', { id, status });
        }
        this.logger.log(`Emitted orderStatusToggled event for order ${id} with status ${status}`);
    }
    emitEntityDeleted(id, isSoftDelete) {
        this.server.emit('orderDeleted', { id, isSoftDelete });
        const clients = this.clientsByEntityId.get(id);
        if (clients && clients.size > 0) {
            this.server
                .to(Array.from(clients))
                .emit('orderDeleted', { id, isSoftDelete });
            this.clientsByEntityId.delete(id);
        }
        this.logger.log(`Emitted orderDeleted event for order ${id} (soft delete: ${isSoftDelete})`);
    }
    emitEntityDuplicated(entity) {
        this.server.emit('orderDuplicated', entity);
        this.logger.log(`Emitted orderDuplicated event for order ${entity.id}`);
    }
    emitEntityRestored(entity) {
        this.server.emit('orderRestored', entity);
        this.logger.log(`Emitted orderRestored event for order ${entity.id}`);
    }
};
exports.OrderBookEventsGateway = OrderBookEventsGateway;
__decorate([
    (0, websockets_1.WebSocketServer)(),
    __metadata("design:type", socket_io_1.Server)
], OrderBookEventsGateway.prototype, "server", void 0);
exports.OrderBookEventsGateway = OrderBookEventsGateway = OrderBookEventsGateway_1 = __decorate([
    (0, websockets_1.WebSocketGateway)({
        cors: {
            origin: '*',
        },
        namespace: 'order-book',
    })
], OrderBookEventsGateway);
//# sourceMappingURL=order-book-events.gateway.js.map
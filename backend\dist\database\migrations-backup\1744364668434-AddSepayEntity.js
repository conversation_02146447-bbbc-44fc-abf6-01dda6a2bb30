"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AddSepayEntity1744364668434 = void 0;
class AddSepayEntity1744364668434 {
    async up(queryRunner) {
        await queryRunner.query(`
            CREATE TABLE "sepay_transactions" (
                "id" SERIAL PRIMARY KEY,
                "gateway" VARCHAR(100) NOT NULL,
                "transaction_date" TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
                "account_number" VARCHAR(100) NULL,
                "sub_account" VARCHAR(250) NULL,
                "amount_in" DECIMAL(20,2) NOT NULL DEFAULT 0.00,
                "amount_out" DECIMAL(20,2) NOT NULL DEFAULT 0.00,
                "accumulated" DECIMAL(20,2) NOT NULL DEFAULT 0.00,
                "code" VARCHAR(250) NULL,
                "transaction_content" TEXT NULL,
                "reference_number" VARCHAR(255) NULL,
                "body" TEXT NULL,
                "created_at" TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
                "payment_transaction_id" UUID NULL
            )
        `);
        await queryRunner.query(`
            ALTER TABLE "sepay_transactions" 
            ADD CONSTRAINT "fk_sepay_transactions_payment_transaction" 
            FOREIGN KEY ("payment_transaction_id") 
            REFERENCES "payment_transactions"("id") 
            ON DELETE CASCADE
        `);
        await queryRunner.query(`
            CREATE INDEX "idx_sepay_transactions_payment_transaction_id" 
            ON "sepay_transactions" ("payment_transaction_id")
        `);
    }
    async down(queryRunner) {
        await queryRunner.query(`
            ALTER TABLE "sepay_transactions" 
            DROP CONSTRAINT "fk_sepay_transactions_payment_transaction"
        `);
        await queryRunner.query(`
            DROP INDEX "idx_sepay_transactions_payment_transaction_id"
        `);
        await queryRunner.query(`
            DROP TABLE "sepay_transactions"
        `);
    }
}
exports.AddSepayEntity1744364668434 = AddSepayEntity1744364668434;
//# sourceMappingURL=1744364668434-AddSepayEntity.js.map
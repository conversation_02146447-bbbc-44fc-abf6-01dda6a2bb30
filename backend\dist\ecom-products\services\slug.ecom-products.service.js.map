{"version": 3, "file": "slug.ecom-products.service.js", "sourceRoot": "", "sources": ["../../../src/ecom-products/services/slug.ecom-products.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAAA,2CAA4C;AAC5C,6CAAmD;AACnD,qCAAqC;AACrC,yEAA6D;AAC7D,+EAA0E;AAC1E,qFAAgF;AAOzE,IAAM,uBAAuB,GAA7B,MAAM,uBAAwB,SAAQ,mCAA4B;IAIlD;IAHrB,YAEE,iBAA0C,EACvB,kBAAsC;QAEzD,KAAK,CAAC,iBAAiB,EAAE,kBAAkB,CAAC,CAAC;QAF1B,uBAAkB,GAAlB,kBAAkB,CAAoB;IAG3D,CAAC;IAKS,gBAAgB;QACxB,OAAO,MAAM,CAAC;IAChB,CAAC;IAES,gBAAgB;QACxB,OAAO,aAAa,CAAC;IACvB,CAAC;IAES,kBAAkB,CAAC,SAAkB;QAC7C,OAAO,EAAE,SAAS,EAAE,KAAK,EAAE,CAAC;IAC9B,CAAC;IAES,oBAAoB;QAC5B,OAAO,WAAW,IAAI,CAAC,GAAG,EAAE,EAAE,CAAC;IACjC,CAAC;IAOD,2BAA2B,CAAC,WAAmB;QAC7C,OAAO,IAAI,CAAC,oBAAoB,CAAC,WAAW,CAAC,CAAC;IAChD,CAAC;IAQD,KAAK,CAAC,2BAA2B,CAC/B,WAAmB,EACnB,YAAqB;QAGrB,OAAO,KAAK,CAAC,2BAA2B,CAAC,WAAW,EAAE,YAAY,CAAC,CAAC;IACtE,CAAC;IAUD,KAAK,CAAC,2BAA2B,CAC/B,WAAmB,EACnB,SAAiB,EACjB,YAAqB,EACrB,WAAoB;QAGpB,OAAO,KAAK,CAAC,2BAA2B,CACtC,WAAW,EACX,SAAS,EACT,YAAY,EACZ,WAAW,CACZ,CAAC;IACJ,CAAC;IAQD,KAAK,CAAC,YAAY,CAAC,IAAY,EAAE,SAAkB;QACjD,OAAO,KAAK,CAAC,YAAY,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC;IAC7C,CAAC;IAOD,KAAK,CAAC,6BAA6B,CACjC,QAAwC;QAExC,OAAO,KAAK,CAAC,kBAAkB,CAC7B,QAAQ,EACR,CAAC,OAAO,EAAE,EAAE,CAAC,OAAO,CAAC,WAAW,CACjC,CAAC;IACJ,CAAC;IAQD,0BAA0B,CAAC,IAAY,EAAE,aAAuB;QAC9D,MAAM,QAAQ,GAAG,IAAI,CAAC,2BAA2B,CAAC,IAAI,CAAC,CAAC;QACxD,OAAO,IAAI,CAAC,kBAAkB,CAAC,kBAAkB,CAAC,QAAQ,EAAE,aAAa,CAAC,CAAC;IAC7E,CAAC;IAOD,KAAK,CAAC,gBAAgB,CAAC,SAAkB;QACvC,MAAM,YAAY,GAAG,IAAI,CAAC,UAAU;aACjC,kBAAkB,CAAC,SAAS,CAAC;aAC7B,MAAM,CAAC,cAAc,CAAC;aACtB,KAAK,CAAC,0BAA0B,CAAC;aACjC,QAAQ,CAAC,gCAAgC,EAAE,EAAE,SAAS,EAAE,KAAK,EAAE,CAAC,CAAC;QAEpE,IAAI,SAAS,EAAE,CAAC;YACd,YAAY,CAAC,QAAQ,CAAC,0BAA0B,EAAE,EAAE,SAAS,EAAE,CAAC,CAAC;QACnE,CAAC;QAED,MAAM,QAAQ,GAAG,MAAM,YAAY,CAAC,OAAO,EAAE,CAAC;QAC9C,OAAO,QAAQ;aACZ,GAAG,CAAC,CAAC,OAAoB,EAAE,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC;aAC3C,MAAM,CAAC,CAAC,IAAI,EAAkB,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC;IACrD,CAAC;IAOD,YAAY,CAAC,IAAY;QACvB,OAAO,KAAK,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC;IAClC,CAAC;IAOD,YAAY,CAAC,IAAY;QACvB,OAAO,KAAK,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC;IAClC,CAAC;IAQD,eAAe,CAAC,IAAY,EAAE,YAAqB;QACjD,OAAO,KAAK,CAAC,eAAe,CAAC,IAAI,EAAE,YAAY,CAAC,CAAC;IACnD,CAAC;IAQD,KAAK,CAAC,qBAAqB,CACzB,mBAA2B,EAC3B,SAAiB,MAAM;QAEvB,MAAM,cAAc,GAAG,GAAG,mBAAmB,KAAK,MAAM,GAAG,CAAC;QAC5D,OAAO,IAAI,CAAC,2BAA2B,CAAC,cAAc,CAAC,CAAC;IAC1D,CAAC;IAOD,2BAA2B,CAAC,WAAmB;QAC7C,IAAI,CAAC,WAAW,IAAI,WAAW,CAAC,IAAI,EAAE,KAAK,EAAE,EAAE,CAAC;YAC9C,OAAO,IAAI,CAAC,oBAAoB,EAAE,CAAC;QACrC,CAAC;QAED,MAAM,QAAQ,GAAG,IAAI,CAAC,kBAAkB,CAAC,iBAAiB,CAAC,WAAW,CAAC,CAAC;QAExE,IAAI,CAAC,QAAQ,EAAE,CAAC;YACd,OAAO,IAAI,CAAC,oBAAoB,EAAE,CAAC;QACrC,CAAC;QAED,OAAO,QAAQ,CAAC;IAClB,CAAC;IAQD,iBAAiB,CAAC,WAAoB,EAAE,WAAoB;QAC1D,IAAI,WAAW,IAAI,WAAW,CAAC,IAAI,EAAE,KAAK,EAAE,EAAE,CAAC;YAC7C,OAAO,IAAI,CAAC,2BAA2B,CAAC,WAAW,CAAC,CAAC;QACvD,CAAC;QAED,IAAI,WAAW,IAAI,WAAW,CAAC,IAAI,EAAE,KAAK,EAAE,EAAE,CAAC;YAC7C,OAAO,IAAI,CAAC,2BAA2B,CAAC,WAAW,CAAC,CAAC;QACvD,CAAC;QAED,OAAO,IAAI,CAAC,oBAAoB,EAAE,CAAC;IACrC,CAAC;IAMD,KAAK,CAAC,kBAAkB;QACtB,MAAM,mBAAmB,GAAG,MAAM,IAAI,CAAC,UAAU;aAC9C,kBAAkB,CAAC,SAAS,CAAC;aAC7B,KAAK,CAAC,iDAAiD,EAAE,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC;aACvE,QAAQ,CAAC,gCAAgC,EAAE,EAAE,SAAS,EAAE,KAAK,EAAE,CAAC;aAChE,OAAO,EAAE,CAAC;QAEb,IAAI,mBAAmB,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACrC,OAAO,CAAC,CAAC;QACX,CAAC;QAED,MAAM,aAAa,GAAG,MAAM,IAAI,CAAC,gBAAgB,EAAE,CAAC;QACpD,IAAI,YAAY,GAAG,CAAC,CAAC;QAErB,KAAK,MAAM,OAAO,IAAI,mBAAmB,EAAE,CAAC;YAC1C,MAAM,OAAO,GAAG,IAAI,CAAC,kBAAkB,CAAC,kBAAkB,CACxD,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,WAAW,EAAE,OAAO,CAAC,WAAW,CAAC,EAChE,CAAC,GAAG,aAAa,EAAE,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE,MAAM,EAAE,YAAY,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,CACnF,CAAC;YAEF,MAAM,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,OAAO,CAAC,EAAE,EAAE,EAAE,IAAI,EAAE,OAAO,EAAE,CAAC,CAAC;YAC5D,aAAa,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;YAC5B,YAAY,EAAE,CAAC;QACjB,CAAC;QAED,OAAO,YAAY,CAAC;IACtB,CAAC;CACF,CAAA;AAjPY,0DAAuB;kCAAvB,uBAAuB;IADnC,IAAA,mBAAU,GAAE;IAGR,WAAA,IAAA,0BAAgB,EAAC,kCAAW,CAAC,CAAA;qCACX,oBAAU;QACU,yCAAkB;GAJhD,uBAAuB,CAiPnC"}
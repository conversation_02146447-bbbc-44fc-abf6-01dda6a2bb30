/**
 * Script để xóa toàn bộ ngân hàng và import lại full danh sách từ banklookup.net
 */

const axios = require('axios');
const fs = require('fs');

// C<PERSON>u hình
const CONFIG = {
  BANK_API_URL: 'https://api.banklookup.net/bank/list',
  BACKEND_URL: 'http://localhost:3168',
  ADMIN_CREDENTIALS: {
    identity: '<EMAIL>',
    password: 'adminX@123'
  },
  OUTPUT_DIR: './scripts/output',
  LOG_FILE: './scripts/output/reset-import-banks.log',
  
  // Cấu hình batch processing
  BATCH_SIZE: 10,           // Số ngân hàng tạo mỗi lần
  DELAY_BETWEEN_BATCHES: 1000, // Delay giữa các batch (ms)
  MAX_LIMIT: 100,           // Limit tối đa cho API GET
};

// Utility functions
const log = (message, type = 'INFO') => {
  const timestamp = new Date().toISOString();
  const logMessage = `[${timestamp}] [${type}] ${message}`;
  console.log(logMessage);
  
  if (!fs.existsSync(CONFIG.OUTPUT_DIR)) {
    fs.mkdirSync(CONFIG.OUTPUT_DIR, { recursive: true });
  }
  fs.appendFileSync(CONFIG.LOG_FILE, logMessage + '\n');
};

const sleep = (ms) => new Promise(resolve => setTimeout(resolve, ms));

// Đăng nhập và lấy token
async function login() {
  try {
    log('Đang đăng nhập...');
    const response = await axios.post(`${CONFIG.BACKEND_URL}/api/v1/auth/login`, CONFIG.ADMIN_CREDENTIALS);
    
    let accessToken = null;
    if (response.data?.data?.access_token) {
      accessToken = response.data.data.access_token;
    } else if (response.data?.access_token) {
      accessToken = response.data.access_token;
    }
    
    if (accessToken) {
      log('Đăng nhập thành công');
      return accessToken;
    } else {
      throw new Error('Không nhận được access token');
    }
  } catch (error) {
    log(`Lỗi đăng nhập: ${error.message}`, 'ERROR');
    throw error;
  }
}

// Lấy tất cả ngân hàng hiện có (với pagination)
async function getAllExistingBanks(token) {
  try {
    log('Đang lấy danh sách ngân hàng hiện có...');

    let allBanks = [];
    let page = 1;
    let hasMore = true;
    let maxPages = 20; // Giới hạn tối đa 20 trang để tránh vô hạn

    while (hasMore && page <= maxPages) {
      try {
        const response = await axios.get(`${CONFIG.BACKEND_URL}/api/v1/banks`, {
          params: {
            page: page,
            limit: CONFIG.MAX_LIMIT, // Sử dụng limit tối đa cho phép
          },
          headers: {
            'Authorization': `Bearer ${token}`
          }
        });

        // Debug response structure
        log(`Response structure for page ${page}: ${JSON.stringify({
          status: response.status,
          dataType: typeof response.data,
          dataKeys: Object.keys(response.data || {}),
          dataLength: response.data?.data?.length || 'N/A'
        })}`);

        const banks = response.data?.data || [];

        if (Array.isArray(banks)) {
          allBanks = allBanks.concat(banks);
          log(`Lấy được ${banks.length} ngân hàng từ trang ${page}`);

          // Kiểm tra có trang tiếp theo không
          if (banks.length < CONFIG.MAX_LIMIT || banks.length === 0) {
            hasMore = false;
            log(`Đã hết dữ liệu tại trang ${page}`);
          } else {
            page++;
            await sleep(500); // Delay nhỏ giữa các request
          }
        } else {
          log(`Dữ liệu không phải array tại trang ${page}: ${typeof banks}`, 'ERROR');
          hasMore = false;
        }

      } catch (pageError) {
        log(`Lỗi tại trang ${page}: ${pageError.message}`, 'ERROR');
        if (pageError.response) {
          log(`Page ${page} response: ${JSON.stringify(pageError.response.data)}`, 'ERROR');
        }
        hasMore = false;
      }
    }

    if (page > maxPages) {
      log(`Đã đạt giới hạn tối đa ${maxPages} trang`, 'ERROR');
    }

    log(`Tổng cộng có ${allBanks.length} ngân hàng hiện có`);
    return allBanks;

  } catch (error) {
    log(`Lỗi khi lấy danh sách ngân hàng: ${error.message}`, 'ERROR');
    if (error.response) {
      log(`Response: ${JSON.stringify(error.response.data)}`, 'ERROR');
    }
    throw error;
  }
}

// Xóa tất cả ngân hàng (bulk delete)
async function deleteAllBanks(token, banks) {
  try {
    if (banks.length === 0) {
      log('Không có ngân hàng nào để xóa');
      return;
    }
    
    log(`Đang xóa ${banks.length} ngân hàng...`);
    
    // Lấy danh sách ID
    const bankIds = banks.map(bank => bank.id);
    
    // Chia thành các batch để tránh timeout
    const batchSize = 50; // Xóa tối đa 50 ngân hàng mỗi lần
    
    for (let i = 0; i < bankIds.length; i += batchSize) {
      const batch = bankIds.slice(i, i + batchSize);
      
      log(`Đang xóa batch ${Math.floor(i/batchSize) + 1}: ${batch.length} ngân hàng`);
      
      await axios.post(`${CONFIG.BACKEND_URL}/api/v1/banks/bulk-delete`, batch, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });
      
      log(`Đã xóa thành công batch ${Math.floor(i/batchSize) + 1}`);
      
      // Delay giữa các batch
      if (i + batchSize < bankIds.length) {
        await sleep(1000);
      }
    }
    
    log(`Đã xóa thành công tất cả ${banks.length} ngân hàng`);
    
  } catch (error) {
    log(`Lỗi khi xóa ngân hàng: ${error.message}`, 'ERROR');
    if (error.response) {
      log(`Response: ${JSON.stringify(error.response.data)}`, 'ERROR');
    }
    throw error;
  }
}

// Lấy dữ liệu từ banklookup.net
async function fetchBankData() {
  try {
    log('Đang lấy dữ liệu từ API banklookup.net...');
    const response = await axios.get(CONFIG.BANK_API_URL);
    
    if (response.data.success && response.data.data) {
      log(`Lấy thành công ${response.data.data.length} ngân hàng từ API`);
      return response.data.data;
    } else {
      throw new Error('API trả về dữ liệu không hợp lệ');
    }
  } catch (error) {
    log(`Lỗi khi lấy dữ liệu từ API: ${error.message}`, 'ERROR');
    throw error;
  }
}

// Chuyển đổi dữ liệu
function transformBankData(apiData) {
  return apiData.map(bank => ({
    brandName: bank.short_name || bank.name,
    fullName: bank.name,
    shortName: bank.short_name || bank.code,
    code: bank.code,
    bin: bank.bin.toString(),
    logoPath: bank.logo_url || null,
    icon: bank.icon_url || null,
    isActive: bank.lookup_supported === 1
  }));
}

// Import ngân hàng theo batch
async function importBanksInBatches(token, banks) {
  try {
    log(`Đang import ${banks.length} ngân hàng theo batch...`);
    
    let successCount = 0;
    let errorCount = 0;
    
    // Chia thành các batch
    for (let i = 0; i < banks.length; i += CONFIG.BATCH_SIZE) {
      const batch = banks.slice(i, i + CONFIG.BATCH_SIZE);
      const batchNumber = Math.floor(i / CONFIG.BATCH_SIZE) + 1;
      const totalBatches = Math.ceil(banks.length / CONFIG.BATCH_SIZE);
      
      log(`Đang xử lý batch ${batchNumber}/${totalBatches}: ${batch.length} ngân hàng`);
      
      try {
        const response = await axios.post(`${CONFIG.BACKEND_URL}/api/v1/banks/bulk`, batch, {
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
          }
        });
        
        const createdCount = response.data?.length || batch.length;
        successCount += createdCount;
        log(`✅ Batch ${batchNumber}: Tạo thành công ${createdCount} ngân hàng`);
        
      } catch (error) {
        errorCount += batch.length;
        log(`❌ Batch ${batchNumber}: Lỗi - ${error.message}`, 'ERROR');
        if (error.response) {
          log(`Response: ${JSON.stringify(error.response.data)}`, 'ERROR');
        }
      }
      
      // Delay giữa các batch
      if (i + CONFIG.BATCH_SIZE < banks.length) {
        await sleep(CONFIG.DELAY_BETWEEN_BATCHES);
      }
    }
    
    log(`=== KẾT QUẢ IMPORT ===`);
    log(`✅ Thành công: ${successCount} ngân hàng`);
    log(`❌ Thất bại: ${errorCount} ngân hàng`);
    log(`📊 Tỷ lệ thành công: ${((successCount / banks.length) * 100).toFixed(2)}%`);
    
    return { successCount, errorCount };
    
  } catch (error) {
    log(`Lỗi trong quá trình import: ${error.message}`, 'ERROR');
    throw error;
  }
}

// Script chính
async function resetAndImportBanks() {
  try {
    log('=== BẮT ĐẦU RESET VÀ IMPORT NGÂN HÀNG ===');
    
    // 1. Đăng nhập
    const token = await login();
    
    // 2. Lấy danh sách ngân hàng hiện có
    log('\n=== BƯỚC 1: LẤY DANH SÁCH NGÂN HÀNG HIỆN CÓ ===');
    const existingBanks = await getAllExistingBanks(token);
    
    // 3. Xóa tất cả ngân hàng hiện có
    log('\n=== BƯỚC 2: XÓA TẤT CẢ NGÂN HÀNG HIỆN CÓ ===');
    await deleteAllBanks(token, existingBanks);
    
    // 4. Lấy dữ liệu mới từ API
    log('\n=== BƯỚC 3: LẤY DỮ LIỆU MỚI TỪ API ===');
    const rawBankData = await fetchBankData();
    
    // 5. Chuyển đổi dữ liệu
    log('\n=== BƯỚC 4: CHUYỂN ĐỔI DỮ LIỆU ===');
    const transformedData = transformBankData(rawBankData);
    
    // Lưu dữ liệu đã chuyển đổi
    const dataFile = `${CONFIG.OUTPUT_DIR}/banks-full-data.json`;
    fs.writeFileSync(dataFile, JSON.stringify(transformedData, null, 2));
    log(`Đã lưu dữ liệu vào ${dataFile}`);
    
    // 6. Import dữ liệu mới
    log('\n=== BƯỚC 5: IMPORT DỮ LIỆU MỚI ===');
    const importResult = await importBanksInBatches(token, transformedData);
    
    // 7. Thống kê cuối cùng
    log('\n=== THỐNG KÊ CUỐI CÙNG ===');
    log(`📊 Ngân hàng đã xóa: ${existingBanks.length}`);
    log(`📊 Ngân hàng từ API: ${rawBankData.length}`);
    log(`📊 Ngân hàng import thành công: ${importResult.successCount}`);
    log(`📊 Ngân hàng import thất bại: ${importResult.errorCount}`);
    
    log('\n=== HOÀN THÀNH RESET VÀ IMPORT ===');
    
  } catch (error) {
    log(`❌ Lỗi trong quá trình reset và import: ${error.message}`, 'ERROR');
    process.exit(1);
  }
}

// Chạy script
if (require.main === module) {
  resetAndImportBanks().catch(error => {
    log(`❌ Lỗi không mong đợi: ${error.message}`, 'ERROR');
    process.exit(1);
  });
}

module.exports = {
  resetAndImportBanks,
  getAllExistingBanks,
  deleteAllBanks,
  importBanksInBatches
};

[{"brandName": "ABBANK", "fullName": "<PERSON><PERSON> hàng TMCP An Bình", "shortName": "ABBANK", "code": "ABB", "bin": "970425", "logoPath": "https://api.vietqr.io/img/ABB.png", "icon": "https://cdn.banklookup.net/assets/images/bank-icons/ABB.svg", "isActive": true, "metadata": {"id": "2d71f051-72fa-48bd-a362-27a08e8df3b9", "swiftCode": "ABBKVNVX", "lookupSupported": 1}}, {"brandName": "ACB", "fullName": "<PERSON>ân hàng TMCP Á Châu", "shortName": "ACB", "code": "ACB", "bin": "970416", "logoPath": "https://api.vietqr.io/img/ACB.png", "icon": "https://cdn.banklookup.net/assets/images/bank-icons/ACB.svg", "isActive": true, "metadata": {"id": "5b8bb807-6d1c-485b-986e-ad0fb2a6d4d2", "swiftCode": "ASCBVNVX", "lookupSupported": 1}}, {"brandName": "BacABank", "fullName": "<PERSON><PERSON> h<PERSON>ng TMCP Bắc Á", "shortName": "BacABank", "code": "BAB", "bin": "970409", "logoPath": "https://api.vietqr.io/img/BAB.png", "icon": "https://cdn.banklookup.net/assets/images/bank-icons/BAB.svg", "isActive": true, "metadata": {"id": "f599edf9-40c8-4bbf-87e8-230c1787a439", "swiftCode": "NASCVNVX", "lookupSupported": 1}}, {"brandName": "BIDV", "fullName": "<PERSON><PERSON> hàng TMCP <PERSON><PERSON><PERSON> tư và Phát triển Việt Nam", "shortName": "BIDV", "code": "BIDV", "bin": "970418", "logoPath": "https://api.vietqr.io/img/BIDV.png", "icon": "https://cdn.banklookup.net/assets/images/bank-icons/BIDV.svg", "isActive": true, "metadata": {"id": "f956e517-d4c1-43b1-bbbb-277bdc5f9037", "swiftCode": "BIDVVNVX", "lookupSupported": 1}}, {"brandName": "BaoVietBank", "fullName": "<PERSON><PERSON> h<PERSON>ng TMCP B<PERSON>o <PERSON>", "shortName": "BaoVietBank", "code": "BVB", "bin": "970438", "logoPath": "https://api.vietqr.io/img/BVB.png", "icon": "https://cdn.banklookup.net/assets/images/bank-icons/BVB.svg", "isActive": true, "metadata": {"id": "a4f2d7b2-7b85-4420-8273-3c5d0a69e8f1", "swiftCode": "BVBVVNVX", "lookupSupported": 1}}, {"brandName": "CAKE", "fullName": "TMCP Việt Nam Thịnh Vượng - <PERSON><PERSON> hàng số CAKE by VPBank", "shortName": "CAKE", "code": "CAKE", "bin": "546034", "logoPath": "https://api.vietqr.io/img/CAKE.png", "icon": "https://cdn.banklookup.net/assets/images/bank-icons/CAKE.svg", "isActive": true, "metadata": {"id": "66fa0378-a568-4ca0-b958-deb03de55ab4", "swiftCode": null, "lookupSupported": 1}}, {"brandName": "CBBank", "fullName": "<PERSON><PERSON> hàng Thương mại TNHH MTV Xây dựng Việt Nam", "shortName": "CBBank", "code": "CBB", "bin": "970444", "logoPath": "https://api.vietqr.io/img/CBB.png", "icon": "https://cdn.banklookup.net/assets/images/bank-icons/CBB.svg", "isActive": true, "metadata": {"id": "78d48899-8cf5-48d7-8572-645f43b0880d", "swiftCode": "GTBAVNVX", "lookupSupported": 1}}, {"brandName": "CIMB", "fullName": "<PERSON><PERSON> hàng TNHH MTV CIMB Việt Nam", "shortName": "CIMB", "code": "CIMB", "bin": "422589", "logoPath": "https://api.vietqr.io/img/CIMB.png", "icon": "https://cdn.banklookup.net/assets/images/bank-icons/CIMB.svg", "isActive": true, "metadata": {"id": "d4e933a0-e11e-470b-9634-30638e4dfa66", "swiftCode": "CIBBVNVN", "lookupSupported": 1}}, {"brandName": "Co-op Bank", "fullName": "<PERSON><PERSON> h<PERSON> tác xã Việt <PERSON>", "shortName": "Co-op Bank", "code": "COOPB", "bin": "970446", "logoPath": "https://api.vietqr.io/img/COOPBANK.png", "icon": "https://cdn.banklookup.net/assets/images/bank-icons/COOPBANK.svg", "isActive": true, "metadata": {"id": "1fe21f42-9b91-418c-a364-6e3190365009", "swiftCode": null, "lookupSupported": 1}}, {"brandName": "VikkiBank", "fullName": "<PERSON><PERSON> hàng TNHH MTV Số Vikki", "shortName": "VikkiBank", "code": "DAB", "bin": "970406", "logoPath": "https://ebanking.vikkibank.vn/khcn/resources/images/common/logo.png", "icon": "https://cdn.banklookup.net/assets/images/bank-icons/DOB.svg", "isActive": true, "metadata": {"id": "dc6920cb-8d1f-4393-a01d-db53d4b0e289", "swiftCode": "EACBVNVX", "lookupSupported": 1}}]
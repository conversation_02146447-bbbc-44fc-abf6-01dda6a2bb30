import { OrderBook } from './order-book.entity';
import { BaseEntity } from '../../common/entities/base.entity';
import { EcomProduct } from '../../ecom-products/entity/ecom-products.entity';
export declare class OrderBookDetail extends BaseEntity {
    orderBookId: string;
    productId: string;
    price: number;
    quantity: number;
    totalPrice: number;
    orderBook: OrderBook;
    product: EcomProduct;
    getEntityName(): string;
}

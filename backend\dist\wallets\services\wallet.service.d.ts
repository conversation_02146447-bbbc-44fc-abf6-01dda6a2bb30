import { UpdateWalletService } from './update.wallet.service';
import { ReadWalletService } from './read.wallet.service';
export declare class WalletService {
    private readonly updateWalletService;
    private readonly readWalletService;
    constructor(updateWalletService: UpdateWalletService, readWalletService: ReadWalletService);
    addBalance(userId: number, amount: number, metadata?: any): Promise<void>;
    subtractBalance(userId: number, amount: number, metadata?: any): Promise<void>;
    getWallet(userId: number): Promise<any>;
}

import { Repository, DataSource } from 'typeorm';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { BaseCmsBannersService } from './base.cms-banners.service';
import { CmsBanners } from '../entity/cms-banners.entity';
import { CreateCmsBannerDto } from '../dto/create.cms-banner.dto';
import { CmsBannerDto } from '../dto/cms-banner.dto';
export declare class CreateCmsBannersService extends BaseCmsBannersService {
    protected readonly bannerRepository: Repository<CmsBanners>;
    protected readonly dataSource: DataSource;
    protected readonly eventEmitter: EventEmitter2;
    constructor(bannerRepository: Repository<CmsBanners>, dataSource: DataSource, eventEmitter: EventEmitter2);
    create(createDto: CreateCmsBannerDto, userId: string): Promise<CmsBannerDto>;
    bulkCreate(createDtos: CreateCmsBannerDto[], userId: string): Promise<CmsBannerDto[]>;
    duplicate(id: string, userId: string, newTitle?: string): Promise<CmsBannerDto>;
    createFromTemplate(templateData: any, userId: string): Promise<CmsBannerDto>;
}

import { UpdateCmsMenusService } from '../services/update.cms-menus.service';
import { UpdateCmsMenuDto } from '../dto/update.cms-menu.dto';
import { CmsMenuDto } from '../dto/cms-menu.dto';
import { CmsMenuStatus } from '../entity/cms-menus.entity';
export declare class UpdateCmsMenusController {
    private readonly cmsMenusService;
    constructor(cmsMenusService: UpdateCmsMenusService);
    update(id: string, updateCmsMenuDto: UpdateCmsMenuDto, userId: string): Promise<CmsMenuDto | null>;
    updateStatus(id: string, status: CmsMenuStatus, userId: string): Promise<CmsMenuDto | null>;
    updateSlugFromName(id: string, userId: string): Promise<CmsMenuDto | null>;
}

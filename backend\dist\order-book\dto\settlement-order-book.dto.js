"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SettlementOrderBookDto = void 0;
const openapi = require("@nestjs/swagger");
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
class SettlementOrderBookDto {
    userId;
    price;
    isImmediateDelivery;
    static _OPENAPI_METADATA_FACTORY() {
        return { userId: { required: false, type: () => String }, price: { required: true, type: () => Number }, isImmediateDelivery: { required: false, type: () => Boolean } };
    }
}
exports.SettlementOrderBookDto = SettlementOrderBookDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'ID của người dùng',
        example: '123',
        required: true,
    }),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], SettlementOrderBookDto.prototype, "userId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Giá tất toán mới',
        example: 5000000,
        required: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], SettlementOrderBookDto.prototype, "price", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Có giao ngay không? true/false',
        example: true,
        required: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], SettlementOrderBookDto.prototype, "isImmediateDelivery", void 0);
//# sourceMappingURL=settlement-order-book.dto.js.map
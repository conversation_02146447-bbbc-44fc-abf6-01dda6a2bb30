{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/bloomgoo/gold-exchange-platform/frontend/components/nav-main.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport Link from \"next/link\"\r\n\r\nimport {\r\n  SidebarGroup,\r\n  SidebarGroupContent,\r\n  SidebarMenu,\r\n  SidebarMenuButton,\r\n  SidebarMenuItem,\r\n} from \"@/components/ui/sidebar\"\r\n\r\nexport function NavMain({\r\n  items,\r\n}: {\r\n  items: any[]\r\n}) {\r\n\r\n  return (\r\n    <SidebarGroup>\r\n      <SidebarGroupContent className=\"flex flex-col gap-2\">\r\n        <SidebarMenu>\r\n          {items.map((item, index) => {\r\n            if ('type' in item && item.type === 'separator') {\r\n              return (\r\n                <SidebarMenuItem key={`separator-${index}`} className=\"mt-2 mb-1\">\r\n                  {/* Separator khi expanded - hiển thị text đầy đủ */}\r\n                  <div className=\"group-data-[collapsible=icon]:hidden\">\r\n                    <p className=\"px-2 text-xs font-semibold text-muted-foreground uppercase tracking-wider\">\r\n                      {item.title}\r\n                    </p>\r\n                  </div>\r\n\r\n                  {/* <PERSON>arator khi collapsed - hiển thị divider với tooltip */}\r\n                  <div className=\"hidden group-data-[collapsible=icon]:block\">\r\n                    <SidebarMenuButton\r\n                      tooltip={{\r\n                        children: item.title,\r\n                        side: \"right\",\r\n                        align: \"center\"\r\n                      }}\r\n                      className=\"h-6 justify-center cursor-default hover:bg-transparent focus:bg-transparent active:bg-transparent pointer-events-auto\"\r\n                      onClick={(e) => e.preventDefault()}\r\n                    >\r\n                      <div className=\"w-4 h-px bg-sidebar-border pointer-events-none\" />\r\n                    </SidebarMenuButton>\r\n                  </div>\r\n                </SidebarMenuItem>\r\n              );\r\n            }\r\n\r\n            // Tạo key unique bằng cách kết hợp key có sẵn, url và index\r\n            const uniqueKey = item.key || item.url || `${item.title}-${index}`;\r\n\r\n            return (\r\n              <SidebarMenuItem key={uniqueKey}>\r\n                <SidebarMenuButton asChild tooltip={item.title}>\r\n                  <Link href={item.url}>\r\n                    {item.icon && <item.icon />}\r\n                    <span>{item.title}</span>\r\n                  </Link>\r\n                </SidebarMenuButton>\r\n              </SidebarMenuItem>\r\n            );\r\n          })}\r\n        </SidebarMenu>\r\n      </SidebarGroupContent>\r\n    </SidebarGroup>\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AAEA;AAJA;;;;AAYO,SAAS,QAAQ,EACtB,KAAK,EAGN;IAEC,qBACE,sSAAC,+HAAA,CAAA,eAAY;kBACX,cAAA,sSAAC,+HAAA,CAAA,sBAAmB;YAAC,WAAU;sBAC7B,cAAA,sSAAC,+HAAA,CAAA,cAAW;0BACT,MAAM,GAAG,CAAC,CAAC,MAAM;oBAChB,IAAI,UAAU,QAAQ,KAAK,IAAI,KAAK,aAAa;wBAC/C,qBACE,sSAAC,+HAAA,CAAA,kBAAe;4BAA4B,WAAU;;8CAEpD,sSAAC;oCAAI,WAAU;8CACb,cAAA,sSAAC;wCAAE,WAAU;kDACV,KAAK,KAAK;;;;;;;;;;;8CAKf,sSAAC;oCAAI,WAAU;8CACb,cAAA,sSAAC,+HAAA,CAAA,oBAAiB;wCAChB,SAAS;4CACP,UAAU,KAAK,KAAK;4CACpB,MAAM;4CACN,OAAO;wCACT;wCACA,WAAU;wCACV,SAAS,CAAC,IAAM,EAAE,cAAc;kDAEhC,cAAA,sSAAC;4CAAI,WAAU;;;;;;;;;;;;;;;;;2BAnBC,CAAC,UAAU,EAAE,OAAO;;;;;oBAwB9C;oBAEA,4DAA4D;oBAC5D,MAAM,YAAY,KAAK,GAAG,IAAI,KAAK,GAAG,IAAI,GAAG,KAAK,KAAK,CAAC,CAAC,EAAE,OAAO;oBAElE,qBACE,sSAAC,+HAAA,CAAA,kBAAe;kCACd,cAAA,sSAAC,+HAAA,CAAA,oBAAiB;4BAAC,OAAO;4BAAC,SAAS,KAAK,KAAK;sCAC5C,cAAA,sSAAC,wQAAA,CAAA,UAAI;gCAAC,MAAM,KAAK,GAAG;;oCACjB,KAAK,IAAI,kBAAI,sSAAC,KAAK,IAAI;;;;;kDACxB,sSAAC;kDAAM,KAAK,KAAK;;;;;;;;;;;;;;;;;uBAJD;;;;;gBAS1B;;;;;;;;;;;;;;;;AAKV;KAzDgB", "debugId": null}}, {"offset": {"line": 142, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/bloomgoo/gold-exchange-platform/frontend/components/nav-secondary.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport { type Icon } from \"@tabler/icons-react\"\r\n\r\nimport {\r\n  SidebarGroup,\r\n  SidebarGroupContent,\r\n  SidebarMenu,\r\n  SidebarMenuButton,\r\n  SidebarMenuItem,\r\n} from \"@/components/ui/sidebar\"\r\n\r\nexport function NavSecondary({\r\n  items,\r\n  ...props\r\n}: {\r\n  items: {\r\n    title: string\r\n    url: string\r\n    icon: Icon\r\n  }[]\r\n} & React.ComponentPropsWithoutRef<typeof SidebarGroup>) {\r\n  return (\r\n    <SidebarGroup {...props}>\r\n      <SidebarGroupContent>\r\n        <SidebarMenu>\r\n          {items.map((item) => (\r\n            <SidebarMenuItem key={item.title}>\r\n              <SidebarMenuButton asChild>\r\n                <a href={item.url}>\r\n                  <item.icon />\r\n                  <span>{item.title}</span>\r\n                </a>\r\n              </SidebarMenuButton>\r\n            </SidebarMenuItem>\r\n          ))}\r\n        </SidebarMenu>\r\n      </SidebarGroupContent>\r\n    </SidebarGroup>\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;AAKA;AALA;;;AAaO,SAAS,aAAa,EAC3B,KAAK,EACL,GAAG,OAOkD;IACrD,qBACE,sSAAC,+HAAA,CAAA,eAAY;QAAE,GAAG,KAAK;kBACrB,cAAA,sSAAC,+HAAA,CAAA,sBAAmB;sBAClB,cAAA,sSAAC,+HAAA,CAAA,cAAW;0BACT,MAAM,GAAG,CAAC,CAAC,qBACV,sSAAC,+HAAA,CAAA,kBAAe;kCACd,cAAA,sSAAC,+HAAA,CAAA,oBAAiB;4BAAC,OAAO;sCACxB,cAAA,sSAAC;gCAAE,MAAM,KAAK,GAAG;;kDACf,sSAAC,KAAK,IAAI;;;;;kDACV,sSAAC;kDAAM,KAAK,KAAK;;;;;;;;;;;;;;;;;uBAJD,KAAK,KAAK;;;;;;;;;;;;;;;;;;;;AAa5C;KA5BgB", "debugId": null}}, {"offset": {"line": 217, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/bloomgoo/gold-exchange-platform/frontend/components/app-sidebar.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport { useAuth } from \"@/hooks/use-auth\"\r\nimport {\r\n  IconBuildingBank,\r\n  IconCash,\r\n  IconCategory,\r\n  IconChartCandle,\r\n  IconCoin,\r\n  IconCreditCard,\r\n  IconCurrencyDollar,\r\n  IconDashboard,\r\n  IconDeviceAnalytics,\r\n  IconExchange,\r\n  IconFileAnalytics,\r\n  IconFileDescription,\r\n  IconFolders,\r\n  IconGift,\r\n  IconHistory,\r\n  IconMoneybag,\r\n  IconPackage,\r\n  IconPackageImport,\r\n  IconReceipt,\r\n  IconRobot,\r\n  IconSettings,\r\n  IconShieldLock,\r\n  IconShoppingCart,\r\n  IconUser,\r\n  IconUserCheck,\r\n  IconUserCircle,\r\n  IconUserCog,\r\n  IconUserStar,\r\n  IconWallet,\r\n  IconNews,\r\n  IconTags,\r\n  IconPhoto,\r\n  IconMessageCircle,\r\n  IconBuilding,\r\n  IconBrandPagekit,\r\n  IconHeartHandshake,\r\n  IconWorld\r\n} from \"@tabler/icons-react\"\r\nimport Link from \"next/link\"\r\nimport * as React from \"react\"\r\n\r\nimport { NavMain } from \"@/components/nav-main\"\r\nimport { NavSecondary } from \"@/components/nav-secondary\"\r\nimport { NavUser } from \"@/components/nav-user\"\r\nimport {\r\n  Sidebar,\r\n  SidebarContent,\r\n  SidebarFooter,\r\n  SidebarHeader,\r\n  SidebarMenu,\r\n  SidebarMenuButton,\r\n  SidebarMenuItem,\r\n} from \"@/components/ui/sidebar\"\r\nimport { Avatar, AvatarFallback, AvatarImage } from \"./ui/avatar\"\r\nimport { useSiteMetadata } from \"@/hooks/use-site-metadata\"\r\n\r\n// Dữ liệu menu cho USER\r\n\r\nconst userNavData = {\r\n  navMain: [\r\n    {\r\n      key: \"user-dashboard\",\r\n      title: \"Bảng quản trị\",\r\n      url: \"/reports\",\r\n      // url: \"/dashboard\",\r\n      icon: IconDashboard,\r\n    },\r\n    // {\r\n    //   title: \"Báo cáo thống kê\",\r\n    //   url: \"/reports\",\r\n    //   icon: IconFileAnalytics,\r\n    // },\r\n    {\r\n      type: \"separator\",\r\n      title: \"QUẢN TRỊ BẠC TRỰC TUYẾN\",\r\n    },\r\n    {\r\n      key: \"user-trading\",\r\n      title: \"Giao dịch mua bán\",\r\n      url: \"/trading\",\r\n      icon: IconChartCandle,\r\n    },\r\n    {\r\n      key: \"user-order-books\",\r\n      title: \"Lịch sử giao dịch\",\r\n      url: \"/order-books\",\r\n      icon: IconReceipt,\r\n    },\r\n    {\r\n      key: \"user-token-assets\",\r\n      title: \"Tài sản\",\r\n      url: \"/token-assets\",\r\n      icon: IconCoin,\r\n    },\r\n    {\r\n      type: \"separator\",\r\n      title: \"QUẢN TRỊ SẢN PHẨM BẠC\",\r\n    },\r\n    {\r\n      key: \"user-ecom-products\",\r\n      title: \"Sản phẩm\",\r\n      url: \"/ecom-products\",\r\n      icon: IconPackageImport,\r\n    },\r\n    {\r\n      key: \"user-ecom-orders\",\r\n      title: \"Đơn hàng\",\r\n      url: \"/ecom-orders\",\r\n      icon: IconShoppingCart,\r\n    },\r\n    {\r\n      type: \"separator\",\r\n      title: \"QUẢN TRỊ TÀI CHÍNH\",\r\n    },\r\n    {\r\n      key: \"transactions\",\r\n      title: \"Lịch sử thanh toán\",\r\n      url: \"/transactions\",\r\n      icon: IconCurrencyDollar,\r\n    },\r\n    {\r\n      type: \"separator\",\r\n      title: \"QUẢN TRỊ THÔNG TIN CÁ NHÂN\",\r\n    },\r\n    {\r\n      key: \"user-profile\",\r\n      title: \"Hồ sơ cá nhân\",\r\n      url: \"/profile\",\r\n      icon: IconUser,\r\n    },\r\n    {\r\n      key: \"user-kyc\",\r\n      title: \"Xác thực danh tính\",\r\n      url: \"/kyc\",\r\n      icon: IconUserCheck,\r\n    },\r\n  ],\r\n};\r\n\r\n// Dữ liệu menu cho ADMIN\r\nconst adminNavData = {\r\n  navMain: [\r\n    {\r\n      key: \"admin-dashboard\",\r\n      title: \"Bảng quản trị\",\r\n      url: \"/admin/dashboard\",\r\n      icon: IconDashboard,\r\n    },\r\n    {\r\n      key: \"admin-reports\",\r\n      title: \"Báo cáo thống kê\",\r\n      url: \"/admin/reports\",\r\n      icon: IconDeviceAnalytics,\r\n    },\r\n    {\r\n      type: \"separator\",\r\n      title: \"QUẢN TRỊ BẠC TRỰC TUYẾN\",\r\n    },\r\n    {\r\n      key: \"admin-order-books\",\r\n      title: \"Giao dịch online\",\r\n      url: \"/admin/order-books\",\r\n      icon: IconExchange,\r\n    },\r\n    {\r\n      key: \"admin-token-assets\",\r\n      title: \"Quản lý tài sản\",\r\n      url: \"/admin/token-assets\",\r\n      icon: IconMoneybag,\r\n    },\r\n    {\r\n      key: \"tokens\",\r\n      title: \"Quản lý sản phẩm\",\r\n      url: \"/admin/tokens\",\r\n      icon: IconPackage,\r\n    },\r\n    {\r\n      key: \"token-categories\",\r\n      title: \"Danh mục sản phẩm\",\r\n      url: \"/admin/token-categories\",\r\n      icon: IconCategory,\r\n    },\r\n    {\r\n      type: \"separator\",\r\n      title: \"QUẢN TRỊ SẢN PHẨM BẠC\",\r\n    },\r\n    {\r\n      key: \"admin-ecom-orders\",\r\n      title: \"Quản lý đơn hàng\",\r\n      url: \"/admin/ecom-orders\",\r\n      icon: IconShoppingCart,\r\n    },\r\n    {\r\n      key: \"ecom-products\",\r\n      title: \"Quản lý sản phẩm\",\r\n      url: \"/admin/ecom-products\",\r\n      icon: IconPackageImport,\r\n    },\r\n    {\r\n      key: \"ecom-product-categories\",\r\n      title: \"Danh mục sản phẩm\",\r\n      url: \"/admin/ecom-product-categories\",\r\n      icon: IconFolders,\r\n    },\r\n    {\r\n      type: \"separator\",\r\n      title: \"QUẢN TRỊ TÀI CHÍNH\",\r\n    },\r\n    {\r\n      key: \"admin-wallets\",\r\n      title: \"Quản lý tài khoản\",\r\n      url: \"/admin/wallets\",\r\n      icon: IconWallet,\r\n    },\r\n    {\r\n      key: \"admin-transactions\",\r\n      title: \"Lịch sử thanh toán\",\r\n      url: \"/admin/transactions\",\r\n      icon: IconCurrencyDollar,\r\n    },\r\n    {\r\n      key: \"admin-payment-methods\",\r\n      title: \"Phương thức thanh toán\",\r\n      url: \"/admin/payment-methods\",\r\n      icon: IconCreditCard,\r\n    },\r\n    {\r\n      key: \"admin-banks\",\r\n      title: \"Quản lý ngân hàng\",\r\n      url: \"/admin/banks\",\r\n      icon: IconBuildingBank,\r\n    },\r\n    {\r\n      type: \"separator\",\r\n      title: \"QUẢN TRỊ ĐIỂM THƯỞNG\",\r\n    },\r\n    {\r\n      key: \"admin-crypto-wallets\",\r\n      title: \"Tài khoản điểm thưởng\",\r\n      url: \"/admin/crypto-wallets\",\r\n      icon: IconGift,\r\n    },\r\n    {\r\n      key: \"admin-crypto-transactions\",\r\n      title: \"Lịch sử điểm thưởng\",\r\n      url: \"/admin/crypto-transactions\",\r\n      icon: IconHistory,\r\n    },\r\n    {\r\n      type: \"separator\",\r\n      title: \"QUẢN TRỊ ĐẠI LÝ\",\r\n    },\r\n    {\r\n      key: \"admin-agents\",\r\n      title: \"Quản lý đại lý\",\r\n      url: \"/admin/agents\",\r\n      icon: IconUserStar,\r\n    },\r\n    {\r\n      key: \"admin-agent-commissions\",\r\n      title: \"Lịch sử hoa hồng\",\r\n      url: \"/admin/agent-commissions\",\r\n      icon: IconCash,\r\n    },\r\n    {\r\n      type: \"separator\",\r\n      title: \"QUẢN TRỊ NỘI DUNG\",\r\n    },\r\n    {\r\n      key: \"cms-categories\",\r\n      title: \"Chuyên mục\",\r\n      url: \"/admin/cms/categories\",\r\n      icon: IconCategory,\r\n    },\r\n    {\r\n      key: \"cms-posts\",\r\n      title: \"Bài viết\",\r\n      url: \"/admin/cms/posts\",\r\n      icon: IconNews,\r\n    },\r\n    {\r\n      key: \"cms-tags\",\r\n      title: \"Thẻ\",\r\n      url: \"/admin/cms/tags\",\r\n      icon: IconTags,\r\n    },\r\n    {\r\n      key: \"cms-pages\",\r\n      title: \"Trang\",\r\n      url: \"/admin/cms/pages\",\r\n      icon: IconBrandPagekit,\r\n    },\r\n    {\r\n      key: \"cms-banners\",\r\n      title: \"Banner\",\r\n      url: \"/admin/cms/banners\",\r\n      icon: IconPhoto,\r\n    },\r\n    {\r\n      key: \"cms-customer-feedbacks\",\r\n      title: \"Phản hồi khách hàng\",\r\n      url: \"/admin/cms/customer-feedbacks\",\r\n      icon: IconMessageCircle,\r\n    },\r\n    {\r\n      key: \"cms-showrooms\",\r\n      title: \"Cửa hàng/đại lý\",\r\n      url: \"/admin/cms/showrooms\",\r\n      icon: IconBuilding,\r\n    },\r\n    {\r\n      key: \"cms-partners\",\r\n      title: \"Đối tác\",\r\n      url: \"/admin/cms/partners\",\r\n      icon: IconHeartHandshake,\r\n    },\r\n    {\r\n      type: \"separator\",\r\n      title: \"QUẢN TRỊ NGƯỜI DÙNG\",\r\n    },\r\n    {\r\n      key: \"admin-users\",\r\n      title: \"Quản lý người dùng\",\r\n      url: \"/admin/users\",\r\n      icon: IconUserCog,\r\n    },\r\n    {\r\n      key: \"admin-roles\",\r\n      title: \"Vai trò & quyền hạn\",\r\n      url: \"/admin/roles\",\r\n      icon: IconShieldLock,\r\n    },\r\n    {\r\n      key: \"admin-kyc\",\r\n      title: \"Xác thực danh tính\",\r\n      url: \"/admin/kyc\",\r\n      icon: IconUserCheck,\r\n    },\r\n    {\r\n      type: \"separator\",\r\n      title: \"QUẢN TRỊ HỆ THỐNG\",\r\n    },\r\n    {\r\n      key: \"admin-profile\",\r\n      title: \"Hồ sơ quản trị viên\",\r\n      url: \"/admin/profile\",\r\n      icon: IconUserCircle,\r\n    },\r\n    {\r\n      key: \"admin-settings\",\r\n      title: \"Cấu hình hệ thống\",\r\n      url: \"/admin/settings\",\r\n      icon: IconSettings,\r\n    },\r\n    {\r\n      key: \"admin-website-settings\",\r\n      title: \"Cấu hình website\",\r\n      url: \"/admin/website-settings\",\r\n      icon: IconWorld,\r\n    },\r\n  ],\r\n};\r\n\r\n// Dữ liệu menu cho ADMIN\r\nconst agentNavData = {\r\n  navMain: [\r\n    {\r\n      key: \"agent-dashboard\",\r\n      title: \"Bảng quản trị\",\r\n      url: \"/agent/dashboard\",\r\n      icon: IconDashboard,\r\n    },\r\n    {\r\n      key: \"agent-reports\",\r\n      title: \"Báo cáo thống kê\",\r\n      url: \"/agent/reports\",\r\n      icon: IconDeviceAnalytics,\r\n    },\r\n    {\r\n      type: \"separator\",\r\n      title: \"QUẢN TRỊ BẠC TRỰC TUYẾN\",\r\n    },\r\n    {\r\n      key: \"agent-order-books\",\r\n      title: \"Giao dịch online\",\r\n      url: \"/agent/order-books\",\r\n      icon: IconExchange,\r\n    },\r\n    {\r\n      key: \"agent-token-assets\",\r\n      title: \"Quản lý tài sản\",\r\n      url: \"/agent/token-assets\",\r\n      icon: IconMoneybag,\r\n    },\r\n    // {\r\n    //   title: \"Quản lý sản phẩm\",\r\n    //   url: \"/agent/tokens\",\r\n    //   icon: IconPackage,\r\n    // },\r\n    // {\r\n    //   title: \"Danh mục sản phẩm\",\r\n    //   url: \"/agent/token-categories\",\r\n    //   icon: IconCategory,\r\n    // },\r\n    {\r\n      type: \"separator\",\r\n      title: \"QUẢN TRỊ SẢN PHẨM BẠC\",\r\n    },\r\n    {\r\n      key: \"agent-ecom-orders\",\r\n      title: \"Quản lý đơn hàng\",\r\n      url: \"/agent/ecom-orders\",\r\n      icon: IconShoppingCart,\r\n    },\r\n    // {\r\n    //   title: \"Quản lý sản phẩm\",\r\n    //   url: \"/agent/ecom-products\",\r\n    //   icon: IconPackageImport,\r\n    // },\r\n    // {\r\n    //   title: \"Danh mục sản phẩm\",\r\n    //   url: \"/agent/ecom-product-categories\",\r\n    //   icon: IconFolders,\r\n    // },\r\n    {\r\n      type: \"separator\",\r\n      title: \"QUẢN TRỊ TÀI CHÍNH\",\r\n    },\r\n    {\r\n      key: \"agent-wallets\",\r\n      title: \"Quản lý tài khoản\",\r\n      url: \"/agent/wallets\",\r\n      icon: IconWallet,\r\n    },\r\n    {\r\n      key: \"agent-transactions\",\r\n      title: \"Lịch sử thanh toán\",\r\n      url: \"/agent/transactions\",\r\n      icon: IconCurrencyDollar,\r\n    },\r\n    {\r\n      key: \"agent-payment-methods\",\r\n      title: \"Phương thức thanh toán\",\r\n      url: \"/agent/payment-methods\",\r\n      icon: IconCreditCard,\r\n    },\r\n    {\r\n      type: \"separator\",\r\n      title: \"QUẢN TRỊ ĐIỂM THƯỞNG\",\r\n    },\r\n    {\r\n      key: \"agent-crypto-wallets\",\r\n      title: \"Tài khoản điểm thưởng\",\r\n      url: \"/agent/crypto-wallets\",\r\n      icon: IconGift,\r\n    },\r\n    {\r\n      key: \"agent-crypto-transactions\",\r\n      title: \"Lịch sử điểm thưởng\",\r\n      url: \"/agent/crypto-transactions\",\r\n      icon: IconHistory,\r\n    },\r\n    {\r\n      type: \"separator\",\r\n      title: \"QUẢN TRỊ ĐẠI LÝ\",\r\n    },\r\n    {\r\n      key: \"agent-agents-hierarchical\",\r\n      title: \"Quản lý hoa hồng\",\r\n      url: \"/agent/agents-hierarchical\",\r\n      icon: IconUserStar,\r\n    },\r\n    {\r\n      key: \"agent-agent-commissions\",\r\n      title: \"Lịch sử hoa hồng\",\r\n      url: \"/agent/agent-commissions\",\r\n      icon: IconCash,\r\n    },\r\n    // {\r\n    //   type: \"separator\",\r\n    //   title: \"QUẢN LÝ NGƯỜI DÙNG\",\r\n    // },\r\n    // {\r\n    //   title: \"Quản lý người dùng\",\r\n    //   url: \"/agent/users\",\r\n    //   icon: IconUserCog,\r\n    // },\r\n    // {\r\n    //   title: \"Vai trò & quyền hạn\",\r\n    //   url: \"/agent/roles\",\r\n    //   icon: IconShieldLock,\r\n    // },\r\n    // {\r\n    //   title: \"Xác thực danh tính\",\r\n    //   url: \"/agent/kyc\",\r\n    //   icon: IconUserCheck,\r\n    // }\r\n  ],\r\n};\r\n\r\nconst data = {\r\n  navClouds: [\r\n    {\r\n      title: \"Capture\",\r\n      icon: IconFileAnalytics,\r\n      isActive: true,\r\n      url: \"#\",\r\n      items: [\r\n        {\r\n          title: \"Active Proposals\",\r\n          url: \"#\",\r\n        },\r\n        {\r\n          title: \"Archived\",\r\n          url: \"#\",\r\n        },\r\n      ],\r\n    },\r\n    {\r\n      title: \"Proposal\",\r\n      icon: IconFileDescription,\r\n      url: \"#\",\r\n      items: [\r\n        {\r\n          title: \"Active Proposals\",\r\n          url: \"#\",\r\n        },\r\n        {\r\n          title: \"Archived\",\r\n          url: \"#\",\r\n        },\r\n      ],\r\n    },\r\n    {\r\n      title: \"Prompts\",\r\n      icon: IconRobot,\r\n      url: \"#\",\r\n      items: [\r\n        {\r\n          title: \"Active Proposals\",\r\n          url: \"#\",\r\n        },\r\n        {\r\n          title: \"Archived\",\r\n          url: \"#\",\r\n        },\r\n      ],\r\n    },\r\n  ],\r\n  navSecondary: [\r\n  ],\r\n}\r\n\r\nexport function AppSidebar({ ...props }: React.ComponentProps<typeof Sidebar>) {\r\n  const { user, isLoading } = useAuth();\r\n  const { config } = useSiteMetadata();\r\n  const siteName = config?.['site_name'];\r\n  const siteLogo = config?.['site_logo'];\r\n\r\n  // Sử dụng ref để lưu menu cuối cùng khi user còn tồn tại\r\n  const lastValidNavDataRef = React.useRef(userNavData);\r\n  const lastValidUserRef = React.useRef<any>(null);\r\n\r\n  // Cập nhật ref khi có user hợp lệ\r\n  if (user && user.roles) {\r\n    const isAdmin = user.roles.includes(\"ADMIN\");\r\n    const isAgent = user.roles.includes(\"AGENT\");\r\n    lastValidNavDataRef.current = isAdmin ? adminNavData : isAgent ? agentNavData : userNavData;\r\n    lastValidUserRef.current = user;\r\n  }\r\n\r\n  // Nếu đang loading (logout), giữ nguyên menu cuối cùng nhưng disable interactions\r\n  if (isLoading && !user) {\r\n    return (\r\n      <Sidebar collapsible=\"icon\" {...props} className=\"pointer-events-none opacity-75\">\r\n        <SidebarHeader>\r\n          <SidebarMenu>\r\n            <SidebarMenuItem>\r\n              <SidebarMenuButton\r\n                asChild\r\n                className=\"data-[slot=sidebar-menu-button]:!p-1.5\"\r\n              >\r\n                <div className=\"flex items-center\">\r\n                  <img src={siteLogo} alt={siteName} className=\"h-10 w-[160px] min-w-[40px] object-contain object-left\" />\r\n                </div>\r\n              </SidebarMenuButton>\r\n            </SidebarMenuItem>\r\n          </SidebarMenu>\r\n        </SidebarHeader>\r\n        <SidebarContent className=\"[&_.tabler-icon]:w-5 [&_.tabler-icon]:h-5 [&_.tabler-icon]:min-w-[20px] [&_.tabler-icon]:min-h-[20px]\">\r\n          {/* Giữ nguyên menu cuối cùng */}\r\n          <NavMain items={lastValidNavDataRef.current.navMain} />\r\n          <NavSecondary items={data.navSecondary} className=\"mt-auto\" />\r\n        </SidebarContent>\r\n        <SidebarFooter>\r\n          <NavUser />\r\n        </SidebarFooter>\r\n      </Sidebar>\r\n    );\r\n  }\r\n\r\n  // Guard: Nếu không có user sau khi loading xong\r\n  if (!user) {\r\n    return (\r\n      <Sidebar collapsible=\"icon\" {...props}>\r\n        <SidebarHeader>\r\n          <SidebarMenu>\r\n            <SidebarMenuItem>\r\n              <SidebarMenuButton\r\n                asChild\r\n                className=\"data-[slot=sidebar-menu-button]:!p-1.5\"\r\n              >\r\n                <Link href=\"/dashboard\" className=\"flex items-center\">\r\n                  <img src={siteLogo} alt={siteName} className=\"h-10 w-[160px] min-w-[40px] object-contain object-left\" />\r\n                </Link>\r\n              </SidebarMenuButton>\r\n            </SidebarMenuItem>\r\n          </SidebarMenu>\r\n        </SidebarHeader>\r\n        <SidebarContent className=\"[&_.tabler-icon]:w-5 [&_.tabler-icon]:h-5 [&_.tabler-icon]:min-w-[20px] [&_.tabler-icon]:min-h-[20px]\">\r\n          <NavMain items={userNavData.navMain} />\r\n          <NavSecondary items={data.navSecondary} className=\"mt-auto\" />\r\n        </SidebarContent>\r\n        <SidebarFooter>\r\n          <NavUser />\r\n        </SidebarFooter>\r\n      </Sidebar>\r\n    );\r\n  }\r\n\r\n  const isAdmin = user.roles?.includes(\"ADMIN\");\r\n  const isAgent = user.roles?.includes(\"AGENT\");\r\n\r\n  // Chọn menu dựa trên vai trò người dùng\r\n  const navData = isAdmin ? adminNavData : isAgent ? agentNavData : userNavData;\r\n\r\n  return (\r\n    <Sidebar collapsible=\"icon\" {...props}>\r\n      <SidebarHeader>\r\n        <SidebarMenu>\r\n          <SidebarMenuItem>\r\n            <SidebarMenuButton\r\n              asChild\r\n              className=\"data-[slot=sidebar-menu-button]:!p-1.5\"\r\n            >\r\n              <Link href={isAdmin ? \"/admin/dashboard\" : isAgent ? \"/agent/dashboard\" : \"/dashboard\"} \r\n                className=\"flex items-center\">\r\n                <img src={siteLogo} alt={siteName} className=\"h-10 w-[160px] min-w-[40px] object-contain object-left\" />\r\n              </Link>\r\n            </SidebarMenuButton>\r\n          </SidebarMenuItem>\r\n        </SidebarMenu>\r\n      </SidebarHeader>\r\n      <SidebarContent className=\"[&_.tabler-icon]:w-5 [&_.tabler-icon]:h-5 [&_.tabler-icon]:min-w-[20px] [&_.tabler-icon]:min-h-[20px]\">\r\n        <NavMain items={navData.navMain} />\r\n        <NavSecondary items={data.navSecondary} className=\"mt-auto\" />\r\n      </SidebarContent>\r\n      <SidebarFooter>\r\n        <NavUser />\r\n      </SidebarFooter>\r\n    </Sidebar>\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAuCA;AACA;AAEA;AACA;AACA;AACA;AAUA;;;AA1DA;;;;;;;;;;AA4DA,wBAAwB;AAExB,MAAM,cAAc;IAClB,SAAS;QACP;YACE,KAAK;YACL,OAAO;YACP,KAAK;YACL,qBAAqB;YACrB,MAAM,iUAAA,CAAA,gBAAa;QACrB;QACA,IAAI;QACJ,+BAA+B;QAC/B,qBAAqB;QACrB,6BAA6B;QAC7B,KAAK;QACL;YACE,MAAM;YACN,OAAO;QACT;QACA;YACE,KAAK;YACL,OAAO;YACP,KAAK;YACL,MAAM,qUAAA,CAAA,kBAAe;QACvB;QACA;YACE,KAAK;YACL,OAAO;YACP,KAAK;YACL,MAAM,6TAAA,CAAA,cAAW;QACnB;QACA;YACE,KAAK;YACL,OAAO;YACP,KAAK;YACL,MAAM,uTAAA,CAAA,WAAQ;QAChB;QACA;YACE,MAAM;YACN,OAAO;QACT;QACA;YACE,KAAK;YACL,OAAO;YACP,KAAK;YACL,MAAM,yUAAA,CAAA,oBAAiB;QACzB;QACA;YACE,KAAK;YACL,OAAO;YACP,KAAK;YACL,MAAM,uUAAA,CAAA,mBAAgB;QACxB;QACA;YACE,MAAM;YACN,OAAO;QACT;QACA;YACE,KAAK;YACL,OAAO;YACP,KAAK;YACL,MAAM,2UAAA,CAAA,qBAAkB;QAC1B;QACA;YACE,MAAM;YACN,OAAO;QACT;QACA;YACE,KAAK;YACL,OAAO;YACP,KAAK;YACL,MAAM,uTAAA,CAAA,WAAQ;QAChB;QACA;YACE,KAAK;YACL,OAAO;YACP,KAAK;YACL,MAAM,iUAAA,CAAA,gBAAa;QACrB;KACD;AACH;AAEA,yBAAyB;AACzB,MAAM,eAAe;IACnB,SAAS;QACP;YACE,KAAK;YACL,OAAO;YACP,KAAK;YACL,MAAM,iUAAA,CAAA,gBAAa;QACrB;QACA;YACE,KAAK;YACL,OAAO;YACP,KAAK;YACL,MAAM,6UAAA,CAAA,sBAAmB;QAC3B;QACA;YACE,MAAM;YACN,OAAO;QACT;QACA;YACE,KAAK;YACL,OAAO;YACP,KAAK;YACL,MAAM,+TAAA,CAAA,eAAY;QACpB;QACA;YACE,KAAK;YACL,OAAO;YACP,KAAK;YACL,MAAM,+TAAA,CAAA,eAAY;QACpB;QACA;YACE,KAAK;YACL,OAAO;YACP,KAAK;YACL,MAAM,6TAAA,CAAA,cAAW;QACnB;QACA;YACE,KAAK;YACL,OAAO;YACP,KAAK;YACL,MAAM,+TAAA,CAAA,eAAY;QACpB;QACA;YACE,MAAM;YACN,OAAO;QACT;QACA;YACE,KAAK;YACL,OAAO;YACP,KAAK;YACL,MAAM,uUAAA,CAAA,mBAAgB;QACxB;QACA;YACE,KAAK;YACL,OAAO;YACP,KAAK;YACL,MAAM,yUAAA,CAAA,oBAAiB;QACzB;QACA;YACE,KAAK;YACL,OAAO;YACP,KAAK;YACL,MAAM,6TAAA,CAAA,cAAW;QACnB;QACA;YACE,MAAM;YACN,OAAO;QACT;QACA;YACE,KAAK;YACL,OAAO;YACP,KAAK;YACL,MAAM,2TAAA,CAAA,aAAU;QAClB;QACA;YACE,KAAK;YACL,OAAO;YACP,KAAK;YACL,MAAM,2UAAA,CAAA,qBAAkB;QAC1B;QACA;YACE,KAAK;YACL,OAAO;YACP,KAAK;YACL,MAAM,mUAAA,CAAA,iBAAc;QACtB;QACA;YACE,KAAK;YACL,OAAO;YACP,KAAK;YACL,MAAM,uUAAA,CAAA,mBAAgB;QACxB;QACA;YACE,MAAM;YACN,OAAO;QACT;QACA;YACE,KAAK;YACL,OAAO;YACP,KAAK;YACL,MAAM,uTAAA,CAAA,WAAQ;QAChB;QACA;YACE,KAAK;YACL,OAAO;YACP,KAAK;YACL,MAAM,6TAAA,CAAA,cAAW;QACnB;QACA;YACE,MAAM;YACN,OAAO;QACT;QACA;YACE,KAAK;YACL,OAAO;YACP,KAAK;YACL,MAAM,+TAAA,CAAA,eAAY;QACpB;QACA;YACE,KAAK;YACL,OAAO;YACP,KAAK;YACL,MAAM,uTAAA,CAAA,WAAQ;QAChB;QACA;YACE,MAAM;YACN,OAAO;QACT;QACA;YACE,KAAK;YACL,OAAO;YACP,KAAK;YACL,MAAM,+TAAA,CAAA,eAAY;QACpB;QACA;YACE,KAAK;YACL,OAAO;YACP,KAAK;YACL,MAAM,uTAAA,CAAA,WAAQ;QAChB;QACA;YACE,KAAK;YACL,OAAO;YACP,KAAK;YACL,MAAM,uTAAA,CAAA,WAAQ;QAChB;QACA;YACE,KAAK;YACL,OAAO;YACP,KAAK;YACL,MAAM,uUAAA,CAAA,mBAAgB;QACxB;QACA;YACE,KAAK;YACL,OAAO;YACP,KAAK;YACL,MAAM,yTAAA,CAAA,YAAS;QACjB;QACA;YACE,KAAK;YACL,OAAO;YACP,KAAK;YACL,MAAM,yUAAA,CAAA,oBAAiB;QACzB;QACA;YACE,KAAK;YACL,OAAO;YACP,KAAK;YACL,MAAM,+TAAA,CAAA,eAAY;QACpB;QACA;YACE,KAAK;YACL,OAAO;YACP,KAAK;YACL,MAAM,2UAAA,CAAA,qBAAkB;QAC1B;QACA;YACE,MAAM;YACN,OAAO;QACT;QACA;YACE,KAAK;YACL,OAAO;YACP,KAAK;YACL,MAAM,6TAAA,CAAA,cAAW;QACnB;QACA;YACE,KAAK;YACL,OAAO;YACP,KAAK;YACL,MAAM,mUAAA,CAAA,iBAAc;QACtB;QACA;YACE,KAAK;YACL,OAAO;YACP,KAAK;YACL,MAAM,iUAAA,CAAA,gBAAa;QACrB;QACA;YACE,MAAM;YACN,OAAO;QACT;QACA;YACE,KAAK;YACL,OAAO;YACP,KAAK;YACL,MAAM,mUAAA,CAAA,iBAAc;QACtB;QACA;YACE,KAAK;YACL,OAAO;YACP,KAAK;YACL,MAAM,+TAAA,CAAA,eAAY;QACpB;QACA;YACE,KAAK;YACL,OAAO;YACP,KAAK;YACL,MAAM,yTAAA,CAAA,YAAS;QACjB;KACD;AACH;AAEA,yBAAyB;AACzB,MAAM,eAAe;IACnB,SAAS;QACP;YACE,KAAK;YACL,OAAO;YACP,KAAK;YACL,MAAM,iUAAA,CAAA,gBAAa;QACrB;QACA;YACE,KAAK;YACL,OAAO;YACP,KAAK;YACL,MAAM,6UAAA,CAAA,sBAAmB;QAC3B;QACA;YACE,MAAM;YACN,OAAO;QACT;QACA;YACE,KAAK;YACL,OAAO;YACP,KAAK;YACL,MAAM,+TAAA,CAAA,eAAY;QACpB;QACA;YACE,KAAK;YACL,OAAO;YACP,KAAK;YACL,MAAM,+TAAA,CAAA,eAAY;QACpB;QACA,IAAI;QACJ,+BAA+B;QAC/B,0BAA0B;QAC1B,uBAAuB;QACvB,KAAK;QACL,IAAI;QACJ,gCAAgC;QAChC,oCAAoC;QACpC,wBAAwB;QACxB,KAAK;QACL;YACE,MAAM;YACN,OAAO;QACT;QACA;YACE,KAAK;YACL,OAAO;YACP,KAAK;YACL,MAAM,uUAAA,CAAA,mBAAgB;QACxB;QACA,IAAI;QACJ,+BAA+B;QAC/B,iCAAiC;QACjC,6BAA6B;QAC7B,KAAK;QACL,IAAI;QACJ,gCAAgC;QAChC,2CAA2C;QAC3C,uBAAuB;QACvB,KAAK;QACL;YACE,MAAM;YACN,OAAO;QACT;QACA;YACE,KAAK;YACL,OAAO;YACP,KAAK;YACL,MAAM,2TAAA,CAAA,aAAU;QAClB;QACA;YACE,KAAK;YACL,OAAO;YACP,KAAK;YACL,MAAM,2UAAA,CAAA,qBAAkB;QAC1B;QACA;YACE,KAAK;YACL,OAAO;YACP,KAAK;YACL,MAAM,mUAAA,CAAA,iBAAc;QACtB;QACA;YACE,MAAM;YACN,OAAO;QACT;QACA;YACE,KAAK;YACL,OAAO;YACP,KAAK;YACL,MAAM,uTAAA,CAAA,WAAQ;QAChB;QACA;YACE,KAAK;YACL,OAAO;YACP,KAAK;YACL,MAAM,6TAAA,CAAA,cAAW;QACnB;QACA;YACE,MAAM;YACN,OAAO;QACT;QACA;YACE,KAAK;YACL,OAAO;YACP,KAAK;YACL,MAAM,+TAAA,CAAA,eAAY;QACpB;QACA;YACE,KAAK;YACL,OAAO;YACP,KAAK;YACL,MAAM,uTAAA,CAAA,WAAQ;QAChB;KAoBD;AACH;AAEA,MAAM,OAAO;IACX,WAAW;QACT;YACE,OAAO;YACP,MAAM,yUAAA,CAAA,oBAAiB;YACvB,UAAU;YACV,KAAK;YACL,OAAO;gBACL;oBACE,OAAO;oBACP,KAAK;gBACP;gBACA;oBACE,OAAO;oBACP,KAAK;gBACP;aACD;QACH;QACA;YACE,OAAO;YACP,MAAM,6UAAA,CAAA,sBAAmB;YACzB,KAAK;YACL,OAAO;gBACL;oBACE,OAAO;oBACP,KAAK;gBACP;gBACA;oBACE,OAAO;oBACP,KAAK;gBACP;aACD;QACH;QACA;YACE,OAAO;YACP,MAAM,yTAAA,CAAA,YAAS;YACf,KAAK;YACL,OAAO;gBACL;oBACE,OAAO;oBACP,KAAK;gBACP;gBACA;oBACE,OAAO;oBACP,KAAK;gBACP;aACD;QACH;KACD;IACD,cAAc,EACb;AACH;AAEO,SAAS,WAAW,EAAE,GAAG,OAA6C;;IAC3E,MAAM,EAAE,IAAI,EAAE,SAAS,EAAE,GAAG,CAAA,GAAA,wHAAA,CAAA,UAAO,AAAD;IAClC,MAAM,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,mIAAA,CAAA,kBAAe,AAAD;IACjC,MAAM,WAAW,QAAQ,CAAC,YAAY;IACtC,MAAM,WAAW,QAAQ,CAAC,YAAY;IAEtC,yDAAyD;IACzD,MAAM,sBAAsB,CAAA,GAAA,sQAAA,CAAA,SAAY,AAAD,EAAE;IACzC,MAAM,mBAAmB,CAAA,GAAA,sQAAA,CAAA,SAAY,AAAD,EAAO;IAE3C,kCAAkC;IAClC,IAAI,QAAQ,KAAK,KAAK,EAAE;QACtB,MAAM,UAAU,KAAK,KAAK,CAAC,QAAQ,CAAC;QACpC,MAAM,UAAU,KAAK,KAAK,CAAC,QAAQ,CAAC;QACpC,oBAAoB,OAAO,GAAG,UAAU,eAAe,UAAU,eAAe;QAChF,iBAAiB,OAAO,GAAG;IAC7B;IAEA,kFAAkF;IAClF,IAAI,aAAa,CAAC,MAAM;QACtB,qBACE,sSAAC,+HAAA,CAAA,UAAO;YAAC,aAAY;YAAQ,GAAG,KAAK;YAAE,WAAU;;8BAC/C,sSAAC,+HAAA,CAAA,gBAAa;8BACZ,cAAA,sSAAC,+HAAA,CAAA,cAAW;kCACV,cAAA,sSAAC,+HAAA,CAAA,kBAAe;sCACd,cAAA,sSAAC,+HAAA,CAAA,oBAAiB;gCAChB,OAAO;gCACP,WAAU;0CAEV,cAAA,sSAAC;oCAAI,WAAU;8CACb,cAAA,sSAAC;wCAAI,KAAK;wCAAU,KAAK;wCAAU,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAMvD,sSAAC,+HAAA,CAAA,iBAAc;oBAAC,WAAU;;sCAExB,sSAAC,6HAAA,CAAA,UAAO;4BAAC,OAAO,oBAAoB,OAAO,CAAC,OAAO;;;;;;sCACnD,sSAAC,kIAAA,CAAA,eAAY;4BAAC,OAAO,KAAK,YAAY;4BAAE,WAAU;;;;;;;;;;;;8BAEpD,sSAAC,+HAAA,CAAA,gBAAa;8BACZ,cAAA,sSAAC,6HAAA,CAAA,UAAO;;;;;;;;;;;;;;;;IAIhB;IAEA,gDAAgD;IAChD,IAAI,CAAC,MAAM;QACT,qBACE,sSAAC,+HAAA,CAAA,UAAO;YAAC,aAAY;YAAQ,GAAG,KAAK;;8BACnC,sSAAC,+HAAA,CAAA,gBAAa;8BACZ,cAAA,sSAAC,+HAAA,CAAA,cAAW;kCACV,cAAA,sSAAC,+HAAA,CAAA,kBAAe;sCACd,cAAA,sSAAC,+HAAA,CAAA,oBAAiB;gCAChB,OAAO;gCACP,WAAU;0CAEV,cAAA,sSAAC,wQAAA,CAAA,UAAI;oCAAC,MAAK;oCAAa,WAAU;8CAChC,cAAA,sSAAC;wCAAI,KAAK;wCAAU,KAAK;wCAAU,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAMvD,sSAAC,+HAAA,CAAA,iBAAc;oBAAC,WAAU;;sCACxB,sSAAC,6HAAA,CAAA,UAAO;4BAAC,OAAO,YAAY,OAAO;;;;;;sCACnC,sSAAC,kIAAA,CAAA,eAAY;4BAAC,OAAO,KAAK,YAAY;4BAAE,WAAU;;;;;;;;;;;;8BAEpD,sSAAC,+HAAA,CAAA,gBAAa;8BACZ,cAAA,sSAAC,6HAAA,CAAA,UAAO;;;;;;;;;;;;;;;;IAIhB;IAEA,MAAM,UAAU,KAAK,KAAK,EAAE,SAAS;IACrC,MAAM,UAAU,KAAK,KAAK,EAAE,SAAS;IAErC,wCAAwC;IACxC,MAAM,UAAU,UAAU,eAAe,UAAU,eAAe;IAElE,qBACE,sSAAC,+HAAA,CAAA,UAAO;QAAC,aAAY;QAAQ,GAAG,KAAK;;0BACnC,sSAAC,+HAAA,CAAA,gBAAa;0BACZ,cAAA,sSAAC,+HAAA,CAAA,cAAW;8BACV,cAAA,sSAAC,+HAAA,CAAA,kBAAe;kCACd,cAAA,sSAAC,+HAAA,CAAA,oBAAiB;4BAChB,OAAO;4BACP,WAAU;sCAEV,cAAA,sSAAC,wQAAA,CAAA,UAAI;gCAAC,MAAM,UAAU,qBAAqB,UAAU,qBAAqB;gCACxE,WAAU;0CACV,cAAA,sSAAC;oCAAI,KAAK;oCAAU,KAAK;oCAAU,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAMvD,sSAAC,+HAAA,CAAA,iBAAc;gBAAC,WAAU;;kCACxB,sSAAC,6HAAA,CAAA,UAAO;wBAAC,OAAO,QAAQ,OAAO;;;;;;kCAC/B,sSAAC,kIAAA,CAAA,eAAY;wBAAC,OAAO,KAAK,YAAY;wBAAE,WAAU;;;;;;;;;;;;0BAEpD,sSAAC,+HAAA,CAAA,gBAAa;0BACZ,cAAA,sSAAC,6HAAA,CAAA,UAAO;;;;;;;;;;;;;;;;AAIhB;GA7GgB;;QACc,wHAAA,CAAA,UAAO;QAChB,mIAAA,CAAA,kBAAe;;;KAFpB", "debugId": null}}, {"offset": {"line": 1059, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/bloomgoo/gold-exchange-platform/frontend/components/layout/main-layout.tsx"], "sourcesContent": ["import { AppSidebar } from '@/components/app-sidebar';\r\nimport { SidebarProvider } from '@/components/ui/sidebar';\r\nimport { cn } from '@/lib/utils';\r\n\r\ninterface MainLayoutProps {\r\n   children: React.ReactNode;\r\n   header: React.ReactNode;\r\n   headersNumber?: 1 | 2;\r\n}\r\n\r\nexport default function MainLayout({ children, header, headersNumber = 2 }: MainLayoutProps) {\r\n   const height = {\r\n      1: 'h-[calc(100svh-40px)] lg:h-[calc(100svh-56px)]',\r\n      2: 'h-[calc(100svh-80px)] lg:h-[calc(100svh-96px)]',\r\n   };\r\n   return (\r\n      <SidebarProvider>\r\n         <AppSidebar variant=\"inset\" />\r\n         <div className=\"h-svh overflow-hidden lg:p-2 w-full\">\r\n            <div className=\"lg:border lg:rounded-md overflow-hidden flex flex-col items-center justify-start bg-container h-full w-full\">\r\n               {header}\r\n               <div\r\n                  className={cn(\r\n                     'overflow-auto w-full',\r\n                     height[headersNumber as keyof typeof height]\r\n                  )}\r\n               >\r\n                  {children}\r\n               </div>\r\n            </div>\r\n         </div>\r\n      </SidebarProvider>\r\n   );\r\n}\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AAAA;;;;;AAQe,SAAS,WAAW,EAAE,QAAQ,EAAE,MAAM,EAAE,gBAAgB,CAAC,EAAmB;IACxF,MAAM,SAAS;QACZ,GAAG;QACH,GAAG;IACN;IACA,qBACG,sSAAC,+HAAA,CAAA,kBAAe;;0BACb,sSAAC,gIAAA,CAAA,aAAU;gBAAC,SAAQ;;;;;;0BACpB,sSAAC;gBAAI,WAAU;0BACZ,cAAA,sSAAC;oBAAI,WAAU;;wBACX;sCACD,sSAAC;4BACE,WAAW,CAAA,GAAA,+HAAA,CAAA,KAAE,AAAD,EACT,wBACA,MAAM,CAAC,cAAqC;sCAG9C;;;;;;;;;;;;;;;;;;;;;;;AAMnB;KAvBwB", "debugId": null}}, {"offset": {"line": 1129, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/bloomgoo/gold-exchange-platform/frontend/components/layout/headers/page-header.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React from \"react\";\r\nimport { cn } from \"@/lib/utils\";\r\nimport { SidebarTrigger } from \"@/components/ui/sidebar\";\r\nimport { SilverPriceIndicator } from \"@/components/common/SilverPriceIndicator\";\r\n\r\ninterface PageHeaderProps {\r\n  title: string;\r\n  description?: string;\r\n  actions?: React.ReactNode;\r\n  className?: string;\r\n}\r\n\r\nexport function PageHeader({ title, description, actions, className }: PageHeaderProps) {\r\n  return (\r\n    <div className={cn(\"flex items-center justify-between gap-2 p-4 border-b w-full\", className)}>\r\n      {/* Left: SidebarTrigger and Title/Description */}\r\n      <div className=\"flex items-center gap-3 overflow-hidden\">\r\n        <SidebarTrigger className=\"-ml-1 flex-shrink-0\" />\r\n        <div className=\"flex flex-col gap-1 overflow-hidden\">\r\n          <h1 className=\"text-xl font-semibold truncate\">{title}</h1>\r\n          {description && <p className=\"text-sm text-muted-foreground truncate\">{description}</p>}\r\n        </div>\r\n      </div>\r\n\r\n      {/* Right: Price Indicator and Actions */}\r\n      <div className=\"flex items-center gap-2 ml-4\">\r\n        <SilverPriceIndicator />\r\n        {actions && (\r\n          <div className=\"flex items-center gap-2 ml-2\">\r\n            {actions}\r\n          </div>\r\n        )}\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAGA;AAAA;AACA;AACA;AALA;;;;;AAcO,SAAS,WAAW,EAAE,KAAK,EAAE,WAAW,EAAE,OAAO,EAAE,SAAS,EAAmB;IACpF,qBACE,sSAAC;QAAI,WAAW,CAAA,GAAA,+HAAA,CAAA,KAAE,AAAD,EAAE,+DAA+D;;0BAEhF,sSAAC;gBAAI,WAAU;;kCACb,sSAAC,+HAAA,CAAA,iBAAc;wBAAC,WAAU;;;;;;kCAC1B,sSAAC;wBAAI,WAAU;;0CACb,sSAAC;gCAAG,WAAU;0CAAkC;;;;;;4BAC/C,6BAAe,sSAAC;gCAAE,WAAU;0CAA0C;;;;;;;;;;;;;;;;;;0BAK3E,sSAAC;gBAAI,WAAU;;kCACb,sSAAC,gJAAA,CAAA,uBAAoB;;;;;oBACpB,yBACC,sSAAC;wBAAI,WAAU;kCACZ;;;;;;;;;;;;;;;;;;AAMb;KAvBgB", "debugId": null}}, {"offset": {"line": 1228, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/bloomgoo/gold-exchange-platform/frontend/components/layout-provider.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React, { createContext, useContext, ReactNode } from \"react\";\r\nimport { useAuth } from \"@/hooks/use-auth\";\r\nimport { AppLayout } from \"@/components/app-layout\";\r\nimport MainLayout from \"@/components/layout/main-layout\";\r\nimport { PageHeader } from \"@/components/layout/headers/page-header\";\r\n\r\ntype LayoutContextType = {\r\n  isAdmin: boolean;\r\n};\r\n\r\nconst LayoutContext = createContext<LayoutContextType | undefined>(undefined);\r\n\r\nexport function useLayout() {\r\n  const context = useContext(LayoutContext);\r\n  if (context === undefined) {\r\n    throw new Error(\"useLayout must be used within a LayoutProvider\");\r\n  }\r\n  return context;\r\n}\r\n\r\ninterface LayoutProviderProps {\r\n  children: ReactNode;\r\n  header?: ReactNode;\r\n  title?: string;\r\n  description?: string;\r\n  actions?: ReactNode;\r\n}\r\n\r\nexport function LayoutProvider({\r\n  children,\r\n  header,\r\n  title,\r\n  description,\r\n  actions,\r\n}: LayoutProviderProps) {\r\n  const { user } = useAuth();\r\n  const isAdmin = user?.roles.includes(\"ADMIN\");\r\n\r\n  // Nếu là admin, sử dụng layout mới\r\n  if (isAdmin) {\r\n    return (\r\n      <LayoutContext.Provider value={{ isAdmin }}>\r\n        <MainLayout\r\n          header={\r\n            header || (\r\n              <PageHeader\r\n                title={title || \"Dashboard\"}\r\n                description={description}\r\n                actions={actions}\r\n              />\r\n            )\r\n          }\r\n        >\r\n          {children}\r\n        </MainLayout>\r\n      </LayoutContext.Provider>\r\n    );\r\n  }\r\n\r\n  // Nếu là user thông thường, sử dụng layout cũ\r\n  return (\r\n    <LayoutContext.Provider value={{ isAdmin: isAdmin ?? false }}>\r\n      <MainLayout\r\n          header={\r\n            header || (\r\n              <PageHeader\r\n                title={title || \"Dashboard\"}\r\n                description={description}\r\n                actions={actions}\r\n              />\r\n            )\r\n          }\r\n        >\r\n          {children}\r\n        </MainLayout>\r\n    </LayoutContext.Provider>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AAEA;AACA;;;AANA;;;;;AAYA,MAAM,8BAAgB,CAAA,GAAA,sQAAA,CAAA,gBAAa,AAAD,EAAiC;AAE5D,SAAS;;IACd,MAAM,UAAU,CAAA,GAAA,sQAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,YAAY,WAAW;QACzB,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT;GANgB;AAgBT,SAAS,eAAe,EAC7B,QAAQ,EACR,MAAM,EACN,KAAK,EACL,WAAW,EACX,OAAO,EACa;;IACpB,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,wHAAA,CAAA,UAAO,AAAD;IACvB,MAAM,UAAU,MAAM,MAAM,SAAS;IAErC,mCAAmC;IACnC,IAAI,SAAS;QACX,qBACE,sSAAC,cAAc,QAAQ;YAAC,OAAO;gBAAE;YAAQ;sBACvC,cAAA,sSAAC,0IAAA,CAAA,UAAU;gBACT,QACE,wBACE,sSAAC,qJAAA,CAAA,aAAU;oBACT,OAAO,SAAS;oBAChB,aAAa;oBACb,SAAS;;;;;;0BAKd;;;;;;;;;;;IAIT;IAEA,8CAA8C;IAC9C,qBACE,sSAAC,cAAc,QAAQ;QAAC,OAAO;YAAE,SAAS,WAAW;QAAM;kBACzD,cAAA,sSAAC,0IAAA,CAAA,UAAU;YACP,QACE,wBACE,sSAAC,qJAAA,CAAA,aAAU;gBACT,OAAO,SAAS;gBAChB,aAAa;gBACb,SAAS;;;;;;sBAKd;;;;;;;;;;;AAIX;IAjDgB;;QAOG,wHAAA,CAAA,UAAO;;;KAPV", "debugId": null}}, {"offset": {"line": 1330, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/bloomgoo/gold-exchange-platform/frontend/components/info-card.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React from 'react';\r\nimport {\r\n  Card,\r\n  CardContent,\r\n  CardDescription,\r\n  CardHeader,\r\n  CardTitle,\r\n} from '@/components/ui/card';\r\nimport { cn } from '@/lib/utils';\r\n\r\ninterface InfoCardProps {\r\n  title: string;\r\n  description?: string;\r\n  children: React.ReactNode;\r\n  className?: string;\r\n}\r\n\r\nexport function InfoCard({\r\n  title,\r\n  description,\r\n  children,\r\n  className = '',\r\n}: InfoCardProps) {\r\n  return (\r\n    <Card className={cn(\"shadow-none border-none py-2\", className)}>\r\n      <CardHeader className=\"border-l-4 border-l-primary pl-4\">\r\n        <CardTitle className=\"text-lg\">{title}</CardTitle>\r\n        {description && <CardDescription>{description}</CardDescription>}\r\n      </CardHeader>\r\n      <CardContent className=\"space-y-4\">\r\n        {children}\r\n      </CardContent>\r\n    </Card>\r\n  );\r\n} "], "names": [], "mappings": ";;;;AAGA;AAOA;AAAA;AAVA;;;;AAmBO,SAAS,SAAS,EACvB,KAAK,EACL,WAAW,EACX,QAAQ,EACR,YAAY,EAAE,EACA;IACd,qBACE,sSAAC,4HAAA,CAAA,OAAI;QAAC,WAAW,CAAA,GAAA,+HAAA,CAAA,KAAE,AAAD,EAAE,gCAAgC;;0BAClD,sSAAC,4HAAA,CAAA,aAAU;gBAAC,WAAU;;kCACpB,sSAAC,4HAAA,CAAA,YAAS;wBAAC,WAAU;kCAAW;;;;;;oBAC/B,6BAAe,sSAAC,4HAAA,CAAA,kBAAe;kCAAE;;;;;;;;;;;;0BAEpC,sSAAC,4HAAA,CAAA,cAAW;gBAAC,WAAU;0BACpB;;;;;;;;;;;;AAIT;KAjBgB", "debugId": null}}]}
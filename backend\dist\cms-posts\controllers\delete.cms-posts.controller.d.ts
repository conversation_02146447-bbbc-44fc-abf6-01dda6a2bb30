import { DeleteCmsPostsService } from '../services/delete.cms-posts.service';
import { CmsPostDto } from '../dto/cms-post.dto';
export declare class DeleteCmsPostsController {
    private readonly cmsPostsService;
    constructor(cmsPostsService: DeleteCmsPostsService);
    softDelete(id: string, userId: string): Promise<CmsPostDto | null>;
    restore(id: string, userId: string): Promise<CmsPostDto | null>;
    remove(id: string): Promise<{
        affected: number;
    }>;
    bulkSoftDelete(ids: string[], userId: string): Promise<CmsPostDto[]>;
    bulkRestore(ids: string[], userId: string): Promise<CmsPostDto[]>;
    bulkRemove(ids: string[]): Promise<{
        affected: number;
    }>;
}

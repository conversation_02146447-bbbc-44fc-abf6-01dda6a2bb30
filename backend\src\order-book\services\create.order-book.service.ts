/**
 * Service xử lý các thao tác tạo mới sổ lệnh
 * <PERSON><PERSON> gồm các phư<PERSON>ng thức tạo mới, tạo hàng loạt và nhân bản
 */
import {
  BadRequestException,
  Injectable,
  InternalServerErrorException,
  NotFoundException,
} from '@nestjs/common';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { InjectRepository } from '@nestjs/typeorm';
import { In, Repository } from 'typeorm';
import { Transactional } from 'typeorm-transactional';
import { CommissionProcessorService } from '../../agent-commissions/services/commission-processor.service';
import {
  InsufficientBalanceException,
  InsufficientTokenException,
} from '../../common/exceptions/localized-http.exception';
import { AssetService } from '../../token-assets/asset.service';
import { CreateAssetDto } from '../../token-assets/dto/create-asset.dto';
import { TransactionType } from '../../transactions/enums/transaction-type.enum';

import { EcomProduct } from 'src/ecom-products/entity/ecom-products.entity';
import { parseNumericValue } from '../../common/utils/number.util';
import { User } from '../../users/entities/user.entity';
import { ReadWalletService } from '../../wallets/services/read.wallet.service';
import { UpdateWalletService } from '../../wallets/services/update.wallet.service';
import { CreateOrderBookDto } from '../dto/create-order-book.dto';
import { OrderBookDto } from '../dto/order-book.dto';
import { OrderBookDetail } from '../entities/order-book-detail.entity';
import { OrderBook } from '../entities/order-book.entity';
import { ApproveStatus } from '../enums/approve-status.enum';
import { BusinessType } from '../enums/business-type.enum';
import { OrderStatus } from '../enums/order-status.enum';
import { OrderType } from '../enums/order-type.enum';
import { BaseOrderBookService } from './base.order-book.service';

@Injectable()
export class CreateOrderBookService extends BaseOrderBookService {
  constructor(
    @InjectRepository(OrderBook)
    protected readonly orderBookRepository: Repository<OrderBook>,
    @InjectRepository(OrderBookDetail)
    private readonly orderBookDetailRepository: Repository<OrderBookDetail>,
    @InjectRepository(User)
    private readonly userRepository: Repository<User>,
    @InjectRepository(EcomProduct)
    private readonly ecomProductRepository: Repository<EcomProduct>,
    protected readonly eventEmitter: EventEmitter2,
    private readonly readWalletService: ReadWalletService,
    private readonly updateWalletService: UpdateWalletService,
    private readonly tokenAssetService: AssetService,
    private readonly commissionProcessorService: CommissionProcessorService,
  ) {
    super(orderBookRepository, eventEmitter);
  }

  /**
   * Tạo mới một lệnh
   * @param createOrderBookDto Dữ liệu lệnh cần tạo
   * @param userId ID của người dùng thực hiện thao tác
   * @returns Thông tin lệnh đã tạo
   * @throws BadRequestException nếu dữ liệu không hợp lệ
   * @throws InternalServerErrorException nếu có lỗi khác xảy ra
   */
  @Transactional()
  async create(
    createOrderBookDto: CreateOrderBookDto,
    userId: string,
  ): Promise<OrderBookDto> {
    try {
      this.logger.log(`Đang tạo lệnh mới: ${createOrderBookDto.orderType}`);

      // 1. Xác thực đầu vào và quyền truy cập
      const validationResult = await this.validateInputAndAuthentication(
        createOrderBookDto,
        userId,
      );

      // 2. Tính toán các giá trị nghiệp vụ
      const calculationResult =
        await this.calculateOrderPrices(createOrderBookDto, validationResult);

      // 3. Tạo entity OrderBook
      const orderBook = await this.createOrderBookEntity(
        createOrderBookDto,
        createOrderBookDto.userId ? createOrderBookDto.userId : userId,
        calculationResult,
      );

      const savedOrderBook = await this.orderBookRepository.save(orderBook);

      await this.createOrderBookDetails(
        createOrderBookDto.products,
        savedOrderBook.id,
        createOrderBookDto.userId ? createOrderBookDto.userId : userId,
      );

      // 4. Xử lý thanh toán và giao dịch ví
      await this.processPaymentTransactions(
        createOrderBookDto,
        validationResult.wallet,
        savedOrderBook,
        calculationResult,
        createOrderBookDto.userId ? createOrderBookDto.userId : userId,
      );

      // 5. Xử lý hoa hồng
      await this.processCommissions(
        createOrderBookDto,
        calculationResult,
        createOrderBookDto.userId ? createOrderBookDto.userId : userId,
      );

      // 6. Hoàn thiện và trả về kết quả
      return await this.finalizeOrderCreation(savedOrderBook.id, createOrderBookDto.userId ? createOrderBookDto.userId : userId);
    } catch (error) {
      this.logger.error(`Lỗi khi tạo lệnh: ${error.message}`, error.stack);
      if (
        error instanceof BadRequestException ||
        error instanceof InsufficientBalanceException ||
        error instanceof InsufficientTokenException
      ) {
        throw error;
      }
      throw new InternalServerErrorException(
        `Không thể tạo lệnh: ${error.message}`,
      );
    }
  }

  /**
   * 1. Xác thực đầu vào và quyền truy cập
   */
  private async validateInputAndAuthentication(
    createOrderBookDto: CreateOrderBookDto,
    userId: string,
  ): Promise<{
    user: User;
    wallet: any;
    products: EcomProduct[];
  }> {
    let user: User | null = null;

    if (createOrderBookDto.userId) {
      user = await this.userRepository.findOne({
        where: { id: createOrderBookDto.userId },
      });
    } else {
      user = await this.userRepository.findOne({
        where: { id: userId },
      });
    }

    // Kiểm tra user tồn tại
    if (!user) {
      throw new BadRequestException(
        `Người dùng với ID ${userId} không tồn tại`,
      );
    }

    // Kiểm tra số dư ví của người dùng
    const wallet = await this.readWalletService.findByUserId(user.id);

    if (!wallet) {
      throw new BadRequestException(`Người dùng với ID ${user.id} không có ví`);
    }

    // Thu thập tất cả productIds để kiểm tra, loại bỏ giá trị null/undefined
    const productIds: string[] = createOrderBookDto.products
      .map((product) => product.productId)
      .filter((id): id is string => Boolean(id));

    if (productIds.length === 0) {
      throw new BadRequestException(
        'Phải có ít nhất một sản phẩm hợp lệ trong lệnh',
      );
    }

    const products = await this.ecomProductRepository.findBy({
      id: In(productIds),
    });

    if (products.length === 0) {
      throw new BadRequestException(
        'Phải có ít nhất một sản phẩm hợp lệ trong lệnh',
      );
    }

    return { user, wallet, products: products };
  }

  /**
   * 2. Tính toán các giá trị nghiệp vụ
   */
  private async calculateOrderPrices(
    createOrderBookDto: CreateOrderBookDto,
    validationResult: {
      products: EcomProduct[];
    },
  ): Promise<{
    totalPrice: number;
    processingPrice: number;
    depositPrice: number;
    settlementPrice: number;
    businessType: BusinessType;
  }> {
    const processingPrice = this.calculateProcessingPrice(createOrderBookDto, validationResult.products);
    // Tính tổng giá trị đơn hàng: đơn giá * số lượng, nếu đơn giá null thì lấy từ createOrderBookDto.price
    const totalPrice = this.calculateTotalPrice(createOrderBookDto, validationResult.products, processingPrice);
    // Xác định businessType và tính toán các khoản thanh toán theo nghiệp vụ
    if (createOrderBookDto.orderType === OrderType.BUY) {
      return this.calculateBuyOrderPrices(createOrderBookDto, totalPrice, processingPrice);
    } else if (createOrderBookDto.orderType === OrderType.SELL) {
      return this.calculateSellOrderPrices(totalPrice, processingPrice);
    }

    // Trường hợp mặc định (không nên xảy ra)
    throw new BadRequestException(
      `Loại lệnh không hợp lệ: ${createOrderBookDto.orderType}`,
    );
  }

  /**
   * Tính tổng giá trị đơn hàng
   * Công thức:
   * - Giá trị sản phẩm = (giá/oz * trọng lượng) * số lượng
   * - Nếu là bạc giao ngay, thêm phí gia công = trọng lượng * 50000 * số lượng
   */
  private calculateTotalPrice(
    createOrderBookDto: CreateOrderBookDto,
    products: EcomProduct[],
    processingPrice: number,
  ): number {
    return createOrderBookDto.products.reduce((sum, orderProduct) => {
      // Tìm thông tin sản phẩm từ database
      const product = products.find(p => p.id === orderProduct.productId);
      if (!product) {
        this.logger.warn(`Không tìm thấy thông tin sản phẩm ${orderProduct.productId} trong database`);
        return sum;
      }

      const price =
        orderProduct.price != null
          ? Number(orderProduct.price)
          : Number(createOrderBookDto.price);
      const quantity = orderProduct.quantity != null ? Number(orderProduct.quantity) : 0;
      const weight = product.weight != null ? Number(product.weight) : 0;

      if (!isNaN(price) && !isNaN(quantity) && !isNaN(weight)) {
        // Tính giá trị sản phẩm: (giá/oz * trọng lượng) * số lượng
        let productTotal = (price * weight) * quantity;

        // Thêm phí gia công cho bạc giao ngay: trọng lượng * 50000 * số lượng
        if (createOrderBookDto.businessType === BusinessType.IMMEDIATE_DELIVERY) {
          productTotal += processingPrice;
          this.logger.log(
            `Phí gia công cho sản phẩm ${product.productName || product.id}: ${processingPrice} (${weight}oz * 50000 * ${quantity})`,
          );
        }

        this.logger.log(
          `Tính giá cho sản phẩm ${product.productName || product.id}: ` +
          `${price} * ${weight}oz * ${quantity} = ${productTotal}`,
        );

        return sum + productTotal;
      }
      return sum;
    }, 0);
  }

  private calculateProcessingPrice(
    createOrderBookDto: CreateOrderBookDto,
    products: EcomProduct[],
  ): number {
    return createOrderBookDto.products.reduce((sum, orderProduct) => {
      const product = products.find(p => p.id === orderProduct.productId);
      if (!product) {
        this.logger.warn(`Không tìm thấy thông tin sản phẩm ${orderProduct.productId} trong database`);
        return sum;
      }
      const weight = product.weight != null ? Number(product.weight) : 0;
      const quantity = orderProduct.quantity != null ? Number(orderProduct.quantity) : 0;

      const processingPrice = weight * 50000 * quantity;
      this.logger.log(
        `Phí gia công cho sản phẩm ${product.productName || product.id}: ${processingPrice} (${weight}oz * 50000 * ${quantity})`,
      );
      return sum + processingPrice;
    }, 0);
  }

  /**
   * Tính toán cho lệnh MUA (BUY)
   * TH1: Mua bán thông thường → thanh toán 10% tiền cọc
   * TH2: Mua bạc giao ngay → thanh toán 100% giá trị
   */
  private calculateBuyOrderPrices(
    createOrderBookDto: CreateOrderBookDto,
    totalPrice: number,
    processingPrice: number,
  ): {
    totalPrice: number;
    depositPrice: number;
    processingPrice: number;
    settlementPrice: number;
    businessType: BusinessType;
  } {
    const businessType = createOrderBookDto.businessType || BusinessType.NORMAL;

    if (businessType === BusinessType.IMMEDIATE_DELIVERY) {
      // TH2: Mua bạc giao ngay - thanh toán 100% giá trị
      this.logger.log(
        `Lệnh MUA bạc giao ngay: thanh toán 100% giá trị (${totalPrice}), phí gia công (${processingPrice})`,
      );
      return {
        totalPrice,
        depositPrice: 0, // Không có tiền cọc
        processingPrice: processingPrice, // Thêm phí gia công
        settlementPrice: totalPrice, // Thanh toán toàn bộ
        businessType: BusinessType.IMMEDIATE_DELIVERY,
      };
    } else {
      // TH1: Mua bán thông thường - thanh toán 10% tiền cọc
      const depositAmount = totalPrice * 0.1;
      this.logger.log(
        `Lệnh MUA thông thường: thanh toán 10% tiền cọc (${depositAmount})`,
      );
      return {
        totalPrice,
        depositPrice: depositAmount,
        processingPrice: 0, // Không có phí gia công
        settlementPrice: 0, // Sẽ tính sau khi tất toán
        businessType: BusinessType.NORMAL,
      };
    }
  }

  /**
   * Tính toán cho lệnh BÁN (SELL)
   * TH1: Thanh toán luôn 10% đặt cọc
   * TH2: Không có giao ngay (luôn là bán khống)
   */
  private calculateSellOrderPrices(totalPrice: number, processingPrice: number): {
    totalPrice: number;
    processingPrice: number;
    depositPrice: number;
    settlementPrice: number;
    businessType: BusinessType;
  } {
    // Lệnh bán luôn là bán khống, thanh toán 10% đặt cọc
    const depositPrice = totalPrice * 0.1;
    this.logger.log(
      `Lệnh BÁN khống: thanh toán 10% tiền cọc (${depositPrice})`,
    );

    return {
      totalPrice,
      processingPrice: 0, // Không có phí gia công cho lệnh bán
      depositPrice: depositPrice,
      settlementPrice: 0, // Sẽ tính sau khi tất toán
      businessType: BusinessType.NORMAL, // Luôn là NORMAL cho lệnh bán
    };
  }

  /**
   * 3. Tạo entity OrderBook
   */
  private async createOrderBookEntity(
    createOrderBookDto: CreateOrderBookDto,
    userId: string,
    calculationResult: {
      totalPrice: number;
      depositPrice: number;
      settlementPrice: number;
      processingPrice: number;
      businessType: BusinessType;
    },
  ): Promise<OrderBook> {
    // Tạo entity cơ bản với thông tin chung
    const orderBook = this.createBaseOrderBookEntity(
      createOrderBookDto,
      userId,
      calculationResult,
    );

    // Thiết lập trạng thái và giá trị theo loại lệnh cụ thể
    if (createOrderBookDto.orderType === OrderType.BUY) {
      this.configureBuyOrderEntity(orderBook, calculationResult);
    } else if (createOrderBookDto.orderType === OrderType.SELL) {
      this.configureSellOrderEntity(orderBook, calculationResult);
    } else {
      throw new BadRequestException(
        `Loại lệnh không hợp lệ: ${createOrderBookDto.orderType}`,
      );
    }

    return orderBook;
  }

  /**
   * Tạo entity OrderBook cơ bản với thông tin chung
   */
  private createBaseOrderBookEntity(
    createOrderBookDto: CreateOrderBookDto,
    userId: string,
    calculationResult: {
      totalPrice: number;
      depositPrice: number;
      settlementPrice: number;
      processingPrice: number;
      businessType: BusinessType;
    },
  ): OrderBook {
    const orderBook = new OrderBook();

    // Thông tin cơ bản
    orderBook.userId = userId;
    orderBook.orderType = createOrderBookDto.orderType;
    orderBook.totalPrice = calculationResult.totalPrice;
    orderBook.businessType = calculationResult.businessType;
    orderBook.processingPrice = calculationResult.processingPrice;
    orderBook.storageFee = 0;
    orderBook.createdBy = userId;
    orderBook.updatedBy = userId;
    orderBook.contractNumber = `HD-${Date.now()}`;

    // Thiết lập deadline thanh toán là ngày hiện tại + 15 ngày
    const settlementDate = new Date();
    settlementDate.setDate(settlementDate.getDate() + 15);
    orderBook.settlementDeadline = settlementDate;

    return orderBook;
  }

  /**
   * Cấu hình entity cho lệnh MUA
   * TH1: Mua thông thường → trạng thái DEPOSITED, chờ tất toán
   * TH2: Mua giao ngay → trạng thái COMPLETED, chờ phê duyệt
   */
  private configureBuyOrderEntity(
    orderBook: OrderBook,
    calculationResult: {
      totalPrice: number;
      depositPrice: number;
      settlementPrice: number;
      processingPrice: number;
      businessType: BusinessType;
    },
  ): void {
    if (calculationResult.businessType === BusinessType.IMMEDIATE_DELIVERY) {
      // TH2: Lệnh mua bạc giao ngay
      orderBook.status = OrderStatus.COMPLETED;
      orderBook.approveStatus = ApproveStatus.PENDING;
      orderBook.settlementAt = new Date();
      orderBook.depositPrice = 0;
      orderBook.settlementPrice = calculationResult.totalPrice;
      orderBook.processingPrice = calculationResult.processingPrice;

      this.logger.log(
        `Cấu hình lệnh MUA giao ngay: trạng thái COMPLETED, chờ phê duyệt, phí gia công ${calculationResult.processingPrice}`,
      );
    } else {
      // TH1: Lệnh mua ký quỹ thông thường
      orderBook.status = OrderStatus.DEPOSITED;
      orderBook.depositPrice = calculationResult.depositPrice;
      orderBook.settlementPrice = calculationResult.settlementPrice;
      orderBook.processingPrice = 0; // Không có phí gia công cho lệnh thông thường

      this.logger.log(
        `Cấu hình lệnh MUA ký quỹ: trạng thái DEPOSITED, tiền cọc ${calculationResult.depositPrice}`,
      );
    }
  }

  /**
   * Cấu hình entity cho lệnh BÁN
   * TH1: Bán khống → trạng thái DEPOSITED, thanh toán 10% cọc
   */
  private configureSellOrderEntity(
    orderBook: OrderBook,
    calculationResult: {
      totalPrice: number;
      depositPrice: number;
      settlementPrice: number;
      processingPrice: number;
      businessType: BusinessType;
    },
  ): void {
    // Lệnh bán luôn là bán khống với trạng thái DEPOSITED
    orderBook.status = OrderStatus.DEPOSITED;
    orderBook.depositPrice = calculationResult.depositPrice;
    orderBook.settlementPrice = calculationResult.settlementPrice;
    orderBook.processingPrice = 0; // Không có phí gia công cho lệnh bán

    this.logger.log(
      `Cấu hình lệnh BÁN khống: trạng thái DEPOSITED, tiền cọc ${calculationResult.depositPrice}`,
    );
  }

  /**
   * 3b. Tạo chi tiết OrderBook
   */
  private async createOrderBookDetails(
    products: any[],
    orderBookId: string,
    userId: string,
  ): Promise<void> {
    // Validation đầu vào
    if (!products || products.length === 0) {
      this.logger.warn(
        `Không có sản phẩm để tạo chi tiết cho OrderBook ${orderBookId}`,
      );
      return;
    }

    // Tạo tất cả chi tiết và lưu batch để tối ưu hiệu suất
    const orderBookDetails = products.map((product) =>
      this.createSingleOrderBookDetail(product, orderBookId, userId),
    );

    // Lưu tất cả chi tiết cùng lúc thay vì từng cái một
    await this.orderBookDetailRepository.save(orderBookDetails);

    this.logger.log(
      `Đã tạo ${orderBookDetails.length} chi tiết cho OrderBook ${orderBookId}`,
    );
  }

  /**
   * Tạo một chi tiết OrderBook từ thông tin sản phẩm
   */
  private createSingleOrderBookDetail(
    productInfo: any,
    orderBookId: string,
    userId: string,
  ): OrderBookDetail {
    const detail = new OrderBookDetail();

    // Thông tin cơ bản
    detail.orderBookId = orderBookId;
    detail.productId = productInfo.productId || '';
    detail.createdBy = userId;
    detail.updatedBy = userId;

    // Xử lý giá sản phẩm (không cho phép giá trị âm)
    detail.price = parseNumericValue(productInfo.price, {
      defaultValue: 0,
      allowNegative: false,
      fieldName: 'price',
      logger: this.logger,
    });

    // Xử lý số lượng sản phẩm (không cho phép giá trị âm)
    detail.quantity = parseNumericValue(productInfo.quantity, {
      defaultValue: 0,
      allowNegative: false,
      fieldName: 'quantity',
      logger: this.logger,
    });

    // Tính tổng tiền
    detail.totalPrice = detail.price * detail.quantity;

    return detail;
  }

  /**
   * 4. Xử lý thanh toán và giao dịch ví
   */
  private async processPaymentTransactions(
    createOrderBookDto: CreateOrderBookDto,
    wallet: any,
    orderBook: OrderBook,
    calculationResult: {
      totalPrice: number;
      depositPrice: number;
      settlementPrice: number;
      businessType: BusinessType;
    },
    userId: string,
  ): Promise<void> {
    // Kiểm tra số dư ví trước khi thực hiện giao dịch
    await this.validateWalletBalance(
      createOrderBookDto,
      wallet,
      calculationResult,
      userId,
    );

    // Xử lý thanh toán theo loại lệnh cụ thể
    if (createOrderBookDto.orderType === OrderType.BUY) {
      await this.processBuyOrderPayment(
        createOrderBookDto,
        wallet,
        orderBook,
        calculationResult,
        userId,
      );
    } else if (createOrderBookDto.orderType === OrderType.SELL) {
      await this.processSellOrderPayment(
        wallet,
        orderBook,
        calculationResult,
        userId,
      );
    } else {
      throw new BadRequestException(
        `Loại lệnh không hợp lệ: ${createOrderBookDto.orderType}`,
      );
    }
  }

  /**
   * Xử lý thanh toán cho lệnh MUA
   * TH1: Mua thông thường → thanh toán 10% tiền cọc
   * TH2: Mua giao ngay → thanh toán 100% giá trị + thêm tài sản
   */
  private async processBuyOrderPayment(
    createOrderBookDto: CreateOrderBookDto,
    wallet: any,
    orderBook: OrderBook,
    calculationResult: {
      totalPrice: number;
      depositPrice: number;
      settlementPrice: number;
      businessType: BusinessType;
    },
    userId: string,
  ): Promise<void> {
    if (calculationResult.businessType === BusinessType.IMMEDIATE_DELIVERY) {
      // TH2: Lệnh MUA BẠC GIAO NGAY - thanh toán 100% giá trị
      await this.processImmediateDeliveryPayment(
        createOrderBookDto,
        wallet,
        orderBook,
        calculationResult.totalPrice,
        userId,
      );
    } else if (calculationResult.depositPrice > 0) {
      // TH1: Lệnh MUA ký quỹ thông thường - thanh toán 10% tiền cọc
      await this.processDepositPayment(
        wallet,
        orderBook,
        calculationResult.depositPrice,
        `Đặt cọc cho lệnh mua #${orderBook.id}`,
        userId,
      );

      this.logger.log(
        `Đã xử lý tiền cọc cho lệnh MUA ký quỹ: ${calculationResult.depositPrice}`,
      );
    }
  }

  /**
   * Xử lý thanh toán cho lệnh BÁN
   * TH1: Bán khống → thanh toán 10% tiền cọc (duy nhất)
   */
  private async processSellOrderPayment(
    wallet: any,
    orderBook: OrderBook,
    calculationResult: {
      depositPrice: number;
    },
    userId: string,
  ): Promise<void> {
    if (calculationResult.depositPrice > 0) {
      // Lệnh BÁN khống - thanh toán 10% tiền cọc
      await this.processDepositPayment(
        wallet,
        orderBook,
        calculationResult.depositPrice,
        `Đặt cọc cho lệnh bán khống #${orderBook.id}`,
        userId,
      );

      this.logger.log(
        `Đã xử lý tiền cọc cho lệnh BÁN khống: ${calculationResult.depositPrice}`,
      );
    }
  }

  /**
   * Xử lý thanh toán giao ngay (100% giá trị)
   */
  private async processImmediateDeliveryPayment(
    createOrderBookDto: CreateOrderBookDto,
    wallet: any,
    orderBook: OrderBook,
    totalAmount: number,
    userId: string,
  ): Promise<void> {
    // Trừ toàn bộ số tiền từ ví
    await this.updateWalletService.updateBalance(
      wallet.id,
      TransactionType.DEBIT,
      totalAmount,
      `ORD-${orderBook.businessCode || orderBook.id}`,
      `Thanh toán 100% giá trị lệnh mua bạc giao ngay #${orderBook.id}`,
      userId,
    );

    this.logger.log(
      `Đã trừ ${totalAmount} (100% giá trị) từ ví người dùng ${userId} cho lệnh MUA BẠC GIAO NGAY`,
    );

    // Thêm product vào tài sản của người dùng
    await this.processPhysicalBuyOrderEcomProductAssets(
      userId,
      createOrderBookDto.products,
      userId,
    );

    // Thiết lập trạng thái phê duyệt
    orderBook.approveStatus = ApproveStatus.PENDING;
    await this.orderBookRepository.save(orderBook);

    this.logger.log(
      `Đã cập nhật trạng thái phê duyệt PENDING cho lệnh giao ngay #${orderBook.id}`,
    );
  }

  /**
   * Xử lý thanh toán tiền cọc (10% giá trị)
   */
  private async processDepositPayment(
    wallet: any,
    orderBook: OrderBook,
    depositAmount: number,
    notes: string,
    userId: string,
  ): Promise<void> {
    await this.updateWalletService.updateBalance(
      wallet.id,
      TransactionType.DEBIT,
      depositAmount,
      `ORD-${orderBook.businessCode || orderBook.id}`,
      notes,
      userId,
    );

    this.logger.log(
      `Đã trừ ${depositAmount} tiền đặt cọc từ ví người dùng ${userId}`,
    );
  }

  /**
   * 4a. Kiểm tra số dư ví
   */
  private async validateWalletBalance(
    createOrderBookDto: CreateOrderBookDto,
    wallet: any,
    calculationResult: {
      totalPrice: number;
      depositPrice: number;
      businessType: BusinessType;
    },
    userId: string,
  ): Promise<void> {
    if (calculationResult.businessType === BusinessType.IMMEDIATE_DELIVERY) {
      // Trường hợp lệnh MUA BẠC GIAO NGAY: Kiểm tra số dư ví cho 100% giá trị
      if (wallet.balance < calculationResult.totalPrice) {
        throw new InsufficientBalanceException(
          wallet.balance,
          calculationResult.totalPrice,
          {
            userId: userId,
            orderType: createOrderBookDto.orderType,
            walletId: wallet.id,
          },
        );
      }
    } else if (calculationResult.depositPrice > 0) {
      // Kiểm tra số dư ví cho tiền đặt cọc
      if (wallet.balance < calculationResult.depositPrice) {
        throw new InsufficientBalanceException(
          wallet.balance,
          calculationResult.depositPrice,
          {
            userId: userId,
            orderType: createOrderBookDto.orderType,
            walletId: wallet.id,
          },
        );
      }
    }
  }

  /**
   * 5. Xử lý hoa hồng
   */
  private async processCommissions(
    createOrderBookDto: CreateOrderBookDto,
    calculationResult: {
      totalPrice: number;
      depositPrice: number;
      businessType: BusinessType;
    },
    userId: string,
  ): Promise<void> {
    try {
      // Xử lý hoa hồng theo loại lệnh cụ thể
      if (createOrderBookDto.orderType === OrderType.BUY) {
        await this.processBuyOrderCommissions(calculationResult, userId);
      } else if (createOrderBookDto.orderType === OrderType.SELL) {
        await this.processSellOrderCommissions(calculationResult, userId);
      } else {
        this.logger.log(
          `Loại lệnh ${createOrderBookDto.orderType} không hỗ trợ xử lý hoa hồng`,
        );
      }
    } catch (commissionError) {
      this.logger.error(
        `Lỗi khi xử lý hoa hồng: ${commissionError.message}`,
        commissionError.stack,
      );
      // Không throw lỗi để không ảnh hưởng đến luồng chính
    }
  }

  /**
   * Xử lý hoa hồng cho lệnh MUA
   * TH1: Mua thông thường → hoa hồng từ phí đặt cọc (40% của 10% tiền cọc)
   * TH2: Mua giao ngay → hoa hồng từ phí thanh toán (40% của 100% giá trị)
   */
  private async processBuyOrderCommissions(
    calculationResult: {
      totalPrice: number;
      depositPrice: number;
      businessType: BusinessType;
    },
    userId: string,
  ): Promise<void> {
    if (calculationResult.businessType === BusinessType.IMMEDIATE_DELIVERY) {
      // TH2: Lệnh MUA BẠC GIAO NGAY - hoa hồng từ phí thanh toán
      await this.processSettlementCommission(
        calculationResult.totalPrice,
        userId,
      );
    } else if (calculationResult.depositPrice > 0) {
      // TH1: Lệnh MUA ký quỹ thông thường - hoa hồng từ phí đặt cọc
      await this.processDepositCommission(
        calculationResult.depositPrice,
        userId,
      );
    } else {
      this.logger.log(`Lệnh MUA không có phí để xử lý hoa hồng`);
    }
  }

  /**
   * Xử lý hoa hồng cho lệnh BÁN
   * TH1: Bán khống → hoa hồng từ phí đặt cọc (40% của 10% tiền cọc)
   */
  private async processSellOrderCommissions(
    calculationResult: {
      depositPrice: number;
    },
    userId: string,
  ): Promise<void> {
    if (calculationResult.depositPrice > 0) {
      // Lệnh BÁN khống - hoa hồng từ phí đặt cọc
      await this.processDepositCommission(
        calculationResult.depositPrice,
        userId,
      );
    } else {
      this.logger.log(`Lệnh BÁN không có phí đặt cọc để xử lý hoa hồng`);
    }
  }

  /**
   * Xử lý hoa hồng từ phí thanh toán (cho lệnh giao ngay)
   * Tỷ lệ hoa hồng: 40% của phí thanh toán
   */
  private async processSettlementCommission(
    totalAmount: number,
    userId: string,
  ): Promise<void> {
    this.logger.log(
      `Xử lý hoa hồng phí thanh toán cho lệnh giao ngay: ${totalAmount} (tỷ lệ 40%)`,
    );

    await this.commissionProcessorService.processSettlementFeeCommission(
      userId,
      totalAmount,
      userId,
    );

    this.logger.log(
      `Đã xử lý thành công hoa hồng phí thanh toán: ${totalAmount}`,
    );
  }

  /**
   * Xử lý hoa hồng từ phí đặt cọc (cho lệnh ký quỹ và bán khống)
   * Tỷ lệ hoa hồng: 40% của phí đặt cọc
   */
  private async processDepositCommission(
    depositAmount: number,
    userId: string,
  ): Promise<void> {
    this.logger.log(`Xử lý hoa hồng phí đặt cọc: ${depositAmount} (tỷ lệ 40%)`);

    await this.commissionProcessorService.processDepositFeeCommission(
      userId,
      depositAmount,
      userId,
    );

    this.logger.log(
      `Đã xử lý thành công hoa hồng phí đặt cọc: ${depositAmount}`,
    );
  }

  /**
   * 6. Hoàn thiện tạo lệnh và trả về kết quả
   */
  private async finalizeOrderCreation(
    orderBookId: string,
    userId: string,
  ): Promise<OrderBookDto> {
    // Tải lại lệnh kèm theo chi tiết
    const orderWithDetails = await this.orderBookRepository.findOne({
      where: { id: orderBookId },
      relations: ['details', 'details.product', 'user'],
    });

    if (!orderWithDetails) {
      throw new NotFoundException(
        `Không tìm thấy lệnh vừa tạo với ID: ${orderBookId}`,
      );
    }

    // Phát sự kiện
    const dto = this.toDto(orderWithDetails);
    this.eventEmitter.emit(this.EVENT_ORDER_CREATED, {
      orderId: orderBookId,
      userId: userId,
      timestamp: new Date(),
      data: dto,
    });

    this.logger.log(
      `Đã tạo thành công lệnh ${orderWithDetails.orderType} với ID: ${orderBookId}`,
    );
    return dto;
  }

  /**
   * Tạo hàng loạt lệnh
   * @param createOrderBookDtos Danh sách dữ liệu lệnh cần tạo
   * @param userId ID của người dùng thực hiện thao tác
   * @returns Danh sách lệnh đã tạo
   * @throws BadRequestException nếu dữ liệu không hợp lệ
   * @throws InternalServerErrorException nếu có lỗi khác xảy ra
   */
  @Transactional()
  async bulkCreate(
    createOrderBookDtos: CreateOrderBookDto[],
    userId: string,
  ): Promise<OrderBookDto[]> {
    try {
      this.logger.log(`Đang tạo hàng loạt ${createOrderBookDtos.length} lệnh`);
      if (createOrderBookDtos.length === 0) return [];

      // Thu thập tất cả userIds (không cần thiết vì tất cả đều dùng userId từ tham số)
      const userIds = [userId];

      // Thu thập tất cả tokenIds từ tất cả các lệnh
      const allEcomProductIds = new Set<string>();
      createOrderBookDtos.forEach((dto) => {
        if (dto.products && dto.products.length > 0) {
          dto.products.forEach((ecomProduct) => {
            if (ecomProduct.productId) {
              allEcomProductIds.add(ecomProduct.productId);
            }
          });
        }
      });
      const ecomProductIds = [...allEcomProductIds];

      const users = await this.userRepository.findBy({ id: In(userIds) });
      const ecomProducts = await this.ecomProductRepository.findBy({
        id: In(ecomProductIds),
      });

      if (users.length !== userIds.length) {
        const foundUserIds = users.map((u) => u.id);
        const notFoundUserIds = userIds.filter(
          (id) => !foundUserIds.includes(id),
        );
        throw new NotFoundException(
          `Không tìm thấy người dùng: ${notFoundUserIds.join(', ')}`,
        );
      }

      if (ecomProducts.length !== ecomProductIds.length) {
        const foundEcomProductIds = ecomProducts.map((t) => t.id);
        const notFoundEcomProductIds = ecomProductIds.filter(
          (id) => !foundEcomProductIds.includes(id),
        );
        throw new NotFoundException(
          `Không tìm thấy sản phẩm: ${notFoundEcomProductIds.join(', ')}`,
        );
      }

      const orders: OrderBookDto[] = [];
      for (const dto of createOrderBookDtos) {
        // Tạo đơn lệnh riêng lẻ
        const order = await this.create(dto, userId);
        orders.push(order);
      }

      this.logger.log(`Đã tạo thành công ${orders.length} lệnh`);
      return orders;
    } catch (error) {
      this.logger.error(
        `Lỗi khi tạo hàng loạt lệnh: ${error.message}`,
        error.stack,
      );
      if (
        error instanceof BadRequestException ||
        error instanceof NotFoundException ||
        error instanceof InsufficientBalanceException ||
        error instanceof InsufficientTokenException
      ) {
        throw error;
      }
      throw new InternalServerErrorException(
        `Không thể tạo hàng loạt lệnh: ${error.message}`,
      );
    }
  }

  /**
   * Nhân bản một lệnh
   * @param id ID của lệnh cần nhân bản
   * @param userId ID của người dùng thực hiện thao tác
   * @returns Thông tin lệnh mới đã tạo
   * @throws NotFoundException nếu không tìm thấy lệnh gốc
   * @throws InternalServerErrorException nếu có lỗi khác xảy ra
   */
  @Transactional()
  async duplicate(id: string, userId: string): Promise<OrderBookDto> {
    try {
      this.logger.log(`Đang nhân bản lệnh với ID: ${id}`);

      // Tìm lệnh gốc
      const originalOrder = await this.orderBookRepository.findOne({
        where: { id, isDeleted: false },
        relations: ['details', 'details.token'],
      });

      if (!originalOrder) {
        throw new NotFoundException(`Không tìm thấy lệnh với ID ${id}`);
      }

      // Tạo order book mới
      const newOrderBook = new OrderBook();
      newOrderBook.userId = originalOrder.userId;
      newOrderBook.orderType = originalOrder.orderType;
      newOrderBook.totalPrice = originalOrder.totalPrice;

      // Sao chép businessType nếu có
      if (originalOrder.businessType) {
        newOrderBook.businessType = originalOrder.businessType;
      }
      // Đặt trạng thái dựa vào loại lệnh
      if (
        newOrderBook.orderType === OrderType.BUY &&
        newOrderBook.businessType === BusinessType.IMMEDIATE_DELIVERY
      ) {
        // Lệnh mua bạc giao ngay: Đặt trạng thái là COMPLETED và trạng thái phê duyệt là WAIT_APPROVE
        newOrderBook.status = OrderStatus.COMPLETED;
        newOrderBook.approveStatus = ApproveStatus.PENDING;
        // Thiết lập ngày tất toán là ngày hiện tại
        newOrderBook.settlementAt = new Date();
      } else if (newOrderBook.orderType === OrderType.SELL) {
        // Tất cả lệnh bán đều là bán khống
        newOrderBook.status = OrderStatus.DEPOSITED;
        newOrderBook.businessType = BusinessType.NORMAL;
        this.logger.log(
          `Đặt businessType = SHORT_INVESTMENT và trạng thái WAIT_PAYMENT cho lệnh bán khống`,
        );
      } else {
        // Lệnh mua ký quỹ
        newOrderBook.status = OrderStatus.DEPOSITED;
      }
      newOrderBook.createdBy = userId;
      newOrderBook.updatedBy = userId;

      // Sao chép các trường liên quan đến contract
      if (originalOrder.contractNumber) {
        newOrderBook.contractNumber = `COPY-${originalOrder.contractNumber}`;
      } else {
        newOrderBook.contractNumber = `HD-COPY-${Date.now()}`;
      }

      // Xử lý depositAmount và settlementAmount dựa vào loại lệnh
      if (
        newOrderBook.orderType === OrderType.BUY &&
        newOrderBook.businessType === BusinessType.IMMEDIATE_DELIVERY
      ) {
        // Đối với bạc giao ngay, depositAmount = 0 và settlementAmount = totalPrice
        newOrderBook.depositPrice = 0;
        newOrderBook.settlementPrice = Number(originalOrder.totalPrice);
      } else {
        // Đối với các loại lệnh khác, sao chép từ lệnh gốc
        if (originalOrder.depositPrice) {
          newOrderBook.depositPrice = Number(originalOrder.depositPrice);
        }
        if (originalOrder.settlementPrice) {
          newOrderBook.settlementPrice = Number(originalOrder.settlementPrice);
        }
      }
      if (originalOrder.storageFee) {
        newOrderBook.storageFee = Number(originalOrder.storageFee);
      }
      if (originalOrder.settlementDeadline) {
        newOrderBook.settlementDeadline = originalOrder.settlementDeadline;
      }

      const savedOrder = await this.orderBookRepository.save(newOrderBook);

      // Tạo chi tiết cho lệnh mới
      if (originalOrder.details && originalOrder.details.length > 0) {
        for (const detail of originalOrder.details) {
          const newDetail = new OrderBookDetail();
          newDetail.orderBookId = savedOrder.id;
          newDetail.productId = detail.productId;
          newDetail.price = detail.price || 0;
          newDetail.quantity = detail.quantity || 0;
          newDetail.totalPrice = (detail.price || 0) * (detail.quantity || 0);
          newDetail.createdBy = userId;
          newDetail.updatedBy = userId;

          await this.orderBookDetailRepository.save(newDetail);
        }
      }

      // Tải lệnh đã tạo kèm chi tiết
      const orderWithDetails = await this.orderBookRepository.findOne({
        where: { id: savedOrder.id },
        relations: ['details', 'details.token', 'user'],
      });

      if (!orderWithDetails) {
        throw new NotFoundException(
          `Không tìm thấy lệnh vừa tạo với ID: ${savedOrder.id}`,
        );
      }

      // Phát sự kiện
      const dto = this.toDto(orderWithDetails);
      this.eventEmitter.emit(this.EVENT_ORDER_DUPLICATED, {
        originalOrderId: id,
        newOrderId: savedOrder.id,
        userId: userId,
        timestamp: new Date(),
        data: dto,
      });

      this.logger.log(
        `Đã nhân bản thành công lệnh ${id} thành lệnh mới ${savedOrder.id}`,
      );
      return dto;
    } catch (error) {
      this.logger.error(`Lỗi khi nhân bản lệnh: ${error.message}`, error.stack);
      if (
        error instanceof NotFoundException ||
        error instanceof BadRequestException ||
        error instanceof InsufficientBalanceException ||
        error instanceof InsufficientTokenException
      ) {
        throw error;
      }
      throw new InternalServerErrorException(
        `Không thể nhân bản lệnh: ${error.message}`,
      );
    }
  }

  // Phương thức processSellOrderTokenAssets đã được loại bỏ vì không còn sử dụng

  /**
   * Xử lý token assets khi mua bạc vật chất
   * @param userId ID người dùng
   * @param ecomProducts Thông tin sản phẩm mua
   * @param operatorId ID người thực hiện thao tác
   */
  private async processPhysicalBuyOrderEcomProductAssets(
    userId: string,
    ecomProducts: any[],
    operatorId: string,
  ): Promise<void> {
    try {
      this.logger.log(
        `Xử lý token assets cho lệnh mua bạc vật chất của người dùng ${userId}`,
      );

      for (const ecomProductDetail of ecomProducts) {
        const ecomProductId = ecomProductDetail.productId;
        const volume = parseNumericValue(ecomProductDetail.quantity, {
          defaultValue: 0,
          allowNegative: false,
          fieldName: 'quantity',
          logger: this.logger,
        });

        this.logger.log(
          `🔧 [TOKEN_ASSET] Xử lý token asset cho ecomProduct ${ecomProductId}, số lượng: ${volume}`,
        );

        // Tìm token asset hiện có của người dùng
        this.logger.log(
          `🔍 [TOKEN_ASSET] Tìm kiếm token asset hiện có cho userId=${userId}, ecomProductId=${ecomProductId}`,
        );
        const existingTokenAsset =
          await this.tokenAssetService.findByUserAndEcomProduct(
            userId,
            ecomProductId,
          );

        this.logger.log(
          `📊 [TOKEN_ASSET] Kết quả tìm kiếm: ${existingTokenAsset ? `Found ID=${existingTokenAsset.id}` : 'Not found'}`,
        );

        if (existingTokenAsset) {
          // Nếu đã có token asset, cập nhật số lượng
          const currentAmount = parseNumericValue(existingTokenAsset.amount, {
            defaultValue: 0,
            allowNegative: false,
            fieldName: 'currentAmount',
            logger: this.logger,
          });
          const newAmount = currentAmount + volume;

          await this.tokenAssetService.update(existingTokenAsset.id, {
            id: existingTokenAsset.id,
            amount: newAmount,
            updatedBy: operatorId,
          });

          this.logger.log(
            `Đã cập nhật token asset ${existingTokenAsset.id} cho token ${ecomProductId}, số lượng mới: ${newAmount}`,
          );
        } else {
          // Nếu chưa có token asset, tạo mới
          this.logger.log(
            `🚀 [TOKEN_ASSET] Tạo mới token asset cho ecomProduct ${ecomProductId}`,
          );
          try {
            const createTokenAssetDto: CreateAssetDto = {
              userId: userId,
              productId: ecomProductId,
              amount: volume,
              // tokenId sẽ được xử lý trong service nếu cần
            };

            this.logger.log(
              `📝 [TOKEN_ASSET] CreateTokenAssetDto:`,
              JSON.stringify(createTokenAssetDto, null, 2),
            );

            const newTokenAsset =
              await this.tokenAssetService.create(createTokenAssetDto, operatorId);
            this.logger.log(
              `✅ [TOKEN_ASSET] Đã tạo mới token asset ${newTokenAsset.id} cho ecomProduct ${ecomProductId}, số lượng: ${volume}`,
            );
          } catch (createError) {
            this.logger.error(
              `❌ [TOKEN_ASSET] Lỗi tạo token asset cho ecomProduct ${ecomProductId}:`,
            );
            this.logger.error(`   Error message: ${createError.message}`);
            this.logger.error(`   Error stack: ${createError.stack}`);
            this.logger.error(`   CreateTokenAssetDto:`, {
              userId,
              ecomProductId,
              amount: volume,
              operatorId,
            });
            // Tiếp tục với sản phẩm tiếp theo thay vì fail toàn bộ order
            continue;
          }
        }
      }
    } catch (error) {
      this.logger.error(
        `Lỗi khi xử lý token assets cho lệnh mua bạc vật chất: ${error.message}`,
        error.stack,
      );

      // Log chi tiết để debug
      this.logger.error(`Chi tiết lỗi token asset processing:`, {
        userId,
        ecomProducts,
        operatorId,
        errorMessage: error.message,
        errorStack: error.stack,
      });

      // Không throw error để không làm fail toàn bộ IMMEDIATE_DELIVERY order
      // Chỉ log warning và tiếp tục
      this.logger.warn(
        `Bỏ qua lỗi token asset processing cho IMMEDIATE_DELIVERY order. Order vẫn được tạo thành công.`,
      );
    }
  }
}

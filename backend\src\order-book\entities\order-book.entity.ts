import { ApiProperty } from '@nestjs/swagger';
import {
  IsDate,
  IsDecimal,
  IsEnum,
  IsNotEmpty,
  IsOptional,
  IsString,
} from 'class-validator';
import { Column, Entity, <PERSON>inC<PERSON>umn, ManyToOne, OneToMany } from 'typeorm';
import { User } from '../../users/entities/user.entity';
import { OrderType } from '../enums/order-type.enum';
import { OrderStatus } from '../enums/order-status.enum';
import { BusinessType } from '../enums/business-type.enum';
import { ApproveStatus } from '../enums/approve-status.enum';
import { OrderBookDetail } from './order-book-detail.entity';
import { BaseEntity } from '../../common/entities/base.entity';

@Entity('order_book')
export class OrderBook extends BaseEntity {
  @ApiProperty({
    description: 'ID người dùng',
    example: '550e8400-e29b-41d4-a716-************',
  })
  @IsNotEmpty()
  @IsString()
  @Column({ type: 'uuid', nullable: false, name: 'user_id' })
  userId: string;

  @ApiProperty({
    description: 'Loại lệnh (mua/bán)',
    enum: OrderType,
    example: OrderType.BUY,
  })
  @IsNotEmpty()
  @IsEnum(OrderType)
  @Column({
    type: 'varchar',
    length: 50,
    nullable: false,
    default: OrderType.BUY,
    name: 'order_type',
  })
  orderType: OrderType;

  @ApiProperty({
    description: 'Trạng thái lệnh (mặc định là đã đặt cọc)',
    enum: OrderStatus,
    example: OrderStatus.DEPOSITED,
  })
  @IsNotEmpty()
  @IsEnum(OrderStatus)
  @Column({
    type: 'varchar',
    length: 50,
    nullable: false,
    default: OrderStatus.DEPOSITED,
    name: 'status',
  })
  status: OrderStatus;

  @ApiProperty({
    description: 'Loại hình giao dịch',
    enum: BusinessType,
    example: BusinessType.NORMAL,
  })
  @IsOptional()
  @IsEnum(BusinessType)
  @Column({
    type: 'varchar',
    length: 50,
    nullable: false,
    default: BusinessType.NORMAL,
    name: 'business_type',
  })
  businessType: BusinessType;

  @ApiProperty({ description: 'Tổng tiền đơn hàng', example: '5000000.00' })
  @IsOptional()
  @IsDecimal({ decimal_digits: '2' })
  @Column({
    type: 'decimal',
    precision: 20,
    scale: 2,
    nullable: true,
    name: 'total_price',
  })
  totalPrice: number;

  @ApiProperty({ description: 'Tổng tiền gia công', example: '5000000.00' })
  @IsOptional()
  @IsDecimal({ decimal_digits: '2' })
  @Column({
    type: 'decimal',
    precision: 20,
    scale: 2,
    nullable: true,
    name: 'processing_price',
  })
  processingPrice: number;

  @ApiProperty({
    description: 'Tổng tiền cọc',
    example: '1000000.00',
  })
  @IsOptional()
  @IsDecimal({ decimal_digits: '2' })
  @Column({
    type: 'decimal',
    precision: 20,
    scale: 2,
    nullable: true,
    name: 'deposit_price',
  })
  depositPrice: number;

  @ApiProperty({
    description: 'Phí lưu kho',
    example: '50000.00',
  })
  @IsOptional()
  @IsDecimal({ decimal_digits: '2' })
  @Column({
    type: 'decimal',
    precision: 20,
    scale: 2,
    nullable: true,
    name: 'storage_fee',
  })
  storageFee: number;

  @ApiProperty({
    description: 'Tổng tiền tất toán',
    example: '4000000.00',
  })
  @IsOptional()
  @IsDecimal({ decimal_digits: '2' })
  @Column({
    type: 'decimal',
    precision: 20,
    scale: 2,
    nullable: true,
    name: 'settlement_price',
  })
  settlementPrice: number;

  @ApiProperty({ description: 'Tổng tiền cuối', example: '5000000.00' })
  @IsOptional()
  @IsDecimal({ decimal_digits: '2' })
  @Column({
    type: 'decimal',
    precision: 20,
    scale: 2,
    nullable: true,
    name: 'total_price_final',
  })
  totalPriceFinal: number;

  @ApiProperty({
    description: 'Số hợp đồng',
    example: 'HD-2023-001',
  })
  @IsOptional()
  @IsString()
  @Column({
    type: 'varchar',
    length: 255,
    nullable: true,
    unique: true,
    name: 'contract_number',
  })
  contractNumber: string;

  @ApiProperty({
    description: 'Hạn chót tất toán',
    example: '2023-12-31T23:59:59Z',
  })
  @IsOptional()
  @IsDate()
  @Column({
    type: 'timestamp',
    nullable: true,
    name: 'settlement_deadline',
  })
  settlementDeadline: Date;

  @ApiProperty({
    description: 'Thời gian tất toán',
    example: '2023-12-15T10:30:00Z',
  })
  @IsOptional()
  @IsDate()
  @Column({
    type: 'timestamp',
    nullable: true,
    name: 'settlement_at',
  })
  settlementAt: Date;

  @ApiProperty({
    description: 'Trạng thái phê duyệt',
    enum: ApproveStatus,
    example: ApproveStatus.PENDING,
  })
  @IsOptional()
  @IsEnum(ApproveStatus)
  @Column({
    type: 'varchar',
    length: 50,
    nullable: false,
    default: ApproveStatus.PENDING,
    name: 'approve_status',
  })
  approveStatus: ApproveStatus;

  @ApiProperty({
    description: 'Thời gian phê duyệt',
    example: '2023-12-15T10:30:00Z',
  })
  @IsOptional()
  @IsDate()
  @Column({
    type: 'timestamp',
    nullable: true,
    name: 'approved_at',
  })
  approvedAt: Date;

  @ApiProperty({ description: 'Người dùng liên kết' })
  @ManyToOne(() => User)
  @JoinColumn({ name: 'user_id' })
  @ApiProperty({
    type: () => User,
    description: 'Người dùng liên quan đến đơn hàng',
  })
  user: User;

  /**
   * Ghi đè phương thức getEntityName từ BaseEntity
   */
  getEntityName(): string {
    return 'order_book';
  }

  @ApiProperty({ description: 'Chi tiết đơn hàng' })
  @OneToMany(() => OrderBookDetail, (detail) => detail.orderBook)
  @ApiProperty({
    type: () => OrderBookDetail,
    isArray: true,
    description: 'Chi tiết các sản phẩm trong đơn hàng',
  })
  details: OrderBookDetail[];
}

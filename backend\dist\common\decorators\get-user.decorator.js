"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.GetUser = void 0;
const common_1 = require("@nestjs/common");
exports.GetUser = (0, common_1.createParamDecorator)((propertyPath, ctx) => {
    const request = ctx.switchToHttp().getRequest();
    const user = request.user;
    if (!user) {
        return null;
    }
    if (!propertyPath) {
        return user;
    }
    const properties = propertyPath.split('.');
    let value = user;
    for (const prop of properties) {
        if (value && Object.prototype.hasOwnProperty.call(value, prop)) {
            value = value[prop];
        }
        else {
            return null;
        }
    }
    return value;
});
//# sourceMappingURL=get-user.decorator.js.map
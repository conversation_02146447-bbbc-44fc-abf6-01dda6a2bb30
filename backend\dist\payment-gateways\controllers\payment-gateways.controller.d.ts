import { Request, Response } from 'express';
import { PaymentGatewaysService, PaymentResult } from '../payment-gateways.service';
import { CreatePaymentDto } from '../dto/create-payment.dto';
import { PaymentGatewayType } from '../enums/payment-gateway-type.enum';
export declare class PaymentGatewaysController {
    private readonly paymentGatewaysService;
    private readonly logger;
    constructor(paymentGatewaysService: PaymentGatewaysService);
    createPayment(createPaymentDto: CreatePaymentDto): Promise<PaymentResult>;
    handleVnpayReturn(query: Record<string, string>, res: Response): Promise<void>;
    handleVnpayIpn(query: Record<string, string>): Promise<{
        RspCode: string;
        Message: string;
    }>;
    queryTransaction(gatewayType: PaymentGatewayType, queryData: {
        txnRef: string;
        transactionDate: string;
        orderInfo: string;
        ipAddr: string;
        transactionNo?: string;
    }): Promise<any>;
    refundTransaction(gatewayType: PaymentGatewayType, refundData: {
        txnRef: string;
        amount: number;
        orderInfo: string;
        transactionDate: string;
        transactionType: '02' | '03';
        createBy: string;
        ipAddr: string;
        transactionNo?: string;
    }): Promise<any>;
    getTransactionStatus(transactionId: string): Promise<any>;
    testWebhook(body: any, req: Request): Promise<any>;
    healthCheck(): Promise<any>;
}

import { ConfigService } from '@nestjs/config';
import { Repository } from 'typeorm';
import { SystemConfig } from '../../../system-configs/entities/system-config.entity';
export declare class RateLimiterService {
    private readonly configService;
    private readonly systemConfigRepository;
    private readonly logger;
    private readonly clientRequests;
    private rateLimit;
    private rateLimitWindow;
    constructor(configService: ConfigService, systemConfigRepository: Repository<SystemConfig>);
    private initializeRateLimits;
    isRateLimited(clientId: string): Promise<boolean>;
    getRemainingRequests(clientId: string): number;
    getResetTime(clientId: string): number;
}

"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.TokenPriceDto = void 0;
const openapi = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
const swagger_1 = require("@nestjs/swagger");
const token_dto_1 = require("../../tokens/dto/token.dto");
class TokenPriceDto {
    id;
    productId;
    buyPrice;
    sellPrice;
    timestamp;
    createdAt;
    updatedAt;
    createdBy;
    updatedBy;
    isDeleted;
    deletedBy;
    token;
    static _OPENAPI_METADATA_FACTORY() {
        return { id: { required: true, type: () => String, format: "uuid" }, productId: { required: true, type: () => String, format: "uuid" }, buyPrice: { required: false, type: () => Number }, sellPrice: { required: false, type: () => Number }, timestamp: { required: true, type: () => Date }, createdAt: { required: true, type: () => Date }, updatedAt: { required: true, type: () => Date }, createdBy: { required: false, type: () => String, format: "uuid" }, updatedBy: { required: false, type: () => String, format: "uuid" }, isDeleted: { required: true, type: () => Boolean }, deletedBy: { required: false, type: () => String, format: "uuid" }, token: { required: true, type: () => require("../../tokens/dto/token.dto").TokenDto } };
    }
}
exports.TokenPriceDto = TokenPriceDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Unique identifier of the token price' }),
    (0, class_validator_1.IsUUID)(),
    __metadata("design:type", String)
], TokenPriceDto.prototype, "id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Token ID associated with the price' }),
    (0, class_validator_1.IsUUID)(),
    __metadata("design:type", String)
], TokenPriceDto.prototype, "productId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Buy price of the token', required: false }),
    (0, class_validator_1.IsDecimal)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Number)
], TokenPriceDto.prototype, "buyPrice", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Sell price of the token', required: false }),
    (0, class_validator_1.IsDecimal)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Number)
], TokenPriceDto.prototype, "sellPrice", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Timestamp of the price' }),
    __metadata("design:type", Date)
], TokenPriceDto.prototype, "timestamp", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Creation date of the price record' }),
    __metadata("design:type", Date)
], TokenPriceDto.prototype, "createdAt", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Last update date of the price record' }),
    __metadata("design:type", Date)
], TokenPriceDto.prototype, "updatedAt", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'ID of user who created the price record',
        required: false,
    }),
    (0, class_validator_1.IsUUID)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], TokenPriceDto.prototype, "createdBy", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'ID of user who last updated the price record',
        required: false,
    }),
    (0, class_validator_1.IsUUID)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], TokenPriceDto.prototype, "updatedBy", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Whether the price record is deleted' }),
    __metadata("design:type", Boolean)
], TokenPriceDto.prototype, "isDeleted", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'ID of user who deleted the price record',
        required: false,
    }),
    (0, class_validator_1.IsUUID)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], TokenPriceDto.prototype, "deletedBy", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Token associated with the price',
        type: () => token_dto_1.TokenDto,
    }),
    __metadata("design:type", token_dto_1.TokenDto)
], TokenPriceDto.prototype, "token", void 0);
//# sourceMappingURL=token-price.dto.js.map
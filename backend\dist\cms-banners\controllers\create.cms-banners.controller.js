"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreateCmsBannersController = void 0;
const openapi = require("@nestjs/swagger");
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const jwt_auth_guard_1 = require("../../common/guards/jwt-auth.guard");
const create_cms_banners_service_1 = require("../services/create.cms-banners.service");
const create_cms_banner_dto_1 = require("../dto/create.cms-banner.dto");
const api_response_dto_1 = require("../../common/dto/api-response.dto");
const get_user_decorator_1 = require("../../common/decorators/get-user.decorator");
const roles_decorator_1 = require("../../common/decorators/roles.decorator");
const permissions_decorator_1 = require("../../common/decorators/permissions.decorator");
let CreateCmsBannersController = class CreateCmsBannersController {
    cmsBannersService;
    constructor(cmsBannersService) {
        this.cmsBannersService = cmsBannersService;
    }
    async create(createCmsBannerDto, userId) {
        return this.cmsBannersService.create(createCmsBannerDto, userId);
    }
    async bulkCreate(createCmsBannerDtos, userId) {
        return this.cmsBannersService.bulkCreate(createCmsBannerDtos, userId);
    }
    async duplicate(id, newTitle, userId) {
        return this.cmsBannersService.duplicate(id, userId, newTitle);
    }
    async createFromTemplate(templateData, userId) {
        return this.cmsBannersService.createFromTemplate(templateData, userId);
    }
};
exports.CreateCmsBannersController = CreateCmsBannersController;
__decorate([
    (0, common_1.Post)(),
    (0, roles_decorator_1.Roles)('ADMIN', 'EDITOR'),
    (0, permissions_decorator_1.Permissions)('cms-banner:create'),
    (0, swagger_1.ApiOperation)({ summary: 'Tạo mới banner CMS' }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.CREATED,
        description: 'Banner CMS đã được tạo thành công.',
        type: api_response_dto_1.ApiResponseDto,
        schema: {
            properties: {
                data: { $ref: '#/components/schemas/CmsBannerDto' },
            },
        },
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.BAD_REQUEST,
        description: 'Dữ liệu đầu vào không hợp lệ.',
        type: api_response_dto_1.ApiResponseDto,
        schema: { properties: { data: { type: 'object', nullable: true } } },
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.CONFLICT,
        description: 'Tên banner đã tồn tại.',
        type: api_response_dto_1.ApiResponseDto,
        schema: { properties: { data: { type: 'object', nullable: true } } },
    }),
    (0, swagger_1.ApiBody)({ type: create_cms_banner_dto_1.CreateCmsBannerDto }),
    openapi.ApiResponse({ status: 201, type: require("../dto/cms-banner.dto").CmsBannerDto }),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, get_user_decorator_1.GetUser)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [create_cms_banner_dto_1.CreateCmsBannerDto, String]),
    __metadata("design:returntype", Promise)
], CreateCmsBannersController.prototype, "create", null);
__decorate([
    (0, common_1.Post)('bulk'),
    (0, roles_decorator_1.Roles)('ADMIN'),
    (0, permissions_decorator_1.Permissions)('cms-banner:create'),
    (0, swagger_1.ApiOperation)({ summary: 'Tạo nhiều banner CMS cùng lúc' }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.CREATED,
        description: 'Các banner CMS đã được tạo thành công.',
        type: api_response_dto_1.ApiResponseDto,
        schema: {
            properties: {
                data: {
                    type: 'array',
                    items: { $ref: '#/components/schemas/CmsBannerDto' },
                },
            },
        },
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.BAD_REQUEST,
        description: 'Dữ liệu đầu vào không hợp lệ.',
        type: api_response_dto_1.ApiResponseDto,
        schema: { properties: { data: { type: 'object', nullable: true } } },
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.CONFLICT,
        description: 'Có tên banner trùng lặp.',
        type: api_response_dto_1.ApiResponseDto,
        schema: { properties: { data: { type: 'object', nullable: true } } },
    }),
    (0, swagger_1.ApiBody)({ type: [create_cms_banner_dto_1.CreateCmsBannerDto] }),
    openapi.ApiResponse({ status: 201, type: [require("../dto/cms-banner.dto").CmsBannerDto] }),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, get_user_decorator_1.GetUser)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Array, String]),
    __metadata("design:returntype", Promise)
], CreateCmsBannersController.prototype, "bulkCreate", null);
__decorate([
    (0, common_1.Post)(':id/duplicate'),
    (0, roles_decorator_1.Roles)('ADMIN', 'EDITOR'),
    (0, permissions_decorator_1.Permissions)('cms-banner:create'),
    (0, swagger_1.ApiOperation)({ summary: 'Sao chép banner CMS từ banner đã có' }),
    (0, swagger_1.ApiQuery)({
        name: 'newTitle',
        description: 'Tên mới cho banner (tùy chọn)',
        required: false,
        example: 'Banner khuyến mãi tháng 1 (Bản sao)',
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.CREATED,
        description: 'Banner CMS đã được sao chép thành công.',
        type: api_response_dto_1.ApiResponseDto,
        schema: {
            properties: { data: { $ref: '#/components/schemas/CmsBannerDto' } },
        },
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.NOT_FOUND,
        description: 'Không tìm thấy banner gốc.',
        type: api_response_dto_1.ApiResponseDto,
        schema: { properties: { data: { type: 'object', nullable: true } } },
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.CONFLICT,
        description: 'Tên banner đã tồn tại.',
        type: api_response_dto_1.ApiResponseDto,
        schema: { properties: { data: { type: 'object', nullable: true } } },
    }),
    openapi.ApiResponse({ status: 201, type: require("../dto/cms-banner.dto").CmsBannerDto }),
    __param(0, (0, common_1.Body)('id')),
    __param(1, (0, common_1.Query)('newTitle')),
    __param(2, (0, get_user_decorator_1.GetUser)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String, String]),
    __metadata("design:returntype", Promise)
], CreateCmsBannersController.prototype, "duplicate", null);
__decorate([
    (0, common_1.Post)('from-template'),
    (0, roles_decorator_1.Roles)('ADMIN', 'EDITOR'),
    (0, permissions_decorator_1.Permissions)('cms-banner:create'),
    (0, swagger_1.ApiOperation)({ summary: 'Tạo banner CMS từ template có sẵn' }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.CREATED,
        description: 'Banner CMS đã được tạo từ template thành công.',
        type: api_response_dto_1.ApiResponseDto,
        schema: {
            properties: { data: { $ref: '#/components/schemas/CmsBannerDto' } },
        },
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.BAD_REQUEST,
        description: 'Dữ liệu template không hợp lệ.',
        type: api_response_dto_1.ApiResponseDto,
        schema: { properties: { data: { type: 'object', nullable: true } } },
    }),
    (0, swagger_1.ApiBody)({
        schema: {
            type: 'object',
            properties: {
                title: {
                    type: 'string',
                    description: 'Tên banner',
                    example: 'Banner từ template',
                },
                imageUrlDesktop: {
                    type: 'string',
                    description: 'URL hình ảnh desktop',
                    example: 'https://example.com/banner-desktop.jpg',
                },
                imageUrlMobile: {
                    type: 'string',
                    description: 'URL hình ảnh mobile',
                    example: 'https://example.com/banner-mobile.jpg',
                },
                linkUrl: {
                    type: 'string',
                    description: 'URL đích',
                    example: 'https://example.com/promotion',
                },
                altText: {
                    type: 'string',
                    description: 'Văn bản thay thế',
                    example: 'Banner khuyến mãi',
                },
                location: {
                    type: 'string',
                    description: 'Vị trí hiển thị',
                    example: 'homepage_slider',
                },
                displayOrder: {
                    type: 'number',
                    description: 'Thứ tự hiển thị',
                    example: 1,
                },
                startDate: {
                    type: 'string',
                    description: 'Ngày bắt đầu',
                    example: '2023-12-01T00:00:00.000Z',
                },
                endDate: {
                    type: 'string',
                    description: 'Ngày kết thúc',
                    example: '2023-12-31T23:59:59.000Z',
                },
            },
            required: ['imageUrlDesktop'],
        },
    }),
    openapi.ApiResponse({ status: 201, type: require("../dto/cms-banner.dto").CmsBannerDto }),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, get_user_decorator_1.GetUser)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, String]),
    __metadata("design:returntype", Promise)
], CreateCmsBannersController.prototype, "createFromTemplate", null);
exports.CreateCmsBannersController = CreateCmsBannersController = __decorate([
    (0, swagger_1.ApiTags)('cms-banners'),
    (0, swagger_1.ApiBearerAuth)(),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, common_1.Controller)('cms/banners'),
    __metadata("design:paramtypes", [create_cms_banners_service_1.CreateCmsBannersService])
], CreateCmsBannersController);
//# sourceMappingURL=create.cms-banners.controller.js.map
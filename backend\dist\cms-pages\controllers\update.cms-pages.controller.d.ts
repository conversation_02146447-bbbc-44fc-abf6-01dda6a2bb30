import { UpdateCmsPagesService } from '../services/update.cms-pages.service';
import { CmsPageDto } from '../dto/cms-page.dto';
import { UpdateCmsPageDto } from '../dto/update.cms-page.dto';
import { CmsPageStatus } from '../entity/cms-pages.entity';
export declare class UpdateCmsPagesController {
    private readonly cmsPagesService;
    constructor(cmsPagesService: UpdateCmsPagesService);
    update(id: string, updateCmsPageDto: UpdateCmsPageDto, userId: string): Promise<CmsPageDto | null>;
    bulkUpdate(updates: Array<{
        id: string;
        data: UpdateCmsPageDto;
    }>, userId: string): Promise<CmsPageDto[]>;
    publish(id: string, userId: string): Promise<CmsPageDto | null>;
    draft(id: string, userId: string): Promise<CmsPageDto | null>;
    updateStatus(id: string, status: CmsPageStatus, userId: string): Promise<CmsPageDto | null>;
    updateTemplate(id: string, template: string, userId: string): Promise<CmsPageDto | null>;
    bulkPublish(ids: string[], userId: string): Promise<CmsPageDto[]>;
    bulkDraft(ids: string[], userId: string): Promise<CmsPageDto[]>;
}

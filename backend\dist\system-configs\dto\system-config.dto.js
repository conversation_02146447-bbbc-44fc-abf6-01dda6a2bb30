"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SystemConfigDto = exports.ConfigType = void 0;
const openapi = require("@nestjs/swagger");
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
const class_transformer_1 = require("class-transformer");
const user_dto_1 = require("../../users/dto/user.dto");
var ConfigType;
(function (ConfigType) {
    ConfigType["TEXT"] = "text";
    ConfigType["TEXTAREA"] = "textarea";
    ConfigType["NUMBER"] = "number";
    ConfigType["BOOLEAN"] = "boolean";
    ConfigType["SELECT"] = "select";
})(ConfigType || (exports.ConfigType = ConfigType = {}));
class SystemConfigDto {
    id;
    configKey;
    configValue;
    description;
    configGroup;
    sectionName;
    sectionDisplayName;
    sectionDescription;
    sectionOrder;
    displayOrder;
    groupDisplayName;
    groupDescription;
    groupIcon;
    groupOrder;
    isGroupConfig;
    configType;
    configOptions;
    createdAt;
    updatedAt;
    createdBy;
    updatedBy;
    isDeleted;
    deletedBy;
    creator;
    updater;
    deleter;
    static _OPENAPI_METADATA_FACTORY() {
        return { id: { required: true, type: () => String, format: "uuid" }, configKey: { required: true, type: () => String }, configValue: { required: false, type: () => String }, description: { required: false, type: () => String }, configGroup: { required: false, type: () => String }, sectionName: { required: false, type: () => String }, sectionDisplayName: { required: false, type: () => String }, sectionDescription: { required: false, type: () => String }, sectionOrder: { required: false, type: () => Number }, displayOrder: { required: false, type: () => Number }, groupDisplayName: { required: false, type: () => String }, groupDescription: { required: false, type: () => String }, groupIcon: { required: false, type: () => String }, groupOrder: { required: false, type: () => Number }, isGroupConfig: { required: false, type: () => Boolean }, configType: { required: false, enum: require("./system-config.dto").ConfigType }, configOptions: { required: false, type: () => String }, createdAt: { required: true, type: () => Date }, updatedAt: { required: true, type: () => Date }, createdBy: { required: false, type: () => String, format: "uuid" }, updatedBy: { required: false, type: () => String, format: "uuid" }, isDeleted: { required: true, type: () => Boolean }, deletedBy: { required: false, type: () => String, format: "uuid" }, creator: { required: false, type: () => require("../../users/dto/user.dto").UserDto }, updater: { required: false, type: () => require("../../users/dto/user.dto").UserDto }, deleter: { required: false, type: () => require("../../users/dto/user.dto").UserDto } };
    }
}
exports.SystemConfigDto = SystemConfigDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'ID duy nhất của cấu hình' }),
    (0, class_validator_1.IsUUID)(),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", String)
], SystemConfigDto.prototype, "id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Khóa cấu hình' }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)(),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", String)
], SystemConfigDto.prototype, "configKey", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Giá trị cấu hình', required: false }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", String)
], SystemConfigDto.prototype, "configValue", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Mô tả cấu hình', required: false }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", String)
], SystemConfigDto.prototype, "description", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Nhóm cấu hình',
        required: false,
        example: 'general'
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", String)
], SystemConfigDto.prototype, "configGroup", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Tên section của cấu hình',
        required: false,
        example: 'header'
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", String)
], SystemConfigDto.prototype, "sectionName", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Tên hiển thị của section',
        required: false,
        example: 'Cấu hình Header'
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", String)
], SystemConfigDto.prototype, "sectionDisplayName", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Mô tả của section',
        required: false,
        example: 'Cấu hình phần header của website'
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", String)
], SystemConfigDto.prototype, "sectionDescription", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Thứ tự hiển thị của section',
        required: false,
        example: 1
    }),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", Number)
], SystemConfigDto.prototype, "sectionOrder", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Thứ tự hiển thị của trường cấu hình trong section',
        required: false,
        example: 1
    }),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", Number)
], SystemConfigDto.prototype, "displayOrder", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Tên hiển thị của nhóm cấu hình',
        required: false,
        example: 'Cấu hình chung'
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", String)
], SystemConfigDto.prototype, "groupDisplayName", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Mô tả của nhóm cấu hình',
        required: false,
        example: 'Cấu hình chung của hệ thống'
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", String)
], SystemConfigDto.prototype, "groupDescription", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Icon của nhóm cấu hình',
        required: false,
        example: 'settings'
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", String)
], SystemConfigDto.prototype, "groupIcon", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Thứ tự hiển thị của nhóm cấu hình',
        required: false,
        example: 1
    }),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", Number)
], SystemConfigDto.prototype, "groupOrder", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Cờ đánh dấu đây là cấu hình nhóm',
        required: false,
        example: false
    }),
    (0, class_validator_1.IsBoolean)(),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", Boolean)
], SystemConfigDto.prototype, "isGroupConfig", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Loại cấu hình',
        required: false,
        enum: ConfigType
    }),
    (0, class_validator_1.IsEnum)(ConfigType),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", String)
], SystemConfigDto.prototype, "configType", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Tùy chọn cho loại select',
        required: false,
        example: '["option1", "option2"]'
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", String)
], SystemConfigDto.prototype, "configOptions", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Thời gian tạo' }),
    (0, class_validator_1.IsDate)(),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", Date)
], SystemConfigDto.prototype, "createdAt", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Thời gian cập nhật' }),
    (0, class_validator_1.IsDate)(),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", Date)
], SystemConfigDto.prototype, "updatedAt", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'ID người tạo', required: false }),
    (0, class_validator_1.IsUUID)(),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", String)
], SystemConfigDto.prototype, "createdBy", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'ID người cập nhật', required: false }),
    (0, class_validator_1.IsUUID)(),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", String)
], SystemConfigDto.prototype, "updatedBy", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Trạng thái xóa' }),
    (0, class_validator_1.IsBoolean)(),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", Boolean)
], SystemConfigDto.prototype, "isDeleted", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'ID người xóa', required: false }),
    (0, class_validator_1.IsUUID)(),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", String)
], SystemConfigDto.prototype, "deletedBy", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Thông tin người tạo',
        type: () => user_dto_1.UserDto,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Expose)(),
    (0, class_transformer_1.Type)(() => user_dto_1.UserDto),
    __metadata("design:type", user_dto_1.UserDto)
], SystemConfigDto.prototype, "creator", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Thông tin người cập nhật',
        type: () => user_dto_1.UserDto,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Expose)(),
    (0, class_transformer_1.Type)(() => user_dto_1.UserDto),
    __metadata("design:type", user_dto_1.UserDto)
], SystemConfigDto.prototype, "updater", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Thông tin người xóa',
        type: () => user_dto_1.UserDto,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Expose)(),
    (0, class_transformer_1.Type)(() => user_dto_1.UserDto),
    __metadata("design:type", user_dto_1.UserDto)
], SystemConfigDto.prototype, "deleter", void 0);
//# sourceMappingURL=system-config.dto.js.map
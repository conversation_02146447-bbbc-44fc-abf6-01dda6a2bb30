module.exports = {

"[externals]/next/dist/compiled/next-server/app-page-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-page-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/fs [external] (fs, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("fs", () => require("fs"));

module.exports = mod;
}}),
"[externals]/url [external] (url, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("url", () => require("url"));

module.exports = mod;
}}),
"[externals]/child_process [external] (child_process, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("child_process", () => require("child_process"));

module.exports = mod;
}}),
"[externals]/http [external] (http, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("http", () => require("http"));

module.exports = mod;
}}),
"[externals]/https [external] (https, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("https", () => require("https"));

module.exports = mod;
}}),
"[externals]/tty [external] (tty, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("tty", () => require("tty"));

module.exports = mod;
}}),
"[externals]/util [external] (util, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("util", () => require("util"));

module.exports = mod;
}}),
"[externals]/os [external] (os, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("os", () => require("os"));

module.exports = mod;
}}),
"[externals]/stream [external] (stream, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("stream", () => require("stream"));

module.exports = mod;
}}),
"[externals]/zlib [external] (zlib, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("zlib", () => require("zlib"));

module.exports = mod;
}}),
"[externals]/buffer [external] (buffer, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("buffer", () => require("buffer"));

module.exports = mod;
}}),
"[externals]/crypto [external] (crypto, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("crypto", () => require("crypto"));

module.exports = mod;
}}),
"[externals]/events [external] (events, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("events", () => require("events"));

module.exports = mod;
}}),
"[externals]/net [external] (net, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("net", () => require("net"));

module.exports = mod;
}}),
"[externals]/tls [external] (tls, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("tls", () => require("tls"));

module.exports = mod;
}}),
"[project]/services/websocket/websocket-config.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
// Cấu hình cho WebSocket
__turbopack_context__.s({
    "useWebSocketConfig": (()=>useWebSocketConfig)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zustand$40$5$2e$0$2e$3_$40$types$2b$react$40$_922ca76f2c6c3e669063fe7f86bd14e7$2f$node_modules$2f$zustand$2f$esm$2f$react$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/zustand@5.0.3_@types+react@_922ca76f2c6c3e669063fe7f86bd14e7/node_modules/zustand/esm/react.mjs [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zustand$40$5$2e$0$2e$3_$40$types$2b$react$40$_922ca76f2c6c3e669063fe7f86bd14e7$2f$node_modules$2f$zustand$2f$esm$2f$middleware$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/zustand@5.0.3_@types+react@_922ca76f2c6c3e669063fe7f86bd14e7/node_modules/zustand/esm/middleware.mjs [app-ssr] (ecmascript)");
;
;
const useWebSocketConfig = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zustand$40$5$2e$0$2e$3_$40$types$2b$react$40$_922ca76f2c6c3e669063fe7f86bd14e7$2f$node_modules$2f$zustand$2f$esm$2f$react$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["create"])()((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zustand$40$5$2e$0$2e$3_$40$types$2b$react$40$_922ca76f2c6c3e669063fe7f86bd14e7$2f$node_modules$2f$zustand$2f$esm$2f$middleware$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["persist"])((set)=>({
        // Mặc định là bật
        enabled: true,
        // Hàm để bật WebSocket
        enableWebSocket: ()=>set({
                enabled: true
            }),
        // Hàm để tắt WebSocket
        disableWebSocket: ()=>set({
                enabled: false
            }),
        // Hàm để toggle trạng thái WebSocket
        toggleWebSocket: ()=>set((state)=>({
                    enabled: !state.enabled
                }))
    }), {
    name: 'websocket-config'
}));
}}),
"[project]/services/websocket/websocket.service.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "WebSocketNamespace": (()=>WebSocketNamespace),
    "useAutoConnectWebSocket": (()=>useAutoConnectWebSocket),
    "useWebSocket": (()=>useWebSocket)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$socket$2e$io$2d$client$40$4$2e$8$2e$1$2f$node_modules$2f$socket$2e$io$2d$client$2f$build$2f$esm$2d$debug$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/socket.io-client@4.8.1/node_modules/socket.io-client/build/esm-debug/index.js [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$socket$2e$io$2d$client$40$4$2e$8$2e$1$2f$node_modules$2f$socket$2e$io$2d$client$2f$build$2f$esm$2d$debug$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/socket.io-client@4.8.1/node_modules/socket.io-client/build/esm-debug/index.js [app-ssr] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zustand$40$5$2e$0$2e$3_$40$types$2b$react$40$_922ca76f2c6c3e669063fe7f86bd14e7$2f$node_modules$2f$zustand$2f$esm$2f$react$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/zustand@5.0.3_@types+react@_922ca76f2c6c3e669063fe7f86bd14e7/node_modules/zustand/esm/react.mjs [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$opentelemetry$2b$_ee4fdf7136c0ae57e36e5bb24f18c6a1$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/next@15.3.0_@opentelemetry+_ee4fdf7136c0ae57e36e5bb24f18c6a1/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$services$2f$websocket$2f$websocket$2d$config$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/services/websocket/websocket-config.ts [app-ssr] (ecmascript)");
;
;
;
;
var WebSocketNamespace = /*#__PURE__*/ function(WebSocketNamespace) {
    WebSocketNamespace["FOREX"] = "forex";
    WebSocketNamespace["NOTIFICATIONS"] = "notifications";
    return WebSocketNamespace;
}({});
const useWebSocket = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zustand$40$5$2e$0$2e$3_$40$types$2b$react$40$_922ca76f2c6c3e669063fe7f86bd14e7$2f$node_modules$2f$zustand$2f$esm$2f$react$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["create"])((set, get)=>({
        sockets: {
            ["forex"]: null,
            ["notifications"]: null
        },
        isConnected: {
            ["forex"]: false,
            ["notifications"]: false
        },
        lastError: {
            ["forex"]: null,
            ["notifications"]: null
        },
        // Kết nối đến WebSocket server
        connect: (namespace)=>{
            const { sockets } = get();
            // Kiểm tra xem WebSocket có được bật không
            const { enabled } = __TURBOPACK__imported__module__$5b$project$5d2f$services$2f$websocket$2f$websocket$2d$config$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useWebSocketConfig"].getState();
            if (!enabled) {
                return;
            }
            // Nếu đã kết nối, không làm gì cả
            if (sockets[namespace] && sockets[namespace]?.connected) return;
            // Ngắt kết nối cũ nếu có
            if (sockets[namespace]) {
                sockets[namespace]?.disconnect();
            }
            // Lấy token từ localStorage
            const token = localStorage.getItem('accessToken');
            if (!token) {
                set((state)=>({
                        lastError: {
                            ...state.lastError,
                            [namespace]: 'Không tìm thấy token xác thực'
                        }
                    }));
                return;
            }
            // Tạo URL WebSocket từ NEXT_PUBLIC_API_URL
            let wsUrl;
            // Lấy base URL từ NEXT_PUBLIC_API_URL và loại bỏ /api/v1
            const apiUrl = ("TURBOPACK compile-time value", "http://localhost:3168/api/v1") || 'http://localhost:3168/api/v1';
            wsUrl = apiUrl.replace('/api/v1', ''); // Loại bỏ /api/v1 để có base URL
            // Trong Socket.IO, namespace được thêm vào URL
            // Tạo kết nối mới - Chỉ sử dụng websocket, không sử dụng polling
            const newSocket = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$socket$2e$io$2d$client$40$4$2e$8$2e$1$2f$node_modules$2f$socket$2e$io$2d$client$2f$build$2f$esm$2d$debug$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["io"])(`${wsUrl}/${namespace}`, {
                auth: {
                    token: `Bearer ${token}` // Thêm 'Bearer ' prefix cho token
                },
                transports: [
                    'websocket',
                    'polling'
                ],
                reconnection: true,
                reconnectionAttempts: 5,
                reconnectionDelay: 1000,
                timeout: 20000
            });
            // Xử lý sự kiện kết nối
            newSocket.on('connect', ()=>{
                set((state)=>({
                        sockets: {
                            ...state.sockets,
                            [namespace]: newSocket
                        },
                        isConnected: {
                            ...state.isConnected,
                            [namespace]: true
                        },
                        lastError: {
                            ...state.lastError,
                            [namespace]: null
                        }
                    }));
            });
            // Xử lý sự kiện ngắt kết nối
            newSocket.on('disconnect', ()=>{
                set((state)=>({
                        isConnected: {
                            ...state.isConnected,
                            [namespace]: false
                        }
                    }));
            });
            // Xử lý lỗi kết nối
            newSocket.on('connect_error', (error)=>{
                set((state)=>({
                        lastError: {
                            ...state.lastError,
                            [namespace]: `Lỗi kết nối: ${error.message}`
                        },
                        isConnected: {
                            ...state.isConnected,
                            [namespace]: false
                        }
                    }));
            });
            // Xử lý lỗi Socket.IO
            newSocket.on('error', (error)=>{
                // Nếu lỗi là do xác thực, thử kết nối đến namespace public
                if (error.code === 401 && namespace === "forex") {
                    // Kết nối đến namespace public-forex không yêu cầu xác thực
                    const publicSocket = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$socket$2e$io$2d$client$40$4$2e$8$2e$1$2f$node_modules$2f$socket$2e$io$2d$client$2f$build$2f$esm$2d$debug$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["io"])(`${wsUrl}/public-forex`, {
                        transports: [
                            'websocket',
                            'polling'
                        ],
                        reconnection: true,
                        reconnectionAttempts: 5,
                        reconnectionDelay: 1000,
                        timeout: 20000
                    });
                    publicSocket.on('connect', ()=>{
                        // Cập nhật state với public socket
                        set((state)=>({
                                sockets: {
                                    ...state.sockets,
                                    [namespace]: publicSocket
                                },
                                isConnected: {
                                    ...state.isConnected,
                                    [namespace]: true
                                }
                            }));
                    });
                }
            });
            // Lưu socket vào state
            set((state)=>({
                    sockets: {
                        ...state.sockets,
                        [namespace]: newSocket
                    }
                }));
        },
        // Ngắt kết nối
        disconnect: (namespace)=>{
            const { sockets } = get();
            if (sockets[namespace]) {
                sockets[namespace]?.disconnect();
                set((state)=>({
                        sockets: {
                            ...state.sockets,
                            [namespace]: null
                        },
                        isConnected: {
                            ...state.isConnected,
                            [namespace]: false
                        }
                    }));
            }
        },
        // Gửi tin nhắn (không chờ response)
        emit: (namespace, event, data)=>{
            const { sockets, isConnected } = get();
            if (!sockets[namespace] || !isConnected[namespace]) {
                return;
            }
            sockets[namespace]?.emit(event, data);
        },
        // Gửi tin nhắn và chờ phản hồi (chỉ dùng khi cần thiết)
        emitWithResponse: async (namespace, event, data)=>{
            const { sockets, isConnected } = get();
            if (!sockets[namespace] || !isConnected[namespace]) {
                throw new Error(`Chưa kết nối đến WebSocket ${namespace}`);
            }
            return new Promise((resolve, reject)=>{
                const timeout = setTimeout(()=>{
                    reject(new Error('Timeout waiting for response'));
                }, 10000); // Tăng timeout lên 10 giây
                sockets[namespace]?.emit(event, data, (response)=>{
                    clearTimeout(timeout);
                    resolve(response);
                });
            });
        },
        // Đăng ký lắng nghe sự kiện
        subscribe: (namespace, event, callback)=>{
            const { sockets } = get();
            if (!sockets[namespace]) {
                return ()=>{};
            }
            sockets[namespace]?.on(event, callback);
            // Trả về hàm để hủy đăng ký
            return ()=>{
                sockets[namespace]?.off(event, callback);
            };
        },
        // Cập nhật trạng thái kết nối
        setConnected: (namespace, isConnected)=>{
            set((state)=>({
                    isConnected: {
                        ...state.isConnected,
                        [namespace]: isConnected
                    }
                }));
        },
        // Cập nhật lỗi
        setError: (namespace, error)=>{
            set((state)=>({
                    lastError: {
                        ...state.lastError,
                        [namespace]: error
                    }
                }));
        }
    }));
const useAutoConnectWebSocket = ()=>{
    const { connect, isConnected } = useWebSocket();
    const { enabled } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$services$2f$websocket$2f$websocket$2d$config$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useWebSocketConfig"])();
    // Kết nối khi component mount
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$opentelemetry$2b$_ee4fdf7136c0ae57e36e5bb24f18c6a1$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        const token = localStorage.getItem('accessToken');
        if (token) {
            // Kích hoạt WebSocket nếu chưa được kích hoạt
            if (!enabled) {
                // Sử dụng getState() để lấy state và actions từ store
                const webSocketConfig = __TURBOPACK__imported__module__$5b$project$5d2f$services$2f$websocket$2f$websocket$2d$config$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useWebSocketConfig"].getState();
                webSocketConfig.enableWebSocket();
            }
            // Kết nối đến tất cả các namespace
            Object.values(WebSocketNamespace).forEach((namespace)=>{
                if (!isConnected[namespace]) {
                    connect(namespace);
                }
            });
        }
        // Cleanup khi unmount
        return ()=>{
        // Không ngắt kết nối khi unmount component để giữ kết nối liên tục
        };
    }, [
        connect,
        isConnected,
        enabled
    ]);
    return useWebSocket();
};
}}),
"[project]/services/websocket/polygon-forex-socket.service.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "ForexEvent": (()=>ForexEvent),
    "usePolygonForex": (()=>usePolygonForex),
    "usePolygonForexSocket": (()=>usePolygonForexSocket)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zustand$40$5$2e$0$2e$3_$40$types$2b$react$40$_922ca76f2c6c3e669063fe7f86bd14e7$2f$node_modules$2f$zustand$2f$esm$2f$react$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/zustand@5.0.3_@types+react@_922ca76f2c6c3e669063fe7f86bd14e7/node_modules/zustand/esm/react.mjs [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$opentelemetry$2b$_ee4fdf7136c0ae57e36e5bb24f18c6a1$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/next@15.3.0_@opentelemetry+_ee4fdf7136c0ae57e36e5bb24f18c6a1/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$services$2f$websocket$2f$websocket$2e$service$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/services/websocket/websocket.service.ts [app-ssr] (ecmascript)");
;
;
;
var ForexEvent = /*#__PURE__*/ function(ForexEvent) {
    ForexEvent["FOREX_QUOTE"] = "forex-quote";
    ForexEvent["CONNECTION_STATUS"] = "connection-status";
    ForexEvent["SUBSCRIPTION_STATUS"] = "subscription-status";
    ForexEvent["ERROR"] = "error";
    ForexEvent["SUBSCRIBE"] = "subscribe-forex";
    return ForexEvent;
}({});
const usePolygonForexSocket = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zustand$40$5$2e$0$2e$3_$40$types$2b$react$40$_922ca76f2c6c3e669063fe7f86bd14e7$2f$node_modules$2f$zustand$2f$esm$2f$react$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["create"])((set)=>({
        currentQuote: null,
        connectionStatus: null,
        isSubscribed: false,
        lastError: null,
        // Đăng ký nhận dữ liệu
        subscribe: ()=>{
            const { emit, isConnected } = __TURBOPACK__imported__module__$5b$project$5d2f$services$2f$websocket$2f$websocket$2e$service$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useWebSocket"].getState();
            if (!isConnected[__TURBOPACK__imported__module__$5b$project$5d2f$services$2f$websocket$2f$websocket$2e$service$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["WebSocketNamespace"].FOREX]) {
                set({
                    lastError: 'Chưa kết nối đến WebSocket Forex'
                });
                return;
            }
            try {
                emit(__TURBOPACK__imported__module__$5b$project$5d2f$services$2f$websocket$2f$websocket$2e$service$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["WebSocketNamespace"].FOREX, "subscribe-forex");
                set({
                    isSubscribed: true,
                    lastError: null
                });
            } catch (error) {
                const errorMessage = error instanceof Error ? error.message : 'Lỗi không xác định';
                set({
                    lastError: errorMessage
                });
            }
        },
        // Cập nhật quote hiện tại
        updateQuote: (quote)=>{
            set({
                currentQuote: quote
            });
        },
        // Cập nhật trạng thái kết nối
        updateConnectionStatus: (status)=>{
            set({
                connectionStatus: status
            });
        },
        // Cập nhật trạng thái đăng ký
        setSubscribed: (isSubscribed)=>{
            set({
                isSubscribed
            });
        },
        // Cập nhật lỗi
        setError: (error)=>{
            set({
                lastError: error
            });
        }
    }));
const usePolygonForex = ()=>{
    const { currentQuote, connectionStatus, isSubscribed, lastError, subscribe, updateQuote, updateConnectionStatus, setError } = usePolygonForexSocket();
    const { connect, subscribe: subscribeToEvent, isConnected } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$services$2f$websocket$2f$websocket$2e$service$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useWebSocket"])();
    // Kết nối đến namespace Forex khi component mount
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$opentelemetry$2b$_ee4fdf7136c0ae57e36e5bb24f18c6a1$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        const token = localStorage.getItem('accessToken');
        if (token && !isConnected[__TURBOPACK__imported__module__$5b$project$5d2f$services$2f$websocket$2f$websocket$2e$service$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["WebSocketNamespace"].FOREX]) {
            connect(__TURBOPACK__imported__module__$5b$project$5d2f$services$2f$websocket$2f$websocket$2e$service$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["WebSocketNamespace"].FOREX);
        }
    }, [
        connect,
        isConnected
    ]);
    // Lắng nghe cập nhật quote
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$opentelemetry$2b$_ee4fdf7136c0ae57e36e5bb24f18c6a1$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        if (!isConnected[__TURBOPACK__imported__module__$5b$project$5d2f$services$2f$websocket$2f$websocket$2e$service$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["WebSocketNamespace"].FOREX]) {
            return;
        }
        // Đăng ký lắng nghe sự kiện cập nhật quote
        const unsubscribeFromQuote = subscribeToEvent(__TURBOPACK__imported__module__$5b$project$5d2f$services$2f$websocket$2f$websocket$2e$service$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["WebSocketNamespace"].FOREX, "forex-quote", (quote)=>{
            updateQuote(quote);
        });
        // Đăng ký lắng nghe sự kiện trạng thái kết nối
        const unsubscribeFromStatus = subscribeToEvent(__TURBOPACK__imported__module__$5b$project$5d2f$services$2f$websocket$2f$websocket$2e$service$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["WebSocketNamespace"].FOREX, "connection-status", (status)=>{
            updateConnectionStatus(status);
        });
        // Đăng ký lắng nghe sự kiện trạng thái đăng ký
        const unsubscribeFromSubscription = subscribeToEvent(__TURBOPACK__imported__module__$5b$project$5d2f$services$2f$websocket$2f$websocket$2e$service$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["WebSocketNamespace"].FOREX, "subscription-status", (status)=>{
            if (status.status === 'success') {
                setError(null);
            } else {
                setError(status.message);
            }
        });
        // Hủy đăng ký khi unmount
        return ()=>{
            unsubscribeFromQuote();
            unsubscribeFromStatus();
            unsubscribeFromSubscription();
        };
    }, [
        subscribeToEvent,
        isConnected,
        updateQuote,
        updateConnectionStatus,
        setError
    ]);
    // Đăng ký nhận dữ liệu khi đã kết nối nhưng chưa đăng ký
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$opentelemetry$2b$_ee4fdf7136c0ae57e36e5bb24f18c6a1$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        if (isConnected[__TURBOPACK__imported__module__$5b$project$5d2f$services$2f$websocket$2f$websocket$2e$service$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["WebSocketNamespace"].FOREX] && !isSubscribed) {
            subscribe();
        }
    }, [
        isConnected,
        isSubscribed,
        subscribe
    ]);
    return {
        currentQuote,
        connectionStatus,
        isSubscribed,
        lastError,
        isConnected: isConnected[__TURBOPACK__imported__module__$5b$project$5d2f$services$2f$websocket$2f$websocket$2e$service$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["WebSocketNamespace"].FOREX],
        isMockData: connectionStatus?.usingMockData || false,
        subscribe
    };
};
}}),
"[project]/services/websocket/notification-socket.service.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "NotificationEvent": (()=>NotificationEvent),
    "NotificationType": (()=>NotificationType),
    "useNotificationSocket": (()=>useNotificationSocket),
    "useNotifications": (()=>useNotifications)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zustand$40$5$2e$0$2e$3_$40$types$2b$react$40$_922ca76f2c6c3e669063fe7f86bd14e7$2f$node_modules$2f$zustand$2f$esm$2f$react$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/zustand@5.0.3_@types+react@_922ca76f2c6c3e669063fe7f86bd14e7/node_modules/zustand/esm/react.mjs [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$opentelemetry$2b$_ee4fdf7136c0ae57e36e5bb24f18c6a1$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/next@15.3.0_@opentelemetry+_ee4fdf7136c0ae57e36e5bb24f18c6a1/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$services$2f$websocket$2f$websocket$2e$service$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/services/websocket/websocket.service.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$services$2f$websocket$2f$websocket$2d$config$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/services/websocket/websocket-config.ts [app-ssr] (ecmascript)");
;
;
;
;
var NotificationType = /*#__PURE__*/ function(NotificationType) {
    NotificationType["SYSTEM"] = "system";
    NotificationType["TRANSACTION"] = "transaction";
    NotificationType["USER"] = "user";
    NotificationType["CONTRACT"] = "contract";
    NotificationType["PAYMENT"] = "payment";
    return NotificationType;
}({});
var NotificationEvent = /*#__PURE__*/ function(NotificationEvent) {
    NotificationEvent["NEW_NOTIFICATION"] = "new_notification";
    NotificationEvent["READ_NOTIFICATION"] = "read_notification";
    NotificationEvent["DELETE_NOTIFICATION"] = "delete_notification";
    NotificationEvent["GET_NOTIFICATIONS"] = "get_notifications";
    return NotificationEvent;
}({});
const useNotificationSocket = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zustand$40$5$2e$0$2e$3_$40$types$2b$react$40$_922ca76f2c6c3e669063fe7f86bd14e7$2f$node_modules$2f$zustand$2f$esm$2f$react$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["create"])((set, get)=>({
        notifications: [],
        unreadCount: 0,
        isLoading: false,
        lastError: null,
        // Lấy danh sách thông báo
        fetchNotifications: async ()=>{
            const { emit } = __TURBOPACK__imported__module__$5b$project$5d2f$services$2f$websocket$2f$websocket$2e$service$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useWebSocket"].getState();
            set({
                isLoading: true
            });
            try {
                const response = await emit(__TURBOPACK__imported__module__$5b$project$5d2f$services$2f$websocket$2f$websocket$2e$service$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["WebSocketNamespace"].NOTIFICATIONS, "get_notifications");
                set({
                    notifications: response.notifications,
                    isLoading: false,
                    lastError: null
                });
                // Cập nhật số lượng thông báo chưa đọc
                get().updateUnreadCount();
            } catch (error) {
                set({
                    isLoading: false,
                    lastError: error instanceof Error ? error.message : 'Lỗi không xác định'
                });
            }
        },
        // Đánh dấu thông báo đã đọc
        markAsRead: async (id)=>{
            const { emit } = __TURBOPACK__imported__module__$5b$project$5d2f$services$2f$websocket$2f$websocket$2e$service$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useWebSocket"].getState();
            try {
                await emit(__TURBOPACK__imported__module__$5b$project$5d2f$services$2f$websocket$2f$websocket$2e$service$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["WebSocketNamespace"].NOTIFICATIONS, "read_notification", {
                    id
                });
                set((state)=>({
                        notifications: state.notifications.map((notification)=>notification.id === id ? {
                                ...notification,
                                isRead: true
                            } : notification),
                        lastError: null
                    }));
                // Cập nhật số lượng thông báo chưa đọc
                get().updateUnreadCount();
            } catch (error) {
                set({
                    lastError: error instanceof Error ? error.message : 'Lỗi không xác định'
                });
            }
        },
        // Đánh dấu tất cả thông báo đã đọc
        markAllAsRead: async ()=>{
            const { emit } = __TURBOPACK__imported__module__$5b$project$5d2f$services$2f$websocket$2f$websocket$2e$service$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useWebSocket"].getState();
            try {
                await emit(__TURBOPACK__imported__module__$5b$project$5d2f$services$2f$websocket$2f$websocket$2e$service$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["WebSocketNamespace"].NOTIFICATIONS, "read_notification", {
                    all: true
                });
                set((state)=>({
                        notifications: state.notifications.map((notification)=>({
                                ...notification,
                                isRead: true
                            })),
                        unreadCount: 0,
                        lastError: null
                    }));
            } catch (error) {
                set({
                    lastError: error instanceof Error ? error.message : 'Lỗi không xác định'
                });
            }
        },
        // Xóa thông báo
        deleteNotification: async (id)=>{
            const { emit } = __TURBOPACK__imported__module__$5b$project$5d2f$services$2f$websocket$2f$websocket$2e$service$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useWebSocket"].getState();
            try {
                await emit(__TURBOPACK__imported__module__$5b$project$5d2f$services$2f$websocket$2f$websocket$2e$service$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["WebSocketNamespace"].NOTIFICATIONS, "delete_notification", {
                    id
                });
                set((state)=>({
                        notifications: state.notifications.filter((notification)=>notification.id !== id),
                        lastError: null
                    }));
                // Cập nhật số lượng thông báo chưa đọc
                get().updateUnreadCount();
            } catch (error) {
                set({
                    lastError: error instanceof Error ? error.message : 'Lỗi không xác định'
                });
            }
        },
        // Thêm thông báo mới
        addNotification: (notification)=>{
            set((state)=>({
                    notifications: [
                        notification,
                        ...state.notifications
                    ],
                    lastError: null
                }));
            // Cập nhật số lượng thông báo chưa đọc
            get().updateUnreadCount();
        },
        // Cập nhật số lượng thông báo chưa đọc
        updateUnreadCount: ()=>{
            const { notifications } = get();
            const unreadCount = notifications.filter((notification)=>!notification.isRead).length;
            set({
                unreadCount
            });
        }
    }));
const useNotifications = ()=>{
    const { notifications, unreadCount, isLoading, lastError, fetchNotifications, markAsRead, markAllAsRead, deleteNotification, addNotification } = useNotificationSocket();
    const { connect, subscribe, isConnected } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$services$2f$websocket$2f$websocket$2e$service$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useWebSocket"])();
    const { enabled } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$services$2f$websocket$2f$websocket$2d$config$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useWebSocketConfig"])();
    // Kết nối đến namespace thông báo khi component mount
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$opentelemetry$2b$_ee4fdf7136c0ae57e36e5bb24f18c6a1$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        const token = localStorage.getItem('accessToken');
        if (token && enabled && !isConnected[__TURBOPACK__imported__module__$5b$project$5d2f$services$2f$websocket$2f$websocket$2e$service$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["WebSocketNamespace"].NOTIFICATIONS]) {
            connect(__TURBOPACK__imported__module__$5b$project$5d2f$services$2f$websocket$2f$websocket$2e$service$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["WebSocketNamespace"].NOTIFICATIONS);
        }
    }, [
        connect,
        isConnected,
        enabled
    ]);
    // Lắng nghe thông báo mới
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$opentelemetry$2b$_ee4fdf7136c0ae57e36e5bb24f18c6a1$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        if (!enabled || !isConnected[__TURBOPACK__imported__module__$5b$project$5d2f$services$2f$websocket$2f$websocket$2e$service$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["WebSocketNamespace"].NOTIFICATIONS]) return;
        // Đăng ký lắng nghe sự kiện thông báo mới
        const unsubscribe = subscribe(__TURBOPACK__imported__module__$5b$project$5d2f$services$2f$websocket$2f$websocket$2e$service$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["WebSocketNamespace"].NOTIFICATIONS, "new_notification", (notification)=>{
            addNotification(notification);
        });
        // Lấy danh sách thông báo
        fetchNotifications();
        // Hủy đăng ký khi unmount
        return unsubscribe;
    }, [
        subscribe,
        isConnected,
        fetchNotifications,
        addNotification,
        enabled
    ]);
    return {
        notifications,
        unreadCount,
        isLoading,
        lastError,
        markAsRead,
        markAllAsRead,
        deleteNotification
    };
};
}}),
"[project]/components/providers/WebSocketProvider.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "WebSocketProvider": (()=>WebSocketProvider)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$opentelemetry$2b$_ee4fdf7136c0ae57e36e5bb24f18c6a1$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/next@15.3.0_@opentelemetry+_ee4fdf7136c0ae57e36e5bb24f18c6a1/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$opentelemetry$2b$_ee4fdf7136c0ae57e36e5bb24f18c6a1$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/next@15.3.0_@opentelemetry+_ee4fdf7136c0ae57e36e5bb24f18c6a1/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$services$2f$websocket$2f$websocket$2e$service$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/services/websocket/websocket.service.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$services$2f$websocket$2f$polygon$2d$forex$2d$socket$2e$service$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/services/websocket/polygon-forex-socket.service.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$services$2f$websocket$2f$notification$2d$socket$2e$service$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/services/websocket/notification-socket.service.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$services$2f$websocket$2f$websocket$2d$config$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/services/websocket/websocket-config.ts [app-ssr] (ecmascript)");
'use client';
;
;
;
;
;
;
function WebSocketProvider({ children }) {
    // Sử dụng hook để tự động kết nối WebSocket
    const { isConnected, connect } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$services$2f$websocket$2f$websocket$2e$service$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useAutoConnectWebSocket"])();
    // Khởi tạo các service
    const forexPrice = (0, __TURBOPACK__imported__module__$5b$project$5d2f$services$2f$websocket$2f$polygon$2d$forex$2d$socket$2e$service$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["usePolygonForex"])();
    const notifications = (0, __TURBOPACK__imported__module__$5b$project$5d2f$services$2f$websocket$2f$notification$2d$socket$2e$service$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useNotifications"])();
    // Lấy cấu hình WebSocket ở cấp cao nhất của component
    const { enabled, enableWebSocket } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$services$2f$websocket$2f$websocket$2d$config$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useWebSocketConfig"])();
    // Kích hoạt WebSocket
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$opentelemetry$2b$_ee4fdf7136c0ae57e36e5bb24f18c6a1$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        // Đảm bảo WebSocket được bật
        if (!enabled) {
            enableWebSocket();
        }
    }, [
        enabled,
        enableWebSocket
    ]);
    // Log trạng thái kết nối khi thay đổi (chỉ để debug)
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$opentelemetry$2b$_ee4fdf7136c0ae57e36e5bb24f18c6a1$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        // Kiểm tra token
        const token = localStorage.getItem('accessToken');
        // Sử dụng biến enabled từ cấp cao nhất của component
        // Thử kết nối lại nếu chưa kết nối
        if (!isConnected[__TURBOPACK__imported__module__$5b$project$5d2f$services$2f$websocket$2f$websocket$2e$service$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["WebSocketNamespace"].FOREX] && token && enabled) {
            connect(__TURBOPACK__imported__module__$5b$project$5d2f$services$2f$websocket$2f$websocket$2e$service$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["WebSocketNamespace"].FOREX);
        }
        if (forexPrice.isSubscribed) {}
        if (notifications.unreadCount > 0) {}
        // Log lỗi nếu có
        if (forexPrice.lastError) {
            console.error(`Forex error: ${forexPrice.lastError}`);
        }
    }, [
        isConnected,
        forexPrice.isSubscribed,
        forexPrice.lastError,
        notifications.unreadCount,
        enabled,
        connect
    ]);
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$opentelemetry$2b$_ee4fdf7136c0ae57e36e5bb24f18c6a1$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$opentelemetry$2b$_ee4fdf7136c0ae57e36e5bb24f18c6a1$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Fragment"], {
        children: children
    }, void 0, false);
}
}}),
"[project]/components/ui/sonner.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "Toaster": (()=>Toaster)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$opentelemetry$2b$_ee4fdf7136c0ae57e36e5bb24f18c6a1$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/next@15.3.0_@opentelemetry+_ee4fdf7136c0ae57e36e5bb24f18c6a1/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$2d$themes$40$0$2e$4$2e$6_react$2d$dom_e207e685aa9cc81adf4eaedb8666d505$2f$node_modules$2f$next$2d$themes$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/next-themes@0.4.6_react-dom_e207e685aa9cc81adf4eaedb8666d505/node_modules/next-themes/dist/index.mjs [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$sonner$40$2$2e$0$2e$3_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/sonner@2.0.3_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/sonner/dist/index.mjs [app-ssr] (ecmascript)");
"use client";
;
;
;
const Toaster = ({ ...props })=>{
    const { theme = "system" } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$2d$themes$40$0$2e$4$2e$6_react$2d$dom_e207e685aa9cc81adf4eaedb8666d505$2f$node_modules$2f$next$2d$themes$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useTheme"])();
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$opentelemetry$2b$_ee4fdf7136c0ae57e36e5bb24f18c6a1$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$sonner$40$2$2e$0$2e$3_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Toaster"], {
        theme: theme,
        className: "toaster group",
        style: {
            "--normal-bg": "var(--popover)",
            "--normal-text": "var(--popover-foreground)",
            "--normal-border": "var(--border)"
        },
        ...props
    }, void 0, false, {
        fileName: "[project]/components/ui/sonner.tsx",
        lineNumber: 10,
        columnNumber: 5
    }, this);
};
;
}}),
"[project]/lib/api.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * API client cho ứng dụng
 */ __turbopack_context__.s({
    "api": (()=>api),
    "fetchApi": (()=>fetchApi)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$auth$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/auth.ts [app-ssr] (ecmascript)");
;
const NEXT_PUBLIC_API_URL = ("TURBOPACK compile-time value", "http://localhost:3168/api/v1") || "http://localhost:3000/api/v1";
// Biến để theo dõi các yêu cầu đang chờ refresh token
let isRefreshing = false;
let failedQueue = [];
/**
 * Xử lý hàng đợi các yêu cầu thất bại
 */ function processQueue(error, token = null) {
    failedQueue.forEach((prom)=>{
        if (error) {
            prom.reject(error);
        } else {
            prom.resolve(token);
        }
    });
    failedQueue = [];
}
/**
 * Hàm thử refresh token và thực hiện lại yêu cầu
 */ async function handleTokenRefresh() {
    try {
        if (!isRefreshing) {
            isRefreshing = true;
            const success = await __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$auth$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["authService"].tryRefreshToken();
            isRefreshing = false;
            if (success) {
                // Lấy token mới
                const newToken = __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$auth$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["authService"].getAccessToken();
                // Xử lý hàng đợi với token mới
                processQueue(null, newToken);
                // Lấy thông tin người dùng hiện tại
                const currentUser = __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$auth$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["authService"].getCurrentUser();
                if (currentUser) {
                    // Luôn điều hướng người dùng về trang phù hợp với vai trò của họ
                    __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$auth$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["authService"].redirectBasedOnRole(currentUser);
                }
                return newToken;
            } else {
                // Nếu refresh token thất bại, đăng xuất người dùng
                __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$auth$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["authService"].logout();
                processQueue(new Error('Refresh token failed'));
                return null;
            }
        } else {
            // Nếu đang refresh token, thêm yêu cầu vào hàng đợi
            return new Promise((resolve, reject)=>{
                failedQueue.push({
                    resolve,
                    reject
                });
            });
        }
    } catch (error) {
        console.error("API: Error during token refresh:", error);
        isRefreshing = false;
        processQueue(error);
        throw error;
    }
}
async function fetchApi(endpoint, options = {}) {
    const url = `${NEXT_PUBLIC_API_URL}${endpoint.startsWith('/') ? endpoint : `/${endpoint}`}`;
    // Hàm để thực hiện yêu cầu API
    const executeRequest = async (token)=>{
        // Tạo headers mới
        const headers = new Headers(options.headers);
        headers.set('Content-Type', 'application/json');
        // Thêm token xác thực nếu có
        const accessToken = token || (("TURBOPACK compile-time falsy", 0) ? ("TURBOPACK unreachable", undefined) : null);
        if (accessToken) {
            headers.set('Authorization', `Bearer ${accessToken}`);
        }
        const config = {
            ...options,
            headers
        };
        const response = await fetch(url, config);
        // Kiểm tra nếu response không thành công
        if (!response.ok) {
            const errorData = await response.json().catch(()=>({}));
            // Xử lý cấu trúc lỗi từ backend
            const errorMessage = errorData.message || errorData.data && errorData.data.message || `API request failed with status ${response.status}`;
            console.error('API Error:', errorData);
            // Tạo error object với thông tin chi tiết hơn
            const error = new Error(errorMessage);
            error.status = response.status;
            error.data = errorData;
            throw error;
        }
        // Parse JSON response
        const responseData = await response.json();
        // Kiểm tra cấu trúc response từ backend (ApiResponseDto)
        if (responseData.data !== undefined) {
            // Trường hợp response có cấu trúc ApiResponseDto
            return responseData.data;
        } else {
            // Trường hợp response không có cấu trúc ApiResponseDto
            console.warn('API response does not follow standard structure:', responseData);
            return responseData;
        }
    };
    try {
        // Thử thực hiện yêu cầu
        return await executeRequest();
    } catch (error) {
        // Nếu lỗi 401 Unauthorized và không phải là yêu cầu đăng nhập/đăng ký/refresh token
        if (error.status === 401 && !endpoint.includes('auth/login') && !endpoint.includes('auth/register') && !endpoint.includes('auth/refresh-token')) {
            try {
                // Thử refresh token
                const newToken = await handleTokenRefresh();
                if (newToken) {
                    // Thực hiện lại yêu cầu với token mới
                    return await executeRequest(newToken);
                }
            } catch (refreshError) {
                console.error('Error refreshing token:', refreshError);
                // Nếu refresh token thất bại, đăng xuất người dùng
                __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$auth$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["authService"].logout();
                throw error; // Trả về lỗi ban đầu
            }
        }
        console.error('API request error:', error);
        throw error;
    }
}
const api = {
    baseUrl: NEXT_PUBLIC_API_URL,
    getToken: ()=>{
        return ("TURBOPACK compile-time falsy", 0) ? ("TURBOPACK unreachable", undefined) : null;
    },
    get: (endpoint, options)=>fetchApi(endpoint, {
            ...options,
            method: 'GET'
        }),
    post: (endpoint, data, options)=>fetchApi(endpoint, {
            ...options,
            method: 'POST',
            body: data ? JSON.stringify(data) : undefined
        }),
    put: (endpoint, data, options)=>fetchApi(endpoint, {
            ...options,
            method: 'PUT',
            body: data ? JSON.stringify(data) : undefined
        }),
    patch: (endpoint, data, options)=>fetchApi(endpoint, {
            ...options,
            method: 'PATCH',
            body: data ? JSON.stringify(data) : undefined
        }),
    delete: (endpoint, options)=>fetchApi(endpoint, {
            ...options,
            method: 'DELETE'
        }),
    // Phương thức download file
    downloadFile: async (endpoint, format, filename)=>{
        const url = `${NEXT_PUBLIC_API_URL}${endpoint.startsWith('/') ? endpoint : `/${endpoint}`}`;
        const token = ("TURBOPACK compile-time falsy", 0) ? ("TURBOPACK unreachable", undefined) : null;
        try {
            // Sử dụng XMLHttpRequest thay vì fetch để xử lý tải file tốt hơn
            return new Promise((resolve, reject)=>{
                const xhr = new XMLHttpRequest();
                xhr.open('GET', url, true);
                xhr.responseType = 'blob';
                xhr.setRequestHeader('Authorization', ("TURBOPACK compile-time falsy", 0) ? ("TURBOPACK unreachable", undefined) : '');
                xhr.onload = function() {
                    if (this.status === 200) {
                        const blob = new Blob([
                            this.response
                        ], {
                            type: format === 'csv' ? 'text/csv' : 'application/json'
                        });
                        const downloadUrl = window.URL.createObjectURL(blob);
                        const a = document.createElement('a');
                        a.href = downloadUrl;
                        a.download = filename;
                        document.body.appendChild(a);
                        a.click();
                        // Cleanup
                        window.URL.revokeObjectURL(downloadUrl);
                        document.body.removeChild(a);
                        resolve(true);
                    } else {
                        reject(new Error(`Download failed: ${this.status} ${this.statusText}`));
                    }
                };
                xhr.onerror = function() {
                    reject(new Error('Network error occurred'));
                };
                xhr.send();
            });
        } catch (error) {
            console.error('Download error:', error);
            throw error;
        }
    }
};
}}),
"[project]/lib/auth.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * Service xử lý xác thực người dùng
 */ __turbopack_context__.s({
    "authService": (()=>authService),
    "decodeToken": (()=>decodeToken),
    "isTokenExpired": (()=>isTokenExpired),
    "isTokenExpiringSoon": (()=>isTokenExpiringSoon)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$api$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/api.ts [app-ssr] (ecmascript)");
;
const decodeToken = (token)=>{
    try {
        if (!token) return null;
        // JWT token có 3 phần: header.payload.signature
        const base64Url = token.split('.')[1];
        if (!base64Url) return null;
        // Thay thế các ký tự đặc biệt trong base64Url
        const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');
        // Giải mã base64 thành JSON
        const jsonPayload = decodeURIComponent(atob(base64).split('').map((c)=>'%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2)).join(''));
        const payload = JSON.parse(jsonPayload);
        // Chuyển đổi payload thành User
        const user = {
            id: payload.sub || payload.id || '',
            username: payload.username || '',
            email: payload.email || '',
            fullName: payload.fullName || payload.name || '',
            roles: payload.roles || [],
            permissions: payload.permissions || [],
            exp: payload.exp || 0
        };
        return user;
    } catch (error) {
        console.error('Error decoding token:', error);
        return null;
    }
};
const isTokenExpired = (token)=>{
    try {
        if (!token) return true;
        const decodedToken = decodeToken(token);
        if (!decodedToken || !decodedToken.exp) return true;
        // exp là thời gian hết hạn tính bằng giây kể từ epoch (1/1/1970)
        const currentTime = Math.floor(Date.now() / 1000); // Chuyển đổi thời gian hiện tại sang giây
        // Trả về true nếu token đã hết hạn
        return decodedToken.exp < currentTime;
    } catch (error) {
        console.error('Error checking token expiration:', error);
        return true; // Nếu có lỗi, coi như token đã hết hạn
    }
};
const isTokenExpiringSoon = (token, thresholdSeconds = 300)=>{
    try {
        if (!token) return true;
        const decodedToken = decodeToken(token);
        if (!decodedToken || !decodedToken.exp) return true;
        const currentTime = Math.floor(Date.now() / 1000);
        // Trả về true nếu token sắp hết hạn trong vòng thresholdSeconds giây
        return decodedToken.exp - currentTime < thresholdSeconds;
    } catch (error) {
        console.error('Error checking token expiration:', error);
        return true;
    }
};
const authService = {
    /**
   * Đăng nhập người dùng
   */ async login (credentials) {
        try {
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$api$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["api"].post('auth/login', credentials);
            // Kiểm tra cấu trúc response
            const authData = response.data || response;
            // Lưu token vào localStorage và cookie
            localStorage.setItem('accessToken', authData.access_token);
            localStorage.setItem('refreshToken', authData.refresh_token);
            // Lưu token vào cookie để middleware có thể truy cập
            document.cookie = `token=${authData.access_token}; path=/; max-age=${60 * 60 * 24 * 7}; SameSite=Lax`; // 7 ngày
            // Nếu không có thông tin người dùng trong response, giải mã token để lấy
            if (!authData.user && authData.access_token) {
                const decodedUser = decodeToken(authData.access_token);
                if (decodedUser) {
                    authData.user = decodedUser;
                    this.saveUser(decodedUser);
                }
            } else if (authData.user) {
                // Lưu thông tin người dùng
                this.saveUser(authData.user);
            }
            return authData;
        } catch (error) {
            console.error('Login error:', error);
            throw error;
        }
    },
    /**
   * Đăng ký người dùng mới
   */ async register (credentials) {
        try {
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$api$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["api"].post('auth/register', credentials);
            // Kiểm tra cấu trúc response
            const authData = response.data || response;
            // Lưu token vào localStorage và cookie
            localStorage.setItem('accessToken', authData.access_token);
            localStorage.setItem('refreshToken', authData.refresh_token);
            // Lưu token vào cookie để middleware có thể truy cập
            document.cookie = `token=${authData.access_token}; path=/; max-age=${60 * 60 * 24 * 7}; SameSite=Lax`; // 7 ngày
            // Nếu không có thông tin người dùng trong response, giải mã token để lấy
            if (!authData.user && authData.access_token) {
                const decodedUser = decodeToken(authData.access_token);
                if (decodedUser) {
                    authData.user = decodedUser;
                    this.saveUser(decodedUser);
                }
            } else if (authData.user) {
                // Lưu thông tin người dùng
                this.saveUser(authData.user);
            }
            return authData;
        } catch (error) {
            console.error('Register error:', error);
            throw error;
        }
    },
    /**
   * Làm mới token
   */ async refreshToken (refreshToken) {
        try {
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$api$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["api"].post('auth/refresh-token', {
                refreshToken
            });
            // Kiểm tra cấu trúc response
            const authData = response.data || response;
            // Cập nhật token trong localStorage và cookie
            localStorage.setItem('accessToken', authData.access_token);
            localStorage.setItem('refreshToken', authData.refresh_token);
            // Cập nhật token trong cookie để middleware có thể truy cập
            document.cookie = `token=${authData.access_token}; path=/; max-age=${60 * 60 * 24 * 7}; SameSite=Lax`; // 7 ngày
            // Nếu không có thông tin người dùng trong response, giải mã token để lấy
            if (!authData.user && authData.access_token) {
                const decodedUser = decodeToken(authData.access_token);
                if (decodedUser) {
                    authData.user = decodedUser;
                    this.saveUser(decodedUser);
                }
            } else if (authData.user) {
                // Lưu thông tin người dùng
                this.saveUser(authData.user);
            }
            return authData;
        } catch (error) {
            console.error('Refresh token error:', error);
            // Nếu refresh token thất bại, đăng xuất người dùng
            this.logout();
            throw error;
        }
    },
    /**
   * Thử làm mới token nếu có refresh token
   */ async tryRefreshToken () {
        try {
            const refreshToken = this.getRefreshToken();
            if (!refreshToken) return false;
            const result = await this.refreshToken(refreshToken);
            // Việc điều hướng sẽ được xử lý ở handleTokenRefresh trong api.ts
            // Không điều hướng ở đây để tránh điều hướng kép
            return true;
        } catch (error) {
            console.error('Try refresh token error:', error);
            return false;
        }
    },
    /**
   * Điều hướng người dùng dựa trên vai trò
   */ redirectBasedOnRole (user) {
        if ("TURBOPACK compile-time truthy", 1) return;
        "TURBOPACK unreachable";
        const isAdmin = undefined;
    },
    /**
   * Đăng xuất người dùng
   */ logout () {
        localStorage.removeItem('accessToken');
        localStorage.removeItem('refreshToken');
        localStorage.removeItem('user');
        // Xóa cookie token
        document.cookie = 'token=; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT; SameSite=Lax';
        // Chuyển hướng về trang đăng nhập nếu cần
        if ("TURBOPACK compile-time falsy", 0) {
            "TURBOPACK unreachable";
        }
    },
    /**
   * Kiểm tra người dùng đã đăng nhập chưa
   */ isAuthenticated () {
        if ("TURBOPACK compile-time truthy", 1) return false;
        "TURBOPACK unreachable";
        // Kiểm tra token trong localStorage hoặc cookie
        const localToken = undefined;
        // Kiểm tra token trong cookie
        const cookies = undefined;
        let i;
    },
    /**
   * Lấy thông tin người dùng hiện tại
   */ getCurrentUser () {
        if ("TURBOPACK compile-time truthy", 1) return null;
        "TURBOPACK unreachable";
        const userJson = undefined;
    },
    /**
   * Lưu thông tin người dùng
   */ saveUser (user) {
        localStorage.setItem('user', JSON.stringify(user));
    },
    /**
   * Lấy token hiện tại
   */ getAccessToken () {
        if ("TURBOPACK compile-time truthy", 1) return null;
        "TURBOPACK unreachable";
    },
    /**
   * Lấy refresh token
   */ getRefreshToken () {
        if ("TURBOPACK compile-time truthy", 1) return null;
        "TURBOPACK unreachable";
    },
    /**
   * Xác minh số điện thoại cho người dùng hiện có
   * @param userId ID của người dùng
   * @param firebaseToken Firebase ID token
   * @returns Kết quả xác minh
   */ async verifyPhoneNumber (userId, firebaseToken) {
        try {
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$api$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["api"].post('auth/verify-phone-public', {
                userId,
                firebaseToken
            });
            return response.data || response;
        } catch (error) {
            console.error('Phone verification error:', error);
            throw error;
        }
    },
    /**
   * Unified OTP verification for both phone and email
   * @param verificationData Verification data
   * @returns Verification result with tokens
   */ async verifyOtp (verificationData) {
        try {
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$api$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["api"].post('auth/verify-otp', verificationData);
            // Kiểm tra cấu trúc response
            const authData = response.data || response;
            // Lưu token vào localStorage và cookie
            localStorage.setItem('accessToken', authData.access_token);
            localStorage.setItem('refreshToken', authData.refresh_token);
            // Lưu token vào cookie để middleware có thể truy cập
            document.cookie = `token=${authData.access_token}; path=/; max-age=${60 * 60 * 24 * 7}; SameSite=Lax`; // 7 ngày
            // Lưu thông tin user vào localStorage
            if (authData.user) {
                localStorage.setItem('user', JSON.stringify(authData.user));
            }
            return authData;
        } catch (error) {
            console.error('OTP verification error:', error);
            throw error;
        }
    }
};
}}),
"[project]/hooks/use-auth.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "AuthProvider": (()=>AuthProvider),
    "useAuth": (()=>useAuth)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$opentelemetry$2b$_ee4fdf7136c0ae57e36e5bb24f18c6a1$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/next@15.3.0_@opentelemetry+_ee4fdf7136c0ae57e36e5bb24f18c6a1/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$opentelemetry$2b$_ee4fdf7136c0ae57e36e5bb24f18c6a1$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/next@15.3.0_@opentelemetry+_ee4fdf7136c0ae57e36e5bb24f18c6a1/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$auth$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/auth.ts [app-ssr] (ecmascript)");
"use client";
;
;
;
// Tạo AuthContext
const AuthContext = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$opentelemetry$2b$_ee4fdf7136c0ae57e36e5bb24f18c6a1$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createContext"])(undefined);
const AuthProvider = ({ children })=>{
    const [user, setUser] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$opentelemetry$2b$_ee4fdf7136c0ae57e36e5bb24f18c6a1$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(null);
    const [isLoading, setIsLoading] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$opentelemetry$2b$_ee4fdf7136c0ae57e36e5bb24f18c6a1$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(true);
    // Hàm tự động đăng nhập với token
    const autoLogin = async ()=>{
        try {
            // Kiểm tra xác thực từ localStorage hoặc cookie
            if (__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$auth$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["authService"].isAuthenticated()) {
                // Thử lấy thông tin người dùng từ localStorage
                let currentUser = __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$auth$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["authService"].getCurrentUser();
                // Nếu không có thông tin người dùng trong localStorage, thử giải mã token
                if (!currentUser) {
                    const accessToken = __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$auth$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["authService"].getAccessToken();
                    if (accessToken) {
                        currentUser = (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$auth$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["decodeToken"])(accessToken);
                        if (currentUser) {
                            __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$auth$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["authService"].saveUser(currentUser);
                        }
                    }
                }
                if (currentUser) {
                    // Chuyển đổi từ ApiUser sang User
                    const userData = {
                        id: currentUser.id,
                        name: currentUser.fullName || currentUser.username || '',
                        email: currentUser.email,
                        roles: currentUser.roles || [],
                        fullName: currentUser.fullName,
                        username: currentUser.username,
                        phone: currentUser.phone,
                        address: currentUser.address
                    };
                    setUser(userData);
                    return true;
                }
            }
            return false;
        } catch (error) {
            console.error("Error in auto login:", error);
            return false;
        } finally{
            setIsLoading(false);
        }
    };
    // Tham chiếu đến interval để có thể xóa khi component unmount
    const tokenCheckIntervalRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$opentelemetry$2b$_ee4fdf7136c0ae57e36e5bb24f18c6a1$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useRef"])(null);
    // Hàm kiểm tra token định kỳ
    const setupTokenCheck = ()=>{
        // Xóa interval cũ nếu có
        if (tokenCheckIntervalRef.current) {
            clearInterval(tokenCheckIntervalRef.current);
        }
        // Tạo interval mới để kiểm tra token mỗi phút
        tokenCheckIntervalRef.current = setInterval(()=>{
            const token = __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$auth$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["authService"].getAccessToken();
            if (token) {
                // Nếu token đã hết hạn, thử refresh token
                if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$auth$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isTokenExpired"])(token)) {
                    __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$auth$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["authService"].tryRefreshToken().catch(()=>{
                        // Nếu refresh token thất bại, đăng xuất người dùng
                        logout();
                    });
                } else if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$auth$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isTokenExpiringSoon"])(token)) {
                    __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$auth$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["authService"].tryRefreshToken().catch((error)=>{
                        console.error('Error refreshing token:', error);
                    });
                }
            }
        }, 60000); // Kiểm tra mỗi phút
    };
    // Kiểm tra xem người dùng đã đăng nhập chưa khi component được mount
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$opentelemetry$2b$_ee4fdf7136c0ae57e36e5bb24f18c6a1$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        const checkAuth = async ()=>{
            try {
                const success = await autoLogin();
                if (success) {
                    // Nếu đăng nhập thành công, thiết lập kiểm tra token định kỳ
                    setupTokenCheck();
                }
            } catch (error) {
                console.error("Error checking auth:", error);
            } finally{
                setIsLoading(false);
            }
        };
        checkAuth();
        // Cleanup khi component unmount
        return ()=>{
            if (tokenCheckIntervalRef.current) {
                clearInterval(tokenCheckIntervalRef.current);
            }
        };
    }, []);
    // Hàm kiểm tra xem người dùng có role cụ thể không
    const hasRole = (roleName)=>{
        if (!user || !user.roles) return false;
        return user.roles.includes(roleName);
    };
    // Hàm kiểm tra xem người dùng có phải là admin không
    const isAdmin = ()=>{
        return hasRole("ADMIN");
    };
    // Hàm kiểm tra xem người dùng có phải là agent không
    const isAgent = ()=>{
        return hasRole("AGENT");
    };
    // Hàm đăng nhập
    const login = async (credentials)=>{
        setIsLoading(true);
        try {
            // Gọi API đăng nhập thực tế
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$auth$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["authService"].login(credentials);
            // Lấy thông tin người dùng từ response hoặc từ token
            let userData = null;
            if (response.user) {
                // Chuyển đổi từ ApiUser sang User
                userData = {
                    id: response.user.id,
                    name: response.user.fullName || response.user.username || '',
                    email: response.user.email,
                    roles: response.user.roles || [],
                    fullName: response.user.fullName,
                    username: response.user.username,
                    phone: response.user.phone,
                    address: response.user.address
                };
            } else if (response.access_token) {
                // Nếu không có thông tin người dùng trong response, giải mã token
                const decodedUser = (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$auth$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["decodeToken"])(response.access_token);
                if (decodedUser) {
                    userData = {
                        id: decodedUser.id,
                        name: decodedUser.fullName || decodedUser.username || '',
                        email: decodedUser.email,
                        roles: decodedUser.roles || [],
                        fullName: decodedUser.fullName,
                        username: decodedUser.username,
                        phone: decodedUser.phone,
                        address: decodedUser.address
                    };
                }
            }
            if (userData) {
                setUser(userData);
                // Thiết lập kiểm tra token định kỳ sau khi đăng nhập thành công
                setupTokenCheck();
            }
        } catch (error) {
            console.error("Login error:", error);
            throw error;
        } finally{
            setIsLoading(false);
        }
    };
    // Hàm đăng xuất
    const logout = ()=>{
        // Xóa interval kiểm tra token
        if (tokenCheckIntervalRef.current) {
            clearInterval(tokenCheckIntervalRef.current);
            tokenCheckIntervalRef.current = null;
        }
        __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$auth$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["authService"].logout();
        setUser(null);
    };
    // Hàm đăng ký
    const register = async (credentials)=>{
        setIsLoading(true);
        try {
            // Gọi API đăng ký thực tế
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$auth$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["authService"].register(credentials);
            // Lấy thông tin người dùng từ response hoặc từ token
            let userData = null;
            if (response.user) {
                // Chuyển đổi từ ApiUser sang User
                userData = {
                    id: response.user.id,
                    name: response.user.fullName || response.user.username || '',
                    email: response.user.email,
                    roles: response.user.roles || [],
                    fullName: response.user.fullName,
                    username: response.user.username,
                    phone: response.user.phone,
                    address: response.user.address
                };
            } else if (response.access_token) {
                // Nếu không có thông tin người dùng trong response, giải mã token
                const decodedUser = (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$auth$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["decodeToken"])(response.access_token);
                if (decodedUser) {
                    userData = {
                        id: decodedUser.id,
                        name: decodedUser.fullName || decodedUser.username || '',
                        email: decodedUser.email,
                        roles: decodedUser.roles || [],
                        fullName: decodedUser.fullName,
                        username: decodedUser.username,
                        phone: decodedUser.phone,
                        address: decodedUser.address
                    };
                }
            }
            if (userData) {
                setUser(userData);
                // Thiết lập kiểm tra token định kỳ sau khi đăng ký thành công
                setupTokenCheck();
            }
        } catch (error) {
            console.error("Register error:", error);
            throw error;
        } finally{
            setIsLoading(false);
        }
    };
    const value = {
        user,
        isLoading,
        login,
        logout,
        register,
        hasRole,
        isAdmin,
        isAgent,
        autoLogin
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$opentelemetry$2b$_ee4fdf7136c0ae57e36e5bb24f18c6a1$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(AuthContext.Provider, {
        value: value,
        children: children
    }, void 0, false, {
        fileName: "[project]/hooks/use-auth.tsx",
        lineNumber: 297,
        columnNumber: 10
    }, this);
};
function useAuth() {
    const context = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$opentelemetry$2b$_ee4fdf7136c0ae57e36e5bb24f18c6a1$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useContext"])(AuthContext);
    if (context === undefined) {
        throw new Error("useAuth must be used within an AuthProvider");
    }
    return context;
}
}}),
"[project]/lib/local-storage-utils.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * Check if cached data is still valid
 */ __turbopack_context__.s({
    "clearAllCache": (()=>clearAllCache),
    "clearCacheByKey": (()=>clearCacheByKey),
    "isCacheValid": (()=>isCacheValid),
    "loadAllCache": (()=>loadAllCache),
    "loadCacheFromStorage": (()=>loadCacheFromStorage),
    "saveCacheToStorage": (()=>saveCacheToStorage)
});
function isCacheValid(cache) {
    if (!cache) return false;
    const now = Date.now();
    return now < cache.expiresAt;
}
function clearCacheByKey(cacheKey) {
    if ("TURBOPACK compile-time falsy", 0) {
        "TURBOPACK unreachable";
    }
}
function clearAllCache() {
    if ("TURBOPACK compile-time falsy", 0) {
        "TURBOPACK unreachable";
    }
}
function saveCacheToStorage(cacheKey, cache) {
    if ("TURBOPACK compile-time truthy", 1) return;
    "TURBOPACK unreachable";
}
function loadCacheFromStorage(cacheKey) {
    if ("TURBOPACK compile-time truthy", 1) return;
    "TURBOPACK unreachable";
}
function loadAllCache() {
    if ("TURBOPACK compile-time truthy", 1) return [];
    "TURBOPACK unreachable";
    const cacheKeys = undefined;
}
}}),
"[project]/lib/system-metadata.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * Site Metadata Service
 * Handles fetching, caching, and managing site metadata from system config API
 */ __turbopack_context__.s({
    "SiteMetadataService": (()=>SiteMetadataService),
    "siteMetadataService": (()=>siteMetadataService)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$api$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/api.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$local$2d$storage$2d$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/local-storage-utils.ts [app-ssr] (ecmascript)");
;
;
const CACHE_KEY = 'site_metadata_cache';
const DEFAULT_TTL = 24 * 60 * 60 * 1000; // 24 hours in milliseconds
class SiteMetadataService {
    static instance;
    cache = null;
    constructor(){
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$local$2d$storage$2d$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["loadAllCache"])();
    }
    static getInstance() {
        if (!SiteMetadataService.instance) {
            SiteMetadataService.instance = new SiteMetadataService();
        }
        return SiteMetadataService.instance;
    }
    /**
   * Fetch system config from backend API
   */ async fetchSystemConfigFromAPI() {
        // Only fetch configs for SEO, header, and footer groups
        return await __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$api$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["api"].get('/public/system-configs?limit=300&filter=configGroup:website_seo|general', {
            cache: "no-cache"
        });
    }
    /**
   * Get metadata from system config
   */ async getMetadata() {
        // // Check cache first
        // if (this.isCacheValid() && this.cache) {
        //   return this.cache.data;
        // }
        try {
            // Always fetch from API first
            const systemConfig = await this.fetchSystemConfigFromAPI();
            // Cache the result
            const now = Date.now();
            this.cache = {
                data: systemConfig,
                cachedAt: now,
                expiresAt: now + DEFAULT_TTL
            };
            // Save to localStorage
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$local$2d$storage$2d$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["saveCacheToStorage"])(CACHE_KEY, this.cache);
            return systemConfig;
        } catch (error) {
            console.error('Failed to fetch system config:', error);
            // If API call fails, return cached data if available
            if (this.cache) {
                return this.cache.data;
            }
            return {};
        }
    }
    /**
   * Refresh metadata
   */ async refreshMetadata() {
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$local$2d$storage$2d$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["clearAllCache"])();
        return this.getMetadata();
    }
    /**
   * Get metadata from system config by group
   */ async getMetadataByGroup(configGroup) {
        const CACHE_KEY_GROUP = `site_metadata_cache_${configGroup}`;
        // Check cache first
        // if (this.isCacheValid() && this.cache) {
        //   return this.cache.data;
        // }
        try {
            // Always fetch from API first
            const systemConfig = await __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$api$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["api"].get(`/public/system-configs?limit=100&filter=configGroup:${configGroup}`);
            // Cache the result
            const now = Date.now();
            const cacheObj = {
                data: systemConfig,
                cachedAt: now,
                expiresAt: now + DEFAULT_TTL
            };
            // Save to localStorage
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$local$2d$storage$2d$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["saveCacheToStorage"])(CACHE_KEY_GROUP, cacheObj);
            return systemConfig;
        } catch (error) {
            // If API call fails, try to get from cache
            if ("TURBOPACK compile-time falsy", 0) {
                "TURBOPACK unreachable";
            }
            return {};
        }
    }
}
const siteMetadataService = SiteMetadataService.getInstance();
}}),
"[project]/lib/utils.ts [app-ssr] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "calculateDeliveryProductPrice": (()=>calculateDeliveryProductPrice),
    "calculateDeposit": (()=>calculateDeposit),
    "calculateExchangePrice": (()=>calculateExchangePrice),
    "calculateNormalProductPrice": (()=>calculateNormalProductPrice),
    "calculateProcessingFee": (()=>calculateProcessingFee),
    "calculateSettlement": (()=>calculateSettlement),
    "calculateTotal": (()=>calculateTotal),
    "censorEmail": (()=>censorEmail),
    "censorPhone": (()=>censorPhone),
    "cn": (()=>cn),
    "formatCurrency": (()=>formatCurrency),
    "formatDate": (()=>formatDate),
    "formatDateTime": (()=>formatDateTime),
    "formatExchangePrice": (()=>formatExchangePrice),
    "formatFullDateTime": (()=>formatFullDateTime),
    "formatNumber": (()=>formatNumber),
    "formatPercentage": (()=>formatPercentage),
    "formatTime": (()=>formatTime),
    "formatTokenPrice": (()=>formatTokenPrice),
    "safeParseJSON": (()=>safeParseJSON)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$kayron013$2b$lexorank$40$2$2e$0$2e$0$2f$node_modules$2f40$kayron013$2f$lexorank$2f$lib$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@kayron013+lexorank@2.0.0/node_modules/@kayron013/lexorank/lib/index.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$clsx$40$2$2e$1$2e$1$2f$node_modules$2f$clsx$2f$dist$2f$clsx$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/clsx@2.1.1/node_modules/clsx/dist/clsx.mjs [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$date$2d$fns$40$4$2e$1$2e$0$2f$node_modules$2f$date$2d$fns$2f$format$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/format.js [app-ssr] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$date$2d$fns$40$4$2e$1$2e$0$2f$node_modules$2f$date$2d$fns$2f$isValid$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/isValid.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$date$2d$fns$40$4$2e$1$2e$0$2f$node_modules$2f$date$2d$fns$2f$locale$2f$vi$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/vi.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$tailwind$2d$merge$40$3$2e$2$2e$0$2f$node_modules$2f$tailwind$2d$merge$2f$dist$2f$bundle$2d$mjs$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/tailwind-merge@3.2.0/node_modules/tailwind-merge/dist/bundle-mjs.mjs [app-ssr] (ecmascript)");
;
;
;
;
;
function cn(...inputs) {
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$tailwind$2d$merge$40$3$2e$2$2e$0$2f$node_modules$2f$tailwind$2d$merge$2f$dist$2f$bundle$2d$mjs$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["twMerge"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$clsx$40$2$2e$1$2e$1$2f$node_modules$2f$clsx$2f$dist$2f$clsx$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["clsx"])(inputs));
}
function formatCurrency(amount, options) {
    if (amount === undefined || amount === null) return "---";
    return new Intl.NumberFormat('vi-VN', {
        minimumFractionDigits: 0,
        maximumFractionDigits: 0,
        ...options
    }).format(amount);
}
function formatNumber(value, decimalPlaces = 0) {
    if (value === undefined || value === null) return "---";
    return new Intl.NumberFormat('vi-VN', {
        minimumFractionDigits: decimalPlaces,
        maximumFractionDigits: decimalPlaces
    }).format(value);
}
function formatPercentage(value, decimalPlaces = 2) {
    if (value === undefined || value === null) return "---";
    return new Intl.NumberFormat('vi-VN', {
        style: 'percent',
        minimumFractionDigits: decimalPlaces,
        maximumFractionDigits: decimalPlaces
    }).format(value);
}
function calculateExchangePrice(usdPrice, exchangeRate = 27000) {
    return Math.round(usdPrice * exchangeRate);
}
function formatExchangePrice(usdPrice, exchangeRate = 27000) {
    if (usdPrice === undefined || usdPrice === null) return "---";
    const vndPrice = calculateExchangePrice(usdPrice, exchangeRate);
    return new Intl.NumberFormat('vi-VN', {
        style: 'currency',
        currency: 'VND',
        maximumFractionDigits: 0
    }).format(vndPrice);
}
function calculateNormalProductPrice(priceVND, oz = 1, quantity = 1) {
    if (isNaN(priceVND) || priceVND <= 0 || isNaN(oz) || oz <= 0) {
        return 0;
    }
    return Math.round(priceVND * oz * quantity);
}
function calculateDeliveryProductPrice(priceVND, oz = 1, quantity = 1) {
    if (isNaN(priceVND) || priceVND <= 0 || isNaN(oz) || oz <= 0) {
        return 0;
    }
    // Giá = (giá * oz * số lượng) + phí gia công
    // Phí gia công = (oz * số lượng) * 50,000
    // Ví dụ: sản phẩm 3oz, số lượng 2
    // - Giá = 761,000 * 3 * 2 = 4,566,000
    // - Phí gia công = (3 * 2) * 50,000 = 300,000
    // - Tổng = 4,566,000 + 300,000 = 4,866,000
    const totalOz = oz * quantity;
    return Math.round(priceVND * oz * quantity + calculateProcessingFee(totalOz));
}
function calculateProcessingFee(oz = 1) {
    if (isNaN(oz) || oz <= 0) {
        return 0;
    }
    // Phí gia công = tổng oz * 50,000
    return Math.round(oz * 50000);
}
function formatTokenPrice(priceVND, oz = 1, volume = 1, isDelivery = false) {
    if (priceVND === undefined || priceVND === null) return "---";
    const price = isDelivery ? calculateDeliveryProductPrice(priceVND, oz, volume) : calculateNormalProductPrice(priceVND, oz, volume);
    return formatCurrency(price);
}
function calculateTotal(items, priceVND, orderType, businessType) {
    // Kiểm tra input
    if (!items?.length || priceVND <= 0) return 0;
    return items.reduce((total, item)=>{
        // Bỏ qua nếu không có thông tin sản phẩm hoặc số lượng
        if (!item.productId || !item.volume || !item.productInfo?.weight) return total;
        const weight = item.productInfo.weight;
        const volume = item.volume;
        // Xử lý theo loại lệnh
        if (orderType === 'buy') {
            // Lệnh mua: phân biệt theo businessType
            if (businessType === 'IMMEDIATE_DELIVERY') {
                // Bạc giao ngay: giá = (giá * oz * số lượng) + phí gia công
                return total + calculateDeliveryProductPrice(priceVND, weight, volume);
            } else {
                // Bạc ký quỹ online: giá = giá * oz * số lượng
                return total + calculateNormalProductPrice(priceVND, weight, volume);
            }
        } else {
            // Lệnh bán: luôn tính như bạc ký quỹ
            return total + calculateNormalProductPrice(priceVND, weight, volume);
        }
    }, 0);
}
function calculateDeposit(items, priceVND, orderType, businessType) {
    const total = calculateTotal(items, priceVND, orderType, businessType);
    return total * 0.1; // 10% tổng giá trị
}
function calculateSettlement(items, worldPrice) {
    return calculateTotal(items, worldPrice) * 0.9;
}
function formatDateTime(date, emptyValue = "---") {
    if (date === null || date === undefined) return emptyValue;
    const dateObj = typeof date === 'string' || typeof date === 'number' ? new Date(date) : date;
    if (!(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$date$2d$fns$40$4$2e$1$2e$0$2f$node_modules$2f$date$2d$fns$2f$isValid$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isValid"])(dateObj)) return "Không hợp lệ";
    try {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$date$2d$fns$40$4$2e$1$2e$0$2f$node_modules$2f$date$2d$fns$2f$format$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["format"])(dateObj, "dd/MM/yyyy HH:mm", {
            locale: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$date$2d$fns$40$4$2e$1$2e$0$2f$node_modules$2f$date$2d$fns$2f$locale$2f$vi$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["vi"]
        });
    } catch (error) {
        return "Không hợp lệ";
    }
}
function formatDate(date, emptyValue = "---") {
    if (date === null || date === undefined) return emptyValue;
    const dateObj = typeof date === 'string' || typeof date === 'number' ? new Date(date) : date;
    if (!(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$date$2d$fns$40$4$2e$1$2e$0$2f$node_modules$2f$date$2d$fns$2f$isValid$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isValid"])(dateObj)) return "Không hợp lệ";
    try {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$date$2d$fns$40$4$2e$1$2e$0$2f$node_modules$2f$date$2d$fns$2f$format$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["format"])(dateObj, "dd/MM/yyyy", {
            locale: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$date$2d$fns$40$4$2e$1$2e$0$2f$node_modules$2f$date$2d$fns$2f$locale$2f$vi$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["vi"]
        });
    } catch (error) {
        return "Không hợp lệ";
    }
}
function formatTime(date, emptyValue = "---") {
    if (date === null || date === undefined) return emptyValue;
    const dateObj = typeof date === 'string' || typeof date === 'number' ? new Date(date) : date;
    if (!(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$date$2d$fns$40$4$2e$1$2e$0$2f$node_modules$2f$date$2d$fns$2f$isValid$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isValid"])(dateObj)) return "Không hợp lệ";
    try {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$date$2d$fns$40$4$2e$1$2e$0$2f$node_modules$2f$date$2d$fns$2f$format$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["format"])(dateObj, "HH:mm:ss", {
            locale: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$date$2d$fns$40$4$2e$1$2e$0$2f$node_modules$2f$date$2d$fns$2f$locale$2f$vi$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["vi"]
        });
    } catch (error) {
        return "Không hợp lệ";
    }
}
function formatFullDateTime(date, emptyValue = "---") {
    if (date === null || date === undefined) return emptyValue;
    const dateObj = typeof date === 'string' || typeof date === 'number' ? new Date(date) : date;
    if (!(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$date$2d$fns$40$4$2e$1$2e$0$2f$node_modules$2f$date$2d$fns$2f$isValid$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isValid"])(dateObj)) return "Không hợp lệ";
    try {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$date$2d$fns$40$4$2e$1$2e$0$2f$node_modules$2f$date$2d$fns$2f$format$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["format"])(dateObj, "dd/MM/yyyy HH:mm:ss", {
            locale: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$date$2d$fns$40$4$2e$1$2e$0$2f$node_modules$2f$date$2d$fns$2f$locale$2f$vi$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["vi"]
        });
    } catch (error) {
        return "Không hợp lệ";
    }
}
function censorPhone(phone) {
    if (!phone || phone.length < 4) return phone;
    const visiblePart = phone.slice(-3);
    const hiddenPart = "*".repeat(Math.max(0, phone.length - 3));
    return hiddenPart + visiblePart;
}
function censorEmail(email) {
    if (!email || !email.includes('@')) return email;
    const [localPart, domain] = email.split('@');
    if (localPart.length <= 2) return email;
    const visibleStart = localPart.slice(0, 2);
    const visibleEnd = localPart.slice(-1);
    const hiddenPart = "*".repeat(Math.max(0, localPart.length - 3));
    return `${visibleStart}${hiddenPart}${visibleEnd}@${domain}`;
}
function safeParseJSON(jsonString, fallback) {
    if (!jsonString) return fallback;
    try {
        return JSON.parse(jsonString);
    } catch (error) {
        console.warn('Failed to parse JSON:', error);
        return fallback;
    }
}
;
}}),
"[project]/lib/utils.ts [app-ssr] (ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$kayron013$2b$lexorank$40$2$2e$0$2e$0$2f$node_modules$2f40$kayron013$2f$lexorank$2f$lib$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@kayron013+lexorank@2.0.0/node_modules/@kayron013/lexorank/lib/index.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$clsx$40$2$2e$1$2e$1$2f$node_modules$2f$clsx$2f$dist$2f$clsx$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/clsx@2.1.1/node_modules/clsx/dist/clsx.mjs [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/lib/utils.ts [app-ssr] (ecmascript) <locals>");
}}),
"[project]/hooks/use-site-metadata.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * React Hook for Site Metadata
 */ __turbopack_context__.s({
    "useConfigValue": (()=>useConfigValue),
    "useDynamicMetadata": (()=>useDynamicMetadata),
    "useSEOMetadata": (()=>useSEOMetadata),
    "useSiteMetadata": (()=>useSiteMetadata)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$local$2d$storage$2d$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/local-storage-utils.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$system$2d$metadata$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/system-metadata.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/lib/utils.ts [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/lib/utils.ts [app-ssr] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$opentelemetry$2b$_ee4fdf7136c0ae57e36e5bb24f18c6a1$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/next@15.3.0_@opentelemetry+_ee4fdf7136c0ae57e36e5bb24f18c6a1/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
;
;
;
;
function useSiteMetadata() {
    const [config, setConfig] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$opentelemetry$2b$_ee4fdf7136c0ae57e36e5bb24f18c6a1$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(null);
    const [loading, setLoading] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$opentelemetry$2b$_ee4fdf7136c0ae57e36e5bb24f18c6a1$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(true);
    const [error, setError] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$opentelemetry$2b$_ee4fdf7136c0ae57e36e5bb24f18c6a1$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(null);
    const loadMetadata = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$opentelemetry$2b$_ee4fdf7136c0ae57e36e5bb24f18c6a1$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])(async ()=>{
        try {
            setLoading(true);
            setError(null);
            const data = await __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$system$2d$metadata$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["siteMetadataService"].getMetadata();
            setConfig(data);
        } catch (err) {
            console.error('Failed to load metadata:', err);
            setError(err instanceof Error ? err.message : 'Failed to load metadata');
        } finally{
            setLoading(false);
        }
    }, []);
    const refresh = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$opentelemetry$2b$_ee4fdf7136c0ae57e36e5bb24f18c6a1$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])(async ()=>{
        try {
            setLoading(true);
            setError(null);
            const data = await __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$system$2d$metadata$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["siteMetadataService"].refreshMetadata();
            setConfig(data);
        } catch (err) {
            setError(err instanceof Error ? err.message : 'Failed to refresh metadata');
        } finally{
            setLoading(false);
        }
    }, []);
    const clearCache = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$opentelemetry$2b$_ee4fdf7136c0ae57e36e5bb24f18c6a1$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])(()=>{
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$local$2d$storage$2d$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["clearAllCache"])();
    }, []);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$opentelemetry$2b$_ee4fdf7136c0ae57e36e5bb24f18c6a1$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        loadMetadata();
    }, [
        loadMetadata
    ]);
    return {
        config,
        loading,
        error,
        refresh,
        clearCache
    };
}
function useConfigValue(key) {
    const { config } = useSiteMetadata();
    return config ? config[key] : null;
}
function useSEOMetadata() {
    const { config, loading, error } = useSiteMetadata();
    const seoData = config ? {
        title: config['website_seo_title'] || '',
        description: config['website_seo_description'] || '',
        keywords: (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["safeParseJSON"])(config['website_seo_keywords'], []),
        author: config['website_seo_author'] || '',
        canonical: config['website_site_url'] || '',
        openGraph: {
            title: config['website_seo_og_title'] || '',
            description: config['website_seo_og_description'] || '',
            image: config['website_seo_og_image'] || '',
            type: config['website_seo_og_type'] || '',
            url: config['website_site_url'] || ''
        },
        twitter: {
            card: config['website_seo_twitter_card'] || '',
            site: config['website_seo_twitter_site'] || '',
            creator: config['website_seo_twitter_creator'] || '',
            image: config['website_seo_twitter_image'] || ''
        },
        icons: {
            favicon: config['website_favicon_url'] || '/favicon.ico',
            appleTouchIcon: config['website_apple_touch_icon'] || '',
            icon192: config['website_icon_192'] || '',
            icon512: config['website_icon_512'] || ''
        },
        manifest: config['website_manifest_url'] || '',
        themeColor: config['website_theme_color'] || ''
    } : null;
    return {
        seoData,
        loading,
        error
    };
}
function useDynamicMetadata(configGroup) {
    const [config, setConfig] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$opentelemetry$2b$_ee4fdf7136c0ae57e36e5bb24f18c6a1$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(null);
    const [loading, setLoading] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$opentelemetry$2b$_ee4fdf7136c0ae57e36e5bb24f18c6a1$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(true);
    const [error, setError] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$opentelemetry$2b$_ee4fdf7136c0ae57e36e5bb24f18c6a1$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(null);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$opentelemetry$2b$_ee4fdf7136c0ae57e36e5bb24f18c6a1$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        const loadData = async ()=>{
            try {
                setLoading(true);
                setError(null);
                const data = await __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$system$2d$metadata$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["siteMetadataService"].getMetadataByGroup(configGroup);
                setConfig(data);
            } catch (err) {
                console.error(`Failed to load ${configGroup} metadata:`, err);
                setError(err instanceof Error ? err.message : `Failed to load ${configGroup} metadata`);
            } finally{
                setLoading(false);
            }
        };
        loadData();
    }, [
        configGroup
    ]);
    return {
        config,
        loading,
        error
    };
}
}}),
"[project]/lib/assets.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * Asset path utilities - simplified without basePath
 */ /**
 * Get the correct asset path
 * @param path - The asset path (should start with /)
 * @returns The normalized asset path
 */ __turbopack_context__.s({
    "createUrl": (()=>createUrl),
    "getAssetPath": (()=>getAssetPath),
    "getCSSImageUrl": (()=>getCSSImageUrl),
    "getImageUrl": (()=>getImageUrl)
});
function getAssetPath(path) {
    // Ensure path starts with /
    return path.startsWith('/') ? path : `/${path}`;
}
function createUrl(path) {
    return path.startsWith('/') ? path : `/${path}`;
}
function getImageUrl(src) {
    // If it's already a full URL, return as is
    if (src.startsWith('http://') || src.startsWith('https://')) {
        return src;
    }
    // If it's a data URL, return as is
    if (src.startsWith('data:')) {
        return src;
    }
    // For relative URLs, just normalize the path
    return getAssetPath(src);
}
function getCSSImageUrl(imagePath) {
    return `url(${getImageUrl(imagePath)})`;
}
}}),
"[project]/components/dynamic-metadata.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * Dynamic Metadata Component
 * Generates metadata based on current domain and backend data
 */ __turbopack_context__.s({
    "DynamicMetadata": (()=>DynamicMetadata)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$opentelemetry$2b$_ee4fdf7136c0ae57e36e5bb24f18c6a1$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/next@15.3.0_@opentelemetry+_ee4fdf7136c0ae57e36e5bb24f18c6a1/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$hooks$2f$use$2d$site$2d$metadata$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/hooks/use-site-metadata.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$assets$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/assets.ts [app-ssr] (ecmascript)");
'use client';
;
;
;
function DynamicMetadata({ pageTitle, pageDescription, pageImage, pageType = 'website' }) {
    const { seoData, loading, error } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$hooks$2f$use$2d$site$2d$metadata$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useSEOMetadata"])();
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$opentelemetry$2b$_ee4fdf7136c0ae57e36e5bb24f18c6a1$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        if (!seoData || loading) return;
        // Update document title
        const title = pageTitle ? `${pageTitle} | ${seoData.title}` : seoData.title;
        if (title) {
            document.title = title;
        }
        // Update meta tags (only if values exist)
        if (pageDescription || seoData.description) {
            updateMetaTag('description', pageDescription || seoData.description);
        }
        if (seoData.keywords.length > 0) {
            updateMetaTag('keywords', seoData.keywords.join(', '));
        }
        if (seoData.author) {
            updateMetaTag('author', seoData.author);
        }
        if (seoData.themeColor) {
            updateMetaTag('theme-color', seoData.themeColor);
        }
        // Update Open Graph tags (only if values exist)
        const ogTitle = pageTitle || seoData.openGraph.title;
        if (ogTitle) {
            updateMetaTag('og:title', ogTitle, 'property');
        }
        const ogDescription = pageDescription || seoData.openGraph.description;
        if (ogDescription) {
            updateMetaTag('og:description', ogDescription, 'property');
        }
        const ogImage = pageImage || seoData.openGraph.image;
        if (ogImage) {
            updateMetaTag('og:image', (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$assets$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getImageUrl"])(ogImage), 'property');
        }
        if (pageType) {
            updateMetaTag('og:type', pageType, 'property');
        }
        if (seoData.openGraph.url) {
            updateMetaTag('og:url', seoData.openGraph.url, 'property');
        }
        // Update Twitter Card tags (only if values exist)
        if (seoData.twitter.card) {
            updateMetaTag('twitter:card', seoData.twitter.card, 'name');
        }
        if (seoData.twitter.site) {
            updateMetaTag('twitter:site', seoData.twitter.site, 'name');
        }
        if (seoData.twitter.creator) {
            updateMetaTag('twitter:creator', seoData.twitter.creator, 'name');
        }
        if (ogTitle) {
            updateMetaTag('twitter:title', ogTitle, 'name');
        }
        if (ogDescription) {
            updateMetaTag('twitter:description', ogDescription, 'name');
        }
        const twitterImage = pageImage || seoData.twitter.image;
        if (twitterImage) {
            updateMetaTag('twitter:image', (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$assets$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getImageUrl"])(twitterImage), 'name');
        }
        // Update canonical link (only if value exists)
        if (seoData.canonical) {
            updateCanonicalLink(seoData.canonical);
        }
        // Update favicon (only if value exists)
        if (seoData.icons.favicon) {
            updateFavicon((0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$assets$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getImageUrl"])(seoData.icons.favicon));
        }
        // Update manifest (only if value exists)
        if (seoData.manifest) {
            updateManifest((0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$assets$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getImageUrl"])(seoData.manifest));
        }
    }, [
        seoData,
        loading,
        pageTitle,
        pageDescription,
        pageImage,
        pageType
    ]);
    if (error) {
        console.error('Failed to load metadata:', error);
    }
    return null; // This component doesn't render anything
}
/**
 * Update or create meta tag
 */ function updateMetaTag(name, content, attribute = 'name') {
    let meta = document.querySelector(`meta[${attribute}="${name}"]`);
    if (!meta) {
        meta = document.createElement('meta');
        meta.setAttribute(attribute, name);
        document.head.appendChild(meta);
    }
    meta.content = content;
}
/**
 * Update canonical link
 */ function updateCanonicalLink(href) {
    let link = document.querySelector('link[rel="canonical"]');
    if (!link) {
        link = document.createElement('link');
        link.rel = 'canonical';
        document.head.appendChild(link);
    }
    link.href = href;
}
/**
 * Update favicon
 */ function updateFavicon(href) {
    let link = document.querySelector('link[rel="icon"]');
    if (!link) {
        link = document.createElement('link');
        link.rel = 'icon';
        document.head.appendChild(link);
    }
    link.href = href;
}
/**
 * Update manifest link
 */ function updateManifest(href) {
    let link = document.querySelector('link[rel="manifest"]');
    if (!link) {
        link = document.createElement('link');
        link.rel = 'manifest';
        document.head.appendChild(link);
    }
    link.href = href;
}
}}),
"[externals]/next/dist/server/app-render/work-async-storage.external.js [external] (next/dist/server/app-render/work-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-async-storage.external.js", () => require("next/dist/server/app-render/work-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/action-async-storage.external.js [external] (next/dist/server/app-render/action-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/action-async-storage.external.js", () => require("next/dist/server/app-render/action-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-unit-async-storage.external.js [external] (next/dist/server/app-render/work-unit-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-unit-async-storage.external.js", () => require("next/dist/server/app-render/work-unit-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/after-task-async-storage.external.js [external] (next/dist/server/app-render/after-task-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/after-task-async-storage.external.js", () => require("next/dist/server/app-render/after-task-async-storage.external.js"));

module.exports = mod;
}}),

};

//# sourceMappingURL=%5Broot-of-the-server%5D__628e66bb._.js.map
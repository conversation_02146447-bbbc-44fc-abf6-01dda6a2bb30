"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreateCmsPartnersService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const event_emitter_1 = require("@nestjs/event-emitter");
const typeorm_transactional_1 = require("typeorm-transactional");
const cms_partners_entity_1 = require("../entity/cms-partners.entity");
const base_cms_partners_service_1 = require("./base.cms-partners.service");
const dto_1 = require("../dto");
const class_transformer_1 = require("class-transformer");
let CreateCmsPartnersService = class CreateCmsPartnersService extends base_cms_partners_service_1.BaseCmsPartnersService {
    partnerRepository;
    eventEmitter;
    dataSource;
    constructor(partnerRepository, eventEmitter, dataSource) {
        super(partnerRepository, eventEmitter);
        this.partnerRepository = partnerRepository;
        this.eventEmitter = eventEmitter;
        this.dataSource = dataSource;
        this.logger.log('CreateCmsPartnersService đã được khởi tạo');
    }
    async create(createDto, userId) {
        this.logger.log(`Attempting to create CMS Partner`);
        try {
            const normalizedData = this.normalizePartnerData(createDto);
            const validationErrors = await this.validatePartnerData(normalizedData);
            if (validationErrors.length > 0) {
                throw new common_1.BadRequestException(`Dữ liệu không hợp lệ: ${validationErrors.join(', ')}`);
            }
            const partner = this.partnerRepository.create({
                ...normalizedData,
                type: normalizedData.type || cms_partners_entity_1.CmsPartnerType.PARTNER,
                status: normalizedData.status || cms_partners_entity_1.CmsPartnerStatus.ACTIVE,
                displayOrder: normalizedData.displayOrder ?? await this.getNextDisplayOrder(),
                createdBy: userId,
                updatedBy: userId,
            });
            const savedPartner = await this.partnerRepository.save(partner);
            this.emitEvent('cms-partner.created', {
                partnerId: savedPartner.id,
                partnerName: savedPartner.name,
                userId,
                timestamp: new Date(),
            });
            this.logOperation('CREATE', savedPartner.id, userId);
            const result = (0, class_transformer_1.plainToClass)(dto_1.CmsPartnerDto, savedPartner, {
                excludeExtraneousValues: true,
            });
            this.logger.log(`CMS Partner created successfully with ID: ${savedPartner.id}`);
            return result;
        }
        catch (error) {
            this.logger.error(`Failed to create CMS Partner: ${error.message}`, error.stack);
            if (error instanceof common_1.BadRequestException) {
                throw error;
            }
            throw new common_1.InternalServerErrorException('Không thể tạo đối tác. Vui lòng thử lại sau.');
        }
    }
    async bulkCreate(bulkCreateDto, userId) {
        this.logger.log(`Attempting to bulk create ${bulkCreateDto.partners.length} CMS Partners`);
        const startTime = Date.now();
        const result = {
            successCount: 0,
            failureCount: 0,
            totalCount: bulkCreateDto.partners.length,
            successIds: [],
            errors: [],
            processingTime: 0,
        };
        try {
            for (let i = 0; i < bulkCreateDto.partners.length; i++) {
                try {
                    const partnerDto = bulkCreateDto.partners[i];
                    const createdPartner = await this.create(partnerDto, userId);
                    result.successCount++;
                    result.successIds.push(createdPartner.id);
                }
                catch (error) {
                    result.failureCount++;
                    result.errors.push({
                        index: i,
                        error: error.message,
                        data: bulkCreateDto.partners[i],
                    });
                }
            }
            result.processingTime = Date.now() - startTime;
            this.emitEvent('cms-partner.bulk-created', {
                totalCount: result.totalCount,
                successCount: result.successCount,
                failureCount: result.failureCount,
                userId,
                timestamp: new Date(),
            });
            this.logger.log(`Bulk create completed: ${result.successCount}/${result.totalCount} successful`);
            return result;
        }
        catch (error) {
            this.logger.error(`Failed to bulk create CMS Partners: ${error.message}`, error.stack);
            throw new common_1.InternalServerErrorException('Không thể tạo nhiều đối tác. Vui lòng thử lại sau.');
        }
    }
    async createFromTemplate(createFromTemplateDto, userId) {
        this.logger.log(`Attempting to create CMS Partner from template: ${createFromTemplateDto.templateId}`);
        try {
            const templatePartner = await this.findById(createFromTemplateDto.templateId);
            if (!templatePartner) {
                throw new common_1.NotFoundException(`Không tìm thấy template với ID: ${createFromTemplateDto.templateId}`);
            }
            const createDto = {
                name: createFromTemplateDto.name,
                logoUrl: templatePartner.logoUrl || undefined,
                websiteUrl: templatePartner.websiteUrl || undefined,
                description: templatePartner.description || undefined,
                type: templatePartner.type,
                displayOrder: templatePartner.displayOrder,
                status: templatePartner.status,
                ...createFromTemplateDto.overrides,
            };
            return this.create(createDto, userId);
        }
        catch (error) {
            this.logger.error(`Failed to create CMS Partner from template: ${error.message}`, error.stack);
            if (error instanceof common_1.NotFoundException || error instanceof common_1.BadRequestException) {
                throw error;
            }
            throw new common_1.InternalServerErrorException('Không thể tạo đối tác từ template. Vui lòng thử lại sau.');
        }
    }
    async duplicate(id, duplicateDto, createdBy) {
        this.logger.log(`Attempting to duplicate CMS Partner: ${id}`);
        try {
            const originalPartner = await this.findById(id);
            if (!originalPartner) {
                throw new common_1.NotFoundException(`Không tìm thấy đối tác với ID: ${id}`);
            }
            let newName = duplicateDto.name;
            let counter = 1;
            while (await this.isNameExists(newName)) {
                newName = `${duplicateDto.name} (${counter})`;
                counter++;
            }
            const createDto = {
                name: newName,
                logoUrl: originalPartner.logoUrl || undefined,
                websiteUrl: originalPartner.websiteUrl || undefined,
                description: originalPartner.description || undefined,
                type: originalPartner.type,
                displayOrder: originalPartner.displayOrder,
                status: cms_partners_entity_1.CmsPartnerStatus.INACTIVE,
                ...duplicateDto.overrides,
            };
            return this.create(createDto, createdBy);
        }
        catch (error) {
            this.logger.error(`Failed to duplicate CMS Partner: ${error.message}`, error.stack);
            if (error instanceof common_1.NotFoundException || error instanceof common_1.BadRequestException) {
                throw error;
            }
            throw new common_1.InternalServerErrorException('Không thể nhân bản đối tác. Vui lòng thử lại sau.');
        }
    }
    async import(importDto, userId) {
        this.logger.log(`Attempting to import ${importDto.partners.length} CMS Partners`);
        const startTime = Date.now();
        const result = {
            successCount: 0,
            failureCount: 0,
            totalCount: importDto.partners.length,
            successIds: [],
            errors: [],
            processingTime: 0,
        };
        try {
            for (let i = 0; i < importDto.partners.length; i++) {
                try {
                    const partnerDto = importDto.partners[i];
                    if (!importDto.overwriteExisting) {
                        const existingPartner = await this.findByName(partnerDto.name);
                        if (existingPartner) {
                            throw new common_1.BadRequestException(`Đối tác với tên "${partnerDto.name}" đã tồn tại`);
                        }
                    }
                    const createdPartner = await this.create(partnerDto, userId);
                    result.successCount++;
                    result.successIds.push(createdPartner.id);
                }
                catch (error) {
                    result.failureCount++;
                    if (importDto.skipValidationErrors && error instanceof common_1.BadRequestException) {
                        this.logger.warn(`Skipped validation error for partner ${i}: ${error.message}`);
                        continue;
                    }
                    result.errors.push({
                        index: i,
                        error: error.message,
                        data: importDto.partners[i],
                    });
                }
            }
            result.processingTime = Date.now() - startTime;
            this.emitEvent('cms-partner.imported', {
                totalCount: result.totalCount,
                successCount: result.successCount,
                failureCount: result.failureCount,
                overwriteExisting: importDto.overwriteExisting,
                skipValidationErrors: importDto.skipValidationErrors,
                userId,
                timestamp: new Date(),
            });
            this.logger.log(`Import completed: ${result.successCount}/${result.totalCount} successful`);
            return result;
        }
        catch (error) {
            this.logger.error(`Failed to import CMS Partners: ${error.message}`, error.stack);
            throw new common_1.InternalServerErrorException('Không thể import đối tác. Vui lòng thử lại sau.');
        }
    }
};
exports.CreateCmsPartnersService = CreateCmsPartnersService;
__decorate([
    (0, typeorm_transactional_1.Transactional)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [dto_1.CreateCmsPartnerDto, String]),
    __metadata("design:returntype", Promise)
], CreateCmsPartnersService.prototype, "create", null);
__decorate([
    (0, typeorm_transactional_1.Transactional)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [dto_1.BulkCreateCmsPartnersDto, String]),
    __metadata("design:returntype", Promise)
], CreateCmsPartnersService.prototype, "bulkCreate", null);
__decorate([
    (0, typeorm_transactional_1.Transactional)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [dto_1.CreateCmsPartnerFromTemplateDto, String]),
    __metadata("design:returntype", Promise)
], CreateCmsPartnersService.prototype, "createFromTemplate", null);
__decorate([
    (0, typeorm_transactional_1.Transactional)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, dto_1.DuplicateCmsPartnerDto, String]),
    __metadata("design:returntype", Promise)
], CreateCmsPartnersService.prototype, "duplicate", null);
__decorate([
    (0, typeorm_transactional_1.Transactional)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [dto_1.ImportCmsPartnersDto, String]),
    __metadata("design:returntype", Promise)
], CreateCmsPartnersService.prototype, "import", null);
exports.CreateCmsPartnersService = CreateCmsPartnersService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(cms_partners_entity_1.CmsPartners)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        event_emitter_1.EventEmitter2,
        typeorm_2.DataSource])
], CreateCmsPartnersService);
//# sourceMappingURL=create.cms-partners.service.js.map
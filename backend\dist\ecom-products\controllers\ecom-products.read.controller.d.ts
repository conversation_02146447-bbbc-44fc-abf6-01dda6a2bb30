import { ReadEcomProductsService } from '../services/read.ecom-products.service';
import { EcomProductDto } from '../dto/ecom-product.dto';
import { CustomPaginationQueryDto } from '../../common/dto/custom-pagination-query.dto';
import { PaginationResponseDto } from '../../common/dto/pagination-response.dto';
export declare class EcomProductsReadController {
    private readonly ecomProductsService;
    constructor(ecomProductsService: ReadEcomProductsService);
    findAll(paginationQuery: CustomPaginationQueryDto, userId?: string, roles?: string[]): Promise<PaginationResponseDto<EcomProductDto>>;
    getStatistics(): Promise<{
        total: number;
        active: number;
        inactive: number;
    }>;
    findById(id: string): Promise<EcomProductDto>;
    findByCode(code: string): Promise<EcomProductDto>;
    findBySlug(slug: string): Promise<EcomProductDto>;
    findByCategory(categoryId: string, paginationQuery: CustomPaginationQueryDto): Promise<PaginationResponseDto<EcomProductDto>>;
    search(keyword: string, paginationQuery: CustomPaginationQueryDto): Promise<PaginationResponseDto<EcomProductDto>>;
    findDeleted(paginationQuery: CustomPaginationQueryDto): Promise<PaginationResponseDto<EcomProductDto>>;
}

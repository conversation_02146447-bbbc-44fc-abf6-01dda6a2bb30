"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.VerifyUserKycDto = void 0;
const openapi = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
const swagger_1 = require("@nestjs/swagger");
const kyc_status_enum_1 = require("../enums/kyc-status.enum");
class VerifyUserKycDto {
    id;
    status;
    notes;
    verifiedBy;
    static _OPENAPI_METADATA_FACTORY() {
        return { id: { required: true, type: () => String, format: "uuid" }, status: { required: true, enum: require("../enums/kyc-status.enum").KycStatus }, notes: { required: false, type: () => String }, verifiedBy: { required: true, type: () => String, format: "uuid" } };
    }
}
exports.VerifyUserKycDto = VerifyUserKycDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'KYC record ID' }),
    (0, class_validator_1.IsUUID)(),
    __metadata("design:type", String)
], VerifyUserKycDto.prototype, "id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'New status for the KYC record', enum: kyc_status_enum_1.KycStatus }),
    (0, class_validator_1.IsEnum)(kyc_status_enum_1.KycStatus),
    __metadata("design:type", String)
], VerifyUserKycDto.prototype, "status", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Notes about the verification' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], VerifyUserKycDto.prototype, "notes", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'ID of the staff member performing the verification' }),
    (0, class_validator_1.IsUUID)(),
    __metadata("design:type", String)
], VerifyUserKycDto.prototype, "verifiedBy", void 0);
//# sourceMappingURL=verify-user-kyc.dto.js.map
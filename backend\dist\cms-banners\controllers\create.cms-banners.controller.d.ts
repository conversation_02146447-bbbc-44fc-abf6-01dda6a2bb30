import { CreateCmsBannersService } from '../services/create.cms-banners.service';
import { CmsBannerDto } from '../dto/cms-banner.dto';
import { CreateCmsBannerDto } from '../dto/create.cms-banner.dto';
export declare class CreateCmsBannersController {
    private readonly cmsBannersService;
    constructor(cmsBannersService: CreateCmsBannersService);
    create(createCmsBannerDto: CreateCmsBannerDto, userId: string): Promise<CmsBannerDto>;
    bulkCreate(createCmsBannerDtos: CreateCmsBannerDto[], userId: string): Promise<CmsBannerDto[]>;
    duplicate(id: string, newTitle: string, userId: string): Promise<CmsBannerDto>;
    createFromTemplate(templateData: any, userId: string): Promise<CmsBannerDto>;
}

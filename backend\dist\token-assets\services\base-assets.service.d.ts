import { Logger } from '@nestjs/common';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { FindOptionsWhere, Repository } from 'typeorm';
import { EcomProduct } from 'src/ecom-products/entity/ecom-products.entity';
import { User } from '../../users/entities/user.entity';
import { Wallet } from '../../wallets/entities/wallet.entity';
import { AssetDto } from '../dto/asset.dto';
import { Asset } from '../entities/asset.entity';
export declare class BaseAssetsService {
    protected readonly assetRepository: Repository<Asset>;
    protected readonly ecomProductRepository: Repository<EcomProduct>;
    protected readonly userRepository: Repository<User>;
    protected readonly walletRepository: Repository<Wallet>;
    protected readonly eventEmitter: EventEmitter2;
    protected readonly logger: Logger;
    protected readonly validRelations: string[];
    protected readonly EVENT_TOKEN_ASSET_CREATED = "asset.created";
    protected readonly EVENT_TOKEN_ASSET_UPDATED = "asset.updated";
    protected readonly EVENT_TOKEN_ASSET_DELETED = "asset.deleted";
    protected readonly EVENT_TOKEN_ASSET_RESTORED = "asset.restored";
    protected readonly EVENT_TOKEN_ASSET_DUPLICATED = "asset.duplicated";
    protected readonly EVENT_TOKEN_ASSET_CLEANUP = "asset.cleanup";
    constructor(assetRepository: Repository<Asset>, ecomProductRepository: Repository<EcomProduct>, userRepository: Repository<User>, walletRepository: Repository<Wallet>, eventEmitter: EventEmitter2);
    protected toDto(tokenAsset: Asset): AssetDto;
    protected validateRelations(relations: string[]): string[];
    protected findByIdOrFail(id: string, relations?: string[], withDeleted?: boolean): Promise<Asset>;
    protected buildWhereClause(filter?: string): FindOptionsWhere<Asset>;
    protected getQueryRunner(): Promise<import("typeorm").QueryRunner>;
    protected findTokenOrFail(tokenId: string): Promise<EcomProduct>;
    protected findEcomProductOrFail(productId?: string): Promise<EcomProduct>;
    protected findUserOrFail(userId: string): Promise<User>;
}

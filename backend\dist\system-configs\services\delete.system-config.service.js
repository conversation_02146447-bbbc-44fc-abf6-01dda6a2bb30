"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var DeleteSystemConfigService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.DeleteSystemConfigService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const event_emitter_1 = require("@nestjs/event-emitter");
const typeorm_transactional_1 = require("typeorm-transactional");
const system_config_entity_1 = require("../entities/system-config.entity");
const base_system_config_service_1 = require("./base.system-config.service");
let DeleteSystemConfigService = DeleteSystemConfigService_1 = class DeleteSystemConfigService extends base_system_config_service_1.BaseSystemConfigService {
    systemConfigRepository;
    dataSource;
    eventEmitter;
    logger = new common_1.Logger(DeleteSystemConfigService_1.name);
    constructor(systemConfigRepository, dataSource, eventEmitter) {
        super(systemConfigRepository, dataSource, eventEmitter);
        this.systemConfigRepository = systemConfigRepository;
        this.dataSource = dataSource;
        this.eventEmitter = eventEmitter;
    }
    async softDelete(id, deletedBy) {
        try {
            this.logger.debug(`Xóa mềm cấu hình với ID: ${id}`);
            const systemConfig = await this.findByIdOrFail(id);
            systemConfig.isDeleted = true;
            if (deletedBy) {
                systemConfig.deletedBy = deletedBy;
            }
            const deletedConfig = await this.systemConfigRepository.save(systemConfig);
            try {
                this.eventEmitter.emit(this.EVENT_SYSTEM_CONFIG_DELETED, deletedConfig);
            }
            catch (emitError) {
                this.logger.warn(`Không thể phát sự kiện xóa: ${emitError.message}`);
            }
            return this.toDto(deletedConfig);
        }
        catch (error) {
            this.logger.error(`Lỗi khi xóa mềm cấu hình: ${error.message}`, error.stack);
            if (error instanceof common_1.NotFoundException) {
                throw error;
            }
            throw new common_1.InternalServerErrorException(`Không thể xóa mềm cấu hình: ${error.message}`);
        }
    }
    async softDeleteByKey(key, deletedBy) {
        try {
            this.logger.debug(`Xóa mềm cấu hình với khóa: ${key}`);
            const systemConfig = await this.findByKeyOrFail(key);
            systemConfig.isDeleted = true;
            if (deletedBy) {
                systemConfig.deletedBy = deletedBy;
            }
            const deletedConfig = await this.systemConfigRepository.save(systemConfig);
            try {
                this.eventEmitter.emit(this.EVENT_SYSTEM_CONFIG_DELETED, deletedConfig);
            }
            catch (emitError) {
                this.logger.warn(`Không thể phát sự kiện xóa: ${emitError.message}`);
            }
            return this.toDto(deletedConfig);
        }
        catch (error) {
            this.logger.error(`Lỗi khi xóa mềm cấu hình: ${error.message}`, error.stack);
            if (error instanceof common_1.NotFoundException) {
                throw error;
            }
            throw new common_1.InternalServerErrorException(`Không thể xóa mềm cấu hình: ${error.message}`);
        }
    }
    async restore(id) {
        try {
            this.logger.debug(`Khôi phục cấu hình với ID: ${id}`);
            const systemConfig = await this.findByIdOrFail(id, [], true);
            if (!systemConfig.isDeleted) {
                throw new common_1.BadRequestException(`Cấu hình với ID "${id}" chưa bị xóa`);
            }
            systemConfig.isDeleted = false;
            systemConfig.deletedBy = '';
            const restoredConfig = await this.systemConfigRepository.save(systemConfig);
            try {
                this.eventEmitter.emit(this.EVENT_SYSTEM_CONFIG_RESTORED, restoredConfig);
            }
            catch (emitError) {
                this.logger.warn(`Không thể phát sự kiện khôi phục: ${emitError.message}`);
            }
            return this.toDto(restoredConfig);
        }
        catch (error) {
            this.logger.error(`Lỗi khi khôi phục cấu hình: ${error.message}`, error.stack);
            if (error instanceof common_1.NotFoundException || error instanceof common_1.BadRequestException) {
                throw error;
            }
            throw new common_1.InternalServerErrorException(`Không thể khôi phục cấu hình: ${error.message}`);
        }
    }
    async hardDelete(id) {
        try {
            this.logger.debug(`Xóa cứng cấu hình với ID: ${id}`);
            const systemConfig = await this.findByIdOrFail(id, [], true);
            await this.systemConfigRepository.remove(systemConfig);
            return true;
        }
        catch (error) {
            this.logger.error(`Lỗi khi xóa cứng cấu hình: ${error.message}`, error.stack);
            if (error instanceof common_1.NotFoundException) {
                throw error;
            }
            throw new common_1.InternalServerErrorException(`Không thể xóa cứng cấu hình: ${error.message}`);
        }
    }
};
exports.DeleteSystemConfigService = DeleteSystemConfigService;
__decorate([
    (0, typeorm_transactional_1.Transactional)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String]),
    __metadata("design:returntype", Promise)
], DeleteSystemConfigService.prototype, "softDelete", null);
__decorate([
    (0, typeorm_transactional_1.Transactional)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String]),
    __metadata("design:returntype", Promise)
], DeleteSystemConfigService.prototype, "softDeleteByKey", null);
__decorate([
    (0, typeorm_transactional_1.Transactional)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], DeleteSystemConfigService.prototype, "restore", null);
__decorate([
    (0, typeorm_transactional_1.Transactional)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], DeleteSystemConfigService.prototype, "hardDelete", null);
exports.DeleteSystemConfigService = DeleteSystemConfigService = DeleteSystemConfigService_1 = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(system_config_entity_1.SystemConfig)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        typeorm_2.DataSource,
        event_emitter_1.EventEmitter2])
], DeleteSystemConfigService);
//# sourceMappingURL=delete.system-config.service.js.map
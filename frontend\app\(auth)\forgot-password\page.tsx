"use client";
import { PublicRoute } from "@/components/auth/public-route";
import { ForgotPasswordForm } from "@/components/forgot-password-form";
import { useSiteMetadata } from "@/hooks/use-site-metadata";
import { ArrowLeft } from "lucide-react";
import Link from "next/link";


export default function ForgotPasswordPage() {
  const { config } = useSiteMetadata(); 

  const siteName = config?.['site_name'];
  const siteLogo = config?.['site_logo'];
  const siteSplashScreen = config?.['site_splash_screen_image'];

  return (
    <PublicRoute>
      <div className="grid min-h-svh lg:grid-cols-2">
        <div className="flex flex-col gap-4 p-6 md:p-10">
          <div className="flex justify-center gap-2 md:justify-start">
            <a href="#" className="flex items-center gap-2 font-medium">
              <div className="flex items-center justify-center">
                <img src={siteLogo} alt={siteName} className="h-15" />
              </div>
              {siteName}
            </a>
          </div>
          <div className="flex flex-1 items-center justify-center">
            <div className="w-full max-w-md">
              <div className="mb-4">
                <Link
                  href="/login"
                  className="inline-flex items-center text-sm font-medium text-primary hover:underline"
                >
                  <ArrowLeft className="mr-1 h-4 w-4" />
                  Quay lại trang đăng nhập
                </Link>
              </div>
              <ForgotPasswordForm />
            </div>
          </div>
        </div>
        <div className="bg-muted relative hidden lg:block">
          <img
            src={siteSplashScreen}
            alt={siteName}
            className="absolute inset-0 h-full w-full object-cover dark:brightness-[0.2] dark:grayscale"
          />
        </div>
      </div>
    </PublicRoute>
  );
}

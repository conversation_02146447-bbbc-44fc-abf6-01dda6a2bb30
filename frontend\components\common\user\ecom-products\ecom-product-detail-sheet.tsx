'use client';

import { format } from 'date-fns';
import { vi } from 'date-fns/locale';
import {
  Calendar,
  CheckCircle,
  Clock,
  Coins,
  FileText,
  Image,
  Loader2,
  Pencil,
  Tag,
  User
} from 'lucide-react';
import { useEffect, useState } from 'react';

import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';

import { UserHoverCard } from '@/components/common/user/user-hover-card';
import { InfoCard } from '@/components/info-card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { api } from '@/lib/api';
import { toast } from 'sonner';
import { EcomProduct } from './type/ecom-product';

interface EcomProductDetailSheetProps {
  product: EcomProduct | null;
  isOpen: boolean;
  onClose: () => void;
  onEdit: (product: EcomProduct) => void;
}

export function EcomProductDetailSheet({ product: initialProduct, isOpen, onClose, onEdit }: EcomProductDetailSheetProps) {
  const [product, setProduct] = useState<EcomProduct | null>(initialProduct);
  const [loading, setLoading] = useState<boolean>(false);

  // Fetch detailed product information when sheet is opened
  useEffect(() => {
    if (isOpen && initialProduct?.id) {
      setLoading(true);
      api.get<EcomProduct>(`ecom-products/${initialProduct.id}?relations=category,creator,updater,deleter`)
        .then(response => {
          setProduct(response);
          setLoading(false);
        })
        .catch(error => {
          console.error('Error fetching product details:', error);
          toast.error('Không thể tải thông tin chi tiết sản phẩm');
          setLoading(false);
        });
    }
  }, [isOpen, initialProduct?.id]);

  if (!product) return null;

  const formatDate = (dateStr: string | Date | null | undefined) => {
    if (!dateStr) return '---';
    try {
      return format(new Date(dateStr), 'dd/MM/yyyy HH:mm', { locale: vi });
    } catch (error) {
      return 'Không hợp lệ';
    }
  };

  const getStatusBadge = (isActive: boolean | undefined) => {
    if (isActive === undefined) return null;
    return (
      <Badge
        variant={isActive ? 'default' : 'secondary'}
      >
        {isActive ? 'Hoạt động' : 'Vô hiệu hóa'}
      </Badge>
    );
  };

  return (
    <Dialog open={isOpen} onOpenChange={(open) => !open && onClose()}>
      <DialogContent className="!max-w-7xl !w-full">
        {loading ? (
          <div className="flex flex-col items-center justify-center h-full py-8 gap-4">
            <Loader2 className="h-8 w-8 animate-spin text-primary" />
            <span className="text-muted-foreground">Đang tải thông tin chi tiết...</span>
          </div>
        ) : (
          <>
            {/* Header */}
            <DialogHeader className="space-y-1 mb-8">
              <DialogTitle className="text-2xl font-semibold flex items-center gap-2">
                {product.productName}
                {getStatusBadge(product.isActive)}
              </DialogTitle>
              <DialogDescription className="text-base text-muted-foreground">
                Mã sản phẩm: {product.productCode}
              </DialogDescription>
            </DialogHeader>

            {/* Main Content - 2 Column Layout */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-12">
              {/* Left Column - Image */}
              <div>
                <div className="aspect-square w-full rounded-lg border overflow-hidden bg-white flex items-center justify-center">
                  <img
                    src={product.imageUrl || 'https://placehold.co/800x800?text=No+Image'}
                    alt={product.productName}
                    className="w-full h-full object-contain p-4"
                    onError={(e) => {
                      (e.target as HTMLImageElement).src = 'https://placehold.co/800x800?text=No+Image';
                    }}
                  />
                </div>
              </div>

              {/* Right Column - Product Information */}
              <div className="space-y-8">
                <div className="space-y-6">
                  <div>
                    <h3 className="text-xl font-semibold mb-6">Thông tin sản phẩm</h3>
                    <div className="grid gap-6">
                      <div className="grid grid-cols-2 gap-6">
                        <div className="space-y-2">
                          <div className="text-base font-medium text-muted-foreground flex items-center gap-2">
                            <FileText className="h-5 w-5" /> Danh mục
                          </div>
                          <div className="text-lg">{product.category?.name || '---'}</div>
                        </div>

                        <div className="space-y-2">
                          <div className="text-base font-medium text-muted-foreground flex items-center gap-2">
                            <Tag className="h-5 w-5" /> Khối lượng
                          </div>
                          <div className="text-lg">{product.weight ? `${product.weight} oz` : '---'}</div>
                        </div>
                      </div>

                      {product.description && (
                        <div className="space-y-2">
                          <div className="text-base font-medium text-muted-foreground flex items-center gap-2">
                            <FileText className="h-5 w-5" /> Mô tả
                          </div>
                          <div className="text-lg prose max-w-none" dangerouslySetInnerHTML={{ __html: product.description }}></div>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </>
        )}
      </DialogContent>
    </Dialog>
  );
}

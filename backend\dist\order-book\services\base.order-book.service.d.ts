import { Logger } from '@nestjs/common';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { FindOptionsWhere, Repository } from 'typeorm';
import { OrderBookDto } from '../dto/order-book.dto';
import { OrderBook } from '../entities/order-book.entity';
export declare class BaseOrderBookService {
    protected readonly orderBookRepository: Repository<OrderBook>;
    protected readonly eventEmitter: EventEmitter2;
    protected readonly logger: Logger;
    protected readonly validRelations: string[];
    protected readonly EVENT_ORDER_CREATED = "order-book.created";
    protected readonly EVENT_ORDER_UPDATED = "order-book.updated";
    protected readonly EVENT_ORDER_DELETED = "order-book.deleted";
    protected readonly EVENT_ORDER_RESTORED = "order-book.restored";
    protected readonly EVENT_ORDER_STATUS_TOGGLED = "order-book.statusToggled";
    protected readonly EVENT_ORDER_DUPLICATED = "order-book.duplicated";
    protected readonly EVENT_ORDER_CLEANUP = "order-book.cleanup";
    constructor(orderBookRepository: Repository<OrderBook>, eventEmitter: EventEmitter2);
    protected toDto(orderBook: OrderBook, relations?: string[]): OrderBookDto;
    protected validateRelations(relations: string[]): string[];
    protected findByIdOrFail(id: string, relations?: string[], withDeleted?: boolean): Promise<OrderBook>;
    protected buildWhereClause(filter?: string): FindOptionsWhere<OrderBook>;
}

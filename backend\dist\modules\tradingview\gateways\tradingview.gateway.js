"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var TradingViewGateway_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.TradingViewGateway = void 0;
const websockets_1 = require("@nestjs/websockets");
const common_1 = require("@nestjs/common");
const socket_io_1 = require("socket.io");
const ws_jwt_auth_guard_1 = require("../../../common/guards/ws-jwt-auth.guard");
const tradingview_websocket_service_1 = require("../services/tradingview-websocket.service");
const rate_limiter_service_1 = require("../services/rate-limiter.service");
let TradingViewGateway = TradingViewGateway_1 = class TradingViewGateway {
    tradingViewWebsocketService;
    rateLimiterService;
    logger = new common_1.Logger(TradingViewGateway_1.name);
    constructor(tradingViewWebsocketService, rateLimiterService) {
        this.tradingViewWebsocketService = tradingViewWebsocketService;
        this.rateLimiterService = rateLimiterService;
    }
    handleConnection(client) {
        this.logger.log(`Client kết nối: ${client.id}`);
        this.logger.log(`Handshake: ${JSON.stringify({
            query: client.handshake.query,
            headers: client.handshake.headers,
            auth: client.handshake.auth
        })}`);
    }
    handleDisconnect(client) {
        this.logger.log(`Client ngắt kết nối: ${client.id}`);
    }
    async handleSubscribe(client, data) {
        this.logger.log(`Nhận yêu cầu đăng ký từ client ${client.id}: ${JSON.stringify(data)}`);
        try {
            const rateLimited = await this.rateLimiterService.isRateLimited(client.id);
            if (rateLimited) {
                throw new websockets_1.WsException('Đã vượt quá giới hạn tần suất. Vui lòng thử lại sau.');
            }
            const user = client.data?.user;
            this.logger.log(`Thông tin người dùng: ${JSON.stringify(user || 'Không có thông tin người dùng')}`);
            const result = await this.tradingViewWebsocketService.handleSubscription(client, data);
            this.logger.log(`Kết quả đăng ký: ${JSON.stringify(result)}`);
            return result;
        }
        catch (error) {
            this.logger.error(`Lỗi khi xử lý đăng ký: ${error.message}`, error.stack);
            this.tradingViewWebsocketService.sendErrorToClient(client, error.message);
            return {
                success: false,
                message: error.message || 'Đã xảy ra lỗi khi xử lý yêu cầu đăng ký của bạn'
            };
        }
    }
    handleUnsubscribe(client, data) {
        try {
            return this.tradingViewWebsocketService.handleUnsubscription(client, data.symbol);
        }
        catch (error) {
            this.logger.error(`Lỗi khi xử lý hủy đăng ký: ${error.message}`, error.stack);
            this.tradingViewWebsocketService.sendErrorToClient(client, error.message);
            return {
                success: false,
                message: error.message || 'Đã xảy ra lỗi khi xử lý yêu cầu hủy đăng ký của bạn'
            };
        }
    }
};
exports.TradingViewGateway = TradingViewGateway;
__decorate([
    (0, common_1.UseGuards)(ws_jwt_auth_guard_1.WsJwtAuthGuard),
    (0, websockets_1.SubscribeMessage)('subscribe'),
    __param(0, (0, websockets_1.ConnectedSocket)()),
    __param(1, (0, websockets_1.MessageBody)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [socket_io_1.Socket, Object]),
    __metadata("design:returntype", Promise)
], TradingViewGateway.prototype, "handleSubscribe", null);
__decorate([
    (0, common_1.UseGuards)(ws_jwt_auth_guard_1.WsJwtAuthGuard),
    (0, websockets_1.SubscribeMessage)('unsubscribe'),
    __param(0, (0, websockets_1.ConnectedSocket)()),
    __param(1, (0, websockets_1.MessageBody)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [socket_io_1.Socket, Object]),
    __metadata("design:returntype", Object)
], TradingViewGateway.prototype, "handleUnsubscribe", null);
exports.TradingViewGateway = TradingViewGateway = TradingViewGateway_1 = __decorate([
    (0, websockets_1.WebSocketGateway)({
        namespace: 'tradingview',
        cors: {
            origin: '*',
            credentials: true
        }
    }),
    __metadata("design:paramtypes", [tradingview_websocket_service_1.TradingViewWebsocketService,
        rate_limiter_service_1.RateLimiterService])
], TradingViewGateway);
//# sourceMappingURL=tradingview.gateway.js.map
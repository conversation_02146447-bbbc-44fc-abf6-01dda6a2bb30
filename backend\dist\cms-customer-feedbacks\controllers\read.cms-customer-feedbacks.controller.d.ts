import { ReadCmsCustomerFeedbacksService } from '../services/read.cms-customer-feedbacks.service';
import { CmsCustomerFeedbackDto } from '../dto/cms-customer-feedback.dto';
import { CmsCustomerFeedbackStatus } from '../entity/cms-customer-feedbacks.entity';
import { CustomPaginationQueryDto } from '../../common/dto/custom-pagination-query.dto';
import { PaginationResponseDto } from '../../common/dto/pagination-response.dto';
export declare class ReadCmsCustomerFeedbacksController {
    private readonly cmsCustomerFeedbacksService;
    constructor(cmsCustomerFeedbacksService: ReadCmsCustomerFeedbacksService);
    findAll(paginationQuery: CustomPaginationQueryDto): Promise<PaginationResponseDto<CmsCustomerFeedbackDto>>;
    findByStatus(status: CmsCustomerFeedbackStatus, paginationQuery: CustomPaginationQueryDto): Promise<PaginationResponseDto<CmsCustomerFeedbackDto>>;
    findByRating(rating: number, paginationQuery: CustomPaginationQueryDto): Promise<PaginationResponseDto<CmsCustomerFeedbackDto>>;
    getApprovedFeedbacks(paginationQuery: CustomPaginationQueryDto): Promise<PaginationResponseDto<CmsCustomerFeedbackDto>>;
    getPendingFeedbacks(paginationQuery: CustomPaginationQueryDto): Promise<PaginationResponseDto<CmsCustomerFeedbackDto>>;
    findByProductService(productServiceName: string, paginationQuery: CustomPaginationQueryDto): Promise<PaginationResponseDto<CmsCustomerFeedbackDto>>;
    getStatistics(): Promise<{
        total: number;
        byStatus: Record<string, number>;
        byRating: Record<string, number>;
        averageRating: number;
        totalWithRating: number;
    }>;
    getHighRatingFeedbacks(limit?: number): Promise<CmsCustomerFeedbackDto[]>;
    count(filter?: string): Promise<number>;
    search(keyword: string, paginationQuery: CustomPaginationQueryDto): Promise<PaginationResponseDto<CmsCustomerFeedbackDto>>;
    findDeleted(paginationQuery: CustomPaginationQueryDto): Promise<PaginationResponseDto<CmsCustomerFeedbackDto>>;
    findOne(id: string, relations?: string): Promise<CmsCustomerFeedbackDto | null>;
    findByBusinessCode(businessCode: string): Promise<CmsCustomerFeedbackDto | null>;
}

import { UpdateCmsCategoriesService } from '../services/update.cms-categories.service';
import { ReadCmsCategoriesService } from '../services/read.cms-categories.service';
import { CmsCategoryDto } from '../dto/cms-category.dto';
import { UpdateCmsCategoryDto } from '../dto/update.cms-category.dto';
export declare class UpdateCmsCategoriesController {
    private readonly updateCmsCategoriesService;
    private readonly readCmsCategoriesService;
    constructor(updateCmsCategoriesService: UpdateCmsCategoriesService, readCmsCategoriesService: ReadCmsCategoriesService);
    update(id: string, updateCmsCategoryDto: UpdateCmsCategoryDto, userId: string): Promise<CmsCategoryDto | null>;
    bulkUpdate(updates: Array<{
        id: string;
        data: UpdateCmsCategoryDto;
    }>, userId: string): Promise<CmsCategoryDto[]>;
    updateStatus(id: string, status: string, userId: string): Promise<CmsCategoryDto | null>;
}

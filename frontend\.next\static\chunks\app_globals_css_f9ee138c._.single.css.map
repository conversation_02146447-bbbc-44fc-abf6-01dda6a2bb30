{"version": 3, "sources": [], "sections": [{"offset": {"line": 1, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/app/globals.css"], "sourcesContent": ["/*! tailwindcss v4.1.3 | MIT License | https://tailwindcss.com */\n@layer properties;\n@layer theme, base, components, utilities;\n@layer theme {\n  :root, :host {\n    --color-red-50: oklch(97.1% 0.013 17.38);\n    --color-red-100: oklch(93.6% 0.032 17.717);\n    --color-red-200: oklch(88.5% 0.062 18.334);\n    --color-red-500: oklch(63.7% 0.237 25.331);\n    --color-red-600: oklch(57.7% 0.245 27.325);\n    --color-red-700: oklch(50.5% 0.213 27.518);\n    --color-red-800: oklch(44.4% 0.177 26.899);\n    --color-orange-100: oklch(95.4% 0.038 75.164);\n    --color-orange-500: oklch(70.5% 0.213 47.604);\n    --color-orange-600: oklch(64.6% 0.222 41.116);\n    --color-orange-800: oklch(47% 0.157 37.304);\n    --color-amber-100: oklch(96.2% 0.059 95.617);\n    --color-amber-400: oklch(82.8% 0.189 84.429);\n    --color-amber-500: oklch(76.9% 0.188 70.08);\n    --color-amber-600: oklch(66.6% 0.179 58.318);\n    --color-amber-800: oklch(47.3% 0.137 46.201);\n    --color-yellow-50: oklch(98.7% 0.026 102.212);\n    --color-yellow-100: oklch(97.3% 0.071 103.193);\n    --color-yellow-200: oklch(94.5% 0.129 101.54);\n    --color-yellow-400: oklch(85.2% 0.199 91.936);\n    --color-yellow-500: oklch(79.5% 0.184 86.047);\n    --color-yellow-600: oklch(68.1% 0.162 75.834);\n    --color-yellow-700: oklch(55.4% 0.135 66.442);\n    --color-yellow-800: oklch(47.6% 0.114 61.907);\n    --color-green-50: oklch(98.2% 0.018 155.826);\n    --color-green-100: oklch(96.2% 0.044 156.743);\n    --color-green-200: oklch(92.5% 0.084 155.995);\n    --color-green-400: oklch(79.2% 0.209 151.711);\n    --color-green-500: oklch(72.3% 0.219 149.579);\n    --color-green-600: oklch(62.7% 0.194 149.214);\n    --color-green-700: oklch(52.7% 0.154 150.069);\n    --color-green-800: oklch(44.8% 0.119 151.328);\n    --color-emerald-50: oklch(97.9% 0.021 166.113);\n    --color-teal-100: oklch(95.3% 0.051 180.801);\n    --color-teal-800: oklch(43.7% 0.078 188.216);\n    --color-cyan-100: oklch(95.6% 0.045 203.388);\n    --color-cyan-800: oklch(45% 0.085 224.283);\n    --color-blue-50: oklch(97% 0.014 254.604);\n    --color-blue-100: oklch(93.2% 0.032 255.585);\n    --color-blue-200: oklch(88.2% 0.059 254.128);\n    --color-blue-500: oklch(62.3% 0.214 259.815);\n    --color-blue-600: oklch(54.6% 0.245 262.881);\n    --color-blue-700: oklch(48.8% 0.243 264.376);\n    --color-blue-800: oklch(42.4% 0.199 265.638);\n    --color-indigo-50: oklch(96.2% 0.018 272.314);\n    --color-indigo-100: oklch(93% 0.034 272.788);\n    --color-indigo-500: oklch(58.5% 0.233 277.117);\n    --color-indigo-600: oklch(51.1% 0.262 276.966);\n    --color-indigo-800: oklch(39.8% 0.195 277.366);\n    --color-purple-50: oklch(97.7% 0.014 308.299);\n    --color-purple-100: oklch(94.6% 0.033 307.174);\n    --color-purple-200: oklch(90.2% 0.063 306.703);\n    --color-purple-500: oklch(62.7% 0.265 303.9);\n    --color-purple-600: oklch(55.8% 0.288 302.321);\n    --color-purple-800: oklch(43.8% 0.218 303.724);\n    --color-pink-100: oklch(94.8% 0.028 342.258);\n    --color-pink-500: oklch(65.6% 0.241 354.308);\n    --color-pink-800: oklch(45.9% 0.187 3.815);\n    --color-rose-50: oklch(96.9% 0.015 12.422);\n    --color-rose-100: oklch(94.1% 0.03 12.58);\n    --color-rose-800: oklch(45.5% 0.188 13.697);\n    --color-slate-50: oklch(98.4% 0.003 247.858);\n    --color-slate-100: oklch(96.8% 0.007 247.896);\n    --color-slate-800: oklch(27.9% 0.041 260.031);\n    --color-slate-900: oklch(20.8% 0.042 265.755);\n    --color-gray-50: oklch(98.5% 0.002 247.839);\n    --color-gray-100: oklch(96.7% 0.003 264.542);\n    --color-gray-200: oklch(92.8% 0.006 264.531);\n    --color-gray-300: oklch(87.2% 0.01 258.338);\n    --color-gray-400: oklch(70.7% 0.022 261.325);\n    --color-gray-500: oklch(55.1% 0.027 264.364);\n    --color-gray-600: oklch(44.6% 0.03 256.802);\n    --color-gray-700: oklch(37.3% 0.034 259.733);\n    --color-gray-800: oklch(27.8% 0.033 256.848);\n    --color-gray-900: oklch(21% 0.034 264.665);\n    --color-black: #000;\n    --color-white: #fff;\n    --spacing: 0.25rem;\n    --container-xs: 20rem;\n    --container-sm: 24rem;\n    --container-md: 28rem;\n    --container-lg: 32rem;\n    --container-xl: 36rem;\n    --container-2xl: 42rem;\n    --container-3xl: 48rem;\n    --container-4xl: 56rem;\n    --container-5xl: 64rem;\n    --container-6xl: 72rem;\n    --container-7xl: 80rem;\n    --text-xs: 0.75rem;\n    --text-xs--line-height: calc(1 / 0.75);\n    --text-sm: 0.875rem;\n    --text-sm--line-height: calc(1.25 / 0.875);\n    --text-base: 1rem;\n    --text-base--line-height: calc(1.5 / 1);\n    --text-lg: 1.125rem;\n    --text-lg--line-height: calc(1.75 / 1.125);\n    --text-xl: 1.25rem;\n    --text-xl--line-height: calc(1.75 / 1.25);\n    --text-2xl: 1.5rem;\n    --text-2xl--line-height: calc(2 / 1.5);\n    --text-3xl: 1.875rem;\n    --text-3xl--line-height: calc(2.25 / 1.875);\n    --text-6xl: 3.75rem;\n    --text-6xl--line-height: 1;\n    --font-weight-extralight: 200;\n    --font-weight-normal: 400;\n    --font-weight-medium: 500;\n    --font-weight-semibold: 600;\n    --font-weight-bold: 700;\n    --tracking-tight: -0.025em;\n    --tracking-wider: 0.05em;\n    --tracking-widest: 0.1em;\n    --leading-tight: 1.25;\n    --leading-relaxed: 1.625;\n    --radius-xs: 0.125rem;\n    --drop-shadow-md: 0 3px 3px rgb(0 0 0 / 0.12);\n    --ease-in: cubic-bezier(0.4, 0, 1, 1);\n    --ease-out: cubic-bezier(0, 0, 0.2, 1);\n    --ease-in-out: cubic-bezier(0.4, 0, 0.2, 1);\n    --animate-spin: spin 1s linear infinite;\n    --animate-pulse: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;\n    --blur-sm: 8px;\n    --blur-md: 12px;\n    --blur-lg: 16px;\n    --blur-xl: 24px;\n    --aspect-video: 16 / 9;\n    --default-transition-duration: 150ms;\n    --default-transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\n    --default-font-family: var(--font-geist-sans);\n    --default-mono-font-family: var(--font-geist-mono);\n    --color-border: var(--border);\n  }\n}\n@layer base {\n  *, ::after, ::before, ::backdrop, ::file-selector-button {\n    box-sizing: border-box;\n    margin: 0;\n    padding: 0;\n    border: 0 solid;\n  }\n  html, :host {\n    line-height: 1.5;\n    -webkit-text-size-adjust: 100%;\n    tab-size: 4;\n    font-family: var(--default-font-family, ui-sans-serif, system-ui, sans-serif, \"Apple Color Emoji\", \"Segoe UI Emoji\", \"Segoe UI Symbol\", \"Noto Color Emoji\");\n    font-feature-settings: var(--default-font-feature-settings, normal);\n    font-variation-settings: var(--default-font-variation-settings, normal);\n    -webkit-tap-highlight-color: transparent;\n  }\n  hr {\n    height: 0;\n    color: inherit;\n    border-top-width: 1px;\n  }\n  abbr:where([title]) {\n    -webkit-text-decoration: underline dotted;\n    text-decoration: underline dotted;\n  }\n  h1, h2, h3, h4, h5, h6 {\n    font-size: inherit;\n    font-weight: inherit;\n  }\n  a {\n    color: inherit;\n    -webkit-text-decoration: inherit;\n    text-decoration: inherit;\n  }\n  b, strong {\n    font-weight: bolder;\n  }\n  code, kbd, samp, pre {\n    font-family: var(--default-mono-font-family, ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, \"Liberation Mono\", \"Courier New\", monospace);\n    font-feature-settings: var(--default-mono-font-feature-settings, normal);\n    font-variation-settings: var(--default-mono-font-variation-settings, normal);\n    font-size: 1em;\n  }\n  small {\n    font-size: 80%;\n  }\n  sub, sup {\n    font-size: 75%;\n    line-height: 0;\n    position: relative;\n    vertical-align: baseline;\n  }\n  sub {\n    bottom: -0.25em;\n  }\n  sup {\n    top: -0.5em;\n  }\n  table {\n    text-indent: 0;\n    border-color: inherit;\n    border-collapse: collapse;\n  }\n  :-moz-focusring {\n    outline: auto;\n  }\n  progress {\n    vertical-align: baseline;\n  }\n  summary {\n    display: list-item;\n  }\n  ol, ul, menu {\n    list-style: none;\n  }\n  img, svg, video, canvas, audio, iframe, embed, object {\n    display: block;\n    vertical-align: middle;\n  }\n  img, video {\n    max-width: 100%;\n    height: auto;\n  }\n  button, input, select, optgroup, textarea, ::file-selector-button {\n    font: inherit;\n    font-feature-settings: inherit;\n    font-variation-settings: inherit;\n    letter-spacing: inherit;\n    color: inherit;\n    border-radius: 0;\n    background-color: transparent;\n    opacity: 1;\n  }\n  :where(select:is([multiple], [size])) optgroup {\n    font-weight: bolder;\n  }\n  :where(select:is([multiple], [size])) optgroup option {\n    padding-inline-start: 20px;\n  }\n  ::file-selector-button {\n    margin-inline-end: 4px;\n  }\n  ::placeholder {\n    opacity: 1;\n  }\n  @supports (not (-webkit-appearance: -apple-pay-button))  or (contain-intrinsic-size: 1px) {\n    ::placeholder {\n      color: currentcolor;\n      @supports (color: color-mix(in lab, red, red)) {\n        color: color-mix(in oklab, currentcolor 50%, transparent);\n      }\n    }\n  }\n  textarea {\n    resize: vertical;\n  }\n  ::-webkit-search-decoration {\n    -webkit-appearance: none;\n  }\n  ::-webkit-date-and-time-value {\n    min-height: 1lh;\n    text-align: inherit;\n  }\n  ::-webkit-datetime-edit {\n    display: inline-flex;\n  }\n  ::-webkit-datetime-edit-fields-wrapper {\n    padding: 0;\n  }\n  ::-webkit-datetime-edit, ::-webkit-datetime-edit-year-field, ::-webkit-datetime-edit-month-field, ::-webkit-datetime-edit-day-field, ::-webkit-datetime-edit-hour-field, ::-webkit-datetime-edit-minute-field, ::-webkit-datetime-edit-second-field, ::-webkit-datetime-edit-millisecond-field, ::-webkit-datetime-edit-meridiem-field {\n    padding-block: 0;\n  }\n  :-moz-ui-invalid {\n    box-shadow: none;\n  }\n  button, input:where([type=\"button\"], [type=\"reset\"], [type=\"submit\"]), ::file-selector-button {\n    appearance: button;\n  }\n  ::-webkit-inner-spin-button, ::-webkit-outer-spin-button {\n    height: auto;\n  }\n  [hidden]:where(:not([hidden=\"until-found\"])) {\n    display: none !important;\n  }\n}\n@layer utilities {\n  .\\@container\\/card {\n    container-type: inline-size;\n    container-name: card;\n  }\n  .\\@container\\/card-header {\n    container-type: inline-size;\n    container-name: card-header;\n  }\n  .\\@container\\/main {\n    container-type: inline-size;\n    container-name: main;\n  }\n  .pointer-events-auto {\n    pointer-events: auto;\n  }\n  .pointer-events-none {\n    pointer-events: none;\n  }\n  .invisible {\n    visibility: hidden;\n  }\n  .visible {\n    visibility: visible;\n  }\n  .sr-only {\n    position: absolute;\n    width: 1px;\n    height: 1px;\n    padding: 0;\n    margin: -1px;\n    overflow: hidden;\n    clip: rect(0, 0, 0, 0);\n    white-space: nowrap;\n    border-width: 0;\n  }\n  .absolute {\n    position: absolute;\n  }\n  .fixed {\n    position: fixed;\n  }\n  .relative {\n    position: relative;\n  }\n  .static {\n    position: static;\n  }\n  .sticky {\n    position: sticky;\n  }\n  .inset-0 {\n    inset: calc(var(--spacing) * 0);\n  }\n  .inset-x-0 {\n    inset-inline: calc(var(--spacing) * 0);\n  }\n  .inset-y-0 {\n    inset-block: calc(var(--spacing) * 0);\n  }\n  .start-0 {\n    inset-inline-start: calc(var(--spacing) * 0);\n  }\n  .start-1\\.5 {\n    inset-inline-start: calc(var(--spacing) * 1.5);\n  }\n  .start-2 {\n    inset-inline-start: calc(var(--spacing) * 2);\n  }\n  .start-3 {\n    inset-inline-start: calc(var(--spacing) * 3);\n  }\n  .start-auto {\n    inset-inline-start: auto;\n  }\n  .-end-0\\.5 {\n    inset-inline-end: calc(var(--spacing) * -0.5);\n  }\n  .end-1 {\n    inset-inline-end: calc(var(--spacing) * 1);\n  }\n  .end-1\\.5 {\n    inset-inline-end: calc(var(--spacing) * 1.5);\n  }\n  .end-3 {\n    inset-inline-end: calc(var(--spacing) * 3);\n  }\n  .end-auto {\n    inset-inline-end: auto;\n  }\n  .-top-1 {\n    top: calc(var(--spacing) * -1);\n  }\n  .-top-2 {\n    top: calc(var(--spacing) * -2);\n  }\n  .-top-12 {\n    top: calc(var(--spacing) * -12);\n  }\n  .-top-px {\n    top: -1px;\n  }\n  .top-0 {\n    top: calc(var(--spacing) * 0);\n  }\n  .top-1 {\n    top: calc(var(--spacing) * 1);\n  }\n  .top-1\\.5 {\n    top: calc(var(--spacing) * 1.5);\n  }\n  .top-1\\/2 {\n    top: calc(1/2 * 100%);\n  }\n  .top-2 {\n    top: calc(var(--spacing) * 2);\n  }\n  .top-2\\.5 {\n    top: calc(var(--spacing) * 2.5);\n  }\n  .top-3\\.5 {\n    top: calc(var(--spacing) * 3.5);\n  }\n  .top-4 {\n    top: calc(var(--spacing) * 4);\n  }\n  .top-5 {\n    top: calc(var(--spacing) * 5);\n  }\n  .top-\\[1px\\] {\n    top: 1px;\n  }\n  .top-\\[5\\%\\] {\n    top: 5%;\n  }\n  .top-\\[10\\%\\] {\n    top: 10%;\n  }\n  .top-\\[50\\%\\] {\n    top: 50%;\n  }\n  .top-\\[60\\%\\] {\n    top: 60%;\n  }\n  .top-\\[calc\\(46\\%\\)\\] {\n    top: calc(46%);\n  }\n  .top-\\[calc\\(100\\%_\\+_2px\\)\\] {\n    top: calc(100% + 2px);\n  }\n  .top-\\[var\\(--navbar-height\\)\\] {\n    top: var(--navbar-height);\n  }\n  .top-full {\n    top: 100%;\n  }\n  .-right-1 {\n    right: calc(var(--spacing) * -1);\n  }\n  .-right-2 {\n    right: calc(var(--spacing) * -2);\n  }\n  .-right-12 {\n    right: calc(var(--spacing) * -12);\n  }\n  .-right-px {\n    right: -1px;\n  }\n  .right-0 {\n    right: calc(var(--spacing) * 0);\n  }\n  .right-1 {\n    right: calc(var(--spacing) * 1);\n  }\n  .right-2 {\n    right: calc(var(--spacing) * 2);\n  }\n  .right-3 {\n    right: calc(var(--spacing) * 3);\n  }\n  .right-4 {\n    right: calc(var(--spacing) * 4);\n  }\n  .right-6 {\n    right: calc(var(--spacing) * 6);\n  }\n  .right-\\[5\\%\\] {\n    right: 5%;\n  }\n  .right-\\[10\\%\\] {\n    right: 10%;\n  }\n  .right-\\[calc\\(-50\\%\\+20px\\)\\] {\n    right: calc(-50% + 20px);\n  }\n  .-bottom-0\\.5 {\n    bottom: calc(var(--spacing) * -0.5);\n  }\n  .-bottom-12 {\n    bottom: calc(var(--spacing) * -12);\n  }\n  .bottom-0 {\n    bottom: calc(var(--spacing) * 0);\n  }\n  .bottom-4 {\n    bottom: calc(var(--spacing) * 4);\n  }\n  .bottom-20 {\n    bottom: calc(var(--spacing) * 20);\n  }\n  .bottom-\\[5\\%\\] {\n    bottom: 5%;\n  }\n  .bottom-\\[10\\%\\] {\n    bottom: 10%;\n  }\n  .-left-12 {\n    left: calc(var(--spacing) * -12);\n  }\n  .left-0 {\n    left: calc(var(--spacing) * 0);\n  }\n  .left-1 {\n    left: calc(var(--spacing) * 1);\n  }\n  .left-1\\/2 {\n    left: calc(1/2 * 100%);\n  }\n  .left-2 {\n    left: calc(var(--spacing) * 2);\n  }\n  .left-2\\.5 {\n    left: calc(var(--spacing) * 2.5);\n  }\n  .left-\\[5\\%\\] {\n    left: 5%;\n  }\n  .left-\\[10\\%\\] {\n    left: 10%;\n  }\n  .left-\\[50\\%\\] {\n    left: 50%;\n  }\n  .left-\\[calc\\(37\\.5\\%\\)\\] {\n    left: calc(37.5%);\n  }\n  .left-\\[calc\\(50\\%\\+30px\\)\\] {\n    left: calc(50% + 30px);\n  }\n  .isolate {\n    isolation: isolate;\n  }\n  .-z-30 {\n    z-index: calc(30 * -1);\n  }\n  .z-0 {\n    z-index: 0;\n  }\n  .z-10 {\n    z-index: 10;\n  }\n  .z-20 {\n    z-index: 20;\n  }\n  .z-30 {\n    z-index: 30;\n  }\n  .z-40 {\n    z-index: 40;\n  }\n  .z-50 {\n    z-index: 50;\n  }\n  .z-\\[1\\] {\n    z-index: 1;\n  }\n  .z-\\[100\\] {\n    z-index: 100;\n  }\n  .order-1 {\n    order: 1;\n  }\n  .order-2 {\n    order: 2;\n  }\n  .order-3 {\n    order: 3;\n  }\n  .col-span-1 {\n    grid-column: span 1 / span 1;\n  }\n  .col-span-2 {\n    grid-column: span 2 / span 2;\n  }\n  .col-span-3 {\n    grid-column: span 3 / span 3;\n  }\n  .col-span-full {\n    grid-column: 1 / -1;\n  }\n  .col-start-2 {\n    grid-column-start: 2;\n  }\n  .row-span-2 {\n    grid-row: span 2 / span 2;\n  }\n  .row-start-1 {\n    grid-row-start: 1;\n  }\n  .container {\n    width: 100%;\n    @media (width >= 40rem) {\n      max-width: 40rem;\n    }\n    @media (width >= 48rem) {\n      max-width: 48rem;\n    }\n    @media (width >= 64rem) {\n      max-width: 64rem;\n    }\n    @media (width >= 80rem) {\n      max-width: 80rem;\n    }\n    @media (width >= 96rem) {\n      max-width: 96rem;\n    }\n  }\n  .-m-2 {\n    margin: calc(var(--spacing) * -2);\n  }\n  .-m-2\\.5 {\n    margin: calc(var(--spacing) * -2.5);\n  }\n  .m-0 {\n    margin: calc(var(--spacing) * 0);\n  }\n  .-mx-1 {\n    margin-inline: calc(var(--spacing) * -1);\n  }\n  .-mx-2 {\n    margin-inline: calc(var(--spacing) * -2);\n  }\n  .mx-0 {\n    margin-inline: calc(var(--spacing) * 0);\n  }\n  .mx-1 {\n    margin-inline: calc(var(--spacing) * 1);\n  }\n  .mx-2 {\n    margin-inline: calc(var(--spacing) * 2);\n  }\n  .mx-3\\.5 {\n    margin-inline: calc(var(--spacing) * 3.5);\n  }\n  .mx-\\[calc\\(\\(theme\\(spacing\\.5\\)-theme\\(spacing\\.1\\)\\)\\/2\\)\\] {\n    margin-inline: calc((1.25rem - 0.25rem) / 2);\n  }\n  .mx-\\[calc\\(\\(theme\\(spacing\\.6\\)-theme\\(spacing\\.3\\)\\)\\/2\\)\\] {\n    margin-inline: calc((1.5rem - 0.75rem) / 2);\n  }\n  .mx-\\[calc\\(\\(theme\\(spacing\\.7\\)-theme\\(spacing\\.5\\)\\)\\/2\\)\\] {\n    margin-inline: calc((1.75rem - 1.25rem) / 2);\n  }\n  .mx-auto {\n    margin-inline: auto;\n  }\n  .my-0 {\n    margin-block: calc(var(--spacing) * 0);\n  }\n  .my-0\\.5 {\n    margin-block: calc(var(--spacing) * 0.5);\n  }\n  .my-1 {\n    margin-block: calc(var(--spacing) * 1);\n  }\n  .my-3 {\n    margin-block: calc(var(--spacing) * 3);\n  }\n  .my-4 {\n    margin-block: calc(var(--spacing) * 4);\n  }\n  .my-16 {\n    margin-block: calc(var(--spacing) * 16);\n  }\n  .my-\\[calc\\(\\(theme\\(spacing\\.5\\)-theme\\(spacing\\.1\\)\\)\\/2\\)\\] {\n    margin-block: calc((1.25rem - 0.25rem) / 2);\n  }\n  .my-\\[calc\\(\\(theme\\(spacing\\.6\\)-theme\\(spacing\\.3\\)\\)\\/2\\)\\] {\n    margin-block: calc((1.5rem - 0.75rem) / 2);\n  }\n  .my-\\[calc\\(\\(theme\\(spacing\\.7\\)-theme\\(spacing\\.5\\)\\)\\/2\\)\\] {\n    margin-block: calc((1.75rem - 1.25rem) / 2);\n  }\n  .my-auto {\n    margin-block: auto;\n  }\n  .-ms-2 {\n    margin-inline-start: calc(var(--spacing) * -2);\n  }\n  .ms-2 {\n    margin-inline-start: calc(var(--spacing) * 2);\n  }\n  .me-2 {\n    margin-inline-end: calc(var(--spacing) * 2);\n  }\n  .me-4 {\n    margin-inline-end: calc(var(--spacing) * 4);\n  }\n  .-mt-1 {\n    margin-top: calc(var(--spacing) * -1);\n  }\n  .-mt-4 {\n    margin-top: calc(var(--spacing) * -4);\n  }\n  .mt-0 {\n    margin-top: calc(var(--spacing) * 0);\n  }\n  .mt-0\\.5 {\n    margin-top: calc(var(--spacing) * 0.5);\n  }\n  .mt-1 {\n    margin-top: calc(var(--spacing) * 1);\n  }\n  .mt-1\\.5 {\n    margin-top: calc(var(--spacing) * 1.5);\n  }\n  .mt-2 {\n    margin-top: calc(var(--spacing) * 2);\n  }\n  .mt-4 {\n    margin-top: calc(var(--spacing) * 4);\n  }\n  .mt-6 {\n    margin-top: calc(var(--spacing) * 6);\n  }\n  .mt-8 {\n    margin-top: calc(var(--spacing) * 8);\n  }\n  .mt-\\[1px\\] {\n    margin-top: 1px;\n  }\n  .mt-auto {\n    margin-top: auto;\n  }\n  .-mr-2 {\n    margin-right: calc(var(--spacing) * -2);\n  }\n  .mr-1 {\n    margin-right: calc(var(--spacing) * 1);\n  }\n  .mr-2 {\n    margin-right: calc(var(--spacing) * 2);\n  }\n  .mr-3 {\n    margin-right: calc(var(--spacing) * 3);\n  }\n  .mb-0\\.5 {\n    margin-bottom: calc(var(--spacing) * 0.5);\n  }\n  .mb-1 {\n    margin-bottom: calc(var(--spacing) * 1);\n  }\n  .mb-1\\.5 {\n    margin-bottom: calc(var(--spacing) * 1.5);\n  }\n  .mb-2 {\n    margin-bottom: calc(var(--spacing) * 2);\n  }\n  .mb-3 {\n    margin-bottom: calc(var(--spacing) * 3);\n  }\n  .mb-4 {\n    margin-bottom: calc(var(--spacing) * 4);\n  }\n  .mb-5 {\n    margin-bottom: calc(var(--spacing) * 5);\n  }\n  .mb-6 {\n    margin-bottom: calc(var(--spacing) * 6);\n  }\n  .mb-8 {\n    margin-bottom: calc(var(--spacing) * 8);\n  }\n  .mb-10 {\n    margin-bottom: calc(var(--spacing) * 10);\n  }\n  .mb-px {\n    margin-bottom: 1px;\n  }\n  .-ml-1 {\n    margin-left: calc(var(--spacing) * -1);\n  }\n  .-ml-4 {\n    margin-left: calc(var(--spacing) * -4);\n  }\n  .ml-1 {\n    margin-left: calc(var(--spacing) * 1);\n  }\n  .ml-1\\.5 {\n    margin-left: calc(var(--spacing) * 1.5);\n  }\n  .ml-2 {\n    margin-left: calc(var(--spacing) * 2);\n  }\n  .ml-4 {\n    margin-left: calc(var(--spacing) * 4);\n  }\n  .ml-auto {\n    margin-left: auto;\n  }\n  .box-border {\n    box-sizing: border-box;\n  }\n  .box-content {\n    box-sizing: content-box;\n  }\n  .line-clamp-1 {\n    overflow: hidden;\n    display: -webkit-box;\n    -webkit-box-orient: vertical;\n    -webkit-line-clamp: 1;\n  }\n  .line-clamp-2 {\n    overflow: hidden;\n    display: -webkit-box;\n    -webkit-box-orient: vertical;\n    -webkit-line-clamp: 2;\n  }\n  .line-clamp-3 {\n    overflow: hidden;\n    display: -webkit-box;\n    -webkit-box-orient: vertical;\n    -webkit-line-clamp: 3;\n  }\n  .scrollbar-default {\n    -ms-overflow-style: auto;\n    scrollbar-width: auto;\n    &::-webkit-scrollbar {\n      display: block;\n    }\n  }\n  .scrollbar-hide {\n    -ms-overflow-style: none;\n    scrollbar-width: none;\n    &::-webkit-scrollbar {\n      display: none;\n    }\n  }\n  .block {\n    display: block;\n  }\n  .flex {\n    display: flex;\n  }\n  .grid {\n    display: grid;\n  }\n  .hidden {\n    display: none;\n  }\n  .inline {\n    display: inline;\n  }\n  .inline-block {\n    display: inline-block;\n  }\n  .inline-flex {\n    display: inline-flex;\n  }\n  .inline-grid {\n    display: inline-grid;\n  }\n  .table {\n    display: table;\n  }\n  .table-caption {\n    display: table-caption;\n  }\n  .table-cell {\n    display: table-cell;\n  }\n  .table-row {\n    display: table-row;\n  }\n  .field-sizing-content {\n    field-sizing: content;\n  }\n  .aspect-\\[3\\/2\\] {\n    aspect-ratio: 3/2;\n  }\n  .aspect-auto {\n    aspect-ratio: auto;\n  }\n  .aspect-square {\n    aspect-ratio: 1 / 1;\n  }\n  .aspect-video {\n    aspect-ratio: var(--aspect-video);\n  }\n  .size-1 {\n    width: calc(var(--spacing) * 1);\n    height: calc(var(--spacing) * 1);\n  }\n  .size-1\\.5 {\n    width: calc(var(--spacing) * 1.5);\n    height: calc(var(--spacing) * 1.5);\n  }\n  .size-2 {\n    width: calc(var(--spacing) * 2);\n    height: calc(var(--spacing) * 2);\n  }\n  .size-2\\.5 {\n    width: calc(var(--spacing) * 2.5);\n    height: calc(var(--spacing) * 2.5);\n  }\n  .size-3 {\n    width: calc(var(--spacing) * 3);\n    height: calc(var(--spacing) * 3);\n  }\n  .size-3\\.5 {\n    width: calc(var(--spacing) * 3.5);\n    height: calc(var(--spacing) * 3.5);\n  }\n  .size-4 {\n    width: calc(var(--spacing) * 4);\n    height: calc(var(--spacing) * 4);\n  }\n  .size-5 {\n    width: calc(var(--spacing) * 5);\n    height: calc(var(--spacing) * 5);\n  }\n  .size-6 {\n    width: calc(var(--spacing) * 6);\n    height: calc(var(--spacing) * 6);\n  }\n  .size-7 {\n    width: calc(var(--spacing) * 7);\n    height: calc(var(--spacing) * 7);\n  }\n  .size-8 {\n    width: calc(var(--spacing) * 8);\n    height: calc(var(--spacing) * 8);\n  }\n  .size-9 {\n    width: calc(var(--spacing) * 9);\n    height: calc(var(--spacing) * 9);\n  }\n  .size-10 {\n    width: calc(var(--spacing) * 10);\n    height: calc(var(--spacing) * 10);\n  }\n  .size-12 {\n    width: calc(var(--spacing) * 12);\n    height: calc(var(--spacing) * 12);\n  }\n  .size-16 {\n    width: calc(var(--spacing) * 16);\n    height: calc(var(--spacing) * 16);\n  }\n  .size-full {\n    width: 100%;\n    height: 100%;\n  }\n  .\\!h-auto {\n    height: auto !important;\n  }\n  .h-\\(--header-height\\) {\n    height: var(--header-height);\n  }\n  .h-0\\.5 {\n    height: calc(var(--spacing) * 0.5);\n  }\n  .h-1 {\n    height: calc(var(--spacing) * 1);\n  }\n  .h-1\\.5 {\n    height: calc(var(--spacing) * 1.5);\n  }\n  .h-2 {\n    height: calc(var(--spacing) * 2);\n  }\n  .h-2\\.5 {\n    height: calc(var(--spacing) * 2.5);\n  }\n  .h-3 {\n    height: calc(var(--spacing) * 3);\n  }\n  .h-3\\.5 {\n    height: calc(var(--spacing) * 3.5);\n  }\n  .h-4 {\n    height: calc(var(--spacing) * 4);\n  }\n  .h-5 {\n    height: calc(var(--spacing) * 5);\n  }\n  .h-6 {\n    height: calc(var(--spacing) * 6);\n  }\n  .h-7 {\n    height: calc(var(--spacing) * 7);\n  }\n  .h-8 {\n    height: calc(var(--spacing) * 8);\n  }\n  .h-9 {\n    height: calc(var(--spacing) * 9);\n  }\n  .h-10 {\n    height: calc(var(--spacing) * 10);\n  }\n  .h-12 {\n    height: calc(var(--spacing) * 12);\n  }\n  .h-14 {\n    height: calc(var(--spacing) * 14);\n  }\n  .h-15 {\n    height: calc(var(--spacing) * 15);\n  }\n  .h-16 {\n    height: calc(var(--spacing) * 16);\n  }\n  .h-20 {\n    height: calc(var(--spacing) * 20);\n  }\n  .h-24 {\n    height: calc(var(--spacing) * 24);\n  }\n  .h-32 {\n    height: calc(var(--spacing) * 32);\n  }\n  .h-40 {\n    height: calc(var(--spacing) * 40);\n  }\n  .h-64 {\n    height: calc(var(--spacing) * 64);\n  }\n  .h-80 {\n    height: calc(var(--spacing) * 80);\n  }\n  .h-\\[--visual-viewport-height\\] {\n    height: --visual-viewport-height;\n  }\n  .h-\\[1\\.15rem\\] {\n    height: 1.15rem;\n  }\n  .h-\\[2px\\] {\n    height: 2px;\n  }\n  .h-\\[8\\%\\] {\n    height: 8%;\n  }\n  .h-\\[40px\\] {\n    height: 40px;\n  }\n  .h-\\[50\\%\\] {\n    height: 50%;\n  }\n  .h-\\[100dvh\\] {\n    height: 100dvh;\n  }\n  .h-\\[100px\\] {\n    height: 100px;\n  }\n  .h-\\[250px\\] {\n    height: 250px;\n  }\n  .h-\\[300px\\] {\n    height: 300px;\n  }\n  .h-\\[320px\\] {\n    height: 320px;\n  }\n  .h-\\[350px\\] {\n    height: 350px;\n  }\n  .h-\\[400px\\] {\n    height: 400px;\n  }\n  .h-\\[500px\\] {\n    height: 500px;\n  }\n  .h-\\[calc\\(100\\%-1px\\)\\] {\n    height: calc(100% - 1px);\n  }\n  .h-\\[calc\\(100dvh_-_var\\(--navbar-height\\)\\)\\] {\n    height: calc(100dvh - var(--navbar-height));\n  }\n  .h-\\[calc\\(100svh-40px\\)\\] {\n    height: calc(100svh - 40px);\n  }\n  .h-\\[calc\\(100svh-80px\\)\\] {\n    height: calc(100svh - 80px);\n  }\n  .h-\\[var\\(--navbar-height\\)\\] {\n    height: var(--navbar-height);\n  }\n  .h-\\[var\\(--picker-height\\)\\] {\n    height: var(--picker-height);\n  }\n  .h-\\[var\\(--radix-navigation-menu-viewport-height\\)\\] {\n    height: var(--radix-navigation-menu-viewport-height);\n  }\n  .h-\\[var\\(--radix-select-trigger-height\\)\\] {\n    height: var(--radix-select-trigger-height);\n  }\n  .h-auto {\n    height: auto;\n  }\n  .h-divider {\n    height: var(--heroui-divider-weight);\n  }\n  .h-fit {\n    height: fit-content;\n  }\n  .h-full {\n    height: 100%;\n  }\n  .h-px {\n    height: 1px;\n  }\n  .h-screen {\n    height: 100vh;\n  }\n  .h-svh {\n    height: 100svh;\n  }\n  .max-h-\\(--radix-context-menu-content-available-height\\) {\n    max-height: var(--radix-context-menu-content-available-height);\n  }\n  .max-h-\\(--radix-dropdown-menu-content-available-height\\) {\n    max-height: var(--radix-dropdown-menu-content-available-height);\n  }\n  .max-h-\\(--radix-select-content-available-height\\) {\n    max-height: var(--radix-select-content-available-height);\n  }\n  .max-h-60 {\n    max-height: calc(var(--spacing) * 60);\n  }\n  .max-h-64 {\n    max-height: calc(var(--spacing) * 64);\n  }\n  .max-h-\\[20rem\\] {\n    max-height: 20rem;\n  }\n  .max-h-\\[24rem\\] {\n    max-height: 24rem;\n  }\n  .max-h-\\[28rem\\] {\n    max-height: 28rem;\n  }\n  .max-h-\\[32rem\\] {\n    max-height: 32rem;\n  }\n  .max-h-\\[36rem\\] {\n    max-height: 36rem;\n  }\n  .max-h-\\[42rem\\] {\n    max-height: 42rem;\n  }\n  .max-h-\\[48rem\\] {\n    max-height: 48rem;\n  }\n  .max-h-\\[56rem\\] {\n    max-height: 56rem;\n  }\n  .max-h-\\[60px\\] {\n    max-height: 60px;\n  }\n  .max-h-\\[60vh\\] {\n    max-height: 60vh;\n  }\n  .max-h-\\[64rem\\] {\n    max-height: 64rem;\n  }\n  .max-h-\\[80vh\\] {\n    max-height: 80vh;\n  }\n  .max-h-\\[90vh\\] {\n    max-height: 90vh;\n  }\n  .max-h-\\[100px\\] {\n    max-height: 100px;\n  }\n  .max-h-\\[150px\\] {\n    max-height: 150px;\n  }\n  .max-h-\\[200px\\] {\n    max-height: 200px;\n  }\n  .max-h-\\[250px\\] {\n    max-height: 250px;\n  }\n  .max-h-\\[300px\\] {\n    max-height: 300px;\n  }\n  .max-h-\\[400px\\] {\n    max-height: 400px;\n  }\n  .max-h-\\[calc\\(90vh-180px\\)\\] {\n    max-height: calc(90vh - 180px);\n  }\n  .max-h-\\[calc\\(100\\%_-_8rem\\)\\] {\n    max-height: calc(100% - 8rem);\n  }\n  .max-h-\\[none\\] {\n    max-height: none;\n  }\n  .max-h-\\[var\\(--radix-dropdown-menu-content-available-height\\)\\] {\n    max-height: var(--radix-dropdown-menu-content-available-height);\n  }\n  .max-h-full {\n    max-height: 100%;\n  }\n  .min-h-0 {\n    min-height: calc(var(--spacing) * 0);\n  }\n  .min-h-3 {\n    min-height: calc(var(--spacing) * 3);\n  }\n  .min-h-3\\.5 {\n    min-height: calc(var(--spacing) * 3.5);\n  }\n  .min-h-4 {\n    min-height: calc(var(--spacing) * 4);\n  }\n  .min-h-5 {\n    min-height: calc(var(--spacing) * 5);\n  }\n  .min-h-6 {\n    min-height: calc(var(--spacing) * 6);\n  }\n  .min-h-7 {\n    min-height: calc(var(--spacing) * 7);\n  }\n  .min-h-8 {\n    min-height: calc(var(--spacing) * 8);\n  }\n  .min-h-10 {\n    min-height: calc(var(--spacing) * 10);\n  }\n  .min-h-12 {\n    min-height: calc(var(--spacing) * 12);\n  }\n  .min-h-14 {\n    min-height: calc(var(--spacing) * 14);\n  }\n  .min-h-16 {\n    min-height: calc(var(--spacing) * 16);\n  }\n  .min-h-\\[32px\\] {\n    min-height: 32px;\n  }\n  .min-h-\\[52px\\] {\n    min-height: 52px;\n  }\n  .min-h-\\[60px\\] {\n    min-height: 60px;\n  }\n  .min-h-\\[60vh\\] {\n    min-height: 60vh;\n  }\n  .min-h-\\[80px\\] {\n    min-height: 80px;\n  }\n  .min-h-\\[80vh\\] {\n    min-height: 80vh;\n  }\n  .min-h-\\[85px\\] {\n    min-height: 85px;\n  }\n  .min-h-\\[100dvh\\] {\n    min-height: 100dvh;\n  }\n  .min-h-\\[100px\\] {\n    min-height: 100px;\n  }\n  .min-h-\\[105px\\] {\n    min-height: 105px;\n  }\n  .min-h-\\[120px\\] {\n    min-height: 120px;\n  }\n  .min-h-\\[150px\\] {\n    min-height: 150px;\n  }\n  .min-h-\\[200px\\] {\n    min-height: 200px;\n  }\n  .min-h-\\[300px\\] {\n    min-height: 300px;\n  }\n  .min-h-screen {\n    min-height: 100vh;\n  }\n  .min-h-svh {\n    min-height: 100svh;\n  }\n  .\\!w-full {\n    width: 100% !important;\n  }\n  .w-\\(--radix-dropdown-menu-trigger-width\\) {\n    width: var(--radix-dropdown-menu-trigger-width);\n  }\n  .w-\\(--sidebar-width\\) {\n    width: var(--sidebar-width);\n  }\n  .w-0 {\n    width: calc(var(--spacing) * 0);\n  }\n  .w-0\\.5 {\n    width: calc(var(--spacing) * 0.5);\n  }\n  .w-1 {\n    width: calc(var(--spacing) * 1);\n  }\n  .w-1\\.5 {\n    width: calc(var(--spacing) * 1.5);\n  }\n  .w-1\\/2 {\n    width: calc(1/2 * 100%);\n  }\n  .w-1\\/3 {\n    width: calc(1/3 * 100%);\n  }\n  .w-2 {\n    width: calc(var(--spacing) * 2);\n  }\n  .w-2\\.5 {\n    width: calc(var(--spacing) * 2.5);\n  }\n  .w-2\\/3 {\n    width: calc(2/3 * 100%);\n  }\n  .w-3 {\n    width: calc(var(--spacing) * 3);\n  }\n  .w-3\\.5 {\n    width: calc(var(--spacing) * 3.5);\n  }\n  .w-3\\/4 {\n    width: calc(3/4 * 100%);\n  }\n  .w-4 {\n    width: calc(var(--spacing) * 4);\n  }\n  .w-5 {\n    width: calc(var(--spacing) * 5);\n  }\n  .w-6 {\n    width: calc(var(--spacing) * 6);\n  }\n  .w-7 {\n    width: calc(var(--spacing) * 7);\n  }\n  .w-8 {\n    width: calc(var(--spacing) * 8);\n  }\n  .w-9 {\n    width: calc(var(--spacing) * 9);\n  }\n  .w-10 {\n    width: calc(var(--spacing) * 10);\n  }\n  .w-12 {\n    width: calc(var(--spacing) * 12);\n  }\n  .w-14 {\n    width: calc(var(--spacing) * 14);\n  }\n  .w-16 {\n    width: calc(var(--spacing) * 16);\n  }\n  .w-20 {\n    width: calc(var(--spacing) * 20);\n  }\n  .w-24 {\n    width: calc(var(--spacing) * 24);\n  }\n  .w-32 {\n    width: calc(var(--spacing) * 32);\n  }\n  .w-38 {\n    width: calc(var(--spacing) * 38);\n  }\n  .w-40 {\n    width: calc(var(--spacing) * 40);\n  }\n  .w-48 {\n    width: calc(var(--spacing) * 48);\n  }\n  .w-56 {\n    width: calc(var(--spacing) * 56);\n  }\n  .w-60 {\n    width: calc(var(--spacing) * 60);\n  }\n  .w-64 {\n    width: calc(var(--spacing) * 64);\n  }\n  .w-72 {\n    width: calc(var(--spacing) * 72);\n  }\n  .w-80 {\n    width: calc(var(--spacing) * 80);\n  }\n  .w-\\[--radix-dropdown-menu-trigger-width\\] {\n    width: --radix-dropdown-menu-trigger-width;\n  }\n  .w-\\[10\\%\\] {\n    width: 10%;\n  }\n  .w-\\[15\\%\\] {\n    width: 15%;\n  }\n  .w-\\[25\\%\\] {\n    width: 25%;\n  }\n  .w-\\[30\\%\\] {\n    width: 30%;\n  }\n  .w-\\[50px\\] {\n    width: 50px;\n  }\n  .w-\\[60px\\] {\n    width: 60px;\n  }\n  .w-\\[70px\\] {\n    width: 70px;\n  }\n  .w-\\[80\\%\\] {\n    width: 80%;\n  }\n  .w-\\[100px\\] {\n    width: 100px;\n  }\n  .w-\\[100x\\] {\n    width: 100x;\n  }\n  .w-\\[120px\\] {\n    width: 120px;\n  }\n  .w-\\[150px\\] {\n    width: 150px;\n  }\n  .w-\\[160px\\] {\n    width: 160px;\n  }\n  .w-\\[180px\\] {\n    width: 180px;\n  }\n  .w-\\[200px\\] {\n    width: 200px;\n  }\n  .w-\\[240px\\] {\n    width: 240px;\n  }\n  .w-\\[250px\\] {\n    width: 250px;\n  }\n  .w-\\[300px\\] {\n    width: 300px;\n  }\n  .w-\\[350px\\] {\n    width: 350px;\n  }\n  .w-\\[400px\\] {\n    width: 400px;\n  }\n  .w-\\[450px\\] {\n    width: 450px;\n  }\n  .w-\\[480px\\] {\n    width: 480px;\n  }\n  .w-\\[calc\\(100\\%_-_16px\\)\\] {\n    width: calc(100% - 16px);\n  }\n  .w-\\[calc\\(100\\%_-_theme\\(spacing\\.6\\)\\)\\] {\n    width: calc(100% - 1.5rem);\n  }\n  .w-\\[calc\\(var\\(--visible-months\\)_\\*_var\\(--calendar-width\\)\\)\\] {\n    width: calc(var(--visible-months) * var(--calendar-width));\n  }\n  .w-\\[var\\(--radix-popover-trigger-width\\)\\] {\n    width: var(--radix-popover-trigger-width);\n  }\n  .w-auto {\n    width: auto;\n  }\n  .w-divider {\n    width: var(--heroui-divider-weight);\n  }\n  .w-fit {\n    width: fit-content;\n  }\n  .w-full {\n    width: 100%;\n  }\n  .w-max {\n    width: max-content;\n  }\n  .w-px {\n    width: 1px;\n  }\n  .w-screen {\n    width: 100vw;\n  }\n  .\\!max-w-7xl {\n    max-width: var(--container-7xl) !important;\n  }\n  .max-w-\\(--skeleton-width\\) {\n    max-width: var(--skeleton-width);\n  }\n  .max-w-2xl {\n    max-width: var(--container-2xl);\n  }\n  .max-w-3xl {\n    max-width: var(--container-3xl);\n  }\n  .max-w-4xl {\n    max-width: var(--container-4xl);\n  }\n  .max-w-5xl {\n    max-width: var(--container-5xl);\n  }\n  .max-w-6xl {\n    max-width: var(--container-6xl);\n  }\n  .max-w-7xl {\n    max-width: var(--container-7xl);\n  }\n  .max-w-\\[100px\\] {\n    max-width: 100px;\n  }\n  .max-w-\\[120px\\] {\n    max-width: 120px;\n  }\n  .max-w-\\[140px\\] {\n    max-width: 140px;\n  }\n  .max-w-\\[150px\\] {\n    max-width: 150px;\n  }\n  .max-w-\\[180px\\] {\n    max-width: 180px;\n  }\n  .max-w-\\[200px\\] {\n    max-width: 200px;\n  }\n  .max-w-\\[250px\\] {\n    max-width: 250px;\n  }\n  .max-w-\\[270px\\] {\n    max-width: 270px;\n  }\n  .max-w-\\[300px\\] {\n    max-width: 300px;\n  }\n  .max-w-\\[400px\\] {\n    max-width: 400px;\n  }\n  .max-w-\\[640px\\] {\n    max-width: 640px;\n  }\n  .max-w-\\[768px\\] {\n    max-width: 768px;\n  }\n  .max-w-\\[1024px\\] {\n    max-width: 1024px;\n  }\n  .max-w-\\[1280px\\] {\n    max-width: 1280px;\n  }\n  .max-w-\\[1536px\\] {\n    max-width: 1536px;\n  }\n  .max-w-\\[calc\\(100\\%-2rem\\)\\] {\n    max-width: calc(100% - 2rem);\n  }\n  .max-w-\\[none\\] {\n    max-width: none;\n  }\n  .max-w-fit {\n    max-width: fit-content;\n  }\n  .max-w-full {\n    max-width: 100%;\n  }\n  .max-w-lg {\n    max-width: var(--container-lg);\n  }\n  .max-w-max {\n    max-width: max-content;\n  }\n  .max-w-md {\n    max-width: var(--container-md);\n  }\n  .max-w-none {\n    max-width: none;\n  }\n  .max-w-sm {\n    max-width: var(--container-sm);\n  }\n  .max-w-xl {\n    max-width: var(--container-xl);\n  }\n  .max-w-xs {\n    max-width: var(--container-xs);\n  }\n  .min-w-0 {\n    min-width: calc(var(--spacing) * 0);\n  }\n  .min-w-3 {\n    min-width: calc(var(--spacing) * 3);\n  }\n  .min-w-3\\.5 {\n    min-width: calc(var(--spacing) * 3.5);\n  }\n  .min-w-4 {\n    min-width: calc(var(--spacing) * 4);\n  }\n  .min-w-5 {\n    min-width: calc(var(--spacing) * 5);\n  }\n  .min-w-6 {\n    min-width: calc(var(--spacing) * 6);\n  }\n  .min-w-7 {\n    min-width: calc(var(--spacing) * 7);\n  }\n  .min-w-8 {\n    min-width: calc(var(--spacing) * 8);\n  }\n  .min-w-9 {\n    min-width: calc(var(--spacing) * 9);\n  }\n  .min-w-10 {\n    min-width: calc(var(--spacing) * 10);\n  }\n  .min-w-12 {\n    min-width: calc(var(--spacing) * 12);\n  }\n  .min-w-16 {\n    min-width: calc(var(--spacing) * 16);\n  }\n  .min-w-20 {\n    min-width: calc(var(--spacing) * 20);\n  }\n  .min-w-24 {\n    min-width: calc(var(--spacing) * 24);\n  }\n  .min-w-40 {\n    min-width: calc(var(--spacing) * 40);\n  }\n  .min-w-56 {\n    min-width: calc(var(--spacing) * 56);\n  }\n  .min-w-60 {\n    min-width: calc(var(--spacing) * 60);\n  }\n  .min-w-\\[8rem\\] {\n    min-width: 8rem;\n  }\n  .min-w-\\[12rem\\] {\n    min-width: 12rem;\n  }\n  .min-w-\\[40px\\] {\n    min-width: 40px;\n  }\n  .min-w-\\[100px\\] {\n    min-width: 100px;\n  }\n  .min-w-\\[120px\\] {\n    min-width: 120px;\n  }\n  .min-w-\\[180px\\] {\n    min-width: 180px;\n  }\n  .min-w-\\[200px\\] {\n    min-width: 200px;\n  }\n  .min-w-\\[var\\(--radix-select-trigger-width\\)\\] {\n    min-width: var(--radix-select-trigger-width);\n  }\n  .min-w-full {\n    min-width: 100%;\n  }\n  .min-w-max {\n    min-width: max-content;\n  }\n  .min-w-min {\n    min-width: min-content;\n  }\n  .flex-1 {\n    flex: 1;\n  }\n  .flex-\\[3\\] {\n    flex: 3;\n  }\n  .flex-auto {\n    flex: auto;\n  }\n  .flex-initial {\n    flex: 0 auto;\n  }\n  .flex-none {\n    flex: none;\n  }\n  .flex-shrink-0 {\n    flex-shrink: 0;\n  }\n  .shrink-0 {\n    flex-shrink: 0;\n  }\n  .flex-grow {\n    flex-grow: 1;\n  }\n  .grow {\n    flex-grow: 1;\n  }\n  .grow-0 {\n    flex-grow: 0;\n  }\n  .basis-0 {\n    flex-basis: calc(var(--spacing) * 0);\n  }\n  .basis-full {\n    flex-basis: 100%;\n  }\n  .table-auto {\n    table-layout: auto;\n  }\n  .table-fixed {\n    table-layout: fixed;\n  }\n  .caption-bottom {\n    caption-side: bottom;\n  }\n  .border-collapse {\n    border-collapse: collapse;\n  }\n  .origin-\\(--radix-context-menu-content-transform-origin\\) {\n    transform-origin: var(--radix-context-menu-content-transform-origin);\n  }\n  .origin-\\(--radix-dropdown-menu-content-transform-origin\\) {\n    transform-origin: var(--radix-dropdown-menu-content-transform-origin);\n  }\n  .origin-\\(--radix-hover-card-content-transform-origin\\) {\n    transform-origin: var(--radix-hover-card-content-transform-origin);\n  }\n  .origin-\\(--radix-menubar-content-transform-origin\\) {\n    transform-origin: var(--radix-menubar-content-transform-origin);\n  }\n  .origin-\\(--radix-popover-content-transform-origin\\) {\n    transform-origin: var(--radix-popover-content-transform-origin);\n  }\n  .origin-\\(--radix-select-content-transform-origin\\) {\n    transform-origin: var(--radix-select-content-transform-origin);\n  }\n  .origin-\\(--radix-tooltip-content-transform-origin\\) {\n    transform-origin: var(--radix-tooltip-content-transform-origin);\n  }\n  .origin-center {\n    transform-origin: center;\n  }\n  .origin-left {\n    transform-origin: left;\n  }\n  .origin-right {\n    transform-origin: right;\n  }\n  .origin-top {\n    transform-origin: top;\n  }\n  .origin-top-left {\n    transform-origin: top left;\n  }\n  .-translate-x-1\\/2 {\n    --tw-translate-x: calc(calc(1/2 * 100%) * -1);\n    translate: var(--tw-translate-x) var(--tw-translate-y);\n  }\n  .-translate-x-px {\n    --tw-translate-x: -1px;\n    translate: var(--tw-translate-x) var(--tw-translate-y);\n  }\n  .translate-x-1 {\n    --tw-translate-x: calc(var(--spacing) * 1);\n    translate: var(--tw-translate-x) var(--tw-translate-y);\n  }\n  .translate-x-1\\/2 {\n    --tw-translate-x: calc(1/2 * 100%);\n    translate: var(--tw-translate-x) var(--tw-translate-y);\n  }\n  .translate-x-\\[-50\\%\\] {\n    --tw-translate-x: -50%;\n    translate: var(--tw-translate-x) var(--tw-translate-y);\n  }\n  .translate-x-px {\n    --tw-translate-x: 1px;\n    translate: var(--tw-translate-x) var(--tw-translate-y);\n  }\n  .-translate-y-1 {\n    --tw-translate-y: calc(var(--spacing) * -1);\n    translate: var(--tw-translate-x) var(--tw-translate-y);\n  }\n  .-translate-y-1\\/2 {\n    --tw-translate-y: calc(calc(1/2 * 100%) * -1);\n    translate: var(--tw-translate-x) var(--tw-translate-y);\n  }\n  .translate-y-0 {\n    --tw-translate-y: calc(var(--spacing) * 0);\n    translate: var(--tw-translate-x) var(--tw-translate-y);\n  }\n  .translate-y-0\\.5 {\n    --tw-translate-y: calc(var(--spacing) * 0.5);\n    translate: var(--tw-translate-x) var(--tw-translate-y);\n  }\n  .translate-y-1 {\n    --tw-translate-y: calc(var(--spacing) * 1);\n    translate: var(--tw-translate-x) var(--tw-translate-y);\n  }\n  .translate-y-1\\/2 {\n    --tw-translate-y: calc(1/2 * 100%);\n    translate: var(--tw-translate-x) var(--tw-translate-y);\n  }\n  .translate-y-2\\/4 {\n    --tw-translate-y: calc(2/4 * 100%);\n    translate: var(--tw-translate-x) var(--tw-translate-y);\n  }\n  .translate-y-3\\/4 {\n    --tw-translate-y: calc(3/4 * 100%);\n    translate: var(--tw-translate-x) var(--tw-translate-y);\n  }\n  .translate-y-16 {\n    --tw-translate-y: calc(var(--spacing) * 16);\n    translate: var(--tw-translate-x) var(--tw-translate-y);\n  }\n  .translate-y-\\[-50\\%\\] {\n    --tw-translate-y: -50%;\n    translate: var(--tw-translate-x) var(--tw-translate-y);\n  }\n  .translate-y-\\[2px\\] {\n    --tw-translate-y: 2px;\n    translate: var(--tw-translate-x) var(--tw-translate-y);\n  }\n  .translate-y-\\[calc\\(-50\\%_-_2px\\)\\] {\n    --tw-translate-y: calc(-50% - 2px);\n    translate: var(--tw-translate-x) var(--tw-translate-y);\n  }\n  .scale-0 {\n    --tw-scale-x: 0%;\n    --tw-scale-y: 0%;\n    --tw-scale-z: 0%;\n    scale: var(--tw-scale-x) var(--tw-scale-y);\n  }\n  .scale-50 {\n    --tw-scale-x: 50%;\n    --tw-scale-y: 50%;\n    --tw-scale-z: 50%;\n    scale: var(--tw-scale-x) var(--tw-scale-y);\n  }\n  .scale-90 {\n    --tw-scale-x: 90%;\n    --tw-scale-y: 90%;\n    --tw-scale-z: 90%;\n    scale: var(--tw-scale-x) var(--tw-scale-y);\n  }\n  .scale-100 {\n    --tw-scale-x: 100%;\n    --tw-scale-y: 100%;\n    --tw-scale-z: 100%;\n    scale: var(--tw-scale-x) var(--tw-scale-y);\n  }\n  .scale-105 {\n    --tw-scale-x: 105%;\n    --tw-scale-y: 105%;\n    --tw-scale-z: 105%;\n    scale: var(--tw-scale-x) var(--tw-scale-y);\n  }\n  .rotate-0 {\n    rotate: 0deg;\n  }\n  .rotate-45 {\n    rotate: 45deg;\n  }\n  .rotate-90 {\n    rotate: 90deg;\n  }\n  .rotate-180 {\n    rotate: 180deg;\n  }\n  .spinner-bar-animation {\n    animation-delay: calc(-1.2s + (0.1s * var(--bar-index)));\n    transform: rotate(calc(30deg * var(--bar-index)))translate(140%);\n  }\n  .transform {\n    transform: var(--tw-rotate-x) var(--tw-rotate-y) var(--tw-rotate-z) var(--tw-skew-x) var(--tw-skew-y);\n  }\n  .transform-gpu {\n    transform: translateZ(0) var(--tw-rotate-x) var(--tw-rotate-y) var(--tw-rotate-z) var(--tw-skew-x) var(--tw-skew-y);\n  }\n  .animate-\\[appearance-in_1s_infinite\\] {\n    animation: appearance-in 1s infinite;\n  }\n  .animate-blink {\n    animation: blink 1.4s infinite both;\n  }\n  .animate-caret-blink {\n    animation: caret-blink 1.25s ease-out infinite;\n  }\n  .animate-drip-expand {\n    animation: drip-expand 420ms linear;\n  }\n  .animate-fade-out {\n    animation: fade-out 1.2s linear 0s infinite normal none running;\n  }\n  .animate-in {\n    animation: enter var(--tw-duration,.15s)var(--tw-ease,ease);\n  }\n  .animate-indeterminate-bar {\n    animation: indeterminate-bar 1.5s cubic-bezier(0.65, 0.815, 0.735, 0.395) infinite normal none running;\n  }\n  .animate-none {\n    animation: none;\n  }\n  .animate-pulse {\n    animation: var(--animate-pulse);\n  }\n  .animate-spin {\n    animation: var(--animate-spin);\n  }\n  .animate-spinner-ease-spin {\n    animation: spinner-spin 0.8s ease infinite;\n  }\n  .animate-spinner-linear-spin {\n    animation: spinner-spin 0.8s linear infinite;\n  }\n  .animate-sway {\n    animation: sway 750ms ease infinite;\n  }\n  .cursor-default {\n    cursor: default;\n  }\n  .cursor-grab {\n    cursor: grab;\n  }\n  .cursor-help {\n    cursor: help;\n  }\n  .cursor-not-allowed {\n    cursor: not-allowed;\n  }\n  .cursor-pointer {\n    cursor: pointer;\n  }\n  .cursor-text {\n    cursor: text;\n  }\n  .touch-none {\n    touch-action: none;\n  }\n  .resize {\n    resize: both;\n  }\n  .resize-none {\n    resize: none;\n  }\n  .resize-y {\n    resize: vertical;\n  }\n  .snap-y {\n    scroll-snap-type: y var(--tw-scroll-snap-strictness);\n  }\n  .snap-mandatory {\n    --tw-scroll-snap-strictness: mandatory;\n  }\n  .snap-center {\n    scroll-snap-align: center;\n  }\n  .scroll-my-1 {\n    scroll-margin-block: calc(var(--spacing) * 1);\n  }\n  .scroll-ml-6 {\n    scroll-margin-left: calc(var(--spacing) * 6);\n  }\n  .scroll-py-1 {\n    scroll-padding-block: calc(var(--spacing) * 1);\n  }\n  .scroll-py-6 {\n    scroll-padding-block: calc(var(--spacing) * 6);\n  }\n  .list-disc {\n    list-style-type: disc;\n  }\n  .list-none {\n    list-style-type: none;\n  }\n  .appearance-none {\n    appearance: none;\n  }\n  .auto-rows-fr {\n    grid-auto-rows: minmax(0, 1fr);\n  }\n  .auto-rows-min {\n    grid-auto-rows: min-content;\n  }\n  .grid-cols-1 {\n    grid-template-columns: repeat(1, minmax(0, 1fr));\n  }\n  .grid-cols-2 {\n    grid-template-columns: repeat(2, minmax(0, 1fr));\n  }\n  .grid-cols-3 {\n    grid-template-columns: repeat(3, minmax(0, 1fr));\n  }\n  .grid-cols-4 {\n    grid-template-columns: repeat(4, minmax(0, 1fr));\n  }\n  .grid-cols-5 {\n    grid-template-columns: repeat(5, minmax(0, 1fr));\n  }\n  .grid-cols-6 {\n    grid-template-columns: repeat(6, minmax(0, 1fr));\n  }\n  .grid-cols-\\[0_1fr\\] {\n    grid-template-columns: 0 1fr;\n  }\n  .grid-rows-\\[auto_auto\\] {\n    grid-template-rows: auto auto;\n  }\n  .flex-col {\n    flex-direction: column;\n  }\n  .flex-col-reverse {\n    flex-direction: column-reverse;\n  }\n  .flex-row {\n    flex-direction: row;\n  }\n  .flex-row-reverse {\n    flex-direction: row-reverse;\n  }\n  .flex-nowrap {\n    flex-wrap: nowrap;\n  }\n  .flex-wrap {\n    flex-wrap: wrap;\n  }\n  .place-content-center {\n    place-content: center;\n  }\n  .place-items-center {\n    place-items: center;\n  }\n  .items-center {\n    align-items: center;\n  }\n  .items-end {\n    align-items: flex-end;\n  }\n  .items-start {\n    align-items: flex-start;\n  }\n  .items-stretch {\n    align-items: stretch;\n  }\n  .justify-between {\n    justify-content: space-between;\n  }\n  .justify-center {\n    justify-content: center;\n  }\n  .justify-end {\n    justify-content: flex-end;\n  }\n  .justify-start {\n    justify-content: flex-start;\n  }\n  .justify-items-start {\n    justify-items: start;\n  }\n  .\\!gap-0 {\n    gap: calc(var(--spacing) * 0) !important;\n  }\n  .gap-0 {\n    gap: calc(var(--spacing) * 0);\n  }\n  .gap-0\\.5 {\n    gap: calc(var(--spacing) * 0.5);\n  }\n  .gap-1 {\n    gap: calc(var(--spacing) * 1);\n  }\n  .gap-1\\.5 {\n    gap: calc(var(--spacing) * 1.5);\n  }\n  .gap-2 {\n    gap: calc(var(--spacing) * 2);\n  }\n  .gap-3 {\n    gap: calc(var(--spacing) * 3);\n  }\n  .gap-4 {\n    gap: calc(var(--spacing) * 4);\n  }\n  .gap-6 {\n    gap: calc(var(--spacing) * 6);\n  }\n  .gap-8 {\n    gap: calc(var(--spacing) * 8);\n  }\n  .gap-12 {\n    gap: calc(var(--spacing) * 12);\n  }\n  .space-y-0 {\n    :where(& > :not(:last-child)) {\n      --tw-space-y-reverse: 0;\n      margin-block-start: calc(calc(var(--spacing) * 0) * var(--tw-space-y-reverse));\n      margin-block-end: calc(calc(var(--spacing) * 0) * calc(1 - var(--tw-space-y-reverse)));\n    }\n  }\n  .space-y-0\\.5 {\n    :where(& > :not(:last-child)) {\n      --tw-space-y-reverse: 0;\n      margin-block-start: calc(calc(var(--spacing) * 0.5) * var(--tw-space-y-reverse));\n      margin-block-end: calc(calc(var(--spacing) * 0.5) * calc(1 - var(--tw-space-y-reverse)));\n    }\n  }\n  .space-y-1 {\n    :where(& > :not(:last-child)) {\n      --tw-space-y-reverse: 0;\n      margin-block-start: calc(calc(var(--spacing) * 1) * var(--tw-space-y-reverse));\n      margin-block-end: calc(calc(var(--spacing) * 1) * calc(1 - var(--tw-space-y-reverse)));\n    }\n  }\n  .space-y-1\\.5 {\n    :where(& > :not(:last-child)) {\n      --tw-space-y-reverse: 0;\n      margin-block-start: calc(calc(var(--spacing) * 1.5) * var(--tw-space-y-reverse));\n      margin-block-end: calc(calc(var(--spacing) * 1.5) * calc(1 - var(--tw-space-y-reverse)));\n    }\n  }\n  .space-y-2 {\n    :where(& > :not(:last-child)) {\n      --tw-space-y-reverse: 0;\n      margin-block-start: calc(calc(var(--spacing) * 2) * var(--tw-space-y-reverse));\n      margin-block-end: calc(calc(var(--spacing) * 2) * calc(1 - var(--tw-space-y-reverse)));\n    }\n  }\n  .space-y-3 {\n    :where(& > :not(:last-child)) {\n      --tw-space-y-reverse: 0;\n      margin-block-start: calc(calc(var(--spacing) * 3) * var(--tw-space-y-reverse));\n      margin-block-end: calc(calc(var(--spacing) * 3) * calc(1 - var(--tw-space-y-reverse)));\n    }\n  }\n  .space-y-4 {\n    :where(& > :not(:last-child)) {\n      --tw-space-y-reverse: 0;\n      margin-block-start: calc(calc(var(--spacing) * 4) * var(--tw-space-y-reverse));\n      margin-block-end: calc(calc(var(--spacing) * 4) * calc(1 - var(--tw-space-y-reverse)));\n    }\n  }\n  .space-y-6 {\n    :where(& > :not(:last-child)) {\n      --tw-space-y-reverse: 0;\n      margin-block-start: calc(calc(var(--spacing) * 6) * var(--tw-space-y-reverse));\n      margin-block-end: calc(calc(var(--spacing) * 6) * calc(1 - var(--tw-space-y-reverse)));\n    }\n  }\n  .space-y-8 {\n    :where(& > :not(:last-child)) {\n      --tw-space-y-reverse: 0;\n      margin-block-start: calc(calc(var(--spacing) * 8) * var(--tw-space-y-reverse));\n      margin-block-end: calc(calc(var(--spacing) * 8) * calc(1 - var(--tw-space-y-reverse)));\n    }\n  }\n  .gap-x-0 {\n    column-gap: calc(var(--spacing) * 0);\n  }\n  .gap-x-0\\.5 {\n    column-gap: calc(var(--spacing) * 0.5);\n  }\n  .gap-x-1 {\n    column-gap: calc(var(--spacing) * 1);\n  }\n  .gap-x-2 {\n    column-gap: calc(var(--spacing) * 2);\n  }\n  .gap-x-4 {\n    column-gap: calc(var(--spacing) * 4);\n  }\n  .gap-x-6 {\n    column-gap: calc(var(--spacing) * 6);\n  }\n  .space-x-0\\.5 {\n    :where(& > :not(:last-child)) {\n      --tw-space-x-reverse: 0;\n      margin-inline-start: calc(calc(var(--spacing) * 0.5) * var(--tw-space-x-reverse));\n      margin-inline-end: calc(calc(var(--spacing) * 0.5) * calc(1 - var(--tw-space-x-reverse)));\n    }\n  }\n  .space-x-1 {\n    :where(& > :not(:last-child)) {\n      --tw-space-x-reverse: 0;\n      margin-inline-start: calc(calc(var(--spacing) * 1) * var(--tw-space-x-reverse));\n      margin-inline-end: calc(calc(var(--spacing) * 1) * calc(1 - var(--tw-space-x-reverse)));\n    }\n  }\n  .space-x-2 {\n    :where(& > :not(:last-child)) {\n      --tw-space-x-reverse: 0;\n      margin-inline-start: calc(calc(var(--spacing) * 2) * var(--tw-space-x-reverse));\n      margin-inline-end: calc(calc(var(--spacing) * 2) * calc(1 - var(--tw-space-x-reverse)));\n    }\n  }\n  .space-x-3 {\n    :where(& > :not(:last-child)) {\n      --tw-space-x-reverse: 0;\n      margin-inline-start: calc(calc(var(--spacing) * 3) * var(--tw-space-x-reverse));\n      margin-inline-end: calc(calc(var(--spacing) * 3) * calc(1 - var(--tw-space-x-reverse)));\n    }\n  }\n  .space-x-4 {\n    :where(& > :not(:last-child)) {\n      --tw-space-x-reverse: 0;\n      margin-inline-start: calc(calc(var(--spacing) * 4) * var(--tw-space-x-reverse));\n      margin-inline-end: calc(calc(var(--spacing) * 4) * calc(1 - var(--tw-space-x-reverse)));\n    }\n  }\n  .space-x-6 {\n    :where(& > :not(:last-child)) {\n      --tw-space-x-reverse: 0;\n      margin-inline-start: calc(calc(var(--spacing) * 6) * var(--tw-space-x-reverse));\n      margin-inline-end: calc(calc(var(--spacing) * 6) * calc(1 - var(--tw-space-x-reverse)));\n    }\n  }\n  .gap-y-0 {\n    row-gap: calc(var(--spacing) * 0);\n  }\n  .gap-y-0\\.5 {\n    row-gap: calc(var(--spacing) * 0.5);\n  }\n  .gap-y-1\\.5 {\n    row-gap: calc(var(--spacing) * 1.5);\n  }\n  .gap-y-2 {\n    row-gap: calc(var(--spacing) * 2);\n  }\n  .divide-y {\n    :where(& > :not(:last-child)) {\n      --tw-divide-y-reverse: 0;\n      border-bottom-style: var(--tw-border-style);\n      border-top-style: var(--tw-border-style);\n      border-top-width: calc(1px * var(--tw-divide-y-reverse));\n      border-bottom-width: calc(1px * calc(1 - var(--tw-divide-y-reverse)));\n    }\n  }\n  .self-center {\n    align-self: center;\n  }\n  .self-start {\n    align-self: flex-start;\n  }\n  .justify-self-end {\n    justify-self: flex-end;\n  }\n  .truncate {\n    overflow: hidden;\n    text-overflow: ellipsis;\n    white-space: nowrap;\n  }\n  .overflow-auto {\n    overflow: auto;\n  }\n  .overflow-clip {\n    overflow: clip;\n  }\n  .overflow-hidden {\n    overflow: hidden;\n  }\n  .overflow-visible {\n    overflow: visible;\n  }\n  .overflow-x-auto {\n    overflow-x: auto;\n  }\n  .overflow-x-hidden {\n    overflow-x: hidden;\n  }\n  .overflow-x-scroll {\n    overflow-x: scroll;\n  }\n  .overflow-y-auto {\n    overflow-y: auto;\n  }\n  .overflow-y-hidden {\n    overflow-y: hidden;\n  }\n  .overflow-y-scroll {\n    overflow-y: scroll;\n  }\n  .\\!rounded-none {\n    border-radius: 0 !important;\n  }\n  .rounded {\n    border-radius: 0.25rem;\n  }\n  .rounded-\\[2px\\] {\n    border-radius: 2px;\n  }\n  .rounded-\\[4px\\] {\n    border-radius: 4px;\n  }\n  .rounded-\\[calc\\(theme\\(borderRadius\\.large\\)\\/1\\.5\\)\\] {\n    border-radius: calc(var(--heroui-radius-large) / 1.5);\n  }\n  .rounded-\\[calc\\(theme\\(borderRadius\\.medium\\)\\*0\\.5\\)\\] {\n    border-radius: calc(var(--heroui-radius-medium) * 0.5);\n  }\n  .rounded-\\[calc\\(theme\\(borderRadius\\.medium\\)\\*0\\.6\\)\\] {\n    border-radius: calc(var(--heroui-radius-medium) * 0.6);\n  }\n  .rounded-\\[calc\\(theme\\(borderRadius\\.medium\\)\\*0\\.7\\)\\] {\n    border-radius: calc(var(--heroui-radius-medium) * 0.7);\n  }\n  .rounded-\\[calc\\(theme\\(borderRadius\\.medium\\)\\/2\\)\\] {\n    border-radius: calc(var(--heroui-radius-medium) / 2);\n  }\n  .rounded-\\[calc\\(theme\\(borderRadius\\.small\\)\\/2\\)\\] {\n    border-radius: calc(var(--heroui-radius-small) / 2);\n  }\n  .rounded-\\[inherit\\] {\n    border-radius: inherit;\n  }\n  .rounded-full {\n    border-radius: calc(infinity * 1px);\n  }\n  .rounded-large {\n    border-radius: var(--heroui-radius-large);\n  }\n  .rounded-lg {\n    border-radius: var(--radius);\n  }\n  .rounded-md {\n    border-radius: calc(var(--radius) - 2px);\n  }\n  .rounded-medium {\n    border-radius: var(--heroui-radius-medium);\n  }\n  .rounded-none {\n    border-radius: 0;\n  }\n  .rounded-sm {\n    border-radius: calc(var(--radius) - 4px);\n  }\n  .rounded-small {\n    border-radius: var(--heroui-radius-small);\n  }\n  .rounded-xl {\n    border-radius: calc(var(--radius) + 4px);\n  }\n  .rounded-xs {\n    border-radius: var(--radius-xs);\n  }\n  .\\!rounded-s-none {\n    border-start-start-radius: 0 !important;\n    border-end-start-radius: 0 !important;\n  }\n  .\\!rounded-e-none {\n    border-start-end-radius: 0 !important;\n    border-end-end-radius: 0 !important;\n  }\n  .rounded-t-large {\n    border-top-left-radius: var(--heroui-radius-large);\n    border-top-right-radius: var(--heroui-radius-large);\n  }\n  .rounded-t-medium {\n    border-top-left-radius: var(--heroui-radius-medium);\n    border-top-right-radius: var(--heroui-radius-medium);\n  }\n  .rounded-t-none {\n    border-top-left-radius: 0;\n    border-top-right-radius: 0;\n  }\n  .rounded-t-small {\n    border-top-left-radius: var(--heroui-radius-small);\n    border-top-right-radius: var(--heroui-radius-small);\n  }\n  .rounded-l-none {\n    border-top-left-radius: 0;\n    border-bottom-left-radius: 0;\n  }\n  .rounded-tl-sm {\n    border-top-left-radius: calc(var(--radius) - 4px);\n  }\n  .rounded-r-none {\n    border-top-right-radius: 0;\n    border-bottom-right-radius: 0;\n  }\n  .rounded-b-large {\n    border-bottom-right-radius: var(--heroui-radius-large);\n    border-bottom-left-radius: var(--heroui-radius-large);\n  }\n  .rounded-b-medium {\n    border-bottom-right-radius: var(--heroui-radius-medium);\n    border-bottom-left-radius: var(--heroui-radius-medium);\n  }\n  .rounded-b-none {\n    border-bottom-right-radius: 0;\n    border-bottom-left-radius: 0;\n  }\n  .rounded-b-small {\n    border-bottom-right-radius: var(--heroui-radius-small);\n    border-bottom-left-radius: var(--heroui-radius-small);\n  }\n  .border {\n    border-style: var(--tw-border-style);\n    border-width: 1px;\n  }\n  .border-0 {\n    border-style: var(--tw-border-style);\n    border-width: 0px;\n  }\n  .border-1 {\n    border-style: var(--tw-border-style);\n    border-width: 1px;\n  }\n  .border-2 {\n    border-style: var(--tw-border-style);\n    border-width: 2px;\n  }\n  .border-3 {\n    border-style: var(--tw-border-style);\n    border-width: 3px;\n  }\n  .border-\\[1\\.5px\\] {\n    border-style: var(--tw-border-style);\n    border-width: 1.5px;\n  }\n  .border-medium {\n    border-style: var(--tw-border-style);\n    border-width: var(--heroui-border-width-medium);\n  }\n  .border-small {\n    border-style: var(--tw-border-style);\n    border-width: var(--heroui-border-width-small);\n  }\n  .border-x-\\[calc\\(theme\\(spacing\\.5\\)\\/2\\)\\] {\n    border-inline-style: var(--tw-border-style);\n    border-inline-width: calc(1.25rem / 2);\n  }\n  .border-x-\\[calc\\(theme\\(spacing\\.6\\)\\/2\\)\\] {\n    border-inline-style: var(--tw-border-style);\n    border-inline-width: calc(1.5rem / 2);\n  }\n  .border-x-\\[calc\\(theme\\(spacing\\.7\\)\\/2\\)\\] {\n    border-inline-style: var(--tw-border-style);\n    border-inline-width: calc(1.75rem / 2);\n  }\n  .border-y {\n    border-block-style: var(--tw-border-style);\n    border-block-width: 1px;\n  }\n  .border-y-\\[calc\\(theme\\(spacing\\.5\\)\\/2\\)\\] {\n    border-block-style: var(--tw-border-style);\n    border-block-width: calc(1.25rem / 2);\n  }\n  .border-y-\\[calc\\(theme\\(spacing\\.6\\)\\/2\\)\\] {\n    border-block-style: var(--tw-border-style);\n    border-block-width: calc(1.5rem / 2);\n  }\n  .border-y-\\[calc\\(theme\\(spacing\\.7\\)\\/2\\)\\] {\n    border-block-style: var(--tw-border-style);\n    border-block-width: calc(1.75rem / 2);\n  }\n  .border-t {\n    border-top-style: var(--tw-border-style);\n    border-top-width: 1px;\n  }\n  .border-t-2 {\n    border-top-style: var(--tw-border-style);\n    border-top-width: 2px;\n  }\n  .border-r {\n    border-right-style: var(--tw-border-style);\n    border-right-width: 1px;\n  }\n  .border-b {\n    border-bottom-style: var(--tw-border-style);\n    border-bottom-width: 1px;\n  }\n  .border-b-2 {\n    border-bottom-style: var(--tw-border-style);\n    border-bottom-width: 2px;\n  }\n  .border-b-medium {\n    border-bottom-style: var(--tw-border-style);\n    border-bottom-width: var(--heroui-border-width-medium);\n  }\n  .border-l {\n    border-left-style: var(--tw-border-style);\n    border-left-width: 1px;\n  }\n  .border-l-4 {\n    border-left-style: var(--tw-border-style);\n    border-left-width: 4px;\n  }\n  .\\!border-none {\n    --tw-border-style: none !important;\n    border-style: none !important;\n  }\n  .border-dashed {\n    --tw-border-style: dashed;\n    border-style: dashed;\n  }\n  .border-dotted {\n    --tw-border-style: dotted;\n    border-style: dotted;\n  }\n  .border-none {\n    --tw-border-style: none;\n    border-style: none;\n  }\n  .border-solid {\n    --tw-border-style: solid;\n    border-style: solid;\n  }\n  .\\!border-danger {\n    border-color: hsl(var(--heroui-danger) / 1) !important;\n  }\n  .border-\\(--color-border\\) {\n    border-color: var(--color-border);\n  }\n  .border-accent {\n    border-color: var(--accent);\n  }\n  .border-background {\n    border-color: var(--background);\n  }\n  .border-blue-100 {\n    border-color: var(--color-blue-100);\n  }\n  .border-blue-200 {\n    border-color: var(--color-blue-200);\n  }\n  .border-border {\n    border-color: var(--border);\n  }\n  .border-border\\/50 {\n    border-color: var(--border);\n    @supports (color: color-mix(in lab, red, red)) {\n      border-color: color-mix(in oklab, var(--border) 50%, transparent);\n    }\n  }\n  .border-danger {\n    border-color: hsl(var(--heroui-danger) / 1);\n  }\n  .border-danger-100 {\n    border-color: hsl(var(--heroui-danger-100) / 1);\n  }\n  .border-danger-200 {\n    border-color: hsl(var(--heroui-danger-200) / 1);\n  }\n  .border-danger-400 {\n    border-color: hsl(var(--heroui-danger-400) / 1);\n  }\n  .border-default {\n    border-color: hsl(var(--heroui-default) / 1);\n  }\n  .border-default-100 {\n    border-color: hsl(var(--heroui-default-100) / 1);\n  }\n  .border-default-200 {\n    border-color: hsl(var(--heroui-default-200) / 1);\n  }\n  .border-default-300 {\n    border-color: hsl(var(--heroui-default-300) / 1);\n  }\n  .border-default-400 {\n    border-color: hsl(var(--heroui-default-400) / 1);\n  }\n  .border-destructive\\/30 {\n    border-color: var(--destructive);\n    @supports (color: color-mix(in lab, red, red)) {\n      border-color: color-mix(in oklab, var(--destructive) 30%, transparent);\n    }\n  }\n  .border-divider {\n    border-color: hsl(var(--heroui-divider) / 0.15);\n  }\n  .border-foreground {\n    border-color: var(--foreground);\n  }\n  .border-foreground-400 {\n    border-color: hsl(var(--heroui-foreground-400) / 1);\n  }\n  .border-gray-100 {\n    border-color: var(--color-gray-100);\n  }\n  .border-gray-200 {\n    border-color: var(--color-gray-200);\n  }\n  .border-green-100 {\n    border-color: var(--color-green-100);\n  }\n  .border-green-200 {\n    border-color: var(--color-green-200);\n  }\n  .border-input {\n    border-color: var(--input);\n  }\n  .border-muted-foreground\\/5 {\n    border-color: var(--muted-foreground);\n    @supports (color: color-mix(in lab, red, red)) {\n      border-color: color-mix(in oklab, var(--muted-foreground) 5%, transparent);\n    }\n  }\n  .border-primary {\n    border-color: var(--primary);\n  }\n  .border-primary-100 {\n    border-color: hsl(var(--heroui-primary-100) / 1);\n  }\n  .border-primary-200 {\n    border-color: hsl(var(--heroui-primary-200) / 1);\n  }\n  .border-primary-400 {\n    border-color: hsl(var(--heroui-primary-400) / 1);\n  }\n  .border-primary\\/20 {\n    border-color: var(--primary);\n    @supports (color: color-mix(in lab, red, red)) {\n      border-color: color-mix(in oklab, var(--primary) 20%, transparent);\n    }\n  }\n  .border-red-100 {\n    border-color: var(--color-red-100);\n  }\n  .border-red-200 {\n    border-color: var(--color-red-200);\n  }\n  .border-red-500 {\n    border-color: var(--color-red-500);\n  }\n  .border-secondary {\n    border-color: var(--secondary);\n  }\n  .border-secondary-100 {\n    border-color: hsl(var(--heroui-secondary-100) / 1);\n  }\n  .border-secondary-200 {\n    border-color: hsl(var(--heroui-secondary-200) / 1);\n  }\n  .border-secondary-400 {\n    border-color: hsl(var(--heroui-secondary-400) / 1);\n  }\n  .border-sidebar-border {\n    border-color: var(--sidebar-border);\n  }\n  .border-success {\n    border-color: hsl(var(--heroui-success) / 1);\n  }\n  .border-success-100 {\n    border-color: hsl(var(--heroui-success-100) / 1);\n  }\n  .border-success-200 {\n    border-color: hsl(var(--heroui-success-200) / 1);\n  }\n  .border-success-300 {\n    border-color: hsl(var(--heroui-success-300) / 1);\n  }\n  .border-success-400 {\n    border-color: hsl(var(--heroui-success-400) / 1);\n  }\n  .border-transparent {\n    border-color: transparent;\n  }\n  .border-warning {\n    border-color: hsl(var(--heroui-warning) / 1);\n  }\n  .border-warning-100 {\n    border-color: hsl(var(--heroui-warning-100) / 1);\n  }\n  .border-warning-200 {\n    border-color: hsl(var(--heroui-warning-200) / 1);\n  }\n  .border-warning-300 {\n    border-color: hsl(var(--heroui-warning-300) / 1);\n  }\n  .border-warning-400 {\n    border-color: hsl(var(--heroui-warning-400) / 1);\n  }\n  .border-yellow-200 {\n    border-color: var(--color-yellow-200);\n  }\n  .border-x-transparent {\n    border-inline-color: transparent;\n  }\n  .border-y-transparent {\n    border-block-color: transparent;\n  }\n  .border-t-transparent {\n    border-top-color: transparent;\n  }\n  .border-r-transparent {\n    border-right-color: transparent;\n  }\n  .border-b-current {\n    border-bottom-color: currentcolor;\n  }\n  .border-b-danger {\n    border-bottom-color: hsl(var(--heroui-danger) / 1);\n  }\n  .border-b-default {\n    border-bottom-color: hsl(var(--heroui-default) / 1);\n  }\n  .border-b-primary {\n    border-bottom-color: var(--primary);\n  }\n  .border-b-secondary {\n    border-bottom-color: var(--secondary);\n  }\n  .border-b-success {\n    border-bottom-color: hsl(var(--heroui-success) / 1);\n  }\n  .border-b-warning {\n    border-bottom-color: hsl(var(--heroui-warning) / 1);\n  }\n  .border-b-white {\n    border-bottom-color: var(--color-white);\n  }\n  .border-l-primary {\n    border-left-color: var(--primary);\n  }\n  .border-l-transparent {\n    border-left-color: transparent;\n  }\n  .\\!bg-danger-50 {\n    background-color: hsl(var(--heroui-danger-50) / 1) !important;\n  }\n  .\\!bg-transparent {\n    background-color: transparent !important;\n  }\n  .bg-\\(--color-bg\\) {\n    background-color: var(--color-bg);\n  }\n  .bg-accent {\n    background-color: var(--accent);\n  }\n  .bg-amber-100 {\n    background-color: var(--color-amber-100);\n  }\n  .bg-amber-500 {\n    background-color: var(--color-amber-500);\n  }\n  .bg-background {\n    background-color: var(--background);\n  }\n  .bg-background\\/10 {\n    background-color: var(--background);\n    @supports (color: color-mix(in lab, red, red)) {\n      background-color: color-mix(in oklab, var(--background) 10%, transparent);\n    }\n  }\n  .bg-background\\/70 {\n    background-color: var(--background);\n    @supports (color: color-mix(in lab, red, red)) {\n      background-color: color-mix(in oklab, var(--background) 70%, transparent);\n    }\n  }\n  .bg-background\\/80 {\n    background-color: var(--background);\n    @supports (color: color-mix(in lab, red, red)) {\n      background-color: color-mix(in oklab, var(--background) 80%, transparent);\n    }\n  }\n  .bg-background\\/95 {\n    background-color: var(--background);\n    @supports (color: color-mix(in lab, red, red)) {\n      background-color: color-mix(in oklab, var(--background) 95%, transparent);\n    }\n  }\n  .bg-black\\/5 {\n    background-color: color-mix(in srgb, #000 5%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      background-color: color-mix(in oklab, var(--color-black) 5%, transparent);\n    }\n  }\n  .bg-black\\/50 {\n    background-color: color-mix(in srgb, #000 50%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      background-color: color-mix(in oklab, var(--color-black) 50%, transparent);\n    }\n  }\n  .bg-blue-50 {\n    background-color: var(--color-blue-50);\n  }\n  .bg-blue-100 {\n    background-color: var(--color-blue-100);\n  }\n  .bg-blue-500 {\n    background-color: var(--color-blue-500);\n  }\n  .bg-blue-500\\/10 {\n    background-color: color-mix(in srgb, oklch(62.3% 0.214 259.815) 10%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      background-color: color-mix(in oklab, var(--color-blue-500) 10%, transparent);\n    }\n  }\n  .bg-blue-600 {\n    background-color: var(--color-blue-600);\n  }\n  .bg-border {\n    background-color: var(--border);\n  }\n  .bg-border\\/60 {\n    background-color: var(--border);\n    @supports (color: color-mix(in lab, red, red)) {\n      background-color: color-mix(in oklab, var(--border) 60%, transparent);\n    }\n  }\n  .bg-card {\n    background-color: var(--card);\n  }\n  .bg-content1 {\n    background-color: hsl(var(--heroui-content1) / 1);\n  }\n  .bg-content3 {\n    background-color: hsl(var(--heroui-content3) / 1);\n  }\n  .bg-current {\n    background-color: currentcolor;\n  }\n  .bg-cyan-100 {\n    background-color: var(--color-cyan-100);\n  }\n  .bg-danger {\n    background-color: hsl(var(--heroui-danger) / 1);\n  }\n  .bg-danger-50 {\n    background-color: hsl(var(--heroui-danger-50) / 1);\n  }\n  .bg-danger-100 {\n    background-color: hsl(var(--heroui-danger-100) / 1);\n  }\n  .bg-danger-400 {\n    background-color: hsl(var(--heroui-danger-400) / 1);\n  }\n  .bg-danger\\/20 {\n    background-color: hsl(var(--heroui-danger) / 1);\n    @supports (color: color-mix(in lab, red, red)) {\n      background-color: color-mix(in oklab, hsl(var(--heroui-danger) / 1) 20%, transparent);\n    }\n  }\n  .bg-default {\n    background-color: hsl(var(--heroui-default) / 1);\n  }\n  .bg-default-50 {\n    background-color: hsl(var(--heroui-default-50) / 1);\n  }\n  .bg-default-100 {\n    background-color: hsl(var(--heroui-default-100) / 1);\n  }\n  .bg-default-200 {\n    background-color: hsl(var(--heroui-default-200) / 1);\n  }\n  .bg-default-300\\/50 {\n    background-color: hsl(var(--heroui-default-300) / 1);\n    @supports (color: color-mix(in lab, red, red)) {\n      background-color: color-mix(in oklab, hsl(var(--heroui-default-300) / 1) 50%, transparent);\n    }\n  }\n  .bg-default-400 {\n    background-color: hsl(var(--heroui-default-400) / 1);\n  }\n  .bg-default-500 {\n    background-color: hsl(var(--heroui-default-500) / 1);\n  }\n  .bg-default-800 {\n    background-color: hsl(var(--heroui-default-800) / 1);\n  }\n  .bg-default\\/40 {\n    background-color: hsl(var(--heroui-default) / 1);\n    @supports (color: color-mix(in lab, red, red)) {\n      background-color: color-mix(in oklab, hsl(var(--heroui-default) / 1) 40%, transparent);\n    }\n  }\n  .bg-destructive {\n    background-color: var(--destructive);\n  }\n  .bg-divider {\n    background-color: hsl(var(--heroui-divider) / 0.15);\n  }\n  .bg-foreground {\n    background-color: var(--foreground);\n  }\n  .bg-foreground-100 {\n    background-color: hsl(var(--heroui-foreground-100) / 1);\n  }\n  .bg-foreground-400 {\n    background-color: hsl(var(--heroui-foreground-400) / 1);\n  }\n  .bg-foreground\\/10 {\n    background-color: var(--foreground);\n    @supports (color: color-mix(in lab, red, red)) {\n      background-color: color-mix(in oklab, var(--foreground) 10%, transparent);\n    }\n  }\n  .bg-gray-50 {\n    background-color: var(--color-gray-50);\n  }\n  .bg-gray-100 {\n    background-color: var(--color-gray-100);\n  }\n  .bg-gray-200 {\n    background-color: var(--color-gray-200);\n  }\n  .bg-gray-300 {\n    background-color: var(--color-gray-300);\n  }\n  .bg-gray-400 {\n    background-color: var(--color-gray-400);\n  }\n  .bg-gray-500 {\n    background-color: var(--color-gray-500);\n  }\n  .bg-green-50 {\n    background-color: var(--color-green-50);\n  }\n  .bg-green-100 {\n    background-color: var(--color-green-100);\n  }\n  .bg-green-500 {\n    background-color: var(--color-green-500);\n  }\n  .bg-green-500\\/10 {\n    background-color: color-mix(in srgb, oklch(72.3% 0.219 149.579) 10%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      background-color: color-mix(in oklab, var(--color-green-500) 10%, transparent);\n    }\n  }\n  .bg-green-600 {\n    background-color: var(--color-green-600);\n  }\n  .bg-indigo-50 {\n    background-color: var(--color-indigo-50);\n  }\n  .bg-indigo-100 {\n    background-color: var(--color-indigo-100);\n  }\n  .bg-muted {\n    background-color: var(--muted);\n  }\n  .bg-muted\\/20 {\n    background-color: var(--muted);\n    @supports (color: color-mix(in lab, red, red)) {\n      background-color: color-mix(in oklab, var(--muted) 20%, transparent);\n    }\n  }\n  .bg-muted\\/30 {\n    background-color: var(--muted);\n    @supports (color: color-mix(in lab, red, red)) {\n      background-color: color-mix(in oklab, var(--muted) 30%, transparent);\n    }\n  }\n  .bg-muted\\/50 {\n    background-color: var(--muted);\n    @supports (color: color-mix(in lab, red, red)) {\n      background-color: color-mix(in oklab, var(--muted) 50%, transparent);\n    }\n  }\n  .bg-orange-100 {\n    background-color: var(--color-orange-100);\n  }\n  .bg-orange-500 {\n    background-color: var(--color-orange-500);\n  }\n  .bg-overlay\\/30 {\n    background-color: hsl(var(--heroui-overlay) / 1);\n    @supports (color: color-mix(in lab, red, red)) {\n      background-color: color-mix(in oklab, hsl(var(--heroui-overlay) / 1) 30%, transparent);\n    }\n  }\n  .bg-overlay\\/50 {\n    background-color: hsl(var(--heroui-overlay) / 1);\n    @supports (color: color-mix(in lab, red, red)) {\n      background-color: color-mix(in oklab, hsl(var(--heroui-overlay) / 1) 50%, transparent);\n    }\n  }\n  .bg-pink-100 {\n    background-color: var(--color-pink-100);\n  }\n  .bg-popover {\n    background-color: var(--popover);\n  }\n  .bg-primary {\n    background-color: var(--primary);\n  }\n  .bg-primary-50 {\n    background-color: hsl(var(--heroui-primary-50) / 1);\n  }\n  .bg-primary-100 {\n    background-color: hsl(var(--heroui-primary-100) / 1);\n  }\n  .bg-primary-400 {\n    background-color: hsl(var(--heroui-primary-400) / 1);\n  }\n  .bg-primary\\/5 {\n    background-color: var(--primary);\n    @supports (color: color-mix(in lab, red, red)) {\n      background-color: color-mix(in oklab, var(--primary) 5%, transparent);\n    }\n  }\n  .bg-primary\\/10 {\n    background-color: var(--primary);\n    @supports (color: color-mix(in lab, red, red)) {\n      background-color: color-mix(in oklab, var(--primary) 10%, transparent);\n    }\n  }\n  .bg-primary\\/20 {\n    background-color: var(--primary);\n    @supports (color: color-mix(in lab, red, red)) {\n      background-color: color-mix(in oklab, var(--primary) 20%, transparent);\n    }\n  }\n  .bg-purple-50 {\n    background-color: var(--color-purple-50);\n  }\n  .bg-purple-100 {\n    background-color: var(--color-purple-100);\n  }\n  .bg-red-50 {\n    background-color: var(--color-red-50);\n  }\n  .bg-red-100 {\n    background-color: var(--color-red-100);\n  }\n  .bg-red-500 {\n    background-color: var(--color-red-500);\n  }\n  .bg-red-500\\/10 {\n    background-color: color-mix(in srgb, oklch(63.7% 0.237 25.331) 10%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      background-color: color-mix(in oklab, var(--color-red-500) 10%, transparent);\n    }\n  }\n  .bg-red-600 {\n    background-color: var(--color-red-600);\n  }\n  .bg-rose-100 {\n    background-color: var(--color-rose-100);\n  }\n  .bg-secondary {\n    background-color: var(--secondary);\n  }\n  .bg-secondary-50 {\n    background-color: hsl(var(--heroui-secondary-50) / 1);\n  }\n  .bg-secondary-100 {\n    background-color: hsl(var(--heroui-secondary-100) / 1);\n  }\n  .bg-secondary-400 {\n    background-color: hsl(var(--heroui-secondary-400) / 1);\n  }\n  .bg-secondary\\/20 {\n    background-color: var(--secondary);\n    @supports (color: color-mix(in lab, red, red)) {\n      background-color: color-mix(in oklab, var(--secondary) 20%, transparent);\n    }\n  }\n  .bg-sidebar {\n    background-color: var(--sidebar);\n  }\n  .bg-sidebar-border {\n    background-color: var(--sidebar-border);\n  }\n  .bg-slate-50 {\n    background-color: var(--color-slate-50);\n  }\n  .bg-slate-100 {\n    background-color: var(--color-slate-100);\n  }\n  .bg-slate-900 {\n    background-color: var(--color-slate-900);\n  }\n  .bg-success {\n    background-color: hsl(var(--heroui-success) / 1);\n  }\n  .bg-success-50 {\n    background-color: hsl(var(--heroui-success-50) / 1);\n  }\n  .bg-success-100 {\n    background-color: hsl(var(--heroui-success-100) / 1);\n  }\n  .bg-success-400 {\n    background-color: hsl(var(--heroui-success-400) / 1);\n  }\n  .bg-success\\/20 {\n    background-color: hsl(var(--heroui-success) / 1);\n    @supports (color: color-mix(in lab, red, red)) {\n      background-color: color-mix(in oklab, hsl(var(--heroui-success) / 1) 20%, transparent);\n    }\n  }\n  .bg-teal-100 {\n    background-color: var(--color-teal-100);\n  }\n  .bg-transparent {\n    background-color: transparent;\n  }\n  .bg-warning {\n    background-color: hsl(var(--heroui-warning) / 1);\n  }\n  .bg-warning-50 {\n    background-color: hsl(var(--heroui-warning-50) / 1);\n  }\n  .bg-warning-100 {\n    background-color: hsl(var(--heroui-warning-100) / 1);\n  }\n  .bg-warning-400 {\n    background-color: hsl(var(--heroui-warning-400) / 1);\n  }\n  .bg-warning\\/20 {\n    background-color: hsl(var(--heroui-warning) / 1);\n    @supports (color: color-mix(in lab, red, red)) {\n      background-color: color-mix(in oklab, hsl(var(--heroui-warning) / 1) 20%, transparent);\n    }\n  }\n  .bg-white {\n    background-color: var(--color-white);\n  }\n  .bg-white\\/80 {\n    background-color: color-mix(in srgb, #fff 80%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      background-color: color-mix(in oklab, var(--color-white) 80%, transparent);\n    }\n  }\n  .bg-yellow-50 {\n    background-color: var(--color-yellow-50);\n  }\n  .bg-yellow-100 {\n    background-color: var(--color-yellow-100);\n  }\n  .bg-yellow-500 {\n    background-color: var(--color-yellow-500);\n  }\n  .bg-yellow-500\\/10 {\n    background-color: color-mix(in srgb, oklch(79.5% 0.184 86.047) 10%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      background-color: color-mix(in oklab, var(--color-yellow-500) 10%, transparent);\n    }\n  }\n  .bg-gradient-to-b {\n    --tw-gradient-position: to bottom in oklab;\n    background-image: linear-gradient(var(--tw-gradient-stops));\n  }\n  .bg-gradient-to-r {\n    --tw-gradient-position: to right in oklab;\n    background-image: linear-gradient(var(--tw-gradient-stops));\n  }\n  .bg-img-inherit {\n    background-image: inherit;\n  }\n  .bg-stripe-gradient-danger {\n    background-image: linear-gradient(45deg,  hsl(var(--heroui-danger-200)) 25%,  hsl(var(--heroui-danger)) 25%,  hsl(var(--heroui-danger)) 50%,  hsl(var(--heroui-danger-200)) 50%,  hsl(var(--heroui-danger-200)) 75%,  hsl(var(--heroui-danger)) 75%,  hsl(var(--heroui-danger)));\n  }\n  .bg-stripe-gradient-default {\n    background-image: linear-gradient(45deg,  hsl(var(--heroui-default-200)) 25%,  hsl(var(--heroui-default-400)) 25%,  hsl(var(--heroui-default-400)) 50%,  hsl(var(--heroui-default-200)) 50%,  hsl(var(--heroui-default-200)) 75%,  hsl(var(--heroui-default-400)) 75%,  hsl(var(--heroui-default-400)));\n  }\n  .bg-stripe-gradient-primary {\n    background-image: linear-gradient(45deg,  hsl(var(--heroui-primary-200)) 25%,  hsl(var(--heroui-primary)) 25%,  hsl(var(--heroui-primary)) 50%,  hsl(var(--heroui-primary-200)) 50%,  hsl(var(--heroui-primary-200)) 75%,  hsl(var(--heroui-primary)) 75%,  hsl(var(--heroui-primary)));\n  }\n  .bg-stripe-gradient-secondary {\n    background-image: linear-gradient(45deg,  hsl(var(--heroui-secondary-200)) 25%,  hsl(var(--heroui-secondary)) 25%,  hsl(var(--heroui-secondary)) 50%,  hsl(var(--heroui-secondary-200)) 50%,  hsl(var(--heroui-secondary-200)) 75%,  hsl(var(--heroui-secondary)) 75%,  hsl(var(--heroui-secondary)));\n  }\n  .bg-stripe-gradient-success {\n    background-image: linear-gradient(45deg,  hsl(var(--heroui-success-200)) 25%,  hsl(var(--heroui-success)) 25%,  hsl(var(--heroui-success)) 50%,  hsl(var(--heroui-success-200)) 50%,  hsl(var(--heroui-success-200)) 75%,  hsl(var(--heroui-success)) 75%,  hsl(var(--heroui-success)));\n  }\n  .bg-stripe-gradient-warning {\n    background-image: linear-gradient(45deg,  hsl(var(--heroui-warning-200)) 25%,  hsl(var(--heroui-warning)) 25%,  hsl(var(--heroui-warning)) 50%,  hsl(var(--heroui-warning-200)) 50%,  hsl(var(--heroui-warning-200)) 75%,  hsl(var(--heroui-warning)) 75%,  hsl(var(--heroui-warning)));\n  }\n  .from-blue-50 {\n    --tw-gradient-from: var(--color-blue-50);\n    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n  }\n  .from-green-50 {\n    --tw-gradient-from: var(--color-green-50);\n    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n  }\n  .from-red-50 {\n    --tw-gradient-from: var(--color-red-50);\n    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n  }\n  .from-transparent {\n    --tw-gradient-from: transparent;\n    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n  }\n  .via-transparent {\n    --tw-gradient-via: transparent;\n    --tw-gradient-via-stops: var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-via) var(--tw-gradient-via-position), var(--tw-gradient-to) var(--tw-gradient-to-position);\n    --tw-gradient-stops: var(--tw-gradient-via-stops);\n  }\n  .to-current {\n    --tw-gradient-to: currentcolor;\n    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n  }\n  .to-danger {\n    --tw-gradient-to: hsl(var(--heroui-danger) / 1);\n    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n  }\n  .to-default {\n    --tw-gradient-to: hsl(var(--heroui-default) / 1);\n    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n  }\n  .to-emerald-50 {\n    --tw-gradient-to: var(--color-emerald-50);\n    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n  }\n  .to-indigo-50 {\n    --tw-gradient-to: var(--color-indigo-50);\n    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n  }\n  .to-primary {\n    --tw-gradient-to: var(--primary);\n    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n  }\n  .to-rose-50 {\n    --tw-gradient-to: var(--color-rose-50);\n    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n  }\n  .to-secondary {\n    --tw-gradient-to: var(--secondary);\n    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n  }\n  .to-success {\n    --tw-gradient-to: hsl(var(--heroui-success) / 1);\n    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n  }\n  .to-warning {\n    --tw-gradient-to: hsl(var(--heroui-warning) / 1);\n    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n  }\n  .to-white {\n    --tw-gradient-to: var(--color-white);\n    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n  }\n  .\\[mask-image\\:linear-gradient\\(\\#000\\,\\#000\\,transparent_0\\,\\#000_var\\(--scroll-shadow-size\\)\\,\\#000_calc\\(100\\%_-_var\\(--scroll-shadow-size\\)\\)\\,transparent\\)\\] {\n    mask-image: linear-gradient(#000,#000,transparent 0,#000 var(--scroll-shadow-size),#000 calc(100% - var(--scroll-shadow-size)),transparent);\n  }\n  .bg-contain {\n    background-size: contain;\n  }\n  .bg-clip-inherit {\n    background-clip: inherit;\n  }\n  .bg-clip-text {\n    background-clip: text;\n  }\n  .bg-center {\n    background-position: center;\n  }\n  .bg-no-repeat {\n    background-repeat: no-repeat;\n  }\n  .fill-current {\n    fill: currentcolor;\n  }\n  .fill-green-500 {\n    fill: var(--color-green-500);\n  }\n  .fill-primary {\n    fill: var(--primary);\n  }\n  .stroke-current {\n    stroke: currentcolor;\n  }\n  .stroke-default-300\\/50 {\n    stroke: hsl(var(--heroui-default-300) / 1);\n    @supports (color: color-mix(in lab, red, red)) {\n      stroke: color-mix(in oklab, hsl(var(--heroui-default-300) / 1) 50%, transparent);\n    }\n  }\n  .object-contain {\n    object-fit: contain;\n  }\n  .object-cover {\n    object-fit: cover;\n  }\n  .object-fill {\n    object-fit: fill;\n  }\n  .object-none {\n    object-fit: none;\n  }\n  .object-scale-down {\n    object-fit: scale-down;\n  }\n  .object-center {\n    object-position: center;\n  }\n  .object-left {\n    object-position: left;\n  }\n  .p-0 {\n    padding: calc(var(--spacing) * 0);\n  }\n  .p-0\\.5 {\n    padding: calc(var(--spacing) * 0.5);\n  }\n  .p-1 {\n    padding: calc(var(--spacing) * 1);\n  }\n  .p-2 {\n    padding: calc(var(--spacing) * 2);\n  }\n  .p-2\\.5 {\n    padding: calc(var(--spacing) * 2.5);\n  }\n  .p-3 {\n    padding: calc(var(--spacing) * 3);\n  }\n  .p-4 {\n    padding: calc(var(--spacing) * 4);\n  }\n  .p-5 {\n    padding: calc(var(--spacing) * 5);\n  }\n  .p-6 {\n    padding: calc(var(--spacing) * 6);\n  }\n  .p-8 {\n    padding: calc(var(--spacing) * 8);\n  }\n  .p-\\[3px\\] {\n    padding: 3px;\n  }\n  .p-px {\n    padding: 1px;\n  }\n  .\\!px-1 {\n    padding-inline: calc(var(--spacing) * 1) !important;\n  }\n  .px-0 {\n    padding-inline: calc(var(--spacing) * 0);\n  }\n  .px-0\\.5 {\n    padding-inline: calc(var(--spacing) * 0.5);\n  }\n  .px-1 {\n    padding-inline: calc(var(--spacing) * 1);\n  }\n  .px-1\\.5 {\n    padding-inline: calc(var(--spacing) * 1.5);\n  }\n  .px-2 {\n    padding-inline: calc(var(--spacing) * 2);\n  }\n  .px-2\\.5 {\n    padding-inline: calc(var(--spacing) * 2.5);\n  }\n  .px-3 {\n    padding-inline: calc(var(--spacing) * 3);\n  }\n  .px-4 {\n    padding-inline: calc(var(--spacing) * 4);\n  }\n  .px-5 {\n    padding-inline: calc(var(--spacing) * 5);\n  }\n  .px-6 {\n    padding-inline: calc(var(--spacing) * 6);\n  }\n  .px-8 {\n    padding-inline: calc(var(--spacing) * 8);\n  }\n  .py-0 {\n    padding-block: calc(var(--spacing) * 0);\n  }\n  .py-0\\.5 {\n    padding-block: calc(var(--spacing) * 0.5);\n  }\n  .py-1 {\n    padding-block: calc(var(--spacing) * 1);\n  }\n  .py-1\\.5 {\n    padding-block: calc(var(--spacing) * 1.5);\n  }\n  .py-2 {\n    padding-block: calc(var(--spacing) * 2);\n  }\n  .py-2\\.5 {\n    padding-block: calc(var(--spacing) * 2.5);\n  }\n  .py-3 {\n    padding-block: calc(var(--spacing) * 3);\n  }\n  .py-4 {\n    padding-block: calc(var(--spacing) * 4);\n  }\n  .py-6 {\n    padding-block: calc(var(--spacing) * 6);\n  }\n  .py-8 {\n    padding-block: calc(var(--spacing) * 8);\n  }\n  .py-10 {\n    padding-block: calc(var(--spacing) * 10);\n  }\n  .py-12 {\n    padding-block: calc(var(--spacing) * 12);\n  }\n  .py-16 {\n    padding-block: calc(var(--spacing) * 16);\n  }\n  .ps-1 {\n    padding-inline-start: calc(var(--spacing) * 1);\n  }\n  .ps-2 {\n    padding-inline-start: calc(var(--spacing) * 2);\n  }\n  .ps-4 {\n    padding-inline-start: calc(var(--spacing) * 4);\n  }\n  .ps-\\[calc\\(var\\(--spacing\\)_\\*_4\\.5_-_1px\\)\\] {\n    padding-inline-start: calc(var(--spacing) * 4.5 - 1px);\n  }\n  .pe-2 {\n    padding-inline-end: calc(var(--spacing) * 2);\n  }\n  .pe-6 {\n    padding-inline-end: calc(var(--spacing) * 6);\n  }\n  .pt-0 {\n    padding-top: calc(var(--spacing) * 0);\n  }\n  .pt-1 {\n    padding-top: calc(var(--spacing) * 1);\n  }\n  .pt-2 {\n    padding-top: calc(var(--spacing) * 2);\n  }\n  .pt-3 {\n    padding-top: calc(var(--spacing) * 3);\n  }\n  .pt-4 {\n    padding-top: calc(var(--spacing) * 4);\n  }\n  .pt-16 {\n    padding-top: calc(var(--spacing) * 16);\n  }\n  .pr-0 {\n    padding-right: calc(var(--spacing) * 0);\n  }\n  .pr-0\\.5 {\n    padding-right: calc(var(--spacing) * 0.5);\n  }\n  .pr-1 {\n    padding-right: calc(var(--spacing) * 1);\n  }\n  .pr-2 {\n    padding-right: calc(var(--spacing) * 2);\n  }\n  .pr-2\\.5 {\n    padding-right: calc(var(--spacing) * 2.5);\n  }\n  .pr-3 {\n    padding-right: calc(var(--spacing) * 3);\n  }\n  .pr-4 {\n    padding-right: calc(var(--spacing) * 4);\n  }\n  .pr-8 {\n    padding-right: calc(var(--spacing) * 8);\n  }\n  .pr-10 {\n    padding-right: calc(var(--spacing) * 10);\n  }\n  .\\!pb-0 {\n    padding-bottom: calc(var(--spacing) * 0) !important;\n  }\n  .pb-0 {\n    padding-bottom: calc(var(--spacing) * 0);\n  }\n  .pb-0\\.5 {\n    padding-bottom: calc(var(--spacing) * 0.5);\n  }\n  .pb-1 {\n    padding-bottom: calc(var(--spacing) * 1);\n  }\n  .pb-1\\.5 {\n    padding-bottom: calc(var(--spacing) * 1.5);\n  }\n  .pb-2 {\n    padding-bottom: calc(var(--spacing) * 2);\n  }\n  .pb-3 {\n    padding-bottom: calc(var(--spacing) * 3);\n  }\n  .pb-4 {\n    padding-bottom: calc(var(--spacing) * 4);\n  }\n  .pl-0\\.5 {\n    padding-left: calc(var(--spacing) * 0.5);\n  }\n  .pl-1 {\n    padding-left: calc(var(--spacing) * 1);\n  }\n  .pl-2 {\n    padding-left: calc(var(--spacing) * 2);\n  }\n  .pl-3 {\n    padding-left: calc(var(--spacing) * 3);\n  }\n  .pl-4 {\n    padding-left: calc(var(--spacing) * 4);\n  }\n  .pl-5 {\n    padding-left: calc(var(--spacing) * 5);\n  }\n  .pl-7 {\n    padding-left: calc(var(--spacing) * 7);\n  }\n  .pl-8 {\n    padding-left: calc(var(--spacing) * 8);\n  }\n  .pl-\\[1px\\] {\n    padding-left: 1px;\n  }\n  .text-center {\n    text-align: center;\n  }\n  .text-end {\n    text-align: end;\n  }\n  .text-left {\n    text-align: left;\n  }\n  .text-right {\n    text-align: right;\n  }\n  .text-start {\n    text-align: start;\n  }\n  .align-middle {\n    vertical-align: middle;\n  }\n  .font-mono {\n    font-family: var(--font-geist-mono);\n  }\n  .font-sans {\n    font-family: var(--font-geist-sans);\n  }\n  .text-2xl {\n    font-size: var(--text-2xl);\n    line-height: var(--tw-leading, var(--text-2xl--line-height));\n  }\n  .text-3xl {\n    font-size: var(--text-3xl);\n    line-height: var(--tw-leading, var(--text-3xl--line-height));\n  }\n  .text-6xl {\n    font-size: var(--text-6xl);\n    line-height: var(--tw-leading, var(--text-6xl--line-height));\n  }\n  .text-base {\n    font-size: var(--text-base);\n    line-height: var(--tw-leading, var(--text-base--line-height));\n  }\n  .text-large {\n    font-size: var(--heroui-font-size-large);\n    line-height: var(--heroui-line-height-large);\n  }\n  .text-lg {\n    font-size: var(--text-lg);\n    line-height: var(--tw-leading, var(--text-lg--line-height));\n  }\n  .text-medium {\n    font-size: var(--heroui-font-size-medium);\n    line-height: var(--heroui-line-height-medium);\n  }\n  .text-sm {\n    font-size: var(--text-sm);\n    line-height: var(--tw-leading, var(--text-sm--line-height));\n  }\n  .text-small {\n    font-size: var(--heroui-font-size-small);\n    line-height: var(--heroui-line-height-small);\n  }\n  .text-tiny {\n    font-size: var(--heroui-font-size-tiny);\n    line-height: var(--heroui-line-height-tiny);\n  }\n  .text-xl {\n    font-size: var(--text-xl);\n    line-height: var(--tw-leading, var(--text-xl--line-height));\n  }\n  .text-xs {\n    font-size: var(--text-xs);\n    line-height: var(--tw-leading, var(--text-xs--line-height));\n  }\n  .text-\\[0\\.5rem\\] {\n    font-size: 0.5rem;\n  }\n  .text-\\[0\\.6rem\\] {\n    font-size: 0.6rem;\n  }\n  .text-\\[0\\.8rem\\] {\n    font-size: 0.8rem;\n  }\n  .text-\\[0\\.55rem\\] {\n    font-size: 0.55rem;\n  }\n  .text-\\[10px\\] {\n    font-size: 10px;\n  }\n  .text-\\[100\\%\\] {\n    font-size: 100%;\n  }\n  .leading-5 {\n    --tw-leading: calc(var(--spacing) * 5);\n    line-height: calc(var(--spacing) * 5);\n  }\n  .leading-\\[1\\.15\\] {\n    --tw-leading: 1.15;\n    line-height: 1.15;\n  }\n  .leading-\\[32px\\] {\n    --tw-leading: 32px;\n    line-height: 32px;\n  }\n  .leading-none {\n    --tw-leading: 1;\n    line-height: 1;\n  }\n  .leading-relaxed {\n    --tw-leading: var(--leading-relaxed);\n    line-height: var(--leading-relaxed);\n  }\n  .leading-tight {\n    --tw-leading: var(--leading-tight);\n    line-height: var(--leading-tight);\n  }\n  .leading-inherit {\n    line-height: inherit;\n  }\n  .font-bold {\n    --tw-font-weight: var(--font-weight-bold);\n    font-weight: var(--font-weight-bold);\n  }\n  .font-extralight {\n    --tw-font-weight: var(--font-weight-extralight);\n    font-weight: var(--font-weight-extralight);\n  }\n  .font-medium {\n    --tw-font-weight: var(--font-weight-medium);\n    font-weight: var(--font-weight-medium);\n  }\n  .font-normal {\n    --tw-font-weight: var(--font-weight-normal);\n    font-weight: var(--font-weight-normal);\n  }\n  .font-semibold {\n    --tw-font-weight: var(--font-weight-semibold);\n    font-weight: var(--font-weight-semibold);\n  }\n  .tracking-tight {\n    --tw-tracking: var(--tracking-tight);\n    letter-spacing: var(--tracking-tight);\n  }\n  .tracking-wider {\n    --tw-tracking: var(--tracking-wider);\n    letter-spacing: var(--tracking-wider);\n  }\n  .tracking-widest {\n    --tw-tracking: var(--tracking-widest);\n    letter-spacing: var(--tracking-widest);\n  }\n  .text-balance {\n    text-wrap: balance;\n  }\n  .break-words {\n    overflow-wrap: break-word;\n  }\n  .break-all {\n    word-break: break-all;\n  }\n  .text-ellipsis {\n    text-overflow: ellipsis;\n  }\n  .whitespace-normal {\n    white-space: normal;\n  }\n  .whitespace-nowrap {\n    white-space: nowrap;\n  }\n  .whitespace-pre-wrap {\n    white-space: pre-wrap;\n  }\n  .\\!text-danger {\n    color: hsl(var(--heroui-danger) / 1) !important;\n  }\n  .text-accent-foreground {\n    color: var(--accent-foreground);\n  }\n  .text-amber-500 {\n    color: var(--color-amber-500);\n  }\n  .text-amber-600 {\n    color: var(--color-amber-600);\n  }\n  .text-amber-800 {\n    color: var(--color-amber-800);\n  }\n  .text-background {\n    color: var(--background);\n  }\n  .text-black {\n    color: var(--color-black);\n  }\n  .text-blue-500 {\n    color: var(--color-blue-500);\n  }\n  .text-blue-600 {\n    color: var(--color-blue-600);\n  }\n  .text-blue-700 {\n    color: var(--color-blue-700);\n  }\n  .text-blue-800 {\n    color: var(--color-blue-800);\n  }\n  .text-card-foreground {\n    color: var(--card-foreground);\n  }\n  .text-current {\n    color: currentcolor;\n  }\n  .text-cyan-800 {\n    color: var(--color-cyan-800);\n  }\n  .text-danger {\n    color: hsl(var(--heroui-danger) / 1);\n  }\n  .text-danger-300 {\n    color: hsl(var(--heroui-danger-300) / 1);\n  }\n  .text-danger-400 {\n    color: hsl(var(--heroui-danger-400) / 1);\n  }\n  .text-danger-500 {\n    color: hsl(var(--heroui-danger-500) / 1);\n  }\n  .text-danger-600 {\n    color: hsl(var(--heroui-danger-600) / 1);\n  }\n  .text-danger-foreground {\n    color: hsl(var(--heroui-danger-foreground) / 1);\n  }\n  .text-danger\\/80 {\n    color: hsl(var(--heroui-danger) / 1);\n    @supports (color: color-mix(in lab, red, red)) {\n      color: color-mix(in oklab, hsl(var(--heroui-danger) / 1) 80%, transparent);\n    }\n  }\n  .text-default {\n    color: hsl(var(--heroui-default) / 1);\n  }\n  .text-default-400 {\n    color: hsl(var(--heroui-default-400) / 1);\n  }\n  .text-default-500 {\n    color: hsl(var(--heroui-default-500) / 1);\n  }\n  .text-default-600 {\n    color: hsl(var(--heroui-default-600) / 1);\n  }\n  .text-default-700 {\n    color: hsl(var(--heroui-default-700) / 1);\n  }\n  .text-default-foreground {\n    color: hsl(var(--heroui-default-foreground) / 1);\n  }\n  .text-destructive {\n    color: var(--destructive);\n  }\n  .text-foreground {\n    color: var(--foreground);\n  }\n  .text-foreground-400 {\n    color: hsl(var(--heroui-foreground-400) / 1);\n  }\n  .text-foreground-500 {\n    color: hsl(var(--heroui-foreground-500) / 1);\n  }\n  .text-foreground-600 {\n    color: hsl(var(--heroui-foreground-600) / 1);\n  }\n  .text-foreground\\/50 {\n    color: var(--foreground);\n    @supports (color: color-mix(in lab, red, red)) {\n      color: color-mix(in oklab, var(--foreground) 50%, transparent);\n    }\n  }\n  .text-gray-300 {\n    color: var(--color-gray-300);\n  }\n  .text-gray-400 {\n    color: var(--color-gray-400);\n  }\n  .text-gray-500 {\n    color: var(--color-gray-500);\n  }\n  .text-gray-600 {\n    color: var(--color-gray-600);\n  }\n  .text-gray-700 {\n    color: var(--color-gray-700);\n  }\n  .text-gray-800 {\n    color: var(--color-gray-800);\n  }\n  .text-gray-900 {\n    color: var(--color-gray-900);\n  }\n  .text-green-500 {\n    color: var(--color-green-500);\n  }\n  .text-green-600 {\n    color: var(--color-green-600);\n  }\n  .text-green-700 {\n    color: var(--color-green-700);\n  }\n  .text-green-800 {\n    color: var(--color-green-800);\n  }\n  .text-indigo-500 {\n    color: var(--color-indigo-500);\n  }\n  .text-indigo-600 {\n    color: var(--color-indigo-600);\n  }\n  .text-indigo-800 {\n    color: var(--color-indigo-800);\n  }\n  .text-inherit {\n    color: inherit;\n  }\n  .text-muted-foreground {\n    color: var(--muted-foreground);\n  }\n  .text-muted-foreground\\/70 {\n    color: var(--muted-foreground);\n    @supports (color: color-mix(in lab, red, red)) {\n      color: color-mix(in oklab, var(--muted-foreground) 70%, transparent);\n    }\n  }\n  .text-orange-500 {\n    color: var(--color-orange-500);\n  }\n  .text-orange-600 {\n    color: var(--color-orange-600);\n  }\n  .text-orange-800 {\n    color: var(--color-orange-800);\n  }\n  .text-pink-500 {\n    color: var(--color-pink-500);\n  }\n  .text-pink-800 {\n    color: var(--color-pink-800);\n  }\n  .text-popover-foreground {\n    color: var(--popover-foreground);\n  }\n  .text-primary {\n    color: var(--primary);\n  }\n  .text-primary-300 {\n    color: hsl(var(--heroui-primary-300) / 1);\n  }\n  .text-primary-400 {\n    color: hsl(var(--heroui-primary-400) / 1);\n  }\n  .text-primary-500 {\n    color: hsl(var(--heroui-primary-500) / 1);\n  }\n  .text-primary-600 {\n    color: hsl(var(--heroui-primary-600) / 1);\n  }\n  .text-primary-foreground {\n    color: var(--primary-foreground);\n  }\n  .text-primary\\/80 {\n    color: var(--primary);\n    @supports (color: color-mix(in lab, red, red)) {\n      color: color-mix(in oklab, var(--primary) 80%, transparent);\n    }\n  }\n  .text-purple-500 {\n    color: var(--color-purple-500);\n  }\n  .text-purple-600 {\n    color: var(--color-purple-600);\n  }\n  .text-purple-800 {\n    color: var(--color-purple-800);\n  }\n  .text-red-500 {\n    color: var(--color-red-500);\n  }\n  .text-red-600 {\n    color: var(--color-red-600);\n  }\n  .text-red-700 {\n    color: var(--color-red-700);\n  }\n  .text-red-800 {\n    color: var(--color-red-800);\n  }\n  .text-rose-800 {\n    color: var(--color-rose-800);\n  }\n  .text-secondary {\n    color: var(--secondary);\n  }\n  .text-secondary-300 {\n    color: hsl(var(--heroui-secondary-300) / 1);\n  }\n  .text-secondary-400 {\n    color: hsl(var(--heroui-secondary-400) / 1);\n  }\n  .text-secondary-500 {\n    color: hsl(var(--heroui-secondary-500) / 1);\n  }\n  .text-secondary-600 {\n    color: hsl(var(--heroui-secondary-600) / 1);\n  }\n  .text-secondary-foreground {\n    color: var(--secondary-foreground);\n  }\n  .text-secondary\\/80 {\n    color: var(--secondary);\n    @supports (color: color-mix(in lab, red, red)) {\n      color: color-mix(in oklab, var(--secondary) 80%, transparent);\n    }\n  }\n  .text-sidebar-foreground {\n    color: var(--sidebar-foreground);\n  }\n  .text-sidebar-foreground\\/70 {\n    color: var(--sidebar-foreground);\n    @supports (color: color-mix(in lab, red, red)) {\n      color: color-mix(in oklab, var(--sidebar-foreground) 70%, transparent);\n    }\n  }\n  .text-sidebar-primary-foreground {\n    color: var(--sidebar-primary-foreground);\n  }\n  .text-slate-800 {\n    color: var(--color-slate-800);\n  }\n  .text-success {\n    color: hsl(var(--heroui-success) / 1);\n  }\n  .text-success-400 {\n    color: hsl(var(--heroui-success-400) / 1);\n  }\n  .text-success-500 {\n    color: hsl(var(--heroui-success-500) / 1);\n  }\n  .text-success-600 {\n    color: hsl(var(--heroui-success-600) / 1);\n  }\n  .text-success-700 {\n    color: hsl(var(--heroui-success-700) / 1);\n  }\n  .text-success-foreground {\n    color: hsl(var(--heroui-success-foreground) / 1);\n  }\n  .text-success\\/80 {\n    color: hsl(var(--heroui-success) / 1);\n    @supports (color: color-mix(in lab, red, red)) {\n      color: color-mix(in oklab, hsl(var(--heroui-success) / 1) 80%, transparent);\n    }\n  }\n  .text-teal-800 {\n    color: var(--color-teal-800);\n  }\n  .text-transparent {\n    color: transparent;\n  }\n  .text-warning {\n    color: hsl(var(--heroui-warning) / 1);\n  }\n  .text-warning-400 {\n    color: hsl(var(--heroui-warning-400) / 1);\n  }\n  .text-warning-500 {\n    color: hsl(var(--heroui-warning-500) / 1);\n  }\n  .text-warning-600 {\n    color: hsl(var(--heroui-warning-600) / 1);\n  }\n  .text-warning-700 {\n    color: hsl(var(--heroui-warning-700) / 1);\n  }\n  .text-warning-foreground {\n    color: hsl(var(--heroui-warning-foreground) / 1);\n  }\n  .text-warning\\/80 {\n    color: hsl(var(--heroui-warning) / 1);\n    @supports (color: color-mix(in lab, red, red)) {\n      color: color-mix(in oklab, hsl(var(--heroui-warning) / 1) 80%, transparent);\n    }\n  }\n  .text-white {\n    color: var(--color-white);\n  }\n  .text-yellow-400 {\n    color: var(--color-yellow-400);\n  }\n  .text-yellow-500 {\n    color: var(--color-yellow-500);\n  }\n  .text-yellow-600 {\n    color: var(--color-yellow-600);\n  }\n  .text-yellow-700 {\n    color: var(--color-yellow-700);\n  }\n  .text-yellow-800 {\n    color: var(--color-yellow-800);\n  }\n  .capitalize {\n    text-transform: capitalize;\n  }\n  .uppercase {\n    text-transform: uppercase;\n  }\n  .italic {\n    font-style: italic;\n  }\n  .tabular-nums {\n    --tw-numeric-spacing: tabular-nums;\n    font-variant-numeric: var(--tw-ordinal,) var(--tw-slashed-zero,) var(--tw-numeric-figure,) var(--tw-numeric-spacing,) var(--tw-numeric-fraction,);\n  }\n  .no-underline {\n    text-decoration-line: none;\n  }\n  .underline {\n    text-decoration-line: underline;\n  }\n  .underline-offset-4 {\n    text-underline-offset: 4px;\n  }\n  .antialiased {\n    -webkit-font-smoothing: antialiased;\n    -moz-osx-font-smoothing: grayscale;\n  }\n  .subpixel-antialiased {\n    -webkit-font-smoothing: auto;\n    -moz-osx-font-smoothing: auto;\n  }\n  .dark {\n    color-scheme: dark;\n    --heroui-background: 0 0% 0%;\n    --heroui-foreground-50: 240 5.88% 10%;\n    --heroui-foreground-100: 240 3.7% 15.88%;\n    --heroui-foreground-200: 240 5.26% 26.08%;\n    --heroui-foreground-300: 240 5.2% 33.92%;\n    --heroui-foreground-400: 240 3.83% 46.08%;\n    --heroui-foreground-500: 240 5.03% 64.9%;\n    --heroui-foreground-600: 240 4.88% 83.92%;\n    --heroui-foreground-700: 240 5.88% 90%;\n    --heroui-foreground-800: 240 4.76% 95.88%;\n    --heroui-foreground-900: 0 0% 98.04%;\n    --heroui-foreground: 210 5.56% 92.94%;\n    --heroui-focus: 212.01999999999998 100% 46.67%;\n    --heroui-overlay: 0 0% 0%;\n    --heroui-divider: 0 0% 100%;\n    --heroui-content1: 240 5.88% 10%;\n    --heroui-content1-foreground: 0 0% 98.04%;\n    --heroui-content2: 240 3.7% 15.88%;\n    --heroui-content2-foreground: 240 4.76% 95.88%;\n    --heroui-content3: 240 5.26% 26.08%;\n    --heroui-content3-foreground: 240 5.88% 90%;\n    --heroui-content4: 240 5.2% 33.92%;\n    --heroui-content4-foreground: 240 4.88% 83.92%;\n    --heroui-default-50: 240 5.88% 10%;\n    --heroui-default-100: 240 3.7% 15.88%;\n    --heroui-default-200: 240 5.26% 26.08%;\n    --heroui-default-300: 240 5.2% 33.92%;\n    --heroui-default-400: 240 3.83% 46.08%;\n    --heroui-default-500: 240 5.03% 64.9%;\n    --heroui-default-600: 240 4.88% 83.92%;\n    --heroui-default-700: 240 5.88% 90%;\n    --heroui-default-800: 240 4.76% 95.88%;\n    --heroui-default-900: 0 0% 98.04%;\n    --heroui-default-foreground: 0 0% 100%;\n    --heroui-default: 240 5.26% 26.08%;\n    --heroui-primary-50: 211.84000000000003 100% 9.61%;\n    --heroui-primary-100: 211.84000000000003 100% 19.22%;\n    --heroui-primary-200: 212.24 100% 28.82%;\n    --heroui-primary-300: 212.14 100% 38.43%;\n    --heroui-primary-400: 212.01999999999998 100% 46.67%;\n    --heroui-primary-500: 212.14 92.45% 58.43%;\n    --heroui-primary-600: 212.24 92.45% 68.82%;\n    --heroui-primary-700: 211.84000000000003 92.45% 79.22%;\n    --heroui-primary-800: 211.84000000000003 92.45% 89.61%;\n    --heroui-primary-900: 212.5 92.31% 94.9%;\n    --heroui-primary-foreground: 0 0% 100%;\n    --heroui-primary: 212.01999999999998 100% 46.67%;\n    --heroui-secondary-50: 270 66.67% 9.41%;\n    --heroui-secondary-100: 270 66.67% 18.82%;\n    --heroui-secondary-200: 270 66.67% 28.24%;\n    --heroui-secondary-300: 270 66.67% 37.65%;\n    --heroui-secondary-400: 270 66.67% 47.06%;\n    --heroui-secondary-500: 270 59.26% 57.65%;\n    --heroui-secondary-600: 270 59.26% 68.24%;\n    --heroui-secondary-700: 270 59.26% 78.82%;\n    --heroui-secondary-800: 270 59.26% 89.41%;\n    --heroui-secondary-900: 270 61.54% 94.9%;\n    --heroui-secondary-foreground: 0 0% 100%;\n    --heroui-secondary: 270 59.26% 57.65%;\n    --heroui-success-50: 145.71000000000004 77.78% 8.82%;\n    --heroui-success-100: 146.2 79.78% 17.45%;\n    --heroui-success-200: 145.78999999999996 79.26% 26.47%;\n    --heroui-success-300: 146.01 79.89% 35.1%;\n    --heroui-success-400: 145.96000000000004 79.46% 43.92%;\n    --heroui-success-500: 146.01 62.45% 55.1%;\n    --heroui-success-600: 145.78999999999996 62.57% 66.47%;\n    --heroui-success-700: 146.2 61.74% 77.45%;\n    --heroui-success-800: 145.71000000000004 61.4% 88.82%;\n    --heroui-success-900: 146.66999999999996 64.29% 94.51%;\n    --heroui-success-foreground: 0 0% 0%;\n    --heroui-success: 145.96000000000004 79.46% 43.92%;\n    --heroui-warning-50: 37.139999999999986 75% 10.98%;\n    --heroui-warning-100: 37.139999999999986 75% 21.96%;\n    --heroui-warning-200: 36.95999999999998 73.96% 33.14%;\n    --heroui-warning-300: 37.00999999999999 74.22% 44.12%;\n    --heroui-warning-400: 37.02999999999997 91.27% 55.1%;\n    --heroui-warning-500: 37.00999999999999 91.26% 64.12%;\n    --heroui-warning-600: 36.95999999999998 91.24% 73.14%;\n    --heroui-warning-700: 37.139999999999986 91.3% 81.96%;\n    --heroui-warning-800: 37.139999999999986 91.3% 90.98%;\n    --heroui-warning-900: 54.55000000000001 91.67% 95.29%;\n    --heroui-warning-foreground: 0 0% 0%;\n    --heroui-warning: 37.02999999999997 91.27% 55.1%;\n    --heroui-danger-50: 340 84.91% 10.39%;\n    --heroui-danger-100: 339.3299999999999 86.54% 20.39%;\n    --heroui-danger-200: 339.11 85.99% 30.78%;\n    --heroui-danger-300: 339 86.54% 40.78%;\n    --heroui-danger-400: 339.20000000000005 90.36% 51.18%;\n    --heroui-danger-500: 339 90% 60.78%;\n    --heroui-danger-600: 339.11 90.6% 70.78%;\n    --heroui-danger-700: 339.3299999999999 90% 80.39%;\n    --heroui-danger-800: 340 91.84% 90.39%;\n    --heroui-danger-900: 339.13 92% 95.1%;\n    --heroui-danger-foreground: 0 0% 100%;\n    --heroui-danger: 339.20000000000005 90.36% 51.18%;\n    --heroui-divider-weight: 1px;\n    --heroui-disabled-opacity: .5;\n    --heroui-font-size-tiny: 0.75rem;\n    --heroui-font-size-small: 0.875rem;\n    --heroui-font-size-medium: 1rem;\n    --heroui-font-size-large: 1.125rem;\n    --heroui-line-height-tiny: 1rem;\n    --heroui-line-height-small: 1.25rem;\n    --heroui-line-height-medium: 1.5rem;\n    --heroui-line-height-large: 1.75rem;\n    --heroui-radius-small: 8px;\n    --heroui-radius-medium: 12px;\n    --heroui-radius-large: 14px;\n    --heroui-border-width-small: 1px;\n    --heroui-border-width-medium: 2px;\n    --heroui-border-width-large: 3px;\n    --heroui-box-shadow-small: 0px 0px 5px 0px rgb(0 0 0 / 0.05), 0px 2px 10px 0px rgb(0 0 0 / 0.2), inset 0px 0px 1px 0px rgb(255 255 255 / 0.15);\n    --heroui-box-shadow-medium: 0px 0px 15px 0px rgb(0 0 0 / 0.06), 0px 2px 30px 0px rgb(0 0 0 / 0.22), inset 0px 0px 1px 0px rgb(255 255 255 / 0.15);\n    --heroui-box-shadow-large: 0px 0px 30px 0px rgb(0 0 0 / 0.07), 0px 30px 60px 0px rgb(0 0 0 / 0.26), inset 0px 0px 1px 0px rgb(255 255 255 / 0.15);\n    --heroui-hover-opacity: .9;\n  }\n  .light {\n    color-scheme: light;\n    --heroui-background: 0 0% 100%;\n    --heroui-foreground-50: 0 0% 98.04%;\n    --heroui-foreground-100: 240 4.76% 95.88%;\n    --heroui-foreground-200: 240 5.88% 90%;\n    --heroui-foreground-300: 240 4.88% 83.92%;\n    --heroui-foreground-400: 240 5.03% 64.9%;\n    --heroui-foreground-500: 240 3.83% 46.08%;\n    --heroui-foreground-600: 240 5.2% 33.92%;\n    --heroui-foreground-700: 240 5.26% 26.08%;\n    --heroui-foreground-800: 240 3.7% 15.88%;\n    --heroui-foreground-900: 240 5.88% 10%;\n    --heroui-foreground: 201.81999999999994 24.44% 8.82%;\n    --heroui-divider: 0 0% 6.67%;\n    --heroui-focus: 212.01999999999998 100% 46.67%;\n    --heroui-overlay: 0 0% 0%;\n    --heroui-content1: 0 0% 100%;\n    --heroui-content1-foreground: 201.81999999999994 24.44% 8.82%;\n    --heroui-content2: 240 4.76% 95.88%;\n    --heroui-content2-foreground: 240 3.7% 15.88%;\n    --heroui-content3: 240 5.88% 90%;\n    --heroui-content3-foreground: 240 5.26% 26.08%;\n    --heroui-content4: 240 4.88% 83.92%;\n    --heroui-content4-foreground: 240 5.2% 33.92%;\n    --heroui-default-50: 0 0% 98.04%;\n    --heroui-default-100: 240 4.76% 95.88%;\n    --heroui-default-200: 240 5.88% 90%;\n    --heroui-default-300: 240 4.88% 83.92%;\n    --heroui-default-400: 240 5.03% 64.9%;\n    --heroui-default-500: 240 3.83% 46.08%;\n    --heroui-default-600: 240 5.2% 33.92%;\n    --heroui-default-700: 240 5.26% 26.08%;\n    --heroui-default-800: 240 3.7% 15.88%;\n    --heroui-default-900: 240 5.88% 10%;\n    --heroui-default-foreground: 0 0% 0%;\n    --heroui-default: 240 4.88% 83.92%;\n    --heroui-primary-50: 212.5 92.31% 94.9%;\n    --heroui-primary-100: 211.84000000000003 92.45% 89.61%;\n    --heroui-primary-200: 211.84000000000003 92.45% 79.22%;\n    --heroui-primary-300: 212.24 92.45% 68.82%;\n    --heroui-primary-400: 212.14 92.45% 58.43%;\n    --heroui-primary-500: 212.01999999999998 100% 46.67%;\n    --heroui-primary-600: 212.14 100% 38.43%;\n    --heroui-primary-700: 212.24 100% 28.82%;\n    --heroui-primary-800: 211.84000000000003 100% 19.22%;\n    --heroui-primary-900: 211.84000000000003 100% 9.61%;\n    --heroui-primary-foreground: 0 0% 100%;\n    --heroui-primary: 212.01999999999998 100% 46.67%;\n    --heroui-secondary-50: 270 61.54% 94.9%;\n    --heroui-secondary-100: 270 59.26% 89.41%;\n    --heroui-secondary-200: 270 59.26% 78.82%;\n    --heroui-secondary-300: 270 59.26% 68.24%;\n    --heroui-secondary-400: 270 59.26% 57.65%;\n    --heroui-secondary-500: 270 66.67% 47.06%;\n    --heroui-secondary-600: 270 66.67% 37.65%;\n    --heroui-secondary-700: 270 66.67% 28.24%;\n    --heroui-secondary-800: 270 66.67% 18.82%;\n    --heroui-secondary-900: 270 66.67% 9.41%;\n    --heroui-secondary-foreground: 0 0% 100%;\n    --heroui-secondary: 270 66.67% 47.06%;\n    --heroui-success-50: 146.66999999999996 64.29% 94.51%;\n    --heroui-success-100: 145.71000000000004 61.4% 88.82%;\n    --heroui-success-200: 146.2 61.74% 77.45%;\n    --heroui-success-300: 145.78999999999996 62.57% 66.47%;\n    --heroui-success-400: 146.01 62.45% 55.1%;\n    --heroui-success-500: 145.96000000000004 79.46% 43.92%;\n    --heroui-success-600: 146.01 79.89% 35.1%;\n    --heroui-success-700: 145.78999999999996 79.26% 26.47%;\n    --heroui-success-800: 146.2 79.78% 17.45%;\n    --heroui-success-900: 145.71000000000004 77.78% 8.82%;\n    --heroui-success-foreground: 0 0% 0%;\n    --heroui-success: 145.96000000000004 79.46% 43.92%;\n    --heroui-warning-50: 54.55000000000001 91.67% 95.29%;\n    --heroui-warning-100: 37.139999999999986 91.3% 90.98%;\n    --heroui-warning-200: 37.139999999999986 91.3% 81.96%;\n    --heroui-warning-300: 36.95999999999998 91.24% 73.14%;\n    --heroui-warning-400: 37.00999999999999 91.26% 64.12%;\n    --heroui-warning-500: 37.02999999999997 91.27% 55.1%;\n    --heroui-warning-600: 37.00999999999999 74.22% 44.12%;\n    --heroui-warning-700: 36.95999999999998 73.96% 33.14%;\n    --heroui-warning-800: 37.139999999999986 75% 21.96%;\n    --heroui-warning-900: 37.139999999999986 75% 10.98%;\n    --heroui-warning-foreground: 0 0% 0%;\n    --heroui-warning: 37.02999999999997 91.27% 55.1%;\n    --heroui-danger-50: 339.13 92% 95.1%;\n    --heroui-danger-100: 340 91.84% 90.39%;\n    --heroui-danger-200: 339.3299999999999 90% 80.39%;\n    --heroui-danger-300: 339.11 90.6% 70.78%;\n    --heroui-danger-400: 339 90% 60.78%;\n    --heroui-danger-500: 339.20000000000005 90.36% 51.18%;\n    --heroui-danger-600: 339 86.54% 40.78%;\n    --heroui-danger-700: 339.11 85.99% 30.78%;\n    --heroui-danger-800: 339.3299999999999 86.54% 20.39%;\n    --heroui-danger-900: 340 84.91% 10.39%;\n    --heroui-danger-foreground: 0 0% 100%;\n    --heroui-danger: 339.20000000000005 90.36% 51.18%;\n    --heroui-divider-weight: 1px;\n    --heroui-disabled-opacity: .5;\n    --heroui-font-size-tiny: 0.75rem;\n    --heroui-font-size-small: 0.875rem;\n    --heroui-font-size-medium: 1rem;\n    --heroui-font-size-large: 1.125rem;\n    --heroui-line-height-tiny: 1rem;\n    --heroui-line-height-small: 1.25rem;\n    --heroui-line-height-medium: 1.5rem;\n    --heroui-line-height-large: 1.75rem;\n    --heroui-radius-small: 8px;\n    --heroui-radius-medium: 12px;\n    --heroui-radius-large: 14px;\n    --heroui-border-width-small: 1px;\n    --heroui-border-width-medium: 2px;\n    --heroui-border-width-large: 3px;\n    --heroui-box-shadow-small: 0px 0px 5px 0px rgb(0 0 0 / 0.02), 0px 2px 10px 0px rgb(0 0 0 / 0.06), 0px 0px 1px 0px rgb(0 0 0 / 0.3);\n    --heroui-box-shadow-medium: 0px 0px 15px 0px rgb(0 0 0 / 0.03), 0px 2px 30px 0px rgb(0 0 0 / 0.08), 0px 0px 1px 0px rgb(0 0 0 / 0.3);\n    --heroui-box-shadow-large: 0px 0px 30px 0px rgb(0 0 0 / 0.04), 0px 30px 60px 0px rgb(0 0 0 / 0.12), 0px 0px 1px 0px rgb(0 0 0 / 0.3);\n    --heroui-hover-opacity: .8;\n  }\n  .opacity-0 {\n    opacity: 0%;\n  }\n  .opacity-20 {\n    opacity: 20%;\n  }\n  .opacity-25 {\n    opacity: 25%;\n  }\n  .opacity-30 {\n    opacity: 30%;\n  }\n  .opacity-50 {\n    opacity: 50%;\n  }\n  .opacity-60 {\n    opacity: 60%;\n  }\n  .opacity-70 {\n    opacity: 70%;\n  }\n  .opacity-75 {\n    opacity: 75%;\n  }\n  .opacity-100 {\n    opacity: 100%;\n  }\n  .opacity-\\[0\\.0001\\] {\n    opacity: 0.0001;\n  }\n  .opacity-\\[value\\] {\n    opacity: value;\n  }\n  .opacity-disabled {\n    opacity: var(--heroui-disabled-opacity);\n  }\n  .\\!shadow-none {\n    --tw-shadow: 0 0 #0000 !important;\n    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow) !important;\n  }\n  .shadow {\n    --tw-shadow: 0 1px 3px 0 var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 1px 2px -1px var(--tw-shadow-color, rgb(0 0 0 / 0.1));\n    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\n  }\n  .shadow-\\[0_0_0_1px_hsl\\(var\\(--sidebar-border\\)\\)\\] {\n    --tw-shadow: 0 0 0 1px var(--tw-shadow-color, hsl(var(--sidebar-border)));\n    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\n  }\n  .shadow-\\[0_1px_0px_0_rgba\\(0\\,0\\,0\\,0\\.05\\)\\] {\n    --tw-shadow: 0 1px 0px 0 var(--tw-shadow-color, rgba(0,0,0,0.05));\n    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\n  }\n  .shadow-\\[0px_20px_20px_0px_rgb\\(0_0_0\\/0\\.05\\)\\] {\n    --tw-shadow: 0px 20px 20px 0px var(--tw-shadow-color, rgb(0 0 0/0.05));\n    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\n  }\n  .shadow-large {\n    --tw-shadow: var(--heroui-box-shadow-large);\n    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\n  }\n  .shadow-lg {\n    --tw-shadow: 0 10px 15px -3px var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 4px 6px -4px var(--tw-shadow-color, rgb(0 0 0 / 0.1));\n    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\n  }\n  .shadow-md {\n    --tw-shadow: 0 4px 6px -1px var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 2px 4px -2px var(--tw-shadow-color, rgb(0 0 0 / 0.1));\n    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\n  }\n  .shadow-medium {\n    --tw-shadow: var(--heroui-box-shadow-medium);\n    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\n  }\n  .shadow-none {\n    --tw-shadow: 0 0 #0000;\n    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\n  }\n  .shadow-sm {\n    --tw-shadow: 0 1px 3px 0 var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 1px 2px -1px var(--tw-shadow-color, rgb(0 0 0 / 0.1));\n    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\n  }\n  .shadow-small {\n    --tw-shadow: var(--heroui-box-shadow-small);\n    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\n  }\n  .shadow-xl {\n    --tw-shadow: 0 20px 25px -5px var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 8px 10px -6px var(--tw-shadow-color, rgb(0 0 0 / 0.1));\n    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\n  }\n  .shadow-xs {\n    --tw-shadow: 0 1px 2px 0 var(--tw-shadow-color, rgb(0 0 0 / 0.05));\n    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\n  }\n  .ring {\n    --tw-ring-shadow: var(--tw-ring-inset,) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);\n    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\n  }\n  .ring-0 {\n    --tw-ring-shadow: var(--tw-ring-inset,) 0 0 0 calc(0px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);\n    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\n  }\n  .ring-1 {\n    --tw-ring-shadow: var(--tw-ring-inset,) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);\n    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\n  }\n  .ring-2 {\n    --tw-ring-shadow: var(--tw-ring-inset,) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);\n    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\n  }\n  .shadow-black\\/5 {\n    --tw-shadow-color: color-mix(in srgb, #000 5%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      --tw-shadow-color: color-mix(in oklab, color-mix(in oklab, var(--color-black) 5%, transparent) var(--tw-shadow-alpha), transparent);\n    }\n  }\n  .shadow-danger\\/40 {\n    --tw-shadow-color: hsl(var(--heroui-danger) / 1);\n    @supports (color: color-mix(in lab, red, red)) {\n      --tw-shadow-color: color-mix(in oklab, color-mix(in oklab, hsl(var(--heroui-danger) / 1) 40%, transparent) var(--tw-shadow-alpha), transparent);\n    }\n  }\n  .shadow-default\\/50 {\n    --tw-shadow-color: hsl(var(--heroui-default) / 1);\n    @supports (color: color-mix(in lab, red, red)) {\n      --tw-shadow-color: color-mix(in oklab, color-mix(in oklab, hsl(var(--heroui-default) / 1) 50%, transparent) var(--tw-shadow-alpha), transparent);\n    }\n  }\n  .shadow-foreground\\/40 {\n    --tw-shadow-color: var(--foreground);\n    @supports (color: color-mix(in lab, red, red)) {\n      --tw-shadow-color: color-mix(in oklab, color-mix(in oklab, var(--foreground) 40%, transparent) var(--tw-shadow-alpha), transparent);\n    }\n  }\n  .shadow-primary\\/40 {\n    --tw-shadow-color: var(--primary);\n    @supports (color: color-mix(in lab, red, red)) {\n      --tw-shadow-color: color-mix(in oklab, color-mix(in oklab, var(--primary) 40%, transparent) var(--tw-shadow-alpha), transparent);\n    }\n  }\n  .shadow-secondary\\/40 {\n    --tw-shadow-color: var(--secondary);\n    @supports (color: color-mix(in lab, red, red)) {\n      --tw-shadow-color: color-mix(in oklab, color-mix(in oklab, var(--secondary) 40%, transparent) var(--tw-shadow-alpha), transparent);\n    }\n  }\n  .shadow-success\\/40 {\n    --tw-shadow-color: hsl(var(--heroui-success) / 1);\n    @supports (color: color-mix(in lab, red, red)) {\n      --tw-shadow-color: color-mix(in oklab, color-mix(in oklab, hsl(var(--heroui-success) / 1) 40%, transparent) var(--tw-shadow-alpha), transparent);\n    }\n  }\n  .shadow-warning\\/40 {\n    --tw-shadow-color: hsl(var(--heroui-warning) / 1);\n    @supports (color: color-mix(in lab, red, red)) {\n      --tw-shadow-color: color-mix(in oklab, color-mix(in oklab, hsl(var(--heroui-warning) / 1) 40%, transparent) var(--tw-shadow-alpha), transparent);\n    }\n  }\n  .ring-background {\n    --tw-ring-color: var(--background);\n  }\n  .ring-danger {\n    --tw-ring-color: hsl(var(--heroui-danger) / 1);\n  }\n  .ring-default {\n    --tw-ring-color: hsl(var(--heroui-default) / 1);\n  }\n  .ring-focus {\n    --tw-ring-color: hsl(var(--heroui-focus) / 1);\n  }\n  .ring-primary {\n    --tw-ring-color: var(--primary);\n  }\n  .ring-ring\\/50 {\n    --tw-ring-color: var(--ring);\n    @supports (color: color-mix(in lab, red, red)) {\n      --tw-ring-color: color-mix(in oklab, var(--ring) 50%, transparent);\n    }\n  }\n  .ring-secondary {\n    --tw-ring-color: var(--secondary);\n  }\n  .ring-sidebar-ring {\n    --tw-ring-color: var(--sidebar-ring);\n  }\n  .ring-success {\n    --tw-ring-color: hsl(var(--heroui-success) / 1);\n  }\n  .ring-transparent {\n    --tw-ring-color: transparent;\n  }\n  .ring-warning {\n    --tw-ring-color: hsl(var(--heroui-warning) / 1);\n  }\n  .ring-offset-2 {\n    --tw-ring-offset-width: 2px;\n    --tw-ring-offset-shadow: var(--tw-ring-inset,) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);\n  }\n  .ring-offset-background {\n    --tw-ring-offset-color: var(--background);\n  }\n  .outline-hidden {\n    --tw-outline-style: none;\n    outline-style: none;\n    @media (forced-colors: active) {\n      outline: 2px solid transparent;\n      outline-offset: 2px;\n    }\n  }\n  .outline {\n    outline-style: var(--tw-outline-style);\n    outline-width: 1px;\n  }\n  .blur {\n    --tw-blur: blur(8px);\n    filter: var(--tw-blur,) var(--tw-brightness,) var(--tw-contrast,) var(--tw-grayscale,) var(--tw-hue-rotate,) var(--tw-invert,) var(--tw-saturate,) var(--tw-sepia,) var(--tw-drop-shadow,);\n  }\n  .blur-lg {\n    --tw-blur: blur(var(--blur-lg));\n    filter: var(--tw-blur,) var(--tw-brightness,) var(--tw-contrast,) var(--tw-grayscale,) var(--tw-hue-rotate,) var(--tw-invert,) var(--tw-saturate,) var(--tw-sepia,) var(--tw-drop-shadow,);\n  }\n  .drop-shadow-md {\n    --tw-drop-shadow-size: drop-shadow(0 3px 3px var(--tw-drop-shadow-color, rgb(0 0 0 / 0.12)));\n    --tw-drop-shadow: drop-shadow(var(--drop-shadow-md));\n    filter: var(--tw-blur,) var(--tw-brightness,) var(--tw-contrast,) var(--tw-grayscale,) var(--tw-hue-rotate,) var(--tw-invert,) var(--tw-saturate,) var(--tw-sepia,) var(--tw-drop-shadow,);\n  }\n  .grayscale {\n    --tw-grayscale: grayscale(100%);\n    filter: var(--tw-blur,) var(--tw-brightness,) var(--tw-contrast,) var(--tw-grayscale,) var(--tw-hue-rotate,) var(--tw-invert,) var(--tw-saturate,) var(--tw-sepia,) var(--tw-drop-shadow,);\n  }\n  .saturate-150 {\n    --tw-saturate: saturate(150%);\n    filter: var(--tw-blur,) var(--tw-brightness,) var(--tw-contrast,) var(--tw-grayscale,) var(--tw-hue-rotate,) var(--tw-invert,) var(--tw-saturate,) var(--tw-sepia,) var(--tw-drop-shadow,);\n  }\n  .filter {\n    filter: var(--tw-blur,) var(--tw-brightness,) var(--tw-contrast,) var(--tw-grayscale,) var(--tw-hue-rotate,) var(--tw-invert,) var(--tw-saturate,) var(--tw-sepia,) var(--tw-drop-shadow,);\n  }\n  .backdrop-blur {\n    --tw-backdrop-blur: blur(8px);\n    -webkit-backdrop-filter: var(--tw-backdrop-blur,) var(--tw-backdrop-brightness,) var(--tw-backdrop-contrast,) var(--tw-backdrop-grayscale,) var(--tw-backdrop-hue-rotate,) var(--tw-backdrop-invert,) var(--tw-backdrop-opacity,) var(--tw-backdrop-saturate,) var(--tw-backdrop-sepia,);\n    backdrop-filter: var(--tw-backdrop-blur,) var(--tw-backdrop-brightness,) var(--tw-backdrop-contrast,) var(--tw-backdrop-grayscale,) var(--tw-backdrop-hue-rotate,) var(--tw-backdrop-invert,) var(--tw-backdrop-opacity,) var(--tw-backdrop-saturate,) var(--tw-backdrop-sepia,);\n  }\n  .backdrop-blur-lg {\n    --tw-backdrop-blur: blur(var(--blur-lg));\n    -webkit-backdrop-filter: var(--tw-backdrop-blur,) var(--tw-backdrop-brightness,) var(--tw-backdrop-contrast,) var(--tw-backdrop-grayscale,) var(--tw-backdrop-hue-rotate,) var(--tw-backdrop-invert,) var(--tw-backdrop-opacity,) var(--tw-backdrop-saturate,) var(--tw-backdrop-sepia,);\n    backdrop-filter: var(--tw-backdrop-blur,) var(--tw-backdrop-brightness,) var(--tw-backdrop-contrast,) var(--tw-backdrop-grayscale,) var(--tw-backdrop-hue-rotate,) var(--tw-backdrop-invert,) var(--tw-backdrop-opacity,) var(--tw-backdrop-saturate,) var(--tw-backdrop-sepia,);\n  }\n  .backdrop-blur-md {\n    --tw-backdrop-blur: blur(var(--blur-md));\n    -webkit-backdrop-filter: var(--tw-backdrop-blur,) var(--tw-backdrop-brightness,) var(--tw-backdrop-contrast,) var(--tw-backdrop-grayscale,) var(--tw-backdrop-hue-rotate,) var(--tw-backdrop-invert,) var(--tw-backdrop-opacity,) var(--tw-backdrop-saturate,) var(--tw-backdrop-sepia,);\n    backdrop-filter: var(--tw-backdrop-blur,) var(--tw-backdrop-brightness,) var(--tw-backdrop-contrast,) var(--tw-backdrop-grayscale,) var(--tw-backdrop-hue-rotate,) var(--tw-backdrop-invert,) var(--tw-backdrop-opacity,) var(--tw-backdrop-saturate,) var(--tw-backdrop-sepia,);\n  }\n  .backdrop-blur-sm {\n    --tw-backdrop-blur: blur(var(--blur-sm));\n    -webkit-backdrop-filter: var(--tw-backdrop-blur,) var(--tw-backdrop-brightness,) var(--tw-backdrop-contrast,) var(--tw-backdrop-grayscale,) var(--tw-backdrop-hue-rotate,) var(--tw-backdrop-invert,) var(--tw-backdrop-opacity,) var(--tw-backdrop-saturate,) var(--tw-backdrop-sepia,);\n    backdrop-filter: var(--tw-backdrop-blur,) var(--tw-backdrop-brightness,) var(--tw-backdrop-contrast,) var(--tw-backdrop-grayscale,) var(--tw-backdrop-hue-rotate,) var(--tw-backdrop-invert,) var(--tw-backdrop-opacity,) var(--tw-backdrop-saturate,) var(--tw-backdrop-sepia,);\n  }\n  .backdrop-blur-xl {\n    --tw-backdrop-blur: blur(var(--blur-xl));\n    -webkit-backdrop-filter: var(--tw-backdrop-blur,) var(--tw-backdrop-brightness,) var(--tw-backdrop-contrast,) var(--tw-backdrop-grayscale,) var(--tw-backdrop-hue-rotate,) var(--tw-backdrop-invert,) var(--tw-backdrop-opacity,) var(--tw-backdrop-saturate,) var(--tw-backdrop-sepia,);\n    backdrop-filter: var(--tw-backdrop-blur,) var(--tw-backdrop-brightness,) var(--tw-backdrop-contrast,) var(--tw-backdrop-grayscale,) var(--tw-backdrop-hue-rotate,) var(--tw-backdrop-invert,) var(--tw-backdrop-opacity,) var(--tw-backdrop-saturate,) var(--tw-backdrop-sepia,);\n  }\n  .backdrop-opacity-disabled {\n    --tw-backdrop-opacity: opacity(var(--heroui-disabled-opacity));\n    -webkit-backdrop-filter: var(--tw-backdrop-blur,) var(--tw-backdrop-brightness,) var(--tw-backdrop-contrast,) var(--tw-backdrop-grayscale,) var(--tw-backdrop-hue-rotate,) var(--tw-backdrop-invert,) var(--tw-backdrop-opacity,) var(--tw-backdrop-saturate,) var(--tw-backdrop-sepia,);\n    backdrop-filter: var(--tw-backdrop-blur,) var(--tw-backdrop-brightness,) var(--tw-backdrop-contrast,) var(--tw-backdrop-grayscale,) var(--tw-backdrop-hue-rotate,) var(--tw-backdrop-invert,) var(--tw-backdrop-opacity,) var(--tw-backdrop-saturate,) var(--tw-backdrop-sepia,);\n  }\n  .backdrop-saturate-150 {\n    --tw-backdrop-saturate: saturate(150%);\n    -webkit-backdrop-filter: var(--tw-backdrop-blur,) var(--tw-backdrop-brightness,) var(--tw-backdrop-contrast,) var(--tw-backdrop-grayscale,) var(--tw-backdrop-hue-rotate,) var(--tw-backdrop-invert,) var(--tw-backdrop-opacity,) var(--tw-backdrop-saturate,) var(--tw-backdrop-sepia,);\n    backdrop-filter: var(--tw-backdrop-blur,) var(--tw-backdrop-brightness,) var(--tw-backdrop-contrast,) var(--tw-backdrop-grayscale,) var(--tw-backdrop-hue-rotate,) var(--tw-backdrop-invert,) var(--tw-backdrop-opacity,) var(--tw-backdrop-saturate,) var(--tw-backdrop-sepia,);\n  }\n  .transition {\n    transition-property: color, background-color, border-color, outline-color, text-decoration-color, fill, stroke, --tw-gradient-from, --tw-gradient-via, --tw-gradient-to, opacity, box-shadow, transform, translate, scale, rotate, filter, -webkit-backdrop-filter, backdrop-filter;\n    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));\n    transition-duration: var(--tw-duration, var(--default-transition-duration));\n  }\n  .transition-\\[color\\,box-shadow\\] {\n    transition-property: color,box-shadow;\n    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));\n    transition-duration: var(--tw-duration, var(--default-transition-duration));\n  }\n  .transition-\\[color\\,opacity\\] {\n    transition-property: color,opacity;\n    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));\n    transition-duration: var(--tw-duration, var(--default-transition-duration));\n  }\n  .transition-\\[left\\,right\\,width\\] {\n    transition-property: left,right,width;\n    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));\n    transition-duration: var(--tw-duration, var(--default-transition-duration));\n  }\n  .transition-\\[margin\\,opacity\\] {\n    transition-property: margin,opacity;\n    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));\n    transition-duration: var(--tw-duration, var(--default-transition-duration));\n  }\n  .transition-\\[opacity\\,transform\\] {\n    transition-property: opacity,transform;\n    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));\n    transition-duration: var(--tw-duration, var(--default-transition-duration));\n  }\n  .transition-\\[transform\\,background-color\\,color\\] {\n    transition-property: transform,background-color,color;\n    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));\n    transition-duration: var(--tw-duration, var(--default-transition-duration));\n  }\n  .transition-\\[transform\\,color\\,left\\,opacity\\] {\n    transition-property: transform,color,left,opacity;\n    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));\n    transition-duration: var(--tw-duration, var(--default-transition-duration));\n  }\n  .transition-\\[width\\,height\\,padding\\] {\n    transition-property: width,height,padding;\n    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));\n    transition-duration: var(--tw-duration, var(--default-transition-duration));\n  }\n  .transition-\\[width\\,height\\] {\n    transition-property: width,height;\n    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));\n    transition-duration: var(--tw-duration, var(--default-transition-duration));\n  }\n  .transition-\\[width\\] {\n    transition-property: width;\n    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));\n    transition-duration: var(--tw-duration, var(--default-transition-duration));\n  }\n  .transition-all {\n    transition-property: all;\n    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));\n    transition-duration: var(--tw-duration, var(--default-transition-duration));\n  }\n  .transition-background {\n    transition-property: background;\n    transition-timing-function: ease;\n    transition-duration: 250ms;\n  }\n  .transition-colors {\n    transition-property: color, background-color, border-color, outline-color, text-decoration-color, fill, stroke, --tw-gradient-from, --tw-gradient-via, --tw-gradient-to;\n    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));\n    transition-duration: var(--tw-duration, var(--default-transition-duration));\n  }\n  .transition-colors-opacity {\n    transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity;\n    transition-timing-function: ease;\n    transition-duration: 250ms;\n  }\n  .transition-height {\n    transition-property: height;\n    transition-timing-function: ease;\n    transition-duration: 250ms;\n  }\n  .transition-left {\n    transition-property: left;\n    transition-timing-function: ease;\n    transition-duration: 250ms;\n  }\n  .transition-opacity {\n    transition-property: opacity;\n    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));\n    transition-duration: var(--tw-duration, var(--default-transition-duration));\n  }\n  .transition-shadow {\n    transition-property: box-shadow;\n    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));\n    transition-duration: var(--tw-duration, var(--default-transition-duration));\n  }\n  .transition-size {\n    transition-property: width, height;\n    transition-timing-function: ease;\n    transition-duration: 250ms;\n  }\n  .transition-transform {\n    transition-property: transform, translate, scale, rotate;\n    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));\n    transition-duration: var(--tw-duration, var(--default-transition-duration));\n  }\n  .transition-transform-background {\n    transition-property: transform, scale, background;\n    transition-timing-function: ease;\n    transition-duration: 250ms;\n  }\n  .transition-transform-colors {\n    transition-property: transform, scale, color, background, background-color, border-color, text-decoration-color, fill, stroke;\n    transition-timing-function: ease;\n    transition-duration: 250ms;\n  }\n  .transition-transform-colors-opacity {\n    transition-property: transform, scale, color, background, background-color, border-color, text-decoration-color, fill, stroke, opacity;\n    transition-timing-function: ease;\n    transition-duration: 250ms;\n  }\n  .transition-transform-opacity {\n    transition-property: transform, scale, opacity;\n    transition-timing-function: ease;\n    transition-duration: 250ms;\n  }\n  .transition-width {\n    transition-property: width;\n    transition-timing-function: ease;\n    transition-duration: 250ms;\n  }\n  .\\!transition-none {\n    transition-property: none !important;\n  }\n  .transition-none {\n    transition-property: none;\n  }\n  .\\!duration-100 {\n    --tw-duration: 100ms !important;\n    transition-duration: 100ms !important;\n  }\n  .\\!duration-150 {\n    --tw-duration: 150ms !important;\n    transition-duration: 150ms !important;\n  }\n  .\\!duration-200 {\n    --tw-duration: 200ms !important;\n    transition-duration: 200ms !important;\n  }\n  .\\!duration-250 {\n    --tw-duration: 250ms !important;\n    transition-duration: 250ms !important;\n  }\n  .\\!duration-300 {\n    --tw-duration: 300ms !important;\n    transition-duration: 300ms !important;\n  }\n  .\\!duration-500 {\n    --tw-duration: 500ms !important;\n    transition-duration: 500ms !important;\n  }\n  .duration-150 {\n    --tw-duration: 150ms;\n    transition-duration: 150ms;\n  }\n  .duration-200 {\n    --tw-duration: 200ms;\n    transition-duration: 200ms;\n  }\n  .duration-300 {\n    --tw-duration: 300ms;\n    transition-duration: 300ms;\n  }\n  .duration-1000 {\n    --tw-duration: 1000ms;\n    transition-duration: 1000ms;\n  }\n  .\\!ease-out {\n    --tw-ease: var(--ease-out) !important;\n    transition-timing-function: var(--ease-out) !important;\n  }\n  .\\!ease-soft-spring {\n    --tw-ease: cubic-bezier(0.155, 1.105, 0.295, 1.12) !important;\n    transition-timing-function: cubic-bezier(0.155, 1.105, 0.295, 1.12) !important;\n  }\n  .ease-in {\n    --tw-ease: var(--ease-in);\n    transition-timing-function: var(--ease-in);\n  }\n  .ease-in-out {\n    --tw-ease: var(--ease-in-out);\n    transition-timing-function: var(--ease-in-out);\n  }\n  .ease-linear {\n    --tw-ease: linear;\n    transition-timing-function: linear;\n  }\n  .ease-out {\n    --tw-ease: var(--ease-out);\n    transition-timing-function: var(--ease-out);\n  }\n  .will-change-auto {\n    will-change: auto;\n  }\n  .will-change-transform {\n    will-change: transform;\n  }\n  .\\!outline-none {\n    --tw-outline-style: none !important;\n    outline-style: none !important;\n  }\n  .outline-none {\n    --tw-outline-style: none;\n    outline-style: none;\n  }\n  .select-none {\n    -webkit-user-select: none;\n    user-select: none;\n  }\n  .slide-in-from-bottom-5 {\n    --tw-enter-translate-y: calc(5*var(--spacing));\n    --tw-enter-translate-y: calc(.05*100%);\n  }\n  .\\[--picker-height\\:224px\\] {\n    --picker-height: 224px;\n  }\n  .\\[--scale-enter\\:100\\%\\] {\n    --scale-enter: 100%;\n  }\n  .\\[--scale-exit\\:100\\%\\] {\n    --scale-exit: 100%;\n  }\n  .\\[--scroll-shadow-size\\:100px\\] {\n    --scroll-shadow-size: 100px;\n  }\n  .\\[--slide-enter\\:0px\\] {\n    --slide-enter: 0px;\n  }\n  .\\[--slide-exit\\:80px\\] {\n    --slide-exit: 80px;\n  }\n  .\\[-webkit-mask\\:radial-gradient\\(closest-side\\,rgba\\(0\\,0\\,0\\,0\\.0\\)calc\\(100\\%-3px\\)\\,rgba\\(0\\,0\\,0\\,1\\)calc\\(100\\%-3px\\)\\)\\] {\n    -webkit-mask: radial-gradient(closest-side,rgba(0,0,0,0.0)calc(100% - 3px),rgba(0,0,0,1)calc(100% - 3px));\n  }\n  .\\[animation-duration\\:1s\\] {\n    animation-duration: 1s;\n  }\n  .fade-in {\n    --tw-enter-opacity: 0;\n  }\n  .fade-in-0 {\n    --tw-enter-opacity: 0;\n  }\n  .fade-out {\n    --tw-exit-opacity: 0;\n  }\n  .input-search-cancel-button-none {\n    &::-webkit-search-cancel-button {\n      -webkit-appearance: none;\n    }\n  }\n  .running {\n    animation-play-state: running;\n  }\n  .spinner-dot-animation {\n    animation-delay: calc(250ms * var(--dot-index));\n  }\n  .spinner-dot-blink-animation {\n    animation-delay: calc(200ms * var(--dot-index));\n  }\n  .tap-highlight-transparent {\n    -webkit-tap-highlight-color: transparent;\n  }\n  .text-fill-inherit {\n    -webkit-text-fill-color: inherit;\n  }\n  .zoom-in {\n    --tw-enter-scale: 0;\n  }\n  .zoom-in-95 {\n    --tw-enter-scale: .95;\n  }\n  .zoom-out {\n    --tw-exit-scale: 0;\n  }\n  .group-focus-within\\/menu-item\\:opacity-100 {\n    &:is(:where(.group\\/menu-item):focus-within *) {\n      opacity: 100%;\n    }\n  }\n  .group-hover\\:pointer-events-auto {\n    &:is(:where(.group):hover *) {\n      @media (hover: hover) {\n        pointer-events: auto;\n      }\n    }\n  }\n  .group-hover\\:block {\n    &:is(:where(.group):hover *) {\n      @media (hover: hover) {\n        display: block;\n      }\n    }\n  }\n  .group-hover\\:hidden {\n    &:is(:where(.group):hover *) {\n      @media (hover: hover) {\n        display: none;\n      }\n    }\n  }\n  .group-hover\\:scale-110 {\n    &:is(:where(.group):hover *) {\n      @media (hover: hover) {\n        --tw-scale-x: 110%;\n        --tw-scale-y: 110%;\n        --tw-scale-z: 110%;\n        scale: var(--tw-scale-x) var(--tw-scale-y);\n      }\n    }\n  }\n  .group-hover\\:border-current {\n    &:is(:where(.group):hover *) {\n      @media (hover: hover) {\n        border-color: currentcolor;\n      }\n    }\n  }\n  .group-hover\\:text-current {\n    &:is(:where(.group):hover *) {\n      @media (hover: hover) {\n        color: currentcolor;\n      }\n    }\n  }\n  .group-hover\\:opacity-100 {\n    &:is(:where(.group):hover *) {\n      @media (hover: hover) {\n        opacity: 100%;\n      }\n    }\n  }\n  .group-hover\\/menu-item\\:opacity-100 {\n    &:is(:where(.group\\/menu-item):hover *) {\n      @media (hover: hover) {\n        opacity: 100%;\n      }\n    }\n  }\n  .group-hover\\/sidebar\\:underline {\n    &:is(:where(.group\\/sidebar):hover *) {\n      @media (hover: hover) {\n        text-decoration-line: underline;\n      }\n    }\n  }\n  .group-has-data-\\[collapsible\\=icon\\]\\/sidebar-wrapper\\:h-\\(--header-height\\) {\n    &:is(:where(.group\\/sidebar-wrapper):has(*[data-collapsible=\"icon\"]) *) {\n      height: var(--header-height);\n    }\n  }\n  .group-has-data-\\[sidebar\\=menu-action\\]\\/menu-item\\:pr-8 {\n    &:is(:where(.group\\/menu-item):has(*[data-sidebar=\"menu-action\"]) *) {\n      padding-right: calc(var(--spacing) * 8);\n    }\n  }\n  .group-data-\\[collapsible\\=icon\\]\\:-mt-8 {\n    &:is(:where(.group)[data-collapsible=\"icon\"] *) {\n      margin-top: calc(var(--spacing) * -8);\n    }\n  }\n  .group-data-\\[collapsible\\=icon\\]\\:block {\n    &:is(:where(.group)[data-collapsible=\"icon\"] *) {\n      display: block;\n    }\n  }\n  .group-data-\\[collapsible\\=icon\\]\\:hidden {\n    &:is(:where(.group)[data-collapsible=\"icon\"] *) {\n      display: none;\n    }\n  }\n  .group-data-\\[collapsible\\=icon\\]\\:size-8\\! {\n    &:is(:where(.group)[data-collapsible=\"icon\"] *) {\n      width: calc(var(--spacing) * 8) !important;\n      height: calc(var(--spacing) * 8) !important;\n    }\n  }\n  .group-data-\\[collapsible\\=icon\\]\\:w-\\(--sidebar-width-icon\\) {\n    &:is(:where(.group)[data-collapsible=\"icon\"] *) {\n      width: var(--sidebar-width-icon);\n    }\n  }\n  .group-data-\\[collapsible\\=icon\\]\\:w-\\[calc\\(var\\(--sidebar-width-icon\\)\\+\\(--spacing\\(4\\)\\)\\)\\] {\n    &:is(:where(.group)[data-collapsible=\"icon\"] *) {\n      width: calc(var(--sidebar-width-icon) + (calc(var(--spacing) * 4)));\n    }\n  }\n  .group-data-\\[collapsible\\=icon\\]\\:w-\\[calc\\(var\\(--sidebar-width-icon\\)\\+\\(--spacing\\(4\\)\\)\\+2px\\)\\] {\n    &:is(:where(.group)[data-collapsible=\"icon\"] *) {\n      width: calc(var(--sidebar-width-icon) + (calc(var(--spacing) * 4)) + 2px);\n    }\n  }\n  .group-data-\\[collapsible\\=icon\\]\\:overflow-hidden {\n    &:is(:where(.group)[data-collapsible=\"icon\"] *) {\n      overflow: hidden;\n    }\n  }\n  .group-data-\\[collapsible\\=icon\\]\\:p-0\\! {\n    &:is(:where(.group)[data-collapsible=\"icon\"] *) {\n      padding: calc(var(--spacing) * 0) !important;\n    }\n  }\n  .group-data-\\[collapsible\\=icon\\]\\:p-2\\! {\n    &:is(:where(.group)[data-collapsible=\"icon\"] *) {\n      padding: calc(var(--spacing) * 2) !important;\n    }\n  }\n  .group-data-\\[collapsible\\=icon\\]\\:opacity-0 {\n    &:is(:where(.group)[data-collapsible=\"icon\"] *) {\n      opacity: 0%;\n    }\n  }\n  .group-data-\\[collapsible\\=offcanvas\\]\\:right-\\[calc\\(var\\(--sidebar-width\\)\\*-1\\)\\] {\n    &:is(:where(.group)[data-collapsible=\"offcanvas\"] *) {\n      right: calc(var(--sidebar-width) * -1);\n    }\n  }\n  .group-data-\\[collapsible\\=offcanvas\\]\\:left-\\[calc\\(var\\(--sidebar-width\\)\\*-1\\)\\] {\n    &:is(:where(.group)[data-collapsible=\"offcanvas\"] *) {\n      left: calc(var(--sidebar-width) * -1);\n    }\n  }\n  .group-data-\\[collapsible\\=offcanvas\\]\\:w-0 {\n    &:is(:where(.group)[data-collapsible=\"offcanvas\"] *) {\n      width: calc(var(--spacing) * 0);\n    }\n  }\n  .group-data-\\[collapsible\\=offcanvas\\]\\:translate-x-0 {\n    &:is(:where(.group)[data-collapsible=\"offcanvas\"] *) {\n      --tw-translate-x: calc(var(--spacing) * 0);\n      translate: var(--tw-translate-x) var(--tw-translate-y);\n    }\n  }\n  .group-data-\\[copied\\=true\\]\\:scale-50 {\n    &:is(:where(.group)[data-copied=\"true\"] *) {\n      --tw-scale-x: 50%;\n      --tw-scale-y: 50%;\n      --tw-scale-z: 50%;\n      scale: var(--tw-scale-x) var(--tw-scale-y);\n    }\n  }\n  .group-data-\\[copied\\=true\\]\\:scale-100 {\n    &:is(:where(.group)[data-copied=\"true\"] *) {\n      --tw-scale-x: 100%;\n      --tw-scale-y: 100%;\n      --tw-scale-z: 100%;\n      scale: var(--tw-scale-x) var(--tw-scale-y);\n    }\n  }\n  .group-data-\\[copied\\=true\\]\\:opacity-0 {\n    &:is(:where(.group)[data-copied=\"true\"] *) {\n      opacity: 0%;\n    }\n  }\n  .group-data-\\[copied\\=true\\]\\:opacity-100 {\n    &:is(:where(.group)[data-copied=\"true\"] *) {\n      opacity: 100%;\n    }\n  }\n  .group-data-\\[disabled\\=true\\]\\:pointer-events-none {\n    &:is(:where(.group)[data-disabled=\"true\"] *) {\n      pointer-events: none;\n    }\n  }\n  .group-data-\\[disabled\\=true\\]\\:opacity-50 {\n    &:is(:where(.group)[data-disabled=\"true\"] *) {\n      opacity: 50%;\n    }\n  }\n  .group-data-\\[disabled\\=true\\]\\/tr\\:cursor-not-allowed {\n    &:is(:where(.group\\/tr)[data-disabled=\"true\"] *) {\n      cursor: not-allowed;\n    }\n  }\n  .group-data-\\[disabled\\=true\\]\\/tr\\:text-foreground-300 {\n    &:is(:where(.group\\/tr)[data-disabled=\"true\"] *) {\n      color: hsl(var(--heroui-foreground-300) / 1);\n    }\n  }\n  .group-data-\\[filled-within\\=true\\]\\:pointer-events-auto {\n    &:is(:where(.group)[data-filled-within=\"true\"] *) {\n      pointer-events: auto;\n    }\n  }\n  .group-data-\\[filled-within\\=true\\]\\:start-0 {\n    &:is(:where(.group)[data-filled-within=\"true\"] *) {\n      inset-inline-start: calc(var(--spacing) * 0);\n    }\n  }\n  .group-data-\\[filled-within\\=true\\]\\:scale-85 {\n    &:is(:where(.group)[data-filled-within=\"true\"] *) {\n      --tw-scale-x: 0.85;\n      --tw-scale-y: 0.85;\n      --tw-scale-z: 0.85;\n      scale: var(--tw-scale-x) var(--tw-scale-y);\n    }\n  }\n  .group-data-\\[filled-within\\=true\\]\\:text-default-600 {\n    &:is(:where(.group)[data-filled-within=\"true\"] *) {\n      color: hsl(var(--heroui-default-600) / 1);\n    }\n  }\n  .group-data-\\[filled-within\\=true\\]\\:text-foreground {\n    &:is(:where(.group)[data-filled-within=\"true\"] *) {\n      color: var(--foreground);\n    }\n  }\n  .group-data-\\[filled\\=true\\]\\:start-0 {\n    &:is(:where(.group)[data-filled=\"true\"] *) {\n      inset-inline-start: calc(var(--spacing) * 0);\n    }\n  }\n  .group-data-\\[filled\\=true\\]\\:scale-85 {\n    &:is(:where(.group)[data-filled=\"true\"] *) {\n      --tw-scale-x: 0.85;\n      --tw-scale-y: 0.85;\n      --tw-scale-z: 0.85;\n      scale: var(--tw-scale-x) var(--tw-scale-y);\n    }\n  }\n  .group-data-\\[filled\\=true\\]\\:text-default-600 {\n    &:is(:where(.group)[data-filled=\"true\"] *) {\n      color: hsl(var(--heroui-default-600) / 1);\n    }\n  }\n  .group-data-\\[filled\\=true\\]\\:text-foreground {\n    &:is(:where(.group)[data-filled=\"true\"] *) {\n      color: var(--foreground);\n    }\n  }\n  .group-data-\\[focus-visible\\=true\\]\\:z-10 {\n    &:is(:where(.group)[data-focus-visible=\"true\"] *) {\n      z-index: 10;\n    }\n  }\n  .group-data-\\[focus-visible\\=true\\]\\:block {\n    &:is(:where(.group)[data-focus-visible=\"true\"] *) {\n      display: block;\n    }\n  }\n  .group-data-\\[focus-visible\\=true\\]\\:hidden {\n    &:is(:where(.group)[data-focus-visible=\"true\"] *) {\n      display: none;\n    }\n  }\n  .group-data-\\[focus-visible\\=true\\]\\:ring-2 {\n    &:is(:where(.group)[data-focus-visible=\"true\"] *) {\n      --tw-ring-shadow: var(--tw-ring-inset,) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);\n      box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\n    }\n  }\n  .group-data-\\[focus-visible\\=true\\]\\:ring-focus {\n    &:is(:where(.group)[data-focus-visible=\"true\"] *) {\n      --tw-ring-color: hsl(var(--heroui-focus) / 1);\n    }\n  }\n  .group-data-\\[focus-visible\\=true\\]\\:ring-offset-2 {\n    &:is(:where(.group)[data-focus-visible=\"true\"] *) {\n      --tw-ring-offset-width: 2px;\n      --tw-ring-offset-shadow: var(--tw-ring-inset,) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);\n    }\n  }\n  .group-data-\\[focus-visible\\=true\\]\\:ring-offset-background {\n    &:is(:where(.group)[data-focus-visible=\"true\"] *) {\n      --tw-ring-offset-color: var(--background);\n    }\n  }\n  .group-data-\\[focus\\=true\\]\\:\\!border-danger {\n    &:is(:where(.group)[data-focus=\"true\"] *) {\n      border-color: hsl(var(--heroui-danger) / 1) !important;\n    }\n  }\n  .group-data-\\[focus\\=true\\]\\:border-danger {\n    &:is(:where(.group)[data-focus=\"true\"] *) {\n      border-color: hsl(var(--heroui-danger) / 1);\n    }\n  }\n  .group-data-\\[focus\\=true\\]\\:border-default-foreground {\n    &:is(:where(.group)[data-focus=\"true\"] *) {\n      border-color: hsl(var(--heroui-default-foreground) / 1);\n    }\n  }\n  .group-data-\\[focus\\=true\\]\\:border-primary {\n    &:is(:where(.group)[data-focus=\"true\"] *) {\n      border-color: var(--primary);\n    }\n  }\n  .group-data-\\[focus\\=true\\]\\:border-secondary {\n    &:is(:where(.group)[data-focus=\"true\"] *) {\n      border-color: var(--secondary);\n    }\n  }\n  .group-data-\\[focus\\=true\\]\\:border-success {\n    &:is(:where(.group)[data-focus=\"true\"] *) {\n      border-color: hsl(var(--heroui-success) / 1);\n    }\n  }\n  .group-data-\\[focus\\=true\\]\\:border-warning {\n    &:is(:where(.group)[data-focus=\"true\"] *) {\n      border-color: hsl(var(--heroui-warning) / 1);\n    }\n  }\n  .group-data-\\[focus\\=true\\]\\:\\!bg-danger-50 {\n    &:is(:where(.group)[data-focus=\"true\"] *) {\n      background-color: hsl(var(--heroui-danger-50) / 1) !important;\n    }\n  }\n  .group-data-\\[focus\\=true\\]\\:bg-danger-50 {\n    &:is(:where(.group)[data-focus=\"true\"] *) {\n      background-color: hsl(var(--heroui-danger-50) / 1);\n    }\n  }\n  .group-data-\\[focus\\=true\\]\\:bg-default-100 {\n    &:is(:where(.group)[data-focus=\"true\"] *) {\n      background-color: hsl(var(--heroui-default-100) / 1);\n    }\n  }\n  .group-data-\\[focus\\=true\\]\\:bg-default-200 {\n    &:is(:where(.group)[data-focus=\"true\"] *) {\n      background-color: hsl(var(--heroui-default-200) / 1);\n    }\n  }\n  .group-data-\\[focus\\=true\\]\\:bg-primary-50 {\n    &:is(:where(.group)[data-focus=\"true\"] *) {\n      background-color: hsl(var(--heroui-primary-50) / 1);\n    }\n  }\n  .group-data-\\[focus\\=true\\]\\:bg-secondary-50 {\n    &:is(:where(.group)[data-focus=\"true\"] *) {\n      background-color: hsl(var(--heroui-secondary-50) / 1);\n    }\n  }\n  .group-data-\\[focus\\=true\\]\\:bg-success-50 {\n    &:is(:where(.group)[data-focus=\"true\"] *) {\n      background-color: hsl(var(--heroui-success-50) / 1);\n    }\n  }\n  .group-data-\\[focus\\=true\\]\\:bg-warning-50 {\n    &:is(:where(.group)[data-focus=\"true\"] *) {\n      background-color: hsl(var(--heroui-warning-50) / 1);\n    }\n  }\n  .group-data-\\[has-helper\\=true\\]\\:flex {\n    &:is(:where(.group)[data-has-helper=\"true\"] *) {\n      display: flex;\n    }\n  }\n  .group-data-\\[has-helper\\=true\\]\\:pt-2 {\n    &:is(:where(.group)[data-has-helper=\"true\"] *) {\n      padding-top: calc(var(--spacing) * 2);\n    }\n  }\n  .group-data-\\[has-helper\\=true\\]\\:pt-3 {\n    &:is(:where(.group)[data-has-helper=\"true\"] *) {\n      padding-top: calc(var(--spacing) * 3);\n    }\n  }\n  .group-data-\\[has-helper\\=true\\]\\:pt-4 {\n    &:is(:where(.group)[data-has-helper=\"true\"] *) {\n      padding-top: calc(var(--spacing) * 4);\n    }\n  }\n  .group-data-\\[has-label-outside\\=true\\]\\:pointer-events-auto {\n    &:is(:where(.group)[data-has-label-outside=\"true\"] *) {\n      pointer-events: auto;\n    }\n  }\n  .group-data-\\[has-label\\=true\\]\\:items-end {\n    &:is(:where(.group)[data-has-label=\"true\"] *) {\n      align-items: flex-end;\n    }\n  }\n  .group-data-\\[has-label\\=true\\]\\:items-start {\n    &:is(:where(.group)[data-has-label=\"true\"] *) {\n      align-items: flex-start;\n    }\n  }\n  .group-data-\\[has-label\\=true\\]\\:pt-4 {\n    &:is(:where(.group)[data-has-label=\"true\"] *) {\n      padding-top: calc(var(--spacing) * 4);\n    }\n  }\n  .group-data-\\[has-label\\=true\\]\\:pt-5 {\n    &:is(:where(.group)[data-has-label=\"true\"] *) {\n      padding-top: calc(var(--spacing) * 5);\n    }\n  }\n  .group-data-\\[has-multiple-months\\=true\\]\\:flex-row {\n    &:is(:where(.group)[data-has-multiple-months=\"true\"] *) {\n      flex-direction: row;\n    }\n  }\n  .group-data-\\[has-value\\=true\\]\\:pointer-events-auto {\n    &:is(:where(.group)[data-has-value=\"true\"] *) {\n      pointer-events: auto;\n    }\n  }\n  .group-data-\\[has-value\\=true\\]\\:block {\n    &:is(:where(.group)[data-has-value=\"true\"] *) {\n      display: block;\n    }\n  }\n  .group-data-\\[has-value\\=true\\]\\:scale-100 {\n    &:is(:where(.group)[data-has-value=\"true\"] *) {\n      --tw-scale-x: 100%;\n      --tw-scale-y: 100%;\n      --tw-scale-z: 100%;\n      scale: var(--tw-scale-x) var(--tw-scale-y);\n    }\n  }\n  .group-data-\\[has-value\\=true\\]\\:text-default-foreground {\n    &:is(:where(.group)[data-has-value=\"true\"] *) {\n      color: hsl(var(--heroui-default-foreground) / 1);\n    }\n  }\n  .group-data-\\[has-value\\=true\\]\\:text-foreground {\n    &:is(:where(.group)[data-has-value=\"true\"] *) {\n      color: var(--foreground);\n    }\n  }\n  .group-data-\\[has-value\\=true\\]\\:opacity-70 {\n    &:is(:where(.group)[data-has-value=\"true\"] *) {\n      opacity: 70%;\n    }\n  }\n  .group-data-\\[hover-unselected\\=true\\]\\:bg-default-100 {\n    &:is(:where(.group)[data-hover-unselected=\"true\"] *) {\n      background-color: hsl(var(--heroui-default-100) / 1);\n    }\n  }\n  .group-data-\\[hover\\=true\\]\\/th\\:opacity-100 {\n    &:is(:where(.group\\/th)[data-hover=\"true\"] *) {\n      opacity: 100%;\n    }\n  }\n  .group-data-\\[invalid\\=true\\]\\:border-danger {\n    &:is(:where(.group)[data-invalid=\"true\"] *) {\n      border-color: hsl(var(--heroui-danger) / 1);\n    }\n  }\n  .group-data-\\[invalid\\=true\\]\\:bg-danger-50 {\n    &:is(:where(.group)[data-invalid=\"true\"] *) {\n      background-color: hsl(var(--heroui-danger-50) / 1);\n    }\n  }\n  .group-data-\\[invalid\\=true\\]\\:text-danger {\n    &:is(:where(.group)[data-invalid=\"true\"] *) {\n      color: hsl(var(--heroui-danger) / 1);\n    }\n  }\n  .group-data-\\[loaded\\=true\\]\\:opacity-100 {\n    &:is(:where(.group)[data-loaded=\"true\"] *) {\n      opacity: 100%;\n    }\n  }\n  .group-data-\\[pressed\\=true\\]\\:w-5 {\n    &:is(:where(.group)[data-pressed=\"true\"] *) {\n      width: calc(var(--spacing) * 5);\n    }\n  }\n  .group-data-\\[pressed\\=true\\]\\:w-6 {\n    &:is(:where(.group)[data-pressed=\"true\"] *) {\n      width: calc(var(--spacing) * 6);\n    }\n  }\n  .group-data-\\[pressed\\=true\\]\\:w-7 {\n    &:is(:where(.group)[data-pressed=\"true\"] *) {\n      width: calc(var(--spacing) * 7);\n    }\n  }\n  .group-data-\\[pressed\\=true\\]\\:scale-95 {\n    &:is(:where(.group)[data-pressed=\"true\"] *) {\n      --tw-scale-x: 95%;\n      --tw-scale-y: 95%;\n      --tw-scale-z: 95%;\n      scale: var(--tw-scale-x) var(--tw-scale-y);\n    }\n  }\n  .group-data-\\[pressed\\=true\\]\\:opacity-70 {\n    &:is(:where(.group)[data-pressed=\"true\"] *) {\n      opacity: 70%;\n    }\n  }\n  .group-data-\\[selected\\]\\:group-data-\\[pressed\\]\\:ml-3 {\n    &:is(:where(.group)[data-selected] *) {\n      &:is(:where(.group)[data-pressed] *) {\n        margin-left: calc(var(--spacing) * 3);\n      }\n    }\n  }\n  .group-data-\\[selected\\]\\:group-data-\\[pressed\\]\\:ml-4 {\n    &:is(:where(.group)[data-selected] *) {\n      &:is(:where(.group)[data-pressed] *) {\n        margin-left: calc(var(--spacing) * 4);\n      }\n    }\n  }\n  .group-data-\\[selected\\]\\:group-data-\\[pressed\\]\\:ml-5 {\n    &:is(:where(.group)[data-selected] *) {\n      &:is(:where(.group)[data-pressed] *) {\n        margin-left: calc(var(--spacing) * 5);\n      }\n    }\n  }\n  .group-data-\\[selected\\=true\\]\\:ms-4 {\n    &:is(:where(.group)[data-selected=\"true\"] *) {\n      margin-inline-start: calc(var(--spacing) * 4);\n    }\n  }\n  .group-data-\\[selected\\=true\\]\\:ms-5 {\n    &:is(:where(.group)[data-selected=\"true\"] *) {\n      margin-inline-start: calc(var(--spacing) * 5);\n    }\n  }\n  .group-data-\\[selected\\=true\\]\\:ms-6 {\n    &:is(:where(.group)[data-selected=\"true\"] *) {\n      margin-inline-start: calc(var(--spacing) * 6);\n    }\n  }\n  .group-data-\\[selected\\=true\\]\\:translate-x-3 {\n    &:is(:where(.group)[data-selected=\"true\"] *) {\n      --tw-translate-x: calc(var(--spacing) * 3);\n      translate: var(--tw-translate-x) var(--tw-translate-y);\n    }\n  }\n  .group-data-\\[selected\\=true\\]\\:scale-100 {\n    &:is(:where(.group)[data-selected=\"true\"] *) {\n      --tw-scale-x: 100%;\n      --tw-scale-y: 100%;\n      --tw-scale-z: 100%;\n      scale: var(--tw-scale-x) var(--tw-scale-y);\n    }\n  }\n  .group-data-\\[selected\\=true\\]\\:border-danger {\n    &:is(:where(.group)[data-selected=\"true\"] *) {\n      border-color: hsl(var(--heroui-danger) / 1);\n    }\n  }\n  .group-data-\\[selected\\=true\\]\\:border-default-500 {\n    &:is(:where(.group)[data-selected=\"true\"] *) {\n      border-color: hsl(var(--heroui-default-500) / 1);\n    }\n  }\n  .group-data-\\[selected\\=true\\]\\:border-primary {\n    &:is(:where(.group)[data-selected=\"true\"] *) {\n      border-color: var(--primary);\n    }\n  }\n  .group-data-\\[selected\\=true\\]\\:border-secondary {\n    &:is(:where(.group)[data-selected=\"true\"] *) {\n      border-color: var(--secondary);\n    }\n  }\n  .group-data-\\[selected\\=true\\]\\:border-success {\n    &:is(:where(.group)[data-selected=\"true\"] *) {\n      border-color: hsl(var(--heroui-success) / 1);\n    }\n  }\n  .group-data-\\[selected\\=true\\]\\:border-warning {\n    &:is(:where(.group)[data-selected=\"true\"] *) {\n      border-color: hsl(var(--heroui-warning) / 1);\n    }\n  }\n  .group-data-\\[selected\\=true\\]\\:bg-danger {\n    &:is(:where(.group)[data-selected=\"true\"] *) {\n      background-color: hsl(var(--heroui-danger) / 1);\n    }\n  }\n  .group-data-\\[selected\\=true\\]\\:bg-default-400 {\n    &:is(:where(.group)[data-selected=\"true\"] *) {\n      background-color: hsl(var(--heroui-default-400) / 1);\n    }\n  }\n  .group-data-\\[selected\\=true\\]\\:bg-primary {\n    &:is(:where(.group)[data-selected=\"true\"] *) {\n      background-color: var(--primary);\n    }\n  }\n  .group-data-\\[selected\\=true\\]\\:bg-secondary {\n    &:is(:where(.group)[data-selected=\"true\"] *) {\n      background-color: var(--secondary);\n    }\n  }\n  .group-data-\\[selected\\=true\\]\\:bg-success {\n    &:is(:where(.group)[data-selected=\"true\"] *) {\n      background-color: hsl(var(--heroui-success) / 1);\n    }\n  }\n  .group-data-\\[selected\\=true\\]\\:bg-warning {\n    &:is(:where(.group)[data-selected=\"true\"] *) {\n      background-color: hsl(var(--heroui-warning) / 1);\n    }\n  }\n  .group-data-\\[selected\\=true\\]\\:text-danger {\n    &:is(:where(.group)[data-selected=\"true\"] *) {\n      color: hsl(var(--heroui-danger) / 1);\n    }\n  }\n  .group-data-\\[selected\\=true\\]\\:text-danger-foreground {\n    &:is(:where(.group)[data-selected=\"true\"] *) {\n      color: hsl(var(--heroui-danger-foreground) / 1);\n    }\n  }\n  .group-data-\\[selected\\=true\\]\\:text-default-foreground {\n    &:is(:where(.group)[data-selected=\"true\"] *) {\n      color: hsl(var(--heroui-default-foreground) / 1);\n    }\n  }\n  .group-data-\\[selected\\=true\\]\\:text-foreground {\n    &:is(:where(.group)[data-selected=\"true\"] *) {\n      color: var(--foreground);\n    }\n  }\n  .group-data-\\[selected\\=true\\]\\:text-primary {\n    &:is(:where(.group)[data-selected=\"true\"] *) {\n      color: var(--primary);\n    }\n  }\n  .group-data-\\[selected\\=true\\]\\:text-primary-foreground {\n    &:is(:where(.group)[data-selected=\"true\"] *) {\n      color: var(--primary-foreground);\n    }\n  }\n  .group-data-\\[selected\\=true\\]\\:text-secondary {\n    &:is(:where(.group)[data-selected=\"true\"] *) {\n      color: var(--secondary);\n    }\n  }\n  .group-data-\\[selected\\=true\\]\\:text-secondary-foreground {\n    &:is(:where(.group)[data-selected=\"true\"] *) {\n      color: var(--secondary-foreground);\n    }\n  }\n  .group-data-\\[selected\\=true\\]\\:text-success {\n    &:is(:where(.group)[data-selected=\"true\"] *) {\n      color: hsl(var(--heroui-success) / 1);\n    }\n  }\n  .group-data-\\[selected\\=true\\]\\:text-success-foreground {\n    &:is(:where(.group)[data-selected=\"true\"] *) {\n      color: hsl(var(--heroui-success-foreground) / 1);\n    }\n  }\n  .group-data-\\[selected\\=true\\]\\:text-warning {\n    &:is(:where(.group)[data-selected=\"true\"] *) {\n      color: hsl(var(--heroui-warning) / 1);\n    }\n  }\n  .group-data-\\[selected\\=true\\]\\:text-warning-foreground {\n    &:is(:where(.group)[data-selected=\"true\"] *) {\n      color: hsl(var(--heroui-warning-foreground) / 1);\n    }\n  }\n  .group-data-\\[selected\\=true\\]\\:opacity-0 {\n    &:is(:where(.group)[data-selected=\"true\"] *) {\n      opacity: 0%;\n    }\n  }\n  .group-data-\\[selected\\=true\\]\\:opacity-60 {\n    &:is(:where(.group)[data-selected=\"true\"] *) {\n      opacity: 60%;\n    }\n  }\n  .group-data-\\[selected\\=true\\]\\:opacity-100 {\n    &:is(:where(.group)[data-selected=\"true\"] *) {\n      opacity: 100%;\n    }\n  }\n  .group-data-\\[side\\=left\\]\\:-right-4 {\n    &:is(:where(.group)[data-side=\"left\"] *) {\n      right: calc(var(--spacing) * -4);\n    }\n  }\n  .group-data-\\[side\\=left\\]\\:border-r {\n    &:is(:where(.group)[data-side=\"left\"] *) {\n      border-right-style: var(--tw-border-style);\n      border-right-width: 1px;\n    }\n  }\n  .group-data-\\[side\\=right\\]\\:left-0 {\n    &:is(:where(.group)[data-side=\"right\"] *) {\n      left: calc(var(--spacing) * 0);\n    }\n  }\n  .group-data-\\[side\\=right\\]\\:rotate-180 {\n    &:is(:where(.group)[data-side=\"right\"] *) {\n      rotate: 180deg;\n    }\n  }\n  .group-data-\\[side\\=right\\]\\:border-l {\n    &:is(:where(.group)[data-side=\"right\"] *) {\n      border-left-style: var(--tw-border-style);\n      border-left-width: 1px;\n    }\n  }\n  .group-data-\\[state\\=open\\]\\:rotate-180 {\n    &:is(:where(.group)[data-state=\"open\"] *) {\n      rotate: 180deg;\n    }\n  }\n  .group-data-\\[variant\\=floating\\]\\:rounded-lg {\n    &:is(:where(.group)[data-variant=\"floating\"] *) {\n      border-radius: var(--radius);\n    }\n  }\n  .group-data-\\[variant\\=floating\\]\\:border {\n    &:is(:where(.group)[data-variant=\"floating\"] *) {\n      border-style: var(--tw-border-style);\n      border-width: 1px;\n    }\n  }\n  .group-data-\\[variant\\=floating\\]\\:border-sidebar-border {\n    &:is(:where(.group)[data-variant=\"floating\"] *) {\n      border-color: var(--sidebar-border);\n    }\n  }\n  .group-data-\\[variant\\=floating\\]\\:shadow-sm {\n    &:is(:where(.group)[data-variant=\"floating\"] *) {\n      --tw-shadow: 0 1px 3px 0 var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 1px 2px -1px var(--tw-shadow-color, rgb(0 0 0 / 0.1));\n      box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\n    }\n  }\n  .group-data-\\[vaul-drawer-direction\\=bottom\\]\\/drawer-content\\:block {\n    &:is(:where(.group\\/drawer-content)[data-vaul-drawer-direction=\"bottom\"] *) {\n      display: block;\n    }\n  }\n  .group-data-\\[viewport\\=false\\]\\/navigation-menu\\:top-full {\n    &:is(:where(.group\\/navigation-menu)[data-viewport=\"false\"] *) {\n      top: 100%;\n    }\n  }\n  .group-data-\\[viewport\\=false\\]\\/navigation-menu\\:mt-1\\.5 {\n    &:is(:where(.group\\/navigation-menu)[data-viewport=\"false\"] *) {\n      margin-top: calc(var(--spacing) * 1.5);\n    }\n  }\n  .group-data-\\[viewport\\=false\\]\\/navigation-menu\\:overflow-hidden {\n    &:is(:where(.group\\/navigation-menu)[data-viewport=\"false\"] *) {\n      overflow: hidden;\n    }\n  }\n  .group-data-\\[viewport\\=false\\]\\/navigation-menu\\:rounded-md {\n    &:is(:where(.group\\/navigation-menu)[data-viewport=\"false\"] *) {\n      border-radius: calc(var(--radius) - 2px);\n    }\n  }\n  .group-data-\\[viewport\\=false\\]\\/navigation-menu\\:border {\n    &:is(:where(.group\\/navigation-menu)[data-viewport=\"false\"] *) {\n      border-style: var(--tw-border-style);\n      border-width: 1px;\n    }\n  }\n  .group-data-\\[viewport\\=false\\]\\/navigation-menu\\:bg-popover {\n    &:is(:where(.group\\/navigation-menu)[data-viewport=\"false\"] *) {\n      background-color: var(--popover);\n    }\n  }\n  .group-data-\\[viewport\\=false\\]\\/navigation-menu\\:text-popover-foreground {\n    &:is(:where(.group\\/navigation-menu)[data-viewport=\"false\"] *) {\n      color: var(--popover-foreground);\n    }\n  }\n  .group-data-\\[viewport\\=false\\]\\/navigation-menu\\:shadow {\n    &:is(:where(.group\\/navigation-menu)[data-viewport=\"false\"] *) {\n      --tw-shadow: 0 1px 3px 0 var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 1px 2px -1px var(--tw-shadow-color, rgb(0 0 0 / 0.1));\n      box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\n    }\n  }\n  .group-data-\\[viewport\\=false\\]\\/navigation-menu\\:duration-200 {\n    &:is(:where(.group\\/navigation-menu)[data-viewport=\"false\"] *) {\n      --tw-duration: 200ms;\n      transition-duration: 200ms;\n    }\n  }\n  .peer-hover\\/menu-button\\:text-sidebar-accent-foreground {\n    &:is(:where(.peer\\/menu-button):hover ~ *) {\n      @media (hover: hover) {\n        color: var(--sidebar-accent-foreground);\n      }\n    }\n  }\n  .peer-disabled\\:cursor-not-allowed {\n    &:is(:where(.peer):disabled ~ *) {\n      cursor: not-allowed;\n    }\n  }\n  .peer-disabled\\:opacity-50 {\n    &:is(:where(.peer):disabled ~ *) {\n      opacity: 50%;\n    }\n  }\n  .peer-disabled\\:opacity-70 {\n    &:is(:where(.peer):disabled ~ *) {\n      opacity: 70%;\n    }\n  }\n  .peer-data-\\[active\\=true\\]\\/menu-button\\:text-sidebar-accent-foreground {\n    &:is(:where(.peer\\/menu-button)[data-active=\"true\"] ~ *) {\n      color: var(--sidebar-accent-foreground);\n    }\n  }\n  .peer-data-\\[filled\\=true\\]\\:pointer-events-auto {\n    &:is(:where(.peer)[data-filled=\"true\"] ~ *) {\n      pointer-events: auto;\n    }\n  }\n  .peer-data-\\[filled\\=true\\]\\:block {\n    &:is(:where(.peer)[data-filled=\"true\"] ~ *) {\n      display: block;\n    }\n  }\n  .peer-data-\\[filled\\=true\\]\\:scale-100 {\n    &:is(:where(.peer)[data-filled=\"true\"] ~ *) {\n      --tw-scale-x: 100%;\n      --tw-scale-y: 100%;\n      --tw-scale-z: 100%;\n      scale: var(--tw-scale-x) var(--tw-scale-y);\n    }\n  }\n  .peer-data-\\[filled\\=true\\]\\:opacity-70 {\n    &:is(:where(.peer)[data-filled=\"true\"] ~ *) {\n      opacity: 70%;\n    }\n  }\n  .peer-data-\\[size\\=default\\]\\/menu-button\\:top-1\\.5 {\n    &:is(:where(.peer\\/menu-button)[data-size=\"default\"] ~ *) {\n      top: calc(var(--spacing) * 1.5);\n    }\n  }\n  .peer-data-\\[size\\=lg\\]\\/menu-button\\:top-2\\.5 {\n    &:is(:where(.peer\\/menu-button)[data-size=\"lg\"] ~ *) {\n      top: calc(var(--spacing) * 2.5);\n    }\n  }\n  .peer-data-\\[size\\=sm\\]\\/menu-button\\:top-1 {\n    &:is(:where(.peer\\/menu-button)[data-size=\"sm\"] ~ *) {\n      top: calc(var(--spacing) * 1);\n    }\n  }\n  .selection\\:bg-primary {\n    & *::selection {\n      background-color: var(--primary);\n    }\n    &::selection {\n      background-color: var(--primary);\n    }\n  }\n  .selection\\:text-primary-foreground {\n    & *::selection {\n      color: var(--primary-foreground);\n    }\n    &::selection {\n      color: var(--primary-foreground);\n    }\n  }\n  .file\\:inline-flex {\n    &::file-selector-button {\n      display: inline-flex;\n    }\n  }\n  .file\\:h-7 {\n    &::file-selector-button {\n      height: calc(var(--spacing) * 7);\n    }\n  }\n  .file\\:cursor-pointer {\n    &::file-selector-button {\n      cursor: pointer;\n    }\n  }\n  .file\\:border-0 {\n    &::file-selector-button {\n      border-style: var(--tw-border-style);\n      border-width: 0px;\n    }\n  }\n  .file\\:bg-transparent {\n    &::file-selector-button {\n      background-color: transparent;\n    }\n  }\n  .file\\:text-sm {\n    &::file-selector-button {\n      font-size: var(--text-sm);\n      line-height: var(--tw-leading, var(--text-sm--line-height));\n    }\n  }\n  .file\\:font-medium {\n    &::file-selector-button {\n      --tw-font-weight: var(--font-weight-medium);\n      font-weight: var(--font-weight-medium);\n    }\n  }\n  .file\\:text-foreground {\n    &::file-selector-button {\n      color: var(--foreground);\n    }\n  }\n  .placeholder\\:text-danger {\n    &::placeholder {\n      color: hsl(var(--heroui-danger) / 1);\n    }\n  }\n  .placeholder\\:text-foreground-500 {\n    &::placeholder {\n      color: hsl(var(--heroui-foreground-500) / 1);\n    }\n  }\n  .placeholder\\:text-muted-foreground {\n    &::placeholder {\n      color: var(--muted-foreground);\n    }\n  }\n  .placeholder\\:text-primary {\n    &::placeholder {\n      color: var(--primary);\n    }\n  }\n  .placeholder\\:text-secondary {\n    &::placeholder {\n      color: var(--secondary);\n    }\n  }\n  .placeholder\\:text-success-600 {\n    &::placeholder {\n      color: hsl(var(--heroui-success-600) / 1);\n    }\n  }\n  .placeholder\\:text-warning-600 {\n    &::placeholder {\n      color: hsl(var(--heroui-warning-600) / 1);\n    }\n  }\n  .before\\:pointer-events-auto {\n    &::before {\n      content: var(--tw-content);\n      pointer-events: auto;\n    }\n  }\n  .before\\:absolute {\n    &::before {\n      content: var(--tw-content);\n      position: absolute;\n    }\n  }\n  .before\\:inset-0 {\n    &::before {\n      content: var(--tw-content);\n      inset: calc(var(--spacing) * 0);\n    }\n  }\n  .before\\:top-\\[calc\\(-1\\*var\\(--top-extension\\,16px\\)\\)\\] {\n    &::before {\n      content: var(--tw-content);\n      top: calc(-1 * var(--top-extension,16px));\n    }\n  }\n  .before\\:right-0 {\n    &::before {\n      content: var(--tw-content);\n      right: calc(var(--spacing) * 0);\n    }\n  }\n  .before\\:left-0 {\n    &::before {\n      content: var(--tw-content);\n      left: calc(var(--spacing) * 0);\n    }\n  }\n  .before\\:z-0 {\n    &::before {\n      content: var(--tw-content);\n      z-index: 0;\n    }\n  }\n  .before\\:z-\\[-1\\] {\n    &::before {\n      content: var(--tw-content);\n      z-index: -1;\n    }\n  }\n  .before\\:box-border {\n    &::before {\n      content: var(--tw-content);\n      box-sizing: border-box;\n    }\n  }\n  .before\\:block {\n    &::before {\n      content: var(--tw-content);\n      display: block;\n    }\n  }\n  .before\\:hidden {\n    &::before {\n      content: var(--tw-content);\n      display: none;\n    }\n  }\n  .before\\:h-0\\.5 {\n    &::before {\n      content: var(--tw-content);\n      height: calc(var(--spacing) * 0.5);\n    }\n  }\n  .before\\:h-2\\.5 {\n    &::before {\n      content: var(--tw-content);\n      height: calc(var(--spacing) * 2.5);\n    }\n  }\n  .before\\:h-4 {\n    &::before {\n      content: var(--tw-content);\n      height: calc(var(--spacing) * 4);\n    }\n  }\n  .before\\:h-6 {\n    &::before {\n      content: var(--tw-content);\n      height: calc(var(--spacing) * 6);\n    }\n  }\n  .before\\:h-8 {\n    &::before {\n      content: var(--tw-content);\n      height: calc(var(--spacing) * 8);\n    }\n  }\n  .before\\:h-11 {\n    &::before {\n      content: var(--tw-content);\n      height: calc(var(--spacing) * 11);\n    }\n  }\n  .before\\:h-\\[var\\(--top-extension\\,16px\\)\\] {\n    &::before {\n      content: var(--tw-content);\n      height: var(--top-extension,16px);\n    }\n  }\n  .before\\:h-px {\n    &::before {\n      content: var(--tw-content);\n      height: 1px;\n    }\n  }\n  .before\\:w-0 {\n    &::before {\n      content: var(--tw-content);\n      width: calc(var(--spacing) * 0);\n    }\n  }\n  .before\\:w-2\\.5 {\n    &::before {\n      content: var(--tw-content);\n      width: calc(var(--spacing) * 2.5);\n    }\n  }\n  .before\\:w-6 {\n    &::before {\n      content: var(--tw-content);\n      width: calc(var(--spacing) * 6);\n    }\n  }\n  .before\\:w-8 {\n    &::before {\n      content: var(--tw-content);\n      width: calc(var(--spacing) * 8);\n    }\n  }\n  .before\\:w-11 {\n    &::before {\n      content: var(--tw-content);\n      width: calc(var(--spacing) * 11);\n    }\n  }\n  .before\\:-translate-x-full {\n    &::before {\n      content: var(--tw-content);\n      --tw-translate-x: -100%;\n      translate: var(--tw-translate-x) var(--tw-translate-y);\n    }\n  }\n  .before\\:-translate-y-1 {\n    &::before {\n      content: var(--tw-content);\n      --tw-translate-y: calc(var(--spacing) * -1);\n      translate: var(--tw-translate-x) var(--tw-translate-y);\n    }\n  }\n  .before\\:rotate-0 {\n    &::before {\n      content: var(--tw-content);\n      rotate: 0deg;\n    }\n  }\n  .before\\:rotate-45 {\n    &::before {\n      content: var(--tw-content);\n      rotate: 45deg;\n    }\n  }\n  .before\\:animate-\\[shimmer_2s_infinite\\] {\n    &::before {\n      content: var(--tw-content);\n      animation: shimmer 2s infinite;\n    }\n  }\n  .before\\:animate-none {\n    &::before {\n      content: var(--tw-content);\n      animation: none;\n    }\n  }\n  .before\\:rounded-\\[calc\\(theme\\(borderRadius\\.medium\\)\\*0\\.5\\)\\] {\n    &::before {\n      content: var(--tw-content);\n      border-radius: calc(var(--heroui-radius-medium) * 0.5);\n    }\n  }\n  .before\\:rounded-\\[calc\\(theme\\(borderRadius\\.medium\\)\\*0\\.6\\)\\] {\n    &::before {\n      content: var(--tw-content);\n      border-radius: calc(var(--heroui-radius-medium) * 0.6);\n    }\n  }\n  .before\\:rounded-\\[calc\\(theme\\(borderRadius\\.medium\\)\\*0\\.7\\)\\] {\n    &::before {\n      content: var(--tw-content);\n      border-radius: calc(var(--heroui-radius-medium) * 0.7);\n    }\n  }\n  .before\\:rounded-full {\n    &::before {\n      content: var(--tw-content);\n      border-radius: calc(infinity * 1px);\n    }\n  }\n  .before\\:rounded-none {\n    &::before {\n      content: var(--tw-content);\n      border-radius: 0;\n    }\n  }\n  .before\\:rounded-sm {\n    &::before {\n      content: var(--tw-content);\n      border-radius: calc(var(--radius) - 4px);\n    }\n  }\n  .before\\:border-2 {\n    &::before {\n      content: var(--tw-content);\n      border-style: var(--tw-border-style);\n      border-width: 2px;\n    }\n  }\n  .before\\:border-t {\n    &::before {\n      content: var(--tw-content);\n      border-top-style: var(--tw-border-style);\n      border-top-width: 1px;\n    }\n  }\n  .before\\:border-solid {\n    &::before {\n      content: var(--tw-content);\n      --tw-border-style: solid;\n      border-style: solid;\n    }\n  }\n  .before\\:border-content4\\/30 {\n    &::before {\n      content: var(--tw-content);\n      border-color: hsl(var(--heroui-content4) / 1);\n      @supports (color: color-mix(in lab, red, red)) {\n        border-color: color-mix(in oklab, hsl(var(--heroui-content4) / 1) 30%, transparent);\n      }\n    }\n  }\n  .before\\:border-danger {\n    &::before {\n      content: var(--tw-content);\n      border-color: hsl(var(--heroui-danger) / 1);\n    }\n  }\n  .before\\:border-default {\n    &::before {\n      content: var(--tw-content);\n      border-color: hsl(var(--heroui-default) / 1);\n    }\n  }\n  .before\\:bg-content1 {\n    &::before {\n      content: var(--tw-content);\n      background-color: hsl(var(--heroui-content1) / 1);\n    }\n  }\n  .before\\:bg-current {\n    &::before {\n      content: var(--tw-content);\n      background-color: currentcolor;\n    }\n  }\n  .before\\:bg-danger {\n    &::before {\n      content: var(--tw-content);\n      background-color: hsl(var(--heroui-danger) / 1);\n    }\n  }\n  .before\\:bg-danger\\/20 {\n    &::before {\n      content: var(--tw-content);\n      background-color: hsl(var(--heroui-danger) / 1);\n      @supports (color: color-mix(in lab, red, red)) {\n        background-color: color-mix(in oklab, hsl(var(--heroui-danger) / 1) 20%, transparent);\n      }\n    }\n  }\n  .before\\:bg-default\\/60 {\n    &::before {\n      content: var(--tw-content);\n      background-color: hsl(var(--heroui-default) / 1);\n      @supports (color: color-mix(in lab, red, red)) {\n        background-color: color-mix(in oklab, hsl(var(--heroui-default) / 1) 60%, transparent);\n      }\n    }\n  }\n  .before\\:bg-foreground {\n    &::before {\n      content: var(--tw-content);\n      background-color: var(--foreground);\n    }\n  }\n  .before\\:bg-primary {\n    &::before {\n      content: var(--tw-content);\n      background-color: var(--primary);\n    }\n  }\n  .before\\:bg-primary\\/20 {\n    &::before {\n      content: var(--tw-content);\n      background-color: var(--primary);\n      @supports (color: color-mix(in lab, red, red)) {\n        background-color: color-mix(in oklab, var(--primary) 20%, transparent);\n      }\n    }\n  }\n  .before\\:bg-secondary {\n    &::before {\n      content: var(--tw-content);\n      background-color: var(--secondary);\n    }\n  }\n  .before\\:bg-secondary\\/20 {\n    &::before {\n      content: var(--tw-content);\n      background-color: var(--secondary);\n      @supports (color: color-mix(in lab, red, red)) {\n        background-color: color-mix(in oklab, var(--secondary) 20%, transparent);\n      }\n    }\n  }\n  .before\\:bg-success {\n    &::before {\n      content: var(--tw-content);\n      background-color: hsl(var(--heroui-success) / 1);\n    }\n  }\n  .before\\:bg-success\\/20 {\n    &::before {\n      content: var(--tw-content);\n      background-color: hsl(var(--heroui-success) / 1);\n      @supports (color: color-mix(in lab, red, red)) {\n        background-color: color-mix(in oklab, hsl(var(--heroui-success) / 1) 20%, transparent);\n      }\n    }\n  }\n  .before\\:bg-transparent {\n    &::before {\n      content: var(--tw-content);\n      background-color: transparent;\n    }\n  }\n  .before\\:bg-warning {\n    &::before {\n      content: var(--tw-content);\n      background-color: hsl(var(--heroui-warning) / 1);\n    }\n  }\n  .before\\:bg-warning\\/20 {\n    &::before {\n      content: var(--tw-content);\n      background-color: hsl(var(--heroui-warning) / 1);\n      @supports (color: color-mix(in lab, red, red)) {\n        background-color: color-mix(in oklab, hsl(var(--heroui-warning) / 1) 20%, transparent);\n      }\n    }\n  }\n  .before\\:bg-gradient-to-r {\n    &::before {\n      content: var(--tw-content);\n      --tw-gradient-position: to right in oklab;\n      background-image: linear-gradient(var(--tw-gradient-stops));\n    }\n  }\n  .before\\:from-transparent {\n    &::before {\n      content: var(--tw-content);\n      --tw-gradient-from: transparent;\n      --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n    }\n  }\n  .before\\:via-content4 {\n    &::before {\n      content: var(--tw-content);\n      --tw-gradient-via: hsl(var(--heroui-content4) / 1);\n      --tw-gradient-via-stops: var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-via) var(--tw-gradient-via-position), var(--tw-gradient-to) var(--tw-gradient-to-position);\n      --tw-gradient-stops: var(--tw-gradient-via-stops);\n    }\n  }\n  .before\\:to-transparent {\n    &::before {\n      content: var(--tw-content);\n      --tw-gradient-to: transparent;\n      --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n    }\n  }\n  .before\\:opacity-0 {\n    &::before {\n      content: var(--tw-content);\n      opacity: 0%;\n    }\n  }\n  .before\\:opacity-100 {\n    &::before {\n      content: var(--tw-content);\n      opacity: 100%;\n    }\n  }\n  .before\\:shadow-small {\n    &::before {\n      content: var(--tw-content);\n      --tw-shadow: var(--heroui-box-shadow-small);\n      box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\n    }\n  }\n  .before\\:transition-colors {\n    &::before {\n      content: var(--tw-content);\n      transition-property: color, background-color, border-color, outline-color, text-decoration-color, fill, stroke, --tw-gradient-from, --tw-gradient-via, --tw-gradient-to;\n      transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));\n      transition-duration: var(--tw-duration, var(--default-transition-duration));\n    }\n  }\n  .before\\:transition-transform {\n    &::before {\n      content: var(--tw-content);\n      transition-property: transform, translate, scale, rotate;\n      transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));\n      transition-duration: var(--tw-duration, var(--default-transition-duration));\n    }\n  }\n  .before\\:transition-width {\n    &::before {\n      content: var(--tw-content);\n      transition-property: width;\n      transition-timing-function: ease;\n      transition-duration: 250ms;\n    }\n  }\n  .before\\:transition-none {\n    &::before {\n      content: var(--tw-content);\n      transition-property: none;\n    }\n  }\n  .before\\:duration-150 {\n    &::before {\n      content: var(--tw-content);\n      --tw-duration: 150ms;\n      transition-duration: 150ms;\n    }\n  }\n  .before\\:content-\\[\\'\\'\\] {\n    &::before {\n      content: var(--tw-content);\n      --tw-content: '';\n      content: var(--tw-content);\n    }\n  }\n  .group-data-\\[hover\\=true\\]\\:before\\:bg-default-100 {\n    &:is(:where(.group)[data-hover=\"true\"] *) {\n      &::before {\n        content: var(--tw-content);\n        background-color: hsl(var(--heroui-default-100) / 1);\n      }\n    }\n  }\n  .group-aria-\\[selected\\=false\\]\\/tr\\:group-data-\\[hover\\=true\\]\\/tr\\:before\\:bg-default-100 {\n    &:is(:where(.group\\/tr)[aria-selected=\"false\"] *) {\n      &:is(:where(.group\\/tr)[data-hover=\"true\"] *) {\n        &::before {\n          content: var(--tw-content);\n          background-color: hsl(var(--heroui-default-100) / 1);\n        }\n      }\n    }\n  }\n  .group-aria-\\[selected\\=false\\]\\/tr\\:group-data-\\[hover\\=true\\]\\/tr\\:before\\:opacity-70 {\n    &:is(:where(.group\\/tr)[aria-selected=\"false\"] *) {\n      &:is(:where(.group\\/tr)[data-hover=\"true\"] *) {\n        &::before {\n          content: var(--tw-content);\n          opacity: 70%;\n        }\n      }\n    }\n  }\n  .group-data-\\[middle\\=true\\]\\/tr\\:before\\:rounded-none {\n    &:is(:where(.group\\/tr)[data-middle=\"true\"] *) {\n      &::before {\n        content: var(--tw-content);\n        border-radius: 0;\n      }\n    }\n  }\n  .group-data-\\[odd\\=true\\]\\/tr\\:before\\:-z-10 {\n    &:is(:where(.group\\/tr)[data-odd=\"true\"] *) {\n      &::before {\n        content: var(--tw-content);\n        z-index: calc(10 * -1);\n      }\n    }\n  }\n  .group-data-\\[odd\\=true\\]\\/tr\\:before\\:bg-default-100 {\n    &:is(:where(.group\\/tr)[data-odd=\"true\"] *) {\n      &::before {\n        content: var(--tw-content);\n        background-color: hsl(var(--heroui-default-100) / 1);\n      }\n    }\n  }\n  .group-data-\\[odd\\=true\\]\\/tr\\:before\\:opacity-100 {\n    &:is(:where(.group\\/tr)[data-odd=\"true\"] *) {\n      &::before {\n        content: var(--tw-content);\n        opacity: 100%;\n      }\n    }\n  }\n  .group-data-\\[open\\=true\\]\\:before\\:translate-y-px {\n    &:is(:where(.group)[data-open=\"true\"] *) {\n      &::before {\n        content: var(--tw-content);\n        --tw-translate-y: 1px;\n        translate: var(--tw-translate-x) var(--tw-translate-y);\n      }\n    }\n  }\n  .group-data-\\[open\\=true\\]\\:before\\:rotate-45 {\n    &:is(:where(.group)[data-open=\"true\"] *) {\n      &::before {\n        content: var(--tw-content);\n        rotate: 45deg;\n      }\n    }\n  }\n  .group-data-\\[selected\\=true\\]\\:before\\:w-full {\n    &:is(:where(.group)[data-selected=\"true\"] *) {\n      &::before {\n        content: var(--tw-content);\n        width: 100%;\n      }\n    }\n  }\n  .after\\:pointer-events-auto {\n    &::after {\n      content: var(--tw-content);\n      pointer-events: auto;\n    }\n  }\n  .after\\:absolute {\n    &::after {\n      content: var(--tw-content);\n      position: absolute;\n    }\n  }\n  .after\\:-inset-2 {\n    &::after {\n      content: var(--tw-content);\n      inset: calc(var(--spacing) * -2);\n    }\n  }\n  .after\\:inset-0 {\n    &::after {\n      content: var(--tw-content);\n      inset: calc(var(--spacing) * 0);\n    }\n  }\n  .after\\:inset-y-0 {\n    &::after {\n      content: var(--tw-content);\n      inset-block: calc(var(--spacing) * 0);\n    }\n  }\n  .after\\:top-0 {\n    &::after {\n      content: var(--tw-content);\n      top: calc(var(--spacing) * 0);\n    }\n  }\n  .after\\:top-1\\/2 {\n    &::after {\n      content: var(--tw-content);\n      top: calc(1/2 * 100%);\n    }\n  }\n  .after\\:right-0 {\n    &::after {\n      content: var(--tw-content);\n      right: calc(var(--spacing) * 0);\n    }\n  }\n  .after\\:-bottom-1 {\n    &::after {\n      content: var(--tw-content);\n      bottom: calc(var(--spacing) * -1);\n    }\n  }\n  .after\\:-bottom-\\[2px\\] {\n    &::after {\n      content: var(--tw-content);\n      bottom: calc(2px * -1);\n    }\n  }\n  .after\\:bottom-0 {\n    &::after {\n      content: var(--tw-content);\n      bottom: calc(var(--spacing) * 0);\n    }\n  }\n  .after\\:bottom-\\[calc\\(-1\\*var\\(--bottom-extension\\,16px\\)\\)\\] {\n    &::after {\n      content: var(--tw-content);\n      bottom: calc(-1 * var(--bottom-extension,16px));\n    }\n  }\n  .after\\:left-0 {\n    &::after {\n      content: var(--tw-content);\n      left: calc(var(--spacing) * 0);\n    }\n  }\n  .after\\:left-1\\/2 {\n    &::after {\n      content: var(--tw-content);\n      left: calc(1/2 * 100%);\n    }\n  }\n  .after\\:-z-10 {\n    &::after {\n      content: var(--tw-content);\n      z-index: calc(10 * -1);\n    }\n  }\n  .after\\:z-0 {\n    &::after {\n      content: var(--tw-content);\n      z-index: 0;\n    }\n  }\n  .after\\:z-\\[-1\\] {\n    &::after {\n      content: var(--tw-content);\n      z-index: -1;\n    }\n  }\n  .after\\:ms-0\\.5 {\n    &::after {\n      content: var(--tw-content);\n      margin-inline-start: calc(var(--spacing) * 0.5);\n    }\n  }\n  .after\\:ml-0\\.5 {\n    &::after {\n      content: var(--tw-content);\n      margin-left: calc(var(--spacing) * 0.5);\n    }\n  }\n  .after\\:block {\n    &::after {\n      content: var(--tw-content);\n      display: block;\n    }\n  }\n  .after\\:flex {\n    &::after {\n      content: var(--tw-content);\n      display: flex;\n    }\n  }\n  .after\\:h-0 {\n    &::after {\n      content: var(--tw-content);\n      height: calc(var(--spacing) * 0);\n    }\n  }\n  .after\\:h-4 {\n    &::after {\n      content: var(--tw-content);\n      height: calc(var(--spacing) * 4);\n    }\n  }\n  .after\\:h-5 {\n    &::after {\n      content: var(--tw-content);\n      height: calc(var(--spacing) * 5);\n    }\n  }\n  .after\\:h-\\[2px\\] {\n    &::after {\n      content: var(--tw-content);\n      height: 2px;\n    }\n  }\n  .after\\:h-\\[var\\(--bottom-extension\\,16px\\)\\] {\n    &::after {\n      content: var(--tw-content);\n      height: var(--bottom-extension,16px);\n    }\n  }\n  .after\\:h-divider {\n    &::after {\n      content: var(--tw-content);\n      height: var(--heroui-divider-weight);\n    }\n  }\n  .after\\:h-full {\n    &::after {\n      content: var(--tw-content);\n      height: 100%;\n    }\n  }\n  .after\\:h-px {\n    &::after {\n      content: var(--tw-content);\n      height: 1px;\n    }\n  }\n  .after\\:w-0 {\n    &::after {\n      content: var(--tw-content);\n      width: calc(var(--spacing) * 0);\n    }\n  }\n  .after\\:w-1 {\n    &::after {\n      content: var(--tw-content);\n      width: calc(var(--spacing) * 1);\n    }\n  }\n  .after\\:w-4 {\n    &::after {\n      content: var(--tw-content);\n      width: calc(var(--spacing) * 4);\n    }\n  }\n  .after\\:w-5 {\n    &::after {\n      content: var(--tw-content);\n      width: calc(var(--spacing) * 5);\n    }\n  }\n  .after\\:w-6 {\n    &::after {\n      content: var(--tw-content);\n      width: calc(var(--spacing) * 6);\n    }\n  }\n  .after\\:w-\\[2px\\] {\n    &::after {\n      content: var(--tw-content);\n      width: 2px;\n    }\n  }\n  .after\\:w-\\[80\\%\\] {\n    &::after {\n      content: var(--tw-content);\n      width: 80%;\n    }\n  }\n  .after\\:w-full {\n    &::after {\n      content: var(--tw-content);\n      width: 100%;\n    }\n  }\n  .after\\:origin-center {\n    &::after {\n      content: var(--tw-content);\n      transform-origin: center;\n    }\n  }\n  .after\\:-translate-x-1\\/2 {\n    &::after {\n      content: var(--tw-content);\n      --tw-translate-x: calc(calc(1/2 * 100%) * -1);\n      translate: var(--tw-translate-x) var(--tw-translate-y);\n    }\n  }\n  .after\\:translate-y-1 {\n    &::after {\n      content: var(--tw-content);\n      --tw-translate-y: calc(var(--spacing) * 1);\n      translate: var(--tw-translate-x) var(--tw-translate-y);\n    }\n  }\n  .after\\:scale-50 {\n    &::after {\n      content: var(--tw-content);\n      --tw-scale-x: 50%;\n      --tw-scale-y: 50%;\n      --tw-scale-z: 50%;\n      scale: var(--tw-scale-x) var(--tw-scale-y);\n    }\n  }\n  .after\\:rotate-0 {\n    &::after {\n      content: var(--tw-content);\n      rotate: 0deg;\n    }\n  }\n  .after\\:items-center {\n    &::after {\n      content: var(--tw-content);\n      align-items: center;\n    }\n  }\n  .after\\:rounded-\\[calc\\(theme\\(borderRadius\\.large\\)\\/2\\)\\] {\n    &::after {\n      content: var(--tw-content);\n      border-radius: calc(var(--heroui-radius-large) / 2);\n    }\n  }\n  .after\\:rounded-\\[calc\\(theme\\(borderRadius\\.medium\\)\\*0\\.5\\)\\] {\n    &::after {\n      content: var(--tw-content);\n      border-radius: calc(var(--heroui-radius-medium) * 0.5);\n    }\n  }\n  .after\\:rounded-\\[calc\\(theme\\(borderRadius\\.medium\\)\\*0\\.6\\)\\] {\n    &::after {\n      content: var(--tw-content);\n      border-radius: calc(var(--heroui-radius-medium) * 0.6);\n    }\n  }\n  .after\\:rounded-\\[calc\\(theme\\(borderRadius\\.medium\\)\\*0\\.7\\)\\] {\n    &::after {\n      content: var(--tw-content);\n      border-radius: calc(var(--heroui-radius-medium) * 0.7);\n    }\n  }\n  .after\\:rounded-\\[calc\\(theme\\(borderRadius\\.medium\\)\\/3\\)\\] {\n    &::after {\n      content: var(--tw-content);\n      border-radius: calc(var(--heroui-radius-medium) / 3);\n    }\n  }\n  .after\\:rounded-\\[calc\\(theme\\(borderRadius\\.small\\)\\/3\\)\\] {\n    &::after {\n      content: var(--tw-content);\n      border-radius: calc(var(--heroui-radius-small) / 3);\n    }\n  }\n  .after\\:rounded-full {\n    &::after {\n      content: var(--tw-content);\n      border-radius: calc(infinity * 1px);\n    }\n  }\n  .after\\:rounded-none {\n    &::after {\n      content: var(--tw-content);\n      border-radius: 0;\n    }\n  }\n  .after\\:rounded-xl {\n    &::after {\n      content: var(--tw-content);\n      border-radius: calc(var(--radius) + 4px);\n    }\n  }\n  .after\\:border-t {\n    &::after {\n      content: var(--tw-content);\n      border-top-style: var(--tw-border-style);\n      border-top-width: 1px;\n    }\n  }\n  .after\\:border-border {\n    &::after {\n      content: var(--tw-content);\n      border-color: var(--border);\n    }\n  }\n  .after\\:\\!bg-danger {\n    &::after {\n      content: var(--tw-content);\n      background-color: hsl(var(--heroui-danger) / 1) !important;\n    }\n  }\n  .after\\:bg-background {\n    &::after {\n      content: var(--tw-content);\n      background-color: var(--background);\n    }\n  }\n  .after\\:bg-content1 {\n    &::after {\n      content: var(--tw-content);\n      background-color: hsl(var(--heroui-content1) / 1);\n    }\n  }\n  .after\\:bg-content3 {\n    &::after {\n      content: var(--tw-content);\n      background-color: hsl(var(--heroui-content3) / 1);\n    }\n  }\n  .after\\:bg-current {\n    &::after {\n      content: var(--tw-content);\n      background-color: currentcolor;\n    }\n  }\n  .after\\:bg-danger {\n    &::after {\n      content: var(--tw-content);\n      background-color: hsl(var(--heroui-danger) / 1);\n    }\n  }\n  .after\\:bg-default {\n    &::after {\n      content: var(--tw-content);\n      background-color: hsl(var(--heroui-default) / 1);\n    }\n  }\n  .after\\:bg-default-foreground {\n    &::after {\n      content: var(--tw-content);\n      background-color: hsl(var(--heroui-default-foreground) / 1);\n    }\n  }\n  .after\\:bg-divider {\n    &::after {\n      content: var(--tw-content);\n      background-color: hsl(var(--heroui-divider) / 0.15);\n    }\n  }\n  .after\\:bg-foreground {\n    &::after {\n      content: var(--tw-content);\n      background-color: var(--foreground);\n    }\n  }\n  .after\\:bg-primary {\n    &::after {\n      content: var(--tw-content);\n      background-color: var(--primary);\n    }\n  }\n  .after\\:bg-secondary {\n    &::after {\n      content: var(--tw-content);\n      background-color: var(--secondary);\n    }\n  }\n  .after\\:bg-success {\n    &::after {\n      content: var(--tw-content);\n      background-color: hsl(var(--heroui-success) / 1);\n    }\n  }\n  .after\\:bg-transparent {\n    &::after {\n      content: var(--tw-content);\n      background-color: transparent;\n    }\n  }\n  .after\\:bg-warning {\n    &::after {\n      content: var(--tw-content);\n      background-color: hsl(var(--heroui-warning) / 1);\n    }\n  }\n  .after\\:text-danger {\n    &::after {\n      content: var(--tw-content);\n      color: hsl(var(--heroui-danger) / 1);\n    }\n  }\n  .after\\:text-danger-foreground {\n    &::after {\n      content: var(--tw-content);\n      color: hsl(var(--heroui-danger-foreground) / 1);\n    }\n  }\n  .after\\:text-default-foreground {\n    &::after {\n      content: var(--tw-content);\n      color: hsl(var(--heroui-default-foreground) / 1);\n    }\n  }\n  .after\\:text-primary-foreground {\n    &::after {\n      content: var(--tw-content);\n      color: var(--primary-foreground);\n    }\n  }\n  .after\\:text-secondary-foreground {\n    &::after {\n      content: var(--tw-content);\n      color: var(--secondary-foreground);\n    }\n  }\n  .after\\:text-success-foreground {\n    &::after {\n      content: var(--tw-content);\n      color: hsl(var(--heroui-success-foreground) / 1);\n    }\n  }\n  .after\\:text-warning-foreground {\n    &::after {\n      content: var(--tw-content);\n      color: hsl(var(--heroui-warning-foreground) / 1);\n    }\n  }\n  .after\\:opacity-0 {\n    &::after {\n      content: var(--tw-content);\n      opacity: 0%;\n    }\n  }\n  .after\\:opacity-100 {\n    &::after {\n      content: var(--tw-content);\n      opacity: 100%;\n    }\n  }\n  .after\\:shadow-\\[0_1px_0px_0_rgba\\(0\\,0\\,0\\,0\\.05\\)\\] {\n    &::after {\n      content: var(--tw-content);\n      --tw-shadow: 0 1px 0px 0 var(--tw-shadow-color, rgba(0,0,0,0.05));\n      box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\n    }\n  }\n  .after\\:shadow-small {\n    &::after {\n      content: var(--tw-content);\n      --tw-shadow: var(--heroui-box-shadow-small);\n      box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\n    }\n  }\n  .after\\:transition-all {\n    &::after {\n      content: var(--tw-content);\n      transition-property: all;\n      transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));\n      transition-duration: var(--tw-duration, var(--default-transition-duration));\n    }\n  }\n  .after\\:transition-background {\n    &::after {\n      content: var(--tw-content);\n      transition-property: background;\n      transition-timing-function: ease;\n      transition-duration: 250ms;\n    }\n  }\n  .after\\:transition-height {\n    &::after {\n      content: var(--tw-content);\n      transition-property: height;\n      transition-timing-function: ease;\n      transition-duration: 250ms;\n    }\n  }\n  .after\\:transition-transform {\n    &::after {\n      content: var(--tw-content);\n      transition-property: transform, translate, scale, rotate;\n      transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));\n      transition-duration: var(--tw-duration, var(--default-transition-duration));\n    }\n  }\n  .after\\:transition-transform-opacity {\n    &::after {\n      content: var(--tw-content);\n      transition-property: transform, scale, opacity;\n      transition-timing-function: ease;\n      transition-duration: 250ms;\n    }\n  }\n  .after\\:transition-width {\n    &::after {\n      content: var(--tw-content);\n      transition-property: width;\n      transition-timing-function: ease;\n      transition-duration: 250ms;\n    }\n  }\n  .after\\:transition-none {\n    &::after {\n      content: var(--tw-content);\n      transition-property: none;\n    }\n  }\n  .after\\:\\!duration-200 {\n    &::after {\n      content: var(--tw-content);\n      --tw-duration: 200ms !important;\n      transition-duration: 200ms !important;\n    }\n  }\n  .after\\:duration-150 {\n    &::after {\n      content: var(--tw-content);\n      --tw-duration: 150ms;\n      transition-duration: 150ms;\n    }\n  }\n  .after\\:\\!ease-linear {\n    &::after {\n      content: var(--tw-content);\n      --tw-ease: linear !important;\n      transition-timing-function: linear !important;\n    }\n  }\n  .after\\:content-\\[\\'\\'\\] {\n    &::after {\n      content: var(--tw-content);\n      --tw-content: '';\n      content: var(--tw-content);\n    }\n  }\n  .after\\:content-\\[\\'\\*\\'\\] {\n    &::after {\n      content: var(--tw-content);\n      --tw-content: '*';\n      content: var(--tw-content);\n    }\n  }\n  .group-data-\\[collapsible\\=offcanvas\\]\\:after\\:left-full {\n    &:is(:where(.group)[data-collapsible=\"offcanvas\"] *) {\n      &::after {\n        content: var(--tw-content);\n        left: 100%;\n      }\n    }\n  }\n  .group-data-\\[focus\\=true\\]\\:after\\:w-full {\n    &:is(:where(.group)[data-focus=\"true\"] *) {\n      &::after {\n        content: var(--tw-content);\n        width: 100%;\n      }\n    }\n  }\n  .group-data-\\[invalid\\=true\\]\\:after\\:bg-danger {\n    &:is(:where(.group)[data-invalid=\"true\"] *) {\n      &::after {\n        content: var(--tw-content);\n        background-color: hsl(var(--heroui-danger) / 1);\n      }\n    }\n  }\n  .group-data-\\[open\\=true\\]\\:after\\:translate-y-0 {\n    &:is(:where(.group)[data-open=\"true\"] *) {\n      &::after {\n        content: var(--tw-content);\n        --tw-translate-y: calc(var(--spacing) * 0);\n        translate: var(--tw-translate-x) var(--tw-translate-y);\n      }\n    }\n  }\n  .group-data-\\[open\\=true\\]\\:after\\:-rotate-45 {\n    &:is(:where(.group)[data-open=\"true\"] *) {\n      &::after {\n        content: var(--tw-content);\n        rotate: calc(45deg * -1);\n      }\n    }\n  }\n  .group-data-\\[required\\=true\\]\\:after\\:ml-0\\.5 {\n    &:is(:where(.group)[data-required=\"true\"] *) {\n      &::after {\n        content: var(--tw-content);\n        margin-left: calc(var(--spacing) * 0.5);\n      }\n    }\n  }\n  .group-data-\\[required\\=true\\]\\:after\\:text-danger {\n    &:is(:where(.group)[data-required=\"true\"] *) {\n      &::after {\n        content: var(--tw-content);\n        color: hsl(var(--heroui-danger) / 1);\n      }\n    }\n  }\n  .group-data-\\[required\\=true\\]\\:after\\:content-\\[\\'\\*\\'\\] {\n    &:is(:where(.group)[data-required=\"true\"] *) {\n      &::after {\n        content: var(--tw-content);\n        --tw-content: '*';\n        content: var(--tw-content);\n      }\n    }\n  }\n  .group-data-\\[selected\\=true\\]\\:after\\:scale-100 {\n    &:is(:where(.group)[data-selected=\"true\"] *) {\n      &::after {\n        content: var(--tw-content);\n        --tw-scale-x: 100%;\n        --tw-scale-y: 100%;\n        --tw-scale-z: 100%;\n        scale: var(--tw-scale-x) var(--tw-scale-y);\n      }\n    }\n  }\n  .group-data-\\[selected\\=true\\]\\:after\\:opacity-100 {\n    &:is(:where(.group)[data-selected=\"true\"] *) {\n      &::after {\n        content: var(--tw-content);\n        opacity: 100%;\n      }\n    }\n  }\n  .first\\:mt-2 {\n    &:first-child {\n      margin-top: calc(var(--spacing) * 2);\n    }\n  }\n  .first\\:-ml-0\\.5 {\n    &:first-child {\n      margin-left: calc(var(--spacing) * -0.5);\n    }\n  }\n  .first\\:rounded-s-full {\n    &:first-child {\n      border-start-start-radius: calc(infinity * 1px);\n      border-end-start-radius: calc(infinity * 1px);\n    }\n  }\n  .first\\:rounded-s-large {\n    &:first-child {\n      border-start-start-radius: var(--heroui-radius-large);\n      border-end-start-radius: var(--heroui-radius-large);\n    }\n  }\n  .first\\:rounded-s-lg {\n    &:first-child {\n      border-start-start-radius: var(--radius);\n      border-end-start-radius: var(--radius);\n    }\n  }\n  .first\\:rounded-s-medium {\n    &:first-child {\n      border-start-start-radius: var(--heroui-radius-medium);\n      border-end-start-radius: var(--heroui-radius-medium);\n    }\n  }\n  .first\\:rounded-s-none {\n    &:first-child {\n      border-start-start-radius: 0;\n      border-end-start-radius: 0;\n    }\n  }\n  .first\\:rounded-s-small {\n    &:first-child {\n      border-start-start-radius: var(--heroui-radius-small);\n      border-end-start-radius: var(--heroui-radius-small);\n    }\n  }\n  .first\\:rounded-l-md {\n    &:first-child {\n      border-top-left-radius: calc(var(--radius) - 2px);\n      border-bottom-left-radius: calc(var(--radius) - 2px);\n    }\n  }\n  .first\\:border-l {\n    &:first-child {\n      border-left-style: var(--tw-border-style);\n      border-left-width: 1px;\n    }\n  }\n  .first\\:before\\:rounded-s-lg {\n    &:first-child {\n      &::before {\n        content: var(--tw-content);\n        border-start-start-radius: var(--radius);\n        border-end-start-radius: var(--radius);\n      }\n    }\n  }\n  .group-data-\\[first\\=true\\]\\/tr\\:first\\:before\\:rounded-ss-lg {\n    &:is(:where(.group\\/tr)[data-first=\"true\"] *) {\n      &:first-child {\n        &::before {\n          content: var(--tw-content);\n          border-start-start-radius: var(--radius);\n        }\n      }\n    }\n  }\n  .group-data-\\[last\\=true\\]\\/tr\\:first\\:before\\:rounded-es-lg {\n    &:is(:where(.group\\/tr)[data-last=\"true\"] *) {\n      &:first-child {\n        &::before {\n          content: var(--tw-content);\n          border-end-start-radius: var(--radius);\n        }\n      }\n    }\n  }\n  .last\\:rounded-e-full {\n    &:last-child {\n      border-start-end-radius: calc(infinity * 1px);\n      border-end-end-radius: calc(infinity * 1px);\n    }\n  }\n  .last\\:rounded-e-large {\n    &:last-child {\n      border-start-end-radius: var(--heroui-radius-large);\n      border-end-end-radius: var(--heroui-radius-large);\n    }\n  }\n  .last\\:rounded-e-lg {\n    &:last-child {\n      border-start-end-radius: var(--radius);\n      border-end-end-radius: var(--radius);\n    }\n  }\n  .last\\:rounded-e-medium {\n    &:last-child {\n      border-start-end-radius: var(--heroui-radius-medium);\n      border-end-end-radius: var(--heroui-radius-medium);\n    }\n  }\n  .last\\:rounded-e-none {\n    &:last-child {\n      border-start-end-radius: 0;\n      border-end-end-radius: 0;\n    }\n  }\n  .last\\:rounded-e-small {\n    &:last-child {\n      border-start-end-radius: var(--heroui-radius-small);\n      border-end-end-radius: var(--heroui-radius-small);\n    }\n  }\n  .last\\:rounded-r-md {\n    &:last-child {\n      border-top-right-radius: calc(var(--radius) - 2px);\n      border-bottom-right-radius: calc(var(--radius) - 2px);\n    }\n  }\n  .last\\:border-0 {\n    &:last-child {\n      border-style: var(--tw-border-style);\n      border-width: 0px;\n    }\n  }\n  .last\\:border-b-0 {\n    &:last-child {\n      border-bottom-style: var(--tw-border-style);\n      border-bottom-width: 0px;\n    }\n  }\n  .last\\:before\\:rounded-e-lg {\n    &:last-child {\n      &::before {\n        content: var(--tw-content);\n        border-start-end-radius: var(--radius);\n        border-end-end-radius: var(--radius);\n      }\n    }\n  }\n  .group-data-\\[first\\=true\\]\\/tr\\:last\\:before\\:rounded-se-lg {\n    &:is(:where(.group\\/tr)[data-first=\"true\"] *) {\n      &:last-child {\n        &::before {\n          content: var(--tw-content);\n          border-start-end-radius: var(--radius);\n        }\n      }\n    }\n  }\n  .group-data-\\[last\\=true\\]\\/tr\\:last\\:before\\:rounded-ee-lg {\n    &:is(:where(.group\\/tr)[data-last=\"true\"] *) {\n      &:last-child {\n        &::before {\n          content: var(--tw-content);\n          border-end-end-radius: var(--radius);\n        }\n      }\n    }\n  }\n  .first-of-type\\:rounded-e-none {\n    &:first-of-type {\n      border-start-end-radius: 0;\n      border-end-end-radius: 0;\n    }\n  }\n  .last-of-type\\:rounded-s-none {\n    &:last-of-type {\n      border-start-start-radius: 0;\n      border-end-start-radius: 0;\n    }\n  }\n  .autofill\\:bg-transparent {\n    &:autofill {\n      background-color: transparent;\n    }\n  }\n  .focus-within\\:relative {\n    &:focus-within {\n      position: relative;\n    }\n  }\n  .focus-within\\:z-20 {\n    &:focus-within {\n      z-index: 20;\n    }\n  }\n  .focus-within\\:border-danger {\n    &:focus-within {\n      border-color: hsl(var(--heroui-danger) / 1);\n    }\n  }\n  .focus-within\\:border-default-400 {\n    &:focus-within {\n      border-color: hsl(var(--heroui-default-400) / 1);\n    }\n  }\n  .focus-within\\:border-default-foreground {\n    &:focus-within {\n      border-color: hsl(var(--heroui-default-foreground) / 1);\n    }\n  }\n  .focus-within\\:border-primary {\n    &:focus-within {\n      border-color: var(--primary);\n    }\n  }\n  .focus-within\\:border-secondary {\n    &:focus-within {\n      border-color: var(--secondary);\n    }\n  }\n  .focus-within\\:border-success {\n    &:focus-within {\n      border-color: hsl(var(--heroui-success) / 1);\n    }\n  }\n  .focus-within\\:border-warning {\n    &:focus-within {\n      border-color: hsl(var(--heroui-warning) / 1);\n    }\n  }\n  .focus-within\\:bg-danger-50 {\n    &:focus-within {\n      background-color: hsl(var(--heroui-danger-50) / 1);\n    }\n  }\n  .focus-within\\:bg-primary-50 {\n    &:focus-within {\n      background-color: hsl(var(--heroui-primary-50) / 1);\n    }\n  }\n  .focus-within\\:bg-secondary-50 {\n    &:focus-within {\n      background-color: hsl(var(--heroui-secondary-50) / 1);\n    }\n  }\n  .focus-within\\:bg-success-50 {\n    &:focus-within {\n      background-color: hsl(var(--heroui-success-50) / 1);\n    }\n  }\n  .focus-within\\:bg-warning-50 {\n    &:focus-within {\n      background-color: hsl(var(--heroui-warning-50) / 1);\n    }\n  }\n  .focus-within\\:ring-2 {\n    &:focus-within {\n      --tw-ring-shadow: var(--tw-ring-inset,) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);\n      box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\n    }\n  }\n  .focus-within\\:ring-ring {\n    &:focus-within {\n      --tw-ring-color: var(--ring);\n    }\n  }\n  .focus-within\\:ring-offset-2 {\n    &:focus-within {\n      --tw-ring-offset-width: 2px;\n      --tw-ring-offset-shadow: var(--tw-ring-inset,) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);\n    }\n  }\n  .focus-within\\:outline-none {\n    &:focus-within {\n      --tw-outline-style: none;\n      outline-style: none;\n    }\n  }\n  .focus-within\\:after\\:w-full {\n    &:focus-within {\n      &::after {\n        content: var(--tw-content);\n        width: 100%;\n      }\n    }\n  }\n  .hover\\:-translate-x-0 {\n    &:hover {\n      @media (hover: hover) {\n        --tw-translate-x: calc(var(--spacing) * -0);\n        translate: var(--tw-translate-x) var(--tw-translate-y);\n      }\n    }\n  }\n  .hover\\:scale-105 {\n    &:hover {\n      @media (hover: hover) {\n        --tw-scale-x: 105%;\n        --tw-scale-y: 105%;\n        --tw-scale-z: 105%;\n        scale: var(--tw-scale-x) var(--tw-scale-y);\n      }\n    }\n  }\n  .hover\\:scale-110 {\n    &:hover {\n      @media (hover: hover) {\n        --tw-scale-x: 110%;\n        --tw-scale-y: 110%;\n        --tw-scale-z: 110%;\n        scale: var(--tw-scale-x) var(--tw-scale-y);\n      }\n    }\n  }\n  .hover\\:scale-125 {\n    &:hover {\n      @media (hover: hover) {\n        --tw-scale-x: 125%;\n        --tw-scale-y: 125%;\n        --tw-scale-z: 125%;\n        scale: var(--tw-scale-x) var(--tw-scale-y);\n      }\n    }\n  }\n  .hover\\:border-danger {\n    &:hover {\n      @media (hover: hover) {\n        border-color: hsl(var(--heroui-danger) / 1);\n      }\n    }\n  }\n  .hover\\:border-default {\n    &:hover {\n      @media (hover: hover) {\n        border-color: hsl(var(--heroui-default) / 1);\n      }\n    }\n  }\n  .hover\\:border-default-300 {\n    &:hover {\n      @media (hover: hover) {\n        border-color: hsl(var(--heroui-default-300) / 1);\n      }\n    }\n  }\n  .hover\\:border-default-400 {\n    &:hover {\n      @media (hover: hover) {\n        border-color: hsl(var(--heroui-default-400) / 1);\n      }\n    }\n  }\n  .hover\\:border-primary {\n    &:hover {\n      @media (hover: hover) {\n        border-color: var(--primary);\n      }\n    }\n  }\n  .hover\\:border-secondary {\n    &:hover {\n      @media (hover: hover) {\n        border-color: var(--secondary);\n      }\n    }\n  }\n  .hover\\:border-success {\n    &:hover {\n      @media (hover: hover) {\n        border-color: hsl(var(--heroui-success) / 1);\n      }\n    }\n  }\n  .hover\\:border-warning {\n    &:hover {\n      @media (hover: hover) {\n        border-color: hsl(var(--heroui-warning) / 1);\n      }\n    }\n  }\n  .hover\\:\\!bg-foreground {\n    &:hover {\n      @media (hover: hover) {\n        background-color: var(--foreground) !important;\n      }\n    }\n  }\n  .hover\\:bg-accent {\n    &:hover {\n      @media (hover: hover) {\n        background-color: var(--accent);\n      }\n    }\n  }\n  .hover\\:bg-background\\/50 {\n    &:hover {\n      @media (hover: hover) {\n        background-color: var(--background);\n        @supports (color: color-mix(in lab, red, red)) {\n          background-color: color-mix(in oklab, var(--background) 50%, transparent);\n        }\n      }\n    }\n  }\n  .hover\\:bg-background\\/90 {\n    &:hover {\n      @media (hover: hover) {\n        background-color: var(--background);\n        @supports (color: color-mix(in lab, red, red)) {\n          background-color: color-mix(in oklab, var(--background) 90%, transparent);\n        }\n      }\n    }\n  }\n  .hover\\:bg-blue-100 {\n    &:hover {\n      @media (hover: hover) {\n        background-color: var(--color-blue-100);\n      }\n    }\n  }\n  .hover\\:bg-blue-100\\/80 {\n    &:hover {\n      @media (hover: hover) {\n        background-color: color-mix(in srgb, oklch(93.2% 0.032 255.585) 80%, transparent);\n        @supports (color: color-mix(in lab, red, red)) {\n          background-color: color-mix(in oklab, var(--color-blue-100) 80%, transparent);\n        }\n      }\n    }\n  }\n  .hover\\:bg-blue-200 {\n    &:hover {\n      @media (hover: hover) {\n        background-color: var(--color-blue-200);\n      }\n    }\n  }\n  .hover\\:bg-danger {\n    &:hover {\n      @media (hover: hover) {\n        background-color: hsl(var(--heroui-danger) / 1);\n      }\n    }\n  }\n  .hover\\:bg-danger-50 {\n    &:hover {\n      @media (hover: hover) {\n        background-color: hsl(var(--heroui-danger-50) / 1);\n      }\n    }\n  }\n  .hover\\:bg-default-100 {\n    &:hover {\n      @media (hover: hover) {\n        background-color: hsl(var(--heroui-default-100) / 1);\n      }\n    }\n  }\n  .hover\\:bg-default-200 {\n    &:hover {\n      @media (hover: hover) {\n        background-color: hsl(var(--heroui-default-200) / 1);\n      }\n    }\n  }\n  .hover\\:bg-destructive\\/10 {\n    &:hover {\n      @media (hover: hover) {\n        background-color: var(--destructive);\n        @supports (color: color-mix(in lab, red, red)) {\n          background-color: color-mix(in oklab, var(--destructive) 10%, transparent);\n        }\n      }\n    }\n  }\n  .hover\\:bg-destructive\\/90 {\n    &:hover {\n      @media (hover: hover) {\n        background-color: var(--destructive);\n        @supports (color: color-mix(in lab, red, red)) {\n          background-color: color-mix(in oklab, var(--destructive) 90%, transparent);\n        }\n      }\n    }\n  }\n  .hover\\:bg-gray-50 {\n    &:hover {\n      @media (hover: hover) {\n        background-color: var(--color-gray-50);\n      }\n    }\n  }\n  .hover\\:bg-gray-100 {\n    &:hover {\n      @media (hover: hover) {\n        background-color: var(--color-gray-100);\n      }\n    }\n  }\n  .hover\\:bg-gray-100\\/80 {\n    &:hover {\n      @media (hover: hover) {\n        background-color: color-mix(in srgb, oklch(96.7% 0.003 264.542) 80%, transparent);\n        @supports (color: color-mix(in lab, red, red)) {\n          background-color: color-mix(in oklab, var(--color-gray-100) 80%, transparent);\n        }\n      }\n    }\n  }\n  .hover\\:bg-gray-200 {\n    &:hover {\n      @media (hover: hover) {\n        background-color: var(--color-gray-200);\n      }\n    }\n  }\n  .hover\\:bg-green-100 {\n    &:hover {\n      @media (hover: hover) {\n        background-color: var(--color-green-100);\n      }\n    }\n  }\n  .hover\\:bg-green-100\\/80 {\n    &:hover {\n      @media (hover: hover) {\n        background-color: color-mix(in srgb, oklch(96.2% 0.044 156.743) 80%, transparent);\n        @supports (color: color-mix(in lab, red, red)) {\n          background-color: color-mix(in oklab, var(--color-green-100) 80%, transparent);\n        }\n      }\n    }\n  }\n  .hover\\:bg-green-200 {\n    &:hover {\n      @media (hover: hover) {\n        background-color: var(--color-green-200);\n      }\n    }\n  }\n  .hover\\:bg-green-600 {\n    &:hover {\n      @media (hover: hover) {\n        background-color: var(--color-green-600);\n      }\n    }\n  }\n  .hover\\:bg-green-700 {\n    &:hover {\n      @media (hover: hover) {\n        background-color: var(--color-green-700);\n      }\n    }\n  }\n  .hover\\:bg-input\\/30 {\n    &:hover {\n      @media (hover: hover) {\n        background-color: var(--input);\n        @supports (color: color-mix(in lab, red, red)) {\n          background-color: color-mix(in oklab, var(--input) 30%, transparent);\n        }\n      }\n    }\n  }\n  .hover\\:bg-muted {\n    &:hover {\n      @media (hover: hover) {\n        background-color: var(--muted);\n      }\n    }\n  }\n  .hover\\:bg-muted\\/50 {\n    &:hover {\n      @media (hover: hover) {\n        background-color: var(--muted);\n        @supports (color: color-mix(in lab, red, red)) {\n          background-color: color-mix(in oklab, var(--muted) 50%, transparent);\n        }\n      }\n    }\n  }\n  .hover\\:bg-muted\\/80 {\n    &:hover {\n      @media (hover: hover) {\n        background-color: var(--muted);\n        @supports (color: color-mix(in lab, red, red)) {\n          background-color: color-mix(in oklab, var(--muted) 80%, transparent);\n        }\n      }\n    }\n  }\n  .hover\\:bg-primary {\n    &:hover {\n      @media (hover: hover) {\n        background-color: var(--primary);\n      }\n    }\n  }\n  .hover\\:bg-primary-50 {\n    &:hover {\n      @media (hover: hover) {\n        background-color: hsl(var(--heroui-primary-50) / 1);\n      }\n    }\n  }\n  .hover\\:bg-primary\\/90 {\n    &:hover {\n      @media (hover: hover) {\n        background-color: var(--primary);\n        @supports (color: color-mix(in lab, red, red)) {\n          background-color: color-mix(in oklab, var(--primary) 90%, transparent);\n        }\n      }\n    }\n  }\n  .hover\\:bg-purple-200 {\n    &:hover {\n      @media (hover: hover) {\n        background-color: var(--color-purple-200);\n      }\n    }\n  }\n  .hover\\:bg-red-50 {\n    &:hover {\n      @media (hover: hover) {\n        background-color: var(--color-red-50);\n      }\n    }\n  }\n  .hover\\:bg-red-100 {\n    &:hover {\n      @media (hover: hover) {\n        background-color: var(--color-red-100);\n      }\n    }\n  }\n  .hover\\:bg-red-100\\/80 {\n    &:hover {\n      @media (hover: hover) {\n        background-color: color-mix(in srgb, oklch(93.6% 0.032 17.717) 80%, transparent);\n        @supports (color: color-mix(in lab, red, red)) {\n          background-color: color-mix(in oklab, var(--color-red-100) 80%, transparent);\n        }\n      }\n    }\n  }\n  .hover\\:bg-red-200 {\n    &:hover {\n      @media (hover: hover) {\n        background-color: var(--color-red-200);\n      }\n    }\n  }\n  .hover\\:bg-red-600 {\n    &:hover {\n      @media (hover: hover) {\n        background-color: var(--color-red-600);\n      }\n    }\n  }\n  .hover\\:bg-red-700 {\n    &:hover {\n      @media (hover: hover) {\n        background-color: var(--color-red-700);\n      }\n    }\n  }\n  .hover\\:bg-secondary-50 {\n    &:hover {\n      @media (hover: hover) {\n        background-color: hsl(var(--heroui-secondary-50) / 1);\n      }\n    }\n  }\n  .hover\\:bg-secondary\\/80 {\n    &:hover {\n      @media (hover: hover) {\n        background-color: var(--secondary);\n        @supports (color: color-mix(in lab, red, red)) {\n          background-color: color-mix(in oklab, var(--secondary) 80%, transparent);\n        }\n      }\n    }\n  }\n  .hover\\:bg-sidebar-accent {\n    &:hover {\n      @media (hover: hover) {\n        background-color: var(--sidebar-accent);\n      }\n    }\n  }\n  .hover\\:bg-sidebar\\/50 {\n    &:hover {\n      @media (hover: hover) {\n        background-color: var(--sidebar);\n        @supports (color: color-mix(in lab, red, red)) {\n          background-color: color-mix(in oklab, var(--sidebar) 50%, transparent);\n        }\n      }\n    }\n  }\n  .hover\\:bg-slate-50 {\n    &:hover {\n      @media (hover: hover) {\n        background-color: var(--color-slate-50);\n      }\n    }\n  }\n  .hover\\:bg-success-50 {\n    &:hover {\n      @media (hover: hover) {\n        background-color: hsl(var(--heroui-success-50) / 1);\n      }\n    }\n  }\n  .hover\\:bg-transparent {\n    &:hover {\n      @media (hover: hover) {\n        background-color: transparent;\n      }\n    }\n  }\n  .hover\\:bg-warning-50 {\n    &:hover {\n      @media (hover: hover) {\n        background-color: hsl(var(--heroui-warning-50) / 1);\n      }\n    }\n  }\n  .hover\\:bg-yellow-100 {\n    &:hover {\n      @media (hover: hover) {\n        background-color: var(--color-yellow-100);\n      }\n    }\n  }\n  .hover\\:bg-yellow-100\\/80 {\n    &:hover {\n      @media (hover: hover) {\n        background-color: color-mix(in srgb, oklch(97.3% 0.071 103.193) 80%, transparent);\n        @supports (color: color-mix(in lab, red, red)) {\n          background-color: color-mix(in oklab, var(--color-yellow-100) 80%, transparent);\n        }\n      }\n    }\n  }\n  .hover\\:bg-yellow-200 {\n    &:hover {\n      @media (hover: hover) {\n        background-color: var(--color-yellow-200);\n      }\n    }\n  }\n  .hover\\:text-accent-foreground {\n    &:hover {\n      @media (hover: hover) {\n        color: var(--accent-foreground);\n      }\n    }\n  }\n  .hover\\:text-blue-800 {\n    &:hover {\n      @media (hover: hover) {\n        color: var(--color-blue-800);\n      }\n    }\n  }\n  .hover\\:text-danger-600 {\n    &:hover {\n      @media (hover: hover) {\n        color: hsl(var(--heroui-danger-600) / 1);\n      }\n    }\n  }\n  .hover\\:text-default-600 {\n    &:hover {\n      @media (hover: hover) {\n        color: hsl(var(--heroui-default-600) / 1);\n      }\n    }\n  }\n  .hover\\:text-destructive {\n    &:hover {\n      @media (hover: hover) {\n        color: var(--destructive);\n      }\n    }\n  }\n  .hover\\:text-foreground {\n    &:hover {\n      @media (hover: hover) {\n        color: var(--foreground);\n      }\n    }\n  }\n  .hover\\:text-foreground-600 {\n    &:hover {\n      @media (hover: hover) {\n        color: hsl(var(--heroui-foreground-600) / 1);\n      }\n    }\n  }\n  .hover\\:text-gray-700 {\n    &:hover {\n      @media (hover: hover) {\n        color: var(--color-gray-700);\n      }\n    }\n  }\n  .hover\\:text-gray-800 {\n    &:hover {\n      @media (hover: hover) {\n        color: var(--color-gray-800);\n      }\n    }\n  }\n  .hover\\:text-muted-foreground {\n    &:hover {\n      @media (hover: hover) {\n        color: var(--muted-foreground);\n      }\n    }\n  }\n  .hover\\:text-primary-600 {\n    &:hover {\n      @media (hover: hover) {\n        color: hsl(var(--heroui-primary-600) / 1);\n      }\n    }\n  }\n  .hover\\:text-primary-foreground {\n    &:hover {\n      @media (hover: hover) {\n        color: var(--primary-foreground);\n      }\n    }\n  }\n  .hover\\:text-primary\\/90 {\n    &:hover {\n      @media (hover: hover) {\n        color: var(--primary);\n        @supports (color: color-mix(in lab, red, red)) {\n          color: color-mix(in oklab, var(--primary) 90%, transparent);\n        }\n      }\n    }\n  }\n  .hover\\:text-red-700 {\n    &:hover {\n      @media (hover: hover) {\n        color: var(--color-red-700);\n      }\n    }\n  }\n  .hover\\:text-secondary-600 {\n    &:hover {\n      @media (hover: hover) {\n        color: hsl(var(--heroui-secondary-600) / 1);\n      }\n    }\n  }\n  .hover\\:text-sidebar-accent-foreground {\n    &:hover {\n      @media (hover: hover) {\n        color: var(--sidebar-accent-foreground);\n      }\n    }\n  }\n  .hover\\:text-slate-900 {\n    &:hover {\n      @media (hover: hover) {\n        color: var(--color-slate-900);\n      }\n    }\n  }\n  .hover\\:text-success-600 {\n    &:hover {\n      @media (hover: hover) {\n        color: hsl(var(--heroui-success-600) / 1);\n      }\n    }\n  }\n  .hover\\:text-warning-600 {\n    &:hover {\n      @media (hover: hover) {\n        color: hsl(var(--heroui-warning-600) / 1);\n      }\n    }\n  }\n  .hover\\:underline {\n    &:hover {\n      @media (hover: hover) {\n        text-decoration-line: underline;\n      }\n    }\n  }\n  .hover\\:\\!opacity-100 {\n    &:hover {\n      @media (hover: hover) {\n        opacity: 100% !important;\n      }\n    }\n  }\n  .hover\\:opacity-100 {\n    &:hover {\n      @media (hover: hover) {\n        opacity: 100%;\n      }\n    }\n  }\n  .hover\\:opacity-hover {\n    &:hover {\n      @media (hover: hover) {\n        opacity: var(--heroui-hover-opacity);\n      }\n    }\n  }\n  .hover\\:shadow-\\[0_0_0_1px_hsl\\(var\\(--sidebar-accent\\)\\)\\] {\n    &:hover {\n      @media (hover: hover) {\n        --tw-shadow: 0 0 0 1px var(--tw-shadow-color, hsl(var(--sidebar-accent)));\n        box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\n      }\n    }\n  }\n  .hover\\:shadow-lg {\n    &:hover {\n      @media (hover: hover) {\n        --tw-shadow: 0 10px 15px -3px var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 4px 6px -4px var(--tw-shadow-color, rgb(0 0 0 / 0.1));\n        box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\n      }\n    }\n  }\n  .hover\\:shadow-xl {\n    &:hover {\n      @media (hover: hover) {\n        --tw-shadow: 0 20px 25px -5px var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 8px 10px -6px var(--tw-shadow-color, rgb(0 0 0 / 0.1));\n        box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\n      }\n    }\n  }\n  .hover\\:ring-4 {\n    &:hover {\n      @media (hover: hover) {\n        --tw-ring-shadow: var(--tw-ring-inset,) 0 0 0 calc(4px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);\n        box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\n      }\n    }\n  }\n  .hover\\:group-data-\\[collapsible\\=offcanvas\\]\\:bg-sidebar {\n    &:hover {\n      @media (hover: hover) {\n        &:is(:where(.group)[data-collapsible=\"offcanvas\"] *) {\n          background-color: var(--sidebar);\n        }\n      }\n    }\n  }\n  .group-data-\\[invalid\\=true\\]\\:hover\\:border-danger {\n    &:is(:where(.group)[data-invalid=\"true\"] *) {\n      &:hover {\n        @media (hover: hover) {\n          border-color: hsl(var(--heroui-danger) / 1);\n        }\n      }\n    }\n  }\n  .group-data-\\[invalid\\=true\\]\\:hover\\:bg-danger-100 {\n    &:is(:where(.group)[data-invalid=\"true\"] *) {\n      &:hover {\n        @media (hover: hover) {\n          background-color: hsl(var(--heroui-danger-100) / 1);\n        }\n      }\n    }\n  }\n  .hover\\:after\\:bg-danger\\/20 {\n    &:hover {\n      @media (hover: hover) {\n        &::after {\n          content: var(--tw-content);\n          background-color: hsl(var(--heroui-danger) / 1);\n          @supports (color: color-mix(in lab, red, red)) {\n            background-color: color-mix(in oklab, hsl(var(--heroui-danger) / 1) 20%, transparent);\n          }\n        }\n      }\n    }\n  }\n  .hover\\:after\\:bg-foreground\\/10 {\n    &:hover {\n      @media (hover: hover) {\n        &::after {\n          content: var(--tw-content);\n          background-color: var(--foreground);\n          @supports (color: color-mix(in lab, red, red)) {\n            background-color: color-mix(in oklab, var(--foreground) 10%, transparent);\n          }\n        }\n      }\n    }\n  }\n  .hover\\:after\\:bg-primary\\/20 {\n    &:hover {\n      @media (hover: hover) {\n        &::after {\n          content: var(--tw-content);\n          background-color: var(--primary);\n          @supports (color: color-mix(in lab, red, red)) {\n            background-color: color-mix(in oklab, var(--primary) 20%, transparent);\n          }\n        }\n      }\n    }\n  }\n  .hover\\:after\\:bg-secondary\\/20 {\n    &:hover {\n      @media (hover: hover) {\n        &::after {\n          content: var(--tw-content);\n          background-color: var(--secondary);\n          @supports (color: color-mix(in lab, red, red)) {\n            background-color: color-mix(in oklab, var(--secondary) 20%, transparent);\n          }\n        }\n      }\n    }\n  }\n  .hover\\:after\\:bg-sidebar-border {\n    &:hover {\n      @media (hover: hover) {\n        &::after {\n          content: var(--tw-content);\n          background-color: var(--sidebar-border);\n        }\n      }\n    }\n  }\n  .hover\\:after\\:bg-success\\/20 {\n    &:hover {\n      @media (hover: hover) {\n        &::after {\n          content: var(--tw-content);\n          background-color: hsl(var(--heroui-success) / 1);\n          @supports (color: color-mix(in lab, red, red)) {\n            background-color: color-mix(in oklab, hsl(var(--heroui-success) / 1) 20%, transparent);\n          }\n        }\n      }\n    }\n  }\n  .hover\\:after\\:bg-warning\\/20 {\n    &:hover {\n      @media (hover: hover) {\n        &::after {\n          content: var(--tw-content);\n          background-color: hsl(var(--heroui-warning) / 1);\n          @supports (color: color-mix(in lab, red, red)) {\n            background-color: color-mix(in oklab, hsl(var(--heroui-warning) / 1) 20%, transparent);\n          }\n        }\n      }\n    }\n  }\n  .hover\\:after\\:opacity-100 {\n    &:hover {\n      @media (hover: hover) {\n        &::after {\n          content: var(--tw-content);\n          opacity: 100%;\n        }\n      }\n    }\n  }\n  .focus-within\\:hover\\:border-danger {\n    &:focus-within {\n      &:hover {\n        @media (hover: hover) {\n          border-color: hsl(var(--heroui-danger) / 1);\n        }\n      }\n    }\n  }\n  .focus-within\\:hover\\:border-default-foreground {\n    &:focus-within {\n      &:hover {\n        @media (hover: hover) {\n          border-color: hsl(var(--heroui-default-foreground) / 1);\n        }\n      }\n    }\n  }\n  .focus-within\\:hover\\:border-primary {\n    &:focus-within {\n      &:hover {\n        @media (hover: hover) {\n          border-color: var(--primary);\n        }\n      }\n    }\n  }\n  .focus-within\\:hover\\:border-secondary {\n    &:focus-within {\n      &:hover {\n        @media (hover: hover) {\n          border-color: var(--secondary);\n        }\n      }\n    }\n  }\n  .focus-within\\:hover\\:border-success {\n    &:focus-within {\n      &:hover {\n        @media (hover: hover) {\n          border-color: hsl(var(--heroui-success) / 1);\n        }\n      }\n    }\n  }\n  .focus-within\\:hover\\:border-warning {\n    &:focus-within {\n      &:hover {\n        @media (hover: hover) {\n          border-color: hsl(var(--heroui-warning) / 1);\n        }\n      }\n    }\n  }\n  .focus-within\\:hover\\:bg-default-100 {\n    &:focus-within {\n      &:hover {\n        @media (hover: hover) {\n          background-color: hsl(var(--heroui-default-100) / 1);\n        }\n      }\n    }\n  }\n  .group-data-\\[invalid\\=true\\]\\:focus-within\\:hover\\:border-danger {\n    &:is(:where(.group)[data-invalid=\"true\"] *) {\n      &:focus-within {\n        &:hover {\n          @media (hover: hover) {\n            border-color: hsl(var(--heroui-danger) / 1);\n          }\n        }\n      }\n    }\n  }\n  .group-data-\\[invalid\\=true\\]\\:focus-within\\:hover\\:bg-danger-50 {\n    &:is(:where(.group)[data-invalid=\"true\"] *) {\n      &:focus-within {\n        &:hover {\n          @media (hover: hover) {\n            background-color: hsl(var(--heroui-danger-50) / 1);\n          }\n        }\n      }\n    }\n  }\n  .focus\\:z-10 {\n    &:focus {\n      z-index: 10;\n    }\n  }\n  .focus\\:bg-accent {\n    &:focus {\n      background-color: var(--accent);\n    }\n  }\n  .focus\\:bg-danger-400\\/50 {\n    &:focus {\n      background-color: hsl(var(--heroui-danger-400) / 1);\n      @supports (color: color-mix(in lab, red, red)) {\n        background-color: color-mix(in oklab, hsl(var(--heroui-danger-400) / 1) 50%, transparent);\n      }\n    }\n  }\n  .focus\\:bg-default-400\\/50 {\n    &:focus {\n      background-color: hsl(var(--heroui-default-400) / 1);\n      @supports (color: color-mix(in lab, red, red)) {\n        background-color: color-mix(in oklab, hsl(var(--heroui-default-400) / 1) 50%, transparent);\n      }\n    }\n  }\n  .focus\\:bg-primary {\n    &:focus {\n      background-color: var(--primary);\n    }\n  }\n  .focus\\:bg-primary-400\\/50 {\n    &:focus {\n      background-color: hsl(var(--heroui-primary-400) / 1);\n      @supports (color: color-mix(in lab, red, red)) {\n        background-color: color-mix(in oklab, hsl(var(--heroui-primary-400) / 1) 50%, transparent);\n      }\n    }\n  }\n  .focus\\:bg-secondary-400\\/50 {\n    &:focus {\n      background-color: hsl(var(--heroui-secondary-400) / 1);\n      @supports (color: color-mix(in lab, red, red)) {\n        background-color: color-mix(in oklab, hsl(var(--heroui-secondary-400) / 1) 50%, transparent);\n      }\n    }\n  }\n  .focus\\:bg-success-400\\/50 {\n    &:focus {\n      background-color: hsl(var(--heroui-success-400) / 1);\n      @supports (color: color-mix(in lab, red, red)) {\n        background-color: color-mix(in oklab, hsl(var(--heroui-success-400) / 1) 50%, transparent);\n      }\n    }\n  }\n  .focus\\:bg-transparent {\n    &:focus {\n      background-color: transparent;\n    }\n  }\n  .focus\\:bg-warning-400\\/50 {\n    &:focus {\n      background-color: hsl(var(--heroui-warning-400) / 1);\n      @supports (color: color-mix(in lab, red, red)) {\n        background-color: color-mix(in oklab, hsl(var(--heroui-warning-400) / 1) 50%, transparent);\n      }\n    }\n  }\n  .focus\\:text-accent-foreground {\n    &:focus {\n      color: var(--accent-foreground);\n    }\n  }\n  .focus\\:text-blue-600 {\n    &:focus {\n      color: var(--color-blue-600);\n    }\n  }\n  .focus\\:text-destructive {\n    &:focus {\n      color: var(--destructive);\n    }\n  }\n  .focus\\:text-green-600 {\n    &:focus {\n      color: var(--color-green-600);\n    }\n  }\n  .focus\\:text-primary-foreground {\n    &:focus {\n      color: var(--primary-foreground);\n    }\n  }\n  .focus\\:text-red-500 {\n    &:focus {\n      color: var(--color-red-500);\n    }\n  }\n  .focus\\:text-red-600 {\n    &:focus {\n      color: var(--color-red-600);\n    }\n  }\n  .focus\\:underline {\n    &:focus {\n      text-decoration-line: underline;\n    }\n  }\n  .focus\\:shadow-sm {\n    &:focus {\n      --tw-shadow: 0 1px 3px 0 var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 1px 2px -1px var(--tw-shadow-color, rgb(0 0 0 / 0.1));\n      box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\n    }\n  }\n  .focus\\:ring-2 {\n    &:focus {\n      --tw-ring-shadow: var(--tw-ring-inset,) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);\n      box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\n    }\n  }\n  .focus\\:ring-ring {\n    &:focus {\n      --tw-ring-color: var(--ring);\n    }\n  }\n  .focus\\:ring-offset-2 {\n    &:focus {\n      --tw-ring-offset-width: 2px;\n      --tw-ring-offset-shadow: var(--tw-ring-inset,) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);\n    }\n  }\n  .focus\\:outline-hidden {\n    &:focus {\n      --tw-outline-style: none;\n      outline-style: none;\n      @media (forced-colors: active) {\n        outline: 2px solid transparent;\n        outline-offset: 2px;\n      }\n    }\n  }\n  .focus\\:outline-none {\n    &:focus {\n      --tw-outline-style: none;\n      outline-style: none;\n    }\n  }\n  .focus-visible\\:z-10 {\n    &:focus-visible {\n      z-index: 10;\n    }\n  }\n  .focus-visible\\:border {\n    &:focus-visible {\n      border-style: var(--tw-border-style);\n      border-width: 1px;\n    }\n  }\n  .focus-visible\\:border-ring {\n    &:focus-visible {\n      border-color: var(--ring);\n    }\n  }\n  .focus-visible\\:bg-background {\n    &:focus-visible {\n      background-color: var(--background);\n    }\n  }\n  .focus-visible\\:ring-0 {\n    &:focus-visible {\n      --tw-ring-shadow: var(--tw-ring-inset,) 0 0 0 calc(0px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);\n      box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\n    }\n  }\n  .focus-visible\\:ring-1 {\n    &:focus-visible {\n      --tw-ring-shadow: var(--tw-ring-inset,) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);\n      box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\n    }\n  }\n  .focus-visible\\:ring-2 {\n    &:focus-visible {\n      --tw-ring-shadow: var(--tw-ring-inset,) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);\n      box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\n    }\n  }\n  .focus-visible\\:ring-4 {\n    &:focus-visible {\n      --tw-ring-shadow: var(--tw-ring-inset,) 0 0 0 calc(4px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);\n      box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\n    }\n  }\n  .focus-visible\\:ring-\\[3px\\] {\n    &:focus-visible {\n      --tw-ring-shadow: var(--tw-ring-inset,) 0 0 0 calc(3px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);\n      box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\n    }\n  }\n  .focus-visible\\:ring-destructive\\/20 {\n    &:focus-visible {\n      --tw-ring-color: var(--destructive);\n      @supports (color: color-mix(in lab, red, red)) {\n        --tw-ring-color: color-mix(in oklab, var(--destructive) 20%, transparent);\n      }\n    }\n  }\n  .focus-visible\\:ring-red-500 {\n    &:focus-visible {\n      --tw-ring-color: var(--color-red-500);\n    }\n  }\n  .focus-visible\\:ring-ring {\n    &:focus-visible {\n      --tw-ring-color: var(--ring);\n    }\n  }\n  .focus-visible\\:ring-ring\\/50 {\n    &:focus-visible {\n      --tw-ring-color: var(--ring);\n      @supports (color: color-mix(in lab, red, red)) {\n        --tw-ring-color: color-mix(in oklab, var(--ring) 50%, transparent);\n      }\n    }\n  }\n  .focus-visible\\:ring-offset-0 {\n    &:focus-visible {\n      --tw-ring-offset-width: 0px;\n      --tw-ring-offset-shadow: var(--tw-ring-inset,) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);\n    }\n  }\n  .focus-visible\\:ring-offset-1 {\n    &:focus-visible {\n      --tw-ring-offset-width: 1px;\n      --tw-ring-offset-shadow: var(--tw-ring-inset,) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);\n    }\n  }\n  .focus-visible\\:outline-hidden {\n    &:focus-visible {\n      --tw-outline-style: none;\n      outline-style: none;\n      @media (forced-colors: active) {\n        outline: 2px solid transparent;\n        outline-offset: 2px;\n      }\n    }\n  }\n  .focus-visible\\:outline-1 {\n    &:focus-visible {\n      outline-style: var(--tw-outline-style);\n      outline-width: 1px;\n    }\n  }\n  .focus-visible\\:outline-2 {\n    &:focus-visible {\n      outline-style: var(--tw-outline-style);\n      outline-width: 2px;\n    }\n  }\n  .focus-visible\\:outline-offset-2 {\n    &:focus-visible {\n      outline-offset: 2px;\n    }\n  }\n  .focus-visible\\:outline-focus {\n    &:focus-visible {\n      outline-color: hsl(var(--heroui-focus) / 1);\n    }\n  }\n  .focus-visible\\:outline-ring {\n    &:focus-visible {\n      outline-color: var(--ring);\n    }\n  }\n  .focus-visible\\:outline-none {\n    &:focus-visible {\n      --tw-outline-style: none;\n      outline-style: none;\n    }\n  }\n  .active\\:bg-default-200 {\n    &:active {\n      background-color: hsl(var(--heroui-default-200) / 1);\n    }\n  }\n  .active\\:bg-default-300 {\n    &:active {\n      background-color: hsl(var(--heroui-default-300) / 1);\n    }\n  }\n  .active\\:bg-sidebar-accent {\n    &:active {\n      background-color: var(--sidebar-accent);\n    }\n  }\n  .active\\:bg-transparent {\n    &:active {\n      background-color: transparent;\n    }\n  }\n  .active\\:text-sidebar-accent-foreground {\n    &:active {\n      color: var(--sidebar-accent-foreground);\n    }\n  }\n  .active\\:underline {\n    &:active {\n      text-decoration-line: underline;\n    }\n  }\n  .active\\:\\!opacity-70 {\n    &:active {\n      opacity: 70% !important;\n    }\n  }\n  .active\\:opacity-disabled {\n    &:active {\n      opacity: var(--heroui-disabled-opacity);\n    }\n  }\n  .disabled\\:pointer-events-none {\n    &:disabled {\n      pointer-events: none;\n    }\n  }\n  .disabled\\:cursor-default {\n    &:disabled {\n      cursor: default;\n    }\n  }\n  .disabled\\:cursor-not-allowed {\n    &:disabled {\n      cursor: not-allowed;\n    }\n  }\n  .disabled\\:text-gray-400 {\n    &:disabled {\n      color: var(--color-gray-400);\n    }\n  }\n  .disabled\\:no-underline {\n    &:disabled {\n      text-decoration-line: none;\n    }\n  }\n  .disabled\\:opacity-50 {\n    &:disabled {\n      opacity: 50%;\n    }\n  }\n  .disabled\\:hover\\:scale-100 {\n    &:disabled {\n      &:hover {\n        @media (hover: hover) {\n          --tw-scale-x: 100%;\n          --tw-scale-y: 100%;\n          --tw-scale-z: 100%;\n          scale: var(--tw-scale-x) var(--tw-scale-y);\n        }\n      }\n    }\n  }\n  .in-data-\\[side\\=left\\]\\:cursor-w-resize {\n    :where(*[data-side=\"left\"]) & {\n      cursor: w-resize;\n    }\n  }\n  .in-data-\\[side\\=right\\]\\:cursor-e-resize {\n    :where(*[data-side=\"right\"]) & {\n      cursor: e-resize;\n    }\n  }\n  .has-disabled\\:opacity-50 {\n    &:has(*:disabled) {\n      opacity: 50%;\n    }\n  }\n  .has-data-\\[slot\\=card-action\\]\\:grid-cols-\\[1fr_auto\\] {\n    &:has(*[data-slot=\"card-action\"]) {\n      grid-template-columns: 1fr auto;\n    }\n  }\n  .has-data-\\[variant\\=inset\\]\\:bg-sidebar {\n    &:has(*[data-variant=\"inset\"]) {\n      background-color: var(--sidebar);\n    }\n  }\n  .has-\\[\\:disabled\\]\\:opacity-60 {\n    &:has(*:is(:disabled)) {\n      opacity: 60%;\n    }\n  }\n  .has-\\[\\>svg\\]\\:grid-cols-\\[calc\\(var\\(--spacing\\)\\*4\\)_1fr\\] {\n    &:has(>svg) {\n      grid-template-columns: calc(var(--spacing) * 4) 1fr;\n    }\n  }\n  .has-\\[\\>svg\\]\\:gap-x-3 {\n    &:has(>svg) {\n      column-gap: calc(var(--spacing) * 3);\n    }\n  }\n  .has-\\[\\>svg\\]\\:px-2\\.5 {\n    &:has(>svg) {\n      padding-inline: calc(var(--spacing) * 2.5);\n    }\n  }\n  .has-\\[\\>svg\\]\\:px-3 {\n    &:has(>svg) {\n      padding-inline: calc(var(--spacing) * 3);\n    }\n  }\n  .has-\\[\\>svg\\]\\:px-4 {\n    &:has(>svg) {\n      padding-inline: calc(var(--spacing) * 4);\n    }\n  }\n  .aria-disabled\\:pointer-events-none {\n    &[aria-disabled=\"true\"] {\n      pointer-events: none;\n    }\n  }\n  .aria-disabled\\:opacity-50 {\n    &[aria-disabled=\"true\"] {\n      opacity: 50%;\n    }\n  }\n  .aria-expanded\\:scale-\\[0\\.97\\] {\n    &[aria-expanded=\"true\"] {\n      scale: 0.97;\n    }\n  }\n  .aria-expanded\\:opacity-70 {\n    &[aria-expanded=\"true\"] {\n      opacity: 70%;\n    }\n  }\n  .aria-invalid\\:border-destructive {\n    &[aria-invalid=\"true\"] {\n      border-color: var(--destructive);\n    }\n  }\n  .aria-invalid\\:ring-destructive\\/20 {\n    &[aria-invalid=\"true\"] {\n      --tw-ring-color: var(--destructive);\n      @supports (color: color-mix(in lab, red, red)) {\n        --tw-ring-color: color-mix(in oklab, var(--destructive) 20%, transparent);\n      }\n    }\n  }\n  .aria-selected\\:bg-accent {\n    &[aria-selected=\"true\"] {\n      background-color: var(--accent);\n    }\n  }\n  .aria-selected\\:bg-primary {\n    &[aria-selected=\"true\"] {\n      background-color: var(--primary);\n    }\n  }\n  .aria-selected\\:text-accent-foreground {\n    &[aria-selected=\"true\"] {\n      color: var(--accent-foreground);\n    }\n  }\n  .aria-selected\\:text-muted-foreground {\n    &[aria-selected=\"true\"] {\n      color: var(--muted-foreground);\n    }\n  }\n  .aria-selected\\:text-primary-foreground {\n    &[aria-selected=\"true\"] {\n      color: var(--primary-foreground);\n    }\n  }\n  .aria-selected\\:opacity-100 {\n    &[aria-selected=\"true\"] {\n      opacity: 100%;\n    }\n  }\n  .data-\\[active\\=true\\]\\:z-10 {\n    &[data-active=\"true\"] {\n      z-index: 10;\n    }\n  }\n  .data-\\[active\\=true\\]\\:scale-100 {\n    &[data-active=\"true\"] {\n      --tw-scale-x: 100%;\n      --tw-scale-y: 100%;\n      --tw-scale-z: 100%;\n      scale: var(--tw-scale-x) var(--tw-scale-y);\n    }\n  }\n  .data-\\[active\\=true\\]\\:scale-110 {\n    &[data-active=\"true\"] {\n      --tw-scale-x: 110%;\n      --tw-scale-y: 110%;\n      --tw-scale-z: 110%;\n      scale: var(--tw-scale-x) var(--tw-scale-y);\n    }\n  }\n  .data-\\[active\\=true\\]\\:border-danger {\n    &[data-active=\"true\"] {\n      border-color: hsl(var(--heroui-danger) / 1);\n    }\n  }\n  .data-\\[active\\=true\\]\\:border-danger-400 {\n    &[data-active=\"true\"] {\n      border-color: hsl(var(--heroui-danger-400) / 1);\n    }\n  }\n  .data-\\[active\\=true\\]\\:border-default-300 {\n    &[data-active=\"true\"] {\n      border-color: hsl(var(--heroui-default-300) / 1);\n    }\n  }\n  .data-\\[active\\=true\\]\\:border-default-400 {\n    &[data-active=\"true\"] {\n      border-color: hsl(var(--heroui-default-400) / 1);\n    }\n  }\n  .data-\\[active\\=true\\]\\:border-foreground {\n    &[data-active=\"true\"] {\n      border-color: var(--foreground);\n    }\n  }\n  .data-\\[active\\=true\\]\\:border-primary {\n    &[data-active=\"true\"] {\n      border-color: var(--primary);\n    }\n  }\n  .data-\\[active\\=true\\]\\:border-ring {\n    &[data-active=\"true\"] {\n      border-color: var(--ring);\n    }\n  }\n  .data-\\[active\\=true\\]\\:border-secondary {\n    &[data-active=\"true\"] {\n      border-color: var(--secondary);\n    }\n  }\n  .data-\\[active\\=true\\]\\:border-success {\n    &[data-active=\"true\"] {\n      border-color: hsl(var(--heroui-success) / 1);\n    }\n  }\n  .data-\\[active\\=true\\]\\:border-warning {\n    &[data-active=\"true\"] {\n      border-color: hsl(var(--heroui-warning) / 1);\n    }\n  }\n  .data-\\[active\\=true\\]\\:bg-accent\\/50 {\n    &[data-active=\"true\"] {\n      background-color: var(--accent);\n      @supports (color: color-mix(in lab, red, red)) {\n        background-color: color-mix(in oklab, var(--accent) 50%, transparent);\n      }\n    }\n  }\n  .data-\\[active\\=true\\]\\:bg-danger {\n    &[data-active=\"true\"] {\n      background-color: hsl(var(--heroui-danger) / 1);\n    }\n  }\n  .data-\\[active\\=true\\]\\:bg-danger-100 {\n    &[data-active=\"true\"] {\n      background-color: hsl(var(--heroui-danger-100) / 1);\n    }\n  }\n  .data-\\[active\\=true\\]\\:bg-danger-200 {\n    &[data-active=\"true\"] {\n      background-color: hsl(var(--heroui-danger-200) / 1);\n    }\n  }\n  .data-\\[active\\=true\\]\\:bg-default-200 {\n    &[data-active=\"true\"] {\n      background-color: hsl(var(--heroui-default-200) / 1);\n    }\n  }\n  .data-\\[active\\=true\\]\\:bg-default-400 {\n    &[data-active=\"true\"] {\n      background-color: hsl(var(--heroui-default-400) / 1);\n    }\n  }\n  .data-\\[active\\=true\\]\\:bg-primary {\n    &[data-active=\"true\"] {\n      background-color: var(--primary);\n    }\n  }\n  .data-\\[active\\=true\\]\\:bg-primary-200 {\n    &[data-active=\"true\"] {\n      background-color: hsl(var(--heroui-primary-200) / 1);\n    }\n  }\n  .data-\\[active\\=true\\]\\:bg-secondary {\n    &[data-active=\"true\"] {\n      background-color: var(--secondary);\n    }\n  }\n  .data-\\[active\\=true\\]\\:bg-secondary-200 {\n    &[data-active=\"true\"] {\n      background-color: hsl(var(--heroui-secondary-200) / 1);\n    }\n  }\n  .data-\\[active\\=true\\]\\:bg-sidebar-accent {\n    &[data-active=\"true\"] {\n      background-color: var(--sidebar-accent);\n    }\n  }\n  .data-\\[active\\=true\\]\\:bg-success {\n    &[data-active=\"true\"] {\n      background-color: hsl(var(--heroui-success) / 1);\n    }\n  }\n  .data-\\[active\\=true\\]\\:bg-success-200 {\n    &[data-active=\"true\"] {\n      background-color: hsl(var(--heroui-success-200) / 1);\n    }\n  }\n  .data-\\[active\\=true\\]\\:bg-warning {\n    &[data-active=\"true\"] {\n      background-color: hsl(var(--heroui-warning) / 1);\n    }\n  }\n  .data-\\[active\\=true\\]\\:bg-warning-200 {\n    &[data-active=\"true\"] {\n      background-color: hsl(var(--heroui-warning-200) / 1);\n    }\n  }\n  .data-\\[active\\=true\\]\\:font-medium {\n    &[data-active=\"true\"] {\n      --tw-font-weight: var(--font-weight-medium);\n      font-weight: var(--font-weight-medium);\n    }\n  }\n  .data-\\[active\\=true\\]\\:font-semibold {\n    &[data-active=\"true\"] {\n      --tw-font-weight: var(--font-weight-semibold);\n      font-weight: var(--font-weight-semibold);\n    }\n  }\n  .data-\\[active\\=true\\]\\:text-accent-foreground {\n    &[data-active=\"true\"] {\n      color: var(--accent-foreground);\n    }\n  }\n  .data-\\[active\\=true\\]\\:text-danger-foreground {\n    &[data-active=\"true\"] {\n      color: hsl(var(--heroui-danger-foreground) / 1);\n    }\n  }\n  .data-\\[active\\=true\\]\\:text-default-foreground {\n    &[data-active=\"true\"] {\n      color: hsl(var(--heroui-default-foreground) / 1);\n    }\n  }\n  .data-\\[active\\=true\\]\\:text-primary-foreground {\n    &[data-active=\"true\"] {\n      color: var(--primary-foreground);\n    }\n  }\n  .data-\\[active\\=true\\]\\:text-secondary-foreground {\n    &[data-active=\"true\"] {\n      color: var(--secondary-foreground);\n    }\n  }\n  .data-\\[active\\=true\\]\\:text-sidebar-accent-foreground {\n    &[data-active=\"true\"] {\n      color: var(--sidebar-accent-foreground);\n    }\n  }\n  .data-\\[active\\=true\\]\\:text-success-foreground {\n    &[data-active=\"true\"] {\n      color: hsl(var(--heroui-success-foreground) / 1);\n    }\n  }\n  .data-\\[active\\=true\\]\\:text-warning-foreground {\n    &[data-active=\"true\"] {\n      color: hsl(var(--heroui-warning-foreground) / 1);\n    }\n  }\n  .data-\\[active\\=true\\]\\:shadow-md {\n    &[data-active=\"true\"] {\n      --tw-shadow: 0 4px 6px -1px var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 2px 4px -2px var(--tw-shadow-color, rgb(0 0 0 / 0.1));\n      box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\n    }\n  }\n  .data-\\[active\\=true\\]\\:ring-\\[3px\\] {\n    &[data-active=\"true\"] {\n      --tw-ring-shadow: var(--tw-ring-inset,) 0 0 0 calc(3px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);\n      box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\n    }\n  }\n  .data-\\[active\\=true\\]\\:shadow-danger\\/40 {\n    &[data-active=\"true\"] {\n      --tw-shadow-color: hsl(var(--heroui-danger) / 1);\n      @supports (color: color-mix(in lab, red, red)) {\n        --tw-shadow-color: color-mix(in oklab, color-mix(in oklab, hsl(var(--heroui-danger) / 1) 40%, transparent) var(--tw-shadow-alpha), transparent);\n      }\n    }\n  }\n  .data-\\[active\\=true\\]\\:shadow-default\\/50 {\n    &[data-active=\"true\"] {\n      --tw-shadow-color: hsl(var(--heroui-default) / 1);\n      @supports (color: color-mix(in lab, red, red)) {\n        --tw-shadow-color: color-mix(in oklab, color-mix(in oklab, hsl(var(--heroui-default) / 1) 50%, transparent) var(--tw-shadow-alpha), transparent);\n      }\n    }\n  }\n  .data-\\[active\\=true\\]\\:shadow-primary\\/40 {\n    &[data-active=\"true\"] {\n      --tw-shadow-color: var(--primary);\n      @supports (color: color-mix(in lab, red, red)) {\n        --tw-shadow-color: color-mix(in oklab, color-mix(in oklab, var(--primary) 40%, transparent) var(--tw-shadow-alpha), transparent);\n      }\n    }\n  }\n  .data-\\[active\\=true\\]\\:shadow-secondary\\/40 {\n    &[data-active=\"true\"] {\n      --tw-shadow-color: var(--secondary);\n      @supports (color: color-mix(in lab, red, red)) {\n        --tw-shadow-color: color-mix(in oklab, color-mix(in oklab, var(--secondary) 40%, transparent) var(--tw-shadow-alpha), transparent);\n      }\n    }\n  }\n  .data-\\[active\\=true\\]\\:shadow-success\\/40 {\n    &[data-active=\"true\"] {\n      --tw-shadow-color: hsl(var(--heroui-success) / 1);\n      @supports (color: color-mix(in lab, red, red)) {\n        --tw-shadow-color: color-mix(in oklab, color-mix(in oklab, hsl(var(--heroui-success) / 1) 40%, transparent) var(--tw-shadow-alpha), transparent);\n      }\n    }\n  }\n  .data-\\[active\\=true\\]\\:shadow-warning\\/40 {\n    &[data-active=\"true\"] {\n      --tw-shadow-color: hsl(var(--heroui-warning) / 1);\n      @supports (color: color-mix(in lab, red, red)) {\n        --tw-shadow-color: color-mix(in oklab, color-mix(in oklab, hsl(var(--heroui-warning) / 1) 40%, transparent) var(--tw-shadow-alpha), transparent);\n      }\n    }\n  }\n  .data-\\[active\\=true\\]\\:ring-ring\\/50 {\n    &[data-active=\"true\"] {\n      --tw-ring-color: var(--ring);\n      @supports (color: color-mix(in lab, red, red)) {\n        --tw-ring-color: color-mix(in oklab, var(--ring) 50%, transparent);\n      }\n    }\n  }\n  .data-\\[active\\=true\\]\\:after\\:w-full {\n    &[data-active=\"true\"] {\n      &::after {\n        content: var(--tw-content);\n        width: 100%;\n      }\n    }\n  }\n  .data-\\[active\\=true\\]\\:after\\:bg-danger-400 {\n    &[data-active=\"true\"] {\n      &::after {\n        content: var(--tw-content);\n        background-color: hsl(var(--heroui-danger-400) / 1);\n      }\n    }\n  }\n  .data-\\[active\\=true\\]\\:hover\\:bg-accent {\n    &[data-active=\"true\"] {\n      &:hover {\n        @media (hover: hover) {\n          background-color: var(--accent);\n        }\n      }\n    }\n  }\n  .data-\\[active\\=true\\]\\:focus\\:bg-accent {\n    &[data-active=\"true\"] {\n      &:focus {\n        background-color: var(--accent);\n      }\n    }\n  }\n  .data-\\[active\\=true\\]\\:aria-invalid\\:border-destructive {\n    &[data-active=\"true\"] {\n      &[aria-invalid=\"true\"] {\n        border-color: var(--destructive);\n      }\n    }\n  }\n  .data-\\[active\\=true\\]\\:aria-invalid\\:ring-destructive\\/20 {\n    &[data-active=\"true\"] {\n      &[aria-invalid=\"true\"] {\n        --tw-ring-color: var(--destructive);\n        @supports (color: color-mix(in lab, red, red)) {\n          --tw-ring-color: color-mix(in oklab, var(--destructive) 20%, transparent);\n        }\n      }\n    }\n  }\n  .data-\\[animation\\=exiting\\]\\:opacity-0 {\n    &[data-animation=\"exiting\"] {\n      opacity: 0%;\n    }\n  }\n  .data-\\[arrow\\=true\\]\\:before\\:block {\n    &[data-arrow=\"true\"] {\n      &::before {\n        content: var(--tw-content);\n        display: block;\n      }\n    }\n  }\n  .data-\\[before\\=true\\]\\:rotate-180 {\n    &[data-before=\"true\"] {\n      rotate: 180deg;\n    }\n  }\n  .data-\\[bottom-scroll\\=true\\]\\:\\[mask-image\\:linear-gradient\\(180deg\\,\\#000_calc\\(100\\%_-_var\\(--scroll-shadow-size\\)\\)\\,transparent\\)\\] {\n    &[data-bottom-scroll=\"true\"] {\n      mask-image: linear-gradient(180deg,#000 calc(100% - var(--scroll-shadow-size)),transparent);\n    }\n  }\n  .data-\\[direction\\=ascending\\]\\:rotate-180 {\n    &[data-direction=\"ascending\"] {\n      rotate: 180deg;\n    }\n  }\n  .data-\\[disabled\\]\\:pointer-events-none {\n    &[data-disabled] {\n      pointer-events: none;\n    }\n  }\n  .data-\\[disabled\\]\\:opacity-50 {\n    &[data-disabled] {\n      opacity: 50%;\n    }\n  }\n  .data-\\[disabled\\=true\\]\\:pointer-events-none {\n    &[data-disabled=\"true\"] {\n      pointer-events: none;\n    }\n  }\n  .data-\\[disabled\\=true\\]\\:cursor-default {\n    &[data-disabled=\"true\"] {\n      cursor: default;\n    }\n  }\n  .data-\\[disabled\\=true\\]\\:cursor-not-allowed {\n    &[data-disabled=\"true\"] {\n      cursor: not-allowed;\n    }\n  }\n  .data-\\[disabled\\=true\\]\\:text-default-300 {\n    &[data-disabled=\"true\"] {\n      color: hsl(var(--heroui-default-300) / 1);\n    }\n  }\n  .data-\\[disabled\\=true\\]\\:opacity-30 {\n    &[data-disabled=\"true\"] {\n      opacity: 30%;\n    }\n  }\n  .data-\\[disabled\\=true\\]\\:opacity-50 {\n    &[data-disabled=\"true\"] {\n      opacity: 50%;\n    }\n  }\n  .data-\\[disabled\\=true\\]\\:transition-none {\n    &[data-disabled=\"true\"] {\n      transition-property: none;\n    }\n  }\n  .data-\\[dragging\\=true\\]\\:z-10 {\n    &[data-dragging=\"true\"] {\n      z-index: 10;\n    }\n  }\n  .data-\\[dragging\\=true\\]\\:cursor-grabbing {\n    &[data-dragging=\"true\"] {\n      cursor: grabbing;\n    }\n  }\n  .data-\\[dragging\\=true\\]\\:opacity-80 {\n    &[data-dragging=\"true\"] {\n      opacity: 80%;\n    }\n  }\n  .data-\\[dragging\\=true\\]\\:after\\:scale-80 {\n    &[data-dragging=\"true\"] {\n      &::after {\n        content: var(--tw-content);\n        --tw-scale-x: 0.8;\n        --tw-scale-y: 0.8;\n        --tw-scale-z: 0.8;\n        scale: var(--tw-scale-x) var(--tw-scale-y);\n      }\n    }\n  }\n  .data-\\[dragging\\=true\\]\\:after\\:scale-100 {\n    &[data-dragging=\"true\"] {\n      &::after {\n        content: var(--tw-content);\n        --tw-scale-x: 100%;\n        --tw-scale-y: 100%;\n        --tw-scale-z: 100%;\n        scale: var(--tw-scale-x) var(--tw-scale-y);\n      }\n    }\n  }\n  .data-\\[editable\\=true\\]\\:text-danger {\n    &[data-editable=\"true\"] {\n      color: hsl(var(--heroui-danger) / 1);\n    }\n  }\n  .data-\\[editable\\=true\\]\\:text-foreground {\n    &[data-editable=\"true\"] {\n      color: var(--foreground);\n    }\n  }\n  .data-\\[editable\\=true\\]\\:text-primary {\n    &[data-editable=\"true\"] {\n      color: var(--primary);\n    }\n  }\n  .data-\\[editable\\=true\\]\\:text-secondary {\n    &[data-editable=\"true\"] {\n      color: var(--secondary);\n    }\n  }\n  .data-\\[editable\\=true\\]\\:text-success-600 {\n    &[data-editable=\"true\"] {\n      color: hsl(var(--heroui-success-600) / 1);\n    }\n  }\n  .data-\\[editable\\=true\\]\\:text-warning-600 {\n    &[data-editable=\"true\"] {\n      color: hsl(var(--heroui-warning-600) / 1);\n    }\n  }\n  .data-\\[editable\\=true\\]\\:focus\\:text-danger {\n    &[data-editable=\"true\"] {\n      &:focus {\n        color: hsl(var(--heroui-danger) / 1);\n      }\n    }\n  }\n  .data-\\[editable\\=true\\]\\:focus\\:text-default-foreground {\n    &[data-editable=\"true\"] {\n      &:focus {\n        color: hsl(var(--heroui-default-foreground) / 1);\n      }\n    }\n  }\n  .data-\\[editable\\=true\\]\\:focus\\:text-primary {\n    &[data-editable=\"true\"] {\n      &:focus {\n        color: var(--primary);\n      }\n    }\n  }\n  .data-\\[editable\\=true\\]\\:focus\\:text-secondary {\n    &[data-editable=\"true\"] {\n      &:focus {\n        color: var(--secondary);\n      }\n    }\n  }\n  .data-\\[editable\\=true\\]\\:focus\\:text-success {\n    &[data-editable=\"true\"] {\n      &:focus {\n        color: hsl(var(--heroui-success) / 1);\n      }\n    }\n  }\n  .data-\\[editable\\=true\\]\\:focus\\:text-success-600 {\n    &[data-editable=\"true\"] {\n      &:focus {\n        color: hsl(var(--heroui-success-600) / 1);\n      }\n    }\n  }\n  .data-\\[editable\\=true\\]\\:focus\\:text-warning {\n    &[data-editable=\"true\"] {\n      &:focus {\n        color: hsl(var(--heroui-warning) / 1);\n      }\n    }\n  }\n  .data-\\[editable\\=true\\]\\:focus\\:text-warning-600 {\n    &[data-editable=\"true\"] {\n      &:focus {\n        color: hsl(var(--heroui-warning-600) / 1);\n      }\n    }\n  }\n  .data-\\[error\\=true\\]\\:text-destructive {\n    &[data-error=\"true\"] {\n      color: var(--destructive);\n    }\n  }\n  .data-\\[fill-end\\=true\\]\\:border-e-danger {\n    &[data-fill-end=\"true\"] {\n      border-inline-end-color: hsl(var(--heroui-danger) / 1);\n    }\n  }\n  .data-\\[fill-end\\=true\\]\\:border-e-foreground {\n    &[data-fill-end=\"true\"] {\n      border-inline-end-color: var(--foreground);\n    }\n  }\n  .data-\\[fill-end\\=true\\]\\:border-e-primary {\n    &[data-fill-end=\"true\"] {\n      border-inline-end-color: var(--primary);\n    }\n  }\n  .data-\\[fill-end\\=true\\]\\:border-e-secondary {\n    &[data-fill-end=\"true\"] {\n      border-inline-end-color: var(--secondary);\n    }\n  }\n  .data-\\[fill-end\\=true\\]\\:border-e-success {\n    &[data-fill-end=\"true\"] {\n      border-inline-end-color: hsl(var(--heroui-success) / 1);\n    }\n  }\n  .data-\\[fill-end\\=true\\]\\:border-e-warning {\n    &[data-fill-end=\"true\"] {\n      border-inline-end-color: hsl(var(--heroui-warning) / 1);\n    }\n  }\n  .data-\\[fill-end\\=true\\]\\:border-t-danger {\n    &[data-fill-end=\"true\"] {\n      border-top-color: hsl(var(--heroui-danger) / 1);\n    }\n  }\n  .data-\\[fill-end\\=true\\]\\:border-t-foreground {\n    &[data-fill-end=\"true\"] {\n      border-top-color: var(--foreground);\n    }\n  }\n  .data-\\[fill-end\\=true\\]\\:border-t-primary {\n    &[data-fill-end=\"true\"] {\n      border-top-color: var(--primary);\n    }\n  }\n  .data-\\[fill-end\\=true\\]\\:border-t-secondary {\n    &[data-fill-end=\"true\"] {\n      border-top-color: var(--secondary);\n    }\n  }\n  .data-\\[fill-end\\=true\\]\\:border-t-success {\n    &[data-fill-end=\"true\"] {\n      border-top-color: hsl(var(--heroui-success) / 1);\n    }\n  }\n  .data-\\[fill-end\\=true\\]\\:border-t-warning {\n    &[data-fill-end=\"true\"] {\n      border-top-color: hsl(var(--heroui-warning) / 1);\n    }\n  }\n  .data-\\[fill-start\\=true\\]\\:border-s-danger {\n    &[data-fill-start=\"true\"] {\n      border-inline-start-color: hsl(var(--heroui-danger) / 1);\n    }\n  }\n  .data-\\[fill-start\\=true\\]\\:border-s-foreground {\n    &[data-fill-start=\"true\"] {\n      border-inline-start-color: var(--foreground);\n    }\n  }\n  .data-\\[fill-start\\=true\\]\\:border-s-primary {\n    &[data-fill-start=\"true\"] {\n      border-inline-start-color: var(--primary);\n    }\n  }\n  .data-\\[fill-start\\=true\\]\\:border-s-secondary {\n    &[data-fill-start=\"true\"] {\n      border-inline-start-color: var(--secondary);\n    }\n  }\n  .data-\\[fill-start\\=true\\]\\:border-s-success {\n    &[data-fill-start=\"true\"] {\n      border-inline-start-color: hsl(var(--heroui-success) / 1);\n    }\n  }\n  .data-\\[fill-start\\=true\\]\\:border-s-warning {\n    &[data-fill-start=\"true\"] {\n      border-inline-start-color: hsl(var(--heroui-warning) / 1);\n    }\n  }\n  .data-\\[fill-start\\=true\\]\\:border-b-danger {\n    &[data-fill-start=\"true\"] {\n      border-bottom-color: hsl(var(--heroui-danger) / 1);\n    }\n  }\n  .data-\\[fill-start\\=true\\]\\:border-b-foreground {\n    &[data-fill-start=\"true\"] {\n      border-bottom-color: var(--foreground);\n    }\n  }\n  .data-\\[fill-start\\=true\\]\\:border-b-primary {\n    &[data-fill-start=\"true\"] {\n      border-bottom-color: var(--primary);\n    }\n  }\n  .data-\\[fill-start\\=true\\]\\:border-b-secondary {\n    &[data-fill-start=\"true\"] {\n      border-bottom-color: var(--secondary);\n    }\n  }\n  .data-\\[fill-start\\=true\\]\\:border-b-success {\n    &[data-fill-start=\"true\"] {\n      border-bottom-color: hsl(var(--heroui-success) / 1);\n    }\n  }\n  .data-\\[fill-start\\=true\\]\\:border-b-warning {\n    &[data-fill-start=\"true\"] {\n      border-bottom-color: hsl(var(--heroui-warning) / 1);\n    }\n  }\n  .data-\\[focus-visible\\]\\:outline-danger-foreground {\n    &[data-focus-visible] {\n      outline-color: hsl(var(--heroui-danger-foreground) / 1);\n    }\n  }\n  .data-\\[focus-visible\\]\\:outline-default-foreground {\n    &[data-focus-visible] {\n      outline-color: hsl(var(--heroui-default-foreground) / 1);\n    }\n  }\n  .data-\\[focus-visible\\]\\:outline-primary-foreground {\n    &[data-focus-visible] {\n      outline-color: var(--primary-foreground);\n    }\n  }\n  .data-\\[focus-visible\\]\\:outline-secondary-foreground {\n    &[data-focus-visible] {\n      outline-color: var(--secondary-foreground);\n    }\n  }\n  .data-\\[focus-visible\\]\\:outline-success-foreground {\n    &[data-focus-visible] {\n      outline-color: hsl(var(--heroui-success-foreground) / 1);\n    }\n  }\n  .data-\\[focus-visible\\]\\:outline-warning-foreground {\n    &[data-focus-visible] {\n      outline-color: hsl(var(--heroui-warning-foreground) / 1);\n    }\n  }\n  .data-\\[focus-visible\\=true\\]\\:z-10 {\n    &[data-focus-visible=\"true\"] {\n      z-index: 10;\n    }\n  }\n  .data-\\[focus-visible\\=true\\]\\:-translate-x-3 {\n    &[data-focus-visible=\"true\"] {\n      --tw-translate-x: calc(var(--spacing) * -3);\n      translate: var(--tw-translate-x) var(--tw-translate-y);\n    }\n  }\n  .data-\\[focus-visible\\=true\\]\\:outline-2 {\n    &[data-focus-visible=\"true\"] {\n      outline-style: var(--tw-outline-style);\n      outline-width: 2px;\n    }\n  }\n  .data-\\[focus-visible\\=true\\]\\:outline-offset-2 {\n    &[data-focus-visible=\"true\"] {\n      outline-offset: 2px;\n    }\n  }\n  .data-\\[focus-visible\\=true\\]\\:outline-focus {\n    &[data-focus-visible=\"true\"] {\n      outline-color: hsl(var(--heroui-focus) / 1);\n    }\n  }\n  .data-\\[focus\\=true\\]\\:border-danger {\n    &[data-focus=\"true\"] {\n      border-color: hsl(var(--heroui-danger) / 1);\n    }\n  }\n  .data-\\[focus\\=true\\]\\:border-default-400 {\n    &[data-focus=\"true\"] {\n      border-color: hsl(var(--heroui-default-400) / 1);\n    }\n  }\n  .data-\\[focus\\=true\\]\\:border-default-foreground {\n    &[data-focus=\"true\"] {\n      border-color: hsl(var(--heroui-default-foreground) / 1);\n    }\n  }\n  .data-\\[focus\\=true\\]\\:border-primary {\n    &[data-focus=\"true\"] {\n      border-color: var(--primary);\n    }\n  }\n  .data-\\[focus\\=true\\]\\:border-secondary {\n    &[data-focus=\"true\"] {\n      border-color: var(--secondary);\n    }\n  }\n  .data-\\[focus\\=true\\]\\:border-success {\n    &[data-focus=\"true\"] {\n      border-color: hsl(var(--heroui-success) / 1);\n    }\n  }\n  .data-\\[focus\\=true\\]\\:border-warning {\n    &[data-focus=\"true\"] {\n      border-color: hsl(var(--heroui-warning) / 1);\n    }\n  }\n  .data-\\[focus\\=true\\]\\:after\\:w-full {\n    &[data-focus=\"true\"] {\n      &::after {\n        content: var(--tw-content);\n        width: 100%;\n      }\n    }\n  }\n  .data-\\[focused\\=true\\]\\:z-10 {\n    &[data-focused=\"true\"] {\n      z-index: 10;\n    }\n  }\n  .data-\\[has-end-content\\=true\\]\\:pe-1\\.5 {\n    &[data-has-end-content=\"true\"] {\n      padding-inline-end: calc(var(--spacing) * 1.5);\n    }\n  }\n  .data-\\[has-helper\\=true\\]\\:items-start {\n    &[data-has-helper=\"true\"] {\n      align-items: flex-start;\n    }\n  }\n  .data-\\[has-multiple-rows\\=true\\]\\:rounded-large {\n    &[data-has-multiple-rows=\"true\"] {\n      border-radius: var(--heroui-radius-large);\n    }\n  }\n  .data-\\[has-start-content\\=true\\]\\:ps-1\\.5 {\n    &[data-has-start-content=\"true\"] {\n      padding-inline-start: calc(var(--spacing) * 1.5);\n    }\n  }\n  .data-\\[has-title\\=true\\]\\:pt-1 {\n    &[data-has-title=\"true\"] {\n      padding-top: calc(var(--spacing) * 1);\n    }\n  }\n  .data-\\[has-value\\=true\\]\\:text-default-foreground {\n    &[data-has-value=\"true\"] {\n      color: hsl(var(--heroui-default-foreground) / 1);\n    }\n  }\n  .data-\\[hidden\\=true\\]\\:hidden {\n    &[data-hidden=\"true\"] {\n      display: none;\n    }\n  }\n  .data-\\[hide-scroll\\=true\\]\\:scrollbar-hide {\n    &[data-hide-scroll=\"true\"] {\n      -ms-overflow-style: none;\n      scrollbar-width: none;\n      &::-webkit-scrollbar {\n        display: none;\n      }\n    }\n  }\n  .data-\\[hover\\]\\:bg-danger-50 {\n    &[data-hover] {\n      background-color: hsl(var(--heroui-danger-50) / 1);\n    }\n  }\n  .data-\\[hover\\]\\:bg-danger-200 {\n    &[data-hover] {\n      background-color: hsl(var(--heroui-danger-200) / 1);\n    }\n  }\n  .data-\\[hover\\]\\:bg-default-100 {\n    &[data-hover] {\n      background-color: hsl(var(--heroui-default-100) / 1);\n    }\n  }\n  .data-\\[hover\\]\\:bg-primary-50 {\n    &[data-hover] {\n      background-color: hsl(var(--heroui-primary-50) / 1);\n    }\n  }\n  .data-\\[hover\\]\\:bg-primary-200 {\n    &[data-hover] {\n      background-color: hsl(var(--heroui-primary-200) / 1);\n    }\n  }\n  .data-\\[hover\\]\\:bg-secondary-50 {\n    &[data-hover] {\n      background-color: hsl(var(--heroui-secondary-50) / 1);\n    }\n  }\n  .data-\\[hover\\]\\:bg-secondary-200 {\n    &[data-hover] {\n      background-color: hsl(var(--heroui-secondary-200) / 1);\n    }\n  }\n  .data-\\[hover\\]\\:bg-success-50 {\n    &[data-hover] {\n      background-color: hsl(var(--heroui-success-50) / 1);\n    }\n  }\n  .data-\\[hover\\]\\:bg-success-200 {\n    &[data-hover] {\n      background-color: hsl(var(--heroui-success-200) / 1);\n    }\n  }\n  .data-\\[hover\\]\\:bg-warning-100 {\n    &[data-hover] {\n      background-color: hsl(var(--heroui-warning-100) / 1);\n    }\n  }\n  .data-\\[hover\\]\\:bg-warning-200 {\n    &[data-hover] {\n      background-color: hsl(var(--heroui-warning-200) / 1);\n    }\n  }\n  .data-\\[hover-unselected\\=true\\]\\:opacity-disabled {\n    &[data-hover-unselected=\"true\"] {\n      opacity: var(--heroui-disabled-opacity);\n    }\n  }\n  .data-\\[hover\\=true\\]\\:-translate-x-3 {\n    &[data-hover=\"true\"] {\n      --tw-translate-x: calc(var(--spacing) * -3);\n      translate: var(--tw-translate-x) var(--tw-translate-y);\n    }\n  }\n  .data-\\[hover\\=true\\]\\:translate-x-0 {\n    &[data-hover=\"true\"] {\n      --tw-translate-x: calc(var(--spacing) * 0);\n      translate: var(--tw-translate-x) var(--tw-translate-y);\n    }\n  }\n  .data-\\[hover\\=true\\]\\:border-danger {\n    &[data-hover=\"true\"] {\n      border-color: hsl(var(--heroui-danger) / 1);\n    }\n  }\n  .data-\\[hover\\=true\\]\\:border-default {\n    &[data-hover=\"true\"] {\n      border-color: hsl(var(--heroui-default) / 1);\n    }\n  }\n  .data-\\[hover\\=true\\]\\:border-default-400 {\n    &[data-hover=\"true\"] {\n      border-color: hsl(var(--heroui-default-400) / 1);\n    }\n  }\n  .data-\\[hover\\=true\\]\\:border-primary {\n    &[data-hover=\"true\"] {\n      border-color: var(--primary);\n    }\n  }\n  .data-\\[hover\\=true\\]\\:border-secondary {\n    &[data-hover=\"true\"] {\n      border-color: var(--secondary);\n    }\n  }\n  .data-\\[hover\\=true\\]\\:border-success {\n    &[data-hover=\"true\"] {\n      border-color: hsl(var(--heroui-success) / 1);\n    }\n  }\n  .data-\\[hover\\=true\\]\\:border-warning {\n    &[data-hover=\"true\"] {\n      border-color: hsl(var(--heroui-warning) / 1);\n    }\n  }\n  .data-\\[hover\\=true\\]\\:\\!bg-danger {\n    &[data-hover=\"true\"] {\n      background-color: hsl(var(--heroui-danger) / 1) !important;\n    }\n  }\n  .data-\\[hover\\=true\\]\\:\\!bg-danger-100 {\n    &[data-hover=\"true\"] {\n      background-color: hsl(var(--heroui-danger-100) / 1) !important;\n    }\n  }\n  .data-\\[hover\\=true\\]\\:\\!bg-default {\n    &[data-hover=\"true\"] {\n      background-color: hsl(var(--heroui-default) / 1) !important;\n    }\n  }\n  .data-\\[hover\\=true\\]\\:\\!bg-primary {\n    &[data-hover=\"true\"] {\n      background-color: var(--primary) !important;\n    }\n  }\n  .data-\\[hover\\=true\\]\\:\\!bg-secondary {\n    &[data-hover=\"true\"] {\n      background-color: var(--secondary) !important;\n    }\n  }\n  .data-\\[hover\\=true\\]\\:\\!bg-success {\n    &[data-hover=\"true\"] {\n      background-color: hsl(var(--heroui-success) / 1) !important;\n    }\n  }\n  .data-\\[hover\\=true\\]\\:\\!bg-warning {\n    &[data-hover=\"true\"] {\n      background-color: hsl(var(--heroui-warning) / 1) !important;\n    }\n  }\n  .data-\\[hover\\=true\\]\\:bg-content2 {\n    &[data-hover=\"true\"] {\n      background-color: hsl(var(--heroui-content2) / 1);\n    }\n  }\n  .data-\\[hover\\=true\\]\\:bg-danger {\n    &[data-hover=\"true\"] {\n      background-color: hsl(var(--heroui-danger) / 1);\n    }\n  }\n  .data-\\[hover\\=true\\]\\:bg-danger-50 {\n    &[data-hover=\"true\"] {\n      background-color: hsl(var(--heroui-danger-50) / 1);\n    }\n  }\n  .data-\\[hover\\=true\\]\\:bg-danger-100 {\n    &[data-hover=\"true\"] {\n      background-color: hsl(var(--heroui-danger-100) / 1);\n    }\n  }\n  .data-\\[hover\\=true\\]\\:bg-danger\\/20 {\n    &[data-hover=\"true\"] {\n      background-color: hsl(var(--heroui-danger) / 1);\n      @supports (color: color-mix(in lab, red, red)) {\n        background-color: color-mix(in oklab, hsl(var(--heroui-danger) / 1) 20%, transparent);\n      }\n    }\n  }\n  .data-\\[hover\\=true\\]\\:bg-default {\n    &[data-hover=\"true\"] {\n      background-color: hsl(var(--heroui-default) / 1);\n    }\n  }\n  .data-\\[hover\\=true\\]\\:bg-default-100 {\n    &[data-hover=\"true\"] {\n      background-color: hsl(var(--heroui-default-100) / 1);\n    }\n  }\n  .data-\\[hover\\=true\\]\\:bg-default-200 {\n    &[data-hover=\"true\"] {\n      background-color: hsl(var(--heroui-default-200) / 1);\n    }\n  }\n  .data-\\[hover\\=true\\]\\:bg-default\\/40 {\n    &[data-hover=\"true\"] {\n      background-color: hsl(var(--heroui-default) / 1);\n      @supports (color: color-mix(in lab, red, red)) {\n        background-color: color-mix(in oklab, hsl(var(--heroui-default) / 1) 40%, transparent);\n      }\n    }\n  }\n  .data-\\[hover\\=true\\]\\:bg-foreground-200 {\n    &[data-hover=\"true\"] {\n      background-color: hsl(var(--heroui-foreground-200) / 1);\n    }\n  }\n  .data-\\[hover\\=true\\]\\:bg-primary {\n    &[data-hover=\"true\"] {\n      background-color: var(--primary);\n    }\n  }\n  .data-\\[hover\\=true\\]\\:bg-primary-50 {\n    &[data-hover=\"true\"] {\n      background-color: hsl(var(--heroui-primary-50) / 1);\n    }\n  }\n  .data-\\[hover\\=true\\]\\:bg-primary\\/20 {\n    &[data-hover=\"true\"] {\n      background-color: var(--primary);\n      @supports (color: color-mix(in lab, red, red)) {\n        background-color: color-mix(in oklab, var(--primary) 20%, transparent);\n      }\n    }\n  }\n  .data-\\[hover\\=true\\]\\:bg-secondary {\n    &[data-hover=\"true\"] {\n      background-color: var(--secondary);\n    }\n  }\n  .data-\\[hover\\=true\\]\\:bg-secondary-50 {\n    &[data-hover=\"true\"] {\n      background-color: hsl(var(--heroui-secondary-50) / 1);\n    }\n  }\n  .data-\\[hover\\=true\\]\\:bg-secondary\\/20 {\n    &[data-hover=\"true\"] {\n      background-color: var(--secondary);\n      @supports (color: color-mix(in lab, red, red)) {\n        background-color: color-mix(in oklab, var(--secondary) 20%, transparent);\n      }\n    }\n  }\n  .data-\\[hover\\=true\\]\\:bg-success {\n    &[data-hover=\"true\"] {\n      background-color: hsl(var(--heroui-success) / 1);\n    }\n  }\n  .data-\\[hover\\=true\\]\\:bg-success-50 {\n    &[data-hover=\"true\"] {\n      background-color: hsl(var(--heroui-success-50) / 1);\n    }\n  }\n  .data-\\[hover\\=true\\]\\:bg-success-100 {\n    &[data-hover=\"true\"] {\n      background-color: hsl(var(--heroui-success-100) / 1);\n    }\n  }\n  .data-\\[hover\\=true\\]\\:bg-success\\/20 {\n    &[data-hover=\"true\"] {\n      background-color: hsl(var(--heroui-success) / 1);\n      @supports (color: color-mix(in lab, red, red)) {\n        background-color: color-mix(in oklab, hsl(var(--heroui-success) / 1) 20%, transparent);\n      }\n    }\n  }\n  .data-\\[hover\\=true\\]\\:bg-transparent {\n    &[data-hover=\"true\"] {\n      background-color: transparent;\n    }\n  }\n  .data-\\[hover\\=true\\]\\:bg-warning {\n    &[data-hover=\"true\"] {\n      background-color: hsl(var(--heroui-warning) / 1);\n    }\n  }\n  .data-\\[hover\\=true\\]\\:bg-warning-50 {\n    &[data-hover=\"true\"] {\n      background-color: hsl(var(--heroui-warning-50) / 1);\n    }\n  }\n  .data-\\[hover\\=true\\]\\:bg-warning-100 {\n    &[data-hover=\"true\"] {\n      background-color: hsl(var(--heroui-warning-100) / 1);\n    }\n  }\n  .data-\\[hover\\=true\\]\\:bg-warning\\/20 {\n    &[data-hover=\"true\"] {\n      background-color: hsl(var(--heroui-warning) / 1);\n      @supports (color: color-mix(in lab, red, red)) {\n        background-color: color-mix(in oklab, hsl(var(--heroui-warning) / 1) 20%, transparent);\n      }\n    }\n  }\n  .data-\\[hover\\=true\\]\\:\\!text-danger-foreground {\n    &[data-hover=\"true\"] {\n      color: hsl(var(--heroui-danger-foreground) / 1) !important;\n    }\n  }\n  .data-\\[hover\\=true\\]\\:\\!text-primary-foreground {\n    &[data-hover=\"true\"] {\n      color: var(--primary-foreground) !important;\n    }\n  }\n  .data-\\[hover\\=true\\]\\:\\!text-secondary-foreground {\n    &[data-hover=\"true\"] {\n      color: var(--secondary-foreground) !important;\n    }\n  }\n  .data-\\[hover\\=true\\]\\:\\!text-success-foreground {\n    &[data-hover=\"true\"] {\n      color: hsl(var(--heroui-success-foreground) / 1) !important;\n    }\n  }\n  .data-\\[hover\\=true\\]\\:\\!text-warning-foreground {\n    &[data-hover=\"true\"] {\n      color: hsl(var(--heroui-warning-foreground) / 1) !important;\n    }\n  }\n  .data-\\[hover\\=true\\]\\:text-danger {\n    &[data-hover=\"true\"] {\n      color: hsl(var(--heroui-danger) / 1);\n    }\n  }\n  .data-\\[hover\\=true\\]\\:text-danger-500 {\n    &[data-hover=\"true\"] {\n      color: hsl(var(--heroui-danger-500) / 1);\n    }\n  }\n  .data-\\[hover\\=true\\]\\:text-danger-foreground {\n    &[data-hover=\"true\"] {\n      color: hsl(var(--heroui-danger-foreground) / 1);\n    }\n  }\n  .data-\\[hover\\=true\\]\\:text-default-500 {\n    &[data-hover=\"true\"] {\n      color: hsl(var(--heroui-default-500) / 1);\n    }\n  }\n  .data-\\[hover\\=true\\]\\:text-default-foreground {\n    &[data-hover=\"true\"] {\n      color: hsl(var(--heroui-default-foreground) / 1);\n    }\n  }\n  .data-\\[hover\\=true\\]\\:text-foreground-400 {\n    &[data-hover=\"true\"] {\n      color: hsl(var(--heroui-foreground-400) / 1);\n    }\n  }\n  .data-\\[hover\\=true\\]\\:text-foreground-600 {\n    &[data-hover=\"true\"] {\n      color: hsl(var(--heroui-foreground-600) / 1);\n    }\n  }\n  .data-\\[hover\\=true\\]\\:text-primary {\n    &[data-hover=\"true\"] {\n      color: var(--primary);\n    }\n  }\n  .data-\\[hover\\=true\\]\\:text-primary-400 {\n    &[data-hover=\"true\"] {\n      color: hsl(var(--heroui-primary-400) / 1);\n    }\n  }\n  .data-\\[hover\\=true\\]\\:text-primary-foreground {\n    &[data-hover=\"true\"] {\n      color: var(--primary-foreground);\n    }\n  }\n  .data-\\[hover\\=true\\]\\:text-secondary {\n    &[data-hover=\"true\"] {\n      color: var(--secondary);\n    }\n  }\n  .data-\\[hover\\=true\\]\\:text-secondary-400 {\n    &[data-hover=\"true\"] {\n      color: hsl(var(--heroui-secondary-400) / 1);\n    }\n  }\n  .data-\\[hover\\=true\\]\\:text-secondary-foreground {\n    &[data-hover=\"true\"] {\n      color: var(--secondary-foreground);\n    }\n  }\n  .data-\\[hover\\=true\\]\\:text-success {\n    &[data-hover=\"true\"] {\n      color: hsl(var(--heroui-success) / 1);\n    }\n  }\n  .data-\\[hover\\=true\\]\\:text-success-600 {\n    &[data-hover=\"true\"] {\n      color: hsl(var(--heroui-success-600) / 1);\n    }\n  }\n  .data-\\[hover\\=true\\]\\:text-success-foreground {\n    &[data-hover=\"true\"] {\n      color: hsl(var(--heroui-success-foreground) / 1);\n    }\n  }\n  .data-\\[hover\\=true\\]\\:text-warning {\n    &[data-hover=\"true\"] {\n      color: hsl(var(--heroui-warning) / 1);\n    }\n  }\n  .data-\\[hover\\=true\\]\\:text-warning-600 {\n    &[data-hover=\"true\"] {\n      color: hsl(var(--heroui-warning-600) / 1);\n    }\n  }\n  .data-\\[hover\\=true\\]\\:text-warning-foreground {\n    &[data-hover=\"true\"] {\n      color: hsl(var(--heroui-warning-foreground) / 1);\n    }\n  }\n  .data-\\[hover\\=true\\]\\:opacity-70 {\n    &[data-hover=\"true\"] {\n      opacity: 70%;\n    }\n  }\n  .data-\\[hover\\=true\\]\\:opacity-hover {\n    &[data-hover=\"true\"] {\n      opacity: var(--heroui-hover-opacity);\n    }\n  }\n  .data-\\[hover\\=true\\]\\:shadow-lg {\n    &[data-hover=\"true\"] {\n      --tw-shadow: 0 10px 15px -3px var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 4px 6px -4px var(--tw-shadow-color, rgb(0 0 0 / 0.1));\n      box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\n    }\n  }\n  .data-\\[hover\\=true\\]\\:shadow-danger\\/30 {\n    &[data-hover=\"true\"] {\n      --tw-shadow-color: hsl(var(--heroui-danger) / 1);\n      @supports (color: color-mix(in lab, red, red)) {\n        --tw-shadow-color: color-mix(in oklab, color-mix(in oklab, hsl(var(--heroui-danger) / 1) 30%, transparent) var(--tw-shadow-alpha), transparent);\n      }\n    }\n  }\n  .data-\\[hover\\=true\\]\\:shadow-default\\/50 {\n    &[data-hover=\"true\"] {\n      --tw-shadow-color: hsl(var(--heroui-default) / 1);\n      @supports (color: color-mix(in lab, red, red)) {\n        --tw-shadow-color: color-mix(in oklab, color-mix(in oklab, hsl(var(--heroui-default) / 1) 50%, transparent) var(--tw-shadow-alpha), transparent);\n      }\n    }\n  }\n  .data-\\[hover\\=true\\]\\:shadow-primary\\/30 {\n    &[data-hover=\"true\"] {\n      --tw-shadow-color: var(--primary);\n      @supports (color: color-mix(in lab, red, red)) {\n        --tw-shadow-color: color-mix(in oklab, color-mix(in oklab, var(--primary) 30%, transparent) var(--tw-shadow-alpha), transparent);\n      }\n    }\n  }\n  .data-\\[hover\\=true\\]\\:shadow-secondary\\/30 {\n    &[data-hover=\"true\"] {\n      --tw-shadow-color: var(--secondary);\n      @supports (color: color-mix(in lab, red, red)) {\n        --tw-shadow-color: color-mix(in oklab, color-mix(in oklab, var(--secondary) 30%, transparent) var(--tw-shadow-alpha), transparent);\n      }\n    }\n  }\n  .data-\\[hover\\=true\\]\\:shadow-success\\/30 {\n    &[data-hover=\"true\"] {\n      --tw-shadow-color: hsl(var(--heroui-success) / 1);\n      @supports (color: color-mix(in lab, red, red)) {\n        --tw-shadow-color: color-mix(in oklab, color-mix(in oklab, hsl(var(--heroui-success) / 1) 30%, transparent) var(--tw-shadow-alpha), transparent);\n      }\n    }\n  }\n  .data-\\[hover\\=true\\]\\:shadow-warning\\/30 {\n    &[data-hover=\"true\"] {\n      --tw-shadow-color: hsl(var(--heroui-warning) / 1);\n      @supports (color: color-mix(in lab, red, red)) {\n        --tw-shadow-color: color-mix(in oklab, color-mix(in oklab, hsl(var(--heroui-warning) / 1) 30%, transparent) var(--tw-shadow-alpha), transparent);\n      }\n    }\n  }\n  .data-\\[hover\\=true\\]\\:transition-colors {\n    &[data-hover=\"true\"] {\n      transition-property: color, background-color, border-color, outline-color, text-decoration-color, fill, stroke, --tw-gradient-from, --tw-gradient-via, --tw-gradient-to;\n      transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));\n      transition-duration: var(--tw-duration, var(--default-transition-duration));\n    }\n  }\n  .data-\\[in-range\\=false\\]\\:bg-default-200 {\n    &[data-in-range=\"false\"] {\n      background-color: hsl(var(--heroui-default-200) / 1);\n    }\n  }\n  .data-\\[in-range\\=true\\]\\:bg-background\\/50 {\n    &[data-in-range=\"true\"] {\n      background-color: var(--background);\n      @supports (color: color-mix(in lab, red, red)) {\n        background-color: color-mix(in oklab, var(--background) 50%, transparent);\n      }\n    }\n  }\n  .data-\\[in-range\\=true\\]\\:bg-danger {\n    &[data-in-range=\"true\"] {\n      background-color: hsl(var(--heroui-danger) / 1);\n    }\n  }\n  .data-\\[in-range\\=true\\]\\:bg-foreground {\n    &[data-in-range=\"true\"] {\n      background-color: var(--foreground);\n    }\n  }\n  .data-\\[in-range\\=true\\]\\:bg-primary {\n    &[data-in-range=\"true\"] {\n      background-color: var(--primary);\n    }\n  }\n  .data-\\[in-range\\=true\\]\\:bg-secondary {\n    &[data-in-range=\"true\"] {\n      background-color: var(--secondary);\n    }\n  }\n  .data-\\[in-range\\=true\\]\\:bg-success {\n    &[data-in-range=\"true\"] {\n      background-color: hsl(var(--heroui-success) / 1);\n    }\n  }\n  .data-\\[in-range\\=true\\]\\:bg-warning {\n    &[data-in-range=\"true\"] {\n      background-color: hsl(var(--heroui-warning) / 1);\n    }\n  }\n  .data-\\[in-range\\=true\\]\\:opacity-100 {\n    &[data-in-range=\"true\"] {\n      opacity: 100%;\n    }\n  }\n  .data-\\[inert\\=true\\]\\:hidden {\n    &[data-inert=\"true\"] {\n      display: none;\n    }\n  }\n  .data-\\[inset\\]\\:pl-8 {\n    &[data-inset] {\n      padding-left: calc(var(--spacing) * 8);\n    }\n  }\n  .data-\\[invalid\\=true\\]\\:text-danger-300 {\n    &[data-invalid=\"true\"] {\n      color: hsl(var(--heroui-danger-300) / 1);\n    }\n  }\n  .data-\\[invalid\\=true\\]\\:focus\\:bg-danger-400\\/50 {\n    &[data-invalid=\"true\"] {\n      &:focus {\n        background-color: hsl(var(--heroui-danger-400) / 1);\n        @supports (color: color-mix(in lab, red, red)) {\n          background-color: color-mix(in oklab, hsl(var(--heroui-danger-400) / 1) 50%, transparent);\n        }\n      }\n    }\n  }\n  .data-\\[invalid\\=true\\]\\:data-\\[editable\\=true\\]\\:text-danger {\n    &[data-invalid=\"true\"] {\n      &[data-editable=\"true\"] {\n        color: hsl(var(--heroui-danger) / 1);\n      }\n    }\n  }\n  .data-\\[invalid\\=true\\]\\:data-\\[editable\\=true\\]\\:focus\\:text-danger {\n    &[data-invalid=\"true\"] {\n      &[data-editable=\"true\"] {\n        &:focus {\n          color: hsl(var(--heroui-danger) / 1);\n        }\n      }\n    }\n  }\n  .data-\\[invisible\\=true\\]\\:scale-0 {\n    &[data-invisible=\"true\"] {\n      --tw-scale-x: 0%;\n      --tw-scale-y: 0%;\n      --tw-scale-z: 0%;\n      scale: var(--tw-scale-x) var(--tw-scale-y);\n    }\n  }\n  .data-\\[invisible\\=true\\]\\:opacity-0 {\n    &[data-invisible=\"true\"] {\n      opacity: 0%;\n    }\n  }\n  .data-\\[justify\\=center\\]\\:justify-center {\n    &[data-justify=\"center\"] {\n      justify-content: center;\n    }\n  }\n  .data-\\[justify\\=end\\]\\:flex-grow {\n    &[data-justify=\"end\"] {\n      flex-grow: 1;\n    }\n  }\n  .data-\\[justify\\=end\\]\\:basis-0 {\n    &[data-justify=\"end\"] {\n      flex-basis: calc(var(--spacing) * 0);\n    }\n  }\n  .data-\\[justify\\=end\\]\\:justify-end {\n    &[data-justify=\"end\"] {\n      justify-content: flex-end;\n    }\n  }\n  .data-\\[justify\\=start\\]\\:flex-grow {\n    &[data-justify=\"start\"] {\n      flex-grow: 1;\n    }\n  }\n  .data-\\[justify\\=start\\]\\:basis-0 {\n    &[data-justify=\"start\"] {\n      flex-basis: calc(var(--spacing) * 0);\n    }\n  }\n  .data-\\[justify\\=start\\]\\:justify-start {\n    &[data-justify=\"start\"] {\n      justify-content: flex-start;\n    }\n  }\n  .data-\\[label-orientation\\=vertical\\]\\:w-full {\n    &[data-label-orientation=\"vertical\"] {\n      width: 100%;\n    }\n  }\n  .data-\\[label-orientation\\=vertical\\]\\:flex-col {\n    &[data-label-orientation=\"vertical\"] {\n      flex-direction: column;\n    }\n  }\n  .data-\\[label-orientation\\=vertical\\]\\:justify-center {\n    &[data-label-orientation=\"vertical\"] {\n      justify-content: center;\n    }\n  }\n  .data-\\[left-right-scroll\\=true\\]\\:\\[mask-image\\:linear-gradient\\(to_right\\,\\#000\\,\\#000\\,transparent_0\\,\\#000_var\\(--scroll-shadow-size\\)\\,\\#000_calc\\(100\\%_-_var\\(--scroll-shadow-size\\)\\)\\,transparent\\)\\] {\n    &[data-left-right-scroll=\"true\"] {\n      mask-image: linear-gradient(to right,#000,#000,transparent 0,#000 var(--scroll-shadow-size),#000 calc(100% - var(--scroll-shadow-size)),transparent);\n    }\n  }\n  .data-\\[left-scroll\\=true\\]\\:\\[mask-image\\:linear-gradient\\(270deg\\,\\#000_calc\\(100\\%_-_var\\(--scroll-shadow-size\\)\\)\\,transparent\\)\\] {\n    &[data-left-scroll=\"true\"] {\n      mask-image: linear-gradient(270deg,#000 calc(100% - var(--scroll-shadow-size)),transparent);\n    }\n  }\n  .data-\\[loaded\\=true\\]\\:pointer-events-auto {\n    &[data-loaded=\"true\"] {\n      pointer-events: auto;\n    }\n  }\n  .data-\\[loaded\\=true\\]\\:overflow-visible {\n    &[data-loaded=\"true\"] {\n      overflow: visible;\n    }\n  }\n  .data-\\[loaded\\=true\\]\\:\\!bg-transparent {\n    &[data-loaded=\"true\"] {\n      background-color: transparent !important;\n    }\n  }\n  .data-\\[loaded\\=true\\]\\:opacity-100 {\n    &[data-loaded=\"true\"] {\n      opacity: 100%;\n    }\n  }\n  .data-\\[loaded\\=true\\]\\:before\\:-z-10 {\n    &[data-loaded=\"true\"] {\n      &::before {\n        content: var(--tw-content);\n        z-index: calc(10 * -1);\n      }\n    }\n  }\n  .data-\\[loaded\\=true\\]\\:before\\:animate-none {\n    &[data-loaded=\"true\"] {\n      &::before {\n        content: var(--tw-content);\n        animation: none;\n      }\n    }\n  }\n  .data-\\[loaded\\=true\\]\\:before\\:opacity-0 {\n    &[data-loaded=\"true\"] {\n      &::before {\n        content: var(--tw-content);\n        opacity: 0%;\n      }\n    }\n  }\n  .data-\\[loaded\\=true\\]\\:after\\:opacity-0 {\n    &[data-loaded=\"true\"] {\n      &::after {\n        content: var(--tw-content);\n        opacity: 0%;\n      }\n    }\n  }\n  .data-\\[menu-open\\=true\\]\\:border-none {\n    &[data-menu-open=\"true\"] {\n      --tw-border-style: none;\n      border-style: none;\n    }\n  }\n  .data-\\[menu-open\\=true\\]\\:backdrop-blur-xl {\n    &[data-menu-open=\"true\"] {\n      --tw-backdrop-blur: blur(var(--blur-xl));\n      -webkit-backdrop-filter: var(--tw-backdrop-blur,) var(--tw-backdrop-brightness,) var(--tw-backdrop-contrast,) var(--tw-backdrop-grayscale,) var(--tw-backdrop-hue-rotate,) var(--tw-backdrop-invert,) var(--tw-backdrop-opacity,) var(--tw-backdrop-saturate,) var(--tw-backdrop-sepia,);\n      backdrop-filter: var(--tw-backdrop-blur,) var(--tw-backdrop-brightness,) var(--tw-backdrop-contrast,) var(--tw-backdrop-grayscale,) var(--tw-backdrop-hue-rotate,) var(--tw-backdrop-invert,) var(--tw-backdrop-opacity,) var(--tw-backdrop-saturate,) var(--tw-backdrop-sepia,);\n    }\n  }\n  .data-\\[motion\\=from-end\\]\\:slide-in-from-right-52 {\n    &[data-motion=\"from-end\"] {\n      --tw-enter-translate-x: calc(52*var(--spacing));\n    }\n  }\n  .data-\\[motion\\=from-start\\]\\:slide-in-from-left-52 {\n    &[data-motion=\"from-start\"] {\n      --tw-enter-translate-x: calc(52*var(--spacing)*-1);\n    }\n  }\n  .data-\\[motion\\=to-end\\]\\:slide-out-to-right-52 {\n    &[data-motion=\"to-end\"] {\n      --tw-exit-translate-x: calc(52*var(--spacing));\n    }\n  }\n  .data-\\[motion\\=to-start\\]\\:slide-out-to-left-52 {\n    &[data-motion=\"to-start\"] {\n      --tw-exit-translate-x: calc(52*var(--spacing)*-1);\n    }\n  }\n  .data-\\[motion\\^\\=from-\\]\\:animate-in {\n    &[data-motion^=\"from-\"] {\n      animation: enter var(--tw-duration,.15s)var(--tw-ease,ease);\n    }\n  }\n  .data-\\[motion\\^\\=from-\\]\\:fade-in {\n    &[data-motion^=\"from-\"] {\n      --tw-enter-opacity: 0;\n    }\n  }\n  .data-\\[motion\\^\\=to-\\]\\:animate-out {\n    &[data-motion^=\"to-\"] {\n      animation: exit var(--tw-duration,.15s)var(--tw-ease,ease);\n    }\n  }\n  .data-\\[motion\\^\\=to-\\]\\:fade-out {\n    &[data-motion^=\"to-\"] {\n      --tw-exit-opacity: 0;\n    }\n  }\n  .data-\\[moving\\]\\:opacity-100 {\n    &[data-moving] {\n      opacity: 100%;\n    }\n  }\n  .data-\\[moving\\=true\\]\\:transition-transform {\n    &[data-moving=\"true\"] {\n      transition-property: transform, translate, scale, rotate;\n      transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));\n      transition-duration: var(--tw-duration, var(--default-transition-duration));\n    }\n  }\n  .data-\\[open\\=true\\]\\:block {\n    &[data-open=\"true\"] {\n      display: block;\n    }\n  }\n  .data-\\[open\\=true\\]\\:flex {\n    &[data-open=\"true\"] {\n      display: flex;\n    }\n  }\n  .data-\\[open\\=true\\]\\:-rotate-90 {\n    &[data-open=\"true\"] {\n      rotate: calc(90deg * -1);\n    }\n  }\n  .data-\\[open\\=true\\]\\:rotate-180 {\n    &[data-open=\"true\"] {\n      rotate: 180deg;\n    }\n  }\n  .data-\\[open\\=true\\]\\:border-danger {\n    &[data-open=\"true\"] {\n      border-color: hsl(var(--heroui-danger) / 1);\n    }\n  }\n  .data-\\[open\\=true\\]\\:border-default-400 {\n    &[data-open=\"true\"] {\n      border-color: hsl(var(--heroui-default-400) / 1);\n    }\n  }\n  .data-\\[open\\=true\\]\\:border-default-foreground {\n    &[data-open=\"true\"] {\n      border-color: hsl(var(--heroui-default-foreground) / 1);\n    }\n  }\n  .data-\\[open\\=true\\]\\:border-primary {\n    &[data-open=\"true\"] {\n      border-color: var(--primary);\n    }\n  }\n  .data-\\[open\\=true\\]\\:border-secondary {\n    &[data-open=\"true\"] {\n      border-color: var(--secondary);\n    }\n  }\n  .data-\\[open\\=true\\]\\:border-success {\n    &[data-open=\"true\"] {\n      border-color: hsl(var(--heroui-success) / 1);\n    }\n  }\n  .data-\\[open\\=true\\]\\:border-warning {\n    &[data-open=\"true\"] {\n      border-color: hsl(var(--heroui-warning) / 1);\n    }\n  }\n  .data-\\[open\\=true\\]\\:after\\:w-full {\n    &[data-open=\"true\"] {\n      &::after {\n        content: var(--tw-content);\n        width: 100%;\n      }\n    }\n  }\n  .data-\\[orientation\\=horizontal\\]\\:h-1\\.5 {\n    &[data-orientation=\"horizontal\"] {\n      height: calc(var(--spacing) * 1.5);\n    }\n  }\n  .data-\\[orientation\\=horizontal\\]\\:h-full {\n    &[data-orientation=\"horizontal\"] {\n      height: 100%;\n    }\n  }\n  .data-\\[orientation\\=horizontal\\]\\:h-px {\n    &[data-orientation=\"horizontal\"] {\n      height: 1px;\n    }\n  }\n  .data-\\[orientation\\=horizontal\\]\\:w-full {\n    &[data-orientation=\"horizontal\"] {\n      width: 100%;\n    }\n  }\n  .data-\\[orientation\\=horizontal\\]\\:flex-row {\n    &[data-orientation=\"horizontal\"] {\n      flex-direction: row;\n    }\n  }\n  .data-\\[orientation\\=vertical\\]\\:h-4 {\n    &[data-orientation=\"vertical\"] {\n      height: calc(var(--spacing) * 4);\n    }\n  }\n  .data-\\[orientation\\=vertical\\]\\:h-full {\n    &[data-orientation=\"vertical\"] {\n      height: 100%;\n    }\n  }\n  .data-\\[orientation\\=vertical\\]\\:min-h-44 {\n    &[data-orientation=\"vertical\"] {\n      min-height: calc(var(--spacing) * 44);\n    }\n  }\n  .data-\\[orientation\\=vertical\\]\\:w-1\\.5 {\n    &[data-orientation=\"vertical\"] {\n      width: calc(var(--spacing) * 1.5);\n    }\n  }\n  .data-\\[orientation\\=vertical\\]\\:w-auto {\n    &[data-orientation=\"vertical\"] {\n      width: auto;\n    }\n  }\n  .data-\\[orientation\\=vertical\\]\\:w-full {\n    &[data-orientation=\"vertical\"] {\n      width: 100%;\n    }\n  }\n  .data-\\[orientation\\=vertical\\]\\:w-px {\n    &[data-orientation=\"vertical\"] {\n      width: 1px;\n    }\n  }\n  .data-\\[orientation\\=vertical\\]\\:flex-col {\n    &[data-orientation=\"vertical\"] {\n      flex-direction: column;\n    }\n  }\n  .data-\\[outside-month\\=true\\]\\:before\\:hidden {\n    &[data-outside-month=\"true\"] {\n      &::before {\n        content: var(--tw-content);\n        display: none;\n      }\n    }\n  }\n  .data-\\[disabled\\=true\\]\\:data-\\[outside-month\\=true\\]\\:opacity-0 {\n    &[data-disabled=\"true\"] {\n      &[data-outside-month=\"true\"] {\n        opacity: 0%;\n      }\n    }\n  }\n  .data-\\[panel-group-direction\\=vertical\\]\\:h-px {\n    &[data-panel-group-direction=\"vertical\"] {\n      height: 1px;\n    }\n  }\n  .data-\\[panel-group-direction\\=vertical\\]\\:w-full {\n    &[data-panel-group-direction=\"vertical\"] {\n      width: 100%;\n    }\n  }\n  .data-\\[panel-group-direction\\=vertical\\]\\:flex-col {\n    &[data-panel-group-direction=\"vertical\"] {\n      flex-direction: column;\n    }\n  }\n  .data-\\[panel-group-direction\\=vertical\\]\\:after\\:left-0 {\n    &[data-panel-group-direction=\"vertical\"] {\n      &::after {\n        content: var(--tw-content);\n        left: calc(var(--spacing) * 0);\n      }\n    }\n  }\n  .data-\\[panel-group-direction\\=vertical\\]\\:after\\:h-1 {\n    &[data-panel-group-direction=\"vertical\"] {\n      &::after {\n        content: var(--tw-content);\n        height: calc(var(--spacing) * 1);\n      }\n    }\n  }\n  .data-\\[panel-group-direction\\=vertical\\]\\:after\\:w-full {\n    &[data-panel-group-direction=\"vertical\"] {\n      &::after {\n        content: var(--tw-content);\n        width: 100%;\n      }\n    }\n  }\n  .data-\\[panel-group-direction\\=vertical\\]\\:after\\:translate-x-0 {\n    &[data-panel-group-direction=\"vertical\"] {\n      &::after {\n        content: var(--tw-content);\n        --tw-translate-x: calc(var(--spacing) * 0);\n        translate: var(--tw-translate-x) var(--tw-translate-y);\n      }\n    }\n  }\n  .data-\\[panel-group-direction\\=vertical\\]\\:after\\:-translate-y-1\\/2 {\n    &[data-panel-group-direction=\"vertical\"] {\n      &::after {\n        content: var(--tw-content);\n        --tw-translate-y: calc(calc(1/2 * 100%) * -1);\n        translate: var(--tw-translate-x) var(--tw-translate-y);\n      }\n    }\n  }\n  .data-\\[placeholder\\]\\:text-muted-foreground {\n    &[data-placeholder] {\n      color: var(--muted-foreground);\n    }\n  }\n  .data-\\[editable\\=true\\]\\:data-\\[placeholder\\=true\\]\\:text-danger-300 {\n    &[data-editable=\"true\"] {\n      &[data-placeholder=\"true\"] {\n        color: hsl(var(--heroui-danger-300) / 1);\n      }\n    }\n  }\n  .data-\\[editable\\=true\\]\\:data-\\[placeholder\\=true\\]\\:text-foreground-500 {\n    &[data-editable=\"true\"] {\n      &[data-placeholder=\"true\"] {\n        color: hsl(var(--heroui-foreground-500) / 1);\n      }\n    }\n  }\n  .data-\\[editable\\=true\\]\\:data-\\[placeholder\\=true\\]\\:text-primary-300 {\n    &[data-editable=\"true\"] {\n      &[data-placeholder=\"true\"] {\n        color: hsl(var(--heroui-primary-300) / 1);\n      }\n    }\n  }\n  .data-\\[editable\\=true\\]\\:data-\\[placeholder\\=true\\]\\:text-secondary-300 {\n    &[data-editable=\"true\"] {\n      &[data-placeholder=\"true\"] {\n        color: hsl(var(--heroui-secondary-300) / 1);\n      }\n    }\n  }\n  .data-\\[editable\\=true\\]\\:data-\\[placeholder\\=true\\]\\:text-success-400 {\n    &[data-editable=\"true\"] {\n      &[data-placeholder=\"true\"] {\n        color: hsl(var(--heroui-success-400) / 1);\n      }\n    }\n  }\n  .data-\\[editable\\=true\\]\\:data-\\[placeholder\\=true\\]\\:text-warning-400 {\n    &[data-editable=\"true\"] {\n      &[data-placeholder=\"true\"] {\n        color: hsl(var(--heroui-warning-400) / 1);\n      }\n    }\n  }\n  .data-\\[placement\\=bottom\\]\\:before\\:-top-\\[calc\\(theme\\(spacing\\.5\\)\\/4_-_1\\.5px\\)\\] {\n    &[data-placement=\"bottom\"] {\n      &::before {\n        content: var(--tw-content);\n        top: calc(calc(1.25rem / 4 - 1.5px) * -1);\n      }\n    }\n  }\n  .data-\\[placement\\=bottom\\]\\:before\\:left-1\\/2 {\n    &[data-placement=\"bottom\"] {\n      &::before {\n        content: var(--tw-content);\n        left: calc(1/2 * 100%);\n      }\n    }\n  }\n  .data-\\[placement\\=bottom\\]\\:before\\:-translate-x-1\\/2 {\n    &[data-placement=\"bottom\"] {\n      &::before {\n        content: var(--tw-content);\n        --tw-translate-x: calc(calc(1/2 * 100%) * -1);\n        translate: var(--tw-translate-x) var(--tw-translate-y);\n      }\n    }\n  }\n  .data-\\[placement\\=bottom-center\\]\\:fixed {\n    &[data-placement=\"bottom-center\"] {\n      position: fixed;\n    }\n  }\n  .data-\\[placement\\=bottom-center\\]\\:right-0 {\n    &[data-placement=\"bottom-center\"] {\n      right: calc(var(--spacing) * 0);\n    }\n  }\n  .data-\\[placement\\=bottom-center\\]\\:bottom-0 {\n    &[data-placement=\"bottom-center\"] {\n      bottom: calc(var(--spacing) * 0);\n    }\n  }\n  .data-\\[placement\\=bottom-center\\]\\:left-0 {\n    &[data-placement=\"bottom-center\"] {\n      left: calc(var(--spacing) * 0);\n    }\n  }\n  .data-\\[placement\\=bottom-center\\]\\:left-1\\/2 {\n    &[data-placement=\"bottom-center\"] {\n      left: calc(1/2 * 100%);\n    }\n  }\n  .data-\\[placement\\=bottom-center\\]\\:flex {\n    &[data-placement=\"bottom-center\"] {\n      display: flex;\n    }\n  }\n  .data-\\[placement\\=bottom-center\\]\\:-translate-x-1\\/2 {\n    &[data-placement=\"bottom-center\"] {\n      --tw-translate-x: calc(calc(1/2 * 100%) * -1);\n      translate: var(--tw-translate-x) var(--tw-translate-y);\n    }\n  }\n  .data-\\[placement\\=bottom-center\\]\\:flex-col {\n    &[data-placement=\"bottom-center\"] {\n      flex-direction: column;\n    }\n  }\n  .data-\\[placement\\=bottom-end\\]\\:before\\:-top-\\[calc\\(theme\\(spacing\\.5\\)\\/4_-_1\\.5px\\)\\] {\n    &[data-placement=\"bottom-end\"] {\n      &::before {\n        content: var(--tw-content);\n        top: calc(calc(1.25rem / 4 - 1.5px) * -1);\n      }\n    }\n  }\n  .data-\\[placement\\=bottom-end\\]\\:before\\:right-3 {\n    &[data-placement=\"bottom-end\"] {\n      &::before {\n        content: var(--tw-content);\n        right: calc(var(--spacing) * 3);\n      }\n    }\n  }\n  .data-\\[placement\\=bottom-left\\]\\:fixed {\n    &[data-placement=\"bottom-left\"] {\n      position: fixed;\n    }\n  }\n  .data-\\[placement\\=bottom-left\\]\\:bottom-0 {\n    &[data-placement=\"bottom-left\"] {\n      bottom: calc(var(--spacing) * 0);\n    }\n  }\n  .data-\\[placement\\=bottom-left\\]\\:left-0 {\n    &[data-placement=\"bottom-left\"] {\n      left: calc(var(--spacing) * 0);\n    }\n  }\n  .data-\\[placement\\=bottom-left\\]\\:mx-auto {\n    &[data-placement=\"bottom-left\"] {\n      margin-inline: auto;\n    }\n  }\n  .data-\\[placement\\=bottom-left\\]\\:flex {\n    &[data-placement=\"bottom-left\"] {\n      display: flex;\n    }\n  }\n  .data-\\[placement\\=bottom-left\\]\\:flex-col {\n    &[data-placement=\"bottom-left\"] {\n      flex-direction: column;\n    }\n  }\n  .data-\\[placement\\=bottom-right\\]\\:fixed {\n    &[data-placement=\"bottom-right\"] {\n      position: fixed;\n    }\n  }\n  .data-\\[placement\\=bottom-right\\]\\:right-0 {\n    &[data-placement=\"bottom-right\"] {\n      right: calc(var(--spacing) * 0);\n    }\n  }\n  .data-\\[placement\\=bottom-right\\]\\:bottom-0 {\n    &[data-placement=\"bottom-right\"] {\n      bottom: calc(var(--spacing) * 0);\n    }\n  }\n  .data-\\[placement\\=bottom-right\\]\\:mx-auto {\n    &[data-placement=\"bottom-right\"] {\n      margin-inline: auto;\n    }\n  }\n  .data-\\[placement\\=bottom-right\\]\\:flex {\n    &[data-placement=\"bottom-right\"] {\n      display: flex;\n    }\n  }\n  .data-\\[placement\\=bottom-right\\]\\:flex-col {\n    &[data-placement=\"bottom-right\"] {\n      flex-direction: column;\n    }\n  }\n  .data-\\[placement\\=bottom-start\\]\\:before\\:-top-\\[calc\\(theme\\(spacing\\.5\\)\\/4_-_1\\.5px\\)\\] {\n    &[data-placement=\"bottom-start\"] {\n      &::before {\n        content: var(--tw-content);\n        top: calc(calc(1.25rem / 4 - 1.5px) * -1);\n      }\n    }\n  }\n  .data-\\[placement\\=bottom-start\\]\\:before\\:left-3 {\n    &[data-placement=\"bottom-start\"] {\n      &::before {\n        content: var(--tw-content);\n        left: calc(var(--spacing) * 3);\n      }\n    }\n  }\n  .data-\\[placement\\=left\\]\\:before\\:top-1\\/2 {\n    &[data-placement=\"left\"] {\n      &::before {\n        content: var(--tw-content);\n        top: calc(1/2 * 100%);\n      }\n    }\n  }\n  .data-\\[placement\\=left\\]\\:before\\:-right-\\[calc\\(theme\\(spacing\\.5\\)\\/4_-_2px\\)\\] {\n    &[data-placement=\"left\"] {\n      &::before {\n        content: var(--tw-content);\n        right: calc(calc(1.25rem / 4 - 2px) * -1);\n      }\n    }\n  }\n  .data-\\[placement\\=left\\]\\:before\\:-translate-y-1\\/2 {\n    &[data-placement=\"left\"] {\n      &::before {\n        content: var(--tw-content);\n        --tw-translate-y: calc(calc(1/2 * 100%) * -1);\n        translate: var(--tw-translate-x) var(--tw-translate-y);\n      }\n    }\n  }\n  .data-\\[placement\\=left-end\\]\\:before\\:-right-\\[calc\\(theme\\(spacing\\.5\\)\\/4_-_3px\\)\\] {\n    &[data-placement=\"left-end\"] {\n      &::before {\n        content: var(--tw-content);\n        right: calc(calc(1.25rem / 4 - 3px) * -1);\n      }\n    }\n  }\n  .data-\\[placement\\=left-end\\]\\:before\\:bottom-1\\/4 {\n    &[data-placement=\"left-end\"] {\n      &::before {\n        content: var(--tw-content);\n        bottom: calc(1/4 * 100%);\n      }\n    }\n  }\n  .data-\\[placement\\=left-start\\]\\:before\\:top-1\\/4 {\n    &[data-placement=\"left-start\"] {\n      &::before {\n        content: var(--tw-content);\n        top: calc(1/4 * 100%);\n      }\n    }\n  }\n  .data-\\[placement\\=left-start\\]\\:before\\:-right-\\[calc\\(theme\\(spacing\\.5\\)\\/4_-_3px\\)\\] {\n    &[data-placement=\"left-start\"] {\n      &::before {\n        content: var(--tw-content);\n        right: calc(calc(1.25rem / 4 - 3px) * -1);\n      }\n    }\n  }\n  .data-\\[placement\\=right\\]\\:before\\:top-1\\/2 {\n    &[data-placement=\"right\"] {\n      &::before {\n        content: var(--tw-content);\n        top: calc(1/2 * 100%);\n      }\n    }\n  }\n  .data-\\[placement\\=right\\]\\:before\\:-left-\\[calc\\(theme\\(spacing\\.5\\)\\/4_-_2px\\)\\] {\n    &[data-placement=\"right\"] {\n      &::before {\n        content: var(--tw-content);\n        left: calc(calc(1.25rem / 4 - 2px) * -1);\n      }\n    }\n  }\n  .data-\\[placement\\=right\\]\\:before\\:-translate-y-1\\/2 {\n    &[data-placement=\"right\"] {\n      &::before {\n        content: var(--tw-content);\n        --tw-translate-y: calc(calc(1/2 * 100%) * -1);\n        translate: var(--tw-translate-x) var(--tw-translate-y);\n      }\n    }\n  }\n  .data-\\[placement\\=right-end\\]\\:before\\:bottom-1\\/4 {\n    &[data-placement=\"right-end\"] {\n      &::before {\n        content: var(--tw-content);\n        bottom: calc(1/4 * 100%);\n      }\n    }\n  }\n  .data-\\[placement\\=right-end\\]\\:before\\:-left-\\[calc\\(theme\\(spacing\\.5\\)\\/4_-_3px\\)\\] {\n    &[data-placement=\"right-end\"] {\n      &::before {\n        content: var(--tw-content);\n        left: calc(calc(1.25rem / 4 - 3px) * -1);\n      }\n    }\n  }\n  .data-\\[placement\\=right-start\\]\\:before\\:top-1\\/4 {\n    &[data-placement=\"right-start\"] {\n      &::before {\n        content: var(--tw-content);\n        top: calc(1/4 * 100%);\n      }\n    }\n  }\n  .data-\\[placement\\=right-start\\]\\:before\\:-left-\\[calc\\(theme\\(spacing\\.5\\)\\/4_-_3px\\)\\] {\n    &[data-placement=\"right-start\"] {\n      &::before {\n        content: var(--tw-content);\n        left: calc(calc(1.25rem / 4 - 3px) * -1);\n      }\n    }\n  }\n  .data-\\[placement\\=top\\]\\:before\\:-bottom-\\[calc\\(theme\\(spacing\\.5\\)\\/4_-_1\\.5px\\)\\] {\n    &[data-placement=\"top\"] {\n      &::before {\n        content: var(--tw-content);\n        bottom: calc(calc(1.25rem / 4 - 1.5px) * -1);\n      }\n    }\n  }\n  .data-\\[placement\\=top\\]\\:before\\:left-1\\/2 {\n    &[data-placement=\"top\"] {\n      &::before {\n        content: var(--tw-content);\n        left: calc(1/2 * 100%);\n      }\n    }\n  }\n  .data-\\[placement\\=top\\]\\:before\\:-translate-x-1\\/2 {\n    &[data-placement=\"top\"] {\n      &::before {\n        content: var(--tw-content);\n        --tw-translate-x: calc(calc(1/2 * 100%) * -1);\n        translate: var(--tw-translate-x) var(--tw-translate-y);\n      }\n    }\n  }\n  .data-\\[placement\\=top-center\\]\\:fixed {\n    &[data-placement=\"top-center\"] {\n      position: fixed;\n    }\n  }\n  .data-\\[placement\\=top-center\\]\\:top-0 {\n    &[data-placement=\"top-center\"] {\n      top: calc(var(--spacing) * 0);\n    }\n  }\n  .data-\\[placement\\=top-center\\]\\:right-0 {\n    &[data-placement=\"top-center\"] {\n      right: calc(var(--spacing) * 0);\n    }\n  }\n  .data-\\[placement\\=top-center\\]\\:left-0 {\n    &[data-placement=\"top-center\"] {\n      left: calc(var(--spacing) * 0);\n    }\n  }\n  .data-\\[placement\\=top-center\\]\\:left-1\\/2 {\n    &[data-placement=\"top-center\"] {\n      left: calc(1/2 * 100%);\n    }\n  }\n  .data-\\[placement\\=top-center\\]\\:flex {\n    &[data-placement=\"top-center\"] {\n      display: flex;\n    }\n  }\n  .data-\\[placement\\=top-center\\]\\:-translate-x-1\\/2 {\n    &[data-placement=\"top-center\"] {\n      --tw-translate-x: calc(calc(1/2 * 100%) * -1);\n      translate: var(--tw-translate-x) var(--tw-translate-y);\n    }\n  }\n  .data-\\[placement\\=top-center\\]\\:flex-col {\n    &[data-placement=\"top-center\"] {\n      flex-direction: column;\n    }\n  }\n  .data-\\[placement\\=top-end\\]\\:before\\:right-3 {\n    &[data-placement=\"top-end\"] {\n      &::before {\n        content: var(--tw-content);\n        right: calc(var(--spacing) * 3);\n      }\n    }\n  }\n  .data-\\[placement\\=top-end\\]\\:before\\:-bottom-\\[calc\\(theme\\(spacing\\.5\\)\\/4_-_1\\.5px\\)\\] {\n    &[data-placement=\"top-end\"] {\n      &::before {\n        content: var(--tw-content);\n        bottom: calc(calc(1.25rem / 4 - 1.5px) * -1);\n      }\n    }\n  }\n  .data-\\[placement\\=top-left\\]\\:fixed {\n    &[data-placement=\"top-left\"] {\n      position: fixed;\n    }\n  }\n  .data-\\[placement\\=top-left\\]\\:top-0 {\n    &[data-placement=\"top-left\"] {\n      top: calc(var(--spacing) * 0);\n    }\n  }\n  .data-\\[placement\\=top-left\\]\\:left-0 {\n    &[data-placement=\"top-left\"] {\n      left: calc(var(--spacing) * 0);\n    }\n  }\n  .data-\\[placement\\=top-left\\]\\:mx-auto {\n    &[data-placement=\"top-left\"] {\n      margin-inline: auto;\n    }\n  }\n  .data-\\[placement\\=top-left\\]\\:flex {\n    &[data-placement=\"top-left\"] {\n      display: flex;\n    }\n  }\n  .data-\\[placement\\=top-left\\]\\:flex-col {\n    &[data-placement=\"top-left\"] {\n      flex-direction: column;\n    }\n  }\n  .data-\\[placement\\=top-right\\]\\:fixed {\n    &[data-placement=\"top-right\"] {\n      position: fixed;\n    }\n  }\n  .data-\\[placement\\=top-right\\]\\:top-0 {\n    &[data-placement=\"top-right\"] {\n      top: calc(var(--spacing) * 0);\n    }\n  }\n  .data-\\[placement\\=top-right\\]\\:right-0 {\n    &[data-placement=\"top-right\"] {\n      right: calc(var(--spacing) * 0);\n    }\n  }\n  .data-\\[placement\\=top-right\\]\\:mx-auto {\n    &[data-placement=\"top-right\"] {\n      margin-inline: auto;\n    }\n  }\n  .data-\\[placement\\=top-right\\]\\:flex {\n    &[data-placement=\"top-right\"] {\n      display: flex;\n    }\n  }\n  .data-\\[placement\\=top-right\\]\\:flex-col {\n    &[data-placement=\"top-right\"] {\n      flex-direction: column;\n    }\n  }\n  .data-\\[placement\\=top-start\\]\\:before\\:-bottom-\\[calc\\(theme\\(spacing\\.5\\)\\/4_-_1\\.5px\\)\\] {\n    &[data-placement=\"top-start\"] {\n      &::before {\n        content: var(--tw-content);\n        bottom: calc(calc(1.25rem / 4 - 1.5px) * -1);\n      }\n    }\n  }\n  .data-\\[placement\\=top-start\\]\\:before\\:left-3 {\n    &[data-placement=\"top-start\"] {\n      &::before {\n        content: var(--tw-content);\n        left: calc(var(--spacing) * 3);\n      }\n    }\n  }\n  .data-\\[pressed\\=true\\]\\:scale-100 {\n    &[data-pressed=\"true\"] {\n      --tw-scale-x: 100%;\n      --tw-scale-y: 100%;\n      --tw-scale-z: 100%;\n      scale: var(--tw-scale-x) var(--tw-scale-y);\n    }\n  }\n  .data-\\[pressed\\=true\\]\\:scale-\\[0\\.97\\] {\n    &[data-pressed=\"true\"] {\n      scale: 0.97;\n    }\n  }\n  .data-\\[pressed\\=true\\]\\:opacity-50 {\n    &[data-pressed=\"true\"] {\n      opacity: 50%;\n    }\n  }\n  .data-\\[pressed\\=true\\]\\:opacity-70 {\n    &[data-pressed=\"true\"] {\n      opacity: 70%;\n    }\n  }\n  .data-\\[pressed\\=true\\]\\:opacity-disabled {\n    &[data-pressed=\"true\"] {\n      opacity: var(--heroui-disabled-opacity);\n    }\n  }\n  .data-\\[range-end\\=true\\]\\:before\\:rounded-e-full {\n    &[data-range-end=\"true\"] {\n      &::before {\n        content: var(--tw-content);\n        border-start-end-radius: calc(infinity * 1px);\n        border-end-end-radius: calc(infinity * 1px);\n      }\n    }\n  }\n  .data-\\[range-start\\=true\\]\\:before\\:rounded-s-full {\n    &[data-range-start=\"true\"] {\n      &::before {\n        content: var(--tw-content);\n        border-start-start-radius: calc(infinity * 1px);\n        border-end-start-radius: calc(infinity * 1px);\n      }\n    }\n  }\n  .data-\\[readonly\\=true\\]\\:cursor-default {\n    &[data-readonly=\"true\"] {\n      cursor: default;\n    }\n  }\n  .data-\\[right-scroll\\=true\\]\\:\\[mask-image\\:linear-gradient\\(90deg\\,\\#000_calc\\(100\\%_-_var\\(--scroll-shadow-size\\)\\)\\,transparent\\)\\] {\n    &[data-right-scroll=\"true\"] {\n      mask-image: linear-gradient(90deg,#000 calc(100% - var(--scroll-shadow-size)),transparent);\n    }\n  }\n  .data-\\[selectable\\=true\\]\\:focus\\:border-danger {\n    &[data-selectable=\"true\"] {\n      &:focus {\n        border-color: hsl(var(--heroui-danger) / 1);\n      }\n    }\n  }\n  .data-\\[selectable\\=true\\]\\:focus\\:border-default {\n    &[data-selectable=\"true\"] {\n      &:focus {\n        border-color: hsl(var(--heroui-default) / 1);\n      }\n    }\n  }\n  .data-\\[selectable\\=true\\]\\:focus\\:border-primary {\n    &[data-selectable=\"true\"] {\n      &:focus {\n        border-color: var(--primary);\n      }\n    }\n  }\n  .data-\\[selectable\\=true\\]\\:focus\\:border-secondary {\n    &[data-selectable=\"true\"] {\n      &:focus {\n        border-color: var(--secondary);\n      }\n    }\n  }\n  .data-\\[selectable\\=true\\]\\:focus\\:border-success {\n    &[data-selectable=\"true\"] {\n      &:focus {\n        border-color: hsl(var(--heroui-success) / 1);\n      }\n    }\n  }\n  .data-\\[selectable\\=true\\]\\:focus\\:border-warning {\n    &[data-selectable=\"true\"] {\n      &:focus {\n        border-color: hsl(var(--heroui-warning) / 1);\n      }\n    }\n  }\n  .data-\\[selectable\\=true\\]\\:focus\\:bg-danger {\n    &[data-selectable=\"true\"] {\n      &:focus {\n        background-color: hsl(var(--heroui-danger) / 1);\n      }\n    }\n  }\n  .data-\\[selectable\\=true\\]\\:focus\\:bg-danger\\/20 {\n    &[data-selectable=\"true\"] {\n      &:focus {\n        background-color: hsl(var(--heroui-danger) / 1);\n        @supports (color: color-mix(in lab, red, red)) {\n          background-color: color-mix(in oklab, hsl(var(--heroui-danger) / 1) 20%, transparent);\n        }\n      }\n    }\n  }\n  .data-\\[selectable\\=true\\]\\:focus\\:bg-default {\n    &[data-selectable=\"true\"] {\n      &:focus {\n        background-color: hsl(var(--heroui-default) / 1);\n      }\n    }\n  }\n  .data-\\[selectable\\=true\\]\\:focus\\:bg-default-100 {\n    &[data-selectable=\"true\"] {\n      &:focus {\n        background-color: hsl(var(--heroui-default-100) / 1);\n      }\n    }\n  }\n  .data-\\[selectable\\=true\\]\\:focus\\:bg-default\\/40 {\n    &[data-selectable=\"true\"] {\n      &:focus {\n        background-color: hsl(var(--heroui-default) / 1);\n        @supports (color: color-mix(in lab, red, red)) {\n          background-color: color-mix(in oklab, hsl(var(--heroui-default) / 1) 40%, transparent);\n        }\n      }\n    }\n  }\n  .data-\\[selectable\\=true\\]\\:focus\\:bg-primary {\n    &[data-selectable=\"true\"] {\n      &:focus {\n        background-color: var(--primary);\n      }\n    }\n  }\n  .data-\\[selectable\\=true\\]\\:focus\\:bg-primary\\/20 {\n    &[data-selectable=\"true\"] {\n      &:focus {\n        background-color: var(--primary);\n        @supports (color: color-mix(in lab, red, red)) {\n          background-color: color-mix(in oklab, var(--primary) 20%, transparent);\n        }\n      }\n    }\n  }\n  .data-\\[selectable\\=true\\]\\:focus\\:bg-secondary {\n    &[data-selectable=\"true\"] {\n      &:focus {\n        background-color: var(--secondary);\n      }\n    }\n  }\n  .data-\\[selectable\\=true\\]\\:focus\\:bg-secondary\\/20 {\n    &[data-selectable=\"true\"] {\n      &:focus {\n        background-color: var(--secondary);\n        @supports (color: color-mix(in lab, red, red)) {\n          background-color: color-mix(in oklab, var(--secondary) 20%, transparent);\n        }\n      }\n    }\n  }\n  .data-\\[selectable\\=true\\]\\:focus\\:bg-success {\n    &[data-selectable=\"true\"] {\n      &:focus {\n        background-color: hsl(var(--heroui-success) / 1);\n      }\n    }\n  }\n  .data-\\[selectable\\=true\\]\\:focus\\:bg-success\\/20 {\n    &[data-selectable=\"true\"] {\n      &:focus {\n        background-color: hsl(var(--heroui-success) / 1);\n        @supports (color: color-mix(in lab, red, red)) {\n          background-color: color-mix(in oklab, hsl(var(--heroui-success) / 1) 20%, transparent);\n        }\n      }\n    }\n  }\n  .data-\\[selectable\\=true\\]\\:focus\\:bg-warning {\n    &[data-selectable=\"true\"] {\n      &:focus {\n        background-color: hsl(var(--heroui-warning) / 1);\n      }\n    }\n  }\n  .data-\\[selectable\\=true\\]\\:focus\\:bg-warning\\/20 {\n    &[data-selectable=\"true\"] {\n      &:focus {\n        background-color: hsl(var(--heroui-warning) / 1);\n        @supports (color: color-mix(in lab, red, red)) {\n          background-color: color-mix(in oklab, hsl(var(--heroui-warning) / 1) 20%, transparent);\n        }\n      }\n    }\n  }\n  .data-\\[selectable\\=true\\]\\:focus\\:text-danger {\n    &[data-selectable=\"true\"] {\n      &:focus {\n        color: hsl(var(--heroui-danger) / 1);\n      }\n    }\n  }\n  .data-\\[selectable\\=true\\]\\:focus\\:text-danger-foreground {\n    &[data-selectable=\"true\"] {\n      &:focus {\n        color: hsl(var(--heroui-danger-foreground) / 1);\n      }\n    }\n  }\n  .data-\\[selectable\\=true\\]\\:focus\\:text-default-500 {\n    &[data-selectable=\"true\"] {\n      &:focus {\n        color: hsl(var(--heroui-default-500) / 1);\n      }\n    }\n  }\n  .data-\\[selectable\\=true\\]\\:focus\\:text-default-foreground {\n    &[data-selectable=\"true\"] {\n      &:focus {\n        color: hsl(var(--heroui-default-foreground) / 1);\n      }\n    }\n  }\n  .data-\\[selectable\\=true\\]\\:focus\\:text-primary {\n    &[data-selectable=\"true\"] {\n      &:focus {\n        color: var(--primary);\n      }\n    }\n  }\n  .data-\\[selectable\\=true\\]\\:focus\\:text-primary-foreground {\n    &[data-selectable=\"true\"] {\n      &:focus {\n        color: var(--primary-foreground);\n      }\n    }\n  }\n  .data-\\[selectable\\=true\\]\\:focus\\:text-secondary {\n    &[data-selectable=\"true\"] {\n      &:focus {\n        color: var(--secondary);\n      }\n    }\n  }\n  .data-\\[selectable\\=true\\]\\:focus\\:text-secondary-foreground {\n    &[data-selectable=\"true\"] {\n      &:focus {\n        color: var(--secondary-foreground);\n      }\n    }\n  }\n  .data-\\[selectable\\=true\\]\\:focus\\:text-success {\n    &[data-selectable=\"true\"] {\n      &:focus {\n        color: hsl(var(--heroui-success) / 1);\n      }\n    }\n  }\n  .data-\\[selectable\\=true\\]\\:focus\\:text-success-foreground {\n    &[data-selectable=\"true\"] {\n      &:focus {\n        color: hsl(var(--heroui-success-foreground) / 1);\n      }\n    }\n  }\n  .data-\\[selectable\\=true\\]\\:focus\\:text-warning {\n    &[data-selectable=\"true\"] {\n      &:focus {\n        color: hsl(var(--heroui-warning) / 1);\n      }\n    }\n  }\n  .data-\\[selectable\\=true\\]\\:focus\\:text-warning-foreground {\n    &[data-selectable=\"true\"] {\n      &:focus {\n        color: hsl(var(--heroui-warning-foreground) / 1);\n      }\n    }\n  }\n  .data-\\[selectable\\=true\\]\\:focus\\:shadow-danger\\/30 {\n    &[data-selectable=\"true\"] {\n      &:focus {\n        --tw-shadow-color: hsl(var(--heroui-danger) / 1);\n        @supports (color: color-mix(in lab, red, red)) {\n          --tw-shadow-color: color-mix(in oklab, color-mix(in oklab, hsl(var(--heroui-danger) / 1) 30%, transparent) var(--tw-shadow-alpha), transparent);\n        }\n      }\n    }\n  }\n  .data-\\[selectable\\=true\\]\\:focus\\:shadow-default\\/50 {\n    &[data-selectable=\"true\"] {\n      &:focus {\n        --tw-shadow-color: hsl(var(--heroui-default) / 1);\n        @supports (color: color-mix(in lab, red, red)) {\n          --tw-shadow-color: color-mix(in oklab, color-mix(in oklab, hsl(var(--heroui-default) / 1) 50%, transparent) var(--tw-shadow-alpha), transparent);\n        }\n      }\n    }\n  }\n  .data-\\[selectable\\=true\\]\\:focus\\:shadow-primary\\/30 {\n    &[data-selectable=\"true\"] {\n      &:focus {\n        --tw-shadow-color: var(--primary);\n        @supports (color: color-mix(in lab, red, red)) {\n          --tw-shadow-color: color-mix(in oklab, color-mix(in oklab, var(--primary) 30%, transparent) var(--tw-shadow-alpha), transparent);\n        }\n      }\n    }\n  }\n  .data-\\[selectable\\=true\\]\\:focus\\:shadow-secondary\\/30 {\n    &[data-selectable=\"true\"] {\n      &:focus {\n        --tw-shadow-color: var(--secondary);\n        @supports (color: color-mix(in lab, red, red)) {\n          --tw-shadow-color: color-mix(in oklab, color-mix(in oklab, var(--secondary) 30%, transparent) var(--tw-shadow-alpha), transparent);\n        }\n      }\n    }\n  }\n  .data-\\[selectable\\=true\\]\\:focus\\:shadow-success\\/30 {\n    &[data-selectable=\"true\"] {\n      &:focus {\n        --tw-shadow-color: hsl(var(--heroui-success) / 1);\n        @supports (color: color-mix(in lab, red, red)) {\n          --tw-shadow-color: color-mix(in oklab, color-mix(in oklab, hsl(var(--heroui-success) / 1) 30%, transparent) var(--tw-shadow-alpha), transparent);\n        }\n      }\n    }\n  }\n  .data-\\[selectable\\=true\\]\\:focus\\:shadow-warning\\/30 {\n    &[data-selectable=\"true\"] {\n      &:focus {\n        --tw-shadow-color: hsl(var(--heroui-warning) / 1);\n        @supports (color: color-mix(in lab, red, red)) {\n          --tw-shadow-color: color-mix(in oklab, color-mix(in oklab, hsl(var(--heroui-warning) / 1) 30%, transparent) var(--tw-shadow-alpha), transparent);\n        }\n      }\n    }\n  }\n  .data-\\[selected\\=true\\]\\:bg-accent {\n    &[data-selected=\"true\"] {\n      background-color: var(--accent);\n    }\n  }\n  .data-\\[selected\\=true\\]\\:bg-danger {\n    &[data-selected=\"true\"] {\n      background-color: hsl(var(--heroui-danger) / 1);\n    }\n  }\n  .data-\\[selected\\=true\\]\\:bg-default {\n    &[data-selected=\"true\"] {\n      background-color: hsl(var(--heroui-default) / 1);\n    }\n  }\n  .data-\\[selected\\=true\\]\\:bg-foreground {\n    &[data-selected=\"true\"] {\n      background-color: var(--foreground);\n    }\n  }\n  .data-\\[selected\\=true\\]\\:bg-primary {\n    &[data-selected=\"true\"] {\n      background-color: var(--primary);\n    }\n  }\n  .data-\\[selected\\=true\\]\\:bg-secondary {\n    &[data-selected=\"true\"] {\n      background-color: var(--secondary);\n    }\n  }\n  .data-\\[selected\\=true\\]\\:bg-success {\n    &[data-selected=\"true\"] {\n      background-color: hsl(var(--heroui-success) / 1);\n    }\n  }\n  .data-\\[selected\\=true\\]\\:bg-warning {\n    &[data-selected=\"true\"] {\n      background-color: hsl(var(--heroui-warning) / 1);\n    }\n  }\n  .data-\\[selected\\=true\\]\\:text-accent-foreground {\n    &[data-selected=\"true\"] {\n      color: var(--accent-foreground);\n    }\n  }\n  .data-\\[selected\\=true\\]\\:text-background {\n    &[data-selected=\"true\"] {\n      color: var(--background);\n    }\n  }\n  .data-\\[selected\\=true\\]\\:text-danger {\n    &[data-selected=\"true\"] {\n      color: hsl(var(--heroui-danger) / 1);\n    }\n  }\n  .data-\\[selected\\=true\\]\\:text-danger-foreground {\n    &[data-selected=\"true\"] {\n      color: hsl(var(--heroui-danger-foreground) / 1);\n    }\n  }\n  .data-\\[selected\\=true\\]\\:text-default-foreground {\n    &[data-selected=\"true\"] {\n      color: hsl(var(--heroui-default-foreground) / 1);\n    }\n  }\n  .data-\\[selected\\=true\\]\\:text-primary {\n    &[data-selected=\"true\"] {\n      color: var(--primary);\n    }\n  }\n  .data-\\[selected\\=true\\]\\:text-primary-foreground {\n    &[data-selected=\"true\"] {\n      color: var(--primary-foreground);\n    }\n  }\n  .data-\\[selected\\=true\\]\\:text-secondary {\n    &[data-selected=\"true\"] {\n      color: var(--secondary);\n    }\n  }\n  .data-\\[selected\\=true\\]\\:text-secondary-foreground {\n    &[data-selected=\"true\"] {\n      color: var(--secondary-foreground);\n    }\n  }\n  .data-\\[selected\\=true\\]\\:text-success-600 {\n    &[data-selected=\"true\"] {\n      color: hsl(var(--heroui-success-600) / 1);\n    }\n  }\n  .data-\\[selected\\=true\\]\\:text-success-foreground {\n    &[data-selected=\"true\"] {\n      color: hsl(var(--heroui-success-foreground) / 1);\n    }\n  }\n  .data-\\[selected\\=true\\]\\:text-warning-600 {\n    &[data-selected=\"true\"] {\n      color: hsl(var(--heroui-warning-600) / 1);\n    }\n  }\n  .data-\\[selected\\=true\\]\\:text-warning-foreground {\n    &[data-selected=\"true\"] {\n      color: hsl(var(--heroui-warning-foreground) / 1);\n    }\n  }\n  .data-\\[selected\\=true\\]\\:shadow-md {\n    &[data-selected=\"true\"] {\n      --tw-shadow: 0 4px 6px -1px var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 2px 4px -2px var(--tw-shadow-color, rgb(0 0 0 / 0.1));\n      box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\n    }\n  }\n  .data-\\[selected\\=true\\]\\:shadow-none {\n    &[data-selected=\"true\"] {\n      --tw-shadow: 0 0 #0000;\n      box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\n    }\n  }\n  .data-\\[selected\\=true\\]\\:shadow-danger\\/40 {\n    &[data-selected=\"true\"] {\n      --tw-shadow-color: hsl(var(--heroui-danger) / 1);\n      @supports (color: color-mix(in lab, red, red)) {\n        --tw-shadow-color: color-mix(in oklab, color-mix(in oklab, hsl(var(--heroui-danger) / 1) 40%, transparent) var(--tw-shadow-alpha), transparent);\n      }\n    }\n  }\n  .data-\\[selected\\=true\\]\\:shadow-foreground\\/40 {\n    &[data-selected=\"true\"] {\n      --tw-shadow-color: var(--foreground);\n      @supports (color: color-mix(in lab, red, red)) {\n        --tw-shadow-color: color-mix(in oklab, color-mix(in oklab, var(--foreground) 40%, transparent) var(--tw-shadow-alpha), transparent);\n      }\n    }\n  }\n  .data-\\[selected\\=true\\]\\:shadow-primary\\/40 {\n    &[data-selected=\"true\"] {\n      --tw-shadow-color: var(--primary);\n      @supports (color: color-mix(in lab, red, red)) {\n        --tw-shadow-color: color-mix(in oklab, color-mix(in oklab, var(--primary) 40%, transparent) var(--tw-shadow-alpha), transparent);\n      }\n    }\n  }\n  .data-\\[selected\\=true\\]\\:shadow-secondary\\/40 {\n    &[data-selected=\"true\"] {\n      --tw-shadow-color: var(--secondary);\n      @supports (color: color-mix(in lab, red, red)) {\n        --tw-shadow-color: color-mix(in oklab, color-mix(in oklab, var(--secondary) 40%, transparent) var(--tw-shadow-alpha), transparent);\n      }\n    }\n  }\n  .data-\\[selected\\=true\\]\\:shadow-success\\/40 {\n    &[data-selected=\"true\"] {\n      --tw-shadow-color: hsl(var(--heroui-success) / 1);\n      @supports (color: color-mix(in lab, red, red)) {\n        --tw-shadow-color: color-mix(in oklab, color-mix(in oklab, hsl(var(--heroui-success) / 1) 40%, transparent) var(--tw-shadow-alpha), transparent);\n      }\n    }\n  }\n  .data-\\[selected\\=true\\]\\:shadow-warning\\/40 {\n    &[data-selected=\"true\"] {\n      --tw-shadow-color: hsl(var(--heroui-warning) / 1);\n      @supports (color: color-mix(in lab, red, red)) {\n        --tw-shadow-color: color-mix(in oklab, color-mix(in oklab, hsl(var(--heroui-warning) / 1) 40%, transparent) var(--tw-shadow-alpha), transparent);\n      }\n    }\n  }\n  .data-\\[selected\\=true\\]\\:before\\:opacity-100 {\n    &[data-selected=\"true\"] {\n      &::before {\n        content: var(--tw-content);\n        opacity: 100%;\n      }\n    }\n  }\n  .data-\\[selected\\=true\\]\\:after\\:bg-danger {\n    &[data-selected=\"true\"] {\n      &::after {\n        content: var(--tw-content);\n        background-color: hsl(var(--heroui-danger) / 1);\n      }\n    }\n  }\n  .data-\\[selected\\=true\\]\\:after\\:bg-foreground {\n    &[data-selected=\"true\"] {\n      &::after {\n        content: var(--tw-content);\n        background-color: var(--foreground);\n      }\n    }\n  }\n  .data-\\[selected\\=true\\]\\:after\\:bg-primary {\n    &[data-selected=\"true\"] {\n      &::after {\n        content: var(--tw-content);\n        background-color: var(--primary);\n      }\n    }\n  }\n  .data-\\[selected\\=true\\]\\:after\\:bg-secondary {\n    &[data-selected=\"true\"] {\n      &::after {\n        content: var(--tw-content);\n        background-color: var(--secondary);\n      }\n    }\n  }\n  .data-\\[selected\\=true\\]\\:after\\:bg-success {\n    &[data-selected=\"true\"] {\n      &::after {\n        content: var(--tw-content);\n        background-color: hsl(var(--heroui-success) / 1);\n      }\n    }\n  }\n  .data-\\[selected\\=true\\]\\:after\\:bg-warning {\n    &[data-selected=\"true\"] {\n      &::after {\n        content: var(--tw-content);\n        background-color: hsl(var(--heroui-warning) / 1);\n      }\n    }\n  }\n  .data-\\[selected\\=true\\]\\:after\\:opacity-100 {\n    &[data-selected=\"true\"] {\n      &::after {\n        content: var(--tw-content);\n        opacity: 100%;\n      }\n    }\n  }\n  .data-\\[selected\\=true\\]\\:data-\\[hover\\=true\\]\\:bg-danger {\n    &[data-selected=\"true\"] {\n      &[data-hover=\"true\"] {\n        background-color: hsl(var(--heroui-danger) / 1);\n      }\n    }\n  }\n  .data-\\[selected\\=true\\]\\:data-\\[hover\\=true\\]\\:bg-foreground {\n    &[data-selected=\"true\"] {\n      &[data-hover=\"true\"] {\n        background-color: var(--foreground);\n      }\n    }\n  }\n  .data-\\[selected\\=true\\]\\:data-\\[hover\\=true\\]\\:bg-primary {\n    &[data-selected=\"true\"] {\n      &[data-hover=\"true\"] {\n        background-color: var(--primary);\n      }\n    }\n  }\n  .data-\\[selected\\=true\\]\\:data-\\[hover\\=true\\]\\:bg-secondary {\n    &[data-selected=\"true\"] {\n      &[data-hover=\"true\"] {\n        background-color: var(--secondary);\n      }\n    }\n  }\n  .data-\\[selected\\=true\\]\\:data-\\[hover\\=true\\]\\:bg-success {\n    &[data-selected=\"true\"] {\n      &[data-hover=\"true\"] {\n        background-color: hsl(var(--heroui-success) / 1);\n      }\n    }\n  }\n  .data-\\[selected\\=true\\]\\:data-\\[hover\\=true\\]\\:bg-warning {\n    &[data-selected=\"true\"] {\n      &[data-hover=\"true\"] {\n        background-color: hsl(var(--heroui-warning) / 1);\n      }\n    }\n  }\n  .data-\\[selected\\=true\\]\\:data-\\[hover\\=true\\]\\:text-background {\n    &[data-selected=\"true\"] {\n      &[data-hover=\"true\"] {\n        color: var(--background);\n      }\n    }\n  }\n  .data-\\[selected\\=true\\]\\:data-\\[hover\\=true\\]\\:text-danger-foreground {\n    &[data-selected=\"true\"] {\n      &[data-hover=\"true\"] {\n        color: hsl(var(--heroui-danger-foreground) / 1);\n      }\n    }\n  }\n  .data-\\[selected\\=true\\]\\:data-\\[hover\\=true\\]\\:text-primary-foreground {\n    &[data-selected=\"true\"] {\n      &[data-hover=\"true\"] {\n        color: var(--primary-foreground);\n      }\n    }\n  }\n  .data-\\[selected\\=true\\]\\:data-\\[hover\\=true\\]\\:text-secondary-foreground {\n    &[data-selected=\"true\"] {\n      &[data-hover=\"true\"] {\n        color: var(--secondary-foreground);\n      }\n    }\n  }\n  .data-\\[selected\\=true\\]\\:data-\\[hover\\=true\\]\\:text-success-foreground {\n    &[data-selected=\"true\"] {\n      &[data-hover=\"true\"] {\n        color: hsl(var(--heroui-success-foreground) / 1);\n      }\n    }\n  }\n  .data-\\[selected\\=true\\]\\:data-\\[hover\\=true\\]\\:text-warning-foreground {\n    &[data-selected=\"true\"] {\n      &[data-hover=\"true\"] {\n        color: hsl(var(--heroui-warning-foreground) / 1);\n      }\n    }\n  }\n  .data-\\[selected\\=true\\]\\:data-\\[range-selection\\=true\\]\\:bg-transparent {\n    &[data-selected=\"true\"] {\n      &[data-range-selection=\"true\"] {\n        background-color: transparent;\n      }\n    }\n  }\n  .data-\\[selected\\=true\\]\\:data-\\[range-selection\\=true\\]\\:text-danger-500 {\n    &[data-selected=\"true\"] {\n      &[data-range-selection=\"true\"] {\n        color: hsl(var(--heroui-danger-500) / 1);\n      }\n    }\n  }\n  .data-\\[selected\\=true\\]\\:data-\\[range-selection\\=true\\]\\:text-foreground {\n    &[data-selected=\"true\"] {\n      &[data-range-selection=\"true\"] {\n        color: var(--foreground);\n      }\n    }\n  }\n  .data-\\[selected\\=true\\]\\:data-\\[range-selection\\=true\\]\\:text-primary {\n    &[data-selected=\"true\"] {\n      &[data-range-selection=\"true\"] {\n        color: var(--primary);\n      }\n    }\n  }\n  .data-\\[selected\\=true\\]\\:data-\\[range-selection\\=true\\]\\:text-secondary {\n    &[data-selected=\"true\"] {\n      &[data-range-selection=\"true\"] {\n        color: var(--secondary);\n      }\n    }\n  }\n  .data-\\[selected\\=true\\]\\:data-\\[range-selection\\=true\\]\\:text-success-600 {\n    &[data-selected=\"true\"] {\n      &[data-range-selection=\"true\"] {\n        color: hsl(var(--heroui-success-600) / 1);\n      }\n    }\n  }\n  .data-\\[selected\\=true\\]\\:data-\\[range-selection\\=true\\]\\:text-warning-500 {\n    &[data-selected=\"true\"] {\n      &[data-range-selection=\"true\"] {\n        color: hsl(var(--heroui-warning-500) / 1);\n      }\n    }\n  }\n  .data-\\[selected\\=true\\]\\:data-\\[range-selection\\=true\\]\\:before\\:bg-danger-50 {\n    &[data-selected=\"true\"] {\n      &[data-range-selection=\"true\"] {\n        &::before {\n          content: var(--tw-content);\n          background-color: hsl(var(--heroui-danger-50) / 1);\n        }\n      }\n    }\n  }\n  .data-\\[selected\\=true\\]\\:data-\\[range-selection\\=true\\]\\:before\\:bg-foreground\\/10 {\n    &[data-selected=\"true\"] {\n      &[data-range-selection=\"true\"] {\n        &::before {\n          content: var(--tw-content);\n          background-color: var(--foreground);\n          @supports (color: color-mix(in lab, red, red)) {\n            background-color: color-mix(in oklab, var(--foreground) 10%, transparent);\n          }\n        }\n      }\n    }\n  }\n  .data-\\[selected\\=true\\]\\:data-\\[range-selection\\=true\\]\\:before\\:bg-primary-50 {\n    &[data-selected=\"true\"] {\n      &[data-range-selection=\"true\"] {\n        &::before {\n          content: var(--tw-content);\n          background-color: hsl(var(--heroui-primary-50) / 1);\n        }\n      }\n    }\n  }\n  .data-\\[selected\\=true\\]\\:data-\\[range-selection\\=true\\]\\:before\\:bg-secondary-50 {\n    &[data-selected=\"true\"] {\n      &[data-range-selection=\"true\"] {\n        &::before {\n          content: var(--tw-content);\n          background-color: hsl(var(--heroui-secondary-50) / 1);\n        }\n      }\n    }\n  }\n  .data-\\[selected\\=true\\]\\:data-\\[range-selection\\=true\\]\\:before\\:bg-success-100 {\n    &[data-selected=\"true\"] {\n      &[data-range-selection=\"true\"] {\n        &::before {\n          content: var(--tw-content);\n          background-color: hsl(var(--heroui-success-100) / 1);\n        }\n      }\n    }\n  }\n  .data-\\[selected\\=true\\]\\:data-\\[range-selection\\=true\\]\\:before\\:bg-warning-100 {\n    &[data-selected=\"true\"] {\n      &[data-range-selection=\"true\"] {\n        &::before {\n          content: var(--tw-content);\n          background-color: hsl(var(--heroui-warning-100) / 1);\n        }\n      }\n    }\n  }\n  .data-\\[selected\\=true\\]\\:data-\\[range-selection\\=true\\]\\:data-\\[outside-month\\=true\\]\\:bg-transparent {\n    &[data-selected=\"true\"] {\n      &[data-range-selection=\"true\"] {\n        &[data-outside-month=\"true\"] {\n          background-color: transparent;\n        }\n      }\n    }\n  }\n  .data-\\[selected\\=true\\]\\:data-\\[range-selection\\=true\\]\\:data-\\[outside-month\\=true\\]\\:text-default-300 {\n    &[data-selected=\"true\"] {\n      &[data-range-selection=\"true\"] {\n        &[data-outside-month=\"true\"] {\n          color: hsl(var(--heroui-default-300) / 1);\n        }\n      }\n    }\n  }\n  .data-\\[selection-end\\=true\\]\\:before\\:rounded-e-full {\n    &[data-selection-end=\"true\"] {\n      &::before {\n        content: var(--tw-content);\n        border-start-end-radius: calc(infinity * 1px);\n        border-end-end-radius: calc(infinity * 1px);\n      }\n    }\n  }\n  .data-\\[selected\\=true\\]\\:data-\\[selection-end\\=true\\]\\:shadow-md {\n    &[data-selected=\"true\"] {\n      &[data-selection-end=\"true\"] {\n        --tw-shadow: 0 4px 6px -1px var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 2px 4px -2px var(--tw-shadow-color, rgb(0 0 0 / 0.1));\n        box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\n      }\n    }\n  }\n  .data-\\[selected\\=true\\]\\:data-\\[selection-end\\=true\\]\\:data-\\[range-selection\\=true\\]\\:rounded-full {\n    &[data-selected=\"true\"] {\n      &[data-selection-end=\"true\"] {\n        &[data-range-selection=\"true\"] {\n          border-radius: calc(infinity * 1px);\n        }\n      }\n    }\n  }\n  .data-\\[selected\\=true\\]\\:data-\\[selection-end\\=true\\]\\:data-\\[range-selection\\=true\\]\\:bg-danger {\n    &[data-selected=\"true\"] {\n      &[data-selection-end=\"true\"] {\n        &[data-range-selection=\"true\"] {\n          background-color: hsl(var(--heroui-danger) / 1);\n        }\n      }\n    }\n  }\n  .data-\\[selected\\=true\\]\\:data-\\[selection-end\\=true\\]\\:data-\\[range-selection\\=true\\]\\:bg-foreground {\n    &[data-selected=\"true\"] {\n      &[data-selection-end=\"true\"] {\n        &[data-range-selection=\"true\"] {\n          background-color: var(--foreground);\n        }\n      }\n    }\n  }\n  .data-\\[selected\\=true\\]\\:data-\\[selection-end\\=true\\]\\:data-\\[range-selection\\=true\\]\\:bg-primary {\n    &[data-selected=\"true\"] {\n      &[data-selection-end=\"true\"] {\n        &[data-range-selection=\"true\"] {\n          background-color: var(--primary);\n        }\n      }\n    }\n  }\n  .data-\\[selected\\=true\\]\\:data-\\[selection-end\\=true\\]\\:data-\\[range-selection\\=true\\]\\:bg-secondary {\n    &[data-selected=\"true\"] {\n      &[data-selection-end=\"true\"] {\n        &[data-range-selection=\"true\"] {\n          background-color: var(--secondary);\n        }\n      }\n    }\n  }\n  .data-\\[selected\\=true\\]\\:data-\\[selection-end\\=true\\]\\:data-\\[range-selection\\=true\\]\\:bg-success {\n    &[data-selected=\"true\"] {\n      &[data-selection-end=\"true\"] {\n        &[data-range-selection=\"true\"] {\n          background-color: hsl(var(--heroui-success) / 1);\n        }\n      }\n    }\n  }\n  .data-\\[selected\\=true\\]\\:data-\\[selection-end\\=true\\]\\:data-\\[range-selection\\=true\\]\\:bg-warning {\n    &[data-selected=\"true\"] {\n      &[data-selection-end=\"true\"] {\n        &[data-range-selection=\"true\"] {\n          background-color: hsl(var(--heroui-warning) / 1);\n        }\n      }\n    }\n  }\n  .data-\\[selected\\=true\\]\\:data-\\[selection-end\\=true\\]\\:data-\\[range-selection\\=true\\]\\:text-background {\n    &[data-selected=\"true\"] {\n      &[data-selection-end=\"true\"] {\n        &[data-range-selection=\"true\"] {\n          color: var(--background);\n        }\n      }\n    }\n  }\n  .data-\\[selected\\=true\\]\\:data-\\[selection-end\\=true\\]\\:data-\\[range-selection\\=true\\]\\:text-danger-foreground {\n    &[data-selected=\"true\"] {\n      &[data-selection-end=\"true\"] {\n        &[data-range-selection=\"true\"] {\n          color: hsl(var(--heroui-danger-foreground) / 1);\n        }\n      }\n    }\n  }\n  .data-\\[selected\\=true\\]\\:data-\\[selection-end\\=true\\]\\:data-\\[range-selection\\=true\\]\\:text-primary-foreground {\n    &[data-selected=\"true\"] {\n      &[data-selection-end=\"true\"] {\n        &[data-range-selection=\"true\"] {\n          color: var(--primary-foreground);\n        }\n      }\n    }\n  }\n  .data-\\[selected\\=true\\]\\:data-\\[selection-end\\=true\\]\\:data-\\[range-selection\\=true\\]\\:text-secondary-foreground {\n    &[data-selected=\"true\"] {\n      &[data-selection-end=\"true\"] {\n        &[data-range-selection=\"true\"] {\n          color: var(--secondary-foreground);\n        }\n      }\n    }\n  }\n  .data-\\[selected\\=true\\]\\:data-\\[selection-end\\=true\\]\\:data-\\[range-selection\\=true\\]\\:text-success-foreground {\n    &[data-selected=\"true\"] {\n      &[data-selection-end=\"true\"] {\n        &[data-range-selection=\"true\"] {\n          color: hsl(var(--heroui-success-foreground) / 1);\n        }\n      }\n    }\n  }\n  .data-\\[selected\\=true\\]\\:data-\\[selection-end\\=true\\]\\:data-\\[range-selection\\=true\\]\\:text-warning-foreground {\n    &[data-selected=\"true\"] {\n      &[data-selection-end=\"true\"] {\n        &[data-range-selection=\"true\"] {\n          color: hsl(var(--heroui-warning-foreground) / 1);\n        }\n      }\n    }\n  }\n  .data-\\[selection-start\\=true\\]\\:before\\:rounded-s-full {\n    &[data-selection-start=\"true\"] {\n      &::before {\n        content: var(--tw-content);\n        border-start-start-radius: calc(infinity * 1px);\n        border-end-start-radius: calc(infinity * 1px);\n      }\n    }\n  }\n  .data-\\[selected\\=true\\]\\:data-\\[selection-start\\=true\\]\\:shadow-md {\n    &[data-selected=\"true\"] {\n      &[data-selection-start=\"true\"] {\n        --tw-shadow: 0 4px 6px -1px var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 2px 4px -2px var(--tw-shadow-color, rgb(0 0 0 / 0.1));\n        box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\n      }\n    }\n  }\n  .data-\\[selected\\=true\\]\\:data-\\[selection-start\\=true\\]\\:data-\\[range-selection\\=true\\]\\:rounded-full {\n    &[data-selected=\"true\"] {\n      &[data-selection-start=\"true\"] {\n        &[data-range-selection=\"true\"] {\n          border-radius: calc(infinity * 1px);\n        }\n      }\n    }\n  }\n  .data-\\[selected\\=true\\]\\:data-\\[selection-start\\=true\\]\\:data-\\[range-selection\\=true\\]\\:bg-danger {\n    &[data-selected=\"true\"] {\n      &[data-selection-start=\"true\"] {\n        &[data-range-selection=\"true\"] {\n          background-color: hsl(var(--heroui-danger) / 1);\n        }\n      }\n    }\n  }\n  .data-\\[selected\\=true\\]\\:data-\\[selection-start\\=true\\]\\:data-\\[range-selection\\=true\\]\\:bg-foreground {\n    &[data-selected=\"true\"] {\n      &[data-selection-start=\"true\"] {\n        &[data-range-selection=\"true\"] {\n          background-color: var(--foreground);\n        }\n      }\n    }\n  }\n  .data-\\[selected\\=true\\]\\:data-\\[selection-start\\=true\\]\\:data-\\[range-selection\\=true\\]\\:bg-primary {\n    &[data-selected=\"true\"] {\n      &[data-selection-start=\"true\"] {\n        &[data-range-selection=\"true\"] {\n          background-color: var(--primary);\n        }\n      }\n    }\n  }\n  .data-\\[selected\\=true\\]\\:data-\\[selection-start\\=true\\]\\:data-\\[range-selection\\=true\\]\\:bg-secondary {\n    &[data-selected=\"true\"] {\n      &[data-selection-start=\"true\"] {\n        &[data-range-selection=\"true\"] {\n          background-color: var(--secondary);\n        }\n      }\n    }\n  }\n  .data-\\[selected\\=true\\]\\:data-\\[selection-start\\=true\\]\\:data-\\[range-selection\\=true\\]\\:bg-success {\n    &[data-selected=\"true\"] {\n      &[data-selection-start=\"true\"] {\n        &[data-range-selection=\"true\"] {\n          background-color: hsl(var(--heroui-success) / 1);\n        }\n      }\n    }\n  }\n  .data-\\[selected\\=true\\]\\:data-\\[selection-start\\=true\\]\\:data-\\[range-selection\\=true\\]\\:bg-warning {\n    &[data-selected=\"true\"] {\n      &[data-selection-start=\"true\"] {\n        &[data-range-selection=\"true\"] {\n          background-color: hsl(var(--heroui-warning) / 1);\n        }\n      }\n    }\n  }\n  .data-\\[selected\\=true\\]\\:data-\\[selection-start\\=true\\]\\:data-\\[range-selection\\=true\\]\\:text-background {\n    &[data-selected=\"true\"] {\n      &[data-selection-start=\"true\"] {\n        &[data-range-selection=\"true\"] {\n          color: var(--background);\n        }\n      }\n    }\n  }\n  .data-\\[selected\\=true\\]\\:data-\\[selection-start\\=true\\]\\:data-\\[range-selection\\=true\\]\\:text-danger-foreground {\n    &[data-selected=\"true\"] {\n      &[data-selection-start=\"true\"] {\n        &[data-range-selection=\"true\"] {\n          color: hsl(var(--heroui-danger-foreground) / 1);\n        }\n      }\n    }\n  }\n  .data-\\[selected\\=true\\]\\:data-\\[selection-start\\=true\\]\\:data-\\[range-selection\\=true\\]\\:text-primary-foreground {\n    &[data-selected=\"true\"] {\n      &[data-selection-start=\"true\"] {\n        &[data-range-selection=\"true\"] {\n          color: var(--primary-foreground);\n        }\n      }\n    }\n  }\n  .data-\\[selected\\=true\\]\\:data-\\[selection-start\\=true\\]\\:data-\\[range-selection\\=true\\]\\:text-secondary-foreground {\n    &[data-selected=\"true\"] {\n      &[data-selection-start=\"true\"] {\n        &[data-range-selection=\"true\"] {\n          color: var(--secondary-foreground);\n        }\n      }\n    }\n  }\n  .data-\\[selected\\=true\\]\\:data-\\[selection-start\\=true\\]\\:data-\\[range-selection\\=true\\]\\:text-success-foreground {\n    &[data-selected=\"true\"] {\n      &[data-selection-start=\"true\"] {\n        &[data-range-selection=\"true\"] {\n          color: hsl(var(--heroui-success-foreground) / 1);\n        }\n      }\n    }\n  }\n  .data-\\[selected\\=true\\]\\:data-\\[selection-start\\=true\\]\\:data-\\[range-selection\\=true\\]\\:text-warning-foreground {\n    &[data-selected=\"true\"] {\n      &[data-selection-start=\"true\"] {\n        &[data-range-selection=\"true\"] {\n          color: hsl(var(--heroui-warning-foreground) / 1);\n        }\n      }\n    }\n  }\n  .data-\\[side\\=bottom\\]\\:translate-y-1 {\n    &[data-side=\"bottom\"] {\n      --tw-translate-y: calc(var(--spacing) * 1);\n      translate: var(--tw-translate-x) var(--tw-translate-y);\n    }\n  }\n  .data-\\[side\\=bottom\\]\\:slide-in-from-top-2 {\n    &[data-side=\"bottom\"] {\n      --tw-enter-translate-y: calc(2*var(--spacing)*-1);\n    }\n  }\n  .data-\\[side\\=left\\]\\:-translate-x-1 {\n    &[data-side=\"left\"] {\n      --tw-translate-x: calc(var(--spacing) * -1);\n      translate: var(--tw-translate-x) var(--tw-translate-y);\n    }\n  }\n  .data-\\[side\\=left\\]\\:slide-in-from-right-2 {\n    &[data-side=\"left\"] {\n      --tw-enter-translate-x: calc(2*var(--spacing));\n    }\n  }\n  .data-\\[side\\=right\\]\\:translate-x-1 {\n    &[data-side=\"right\"] {\n      --tw-translate-x: calc(var(--spacing) * 1);\n      translate: var(--tw-translate-x) var(--tw-translate-y);\n    }\n  }\n  .data-\\[side\\=right\\]\\:slide-in-from-left-2 {\n    &[data-side=\"right\"] {\n      --tw-enter-translate-x: calc(2*var(--spacing)*-1);\n    }\n  }\n  .data-\\[side\\=top\\]\\:-translate-y-1 {\n    &[data-side=\"top\"] {\n      --tw-translate-y: calc(var(--spacing) * -1);\n      translate: var(--tw-translate-x) var(--tw-translate-y);\n    }\n  }\n  .data-\\[side\\=top\\]\\:slide-in-from-bottom-2 {\n    &[data-side=\"top\"] {\n      --tw-enter-translate-y: calc(2*var(--spacing));\n    }\n  }\n  .data-\\[size\\=default\\]\\:h-9 {\n    &[data-size=\"default\"] {\n      height: calc(var(--spacing) * 9);\n    }\n  }\n  .data-\\[size\\=sm\\]\\:h-8 {\n    &[data-size=\"sm\"] {\n      height: calc(var(--spacing) * 8);\n    }\n  }\n  .\\*\\:data-\\[slot\\=alert-description\\]\\:text-destructive\\/90 {\n    :is(& > *) {\n      &[data-slot=\"alert-description\"] {\n        color: var(--destructive);\n        @supports (color: color-mix(in lab, red, red)) {\n          color: color-mix(in oklab, var(--destructive) 90%, transparent);\n        }\n      }\n    }\n  }\n  .\\*\\*\\:data-\\[slot\\=badge\\]\\:size-5 {\n    :is(& *) {\n      &[data-slot=\"badge\"] {\n        width: calc(var(--spacing) * 5);\n        height: calc(var(--spacing) * 5);\n      }\n    }\n  }\n  .\\*\\*\\:data-\\[slot\\=badge\\]\\:rounded-full {\n    :is(& *) {\n      &[data-slot=\"badge\"] {\n        border-radius: calc(infinity * 1px);\n      }\n    }\n  }\n  .\\*\\*\\:data-\\[slot\\=badge\\]\\:bg-muted-foreground\\/30 {\n    :is(& *) {\n      &[data-slot=\"badge\"] {\n        background-color: var(--muted-foreground);\n        @supports (color: color-mix(in lab, red, red)) {\n          background-color: color-mix(in oklab, var(--muted-foreground) 30%, transparent);\n        }\n      }\n    }\n  }\n  .\\*\\*\\:data-\\[slot\\=badge\\]\\:px-1 {\n    :is(& *) {\n      &[data-slot=\"badge\"] {\n        padding-inline: calc(var(--spacing) * 1);\n      }\n    }\n  }\n  .\\*\\:data-\\[slot\\=card\\]\\:bg-gradient-to-t {\n    :is(& > *) {\n      &[data-slot=\"card\"] {\n        --tw-gradient-position: to top in oklab;\n        background-image: linear-gradient(var(--tw-gradient-stops));\n      }\n    }\n  }\n  .\\*\\:data-\\[slot\\=card\\]\\:from-primary\\/5 {\n    :is(& > *) {\n      &[data-slot=\"card\"] {\n        --tw-gradient-from: var(--primary);\n        @supports (color: color-mix(in lab, red, red)) {\n          --tw-gradient-from: color-mix(in oklab, var(--primary) 5%, transparent);\n        }\n        --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n      }\n    }\n  }\n  .\\*\\:data-\\[slot\\=card\\]\\:to-card {\n    :is(& > *) {\n      &[data-slot=\"card\"] {\n        --tw-gradient-to: var(--card);\n        --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n      }\n    }\n  }\n  .\\*\\:data-\\[slot\\=card\\]\\:shadow-xs {\n    :is(& > *) {\n      &[data-slot=\"card\"] {\n        --tw-shadow: 0 1px 2px 0 var(--tw-shadow-color, rgb(0 0 0 / 0.05));\n        box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\n      }\n    }\n  }\n  .\\*\\*\\:data-\\[slot\\=command-input-wrapper\\]\\:h-12 {\n    :is(& *) {\n      &[data-slot=\"command-input-wrapper\"] {\n        height: calc(var(--spacing) * 12);\n      }\n    }\n  }\n  .\\*\\*\\:data-\\[slot\\=navigation-menu-link\\]\\:focus\\:ring-0 {\n    :is(& *) {\n      &[data-slot=\"navigation-menu-link\"] {\n        &:focus {\n          --tw-ring-shadow: var(--tw-ring-inset,) 0 0 0 calc(0px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);\n          box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\n        }\n      }\n    }\n  }\n  .\\*\\*\\:data-\\[slot\\=navigation-menu-link\\]\\:focus\\:outline-none {\n    :is(& *) {\n      &[data-slot=\"navigation-menu-link\"] {\n        &:focus {\n          --tw-outline-style: none;\n          outline-style: none;\n        }\n      }\n    }\n  }\n  .\\*\\:data-\\[slot\\=select-value\\]\\:line-clamp-1 {\n    :is(& > *) {\n      &[data-slot=\"select-value\"] {\n        overflow: hidden;\n        display: -webkit-box;\n        -webkit-box-orient: vertical;\n        -webkit-line-clamp: 1;\n      }\n    }\n  }\n  .\\*\\:data-\\[slot\\=select-value\\]\\:flex {\n    :is(& > *) {\n      &[data-slot=\"select-value\"] {\n        display: flex;\n      }\n    }\n  }\n  .\\*\\:data-\\[slot\\=select-value\\]\\:items-center {\n    :is(& > *) {\n      &[data-slot=\"select-value\"] {\n        align-items: center;\n      }\n    }\n  }\n  .\\*\\:data-\\[slot\\=select-value\\]\\:gap-2 {\n    :is(& > *) {\n      &[data-slot=\"select-value\"] {\n        gap: calc(var(--spacing) * 2);\n      }\n    }\n  }\n  .\\*\\*\\:data-\\[slot\\=select-value\\]\\:block {\n    :is(& *) {\n      &[data-slot=\"select-value\"] {\n        display: block;\n      }\n    }\n  }\n  .\\*\\*\\:data-\\[slot\\=select-value\\]\\:truncate {\n    :is(& *) {\n      &[data-slot=\"select-value\"] {\n        overflow: hidden;\n        text-overflow: ellipsis;\n        white-space: nowrap;\n      }\n    }\n  }\n  .data-\\[slot\\=sidebar-menu-button\\]\\:\\!p-1\\.5 {\n    &[data-slot=\"sidebar-menu-button\"] {\n      padding: calc(var(--spacing) * 1.5) !important;\n    }\n  }\n  .\\*\\*\\:data-\\[slot\\=table-cell\\]\\:first\\:w-8 {\n    :is(& *) {\n      &[data-slot=\"table-cell\"] {\n        &:first-child {\n          width: calc(var(--spacing) * 8);\n        }\n      }\n    }\n  }\n  .\\*\\:data-\\[slot\\=toggle-group-item\\]\\:\\!px-4 {\n    :is(& > *) {\n      &[data-slot=\"toggle-group-item\"] {\n        padding-inline: calc(var(--spacing) * 4) !important;\n      }\n    }\n  }\n  .data-\\[sortable\\=true\\]\\:cursor-pointer {\n    &[data-sortable=\"true\"] {\n      cursor: pointer;\n    }\n  }\n  .data-\\[state\\=active\\]\\:bg-background {\n    &[data-state=\"active\"] {\n      background-color: var(--background);\n    }\n  }\n  .data-\\[state\\=active\\]\\:shadow-sm {\n    &[data-state=\"active\"] {\n      --tw-shadow: 0 1px 3px 0 var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 1px 2px -1px var(--tw-shadow-color, rgb(0 0 0 / 0.1));\n      box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\n    }\n  }\n  .data-\\[state\\=checked\\]\\:translate-x-\\[calc\\(100\\%-2px\\)\\] {\n    &[data-state=\"checked\"] {\n      --tw-translate-x: calc(100% - 2px);\n      translate: var(--tw-translate-x) var(--tw-translate-y);\n    }\n  }\n  .data-\\[state\\=checked\\]\\:border-primary {\n    &[data-state=\"checked\"] {\n      border-color: var(--primary);\n    }\n  }\n  .data-\\[state\\=checked\\]\\:bg-primary {\n    &[data-state=\"checked\"] {\n      background-color: var(--primary);\n    }\n  }\n  .data-\\[state\\=checked\\]\\:text-primary-foreground {\n    &[data-state=\"checked\"] {\n      color: var(--primary-foreground);\n    }\n  }\n  .data-\\[state\\=closed\\]\\:animate-accordion-up {\n    &[data-state=\"closed\"] {\n      animation: accordion-up 0.2s ease-out;\n    }\n  }\n  .data-\\[state\\=closed\\]\\:animate-out {\n    &[data-state=\"closed\"] {\n      animation: exit var(--tw-duration,.15s)var(--tw-ease,ease);\n    }\n  }\n  .data-\\[state\\=closed\\]\\:duration-300 {\n    &[data-state=\"closed\"] {\n      --tw-duration: 300ms;\n      transition-duration: 300ms;\n    }\n  }\n  .data-\\[state\\=closed\\]\\:fade-out-0 {\n    &[data-state=\"closed\"] {\n      --tw-exit-opacity: 0;\n    }\n  }\n  .data-\\[state\\=closed\\]\\:slide-out-to-bottom {\n    &[data-state=\"closed\"] {\n      --tw-exit-translate-y: 100%;\n    }\n  }\n  .data-\\[state\\=closed\\]\\:slide-out-to-left {\n    &[data-state=\"closed\"] {\n      --tw-exit-translate-x: -100%;\n    }\n  }\n  .data-\\[state\\=closed\\]\\:slide-out-to-right {\n    &[data-state=\"closed\"] {\n      --tw-exit-translate-x: 100%;\n    }\n  }\n  .data-\\[state\\=closed\\]\\:slide-out-to-top {\n    &[data-state=\"closed\"] {\n      --tw-exit-translate-y: -100%;\n    }\n  }\n  .data-\\[state\\=closed\\]\\:zoom-out-95 {\n    &[data-state=\"closed\"] {\n      --tw-exit-scale: .95;\n    }\n  }\n  .group-data-\\[viewport\\=false\\]\\/navigation-menu\\:data-\\[state\\=closed\\]\\:animate-out {\n    &:is(:where(.group\\/navigation-menu)[data-viewport=\"false\"] *) {\n      &[data-state=\"closed\"] {\n        animation: exit var(--tw-duration,.15s)var(--tw-ease,ease);\n      }\n    }\n  }\n  .group-data-\\[viewport\\=false\\]\\/navigation-menu\\:data-\\[state\\=closed\\]\\:fade-out-0 {\n    &:is(:where(.group\\/navigation-menu)[data-viewport=\"false\"] *) {\n      &[data-state=\"closed\"] {\n        --tw-exit-opacity: 0;\n      }\n    }\n  }\n  .group-data-\\[viewport\\=false\\]\\/navigation-menu\\:data-\\[state\\=closed\\]\\:zoom-out-95 {\n    &:is(:where(.group\\/navigation-menu)[data-viewport=\"false\"] *) {\n      &[data-state=\"closed\"] {\n        --tw-exit-scale: .95;\n      }\n    }\n  }\n  .data-\\[state\\=completed\\]\\:bg-primary {\n    &[data-state=\"completed\"] {\n      background-color: var(--primary);\n    }\n  }\n  .data-\\[state\\=hidden\\]\\:animate-out {\n    &[data-state=\"hidden\"] {\n      animation: exit var(--tw-duration,.15s)var(--tw-ease,ease);\n    }\n  }\n  .data-\\[state\\=hidden\\]\\:fade-out {\n    &[data-state=\"hidden\"] {\n      --tw-exit-opacity: 0;\n    }\n  }\n  .data-\\[state\\=on\\]\\:bg-accent {\n    &[data-state=\"on\"] {\n      background-color: var(--accent);\n    }\n  }\n  .data-\\[state\\=on\\]\\:text-accent-foreground {\n    &[data-state=\"on\"] {\n      color: var(--accent-foreground);\n    }\n  }\n  .data-\\[state\\=open\\]\\:animate-accordion-down {\n    &[data-state=\"open\"] {\n      animation: accordion-down 0.2s ease-out;\n    }\n  }\n  .data-\\[state\\=open\\]\\:animate-in {\n    &[data-state=\"open\"] {\n      animation: enter var(--tw-duration,.15s)var(--tw-ease,ease);\n    }\n  }\n  .data-\\[state\\=open\\]\\:bg-accent {\n    &[data-state=\"open\"] {\n      background-color: var(--accent);\n    }\n  }\n  .data-\\[state\\=open\\]\\:bg-accent\\/50 {\n    &[data-state=\"open\"] {\n      background-color: var(--accent);\n      @supports (color: color-mix(in lab, red, red)) {\n        background-color: color-mix(in oklab, var(--accent) 50%, transparent);\n      }\n    }\n  }\n  .data-\\[state\\=open\\]\\:bg-muted {\n    &[data-state=\"open\"] {\n      background-color: var(--muted);\n    }\n  }\n  .data-\\[state\\=open\\]\\:bg-secondary {\n    &[data-state=\"open\"] {\n      background-color: var(--secondary);\n    }\n  }\n  .data-\\[state\\=open\\]\\:bg-sidebar-accent {\n    &[data-state=\"open\"] {\n      background-color: var(--sidebar-accent);\n    }\n  }\n  .data-\\[state\\=open\\]\\:text-accent-foreground {\n    &[data-state=\"open\"] {\n      color: var(--accent-foreground);\n    }\n  }\n  .data-\\[state\\=open\\]\\:text-muted-foreground {\n    &[data-state=\"open\"] {\n      color: var(--muted-foreground);\n    }\n  }\n  .data-\\[state\\=open\\]\\:text-sidebar-accent-foreground {\n    &[data-state=\"open\"] {\n      color: var(--sidebar-accent-foreground);\n    }\n  }\n  .data-\\[state\\=open\\]\\:opacity-100 {\n    &[data-state=\"open\"] {\n      opacity: 100%;\n    }\n  }\n  .data-\\[state\\=open\\]\\:duration-500 {\n    &[data-state=\"open\"] {\n      --tw-duration: 500ms;\n      transition-duration: 500ms;\n    }\n  }\n  .data-\\[state\\=open\\]\\:fade-in-0 {\n    &[data-state=\"open\"] {\n      --tw-enter-opacity: 0;\n    }\n  }\n  .data-\\[state\\=open\\]\\:slide-in-from-bottom {\n    &[data-state=\"open\"] {\n      --tw-enter-translate-y: 100%;\n    }\n  }\n  .data-\\[state\\=open\\]\\:slide-in-from-left {\n    &[data-state=\"open\"] {\n      --tw-enter-translate-x: -100%;\n    }\n  }\n  .data-\\[state\\=open\\]\\:slide-in-from-right {\n    &[data-state=\"open\"] {\n      --tw-enter-translate-x: 100%;\n    }\n  }\n  .data-\\[state\\=open\\]\\:slide-in-from-top {\n    &[data-state=\"open\"] {\n      --tw-enter-translate-y: -100%;\n    }\n  }\n  .data-\\[state\\=open\\]\\:zoom-in-90 {\n    &[data-state=\"open\"] {\n      --tw-enter-scale: .9;\n    }\n  }\n  .data-\\[state\\=open\\]\\:zoom-in-95 {\n    &[data-state=\"open\"] {\n      --tw-enter-scale: .95;\n    }\n  }\n  .group-data-\\[viewport\\=false\\]\\/navigation-menu\\:data-\\[state\\=open\\]\\:animate-in {\n    &:is(:where(.group\\/navigation-menu)[data-viewport=\"false\"] *) {\n      &[data-state=\"open\"] {\n        animation: enter var(--tw-duration,.15s)var(--tw-ease,ease);\n      }\n    }\n  }\n  .group-data-\\[viewport\\=false\\]\\/navigation-menu\\:data-\\[state\\=open\\]\\:fade-in-0 {\n    &:is(:where(.group\\/navigation-menu)[data-viewport=\"false\"] *) {\n      &[data-state=\"open\"] {\n        --tw-enter-opacity: 0;\n      }\n    }\n  }\n  .group-data-\\[viewport\\=false\\]\\/navigation-menu\\:data-\\[state\\=open\\]\\:zoom-in-95 {\n    &:is(:where(.group\\/navigation-menu)[data-viewport=\"false\"] *) {\n      &[data-state=\"open\"] {\n        --tw-enter-scale: .95;\n      }\n    }\n  }\n  .data-\\[state\\=open\\]\\:hover\\:bg-accent {\n    &[data-state=\"open\"] {\n      &:hover {\n        @media (hover: hover) {\n          background-color: var(--accent);\n        }\n      }\n    }\n  }\n  .data-\\[state\\=open\\]\\:hover\\:bg-sidebar-accent {\n    &[data-state=\"open\"] {\n      &:hover {\n        @media (hover: hover) {\n          background-color: var(--sidebar-accent);\n        }\n      }\n    }\n  }\n  .data-\\[state\\=open\\]\\:hover\\:text-sidebar-accent-foreground {\n    &[data-state=\"open\"] {\n      &:hover {\n        @media (hover: hover) {\n          color: var(--sidebar-accent-foreground);\n        }\n      }\n    }\n  }\n  .data-\\[state\\=open\\]\\:focus\\:bg-accent {\n    &[data-state=\"open\"] {\n      &:focus {\n        background-color: var(--accent);\n      }\n    }\n  }\n  .data-\\[state\\=selected\\]\\:bg-muted {\n    &[data-state=\"selected\"] {\n      background-color: var(--muted);\n    }\n  }\n  .data-\\[state\\=unchecked\\]\\:translate-x-0 {\n    &[data-state=\"unchecked\"] {\n      --tw-translate-x: calc(var(--spacing) * 0);\n      translate: var(--tw-translate-x) var(--tw-translate-y);\n    }\n  }\n  .data-\\[state\\=unchecked\\]\\:bg-input {\n    &[data-state=\"unchecked\"] {\n      background-color: var(--input);\n    }\n  }\n  .data-\\[state\\=visible\\]\\:animate-in {\n    &[data-state=\"visible\"] {\n      animation: enter var(--tw-duration,.15s)var(--tw-ease,ease);\n    }\n  }\n  .data-\\[state\\=visible\\]\\:fade-in {\n    &[data-state=\"visible\"] {\n      --tw-enter-opacity: 0;\n    }\n  }\n  .data-\\[toast-exiting\\=true\\]\\:transform-gpu {\n    &[data-toast-exiting=\"true\"] {\n      transform: translateZ(0) var(--tw-rotate-x) var(--tw-rotate-y) var(--tw-rotate-z) var(--tw-skew-x) var(--tw-skew-y);\n    }\n  }\n  .data-\\[toast-exiting\\=true\\]\\:opacity-0 {\n    &[data-toast-exiting=\"true\"] {\n      opacity: 0%;\n    }\n  }\n  .data-\\[toast-exiting\\=true\\]\\:transition-all {\n    &[data-toast-exiting=\"true\"] {\n      transition-property: all;\n      transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));\n      transition-duration: var(--tw-duration, var(--default-transition-duration));\n    }\n  }\n  .data-\\[toast-exiting\\=true\\]\\:duration-300 {\n    &[data-toast-exiting=\"true\"] {\n      --tw-duration: 300ms;\n      transition-duration: 300ms;\n    }\n  }\n  .data-\\[toast-exiting\\=true\\]\\:ease-out {\n    &[data-toast-exiting=\"true\"] {\n      --tw-ease: var(--ease-out);\n      transition-timing-function: var(--ease-out);\n    }\n  }\n  .data-\\[toast-exiting\\=true\\]\\:will-change-transform {\n    &[data-toast-exiting=\"true\"] {\n      will-change: transform;\n    }\n  }\n  .data-\\[toast-exiting\\=true\\]\\:data-\\[placement\\=bottom-center\\]\\:translate-y-full {\n    &[data-toast-exiting=\"true\"] {\n      &[data-placement=\"bottom-center\"] {\n        --tw-translate-y: 100%;\n        translate: var(--tw-translate-x) var(--tw-translate-y);\n      }\n    }\n  }\n  .data-\\[toast-exiting\\=true\\]\\:data-\\[placement\\=bottom-left\\]\\:-translate-x-full {\n    &[data-toast-exiting=\"true\"] {\n      &[data-placement=\"bottom-left\"] {\n        --tw-translate-x: -100%;\n        translate: var(--tw-translate-x) var(--tw-translate-y);\n      }\n    }\n  }\n  .data-\\[toast-exiting\\=true\\]\\:data-\\[placement\\=bottom-right\\]\\:translate-x-full {\n    &[data-toast-exiting=\"true\"] {\n      &[data-placement=\"bottom-right\"] {\n        --tw-translate-x: 100%;\n        translate: var(--tw-translate-x) var(--tw-translate-y);\n      }\n    }\n  }\n  .data-\\[toast-exiting\\=true\\]\\:data-\\[placement\\=top-center\\]\\:-translate-y-full {\n    &[data-toast-exiting=\"true\"] {\n      &[data-placement=\"top-center\"] {\n        --tw-translate-y: -100%;\n        translate: var(--tw-translate-x) var(--tw-translate-y);\n      }\n    }\n  }\n  .data-\\[toast-exiting\\=true\\]\\:data-\\[placement\\=top-left\\]\\:-translate-x-full {\n    &[data-toast-exiting=\"true\"] {\n      &[data-placement=\"top-left\"] {\n        --tw-translate-x: -100%;\n        translate: var(--tw-translate-x) var(--tw-translate-y);\n      }\n    }\n  }\n  .data-\\[toast-exiting\\=true\\]\\:data-\\[placement\\=top-right\\]\\:translate-x-full {\n    &[data-toast-exiting=\"true\"] {\n      &[data-placement=\"top-right\"] {\n        --tw-translate-x: 100%;\n        translate: var(--tw-translate-x) var(--tw-translate-y);\n      }\n    }\n  }\n  .data-\\[top-bottom-scroll\\=true\\]\\:\\[mask-image\\:linear-gradient\\(\\#000\\,\\#000\\,transparent_0\\,\\#000_var\\(--scroll-shadow-size\\)\\,\\#000_calc\\(100\\%_-_var\\(--scroll-shadow-size\\)\\)\\,transparent\\)\\] {\n    &[data-top-bottom-scroll=\"true\"] {\n      mask-image: linear-gradient(#000,#000,transparent 0,#000 var(--scroll-shadow-size),#000 calc(100% - var(--scroll-shadow-size)),transparent);\n    }\n  }\n  .data-\\[top-scroll\\=true\\]\\:\\[mask-image\\:linear-gradient\\(0deg\\,\\#000_calc\\(100\\%_-_var\\(--scroll-shadow-size\\)\\)\\,transparent\\)\\] {\n    &[data-top-scroll=\"true\"] {\n      mask-image: linear-gradient(0deg,#000 calc(100% - var(--scroll-shadow-size)),transparent);\n    }\n  }\n  .data-\\[type\\=color\\]\\:rounded-none {\n    &[data-type=\"color\"] {\n      border-radius: 0;\n    }\n  }\n  .data-\\[unavailable\\=true\\]\\:cursor-default {\n    &[data-unavailable=\"true\"] {\n      cursor: default;\n    }\n  }\n  .data-\\[unavailable\\=true\\]\\:text-default-300 {\n    &[data-unavailable=\"true\"] {\n      color: hsl(var(--heroui-default-300) / 1);\n    }\n  }\n  .data-\\[unavailable\\=true\\]\\:line-through {\n    &[data-unavailable=\"true\"] {\n      text-decoration-line: line-through;\n    }\n  }\n  .data-\\[variant\\=destructive\\]\\:text-destructive {\n    &[data-variant=\"destructive\"] {\n      color: var(--destructive);\n    }\n  }\n  .data-\\[variant\\=destructive\\]\\:focus\\:bg-destructive\\/10 {\n    &[data-variant=\"destructive\"] {\n      &:focus {\n        background-color: var(--destructive);\n        @supports (color: color-mix(in lab, red, red)) {\n          background-color: color-mix(in oklab, var(--destructive) 10%, transparent);\n        }\n      }\n    }\n  }\n  .data-\\[variant\\=destructive\\]\\:focus\\:text-destructive {\n    &[data-variant=\"destructive\"] {\n      &:focus {\n        color: var(--destructive);\n      }\n    }\n  }\n  .data-\\[variant\\=outline\\]\\:border-l-0 {\n    &[data-variant=\"outline\"] {\n      border-left-style: var(--tw-border-style);\n      border-left-width: 0px;\n    }\n  }\n  .data-\\[variant\\=outline\\]\\:shadow-xs {\n    &[data-variant=\"outline\"] {\n      --tw-shadow: 0 1px 2px 0 var(--tw-shadow-color, rgb(0 0 0 / 0.05));\n      box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\n    }\n  }\n  .data-\\[variant\\=outline\\]\\:first\\:border-l {\n    &[data-variant=\"outline\"] {\n      &:first-child {\n        border-left-style: var(--tw-border-style);\n        border-left-width: 1px;\n      }\n    }\n  }\n  .data-\\[variant\\=vertical\\]\\:flex-row {\n    &[data-variant=\"vertical\"] {\n      flex-direction: row;\n    }\n  }\n  .data-\\[vaul-drawer-direction\\=bottom\\]\\:inset-x-0 {\n    &[data-vaul-drawer-direction=\"bottom\"] {\n      inset-inline: calc(var(--spacing) * 0);\n    }\n  }\n  .data-\\[vaul-drawer-direction\\=bottom\\]\\:bottom-0 {\n    &[data-vaul-drawer-direction=\"bottom\"] {\n      bottom: calc(var(--spacing) * 0);\n    }\n  }\n  .data-\\[vaul-drawer-direction\\=bottom\\]\\:mt-24 {\n    &[data-vaul-drawer-direction=\"bottom\"] {\n      margin-top: calc(var(--spacing) * 24);\n    }\n  }\n  .data-\\[vaul-drawer-direction\\=bottom\\]\\:max-h-\\[80vh\\] {\n    &[data-vaul-drawer-direction=\"bottom\"] {\n      max-height: 80vh;\n    }\n  }\n  .data-\\[vaul-drawer-direction\\=bottom\\]\\:rounded-t-lg {\n    &[data-vaul-drawer-direction=\"bottom\"] {\n      border-top-left-radius: var(--radius);\n      border-top-right-radius: var(--radius);\n    }\n  }\n  .data-\\[vaul-drawer-direction\\=bottom\\]\\:border-t {\n    &[data-vaul-drawer-direction=\"bottom\"] {\n      border-top-style: var(--tw-border-style);\n      border-top-width: 1px;\n    }\n  }\n  .data-\\[vaul-drawer-direction\\=left\\]\\:inset-y-0 {\n    &[data-vaul-drawer-direction=\"left\"] {\n      inset-block: calc(var(--spacing) * 0);\n    }\n  }\n  .data-\\[vaul-drawer-direction\\=left\\]\\:left-0 {\n    &[data-vaul-drawer-direction=\"left\"] {\n      left: calc(var(--spacing) * 0);\n    }\n  }\n  .data-\\[vaul-drawer-direction\\=left\\]\\:w-3\\/4 {\n    &[data-vaul-drawer-direction=\"left\"] {\n      width: calc(3/4 * 100%);\n    }\n  }\n  .data-\\[vaul-drawer-direction\\=left\\]\\:border-r {\n    &[data-vaul-drawer-direction=\"left\"] {\n      border-right-style: var(--tw-border-style);\n      border-right-width: 1px;\n    }\n  }\n  .data-\\[vaul-drawer-direction\\=right\\]\\:inset-y-0 {\n    &[data-vaul-drawer-direction=\"right\"] {\n      inset-block: calc(var(--spacing) * 0);\n    }\n  }\n  .data-\\[vaul-drawer-direction\\=right\\]\\:right-0 {\n    &[data-vaul-drawer-direction=\"right\"] {\n      right: calc(var(--spacing) * 0);\n    }\n  }\n  .data-\\[vaul-drawer-direction\\=right\\]\\:w-3\\/4 {\n    &[data-vaul-drawer-direction=\"right\"] {\n      width: calc(3/4 * 100%);\n    }\n  }\n  .data-\\[vaul-drawer-direction\\=right\\]\\:border-l {\n    &[data-vaul-drawer-direction=\"right\"] {\n      border-left-style: var(--tw-border-style);\n      border-left-width: 1px;\n    }\n  }\n  .data-\\[vaul-drawer-direction\\=top\\]\\:inset-x-0 {\n    &[data-vaul-drawer-direction=\"top\"] {\n      inset-inline: calc(var(--spacing) * 0);\n    }\n  }\n  .data-\\[vaul-drawer-direction\\=top\\]\\:top-0 {\n    &[data-vaul-drawer-direction=\"top\"] {\n      top: calc(var(--spacing) * 0);\n    }\n  }\n  .data-\\[vaul-drawer-direction\\=top\\]\\:mb-24 {\n    &[data-vaul-drawer-direction=\"top\"] {\n      margin-bottom: calc(var(--spacing) * 24);\n    }\n  }\n  .data-\\[vaul-drawer-direction\\=top\\]\\:max-h-\\[80vh\\] {\n    &[data-vaul-drawer-direction=\"top\"] {\n      max-height: 80vh;\n    }\n  }\n  .data-\\[vaul-drawer-direction\\=top\\]\\:rounded-b-lg {\n    &[data-vaul-drawer-direction=\"top\"] {\n      border-bottom-right-radius: var(--radius);\n      border-bottom-left-radius: var(--radius);\n    }\n  }\n  .data-\\[vaul-drawer-direction\\=top\\]\\:border-b {\n    &[data-vaul-drawer-direction=\"top\"] {\n      border-bottom-style: var(--tw-border-style);\n      border-bottom-width: 1px;\n    }\n  }\n  .data-\\[visible\\=true\\]\\:pointer-events-auto {\n    &[data-visible=\"true\"] {\n      pointer-events: auto;\n    }\n  }\n  .data-\\[visible\\=true\\]\\:cursor-pointer {\n    &[data-visible=\"true\"] {\n      cursor: pointer;\n    }\n  }\n  .data-\\[visible\\=true\\]\\:opacity-100 {\n    &[data-visible=\"true\"] {\n      opacity: 100%;\n    }\n  }\n  .motion-reduce\\:scale-100 {\n    @media (prefers-reduced-motion: reduce) {\n      --tw-scale-x: 100%;\n      --tw-scale-y: 100%;\n      --tw-scale-z: 100%;\n      scale: var(--tw-scale-x) var(--tw-scale-y);\n    }\n  }\n  .motion-reduce\\:transition-none {\n    @media (prefers-reduced-motion: reduce) {\n      transition-property: none;\n    }\n  }\n  .motion-reduce\\:after\\:transition-none {\n    @media (prefers-reduced-motion: reduce) {\n      &::after {\n        content: var(--tw-content);\n        transition-property: none;\n      }\n    }\n  }\n  .sm\\:m-0 {\n    @media (width >= 40rem) {\n      margin: calc(var(--spacing) * 0);\n    }\n  }\n  .sm\\:mx-0 {\n    @media (width >= 40rem) {\n      margin-inline: calc(var(--spacing) * 0);\n    }\n  }\n  .sm\\:mx-1 {\n    @media (width >= 40rem) {\n      margin-inline: calc(var(--spacing) * 1);\n    }\n  }\n  .sm\\:mx-6 {\n    @media (width >= 40rem) {\n      margin-inline: calc(var(--spacing) * 6);\n    }\n  }\n  .sm\\:my-0 {\n    @media (width >= 40rem) {\n      margin-block: calc(var(--spacing) * 0);\n    }\n  }\n  .sm\\:my-16 {\n    @media (width >= 40rem) {\n      margin-block: calc(var(--spacing) * 16);\n    }\n  }\n  .sm\\:block {\n    @media (width >= 40rem) {\n      display: block;\n    }\n  }\n  .sm\\:flex {\n    @media (width >= 40rem) {\n      display: flex;\n    }\n  }\n  .sm\\:inline {\n    @media (width >= 40rem) {\n      display: inline;\n    }\n  }\n  .sm\\:w-64 {\n    @media (width >= 40rem) {\n      width: calc(var(--spacing) * 64);\n    }\n  }\n  .sm\\:w-\\[350px\\] {\n    @media (width >= 40rem) {\n      width: 350px;\n    }\n  }\n  .sm\\:w-\\[356px\\] {\n    @media (width >= 40rem) {\n      width: 356px;\n    }\n  }\n  .sm\\:w-auto {\n    @media (width >= 40rem) {\n      width: auto;\n    }\n  }\n  .sm\\:max-w-\\[500px\\] {\n    @media (width >= 40rem) {\n      max-width: 500px;\n    }\n  }\n  .sm\\:max-w-\\[550px\\] {\n    @media (width >= 40rem) {\n      max-width: 550px;\n    }\n  }\n  .sm\\:max-w-\\[600px\\] {\n    @media (width >= 40rem) {\n      max-width: 600px;\n    }\n  }\n  .sm\\:max-w-\\[800px\\] {\n    @media (width >= 40rem) {\n      max-width: 800px;\n    }\n  }\n  .sm\\:max-w-lg {\n    @media (width >= 40rem) {\n      max-width: var(--container-lg);\n    }\n  }\n  .sm\\:max-w-md {\n    @media (width >= 40rem) {\n      max-width: var(--container-md);\n    }\n  }\n  .sm\\:max-w-sm {\n    @media (width >= 40rem) {\n      max-width: var(--container-sm);\n    }\n  }\n  .sm\\:max-w-xl {\n    @media (width >= 40rem) {\n      max-width: var(--container-xl);\n    }\n  }\n  .sm\\:grid-cols-2 {\n    @media (width >= 40rem) {\n      grid-template-columns: repeat(2, minmax(0, 1fr));\n    }\n  }\n  .sm\\:grid-cols-3 {\n    @media (width >= 40rem) {\n      grid-template-columns: repeat(3, minmax(0, 1fr));\n    }\n  }\n  .sm\\:flex-row {\n    @media (width >= 40rem) {\n      flex-direction: row;\n    }\n  }\n  .sm\\:items-center {\n    @media (width >= 40rem) {\n      align-items: center;\n    }\n  }\n  .sm\\:items-end {\n    @media (width >= 40rem) {\n      align-items: flex-end;\n    }\n  }\n  .sm\\:items-start {\n    @media (width >= 40rem) {\n      align-items: flex-start;\n    }\n  }\n  .sm\\:justify-between {\n    @media (width >= 40rem) {\n      justify-content: space-between;\n    }\n  }\n  .sm\\:justify-end {\n    @media (width >= 40rem) {\n      justify-content: flex-end;\n    }\n  }\n  .sm\\:gap-2\\.5 {\n    @media (width >= 40rem) {\n      gap: calc(var(--spacing) * 2.5);\n    }\n  }\n  .sm\\:px-0 {\n    @media (width >= 40rem) {\n      padding-inline: calc(var(--spacing) * 0);\n    }\n  }\n  .sm\\:px-6 {\n    @media (width >= 40rem) {\n      padding-inline: calc(var(--spacing) * 6);\n    }\n  }\n  .sm\\:pt-6 {\n    @media (width >= 40rem) {\n      padding-top: calc(var(--spacing) * 6);\n    }\n  }\n  .sm\\:pr-2\\.5 {\n    @media (width >= 40rem) {\n      padding-right: calc(var(--spacing) * 2.5);\n    }\n  }\n  .sm\\:pl-2\\.5 {\n    @media (width >= 40rem) {\n      padding-left: calc(var(--spacing) * 2.5);\n    }\n  }\n  .sm\\:text-left {\n    @media (width >= 40rem) {\n      text-align: left;\n    }\n  }\n  .sm\\:\\[--scale-enter\\:100\\%\\] {\n    @media (width >= 40rem) {\n      --scale-enter: 100%;\n    }\n  }\n  .sm\\:\\[--scale-exit\\:103\\%\\] {\n    @media (width >= 40rem) {\n      --scale-exit: 103%;\n    }\n  }\n  .sm\\:\\[--slide-enter\\:0px\\] {\n    @media (width >= 40rem) {\n      --slide-enter: 0px;\n    }\n  }\n  .sm\\:\\[--slide-exit\\:0px\\] {\n    @media (width >= 40rem) {\n      --slide-exit: 0px;\n    }\n  }\n  .sm\\:data-\\[placement\\=bottom-center\\]\\:mx-auto {\n    @media (width >= 40rem) {\n      &[data-placement=\"bottom-center\"] {\n        margin-inline: auto;\n      }\n    }\n  }\n  .sm\\:data-\\[placement\\=bottom-center\\]\\:w-max {\n    @media (width >= 40rem) {\n      &[data-placement=\"bottom-center\"] {\n        width: max-content;\n      }\n    }\n  }\n  .sm\\:data-\\[placement\\=bottom-left\\]\\:ml-2 {\n    @media (width >= 40rem) {\n      &[data-placement=\"bottom-left\"] {\n        margin-left: calc(var(--spacing) * 2);\n      }\n    }\n  }\n  .sm\\:data-\\[placement\\=bottom-left\\]\\:w-max {\n    @media (width >= 40rem) {\n      &[data-placement=\"bottom-left\"] {\n        width: max-content;\n      }\n    }\n  }\n  .sm\\:data-\\[placement\\=bottom-right\\]\\:mr-2 {\n    @media (width >= 40rem) {\n      &[data-placement=\"bottom-right\"] {\n        margin-right: calc(var(--spacing) * 2);\n      }\n    }\n  }\n  .sm\\:data-\\[placement\\=bottom-right\\]\\:w-max {\n    @media (width >= 40rem) {\n      &[data-placement=\"bottom-right\"] {\n        width: max-content;\n      }\n    }\n  }\n  .sm\\:data-\\[placement\\=top-center\\]\\:mx-auto {\n    @media (width >= 40rem) {\n      &[data-placement=\"top-center\"] {\n        margin-inline: auto;\n      }\n    }\n  }\n  .sm\\:data-\\[placement\\=top-center\\]\\:w-max {\n    @media (width >= 40rem) {\n      &[data-placement=\"top-center\"] {\n        width: max-content;\n      }\n    }\n  }\n  .sm\\:data-\\[placement\\=top-left\\]\\:ml-2 {\n    @media (width >= 40rem) {\n      &[data-placement=\"top-left\"] {\n        margin-left: calc(var(--spacing) * 2);\n      }\n    }\n  }\n  .sm\\:data-\\[placement\\=top-left\\]\\:w-max {\n    @media (width >= 40rem) {\n      &[data-placement=\"top-left\"] {\n        width: max-content;\n      }\n    }\n  }\n  .sm\\:data-\\[placement\\=top-right\\]\\:mr-2 {\n    @media (width >= 40rem) {\n      &[data-placement=\"top-right\"] {\n        margin-right: calc(var(--spacing) * 2);\n      }\n    }\n  }\n  .sm\\:data-\\[placement\\=top-right\\]\\:w-max {\n    @media (width >= 40rem) {\n      &[data-placement=\"top-right\"] {\n        width: max-content;\n      }\n    }\n  }\n  .data-\\[vaul-drawer-direction\\=left\\]\\:sm\\:max-w-sm {\n    &[data-vaul-drawer-direction=\"left\"] {\n      @media (width >= 40rem) {\n        max-width: var(--container-sm);\n      }\n    }\n  }\n  .data-\\[vaul-drawer-direction\\=right\\]\\:sm\\:max-w-sm {\n    &[data-vaul-drawer-direction=\"right\"] {\n      @media (width >= 40rem) {\n        max-width: var(--container-sm);\n      }\n    }\n  }\n  .sm\\:data-\\[visible\\=true\\]\\:pointer-events-none {\n    @media (width >= 40rem) {\n      &[data-visible=\"true\"] {\n        pointer-events: none;\n      }\n    }\n  }\n  .sm\\:data-\\[visible\\=true\\]\\:opacity-0 {\n    @media (width >= 40rem) {\n      &[data-visible=\"true\"] {\n        opacity: 0%;\n      }\n    }\n  }\n  .sm\\:group-data-\\[hover\\=true\\]\\:data-\\[visible\\=true\\]\\:pointer-events-auto {\n    @media (width >= 40rem) {\n      &:is(:where(.group)[data-hover=\"true\"] *) {\n        &[data-visible=\"true\"] {\n          pointer-events: auto;\n        }\n      }\n    }\n  }\n  .sm\\:group-data-\\[hover\\=true\\]\\:data-\\[visible\\=true\\]\\:opacity-100 {\n    @media (width >= 40rem) {\n      &:is(:where(.group)[data-hover=\"true\"] *) {\n        &[data-visible=\"true\"] {\n          opacity: 100%;\n        }\n      }\n    }\n  }\n  .md\\:absolute {\n    @media (width >= 48rem) {\n      position: absolute;\n    }\n  }\n  .md\\:col-span-1 {\n    @media (width >= 48rem) {\n      grid-column: span 1 / span 1;\n    }\n  }\n  .md\\:col-span-2 {\n    @media (width >= 48rem) {\n      grid-column: span 2 / span 2;\n    }\n  }\n  .md\\:col-span-4 {\n    @media (width >= 48rem) {\n      grid-column: span 4 / span 4;\n    }\n  }\n  .md\\:col-span-8 {\n    @media (width >= 48rem) {\n      grid-column: span 8 / span 8;\n    }\n  }\n  .md\\:mr-0\\.5 {\n    @media (width >= 48rem) {\n      margin-right: calc(var(--spacing) * 0.5);\n    }\n  }\n  .md\\:block {\n    @media (width >= 48rem) {\n      display: block;\n    }\n  }\n  .md\\:flex {\n    @media (width >= 48rem) {\n      display: flex;\n    }\n  }\n  .md\\:hidden {\n    @media (width >= 48rem) {\n      display: none;\n    }\n  }\n  .md\\:table-cell {\n    @media (width >= 48rem) {\n      display: table-cell;\n    }\n  }\n  .md\\:w-60 {\n    @media (width >= 48rem) {\n      width: calc(var(--spacing) * 60);\n    }\n  }\n  .md\\:w-64 {\n    @media (width >= 48rem) {\n      width: calc(var(--spacing) * 64);\n    }\n  }\n  .md\\:w-\\[var\\(--radix-navigation-menu-viewport-width\\)\\] {\n    @media (width >= 48rem) {\n      width: var(--radix-navigation-menu-viewport-width);\n    }\n  }\n  .md\\:w-auto {\n    @media (width >= 48rem) {\n      width: auto;\n    }\n  }\n  .md\\:max-w-2xl {\n    @media (width >= 48rem) {\n      max-width: var(--container-2xl);\n    }\n  }\n  .md\\:grid-cols-1 {\n    @media (width >= 48rem) {\n      grid-template-columns: repeat(1, minmax(0, 1fr));\n    }\n  }\n  .md\\:grid-cols-2 {\n    @media (width >= 48rem) {\n      grid-template-columns: repeat(2, minmax(0, 1fr));\n    }\n  }\n  .md\\:grid-cols-3 {\n    @media (width >= 48rem) {\n      grid-template-columns: repeat(3, minmax(0, 1fr));\n    }\n  }\n  .md\\:grid-cols-4 {\n    @media (width >= 48rem) {\n      grid-template-columns: repeat(4, minmax(0, 1fr));\n    }\n  }\n  .md\\:grid-cols-5 {\n    @media (width >= 48rem) {\n      grid-template-columns: repeat(5, minmax(0, 1fr));\n    }\n  }\n  .md\\:grid-cols-12 {\n    @media (width >= 48rem) {\n      grid-template-columns: repeat(12, minmax(0, 1fr));\n    }\n  }\n  .md\\:flex-row {\n    @media (width >= 48rem) {\n      flex-direction: row;\n    }\n  }\n  .md\\:justify-start {\n    @media (width >= 48rem) {\n      justify-content: flex-start;\n    }\n  }\n  .md\\:gap-6 {\n    @media (width >= 48rem) {\n      gap: calc(var(--spacing) * 6);\n    }\n  }\n  .md\\:p-10 {\n    @media (width >= 48rem) {\n      padding: calc(var(--spacing) * 10);\n    }\n  }\n  .md\\:py-6 {\n    @media (width >= 48rem) {\n      padding-block: calc(var(--spacing) * 6);\n    }\n  }\n  .md\\:text-sm {\n    @media (width >= 48rem) {\n      font-size: var(--text-sm);\n      line-height: var(--tw-leading, var(--text-sm--line-height));\n    }\n  }\n  .md\\:opacity-0 {\n    @media (width >= 48rem) {\n      opacity: 0%;\n    }\n  }\n  .md\\:peer-data-\\[variant\\=inset\\]\\:m-2 {\n    @media (width >= 48rem) {\n      &:is(:where(.peer)[data-variant=\"inset\"] ~ *) {\n        margin: calc(var(--spacing) * 2);\n      }\n    }\n  }\n  .md\\:peer-data-\\[variant\\=inset\\]\\:ml-0 {\n    @media (width >= 48rem) {\n      &:is(:where(.peer)[data-variant=\"inset\"] ~ *) {\n        margin-left: calc(var(--spacing) * 0);\n      }\n    }\n  }\n  .md\\:peer-data-\\[variant\\=inset\\]\\:rounded-xl {\n    @media (width >= 48rem) {\n      &:is(:where(.peer)[data-variant=\"inset\"] ~ *) {\n        border-radius: calc(var(--radius) + 4px);\n      }\n    }\n  }\n  .md\\:peer-data-\\[variant\\=inset\\]\\:shadow-sm {\n    @media (width >= 48rem) {\n      &:is(:where(.peer)[data-variant=\"inset\"] ~ *) {\n        --tw-shadow: 0 1px 3px 0 var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 1px 2px -1px var(--tw-shadow-color, rgb(0 0 0 / 0.1));\n        box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\n      }\n    }\n  }\n  .md\\:peer-data-\\[variant\\=inset\\]\\:peer-data-\\[state\\=collapsed\\]\\:ml-2 {\n    @media (width >= 48rem) {\n      &:is(:where(.peer)[data-variant=\"inset\"] ~ *) {\n        &:is(:where(.peer)[data-state=\"collapsed\"] ~ *) {\n          margin-left: calc(var(--spacing) * 2);\n        }\n      }\n    }\n  }\n  .md\\:after\\:hidden {\n    @media (width >= 48rem) {\n      &::after {\n        content: var(--tw-content);\n        display: none;\n      }\n    }\n  }\n  .lg\\:col-span-2 {\n    @media (width >= 64rem) {\n      grid-column: span 2 / span 2;\n    }\n  }\n  .lg\\:ml-0 {\n    @media (width >= 64rem) {\n      margin-left: calc(var(--spacing) * 0);\n    }\n  }\n  .lg\\:block {\n    @media (width >= 64rem) {\n      display: block;\n    }\n  }\n  .lg\\:flex {\n    @media (width >= 64rem) {\n      display: flex;\n    }\n  }\n  .lg\\:hidden {\n    @media (width >= 64rem) {\n      display: none;\n    }\n  }\n  .lg\\:inline {\n    @media (width >= 64rem) {\n      display: inline;\n    }\n  }\n  .lg\\:h-\\[calc\\(100svh-56px\\)\\] {\n    @media (width >= 64rem) {\n      height: calc(100svh - 56px);\n    }\n  }\n  .lg\\:h-\\[calc\\(100svh-96px\\)\\] {\n    @media (width >= 64rem) {\n      height: calc(100svh - 96px);\n    }\n  }\n  .lg\\:w-fit {\n    @media (width >= 64rem) {\n      width: fit-content;\n    }\n  }\n  .lg\\:max-w-3xl {\n    @media (width >= 64rem) {\n      max-width: var(--container-3xl);\n    }\n  }\n  .lg\\:grid-cols-2 {\n    @media (width >= 64rem) {\n      grid-template-columns: repeat(2, minmax(0, 1fr));\n    }\n  }\n  .lg\\:grid-cols-3 {\n    @media (width >= 64rem) {\n      grid-template-columns: repeat(3, minmax(0, 1fr));\n    }\n  }\n  .lg\\:grid-cols-4 {\n    @media (width >= 64rem) {\n      grid-template-columns: repeat(4, minmax(0, 1fr));\n    }\n  }\n  .lg\\:grid-cols-5 {\n    @media (width >= 64rem) {\n      grid-template-columns: repeat(5, minmax(0, 1fr));\n    }\n  }\n  .lg\\:grid-cols-6 {\n    @media (width >= 64rem) {\n      grid-template-columns: repeat(6, minmax(0, 1fr));\n    }\n  }\n  .lg\\:gap-2 {\n    @media (width >= 64rem) {\n      gap: calc(var(--spacing) * 2);\n    }\n  }\n  .lg\\:space-x-8 {\n    @media (width >= 64rem) {\n      :where(& > :not(:last-child)) {\n        --tw-space-x-reverse: 0;\n        margin-inline-start: calc(calc(var(--spacing) * 8) * var(--tw-space-x-reverse));\n        margin-inline-end: calc(calc(var(--spacing) * 8) * calc(1 - var(--tw-space-x-reverse)));\n      }\n    }\n  }\n  .lg\\:rounded-md {\n    @media (width >= 64rem) {\n      border-radius: calc(var(--radius) - 2px);\n    }\n  }\n  .lg\\:border {\n    @media (width >= 64rem) {\n      border-style: var(--tw-border-style);\n      border-width: 1px;\n    }\n  }\n  .lg\\:p-2 {\n    @media (width >= 64rem) {\n      padding: calc(var(--spacing) * 2);\n    }\n  }\n  .lg\\:px-6 {\n    @media (width >= 64rem) {\n      padding-inline: calc(var(--spacing) * 6);\n    }\n  }\n  .xl\\:inline {\n    @media (width >= 80rem) {\n      display: inline;\n    }\n  }\n  .xl\\:grid-cols-6 {\n    @media (width >= 80rem) {\n      grid-template-columns: repeat(6, minmax(0, 1fr));\n    }\n  }\n  .\\@\\[200px\\]\\/card\\:text-2xl {\n    @container card (width >= 200px) {\n      font-size: var(--text-2xl);\n      line-height: var(--tw-leading, var(--text-2xl--line-height));\n    }\n  }\n  .\\@\\[250px\\]\\/card\\:text-3xl {\n    @container card (width >= 250px) {\n      font-size: var(--text-3xl);\n      line-height: var(--tw-leading, var(--text-3xl--line-height));\n    }\n  }\n  .\\@\\[540px\\]\\/card\\:block {\n    @container card (width >= 540px) {\n      display: block;\n    }\n  }\n  .\\@\\[540px\\]\\/card\\:hidden {\n    @container card (width >= 540px) {\n      display: none;\n    }\n  }\n  .\\@\\[767px\\]\\/card\\:flex {\n    @container card (width >= 767px) {\n      display: flex;\n    }\n  }\n  .\\@\\[767px\\]\\/card\\:hidden {\n    @container card (width >= 767px) {\n      display: none;\n    }\n  }\n  .\\@xl\\/main\\:grid-cols-2 {\n    @container main (width >= 36rem) {\n      grid-template-columns: repeat(2, minmax(0, 1fr));\n    }\n  }\n  .\\@4xl\\/main\\:flex {\n    @container main (width >= 56rem) {\n      display: flex;\n    }\n  }\n  .\\@4xl\\/main\\:hidden {\n    @container main (width >= 56rem) {\n      display: none;\n    }\n  }\n  .\\@5xl\\/main\\:grid-cols-4 {\n    @container main (width >= 64rem) {\n      grid-template-columns: repeat(4, minmax(0, 1fr));\n    }\n  }\n  .rtl\\:right-auto {\n    &:where(:dir(rtl), [dir=\"rtl\"], [dir=\"rtl\"] *) {\n      right: auto;\n    }\n  }\n  .rtl\\:left-2 {\n    &:where(:dir(rtl), [dir=\"rtl\"], [dir=\"rtl\"] *) {\n      left: calc(var(--spacing) * 2);\n    }\n  }\n  .rtl\\:origin-top-right {\n    &:where(:dir(rtl), [dir=\"rtl\"], [dir=\"rtl\"] *) {\n      transform-origin: top right;\n    }\n  }\n  .rtl\\:-rotate-180 {\n    &:where(:dir(rtl), [dir=\"rtl\"], [dir=\"rtl\"] *) {\n      rotate: calc(180deg * -1);\n    }\n  }\n  .rtl\\:rotate-180 {\n    &:where(:dir(rtl), [dir=\"rtl\"], [dir=\"rtl\"] *) {\n      rotate: 180deg;\n    }\n  }\n  .rtl\\:flex-row-reverse {\n    &:where(:dir(rtl), [dir=\"rtl\"], [dir=\"rtl\"] *) {\n      flex-direction: row-reverse;\n    }\n  }\n  .rtl\\:space-x-reverse {\n    &:where(:dir(rtl), [dir=\"rtl\"], [dir=\"rtl\"] *) {\n      :where(& > :not(:last-child)) {\n        --tw-space-x-reverse: 1;\n      }\n    }\n  }\n  .rtl\\:data-\\[focus-visible\\=true\\]\\:translate-x-3 {\n    &:where(:dir(rtl), [dir=\"rtl\"], [dir=\"rtl\"] *) {\n      &[data-focus-visible=\"true\"] {\n        --tw-translate-x: calc(var(--spacing) * 3);\n        translate: var(--tw-translate-x) var(--tw-translate-y);\n      }\n    }\n  }\n  .rtl\\:data-\\[hover\\=true\\]\\:translate-x-3 {\n    &:where(:dir(rtl), [dir=\"rtl\"], [dir=\"rtl\"] *) {\n      &[data-hover=\"true\"] {\n        --tw-translate-x: calc(var(--spacing) * 3);\n        translate: var(--tw-translate-x) var(--tw-translate-y);\n      }\n    }\n  }\n  .rtl\\:data-\\[open\\=true\\]\\:-rotate-90 {\n    &:where(:dir(rtl), [dir=\"rtl\"], [dir=\"rtl\"] *) {\n      &[data-open=\"true\"] {\n        rotate: calc(90deg * -1);\n      }\n    }\n  }\n  .dark\\:border-danger-100 {\n    &:is(.dark *) {\n      border-color: hsl(var(--heroui-danger-100) / 1);\n    }\n  }\n  .dark\\:border-default-200 {\n    &:is(.dark *) {\n      border-color: hsl(var(--heroui-default-200) / 1);\n    }\n  }\n  .dark\\:border-input {\n    &:is(.dark *) {\n      border-color: var(--input);\n    }\n  }\n  .dark\\:border-primary-100 {\n    &:is(.dark *) {\n      border-color: hsl(var(--heroui-primary-100) / 1);\n    }\n  }\n  .dark\\:border-success-100 {\n    &:is(.dark *) {\n      border-color: hsl(var(--heroui-success-100) / 1);\n    }\n  }\n  .dark\\:border-warning-100 {\n    &:is(.dark *) {\n      border-color: hsl(var(--heroui-warning-100) / 1);\n    }\n  }\n  .dark\\:bg-background {\n    &:is(.dark *) {\n      background-color: var(--background);\n    }\n  }\n  .dark\\:bg-background\\/20 {\n    &:is(.dark *) {\n      background-color: var(--background);\n      @supports (color: color-mix(in lab, red, red)) {\n        background-color: color-mix(in oklab, var(--background) 20%, transparent);\n      }\n    }\n  }\n  .dark\\:bg-content2 {\n    &:is(.dark *) {\n      background-color: hsl(var(--heroui-content2) / 1);\n    }\n  }\n  .dark\\:bg-danger-50 {\n    &:is(.dark *) {\n      background-color: hsl(var(--heroui-danger-50) / 1);\n    }\n  }\n  .dark\\:bg-danger-50\\/50 {\n    &:is(.dark *) {\n      background-color: hsl(var(--heroui-danger-50) / 1);\n      @supports (color: color-mix(in lab, red, red)) {\n        background-color: color-mix(in oklab, hsl(var(--heroui-danger-50) / 1) 50%, transparent);\n      }\n    }\n  }\n  .dark\\:bg-danger-100 {\n    &:is(.dark *) {\n      background-color: hsl(var(--heroui-danger-100) / 1);\n    }\n  }\n  .dark\\:bg-default {\n    &:is(.dark *) {\n      background-color: hsl(var(--heroui-default) / 1);\n    }\n  }\n  .dark\\:bg-default-50\\/50 {\n    &:is(.dark *) {\n      background-color: hsl(var(--heroui-default-50) / 1);\n      @supports (color: color-mix(in lab, red, red)) {\n        background-color: color-mix(in oklab, hsl(var(--heroui-default-50) / 1) 50%, transparent);\n      }\n    }\n  }\n  .dark\\:bg-default-100 {\n    &:is(.dark *) {\n      background-color: hsl(var(--heroui-default-100) / 1);\n    }\n  }\n  .dark\\:bg-destructive\\/60 {\n    &:is(.dark *) {\n      background-color: var(--destructive);\n      @supports (color: color-mix(in lab, red, red)) {\n        background-color: color-mix(in oklab, var(--destructive) 60%, transparent);\n      }\n    }\n  }\n  .dark\\:bg-input\\/30 {\n    &:is(.dark *) {\n      background-color: var(--input);\n      @supports (color: color-mix(in lab, red, red)) {\n        background-color: color-mix(in oklab, var(--input) 30%, transparent);\n      }\n    }\n  }\n  .dark\\:bg-primary-50 {\n    &:is(.dark *) {\n      background-color: hsl(var(--heroui-primary-50) / 1);\n    }\n  }\n  .dark\\:bg-primary-50\\/50 {\n    &:is(.dark *) {\n      background-color: hsl(var(--heroui-primary-50) / 1);\n      @supports (color: color-mix(in lab, red, red)) {\n        background-color: color-mix(in oklab, hsl(var(--heroui-primary-50) / 1) 50%, transparent);\n      }\n    }\n  }\n  .dark\\:bg-primary-100 {\n    &:is(.dark *) {\n      background-color: hsl(var(--heroui-primary-100) / 1);\n    }\n  }\n  .dark\\:bg-secondary-50 {\n    &:is(.dark *) {\n      background-color: hsl(var(--heroui-secondary-50) / 1);\n    }\n  }\n  .dark\\:bg-secondary-50\\/50 {\n    &:is(.dark *) {\n      background-color: hsl(var(--heroui-secondary-50) / 1);\n      @supports (color: color-mix(in lab, red, red)) {\n        background-color: color-mix(in oklab, hsl(var(--heroui-secondary-50) / 1) 50%, transparent);\n      }\n    }\n  }\n  .dark\\:bg-secondary-100 {\n    &:is(.dark *) {\n      background-color: hsl(var(--heroui-secondary-100) / 1);\n    }\n  }\n  .dark\\:bg-success-50 {\n    &:is(.dark *) {\n      background-color: hsl(var(--heroui-success-50) / 1);\n    }\n  }\n  .dark\\:bg-success-50\\/50 {\n    &:is(.dark *) {\n      background-color: hsl(var(--heroui-success-50) / 1);\n      @supports (color: color-mix(in lab, red, red)) {\n        background-color: color-mix(in oklab, hsl(var(--heroui-success-50) / 1) 50%, transparent);\n      }\n    }\n  }\n  .dark\\:bg-success-100 {\n    &:is(.dark *) {\n      background-color: hsl(var(--heroui-success-100) / 1);\n    }\n  }\n  .dark\\:bg-transparent {\n    &:is(.dark *) {\n      background-color: transparent;\n    }\n  }\n  .dark\\:bg-warning-50 {\n    &:is(.dark *) {\n      background-color: hsl(var(--heroui-warning-50) / 1);\n    }\n  }\n  .dark\\:bg-warning-50\\/50 {\n    &:is(.dark *) {\n      background-color: hsl(var(--heroui-warning-50) / 1);\n      @supports (color: color-mix(in lab, red, red)) {\n        background-color: color-mix(in oklab, hsl(var(--heroui-warning-50) / 1) 50%, transparent);\n      }\n    }\n  }\n  .dark\\:bg-warning-100 {\n    &:is(.dark *) {\n      background-color: hsl(var(--heroui-warning-100) / 1);\n    }\n  }\n  .dark\\:fill-green-400 {\n    &:is(.dark *) {\n      fill: var(--color-green-400);\n    }\n  }\n  .dark\\:text-amber-400 {\n    &:is(.dark *) {\n      color: var(--color-amber-400);\n    }\n  }\n  .dark\\:text-amber-500 {\n    &:is(.dark *) {\n      color: var(--color-amber-500);\n    }\n  }\n  .dark\\:text-danger-500 {\n    &:is(.dark *) {\n      color: hsl(var(--heroui-danger-500) / 1);\n    }\n  }\n  .dark\\:text-green-500 {\n    &:is(.dark *) {\n      color: var(--color-green-500);\n    }\n  }\n  .dark\\:text-muted-foreground {\n    &:is(.dark *) {\n      color: var(--muted-foreground);\n    }\n  }\n  .dark\\:text-red-500 {\n    &:is(.dark *) {\n      color: var(--color-red-500);\n    }\n  }\n  .dark\\:text-success {\n    &:is(.dark *) {\n      color: hsl(var(--heroui-success) / 1);\n    }\n  }\n  .dark\\:text-warning {\n    &:is(.dark *) {\n      color: hsl(var(--heroui-warning) / 1);\n    }\n  }\n  .dark\\:brightness-\\[0\\.2\\] {\n    &:is(.dark *) {\n      --tw-brightness: brightness(0.2);\n      filter: var(--tw-blur,) var(--tw-brightness,) var(--tw-contrast,) var(--tw-grayscale,) var(--tw-hue-rotate,) var(--tw-invert,) var(--tw-saturate,) var(--tw-sepia,) var(--tw-drop-shadow,);\n    }\n  }\n  .dark\\:grayscale {\n    &:is(.dark *) {\n      --tw-grayscale: grayscale(100%);\n      filter: var(--tw-blur,) var(--tw-brightness,) var(--tw-contrast,) var(--tw-grayscale,) var(--tw-hue-rotate,) var(--tw-invert,) var(--tw-saturate,) var(--tw-sepia,) var(--tw-drop-shadow,);\n    }\n  }\n  .dark\\:placeholder\\:text-danger-500 {\n    &:is(.dark *) {\n      &::placeholder {\n        color: hsl(var(--heroui-danger-500) / 1);\n      }\n    }\n  }\n  .dark\\:placeholder\\:text-success {\n    &:is(.dark *) {\n      &::placeholder {\n        color: hsl(var(--heroui-success) / 1);\n      }\n    }\n  }\n  .dark\\:placeholder\\:text-warning {\n    &:is(.dark *) {\n      &::placeholder {\n        color: hsl(var(--heroui-warning) / 1);\n      }\n    }\n  }\n  .dark\\:before\\:via-default-700\\/10 {\n    &:is(.dark *) {\n      &::before {\n        content: var(--tw-content);\n        --tw-gradient-via: hsl(var(--heroui-default-700) / 1);\n        @supports (color: color-mix(in lab, red, red)) {\n          --tw-gradient-via: color-mix(in oklab, hsl(var(--heroui-default-700) / 1) 10%, transparent);\n        }\n        --tw-gradient-via-stops: var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-via) var(--tw-gradient-via-position), var(--tw-gradient-to) var(--tw-gradient-to-position);\n        --tw-gradient-stops: var(--tw-gradient-via-stops);\n      }\n    }\n  }\n  .dark\\:after\\:bg-content2 {\n    &:is(.dark *) {\n      &::after {\n        content: var(--tw-content);\n        background-color: hsl(var(--heroui-content2) / 1);\n      }\n    }\n  }\n  .dark\\:hover\\:bg-accent\\/50 {\n    &:is(.dark *) {\n      &:hover {\n        @media (hover: hover) {\n          background-color: var(--accent);\n          @supports (color: color-mix(in lab, red, red)) {\n            background-color: color-mix(in oklab, var(--accent) 50%, transparent);\n          }\n        }\n      }\n    }\n  }\n  .dark\\:hover\\:bg-input\\/30 {\n    &:is(.dark *) {\n      &:hover {\n        @media (hover: hover) {\n          background-color: var(--input);\n          @supports (color: color-mix(in lab, red, red)) {\n            background-color: color-mix(in oklab, var(--input) 30%, transparent);\n          }\n        }\n      }\n    }\n  }\n  .dark\\:hover\\:bg-input\\/50 {\n    &:is(.dark *) {\n      &:hover {\n        @media (hover: hover) {\n          background-color: var(--input);\n          @supports (color: color-mix(in lab, red, red)) {\n            background-color: color-mix(in oklab, var(--input) 50%, transparent);\n          }\n        }\n      }\n    }\n  }\n  .dark\\:hover\\:bg-slate-900 {\n    &:is(.dark *) {\n      &:hover {\n        @media (hover: hover) {\n          background-color: var(--color-slate-900);\n        }\n      }\n    }\n  }\n  .dark\\:hover\\:text-slate-50 {\n    &:is(.dark *) {\n      &:hover {\n        @media (hover: hover) {\n          color: var(--color-slate-50);\n        }\n      }\n    }\n  }\n  .dark\\:focus\\:bg-danger-400\\/20 {\n    &:is(.dark *) {\n      &:focus {\n        background-color: hsl(var(--heroui-danger-400) / 1);\n        @supports (color: color-mix(in lab, red, red)) {\n          background-color: color-mix(in oklab, hsl(var(--heroui-danger-400) / 1) 20%, transparent);\n        }\n      }\n    }\n  }\n  .dark\\:focus\\:bg-success-400\\/20 {\n    &:is(.dark *) {\n      &:focus {\n        background-color: hsl(var(--heroui-success-400) / 1);\n        @supports (color: color-mix(in lab, red, red)) {\n          background-color: color-mix(in oklab, hsl(var(--heroui-success-400) / 1) 20%, transparent);\n        }\n      }\n    }\n  }\n  .dark\\:focus\\:bg-warning-400\\/20 {\n    &:is(.dark *) {\n      &:focus {\n        background-color: hsl(var(--heroui-warning-400) / 1);\n        @supports (color: color-mix(in lab, red, red)) {\n          background-color: color-mix(in oklab, hsl(var(--heroui-warning-400) / 1) 20%, transparent);\n        }\n      }\n    }\n  }\n  .dark\\:focus-visible\\:bg-input\\/30 {\n    &:is(.dark *) {\n      &:focus-visible {\n        background-color: var(--input);\n        @supports (color: color-mix(in lab, red, red)) {\n          background-color: color-mix(in oklab, var(--input) 30%, transparent);\n        }\n      }\n    }\n  }\n  .dark\\:focus-visible\\:ring-destructive\\/40 {\n    &:is(.dark *) {\n      &:focus-visible {\n        --tw-ring-color: var(--destructive);\n        @supports (color: color-mix(in lab, red, red)) {\n          --tw-ring-color: color-mix(in oklab, var(--destructive) 40%, transparent);\n        }\n      }\n    }\n  }\n  .dark\\:aria-invalid\\:ring-destructive\\/40 {\n    &:is(.dark *) {\n      &[aria-invalid=\"true\"] {\n        --tw-ring-color: var(--destructive);\n        @supports (color: color-mix(in lab, red, red)) {\n          --tw-ring-color: color-mix(in oklab, var(--destructive) 40%, transparent);\n        }\n      }\n    }\n  }\n  .dark\\:data-\\[active\\=true\\]\\:aria-invalid\\:ring-destructive\\/40 {\n    &:is(.dark *) {\n      &[data-active=\"true\"] {\n        &[aria-invalid=\"true\"] {\n          --tw-ring-color: var(--destructive);\n          @supports (color: color-mix(in lab, red, red)) {\n            --tw-ring-color: color-mix(in oklab, var(--destructive) 40%, transparent);\n          }\n        }\n      }\n    }\n  }\n  .dark\\:data-\\[hover\\=true\\]\\:bg-content2 {\n    &:is(.dark *) {\n      &[data-hover=\"true\"] {\n        background-color: hsl(var(--heroui-content2) / 1);\n      }\n    }\n  }\n  .dark\\:data-\\[hover\\=true\\]\\:bg-danger-50 {\n    &:is(.dark *) {\n      &[data-hover=\"true\"] {\n        background-color: hsl(var(--heroui-danger-50) / 1);\n      }\n    }\n  }\n  .dark\\:data-\\[hover\\=true\\]\\:bg-success-50 {\n    &:is(.dark *) {\n      &[data-hover=\"true\"] {\n        background-color: hsl(var(--heroui-success-50) / 1);\n      }\n    }\n  }\n  .dark\\:data-\\[hover\\=true\\]\\:bg-warning-50 {\n    &:is(.dark *) {\n      &[data-hover=\"true\"] {\n        background-color: hsl(var(--heroui-warning-50) / 1);\n      }\n    }\n  }\n  .dark\\:data-\\[hover\\=true\\]\\:text-danger-500 {\n    &:is(.dark *) {\n      &[data-hover=\"true\"] {\n        color: hsl(var(--heroui-danger-500) / 1);\n      }\n    }\n  }\n  .dark\\:data-\\[hover\\=true\\]\\:text-success-500 {\n    &:is(.dark *) {\n      &[data-hover=\"true\"] {\n        color: hsl(var(--heroui-success-500) / 1);\n      }\n    }\n  }\n  .dark\\:data-\\[hover\\=true\\]\\:text-warning-500 {\n    &:is(.dark *) {\n      &[data-hover=\"true\"] {\n        color: hsl(var(--heroui-warning-500) / 1);\n      }\n    }\n  }\n  .dark\\:data-\\[invalid\\=true\\]\\:focus\\:bg-danger-400\\/20 {\n    &:is(.dark *) {\n      &[data-invalid=\"true\"] {\n        &:focus {\n          background-color: hsl(var(--heroui-danger-400) / 1);\n          @supports (color: color-mix(in lab, red, red)) {\n            background-color: color-mix(in oklab, hsl(var(--heroui-danger-400) / 1) 20%, transparent);\n          }\n        }\n      }\n    }\n  }\n  .dark\\:data-\\[selected\\=true\\]\\:text-danger-500 {\n    &:is(.dark *) {\n      &[data-selected=\"true\"] {\n        color: hsl(var(--heroui-danger-500) / 1);\n      }\n    }\n  }\n  .dark\\:data-\\[selected\\=true\\]\\:text-success {\n    &:is(.dark *) {\n      &[data-selected=\"true\"] {\n        color: hsl(var(--heroui-success) / 1);\n      }\n    }\n  }\n  .dark\\:data-\\[selected\\=true\\]\\:text-warning {\n    &:is(.dark *) {\n      &[data-selected=\"true\"] {\n        color: hsl(var(--heroui-warning) / 1);\n      }\n    }\n  }\n  .dark\\:data-\\[selected\\=true\\]\\:data-\\[hover\\=true\\]\\:bg-danger {\n    &:is(.dark *) {\n      &[data-selected=\"true\"] {\n        &[data-hover=\"true\"] {\n          background-color: hsl(var(--heroui-danger) / 1);\n        }\n      }\n    }\n  }\n  .dark\\:data-\\[selected\\=true\\]\\:data-\\[hover\\=true\\]\\:bg-success {\n    &:is(.dark *) {\n      &[data-selected=\"true\"] {\n        &[data-hover=\"true\"] {\n          background-color: hsl(var(--heroui-success) / 1);\n        }\n      }\n    }\n  }\n  .dark\\:data-\\[selected\\=true\\]\\:data-\\[hover\\=true\\]\\:bg-warning {\n    &:is(.dark *) {\n      &[data-selected=\"true\"] {\n        &[data-hover=\"true\"] {\n          background-color: hsl(var(--heroui-warning) / 1);\n        }\n      }\n    }\n  }\n  .dark\\:data-\\[selected\\=true\\]\\:data-\\[hover\\=true\\]\\:text-danger-foreground {\n    &:is(.dark *) {\n      &[data-selected=\"true\"] {\n        &[data-hover=\"true\"] {\n          color: hsl(var(--heroui-danger-foreground) / 1);\n        }\n      }\n    }\n  }\n  .dark\\:data-\\[selected\\=true\\]\\:data-\\[hover\\=true\\]\\:text-success-foreground {\n    &:is(.dark *) {\n      &[data-selected=\"true\"] {\n        &[data-hover=\"true\"] {\n          color: hsl(var(--heroui-success-foreground) / 1);\n        }\n      }\n    }\n  }\n  .dark\\:data-\\[selected\\=true\\]\\:data-\\[hover\\=true\\]\\:text-warning-foreground {\n    &:is(.dark *) {\n      &[data-selected=\"true\"] {\n        &[data-hover=\"true\"] {\n          color: hsl(var(--heroui-warning-foreground) / 1);\n        }\n      }\n    }\n  }\n  .dark\\:data-\\[selected\\=true\\]\\:data-\\[range-selection\\=true\\]\\:text-success-500 {\n    &:is(.dark *) {\n      &[data-selected=\"true\"] {\n        &[data-range-selection=\"true\"] {\n          color: hsl(var(--heroui-success-500) / 1);\n        }\n      }\n    }\n  }\n  .dark\\:data-\\[selected\\=true\\]\\:data-\\[range-selection\\=true\\]\\:before\\:bg-success-50 {\n    &:is(.dark *) {\n      &[data-selected=\"true\"] {\n        &[data-range-selection=\"true\"] {\n          &::before {\n            content: var(--tw-content);\n            background-color: hsl(var(--heroui-success-50) / 1);\n          }\n        }\n      }\n    }\n  }\n  .dark\\:data-\\[selected\\=true\\]\\:data-\\[range-selection\\=true\\]\\:before\\:bg-warning-50 {\n    &:is(.dark *) {\n      &[data-selected=\"true\"] {\n        &[data-range-selection=\"true\"] {\n          &::before {\n            content: var(--tw-content);\n            background-color: hsl(var(--heroui-warning-50) / 1);\n          }\n        }\n      }\n    }\n  }\n  .dark\\:data-\\[selected\\=true\\]\\:data-\\[selection-end\\=true\\]\\:data-\\[range-selection\\=true\\]\\:text-success-foreground {\n    &:is(.dark *) {\n      &[data-selected=\"true\"] {\n        &[data-selection-end=\"true\"] {\n          &[data-range-selection=\"true\"] {\n            color: hsl(var(--heroui-success-foreground) / 1);\n          }\n        }\n      }\n    }\n  }\n  .dark\\:data-\\[selected\\=true\\]\\:data-\\[selection-start\\=true\\]\\:data-\\[range-selection\\=true\\]\\:text-success-foreground {\n    &:is(.dark *) {\n      &[data-selected=\"true\"] {\n        &[data-selection-start=\"true\"] {\n          &[data-range-selection=\"true\"] {\n            color: hsl(var(--heroui-success-foreground) / 1);\n          }\n        }\n      }\n    }\n  }\n  .dark\\:\\*\\:data-\\[slot\\=card\\]\\:bg-card {\n    &:is(.dark *) {\n      :is(& > *) {\n        &[data-slot=\"card\"] {\n          background-color: var(--card);\n        }\n      }\n    }\n  }\n  .dark\\:data-\\[state\\=active\\]\\:border-input {\n    &:is(.dark *) {\n      &[data-state=\"active\"] {\n        border-color: var(--input);\n      }\n    }\n  }\n  .dark\\:data-\\[state\\=active\\]\\:bg-input\\/30 {\n    &:is(.dark *) {\n      &[data-state=\"active\"] {\n        background-color: var(--input);\n        @supports (color: color-mix(in lab, red, red)) {\n          background-color: color-mix(in oklab, var(--input) 30%, transparent);\n        }\n      }\n    }\n  }\n  .dark\\:data-\\[state\\=active\\]\\:text-foreground {\n    &:is(.dark *) {\n      &[data-state=\"active\"] {\n        color: var(--foreground);\n      }\n    }\n  }\n  .dark\\:data-\\[state\\=checked\\]\\:bg-primary {\n    &:is(.dark *) {\n      &[data-state=\"checked\"] {\n        background-color: var(--primary);\n      }\n    }\n  }\n  .dark\\:data-\\[state\\=checked\\]\\:bg-primary-foreground {\n    &:is(.dark *) {\n      &[data-state=\"checked\"] {\n        background-color: var(--primary-foreground);\n      }\n    }\n  }\n  .dark\\:data-\\[state\\=unchecked\\]\\:bg-foreground {\n    &:is(.dark *) {\n      &[data-state=\"unchecked\"] {\n        background-color: var(--foreground);\n      }\n    }\n  }\n  .dark\\:data-\\[state\\=unchecked\\]\\:bg-input\\/80 {\n    &:is(.dark *) {\n      &[data-state=\"unchecked\"] {\n        background-color: var(--input);\n        @supports (color: color-mix(in lab, red, red)) {\n          background-color: color-mix(in oklab, var(--input) 80%, transparent);\n        }\n      }\n    }\n  }\n  .dark\\:data-\\[variant\\=destructive\\]\\:focus\\:bg-destructive\\/20 {\n    &:is(.dark *) {\n      &[data-variant=\"destructive\"] {\n        &:focus {\n          background-color: var(--destructive);\n          @supports (color: color-mix(in lab, red, red)) {\n            background-color: color-mix(in oklab, var(--destructive) 20%, transparent);\n          }\n        }\n      }\n    }\n  }\n  .\\[\\&_\\.ProseMirror\\]\\:min-h-\\[80px\\] {\n    & .ProseMirror {\n      min-height: 80px;\n    }\n  }\n  .\\[\\&_\\.ProseMirror\\]\\:outline-none {\n    & .ProseMirror {\n      --tw-outline-style: none;\n      outline-style: none;\n    }\n  }\n  .\\[\\&_\\.chevron-icon\\]\\:flex-none {\n    & .chevron-icon {\n      flex: none;\n    }\n  }\n  .\\[\\&_\\.chevron-icon\\]\\:rotate-180 {\n    & .chevron-icon {\n      rotate: 180deg;\n    }\n  }\n  .\\[\\&_\\.chevron-icon\\]\\:transition-transform {\n    & .chevron-icon {\n      transition-property: transform, translate, scale, rotate;\n      transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));\n      transition-duration: var(--tw-duration, var(--default-transition-duration));\n    }\n  }\n  .\\[\\&_\\.recharts-cartesian-axis-tick_text\\]\\:fill-muted-foreground {\n    & .recharts-cartesian-axis-tick text {\n      fill: var(--muted-foreground);\n    }\n  }\n  .\\[\\&_\\.recharts-cartesian-grid_line\\[stroke\\=\\'\\#ccc\\'\\]\\]\\:stroke-border\\/50 {\n    & .recharts-cartesian-grid line[stroke='#ccc'] {\n      stroke: var(--border);\n      @supports (color: color-mix(in lab, red, red)) {\n        stroke: color-mix(in oklab, var(--border) 50%, transparent);\n      }\n    }\n  }\n  .\\[\\&_\\.recharts-curve\\.recharts-tooltip-cursor\\]\\:stroke-border {\n    & .recharts-curve.recharts-tooltip-cursor {\n      stroke: var(--border);\n    }\n  }\n  .\\[\\&_\\.recharts-dot\\[stroke\\=\\'\\#fff\\'\\]\\]\\:stroke-transparent {\n    & .recharts-dot[stroke='#fff'] {\n      stroke: transparent;\n    }\n  }\n  .\\[\\&_\\.recharts-layer\\]\\:outline-hidden {\n    & .recharts-layer {\n      --tw-outline-style: none;\n      outline-style: none;\n      @media (forced-colors: active) {\n        outline: 2px solid transparent;\n        outline-offset: 2px;\n      }\n    }\n  }\n  .\\[\\&_\\.recharts-polar-grid_\\[stroke\\=\\'\\#ccc\\'\\]\\]\\:stroke-border {\n    & .recharts-polar-grid [stroke='#ccc'] {\n      stroke: var(--border);\n    }\n  }\n  .\\[\\&_\\.recharts-radial-bar-background-sector\\]\\:fill-muted {\n    & .recharts-radial-bar-background-sector {\n      fill: var(--muted);\n    }\n  }\n  .\\[\\&_\\.recharts-rectangle\\.recharts-tooltip-cursor\\]\\:fill-muted {\n    & .recharts-rectangle.recharts-tooltip-cursor {\n      fill: var(--muted);\n    }\n  }\n  .\\[\\&_\\.recharts-reference-line_\\[stroke\\=\\'\\#ccc\\'\\]\\]\\:stroke-border {\n    & .recharts-reference-line [stroke='#ccc'] {\n      stroke: var(--border);\n    }\n  }\n  .\\[\\&_\\.recharts-sector\\]\\:outline-hidden {\n    & .recharts-sector {\n      --tw-outline-style: none;\n      outline-style: none;\n      @media (forced-colors: active) {\n        outline: 2px solid transparent;\n        outline-offset: 2px;\n      }\n    }\n  }\n  .\\[\\&_\\.recharts-sector\\[stroke\\=\\'\\#fff\\'\\]\\]\\:stroke-transparent {\n    & .recharts-sector[stroke='#fff'] {\n      stroke: transparent;\n    }\n  }\n  .\\[\\&_\\.recharts-surface\\]\\:outline-hidden {\n    & .recharts-surface {\n      --tw-outline-style: none;\n      outline-style: none;\n      @media (forced-colors: active) {\n        outline: 2px solid transparent;\n        outline-offset: 2px;\n      }\n    }\n  }\n  .\\[\\&_\\.tabler-icon\\]\\:h-5 {\n    & .tabler-icon {\n      height: calc(var(--spacing) * 5);\n    }\n  }\n  .\\[\\&_\\.tabler-icon\\]\\:min-h-\\[20px\\] {\n    & .tabler-icon {\n      min-height: 20px;\n    }\n  }\n  .\\[\\&_\\.tabler-icon\\]\\:w-5 {\n    & .tabler-icon {\n      width: calc(var(--spacing) * 5);\n    }\n  }\n  .\\[\\&_\\.tabler-icon\\]\\:min-w-\\[20px\\] {\n    & .tabler-icon {\n      min-width: 20px;\n    }\n  }\n  .\\[\\&_\\[cmdk-group-heading\\]\\]\\:px-2 {\n    & [cmdk-group-heading] {\n      padding-inline: calc(var(--spacing) * 2);\n    }\n  }\n  .\\[\\&_\\[cmdk-group-heading\\]\\]\\:py-1\\.5 {\n    & [cmdk-group-heading] {\n      padding-block: calc(var(--spacing) * 1.5);\n    }\n  }\n  .\\[\\&_\\[cmdk-group-heading\\]\\]\\:text-xs {\n    & [cmdk-group-heading] {\n      font-size: var(--text-xs);\n      line-height: var(--tw-leading, var(--text-xs--line-height));\n    }\n  }\n  .\\[\\&_\\[cmdk-group-heading\\]\\]\\:font-medium {\n    & [cmdk-group-heading] {\n      --tw-font-weight: var(--font-weight-medium);\n      font-weight: var(--font-weight-medium);\n    }\n  }\n  .\\[\\&_\\[cmdk-group-heading\\]\\]\\:text-muted-foreground {\n    & [cmdk-group-heading] {\n      color: var(--muted-foreground);\n    }\n  }\n  .\\[\\&_\\[cmdk-group\\]\\]\\:px-2 {\n    & [cmdk-group] {\n      padding-inline: calc(var(--spacing) * 2);\n    }\n  }\n  .\\[\\&_\\[cmdk-group\\]\\:not\\(\\[hidden\\]\\)_\\~\\[cmdk-group\\]\\]\\:pt-0 {\n    & [cmdk-group]:not([hidden]) ~[cmdk-group] {\n      padding-top: calc(var(--spacing) * 0);\n    }\n  }\n  .\\[\\&_\\[cmdk-input-wrapper\\]_svg\\]\\:h-5 {\n    & [cmdk-input-wrapper] svg {\n      height: calc(var(--spacing) * 5);\n    }\n  }\n  .\\[\\&_\\[cmdk-input-wrapper\\]_svg\\]\\:w-5 {\n    & [cmdk-input-wrapper] svg {\n      width: calc(var(--spacing) * 5);\n    }\n  }\n  .\\[\\&_\\[cmdk-input\\]\\]\\:h-12 {\n    & [cmdk-input] {\n      height: calc(var(--spacing) * 12);\n    }\n  }\n  .\\[\\&_\\[cmdk-item\\]\\]\\:px-2 {\n    & [cmdk-item] {\n      padding-inline: calc(var(--spacing) * 2);\n    }\n  }\n  .\\[\\&_\\[cmdk-item\\]\\]\\:py-3 {\n    & [cmdk-item] {\n      padding-block: calc(var(--spacing) * 3);\n    }\n  }\n  .\\[\\&_\\[cmdk-item\\]_svg\\]\\:h-5 {\n    & [cmdk-item] svg {\n      height: calc(var(--spacing) * 5);\n    }\n  }\n  .\\[\\&_\\[cmdk-item\\]_svg\\]\\:w-5 {\n    & [cmdk-item] svg {\n      width: calc(var(--spacing) * 5);\n    }\n  }\n  .\\[\\&_p\\]\\:leading-relaxed {\n    & p {\n      --tw-leading: var(--leading-relaxed);\n      line-height: var(--leading-relaxed);\n    }\n  }\n  .\\[\\&_svg\\]\\:pointer-events-none {\n    & svg {\n      pointer-events: none;\n    }\n  }\n  .\\[\\&_svg\\]\\:shrink-0 {\n    & svg {\n      flex-shrink: 0;\n    }\n  }\n  .\\[\\&_svg\\:not\\(\\[class\\*\\=\\'size-\\'\\]\\)\\]\\:size-4 {\n    & svg:not([class*='size-']) {\n      width: calc(var(--spacing) * 4);\n      height: calc(var(--spacing) * 4);\n    }\n  }\n  .\\[\\&_svg\\:not\\(\\[class\\*\\=\\'text-\\'\\]\\)\\]\\:text-muted-foreground {\n    & svg:not([class*='text-']) {\n      color: var(--muted-foreground);\n    }\n  }\n  .\\[\\&_tr\\]\\:border-b {\n    & tr {\n      border-bottom-style: var(--tw-border-style);\n      border-bottom-width: 1px;\n    }\n  }\n  .\\[\\&_tr\\:last-child\\]\\:border-0 {\n    & tr:last-child {\n      border-style: var(--tw-border-style);\n      border-width: 0px;\n    }\n  }\n  .\\[\\&\\+\\.border-medium\\.border-danger\\]\\:ms-\\[calc\\(theme\\(borderWidth\\.medium\\)\\*-1\\)\\] {\n    &+.border-medium.border-danger {\n      margin-inline-start: calc(var(--heroui-border-width-medium) * -1);\n    }\n  }\n  .\\[\\&\\+\\.border-medium\\.border-default\\]\\:ms-\\[calc\\(theme\\(borderWidth\\.medium\\)\\*-1\\)\\] {\n    &+.border-medium.border-default {\n      margin-inline-start: calc(var(--heroui-border-width-medium) * -1);\n    }\n  }\n  .\\[\\&\\+\\.border-medium\\.border-primary\\]\\:ms-\\[calc\\(theme\\(borderWidth\\.medium\\)\\*-1\\)\\] {\n    &+.border-medium.border-primary {\n      margin-inline-start: calc(var(--heroui-border-width-medium) * -1);\n    }\n  }\n  .\\[\\&\\+\\.border-medium\\.border-secondary\\]\\:ms-\\[calc\\(theme\\(borderWidth\\.medium\\)\\*-1\\)\\] {\n    &+.border-medium.border-secondary {\n      margin-inline-start: calc(var(--heroui-border-width-medium) * -1);\n    }\n  }\n  .\\[\\&\\+\\.border-medium\\.border-success\\]\\:ms-\\[calc\\(theme\\(borderWidth\\.medium\\)\\*-1\\)\\] {\n    &+.border-medium.border-success {\n      margin-inline-start: calc(var(--heroui-border-width-medium) * -1);\n    }\n  }\n  .\\[\\&\\+\\.border-medium\\.border-warning\\]\\:ms-\\[calc\\(theme\\(borderWidth\\.medium\\)\\*-1\\)\\] {\n    &+.border-medium.border-warning {\n      margin-inline-start: calc(var(--heroui-border-width-medium) * -1);\n    }\n  }\n  .\\[\\&\\:has\\(\\>\\.day-range-end\\)\\]\\:rounded-r-md {\n    &:has(>.day-range-end) {\n      border-top-right-radius: calc(var(--radius) - 2px);\n      border-bottom-right-radius: calc(var(--radius) - 2px);\n    }\n  }\n  .\\[\\&\\:has\\(\\>\\.day-range-start\\)\\]\\:rounded-l-md {\n    &:has(>.day-range-start) {\n      border-top-left-radius: calc(var(--radius) - 2px);\n      border-bottom-left-radius: calc(var(--radius) - 2px);\n    }\n  }\n  .\\[\\&\\:has\\(\\[aria-selected\\]\\)\\]\\:rounded-md {\n    &:has([aria-selected]) {\n      border-radius: calc(var(--radius) - 2px);\n    }\n  }\n  .\\[\\&\\:has\\(\\[aria-selected\\]\\)\\]\\:bg-accent {\n    &:has([aria-selected]) {\n      background-color: var(--accent);\n    }\n  }\n  .first\\:\\[\\&\\:has\\(\\[aria-selected\\]\\)\\]\\:rounded-l-md {\n    &:first-child {\n      &:has([aria-selected]) {\n        border-top-left-radius: calc(var(--radius) - 2px);\n        border-bottom-left-radius: calc(var(--radius) - 2px);\n      }\n    }\n  }\n  .last\\:\\[\\&\\:has\\(\\[aria-selected\\]\\)\\]\\:rounded-r-md {\n    &:last-child {\n      &:has([aria-selected]) {\n        border-top-right-radius: calc(var(--radius) - 2px);\n        border-bottom-right-radius: calc(var(--radius) - 2px);\n      }\n    }\n  }\n  .\\[\\&\\:has\\(\\[aria-selected\\]\\.day-range-end\\)\\]\\:rounded-r-md {\n    &:has([aria-selected].day-range-end) {\n      border-top-right-radius: calc(var(--radius) - 2px);\n      border-bottom-right-radius: calc(var(--radius) - 2px);\n    }\n  }\n  .\\[\\&\\:has\\(\\[data-state\\=checked\\]\\)\\]\\:border-primary {\n    &:has([data-state=checked]) {\n      border-color: var(--primary);\n    }\n  }\n  .\\[\\&\\:has\\(\\[data-state\\=checked\\]\\)\\]\\:bg-accent {\n    &:has([data-state=checked]) {\n      background-color: var(--accent);\n    }\n  }\n  .\\[\\&\\:has\\(\\[role\\=checkbox\\]\\)\\]\\:pr-0 {\n    &:has([role=checkbox]) {\n      padding-right: calc(var(--spacing) * 0);\n    }\n  }\n  .\\[\\.border-b\\]\\:pb-6 {\n    &:is(.border-b) {\n      padding-bottom: calc(var(--spacing) * 6);\n    }\n  }\n  .\\[\\.border-t\\]\\:pt-6 {\n    &:is(.border-t) {\n      padding-top: calc(var(--spacing) * 6);\n    }\n  }\n  .\\*\\:\\[span\\]\\:last\\:flex {\n    :is(& > *) {\n      &:is(span) {\n        &:last-child {\n          display: flex;\n        }\n      }\n    }\n  }\n  .\\*\\:\\[span\\]\\:last\\:items-center {\n    :is(& > *) {\n      &:is(span) {\n        &:last-child {\n          align-items: center;\n        }\n      }\n    }\n  }\n  .\\*\\:\\[span\\]\\:last\\:gap-2 {\n    :is(& > *) {\n      &:is(span) {\n        &:last-child {\n          gap: calc(var(--spacing) * 2);\n        }\n      }\n    }\n  }\n  .data-\\[variant\\=destructive\\]\\:\\*\\:\\[svg\\]\\:\\!text-destructive {\n    &[data-variant=\"destructive\"] {\n      :is(& > *) {\n        &:is(svg) {\n          color: var(--destructive) !important;\n        }\n      }\n    }\n  }\n  .\\[\\&\\:not\\(\\:first-child\\)\\]\\:-ml-1 {\n    &:not(:first-child) {\n      margin-left: calc(var(--spacing) * -1);\n    }\n  }\n  .\\[\\&\\:not\\(\\:first-child\\)\\:not\\(\\:last-child\\)\\]\\:rounded-none {\n    &:not(:first-child):not(:last-child) {\n      border-radius: 0;\n    }\n  }\n  .\\[\\&\\:not\\(\\:first-of-type\\)\\]\\:ms-\\[calc\\(theme\\(borderWidth\\.2\\)\\*-1\\)\\] {\n    &:not(:first-of-type) {\n      margin-inline-start: calc(2px * -1);\n    }\n  }\n  .\\[\\&\\:not\\(\\:first-of-type\\)\\:not\\(\\:last-of-type\\)\\]\\:rounded-none {\n    &:not(:first-of-type):not(:last-of-type) {\n      border-radius: 0;\n    }\n  }\n  .\\[\\&\\>\\*\\]\\:relative {\n    &>* {\n      position: relative;\n    }\n  }\n  .\\[\\&\\>\\*\\]\\:z-1 {\n    &>* {\n      z-index: 1;\n    }\n  }\n  .\\[\\&\\>\\[role\\=checkbox\\]\\]\\:translate-y-\\[2px\\] {\n    &>[role=checkbox] {\n      --tw-translate-y: 2px;\n      translate: var(--tw-translate-x) var(--tw-translate-y);\n    }\n  }\n  .\\[\\&\\>button\\]\\:hidden {\n    &>button {\n      display: none;\n    }\n  }\n  .\\[\\&\\>span\\:last-child\\]\\:truncate {\n    &>span:last-child {\n      overflow: hidden;\n      text-overflow: ellipsis;\n      white-space: nowrap;\n    }\n  }\n  .\\[\\&\\>svg\\]\\:pointer-events-none {\n    &>svg {\n      pointer-events: none;\n    }\n  }\n  .\\[\\&\\>svg\\]\\:size-3 {\n    &>svg {\n      width: calc(var(--spacing) * 3);\n      height: calc(var(--spacing) * 3);\n    }\n  }\n  .\\[\\&\\>svg\\]\\:size-3\\.5 {\n    &>svg {\n      width: calc(var(--spacing) * 3.5);\n      height: calc(var(--spacing) * 3.5);\n    }\n  }\n  .\\[\\&\\>svg\\]\\:size-4 {\n    &>svg {\n      width: calc(var(--spacing) * 4);\n      height: calc(var(--spacing) * 4);\n    }\n  }\n  .\\[\\&\\>svg\\]\\:h-2\\.5 {\n    &>svg {\n      height: calc(var(--spacing) * 2.5);\n    }\n  }\n  .\\[\\&\\>svg\\]\\:h-3 {\n    &>svg {\n      height: calc(var(--spacing) * 3);\n    }\n  }\n  .\\[\\&\\>svg\\]\\:w-2\\.5 {\n    &>svg {\n      width: calc(var(--spacing) * 2.5);\n    }\n  }\n  .\\[\\&\\>svg\\]\\:w-3 {\n    &>svg {\n      width: calc(var(--spacing) * 3);\n    }\n  }\n  .\\[\\&\\>svg\\]\\:max-w-\\[theme\\(spacing\\.8\\)\\] {\n    &>svg {\n      max-width: 2rem;\n    }\n  }\n  .\\[\\&\\>svg\\]\\:shrink-0 {\n    &>svg {\n      flex-shrink: 0;\n    }\n  }\n  .\\[\\&\\>svg\\]\\:translate-y-0\\.5 {\n    &>svg {\n      --tw-translate-y: calc(var(--spacing) * 0.5);\n      translate: var(--tw-translate-x) var(--tw-translate-y);\n    }\n  }\n  .\\[\\&\\>svg\\]\\:text-current {\n    &>svg {\n      color: currentcolor;\n    }\n  }\n  .\\[\\&\\>svg\\]\\:text-muted-foreground {\n    &>svg {\n      color: var(--muted-foreground);\n    }\n  }\n  .\\[\\&\\>svg\\]\\:text-sidebar-accent-foreground {\n    &>svg {\n      color: var(--sidebar-accent-foreground);\n    }\n  }\n  .\\[\\&\\>tr\\]\\:first\\:rounded-lg {\n    &>tr {\n      &:first-child {\n        border-radius: var(--radius);\n      }\n    }\n  }\n  .\\[\\&\\>tr\\]\\:first\\:shadow-small {\n    &>tr {\n      &:first-child {\n        --tw-shadow: var(--heroui-box-shadow-small);\n        box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\n      }\n    }\n  }\n  .\\[\\&\\>tr\\]\\:last\\:border-b-0 {\n    &>tr {\n      &:last-child {\n        border-bottom-style: var(--tw-border-style);\n        border-bottom-width: 0px;\n      }\n    }\n  }\n  .\\[\\&\\[data-hover\\=true\\]\\:not\\(\\[data-active\\=true\\]\\)\\]\\:bg-default-100 {\n    &[data-hover=true]:not([data-active=true]) {\n      background-color: hsl(var(--heroui-default-100) / 1);\n    }\n  }\n  .\\[\\&\\[data-hover\\=true\\]\\:not\\(\\[data-active\\=true\\]\\)\\]\\:bg-default-200 {\n    &[data-hover=true]:not([data-active=true]) {\n      background-color: hsl(var(--heroui-default-200) / 1);\n    }\n  }\n  .\\[\\&\\[data-panel-group-direction\\=vertical\\]\\>div\\]\\:rotate-90 {\n    &[data-panel-group-direction=vertical]>div {\n      rotate: 90deg;\n    }\n  }\n  .\\[\\&\\[data-state\\=\\\"checked\\\"\\]\\]\\:border-primary {\n    &[data-state=\"checked\"] {\n      border-color: var(--primary);\n    }\n  }\n  .\\[\\&\\[data-state\\=\\\"checked\\\"\\]\\]\\:bg-primary-foreground {\n    &[data-state=\"checked\"] {\n      background-color: var(--primary-foreground);\n    }\n  }\n  .\\[\\&\\[data-state\\=open\\]\\>svg\\]\\:rotate-180 {\n    &[data-state=open]>svg {\n      rotate: 180deg;\n    }\n  }\n  .\\[\\[data-side\\=left\\]\\[data-collapsible\\=offcanvas\\]_\\&\\]\\:-right-2 {\n    [data-side=left][data-collapsible=offcanvas] & {\n      right: calc(var(--spacing) * -2);\n    }\n  }\n  .\\[\\[data-side\\=left\\]\\[data-state\\=collapsed\\]_\\&\\]\\:cursor-e-resize {\n    [data-side=left][data-state=collapsed] & {\n      cursor: e-resize;\n    }\n  }\n  .\\[\\[data-side\\=right\\]\\[data-collapsible\\=offcanvas\\]_\\&\\]\\:-left-2 {\n    [data-side=right][data-collapsible=offcanvas] & {\n      left: calc(var(--spacing) * -2);\n    }\n  }\n  .\\[\\[data-side\\=right\\]\\[data-state\\=collapsed\\]_\\&\\]\\:cursor-w-resize {\n    [data-side=right][data-state=collapsed] & {\n      cursor: w-resize;\n    }\n  }\n  .\\[a\\&\\]\\:hover\\:bg-accent {\n    a& {\n      &:hover {\n        @media (hover: hover) {\n          background-color: var(--accent);\n        }\n      }\n    }\n  }\n  .\\[a\\&\\]\\:hover\\:bg-destructive\\/90 {\n    a& {\n      &:hover {\n        @media (hover: hover) {\n          background-color: var(--destructive);\n          @supports (color: color-mix(in lab, red, red)) {\n            background-color: color-mix(in oklab, var(--destructive) 90%, transparent);\n          }\n        }\n      }\n    }\n  }\n  .\\[a\\&\\]\\:hover\\:bg-primary\\/90 {\n    a& {\n      &:hover {\n        @media (hover: hover) {\n          background-color: var(--primary);\n          @supports (color: color-mix(in lab, red, red)) {\n            background-color: color-mix(in oklab, var(--primary) 90%, transparent);\n          }\n        }\n      }\n    }\n  }\n  .\\[a\\&\\]\\:hover\\:bg-secondary\\/90 {\n    a& {\n      &:hover {\n        @media (hover: hover) {\n          background-color: var(--secondary);\n          @supports (color: color-mix(in lab, red, red)) {\n            background-color: color-mix(in oklab, var(--secondary) 90%, transparent);\n          }\n        }\n      }\n    }\n  }\n  .\\[a\\&\\]\\:hover\\:text-accent-foreground {\n    a& {\n      &:hover {\n        @media (hover: hover) {\n          color: var(--accent-foreground);\n        }\n      }\n    }\n  }\n}\n.ProseMirror {\n  outline: none;\n  padding: 1rem;\n  min-height: 120px;\n}\n.ProseMirror p {\n  margin: 0.5rem 0;\n}\n.ProseMirror p:first-child {\n  margin-top: 0;\n}\n.ProseMirror p:last-child {\n  margin-bottom: 0;\n}\n.ProseMirror h1, .ProseMirror h2, .ProseMirror h3, .ProseMirror h4, .ProseMirror h5, .ProseMirror h6 {\n  margin: 1rem 0 0.5rem 0;\n  font-weight: 600;\n}\n.ProseMirror h1 {\n  font-size: 1.5rem;\n}\n.ProseMirror h2 {\n  font-size: 1.25rem;\n}\n.ProseMirror h3 {\n  font-size: 1.125rem;\n}\n.ProseMirror ul, .ProseMirror ol {\n  margin: 0.5rem 0;\n  padding-left: 1.5rem;\n}\n.ProseMirror li {\n  margin: 0.25rem 0;\n}\n.ProseMirror a {\n  color: #3b82f6;\n  text-decoration: underline;\n}\n.ProseMirror a:hover {\n  color: #1d4ed8;\n}\n.ProseMirror strong {\n  font-weight: 600;\n}\n.ProseMirror em {\n  font-style: italic;\n}\n.ProseMirror p.is-editor-empty:first-child::before {\n  content: attr(data-placeholder);\n  float: left;\n  color: #9ca3af;\n  pointer-events: none;\n  height: 0;\n}\n:root {\n  --radius: 0.625rem;\n  --background: oklch(1 0 0);\n  --foreground: oklch(0.145 0 0);\n  --card: oklch(1 0 0);\n  --card-foreground: oklch(0.145 0 0);\n  --popover: oklch(1 0 0);\n  --popover-foreground: oklch(0.145 0 0);\n  --primary: oklch(0.55 0.22 263);\n  --primary-foreground: oklch(0.97 0.01 255);\n  --secondary: oklch(0.97 0 0);\n  --secondary-foreground: oklch(0.205 0 0);\n  --muted: oklch(0.97 0 0);\n  --muted-foreground: oklch(0.556 0 0);\n  --accent: oklch(0.97 0 0);\n  --accent-foreground: oklch(0.205 0 0);\n  --destructive: oklch(0.577 0.245 27.325);\n  --border: oklch(0.922 0 0);\n  --input: oklch(0.922 0 0);\n  --ring: oklch(0.708 0 0);\n  --chart-1: oklch(0.646 0.222 41.116);\n  --chart-2: oklch(0.6 0.118 184.704);\n  --chart-3: oklch(0.398 0.07 227.392);\n  --chart-4: oklch(0.828 0.189 84.429);\n  --chart-5: oklch(0.769 0.188 70.08);\n  --sidebar: oklch(0.985 0 0);\n  --sidebar-foreground: oklch(0.145 0 0);\n  --sidebar-primary: oklch(0.205 0 0);\n  --sidebar-primary-foreground: oklch(0.985 0 0);\n  --sidebar-accent: oklch(0.97 0 0);\n  --sidebar-accent-foreground: oklch(0.205 0 0);\n  --sidebar-border: oklch(0.922 0 0);\n  --sidebar-ring: oklch(0.708 0 0);\n}\n.dark {\n  --background: oklch(0.145 0 0);\n  --foreground: oklch(0.985 0 0);\n  --card: oklch(0.205 0 0);\n  --card-foreground: oklch(0.985 0 0);\n  --popover: oklch(0.205 0 0);\n  --popover-foreground: oklch(0.985 0 0);\n  --primary: oklch(0.62 0.19 260);\n  --primary-foreground: oklch(0.97 0.01 255);\n  --secondary: oklch(0.269 0 0);\n  --secondary-foreground: oklch(0.985 0 0);\n  --muted: oklch(0.269 0 0);\n  --muted-foreground: oklch(0.708 0 0);\n  --accent: oklch(0.269 0 0);\n  --accent-foreground: oklch(0.985 0 0);\n  --destructive: oklch(0.704 0.191 22.216);\n  --border: oklch(1 0 0 / 10%);\n  --input: oklch(1 0 0 / 15%);\n  --ring: oklch(0.556 0 0);\n  --chart-1: oklch(0.488 0.243 264.376);\n  --chart-2: oklch(0.696 0.17 162.48);\n  --chart-3: oklch(0.769 0.188 70.08);\n  --chart-4: oklch(0.627 0.265 303.9);\n  --chart-5: oklch(0.645 0.246 16.439);\n  --sidebar: oklch(0.205 0 0);\n  --sidebar-foreground: oklch(0.985 0 0);\n  --sidebar-primary: oklch(0.488 0.243 264.376);\n  --sidebar-primary-foreground: oklch(0.985 0 0);\n  --sidebar-accent: oklch(0.269 0 0);\n  --sidebar-accent-foreground: oklch(0.985 0 0);\n  --sidebar-border: oklch(1 0 0 / 10%);\n  --sidebar-ring: oklch(0.556 0 0);\n}\n@layer base {\n  * {\n    border-color: var(--border);\n    outline-color: var(--ring);\n    @supports (color: color-mix(in lab, red, red)) {\n      outline-color: color-mix(in oklab, var(--ring) 50%, transparent);\n    }\n  }\n  body {\n    background-color: var(--background);\n    color: var(--foreground);\n  }\n}\n@layer utilities {\n  .animate-scrolling-banner {\n    animation: scrolling-banner var(--duration) linear infinite;\n  }\n  .animate-scrolling-banner-vertical {\n    animation: scrolling-banner-vertical var(--duration) linear infinite;\n  }\n}\n@layer base {\n  :root, [data-theme] {\n    color: hsl(var(--heroui-foreground));\n    background-color: hsl(var(--heroui-background));\n  }\n}\n@layer base {\n  :root, [data-theme=light] {\n    color-scheme: light;\n    --heroui-background: 0 0% 100%;\n    --heroui-foreground-50: 0 0% 98.04%;\n    --heroui-foreground-100: 240 4.76% 95.88%;\n    --heroui-foreground-200: 240 5.88% 90%;\n    --heroui-foreground-300: 240 4.88% 83.92%;\n    --heroui-foreground-400: 240 5.03% 64.9%;\n    --heroui-foreground-500: 240 3.83% 46.08%;\n    --heroui-foreground-600: 240 5.2% 33.92%;\n    --heroui-foreground-700: 240 5.26% 26.08%;\n    --heroui-foreground-800: 240 3.7% 15.88%;\n    --heroui-foreground-900: 240 5.88% 10%;\n    --heroui-foreground: 201.81999999999994 24.44% 8.82%;\n    --heroui-divider: 0 0% 6.67%;\n    --heroui-focus: 212.01999999999998 100% 46.67%;\n    --heroui-overlay: 0 0% 0%;\n    --heroui-content1: 0 0% 100%;\n    --heroui-content1-foreground: 201.81999999999994 24.44% 8.82%;\n    --heroui-content2: 240 4.76% 95.88%;\n    --heroui-content2-foreground: 240 3.7% 15.88%;\n    --heroui-content3: 240 5.88% 90%;\n    --heroui-content3-foreground: 240 5.26% 26.08%;\n    --heroui-content4: 240 4.88% 83.92%;\n    --heroui-content4-foreground: 240 5.2% 33.92%;\n    --heroui-default-50: 0 0% 98.04%;\n    --heroui-default-100: 240 4.76% 95.88%;\n    --heroui-default-200: 240 5.88% 90%;\n    --heroui-default-300: 240 4.88% 83.92%;\n    --heroui-default-400: 240 5.03% 64.9%;\n    --heroui-default-500: 240 3.83% 46.08%;\n    --heroui-default-600: 240 5.2% 33.92%;\n    --heroui-default-700: 240 5.26% 26.08%;\n    --heroui-default-800: 240 3.7% 15.88%;\n    --heroui-default-900: 240 5.88% 10%;\n    --heroui-default-foreground: 0 0% 0%;\n    --heroui-default: 240 4.88% 83.92%;\n    --heroui-primary-50: 212.5 92.31% 94.9%;\n    --heroui-primary-100: 211.84000000000003 92.45% 89.61%;\n    --heroui-primary-200: 211.84000000000003 92.45% 79.22%;\n    --heroui-primary-300: 212.24 92.45% 68.82%;\n    --heroui-primary-400: 212.14 92.45% 58.43%;\n    --heroui-primary-500: 212.01999999999998 100% 46.67%;\n    --heroui-primary-600: 212.14 100% 38.43%;\n    --heroui-primary-700: 212.24 100% 28.82%;\n    --heroui-primary-800: 211.84000000000003 100% 19.22%;\n    --heroui-primary-900: 211.84000000000003 100% 9.61%;\n    --heroui-primary-foreground: 0 0% 100%;\n    --heroui-primary: 212.01999999999998 100% 46.67%;\n    --heroui-secondary-50: 270 61.54% 94.9%;\n    --heroui-secondary-100: 270 59.26% 89.41%;\n    --heroui-secondary-200: 270 59.26% 78.82%;\n    --heroui-secondary-300: 270 59.26% 68.24%;\n    --heroui-secondary-400: 270 59.26% 57.65%;\n    --heroui-secondary-500: 270 66.67% 47.06%;\n    --heroui-secondary-600: 270 66.67% 37.65%;\n    --heroui-secondary-700: 270 66.67% 28.24%;\n    --heroui-secondary-800: 270 66.67% 18.82%;\n    --heroui-secondary-900: 270 66.67% 9.41%;\n    --heroui-secondary-foreground: 0 0% 100%;\n    --heroui-secondary: 270 66.67% 47.06%;\n    --heroui-success-50: 146.66999999999996 64.29% 94.51%;\n    --heroui-success-100: 145.71000000000004 61.4% 88.82%;\n    --heroui-success-200: 146.2 61.74% 77.45%;\n    --heroui-success-300: 145.78999999999996 62.57% 66.47%;\n    --heroui-success-400: 146.01 62.45% 55.1%;\n    --heroui-success-500: 145.96000000000004 79.46% 43.92%;\n    --heroui-success-600: 146.01 79.89% 35.1%;\n    --heroui-success-700: 145.78999999999996 79.26% 26.47%;\n    --heroui-success-800: 146.2 79.78% 17.45%;\n    --heroui-success-900: 145.71000000000004 77.78% 8.82%;\n    --heroui-success-foreground: 0 0% 0%;\n    --heroui-success: 145.96000000000004 79.46% 43.92%;\n    --heroui-warning-50: 54.55000000000001 91.67% 95.29%;\n    --heroui-warning-100: 37.139999999999986 91.3% 90.98%;\n    --heroui-warning-200: 37.139999999999986 91.3% 81.96%;\n    --heroui-warning-300: 36.95999999999998 91.24% 73.14%;\n    --heroui-warning-400: 37.00999999999999 91.26% 64.12%;\n    --heroui-warning-500: 37.02999999999997 91.27% 55.1%;\n    --heroui-warning-600: 37.00999999999999 74.22% 44.12%;\n    --heroui-warning-700: 36.95999999999998 73.96% 33.14%;\n    --heroui-warning-800: 37.139999999999986 75% 21.96%;\n    --heroui-warning-900: 37.139999999999986 75% 10.98%;\n    --heroui-warning-foreground: 0 0% 0%;\n    --heroui-warning: 37.02999999999997 91.27% 55.1%;\n    --heroui-danger-50: 339.13 92% 95.1%;\n    --heroui-danger-100: 340 91.84% 90.39%;\n    --heroui-danger-200: 339.3299999999999 90% 80.39%;\n    --heroui-danger-300: 339.11 90.6% 70.78%;\n    --heroui-danger-400: 339 90% 60.78%;\n    --heroui-danger-500: 339.20000000000005 90.36% 51.18%;\n    --heroui-danger-600: 339 86.54% 40.78%;\n    --heroui-danger-700: 339.11 85.99% 30.78%;\n    --heroui-danger-800: 339.3299999999999 86.54% 20.39%;\n    --heroui-danger-900: 340 84.91% 10.39%;\n    --heroui-danger-foreground: 0 0% 100%;\n    --heroui-danger: 339.20000000000005 90.36% 51.18%;\n    --heroui-divider-weight: 1px;\n    --heroui-disabled-opacity: .5;\n    --heroui-font-size-tiny: 0.75rem;\n    --heroui-font-size-small: 0.875rem;\n    --heroui-font-size-medium: 1rem;\n    --heroui-font-size-large: 1.125rem;\n    --heroui-line-height-tiny: 1rem;\n    --heroui-line-height-small: 1.25rem;\n    --heroui-line-height-medium: 1.5rem;\n    --heroui-line-height-large: 1.75rem;\n    --heroui-radius-small: 8px;\n    --heroui-radius-medium: 12px;\n    --heroui-radius-large: 14px;\n    --heroui-border-width-small: 1px;\n    --heroui-border-width-medium: 2px;\n    --heroui-border-width-large: 3px;\n    --heroui-box-shadow-small: 0px 0px 5px 0px rgb(0 0 0 / 0.02), 0px 2px 10px 0px rgb(0 0 0 / 0.06), 0px 0px 1px 0px rgb(0 0 0 / 0.3);\n    --heroui-box-shadow-medium: 0px 0px 15px 0px rgb(0 0 0 / 0.03), 0px 2px 30px 0px rgb(0 0 0 / 0.08), 0px 0px 1px 0px rgb(0 0 0 / 0.3);\n    --heroui-box-shadow-large: 0px 0px 30px 0px rgb(0 0 0 / 0.04), 0px 30px 60px 0px rgb(0 0 0 / 0.12), 0px 0px 1px 0px rgb(0 0 0 / 0.3);\n    --heroui-hover-opacity: .8;\n  }\n}\n@property --tw-translate-x {\n  syntax: \"*\";\n  inherits: false;\n  initial-value: 0;\n}\n@property --tw-translate-y {\n  syntax: \"*\";\n  inherits: false;\n  initial-value: 0;\n}\n@property --tw-translate-z {\n  syntax: \"*\";\n  inherits: false;\n  initial-value: 0;\n}\n@property --tw-scale-x {\n  syntax: \"*\";\n  inherits: false;\n  initial-value: 1;\n}\n@property --tw-scale-y {\n  syntax: \"*\";\n  inherits: false;\n  initial-value: 1;\n}\n@property --tw-scale-z {\n  syntax: \"*\";\n  inherits: false;\n  initial-value: 1;\n}\n@property --tw-rotate-x {\n  syntax: \"*\";\n  inherits: false;\n  initial-value: rotateX(0);\n}\n@property --tw-rotate-y {\n  syntax: \"*\";\n  inherits: false;\n  initial-value: rotateY(0);\n}\n@property --tw-rotate-z {\n  syntax: \"*\";\n  inherits: false;\n  initial-value: rotateZ(0);\n}\n@property --tw-skew-x {\n  syntax: \"*\";\n  inherits: false;\n  initial-value: skewX(0);\n}\n@property --tw-skew-y {\n  syntax: \"*\";\n  inherits: false;\n  initial-value: skewY(0);\n}\n@property --tw-scroll-snap-strictness {\n  syntax: \"*\";\n  inherits: false;\n  initial-value: proximity;\n}\n@property --tw-space-y-reverse {\n  syntax: \"*\";\n  inherits: false;\n  initial-value: 0;\n}\n@property --tw-space-x-reverse {\n  syntax: \"*\";\n  inherits: false;\n  initial-value: 0;\n}\n@property --tw-divide-y-reverse {\n  syntax: \"*\";\n  inherits: false;\n  initial-value: 0;\n}\n@property --tw-border-style {\n  syntax: \"*\";\n  inherits: false;\n  initial-value: solid;\n}\n@property --tw-gradient-position {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-gradient-from {\n  syntax: \"<color>\";\n  inherits: false;\n  initial-value: #0000;\n}\n@property --tw-gradient-via {\n  syntax: \"<color>\";\n  inherits: false;\n  initial-value: #0000;\n}\n@property --tw-gradient-to {\n  syntax: \"<color>\";\n  inherits: false;\n  initial-value: #0000;\n}\n@property --tw-gradient-stops {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-gradient-via-stops {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-gradient-from-position {\n  syntax: \"<length-percentage>\";\n  inherits: false;\n  initial-value: 0%;\n}\n@property --tw-gradient-via-position {\n  syntax: \"<length-percentage>\";\n  inherits: false;\n  initial-value: 50%;\n}\n@property --tw-gradient-to-position {\n  syntax: \"<length-percentage>\";\n  inherits: false;\n  initial-value: 100%;\n}\n@property --tw-leading {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-font-weight {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-tracking {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-ordinal {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-slashed-zero {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-numeric-figure {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-numeric-spacing {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-numeric-fraction {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-shadow {\n  syntax: \"*\";\n  inherits: false;\n  initial-value: 0 0 #0000;\n}\n@property --tw-shadow-color {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-shadow-alpha {\n  syntax: \"<percentage>\";\n  inherits: false;\n  initial-value: 100%;\n}\n@property --tw-inset-shadow {\n  syntax: \"*\";\n  inherits: false;\n  initial-value: 0 0 #0000;\n}\n@property --tw-inset-shadow-color {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-inset-shadow-alpha {\n  syntax: \"<percentage>\";\n  inherits: false;\n  initial-value: 100%;\n}\n@property --tw-ring-color {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-ring-shadow {\n  syntax: \"*\";\n  inherits: false;\n  initial-value: 0 0 #0000;\n}\n@property --tw-inset-ring-color {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-inset-ring-shadow {\n  syntax: \"*\";\n  inherits: false;\n  initial-value: 0 0 #0000;\n}\n@property --tw-ring-inset {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-ring-offset-width {\n  syntax: \"<length>\";\n  inherits: false;\n  initial-value: 0px;\n}\n@property --tw-ring-offset-color {\n  syntax: \"*\";\n  inherits: false;\n  initial-value: #fff;\n}\n@property --tw-ring-offset-shadow {\n  syntax: \"*\";\n  inherits: false;\n  initial-value: 0 0 #0000;\n}\n@property --tw-outline-style {\n  syntax: \"*\";\n  inherits: false;\n  initial-value: solid;\n}\n@property --tw-blur {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-brightness {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-contrast {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-grayscale {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-hue-rotate {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-invert {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-opacity {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-saturate {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-sepia {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-drop-shadow {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-drop-shadow-color {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-drop-shadow-alpha {\n  syntax: \"<percentage>\";\n  inherits: false;\n  initial-value: 100%;\n}\n@property --tw-drop-shadow-size {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-backdrop-blur {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-backdrop-brightness {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-backdrop-contrast {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-backdrop-grayscale {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-backdrop-hue-rotate {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-backdrop-invert {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-backdrop-opacity {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-backdrop-saturate {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-backdrop-sepia {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-duration {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-ease {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-content {\n  syntax: \"*\";\n  initial-value: \"\";\n  inherits: false;\n}\n@keyframes spin {\n  to {\n    transform: rotate(360deg);\n  }\n}\n@keyframes pulse {\n  50% {\n    opacity: 0.5;\n  }\n}\n@keyframes enter {\n  from {\n    opacity: var(--tw-enter-opacity,1);\n    transform: translate3d(var(--tw-enter-translate-x,0),var(--tw-enter-translate-y,0),0)scale3d(var(--tw-enter-scale,1),var(--tw-enter-scale,1),var(--tw-enter-scale,1))rotate(var(--tw-enter-rotate,0));\n  }\n}\n@keyframes exit {\n  to {\n    opacity: var(--tw-exit-opacity,1);\n    transform: translate3d(var(--tw-exit-translate-x,0),var(--tw-exit-translate-y,0),0)scale3d(var(--tw-exit-scale,1),var(--tw-exit-scale,1),var(--tw-exit-scale,1))rotate(var(--tw-exit-rotate,0));\n  }\n}\n@keyframes accordion-down {\n  from {\n    height: 0;\n  }\n  to {\n    height: var(--radix-accordion-content-height,var(--bits-accordion-content-height));\n  }\n}\n@keyframes accordion-up {\n  from {\n    height: var(--radix-accordion-content-height,var(--bits-accordion-content-height));\n  }\n  to {\n    height: 0;\n  }\n}\n@keyframes caret-blink {\n  0%,70%,100% {\n    opacity: 1;\n  }\n  20%,50% {\n    opacity: 0;\n  }\n}\n@keyframes accordion-down {\n  from {\n    height: 0;\n  }\n  to {\n    height: var(--radix-accordion-content-height);\n  }\n}\n@keyframes accordion-up {\n  from {\n    height: var(--radix-accordion-content-height);\n  }\n  to {\n    height: 0;\n  }\n}\n@keyframes scrolling-banner {\n  from {\n    transform: translateX(0);\n  }\n  to {\n    transform: translateX(calc(-50% - var(--gap)/2));\n  }\n}\n@keyframes scrolling-banner-vertical {\n  from {\n    transform: translateY(0);\n  }\n  to {\n    transform: translateY(calc(-50% - var(--gap)/2));\n  }\n}\n@keyframes shimmer {\n  100% {\n    transform: translateX(200%);\n  }\n}\n@keyframes spinner-spin {\n  0% {\n    transform: rotate(0deg);\n  }\n  100% {\n    transform: rotate(360deg);\n  }\n}\n@keyframes drip-expand {\n  0% {\n    opacity: 0.2;\n    transform: scale(0);\n  }\n  100% {\n    opacity: 0;\n    transform: scale(2);\n  }\n}\n@keyframes appearance-in {\n  0% {\n    opacity: 0;\n    transform: translateZ(0)  scale(0.95);\n  }\n  60% {\n    opacity: 0.75;\n    backface-visibility: hidden;\n    webkit-font-smoothing: antialiased;\n    transform: translateZ(0) scale(1.05);\n  }\n  100% {\n    opacity: 1;\n    transform: translateZ(0) scale(1);\n  }\n}\n@keyframes indeterminate-bar {\n  0% {\n    transform: translateX(-50%) scaleX(0.2);\n  }\n  100% {\n    transform: translateX(100%) scaleX(1);\n  }\n}\n@keyframes sway {\n  0% {\n    transform: translate(0px, 0px);\n  }\n  50% {\n    transform: translate(0px, -150%);\n  }\n  100% {\n    transform: translate(0px, 0px);\n  }\n}\n@keyframes blink {\n  0% {\n    opacity: 0.2;\n  }\n  20% {\n    opacity: 1;\n  }\n  100% {\n    opacity: 0.2;\n  }\n}\n@keyframes fade-out {\n  0% {\n    opacity: 1;\n  }\n  100% {\n    opacity: 0.15;\n  }\n}\n@layer properties {\n  @supports ((-webkit-hyphens: none) and (not (margin-trim: inline))) or ((-moz-orient: inline) and (not (color:rgb(from red r g b)))) {\n    *, ::before, ::after, ::backdrop {\n      --tw-translate-x: 0;\n      --tw-translate-y: 0;\n      --tw-translate-z: 0;\n      --tw-scale-x: 1;\n      --tw-scale-y: 1;\n      --tw-scale-z: 1;\n      --tw-rotate-x: rotateX(0);\n      --tw-rotate-y: rotateY(0);\n      --tw-rotate-z: rotateZ(0);\n      --tw-skew-x: skewX(0);\n      --tw-skew-y: skewY(0);\n      --tw-scroll-snap-strictness: proximity;\n      --tw-space-y-reverse: 0;\n      --tw-space-x-reverse: 0;\n      --tw-divide-y-reverse: 0;\n      --tw-border-style: solid;\n      --tw-gradient-position: initial;\n      --tw-gradient-from: #0000;\n      --tw-gradient-via: #0000;\n      --tw-gradient-to: #0000;\n      --tw-gradient-stops: initial;\n      --tw-gradient-via-stops: initial;\n      --tw-gradient-from-position: 0%;\n      --tw-gradient-via-position: 50%;\n      --tw-gradient-to-position: 100%;\n      --tw-leading: initial;\n      --tw-font-weight: initial;\n      --tw-tracking: initial;\n      --tw-ordinal: initial;\n      --tw-slashed-zero: initial;\n      --tw-numeric-figure: initial;\n      --tw-numeric-spacing: initial;\n      --tw-numeric-fraction: initial;\n      --tw-shadow: 0 0 #0000;\n      --tw-shadow-color: initial;\n      --tw-shadow-alpha: 100%;\n      --tw-inset-shadow: 0 0 #0000;\n      --tw-inset-shadow-color: initial;\n      --tw-inset-shadow-alpha: 100%;\n      --tw-ring-color: initial;\n      --tw-ring-shadow: 0 0 #0000;\n      --tw-inset-ring-color: initial;\n      --tw-inset-ring-shadow: 0 0 #0000;\n      --tw-ring-inset: initial;\n      --tw-ring-offset-width: 0px;\n      --tw-ring-offset-color: #fff;\n      --tw-ring-offset-shadow: 0 0 #0000;\n      --tw-outline-style: solid;\n      --tw-blur: initial;\n      --tw-brightness: initial;\n      --tw-contrast: initial;\n      --tw-grayscale: initial;\n      --tw-hue-rotate: initial;\n      --tw-invert: initial;\n      --tw-opacity: initial;\n      --tw-saturate: initial;\n      --tw-sepia: initial;\n      --tw-drop-shadow: initial;\n      --tw-drop-shadow-color: initial;\n      --tw-drop-shadow-alpha: 100%;\n      --tw-drop-shadow-size: initial;\n      --tw-backdrop-blur: initial;\n      --tw-backdrop-brightness: initial;\n      --tw-backdrop-contrast: initial;\n      --tw-backdrop-grayscale: initial;\n      --tw-backdrop-hue-rotate: initial;\n      --tw-backdrop-invert: initial;\n      --tw-backdrop-opacity: initial;\n      --tw-backdrop-saturate: initial;\n      --tw-backdrop-sepia: initial;\n      --tw-duration: initial;\n      --tw-ease: initial;\n      --tw-content: \"\";\n    }\n  }\n}\r\n"], "names": [], "mappings": "AACA;EAkseE;IACE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAlseJ;EAEE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAFF;EA0IE;;;;;;;EAMA;;;;;;;;;;EASA;;;;;;EAKA;;;;EAIA;;;;;EAIA;;;;;;EAKA;;;;EAGA;;;;;;;EAMA;;;;EAGA;;;;;;;EAMA;;;;EAGA;;;;EAGA;;;;;;EAKA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;;EAIA;;;;;EAIA;;;;;;;;;;;EAUA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;IACE;;;;IAEE;MAAgD;;;;;;EAKpD;;;;EAGA;;;;EAGA;;;;;EAIA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAuzcA;;;;;EAGE;IAAgD;;;;;EAIlD;;;;;EAcA;;;;;EAMA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAxmdF;;AAAA;EA2RE;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;;;;;;;;;EAWA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAEE;IAAyB;;;;;EAGzB;IAAyB;;;;;EAGzB;IAAyB;;;;;EAGzB;IAAyB;;;;;EAGzB;IAAyB;;;;;EAI3B;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;;;;EAMA;;;;;;;EAMA;;;;;;;EAMA;;;;;EAGE;;;;EAIF;;;;;EAGE;;;;EAIF;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAMA;;;;EAMA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;;;EAMA;;;;;;;EAMA;;;;;;;EAMA;;;;;;;EAMA;;;;;;;EAMA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;;EAIA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAIE;;;;;;EAOA;;;;;;EAOA;;;;;;EAOA;;;;;;EAOA;;;;;;EAOA;;;;;;EAOA;;;;;;EAOA;;;;;;EAOA;;;;;;EAMF;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAIE;;;;;;EAOA;;;;;;EAOA;;;;;;EAOA;;;;;;EAOA;;;;;;EAOA;;;;;;EAMF;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAIE;;;;;;;;EAQF;;;;EAGA;;;;EAGA;;;;EAGA;;;;;;EAKA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;EAGA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAGA;;;;EAGA;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAGA;;;;EAGA;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAGA;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAEE;IAAgD;;;;;EAIlD;;;;;EAIA;;;;;EAIA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;;EAKA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;EAGA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAGA;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;;EAIA;;;;EAGA;;;;EAGA;;;;EAGA;;;;;EAIA;;;;;EAIA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;EAsHA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;EAsHA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;;EAIA;;;;EAGA;;;;;EAGE;IAAgC;;;;;;EAKlC;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;;EAKA;;;;;EAIA;;;;;EAIA;;;;EAGA;;;;;EAKA;;;;;EAKA;;;;;EAKA;;;;;EAKA;;;;;EAKA;;;;;EAKA;;;;;EAKA;;;;;;EAKA;;;;;;EAKA;;;;;;EAKA;;;;;;EAKA;;;;;;EAKA;;;;;;EAKA;;;;;;EAKA;;;;;;EAKA;;;;;;EAKA;;;;;;EAKA;;;;;;EAKA;;;;;;EAKA;;;;;;EAKA;;;;;;EAKA;;;;;;EAKA;;;;;;EAKA;;;;;;EAKA;;;;;;EAKA;;;;;;EAKA;;;;;;EAKA;;;;;;EAKA;;;;;;EAKA;;;;;;EAKA;;;;;;EAKA;;;;;;EAKA;;;;;;EAKA;;;;EAGA;;;;EAGA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;EAGA;;;;EAGA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;EAIA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAMA;;;;EAIE;;;;EAIF;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAIE;;;;EAME;IAAuB;;;;;EAOvB;IAAuB;;;;;EAOvB;IAAuB;;;;;EAOvB;IAAuB;;;;;;;;EAUvB;IAAuB;;;;;EAOvB;IAAuB;;;;;EAOvB;IAAuB;;;;;EAOvB;IAAuB;;;;;EAOvB;IAAuB;;;;;EAMzB;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;;EAMA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;;EAMA;;;;;;;EAQA;;;;;;;EAQA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;;;;EAQA;;;;EAKA;;;;EAKA;;;;EAKA;;;;;;;EAQA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;;EAMA;;;;EAKA;;;;;EAMA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;;;;EAQA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;;;;EAQA;;;;EAME;;;;EAOA;;;;EAOA;;;;EAMF;;;;EAKA;;;;EAKA;;;;EAKA;;;;;EAMA;;;;;;;EAQA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;;EAMA;;;;EAKA;;;;EAKA;;;;;EAMA;;;;EAKA;;;;EAKA;;;;;EAMA;;;;EAKA;;;;;EAMA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;;EAMA;;;;EAKA;;;;EAKA;;;;;EAMA;;;;;EAOE;IAAuB;;;;;EAMzB;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;;;;EAQA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAQA;;;;EAQA;;;;EAKA;;;;EAKA;;;;EAKA;;;;;EAMA;;;;EAKA;;;;;EAMA;;;;;EAMA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;;EAMA;;;;;EAMA;;;;;EAMA;;;;;EAMA;;;;;EAMA;;;;;EAMA;;;;;EAMA;;;;;EAMA;;;;;EAMA;;;;;EAMA;;;;;EAMA;;;;;EAMA;;;;;EAMA;;;;;EAMA;;;;;EAMA;;;;;EAMA;;;;;EAMA;;;;;EAMA;;;;;EAMA;;;;;EAMA;;;;;EAMA;;;;;EAMA;;;;;EAMA;;;;;EAMA;;;;;;EAOA;;;;;;EAOA;;;;;EAMA;;;;;EAMA;;;;;EAMA;;;;;EAMA;;;;;EAMA;;;;;EAMA;;;;;EAMA;;;;;EAMA;;;;;EAMA;;;;;EAMA;;;;;;EAOA;;;;;;EAOA;;;;;;EAOA;;;;;EAGE;IAAgD;;;;;EAMlD;;;;;EAMA;;;;;EAMA;;;;;EAMA;;;;;EAMA;;;;;EAMA;;;;;EAGE;IAAgD;;;;;EAMlD;;;;;EAGE;IAAgD;;;;;EAMlD;;;;;EAMA;;;;;EAMA;;;;;EAGE;IAAgD;;;;;EAMlD;;;;;EAMA;;;;;EAGE;IAAgD;;;;;EAMlD;;;;;EAMA;;;;;EAGE;IAAgD;;;;;EAMlD;;;;;EAMA;;;;;EAMA;;;;;EAGE;IAAgD;;;;;EAMlD;;;;;;EAOA;;;;;;EAOA;;;;;;;EAQA;;;;;;EAOA;;;;;EAMA;;;;;EAMA;;;;;;EAOA;;;;;;;EAQA;;;;;;;EAQA;;;;;;;EAQA;;;;;EAMA;;;;;;EAOA;;;;;;EAQE;;;;;EASE;;;;;EAUA;;;;;EASF;;;;;EAQA;;;;;EAQA;;;;;EAQA;;;;;EAQA;;;;;;EASA;;;;;EAQA;;;;;EAOF;;;;;EAMA;;;;;EAMA;;;;;EAMA;;;;;EAMA;;;;;EAMA;;;;;EAMA;;;;;EAMA;;;;;EAMA;;;;;EAMA;;;;;EAMA;;;;;EAMA;;;;;EAMA;;;;;EAMA;;;;;EAMA;;;;;EAMA;;;;;EAMA;;;;;EAMA;;;;;EAMA;;;;;EAMA;;;;;EAMA;;;;;EAMA;;;;;EAMA;;;;;EAMA;;;;;EAMA;;;;;EAMA;;;;;EAMA;;;;;EAMA;;;;;EAMA;;;;;EAMA;;;;;EAMA;;;;;EAMA;;;;;EAMA;;;;;EAMA;;;;;EAMA;;;;;EAMA;;;;;EAMA;;;;;EAMA;;;;;EAMA;;;;;;EAOA;;;;;;EAOA;;;;;;;;EASA;;;;;EAMA;;;;;EAMA;;;;;EAMA;;;;;EAMA;;;;;EAMA;;;;;EAMA;;;;;EAMA;;;;;EAMA;;;;;EAMA;;;;;EAMA;;;;;EAMA;;;;;;EAOA;;;;;EAMA;;;;;EAMA;;;;;EAMA;;;;;EAMA;;;;;EAMA;;;;;EAMA;;;;;EAMA;;;;;EAMA;;;;;EAMA;;;;;EAMA;;;;;EAMA;;;;;EAMA;;;;;EAMA;;;;;EAMA;;;;;EAMA;;;;;EAMA;;;;;EAMA;;;;;EAMA;;;;;EAMA;;;;;EAMA;;;;;EAMA;;;;;EAMA;;;;;EAMA;;;;;EAMA;;;;;EAMA;;;;;;EAOA;;;;;;EAOA;;;;;;;EAQA;;;;;;;EAQA;;;;;;;EAQA;;;;;;;EAQA;;;;;;;EAQA;;;;;;;EAQA;;;;;EAMA;;;;;;EAOA;;;;;;EAOA;;;;;;EAOA;;;;;;EAOA;;;;;;EAQE;;;;;EAQA;;;;;EAQA;;;;;EAQA;;;;;;EASA;;;;;EAQA;;;;;EAQA;;;;;EAQA;;;;;;EASA;;;;;;;;EAWA;;;;;EAOF;;;;EAKA;;;;EAKA;;;;;EAMA;;;;;EAMA;;;;;EAMA;;;;;EAMA;;;;;EAMA;;;;;EAMA;;;;;EAMA;;;;;EAOE;;;;;;EAUE;;;;;EAUA;;;;;EAQJ;;;;;EAMA;;;;;EAMA;;;;;EAMA;;;;;EAMA;;;;;EAMA;;;;;EAMA;;;;;EAMA;;;;;EAMA;;;;;EAOE;;;;;;EAUE;;;;;EAUA;;;;;EAQJ;;;;;EAMA;;;;;EAMA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;;EAMA;;;;EAKA;;;;;EAMA;;;;;EAOE;;;;;EAQA;IAAuB;;;;;;EAQvB;IAAuB;;;;;;;;EAUvB;IAAuB;;;;;;;;EAUvB;IAAuB;;;;;;;;EAUvB;IAAuB;;;;;EAOvB;IAAuB;;;;;EAOvB;IAAuB;;;;;EAOvB;IAAuB;;;;;EAOvB;IAAuB;;;;;EAOvB;IAAuB;;;;;EAOvB;IAAuB;;;;;EAOvB;IAAuB;;;;;EAOvB;IAAuB;;;;;EAOvB;IAAuB;;;;;EAOvB;IAAuB;;;;IAErB;MAAgD;;;;;;EAQlD;IAAuB;;;;IAErB;MAAgD;;;;;;EAQlD;IAAuB;;;;;EAOvB;IAAuB;;;;IAErB;MAAgD;;;;;;EAQlD;IAAuB;;;;;EAOvB;IAAuB;;;;;EAOvB;IAAuB;;;;;EAOvB;IAAuB;;;;;EAOvB;IAAuB;;;;;EAOvB;IAAuB;;;;IAErB;MAAgD;;;;;;EAQlD;IAAuB;;;;IAErB;MAAgD;;;;;;EAQlD;IAAuB;;;;;EAOvB;IAAuB;;;;;EAOvB;IAAuB;;;;IAErB;MAAgD;;;;;;EAQlD;IAAuB;;;;;EAOvB;IAAuB;;;;;EAOvB;IAAuB;;;;IAErB;MAAgD;;;;;;EAQlD;IAAuB;;;;;EAOvB;IAAuB;;;;;EAOvB;IAAuB;;;;;EAOvB;IAAuB;;;;IAErB;MAAgD;;;;;;EAQlD;IAAuB;;;;;EAOvB;IAAuB;;;;IAErB;MAAgD;;;;;;EAQlD;IAAuB;;;;IAErB;MAAgD;;;;;;EAQlD;IAAuB;;;;;EAOvB;IAAuB;;;;;EAOvB;IAAuB;;;;IAErB;MAAgD;;;;;;EAQlD;IAAuB;;;;;EAOvB;IAAuB;;;;;EAOvB;IAAuB;;;;;EAOvB;IAAuB;;;;IAErB;MAAgD;;;;;;EAQlD;IAAuB;;;;;EAOvB;IAAuB;;;;;EAOvB;IAAuB;;;;;EAOvB;IAAuB;;;;;EAOvB;IAAuB;;;;IAErB;MAAgD;;;;;;EAQlD;IAAuB;;;;;EAOvB;IAAuB;;;;IAErB;MAAgD;;;;;;EAQlD;IAAuB;;;;;EAOvB;IAAuB;;;;;EAOvB;IAAuB;;;;;EAOvB;IAAuB;;;;;EAOvB;IAAuB;;;;;EAOvB;IAAuB;;;;IAErB;MAAgD;;;;;;EAQlD;IAAuB;;;;;EAOvB;IAAuB;;;;;EAOvB;IAAuB;;;;;EAOvB;IAAuB;;;;;EAOvB;IAAuB;;;;;EAOvB;IAAuB;;;;;EAOvB;IAAuB;;;;;EAOvB;IAAuB;;;;;EAOvB;IAAuB;;;;;EAOvB;IAAuB;;;;;EAOvB;IAAuB;;;;;EAOvB;IAAuB;;;;;EAOvB;IAAuB;;;;;EAOvB;IAAuB;;;;IAErB;MAAgD;;;;;;EAQlD;IAAuB;;;;;EAOvB;IAAuB;;;;;EAOvB;IAAuB;;;;;EAOvB;IAAuB;;;;;EAOvB;IAAuB;;;;;EAOvB;IAAuB;;;;;EAOvB;IAAuB;;;;;EAOvB;IAAuB;;;;;EAOvB;IAAuB;;;;;EAOvB;IAAuB;;;;;EAOvB;IAAuB;;;;;;EAQvB;IAAuB;;;;;;EAQvB;IAAuB;;;;;;EAQvB;IAAuB;;;;;;EAQvB;IACE;;;;;EASA;IAAuB;;;;;EASvB;IAAuB;;;;;EAQzB;IACE;;;;;IAGE;MAAgD;;;;;;EASpD;IACE;;;;;IAGE;MAAgD;;;;;;EASpD;IACE;;;;;IAGE;MAAgD;;;;;;EASpD;IACE;;;;;IAGE;MAAgD;;;;;;EASpD;IACE;;;;;;EASF;IACE;;;;;IAGE;MAAgD;;;;;;EASpD;IACE;;;;;IAGE;MAAgD;;;;;;EASpD;IACE;;;;;;EAUA;IAAuB;;;;;EASvB;IAAuB;;;;;EASvB;IAAuB;;;;;EASvB;IAAuB;;;;;EASvB;IAAuB;;;;;EASvB;IAAuB;;;;;EASvB;IAAuB;;;;;EAUrB;IAAuB;;;;;EAWvB;IAAuB;;;;;EAQ7B;;;;EAKA;;;;EAKA;;;;EAEE;IAAgD;;;;;EAMlD;;;;EAEE;IAAgD;;;;;EAMlD;;;;EAKA;;;;EAEE;IAAgD;;;;;EAMlD;;;;EAEE;IAAgD;;;;;EAMlD;;;;EAEE;IAAgD;;;;;EAMlD;;;;EAKA;;;;EAEE;IAAgD;;;;;EAMlD;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;;EAMA;;;;;EAMA;;;;EAKA;;;;;EAMA;;;;;EAGE;IAAgC;;;;;;EAOlC;;;;;EAMA;;;;EAKA;;;;;EAMA;;;;EAKA;;;;EAKA;;;;;EAMA;;;;;EAMA;;;;;EAMA;;;;;EAMA;;;;;EAMA;;;;EAEE;IAAgD;;;;;EAMlD;;;;EAKA;;;;EAKA;;;;EAEE;IAAgD;;;;;EAMlD;;;;;EAMA;;;;;EAMA;;;;;EAGE;IAAgC;;;;;;EAOlC;;;;;EAMA;;;;;EAMA;;;;EAKA;;;;EAKA;;;;EAKA;;;;;EAMA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAOI;IAAuB;;;;;;;;EAU3B;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAEE;IAAgD;;;;;EAMlD;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;;;;EAQA;;;;;;;EAQA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAEE;IAAgD;;;;;EAMlD;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;;EAMA;;;;;EAMA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;;EAMA;;;;;EAMA;;;;EAEE;IAAgD;;;;;EAMlD;;;;EAEE;IAAgD;;;;;EAMlD;;;;EAEE;IAAgD;;;;;EAMlD;;;;EAEE;IAAgD;;;;;EAMlD;;;;EAEE;IAAgD;;;;;EAMlD;;;;EAEE;IAAgD;;;;;EAMlD;;;;EAEE;IAAgD;;;;;EAOhD;;;;;EAQA;;;;;EASE;IAAuB;;;;;EAQzB;;;;EAOA;;;;EAOA;;;;EAEE;IAAgD;;;;;EAOpD;;;;EAME;;;;;EAOF;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAME;;;;;;;;EAWA;;;;;;;;EAUF;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAME;;;;EAOA;;;;EAOA;;;;EAOA;;;;EAOA;;;;EAOA;;;;EAOA;;;;EAOA;;;;EAMF;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;;EAMA;;;;;EAMA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAME;;;;;EAOF;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;;EAGE;;;;EAMF;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;;EAMA;;;;;EAMA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAEE;IAAgD;;;;;EAMlD;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAEE;IAAgD;;;;;EAMlD;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAEE;IAAgD;;;;;EAMlD;;;;EAKA;;;;EAKA;;;;EAEE;IAAgD;;;;;EAMlD;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAEE;IAAgD;;;;;EAMlD;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAEE;IAAgD;;;;;EAMlD;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;;EAMA;;;;EAEE;IAAgD;;;;;EAMlD;;;;EAEE;IAAgD;;;;;EAMlD;;;;EAEE;IAAgD;;;;;EAMlD;;;;EAEE;IAAgD;;;;;EAMlD;;;;EAEE;IAAgD;;;;;EAMlD;;;;EAEE;IAAgD;;;;;EAMlD;;;;;;EAOA;;;;EAKA;;;;EAEE;IAAgD;;;;;EAMlD;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAME;;;;EAEE;IAAgD;;;;;EAQlD;;;;EAQE;;;;EAOJ;;;;;;;EAQA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAME;;;;;EAQA;;;;;EAQA;;;;;EAQA;;;;;EAOF;;;;;EAMA;;;;;EAOA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;;;EAOA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAME;;;;;EAOF;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAME;;;;;EAQA;;;;EAMF;;;;EAKA;;;;EAKA;;;;EAME;;;;;EAQA;;;;;EAQA;;;;;EAQA;;;;;;EASA;;;;;;EAQF;;;;EAME;;;;EAOA;;;;EAOA;;;;EAOA;;;;EAOA;;;;EAOA;;;;EAOA;;;;;EAQA;;;;;EAQA;;;;;;EAQF;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;;EAMA;;;;EAME;;;;;EAQA;;;;;EAOF;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAME;;;;;EAQA;;;;;EAQA;;;;;EAQA;;;;;EAQA;;;;;;EASA;;;;;EAQA;;;;;EAQA;;;;;EAQA;;;;;EAQA;;;;;EAQA;;;;;EAQA;;;;;;EASA;;;;;EAQA;;;;;EAQA;;;;;EAQA;;;;;EAQA;;;;;EAQA;;;;;EAQA;;;;;;EAQF;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;;EAMA;;;;EAME;;;;;EAQA;;;;;EAOF;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAME;;;;;EAQA;;;;;EAOF;;;;;;;EAQA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAME;;;;;;EASA;;;;;;EAQF;;;;EAKA;;;;EAME;;;;EAOA;;;;EAOA;;;;EAOA;;;;EAOA;;;;EAOA;;;;EAOA;;;;EAOA;;;;EAEE;IAAgD;;;;;EAQlD;;;;EAOA;;;;EAOA;;;;EAEE;IAAgD;;;;;EAQlD;;;;EAOA;;;;EAEE;IAAgD;;;;;EAQlD;;;;EAOA;;;;EAEE;IAAgD;;;;;EAQlD;;;;EAOA;;;;EAEE;IAAgD;;;;;EAQlD;;;;EAOA;;;;EAEE;IAAgD;;;;;EAQlD;;;;EAOA;;;;EAOA;;;;EAOA;;;;EAOA;;;;EAOA;;;;EAOA;;;;EAOA;;;;EAOA;;;;EAOA;;;;EAOA;;;;EAOA;;;;EAOA;;;;EAEE;IAAgD;;;;;EAQlD;;;;EAEE;IAAgD;;;;;EAQlD;;;;EAEE;IAAgD;;;;;EAQlD;;;;EAEE;IAAgD;;;;;EAQlD;;;;EAEE;IAAgD;;;;;EAQlD;;;;EAEE;IAAgD;;;;;EAOpD;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;;EAMA;;;;;EAMA;;;;EAEE;IAAgD;;;;;EAMlD;;;;EAEE;IAAgD;;;;;EAMlD;;;;EAEE;IAAgD;;;;;EAMlD;;;;EAEE;IAAgD;;;;;EAMlD;;;;EAEE;IAAgD;;;;;EAMlD;;;;EAEE;IAAgD;;;;;EAOhD;;;;;EAQA;;;;;EAQA;;;;;EAQA;;;;;EAQA;;;;;EAQA;;;;;EAQA;;;;;EAQA;;;;;EAQA;;;;EAOA;;;;EAOA;;;;EAOA;;;;EAOA;;;;EAOA;;;;EAOA;;;;EAOA;;;;EAOA;;;;EAOA;;;;EAOA;;;;EAOA;;;;EAOA;;;;EAOA;;;;EAOA;;;;EAOA;;;;EAOA;;;;EAOA;;;;EAOA;;;;EAQE;;;;;EAUA;;;;;EAGE;IAAgD;;;;;EAUlD;;;;;EAUA;;;;;EAUA;;;;;EAUA;;;;;EAUA;;;;EASA;;;;EAQF;;;;;;EASA;;;;;EASE;;;;EASA;;;;EASA;;;;EASA;;;;EASA;;;;EASA;;;;EASA;;;;EASA;;;;EASA;;;;EASA;;;;EASA;;;;EASA;;;;EASA;;;;EAQF;;;;;;EASA;;;;;EASE;;;;EASA;;;;EASA;;;;EASA;;;;EASA;;;;EASA;;;;EASA;;;;EASA;;;;EASA;;;;EASA;;;;EASA;;;;EASA;;;;EASA;;;;EAOJ;;;;;EAMA;;;;EAKA;;;;;EAMA;;;;EAKA;;;;;EAMA;;;;EAKA;;;;;EAMA;;;;EAKA;;;;EAKA;;;;EAME;;;;EAEE;IAAgD;;;;;EAQlD;;;;;EAQA;;;;EAOA;;;;EAEE;IAAgD;;;;;EAQlD;;;;EAOA;;;;;EAQA;;;;;EAEE;IAAgD;;;;;EASlD;;;;;EAQA;;;;;EAQA;;;;EAQE;;;;;EAUA;;;;;EASF;;;;;;;EAUA;;;;EAOA;;;;EAOA;;;;EAOA;;;;EAOA;;;;;;EAQF;;;;EAOI;;;;EAQF;;;;EAMF;;;;EAKA;;;;EAKA;;;;;EAMA;;;;;EAMA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;;EAMA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAME;;;;EAOA;;;;EAOA;;;;EAMF;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAEE;IAAgD;;;;;EAMlD;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;;EAMA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAME;;;;EAOA;;;;EAOA;;;;EAQE;IAAuB;;;;;EASvB;IAAuB;;;;;EASvB;IAAuB;;;;;EAQzB;;;;EAMF;;;;EAKA;;;;;EAMA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;;;EAOA;;;;;EAMA;;;;;EAMA;;;;EAME;;;;;EAQA;;;;;EAQA;;;;;EAQA;;;;;EAQA;;;;;EAQA;;;;;EAOF;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAME;;;;EAEE;IAAgD;;;;;EAQlD;;;;EAMF;;;;;EAMA;;;;;EAOE;;;;;EAOF;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;;EAMA;;;;;EAMA;;;;EAKA;;;;EAKA;;;;EAKA;;;;;EAMA;;;;EAKA;;;;EAKA;;;;EAKA;;;;;EAMA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;;EAMA;;;;;EAMA;;;;EAKA;;;;EAKA;;;;EAKA;IAAyC;;;;;;;;EAQzC;IAAyC;;;;;EAKzC;IACE;;;;;;EAOF;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IACE;;;;;EAMF;IACE;;;;;EAMF;IACE;;;;;EAMF;IACE;;;;;EAMF;IACE;;;;;EAMF;IACE;;;;;EAMF;IACE;;;;;EAMF;IACE;;;;;EAMF;IACE;;;;;EAMF;IACE;;;;;EAMF;IACE;;;;;EAMF;IACE;;;;;EAOA;IAAyB;;;;;EAOzB;IAAyB;;;;;EAM3B;IACE;;;;;EAMF;IACE;;;;;EAMF;IAEI;;;;;EAOJ;IAEI;;;;;EAOJ;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;;EAMzB;IAAyB;;;;;EAKzB;IACE;;;;;EAMF;IACE;;;;;EAMF;IACE;;;;;EAMF;IACE;;;;;;EAOF;IAEI;;;;;EAOJ;IACE;;;;;;EAOF;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IACE;;;;;;;EAQF;IAAyB;;;;;EAKzB;IAAyB;;;;;;EAMzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAkC;;;;;;EAMlC;IAAkC;;;;;;EAMlC;IAAkC;;;;;EAKlC;IAAkC;;;;;EAKlC;IAAkC;;;;;EAKlC;IAAkC;;;;;EAKlC;IAAkC;;;;;EAKlC;IAAkC;;;;;EAKlC;IAAkC;;;;;EAKlC;IAAkC;;;;;EAKlC;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAME;;;;EAOA;;;;;EAQA;;;;;EAQA;;;;EAMF;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAEE;IAAgD;;;;;EAMlD;;;;EAKA;;;;EAKA;;;;EAEE;IAAgD;;;;;EAMlD;;;;EAKA;;;;EAKA;;;;EAEE;IAAgD;;;;;EAMlD;;;;EAKA;;;;EAEE;IAAgD;;;;;EAMlD;;;;EAEE;IAAgD;;;;;EAMlD;;;;EAKA;;;;EAEE;IAAgD;;;;;EAMlD;;;;EAKA;;;;EAKA;;;;EAEE;IAAgD;;;;;EAMlD;;;;EAKA;;;;EAKA;;;;EAEE;IAAgD;;;;;EAMlD;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAEE;IAAgD;;;;;EAMlD;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;;EAMA;;;;;EAOE;;;;EAOA;;;;EAOA;;;;EAOA;;;;;;;EAGE;IAAgD;;;;;EAUlD;;;;;EASE;IAAuB;;;;IAErB;MAAgD;;;;;;EAUlD;IAAuB;;;;IAErB;MAAgD;;;;;;EAUlD;IAAuB;;;;IAErB;MAAgD;;;;;;EAUlD;IAAuB;;;;;EASvB;IAAuB;;;;;EAQzB;;;;EAEE;IAAgD;;;;;EAQlD;;;;EAEE;IAAgD;;;;;EAQlD;;;;EAEE;IAAgD;;;;;EAQlD;;;;EAEE;IAAgD;;;;;EAQlD;;;;EAEE;IAAgD;;;;;EAQlD;;;;EAEE;IAAgD;;;;;EAShD;;;;EAEE;IAAgD;;;;;EASpD;;;;EAOA;;;;EAOA;;;;EAOA;;;;EAOA;;;;EAOA;;;;EAOA;;;;EAQE;;;;EAEE;IAAgD;;;;;EASpD;;;;EAOA;;;;EAOA;;;;EAQE;;;;EASA;;;;EASA;;;;EASA;;;;EASA;;;;EASA;;;;EASA;;;;EAUE;;;;;EAYA;;;;;EAYA;;;;EAWA;;;;EAUF;;;;EAQF;;;;EAOA;;;;EAEE;IAAgD;;;;;EAQlD;;;;EAOA;;;;EAOA;;;;EAOA;;;;EAOA;;;;EAEE;IAAgD;;;;;EAShD;;;;EAEE;IAAgD;;;;;EAQtD;;;;EAKA;;;;;EAMA;;;;EAKA;;;;EAKA;;;;;;EAOA;;;;EAKA;;;;EAEE;IAAgD;;;;;EAMlD;;;;EAKA;;;;EAKA;;;;;EAGE;IAAgC;;;;;;EAOlC;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;;EAGE;IAAgC;;;;;;EAOlC;;;;EAKA;;;;;EAGE;IAAgC;;;;;;EAOlC;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;;EAMA;;;;;EAMA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;;EAMA;;;;EAKA;;;;EAKA;;;;;EAMA;;;;EAKA;;;;;EAMA;;;;;EAMA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;;EAMA;;;;;EAMA;;;;EAKA;;;;EAME;;;;;EAQA;;;;;EAOF;;;;;EAMA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAOI;;;;EASA;;;;EASA;;;;EASA;;;;EAOJ;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;;EAMA;;;;EAKA;;;;;;EAOA;;;;EAKA;;;;;EAMA;;;;;EAMA;;;;;EAMA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;;EAMA;;;;EAKA;;;;EAKA;;;;EAME;;;;EAOA;;;;;EAQA;;;;;EAOF;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAOI;IAAuB;;;;;EASvB;IAAuB;;;;IAErB;MAAgD;;;;;;EAUlD;IAAuB;;;;IAErB;MAAgD;;;;;;EAUlD;IAAuB;;;;IAErB;MAAgD;;;;;;EAUlD;IAAuB;;;;;EA8I7B;;;;EAGA;;;;;AA1IF;;;;;;AAKA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;AAGA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;;;;AAOA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAkCA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAoLA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;AAIA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;AAIA;;;;;AAIA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;;AAKA;;;;;AAIA;;;;;;AAKA;;;;;;AAKA;;;;;AAIA;;;;;;AAKA;;;;;AAIA;;;;;;AAKA;;;;;AAIA;;;;;;AAKA;;;;;AAIA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;;AAKA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;;AAMA;;;;;;;AA8BA;;;;;;;;;;AAQA;;;;;;;;;;AAhBA;;;;;;;;;;AAwBA;;;;;;;;;;AAQA;;;;;;;;;;AAQA;;;;;;AAKA;;;;;;;;;;AAQA;;;;;;;;;;;;AAUA;;;;;;;;;;;;;;;;;;;AAgBA;;;;;;;;;;AAQA;;;;;;;;;;;;;;AAWA;;;;;;;;;;;;;;AAWA"}}]}
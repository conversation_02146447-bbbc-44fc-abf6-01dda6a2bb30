import { UpdateCmsBannersService } from '../services/update.cms-banners.service';
import { ReadCmsBannersService } from '../services/read.cms-banners.service';
import { CmsBannerDto } from '../dto/cms-banner.dto';
import { UpdateCmsBannerDto } from '../dto/update.cms-banner.dto';
import { CmsBannerStatus, CmsBannerLocation } from '../entity/cms-banners.entity';
export declare class UpdateCmsBannersController {
    private readonly updateCmsBannersService;
    private readonly readCmsBannersService;
    constructor(updateCmsBannersService: UpdateCmsBannersService, readCmsBannersService: ReadCmsBannersService);
    update(id: string, updateCmsBannerDto: UpdateCmsBannerDto, userId: string): Promise<CmsBannerDto | null>;
    bulkUpdate(updates: Array<{
        id: string;
        data: UpdateCmsBannerDto;
    }>, userId: string): Promise<CmsBannerDto[]>;
    bulkUpdateStatus(body: {
        ids: string[];
        status: CmsBannerStatus;
    }, userId: string): Promise<CmsBannerDto[]>;
    updateStatus(id: string, status: CmsBannerStatus, userId: string): Promise<CmsBannerDto | null>;
    updateLocation(id: string, location: CmsBannerLocation | null, userId: string): Promise<CmsBannerDto | null>;
    updateDisplayOrder(id: string, displayOrder: number, userId: string): Promise<CmsBannerDto | null>;
    updateDisplayTime(id: string, startDate: string | null, endDate: string | null, userId: string): Promise<CmsBannerDto | null>;
}

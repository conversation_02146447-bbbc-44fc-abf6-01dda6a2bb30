"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var VnpayPaymentService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.VnpayPaymentService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_transactional_1 = require("typeorm-transactional");
const event_emitter_1 = require("@nestjs/event-emitter");
const vnpay_service_1 = require("./vnpay.service");
const vnpay_transaction_repository_1 = require("../repositories/vnpay-transaction.repository");
const vnpay_transaction_entity_1 = require("../entities/vnpay-transaction.entity");
let VnpayPaymentService = VnpayPaymentService_1 = class VnpayPaymentService {
    vnpayService;
    vnpayTransactionRepository;
    eventEmitter;
    logger = new common_1.Logger(VnpayPaymentService_1.name);
    eventHandler;
    constructor(vnpayService, vnpayTransactionRepository, eventEmitter) {
        this.vnpayService = vnpayService;
        this.vnpayTransactionRepository = vnpayTransactionRepository;
        this.eventEmitter = eventEmitter;
    }
    setEventHandler(handler) {
        this.eventHandler = handler;
    }
    async createPayment(request) {
        try {
            this.logger.log(`Creating VNPAY payment for external ref: ${request.externalRef}`);
            this.validatePaymentRequest(request);
            const merchantTxnRef = this.generateMerchantTxnRef();
            const expiresAt = new Date(Date.now() + 15 * 60 * 1000);
            const transaction = await this.vnpayTransactionRepository.create({
                merchantTxnRef,
                type: vnpay_transaction_entity_1.VnpayTransactionType.PAYMENT,
                status: vnpay_transaction_entity_1.VnpayTransactionStatus.PENDING,
                amount: request.amount,
                orderInfo: request.description,
                clientIp: request.clientIp,
                bankCode: request.bankCode,
                locale: request.locale || 'vn',
                externalRef: request.externalRef,
                externalMetadata: request.metadata ? JSON.stringify(request.metadata) : null,
                expiresAt,
            });
            const vnpayRequest = {
                userId: request.externalRef,
                walletId: merchantTxnRef,
                amount: request.amount,
                gatewayType: 'VNPAY',
                description: request.description,
                ipAddress: request.clientIp,
                bankCode: request.bankCode,
                locale: request.locale,
            };
            const vnpayResult = await this.vnpayService.createPaymentUrl(vnpayRequest);
            await this.vnpayTransactionRepository.update(transaction.id, {
                vnpayTxnRef: vnpayResult.transactionId,
                vnpayRequest: JSON.stringify(vnpayRequest),
            });
            const response = {
                paymentUrl: vnpayResult.paymentUrl,
                merchantTxnRef,
                transactionId: transaction.id,
                expiresAt,
                gatewayType: 'VNPAY',
            };
            this.eventEmitter.emit('vnpay.payment.created', {
                merchantTxnRef,
                externalRef: request.externalRef,
                amount: request.amount,
                paymentUrl: vnpayResult.paymentUrl,
            });
            if (this.eventHandler?.onPaymentCreated) {
                await this.eventHandler.onPaymentCreated({
                    merchantTxnRef,
                    externalRef: request.externalRef,
                    amount: request.amount,
                    paymentUrl: vnpayResult.paymentUrl,
                });
            }
            this.logger.log(`VNPAY payment created successfully: ${merchantTxnRef}`);
            return response;
        }
        catch (error) {
            this.logger.error(`Error creating VNPAY payment: ${error.message}`, error.stack);
            throw error;
        }
    }
    async handleReturnCallback(params) {
        try {
            this.logger.log(`Handling VNPAY return callback: ${JSON.stringify(params)}`);
            const verifyResult = await this.vnpayService.verifyReturnUrl(params);
            const transaction = await this.vnpayTransactionRepository.findByVnpayRef(verifyResult.transactionId);
            if (!transaction) {
                this.logger.warn(`Transaction not found for VNPAY ref: ${verifyResult.transactionId}`);
                return this.createFailedCallback(params, 'Transaction not found');
            }
            await this.vnpayTransactionRepository.update(transaction.id, {
                returnCallbackData: JSON.stringify(params),
                vnpayResponse: JSON.stringify(verifyResult),
            });
            const callback = {
                isValid: verifyResult.isValid,
                isSuccess: verifyResult.responseCode === '00' && verifyResult.transactionStatus === '00',
                merchantTxnRef: transaction.merchantTxnRef,
                vnpayTxnRef: verifyResult.transactionId,
                vnpayTxnNo: verifyResult.vnpayTransactionNo,
                amount: verifyResult.amount || transaction.amount,
                responseCode: verifyResult.responseCode,
                transactionStatus: verifyResult.transactionStatus,
                bankCode: verifyResult.bankCode,
                cardType: verifyResult.cardType,
                payDate: verifyResult.payDate,
                message: verifyResult.message,
                externalRef: transaction.externalRef,
                rawData: params,
            };
            if (callback.isValid && callback.isSuccess) {
                await this.processSuccessfulPayment(transaction, callback);
            }
            else {
                await this.processFailedPayment(transaction, callback);
            }
            return callback;
        }
        catch (error) {
            this.logger.error(`Error handling VNPAY return callback: ${error.message}`, error.stack);
            return this.createFailedCallback(params, `Processing error: ${error.message}`);
        }
    }
    async handleIpnCallback(params) {
        try {
            this.logger.log(`Handling VNPAY IPN callback: ${JSON.stringify(params)}`);
            const callback = await this.handleReturnCallback(params);
            if (callback.merchantTxnRef) {
                const transaction = await this.vnpayTransactionRepository.findByMerchantRef(callback.merchantTxnRef);
                if (transaction) {
                    await this.vnpayTransactionRepository.update(transaction.id, {
                        ipnCallbackData: JSON.stringify(params),
                    });
                }
            }
            return callback;
        }
        catch (error) {
            this.logger.error(`Error handling VNPAY IPN callback: ${error.message}`, error.stack);
            return this.createFailedCallback(params, `IPN processing error: ${error.message}`);
        }
    }
    async queryTransaction(request) {
        try {
            this.logger.log(`Querying VNPAY transaction: ${request.merchantTxnRef}`);
            const transaction = await this.vnpayTransactionRepository.findByMerchantRef(request.merchantTxnRef);
            if (!transaction) {
                throw new common_1.BadRequestException(`Transaction not found: ${request.merchantTxnRef}`);
            }
            const queryResult = await this.vnpayService.queryTransaction({
                txnRef: transaction.vnpayTxnRef || request.merchantTxnRef,
                transactionDate: request.transactionDate,
                orderInfo: request.orderInfo,
                ipAddr: request.ipAddr,
                transactionNo: request.vnpayTxnNo,
            });
            const response = {
                isSuccess: queryResult.responseCode === '00',
                responseCode: queryResult.responseCode,
                message: queryResult.message,
                rawResponse: queryResult,
            };
            if (response.isSuccess && queryResult.transaction) {
                response.transaction = {
                    merchantTxnRef: transaction.merchantTxnRef,
                    vnpayTxnRef: queryResult.transaction.vnpayTxnRef,
                    vnpayTxnNo: queryResult.transaction.vnpayTxnNo,
                    amount: queryResult.transaction.amount,
                    orderInfo: queryResult.transaction.orderInfo,
                    responseCode: queryResult.transaction.responseCode,
                    transactionStatus: queryResult.transaction.transactionStatus,
                    bankCode: queryResult.transaction.bankCode,
                    cardType: queryResult.transaction.cardType,
                    payDate: queryResult.transaction.payDate,
                };
            }
            return response;
        }
        catch (error) {
            this.logger.error(`Error querying VNPAY transaction: ${error.message}`, error.stack);
            throw error;
        }
    }
    async refundTransaction(request) {
        try {
            this.logger.log(`Refunding VNPAY transaction: ${request.originalMerchantTxnRef}`);
            const originalTransaction = await this.vnpayTransactionRepository.findByMerchantRef(request.originalMerchantTxnRef);
            if (!originalTransaction) {
                throw new common_1.BadRequestException(`Original transaction not found: ${request.originalMerchantTxnRef}`);
            }
            const refundMerchantRef = this.generateMerchantTxnRef();
            const refundTransaction = await this.vnpayTransactionRepository.create({
                merchantTxnRef: refundMerchantRef,
                type: vnpay_transaction_entity_1.VnpayTransactionType.REFUND,
                status: vnpay_transaction_entity_1.VnpayTransactionStatus.PENDING,
                amount: request.amount,
                orderInfo: request.orderInfo,
                clientIp: request.ipAddr,
                externalRef: originalTransaction.externalRef,
                externalMetadata: JSON.stringify({
                    originalMerchantTxnRef: request.originalMerchantTxnRef,
                    refundType: request.transactionType,
                    createBy: request.createBy,
                }),
            });
            const refundResult = await this.vnpayService.refundTransaction({
                txnRef: originalTransaction.vnpayTxnRef || request.originalMerchantTxnRef,
                amount: request.amount,
                orderInfo: request.orderInfo,
                transactionDate: request.transactionDate,
                transactionType: request.transactionType,
                createBy: request.createBy,
                ipAddr: request.ipAddr,
                transactionNo: request.vnpayTxnNo,
            });
            await this.vnpayTransactionRepository.update(refundTransaction.id, {
                status: refundResult.responseCode === '00' ?
                    vnpay_transaction_entity_1.VnpayTransactionStatus.SUCCESS : vnpay_transaction_entity_1.VnpayTransactionStatus.FAILED,
                vnpayResponse: JSON.stringify(refundResult),
                processedAt: new Date(),
            });
            const response = {
                isSuccess: refundResult.responseCode === '00',
                responseCode: refundResult.responseCode,
                message: refundResult.message,
                rawResponse: refundResult,
            };
            if (response.isSuccess) {
                response.refund = {
                    merchantTxnRef: refundMerchantRef,
                    vnpayTxnRef: refundResult.vnpayTxnRef,
                    amount: request.amount,
                    orderInfo: request.orderInfo,
                    transactionType: request.transactionType,
                    createBy: request.createBy,
                    createDate: new Date().toISOString(),
                };
            }
            return response;
        }
        catch (error) {
            this.logger.error(`Error refunding VNPAY transaction: ${error.message}`, error.stack);
            throw error;
        }
    }
    async getTransaction(merchantTxnRef) {
        return await this.vnpayTransactionRepository.findByMerchantRef(merchantTxnRef);
    }
    async getStatistics(filter) {
        return await this.vnpayTransactionRepository.getStats(filter);
    }
    validatePaymentRequest(request) {
        if (!request.externalRef) {
            throw new common_1.BadRequestException('External reference is required');
        }
        if (!request.amount || request.amount <= 0) {
            throw new common_1.BadRequestException('Amount must be greater than 0');
        }
        if (request.amount < 10000) {
            throw new common_1.BadRequestException('Minimum amount is 10,000 VND');
        }
        if (request.amount > 50000000) {
            throw new common_1.BadRequestException('Maximum amount is 50,000,000 VND');
        }
        if (!request.description) {
            throw new common_1.BadRequestException('Description is required');
        }
        if (!request.clientIp) {
            throw new common_1.BadRequestException('Client IP is required');
        }
    }
    generateMerchantTxnRef() {
        const timestamp = Date.now();
        const random = Math.random().toString(36).substring(2, 8).toUpperCase();
        return `VNPAY${timestamp}${random}`;
    }
    async processSuccessfulPayment(transaction, callback) {
        await this.vnpayTransactionRepository.updateStatus(transaction.id, vnpay_transaction_entity_1.VnpayTransactionStatus.SUCCESS, {
            vnpayTxnNo: callback.vnpayTxnNo,
            vnpayResponseCode: callback.responseCode,
            vnpayTransactionStatus: callback.transactionStatus,
            vnpayPayDate: callback.payDate,
            bankCode: callback.bankCode,
            cardType: callback.cardType,
        });
        this.eventEmitter.emit('vnpay.payment.success', {
            merchantTxnRef: transaction.merchantTxnRef,
            externalRef: transaction.externalRef,
            amount: transaction.amount,
            vnpayTxnRef: callback.vnpayTxnRef,
            vnpayTxnNo: callback.vnpayTxnNo,
            bankCode: callback.bankCode,
            payDate: callback.payDate,
        });
        if (this.eventHandler?.onPaymentSuccess) {
            await this.eventHandler.onPaymentSuccess({
                merchantTxnRef: transaction.merchantTxnRef,
                externalRef: transaction.externalRef,
                amount: transaction.amount,
                vnpayTxnRef: callback.vnpayTxnRef,
                vnpayTxnNo: callback.vnpayTxnNo,
                bankCode: callback.bankCode,
                payDate: callback.payDate,
            });
        }
        this.logger.log(`Payment processed successfully: ${transaction.merchantTxnRef}`);
    }
    async processFailedPayment(transaction, callback) {
        await this.vnpayTransactionRepository.updateStatus(transaction.id, vnpay_transaction_entity_1.VnpayTransactionStatus.FAILED, {
            vnpayResponseCode: callback.responseCode,
            vnpayTransactionStatus: callback.transactionStatus,
            errorMessage: callback.message,
        });
        this.eventEmitter.emit('vnpay.payment.failed', {
            merchantTxnRef: transaction.merchantTxnRef,
            externalRef: transaction.externalRef,
            amount: transaction.amount,
            responseCode: callback.responseCode,
            message: callback.message,
        });
        if (this.eventHandler?.onPaymentFailed) {
            await this.eventHandler.onPaymentFailed({
                merchantTxnRef: transaction.merchantTxnRef,
                externalRef: transaction.externalRef,
                amount: transaction.amount,
                responseCode: callback.responseCode,
                message: callback.message,
            });
        }
        this.logger.log(`Payment failed: ${transaction.merchantTxnRef} - ${callback.message}`);
    }
    createFailedCallback(params, message) {
        return {
            isValid: false,
            isSuccess: false,
            merchantTxnRef: '',
            amount: 0,
            message,
            externalRef: '',
            rawData: params,
        };
    }
};
exports.VnpayPaymentService = VnpayPaymentService;
__decorate([
    (0, typeorm_transactional_1.Transactional)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], VnpayPaymentService.prototype, "createPayment", null);
__decorate([
    (0, typeorm_transactional_1.Transactional)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], VnpayPaymentService.prototype, "handleReturnCallback", null);
__decorate([
    (0, typeorm_transactional_1.Transactional)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], VnpayPaymentService.prototype, "handleIpnCallback", null);
exports.VnpayPaymentService = VnpayPaymentService = VnpayPaymentService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [vnpay_service_1.VnpayService,
        vnpay_transaction_repository_1.VnpayTransactionRepository,
        event_emitter_1.EventEmitter2])
], VnpayPaymentService);
//# sourceMappingURL=vnpay-payment.service.js.map
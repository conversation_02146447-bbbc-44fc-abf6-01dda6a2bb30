{"version": 3, "file": "mock-tradingview.service.js", "sourceRoot": "", "sources": ["../../../../src/modules/tradingview/services/mock-tradingview.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;AAAA,2CAAkE;AAClE,6CAAmD;AACnD,qCAAqC;AACrC,yEAA8D;AAE9D,mFAA8E;AAC9E,mDAA+C;AAOxC,IAAM,sBAAsB,8BAA5B,MAAM,sBAAsB;IASd;IACA;IAEA;IAXF,MAAM,GAAG,IAAI,eAAM,CAAC,wBAAsB,CAAC,IAAI,CAAC,CAAC;IAC1D,sBAAsB,CAAiB;IAC9B,aAAa,GAAG,QAAQ,CAAC;IAClC,SAAS,GAAG,IAAI,CAAC;IACR,UAAU,GAAG,IAAI,CAAC;IAClB,cAAc,GAAG,IAAI,CAAC;IAEvC,YACmB,2BAAwD,EACxD,YAA0B,EAE1B,qBAA8C;QAH9C,gCAA2B,GAA3B,2BAA2B,CAA6B;QACxD,iBAAY,GAAZ,YAAY,CAAc;QAE1B,0BAAqB,GAArB,qBAAqB,CAAyB;IAC9D,CAAC;IAEJ,KAAK,CAAC,YAAY;QAChB,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,8CAA8C,CAAC,CAAC;QAChE,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,qBAAqB,IAAI,CAAC,SAAS,EAAE,CAAC,CAAC;QACvD,IAAI,CAAC,mBAAmB,EAAE,CAAC;IAC7B,CAAC;IAKO,mBAAmB;QACzB,IAAI,CAAC,sBAAsB,GAAG,WAAW,CAAC,GAAG,EAAE;YAC7C,IAAI,CAAC,yBAAyB,EAAE,CAAC;QACnC,CAAC,EAAE,IAAI,CAAC,cAAc,CAAC,CAAC;QAExB,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,uDAAuD,IAAI,CAAC,cAAc,IAAI,CAAC,CAAC;QAGhG,IAAI,CAAC,yBAAyB,EAAE,CAAC;QAGjC,WAAW,CAAC,GAAG,EAAE;YACf,MAAM,WAAW,GAAG,IAAI,CAAC,2BAA2B,CAAC,SAAS,CAAC,EAAE,IAAI,IAAI,CAAC,CAAC;YAC3E,MAAM,iBAAiB,GAAG,IAAI,CAAC,2BAA2B,CAAC,eAAe,CAAC,EAAE,IAAI,IAAI,CAAC,CAAC;YACvF,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,uBAAuB,WAAW,oBAAoB,iBAAiB,UAAU,CAAC,CAAC;QACrG,CAAC,EAAE,KAAK,CAAC,CAAC;IACZ,CAAC;IAKO,yBAAyB;QAC/B,IAAI,CAAC;YACH,MAAM,SAAS,GAAG,IAAI,CAAC,qBAAqB,EAAE,CAAC;YAG/C,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,SAAS,SAAS,CAAC,MAAM,EAAE,EAAE,SAAS,EAAE,EAAE,CAAC,CAAC;YAGlE,IAAI,CAAC,oBAAoB,CAAC,SAAS,CAAC,CAAC;YAGrC,IAAI,CAAC,aAAa,CAAC,SAAS,CAAC,CAAC;YAE9B,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,8BAA8B,SAAS,CAAC,MAAM,KAAK,SAAS,CAAC,KAAK,EAAE,CAAC,CAAC;QAC1F,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,gCAAgC,KAAK,CAAC,OAAO,EAAE,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC;QAClF,CAAC;IACH,CAAC;IAKO,qBAAqB;QAE3B,MAAM,MAAM,GAAG,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,UAAU,GAAG,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;QAC1E,MAAM,QAAQ,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,SAAS,GAAG,MAAM,CAAC,CAAC;QACtD,MAAM,aAAa,GAAG,CAAC,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,GAAG,CAAC;QAGtD,IAAI,CAAC,SAAS,GAAG,QAAQ,CAAC;QAG1B,MAAM,MAAM,GAAG,QAAQ,GAAG,KAAK,CAAC;QAChC,MAAM,GAAG,GAAG,QAAQ,GAAG,MAAM,GAAG,CAAC,CAAC;QAClC,MAAM,GAAG,GAAG,QAAQ,GAAG,MAAM,GAAG,CAAC,CAAC;QAGlC,MAAM,OAAO,GAAG,QAAQ,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,GAAG,IAAI,CAAC,CAAC;QACtD,MAAM,MAAM,GAAG,QAAQ,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,GAAG,IAAI,CAAC,CAAC;QAErD,OAAO;YACL,MAAM,EAAE,IAAI,CAAC,aAAa;YAC1B,KAAK,EAAE,UAAU,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;YACtC,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;YACrB,MAAM,EAAE,UAAU,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;YACrC,aAAa,EAAE,UAAU,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;YACnD,GAAG,EAAE,UAAU,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;YAC/B,GAAG,EAAE,UAAU,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;YAC/B,IAAI,EAAE,UAAU,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;YACpC,GAAG,EAAE,UAAU,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;SACnC,CAAC;IACJ,CAAC;IAKO,oBAAoB,CAAC,IAA6B;QACxD,MAAM,WAAW,GAAiB;YAChC,MAAM,EAAE,IAAI,CAAC,MAAM;YACnB,KAAK,EAAE,IAAI,CAAC,KAAK;YACjB,SAAS,EAAE,IAAI,CAAC,SAAS;YACzB,MAAM,EAAE,IAAI,CAAC,MAAM;YACnB,aAAa,EAAE,IAAI,CAAC,aAAa;YACjC,GAAG,EAAE,IAAI,CAAC,GAAG;YACb,GAAG,EAAE,IAAI,CAAC,GAAG;YACb,IAAI,EAAE,IAAI,CAAC,IAAI;YACf,GAAG,EAAE,IAAI,CAAC,GAAG;SACd,CAAC;QAEF,IAAI,CAAC,2BAA2B,CAAC,oBAAoB,CAAC,WAAW,CAAC,CAAC;IACrE,CAAC;IAKO,KAAK,CAAC,aAAa,CAAC,IAA6B;QACvD,IAAI,CAAC;YACH,MAAM,WAAW,GAAG,IAAI,CAAC,qBAAqB,CAAC,MAAM,CAAC;gBACpD,MAAM,EAAE,IAAI,CAAC,MAAM;gBACnB,KAAK,EAAE,IAAI,CAAC,KAAK;gBACjB,GAAG,EAAE,IAAI,CAAC,GAAG;gBACb,GAAG,EAAE,IAAI,CAAC,GAAG;gBACb,IAAI,EAAE,IAAI,CAAC,IAAI;gBACf,GAAG,EAAE,IAAI,CAAC,GAAG;gBACb,MAAM,EAAE,IAAI,CAAC,MAAM;gBACnB,aAAa,EAAE,IAAI,CAAC,aAAa;gBACjC,SAAS,EAAE,IAAI,CAAC,SAAS;gBACzB,MAAM,EAAE,MAAM;aACf,CAAC,CAAC;YAEH,MAAM,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;QACrD,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,oCAAoC,KAAK,CAAC,OAAO,EAAE,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC;QACtF,CAAC;IACH,CAAC;IAKM,KAAK,CAAC,gBAAgB;QAC3B,IAAI,CAAC;YAEH,MAAM,UAAU,GAAG,IAAI,CAAC,YAAY,CAAC,GAAG,CAA0B,SAAS,IAAI,CAAC,aAAa,EAAE,CAAC,CAAC;YACjG,IAAI,UAAU,EAAE,CAAC;gBACf,OAAO;oBACL,MAAM,EAAE,UAAU,CAAC,MAAM;oBACzB,KAAK,EAAE,UAAU,CAAC,KAAK;oBACvB,SAAS,EAAE,UAAU,CAAC,SAAS;oBAC/B,MAAM,EAAE,UAAU,CAAC,MAAM;oBACzB,aAAa,EAAE,UAAU,CAAC,aAAa;oBACvC,GAAG,EAAE,UAAU,CAAC,GAAG;oBACnB,GAAG,EAAE,UAAU,CAAC,GAAG;oBACnB,IAAI,EAAE,UAAU,CAAC,IAAI;oBACrB,GAAG,EAAE,UAAU,CAAC,GAAG;iBACpB,CAAC;YACJ,CAAC;YAGD,MAAM,SAAS,GAAG,IAAI,CAAC,qBAAqB,EAAE,CAAC;YAG/C,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,SAAS,SAAS,CAAC,MAAM,EAAE,EAAE,SAAS,EAAE,EAAE,CAAC,CAAC;YAElE,OAAO,SAAS,CAAC;QACnB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,6BAA6B,KAAK,CAAC,OAAO,EAAE,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC;YAC7E,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;CACF,CAAA;AA9KY,wDAAsB;iCAAtB,sBAAsB;IADlC,IAAA,mBAAU,GAAE;IAYR,WAAA,IAAA,0BAAgB,EAAC,iCAAW,CAAC,CAAA;qCAFgB,2DAA2B;QAC1C,4BAAY;QAEH,oBAAU;GAZzC,sBAAsB,CA8KlC"}
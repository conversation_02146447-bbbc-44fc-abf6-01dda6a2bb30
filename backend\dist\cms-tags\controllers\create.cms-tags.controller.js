"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreateCmsTagsController = void 0;
const openapi = require("@nestjs/swagger");
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const jwt_auth_guard_1 = require("../../common/guards/jwt-auth.guard");
const create_cms_tags_service_1 = require("../services/create.cms-tags.service");
const create_cms_tag_dto_1 = require("../dto/create.cms-tag.dto");
const api_response_dto_1 = require("../../common/dto/api-response.dto");
const get_user_decorator_1 = require("../../common/decorators/get-user.decorator");
const roles_decorator_1 = require("../../common/decorators/roles.decorator");
const permissions_decorator_1 = require("../../common/decorators/permissions.decorator");
let CreateCmsTagsController = class CreateCmsTagsController {
    cmsTagsService;
    constructor(cmsTagsService) {
        this.cmsTagsService = cmsTagsService;
    }
    async create(createCmsTagDto, userId) {
        return this.cmsTagsService.create(createCmsTagDto, userId);
    }
    async bulkCreate(createCmsTagDtos, userId) {
        return this.cmsTagsService.bulkCreate(createCmsTagDtos, userId);
    }
    async createFromName(name, userId) {
        return this.cmsTagsService.createFromName(name, userId);
    }
    async findOrCreate(name, userId) {
        return this.cmsTagsService.findOrCreate(name, userId);
    }
    async createFromNames(names, userId) {
        return this.cmsTagsService.createFromNames(names, userId);
    }
};
exports.CreateCmsTagsController = CreateCmsTagsController;
__decorate([
    (0, common_1.Post)(),
    (0, roles_decorator_1.Roles)('ADMIN', 'EDITOR'),
    (0, permissions_decorator_1.Permissions)('cms-tag:create'),
    (0, swagger_1.ApiOperation)({ summary: 'Tạo mới thẻ CMS' }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.CREATED,
        description: 'Thẻ CMS đã được tạo thành công.',
        type: api_response_dto_1.ApiResponseDto,
        schema: {
            properties: {
                data: { $ref: '#/components/schemas/CmsTagDto' },
            },
        },
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.BAD_REQUEST,
        description: 'Dữ liệu đầu vào không hợp lệ.',
        type: api_response_dto_1.ApiResponseDto,
        schema: { properties: { data: { type: 'object', nullable: true } } },
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.CONFLICT,
        description: 'Tên hoặc slug đã tồn tại.',
        type: api_response_dto_1.ApiResponseDto,
        schema: { properties: { data: { type: 'object', nullable: true } } },
    }),
    (0, swagger_1.ApiBody)({ type: create_cms_tag_dto_1.CreateCmsTagDto }),
    openapi.ApiResponse({ status: 201, type: require("../dto/cms-tag.dto").CmsTagDto }),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, get_user_decorator_1.GetUser)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [create_cms_tag_dto_1.CreateCmsTagDto, String]),
    __metadata("design:returntype", Promise)
], CreateCmsTagsController.prototype, "create", null);
__decorate([
    (0, common_1.Post)('bulk'),
    (0, roles_decorator_1.Roles)('ADMIN'),
    (0, permissions_decorator_1.Permissions)('cms-tag:create'),
    (0, swagger_1.ApiOperation)({ summary: 'Tạo nhiều thẻ CMS cùng lúc' }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.CREATED,
        description: 'Các thẻ CMS đã được tạo thành công.',
        type: api_response_dto_1.ApiResponseDto,
        schema: {
            properties: {
                data: {
                    type: 'array',
                    items: { $ref: '#/components/schemas/CmsTagDto' },
                },
            },
        },
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.BAD_REQUEST,
        description: 'Dữ liệu đầu vào không hợp lệ.',
        type: api_response_dto_1.ApiResponseDto,
        schema: { properties: { data: { type: 'object', nullable: true } } },
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.CONFLICT,
        description: 'Có tên hoặc slug trùng lặp.',
        type: api_response_dto_1.ApiResponseDto,
        schema: { properties: { data: { type: 'object', nullable: true } } },
    }),
    (0, swagger_1.ApiBody)({ type: [create_cms_tag_dto_1.CreateCmsTagDto] }),
    openapi.ApiResponse({ status: 201, type: [require("../dto/cms-tag.dto").CmsTagDto] }),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, get_user_decorator_1.GetUser)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Array, String]),
    __metadata("design:returntype", Promise)
], CreateCmsTagsController.prototype, "bulkCreate", null);
__decorate([
    (0, common_1.Post)('from-name'),
    (0, roles_decorator_1.Roles)('ADMIN', 'EDITOR'),
    (0, permissions_decorator_1.Permissions)('cms-tag:create'),
    (0, swagger_1.ApiOperation)({ summary: 'Tạo thẻ CMS từ tên (tự động tạo slug)' }),
    (0, swagger_1.ApiQuery)({
        name: 'name',
        description: 'Tên thẻ',
        example: 'Thị trường vàng',
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.CREATED,
        description: 'Thẻ CMS đã được tạo từ tên thành công.',
        type: api_response_dto_1.ApiResponseDto,
        schema: {
            properties: { data: { $ref: '#/components/schemas/CmsTagDto' } },
        },
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.BAD_REQUEST,
        description: 'Tên thẻ không hợp lệ.',
        type: api_response_dto_1.ApiResponseDto,
        schema: { properties: { data: { type: 'object', nullable: true } } },
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.CONFLICT,
        description: 'Tên thẻ đã tồn tại.',
        type: api_response_dto_1.ApiResponseDto,
        schema: { properties: { data: { type: 'object', nullable: true } } },
    }),
    openapi.ApiResponse({ status: 201, type: require("../dto/cms-tag.dto").CmsTagDto }),
    __param(0, (0, common_1.Query)('name')),
    __param(1, (0, get_user_decorator_1.GetUser)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String]),
    __metadata("design:returntype", Promise)
], CreateCmsTagsController.prototype, "createFromName", null);
__decorate([
    (0, common_1.Post)('find-or-create'),
    (0, roles_decorator_1.Roles)('ADMIN', 'EDITOR'),
    (0, permissions_decorator_1.Permissions)('cms-tag:create'),
    (0, swagger_1.ApiOperation)({ summary: 'Tìm hoặc tạo thẻ CMS theo tên' }),
    (0, swagger_1.ApiQuery)({
        name: 'name',
        description: 'Tên thẻ',
        example: 'Thị trường vàng',
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Thẻ CMS đã được tìm thấy hoặc tạo mới thành công.',
        type: api_response_dto_1.ApiResponseDto,
        schema: {
            properties: { data: { $ref: '#/components/schemas/CmsTagDto' } },
        },
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.BAD_REQUEST,
        description: 'Tên thẻ không hợp lệ.',
        type: api_response_dto_1.ApiResponseDto,
        schema: { properties: { data: { type: 'object', nullable: true } } },
    }),
    openapi.ApiResponse({ status: 201, type: require("../dto/cms-tag.dto").CmsTagDto }),
    __param(0, (0, common_1.Query)('name')),
    __param(1, (0, get_user_decorator_1.GetUser)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String]),
    __metadata("design:returntype", Promise)
], CreateCmsTagsController.prototype, "findOrCreate", null);
__decorate([
    (0, common_1.Post)('from-names'),
    (0, roles_decorator_1.Roles)('ADMIN', 'EDITOR'),
    (0, permissions_decorator_1.Permissions)('cms-tag:create'),
    (0, swagger_1.ApiOperation)({ summary: 'Tạo nhiều thẻ CMS từ danh sách tên' }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.CREATED,
        description: 'Các thẻ CMS đã được tạo từ danh sách tên thành công.',
        type: api_response_dto_1.ApiResponseDto,
        schema: {
            properties: {
                data: {
                    type: 'array',
                    items: { $ref: '#/components/schemas/CmsTagDto' },
                },
            },
        },
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.BAD_REQUEST,
        description: 'Danh sách tên không hợp lệ.',
        type: api_response_dto_1.ApiResponseDto,
        schema: { properties: { data: { type: 'object', nullable: true } } },
    }),
    (0, swagger_1.ApiBody)({
        schema: {
            type: 'object',
            properties: {
                names: {
                    type: 'array',
                    items: { type: 'string' },
                    description: 'Danh sách tên thẻ',
                    example: ['Thị trường vàng', 'Đầu tư', 'Tài chính'],
                },
            },
            required: ['names'],
        },
    }),
    openapi.ApiResponse({ status: 201, type: [require("../dto/cms-tag.dto").CmsTagDto] }),
    __param(0, (0, common_1.Body)('names')),
    __param(1, (0, get_user_decorator_1.GetUser)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Array, String]),
    __metadata("design:returntype", Promise)
], CreateCmsTagsController.prototype, "createFromNames", null);
exports.CreateCmsTagsController = CreateCmsTagsController = __decorate([
    (0, swagger_1.ApiTags)('cms-tags'),
    (0, swagger_1.ApiBearerAuth)(),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, common_1.Controller)('cms/tags'),
    __metadata("design:paramtypes", [create_cms_tags_service_1.CreateCmsTagsService])
], CreateCmsTagsController);
//# sourceMappingURL=create.cms-tags.controller.js.map
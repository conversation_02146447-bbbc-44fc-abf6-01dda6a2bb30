/**
 * Script test để xóa một vài ngân hàng (không phải tất cả)
 */

const axios = require('axios');

const CONFIG = {
  BACKEND_URL: 'http://localhost:3168',
  ADMIN_CREDENTIALS: {
    identity: '<EMAIL>',
    password: 'adminX@123'
  }
};

const log = (message, type = 'INFO') => {
  const timestamp = new Date().toLocaleTimeString();
  const prefix = type === 'ERROR' ? '❌' : type === 'SUCCESS' ? '✅' : 'ℹ️';
  console.log(`[${timestamp}] ${prefix} ${message}`);
};

// Đăng nhập
async function login() {
  try {
    log('Đang đăng nhập...');
    const response = await axios.post(`${CONFIG.BACKEND_URL}/api/v1/auth/login`, CONFIG.ADMIN_CREDENTIALS);
    
    const token = response.data?.data?.access_token;
    if (token) {
      log('Đăng nhập thành công', 'SUCCESS');
      return token;
    } else {
      throw new Error('Không nhận được access token');
    }
  } catch (error) {
    log(`Lỗi đăng nhập: ${error.message}`, 'ERROR');
    throw error;
  }
}

// Lấy danh sách ngân hàng (với limit đúng)
async function getBanks(token, limit = 10) {
  try {
    log(`Đang lấy ${limit} ngân hàng đầu tiên...`);
    
    const response = await axios.get(`${CONFIG.BACKEND_URL}/api/v1/banks`, {
      params: {
        page: 1,
        limit: Math.min(limit, 100) // Đảm bảo không vượt quá 100
      },
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });
    
    const banks = response.data?.data || [];
    log(`Lấy được ${banks.length} ngân hàng`, 'SUCCESS');
    
    return banks;
  } catch (error) {
    log(`Lỗi lấy danh sách ngân hàng: ${error.message}`, 'ERROR');
    if (error.response) {
      log(`Response: ${JSON.stringify(error.response.data)}`, 'ERROR');
    }
    throw error;
  }
}

// Test xóa một ngân hàng
async function testDeleteSingle(token, bankId) {
  try {
    log(`Đang xóa ngân hàng ID: ${bankId}`);
    
    await axios.delete(`${CONFIG.BACKEND_URL}/api/v1/banks/${bankId}`, {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });
    
    log(`Xóa thành công ngân hàng ${bankId}`, 'SUCCESS');
    return true;
  } catch (error) {
    log(`Lỗi xóa ngân hàng ${bankId}: ${error.message}`, 'ERROR');
    if (error.response) {
      log(`Response: ${JSON.stringify(error.response.data)}`, 'ERROR');
    }
    return false;
  }
}

// Test xóa nhiều ngân hàng
async function testBulkDelete(token, bankIds) {
  try {
    log(`Đang xóa ${bankIds.length} ngân hàng cùng lúc...`);
    
    await axios.post(`${CONFIG.BACKEND_URL}/api/v1/banks/bulk-delete`, bankIds, {
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    });
    
    log(`Xóa bulk thành công ${bankIds.length} ngân hàng`, 'SUCCESS');
    return true;
  } catch (error) {
    log(`Lỗi xóa bulk: ${error.message}`, 'ERROR');
    if (error.response) {
      log(`Response: ${JSON.stringify(error.response.data)}`, 'ERROR');
    }
    return false;
  }
}

// Script chính
async function testDeleteBanks() {
  try {
    log('=== BẮT ĐẦU TEST XÓA NGÂN HÀNG ===');
    
    // 1. Đăng nhập
    const token = await login();
    
    // 2. Lấy danh sách ngân hàng hiện có
    log('\n=== BƯỚC 1: LẤY DANH SÁCH NGÂN HÀNG ===');
    const banks = await getBanks(token, 10);
    
    if (banks.length === 0) {
      log('Không có ngân hàng nào để test xóa');
      return;
    }
    
    // Hiển thị danh sách
    log('Danh sách ngân hàng hiện có:');
    banks.forEach((bank, index) => {
      log(`  ${index + 1}. ${bank.brandName} (${bank.code}) - ID: ${bank.id}`);
    });
    
    // 3. Test xóa một ngân hàng đầu tiên
    if (banks.length > 0) {
      log('\n=== BƯỚC 2: TEST XÓA MỘT NGÂN HÀNG ===');
      const firstBank = banks[0];
      log(`Sẽ xóa: ${firstBank.brandName} (${firstBank.code})`);
      
      const singleDeleteSuccess = await testDeleteSingle(token, firstBank.id);
      
      if (singleDeleteSuccess) {
        log('✅ Test xóa đơn lẻ thành công');
      } else {
        log('❌ Test xóa đơn lẻ thất bại');
      }
    }
    
    // 4. Test xóa nhiều ngân hàng (nếu còn đủ)
    if (banks.length > 3) {
      log('\n=== BƯỚC 3: TEST XÓA NHIỀU NGÂN HÀNG ===');
      const banksToDelete = banks.slice(1, 4); // Lấy 3 ngân hàng tiếp theo
      const idsToDelete = banksToDelete.map(bank => bank.id);
      
      log('Sẽ xóa bulk:');
      banksToDelete.forEach((bank, index) => {
        log(`  ${index + 1}. ${bank.brandName} (${bank.code})`);
      });
      
      const bulkDeleteSuccess = await testBulkDelete(token, idsToDelete);
      
      if (bulkDeleteSuccess) {
        log('✅ Test xóa bulk thành công');
      } else {
        log('❌ Test xóa bulk thất bại');
      }
    }
    
    // 5. Kiểm tra lại danh sách sau khi xóa
    log('\n=== BƯỚC 4: KIỂM TRA SAU KHI XÓA ===');
    const remainingBanks = await getBanks(token, 10);
    log(`Số ngân hàng còn lại: ${remainingBanks.length}`);
    
    log('\n=== HOÀN THÀNH TEST XÓA ===');
    
  } catch (error) {
    log(`❌ Lỗi trong quá trình test: ${error.message}`, 'ERROR');
    process.exit(1);
  }
}

// Chạy test
if (require.main === module) {
  // Kiểm tra arguments
  const args = process.argv.slice(2);
  if (args.includes('--help') || args.includes('-h')) {
    console.log(`
Cách sử dụng: node test-delete-banks.js

Script này sẽ:
1. Lấy 10 ngân hàng đầu tiên
2. Xóa 1 ngân hàng đầu tiên (test single delete)
3. Xóa 3 ngân hàng tiếp theo (test bulk delete)
4. Kiểm tra kết quả

⚠️  CẢNH BÁO: Script này sẽ XÓA THẬT ngân hàng trong database!
`);
    process.exit(0);
  }
  
  testDeleteBanks().catch(error => {
    log(`❌ Lỗi không mong đợi: ${error.message}`, 'ERROR');
    process.exit(1);
  });
}

module.exports = {
  testDeleteBanks,
  getBanks,
  testDeleteSingle,
  testBulkDelete
};

"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var CreateCmsPartnersController_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreateCmsPartnersController = void 0;
const openapi = require("@nestjs/swagger");
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const jwt_auth_guard_1 = require("../../common/guards/jwt-auth.guard");
const roles_decorator_1 = require("../../common/decorators/roles.decorator");
const permissions_decorator_1 = require("../../common/decorators/permissions.decorator");
const create_cms_partners_service_1 = require("../services/create.cms-partners.service");
const dto_1 = require("../dto");
let CreateCmsPartnersController = CreateCmsPartnersController_1 = class CreateCmsPartnersController {
    createCmsPartnersService;
    logger = new common_1.Logger(CreateCmsPartnersController_1.name);
    constructor(createCmsPartnersService) {
        this.createCmsPartnersService = createCmsPartnersService;
    }
    async create(createCmsPartnerDto, req) {
        this.logger.log(`Creating new CMS Partner: ${createCmsPartnerDto.name}`);
        return this.createCmsPartnersService.create(createCmsPartnerDto, req.user.id);
    }
    async bulkCreate(bulkCreateCmsPartnersDto, req) {
        this.logger.log(`Bulk creating ${bulkCreateCmsPartnersDto.partners.length} CMS Partners`);
        return this.createCmsPartnersService.bulkCreate(bulkCreateCmsPartnersDto, req.user.id);
    }
    async createFromTemplate(createFromTemplateDto, req) {
        this.logger.log(`Creating CMS Partner from template: ${createFromTemplateDto.templateId}`);
        return this.createCmsPartnersService.createFromTemplate(createFromTemplateDto, req.user.id);
    }
    async duplicate(id, duplicateDto, req) {
        this.logger.log(`Duplicating CMS Partner: ${id}`);
        return this.createCmsPartnersService.duplicate(id, duplicateDto, req.user.id);
    }
    async import(importDto, req) {
        this.logger.log(`Importing ${importDto.partners.length} CMS Partners`);
        return this.createCmsPartnersService.import(importDto, req.user.id);
    }
};
exports.CreateCmsPartnersController = CreateCmsPartnersController;
__decorate([
    (0, common_1.Post)(),
    (0, common_1.HttpCode)(common_1.HttpStatus.CREATED),
    (0, roles_decorator_1.Roles)('admin', 'content_manager'),
    (0, permissions_decorator_1.Permissions)('cms_partners:create'),
    (0, common_1.UsePipes)(new common_1.ValidationPipe({ transform: true, whitelist: true })),
    (0, swagger_1.ApiOperation)({
        summary: 'Tạo mới đối tác',
        description: 'Tạo mới một đối tác trong hệ thống CMS',
    }),
    (0, swagger_1.ApiBody)({
        type: dto_1.CreateCmsPartnerDto,
        description: 'Thông tin đối tác cần tạo',
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.CREATED,
        description: 'Đối tác đã được tạo thành công',
        type: dto_1.CmsPartnerDto,
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.BAD_REQUEST,
        description: 'Dữ liệu đầu vào không hợp lệ',
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.UNAUTHORIZED,
        description: 'Không có quyền truy cập',
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.FORBIDDEN,
        description: 'Không có quyền thực hiện thao tác này',
    }),
    openapi.ApiResponse({ status: common_1.HttpStatus.CREATED, type: require("../dto/cms-partner.dto").CmsPartnerDto }),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [dto_1.CreateCmsPartnerDto, Object]),
    __metadata("design:returntype", Promise)
], CreateCmsPartnersController.prototype, "create", null);
__decorate([
    (0, common_1.Post)('bulk'),
    (0, common_1.HttpCode)(common_1.HttpStatus.CREATED),
    (0, roles_decorator_1.Roles)('admin', 'content_manager'),
    (0, permissions_decorator_1.Permissions)('cms_partners:create'),
    (0, common_1.UsePipes)(new common_1.ValidationPipe({ transform: true, whitelist: true })),
    (0, swagger_1.ApiOperation)({
        summary: 'Tạo nhiều đối tác',
        description: 'Tạo nhiều đối tác cùng lúc trong hệ thống CMS',
    }),
    (0, swagger_1.ApiBody)({
        type: dto_1.BulkCreateCmsPartnersDto,
        description: 'Danh sách đối tác cần tạo',
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.CREATED,
        description: 'Kết quả tạo nhiều đối tác',
        type: dto_1.BulkOperationResponseDto,
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.BAD_REQUEST,
        description: 'Dữ liệu đầu vào không hợp lệ',
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.UNAUTHORIZED,
        description: 'Không có quyền truy cập',
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.FORBIDDEN,
        description: 'Không có quyền thực hiện thao tác này',
    }),
    openapi.ApiResponse({ status: common_1.HttpStatus.CREATED, type: require("../dto/create.cms-partner.dto").BulkOperationResponseDto }),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [dto_1.BulkCreateCmsPartnersDto, Object]),
    __metadata("design:returntype", Promise)
], CreateCmsPartnersController.prototype, "bulkCreate", null);
__decorate([
    (0, common_1.Post)('from-template'),
    (0, common_1.HttpCode)(common_1.HttpStatus.CREATED),
    (0, roles_decorator_1.Roles)('admin', 'content_manager'),
    (0, permissions_decorator_1.Permissions)('cms_partners:create'),
    (0, common_1.UsePipes)(new common_1.ValidationPipe({ transform: true, whitelist: true })),
    (0, swagger_1.ApiOperation)({
        summary: 'Tạo đối tác từ template',
        description: 'Tạo đối tác mới dựa trên template có sẵn',
    }),
    (0, swagger_1.ApiBody)({
        type: dto_1.CreateCmsPartnerFromTemplateDto,
        description: 'Thông tin tạo đối tác từ template',
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.CREATED,
        description: 'Đối tác đã được tạo từ template thành công',
        type: dto_1.CmsPartnerDto,
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.BAD_REQUEST,
        description: 'Dữ liệu đầu vào không hợp lệ',
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.NOT_FOUND,
        description: 'Không tìm thấy template',
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.UNAUTHORIZED,
        description: 'Không có quyền truy cập',
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.FORBIDDEN,
        description: 'Không có quyền thực hiện thao tác này',
    }),
    openapi.ApiResponse({ status: common_1.HttpStatus.CREATED, type: require("../dto/cms-partner.dto").CmsPartnerDto }),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [dto_1.CreateCmsPartnerFromTemplateDto, Object]),
    __metadata("design:returntype", Promise)
], CreateCmsPartnersController.prototype, "createFromTemplate", null);
__decorate([
    (0, common_1.Post)(':id/duplicate'),
    (0, common_1.HttpCode)(common_1.HttpStatus.CREATED),
    (0, roles_decorator_1.Roles)('admin', 'content_manager'),
    (0, permissions_decorator_1.Permissions)('cms_partners:create'),
    (0, common_1.UsePipes)(new common_1.ValidationPipe({ transform: true, whitelist: true })),
    (0, swagger_1.ApiOperation)({
        summary: 'Nhân bản đối tác',
        description: 'Tạo bản sao của đối tác hiện có',
    }),
    (0, swagger_1.ApiParam)({
        name: 'id',
        description: 'ID của đối tác cần nhân bản',
        example: '123e4567-e89b-12d3-a456-426614174000',
    }),
    (0, swagger_1.ApiBody)({
        type: dto_1.DuplicateCmsPartnerDto,
        description: 'Thông tin nhân bản đối tác',
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.CREATED,
        description: 'Đối tác đã được nhân bản thành công',
        type: dto_1.CmsPartnerDto,
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.BAD_REQUEST,
        description: 'Dữ liệu đầu vào không hợp lệ',
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.NOT_FOUND,
        description: 'Không tìm thấy đối tác cần nhân bản',
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.UNAUTHORIZED,
        description: 'Không có quyền truy cập',
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.FORBIDDEN,
        description: 'Không có quyền thực hiện thao tác này',
    }),
    openapi.ApiResponse({ status: common_1.HttpStatus.CREATED, type: require("../dto/cms-partner.dto").CmsPartnerDto }),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __param(2, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, dto_1.DuplicateCmsPartnerDto, Object]),
    __metadata("design:returntype", Promise)
], CreateCmsPartnersController.prototype, "duplicate", null);
__decorate([
    (0, common_1.Post)('import'),
    (0, common_1.HttpCode)(common_1.HttpStatus.CREATED),
    (0, roles_decorator_1.Roles)('admin', 'content_manager'),
    (0, permissions_decorator_1.Permissions)('cms_partners:create'),
    (0, common_1.UsePipes)(new common_1.ValidationPipe({ transform: true, whitelist: true })),
    (0, swagger_1.ApiOperation)({
        summary: 'Import đối tác',
        description: 'Import đối tác từ dữ liệu bên ngoài',
    }),
    (0, swagger_1.ApiBody)({
        type: dto_1.ImportCmsPartnersDto,
        description: 'Dữ liệu import đối tác',
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.CREATED,
        description: 'Kết quả import đối tác',
        type: dto_1.BulkOperationResponseDto,
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.BAD_REQUEST,
        description: 'Dữ liệu đầu vào không hợp lệ',
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.UNAUTHORIZED,
        description: 'Không có quyền truy cập',
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.FORBIDDEN,
        description: 'Không có quyền thực hiện thao tác này',
    }),
    openapi.ApiResponse({ status: common_1.HttpStatus.CREATED, type: require("../dto/create.cms-partner.dto").BulkOperationResponseDto }),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [dto_1.ImportCmsPartnersDto, Object]),
    __metadata("design:returntype", Promise)
], CreateCmsPartnersController.prototype, "import", null);
exports.CreateCmsPartnersController = CreateCmsPartnersController = CreateCmsPartnersController_1 = __decorate([
    (0, swagger_1.ApiTags)('CMS Partners - Create'),
    (0, common_1.Controller)('cms/partners'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, swagger_1.ApiBearerAuth)(),
    __metadata("design:paramtypes", [create_cms_partners_service_1.CreateCmsPartnersService])
], CreateCmsPartnersController);
//# sourceMappingURL=create.cms-partners.controller.js.map
import { UserDto } from '../../users/dto/user.dto';
import { CmsPartnerType, CmsPartnerStatus } from '../entity/cms-partners.entity';
export declare class CmsPartnerDto {
    id: string;
    name: string;
    logoUrl?: string | null;
    websiteUrl?: string | null;
    description?: string | null;
    type: CmsPartnerType;
    displayOrder: number;
    status: CmsPartnerStatus;
    createdAt: Date;
    updatedAt: Date;
    createdBy?: string | null;
    updatedBy?: string | null;
    deletedAt?: Date | null;
    deletedBy?: string | null;
    isDeleted: boolean;
    isActive: boolean;
    hasLogo: boolean;
    hasWebsite: boolean;
    hasDescription: boolean;
    hasCompleteInfo: boolean;
    typeDisplayName: string;
    statusDisplayName: string;
    canDisplayPublicly: boolean;
    slug: string;
    isPartner: boolean;
    isClient: boolean;
    isSupplier: boolean;
    creator?: UserDto;
    updater?: UserDto;
    deleter?: UserDto;
}

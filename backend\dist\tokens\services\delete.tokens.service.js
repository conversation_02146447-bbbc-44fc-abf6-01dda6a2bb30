"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.DeleteTokensService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_transactional_1 = require("typeorm-transactional");
const typeorm_1 = require("typeorm");
const typeorm_2 = require("typeorm");
const typeorm_3 = require("@nestjs/typeorm");
const event_emitter_1 = require("@nestjs/event-emitter");
const base_tokens_service_1 = require("./base.tokens.service");
const token_entity_1 = require("../entities/token.entity");
let DeleteTokensService = class DeleteTokensService extends base_tokens_service_1.BaseTokensService {
    tokenRepository;
    eventEmitter;
    constructor(tokenRepository, eventEmitter) {
        super(tokenRepository, eventEmitter);
        this.tokenRepository = tokenRepository;
        this.eventEmitter = eventEmitter;
    }
    async remove(id) {
        try {
            this.logger.debug(`Đang xóa vĩnh viễn token với ID: ${id}`);
            const token = await this.findByIdOrFail(id);
            const result = await this.tokenRepository.delete(id);
            if (result.affected === 0) {
                throw new common_1.InternalServerErrorException(`Không thể xóa token với ID "${id}"`);
            }
            this.eventEmitter.emit(this.EVENT_TOKEN_DELETED, {
                entityId: id,
                userId: null,
                softDelete: false,
            });
            this.logger.debug(`Đã xóa thành công token với ID: ${id}`);
            return { affected: result.affected || 0 };
        }
        catch (error) {
            this.logger.error(`Lỗi khi xóa token: ${error.message}`, error.stack);
            if (error instanceof common_1.NotFoundException) {
                throw error;
            }
            throw new common_1.InternalServerErrorException(`Không thể xóa token: ${error.message}`);
        }
    }
    async bulkDelete(ids) {
        try {
            this.logger.debug(`Đang xóa hàng loạt token với các ID: ${ids.join(', ')}`);
            if (!ids.length) {
                return { affected: 0 };
            }
            const result = await this.tokenRepository.delete(ids);
            const affectedCount = result.affected || 0;
            if (affectedCount > 0) {
                ids.forEach(id => {
                    this.eventEmitter.emit(this.EVENT_TOKEN_DELETED, {
                        entityId: id,
                        userId: null,
                        softDelete: false,
                    });
                });
            }
            this.logger.debug(`Đã xóa thành công ${affectedCount} token`);
            return { affected: affectedCount };
        }
        catch (error) {
            this.logger.error(`Lỗi khi xóa hàng loạt token: ${error.message}`, error.stack);
            throw new common_1.InternalServerErrorException(`Không thể xóa hàng loạt token: ${error.message}`);
        }
    }
    async softDelete(id, deletedById) {
        try {
            this.logger.debug(`Đang xóa mềm token với ID: ${id}`);
            await this.findByIdOrFail(id);
            const updateResult = await this.tokenRepository.update(id, {
                isDeleted: true,
                deletedBy: deletedById,
            });
            if (updateResult.affected === 0) {
                throw new common_1.InternalServerErrorException(`Không thể xóa mềm token với ID "${id}"`);
            }
            const deletedToken = await this.findByIdOrFail(id, [], true);
            const dto = this.toDto(deletedToken);
            this.eventEmitter.emit(this.EVENT_TOKEN_DELETED, {
                entityId: id,
                userId: deletedById,
                softDelete: true,
            });
            this.logger.debug(`Đã xóa mềm thành công token với ID: ${id}`);
            return dto;
        }
        catch (error) {
            this.logger.error(`Lỗi khi xóa mềm token: ${error.message}`, error.stack);
            if (error instanceof common_1.NotFoundException) {
                throw error;
            }
            throw new common_1.InternalServerErrorException(`Không thể xóa mềm token: ${error.message}`);
        }
    }
    async bulkSoftDelete(ids, deletedById) {
        try {
            this.logger.debug(`Đang xóa mềm hàng loạt token với các ID: ${ids.join(', ')}`);
            if (!ids.length) {
                return { affected: 0, dtos: [] };
            }
            const result = await this.tokenRepository.update(ids, {
                isDeleted: true,
                deletedBy: deletedById,
            });
            const affectedCount = result.affected || 0;
            let dtos = [];
            if (affectedCount > 0) {
                const deletedTokens = await this.tokenRepository.find({
                    where: { id: (0, typeorm_1.In)(ids) },
                    withDeleted: true,
                });
                dtos = deletedTokens.filter(t => t.isDeleted).map(token => this.toDto(token));
                dtos.forEach(dto => {
                    this.eventEmitter.emit(this.EVENT_TOKEN_DELETED, {
                        entityId: dto.id,
                        userId: deletedById,
                        softDelete: true,
                    });
                });
            }
            this.logger.debug(`Đã xóa mềm thành công ${affectedCount} token`);
            return { affected: affectedCount, dtos };
        }
        catch (error) {
            this.logger.error(`Lỗi khi xóa mềm hàng loạt token: ${error.message}`, error.stack);
            throw new common_1.InternalServerErrorException(`Không thể xóa mềm hàng loạt token: ${error.message}`);
        }
    }
    async restore(id) {
        try {
            this.logger.debug(`Đang khôi phục token với ID: ${id}`);
            const token = await this.findByIdOrFail(id, [], true);
            if (!token.isDeleted) {
                throw new common_1.InternalServerErrorException(`Token với ID "${id}" chưa bị xóa`);
            }
            const result = await this.tokenRepository.update(id, {
                isDeleted: false,
                deletedBy: undefined,
            });
            if (result.affected === 0) {
                throw new common_1.InternalServerErrorException(`Không thể khôi phục token với ID "${id}"`);
            }
            const restoredToken = await this.findByIdOrFail(id);
            const dto = this.toDto(restoredToken);
            this.eventEmitter.emit(this.EVENT_TOKEN_RESTORED, {
                entityId: id,
                userId: null,
                newData: dto,
            });
            this.logger.debug(`Đã khôi phục thành công token với ID: ${id}`);
            return dto;
        }
        catch (error) {
            this.logger.error(`Lỗi khi khôi phục token: ${error.message}`, error.stack);
            throw error;
        }
    }
    async cleanupOldRecords(days) {
        try {
            this.logger.debug(`Đang dọn dẹp các bản ghi token cũ hơn ${days} ngày`);
            const cutoffDate = new Date();
            cutoffDate.setDate(cutoffDate.getDate() - days);
            const result = await this.tokenRepository.delete({
                isDeleted: true,
                updatedAt: (0, typeorm_1.LessThan)(cutoffDate),
            });
            const affectedCount = result.affected || 0;
            if (affectedCount > 0) {
                this.eventEmitter.emit(this.EVENT_TOKEN_CLEANUP, {
                    userId: null,
                    days,
                    affectedCount,
                    cutoffDate,
                });
            }
            this.logger.debug(`Đã dọn dẹp ${affectedCount} bản ghi token cũ`);
            return { affected: affectedCount };
        }
        catch (error) {
            this.logger.error(`Lỗi khi dọn dẹp các bản ghi token cũ: ${error.message}`, error.stack);
            throw new common_1.InternalServerErrorException(`Không thể dọn dẹp các bản ghi token cũ: ${error.message}`);
        }
    }
};
exports.DeleteTokensService = DeleteTokensService;
__decorate([
    (0, typeorm_transactional_1.Transactional)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], DeleteTokensService.prototype, "remove", null);
__decorate([
    (0, typeorm_transactional_1.Transactional)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Array]),
    __metadata("design:returntype", Promise)
], DeleteTokensService.prototype, "bulkDelete", null);
__decorate([
    (0, typeorm_transactional_1.Transactional)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String]),
    __metadata("design:returntype", Promise)
], DeleteTokensService.prototype, "softDelete", null);
__decorate([
    (0, typeorm_transactional_1.Transactional)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Array, String]),
    __metadata("design:returntype", Promise)
], DeleteTokensService.prototype, "bulkSoftDelete", null);
__decorate([
    (0, typeorm_transactional_1.Transactional)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], DeleteTokensService.prototype, "restore", null);
__decorate([
    (0, typeorm_transactional_1.Transactional)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", Promise)
], DeleteTokensService.prototype, "cleanupOldRecords", null);
exports.DeleteTokensService = DeleteTokensService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_3.InjectRepository)(token_entity_1.Token)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        event_emitter_1.EventEmitter2])
], DeleteTokensService);
//# sourceMappingURL=delete.tokens.service.js.map
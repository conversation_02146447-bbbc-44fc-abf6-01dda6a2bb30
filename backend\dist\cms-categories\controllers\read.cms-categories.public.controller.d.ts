import { ReadCmsCategoriesService } from '../services/read.cms-categories.service';
import { CmsCategoryPublicDto } from '../dto/cms-category-public.dto';
import { CustomPaginationQueryDto } from '../../common/dto/custom-pagination-query.dto';
import { PaginationResponseDto } from '../../common/dto/pagination-response.dto';
export declare class ReadCmsCategoriesPublicController {
    private readonly cmsCategoriesService;
    private readonly logger;
    constructor(cmsCategoriesService: ReadCmsCategoriesService);
    getActiveCategories(paginationQuery: CustomPaginationQueryDto): Promise<PaginationResponseDto<CmsCategoryPublicDto>>;
    getCarouselCategories(limit?: number): Promise<CmsCategoryPublicDto[]>;
    search(searchTerm: string, paginationQuery: CustomPaginationQueryDto): Promise<PaginationResponseDto<CmsCategoryPublicDto>>;
    findAllPublic(paginationQuery: CustomPaginationQueryDto): Promise<PaginationResponseDto<CmsCategoryPublicDto>>;
}

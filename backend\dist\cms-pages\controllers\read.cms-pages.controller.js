"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ReadCmsPagesPublicController = exports.ReadCmsPagesController = void 0;
const openapi = require("@nestjs/swagger");
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const jwt_auth_guard_1 = require("../../common/guards/jwt-auth.guard");
const read_cms_pages_service_1 = require("../services/read.cms-pages.service");
const cms_pages_entity_1 = require("../entity/cms-pages.entity");
const api_response_dto_1 = require("../../common/dto/api-response.dto");
const custom_pagination_query_dto_1 = require("../../common/dto/custom-pagination-query.dto");
const pagination_response_dto_1 = require("../../common/dto/pagination-response.dto");
const custom_pagination_meta_dto_1 = require("../../common/dto/custom-pagination-meta.dto");
const roles_decorator_1 = require("../../common/decorators/roles.decorator");
const permissions_decorator_1 = require("../../common/decorators/permissions.decorator");
let ReadCmsPagesController = class ReadCmsPagesController {
    cmsPagesService;
    constructor(cmsPagesService) {
        this.cmsPagesService = cmsPagesService;
    }
    async findAll(paginationQuery) {
        const { data, total } = await this.cmsPagesService.findAll(paginationQuery);
        const meta = new custom_pagination_meta_dto_1.CustomPageMetaDto({
            pageQueryDto: paginationQuery,
            itemCount: total,
        });
        return new pagination_response_dto_1.PaginationResponseDto(data, meta);
    }
    async findByStatus(status, paginationQuery) {
        const { data, total } = await this.cmsPagesService.findByStatus(status, paginationQuery);
        const meta = new custom_pagination_meta_dto_1.CustomPageMetaDto({
            pageQueryDto: paginationQuery,
            itemCount: total,
        });
        return new pagination_response_dto_1.PaginationResponseDto(data, meta);
    }
    async findByTemplate(template, paginationQuery) {
        const { data, total } = await this.cmsPagesService.findByTemplate(template, paginationQuery);
        const meta = new custom_pagination_meta_dto_1.CustomPageMetaDto({
            pageQueryDto: paginationQuery,
            itemCount: total,
        });
        return new pagination_response_dto_1.PaginationResponseDto(data, meta);
    }
    async getPublishedPages(paginationQuery) {
        const { data, total } = await this.cmsPagesService.getPublishedPages(paginationQuery);
        const meta = new custom_pagination_meta_dto_1.CustomPageMetaDto({
            pageQueryDto: paginationQuery,
            itemCount: total,
        });
        return new pagination_response_dto_1.PaginationResponseDto(data, meta);
    }
    async getDraftPages(paginationQuery) {
        const { data, total } = await this.cmsPagesService.getDraftPages(paginationQuery);
        const meta = new custom_pagination_meta_dto_1.CustomPageMetaDto({
            pageQueryDto: paginationQuery,
            itemCount: total,
        });
        return new pagination_response_dto_1.PaginationResponseDto(data, meta);
    }
    async getStatistics() {
        return this.cmsPagesService.getStatistics();
    }
    getAvailableTemplates() {
        return this.cmsPagesService.getAvailableTemplates();
    }
    async getPopularTemplatePages(limit) {
        return this.cmsPagesService.getPopularTemplatePages(limit || 10);
    }
    async count(filter) {
        return this.cmsPagesService.count(filter);
    }
    async search(keyword, paginationQuery) {
        const { data, total } = await this.cmsPagesService.search(keyword, paginationQuery);
        const meta = new custom_pagination_meta_dto_1.CustomPageMetaDto({
            pageQueryDto: paginationQuery,
            itemCount: total,
        });
        return new pagination_response_dto_1.PaginationResponseDto(data, meta);
    }
    async findDeleted(paginationQuery) {
        const { data, total } = await this.cmsPagesService.findDeleted(paginationQuery);
        const meta = new custom_pagination_meta_dto_1.CustomPageMetaDto({
            pageQueryDto: paginationQuery,
            itemCount: total,
        });
        return new pagination_response_dto_1.PaginationResponseDto(data, meta);
    }
    async findOne(id, relations) {
        const relationsArray = relations ? relations.split(',').map(r => r.trim()) : [];
        return this.cmsPagesService.findOneOrFail(id, relationsArray);
    }
    async findByBusinessCode(businessCode) {
        return this.cmsPagesService.findByBusinessCodePublic(businessCode);
    }
    async findBySlug(slug) {
        return this.cmsPagesService.findBySlugPublic(slug);
    }
};
exports.ReadCmsPagesController = ReadCmsPagesController;
__decorate([
    (0, common_1.Get)(),
    (0, roles_decorator_1.Roles)('ADMIN', 'EDITOR', 'USER'),
    (0, permissions_decorator_1.Permissions)('cms-page:read'),
    (0, swagger_1.ApiOperation)({ summary: 'Lấy danh sách trang CMS với phân trang' }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Danh sách trang CMS.',
        type: api_response_dto_1.ApiResponseDto,
        schema: {
            properties: {
                data: {
                    type: 'object',
                    properties: {
                        data: {
                            type: 'array',
                            items: { $ref: '#/components/schemas/CmsPageDto' },
                        },
                        meta: {
                            type: 'object',
                            properties: {
                                total: { type: 'number' },
                                page: { type: 'number' },
                                limit: { type: 'number' },
                                totalPages: { type: 'number' },
                            },
                        },
                    },
                },
            },
        },
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.BAD_REQUEST,
        description: 'Tham số không hợp lệ.',
        type: api_response_dto_1.ApiResponseDto,
        schema: { properties: { data: { type: 'object', nullable: true } } },
    }),
    (0, swagger_1.ApiQuery)({ name: 'page', required: false, type: Number, description: 'Số trang' }),
    (0, swagger_1.ApiQuery)({ name: 'limit', required: false, type: Number, description: 'Số lượng bản ghi mỗi trang' }),
    (0, swagger_1.ApiQuery)({ name: 'search', required: false, type: String, description: 'Tìm kiếm theo từ khóa' }),
    openapi.ApiResponse({ status: 200 }),
    __param(0, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [custom_pagination_query_dto_1.CustomPaginationQueryDto]),
    __metadata("design:returntype", Promise)
], ReadCmsPagesController.prototype, "findAll", null);
__decorate([
    (0, common_1.Get)('by-status/:status'),
    (0, roles_decorator_1.Roles)('ADMIN', 'EDITOR', 'USER'),
    (0, permissions_decorator_1.Permissions)('cms-page:read'),
    (0, swagger_1.ApiOperation)({ summary: 'Lấy danh sách trang CMS theo trạng thái' }),
    (0, swagger_1.ApiParam)({
        name: 'status',
        enum: cms_pages_entity_1.CmsPageStatus,
        description: 'Trạng thái trang',
    }),
    (0, swagger_1.ApiQuery)({ name: 'page', required: false, type: Number, description: 'Số trang' }),
    (0, swagger_1.ApiQuery)({ name: 'limit', required: false, type: Number, description: 'Số lượng bản ghi mỗi trang' }),
    (0, swagger_1.ApiQuery)({ name: 'search', required: false, type: String, description: 'Tìm kiếm theo từ khóa' }),
    openapi.ApiResponse({ status: 200 }),
    __param(0, (0, common_1.Param)('status')),
    __param(1, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, custom_pagination_query_dto_1.CustomPaginationQueryDto]),
    __metadata("design:returntype", Promise)
], ReadCmsPagesController.prototype, "findByStatus", null);
__decorate([
    (0, common_1.Get)('by-template/:template'),
    (0, roles_decorator_1.Roles)('ADMIN', 'EDITOR', 'USER'),
    (0, permissions_decorator_1.Permissions)('cms-page:read'),
    (0, swagger_1.ApiOperation)({ summary: 'Lấy danh sách trang CMS theo template' }),
    (0, swagger_1.ApiParam)({
        name: 'template',
        type: String,
        description: 'Template trang',
    }),
    (0, swagger_1.ApiQuery)({ name: 'page', required: false, type: Number, description: 'Số trang' }),
    (0, swagger_1.ApiQuery)({ name: 'limit', required: false, type: Number, description: 'Số lượng bản ghi mỗi trang' }),
    (0, swagger_1.ApiQuery)({ name: 'search', required: false, type: String, description: 'Tìm kiếm theo từ khóa' }),
    openapi.ApiResponse({ status: 200 }),
    __param(0, (0, common_1.Param)('template')),
    __param(1, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, custom_pagination_query_dto_1.CustomPaginationQueryDto]),
    __metadata("design:returntype", Promise)
], ReadCmsPagesController.prototype, "findByTemplate", null);
__decorate([
    (0, common_1.Get)('published'),
    (0, roles_decorator_1.Roles)('ADMIN', 'EDITOR', 'USER'),
    (0, permissions_decorator_1.Permissions)('cms-page:read'),
    (0, swagger_1.ApiOperation)({ summary: 'Lấy danh sách trang đã được xuất bản' }),
    (0, swagger_1.ApiQuery)({ name: 'page', required: false, type: Number, description: 'Số trang' }),
    (0, swagger_1.ApiQuery)({ name: 'limit', required: false, type: Number, description: 'Số lượng bản ghi mỗi trang' }),
    (0, swagger_1.ApiQuery)({ name: 'search', required: false, type: String, description: 'Tìm kiếm theo từ khóa' }),
    openapi.ApiResponse({ status: 200 }),
    __param(0, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [custom_pagination_query_dto_1.CustomPaginationQueryDto]),
    __metadata("design:returntype", Promise)
], ReadCmsPagesController.prototype, "getPublishedPages", null);
__decorate([
    (0, common_1.Get)('draft'),
    (0, roles_decorator_1.Roles)('ADMIN', 'EDITOR'),
    (0, permissions_decorator_1.Permissions)('cms-page:read'),
    (0, swagger_1.ApiOperation)({ summary: 'Lấy danh sách trang draft' }),
    (0, swagger_1.ApiQuery)({ name: 'page', required: false, type: Number, description: 'Số trang' }),
    (0, swagger_1.ApiQuery)({ name: 'limit', required: false, type: Number, description: 'Số lượng bản ghi mỗi trang' }),
    openapi.ApiResponse({ status: 200 }),
    __param(0, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [custom_pagination_query_dto_1.CustomPaginationQueryDto]),
    __metadata("design:returntype", Promise)
], ReadCmsPagesController.prototype, "getDraftPages", null);
__decorate([
    (0, common_1.Get)('statistics'),
    (0, roles_decorator_1.Roles)('ADMIN', 'EDITOR'),
    (0, permissions_decorator_1.Permissions)('cms-page:read'),
    (0, swagger_1.ApiOperation)({ summary: 'Lấy thống kê trang CMS' }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Thống kê trang CMS.',
        type: api_response_dto_1.ApiResponseDto,
        schema: {
            properties: {
                data: {
                    type: 'object',
                    properties: {
                        total: { type: 'number' },
                        byStatus: {
                            type: 'object',
                            additionalProperties: { type: 'number' },
                        },
                        byTemplate: {
                            type: 'object',
                            additionalProperties: { type: 'number' },
                        },
                        totalWordCount: { type: 'number' },
                        averageWordCount: { type: 'number' },
                    },
                },
            },
        },
    }),
    openapi.ApiResponse({ status: 200 }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], ReadCmsPagesController.prototype, "getStatistics", null);
__decorate([
    (0, common_1.Get)('templates'),
    (0, roles_decorator_1.Roles)('ADMIN', 'EDITOR', 'USER'),
    (0, permissions_decorator_1.Permissions)('cms-page:read'),
    (0, swagger_1.ApiOperation)({ summary: 'Lấy danh sách template có sẵn' }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Danh sách template có sẵn.',
        type: api_response_dto_1.ApiResponseDto,
        schema: {
            properties: {
                data: {
                    type: 'array',
                    items: { type: 'string' },
                },
            },
        },
    }),
    openapi.ApiResponse({ status: 200, type: [String] }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Array)
], ReadCmsPagesController.prototype, "getAvailableTemplates", null);
__decorate([
    (0, common_1.Get)('popular-template'),
    (0, roles_decorator_1.Roles)('ADMIN', 'EDITOR', 'USER'),
    (0, permissions_decorator_1.Permissions)('cms-page:read'),
    (0, swagger_1.ApiOperation)({ summary: 'Lấy trang theo template phổ biến' }),
    (0, swagger_1.ApiQuery)({
        name: 'limit',
        required: false,
        type: Number,
        description: 'Số lượng trang cần lấy',
        example: 10,
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Danh sách trang theo template phổ biến.',
        type: api_response_dto_1.ApiResponseDto,
        schema: {
            properties: {
                data: {
                    type: 'array',
                    items: { $ref: '#/components/schemas/CmsPageDto' },
                },
            },
        },
    }),
    openapi.ApiResponse({ status: 200, type: [require("../dto/cms-page.dto").CmsPageDto] }),
    __param(0, (0, common_1.Query)('limit')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", Promise)
], ReadCmsPagesController.prototype, "getPopularTemplatePages", null);
__decorate([
    (0, common_1.Get)('count'),
    (0, roles_decorator_1.Roles)('ADMIN', 'EDITOR', 'USER'),
    (0, permissions_decorator_1.Permissions)('cms-page:read'),
    (0, swagger_1.ApiOperation)({ summary: 'Đếm số lượng trang CMS' }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Số lượng trang CMS.',
        type: api_response_dto_1.ApiResponseDto,
        schema: {
            properties: {
                data: { type: 'number' },
            },
        },
    }),
    (0, swagger_1.ApiQuery)({ name: 'filter', required: false, type: String, description: 'Bộ lọc' }),
    openapi.ApiResponse({ status: 200, type: Number }),
    __param(0, (0, common_1.Query)('filter')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], ReadCmsPagesController.prototype, "count", null);
__decorate([
    (0, common_1.Get)('search'),
    (0, roles_decorator_1.Roles)('ADMIN', 'EDITOR', 'USER'),
    (0, permissions_decorator_1.Permissions)('cms-page:read'),
    (0, swagger_1.ApiOperation)({ summary: 'Tìm kiếm trang CMS' }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Kết quả tìm kiếm trang CMS.',
        type: api_response_dto_1.ApiResponseDto,
        schema: {
            properties: {
                data: {
                    type: 'object',
                    properties: {
                        data: {
                            type: 'array',
                            items: { $ref: '#/components/schemas/CmsPageDto' },
                        },
                        meta: {
                            type: 'object',
                            properties: {
                                total: { type: 'number' },
                                page: { type: 'number' },
                                limit: { type: 'number' },
                                totalPages: { type: 'number' },
                            },
                        },
                    },
                },
            },
        },
    }),
    (0, swagger_1.ApiQuery)({ name: 'keyword', required: true, type: String, description: 'Từ khóa tìm kiếm' }),
    (0, swagger_1.ApiQuery)({ name: 'page', required: false, type: Number, description: 'Số trang' }),
    (0, swagger_1.ApiQuery)({ name: 'limit', required: false, type: Number, description: 'Số lượng bản ghi mỗi trang' }),
    openapi.ApiResponse({ status: 200 }),
    __param(0, (0, common_1.Query)('keyword')),
    __param(1, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, custom_pagination_query_dto_1.CustomPaginationQueryDto]),
    __metadata("design:returntype", Promise)
], ReadCmsPagesController.prototype, "search", null);
__decorate([
    (0, common_1.Get)('deleted'),
    (0, roles_decorator_1.Roles)('ADMIN'),
    (0, permissions_decorator_1.Permissions)('cms-page:read'),
    (0, swagger_1.ApiOperation)({ summary: 'Lấy danh sách trang CMS đã xóa' }),
    (0, swagger_1.ApiQuery)({ name: 'page', required: false, type: Number, description: 'Số trang' }),
    (0, swagger_1.ApiQuery)({ name: 'limit', required: false, type: Number, description: 'Số lượng bản ghi mỗi trang' }),
    openapi.ApiResponse({ status: 200 }),
    __param(0, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [custom_pagination_query_dto_1.CustomPaginationQueryDto]),
    __metadata("design:returntype", Promise)
], ReadCmsPagesController.prototype, "findDeleted", null);
__decorate([
    (0, common_1.Get)(':id'),
    (0, roles_decorator_1.Roles)('ADMIN', 'EDITOR', 'USER'),
    (0, permissions_decorator_1.Permissions)('cms-page:read'),
    (0, swagger_1.ApiOperation)({ summary: 'Lấy thông tin chi tiết trang CMS' }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Thông tin chi tiết trang CMS.',
        type: api_response_dto_1.ApiResponseDto,
        schema: {
            properties: {
                data: { $ref: '#/components/schemas/CmsPageDto' },
            },
        },
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.NOT_FOUND,
        description: 'Không tìm thấy trang CMS.',
        type: api_response_dto_1.ApiResponseDto,
        schema: { properties: { data: { type: 'object', nullable: true } } },
    }),
    (0, swagger_1.ApiParam)({ name: 'id', type: String, description: 'ID của trang CMS' }),
    openapi.ApiResponse({ status: 200, type: Object }),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __param(1, (0, common_1.Query)('relations')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String]),
    __metadata("design:returntype", Promise)
], ReadCmsPagesController.prototype, "findOne", null);
__decorate([
    (0, common_1.Get)('business-code/:businessCode'),
    (0, roles_decorator_1.Roles)('ADMIN', 'EDITOR', 'USER'),
    (0, permissions_decorator_1.Permissions)('cms-page:read'),
    (0, swagger_1.ApiOperation)({ summary: 'Lấy thông tin trang CMS theo business code' }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Thông tin trang CMS.',
        type: api_response_dto_1.ApiResponseDto,
        schema: {
            properties: {
                data: { $ref: '#/components/schemas/CmsPageDto' },
            },
        },
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.NOT_FOUND,
        description: 'Không tìm thấy trang CMS.',
        type: api_response_dto_1.ApiResponseDto,
        schema: { properties: { data: { type: 'object', nullable: true } } },
    }),
    (0, swagger_1.ApiParam)({ name: 'businessCode', type: String, description: 'Business code của trang CMS' }),
    openapi.ApiResponse({ status: 200, type: Object }),
    __param(0, (0, common_1.Param)('businessCode')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], ReadCmsPagesController.prototype, "findByBusinessCode", null);
__decorate([
    (0, common_1.Get)('slug/:slug'),
    (0, roles_decorator_1.Roles)('ADMIN', 'EDITOR', 'USER'),
    (0, permissions_decorator_1.Permissions)('cms-page:read'),
    (0, swagger_1.ApiOperation)({ summary: 'Lấy thông tin trang CMS theo slug' }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Thông tin trang CMS.',
        type: api_response_dto_1.ApiResponseDto,
        schema: {
            properties: {
                data: { $ref: '#/components/schemas/CmsPageDto' },
            },
        },
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.NOT_FOUND,
        description: 'Không tìm thấy trang CMS.',
        type: api_response_dto_1.ApiResponseDto,
        schema: { properties: { data: { type: 'object', nullable: true } } },
    }),
    (0, swagger_1.ApiParam)({ name: 'slug', type: String, description: 'Slug của trang CMS' }),
    openapi.ApiResponse({ status: 200, type: Object }),
    __param(0, (0, common_1.Param)('slug')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], ReadCmsPagesController.prototype, "findBySlug", null);
exports.ReadCmsPagesController = ReadCmsPagesController = __decorate([
    (0, swagger_1.ApiTags)('cms-pages'),
    (0, swagger_1.ApiBearerAuth)(),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, common_1.Controller)('cms/pages'),
    __metadata("design:paramtypes", [read_cms_pages_service_1.ReadCmsPagesService])
], ReadCmsPagesController);
let ReadCmsPagesPublicController = class ReadCmsPagesPublicController {
    cmsPagesService;
    constructor(cmsPagesService) {
        this.cmsPagesService = cmsPagesService;
    }
    async test() {
        return {
            message: 'CMS Pages module đang hoạt động!',
            timestamp: new Date().toISOString(),
        };
    }
    async getPublishedPages(paginationQuery) {
        const { data, total } = await this.cmsPagesService.getPublishedPages(paginationQuery);
        const meta = new custom_pagination_meta_dto_1.CustomPageMetaDto({
            pageQueryDto: paginationQuery,
            itemCount: total,
        });
        return new pagination_response_dto_1.PaginationResponseDto(data, meta);
    }
};
exports.ReadCmsPagesPublicController = ReadCmsPagesPublicController;
__decorate([
    (0, common_1.Get)('test'),
    (0, swagger_1.ApiOperation)({ summary: 'Test endpoint để kiểm tra module hoạt động' }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Test thành công.',
        schema: {
            properties: {
                message: { type: 'string' },
                timestamp: { type: 'string' },
            },
        },
    }),
    openapi.ApiResponse({ status: 200 }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], ReadCmsPagesPublicController.prototype, "test", null);
__decorate([
    (0, common_1.Get)('published'),
    (0, swagger_1.ApiOperation)({ summary: 'Lấy danh sách trang đã được xuất bản (public)' }),
    (0, swagger_1.ApiQuery)({ name: 'page', required: false, type: Number, description: 'Số trang' }),
    (0, swagger_1.ApiQuery)({ name: 'limit', required: false, type: Number, description: 'Số lượng bản ghi mỗi trang' }),
    openapi.ApiResponse({ status: 200 }),
    __param(0, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [custom_pagination_query_dto_1.CustomPaginationQueryDto]),
    __metadata("design:returntype", Promise)
], ReadCmsPagesPublicController.prototype, "getPublishedPages", null);
exports.ReadCmsPagesPublicController = ReadCmsPagesPublicController = __decorate([
    (0, swagger_1.ApiTags)('cms-pages/public'),
    (0, common_1.Controller)('cms/pages/public'),
    __metadata("design:paramtypes", [read_cms_pages_service_1.ReadCmsPagesService])
], ReadCmsPagesPublicController);
//# sourceMappingURL=read.cms-pages.controller.js.map
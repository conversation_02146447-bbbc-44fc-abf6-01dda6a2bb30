(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/hooks/use-mobile.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "useIsMobile": (()=>useIsMobile)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$opentelemetry$2b$_ee4fdf7136c0ae57e36e5bb24f18c6a1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/next@15.3.0_@opentelemetry+_ee4fdf7136c0ae57e36e5bb24f18c6a1/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var _s = __turbopack_context__.k.signature();
;
const MOBILE_BREAKPOINT = 768;
function useIsMobile() {
    _s();
    const [isMobile, setIsMobile] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$opentelemetry$2b$_ee4fdf7136c0ae57e36e5bb24f18c6a1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(undefined);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$opentelemetry$2b$_ee4fdf7136c0ae57e36e5bb24f18c6a1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "useIsMobile.useEffect": ()=>{
            const mql = window.matchMedia(`(max-width: ${MOBILE_BREAKPOINT - 1}px)`);
            const onChange = {
                "useIsMobile.useEffect.onChange": ()=>{
                    setIsMobile(window.innerWidth < MOBILE_BREAKPOINT);
                }
            }["useIsMobile.useEffect.onChange"];
            mql.addEventListener("change", onChange);
            setIsMobile(window.innerWidth < MOBILE_BREAKPOINT);
            return ({
                "useIsMobile.useEffect": ()=>mql.removeEventListener("change", onChange)
            })["useIsMobile.useEffect"];
        }
    }["useIsMobile.useEffect"], []);
    return !!isMobile;
}
_s(useIsMobile, "D6B2cPXNCaIbeOx+abFr1uxLRM0=");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/lib/response.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
// API Response DTO
__turbopack_context__.s({
    "SortOrder": (()=>SortOrder),
    "createPaginationQuery": (()=>createPaginationQuery),
    "getDataFromResponse": (()=>getDataFromResponse),
    "handleApiError": (()=>handleApiError),
    "isSuccessResponse": (()=>isSuccessResponse),
    "parseApiResponse": (()=>parseApiResponse),
    "parsePaginationResponse": (()=>parsePaginationResponse)
});
var SortOrder = /*#__PURE__*/ function(SortOrder) {
    SortOrder["ASC"] = "ASC";
    SortOrder["DESC"] = "DESC";
    return SortOrder;
}({});
const createPaginationQuery = (query)=>{
    const params = new URLSearchParams();
    if (query.page) params.append('page', query.page.toString());
    if (query.limit) params.append('limit', query.limit.toString());
    if (query.search) params.append('search', query.search);
    if (query.sortBy) params.append('sortBy', query.sortBy);
    if (query.sortOrder) params.append('sortOrder', query.sortOrder);
    return params.toString();
};
const parseApiResponse = (response)=>{
    return {
        success: response.success,
        statusCode: response.statusCode,
        message: response.message,
        data: response.data,
        timestamp: response.timestamp,
        path: response.path
    };
};
const parsePaginationResponse = (response)=>{
    return {
        data: response.data,
        meta: {
            page: response.meta.page,
            limit: response.meta.limit,
            itemCount: response.meta.itemCount,
            pageCount: response.meta.pageCount,
            hasPreviousPage: response.meta.hasPreviousPage,
            hasNextPage: response.meta.hasNextPage
        }
    };
};
const isSuccessResponse = (response)=>{
    return response.success === true && response.statusCode >= 200 && response.statusCode < 300;
};
const getDataFromResponse = (response)=>{
    return response.data || null;
};
const handleApiError = (error)=>{
    if (error.response && error.response.data) {
        const apiError = error.response.data;
        return apiError.message || 'Có lỗi xảy ra khi gọi API';
    }
    if (error.message) {
        if (error.message.includes('NOT_FOUND')) {
            return 'Không tìm thấy API endpoint. Vui lòng kiểm tra cấu hình API.';
        } else if (error.message.includes('Network Error')) {
            return 'Không thể kết nối đến máy chủ. Vui lòng kiểm tra kết nối mạng.';
        }
        return error.message;
    }
    return 'Có lỗi xảy ra khi gọi API';
};
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/lib/constants.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
/**
 * Constants cho frontend application
 */ // API Pagination Limits (phải khớp với backend)
__turbopack_context__.s({
    "API_ENDPOINTS": (()=>API_ENDPOINTS),
    "API_PAGINATION": (()=>API_PAGINATION),
    "CURRENCY": (()=>CURRENCY),
    "DATE_FORMATS": (()=>DATE_FORMATS),
    "ERROR_MESSAGES": (()=>ERROR_MESSAGES),
    "FILE_UPLOAD": (()=>FILE_UPLOAD),
    "STORAGE_KEYS": (()=>STORAGE_KEYS),
    "SUCCESS_MESSAGES": (()=>SUCCESS_MESSAGES),
    "TABLE_SETTINGS": (()=>TABLE_SETTINGS),
    "TIMEOUTS": (()=>TIMEOUTS),
    "VALIDATION": (()=>VALIDATION)
});
const API_PAGINATION = {
    MAX_LIMIT: 100,
    DEFAULT_LIMIT: 20,
    DEFAULT_PAGE: 1,
    // Limits cho các endpoint đặc biệt
    CUSTOM_MAX_LIMIT: 300,
    SEARCH_LIMIT: 10
};
const VALIDATION = {
    PASSWORD_MIN_LENGTH: 8,
    USERNAME_MIN_LENGTH: 3,
    USERNAME_MAX_LENGTH: 20,
    PHONE_REGEX: /^[0-9]{10,11}$/,
    EMAIL_REGEX: /^[^\s@]+@[^\s@]+\.[^\s@]+$/
};
const ERROR_MESSAGES = {
    LIMIT_EXCEEDED: 'Số lượng yêu cầu vượt quá giới hạn cho phép',
    INVALID_PAGE: 'Số trang không hợp lệ',
    NETWORK_ERROR: 'Lỗi kết nối mạng',
    UNAUTHORIZED: 'Bạn không có quyền truy cập',
    FORBIDDEN: 'Truy cập bị từ chối',
    NOT_FOUND: 'Không tìm thấy tài nguyên',
    SERVER_ERROR: 'Lỗi máy chủ'
};
const SUCCESS_MESSAGES = {
    CREATED: 'Tạo thành công',
    UPDATED: 'Cập nhật thành công',
    DELETED: 'Xóa thành công',
    SAVED: 'Lưu thành công'
};
const STORAGE_KEYS = {
    ACCESS_TOKEN: 'accessToken',
    REFRESH_TOKEN: 'refreshToken',
    USER_DATA: 'userData',
    THEME: 'theme',
    LANGUAGE: 'language'
};
const API_ENDPOINTS = {
    AUTH: '/auth',
    USERS: '/users',
    BANKS: '/banks',
    TRANSACTIONS: '/transactions',
    WALLETS: '/wallets',
    ORDERS: '/orders',
    PRODUCTS: '/products'
};
const TABLE_SETTINGS = {
    DEFAULT_PAGE_SIZE: 20,
    PAGE_SIZE_OPTIONS: [
        10,
        20,
        50,
        100
    ],
    MAX_PAGE_SIZE: API_PAGINATION.MAX_LIMIT
};
const FILE_UPLOAD = {
    MAX_SIZE: 5 * 1024 * 1024,
    ALLOWED_TYPES: [
        'image/jpeg',
        'image/png',
        'image/gif',
        'application/pdf'
    ],
    ALLOWED_EXTENSIONS: [
        '.jpg',
        '.jpeg',
        '.png',
        '.gif',
        '.pdf'
    ]
};
const DATE_FORMATS = {
    DISPLAY: 'dd/MM/yyyy',
    DISPLAY_WITH_TIME: 'dd/MM/yyyy HH:mm',
    API: 'yyyy-MM-dd',
    API_WITH_TIME: "yyyy-MM-dd'T'HH:mm:ss.SSSxxx"
};
const CURRENCY = {
    VND: 'VND',
    USD: 'USD',
    DEFAULT_EXCHANGE_RATE: 24000
};
const TIMEOUTS = {
    API_REQUEST: 30000,
    DEBOUNCE_SEARCH: 300,
    TOAST_DURATION: 5000,
    POLLING_INTERVAL: 5000
};
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
}]);

//# sourceMappingURL=_9ab520ed._.js.map
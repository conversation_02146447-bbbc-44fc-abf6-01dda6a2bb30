import { Repository, DataSource } from 'typeorm';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { BaseEcomProductCategoriesService } from './base.ecom-product-categories.service';
import { SlugEcomProductCategoriesService } from './slug.ecom-product-categories.service';
import { EcomProductCategories } from '../entity/ecom-product-categories.entity';
import { UpdateEcomProductCategoryDto } from '../dto/update-ecom-product-category.dto';
import { EcomProductCategoryDto } from '../dto/ecom-product-category.dto';
export declare class UpdateEcomProductCategoriesService extends BaseEcomProductCategoriesService {
    protected readonly categoryRepository: Repository<EcomProductCategories>;
    protected readonly dataSource: DataSource;
    protected readonly eventEmitter: EventEmitter2;
    private readonly slugService;
    constructor(categoryRepository: Repository<EcomProductCategories>, dataSource: DataSource, eventEmitter: EventEmitter2, slugService: SlugEcomProductCategoriesService);
    update(id: string, updateDto: UpdateEcomProductCategoryDto, userId: string): Promise<EcomProductCategoryDto | null>;
    private isChildOf;
}

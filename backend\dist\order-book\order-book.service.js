"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var OrderBookService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.OrderBookService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const class_transformer_1 = require("class-transformer");
const typeorm_2 = require("typeorm");
const order_book_dto_1 = require("./dto/order-book.dto");
const order_book_entity_1 = require("./entities/order-book.entity");
const services_1 = require("./services");
let OrderBookService = OrderBookService_1 = class OrderBookService {
    orderBookRepository;
    createService;
    readService;
    updateService;
    deleteService;
    logger = new common_1.Logger(OrderBookService_1.name);
    validRelations = ['user', 'token', 'contract'];
    constructor(orderBookRepository, createService, readService, updateService, deleteService) {
        this.orderBookRepository = orderBookRepository;
        this.createService = createService;
        this.readService = readService;
        this.updateService = updateService;
        this.deleteService = deleteService;
    }
    convertToDto(entity) {
        if (!entity) {
            throw new common_1.NotFoundException('Order not found');
        }
        return (0, class_transformer_1.plainToInstance)(order_book_dto_1.OrderBookDto, entity, {
            excludeExtraneousValues: true,
        });
    }
    convertToDtoList(entities) {
        if (!entities?.length)
            return [];
        return entities.map((entity) => this.convertToDto(entity));
    }
    async findById(id) {
        const entity = await this.orderBookRepository.findOne({
            where: { id, isDeleted: false },
            relations: this.validRelations,
        });
        if (!entity) {
            throw new common_1.NotFoundException(`Order with ID ${id} not found`);
        }
        return entity;
    }
    async create(createOrderBookDto, userId) {
        return this.createService.create(createOrderBookDto, userId);
    }
    async bulkCreate(createOrderBookDtos, userId) {
        return this.createService.bulkCreate(createOrderBookDtos, userId);
    }
    async duplicate(id, userId) {
        return this.createService.duplicate(id, userId);
    }
    async findAll(params) {
        return this.readService.findAll(params);
    }
    async search(user, keyword, params) {
        return this.readService.search(keyword, params);
    }
    async count(filter) {
        return this.readService.count(filter);
    }
    async getStatistics(filter) {
        return this.readService.getStatistics(filter);
    }
    async findOne(id, relations = []) {
        return this.readService.findOne(id, relations);
    }
    async findDeleted(params) {
        return this.readService.findDeleted(params);
    }
    async export() {
        return this.readService.export();
    }
    async update(id, updateOrderBookDto, userId) {
        return this.updateService.update(id, updateOrderBookDto, userId);
    }
    async bulkUpdate(updateOrderBookDtos, userId) {
        return this.updateService.bulkUpdate(updateOrderBookDtos, userId);
    }
    async toggleStatus(id, userId) {
        return this.updateService.toggleStatus(id, userId);
    }
    async updateOrderStatusPending(orderBookId, userId) {
        return this.updateService.updateOrderStatusPending(orderBookId, userId);
    }
    async updateOrderStatusCompleted(orderBookId, userId, settlementData) {
        return this.updateService.updateOrderStatusCompleted(orderBookId, userId, settlementData);
    }
    async updateOrderStatusWaitPayment(orderBookId, userId) {
        return this.updateService.updateOrderStatusWaitPayment(orderBookId, userId);
    }
    async remove(id) {
        return this.deleteService.remove(id);
    }
    async bulkDelete(ids) {
        return this.deleteService.bulkDelete(ids);
    }
    async softDelete(id, userId) {
        return this.deleteService.softDelete(id, userId);
    }
    async bulkSoftDelete(ids, userId) {
        return this.deleteService.bulkSoftDelete(ids, userId);
    }
    async restore(id, userId) {
        return this.deleteService.restore(id, userId);
    }
    async cleanupOldRecords(days) {
        return this.deleteService.cleanupOldRecords(days);
    }
    async createTokenWithdrawal(createOrderBookDto, userId) {
        try {
            const orderBook = await this.createService.create(createOrderBookDto, userId);
            return this.readService.findOne(orderBook.id, [
                'details',
                'details.token',
                'user',
            ]);
        }
        catch (error) {
            if (error instanceof common_1.NotFoundException) {
                throw error;
            }
            if (error instanceof common_1.BadRequestException) {
                throw error;
            }
            throw new common_1.InternalServerErrorException(`Lỗi khi tạo lệnh rút token: ${error.message}`);
        }
    }
    async updateOrderStatusCancelled(id, userId) {
        return this.updateService.updateOrderStatusCancelled(id, userId);
    }
    async extendSettlement(id, userId) {
        return this.updateService.extendSettlement(id, userId);
    }
    async approveOrder(id, userId) {
        return this.updateService.approveOrder(id, userId);
    }
};
exports.OrderBookService = OrderBookService;
exports.OrderBookService = OrderBookService = OrderBookService_1 = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(order_book_entity_1.OrderBook)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        services_1.CreateOrderBookService,
        services_1.ReadOrderBookService,
        services_1.UpdateOrderBookService,
        services_1.DeleteOrderBookService])
], OrderBookService);
//# sourceMappingURL=order-book.service.js.map
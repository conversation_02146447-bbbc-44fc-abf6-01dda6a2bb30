"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.DeleteCmsMenusController = exports.UpdateCmsMenusController = exports.ReadCmsMenusController = exports.CreateCmsMenusController = void 0;
var create_cms_menus_controller_1 = require("./create.cms-menus.controller");
Object.defineProperty(exports, "CreateCmsMenusController", { enumerable: true, get: function () { return create_cms_menus_controller_1.CreateCmsMenusController; } });
var read_cms_menus_controller_1 = require("./read.cms-menus.controller");
Object.defineProperty(exports, "ReadCmsMenusController", { enumerable: true, get: function () { return read_cms_menus_controller_1.ReadCmsMenusController; } });
var update_cms_menus_controller_1 = require("./update.cms-menus.controller");
Object.defineProperty(exports, "UpdateCmsMenusController", { enumerable: true, get: function () { return update_cms_menus_controller_1.UpdateCmsMenusController; } });
var delete_cms_menus_controller_1 = require("./delete.cms-menus.controller");
Object.defineProperty(exports, "DeleteCmsMenusController", { enumerable: true, get: function () { return delete_cms_menus_controller_1.DeleteCmsMenusController; } });
//# sourceMappingURL=index.js.map
import { Repository } from 'typeorm';
import { EcomProduct } from '../entity/ecom-products.entity';
import { BaseSlugService } from '../../common/services/base-slug.service';
import { UnifiedSlugService } from '../../common/services/unified-slug.service';
export declare class SlugEcomProductsService extends BaseSlugService<EcomProduct> {
    protected readonly unifiedSlugService: UnifiedSlugService;
    constructor(productRepository: Repository<EcomProduct>, unifiedSlugService: UnifiedSlugService);
    protected getSlugFieldName(): string;
    protected getTextFieldName(): string;
    protected getWhereConditions(excludeId?: string): any;
    protected generateFallbackSlug(): string;
    generateSlugFromProductName(productName: string): string;
    generateUniqueSlugForCreate(productName: string, providedSlug?: string): Promise<string>;
    generateUniqueSlugForUpdate(productName: string, currentId: string, providedSlug?: string, currentSlug?: string): Promise<string | null>;
    isSlugExists(slug: string, excludeId?: string): Promise<boolean>;
    generateBatchSlugsForProducts(products: Array<{
        productName: string;
    }>): Promise<Map<number, string>>;
    generateUniqueSlugFromText(text: string, existingSlugs: string[]): string;
    getExistingSlugs(excludeId?: string): Promise<string[]>;
    validateSlug(slug: string): boolean;
    sanitizeSlug(slug: string): string;
    ensureValidSlug(slug: string, fallbackText?: string): string;
    generateDuplicateSlug(originalProductName: string, suffix?: string): Promise<string>;
    generateSlugFromProductCode(productCode: string): string;
    generateSmartSlug(productName?: string, productCode?: string): string;
    updateMissingSlugs(): Promise<number>;
}

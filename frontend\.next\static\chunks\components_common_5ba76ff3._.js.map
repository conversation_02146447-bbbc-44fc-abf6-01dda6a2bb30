{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/bloomgoo/gold-exchange-platform/frontend/components/common/SilverPriceIndicator.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { usePolygonForex } from '@/services/websocket/polygon-forex-socket.service';\r\nimport { Badge } from '@/components/ui/badge';\r\n\r\nexport function SilverPriceIndicator() {\r\n  const {\r\n    currentQuote,\r\n    isMockData\r\n  } = usePolygonForex();\r\n\r\n  // Format giá\r\n  const formatPrice = (price?: number) => {\r\n    if (price === undefined || price === null) return '---.----';\r\n    return price.toFixed(4);\r\n  };\r\n\r\n  // Format giá VND\r\n  const formatVND = (price?: number) => {\r\n    if (price === undefined || price === null) return '---,---';\r\n    return price.toLocaleString('vi-VN');\r\n  };\r\n\r\n  // Xác định màu sắc cho chênh lệch giá\r\n  const getSpreadColor = (spread?: number) => {\r\n    if (spread === undefined || spread === null) return 'bg-gray-500';\r\n    if (spread > 0.1) return 'bg-red-500';\r\n    if (spread < 0.05) return 'bg-green-500';\r\n    return 'bg-yellow-500';\r\n  };\r\n\r\n  return (\r\n    <div className=\"flex items-center gap-2\">\r\n      <Badge variant=\"outline\" className=\"flex items-center gap-1 py-1\">\r\n        <span className=\"text-xs font-medium\">AXGUSD:</span>\r\n        <span className=\"text-xs font-bold\">{formatPrice(currentQuote?.bidPrice)}</span>\r\n        {currentQuote?.spread !== undefined && (\r\n          <span className={`inline-block w-2 h-2 rounded-full ${getSpreadColor(currentQuote?.spread)}`} />\r\n        )}\r\n      </Badge>\r\n      \r\n      {isMockData && (\r\n        <Badge variant=\"outline\" className=\"bg-yellow-50 text-yellow-700 border-yellow-200 text-xs\">\r\n          Mẫu\r\n        </Badge>\r\n      )}\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;;;AAHA;;;AAKO,SAAS;;IACd,MAAM,EACJ,YAAY,EACZ,UAAU,EACX,GAAG,CAAA,GAAA,iKAAA,CAAA,kBAAe,AAAD;IAElB,aAAa;IACb,MAAM,cAAc,CAAC;QACnB,IAAI,UAAU,aAAa,UAAU,MAAM,OAAO;QAClD,OAAO,MAAM,OAAO,CAAC;IACvB;IAEA,iBAAiB;IACjB,MAAM,YAAY,CAAC;QACjB,IAAI,UAAU,aAAa,UAAU,MAAM,OAAO;QAClD,OAAO,MAAM,cAAc,CAAC;IAC9B;IAEA,sCAAsC;IACtC,MAAM,iBAAiB,CAAC;QACtB,IAAI,WAAW,aAAa,WAAW,MAAM,OAAO;QACpD,IAAI,SAAS,KAAK,OAAO;QACzB,IAAI,SAAS,MAAM,OAAO;QAC1B,OAAO;IACT;IAEA,qBACE,sSAAC;QAAI,WAAU;;0BACb,sSAAC,6HAAA,CAAA,QAAK;gBAAC,SAAQ;gBAAU,WAAU;;kCACjC,sSAAC;wBAAK,WAAU;kCAAsB;;;;;;kCACtC,sSAAC;wBAAK,WAAU;kCAAqB,YAAY,cAAc;;;;;;oBAC9D,cAAc,WAAW,2BACxB,sSAAC;wBAAK,WAAW,CAAC,kCAAkC,EAAE,eAAe,cAAc,SAAS;;;;;;;;;;;;YAI/F,4BACC,sSAAC,6HAAA,CAAA,QAAK;gBAAC,SAAQ;gBAAU,WAAU;0BAAyD;;;;;;;;;;;;AAMpG;GA3CgB;;QAIV,iKAAA,CAAA,kBAAe;;;KAJL", "debugId": null}}, {"offset": {"line": 107, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/bloomgoo/gold-exchange-platform/frontend/components/common/data-table/data-table.tsx"], "sourcesContent": ["\"use client\"\r\n\r\n/**\r\n * Component DataTable cung cấp bảng dữ liệu với các tính năng:\r\n * - Header cố định khi cuộn\r\n * - Cột cố định (sticky columns) ở bên trái và bên phải\r\n * - Hỗ trợ đầy đủ các tính năng của TanStack Table\r\n *\r\n * Để sử dụng sticky columns, hãy thêm thuộc tính meta vào định nghĩa cột:\r\n * ```\r\n * {\r\n *   id: 'actions',\r\n *   meta: {\r\n *     isSticky: true,\r\n *     position: 'right' // hoặc 'left'\r\n *   }\r\n * }\r\n * ```\r\n *\r\n * Xem thêm hướng dẫn chi tiết trong file README.md\r\n */\r\n\r\nimport { flexRender, Table as TableType } from \"@tanstack/react-table\";\r\n\r\nimport { cn } from \"@/lib/utils\";\r\n\r\n// Định nghĩa kiểu dữ liệu cho thuộc tính meta của cột\r\ndeclare module '@tanstack/react-table' {\r\n  interface ColumnMeta<TData, TValue> {\r\n    isSticky?: boolean;\r\n    position?: 'left' | 'right';\r\n    header?: string;\r\n  }\r\n}\r\n\r\ninterface DataTableProps<TData> {\r\n    table: TableType<TData>\r\n    className?: string\r\n    isLoading?: boolean\r\n}\r\n\r\nexport function DataTable<TData>({\r\n    table,\r\n    className,\r\n    isLoading = false,\r\n}: DataTableProps<TData>) {\r\n    return (\r\n        <div className={cn(\"w-full flex flex-col h-full\", className)}>\r\n            {/* Table Container with border */}\r\n            <div className=\"border flex flex-col flex-1 overflow-hidden\">\r\n                {/* Single table with sticky header */}\r\n                <div className=\"table-scroll-container\">\r\n                    <div className=\"table-viewport\">\r\n                        <table className=\"data-table\">\r\n                            <thead className=\"sticky-header\">\r\n                                {table.getHeaderGroups().map((headerGroup) => (\r\n                                    <tr key={headerGroup.id}>\r\n                                        {headerGroup.headers.map((header, index) => {\r\n                                            // Determine if this is the first column (checkbox)\r\n                                            const isFirstColumn = index === 0;\r\n                                            // Determine if this column has sticky metadata\r\n                                            const hasSticky = header.column.columnDef.meta?.isSticky;\r\n\r\n                                            return (\r\n                                                <th\r\n                                                    key={header.id}\r\n                                                    style={{\r\n                                                        width: header.getSize(),\r\n                                                        minWidth: header.getSize(),\r\n                                                        maxWidth: header.getSize(),\r\n                                                        ...(hasSticky && {\r\n                                                            position: 'sticky',\r\n                                                            [header.column.columnDef.meta?.position === 'right' ? 'right' : 'left']: 0,\r\n                                                            zIndex: 60,\r\n                                                            boxShadow: header.column.columnDef.meta?.position === 'right'\r\n                                                                ? '-5px 0 5px -5px rgba(0,0,0,0.1)'\r\n                                                                : '5px 0 5px -5px rgba(0,0,0,0.1)'\r\n                                                        }),\r\n                                                        // Áp dụng kiểu dáng sticky cho cột đầu tiên (checkbox)\r\n                                                        ...(isFirstColumn && !hasSticky && {\r\n                                                            position: 'sticky',\r\n                                                            left: 0,\r\n                                                            zIndex: 60,\r\n                                                            boxShadow: '5px 0 5px -5px rgba(0,0,0,0.1)'\r\n                                                        })\r\n                                                    }}\r\n                                                    className={cn(\r\n                                                        \"bg-background py-2 text-left align-middle font-medium text-muted-foreground\",\r\n                                                        hasSticky && `sticky-${header.column.columnDef.meta?.position || 'left'}`,\r\n                                                        isFirstColumn && !hasSticky && \"sticky-left\",\r\n                                                        isFirstColumn && \"checkbox-header\"\r\n                                                    )}\r\n                                                >\r\n                                                    {header.isPlaceholder\r\n                                                        ? null\r\n                                                        : flexRender(\r\n                                                            header.column.columnDef.header,\r\n                                                            header.getContext()\r\n                                                        )}\r\n                                                </th>\r\n                                            )\r\n                                        })}\r\n                                    </tr>\r\n                                ))}\r\n                            </thead>\r\n                            <tbody>\r\n                                {isLoading ? (\r\n                                    <tr>\r\n                                        <td\r\n                                            colSpan={table.getAllColumns().length}\r\n                                            className=\"h-24 text-center\"\r\n                                        >\r\n                                            Đang tải dữ liệu...\r\n                                        </td>\r\n                                    </tr>\r\n                                ) : table.getRowModel().rows?.length ? (\r\n                                    table.getRowModel().rows.map((row) => (\r\n                                        <tr\r\n                                            key={row.id}\r\n                                            data-state={row.getIsSelected() && \"selected\"}\r\n                                            className=\"border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted\"\r\n                                        >\r\n                                            {row.getVisibleCells().map((cell, index) => {\r\n                                                // Determine if this is the first column (checkbox)\r\n                                                const isFirstColumn = index === 0;\r\n                                                // Determine if this column has sticky metadata\r\n                                                const hasSticky = cell.column.columnDef.meta?.isSticky;\r\n\r\n                                                return (\r\n                                                    <td\r\n                                                        key={cell.id}\r\n                                                        style={{\r\n                                                            width: cell.column.getSize(),\r\n                                                            minWidth: cell.column.getSize(),\r\n                                                            maxWidth: cell.column.getSize(),\r\n                                                            ...(hasSticky && {\r\n                                                                position: 'sticky',\r\n                                                                [cell.column.columnDef.meta?.position === 'right' ? 'right' : 'left']: 0,\r\n                                                                backgroundColor: 'var(--background)',\r\n                                                                zIndex: 30,\r\n                                                                boxShadow: cell.column.columnDef.meta?.position === 'right'\r\n                                                                    ? '-5px 0 5px -5px rgba(0,0,0,0.1)'\r\n                                                                    : '5px 0 5px -5px rgba(0,0,0,0.1)'\r\n                                                            }),\r\n                                                            // Áp dụng kiểu dáng sticky cho cột đầu tiên (checkbox)\r\n                                                            ...(isFirstColumn && !hasSticky && {\r\n                                                                position: 'sticky',\r\n                                                                left: 0,\r\n                                                                backgroundColor: 'var(--background)',\r\n                                                                zIndex: 30,\r\n                                                                boxShadow: '5px 0 5px -5px rgba(0,0,0,0.1)'\r\n                                                            })\r\n                                                        }}\r\n                                                        className={cn(\r\n                                                            \"py-1 px-2 align-middle [&:has([role=checkbox])]:pr-0\",\r\n                                                            hasSticky && `sticky-${cell.column.columnDef.meta?.position || 'left'}`,\r\n                                                            isFirstColumn && !hasSticky && \"sticky-left\"\r\n                                                        )}\r\n                                                    >\r\n                                                        {flexRender(\r\n                                                            cell.column.columnDef.cell,\r\n                                                            cell.getContext()\r\n                                                        )}\r\n                                                    </td>\r\n                                                )\r\n                                            })}\r\n                                        </tr>\r\n                                    ))\r\n                                ) : (\r\n                                    <tr>\r\n                                        <td\r\n                                            colSpan={table.getAllColumns().length}\r\n                                            className=\"h-24 text-center\"\r\n                                        >\r\n                                            Không có dữ liệu\r\n                                        </td>\r\n                                    </tr>\r\n                                )}\r\n                            </tbody>\r\n                        </table>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n        </div>\r\n    )\r\n}"], "names": [], "mappings": ";;;;AAEA;;;;;;;;;;;;;;;;;;CAkBC,GAED;AAEA;AAAA;AAxBA;;;;AAyCO,SAAS,UAAiB,EAC7B,KAAK,EACL,SAAS,EACT,YAAY,KAAK,EACG;IACpB,qBACI,sSAAC;QAAI,WAAW,CAAA,GAAA,+HAAA,CAAA,KAAE,AAAD,EAAE,+BAA+B;kBAE9C,cAAA,sSAAC;YAAI,WAAU;sBAEX,cAAA,sSAAC;gBAAI,WAAU;0BACX,cAAA,sSAAC;oBAAI,WAAU;8BACX,cAAA,sSAAC;wBAAM,WAAU;;0CACb,sSAAC;gCAAM,WAAU;0CACZ,MAAM,eAAe,GAAG,GAAG,CAAC,CAAC,4BAC1B,sSAAC;kDACI,YAAY,OAAO,CAAC,GAAG,CAAC,CAAC,QAAQ;4CAC9B,mDAAmD;4CACnD,MAAM,gBAAgB,UAAU;4CAChC,+CAA+C;4CAC/C,MAAM,YAAY,OAAO,MAAM,CAAC,SAAS,CAAC,IAAI,EAAE;4CAEhD,qBACI,sSAAC;gDAEG,OAAO;oDACH,OAAO,OAAO,OAAO;oDACrB,UAAU,OAAO,OAAO;oDACxB,UAAU,OAAO,OAAO;oDACxB,GAAI,aAAa;wDACb,UAAU;wDACV,CAAC,OAAO,MAAM,CAAC,SAAS,CAAC,IAAI,EAAE,aAAa,UAAU,UAAU,OAAO,EAAE;wDACzE,QAAQ;wDACR,WAAW,OAAO,MAAM,CAAC,SAAS,CAAC,IAAI,EAAE,aAAa,UAChD,oCACA;oDACV,CAAC;oDACD,uDAAuD;oDACvD,GAAI,iBAAiB,CAAC,aAAa;wDAC/B,UAAU;wDACV,MAAM;wDACN,QAAQ;wDACR,WAAW;oDACf,CAAC;gDACL;gDACA,WAAW,CAAA,GAAA,+HAAA,CAAA,KAAE,AAAD,EACR,+EACA,aAAa,CAAC,OAAO,EAAE,OAAO,MAAM,CAAC,SAAS,CAAC,IAAI,EAAE,YAAY,QAAQ,EACzE,iBAAiB,CAAC,aAAa,eAC/B,iBAAiB;0DAGpB,OAAO,aAAa,GACf,OACA,CAAA,GAAA,mSAAA,CAAA,aAAU,AAAD,EACP,OAAO,MAAM,CAAC,SAAS,CAAC,MAAM,EAC9B,OAAO,UAAU;+CAhCpB,OAAO,EAAE;;;;;wCAoC1B;uCA7CK,YAAY,EAAE;;;;;;;;;;0CAiD/B,sSAAC;0CACI,0BACG,sSAAC;8CACG,cAAA,sSAAC;wCACG,SAAS,MAAM,aAAa,GAAG,MAAM;wCACrC,WAAU;kDACb;;;;;;;;;;2CAIL,MAAM,WAAW,GAAG,IAAI,EAAE,SAC1B,MAAM,WAAW,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,oBAC1B,sSAAC;wCAEG,cAAY,IAAI,aAAa,MAAM;wCACnC,WAAU;kDAET,IAAI,eAAe,GAAG,GAAG,CAAC,CAAC,MAAM;4CAC9B,mDAAmD;4CACnD,MAAM,gBAAgB,UAAU;4CAChC,+CAA+C;4CAC/C,MAAM,YAAY,KAAK,MAAM,CAAC,SAAS,CAAC,IAAI,EAAE;4CAE9C,qBACI,sSAAC;gDAEG,OAAO;oDACH,OAAO,KAAK,MAAM,CAAC,OAAO;oDAC1B,UAAU,KAAK,MAAM,CAAC,OAAO;oDAC7B,UAAU,KAAK,MAAM,CAAC,OAAO;oDAC7B,GAAI,aAAa;wDACb,UAAU;wDACV,CAAC,KAAK,MAAM,CAAC,SAAS,CAAC,IAAI,EAAE,aAAa,UAAU,UAAU,OAAO,EAAE;wDACvE,iBAAiB;wDACjB,QAAQ;wDACR,WAAW,KAAK,MAAM,CAAC,SAAS,CAAC,IAAI,EAAE,aAAa,UAC9C,oCACA;oDACV,CAAC;oDACD,uDAAuD;oDACvD,GAAI,iBAAiB,CAAC,aAAa;wDAC/B,UAAU;wDACV,MAAM;wDACN,iBAAiB;wDACjB,QAAQ;wDACR,WAAW;oDACf,CAAC;gDACL;gDACA,WAAW,CAAA,GAAA,+HAAA,CAAA,KAAE,AAAD,EACR,wDACA,aAAa,CAAC,OAAO,EAAE,KAAK,MAAM,CAAC,SAAS,CAAC,IAAI,EAAE,YAAY,QAAQ,EACvE,iBAAiB,CAAC,aAAa;0DAGlC,CAAA,GAAA,mSAAA,CAAA,aAAU,AAAD,EACN,KAAK,MAAM,CAAC,SAAS,CAAC,IAAI,EAC1B,KAAK,UAAU;+CA/Bd,KAAK,EAAE;;;;;wCAmCxB;uCA/CK,IAAI,EAAE;;;;8DAmDnB,sSAAC;8CACG,cAAA,sSAAC;wCACG,SAAS,MAAM,aAAa,GAAG,MAAM;wCACrC,WAAU;kDACb;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAYzC;KAhJgB", "debugId": null}}, {"offset": {"line": 308, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/bloomgoo/gold-exchange-platform/frontend/components/common/admin/order-books/type/order-books.ts"], "sourcesContent": ["import { User } from \"@/components/common/admin/users/type/user\";\r\nimport { Token } from \"../../tokens/type/token\";\r\nimport { EcomProduct } from \"../../ecom-products/type/ecom-product\";\r\nimport { OrderTokenDetail } from \"@/components/common/user/order-books/type/order-books\";\r\n\r\n/**\r\n * Enum đại diện cho loại lệnh (OrderType) từ backend\r\n * Đại diện cho hướng của lệnh (mua/bán/rút)\r\n */\r\nexport enum OrderType {\r\n    BUY = 'BUY',\r\n    SELL = 'SELL',\r\n    WITHDRAWAL = 'WITHDRAWAL',\r\n}\r\n\r\n/**\r\n * Enum đại diện cho trạng thái của lệnh (OrderStatus) từ backend\r\n */\r\nexport enum OrderStatus {\r\n    COMPLETED = 'COMPLETED',\r\n    DEPOSITED = 'DEPOSITED',\r\n    TERMINATED = 'TERMINATED',\r\n    CANCELLED = 'CANCELLED',\r\n}\r\n\r\n/**\r\n * Enum đại diện cho loại hình kinh doanh (BusinessType) từ backend\r\n */\r\nexport enum BusinessType {\r\n    NORMAL = 'NORMAL',\r\n    IMMEDIATE_DELIVERY = 'IMMEDIATE_DELIVERY',\r\n}\r\n\r\n/**\r\n * Enum đại diện cho trạng thái phê duyệt (ApproveStatus) từ backend\r\n */\r\nexport enum ApproveStatus {\r\n    PENDING = 'PENDING',\r\n    APPROVED = 'APPROVED',\r\n}\r\n\r\n/**\r\n * Interface đại diện cho chi tiết token trong lệnh\r\n * Dựa trên OrderTokenDetailDto từ backend\r\n */\r\nexport interface OrderProductDetail {\r\n    productId: string;\r\n    price: number;\r\n    quantity: number;\r\n}\r\n\r\n/**\r\n * Interface đại diện chi tiết trong sổ lệnh (OrderBookDetail) từ backend\r\n */\r\nexport interface OrderBookDetail {\r\n    id: string;\r\n    orderBookId: string;\r\n    productId: string;\r\n    price: number;\r\n    quantity: number;\r\n    totalPrice: number;\r\n    createdAt: string;\r\n    updatedAt: string;\r\n    createdBy?: string;\r\n    updatedBy?: string;\r\n    product?: EcomProduct; \r\n}\r\n\r\n/**\r\n * Interface đại diện cho sổ lệnh (OrderBook) từ backend\r\n */\r\nexport interface OrderBook {\r\n    id: string;\r\n    userId: string;\r\n    orderType: OrderType;\r\n    totalPrice?: number;\r\n    status: OrderStatus;\r\n    businessType?: BusinessType;\r\n    depositPrice?: number;\r\n    settlementPrice?: number;\r\n    contractNumber?: string;\r\n    storageFee?: number;\r\n    settlementDeadline?: Date;\r\n    settlementAt?: Date;\r\n    approveStatus?: ApproveStatus;\r\n    approvedAt?: Date;\r\n    createdAt: string;\r\n    updatedAt: string;\r\n    createdBy?: string;\r\n    updatedBy?: string;\r\n    isDeleted: boolean;\r\n    deletedBy?: string;\r\n    user?: User;\r\n    creator?: User;\r\n    updater?: User;\r\n    deleter?: User;\r\n    details: OrderBookDetail[];\r\n}\r\n\r\n/**\r\n * Interface cho dữ liệu tạo lệnh mới\r\n * Dựa trên CreateOrderBookDto từ backend\r\n */\r\nexport interface CreateOrderBookDto {\r\n    userId: string;\r\n    orderType: OrderType;\r\n    businessType: BusinessType;\r\n    products: OrderProductDetail[];\r\n    depositPrice: number;\r\n    settlementPrice?: number;\r\n    contractNumber?: string;\r\n    storageFee: number;\r\n    settlementDeadline?: Date;\r\n    createdBy: string;\r\n}\r\n\r\n/**\r\n * Interface cho dữ liệu cập nhật lệnh\r\n * Dựa trên UpdateOrderBookDto từ backend\r\n */\r\nexport interface UpdateOrderBookDto {\r\n    id?: string;\r\n    userId?: string;\r\n    orderType?: OrderType;\r\n    totalPrice?: number;\r\n    status?: OrderStatus;\r\n    businessType?: BusinessType;\r\n    depositPrice?: number;\r\n    settlementPrice?: number;\r\n    contractNumber?: string;\r\n    storageFee?: number;\r\n    settlementDeadline?: Date;\r\n    settlementAt?: Date;\r\n    products?: OrderProductDetail[];\r\n    updatedBy?: string;\r\n}\r\n\r\n/**\r\n * Interface cho bộ lọc sổ lệnh\r\n */\r\nexport interface OrderBookFilter {\r\n    userId?: string;\r\n    productId?: string;\r\n    orderType?: OrderType;\r\n    status?: OrderStatus;\r\n    dateFrom?: Date;\r\n    dateTo?: Date;\r\n    contractNumber?: string;\r\n}\r\n"], "names": [], "mappings": ";;;;;;AASO,IAAA,AAAK,mCAAA;;;;WAAA;;AASL,IAAA,AAAK,qCAAA;;;;;WAAA;;AAUL,IAAA,AAAK,sCAAA;;;WAAA;;AAQL,IAAA,AAAK,uCAAA;;;WAAA", "debugId": null}}, {"offset": {"line": 346, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/bloomgoo/gold-exchange-platform/frontend/components/common/user/user-hover-card.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';\r\nimport { HoverCard, HoverCardContent, HoverCardTrigger } from '@/components/ui/hover-card';\r\nimport { User } from '@/components/common/admin/users/type/user';\r\nimport { Badge } from '@/components/ui/badge';\r\nimport { format } from 'date-fns';\r\nimport { vi } from 'date-fns/locale';\r\nimport { Mail } from 'lucide-react';\r\n\r\ninterface UserHoverCardProps {\r\n  user?: Partial<User> | null;\r\n  userId?: string;\r\n  showAvatar?: boolean;\r\n  size?: 'sm' | 'md' | 'lg';\r\n  children: React.ReactNode;\r\n}\r\n\r\n/**\r\n * Component hiển thị thông tin người dùng khi hover\r\n * @param user Thông tin người dùng\r\n * @param userId ID người dùng (nếu không có thông tin đầy đủ)\r\n * @param showAvatar Có hiển thị avatar không\r\n * @param size Kích thước hiển thị (sm, md, lg)\r\n * @param children Nội dung hiển thị khi không hover\r\n */\r\nexport function UserHoverCard({ user, userId, showAvatar = true, size = 'md', children }: UserHoverCardProps) {\r\n  // Nếu không có thông tin người dùng và không có userId, hiển thị children\r\n  if (!user && !userId) {\r\n    return <>{children}</>;\r\n  }\r\n\r\n  // Lấy ID từ user hoặc từ userId\r\n  const id = user?.id || userId;\r\n\r\n  // Xác định kích thước avatar dựa trên size\r\n  const avatarSize = {\r\n    sm: 'size-5',\r\n    md: 'size-6',\r\n    lg: 'size-8',\r\n  }[size];\r\n\r\n  // Hiển thị badge trạng thái người dùng\r\n  const getStatusBadge = (isActive?: boolean) => {\r\n    if (isActive === undefined) return null;\r\n    return (\r\n      <Badge variant={isActive ? 'default' : 'secondary'} className=\"ml-2\">\r\n        {isActive ? 'Hoạt động' : 'Vô hiệu'}\r\n      </Badge>\r\n    );\r\n  };\r\n\r\n  // Format ngày tháng\r\n  const formatDate = (date?: string | Date) => {\r\n    if (!date) return 'N/A';\r\n    try {\r\n      const dateObj = typeof date === 'string' ? new Date(date) : date;\r\n      return format(dateObj, 'dd/MM/yyyy HH:mm', { locale: vi });\r\n    } catch (error) {\r\n      return 'N/A';\r\n    }\r\n  };\r\n\r\n  return (\r\n    <HoverCard>\r\n      <HoverCardTrigger asChild>\r\n        <div className=\"flex items-center gap-2 cursor-pointer\">\r\n          {showAvatar && (\r\n            <Avatar className={`shrink-0 ${avatarSize}`}>\r\n              <AvatarImage\r\n                src={user?.avatar || `https://api.dicebear.com/9.x/initials/svg?seed=${user?.fullName || user?.username || id}`}\r\n                alt={user?.fullName || user?.username || 'User'}\r\n              />\r\n              <AvatarFallback>\r\n                {user?.fullName\r\n                  ? user.fullName[0].toUpperCase()\r\n                  : (user?.username ? user.username[0].toUpperCase() : 'U')}\r\n              </AvatarFallback>\r\n            </Avatar>\r\n          )}\r\n          {children}\r\n        </div>\r\n      </HoverCardTrigger>\r\n      <HoverCardContent className=\"w-80 p-4\">\r\n        <div className=\"flex flex-col gap-4\">\r\n          {/* Header với thông tin cơ bản */}\r\n          <div className=\"flex items-center gap-3\">\r\n            <Avatar className=\"size-10 shrink-0\">\r\n              <AvatarImage\r\n                src={user?.avatar || `https://api.dicebear.com/9.x/initials/svg?seed=${user?.fullName || user?.username || id}`}\r\n                alt={user?.fullName || user?.username || 'User'}\r\n              />\r\n              <AvatarFallback>\r\n                {user?.fullName\r\n                  ? user.fullName[0].toUpperCase()\r\n                  : (user?.username ? user.username[0].toUpperCase() : 'U')}\r\n              </AvatarFallback>\r\n            </Avatar>\r\n            <div>\r\n              <div className=\"font-medium flex items-center\">\r\n                {user?.fullName || user?.username || id?.substring(0, 8)}\r\n                {getStatusBadge(user?.isActive)}\r\n              </div>\r\n              {user?.email && (\r\n                <div className=\"text-sm text-muted-foreground flex items-center gap-1 mt-1\">\r\n                  <Mail className=\"size-3\" />\r\n                  {user.email}\r\n                </div>\r\n              )}\r\n            </div>\r\n          </div>\r\n\r\n          {/* Thông tin chi tiết */}\r\n          <div className=\"grid grid-cols-2 gap-3 text-sm\">\r\n            {user?.username && (\r\n              <div>\r\n                <div className=\"text-muted-foreground\">Tên đăng nhập</div>\r\n                <div>{user.username}</div>\r\n              </div>\r\n            )}\r\n            {user?.phone && (\r\n              <div>\r\n                <div className=\"text-muted-foreground\">Số điện thoại</div>\r\n                <div>{user.phone}</div>\r\n              </div>\r\n            )}\r\n            {user?.address && (\r\n              <div className=\"col-span-2\">\r\n                <div className=\"text-muted-foreground\">Địa chỉ</div>\r\n                <div>{user.address}</div>\r\n              </div>\r\n            )}\r\n            {user?.bio && (\r\n              <div className=\"col-span-2\">\r\n                <div className=\"text-muted-foreground\">Giới thiệu</div>\r\n                <div>{user.bio}</div>\r\n              </div>\r\n            )}\r\n            {user?.birthday && (\r\n              <div>\r\n                <div className=\"text-muted-foreground\">Ngày sinh</div>\r\n                <div>{formatDate(user.birthday)}</div>\r\n              </div>\r\n            )}\r\n            {user?.createdAt && (\r\n              <div>\r\n                <div className=\"text-muted-foreground\">Ngày tạo</div>\r\n                <div>{formatDate(user.createdAt)}</div>\r\n              </div>\r\n            )}\r\n            {user?.role && (\r\n              <div>\r\n                <div className=\"text-muted-foreground\">Vai trò</div>\r\n                <div>{Array.isArray(user.role) ? user.role.join(', ') : user.role}</div>\r\n              </div>\r\n            )}\r\n          </div>\r\n        </div>\r\n      </HoverCardContent>\r\n    </HoverCard>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAEA;AACA;AACA;AACA;AARA;;;;;;;;AA0BO,SAAS,cAAc,EAAE,IAAI,EAAE,MAAM,EAAE,aAAa,IAAI,EAAE,OAAO,IAAI,EAAE,QAAQ,EAAsB;IAC1G,0EAA0E;IAC1E,IAAI,CAAC,QAAQ,CAAC,QAAQ;QACpB,qBAAO;sBAAG;;IACZ;IAEA,gCAAgC;IAChC,MAAM,KAAK,MAAM,MAAM;IAEvB,2CAA2C;IAC3C,MAAM,aAAa;QACjB,IAAI;QACJ,IAAI;QACJ,IAAI;IACN,CAAC,CAAC,KAAK;IAEP,uCAAuC;IACvC,MAAM,iBAAiB,CAAC;QACtB,IAAI,aAAa,WAAW,OAAO;QACnC,qBACE,sSAAC,6HAAA,CAAA,QAAK;YAAC,SAAS,WAAW,YAAY;YAAa,WAAU;sBAC3D,WAAW,cAAc;;;;;;IAGhC;IAEA,oBAAoB;IACpB,MAAM,aAAa,CAAC;QAClB,IAAI,CAAC,MAAM,OAAO;QAClB,IAAI;YACF,MAAM,UAAU,OAAO,SAAS,WAAW,IAAI,KAAK,QAAQ;YAC5D,OAAO,CAAA,GAAA,gNAAA,CAAA,SAAM,AAAD,EAAE,SAAS,oBAAoB;gBAAE,QAAQ,sMAAA,CAAA,KAAE;YAAC;QAC1D,EAAE,OAAO,OAAO;YACd,OAAO;QACT;IACF;IAEA,qBACE,sSAAC,qIAAA,CAAA,YAAS;;0BACR,sSAAC,qIAAA,CAAA,mBAAgB;gBAAC,OAAO;0BACvB,cAAA,sSAAC;oBAAI,WAAU;;wBACZ,4BACC,sSAAC,8HAAA,CAAA,SAAM;4BAAC,WAAW,CAAC,SAAS,EAAE,YAAY;;8CACzC,sSAAC,8HAAA,CAAA,cAAW;oCACV,KAAK,MAAM,UAAU,CAAC,+CAA+C,EAAE,MAAM,YAAY,MAAM,YAAY,IAAI;oCAC/G,KAAK,MAAM,YAAY,MAAM,YAAY;;;;;;8CAE3C,sSAAC,8HAAA,CAAA,iBAAc;8CACZ,MAAM,WACH,KAAK,QAAQ,CAAC,EAAE,CAAC,WAAW,KAC3B,MAAM,WAAW,KAAK,QAAQ,CAAC,EAAE,CAAC,WAAW,KAAK;;;;;;;;;;;;wBAI5D;;;;;;;;;;;;0BAGL,sSAAC,qIAAA,CAAA,mBAAgB;gBAAC,WAAU;0BAC1B,cAAA,sSAAC;oBAAI,WAAU;;sCAEb,sSAAC;4BAAI,WAAU;;8CACb,sSAAC,8HAAA,CAAA,SAAM;oCAAC,WAAU;;sDAChB,sSAAC,8HAAA,CAAA,cAAW;4CACV,KAAK,MAAM,UAAU,CAAC,+CAA+C,EAAE,MAAM,YAAY,MAAM,YAAY,IAAI;4CAC/G,KAAK,MAAM,YAAY,MAAM,YAAY;;;;;;sDAE3C,sSAAC,8HAAA,CAAA,iBAAc;sDACZ,MAAM,WACH,KAAK,QAAQ,CAAC,EAAE,CAAC,WAAW,KAC3B,MAAM,WAAW,KAAK,QAAQ,CAAC,EAAE,CAAC,WAAW,KAAK;;;;;;;;;;;;8CAG3D,sSAAC;;sDACC,sSAAC;4CAAI,WAAU;;gDACZ,MAAM,YAAY,MAAM,YAAY,IAAI,UAAU,GAAG;gDACrD,eAAe,MAAM;;;;;;;wCAEvB,MAAM,uBACL,sSAAC;4CAAI,WAAU;;8DACb,sSAAC,yRAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;gDACf,KAAK,KAAK;;;;;;;;;;;;;;;;;;;sCAOnB,sSAAC;4BAAI,WAAU;;gCACZ,MAAM,0BACL,sSAAC;;sDACC,sSAAC;4CAAI,WAAU;sDAAwB;;;;;;sDACvC,sSAAC;sDAAK,KAAK,QAAQ;;;;;;;;;;;;gCAGtB,MAAM,uBACL,sSAAC;;sDACC,sSAAC;4CAAI,WAAU;sDAAwB;;;;;;sDACvC,sSAAC;sDAAK,KAAK,KAAK;;;;;;;;;;;;gCAGnB,MAAM,yBACL,sSAAC;oCAAI,WAAU;;sDACb,sSAAC;4CAAI,WAAU;sDAAwB;;;;;;sDACvC,sSAAC;sDAAK,KAAK,OAAO;;;;;;;;;;;;gCAGrB,MAAM,qBACL,sSAAC;oCAAI,WAAU;;sDACb,sSAAC;4CAAI,WAAU;sDAAwB;;;;;;sDACvC,sSAAC;sDAAK,KAAK,GAAG;;;;;;;;;;;;gCAGjB,MAAM,0BACL,sSAAC;;sDACC,sSAAC;4CAAI,WAAU;sDAAwB;;;;;;sDACvC,sSAAC;sDAAK,WAAW,KAAK,QAAQ;;;;;;;;;;;;gCAGjC,MAAM,2BACL,sSAAC;;sDACC,sSAAC;4CAAI,WAAU;sDAAwB;;;;;;sDACvC,sSAAC;sDAAK,WAAW,KAAK,SAAS;;;;;;;;;;;;gCAGlC,MAAM,sBACL,sSAAC;;sDACC,sSAAC;4CAAI,WAAU;sDAAwB;;;;;;sDACvC,sSAAC;sDAAK,MAAM,OAAO,CAAC,KAAK,IAAI,IAAI,KAAK,IAAI,CAAC,IAAI,CAAC,QAAQ,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQjF;KAvIgB", "debugId": null}}, {"offset": {"line": 723, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/bloomgoo/gold-exchange-platform/frontend/components/common/admin/order-books/table/order-book-cell.tsx"], "sourcesContent": ["import { UserHoverCard } from \"@/components/common/user/user-hover-card\"\r\nimport { Badge } from \"@/components/ui/badge\"\r\nimport { But<PERSON> } from \"@/components/ui/button\"\r\nimport {\r\n  Dialog,\r\n  DialogContent,\r\n  DialogDescription,\r\n  DialogFooter,\r\n  DialogHeader,\r\n  DialogTitle\r\n} from \"@/components/ui/dialog\"\r\nimport {\r\n  DropdownMenu,\r\n  DropdownMenuContent,\r\n  DropdownMenuItem,\r\n  DropdownMenuSeparator,\r\n  DropdownMenuShortcut,\r\n  DropdownMenuTrigger\r\n} from \"@/components/ui/dropdown-menu\"\r\nimport {\r\n  Popover,\r\n  PopoverContent,\r\n  PopoverTrigger\r\n} from \"@/components/ui/popover\"\r\n\r\nimport {\r\n  Command,\r\n  CommandEmpty,\r\n  CommandGroup,\r\n  CommandInput,\r\n  CommandItem,\r\n} from \"@/components/ui/command\"\r\nimport { DateTimeDisplay } from \"@/components/ui/date-time-display\"\r\nimport { api } from '@/lib/api'\r\nimport { cn } from \"@/lib/utils\"\r\nimport { ForexQuote } from '@/services/websocket/polygon-forex-socket.service'\r\nimport { type ColumnDef, Row } from \"@tanstack/react-table\"\r\nimport { format, isValid } from \"date-fns\"\r\nimport { vi } from \"date-fns/locale\"\r\nimport {\r\n  ArrowUpDown,\r\n  CalendarPlus,\r\n  Check,\r\n  ChevronsUpDown,\r\n  Edit,\r\n  Eye,\r\n  MoreVertical,\r\n  Trash2,\r\n  User,\r\n  X\r\n} from \"lucide-react\"\r\nimport { useState } from \"react\"\r\nimport { toast } from \"sonner\"\r\nimport { ApproveStatus, BusinessType, OrderBook, OrderStatus, OrderType } from \"../type/order-books\"\r\n\r\n// Format currency with VND\r\nconst formatCurrency = (amount: number | undefined) => {\r\n  if (amount === undefined || amount === null) return \"---\"\r\n  return new Intl.NumberFormat('vi-VN', {\r\n    maximumFractionDigits: 0\r\n  }).format(amount)\r\n}\r\n\r\n// Format date from ISO string\r\nconst formatDate = (dateStr: string | null) => {\r\n  if (!dateStr) return \"---\"\r\n  const date = new Date(dateStr)\r\n  if (!isValid(date)) return \"Không hợp lệ\"\r\n  try {\r\n    return format(date, \"dd/MM/yyyy HH:mm\", { locale: vi })\r\n  } catch (error) {\r\n    return \"Không hợp lệ\"\r\n  }\r\n}\r\n\r\n// Get color for order status badge\r\nconst getStatusColor = (status: OrderStatus) => {\r\n  switch (status) {\r\n    case OrderStatus.COMPLETED:\r\n      return 'bg-green-100 text-green-800 font-normal';\r\n    case OrderStatus.DEPOSITED:\r\n      return 'bg-purple-100 text-purple-800 font-normal';\r\n    case OrderStatus.TERMINATED:\r\n      return 'bg-red-100 text-red-800 font-normal';\r\n    case OrderStatus.CANCELLED:\r\n      return 'bg-orange-100 text-orange-800 font-normal';\r\n    default:\r\n      return 'bg-gray-100 text-gray-800 font-normal';\r\n  }\r\n};\r\n\r\n// Order status in Vietnamese\r\nconst getStatusName = (status: OrderStatus) => {\r\n  switch (status) {\r\n    case OrderStatus.COMPLETED:\r\n      return 'Đã tất toán';\r\n    case OrderStatus.DEPOSITED:\r\n      return 'Đã ký quỹ';\r\n    case OrderStatus.TERMINATED:\r\n      return 'Đã cắt hợp đồng';\r\n    case OrderStatus.CANCELLED:\r\n      return 'Đã hủy';\r\n    default:\r\n      return status;\r\n  }\r\n};\r\n\r\n// Order type in Vietnamese\r\nconst getOrderTypeName = (type: OrderType) => {\r\n  switch (type) {\r\n    case OrderType.BUY:\r\n      return 'Mua';\r\n    case OrderType.SELL:\r\n      return 'Bán';\r\n    default:\r\n      return type;\r\n  }\r\n};\r\n\r\n// Get color for order type\r\nconst getOrderTypeColor = (type: OrderType) => {\r\n  switch (type) {\r\n    case OrderType.BUY:\r\n      return 'bg-green-100 text-green-800 font-normal';\r\n    case OrderType.SELL:\r\n      return 'bg-red-100 text-red-800 font-normal';\r\n    default:\r\n      return 'bg-gray-100 text-gray-800 font-normal';\r\n  }\r\n};\r\n\r\n// Get color for business type\r\nconst getBusinessTypeColor = (type: BusinessType | undefined) => {\r\n  switch (type) {\r\n    case BusinessType.NORMAL:\r\n      return 'bg-orange-100 text-orange-800 font-normal';\r\n    case BusinessType.IMMEDIATE_DELIVERY:\r\n      return 'bg-blue-100 text-blue-800 font-normal';\r\n    default:\r\n      return 'bg-gray-100 text-gray-800 font-normal';\r\n  }\r\n};\r\n\r\n// Get color for approve status badge\r\nconst getApproveStatusColor = (status: ApproveStatus | undefined) => {\r\n  switch (status) {\r\n    case ApproveStatus.PENDING:\r\n      return 'bg-yellow-100 text-yellow-800 font-normal';\r\n    case ApproveStatus.APPROVED:\r\n      return 'bg-green-100 text-green-800 font-normal';\r\n    default:\r\n      return 'bg-gray-100 text-gray-800 font-normal';\r\n  }\r\n};\r\n\r\n// Approve status in Vietnamese\r\nconst getApproveStatusName = (status: ApproveStatus | undefined) => {\r\n  switch (status) {\r\n    case ApproveStatus.PENDING:\r\n      return 'Chờ phê duyệt';\r\n    case ApproveStatus.APPROVED:\r\n      return 'Đã phê duyệt';\r\n    default:\r\n      return '---';\r\n  }\r\n};\r\n\r\n// Add helper function to calculate real-time total price\r\nconst calculateRealTimePrice = (order: OrderBook, currentQuote: ForexQuote | null) => {\r\n  if (!currentQuote || order.status === OrderStatus.COMPLETED) {\r\n    return order.totalPrice || 0;\r\n  }\r\n\r\n  // Get the appropriate price based on order type\r\n  const price = order.orderType === OrderType.BUY ? currentQuote.askPrice : currentQuote.bidPrice;\r\n  \r\n  // Calculate total volume and price\r\n  const totalVolume = order.details?.reduce((sum, detail) => {\r\n    const volume = detail.quantity || 0;\r\n    const weight = detail.product?.weight || 1;\r\n    return sum + (volume * weight);\r\n  }, 0) || 0;\r\n\r\n  // Calculate new total price\r\n  const basePrice = price * totalVolume * 1.2056 * 27000;\r\n  return Math.round(basePrice + (basePrice * 0.11)); // Add 11% VAT\r\n};\r\n\r\n// Add helper function to calculate real-time settlement amount\r\nconst calculateRealTimeSettlement = (order: OrderBook, currentQuote: ForexQuote | null) => {\r\n  if (!currentQuote || order.status === OrderStatus.COMPLETED) {\r\n    return order.settlementPrice;\r\n  }\r\n\r\n  const totalPrice = calculateRealTimePrice(order, currentQuote);\r\n  \r\n  // For physical orders, settlement is 100%\r\n  if (order.businessType === BusinessType.IMMEDIATE_DELIVERY) {\r\n    return totalPrice;\r\n  }\r\n  \r\n  // For short investment, settlement is remaining balance (90% if not paid)\r\n  return Math.round(totalPrice * 0.9);\r\n};\r\n\r\n// Add helper function to get price color based on order type\r\nconst getPriceColor = (order: OrderBook) => {\r\n  if (order.status === OrderStatus.COMPLETED) return \"\";\r\n  return order.orderType === OrderType.BUY ? \"text-green-500\" : \"text-red-500\";\r\n};\r\n\r\ninterface ActionsProps {\r\n  row: Row<OrderBook>\r\n  onViewDetail: (order: OrderBook) => void\r\n  onDelete: (order: OrderBook) => void\r\n  onEdit: (order: OrderBook) => void\r\n  onChangeStatus: (order: OrderBook, status: OrderStatus) => void\r\n  onExtendSettlement?: (order: OrderBook) => void\r\n}\r\n\r\nfunction Actions({ row, onViewDetail, onDelete, onEdit, onChangeStatus, onExtendSettlement }: ActionsProps) {\r\n  const [showDeleteDialog, setShowDeleteDialog] = useState(false)\r\n  const [showExtendDialog, setShowExtendDialog] = useState(false)\r\n  const order = row.original\r\n\r\n  const handleDelete = (order: OrderBook) => {\r\n    onDelete(order)\r\n    setShowDeleteDialog(false)\r\n  }\r\n\r\n  // Xử lý khi click vào nút xem chi tiết\r\n  const handleViewDetail = async (order: OrderBook) => {\r\n    try {\r\n      // Sử dụng api client để gọi API\r\n      const response = await api.get<OrderBook>(`order-books/${order.id}`);\r\n\r\n      // Chỉ truyền dữ liệu chi tiết nếu API trả về thành công\r\n      if (response) {\r\n        onViewDetail(response);\r\n      } else {\r\n        // Nếu không có response, hiển thị thông báo lỗi\r\n        toast.error(\"Không thể tải thông tin chi tiết lệnh. Vui lòng thử lại sau.\");\r\n      }\r\n    } catch (error) {\r\n      console.error('Error fetching order detail:', error);\r\n      // Hiển thị thông báo lỗi cho người dùng\r\n      toast.error(\"Có lỗi xảy ra khi tải thông tin lệnh. Vui lòng thử lại sau.\");\r\n    }\r\n  }\r\n\r\n  // Xử lý khi click vào nút chỉnh sửa\r\n  const handleEdit = async (order: OrderBook) => {\r\n    try {\r\n      // Sử dụng api client để gọi API lấy dữ liệu đầy đủ giống như chi tiết\r\n      const response = await api.get<OrderBook>(`order-books/${order.id}`);\r\n\r\n      // Chỉ truyền dữ liệu chi tiết nếu API trả về thành công\r\n      if (response) {\r\n        // Chuẩn bị dữ liệu cho form chỉnh sửa\r\n        // Chuyển đổi OrderType sang type cho form\r\n        let formType = 'buy';\r\n        if (response.orderType === OrderType.SELL) {\r\n          formType = 'sell';\r\n        } else if (response.orderType === OrderType.WITHDRAWAL) {\r\n          formType = 'withdrawal';\r\n        }\r\n\r\n        // Gán type vào response để form có thể sử dụng\r\n        const enhancedResponse = {\r\n          ...response,\r\n          type: formType,\r\n          // Không gửi remainingVolume để lược bỏ trường này theo yêu cầu\r\n          remainingVolume: null,\r\n          // Để trống giá thị trường theo yêu cầu\r\n          price: null\r\n        };\r\n\r\n        onEdit(enhancedResponse);\r\n      } else {\r\n        // Nếu không có response, hiển thị thông báo lỗi\r\n        toast.error(\"Không thể tải thông tin chi tiết đơn hàng. Vui lòng thử lại sau.\");\r\n      }\r\n    } catch (error) {\r\n      console.error('Error fetching order detail for edit:', error);\r\n      // Hiển thị thông báo lỗi cho người dùng\r\n      toast.error(\"Có lỗi xảy ra khi tải thông tin đơn hàng. Vui lòng thử lại sau.\");\r\n    }\r\n  }\r\n\r\n  // Kiểm tra xem lệnh có thể gia hạn hay không\r\n  const canExtend = () => {\r\n\r\n    // Chỉ cho phép gia hạn lệnh mua/bán đang ở trạng thái WAIT_PAYMENT\r\n    return (\r\n      order.status === OrderStatus.DEPOSITED &&\r\n      order.settlementDeadline // Phải có hạn tất toán\r\n    )\r\n  }\r\n\r\n  return (\r\n    <>\r\n      {/* Dialog xác nhận xóa */}\r\n      <Dialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>\r\n        <DialogContent>\r\n          <DialogHeader>\r\n            <DialogTitle>Xác nhận xóa</DialogTitle>\r\n            <DialogDescription>\r\n              Bạn có chắc chắn muốn xóa lệnh <span className=\"font-medium\">{order.id}</span>?\r\n              <p className=\"mt-2\">Hành động này không thể hoàn tác.</p>\r\n            </DialogDescription>\r\n          </DialogHeader>\r\n          <DialogFooter>\r\n            <Button variant=\"outline\" onClick={() => setShowDeleteDialog(false)}>Hủy</Button>\r\n            <Button variant=\"destructive\" onClick={() => handleDelete(order)}>Xóa</Button>\r\n          </DialogFooter>\r\n        </DialogContent>\r\n      </Dialog>\r\n\r\n      {/* Dialog xác nhận gia hạn */}\r\n      <Dialog open={showExtendDialog} onOpenChange={setShowExtendDialog}>\r\n        <DialogContent>\r\n          <DialogHeader>\r\n            <DialogTitle>Xác nhận gia hạn</DialogTitle>\r\n            <DialogDescription>\r\n              Bạn có chắc chắn muốn gia hạn thời gian tất toán thêm 15 ngày cho lệnh này?\r\n              <p className=\"mt-2\">Phí gia hạn là 100,000 VND và sẽ được trừ từ số dư của người dùng.</p>\r\n            </DialogDescription>\r\n          </DialogHeader>\r\n          <DialogFooter>\r\n            <Button variant=\"outline\" onClick={() => setShowExtendDialog(false)}>Hủy</Button>\r\n            <Button\r\n              onClick={() => {\r\n                if (onExtendSettlement) {\r\n                  onExtendSettlement(order);\r\n                }\r\n                setShowExtendDialog(false);\r\n              }}\r\n            >\r\n              Xác nhận gia hạn\r\n            </Button>\r\n          </DialogFooter>\r\n        </DialogContent>\r\n      </Dialog>\r\n\r\n      <div className=\"flex justify-end px-1 sticky-action-cell h-full items-center\">\r\n        <DropdownMenu>\r\n          <DropdownMenuTrigger asChild>\r\n            <Button\r\n              variant=\"ghost\"\r\n              size=\"sm\"\r\n              className=\"flex h-6 w-6 p-0 data-[state=open]:bg-muted transition-colors hover:bg-muted\"\r\n            >\r\n              <MoreVertical className=\"h-3 w-3\" />\r\n              <span className=\"sr-only\">Mở menu</span>\r\n            </Button>\r\n          </DropdownMenuTrigger>\r\n          <DropdownMenuContent align=\"end\" className=\"p-1\">\r\n            <DropdownMenuItem onClick={() => handleViewDetail(order)}>\r\n              <Eye className=\"mr-2 h-3.5 w-3.5\" />\r\n              <span className=\"flex-1 text-sm\">Chi tiết</span>\r\n              <DropdownMenuShortcut className=\"text-sm\">⌘V</DropdownMenuShortcut>\r\n            </DropdownMenuItem>\r\n            <DropdownMenuItem onClick={() => handleEdit(order)}>\r\n              <Edit className=\"mr-2 h-3.5 w-3.5\" />\r\n              <span className=\"flex-1 text-sm\">Chỉnh sửa</span>\r\n              <DropdownMenuShortcut className=\"text-sm\">⌘E</DropdownMenuShortcut>\r\n            </DropdownMenuItem>\r\n\r\n            <DropdownMenuSeparator />\r\n\r\n            {/* Status change options */}\r\n            {order.status !== OrderStatus.COMPLETED && order.status !== OrderStatus.TERMINATED && (\r\n              <>\r\n                {/* Tất toán hợp đồng - Hiển thị modal tất toán cho tất cả các loại */}\r\n                <DropdownMenuItem onClick={() => onChangeStatus(order, OrderStatus.COMPLETED)}>\r\n                  <Check className=\"mr-2 h-3.5 w-3.5 text-green-500\" />\r\n                  <span className=\"flex-1 text-sm text-green-500\">Tất toán hợp đồng</span>\r\n                </DropdownMenuItem>\r\n\r\n                <DropdownMenuItem\r\n                  onClick={() => onChangeStatus(order, OrderStatus.TERMINATED)}\r\n                >\r\n                  <X className=\"mr-2 h-3.5 w-3.5 text-red-500\" />\r\n                  <span className=\"flex-1 text-sm text-red-500\">Cắt hợp đồng</span>\r\n                </DropdownMenuItem>\r\n\r\n                {/* Gia hạn option */}\r\n                {canExtend() && onExtendSettlement && (\r\n                  <DropdownMenuItem onClick={() => setShowExtendDialog(true)}>\r\n                    <CalendarPlus className=\"mr-2 h-3.5 w-3.5 text-blue-500\" />\r\n                    <span className=\"flex-1 text-sm text-blue-500\">Gia hạn thời gian</span>\r\n                  </DropdownMenuItem>\r\n                )}\r\n\r\n                <DropdownMenuSeparator />\r\n              </>\r\n            )}\r\n\r\n            <DropdownMenuItem\r\n              onClick={() => setShowDeleteDialog(true)}\r\n              className=\"text-destructive focus:text-destructive\"\r\n            >\r\n              <Trash2 className=\"mr-2 h-3.5 w-3.5 text-destructive\" />\r\n              <span className=\"flex-1 text-sm text-destructive\">Xóa</span>\r\n              <DropdownMenuShortcut className=\"text-sm text-destructive\">⌫</DropdownMenuShortcut>\r\n            </DropdownMenuItem>\r\n          </DropdownMenuContent>\r\n        </DropdownMenu>\r\n      </div>\r\n    </>\r\n  )\r\n}\r\n\r\n// Status selector for changing order status\r\ninterface OrderStatusSelectorProps {\r\n  status: OrderStatus;\r\n  onStatusChange: (status: OrderStatus) => void;\r\n  disabled?: boolean;\r\n}\r\n\r\nexport function OrderStatusSelector({ status, onStatusChange, disabled = false }: OrderStatusSelectorProps) {\r\n  const [open, setOpen] = useState<boolean>(false);\r\n\r\n  // Tất cả các trạng thái có thể có\r\n  const statusOptions = [\r\n    { value: OrderStatus.COMPLETED as string, label: 'Đã tất toán', color: 'bg-green-100 text-green-800 font-normal' },\r\n    { value: OrderStatus.TERMINATED as string, label: 'Đã cắt hợp đồng', color: 'bg-red-100 text-red-800 font-normal' },\r\n    { value: OrderStatus.DEPOSITED as string, label: 'Đã ký quỹ', color: 'bg-purple-100 text-purple-800 font-normal' },\r\n    { value: OrderStatus.CANCELLED as string, label: 'Đã hủy', color: 'bg-gray-100 text-gray-800 font-normal' },\r\n\r\n  ];\r\n\r\n  const currentStatus = statusOptions.find((option) => option.value === status) || statusOptions[0];\r\n\r\n  return (\r\n    <Popover open={open} onOpenChange={setOpen}>\r\n      <PopoverTrigger asChild>\r\n        <Button\r\n          variant=\"ghost\"\r\n          role=\"combobox\"\r\n          aria-expanded={open}\r\n          disabled={disabled}\r\n          className=\"justify-between h-auto p-1 hover:bg-transparent\"\r\n        >\r\n          <Badge className={currentStatus.color}>\r\n            {currentStatus.label}\r\n          </Badge>\r\n        </Button>\r\n      </PopoverTrigger>\r\n      <PopoverContent className=\"w-[200px] p-0\">\r\n        <Command>\r\n          <CommandInput placeholder=\"Tìm trạng thái...\" />\r\n          <CommandEmpty>Không tìm thấy trạng thái</CommandEmpty>\r\n          <CommandGroup>\r\n            {statusOptions.map((option) => (\r\n              <CommandItem\r\n                key={option.value}\r\n                value={option.value}\r\n                onSelect={() => {\r\n                  onStatusChange(option.value as OrderStatus);\r\n                  setOpen(false);\r\n                }}\r\n              >\r\n                <Check\r\n                  className={cn(\r\n                    \"mr-2 h-4 w-4\",\r\n                    status === option.value ? \"opacity-100\" : \"opacity-0\"\r\n                  )}\r\n                />\r\n                <Badge className={option.color}>\r\n                  {option.label}\r\n                </Badge>\r\n              </CommandItem>\r\n            ))}\r\n          </CommandGroup>\r\n        </Command>\r\n      </PopoverContent>\r\n    </Popover>\r\n  );\r\n}\r\n\r\n// Approve status selector for changing approve status\r\ninterface ApproveStatusSelectorProps {\r\n  approveStatus: ApproveStatus | undefined;\r\n  onApproveStatusChange: (status: ApproveStatus) => void;\r\n  disabled?: boolean;\r\n}\r\n\r\nexport function ApproveStatusSelector({ approveStatus, onApproveStatusChange, disabled = false }: ApproveStatusSelectorProps) {\r\n  const [open, setOpen] = useState<boolean>(false);\r\n\r\n  // Các trạng thái phê duyệt có thể có\r\n  const approveStatusOptions = [\r\n    { value: ApproveStatus.PENDING as string, label: 'Chờ phê duyệt', color: 'bg-yellow-100 text-yellow-800 font-normal' },\r\n    { value: ApproveStatus.APPROVED as string, label: 'Đã phê duyệt', color: 'bg-green-100 text-green-800 font-normal' },\r\n  ];\r\n\r\n  const currentStatus = approveStatusOptions.find((option) => option.value === approveStatus) ||\r\n    { value: undefined, label: '---', color: 'bg-gray-100 text-gray-800 font-normal' };\r\n\r\n  return (\r\n    <Popover open={open} onOpenChange={setOpen}>\r\n      <PopoverTrigger asChild>\r\n        <Button\r\n          variant=\"ghost\"\r\n          role=\"combobox\"\r\n          aria-expanded={open}\r\n          disabled={disabled}\r\n          className=\"justify-between h-auto p-1 hover:bg-transparent\"\r\n        >\r\n          <Badge className={currentStatus.color}>\r\n            {currentStatus.label}\r\n          </Badge>\r\n        </Button>\r\n      </PopoverTrigger>\r\n      <PopoverContent className=\"w-[200px] p-0\">\r\n        <Command>\r\n          <CommandInput placeholder=\"Tìm trạng thái...\" />\r\n          <CommandEmpty>Không tìm thấy trạng thái</CommandEmpty>\r\n          <CommandGroup>\r\n            {approveStatusOptions.map((option) => (\r\n              <CommandItem\r\n                key={option.value}\r\n                value={option.value}\r\n                onSelect={() => {\r\n                  onApproveStatusChange(option.value as ApproveStatus);\r\n                  setOpen(false);\r\n                }}\r\n              >\r\n                <Check\r\n                  className={cn(\r\n                    \"mr-2 h-4 w-4\",\r\n                    approveStatus === option.value ? \"opacity-100\" : \"opacity-0\"\r\n                  )}\r\n                />\r\n                <Badge className={option.color}>\r\n                  {option.label}\r\n                </Badge>\r\n              </CommandItem>\r\n            ))}\r\n          </CommandGroup>\r\n        </Command>\r\n      </PopoverContent>\r\n    </Popover>\r\n  );\r\n}\r\n\r\n\r\ninterface OrderBookColumnsProps {\r\n  onViewDetail: (order: OrderBook) => void\r\n  onDelete: (order: OrderBook) => void\r\n  onEdit: (order: OrderBook) => void\r\n  onChangeStatus: (order: OrderBook, status: OrderStatus) => void\r\n  onExtendSettlement?: (order: OrderBook) => void\r\n  onChangeApproveStatus?: (order: OrderBook, approveStatus: ApproveStatus) => void\r\n  currentQuote?: ForexQuote | null\r\n}\r\n\r\nexport function getOrderBookColumns({\r\n  onViewDetail,\r\n  onDelete,\r\n  onEdit,\r\n  onChangeStatus,\r\n  onExtendSettlement,\r\n  onChangeApproveStatus,\r\n  currentQuote\r\n}: OrderBookColumnsProps): ColumnDef<OrderBook>[] {\r\n  return [\r\n    {\r\n      accessorKey: \"contractNumber\",\r\n      header: ({ column }) => {\r\n        return (\r\n          <Button\r\n            variant=\"ghost\"\r\n            size=\"sm\"\r\n            onClick={() => column.toggleSorting(column.getIsSorted() === \"asc\")}\r\n            className=\"flex items-center gap-1 px-0 h-6 py-0\"\r\n            data-column-id=\"contractNumber\"\r\n          >\r\n            <span className=\"text-xs\">Số hợp đồng</span>\r\n            <ArrowUpDown className=\"ml-1 h-3 w-3\" />\r\n          </Button>\r\n        )\r\n      },\r\n      meta: {\r\n        header: \"Số hợp đồng\"\r\n      },\r\n      cell: ({ row }) => {\r\n        const contractNumber = row.original.contractNumber;\r\n        return (\r\n          <div className=\"text-sm\">\r\n            {contractNumber || \"---\"}\r\n          </div>\r\n        )\r\n      },\r\n      enableSorting: true,\r\n    },\r\n    {\r\n      accessorKey: \"orderType\",\r\n      header: ({ column }) => {\r\n        return (\r\n          <Button\r\n            variant=\"ghost\"\r\n            size=\"sm\"\r\n            onClick={() => column.toggleSorting(column.getIsSorted() === \"asc\")}\r\n            className=\"flex items-center gap-1 px-0 h-6 py-0\"\r\n            data-column-id=\"orderType\"\r\n          >\r\n            <span className=\"text-xs\">Loại lệnh</span>\r\n            <ArrowUpDown className=\"ml-1 h-3 w-3\" />\r\n          </Button>\r\n        )\r\n      },\r\n      meta: {\r\n        header: \"Loại lệnh\"\r\n      },\r\n      cell: ({ row }) => {\r\n        const type = row.getValue(\"orderType\") as OrderType\r\n        return (\r\n          <Badge className={getOrderTypeColor(type)}>\r\n            {getOrderTypeName(type)}\r\n          </Badge>\r\n        )\r\n      },\r\n      enableSorting: true,\r\n    },\r\n    {\r\n      accessorKey: \"businessType\",\r\n      header: ({ column }) => {\r\n        return (\r\n          <Button\r\n            variant=\"ghost\"\r\n            size=\"sm\"\r\n            onClick={() => column.toggleSorting(column.getIsSorted() === \"asc\")}\r\n            className=\"flex items-center gap-1 px-0 h-6 py-0\"\r\n            data-column-id=\"businessType\"\r\n          >\r\n            <span className=\"text-xs\">Loại hình giao dịch</span>\r\n            <ArrowUpDown className=\"ml-1 h-3 w-3\" />\r\n          </Button>\r\n        )\r\n      },\r\n      meta: {\r\n        header: \"Loại hình giao dịch\"\r\n      },\r\n      cell: ({ row }) => {\r\n        const businessType = row.getValue(\"businessType\") as BusinessType | undefined\r\n\r\n        if (!businessType || row.original.orderType !== OrderType.BUY) {\r\n          return <span className=\"text-xs text-muted-foreground\">---</span>\r\n        }\r\n\r\n        return (\r\n          <Badge className={getBusinessTypeColor(businessType)}>\r\n            {businessType === BusinessType.NORMAL ? \"Bạc online ký quỹ\" : \"Bạc giao ngay\"}\r\n          </Badge>\r\n        )\r\n      },\r\n      enableSorting: true,\r\n    },\r\n    {\r\n      accessorKey: \"totalPrice\",\r\n      header: ({ column }) => {\r\n        return (\r\n          <Button\r\n            variant=\"ghost\"\r\n            size=\"sm\"\r\n            onClick={() => column.toggleSorting(column.getIsSorted() === \"asc\")}\r\n            className=\"flex items-center gap-1 px-0 h-6 py-0\"\r\n            data-column-id=\"totalPrice\"\r\n          >\r\n            <span className=\"text-xs\">Tổng giá trị</span>\r\n            <ArrowUpDown className=\"ml-1 h-3 w-3\" />\r\n          </Button>\r\n        )\r\n      },\r\n      meta: {\r\n        header: \"Tổng giá trị\"\r\n      },\r\n      cell: ({ row }) => {\r\n        const order = row.original;\r\n        const totalPrice = calculateRealTimePrice(order, currentQuote || null);\r\n\r\n        return (\r\n          <div className=\"flex items-center gap-1 text-sm\">\r\n            <span className={getPriceColor(order)}>\r\n              {formatCurrency(totalPrice)} VND\r\n            </span>\r\n          </div>\r\n        )\r\n      },\r\n      enableSorting: true,\r\n    },\r\n    {\r\n      id: \"totalVolume\",\r\n      header: ({ column }) => {\r\n        return (\r\n          <Button\r\n            variant=\"ghost\"\r\n            size=\"sm\"\r\n            className=\"flex items-center gap-1 px-0 h-6 py-0\"\r\n            data-column-id=\"totalVolume\"\r\n          >\r\n            <span className=\"text-xs\">Tổng khối lượng</span>\r\n          </Button>\r\n        )\r\n      },\r\n      meta: {\r\n        header: \"Tổng khối lượng\"\r\n      },\r\n      cell: ({ row }) => {\r\n        const order = row.original;\r\n        // Tính tổng khối lượng bằng cách nhân số lượng với oz của token\r\n        const totalVolume = order.details?.reduce((sum, detail) => {\r\n          const volume = detail.quantity || 0;\r\n          const weight = detail.product?.weight || 1;\r\n          return sum + (volume * weight);\r\n        }, 0) || 0;\r\n        return (\r\n          <div className=\"text-sm\">\r\n            {totalVolume.toLocaleString()}\r\n          </div>\r\n        )\r\n      },\r\n      enableSorting: false,\r\n    },\r\n    {\r\n      accessorKey: \"depositPrice\",\r\n      header: ({ column }) => {\r\n        return (\r\n          <Button\r\n            variant=\"ghost\"\r\n            size=\"sm\"\r\n            onClick={() => column.toggleSorting(column.getIsSorted() === \"asc\")}\r\n            className=\"flex items-center gap-1 px-0 h-6 py-0\"\r\n            data-column-id=\"depositPrice\"\r\n          >\r\n            <span className=\"text-xs\">Tiền ký quỹ</span>\r\n            <ArrowUpDown className=\"ml-1 h-3 w-3\" />\r\n          </Button>\r\n        )\r\n      },\r\n      meta: {\r\n        header: \"Tiền ký quỹ\"\r\n      },\r\n      cell: ({ row }) => {\r\n        const depositPrice = row.getValue(\"depositPrice\") as number | undefined\r\n\r\n        return (\r\n          <div className=\"flex items-center gap-1 text-sm\">\r\n            <span>{formatCurrency(depositPrice)} VND</span>\r\n          </div>\r\n        )\r\n      },\r\n      enableSorting: true,\r\n    },\r\n    {\r\n      accessorKey: \"settlementPrice\",\r\n      header: ({ column }) => {\r\n        return (\r\n          <Button\r\n            variant=\"ghost\"\r\n            size=\"sm\"\r\n            onClick={() => column.toggleSorting(column.getIsSorted() === \"asc\")}\r\n            className=\"flex items-center gap-1 px-0 h-6 py-0\"\r\n            data-column-id=\"settlementPrice\"\r\n          >\r\n            <span className=\"text-xs\">Tiền tất toán</span>\r\n            <ArrowUpDown className=\"ml-1 h-3 w-3\" />\r\n          </Button>\r\n        )\r\n      },\r\n      meta: {\r\n        header: \"Tiền tất toán\"\r\n      },\r\n      cell: ({ row }) => {\r\n        const order = row.original;\r\n        const settlementPrice = calculateRealTimeSettlement(order, currentQuote || null);\r\n\r\n        return (\r\n          <div className=\"flex items-center gap-1 text-sm\">\r\n            <span className={getPriceColor(order)}>\r\n              {formatCurrency(settlementPrice)} VND\r\n            </span>\r\n          </div>\r\n        )\r\n      },\r\n      enableSorting: true,\r\n    },\r\n    {\r\n      accessorKey: \"storageFee\",\r\n      header: ({ column }) => {\r\n        return (\r\n          <Button\r\n            variant=\"ghost\"\r\n            size=\"sm\"\r\n            onClick={() => column.toggleSorting(column.getIsSorted() === \"asc\")}\r\n            className=\"flex items-center gap-1 px-0 h-6 py-0\"\r\n            data-column-id=\"storageFee\"\r\n          >\r\n            <span className=\"text-xs\">Phí lưu kho</span>\r\n            <ArrowUpDown className=\"ml-1 h-3 w-3\" />\r\n          </Button>\r\n        )\r\n      },\r\n      meta: {\r\n        header: \"Phí lưu kho\"\r\n      },\r\n      cell: ({ row }) => {\r\n        const storageFee = row.getValue(\"storageFee\") as number | undefined\r\n\r\n        return (\r\n          <div className=\"flex items-center gap-1 text-sm\">\r\n            <span>{formatCurrency(storageFee)} VND</span>\r\n          </div>\r\n        )\r\n      },\r\n      enableSorting: true,\r\n    },\r\n    {\r\n      accessorKey: \"settlementDeadline\",\r\n      header: ({ column }) => {\r\n        return (\r\n          <Button\r\n            variant=\"ghost\"\r\n            size=\"sm\"\r\n            onClick={() => column.toggleSorting(column.getIsSorted() === \"asc\")}\r\n            className=\"flex items-center gap-1 px-0 h-6 py-0\"\r\n            data-column-id=\"settlementDeadline\"\r\n          >\r\n            <span className=\"text-xs\">Hạn tất toán</span>\r\n            <ArrowUpDown className=\"ml-1 h-3 w-3\" />\r\n          </Button>\r\n        )\r\n      },\r\n      meta: {\r\n        header: \"Hạn tất toán\"\r\n      },\r\n      cell: ({ row }) => {\r\n        const date = row.original\r\n        return (\r\n          <DateTimeDisplay \r\n            date={date.settlementDeadline}\r\n            className=\"text-sm\"\r\n            timeClassName=\"text-xs text-muted-foreground\" \r\n          />\r\n        )\r\n      },\r\n      enableSorting: true,\r\n      size: 150,\r\n    },\r\n    {\r\n      accessorKey: \"settlementAt\",\r\n      header: ({ column }) => {\r\n        return (\r\n          <Button\r\n            variant=\"ghost\"\r\n            size=\"sm\"\r\n            onClick={() => column.toggleSorting(column.getIsSorted() === \"asc\")}\r\n            className=\"flex items-center gap-1 px-0 h-6 py-0\"\r\n            data-column-id=\"settlementAt\"\r\n          >\r\n            <span className=\"text-xs\">Ngày tất toán</span>\r\n            <ArrowUpDown className=\"ml-1 h-3 w-3\" />\r\n          </Button>\r\n        )\r\n      },\r\n      meta: {\r\n        header: \"Ngày tất toán\"\r\n      },\r\n      cell: ({ row }) => {\r\n        const date = row.original\r\n        return (\r\n          <DateTimeDisplay \r\n            date={date.settlementAt}\r\n            className=\"text-sm\"\r\n            timeClassName=\"text-xs text-muted-foreground\" \r\n          />\r\n        )\r\n      },\r\n      enableSorting: true,\r\n      size: 150,\r\n    },\r\n    {\r\n      accessorKey: \"approveStatus\",\r\n      header: ({ column }) => {\r\n        return (\r\n          <Button\r\n            variant=\"ghost\"\r\n            size=\"sm\"\r\n            onClick={() => column.toggleSorting(column.getIsSorted() === \"asc\")}\r\n            className=\"flex items-center gap-1 px-0 h-6 py-0\"\r\n            data-column-id=\"approveStatus\"\r\n          >\r\n            <span className=\"text-xs\">Phê duyệt</span>\r\n            <ArrowUpDown className=\"ml-1 h-3 w-3\" />\r\n          </Button>\r\n        )\r\n      },\r\n      meta: {\r\n        header: \"Phê duyệt\"\r\n      },\r\n      cell: ({ row }) => {\r\n        const approveStatus = row.original.approveStatus\r\n        const order = row.original\r\n\r\n        const handleApproveStatusChange = (newStatus: ApproveStatus) => {\r\n          if (onChangeApproveStatus) {\r\n            onChangeApproveStatus(order, newStatus);\r\n          }\r\n        };\r\n\r\n        return (\r\n          <ApproveStatusSelector\r\n            approveStatus={approveStatus}\r\n            onApproveStatusChange={handleApproveStatusChange}\r\n          />\r\n        )\r\n      },\r\n      enableSorting: true,\r\n      size: 120,\r\n    },\r\n    {\r\n      accessorKey: \"status\",\r\n      header: ({ column }) => {\r\n        return (\r\n          <Button\r\n            variant=\"ghost\"\r\n            size=\"sm\"\r\n            onClick={() => column.toggleSorting(column.getIsSorted() === \"asc\")}\r\n            className=\"flex items-center gap-1 px-0 h-6 py-0\"\r\n            data-column-id=\"status\"\r\n          >\r\n            <span className=\"text-xs\">Trạng thái</span>\r\n            <ArrowUpDown className=\"ml-1 h-3 w-3\" />\r\n          </Button>\r\n        )\r\n      },\r\n      meta: {\r\n        header: \"Trạng thái\"\r\n      },\r\n      cell: ({ row }) => {\r\n        const status = row.getValue(\"status\") as OrderStatus\r\n        const order = row.original\r\n\r\n        return (\r\n          <Badge className={getStatusColor(status)}>\r\n            {getStatusName(status)}\r\n          </Badge>\r\n        )\r\n      },\r\n      enableSorting: true,\r\n      size: 120,\r\n    },\r\n    {\r\n      accessorKey: \"creator\",\r\n      header: ({ column }) => {\r\n        return (\r\n          <Button\r\n            variant=\"ghost\"\r\n            size=\"sm\"\r\n            onClick={() => column.toggleSorting(column.getIsSorted() === \"asc\")}\r\n            className=\"flex items-center gap-1 px-0 h-6 py-0\"\r\n            data-column-id=\"creator\"\r\n          >\r\n            <span className=\"text-xs\">Người tạo</span>\r\n            <ArrowUpDown className=\"ml-1 h-3 w-3\" />\r\n          </Button>\r\n        )\r\n      },\r\n      meta: {\r\n        header: \"Người tạo\"\r\n      },\r\n      cell: ({ row }) => {\r\n        const order = row.original;\r\n\r\n        // Kiểm tra cả hai trường hợp: creator là object hoặc createdBy là string\r\n        const hasCreator = order.creator && typeof order.creator === 'object';\r\n        const hasCreatedBy = order.createdBy && typeof order.createdBy === 'string';\r\n\r\n        // Nếu có thông tin user, hiển thị thông tin chi tiết\r\n        if (hasCreator) {\r\n          return (\r\n            <UserHoverCard user={order.creator} showAvatar={true} size=\"sm\">\r\n              <div className=\"max-w-[120px] overflow-hidden\">\r\n                <div className=\"text-xs font-normal truncate\">{order.creator?.fullName || order.creator?.username || 'User'}</div>\r\n                <div className=\"text-xs text-muted-foreground truncate\">{order.creator?.email || ''}</div>\r\n              </div>\r\n            </UserHoverCard>\r\n          );\r\n        }\r\n\r\n        // Nếu chỉ có ID, hiển thị ID rút gọn\r\n        if (hasCreatedBy) {\r\n          return (\r\n            <UserHoverCard userId={order.createdBy} showAvatar={true} size=\"sm\">\r\n              <div className=\"flex items-center gap-1\">\r\n                <User className=\"h-4 w-4 text-blue-500\" />\r\n                <span className=\"text-xs text-muted-foreground\">\r\n                  {order.createdBy?.substring(0, 8)}...\r\n                </span>\r\n              </div>\r\n            </UserHoverCard>\r\n          );\r\n        }\r\n\r\n        // Nếu không có thông tin, hiển thị ---\r\n        return (\r\n          <span className=\"text-xs text-muted-foreground\">---</span>\r\n        );\r\n      },\r\n      enableSorting: true,\r\n    },\r\n    {\r\n      accessorKey: \"createdAt\",\r\n      header: ({ column }) => {\r\n        return (\r\n          <Button\r\n            variant=\"ghost\"\r\n            size=\"sm\"\r\n            onClick={() => column.toggleSorting(column.getIsSorted() === \"asc\")}\r\n            className=\"flex items-center gap-1 px-0 h-6 py-0\"\r\n            data-column-id=\"createdAt\"\r\n          >\r\n            <span className=\"text-xs\">Ngày tạo</span>\r\n            <ArrowUpDown className=\"ml-1 h-3 w-3\" />\r\n          </Button>\r\n        )\r\n      },\r\n      meta: {\r\n        header: \"Ngày tạo\"\r\n      },\r\n      cell: ({ row }) => {\r\n        const date = row.original\r\n        return (\r\n          <DateTimeDisplay \r\n            date={date.createdAt}\r\n            className=\"text-sm\"\r\n            timeClassName=\"text-xs text-muted-foreground\" \r\n          />\r\n        )\r\n      },\r\n      enableSorting: true,\r\n      size: 150,\r\n    },\r\n    {\r\n      accessorKey: \"updater\",\r\n      header: ({ column }) => {\r\n        return (\r\n          <Button\r\n            variant=\"ghost\"\r\n            size=\"sm\"\r\n            onClick={() => column.toggleSorting(column.getIsSorted() === \"asc\")}\r\n            className=\"flex items-center gap-1 px-0 h-6 py-0\"\r\n            data-column-id=\"updater\"\r\n          >\r\n            <span className=\"text-xs\">Người cập nhật</span>\r\n            <ArrowUpDown className=\"ml-1 h-3 w-3\" />\r\n          </Button>\r\n        )\r\n      },\r\n      meta: {\r\n        header: \"Người cập nhật\"\r\n      },\r\n      cell: ({ row }) => {\r\n        const order = row.original;\r\n\r\n        // Kiểm tra cả hai trường hợp: updater là object hoặc updatedBy là string\r\n        const hasUpdater = order.updater && typeof order.updater === 'object';\r\n        const hasUpdatedBy = order.updatedBy && typeof order.updatedBy === 'string';\r\n\r\n        // Nếu có thông tin user, hiển thị thông tin chi tiết\r\n        if (hasUpdater) {\r\n          return (\r\n            <UserHoverCard user={order.updater} showAvatar={true} size=\"sm\">\r\n              <div className=\"max-w-[120px] overflow-hidden\">\r\n                <div className=\"text-xs font-normal truncate\">{order.updater?.fullName || order.updater?.username || 'User'}</div>\r\n                <div className=\"text-xs text-muted-foreground truncate\">{order.updater?.email || ''}</div>\r\n              </div>\r\n            </UserHoverCard>\r\n          );\r\n        }\r\n\r\n        // Nếu chỉ có ID, hiển thị ID rút gọn\r\n        if (hasUpdatedBy) {\r\n          return (\r\n            <UserHoverCard userId={order.updatedBy} showAvatar={true} size=\"sm\">\r\n              <div className=\"flex items-center gap-1\">\r\n                <User className=\"h-4 w-4 text-blue-500\" />\r\n                <span className=\"text-xs text-muted-foreground\">\r\n                  {order.updatedBy?.substring(0, 8)}...\r\n                </span>\r\n              </div>\r\n            </UserHoverCard>\r\n          );\r\n        }\r\n\r\n        // Nếu không có thông tin, hiển thị ---\r\n        return (\r\n          <span className=\"text-xs text-muted-foreground\">---</span>\r\n        );\r\n      },\r\n      enableSorting: true,\r\n      enableHiding: true,\r\n    },\r\n    {\r\n      id: 'actions',\r\n      size: 40,\r\n      enableHiding: false,\r\n      header: () => <div data-column-id=\"actions\"></div>,\r\n      cell: ({ row }) => (\r\n        <Actions\r\n          row={row}\r\n          onViewDetail={onViewDetail}\r\n          onDelete={onDelete}\r\n          onEdit={onEdit}\r\n          onChangeStatus={onChangeStatus}\r\n          onExtendSettlement={onExtendSettlement}\r\n        />\r\n      ),\r\n      meta: {\r\n        isSticky: true, // Đánh dấu cột này là cố định\r\n        position: 'right', // Vị trí cố định (right hoặc left)\r\n        header: \"Thao tác\"\r\n      }\r\n    }\r\n  ]\r\n}"], "names": [], "mappings": ";;;;;;AAAA;AACA;AACA;AACA;AAQA;AAQA;AAMA;AAOA;AACA;AACA;AAAA;AAGA;AAAA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAYA;AACA;AACA;;;;;;;;;;;;;;;;;;;AAEA,2BAA2B;AAC3B,MAAM,iBAAiB,CAAC;IACtB,IAAI,WAAW,aAAa,WAAW,MAAM,OAAO;IACpD,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;QACpC,uBAAuB;IACzB,GAAG,MAAM,CAAC;AACZ;AAEA,8BAA8B;AAC9B,MAAM,aAAa,CAAC;IAClB,IAAI,CAAC,SAAS,OAAO;IACrB,MAAM,OAAO,IAAI,KAAK;IACtB,IAAI,CAAC,CAAA,GAAA,iMAAA,CAAA,UAAO,AAAD,EAAE,OAAO,OAAO;IAC3B,IAAI;QACF,OAAO,CAAA,GAAA,gNAAA,CAAA,SAAM,AAAD,EAAE,MAAM,oBAAoB;YAAE,QAAQ,sMAAA,CAAA,KAAE;QAAC;IACvD,EAAE,OAAO,OAAO;QACd,OAAO;IACT;AACF;AAEA,mCAAmC;AACnC,MAAM,iBAAiB,CAAC;IACtB,OAAQ;QACN,KAAK,4KAAA,CAAA,cAAW,CAAC,SAAS;YACxB,OAAO;QACT,KAAK,4KAAA,CAAA,cAAW,CAAC,SAAS;YACxB,OAAO;QACT,KAAK,4KAAA,CAAA,cAAW,CAAC,UAAU;YACzB,OAAO;QACT,KAAK,4KAAA,CAAA,cAAW,CAAC,SAAS;YACxB,OAAO;QACT;YACE,OAAO;IACX;AACF;AAEA,6BAA6B;AAC7B,MAAM,gBAAgB,CAAC;IACrB,OAAQ;QACN,KAAK,4KAAA,CAAA,cAAW,CAAC,SAAS;YACxB,OAAO;QACT,KAAK,4KAAA,CAAA,cAAW,CAAC,SAAS;YACxB,OAAO;QACT,KAAK,4KAAA,CAAA,cAAW,CAAC,UAAU;YACzB,OAAO;QACT,KAAK,4KAAA,CAAA,cAAW,CAAC,SAAS;YACxB,OAAO;QACT;YACE,OAAO;IACX;AACF;AAEA,2BAA2B;AAC3B,MAAM,mBAAmB,CAAC;IACxB,OAAQ;QACN,KAAK,4KAAA,CAAA,YAAS,CAAC,GAAG;YAChB,OAAO;QACT,KAAK,4KAAA,CAAA,YAAS,CAAC,IAAI;YACjB,OAAO;QACT;YACE,OAAO;IACX;AACF;AAEA,2BAA2B;AAC3B,MAAM,oBAAoB,CAAC;IACzB,OAAQ;QACN,KAAK,4KAAA,CAAA,YAAS,CAAC,GAAG;YAChB,OAAO;QACT,KAAK,4KAAA,CAAA,YAAS,CAAC,IAAI;YACjB,OAAO;QACT;YACE,OAAO;IACX;AACF;AAEA,8BAA8B;AAC9B,MAAM,uBAAuB,CAAC;IAC5B,OAAQ;QACN,KAAK,4KAAA,CAAA,eAAY,CAAC,MAAM;YACtB,OAAO;QACT,KAAK,4KAAA,CAAA,eAAY,CAAC,kBAAkB;YAClC,OAAO;QACT;YACE,OAAO;IACX;AACF;AAEA,qCAAqC;AACrC,MAAM,wBAAwB,CAAC;IAC7B,OAAQ;QACN,KAAK,4KAAA,CAAA,gBAAa,CAAC,OAAO;YACxB,OAAO;QACT,KAAK,4KAAA,CAAA,gBAAa,CAAC,QAAQ;YACzB,OAAO;QACT;YACE,OAAO;IACX;AACF;AAEA,+BAA+B;AAC/B,MAAM,uBAAuB,CAAC;IAC5B,OAAQ;QACN,KAAK,4KAAA,CAAA,gBAAa,CAAC,OAAO;YACxB,OAAO;QACT,KAAK,4KAAA,CAAA,gBAAa,CAAC,QAAQ;YACzB,OAAO;QACT;YACE,OAAO;IACX;AACF;AAEA,yDAAyD;AACzD,MAAM,yBAAyB,CAAC,OAAkB;IAChD,IAAI,CAAC,gBAAgB,MAAM,MAAM,KAAK,4KAAA,CAAA,cAAW,CAAC,SAAS,EAAE;QAC3D,OAAO,MAAM,UAAU,IAAI;IAC7B;IAEA,gDAAgD;IAChD,MAAM,QAAQ,MAAM,SAAS,KAAK,4KAAA,CAAA,YAAS,CAAC,GAAG,GAAG,aAAa,QAAQ,GAAG,aAAa,QAAQ;IAE/F,mCAAmC;IACnC,MAAM,cAAc,MAAM,OAAO,EAAE,OAAO,CAAC,KAAK;QAC9C,MAAM,SAAS,OAAO,QAAQ,IAAI;QAClC,MAAM,SAAS,OAAO,OAAO,EAAE,UAAU;QACzC,OAAO,MAAO,SAAS;IACzB,GAAG,MAAM;IAET,4BAA4B;IAC5B,MAAM,YAAY,QAAQ,cAAc,SAAS;IACjD,OAAO,KAAK,KAAK,CAAC,YAAa,YAAY,OAAQ,cAAc;AACnE;AAEA,+DAA+D;AAC/D,MAAM,8BAA8B,CAAC,OAAkB;IACrD,IAAI,CAAC,gBAAgB,MAAM,MAAM,KAAK,4KAAA,CAAA,cAAW,CAAC,SAAS,EAAE;QAC3D,OAAO,MAAM,eAAe;IAC9B;IAEA,MAAM,aAAa,uBAAuB,OAAO;IAEjD,0CAA0C;IAC1C,IAAI,MAAM,YAAY,KAAK,4KAAA,CAAA,eAAY,CAAC,kBAAkB,EAAE;QAC1D,OAAO;IACT;IAEA,0EAA0E;IAC1E,OAAO,KAAK,KAAK,CAAC,aAAa;AACjC;AAEA,6DAA6D;AAC7D,MAAM,gBAAgB,CAAC;IACrB,IAAI,MAAM,MAAM,KAAK,4KAAA,CAAA,cAAW,CAAC,SAAS,EAAE,OAAO;IACnD,OAAO,MAAM,SAAS,KAAK,4KAAA,CAAA,YAAS,CAAC,GAAG,GAAG,mBAAmB;AAChE;AAWA,SAAS,QAAQ,EAAE,GAAG,EAAE,YAAY,EAAE,QAAQ,EAAE,MAAM,EAAE,cAAc,EAAE,kBAAkB,EAAgB;;IACxG,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,sQAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,sQAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,QAAQ,IAAI,QAAQ;IAE1B,MAAM,eAAe,CAAC;QACpB,SAAS;QACT,oBAAoB;IACtB;IAEA,uCAAuC;IACvC,MAAM,mBAAmB,OAAO;QAC9B,IAAI;YACF,gCAAgC;YAChC,MAAM,WAAW,MAAM,6GAAA,CAAA,MAAG,CAAC,GAAG,CAAY,CAAC,YAAY,EAAE,MAAM,EAAE,EAAE;YAEnE,wDAAwD;YACxD,IAAI,UAAU;gBACZ,aAAa;YACf,OAAO;gBACL,gDAAgD;gBAChD,2QAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACd;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,gCAAgC;YAC9C,wCAAwC;YACxC,2QAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd;IACF;IAEA,oCAAoC;IACpC,MAAM,aAAa,OAAO;QACxB,IAAI;YACF,sEAAsE;YACtE,MAAM,WAAW,MAAM,6GAAA,CAAA,MAAG,CAAC,GAAG,CAAY,CAAC,YAAY,EAAE,MAAM,EAAE,EAAE;YAEnE,wDAAwD;YACxD,IAAI,UAAU;gBACZ,sCAAsC;gBACtC,0CAA0C;gBAC1C,IAAI,WAAW;gBACf,IAAI,SAAS,SAAS,KAAK,4KAAA,CAAA,YAAS,CAAC,IAAI,EAAE;oBACzC,WAAW;gBACb,OAAO,IAAI,SAAS,SAAS,KAAK,4KAAA,CAAA,YAAS,CAAC,UAAU,EAAE;oBACtD,WAAW;gBACb;gBAEA,+CAA+C;gBAC/C,MAAM,mBAAmB;oBACvB,GAAG,QAAQ;oBACX,MAAM;oBACN,+DAA+D;oBAC/D,iBAAiB;oBACjB,uCAAuC;oBACvC,OAAO;gBACT;gBAEA,OAAO;YACT,OAAO;gBACL,gDAAgD;gBAChD,2QAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACd;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yCAAyC;YACvD,wCAAwC;YACxC,2QAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd;IACF;IAEA,6CAA6C;IAC7C,MAAM,YAAY;QAEhB,mEAAmE;QACnE,OACE,MAAM,MAAM,KAAK,4KAAA,CAAA,cAAW,CAAC,SAAS,IACtC,MAAM,kBAAkB,CAAC,uBAAuB;;IAEpD;IAEA,qBACE;;0BAEE,sSAAC,8HAAA,CAAA,SAAM;gBAAC,MAAM;gBAAkB,cAAc;0BAC5C,cAAA,sSAAC,8HAAA,CAAA,gBAAa;;sCACZ,sSAAC,8HAAA,CAAA,eAAY;;8CACX,sSAAC,8HAAA,CAAA,cAAW;8CAAC;;;;;;8CACb,sSAAC,8HAAA,CAAA,oBAAiB;;wCAAC;sDACc,sSAAC;4CAAK,WAAU;sDAAe,MAAM,EAAE;;;;;;wCAAQ;sDAC9E,sSAAC;4CAAE,WAAU;sDAAO;;;;;;;;;;;;;;;;;;sCAGxB,sSAAC,8HAAA,CAAA,eAAY;;8CACX,sSAAC,8HAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAU,SAAS,IAAM,oBAAoB;8CAAQ;;;;;;8CACrE,sSAAC,8HAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAc,SAAS,IAAM,aAAa;8CAAQ;;;;;;;;;;;;;;;;;;;;;;;0BAMxE,sSAAC,8HAAA,CAAA,SAAM;gBAAC,MAAM;gBAAkB,cAAc;0BAC5C,cAAA,sSAAC,8HAAA,CAAA,gBAAa;;sCACZ,sSAAC,8HAAA,CAAA,eAAY;;8CACX,sSAAC,8HAAA,CAAA,cAAW;8CAAC;;;;;;8CACb,sSAAC,8HAAA,CAAA,oBAAiB;;wCAAC;sDAEjB,sSAAC;4CAAE,WAAU;sDAAO;;;;;;;;;;;;;;;;;;sCAGxB,sSAAC,8HAAA,CAAA,eAAY;;8CACX,sSAAC,8HAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAU,SAAS,IAAM,oBAAoB;8CAAQ;;;;;;8CACrE,sSAAC,8HAAA,CAAA,SAAM;oCACL,SAAS;wCACP,IAAI,oBAAoB;4CACtB,mBAAmB;wCACrB;wCACA,oBAAoB;oCACtB;8CACD;;;;;;;;;;;;;;;;;;;;;;;0BAOP,sSAAC;gBAAI,WAAU;0BACb,cAAA,sSAAC,wIAAA,CAAA,eAAY;;sCACX,sSAAC,wIAAA,CAAA,sBAAmB;4BAAC,OAAO;sCAC1B,cAAA,sSAAC,8HAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,WAAU;;kDAEV,sSAAC,iTAAA,CAAA,eAAY;wCAAC,WAAU;;;;;;kDACxB,sSAAC;wCAAK,WAAU;kDAAU;;;;;;;;;;;;;;;;;sCAG9B,sSAAC,wIAAA,CAAA,sBAAmB;4BAAC,OAAM;4BAAM,WAAU;;8CACzC,sSAAC,wIAAA,CAAA,mBAAgB;oCAAC,SAAS,IAAM,iBAAiB;;sDAChD,sSAAC,uRAAA,CAAA,MAAG;4CAAC,WAAU;;;;;;sDACf,sSAAC;4CAAK,WAAU;sDAAiB;;;;;;sDACjC,sSAAC,wIAAA,CAAA,uBAAoB;4CAAC,WAAU;sDAAU;;;;;;;;;;;;8CAE5C,sSAAC,wIAAA,CAAA,mBAAgB;oCAAC,SAAS,IAAM,WAAW;;sDAC1C,sSAAC,kSAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;sDAChB,sSAAC;4CAAK,WAAU;sDAAiB;;;;;;sDACjC,sSAAC,wIAAA,CAAA,uBAAoB;4CAAC,WAAU;sDAAU;;;;;;;;;;;;8CAG5C,sSAAC,wIAAA,CAAA,wBAAqB;;;;;gCAGrB,MAAM,MAAM,KAAK,4KAAA,CAAA,cAAW,CAAC,SAAS,IAAI,MAAM,MAAM,KAAK,4KAAA,CAAA,cAAW,CAAC,UAAU,kBAChF;;sDAEE,sSAAC,wIAAA,CAAA,mBAAgB;4CAAC,SAAS,IAAM,eAAe,OAAO,4KAAA,CAAA,cAAW,CAAC,SAAS;;8DAC1E,sSAAC,2RAAA,CAAA,QAAK;oDAAC,WAAU;;;;;;8DACjB,sSAAC;oDAAK,WAAU;8DAAgC;;;;;;;;;;;;sDAGlD,sSAAC,wIAAA,CAAA,mBAAgB;4CACf,SAAS,IAAM,eAAe,OAAO,4KAAA,CAAA,cAAW,CAAC,UAAU;;8DAE3D,sSAAC,mRAAA,CAAA,IAAC;oDAAC,WAAU;;;;;;8DACb,sSAAC;oDAAK,WAAU;8DAA8B;;;;;;;;;;;;wCAI/C,eAAe,oCACd,sSAAC,wIAAA,CAAA,mBAAgB;4CAAC,SAAS,IAAM,oBAAoB;;8DACnD,sSAAC,6SAAA,CAAA,eAAY;oDAAC,WAAU;;;;;;8DACxB,sSAAC;oDAAK,WAAU;8DAA+B;;;;;;;;;;;;sDAInD,sSAAC,wIAAA,CAAA,wBAAqB;;;;;;;8CAI1B,sSAAC,wIAAA,CAAA,mBAAgB;oCACf,SAAS,IAAM,oBAAoB;oCACnC,WAAU;;sDAEV,sSAAC,iSAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;sDAClB,sSAAC;4CAAK,WAAU;sDAAkC;;;;;;sDAClD,sSAAC,wIAAA,CAAA,uBAAoB;4CAAC,WAAU;sDAA2B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOzE;GA/LS;KAAA;AAwMF,SAAS,oBAAoB,EAAE,MAAM,EAAE,cAAc,EAAE,WAAW,KAAK,EAA4B;;IACxG,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,sQAAA,CAAA,WAAQ,AAAD,EAAW;IAE1C,kCAAkC;IAClC,MAAM,gBAAgB;QACpB;YAAE,OAAO,4KAAA,CAAA,cAAW,CAAC,SAAS;YAAY,OAAO;YAAe,OAAO;QAA0C;QACjH;YAAE,OAAO,4KAAA,CAAA,cAAW,CAAC,UAAU;YAAY,OAAO;YAAmB,OAAO;QAAsC;QAClH;YAAE,OAAO,4KAAA,CAAA,cAAW,CAAC,SAAS;YAAY,OAAO;YAAa,OAAO;QAA4C;QACjH;YAAE,OAAO,4KAAA,CAAA,cAAW,CAAC,SAAS;YAAY,OAAO;YAAU,OAAO;QAAwC;KAE3G;IAED,MAAM,gBAAgB,cAAc,IAAI,CAAC,CAAC,SAAW,OAAO,KAAK,KAAK,WAAW,aAAa,CAAC,EAAE;IAEjG,qBACE,sSAAC,+HAAA,CAAA,UAAO;QAAC,MAAM;QAAM,cAAc;;0BACjC,sSAAC,+HAAA,CAAA,iBAAc;gBAAC,OAAO;0BACrB,cAAA,sSAAC,8HAAA,CAAA,SAAM;oBACL,SAAQ;oBACR,MAAK;oBACL,iBAAe;oBACf,UAAU;oBACV,WAAU;8BAEV,cAAA,sSAAC,6HAAA,CAAA,QAAK;wBAAC,WAAW,cAAc,KAAK;kCAClC,cAAc,KAAK;;;;;;;;;;;;;;;;0BAI1B,sSAAC,+HAAA,CAAA,iBAAc;gBAAC,WAAU;0BACxB,cAAA,sSAAC,+HAAA,CAAA,UAAO;;sCACN,sSAAC,+HAAA,CAAA,eAAY;4BAAC,aAAY;;;;;;sCAC1B,sSAAC,+HAAA,CAAA,eAAY;sCAAC;;;;;;sCACd,sSAAC,+HAAA,CAAA,eAAY;sCACV,cAAc,GAAG,CAAC,CAAC,uBAClB,sSAAC,+HAAA,CAAA,cAAW;oCAEV,OAAO,OAAO,KAAK;oCACnB,UAAU;wCACR,eAAe,OAAO,KAAK;wCAC3B,QAAQ;oCACV;;sDAEA,sSAAC,2RAAA,CAAA,QAAK;4CACJ,WAAW,CAAA,GAAA,+HAAA,CAAA,KAAE,AAAD,EACV,gBACA,WAAW,OAAO,KAAK,GAAG,gBAAgB;;;;;;sDAG9C,sSAAC,6HAAA,CAAA,QAAK;4CAAC,WAAW,OAAO,KAAK;sDAC3B,OAAO,KAAK;;;;;;;mCAdV,OAAO,KAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;AAuBjC;IA3DgB;MAAA;AAoET,SAAS,sBAAsB,EAAE,aAAa,EAAE,qBAAqB,EAAE,WAAW,KAAK,EAA8B;;IAC1H,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,sQAAA,CAAA,WAAQ,AAAD,EAAW;IAE1C,qCAAqC;IACrC,MAAM,uBAAuB;QAC3B;YAAE,OAAO,4KAAA,CAAA,gBAAa,CAAC,OAAO;YAAY,OAAO;YAAiB,OAAO;QAA4C;QACrH;YAAE,OAAO,4KAAA,CAAA,gBAAa,CAAC,QAAQ;YAAY,OAAO;YAAgB,OAAO;QAA0C;KACpH;IAED,MAAM,gBAAgB,qBAAqB,IAAI,CAAC,CAAC,SAAW,OAAO,KAAK,KAAK,kBAC3E;QAAE,OAAO;QAAW,OAAO;QAAO,OAAO;IAAwC;IAEnF,qBACE,sSAAC,+HAAA,CAAA,UAAO;QAAC,MAAM;QAAM,cAAc;;0BACjC,sSAAC,+HAAA,CAAA,iBAAc;gBAAC,OAAO;0BACrB,cAAA,sSAAC,8HAAA,CAAA,SAAM;oBACL,SAAQ;oBACR,MAAK;oBACL,iBAAe;oBACf,UAAU;oBACV,WAAU;8BAEV,cAAA,sSAAC,6HAAA,CAAA,QAAK;wBAAC,WAAW,cAAc,KAAK;kCAClC,cAAc,KAAK;;;;;;;;;;;;;;;;0BAI1B,sSAAC,+HAAA,CAAA,iBAAc;gBAAC,WAAU;0BACxB,cAAA,sSAAC,+HAAA,CAAA,UAAO;;sCACN,sSAAC,+HAAA,CAAA,eAAY;4BAAC,aAAY;;;;;;sCAC1B,sSAAC,+HAAA,CAAA,eAAY;sCAAC;;;;;;sCACd,sSAAC,+HAAA,CAAA,eAAY;sCACV,qBAAqB,GAAG,CAAC,CAAC,uBACzB,sSAAC,+HAAA,CAAA,cAAW;oCAEV,OAAO,OAAO,KAAK;oCACnB,UAAU;wCACR,sBAAsB,OAAO,KAAK;wCAClC,QAAQ;oCACV;;sDAEA,sSAAC,2RAAA,CAAA,QAAK;4CACJ,WAAW,CAAA,GAAA,+HAAA,CAAA,KAAE,AAAD,EACV,gBACA,kBAAkB,OAAO,KAAK,GAAG,gBAAgB;;;;;;sDAGrD,sSAAC,6HAAA,CAAA,QAAK;4CAAC,WAAW,OAAO,KAAK;sDAC3B,OAAO,KAAK;;;;;;;mCAdV,OAAO,KAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;AAuBjC;IAzDgB;MAAA;AAsET,SAAS,oBAAoB,EAClC,YAAY,EACZ,QAAQ,EACR,MAAM,EACN,cAAc,EACd,kBAAkB,EAClB,qBAAqB,EACrB,YAAY,EACU;IACtB,OAAO;QACL;YACE,aAAa;YACb,QAAQ,CAAC,EAAE,MAAM,EAAE;gBACjB,qBACE,sSAAC,8HAAA,CAAA,SAAM;oBACL,SAAQ;oBACR,MAAK;oBACL,SAAS,IAAM,OAAO,aAAa,CAAC,OAAO,WAAW,OAAO;oBAC7D,WAAU;oBACV,kBAAe;;sCAEf,sSAAC;4BAAK,WAAU;sCAAU;;;;;;sCAC1B,sSAAC,+SAAA,CAAA,cAAW;4BAAC,WAAU;;;;;;;;;;;;YAG7B;YACA,MAAM;gBACJ,QAAQ;YACV;YACA,MAAM,CAAC,EAAE,GAAG,EAAE;gBACZ,MAAM,iBAAiB,IAAI,QAAQ,CAAC,cAAc;gBAClD,qBACE,sSAAC;oBAAI,WAAU;8BACZ,kBAAkB;;;;;;YAGzB;YACA,eAAe;QACjB;QACA;YACE,aAAa;YACb,QAAQ,CAAC,EAAE,MAAM,EAAE;gBACjB,qBACE,sSAAC,8HAAA,CAAA,SAAM;oBACL,SAAQ;oBACR,MAAK;oBACL,SAAS,IAAM,OAAO,aAAa,CAAC,OAAO,WAAW,OAAO;oBAC7D,WAAU;oBACV,kBAAe;;sCAEf,sSAAC;4BAAK,WAAU;sCAAU;;;;;;sCAC1B,sSAAC,+SAAA,CAAA,cAAW;4BAAC,WAAU;;;;;;;;;;;;YAG7B;YACA,MAAM;gBACJ,QAAQ;YACV;YACA,MAAM,CAAC,EAAE,GAAG,EAAE;gBACZ,MAAM,OAAO,IAAI,QAAQ,CAAC;gBAC1B,qBACE,sSAAC,6HAAA,CAAA,QAAK;oBAAC,WAAW,kBAAkB;8BACjC,iBAAiB;;;;;;YAGxB;YACA,eAAe;QACjB;QACA;YACE,aAAa;YACb,QAAQ,CAAC,EAAE,MAAM,EAAE;gBACjB,qBACE,sSAAC,8HAAA,CAAA,SAAM;oBACL,SAAQ;oBACR,MAAK;oBACL,SAAS,IAAM,OAAO,aAAa,CAAC,OAAO,WAAW,OAAO;oBAC7D,WAAU;oBACV,kBAAe;;sCAEf,sSAAC;4BAAK,WAAU;sCAAU;;;;;;sCAC1B,sSAAC,+SAAA,CAAA,cAAW;4BAAC,WAAU;;;;;;;;;;;;YAG7B;YACA,MAAM;gBACJ,QAAQ;YACV;YACA,MAAM,CAAC,EAAE,GAAG,EAAE;gBACZ,MAAM,eAAe,IAAI,QAAQ,CAAC;gBAElC,IAAI,CAAC,gBAAgB,IAAI,QAAQ,CAAC,SAAS,KAAK,4KAAA,CAAA,YAAS,CAAC,GAAG,EAAE;oBAC7D,qBAAO,sSAAC;wBAAK,WAAU;kCAAgC;;;;;;gBACzD;gBAEA,qBACE,sSAAC,6HAAA,CAAA,QAAK;oBAAC,WAAW,qBAAqB;8BACpC,iBAAiB,4KAAA,CAAA,eAAY,CAAC,MAAM,GAAG,sBAAsB;;;;;;YAGpE;YACA,eAAe;QACjB;QACA;YACE,aAAa;YACb,QAAQ,CAAC,EAAE,MAAM,EAAE;gBACjB,qBACE,sSAAC,8HAAA,CAAA,SAAM;oBACL,SAAQ;oBACR,MAAK;oBACL,SAAS,IAAM,OAAO,aAAa,CAAC,OAAO,WAAW,OAAO;oBAC7D,WAAU;oBACV,kBAAe;;sCAEf,sSAAC;4BAAK,WAAU;sCAAU;;;;;;sCAC1B,sSAAC,+SAAA,CAAA,cAAW;4BAAC,WAAU;;;;;;;;;;;;YAG7B;YACA,MAAM;gBACJ,QAAQ;YACV;YACA,MAAM,CAAC,EAAE,GAAG,EAAE;gBACZ,MAAM,QAAQ,IAAI,QAAQ;gBAC1B,MAAM,aAAa,uBAAuB,OAAO,gBAAgB;gBAEjE,qBACE,sSAAC;oBAAI,WAAU;8BACb,cAAA,sSAAC;wBAAK,WAAW,cAAc;;4BAC5B,eAAe;4BAAY;;;;;;;;;;;;YAIpC;YACA,eAAe;QACjB;QACA;YACE,IAAI;YACJ,QAAQ,CAAC,EAAE,MAAM,EAAE;gBACjB,qBACE,sSAAC,8HAAA,CAAA,SAAM;oBACL,SAAQ;oBACR,MAAK;oBACL,WAAU;oBACV,kBAAe;8BAEf,cAAA,sSAAC;wBAAK,WAAU;kCAAU;;;;;;;;;;;YAGhC;YACA,MAAM;gBACJ,QAAQ;YACV;YACA,MAAM,CAAC,EAAE,GAAG,EAAE;gBACZ,MAAM,QAAQ,IAAI,QAAQ;gBAC1B,gEAAgE;gBAChE,MAAM,cAAc,MAAM,OAAO,EAAE,OAAO,CAAC,KAAK;oBAC9C,MAAM,SAAS,OAAO,QAAQ,IAAI;oBAClC,MAAM,SAAS,OAAO,OAAO,EAAE,UAAU;oBACzC,OAAO,MAAO,SAAS;gBACzB,GAAG,MAAM;gBACT,qBACE,sSAAC;oBAAI,WAAU;8BACZ,YAAY,cAAc;;;;;;YAGjC;YACA,eAAe;QACjB;QACA;YACE,aAAa;YACb,QAAQ,CAAC,EAAE,MAAM,EAAE;gBACjB,qBACE,sSAAC,8HAAA,CAAA,SAAM;oBACL,SAAQ;oBACR,MAAK;oBACL,SAAS,IAAM,OAAO,aAAa,CAAC,OAAO,WAAW,OAAO;oBAC7D,WAAU;oBACV,kBAAe;;sCAEf,sSAAC;4BAAK,WAAU;sCAAU;;;;;;sCAC1B,sSAAC,+SAAA,CAAA,cAAW;4BAAC,WAAU;;;;;;;;;;;;YAG7B;YACA,MAAM;gBACJ,QAAQ;YACV;YACA,MAAM,CAAC,EAAE,GAAG,EAAE;gBACZ,MAAM,eAAe,IAAI,QAAQ,CAAC;gBAElC,qBACE,sSAAC;oBAAI,WAAU;8BACb,cAAA,sSAAC;;4BAAM,eAAe;4BAAc;;;;;;;;;;;;YAG1C;YACA,eAAe;QACjB;QACA;YACE,aAAa;YACb,QAAQ,CAAC,EAAE,MAAM,EAAE;gBACjB,qBACE,sSAAC,8HAAA,CAAA,SAAM;oBACL,SAAQ;oBACR,MAAK;oBACL,SAAS,IAAM,OAAO,aAAa,CAAC,OAAO,WAAW,OAAO;oBAC7D,WAAU;oBACV,kBAAe;;sCAEf,sSAAC;4BAAK,WAAU;sCAAU;;;;;;sCAC1B,sSAAC,+SAAA,CAAA,cAAW;4BAAC,WAAU;;;;;;;;;;;;YAG7B;YACA,MAAM;gBACJ,QAAQ;YACV;YACA,MAAM,CAAC,EAAE,GAAG,EAAE;gBACZ,MAAM,QAAQ,IAAI,QAAQ;gBAC1B,MAAM,kBAAkB,4BAA4B,OAAO,gBAAgB;gBAE3E,qBACE,sSAAC;oBAAI,WAAU;8BACb,cAAA,sSAAC;wBAAK,WAAW,cAAc;;4BAC5B,eAAe;4BAAiB;;;;;;;;;;;;YAIzC;YACA,eAAe;QACjB;QACA;YACE,aAAa;YACb,QAAQ,CAAC,EAAE,MAAM,EAAE;gBACjB,qBACE,sSAAC,8HAAA,CAAA,SAAM;oBACL,SAAQ;oBACR,MAAK;oBACL,SAAS,IAAM,OAAO,aAAa,CAAC,OAAO,WAAW,OAAO;oBAC7D,WAAU;oBACV,kBAAe;;sCAEf,sSAAC;4BAAK,WAAU;sCAAU;;;;;;sCAC1B,sSAAC,+SAAA,CAAA,cAAW;4BAAC,WAAU;;;;;;;;;;;;YAG7B;YACA,MAAM;gBACJ,QAAQ;YACV;YACA,MAAM,CAAC,EAAE,GAAG,EAAE;gBACZ,MAAM,aAAa,IAAI,QAAQ,CAAC;gBAEhC,qBACE,sSAAC;oBAAI,WAAU;8BACb,cAAA,sSAAC;;4BAAM,eAAe;4BAAY;;;;;;;;;;;;YAGxC;YACA,eAAe;QACjB;QACA;YACE,aAAa;YACb,QAAQ,CAAC,EAAE,MAAM,EAAE;gBACjB,qBACE,sSAAC,8HAAA,CAAA,SAAM;oBACL,SAAQ;oBACR,MAAK;oBACL,SAAS,IAAM,OAAO,aAAa,CAAC,OAAO,WAAW,OAAO;oBAC7D,WAAU;oBACV,kBAAe;;sCAEf,sSAAC;4BAAK,WAAU;sCAAU;;;;;;sCAC1B,sSAAC,+SAAA,CAAA,cAAW;4BAAC,WAAU;;;;;;;;;;;;YAG7B;YACA,MAAM;gBACJ,QAAQ;YACV;YACA,MAAM,CAAC,EAAE,GAAG,EAAE;gBACZ,MAAM,OAAO,IAAI,QAAQ;gBACzB,qBACE,sSAAC,+IAAA,CAAA,kBAAe;oBACd,MAAM,KAAK,kBAAkB;oBAC7B,WAAU;oBACV,eAAc;;;;;;YAGpB;YACA,eAAe;YACf,MAAM;QACR;QACA;YACE,aAAa;YACb,QAAQ,CAAC,EAAE,MAAM,EAAE;gBACjB,qBACE,sSAAC,8HAAA,CAAA,SAAM;oBACL,SAAQ;oBACR,MAAK;oBACL,SAAS,IAAM,OAAO,aAAa,CAAC,OAAO,WAAW,OAAO;oBAC7D,WAAU;oBACV,kBAAe;;sCAEf,sSAAC;4BAAK,WAAU;sCAAU;;;;;;sCAC1B,sSAAC,+SAAA,CAAA,cAAW;4BAAC,WAAU;;;;;;;;;;;;YAG7B;YACA,MAAM;gBACJ,QAAQ;YACV;YACA,MAAM,CAAC,EAAE,GAAG,EAAE;gBACZ,MAAM,OAAO,IAAI,QAAQ;gBACzB,qBACE,sSAAC,+IAAA,CAAA,kBAAe;oBACd,MAAM,KAAK,YAAY;oBACvB,WAAU;oBACV,eAAc;;;;;;YAGpB;YACA,eAAe;YACf,MAAM;QACR;QACA;YACE,aAAa;YACb,QAAQ,CAAC,EAAE,MAAM,EAAE;gBACjB,qBACE,sSAAC,8HAAA,CAAA,SAAM;oBACL,SAAQ;oBACR,MAAK;oBACL,SAAS,IAAM,OAAO,aAAa,CAAC,OAAO,WAAW,OAAO;oBAC7D,WAAU;oBACV,kBAAe;;sCAEf,sSAAC;4BAAK,WAAU;sCAAU;;;;;;sCAC1B,sSAAC,+SAAA,CAAA,cAAW;4BAAC,WAAU;;;;;;;;;;;;YAG7B;YACA,MAAM;gBACJ,QAAQ;YACV;YACA,MAAM,CAAC,EAAE,GAAG,EAAE;gBACZ,MAAM,gBAAgB,IAAI,QAAQ,CAAC,aAAa;gBAChD,MAAM,QAAQ,IAAI,QAAQ;gBAE1B,MAAM,4BAA4B,CAAC;oBACjC,IAAI,uBAAuB;wBACzB,sBAAsB,OAAO;oBAC/B;gBACF;gBAEA,qBACE,sSAAC;oBACC,eAAe;oBACf,uBAAuB;;;;;;YAG7B;YACA,eAAe;YACf,MAAM;QACR;QACA;YACE,aAAa;YACb,QAAQ,CAAC,EAAE,MAAM,EAAE;gBACjB,qBACE,sSAAC,8HAAA,CAAA,SAAM;oBACL,SAAQ;oBACR,MAAK;oBACL,SAAS,IAAM,OAAO,aAAa,CAAC,OAAO,WAAW,OAAO;oBAC7D,WAAU;oBACV,kBAAe;;sCAEf,sSAAC;4BAAK,WAAU;sCAAU;;;;;;sCAC1B,sSAAC,+SAAA,CAAA,cAAW;4BAAC,WAAU;;;;;;;;;;;;YAG7B;YACA,MAAM;gBACJ,QAAQ;YACV;YACA,MAAM,CAAC,EAAE,GAAG,EAAE;gBACZ,MAAM,SAAS,IAAI,QAAQ,CAAC;gBAC5B,MAAM,QAAQ,IAAI,QAAQ;gBAE1B,qBACE,sSAAC,6HAAA,CAAA,QAAK;oBAAC,WAAW,eAAe;8BAC9B,cAAc;;;;;;YAGrB;YACA,eAAe;YACf,MAAM;QACR;QACA;YACE,aAAa;YACb,QAAQ,CAAC,EAAE,MAAM,EAAE;gBACjB,qBACE,sSAAC,8HAAA,CAAA,SAAM;oBACL,SAAQ;oBACR,MAAK;oBACL,SAAS,IAAM,OAAO,aAAa,CAAC,OAAO,WAAW,OAAO;oBAC7D,WAAU;oBACV,kBAAe;;sCAEf,sSAAC;4BAAK,WAAU;sCAAU;;;;;;sCAC1B,sSAAC,+SAAA,CAAA,cAAW;4BAAC,WAAU;;;;;;;;;;;;YAG7B;YACA,MAAM;gBACJ,QAAQ;YACV;YACA,MAAM,CAAC,EAAE,GAAG,EAAE;gBACZ,MAAM,QAAQ,IAAI,QAAQ;gBAE1B,yEAAyE;gBACzE,MAAM,aAAa,MAAM,OAAO,IAAI,OAAO,MAAM,OAAO,KAAK;gBAC7D,MAAM,eAAe,MAAM,SAAS,IAAI,OAAO,MAAM,SAAS,KAAK;gBAEnE,qDAAqD;gBACrD,IAAI,YAAY;oBACd,qBACE,sSAAC,yJAAA,CAAA,gBAAa;wBAAC,MAAM,MAAM,OAAO;wBAAE,YAAY;wBAAM,MAAK;kCACzD,cAAA,sSAAC;4BAAI,WAAU;;8CACb,sSAAC;oCAAI,WAAU;8CAAgC,MAAM,OAAO,EAAE,YAAY,MAAM,OAAO,EAAE,YAAY;;;;;;8CACrG,sSAAC;oCAAI,WAAU;8CAA0C,MAAM,OAAO,EAAE,SAAS;;;;;;;;;;;;;;;;;gBAIzF;gBAEA,qCAAqC;gBACrC,IAAI,cAAc;oBAChB,qBACE,sSAAC,yJAAA,CAAA,gBAAa;wBAAC,QAAQ,MAAM,SAAS;wBAAE,YAAY;wBAAM,MAAK;kCAC7D,cAAA,sSAAC;4BAAI,WAAU;;8CACb,sSAAC,yRAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;8CAChB,sSAAC;oCAAK,WAAU;;wCACb,MAAM,SAAS,EAAE,UAAU,GAAG;wCAAG;;;;;;;;;;;;;;;;;;gBAK5C;gBAEA,uCAAuC;gBACvC,qBACE,sSAAC;oBAAK,WAAU;8BAAgC;;;;;;YAEpD;YACA,eAAe;QACjB;QACA;YACE,aAAa;YACb,QAAQ,CAAC,EAAE,MAAM,EAAE;gBACjB,qBACE,sSAAC,8HAAA,CAAA,SAAM;oBACL,SAAQ;oBACR,MAAK;oBACL,SAAS,IAAM,OAAO,aAAa,CAAC,OAAO,WAAW,OAAO;oBAC7D,WAAU;oBACV,kBAAe;;sCAEf,sSAAC;4BAAK,WAAU;sCAAU;;;;;;sCAC1B,sSAAC,+SAAA,CAAA,cAAW;4BAAC,WAAU;;;;;;;;;;;;YAG7B;YACA,MAAM;gBACJ,QAAQ;YACV;YACA,MAAM,CAAC,EAAE,GAAG,EAAE;gBACZ,MAAM,OAAO,IAAI,QAAQ;gBACzB,qBACE,sSAAC,+IAAA,CAAA,kBAAe;oBACd,MAAM,KAAK,SAAS;oBACpB,WAAU;oBACV,eAAc;;;;;;YAGpB;YACA,eAAe;YACf,MAAM;QACR;QACA;YACE,aAAa;YACb,QAAQ,CAAC,EAAE,MAAM,EAAE;gBACjB,qBACE,sSAAC,8HAAA,CAAA,SAAM;oBACL,SAAQ;oBACR,MAAK;oBACL,SAAS,IAAM,OAAO,aAAa,CAAC,OAAO,WAAW,OAAO;oBAC7D,WAAU;oBACV,kBAAe;;sCAEf,sSAAC;4BAAK,WAAU;sCAAU;;;;;;sCAC1B,sSAAC,+SAAA,CAAA,cAAW;4BAAC,WAAU;;;;;;;;;;;;YAG7B;YACA,MAAM;gBACJ,QAAQ;YACV;YACA,MAAM,CAAC,EAAE,GAAG,EAAE;gBACZ,MAAM,QAAQ,IAAI,QAAQ;gBAE1B,yEAAyE;gBACzE,MAAM,aAAa,MAAM,OAAO,IAAI,OAAO,MAAM,OAAO,KAAK;gBAC7D,MAAM,eAAe,MAAM,SAAS,IAAI,OAAO,MAAM,SAAS,KAAK;gBAEnE,qDAAqD;gBACrD,IAAI,YAAY;oBACd,qBACE,sSAAC,yJAAA,CAAA,gBAAa;wBAAC,MAAM,MAAM,OAAO;wBAAE,YAAY;wBAAM,MAAK;kCACzD,cAAA,sSAAC;4BAAI,WAAU;;8CACb,sSAAC;oCAAI,WAAU;8CAAgC,MAAM,OAAO,EAAE,YAAY,MAAM,OAAO,EAAE,YAAY;;;;;;8CACrG,sSAAC;oCAAI,WAAU;8CAA0C,MAAM,OAAO,EAAE,SAAS;;;;;;;;;;;;;;;;;gBAIzF;gBAEA,qCAAqC;gBACrC,IAAI,cAAc;oBAChB,qBACE,sSAAC,yJAAA,CAAA,gBAAa;wBAAC,QAAQ,MAAM,SAAS;wBAAE,YAAY;wBAAM,MAAK;kCAC7D,cAAA,sSAAC;4BAAI,WAAU;;8CACb,sSAAC,yRAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;8CAChB,sSAAC;oCAAK,WAAU;;wCACb,MAAM,SAAS,EAAE,UAAU,GAAG;wCAAG;;;;;;;;;;;;;;;;;;gBAK5C;gBAEA,uCAAuC;gBACvC,qBACE,sSAAC;oBAAK,WAAU;8BAAgC;;;;;;YAEpD;YACA,eAAe;YACf,cAAc;QAChB;QACA;YACE,IAAI;YACJ,MAAM;YACN,cAAc;YACd,QAAQ,kBAAM,sSAAC;oBAAI,kBAAe;;;;;;YAClC,MAAM,CAAC,EAAE,GAAG,EAAE,iBACZ,sSAAC;oBACC,KAAK;oBACL,cAAc;oBACd,UAAU;oBACV,QAAQ;oBACR,gBAAgB;oBAChB,oBAAoB;;;;;;YAGxB,MAAM;gBACJ,UAAU;gBACV,UAAU;gBACV,QAAQ;YACV;QACF;KACD;AACH", "debugId": null}}, {"offset": {"line": 2630, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/bloomgoo/gold-exchange-platform/frontend/components/common/selector/column-visibility-selector.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { But<PERSON> } from '@/components/ui/button';\r\nimport { Checkbox } from '@/components/ui/checkbox';\r\nimport {\r\n  DropdownMenu,\r\n  DropdownMenuContent,\r\n  DropdownMenuTrigger,\r\n} from '@/components/ui/dropdown-menu';\r\nimport { Eye, Search, Settings2 } from 'lucide-react';\r\nimport { Table } from '@tanstack/react-table';\r\nimport { Input } from '@/components/ui/input';\r\nimport { useState, useMemo } from 'react';\r\n\r\n// Định nghĩa kiểu dữ liệu cho thuộc tính meta của cột\r\ndeclare module '@tanstack/react-table' {\r\n  interface ColumnMeta<TData, TValue> {\r\n    isSticky?: boolean;\r\n    position?: 'left' | 'right';\r\n    header?: string;\r\n  }\r\n}\r\n\r\ninterface ColumnVisibilitySelectorProps<TData> {\r\n  table: Table<TData>;\r\n  className?: string;\r\n  buttonClassName?: string;\r\n  contentClassName?: string;\r\n  label?: string;\r\n}\r\n\r\nexport function ColumnVisibilitySelector<TData>({\r\n  table,\r\n  className,\r\n  buttonClassName,\r\n  contentClassName = 'w-[250px] max-h-[var(--radix-dropdown-menu-content-available-height)] overflow-y-auto',\r\n  label = 'Tất cả',\r\n}: ColumnVisibilitySelectorProps<TData>) {\r\n  // State để lưu từ khóa tìm kiếm\r\n  const [searchQuery, setSearchQuery] = useState('');\r\n  // Lấy tất cả các cột có thể ẩn/hiện\r\n  const allColumns = useMemo(() => {\r\n    return table.getAllColumns().filter(column => column.getCanHide());\r\n  }, [table]);\r\n\r\n  // Lấy các cột đang hiển thị\r\n  const visibleColumns = useMemo(() => {\r\n    return allColumns.filter(column => column.getIsVisible());\r\n  }, [allColumns, table.getState().columnVisibility]);\r\n\r\n  // Lọc các cột dựa trên từ khóa tìm kiếm\r\n  const filteredColumns = useMemo(() => {\r\n    if (!searchQuery.trim()) {\r\n      return allColumns;\r\n    }\r\n\r\n    const query = searchQuery.toLowerCase().trim();\r\n    return allColumns.filter(column => {\r\n      // Lấy tên cột thân thiện từ header\r\n      let header = column.id;\r\n\r\n      // Ưu tiên sử dụng meta.header\r\n      if (column.columnDef.meta?.header) {\r\n        header = column.columnDef.meta.header;\r\n      }\r\n      // Nếu header là string, sử dụng trực tiếp\r\n      else if (typeof column.columnDef.header === 'string') {\r\n        header = column.columnDef.header;\r\n      }\r\n      // Nếu header là function, tìm kiếm phần tử span chứa tên cột\r\n      else if (typeof column.columnDef.header === 'function') {\r\n        const headerElement = document.querySelector(`[data-column-id=\"${column.id}\"] span`);\r\n        if (headerElement && headerElement.textContent) {\r\n          header = headerElement.textContent;\r\n        }\r\n      }\r\n\r\n      return header.toString().toLowerCase().includes(query);\r\n    });\r\n  }, [allColumns, searchQuery]);\r\n\r\n  // Hàm để toggle tất cả các cột\r\n  const toggleAllColumns = (checked: boolean) => {\r\n    table.toggleAllColumnsVisible(checked);\r\n  };\r\n\r\n  return (\r\n    <div className={className}>\r\n      <DropdownMenu>\r\n        <DropdownMenuTrigger asChild>\r\n          <Button\r\n            variant=\"ghost\"\r\n            size=\"icon\"\r\n            className={`relative ${buttonClassName || ''}`}\r\n          >\r\n            <Settings2 className=\"h-4 w-4\" />\r\n            {visibleColumns.length < allColumns.length && (\r\n              <span className=\"absolute -top-1 -right-1 h-2 w-2 rounded-full bg-blue-600\" />\r\n            )}\r\n          </Button>\r\n        </DropdownMenuTrigger>\r\n        <DropdownMenuContent\r\n          align=\"end\"\r\n          className={contentClassName}\r\n        >\r\n          <div className=\"sticky top-0 bg-background z-10 border-b\">\r\n            <div className=\"p-2\">\r\n              <div className=\"flex items-center space-x-2 px-1 py-1\">\r\n                <Checkbox\r\n                  checked={visibleColumns.length === allColumns.length}\r\n                  onCheckedChange={(checked) => toggleAllColumns(!!checked)}\r\n                  id=\"all-columns\"\r\n                />\r\n                <label\r\n                  htmlFor=\"all-columns\"\r\n                  className=\"text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70\"\r\n                >\r\n                  {label}\r\n                </label>\r\n              </div>\r\n\r\n              {/* Thêm ô tìm kiếm */}\r\n              <div className=\"relative mt-2\">\r\n                <Search className=\"absolute left-2 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground\" />\r\n                <Input\r\n                  placeholder=\"Tìm kiếm cột...\"\r\n                  value={searchQuery}\r\n                  onChange={(e) => setSearchQuery(e.target.value)}\r\n                  className=\"pl-8 h-8 text-sm\"\r\n                />\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <div className=\"p-2 pt-0\">\r\n            {filteredColumns.length === 0 ? (\r\n              <div className=\"py-6 text-center text-sm text-muted-foreground\">\r\n                Không tìm thấy cột nào\r\n              </div>\r\n            ) : (\r\n              filteredColumns.map(column => {\r\n                const isVisible = column.getIsVisible();\r\n                return (\r\n                  <div\r\n                    key={column.id}\r\n                    className=\"flex items-center space-x-2 px-1 py-1.5 rounded hover:bg-accent\"\r\n                  >\r\n                    <Checkbox\r\n                      checked={isVisible}\r\n                      onCheckedChange={(checked) => column.toggleVisibility(!!checked)}\r\n                      id={column.id}\r\n                    />\r\n                    <label\r\n                      htmlFor={column.id}\r\n                      className=\"flex-1 text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70\"\r\n                    >\r\n                      {(() => {\r\n                        // Ưu tiên sử dụng meta.header\r\n                        if (column.columnDef.meta?.header) {\r\n                          return column.columnDef.meta.header;\r\n                        }\r\n\r\n                        // Nếu header là string, sử dụng trực tiếp\r\n                        if (typeof column.columnDef.header === 'string') {\r\n                          return column.columnDef.header;\r\n                        }\r\n\r\n                        // Tìm kiếm phần tử span trong header\r\n                        const headerElement = document.querySelector(`[data-column-id=\"${column.id}\"] span`);\r\n                        if (headerElement && headerElement.textContent) {\r\n                          return headerElement.textContent;\r\n                        }\r\n\r\n                        // Fallback: Sử dụng ID\r\n                        return column.id;\r\n                      })()}\r\n                    </label>\r\n                  </div>\r\n                );\r\n              })\r\n            )}\r\n          </div>\r\n        </DropdownMenuContent>\r\n      </DropdownMenu>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAKA;AAAA;AAEA;AACA;;;AAZA;;;;;;;AA+BO,SAAS,yBAAgC,EAC9C,KAAK,EACL,SAAS,EACT,eAAe,EACf,mBAAmB,uFAAuF,EAC1G,QAAQ,QAAQ,EACqB;;IACrC,gCAAgC;IAChC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,sQAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,oCAAoC;IACpC,MAAM,aAAa,CAAA,GAAA,sQAAA,CAAA,UAAO,AAAD;wDAAE;YACzB,OAAO,MAAM,aAAa,GAAG,MAAM;gEAAC,CAAA,SAAU,OAAO,UAAU;;QACjE;uDAAG;QAAC;KAAM;IAEV,4BAA4B;IAC5B,MAAM,iBAAiB,CAAA,GAAA,sQAAA,CAAA,UAAO,AAAD;4DAAE;YAC7B,OAAO,WAAW,MAAM;oEAAC,CAAA,SAAU,OAAO,YAAY;;QACxD;2DAAG;QAAC;QAAY,MAAM,QAAQ,GAAG,gBAAgB;KAAC;IAElD,wCAAwC;IACxC,MAAM,kBAAkB,CAAA,GAAA,sQAAA,CAAA,UAAO,AAAD;6DAAE;YAC9B,IAAI,CAAC,YAAY,IAAI,IAAI;gBACvB,OAAO;YACT;YAEA,MAAM,QAAQ,YAAY,WAAW,GAAG,IAAI;YAC5C,OAAO,WAAW,MAAM;qEAAC,CAAA;oBACvB,mCAAmC;oBACnC,IAAI,SAAS,OAAO,EAAE;oBAEtB,8BAA8B;oBAC9B,IAAI,OAAO,SAAS,CAAC,IAAI,EAAE,QAAQ;wBACjC,SAAS,OAAO,SAAS,CAAC,IAAI,CAAC,MAAM;oBACvC,OAEK,IAAI,OAAO,OAAO,SAAS,CAAC,MAAM,KAAK,UAAU;wBACpD,SAAS,OAAO,SAAS,CAAC,MAAM;oBAClC,OAEK,IAAI,OAAO,OAAO,SAAS,CAAC,MAAM,KAAK,YAAY;wBACtD,MAAM,gBAAgB,SAAS,aAAa,CAAC,CAAC,iBAAiB,EAAE,OAAO,EAAE,CAAC,OAAO,CAAC;wBACnF,IAAI,iBAAiB,cAAc,WAAW,EAAE;4BAC9C,SAAS,cAAc,WAAW;wBACpC;oBACF;oBAEA,OAAO,OAAO,QAAQ,GAAG,WAAW,GAAG,QAAQ,CAAC;gBAClD;;QACF;4DAAG;QAAC;QAAY;KAAY;IAE5B,+BAA+B;IAC/B,MAAM,mBAAmB,CAAC;QACxB,MAAM,uBAAuB,CAAC;IAChC;IAEA,qBACE,sSAAC;QAAI,WAAW;kBACd,cAAA,sSAAC,wIAAA,CAAA,eAAY;;8BACX,sSAAC,wIAAA,CAAA,sBAAmB;oBAAC,OAAO;8BAC1B,cAAA,sSAAC,8HAAA,CAAA,SAAM;wBACL,SAAQ;wBACR,MAAK;wBACL,WAAW,CAAC,SAAS,EAAE,mBAAmB,IAAI;;0CAE9C,sSAAC,uSAAA,CAAA,YAAS;gCAAC,WAAU;;;;;;4BACpB,eAAe,MAAM,GAAG,WAAW,MAAM,kBACxC,sSAAC;gCAAK,WAAU;;;;;;;;;;;;;;;;;8BAItB,sSAAC,wIAAA,CAAA,sBAAmB;oBAClB,OAAM;oBACN,WAAW;;sCAEX,sSAAC;4BAAI,WAAU;sCACb,cAAA,sSAAC;gCAAI,WAAU;;kDACb,sSAAC;wCAAI,WAAU;;0DACb,sSAAC,gIAAA,CAAA,WAAQ;gDACP,SAAS,eAAe,MAAM,KAAK,WAAW,MAAM;gDACpD,iBAAiB,CAAC,UAAY,iBAAiB,CAAC,CAAC;gDACjD,IAAG;;;;;;0DAEL,sSAAC;gDACC,SAAQ;gDACR,WAAU;0DAET;;;;;;;;;;;;kDAKL,sSAAC;wCAAI,WAAU;;0DACb,sSAAC,6RAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;0DAClB,sSAAC,6HAAA,CAAA,QAAK;gDACJ,aAAY;gDACZ,OAAO;gDACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;gDAC9C,WAAU;;;;;;;;;;;;;;;;;;;;;;;sCAMlB,sSAAC;4BAAI,WAAU;sCACZ,gBAAgB,MAAM,KAAK,kBAC1B,sSAAC;gCAAI,WAAU;0CAAiD;;;;;uCAIhE,gBAAgB,GAAG,CAAC,CAAA;gCAClB,MAAM,YAAY,OAAO,YAAY;gCACrC,qBACE,sSAAC;oCAEC,WAAU;;sDAEV,sSAAC,gIAAA,CAAA,WAAQ;4CACP,SAAS;4CACT,iBAAiB,CAAC,UAAY,OAAO,gBAAgB,CAAC,CAAC,CAAC;4CACxD,IAAI,OAAO,EAAE;;;;;;sDAEf,sSAAC;4CACC,SAAS,OAAO,EAAE;4CAClB,WAAU;sDAET,CAAC;gDACA,8BAA8B;gDAC9B,IAAI,OAAO,SAAS,CAAC,IAAI,EAAE,QAAQ;oDACjC,OAAO,OAAO,SAAS,CAAC,IAAI,CAAC,MAAM;gDACrC;gDAEA,0CAA0C;gDAC1C,IAAI,OAAO,OAAO,SAAS,CAAC,MAAM,KAAK,UAAU;oDAC/C,OAAO,OAAO,SAAS,CAAC,MAAM;gDAChC;gDAEA,qCAAqC;gDACrC,MAAM,gBAAgB,SAAS,aAAa,CAAC,CAAC,iBAAiB,EAAE,OAAO,EAAE,CAAC,OAAO,CAAC;gDACnF,IAAI,iBAAiB,cAAc,WAAW,EAAE;oDAC9C,OAAO,cAAc,WAAW;gDAClC;gDAEA,uBAAuB;gDACvB,OAAO,OAAO,EAAE;4CAClB,CAAC;;;;;;;mCA/BE,OAAO,EAAE;;;;;4BAmCpB;;;;;;;;;;;;;;;;;;;;;;;AAOd;GA3JgB;KAAA", "debugId": null}}, {"offset": {"line": 2909, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/bloomgoo/gold-exchange-platform/frontend/components/common/selector/sort-selector.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { But<PERSON> } from '@/components/ui/button';\r\nimport {\r\n  Popover,\r\n  PopoverContent,\r\n  PopoverTrigger,\r\n} from '@/components/ui/popover';\r\nimport {\r\n  ArrowDownWideNarrow,\r\n  ArrowUpDown,\r\n  ArrowUpNarrowWide,\r\n  X,\r\n  Plus,\r\n  GripVertical,\r\n  Search\r\n} from 'lucide-react';\r\nimport { Input } from '@/components/ui/input';\r\nimport { SortingState, Table } from '@tanstack/react-table';\r\nimport { cn } from '@/lib/utils';\r\nimport { Badge } from '@/components/ui/badge';\r\n\r\nimport { useState, useMemo, useEffect } from 'react';\r\n\r\n// Định nghĩa kiểu dữ liệu cho thuộc tính meta của cột\r\ndeclare module '@tanstack/react-table' {\r\n  interface ColumnMeta<TData, TValue> {\r\n    isSticky?: boolean;\r\n    position?: 'left' | 'right';\r\n    header?: string;\r\n  }\r\n}\r\nimport {\r\n  DndContext,\r\n  closestCenter,\r\n  KeyboardSensor,\r\n  PointerSensor,\r\n  useSensor,\r\n  useSensors,\r\n  DragEndEvent\r\n} from '@dnd-kit/core';\r\nimport {\r\n  arrayMove,\r\n  SortableContext,\r\n  sortableKeyboardCoordinates,\r\n  useSortable,\r\n  verticalListSortingStrategy,\r\n} from '@dnd-kit/sortable';\r\nimport { CSS } from '@dnd-kit/utilities';\r\n\r\ninterface SortSelectorProps<TData> {\r\n  table: Table<TData>;\r\n  className?: string;\r\n  buttonClassName?: string;\r\n  contentClassName?: string;\r\n  maxSortFields?: number;\r\n}\r\n\r\n// Component cho mỗi mục sort có thể kéo thả\r\ninterface SortableItemProps {\r\n  id: string;\r\n  columnName: string;\r\n  direction: 'asc' | 'desc';\r\n  onDirectionChange: (direction: 'asc' | 'desc') => void;\r\n  onRemove: () => void;\r\n}\r\n\r\nfunction SortableItem({\r\n  id,\r\n  columnName,\r\n  direction,\r\n  onDirectionChange,\r\n  onRemove\r\n}: SortableItemProps) {\r\n  const {\r\n    attributes,\r\n    listeners,\r\n    setNodeRef,\r\n    transform,\r\n    transition,\r\n  } = useSortable({ id });\r\n\r\n  const style = {\r\n    transform: CSS.Transform.toString(transform),\r\n    transition,\r\n  };\r\n\r\n  return (\r\n    <div\r\n      ref={setNodeRef}\r\n      style={style}\r\n      className=\"flex items-center justify-between p-2 mb-1 bg-muted/50 rounded-md\"\r\n    >\r\n      <div className=\"flex items-center gap-2\">\r\n        <button\r\n          {...attributes}\r\n          {...listeners}\r\n          className=\"cursor-grab touch-none p-1 rounded hover:bg-muted\"\r\n        >\r\n          <GripVertical className=\"h-4 w-4 text-muted-foreground\" />\r\n        </button>\r\n        <span className=\"text-sm\">{columnName}</span>\r\n      </div>\r\n      <div className=\"flex items-center gap-1\">\r\n        <Button\r\n          variant=\"ghost\"\r\n          size=\"icon\"\r\n          className=\"h-7 w-7\"\r\n          onClick={() => onDirectionChange(direction === 'asc' ? 'desc' : 'asc')}\r\n        >\r\n          {direction === 'asc' ? (\r\n            <ArrowDownWideNarrow className=\"h-4 w-4\" />\r\n          ) : (\r\n            <ArrowUpNarrowWide className=\"h-4 w-4\" />\r\n          )}\r\n        </Button>\r\n        <Button\r\n          variant=\"ghost\"\r\n          size=\"icon\"\r\n          className=\"h-7 w-7 text-destructive\"\r\n          onClick={onRemove}\r\n        >\r\n          <X className=\"h-4 w-4\" />\r\n        </Button>\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n\r\nexport function SortSelector<TData>({\r\n  table,\r\n  className,\r\n  buttonClassName,\r\n  contentClassName = 'w-[300px]',\r\n  maxSortFields = 3,\r\n}: SortSelectorProps<TData>) {\r\n  const [open, setOpen] = useState(false);\r\n  const [searchQuery, setSearchQuery] = useState('');\r\n  const [tempSorting, setTempSorting] = useState<SortingState>([]);\r\n\r\n  // Lấy tất cả các cột có thể sắp xếp\r\n  const sortableColumns = useMemo(() => {\r\n    return table.getAllColumns().filter(column =>\r\n      column.getCanSort() && column.getIsVisible()\r\n    );\r\n  }, [table]);\r\n\r\n  // Lọc các cột dựa trên từ khóa tìm kiếm\r\n  const filteredColumns = useMemo(() => {\r\n    if (!searchQuery.trim()) {\r\n      return sortableColumns;\r\n    }\r\n\r\n    const query = searchQuery.toLowerCase().trim();\r\n    return sortableColumns.filter(column => {\r\n      // Lấy tên cột thân thiện từ header\r\n      let header = column.id;\r\n\r\n      // Ưu tiên sử dụng meta.header\r\n      if (column.columnDef.meta?.header) {\r\n        header = column.columnDef.meta.header;\r\n      }\r\n      // Nếu header là string, sử dụng trực tiếp\r\n      else if (typeof column.columnDef.header === 'string') {\r\n        header = column.columnDef.header;\r\n      }\r\n      // Nếu header là function, tìm kiếm phần tử span chứa tên cột\r\n      else if (typeof column.columnDef.header === 'function') {\r\n        const headerElement = document.querySelector(`[data-column-id=\"${column.id}\"] span`);\r\n        if (headerElement && headerElement.textContent) {\r\n          header = headerElement.textContent;\r\n        }\r\n      }\r\n\r\n      return header.toString().toLowerCase().includes(query);\r\n    });\r\n  }, [sortableColumns, searchQuery]);\r\n\r\n  // Khi mở dropdown, sao chép trạng thái sắp xếp hiện tại vào tempSorting\r\n  useEffect(() => {\r\n    if (open) {\r\n      setTempSorting(table.getState().sorting);\r\n    }\r\n  }, [open, table]);\r\n\r\n  // Lấy thông tin các cột đang được sắp xếp (dựa trên tempSorting)\r\n  const sortedColumns = useMemo(() => {\r\n    return tempSorting.map(sort => {\r\n      const column = table.getColumn(sort.id);\r\n\r\n      // Lấy tên cột thân thiện từ header\r\n      let columnName = sort.id;\r\n\r\n      if (column) {\r\n        // Ưu tiên sử dụng meta.header\r\n        if (column.columnDef.meta?.header) {\r\n          columnName = column.columnDef.meta.header;\r\n        }\r\n        // Nếu header là string, sử dụng trực tiếp\r\n        else if (typeof column.columnDef.header === 'string') {\r\n          columnName = column.columnDef.header;\r\n        }\r\n        // Nếu header là function, tìm kiếm phần tử span chứa tên cột\r\n        else if (typeof column.columnDef.header === 'function') {\r\n          const headerElement = document.querySelector(`[data-column-id=\"${sort.id}\"] span`);\r\n          if (headerElement && headerElement.textContent) {\r\n            columnName = headerElement.textContent;\r\n          }\r\n        }\r\n      }\r\n\r\n      return {\r\n        id: sort.id,\r\n        name: columnName,\r\n        direction: (sort.desc ? 'desc' : 'asc') as 'desc' | 'asc'\r\n      };\r\n    });\r\n  }, [tempSorting, table]);\r\n\r\n  // Sensors cho DnD\r\n  const sensors = useSensors(\r\n    useSensor(PointerSensor),\r\n    useSensor(KeyboardSensor, {\r\n      coordinateGetter: sortableKeyboardCoordinates,\r\n    })\r\n  );\r\n\r\n  // Xử lý khi kết thúc kéo thả\r\n  const handleDragEnd = (event: DragEndEvent) => {\r\n    const { active, over } = event;\r\n\r\n    if (over && active.id !== over.id) {\r\n      const oldIndex = sortedColumns.findIndex(item => item.id === active.id);\r\n      const newIndex = sortedColumns.findIndex(item => item.id === over.id);\r\n\r\n      const newSortedColumns = arrayMove(sortedColumns, oldIndex, newIndex);\r\n\r\n      // Cập nhật tempSorting\r\n      const newSorting = newSortedColumns.map(col => ({\r\n        id: col.id,\r\n        desc: col.direction === 'desc'\r\n      }));\r\n\r\n      setTempSorting(newSorting);\r\n    }\r\n  };\r\n\r\n  // Xử lý thêm tiêu chí sắp xếp mới\r\n  const handleAddSort = (columnId: string) => {\r\n    // Kiểm tra xem cột đã được sắp xếp chưa\r\n    const existingSort = tempSorting.find(sort => sort.id === columnId);\r\n\r\n    if (existingSort) {\r\n      // Nếu đã có, thay đổi hướng sắp xếp\r\n      const newSorting = tempSorting.map(sort => {\r\n        if (sort.id === columnId) {\r\n          return { ...sort, desc: !sort.desc };\r\n        }\r\n        return sort;\r\n      });\r\n\r\n      setTempSorting(newSorting);\r\n    } else if (tempSorting.length < maxSortFields) {\r\n      // Nếu chưa có và chưa đạt giới hạn, thêm mới\r\n      const newSorting = [\r\n        ...tempSorting,\r\n        { id: columnId, desc: false }\r\n      ];\r\n\r\n      setTempSorting(newSorting);\r\n    }\r\n\r\n    // Xóa từ khóa tìm kiếm sau khi chọn\r\n    setSearchQuery('');\r\n  };\r\n\r\n  // Xử lý thay đổi hướng sắp xếp\r\n  const handleDirectionChange = (columnId: string, direction: 'asc' | 'desc') => {\r\n    const newSorting = tempSorting.map(sort => {\r\n      if (sort.id === columnId) {\r\n        return { ...sort, desc: direction === 'desc' };\r\n      }\r\n      return sort;\r\n    });\r\n\r\n    setTempSorting(newSorting);\r\n  };\r\n\r\n  // Xử lý xóa tiêu chí sắp xếp\r\n  const handleRemoveSort = (columnId: string) => {\r\n    const newSorting = tempSorting.filter(sort => sort.id !== columnId);\r\n    setTempSorting(newSorting);\r\n  };\r\n\r\n  // Xử lý xóa tất cả tiêu chí sắp xếp\r\n  const handleClearAll = () => {\r\n    setTempSorting([]);\r\n  };\r\n\r\n  // Xử lý áp dụng các thay đổi\r\n  const handleApply = () => {\r\n    table.setSorting(tempSorting);\r\n    setOpen(false);\r\n  };\r\n\r\n  // Xử lý hủy thay đổi\r\n  const handleCancel = () => {\r\n    setTempSorting(table.getState().sorting);\r\n    setOpen(false);\r\n  };\r\n\r\n  // Lấy icon sắp xếp cho button\r\n  const getSortIcon = () => {\r\n    const currentSorting = table.getState().sorting;\r\n\r\n    if (currentSorting.length === 0) {\r\n      return <ArrowUpDown className=\"h-4 w-4\" />;\r\n    }\r\n\r\n    if (currentSorting.length === 1) {\r\n      return currentSorting[0].desc ?\r\n        <ArrowUpNarrowWide className=\"h-4 w-4\" /> :\r\n        <ArrowDownWideNarrow className=\"h-4 w-4\" />;\r\n    }\r\n\r\n    // Nếu có nhiều tiêu chí sắp xếp, hiển thị số lượng\r\n    return (\r\n      <div className=\"relative\">\r\n        <ArrowUpDown className=\"h-4 w-4\" />\r\n        <Badge\r\n          className=\"absolute -top-2 -right-2 h-4 w-4 flex items-center justify-center p-0 text-[10px]\"\r\n          variant=\"default\"\r\n        >\r\n          {currentSorting.length}\r\n        </Badge>\r\n      </div>\r\n    );\r\n  };\r\n\r\n  return (\r\n    <div className={className}>\r\n      <Popover open={open} onOpenChange={setOpen}>\r\n        <PopoverTrigger asChild>\r\n          <Button\r\n            variant=\"ghost\"\r\n            size=\"icon\"\r\n            className={cn(\"relative\", buttonClassName, table.getState().sorting.length > 0 && \"bg-accent\")}\r\n          >\r\n            {getSortIcon()}\r\n          </Button>\r\n        </PopoverTrigger>\r\n        <PopoverContent align=\"end\" className={contentClassName} onEscapeKeyDown={handleCancel} onInteractOutside={(e) => e.preventDefault()}>\r\n          <div className=\"p-0\">\r\n            <h4 className=\"mb-2 text-sm font-medium\">Sắp xếp theo</h4>\r\n\r\n            {/* Danh sách các tiêu chí sắp xếp hiện tại */}\r\n            {sortedColumns.length > 0 ? (\r\n              <div className=\"mb-2\">\r\n                <DndContext\r\n                  sensors={sensors}\r\n                  collisionDetection={closestCenter}\r\n                  onDragEnd={handleDragEnd}\r\n                >\r\n                  <SortableContext\r\n                    items={sortedColumns.map(col => col.id)}\r\n                    strategy={verticalListSortingStrategy}\r\n                  >\r\n                    {sortedColumns.map((column) => (\r\n                      <SortableItem\r\n                        key={column.id}\r\n                        id={column.id}\r\n                        columnName={column.name}\r\n                        direction={column.direction}\r\n                        onDirectionChange={(direction) =>\r\n                          handleDirectionChange(column.id, direction)\r\n                        }\r\n                        onRemove={() => handleRemoveSort(column.id)}\r\n                      />\r\n                    ))}\r\n                  </SortableContext>\r\n                </DndContext>\r\n              </div>\r\n            ) : (\r\n              <div className=\"text-sm text-muted-foreground mb-2 py-2 text-center\">\r\n                Chưa có tiêu chí sắp xếp nào\r\n              </div>\r\n            )}\r\n\r\n            {/* Phần chọn trường sắp xếp */}\r\n            {tempSorting.length < maxSortFields && (\r\n              <div className=\"mb-2\">\r\n                <div className=\"text-xs font-medium mb-1 text-muted-foreground\">Thêm tiêu chí sắp xếp</div>\r\n\r\n                {/* Ô tìm kiếm trường */}\r\n                <div className=\"relative mb-2\">\r\n                  <Search className=\"absolute left-2 top-1/2 h-3.5 w-3.5 -translate-y-1/2 text-muted-foreground\" />\r\n                  <Input\r\n                    placeholder=\"Tìm kiếm trường...\"\r\n                    value={searchQuery}\r\n                    onChange={(e) => setSearchQuery(e.target.value)}\r\n                    className=\"pl-7 h-8 text-sm\"\r\n                  />\r\n                </div>\r\n\r\n                {/* Danh sách các trường có thể sắp xếp */}\r\n                <div className=\"max-h-[150px] overflow-y-auto border rounded-md\">\r\n                  {filteredColumns.length === 0 ? (\r\n                    <div className=\"p-2 text-center text-sm text-muted-foreground\">\r\n                      Không tìm thấy trường nào\r\n                    </div>\r\n                  ) : (\r\n                    <div className=\"p-1\">\r\n                      {filteredColumns.map(column => {\r\n                        const isSelected = tempSorting.some(sort => sort.id === column.id);\r\n                        return (\r\n                          <button\r\n                            key={column.id}\r\n                            onClick={() => handleAddSort(column.id)}\r\n                            disabled={isSelected}\r\n                            className={cn(\r\n                              \"w-full text-left px-2 py-1.5 text-sm rounded-md hover:bg-accent flex items-center justify-between\",\r\n                              isSelected && \"opacity-50 cursor-not-allowed\"\r\n                            )}\r\n                          >\r\n                            <span>\r\n                              {(() => {\r\n                                // Ưu tiên sử dụng meta.header\r\n                                if (column.columnDef.meta?.header) {\r\n                                  return column.columnDef.meta.header;\r\n                                }\r\n\r\n                                // Nếu header là string, sử dụng trực tiếp\r\n                                if (typeof column.columnDef.header === 'string') {\r\n                                  return column.columnDef.header;\r\n                                }\r\n\r\n                                // Tìm kiếm phần tử span trong header\r\n                                const headerElement = document.querySelector(`[data-column-id=\"${column.id}\"] span`);\r\n                                if (headerElement && headerElement.textContent) {\r\n                                  return headerElement.textContent;\r\n                                }\r\n\r\n                                // Fallback: Sử dụng ID\r\n                                return column.id;\r\n                              })()}\r\n                            </span>\r\n                          </button>\r\n                        );\r\n                      })}\r\n                    </div>\r\n                  )}\r\n                </div>\r\n              </div>\r\n            )}\r\n\r\n            {/* Nút Áp dụng và Xóa tất cả */}\r\n            <div className=\"flex items-center justify-between mt-4\">\r\n              <Button\r\n                onClick={handleApply}\r\n                size=\"sm\"\r\n                className=\"flex-1 mr-1\"\r\n              >\r\n                Áp dụng\r\n              </Button>\r\n\r\n              <Button\r\n                onClick={handleClearAll}\r\n                variant=\"link\"\r\n                size=\"sm\"\r\n                className=\"flex-1 ml-1 text-destructive\"\r\n                disabled={tempSorting.length === 0}\r\n              >\r\n                Xóa tất cả\r\n              </Button>\r\n            </div>\r\n          </div>\r\n        </PopoverContent>\r\n      </Popover>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAKA;AAAA;AAAA;AAAA;AAAA;AAAA;AASA;AAEA;AAAA;AACA;AAEA;AAUA;AASA;AAOA;;;AAhDA;;;;;;;;;;;AAmEA,SAAS,aAAa,EACpB,EAAE,EACF,UAAU,EACV,SAAS,EACT,iBAAiB,EACjB,QAAQ,EACU;;IAClB,MAAM,EACJ,UAAU,EACV,SAAS,EACT,UAAU,EACV,SAAS,EACT,UAAU,EACX,GAAG,CAAA,GAAA,mRAAA,CAAA,cAAW,AAAD,EAAE;QAAE;IAAG;IAErB,MAAM,QAAQ;QACZ,WAAW,oQAAA,CAAA,MAAG,CAAC,SAAS,CAAC,QAAQ,CAAC;QAClC;IACF;IAEA,qBACE,sSAAC;QACC,KAAK;QACL,OAAO;QACP,WAAU;;0BAEV,sSAAC;gBAAI,WAAU;;kCACb,sSAAC;wBACE,GAAG,UAAU;wBACb,GAAG,SAAS;wBACb,WAAU;kCAEV,cAAA,sSAAC,6SAAA,CAAA,eAAY;4BAAC,WAAU;;;;;;;;;;;kCAE1B,sSAAC;wBAAK,WAAU;kCAAW;;;;;;;;;;;;0BAE7B,sSAAC;gBAAI,WAAU;;kCACb,sSAAC,8HAAA,CAAA,SAAM;wBACL,SAAQ;wBACR,MAAK;wBACL,WAAU;wBACV,SAAS,IAAM,kBAAkB,cAAc,QAAQ,SAAS;kCAE/D,cAAc,sBACb,sSAAC,mUAAA,CAAA,sBAAmB;4BAAC,WAAU;;;;;iDAE/B,sSAAC,+TAAA,CAAA,oBAAiB;4BAAC,WAAU;;;;;;;;;;;kCAGjC,sSAAC,8HAAA,CAAA,SAAM;wBACL,SAAQ;wBACR,MAAK;wBACL,WAAU;wBACV,SAAS;kCAET,cAAA,sSAAC,mRAAA,CAAA,IAAC;4BAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;AAKvB;GA5DS;;QAaH,mRAAA,CAAA,cAAW;;;KAbR;AA8DF,SAAS,aAAoB,EAClC,KAAK,EACL,SAAS,EACT,eAAe,EACf,mBAAmB,WAAW,EAC9B,gBAAgB,CAAC,EACQ;;IACzB,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,sQAAA,CAAA,WAAQ,AAAD,EAAE;IACjC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,sQAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,sQAAA,CAAA,WAAQ,AAAD,EAAgB,EAAE;IAE/D,oCAAoC;IACpC,MAAM,kBAAkB,CAAA,GAAA,sQAAA,CAAA,UAAO,AAAD;iDAAE;YAC9B,OAAO,MAAM,aAAa,GAAG,MAAM;yDAAC,CAAA,SAClC,OAAO,UAAU,MAAM,OAAO,YAAY;;QAE9C;gDAAG;QAAC;KAAM;IAEV,wCAAwC;IACxC,MAAM,kBAAkB,CAAA,GAAA,sQAAA,CAAA,UAAO,AAAD;iDAAE;YAC9B,IAAI,CAAC,YAAY,IAAI,IAAI;gBACvB,OAAO;YACT;YAEA,MAAM,QAAQ,YAAY,WAAW,GAAG,IAAI;YAC5C,OAAO,gBAAgB,MAAM;yDAAC,CAAA;oBAC5B,mCAAmC;oBACnC,IAAI,SAAS,OAAO,EAAE;oBAEtB,8BAA8B;oBAC9B,IAAI,OAAO,SAAS,CAAC,IAAI,EAAE,QAAQ;wBACjC,SAAS,OAAO,SAAS,CAAC,IAAI,CAAC,MAAM;oBACvC,OAEK,IAAI,OAAO,OAAO,SAAS,CAAC,MAAM,KAAK,UAAU;wBACpD,SAAS,OAAO,SAAS,CAAC,MAAM;oBAClC,OAEK,IAAI,OAAO,OAAO,SAAS,CAAC,MAAM,KAAK,YAAY;wBACtD,MAAM,gBAAgB,SAAS,aAAa,CAAC,CAAC,iBAAiB,EAAE,OAAO,EAAE,CAAC,OAAO,CAAC;wBACnF,IAAI,iBAAiB,cAAc,WAAW,EAAE;4BAC9C,SAAS,cAAc,WAAW;wBACpC;oBACF;oBAEA,OAAO,OAAO,QAAQ,GAAG,WAAW,GAAG,QAAQ,CAAC;gBAClD;;QACF;gDAAG;QAAC;QAAiB;KAAY;IAEjC,wEAAwE;IACxE,CAAA,GAAA,sQAAA,CAAA,YAAS,AAAD;kCAAE;YACR,IAAI,MAAM;gBACR,eAAe,MAAM,QAAQ,GAAG,OAAO;YACzC;QACF;iCAAG;QAAC;QAAM;KAAM;IAEhB,iEAAiE;IACjE,MAAM,gBAAgB,CAAA,GAAA,sQAAA,CAAA,UAAO,AAAD;+CAAE;YAC5B,OAAO,YAAY,GAAG;uDAAC,CAAA;oBACrB,MAAM,SAAS,MAAM,SAAS,CAAC,KAAK,EAAE;oBAEtC,mCAAmC;oBACnC,IAAI,aAAa,KAAK,EAAE;oBAExB,IAAI,QAAQ;wBACV,8BAA8B;wBAC9B,IAAI,OAAO,SAAS,CAAC,IAAI,EAAE,QAAQ;4BACjC,aAAa,OAAO,SAAS,CAAC,IAAI,CAAC,MAAM;wBAC3C,OAEK,IAAI,OAAO,OAAO,SAAS,CAAC,MAAM,KAAK,UAAU;4BACpD,aAAa,OAAO,SAAS,CAAC,MAAM;wBACtC,OAEK,IAAI,OAAO,OAAO,SAAS,CAAC,MAAM,KAAK,YAAY;4BACtD,MAAM,gBAAgB,SAAS,aAAa,CAAC,CAAC,iBAAiB,EAAE,KAAK,EAAE,CAAC,OAAO,CAAC;4BACjF,IAAI,iBAAiB,cAAc,WAAW,EAAE;gCAC9C,aAAa,cAAc,WAAW;4BACxC;wBACF;oBACF;oBAEA,OAAO;wBACL,IAAI,KAAK,EAAE;wBACX,MAAM;wBACN,WAAY,KAAK,IAAI,GAAG,SAAS;oBACnC;gBACF;;QACF;8CAAG;QAAC;QAAa;KAAM;IAEvB,kBAAkB;IAClB,MAAM,UAAU,CAAA,GAAA,2QAAA,CAAA,aAAU,AAAD,EACvB,CAAA,GAAA,2QAAA,CAAA,YAAS,AAAD,EAAE,2QAAA,CAAA,gBAAa,GACvB,CAAA,GAAA,2QAAA,CAAA,YAAS,AAAD,EAAE,2QAAA,CAAA,iBAAc,EAAE;QACxB,kBAAkB,mRAAA,CAAA,8BAA2B;IAC/C;IAGF,6BAA6B;IAC7B,MAAM,gBAAgB,CAAC;QACrB,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,GAAG;QAEzB,IAAI,QAAQ,OAAO,EAAE,KAAK,KAAK,EAAE,EAAE;YACjC,MAAM,WAAW,cAAc,SAAS,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK,OAAO,EAAE;YACtE,MAAM,WAAW,cAAc,SAAS,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK,KAAK,EAAE;YAEpE,MAAM,mBAAmB,CAAA,GAAA,mRAAA,CAAA,YAAS,AAAD,EAAE,eAAe,UAAU;YAE5D,uBAAuB;YACvB,MAAM,aAAa,iBAAiB,GAAG,CAAC,CAAA,MAAO,CAAC;oBAC9C,IAAI,IAAI,EAAE;oBACV,MAAM,IAAI,SAAS,KAAK;gBAC1B,CAAC;YAED,eAAe;QACjB;IACF;IAEA,kCAAkC;IAClC,MAAM,gBAAgB,CAAC;QACrB,wCAAwC;QACxC,MAAM,eAAe,YAAY,IAAI,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK;QAE1D,IAAI,cAAc;YAChB,oCAAoC;YACpC,MAAM,aAAa,YAAY,GAAG,CAAC,CAAA;gBACjC,IAAI,KAAK,EAAE,KAAK,UAAU;oBACxB,OAAO;wBAAE,GAAG,IAAI;wBAAE,MAAM,CAAC,KAAK,IAAI;oBAAC;gBACrC;gBACA,OAAO;YACT;YAEA,eAAe;QACjB,OAAO,IAAI,YAAY,MAAM,GAAG,eAAe;YAC7C,6CAA6C;YAC7C,MAAM,aAAa;mBACd;gBACH;oBAAE,IAAI;oBAAU,MAAM;gBAAM;aAC7B;YAED,eAAe;QACjB;QAEA,oCAAoC;QACpC,eAAe;IACjB;IAEA,+BAA+B;IAC/B,MAAM,wBAAwB,CAAC,UAAkB;QAC/C,MAAM,aAAa,YAAY,GAAG,CAAC,CAAA;YACjC,IAAI,KAAK,EAAE,KAAK,UAAU;gBACxB,OAAO;oBAAE,GAAG,IAAI;oBAAE,MAAM,cAAc;gBAAO;YAC/C;YACA,OAAO;QACT;QAEA,eAAe;IACjB;IAEA,6BAA6B;IAC7B,MAAM,mBAAmB,CAAC;QACxB,MAAM,aAAa,YAAY,MAAM,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK;QAC1D,eAAe;IACjB;IAEA,oCAAoC;IACpC,MAAM,iBAAiB;QACrB,eAAe,EAAE;IACnB;IAEA,6BAA6B;IAC7B,MAAM,cAAc;QAClB,MAAM,UAAU,CAAC;QACjB,QAAQ;IACV;IAEA,qBAAqB;IACrB,MAAM,eAAe;QACnB,eAAe,MAAM,QAAQ,GAAG,OAAO;QACvC,QAAQ;IACV;IAEA,8BAA8B;IAC9B,MAAM,cAAc;QAClB,MAAM,iBAAiB,MAAM,QAAQ,GAAG,OAAO;QAE/C,IAAI,eAAe,MAAM,KAAK,GAAG;YAC/B,qBAAO,sSAAC,+SAAA,CAAA,cAAW;gBAAC,WAAU;;;;;;QAChC;QAEA,IAAI,eAAe,MAAM,KAAK,GAAG;YAC/B,OAAO,cAAc,CAAC,EAAE,CAAC,IAAI,iBAC3B,sSAAC,+TAAA,CAAA,oBAAiB;gBAAC,WAAU;;;;;qCAC7B,sSAAC,mUAAA,CAAA,sBAAmB;gBAAC,WAAU;;;;;;QACnC;QAEA,mDAAmD;QACnD,qBACE,sSAAC;YAAI,WAAU;;8BACb,sSAAC,+SAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;8BACvB,sSAAC,6HAAA,CAAA,QAAK;oBACJ,WAAU;oBACV,SAAQ;8BAEP,eAAe,MAAM;;;;;;;;;;;;IAI9B;IAEA,qBACE,sSAAC;QAAI,WAAW;kBACd,cAAA,sSAAC,+HAAA,CAAA,UAAO;YAAC,MAAM;YAAM,cAAc;;8BACjC,sSAAC,+HAAA,CAAA,iBAAc;oBAAC,OAAO;8BACrB,cAAA,sSAAC,8HAAA,CAAA,SAAM;wBACL,SAAQ;wBACR,MAAK;wBACL,WAAW,CAAA,GAAA,+HAAA,CAAA,KAAE,AAAD,EAAE,YAAY,iBAAiB,MAAM,QAAQ,GAAG,OAAO,CAAC,MAAM,GAAG,KAAK;kCAEjF;;;;;;;;;;;8BAGL,sSAAC,+HAAA,CAAA,iBAAc;oBAAC,OAAM;oBAAM,WAAW;oBAAkB,iBAAiB;oBAAc,mBAAmB,CAAC,IAAM,EAAE,cAAc;8BAChI,cAAA,sSAAC;wBAAI,WAAU;;0CACb,sSAAC;gCAAG,WAAU;0CAA2B;;;;;;4BAGxC,cAAc,MAAM,GAAG,kBACtB,sSAAC;gCAAI,WAAU;0CACb,cAAA,sSAAC,2QAAA,CAAA,aAAU;oCACT,SAAS;oCACT,oBAAoB,2QAAA,CAAA,gBAAa;oCACjC,WAAW;8CAEX,cAAA,sSAAC,mRAAA,CAAA,kBAAe;wCACd,OAAO,cAAc,GAAG,CAAC,CAAA,MAAO,IAAI,EAAE;wCACtC,UAAU,mRAAA,CAAA,8BAA2B;kDAEpC,cAAc,GAAG,CAAC,CAAC,uBAClB,sSAAC;gDAEC,IAAI,OAAO,EAAE;gDACb,YAAY,OAAO,IAAI;gDACvB,WAAW,OAAO,SAAS;gDAC3B,mBAAmB,CAAC,YAClB,sBAAsB,OAAO,EAAE,EAAE;gDAEnC,UAAU,IAAM,iBAAiB,OAAO,EAAE;+CAPrC,OAAO,EAAE;;;;;;;;;;;;;;;;;;;qDAcxB,sSAAC;gCAAI,WAAU;0CAAsD;;;;;;4BAMtE,YAAY,MAAM,GAAG,+BACpB,sSAAC;gCAAI,WAAU;;kDACb,sSAAC;wCAAI,WAAU;kDAAiD;;;;;;kDAGhE,sSAAC;wCAAI,WAAU;;0DACb,sSAAC,6RAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;0DAClB,sSAAC,6HAAA,CAAA,QAAK;gDACJ,aAAY;gDACZ,OAAO;gDACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;gDAC9C,WAAU;;;;;;;;;;;;kDAKd,sSAAC;wCAAI,WAAU;kDACZ,gBAAgB,MAAM,KAAK,kBAC1B,sSAAC;4CAAI,WAAU;sDAAgD;;;;;iEAI/D,sSAAC;4CAAI,WAAU;sDACZ,gBAAgB,GAAG,CAAC,CAAA;gDACnB,MAAM,aAAa,YAAY,IAAI,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK,OAAO,EAAE;gDACjE,qBACE,sSAAC;oDAEC,SAAS,IAAM,cAAc,OAAO,EAAE;oDACtC,UAAU;oDACV,WAAW,CAAA,GAAA,+HAAA,CAAA,KAAE,AAAD,EACV,qGACA,cAAc;8DAGhB,cAAA,sSAAC;kEACE,CAAC;4DACA,8BAA8B;4DAC9B,IAAI,OAAO,SAAS,CAAC,IAAI,EAAE,QAAQ;gEACjC,OAAO,OAAO,SAAS,CAAC,IAAI,CAAC,MAAM;4DACrC;4DAEA,0CAA0C;4DAC1C,IAAI,OAAO,OAAO,SAAS,CAAC,MAAM,KAAK,UAAU;gEAC/C,OAAO,OAAO,SAAS,CAAC,MAAM;4DAChC;4DAEA,qCAAqC;4DACrC,MAAM,gBAAgB,SAAS,aAAa,CAAC,CAAC,iBAAiB,EAAE,OAAO,EAAE,CAAC,OAAO,CAAC;4DACnF,IAAI,iBAAiB,cAAc,WAAW,EAAE;gEAC9C,OAAO,cAAc,WAAW;4DAClC;4DAEA,uBAAuB;4DACvB,OAAO,OAAO,EAAE;wDAClB,CAAC;;;;;;mDA5BE,OAAO,EAAE;;;;;4CAgCpB;;;;;;;;;;;;;;;;;0CAQV,sSAAC;gCAAI,WAAU;;kDACb,sSAAC,8HAAA,CAAA,SAAM;wCACL,SAAS;wCACT,MAAK;wCACL,WAAU;kDACX;;;;;;kDAID,sSAAC,8HAAA,CAAA,SAAM;wCACL,SAAS;wCACT,SAAQ;wCACR,MAAK;wCACL,WAAU;wCACV,UAAU,YAAY,MAAM,KAAK;kDAClC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASf;IA/VgB;;QA2FE,2QAAA,CAAA,aAAU;;;MA3FZ", "debugId": null}}, {"offset": {"line": 3527, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/bloomgoo/gold-exchange-platform/frontend/components/common/data-table/table-toolbar.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport { ColumnVisibilitySelector } from \"@/components/common/selector/column-visibility-selector\"\r\nimport { SortSelector } from \"@/components/common/selector/sort-selector\"\r\nimport { Button } from \"@/components/ui/button\"\r\nimport { Input } from \"@/components/ui/input\"\r\nimport { cn } from \"@/lib/utils\"\r\nimport { Table } from \"@tanstack/react-table\"\r\nimport {\r\n    RefreshCcw,\r\n    Search\r\n} from \"lucide-react\"\r\n\r\ninterface TableToolbarProps<TData> {\r\n    table: Table<TData>\r\n    globalFilter: string\r\n    setGlobalFilter: (value: string) => void\r\n    onRefresh: () => void\r\n    isRefreshing?: boolean\r\n    searchPlaceholder?: string\r\n\r\n    onDeleteSelected?: () => void\r\n    isShowSelectedRows?: boolean\r\n    onShowSelectedRows?: () => void\r\n    // Slot tùy chỉnh để thêm nội dung vào bên trái của thanh tìm kiếm\r\n    beforeSearchSlot?: React.ReactNode\r\n    // Slot tùy chỉnh để thêm nội dung vào bên phải của thanh tìm kiếm\r\n    afterSearchSlot?: React.ReactNode\r\n    // Slot tùy chỉnh để thêm nội dung vào bên trái của các nút hành động\r\n    beforeActionsSlot?: React.ReactNode\r\n    // Slot tùy chỉnh để thêm nội dung vào bên phải của các nút hành động\r\n    afterActionsSlot?: React.ReactNode\r\n    // Tùy chỉnh class cho thanh công cụ\r\n    className?: string\r\n}\r\n\r\nexport function TableToolbar<TData>({\r\n    table,\r\n    globalFilter,\r\n    setGlobalFilter,\r\n    onRefresh,\r\n    isRefreshing = false,\r\n    searchPlaceholder = \"Tìm kiếm...\",\r\n\r\n    onDeleteSelected,\r\n    isShowSelectedRows,\r\n    onShowSelectedRows,\r\n    beforeSearchSlot,\r\n    afterSearchSlot,\r\n    beforeActionsSlot,\r\n    afterActionsSlot,\r\n    className,\r\n}: TableToolbarProps<TData>) {\r\n    const selectedRows = table.getSelectedRowModel().rows\r\n\r\n    return (\r\n        <div className={cn(\"flex items-center justify-between gap-2 px-4 py-2 border-b\", className)}>\r\n            {/* Left Side */}\r\n            <div className=\"flex items-center space-x-4 flex-1\">\r\n                {/* Slot trước thanh tìm kiếm */}\r\n                {beforeSearchSlot}\r\n\r\n                <div className=\"relative w-64\">\r\n                    <Search\r\n                        className=\"h-4 w-4 absolute left-2 top-1/2 transform -translate-y-1/2 text-muted-foreground\"\r\n                    />\r\n                    <Input\r\n                        placeholder={searchPlaceholder}\r\n                        className=\"pl-8 h-9\"\r\n                        value={globalFilter}\r\n                        onChange={(e) => setGlobalFilter(e.target.value)}\r\n                    />\r\n                </div>\r\n\r\n                {/* Slot sau thanh tìm kiếm */}\r\n                {afterSearchSlot}\r\n            </div>\r\n\r\n            {/* Right Side */}\r\n            <div className=\"flex items-center space-x-2\">\r\n                {/* Slot trước các nút hành động */}\r\n                {beforeActionsSlot}\r\n                {onDeleteSelected && (<></>)}\r\n\r\n                {/* <FilterSelector table={table} /> */}\r\n\r\n                <SortSelector table={table} />\r\n\r\n                <ColumnVisibilitySelector table={table} />\r\n\r\n                <Button\r\n                    variant=\"ghost\"\r\n                    size=\"icon\"\r\n                    onClick={onRefresh}\r\n                    disabled={isRefreshing}\r\n                >\r\n                    <RefreshCcw className={cn(\"h-4 w-4\", isRefreshing && \"animate-spin\")} />\r\n                </Button>\r\n\r\n                {/* Slot sau các nút hành động */}\r\n                {afterActionsSlot}\r\n            </div>\r\n        </div>\r\n    )\r\n}"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AAAA;AAEA;AAAA;AARA;;;;;;;;AAoCO,SAAS,aAAoB,EAChC,KAAK,EACL,YAAY,EACZ,eAAe,EACf,SAAS,EACT,eAAe,KAAK,EACpB,oBAAoB,aAAa,EAEjC,gBAAgB,EAChB,kBAAkB,EAClB,kBAAkB,EAClB,gBAAgB,EAChB,eAAe,EACf,iBAAiB,EACjB,gBAAgB,EAChB,SAAS,EACc;IACvB,MAAM,eAAe,MAAM,mBAAmB,GAAG,IAAI;IAErD,qBACI,sSAAC;QAAI,WAAW,CAAA,GAAA,+HAAA,CAAA,KAAE,AAAD,EAAE,8DAA8D;;0BAE7E,sSAAC;gBAAI,WAAU;;oBAEV;kCAED,sSAAC;wBAAI,WAAU;;0CACX,sSAAC,6RAAA,CAAA,SAAM;gCACH,WAAU;;;;;;0CAEd,sSAAC,6HAAA,CAAA,QAAK;gCACF,aAAa;gCACb,WAAU;gCACV,OAAO;gCACP,UAAU,CAAC,IAAM,gBAAgB,EAAE,MAAM,CAAC,KAAK;;;;;;;;;;;;oBAKtD;;;;;;;0BAIL,sSAAC;gBAAI,WAAU;;oBAEV;oBACA,kCAAqB;kCAItB,sSAAC,wJAAA,CAAA,eAAY;wBAAC,OAAO;;;;;;kCAErB,sSAAC,wKAAA,CAAA,2BAAwB;wBAAC,OAAO;;;;;;kCAEjC,sSAAC,8HAAA,CAAA,SAAM;wBACH,SAAQ;wBACR,MAAK;wBACL,SAAS;wBACT,UAAU;kCAEV,cAAA,sSAAC,ySAAA,CAAA,aAAU;4BAAC,WAAW,CAAA,GAAA,+HAAA,CAAA,KAAE,AAAD,EAAE,WAAW,gBAAgB;;;;;;;;;;;oBAIxD;;;;;;;;;;;;;AAIjB;KApEgB", "debugId": null}}, {"offset": {"line": 3651, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/bloomgoo/gold-exchange-platform/frontend/components/common/selector/page-selector.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { Button } from '@/components/ui/button';\r\nimport {\r\n   Command,\r\n   CommandEmpty,\r\n   CommandGroup,\r\n   CommandInput,\r\n   CommandItem,\r\n   CommandList,\r\n} from '@/components/ui/command';\r\nimport { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';\r\nimport { CheckIcon, ChevronLeft, ChevronRight, ChevronsLeft, ChevronsRight } from 'lucide-react';\r\nimport { useId, useState, useEffect } from 'react';\r\nimport { Table as TableType } from \"@tanstack/react-table\";\r\n\r\n// Interface for table-based pagination\r\ninterface TablePaginationProps {\r\n   table: TableType<any>;\r\n   type: 'table';\r\n}\r\n\r\n// Interface for custom pagination\r\ninterface CustomPaginationProps {\r\n   type: 'custom';\r\n   currentPage: number;\r\n   totalPages: number;\r\n   onPageChange: (page: number) => void;\r\n}\r\n\r\n// Combined props type\r\ntype PageSelectorProps = {\r\n   maxPagesToShow?: number;\r\n   showFirstLastButtons?: boolean;\r\n   showPrevNextButtons?: boolean;\r\n   className?: string;\r\n} & (TablePaginationProps | CustomPaginationProps);\r\n\r\nexport function PageSelector(props: PageSelectorProps) {\r\n   const id = useId();\r\n   const [open, setOpen] = useState<boolean>(false);\r\n   \r\n   // Determine current page and total pages based on props type\r\n   const currentPage = props.type === 'table' \r\n      ? props.table.getState().pagination.pageIndex + 1 \r\n      : props.currentPage;\r\n   \r\n   const totalPages = props.type === 'table'\r\n      ? props.table.getPageCount()\r\n      : props.totalPages;\r\n   \r\n   // Default to showing 5 pages in the selector\r\n   const maxPagesToShow = props.maxPagesToShow || 5;\r\n   \r\n   // Generate array of page numbers to display\r\n   const getPageNumbers = () => {\r\n      if (totalPages <= maxPagesToShow) {\r\n         // If total pages is less than max to show, display all pages\r\n         return Array.from({ length: totalPages }, (_, i) => i + 1);\r\n      }\r\n      \r\n      // Calculate start and end page numbers\r\n      let startPage = Math.max(1, currentPage - Math.floor(maxPagesToShow / 2));\r\n      let endPage = startPage + maxPagesToShow - 1;\r\n      \r\n      // Adjust if end page exceeds total pages\r\n      if (endPage > totalPages) {\r\n         endPage = totalPages;\r\n         startPage = Math.max(1, endPage - maxPagesToShow + 1);\r\n      }\r\n      \r\n      return Array.from({ length: endPage - startPage + 1 }, (_, i) => startPage + i);\r\n   };\r\n   \r\n   const pageNumbers = getPageNumbers();\r\n   \r\n   // Handle page change\r\n   const handlePageChange = (page: number) => {\r\n      if (props.type === 'table') {\r\n         props.table.setPageIndex(page - 1);\r\n      } else {\r\n         props.onPageChange(page);\r\n      }\r\n      setOpen(false);\r\n   };\r\n   \r\n   // Handle navigation to first page\r\n   const goToFirstPage = () => {\r\n      if (props.type === 'table') {\r\n         props.table.setPageIndex(0);\r\n      } else {\r\n         props.onPageChange(1);\r\n      }\r\n   };\r\n   \r\n   // Handle navigation to previous page\r\n   const goToPreviousPage = () => {\r\n      if (props.type === 'table') {\r\n         props.table.previousPage();\r\n      } else {\r\n         props.onPageChange(Math.max(1, currentPage - 1));\r\n      }\r\n   };\r\n   \r\n   // Handle navigation to next page\r\n   const goToNextPage = () => {\r\n      if (props.type === 'table') {\r\n         props.table.nextPage();\r\n      } else {\r\n         props.onPageChange(Math.min(totalPages, currentPage + 1));\r\n      }\r\n   };\r\n   \r\n   // Handle navigation to last page\r\n   const goToLastPage = () => {\r\n      if (props.type === 'table') {\r\n         props.table.setPageIndex(totalPages - 1);\r\n      } else {\r\n         props.onPageChange(totalPages);\r\n      }\r\n   };\r\n   \r\n   // Check if can go to previous or next page\r\n   const canPreviousPage = props.type === 'table' \r\n      ? props.table.getCanPreviousPage()\r\n      : currentPage > 1;\r\n      \r\n   const canNextPage = props.type === 'table'\r\n      ? props.table.getCanNextPage()\r\n      : currentPage < totalPages;\r\n\r\n   return (\r\n      <div className=\"flex items-center space-x-2\">\r\n         {/* First Page Button */}\r\n         {props.showFirstLastButtons && (\r\n            <Button\r\n               variant=\"outline\"\r\n               className=\"hidden h-8 w-8 p-0 lg:flex\"\r\n               onClick={goToFirstPage}\r\n               disabled={!canPreviousPage}\r\n            >\r\n               <ChevronsLeft className=\"size-4\" />\r\n            </Button>\r\n         )}\r\n         \r\n         {/* Previous Page Button */}\r\n         {props.showPrevNextButtons && (\r\n            <Button\r\n               variant=\"outline\"\r\n               className=\"h-8 w-8 p-0\"\r\n               onClick={goToPreviousPage}\r\n               disabled={!canPreviousPage}\r\n            >\r\n               <ChevronLeft className=\"size-4\" />\r\n            </Button>\r\n         )}\r\n         \r\n         {/* Page Selector */}\r\n         <Popover open={open} onOpenChange={setOpen}>\r\n            <PopoverTrigger asChild>\r\n               <Button\r\n                  id={id}\r\n                  className=\"flex items-center justify-center h-8 px-3\"\r\n                  size=\"sm\"\r\n                  variant=\"outline\"\r\n                  role=\"combobox\"\r\n                  aria-expanded={open}\r\n               >\r\n                  <span className=\"text-sm font-medium\">\r\n                     {currentPage} / {totalPages}\r\n                  </span>\r\n               </Button>\r\n            </PopoverTrigger>\r\n            <PopoverContent className=\"border-input w-48 p-0\" align=\"center\" side=\"top\">\r\n               <Command>\r\n                  <CommandInput placeholder=\"Trang...\" />\r\n                  <CommandList>\r\n                     <CommandEmpty>Không tìm thấy trang.</CommandEmpty>\r\n                     <CommandGroup>\r\n                        {pageNumbers.map((page) => (\r\n                           <CommandItem\r\n                              key={page}\r\n                              value={page.toString()}\r\n                              onSelect={() => handlePageChange(page)}\r\n                              className=\"flex items-center justify-between\"\r\n                           >\r\n                              <div className=\"flex items-center gap-2\">\r\n                                 <span className=\"text-xs\">Trang {page}</span>\r\n                              </div>\r\n                              {currentPage === page && <CheckIcon size={14} className=\"ml-auto\" />}\r\n                           </CommandItem>\r\n                        ))}\r\n                     </CommandGroup>\r\n                  </CommandList>\r\n               </Command>\r\n            </PopoverContent>\r\n         </Popover>\r\n         \r\n         {/* Next Page Button */}\r\n         {props.showPrevNextButtons && (\r\n            <Button\r\n               variant=\"outline\"\r\n               className=\"h-8 w-8 p-0\"\r\n               onClick={goToNextPage}\r\n               disabled={!canNextPage}\r\n            >\r\n               <ChevronRight className=\"size-4\" />\r\n            </Button>\r\n         )}\r\n         \r\n         {/* Last Page Button */}\r\n         {props.showFirstLastButtons && (\r\n            <Button\r\n               variant=\"outline\"\r\n               className=\"hidden h-8 w-8 p-0 lg:flex\"\r\n               onClick={goToLastPage}\r\n               disabled={!canNextPage}\r\n            >\r\n               <ChevronsRight className=\"size-4\" />\r\n            </Button>\r\n         )}\r\n      </div>\r\n   );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAQA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;;;AAbA;;;;;;AAsCO,SAAS,aAAa,KAAwB;;IAClD,MAAM,KAAK,CAAA,GAAA,sQAAA,CAAA,QAAK,AAAD;IACf,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,sQAAA,CAAA,WAAQ,AAAD,EAAW;IAE1C,6DAA6D;IAC7D,MAAM,cAAc,MAAM,IAAI,KAAK,UAC9B,MAAM,KAAK,CAAC,QAAQ,GAAG,UAAU,CAAC,SAAS,GAAG,IAC9C,MAAM,WAAW;IAEtB,MAAM,aAAa,MAAM,IAAI,KAAK,UAC7B,MAAM,KAAK,CAAC,YAAY,KACxB,MAAM,UAAU;IAErB,6CAA6C;IAC7C,MAAM,iBAAiB,MAAM,cAAc,IAAI;IAE/C,4CAA4C;IAC5C,MAAM,iBAAiB;QACpB,IAAI,cAAc,gBAAgB;YAC/B,6DAA6D;YAC7D,OAAO,MAAM,IAAI,CAAC;gBAAE,QAAQ;YAAW,GAAG,CAAC,GAAG,IAAM,IAAI;QAC3D;QAEA,uCAAuC;QACvC,IAAI,YAAY,KAAK,GAAG,CAAC,GAAG,cAAc,KAAK,KAAK,CAAC,iBAAiB;QACtE,IAAI,UAAU,YAAY,iBAAiB;QAE3C,yCAAyC;QACzC,IAAI,UAAU,YAAY;YACvB,UAAU;YACV,YAAY,KAAK,GAAG,CAAC,GAAG,UAAU,iBAAiB;QACtD;QAEA,OAAO,MAAM,IAAI,CAAC;YAAE,QAAQ,UAAU,YAAY;QAAE,GAAG,CAAC,GAAG,IAAM,YAAY;IAChF;IAEA,MAAM,cAAc;IAEpB,qBAAqB;IACrB,MAAM,mBAAmB,CAAC;QACvB,IAAI,MAAM,IAAI,KAAK,SAAS;YACzB,MAAM,KAAK,CAAC,YAAY,CAAC,OAAO;QACnC,OAAO;YACJ,MAAM,YAAY,CAAC;QACtB;QACA,QAAQ;IACX;IAEA,kCAAkC;IAClC,MAAM,gBAAgB;QACnB,IAAI,MAAM,IAAI,KAAK,SAAS;YACzB,MAAM,KAAK,CAAC,YAAY,CAAC;QAC5B,OAAO;YACJ,MAAM,YAAY,CAAC;QACtB;IACH;IAEA,qCAAqC;IACrC,MAAM,mBAAmB;QACtB,IAAI,MAAM,IAAI,KAAK,SAAS;YACzB,MAAM,KAAK,CAAC,YAAY;QAC3B,OAAO;YACJ,MAAM,YAAY,CAAC,KAAK,GAAG,CAAC,GAAG,cAAc;QAChD;IACH;IAEA,iCAAiC;IACjC,MAAM,eAAe;QAClB,IAAI,MAAM,IAAI,KAAK,SAAS;YACzB,MAAM,KAAK,CAAC,QAAQ;QACvB,OAAO;YACJ,MAAM,YAAY,CAAC,KAAK,GAAG,CAAC,YAAY,cAAc;QACzD;IACH;IAEA,iCAAiC;IACjC,MAAM,eAAe;QAClB,IAAI,MAAM,IAAI,KAAK,SAAS;YACzB,MAAM,KAAK,CAAC,YAAY,CAAC,aAAa;QACzC,OAAO;YACJ,MAAM,YAAY,CAAC;QACtB;IACH;IAEA,2CAA2C;IAC3C,MAAM,kBAAkB,MAAM,IAAI,KAAK,UAClC,MAAM,KAAK,CAAC,kBAAkB,KAC9B,cAAc;IAEnB,MAAM,cAAc,MAAM,IAAI,KAAK,UAC9B,MAAM,KAAK,CAAC,cAAc,KAC1B,cAAc;IAEnB,qBACG,sSAAC;QAAI,WAAU;;YAEX,MAAM,oBAAoB,kBACxB,sSAAC,8HAAA,CAAA,SAAM;gBACJ,SAAQ;gBACR,WAAU;gBACV,SAAS;gBACT,UAAU,CAAC;0BAEX,cAAA,sSAAC,6SAAA,CAAA,eAAY;oBAAC,WAAU;;;;;;;;;;;YAK7B,MAAM,mBAAmB,kBACvB,sSAAC,8HAAA,CAAA,SAAM;gBACJ,SAAQ;gBACR,WAAU;gBACV,SAAS;gBACT,UAAU,CAAC;0BAEX,cAAA,sSAAC,2SAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;;;;;;0BAK7B,sSAAC,+HAAA,CAAA,UAAO;gBAAC,MAAM;gBAAM,cAAc;;kCAChC,sSAAC,+HAAA,CAAA,iBAAc;wBAAC,OAAO;kCACpB,cAAA,sSAAC,8HAAA,CAAA,SAAM;4BACJ,IAAI;4BACJ,WAAU;4BACV,MAAK;4BACL,SAAQ;4BACR,MAAK;4BACL,iBAAe;sCAEf,cAAA,sSAAC;gCAAK,WAAU;;oCACZ;oCAAY;oCAAI;;;;;;;;;;;;;;;;;kCAI1B,sSAAC,+HAAA,CAAA,iBAAc;wBAAC,WAAU;wBAAwB,OAAM;wBAAS,MAAK;kCACnE,cAAA,sSAAC,+HAAA,CAAA,UAAO;;8CACL,sSAAC,+HAAA,CAAA,eAAY;oCAAC,aAAY;;;;;;8CAC1B,sSAAC,+HAAA,CAAA,cAAW;;sDACT,sSAAC,+HAAA,CAAA,eAAY;sDAAC;;;;;;sDACd,sSAAC,+HAAA,CAAA,eAAY;sDACT,YAAY,GAAG,CAAC,CAAC,qBACf,sSAAC,+HAAA,CAAA,cAAW;oDAET,OAAO,KAAK,QAAQ;oDACpB,UAAU,IAAM,iBAAiB;oDACjC,WAAU;;sEAEV,sSAAC;4DAAI,WAAU;sEACZ,cAAA,sSAAC;gEAAK,WAAU;;oEAAU;oEAAO;;;;;;;;;;;;wDAEnC,gBAAgB,sBAAQ,sSAAC,+RAAA,CAAA,YAAS;4DAAC,MAAM;4DAAI,WAAU;;;;;;;mDARnD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAkBzB,MAAM,mBAAmB,kBACvB,sSAAC,8HAAA,CAAA,SAAM;gBACJ,SAAQ;gBACR,WAAU;gBACV,SAAS;gBACT,UAAU,CAAC;0BAEX,cAAA,sSAAC,6SAAA,CAAA,eAAY;oBAAC,WAAU;;;;;;;;;;;YAK7B,MAAM,oBAAoB,kBACxB,sSAAC,8HAAA,CAAA,SAAM;gBACJ,SAAQ;gBACR,WAAU;gBACV,SAAS;gBACT,UAAU,CAAC;0BAEX,cAAA,sSAAC,+SAAA,CAAA,gBAAa;oBAAC,WAAU;;;;;;;;;;;;;;;;;AAKxC;GAzLgB;;QACF,sQAAA,CAAA,QAAK;;;KADH", "debugId": null}}, {"offset": {"line": 3964, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/bloomgoo/gold-exchange-platform/frontend/components/common/selector/page-size-selector.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { Button } from '@/components/ui/button';\r\nimport {\r\n   Command,\r\n   CommandEmpty,\r\n   CommandGroup,\r\n   CommandInput,\r\n   CommandItem,\r\n   CommandList,\r\n} from '@/components/ui/command';\r\nimport { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';\r\nimport { CheckIcon } from 'lucide-react';\r\nimport { useId, useState } from 'react';\r\nimport { Table as TableType } from \"@tanstack/react-table\";\r\n\r\n// Interface for table-based pagination\r\ninterface TablePageSizeProps {\r\n   table: TableType<any>;\r\n   type: 'table';\r\n}\r\n\r\n// Interface for custom pagination\r\ninterface CustomPageSizeProps {\r\n   type: 'custom';\r\n   pageSize: number;\r\n   onPageSizeChange: (pageSize: number) => void;\r\n}\r\n\r\n// Combined props type\r\ntype PageSizeSelectorProps = {\r\n   pageSizeOptions?: number[];\r\n   className?: string;\r\n   label?: string;\r\n   triggerClassName?: string;\r\n} & (TablePageSizeProps | CustomPageSizeProps);\r\n\r\nexport function PageSizeSelector(props: PageSizeSelectorProps) {\r\n   const id = useId();\r\n   const [open, setOpen] = useState<boolean>(false);\r\n   \r\n   // Default page size options if not provided\r\n   const pageSizeOptions = props.pageSizeOptions || [10, 20, 30, 50, 100];\r\n   \r\n   // Get current page size based on props type\r\n   const currentPageSize = props.type === 'table' \r\n      ? props.table.getState().pagination.pageSize \r\n      : props.pageSize;\r\n   \r\n   // Handle page size change\r\n   const handlePageSizeChange = (pageSize: number) => {\r\n      if (props.type === 'table') {\r\n         props.table.setPageSize(pageSize);\r\n      } else {\r\n         props.onPageSizeChange(pageSize);\r\n      }\r\n      setOpen(false);\r\n   };\r\n\r\n   return (\r\n      <div className=\"flex items-center space-x-2\">\r\n         {props.label && (\r\n            <span className=\"text-sm text-muted-foreground\">{props.label}</span>\r\n         )}\r\n         \r\n         <Popover open={open} onOpenChange={setOpen}>\r\n            <PopoverTrigger asChild>\r\n               <Button\r\n                  id={id}\r\n                  className={`flex items-center justify-center h-8 px-3 ${props.triggerClassName || ''}`}\r\n                  size=\"sm\"\r\n                  variant=\"outline\"\r\n                  role=\"combobox\"\r\n                  aria-expanded={open}\r\n               >\r\n                  <span className=\"text-sm font-medium\">\r\n                     {currentPageSize} bản ghi\r\n                  </span>\r\n               </Button>\r\n            </PopoverTrigger>\r\n            <PopoverContent className=\"border-input w-48 p-0\" align=\"start\" side=\"top\">\r\n               <Command>\r\n                  <CommandInput placeholder=\"Số bản ghi...\" />\r\n                  <CommandList>\r\n                     <CommandEmpty>Không tìm thấy tùy chọn.</CommandEmpty>\r\n                     <CommandGroup>\r\n                        {pageSizeOptions.map((size) => (\r\n                           <CommandItem\r\n                              key={size}\r\n                              value={size.toString()}\r\n                              onSelect={() => handlePageSizeChange(size)}\r\n                              className=\"flex items-center justify-between\"\r\n                           >\r\n                              <div className=\"flex items-center gap-2\">\r\n                                 <span className=\"text-xs\">{size} bản ghi</span>\r\n                              </div>\r\n                              {currentPageSize === size && <CheckIcon size={14} className=\"ml-auto\" />}\r\n                           </CommandItem>\r\n                        ))}\r\n                     </CommandGroup>\r\n                  </CommandList>\r\n               </Command>\r\n            </PopoverContent>\r\n         </Popover>\r\n      </div>\r\n   );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAQA;AACA;AACA;;;AAbA;;;;;;AAqCO,SAAS,iBAAiB,KAA4B;;IAC1D,MAAM,KAAK,CAAA,GAAA,sQAAA,CAAA,QAAK,AAAD;IACf,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,sQAAA,CAAA,WAAQ,AAAD,EAAW;IAE1C,4CAA4C;IAC5C,MAAM,kBAAkB,MAAM,eAAe,IAAI;QAAC;QAAI;QAAI;QAAI;QAAI;KAAI;IAEtE,4CAA4C;IAC5C,MAAM,kBAAkB,MAAM,IAAI,KAAK,UAClC,MAAM,KAAK,CAAC,QAAQ,GAAG,UAAU,CAAC,QAAQ,GAC1C,MAAM,QAAQ;IAEnB,0BAA0B;IAC1B,MAAM,uBAAuB,CAAC;QAC3B,IAAI,MAAM,IAAI,KAAK,SAAS;YACzB,MAAM,KAAK,CAAC,WAAW,CAAC;QAC3B,OAAO;YACJ,MAAM,gBAAgB,CAAC;QAC1B;QACA,QAAQ;IACX;IAEA,qBACG,sSAAC;QAAI,WAAU;;YACX,MAAM,KAAK,kBACT,sSAAC;gBAAK,WAAU;0BAAiC,MAAM,KAAK;;;;;;0BAG/D,sSAAC,+HAAA,CAAA,UAAO;gBAAC,MAAM;gBAAM,cAAc;;kCAChC,sSAAC,+HAAA,CAAA,iBAAc;wBAAC,OAAO;kCACpB,cAAA,sSAAC,8HAAA,CAAA,SAAM;4BACJ,IAAI;4BACJ,WAAW,CAAC,0CAA0C,EAAE,MAAM,gBAAgB,IAAI,IAAI;4BACtF,MAAK;4BACL,SAAQ;4BACR,MAAK;4BACL,iBAAe;sCAEf,cAAA,sSAAC;gCAAK,WAAU;;oCACZ;oCAAgB;;;;;;;;;;;;;;;;;kCAI1B,sSAAC,+HAAA,CAAA,iBAAc;wBAAC,WAAU;wBAAwB,OAAM;wBAAQ,MAAK;kCAClE,cAAA,sSAAC,+HAAA,CAAA,UAAO;;8CACL,sSAAC,+HAAA,CAAA,eAAY;oCAAC,aAAY;;;;;;8CAC1B,sSAAC,+HAAA,CAAA,cAAW;;sDACT,sSAAC,+HAAA,CAAA,eAAY;sDAAC;;;;;;sDACd,sSAAC,+HAAA,CAAA,eAAY;sDACT,gBAAgB,GAAG,CAAC,CAAC,qBACnB,sSAAC,+HAAA,CAAA,cAAW;oDAET,OAAO,KAAK,QAAQ;oDACpB,UAAU,IAAM,qBAAqB;oDACrC,WAAU;;sEAEV,sSAAC;4DAAI,WAAU;sEACZ,cAAA,sSAAC;gEAAK,WAAU;;oEAAW;oEAAK;;;;;;;;;;;;wDAElC,oBAAoB,sBAAQ,sSAAC,+RAAA,CAAA,YAAS;4DAAC,MAAM;4DAAI,WAAU;;;;;;;mDARvD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAkBnC;GArEgB;;QACF,sQAAA,CAAA,QAAK;;;KADH", "debugId": null}}, {"offset": {"line": 4161, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/bloomgoo/gold-exchange-platform/frontend/components/common/data-table/table-footer.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport { Table as TableType } from \"@tanstack/react-table\"\r\nimport { But<PERSON> } from \"@/components/ui/button\"\r\nimport { ChevronLeft, ChevronRight, ChevronsLeft, ChevronsRight } from \"lucide-react\"\r\nimport { PageSelector } from \"@/components/common/selector/page-selector\"\r\nimport { PageSizeSelector } from \"@/components/common/selector/page-size-selector\"\r\n\r\ninterface TableFooterProps<TData> {\r\n  table: TableType<TData>\r\n  className?: string\r\n  totalItems?: number\r\n  isShowSelectedRows?: boolean\r\n  onShowSelectedRows?: () => void\r\n}\r\n\r\nexport function TableFooter<TData>({\r\n  table,\r\n  className,\r\n  totalItems,\r\n  isShowSelectedRows,\r\n  onShowSelectedRows,\r\n}: TableFooterProps<TData>) {\r\n  // Kiểm tra xem có hàng nào được chọn không\r\n  const selectedRowsCount = table.getFilteredSelectedRowModel().rows.length;\r\n  const hasSelectedRows = selectedRowsCount > 0;\r\n\r\n  return (\r\n    <div className=\"sticky bottom-0 bg-background border-t mt-auto\">\r\n      <div className=\"flex items-center justify-between space-x-2 px-4 py-2\">\r\n        <div className=\"flex items-center space-x-2\">\r\n          <PageSizeSelector\r\n            type=\"table\"\r\n            table={table}\r\n            pageSizeOptions={[20, 50, 100, 200]}\r\n            triggerClassName=\"w-[120px]\"\r\n          />\r\n\r\n          {/* Hiển thị thông tin về hàng đã chọn khi có hàng được chọn */}\r\n          {hasSelectedRows && (\r\n            <p className=\"text-sm text-muted-foreground\">\r\n              {selectedRowsCount} trên{\" \"}\r\n              {table.getFilteredRowModel().rows.length} bản ghi đã chọn.\r\n            </p>\r\n          )}\r\n        </div>\r\n\r\n        <div className=\"flex items-center space-x-6 lg:space-x-8\">\r\n          <div className=\"flex w-[100x] items-center justify-center text-sm font-medium\">\r\n            Hiển thị {table.getRowCount()} trên {totalItems} bản ghi\r\n          </div>\r\n          <PageSelector\r\n            type=\"table\"\r\n            table={table}\r\n            showFirstLastButtons={true}\r\n            showPrevNextButtons={true}\r\n            maxPagesToShow={7}\r\n          />\r\n        </div>\r\n      </div>\r\n    </div>\r\n  )\r\n}"], "names": [], "mappings": ";;;;AAKA;AACA;AANA;;;;AAgBO,SAAS,YAAmB,EACjC,KAAK,EACL,SAAS,EACT,UAAU,EACV,kBAAkB,EAClB,kBAAkB,EACM;IACxB,2CAA2C;IAC3C,MAAM,oBAAoB,MAAM,2BAA2B,GAAG,IAAI,CAAC,MAAM;IACzE,MAAM,kBAAkB,oBAAoB;IAE5C,qBACE,sSAAC;QAAI,WAAU;kBACb,cAAA,sSAAC;YAAI,WAAU;;8BACb,sSAAC;oBAAI,WAAU;;sCACb,sSAAC,gKAAA,CAAA,mBAAgB;4BACf,MAAK;4BACL,OAAO;4BACP,iBAAiB;gCAAC;gCAAI;gCAAI;gCAAK;6BAAI;4BACnC,kBAAiB;;;;;;wBAIlB,iCACC,sSAAC;4BAAE,WAAU;;gCACV;gCAAkB;gCAAM;gCACxB,MAAM,mBAAmB,GAAG,IAAI,CAAC,MAAM;gCAAC;;;;;;;;;;;;;8BAK/C,sSAAC;oBAAI,WAAU;;sCACb,sSAAC;4BAAI,WAAU;;gCAAgE;gCACnE,MAAM,WAAW;gCAAG;gCAAO;gCAAW;;;;;;;sCAElD,sSAAC,wJAAA,CAAA,eAAY;4BACX,MAAK;4BACL,OAAO;4BACP,sBAAsB;4BACtB,qBAAqB;4BACrB,gBAAgB;;;;;;;;;;;;;;;;;;;;;;;AAM5B;KA9CgB", "debugId": null}}, {"offset": {"line": 4276, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/bloomgoo/gold-exchange-platform/frontend/components/common/admin/order-books/order-book-form-modal.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { useAuth } from '@/hooks/use-auth';\r\nimport { cn } from '@/lib/utils';\r\nimport { zodResolver } from '@hookform/resolvers/zod';\r\nimport { Loader2, TrendingDown, TrendingUp, User } from 'lucide-react';\r\nimport { useEffect, useMemo, useRef, useState } from 'react';\r\nimport { useForm } from 'react-hook-form';\r\nimport { toast } from 'sonner';\r\nimport { z } from 'zod';\r\n\r\nimport {\r\n  AlertDialog,\r\n  AlertDialogAction,\r\n  AlertDialogCancel,\r\n  AlertDialogContent,\r\n  AlertDialogDescription,\r\n  AlertDialogFooter,\r\n  AlertDialogHeader,\r\n  AlertDialogTitle,\r\n} from '@/components/ui/alert-dialog';\r\nimport { Button } from '@/components/ui/button';\r\nimport {\r\n  Dialog,\r\n  DialogContent,\r\n  DialogDescription,\r\n  DialogFooter,\r\n  DialogHeader,\r\n  DialogTitle,\r\n} from '@/components/ui/dialog';\r\nimport {\r\n  Form,\r\n  FormControl,\r\n  FormDescription,\r\n  FormField,\r\n  FormItem,\r\n  FormLabel,\r\n  FormMessage,\r\n} from '@/components/ui/form';\r\nimport { Input } from '@/components/ui/input';\r\nimport {\r\n  Choicebox,\r\n  ChoiceboxItem,\r\n  ChoiceboxItemContent,\r\n  ChoiceboxItemDescription,\r\n  ChoiceboxItemHeader,\r\n  ChoiceboxItemIndicator,\r\n  ChoiceboxItemTitle,\r\n} from '@/components/ui/kibo-ui/choicebox';\r\nimport { SearchableSelect } from '@/components/ui/searchable-select';\r\nimport {\r\n  Select,\r\n  SelectContent,\r\n  SelectItem,\r\n  SelectTrigger,\r\n  SelectValue,\r\n} from '@/components/ui/select';\r\n\r\nimport { api } from '@/lib/api';\r\nimport { ApiResponse, PaginationResponse } from '@/lib/response';\r\nimport { calculateDeposit, calculateNormalProductPrice, calculateProcessingFee, calculateTotal, BusinessType as UtilsBusinessType, OrderType as UtilsOrderType } from '@/lib/utils';\r\nimport { BusinessType, OrderBook, OrderProductDetail, OrderStatus, OrderType } from './type/order-books';\r\nimport { ProductInfo, ProductResponse, ProductSearchOption, UserSearchOption } from './type/product';\r\n\r\ninterface OrderBookFormModalProps {\r\n  isOpen: boolean;\r\n  onClose: () => void;\r\n  orderBook?: OrderBook | null;\r\n  mode: 'create' | 'update' | 'view';\r\n  onSuccess?: () => void;\r\n  initialToken?: any; // Token ban đầu khi mở form từ danh sách token assets\r\n  isUserMode?: boolean; // Chế độ người dùng (không phải admin)\r\n  initialOrderType?: 'buy' | 'sell' | 'withdrawal'; // Loại lệnh ban đầu\r\n  fixedPrice?: { // Add fixed price prop\r\n    usd: number;\r\n    vnd: number;\r\n  } | null;\r\n}\r\n\r\n// Define these before the formSchema\r\ntype FormOrderStatus = 'COMPLETED' | 'DEPOSITED' | 'TERMINATED' | 'CANCELLED';\r\n\r\n// Map OrderStatus enum mới sang các trạng thái cũ\r\nconst OrderStatusMap = {\r\n  COMPLETED: OrderStatus.COMPLETED,\r\n  DEPOSITED: OrderStatus.DEPOSITED,\r\n  TERMINATED: OrderStatus.TERMINATED,\r\n  CANCELLED: OrderStatus.CANCELLED\r\n};\r\n\r\n// Hàm tính giá token đã được chuyển sang utils.ts\r\n\r\n// Schema cho mỗi item bạc trong lệnh\r\nconst silverItemSchema = z.object({\r\n  productId: z.string().min(1, 'Vui lòng chọn bạc'),\r\n  volume: z.number().min(0.0001, 'Khối lượng phải lớn hơn 0'),\r\n  productInfo: z.any().optional(), // Lưu thông tin bổ sung về token để hiển thị\r\n});\r\n\r\nconst formSchema = z.object({\r\n  userId: z.string().min(1, 'Vui lòng chọn người dùng'),\r\n  type: z.enum(['buy', 'sell', 'withdrawal']),\r\n  businessType: z.nativeEnum(BusinessType).default(BusinessType.NORMAL),\r\n  price: z.number().nullable().default(null),\r\n  silverItems: z.array(silverItemSchema).min(1, 'Vui lòng thêm ít nhất một loại bạc'),\r\n  remainingVolume: z.number().nullable().default(null),\r\n  status: z.enum([\r\n    OrderStatus.COMPLETED,\r\n    OrderStatus.DEPOSITED,\r\n    OrderStatus.TERMINATED,\r\n    OrderStatus.CANCELLED\r\n  ] as [FormOrderStatus, ...FormOrderStatus[]]).default(OrderStatus.DEPOSITED as FormOrderStatus),\r\n});\r\n\r\ntype FormValues = z.infer<typeof formSchema>;\r\n\r\n\r\nexport function OrderBookFormModal({ isOpen, onClose, orderBook, mode, onSuccess, initialToken, isUserMode = false, initialOrderType = 'buy', fixedPrice = null }: OrderBookFormModalProps) {\r\n  const [isLoading, setIsLoading] = useState(false);\r\n  const [isFormReady, setIsFormReady] = useState(false);\r\n  const [showConfirmDialog, setShowConfirmDialog] = useState(false);\r\n  const formIsDirty = useRef(false);\r\n  const formRef = useRef<HTMLFormElement>(null);\r\n  const { user } = useAuth(); // Lấy thông tin người dùng đang đăng nhập\r\n\r\n  // Khởi tạo form với schema phù hợp với mode\r\n  const form = useForm<FormValues>({\r\n    resolver: zodResolver(formSchema) as any,\r\n    defaultValues: {\r\n      userId: '',\r\n      type: 'buy',\r\n      businessType: BusinessType.NORMAL,\r\n      price: null,\r\n      silverItems: [],\r\n      remainingVolume: null,\r\n      status: 'pending' as FormOrderStatus,\r\n    },\r\n  });\r\n\r\n  // Theo dõi thay đổi của form\r\n  useEffect(() => {\r\n    const subscription = form.watch(() => {\r\n      formIsDirty.current = form.formState.isDirty;\r\n    });\r\n    return () => subscription.unsubscribe();\r\n  }, [form, form.watch]);\r\n\r\n  // Xử lý đóng form\r\n  const handleClose = () => {\r\n    if (mode !== 'view' && formIsDirty.current) {\r\n      setShowConfirmDialog(true);\r\n    } else {\r\n      onClose();\r\n    }\r\n  };\r\n\r\n  // Fetch users function\r\n  const fetchUsers = async (search: string): Promise<UserSearchOption[]> => {\r\n    try {\r\n      const response = await api.get<PaginationResponse<any>>(`users?page=1&limit=10&search=${encodeURIComponent(search)}`);\r\n      const users = response?.data || [];\r\n      return users.map(user => ({\r\n        id: user.id,\r\n        fullName: user.fullName,\r\n        username: user.username,\r\n        email: user.email\r\n      }));\r\n    } catch (error) {\r\n      return [];\r\n    }\r\n  };\r\n\r\n  // Fetch products function\r\n  const fetchProducts = async (search: string): Promise<ProductSearchOption[]> => {\r\n    try {\r\n      const response = await api.get<PaginationResponse<ProductResponse>>(`ecom-products?page=1&limit=10&search=${encodeURIComponent(search)}`);\r\n      const products = response?.data || [];\r\n      return products.map(product => ({\r\n        id: product.id,\r\n        productCode: product.productCode,\r\n        productName: product.productName,\r\n        weight: typeof product.weight === 'string' ? parseFloat(product.weight) : (product.weight || 1)\r\n      }));\r\n    } catch (error) {\r\n      return [];\r\n    }\r\n  };\r\n\r\n  // Cập nhật form khi có dữ liệu order book hoặc khi modal mở/đóng\r\n  useEffect(() => {\r\n    if (!isOpen) {\r\n      // Reset form khi modal đóng\r\n      setTimeout(() => {\r\n        form.reset();\r\n        formIsDirty.current = false;\r\n        setIsFormReady(false);\r\n      }, 300); // Đợi animation đóng hoàn tất\r\n    } else {\r\n      // Đặt trạng thái đang tải khi mở modal\r\n      setIsFormReady(false);\r\n\r\n      setTimeout(() => {\r\n        if (orderBook && (mode === 'update' || mode === 'view')) {\r\n          // Chuyển đổi dữ liệu order book để phù hợp với form\r\n          // Sử dụng type từ orderBook nếu có, nếu không thì dựa vào orderType\r\n          let formType = (orderBook as any).type;\r\n          if (!formType) {\r\n            if (orderBook.orderType === OrderType.BUY) {\r\n              formType = 'buy';\r\n            } else if (orderBook.orderType === OrderType.SELL) {\r\n              formType = 'sell';\r\n            } else {\r\n              formType = 'buy'; // Mặc định là buy nếu không xác định được\r\n            }\r\n          }\r\n\r\n          // Chuẩn bị dữ liệu silverItems với thông tin token\r\n          const silverItems = orderBook.details?.length > 0\r\n            ? orderBook.details.map(detail => ({\r\n              productId: detail.productId,\r\n              quantity: detail.quantity || 0,\r\n              productInfo: detail.product ? {\r\n                id: detail.product.id,\r\n                productCode: detail.product.productCode,\r\n                productName: detail.product.productName,\r\n                weight: detail.product.weight || 1\r\n              } : null\r\n            }))\r\n            : [];\r\n\r\n          const formData = {\r\n            id: orderBook.id,\r\n            userId: orderBook.userId,\r\n            type: formType,\r\n            // Để trống giá thị trường theo yêu cầu\r\n            price: null,\r\n            silverItems: silverItems,\r\n            // Không gửi remainingVolume để lược bỏ trường này theo yêu cầu\r\n            remainingVolume: null,\r\n            status: orderBook.status,\r\n            businessType: orderBook.businessType || BusinessType.NORMAL,\r\n          };\r\n\r\n          // Reset form với dữ liệu order book\r\n          form.reset(formData as any);\r\n          formIsDirty.current = false;\r\n        } else if (isOpen && mode === 'create') {\r\n          // Reset form với giá trị mặc định khi tạo mới\r\n          // Lấy userId từ người dùng đang đăng nhập nếu ở chế độ người dùng\r\n          const userId = isUserMode && user ? user.id : '';\r\n\r\n          if (initialToken) {\r\n            // Nếu có initialToken, tạo form với token đã chọn\r\n            form.reset({\r\n              userId: userId,\r\n              type: initialOrderType,\r\n              price: null,\r\n              silverItems: [\r\n                {\r\n                  productId: initialToken.id || '',\r\n                  volume: initialOrderType === 'buy' ? 1 : (initialToken.amount || 1),\r\n                  productInfo: {\r\n                    id: initialToken.id,\r\n                    tokenCode: initialToken.tokenCode,\r\n                    tokenName: initialToken.tokenName,\r\n                    oz: initialToken.oz || 1,\r\n                    amount: initialToken.amount || 0\r\n                  }\r\n                }\r\n              ],\r\n              remainingVolume: null,\r\n              status: 'pending' as FormOrderStatus,\r\n            });\r\n          } else {\r\n            // Nếu không có initialToken, tạo form mặc định\r\n            form.reset({\r\n              userId: userId,\r\n              type: initialOrderType,\r\n              price: null,\r\n              silverItems: [\r\n                { productId: '', volume: 0, productInfo: null }\r\n              ],\r\n              remainingVolume: null,\r\n              status: 'pending' as FormOrderStatus,\r\n            });\r\n          }\r\n          formIsDirty.current = false;\r\n        }\r\n\r\n        // Đánh dấu form đã sẵn sàng\r\n        setIsFormReady(true);\r\n      }, 300);\r\n    }\r\n  }, [isOpen, orderBook, mode, form, initialToken, initialOrderType]);\r\n\r\n  // Reset silverItems khi type hoặc userId thay đổi\r\n  useEffect(() => {\r\n    const subscription = form.watch((_, { name }) => {\r\n      // Nếu thay đổi loại lệnh hoặc người dùng, reset danh sách bạc\r\n      if (name === 'type' || name === 'userId') {\r\n        // Xóa các token đã chọn\r\n        setTimeout(() => {\r\n          form.setValue('silverItems', [{ productId: '', volume: 0, productInfo: null }], { shouldDirty: true });\r\n        }, 100);\r\n      }\r\n    });\r\n    return () => subscription.unsubscribe();\r\n  }, [form]);\r\n\r\n  // Xử lý khi form submit\r\n  const handleSubmit = async (data: FormValues) => {\r\n    try {\r\n\r\n      if (mode === 'view') return;\r\n\r\n      setIsLoading(true);\r\n\r\n      if (mode === 'create') {\r\n        // Kiểm tra có ít nhất một loại bạc hợp lệ\r\n        const hasValidItems = data.silverItems.some(item => item.productId && item.volume > 0);\r\n\r\n        if (!hasValidItems) {\r\n          toast.error('Vui lòng thêm ít nhất một loại bạc với số lượng hợp lệ');\r\n          setIsLoading(false);\r\n          return;\r\n        }\r\n\r\n        // Chuyển đổi type từ form sang orderType cho API\r\n        const orderType = data.type === 'buy' ? OrderType.BUY : OrderType.SELL;\r\n\r\n        // Chuẩn bị products array cho API\r\n        const products = data.silverItems.map(item => {\r\n          // Tính giá cho từng loại sản phẩm dựa trên weight\r\n          const productWeight = item.productInfo?.weight || 1;\r\n          const productPrice = calculateNormalProductPrice(fixedPrice?.vnd || 0, productWeight);\r\n\r\n          return {\r\n            productId: item.productId,\r\n            price: productPrice,\r\n            quantity: Number(item.volume)\r\n          };\r\n        });\r\n\r\n        // Tạo payload cho API\r\n        const createPayload = {\r\n          userId: data.userId,\r\n          orderType,\r\n          businessType: data.businessType,\r\n          products,\r\n          price: fixedPrice?.vnd || 0 // Sử dụng giá cố định\r\n        };\r\n\r\n        // Gọi API tạo lệnh mới\r\n        try {\r\n          const response = await api.post<ApiResponse<OrderBook>>('/order-books', createPayload);\r\n          toast.success('Khớp lệnh thành công');\r\n          form.reset();\r\n          formIsDirty.current = false;\r\n          onClose();\r\n          if (onSuccess) onSuccess();\r\n        } catch (error: any) {\r\n          const errorMessage = error.response?.data?.message || 'Có lỗi xảy ra khi tạo lệnh';\r\n          const validationErrors = error.response?.data?.errors;\r\n\r\n          if (validationErrors) {\r\n            Object.values(validationErrors).forEach((message: any) => {\r\n              toast.error(`Lỗi: ${message}`);\r\n            });\r\n          } else {\r\n            toast.error(errorMessage);\r\n          }\r\n        }\r\n      } else if (mode === 'update' && orderBook?.id) {\r\n        // Hiện tại chỉ hỗ trợ cập nhật lệnh đơn\r\n        // TODO: Cập nhật khi API hỗ trợ nhiều loại bạc\r\n        if (data.silverItems.length > 0) {\r\n          // Xử lý tất cả các token trong danh sách\r\n\r\n          // Chuyển đổi type từ form sang orderType cho API\r\n          let orderType;\r\n          if (data.type === 'buy') {\r\n            orderType = OrderType.BUY;\r\n          } else if (data.type === 'sell') {\r\n            orderType = OrderType.SELL;\r\n          } else {\r\n            orderType = OrderType.WITHDRAWAL;\r\n          }\r\n\r\n          // Đảm bảo chúng ta có giá hợp lệ (chỉ kiểm tra khi không phải lệnh rút)\r\n          if (data.type !== 'withdrawal' && (!data.price || isNaN(Number(data.price)))) {\r\n            toast.error('Vui lòng nhập giá hợp lệ');\r\n            setIsLoading(false);\r\n            return;\r\n          }\r\n\r\n          // Chuẩn bị tokens array và payload theo loại lệnh\r\n          let updateProducts: OrderProductDetail[];\r\n          let updatePayload: any;\r\n\r\n          if (data.type === 'withdrawal') {\r\n            // Lệnh rút không cần tính giá\r\n            // Xử lý tất cả các token trong danh sách\r\n            updateProducts = data.silverItems.map(item => ({\r\n              productId: item.productId,\r\n              price: 0, // Giá không quan trọng cho lệnh rút\r\n              quantity: Number(item.volume)\r\n            }));\r\n\r\n            // Cập nhật lệnh rút với cấu trúc mới\r\n            updatePayload = {\r\n              id: orderBook.id,\r\n              userId: data.userId,\r\n              orderType: orderType,\r\n              status: data.status.toUpperCase() as OrderStatus,\r\n              tokens: updateProducts,\r\n              updatedBy: data.userId, // Dùng userId là người cập nhật\r\n            };\r\n          } else {\r\n            // Tính toán thông tin đơn hàng cho update mode (lệnh mua/bán)\r\n            // Xử lý tất cả các token trong danh sách\r\n            updateProducts = data.silverItems.map(item => {\r\n              // Tính giá cho từng loại token dựa trên oz nếu có\r\n              const productWeight = item.productInfo?.weight || 1;\r\n              const productPrice = calculateNormalProductPrice(Number(data.price), productWeight);\r\n\r\n              return {\r\n                productId: item.productId,\r\n                price: productPrice,\r\n                quantity: Number(item.volume)\r\n              };\r\n            });\r\n\r\n            // Tính tổng giá trị (chỉ để tham khảo, không gửi lên server)\r\n            const calculatedTotalPrice = updateProducts.reduce((sum, product) => sum + (product.price * product.quantity), 0);\r\n\r\n            // Xử lý dựa trên loại hình giao dịch\r\n            if (data.businessType === BusinessType.NORMAL) {\r\n              // Ký quỹ: Chỉ tính phí đặt cọc 10%, không tính phí tất toán\r\n              const updateDepositAmount = calculatedTotalPrice * 0.1;\r\n\r\n              // Cập nhật lệnh mua/bán với cấu trúc mới cho ký quỹ\r\n              updatePayload = {\r\n                id: orderBook.id,\r\n                userId: data.userId,\r\n                orderType: orderType,\r\n                businessType: BusinessType.NORMAL,\r\n                status: data.status.toUpperCase() as OrderStatus,\r\n                tokens: updateProducts,\r\n                depositAmount: updateDepositAmount.toFixed(2),\r\n                updatedBy: data.userId, // Dùng userId là người cập nhật\r\n              };\r\n            } else {\r\n              // Bạc giao ngay: Thanh toán 100% giá trị\r\n              // Kiểm tra tổng khối lượng cho loại hình bạc giao ngay\r\n              const totalVolume = data.silverItems.reduce((sum, item) => sum + Number(item.volume), 0);\r\n\r\n              if (totalVolume < 10) {\r\n                toast.error('Lệnh bạc giao ngay yêu cầu tối thiểu 10 lượng bạc');\r\n                setIsLoading(false);\r\n                return;\r\n              }\r\n\r\n              // Cập nhật lệnh mua/bán với cấu trúc mới cho bạc giao ngay\r\n              updatePayload = {\r\n                id: orderBook.id,\r\n                userId: data.userId,\r\n                orderType: orderType,\r\n                businessType: BusinessType.IMMEDIATE_DELIVERY,\r\n                status: data.status.toUpperCase() as OrderStatus,\r\n                tokens: updateProducts,\r\n                // Không gửi depositAmount và settlementAmount vì backend sẽ tính toán\r\n                // depositAmount sẽ được đặt = 0 và settlementAmount = totalPrice\r\n                updatedBy: data.userId, // Dùng userId là người cập nhật\r\n              };\r\n            }\r\n          }\r\n\r\n\r\n          // Gọi API cập nhật lệnh\r\n          try {\r\n            await api.put<ApiResponse<OrderBook>>(`/order-books/${orderBook.id}`, updatePayload);\r\n            toast.success('Cập nhật lệnh thành công');\r\n            formIsDirty.current = false;\r\n            onClose();\r\n            if (onSuccess) onSuccess();\r\n          } catch (error: any) {\r\n            const errorMessage = error.response?.data?.message || 'Có lỗi xảy ra khi cập nhật lệnh';\r\n            const validationErrors = error.response?.data?.errors;\r\n\r\n            if (validationErrors) {\r\n              Object.values(validationErrors).forEach((message: any) => {\r\n                toast.error(`Lỗi: ${message}`);\r\n              });\r\n            } else {\r\n              toast.error(errorMessage);\r\n            }\r\n          }\r\n        } else {\r\n          toast.error('Vui lòng thêm ít nhất một loại bạc');\r\n        }\r\n      }\r\n    } catch (error: any) {\r\n      toast.error(error.response?.data?.message || 'Có lỗi xảy ra');\r\n    } finally {\r\n      setIsLoading(false);\r\n    }\r\n  };\r\n\r\n  // Format currency\r\n  const formatCurrency = (value: number | undefined | null, ozValue?: number, volume?: number) => {\r\n    // Giá bạc thế giới * 1.2056 * 270000 + 11% VAT\r\n    if (value === undefined || value === null) return \"\";\r\n\r\n    // Sử dụng giá trị oz nếu có\r\n    const oz = ozValue || 1;\r\n    const vol = volume || 1;\r\n\r\n    // Sử dụng hàm tính giá đã định nghĩa\r\n    const price = calculateNormalProductPrice(value, oz);\r\n\r\n    // Nhân với volume nếu cần tính tổng giá trị\r\n    const totalPrice = price * vol;\r\n\r\n    return new Intl.NumberFormat('vi-VN', {\r\n\r\n      maximumFractionDigits: 0\r\n    }).format(totalPrice);\r\n  };\r\n\r\n  const title = {\r\n    create: 'Tạo lệnh mới',\r\n    update: 'Cập nhật lệnh',\r\n    view: 'Chi tiết lệnh',\r\n  }[mode];\r\n\r\n  const description = {\r\n    create: 'Điền thông tin để tạo lệnh giao dịch mới',\r\n    update: 'Cập nhật thông tin lệnh giao dịch',\r\n    view: 'Xem chi tiết thông tin lệnh giao dịch',\r\n  }[mode];\r\n\r\n  // Update form price when fixedPrice changes\r\n  useEffect(() => {\r\n    if (fixedPrice) {\r\n      form.setValue('price', fixedPrice.vnd);\r\n    }\r\n  }, [fixedPrice, form]);\r\n\r\n  // Chuyển đổi type form sang OrderType cho utils\r\n  const getOrderTypeForCalculation = (type: 'buy' | 'sell' | 'withdrawal'): UtilsOrderType => {\r\n    return type === 'buy' ? 'buy' : 'sell';\r\n  };\r\n\r\n  // Chuyển đổi BusinessType từ enum sang string\r\n  const getBusinessTypeForCalculation = (type: BusinessType): UtilsBusinessType => {\r\n    return type as UtilsBusinessType;\r\n  };\r\n\r\n  // Tính toán giá hiển thị dựa trên loại lệnh\r\n  const displayPrice = useMemo(() => {\r\n    if (!fixedPrice) return null;\r\n\r\n    return {\r\n      usd: form.watch('type') === 'buy' ? fixedPrice.usd : fixedPrice.usd * 0.98, // Giá bán thấp hơn 2%\r\n      vnd: form.watch('type') === 'buy' ? fixedPrice.vnd : fixedPrice.vnd * 0.98\r\n    };\r\n  }, [fixedPrice, form.watch('type')]);\r\n\r\n  return (\r\n    <>\r\n      <Dialog open={isOpen} onOpenChange={(open) => !open && handleClose()}>\r\n        <DialogContent className=\"sm:max-w-[600px] flex flex-col max-h-[90vh]\">\r\n          <DialogHeader className=\"flex-shrink-0\">\r\n            <DialogTitle className=\"flex items-center gap-2\">\r\n              {form.watch('type') === 'buy' ? (\r\n                <>\r\n                  <TrendingUp className=\"h-5 w-5 text-green-600\" />\r\n                  <span className=\"text-green-600\">Đặt lệnh mua</span>\r\n                </>\r\n              ) : (\r\n                <>\r\n                  <TrendingDown className=\"h-5 w-5 text-red-600\" />\r\n                  <span className=\"text-red-600\">Đặt lệnh bán</span>\r\n                </>\r\n              )}\r\n            </DialogTitle>\r\n            <DialogDescription>\r\n              {description}\r\n            </DialogDescription>\r\n          </DialogHeader>\r\n\r\n          {!isFormReady ? (\r\n            <div className=\"flex flex-col items-center justify-center py-8\">\r\n              <Loader2 className=\"h-8 w-8 animate-spin text-primary mb-4\" />\r\n              <p className=\"text-sm text-muted-foreground\">Đang tải dữ liệu...</p>\r\n            </div>\r\n          ) : (\r\n            <div className=\"flex-1 overflow-hidden flex flex-col\">\r\n              <Form {...form}>\r\n                <form\r\n                  ref={formRef}\r\n                  onSubmit={form.handleSubmit(handleSubmit)}\r\n                  className=\"space-y-4 overflow-y-auto pr-4 flex-1\"\r\n                >\r\n                  <div className=\"grid grid-cols-2 gap-4\">\r\n                    {/* User selection - Chỉ hiển thị khi không phải chế độ người dùng */}\r\n                    {!isUserMode ? (\r\n                      <FormField\r\n                        control={form.control}\r\n                        name=\"userId\"\r\n                        render={({ field }) => (\r\n                          <FormItem>\r\n                            <FormLabel>Người dùng</FormLabel>\r\n                            <FormControl>\r\n                              <SearchableSelect\r\n                                value={field.value}\r\n                                onChange={field.onChange}\r\n                                placeholder=\"Chọn người dùng\"\r\n                                searchPlaceholder=\"Tìm kiếm người dùng...\"\r\n                                fetchOptions={fetchUsers}\r\n                                disabled={mode === 'view'}\r\n                                renderOption={(option) => option ?\r\n                                  `${option.fullName || option.username} - (${option.email})` :\r\n                                  \"Chọn người dùng\"\r\n                                }\r\n                                loadOnMount={!!field.value}\r\n                              />\r\n                            </FormControl>\r\n                            <FormMessage />\r\n                          </FormItem>\r\n                        )}\r\n                      />\r\n                    ) : (\r\n                      // Hiển thị thông tin người dùng đang đăng nhập khi ở chế độ người dùng\r\n                      <div className=\"flex flex-col space-y-1.5\">\r\n                        <div className=\"font-medium text-sm\">Người dùng</div>\r\n                        <div className=\"flex items-center p-2 border rounded-md bg-muted\">\r\n                          <User className=\"h-4 w-4 mr-2 text-muted-foreground\" />\r\n                          <span>{user?.fullName || user?.username || 'Người dùng hiện tại'}</span>\r\n                        </div>\r\n                      </div>\r\n                    )}\r\n\r\n                    {/* Buy/Sell Type selection */}\r\n                    <FormField\r\n                      control={form.control}\r\n                      name=\"type\"\r\n                      render={({ field }) => (\r\n                        <FormItem>\r\n                          <FormLabel>Loại lệnh</FormLabel>\r\n                          <Select\r\n                            disabled={mode === 'view'}\r\n                            onValueChange={field.onChange}\r\n                            defaultValue={field.value}\r\n                            value={field.value}\r\n                          >\r\n                            <FormControl>\r\n                              <SelectTrigger className='w-full'>\r\n                                <SelectValue placeholder=\"Chọn loại lệnh\" />\r\n                              </SelectTrigger>\r\n                            </FormControl>\r\n                            <SelectContent>\r\n                              <SelectItem value=\"buy\">Mua (Buy)</SelectItem>\r\n                              <SelectItem value=\"sell\">Bán (Sell)</SelectItem>\r\n                            </SelectContent>\r\n                          </Select>\r\n\r\n                          <FormMessage />\r\n                        </FormItem>\r\n                      )}\r\n                    />\r\n                  </div>\r\n\r\n                  {/* Fixed Price Display Box */}\r\n                  <div className={cn(\r\n                    \"rounded-lg p-4\",\r\n                    form.watch('type') === 'buy' ? \"bg-green-50\" : \"bg-red-50\"\r\n                  )}>\r\n                    <div className=\"flex items-center justify-between\">\r\n                      <div className=\"flex items-center gap-3\">\r\n                        <div className={`w-8 h-8 rounded-lg flex items-center justify-center ${form.watch('type') === 'buy' ? 'bg-green-100' : 'bg-red-100'}`}>\r\n                          {form.watch('type') === 'buy' ? (\r\n                            <TrendingUp className={`h-4 w-4 text-green-600`} />\r\n                          ) : (\r\n                            <TrendingDown className={`h-4 w-4 text-red-600`} />\r\n                          )}\r\n                        </div>\r\n                        <div>\r\n                          <p className={`text-sm font-medium ${form.watch('type') === 'buy' ? 'text-green-700' : 'text-red-700'}`}>\r\n                            Giá {form.watch('type') === 'buy' ? 'mua' : 'bán'}\r\n                          </p>\r\n                        </div>\r\n                      </div>\r\n                      <div className=\"text-right\">\r\n                        <p className={`text-lg font-bold ${form.watch('type') === 'buy' ? 'text-green-600' : 'text-red-600'}`}>\r\n                          ${displayPrice?.usd?.toFixed(2) || \"0.00\"}\r\n                        </p>\r\n                        <p className={`text-sm font-semibold ${form.watch('type') === 'buy' ? 'text-green-600' : 'text-red-600'}`}>\r\n                          {formatCurrency(displayPrice?.vnd || 0)} VND\r\n                        </p>\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n\r\n                  {/* Business Type selection - Chỉ hiển thị khi là lệnh mua */}\r\n                  {form.watch('type') === 'buy' && (\r\n                    <FormField\r\n                      control={form.control}\r\n                      name=\"businessType\"\r\n                      render={({ field }) => (\r\n                        <FormItem className=\"space-y-3\">\r\n                          <FormLabel>Loại hình giao dịch</FormLabel>\r\n                          <FormControl>\r\n                            <Choicebox\r\n                              onValueChange={field.onChange}\r\n                              value={field.value}\r\n                              disabled={mode === 'view'}\r\n                              className=\"grid grid-cols-2 gap-4\"\r\n                            >\r\n                              <ChoiceboxItem value={BusinessType.NORMAL}>\r\n                                <ChoiceboxItemHeader>\r\n                                  <ChoiceboxItemTitle>Bạc online ký quỹ</ChoiceboxItemTitle>\r\n                                  <ChoiceboxItemDescription>\r\n                                    Ký quỹ 10% giá trị\r\n                                  </ChoiceboxItemDescription>\r\n                                </ChoiceboxItemHeader>\r\n                                <ChoiceboxItemContent>\r\n                                  <ChoiceboxItemIndicator />\r\n                                </ChoiceboxItemContent>\r\n                              </ChoiceboxItem>\r\n\r\n                              <ChoiceboxItem value={BusinessType.IMMEDIATE_DELIVERY}>\r\n                                <ChoiceboxItemHeader>\r\n                                  <ChoiceboxItemTitle>Bạc giao ngay</ChoiceboxItemTitle>\r\n                                  <ChoiceboxItemDescription>\r\n                                    Thanh toán 100% giá trị\r\n                                  </ChoiceboxItemDescription>\r\n                                </ChoiceboxItemHeader>\r\n                                <ChoiceboxItemContent>\r\n                                  <ChoiceboxItemIndicator />\r\n                                </ChoiceboxItemContent>\r\n                              </ChoiceboxItem>\r\n                            </Choicebox>\r\n                          </FormControl>\r\n                          <FormMessage />\r\n                        </FormItem>\r\n                      )}\r\n                    />\r\n                  )}\r\n\r\n\r\n                  {/* Price input */}\r\n                  <div className=\"grid grid-cols-1 gap-4\">\r\n                    {/* Comment out the old price input */}\r\n                    {/* <FormField\r\n                    control={form.control}\r\n                    name=\"price\"\r\n                    render={({ field: { value, onChange, ...field } }) => (\r\n                      <FormItem>\r\n                        <FormLabel>Giá thị trường</FormLabel>\r\n                        <FormControl>\r\n                          <div className=\"relative flex-1\">\r\n                            <div className=\"absolute inset-y-0 left-2 flex items-center pointer-events-none\">\r\n                              <IconCurrencyDollar className=\"h-4 w-4 text-muted-foreground\" />\r\n                            </div>\r\n                            <Input\r\n                              {...field}\r\n                              disabled={mode === 'view'}\r\n                              type=\"number\"\r\n                              placeholder=\"Nhập giá thị trường\"\r\n                              step=\"0.1\"\r\n                              value={value === null ? '' : value}\r\n                              onChange={(e) => {\r\n                                const val = e.target.value === '' ? null : Number(e.target.value);\r\n                                onChange(val);\r\n                              }}\r\n                              className=\"pl-8\"\r\n                            />\r\n                          </div>\r\n                        </FormControl>\r\n                        <FormDescription>\r\n                          Giá thị trường {form.watch('type') === 'buy' ? 'mua' : 'bán'} bạc\r\n                        </FormDescription>\r\n                        <FormMessage />\r\n                      </FormItem> */}\r\n\r\n                    {/* Status selection (only visible in update mode) */}\r\n                    {mode === 'update' ? (\r\n                      <FormField\r\n                        control={form.control}\r\n                        name=\"status\"\r\n                        render={({ field }) => (\r\n                          <FormItem>\r\n                            <FormLabel>Trạng thái</FormLabel>\r\n                            <Select\r\n                              disabled={mode !== 'update'}\r\n                              onValueChange={field.onChange}\r\n                              defaultValue={field.value}\r\n                              value={field.value}\r\n                            >\r\n                              <FormControl>\r\n                                <SelectTrigger>\r\n                                  <SelectValue placeholder=\"Chọn trạng thái\" />\r\n                                </SelectTrigger>\r\n                              </FormControl>\r\n                              <SelectContent>\r\n                                <SelectItem value={OrderStatus.DEPOSITED}>Đã ký quỹ</SelectItem>\r\n                                <SelectItem value={OrderStatus.COMPLETED}>Đã tất toán</SelectItem>\r\n                                <SelectItem value={OrderStatus.TERMINATED}>Đã hủy</SelectItem>\r\n                              </SelectContent>\r\n                            </Select>\r\n                            <FormDescription>\r\n                              Trạng thái hiện tại của lệnh\r\n                            </FormDescription>\r\n                            <FormMessage />\r\n                          </FormItem>\r\n                        )}\r\n                      />\r\n                    ) : (\r\n                      // Empty div to maintain grid layout when not in update mode\r\n                      <div></div>\r\n                    )}\r\n                  </div>\r\n\r\n                  {/* Silver Items Section */}\r\n                  <div className=\"space-y-4\">\r\n                    <div className=\"flex justify-between items-center\">\r\n                      <h3 className=\"text-lg font-medium\">Danh sách bạc</h3>\r\n                      <Button\r\n                        type=\"button\"\r\n                        variant=\"outline\"\r\n                        size=\"sm\"\r\n                        onClick={() => {\r\n                          const currentItems = form.getValues('silverItems') || [];\r\n                          form.setValue('silverItems', [\r\n                            ...currentItems,\r\n                            { productId: '', volume: 0, productInfo: null }\r\n                          ]);\r\n                        }}\r\n                        disabled={mode === 'view'}\r\n                      >\r\n                        Thêm bạc\r\n                      </Button>\r\n                    </div>\r\n\r\n                    <div className=\"space-y-4\">\r\n                      {form.watch('silverItems')?.map((_, index) => (\r\n                        <div key={index} className=\"border rounded-md p-4 relative\">\r\n                          {mode !== 'view' && (\r\n                            <Button\r\n                              type=\"button\"\r\n                              variant=\"ghost\"\r\n                              size=\"icon\"\r\n                              className=\"absolute top-2 right-2 h-6 w-6\"\r\n                              onClick={() => {\r\n                                const currentItems = [...form.getValues('silverItems')];\r\n                                currentItems.splice(index, 1);\r\n                                form.setValue('silverItems', currentItems);\r\n                              }}\r\n                            >\r\n                              ✕\r\n                            </Button>\r\n                          )}\r\n\r\n                          <div className=\"grid grid-cols-2 gap-4\">\r\n                            {/* Product selection */}\r\n                            <FormField\r\n                              control={form.control}\r\n                              name={`silverItems.${index}.productId`}\r\n                              render={({ field }) => (\r\n                                <FormItem>\r\n                                  <FormLabel>Loại bạc</FormLabel>\r\n                                  <FormControl>\r\n                                    <SearchableSelect\r\n                                      value={field.value}\r\n                                      onChange={(value) => {\r\n                                        field.onChange(value);\r\n                                        // Lưu thông tin sản phẩm để hiển thị\r\n                                        api.get<ProductResponse>(`ecom-products/${value}`).then(response => {\r\n                                          if (response) {\r\n                                            const option: ProductInfo = {\r\n                                              id: response.id,\r\n                                              productCode: response.productCode || 'Không xác định',\r\n                                              productName: response.productName || 'Không xác định',\r\n                                              weight: typeof response.weight === 'string' ? parseFloat(response.weight) : (response.weight || 1)\r\n                                            };\r\n                                            const currentItems = [...form.getValues('silverItems')];\r\n                                            currentItems[index].productInfo = option;\r\n                                            form.setValue('silverItems', currentItems);\r\n                                          }\r\n                                        }).catch(error => {\r\n                                          console.error('Error fetching product details:', error);\r\n                                        });\r\n                                      }}\r\n                                      placeholder=\"Chọn loại bạc\"\r\n                                      searchPlaceholder=\"Tìm kiếm bạc...\"\r\n                                      fetchOptions={fetchProducts}\r\n                                      disabled={mode === 'view' || !form.watch('userId')}\r\n                                      renderOption={(option) => {\r\n                                        if (!option) return \"Chọn bạc\";\r\n                                        const productOption = option as unknown as ProductSearchOption;\r\n                                        return `${productOption.productCode} - ${productOption.productName} (${productOption.weight} oz)`;\r\n                                      }}\r\n                                      loadOnMount={!!field.value}\r\n                                    />\r\n                                  </FormControl>\r\n                                  <FormMessage />\r\n                                </FormItem>\r\n                              )}\r\n                            />\r\n\r\n                            {/* Volume input */}\r\n                            <FormField\r\n                              control={form.control}\r\n                              name={`silverItems.${index}.volume`}\r\n                              render={({ field: { value, onChange, ...field } }) => (\r\n                                <FormItem>\r\n                                  <FormLabel>Số lượng</FormLabel>\r\n                                  <FormControl>\r\n                                    <div className=\"flex items-center space-x-2\">\r\n                                      <Input\r\n                                        {...field}\r\n                                        disabled={mode === 'view'}\r\n                                        type=\"number\"\r\n                                        step=\"0.0001\"\r\n                                        placeholder=\"Nhập số lượng\"\r\n                                        value={value === null ? '' : value}\r\n                                        onChange={(e) => {\r\n                                          const val = e.target.value === '' ? 0 : Number(e.target.value);\r\n                                          onChange(val);\r\n\r\n                                          // Kiểm tra giới hạn số lượng cho lệnh bán\r\n                                          const orderType = form.watch('type');\r\n                                          const tokenInfo = form.getValues(`silverItems.${index}.productInfo`);\r\n\r\n                                          if (orderType === 'sell' && tokenInfo?.amount) {\r\n                                            // Nếu nhập số lượng lớn hơn số hiện có\r\n                                            if (val > tokenInfo.amount) {\r\n                                              toast.warning(`Số lượng không thể vượt quá ${tokenInfo.amount}g`);\r\n                                              // Đặt về số lượng tối đa có thể\r\n                                              setTimeout(() => {\r\n                                                onChange(tokenInfo.amount);\r\n                                              }, 100);\r\n                                            }\r\n                                          }\r\n                                        }}\r\n                                      />\r\n                                    </div>\r\n                                  </FormControl>\r\n\r\n                                  <FormMessage />\r\n                                </FormItem>\r\n                              )}\r\n                            />\r\n                          </div>\r\n                        </div>\r\n                      ))}\r\n\r\n                      {(!form.watch('silverItems') || form.watch('silverItems').length === 0) && (\r\n                        <div className=\"text-center p-4 border border-dashed rounded-md\">\r\n                          <p className=\"text-muted-foreground\">Chưa có bạc nào được thêm vào lệnh</p>\r\n                          {mode !== 'view' && (\r\n                            <Button\r\n                              type=\"button\"\r\n                              variant=\"outline\"\r\n                              size=\"sm\"\r\n                              className=\"mt-2\"\r\n                              onClick={() => {\r\n                                form.setValue('silverItems', [\r\n                                  { productId: '', volume: 0, productInfo: null }\r\n                                ]);\r\n                              }}\r\n                            >\r\n                              Thêm bạc\r\n                            </Button>\r\n                          )}\r\n                        </div>\r\n                      )}\r\n                    </div>\r\n                  </div>\r\n\r\n                  {/* Order Summary */}\r\n                  <div className=\"rounded-md border p-4 space-y-4 bg-gray-50\">\r\n                    <h3 className=\"font-medium mb-2\">Tóm tắt lệnh</h3>\r\n\r\n                    <div className=\"space-y-2\">\r\n                      <div className=\"grid grid-cols-2 gap-2\">\r\n                        <div className=\"text-muted-foreground\">Loại lệnh:</div>\r\n                        <div className={`font-medium ${form.watch('type') === 'buy' ? 'text-green-600' : 'text-red-600'}`}>\r\n                          {form.watch('type') === 'buy' ? 'Mua' : 'Bán'}\r\n                        </div>\r\n                      </div>\r\n\r\n                      <div className=\"grid grid-cols-2 gap-2\">\r\n                        <div className=\"text-muted-foreground\">Tổng khối lượng:</div>\r\n                        <div className=\"font-medium\">\r\n                          {form.watch('silverItems')?.reduce((sum, item) => {\r\n                            if (!item.productId || !item.volume || !item.productInfo?.weight) return sum;\r\n                            return sum + (item.volume * item.productInfo.weight);\r\n                          }, 0).toFixed(2)} oz\r\n                        </div>\r\n                      </div>\r\n\r\n                      {form.watch('type') === 'buy' ? (\r\n                        <>\r\n                          {form.watch('businessType') === BusinessType.NORMAL ? (\r\n                            <>\r\n                              <div className=\"grid grid-cols-2 gap-2\">\r\n                                <div className=\"text-muted-foreground\">Thành tiền:</div>\r\n                                <div className=\"font-medium\">\r\n                                  {formatCurrency(calculateTotal(\r\n                                    form.watch('silverItems'),\r\n                                    fixedPrice?.vnd || 0,\r\n                                    getOrderTypeForCalculation(form.watch('type')),\r\n                                    getBusinessTypeForCalculation(form.watch('businessType'))\r\n                                  ))}\r\n                                </div>\r\n                              </div>\r\n\r\n                              <div className=\"grid grid-cols-2 gap-2\">\r\n                                <div className=\"text-muted-foreground\">Đặt cọc (10%):</div>\r\n                                <div className=\"font-medium\">\r\n                                  {formatCurrency(calculateDeposit(\r\n                                    form.watch('silverItems'),\r\n                                    fixedPrice?.vnd || 0,\r\n                                    getOrderTypeForCalculation(form.watch('type')),\r\n                                    getBusinessTypeForCalculation(form.watch('businessType'))\r\n                                  ))}\r\n                                </div>\r\n                              </div>\r\n                            </>\r\n                          ) : (\r\n                            <>\r\n                              <div className=\"grid grid-cols-2 gap-2\">\r\n                                <div className=\"text-muted-foreground\">Phí gia công:</div>\r\n                                <div className=\"font-medium\">\r\n                                  {formatCurrency(form.watch('silverItems')?.reduce((sum, item) => {\r\n                                    if (!item.volume || !item.productInfo?.weight) return sum;\r\n                                    return sum + calculateProcessingFee(item.productInfo.weight * item.volume);\r\n                                  }, 0) || 0)}\r\n                                </div>\r\n                              </div>\r\n\r\n                              <div className=\"grid grid-cols-2 gap-2\">\r\n                                <div className=\"text-muted-foreground\">Thành tiền:</div>\r\n                                <div className=\"font-medium\">\r\n                                  {formatCurrency(calculateTotal(\r\n                                    form.watch('silverItems'),\r\n                                    fixedPrice?.vnd || 0,\r\n                                    getOrderTypeForCalculation(form.watch('type')),\r\n                                    getBusinessTypeForCalculation(form.watch('businessType'))\r\n                                  ))}\r\n                                </div>\r\n                              </div>\r\n\r\n                              {/* Kiểm tra và hiển thị cảnh báo nếu khối lượng < 10 */}\r\n                              {form.watch('silverItems')?.reduce((sum, item) => {\r\n                                if (!item.productId || !item.volume || !item.productInfo?.weight) return sum;\r\n                                return sum + (item.volume * item.productInfo.weight);\r\n                              }, 0) < 10 && (\r\n                                  <div className=\"col-span-2 mt-2 p-2 bg-red-50 text-red-600 text-sm rounded\">\r\n                                    Lệnh bạc giao ngay yêu cầu tối thiểu 10 oz bạc\r\n                                  </div>\r\n                                )}\r\n                            </>\r\n                          )}\r\n                        </>\r\n                      ) : (\r\n                        <>\r\n                          <div className=\"grid grid-cols-2 gap-2\">\r\n                            <div className=\"text-muted-foreground\">Thành tiền:</div>\r\n                            <div className=\"font-medium\">\r\n                              {formatCurrency(calculateTotal(\r\n                                form.watch('silverItems'),\r\n                                fixedPrice?.vnd || 0,\r\n                                getOrderTypeForCalculation(form.watch('type')),\r\n                                getBusinessTypeForCalculation(form.watch('businessType'))\r\n                              ))}\r\n                            </div>\r\n                          </div>\r\n\r\n                          <div className=\"grid grid-cols-2 gap-2\">\r\n                            <div className=\"text-muted-foreground\">Đặt cọc (10%):</div>\r\n                            <div className=\"font-medium\">\r\n                              {formatCurrency(calculateDeposit(\r\n                                form.watch('silverItems'),\r\n                                fixedPrice?.vnd || 0,\r\n                                getOrderTypeForCalculation(form.watch('type')),\r\n                                getBusinessTypeForCalculation(form.watch('businessType'))\r\n                              ))}\r\n                            </div>\r\n                          </div>\r\n                        </>\r\n                      )}\r\n                    </div>\r\n                  </div>\r\n                </form>\r\n              </Form>\r\n            </div>\r\n          )}\r\n\r\n          {/* Footer cố định bên ngoài scroll */}\r\n          {isFormReady && (\r\n            <DialogFooter className=\"flex-shrink-0 mt-4 border-t pt-4\">\r\n              <Button\r\n                type=\"button\"\r\n                variant=\"outline\"\r\n                onClick={handleClose}\r\n                disabled={isLoading}\r\n              >\r\n                {mode === 'view' ? 'Đóng' : 'Hủy'}\r\n              </Button>\r\n              {mode !== 'view' && (\r\n                <Button\r\n                  type=\"button\"\r\n                  className={`${form.watch('type') === 'buy' ? 'bg-green-600 hover:bg-green-700' : 'bg-red-600 hover:bg-red-700'}`}\r\n                  disabled={isLoading}\r\n                  onClick={() => handleSubmit(form.getValues())}\r\n                >\r\n                  {isLoading ? (\r\n                    <>\r\n                      <Loader2 className=\"mr-2 h-4 w-4 animate-spin\" />\r\n                      Đang xử lý...\r\n                    </>\r\n                  ) : (\r\n                    <>\r\n                      {form.watch('type') === 'buy' ? (\r\n                        <>\r\n                          <TrendingUp className=\"mr-2 h-4 w-4\" />\r\n                          Khớp lệnh mua\r\n                        </>\r\n                      ) : (\r\n                        <>\r\n                          <TrendingDown className=\"mr-2 h-4 w-4\" />\r\n                          Khớp lệnh bán\r\n                        </>\r\n                      )}\r\n                    </>\r\n                  )}\r\n                </Button>\r\n              )}\r\n            </DialogFooter>\r\n          )}\r\n        </DialogContent>\r\n      </Dialog>\r\n\r\n      <AlertDialog open={showConfirmDialog} onOpenChange={setShowConfirmDialog}>\r\n        <AlertDialogContent>\r\n          <AlertDialogHeader>\r\n            <AlertDialogTitle>Xác nhận hủy</AlertDialogTitle>\r\n            <AlertDialogDescription>\r\n              Bạn có thay đổi chưa lưu. Bạn có chắc chắn muốn hủy không?\r\n            </AlertDialogDescription>\r\n          </AlertDialogHeader>\r\n          <AlertDialogFooter>\r\n            <AlertDialogCancel>Tiếp tục chỉnh sửa</AlertDialogCancel>\r\n            <AlertDialogAction\r\n              onClick={() => {\r\n                onClose();\r\n                setShowConfirmDialog(false);\r\n              }}\r\n              className='bg-destructive hover:bg-destructive/90'\r\n            >\r\n              Hủy thay đổi\r\n            </AlertDialogAction>\r\n          </AlertDialogFooter>\r\n        </AlertDialogContent>\r\n      </AlertDialog>\r\n    </>\r\n  );\r\n}\r\n\r\n\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AACA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AAEA;AAUA;AACA;AAQA;AASA;AACA;AASA;AACA;AAQA;AAGA;;;AA7DA;;;;;;;;;;;;;;;;;;;;AAkFA,kDAAkD;AAClD,MAAM,iBAAiB;IACrB,WAAW,4KAAA,CAAA,cAAW,CAAC,SAAS;IAChC,WAAW,4KAAA,CAAA,cAAW,CAAC,SAAS;IAChC,YAAY,4KAAA,CAAA,cAAW,CAAC,UAAU;IAClC,WAAW,4KAAA,CAAA,cAAW,CAAC,SAAS;AAClC;AAEA,kDAAkD;AAElD,qCAAqC;AACrC,MAAM,mBAAmB,wLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IAChC,WAAW,wLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IAC7B,QAAQ,wLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,QAAQ;IAC/B,aAAa,wLAAA,CAAA,IAAC,CAAC,GAAG,GAAG,QAAQ;AAC/B;AAEA,MAAM,aAAa,wLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IAC1B,QAAQ,wLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IAC1B,MAAM,wLAAA,CAAA,IAAC,CAAC,IAAI,CAAC;QAAC;QAAO;QAAQ;KAAa;IAC1C,cAAc,wLAAA,CAAA,IAAC,CAAC,UAAU,CAAC,4KAAA,CAAA,eAAY,EAAE,OAAO,CAAC,4KAAA,CAAA,eAAY,CAAC,MAAM;IACpE,OAAO,wLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,GAAG,OAAO,CAAC;IACrC,aAAa,wLAAA,CAAA,IAAC,CAAC,KAAK,CAAC,kBAAkB,GAAG,CAAC,GAAG;IAC9C,iBAAiB,wLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,GAAG,OAAO,CAAC;IAC/C,QAAQ,wLAAA,CAAA,IAAC,CAAC,IAAI,CAAC;QACb,4KAAA,CAAA,cAAW,CAAC,SAAS;QACrB,4KAAA,CAAA,cAAW,CAAC,SAAS;QACrB,4KAAA,CAAA,cAAW,CAAC,UAAU;QACtB,4KAAA,CAAA,cAAW,CAAC,SAAS;KACtB,EAA6C,OAAO,CAAC,4KAAA,CAAA,cAAW,CAAC,SAAS;AAC7E;AAKO,SAAS,mBAAmB,EAAE,MAAM,EAAE,OAAO,EAAE,SAAS,EAAE,IAAI,EAAE,SAAS,EAAE,YAAY,EAAE,aAAa,KAAK,EAAE,mBAAmB,KAAK,EAAE,aAAa,IAAI,EAA2B;;IACxL,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,sQAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,sQAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,sQAAA,CAAA,WAAQ,AAAD,EAAE;IAC3D,MAAM,cAAc,CAAA,GAAA,sQAAA,CAAA,SAAM,AAAD,EAAE;IAC3B,MAAM,UAAU,CAAA,GAAA,sQAAA,CAAA,SAAM,AAAD,EAAmB;IACxC,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,wHAAA,CAAA,UAAO,AAAD,KAAK,0CAA0C;IAEtE,4CAA4C;IAC5C,MAAM,OAAO,CAAA,GAAA,0PAAA,CAAA,UAAO,AAAD,EAAc;QAC/B,UAAU,CAAA,GAAA,wQAAA,CAAA,cAAW,AAAD,EAAE;QACtB,eAAe;YACb,QAAQ;YACR,MAAM;YACN,cAAc,4KAAA,CAAA,eAAY,CAAC,MAAM;YACjC,OAAO;YACP,aAAa,EAAE;YACf,iBAAiB;YACjB,QAAQ;QACV;IACF;IAEA,6BAA6B;IAC7B,CAAA,GAAA,sQAAA,CAAA,YAAS,AAAD;wCAAE;YACR,MAAM,eAAe,KAAK,KAAK;6DAAC;oBAC9B,YAAY,OAAO,GAAG,KAAK,SAAS,CAAC,OAAO;gBAC9C;;YACA;gDAAO,IAAM,aAAa,WAAW;;QACvC;uCAAG;QAAC;QAAM,KAAK,KAAK;KAAC;IAErB,kBAAkB;IAClB,MAAM,cAAc;QAClB,IAAI,SAAS,UAAU,YAAY,OAAO,EAAE;YAC1C,qBAAqB;QACvB,OAAO;YACL;QACF;IACF;IAEA,uBAAuB;IACvB,MAAM,aAAa,OAAO;QACxB,IAAI;YACF,MAAM,WAAW,MAAM,6GAAA,CAAA,MAAG,CAAC,GAAG,CAA0B,CAAC,6BAA6B,EAAE,mBAAmB,SAAS;YACpH,MAAM,QAAQ,UAAU,QAAQ,EAAE;YAClC,OAAO,MAAM,GAAG,CAAC,CAAA,OAAQ,CAAC;oBACxB,IAAI,KAAK,EAAE;oBACX,UAAU,KAAK,QAAQ;oBACvB,UAAU,KAAK,QAAQ;oBACvB,OAAO,KAAK,KAAK;gBACnB,CAAC;QACH,EAAE,OAAO,OAAO;YACd,OAAO,EAAE;QACX;IACF;IAEA,0BAA0B;IAC1B,MAAM,gBAAgB,OAAO;QAC3B,IAAI;YACF,MAAM,WAAW,MAAM,6GAAA,CAAA,MAAG,CAAC,GAAG,CAAsC,CAAC,qCAAqC,EAAE,mBAAmB,SAAS;YACxI,MAAM,WAAW,UAAU,QAAQ,EAAE;YACrC,OAAO,SAAS,GAAG,CAAC,CAAA,UAAW,CAAC;oBAC9B,IAAI,QAAQ,EAAE;oBACd,aAAa,QAAQ,WAAW;oBAChC,aAAa,QAAQ,WAAW;oBAChC,QAAQ,OAAO,QAAQ,MAAM,KAAK,WAAW,WAAW,QAAQ,MAAM,IAAK,QAAQ,MAAM,IAAI;gBAC/F,CAAC;QACH,EAAE,OAAO,OAAO;YACd,OAAO,EAAE;QACX;IACF;IAEA,iEAAiE;IACjE,CAAA,GAAA,sQAAA,CAAA,YAAS,AAAD;wCAAE;YACR,IAAI,CAAC,QAAQ;gBACX,4BAA4B;gBAC5B;oDAAW;wBACT,KAAK,KAAK;wBACV,YAAY,OAAO,GAAG;wBACtB,eAAe;oBACjB;mDAAG,MAAM,8BAA8B;YACzC,OAAO;gBACL,uCAAuC;gBACvC,eAAe;gBAEf;oDAAW;wBACT,IAAI,aAAa,CAAC,SAAS,YAAY,SAAS,MAAM,GAAG;4BACvD,oDAAoD;4BACpD,oEAAoE;4BACpE,IAAI,WAAW,AAAC,UAAkB,IAAI;4BACtC,IAAI,CAAC,UAAU;gCACb,IAAI,UAAU,SAAS,KAAK,4KAAA,CAAA,YAAS,CAAC,GAAG,EAAE;oCACzC,WAAW;gCACb,OAAO,IAAI,UAAU,SAAS,KAAK,4KAAA,CAAA,YAAS,CAAC,IAAI,EAAE;oCACjD,WAAW;gCACb,OAAO;oCACL,WAAW,OAAO,0CAA0C;gCAC9D;4BACF;4BAEA,mDAAmD;4BACnD,MAAM,cAAc,UAAU,OAAO,EAAE,SAAS,IAC5C,UAAU,OAAO,CAAC,GAAG;gEAAC,CAAA,SAAU,CAAC;wCACjC,WAAW,OAAO,SAAS;wCAC3B,UAAU,OAAO,QAAQ,IAAI;wCAC7B,aAAa,OAAO,OAAO,GAAG;4CAC5B,IAAI,OAAO,OAAO,CAAC,EAAE;4CACrB,aAAa,OAAO,OAAO,CAAC,WAAW;4CACvC,aAAa,OAAO,OAAO,CAAC,WAAW;4CACvC,QAAQ,OAAO,OAAO,CAAC,MAAM,IAAI;wCACnC,IAAI;oCACN,CAAC;iEACC,EAAE;4BAEN,MAAM,WAAW;gCACf,IAAI,UAAU,EAAE;gCAChB,QAAQ,UAAU,MAAM;gCACxB,MAAM;gCACN,uCAAuC;gCACvC,OAAO;gCACP,aAAa;gCACb,+DAA+D;gCAC/D,iBAAiB;gCACjB,QAAQ,UAAU,MAAM;gCACxB,cAAc,UAAU,YAAY,IAAI,4KAAA,CAAA,eAAY,CAAC,MAAM;4BAC7D;4BAEA,oCAAoC;4BACpC,KAAK,KAAK,CAAC;4BACX,YAAY,OAAO,GAAG;wBACxB,OAAO,IAAI,UAAU,SAAS,UAAU;4BACtC,8CAA8C;4BAC9C,kEAAkE;4BAClE,MAAM,SAAS,cAAc,OAAO,KAAK,EAAE,GAAG;4BAE9C,IAAI,cAAc;gCAChB,kDAAkD;gCAClD,KAAK,KAAK,CAAC;oCACT,QAAQ;oCACR,MAAM;oCACN,OAAO;oCACP,aAAa;wCACX;4CACE,WAAW,aAAa,EAAE,IAAI;4CAC9B,QAAQ,qBAAqB,QAAQ,IAAK,aAAa,MAAM,IAAI;4CACjE,aAAa;gDACX,IAAI,aAAa,EAAE;gDACnB,WAAW,aAAa,SAAS;gDACjC,WAAW,aAAa,SAAS;gDACjC,IAAI,aAAa,EAAE,IAAI;gDACvB,QAAQ,aAAa,MAAM,IAAI;4CACjC;wCACF;qCACD;oCACD,iBAAiB;oCACjB,QAAQ;gCACV;4BACF,OAAO;gCACL,+CAA+C;gCAC/C,KAAK,KAAK,CAAC;oCACT,QAAQ;oCACR,MAAM;oCACN,OAAO;oCACP,aAAa;wCACX;4CAAE,WAAW;4CAAI,QAAQ;4CAAG,aAAa;wCAAK;qCAC/C;oCACD,iBAAiB;oCACjB,QAAQ;gCACV;4BACF;4BACA,YAAY,OAAO,GAAG;wBACxB;wBAEA,4BAA4B;wBAC5B,eAAe;oBACjB;mDAAG;YACL;QACF;uCAAG;QAAC;QAAQ;QAAW;QAAM;QAAM;QAAc;KAAiB;IAElE,kDAAkD;IAClD,CAAA,GAAA,sQAAA,CAAA,YAAS,AAAD;wCAAE;YACR,MAAM,eAAe,KAAK,KAAK;6DAAC,CAAC,GAAG,EAAE,IAAI,EAAE;oBAC1C,8DAA8D;oBAC9D,IAAI,SAAS,UAAU,SAAS,UAAU;wBACxC,wBAAwB;wBACxB;yEAAW;gCACT,KAAK,QAAQ,CAAC,eAAe;oCAAC;wCAAE,WAAW;wCAAI,QAAQ;wCAAG,aAAa;oCAAK;iCAAE,EAAE;oCAAE,aAAa;gCAAK;4BACtG;wEAAG;oBACL;gBACF;;YACA;gDAAO,IAAM,aAAa,WAAW;;QACvC;uCAAG;QAAC;KAAK;IAET,wBAAwB;IACxB,MAAM,eAAe,OAAO;QAC1B,IAAI;YAEF,IAAI,SAAS,QAAQ;YAErB,aAAa;YAEb,IAAI,SAAS,UAAU;gBACrB,0CAA0C;gBAC1C,MAAM,gBAAgB,KAAK,WAAW,CAAC,IAAI,CAAC,CAAA,OAAQ,KAAK,SAAS,IAAI,KAAK,MAAM,GAAG;gBAEpF,IAAI,CAAC,eAAe;oBAClB,2QAAA,CAAA,QAAK,CAAC,KAAK,CAAC;oBACZ,aAAa;oBACb;gBACF;gBAEA,iDAAiD;gBACjD,MAAM,YAAY,KAAK,IAAI,KAAK,QAAQ,4KAAA,CAAA,YAAS,CAAC,GAAG,GAAG,4KAAA,CAAA,YAAS,CAAC,IAAI;gBAEtE,kCAAkC;gBAClC,MAAM,WAAW,KAAK,WAAW,CAAC,GAAG,CAAC,CAAA;oBACpC,kDAAkD;oBAClD,MAAM,gBAAgB,KAAK,WAAW,EAAE,UAAU;oBAClD,MAAM,eAAe,CAAA,GAAA,+HAAA,CAAA,8BAA2B,AAAD,EAAE,YAAY,OAAO,GAAG;oBAEvE,OAAO;wBACL,WAAW,KAAK,SAAS;wBACzB,OAAO;wBACP,UAAU,OAAO,KAAK,MAAM;oBAC9B;gBACF;gBAEA,sBAAsB;gBACtB,MAAM,gBAAgB;oBACpB,QAAQ,KAAK,MAAM;oBACnB;oBACA,cAAc,KAAK,YAAY;oBAC/B;oBACA,OAAO,YAAY,OAAO,EAAE,sBAAsB;gBACpD;gBAEA,uBAAuB;gBACvB,IAAI;oBACF,MAAM,WAAW,MAAM,6GAAA,CAAA,MAAG,CAAC,IAAI,CAAyB,gBAAgB;oBACxE,2QAAA,CAAA,QAAK,CAAC,OAAO,CAAC;oBACd,KAAK,KAAK;oBACV,YAAY,OAAO,GAAG;oBACtB;oBACA,IAAI,WAAW;gBACjB,EAAE,OAAO,OAAY;oBACnB,MAAM,eAAe,MAAM,QAAQ,EAAE,MAAM,WAAW;oBACtD,MAAM,mBAAmB,MAAM,QAAQ,EAAE,MAAM;oBAE/C,IAAI,kBAAkB;wBACpB,OAAO,MAAM,CAAC,kBAAkB,OAAO,CAAC,CAAC;4BACvC,2QAAA,CAAA,QAAK,CAAC,KAAK,CAAC,CAAC,KAAK,EAAE,SAAS;wBAC/B;oBACF,OAAO;wBACL,2QAAA,CAAA,QAAK,CAAC,KAAK,CAAC;oBACd;gBACF;YACF,OAAO,IAAI,SAAS,YAAY,WAAW,IAAI;gBAC7C,wCAAwC;gBACxC,+CAA+C;gBAC/C,IAAI,KAAK,WAAW,CAAC,MAAM,GAAG,GAAG;oBAC/B,yCAAyC;oBAEzC,iDAAiD;oBACjD,IAAI;oBACJ,IAAI,KAAK,IAAI,KAAK,OAAO;wBACvB,YAAY,4KAAA,CAAA,YAAS,CAAC,GAAG;oBAC3B,OAAO,IAAI,KAAK,IAAI,KAAK,QAAQ;wBAC/B,YAAY,4KAAA,CAAA,YAAS,CAAC,IAAI;oBAC5B,OAAO;wBACL,YAAY,4KAAA,CAAA,YAAS,CAAC,UAAU;oBAClC;oBAEA,wEAAwE;oBACxE,IAAI,KAAK,IAAI,KAAK,gBAAgB,CAAC,CAAC,KAAK,KAAK,IAAI,MAAM,OAAO,KAAK,KAAK,EAAE,GAAG;wBAC5E,2QAAA,CAAA,QAAK,CAAC,KAAK,CAAC;wBACZ,aAAa;wBACb;oBACF;oBAEA,kDAAkD;oBAClD,IAAI;oBACJ,IAAI;oBAEJ,IAAI,KAAK,IAAI,KAAK,cAAc;wBAC9B,8BAA8B;wBAC9B,yCAAyC;wBACzC,iBAAiB,KAAK,WAAW,CAAC,GAAG,CAAC,CAAA,OAAQ,CAAC;gCAC7C,WAAW,KAAK,SAAS;gCACzB,OAAO;gCACP,UAAU,OAAO,KAAK,MAAM;4BAC9B,CAAC;wBAED,qCAAqC;wBACrC,gBAAgB;4BACd,IAAI,UAAU,EAAE;4BAChB,QAAQ,KAAK,MAAM;4BACnB,WAAW;4BACX,QAAQ,KAAK,MAAM,CAAC,WAAW;4BAC/B,QAAQ;4BACR,WAAW,KAAK,MAAM;wBACxB;oBACF,OAAO;wBACL,8DAA8D;wBAC9D,yCAAyC;wBACzC,iBAAiB,KAAK,WAAW,CAAC,GAAG,CAAC,CAAA;4BACpC,kDAAkD;4BAClD,MAAM,gBAAgB,KAAK,WAAW,EAAE,UAAU;4BAClD,MAAM,eAAe,CAAA,GAAA,+HAAA,CAAA,8BAA2B,AAAD,EAAE,OAAO,KAAK,KAAK,GAAG;4BAErE,OAAO;gCACL,WAAW,KAAK,SAAS;gCACzB,OAAO;gCACP,UAAU,OAAO,KAAK,MAAM;4BAC9B;wBACF;wBAEA,6DAA6D;wBAC7D,MAAM,uBAAuB,eAAe,MAAM,CAAC,CAAC,KAAK,UAAY,MAAO,QAAQ,KAAK,GAAG,QAAQ,QAAQ,EAAG;wBAE/G,qCAAqC;wBACrC,IAAI,KAAK,YAAY,KAAK,4KAAA,CAAA,eAAY,CAAC,MAAM,EAAE;4BAC7C,4DAA4D;4BAC5D,MAAM,sBAAsB,uBAAuB;4BAEnD,oDAAoD;4BACpD,gBAAgB;gCACd,IAAI,UAAU,EAAE;gCAChB,QAAQ,KAAK,MAAM;gCACnB,WAAW;gCACX,cAAc,4KAAA,CAAA,eAAY,CAAC,MAAM;gCACjC,QAAQ,KAAK,MAAM,CAAC,WAAW;gCAC/B,QAAQ;gCACR,eAAe,oBAAoB,OAAO,CAAC;gCAC3C,WAAW,KAAK,MAAM;4BACxB;wBACF,OAAO;4BACL,yCAAyC;4BACzC,uDAAuD;4BACvD,MAAM,cAAc,KAAK,WAAW,CAAC,MAAM,CAAC,CAAC,KAAK,OAAS,MAAM,OAAO,KAAK,MAAM,GAAG;4BAEtF,IAAI,cAAc,IAAI;gCACpB,2QAAA,CAAA,QAAK,CAAC,KAAK,CAAC;gCACZ,aAAa;gCACb;4BACF;4BAEA,2DAA2D;4BAC3D,gBAAgB;gCACd,IAAI,UAAU,EAAE;gCAChB,QAAQ,KAAK,MAAM;gCACnB,WAAW;gCACX,cAAc,4KAAA,CAAA,eAAY,CAAC,kBAAkB;gCAC7C,QAAQ,KAAK,MAAM,CAAC,WAAW;gCAC/B,QAAQ;gCACR,sEAAsE;gCACtE,iEAAiE;gCACjE,WAAW,KAAK,MAAM;4BACxB;wBACF;oBACF;oBAGA,wBAAwB;oBACxB,IAAI;wBACF,MAAM,6GAAA,CAAA,MAAG,CAAC,GAAG,CAAyB,CAAC,aAAa,EAAE,UAAU,EAAE,EAAE,EAAE;wBACtE,2QAAA,CAAA,QAAK,CAAC,OAAO,CAAC;wBACd,YAAY,OAAO,GAAG;wBACtB;wBACA,IAAI,WAAW;oBACjB,EAAE,OAAO,OAAY;wBACnB,MAAM,eAAe,MAAM,QAAQ,EAAE,MAAM,WAAW;wBACtD,MAAM,mBAAmB,MAAM,QAAQ,EAAE,MAAM;wBAE/C,IAAI,kBAAkB;4BACpB,OAAO,MAAM,CAAC,kBAAkB,OAAO,CAAC,CAAC;gCACvC,2QAAA,CAAA,QAAK,CAAC,KAAK,CAAC,CAAC,KAAK,EAAE,SAAS;4BAC/B;wBACF,OAAO;4BACL,2QAAA,CAAA,QAAK,CAAC,KAAK,CAAC;wBACd;oBACF;gBACF,OAAO;oBACL,2QAAA,CAAA,QAAK,CAAC,KAAK,CAAC;gBACd;YACF;QACF,EAAE,OAAO,OAAY;YACnB,2QAAA,CAAA,QAAK,CAAC,KAAK,CAAC,MAAM,QAAQ,EAAE,MAAM,WAAW;QAC/C,SAAU;YACR,aAAa;QACf;IACF;IAEA,kBAAkB;IAClB,MAAM,iBAAiB,CAAC,OAAkC,SAAkB;QAC1E,+CAA+C;QAC/C,IAAI,UAAU,aAAa,UAAU,MAAM,OAAO;QAElD,4BAA4B;QAC5B,MAAM,KAAK,WAAW;QACtB,MAAM,MAAM,UAAU;QAEtB,qCAAqC;QACrC,MAAM,QAAQ,CAAA,GAAA,+HAAA,CAAA,8BAA2B,AAAD,EAAE,OAAO;QAEjD,4CAA4C;QAC5C,MAAM,aAAa,QAAQ;QAE3B,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;YAEpC,uBAAuB;QACzB,GAAG,MAAM,CAAC;IACZ;IAEA,MAAM,QAAQ;QACZ,QAAQ;QACR,QAAQ;QACR,MAAM;IACR,CAAC,CAAC,KAAK;IAEP,MAAM,cAAc;QAClB,QAAQ;QACR,QAAQ;QACR,MAAM;IACR,CAAC,CAAC,KAAK;IAEP,4CAA4C;IAC5C,CAAA,GAAA,sQAAA,CAAA,YAAS,AAAD;wCAAE;YACR,IAAI,YAAY;gBACd,KAAK,QAAQ,CAAC,SAAS,WAAW,GAAG;YACvC;QACF;uCAAG;QAAC;QAAY;KAAK;IAErB,gDAAgD;IAChD,MAAM,6BAA6B,CAAC;QAClC,OAAO,SAAS,QAAQ,QAAQ;IAClC;IAEA,8CAA8C;IAC9C,MAAM,gCAAgC,CAAC;QACrC,OAAO;IACT;IAEA,4CAA4C;IAC5C,MAAM,eAAe,CAAA,GAAA,sQAAA,CAAA,UAAO,AAAD;oDAAE;YAC3B,IAAI,CAAC,YAAY,OAAO;YAExB,OAAO;gBACL,KAAK,KAAK,KAAK,CAAC,YAAY,QAAQ,WAAW,GAAG,GAAG,WAAW,GAAG,GAAG;gBACtE,KAAK,KAAK,KAAK,CAAC,YAAY,QAAQ,WAAW,GAAG,GAAG,WAAW,GAAG,GAAG;YACxE;QACF;mDAAG;QAAC;QAAY,KAAK,KAAK,CAAC;KAAQ;IAEnC,qBACE;;0BACE,sSAAC,8HAAA,CAAA,SAAM;gBAAC,MAAM;gBAAQ,cAAc,CAAC,OAAS,CAAC,QAAQ;0BACrD,cAAA,sSAAC,8HAAA,CAAA,gBAAa;oBAAC,WAAU;;sCACvB,sSAAC,8HAAA,CAAA,eAAY;4BAAC,WAAU;;8CACtB,sSAAC,8HAAA,CAAA,cAAW;oCAAC,WAAU;8CACpB,KAAK,KAAK,CAAC,YAAY,sBACtB;;0DACE,sSAAC,ySAAA,CAAA,aAAU;gDAAC,WAAU;;;;;;0DACtB,sSAAC;gDAAK,WAAU;0DAAiB;;;;;;;qEAGnC;;0DACE,sSAAC,6SAAA,CAAA,eAAY;gDAAC,WAAU;;;;;;0DACxB,sSAAC;gDAAK,WAAU;0DAAe;;;;;;;;;;;;;8CAIrC,sSAAC,8HAAA,CAAA,oBAAiB;8CACf;;;;;;;;;;;;wBAIJ,CAAC,4BACA,sSAAC;4BAAI,WAAU;;8CACb,sSAAC,wSAAA,CAAA,UAAO;oCAAC,WAAU;;;;;;8CACnB,sSAAC;oCAAE,WAAU;8CAAgC;;;;;;;;;;;iDAG/C,sSAAC;4BAAI,WAAU;sCACb,cAAA,sSAAC,4HAAA,CAAA,OAAI;gCAAE,GAAG,IAAI;0CACZ,cAAA,sSAAC;oCACC,KAAK;oCACL,UAAU,KAAK,YAAY,CAAC;oCAC5B,WAAU;;sDAEV,sSAAC;4CAAI,WAAU;;gDAEZ,CAAC,2BACA,sSAAC,4HAAA,CAAA,YAAS;oDACR,SAAS,KAAK,OAAO;oDACrB,MAAK;oDACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,sSAAC,4HAAA,CAAA,WAAQ;;8EACP,sSAAC,4HAAA,CAAA,YAAS;8EAAC;;;;;;8EACX,sSAAC,4HAAA,CAAA,cAAW;8EACV,cAAA,sSAAC,4IAAA,CAAA,mBAAgB;wEACf,OAAO,MAAM,KAAK;wEAClB,UAAU,MAAM,QAAQ;wEACxB,aAAY;wEACZ,mBAAkB;wEAClB,cAAc;wEACd,UAAU,SAAS;wEACnB,cAAc,CAAC,SAAW,SACxB,GAAG,OAAO,QAAQ,IAAI,OAAO,QAAQ,CAAC,IAAI,EAAE,OAAO,KAAK,CAAC,CAAC,CAAC,GAC3D;wEAEF,aAAa,CAAC,CAAC,MAAM,KAAK;;;;;;;;;;;8EAG9B,sSAAC,4HAAA,CAAA,cAAW;;;;;;;;;;;;;;;2DAKlB,uEAAuE;8DACvE,sSAAC;oDAAI,WAAU;;sEACb,sSAAC;4DAAI,WAAU;sEAAsB;;;;;;sEACrC,sSAAC;4DAAI,WAAU;;8EACb,sSAAC,yRAAA,CAAA,OAAI;oEAAC,WAAU;;;;;;8EAChB,sSAAC;8EAAM,MAAM,YAAY,MAAM,YAAY;;;;;;;;;;;;;;;;;;8DAMjD,sSAAC,4HAAA,CAAA,YAAS;oDACR,SAAS,KAAK,OAAO;oDACrB,MAAK;oDACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,sSAAC,4HAAA,CAAA,WAAQ;;8EACP,sSAAC,4HAAA,CAAA,YAAS;8EAAC;;;;;;8EACX,sSAAC,8HAAA,CAAA,SAAM;oEACL,UAAU,SAAS;oEACnB,eAAe,MAAM,QAAQ;oEAC7B,cAAc,MAAM,KAAK;oEACzB,OAAO,MAAM,KAAK;;sFAElB,sSAAC,4HAAA,CAAA,cAAW;sFACV,cAAA,sSAAC,8HAAA,CAAA,gBAAa;gFAAC,WAAU;0FACvB,cAAA,sSAAC,8HAAA,CAAA,cAAW;oFAAC,aAAY;;;;;;;;;;;;;;;;sFAG7B,sSAAC,8HAAA,CAAA,gBAAa;;8FACZ,sSAAC,8HAAA,CAAA,aAAU;oFAAC,OAAM;8FAAM;;;;;;8FACxB,sSAAC,8HAAA,CAAA,aAAU;oFAAC,OAAM;8FAAO;;;;;;;;;;;;;;;;;;8EAI7B,sSAAC,4HAAA,CAAA,cAAW;;;;;;;;;;;;;;;;;;;;;;sDAOpB,sSAAC;4CAAI,WAAW,CAAA,GAAA,+HAAA,CAAA,KAAE,AAAD,EACf,kBACA,KAAK,KAAK,CAAC,YAAY,QAAQ,gBAAgB;sDAE/C,cAAA,sSAAC;gDAAI,WAAU;;kEACb,sSAAC;wDAAI,WAAU;;0EACb,sSAAC;gEAAI,WAAW,CAAC,oDAAoD,EAAE,KAAK,KAAK,CAAC,YAAY,QAAQ,iBAAiB,cAAc;0EAClI,KAAK,KAAK,CAAC,YAAY,sBACtB,sSAAC,ySAAA,CAAA,aAAU;oEAAC,WAAW,CAAC,sBAAsB,CAAC;;;;;yFAE/C,sSAAC,6SAAA,CAAA,eAAY;oEAAC,WAAW,CAAC,oBAAoB,CAAC;;;;;;;;;;;0EAGnD,sSAAC;0EACC,cAAA,sSAAC;oEAAE,WAAW,CAAC,oBAAoB,EAAE,KAAK,KAAK,CAAC,YAAY,QAAQ,mBAAmB,gBAAgB;;wEAAE;wEAClG,KAAK,KAAK,CAAC,YAAY,QAAQ,QAAQ;;;;;;;;;;;;;;;;;;kEAIlD,sSAAC;wDAAI,WAAU;;0EACb,sSAAC;gEAAE,WAAW,CAAC,kBAAkB,EAAE,KAAK,KAAK,CAAC,YAAY,QAAQ,mBAAmB,gBAAgB;;oEAAE;oEACnG,cAAc,KAAK,QAAQ,MAAM;;;;;;;0EAErC,sSAAC;gEAAE,WAAW,CAAC,sBAAsB,EAAE,KAAK,KAAK,CAAC,YAAY,QAAQ,mBAAmB,gBAAgB;;oEACtG,eAAe,cAAc,OAAO;oEAAG;;;;;;;;;;;;;;;;;;;;;;;;wCAO/C,KAAK,KAAK,CAAC,YAAY,uBACtB,sSAAC,4HAAA,CAAA,YAAS;4CACR,SAAS,KAAK,OAAO;4CACrB,MAAK;4CACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,sSAAC,4HAAA,CAAA,WAAQ;oDAAC,WAAU;;sEAClB,sSAAC,4HAAA,CAAA,YAAS;sEAAC;;;;;;sEACX,sSAAC,4HAAA,CAAA,cAAW;sEACV,cAAA,sSAAC,wJAAA,CAAA,YAAS;gEACR,eAAe,MAAM,QAAQ;gEAC7B,OAAO,MAAM,KAAK;gEAClB,UAAU,SAAS;gEACnB,WAAU;;kFAEV,sSAAC,wJAAA,CAAA,gBAAa;wEAAC,OAAO,4KAAA,CAAA,eAAY,CAAC,MAAM;;0FACvC,sSAAC,wJAAA,CAAA,sBAAmB;;kGAClB,sSAAC,wJAAA,CAAA,qBAAkB;kGAAC;;;;;;kGACpB,sSAAC,wJAAA,CAAA,2BAAwB;kGAAC;;;;;;;;;;;;0FAI5B,sSAAC,wJAAA,CAAA,uBAAoB;0FACnB,cAAA,sSAAC,wJAAA,CAAA,yBAAsB;;;;;;;;;;;;;;;;kFAI3B,sSAAC,wJAAA,CAAA,gBAAa;wEAAC,OAAO,4KAAA,CAAA,eAAY,CAAC,kBAAkB;;0FACnD,sSAAC,wJAAA,CAAA,sBAAmB;;kGAClB,sSAAC,wJAAA,CAAA,qBAAkB;kGAAC;;;;;;kGACpB,sSAAC,wJAAA,CAAA,2BAAwB;kGAAC;;;;;;;;;;;;0FAI5B,sSAAC,wJAAA,CAAA,uBAAoB;0FACnB,cAAA,sSAAC,wJAAA,CAAA,yBAAsB;;;;;;;;;;;;;;;;;;;;;;;;;;;sEAK/B,sSAAC,4HAAA,CAAA,cAAW;;;;;;;;;;;;;;;;sDAQpB,sSAAC;4CAAI,WAAU;sDAmCZ,SAAS,yBACR,sSAAC,4HAAA,CAAA,YAAS;gDACR,SAAS,KAAK,OAAO;gDACrB,MAAK;gDACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,sSAAC,4HAAA,CAAA,WAAQ;;0EACP,sSAAC,4HAAA,CAAA,YAAS;0EAAC;;;;;;0EACX,sSAAC,8HAAA,CAAA,SAAM;gEACL,UAAU,SAAS;gEACnB,eAAe,MAAM,QAAQ;gEAC7B,cAAc,MAAM,KAAK;gEACzB,OAAO,MAAM,KAAK;;kFAElB,sSAAC,4HAAA,CAAA,cAAW;kFACV,cAAA,sSAAC,8HAAA,CAAA,gBAAa;sFACZ,cAAA,sSAAC,8HAAA,CAAA,cAAW;gFAAC,aAAY;;;;;;;;;;;;;;;;kFAG7B,sSAAC,8HAAA,CAAA,gBAAa;;0FACZ,sSAAC,8HAAA,CAAA,aAAU;gFAAC,OAAO,4KAAA,CAAA,cAAW,CAAC,SAAS;0FAAE;;;;;;0FAC1C,sSAAC,8HAAA,CAAA,aAAU;gFAAC,OAAO,4KAAA,CAAA,cAAW,CAAC,SAAS;0FAAE;;;;;;0FAC1C,sSAAC,8HAAA,CAAA,aAAU;gFAAC,OAAO,4KAAA,CAAA,cAAW,CAAC,UAAU;0FAAE;;;;;;;;;;;;;;;;;;0EAG/C,sSAAC,4HAAA,CAAA,kBAAe;0EAAC;;;;;;0EAGjB,sSAAC,4HAAA,CAAA,cAAW;;;;;;;;;;;;;;;uDAKlB,4DAA4D;0DAC5D,sSAAC;;;;;;;;;;sDAKL,sSAAC;4CAAI,WAAU;;8DACb,sSAAC;oDAAI,WAAU;;sEACb,sSAAC;4DAAG,WAAU;sEAAsB;;;;;;sEACpC,sSAAC,8HAAA,CAAA,SAAM;4DACL,MAAK;4DACL,SAAQ;4DACR,MAAK;4DACL,SAAS;gEACP,MAAM,eAAe,KAAK,SAAS,CAAC,kBAAkB,EAAE;gEACxD,KAAK,QAAQ,CAAC,eAAe;uEACxB;oEACH;wEAAE,WAAW;wEAAI,QAAQ;wEAAG,aAAa;oEAAK;iEAC/C;4DACH;4DACA,UAAU,SAAS;sEACpB;;;;;;;;;;;;8DAKH,sSAAC;oDAAI,WAAU;;wDACZ,KAAK,KAAK,CAAC,gBAAgB,IAAI,CAAC,GAAG,sBAClC,sSAAC;gEAAgB,WAAU;;oEACxB,SAAS,wBACR,sSAAC,8HAAA,CAAA,SAAM;wEACL,MAAK;wEACL,SAAQ;wEACR,MAAK;wEACL,WAAU;wEACV,SAAS;4EACP,MAAM,eAAe;mFAAI,KAAK,SAAS,CAAC;6EAAe;4EACvD,aAAa,MAAM,CAAC,OAAO;4EAC3B,KAAK,QAAQ,CAAC,eAAe;wEAC/B;kFACD;;;;;;kFAKH,sSAAC;wEAAI,WAAU;;0FAEb,sSAAC,4HAAA,CAAA,YAAS;gFACR,SAAS,KAAK,OAAO;gFACrB,MAAM,CAAC,YAAY,EAAE,MAAM,UAAU,CAAC;gFACtC,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,sSAAC,4HAAA,CAAA,WAAQ;;0GACP,sSAAC,4HAAA,CAAA,YAAS;0GAAC;;;;;;0GACX,sSAAC,4HAAA,CAAA,cAAW;0GACV,cAAA,sSAAC,4IAAA,CAAA,mBAAgB;oGACf,OAAO,MAAM,KAAK;oGAClB,UAAU,CAAC;wGACT,MAAM,QAAQ,CAAC;wGACf,qCAAqC;wGACrC,6GAAA,CAAA,MAAG,CAAC,GAAG,CAAkB,CAAC,cAAc,EAAE,OAAO,EAAE,IAAI,CAAC,CAAA;4GACtD,IAAI,UAAU;gHACZ,MAAM,SAAsB;oHAC1B,IAAI,SAAS,EAAE;oHACf,aAAa,SAAS,WAAW,IAAI;oHACrC,aAAa,SAAS,WAAW,IAAI;oHACrC,QAAQ,OAAO,SAAS,MAAM,KAAK,WAAW,WAAW,SAAS,MAAM,IAAK,SAAS,MAAM,IAAI;gHAClG;gHACA,MAAM,eAAe;uHAAI,KAAK,SAAS,CAAC;iHAAe;gHACvD,YAAY,CAAC,MAAM,CAAC,WAAW,GAAG;gHAClC,KAAK,QAAQ,CAAC,eAAe;4GAC/B;wGACF,GAAG,KAAK,CAAC,CAAA;4GACP,QAAQ,KAAK,CAAC,mCAAmC;wGACnD;oGACF;oGACA,aAAY;oGACZ,mBAAkB;oGAClB,cAAc;oGACd,UAAU,SAAS,UAAU,CAAC,KAAK,KAAK,CAAC;oGACzC,cAAc,CAAC;wGACb,IAAI,CAAC,QAAQ,OAAO;wGACpB,MAAM,gBAAgB;wGACtB,OAAO,GAAG,cAAc,WAAW,CAAC,GAAG,EAAE,cAAc,WAAW,CAAC,EAAE,EAAE,cAAc,MAAM,CAAC,IAAI,CAAC;oGACnG;oGACA,aAAa,CAAC,CAAC,MAAM,KAAK;;;;;;;;;;;0GAG9B,sSAAC,4HAAA,CAAA,cAAW;;;;;;;;;;;;;;;;0FAMlB,sSAAC,4HAAA,CAAA,YAAS;gFACR,SAAS,KAAK,OAAO;gFACrB,MAAM,CAAC,YAAY,EAAE,MAAM,OAAO,CAAC;gFACnC,QAAQ,CAAC,EAAE,OAAO,EAAE,KAAK,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,iBAC/C,sSAAC,4HAAA,CAAA,WAAQ;;0GACP,sSAAC,4HAAA,CAAA,YAAS;0GAAC;;;;;;0GACX,sSAAC,4HAAA,CAAA,cAAW;0GACV,cAAA,sSAAC;oGAAI,WAAU;8GACb,cAAA,sSAAC,6HAAA,CAAA,QAAK;wGACH,GAAG,KAAK;wGACT,UAAU,SAAS;wGACnB,MAAK;wGACL,MAAK;wGACL,aAAY;wGACZ,OAAO,UAAU,OAAO,KAAK;wGAC7B,UAAU,CAAC;4GACT,MAAM,MAAM,EAAE,MAAM,CAAC,KAAK,KAAK,KAAK,IAAI,OAAO,EAAE,MAAM,CAAC,KAAK;4GAC7D,SAAS;4GAET,0CAA0C;4GAC1C,MAAM,YAAY,KAAK,KAAK,CAAC;4GAC7B,MAAM,YAAY,KAAK,SAAS,CAAC,CAAC,YAAY,EAAE,MAAM,YAAY,CAAC;4GAEnE,IAAI,cAAc,UAAU,WAAW,QAAQ;gHAC7C,uCAAuC;gHACvC,IAAI,MAAM,UAAU,MAAM,EAAE;oHAC1B,2QAAA,CAAA,QAAK,CAAC,OAAO,CAAC,CAAC,4BAA4B,EAAE,UAAU,MAAM,CAAC,CAAC,CAAC;oHAChE,gCAAgC;oHAChC,WAAW;wHACT,SAAS,UAAU,MAAM;oHAC3B,GAAG;gHACL;4GACF;wGACF;;;;;;;;;;;;;;;;0GAKN,sSAAC,4HAAA,CAAA,cAAW;;;;;;;;;;;;;;;;;;;;;;;+DAvGZ;;;;;wDA+GX,CAAC,CAAC,KAAK,KAAK,CAAC,kBAAkB,KAAK,KAAK,CAAC,eAAe,MAAM,KAAK,CAAC,mBACpE,sSAAC;4DAAI,WAAU;;8EACb,sSAAC;oEAAE,WAAU;8EAAwB;;;;;;gEACpC,SAAS,wBACR,sSAAC,8HAAA,CAAA,SAAM;oEACL,MAAK;oEACL,SAAQ;oEACR,MAAK;oEACL,WAAU;oEACV,SAAS;wEACP,KAAK,QAAQ,CAAC,eAAe;4EAC3B;gFAAE,WAAW;gFAAI,QAAQ;gFAAG,aAAa;4EAAK;yEAC/C;oEACH;8EACD;;;;;;;;;;;;;;;;;;;;;;;;sDAUX,sSAAC;4CAAI,WAAU;;8DACb,sSAAC;oDAAG,WAAU;8DAAmB;;;;;;8DAEjC,sSAAC;oDAAI,WAAU;;sEACb,sSAAC;4DAAI,WAAU;;8EACb,sSAAC;oEAAI,WAAU;8EAAwB;;;;;;8EACvC,sSAAC;oEAAI,WAAW,CAAC,YAAY,EAAE,KAAK,KAAK,CAAC,YAAY,QAAQ,mBAAmB,gBAAgB;8EAC9F,KAAK,KAAK,CAAC,YAAY,QAAQ,QAAQ;;;;;;;;;;;;sEAI5C,sSAAC;4DAAI,WAAU;;8EACb,sSAAC;oEAAI,WAAU;8EAAwB;;;;;;8EACvC,sSAAC;oEAAI,WAAU;;wEACZ,KAAK,KAAK,CAAC,gBAAgB,OAAO,CAAC,KAAK;4EACvC,IAAI,CAAC,KAAK,SAAS,IAAI,CAAC,KAAK,MAAM,IAAI,CAAC,KAAK,WAAW,EAAE,QAAQ,OAAO;4EACzE,OAAO,MAAO,KAAK,MAAM,GAAG,KAAK,WAAW,CAAC,MAAM;wEACrD,GAAG,GAAG,QAAQ;wEAAG;;;;;;;;;;;;;wDAIpB,KAAK,KAAK,CAAC,YAAY,sBACtB;sEACG,KAAK,KAAK,CAAC,oBAAoB,4KAAA,CAAA,eAAY,CAAC,MAAM,iBACjD;;kFACE,sSAAC;wEAAI,WAAU;;0FACb,sSAAC;gFAAI,WAAU;0FAAwB;;;;;;0FACvC,sSAAC;gFAAI,WAAU;0FACZ,eAAe,CAAA,GAAA,+HAAA,CAAA,iBAAc,AAAD,EAC3B,KAAK,KAAK,CAAC,gBACX,YAAY,OAAO,GACnB,2BAA2B,KAAK,KAAK,CAAC,UACtC,8BAA8B,KAAK,KAAK,CAAC;;;;;;;;;;;;kFAK/C,sSAAC;wEAAI,WAAU;;0FACb,sSAAC;gFAAI,WAAU;0FAAwB;;;;;;0FACvC,sSAAC;gFAAI,WAAU;0FACZ,eAAe,CAAA,GAAA,+HAAA,CAAA,mBAAgB,AAAD,EAC7B,KAAK,KAAK,CAAC,gBACX,YAAY,OAAO,GACnB,2BAA2B,KAAK,KAAK,CAAC,UACtC,8BAA8B,KAAK,KAAK,CAAC;;;;;;;;;;;;;6FAMjD;;kFACE,sSAAC;wEAAI,WAAU;;0FACb,sSAAC;gFAAI,WAAU;0FAAwB;;;;;;0FACvC,sSAAC;gFAAI,WAAU;0FACZ,eAAe,KAAK,KAAK,CAAC,gBAAgB,OAAO,CAAC,KAAK;oFACtD,IAAI,CAAC,KAAK,MAAM,IAAI,CAAC,KAAK,WAAW,EAAE,QAAQ,OAAO;oFACtD,OAAO,MAAM,CAAA,GAAA,+HAAA,CAAA,yBAAsB,AAAD,EAAE,KAAK,WAAW,CAAC,MAAM,GAAG,KAAK,MAAM;gFAC3E,GAAG,MAAM;;;;;;;;;;;;kFAIb,sSAAC;wEAAI,WAAU;;0FACb,sSAAC;gFAAI,WAAU;0FAAwB;;;;;;0FACvC,sSAAC;gFAAI,WAAU;0FACZ,eAAe,CAAA,GAAA,+HAAA,CAAA,iBAAc,AAAD,EAC3B,KAAK,KAAK,CAAC,gBACX,YAAY,OAAO,GACnB,2BAA2B,KAAK,KAAK,CAAC,UACtC,8BAA8B,KAAK,KAAK,CAAC;;;;;;;;;;;;oEAM9C,KAAK,KAAK,CAAC,gBAAgB,OAAO,CAAC,KAAK;wEACvC,IAAI,CAAC,KAAK,SAAS,IAAI,CAAC,KAAK,MAAM,IAAI,CAAC,KAAK,WAAW,EAAE,QAAQ,OAAO;wEACzE,OAAO,MAAO,KAAK,MAAM,GAAG,KAAK,WAAW,CAAC,MAAM;oEACrD,GAAG,KAAK,oBACJ,sSAAC;wEAAI,WAAU;kFAA6D;;;;;;;;0FAQtF;;8EACE,sSAAC;oEAAI,WAAU;;sFACb,sSAAC;4EAAI,WAAU;sFAAwB;;;;;;sFACvC,sSAAC;4EAAI,WAAU;sFACZ,eAAe,CAAA,GAAA,+HAAA,CAAA,iBAAc,AAAD,EAC3B,KAAK,KAAK,CAAC,gBACX,YAAY,OAAO,GACnB,2BAA2B,KAAK,KAAK,CAAC,UACtC,8BAA8B,KAAK,KAAK,CAAC;;;;;;;;;;;;8EAK/C,sSAAC;oEAAI,WAAU;;sFACb,sSAAC;4EAAI,WAAU;sFAAwB;;;;;;sFACvC,sSAAC;4EAAI,WAAU;sFACZ,eAAe,CAAA,GAAA,+HAAA,CAAA,mBAAgB,AAAD,EAC7B,KAAK,KAAK,CAAC,gBACX,YAAY,OAAO,GACnB,2BAA2B,KAAK,KAAK,CAAC,UACtC,8BAA8B,KAAK,KAAK,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;wBAc9D,6BACC,sSAAC,8HAAA,CAAA,eAAY;4BAAC,WAAU;;8CACtB,sSAAC,8HAAA,CAAA,SAAM;oCACL,MAAK;oCACL,SAAQ;oCACR,SAAS;oCACT,UAAU;8CAET,SAAS,SAAS,SAAS;;;;;;gCAE7B,SAAS,wBACR,sSAAC,8HAAA,CAAA,SAAM;oCACL,MAAK;oCACL,WAAW,GAAG,KAAK,KAAK,CAAC,YAAY,QAAQ,oCAAoC,+BAA+B;oCAChH,UAAU;oCACV,SAAS,IAAM,aAAa,KAAK,SAAS;8CAEzC,0BACC;;0DACE,sSAAC,wSAAA,CAAA,UAAO;gDAAC,WAAU;;;;;;4CAA8B;;qEAInD;kDACG,KAAK,KAAK,CAAC,YAAY,sBACtB;;8DACE,sSAAC,ySAAA,CAAA,aAAU;oDAAC,WAAU;;;;;;gDAAiB;;yEAIzC;;8DACE,sSAAC,6SAAA,CAAA,eAAY;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;0BAa7D,sSAAC,uIAAA,CAAA,cAAW;gBAAC,MAAM;gBAAmB,cAAc;0BAClD,cAAA,sSAAC,uIAAA,CAAA,qBAAkB;;sCACjB,sSAAC,uIAAA,CAAA,oBAAiB;;8CAChB,sSAAC,uIAAA,CAAA,mBAAgB;8CAAC;;;;;;8CAClB,sSAAC,uIAAA,CAAA,yBAAsB;8CAAC;;;;;;;;;;;;sCAI1B,sSAAC,uIAAA,CAAA,oBAAiB;;8CAChB,sSAAC,uIAAA,CAAA,oBAAiB;8CAAC;;;;;;8CACnB,sSAAC,uIAAA,CAAA,oBAAiB;oCAChB,SAAS;wCACP;wCACA,qBAAqB;oCACvB;oCACA,WAAU;8CACX;;;;;;;;;;;;;;;;;;;;;;;;;AAQb;GA7hCgB;;QAMG,wHAAA,CAAA,UAAO;QAGX,0PAAA,CAAA,UAAO;;;KATN", "debugId": null}}, {"offset": {"line": 6134, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/bloomgoo/gold-exchange-platform/frontend/components/common/admin/order-books/components/float-delete-button.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { useState } from 'react';\r\nimport { Trash2 } from 'lucide-react';\r\nimport { Button } from '@/components/ui/button';\r\nimport {\r\n  AlertDialog,\r\n  AlertDialogAction,\r\n  AlertDialogCancel,\r\n  AlertDialogContent,\r\n  AlertDialogDescription,\r\n  AlertDialogFooter,\r\n  AlertDialogHeader,\r\n  AlertDialogTitle,\r\n} from '@/components/ui/alert-dialog';\r\n\r\ninterface FloatDeleteButtonProps {\r\n  selectedCount: number;\r\n  onDelete: () => void;\r\n}\r\n\r\nexport function FloatDeleteButton({ selectedCount, onDelete }: FloatDeleteButtonProps) {\r\n  const [showConfirmDialog, setShowConfirmDialog] = useState(false);\r\n\r\n  if (selectedCount === 0) {\r\n    return null;\r\n  }\r\n\r\n  return (\r\n    <>\r\n      <div className=\"fixed bottom-20 right-6 z-50 animate-in fade-in slide-in-from-bottom-5 duration-300\">\r\n        <Button\r\n          onClick={() => setShowConfirmDialog(true)}\r\n          className=\"rounded-full shadow-lg px-2 py-2 h-auto bg-destructive hover:bg-destructive/90\"\r\n        >\r\n          <Trash2 className=\"mr-1 h-5 w-5\" />\r\n          Xóa {selectedCount} lệnh đã chọn\r\n        </Button>\r\n      </div>\r\n\r\n      <AlertDialog open={showConfirmDialog} onOpenChange={setShowConfirmDialog}>\r\n        <AlertDialogContent>\r\n          <AlertDialogHeader>\r\n            <AlertDialogTitle>Xác nhận xóa</AlertDialogTitle>\r\n            <AlertDialogDescription>\r\n              Bạn có chắc chắn muốn xóa {selectedCount} lệnh đã chọn không? Hành động này không thể hoàn tác.\r\n            </AlertDialogDescription>\r\n          </AlertDialogHeader>\r\n          <AlertDialogFooter>\r\n            <AlertDialogCancel>Hủy</AlertDialogCancel>\r\n            <AlertDialogAction\r\n              onClick={() => {\r\n                onDelete();\r\n                setShowConfirmDialog(false);\r\n              }}\r\n              className=\"bg-destructive hover:bg-destructive/90\"\r\n            >\r\n              Xóa\r\n            </AlertDialogAction>\r\n          </AlertDialogFooter>\r\n        </AlertDialogContent>\r\n      </AlertDialog>\r\n    </>\r\n  );\r\n} "], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;;;AALA;;;;;AAqBO,SAAS,kBAAkB,EAAE,aAAa,EAAE,QAAQ,EAA0B;;IACnF,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,sQAAA,CAAA,WAAQ,AAAD,EAAE;IAE3D,IAAI,kBAAkB,GAAG;QACvB,OAAO;IACT;IAEA,qBACE;;0BACE,sSAAC;gBAAI,WAAU;0BACb,cAAA,sSAAC,8HAAA,CAAA,SAAM;oBACL,SAAS,IAAM,qBAAqB;oBACpC,WAAU;;sCAEV,sSAAC,iSAAA,CAAA,SAAM;4BAAC,WAAU;;;;;;wBAAiB;wBAC9B;wBAAc;;;;;;;;;;;;0BAIvB,sSAAC,uIAAA,CAAA,cAAW;gBAAC,MAAM;gBAAmB,cAAc;0BAClD,cAAA,sSAAC,uIAAA,CAAA,qBAAkB;;sCACjB,sSAAC,uIAAA,CAAA,oBAAiB;;8CAChB,sSAAC,uIAAA,CAAA,mBAAgB;8CAAC;;;;;;8CAClB,sSAAC,uIAAA,CAAA,yBAAsB;;wCAAC;wCACK;wCAAc;;;;;;;;;;;;;sCAG7C,sSAAC,uIAAA,CAAA,oBAAiB;;8CAChB,sSAAC,uIAAA,CAAA,oBAAiB;8CAAC;;;;;;8CACnB,sSAAC,uIAAA,CAAA,oBAAiB;oCAChB,SAAS;wCACP;wCACA,qBAAqB;oCACvB;oCACA,WAAU;8CACX;;;;;;;;;;;;;;;;;;;;;;;;;AAQb;GA3CgB;KAAA", "debugId": null}}, {"offset": {"line": 6269, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/bloomgoo/gold-exchange-platform/frontend/components/common/admin/order-books/components/order-business-type-tabs.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { cn } from '@/lib/utils';\r\nimport { Badge } from '@/components/ui/badge';\r\nimport { BusinessType } from '../type/order-books';\r\n\r\nexport type OrderBusinessTypeFilter = 'all' | BusinessType;\r\n\r\ninterface OrderBusinessTypeTabsProps {\r\n  currentStatus: OrderBusinessTypeFilter;\r\n  onStatusChange: (status: OrderBusinessTypeFilter) => void;\r\n  counts: {\r\n    all: number;\r\n    normal: number;\r\n    immediateDelivery: number;\r\n  };\r\n  className?: string;\r\n}\r\n\r\nexport function OrderBusinessTypeTabs({\r\n  currentStatus,\r\n  onStatusChange,\r\n  counts,\r\n  className\r\n}: OrderBusinessTypeTabsProps) {\r\n  const tabs = [\r\n    {\r\n      id: 'all' as const,\r\n      label: 'Tất cả',\r\n      count: counts.all,\r\n    },\r\n    {\r\n      id: BusinessType.NORMAL,\r\n      label: 'Bạc online ký quỹ',\r\n      count: counts.normal,\r\n    },\r\n    {\r\n      id: BusinessType.IMMEDIATE_DELIVERY,\r\n      label: 'Bạc giao ngay',\r\n      count: counts.immediateDelivery,\r\n    },\r\n\r\n  ];\r\n\r\n  return (\r\n    <div className={cn(\"flex items-center space-x-1 overflow-x-auto bg-muted/50 p-1 rounded-md\", className)}>\r\n      {tabs.map((tab) => (\r\n        <button\r\n          key={tab.id}\r\n          onClick={() => onStatusChange(tab.id)}\r\n          className={cn(\r\n            \"flex items-center px-3 py-1.5 text-sm font-medium rounded-md transition-colors whitespace-nowrap\",\r\n            currentStatus === tab.id\r\n              ? \"bg-background text-foreground shadow-sm\"\r\n              : \"text-muted-foreground hover:bg-background/50\"\r\n          )}\r\n        >\r\n          {tab.label}\r\n          <Badge\r\n            variant={currentStatus === tab.id ? \"default\" : \"secondary\"}\r\n            className={cn(\r\n              \"ml-2\",\r\n              currentStatus === tab.id\r\n                ? \"bg-primary/10 text-primary\"\r\n                : \"bg-muted text-muted-foreground\"\r\n            )}\r\n            title={`Tổng số lệnh ${tab.label.toLowerCase()} trong hệ thống`}\r\n          >\r\n            {tab.count.toLocaleString('vi-VN')}\r\n          </Badge>\r\n        </button>\r\n      ))}\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AAAA;AACA;AACA;AAJA;;;;;AAmBO,SAAS,sBAAsB,EACpC,aAAa,EACb,cAAc,EACd,MAAM,EACN,SAAS,EACkB;IAC3B,MAAM,OAAO;QACX;YACE,IAAI;YACJ,OAAO;YACP,OAAO,OAAO,GAAG;QACnB;QACA;YACE,IAAI,4KAAA,CAAA,eAAY,CAAC,MAAM;YACvB,OAAO;YACP,OAAO,OAAO,MAAM;QACtB;QACA;YACE,IAAI,4KAAA,CAAA,eAAY,CAAC,kBAAkB;YACnC,OAAO;YACP,OAAO,OAAO,iBAAiB;QACjC;KAED;IAED,qBACE,sSAAC;QAAI,WAAW,CAAA,GAAA,+HAAA,CAAA,KAAE,AAAD,EAAE,0EAA0E;kBAC1F,KAAK,GAAG,CAAC,CAAC,oBACT,sSAAC;gBAEC,SAAS,IAAM,eAAe,IAAI,EAAE;gBACpC,WAAW,CAAA,GAAA,+HAAA,CAAA,KAAE,AAAD,EACV,oGACA,kBAAkB,IAAI,EAAE,GACpB,4CACA;;oBAGL,IAAI,KAAK;kCACV,sSAAC,6HAAA,CAAA,QAAK;wBACJ,SAAS,kBAAkB,IAAI,EAAE,GAAG,YAAY;wBAChD,WAAW,CAAA,GAAA,+HAAA,CAAA,KAAE,AAAD,EACV,QACA,kBAAkB,IAAI,EAAE,GACpB,+BACA;wBAEN,OAAO,CAAC,aAAa,EAAE,IAAI,KAAK,CAAC,WAAW,GAAG,eAAe,CAAC;kCAE9D,IAAI,KAAK,CAAC,cAAc,CAAC;;;;;;;eApBvB,IAAI,EAAE;;;;;;;;;;AA0BrB;KAvDgB", "debugId": null}}, {"offset": {"line": 6341, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/bloomgoo/gold-exchange-platform/frontend/components/common/admin/order-books/order-book-detail-sheet.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { useState, useEffect } from 'react';\r\nimport { format } from 'date-fns';\r\nimport { vi } from 'date-fns/locale';\r\nimport {\r\n  FileText,\r\n  Calendar,\r\n  DollarSign,\r\n  CreditCard,\r\n  Tag,\r\n  BarChart2,\r\n  Clock,\r\n  User,\r\n  Activity,\r\n  Landmark,\r\n  Home,\r\n  ScrollText,\r\n  Pencil,\r\n  Loader2\r\n} from 'lucide-react';\r\n\r\nimport {\r\n  Sheet,\r\n  SheetContent,\r\n  SheetDescription,\r\n  SheetHeader,\r\n  SheetTitle,\r\n} from '@/components/ui/sheet';\r\n\r\nimport {\r\n  Table,\r\n  TableBody,\r\n  TableCaption,\r\n  TableCell,\r\n  TableHead,\r\n  TableHeader,\r\n  TableRow,\r\n} from \"@/components/ui/table\";\r\n\r\nimport { Badge } from '@/components/ui/badge';\r\nimport { Separator } from '@/components/ui/separator';\r\nimport { Button } from '@/components/ui/button';\r\nimport { OrderBook, OrderStatus, OrderType, OrderBookDetail } from './type/order-books';\r\nimport { api } from '@/lib/api';\r\nimport { IconCurrencyDong, IconExchange } from '@tabler/icons-react';\r\nimport { InfoCard } from '@/components/info-card';\r\nimport { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';\r\nimport { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';\r\nimport { UserHoverCard } from '@/components/common/user/user-hover-card';\r\nimport { toast } from 'sonner';\r\n\r\n// Define InfoCardItem component directly in this file\r\ninterface InfoCardItemProps {\r\n  label: React.ReactNode;\r\n  value: React.ReactNode;\r\n  className?: string;\r\n}\r\n\r\nfunction InfoCardItem({ label, value, className }: InfoCardItemProps) {\r\n  return (\r\n    <div className={`space-y-1 ${className}`}>\r\n      <div className=\"text-xs font-medium text-muted-foreground\">{label}</div>\r\n      <div className=\"text-md\">{value}</div>\r\n    </div>\r\n  );\r\n}\r\n\r\n// InfoCard component\r\ninterface InfoCardProps {\r\n  title: string;\r\n  description?: string;\r\n  children: React.ReactNode;\r\n}\r\n\r\ninterface OrderBookDetailSheetProps {\r\n  orderBook: OrderBook | null;\r\n  isOpen: boolean;\r\n  onClose: () => void;\r\n  onEdit: (orderBook: OrderBook) => void;\r\n}\r\n\r\nexport function OrderBookDetailSheet({ orderBook: initialOrderBook, isOpen, onClose, onEdit }: OrderBookDetailSheetProps) {\r\n  const [orderBook, setOrderBook] = useState<OrderBook | null>(initialOrderBook);\r\n  const [loading, setLoading] = useState<boolean>(false);\r\n\r\n  // Gọi API để lấy thông tin chi tiết của lệnh khi mở sheet\r\n  useEffect(() => {\r\n    if (isOpen && initialOrderBook?.id) {\r\n      setLoading(true);\r\n      api.get<OrderBook>(`order-books/${initialOrderBook.id}`)\r\n        .then(response => {\r\n          setOrderBook(response);\r\n          setLoading(false);\r\n        })\r\n        .catch(error => {\r\n          console.error('Error fetching order book details:', error);\r\n          toast.error('Không thể tải thông tin chi tiết lệnh');\r\n          setLoading(false);\r\n        });\r\n    }\r\n  }, [isOpen, initialOrderBook?.id]);\r\n\r\n  if (!orderBook) return null;\r\n\r\n  const formatDate = (dateStr: string | null | undefined) => {\r\n    if (!dateStr) return '---';\r\n    try {\r\n      return format(new Date(dateStr), 'dd/MM/yyyy HH:mm', { locale: vi });\r\n    } catch (error) {\r\n      return 'Không hợp lệ';\r\n    }\r\n  };\r\n\r\n  // Format currency with VND\r\n  const formatCurrency = (amount: number | undefined) => {\r\n    if (amount === undefined || amount === null) return \"---\";\r\n    return new Intl.NumberFormat('vi-VN', {\r\n      maximumFractionDigits: 0\r\n    }).format(amount);\r\n  };\r\n\r\n  // Get color for order status badge\r\n  const getStatusColor = (status: OrderStatus) => {\r\n    switch (status) {\r\n      case OrderStatus.DEPOSITED:\r\n        return 'bg-purple-100 text-purple-800 hover:bg-purple-200';\r\n      case OrderStatus.COMPLETED:\r\n        return 'bg-green-100 text-green-800 hover:bg-green-200';\r\n      case OrderStatus.TERMINATED:\r\n        return 'bg-red-100 text-red-800 hover:bg-red-200';\r\n      case OrderStatus.CANCELLED:\r\n        return 'bg-gray-300 text-gray-800 hover:bg-gray-200';\r\n      default:\r\n        return 'bg-gray-100 text-gray-800 hover:bg-gray-200';\r\n    }\r\n  };\r\n\r\n  // Order status in Vietnamese\r\n  const getStatusName = (status: OrderStatus) => {\r\n    switch (status) {\r\n      case OrderStatus.DEPOSITED:\r\n        return 'Đã ký quỹ';\r\n      case OrderStatus.COMPLETED:\r\n        return 'Đã tất toán';\r\n      case OrderStatus.TERMINATED:\r\n        return 'Đã cắt hợp đồng';\r\n      case OrderStatus.CANCELLED:\r\n        return 'Đã hủy';\r\n      default:\r\n        return status;\r\n    }\r\n  };\r\n\r\n  // Order type in Vietnamese\r\n  const getOrderTypeName = (type: OrderType) => {\r\n    switch (type) {\r\n      case OrderType.BUY:\r\n        return 'Mua';\r\n      case OrderType.SELL:\r\n        return 'Bán';\r\n      default:\r\n        return type;\r\n    }\r\n  };\r\n\r\n  // Get color for order type\r\n  const getOrderTypeColor = (type: OrderType) => {\r\n    switch (type) {\r\n      case OrderType.BUY:\r\n        return 'bg-green-100 text-green-800 hover:bg-green-200';\r\n      case OrderType.SELL:\r\n        return 'bg-red-100 text-red-800 hover:bg-red-200';\r\n      default:\r\n        return 'bg-gray-100 text-gray-800 hover:bg-gray-200';\r\n    }\r\n  };\r\n\r\n  // Tính tổng token\r\n  const getTotalTokens = () => {\r\n    return orderBook.details?.length || 0;\r\n  };\r\n\r\n  // Tính tổng khối lượng (số lượng × oz)\r\n  const getTotalVolume = () => {\r\n    if (!orderBook.details || orderBook.details.length === 0) return 0;\r\n\r\n    return orderBook.details.reduce((sum, detail) => {\r\n      const volume = detail.quantity || 0;\r\n      const weight = detail.product?.weight || 1;\r\n      return sum + (volume * weight);\r\n    }, 0);\r\n  };\r\n\r\n  return (\r\n    <Sheet open={isOpen} onOpenChange={(open) => !open && onClose()}>\r\n      <SheetContent className=\"w-full sm:max-w-xl md:max-w-2xl lg:max-w-3xl flex flex-col p-0\">\r\n        {/* Header cố định */}\r\n        <SheetHeader className=\"px-0 py-0 border-b\">\r\n          <div className=\"flex items-center gap-4 px-4 py-2\">\r\n          <div className=\"h-16 w-16 shrink-0 flex items-center justify-center\">\r\n              {loading ? (\r\n                <Loader2 className=\"h-8 w-8 animate-spin text-muted-foreground\" />\r\n              ) : (\r\n                <IconExchange className=\"h-8 w-8 text-primary\" />\r\n              )}\r\n            </div>\r\n            <div className=\"flex-1\">\r\n              <SheetTitle className=\"text-md font-semibold\">\r\n                {loading ? (\r\n                  <div className=\"flex items-center gap-2\">\r\n                    <Loader2 className=\"h-4 w-4 animate-spin\" />\r\n                    <span>Đang tải...</span>\r\n                  </div>\r\n                ) : (\r\n                  `Lệnh #${orderBook.contractNumber || orderBook.id?.substring(0, 8)}`\r\n                )}\r\n              </SheetTitle>\r\n              <SheetDescription>\r\n                {loading ? (\r\n                  <span className=\"flex items-center gap-2\">\r\n                    <Loader2 className=\"h-3 w-3 animate-spin\" />\r\n                    <span>Đang tải thông tin...</span>\r\n                  </span>\r\n                ) : (\r\n                  <span className=\"flex items-center gap-2\">\r\n                    <Badge className={getStatusColor(orderBook.status)}>\r\n                      {getStatusName(orderBook.status)}\r\n                    </Badge>\r\n                    <Badge className={getOrderTypeColor(orderBook.orderType)}>\r\n                      {getOrderTypeName(orderBook.orderType)}\r\n                    </Badge>\r\n                  </span>\r\n                )}\r\n              </SheetDescription>\r\n            </div>\r\n          </div>\r\n\r\n          {/* Đường kẻ phân tách */}\r\n          <div className=\"h-px w-full bg-border\"></div>\r\n\r\n          {/* Các nút chức năng */}\r\n          <div className=\"flex items-center gap-2 px-4 mb-2\">\r\n          <Button\r\n                variant=\"outline\"\r\n                size=\"sm\"\r\n                className=\"flex items-center gap-1\"\r\n                onClick={() => onEdit(orderBook)}\r\n              >\r\n                <Pencil className=\"h-3.5 w-3.5\" />\r\n                <span>Chỉnh sửa</span>\r\n              </Button>\r\n          </div>\r\n        </SheetHeader>\r\n\r\n        {/* Body có thể scroll */}\r\n        <div className=\"flex-1 overflow-y-auto px-4\">\r\n          {loading ? (\r\n            <div className=\"flex flex-col items-center justify-center h-full py-8 gap-4\">\r\n              <Loader2 className=\"h-8 w-8 animate-spin text-primary\" />\r\n              <p className=\"text-muted-foreground\">Đang tải thông tin chi tiết...</p>\r\n            </div>\r\n          ) : (\r\n            <>\r\n              {/* Thông tin cơ bản */}\r\n              <InfoCard\r\n                title=\"Thông tin lệnh\"\r\n                description=\"Thông tin chi tiết về lệnh\"\r\n                className=\"py-4\"\r\n              >\r\n                <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\r\n                  <InfoCardItem\r\n                    label={<div className=\"flex items-center gap-1\"><User className=\"h-3.5 w-3.5\" /> Người dùng</div>}\r\n                    value={orderBook.user\r\n                      ? (\r\n                        <UserHoverCard user={orderBook.user} showAvatar={true} size=\"sm\">\r\n                          <div className=\"max-w-[120px] overflow-hidden\">\r\n                            <div className=\"truncate\">{orderBook.user.fullName || orderBook.user.username || orderBook.user.email}</div>\r\n                          </div>\r\n                        </UserHoverCard>\r\n                      )\r\n                      : `User ID: ${orderBook.userId ? orderBook.userId.substring(0, 8) + '...' : 'Không xác định'}`}\r\n                  />\r\n                  <InfoCardItem\r\n                    label={<div className=\"flex items-center gap-1\"><ScrollText className=\"h-3.5 w-3.5\" /> Số hợp đồng</div>}\r\n                    value={orderBook.contractNumber || '---'}\r\n                  />\r\n                  <InfoCardItem\r\n                    label={<div className=\"flex items-center gap-1\"><DollarSign className=\"h-3.5 w-3.5\" /> Tổng giá trị</div>}\r\n                    value={<div className=\"flex items-center gap-1\">\r\n                      {formatCurrency(orderBook.totalPrice)} VND\r\n                    </div>}\r\n                  />\r\n                  <InfoCardItem\r\n                    label={<div className=\"flex items-center gap-1\"><Landmark className=\"h-3.5 w-3.5\" /> Tiền cọc</div>}\r\n                    value={<div className=\"flex items-center gap-1\">\r\n                      {formatCurrency(orderBook.depositPrice)} VND\r\n                    </div>}\r\n                  />\r\n                  <InfoCardItem\r\n                    label={<div className=\"flex items-center gap-1\"><DollarSign className=\"h-3.5 w-3.5\" /> Tiền tất toán</div>}\r\n                    value={<div className=\"flex items-center gap-1\">\r\n                      {formatCurrency(orderBook.settlementPrice)} VND\r\n                    </div>}\r\n                  />\r\n                  <InfoCardItem\r\n                    label={<div className=\"flex items-center gap-1\"><Home className=\"h-3.5 w-3.5\" /> Phí lưu kho</div>}\r\n                    value={<div className=\"flex items-center gap-1\">\r\n                      {formatCurrency(orderBook.storageFee)} VND\r\n                    </div>}\r\n                  />\r\n                  <InfoCardItem\r\n                    label={<div className=\"flex items-center gap-1\"><Calendar className=\"h-3.5 w-3.5\" /> Thời hạn tất toán</div>}\r\n                    value={orderBook.settlementDeadline ? formatDate(orderBook.settlementDeadline.toString()) : '---'}\r\n                  />\r\n                  <InfoCardItem\r\n                    label={<div className=\"flex items-center gap-1\"><Calendar className=\"h-3.5 w-3.5\" /> Thời gian tất toán</div>}\r\n                    value={orderBook.settlementAt ? formatDate(orderBook.settlementAt.toString()) : '---'}\r\n                  />\r\n                  <InfoCardItem\r\n                    label={<div className=\"flex items-center gap-1\"><BarChart2 className=\"h-3.5 w-3.5\" /> Số lượng sản phẩm</div>}\r\n                    value={getTotalTokens()}\r\n                  />\r\n                  <InfoCardItem\r\n                    label={<div className=\"flex items-center gap-1\"><BarChart2 className=\"h-3.5 w-3.5\" /> Tổng khối lượng</div>}\r\n                    value={`${getTotalVolume().toLocaleString()}`}\r\n                  />\r\n                </div>\r\n              </InfoCard>\r\n\r\n              {/* Thông tin thời gian */}\r\n              <InfoCard\r\n                title=\"Thông tin thời gian\"\r\n                description=\"Thời gian tạo và cập nhật lệnh\"\r\n                className=\"py-4\"\r\n              >\r\n                <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\r\n                  <InfoCardItem\r\n                    label={<div className=\"flex items-center gap-1\"><Calendar className=\"h-3.5 w-3.5\" /> Ngày tạo</div>}\r\n                    value={formatDate(orderBook.createdAt)}\r\n                  />\r\n                  <InfoCardItem\r\n                    label={<div className=\"flex items-center gap-1\"><Clock className=\"h-3.5 w-3.5\" /> Cập nhật lần cuối</div>}\r\n                    value={formatDate(orderBook.updatedAt)}\r\n                  />\r\n                  <InfoCardItem\r\n                    label={<div className=\"flex items-center gap-1\"><User className=\"h-3.5 w-3.5\" /> Người tạo</div>}\r\n                    value={orderBook.creator ? (\r\n                      <UserHoverCard user={orderBook.creator} showAvatar={true} size=\"sm\">\r\n                        <div className=\"max-w-[120px] overflow-hidden\">\r\n                          <div className=\"truncate\">{orderBook.creator.fullName || orderBook.creator.username || orderBook.creator.email || 'Không xác định'}</div>\r\n                        </div>\r\n                      </UserHoverCard>\r\n                    ) : (\r\n                      <span>Không có thông tin</span>\r\n                    )}\r\n                  />\r\n                  <InfoCardItem\r\n                    label={<div className=\"flex items-center gap-1\"><User className=\"h-3.5 w-3.5\" /> Người cập nhật</div>}\r\n                    value={orderBook.updater ? (\r\n                      <UserHoverCard user={orderBook.updater} showAvatar={true} size=\"sm\">\r\n                        <div className=\"max-w-[120px] overflow-hidden\">\r\n                          <div className=\"truncate\">{orderBook.updater.fullName || orderBook.updater.username || orderBook.updater.email || 'Không xác định'}</div>\r\n                        </div>\r\n                      </UserHoverCard>\r\n                    ) : (\r\n                      <span>Không có thông tin</span>\r\n                    )}\r\n                  />\r\n                </div>\r\n              </InfoCard>\r\n\r\n              {/* Danh sách Token */}\r\n              <InfoCard\r\n                title=\"Danh sách sản phẩm\"\r\n                description={`${orderBook.details?.length || 0} sản phẩm trong lệnh này`}\r\n                className=\"py-4\"\r\n              >\r\n                {orderBook.details && orderBook.details.length > 0 ? (\r\n                  <div className=\"overflow-x-auto\">\r\n                    <Table>\r\n                      <TableCaption>Tổng cộng: {orderBook.details.length} sản phẩm</TableCaption>\r\n                      <TableHeader>\r\n                        <TableRow>\r\n                          <TableHead className=\"w-[60px]\">STT</TableHead>\r\n                          <TableHead>Token ID</TableHead>\r\n                          <TableHead className=\"text-right\">Giá (VND)</TableHead>\r\n                          <TableHead className=\"text-right\">Số lượng</TableHead>\r\n                          <TableHead className=\"text-right\">Khối lượng </TableHead>\r\n                          <TableHead className=\"text-right\">Thành tiền (VND)</TableHead>\r\n                        </TableRow>\r\n                      </TableHeader>\r\n                      <TableBody>\r\n                        {orderBook.details.map((detail, index) => (\r\n                          <TableRow key={detail.id || `token-${index}`}>\r\n                            <TableCell>{index + 1}</TableCell>\r\n                            <TableCell className=\"font-medium\">\r\n                              {detail.product?.productName  || `Sản phẩm #${index + 1}`}\r\n                            </TableCell>\r\n                            <TableCell className=\"text-right\">\r\n                              {formatCurrency(detail.price)}\r\n                            </TableCell>\r\n                            <TableCell className=\"text-right\">\r\n                              {detail.quantity.toLocaleString()}\r\n                            </TableCell>\r\n                            <TableCell className=\"text-right\">\r\n                              {((detail.quantity || 0) * (detail.product?.weight || 1)).toLocaleString()}\r\n                            </TableCell>\r\n                            <TableCell className=\"text-right\">\r\n                              {formatCurrency(detail.totalPrice)}\r\n                            </TableCell>\r\n                          </TableRow>\r\n                        ))}\r\n                        {orderBook.details.length > 1 && (\r\n                          <TableRow className=\"font-medium bg-muted/50\">\r\n                            <TableCell colSpan={3} className=\"text-right\">Tổng cộng:</TableCell>\r\n                            <TableCell className=\"text-right\">\r\n                              {orderBook.details.reduce((sum, detail) => sum + detail.quantity, 0).toLocaleString()}\r\n                            </TableCell>\r\n                            <TableCell className=\"text-right\">\r\n                              {orderBook.details.reduce((sum, detail) => {\r\n                                const volume = detail.quantity || 0;\r\n                                const weight = detail.product?.weight || 1;\r\n                                return sum + (volume * weight);\r\n                              }, 0).toLocaleString()}\r\n                            </TableCell>\r\n                            <TableCell className=\"text-right\">\r\n                              {formatCurrency(orderBook.details.reduce((sum, detail) => sum + (detail.totalPrice || 0), 0))}\r\n                            </TableCell>\r\n                          </TableRow>\r\n                        )}\r\n                      </TableBody>\r\n                    </Table>\r\n                  </div>\r\n                ) : (\r\n                  <div className=\"text-center text-muted-foreground py-4\">\r\n                    Không có dữ liệu token trong lệnh này\r\n                  </div>\r\n                )}\r\n              </InfoCard>\r\n\r\n              {/* Lịch sử giao dịch */}\r\n              <InfoCard\r\n                title=\"Lịch sử giao dịch\"\r\n                description=\"Các giao dịch liên quan đến lệnh này\"\r\n                className=\"py-4\"\r\n              >\r\n                <div className=\"text-center text-muted-foreground py-4\">\r\n                  Chưa có dữ liệu lịch sử giao dịch\r\n                </div>\r\n              </InfoCard>\r\n            </>\r\n          )}\r\n        </div>\r\n      </SheetContent>\r\n    </Sheet>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAiBA;AAQA;AAUA;AAEA;AACA;AACA;AACA;AACA;AAGA;AACA;;;AAlDA;;;;;;;;;;;;;;;AA2DA,SAAS,aAAa,EAAE,KAAK,EAAE,KAAK,EAAE,SAAS,EAAqB;IAClE,qBACE,sSAAC;QAAI,WAAW,CAAC,UAAU,EAAE,WAAW;;0BACtC,sSAAC;gBAAI,WAAU;0BAA6C;;;;;;0BAC5D,sSAAC;gBAAI,WAAU;0BAAW;;;;;;;;;;;;AAGhC;KAPS;AAuBF,SAAS,qBAAqB,EAAE,WAAW,gBAAgB,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAA6B;;IACtH,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,sQAAA,CAAA,WAAQ,AAAD,EAAoB;IAC7D,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,sQAAA,CAAA,WAAQ,AAAD,EAAW;IAEhD,0DAA0D;IAC1D,CAAA,GAAA,sQAAA,CAAA,YAAS,AAAD;0CAAE;YACR,IAAI,UAAU,kBAAkB,IAAI;gBAClC,WAAW;gBACX,6GAAA,CAAA,MAAG,CAAC,GAAG,CAAY,CAAC,YAAY,EAAE,iBAAiB,EAAE,EAAE,EACpD,IAAI;sDAAC,CAAA;wBACJ,aAAa;wBACb,WAAW;oBACb;qDACC,KAAK;sDAAC,CAAA;wBACL,QAAQ,KAAK,CAAC,sCAAsC;wBACpD,2QAAA,CAAA,QAAK,CAAC,KAAK,CAAC;wBACZ,WAAW;oBACb;;YACJ;QACF;yCAAG;QAAC;QAAQ,kBAAkB;KAAG;IAEjC,IAAI,CAAC,WAAW,OAAO;IAEvB,MAAM,aAAa,CAAC;QAClB,IAAI,CAAC,SAAS,OAAO;QACrB,IAAI;YACF,OAAO,CAAA,GAAA,gNAAA,CAAA,SAAM,AAAD,EAAE,IAAI,KAAK,UAAU,oBAAoB;gBAAE,QAAQ,sMAAA,CAAA,KAAE;YAAC;QACpE,EAAE,OAAO,OAAO;YACd,OAAO;QACT;IACF;IAEA,2BAA2B;IAC3B,MAAM,iBAAiB,CAAC;QACtB,IAAI,WAAW,aAAa,WAAW,MAAM,OAAO;QACpD,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;YACpC,uBAAuB;QACzB,GAAG,MAAM,CAAC;IACZ;IAEA,mCAAmC;IACnC,MAAM,iBAAiB,CAAC;QACtB,OAAQ;YACN,KAAK,4KAAA,CAAA,cAAW,CAAC,SAAS;gBACxB,OAAO;YACT,KAAK,4KAAA,CAAA,cAAW,CAAC,SAAS;gBACxB,OAAO;YACT,KAAK,4KAAA,CAAA,cAAW,CAAC,UAAU;gBACzB,OAAO;YACT,KAAK,4KAAA,CAAA,cAAW,CAAC,SAAS;gBACxB,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,6BAA6B;IAC7B,MAAM,gBAAgB,CAAC;QACrB,OAAQ;YACN,KAAK,4KAAA,CAAA,cAAW,CAAC,SAAS;gBACxB,OAAO;YACT,KAAK,4KAAA,CAAA,cAAW,CAAC,SAAS;gBACxB,OAAO;YACT,KAAK,4KAAA,CAAA,cAAW,CAAC,UAAU;gBACzB,OAAO;YACT,KAAK,4KAAA,CAAA,cAAW,CAAC,SAAS;gBACxB,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,2BAA2B;IAC3B,MAAM,mBAAmB,CAAC;QACxB,OAAQ;YACN,KAAK,4KAAA,CAAA,YAAS,CAAC,GAAG;gBAChB,OAAO;YACT,KAAK,4KAAA,CAAA,YAAS,CAAC,IAAI;gBACjB,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,2BAA2B;IAC3B,MAAM,oBAAoB,CAAC;QACzB,OAAQ;YACN,KAAK,4KAAA,CAAA,YAAS,CAAC,GAAG;gBAChB,OAAO;YACT,KAAK,4KAAA,CAAA,YAAS,CAAC,IAAI;gBACjB,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,kBAAkB;IAClB,MAAM,iBAAiB;QACrB,OAAO,UAAU,OAAO,EAAE,UAAU;IACtC;IAEA,uCAAuC;IACvC,MAAM,iBAAiB;QACrB,IAAI,CAAC,UAAU,OAAO,IAAI,UAAU,OAAO,CAAC,MAAM,KAAK,GAAG,OAAO;QAEjE,OAAO,UAAU,OAAO,CAAC,MAAM,CAAC,CAAC,KAAK;YACpC,MAAM,SAAS,OAAO,QAAQ,IAAI;YAClC,MAAM,SAAS,OAAO,OAAO,EAAE,UAAU;YACzC,OAAO,MAAO,SAAS;QACzB,GAAG;IACL;IAEA,qBACE,sSAAC,6HAAA,CAAA,QAAK;QAAC,MAAM;QAAQ,cAAc,CAAC,OAAS,CAAC,QAAQ;kBACpD,cAAA,sSAAC,6HAAA,CAAA,eAAY;YAAC,WAAU;;8BAEtB,sSAAC,6HAAA,CAAA,cAAW;oBAAC,WAAU;;sCACrB,sSAAC;4BAAI,WAAU;;8CACf,sSAAC;oCAAI,WAAU;8CACV,wBACC,sSAAC,wSAAA,CAAA,UAAO;wCAAC,WAAU;;;;;6DAEnB,sSAAC,+TAAA,CAAA,eAAY;wCAAC,WAAU;;;;;;;;;;;8CAG5B,sSAAC;oCAAI,WAAU;;sDACb,sSAAC,6HAAA,CAAA,aAAU;4CAAC,WAAU;sDACnB,wBACC,sSAAC;gDAAI,WAAU;;kEACb,sSAAC,wSAAA,CAAA,UAAO;wDAAC,WAAU;;;;;;kEACnB,sSAAC;kEAAK;;;;;;;;;;;uDAGR,CAAC,MAAM,EAAE,UAAU,cAAc,IAAI,UAAU,EAAE,EAAE,UAAU,GAAG,IAAI;;;;;;sDAGxE,sSAAC,6HAAA,CAAA,mBAAgB;sDACd,wBACC,sSAAC;gDAAK,WAAU;;kEACd,sSAAC,wSAAA,CAAA,UAAO;wDAAC,WAAU;;;;;;kEACnB,sSAAC;kEAAK;;;;;;;;;;;qEAGR,sSAAC;gDAAK,WAAU;;kEACd,sSAAC,6HAAA,CAAA,QAAK;wDAAC,WAAW,eAAe,UAAU,MAAM;kEAC9C,cAAc,UAAU,MAAM;;;;;;kEAEjC,sSAAC,6HAAA,CAAA,QAAK;wDAAC,WAAW,kBAAkB,UAAU,SAAS;kEACpD,iBAAiB,UAAU,SAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCASjD,sSAAC;4BAAI,WAAU;;;;;;sCAGf,sSAAC;4BAAI,WAAU;sCACf,cAAA,sSAAC,8HAAA,CAAA,SAAM;gCACD,SAAQ;gCACR,MAAK;gCACL,WAAU;gCACV,SAAS,IAAM,OAAO;;kDAEtB,sSAAC,6RAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;kDAClB,sSAAC;kDAAK;;;;;;;;;;;;;;;;;;;;;;;8BAMd,sSAAC;oBAAI,WAAU;8BACZ,wBACC,sSAAC;wBAAI,WAAU;;0CACb,sSAAC,wSAAA,CAAA,UAAO;gCAAC,WAAU;;;;;;0CACnB,sSAAC;gCAAE,WAAU;0CAAwB;;;;;;;;;;;6CAGvC;;0CAEE,sSAAC,8HAAA,CAAA,WAAQ;gCACP,OAAM;gCACN,aAAY;gCACZ,WAAU;0CAEV,cAAA,sSAAC;oCAAI,WAAU;;sDACb,sSAAC;4CACC,qBAAO,sSAAC;gDAAI,WAAU;;kEAA0B,sSAAC,yRAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;oDAAgB;;;;;;;4CAChF,OAAO,UAAU,IAAI,iBAEjB,sSAAC,yJAAA,CAAA,gBAAa;gDAAC,MAAM,UAAU,IAAI;gDAAE,YAAY;gDAAM,MAAK;0DAC1D,cAAA,sSAAC;oDAAI,WAAU;8DACb,cAAA,sSAAC;wDAAI,WAAU;kEAAY,UAAU,IAAI,CAAC,QAAQ,IAAI,UAAU,IAAI,CAAC,QAAQ,IAAI,UAAU,IAAI,CAAC,KAAK;;;;;;;;;;;;;;;yDAIzG,CAAC,SAAS,EAAE,UAAU,MAAM,GAAG,UAAU,MAAM,CAAC,SAAS,CAAC,GAAG,KAAK,QAAQ,kBAAkB;;;;;;sDAElG,sSAAC;4CACC,qBAAO,sSAAC;gDAAI,WAAU;;kEAA0B,sSAAC,ySAAA,CAAA,aAAU;wDAAC,WAAU;;;;;;oDAAgB;;;;;;;4CACtF,OAAO,UAAU,cAAc,IAAI;;;;;;sDAErC,sSAAC;4CACC,qBAAO,sSAAC;gDAAI,WAAU;;kEAA0B,sSAAC,ySAAA,CAAA,aAAU;wDAAC,WAAU;;;;;;oDAAgB;;;;;;;4CACtF,qBAAO,sSAAC;gDAAI,WAAU;;oDACnB,eAAe,UAAU,UAAU;oDAAE;;;;;;;;;;;;sDAG1C,sSAAC;4CACC,qBAAO,sSAAC;gDAAI,WAAU;;kEAA0B,sSAAC,iSAAA,CAAA,WAAQ;wDAAC,WAAU;;;;;;oDAAgB;;;;;;;4CACpF,qBAAO,sSAAC;gDAAI,WAAU;;oDACnB,eAAe,UAAU,YAAY;oDAAE;;;;;;;;;;;;sDAG5C,sSAAC;4CACC,qBAAO,sSAAC;gDAAI,WAAU;;kEAA0B,sSAAC,ySAAA,CAAA,aAAU;wDAAC,WAAU;;;;;;oDAAgB;;;;;;;4CACtF,qBAAO,sSAAC;gDAAI,WAAU;;oDACnB,eAAe,UAAU,eAAe;oDAAE;;;;;;;;;;;;sDAG/C,sSAAC;4CACC,qBAAO,sSAAC;gDAAI,WAAU;;kEAA0B,sSAAC,0RAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;oDAAgB;;;;;;;4CAChF,qBAAO,sSAAC;gDAAI,WAAU;;oDACnB,eAAe,UAAU,UAAU;oDAAE;;;;;;;;;;;;sDAG1C,sSAAC;4CACC,qBAAO,sSAAC;gDAAI,WAAU;;kEAA0B,sSAAC,iSAAA,CAAA,WAAQ;wDAAC,WAAU;;;;;;oDAAgB;;;;;;;4CACpF,OAAO,UAAU,kBAAkB,GAAG,WAAW,UAAU,kBAAkB,CAAC,QAAQ,MAAM;;;;;;sDAE9F,sSAAC;4CACC,qBAAO,sSAAC;gDAAI,WAAU;;kEAA0B,sSAAC,iSAAA,CAAA,WAAQ;wDAAC,WAAU;;;;;;oDAAgB;;;;;;;4CACpF,OAAO,UAAU,YAAY,GAAG,WAAW,UAAU,YAAY,CAAC,QAAQ,MAAM;;;;;;sDAElF,sSAAC;4CACC,qBAAO,sSAAC;gDAAI,WAAU;;kEAA0B,sSAAC,uTAAA,CAAA,YAAS;wDAAC,WAAU;;;;;;oDAAgB;;;;;;;4CACrF,OAAO;;;;;;sDAET,sSAAC;4CACC,qBAAO,sSAAC;gDAAI,WAAU;;kEAA0B,sSAAC,uTAAA,CAAA,YAAS;wDAAC,WAAU;;;;;;oDAAgB;;;;;;;4CACrF,OAAO,GAAG,iBAAiB,cAAc,IAAI;;;;;;;;;;;;;;;;;0CAMnD,sSAAC,8HAAA,CAAA,WAAQ;gCACP,OAAM;gCACN,aAAY;gCACZ,WAAU;0CAEV,cAAA,sSAAC;oCAAI,WAAU;;sDACb,sSAAC;4CACC,qBAAO,sSAAC;gDAAI,WAAU;;kEAA0B,sSAAC,iSAAA,CAAA,WAAQ;wDAAC,WAAU;;;;;;oDAAgB;;;;;;;4CACpF,OAAO,WAAW,UAAU,SAAS;;;;;;sDAEvC,sSAAC;4CACC,qBAAO,sSAAC;gDAAI,WAAU;;kEAA0B,sSAAC,2RAAA,CAAA,QAAK;wDAAC,WAAU;;;;;;oDAAgB;;;;;;;4CACjF,OAAO,WAAW,UAAU,SAAS;;;;;;sDAEvC,sSAAC;4CACC,qBAAO,sSAAC;gDAAI,WAAU;;kEAA0B,sSAAC,yRAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;oDAAgB;;;;;;;4CAChF,OAAO,UAAU,OAAO,iBACtB,sSAAC,yJAAA,CAAA,gBAAa;gDAAC,MAAM,UAAU,OAAO;gDAAE,YAAY;gDAAM,MAAK;0DAC7D,cAAA,sSAAC;oDAAI,WAAU;8DACb,cAAA,sSAAC;wDAAI,WAAU;kEAAY,UAAU,OAAO,CAAC,QAAQ,IAAI,UAAU,OAAO,CAAC,QAAQ,IAAI,UAAU,OAAO,CAAC,KAAK,IAAI;;;;;;;;;;;;;;;uEAItH,sSAAC;0DAAK;;;;;;;;;;;sDAGV,sSAAC;4CACC,qBAAO,sSAAC;gDAAI,WAAU;;kEAA0B,sSAAC,yRAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;oDAAgB;;;;;;;4CAChF,OAAO,UAAU,OAAO,iBACtB,sSAAC,yJAAA,CAAA,gBAAa;gDAAC,MAAM,UAAU,OAAO;gDAAE,YAAY;gDAAM,MAAK;0DAC7D,cAAA,sSAAC;oDAAI,WAAU;8DACb,cAAA,sSAAC;wDAAI,WAAU;kEAAY,UAAU,OAAO,CAAC,QAAQ,IAAI,UAAU,OAAO,CAAC,QAAQ,IAAI,UAAU,OAAO,CAAC,KAAK,IAAI;;;;;;;;;;;;;;;uEAItH,sSAAC;0DAAK;;;;;;;;;;;;;;;;;;;;;;0CAOd,sSAAC,8HAAA,CAAA,WAAQ;gCACP,OAAM;gCACN,aAAa,GAAG,UAAU,OAAO,EAAE,UAAU,EAAE,wBAAwB,CAAC;gCACxE,WAAU;0CAET,UAAU,OAAO,IAAI,UAAU,OAAO,CAAC,MAAM,GAAG,kBAC/C,sSAAC;oCAAI,WAAU;8CACb,cAAA,sSAAC,6HAAA,CAAA,QAAK;;0DACJ,sSAAC,6HAAA,CAAA,eAAY;;oDAAC;oDAAY,UAAU,OAAO,CAAC,MAAM;oDAAC;;;;;;;0DACnD,sSAAC,6HAAA,CAAA,cAAW;0DACV,cAAA,sSAAC,6HAAA,CAAA,WAAQ;;sEACP,sSAAC,6HAAA,CAAA,YAAS;4DAAC,WAAU;sEAAW;;;;;;sEAChC,sSAAC,6HAAA,CAAA,YAAS;sEAAC;;;;;;sEACX,sSAAC,6HAAA,CAAA,YAAS;4DAAC,WAAU;sEAAa;;;;;;sEAClC,sSAAC,6HAAA,CAAA,YAAS;4DAAC,WAAU;sEAAa;;;;;;sEAClC,sSAAC,6HAAA,CAAA,YAAS;4DAAC,WAAU;sEAAa;;;;;;sEAClC,sSAAC,6HAAA,CAAA,YAAS;4DAAC,WAAU;sEAAa;;;;;;;;;;;;;;;;;0DAGtC,sSAAC,6HAAA,CAAA,YAAS;;oDACP,UAAU,OAAO,CAAC,GAAG,CAAC,CAAC,QAAQ,sBAC9B,sSAAC,6HAAA,CAAA,WAAQ;;8EACP,sSAAC,6HAAA,CAAA,YAAS;8EAAE,QAAQ;;;;;;8EACpB,sSAAC,6HAAA,CAAA,YAAS;oEAAC,WAAU;8EAClB,OAAO,OAAO,EAAE,eAAgB,CAAC,UAAU,EAAE,QAAQ,GAAG;;;;;;8EAE3D,sSAAC,6HAAA,CAAA,YAAS;oEAAC,WAAU;8EAClB,eAAe,OAAO,KAAK;;;;;;8EAE9B,sSAAC,6HAAA,CAAA,YAAS;oEAAC,WAAU;8EAClB,OAAO,QAAQ,CAAC,cAAc;;;;;;8EAEjC,sSAAC,6HAAA,CAAA,YAAS;oEAAC,WAAU;8EAClB,CAAC,CAAC,OAAO,QAAQ,IAAI,CAAC,IAAI,CAAC,OAAO,OAAO,EAAE,UAAU,CAAC,CAAC,EAAE,cAAc;;;;;;8EAE1E,sSAAC,6HAAA,CAAA,YAAS;oEAAC,WAAU;8EAClB,eAAe,OAAO,UAAU;;;;;;;2DAftB,OAAO,EAAE,IAAI,CAAC,MAAM,EAAE,OAAO;;;;;oDAmB7C,UAAU,OAAO,CAAC,MAAM,GAAG,mBAC1B,sSAAC,6HAAA,CAAA,WAAQ;wDAAC,WAAU;;0EAClB,sSAAC,6HAAA,CAAA,YAAS;gEAAC,SAAS;gEAAG,WAAU;0EAAa;;;;;;0EAC9C,sSAAC,6HAAA,CAAA,YAAS;gEAAC,WAAU;0EAClB,UAAU,OAAO,CAAC,MAAM,CAAC,CAAC,KAAK,SAAW,MAAM,OAAO,QAAQ,EAAE,GAAG,cAAc;;;;;;0EAErF,sSAAC,6HAAA,CAAA,YAAS;gEAAC,WAAU;0EAClB,UAAU,OAAO,CAAC,MAAM,CAAC,CAAC,KAAK;oEAC9B,MAAM,SAAS,OAAO,QAAQ,IAAI;oEAClC,MAAM,SAAS,OAAO,OAAO,EAAE,UAAU;oEACzC,OAAO,MAAO,SAAS;gEACzB,GAAG,GAAG,cAAc;;;;;;0EAEtB,sSAAC,6HAAA,CAAA,YAAS;gEAAC,WAAU;0EAClB,eAAe,UAAU,OAAO,CAAC,MAAM,CAAC,CAAC,KAAK,SAAW,MAAM,CAAC,OAAO,UAAU,IAAI,CAAC,GAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;yDAQtG,sSAAC;oCAAI,WAAU;8CAAyC;;;;;;;;;;;0CAO5D,sSAAC,8HAAA,CAAA,WAAQ;gCACP,OAAM;gCACN,aAAY;gCACZ,WAAU;0CAEV,cAAA,sSAAC;oCAAI,WAAU;8CAAyC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUxE;GAvXgB;MAAA", "debugId": null}}, {"offset": {"line": 7481, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/bloomgoo/gold-exchange-platform/frontend/components/common/admin/order-books/settlement-modal.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { Badge } from '@/components/ui/badge';\r\nimport { Button } from '@/components/ui/button';\r\nimport {\r\n  Dialog,\r\n  DialogContent,\r\n  DialogDescription,\r\n  DialogFooter,\r\n  DialogHeader,\r\n  DialogTitle,\r\n} from '@/components/ui/dialog';\r\nimport { Input } from '@/components/ui/input';\r\nimport { Label } from '@/components/ui/label';\r\nimport { api } from '@/lib/api';\r\nimport { ApiResponse } from '@/lib/response';\r\nimport { calculateNormalProductPrice, formatCurrency, formatExchangePrice } from '@/lib/utils';\r\nimport { Loader2, TrendingDown, TrendingUp } from 'lucide-react';\r\nimport { useEffect, useState } from 'react';\r\nimport { toast } from 'sonner';\r\nimport { BusinessType, OrderBook, OrderStatus, OrderType } from './type/order-books';\r\nimport { usePolygonForex } from '@/services/websocket/polygon-forex-socket.service';\r\n\r\ninterface SettlementModalProps {\r\n  orderBook: OrderBook | null;\r\n  isOpen: boolean;\r\n  onClose: () => void;\r\n  onSuccess: () => void;\r\n  withdrawalMode?: boolean;\r\n  fixedPrice?: {\r\n    usd: number;\r\n    vnd: number;\r\n  } | null;\r\n}\r\n\r\nexport function SettlementModal({ orderBook, isOpen, onClose, onSuccess, withdrawalMode = false, fixedPrice = null }: SettlementModalProps) {\r\n  const [isSubmitting, setIsSubmitting] = useState(false);\r\n  const [isLoading, setIsLoading] = useState(false);\r\n  const [orderDetail, setOrderDetail] = useState<OrderBook | null>(null);\r\n  const [marketPrice, setMarketPrice] = useState<string>('');\r\n  const [calculatedPrices, setCalculatedPrices] = useState<{ [key: string]: number }>({});\r\n  const [calculatedSettlementAmount, setCalculatedSettlementAmount] = useState<number | null>(null);\r\n  const [calculatedProfit, setCalculatedProfit] = useState<number | null>(null);\r\n  // Lấy thông tin chi tiết của lệnh khi modal mở\r\n  useEffect(() => {\r\n    if (isOpen && orderBook) {\r\n      fetchOrderDetail(orderBook.id);\r\n      // Reset các state khi mở modal\r\n      setMarketPrice('');\r\n      setCalculatedPrices({});\r\n      setCalculatedSettlementAmount(null);\r\n      setCalculatedProfit(null);\r\n    }\r\n  }, [isOpen, orderBook]);\r\n\r\n  // Thiết lập giá trị tất toán cho chế độ rút bạc\r\n  useEffect(() => {\r\n    if (isOpen && orderDetail && withdrawalMode) {\r\n      // Tính toán số tiền cần thanh toán (tổng giá trị - tiền đặt cọc)\r\n      const remainingAmount = (orderDetail.totalPrice || 0) - (orderDetail.depositPrice || 0);\r\n      setCalculatedSettlementAmount(remainingAmount);\r\n    }\r\n  }, [isOpen, orderDetail, withdrawalMode]);\r\n\r\n  // Sử dụng hook usePolygonForex để lấy thông tin giá từ websocket\r\n  const { currentQuote, isConnected } = usePolygonForex();\r\n\r\n  // Effect để tự động cập nhật giá thị trường khi có dữ liệu mới từ websocket\r\n  useEffect(() => {\r\n    if (!withdrawalMode && orderDetail?.businessType !== BusinessType.IMMEDIATE_DELIVERY && fixedPrice) {\r\n      // Chỉ sử dụng giá cố định, không cập nhật theo websocket\r\n      setMarketPrice(fixedPrice.usd.toString());\r\n      handleMarketPriceChange(fixedPrice.usd.toString(), fixedPrice.vnd);\r\n    }\r\n  }, [orderDetail, withdrawalMode, fixedPrice]);\r\n\r\n  // Cập nhật hàm handleMarketPriceChange để hỗ trợ giá VND từ websocket\r\n  const handleMarketPriceChange = (value: string, priceVND?: number) => {\r\n    setMarketPrice(value);\r\n\r\n    if (!orderDetail?.details || orderDetail.details.length === 0) return;\r\n\r\n    const numValue = parseFloat(value);\r\n    if (isNaN(numValue) || numValue <= 0) {\r\n      setCalculatedPrices({});\r\n      setCalculatedSettlementAmount(null);\r\n      setCalculatedProfit(null);\r\n      return;\r\n    }\r\n\r\n    // Tính toán giá mới cho từng loại bạc dựa trên giá thị trường\r\n    const newPrices: { [key: string]: number } = {};\r\n\r\n    orderDetail.details.forEach(detail => {\r\n      if (detail.product) {\r\n        // Sử dụng giá VND từ websocket nếu có, nếu không thì tính từ giá USD\r\n        const tokenOz = detail.product.weight || 1;\r\n        const price = priceVND || calculateNormalProductPrice(numValue, tokenOz);\r\n        newPrices[detail.productId] = price;\r\n      }\r\n    });\r\n\r\n    setCalculatedPrices(newPrices);\r\n\r\n    // Tính toán tổng giá trị mới\r\n    const newTotalPrice = orderDetail.details.reduce((sum, detail) => {\r\n      return sum + (newPrices[detail.productId] || 0) * (detail.quantity || 0);\r\n    }, 0);\r\n\r\n    // Tính toán tiền tất toán mới (90% của tổng giá trị)\r\n    const newSettlementAmount = newTotalPrice * 0.9;\r\n    setCalculatedSettlementAmount(newSettlementAmount);\r\n\r\n    // Tính toán lợi nhuận\r\n    const originalTotalPrice = orderDetail.totalPrice || 0;\r\n    const swapFee = 0; // Phí swap tạm thời để 0\r\n    const profit = newTotalPrice - originalTotalPrice - swapFee;\r\n    setCalculatedProfit(profit);\r\n  };\r\n\r\n  // Lấy thông tin chi tiết của lệnh\r\n  const fetchOrderDetail = async (id: string) => {\r\n    try {\r\n      setIsLoading(true);\r\n      const response = await api.get(`order-books/${id}`);\r\n      setOrderDetail(response as OrderBook);\r\n    } catch (error) {\r\n      console.error('Error fetching order detail:', error);\r\n      toast.error(\"Không thể tải thông tin chi tiết lệnh\");\r\n    } finally {\r\n      setIsLoading(false);\r\n    }\r\n  };\r\n\r\n  // Cập nhật hàm handleSettlement để xử lý giá cố định\r\n  const handleSettlement = async () => {\r\n    try {\r\n      setIsSubmitting(true);\r\n\r\n      // Chuẩn bị dữ liệu theo DTO\r\n      const payload: {\r\n        userId?: string;\r\n        price?: number;\r\n        isImmediateDelivery?: boolean;\r\n      } = {};\r\n\r\n      // Xử lý khác nhau dựa trên loại hình kinh doanh và chế độ rút bạc\r\n      if (orderDetail?.businessType === BusinessType.IMMEDIATE_DELIVERY) {\r\n        payload.userId = orderDetail?.userId;\r\n        // Với bạc giao ngay, chỉ cần gọi API tất toán lệnh\r\n        payload.isImmediateDelivery = true;\r\n        await api.patch(`order-books/${orderBook?.id}/status/completed`, payload);\r\n        toast.success('Đã xác nhận rút bạc giao ngay thành công');\r\n      } else if (withdrawalMode) {\r\n        // Chế độ rút bạc: Chuyển lệnh mua bạc ký quỹ sang bạc giao ngay\r\n        payload.userId = orderDetail?.userId;\r\n        payload.price = orderDetail?.totalPrice || 0;\r\n        payload.isImmediateDelivery = true;\r\n        await api.patch(`order-books/${orderBook?.id}/status/completed`, payload);\r\n        toast.success('Đã xác nhận chuyển đổi sang bạc giao ngay thành công');\r\n      } else {\r\n        // Tất toán bình thường cho bạc ký quỹ online\r\n        if (fixedPrice) {\r\n          // Sử dụng giá cố định nếu có\r\n          payload.userId = orderDetail?.userId;\r\n          payload.price = fixedPrice.vnd;\r\n        } else if (currentQuote) {\r\n          // Sử dụng giá từ currentQuote nếu không có giá cố định\r\n          const basePrice = orderDetail?.orderType === OrderType.BUY ? \r\n            currentQuote.sellPriceVND : currentQuote.buyPriceVND;\r\n          payload.userId = orderDetail?.userId;\r\n          payload.price = basePrice;\r\n        } else {\r\n          toast.error('Không có thông tin giá để tất toán. Vui lòng thử lại sau.');\r\n          setIsSubmitting(false);\r\n          return;\r\n        }\r\n\r\n        await api.patch<ApiResponse<OrderBook>>(`order-books/${orderBook?.id}/status/completed`, payload);\r\n        toast.success('Đã tất toán lệnh thành công');\r\n      }\r\n\r\n      onSuccess();\r\n      onClose();\r\n    } catch (error) {\r\n      console.error('Error completing order:', error);\r\n\r\n      // Thông báo lỗi tùy theo loại hình kinh doanh\r\n      if (orderDetail?.businessType === BusinessType.IMMEDIATE_DELIVERY) {\r\n        toast.error('Không thể rút bạc giao ngay', {\r\n          description: error instanceof Error ? error.message : 'Vui lòng thử lại sau',\r\n        });\r\n      } else {\r\n        toast.error('Không thể tất toán lệnh', {\r\n          description: error instanceof Error ? error.message : 'Vui lòng thử lại sau',\r\n        });\r\n      }\r\n    } finally {\r\n      setIsSubmitting(false);\r\n    }\r\n  };\r\n\r\n  // Chuyển đổi trạng thái sang tiếng Việt\r\n  const getStatusName = (status: OrderStatus) => {\r\n    switch (status) {\r\n      case OrderStatus.COMPLETED:\r\n        return 'Đã tất toán';\r\n      case OrderStatus.DEPOSITED:\r\n        return 'Đã ký quỹ';\r\n      case OrderStatus.TERMINATED:\r\n        return 'Đã cắt hợp đồng';\r\n      case OrderStatus.CANCELLED:\r\n        return 'Đã hủy';\r\n      default:\r\n        return status;\r\n    }\r\n  };\r\n\r\n  // Lấy màu cho badge trạng thái\r\n  const getStatusColor = (status: OrderStatus) => {\r\n    switch (status) {\r\n      case OrderStatus.COMPLETED:\r\n        return 'bg-green-100 text-green-800';\r\n      case OrderStatus.DEPOSITED:  \r\n        return 'bg-purple-100 text-purple-800';\r\n      case OrderStatus.TERMINATED:\r\n        return 'bg-red-100 text-red-800';\r\n      case OrderStatus.CANCELLED:\r\n        return 'bg-orange-100 text-orange-800';\r\n      default:\r\n        return 'bg-gray-100 text-gray-800';\r\n    }\r\n  };\r\n\r\n  // Tính tổng khối lượng\r\n  const totalVolume = orderDetail?.details?.reduce((sum, detail) => sum + (detail.quantity || 0), 0) || 0;\r\n\r\n  if (!orderBook) return null;\r\n\r\n  return (\r\n    <Dialog open={isOpen} onOpenChange={onClose}>\r\n      <DialogContent className=\"sm:max-w-[550px]\">\r\n        <DialogHeader>\r\n          <DialogTitle>\r\n            {orderDetail?.businessType === BusinessType.IMMEDIATE_DELIVERY\r\n              ? \"Rút bạc giao ngay\"\r\n              : withdrawalMode\r\n                ? \"Chuyển đổi sang bạc giao ngay\"\r\n                : \"Tất toán lệnh\"}\r\n          </DialogTitle>\r\n          <DialogDescription>\r\n            {orderDetail?.businessType === BusinessType.IMMEDIATE_DELIVERY\r\n              ? \"Xác nhận thông tin để rút bạc giao ngay. Vui lòng kiểm tra thông tin lệnh trước khi xác nhận.\"\r\n              : withdrawalMode\r\n                ? \"Xác nhận chuyển đổi lệnh bạc ký quỹ sang bạc giao ngay. Khách hàng cần thanh toán 90% giá trị còn lại để rút bạc.\"\r\n                : \"Xác nhận tất toán lệnh. Bạn có thể điều chỉnh giá thị trường để tính toán giá tất toán.\"}\r\n          </DialogDescription>\r\n        </DialogHeader>\r\n\r\n        {isLoading ? (\r\n          <div className=\"flex items-center justify-center py-8\">\r\n            <Loader2 className=\"h-8 w-8 animate-spin text-primary\" />\r\n            <span className=\"ml-2\">Đang tải thông tin...</span>\r\n          </div>\r\n        ) : (\r\n          <div className=\"grid gap-4 py-4\">\r\n            <div className=\"grid grid-cols-2 gap-4\">\r\n              <div className=\"space-y-1\">\r\n                <p className=\"text-sm font-medium text-muted-foreground\">Số hợp đồng</p>\r\n                <p className=\"text-sm font-semibold\">{orderDetail?.contractNumber || '---'}</p>\r\n              </div>\r\n              <div className=\"space-y-1\">\r\n                <p className=\"text-sm font-medium text-muted-foreground\">Trạng thái</p>\r\n                <Badge className={getStatusColor(orderDetail?.status || OrderStatus.DEPOSITED)}>\r\n                  {getStatusName(orderDetail?.status || OrderStatus.DEPOSITED)}\r\n                </Badge>\r\n              </div>\r\n            </div>\r\n\r\n            <div className=\"grid grid-cols-2 gap-4\">\r\n              <div className=\"space-y-1\">\r\n                <p className=\"text-sm font-medium text-muted-foreground\">Khối lượng</p>\r\n                <p className=\"text-sm font-semibold\">{totalVolume.toLocaleString()} g</p>\r\n              </div>\r\n              <div className=\"space-y-1\">\r\n                <p className=\"text-sm font-medium text-muted-foreground\">Tổng giá trị</p>\r\n                <p className=\"text-sm font-semibold\">{formatCurrency(orderDetail?.totalPrice)}</p>\r\n              </div>\r\n            </div>\r\n\r\n            <div className=\"grid grid-cols-2 gap-4\">\r\n              <div className=\"space-y-1\">\r\n                <p className=\"text-sm font-medium text-muted-foreground\">Tiền đặt cọc</p>\r\n                <p className=\"text-sm font-semibold\">{formatCurrency(orderDetail?.depositPrice)}</p>\r\n              </div>\r\n              <div className=\"space-y-1\">\r\n                <p className=\"text-sm font-medium text-muted-foreground\">Tiền tất toán</p>\r\n                <p className=\"text-sm font-semibold\">\r\n                  {orderDetail?.businessType === BusinessType.IMMEDIATE_DELIVERY || !calculatedSettlementAmount\r\n                    ? formatCurrency(orderDetail?.settlementPrice)\r\n                    : formatCurrency(calculatedSettlementAmount)}\r\n                </p>\r\n              </div>\r\n            </div>\r\n\r\n            <div className=\"grid grid-cols-2 gap-4\">\r\n              <div className=\"space-y-1\">\r\n                <p className=\"text-sm font-medium text-muted-foreground\">Phí lưu kho</p>\r\n                <p className=\"text-sm font-semibold\">{formatCurrency(orderDetail?.storageFee)}</p>\r\n              </div>\r\n              <div className=\"space-y-1\">\r\n                <p className=\"text-sm font-medium text-muted-foreground\">Hạn tất toán</p>\r\n                <p className=\"text-sm font-semibold\">\r\n                  {orderDetail?.settlementDeadline\r\n                    ? new Date(orderDetail.settlementDeadline).toLocaleDateString('vi-VN')\r\n                    : '---'}\r\n                </p>\r\n              </div>\r\n            </div>\r\n\r\n            <div className=\"space-y-4 mt-4\">\r\n              {/* Hiển thị khác nhau dựa trên loại hình kinh doanh */}\r\n              {orderDetail?.businessType === BusinessType.IMMEDIATE_DELIVERY ? (\r\n                // Form cho bạc giao ngay - chỉ hiển thị thông tin chi tiết\r\n                <div className=\"space-y-2\">\r\n                  <div className=\"flex items-center space-x-2 mb-2\">\r\n                    <Badge className=\"bg-blue-100 text-blue-800\">Bạc giao ngay</Badge>\r\n                    <p className=\"text-xs text-muted-foreground\">\r\n                      Xác nhận thông tin để rút bạc giao ngay\r\n                    </p>\r\n                  </div>\r\n\r\n                  <p className=\"text-sm font-medium\">Thông tin bạc</p>\r\n                  <div className=\"border rounded-md p-3 bg-muted/50\">\r\n                    <div className=\"grid grid-cols-4 gap-2 mb-2 text-xs font-medium text-muted-foreground\">\r\n                      <div>Loại bạc</div>\r\n                      <div>Khối lượng</div>\r\n                      <div>Giá mua</div>\r\n                      <div>Thành tiền</div>\r\n                    </div>\r\n                    {orderDetail?.details?.map((detail, index) => (\r\n                      <div key={index} className=\"grid grid-cols-4 gap-2 py-1.5 border-t text-sm\">\r\n                        <div className=\"font-medium\">{detail.product?.productName || 'Bạc'}</div>\r\n                        <div>{detail.quantity?.toLocaleString() || 0} g</div>\r\n                        <div>{formatCurrency(detail.price)}</div>\r\n                        <div>{formatCurrency(detail.price ? detail.price * (detail.quantity || 0) : 0)}</div>\r\n                      </div>\r\n                    ))}\r\n                    <div className=\"grid grid-cols-4 gap-2 py-1.5 border-t text-sm font-semibold\">\r\n                      <div>Tổng cộng</div>\r\n                      <div>{totalVolume.toLocaleString()} g</div>\r\n                      <div></div>\r\n                      <div>{formatCurrency(orderDetail?.totalPrice)}</div>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              ) : (\r\n                // Form cho bạc ký quỹ online - có ô nhập giá thị trường\r\n                <>\r\n                  <div className=\"flex items-center space-x-2 mb-2\">\r\n                    <Badge className=\"bg-orange-100 text-orange-800\">Bạc ký quỹ online</Badge>\r\n                  </div>\r\n\r\n                  {withdrawalMode ? (\r\n                    // Hiển thị thông tin thanh toán cho chế độ rút bạc\r\n                    <div className=\"p-3 bg-blue-50 rounded-md\">\r\n                      <p className=\"text-sm font-medium mb-2\">Thông tin thanh toán</p>\r\n                      <div className=\"grid grid-cols-2 gap-2 text-sm\">\r\n                        <div className=\"text-muted-foreground\">Tổng giá trị:</div>\r\n                        <div className=\"font-medium\">{formatCurrency(orderDetail?.totalPrice)}</div>\r\n\r\n                        <div className=\"text-muted-foreground\">Đã đặt cọc:</div>\r\n                        <div className=\"font-medium\">{formatCurrency(orderDetail?.depositPrice)}</div>\r\n\r\n                        <div className=\"text-muted-foreground font-medium\">Cần thanh toán:</div>\r\n                        <div className=\"font-medium text-blue-700\">\r\n                          {formatCurrency((orderDetail?.totalPrice || 0) - (orderDetail?.depositPrice || 0))}\r\n                        </div>\r\n                      </div>\r\n                    </div>\r\n                  ) : (\r\n                    // Hiển thị ô nhập giá thị trường cho chế độ tất toán thông thường\r\n                    <>\r\n                      <div className=\"p-2 flex items-center justify-between\">\r\n                        <div className=\"space-y-1\">\r\n                          <span className={`text-base font-medium ${orderDetail?.orderType === OrderType.BUY ? 'text-green-600' : 'text-red-600'}`}>\r\n                            {formatCurrency(fixedPrice ? fixedPrice.vnd : (currentQuote ?\r\n                              orderDetail?.orderType === OrderType.BUY ? currentQuote.sellPriceVND : currentQuote.buyPriceVND\r\n                              : 0\r\n                            ))}\r\n                          </span>\r\n                          <p className=\"text-sm text-muted-foreground\">\r\n                            ${fixedPrice ? fixedPrice.usd.toFixed(2) : (currentQuote ?\r\n                              (orderDetail?.orderType === OrderType.BUY ? currentQuote.askPrice : currentQuote.bidPrice).toFixed(2)\r\n                              : \"0.00\"\r\n                            )}\r\n                          </p>\r\n                        </div>\r\n                        <span className={`font-bold text-lg ${orderDetail?.orderType === OrderType.BUY ? 'text-green-600' : 'text-red-600'}`}>\r\n                          {orderDetail?.orderType === OrderType.BUY ?\r\n                            <TrendingUp className=\"h-5 w-5\" /> :\r\n                            <TrendingDown className=\"h-5 w-5\" />\r\n                          }\r\n                        </span>\r\n                      </div>\r\n\r\n                      {/* Hiển thị lợi nhuận dự kiến */}\r\n                      {calculatedProfit !== null && (\r\n                        <div className={`p-2 rounded-md mb-2 ${calculatedProfit >= 0 ? 'bg-green-50' : 'bg-red-50'}`}>\r\n                          <p className=\"text-sm font-medium\">\r\n                            Lợi nhuận dự kiến:\r\n                            <span className={`ml-1 font-semibold ${calculatedProfit >= 0 ? 'text-green-600' : 'text-red-600'}`}>\r\n                              {formatCurrency(calculatedProfit)}\r\n                            </span>\r\n                          </p>\r\n                        </div>\r\n                      )}\r\n                    </>\r\n                  )}\r\n\r\n                  <div className=\"space-y-2\">\r\n                    <p className=\"text-sm font-medium\">Thông tin bạc</p>\r\n                    <div className=\"border rounded-md p-3 bg-muted/50\">\r\n                      <div className=\"grid grid-cols-4 gap-2 mb-2 text-xs font-medium text-muted-foreground\">\r\n                        <div>Loại bạc</div>\r\n                        <div>Khối lượng</div>\r\n                        <div>Giá mua</div>\r\n                        <div>Thành tiền</div>\r\n                      </div>\r\n                      {orderDetail?.details?.map((detail, index) => (\r\n                        <div key={index} className=\"grid grid-cols-4 gap-2 py-1.5 border-t text-sm\">\r\n                          <div className=\"font-medium\">{detail.product?.productName || 'Bạc'}</div>\r\n                          <div>{detail.quantity?.toLocaleString() || 0} g</div>\r\n                          <div>\r\n                            {withdrawalMode\r\n                              ? formatCurrency(detail.price)\r\n                              : calculatedPrices[detail.productId]\r\n                                ? formatCurrency(calculatedPrices[detail.productId])\r\n                                : formatCurrency(detail.price)}\r\n                          </div>\r\n                          <div>\r\n                            {withdrawalMode\r\n                              ? formatCurrency(detail.price ? detail.price * (detail.quantity || 0) : 0)\r\n                              : calculatedPrices[detail.productId]\r\n                                ? formatCurrency(calculatedPrices[detail.productId] * (detail.quantity || 0))\r\n                                : formatCurrency(detail.price ? detail.price * (detail.quantity || 0) : 0)}\r\n                          </div>\r\n                        </div>\r\n                      ))}\r\n                      <div className=\"grid grid-cols-4 gap-2 py-1.5 border-t text-sm font-semibold\">\r\n                        <div>Tổng cộng</div>\r\n                        <div>{totalVolume.toLocaleString()} g</div>\r\n                        <div></div>\r\n                        <div>\r\n                          {withdrawalMode\r\n                            ? formatCurrency(orderDetail?.totalPrice)\r\n                            : Object.keys(calculatedPrices).length > 0\r\n                              ? formatCurrency(\r\n                                orderDetail?.details?.reduce((sum, detail) => {\r\n                                  return sum + (calculatedPrices[detail.productId] || 0) * (detail.quantity || 0);\r\n                                }, 0) || 0\r\n                              )\r\n                              : formatCurrency(orderDetail?.totalPrice)}\r\n                        </div>\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n                </>\r\n              )}\r\n            </div>\r\n          </div>\r\n        )}\r\n\r\n        <DialogFooter>\r\n          <Button variant=\"outline\" onClick={onClose} disabled={isSubmitting || isLoading}>\r\n            Hủy\r\n          </Button>\r\n          <Button onClick={handleSettlement} disabled={isSubmitting || isLoading}>\r\n            {isSubmitting\r\n              ? 'Đang xử lý...'\r\n              : orderDetail?.businessType === BusinessType.IMMEDIATE_DELIVERY\r\n                ? 'Xác nhận rút bạc'\r\n                : withdrawalMode\r\n                  ? 'Xác nhận chuyển đổi và rút bạc'\r\n                  : 'Xác nhận tất toán'\r\n            }\r\n          </Button>\r\n        </DialogFooter>\r\n      </DialogContent>\r\n    </Dialog>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAUA;AAEA;AAAA;AACA;AAAA;AAAA;AACA;AACA;AACA;AACA;;;AArBA;;;;;;;;;;;AAmCO,SAAS,gBAAgB,EAAE,SAAS,EAAE,MAAM,EAAE,OAAO,EAAE,SAAS,EAAE,iBAAiB,KAAK,EAAE,aAAa,IAAI,EAAwB;;IACxI,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,sQAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,sQAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,sQAAA,CAAA,WAAQ,AAAD,EAAoB;IACjE,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,sQAAA,CAAA,WAAQ,AAAD,EAAU;IACvD,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,sQAAA,CAAA,WAAQ,AAAD,EAA6B,CAAC;IACrF,MAAM,CAAC,4BAA4B,8BAA8B,GAAG,CAAA,GAAA,sQAAA,CAAA,WAAQ,AAAD,EAAiB;IAC5F,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,sQAAA,CAAA,WAAQ,AAAD,EAAiB;IACxE,+CAA+C;IAC/C,CAAA,GAAA,sQAAA,CAAA,YAAS,AAAD;qCAAE;YACR,IAAI,UAAU,WAAW;gBACvB,iBAAiB,UAAU,EAAE;gBAC7B,+BAA+B;gBAC/B,eAAe;gBACf,oBAAoB,CAAC;gBACrB,8BAA8B;gBAC9B,oBAAoB;YACtB;QACF;oCAAG;QAAC;QAAQ;KAAU;IAEtB,gDAAgD;IAChD,CAAA,GAAA,sQAAA,CAAA,YAAS,AAAD;qCAAE;YACR,IAAI,UAAU,eAAe,gBAAgB;gBAC3C,iEAAiE;gBACjE,MAAM,kBAAkB,CAAC,YAAY,UAAU,IAAI,CAAC,IAAI,CAAC,YAAY,YAAY,IAAI,CAAC;gBACtF,8BAA8B;YAChC;QACF;oCAAG;QAAC;QAAQ;QAAa;KAAe;IAExC,iEAAiE;IACjE,MAAM,EAAE,YAAY,EAAE,WAAW,EAAE,GAAG,CAAA,GAAA,iKAAA,CAAA,kBAAe,AAAD;IAEpD,4EAA4E;IAC5E,CAAA,GAAA,sQAAA,CAAA,YAAS,AAAD;qCAAE;YACR,IAAI,CAAC,kBAAkB,aAAa,iBAAiB,4KAAA,CAAA,eAAY,CAAC,kBAAkB,IAAI,YAAY;gBAClG,yDAAyD;gBACzD,eAAe,WAAW,GAAG,CAAC,QAAQ;gBACtC,wBAAwB,WAAW,GAAG,CAAC,QAAQ,IAAI,WAAW,GAAG;YACnE;QACF;oCAAG;QAAC;QAAa;QAAgB;KAAW;IAE5C,sEAAsE;IACtE,MAAM,0BAA0B,CAAC,OAAe;QAC9C,eAAe;QAEf,IAAI,CAAC,aAAa,WAAW,YAAY,OAAO,CAAC,MAAM,KAAK,GAAG;QAE/D,MAAM,WAAW,WAAW;QAC5B,IAAI,MAAM,aAAa,YAAY,GAAG;YACpC,oBAAoB,CAAC;YACrB,8BAA8B;YAC9B,oBAAoB;YACpB;QACF;QAEA,8DAA8D;QAC9D,MAAM,YAAuC,CAAC;QAE9C,YAAY,OAAO,CAAC,OAAO,CAAC,CAAA;YAC1B,IAAI,OAAO,OAAO,EAAE;gBAClB,qEAAqE;gBACrE,MAAM,UAAU,OAAO,OAAO,CAAC,MAAM,IAAI;gBACzC,MAAM,QAAQ,YAAY,CAAA,GAAA,+HAAA,CAAA,8BAA2B,AAAD,EAAE,UAAU;gBAChE,SAAS,CAAC,OAAO,SAAS,CAAC,GAAG;YAChC;QACF;QAEA,oBAAoB;QAEpB,6BAA6B;QAC7B,MAAM,gBAAgB,YAAY,OAAO,CAAC,MAAM,CAAC,CAAC,KAAK;YACrD,OAAO,MAAM,CAAC,SAAS,CAAC,OAAO,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,QAAQ,IAAI,CAAC;QACzE,GAAG;QAEH,qDAAqD;QACrD,MAAM,sBAAsB,gBAAgB;QAC5C,8BAA8B;QAE9B,sBAAsB;QACtB,MAAM,qBAAqB,YAAY,UAAU,IAAI;QACrD,MAAM,UAAU,GAAG,yBAAyB;QAC5C,MAAM,SAAS,gBAAgB,qBAAqB;QACpD,oBAAoB;IACtB;IAEA,kCAAkC;IAClC,MAAM,mBAAmB,OAAO;QAC9B,IAAI;YACF,aAAa;YACb,MAAM,WAAW,MAAM,6GAAA,CAAA,MAAG,CAAC,GAAG,CAAC,CAAC,YAAY,EAAE,IAAI;YAClD,eAAe;QACjB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,gCAAgC;YAC9C,2QAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd,SAAU;YACR,aAAa;QACf;IACF;IAEA,qDAAqD;IACrD,MAAM,mBAAmB;QACvB,IAAI;YACF,gBAAgB;YAEhB,4BAA4B;YAC5B,MAAM,UAIF,CAAC;YAEL,kEAAkE;YAClE,IAAI,aAAa,iBAAiB,4KAAA,CAAA,eAAY,CAAC,kBAAkB,EAAE;gBACjE,QAAQ,MAAM,GAAG,aAAa;gBAC9B,mDAAmD;gBACnD,QAAQ,mBAAmB,GAAG;gBAC9B,MAAM,6GAAA,CAAA,MAAG,CAAC,KAAK,CAAC,CAAC,YAAY,EAAE,WAAW,GAAG,iBAAiB,CAAC,EAAE;gBACjE,2QAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YAChB,OAAO,IAAI,gBAAgB;gBACzB,gEAAgE;gBAChE,QAAQ,MAAM,GAAG,aAAa;gBAC9B,QAAQ,KAAK,GAAG,aAAa,cAAc;gBAC3C,QAAQ,mBAAmB,GAAG;gBAC9B,MAAM,6GAAA,CAAA,MAAG,CAAC,KAAK,CAAC,CAAC,YAAY,EAAE,WAAW,GAAG,iBAAiB,CAAC,EAAE;gBACjE,2QAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YAChB,OAAO;gBACL,6CAA6C;gBAC7C,IAAI,YAAY;oBACd,6BAA6B;oBAC7B,QAAQ,MAAM,GAAG,aAAa;oBAC9B,QAAQ,KAAK,GAAG,WAAW,GAAG;gBAChC,OAAO,IAAI,cAAc;oBACvB,uDAAuD;oBACvD,MAAM,YAAY,aAAa,cAAc,4KAAA,CAAA,YAAS,CAAC,GAAG,GACxD,aAAa,YAAY,GAAG,aAAa,WAAW;oBACtD,QAAQ,MAAM,GAAG,aAAa;oBAC9B,QAAQ,KAAK,GAAG;gBAClB,OAAO;oBACL,2QAAA,CAAA,QAAK,CAAC,KAAK,CAAC;oBACZ,gBAAgB;oBAChB;gBACF;gBAEA,MAAM,6GAAA,CAAA,MAAG,CAAC,KAAK,CAAyB,CAAC,YAAY,EAAE,WAAW,GAAG,iBAAiB,CAAC,EAAE;gBACzF,2QAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YAChB;YAEA;YACA;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2BAA2B;YAEzC,8CAA8C;YAC9C,IAAI,aAAa,iBAAiB,4KAAA,CAAA,eAAY,CAAC,kBAAkB,EAAE;gBACjE,2QAAA,CAAA,QAAK,CAAC,KAAK,CAAC,+BAA+B;oBACzC,aAAa,iBAAiB,QAAQ,MAAM,OAAO,GAAG;gBACxD;YACF,OAAO;gBACL,2QAAA,CAAA,QAAK,CAAC,KAAK,CAAC,2BAA2B;oBACrC,aAAa,iBAAiB,QAAQ,MAAM,OAAO,GAAG;gBACxD;YACF;QACF,SAAU;YACR,gBAAgB;QAClB;IACF;IAEA,wCAAwC;IACxC,MAAM,gBAAgB,CAAC;QACrB,OAAQ;YACN,KAAK,4KAAA,CAAA,cAAW,CAAC,SAAS;gBACxB,OAAO;YACT,KAAK,4KAAA,CAAA,cAAW,CAAC,SAAS;gBACxB,OAAO;YACT,KAAK,4KAAA,CAAA,cAAW,CAAC,UAAU;gBACzB,OAAO;YACT,KAAK,4KAAA,CAAA,cAAW,CAAC,SAAS;gBACxB,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,+BAA+B;IAC/B,MAAM,iBAAiB,CAAC;QACtB,OAAQ;YACN,KAAK,4KAAA,CAAA,cAAW,CAAC,SAAS;gBACxB,OAAO;YACT,KAAK,4KAAA,CAAA,cAAW,CAAC,SAAS;gBACxB,OAAO;YACT,KAAK,4KAAA,CAAA,cAAW,CAAC,UAAU;gBACzB,OAAO;YACT,KAAK,4KAAA,CAAA,cAAW,CAAC,SAAS;gBACxB,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,uBAAuB;IACvB,MAAM,cAAc,aAAa,SAAS,OAAO,CAAC,KAAK,SAAW,MAAM,CAAC,OAAO,QAAQ,IAAI,CAAC,GAAG,MAAM;IAEtG,IAAI,CAAC,WAAW,OAAO;IAEvB,qBACE,sSAAC,8HAAA,CAAA,SAAM;QAAC,MAAM;QAAQ,cAAc;kBAClC,cAAA,sSAAC,8HAAA,CAAA,gBAAa;YAAC,WAAU;;8BACvB,sSAAC,8HAAA,CAAA,eAAY;;sCACX,sSAAC,8HAAA,CAAA,cAAW;sCACT,aAAa,iBAAiB,4KAAA,CAAA,eAAY,CAAC,kBAAkB,GAC1D,sBACA,iBACE,kCACA;;;;;;sCAER,sSAAC,8HAAA,CAAA,oBAAiB;sCACf,aAAa,iBAAiB,4KAAA,CAAA,eAAY,CAAC,kBAAkB,GAC1D,kGACA,iBACE,sHACA;;;;;;;;;;;;gBAIT,0BACC,sSAAC;oBAAI,WAAU;;sCACb,sSAAC,wSAAA,CAAA,UAAO;4BAAC,WAAU;;;;;;sCACnB,sSAAC;4BAAK,WAAU;sCAAO;;;;;;;;;;;yCAGzB,sSAAC;oBAAI,WAAU;;sCACb,sSAAC;4BAAI,WAAU;;8CACb,sSAAC;oCAAI,WAAU;;sDACb,sSAAC;4CAAE,WAAU;sDAA4C;;;;;;sDACzD,sSAAC;4CAAE,WAAU;sDAAyB,aAAa,kBAAkB;;;;;;;;;;;;8CAEvE,sSAAC;oCAAI,WAAU;;sDACb,sSAAC;4CAAE,WAAU;sDAA4C;;;;;;sDACzD,sSAAC,6HAAA,CAAA,QAAK;4CAAC,WAAW,eAAe,aAAa,UAAU,4KAAA,CAAA,cAAW,CAAC,SAAS;sDAC1E,cAAc,aAAa,UAAU,4KAAA,CAAA,cAAW,CAAC,SAAS;;;;;;;;;;;;;;;;;;sCAKjE,sSAAC;4BAAI,WAAU;;8CACb,sSAAC;oCAAI,WAAU;;sDACb,sSAAC;4CAAE,WAAU;sDAA4C;;;;;;sDACzD,sSAAC;4CAAE,WAAU;;gDAAyB,YAAY,cAAc;gDAAG;;;;;;;;;;;;;8CAErE,sSAAC;oCAAI,WAAU;;sDACb,sSAAC;4CAAE,WAAU;sDAA4C;;;;;;sDACzD,sSAAC;4CAAE,WAAU;sDAAyB,CAAA,GAAA,+HAAA,CAAA,iBAAc,AAAD,EAAE,aAAa;;;;;;;;;;;;;;;;;;sCAItE,sSAAC;4BAAI,WAAU;;8CACb,sSAAC;oCAAI,WAAU;;sDACb,sSAAC;4CAAE,WAAU;sDAA4C;;;;;;sDACzD,sSAAC;4CAAE,WAAU;sDAAyB,CAAA,GAAA,+HAAA,CAAA,iBAAc,AAAD,EAAE,aAAa;;;;;;;;;;;;8CAEpE,sSAAC;oCAAI,WAAU;;sDACb,sSAAC;4CAAE,WAAU;sDAA4C;;;;;;sDACzD,sSAAC;4CAAE,WAAU;sDACV,aAAa,iBAAiB,4KAAA,CAAA,eAAY,CAAC,kBAAkB,IAAI,CAAC,6BAC/D,CAAA,GAAA,+HAAA,CAAA,iBAAc,AAAD,EAAE,aAAa,mBAC5B,CAAA,GAAA,+HAAA,CAAA,iBAAc,AAAD,EAAE;;;;;;;;;;;;;;;;;;sCAKzB,sSAAC;4BAAI,WAAU;;8CACb,sSAAC;oCAAI,WAAU;;sDACb,sSAAC;4CAAE,WAAU;sDAA4C;;;;;;sDACzD,sSAAC;4CAAE,WAAU;sDAAyB,CAAA,GAAA,+HAAA,CAAA,iBAAc,AAAD,EAAE,aAAa;;;;;;;;;;;;8CAEpE,sSAAC;oCAAI,WAAU;;sDACb,sSAAC;4CAAE,WAAU;sDAA4C;;;;;;sDACzD,sSAAC;4CAAE,WAAU;sDACV,aAAa,qBACV,IAAI,KAAK,YAAY,kBAAkB,EAAE,kBAAkB,CAAC,WAC5D;;;;;;;;;;;;;;;;;;sCAKV,sSAAC;4BAAI,WAAU;sCAEZ,aAAa,iBAAiB,4KAAA,CAAA,eAAY,CAAC,kBAAkB,GAC5D,2DAA2D;0CAC3D,sSAAC;gCAAI,WAAU;;kDACb,sSAAC;wCAAI,WAAU;;0DACb,sSAAC,6HAAA,CAAA,QAAK;gDAAC,WAAU;0DAA4B;;;;;;0DAC7C,sSAAC;gDAAE,WAAU;0DAAgC;;;;;;;;;;;;kDAK/C,sSAAC;wCAAE,WAAU;kDAAsB;;;;;;kDACnC,sSAAC;wCAAI,WAAU;;0DACb,sSAAC;gDAAI,WAAU;;kEACb,sSAAC;kEAAI;;;;;;kEACL,sSAAC;kEAAI;;;;;;kEACL,sSAAC;kEAAI;;;;;;kEACL,sSAAC;kEAAI;;;;;;;;;;;;4CAEN,aAAa,SAAS,IAAI,CAAC,QAAQ,sBAClC,sSAAC;oDAAgB,WAAU;;sEACzB,sSAAC;4DAAI,WAAU;sEAAe,OAAO,OAAO,EAAE,eAAe;;;;;;sEAC7D,sSAAC;;gEAAK,OAAO,QAAQ,EAAE,oBAAoB;gEAAE;;;;;;;sEAC7C,sSAAC;sEAAK,CAAA,GAAA,+HAAA,CAAA,iBAAc,AAAD,EAAE,OAAO,KAAK;;;;;;sEACjC,sSAAC;sEAAK,CAAA,GAAA,+HAAA,CAAA,iBAAc,AAAD,EAAE,OAAO,KAAK,GAAG,OAAO,KAAK,GAAG,CAAC,OAAO,QAAQ,IAAI,CAAC,IAAI;;;;;;;mDAJpE;;;;;0DAOZ,sSAAC;gDAAI,WAAU;;kEACb,sSAAC;kEAAI;;;;;;kEACL,sSAAC;;4DAAK,YAAY,cAAc;4DAAG;;;;;;;kEACnC,sSAAC;;;;;kEACD,sSAAC;kEAAK,CAAA,GAAA,+HAAA,CAAA,iBAAc,AAAD,EAAE,aAAa;;;;;;;;;;;;;;;;;;;;;;;uCAKxC,wDAAwD;0CACxD;;kDACE,sSAAC;wCAAI,WAAU;kDACb,cAAA,sSAAC,6HAAA,CAAA,QAAK;4CAAC,WAAU;sDAAgC;;;;;;;;;;;oCAGlD,iBACC,mDAAmD;kDACnD,sSAAC;wCAAI,WAAU;;0DACb,sSAAC;gDAAE,WAAU;0DAA2B;;;;;;0DACxC,sSAAC;gDAAI,WAAU;;kEACb,sSAAC;wDAAI,WAAU;kEAAwB;;;;;;kEACvC,sSAAC;wDAAI,WAAU;kEAAe,CAAA,GAAA,+HAAA,CAAA,iBAAc,AAAD,EAAE,aAAa;;;;;;kEAE1D,sSAAC;wDAAI,WAAU;kEAAwB;;;;;;kEACvC,sSAAC;wDAAI,WAAU;kEAAe,CAAA,GAAA,+HAAA,CAAA,iBAAc,AAAD,EAAE,aAAa;;;;;;kEAE1D,sSAAC;wDAAI,WAAU;kEAAoC;;;;;;kEACnD,sSAAC;wDAAI,WAAU;kEACZ,CAAA,GAAA,+HAAA,CAAA,iBAAc,AAAD,EAAE,CAAC,aAAa,cAAc,CAAC,IAAI,CAAC,aAAa,gBAAgB,CAAC;;;;;;;;;;;;;;;;;+CAKtF,kEAAkE;kDAClE;;0DACE,sSAAC;gDAAI,WAAU;;kEACb,sSAAC;wDAAI,WAAU;;0EACb,sSAAC;gEAAK,WAAW,CAAC,sBAAsB,EAAE,aAAa,cAAc,4KAAA,CAAA,YAAS,CAAC,GAAG,GAAG,mBAAmB,gBAAgB;0EACrH,CAAA,GAAA,+HAAA,CAAA,iBAAc,AAAD,EAAE,aAAa,WAAW,GAAG,GAAI,eAC7C,aAAa,cAAc,4KAAA,CAAA,YAAS,CAAC,GAAG,GAAG,aAAa,YAAY,GAAG,aAAa,WAAW,GAC7F;;;;;;0EAGN,sSAAC;gEAAE,WAAU;;oEAAgC;oEACzC,aAAa,WAAW,GAAG,CAAC,OAAO,CAAC,KAAM,eAC1C,CAAC,aAAa,cAAc,4KAAA,CAAA,YAAS,CAAC,GAAG,GAAG,aAAa,QAAQ,GAAG,aAAa,QAAQ,EAAE,OAAO,CAAC,KACjG;;;;;;;;;;;;;kEAIR,sSAAC;wDAAK,WAAW,CAAC,kBAAkB,EAAE,aAAa,cAAc,4KAAA,CAAA,YAAS,CAAC,GAAG,GAAG,mBAAmB,gBAAgB;kEACjH,aAAa,cAAc,4KAAA,CAAA,YAAS,CAAC,GAAG,iBACvC,sSAAC,ySAAA,CAAA,aAAU;4DAAC,WAAU;;;;;iFACtB,sSAAC,6SAAA,CAAA,eAAY;4DAAC,WAAU;;;;;;;;;;;;;;;;;4CAM7B,qBAAqB,sBACpB,sSAAC;gDAAI,WAAW,CAAC,oBAAoB,EAAE,oBAAoB,IAAI,gBAAgB,aAAa;0DAC1F,cAAA,sSAAC;oDAAE,WAAU;;wDAAsB;sEAEjC,sSAAC;4DAAK,WAAW,CAAC,mBAAmB,EAAE,oBAAoB,IAAI,mBAAmB,gBAAgB;sEAC/F,CAAA,GAAA,+HAAA,CAAA,iBAAc,AAAD,EAAE;;;;;;;;;;;;;;;;;;;kDAQ5B,sSAAC;wCAAI,WAAU;;0DACb,sSAAC;gDAAE,WAAU;0DAAsB;;;;;;0DACnC,sSAAC;gDAAI,WAAU;;kEACb,sSAAC;wDAAI,WAAU;;0EACb,sSAAC;0EAAI;;;;;;0EACL,sSAAC;0EAAI;;;;;;0EACL,sSAAC;0EAAI;;;;;;0EACL,sSAAC;0EAAI;;;;;;;;;;;;oDAEN,aAAa,SAAS,IAAI,CAAC,QAAQ,sBAClC,sSAAC;4DAAgB,WAAU;;8EACzB,sSAAC;oEAAI,WAAU;8EAAe,OAAO,OAAO,EAAE,eAAe;;;;;;8EAC7D,sSAAC;;wEAAK,OAAO,QAAQ,EAAE,oBAAoB;wEAAE;;;;;;;8EAC7C,sSAAC;8EACE,iBACG,CAAA,GAAA,+HAAA,CAAA,iBAAc,AAAD,EAAE,OAAO,KAAK,IAC3B,gBAAgB,CAAC,OAAO,SAAS,CAAC,GAChC,CAAA,GAAA,+HAAA,CAAA,iBAAc,AAAD,EAAE,gBAAgB,CAAC,OAAO,SAAS,CAAC,IACjD,CAAA,GAAA,+HAAA,CAAA,iBAAc,AAAD,EAAE,OAAO,KAAK;;;;;;8EAEnC,sSAAC;8EACE,iBACG,CAAA,GAAA,+HAAA,CAAA,iBAAc,AAAD,EAAE,OAAO,KAAK,GAAG,OAAO,KAAK,GAAG,CAAC,OAAO,QAAQ,IAAI,CAAC,IAAI,KACtE,gBAAgB,CAAC,OAAO,SAAS,CAAC,GAChC,CAAA,GAAA,+HAAA,CAAA,iBAAc,AAAD,EAAE,gBAAgB,CAAC,OAAO,SAAS,CAAC,GAAG,CAAC,OAAO,QAAQ,IAAI,CAAC,KACzE,CAAA,GAAA,+HAAA,CAAA,iBAAc,AAAD,EAAE,OAAO,KAAK,GAAG,OAAO,KAAK,GAAG,CAAC,OAAO,QAAQ,IAAI,CAAC,IAAI;;;;;;;2DAftE;;;;;kEAmBZ,sSAAC;wDAAI,WAAU;;0EACb,sSAAC;0EAAI;;;;;;0EACL,sSAAC;;oEAAK,YAAY,cAAc;oEAAG;;;;;;;0EACnC,sSAAC;;;;;0EACD,sSAAC;0EACE,iBACG,CAAA,GAAA,+HAAA,CAAA,iBAAc,AAAD,EAAE,aAAa,cAC5B,OAAO,IAAI,CAAC,kBAAkB,MAAM,GAAG,IACrC,CAAA,GAAA,+HAAA,CAAA,iBAAc,AAAD,EACb,aAAa,SAAS,OAAO,CAAC,KAAK;oEACjC,OAAO,MAAM,CAAC,gBAAgB,CAAC,OAAO,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,QAAQ,IAAI,CAAC;gEAChF,GAAG,MAAM,KAET,CAAA,GAAA,+HAAA,CAAA,iBAAc,AAAD,EAAE,aAAa;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAWpD,sSAAC,8HAAA,CAAA,eAAY;;sCACX,sSAAC,8HAAA,CAAA,SAAM;4BAAC,SAAQ;4BAAU,SAAS;4BAAS,UAAU,gBAAgB;sCAAW;;;;;;sCAGjF,sSAAC,8HAAA,CAAA,SAAM;4BAAC,SAAS;4BAAkB,UAAU,gBAAgB;sCAC1D,eACG,kBACA,aAAa,iBAAiB,4KAAA,CAAA,eAAY,CAAC,kBAAkB,GAC3D,qBACA,iBACE,mCACA;;;;;;;;;;;;;;;;;;;;;;;AAOpB;GAxcgB;;QA8BwB,iKAAA,CAAA,kBAAe;;;KA9BvC", "debugId": null}}, {"offset": {"line": 8558, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/bloomgoo/gold-exchange-platform/frontend/components/common/admin/order-books/order-books.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { useState, useEffect, useCallback, useRef } from 'react';\r\nimport { Plus, FileDown } from 'lucide-react';\r\nimport { DataTable } from '../../data-table/data-table';\r\nimport { api } from '@/lib/api';\r\nimport { ApproveStatus, BusinessType, OrderBook, OrderStatus, OrderType } from './type/order-books';\r\nimport { getOrderBookColumns } from './table/order-book-cell';\r\nimport { Checkbox } from '@/components/ui/checkbox';\r\nimport { useReactTable, ColumnFiltersState, PaginationState, RowSelectionState, SortingState, VisibilityState, getCoreRowModel, getSortedRowModel, getFilteredRowModel, Updater } from '@tanstack/react-table';\r\nimport { TableToolbar } from '../../data-table/table-toolbar';\r\nimport { TableFooter } from '../../data-table/table-footer';\r\nimport { But<PERSON> } from '@/components/ui/button';\r\nimport { PaginationResponse, SortOrder } from '@/lib/response';\r\nimport { toast } from \"sonner\";\r\nimport { OrderBookFormModal } from './order-book-form-modal';\r\nimport { FloatDeleteButton } from './components/float-delete-button';\r\nimport { OrderBusinessTypeTabs, OrderBusinessTypeFilter } from './components/order-business-type-tabs';\r\nimport { OrderBookDetailSheet } from './order-book-detail-sheet';\r\nimport { SettlementModal } from './settlement-modal';\r\nimport {\r\n  AlertDialog,\r\n  AlertDialogAction,\r\n  AlertDialogCancel,\r\n  AlertDialogContent,\r\n  AlertDialogDescription,\r\n  AlertDialogFooter,\r\n  AlertDialogHeader,\r\n  AlertDialogTitle,\r\n} from \"@/components/ui/alert-dialog\";\r\nimport { usePolygonForex } from '@/services/websocket/polygon-forex-socket.service';\r\n\r\n// Define interface for order book statistics response\r\ninterface OrderBookStatistics {\r\n  total: number;\r\n  businessTypeCounts: {\r\n    NORMAL: number;\r\n    IMMEDIATE_DELIVERY: number;\r\n  };\r\n}\r\n\r\nexport default function OrderBooks() {\r\n  const [orderBooks, setOrderBooks] = useState<OrderBook[]>([]);\r\n  const [loading, setLoading] = useState(true);\r\n  const [sorting, setSorting] = useState<SortingState>([]);\r\n  const sortingRef = useRef<SortingState>([]);\r\n  const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([]);\r\n  const [pagination, setPagination] = useState<PaginationState>({\r\n    pageIndex: 0,\r\n    pageSize: 20,\r\n  });\r\n  const [meta, setMeta] = useState({\r\n    page: 1,\r\n    limit: 10,\r\n    itemCount: 0,\r\n    pageCount: 0,\r\n    hasPreviousPage: false,\r\n    hasNextPage: false\r\n  });\r\n  const [globalFilter, setGlobalFilter] = useState(\"\");\r\n  const [rowSelection, setRowSelection] = useState<RowSelectionState>({});\r\n  const [columnVisibility, setColumnVisibility] = useState<VisibilityState>({});\r\n  const [isRefreshing, setIsRefreshing] = useState(false);\r\n  const [selectedOrderBook, setSelectedOrderBook] = useState<OrderBook | null>(null);\r\n  const [showDetail, setShowDetail] = useState(false);\r\n  const [isUpdating, setIsUpdating] = useState(false);\r\n  const [businessTypeFilter, setBusinessTypeFilter] = useState<OrderBusinessTypeFilter>('all');\r\n\r\n  // State để lưu trữ số lượng thống kê từ API statistics\r\n  const [statisticsCounts, setStatisticsCounts] = useState({ all: 0, normal: 0, immediateDelivery: 0 });\r\n  const [showOrderBookFormModal, setShowOrderBookFormModal] = useState(false)\r\n  const [formMode, setFormMode] = useState<'create' | 'update' | 'view'>('create')\r\n  const [showSettlementModal, setShowSettlementModal] = useState(false)\r\n  const [withdrawalMode, setWithdrawalMode] = useState(false)\r\n  const [showWithdrawalConfirm, setShowWithdrawalConfirm] = useState(false)\r\n  const [fixedSettlementPrice, setFixedSettlementPrice] = useState<{ usd: number; vnd: number; } | null>(null);\r\n\r\n  // Add real-time forex quote\r\n  const { currentQuote, isConnected } = usePolygonForex();\r\n\r\n  // Custom sorting state handler that updates both state and ref\r\n  const handleSortingChange = useCallback((updaterOrValue: Updater<SortingState>) => {\r\n    // Handle both direct value and updater function\r\n    setSorting(updaterOrValue);\r\n\r\n    // Update ref with the new value\r\n    if (typeof updaterOrValue === 'function') {\r\n      // If it's an updater function, call it with current state to get new value\r\n      const newValue = updaterOrValue(sorting);\r\n      sortingRef.current = newValue;\r\n    } else {\r\n      // If it's a direct value\r\n      sortingRef.current = updaterOrValue;\r\n    }\r\n  }, [sorting]);\r\n\r\n  // Xử lý xem chi tiết lệnh\r\n  const handleViewDetail = (orderBook: OrderBook) => {\r\n    setSelectedOrderBook(orderBook);\r\n    setShowDetail(true);\r\n    toast.info(`Đang xem chi tiết lệnh #${orderBook.contractNumber}`);\r\n  };\r\n\r\n  // Xử lý chỉnh sửa lệnh\r\n  const handleEdit = (orderBook: OrderBook) => {\r\n    setSelectedOrderBook(orderBook);\r\n    setFormMode('update');\r\n    setShowOrderBookFormModal(true);\r\n    toast.info(`Đang chỉnh sửa lệnh #${orderBook.contractNumber}`);\r\n  };\r\n\r\n  // Xử lý tạo lệnh mới\r\n  const handleCreateNew = () => {\r\n    setSelectedOrderBook(null);\r\n    setFormMode('create');\r\n    setShowOrderBookFormModal(true);\r\n  };\r\n\r\n  // Xử lý xóa lệnh\r\n  const handleDelete = async (orderBook: OrderBook) => {\r\n    try {\r\n      setIsUpdating(true);\r\n      await api.delete(`order-books/${orderBook.id}`);\r\n      toast.success(`Đã xóa lệnh #${orderBook.contractNumber}`);\r\n      fetchOrderBooks();\r\n    } catch (error) {\r\n      console.error('Error deleting order book:', error);\r\n      toast.error(\"Không thể xóa lệnh\");\r\n    } finally {\r\n      setIsUpdating(false);\r\n    }\r\n  };\r\n\r\n  // Xử lý thay đổi trạng thái lệnh\r\n  const handleChangeStatus = async (orderBook: OrderBook, status: OrderStatus) => {\r\n    try {\r\n      // Nếu là trạng thái COMPLETED và là lệnh mua/bán, hiển thị modal tất toán\r\n      if (status === OrderStatus.COMPLETED && orderBook.orderType !== 'WITHDRAWAL') {\r\n        setSelectedOrderBook(orderBook);\r\n\r\n        // Kiểm tra nếu là lệnh mua bạc ký quỹ online và khối lượng >= 10\r\n        if (\r\n          orderBook.orderType === 'BUY' &&\r\n          orderBook.businessType === BusinessType.NORMAL\r\n        ) {\r\n          // Tính tổng khối lượng bạc\r\n          const totalVolume = orderBook.details?.reduce((sum, detail) => sum + (detail.quantity || 0), 0) || 0;\r\n\r\n          if (totalVolume >= 10) {\r\n            // Hiển thị dialog xác nhận rút bạc\r\n            setSelectedOrderBook(orderBook);\r\n            setShowWithdrawalConfirm(true);\r\n            return;\r\n          }\r\n        }\r\n\r\n        // Kiểm tra và set giá cố định\r\n        if (!currentQuote) {\r\n          toast.error('Vui lòng chờ kết nối tới máy chủ giá');\r\n          return;\r\n        }\r\n\r\n        // Lưu giá cố định tại thời điểm mở modal\r\n        const fixedPrice = {\r\n          usd: orderBook.orderType === OrderType.BUY ? currentQuote.askPrice : currentQuote.bidPrice,\r\n          vnd: orderBook.orderType === OrderType.BUY ? currentQuote.sellPriceVND : currentQuote.buyPriceVND\r\n        };\r\n        setFixedSettlementPrice(fixedPrice);\r\n\r\n        // Nếu là bạc giao ngay hoặc không đủ điều kiện rút bạc\r\n        setWithdrawalMode(false);\r\n        setShowSettlementModal(true);\r\n        return;\r\n      }\r\n\r\n      setIsUpdating(true);\r\n\r\n      // Sử dụng các API endpoint mới theo trạng thái cụ thể\r\n      let endpoint = '';\r\n\r\n      // Phân tách xử lý dựa trên loại lệnh\r\n      if (orderBook.orderType === 'WITHDRAWAL') {\r\n        // Xử lý cho lệnh rút token\r\n        switch (status) {\r\n          case OrderStatus.CANCELLED:\r\n            endpoint = `/order-books/${orderBook.id}/status/cancelled`;\r\n            break;\r\n          default:\r\n            throw new Error(`Trạng thái không hợp lệ cho lệnh rút: ${status}`);\r\n        }\r\n      } else {\r\n        // Xử lý cho lệnh mua/bán\r\n        switch (status) {\r\n          case OrderStatus.DEPOSITED:\r\n            endpoint = `/order-books/${orderBook.id}/status/deposited`;\r\n            break;\r\n          case OrderStatus.TERMINATED:\r\n            endpoint = `/order-books/${orderBook.id}/status/terminated`;\r\n            break;\r\n          default:\r\n            throw new Error(`Trạng thái không hợp lệ cho lệnh mua/bán: ${status}`);\r\n        }\r\n      }\r\n\r\n      // Gọi API với endpoint tương ứng với trạng thái\r\n      if (endpoint) {\r\n        await api.patch(endpoint);\r\n\r\n        // Cập nhật dữ liệu local\r\n        const updatedOrderBooks = orderBooks.map(ob => {\r\n          if (ob.id === orderBook.id) {\r\n            return {\r\n              ...ob,\r\n              status\r\n            };\r\n          }\r\n          return ob;\r\n        });\r\n\r\n        setOrderBooks(updatedOrderBooks);\r\n        toast.success(`Đã cập nhật trạng thái lệnh #${orderBook.contractNumber} thành ${status}`);\r\n      }\r\n    } catch (error) {\r\n      console.error('Error changing order book status:', error);\r\n      toast.error(\"Không thể thay đổi trạng thái lệnh\");\r\n    } finally {\r\n      setIsUpdating(false);\r\n      fetchOrderBooks();\r\n    }\r\n  };\r\n\r\n  // Các hàm xử lý thay đổi giá và khối lượng đã được loại bỏ vì không sử dụng\r\n\r\n  // Xử lý gia hạn thời gian tất toán\r\n  const handleExtendSettlement = async (orderBook: OrderBook) => {\r\n    try {\r\n      setIsUpdating(true);\r\n      await api.patch(`order-books/${orderBook.id}/extend-settlement`);\r\n\r\n      // Tính thời gian tất toán mới (thêm 15 ngày)\r\n      const currentDeadline = new Date(orderBook.settlementDeadline || new Date());\r\n      const newDeadline = new Date(currentDeadline);\r\n      newDeadline.setDate(newDeadline.getDate() + 15);\r\n\r\n      // Cập nhật dữ liệu local\r\n      const updatedOrderBooks = orderBooks.map(ob => {\r\n        if (ob.id === orderBook.id) {\r\n          return {\r\n            ...ob,\r\n            settlementDeadline: newDeadline\r\n          };\r\n        }\r\n        return ob;\r\n      });\r\n\r\n      setOrderBooks(updatedOrderBooks);\r\n      toast.success(`Đã gia hạn thời gian tất toán cho lệnh #${orderBook.contractNumber} thêm 15 ngày`);\r\n\r\n      // Refresh để lấy dữ liệu mới nhất\r\n      fetchOrderBooks();\r\n    } catch (error) {\r\n      console.error('Error extending settlement deadline:', error);\r\n      toast.error(\"Không thể gia hạn thời gian tất toán\", {\r\n        description: error instanceof Error ? error.message : \"Vui lòng thử lại sau.\"\r\n      });\r\n    } finally {\r\n      setIsUpdating(false);\r\n    }\r\n  };\r\n\r\n  // Xử lý thay đổi trạng thái phê duyệt\r\n  const handleChangeApproveStatus = async (orderBook: OrderBook, approveStatus: ApproveStatus) => {\r\n    try {\r\n      setIsUpdating(true);\r\n\r\n      // Chỉ hỗ trợ phê duyệt (chuyển từ WAIT_APPROVE sang APPROVED)\r\n      if (approveStatus === ApproveStatus.APPROVED) {\r\n        await api.patch(`order-books/${orderBook.id}/approve`);\r\n\r\n        // Cập nhật dữ liệu local\r\n        const updatedOrderBooks = orderBooks.map(ob => {\r\n          if (ob.id === orderBook.id) {\r\n            return {\r\n              ...ob,\r\n              approveStatus: ApproveStatus.APPROVED,\r\n              approvedAt: new Date()\r\n            };\r\n          }\r\n          return ob;\r\n        });\r\n\r\n        setOrderBooks(updatedOrderBooks);\r\n        toast.success(`Đã phê duyệt lệnh #${orderBook.id}`);\r\n      } else {\r\n        // Hiện tại API chưa hỗ trợ chuyển về trạng thái chờ phê duyệt\r\n        toast.error(\"Chức năng chuyển về trạng thái chờ phê duyệt chưa được hỗ trợ\");\r\n      }\r\n\r\n      // Refresh để lấy dữ liệu mới nhất\r\n      fetchOrderBooks();\r\n    } catch (error) {\r\n      console.error('Error changing approve status:', error);\r\n      toast.error(\"Không thể thay đổi trạng thái phê duyệt\", {\r\n        description: error instanceof Error ? error.message : \"Vui lòng thử lại sau.\"\r\n      });\r\n    } finally {\r\n      setIsUpdating(false);\r\n    }\r\n  };\r\n\r\n  const fetchOrderBooks = async () => {\r\n    setLoading(true);\r\n    try {\r\n      // Use the ref for sorting\r\n      let sortByParam = '';\r\n      let sortOrderParam = '';\r\n      if (sortingRef.current.length > 0) {\r\n        const firstSort = sortingRef.current[0];\r\n        sortByParam = `&sortBy=${firstSort.id}`;\r\n        sortOrderParam = `&sortOrder=${firstSort.desc ? 'DESC' : 'ASC'}`;\r\n      }\r\n\r\n      // Tạo query string cho search\r\n      let searchParam = '';\r\n\r\n      // Chỉ sử dụng globalFilter cho tìm kiếm\r\n      if (globalFilter) {\r\n        searchParam = `&search=${encodeURIComponent(globalFilter)}`;\r\n      }\r\n\r\n      // Xử lý filter theo loại hình giao dịch\r\n      let filterParam = '';\r\n\r\n      // Thêm filter theo loại hình giao dịch\r\n      if (businessTypeFilter !== 'all') {\r\n        filterParam = `&filter=businessType:${businessTypeFilter}`;\r\n      }\r\n\r\n      const response = await api.get<PaginationResponse<OrderBook>>(\r\n        `order-books?page=${pagination.pageIndex + 1}&limit=${pagination.pageSize}${sortByParam}${sortOrderParam}${searchParam}${filterParam}`\r\n      );\r\n\r\n      if (response && response.data) {\r\n        // Sử dụng dữ liệu trực tiếp từ API mà không cần lọc ở client\r\n        setOrderBooks(response.data);\r\n\r\n        if (response.meta) {\r\n          setMeta(response.meta);\r\n        }\r\n\r\n        // Không cập nhật businessTypeCounts ở đây nữa\r\n        // Chúng ta sẽ sử dụng số liệu từ API statistics\r\n      } else {\r\n        setOrderBooks([]);\r\n        setMeta({\r\n          page: 1,\r\n          limit: pagination.pageSize,\r\n          itemCount: 0,\r\n          pageCount: 0,\r\n          hasPreviousPage: false,\r\n          hasNextPage: false\r\n        });\r\n      }\r\n    } catch (error: any) {\r\n      console.error('Error fetching order books:', error);\r\n\r\n      // Kiểm tra lỗi 401/403\r\n      if (error.status === 401) {\r\n        toast.error(\"Bạn chưa đăng nhập hoặc phiên đăng nhập đã hết hạn\");\r\n      } else if (error.status === 403) {\r\n        toast.error(\"Bạn không có quyền truy cập vào tài nguyên này\");\r\n      } else if (error.status === 404) {\r\n        toast.error(\"Không tìm thấy tài nguyên\");\r\n      } else {\r\n        toast.error(\"Không thể tải dữ liệu sổ lệnh\");\r\n      }\r\n\r\n      // Đặt giá trị mặc định khi có lỗi\r\n      setOrderBooks([]);\r\n      setMeta({\r\n        page: 1,\r\n        limit: pagination.pageSize,\r\n        itemCount: 0,\r\n        pageCount: 0,\r\n        hasPreviousPage: false,\r\n        hasNextPage: false\r\n      });\r\n      // Không cần cập nhật businessTypeCounts ở đây nữa\r\n    } finally {\r\n      setLoading(false);\r\n      setIsRefreshing(false);\r\n    }\r\n  };\r\n\r\n  const handleDeleteSelected = async () => {\r\n    try {\r\n      setIsUpdating(true);\r\n      const selectedRows = table.getSelectedRowModel().rows;\r\n      const selectedOrderBooks = selectedRows.map(row => row.original);\r\n\r\n      if (selectedOrderBooks.length === 0) {\r\n        toast.error(\"Vui lòng chọn ít nhất một lệnh để xóa\");\r\n        return;\r\n      }\r\n\r\n      // Thực hiện xóa từng lệnh đã chọn\r\n      const deletePromises = selectedOrderBooks.map(orderBook =>\r\n        api.delete(`order-books/${orderBook.id}`)\r\n      );\r\n\r\n      await Promise.all(deletePromises);\r\n\r\n      // Cập nhật UI\r\n      toast.success(`Đã xóa ${selectedOrderBooks.length} lệnh thành công`);\r\n\r\n      // Reset selection\r\n      table.resetRowSelection();\r\n\r\n      // Refresh data\r\n      await fetchOrderBooks();\r\n    } catch (error) {\r\n      console.error('Error deleting order books:', error);\r\n      toast.error(\"Không thể xóa các lệnh đã chọn\");\r\n    } finally {\r\n      setIsUpdating(false);\r\n    }\r\n  };\r\n\r\n  const table = useReactTable({\r\n    data: orderBooks,\r\n    columns: [\r\n      {\r\n        id: 'select',\r\n        size: 40,\r\n        header: ({ table }) => (\r\n          <div className=\"px-1\">\r\n            <Checkbox\r\n              checked={\r\n                table.getIsAllPageRowsSelected() ||\r\n                (table.getIsSomePageRowsSelected() && \"indeterminate\")\r\n              }\r\n              onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}\r\n              aria-label=\"Select all\"\r\n              className=\"translate-y-[2px]\"\r\n            />\r\n          </div>\r\n        ),\r\n        cell: ({ row }) => (\r\n          <div className=\"px-1\">\r\n            <Checkbox\r\n              checked={row.getIsSelected()}\r\n              onCheckedChange={(value) => row.toggleSelected(!!value)}\r\n              onClick={(e) => e.stopPropagation()}\r\n              aria-label=\"Select row\"\r\n              className=\"translate-y-[2px]\"\r\n            />\r\n          </div>\r\n        ),\r\n        enableSorting: false,\r\n        enableHiding: false,\r\n      },\r\n      ...getOrderBookColumns({\r\n        onViewDetail: handleViewDetail,\r\n        onDelete: handleDelete,\r\n        onEdit: handleEdit,\r\n        onChangeStatus: handleChangeStatus,\r\n        onExtendSettlement: handleExtendSettlement,\r\n        onChangeApproveStatus: handleChangeApproveStatus,\r\n        currentQuote: currentQuote\r\n      })\r\n    ],\r\n    state: {\r\n      sorting,\r\n      columnFilters,\r\n      pagination,\r\n      rowSelection,\r\n      columnVisibility,\r\n      globalFilter,\r\n    },\r\n    pageCount: meta.pageCount,\r\n    onSortingChange: handleSortingChange,\r\n    onColumnFiltersChange: setColumnFilters,\r\n    onPaginationChange: setPagination,\r\n    onRowSelectionChange: setRowSelection,\r\n    onColumnVisibilityChange: setColumnVisibility,\r\n    onGlobalFilterChange: setGlobalFilter,\r\n    getCoreRowModel: getCoreRowModel(),\r\n    getSortedRowModel: getSortedRowModel(),\r\n    getFilteredRowModel: getFilteredRowModel(),\r\n    manualPagination: true,\r\n    enableSorting: true,\r\n    enableColumnFilters: true,\r\n    enableRowSelection: true,\r\n    enableMultiSort: false,\r\n    manualSorting: false,\r\n    manualFiltering: false,\r\n  });\r\n\r\n  const handleRefresh = useCallback(async () => {\r\n    setIsRefreshing(true);\r\n    try {\r\n      // Đặt lại các bộ lọc và sắp xếp\r\n      table.resetColumnFilters();\r\n      table.resetSorting();\r\n      setBusinessTypeFilter('all');\r\n      setGlobalFilter('');\r\n\r\n      // Fetch dữ liệu mới và thống kê\r\n      await Promise.all([\r\n        fetchOrderBooks(),\r\n        fetchStatistics()\r\n      ]);\r\n    } finally {\r\n      setTimeout(() => {\r\n        setIsRefreshing(false);\r\n      }, 1000);\r\n    }\r\n  }, [table]);\r\n\r\n  // Xử lý xuất dữ liệu\r\n  const handleExport = async () => {\r\n    try {\r\n      // Hiển thị thông báo đang xuất dữ liệu\r\n      toast.info(`Đang xuất dữ liệu sổ lệnh...`);\r\n\r\n      // Lấy token xác thực từ localStorage\r\n      const token = localStorage.getItem('accessToken');\r\n\r\n      // Tạo URL API\r\n      const apiUrl = `${api.baseUrl}/order-books/export`;\r\n\r\n      // Sử dụng XMLHttpRequest để tải file\r\n      const xhr = new XMLHttpRequest();\r\n      xhr.open('GET', apiUrl, true);\r\n      xhr.responseType = 'blob';\r\n\r\n      // Thêm token vào header\r\n      if (token) {\r\n        xhr.setRequestHeader('Authorization', `Bearer ${token}`);\r\n      }\r\n\r\n      // Xử lý khi tải xong\r\n      xhr.onload = function() {\r\n        if (this.status === 200) {\r\n          // Tạo blob từ response\r\n          const blob = new Blob([this.response], {\r\n            type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'\r\n          });\r\n\r\n          // Tạo URL cho blob\r\n          const url = window.URL.createObjectURL(blob);\r\n\r\n          // Tạo thẻ a để tải xuống\r\n          const a = document.createElement('a');\r\n          a.href = url;\r\n          a.download = `order-books-${new Date().toISOString().split('T')[0]}.xlsx`;\r\n          document.body.appendChild(a);\r\n          a.click();\r\n\r\n          // Dọn dẹp\r\n          setTimeout(() => {\r\n            document.body.removeChild(a);\r\n            window.URL.revokeObjectURL(url);\r\n          }, 0);\r\n\r\n          toast.success(`Đã xuất dữ liệu sổ lệnh thành công`);\r\n        } else {\r\n          toast.error(`Không thể xuất dữ liệu sổ lệnh: ${this.statusText}`);\r\n        }\r\n      };\r\n\r\n      // Xử lý lỗi\r\n      xhr.onerror = function() {\r\n        toast.error(\"Không thể xuất dữ liệu sổ lệnh: Lỗi kết nối\");\r\n      };\r\n\r\n      // Gửi request\r\n      xhr.send();\r\n    } catch (error) {\r\n      console.error('Error exporting order books:', error);\r\n      toast.error(\"Không thể xuất dữ liệu sổ lệnh\");\r\n    }\r\n  };\r\n\r\n  // Hàm lấy thống kê số lượng theo loại hình kinh doanh\r\n  const fetchStatistics = async () => {\r\n    try {\r\n      const response = await api.get<OrderBookStatistics>('order-books/statistics');\r\n      if (response) {\r\n        // Lưu trữ số lượng thống kê tổng cộng\r\n        const statsData = {\r\n          all: response.total || 0,\r\n          normal: response.businessTypeCounts?.NORMAL || 0,\r\n          immediateDelivery: response.businessTypeCounts?.IMMEDIATE_DELIVERY || 0\r\n        };\r\n\r\n        // Cập nhật state thống kê\r\n        setStatisticsCounts(statsData);\r\n\r\n        // Log thông tin để debug\r\n         \r\n      }\r\n    } catch (error) {\r\n      console.error('Error fetching statistics:', error);\r\n      toast.error('Không thể tải thống kê. Vui lòng thử lại sau.');\r\n\r\n      // Đặt giá trị mặc định khi có lỗi\r\n      const defaultStats = {\r\n        all: 0,\r\n        normal: 0,\r\n        immediateDelivery: 0\r\n      };\r\n\r\n      setStatisticsCounts(defaultStats);\r\n    }\r\n  };\r\n\r\n  // Xử lý khi click nút tất toán\r\n  const handleSettlement = (orderBook: OrderBook) => {\r\n    if (!currentQuote) {\r\n      toast.error('Vui lòng chờ kết nối tới máy chủ giá');\r\n      return;\r\n    }\r\n\r\n    setSelectedOrderBook(orderBook);\r\n\r\n    // Lưu giá cố định tại thời điểm mở modal\r\n    const fixedPrice = {\r\n      usd: orderBook.orderType === OrderType.BUY ? currentQuote.askPrice : currentQuote.bidPrice,\r\n      vnd: orderBook.orderType === OrderType.BUY ? currentQuote.sellPriceVND : currentQuote.buyPriceVND\r\n    };\r\n    setFixedSettlementPrice(fixedPrice);\r\n    \r\n    setShowSettlementModal(true);\r\n  };\r\n\r\n  // Xử lý đóng modal tất toán\r\n  const handleCloseSettlementModal = () => {\r\n    setShowSettlementModal(false);\r\n    setSelectedOrderBook(null);\r\n    setFixedSettlementPrice(null);\r\n  };\r\n\r\n  useEffect(() => {\r\n    fetchOrderBooks();\r\n  }, [pagination.pageIndex, pagination.pageSize, businessTypeFilter, sorting, globalFilter]);\r\n\r\n  useEffect(() => {\r\n    fetchStatistics();\r\n  }, []);\r\n\r\n  return (\r\n    <div className=\"w-full flex flex-col h-full\">\r\n      {/* Header Navigation */}\r\n      <div className=\"w-full flex justify-between items-center border-b py-1.5 px-6 h-10\">\r\n        <div className=\"flex items-center gap-2\">\r\n          <div className=\"flex items-center gap-1\">\r\n            <span className=\"text-sm font-medium\">Sổ Lệnh</span>\r\n            <span className=\"text-xs bg-accent rounded-md px-1.5 py-1\">{statisticsCounts.all}</span>\r\n          </div>\r\n        </div>\r\n        <div className=\"flex items-center gap-2\">\r\n          {isUpdating && (\r\n            <span className=\"text-xs text-muted-foreground\">Đang cập nhật...</span>\r\n          )}\r\n          <div className=\"flex items-center gap-2\">\r\n            <Button className=\"relative\" size=\"sm\" variant=\"secondary\" onClick={handleExport}>\r\n              <FileDown className=\"size-4 mr-1\" />\r\n              Xuất Dữ Liệu\r\n            </Button>\r\n            <Button className=\"relative\" size=\"sm\" variant=\"secondary\" onClick={handleCreateNew}>\r\n              <Plus className=\"size-4 mr-1\" />\r\n              Tạo Lệnh Mới\r\n            </Button>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Table Toolbar with Status Tabs */}\r\n      <TableToolbar\r\n        table={table}\r\n        globalFilter={globalFilter}\r\n        setGlobalFilter={setGlobalFilter}\r\n        onRefresh={handleRefresh}\r\n        isRefreshing={isRefreshing}\r\n        beforeSearchSlot={\r\n          <OrderBusinessTypeTabs\r\n            currentStatus={businessTypeFilter}\r\n            onStatusChange={setBusinessTypeFilter}\r\n            counts={statisticsCounts}\r\n            className=\"w-fit\"\r\n          />\r\n        }\r\n      />\r\n\r\n      {/* Data Table */}\r\n      <div className=\"flex-1 overflow-auto\">\r\n        <DataTable\r\n          table={table}\r\n          className=\"w-full\"\r\n          isLoading={loading}\r\n        />\r\n      </div>\r\n\r\n      {/* Fixed Pagination Footer */}\r\n      <TableFooter\r\n        table={table}\r\n        totalItems={meta.itemCount}\r\n      />\r\n\r\n      {/* Float Delete Button */}\r\n      <FloatDeleteButton\r\n        selectedCount={table.getFilteredSelectedRowModel().rows.length}\r\n        onDelete={handleDeleteSelected}\r\n      />\r\n\r\n      <OrderBookDetailSheet\r\n        orderBook={selectedOrderBook}\r\n        isOpen={showDetail}\r\n        onClose={() => setShowDetail(false)}\r\n        onEdit={handleEdit}\r\n      />\r\n\r\n      <OrderBookFormModal\r\n        isOpen={showOrderBookFormModal}\r\n        onClose={() => setShowOrderBookFormModal(false)}\r\n        orderBook={selectedOrderBook}\r\n        mode={formMode}\r\n        onSuccess={handleRefresh}\r\n        fixedPrice={currentQuote ? {\r\n          usd: currentQuote.askPrice,\r\n          vnd: currentQuote.sellPriceVND\r\n        } : null}\r\n      />\r\n\r\n      {/* Modal tất toán lệnh */}\r\n      <SettlementModal\r\n        orderBook={selectedOrderBook}\r\n        isOpen={showSettlementModal}\r\n        onClose={handleCloseSettlementModal}\r\n        onSuccess={() => {\r\n          handleCloseSettlementModal();\r\n          fetchOrderBooks();\r\n        }}\r\n        withdrawalMode={withdrawalMode}\r\n        fixedPrice={fixedSettlementPrice}\r\n      />\r\n\r\n      {/* Confirmation Dialog for Silver Withdrawal */}\r\n      <AlertDialog open={showWithdrawalConfirm} onOpenChange={setShowWithdrawalConfirm}>\r\n        <AlertDialogContent>\r\n          <AlertDialogHeader>\r\n            <AlertDialogTitle>Xác nhận rút bạc</AlertDialogTitle>\r\n            <AlertDialogDescription>\r\n             Bạn có muốn rút bạc không?\r\n              <p className=\"mt-2\">Nếu rút bạc, khách hàng cần thanh toán 90% giá trị còn lại.</p>\r\n            </AlertDialogDescription>\r\n          </AlertDialogHeader>\r\n          <AlertDialogFooter>\r\n            <AlertDialogCancel onClick={() => {\r\n              // Nếu không rút bạc, mở form tất toán thông thường\r\n              setWithdrawalMode(false);\r\n              setShowSettlementModal(true);\r\n              setShowWithdrawalConfirm(false);\r\n            }}>\r\n              Không, tất toán thông thường\r\n            </AlertDialogCancel>\r\n            <AlertDialogAction onClick={() => {\r\n              // Nếu rút bạc, mở form tất toán với chế độ rút bạc\r\n              setWithdrawalMode(true);\r\n              setShowSettlementModal(true);\r\n              setShowWithdrawalConfirm(false);\r\n            }}>\r\n              Có, rút bạc\r\n            </AlertDialogAction>\r\n          </AlertDialogFooter>\r\n        </AlertDialogContent>\r\n      </AlertDialog>\r\n\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAUA;;;AA9BA;;;;;;;;;;;;;;;;;;;;AAyCe,SAAS;;IACtB,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,sQAAA,CAAA,WAAQ,AAAD,EAAe,EAAE;IAC5D,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,sQAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,sQAAA,CAAA,WAAQ,AAAD,EAAgB,EAAE;IACvD,MAAM,aAAa,CAAA,GAAA,sQAAA,CAAA,SAAM,AAAD,EAAgB,EAAE;IAC1C,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,sQAAA,CAAA,WAAQ,AAAD,EAAsB,EAAE;IACzE,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,sQAAA,CAAA,WAAQ,AAAD,EAAmB;QAC5D,WAAW;QACX,UAAU;IACZ;IACA,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,sQAAA,CAAA,WAAQ,AAAD,EAAE;QAC/B,MAAM;QACN,OAAO;QACP,WAAW;QACX,WAAW;QACX,iBAAiB;QACjB,aAAa;IACf;IACA,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,sQAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,sQAAA,CAAA,WAAQ,AAAD,EAAqB,CAAC;IACrE,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,sQAAA,CAAA,WAAQ,AAAD,EAAmB,CAAC;IAC3E,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,sQAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,sQAAA,CAAA,WAAQ,AAAD,EAAoB;IAC7E,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,sQAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,sQAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,sQAAA,CAAA,WAAQ,AAAD,EAA2B;IAEtF,uDAAuD;IACvD,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,sQAAA,CAAA,WAAQ,AAAD,EAAE;QAAE,KAAK;QAAG,QAAQ;QAAG,mBAAmB;IAAE;IACnG,MAAM,CAAC,wBAAwB,0BAA0B,GAAG,CAAA,GAAA,sQAAA,CAAA,WAAQ,AAAD,EAAE;IACrE,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,sQAAA,CAAA,WAAQ,AAAD,EAAgC;IACvE,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,sQAAA,CAAA,WAAQ,AAAD,EAAE;IAC/D,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,sQAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,uBAAuB,yBAAyB,GAAG,CAAA,GAAA,sQAAA,CAAA,WAAQ,AAAD,EAAE;IACnE,MAAM,CAAC,sBAAsB,wBAAwB,GAAG,CAAA,GAAA,sQAAA,CAAA,WAAQ,AAAD,EAAwC;IAEvG,4BAA4B;IAC5B,MAAM,EAAE,YAAY,EAAE,WAAW,EAAE,GAAG,CAAA,GAAA,iKAAA,CAAA,kBAAe,AAAD;IAEpD,+DAA+D;IAC/D,MAAM,sBAAsB,CAAA,GAAA,sQAAA,CAAA,cAAW,AAAD;uDAAE,CAAC;YACvC,gDAAgD;YAChD,WAAW;YAEX,gCAAgC;YAChC,IAAI,OAAO,mBAAmB,YAAY;gBACxC,2EAA2E;gBAC3E,MAAM,WAAW,eAAe;gBAChC,WAAW,OAAO,GAAG;YACvB,OAAO;gBACL,yBAAyB;gBACzB,WAAW,OAAO,GAAG;YACvB;QACF;sDAAG;QAAC;KAAQ;IAEZ,0BAA0B;IAC1B,MAAM,mBAAmB,CAAC;QACxB,qBAAqB;QACrB,cAAc;QACd,2QAAA,CAAA,QAAK,CAAC,IAAI,CAAC,CAAC,wBAAwB,EAAE,UAAU,cAAc,EAAE;IAClE;IAEA,uBAAuB;IACvB,MAAM,aAAa,CAAC;QAClB,qBAAqB;QACrB,YAAY;QACZ,0BAA0B;QAC1B,2QAAA,CAAA,QAAK,CAAC,IAAI,CAAC,CAAC,qBAAqB,EAAE,UAAU,cAAc,EAAE;IAC/D;IAEA,qBAAqB;IACrB,MAAM,kBAAkB;QACtB,qBAAqB;QACrB,YAAY;QACZ,0BAA0B;IAC5B;IAEA,iBAAiB;IACjB,MAAM,eAAe,OAAO;QAC1B,IAAI;YACF,cAAc;YACd,MAAM,6GAAA,CAAA,MAAG,CAAC,MAAM,CAAC,CAAC,YAAY,EAAE,UAAU,EAAE,EAAE;YAC9C,2QAAA,CAAA,QAAK,CAAC,OAAO,CAAC,CAAC,aAAa,EAAE,UAAU,cAAc,EAAE;YACxD;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,8BAA8B;YAC5C,2QAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd,SAAU;YACR,cAAc;QAChB;IACF;IAEA,iCAAiC;IACjC,MAAM,qBAAqB,OAAO,WAAsB;QACtD,IAAI;YACF,0EAA0E;YAC1E,IAAI,WAAW,4KAAA,CAAA,cAAW,CAAC,SAAS,IAAI,UAAU,SAAS,KAAK,cAAc;gBAC5E,qBAAqB;gBAErB,iEAAiE;gBACjE,IACE,UAAU,SAAS,KAAK,SACxB,UAAU,YAAY,KAAK,4KAAA,CAAA,eAAY,CAAC,MAAM,EAC9C;oBACA,2BAA2B;oBAC3B,MAAM,cAAc,UAAU,OAAO,EAAE,OAAO,CAAC,KAAK,SAAW,MAAM,CAAC,OAAO,QAAQ,IAAI,CAAC,GAAG,MAAM;oBAEnG,IAAI,eAAe,IAAI;wBACrB,mCAAmC;wBACnC,qBAAqB;wBACrB,yBAAyB;wBACzB;oBACF;gBACF;gBAEA,8BAA8B;gBAC9B,IAAI,CAAC,cAAc;oBACjB,2QAAA,CAAA,QAAK,CAAC,KAAK,CAAC;oBACZ;gBACF;gBAEA,yCAAyC;gBACzC,MAAM,aAAa;oBACjB,KAAK,UAAU,SAAS,KAAK,4KAAA,CAAA,YAAS,CAAC,GAAG,GAAG,aAAa,QAAQ,GAAG,aAAa,QAAQ;oBAC1F,KAAK,UAAU,SAAS,KAAK,4KAAA,CAAA,YAAS,CAAC,GAAG,GAAG,aAAa,YAAY,GAAG,aAAa,WAAW;gBACnG;gBACA,wBAAwB;gBAExB,uDAAuD;gBACvD,kBAAkB;gBAClB,uBAAuB;gBACvB;YACF;YAEA,cAAc;YAEd,sDAAsD;YACtD,IAAI,WAAW;YAEf,qCAAqC;YACrC,IAAI,UAAU,SAAS,KAAK,cAAc;gBACxC,2BAA2B;gBAC3B,OAAQ;oBACN,KAAK,4KAAA,CAAA,cAAW,CAAC,SAAS;wBACxB,WAAW,CAAC,aAAa,EAAE,UAAU,EAAE,CAAC,iBAAiB,CAAC;wBAC1D;oBACF;wBACE,MAAM,IAAI,MAAM,CAAC,sCAAsC,EAAE,QAAQ;gBACrE;YACF,OAAO;gBACL,yBAAyB;gBACzB,OAAQ;oBACN,KAAK,4KAAA,CAAA,cAAW,CAAC,SAAS;wBACxB,WAAW,CAAC,aAAa,EAAE,UAAU,EAAE,CAAC,iBAAiB,CAAC;wBAC1D;oBACF,KAAK,4KAAA,CAAA,cAAW,CAAC,UAAU;wBACzB,WAAW,CAAC,aAAa,EAAE,UAAU,EAAE,CAAC,kBAAkB,CAAC;wBAC3D;oBACF;wBACE,MAAM,IAAI,MAAM,CAAC,0CAA0C,EAAE,QAAQ;gBACzE;YACF;YAEA,gDAAgD;YAChD,IAAI,UAAU;gBACZ,MAAM,6GAAA,CAAA,MAAG,CAAC,KAAK,CAAC;gBAEhB,yBAAyB;gBACzB,MAAM,oBAAoB,WAAW,GAAG,CAAC,CAAA;oBACvC,IAAI,GAAG,EAAE,KAAK,UAAU,EAAE,EAAE;wBAC1B,OAAO;4BACL,GAAG,EAAE;4BACL;wBACF;oBACF;oBACA,OAAO;gBACT;gBAEA,cAAc;gBACd,2QAAA,CAAA,QAAK,CAAC,OAAO,CAAC,CAAC,6BAA6B,EAAE,UAAU,cAAc,CAAC,OAAO,EAAE,QAAQ;YAC1F;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,qCAAqC;YACnD,2QAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd,SAAU;YACR,cAAc;YACd;QACF;IACF;IAEA,4EAA4E;IAE5E,mCAAmC;IACnC,MAAM,yBAAyB,OAAO;QACpC,IAAI;YACF,cAAc;YACd,MAAM,6GAAA,CAAA,MAAG,CAAC,KAAK,CAAC,CAAC,YAAY,EAAE,UAAU,EAAE,CAAC,kBAAkB,CAAC;YAE/D,6CAA6C;YAC7C,MAAM,kBAAkB,IAAI,KAAK,UAAU,kBAAkB,IAAI,IAAI;YACrE,MAAM,cAAc,IAAI,KAAK;YAC7B,YAAY,OAAO,CAAC,YAAY,OAAO,KAAK;YAE5C,yBAAyB;YACzB,MAAM,oBAAoB,WAAW,GAAG,CAAC,CAAA;gBACvC,IAAI,GAAG,EAAE,KAAK,UAAU,EAAE,EAAE;oBAC1B,OAAO;wBACL,GAAG,EAAE;wBACL,oBAAoB;oBACtB;gBACF;gBACA,OAAO;YACT;YAEA,cAAc;YACd,2QAAA,CAAA,QAAK,CAAC,OAAO,CAAC,CAAC,wCAAwC,EAAE,UAAU,cAAc,CAAC,aAAa,CAAC;YAEhG,kCAAkC;YAClC;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,wCAAwC;YACtD,2QAAA,CAAA,QAAK,CAAC,KAAK,CAAC,wCAAwC;gBAClD,aAAa,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YACxD;QACF,SAAU;YACR,cAAc;QAChB;IACF;IAEA,sCAAsC;IACtC,MAAM,4BAA4B,OAAO,WAAsB;QAC7D,IAAI;YACF,cAAc;YAEd,8DAA8D;YAC9D,IAAI,kBAAkB,4KAAA,CAAA,gBAAa,CAAC,QAAQ,EAAE;gBAC5C,MAAM,6GAAA,CAAA,MAAG,CAAC,KAAK,CAAC,CAAC,YAAY,EAAE,UAAU,EAAE,CAAC,QAAQ,CAAC;gBAErD,yBAAyB;gBACzB,MAAM,oBAAoB,WAAW,GAAG,CAAC,CAAA;oBACvC,IAAI,GAAG,EAAE,KAAK,UAAU,EAAE,EAAE;wBAC1B,OAAO;4BACL,GAAG,EAAE;4BACL,eAAe,4KAAA,CAAA,gBAAa,CAAC,QAAQ;4BACrC,YAAY,IAAI;wBAClB;oBACF;oBACA,OAAO;gBACT;gBAEA,cAAc;gBACd,2QAAA,CAAA,QAAK,CAAC,OAAO,CAAC,CAAC,mBAAmB,EAAE,UAAU,EAAE,EAAE;YACpD,OAAO;gBACL,8DAA8D;gBAC9D,2QAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACd;YAEA,kCAAkC;YAClC;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,kCAAkC;YAChD,2QAAA,CAAA,QAAK,CAAC,KAAK,CAAC,2CAA2C;gBACrD,aAAa,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YACxD;QACF,SAAU;YACR,cAAc;QAChB;IACF;IAEA,MAAM,kBAAkB;QACtB,WAAW;QACX,IAAI;YACF,0BAA0B;YAC1B,IAAI,cAAc;YAClB,IAAI,iBAAiB;YACrB,IAAI,WAAW,OAAO,CAAC,MAAM,GAAG,GAAG;gBACjC,MAAM,YAAY,WAAW,OAAO,CAAC,EAAE;gBACvC,cAAc,CAAC,QAAQ,EAAE,UAAU,EAAE,EAAE;gBACvC,iBAAiB,CAAC,WAAW,EAAE,UAAU,IAAI,GAAG,SAAS,OAAO;YAClE;YAEA,8BAA8B;YAC9B,IAAI,cAAc;YAElB,wCAAwC;YACxC,IAAI,cAAc;gBAChB,cAAc,CAAC,QAAQ,EAAE,mBAAmB,eAAe;YAC7D;YAEA,wCAAwC;YACxC,IAAI,cAAc;YAElB,uCAAuC;YACvC,IAAI,uBAAuB,OAAO;gBAChC,cAAc,CAAC,qBAAqB,EAAE,oBAAoB;YAC5D;YAEA,MAAM,WAAW,MAAM,6GAAA,CAAA,MAAG,CAAC,GAAG,CAC5B,CAAC,iBAAiB,EAAE,WAAW,SAAS,GAAG,EAAE,OAAO,EAAE,WAAW,QAAQ,GAAG,cAAc,iBAAiB,cAAc,aAAa;YAGxI,IAAI,YAAY,SAAS,IAAI,EAAE;gBAC7B,6DAA6D;gBAC7D,cAAc,SAAS,IAAI;gBAE3B,IAAI,SAAS,IAAI,EAAE;oBACjB,QAAQ,SAAS,IAAI;gBACvB;YAEA,8CAA8C;YAC9C,gDAAgD;YAClD,OAAO;gBACL,cAAc,EAAE;gBAChB,QAAQ;oBACN,MAAM;oBACN,OAAO,WAAW,QAAQ;oBAC1B,WAAW;oBACX,WAAW;oBACX,iBAAiB;oBACjB,aAAa;gBACf;YACF;QACF,EAAE,OAAO,OAAY;YACnB,QAAQ,KAAK,CAAC,+BAA+B;YAE7C,uBAAuB;YACvB,IAAI,MAAM,MAAM,KAAK,KAAK;gBACxB,2QAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACd,OAAO,IAAI,MAAM,MAAM,KAAK,KAAK;gBAC/B,2QAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACd,OAAO,IAAI,MAAM,MAAM,KAAK,KAAK;gBAC/B,2QAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACd,OAAO;gBACL,2QAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACd;YAEA,kCAAkC;YAClC,cAAc,EAAE;YAChB,QAAQ;gBACN,MAAM;gBACN,OAAO,WAAW,QAAQ;gBAC1B,WAAW;gBACX,WAAW;gBACX,iBAAiB;gBACjB,aAAa;YACf;QACA,kDAAkD;QACpD,SAAU;YACR,WAAW;YACX,gBAAgB;QAClB;IACF;IAEA,MAAM,uBAAuB;QAC3B,IAAI;YACF,cAAc;YACd,MAAM,eAAe,MAAM,mBAAmB,GAAG,IAAI;YACrD,MAAM,qBAAqB,aAAa,GAAG,CAAC,CAAA,MAAO,IAAI,QAAQ;YAE/D,IAAI,mBAAmB,MAAM,KAAK,GAAG;gBACnC,2QAAA,CAAA,QAAK,CAAC,KAAK,CAAC;gBACZ;YACF;YAEA,kCAAkC;YAClC,MAAM,iBAAiB,mBAAmB,GAAG,CAAC,CAAA,YAC5C,6GAAA,CAAA,MAAG,CAAC,MAAM,CAAC,CAAC,YAAY,EAAE,UAAU,EAAE,EAAE;YAG1C,MAAM,QAAQ,GAAG,CAAC;YAElB,cAAc;YACd,2QAAA,CAAA,QAAK,CAAC,OAAO,CAAC,CAAC,OAAO,EAAE,mBAAmB,MAAM,CAAC,gBAAgB,CAAC;YAEnE,kBAAkB;YAClB,MAAM,iBAAiB;YAEvB,eAAe;YACf,MAAM;QACR,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,+BAA+B;YAC7C,2QAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd,SAAU;YACR,cAAc;QAChB;IACF;IAEA,MAAM,QAAQ,CAAA,GAAA,mSAAA,CAAA,gBAAa,AAAD,EAAE;QAC1B,MAAM;QACN,SAAS;YACP;gBACE,IAAI;gBACJ,MAAM;gBACN,MAAM;uDAAE,CAAC,EAAE,KAAK,EAAE,iBAChB,sSAAC;4BAAI,WAAU;sCACb,cAAA,sSAAC,gIAAA,CAAA,WAAQ;gCACP,SACE,MAAM,wBAAwB,MAC7B,MAAM,yBAAyB,MAAM;gCAExC,eAAe;uEAAE,CAAC,QAAU,MAAM,yBAAyB,CAAC,CAAC,CAAC;;gCAC9D,cAAW;gCACX,WAAU;;;;;;;;;;;;gBAIhB,IAAI;uDAAE,CAAC,EAAE,GAAG,EAAE,iBACZ,sSAAC;4BAAI,WAAU;sCACb,cAAA,sSAAC,gIAAA,CAAA,WAAQ;gCACP,SAAS,IAAI,aAAa;gCAC1B,eAAe;uEAAE,CAAC,QAAU,IAAI,cAAc,CAAC,CAAC,CAAC;;gCACjD,OAAO;uEAAE,CAAC,IAAM,EAAE,eAAe;;gCACjC,cAAW;gCACX,WAAU;;;;;;;;;;;;gBAIhB,eAAe;gBACf,cAAc;YAChB;eACG,CAAA,GAAA,qLAAA,CAAA,sBAAmB,AAAD,EAAE;gBACrB,cAAc;gBACd,UAAU;gBACV,QAAQ;gBACR,gBAAgB;gBAChB,oBAAoB;gBACpB,uBAAuB;gBACvB,cAAc;YAChB;SACD;QACD,OAAO;YACL;YACA;YACA;YACA;YACA;YACA;QACF;QACA,WAAW,KAAK,SAAS;QACzB,iBAAiB;QACjB,uBAAuB;QACvB,oBAAoB;QACpB,sBAAsB;QACtB,0BAA0B;QAC1B,sBAAsB;QACtB,iBAAiB,CAAA,GAAA,iPAAA,CAAA,kBAAe,AAAD;QAC/B,mBAAmB,CAAA,GAAA,iPAAA,CAAA,oBAAiB,AAAD;QACnC,qBAAqB,CAAA,GAAA,iPAAA,CAAA,sBAAmB,AAAD;QACvC,kBAAkB;QAClB,eAAe;QACf,qBAAqB;QACrB,oBAAoB;QACpB,iBAAiB;QACjB,eAAe;QACf,iBAAiB;IACnB;IAEA,MAAM,gBAAgB,CAAA,GAAA,sQAAA,CAAA,cAAW,AAAD;iDAAE;YAChC,gBAAgB;YAChB,IAAI;gBACF,gCAAgC;gBAChC,MAAM,kBAAkB;gBACxB,MAAM,YAAY;gBAClB,sBAAsB;gBACtB,gBAAgB;gBAEhB,gCAAgC;gBAChC,MAAM,QAAQ,GAAG,CAAC;oBAChB;oBACA;iBACD;YACH,SAAU;gBACR;6DAAW;wBACT,gBAAgB;oBAClB;4DAAG;YACL;QACF;gDAAG;QAAC;KAAM;IAEV,qBAAqB;IACrB,MAAM,eAAe;QACnB,IAAI;YACF,uCAAuC;YACvC,2QAAA,CAAA,QAAK,CAAC,IAAI,CAAC,CAAC,4BAA4B,CAAC;YAEzC,qCAAqC;YACrC,MAAM,QAAQ,aAAa,OAAO,CAAC;YAEnC,cAAc;YACd,MAAM,SAAS,GAAG,6GAAA,CAAA,MAAG,CAAC,OAAO,CAAC,mBAAmB,CAAC;YAElD,qCAAqC;YACrC,MAAM,MAAM,IAAI;YAChB,IAAI,IAAI,CAAC,OAAO,QAAQ;YACxB,IAAI,YAAY,GAAG;YAEnB,wBAAwB;YACxB,IAAI,OAAO;gBACT,IAAI,gBAAgB,CAAC,iBAAiB,CAAC,OAAO,EAAE,OAAO;YACzD;YAEA,qBAAqB;YACrB,IAAI,MAAM,GAAG;gBACX,IAAI,IAAI,CAAC,MAAM,KAAK,KAAK;oBACvB,uBAAuB;oBACvB,MAAM,OAAO,IAAI,KAAK;wBAAC,IAAI,CAAC,QAAQ;qBAAC,EAAE;wBACrC,MAAM;oBACR;oBAEA,mBAAmB;oBACnB,MAAM,MAAM,OAAO,GAAG,CAAC,eAAe,CAAC;oBAEvC,yBAAyB;oBACzB,MAAM,IAAI,SAAS,aAAa,CAAC;oBACjC,EAAE,IAAI,GAAG;oBACT,EAAE,QAAQ,GAAG,CAAC,YAAY,EAAE,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,KAAK,CAAC;oBACzE,SAAS,IAAI,CAAC,WAAW,CAAC;oBAC1B,EAAE,KAAK;oBAEP,UAAU;oBACV,WAAW;wBACT,SAAS,IAAI,CAAC,WAAW,CAAC;wBAC1B,OAAO,GAAG,CAAC,eAAe,CAAC;oBAC7B,GAAG;oBAEH,2QAAA,CAAA,QAAK,CAAC,OAAO,CAAC,CAAC,kCAAkC,CAAC;gBACpD,OAAO;oBACL,2QAAA,CAAA,QAAK,CAAC,KAAK,CAAC,CAAC,gCAAgC,EAAE,IAAI,CAAC,UAAU,EAAE;gBAClE;YACF;YAEA,YAAY;YACZ,IAAI,OAAO,GAAG;gBACZ,2QAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACd;YAEA,cAAc;YACd,IAAI,IAAI;QACV,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,gCAAgC;YAC9C,2QAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd;IACF;IAEA,sDAAsD;IACtD,MAAM,kBAAkB;QACtB,IAAI;YACF,MAAM,WAAW,MAAM,6GAAA,CAAA,MAAG,CAAC,GAAG,CAAsB;YACpD,IAAI,UAAU;gBACZ,sCAAsC;gBACtC,MAAM,YAAY;oBAChB,KAAK,SAAS,KAAK,IAAI;oBACvB,QAAQ,SAAS,kBAAkB,EAAE,UAAU;oBAC/C,mBAAmB,SAAS,kBAAkB,EAAE,sBAAsB;gBACxE;gBAEA,0BAA0B;gBAC1B,oBAAoB;YAEpB,yBAAyB;YAE3B;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,8BAA8B;YAC5C,2QAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YAEZ,kCAAkC;YAClC,MAAM,eAAe;gBACnB,KAAK;gBACL,QAAQ;gBACR,mBAAmB;YACrB;YAEA,oBAAoB;QACtB;IACF;IAEA,+BAA+B;IAC/B,MAAM,mBAAmB,CAAC;QACxB,IAAI,CAAC,cAAc;YACjB,2QAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ;QACF;QAEA,qBAAqB;QAErB,yCAAyC;QACzC,MAAM,aAAa;YACjB,KAAK,UAAU,SAAS,KAAK,4KAAA,CAAA,YAAS,CAAC,GAAG,GAAG,aAAa,QAAQ,GAAG,aAAa,QAAQ;YAC1F,KAAK,UAAU,SAAS,KAAK,4KAAA,CAAA,YAAS,CAAC,GAAG,GAAG,aAAa,YAAY,GAAG,aAAa,WAAW;QACnG;QACA,wBAAwB;QAExB,uBAAuB;IACzB;IAEA,4BAA4B;IAC5B,MAAM,6BAA6B;QACjC,uBAAuB;QACvB,qBAAqB;QACrB,wBAAwB;IAC1B;IAEA,CAAA,GAAA,sQAAA,CAAA,YAAS,AAAD;gCAAE;YACR;QACF;+BAAG;QAAC,WAAW,SAAS;QAAE,WAAW,QAAQ;QAAE;QAAoB;QAAS;KAAa;IAEzF,CAAA,GAAA,sQAAA,CAAA,YAAS,AAAD;gCAAE;YACR;QACF;+BAAG,EAAE;IAEL,qBACE,sSAAC;QAAI,WAAU;;0BAEb,sSAAC;gBAAI,WAAU;;kCACb,sSAAC;wBAAI,WAAU;kCACb,cAAA,sSAAC;4BAAI,WAAU;;8CACb,sSAAC;oCAAK,WAAU;8CAAsB;;;;;;8CACtC,sSAAC;oCAAK,WAAU;8CAA4C,iBAAiB,GAAG;;;;;;;;;;;;;;;;;kCAGpF,sSAAC;wBAAI,WAAU;;4BACZ,4BACC,sSAAC;gCAAK,WAAU;0CAAgC;;;;;;0CAElD,sSAAC;gCAAI,WAAU;;kDACb,sSAAC,8HAAA,CAAA,SAAM;wCAAC,WAAU;wCAAW,MAAK;wCAAK,SAAQ;wCAAY,SAAS;;0DAClE,sSAAC,qSAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;4CAAgB;;;;;;;kDAGtC,sSAAC,8HAAA,CAAA,SAAM;wCAAC,WAAU;wCAAW,MAAK;wCAAK,SAAQ;wCAAY,SAAS;;0DAClE,sSAAC,yRAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;4CAAgB;;;;;;;;;;;;;;;;;;;;;;;;;0BAQxC,sSAAC,6JAAA,CAAA,eAAY;gBACX,OAAO;gBACP,cAAc;gBACd,iBAAiB;gBACjB,WAAW;gBACX,cAAc;gBACd,gCACE,sSAAC,sMAAA,CAAA,wBAAqB;oBACpB,eAAe;oBACf,gBAAgB;oBAChB,QAAQ;oBACR,WAAU;;;;;;;;;;;0BAMhB,sSAAC;gBAAI,WAAU;0BACb,cAAA,sSAAC,0JAAA,CAAA,YAAS;oBACR,OAAO;oBACP,WAAU;oBACV,WAAW;;;;;;;;;;;0BAKf,sSAAC,4JAAA,CAAA,cAAW;gBACV,OAAO;gBACP,YAAY,KAAK,SAAS;;;;;;0BAI5B,sSAAC,8LAAA,CAAA,oBAAiB;gBAChB,eAAe,MAAM,2BAA2B,GAAG,IAAI,CAAC,MAAM;gBAC9D,UAAU;;;;;;0BAGZ,sSAAC,uLAAA,CAAA,uBAAoB;gBACnB,WAAW;gBACX,QAAQ;gBACR,SAAS,IAAM,cAAc;gBAC7B,QAAQ;;;;;;0BAGV,sSAAC,qLAAA,CAAA,qBAAkB;gBACjB,QAAQ;gBACR,SAAS,IAAM,0BAA0B;gBACzC,WAAW;gBACX,MAAM;gBACN,WAAW;gBACX,YAAY,eAAe;oBACzB,KAAK,aAAa,QAAQ;oBAC1B,KAAK,aAAa,YAAY;gBAChC,IAAI;;;;;;0BAIN,sSAAC,0KAAA,CAAA,kBAAe;gBACd,WAAW;gBACX,QAAQ;gBACR,SAAS;gBACT,WAAW;oBACT;oBACA;gBACF;gBACA,gBAAgB;gBAChB,YAAY;;;;;;0BAId,sSAAC,uIAAA,CAAA,cAAW;gBAAC,MAAM;gBAAuB,cAAc;0BACtD,cAAA,sSAAC,uIAAA,CAAA,qBAAkB;;sCACjB,sSAAC,uIAAA,CAAA,oBAAiB;;8CAChB,sSAAC,uIAAA,CAAA,mBAAgB;8CAAC;;;;;;8CAClB,sSAAC,uIAAA,CAAA,yBAAsB;;wCAAC;sDAEtB,sSAAC;4CAAE,WAAU;sDAAO;;;;;;;;;;;;;;;;;;sCAGxB,sSAAC,uIAAA,CAAA,oBAAiB;;8CAChB,sSAAC,uIAAA,CAAA,oBAAiB;oCAAC,SAAS;wCAC1B,mDAAmD;wCACnD,kBAAkB;wCAClB,uBAAuB;wCACvB,yBAAyB;oCAC3B;8CAAG;;;;;;8CAGH,sSAAC,uIAAA,CAAA,oBAAiB;oCAAC,SAAS;wCAC1B,mDAAmD;wCACnD,kBAAkB;wCAClB,uBAAuB;wCACvB,yBAAyB;oCAC3B;8CAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASf;GApuBwB;;QAqCgB,iKAAA,CAAA,kBAAe;QA8VvC,mSAAA,CAAA,gBAAa;;;KAnYL", "debugId": null}}]}
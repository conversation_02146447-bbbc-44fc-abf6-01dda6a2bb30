import { Repository, DataSource } from 'typeorm';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { BaseCmsPagesService } from './base.cms-pages.service';
import { CmsPages, CmsPageStatus } from '../entity/cms-pages.entity';
import { CmsPageDto } from '../dto/cms-page.dto';
import { CustomPaginationQueryDto } from '../../common/dto/custom-pagination-query.dto';
export declare class ReadCmsPagesService extends BaseCmsPagesService {
    protected readonly pageRepository: Repository<CmsPages>;
    protected readonly dataSource: DataSource;
    protected readonly eventEmitter: EventEmitter2;
    constructor(pageRepository: Repository<CmsPages>, dataSource: DataSource, eventEmitter: EventEmitter2);
    findAll(params: CustomPaginationQueryDto): Promise<{
        data: CmsPageDto[];
        total: number;
    }>;
    findByStatus(status: CmsPageStatus, params: CustomPaginationQueryDto): Promise<{
        data: CmsPageDto[];
        total: number;
    }>;
    findByTemplate(template: string, params: CustomPaginationQueryDto): Promise<{
        data: CmsPageDto[];
        total: number;
    }>;
    getPublishedPages(params: CustomPaginationQueryDto): Promise<{
        data: CmsPageDto[];
        total: number;
    }>;
    getDraftPages(params: CustomPaginationQueryDto): Promise<{
        data: CmsPageDto[];
        total: number;
    }>;
    count(filter?: string): Promise<number>;
    findOne(id: string, relations?: string[]): Promise<CmsPageDto | null>;
    findOneOrFail(id: string, relations?: string[]): Promise<CmsPageDto | null>;
    findByBusinessCodePublic(businessCode: string): Promise<CmsPageDto | null>;
    findBySlugPublic(slug: string): Promise<CmsPageDto | null>;
    search(keyword: string, params: CustomPaginationQueryDto): Promise<{
        data: CmsPageDto[];
        total: number;
    }>;
    findDeleted(params: CustomPaginationQueryDto): Promise<{
        data: CmsPageDto[];
        total: number;
    }>;
    getStatistics(): Promise<{
        total: number;
        byStatus: Record<string, number>;
        byTemplate: Record<string, number>;
        totalWordCount: number;
        averageWordCount: number;
    }>;
    getAvailableTemplates(): string[];
    getPopularTemplatePages(limit?: number): Promise<CmsPageDto[]>;
}

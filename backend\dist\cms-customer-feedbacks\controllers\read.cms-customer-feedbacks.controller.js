"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ReadCmsCustomerFeedbacksController = void 0;
const openapi = require("@nestjs/swagger");
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const jwt_auth_guard_1 = require("../../common/guards/jwt-auth.guard");
const read_cms_customer_feedbacks_service_1 = require("../services/read.cms-customer-feedbacks.service");
const cms_customer_feedbacks_entity_1 = require("../entity/cms-customer-feedbacks.entity");
const api_response_dto_1 = require("../../common/dto/api-response.dto");
const custom_pagination_query_dto_1 = require("../../common/dto/custom-pagination-query.dto");
const pagination_response_dto_1 = require("../../common/dto/pagination-response.dto");
const custom_pagination_meta_dto_1 = require("../../common/dto/custom-pagination-meta.dto");
const roles_decorator_1 = require("../../common/decorators/roles.decorator");
const permissions_decorator_1 = require("../../common/decorators/permissions.decorator");
let ReadCmsCustomerFeedbacksController = class ReadCmsCustomerFeedbacksController {
    cmsCustomerFeedbacksService;
    constructor(cmsCustomerFeedbacksService) {
        this.cmsCustomerFeedbacksService = cmsCustomerFeedbacksService;
    }
    async findAll(paginationQuery) {
        const { data, total } = await this.cmsCustomerFeedbacksService.findAll(paginationQuery);
        const meta = new custom_pagination_meta_dto_1.CustomPageMetaDto({
            pageQueryDto: paginationQuery,
            itemCount: total,
        });
        return new pagination_response_dto_1.PaginationResponseDto(data, meta);
    }
    async findByStatus(status, paginationQuery) {
        const { data, total } = await this.cmsCustomerFeedbacksService.findByStatus(status, paginationQuery);
        const meta = new custom_pagination_meta_dto_1.CustomPageMetaDto({
            pageQueryDto: paginationQuery,
            itemCount: total,
        });
        return new pagination_response_dto_1.PaginationResponseDto(data, meta);
    }
    async findByRating(rating, paginationQuery) {
        const { data, total } = await this.cmsCustomerFeedbacksService.findByRating(rating, paginationQuery);
        const meta = new custom_pagination_meta_dto_1.CustomPageMetaDto({
            pageQueryDto: paginationQuery,
            itemCount: total,
        });
        return new pagination_response_dto_1.PaginationResponseDto(data, meta);
    }
    async getApprovedFeedbacks(paginationQuery) {
        const { data, total } = await this.cmsCustomerFeedbacksService.getApprovedFeedbacks(paginationQuery);
        const meta = new custom_pagination_meta_dto_1.CustomPageMetaDto({
            pageQueryDto: paginationQuery,
            itemCount: total,
        });
        return new pagination_response_dto_1.PaginationResponseDto(data, meta);
    }
    async getPendingFeedbacks(paginationQuery) {
        const { data, total } = await this.cmsCustomerFeedbacksService.getPendingFeedbacks(paginationQuery);
        const meta = new custom_pagination_meta_dto_1.CustomPageMetaDto({
            pageQueryDto: paginationQuery,
            itemCount: total,
        });
        return new pagination_response_dto_1.PaginationResponseDto(data, meta);
    }
    async findByProductService(productServiceName, paginationQuery) {
        const { data, total } = await this.cmsCustomerFeedbacksService.findByProductService(productServiceName, paginationQuery);
        const meta = new custom_pagination_meta_dto_1.CustomPageMetaDto({
            pageQueryDto: paginationQuery,
            itemCount: total,
        });
        return new pagination_response_dto_1.PaginationResponseDto(data, meta);
    }
    async getStatistics() {
        return this.cmsCustomerFeedbacksService.getStatistics();
    }
    async getHighRatingFeedbacks(limit) {
        return this.cmsCustomerFeedbacksService.getHighRatingFeedbacks(limit || 10);
    }
    async count(filter) {
        return this.cmsCustomerFeedbacksService.count(filter);
    }
    async search(keyword, paginationQuery) {
        const { data, total } = await this.cmsCustomerFeedbacksService.search(keyword, paginationQuery);
        const meta = new custom_pagination_meta_dto_1.CustomPageMetaDto({
            pageQueryDto: paginationQuery,
            itemCount: total,
        });
        return new pagination_response_dto_1.PaginationResponseDto(data, meta);
    }
    async findDeleted(paginationQuery) {
        const { data, total } = await this.cmsCustomerFeedbacksService.findDeleted(paginationQuery);
        const meta = new custom_pagination_meta_dto_1.CustomPageMetaDto({
            pageQueryDto: paginationQuery,
            itemCount: total,
        });
        return new pagination_response_dto_1.PaginationResponseDto(data, meta);
    }
    async findOne(id, relations) {
        const relationsArray = relations ? relations.split(',').map(r => r.trim()) : [];
        return this.cmsCustomerFeedbacksService.findOneOrFail(id, relationsArray);
    }
    async findByBusinessCode(businessCode) {
        return this.cmsCustomerFeedbacksService.findByBusinessCodePublic(businessCode);
    }
};
exports.ReadCmsCustomerFeedbacksController = ReadCmsCustomerFeedbacksController;
__decorate([
    (0, common_1.Get)(),
    (0, roles_decorator_1.Roles)('ADMIN', 'EDITOR', 'USER'),
    (0, permissions_decorator_1.Permissions)('cms-customer-feedback:read'),
    (0, swagger_1.ApiOperation)({ summary: 'Lấy danh sách feedback khách hàng CMS với phân trang' }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Danh sách feedback khách hàng CMS.',
        type: api_response_dto_1.ApiResponseDto,
        schema: {
            properties: {
                data: {
                    type: 'object',
                    properties: {
                        data: {
                            type: 'array',
                            items: { $ref: '#/components/schemas/CmsCustomerFeedbackDto' },
                        },
                        meta: {
                            type: 'object',
                            properties: {
                                total: { type: 'number' },
                                page: { type: 'number' },
                                limit: { type: 'number' },
                                totalPages: { type: 'number' },
                            },
                        },
                    },
                },
            },
        },
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.BAD_REQUEST,
        description: 'Tham số không hợp lệ.',
        type: api_response_dto_1.ApiResponseDto,
        schema: { properties: { data: { type: 'object', nullable: true } } },
    }),
    (0, swagger_1.ApiQuery)({ name: 'page', required: false, type: Number, description: 'Số trang' }),
    (0, swagger_1.ApiQuery)({ name: 'limit', required: false, type: Number, description: 'Số lượng bản ghi mỗi trang' }),
    (0, swagger_1.ApiQuery)({ name: 'search', required: false, type: String, description: 'Tìm kiếm theo từ khóa' }),
    openapi.ApiResponse({ status: 200 }),
    __param(0, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [custom_pagination_query_dto_1.CustomPaginationQueryDto]),
    __metadata("design:returntype", Promise)
], ReadCmsCustomerFeedbacksController.prototype, "findAll", null);
__decorate([
    (0, common_1.Get)('by-status/:status'),
    (0, roles_decorator_1.Roles)('ADMIN', 'EDITOR', 'USER'),
    (0, permissions_decorator_1.Permissions)('cms-customer-feedback:read'),
    (0, swagger_1.ApiOperation)({ summary: 'Lấy danh sách feedback CMS theo trạng thái' }),
    (0, swagger_1.ApiParam)({
        name: 'status',
        enum: cms_customer_feedbacks_entity_1.CmsCustomerFeedbackStatus,
        description: 'Trạng thái feedback',
    }),
    (0, swagger_1.ApiQuery)({ name: 'page', required: false, type: Number, description: 'Số trang' }),
    (0, swagger_1.ApiQuery)({ name: 'limit', required: false, type: Number, description: 'Số lượng bản ghi mỗi trang' }),
    (0, swagger_1.ApiQuery)({ name: 'search', required: false, type: String, description: 'Tìm kiếm theo từ khóa' }),
    openapi.ApiResponse({ status: 200 }),
    __param(0, (0, common_1.Param)('status')),
    __param(1, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, custom_pagination_query_dto_1.CustomPaginationQueryDto]),
    __metadata("design:returntype", Promise)
], ReadCmsCustomerFeedbacksController.prototype, "findByStatus", null);
__decorate([
    (0, common_1.Get)('by-rating/:rating'),
    (0, roles_decorator_1.Roles)('ADMIN', 'EDITOR', 'USER'),
    (0, permissions_decorator_1.Permissions)('cms-customer-feedback:read'),
    (0, swagger_1.ApiOperation)({ summary: 'Lấy danh sách feedback CMS theo rating' }),
    (0, swagger_1.ApiParam)({
        name: 'rating',
        type: Number,
        description: 'Rating (1-5 sao)',
    }),
    (0, swagger_1.ApiQuery)({ name: 'page', required: false, type: Number, description: 'Số trang' }),
    (0, swagger_1.ApiQuery)({ name: 'limit', required: false, type: Number, description: 'Số lượng bản ghi mỗi trang' }),
    (0, swagger_1.ApiQuery)({ name: 'search', required: false, type: String, description: 'Tìm kiếm theo từ khóa' }),
    openapi.ApiResponse({ status: 200 }),
    __param(0, (0, common_1.Param)('rating', common_1.ParseIntPipe)),
    __param(1, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, custom_pagination_query_dto_1.CustomPaginationQueryDto]),
    __metadata("design:returntype", Promise)
], ReadCmsCustomerFeedbacksController.prototype, "findByRating", null);
__decorate([
    (0, common_1.Get)('approved'),
    (0, roles_decorator_1.Roles)('ADMIN', 'EDITOR', 'USER'),
    (0, permissions_decorator_1.Permissions)('cms-customer-feedback:read'),
    (0, swagger_1.ApiOperation)({ summary: 'Lấy danh sách feedback đã được duyệt (testimonials)' }),
    (0, swagger_1.ApiQuery)({ name: 'page', required: false, type: Number, description: 'Số trang' }),
    (0, swagger_1.ApiQuery)({ name: 'limit', required: false, type: Number, description: 'Số lượng bản ghi mỗi trang' }),
    (0, swagger_1.ApiQuery)({ name: 'search', required: false, type: String, description: 'Tìm kiếm theo từ khóa' }),
    openapi.ApiResponse({ status: 200 }),
    __param(0, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [custom_pagination_query_dto_1.CustomPaginationQueryDto]),
    __metadata("design:returntype", Promise)
], ReadCmsCustomerFeedbacksController.prototype, "getApprovedFeedbacks", null);
__decorate([
    (0, common_1.Get)('pending'),
    (0, roles_decorator_1.Roles)('ADMIN', 'EDITOR'),
    (0, permissions_decorator_1.Permissions)('cms-customer-feedback:read'),
    (0, swagger_1.ApiOperation)({ summary: 'Lấy danh sách feedback chờ duyệt' }),
    (0, swagger_1.ApiQuery)({ name: 'page', required: false, type: Number, description: 'Số trang' }),
    (0, swagger_1.ApiQuery)({ name: 'limit', required: false, type: Number, description: 'Số lượng bản ghi mỗi trang' }),
    openapi.ApiResponse({ status: 200 }),
    __param(0, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [custom_pagination_query_dto_1.CustomPaginationQueryDto]),
    __metadata("design:returntype", Promise)
], ReadCmsCustomerFeedbacksController.prototype, "getPendingFeedbacks", null);
__decorate([
    (0, common_1.Get)('by-product-service'),
    (0, roles_decorator_1.Roles)('ADMIN', 'EDITOR', 'USER'),
    (0, permissions_decorator_1.Permissions)('cms-customer-feedback:read'),
    (0, swagger_1.ApiOperation)({ summary: 'Tìm feedback theo sản phẩm/dịch vụ' }),
    (0, swagger_1.ApiQuery)({
        name: 'productServiceName',
        required: true,
        type: String,
        description: 'Tên sản phẩm/dịch vụ',
        example: 'Dịch vụ mua bán vàng',
    }),
    (0, swagger_1.ApiQuery)({ name: 'page', required: false, type: Number, description: 'Số trang' }),
    (0, swagger_1.ApiQuery)({ name: 'limit', required: false, type: Number, description: 'Số lượng bản ghi mỗi trang' }),
    openapi.ApiResponse({ status: 200 }),
    __param(0, (0, common_1.Query)('productServiceName')),
    __param(1, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, custom_pagination_query_dto_1.CustomPaginationQueryDto]),
    __metadata("design:returntype", Promise)
], ReadCmsCustomerFeedbacksController.prototype, "findByProductService", null);
__decorate([
    (0, common_1.Get)('statistics'),
    (0, roles_decorator_1.Roles)('ADMIN', 'EDITOR'),
    (0, permissions_decorator_1.Permissions)('cms-customer-feedback:read'),
    (0, swagger_1.ApiOperation)({ summary: 'Lấy thống kê feedback CMS' }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Thống kê feedback CMS.',
        type: api_response_dto_1.ApiResponseDto,
        schema: {
            properties: {
                data: {
                    type: 'object',
                    properties: {
                        total: { type: 'number' },
                        byStatus: {
                            type: 'object',
                            additionalProperties: { type: 'number' },
                        },
                        byRating: {
                            type: 'object',
                            additionalProperties: { type: 'number' },
                        },
                        averageRating: { type: 'number' },
                        totalWithRating: { type: 'number' },
                    },
                },
            },
        },
    }),
    openapi.ApiResponse({ status: 200 }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], ReadCmsCustomerFeedbacksController.prototype, "getStatistics", null);
__decorate([
    (0, common_1.Get)('high-rating'),
    (0, roles_decorator_1.Roles)('ADMIN', 'EDITOR', 'USER'),
    (0, permissions_decorator_1.Permissions)('cms-customer-feedback:read'),
    (0, swagger_1.ApiOperation)({ summary: 'Lấy feedback rating cao (4-5 sao)' }),
    (0, swagger_1.ApiQuery)({
        name: 'limit',
        required: false,
        type: Number,
        description: 'Số lượng feedback cần lấy',
        example: 10,
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Danh sách feedback rating cao.',
        type: api_response_dto_1.ApiResponseDto,
        schema: {
            properties: {
                data: {
                    type: 'array',
                    items: { $ref: '#/components/schemas/CmsCustomerFeedbackDto' },
                },
            },
        },
    }),
    openapi.ApiResponse({ status: 200, type: [require("../dto/cms-customer-feedback.dto").CmsCustomerFeedbackDto] }),
    __param(0, (0, common_1.Query)('limit')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", Promise)
], ReadCmsCustomerFeedbacksController.prototype, "getHighRatingFeedbacks", null);
__decorate([
    (0, common_1.Get)('count'),
    (0, roles_decorator_1.Roles)('ADMIN', 'EDITOR', 'USER'),
    (0, permissions_decorator_1.Permissions)('cms-customer-feedback:read'),
    (0, swagger_1.ApiOperation)({ summary: 'Đếm số lượng feedback CMS' }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Số lượng feedback CMS.',
        type: api_response_dto_1.ApiResponseDto,
        schema: {
            properties: {
                data: { type: 'number' },
            },
        },
    }),
    (0, swagger_1.ApiQuery)({ name: 'filter', required: false, type: String, description: 'Bộ lọc' }),
    openapi.ApiResponse({ status: 200, type: Number }),
    __param(0, (0, common_1.Query)('filter')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], ReadCmsCustomerFeedbacksController.prototype, "count", null);
__decorate([
    (0, common_1.Get)('search'),
    (0, roles_decorator_1.Roles)('ADMIN', 'EDITOR', 'USER'),
    (0, permissions_decorator_1.Permissions)('cms-customer-feedback:read'),
    (0, swagger_1.ApiOperation)({ summary: 'Tìm kiếm feedback CMS' }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Kết quả tìm kiếm feedback CMS.',
        type: api_response_dto_1.ApiResponseDto,
        schema: {
            properties: {
                data: {
                    type: 'object',
                    properties: {
                        data: {
                            type: 'array',
                            items: { $ref: '#/components/schemas/CmsCustomerFeedbackDto' },
                        },
                        meta: {
                            type: 'object',
                            properties: {
                                total: { type: 'number' },
                                page: { type: 'number' },
                                limit: { type: 'number' },
                                totalPages: { type: 'number' },
                            },
                        },
                    },
                },
            },
        },
    }),
    (0, swagger_1.ApiQuery)({ name: 'keyword', required: true, type: String, description: 'Từ khóa tìm kiếm' }),
    (0, swagger_1.ApiQuery)({ name: 'page', required: false, type: Number, description: 'Số trang' }),
    (0, swagger_1.ApiQuery)({ name: 'limit', required: false, type: Number, description: 'Số lượng bản ghi mỗi trang' }),
    openapi.ApiResponse({ status: 200 }),
    __param(0, (0, common_1.Query)('keyword')),
    __param(1, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, custom_pagination_query_dto_1.CustomPaginationQueryDto]),
    __metadata("design:returntype", Promise)
], ReadCmsCustomerFeedbacksController.prototype, "search", null);
__decorate([
    (0, common_1.Get)('deleted'),
    (0, roles_decorator_1.Roles)('ADMIN'),
    (0, permissions_decorator_1.Permissions)('cms-customer-feedback:read'),
    (0, swagger_1.ApiOperation)({ summary: 'Lấy danh sách feedback CMS đã xóa' }),
    (0, swagger_1.ApiQuery)({ name: 'page', required: false, type: Number, description: 'Số trang' }),
    (0, swagger_1.ApiQuery)({ name: 'limit', required: false, type: Number, description: 'Số lượng bản ghi mỗi trang' }),
    openapi.ApiResponse({ status: 200 }),
    __param(0, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [custom_pagination_query_dto_1.CustomPaginationQueryDto]),
    __metadata("design:returntype", Promise)
], ReadCmsCustomerFeedbacksController.prototype, "findDeleted", null);
__decorate([
    (0, common_1.Get)(':id'),
    (0, roles_decorator_1.Roles)('ADMIN', 'EDITOR', 'USER'),
    (0, permissions_decorator_1.Permissions)('cms-customer-feedback:read'),
    (0, swagger_1.ApiOperation)({ summary: 'Lấy thông tin chi tiết feedback CMS' }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Thông tin chi tiết feedback CMS.',
        type: api_response_dto_1.ApiResponseDto,
        schema: {
            properties: {
                data: { $ref: '#/components/schemas/CmsCustomerFeedbackDto' },
            },
        },
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.NOT_FOUND,
        description: 'Không tìm thấy feedback CMS.',
        type: api_response_dto_1.ApiResponseDto,
        schema: { properties: { data: { type: 'object', nullable: true } } },
    }),
    (0, swagger_1.ApiParam)({ name: 'id', type: String, description: 'ID của feedback CMS' }),
    openapi.ApiResponse({ status: 200, type: Object }),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __param(1, (0, common_1.Query)('relations')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String]),
    __metadata("design:returntype", Promise)
], ReadCmsCustomerFeedbacksController.prototype, "findOne", null);
__decorate([
    (0, common_1.Get)('business-code/:businessCode'),
    (0, roles_decorator_1.Roles)('ADMIN', 'EDITOR', 'USER'),
    (0, permissions_decorator_1.Permissions)('cms-customer-feedback:read'),
    (0, swagger_1.ApiOperation)({ summary: 'Lấy thông tin feedback CMS theo business code' }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Thông tin feedback CMS.',
        type: api_response_dto_1.ApiResponseDto,
        schema: {
            properties: {
                data: { $ref: '#/components/schemas/CmsCustomerFeedbackDto' },
            },
        },
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.NOT_FOUND,
        description: 'Không tìm thấy feedback CMS.',
        type: api_response_dto_1.ApiResponseDto,
        schema: { properties: { data: { type: 'object', nullable: true } } },
    }),
    (0, swagger_1.ApiParam)({ name: 'businessCode', type: String, description: 'Business code của feedback CMS' }),
    openapi.ApiResponse({ status: 200, type: Object }),
    __param(0, (0, common_1.Param)('businessCode')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], ReadCmsCustomerFeedbacksController.prototype, "findByBusinessCode", null);
exports.ReadCmsCustomerFeedbacksController = ReadCmsCustomerFeedbacksController = __decorate([
    (0, swagger_1.ApiTags)('cms-customer-feedbacks'),
    (0, swagger_1.ApiBearerAuth)(),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, common_1.Controller)('cms/customer-feedbacks'),
    __metadata("design:paramtypes", [read_cms_customer_feedbacks_service_1.ReadCmsCustomerFeedbacksService])
], ReadCmsCustomerFeedbacksController);
//# sourceMappingURL=read.cms-customer-feedbacks.controller.js.map
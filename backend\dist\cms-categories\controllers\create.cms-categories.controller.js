"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreateCmsCategoriesController = void 0;
const openapi = require("@nestjs/swagger");
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const jwt_auth_guard_1 = require("../../common/guards/jwt-auth.guard");
const create_cms_categories_service_1 = require("../services/create.cms-categories.service");
const create_cms_category_dto_1 = require("../dto/create.cms-category.dto");
const api_response_dto_1 = require("../../common/dto/api-response.dto");
const get_user_decorator_1 = require("../../common/decorators/get-user.decorator");
const roles_decorator_1 = require("../../common/decorators/roles.decorator");
const permissions_decorator_1 = require("../../common/decorators/permissions.decorator");
let CreateCmsCategoriesController = class CreateCmsCategoriesController {
    cmsCategoriesService;
    constructor(cmsCategoriesService) {
        this.cmsCategoriesService = cmsCategoriesService;
    }
    async create(createCmsCategoryDto, userId) {
        return this.cmsCategoriesService.create(createCmsCategoryDto, userId);
    }
    async bulkCreate(createCmsCategoryDtos, userId) {
        return this.cmsCategoriesService.bulkCreate(createCmsCategoryDtos, userId);
    }
    async duplicate(id, userId) {
        return this.cmsCategoriesService.duplicate(id, userId);
    }
};
exports.CreateCmsCategoriesController = CreateCmsCategoriesController;
__decorate([
    (0, common_1.Post)(),
    (0, roles_decorator_1.Roles)('ADMIN'),
    (0, permissions_decorator_1.Permissions)('cms-category:create'),
    (0, swagger_1.ApiOperation)({ summary: 'Tạo mới chuyên mục CMS' }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.CREATED,
        description: 'Chuyên mục CMS đã được tạo thành công.',
        type: api_response_dto_1.ApiResponseDto,
        schema: {
            properties: {
                data: { $ref: '#/components/schemas/CmsCategoryDto' },
            },
        },
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.BAD_REQUEST,
        description: 'Dữ liệu đầu vào không hợp lệ.',
        type: api_response_dto_1.ApiResponseDto,
        schema: { properties: { data: { type: 'object', nullable: true } } },
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.CONFLICT,
        description: 'Slug đã tồn tại.',
        type: api_response_dto_1.ApiResponseDto,
        schema: { properties: { data: { type: 'object', nullable: true } } },
    }),
    (0, swagger_1.ApiBody)({ type: create_cms_category_dto_1.CreateCmsCategoryDto }),
    openapi.ApiResponse({ status: 201, type: require("../dto/cms-category.dto").CmsCategoryDto }),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, get_user_decorator_1.GetUser)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [create_cms_category_dto_1.CreateCmsCategoryDto, String]),
    __metadata("design:returntype", Promise)
], CreateCmsCategoriesController.prototype, "create", null);
__decorate([
    (0, common_1.Post)('bulk'),
    (0, roles_decorator_1.Roles)('ADMIN'),
    (0, permissions_decorator_1.Permissions)('cms-category:create'),
    (0, swagger_1.ApiOperation)({ summary: 'Tạo nhiều chuyên mục CMS cùng lúc' }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.CREATED,
        description: 'Các chuyên mục CMS đã được tạo thành công.',
        type: api_response_dto_1.ApiResponseDto,
        schema: {
            properties: {
                data: {
                    type: 'array',
                    items: { $ref: '#/components/schemas/CmsCategoryDto' },
                },
            },
        },
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.BAD_REQUEST,
        description: 'Dữ liệu đầu vào không hợp lệ.',
        type: api_response_dto_1.ApiResponseDto,
        schema: { properties: { data: { type: 'object', nullable: true } } },
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.CONFLICT,
        description: 'Có slug trùng lặp.',
        type: api_response_dto_1.ApiResponseDto,
        schema: { properties: { data: { type: 'object', nullable: true } } },
    }),
    (0, swagger_1.ApiBody)({ type: [create_cms_category_dto_1.CreateCmsCategoryDto] }),
    openapi.ApiResponse({ status: 201, type: [require("../dto/cms-category.dto").CmsCategoryDto] }),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, get_user_decorator_1.GetUser)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Array, String]),
    __metadata("design:returntype", Promise)
], CreateCmsCategoriesController.prototype, "bulkCreate", null);
__decorate([
    (0, common_1.Post)(':id/duplicate'),
    (0, roles_decorator_1.Roles)('ADMIN'),
    (0, permissions_decorator_1.Permissions)('cms-category:create'),
    (0, swagger_1.ApiOperation)({ summary: 'Nhân bản chuyên mục CMS' }),
    (0, swagger_1.ApiParam)({
        name: 'id',
        description: 'ID của chuyên mục CMS cần nhân bản',
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.CREATED,
        description: 'Chuyên mục CMS đã được nhân bản thành công.',
        type: api_response_dto_1.ApiResponseDto,
        schema: {
            properties: { data: { $ref: '#/components/schemas/CmsCategoryDto' } },
        },
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.NOT_FOUND,
        description: 'Không tìm thấy chuyên mục CMS.',
        type: api_response_dto_1.ApiResponseDto,
        schema: { properties: { data: { type: 'object', nullable: true } } },
    }),
    openapi.ApiResponse({ status: 201, type: require("../dto/cms-category.dto").CmsCategoryDto }),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __param(1, (0, get_user_decorator_1.GetUser)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String]),
    __metadata("design:returntype", Promise)
], CreateCmsCategoriesController.prototype, "duplicate", null);
exports.CreateCmsCategoriesController = CreateCmsCategoriesController = __decorate([
    (0, swagger_1.ApiTags)('cms-categories'),
    (0, swagger_1.ApiBearerAuth)(),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, common_1.Controller)('cms/categories'),
    __metadata("design:paramtypes", [create_cms_categories_service_1.CreateCmsCategoriesService])
], CreateCmsCategoriesController);
//# sourceMappingURL=create.cms-categories.controller.js.map
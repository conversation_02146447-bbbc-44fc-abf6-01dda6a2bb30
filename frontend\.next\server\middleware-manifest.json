{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_b8fada48._.js", "server/edge/chunks/[root-of-the-server]__17f5e14d._.js", "server/edge/chunks/edge-wrapper_da667e18.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!api|_next|_static|.*\\.|favicon.ico|sitemap.xml).*))(\\\\.json)?[\\/#\\?]?$", "originalSource": "/((?!api|_next|_static|.*\\.|favicon.ico|sitemap.xml).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "MiT1oeakaJWkJ8EwFblXRNw+ISsoYbrMf8Y9opW88hE=", "__NEXT_PREVIEW_MODE_ID": "5b9d19116b5473bb71e83633899f857b", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "bad2cfc3f60e239a43ff40b726fb5235c6d3fffd03c4ff202f0b7a7fc99576ce", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "5b914992d766f481c7333004b501f374783c9de67e81861d328bddea659dbb8f"}}}, "sortedMiddleware": ["/"], "functions": {}}
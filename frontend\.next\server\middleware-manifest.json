{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_b8fada48._.js", "server/edge/chunks/[root-of-the-server]__b45db423._.js", "server/edge/chunks/edge-wrapper_cb0e5053.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!api|_next|_static|.*\\.|favicon.ico|sitemap.xml).*))(\\\\.json)?[\\/#\\?]?$", "originalSource": "/((?!api|_next|_static|.*\\.|favicon.ico|sitemap.xml).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "MiT1oeakaJWkJ8EwFblXRNw+ISsoYbrMf8Y9opW88hE=", "__NEXT_PREVIEW_MODE_ID": "96be77ed48ed90c4c86949a5726665b9", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "b3f5e2f78b7819c321648c8d9ae59d047178d0818c57564d267b35948881abd9", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "9a99f3b4732c4a90a8953a4729f7250e9226578b40984ce953cb41eefd9fe412"}}}, "sortedMiddleware": ["/"], "functions": {}}
"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CmsTags = void 0;
const openapi = require("@nestjs/swagger");
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
const typeorm_1 = require("typeorm");
const base_entity_1 = require("../../common/entities/base.entity");
const cms_posts_entity_1 = require("../../cms-posts/entity/cms-posts.entity");
let CmsTags = class CmsTags extends base_entity_1.BaseEntity {
    name;
    slug;
    description;
    imageUrl;
    metaTitle;
    metaDescription;
    metaKeywords;
    posts;
    getEntityName() {
        return 'cms_tags';
    }
    getSeoTitle() {
        return this.metaTitle || this.name;
    }
    getSeoDescription() {
        if (this.metaDescription) {
            return this.metaDescription;
        }
        if (this.description) {
            return this.description.length > 160
                ? this.description.substring(0, 157) + '...'
                : this.description;
        }
        return `Thẻ ${this.name} - Tìm hiểu thêm về chủ đề này`;
    }
    getSeoKeywordsArray() {
        if (!this.metaKeywords)
            return [];
        return this.metaKeywords.split(',').map(keyword => keyword.trim()).filter(Boolean);
    }
    hasImage() {
        return !!this.imageUrl;
    }
    static _OPENAPI_METADATA_FACTORY() {
        return { name: { required: true, type: () => String, maxLength: 100 }, slug: { required: true, type: () => String, maxLength: 100 }, description: { required: false, type: () => String, nullable: true }, imageUrl: { required: false, type: () => String, nullable: true, maxLength: 500 }, metaTitle: { required: false, type: () => String, nullable: true, maxLength: 255 }, metaDescription: { required: false, type: () => String, nullable: true, maxLength: 500 }, metaKeywords: { required: false, type: () => String, nullable: true, maxLength: 255 }, posts: { required: false, type: () => [require("../../cms-posts/entity/cms-posts.entity").CmsPosts] } };
    }
};
exports.CmsTags = CmsTags;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Tên thẻ',
        example: 'Thị trường vàng',
    }),
    (0, class_validator_1.IsNotEmpty)({ message: 'Tên thẻ không được để trống' }),
    (0, class_validator_1.IsString)({ message: 'Tên thẻ phải là chuỗi' }),
    (0, class_validator_1.MaxLength)(100, { message: 'Tên thẻ không được vượt quá 100 ký tự' }),
    (0, typeorm_1.Column)({ type: 'varchar', length: 100, nullable: false, unique: true, name: 'name' }),
    __metadata("design:type", String)
], CmsTags.prototype, "name", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Chuỗi URL thân thiện cho thẻ',
        example: 'thi-truong-vang',
    }),
    (0, class_validator_1.IsNotEmpty)({ message: 'Slug không được để trống' }),
    (0, class_validator_1.IsString)({ message: 'Slug phải là chuỗi' }),
    (0, class_validator_1.MaxLength)(100, { message: 'Slug không được vượt quá 100 ký tự' }),
    (0, typeorm_1.Column)({ type: 'varchar', length: 100, nullable: false, unique: true, name: 'slug' }),
    __metadata("design:type", String)
], CmsTags.prototype, "slug", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Mô tả ngắn về thẻ',
        example: 'Thẻ dành cho các bài viết về thị trường vàng',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)({ message: 'Mô tả phải là chuỗi' }),
    (0, typeorm_1.Column)({ type: 'text', nullable: true, name: 'description' }),
    __metadata("design:type", Object)
], CmsTags.prototype, "description", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'URL hình ảnh đại diện cho thẻ',
        example: 'https://example.com/images/tag-image.jpg',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)({ message: 'URL hình ảnh phải là chuỗi' }),
    (0, class_validator_1.MaxLength)(500, { message: 'URL hình ảnh không được vượt quá 500 ký tự' }),
    (0, typeorm_1.Column)({ type: 'varchar', length: 500, nullable: true, name: 'image_url' }),
    __metadata("design:type", Object)
], CmsTags.prototype, "imageUrl", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Tiêu đề SEO',
        example: 'Thị trường vàng - Tin tức và phân tích mới nhất',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)({ message: 'Tiêu đề SEO phải là chuỗi' }),
    (0, class_validator_1.MaxLength)(255, { message: 'Tiêu đề SEO không được vượt quá 255 ký tự' }),
    (0, typeorm_1.Column)({ type: 'varchar', length: 255, nullable: true, name: 'meta_title' }),
    __metadata("design:type", Object)
], CmsTags.prototype, "metaTitle", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Mô tả SEO',
        example: 'Theo dõi tin tức, phân tích và dự báo thị trường vàng mới nhất',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)({ message: 'Mô tả SEO phải là chuỗi' }),
    (0, class_validator_1.MaxLength)(500, { message: 'Mô tả SEO không được vượt quá 500 ký tự' }),
    (0, typeorm_1.Column)({ type: 'varchar', length: 500, nullable: true, name: 'meta_description' }),
    __metadata("design:type", Object)
], CmsTags.prototype, "metaDescription", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Từ khóa SEO (phân cách bằng dấu phẩy)',
        example: 'thị trường vàng, giá vàng, đầu tư vàng',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)({ message: 'Từ khóa SEO phải là chuỗi' }),
    (0, class_validator_1.MaxLength)(255, { message: 'Từ khóa SEO không được vượt quá 255 ký tự' }),
    (0, typeorm_1.Column)({ type: 'varchar', length: 255, nullable: true, name: 'meta_keywords' }),
    __metadata("design:type", Object)
], CmsTags.prototype, "metaKeywords", void 0);
__decorate([
    (0, typeorm_1.ManyToMany)(() => cms_posts_entity_1.CmsPosts, (post) => post.tags),
    __metadata("design:type", Array)
], CmsTags.prototype, "posts", void 0);
exports.CmsTags = CmsTags = __decorate([
    (0, typeorm_1.Entity)('cms_tags'),
    (0, typeorm_1.Index)(['name'], { unique: true }),
    (0, typeorm_1.Index)(['slug'], { unique: true })
], CmsTags);
//# sourceMappingURL=cms-tags.entity.js.map
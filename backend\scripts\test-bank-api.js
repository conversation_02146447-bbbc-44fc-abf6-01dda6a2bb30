/**
 * Script đơn giản để test API tạo ngân hàng
 * Sử dụng dữ liệu từ banklookup.net
 */

const axios = require('axios');

// C<PERSON>u hình
const CONFIG = {
  BACKEND_URL: 'http://localhost:3188',  // Sử dụng port từ .env
  ADMIN_EMAIL: '<EMAIL>',
  ADMIN_PASSWORD: 'adminX@123'
};

// Dữ liệu mẫu từ banklookup.net (một số ngân hàng phổ biến)
const SAMPLE_BANKS = [
  {
    brandName: 'Vietcombank',
    fullName: 'Ngân hàng TMCP Ngoại Thương Việt Nam',
    shortName: 'Vietcombank',
    code: 'VCB',
    bin: '970436',
    logoPath: 'https://api.vietqr.io/img/VCB.png',
    icon: 'https://cdn.banklookup.net/assets/images/bank-icons/VCB.svg',
    isActive: true
  },
  {
    brandName: 'VietinBank',
    fullName: 'Ngân hàng TMCP Công thương Việt Nam',
    shortName: 'VietinBank',
    code: 'VTB',
    bin: '970415',
    logoPath: 'https://api.vietqr.io/img/ICB.png',
    icon: 'https://cdn.banklookup.net/assets/images/bank-icons/ICB.svg',
    isActive: true
  },
  {
    brandName: 'BIDV',
    fullName: 'Ngân hàng TMCP Đầu tư và Phát triển Việt Nam',
    shortName: 'BIDV',
    code: 'BIDV',
    bin: '970418',
    logoPath: 'https://api.vietqr.io/img/BIDV.png',
    icon: 'https://cdn.banklookup.net/assets/images/bank-icons/BIDV.svg',
    isActive: true
  },
  {
    brandName: 'Agribank',
    fullName: 'Ngân hàng Nông nghiệp và Phát triển Nông thôn Việt Nam',
    shortName: 'Agribank',
    code: 'VARB',
    bin: '970405',
    logoPath: 'https://api.vietqr.io/img/VBA.png',
    icon: 'https://cdn.banklookup.net/assets/images/bank-icons/VBA.svg',
    isActive: true
  },
  {
    brandName: 'Techcombank',
    fullName: 'Ngân hàng TMCP Kỹ thương Việt Nam',
    shortName: 'Techcombank',
    code: 'TCB',
    bin: '970407',
    logoPath: 'https://api.vietqr.io/img/TCB.png',
    icon: 'https://cdn.banklookup.net/assets/images/bank-icons/TCB.svg',
    isActive: true
  }
];

// Utility functions
const log = (message, type = 'INFO') => {
  const timestamp = new Date().toLocaleTimeString();
  const prefix = type === 'ERROR' ? '❌' : type === 'SUCCESS' ? '✅' : 'ℹ️';
  console.log(`[${timestamp}] ${prefix} ${message}`);
};

const sleep = (ms) => new Promise(resolve => setTimeout(resolve, ms));

// Đăng nhập và lấy token
async function login() {
  try {
    log('Đang đăng nhập...');
    const response = await axios.post(`${CONFIG.BACKEND_URL}/api/v1/auth/login`, {
      email: CONFIG.ADMIN_EMAIL,
      password: CONFIG.ADMIN_PASSWORD
    });
    
    if (response.data && response.data.access_token) {
      log('Đăng nhập thành công', 'SUCCESS');
      return response.data.access_token;
    } else {
      throw new Error('Không nhận được access token');
    }
  } catch (error) {
    log(`Lỗi đăng nhập: ${error.message}`, 'ERROR');
    if (error.response) {
      log(`Status: ${error.response.status}`, 'ERROR');
      log(`Data: ${JSON.stringify(error.response.data)}`, 'ERROR');
    }
    throw error;
  }
}

// Test tạo một ngân hàng
async function testCreateBank(bankData, token) {
  try {
    log(`Đang tạo ngân hàng: ${bankData.brandName}`);
    
    const response = await axios.post(
      `${CONFIG.BACKEND_URL}/api/v1/banks`,
      bankData,
      {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      }
    );
    
    log(`Tạo thành công: ${response.data.brandName} (ID: ${response.data.id})`, 'SUCCESS');
    return response.data;
  } catch (error) {
    log(`Lỗi tạo ngân hàng ${bankData.brandName}: ${error.message}`, 'ERROR');
    if (error.response) {
      log(`Status: ${error.response.status}`, 'ERROR');
      log(`Data: ${JSON.stringify(error.response.data)}`, 'ERROR');
    }
    return null;
  }
}

// Test tạo nhiều ngân hàng
async function testBulkCreate(banksData, token) {
  try {
    log(`Đang tạo ${banksData.length} ngân hàng cùng lúc...`);
    
    const response = await axios.post(
      `${CONFIG.BACKEND_URL}/api/v1/banks/bulk`,
      banksData,
      {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      }
    );
    
    log(`Tạo bulk thành công ${response.data.length} ngân hàng`, 'SUCCESS');
    response.data.forEach((bank, index) => {
      log(`  ${index + 1}. ${bank.brandName} (${bank.code})`);
    });
    return response.data;
  } catch (error) {
    log(`Lỗi tạo bulk: ${error.message}`, 'ERROR');
    if (error.response) {
      log(`Status: ${error.response.status}`, 'ERROR');
      log(`Data: ${JSON.stringify(error.response.data)}`, 'ERROR');
    }
    return null;
  }
}

// Test đọc danh sách ngân hàng
async function testReadBanks(token) {
  try {
    log('Đang lấy danh sách ngân hàng...');
    
    const response = await axios.get(
      `${CONFIG.BACKEND_URL}/api/v1/banks`,
      {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      }
    );
    
    log(`Lấy thành công ${response.data.data?.length || 0} ngân hàng`, 'SUCCESS');
    return response.data;
  } catch (error) {
    log(`Lỗi lấy danh sách: ${error.message}`, 'ERROR');
    return null;
  }
}

// Test chính
async function runTest() {
  try {
    log('=== BẮT ĐẦU TEST API NGÂN HÀNG ===');
    
    // 1. Kiểm tra kết nối backend
    try {
      await axios.get(`${CONFIG.BACKEND_URL}/api/v1/health`);
      log('Backend đang hoạt động', 'SUCCESS');
    } catch (error) {
      log('Không thể kết nối đến backend', 'ERROR');
      throw error;
    }
    
    // 2. Đăng nhập
    const token = await login();
    
    // 3. Test đọc danh sách hiện tại
    log('\n=== TEST ĐỌC DANH SÁCH ===');
    const currentBanks = await testReadBanks(token);
    
    // 4. Test tạo một ngân hàng
    log('\n=== TEST TẠO MỘT NGÂN HÀNG ===');
    const firstBank = SAMPLE_BANKS[0];
    const createdBank = await testCreateBank(firstBank, token);
    
    await sleep(1000);
    
    // 5. Test tạo nhiều ngân hàng
    log('\n=== TEST TẠO NHIỀU NGÂN HÀNG ===');
    const remainingBanks = SAMPLE_BANKS.slice(1, 4); // Lấy 3 ngân hàng tiếp theo
    const bulkResult = await testBulkCreate(remainingBanks, token);
    
    await sleep(1000);
    
    // 6. Test đọc lại danh sách sau khi tạo
    log('\n=== TEST ĐỌC DANH SÁCH SAU KHI TẠO ===');
    const updatedBanks = await testReadBanks(token);
    
    // 7. Thống kê
    log('\n=== THỐNG KÊ ===');
    log(`Số ngân hàng ban đầu: ${currentBanks?.data?.length || 0}`);
    log(`Số ngân hàng sau khi tạo: ${updatedBanks?.data?.length || 0}`);
    log(`Số ngân hàng đã tạo thành công: ${(createdBank ? 1 : 0) + (bulkResult?.length || 0)}`);
    
    log('\n=== HOÀN THÀNH TEST ===', 'SUCCESS');
    
  } catch (error) {
    log(`Lỗi trong quá trình test: ${error.message}`, 'ERROR');
    process.exit(1);
  }
}

// Chạy test nếu file được gọi trực tiếp
if (require.main === module) {
  // Kiểm tra arguments
  const args = process.argv.slice(2);
  if (args.includes('--help') || args.includes('-h')) {
    console.log(`
Cách sử dụng: node test-bank-api.js [options]

Options:
  --backend-url <url>    URL của backend (mặc định: http://localhost:3000)
  --admin-email <email>  Email admin (mặc định: <EMAIL>)
  --admin-password <pwd> Password admin (mặc định: admin123)
  --help, -h            Hiển thị help

Ví dụ:
  node test-bank-api.js
  node test-bank-api.js --backend-url http://localhost:3001
  node test-bank-api.js --admin-email <EMAIL> --admin-password test123
`);
    process.exit(0);
  }
  
  // Parse arguments
  for (let i = 0; i < args.length; i += 2) {
    const arg = args[i];
    const value = args[i + 1];
    
    switch (arg) {
      case '--backend-url':
        CONFIG.BACKEND_URL = value;
        break;
      case '--admin-email':
        CONFIG.ADMIN_EMAIL = value;
        break;
      case '--admin-password':
        CONFIG.ADMIN_PASSWORD = value;
        break;
    }
  }
  
  log(`Backend URL: ${CONFIG.BACKEND_URL}`);
  log(`Admin Email: ${CONFIG.ADMIN_EMAIL}`);
  
  runTest().catch(error => {
    log(`Lỗi không mong đợi: ${error.message}`, 'ERROR');
    process.exit(1);
  });
}

module.exports = {
  runTest,
  testCreateBank,
  testBulkCreate,
  SAMPLE_BANKS
};

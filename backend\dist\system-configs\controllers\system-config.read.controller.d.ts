import { ReadSystemConfigService } from '../services/read.system-config.service';
import { SystemConfigDto } from '../dto/system-config.dto';
import { CustomPaginationQueryDto } from '../../common/dto/custom-pagination-query.dto';
import { PaginationResponseDto } from '../../common/dto/pagination-response.dto';
export declare class SystemConfigReadController {
    private readonly systemConfigService;
    private readonly logger;
    constructor(systemConfigService: ReadSystemConfigService);
    findAll(paginationQuery: CustomPaginationQueryDto): Promise<PaginationResponseDto<SystemConfigDto>>;
    search(paginationQuery: CustomPaginationQueryDto): Promise<PaginationResponseDto<SystemConfigDto>>;
    findOne(id: string, relations?: string): Promise<SystemConfigDto>;
    findByKey(key: string, relations?: string): Promise<SystemConfigDto>;
    findByGroup(group: string, paginationQuery: CustomPaginationQueryDto): Promise<PaginationResponseDto<SystemConfigDto>>;
    getConfigGroups(): Promise<SystemConfigDto[]>;
    findDeleted(paginationQuery: CustomPaginationQueryDto): Promise<PaginationResponseDto<SystemConfigDto>>;
}

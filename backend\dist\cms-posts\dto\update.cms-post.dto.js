"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UpdateCmsPostDto = void 0;
const openapi = require("@nestjs/swagger");
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
const cms_posts_entity_1 = require("../entity/cms-posts.entity");
class UpdateCmsPostDto {
    postType;
    title;
    slug;
    excerpt;
    content;
    featuredImageUrl;
    status;
    publishedAt;
    eventStartDate;
    eventEndDate;
    eventLocation;
    metaTitle;
    metaDescription;
    metaKeywords;
    allowComments;
    isFeatured;
    categoryId;
    authorId;
    static _OPENAPI_METADATA_FACTORY() {
        return { postType: { required: false, enum: require("../entity/cms-posts.entity").CmsPostType }, title: { required: false, type: () => String, maxLength: 255 }, slug: { required: false, type: () => String, maxLength: 255 }, excerpt: { required: false, type: () => String }, content: { required: false, type: () => String }, featuredImageUrl: { required: false, type: () => String }, status: { required: false, enum: require("../entity/cms-posts.entity").CmsPostStatus }, publishedAt: { required: false, type: () => String }, eventStartDate: { required: false, type: () => String }, eventEndDate: { required: false, type: () => String }, eventLocation: { required: false, type: () => String, maxLength: 255 }, metaTitle: { required: false, type: () => String, maxLength: 255 }, metaDescription: { required: false, type: () => String }, metaKeywords: { required: false, type: () => String, maxLength: 255 }, allowComments: { required: false, type: () => Boolean }, isFeatured: { required: false, type: () => Boolean }, categoryId: { required: false, type: () => String, format: "uuid" }, authorId: { required: false, type: () => String, format: "uuid" } };
    }
}
exports.UpdateCmsPostDto = UpdateCmsPostDto;
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Loại bài viết',
        example: cms_posts_entity_1.CmsPostType.NEWS,
        enum: cms_posts_entity_1.CmsPostType,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)(cms_posts_entity_1.CmsPostType, { message: 'Loại bài viết không hợp lệ' }),
    __metadata("design:type", String)
], UpdateCmsPostDto.prototype, "postType", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Tiêu đề bài viết',
        example: 'Tin tức cập nhật về thị trường vàng',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)({ message: 'Tiêu đề phải là chuỗi' }),
    (0, class_validator_1.MaxLength)(255, { message: 'Tiêu đề không được vượt quá 255 ký tự' }),
    __metadata("design:type", String)
], UpdateCmsPostDto.prototype, "title", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Chuỗi URL thân thiện',
        example: 'tin-tuc-cap-nhat-ve-thi-truong-vang',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)({ message: 'Slug phải là chuỗi' }),
    (0, class_validator_1.MaxLength)(255, { message: 'Slug không được vượt quá 255 ký tự' }),
    __metadata("design:type", String)
], UpdateCmsPostDto.prototype, "slug", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Đoạn tóm tắt ngắn',
        example: 'Thị trường vàng tuần này có nhiều biến động mới...',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)({ message: 'Tóm tắt phải là chuỗi' }),
    __metadata("design:type", String)
], UpdateCmsPostDto.prototype, "excerpt", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Nội dung đầy đủ (HTML)',
        example: '<p>Nội dung chi tiết cập nhật về thị trường vàng...</p>',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)({ message: 'Nội dung phải là chuỗi' }),
    __metadata("design:type", String)
], UpdateCmsPostDto.prototype, "content", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'URL hình ảnh đại diện',
        example: 'https://example.com/images/featured-updated.jpg',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)({ message: 'URL hình ảnh phải là chuỗi' }),
    __metadata("design:type", String)
], UpdateCmsPostDto.prototype, "featuredImageUrl", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Trạng thái bài viết',
        example: cms_posts_entity_1.CmsPostStatus.PUBLISHED,
        enum: cms_posts_entity_1.CmsPostStatus,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)(cms_posts_entity_1.CmsPostStatus, { message: 'Trạng thái không hợp lệ' }),
    __metadata("design:type", String)
], UpdateCmsPostDto.prototype, "status", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Thời gian xuất bản',
        example: '2023-01-01T00:00:00.000Z',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsDateString)({}, { message: 'Thời gian xuất bản không hợp lệ' }),
    __metadata("design:type", String)
], UpdateCmsPostDto.prototype, "publishedAt", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Ngày bắt đầu sự kiện (cho post_type = event)',
        example: '2023-01-01T09:00:00.000Z',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsDateString)({}, { message: 'Ngày bắt đầu sự kiện không hợp lệ' }),
    __metadata("design:type", String)
], UpdateCmsPostDto.prototype, "eventStartDate", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Ngày kết thúc sự kiện (cho post_type = event)',
        example: '2023-01-01T17:00:00.000Z',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsDateString)({}, { message: 'Ngày kết thúc sự kiện không hợp lệ' }),
    __metadata("design:type", String)
], UpdateCmsPostDto.prototype, "eventEndDate", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Địa điểm sự kiện (cho post_type = event)',
        example: 'Trung tâm Hội nghị Quốc gia',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)({ message: 'Địa điểm sự kiện phải là chuỗi' }),
    (0, class_validator_1.MaxLength)(255, { message: 'Địa điểm sự kiện không được vượt quá 255 ký tự' }),
    __metadata("design:type", String)
], UpdateCmsPostDto.prototype, "eventLocation", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Tiêu đề SEO',
        example: 'Tin tức thị trường vàng - Cập nhật mới nhất',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)({ message: 'Tiêu đề SEO phải là chuỗi' }),
    (0, class_validator_1.MaxLength)(255, { message: 'Tiêu đề SEO không được vượt quá 255 ký tự' }),
    __metadata("design:type", String)
], UpdateCmsPostDto.prototype, "metaTitle", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Mô tả SEO',
        example: 'Theo dõi tin tức và cập nhật mới nhất về thị trường vàng',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)({ message: 'Mô tả SEO phải là chuỗi' }),
    __metadata("design:type", String)
], UpdateCmsPostDto.prototype, "metaDescription", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Từ khóa SEO',
        example: 'thị trường vàng, tin tức, giá vàng',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)({ message: 'Từ khóa SEO phải là chuỗi' }),
    (0, class_validator_1.MaxLength)(255, { message: 'Từ khóa SEO không được vượt quá 255 ký tự' }),
    __metadata("design:type", String)
], UpdateCmsPostDto.prototype, "metaKeywords", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Cho phép bình luận',
        example: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsBoolean)({ message: 'Cho phép bình luận phải là boolean' }),
    __metadata("design:type", Boolean)
], UpdateCmsPostDto.prototype, "allowComments", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Bài viết nổi bật',
        example: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsBoolean)({ message: 'Bài viết nổi bật phải là boolean' }),
    __metadata("design:type", Boolean)
], UpdateCmsPostDto.prototype, "isFeatured", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'ID của chuyên mục',
        example: '550e8400-e29b-41d4-a716-************',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsUUID)('4', { message: 'ID chuyên mục không hợp lệ' }),
    __metadata("design:type", String)
], UpdateCmsPostDto.prototype, "categoryId", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'ID của tác giả',
        example: '550e8400-e29b-41d4-a716-************',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsUUID)('4', { message: 'ID tác giả không hợp lệ' }),
    __metadata("design:type", String)
], UpdateCmsPostDto.prototype, "authorId", void 0);
//# sourceMappingURL=update.cms-post.dto.js.map
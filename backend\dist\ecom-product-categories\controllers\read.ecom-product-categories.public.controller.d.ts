import { ReadEcomProductCategoriesService } from '../services/read.ecom-product-categories.service';
import { EcomProductCategoryPublicDto } from '../dto/ecom-product-category.public.dto';
import { CustomPaginationQueryDto } from '../../common/dto/custom-pagination-query.dto';
import { PaginationResponseDto } from '../../common/dto/pagination-response.dto';
export declare class ReadEcomProductCategoriesPublicController {
    private readonly ecomProductCategoriesService;
    private readonly logger;
    constructor(ecomProductCategoriesService: ReadEcomProductCategoriesService);
    private toPublicDto;
    private toPublicDtos;
    getActiveProductCategories(paginationQuery: CustomPaginationQueryDto): Promise<PaginationResponseDto<EcomProductCategoryPublicDto>>;
    getCarouselProductCategories(limit?: number): Promise<EcomProductCategoryPublicDto[]>;
    getRandomizedProductCategories(limit?: number): Promise<{
        data: EcomProductCategoryPublicDto[];
        meta: {
            total: number;
            returned: number;
            limit: number;
        };
    }>;
    search(searchTerm: string, paginationQuery: CustomPaginationQueryDto): Promise<PaginationResponseDto<EcomProductCategoryPublicDto>>;
    findAllPublic(paginationQuery: CustomPaginationQueryDto): Promise<PaginationResponseDto<EcomProductCategoryPublicDto>>;
}

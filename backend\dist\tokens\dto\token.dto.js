"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.TokenDto = void 0;
const openapi = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
const swagger_1 = require("@nestjs/swagger");
const token_category_dto_1 = require("../../token-categories/dto/token-category.dto");
const token_price_dto_1 = require("../../token-prices/dto/token-price.dto");
const asset_dto_1 = require("../../token-assets/dto/asset.dto");
const user_dto_1 = require("../../users/dto/user.dto");
const class_transformer_1 = require("class-transformer");
class TokenDto {
    id;
    tokenCode;
    tokenName;
    categoryId;
    description;
    decimalPlaces;
    minTradeAmount;
    maxTradeAmount;
    oz;
    isActive;
    createdAt;
    updatedAt;
    createdBy;
    updatedBy;
    category;
    tokenAssets;
    tokenPrices;
    creator;
    updater;
    deleter;
    static _OPENAPI_METADATA_FACTORY() {
        return { id: { required: true, type: () => String, format: "uuid" }, tokenCode: { required: true, type: () => String }, tokenName: { required: true, type: () => String }, categoryId: { required: true, type: () => String, format: "uuid" }, description: { required: false, type: () => String }, decimalPlaces: { required: false, type: () => Number }, minTradeAmount: { required: false, type: () => Number }, maxTradeAmount: { required: false, type: () => Number }, oz: { required: false, type: () => Number }, isActive: { required: true, type: () => Boolean }, createdAt: { required: true, type: () => Date }, updatedAt: { required: true, type: () => Date }, createdBy: { required: false, type: () => String, format: "uuid" }, updatedBy: { required: false, type: () => String, format: "uuid" }, category: { required: false, type: () => require("../../token-categories/dto/token-category.dto").TokenCategoryDto }, tokenAssets: { required: false, type: () => [require("../../token-assets/dto/asset.dto").AssetDto] }, tokenPrices: { required: false, type: () => [require("../../token-prices/dto/token-price.dto").TokenPriceDto] }, creator: { required: false, type: () => require("../../users/dto/user.dto").UserDto }, updater: { required: false, type: () => require("../../users/dto/user.dto").UserDto }, deleter: { required: false, type: () => require("../../users/dto/user.dto").UserDto } };
    }
}
exports.TokenDto = TokenDto;
__decorate([
    (0, class_transformer_1.Expose)(),
    (0, swagger_1.ApiProperty)({ description: 'ID duy nhất của token' }),
    (0, class_validator_1.IsUUID)(),
    __metadata("design:type", String)
], TokenDto.prototype, "id", void 0);
__decorate([
    (0, class_transformer_1.Expose)(),
    (0, swagger_1.ApiProperty)({ description: 'Mã token' }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], TokenDto.prototype, "tokenCode", void 0);
__decorate([
    (0, class_transformer_1.Expose)(),
    (0, swagger_1.ApiProperty)({ description: 'Tên token' }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], TokenDto.prototype, "tokenName", void 0);
__decorate([
    (0, class_transformer_1.Expose)(),
    (0, swagger_1.ApiProperty)({ description: 'ID loại token' }),
    (0, class_validator_1.IsUUID)(),
    __metadata("design:type", String)
], TokenDto.prototype, "categoryId", void 0);
__decorate([
    (0, class_transformer_1.Expose)(),
    (0, swagger_1.ApiProperty)({ description: 'Mô tả chi tiết về token', required: false }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], TokenDto.prototype, "description", void 0);
__decorate([
    (0, class_transformer_1.Expose)(),
    (0, swagger_1.ApiProperty)({ description: 'Số chữ số thập phân', required: false }),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Number)
], TokenDto.prototype, "decimalPlaces", void 0);
__decorate([
    (0, class_transformer_1.Expose)(),
    (0, swagger_1.ApiProperty)({ description: 'Số lượng giao dịch tối thiểu', required: false }),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Number)
], TokenDto.prototype, "minTradeAmount", void 0);
__decorate([
    (0, class_transformer_1.Expose)(),
    (0, swagger_1.ApiProperty)({ description: 'Số lượng giao dịch tối đa', required: false }),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Number)
], TokenDto.prototype, "maxTradeAmount", void 0);
__decorate([
    (0, class_transformer_1.Expose)(),
    (0, swagger_1.ApiProperty)({ description: 'Khối lượng cố định (oz)', required: false }),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Number)
], TokenDto.prototype, "oz", void 0);
__decorate([
    (0, class_transformer_1.Expose)(),
    (0, swagger_1.ApiProperty)({ description: 'Trạng thái hoạt động' }),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], TokenDto.prototype, "isActive", void 0);
__decorate([
    (0, class_transformer_1.Expose)(),
    (0, swagger_1.ApiProperty)({ description: 'Thời gian tạo' }),
    (0, class_validator_1.IsDate)(),
    __metadata("design:type", Date)
], TokenDto.prototype, "createdAt", void 0);
__decorate([
    (0, class_transformer_1.Expose)(),
    (0, swagger_1.ApiProperty)({ description: 'Thời gian cập nhật' }),
    (0, class_validator_1.IsDate)(),
    __metadata("design:type", Date)
], TokenDto.prototype, "updatedAt", void 0);
__decorate([
    (0, class_transformer_1.Expose)(),
    (0, swagger_1.ApiProperty)({ description: 'ID người tạo', required: false }),
    (0, class_validator_1.IsUUID)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], TokenDto.prototype, "createdBy", void 0);
__decorate([
    (0, class_transformer_1.Expose)(),
    (0, swagger_1.ApiProperty)({ description: 'ID người cập nhật', required: false }),
    (0, class_validator_1.IsUUID)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], TokenDto.prototype, "updatedBy", void 0);
__decorate([
    (0, class_transformer_1.Expose)(),
    (0, swagger_1.ApiProperty)({
        description: 'Loại token',
        type: () => token_category_dto_1.TokenCategoryDto,
        required: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => token_category_dto_1.TokenCategoryDto),
    __metadata("design:type", token_category_dto_1.TokenCategoryDto)
], TokenDto.prototype, "category", void 0);
__decorate([
    (0, class_transformer_1.Expose)(),
    (0, swagger_1.ApiProperty)({
        description: 'Tài sản token',
        type: () => [asset_dto_1.AssetDto],
        required: false,
    }),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Array)
], TokenDto.prototype, "tokenAssets", void 0);
__decorate([
    (0, class_transformer_1.Expose)(),
    (0, swagger_1.ApiProperty)({
        description: 'Giá token',
        type: () => [token_price_dto_1.TokenPriceDto],
        required: false,
    }),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Array)
], TokenDto.prototype, "tokenPrices", void 0);
__decorate([
    (0, class_transformer_1.Expose)(),
    (0, swagger_1.ApiPropertyOptional)({ description: 'Người đã tạo token' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => user_dto_1.UserDto),
    __metadata("design:type", user_dto_1.UserDto)
], TokenDto.prototype, "creator", void 0);
__decorate([
    (0, class_transformer_1.Expose)(),
    (0, swagger_1.ApiPropertyOptional)({ description: 'Người đã cập nhật token' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => user_dto_1.UserDto),
    __metadata("design:type", user_dto_1.UserDto)
], TokenDto.prototype, "updater", void 0);
__decorate([
    (0, class_transformer_1.Expose)(),
    (0, swagger_1.ApiPropertyOptional)({ description: 'Người đã xóa token' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => user_dto_1.UserDto),
    __metadata("design:type", user_dto_1.UserDto)
], TokenDto.prototype, "deleter", void 0);
//# sourceMappingURL=token.dto.js.map
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import {
  <PERSON>alog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle
} from "@/components/ui/dialog"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuShortcut,
  DropdownMenuTrigger
} from "@/components/ui/dropdown-menu"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Popconfirm } from "@/components/ui/popconfirm"
import {
  Popover,
  PopoverContent,
  PopoverTrigger
} from "@/components/ui/popover"

import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
} from "@/components/ui/command"
import { DateTimeDisplay } from "@/components/ui/date-time-display"
import { api } from '@/lib/api'
import { cn } from "@/lib/utils"
import { ForexQuote } from '@/services/websocket/polygon-forex-socket.service'
import { IconCurrencyDong } from "@tabler/icons-react"
import { type ColumnDef, Row } from "@tanstack/react-table"
import { format, isValid } from "date-fns"
import { vi } from "date-fns/locale"
import {
  ArrowUpDown,
  CalendarPlus,
  Check,
  ChevronsUpDown,
  Eye,
  MoreVertical
} from "lucide-react"
import { useId, useState } from "react"
import { toast } from "sonner"
import { ApproveStatus, BusinessType, OrderBook, OrderStatus, OrderType } from "../type/order-books"

// Format currency with VND
const formatCurrency = (amount: number | undefined) => {
  if (amount === undefined || amount === null) return "---"
  return new Intl.NumberFormat('vi-VN', {
    maximumFractionDigits: 0
  }).format(amount)
}

// Format date from ISO string
const formatDate = (dateStr: string | null) => {
  if (!dateStr) return "---"
  const date = new Date(dateStr)
  if (!isValid(date)) return "Không hợp lệ"
  try {
    return format(date, "dd/MM/yyyy HH:mm", { locale: vi })
  } catch (error) {
    return "Không hợp lệ"
  }
}

// Get color for order status badge
const getStatusColor = (status: OrderStatus) => {
  switch (status) {
    case OrderStatus.COMPLETED:
      return 'bg-green-100 text-green-800 font-normal';
    case OrderStatus.DEPOSITED:
      return 'bg-purple-100 text-purple-800 font-normal';
    case OrderStatus.TERMINATED:
      return 'bg-red-100 text-red-800 font-normal';
    case OrderStatus.CANCELLED:
      return 'bg-orange-100 text-orange-800 font-normal';
    default:
      return 'bg-gray-100 text-gray-800 font-normal';
  }
};

// Order status in Vietnamese
const getStatusName = (status: OrderStatus) => {
  switch (status) {
    case OrderStatus.COMPLETED:
      return 'Đã tất toán';
    case OrderStatus.DEPOSITED:
      return 'Đã ký quỹ';
    case OrderStatus.TERMINATED:
      return 'Đã cắt hợp đồng';
    case OrderStatus.CANCELLED:
      return 'Đã hủy';
    default:
      return status;
  }
};

// Order type in Vietnamese
const getOrderTypeName = (type: OrderType) => {
  switch (type) {
    case OrderType.BUY:
      return 'Mua';
    case OrderType.SELL:
      return 'Bán';
    default:
      return type;
  }
};

// Get color for order type
const getOrderTypeColor = (type: OrderType) => {
  switch (type) {
    case OrderType.BUY:
      return 'bg-green-100 text-green-800 font-normal';
    case OrderType.SELL:
      return 'bg-red-100 text-red-800 font-normal';
    default:
      return 'bg-gray-100 text-gray-800 font-normal';
  }
};

// Get color for business type
const getBusinessTypeColor = (type: BusinessType | undefined) => {
  switch (type) {
    case BusinessType.NORMAL:
      return 'bg-orange-100 text-orange-800 font-normal';
    case BusinessType.IMMEDIATE_DELIVERY:
      return 'bg-blue-100 text-blue-800 font-normal';
    default:
      return 'bg-gray-100 text-gray-800 font-normal';
  }
};

// Get color for approve status badge
const getApproveStatusColor = (status: ApproveStatus | undefined) => {
  switch (status) {
    case ApproveStatus.PENDING:
      return 'bg-yellow-100 text-yellow-800 font-normal';
    case ApproveStatus.APPROVED:
      return 'bg-green-100 text-green-800 font-normal';
    default:
      return 'bg-gray-100 text-gray-800 font-normal';
  }
};

// Approve status in Vietnamese
const getApproveStatusName = (status: ApproveStatus | undefined) => {
  switch (status) {
    case ApproveStatus.PENDING:
      return 'Chờ phê duyệt';
    case ApproveStatus.APPROVED:
      return 'Đã phê duyệt';
    default:
      return '---';
  }
};

// Add helper function to calculate real-time total price
const calculateRealTimePrice = (order: OrderBook, currentQuote: ForexQuote | null) => {
  if (!currentQuote || order.status === OrderStatus.COMPLETED) {
    return order.totalPrice || 0;
  }

  // Get the appropriate price based on order type
  const price = order.orderType === OrderType.BUY ? currentQuote.askPrice : currentQuote.bidPrice;

  // Calculate total volume and price
  const totalVolume = order.details?.reduce((sum, detail) => {
    const volume = detail.quantity || 0;
    const weight = detail.product?.weight || 1;
    return sum + (volume * weight);
  }, 0) || 0;

  // Calculate new total price
  const basePrice = price * totalVolume * 1.2056 * 27000;
  return Math.round(basePrice + (basePrice * 0.11)); // Add 11% VAT
};

// Add helper function to calculate real-time settlement amount
const calculateRealTimeSettlement = (order: OrderBook, currentQuote: ForexQuote | null) => {
  if (!currentQuote || order.status === OrderStatus.COMPLETED) {
    return order.settlementPrice || 0;
  }

  const totalPrice = calculateRealTimePrice(order, currentQuote);

  // For physical orders, settlement is 100%
  if (order.businessType === BusinessType.IMMEDIATE_DELIVERY) {
    return totalPrice;
  }

  // For short investment, settlement is remaining balance (90% if not paid)
  return Math.round(totalPrice * 0.9);
};

// Add helper function to get price color based on order type
const getPriceColor = (order: OrderBook) => {
  if (order.status === OrderStatus.COMPLETED) return "";
  return order.orderType === OrderType.BUY ? "text-green-500" : "text-red-500";
};

interface ActionsProps {
  row: Row<OrderBook>
  onViewDetail: (order: OrderBook) => void
  onExtendSettlement?: (order: OrderBook) => void
  onSettlement?: (order: OrderBook, option: 'silver' | 'deposit' | 'cancel') => void
}

function Actions({ row, onViewDetail, onExtendSettlement, onSettlement }: ActionsProps) {
  const order = row.original
  const [showExtendDialog, setShowExtendDialog] = useState(false)

  // Xử lý khi click vào nút xem chi tiết
  const handleViewDetail = async (order: OrderBook) => {
    try {
      // Sử dụng api client để gọi API
      const response = await api.get<OrderBook>(`order-books/${order.id}`);

      // Chỉ truyền dữ liệu chi tiết nếu API trả về thành công
      if (response) {
        onViewDetail(response);
      } else {
        // Nếu không có response, hiển thị thông báo lỗi
        toast.error("Không thể tải thông tin chi tiết lệnh. Vui lòng thử lại sau.");
      }
    } catch (error) {
      console.error('Error fetching order detail:', error);
      // Hiển thị thông báo lỗi cho người dùng
      toast.error("Có lỗi xảy ra khi tải thông tin lệnh. Vui lòng thử lại sau.");
    }
  };

  // Kiểm tra xem lệnh có thể gia hạn hay không
  const canExtend = () => {
    // Chỉ cho phép gia hạn lệnh mua/bán đang ở trạng thái WAIT_PAYMENT
    return (
      order.status === OrderStatus.DEPOSITED &&
      order.settlementDeadline // Phải có hạn tất toán
    )
  }

  // Kiểm tra xem lệnh có thể tất toán hay không
  const canSettle = () => {
    // Chỉ cho phép tất toán lệnh mua/bán đang ở trạng thái WAIT_PAYMENT
    return (
      (order.orderType === OrderType.BUY || order.orderType === OrderType.SELL) &&
      order.status === OrderStatus.DEPOSITED
    )
  }

  // Xử lý khi người dùng chọn phương thức tất toán
  const handleSettlementOption = (option: 'silver' | 'deposit' | 'cancel') => {
    if (onSettlement) {
      onSettlement(order, option);
    }
  };

  return (
    <>
      {/* Dialog xác nhận gia hạn */}
      <Dialog open={showExtendDialog} onOpenChange={setShowExtendDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Xác nhận gia hạn</DialogTitle>
            <DialogDescription>
              Bạn có chắc chắn muốn gia hạn thời gian tất toán thêm 15 ngày cho lệnh này?
              <p className="mt-2">Phí gia hạn là 100,000 VND và sẽ được trừ từ số dư của bạn.</p>
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowExtendDialog(false)}>Hủy</Button>
            <Button
              onClick={() => {
                if (onExtendSettlement) {
                  onExtendSettlement(order);
                }
                setShowExtendDialog(false);
              }}
            >
              Xác nhận gia hạn
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      <div className="flex justify-end px-1 sticky-action-cell h-full items-center">
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button
              variant="ghost"
              size="sm"
              className="flex h-6 w-6 p-0 data-[state=open]:bg-muted transition-colors hover:bg-muted"
            >
              <MoreVertical className="h-3 w-3" />
              <span className="sr-only">Mở menu</span>
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end" className="p-1">
            <DropdownMenuItem onClick={() => handleViewDetail(order)}>
              <Eye className="mr-2 h-3.5 w-3.5" />
              <span className="flex-1 text-sm">Chi tiết</span>
              <DropdownMenuShortcut className="text-sm">⌘V</DropdownMenuShortcut>
            </DropdownMenuItem>

            {canExtend() && onExtendSettlement && (
              <>
                <DropdownMenuSeparator />
                <DropdownMenuItem onClick={() => setShowExtendDialog(true)}>
                  <CalendarPlus className="mr-2 h-3.5 w-3.5 text-blue-500" />
                  <span className="flex-1 text-sm text-blue-500">Gia hạn thời gian</span>
                </DropdownMenuItem>
              </>
            )}

            {canSettle() && onSettlement && (
              <>
                <DropdownMenuSeparator />
                {order.businessType === BusinessType.IMMEDIATE_DELIVERY ? (
                  // Với bạc giao ngay, hiển thị popconfirm hỏi có muốn rút bạc không
                  <DropdownMenuItem onSelect={(e) => {
                    // Ngăn đóng dropdown menu khi mở popconfirm
                    e.preventDefault();
                  }}>
                    <Popconfirm
                      title="Xác nhận rút bạc"
                      description="Bạn có muốn rút bạc không?"
                      cancelText="Không"
                      confirmText="Có"
                      onConfirm={() => handleSettlementOption('silver')}
                      onCancel={() => handleSettlementOption('cancel')}
                    >
                      <div className="flex items-center w-full cursor-pointer">
                        <Check className="mr-2 h-3.5 w-3.5 text-green-500" />
                        <span className="flex-1 text-sm text-green-500">Tất toán</span>
                      </div>
                    </Popconfirm>
                  </DropdownMenuItem>
                ) : (
                  // Với bạc ký quỹ online, mở modal tất toán
                  <DropdownMenuItem onClick={() => handleSettlementOption('silver')}>
                    <div className="flex items-center w-full cursor-pointer">
                      <Check className="mr-2 h-3.5 w-3.5 text-green-500" />
                      <span className="flex-1 text-sm text-green-500">Tất toán</span>
                    </div>
                  </DropdownMenuItem>
                )}
              </>
            )}
          </DropdownMenuContent>
        </DropdownMenu>
      </div>
    </>
  )
}

// Status selector for changing order status
interface OrderStatusSelectorProps {
  status: OrderStatus;
  onStatusChange: (status: OrderStatus) => void;
  disabled?: boolean;
}

export function OrderStatusSelector({ status, onStatusChange, disabled = false }: OrderStatusSelectorProps) {
  const [open, setOpen] = useState<boolean>(false);

  // Tất cả các trạng thái có thể có
  const statusOptions = [
    { value: OrderStatus.COMPLETED as string, label: 'Đã tất toán', color: 'bg-green-100 text-green-800 font-normal' },
    { value: OrderStatus.TERMINATED as string, label: 'Đã cắt hợp đồng', color: 'bg-red-100 text-red-800 font-normal' },
    { value: OrderStatus.DEPOSITED as string, label: 'Đã ký quỹ', color: 'bg-purple-100 text-purple-800 font-normal' },
  ];

  const currentStatus = statusOptions.find((option) => option.value === status) || statusOptions[0];

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <Button
          variant="ghost"
          role="combobox"
          aria-expanded={open}
          disabled={disabled}
          className="justify-between h-auto p-1 hover:bg-transparent"
        >
          <Badge className={currentStatus.color}>
            {currentStatus.label}
          </Badge>
          <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-[200px] p-0">
        <Command>
          <CommandInput placeholder="Tìm trạng thái..." />
          <CommandEmpty>Không tìm thấy trạng thái</CommandEmpty>
          <CommandGroup>
            {statusOptions.map((option) => (
              <CommandItem
                key={option.value}
                value={option.value}
                onSelect={() => {
                  onStatusChange(option.value as OrderStatus);
                  setOpen(false);
                }}
              >
                <Check
                  className={cn(
                    "mr-2 h-4 w-4",
                    status === option.value ? "opacity-100" : "opacity-0"
                  )}
                />
                <Badge className={option.color}>
                  {option.label}
                </Badge>
              </CommandItem>
            ))}
          </CommandGroup>
        </Command>
      </PopoverContent>
    </Popover>
  );
}

// Price editor for changing order price
interface PriceSelectorProps {
  price: number;
  onPriceChange: (price: number) => void;
  disabled?: boolean;
  buttonText?: string;
}

export function PriceSelector({
  price,
  onPriceChange,
  disabled = false,
  buttonText = 'Áp dụng'
}: PriceSelectorProps) {
  const id = useId();
  const [open, setOpen] = useState<boolean>(false);
  const [inputValue, setInputValue] = useState<string>(price?.toString() || '');

  // Xử lý khi người dùng nhấn nút Áp dụng
  const handleApply = () => {
    const numValue = Number(inputValue);
    if (!isNaN(numValue) && numValue > 0) {
      onPriceChange(numValue);
    }
    setOpen(false);
  };

  // Xử lý khi đóng popover mà không áp dụng
  const handleOpenChange = (isOpen: boolean) => {
    if (isOpen) {
      // Khi mở popover, cập nhật lại giá trị input
      setInputValue(price?.toString() || '');
    }
    setOpen(isOpen);
  };

  // Hiển thị giá theo format
  const displayPrice = () => {
    if (!price) return "---";
    return formatCurrency(price);
  };

  return (
    <Popover open={open} onOpenChange={handleOpenChange}>
      <PopoverTrigger asChild>
        <Button
          id={id}
          className="p-0 h-auto"
          variant="ghost"
          role="combobox"
          aria-expanded={open}
          disabled={disabled}
        >
          <div className="flex items-center gap-1 text-left">
            <span className="font-medium truncate">{displayPrice()}</span>
          </div>
        </Button>
      </PopoverTrigger>
      <PopoverContent className="p-4 w-64" align="start">
        <div className="flex flex-col space-y-4">
          <div className="space-y-2">
            <Label htmlFor={`price-input-${id}`}>Giá</Label>
            <div className="relative">
              <IconCurrencyDong className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                id={`price-input-${id}`}
                value={inputValue}
                onChange={(e) => setInputValue(e.target.value)}
                placeholder="Nhập giá mới"
                type="number"
                min="0"
                className="pl-8"
              />
            </div>
          </div>

          <Button
            onClick={handleApply}
            className="w-full"
            size="sm"
          >
            {buttonText}
          </Button>
        </div>
      </PopoverContent>
    </Popover>
  );
}

// Volume selector for changing order volume
interface VolumeSelectorProps {
  volume: number;
  onVolumeChange: (volume: number) => void;
  disabled?: boolean;
  buttonText?: string;
}

export function VolumeSelector({
  volume,
  onVolumeChange,
  disabled = false,
  buttonText = 'Áp dụng'
}: VolumeSelectorProps) {
  const id = useId();
  const [open, setOpen] = useState<boolean>(false);
  const [inputValue, setInputValue] = useState<string>(volume?.toString() || '');

  // Xử lý khi người dùng nhấn nút Áp dụng
  const handleApply = () => {
    const numValue = Number(inputValue);
    if (!isNaN(numValue) && numValue > 0) {
      onVolumeChange(numValue);
    }
    setOpen(false);
  };

  // Xử lý khi đóng popover mà không áp dụng
  const handleOpenChange = (isOpen: boolean) => {
    if (isOpen) {
      // Khi mở popover, cập nhật lại giá trị input
      setInputValue(volume?.toString() || '');
    }
    setOpen(isOpen);
  };

  // Hiển thị khối lượng theo format
  const displayVolume = () => {
    if (volume === undefined || volume === null) return "---";
    return `${volume.toLocaleString()}`;
  };

  return (
    <Popover open={open} onOpenChange={handleOpenChange}>
      <PopoverTrigger asChild>
        <Button
          id={id}
          className="p-0 h-auto"
          variant="ghost"
          role="combobox"
          aria-expanded={open}
          disabled={disabled}
        >
          <div className="flex items-center gap-1 text-left">
            <span className="font-medium truncate">{displayVolume()}</span>
          </div>
        </Button>
      </PopoverTrigger>
      <PopoverContent className="p-4 w-64" align="start">
        <div className="flex flex-col space-y-4">
          <div className="space-y-2">
            <Label htmlFor={`volume-input-${id}`}>Số lượng</Label>
            <Input
              id={`volume-input-${id}`}
              value={inputValue}
              onChange={(e) => setInputValue(e.target.value)}
              placeholder="Nhập số lượng mới"
              type="number"
              min="0"
              step="1"
            />
          </div>

          <Button
            onClick={handleApply}
            className="w-full"
            size="sm"
          >
            {buttonText}
          </Button>
        </div>
      </PopoverContent>
    </Popover>
  );
}

interface OrderBookColumnsProps {
  onViewDetail: (order: OrderBook) => void
  onExtendSettlement?: (order: OrderBook) => void
  onSettlement?: (order: OrderBook, option: 'silver' | 'deposit' | 'cancel') => void
  currentQuote?: ForexQuote | null
}

export function getOrderBookColumns({
  onViewDetail,
  onExtendSettlement,
  onSettlement,
  currentQuote
}: OrderBookColumnsProps): ColumnDef<OrderBook>[] {
  return [
    {
      accessorKey: "contractNumber",
      header: ({ column }) => {
        return (
          <Button
            variant="ghost"
            size="sm"
            onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
            className="flex items-center gap-1 px-0 h-6 py-0"
            data-column-id="contractNumber"
          >
            <span className="text-xs">Số hợp đồng</span>
            <ArrowUpDown className="ml-1 h-3 w-3" />
          </Button>
        )
      },
      meta: {
        header: "Số hợp đồng",
        isSticky: true,
        position: 'left',
      },
      cell: ({ row }) => {
        const contractNumber = row.original.contractNumber;
        return (
          <div className="text-sm">
            {contractNumber || "---"}
          </div>
        )
      },
      enableSorting: true,
    },
    {
      accessorKey: "orderType",
      header: ({ column }) => {
        return (
          <Button
            variant="ghost"
            size="sm"
            onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
            className="flex items-center gap-1 px-0 h-6 py-0"
            data-column-id="orderType"
          >
            <span className="text-xs">Loại lệnh</span>
            <ArrowUpDown className="ml-1 h-3 w-3" />
          </Button>
        )
      },
      meta: {
        header: "Loại lệnh"
      },
      cell: ({ row }) => {
        const type = row.getValue("orderType") as OrderType
        return (
          <Badge className={getOrderTypeColor(type)}>
            {getOrderTypeName(type)}
          </Badge>
        )
      },
      enableSorting: true,
    },
    {
      accessorKey: "businessType",
      header: ({ column }) => {
        return (
          <Button
            variant="ghost"
            size="sm"
            onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
            className="flex items-center gap-1 px-0 h-6 py-0"
            data-column-id="businessType"
          >
            <span className="text-xs">Loại hình giao dịch</span>
            <ArrowUpDown className="ml-1 h-3 w-3" />
          </Button>
        )
      },
      meta: {
        header: "Loại hình giao dịch"
      },
      cell: ({ row }) => {
        const businessType = row.getValue("businessType") as BusinessType | undefined

        if (!businessType || row.original.orderType !== OrderType.BUY) {
          return <span className="text-xs text-muted-foreground">---</span>
        }

        return (
          <Badge className={getBusinessTypeColor(businessType)}>
            {businessType === BusinessType.NORMAL ? "Bạc online ký quỹ" : "Bạc giao ngay"}
          </Badge>
        )
      },
      enableSorting: true,
    },
    {
      accessorKey: "totalPrice",
      header: ({ column }) => {
        return (
          <Button
            variant="ghost"
            size="sm"
            onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
            className="flex items-center gap-1 px-0 h-6 py-0"
            data-column-id="totalPrice"
          >
            <span className="text-xs">Tổng giá trị</span>
            <ArrowUpDown className="ml-1 h-3 w-3" />
          </Button>
        )
      },
      meta: {
        header: "Tổng giá trị"
      },
      cell: ({ row }) => {
        const order = row.original;
        const totalPrice = calculateRealTimePrice(order, currentQuote || null);

        return (
          <div className="flex items-center gap-1 text-sm">
            <span className={getPriceColor(order)}>
              {formatCurrency(totalPrice)} VND
            </span>
          </div>
        )
      },
      enableSorting: true,
    },
    {
      accessorKey: "processingPrice",
      header: ({ column }) => {
        return (
          <Button
            variant="ghost"
            size="sm"
            onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
            className="flex items-center gap-1 px-0 h-6 py-0"
            data-column-id="processingPrice"
          >
            <span className="text-xs">Phí gia công</span>
            <ArrowUpDown className="ml-1 h-3 w-3" />
          </Button>
        )
      },
      meta: {
        header: "Phí gia công"
      },
      cell: ({ row }) => {
        const order = row.original;
        const processingPrice = order.processingPrice;

        return (
          <div className="flex items-center gap-1 text-sm">
            <span>
              {processingPrice ? formatCurrency(processingPrice) : "0"} VND
            </span>
          </div>
        )
      },
      enableSorting: true,
    },
    {
      id: "totalVolume",
      header: () => {
        return (
          <Button
            variant="ghost"
            size="sm"
            className="flex items-center gap-1 px-0 h-6 py-0"
            data-column-id="totalVolume"
          >
            <span className="text-xs">Tổng khối lượng</span>
          </Button>
        )
      },
      meta: {
        header: "Tổng khối lượng"
      },
      cell: ({ row }) => {
        const order = row.original;
        // Tính tổng khối lượng bằng cách nhân số lượng với oz của token
        const totalVolume = order.details?.reduce((sum, detail) => {
          const volume = detail.quantity || 0;
          const weight = detail.product?.weight || 1; // Lấy oz từ token, mặc định là 1 nếu không có
          return sum + (volume * weight);
        }, 0) || 0;
        return (
          <div className="text-sm">
            {totalVolume.toLocaleString()}
          </div>
        )
      },
      enableSorting: false,
    },
    {
      accessorKey: "depositPrice",
      header: ({ column }) => {
        return (
          <Button
            variant="ghost"
            size="sm"
            onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
            className="flex items-center gap-1 px-0 h-6 py-0"
            data-column-id="depositPrice"
          >
            <span className="text-xs">Tiền ký quỹ</span>
            <ArrowUpDown className="ml-1 h-3 w-3" />
          </Button>
        )
      },
      meta: {
        header: "Tiền ký quỹ"
      },
      cell: ({ row }) => {
        const depositPrice = row.getValue("depositPrice") as number | undefined

        return (
          <div className="flex items-center gap-1 text-sm">
            <span>{formatCurrency(depositPrice)} VND</span>
          </div>
        )
      },
      enableSorting: true,
    },
    {
      accessorKey: "settlementPrice",
      header: ({ column }) => {
        return (
          <Button
            variant="ghost"
            size="sm"
            onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
            className="flex items-center gap-1 px-0 h-6 py-0"
            data-column-id="settlementPrice"
          >
            <span className="text-xs">Tiền tất toán</span>
            <ArrowUpDown className="ml-1 h-3 w-3" />
          </Button>
        )
      },
      meta: {
        header: "Tiền tất toán"
      },
      cell: ({ row }) => {
        const order = row.original;
        const settlementPrice = calculateRealTimeSettlement(order, currentQuote || null);

        return (
          <div className="flex items-center gap-1 text-sm">
            <span className={getPriceColor(order)}>
              {formatCurrency(settlementPrice)} VND
            </span>
          </div>
        )
      },
      enableSorting: true,
    },
    {
      accessorKey: "storageFee",
      header: ({ column }) => {
        return (
          <Button
            variant="ghost"
            size="sm"
            onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
            className="flex items-center gap-1 px-0 h-6 py-0"
            data-column-id="storageFee"
          >
            <span className="text-xs">Phí lưu kho</span>
            <ArrowUpDown className="ml-1 h-3 w-3" />
          </Button>
        )
      },
      meta: {
        header: "Phí lưu kho"
      },
      cell: ({ row }) => {
        const storageFee = row.getValue("storageFee") as number | undefined

        return (
          <div className="flex items-center gap-1 text-sm">
            <span>{formatCurrency(storageFee)} VND</span>
          </div>
        )
      },
      enableSorting: true,
    },
    {
      accessorKey: "settlementDeadline",
      header: ({ column }) => {
        return (
          <Button
            variant="ghost"
            size="sm"
            onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
            className="flex items-center gap-1 px-0 h-6 py-0"
            data-column-id="settlementDeadline"
          >
            <span className="text-xs">Hạn tất toán</span>
            <ArrowUpDown className="ml-1 h-3 w-3" />
          </Button>
        )
      },
      meta: {
        header: "Hạn tất toán"
      },
      cell: ({ row }) => {
        const date = row.original
        return (
          <DateTimeDisplay
            date={date.settlementDeadline}
            className="text-sm"
            timeClassName="text-xs text-muted-foreground"
          />
        )
      },
      enableSorting: true,
      size: 150,
    },
    {
      accessorKey: "settlementAt",
      header: ({ column }) => {
        return (
          <Button
            variant="ghost"
            size="sm"
            onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
            className="flex items-center gap-1 px-0 h-6 py-0"
            data-column-id="settlementAt"
          >
            <span className="text-xs">Ngày tất toán</span>
            <ArrowUpDown className="ml-1 h-3 w-3" />
          </Button>
        )
      },
      meta: {
        header: "Ngày tất toán"
      },
      cell: ({ row }) => {
        const date = row.original
        return (
          <DateTimeDisplay
            date={date.settlementAt}
            className="text-sm"
            timeClassName="text-xs text-muted-foreground"
          />
        )
      },
      enableSorting: true,
      size: 150,
    },
    {
      accessorKey: "approveStatus",
      header: ({ column }) => {
        return (
          <Button
            variant="ghost"
            size="sm"
            onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
            className="flex items-center gap-1 px-0 h-6 py-0"
            data-column-id="approveStatus"
          >
            <span className="text-xs">Phê duyệt</span>
            <ArrowUpDown className="ml-1 h-3 w-3" />
          </Button>
        )
      },
      meta: {
        header: "Phê duyệt"
      },
      cell: ({ row }) => {
        const approveStatus = row.original.approveStatus

        return (
          <Badge className={getApproveStatusColor(approveStatus)}>
            {getApproveStatusName(approveStatus)}
          </Badge>
        )
      },
      enableSorting: true,
      size: 120,
    },
    {
      accessorKey: "status",
      header: ({ column }) => {
        return (
          <Button
            variant="ghost"
            size="sm"
            onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
            className="flex items-center gap-1 px-0 h-6 py-0"
            data-column-id="status"
          >
            <span className="text-xs">Trạng thái</span>
            <ArrowUpDown className="ml-1 h-3 w-3" />
          </Button>
        )
      },
      meta: {
        header: "Trạng thái"
      },
      cell: ({ row }) => {
        const status = row.getValue("status") as OrderStatus

        return (
          <Badge className={getStatusColor(status)}>
            {getStatusName(status)}
          </Badge>
        )
      },
      enableSorting: true,
      size: 120,
    },
    {
      accessorKey: "createdAt",
      header: ({ column }) => {
        return (
          <Button
            variant="ghost"
            size="sm"
            onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
            className="flex items-center gap-1 px-0 h-6 py-0"
            data-column-id="createdAt"
          >
            <span className="text-xs">Ngày tạo</span>
            <ArrowUpDown className="ml-1 h-3 w-3" />
          </Button>
        )
      },
      meta: {
        header: "Ngày tạo"
      },
      cell: ({ row }) => {
        const date = row.original
        return (
          <DateTimeDisplay
            date={date.createdAt}
            className="text-sm"
            timeClassName="text-xs text-muted-foreground"
          />
        )
      },
      enableSorting: true,
      size: 150,
    },
    {
      id: 'actions',
      size: 40,
      enableHiding: false,
      header: () => <div data-column-id="actions"></div>,
      cell: ({ row }) => (
        <Actions
          row={row}
          onViewDetail={onViewDetail}
          onExtendSettlement={onExtendSettlement}
          onSettlement={onSettlement}
        />
      ),
      meta: {
        isSticky: true, // Đánh dấu cột này là cố định
        position: 'right', // Vị trí cố định (right hoặc left)
        header: "Thao tác"
      }
    }
  ]
}
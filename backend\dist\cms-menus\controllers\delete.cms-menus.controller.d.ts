import { DeleteCmsMenusService } from '../services/delete.cms-menus.service';
import { CmsMenuDto } from '../dto/cms-menu.dto';
export declare class DeleteCmsMenusController {
    private readonly cmsMenusService;
    constructor(cmsMenusService: DeleteCmsMenusService);
    softDelete(id: string, userId: string): Promise<CmsMenuDto | null>;
    restore(id: string, userId: string): Promise<CmsMenuDto | null>;
    forceDelete(id: string, userId: string): Promise<boolean>;
    bulkSoftDelete(ids: string[], userId: string): Promise<CmsMenuDto[]>;
}

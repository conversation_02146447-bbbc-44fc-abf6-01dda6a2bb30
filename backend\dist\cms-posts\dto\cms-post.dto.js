"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CmsPostDto = void 0;
const openapi = require("@nestjs/swagger");
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
const class_transformer_1 = require("class-transformer");
const user_dto_1 = require("../../users/dto/user.dto");
const cms_category_dto_1 = require("../../cms-categories/dto/cms-category.dto");
const cms_posts_entity_1 = require("../entity/cms-posts.entity");
class CmsPostDto {
    id;
    businessCode;
    postType;
    title;
    slug;
    excerpt;
    content;
    featuredImageUrl;
    status;
    publishedAt;
    eventStartDate;
    eventEndDate;
    eventLocation;
    viewCount;
    metaTitle;
    metaDescription;
    metaKeywords;
    allowComments;
    categoryId;
    category;
    authorId;
    author;
    createdAt;
    updatedAt;
    createdBy;
    updatedBy;
    deletedBy;
    isDeleted;
    deletedAt;
    creator;
    updater;
    deleter;
    static _OPENAPI_METADATA_FACTORY() {
        return { id: { required: true, type: () => String, format: "uuid" }, businessCode: { required: true, type: () => String }, postType: { required: true, enum: require("../entity/cms-posts.entity").CmsPostType }, title: { required: true, type: () => String }, slug: { required: true, type: () => String }, excerpt: { required: false, type: () => String }, content: { required: true, type: () => String }, featuredImageUrl: { required: false, type: () => String }, status: { required: true, enum: require("../entity/cms-posts.entity").CmsPostStatus }, publishedAt: { required: false, type: () => Date }, eventStartDate: { required: false, type: () => Date }, eventEndDate: { required: false, type: () => Date }, eventLocation: { required: false, type: () => String }, viewCount: { required: true, type: () => Number, minimum: 0 }, metaTitle: { required: false, type: () => String }, metaDescription: { required: false, type: () => String }, metaKeywords: { required: false, type: () => String }, allowComments: { required: true, type: () => Boolean }, categoryId: { required: false, type: () => String, format: "uuid" }, category: { required: false, type: () => require("../../cms-categories/dto/cms-category.dto").CmsCategoryDto }, authorId: { required: true, type: () => String, format: "uuid" }, author: { required: true, type: () => require("../../users/dto/user.dto").UserDto }, createdAt: { required: true, type: () => Date }, updatedAt: { required: true, type: () => Date }, createdBy: { required: false, type: () => String, format: "uuid" }, updatedBy: { required: false, type: () => String, format: "uuid" }, deletedBy: { required: false, type: () => String, format: "uuid" }, isDeleted: { required: true, type: () => Boolean }, deletedAt: { required: false, type: () => Date }, creator: { required: false, type: () => require("../../users/dto/user.dto").UserDto }, updater: { required: false, type: () => require("../../users/dto/user.dto").UserDto }, deleter: { required: false, type: () => require("../../users/dto/user.dto").UserDto } };
    }
}
exports.CmsPostDto = CmsPostDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'ID của bài viết',
        example: '550e8400-e29b-41d4-a716-************',
    }),
    (0, class_transformer_1.Expose)(),
    (0, class_validator_1.IsUUID)('4'),
    __metadata("design:type", String)
], CmsPostDto.prototype, "id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Mã kinh doanh của bài viết',
        example: 'CMP-240101-00001',
    }),
    (0, class_transformer_1.Expose)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CmsPostDto.prototype, "businessCode", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Loại bài viết',
        example: cms_posts_entity_1.CmsPostType.POST,
        enum: cms_posts_entity_1.CmsPostType,
    }),
    (0, class_transformer_1.Expose)(),
    (0, class_validator_1.IsEnum)(cms_posts_entity_1.CmsPostType),
    __metadata("design:type", String)
], CmsPostDto.prototype, "postType", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Tiêu đề bài viết',
        example: 'Tin tức mới nhất về thị trường vàng',
    }),
    (0, class_transformer_1.Expose)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CmsPostDto.prototype, "title", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Chuỗi URL thân thiện',
        example: 'tin-tuc-moi-nhat-ve-thi-truong-vang',
    }),
    (0, class_transformer_1.Expose)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CmsPostDto.prototype, "slug", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Đoạn tóm tắt ngắn',
        example: 'Thị trường vàng tuần này có nhiều biến động...',
    }),
    (0, class_transformer_1.Expose)(),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CmsPostDto.prototype, "excerpt", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Nội dung đầy đủ (HTML)',
        example: '<p>Nội dung chi tiết về thị trường vàng...</p>',
    }),
    (0, class_transformer_1.Expose)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CmsPostDto.prototype, "content", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'URL hình ảnh đại diện',
        example: 'https://example.com/images/featured.jpg',
    }),
    (0, class_transformer_1.Expose)(),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CmsPostDto.prototype, "featuredImageUrl", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Trạng thái bài viết',
        example: cms_posts_entity_1.CmsPostStatus.PUBLISHED,
        enum: cms_posts_entity_1.CmsPostStatus,
    }),
    (0, class_transformer_1.Expose)(),
    (0, class_validator_1.IsEnum)(cms_posts_entity_1.CmsPostStatus),
    __metadata("design:type", String)
], CmsPostDto.prototype, "status", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Thời gian xuất bản',
        example: '2023-01-01T00:00:00.000Z',
    }),
    (0, class_transformer_1.Expose)(),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsDate)(),
    __metadata("design:type", Date)
], CmsPostDto.prototype, "publishedAt", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Ngày bắt đầu sự kiện (cho post_type = event)',
        example: '2023-01-01T09:00:00.000Z',
    }),
    (0, class_transformer_1.Expose)(),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsDate)(),
    __metadata("design:type", Date)
], CmsPostDto.prototype, "eventStartDate", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Ngày kết thúc sự kiện (cho post_type = event)',
        example: '2023-01-01T17:00:00.000Z',
    }),
    (0, class_transformer_1.Expose)(),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsDate)(),
    __metadata("design:type", Date)
], CmsPostDto.prototype, "eventEndDate", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Địa điểm sự kiện (cho post_type = event)',
        example: 'Trung tâm Hội nghị Quốc gia',
    }),
    (0, class_transformer_1.Expose)(),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CmsPostDto.prototype, "eventLocation", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Số lượt xem',
        example: 100,
    }),
    (0, class_transformer_1.Expose)(),
    (0, class_validator_1.IsInt)(),
    (0, class_validator_1.Min)(0),
    __metadata("design:type", Number)
], CmsPostDto.prototype, "viewCount", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Tiêu đề SEO',
        example: 'Tin tức thị trường vàng - Cập nhật mới nhất',
    }),
    (0, class_transformer_1.Expose)(),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CmsPostDto.prototype, "metaTitle", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Mô tả SEO',
        example: 'Theo dõi tin tức và cập nhật mới nhất về thị trường vàng',
    }),
    (0, class_transformer_1.Expose)(),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CmsPostDto.prototype, "metaDescription", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Từ khóa SEO',
        example: 'thị trường vàng, tin tức, giá vàng',
    }),
    (0, class_transformer_1.Expose)(),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CmsPostDto.prototype, "metaKeywords", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Cho phép bình luận',
        example: true,
    }),
    (0, class_transformer_1.Expose)(),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], CmsPostDto.prototype, "allowComments", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'ID của chuyên mục',
        example: '550e8400-e29b-41d4-a716-************',
    }),
    (0, class_transformer_1.Expose)(),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsUUID)('4'),
    __metadata("design:type", String)
], CmsPostDto.prototype, "categoryId", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Thông tin chuyên mục',
        type: () => cms_category_dto_1.CmsCategoryDto,
    }),
    (0, class_transformer_1.Expose)(),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => cms_category_dto_1.CmsCategoryDto),
    __metadata("design:type", cms_category_dto_1.CmsCategoryDto)
], CmsPostDto.prototype, "category", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'ID của tác giả',
        example: '550e8400-e29b-41d4-a716-************',
    }),
    (0, class_transformer_1.Expose)(),
    (0, class_validator_1.IsUUID)('4'),
    __metadata("design:type", String)
], CmsPostDto.prototype, "authorId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Thông tin tác giả',
        type: () => user_dto_1.UserDto,
    }),
    (0, class_transformer_1.Expose)(),
    (0, class_transformer_1.Type)(() => user_dto_1.UserDto),
    __metadata("design:type", user_dto_1.UserDto)
], CmsPostDto.prototype, "author", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Thời gian tạo',
        example: '2023-01-01T00:00:00.000Z',
    }),
    (0, class_transformer_1.Expose)(),
    (0, class_validator_1.IsDate)(),
    __metadata("design:type", Date)
], CmsPostDto.prototype, "createdAt", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Thời gian cập nhật',
        example: '2023-01-01T00:00:00.000Z',
    }),
    (0, class_transformer_1.Expose)(),
    (0, class_validator_1.IsDate)(),
    __metadata("design:type", Date)
], CmsPostDto.prototype, "updatedAt", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'ID người tạo',
        example: '550e8400-e29b-41d4-a716-************',
    }),
    (0, class_transformer_1.Expose)(),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsUUID)('4'),
    __metadata("design:type", String)
], CmsPostDto.prototype, "createdBy", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'ID người cập nhật',
        example: '550e8400-e29b-41d4-a716-************',
    }),
    (0, class_transformer_1.Expose)(),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsUUID)('4'),
    __metadata("design:type", String)
], CmsPostDto.prototype, "updatedBy", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'ID người xóa',
        example: '550e8400-e29b-41d4-a716-************',
    }),
    (0, class_transformer_1.Expose)(),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsUUID)('4'),
    __metadata("design:type", String)
], CmsPostDto.prototype, "deletedBy", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Trạng thái xóa',
        example: false,
    }),
    (0, class_transformer_1.Expose)(),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], CmsPostDto.prototype, "isDeleted", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Thời gian xóa',
        example: '2023-01-01T00:00:00.000Z',
    }),
    (0, class_transformer_1.Expose)(),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsDate)(),
    __metadata("design:type", Date)
], CmsPostDto.prototype, "deletedAt", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Thông tin người tạo',
        type: () => user_dto_1.UserDto,
    }),
    (0, class_transformer_1.Expose)(),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => user_dto_1.UserDto),
    __metadata("design:type", user_dto_1.UserDto)
], CmsPostDto.prototype, "creator", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Thông tin người cập nhật',
        type: () => user_dto_1.UserDto,
    }),
    (0, class_transformer_1.Expose)(),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => user_dto_1.UserDto),
    __metadata("design:type", user_dto_1.UserDto)
], CmsPostDto.prototype, "updater", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Thông tin người xóa',
        type: () => user_dto_1.UserDto,
    }),
    (0, class_transformer_1.Expose)(),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => user_dto_1.UserDto),
    __metadata("design:type", user_dto_1.UserDto)
], CmsPostDto.prototype, "deleter", void 0);
//# sourceMappingURL=cms-post.dto.js.map
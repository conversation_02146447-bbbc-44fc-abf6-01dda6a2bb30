import { Repository } from 'typeorm';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { CmsPartners, CmsPartnerStatus, CmsPartnerType } from '../entity/cms-partners.entity';
import { BaseCmsPartnersService } from './base.cms-partners.service';
import { CmsPartnerDto } from '../dto';
import { CustomPaginationQueryDto } from 'src/common/dto/custom-pagination-query.dto';
export declare class ReadCmsPartnersService extends BaseCmsPartnersService {
    protected readonly partnerRepository: Repository<CmsPartners>;
    protected readonly eventEmitter: EventEmitter2;
    constructor(partnerRepository: Repository<CmsPartners>, eventEmitter: EventEmitter2);
    findAll(paginationQuery: CustomPaginationQueryDto, relations?: string[]): Promise<{
        data: CmsPartnerDto[];
        total: number;
    }>;
    findByStatus(status: CmsPartnerStatus, paginationQuery: CustomPaginationQueryDto, relations?: string[]): Promise<{
        data: CmsPartnerDto[];
        total: number;
    }>;
    findByType(type: CmsPartnerType, paginationQuery: CustomPaginationQueryDto, relations?: string[]): Promise<{
        data: CmsPartnerDto[];
        total: number;
    }>;
    getActivePartners(paginationQuery: CustomPaginationQueryDto, relations?: string[]): Promise<{
        data: CmsPartnerDto[];
        total: number;
    }>;
    getStatistics(): Promise<{
        total: number;
        active: number;
        inactive: number;
        byType: Record<CmsPartnerType, number>;
        withLogo: number;
        withWebsite: number;
        withCompleteInfo: number;
    }>;
    count(status?: CmsPartnerStatus): Promise<number>;
    countByType(type: CmsPartnerType): Promise<number>;
    search(searchTerm: string, paginationQuery: CustomPaginationQueryDto, relations?: string[]): Promise<{
        data: CmsPartnerDto[];
        total: number;
    }>;
    getDeleted(paginationQuery: CustomPaginationQueryDto, relations?: string[]): Promise<{
        data: CmsPartnerDto[];
        total: number;
    }>;
    getPartnerById(id: string, relations?: string[]): Promise<CmsPartnerDto>;
    getPartnerByName(name: string, relations?: string[]): Promise<CmsPartnerDto | null>;
}

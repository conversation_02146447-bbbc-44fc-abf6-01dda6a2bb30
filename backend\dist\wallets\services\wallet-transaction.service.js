"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.WalletTransactionService = exports.WalletTransactionStatus = exports.WalletTransactionType = void 0;
const common_1 = require("@nestjs/common");
var WalletTransactionType;
(function (WalletTransactionType) {
    WalletTransactionType["DEPOSIT"] = "DEPOSIT";
    WalletTransactionType["WITHDRAWAL"] = "WITHDRAWAL";
    WalletTransactionType["TRANSFER"] = "TRANSFER";
    WalletTransactionType["PAYMENT"] = "PAYMENT";
})(WalletTransactionType || (exports.WalletTransactionType = WalletTransactionType = {}));
var WalletTransactionStatus;
(function (WalletTransactionStatus) {
    WalletTransactionStatus["PENDING"] = "PENDING";
    WalletTransactionStatus["PROCESSING"] = "PROCESSING";
    WalletTransactionStatus["COMPLETED"] = "COMPLETED";
    WalletTransactionStatus["FAILED"] = "FAILED";
    WalletTransactionStatus["CANCELLED"] = "CANCELLED";
})(WalletTransactionStatus || (exports.WalletTransactionStatus = WalletTransactionStatus = {}));
let WalletTransactionService = class WalletTransactionService {
    transactions = new Map();
    nextId = 1;
    async create(data) {
        const transaction = {
            id: this.nextId++,
            ...data,
            createdAt: new Date(),
            updatedAt: new Date(),
        };
        this.transactions.set(transaction.id, transaction);
        return transaction;
    }
    async update(id, data) {
        const transaction = this.transactions.get(id);
        if (!transaction) {
            throw new Error(`Wallet transaction not found: ${id}`);
        }
        const updatedTransaction = {
            ...transaction,
            ...data,
            updatedAt: new Date(),
        };
        this.transactions.set(id, updatedTransaction);
        return updatedTransaction;
    }
    async findById(id) {
        return this.transactions.get(id) || null;
    }
    async findByUserId(userId) {
        return Array.from(this.transactions.values()).filter((transaction) => transaction.userId === userId);
    }
};
exports.WalletTransactionService = WalletTransactionService;
exports.WalletTransactionService = WalletTransactionService = __decorate([
    (0, common_1.Injectable)()
], WalletTransactionService);
//# sourceMappingURL=wallet-transaction.service.js.map
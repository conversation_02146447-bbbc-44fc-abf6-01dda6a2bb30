import { ConfigService } from '@nestjs/config';
import { HttpService } from '@nestjs/axios';
import { IPaymentGateway } from '../interfaces/payment-gateway.interface';
import { CreatePaymentDto } from '../dto/create-payment.dto';
export declare class MomoService implements IPaymentGateway {
    private readonly configService;
    private readonly httpService;
    private readonly logger;
    private readonly partnerCode;
    private readonly accessKey;
    private readonly secretKey;
    private readonly endpoint;
    private readonly redirectUrl;
    private readonly ipnUrl;
    constructor(configService: ConfigService, httpService: HttpService);
    createPaymentUrl(paymentData: CreatePaymentDto): Promise<{
        paymentUrl: string;
        transactionId: string;
    }>;
    verifyReturnUrl(params: Record<string, string>): Promise<{
        isValid: boolean;
        transactionId?: string;
        amount?: number;
        message?: string;
    }>;
    handleIpnCallback(params: Record<string, string>): Promise<{
        isValid: boolean;
        transactionId?: string;
        amount?: number;
        message?: string;
    }>;
}

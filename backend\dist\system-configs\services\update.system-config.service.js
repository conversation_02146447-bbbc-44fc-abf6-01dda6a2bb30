"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var UpdateSystemConfigService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.UpdateSystemConfigService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const event_emitter_1 = require("@nestjs/event-emitter");
const typeorm_transactional_1 = require("typeorm-transactional");
const system_config_entity_1 = require("../entities/system-config.entity");
const update_system_config_dto_1 = require("../dto/update-system-config.dto");
const base_system_config_service_1 = require("./base.system-config.service");
let UpdateSystemConfigService = UpdateSystemConfigService_1 = class UpdateSystemConfigService extends base_system_config_service_1.BaseSystemConfigService {
    systemConfigRepository;
    dataSource;
    eventEmitter;
    logger = new common_1.Logger(UpdateSystemConfigService_1.name);
    constructor(systemConfigRepository, dataSource, eventEmitter) {
        super(systemConfigRepository, dataSource, eventEmitter);
        this.systemConfigRepository = systemConfigRepository;
        this.dataSource = dataSource;
        this.eventEmitter = eventEmitter;
    }
    async update(id, updateSystemConfigDto) {
        try {
            this.logger.debug(`Cập nhật cấu hình với ID: ${id}`);
            const systemConfig = await this.findByIdOrFail(id);
            Object.assign(systemConfig, updateSystemConfigDto);
            systemConfig.updatedAt = new Date();
            const updatedConfig = await this.systemConfigRepository.save(systemConfig);
            try {
                this.eventEmitter.emit(this.EVENT_SYSTEM_CONFIG_UPDATED, updatedConfig);
            }
            catch (emitError) {
                this.logger.warn(`Không thể phát sự kiện cập nhật: ${emitError.message}`);
            }
            return this.toDto(updatedConfig);
        }
        catch (error) {
            this.logger.error(`Lỗi khi cập nhật cấu hình: ${error.message}`, error.stack);
            if (error instanceof common_1.NotFoundException) {
                throw error;
            }
            throw new common_1.InternalServerErrorException(`Không thể cập nhật cấu hình: ${error.message}`);
        }
    }
    async updateByKey(key, updateSystemConfigDto) {
        try {
            this.logger.debug(`Cập nhật cấu hình với khóa: ${key}`);
            const systemConfig = await this.findByKeyOrFail(key);
            Object.assign(systemConfig, updateSystemConfigDto);
            systemConfig.updatedAt = new Date();
            const updatedConfig = await this.systemConfigRepository.save(systemConfig);
            try {
                this.eventEmitter.emit(this.EVENT_SYSTEM_CONFIG_UPDATED, updatedConfig);
            }
            catch (emitError) {
                this.logger.warn(`Không thể phát sự kiện cập nhật: ${emitError.message}`);
            }
            return this.toDto(updatedConfig);
        }
        catch (error) {
            this.logger.error(`Lỗi khi cập nhật cấu hình: ${error.message}`, error.stack);
            if (error instanceof common_1.NotFoundException) {
                throw error;
            }
            throw new common_1.InternalServerErrorException(`Không thể cập nhật cấu hình: ${error.message}`);
        }
    }
    async updateBulk(items) {
        try {
            this.logger.debug(`Cập nhật ${items.length} cấu hình`);
            if (!items || items.length === 0) {
                throw new common_1.BadRequestException('Danh sách cấu hình không được để trống');
            }
            const configKeys = items.map(item => item.configKey);
            const existingConfigs = await this.systemConfigRepository.find({
                where: { configKey: (0, typeorm_2.In)(configKeys) },
            });
            const existingConfigKeys = existingConfigs.map(config => config.configKey);
            const missingKeys = configKeys.filter(key => !existingConfigKeys.includes(key));
            if (missingKeys.length > 0) {
                throw new common_1.NotFoundException(`Không tìm thấy cấu hình với các khóa: ${missingKeys.join(', ')}`);
            }
            const existingConfigMap = new Map();
            existingConfigs.forEach(config => {
                existingConfigMap.set(config.configKey, config);
            });
            const updatedConfigs = [];
            for (const item of items) {
                const config = existingConfigMap.get(item.configKey);
                Object.assign(config, item);
                config.updatedAt = new Date();
                const updatedConfig = await this.systemConfigRepository.save(config);
                updatedConfigs.push(updatedConfig);
                try {
                    this.eventEmitter.emit(this.EVENT_SYSTEM_CONFIG_UPDATED, updatedConfig);
                }
                catch (emitError) {
                    this.logger.warn(`Không thể phát sự kiện cập nhật cho cấu hình ${item.configKey}: ${emitError.message}`);
                }
            }
            return updatedConfigs.map(config => this.toDto(config));
        }
        catch (error) {
            this.logger.error(`Lỗi khi cập nhật hàng loạt cấu hình: ${error.message}`, error.stack);
            if (error instanceof common_1.NotFoundException || error instanceof common_1.BadRequestException) {
                throw error;
            }
            throw new common_1.InternalServerErrorException(`Không thể cập nhật hàng loạt cấu hình: ${error.message}`);
        }
    }
};
exports.UpdateSystemConfigService = UpdateSystemConfigService;
__decorate([
    (0, typeorm_transactional_1.Transactional)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, update_system_config_dto_1.UpdateSystemConfigDto]),
    __metadata("design:returntype", Promise)
], UpdateSystemConfigService.prototype, "update", null);
__decorate([
    (0, typeorm_transactional_1.Transactional)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, update_system_config_dto_1.UpdateSystemConfigDto]),
    __metadata("design:returntype", Promise)
], UpdateSystemConfigService.prototype, "updateByKey", null);
__decorate([
    (0, typeorm_transactional_1.Transactional)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Array]),
    __metadata("design:returntype", Promise)
], UpdateSystemConfigService.prototype, "updateBulk", null);
exports.UpdateSystemConfigService = UpdateSystemConfigService = UpdateSystemConfigService_1 = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(system_config_entity_1.SystemConfig)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        typeorm_2.DataSource,
        event_emitter_1.EventEmitter2])
], UpdateSystemConfigService);
//# sourceMappingURL=update.system-config.service.js.map
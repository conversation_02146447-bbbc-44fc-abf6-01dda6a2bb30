# VNPAY Service Documentation

## Tổng quan

VnpayService là service tích hợp cổng thanh toán VNPAY theo tài liệu chính thức của VNPAY. Service này hỗ trợ đầy đủ các chức năng:

- Tạo URL thanh toán
- <PERSON><PERSON><PERSON> thực Return URL và IPN callback
- T<PERSON>y vấn kết quả giao dịch (QueryDR)
- <PERSON><PERSON><PERSON> tiền (Refund)

## Cấu hình Environment Variables

Thêm các biến môi trường sau vào file `.env`:

```env
# VNPAY Configuration
VNPAY_TMN_CODE=your_tmn_code_here
VNPAY_HASH_SECRET=your_hash_secret_here
VNPAY_URL=https://sandbox.vnpayment.vn/paymentv2/vpcpay.html
VNPAY_API_URL=https://sandbox.vnpayment.vn/merchant_webapi/api/transaction
VNPAY_RETURN_URL=https://yourdomain.com/payment/vnpay/return
VNPAY_IPN_URL=https://yourdomain.com/payment/vnpay/ipn
```

## Sử dụng Service

### 1. Tạo URL thanh toán

```typescript
const paymentData: CreatePaymentDto = {
  userId: 'user-uuid',
  walletId: 'wallet-uuid',
  amount: 100000, // 100,000 VND
  gatewayType: PaymentGatewayType.VNPAY,
  description: 'Nap tien vao vi',
  ipAddress: '127.0.0.1',
  bankCode: 'VNPAYQR', // Tùy chọn
  locale: 'vn', // vn hoặc en
  orderType: 'other' // Mã danh mục hàng hóa
};

const result = await vnpayService.createPaymentUrl(paymentData);
// result: { paymentUrl: string, transactionId: string }
```

### 2. Xác thực Return URL

```typescript
// Trong controller xử lý return URL
const params = req.query; // Tham số từ VNPAY
const result = await vnpayService.verifyReturnUrl(params);

if (result.isValid && result.responseCode === '00') {
  // Thanh toán thành công
  console.log('Giao dịch thành công:', result.transactionId);
} else {
  // Thanh toán thất bại hoặc chữ ký không hợp lệ
  console.log('Lỗi:', result.message);
}
```

### 3. Xử lý IPN Callback

```typescript
// Trong controller xử lý IPN
const params = req.query; // Tham số từ VNPAY

// Xác thực và xử lý
const result = await vnpayService.handleIpnCallback(params);

// Tạo response cho VNPAY
const ipnResponse = await vnpayService.createIpnResponse(params);
res.json(ipnResponse); // { RspCode: '00', Message: 'Confirm Success' }
```

### 4. Truy vấn giao dịch

```typescript
const queryResult = await vnpayService.queryTransaction({
  txnRef: 'order_id',
  transactionDate: '**************', // yyyyMMddHHmmss
  orderInfo: 'Truy van giao dich',
  ipAddr: '127.0.0.1',
  transactionNo: 'vnpay_transaction_no' // Tùy chọn
});
```

### 5. Hoàn tiền

```typescript
const refundResult = await vnpayService.refundTransaction({
  txnRef: 'order_id',
  amount: 50000, // Số tiền hoàn (VND)
  orderInfo: 'Hoan tien don hang',
  transactionDate: '**************',
  transactionType: '02', // '02': toàn phần, '03': một phần
  createBy: 'admin_user',
  ipAddr: '127.0.0.1',
  transactionNo: 'vnpay_transaction_no' // Tùy chọn
});
```

## Mã lỗi VNPAY

### Response Code (vnp_ResponseCode)

- `00`: Giao dịch thành công
- `07`: Trừ tiền thành công. Giao dịch bị nghi ngờ gian lận
- `09`: Thẻ/Tài khoản chưa đăng ký InternetBanking
- `10`: Xác thực thông tin sai quá 3 lần
- `11`: Hết hạn chờ thanh toán
- `12`: Thẻ/Tài khoản bị khóa
- `13`: Sai mật khẩu OTP
- `24`: Khách hàng hủy giao dịch
- `51`: Tài khoản không đủ số dư
- `65`: Vượt quá hạn mức giao dịch trong ngày
- `75`: Ngân hàng đang bảo trì
- `79`: Nhập sai mật khẩu thanh toán quá số lần quy định
- `99`: Các lỗi khác

### Transaction Status (vnp_TransactionStatus)

- `00`: Giao dịch thành công
- `01`: Giao dịch chưa hoàn tất
- `02`: Giao dịch bị lỗi
- `04`: Giao dịch đảo
- `05`: VNPAY đang xử lý giao dịch hoàn tiền
- `06`: VNPAY đã gửi yêu cầu hoàn tiền sang Ngân hàng
- `07`: Giao dịch bị nghi ngờ gian lận
- `09`: Giao dịch hoàn trả bị từ chối

## Lưu ý quan trọng

1. **Bảo mật**: Không bao giờ expose `VNPAY_HASH_SECRET` ra client
2. **Số tiền**: VNPAY yêu cầu nhân số tiền với 100 (đã xử lý trong service)
3. **Thời gian**: Sử dụng múi giờ GMT+7 (đã xử lý trong service)
4. **Encoding**: Thông tin đơn hàng phải là tiếng Việt không dấu (đã xử lý trong service)
5. **IPN**: Phải trả về JSON response cho VNPAY theo đúng format
6. **SSL**: IPN URL bắt buộc phải có SSL (HTTPS)

## Testing

Sử dụng môi trường Sandbox của VNPAY để test:
- URL: https://sandbox.vnpayment.vn/paymentv2/vpcpay.html
- API: https://sandbox.vnpayment.vn/merchant_webapi/api/transaction
- Merchant View: https://doitac-test.vnpaytest.vn/mcview/login

## Dependencies

Service này yêu cầu:
- `@nestjs/axios` cho HTTP requests
- `rxjs` cho reactive programming
- `crypto` (built-in Node.js) cho HMAC SHA512

## Tài liệu tham khảo

- [VNPAY API Documentation](https://sandbox.vnpayment.vn/apis/docs/thanh-toan-pay/pay.html)
- [Query & Refund API](https://sandbox.vnpayment.vn/apis/docs/truy-van-hoan-tien/querydr&refund.html)

import { Logger } from '@nestjs/common';
import { Repository, DataSource, FindOptionsWhere } from 'typeorm';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { CmsTags } from '../entity/cms-tags.entity';
import { CmsTagDto } from '../dto/cms-tag.dto';
export declare class BaseCmsTagsService {
    protected readonly tagRepository: Repository<CmsTags>;
    protected readonly dataSource: DataSource;
    protected readonly eventEmitter: EventEmitter2;
    protected readonly logger: Logger;
    protected readonly EVENT_TAG_CREATED = "cms-tag.created";
    protected readonly EVENT_TAG_UPDATED = "cms-tag.updated";
    protected readonly EVENT_TAG_DELETED = "cms-tag.deleted";
    protected readonly validRelations: string[];
    constructor(tagRepository: Repository<CmsTags>, dataSource: DataSource, eventEmitter: EventEmitter2);
    protected validateRelations(relations: string[]): string[];
    protected buildWhereClause(filter?: string): FindOptionsWhere<CmsTags> | FindOptionsWhere<CmsTags>[];
    protected findById(id: string, relations?: string[], throwError?: boolean): Promise<CmsTags | null>;
    protected findBySlug(slug: string, throwError?: boolean): Promise<CmsTags | null>;
    protected findByName(name: string, throwError?: boolean): Promise<CmsTags | null>;
    protected toDto(tag: CmsTags | null): CmsTagDto | null;
    protected toDtos(tags: CmsTags[]): CmsTagDto[];
    protected generateSlugFromName(name: string): string;
    protected isSlugUnique(slug: string, excludeId?: string): Promise<boolean>;
    protected isNameUnique(name: string, excludeId?: string): Promise<boolean>;
}

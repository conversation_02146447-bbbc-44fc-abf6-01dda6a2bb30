import { UserDto } from '../../users/dto/user.dto';
export declare class BankDto {
    id: string;
    brandName: string;
    fullName: string;
    shortName: string;
    code: string;
    bin: string;
    logoPath?: string;
    icon?: string;
    isActive: boolean;
    createdAt: Date;
    updatedAt: Date;
    createdBy?: string;
    updatedBy?: string;
    isDeleted: boolean;
    deletedBy?: string;
    deletedAt?: Date;
    creator?: UserDto;
    updater?: UserDto;
    deleter?: UserDto;
}

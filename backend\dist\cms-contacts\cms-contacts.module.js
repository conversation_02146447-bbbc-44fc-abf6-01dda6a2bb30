"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CmsContactsModule = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const cms_contacts_entity_1 = require("./entity/cms-contacts.entity");
const user_entity_1 = require("../users/entities/user.entity");
const base_cms_contacts_service_1 = require("./services/base.cms-contacts.service");
const create_cms_contacts_service_1 = require("./services/create.cms-contacts.service");
const read_cms_contacts_service_1 = require("./services/read.cms-contacts.service");
const update_cms_contacts_service_1 = require("./services/update.cms-contacts.service");
const delete_cms_contacts_service_1 = require("./services/delete.cms-contacts.service");
const controllers_1 = require("./controllers");
let CmsContactsModule = class CmsContactsModule {
};
exports.CmsContactsModule = CmsContactsModule;
exports.CmsContactsModule = CmsContactsModule = __decorate([
    (0, common_1.Module)({
        imports: [
            typeorm_1.TypeOrmModule.forFeature([cms_contacts_entity_1.CmsContacts, user_entity_1.User]),
        ],
        controllers: [
            controllers_1.CreateCmsContactsController,
            controllers_1.ReadCmsContactsController,
            controllers_1.UpdateCmsContactsController,
            controllers_1.DeleteCmsContactsController,
        ],
        providers: [
            base_cms_contacts_service_1.BaseCmsContactsService,
            create_cms_contacts_service_1.CreateCmsContactsService,
            read_cms_contacts_service_1.ReadCmsContactsService,
            update_cms_contacts_service_1.UpdateCmsContactsService,
            delete_cms_contacts_service_1.DeleteCmsContactsService,
        ],
        exports: [
            typeorm_1.TypeOrmModule,
            base_cms_contacts_service_1.BaseCmsContactsService,
            create_cms_contacts_service_1.CreateCmsContactsService,
            read_cms_contacts_service_1.ReadCmsContactsService,
            update_cms_contacts_service_1.UpdateCmsContactsService,
            delete_cms_contacts_service_1.DeleteCmsContactsService,
        ],
    })
], CmsContactsModule);
//# sourceMappingURL=cms-contacts.module.js.map
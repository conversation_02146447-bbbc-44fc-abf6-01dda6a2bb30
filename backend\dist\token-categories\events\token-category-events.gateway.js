"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var TokenCategoryEventsGateway_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.TokenCategoryEventsGateway = void 0;
const websockets_1 = require("@nestjs/websockets");
const socket_io_1 = require("socket.io");
const common_1 = require("@nestjs/common");
let TokenCategoryEventsGateway = TokenCategoryEventsGateway_1 = class TokenCategoryEventsGateway {
    server;
    logger = new common_1.Logger(TokenCategoryEventsGateway_1.name);
    handleConnection(client, ...args) {
        const clientId = client.id;
        this.logger.log(`Client connected: ${clientId}`);
    }
    handleDisconnect(client) {
        this.logger.log(`Client disconnected: ${client.id}`);
    }
    emitEntityCreated(dto) {
        const event = `token_category_created`;
        this.logger.verbose(`Emitting event [${event}]`, dto);
        this.server.emit(event, dto);
    }
    emitEntityUpdated(dto) {
        const event = `token_category_updated`;
        const roomId = `token_category_${dto.id}`;
        this.logger.verbose(`Emitting event [${event}] to room [${roomId}]`, dto);
        this.server.to(roomId).emit(event, dto);
        this.server.emit(event, dto);
    }
    emitStatusToggled(id, isActive) {
        const event = `token_category_status_toggled`;
        const payload = { id, isActive };
        const roomId = `token_category_${id}`;
        this.logger.verbose(`Emitting event [${event}] to room [${roomId}]`, payload);
        this.server.to(roomId).emit(event, payload);
        this.server.emit(event, payload);
    }
    emitEntityDuplicated(dto) {
        this.emitEntityCreated(dto);
    }
    emitEntityDeleted(id, isSoftDelete) {
        const event = isSoftDelete
            ? `token_category_soft_deleted`
            : `token_category_deleted`;
        const payload = { id };
        const roomId = `token_category_${id}`;
        this.logger.verbose(`Emitting event [${event}] to room [${roomId}]`, payload);
        this.server.to(roomId).emit(event, payload);
        this.server.emit(event, payload);
        this.server.in(roomId).socketsLeave(roomId);
        this.logger.verbose(`Forced clients to leave room [${roomId}]`);
    }
    handleSubscribe(entityId, client) {
        const roomId = `token_category_${entityId}`;
        client.join(roomId);
        this.logger.log(`Client ${client.id} subscribed to room [${roomId}]`);
        client.emit('subscribed', { room: roomId });
    }
    handleUnsubscribe(entityId, client) {
        const roomId = `token_category_${entityId}`;
        client.leave(roomId);
        this.logger.log(`Client ${client.id} unsubscribed from room [${roomId}]`);
        client.emit('unsubscribed', { room: roomId });
    }
};
exports.TokenCategoryEventsGateway = TokenCategoryEventsGateway;
__decorate([
    (0, websockets_1.WebSocketServer)(),
    __metadata("design:type", socket_io_1.Server)
], TokenCategoryEventsGateway.prototype, "server", void 0);
__decorate([
    (0, websockets_1.SubscribeMessage)('subscribe_to_token_category'),
    __param(0, (0, websockets_1.MessageBody)()),
    __param(1, (0, websockets_1.ConnectedSocket)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, socket_io_1.Socket]),
    __metadata("design:returntype", void 0)
], TokenCategoryEventsGateway.prototype, "handleSubscribe", null);
__decorate([
    (0, websockets_1.SubscribeMessage)('unsubscribe_from_token_category'),
    __param(0, (0, websockets_1.MessageBody)()),
    __param(1, (0, websockets_1.ConnectedSocket)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, socket_io_1.Socket]),
    __metadata("design:returntype", void 0)
], TokenCategoryEventsGateway.prototype, "handleUnsubscribe", null);
exports.TokenCategoryEventsGateway = TokenCategoryEventsGateway = TokenCategoryEventsGateway_1 = __decorate([
    (0, websockets_1.WebSocketGateway)({
        cors: { origin: '*' },
    })
], TokenCategoryEventsGateway);
//# sourceMappingURL=token-category-events.gateway.js.map
import { CreatePaymentDto } from '../dto/create-payment.dto';
export interface IPaymentGateway {
    createPaymentUrl(paymentData: CreatePaymentDto): Promise<{
        paymentUrl: string;
        transactionId: string;
    }>;
    verifyReturnUrl(params: Record<string, string>): Promise<{
        isValid: boolean;
        transactionId?: string;
        amount?: number;
        message?: string;
    }>;
    handleIpnCallback(params: Record<string, string>): Promise<{
        isValid: boolean;
        transactionId?: string;
        amount?: number;
        message?: string;
    }>;
}

import { User } from '../../users/entities/user.entity';
import { CreateActivityLogService } from '../services/create.activity-log.service';
import { ActivityLogDto } from '../dto/activity-log.dto';
import { CreateActivityLogDto } from '../dto/create-activity-log.dto';
export declare class ActivityLogCreateController {
    private readonly activityLogService;
    private readonly logger;
    constructor(activityLogService: CreateActivityLogService);
    create(createActivityLogDto: CreateActivityLogDto, userId: string): Promise<ActivityLogDto>;
    bulkCreate(createActivityLogDtos: CreateActivityLogDto[], userId: string): Promise<ActivityLogDto[]>;
    logActivity(createActivityLogDto: CreateActivityLogDto, user: User): Promise<ActivityLogDto>;
}

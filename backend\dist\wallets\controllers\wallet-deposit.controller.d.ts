import { WalletVnpayService, DepositResponse } from '../services/wallet-vnpay.service';
export declare class CreateDepositDto {
    amount: number;
    description?: string;
    bankCode?: string;
}
export declare class WalletDepositController {
    private readonly walletVnpayService;
    private readonly logger;
    constructor(walletVnpayService: WalletVnpayService);
    createVnpayDeposit(createDepositDto: CreateDepositDto, req: any, clientIp: string): Promise<DepositResponse>;
    getVnpayDepositStatus(merchantTxnRef: string, req: any): Promise<any>;
    getVnpayBanks(): Promise<any[]>;
}

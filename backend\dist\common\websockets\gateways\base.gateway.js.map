{"version": 3, "file": "base.gateway.js", "sourceRoot": "", "sources": ["../../../../src/common/websockets/gateways/base.gateway.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;AACA,mDAU4B;AAC5B,yCAA2C;AAC3C,2CAMwB;AACxB,2CAA+C;AAC/C,qCAAyC;AACzC,yDAAoD;AACpD,wEAAmE;AAEnE,qFAAgF;AAsBzE,IAAM,WAAW,mBAAjB,MAAM,WAAW;IAUH;IACA;IACA;IARnB,MAAM,CAAS;IAEE,MAAM,GAAG,IAAI,eAAM,CAAC,aAAW,CAAC,IAAI,CAAC,CAAC;IAGvD,YACmB,aAA4B,EAC5B,gBAAyC,EACzC,UAAsB;QAFtB,kBAAa,GAAb,aAAa,CAAe;QAC5B,qBAAgB,GAAhB,gBAAgB,CAAyB;QACzC,eAAU,GAAV,UAAU,CAAY;IACtC,CAAC;IAGJ,SAAS,CAAC,MAAc;QAEtB,IAAI,CAAC,gBAAgB,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;IAC1C,CAAC;IAGD,KAAK,CAAC,gBAAgB,CAAC,MAAc,EAAE,GAAG,IAAW;QACnD,IAAI,CAAC,MAAM,CAAC,GAAG,CACb,qBAAqB,MAAM,CAAC,EAAE,aAAa,MAAM,CAAC,SAAS,CAAC,OAAO,EAAE,CACtE,CAAC;QAGF,IAAI,CAAC;YAEH,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,wBAAwB,IAAI,CAAC,SAAS,CAAC;gBACrD,IAAI,EAAE,MAAM,CAAC,SAAS,EAAE,IAAI;gBAC5B,OAAO,EAAE,MAAM,CAAC,SAAS,EAAE,OAAO;gBAClC,KAAK,EAAE,MAAM,CAAC,SAAS,EAAE,KAAK;aAC/B,CAAC,EAAE,CAAC,CAAC;YAGN,MAAM,KAAK,GAAG,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC;YAExC,IAAI,CAAC,KAAK,EAAE,CAAC;gBACX,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,UAAU,MAAM,CAAC,EAAE,sBAAsB,CAAC,CAAC;gBAE5D,MAAM,CAAC,IAAI,CAAC,OAAO,EAAE,EAAE,OAAO,EAAE,kCAAkC,EAAE,IAAI,EAAE,GAAG,EAAE,CAAC,CAAC;gBACjF,MAAM,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;gBACxB,OAAO;YACT,CAAC;YAED,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,UAAU,MAAM,CAAC,EAAE,6BAA6B,CAAC,CAAC;YAGlE,MAAM,SAAS,GAAG,IAAI,CAAC,aAAa,CAAC,GAAG,CAAS,YAAY,CAAC,CAAC;YAC/D,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,sBAAsB,CAAC,CAAC,SAAS,EAAE,CAAC,CAAC;YAGrD,MAAM,OAAO,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,KAAK,EAAE;gBAC5C,MAAM,EAAE,SAAS;aAClB,CAAC,CAAC;YACH,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,0CAA0C,MAAM,CAAC,EAAE,EAAE,CAAC,CAAC;YACvE,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,kBAAkB,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;YAGjE,MAAM,CAAC,IAAI,GAAG,MAAM,CAAC,IAAI,IAAI,EAAE,CAAC;YAChC,MAAM,CAAC,IAAI,CAAC,IAAI,GAAG,OAAO,CAAC;YAG3B,MAAM,QAAQ,GAAG,QAAQ,OAAO,CAAC,MAAM,EAAE,CAAC;YAC1C,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YAEtB,IAAI,CAAC,MAAM,CAAC,OAAO,CACjB,uCAAuC,MAAM,CAAC,EAAE,aAAa,OAAO,CAAC,MAAM,WAAW,QAAQ,EAAE,CACjG,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,IAAI,CACd,UAAU,MAAM,CAAC,EAAE,4BAA4B,KAAK,CAAC,OAAO,EAAE,CAC/D,CAAC;YAEF,MAAM,CAAC,IAAI,CAAC,OAAO,EAAE,EAAE,OAAO,EAAE,KAAK,CAAC,OAAO,IAAI,cAAc,EAAE,IAAI,EAAE,GAAG,EAAE,CAAC,CAAC;YAC9E,MAAM,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;QAC1B,CAAC;IACH,CAAC;IAGD,gBAAgB,CAAC,MAAc;QAC7B,MAAM,IAAI,GAAG,MAAM,CAAC,IAAI,CAAC,IAAmB,CAAC;QAC7C,IAAI,CAAC,MAAM,CAAC,GAAG,CACb,wBAAwB,MAAM,CAAC,EAAE,IAAI,IAAI,CAAC,CAAC,CAAC,UAAU,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAC5E,CAAC;IAEJ,CAAC;IAKD,oBAAoB,CACC,MAAc,EAClB,OAAY;QAG3B,MAAM,IAAI,GAAG,MAAM,CAAC,IAAI,CAAC,IAAmB,CAAC;QAC7C,IAAI,CAAC,MAAM,CAAC,GAAG,CACb,qCAAqC,IAAI,CAAC,MAAM,KAAK,MAAM,CAAC,EAAE,IAAI,EAClE,OAAO,CACR,CAAC;QAIF,IAAI,CAAC,gBAAgB,CAAC,UAAU,CAAC,IAAI,CAAC,MAAM,EAAE,cAAc,EAAE;YAC5D,OAAO,EAAE,oCAAoC;YAC7C,QAAQ,EAAE,OAAO;SAClB,CAAC,CAAC;IACL,CAAC;IAKD,gBAAgB,CACK,MAAc,EAClB,OAAY;QAG3B,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,4BAA4B,MAAM,CAAC,EAAE,GAAG,EAAE,OAAO,CAAC,CAAC;QACnE,OAAO;YACL,KAAK,EAAE,MAAM;YACb,IAAI,EAAE,EAAE,OAAO,EAAE,cAAc,EAAE,SAAS,EAAE,IAAI,IAAI,EAAE,EAAE;SACzD,CAAC;IACJ,CAAC;IAKD,gBAAgB,CAAoB,MAAc;QAChD,MAAM,IAAI,GAAG,MAAM,CAAC,IAAI,CAAC,IAAmB,CAAC;QAC7C,MAAM,QAAQ,GAAG,QAAQ,IAAI,CAAC,MAAM,EAAE,CAAC;QACvC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE,CAAC;YAEhC,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YACtB,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,UAAU,MAAM,CAAC,EAAE,0BAA0B,QAAQ,EAAE,CAAC,CAAC;QAC3E,CAAC;aAAM,CAAC;YACN,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,UAAU,MAAM,CAAC,EAAE,yBAAyB,QAAQ,EAAE,CAAC,CAAC;QAC1E,CAAC;QACD,MAAM,CAAC,IAAI,CAAC,YAAY,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,CAAC,CAAC;IAChD,CAAC;IAOO,YAAY,CAAC,MAAc;QACjC,MAAM,UAAU,GAAG,MAAM,CAAC,SAAS,CAAC,OAAO,CAAC,aAAa,CAAC;QAC1D,IAAI,KAAK,GAAuB,SAAS,CAAC;QAE1C,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,+BAA+B,MAAM,CAAC,EAAE,EAAE,CAAC,CAAC;QAE5D,IAAI,UAAU,IAAI,OAAO,UAAU,KAAK,QAAQ,EAAE,CAAC;YACjD,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,+BAA+B,UAAU,CAAC,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,KAAK,CAAC,CAAC;YACjF,MAAM,KAAK,GAAG,UAAU,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;YACpC,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC,IAAI,KAAK,CAAC,CAAC,CAAC,KAAK,QAAQ,EAAE,CAAC;gBAChD,KAAK,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;gBACjB,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,2CAA2C,CAAC,CAAC;YAC/D,CAAC;QACH,CAAC;QAGD,IAAI,CAAC,KAAK,IAAI,MAAM,CAAC,SAAS,CAAC,IAAI,EAAE,CAAC;YACpC,MAAM,SAAS,GAAG,MAAM,CAAC,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC;YAC9C,IAAI,SAAS,EAAE,CAAC;gBACd,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,kCAAkC,OAAO,SAAS,KAAK,QAAQ,CAAC,CAAC,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC,cAAc,EAAE,CAAC,CAAC;gBAGzI,IAAI,OAAO,SAAS,KAAK,QAAQ,IAAI,SAAS,CAAC,UAAU,CAAC,SAAS,CAAC,EAAE,CAAC;oBACrE,KAAK,GAAG,SAAS,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;oBAC/B,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,0DAA0D,CAAC,CAAC;gBAC9E,CAAC;qBAAM,CAAC;oBACN,KAAK,GAAG,SAAS,CAAC;oBAClB,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,6DAA6D,CAAC,CAAC;gBACjF,CAAC;YACH,CAAC;QACH,CAAC;QAGD,IAAI,CAAC,KAAK,IAAI,MAAM,CAAC,SAAS,CAAC,KAAK,IAAI,MAAM,CAAC,SAAS,CAAC,KAAK,CAAC,KAAK,EAAE,CAAC;YACrE,MAAM,UAAU,GAAG,MAAM,CAAC,SAAS,CAAC,KAAK,CAAC,KAAe,CAAC;YAC1D,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,yBAAyB,UAAU,CAAC,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,KAAK,CAAC,CAAC;YAG3E,IAAI,UAAU,CAAC,UAAU,CAAC,SAAS,CAAC,EAAE,CAAC;gBACrC,KAAK,GAAG,UAAU,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;gBAChC,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,iDAAiD,CAAC,CAAC;YACrE,CAAC;iBAAM,CAAC;gBACN,KAAK,GAAG,UAAU,CAAC;gBACnB,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,oDAAoD,CAAC,CAAC;YACxE,CAAC;QACH,CAAC;QAED,IAAI,KAAK,EAAE,CAAC;YACV,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,2CAA2C,MAAM,CAAC,EAAE,EAAE,CAAC,CAAC;QAC1E,CAAC;aAAM,CAAC;YACN,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,6BAA6B,MAAM,CAAC,EAAE,EAAE,CAAC,CAAC;QAC7D,CAAC;QAED,OAAO,KAAK,CAAC;IACf,CAAC;CACF,CAAA;AA3MY,kCAAW;AAItB;IADC,IAAA,4BAAe,GAAE;8BACV,kBAAM;2CAAC;AAyFf;IAFC,IAAA,kBAAS,EAAC,yBAAU,CAAC;IACrB,IAAA,6BAAgB,EAAC,gBAAgB,CAAC;IAEhC,WAAA,IAAA,4BAAe,GAAE,CAAA;IACjB,WAAA,IAAA,wBAAW,GAAE,CAAA;;qCADa,kBAAM;;uDAgBlC;AAKD;IADC,IAAA,6BAAgB,EAAC,YAAY,CAAC;IAE5B,WAAA,IAAA,4BAAe,GAAE,CAAA;IACjB,WAAA,IAAA,wBAAW,GAAE,CAAA;;qCADa,kBAAM;;mDASlC;AAKD;IAFC,IAAA,kBAAS,EAAC,yBAAU,CAAC;IACrB,IAAA,6BAAgB,EAAC,YAAY,CAAC;IACb,WAAA,IAAA,4BAAe,GAAE,CAAA;;qCAAS,kBAAM;;mDAWjD;sBA7IU,WAAW;IAjBvB,IAAA,6BAAgB,EAAC;QAGhB,SAAS,EAAE,OAAO,CAAC,GAAG,CAAC,YAAY,IAAI,QAAQ;QAC/C,IAAI,EAAE;YACJ,MAAM,EAAE,OAAO,CAAC,GAAG,CAAC,WAAW,EAAE,KAAK,CAAC,GAAG,CAAC,IAAI,GAAG;SAEnD;KACF,CAAC;IACD,IAAA,mBAAU,EAAC,IAAI,uCAAiB,EAAE,CAAC;IACnC,IAAA,iBAAQ,EACP,IAAI,uBAAc,CAAC;QACjB,SAAS,EAAE,IAAI;QACf,SAAS,EAAE,IAAI;QACf,gBAAgB,EAAE,CAAC,MAAM,EAAE,EAAE,CAAC,IAAI,wBAAW,CAAC,MAAM,CAAC;KACtD,CAAC,CACH;qCAWmC,sBAAa;QACV,mDAAuB;QAC7B,gBAAU;GAZ9B,WAAW,CA2MvB"}
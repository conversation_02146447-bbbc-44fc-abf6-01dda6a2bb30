{"version": 3, "file": "base.order-book.service.js", "sourceRoot": "", "sources": ["../../../src/order-book/services/base.order-book.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAIA,2CAAuE;AACvE,yDAAsD;AACtD,6CAAmD;AACnD,yDAAoD;AACpD,qCAAuD;AAEvD,0FAA4E;AAC5E,kEAAsD;AACtD,wEAAkE;AAClE,0DAAqD;AACrD,qEAA0D;AAGnD,IAAM,oBAAoB,GAA1B,MAAM,oBAAoB;IA6BV;IACA;IA7BF,MAAM,GAAG,IAAI,eAAM,CAAC,kBAAkB,CAAC,CAAC;IAMxC,cAAc,GAAG;QAClC,MAAM;QACN,SAAS;QACT,eAAe;QACf,iBAAiB;QACjB,UAAU;QACV,SAAS;QACT,SAAS;QACT,SAAS;KACV,CAAC;IAGiB,mBAAmB,GAAG,oBAAoB,CAAC;IAC3C,mBAAmB,GAAG,oBAAoB,CAAC;IAC3C,mBAAmB,GAAG,oBAAoB,CAAC;IAC3C,oBAAoB,GAAG,qBAAqB,CAAC;IAC7C,0BAA0B,GAAG,0BAA0B,CAAC;IACxD,sBAAsB,GAAG,uBAAuB,CAAC;IACjD,mBAAmB,GAAG,oBAAoB,CAAC;IAE9D,YAEqB,mBAA0C,EAC1C,YAA2B;QAD3B,wBAAmB,GAAnB,mBAAmB,CAAuB;QAC1C,iBAAY,GAAZ,YAAY,CAAe;IAC5C,CAAC;IASK,KAAK,CACb,SAAoB,EACpB,YAAsB,EAAE;QAExB,MAAM,GAAG,GAAG,IAAA,mCAAe,EAAC,6BAAY,EAAE,SAAS,EAAE;YACnD,uBAAuB,EAAE,IAAI;YAC7B,wBAAwB,EAAE,IAAI;YAC9B,mBAAmB,EAAE,IAAI;YACzB,iBAAiB,EAAE,KAAK;SACzB,CAAC,CAAC;QAEH,IAAI,SAAS,CAAC,OAAO,IAAI,SAAS,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACtD,GAAG,CAAC,OAAO,GAAG,SAAS,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,EAAE;gBAC7C,MAAM,SAAS,GAAG,IAAA,mCAAe,EAAC,0CAAkB,EAAE,MAAM,EAAE;oBAC5D,uBAAuB,EAAE,IAAI;oBAC7B,wBAAwB,EAAE,IAAI;oBAC9B,mBAAmB,EAAE,IAAI;oBACzB,iBAAiB,EAAE,KAAK;iBACzB,CAAC,CAAC;gBAEH,IAAI,MAAM,CAAC,OAAO,EAAE,CAAC;oBACnB,SAAS,CAAC,OAAO,GAAG,IAAA,mCAAe,EAAC,kCAAW,EAAE,MAAM,CAAC,OAAO,EAAE;wBAC/D,uBAAuB,EAAE,IAAI;wBAC7B,wBAAwB,EAAE,IAAI;wBAC9B,mBAAmB,EAAE,IAAI;wBACzB,iBAAiB,EAAE,KAAK;qBACzB,CAAC,CAAC;gBACL,CAAC;gBAED,OAAO,SAAS,CAAC;YACnB,CAAC,CAAC,CAAC;QACL,CAAC;aAAM,CAAC;YACN,GAAG,CAAC,OAAO,GAAG,EAAE,CAAC;QACnB,CAAC;QAED,IAAI,GAAG,CAAC,IAAI,EAAE,CAAC;YACb,GAAG,CAAC,IAAI,GAAG,IAAA,mCAAe,EAAC,kBAAI,EAAE,GAAG,CAAC,IAAI,EAAE;gBACzC,uBAAuB,EAAE,IAAI;gBAC7B,wBAAwB,EAAE,IAAI;aAC/B,CAAC,CAAC;QACL,CAAC;QAED,IAAI,GAAG,CAAC,OAAO,EAAE,CAAC;YAEhB,GAAG,CAAC,OAAO,GAAG,IAAA,mCAAe,EAAC,kBAAI,EAAE,GAAG,CAAC,OAAO,EAAE;gBAC/C,uBAAuB,EAAE,IAAI;gBAC7B,wBAAwB,EAAE,IAAI;aAC/B,CAAC,CAAC;QACL,CAAC;QAED,IAAI,GAAG,CAAC,OAAO,EAAE,CAAC;YAEhB,GAAG,CAAC,OAAO,GAAG,IAAA,mCAAe,EAAC,kBAAI,EAAE,GAAG,CAAC,OAAO,EAAE;gBAC/C,uBAAuB,EAAE,IAAI;gBAC7B,wBAAwB,EAAE,IAAI;aAC/B,CAAC,CAAC;QACL,CAAC;QACD,OAAO,GAAG,CAAC;IACb,CAAC;IAOS,iBAAiB,CAAC,SAAmB;QAC7C,IAAI,CAAC,SAAS,IAAI,CAAC,SAAS,CAAC,MAAM;YAAE,OAAO,EAAE,CAAC;QAC/C,OAAO,SAAS,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,EAAE;YAE9B,MAAM,QAAQ,GAAG,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;YACnC,OAAO,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC,CAAC;QACnE,CAAC,CAAC,CAAC;IACL,CAAC;IAUS,KAAK,CAAC,cAAc,CAC5B,EAAU,EACV,YAAsB,EAAE,EACxB,cAAuB,KAAK;QAG5B,IAAI,eAAe,GAAG;YACpB,GAAG,IAAI,GAAG,CAAC,CAAC,GAAG,SAAS,EAAE,SAAS,EAAE,eAAe,CAAC,CAAC;SACvD,CAAC;QACF,eAAe,GAAG,IAAI,CAAC,iBAAiB,CAAC,eAAe,CAAC,CAAC;QAE1D,MAAM,SAAS,GAAG,MAAM,IAAI,CAAC,mBAAmB,CAAC,OAAO,CAAC;YACvD,KAAK,EAAE,EAAE,EAAE,EAAE,GAAG,CAAC,WAAW,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,SAAS,EAAE,KAAK,EAAE,CAAC,EAAE;YAC3D,SAAS,EAAE,eAAe;SAC3B,CAAC,CAAC;QAEH,IAAI,CAAC,SAAS,EAAE,CAAC;YACf,MAAM,IAAI,0BAAiB,CACzB,+BAA+B,EAAE,IAAI,WAAW,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,iBAAiB,EAAE,CAC5E,CAAC;QACJ,CAAC;QAED,OAAO,SAAS,CAAC;IACnB,CAAC;IAOS,gBAAgB,CAAC,MAAe;QACxC,MAAM,WAAW,GAAgC,EAAE,SAAS,EAAE,KAAK,EAAE,CAAC;QAEtE,IAAI,MAAM,EAAE,CAAC;YACX,IAAI,CAAC;gBACH,MAAM,CAAC,KAAK,EAAE,KAAK,CAAC,GAAG,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;gBACzC,WAAW,CAAC,KAAK,CAAC,GAAG,KAAK,CAAC;YAC7B,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,+BAA+B,MAAM,EAAE,CAAC,CAAC;YAC5D,CAAC;QACH,CAAC;QAED,OAAO,WAAW,CAAC;IACrB,CAAC;CACF,CAAA;AAtKY,oDAAoB;+BAApB,oBAAoB;IADhC,IAAA,mBAAU,GAAE;IA6BR,WAAA,IAAA,0BAAgB,EAAC,6BAAS,CAAC,CAAA;qCACY,oBAAU;QACjB,6BAAa;GA9BrC,oBAAoB,CAsKhC"}
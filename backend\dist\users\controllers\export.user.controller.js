"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ExportUserController = void 0;
const openapi = require("@nestjs/swagger");
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const export_user_service_1 = require("../services/export.user.service");
const jwt_auth_guard_1 = require("../../common/guards/jwt-auth.guard");
const roles_guard_1 = require("../../common/guards/roles.guard");
const permissions_guard_1 = require("../../common/guards/permissions.guard");
const roles_decorator_1 = require("../../common/decorators/roles.decorator");
const permissions_decorator_1 = require("../../common/decorators/permissions.decorator");
const api_response_dto_1 = require("../../common/dto/api-response.dto");
let ExportUserController = class ExportUserController {
    exportUserService;
    constructor(exportUserService) {
        this.exportUserService = exportUserService;
    }
    async export(format = 'json') {
        return this.exportUserService.export(format);
    }
    async exportStream(format = 'json', batchSize = 1000, response) {
        return this.exportUserService.exportStream(format, response, batchSize);
    }
    async getExportEstimate() {
        return this.exportUserService.getExportEstimate();
    }
};
exports.ExportUserController = ExportUserController;
__decorate([
    (0, common_1.Get)('export'),
    (0, roles_decorator_1.Roles)('ADMIN'),
    (0, permissions_decorator_1.Permissions)('user:list'),
    (0, swagger_1.ApiOperation)({ summary: 'Xuất danh sách người dùng ra file' }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Dữ liệu đã được xuất thành công.',
        type: api_response_dto_1.ApiResponseDto,
        schema: {
            properties: {
                data: {
                    type: 'array',
                    items: { $ref: '#/components/schemas/UserDto' },
                },
            },
        },
    }),
    (0, swagger_1.ApiQuery)({
        name: 'format',
        required: false,
        enum: ['csv', 'json'],
        description: 'Định dạng xuất file',
    }),
    openapi.ApiResponse({ status: 200, type: Object }),
    __param(0, (0, common_1.Query)('format')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], ExportUserController.prototype, "export", null);
__decorate([
    (0, common_1.Get)('export/stream'),
    (0, roles_decorator_1.Roles)('ADMIN'),
    (0, permissions_decorator_1.Permissions)('user:list'),
    (0, swagger_1.ApiOperation)({ summary: 'Xuất danh sách người dùng ra file với streaming (cho dataset lớn)' }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Dữ liệu đang được stream.',
    }),
    (0, swagger_1.ApiQuery)({
        name: 'format',
        required: false,
        enum: ['csv', 'json'],
        description: 'Định dạng xuất file',
    }),
    (0, swagger_1.ApiQuery)({
        name: 'batchSize',
        required: false,
        type: Number,
        description: 'Kích thước batch (default: 1000)',
    }),
    openapi.ApiResponse({ status: 200 }),
    __param(0, (0, common_1.Query)('format')),
    __param(1, (0, common_1.Query)('batchSize')),
    __param(2, (0, common_1.Res)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Number, Object]),
    __metadata("design:returntype", Promise)
], ExportUserController.prototype, "exportStream", null);
__decorate([
    (0, common_1.Get)('export/estimate'),
    (0, roles_decorator_1.Roles)('ADMIN'),
    (0, permissions_decorator_1.Permissions)('user:list'),
    (0, swagger_1.ApiOperation)({ summary: 'Ước tính thời gian và thông tin export' }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Thông tin ước tính export.',
        schema: {
            properties: {
                totalRecords: { type: 'number' },
                estimatedTimeSeconds: { type: 'number' },
                recommendedBatchSize: { type: 'number' },
                maxAllowedRecords: { type: 'number' },
            },
        },
    }),
    openapi.ApiResponse({ status: 200 }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], ExportUserController.prototype, "getExportEstimate", null);
exports.ExportUserController = ExportUserController = __decorate([
    (0, swagger_1.ApiTags)('users-export'),
    (0, common_1.Controller)('users'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard, roles_guard_1.RolesGuard, permissions_guard_1.PermissionsGuard),
    __metadata("design:paramtypes", [export_user_service_1.ExportUserService])
], ExportUserController);
//# sourceMappingURL=export.user.controller.js.map
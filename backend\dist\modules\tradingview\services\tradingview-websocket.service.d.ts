import { OnM<PERSON>ule<PERSON><PERSON>roy, OnModuleInit } from '@nestjs/common';
import { OnGatewayConnection, OnGatewayDisconnect } from '@nestjs/websockets';
import { Server, Socket } from 'socket.io';
import { ConfigService } from '@nestjs/config';
import { WebSocketMessage, SubscriptionRequest, SubscriptionResponse, CurrentPrice } from '../models/tradingview-data.model';
import { RateLimiterService } from './rate-limiter.service';
export declare class TradingViewWebsocketService implements OnGatewayConnection, OnGatewayDisconnect, OnModuleInit, OnModuleDestroy {
    private readonly configService;
    private readonly rateLimiterService;
    server: Server;
    private readonly logger;
    private clients;
    private subscriptions;
    private heartbeatInterval;
    constructor(configService: ConfigService, rateLimiterService: RateLimiterService);
    onModuleInit(): void;
    onModuleDestroy(): void;
    handleConnection(client: Socket): void;
    handleDisconnect(client: Socket): void;
    handleSubscription(client: Socket, request: SubscriptionRequest): Promise<SubscriptionResponse>;
    handleUnsubscription(client: Socket, symbol: string): SubscriptionResponse;
    broadcastPriceUpdate(priceData: CurrentPrice): void;
    sendToClient(client: Socket, message: WebSocketMessage): void;
    sendErrorToClient(client: Socket, errorMessage: string): void;
    private startHeartbeat;
}

import { OnGatewayConnection, OnGatewayDisconnect } from '@nestjs/websockets';
import { Server, Socket } from 'socket.io';
import { UserDto } from '../dto/user.dto';
export declare class EventsUserGateway implements OnGatewayConnection, OnGatewayDisconnect {
    server: Server;
    private readonly logger;
    handleConnection(client: Socket, ...args: any[]): void;
    handleDisconnect(client: Socket): void;
    emitUserCreated(dto: UserDto): void;
    emitUserUpdated(dto: UserDto): void;
    emitStatusToggled(id: string, status: any): void;
    emitUserDuplicated(dto: UserDto): void;
    emitUserDeleted(id: string, isSoftDelete: boolean): void;
    handleSubscribe(userId: string, client: Socket): void;
    handleUnsubscribe(userId: string, client: Socket): void;
}

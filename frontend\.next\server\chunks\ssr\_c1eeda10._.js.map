{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/bloomgoo/gold-exchange-platform/frontend/hooks/use-mobile.ts"], "sourcesContent": ["import * as React from \"react\"\r\n\r\nconst MO<PERSON>LE_BREAKPOINT = 768\r\n\r\nexport function useIsMobile() {\r\n  const [isMobile, setIsMobile] = React.useState<boolean | undefined>(undefined)\r\n\r\n  React.useEffect(() => {\r\n    const mql = window.matchMedia(`(max-width: ${MOBILE_BREAKPOINT - 1}px)`)\r\n    const onChange = () => {\r\n      setIsMobile(window.innerWidth < MOBILE_BREAKPOINT)\r\n    }\r\n    mql.addEventListener(\"change\", onChange)\r\n    setIsMobile(window.innerWidth < MOBILE_BREAKPOINT)\r\n    return () => mql.removeEventListener(\"change\", onChange)\r\n  }, [])\r\n\r\n  return !!isMobile\r\n}\r\n"], "names": [], "mappings": ";;;AAAA;;AAEA,MAAM,oBAAoB;AAEnB,SAAS;IACd,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,8SAAA,CAAA,WAAc,AAAD,EAAuB;IAEpE,CAAA,GAAA,8SAAA,CAAA,YAAe,AAAD,EAAE;QACd,MAAM,MAAM,OAAO,UAAU,CAAC,CAAC,YAAY,EAAE,oBAAoB,EAAE,GAAG,CAAC;QACvE,MAAM,WAAW;YACf,YAAY,OAAO,UAAU,GAAG;QAClC;QACA,IAAI,gBAAgB,CAAC,UAAU;QAC/B,YAAY,OAAO,UAAU,GAAG;QAChC,OAAO,IAAM,IAAI,mBAAmB,CAAC,UAAU;IACjD,GAAG,EAAE;IAEL,OAAO,CAAC,CAAC;AACX", "debugId": null}}, {"offset": {"line": 32, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/bloomgoo/gold-exchange-platform/frontend/lib/response.ts"], "sourcesContent": ["// API Response DTO\r\nexport interface ApiResponse<T> {\r\n  success: boolean;\r\n  statusCode: number;\r\n  message: string;\r\n  data?: T | T[] | null;\r\n  timestamp: string;\r\n  path?: string;\r\n}\r\n\r\n// Pagination Meta DTO\r\nexport interface PageMeta {\r\n  page: number;\r\n  limit: number;\r\n  itemCount: number;\r\n  pageCount: number;\r\n  hasPreviousPage: boolean;\r\n  hasNextPage: boolean;\r\n}\r\n\r\n// Pagination Query DTO\r\nexport enum SortOrder {\r\n  ASC = 'ASC',\r\n  DESC = 'DESC',\r\n}\r\n\r\nexport interface PaginationQuery {\r\n  page?: number;\r\n  limit?: number;\r\n  search?: string;\r\n  sortBy?: string;\r\n  sortOrder?: SortOrder;\r\n}\r\n\r\n// Pagination Response DTO\r\nexport interface PaginationResponse<T> {\r\n  data: T[];\r\n  meta: PageMeta;\r\n}\r\n\r\n// Helper function để tạo pagination query params\r\nexport const createPaginationQuery = (query: PaginationQuery): string => {\r\n  const params = new URLSearchParams();\r\n  \r\n  if (query.page) params.append('page', query.page.toString());\r\n  if (query.limit) params.append('limit', query.limit.toString());\r\n  if (query.search) params.append('search', query.search);\r\n  if (query.sortBy) params.append('sortBy', query.sortBy);\r\n  if (query.sortOrder) params.append('sortOrder', query.sortOrder);\r\n  \r\n  return params.toString();\r\n};\r\n\r\n// Helper function để parse response từ API\r\nexport const parseApiResponse = <T>(response: any): ApiResponse<T> => {\r\n  return {\r\n    success: response.success,\r\n    statusCode: response.statusCode,\r\n    message: response.message,\r\n    data: response.data,\r\n    timestamp: response.timestamp,\r\n    path: response.path,\r\n  };\r\n};\r\n\r\n// Helper function để parse pagination response\r\nexport const parsePaginationResponse = <T>(response: any): PaginationResponse<T> => {\r\n  return {\r\n    data: response.data,\r\n    meta: {\r\n      page: response.meta.page,\r\n      limit: response.meta.limit,\r\n      itemCount: response.meta.itemCount,\r\n      pageCount: response.meta.pageCount,\r\n      hasPreviousPage: response.meta.hasPreviousPage,\r\n      hasNextPage: response.meta.hasNextPage,\r\n    },\r\n  };\r\n};\r\n\r\n// Helper function để kiểm tra response có thành công không\r\nexport const isSuccessResponse = (response: ApiResponse<any>): boolean => {\r\n  return response.success === true && response.statusCode >= 200 && response.statusCode < 300;\r\n};\r\n\r\n// Helper function để lấy data từ response\r\nexport const getDataFromResponse = <T>(response: ApiResponse<T>): T | null => {\r\n  return response.data as T || null;\r\n};\r\n\r\n// Helper function để xử lý lỗi từ response\r\nexport const handleApiError = (error: any): string => {\r\n  if (error.response && error.response.data) {\r\n    const apiError = error.response.data as ApiResponse<any>;\r\n    return apiError.message || 'Có lỗi xảy ra khi gọi API';\r\n  }\r\n  \r\n  if (error.message) {\r\n    if (error.message.includes('NOT_FOUND')) {\r\n      return 'Không tìm thấy API endpoint. Vui lòng kiểm tra cấu hình API.';\r\n    } else if (error.message.includes('Network Error')) {\r\n      return 'Không thể kết nối đến máy chủ. Vui lòng kiểm tra kết nối mạng.';\r\n    }\r\n    return error.message;\r\n  }\r\n  \r\n  return 'Có lỗi xảy ra khi gọi API';\r\n};\r\n"], "names": [], "mappings": "AAAA,mBAAmB;;;;;;;;;;AAqBZ,IAAA,AAAK,mCAAA;;;WAAA;;AAoBL,MAAM,wBAAwB,CAAC;IACpC,MAAM,SAAS,IAAI;IAEnB,IAAI,MAAM,IAAI,EAAE,OAAO,MAAM,CAAC,QAAQ,MAAM,IAAI,CAAC,QAAQ;IACzD,IAAI,MAAM,KAAK,EAAE,OAAO,MAAM,CAAC,SAAS,MAAM,KAAK,CAAC,QAAQ;IAC5D,IAAI,MAAM,MAAM,EAAE,OAAO,MAAM,CAAC,UAAU,MAAM,MAAM;IACtD,IAAI,MAAM,MAAM,EAAE,OAAO,MAAM,CAAC,UAAU,MAAM,MAAM;IACtD,IAAI,MAAM,SAAS,EAAE,OAAO,MAAM,CAAC,aAAa,MAAM,SAAS;IAE/D,OAAO,OAAO,QAAQ;AACxB;AAGO,MAAM,mBAAmB,CAAI;IAClC,OAAO;QACL,SAAS,SAAS,OAAO;QACzB,YAAY,SAAS,UAAU;QAC/B,SAAS,SAAS,OAAO;QACzB,MAAM,SAAS,IAAI;QACnB,WAAW,SAAS,SAAS;QAC7B,MAAM,SAAS,IAAI;IACrB;AACF;AAGO,MAAM,0BAA0B,CAAI;IACzC,OAAO;QACL,MAAM,SAAS,IAAI;QACnB,MAAM;YACJ,MAAM,SAAS,IAAI,CAAC,IAAI;YACxB,OAAO,SAAS,IAAI,CAAC,KAAK;YAC1B,WAAW,SAAS,IAAI,CAAC,SAAS;YAClC,WAAW,SAAS,IAAI,CAAC,SAAS;YAClC,iBAAiB,SAAS,IAAI,CAAC,eAAe;YAC9C,aAAa,SAAS,IAAI,CAAC,WAAW;QACxC;IACF;AACF;AAGO,MAAM,oBAAoB,CAAC;IAChC,OAAO,SAAS,OAAO,KAAK,QAAQ,SAAS,UAAU,IAAI,OAAO,SAAS,UAAU,GAAG;AAC1F;AAGO,MAAM,sBAAsB,CAAI;IACrC,OAAO,SAAS,IAAI,IAAS;AAC/B;AAGO,MAAM,iBAAiB,CAAC;IAC7B,IAAI,MAAM,QAAQ,IAAI,MAAM,QAAQ,CAAC,IAAI,EAAE;QACzC,MAAM,WAAW,MAAM,QAAQ,CAAC,IAAI;QACpC,OAAO,SAAS,OAAO,IAAI;IAC7B;IAEA,IAAI,MAAM,OAAO,EAAE;QACjB,IAAI,MAAM,OAAO,CAAC,QAAQ,CAAC,cAAc;YACvC,OAAO;QACT,OAAO,IAAI,MAAM,OAAO,CAAC,QAAQ,CAAC,kBAAkB;YAClD,OAAO;QACT;QACA,OAAO,MAAM,OAAO;IACtB;IAEA,OAAO;AACT", "debugId": null}}, {"offset": {"line": 106, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/bloomgoo/gold-exchange-platform/frontend/lib/constants.ts"], "sourcesContent": ["/**\n * Constants cho frontend application\n */\n\n// API Pagination Limits (phải khớp với backend)\nexport const API_PAGINATION = {\n  MAX_LIMIT: 100,           // Giới hạn tối đa từ backend PaginationQueryDto\n  DEFAULT_LIMIT: 20,        // Limit mặc định\n  DEFAULT_PAGE: 1,          // Page mặc định\n  \n  // Limits cho các endpoint đặc biệt\n  CUSTOM_MAX_LIMIT: 300,    // Từ CustomPaginationQueryDto\n  SEARCH_LIMIT: 10,         // Cho search/autocomplete\n} as const;\n\n// Validation Rules\nexport const VALIDATION = {\n  PASSWORD_MIN_LENGTH: 8,\n  USERNAME_MIN_LENGTH: 3,\n  USERNAME_MAX_LENGTH: 20,\n  PHONE_REGEX: /^[0-9]{10,11}$/,\n  EMAIL_REGEX: /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/,\n} as const;\n\n// Error Messages\nexport const ERROR_MESSAGES = {\n  LIMIT_EXCEEDED: 'Số lượng yêu cầu vượt quá giới hạn cho phép',\n  INVALID_PAGE: 'Số trang không hợp lệ',\n  NETWORK_ERROR: 'Lỗi kết nối mạng',\n  UNAUTHORIZED: 'Bạn không có quyền truy cập',\n  FORBIDDEN: 'Truy cập bị từ chối',\n  NOT_FOUND: 'Không tìm thấy tài nguyên',\n  SERVER_ERROR: 'Lỗi máy chủ',\n} as const;\n\n// Success Messages\nexport const SUCCESS_MESSAGES = {\n  CREATED: 'Tạo thành công',\n  UPDATED: 'Cập nhật thành công',\n  DELETED: 'Xóa thành công',\n  SAVED: 'Lưu thành công',\n} as const;\n\n// Storage Keys\nexport const STORAGE_KEYS = {\n  ACCESS_TOKEN: 'accessToken',\n  REFRESH_TOKEN: 'refreshToken',\n  USER_DATA: 'userData',\n  THEME: 'theme',\n  LANGUAGE: 'language',\n} as const;\n\n// API Endpoints Base\nexport const API_ENDPOINTS = {\n  AUTH: '/auth',\n  USERS: '/users',\n  BANKS: '/banks',\n  TRANSACTIONS: '/transactions',\n  WALLETS: '/wallets',\n  ORDERS: '/orders',\n  PRODUCTS: '/products',\n} as const;\n\n// Table/DataGrid Settings\nexport const TABLE_SETTINGS = {\n  DEFAULT_PAGE_SIZE: 20,\n  PAGE_SIZE_OPTIONS: [10, 20, 50, 100],\n  MAX_PAGE_SIZE: API_PAGINATION.MAX_LIMIT,\n} as const;\n\n// File Upload Limits\nexport const FILE_UPLOAD = {\n  MAX_SIZE: 5 * 1024 * 1024, // 5MB\n  ALLOWED_TYPES: ['image/jpeg', 'image/png', 'image/gif', 'application/pdf'],\n  ALLOWED_EXTENSIONS: ['.jpg', '.jpeg', '.png', '.gif', '.pdf'],\n} as const;\n\n// Date/Time Formats\nexport const DATE_FORMATS = {\n  DISPLAY: 'dd/MM/yyyy',\n  DISPLAY_WITH_TIME: 'dd/MM/yyyy HH:mm',\n  API: 'yyyy-MM-dd',\n  API_WITH_TIME: \"yyyy-MM-dd'T'HH:mm:ss.SSSxxx\",\n} as const;\n\n// Currency\nexport const CURRENCY = {\n  VND: 'VND',\n  USD: 'USD',\n  DEFAULT_EXCHANGE_RATE: 24000, // USD to VND\n} as const;\n\n// Timeouts\nexport const TIMEOUTS = {\n  API_REQUEST: 30000,      // 30 seconds\n  DEBOUNCE_SEARCH: 300,    // 300ms\n  TOAST_DURATION: 5000,    // 5 seconds\n  POLLING_INTERVAL: 5000,  // 5 seconds\n} as const;\n"], "names": [], "mappings": "AAAA;;CAEC,GAED,gDAAgD;;;;;;;;;;;;;;AACzC,MAAM,iBAAiB;IAC5B,WAAW;IACX,eAAe;IACf,cAAc;IAEd,mCAAmC;IACnC,kBAAkB;IAClB,cAAc;AAChB;AAGO,MAAM,aAAa;IACxB,qBAAqB;IACrB,qBAAqB;IACrB,qBAAqB;IACrB,aAAa;IACb,aAAa;AACf;AAGO,MAAM,iBAAiB;IAC5B,gBAAgB;IAChB,cAAc;IACd,eAAe;IACf,cAAc;IACd,WAAW;IACX,WAAW;IACX,cAAc;AAChB;AAGO,MAAM,mBAAmB;IAC9B,SAAS;IACT,SAAS;IACT,SAAS;IACT,OAAO;AACT;AAGO,MAAM,eAAe;IAC1B,cAAc;IACd,eAAe;IACf,WAAW;IACX,OAAO;IACP,UAAU;AACZ;AAGO,MAAM,gBAAgB;IAC3B,MAAM;IACN,OAAO;IACP,OAAO;IACP,cAAc;IACd,SAAS;IACT,QAAQ;IACR,UAAU;AACZ;AAGO,MAAM,iBAAiB;IAC5B,mBAAmB;IACnB,mBAAmB;QAAC;QAAI;QAAI;QAAI;KAAI;IACpC,eAAe,eAAe,SAAS;AACzC;AAGO,MAAM,cAAc;IACzB,UAAU,IAAI,OAAO;IACrB,eAAe;QAAC;QAAc;QAAa;QAAa;KAAkB;IAC1E,oBAAoB;QAAC;QAAQ;QAAS;QAAQ;QAAQ;KAAO;AAC/D;AAGO,MAAM,eAAe;IAC1B,SAAS;IACT,mBAAmB;IACnB,KAAK;IACL,eAAe;AACjB;AAGO,MAAM,WAAW;IACtB,KAAK;IACL,KAAK;IACL,uBAAuB;AACzB;AAGO,MAAM,WAAW;IACtB,aAAa;IACb,iBAAiB;IACjB,gBAAgB;IAChB,kBAAkB;AACpB", "debugId": null}}]}
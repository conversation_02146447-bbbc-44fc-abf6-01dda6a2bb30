"use client"

import React from 'react';
import { cn } from '@/lib/utils';

interface NoDataProps {
  title?: string;
  description?: string;
  icon?: 'no-data' | 'empty';
  className?: string;
  size?: 'sm' | 'md' | 'lg';
  showIcon?: boolean;
}

export function NoData({
  title = "Không có dữ liệu",
  description = "Hiện tại chưa có dữ liệu để hiển thị",
  icon = 'no-data',
  className,
  size = 'md',
  showIcon = true,
}: NoDataProps) {
  const sizeConfig = {
    sm: {
      container: "py-8",
      icon: "w-24 h-24",
      title: "text-sm font-medium",
      description: "text-xs"
    },
    md: {
      container: "py-12",
      icon: "w-32 h-32",
      title: "text-base font-medium",
      description: "text-sm"
    },
    lg: {
      container: "py-16",
      icon: "w-40 h-40",
      title: "text-lg font-semibold",
      description: "text-base"
    }
  };

  const config = sizeConfig[size];

  return (
    <div className={cn(
      "flex flex-col items-center justify-center text-center",
      config.container,
      className
    )}>
      {showIcon && (
        <div
          className={cn("mb-4 opacity-60 flex items-center justify-center bg-no-repeat bg-center bg-contain", config.icon)}
          style={{
            backgroundImage: `url(/svg/${icon}.svg)`
          }}
          aria-label="No data illustration"
        />
      )}
      
      <div className="space-y-2">
        <h3 className={cn(
          "text-muted-foreground",
          config.title
        )}>
          {title}
        </h3>
        
        {description && (
          <p className={cn(
            "text-muted-foreground/70 max-w-sm mx-auto",
            config.description
          )}>
            {description}
          </p>
        )}
      </div>
    </div>
  );
}

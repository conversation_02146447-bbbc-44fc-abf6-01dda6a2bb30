"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AddSepayIdFromSepayEntity1744366735282 = void 0;
class AddSepayIdFromSepayEntity1744366735282 {
    async up(queryRunner) {
        await queryRunner.query(`ALTER TABLE "sepay_transactions" ADD COLUMN "sepay_id" varchar(100)`);
    }
    async down(queryRunner) {
        await queryRunner.query(`ALTER TABLE "sepay_transactions" DROP COLUMN "sepay_id"`);
    }
}
exports.AddSepayIdFromSepayEntity1744366735282 = AddSepayIdFromSepayEntity1744366735282;
//# sourceMappingURL=1744366735282-AddSepayIdFromSepayEntity.js.map
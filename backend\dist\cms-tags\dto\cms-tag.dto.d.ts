import { UserDto } from '../../users/dto/user.dto';
export declare class CmsTagDto {
    id: string;
    businessCode: string;
    name: string;
    slug: string;
    description?: string | null;
    imageUrl?: string | null;
    metaTitle?: string | null;
    metaDescription?: string | null;
    metaKeywords?: string | null;
    createdAt: Date;
    updatedAt: Date;
    createdBy?: string;
    updatedBy?: string;
    deletedBy?: string;
    isDeleted: boolean;
    deletedAt?: Date;
    creator?: UserDto;
    updater?: UserDto;
    deleter?: UserDto;
    postsCount?: number;
}

"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AddGoogleFieldUser1747625462464 = void 0;
class AddGoogleFieldUser1747625462464 {
    async up(queryRunner) {
        await queryRunner.query(`
          ALTER TABLE "users" 
          ADD COLUMN IF NOT EXISTS "google_id" VARCHAR NULL,
          ADD COLUMN IF NOT EXISTS "avatar_url" VARCHAR NULL
        `);
    }
    async down(queryRunner) {
        await queryRunner.query(`
          ALTER TABLE "users" 
          DROP COLUMN IF EXISTS "google_id",
          DROP COLUMN IF EXISTS "avatar_url"
        `);
    }
}
exports.AddGoogleFieldUser1747625462464 = AddGoogleFieldUser1747625462464;
//# sourceMappingURL=1747625462464-addGoogleFieldUser.js.map
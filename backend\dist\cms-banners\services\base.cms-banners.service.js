"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var BaseCmsBannersService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.BaseCmsBannersService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const event_emitter_1 = require("@nestjs/event-emitter");
const class_transformer_1 = require("class-transformer");
const cms_banners_entity_1 = require("../entity/cms-banners.entity");
const cms_banner_dto_1 = require("../dto/cms-banner.dto");
let BaseCmsBannersService = BaseCmsBannersService_1 = class BaseCmsBannersService {
    bannerRepository;
    dataSource;
    eventEmitter;
    logger = new common_1.Logger(BaseCmsBannersService_1.name);
    EVENT_BANNER_CREATED = 'cms-banner.created';
    EVENT_BANNER_UPDATED = 'cms-banner.updated';
    EVENT_BANNER_DELETED = 'cms-banner.deleted';
    validRelations = [
        'creator',
        'updater',
        'deleter'
    ];
    constructor(bannerRepository, dataSource, eventEmitter) {
        this.bannerRepository = bannerRepository;
        this.dataSource = dataSource;
        this.eventEmitter = eventEmitter;
    }
    validateRelations(relations) {
        if (!relations || !Array.isArray(relations)) {
            return [];
        }
        return relations.filter(relation => {
            const isValid = this.validRelations.includes(relation);
            if (!isValid) {
                this.logger.warn(`Mối quan hệ không hợp lệ: ${relation}`);
            }
            return isValid;
        });
    }
    buildWhereClause(filter) {
        if (!filter) {
            return { isDeleted: false };
        }
        try {
            const filterObj = JSON.parse(filter);
            return {
                ...filterObj,
                isDeleted: false,
            };
        }
        catch (e) {
            return [
                { title: (0, typeorm_2.ILike)(`%${filter}%`), isDeleted: false },
                { altText: (0, typeorm_2.ILike)(`%${filter}%`), isDeleted: false },
            ];
        }
    }
    async findById(id, relations = [], throwError = true) {
        const validatedRelations = this.validateRelations(relations);
        const banner = await this.bannerRepository.findOne({
            where: { id, isDeleted: false },
            relations: validatedRelations,
        });
        if (!banner && throwError) {
            throw new common_1.NotFoundException(`Không tìm thấy banner với ID: ${id}`);
        }
        return banner;
    }
    async findByBusinessCode(businessCode, throwError = true) {
        const banner = await this.bannerRepository.findOne({
            where: { businessCode, isDeleted: false },
        });
        if (!banner && throwError) {
            throw new common_1.NotFoundException(`Không tìm thấy banner với mã: ${businessCode}`);
        }
        return banner;
    }
    toDto(banner) {
        if (!banner) {
            return null;
        }
        const plainObj = Object.assign({}, banner);
        plainObj.isActive = banner.isActive();
        return (0, class_transformer_1.plainToInstance)(cms_banner_dto_1.CmsBannerDto, plainObj, {
            excludeExtraneousValues: true,
            enableImplicitConversion: true,
            exposeDefaultValues: true,
            exposeUnsetFields: false,
        });
    }
    toDtos(banners) {
        if (!banners || !Array.isArray(banners)) {
            return [];
        }
        return banners.map(banner => this.toDto(banner))
            .filter((dto) => dto !== null);
    }
    isBannerActive(banner) {
        return banner.isActive();
    }
    getBannerImageUrl(banner, isMobile = false) {
        return banner.getImageUrl(isMobile);
    }
    async isTitleUnique(title, excludeId) {
        const whereCondition = { title, isDeleted: false };
        if (excludeId) {
            whereCondition.id = { $ne: excludeId };
        }
        const existingBanner = await this.bannerRepository.findOne({
            where: whereCondition,
        });
        return !existingBanner;
    }
    async getNextDisplayOrder(location) {
        const whereCondition = { isDeleted: false };
        if (location) {
            whereCondition.location = location;
        }
        const maxOrder = await this.bannerRepository
            .createQueryBuilder('banner')
            .select('MAX(banner.displayOrder)', 'maxOrder')
            .where(whereCondition)
            .getRawOne();
        return (maxOrder?.maxOrder || 0) + 1;
    }
    async updateDisplayOrders(location, fromOrder, increment) {
        const queryBuilder = this.bannerRepository
            .createQueryBuilder()
            .update(cms_banners_entity_1.CmsBanners)
            .set({ displayOrder: () => `display_order + ${increment}` })
            .where('display_order >= :fromOrder', { fromOrder })
            .andWhere('is_deleted = :isDeleted', { isDeleted: false });
        if (location) {
            queryBuilder.andWhere('location = :location', { location });
        }
        else {
            queryBuilder.andWhere('location IS NULL');
        }
        await queryBuilder.execute();
    }
};
exports.BaseCmsBannersService = BaseCmsBannersService;
exports.BaseCmsBannersService = BaseCmsBannersService = BaseCmsBannersService_1 = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(cms_banners_entity_1.CmsBanners)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        typeorm_2.DataSource,
        event_emitter_1.EventEmitter2])
], BaseCmsBannersService);
//# sourceMappingURL=base.cms-banners.service.js.map
"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreateOrderBookDto = void 0;
const openapi = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
const swagger_1 = require("@nestjs/swagger");
const order_type_enum_1 = require("../enums/order-type.enum");
const business_type_enum_1 = require("../enums/business-type.enum");
const class_transformer_1 = require("class-transformer");
const create_order_book_detail_dto_1 = require("./create-order-book-detail.dto");
class CreateOrderBookDto {
    userId;
    orderType;
    businessType;
    price;
    products;
    static _OPENAPI_METADATA_FACTORY() {
        return { userId: { required: false, type: () => String }, orderType: { required: true, enum: require("../enums/order-type.enum").OrderType }, businessType: { required: false, enum: require("../enums/business-type.enum").BusinessType }, price: { required: true, type: () => Number }, products: { required: true, type: () => [require("./create-order-book-detail.dto").CreateOrderBookDetailDto] } };
    }
}
exports.CreateOrderBookDto = CreateOrderBookDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'ID người dùng',
        example: '550e8400-e29b-41d4-a716-446655440000',
    }),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], CreateOrderBookDto.prototype, "userId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Loại lệnh (mua/bán)',
        enum: order_type_enum_1.OrderType,
        example: order_type_enum_1.OrderType.BUY,
    }),
    (0, class_validator_1.IsEnum)(order_type_enum_1.OrderType),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], CreateOrderBookDto.prototype, "orderType", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Loại hình giao dịch (thông thường/giao ngay)',
        enum: business_type_enum_1.BusinessType,
        example: business_type_enum_1.BusinessType.NORMAL,
        required: false,
    }),
    (0, class_validator_1.IsEnum)(business_type_enum_1.BusinessType),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], CreateOrderBookDto.prototype, "businessType", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Giá (mua/bán)',
        example: 1000000,
    }),
    (0, class_validator_1.IsNotEmpty)(),
    (0, class_transformer_1.Type)(() => Number),
    __metadata("design:type", Number)
], CreateOrderBookDto.prototype, "price", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Danh sách thông tin chi tiết lệnh',
        type: [create_order_book_detail_dto_1.CreateOrderBookDetailDto],
    }),
    (0, class_validator_1.IsNotEmpty)(),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.ValidateNested)({ each: true }),
    (0, class_transformer_1.Type)(() => create_order_book_detail_dto_1.CreateOrderBookDetailDto),
    __metadata("design:type", Array)
], CreateOrderBookDto.prototype, "products", void 0);
//# sourceMappingURL=create-order-book.dto.js.map
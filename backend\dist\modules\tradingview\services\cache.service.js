"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var CacheService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.CacheService = void 0;
const common_1 = require("@nestjs/common");
const config_1 = require("@nestjs/config");
let CacheService = CacheService_1 = class CacheService {
    configService;
    logger = new common_1.Logger(CacheService_1.name);
    cache = new Map();
    defaultTTL;
    cleanupInterval;
    constructor(configService) {
        this.configService = configService;
        this.defaultTTL = this.configService.get('CACHE_DEFAULT_TTL', 60);
        this.startCleanupInterval();
    }
    set(key, value, ttl = this.defaultTTL) {
        const expiry = Date.now() + ttl * 1000;
        this.cache.set(key, { value, expiry });
        this.logger.debug(`Đã lưu ${key} vào bộ nhớ đệm với thời gian sống ${ttl}s`);
    }
    get(key) {
        const item = this.cache.get(key);
        if (!item) {
            return null;
        }
        if (Date.now() > item.expiry) {
            this.cache.delete(key);
            return null;
        }
        return item.value;
    }
    delete(key) {
        this.cache.delete(key);
    }
    clear() {
        this.cache.clear();
    }
    has(key) {
        const item = this.cache.get(key);
        if (!item) {
            return false;
        }
        if (Date.now() > item.expiry) {
            this.cache.delete(key);
            return false;
        }
        return true;
    }
    startCleanupInterval() {
        const cleanupIntervalTime = this.configService.get('CACHE_CLEANUP_INTERVAL', 60000);
        this.cleanupInterval = setInterval(() => {
            this.cleanup();
        }, cleanupIntervalTime);
    }
    cleanup() {
        const now = Date.now();
        let expiredCount = 0;
        this.cache.forEach((item, key) => {
            if (now > item.expiry) {
                this.cache.delete(key);
                expiredCount++;
            }
        });
        if (expiredCount > 0) {
            this.logger.debug(`Đã dọn dẹp ${expiredCount} mục đã hết hạn trong bộ nhớ đệm`);
        }
    }
    onModuleDestroy() {
        if (this.cleanupInterval) {
            clearInterval(this.cleanupInterval);
        }
    }
};
exports.CacheService = CacheService;
exports.CacheService = CacheService = CacheService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [config_1.ConfigService])
], CacheService);
//# sourceMappingURL=cache.service.js.map
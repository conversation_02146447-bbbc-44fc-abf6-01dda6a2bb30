import { Repository, DataSource } from 'typeorm';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { BaseCmsTagsService } from './base.cms-tags.service';
import { SlugCmsTagsService } from './slug.cms-tags.service';
import { CmsTags } from '../entity/cms-tags.entity';
import { UpdateCmsTagDto } from '../dto/update.cms-tag.dto';
import { CmsTagDto } from '../dto/cms-tag.dto';
export declare class UpdateCmsTagsService extends BaseCmsTagsService {
    protected readonly tagRepository: Repository<CmsTags>;
    protected readonly dataSource: DataSource;
    protected readonly eventEmitter: EventEmitter2;
    private readonly slugService;
    constructor(tagRepository: Repository<CmsTags>, dataSource: DataSource, eventEmitter: EventEmitter2, slugService: SlugCmsTagsService);
    update(id: string, updateDto: UpdateCmsTagDto, userId: string): Promise<CmsTagDto | null>;
    bulkUpdate(updates: Array<{
        id: string;
        data: UpdateCmsTagDto;
    }>, userId: string): Promise<CmsTagDto[]>;
    updateSlugFromName(id: string, userId: string): Promise<CmsTagDto | null>;
    updateNameAndSlug(id: string, name: string, userId: string): Promise<CmsTagDto | null>;
}

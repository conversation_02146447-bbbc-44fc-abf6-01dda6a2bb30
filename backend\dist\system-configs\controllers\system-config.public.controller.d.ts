import { ReadSystemConfigService } from '../services/read.system-config.service';
import { CustomPaginationQueryDto } from '../../common/dto/custom-pagination-query.dto';
export declare class SystemConfigPublicController {
    private readonly systemConfigService;
    private readonly logger;
    constructor(systemConfigService: ReadSystemConfigService);
    getAllConfigs(query: CustomPaginationQueryDto): Promise<Record<string, string>>;
}

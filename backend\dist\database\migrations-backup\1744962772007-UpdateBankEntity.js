"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.UpdateBankEntity************* = void 0;
class UpdateBankEntity************* {
    name = 'UpdateBankEntity*************';
    async up(queryRunner) {
        await queryRunner.query(`ALTER TABLE "banks" DROP COLUMN IF EXISTS "bankBranch"`);
        await queryRunner.query(`ALTER TABLE "banks" DROP COLUMN IF EXISTS "bankAccountNumber"`);
        await queryRunner.query(`ALTER TABLE "banks" DROP COLUMN IF EXISTS "bankAccountHolderName"`);
        const hasIsActiveColumn = await this.checkColumnExists(queryRunner, 'banks', 'isActive');
        if (!hasIsActiveColumn) {
            await queryRunner.query(`ALTER TABLE "banks"
                ADD "isActive" boolean NOT NULL DEFAULT true`);
        }
        const hasDeletedAtColumn = await this.checkColumnExists(queryRunner, 'banks', 'deletedAt');
        if (!hasDeletedAtColumn) {
            await queryRunner.query(`ALTER TABLE "banks"
                ADD "deletedAt" TIMESTAMP`);
        }
        await queryRunner.query(`
            ALTER TABLE "banks"
            ALTER
            COLUMN "createdBy" TYPE uuid USING 
        CASE 
          WHEN "createdBy" IS NULL THEN NULL 
          WHEN "createdBy" ~ '^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$' THEN "createdBy"::uuid
          ELSE NULL
            END
        `);
        await queryRunner.query(`
            ALTER TABLE "banks"
            ALTER
            COLUMN "updatedBy" TYPE uuid USING 
        CASE 
          WHEN "updatedBy" IS NULL THEN NULL 
          WHEN "updatedBy" ~ '^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$' THEN "updatedBy"::uuid
          ELSE NULL
            END
        `);
        await queryRunner.query(`
            ALTER TABLE "banks"
            ALTER
            COLUMN "deletedBy" TYPE uuid USING 
        CASE 
          WHEN "deletedBy" IS NULL THEN NULL 
          WHEN "deletedBy" ~ '^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$' THEN "deletedBy"::uuid
          ELSE NULL
            END
        `);
    }
    async down(queryRunner) {
        await queryRunner.query(`ALTER TABLE "banks" ALTER COLUMN "createdBy" TYPE character varying`);
        await queryRunner.query(`ALTER TABLE "banks" ALTER COLUMN "updatedBy" TYPE character varying`);
        await queryRunner.query(`ALTER TABLE "banks" ALTER COLUMN "deletedBy" TYPE character varying`);
        const hasDeletedAtColumn = await this.checkColumnExists(queryRunner, 'banks', 'deletedAt');
        if (hasDeletedAtColumn) {
            await queryRunner.query(`ALTER TABLE "banks" DROP COLUMN "deletedAt"`);
        }
        const hasIsActiveColumn = await this.checkColumnExists(queryRunner, 'banks', 'isActive');
        if (hasIsActiveColumn) {
            await queryRunner.query(`ALTER TABLE "banks" DROP COLUMN "isActive"`);
        }
        await queryRunner.query(`ALTER TABLE "banks"
            ADD "bankBranch" character varying`);
        await queryRunner.query(`ALTER TABLE "banks"
            ADD "bankAccountNumber" character varying NOT NULL DEFAULT ''`);
        await queryRunner.query(`ALTER TABLE "banks"
            ADD "bankAccountHolderName" character varying NOT NULL DEFAULT ''`);
    }
    async checkColumnExists(queryRunner, tableName, columnName) {
        const table = await queryRunner.getTable(tableName);
        return !!table?.findColumnByName(columnName);
    }
}
exports.UpdateBankEntity************* = UpdateBankEntity*************;
//# sourceMappingURL=*************-UpdateBankEntity.js.map
"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var BaseEcomProductCategoriesService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.BaseEcomProductCategoriesService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const event_emitter_1 = require("@nestjs/event-emitter");
const class_transformer_1 = require("class-transformer");
const ecom_product_categories_entity_1 = require("../entity/ecom-product-categories.entity");
const ecom_product_category_dto_1 = require("../dto/ecom-product-category.dto");
let BaseEcomProductCategoriesService = BaseEcomProductCategoriesService_1 = class BaseEcomProductCategoriesService {
    categoryRepository;
    dataSource;
    eventEmitter;
    logger = new common_1.Logger(BaseEcomProductCategoriesService_1.name);
    EVENT_CATEGORY_CREATED = 'ecom-product-category.created';
    EVENT_CATEGORY_UPDATED = 'ecom-product-category.updated';
    EVENT_CATEGORY_DELETED = 'ecom-product-category.deleted';
    validRelations = [
        'parent',
        'children',
        'products',
        'creator',
        'updater',
        'deleter'
    ];
    constructor(categoryRepository, dataSource, eventEmitter) {
        this.categoryRepository = categoryRepository;
        this.dataSource = dataSource;
        this.eventEmitter = eventEmitter;
    }
    async getQueryRunner() {
        const queryRunner = this.dataSource.createQueryRunner();
        await queryRunner.connect();
        await queryRunner.startTransaction();
        return queryRunner;
    }
    validateRelations(relations) {
        if (!relations || !Array.isArray(relations)) {
            return [];
        }
        return relations.filter(relation => this.validRelations.includes(relation));
    }
    buildWhereClause(filter) {
        if (!filter) {
            return { isDeleted: false };
        }
        try {
            const filterObj = JSON.parse(filter);
            return {
                ...filterObj,
                isDeleted: false,
            };
        }
        catch (e) {
            return [
                { name: (0, typeorm_2.ILike)(`%${filter}%`), isDeleted: false },
                { description: (0, typeorm_2.ILike)(`%${filter}%`), isDeleted: false },
            ];
        }
    }
    async findById(id, relations = [], throwError = true) {
        const validatedRelations = this.validateRelations(relations);
        const category = await this.categoryRepository.findOne({
            where: { id, isDeleted: false },
            relations: validatedRelations,
        });
        if (!category && throwError) {
            throw new common_1.NotFoundException(`Không tìm thấy danh mục với ID: ${id}`);
        }
        return category;
    }
    toDto(category) {
        if (!category) {
            return null;
        }
        const plainObj = Object.assign({}, category);
        if (category.parent) {
            plainObj.parentId = category.parent.id;
        }
        return (0, class_transformer_1.plainToInstance)(ecom_product_category_dto_1.EcomProductCategoryDto, plainObj, {
            excludeExtraneousValues: true,
            enableImplicitConversion: true,
            exposeDefaultValues: true,
            exposeUnsetFields: false,
        });
    }
    toDtos(categories) {
        if (!categories || !Array.isArray(categories)) {
            return [];
        }
        return categories.map(category => this.toDto(category))
            .filter((dto) => dto !== null);
    }
};
exports.BaseEcomProductCategoriesService = BaseEcomProductCategoriesService;
exports.BaseEcomProductCategoriesService = BaseEcomProductCategoriesService = BaseEcomProductCategoriesService_1 = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(ecom_product_categories_entity_1.EcomProductCategories)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        typeorm_2.DataSource,
        event_emitter_1.EventEmitter2])
], BaseEcomProductCategoriesService);
//# sourceMappingURL=base.ecom-product-categories.service.js.map
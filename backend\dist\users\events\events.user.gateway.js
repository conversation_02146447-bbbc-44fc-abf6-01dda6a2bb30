"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var EventsUserGateway_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.EventsUserGateway = void 0;
const websockets_1 = require("@nestjs/websockets");
const socket_io_1 = require("socket.io");
const common_1 = require("@nestjs/common");
let EventsUserGateway = EventsUserGateway_1 = class EventsUserGateway {
    server;
    logger = new common_1.Logger(EventsUserGateway_1.name);
    handleConnection(client, ...args) {
        const clientId = client.id;
        this.logger.log(`Client connected: ${clientId}`);
    }
    handleDisconnect(client) {
        this.logger.log(`Client disconnected: ${client.id}`);
    }
    emitUserCreated(dto) {
        const event = `user_created`;
        this.logger.verbose(`Emitting event [${event}]`, dto);
        this.server.emit(event, dto);
    }
    emitUserUpdated(dto) {
        const event = `user_updated`;
        const roomId = `user_${dto.id}`;
        this.logger.verbose(`Emitting event [${event}] to room [${roomId}]`, dto);
        this.server.to(roomId).emit(event, dto);
        this.server.emit(event, dto);
    }
    emitStatusToggled(id, status) {
        const event = `user_status_toggled`;
        const payload = { id, status };
        const roomId = `user_${id}`;
        this.logger.verbose(`Emitting event [${event}] to room [${roomId}]`, payload);
        this.server.to(roomId).emit(event, payload);
        this.server.emit(event, payload);
    }
    emitUserDuplicated(dto) {
        this.emitUserCreated(dto);
    }
    emitUserDeleted(id, isSoftDelete) {
        const event = isSoftDelete ? `user_soft_deleted` : `user_deleted`;
        const payload = { id };
        const roomId = `user_${id}`;
        this.logger.verbose(`Emitting event [${event}] to room [${roomId}]`, payload);
        this.server.to(roomId).emit(event, payload);
        this.server.emit(event, payload);
        this.server.in(roomId).socketsLeave(roomId);
        this.logger.verbose(`Forced clients to leave room [${roomId}]`);
    }
    handleSubscribe(userId, client) {
        const roomId = `user_${userId}`;
        client.join(roomId);
        this.logger.log(`Client ${client.id} subscribed to room [${roomId}]`);
        client.emit('subscribed', { room: roomId });
    }
    handleUnsubscribe(userId, client) {
        const roomId = `user_${userId}`;
        client.leave(roomId);
        this.logger.log(`Client ${client.id} unsubscribed from room [${roomId}]`);
        client.emit('unsubscribed', { room: roomId });
    }
};
exports.EventsUserGateway = EventsUserGateway;
__decorate([
    (0, websockets_1.WebSocketServer)(),
    __metadata("design:type", socket_io_1.Server)
], EventsUserGateway.prototype, "server", void 0);
__decorate([
    (0, websockets_1.SubscribeMessage)('subscribe_to_user'),
    __param(0, (0, websockets_1.MessageBody)()),
    __param(1, (0, websockets_1.ConnectedSocket)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, socket_io_1.Socket]),
    __metadata("design:returntype", void 0)
], EventsUserGateway.prototype, "handleSubscribe", null);
__decorate([
    (0, websockets_1.SubscribeMessage)('unsubscribe_from_user'),
    __param(0, (0, websockets_1.MessageBody)()),
    __param(1, (0, websockets_1.ConnectedSocket)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, socket_io_1.Socket]),
    __metadata("design:returntype", void 0)
], EventsUserGateway.prototype, "handleUnsubscribe", null);
exports.EventsUserGateway = EventsUserGateway = EventsUserGateway_1 = __decorate([
    (0, websockets_1.WebSocketGateway)({
        cors: { origin: '*' },
    })
], EventsUserGateway);
//# sourceMappingURL=events.user.gateway.js.map
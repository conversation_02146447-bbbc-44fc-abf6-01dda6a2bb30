# Script PowerShell để test chức năng import ngân hàng
# Chạy từ thư mục backend: .\scripts\run-bank-import-test.ps1

param(
    [string]$BackendUrl = "http://localhost:3000",
    [string]$AdminEmail = "<EMAIL>", 
    [string]$AdminPassword = "admin123",
    [switch]$SkipInstall,
    [switch]$OnlyFetch,
    [switch]$Verbose
)

Write-Host "=== SCRIPT TEST IMPORT NGÂN HÀNG ===" -ForegroundColor Green
Write-Host "Backend URL: $BackendUrl" -ForegroundColor Yellow
Write-Host "Admin Email: $AdminEmail" -ForegroundColor Yellow

# Kiểm tra Node.js
if (-not (Get-Command node -ErrorAction SilentlyContinue)) {
    Write-Host "❌ Node.js không được cài đặt. Vui lòng cài đặt Node.js trước." -ForegroundColor Red
    exit 1
}

# Kiểm tra thư mục hiện tại
if (-not (Test-Path "package.json")) {
    Write-Host "❌ Vui lòng chạy script từ thư mục backend (chứa package.json)" -ForegroundColor Red
    exit 1
}

# Tạo thư mục output
$outputDir = "scripts/output"
if (-not (Test-Path $outputDir)) {
    New-Item -ItemType Directory -Path $outputDir -Force | Out-Null
    Write-Host "✅ Đã tạo thư mục output: $outputDir" -ForegroundColor Green
}

# Cài đặt dependencies nếu cần
if (-not $SkipInstall) {
    Write-Host "📦 Kiểm tra và cài đặt dependencies..." -ForegroundColor Blue
    
    # Kiểm tra axios
    $axiosInstalled = npm list axios --depth=0 2>$null
    if ($LASTEXITCODE -ne 0) {
        Write-Host "📦 Cài đặt axios..." -ForegroundColor Blue
        npm install axios
        if ($LASTEXITCODE -ne 0) {
            Write-Host "❌ Không thể cài đặt axios" -ForegroundColor Red
            exit 1
        }
    }
    
    Write-Host "✅ Dependencies đã sẵn sàng" -ForegroundColor Green
}

# Cập nhật cấu hình trong script
$scriptPath = "scripts/import-banks.js"
if (Test-Path $scriptPath) {
    Write-Host "🔧 Cập nhật cấu hình script..." -ForegroundColor Blue
    
    # Đọc nội dung file
    $content = Get-Content $scriptPath -Raw
    
    # Thay thế cấu hình
    $content = $content -replace "BACKEND_URL: 'http://localhost:3000'", "BACKEND_URL: '$BackendUrl'"
    $content = $content -replace "email: '<EMAIL>'", "email: '$AdminEmail'"
    $content = $content -replace "password: 'admin123'", "password: '$AdminPassword'"
    
    # Ghi lại file
    Set-Content $scriptPath -Value $content -Encoding UTF8
    Write-Host "✅ Đã cập nhật cấu hình" -ForegroundColor Green
}

# Kiểm tra backend có đang chạy không
Write-Host "🔍 Kiểm tra backend..." -ForegroundColor Blue
try {
    $response = Invoke-WebRequest -Uri "$BackendUrl/api/v1/health" -Method GET -TimeoutSec 5 -ErrorAction Stop
    Write-Host "✅ Backend đang chạy" -ForegroundColor Green
} catch {
    Write-Host "⚠️  Không thể kết nối đến backend tại $BackendUrl" -ForegroundColor Yellow
    Write-Host "   Vui lòng đảm bảo backend đang chạy trước khi tiếp tục" -ForegroundColor Yellow
    
    $continue = Read-Host "Bạn có muốn tiếp tục không? (y/N)"
    if ($continue -ne "y" -and $continue -ne "Y") {
        exit 1
    }
}

# Chạy script chính
Write-Host "🚀 Bắt đầu chạy test..." -ForegroundColor Green
Write-Host "📝 Log sẽ được ghi vào: scripts/output/import-banks.log" -ForegroundColor Blue

if ($OnlyFetch) {
    Write-Host "📡 Chỉ lấy dữ liệu từ API (không test tạo ngân hàng)..." -ForegroundColor Blue
    
    # Tạo script tạm để chỉ fetch data
    $tempScript = @"
const { fetchBankData, transformBankData } = require('./scripts/import-banks.js');
const fs = require('fs');

async function fetchOnly() {
    try {
        console.log('Đang lấy dữ liệu từ API...');
        const rawData = await fetchBankData();
        const transformedData = transformBankData(rawData);
        
        fs.writeFileSync('./scripts/output/banks-data.json', JSON.stringify(transformedData, null, 2));
        console.log('✅ Đã lưu dữ liệu vào scripts/output/banks-data.json');
        console.log('📊 Tổng số ngân hàng:', rawData.length);
        console.log('📊 Ngân hàng hỗ trợ lookup:', rawData.filter(b => b.lookup_supported === 1).length);
    } catch (error) {
        console.error('❌ Lỗi:', error.message);
        process.exit(1);
    }
}

fetchOnly();
"@
    
    Set-Content "temp-fetch.js" -Value $tempScript -Encoding UTF8
    node temp-fetch.js
    Remove-Item "temp-fetch.js" -Force
} else {
    # Chạy script đầy đủ
    if ($Verbose) {
        node scripts/import-banks.js
    } else {
        node scripts/import-banks.js 2>&1 | Tee-Object -FilePath "scripts/output/console.log"
    }
}

$exitCode = $LASTEXITCODE

# Hiển thị kết quả
Write-Host "`n=== KẾT QUẢ ===" -ForegroundColor Green

if ($exitCode -eq 0) {
    Write-Host "✅ Script chạy thành công!" -ForegroundColor Green
    
    # Hiển thị các file đã tạo
    Write-Host "`n📁 Các file đã tạo:" -ForegroundColor Blue
    Get-ChildItem "scripts/output" | ForEach-Object {
        $size = [math]::Round($_.Length / 1KB, 2)
        Write-Host "   📄 $($_.Name) ($size KB)" -ForegroundColor White
    }
    
    # Hiển thị một số thống kê nếu có file dữ liệu
    $dataFile = "scripts/output/banks-data.json"
    if (Test-Path $dataFile) {
        try {
            $bankData = Get-Content $dataFile | ConvertFrom-Json
            Write-Host "`n📊 Thống kê:" -ForegroundColor Blue
            Write-Host "   🏦 Tổng số ngân hàng: $($bankData.Count)" -ForegroundColor White
            Write-Host "   ✅ Ngân hàng active: $(($bankData | Where-Object { $_.isActive }).Count)" -ForegroundColor White
            Write-Host "   🔗 Có logo: $(($bankData | Where-Object { $_.logoPath }).Count)" -ForegroundColor White
        } catch {
            Write-Host "   ⚠️  Không thể đọc thống kê từ file dữ liệu" -ForegroundColor Yellow
        }
    }
    
} else {
    Write-Host "❌ Script gặp lỗi (Exit code: $exitCode)" -ForegroundColor Red
    
    # Hiển thị log lỗi nếu có
    $logFile = "scripts/output/import-banks.log"
    if (Test-Path $logFile) {
        Write-Host "`n📋 Log lỗi cuối cùng:" -ForegroundColor Yellow
        Get-Content $logFile | Select-Object -Last 10 | ForEach-Object {
            if ($_ -match "\[ERROR\]") {
                Write-Host "   $_" -ForegroundColor Red
            } else {
                Write-Host "   $_" -ForegroundColor White
            }
        }
    }
}

Write-Host "`n🔍 Để xem log chi tiết: Get-Content scripts/output/import-banks.log" -ForegroundColor Blue
Write-Host "📁 Để xem dữ liệu: Get-Content scripts/output/banks-data.json | ConvertFrom-Json" -ForegroundColor Blue

exit $exitCode

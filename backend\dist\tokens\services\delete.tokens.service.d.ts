import { Repository } from 'typeorm';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { BaseTokensService } from './base.tokens.service';
import { Token } from '../entities/token.entity';
import { TokenDto } from '../dto/token.dto';
export declare class DeleteTokensService extends BaseTokensService {
    protected readonly tokenRepository: Repository<Token>;
    protected readonly eventEmitter: EventEmitter2;
    constructor(tokenRepository: Repository<Token>, eventEmitter: EventEmitter2);
    remove(id: string): Promise<{
        affected: number;
    }>;
    bulkDelete(ids: string[]): Promise<{
        affected: number;
    }>;
    softDelete(id: string, deletedById?: string): Promise<TokenDto>;
    bulkSoftDelete(ids: string[], deletedById?: string): Promise<{
        affected: number;
        dtos: TokenDto[];
    }>;
    restore(id: string): Promise<TokenDto>;
    cleanupOldRecords(days: number): Promise<{
        affected: number;
    }>;
}

import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  IsBoolean,
  IsDecimal,
} from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export class UpdateEcomProductDto {
  @ApiProperty({
    description: 'ID của sản phẩm',
    example: '550e8400-e29b-41d4-a716-************',
  })
  @IsUUID()
  id: string;

  @ApiPropertyOptional({
    description: 'Mã sản phẩm',
    example: 'SP001',
  })
  @IsString()
  @MaxLength(50)
  @IsOptional()
  productCode?: string;

  @ApiPropertyOptional({
    description: 'Tên sản phẩm',
    example: 'Nhẫn bạc 925',
  })
  @IsString()
  @MaxLength(100)
  @IsOptional()
  productName?: string;

  @ApiPropertyOptional({
    description: 'ID danh mục sản phẩm',
    example: '550e8400-e29b-41d4-a716-************',
  })
  @IsUUID()
  @IsOptional()
  categoryId?: string;

  @ApiPropertyOptional({
    description: 'Mô tả sản phẩm',
    example: 'Nhẫn bạc cao cấp với độ tinh khiết 925',
  })
  @IsString()
  @IsOptional()
  description?: string;

  @ApiPropertyOptional({
    description: 'Trọng lượng (oz)',
    example: 5.75,
  })
  @IsNumber()
  @IsOptional()
  weight?: number;

  @ApiPropertyOptional({
    description: 'URL hình ảnh',
    example: 'https://example.com/images/silver-ring.jpg',
  })
  @IsString()
  @IsOptional()
  imageUrl?: string;

  @ApiPropertyOptional({
    description: 'Giá bán thông thường',
    example: 1500000,
  })
  @IsNumber()
  @IsOptional()
  regularPrice?: number;

  @ApiPropertyOptional({
    description: 'Giá khuyến mãi',
    example: 1350000,
  })
  @IsNumber()
  @IsOptional()
  salePrice?: number;

  @ApiPropertyOptional({
    description: 'Số lượng tồn kho',
    example: 100,
  })
  @IsNumber()
  @IsOptional()
  stockQuantity?: number;

  @ApiPropertyOptional({
    description: 'Trạng thái hoạt động',
    example: true,
  })
  @IsBoolean()
  @IsOptional()
  isActive?: boolean;

  @ApiPropertyOptional({
    description: 'Slug SEO-friendly (tự động tạo từ tên sản phẩm nếu không cung cấp)',
    example: 'nhan-bac-925-cao-cap',
  })
  @IsString()
  @IsOptional()
  slug?: string;
}
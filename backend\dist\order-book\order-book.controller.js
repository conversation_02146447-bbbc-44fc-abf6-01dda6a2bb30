"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var OrderBookController_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.OrderBookController = void 0;
const openapi = require("@nestjs/swagger");
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const order_book_dto_1 = require("./dto/order-book.dto");
const create_order_book_dto_1 = require("./dto/create-order-book.dto");
const update_order_book_dto_1 = require("./dto/update-order-book.dto");
const settlement_order_book_dto_1 = require("./dto/settlement-order-book.dto");
const jwt_auth_guard_1 = require("../common/guards/jwt-auth.guard");
const api_response_dto_1 = require("../dto/api-response.dto");
const order_book_service_1 = require("./order-book.service");
const roles_decorator_1 = require("../common/decorators/roles.decorator");
const permissions_decorator_1 = require("../common/decorators/permissions.decorator");
const get_user_decorator_1 = require("../common/decorators/get-user.decorator");
const pagination_query_dto_1 = require("../common/dto/pagination-query.dto");
const class_transformer_1 = require("class-transformer");
const order_status_enum_1 = require("./enums/order-status.enum");
const user_entity_1 = require("../users/entities/user.entity");
let OrderBookController = OrderBookController_1 = class OrderBookController {
    orderBookService;
    logger = new common_1.Logger(OrderBookController_1.name);
    constructor(orderBookService) {
        this.orderBookService = orderBookService;
    }
    async create(createOrderBookDto, userId) {
        return this.orderBookService.create(createOrderBookDto, userId);
    }
    async bulkCreate(createOrderBookDtos, userId) {
        return this.orderBookService.bulkCreate(createOrderBookDtos, userId);
    }
    async findAll(user, limit = 10, page = 1, sortBy, sortOrder = pagination_query_dto_1.SortOrder.DESC, search, relations, filter) {
        this.logger.log(`Received filter in controller: ${filter}`);
        const relationArray = relations ? relations.split(',') : [];
        const paginationParams = {
            limit,
            page,
            sortBy,
            sortOrder,
            search,
            skip: (page - 1) * limit,
            filter,
        };
        const isAdmin = Array.isArray(user.roles) && user.roles.includes('ADMIN');
        if (isAdmin) {
            return this.orderBookService.findAll({
                ...paginationParams,
                relations: relationArray,
            });
        }
        else {
            const userFilter = `userId:${user.id}`;
            const combinedFilter = filter ? `${userFilter},${filter}` : userFilter;
            return this.orderBookService.findAll({
                ...paginationParams,
                relations: relationArray,
                filter: combinedFilter,
            });
        }
    }
    async search(user, keyword, limit = 10, page = 1, sortBy, sortOrder = pagination_query_dto_1.SortOrder.DESC) {
        const paginationParams = {
            limit,
            page,
            sortBy,
            sortOrder,
            skip: (page - 1) * limit,
        };
        return this.orderBookService.search(user, keyword, paginationParams);
    }
    async getStatistics(user) {
        const isAdmin = Array.isArray(user.roles) && user.roles.includes('ADMIN');
        if (isAdmin) {
            return this.orderBookService.getStatistics();
        }
        else {
            const filter = `userId:${user.id}`;
            return this.orderBookService.getStatistics(filter);
        }
    }
    async count(user, filter) {
        const isAdmin = user.userRoles?.some((ur) => !ur.isDeleted &&
            !ur.deletedAt &&
            ur.role &&
            !ur.role.isDeleted &&
            ur.role.name === 'ADMIN');
        if (!isAdmin) {
            filter = filter ? `${filter},userId:${user.id}` : `userId:${user.id}`;
        }
        return this.orderBookService.count(filter);
    }
    async export(res) {
        try {
            const buffer = await this.orderBookService.export();
            const filename = `order-books-${new Date().toISOString().split('T')[0]}.xlsx`;
            res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
            res.setHeader('Content-Disposition', `attachment; filename="${filename}"`);
            res.setHeader('Content-Length', buffer.length);
            res.end(buffer);
        }
        catch (error) {
            this.logger.error(`Lỗi khi xuất dữ liệu: ${error.message}`, error.stack);
            res.status(500).json({
                message: 'Không thể xuất dữ liệu sổ lệnh',
                error: error.message,
            });
        }
    }
    async findOne(id) {
        return this.orderBookService.findOne(id);
    }
    async duplicate(id, userId) {
        return this.orderBookService.duplicate(id, userId);
    }
    async update(id, updateOrderBookDto, userId) {
        return this.orderBookService.update(id, updateOrderBookDto, userId);
    }
    async bulkUpdate(updateOrderBookDtos, userId) {
        return this.orderBookService.bulkUpdate(updateOrderBookDtos, userId);
    }
    async toggleStatus(id, userId) {
        return this.orderBookService.toggleStatus(id, userId);
    }
    async remove(id) {
        return this.orderBookService.remove(id);
    }
    async bulkDelete(ids) {
        return this.orderBookService.bulkDelete(ids);
    }
    async softDelete(id, userId) {
        return this.orderBookService.softDelete(id, userId);
    }
    async bulkSoftDelete(ids, userId) {
        return this.orderBookService.bulkSoftDelete(ids, userId);
    }
    async findDeleted(limit = 10, page = 1, sortBy, sortOrder = pagination_query_dto_1.SortOrder.DESC) {
        const paginationParams = {
            limit,
            page,
            sortBy,
            sortOrder,
            skip: (page - 1) * limit,
        };
        return this.orderBookService.findDeleted(paginationParams);
    }
    async restore(id, userId) {
        return this.orderBookService.restore(id, userId);
    }
    async updateStatusPending(id, userId) {
        const order = await this.orderBookService.updateOrderStatusPending(id, userId);
        return (0, class_transformer_1.plainToInstance)(order_book_dto_1.OrderBookDto, order, {
            excludeExtraneousValues: true,
        });
    }
    async updateStatusCompleted(id, settlementData, userId) {
        try {
            const updatedOrder = await this.orderBookService.updateOrderStatusCompleted(id, userId, settlementData || undefined);
            return (0, class_transformer_1.plainToInstance)(order_book_dto_1.OrderBookDto, updatedOrder, {
                excludeExtraneousValues: true,
            });
        }
        catch (error) {
            if (error instanceof common_1.NotFoundException) {
                throw error;
            }
            if (error instanceof common_1.BadRequestException) {
                throw error;
            }
            throw new common_1.InternalServerErrorException(`Lỗi khi cập nhật trạng thái lệnh: ${error.message}`);
        }
    }
    async updateStatusWaitPayment(id, userId) {
        const order = await this.orderBookService.updateOrderStatusWaitPayment(id, userId);
        return (0, class_transformer_1.plainToInstance)(order_book_dto_1.OrderBookDto, order, {
            excludeExtraneousValues: true,
        });
    }
    async findMyOrders(user, limit = 10, page = 1, sortBy, sortOrder = pagination_query_dto_1.SortOrder.DESC, search, status, relations) {
        const relationArray = relations ? relations.split(',') : [];
        const paginationParams = {
            limit,
            page,
            sortBy,
            sortOrder,
            search,
            skip: (page - 1) * limit,
            relations: relationArray,
            filter: `userId:${user.id}${status ? `,status:${status}` : ''}`,
        };
        return this.orderBookService.findAll(paginationParams);
    }
    async createTokenWithdrawal(createOrderBookDto, userId) {
        try {
            return this.orderBookService.createTokenWithdrawal(createOrderBookDto, userId);
        }
        catch (error) {
            if (error instanceof common_1.NotFoundException) {
                throw error;
            }
            if (error instanceof common_1.BadRequestException) {
                throw error;
            }
            throw new common_1.InternalServerErrorException(`Lỗi khi tạo lệnh rút token: ${error.message}`);
        }
    }
    async updateStatusCancelled(id, userId) {
        try {
            const updatedOrder = await this.orderBookService.updateOrderStatusCancelled(id, userId);
            return (0, class_transformer_1.plainToInstance)(order_book_dto_1.OrderBookDto, updatedOrder, {
                excludeExtraneousValues: true,
            });
        }
        catch (error) {
            if (error instanceof common_1.NotFoundException ||
                error instanceof common_1.BadRequestException) {
                throw error;
            }
            throw new common_1.InternalServerErrorException(`Lỗi khi cập nhật trạng thái lệnh rút token: ${error.message}`);
        }
    }
    async approveOrder(id, userId) {
        try {
            const updatedOrder = await this.orderBookService.approveOrder(id, userId);
            return (0, class_transformer_1.plainToInstance)(order_book_dto_1.OrderBookDto, updatedOrder, {
                excludeExtraneousValues: true,
            });
        }
        catch (error) {
            if (error instanceof common_1.NotFoundException) {
                throw error;
            }
            if (error instanceof common_1.BadRequestException) {
                throw error;
            }
            throw new common_1.InternalServerErrorException(`Lỗi khi phê duyệt lệnh: ${error.message}`);
        }
    }
    async extendSettlement(id, userId) {
        try {
            return this.orderBookService.extendSettlement(id, userId);
        }
        catch (error) {
            if (error instanceof common_1.NotFoundException ||
                error instanceof common_1.BadRequestException) {
                throw error;
            }
            throw new common_1.InternalServerErrorException(`Lỗi khi gia hạn thời gian tất toán: ${error.message}`);
        }
    }
};
exports.OrderBookController = OrderBookController;
__decorate([
    (0, common_1.Post)(),
    (0, roles_decorator_1.Roles)('ADMIN', 'USER'),
    (0, permissions_decorator_1.Permissions)('order:create'),
    (0, swagger_1.ApiOperation)({ summary: 'Create a new order with relations' }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.CREATED,
        description: 'The order has been successfully created.',
        type: api_response_dto_1.ApiResponseDto,
        schema: {
            properties: { data: { $ref: '#/components/schemas/OrderBookDto' } },
        },
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.BAD_REQUEST,
        description: 'Invalid input data.',
        type: api_response_dto_1.ApiResponseDto,
        schema: { properties: { data: { type: 'object', nullable: true } } },
    }),
    (0, swagger_1.ApiBody)({ type: create_order_book_dto_1.CreateOrderBookDto }),
    openapi.ApiResponse({ status: 201, type: require("./dto/order-book.dto").OrderBookDto }),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, get_user_decorator_1.GetUser)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [create_order_book_dto_1.CreateOrderBookDto, String]),
    __metadata("design:returntype", Promise)
], OrderBookController.prototype, "create", null);
__decorate([
    (0, common_1.Post)('bulk'),
    (0, roles_decorator_1.Roles)('ADMIN', 'USER'),
    (0, permissions_decorator_1.Permissions)('order:create'),
    (0, swagger_1.ApiOperation)({ summary: 'Create multiple orders with relations' }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.CREATED,
        description: 'The orders have been successfully created.',
        type: api_response_dto_1.ApiResponseDto,
        schema: {
            properties: {
                data: {
                    type: 'array',
                    items: { $ref: '#/components/schemas/OrderBookDto' },
                },
            },
        },
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.BAD_REQUEST,
        description: 'Invalid input data.',
        type: api_response_dto_1.ApiResponseDto,
        schema: { properties: { data: { type: 'object', nullable: true } } },
    }),
    (0, swagger_1.ApiBody)({ type: [create_order_book_dto_1.CreateOrderBookDto] }),
    openapi.ApiResponse({ status: 201, type: [require("./dto/order-book.dto").OrderBookDto] }),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, get_user_decorator_1.GetUser)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Array, String]),
    __metadata("design:returntype", Promise)
], OrderBookController.prototype, "bulkCreate", null);
__decorate([
    (0, common_1.Get)(),
    (0, roles_decorator_1.Roles)('ADMIN', 'USER'),
    (0, permissions_decorator_1.Permissions)('order:read'),
    (0, swagger_1.ApiOperation)({
        summary: 'Get all orders with relations, filter, sort and pagination',
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'List of orders retrieved successfully.',
        type: api_response_dto_1.ApiResponseDto,
        schema: {
            properties: {
                data: {
                    type: 'array',
                    items: { $ref: '#/components/schemas/OrderBookDto' },
                },
            },
        },
    }),
    (0, swagger_1.ApiQuery)({
        name: 'limit',
        required: false,
        description: 'Limit the number of results',
    }),
    (0, swagger_1.ApiQuery)({
        name: 'page',
        required: false,
        description: 'Page number for pagination',
    }),
    (0, swagger_1.ApiQuery)({
        name: 'sortBy',
        required: false,
        description: 'Field to sort by (e.g., createdAt, price)',
    }),
    (0, swagger_1.ApiQuery)({
        name: 'sortOrder',
        required: false,
        enum: ['ASC', 'DESC'],
        description: 'Sort order',
    }),
    (0, swagger_1.ApiQuery)({
        name: 'search',
        required: false,
        description: 'Search term to filter results',
    }),
    (0, swagger_1.ApiQuery)({
        name: 'relations',
        required: false,
        description: 'Relations to load (e.g., user,token)',
    }),
    (0, swagger_1.ApiQuery)({
        name: 'filter',
        required: false,
        description: 'Filter by field (e.g., businessType:SHORT_INVESTMENT)',
    }),
    openapi.ApiResponse({ status: 200 }),
    __param(0, (0, get_user_decorator_1.GetUser)()),
    __param(1, (0, common_1.Query)('limit')),
    __param(2, (0, common_1.Query)('page')),
    __param(3, (0, common_1.Query)('sortBy')),
    __param(4, (0, common_1.Query)('sortOrder')),
    __param(5, (0, common_1.Query)('search')),
    __param(6, (0, common_1.Query)('relations')),
    __param(7, (0, common_1.Query)('filter')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object, Object, String, String, String, String, String]),
    __metadata("design:returntype", Promise)
], OrderBookController.prototype, "findAll", null);
__decorate([
    (0, common_1.Get)('search'),
    (0, roles_decorator_1.Roles)('ADMIN', 'USER'),
    (0, permissions_decorator_1.Permissions)('order:read'),
    (0, swagger_1.ApiOperation)({ summary: 'Search orders by keyword' }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'List of orders matching the keyword.',
        type: api_response_dto_1.ApiResponseDto,
        schema: {
            properties: {
                data: {
                    type: 'array',
                    items: { $ref: '#/components/schemas/OrderBookDto' },
                },
            },
        },
    }),
    (0, swagger_1.ApiQuery)({
        name: 'keyword',
        required: true,
        description: 'Keyword to search across fields',
    }),
    (0, swagger_1.ApiQuery)({
        name: 'limit',
        required: false,
        description: 'Limit the number of results',
    }),
    (0, swagger_1.ApiQuery)({
        name: 'page',
        required: false,
        description: 'Page number for pagination',
    }),
    (0, swagger_1.ApiQuery)({
        name: 'sortBy',
        required: false,
        description: 'Field to sort by (e.g., createdAt, price)',
    }),
    (0, swagger_1.ApiQuery)({
        name: 'sortOrder',
        required: false,
        enum: ['ASC', 'DESC'],
        description: 'Sort order',
    }),
    openapi.ApiResponse({ status: 200 }),
    __param(0, (0, get_user_decorator_1.GetUser)()),
    __param(1, (0, common_1.Query)('keyword')),
    __param(2, (0, common_1.Query)('limit')),
    __param(3, (0, common_1.Query)('page')),
    __param(4, (0, common_1.Query)('sortBy')),
    __param(5, (0, common_1.Query)('sortOrder')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [user_entity_1.User, String, Object, Object, String, String]),
    __metadata("design:returntype", Promise)
], OrderBookController.prototype, "search", null);
__decorate([
    (0, common_1.Get)('statistics'),
    (0, roles_decorator_1.Roles)('ADMIN', 'USER'),
    (0, permissions_decorator_1.Permissions)('order:read'),
    (0, swagger_1.ApiOperation)({ summary: 'Get order books statistics' }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Order books statistics.',
        type: api_response_dto_1.ApiResponseDto,
        schema: {
            properties: {
                data: {
                    type: 'object',
                    properties: {
                        total: { type: 'number' },
                        businessTypeCounts: {
                            type: 'object',
                            properties: {
                                NORMAL: { type: 'number' },
                                IMMEDIATE_DELIVERY: { type: 'number' },
                            },
                        },
                    },
                },
            },
        },
    }),
    openapi.ApiResponse({ status: 200 }),
    __param(0, (0, get_user_decorator_1.GetUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], OrderBookController.prototype, "getStatistics", null);
__decorate([
    (0, common_1.Get)('count'),
    (0, roles_decorator_1.Roles)('ADMIN', 'USER'),
    (0, permissions_decorator_1.Permissions)('order:read'),
    (0, swagger_1.ApiOperation)({ summary: 'Count orders' }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Number of orders.',
        type: api_response_dto_1.ApiResponseDto,
        schema: { properties: { data: { type: 'number' } } },
    }),
    (0, swagger_1.ApiQuery)({
        name: 'filter',
        required: false,
        description: 'Filter by field (e.g., status:PENDING)',
    }),
    openapi.ApiResponse({ status: 200, type: Number }),
    __param(0, (0, get_user_decorator_1.GetUser)()),
    __param(1, (0, common_1.Query)('filter')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [user_entity_1.User, String]),
    __metadata("design:returntype", Promise)
], OrderBookController.prototype, "count", null);
__decorate([
    (0, common_1.Get)('export'),
    (0, roles_decorator_1.Roles)('ADMIN', 'USER'),
    (0, permissions_decorator_1.Permissions)('order:read'),
    (0, swagger_1.ApiOperation)({ summary: 'Export orders to XLSX file' }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Data exported successfully.',
        schema: {
            type: 'string',
            format: 'binary',
        },
    }),
    openapi.ApiResponse({ status: 200 }),
    __param(0, (0, common_1.Res)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], OrderBookController.prototype, "export", null);
__decorate([
    (0, common_1.Get)(':id'),
    (0, roles_decorator_1.Roles)('ADMIN', 'USER'),
    (0, permissions_decorator_1.Permissions)('order:read'),
    (0, swagger_1.ApiOperation)({ summary: 'Get an order by ID with relations' }),
    (0, swagger_1.ApiParam)({ name: 'id', description: 'The ID of the order to retrieve' }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'The order retrieved successfully.',
        type: api_response_dto_1.ApiResponseDto,
        schema: {
            properties: { data: { $ref: '#/components/schemas/OrderBookDto' } },
        },
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.NOT_FOUND,
        description: 'Order not found.',
        type: api_response_dto_1.ApiResponseDto,
        schema: { properties: { data: { type: 'object', nullable: true } } },
    }),
    (0, swagger_1.ApiQuery)({
        name: 'relations',
        required: false,
        description: 'Relations to load (e.g., user,token)',
    }),
    openapi.ApiResponse({ status: 200, type: require("./dto/order-book.dto").OrderBookDto }),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], OrderBookController.prototype, "findOne", null);
__decorate([
    (0, common_1.Post)(':id/duplicate'),
    (0, roles_decorator_1.Roles)('ADMIN', 'USER'),
    (0, permissions_decorator_1.Permissions)('order:create'),
    (0, swagger_1.ApiOperation)({ summary: 'Duplicate an order' }),
    (0, swagger_1.ApiParam)({ name: 'id', description: 'The ID of the order to duplicate' }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.CREATED,
        description: 'The order has been duplicated.',
        type: api_response_dto_1.ApiResponseDto,
        schema: {
            properties: { data: { $ref: '#/components/schemas/OrderBookDto' } },
        },
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.NOT_FOUND,
        description: 'Order not found.',
        type: api_response_dto_1.ApiResponseDto,
        schema: { properties: { data: { type: 'object', nullable: true } } },
    }),
    openapi.ApiResponse({ status: 201, type: require("./dto/order-book.dto").OrderBookDto }),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, get_user_decorator_1.GetUser)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String]),
    __metadata("design:returntype", Promise)
], OrderBookController.prototype, "duplicate", null);
__decorate([
    (0, common_1.Put)(':id'),
    (0, roles_decorator_1.Roles)('ADMIN'),
    (0, permissions_decorator_1.Permissions)('order:update'),
    (0, swagger_1.ApiOperation)({ summary: 'Update an order with relations' }),
    (0, swagger_1.ApiParam)({ name: 'id', description: 'The ID of the order to update' }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'The order has been successfully updated.',
        type: api_response_dto_1.ApiResponseDto,
        schema: {
            properties: { data: { $ref: '#/components/schemas/OrderBookDto' } },
        },
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.NOT_FOUND,
        description: 'Order not found.',
        type: api_response_dto_1.ApiResponseDto,
        schema: { properties: { data: { type: 'object', nullable: true } } },
    }),
    (0, swagger_1.ApiBody)({ type: update_order_book_dto_1.UpdateOrderBookDto }),
    openapi.ApiResponse({ status: 200, type: require("./dto/order-book.dto").OrderBookDto }),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __param(2, (0, get_user_decorator_1.GetUser)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, update_order_book_dto_1.UpdateOrderBookDto, String]),
    __metadata("design:returntype", Promise)
], OrderBookController.prototype, "update", null);
__decorate([
    (0, common_1.Put)('bulk'),
    (0, roles_decorator_1.Roles)('ADMIN'),
    (0, permissions_decorator_1.Permissions)('order:update'),
    (0, swagger_1.ApiOperation)({ summary: 'Update multiple orders with relations' }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'The orders have been successfully updated.',
        type: api_response_dto_1.ApiResponseDto,
        schema: {
            properties: {
                data: {
                    type: 'array',
                    items: { $ref: '#/components/schemas/OrderBookDto' },
                },
            },
        },
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.BAD_REQUEST,
        description: 'Invalid input data.',
        type: api_response_dto_1.ApiResponseDto,
        schema: { properties: { data: { type: 'object', nullable: true } } },
    }),
    (0, swagger_1.ApiBody)({ type: [update_order_book_dto_1.UpdateOrderBookDto] }),
    openapi.ApiResponse({ status: 200, type: [require("./dto/order-book.dto").OrderBookDto] }),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, get_user_decorator_1.GetUser)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Array, String]),
    __metadata("design:returntype", Promise)
], OrderBookController.prototype, "bulkUpdate", null);
__decorate([
    (0, common_1.Put)(':id/toggle-status'),
    (0, roles_decorator_1.Roles)('ADMIN'),
    (0, permissions_decorator_1.Permissions)('order:update'),
    (0, swagger_1.ApiOperation)({ summary: 'Toggle order status' }),
    (0, swagger_1.ApiParam)({ name: 'id', description: 'The ID of the order to toggle status' }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'The order status has been successfully toggled.',
        type: api_response_dto_1.ApiResponseDto,
        schema: {
            properties: { data: { $ref: '#/components/schemas/OrderBookDto' } },
        },
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.NOT_FOUND,
        description: 'Order not found.',
        type: api_response_dto_1.ApiResponseDto,
        schema: { properties: { data: { type: 'object', nullable: true } } },
    }),
    openapi.ApiResponse({ status: 200, type: require("./dto/order-book.dto").OrderBookDto }),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, get_user_decorator_1.GetUser)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String]),
    __metadata("design:returntype", Promise)
], OrderBookController.prototype, "toggleStatus", null);
__decorate([
    (0, common_1.Delete)(':id'),
    (0, roles_decorator_1.Roles)('ADMIN'),
    (0, permissions_decorator_1.Permissions)('order:delete'),
    (0, swagger_1.ApiOperation)({ summary: 'Delete an order' }),
    (0, swagger_1.ApiParam)({ name: 'id', description: 'The ID of the order to delete' }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'The order has been successfully deleted.',
        type: api_response_dto_1.ApiResponseDto,
        schema: { properties: { data: { type: 'object', nullable: true } } },
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.NOT_FOUND,
        description: 'Order not found.',
        type: api_response_dto_1.ApiResponseDto,
        schema: { properties: { data: { type: 'object', nullable: true } } },
    }),
    openapi.ApiResponse({ status: 200 }),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], OrderBookController.prototype, "remove", null);
__decorate([
    (0, common_1.Delete)('bulk'),
    (0, roles_decorator_1.Roles)('ADMIN'),
    (0, permissions_decorator_1.Permissions)('order:delete'),
    (0, swagger_1.ApiOperation)({ summary: 'Delete multiple orders' }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'The orders have been successfully deleted.',
        type: api_response_dto_1.ApiResponseDto,
        schema: { properties: { data: { type: 'object', nullable: true } } },
    }),
    (0, swagger_1.ApiBody)({ type: [String], description: 'Array of order IDs to delete' }),
    openapi.ApiResponse({ status: 200 }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Array]),
    __metadata("design:returntype", Promise)
], OrderBookController.prototype, "bulkDelete", null);
__decorate([
    (0, common_1.Put)(':id/soft-delete'),
    (0, roles_decorator_1.Roles)('ADMIN'),
    (0, permissions_decorator_1.Permissions)('order:delete'),
    (0, swagger_1.ApiOperation)({ summary: 'Soft delete an order by ID' }),
    (0, swagger_1.ApiParam)({ name: 'id', description: 'The ID of the order to soft delete' }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'The order has been soft deleted.',
        type: api_response_dto_1.ApiResponseDto,
        schema: {
            properties: { data: { $ref: '#/components/schemas/OrderBookDto' } },
        },
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.NOT_FOUND,
        description: 'Order not found.',
        type: api_response_dto_1.ApiResponseDto,
        schema: { properties: { data: { type: 'object', nullable: true } } },
    }),
    openapi.ApiResponse({ status: 200, type: require("./dto/order-book.dto").OrderBookDto }),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, get_user_decorator_1.GetUser)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String]),
    __metadata("design:returntype", Promise)
], OrderBookController.prototype, "softDelete", null);
__decorate([
    (0, common_1.Put)('bulk/soft-delete'),
    (0, roles_decorator_1.Roles)('ADMIN'),
    (0, permissions_decorator_1.Permissions)('order:delete'),
    (0, swagger_1.ApiOperation)({ summary: 'Soft delete multiple orders' }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'The orders have been soft deleted.',
        type: api_response_dto_1.ApiResponseDto,
        schema: {
            properties: {
                data: {
                    type: 'array',
                    items: { $ref: '#/components/schemas/OrderBookDto' },
                },
            },
        },
    }),
    (0, swagger_1.ApiBody)({ type: [String], description: 'Array of order IDs to soft delete' }),
    openapi.ApiResponse({ status: 200 }),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, get_user_decorator_1.GetUser)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Array, String]),
    __metadata("design:returntype", Promise)
], OrderBookController.prototype, "bulkSoftDelete", null);
__decorate([
    (0, common_1.Get)('deleted'),
    (0, roles_decorator_1.Roles)('ADMIN'),
    (0, permissions_decorator_1.Permissions)('order:read'),
    (0, swagger_1.ApiOperation)({ summary: 'Get all soft-deleted orders' }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'List of soft-deleted orders retrieved successfully.',
        type: api_response_dto_1.ApiResponseDto,
        schema: {
            properties: {
                data: {
                    type: 'array',
                    items: { $ref: '#/components/schemas/OrderBookDto' },
                },
            },
        },
    }),
    (0, swagger_1.ApiQuery)({
        name: 'limit',
        required: false,
        description: 'Limit the number of results',
    }),
    (0, swagger_1.ApiQuery)({
        name: 'page',
        required: false,
        description: 'Page number for pagination',
    }),
    (0, swagger_1.ApiQuery)({
        name: 'sortBy',
        required: false,
        description: 'Field to sort by (e.g., updatedAt)',
    }),
    (0, swagger_1.ApiQuery)({
        name: 'sortOrder',
        required: false,
        enum: ['ASC', 'DESC'],
        description: 'Sort order',
    }),
    openapi.ApiResponse({ status: 200 }),
    __param(0, (0, common_1.Query)('limit')),
    __param(1, (0, common_1.Query)('page')),
    __param(2, (0, common_1.Query)('sortBy')),
    __param(3, (0, common_1.Query)('sortOrder')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object, String, String]),
    __metadata("design:returntype", Promise)
], OrderBookController.prototype, "findDeleted", null);
__decorate([
    (0, common_1.Put)(':id/restore'),
    (0, roles_decorator_1.Roles)('ADMIN'),
    (0, permissions_decorator_1.Permissions)('order:update'),
    (0, swagger_1.ApiOperation)({ summary: 'Restore a soft-deleted order' }),
    (0, swagger_1.ApiParam)({ name: 'id', description: 'The ID of the order to restore' }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'The order has been restored.',
        type: api_response_dto_1.ApiResponseDto,
        schema: {
            properties: { data: { $ref: '#/components/schemas/OrderBookDto' } },
        },
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.NOT_FOUND,
        description: 'Order not found.',
        type: api_response_dto_1.ApiResponseDto,
        schema: { properties: { data: { type: 'object', nullable: true } } },
    }),
    openapi.ApiResponse({ status: 200, type: require("./dto/order-book.dto").OrderBookDto }),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, get_user_decorator_1.GetUser)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String]),
    __metadata("design:returntype", Promise)
], OrderBookController.prototype, "restore", null);
__decorate([
    (0, common_1.Patch)(':id/status/pending'),
    (0, roles_decorator_1.Roles)('ADMIN'),
    (0, permissions_decorator_1.Permissions)('order:update'),
    (0, swagger_1.ApiOperation)({
        summary: 'Cập nhật trạng thái lệnh thành PENDING (Chờ xử lý)',
    }),
    (0, swagger_1.ApiParam)({ name: 'id', description: 'ID của lệnh' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Cập nhật trạng thái thành công' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Không tìm thấy lệnh' }),
    (0, swagger_1.ApiResponse)({ status: 500, description: 'Lỗi server' }),
    openapi.ApiResponse({ status: 200, type: require("./dto/order-book.dto").OrderBookDto }),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, get_user_decorator_1.GetUser)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String]),
    __metadata("design:returntype", Promise)
], OrderBookController.prototype, "updateStatusPending", null);
__decorate([
    (0, common_1.Patch)(':id/status/completed'),
    (0, roles_decorator_1.Roles)('ADMIN'),
    (0, permissions_decorator_1.Permissions)('order:update'),
    (0, swagger_1.ApiOperation)({
        summary: 'Cập nhật trạng thái lệnh thành COMPLETED (Đã tất toán) và tất toán hợp đồng',
    }),
    (0, swagger_1.ApiParam)({ name: 'id', description: 'ID của lệnh' }),
    (0, swagger_1.ApiBody)({
        type: settlement_order_book_dto_1.SettlementOrderBookDto,
        required: false,
        description: 'Dữ liệu tất toán (giá mới, lợi nhuận)',
    }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Cập nhật trạng thái thành công' }),
    (0, swagger_1.ApiResponse)({ status: 400, description: 'Số dư không đủ để thanh toán' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Không tìm thấy lệnh' }),
    (0, swagger_1.ApiResponse)({ status: 500, description: 'Lỗi server' }),
    openapi.ApiResponse({ status: 200, type: require("./dto/order-book.dto").OrderBookDto }),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __param(2, (0, get_user_decorator_1.GetUser)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, settlement_order_book_dto_1.SettlementOrderBookDto, String]),
    __metadata("design:returntype", Promise)
], OrderBookController.prototype, "updateStatusCompleted", null);
__decorate([
    (0, common_1.Patch)(':id/status/wait-payment'),
    (0, roles_decorator_1.Roles)('ADMIN'),
    (0, permissions_decorator_1.Permissions)('order:update'),
    (0, swagger_1.ApiOperation)({
        summary: 'Cập nhật trạng thái lệnh thành WAIT_PAYMENT (Chờ thanh toán)',
    }),
    (0, swagger_1.ApiParam)({ name: 'id', description: 'ID của lệnh' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Cập nhật trạng thái thành công' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Không tìm thấy lệnh' }),
    (0, swagger_1.ApiResponse)({ status: 500, description: 'Lỗi server' }),
    openapi.ApiResponse({ status: 200, type: require("./dto/order-book.dto").OrderBookDto }),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, get_user_decorator_1.GetUser)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String]),
    __metadata("design:returntype", Promise)
], OrderBookController.prototype, "updateStatusWaitPayment", null);
__decorate([
    (0, common_1.Get)('my-orders'),
    (0, roles_decorator_1.Roles)('USER'),
    (0, permissions_decorator_1.Permissions)('order:read'),
    (0, swagger_1.ApiOperation)({
        summary: 'Get all orders of the current user with pagination',
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'List of user orders retrieved successfully.',
        type: api_response_dto_1.ApiResponseDto,
        schema: {
            properties: {
                data: {
                    type: 'array',
                    items: { $ref: '#/components/schemas/OrderBookDto' },
                },
            },
        },
    }),
    (0, swagger_1.ApiQuery)({
        name: 'limit',
        required: false,
        description: 'Limit the number of results',
    }),
    (0, swagger_1.ApiQuery)({
        name: 'page',
        required: false,
        description: 'Page number for pagination',
    }),
    (0, swagger_1.ApiQuery)({
        name: 'sortBy',
        required: false,
        description: 'Field to sort by (e.g., createdAt, price)',
    }),
    (0, swagger_1.ApiQuery)({
        name: 'sortOrder',
        required: false,
        enum: ['ASC', 'DESC'],
        description: 'Sort order',
    }),
    (0, swagger_1.ApiQuery)({
        name: 'search',
        required: false,
        description: 'Search term to filter results',
    }),
    (0, swagger_1.ApiQuery)({
        name: 'status',
        required: false,
        enum: Object.values(order_status_enum_1.OrderStatus),
        description: 'Filter by order status',
    }),
    (0, swagger_1.ApiQuery)({
        name: 'relations',
        required: false,
        description: 'Relations to load (e.g., user,token)',
    }),
    openapi.ApiResponse({ status: 200 }),
    __param(0, (0, get_user_decorator_1.GetUser)()),
    __param(1, (0, common_1.Query)('limit')),
    __param(2, (0, common_1.Query)('page')),
    __param(3, (0, common_1.Query)('sortBy')),
    __param(4, (0, common_1.Query)('sortOrder')),
    __param(5, (0, common_1.Query)('search')),
    __param(6, (0, common_1.Query)('status')),
    __param(7, (0, common_1.Query)('relations')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [user_entity_1.User, Object, Object, String, String, String, String, String]),
    __metadata("design:returntype", Promise)
], OrderBookController.prototype, "findMyOrders", null);
__decorate([
    (0, common_1.Post)('token-withdrawal'),
    (0, roles_decorator_1.Roles)('ADMIN', 'USER'),
    (0, permissions_decorator_1.Permissions)('order:create'),
    (0, swagger_1.ApiOperation)({ summary: 'Tạo lệnh rút token' }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.CREATED,
        description: 'Lệnh rút token đã được tạo thành công.',
        type: api_response_dto_1.ApiResponseDto,
        schema: {
            properties: { data: { $ref: '#/components/schemas/OrderBookDto' } },
        },
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.BAD_REQUEST,
        description: 'Dữ liệu đầu vào không hợp lệ hoặc số lượng token không đủ.',
        type: api_response_dto_1.ApiResponseDto,
        schema: { properties: { data: { type: 'object', nullable: true } } },
    }),
    (0, swagger_1.ApiBody)({ type: create_order_book_dto_1.CreateOrderBookDto }),
    openapi.ApiResponse({ status: 201, type: require("./dto/order-book.dto").OrderBookDto }),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, get_user_decorator_1.GetUser)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [create_order_book_dto_1.CreateOrderBookDto, String]),
    __metadata("design:returntype", Promise)
], OrderBookController.prototype, "createTokenWithdrawal", null);
__decorate([
    (0, common_1.Patch)(':id/status/cancelled'),
    (0, roles_decorator_1.Roles)('ADMIN'),
    (0, permissions_decorator_1.Permissions)('order:update'),
    (0, swagger_1.ApiOperation)({ summary: 'Cập nhật trạng thái lệnh rút token thành ĐÃ HỦY' }),
    (0, swagger_1.ApiParam)({ name: 'id', description: 'ID của lệnh' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Cập nhật trạng thái thành công' }),
    (0, swagger_1.ApiResponse)({
        status: 400,
        description: 'Lệnh không phải là lệnh rút token hoặc không ở trạng thái CHỜ RÚT',
    }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Không tìm thấy lệnh' }),
    (0, swagger_1.ApiResponse)({ status: 500, description: 'Lỗi server' }),
    openapi.ApiResponse({ status: 200, type: require("./dto/order-book.dto").OrderBookDto }),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, get_user_decorator_1.GetUser)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String]),
    __metadata("design:returntype", Promise)
], OrderBookController.prototype, "updateStatusCancelled", null);
__decorate([
    (0, common_1.Patch)(':id/approve'),
    (0, roles_decorator_1.Roles)('ADMIN'),
    (0, permissions_decorator_1.Permissions)('order:update'),
    (0, swagger_1.ApiOperation)({
        summary: 'Phê duyệt lệnh bạc vật chất (chuyển từ WAIT_APPROVE sang APPROVED)',
    }),
    (0, swagger_1.ApiParam)({ name: 'id', description: 'ID của lệnh' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Phê duyệt lệnh thành công' }),
    (0, swagger_1.ApiResponse)({
        status: 400,
        description: 'Lệnh không ở trạng thái chờ phê duyệt',
    }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Không tìm thấy lệnh' }),
    (0, swagger_1.ApiResponse)({ status: 500, description: 'Lỗi server' }),
    openapi.ApiResponse({ status: 200, type: require("./dto/order-book.dto").OrderBookDto }),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, get_user_decorator_1.GetUser)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String]),
    __metadata("design:returntype", Promise)
], OrderBookController.prototype, "approveOrder", null);
__decorate([
    (0, common_1.Patch)(':id/extend-settlement'),
    (0, roles_decorator_1.Roles)('ADMIN', 'USER'),
    (0, permissions_decorator_1.Permissions)('order:update'),
    (0, swagger_1.ApiOperation)({ summary: 'Gia hạn thời gian tất toán thêm 15 ngày' }),
    (0, swagger_1.ApiParam)({ name: 'id', description: 'ID của lệnh cần gia hạn' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Gia hạn thành công' }),
    (0, swagger_1.ApiResponse)({
        status: 400,
        description: 'Lệnh không thể gia hạn hoặc số dư không đủ',
    }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Không tìm thấy lệnh' }),
    (0, swagger_1.ApiResponse)({ status: 500, description: 'Lỗi server' }),
    openapi.ApiResponse({ status: 200, type: require("./dto/order-book.dto").OrderBookDto }),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, get_user_decorator_1.GetUser)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String]),
    __metadata("design:returntype", Promise)
], OrderBookController.prototype, "extendSettlement", null);
exports.OrderBookController = OrderBookController = OrderBookController_1 = __decorate([
    (0, swagger_1.ApiTags)('order-books'),
    (0, common_1.Controller)('order-books'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    __metadata("design:paramtypes", [order_book_service_1.OrderBookService])
], OrderBookController);
//# sourceMappingURL=order-book.controller.js.map
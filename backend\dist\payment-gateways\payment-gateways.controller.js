"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var PaymentGatewaysController_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.PaymentGatewaysController = void 0;
const openapi = require("@nestjs/swagger");
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const payment_gateways_service_1 = require("./payment-gateways.service");
const create_payment_dto_1 = require("./dto/create-payment.dto");
const payment_response_dto_1 = require("./dto/payment-response.dto");
const get_user_decorator_1 = require("../common/decorators/get-user.decorator");
const public_decorator_1 = require("../common/decorators/public.decorator");
let PaymentGatewaysController = PaymentGatewaysController_1 = class PaymentGatewaysController {
    paymentGatewaysService;
    logger = new common_1.Logger(PaymentGatewaysController_1.name);
    constructor(paymentGatewaysService) {
        this.paymentGatewaysService = paymentGatewaysService;
    }
    async createPayment(createPaymentDto, userId, req) {
        createPaymentDto.userId = userId;
        createPaymentDto.ipAddress = req.ip || req.socket.remoteAddress;
        return this.paymentGatewaysService.createPayment(createPaymentDto);
    }
    async handleVnpayReturn(query, res) {
        try {
            this.logger.log(`Nhận callback từ VNPAY: ${JSON.stringify(query)}`);
            const result = await this.paymentGatewaysService.handleVnpayCallback(query);
            const frontendUrl = process.env.FRONTEND_URL || 'http://localhost:3188';
            let redirectUrl;
            if (result.isValid) {
                redirectUrl = `${frontendUrl}/payment/success?amount=${result.amount || 0}&transactionId=${result.transactionId || ''}`;
            }
            else {
                redirectUrl = `${frontendUrl}/payment/failed?message=${encodeURIComponent(result.message || 'Thanh toán thất bại')}`;
            }
            res.redirect(redirectUrl);
        }
        catch (error) {
            this.logger.error(`Lỗi khi xử lý callback VNPAY: ${error.message}`, error.stack);
            const frontendUrl = process.env.FRONTEND_URL || 'http://localhost:3188';
            res.redirect(`${frontendUrl}/payment/failed?message=${encodeURIComponent('Đã xảy ra lỗi khi xử lý thanh toán')}`);
        }
    }
    async handleMomoReturn(query, res) {
        try {
            this.logger.log(`Nhận callback từ MOMO: ${JSON.stringify(query)}`);
            const result = await this.paymentGatewaysService.handleMomoCallback(query);
            const frontendUrl = process.env.FRONTEND_URL || 'http://localhost:3188';
            let redirectUrl;
            if (result.isValid) {
                redirectUrl = `${frontendUrl}/payment/success?amount=${result.amount || 0}&transactionId=${result.transactionId || ''}`;
            }
            else {
                redirectUrl = `${frontendUrl}/payment/failed?message=${encodeURIComponent(result.message || 'Thanh toán thất bại')}`;
            }
            res.redirect(redirectUrl);
        }
        catch (error) {
            this.logger.error(`Lỗi khi xử lý callback MOMO: ${error.message}`, error.stack);
            const frontendUrl = process.env.FRONTEND_URL || 'http://localhost:3188';
            res.redirect(`${frontendUrl}/payment/failed?message=${encodeURIComponent('Đã xảy ra lỗi khi xử lý thanh toán')}`);
        }
    }
    async handleVnpayIpn(body, res) {
        try {
            this.logger.log(`Nhận IPN từ VNPAY: ${JSON.stringify(body)}`);
            const result = await this.paymentGatewaysService.handleVnpayIpn(body);
            res.status(common_1.HttpStatus.OK).json({
                RspCode: result.RspCode,
                Message: result.Message,
            });
        }
        catch (error) {
            this.logger.error(`Lỗi khi xử lý IPN VNPAY: ${error.message}`, error.stack);
            res.status(common_1.HttpStatus.OK).json({
                RspCode: '99',
                Message: 'Unknow error',
            });
        }
    }
    async handleMomoIpn(body, res) {
        try {
            this.logger.log(`Nhận IPN từ MOMO: ${JSON.stringify(body)}`);
            const result = await this.paymentGatewaysService.handleMomoIpn(body);
            res.status(common_1.HttpStatus.OK).json({
                status: result.status,
                message: result.message,
            });
        }
        catch (error) {
            this.logger.error(`Lỗi khi xử lý IPN MOMO: ${error.message}`, error.stack);
            res.status(common_1.HttpStatus.OK).json({
                status: 1,
                message: 'Unknow error',
            });
        }
    }
};
exports.PaymentGatewaysController = PaymentGatewaysController;
__decorate([
    (0, common_1.Post)('create-payment'),
    (0, swagger_1.ApiOperation)({ summary: 'Tạo yêu cầu thanh toán' }),
    (0, swagger_1.ApiResponse)({ status: 201, description: 'Tạo yêu cầu thanh toán thành công', type: payment_response_dto_1.PaymentResponseDto }),
    (0, swagger_1.ApiBody)({ type: create_payment_dto_1.CreatePaymentDto }),
    openapi.ApiResponse({ status: 201, type: require("./dto/payment-response.dto").PaymentResponseDto }),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, get_user_decorator_1.GetUser)('id')),
    __param(2, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [create_payment_dto_1.CreatePaymentDto, String, Object]),
    __metadata("design:returntype", Promise)
], PaymentGatewaysController.prototype, "createPayment", null);
__decorate([
    (0, public_decorator_1.Public)(),
    (0, common_1.Get)('vnpay/return'),
    (0, swagger_1.ApiOperation)({ summary: 'Xử lý callback từ VNPAY' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Xử lý callback thành công' }),
    openapi.ApiResponse({ status: 200 }),
    __param(0, (0, common_1.Query)()),
    __param(1, (0, common_1.Res)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", Promise)
], PaymentGatewaysController.prototype, "handleVnpayReturn", null);
__decorate([
    (0, public_decorator_1.Public)(),
    (0, common_1.Get)('momo/return'),
    (0, swagger_1.ApiOperation)({ summary: 'Xử lý callback từ MOMO' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Xử lý callback thành công' }),
    openapi.ApiResponse({ status: 200 }),
    __param(0, (0, common_1.Query)()),
    __param(1, (0, common_1.Res)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", Promise)
], PaymentGatewaysController.prototype, "handleMomoReturn", null);
__decorate([
    (0, public_decorator_1.Public)(),
    (0, common_1.Post)('vnpay/ipn'),
    (0, swagger_1.ApiOperation)({ summary: 'Xử lý IPN từ VNPAY' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Xử lý IPN thành công' }),
    openapi.ApiResponse({ status: 201 }),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, common_1.Res)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", Promise)
], PaymentGatewaysController.prototype, "handleVnpayIpn", null);
__decorate([
    (0, public_decorator_1.Public)(),
    (0, common_1.Post)('momo/ipn'),
    (0, swagger_1.ApiOperation)({ summary: 'Xử lý IPN từ MOMO' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Xử lý IPN thành công' }),
    openapi.ApiResponse({ status: 201 }),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, common_1.Res)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", Promise)
], PaymentGatewaysController.prototype, "handleMomoIpn", null);
exports.PaymentGatewaysController = PaymentGatewaysController = PaymentGatewaysController_1 = __decorate([
    (0, swagger_1.ApiTags)('payment-gateways'),
    (0, common_1.Controller)('payment-gateways'),
    __metadata("design:paramtypes", [payment_gateways_service_1.PaymentGatewaysService])
], PaymentGatewaysController);
//# sourceMappingURL=payment-gateways.controller.js.map
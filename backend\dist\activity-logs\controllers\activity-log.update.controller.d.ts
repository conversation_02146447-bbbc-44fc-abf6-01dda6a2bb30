import { UpdateActivityLogService } from '../services/update.activity-log.service';
import { ActivityLogDto } from '../dto/activity-log.dto';
import { UpdateActivityLogDto } from '../dto/update-activity-log.dto';
export declare class ActivityLogUpdateController {
    private readonly activityLogService;
    private readonly logger;
    constructor(activityLogService: UpdateActivityLogService);
    update(id: string, updateActivityLogDto: UpdateActivityLogDto, userId: string): Promise<ActivityLogDto>;
    bulkUpdate(updateActivityLogDtos: UpdateActivityLogDto[], userId: string): Promise<ActivityLogDto[]>;
}

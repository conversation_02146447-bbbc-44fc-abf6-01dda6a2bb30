"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.EcomProductDto = void 0;
const openapi = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
const swagger_1 = require("@nestjs/swagger");
const class_transformer_1 = require("class-transformer");
const user_dto_1 = require("../../users/dto/user.dto");
const ecom_product_category_dto_1 = require("../../ecom-product-categories/dto/ecom-product-category.dto");
class EcomProductDto {
    id;
    productCode;
    productName;
    categoryId;
    description;
    weight;
    imageUrl;
    regularPrice;
    salePrice;
    stockQuantity;
    isActive;
    slug;
    isDeleted;
    createdAt;
    updatedAt;
    createdBy;
    updatedBy;
    deletedAt;
    deletedBy;
    category;
    creator;
    updater;
    deleter;
    static _OPENAPI_METADATA_FACTORY() {
        return { id: { required: true, type: () => String, format: "uuid" }, productCode: { required: true, type: () => String }, productName: { required: true, type: () => String }, categoryId: { required: true, type: () => String, format: "uuid" }, description: { required: false, type: () => String }, weight: { required: false, type: () => Number }, imageUrl: { required: false, type: () => String }, regularPrice: { required: true, type: () => Number }, salePrice: { required: false, type: () => Number }, stockQuantity: { required: true, type: () => Number }, isActive: { required: true, type: () => Boolean }, slug: { required: false, type: () => String }, isDeleted: { required: true, type: () => Boolean }, createdAt: { required: true, type: () => Date }, updatedAt: { required: true, type: () => Date }, createdBy: { required: false, type: () => String, nullable: true, format: "uuid" }, updatedBy: { required: false, type: () => String, nullable: true, format: "uuid" }, deletedAt: { required: false, type: () => Date, nullable: true }, deletedBy: { required: false, type: () => String, nullable: true, format: "uuid" }, category: { required: true, type: () => require("../../ecom-product-categories/dto/ecom-product-category.dto").EcomProductCategoryDto }, creator: { required: false, type: () => require("../../users/dto/user.dto").UserDto, nullable: true }, updater: { required: false, type: () => require("../../users/dto/user.dto").UserDto, nullable: true }, deleter: { required: false, type: () => require("../../users/dto/user.dto").UserDto, nullable: true } };
    }
}
exports.EcomProductDto = EcomProductDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'ID của sản phẩm' }),
    (0, class_validator_1.IsUUID)(),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", String)
], EcomProductDto.prototype, "id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Mã sản phẩm' }),
    (0, class_validator_1.IsString)(),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", String)
], EcomProductDto.prototype, "productCode", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Tên sản phẩm' }),
    (0, class_validator_1.IsString)(),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", String)
], EcomProductDto.prototype, "productName", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'ID danh mục sản phẩm' }),
    (0, class_validator_1.IsUUID)(),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", String)
], EcomProductDto.prototype, "categoryId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Mô tả sản phẩm' }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", String)
], EcomProductDto.prototype, "description", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Trọng lượng (gram)' }),
    (0, class_validator_1.IsDecimal)(),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", Number)
], EcomProductDto.prototype, "weight", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'URL hình ảnh' }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", String)
], EcomProductDto.prototype, "imageUrl", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Giá bán thông thường' }),
    (0, class_validator_1.IsNumber)(),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", Number)
], EcomProductDto.prototype, "regularPrice", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Giá khuyến mãi' }),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", Number)
], EcomProductDto.prototype, "salePrice", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Số lượng tồn kho' }),
    (0, class_validator_1.IsNumber)(),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", Number)
], EcomProductDto.prototype, "stockQuantity", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Trạng thái hoạt động' }),
    (0, class_validator_1.IsBoolean)(),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", Boolean)
], EcomProductDto.prototype, "isActive", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Slug SEO-friendly từ tên sản phẩm' }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", String)
], EcomProductDto.prototype, "slug", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Trạng thái xóa' }),
    (0, class_validator_1.IsBoolean)(),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", Boolean)
], EcomProductDto.prototype, "isDeleted", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Thời gian tạo' }),
    (0, class_validator_1.IsDate)(),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", Date)
], EcomProductDto.prototype, "createdAt", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Thời gian cập nhật' }),
    (0, class_validator_1.IsDate)(),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", Date)
], EcomProductDto.prototype, "updatedAt", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'ID người tạo', required: false }),
    (0, class_validator_1.IsUUID)(),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", Object)
], EcomProductDto.prototype, "createdBy", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'ID người cập nhật', required: false }),
    (0, class_validator_1.IsUUID)(),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", Object)
], EcomProductDto.prototype, "updatedBy", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Thời gian xóa', required: false }),
    (0, class_validator_1.IsDate)(),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", Object)
], EcomProductDto.prototype, "deletedAt", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'ID người xóa', required: false }),
    (0, class_validator_1.IsUUID)(),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", Object)
], EcomProductDto.prototype, "deletedBy", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Danh mục sản phẩm',
        type: () => ecom_product_category_dto_1.EcomProductCategoryDto,
    }),
    (0, class_transformer_1.Expose)(),
    (0, class_transformer_1.Type)(() => ecom_product_category_dto_1.EcomProductCategoryDto),
    __metadata("design:type", ecom_product_category_dto_1.EcomProductCategoryDto)
], EcomProductDto.prototype, "category", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Người tạo sản phẩm',
        type: () => user_dto_1.UserDto,
        required: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Expose)(),
    (0, class_transformer_1.Type)(() => user_dto_1.UserDto),
    __metadata("design:type", Object)
], EcomProductDto.prototype, "creator", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Người cập nhật sản phẩm',
        type: () => user_dto_1.UserDto,
        required: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Expose)(),
    (0, class_transformer_1.Type)(() => user_dto_1.UserDto),
    __metadata("design:type", Object)
], EcomProductDto.prototype, "updater", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Người xóa sản phẩm',
        type: () => user_dto_1.UserDto,
        required: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Expose)(),
    (0, class_transformer_1.Type)(() => user_dto_1.UserDto),
    __metadata("design:type", Object)
], EcomProductDto.prototype, "deleter", void 0);
//# sourceMappingURL=ecom-product.dto.js.map
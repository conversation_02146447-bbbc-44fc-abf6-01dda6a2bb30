"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ReadCmsPagesService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const event_emitter_1 = require("@nestjs/event-emitter");
const base_cms_pages_service_1 = require("./base.cms-pages.service");
const cms_pages_entity_1 = require("../entity/cms-pages.entity");
let ReadCmsPagesService = class ReadCmsPagesService extends base_cms_pages_service_1.BaseCmsPagesService {
    pageRepository;
    dataSource;
    eventEmitter;
    constructor(pageRepository, dataSource, eventEmitter) {
        super(pageRepository, dataSource, eventEmitter);
        this.pageRepository = pageRepository;
        this.dataSource = dataSource;
        this.eventEmitter = eventEmitter;
    }
    async findAll(params) {
        try {
            this.logger.debug(`Đang tìm tất cả trang CMS với tham số: ${JSON.stringify(params)}`);
            const { limit, page, search } = params;
            const skip = (page - 1) * limit;
            let relationsArray = params.relationsArray || [];
            const requiredRelations = ['creator', 'updater', 'deleter'];
            requiredRelations.forEach(relation => {
                if (!relationsArray.includes(relation)) {
                    relationsArray.push(relation);
                }
            });
            const validatedRelations = this.validateRelations(relationsArray);
            const queryBuilder = this.pageRepository
                .createQueryBuilder('page')
                .where('page.isDeleted = :isDeleted', { isDeleted: false });
            if (search) {
                queryBuilder.andWhere(new typeorm_2.Brackets(qb => {
                    qb.where('LOWER(page.title) LIKE LOWER(:search)', { search: `%${search}%` })
                        .orWhere('LOWER(page.content) LIKE LOWER(:search)', { search: `%${search}%` })
                        .orWhere('LOWER(page.slug) LIKE LOWER(:search)', { search: `%${search}%` })
                        .orWhere('LOWER(page.metaTitle) LIKE LOWER(:search)', { search: `%${search}%` })
                        .orWhere('LOWER(page.metaDescription) LIKE LOWER(:search)', { search: `%${search}%` });
                }));
            }
            const sortOptions = params.sortOptions;
            if (sortOptions && sortOptions.length > 0) {
                const { field, order } = sortOptions[0];
                queryBuilder.orderBy(`page.${field}`, order);
            }
            else {
                queryBuilder.orderBy('page.createdAt', 'DESC');
            }
            validatedRelations.forEach(relation => {
                queryBuilder.leftJoinAndSelect(`page.${relation}`, relation);
            });
            queryBuilder.skip(skip).take(limit);
            const [pages, total] = await queryBuilder.getManyAndCount();
            const pageDtos = this.toDtos(pages);
            return {
                data: pageDtos,
                total,
            };
        }
        catch (error) {
            this.logger.error(`Lỗi khi tìm tất cả trang CMS: ${error.message}`, error.stack);
            if (error instanceof common_1.BadRequestException) {
                throw error;
            }
            throw new common_1.InternalServerErrorException(`Không thể lấy danh sách trang CMS: ${error.message}`);
        }
    }
    async findByStatus(status, params) {
        try {
            this.logger.debug(`Đang tìm trang CMS theo trạng thái: ${status}`);
            const queryBuilder = this.pageRepository
                .createQueryBuilder('page')
                .where('page.isDeleted = :isDeleted', { isDeleted: false })
                .andWhere('page.status = :status', { status });
            if (params.search) {
                queryBuilder.andWhere(new typeorm_2.Brackets(qb => {
                    qb.where('LOWER(page.title) LIKE LOWER(:search)', { search: `%${params.search}%` })
                        .orWhere('LOWER(page.content) LIKE LOWER(:search)', { search: `%${params.search}%` })
                        .orWhere('LOWER(page.slug) LIKE LOWER(:search)', { search: `%${params.search}%` });
                }));
            }
            const sortOptions = params.sortOptions;
            if (sortOptions && sortOptions.length > 0) {
                const { field, order } = sortOptions[0];
                queryBuilder.orderBy(`page.${field}`, order);
            }
            else {
                queryBuilder.orderBy('page.createdAt', 'DESC');
            }
            const skip = (params.page - 1) * params.limit;
            queryBuilder.skip(skip).take(params.limit);
            const [pages, total] = await queryBuilder.getManyAndCount();
            const pageDtos = this.toDtos(pages);
            return { data: pageDtos, total };
        }
        catch (error) {
            this.logger.error(`Lỗi khi tìm trang theo trạng thái: ${error.message}`, error.stack);
            throw new common_1.InternalServerErrorException(`Không thể tìm trang theo trạng thái: ${error.message}`);
        }
    }
    async findByTemplate(template, params) {
        try {
            this.logger.debug(`Đang tìm trang CMS theo template: ${template}`);
            const queryBuilder = this.pageRepository
                .createQueryBuilder('page')
                .where('page.isDeleted = :isDeleted', { isDeleted: false })
                .andWhere('page.template = :template', { template });
            if (params.search) {
                queryBuilder.andWhere(new typeorm_2.Brackets(qb => {
                    qb.where('LOWER(page.title) LIKE LOWER(:search)', { search: `%${params.search}%` })
                        .orWhere('LOWER(page.content) LIKE LOWER(:search)', { search: `%${params.search}%` });
                }));
            }
            queryBuilder.orderBy('page.createdAt', 'DESC');
            const skip = (params.page - 1) * params.limit;
            queryBuilder.skip(skip).take(params.limit);
            const [pages, total] = await queryBuilder.getManyAndCount();
            const pageDtos = this.toDtos(pages);
            return { data: pageDtos, total };
        }
        catch (error) {
            this.logger.error(`Lỗi khi tìm trang theo template: ${error.message}`, error.stack);
            throw new common_1.InternalServerErrorException(`Không thể tìm trang theo template: ${error.message}`);
        }
    }
    async getPublishedPages(params) {
        try {
            this.logger.debug(`Đang lấy trang đã được xuất bản`);
            return this.findByStatus(cms_pages_entity_1.CmsPageStatus.PUBLISHED, params);
        }
        catch (error) {
            this.logger.error(`Lỗi khi lấy trang đã xuất bản: ${error.message}`, error.stack);
            throw new common_1.InternalServerErrorException(`Không thể lấy trang đã xuất bản: ${error.message}`);
        }
    }
    async getDraftPages(params) {
        try {
            this.logger.debug(`Đang lấy trang draft`);
            return this.findByStatus(cms_pages_entity_1.CmsPageStatus.DRAFT, params);
        }
        catch (error) {
            this.logger.error(`Lỗi khi lấy trang draft: ${error.message}`, error.stack);
            throw new common_1.InternalServerErrorException(`Không thể lấy trang draft: ${error.message}`);
        }
    }
    async count(filter) {
        try {
            const where = this.buildWhereClause(filter);
            return this.pageRepository.count({ where });
        }
        catch (error) {
            this.logger.error(`Lỗi khi đếm số lượng trang CMS: ${error.message}`, error.stack);
            throw new common_1.InternalServerErrorException(`Không thể đếm số lượng trang CMS: ${error.message}`);
        }
    }
    async findOne(id, relations = []) {
        try {
            this.logger.debug(`Đang tìm trang CMS theo ID: ${id}`);
            const validatedRelations = this.validateRelations(relations);
            try {
                const page = await this.findById(id, validatedRelations, true);
                if (!page) {
                    throw new common_1.NotFoundException(`Không tìm thấy trang với ID: ${id}`);
                }
                return this.toDto(page);
            }
            catch (notFoundError) {
                if (notFoundError instanceof common_1.NotFoundException) {
                    return null;
                }
                throw notFoundError;
            }
        }
        catch (error) {
            this.logger.error(`Lỗi khi tìm trang CMS theo ID ${id}: ${error.message}`, error.stack);
            if (error instanceof common_1.NotFoundException) {
                return null;
            }
            throw new common_1.InternalServerErrorException(`Không thể lấy thông tin trang CMS: ${error.message}`);
        }
    }
    async findOneOrFail(id, relations = []) {
        try {
            this.logger.debug(`Đang tìm trang CMS theo ID: ${id}`);
            const validatedRelations = this.validateRelations(relations);
            const validRelationsToLoad = [...validatedRelations];
            if (!validRelationsToLoad.includes('creator')) {
                validRelationsToLoad.push('creator');
            }
            if (!validRelationsToLoad.includes('updater')) {
                validRelationsToLoad.push('updater');
            }
            const page = await this.findById(id, validRelationsToLoad);
            if (!page) {
                throw new common_1.NotFoundException(`Không tìm thấy trang với ID: ${id}`);
            }
            return this.toDto(page);
        }
        catch (error) {
            this.logger.error(`Lỗi khi tìm trang CMS theo ID ${id}: ${error.message}`, error.stack);
            if (error instanceof common_1.NotFoundException) {
                throw error;
            }
            throw new common_1.InternalServerErrorException(`Không thể lấy thông tin trang CMS: ${error.message}`);
        }
    }
    async findByBusinessCodePublic(businessCode) {
        try {
            this.logger.debug(`Đang tìm trang CMS theo business code: ${businessCode}`);
            const page = await super.findByBusinessCode(businessCode, false);
            if (!page) {
                return null;
            }
            return this.toDto(page);
        }
        catch (error) {
            this.logger.error(`Lỗi khi tìm trang theo business code: ${error.message}`, error.stack);
            throw new common_1.InternalServerErrorException(`Không thể tìm trang theo business code: ${error.message}`);
        }
    }
    async findBySlugPublic(slug) {
        try {
            this.logger.debug(`Đang tìm trang CMS theo slug: ${slug}`);
            const page = await super.findBySlug(slug, false);
            if (!page) {
                return null;
            }
            return this.toDto(page);
        }
        catch (error) {
            this.logger.error(`Lỗi khi tìm trang theo slug: ${error.message}`, error.stack);
            throw new common_1.InternalServerErrorException(`Không thể tìm trang theo slug: ${error.message}`);
        }
    }
    async search(keyword, params) {
        try {
            this.logger.debug(`Đang tìm kiếm trang CMS với từ khóa: ${keyword}`);
            const queryBuilder = this.pageRepository
                .createQueryBuilder('page')
                .where('page.isDeleted = :isDeleted', { isDeleted: false });
            queryBuilder.andWhere(new typeorm_2.Brackets(qb => {
                qb.where('LOWER(page.title) LIKE LOWER(:keyword)', { keyword: `%${keyword}%` })
                    .orWhere('LOWER(page.content) LIKE LOWER(:keyword)', { keyword: `%${keyword}%` })
                    .orWhere('LOWER(page.slug) LIKE LOWER(:keyword)', { keyword: `%${keyword}%` })
                    .orWhere('LOWER(page.metaTitle) LIKE LOWER(:keyword)', { keyword: `%${keyword}%` })
                    .orWhere('LOWER(page.metaDescription) LIKE LOWER(:keyword)', { keyword: `%${keyword}%` })
                    .orWhere('LOWER(page.metaKeywords) LIKE LOWER(:keyword)', { keyword: `%${keyword}%` });
            }));
            const sortOptions = params.sortOptions;
            if (sortOptions && sortOptions.length > 0) {
                const { field, order } = sortOptions[0];
                queryBuilder.orderBy(`page.${field}`, order);
            }
            else {
                queryBuilder.orderBy('page.createdAt', 'DESC');
            }
            const skip = (params.page - 1) * params.limit;
            queryBuilder.skip(skip).take(params.limit);
            const [pages, total] = await queryBuilder.getManyAndCount();
            const pageDtos = this.toDtos(pages);
            return { data: pageDtos, total };
        }
        catch (error) {
            this.logger.error(`Lỗi khi tìm kiếm trang CMS: ${error.message}`, error.stack);
            throw new common_1.InternalServerErrorException(`Không thể tìm kiếm trang CMS: ${error.message}`);
        }
    }
    async findDeleted(params) {
        try {
            this.logger.debug(`Đang tìm các trang CMS đã bị xóa mềm với tham số: ${JSON.stringify(params)}`);
            const { limit, page } = params;
            const queryBuilder = this.pageRepository
                .createQueryBuilder('page')
                .where('page.isDeleted = :isDeleted', { isDeleted: true });
            if (params.search) {
                queryBuilder.andWhere(new typeorm_2.Brackets(qb => {
                    qb.where('LOWER(page.title) LIKE LOWER(:search)', { search: `%${params.search}%` })
                        .orWhere('LOWER(page.content) LIKE LOWER(:search)', { search: `%${params.search}%` });
                }));
            }
            const sortOptions = params.sortOptions;
            if (sortOptions && sortOptions.length > 0) {
                const { field, order } = sortOptions[0];
                queryBuilder.orderBy(`page.${field}`, order);
            }
            else {
                queryBuilder.orderBy('page.deletedAt', 'DESC');
            }
            queryBuilder.leftJoinAndSelect('page.creator', 'creator')
                .leftJoinAndSelect('page.updater', 'updater')
                .leftJoinAndSelect('page.deleter', 'deleter');
            const skip = (page - 1) * limit;
            queryBuilder.skip(skip).take(limit);
            const [pages, total] = await queryBuilder.getManyAndCount();
            const pageDtos = this.toDtos(pages);
            return { data: pageDtos, total };
        }
        catch (error) {
            this.logger.error(`Lỗi khi tìm trang CMS đã xóa: ${error.message}`, error.stack);
            throw new common_1.InternalServerErrorException(`Không thể lấy danh sách trang CMS đã xóa: ${error.message}`);
        }
    }
    async getStatistics() {
        try {
            const total = await this.pageRepository.count({
                where: { isDeleted: false },
            });
            const byStatusQuery = await this.pageRepository
                .createQueryBuilder('page')
                .select('page.status', 'status')
                .addSelect('COUNT(*)', 'count')
                .where('page.isDeleted = :isDeleted', { isDeleted: false })
                .groupBy('page.status')
                .getRawMany();
            const byStatus = {};
            byStatusQuery.forEach(item => {
                byStatus[item.status] = parseInt(item.count, 10);
            });
            const byTemplateQuery = await this.pageRepository
                .createQueryBuilder('page')
                .select('COALESCE(page.template, \'default\')', 'template')
                .addSelect('COUNT(*)', 'count')
                .where('page.isDeleted = :isDeleted', { isDeleted: false })
                .groupBy('COALESCE(page.template, \'default\')')
                .getRawMany();
            const byTemplate = {};
            byTemplateQuery.forEach(item => {
                byTemplate[item.template] = parseInt(item.count, 10);
            });
            const pages = await this.pageRepository.find({
                where: { isDeleted: false },
                select: ['content'],
            });
            let totalWordCount = 0;
            pages.forEach(page => {
                const plainText = page.content.replace(/<[^>]*>/g, '');
                const wordCount = plainText.split(/\s+/).filter(word => word.length > 0).length;
                totalWordCount += wordCount;
            });
            const averageWordCount = pages.length > 0 ? Math.round(totalWordCount / pages.length) : 0;
            return {
                total,
                byStatus,
                byTemplate,
                totalWordCount,
                averageWordCount,
            };
        }
        catch (error) {
            this.logger.error(`Lỗi khi lấy thống kê trang CMS: ${error.message}`, error.stack);
            throw new common_1.InternalServerErrorException(`Không thể lấy thống kê trang CMS: ${error.message}`);
        }
    }
    getAvailableTemplates() {
        try {
            return super.getAvailableTemplates();
        }
        catch (error) {
            this.logger.error(`Lỗi khi lấy danh sách template: ${error.message}`, error.stack);
            throw new common_1.InternalServerErrorException(`Không thể lấy danh sách template: ${error.message}`);
        }
    }
    async getPopularTemplatePages(limit = 10) {
        try {
            this.logger.debug(`Đang lấy ${limit} trang theo template phổ biến`);
            const popularTemplateQuery = await this.pageRepository
                .createQueryBuilder('page')
                .select('COALESCE(page.template, \'default\')', 'template')
                .addSelect('COUNT(*)', 'count')
                .where('page.isDeleted = :isDeleted', { isDeleted: false })
                .andWhere('page.status = :status', { status: cms_pages_entity_1.CmsPageStatus.PUBLISHED })
                .groupBy('COALESCE(page.template, \'default\')')
                .orderBy('COUNT(*)', 'DESC')
                .limit(1)
                .getRawOne();
            if (!popularTemplateQuery) {
                return [];
            }
            const popularTemplate = popularTemplateQuery.template;
            const pages = await this.pageRepository.find({
                where: {
                    isDeleted: false,
                    status: cms_pages_entity_1.CmsPageStatus.PUBLISHED,
                    template: popularTemplate === 'default' ? null : popularTemplate,
                },
                order: { createdAt: 'DESC' },
                take: limit,
                relations: ['creator'],
            });
            return this.toDtos(pages);
        }
        catch (error) {
            this.logger.error(`Lỗi khi lấy trang template phổ biến: ${error.message}`, error.stack);
            throw new common_1.InternalServerErrorException(`Không thể lấy trang template phổ biến: ${error.message}`);
        }
    }
};
exports.ReadCmsPagesService = ReadCmsPagesService;
exports.ReadCmsPagesService = ReadCmsPagesService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(cms_pages_entity_1.CmsPages)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        typeorm_2.DataSource,
        event_emitter_1.EventEmitter2])
], ReadCmsPagesService);
//# sourceMappingURL=read.cms-pages.service.js.map
"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var BaseCmsPagesService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.BaseCmsPagesService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const event_emitter_1 = require("@nestjs/event-emitter");
const class_transformer_1 = require("class-transformer");
const cms_pages_entity_1 = require("../entity/cms-pages.entity");
const cms_page_dto_1 = require("../dto/cms-page.dto");
let BaseCmsPagesService = BaseCmsPagesService_1 = class BaseCmsPagesService {
    pageRepository;
    dataSource;
    eventEmitter;
    logger = new common_1.Logger(BaseCmsPagesService_1.name);
    EVENT_PAGE_CREATED = 'cms-page.created';
    EVENT_PAGE_UPDATED = 'cms-page.updated';
    EVENT_PAGE_DELETED = 'cms-page.deleted';
    EVENT_PAGE_PUBLISHED = 'cms-page.published';
    EVENT_PAGE_DRAFTED = 'cms-page.drafted';
    validRelations = [
        'creator',
        'updater',
        'deleter'
    ];
    constructor(pageRepository, dataSource, eventEmitter) {
        this.pageRepository = pageRepository;
        this.dataSource = dataSource;
        this.eventEmitter = eventEmitter;
    }
    validateRelations(relations) {
        if (!relations || !Array.isArray(relations)) {
            return [];
        }
        return relations.filter(relation => {
            const isValid = this.validRelations.includes(relation);
            if (!isValid) {
                this.logger.warn(`Mối quan hệ không hợp lệ: ${relation}`);
            }
            return isValid;
        });
    }
    buildWhereClause(filter) {
        if (!filter) {
            return { isDeleted: false };
        }
        try {
            const filterObj = JSON.parse(filter);
            return {
                ...filterObj,
                isDeleted: false,
            };
        }
        catch (e) {
            return [
                { title: (0, typeorm_2.ILike)(`%${filter}%`), isDeleted: false },
                { content: (0, typeorm_2.ILike)(`%${filter}%`), isDeleted: false },
                { slug: (0, typeorm_2.ILike)(`%${filter}%`), isDeleted: false },
                { metaTitle: (0, typeorm_2.ILike)(`%${filter}%`), isDeleted: false },
                { metaDescription: (0, typeorm_2.ILike)(`%${filter}%`), isDeleted: false },
            ];
        }
    }
    async findById(id, relations = [], throwError = true) {
        const validatedRelations = this.validateRelations(relations);
        const page = await this.pageRepository.findOne({
            where: { id, isDeleted: false },
            relations: validatedRelations,
        });
        if (!page && throwError) {
            throw new common_1.NotFoundException(`Không tìm thấy trang với ID: ${id}`);
        }
        return page;
    }
    async findByBusinessCode(businessCode, throwError = true) {
        const page = await this.pageRepository.findOne({
            where: { businessCode, isDeleted: false },
        });
        if (!page && throwError) {
            throw new common_1.NotFoundException(`Không tìm thấy trang với mã: ${businessCode}`);
        }
        return page;
    }
    async findBySlug(slug, throwError = true) {
        const page = await this.pageRepository.findOne({
            where: { slug, isDeleted: false },
        });
        if (!page && throwError) {
            throw new common_1.NotFoundException(`Không tìm thấy trang với slug: ${slug}`);
        }
        return page;
    }
    async isSlugExists(slug, excludeId) {
        const whereCondition = {
            slug,
            isDeleted: false,
        };
        if (excludeId) {
            whereCondition.id = (0, typeorm_2.Not)(excludeId);
        }
        const count = await this.pageRepository.count({
            where: whereCondition,
        });
        return count > 0;
    }
    toDto(page) {
        if (!page) {
            return null;
        }
        const plainObj = Object.assign({}, page);
        plainObj.seoTitle = page.getSeoTitle();
        plainObj.seoDescription = page.getSeoDescription();
        plainObj.seoKeywordsArray = page.getSeoKeywordsArray();
        plainObj.usedTemplate = page.getTemplate();
        plainObj.fullUrl = page.getFullUrl();
        plainObj.excerpt = page.getExcerpt();
        plainObj.wordCount = page.getWordCount();
        plainObj.estimatedReadingTime = page.getEstimatedReadingTime();
        return (0, class_transformer_1.plainToInstance)(cms_page_dto_1.CmsPageDto, plainObj, {
            excludeExtraneousValues: true,
            enableImplicitConversion: true,
            exposeDefaultValues: true,
            exposeUnsetFields: false,
        });
    }
    toDtos(pages) {
        if (!pages || !Array.isArray(pages)) {
            return [];
        }
        return pages.map(page => this.toDto(page))
            .filter((dto) => dto !== null);
    }
    isPagePublished(page) {
        return page.isPublished();
    }
    isPageDraft(page) {
        return page.isDraft();
    }
    generateSlugFromTitle(title) {
        return title
            .toLowerCase()
            .normalize('NFD')
            .replace(/[\u0300-\u036f]/g, '')
            .replace(/[đĐ]/g, 'd')
            .replace(/[^a-z0-9\s-]/g, '')
            .replace(/\s+/g, '-')
            .replace(/-+/g, '-')
            .replace(/^-|-$/g, '');
    }
    async generateUniqueSlug(title, excludeId) {
        let baseSlug = this.generateSlugFromTitle(title);
        let slug = baseSlug;
        let counter = 1;
        while (await this.isSlugExists(slug, excludeId)) {
            slug = `${baseSlug}-${counter}`;
            counter++;
        }
        return slug;
    }
    getAvailableTemplates() {
        return [
            'default',
            'full-width',
            'sidebar-left',
            'sidebar-right',
            'landing-page',
            'contact',
            'about',
            'privacy-policy',
            'terms-of-service',
        ];
    }
    isValidTemplate(template) {
        const availableTemplates = this.getAvailableTemplates();
        return availableTemplates.includes(template);
    }
};
exports.BaseCmsPagesService = BaseCmsPagesService;
exports.BaseCmsPagesService = BaseCmsPagesService = BaseCmsPagesService_1 = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(cms_pages_entity_1.CmsPages)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        typeorm_2.DataSource,
        event_emitter_1.EventEmitter2])
], BaseCmsPagesService);
//# sourceMappingURL=base.cms-pages.service.js.map
"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UpdateCmsCustomerFeedbackDto = void 0;
const openapi = require("@nestjs/swagger");
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
const cms_customer_feedbacks_entity_1 = require("../entity/cms-customer-feedbacks.entity");
class UpdateCmsCustomerFeedbackDto {
    customerName;
    customerTitleCompany;
    feedbackText;
    rating;
    avatarUrl;
    productServiceName;
    status;
    static _OPENAPI_METADATA_FACTORY() {
        return { customerName: { required: false, type: () => String, maxLength: 255 }, customerTitleCompany: { required: false, type: () => String, maxLength: 255 }, feedbackText: { required: false, type: () => String }, rating: { required: false, type: () => Number, minimum: 1, maximum: 5 }, avatarUrl: { required: false, type: () => String, format: "uri" }, productServiceName: { required: false, type: () => String, maxLength: 255 }, status: { required: false, enum: require("../entity/cms-customer-feedbacks.entity").CmsCustomerFeedbackStatus } };
    }
}
exports.UpdateCmsCustomerFeedbackDto = UpdateCmsCustomerFeedbackDto;
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Tên khách hàng',
        example: 'Nguyễn Văn A',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)({ message: 'Tên khách hàng phải là chuỗi' }),
    (0, class_validator_1.MaxLength)(255, { message: 'Tên khách hàng không được vượt quá 255 ký tự' }),
    __metadata("design:type", String)
], UpdateCmsCustomerFeedbackDto.prototype, "customerName", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Chức danh, công ty của khách hàng',
        example: 'Giám đốc, Công ty ABC',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)({ message: 'Chức danh, công ty phải là chuỗi' }),
    (0, class_validator_1.MaxLength)(255, { message: 'Chức danh, công ty không được vượt quá 255 ký tự' }),
    __metadata("design:type", String)
], UpdateCmsCustomerFeedbackDto.prototype, "customerTitleCompany", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Nội dung phản hồi',
        example: 'Dịch vụ rất tốt, tôi rất hài lòng với chất lượng sản phẩm.',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)({ message: 'Nội dung phản hồi phải là chuỗi' }),
    __metadata("design:type", String)
], UpdateCmsCustomerFeedbackDto.prototype, "feedbackText", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Đánh giá sao (1-5)',
        example: 5,
        minimum: 1,
        maximum: 5,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsInt)({ message: 'Đánh giá phải là số nguyên' }),
    (0, class_validator_1.Min)(1, { message: 'Đánh giá tối thiểu là 1 sao' }),
    (0, class_validator_1.Max)(5, { message: 'Đánh giá tối đa là 5 sao' }),
    __metadata("design:type", Number)
], UpdateCmsCustomerFeedbackDto.prototype, "rating", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'URL ảnh đại diện của khách hàng',
        example: 'https://example.com/avatar.jpg',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsUrl)({}, { message: 'URL ảnh đại diện không hợp lệ' }),
    __metadata("design:type", String)
], UpdateCmsCustomerFeedbackDto.prototype, "avatarUrl", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Tên sản phẩm/dịch vụ được đánh giá',
        example: 'Dịch vụ mua bán vàng',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)({ message: 'Tên sản phẩm/dịch vụ phải là chuỗi' }),
    (0, class_validator_1.MaxLength)(255, { message: 'Tên sản phẩm/dịch vụ không được vượt quá 255 ký tự' }),
    __metadata("design:type", String)
], UpdateCmsCustomerFeedbackDto.prototype, "productServiceName", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Trạng thái feedback',
        example: cms_customer_feedbacks_entity_1.CmsCustomerFeedbackStatus.APPROVED,
        enum: cms_customer_feedbacks_entity_1.CmsCustomerFeedbackStatus,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)(cms_customer_feedbacks_entity_1.CmsCustomerFeedbackStatus, { message: 'Trạng thái feedback không hợp lệ' }),
    __metadata("design:type", String)
], UpdateCmsCustomerFeedbackDto.prototype, "status", void 0);
//# sourceMappingURL=update.cms-customer-feedback.dto.js.map
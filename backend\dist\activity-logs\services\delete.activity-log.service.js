"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.DeleteActivityLogService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_transactional_1 = require("typeorm-transactional");
const typeorm_1 = require("typeorm");
const base_activity_log_service_1 = require("./base.activity-log.service");
let DeleteActivityLogService = class DeleteActivityLogService extends base_activity_log_service_1.BaseActivityLogService {
    async softDelete(id, userId) {
        try {
            this.logger.debug(`Đang xóa mềm lịch sử hoạt động với ID: ${id}`);
            const activityLog = await this.findByIdOrFail(id);
            if (activityLog.isDeleted) {
                throw new common_1.BadRequestException('Lịch sử hoạt động đã bị xóa');
            }
            activityLog.isDeleted = true;
            activityLog.deletedBy = userId || null;
            const savedActivityLog = await this.activityLogRepository.save(activityLog);
            this.logger.debug(`Đã xóa mềm lịch sử hoạt động với ID: ${savedActivityLog.id}`);
            this.eventEmitter.emit(this.EVENT_ACTIVITY_LOG_DELETED, {
                activityLogId: savedActivityLog.id,
                userId: savedActivityLog.userId,
                action: savedActivityLog.action,
                module: savedActivityLog.module,
            });
            return this.toDto(savedActivityLog);
        }
        catch (error) {
            this.logger.error(`Lỗi khi xóa mềm lịch sử hoạt động: ${error.message}`, error.stack);
            if (error instanceof common_1.NotFoundException || error instanceof common_1.BadRequestException) {
                throw error;
            }
            throw new common_1.InternalServerErrorException('Lỗi khi xóa mềm lịch sử hoạt động');
        }
    }
    async bulkSoftDelete(ids, userId) {
        try {
            this.logger.debug(`Đang xóa mềm hàng loạt ${ids.length} lịch sử hoạt động`);
            const activityLogs = await this.activityLogRepository.find({
                where: { id: (0, typeorm_1.In)(ids), isDeleted: false },
            });
            if (activityLogs.length !== ids.length) {
                throw new common_1.NotFoundException('Một hoặc nhiều lịch sử hoạt động không tồn tại hoặc đã bị xóa');
            }
            const updatedActivityLogs = activityLogs.map(log => ({
                ...log,
                isDeleted: true,
                deletedBy: userId || null,
            }));
            const savedActivityLogs = await this.activityLogRepository.save(updatedActivityLogs);
            this.logger.debug(`Đã xóa mềm ${savedActivityLogs.length} lịch sử hoạt động`);
            savedActivityLogs.forEach(log => {
                this.eventEmitter.emit(this.EVENT_ACTIVITY_LOG_DELETED, {
                    activityLogId: log.id,
                    userId: log.userId,
                    action: log.action,
                    module: log.module,
                });
            });
            return savedActivityLogs.map(log => this.toDto(log));
        }
        catch (error) {
            this.logger.error(`Lỗi khi xóa mềm hàng loạt lịch sử hoạt động: ${error.message}`, error.stack);
            if (error instanceof common_1.NotFoundException) {
                throw error;
            }
            throw new common_1.InternalServerErrorException('Lỗi khi xóa mềm hàng loạt lịch sử hoạt động');
        }
    }
    async remove(id) {
        try {
            this.logger.debug(`Đang xóa cứng lịch sử hoạt động với ID: ${id}`);
            const activityLog = await this.findByIdOrFail(id, [], true);
            await this.activityLogRepository.remove(activityLog);
            this.logger.debug(`Đã xóa cứng lịch sử hoạt động với ID: ${id}`);
        }
        catch (error) {
            this.logger.error(`Lỗi khi xóa cứng lịch sử hoạt động: ${error.message}`, error.stack);
            if (error instanceof common_1.NotFoundException) {
                throw error;
            }
            throw new common_1.InternalServerErrorException('Lỗi khi xóa cứng lịch sử hoạt động');
        }
    }
    async bulkRemove(ids) {
        try {
            this.logger.debug(`Đang xóa cứng hàng loạt ${ids.length} lịch sử hoạt động`);
            const activityLogs = await this.activityLogRepository.find({
                where: { id: (0, typeorm_1.In)(ids) },
                withDeleted: true,
            });
            if (activityLogs.length !== ids.length) {
                throw new common_1.NotFoundException('Một hoặc nhiều lịch sử hoạt động không tồn tại');
            }
            await this.activityLogRepository.remove(activityLogs);
            this.logger.debug(`Đã xóa cứng ${activityLogs.length} lịch sử hoạt động`);
        }
        catch (error) {
            this.logger.error(`Lỗi khi xóa cứng hàng loạt lịch sử hoạt động: ${error.message}`, error.stack);
            if (error instanceof common_1.NotFoundException) {
                throw error;
            }
            throw new common_1.InternalServerErrorException('Lỗi khi xóa cứng hàng loạt lịch sử hoạt động');
        }
    }
    async restore(id) {
        try {
            this.logger.debug(`Đang khôi phục lịch sử hoạt động với ID: ${id}`);
            const activityLog = await this.findByIdOrFail(id, [], true);
            if (!activityLog.isDeleted) {
                throw new common_1.BadRequestException('Lịch sử hoạt động chưa bị xóa');
            }
            activityLog.isDeleted = false;
            activityLog.deletedBy = null;
            const savedActivityLog = await this.activityLogRepository.save(activityLog);
            this.logger.debug(`Đã khôi phục lịch sử hoạt động với ID: ${savedActivityLog.id}`);
            return this.toDto(savedActivityLog);
        }
        catch (error) {
            this.logger.error(`Lỗi khi khôi phục lịch sử hoạt động: ${error.message}`, error.stack);
            if (error instanceof common_1.NotFoundException || error instanceof common_1.BadRequestException) {
                throw error;
            }
            throw new common_1.InternalServerErrorException('Lỗi khi khôi phục lịch sử hoạt động');
        }
    }
    async clearOldLogs(days) {
        try {
            this.logger.debug(`Đang xóa lịch sử hoạt động cũ hơn ${days} ngày`);
            const date = new Date();
            date.setDate(date.getDate() - days);
            const result = await this.activityLogRepository.delete({
                createdAt: (0, typeorm_1.LessThan)(date),
            });
            this.logger.debug(`Đã xóa ${result.affected} lịch sử hoạt động cũ`);
            return result.affected || 0;
        }
        catch (error) {
            this.logger.error(`Lỗi khi xóa lịch sử hoạt động cũ: ${error.message}`, error.stack);
            throw new common_1.InternalServerErrorException('Lỗi khi xóa lịch sử hoạt động cũ');
        }
    }
};
exports.DeleteActivityLogService = DeleteActivityLogService;
__decorate([
    (0, typeorm_transactional_1.Transactional)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String]),
    __metadata("design:returntype", Promise)
], DeleteActivityLogService.prototype, "softDelete", null);
__decorate([
    (0, typeorm_transactional_1.Transactional)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Array, String]),
    __metadata("design:returntype", Promise)
], DeleteActivityLogService.prototype, "bulkSoftDelete", null);
__decorate([
    (0, typeorm_transactional_1.Transactional)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], DeleteActivityLogService.prototype, "remove", null);
__decorate([
    (0, typeorm_transactional_1.Transactional)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Array]),
    __metadata("design:returntype", Promise)
], DeleteActivityLogService.prototype, "bulkRemove", null);
__decorate([
    (0, typeorm_transactional_1.Transactional)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], DeleteActivityLogService.prototype, "restore", null);
__decorate([
    (0, typeorm_transactional_1.Transactional)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", Promise)
], DeleteActivityLogService.prototype, "clearOldLogs", null);
exports.DeleteActivityLogService = DeleteActivityLogService = __decorate([
    (0, common_1.Injectable)()
], DeleteActivityLogService);
//# sourceMappingURL=delete.activity-log.service.js.map
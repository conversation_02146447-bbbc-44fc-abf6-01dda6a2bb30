"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UpdateCmsPagesService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const event_emitter_1 = require("@nestjs/event-emitter");
const typeorm_transactional_1 = require("typeorm-transactional");
const base_cms_pages_service_1 = require("./base.cms-pages.service");
const cms_pages_entity_1 = require("../entity/cms-pages.entity");
const update_cms_page_dto_1 = require("../dto/update.cms-page.dto");
let UpdateCmsPagesService = class UpdateCmsPagesService extends base_cms_pages_service_1.BaseCmsPagesService {
    pageRepository;
    dataSource;
    eventEmitter;
    constructor(pageRepository, dataSource, eventEmitter) {
        super(pageRepository, dataSource, eventEmitter);
        this.pageRepository = pageRepository;
        this.dataSource = dataSource;
        this.eventEmitter = eventEmitter;
    }
    async update(id, updateDto, userId) {
        try {
            this.logger.debug(`Đang cập nhật trang CMS với ID: ${id}`);
            const page = await this.findById(id, []);
            if (!page) {
                throw new common_1.NotFoundException(`Không tìm thấy trang với ID: ${id}`);
            }
            if (updateDto.slug && updateDto.slug !== page.slug) {
                if (await this.isSlugExists(updateDto.slug, id)) {
                    throw new common_1.BadRequestException(`Slug "${updateDto.slug}" đã tồn tại`);
                }
            }
            if (updateDto.template && !this.isValidTemplate(updateDto.template)) {
                throw new common_1.BadRequestException(`Template "${updateDto.template}" không hợp lệ`);
            }
            const oldData = this.toDto(page);
            if (updateDto.title !== undefined) {
                page.title = updateDto.title;
            }
            if (updateDto.slug !== undefined) {
                page.slug = updateDto.slug;
            }
            if (updateDto.content !== undefined) {
                page.content = updateDto.content;
            }
            if (updateDto.template !== undefined) {
                page.template = updateDto.template || null;
            }
            if (updateDto.status !== undefined) {
                page.status = updateDto.status;
            }
            if (updateDto.metaTitle !== undefined) {
                page.metaTitle = updateDto.metaTitle || null;
            }
            if (updateDto.metaDescription !== undefined) {
                page.metaDescription = updateDto.metaDescription || null;
            }
            if (updateDto.metaKeywords !== undefined) {
                page.metaKeywords = updateDto.metaKeywords || null;
            }
            page.updatedBy = userId;
            const updatedPage = await this.pageRepository.save(page);
            const pageDto = this.toDto(updatedPage);
            if (!pageDto) {
                throw new common_1.InternalServerErrorException('Không thể chuyển đổi entity thành DTO');
            }
            this.eventEmitter.emit(this.EVENT_PAGE_UPDATED, {
                pageId: pageDto.id,
                userId,
                oldData,
                newData: pageDto,
            });
            if (oldData?.status !== pageDto.status) {
                if (pageDto.status === cms_pages_entity_1.CmsPageStatus.PUBLISHED) {
                    this.eventEmitter.emit(this.EVENT_PAGE_PUBLISHED, {
                        pageId: pageDto.id,
                        userId,
                        pageData: pageDto,
                    });
                }
                else if (pageDto.status === cms_pages_entity_1.CmsPageStatus.DRAFT) {
                    this.eventEmitter.emit(this.EVENT_PAGE_DRAFTED, {
                        pageId: pageDto.id,
                        userId,
                        pageData: pageDto,
                    });
                }
            }
            return pageDto;
        }
        catch (error) {
            this.logger.error(`Lỗi khi cập nhật trang CMS: ${error.message}`, error.stack);
            if (error instanceof common_1.BadRequestException || error instanceof common_1.NotFoundException) {
                throw error;
            }
            throw new common_1.InternalServerErrorException(`Không thể cập nhật trang CMS: ${error.message}`);
        }
    }
    async bulkUpdate(updates, userId) {
        try {
            this.logger.debug(`Đang cập nhật ${updates.length} trang CMS`);
            const updatedPages = [];
            for (const update of updates) {
                const page = await this.update(update.id, update.data, userId);
                if (page) {
                    updatedPages.push(page);
                }
            }
            return updatedPages;
        }
        catch (error) {
            this.logger.error(`Lỗi khi cập nhật nhiều trang CMS: ${error.message}`, error.stack);
            throw new common_1.InternalServerErrorException(`Không thể cập nhật nhiều trang CMS: ${error.message}`);
        }
    }
    async publish(id, userId) {
        try {
            this.logger.debug(`Đang xuất bản trang CMS với ID: ${id}`);
            const updateDto = { status: cms_pages_entity_1.CmsPageStatus.PUBLISHED };
            return this.update(id, updateDto, userId);
        }
        catch (error) {
            this.logger.error(`Lỗi khi xuất bản trang CMS: ${error.message}`, error.stack);
            if (error instanceof common_1.NotFoundException) {
                throw error;
            }
            throw new common_1.InternalServerErrorException(`Không thể xuất bản trang CMS: ${error.message}`);
        }
    }
    async draft(id, userId) {
        try {
            this.logger.debug(`Đang chuyển trang CMS thành draft với ID: ${id}`);
            const updateDto = { status: cms_pages_entity_1.CmsPageStatus.DRAFT };
            return this.update(id, updateDto, userId);
        }
        catch (error) {
            this.logger.error(`Lỗi khi chuyển trang CMS thành draft: ${error.message}`, error.stack);
            if (error instanceof common_1.NotFoundException) {
                throw error;
            }
            throw new common_1.InternalServerErrorException(`Không thể chuyển trang CMS thành draft: ${error.message}`);
        }
    }
    async updateStatus(id, status, userId) {
        try {
            this.logger.debug(`Đang cập nhật trạng thái trang CMS với ID: ${id} thành ${status}`);
            if (status === cms_pages_entity_1.CmsPageStatus.PUBLISHED) {
                return this.publish(id, userId);
            }
            else if (status === cms_pages_entity_1.CmsPageStatus.DRAFT) {
                return this.draft(id, userId);
            }
            else {
                const updateDto = { status };
                return this.update(id, updateDto, userId);
            }
        }
        catch (error) {
            this.logger.error(`Lỗi khi cập nhật trạng thái trang CMS: ${error.message}`, error.stack);
            if (error instanceof common_1.NotFoundException) {
                throw error;
            }
            throw new common_1.InternalServerErrorException(`Không thể cập nhật trạng thái trang CMS: ${error.message}`);
        }
    }
    async updateTemplate(id, template, userId) {
        try {
            this.logger.debug(`Đang cập nhật template trang CMS với ID: ${id} thành ${template}`);
            const updateDto = { template };
            return this.update(id, updateDto, userId);
        }
        catch (error) {
            this.logger.error(`Lỗi khi cập nhật template trang CMS: ${error.message}`, error.stack);
            if (error instanceof common_1.NotFoundException || error instanceof common_1.BadRequestException) {
                throw error;
            }
            throw new common_1.InternalServerErrorException(`Không thể cập nhật template trang CMS: ${error.message}`);
        }
    }
    async bulkPublish(ids, userId) {
        try {
            this.logger.debug(`Đang xuất bản ${ids.length} trang CMS`);
            const publishedPages = [];
            for (const id of ids) {
                try {
                    const page = await this.publish(id, userId);
                    if (page) {
                        publishedPages.push(page);
                    }
                }
                catch (error) {
                    this.logger.warn(`Không thể xuất bản trang với ID ${id}: ${error.message}`);
                }
            }
            return publishedPages;
        }
        catch (error) {
            this.logger.error(`Lỗi khi xuất bản nhiều trang CMS: ${error.message}`, error.stack);
            throw new common_1.InternalServerErrorException(`Không thể xuất bản nhiều trang CMS: ${error.message}`);
        }
    }
    async bulkDraft(ids, userId) {
        try {
            this.logger.debug(`Đang chuyển ${ids.length} trang CMS thành draft`);
            const draftedPages = [];
            for (const id of ids) {
                try {
                    const page = await this.draft(id, userId);
                    if (page) {
                        draftedPages.push(page);
                    }
                }
                catch (error) {
                    this.logger.warn(`Không thể chuyển trang với ID ${id} thành draft: ${error.message}`);
                }
            }
            return draftedPages;
        }
        catch (error) {
            this.logger.error(`Lỗi khi chuyển nhiều trang CMS thành draft: ${error.message}`, error.stack);
            throw new common_1.InternalServerErrorException(`Không thể chuyển nhiều trang CMS thành draft: ${error.message}`);
        }
    }
};
exports.UpdateCmsPagesService = UpdateCmsPagesService;
__decorate([
    (0, typeorm_transactional_1.Transactional)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, update_cms_page_dto_1.UpdateCmsPageDto, String]),
    __metadata("design:returntype", Promise)
], UpdateCmsPagesService.prototype, "update", null);
__decorate([
    (0, typeorm_transactional_1.Transactional)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Array, String]),
    __metadata("design:returntype", Promise)
], UpdateCmsPagesService.prototype, "bulkUpdate", null);
__decorate([
    (0, typeorm_transactional_1.Transactional)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String]),
    __metadata("design:returntype", Promise)
], UpdateCmsPagesService.prototype, "publish", null);
__decorate([
    (0, typeorm_transactional_1.Transactional)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String]),
    __metadata("design:returntype", Promise)
], UpdateCmsPagesService.prototype, "draft", null);
__decorate([
    (0, typeorm_transactional_1.Transactional)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String, String]),
    __metadata("design:returntype", Promise)
], UpdateCmsPagesService.prototype, "updateStatus", null);
__decorate([
    (0, typeorm_transactional_1.Transactional)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String, String]),
    __metadata("design:returntype", Promise)
], UpdateCmsPagesService.prototype, "updateTemplate", null);
__decorate([
    (0, typeorm_transactional_1.Transactional)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Array, String]),
    __metadata("design:returntype", Promise)
], UpdateCmsPagesService.prototype, "bulkPublish", null);
__decorate([
    (0, typeorm_transactional_1.Transactional)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Array, String]),
    __metadata("design:returntype", Promise)
], UpdateCmsPagesService.prototype, "bulkDraft", null);
exports.UpdateCmsPagesService = UpdateCmsPagesService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(cms_pages_entity_1.CmsPages)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        typeorm_2.DataSource,
        event_emitter_1.EventEmitter2])
], UpdateCmsPagesService);
//# sourceMappingURL=update.cms-pages.service.js.map
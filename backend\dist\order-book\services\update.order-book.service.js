"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UpdateOrderBookService = void 0;
const common_1 = require("@nestjs/common");
const event_emitter_1 = require("@nestjs/event-emitter");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const typeorm_transactional_1 = require("typeorm-transactional");
const localized_http_exception_1 = require("../../common/exceptions/localized-http.exception");
const commission_processor_service_1 = require("../../agent-commissions/services/commission-processor.service");
const asset_service_1 = require("../../token-assets/asset.service");
const transaction_type_enum_1 = require("../../transactions/enums/transaction-type.enum");
const read_wallet_service_1 = require("../../wallets/services/read.wallet.service");
const update_wallet_service_1 = require("../../wallets/services/update.wallet.service");
const settlement_order_book_dto_1 = require("../dto/settlement-order-book.dto");
const update_order_book_dto_1 = require("../dto/update-order-book.dto");
const order_book_detail_entity_1 = require("../entities/order-book-detail.entity");
const order_book_entity_1 = require("../entities/order-book.entity");
const approve_status_enum_1 = require("../enums/approve-status.enum");
const business_type_enum_1 = require("../enums/business-type.enum");
const order_status_enum_1 = require("../enums/order-status.enum");
const order_type_enum_1 = require("../enums/order-type.enum");
const base_order_book_service_1 = require("./base.order-book.service");
let UpdateOrderBookService = class UpdateOrderBookService extends base_order_book_service_1.BaseOrderBookService {
    orderBookRepository;
    orderBookDetailRepository;
    eventEmitter;
    tokenAssetService;
    updateWalletService;
    readWalletService;
    commissionProcessorService;
    constructor(orderBookRepository, orderBookDetailRepository, eventEmitter, tokenAssetService, updateWalletService, readWalletService, commissionProcessorService) {
        super(orderBookRepository, eventEmitter);
        this.orderBookRepository = orderBookRepository;
        this.orderBookDetailRepository = orderBookDetailRepository;
        this.eventEmitter = eventEmitter;
        this.tokenAssetService = tokenAssetService;
        this.updateWalletService = updateWalletService;
        this.readWalletService = readWalletService;
        this.commissionProcessorService = commissionProcessorService;
    }
    async update(id, updateOrderBookDto, userId) {
        try {
            this.logger.log(`Cập nhật lệnh ${id} với dữ liệu: ${JSON.stringify(updateOrderBookDto)}`);
            const orderBook = await this.validateUpdatePermissions(id);
            const originalOrder = this.toDto(orderBook, [
                'details',
                'details.product',
                'user',
            ]);
            const hasChanges = await this.updateAllowedFields(orderBook, updateOrderBookDto, userId);
            if (!hasChanges) {
                this.logger.log(`Không có thay đổi nào cho lệnh ${id}`);
                return this.toDto(orderBook, ['details', 'details.product', 'user']);
            }
            const updatedOrder = await this.orderBookRepository.save(orderBook);
            this.eventEmitter.emit(this.EVENT_ORDER_UPDATED, {
                originalOrder,
                updatedOrder: this.toDto(updatedOrder, [
                    'details',
                    'details.product',
                    'user',
                ]),
                userId,
            });
            this.logger.log(`Đã cập nhật thành công lệnh ${id}`);
            return this.toDto(updatedOrder, ['details', 'details.product', 'user']);
        }
        catch (error) {
            this.logger.error(`Lỗi khi cập nhật lệnh: ${error.message}`, error.stack);
            if (error instanceof common_1.NotFoundException ||
                error instanceof common_1.BadRequestException) {
                throw error;
            }
            throw new common_1.InternalServerErrorException(`Không thể cập nhật lệnh: ${error.message}`);
        }
    }
    async validateUpdatePermissions(id) {
        const orderBook = await this.findByIdOrFail(id, [
            'details',
            'details.product',
            'user',
        ]);
        if (orderBook.status === order_status_enum_1.OrderStatus.COMPLETED) {
            throw new common_1.BadRequestException(`Lệnh ${id} đã hoàn thành, không thể chỉnh sửa`);
        }
        if (orderBook.status === order_status_enum_1.OrderStatus.CANCELLED) {
            throw new common_1.BadRequestException(`Lệnh ${id} đã bị hủy, không thể chỉnh sửa`);
        }
        if (orderBook.status !== order_status_enum_1.OrderStatus.DEPOSITED) {
            throw new common_1.BadRequestException(`Lệnh ${id} ở trạng thái ${orderBook.status}, không thể chỉnh sửa`);
        }
        this.logger.log(`Validation thành công cho lệnh ${id}, trạng thái: ${orderBook.status}`);
        return orderBook;
    }
    async updateAllowedFields(orderBook, updateOrderBookDto, userId) {
        let hasChanges = false;
        if (updateOrderBookDto.settlementDeadline !== undefined) {
            const newDeadline = new Date(updateOrderBookDto.settlementDeadline);
            if (newDeadline <= new Date()) {
                throw new common_1.BadRequestException('Thời hạn tất toán phải sau thời điểm hiện tại');
            }
            if (orderBook.settlementDeadline &&
                newDeadline <= orderBook.settlementDeadline) {
                throw new common_1.BadRequestException('Thời hạn tất toán mới phải sau thời hạn hiện tại');
            }
            const oldDeadline = orderBook.settlementDeadline;
            orderBook.settlementDeadline = newDeadline;
            orderBook.updatedBy = userId;
            orderBook.updatedAt = new Date();
            hasChanges = true;
            this.logger.log(`Cập nhật thời hạn tất toán từ ${oldDeadline} sang ${newDeadline} cho lệnh ${orderBook.id}`);
        }
        return hasChanges;
    }
    async bulkUpdate(updateOrderBookDtos, userId) {
        try {
            this.logger.log(`Đang cập nhật hàng loạt ${updateOrderBookDtos.length} lệnh`);
            if (updateOrderBookDtos.length === 0) {
                return [];
            }
            const updatedDtosResult = [];
            for (const dto of updateOrderBookDtos) {
                if (!dto.id) {
                    this.logger.warn('Bỏ qua bản ghi cập nhật do thiếu ID');
                    continue;
                }
                const updatedDto = await this.update(dto.id, dto, userId);
                updatedDtosResult.push(updatedDto);
            }
            this.logger.log(`Đã cập nhật thành công ${updatedDtosResult.length} lệnh`);
            return updatedDtosResult;
        }
        catch (error) {
            this.logger.error(`Lỗi khi cập nhật hàng loạt lệnh: ${error.message}`, error.stack);
            throw new common_1.InternalServerErrorException(`Không thể cập nhật hàng loạt lệnh: ${error.message}`);
        }
    }
    async toggleStatus(id, userId) {
        try {
            this.logger.log(`Đang chuyển đổi trạng thái cho lệnh với ID: ${id}`);
            const orderBook = await this.orderBookRepository.findOne({
                where: { id, isDeleted: false },
            });
            if (!orderBook) {
                throw new common_1.NotFoundException(`Không tìm thấy lệnh với ID ${id}`);
            }
            if (!orderBook.status) {
                throw new common_1.BadRequestException('Trường status không tồn tại');
            }
            orderBook.status =
                orderBook.status === order_status_enum_1.OrderStatus.COMPLETED
                    ? order_status_enum_1.OrderStatus.DEPOSITED
                    : order_status_enum_1.OrderStatus.COMPLETED;
            orderBook.updatedBy = userId;
            const updatedOrderBook = await this.orderBookRepository.save(orderBook);
            const refreshedOrderBook = await this.orderBookRepository.findOne({
                where: { id },
                relations: ['details', 'details.token', 'user', 'contract'],
            });
            if (!refreshedOrderBook) {
                throw new common_1.NotFoundException(`Không tìm thấy lệnh sau khi cập nhật trạng thái với ID ${id}`);
            }
            const dto = this.toDto(refreshedOrderBook, [
                'details',
                'details.token',
                'user',
                'contract',
            ]);
            this.eventEmitter.emit(this.EVENT_ORDER_STATUS_TOGGLED, dto);
            this.logger.log(`Đã chuyển đổi trạng thái lệnh với ID: ${id} sang ${orderBook.status}`);
            return dto;
        }
        catch (error) {
            this.logger.error(`Lỗi khi chuyển đổi trạng thái lệnh: ${error.message}`, error.stack);
            if (error instanceof common_1.NotFoundException ||
                error instanceof common_1.BadRequestException) {
                throw error;
            }
            throw new common_1.InternalServerErrorException(`Không thể chuyển đổi trạng thái lệnh: ${error.message}`);
        }
    }
    async updateOrderStatusPending(orderBookId, userId) {
        try {
            this.logger.log(`Cập nhật trạng thái lệnh ${orderBookId} thành PENDING`);
            const orderBook = await this.orderBookRepository.findOne({
                where: { id: orderBookId, isDeleted: false },
                relations: ['details', 'details.token'],
            });
            if (!orderBook) {
                throw new common_1.NotFoundException(`Không tìm thấy lệnh với ID ${orderBookId}`);
            }
            this.processOrderStatusUpdate(orderBook, order_status_enum_1.OrderStatus.DEPOSITED);
            orderBook.updatedBy = userId;
            return this.orderBookRepository.save(orderBook);
        }
        catch (error) {
            this.logger.error(`Lỗi khi cập nhật trạng thái lệnh: ${error.message}`, error.stack);
            if (error instanceof common_1.NotFoundException) {
                throw error;
            }
            throw new common_1.InternalServerErrorException(`Không thể cập nhật trạng thái lệnh: ${error.message}`);
        }
    }
    async updateOrderStatusCompleted(orderBookId, userId, settlementData) {
        try {
            this.logger.log(`Bắt đầu tất toán lệnh ${orderBookId} - thanh toán lần 2`);
            this.logger.log(`Dữ liệu tất toán: ${JSON.stringify(settlementData)}`);
            const validationResult = await this.validateSettlementInput(orderBookId, settlementData);
            const calculationResult = await this.calculateSettlementAmounts(validationResult.order, settlementData);
            await this.updateOrderForSettlement(validationResult.order, calculationResult, settlementData);
            await this.processSettlementPayments(validationResult.order, validationResult.wallet, calculationResult, settlementData?.userId ? settlementData.userId : userId);
            await this.processSettlementCommissions(validationResult.order, calculationResult, settlementData?.userId ? settlementData.userId : userId);
            return await this.finalizeSettlement(validationResult.order, calculationResult, settlementData?.userId ? settlementData.userId : userId);
        }
        catch (error) {
            this.logger.error(`Lỗi khi tất toán lệnh: ${error.message}`, error.stack);
            if (error instanceof common_1.NotFoundException ||
                error instanceof common_1.BadRequestException ||
                error instanceof localized_http_exception_1.InsufficientBalanceException ||
                error instanceof localized_http_exception_1.InsufficientTokenException) {
                throw error;
            }
            throw new common_1.InternalServerErrorException(`Không thể tất toán lệnh: ${error.message}`);
        }
    }
    async validateSettlementInput(orderBookId, settlementData) {
        const order = await this.orderBookRepository.findOne({
            where: { id: orderBookId, isDeleted: false },
            relations: ['details', 'details.product', 'user'],
        });
        if (!order) {
            throw new common_1.NotFoundException(`Không tìm thấy lệnh với ID ${orderBookId}`);
        }
        if (order.status === order_status_enum_1.OrderStatus.COMPLETED) {
            throw new common_1.BadRequestException(`Lệnh ${orderBookId} đã được tất toán`);
        }
        if (order.status !== order_status_enum_1.OrderStatus.DEPOSITED) {
            throw new common_1.BadRequestException(`Lệnh ${orderBookId} không ở trạng thái có thể tất toán`);
        }
        let wallet = null;
        if (settlementData?.userId) {
            wallet = await this.readWalletService.findByUserId(settlementData.userId);
        }
        else {
            wallet = await this.readWalletService.findByUserId(order.userId);
        }
        if (!wallet) {
            throw new common_1.NotFoundException(`Không tìm thấy ví của người dùng ${settlementData?.userId || order.userId}`);
        }
        this.logger.log(`Validation thành công cho lệnh ${orderBookId}, loại: ${order.orderType}, businessType: ${order.businessType}`);
        return { order, wallet };
    }
    async calculateSettlementAmounts(order, settlementData) {
        const newTotalPrice = settlementData?.price || order.totalPrice;
        const daysSinceCreated = Math.floor((new Date().getTime() - order.createdAt.getTime()) /
            (1000 * 60 * 60 * 24));
        const storageFeePerDay = 10000;
        const storageFeeAmount = daysSinceCreated * storageFeePerDay;
        let finalBusinessType = order.businessType;
        if (settlementData?.isImmediateDelivery) {
            finalBusinessType = business_type_enum_1.BusinessType.IMMEDIATE_DELIVERY;
        }
        let settlementAmount = 0;
        if (order.orderType === order_type_enum_1.OrderType.BUY) {
            if (finalBusinessType === business_type_enum_1.BusinessType.IMMEDIATE_DELIVERY) {
                settlementAmount =
                    newTotalPrice - (order.depositPrice || 0) + storageFeeAmount;
            }
            else {
                settlementAmount =
                    newTotalPrice - (order.depositPrice || 0) + storageFeeAmount;
            }
        }
        else if (order.orderType === order_type_enum_1.OrderType.SELL) {
            const priceDifference = (order.totalPrice || 0) - newTotalPrice;
            settlementAmount =
                (order.depositPrice || 0) + priceDifference - storageFeeAmount;
        }
        this.logger.log(`Tính toán tất toán: giá mới=${newTotalPrice}, phí lưu kho=${storageFeeAmount} (${daysSinceCreated} ngày), số tiền tất toán=${settlementAmount}`);
        return {
            newTotalPrice,
            settlementAmount,
            storageFeeAmount,
            finalBusinessType,
        };
    }
    async updateOrderForSettlement(order, calculationResult, settlementData) {
        order.totalPrice = calculationResult.newTotalPrice;
        order.storageFee = calculationResult.storageFeeAmount;
        if (calculationResult.finalBusinessType !== order.businessType) {
            order.businessType = calculationResult.finalBusinessType;
            this.logger.log(`Chuyển đổi businessType từ ${order.businessType} sang ${calculationResult.finalBusinessType}`);
        }
        this.logger.log(`Cập nhật lệnh: giá mới=${calculationResult.newTotalPrice}, phí lưu kho=${calculationResult.storageFeeAmount}`);
    }
    async processSettlementPayments(order, wallet, calculationResult, userId) {
        await this.validateSettlementWalletBalance(order, wallet, calculationResult.settlementAmount);
        if (order.orderType === order_type_enum_1.OrderType.BUY) {
            await this.processBuySettlementPayment(order, wallet, calculationResult, userId);
        }
        else if (order.orderType === order_type_enum_1.OrderType.SELL) {
            await this.processSellSettlementPayment(order, wallet, calculationResult, userId);
        }
    }
    async validateSettlementWalletBalance(order, wallet, settlementAmount) {
        if (settlementAmount > 0 && wallet.balance < settlementAmount) {
            throw new localized_http_exception_1.InsufficientBalanceException(wallet.balance, settlementAmount, {
                userId: order.userId,
                orderType: order.orderType,
                walletId: wallet.id,
                orderId: order.id,
            });
        }
    }
    async processBuySettlementPayment(order, wallet, calculationResult, userId) {
        if (calculationResult.settlementAmount > 0) {
            await this.updateWalletService.updateBalance(wallet.id, transaction_type_enum_1.TransactionType.DEBIT, calculationResult.settlementAmount, `ORD-${order.businessCode || order.id}`, `Thanh toán tất toán lệnh mua #${order.id}`, userId);
            this.logger.log(`Đã trừ ${calculationResult.settlementAmount} từ ví cho tất toán lệnh MUA #${order.id}`);
        }
        else if (calculationResult.settlementAmount < 0) {
            await this.updateWalletService.updateBalance(wallet.id, transaction_type_enum_1.TransactionType.CREDIT, Math.abs(calculationResult.settlementAmount), `ORD-${order.businessCode || order.id}`, `Hoàn tiền thừa tất toán lệnh mua #${order.id}`, userId);
            this.logger.log(`Đã hoàn ${Math.abs(calculationResult.settlementAmount)} vào ví cho tất toán lệnh MUA #${order.id}`);
        }
    }
    async processSellSettlementPayment(order, wallet, calculationResult, userId) {
        if (calculationResult.settlementAmount > 0) {
            await this.updateWalletService.updateBalance(wallet.id, transaction_type_enum_1.TransactionType.CREDIT, calculationResult.settlementAmount, `ORD-${order.businessCode || order.id}`, `Thanh toán tất toán lệnh bán khống #${order.id}`, userId);
            this.logger.log(`Đã cộng ${calculationResult.settlementAmount} vào ví cho tất toán lệnh BÁN #${order.id}`);
        }
        else if (calculationResult.settlementAmount < 0) {
            await this.updateWalletService.updateBalance(wallet.id, transaction_type_enum_1.TransactionType.DEBIT, Math.abs(calculationResult.settlementAmount), `ORD-${order.businessCode || order.id}`, `Thanh toán lỗ tất toán lệnh bán khống #${order.id}`, userId);
            this.logger.log(`Đã trừ ${Math.abs(calculationResult.settlementAmount)} từ ví cho tất toán lệnh BÁN #${order.id}`);
        }
    }
    async processSettlementCommissions(order, calculationResult, userId) {
        try {
            if (calculationResult.settlementAmount > 0) {
                this.logger.log(`Xử lý hoa hồng tất toán: ${calculationResult.settlementAmount} (tỷ lệ 40%)`);
                await this.commissionProcessorService.processSettlementFeeCommission(order.userId, calculationResult.settlementAmount, userId);
                this.logger.log(`Đã xử lý thành công hoa hồng tất toán: ${calculationResult.settlementAmount}`);
            }
            else {
                this.logger.log(`Không xử lý hoa hồng do số tiền tất toán <= 0: ${calculationResult.settlementAmount}`);
            }
        }
        catch (commissionError) {
            this.logger.error(`Lỗi khi xử lý hoa hồng tất toán: ${commissionError.message}`, commissionError.stack);
        }
    }
    async finalizeSettlement(order, calculationResult, userId) {
        order.status = order_status_enum_1.OrderStatus.COMPLETED;
        order.updatedBy = userId;
        order.settlementAt = new Date();
        if (calculationResult.finalBusinessType === business_type_enum_1.BusinessType.IMMEDIATE_DELIVERY) {
            order.approveStatus = approve_status_enum_1.ApproveStatus.PENDING;
            this.logger.log(`Chuyển đổi lệnh ${order.id} sang giao ngay, thiết lập trạng thái phê duyệt PENDING`);
        }
        const updatedOrder = await this.orderBookRepository.save(order);
        if (calculationResult.finalBusinessType === business_type_enum_1.BusinessType.IMMEDIATE_DELIVERY) {
            await this.processAssetManagement(updatedOrder, userId);
        }
        const dto = this.toDto(updatedOrder, [
            'details',
            'details.product',
            'user',
        ]);
        this.eventEmitter.emit(this.EVENT_ORDER_UPDATED, dto);
        this.logger.log(`Hoàn thành tất toán lệnh ${updatedOrder.id} với trạng thái ${updatedOrder.status}`);
        return updatedOrder;
    }
    async processAssetManagement(order, userId) {
        try {
            if (order.orderType === order_type_enum_1.OrderType.BUY) {
                await this.handleBuyOrderTokenAsset(order, userId);
            }
            else if (order.orderType === order_type_enum_1.OrderType.SELL) {
                await this.handleSellOrderTokenAsset(order, userId);
            }
        }
        catch (error) {
            this.logger.error(`Lỗi khi xử lý tài sản: ${error.message}`, error.stack);
            if (error instanceof common_1.BadRequestException) {
                throw error;
            }
            throw new common_1.InternalServerErrorException(`Lỗi khi xử lý tài sản: ${error.message}`);
        }
    }
    async updateOrderStatusWaitPayment(orderBookId, userId) {
        try {
            this.logger.log(`Cập nhật trạng thái lệnh ${orderBookId} thành WAIT_PAYMENT`);
            const orderBook = await this.orderBookRepository.findOne({
                where: { id: orderBookId, isDeleted: false },
                relations: ['details', 'details.token'],
            });
            if (!orderBook) {
                throw new common_1.NotFoundException(`Không tìm thấy lệnh với ID ${orderBookId}`);
            }
            this.processOrderStatusUpdate(orderBook, order_status_enum_1.OrderStatus.DEPOSITED);
            orderBook.updatedBy = userId;
            return this.orderBookRepository.save(orderBook);
        }
        catch (error) {
            this.logger.error(`Lỗi khi cập nhật trạng thái lệnh: ${error.message}`, error.stack);
            if (error instanceof common_1.NotFoundException) {
                throw error;
            }
            throw new common_1.InternalServerErrorException(`Không thể cập nhật trạng thái lệnh: ${error.message}`);
        }
    }
    processOrderStatusUpdate(orderBook, newStatus) {
        if (orderBook.status === newStatus) {
            return;
        }
        const originalStatus = orderBook.status;
        orderBook.status = newStatus;
        this.eventEmitter.emit(this.EVENT_ORDER_STATUS_TOGGLED, {
            orderId: orderBook.id,
            originalStatus,
            newStatus,
            order: this.toDto(orderBook, ['details', 'details.token']),
        });
    }
    async handleBuyOrderTokenAsset(order, userId) {
        try {
            this.logger.debug(`Xử lý lệnh mua: orderID=${order.id}, userID=${order.userId}`);
            if (!order.details || order.details.length === 0) {
                throw new common_1.BadRequestException(`Lệnh không có chi tiết token để xử lý`);
            }
            const existingTokenAssets = await this.tokenAssetService.findAll({
                limit: 100,
                page: 1,
                filter: undefined,
                search: undefined,
                sortBy: undefined,
                relations: ['token', 'user'],
            });
            for (const detail of order.details) {
                const userTokenAssets = existingTokenAssets.data.filter((asset) => asset.userId === order.userId &&
                    asset.productId === detail.productId);
                const volume = parseFloat(detail.quantity.toString());
                if (userTokenAssets.length > 0) {
                    const existingAsset = userTokenAssets[0];
                    const assetAmount = parseFloat(existingAsset.amount.toString());
                    if (isNaN(assetAmount) || isNaN(volume)) {
                        throw new common_1.BadRequestException(`Số lượng không hợp lệ. Asset amount: ${existingAsset.amount}, Detail volume: ${detail.quantity}`);
                    }
                    const newAmount = assetAmount + volume;
                    const updateDto = {
                        id: existingAsset.id,
                        amount: newAmount,
                        updatedBy: userId,
                    };
                    await this.tokenAssetService.update(existingAsset.id, updateDto);
                    this.logger.log(`Đã cập nhật token asset ${existingAsset.id} cho token ${detail.productId}`);
                }
                else {
                    const createDto = {
                        userId: order.userId,
                        productId: detail.productId,
                        amount: volume,
                    };
                    await this.tokenAssetService.create(createDto);
                    this.logger.log(`Đã tạo mới token asset cho token ${detail.productId}`);
                }
            }
        }
        catch (error) {
            this.logger.error(`Lỗi khi xử lý token asset cho lệnh mua: ${error.message}`, error.stack);
            if (error instanceof common_1.BadRequestException ||
                error instanceof localized_http_exception_1.InsufficientBalanceException ||
                error instanceof localized_http_exception_1.InsufficientTokenException) {
                throw error;
            }
            throw new common_1.InternalServerErrorException(`Lỗi khi xử lý token asset: ${error.message}`);
        }
    }
    async handleSellOrderTokenAsset(order, userId) {
        try {
            this.logger.debug(`Xử lý lệnh bán: orderID=${order.id}, userID=${order.userId}`);
            if (!order.details || order.details.length === 0) {
                throw new common_1.BadRequestException(`Lệnh không có chi tiết token để xử lý`);
            }
            const existingTokenAssets = await this.tokenAssetService.findAll({
                limit: 100,
                page: 1,
                filter: undefined,
                search: undefined,
                sortBy: undefined,
                relations: ['token', 'user'],
            });
            for (const detail of order.details) {
                const userTokenAssets = existingTokenAssets.data.filter((asset) => asset.userId === order.userId &&
                    asset.productId === detail.productId);
                const volume = parseFloat(detail.quantity.toString());
                if (userTokenAssets.length > 0) {
                    const existingAsset = userTokenAssets[0];
                    const assetAmount = parseFloat(existingAsset.amount.toString());
                    if (assetAmount < volume) {
                        throw new localized_http_exception_1.InsufficientTokenException(assetAmount, volume, detail.productId, {
                            userId: order.userId,
                            orderType: order.orderType,
                            tokenAssetId: existingAsset.id,
                            orderId: order.id,
                        });
                    }
                    const newAmount = assetAmount - volume;
                    const updateDto = {
                        id: existingAsset.id,
                        amount: newAmount,
                        updatedBy: userId,
                    };
                    await this.tokenAssetService.update(existingAsset.id, updateDto);
                    this.logger.log(`Đã cập nhật token asset ${existingAsset.id} cho token ${detail.productId}, số lượng còn lại: ${newAmount}`);
                }
                else {
                    throw new common_1.BadRequestException(`Người dùng không có token ${detail.productId} để bán.`);
                }
            }
        }
        catch (error) {
            this.logger.error(`Lỗi khi xử lý token asset cho lệnh bán: ${error.message}`, error.stack);
            if (error instanceof common_1.BadRequestException ||
                error instanceof localized_http_exception_1.InsufficientBalanceException ||
                error instanceof localized_http_exception_1.InsufficientTokenException) {
                throw error;
            }
            throw new common_1.InternalServerErrorException(`Lỗi khi xử lý token asset: ${error.message}`);
        }
    }
    async extendSettlement(id, userId) {
        try {
            this.logger.log(`Gia hạn thời gian tất toán cho lệnh ${id}`);
            const orderBook = await this.findByIdOrFail(id, [
                'details',
                'details.token',
                'user',
            ]);
            const currentSettlementDeadline = orderBook.settlementDeadline || new Date();
            const newSettlementDeadline = new Date(currentSettlementDeadline);
            newSettlementDeadline.setDate(newSettlementDeadline.getDate() + 15);
            orderBook.settlementDeadline = newSettlementDeadline;
            orderBook.updatedBy = userId;
            orderBook.updatedAt = new Date();
            const updatedOrder = await this.orderBookRepository.save(orderBook);
            const extensionFee = 100000;
            const wallet = await this.readWalletService.findByUserId(orderBook.userId);
            if (!wallet) {
                throw new common_1.BadRequestException('Không tìm thấy ví của người dùng');
            }
            await this.updateWalletService.updateBalance(wallet.id, transaction_type_enum_1.TransactionType.DEBIT, extensionFee, `ORD-${orderBook.businessCode || orderBook.id}`, `Phí gia hạn thời gian tất toán cho lệnh #${orderBook.id}`, userId);
            this.eventEmitter.emit(this.EVENT_ORDER_UPDATED, {
                originalOrder: this.toDto(orderBook, [
                    'details',
                    'details.token',
                    'user',
                ]),
                updatedOrder: this.toDto(updatedOrder, [
                    'details',
                    'details.token',
                    'user',
                ]),
                userId,
            });
            return this.toDto(updatedOrder, ['details', 'details.token', 'user']);
        }
        catch (error) {
            this.logger.error(`Lỗi khi gia hạn thời gian tất toán: ${error.message}`, error.stack);
            if (error instanceof common_1.NotFoundException ||
                error instanceof common_1.BadRequestException) {
                throw error;
            }
            throw new common_1.InternalServerErrorException(`Không thể gia hạn thời gian tất toán: ${error.message}`);
        }
    }
    async updateOrderStatusCancelled(id, userId) {
        try {
            this.logger.log(`Cập nhật trạng thái lệnh ${id} thành CANCELLED`);
            const orderBook = await this.findByIdOrFail(id, [
                'details',
                'details.token',
                'user',
            ]);
            orderBook.status = order_status_enum_1.OrderStatus.CANCELLED;
            orderBook.updatedBy = userId;
            orderBook.updatedAt = new Date();
            const updatedOrder = await this.orderBookRepository.save(orderBook);
            this.logger.log(`Đã hủy lệnh ${id} mà không hoàn cọc`);
            return updatedOrder;
        }
        catch (error) {
            this.logger.error(`Lỗi khi cập nhật trạng thái lệnh thành CANCELLED: ${error.message}`, error.stack);
            if (error instanceof common_1.NotFoundException) {
                throw error;
            }
            throw new common_1.InternalServerErrorException(`Không thể cập nhật trạng thái lệnh thành CANCELLED: ${error.message}`);
        }
    }
    async approveOrder(id, userId) {
        try {
            this.logger.log(`Phê duyệt lệnh ${id} bởi người dùng ${userId}`);
            const order = await this.orderBookRepository.findOne({
                where: { id, isDeleted: false },
                relations: ['details', 'details.token', 'user'],
            });
            if (!order) {
                throw new common_1.NotFoundException(`Không tìm thấy lệnh với ID ${id}`);
            }
            if (order.businessType !== business_type_enum_1.BusinessType.IMMEDIATE_DELIVERY) {
                throw new common_1.BadRequestException(`Lệnh này không phải là lệnh bạc vật chất`);
            }
            if (order.approveStatus !== approve_status_enum_1.ApproveStatus.PENDING) {
                throw new common_1.BadRequestException(`Lệnh này không ở trạng thái chờ phê duyệt`);
            }
            order.approveStatus = approve_status_enum_1.ApproveStatus.APPROVED;
            order.approvedAt = new Date();
            order.updatedBy = userId;
            const updatedOrder = await this.orderBookRepository.save(order);
            try {
                if (order.details && order.details.length > 0) {
                    for (const detail of order.details) {
                        const ecomProductId = detail.productId;
                        const volume = parseFloat(detail.quantity.toString());
                        const tokenAsset = await this.tokenAssetService.findByUserAndToken(order.userId, ecomProductId);
                        if (!tokenAsset) {
                            throw new common_1.BadRequestException(`Người dùng không sở hữu token ${ecomProductId}`);
                        }
                        const currentAmount = parseFloat(tokenAsset.amount.toString());
                        if (currentAmount < volume) {
                            throw new localized_http_exception_1.InsufficientTokenException(currentAmount, volume, ecomProductId, {
                                userId: order.userId,
                                orderType: order.orderType,
                                tokenAssetId: tokenAsset.id,
                            });
                        }
                        const newAmount = currentAmount - volume;
                        await this.tokenAssetService.update(tokenAsset.id, {
                            id: tokenAsset.id,
                            amount: newAmount,
                            updatedBy: userId,
                        });
                        this.logger.log(`Đã cập nhật token asset ${tokenAsset.id} cho token ${ecomProductId}, số lượng còn lại: ${newAmount}`);
                    }
                }
            }
            catch (error) {
                this.logger.error(`Lỗi khi xử lý token asset: ${error.message}`, error.stack);
                throw error;
            }
            this.logger.log(`Đã phê duyệt thành công lệnh ${id}, token đã được trừ khỏi tài sản của người dùng`);
            this.logger.log(`Đã phê duyệt thành công lệnh ${id}`);
            return updatedOrder;
        }
        catch (error) {
            this.logger.error(`Lỗi khi phê duyệt lệnh: ${error.message}`, error.stack);
            if (error instanceof common_1.NotFoundException ||
                error instanceof common_1.BadRequestException ||
                error instanceof localized_http_exception_1.InsufficientTokenException) {
                throw error;
            }
            throw new common_1.InternalServerErrorException(`Không thể phê duyệt lệnh: ${error.message}`);
        }
    }
    async duplicate(id, userId) {
        const sourceOrderBook = await this.findByIdOrFail(id, [
            'details',
            'details.token',
        ]);
        const newOrderBook = this.orderBookRepository.create({
            orderType: sourceOrderBook.orderType,
            totalPrice: sourceOrderBook.totalPrice,
            status: order_status_enum_1.OrderStatus.DEPOSITED,
            userId: sourceOrderBook.userId,
            createdBy: userId,
            updatedBy: userId,
        });
        const savedOrderBook = await this.orderBookRepository.save(newOrderBook);
        if (sourceOrderBook.details && sourceOrderBook.details.length > 0) {
            const newDetails = sourceOrderBook.details.map((detail) => ({
                orderBookId: savedOrderBook.id,
                ecomProductId: detail.productId,
                price: detail.price,
                volume: detail.quantity,
                totalPrice: detail.totalPrice,
            }));
            const details = await this.orderBookDetailRepository.save(newDetails);
            savedOrderBook.details = details;
        }
        this.eventEmitter.emit(this.EVENT_ORDER_DUPLICATED, {
            sourceOrderId: id,
            newOrderId: savedOrderBook.id,
            newOrder: this.toDto(savedOrderBook, ['details', 'details.token']),
            userId,
        });
        return this.toDto(savedOrderBook, ['details', 'details.token']);
    }
};
exports.UpdateOrderBookService = UpdateOrderBookService;
__decorate([
    (0, typeorm_transactional_1.Transactional)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, update_order_book_dto_1.UpdateOrderBookDto, String]),
    __metadata("design:returntype", Promise)
], UpdateOrderBookService.prototype, "update", null);
__decorate([
    (0, typeorm_transactional_1.Transactional)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Array, String]),
    __metadata("design:returntype", Promise)
], UpdateOrderBookService.prototype, "bulkUpdate", null);
__decorate([
    (0, typeorm_transactional_1.Transactional)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String]),
    __metadata("design:returntype", Promise)
], UpdateOrderBookService.prototype, "toggleStatus", null);
__decorate([
    (0, typeorm_transactional_1.Transactional)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String]),
    __metadata("design:returntype", Promise)
], UpdateOrderBookService.prototype, "updateOrderStatusPending", null);
__decorate([
    (0, typeorm_transactional_1.Transactional)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String, settlement_order_book_dto_1.SettlementOrderBookDto]),
    __metadata("design:returntype", Promise)
], UpdateOrderBookService.prototype, "updateOrderStatusCompleted", null);
__decorate([
    (0, typeorm_transactional_1.Transactional)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String]),
    __metadata("design:returntype", Promise)
], UpdateOrderBookService.prototype, "updateOrderStatusWaitPayment", null);
__decorate([
    (0, typeorm_transactional_1.Transactional)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String]),
    __metadata("design:returntype", Promise)
], UpdateOrderBookService.prototype, "extendSettlement", null);
__decorate([
    (0, typeorm_transactional_1.Transactional)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String]),
    __metadata("design:returntype", Promise)
], UpdateOrderBookService.prototype, "updateOrderStatusCancelled", null);
__decorate([
    (0, typeorm_transactional_1.Transactional)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String]),
    __metadata("design:returntype", Promise)
], UpdateOrderBookService.prototype, "approveOrder", null);
exports.UpdateOrderBookService = UpdateOrderBookService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(order_book_entity_1.OrderBook)),
    __param(1, (0, typeorm_1.InjectRepository)(order_book_detail_entity_1.OrderBookDetail)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        typeorm_2.Repository,
        event_emitter_1.EventEmitter2,
        asset_service_1.AssetService,
        update_wallet_service_1.UpdateWalletService,
        read_wallet_service_1.ReadWalletService,
        commission_processor_service_1.CommissionProcessorService])
], UpdateOrderBookService);
//# sourceMappingURL=update.order-book.service.js.map
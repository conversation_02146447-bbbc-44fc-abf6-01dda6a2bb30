import { DeleteSystemConfigService } from '../services/delete.system-config.service';
import { SystemConfigDto } from '../dto/system-config.dto';
export declare class SystemConfigDeleteController {
    private readonly systemConfigService;
    private readonly logger;
    constructor(systemConfigService: DeleteSystemConfigService);
    softDelete(id: string, userId: string): Promise<SystemConfigDto>;
    softDeleteByKey(key: string, userId: string): Promise<SystemConfigDto>;
    restore(id: string): Promise<SystemConfigDto>;
    hardDelete(id: string): Promise<boolean>;
}

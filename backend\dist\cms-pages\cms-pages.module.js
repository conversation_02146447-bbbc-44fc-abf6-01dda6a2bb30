"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CmsPagesModule = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const cms_pages_entity_1 = require("./entity/cms-pages.entity");
const base_cms_pages_service_1 = require("./services/base.cms-pages.service");
const create_cms_pages_service_1 = require("./services/create.cms-pages.service");
const read_cms_pages_service_1 = require("./services/read.cms-pages.service");
const update_cms_pages_service_1 = require("./services/update.cms-pages.service");
const delete_cms_pages_service_1 = require("./services/delete.cms-pages.service");
const controllers_1 = require("./controllers");
let CmsPagesModule = class CmsPagesModule {
    constructor() {
    }
};
exports.CmsPagesModule = CmsPagesModule;
exports.CmsPagesModule = CmsPagesModule = __decorate([
    (0, common_1.Module)({
        imports: [
            typeorm_1.TypeOrmModule.forFeature([cms_pages_entity_1.CmsPages]),
        ],
        controllers: [
            controllers_1.CreateCmsPagesController,
            controllers_1.ReadCmsPagesController,
            controllers_1.ReadCmsPagesPublicController,
            controllers_1.UpdateCmsPagesController,
            controllers_1.DeleteCmsPagesController,
        ],
        providers: [
            base_cms_pages_service_1.BaseCmsPagesService,
            create_cms_pages_service_1.CreateCmsPagesService,
            read_cms_pages_service_1.ReadCmsPagesService,
            update_cms_pages_service_1.UpdateCmsPagesService,
            delete_cms_pages_service_1.DeleteCmsPagesService,
        ],
        exports: [
            base_cms_pages_service_1.BaseCmsPagesService,
            create_cms_pages_service_1.CreateCmsPagesService,
            read_cms_pages_service_1.ReadCmsPagesService,
            update_cms_pages_service_1.UpdateCmsPagesService,
            delete_cms_pages_service_1.DeleteCmsPagesService,
        ],
    }),
    __metadata("design:paramtypes", [])
], CmsPagesModule);
//# sourceMappingURL=cms-pages.module.js.map
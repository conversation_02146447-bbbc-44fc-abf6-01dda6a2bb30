# Test Case: Multiple Sticky Left Columns

## 🎯 **M<PERSON><PERSON> tiêu test:**
<PERSON><PERSON><PERSON> bảo khi có nhiều sticky left columns, chúng được xếp chồng lên nhau đúng thứ tự, không bị đè lên nhau.

## 🧪 **Test Cases:**

### **Test 1: Multiple sticky left columns positioning**
- ✅ **Checkbox column**: `left: 0px` (width: 40px)
- ✅ **Column thứ 2 sticky left**: `left: 40px` (width: column_width)
- ✅ **Column thứ 3 sticky left**: `left: 40px + column_2_width`
- ✅ **Khi scroll ngang**: Tất cả sticky left columns vẫn cố định

### **Test 2: Visual stacking behavior**
- ✅ Không có column nào bị đè lên nhau
- ✅ Mỗi sticky column có border để phân biệt
- ✅ Box shadow hiển thị đúng cho từng column

### **Test 3: Dynamic width calculation**
- ✅ Khi resize columns: Left position đư<PERSON><PERSON> t<PERSON>h lại đúng
- ✅ Khi ẩn/hiện columns: Position được cập nhật
- ✅ Responsive behavior vẫn hoạt động

## 🔧 **Cách test:**

### **1. Manual Test:**
```bash
# 1. Mở trang có datatable với nhiều sticky left columns
# 2. Scroll ngang để test stacking behavior
# 3. Verify không có column nào bị đè
# 4. Check left positions trong DevTools
```

### **2. Browser DevTools Check:**
```javascript
// Check left positions
const stickyColumns = document.querySelectorAll('.sticky-left');
stickyColumns.forEach((col, index) => {
  console.log(`Column ${index}:`, {
    left: getComputedStyle(col).left,
    width: getComputedStyle(col).width,
    zIndex: getComputedStyle(col).zIndex
  });
});

// Expected output:
// Column 0 (checkbox): left: "0px", width: "40px"
// Column 1: left: "40px", width: "200px" 
// Column 2: left: "240px", width: "150px"
```

### **3. CSS Verification:**
```css
/* ✅ Checkbox column */
th[data-column-id="select"] { left: 0px; }

/* ✅ Other sticky left columns */
.sticky-left { 
  left: calculated_position; /* Tính toán động */
  position: sticky;
  z-index: 50;
}
```

## 🔧 **Logic Implementation:**

### **1. Cumulative Left Position Calculation:**
```typescript
const calculateStickyLeftPosition = (headers, currentIndex) => {
  let leftPosition = 0;
  for (let i = 0; i < currentIndex; i++) {
    const header = headers[i];
    const isSticky = header.column.columnDef.meta?.isSticky;
    const position = header.column.columnDef.meta?.position;
    if (isSticky && position === 'left') {
      leftPosition += header.getSize(); // Cộng dồn width
    }
  }
  return leftPosition;
};
```

### **2. Dynamic Style Application:**
```typescript
style={{
  ...(hasSticky && {
    position: 'sticky',
    [stickyPosition === 'right' ? 'right' : 'left']: 
      stickyPosition === 'right' ? 0 : leftPosition, // Dynamic left
    zIndex: 60,
  })
}}
```

## 🐛 **Vấn đề đã fix:**

### **❌ Trước khi fix:**
```css
/* SAI: Tất cả sticky left columns có left: 0 */
.sticky-left { left: 0; } /* Bị đè lên nhau */
```

### **✅ Sau khi fix:**
```css
/* ĐÚNG: Left position được tính toán động */
/* Checkbox: left: 0px */
/* Column 2: left: 40px */
/* Column 3: left: 240px */
```

## 📋 **Checklist:**

- [ ] Checkbox column: `left: 0px`
- [ ] Column thứ 2 sticky left: `left: 40px` (hoặc width của checkbox)
- [ ] Column thứ 3 sticky left: `left: cumulative_width`
- [ ] Không có column nào bị đè
- [ ] Scroll ngang: Tất cả sticky columns vẫn cố định
- [ ] Visual borders để phân biệt columns
- [ ] Z-index đúng thứ tự
- [ ] Performance không bị impact

## 🎉 **Expected Result:**
Khi có nhiều sticky left columns, chúng được **xếp chồng lên nhau** theo thứ tự từ trái sang phải, **không bị đè lên nhau**.

## 🔧 **Debug Tools:**
```javascript
// Helper để check positioning
function debugStickyColumns() {
  const headers = document.querySelectorAll('th.sticky-left');
  const cells = document.querySelectorAll('td.sticky-left');
  
  console.log('Header positions:');
  headers.forEach((h, i) => console.log(`${i}: left=${h.style.left}`));
  
  console.log('Cell positions:');
  cells.forEach((c, i) => console.log(`${i}: left=${c.style.left}`));
}
```

## 📝 **Notes:**
- Logic tính toán áp dụng cho cả header và tbody
- CSS debug borders giúp visualize stacking
- Z-index được set đúng thứ tự
- Performance optimized với memoization nếu cần

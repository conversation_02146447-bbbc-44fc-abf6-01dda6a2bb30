"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ReadCmsTagsService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const event_emitter_1 = require("@nestjs/event-emitter");
const base_cms_tags_service_1 = require("./base.cms-tags.service");
const cms_tags_entity_1 = require("../entity/cms-tags.entity");
let ReadCmsTagsService = class ReadCmsTagsService extends base_cms_tags_service_1.BaseCmsTagsService {
    tagRepository;
    dataSource;
    eventEmitter;
    constructor(tagRepository, dataSource, eventEmitter) {
        super(tagRepository, dataSource, eventEmitter);
        this.tagRepository = tagRepository;
        this.dataSource = dataSource;
        this.eventEmitter = eventEmitter;
    }
    async findAll(params) {
        try {
            this.logger.debug(`Đang tìm tất cả thẻ CMS với tham số: ${JSON.stringify(params)}`);
            const { limit, page, search } = params;
            const skip = (page - 1) * limit;
            let relationsArray = params.relationsArray || [];
            const requiredRelations = ['creator', 'updater', 'deleter'];
            requiredRelations.forEach(relation => {
                if (!relationsArray.includes(relation)) {
                    relationsArray.push(relation);
                }
            });
            const validatedRelations = this.validateRelations(relationsArray);
            const queryBuilder = this.tagRepository
                .createQueryBuilder('tag')
                .where('tag.isDeleted = :isDeleted', { isDeleted: false });
            if (search) {
                queryBuilder.andWhere(new typeorm_2.Brackets(qb => {
                    qb.where('LOWER(tag.name) LIKE LOWER(:search)', { search: `%${search}%` })
                        .orWhere('LOWER(tag.slug) LIKE LOWER(:search)', { search: `%${search}%` });
                }));
            }
            const sortOptions = params.sortOptions;
            if (sortOptions && sortOptions.length > 0) {
                const { field, order } = sortOptions[0];
                queryBuilder.orderBy(`tag.${field}`, order);
            }
            else {
                queryBuilder.orderBy('tag.name', 'ASC');
            }
            validatedRelations.forEach(relation => {
                queryBuilder.leftJoinAndSelect(`tag.${relation}`, relation);
            });
            queryBuilder.skip(skip).take(limit);
            const [tags, total] = await queryBuilder.getManyAndCount();
            const tagDtos = this.toDtos(tags);
            return {
                data: tagDtos,
                total,
            };
        }
        catch (error) {
            this.logger.error(`Lỗi khi tìm tất cả thẻ CMS: ${error.message}`, error.stack);
            if (error instanceof common_1.BadRequestException) {
                throw error;
            }
            throw new common_1.InternalServerErrorException(`Không thể lấy danh sách thẻ CMS: ${error.message}`);
        }
    }
    async count(filter) {
        try {
            const where = this.buildWhereClause(filter);
            return this.tagRepository.count({ where });
        }
        catch (error) {
            this.logger.error(`Lỗi khi đếm số lượng thẻ CMS: ${error.message}`, error.stack);
            throw new common_1.InternalServerErrorException(`Không thể đếm số lượng thẻ CMS: ${error.message}`);
        }
    }
    async findOne(id, relations = []) {
        try {
            this.logger.debug(`Đang tìm thẻ CMS theo ID: ${id}`);
            const validatedRelations = this.validateRelations(relations);
            try {
                const tag = await this.findById(id, validatedRelations, true);
                if (!tag) {
                    throw new common_1.NotFoundException(`Không tìm thấy thẻ với ID: ${id}`);
                }
                return this.toDto(tag);
            }
            catch (notFoundError) {
                if (notFoundError instanceof common_1.NotFoundException) {
                    return null;
                }
                throw notFoundError;
            }
        }
        catch (error) {
            this.logger.error(`Lỗi khi tìm thẻ CMS theo ID ${id}: ${error.message}`, error.stack);
            if (error instanceof common_1.NotFoundException) {
                return null;
            }
            throw new common_1.InternalServerErrorException(`Không thể lấy thông tin thẻ CMS: ${error.message}`);
        }
    }
    async findOneOrFail(id, relations = []) {
        try {
            this.logger.debug(`Đang tìm thẻ CMS theo ID: ${id}`);
            const validatedRelations = this.validateRelations(relations);
            const validRelationsToLoad = [...validatedRelations];
            if (!validRelationsToLoad.includes('creator')) {
                validRelationsToLoad.push('creator');
            }
            if (!validRelationsToLoad.includes('updater')) {
                validRelationsToLoad.push('updater');
            }
            const tag = await this.findById(id, validRelationsToLoad);
            if (!tag) {
                throw new common_1.NotFoundException(`Không tìm thấy thẻ với ID: ${id}`);
            }
            return this.toDto(tag);
        }
        catch (error) {
            this.logger.error(`Lỗi khi tìm thẻ CMS theo ID ${id}: ${error.message}`, error.stack);
            if (error instanceof common_1.NotFoundException) {
                throw error;
            }
            throw new common_1.InternalServerErrorException(`Không thể lấy thông tin thẻ CMS: ${error.message}`);
        }
    }
    async findBySlugPublic(slug) {
        try {
            this.logger.debug(`Đang tìm thẻ CMS theo slug: ${slug}`);
            const tag = await this.findBySlug(slug, false);
            if (!tag) {
                return null;
            }
            return this.toDto(tag);
        }
        catch (error) {
            this.logger.error(`Lỗi khi tìm thẻ theo slug: ${error.message}`, error.stack);
            throw new common_1.InternalServerErrorException(`Không thể tìm thẻ theo slug: ${error.message}`);
        }
    }
    async search(keyword, params) {
        try {
            this.logger.debug(`Đang tìm kiếm thẻ CMS với từ khóa: ${keyword}`);
            const queryBuilder = this.tagRepository
                .createQueryBuilder('tag')
                .where('tag.isDeleted = :isDeleted', { isDeleted: false });
            queryBuilder.andWhere(new typeorm_2.Brackets(qb => {
                qb.where('LOWER(tag.name) LIKE LOWER(:keyword)', { keyword: `%${keyword}%` })
                    .orWhere('LOWER(tag.slug) LIKE LOWER(:keyword)', { keyword: `%${keyword}%` });
            }));
            const sortOptions = params.sortOptions;
            if (sortOptions && sortOptions.length > 0) {
                const { field, order } = sortOptions[0];
                queryBuilder.orderBy(`tag.${field}`, order);
            }
            else {
                queryBuilder.orderBy('tag.name', 'ASC');
            }
            const skip = (params.page - 1) * params.limit;
            queryBuilder.skip(skip).take(params.limit);
            const [tags, total] = await queryBuilder.getManyAndCount();
            const tagDtos = this.toDtos(tags);
            return { data: tagDtos, total };
        }
        catch (error) {
            this.logger.error(`Lỗi khi tìm kiếm thẻ CMS: ${error.message}`, error.stack);
            throw new common_1.InternalServerErrorException(`Không thể tìm kiếm thẻ CMS: ${error.message}`);
        }
    }
    async findDeleted(params) {
        try {
            this.logger.debug(`Đang tìm các thẻ CMS đã bị xóa mềm với tham số: ${JSON.stringify(params)}`);
            const { limit, page } = params;
            const queryBuilder = this.tagRepository
                .createQueryBuilder('tag')
                .where('tag.isDeleted = :isDeleted', { isDeleted: true });
            if (params.search) {
                queryBuilder.andWhere(new typeorm_2.Brackets(qb => {
                    qb.where('LOWER(tag.name) LIKE LOWER(:search)', { search: `%${params.search}%` })
                        .orWhere('LOWER(tag.slug) LIKE LOWER(:search)', { search: `%${params.search}%` });
                }));
            }
            const sortOptions = params.sortOptions;
            if (sortOptions && sortOptions.length > 0) {
                const { field, order } = sortOptions[0];
                queryBuilder.orderBy(`tag.${field}`, order);
            }
            else {
                queryBuilder.orderBy('tag.deletedAt', 'DESC');
            }
            queryBuilder.leftJoinAndSelect('tag.creator', 'creator')
                .leftJoinAndSelect('tag.updater', 'updater')
                .leftJoinAndSelect('tag.deleter', 'deleter');
            const skip = (page - 1) * limit;
            queryBuilder.skip(skip).take(limit);
            const [tags, total] = await queryBuilder.getManyAndCount();
            const tagDtos = this.toDtos(tags);
            return { data: tagDtos, total };
        }
        catch (error) {
            this.logger.error(`Lỗi khi tìm thẻ CMS đã xóa: ${error.message}`, error.stack);
            throw new common_1.InternalServerErrorException(`Không thể lấy danh sách thẻ CMS đã xóa: ${error.message}`);
        }
    }
    async getStatistics() {
        try {
            const total = await this.tagRepository.count({
                where: { isDeleted: false },
            });
            const mostUsedQuery = await this.tagRepository
                .createQueryBuilder('tag')
                .select(['tag.name', 'tag.slug'])
                .where('tag.isDeleted = :isDeleted', { isDeleted: false })
                .orderBy('tag.createdAt', 'DESC')
                .limit(10)
                .getMany();
            const mostUsed = mostUsedQuery.map(tag => ({
                name: tag.name,
                slug: tag.slug,
                postsCount: 0,
            }));
            return {
                total,
                mostUsed,
            };
        }
        catch (error) {
            this.logger.error(`Lỗi khi lấy thống kê thẻ CMS: ${error.message}`, error.stack);
            throw new common_1.InternalServerErrorException(`Không thể lấy thống kê thẻ CMS: ${error.message}`);
        }
    }
    async getPopularTags(limit = 10) {
        try {
            this.logger.debug(`Đang lấy ${limit} thẻ phổ biến`);
            const tags = await this.tagRepository.find({
                where: { isDeleted: false },
                order: { createdAt: 'DESC' },
                take: limit,
            });
            return this.toDtos(tags);
        }
        catch (error) {
            this.logger.error(`Lỗi khi lấy thẻ phổ biến: ${error.message}`, error.stack);
            throw new common_1.InternalServerErrorException(`Không thể lấy thẻ phổ biến: ${error.message}`);
        }
    }
    async findByNames(names) {
        try {
            this.logger.debug(`Đang tìm thẻ theo danh sách tên: ${names.join(', ')}`);
            if (!names || names.length === 0) {
                return [];
            }
            const tags = await this.tagRepository
                .createQueryBuilder('tag')
                .where('tag.isDeleted = :isDeleted', { isDeleted: false })
                .andWhere('tag.name IN (:...names)', { names })
                .getMany();
            return this.toDtos(tags);
        }
        catch (error) {
            this.logger.error(`Lỗi khi tìm thẻ theo danh sách tên: ${error.message}`, error.stack);
            throw new common_1.InternalServerErrorException(`Không thể tìm thẻ theo danh sách tên: ${error.message}`);
        }
    }
};
exports.ReadCmsTagsService = ReadCmsTagsService;
exports.ReadCmsTagsService = ReadCmsTagsService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(cms_tags_entity_1.CmsTags)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        typeorm_2.DataSource,
        event_emitter_1.EventEmitter2])
], ReadCmsTagsService);
//# sourceMappingURL=read.cms-tags.service.js.map
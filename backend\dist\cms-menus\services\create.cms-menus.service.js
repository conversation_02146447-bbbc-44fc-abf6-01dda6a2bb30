"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreateCmsMenusService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const event_emitter_1 = require("@nestjs/event-emitter");
const typeorm_transactional_1 = require("typeorm-transactional");
const base_cms_menus_service_1 = require("./base.cms-menus.service");
const cms_menus_entity_1 = require("../entity/cms-menus.entity");
const create_cms_menu_dto_1 = require("../dto/create.cms-menu.dto");
let CreateCmsMenusService = class CreateCmsMenusService extends base_cms_menus_service_1.BaseCmsMenusService {
    menuRepository;
    dataSource;
    eventEmitter;
    constructor(menuRepository, dataSource, eventEmitter) {
        super(menuRepository, dataSource, eventEmitter);
        this.menuRepository = menuRepository;
        this.dataSource = dataSource;
        this.eventEmitter = eventEmitter;
    }
    async create(createDto, userId) {
        try {
            this.logger.debug(`Đang tạo menu CMS: ${JSON.stringify(createDto)}`);
            const existingMenuByName = await this.findByName(createDto.name, createDto.postType || 'post', false);
            if (existingMenuByName) {
                throw new common_1.ConflictException(`Tên menu "${createDto.name}" đã tồn tại cho loại post "${createDto.postType}"`);
            }
            const existingMenu = await this.findBySlug(createDto.slug, createDto.postType || 'post', false);
            if (existingMenu) {
                throw new common_1.ConflictException(`Slug "${createDto.slug}" đã tồn tại cho loại post "${createDto.postType}"`);
            }
            const menu = this.menuRepository.create({
                name: createDto.name,
                slug: createDto.slug,
                description: createDto.description,
                postType: createDto.postType,
                imageUrl: createDto.imageUrl,
                metaTitle: createDto.metaTitle,
                metaDescription: createDto.metaDescription,
                metaKeywords: createDto.metaKeywords,
                status: createDto.status,
                createdBy: userId,
                updatedBy: userId,
            });
            if (createDto.parentId) {
                const parent = await this.findById(createDto.parentId);
                if (parent) {
                    menu.parent = parent;
                }
            }
            const savedMenu = await this.menuRepository.save(menu);
            const menuDto = this.toDto(savedMenu);
            if (!menuDto) {
                throw new common_1.InternalServerErrorException('Không thể chuyển đổi entity thành DTO');
            }
            this.eventEmitter.emit(this.EVENT_MENU_CREATED, {
                menuId: menuDto.id,
                userId,
                newData: menuDto,
            });
            this.logger.debug(`Đã tạo menu CMS thành công với ID: ${menuDto.id}`);
            return menuDto;
        }
        catch (error) {
            this.logger.error(`Lỗi khi tạo menu CMS: ${error.message}`, error.stack);
            if (error instanceof common_1.ConflictException || error instanceof common_1.BadRequestException) {
                throw error;
            }
            throw new common_1.InternalServerErrorException(`Không thể tạo menu CMS: ${error.message}`);
        }
    }
    async bulkCreate(createDtos, userId) {
        try {
            this.logger.debug(`Đang tạo ${createDtos.length} menu CMS`);
            const menus = [];
            for (const createDto of createDtos) {
                const menu = await this.create(createDto, userId);
                menus.push(menu);
            }
            this.logger.debug(`Đã tạo ${menus.length} menu CMS thành công`);
            return menus;
        }
        catch (error) {
            this.logger.error(`Lỗi khi tạo nhiều menu CMS: ${error.message}`, error.stack);
            throw new common_1.InternalServerErrorException(`Không thể tạo nhiều menu CMS: ${error.message}`);
        }
    }
    async duplicate(id, userId) {
        try {
            this.logger.debug(`Đang nhân bản menu CMS với ID: ${id}`);
            const originalMenu = await this.findById(id);
            if (!originalMenu) {
                throw new common_1.BadRequestException(`Không tìm thấy menu với ID: ${id}`);
            }
            let newName = `${originalMenu.name} (Copy)`;
            let newSlug = `${originalMenu.slug}-copy`;
            let counter = 1;
            while (!(await this.isNameUnique(newName, originalMenu.postType))) {
                newName = `${originalMenu.name} (Copy ${counter})`;
                counter++;
            }
            counter = 1;
            while (!(await this.isSlugUnique(newSlug, originalMenu.postType))) {
                newSlug = `${originalMenu.slug}-copy-${counter}`;
                counter++;
            }
            const createDto = {
                name: newName,
                slug: newSlug,
                description: originalMenu.description,
                postType: originalMenu.postType,
                imageUrl: originalMenu.imageUrl,
                metaTitle: originalMenu.metaTitle,
                metaDescription: originalMenu.metaDescription,
                metaKeywords: originalMenu.metaKeywords,
                status: originalMenu.status,
                parentId: originalMenu.parent?.id,
            };
            const duplicatedMenu = await this.create(createDto, userId);
            this.logger.debug(`Đã nhân bản menu CMS thành công với ID mới: ${duplicatedMenu.id}`);
            return duplicatedMenu;
        }
        catch (error) {
            this.logger.error(`Lỗi khi nhân bản menu CMS: ${error.message}`, error.stack);
            throw new common_1.InternalServerErrorException(`Không thể nhân bản menu CMS: ${error.message}`);
        }
    }
};
exports.CreateCmsMenusService = CreateCmsMenusService;
__decorate([
    (0, typeorm_transactional_1.Transactional)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [create_cms_menu_dto_1.CreateCmsMenuDto, String]),
    __metadata("design:returntype", Promise)
], CreateCmsMenusService.prototype, "create", null);
__decorate([
    (0, typeorm_transactional_1.Transactional)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Array, String]),
    __metadata("design:returntype", Promise)
], CreateCmsMenusService.prototype, "bulkCreate", null);
__decorate([
    (0, typeorm_transactional_1.Transactional)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String]),
    __metadata("design:returntype", Promise)
], CreateCmsMenusService.prototype, "duplicate", null);
exports.CreateCmsMenusService = CreateCmsMenusService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(cms_menus_entity_1.CmsMenus)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        typeorm_2.DataSource,
        event_emitter_1.EventEmitter2])
], CreateCmsMenusService);
//# sourceMappingURL=create.cms-menus.service.js.map
import { DeleteCmsCategoriesService } from '../services/delete.cms-categories.service';
import { CmsCategoryDto } from '../dto/cms-category.dto';
export declare class DeleteCmsCategoriesController {
    private readonly cmsCategoriesService;
    constructor(cmsCategoriesService: DeleteCmsCategoriesService);
    softDelete(id: string, userId: string): Promise<CmsCategoryDto | null>;
    restore(id: string, userId: string): Promise<CmsCategoryDto | null>;
    remove(id: string): Promise<{
        affected: number;
    }>;
}

"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.Asset = void 0;
const openapi = require("@nestjs/swagger");
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
const typeorm_1 = require("typeorm");
const user_entity_1 = require("../../users/entities/user.entity");
const base_entity_1 = require("../../common/entities/base.entity");
const ecom_products_entity_1 = require("../../ecom-products/entity/ecom-products.entity");
let Asset = class Asset extends base_entity_1.BaseEntity {
    userId;
    productId;
    amount;
    user;
    product;
    getEntityName() {
        return 'assets';
    }
    static _OPENAPI_METADATA_FACTORY() {
        return { userId: { required: true, type: () => String }, productId: { required: true, type: () => String, format: "uuid" }, amount: { required: true, type: () => Number }, user: { required: true, type: () => require("../../users/entities/user.entity").User }, product: { required: true, type: () => require("../../ecom-products/entity/ecom-products.entity").EcomProduct } };
    }
};
exports.Asset = Asset;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'ID người dùng',
        example: '550e8400-e29b-41d4-a716-************',
    }),
    (0, class_validator_1.IsNotEmpty)(),
    (0, class_validator_1.IsNumber)(),
    (0, typeorm_1.Column)({ type: 'uuid', nullable: false, name: 'user_id' }),
    __metadata("design:type", String)
], Asset.prototype, "userId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'ID token',
        example: '550e8400-e29b-41d4-a716-************',
    }),
    (0, class_validator_1.IsNotEmpty)(),
    (0, class_validator_1.IsUUID)(),
    (0, typeorm_1.Column)({ type: 'uuid', nullable: false, name: 'ecom_product_id' }),
    __metadata("design:type", String)
], Asset.prototype, "productId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Số lượng token', example: '1.0000' }),
    (0, class_validator_1.IsNotEmpty)(),
    (0, class_validator_1.IsDecimal)(),
    (0, typeorm_1.Column)({
        type: 'decimal',
        precision: 20,
        scale: 4,
        default: 0,
        nullable: false,
        name: 'amount',
    }),
    __metadata("design:type", Number)
], Asset.prototype, "amount", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => user_entity_1.User, (user) => user.tokenAssets, { nullable: false }),
    (0, typeorm_1.JoinColumn)({ name: 'user_id' }),
    (0, swagger_1.ApiProperty)({
        type: () => user_entity_1.User,
        description: 'Người dùng liên quan đến tài sản token',
    }),
    __metadata("design:type", user_entity_1.User)
], Asset.prototype, "user", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => ecom_products_entity_1.EcomProduct),
    (0, typeorm_1.JoinColumn)({ name: 'ecom_product_id' }),
    (0, swagger_1.ApiProperty)({
        type: () => ecom_products_entity_1.EcomProduct,
        description: 'Sản phẩm liên kết',
    }),
    __metadata("design:type", ecom_products_entity_1.EcomProduct)
], Asset.prototype, "product", void 0);
exports.Asset = Asset = __decorate([
    (0, typeorm_1.Entity)('assets')
], Asset);
//# sourceMappingURL=asset.entity.js.map
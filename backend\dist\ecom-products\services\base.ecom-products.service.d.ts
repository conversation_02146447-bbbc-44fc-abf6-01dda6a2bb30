import { Logger } from '@nestjs/common';
import { Repository, QueryRunner, DataSource } from 'typeorm';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { EcomProduct } from '../entity/ecom-products.entity';
import { EcomProductDto } from '../dto/ecom-product.dto';
export declare class BaseEcomProductsService {
    protected readonly ecomProductRepository: Repository<EcomProduct>;
    protected readonly dataSource: DataSource;
    protected readonly eventEmitter: EventEmitter2;
    protected readonly logger: Logger;
    protected readonly validRelations: string[];
    protected readonly EVENT_PRODUCT_CREATED = "ecom-product.created";
    protected readonly EVENT_PRODUCT_UPDATED = "ecom-product.updated";
    protected readonly EVENT_PRODUCT_DELETED = "ecom-product.deleted";
    protected readonly EVENT_PRODUCT_RESTORED = "ecom-product.restored";
    constructor(ecomProductRepository: Repository<EcomProduct>, dataSource: DataSource, eventEmitter: EventEmitter2);
    protected getQueryRunner(): Promise<QueryRunner>;
    protected validateRelations(relations: string[]): string[];
    protected findByIdOrFail(id: string, relations?: string[], withDeleted?: boolean): Promise<EcomProduct>;
    protected toDto(product: EcomProduct): EcomProductDto;
}

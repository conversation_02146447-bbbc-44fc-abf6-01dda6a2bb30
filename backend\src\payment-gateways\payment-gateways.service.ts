import { Injectable, Logger, BadRequestException, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Transactional } from 'typeorm-transactional';
import { EventEmitter2 } from '@nestjs/event-emitter';

import { VnpayService } from './services/vnpay.service';
import { MomoService } from './services/momo.service';
import { CreatePaymentDto } from './dto/create-payment.dto';
import { PaymentGatewayType } from './enums/payment-gateway-type.enum';
import { Transaction } from '../transactions/entities/transaction.entity';
import { Wallet } from '../wallets/entities/wallet.entity';
import { TransactionType } from '../transactions/enums/transaction-type.enum';
import { TransactionStatus } from '../transactions/enums/transaction-status.enum';
import { TransactionReferenceType } from '../transactions/enums/transaction-reference-type.enum';
import { UpdateWalletService } from '../wallets/services/update.wallet.service';
import { ReadWalletService } from '../wallets/services/read.wallet.service';

// Interfaces cho tính trong suốt và tái sử dụng
export interface PaymentResult {
  paymentUrl: string;
  transactionId: string;
  gatewayType: PaymentGatewayType;
  expiresAt?: Date;
}

export interface CallbackResult {
  isValid: boolean;
  isSuccess?: boolean;
  transactionId?: string;
  amount?: number;
  responseCode?: string;
  transactionStatus?: string;
  message?: string;
  gatewayTransactionNo?: string;
  bankCode?: string;
  payDate?: string;
  gatewayType: PaymentGatewayType;
  rawData: Record<string, any>;
}

export interface IpnResponse {
  success: boolean;
  code: string;
  message: string;
  data?: any;
}

export interface PaymentCallbacks {
  onPaymentCreated?: (result: PaymentResult) => Promise<void>;
  onPaymentSuccess?: (result: CallbackResult) => Promise<void>;
  onPaymentFailed?: (result: CallbackResult) => Promise<void>;
  onPaymentPending?: (result: CallbackResult) => Promise<void>;
}

@Injectable()
export class PaymentGatewaysService {
  private readonly logger = new Logger(PaymentGatewaysService.name);

  constructor(
    @InjectRepository(Transaction)
    private readonly transactionRepository: Repository<Transaction>,
    @InjectRepository(Wallet)
    private readonly walletRepository: Repository<Wallet>,
    private readonly vnpayService: VnpayService,
    private readonly momoService: MomoService,
    private readonly updateWalletService: UpdateWalletService,
    private readonly readWalletService: ReadWalletService,
    private readonly eventEmitter: EventEmitter2,
  ) {}

  /**
   * Tạo URL thanh toán dựa trên loại cổng thanh toán
   * @param createPaymentDto Dữ liệu thanh toán
   * @param callbacks Callbacks tùy chọn cho xử lý sự kiện
   * @returns Thông tin thanh toán đầy đủ
   */
  async createPayment(
    createPaymentDto: CreatePaymentDto,
    callbacks?: PaymentCallbacks
  ): Promise<PaymentResult> {
    try {
      this.logger.log(`Đang tạo thanh toán với cổng: ${createPaymentDto.gatewayType}`);

      // Validation cơ bản
      await this.validatePaymentRequest(createPaymentDto);

      // Tạo URL thanh toán dựa trên loại cổng thanh toán
      let gatewayResult: { paymentUrl: string; transactionId: string };

      switch (createPaymentDto.gatewayType) {
        case PaymentGatewayType.VNPAY:
          gatewayResult = await this.vnpayService.createPaymentUrl(createPaymentDto);
          break;
        case PaymentGatewayType.MOMO:
          gatewayResult = await this.momoService.createPaymentUrl(createPaymentDto);
          break;
        default:
          throw new BadRequestException(`Cổng thanh toán không được hỗ trợ: ${createPaymentDto.gatewayType}`);
      }

      // Tạo giao dịch với trạng thái PENDING
      await this.createPendingTransaction(
        createPaymentDto.userId,
        createPaymentDto.walletId,
        createPaymentDto.amount,
        gatewayResult.transactionId,
        createPaymentDto.gatewayType,
        createPaymentDto.description || 'Nạp tiền vào ví',
      );

      // Tạo kết quả trả về
      const paymentResult: PaymentResult = {
        paymentUrl: gatewayResult.paymentUrl,
        transactionId: gatewayResult.transactionId,
        gatewayType: createPaymentDto.gatewayType,
        expiresAt: new Date(Date.now() + 15 * 60 * 1000), // 15 phút
      };

      // Gọi callback nếu có
      if (callbacks?.onPaymentCreated) {
        await callbacks.onPaymentCreated(paymentResult);
      }

      return paymentResult;
    } catch (error) {
      this.logger.error(`Lỗi khi tạo thanh toán: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Validate payment request
   * @param createPaymentDto Dữ liệu thanh toán
   */
  private async validatePaymentRequest(createPaymentDto: CreatePaymentDto): Promise<void> {
    // Kiểm tra ví tồn tại
    const wallet = await this.readWalletService.findById(createPaymentDto.walletId);
    if (!wallet) {
      throw new NotFoundException(`Không tìm thấy ví với ID: ${createPaymentDto.walletId}`);
    }

    // Kiểm tra người dùng có quyền truy cập ví
    if (wallet.userId !== createPaymentDto.userId) {
      throw new BadRequestException('Bạn không có quyền truy cập ví này');
    }

    // Kiểm tra số tiền hợp lệ
    if (createPaymentDto.amount <= 0) {
      throw new BadRequestException('Số tiền phải lớn hơn 0');
    }

    // Kiểm tra giới hạn số tiền (có thể cấu hình)
    const maxAmount = 50000000; // 50 triệu VND
    if (createPaymentDto.amount > maxAmount) {
      throw new BadRequestException(`Số tiền không được vượt quá ${maxAmount.toLocaleString()} VND`);
    }
  }

  /**
   * Xử lý callback từ VNPAY (Return URL)
   * @param params Tham số từ VNPAY
   * @param callbacks Callbacks tùy chọn
   * @returns Kết quả xử lý
   */
  @Transactional()
  async handleVnpayCallback(
    params: Record<string, string>,
    callbacks?: PaymentCallbacks
  ): Promise<CallbackResult> {
    try {
      this.logger.log(`Đang xử lý callback từ VNPAY: ${JSON.stringify(params)}`);

      // Xác thực callback
      const verifyResult = await this.vnpayService.verifyReturnUrl(params);

      // Tạo kết quả chuẩn hóa
      const callbackResult: CallbackResult = {
        isValid: verifyResult.isValid,
        isSuccess: verifyResult.responseCode === '00' && verifyResult.transactionStatus === '00',
        transactionId: verifyResult.transactionId,
        amount: verifyResult.amount,
        responseCode: verifyResult.responseCode,
        transactionStatus: verifyResult.transactionStatus,
        message: verifyResult.message,
        gatewayTransactionNo: verifyResult.vnpayTransactionNo,
        bankCode: verifyResult.bankCode,
        payDate: verifyResult.payDate,
        gatewayType: PaymentGatewayType.VNPAY,
        rawData: params,
      };

      // Xử lý theo trạng thái
      if (callbackResult.isValid && callbackResult.transactionId) {
        if (callbackResult.isSuccess) {
          await this.processSuccessfulPayment(
            callbackResult.transactionId,
            callbackResult.amount,
            PaymentGatewayType.VNPAY,
            params,
          );

          // Gọi callback success
          if (callbacks?.onPaymentSuccess) {
            await callbacks.onPaymentSuccess(callbackResult);
          }
        } else {
          await this.processFailedPayment(
            callbackResult.transactionId,
            PaymentGatewayType.VNPAY,
            callbackResult.message || 'Payment failed',
            params,
          );

          // Gọi callback failed
          if (callbacks?.onPaymentFailed) {
            await callbacks.onPaymentFailed(callbackResult);
          }
        }
      }

      return callbackResult;
    } catch (error) {
      this.logger.error(`Lỗi khi xử lý callback VNPAY: ${error.message}`, error.stack);

      return {
        isValid: false,
        isSuccess: false,
        message: `Lỗi xử lý: ${error.message}`,
        gatewayType: PaymentGatewayType.VNPAY,
        rawData: params,
      };
    }
  }

  /**
   * Xử lý callback từ MOMO
   * @param params Tham số từ MOMO
   * @returns Kết quả xử lý
   */
  @Transactional()
  async handleMomoCallback(params: Record<string, string>): Promise<{
    isValid: boolean;
    transactionId?: string;
    amount?: number;
    message?: string;
  }> {
    try {
      this.logger.log(`Đang xử lý callback từ MOMO: ${JSON.stringify(params)}`);

      // Xác thực callback
      const verifyResult = await this.momoService.verifyReturnUrl(params);

      // Nếu thanh toán thành công, cập nhật giao dịch và số dư ví
      if (verifyResult.isValid && verifyResult.transactionId) {
        await this.processSuccessfulPayment(
          verifyResult.transactionId,
          verifyResult.amount,
          PaymentGatewayType.MOMO,
          params,
        );
      }

      return verifyResult;
    } catch (error) {
      this.logger.error(`Lỗi khi xử lý callback MOMO: ${error.message}`, error.stack);
      return {
        isValid: false,
        message: `Lỗi xử lý: ${error.message}`,
      };
    }
  }

  /**
   * Xử lý IPN từ VNPAY
   * @param params Tham số từ VNPAY
   * @returns Kết quả xử lý
   */
  @Transactional()
  async handleVnpayIpn(params: Record<string, string>): Promise<{
    isValid: boolean;
    transactionId?: string;
    amount?: number;
    message?: string;
    RspCode?: string;
    Message?: string;
  }> {
    try {
      this.logger.log(`Đang xử lý IPN từ VNPAY: ${JSON.stringify(params)}`);

      // Xác thực IPN
      const verifyResult = await this.vnpayService.handleIpnCallback(params);

      // Nếu thanh toán thành công, cập nhật giao dịch và số dư ví
      if (verifyResult.isValid && verifyResult.transactionId) {
        await this.processSuccessfulPayment(
          verifyResult.transactionId,
          verifyResult.amount,
          PaymentGatewayType.VNPAY,
          params,
        );

        return {
          ...verifyResult,
          RspCode: '00',
          Message: 'Confirm Success',
        };
      }

      return {
        ...verifyResult,
        RspCode: '99',
        Message: 'Confirm Fail',
      };
    } catch (error) {
      this.logger.error(`Lỗi khi xử lý IPN VNPAY: ${error.message}`, error.stack);
      return {
        isValid: false,
        message: `Lỗi xử lý: ${error.message}`,
        RspCode: '99',
        Message: 'Confirm Fail',
      };
    }
  }

  /**
   * Xử lý IPN từ MOMO
   * @param params Tham số từ MOMO
   * @returns Kết quả xử lý
   */
  @Transactional()
  async handleMomoIpn(params: Record<string, string>): Promise<{
    isValid: boolean;
    transactionId?: string;
    amount?: number;
    message?: string;
    status?: number;
  }> {
    try {
      this.logger.log(`Đang xử lý IPN từ MOMO: ${JSON.stringify(params)}`);

      // Xác thực IPN
      const verifyResult = await this.momoService.handleIpnCallback(params);

      // Nếu thanh toán thành công, cập nhật giao dịch và số dư ví
      if (verifyResult.isValid && verifyResult.transactionId) {
        await this.processSuccessfulPayment(
          verifyResult.transactionId,
          verifyResult.amount,
          PaymentGatewayType.MOMO,
          params,
        );

        return {
          ...verifyResult,
          status: 0,
          message: 'Thành công',
        };
      }

      return {
        ...verifyResult,
        status: 1,
      };
    } catch (error) {
      this.logger.error(`Lỗi khi xử lý IPN MOMO: ${error.message}`, error.stack);
      return {
        isValid: false,
        message: `Lỗi xử lý: ${error.message}`,
        status: 1
      };
    }
  }

  /**
   * Tạo giao dịch với trạng thái PENDING
   * @param userId ID người dùng
   * @param walletId ID ví
   * @param amount Số tiền
   * @param transactionId Mã giao dịch
   * @param gatewayType Loại cổng thanh toán
   * @param description Mô tả
   */
  private async createPendingTransaction(
    userId: string,
    walletId: string,
    amount: number,
    transactionId: string,
    gatewayType: PaymentGatewayType,
    description: string,
  ): Promise<void> {
    try {
      // Tạo giao dịch mới
      const transaction = this.transactionRepository.create({
        userId,
        walletId,
        transactionType: TransactionType.CREDIT,
        amount,
        status: TransactionStatus.PENDING,
        notes: description,
        referenceCode: `DEP-${transactionId}`,
        gatewayTransactionId: transactionId,
        createdBy: userId,
        gatewayResponse: JSON.stringify({
          gatewayType,
          transactionId,
        }),
      });

      await this.transactionRepository.save(transaction);
      this.logger.log(`Đã tạo giao dịch PENDING với ID: ${transaction.id}`);
    } catch (error) {
      this.logger.error(`Lỗi khi tạo giao dịch PENDING: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Xử lý thanh toán thành công
   * @param transactionId Mã giao dịch
   * @param amount Số tiền
   * @param gatewayType Loại cổng thanh toán
   * @param gatewayResponse Phản hồi từ cổng thanh toán
   */
  private async processSuccessfulPayment(
    transactionId: string,
    amount: number | undefined,
    gatewayType: PaymentGatewayType,
    gatewayResponse: Record<string, string>,
  ): Promise<void> {
    try {
      // Tìm giao dịch PENDING
      const transaction = await this.transactionRepository.findOne({
        where: {
          gatewayTransactionId: transactionId,
          status: TransactionStatus.PENDING,
        },
      });

      if (!transaction) {
        this.logger.warn(`Không tìm thấy giao dịch PENDING với mã: ${transactionId}`);
        return;
      }

      // Sử dụng số tiền từ giao dịch nếu amount không được cung cấp
      const paymentAmount = amount || Number(transaction.amount);

      // Tìm ví
      const wallet = await this.walletRepository.findOne({
        where: { id: transaction.walletId },
      });

      if (!wallet) {
        this.logger.warn(`Không tìm thấy ví với ID: ${transaction.walletId}`);
        return;
      }

      // Cập nhật số dư ví trực tiếp
      const currentBalance = Number(wallet.balance);
      const currentAvailableBalance = Number(wallet.availableBalance);
      const newBalance = currentBalance + paymentAmount;
      const newAvailableBalance = currentAvailableBalance + paymentAmount;

      wallet.balance = newBalance;
      wallet.availableBalance = newAvailableBalance;
      wallet.updatedBy = 'system';

      // Cập nhật giao dịch thành COMPLETED
      transaction.status = TransactionStatus.COMPLETED;
      transaction.updatedAt = new Date();
      transaction.balanceBefore = currentBalance;
      transaction.balanceAfter = newBalance;
      transaction.gatewayResponse = JSON.stringify({
        gatewayType,
        response: gatewayResponse,
      });

      // Lưu cả ví và giao dịch
      await this.walletRepository.save(wallet);
      await this.transactionRepository.save(transaction);

      this.logger.log(`Đã cập nhật giao dịch ${transaction.id} thành COMPLETED`);
      this.logger.log(`Đã cập nhật số dư ví ${transaction.walletId} từ ${currentBalance} thành ${newBalance}`);

      // Phát sự kiện thay đổi số dư
      this.eventEmitter.emit('wallet.balance.changed', {
        walletId: wallet.id,
        oldBalance: currentBalance,
        newBalance: newBalance,
        amount: paymentAmount,
        performedBy: 'system',
      });

      // Phát sự kiện thanh toán thành công
      this.eventEmitter.emit('payment.success', {
        userId: transaction.userId,
        walletId: transaction.walletId,
        amount: paymentAmount,
        transactionId: transaction.id,
        gatewayType,
      });
    } catch (error) {
      this.logger.error(`Lỗi khi xử lý thanh toán thành công: ${error.message}`, error.stack);
      throw error;
    }
  }
}

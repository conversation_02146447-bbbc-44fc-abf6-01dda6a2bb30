import {
  BadRequestException,
  Injectable,
  Logger,
  NotFoundException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Transactional } from 'typeorm-transactional';
import { EventEmitter2 } from '@nestjs/event-emitter';

import { VnpayService } from './services/vnpay.service';
import { CreatePaymentDto } from './dto/create-payment.dto';
import { PaymentGatewayType } from './enums/payment-gateway-type.enum';
import { Transaction } from '../transactions/entities/transaction.entity';
import { Wallet } from '../wallets/entities/wallet.entity';
import { TransactionType } from '../transactions/enums/transaction-type.enum';
import { TransactionStatus } from '../transactions/enums/transaction-status.enum';
import { UpdateWalletService } from '../wallets/services/update.wallet.service';
import { ReadWalletService } from '../wallets/services/read.wallet.service';

// Interfaces cho tính trong suốt và tái sử dụng
export interface PaymentResult {
  paymentUrl: string;
  transactionId: string;
  gatewayType: PaymentGatewayType;
  expiresAt?: Date;
}

export interface CallbackResult {
  isValid: boolean;
  isSuccess?: boolean;
  transactionId?: string;
  amount?: number;
  responseCode?: string;
  transactionStatus?: string;
  message?: string;
  gatewayTransactionNo?: string;
  bankCode?: string;
  payDate?: string;
  gatewayType: PaymentGatewayType;
  rawData: Record<string, any>;
}

export interface IpnResponse {
  success: boolean;
  code: string;
  message: string;
  data?: any;
}

export interface PaymentCallbacks {
  onPaymentCreated?: (result: PaymentResult) => Promise<void>;
  onPaymentSuccess?: (result: CallbackResult) => Promise<void>;
  onPaymentFailed?: (result: CallbackResult) => Promise<void>;
  onPaymentPending?: (result: CallbackResult) => Promise<void>;
}

@Injectable()
export class PaymentGatewaysService {
  private readonly logger = new Logger(PaymentGatewaysService.name);

  constructor(
    @InjectRepository(Transaction)
    private readonly transactionRepository: Repository<Transaction>,
    @InjectRepository(Wallet)
    private readonly walletRepository: Repository<Wallet>,
    private readonly vnpayService: VnpayService,
    private readonly updateWalletService: UpdateWalletService,
    private readonly readWalletService: ReadWalletService,
    private readonly eventEmitter: EventEmitter2,
  ) {}

  /**
   * Tạo URL thanh toán dựa trên loại cổng thanh toán
   * @param createPaymentDto Dữ liệu thanh toán
   * @param callbacks Callbacks tùy chọn cho xử lý sự kiện
   * @returns Thông tin thanh toán đầy đủ
   */
  async createPayment(
    createPaymentDto: CreatePaymentDto,
    callbacks?: PaymentCallbacks,
  ): Promise<PaymentResult> {
    try {
      this.logger.log(
        `Đang tạo thanh toán với cổng: ${createPaymentDto.gatewayType}`,
      );

      // Validation cơ bản
      await this.validatePaymentRequest(createPaymentDto);

      // Tạo URL thanh toán dựa trên loại cổng thanh toán
      let gatewayResult: { paymentUrl: string; transactionId: string };

      switch (createPaymentDto.gatewayType) {
        case PaymentGatewayType.VNPAY:
          gatewayResult =
            await this.vnpayService.createPaymentUrl(createPaymentDto);
          break;
        default:
          throw new BadRequestException(
            `Cổng thanh toán không được hỗ trợ: ${createPaymentDto.gatewayType}`,
          );
      }

      // Tạo giao dịch với trạng thái PENDING
      await this.createPendingTransaction(
        createPaymentDto.userId,
        createPaymentDto.walletId,
        createPaymentDto.amount,
        gatewayResult.transactionId,
        createPaymentDto.gatewayType,
        createPaymentDto.description || 'Nạp tiền vào ví',
      );

      // Tạo kết quả trả về
      const paymentResult: PaymentResult = {
        paymentUrl: gatewayResult.paymentUrl,
        transactionId: gatewayResult.transactionId,
        gatewayType: createPaymentDto.gatewayType,
        expiresAt: new Date(Date.now() + 15 * 60 * 1000), // 15 phút
      };

      // Gọi callback nếu có
      if (callbacks?.onPaymentCreated) {
        await callbacks.onPaymentCreated(paymentResult);
      }

      return paymentResult;
    } catch (error) {
      this.logger.error(
        `Lỗi khi tạo thanh toán: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Validate payment request
   * @param createPaymentDto Dữ liệu thanh toán
   */
  private async validatePaymentRequest(
    createPaymentDto: CreatePaymentDto,
  ): Promise<void> {
    // Kiểm tra ví tồn tại
    const wallet = await this.readWalletService.findById(
      createPaymentDto.walletId,
    );
    if (!wallet) {
      throw new NotFoundException(
        `Không tìm thấy ví với ID: ${createPaymentDto.walletId}`,
      );
    }

    // Kiểm tra người dùng có quyền truy cập ví
    if (wallet.userId !== createPaymentDto.userId) {
      throw new BadRequestException('Bạn không có quyền truy cập ví này');
    }

    // Kiểm tra số tiền hợp lệ
    if (createPaymentDto.amount <= 0) {
      throw new BadRequestException('Số tiền phải lớn hơn 0');
    }

    // Kiểm tra giới hạn số tiền (có thể cấu hình)
    const maxAmount = 50000000; // 50 triệu VND
    if (createPaymentDto.amount > maxAmount) {
      throw new BadRequestException(
        `Số tiền không được vượt quá ${maxAmount.toLocaleString()} VND`,
      );
    }
  }

  /**
   * Xử lý callback từ VNPAY (Return URL)
   * @param params Tham số từ VNPAY
   * @param callbacks Callbacks tùy chọn
   * @returns Kết quả xử lý
   */
  @Transactional()
  async handleVnpayCallback(
    params: Record<string, string>,
    callbacks?: PaymentCallbacks,
  ): Promise<CallbackResult> {
    try {
      this.logger.log(
        `Đang xử lý callback từ VNPAY: ${JSON.stringify(params)}`,
      );

      // Xác thực callback
      const verifyResult = await this.vnpayService.verifyReturnUrl(params);

      // Tạo kết quả chuẩn hóa
      const callbackResult: CallbackResult = {
        isValid: verifyResult.isValid,
        isSuccess:
          verifyResult.responseCode === '00' &&
          verifyResult.transactionStatus === '00',
        transactionId: verifyResult.transactionId,
        amount: verifyResult.amount,
        responseCode: verifyResult.responseCode,
        transactionStatus: verifyResult.transactionStatus,
        message: verifyResult.message,
        gatewayTransactionNo: verifyResult.vnpayTransactionNo,
        bankCode: verifyResult.bankCode,
        payDate: verifyResult.payDate,
        gatewayType: PaymentGatewayType.VNPAY,
        rawData: params,
      };

      // Xử lý theo trạng thái
      if (callbackResult.isValid && callbackResult.transactionId) {
        if (callbackResult.isSuccess) {
          await this.processSuccessfulPayment(
            callbackResult.transactionId,
            callbackResult.amount,
            PaymentGatewayType.VNPAY,
            params,
          );

          // Gọi callback success
          if (callbacks?.onPaymentSuccess) {
            await callbacks.onPaymentSuccess(callbackResult);
          }
        } else {
          await this.processFailedPayment(
            callbackResult.transactionId,
            PaymentGatewayType.VNPAY,
            callbackResult.message || 'Payment failed',
            params,
          );

          // Gọi callback failed
          if (callbacks?.onPaymentFailed) {
            await callbacks.onPaymentFailed(callbackResult);
          }
        }
      }

      return callbackResult;
    } catch (error) {
      this.logger.error(
        `Lỗi khi xử lý callback VNPAY: ${error.message}`,
        error.stack,
      );

      return {
        isValid: false,
        isSuccess: false,
        message: `Lỗi xử lý: ${error.message}`,
        gatewayType: PaymentGatewayType.VNPAY,
        rawData: params,
      };
    }
  }

  /**
   * Tạo giao dịch với trạng thái PENDING
   * @param userId ID người dùng
   * @param walletId ID ví
   * @param amount Số tiền
   * @param transactionId Mã giao dịch
   * @param gatewayType Loại cổng thanh toán
   * @param description Mô tả
   */
  private async createPendingTransaction(
    userId: string,
    walletId: string,
    amount: number,
    transactionId: string,
    gatewayType: PaymentGatewayType,
    description: string,
  ): Promise<void> {
    try {
      // Tạo giao dịch mới
      const transaction = this.transactionRepository.create({
        userId,
        walletId,
        transactionType: TransactionType.CREDIT,
        amount,
        status: TransactionStatus.PENDING,
        notes: description,
        referenceCode: `DEP-${transactionId}`,
        gatewayTransactionId: transactionId,
        createdBy: userId,
        gatewayResponse: JSON.stringify({
          gatewayType,
          transactionId,
        }),
      });

      await this.transactionRepository.save(transaction);
      this.logger.log(`Đã tạo giao dịch PENDING với ID: ${transaction.id}`);
    } catch (error) {
      this.logger.error(
        `Lỗi khi tạo giao dịch PENDING: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Xử lý thanh toán thành công
   * @param transactionId Mã giao dịch
   * @param amount Số tiền
   * @param gatewayType Loại cổng thanh toán
   * @param gatewayResponse Phản hồi từ cổng thanh toán
   */
  private async processSuccessfulPayment(
    transactionId: string,
    amount: number | undefined,
    gatewayType: PaymentGatewayType,
    gatewayResponse: Record<string, string>,
  ): Promise<void> {
    try {
      // Tìm giao dịch PENDING
      const transaction = await this.transactionRepository.findOne({
        where: {
          gatewayTransactionId: transactionId,
          status: TransactionStatus.PENDING,
        },
      });

      if (!transaction) {
        this.logger.warn(
          `Không tìm thấy giao dịch PENDING với mã: ${transactionId}`,
        );
        return;
      }

      // Sử dụng số tiền từ giao dịch nếu amount không được cung cấp
      const paymentAmount = amount || Number(transaction.amount);

      // Tìm ví
      const wallet = await this.walletRepository.findOne({
        where: { id: transaction.walletId },
      });

      if (!wallet) {
        this.logger.warn(`Không tìm thấy ví với ID: ${transaction.walletId}`);
        return;
      }

      // Cập nhật số dư ví trực tiếp
      const currentBalance = Number(wallet.balance);
      const currentAvailableBalance = Number(wallet.availableBalance);
      const newBalance = currentBalance + paymentAmount;
      const newAvailableBalance = currentAvailableBalance + paymentAmount;

      wallet.balance = newBalance;
      wallet.availableBalance = newAvailableBalance;
      wallet.updatedBy = 'system';

      // Cập nhật giao dịch thành COMPLETED
      transaction.status = TransactionStatus.COMPLETED;
      transaction.updatedAt = new Date();
      transaction.balanceBefore = currentBalance;
      transaction.balanceAfter = newBalance;
      transaction.gatewayResponse = JSON.stringify({
        gatewayType,
        response: gatewayResponse,
      });

      // Lưu cả ví và giao dịch
      await this.walletRepository.save(wallet);
      await this.transactionRepository.save(transaction);

      this.logger.log(
        `Đã cập nhật giao dịch ${transaction.id} thành COMPLETED`,
      );
      this.logger.log(
        `Đã cập nhật số dư ví ${transaction.walletId} từ ${currentBalance} thành ${newBalance}`,
      );

      // Phát sự kiện thay đổi số dư
      this.eventEmitter.emit('wallet.balance.changed', {
        walletId: wallet.id,
        oldBalance: currentBalance,
        newBalance: newBalance,
        amount: paymentAmount,
        performedBy: 'system',
      });

      // Phát sự kiện thanh toán thành công
      this.eventEmitter.emit('payment.success', {
        userId: transaction.userId,
        walletId: transaction.walletId,
        amount: paymentAmount,
        transactionId: transaction.id,
        gatewayType,
      });
    } catch (error) {
      this.logger.error(
        `Lỗi khi xử lý thanh toán thành công: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Xử lý thanh toán thất bại
   * @param transactionId Mã giao dịch
   * @param gatewayType Loại cổng thanh toán
   * @param reason Lý do thất bại
   * @param gatewayResponse Phản hồi từ cổng thanh toán
   */
  private async processFailedPayment(
    transactionId: string,
    gatewayType: PaymentGatewayType,
    reason: string,
    gatewayResponse: Record<string, string>,
  ): Promise<void> {
    try {
      // Tìm giao dịch PENDING
      const transaction = await this.transactionRepository.findOne({
        where: {
          gatewayTransactionId: transactionId,
          status: TransactionStatus.PENDING,
        },
      });

      if (!transaction) {
        this.logger.warn(
          `Không tìm thấy giao dịch PENDING với mã: ${transactionId}`,
        );
        return;
      }

      // Cập nhật giao dịch thành FAILED
      transaction.status = TransactionStatus.FAILED;
      transaction.updatedAt = new Date();
      transaction.notes = `${transaction.notes} - Lý do thất bại: ${reason}`;
      transaction.gatewayResponse = JSON.stringify({
        gatewayType,
        response: gatewayResponse,
        failureReason: reason,
      });

      await this.transactionRepository.save(transaction);

      this.logger.log(
        `Đã cập nhật giao dịch ${transaction.id} thành FAILED: ${reason}`,
      );

      // Phát sự kiện thanh toán thất bại
      this.eventEmitter.emit('payment.failed', {
        userId: transaction.userId,
        walletId: transaction.walletId,
        amount: Number(transaction.amount),
        transactionId: transaction.id,
        gatewayType,
        reason,
      });
    } catch (error) {
      this.logger.error(
        `Lỗi khi xử lý thanh toán thất bại: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Xử lý IPN từ VNPAY với response chuẩn hóa
   * @param params Tham số từ VNPAY
   * @param callbacks Callbacks tùy chọn
   * @returns Response cho VNPAY
   */
  @Transactional()
  async handleVnpayIpn(
    params: Record<string, string>,
    callbacks?: PaymentCallbacks,
  ): Promise<IpnResponse> {
    try {
      this.logger.log(`Đang xử lý IPN từ VNPAY: ${JSON.stringify(params)}`);

      // Sử dụng lại logic callback
      const callbackResult = await this.handleVnpayCallback(params, callbacks);

      if (callbackResult.isValid) {
        return {
          success: true,
          code: '00',
          message: 'Confirm Success',
          data: callbackResult,
        };
      } else {
        return {
          success: false,
          code: '97',
          message: 'Invalid signature',
          data: callbackResult,
        };
      }
    } catch (error) {
      this.logger.error(
        `Lỗi khi xử lý IPN VNPAY: ${error.message}`,
        error.stack,
      );
      return {
        success: false,
        code: '99',
        message: 'Unknown error',
      };
    }
  }

  /**
   * Query transaction từ gateway
   * @param gatewayType Loại cổng thanh toán
   * @param queryData Dữ liệu truy vấn
   * @returns Kết quả truy vấn
   */
  async queryTransaction(
    gatewayType: PaymentGatewayType,
    queryData: {
      txnRef: string;
      transactionDate: string;
      orderInfo: string;
      ipAddr: string;
      transactionNo?: string;
    },
  ): Promise<any> {
    try {
      this.logger.log(
        `Đang truy vấn giao dịch từ ${gatewayType}: ${queryData.txnRef}`,
      );

      switch (gatewayType) {
        case PaymentGatewayType.VNPAY:
          return await this.vnpayService.queryTransaction(queryData);
        default:
          throw new BadRequestException(
            `Cổng thanh toán không được hỗ trợ: ${gatewayType}`,
          );
      }
    } catch (error) {
      this.logger.error(
        `Lỗi khi truy vấn giao dịch: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Refund transaction từ gateway
   * @param gatewayType Loại cổng thanh toán
   * @param refundData Dữ liệu hoàn tiền
   * @returns Kết quả hoàn tiền
   */
  async refundTransaction(
    gatewayType: PaymentGatewayType,
    refundData: {
      txnRef: string;
      amount: number;
      orderInfo: string;
      transactionDate: string;
      transactionType: '02' | '03';
      createBy: string;
      ipAddr: string;
      transactionNo?: string;
    },
  ): Promise<any> {
    try {
      this.logger.log(`Đang hoàn tiền từ ${gatewayType}: ${refundData.txnRef}`);

      switch (gatewayType) {
        case PaymentGatewayType.VNPAY:
          return await this.vnpayService.refundTransaction(refundData);
        default:
          throw new BadRequestException(
            `Cổng thanh toán không được hỗ trợ: ${gatewayType}`,
          );
      }
    } catch (error) {
      this.logger.error(`Lỗi khi hoàn tiền: ${error.message}`, error.stack);
      throw error;
    }
  }
}

"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AuditUtils = void 0;
const class_transformer_1 = require("class-transformer");
const user_dto_1 = require("../../users/dto/user.dto");
const request_context_1 = require("../context/request-context");
class AuditUtils {
    static AUDIT_RELATIONS = ['creator', 'updater', 'deleter'];
    static mapAuditRelationships(entity, dto) {
        if (entity.creator) {
            dto.creator = (0, class_transformer_1.plainToInstance)(user_dto_1.UserDto, entity.creator, {
                excludeExtraneousValues: true,
            });
        }
        if (entity.updater) {
            dto.updater = (0, class_transformer_1.plainToInstance)(user_dto_1.UserDto, entity.updater, {
                excludeExtraneousValues: true,
            });
        }
        if (entity.deleter) {
            dto.deleter = (0, class_transformer_1.plainToInstance)(user_dto_1.UserDto, entity.deleter, {
                excludeExtraneousValues: true,
            });
        }
    }
    static mergeAuditRelations(relations = [], includeAudit = true) {
        if (!includeAudit) {
            return relations;
        }
        const mergedRelations = [...relations];
        AuditUtils.AUDIT_RELATIONS.forEach(relation => {
            if (!mergedRelations.includes(relation)) {
                mergedRelations.push(relation);
            }
        });
        return mergedRelations;
    }
    static getAuditRelations() {
        return [...AuditUtils.AUDIT_RELATIONS];
    }
    static isAuditRelation(relation) {
        return AuditUtils.AUDIT_RELATIONS.includes(relation);
    }
    static filterOutAuditRelations(relations) {
        return relations.filter(relation => !AuditUtils.isAuditRelation(relation));
    }
    static filterAuditRelations(relations) {
        return relations.filter(relation => AuditUtils.isAuditRelation(relation));
    }
    static setCreateAuditFields(entity, userId) {
        const currentUserId = userId || request_context_1.RequestContext.getCurrentUserId();
        if (currentUserId) {
            entity.createdBy = currentUserId;
            entity.updatedBy = currentUserId;
        }
    }
    static setUpdateAuditFields(entity, userId) {
        const currentUserId = userId || request_context_1.RequestContext.getCurrentUserId();
        if (currentUserId) {
            entity.updatedBy = currentUserId;
        }
        entity.updatedAt = new Date();
    }
    static setSoftDeleteAuditFields(entity, userId) {
        const currentUserId = userId || request_context_1.RequestContext.getCurrentUserId();
        entity.isDeleted = true;
        entity.deletedAt = new Date();
        if (currentUserId) {
            entity.deletedBy = currentUserId;
            entity.updatedBy = currentUserId;
        }
        entity.updatedAt = new Date();
    }
    static setRestoreAuditFields(entity, userId) {
        const currentUserId = userId || request_context_1.RequestContext.getCurrentUserId();
        entity.isDeleted = false;
        entity.deletedAt = null;
        entity.deletedBy = null;
        if (currentUserId) {
            entity.updatedBy = currentUserId;
        }
        entity.updatedAt = new Date();
    }
}
exports.AuditUtils = AuditUtils;
//# sourceMappingURL=audit.utils.js.map
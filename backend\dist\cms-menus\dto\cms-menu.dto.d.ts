import { UserDto } from '../../users/dto/user.dto';
import { CmsMenuStatus, CmsMenuPostType } from '../entity/cms-menus.entity';
export declare class CmsMenuDto {
    id: string;
    businessCode: string;
    name: string;
    slug: string;
    description?: string;
    postType: CmsMenuPostType;
    imageUrl?: string;
    metaTitle?: string;
    metaDescription?: string;
    metaKeywords?: string;
    status: CmsMenuStatus;
    parentId?: string;
    parent?: CmsMenuDto;
    children?: CmsMenuDto[];
    creator?: UserDto;
    updater?: UserDto;
    deleter?: UserDto;
    createdAt: Date;
    updatedAt: Date;
    deletedAt?: Date;
    isDeleted: boolean;
}

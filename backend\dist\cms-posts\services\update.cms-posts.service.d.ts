import { Repository, DataSource } from 'typeorm';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { BaseCmsPostsService } from './base.cms-posts.service';
import { SlugCmsPostsService } from './slug.cms-posts.service';
import { CmsPosts, CmsPostStatus } from '../entity/cms-posts.entity';
import { UpdateCmsPostDto } from '../dto/update.cms-post.dto';
import { CmsPostDto } from '../dto/cms-post.dto';
import { CmsCategories } from '../../cms-categories/entity/cms-categories.entity';
import { User } from '../../users/entities/user.entity';
export declare class UpdateCmsPostsService extends BaseCmsPostsService {
    protected readonly postRepository: Repository<CmsPosts>;
    private readonly categoryRepository;
    private readonly userRepository;
    private readonly slugService;
    protected readonly dataSource: DataSource;
    protected readonly eventEmitter: EventEmitter2;
    constructor(postRepository: Repository<CmsPosts>, categoryRepository: Repository<CmsCategories>, userRepository: Repository<User>, slugService: SlugCmsPostsService, dataSource: DataSource, eventEmitter: EventEmitter2);
    update(id: string, updateDto: UpdateCmsPostDto, userId: string): Promise<CmsPostDto | null>;
    bulkUpdate(updates: Array<{
        id: string;
        data: UpdateCmsPostDto;
    }>, userId: string): Promise<CmsPostDto[]>;
    updateStatus(id: string, status: CmsPostStatus, userId: string): Promise<CmsPostDto | null>;
    publish(id: string, userId: string): Promise<CmsPostDto | null>;
    unpublish(id: string, userId: string): Promise<CmsPostDto | null>;
}

# Payment Gateways Module

## Tổng quan

Module Payment Gateways được thiết kế để **trong suốt và có thể tái sử dụng** cho nhiều dự án khác nhau. Module này cung cấp một giao diện thống nhất để tích hợp với các cổng thanh toán khác nhau.

## Cấu trúc Module

```
backend/src/payment-gateways/
├── dto/                              # Data Transfer Objects
│   ├── create-payment.dto.ts         # DTO cho tạo thanh toán
│   ├── payment-callback.dto.ts       # DTO cho callback
│   └── payment-response.dto.ts       # DTO cho response
├── enums/                            # Enumerations
│   └── payment-gateway-type.enum.ts  # Enum loại cổng thanh toán
├── interfaces/                       # Interfaces
│   └── payment-gateway.interface.ts  # Interface chung cho gateways
├── services/                         # Gateway Services
│   ├── vnpay.service.ts              # VNPAY service implementation
│   ├── vnpay.service.spec.ts         # VNPAY unit tests
│   ├── vnpay.service.md              # VNPAY documentation
│   └── momo.service.ts               # MOMO service implementation
├── examples/                         # Usage Examples
│   └── vnpay-usage.example.ts        # VNPAY usage examples
├── payment-gateways.controller.ts    # REST API Controller
├── payment-gateways.service.ts       # Main service (transparent layer)
├── payment-gateways.module.ts        # NestJS Module
├── INTEGRATION_GUIDE.md              # Integration guide
└── README.md                         # This file
```

## Features

### ✅ **Transparent Architecture**
- Giao diện thống nhất cho tất cả payment gateways
- Standardized response format
- Unified error handling

### ✅ **Reusable Design**
- Có thể copy toàn bộ module sang dự án khác
- Minimal dependencies
- Environment-based configuration

### ✅ **Callback System**
- Flexible callback system cho custom business logic
- Event-driven architecture
- Async processing support

### ✅ **Production Ready**
- Comprehensive error handling
- Detailed logging
- Input validation
- Security best practices

### ✅ **Gateway Support**
- **VNPAY**: Full implementation (Payment, Query, Refund, IPN)
- **MOMO**: Basic implementation (extensible)
- **Extensible**: Easy to add new gateways

## Quick Start

### 1. Environment Configuration

```env
# VNPAY Configuration
VNPAY_TMN_CODE=your_tmn_code
VNPAY_HASH_SECRET=your_hash_secret
VNPAY_URL=https://sandbox.vnpayment.vn/paymentv2/vpcpay.html
VNPAY_API_URL=https://sandbox.vnpayment.vn/merchant_webapi/api/transaction
VNPAY_RETURN_URL=https://yourdomain.com/api/payment-gateways/vnpay/return
VNPAY_IPN_URL=https://yourdomain.com/api/payment-gateways/vnpay/ipn

# Frontend URLs
FRONTEND_URL=https://yourdomain.com
```

### 2. Import Module

```typescript
// app.module.ts
import { PaymentGatewaysModule } from './payment-gateways/payment-gateways.module';

@Module({
  imports: [
    // ... other modules
    PaymentGatewaysModule,
  ],
})
export class AppModule {}
```

### 3. Basic Usage

```typescript
// your.service.ts
import { PaymentGatewaysService } from './payment-gateways/payment-gateways.service';

@Injectable()
export class YourService {
  constructor(
    private readonly paymentService: PaymentGatewaysService
  ) {}

  async createPayment() {
    const result = await this.paymentService.createPayment({
      userId: 'user-id',
      walletId: 'wallet-id',
      amount: 100000,
      gatewayType: PaymentGatewayType.VNPAY,
      description: 'Nạp tiền vào ví',
      ipAddress: '127.0.0.1'
    });

    return result; // { paymentUrl, transactionId, gatewayType, expiresAt }
  }
}
```

## API Endpoints

### Payment Operations
- `POST /payment-gateways/create-payment` - Tạo URL thanh toán
- `GET /payment-gateways/vnpay/return` - VNPAY Return URL
- `POST /payment-gateways/vnpay/ipn` - VNPAY IPN
- `POST /payment-gateways/query/:gatewayType` - Truy vấn giao dịch
- `POST /payment-gateways/refund/:gatewayType` - Hoàn tiền

### Utility Endpoints
- `GET /payment-gateways/transaction/:id/status` - Trạng thái giao dịch
- `POST /payment-gateways/webhook/test` - Test webhook
- `GET /payment-gateways/health` - Health check

## Advanced Usage

### Custom Callbacks

```typescript
const callbacks = {
  onPaymentCreated: async (result) => {
    // Send email notification
    await emailService.sendPaymentCreated(result);
  },
  onPaymentSuccess: async (result) => {
    // Update user tier, add loyalty points
    await userService.updateAfterPayment(result);
  },
  onPaymentFailed: async (result) => {
    // Log failure, send alert
    await alertService.sendPaymentFailed(result);
  }
};

await paymentService.createPayment(dto, callbacks);
```

### Query Transaction

```typescript
const queryResult = await paymentService.queryTransaction(
  PaymentGatewayType.VNPAY,
  {
    txnRef: 'transaction_id',
    transactionDate: '20231207153333',
    orderInfo: 'Query transaction',
    ipAddr: '127.0.0.1'
  }
);
```

### Refund Transaction

```typescript
const refundResult = await paymentService.refundTransaction(
  PaymentGatewayType.VNPAY,
  {
    txnRef: 'transaction_id',
    amount: 50000,
    orderInfo: 'Refund for cancellation',
    transactionDate: '20231207153333',
    transactionType: '02', // Full refund
    createBy: 'admin_user',
    ipAddr: '127.0.0.1'
  }
);
```

## Frontend Integration

### React/Next.js Example

```tsx
const PaymentButton = ({ amount, userId, walletId }) => {
  const handlePayment = async () => {
    const response = await fetch('/api/payment-gateways/create-payment', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        userId,
        walletId,
        amount,
        gatewayType: 'VNPAY',
        description: `Nạp ${amount.toLocaleString()} VND`
      })
    });

    const result = await response.json();
    if (result.paymentUrl) {
      window.location.href = result.paymentUrl;
    }
  };

  return (
    <button onClick={handlePayment}>
      Thanh toán {amount.toLocaleString()} VND
    </button>
  );
};
```

## Testing

### Unit Tests
```bash
npm run test payment-gateways
```

### Integration Tests
```bash
npm run test:e2e payment-gateways
```

### Manual Testing
1. Use `/payment-gateways/health` to check service status
2. Use `/payment-gateways/webhook/test` to test webhook handling
3. Use VNPAY sandbox environment for payment testing

## Deployment

### Requirements
- Node.js 18+
- NestJS 9+
- TypeORM
- PostgreSQL/MySQL

### Environment Setup
1. Copy environment variables
2. Configure VNPAY merchant portal
3. Set up SSL for IPN URLs
4. Configure firewall for webhook IPs

### Production Checklist
- [ ] SSL certificates configured
- [ ] Environment variables set
- [ ] Database migrations run
- [ ] VNPAY merchant portal configured
- [ ] Monitoring and alerting setup
- [ ] Error tracking configured

## Security

### Best Practices
- Always validate signatures from gateways
- Use HTTPS for all webhook URLs
- Implement rate limiting
- Log all payment activities
- Never expose sensitive data in logs
- Validate all input parameters

### VNPAY Security
- HMAC SHA512 signature validation
- IP whitelist for callbacks
- Secure hash verification
- Transaction amount validation

## Support

### Documentation
- `INTEGRATION_GUIDE.md` - Detailed integration guide
- `services/vnpay.service.md` - VNPAY specific documentation
- `examples/vnpay-usage.example.ts` - Usage examples

### Troubleshooting
1. Check environment variables
2. Verify VNPAY configuration
3. Check logs for detailed error messages
4. Use health check endpoint
5. Test with webhook test endpoint

## Contributing

### Adding New Gateway
1. Create service in `services/` directory
2. Implement `IPaymentGateway` interface
3. Add gateway type to enum
4. Update main service switch statements
5. Add tests and documentation

### Code Standards
- Follow NestJS conventions
- Add comprehensive tests
- Document all public methods
- Use TypeScript strictly
- Follow security best practices

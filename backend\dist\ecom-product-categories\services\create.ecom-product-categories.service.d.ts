import { Repository, DataSource } from 'typeorm';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { BaseEcomProductCategoriesService } from './base.ecom-product-categories.service';
import { SlugEcomProductCategoriesService } from './slug.ecom-product-categories.service';
import { EcomProductCategories } from '../entity/ecom-product-categories.entity';
import { CreateEcomProductCategoryDto } from '../dto/create-ecom-product-category.dto';
import { EcomProductCategoryDto } from '../dto/ecom-product-category.dto';
export declare class CreateEcomProductCategoriesService extends BaseEcomProductCategoriesService {
    protected readonly categoryRepository: Repository<EcomProductCategories>;
    protected readonly dataSource: DataSource;
    protected readonly eventEmitter: EventEmitter2;
    private readonly slugService;
    constructor(categoryRepository: Repository<EcomProductCategories>, dataSource: DataSource, eventEmitter: EventEmitter2, slugService: SlugEcomProductCategoriesService);
    create(createDto: CreateEcomProductCategoryDto, userId: string): Promise<EcomProductCategoryDto>;
    bulkCreate(createDtos: CreateEcomProductCategoryDto[], userId: string): Promise<EcomProductCategoryDto[]>;
    duplicate(id: string, userId: string): Promise<EcomProductCategoryDto>;
}

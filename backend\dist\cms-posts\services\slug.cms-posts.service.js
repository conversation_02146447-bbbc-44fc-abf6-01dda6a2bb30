"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SlugCmsPostsService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const cms_posts_entity_1 = require("../entity/cms-posts.entity");
const base_slug_service_1 = require("../../common/services/base-slug.service");
const unified_slug_service_1 = require("../../common/services/unified-slug.service");
let SlugCmsPostsService = class SlugCmsPostsService extends base_slug_service_1.BaseSlugService {
    unifiedSlugService;
    constructor(postRepository, unifiedSlugService) {
        super(postRepository, unifiedSlugService);
        this.unifiedSlugService = unifiedSlugService;
    }
    getSlugFieldName() {
        return 'slug';
    }
    getTextFieldName() {
        return 'title';
    }
    getWhereConditions(excludeId) {
        return { isDeleted: false };
    }
    generateFallbackSlug() {
        return `bai-viet-${Date.now()}`;
    }
    generateSlugFromTitle(title) {
        return this.generateSlugFromText(title);
    }
    async getExistingSlugs(postType, excludeId) {
        const queryBuilder = this.repository
            .createQueryBuilder('post')
            .select('post.slug')
            .where('post.postType = :postType', { postType })
            .andWhere('post.isDeleted = :isDeleted', { isDeleted: false });
        if (excludeId) {
            queryBuilder.andWhere('post.id != :excludeId', { excludeId });
        }
        const posts = await queryBuilder.getMany();
        return posts.map((post) => post.slug);
    }
    async generateUniqueSlugForCreate(title, postType, providedSlug) {
        let baseSlug;
        if (providedSlug && providedSlug.trim() !== '') {
            if (!this.validateSlug(providedSlug)) {
                baseSlug = this.generateSlugFromTitle(title);
            }
            else {
                baseSlug = providedSlug.toLowerCase().trim();
            }
        }
        else {
            baseSlug = this.generateSlugFromTitle(title);
        }
        const existingSlugs = await this.getExistingSlugs(postType);
        return this.unifiedSlugService.generateUniqueSlug(baseSlug, existingSlugs);
    }
    async generateUniqueSlugForUpdate(title, postType, currentId, providedSlug, currentSlug) {
        let targetSlug;
        if (providedSlug !== undefined) {
            if (providedSlug.trim() === '') {
                targetSlug = this.generateSlugFromTitle(title);
            }
            else {
                if (!this.validateSlug(providedSlug)) {
                    targetSlug = this.generateSlugFromTitle(title);
                }
                else {
                    targetSlug = providedSlug.toLowerCase().trim();
                }
            }
        }
        else {
            return null;
        }
        if (targetSlug === currentSlug) {
            return null;
        }
        const existingSlugs = await this.getExistingSlugs(postType, currentId);
        return this.unifiedSlugService.generateUniqueSlug(targetSlug, existingSlugs);
    }
    async isSlugExists(slug, postType, excludeId) {
        const queryBuilder = this.repository
            .createQueryBuilder('post')
            .where('post.slug = :slug', { slug })
            .andWhere('post.postType = :postType', { postType })
            .andWhere('post.isDeleted = :isDeleted', { isDeleted: false });
        if (excludeId) {
            queryBuilder.andWhere('post.id != :excludeId', { excludeId });
        }
        const count = await queryBuilder.getCount();
        return count > 0;
    }
};
exports.SlugCmsPostsService = SlugCmsPostsService;
exports.SlugCmsPostsService = SlugCmsPostsService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(cms_posts_entity_1.CmsPosts)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        unified_slug_service_1.UnifiedSlugService])
], SlugCmsPostsService);
//# sourceMappingURL=slug.cms-posts.service.js.map
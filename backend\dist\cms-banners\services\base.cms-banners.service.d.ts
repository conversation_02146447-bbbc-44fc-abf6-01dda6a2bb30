import { Logger } from '@nestjs/common';
import { Repository, DataSource, FindOptionsWhere } from 'typeorm';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { CmsBanners, CmsBannerLocation } from '../entity/cms-banners.entity';
import { CmsBannerDto } from '../dto/cms-banner.dto';
export declare class BaseCmsBannersService {
    protected readonly bannerRepository: Repository<CmsBanners>;
    protected readonly dataSource: DataSource;
    protected readonly eventEmitter: EventEmitter2;
    protected readonly logger: Logger;
    protected readonly EVENT_BANNER_CREATED = "cms-banner.created";
    protected readonly EVENT_BANNER_UPDATED = "cms-banner.updated";
    protected readonly EVENT_BANNER_DELETED = "cms-banner.deleted";
    protected readonly validRelations: string[];
    constructor(bannerRepository: Repository<CmsBanners>, dataSource: DataSource, eventEmitter: EventEmitter2);
    protected validateRelations(relations: string[]): string[];
    protected buildWhereClause(filter?: string): FindOptionsWhere<CmsBanners> | FindOptionsWhere<CmsBanners>[];
    protected findById(id: string, relations?: string[], throwError?: boolean): Promise<CmsBanners | null>;
    protected findByBusinessCode(businessCode: string, throwError?: boolean): Promise<CmsBanners | null>;
    protected toDto(banner: CmsBanners | null): CmsBannerDto | null;
    protected toDtos(banners: CmsBanners[]): CmsBannerDto[];
    protected isBannerActive(banner: CmsBanners): boolean;
    protected getBannerImageUrl(banner: CmsBanners, isMobile?: boolean): string;
    protected isTitleUnique(title: string, excludeId?: string): Promise<boolean>;
    protected getNextDisplayOrder(location?: CmsBannerLocation): Promise<number>;
    protected updateDisplayOrders(location: CmsBannerLocation | null, fromOrder: number, increment: number): Promise<void>;
}

"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UpdateCmsMenusController = void 0;
const openapi = require("@nestjs/swagger");
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const jwt_auth_guard_1 = require("../../common/guards/jwt-auth.guard");
const roles_decorator_1 = require("../../common/decorators/roles.decorator");
const permissions_decorator_1 = require("../../common/decorators/permissions.decorator");
const get_user_decorator_1 = require("../../common/decorators/get-user.decorator");
const update_cms_menus_service_1 = require("../services/update.cms-menus.service");
const update_cms_menu_dto_1 = require("../dto/update.cms-menu.dto");
const cms_menus_entity_1 = require("../entity/cms-menus.entity");
const api_response_dto_1 = require("../../common/dto/api-response.dto");
let UpdateCmsMenusController = class UpdateCmsMenusController {
    cmsMenusService;
    constructor(cmsMenusService) {
        this.cmsMenusService = cmsMenusService;
    }
    async update(id, updateCmsMenuDto, userId) {
        const result = await this.cmsMenusService.update(id, updateCmsMenuDto, userId);
        if (!result) {
            throw new common_1.NotFoundException(`Không tìm thấy menu CMS với ID: ${id}`);
        }
        return result;
    }
    async updateStatus(id, status, userId) {
        const result = await this.cmsMenusService.updateStatus(id, status, userId);
        if (!result) {
            throw new common_1.NotFoundException(`Không tìm thấy menu CMS với ID: ${id}`);
        }
        return result;
    }
    async updateSlugFromName(id, userId) {
        const result = await this.cmsMenusService.updateSlugFromName(id, userId);
        if (!result) {
            throw new common_1.NotFoundException(`Không tìm thấy menu CMS với ID: ${id}`);
        }
        return result;
    }
};
exports.UpdateCmsMenusController = UpdateCmsMenusController;
__decorate([
    (0, common_1.Put)(':id'),
    (0, roles_decorator_1.Roles)('ADMIN', 'EDITOR'),
    (0, permissions_decorator_1.Permissions)('cms-menu:update'),
    (0, swagger_1.ApiOperation)({ summary: 'Cập nhật thông tin menu CMS' }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Menu CMS đã được cập nhật thành công.',
        type: api_response_dto_1.ApiResponseDto,
        schema: {
            properties: {
                data: { $ref: '#/components/schemas/CmsMenuDto' },
            },
        },
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.NOT_FOUND,
        description: 'Không tìm thấy menu CMS.',
        type: api_response_dto_1.ApiResponseDto,
        schema: { properties: { data: { type: 'object', nullable: true } } },
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.BAD_REQUEST,
        description: 'Dữ liệu đầu vào không hợp lệ.',
        type: api_response_dto_1.ApiResponseDto,
        schema: { properties: { data: { type: 'object', nullable: true } } },
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.CONFLICT,
        description: 'Tên hoặc slug đã tồn tại.',
        type: api_response_dto_1.ApiResponseDto,
        schema: { properties: { data: { type: 'object', nullable: true } } },
    }),
    (0, swagger_1.ApiParam)({ name: 'id', type: String, description: 'ID của menu CMS' }),
    (0, swagger_1.ApiBody)({ type: update_cms_menu_dto_1.UpdateCmsMenuDto }),
    openapi.ApiResponse({ status: 200, type: Object }),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __param(1, (0, common_1.Body)()),
    __param(2, (0, get_user_decorator_1.GetUser)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, update_cms_menu_dto_1.UpdateCmsMenuDto, String]),
    __metadata("design:returntype", Promise)
], UpdateCmsMenusController.prototype, "update", null);
__decorate([
    (0, common_1.Patch)(':id/status'),
    (0, roles_decorator_1.Roles)('ADMIN', 'EDITOR'),
    (0, permissions_decorator_1.Permissions)('cms-menu:update'),
    (0, swagger_1.ApiOperation)({ summary: 'Cập nhật trạng thái menu CMS' }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Trạng thái menu CMS đã được cập nhật thành công.',
        type: api_response_dto_1.ApiResponseDto,
        schema: {
            properties: {
                data: { $ref: '#/components/schemas/CmsMenuDto' },
            },
        },
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.NOT_FOUND,
        description: 'Không tìm thấy menu CMS.',
        type: api_response_dto_1.ApiResponseDto,
        schema: { properties: { data: { type: 'object', nullable: true } } },
    }),
    (0, swagger_1.ApiParam)({ name: 'id', type: String, description: 'ID của menu CMS' }),
    (0, swagger_1.ApiBody)({
        schema: {
            type: 'object',
            properties: {
                status: {
                    type: 'string',
                    enum: Object.values(cms_menus_entity_1.CmsMenuStatus),
                    description: 'Trạng thái mới của menu',
                },
            },
            required: ['status'],
        },
    }),
    openapi.ApiResponse({ status: 200, type: Object }),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __param(1, (0, common_1.Body)('status')),
    __param(2, (0, get_user_decorator_1.GetUser)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String, String]),
    __metadata("design:returntype", Promise)
], UpdateCmsMenusController.prototype, "updateStatus", null);
__decorate([
    (0, common_1.Patch)(':id/slug-from-name'),
    (0, roles_decorator_1.Roles)('ADMIN', 'EDITOR'),
    (0, permissions_decorator_1.Permissions)('cms-menu:update'),
    (0, swagger_1.ApiOperation)({ summary: 'Cập nhật slug từ tên menu CMS' }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Slug menu CMS đã được cập nhật thành công.',
        type: api_response_dto_1.ApiResponseDto,
        schema: {
            properties: {
                data: { $ref: '#/components/schemas/CmsMenuDto' },
            },
        },
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.NOT_FOUND,
        description: 'Không tìm thấy menu CMS.',
        type: api_response_dto_1.ApiResponseDto,
        schema: { properties: { data: { type: 'object', nullable: true } } },
    }),
    (0, swagger_1.ApiParam)({ name: 'id', type: String, description: 'ID của menu CMS' }),
    openapi.ApiResponse({ status: 200, type: Object }),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __param(1, (0, get_user_decorator_1.GetUser)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String]),
    __metadata("design:returntype", Promise)
], UpdateCmsMenusController.prototype, "updateSlugFromName", null);
exports.UpdateCmsMenusController = UpdateCmsMenusController = __decorate([
    (0, swagger_1.ApiTags)('cms-menus'),
    (0, swagger_1.ApiBearerAuth)(),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, common_1.Controller)('cms/menus'),
    __metadata("design:paramtypes", [update_cms_menus_service_1.UpdateCmsMenusService])
], UpdateCmsMenusController);
//# sourceMappingURL=update.cms-menus.controller.js.map
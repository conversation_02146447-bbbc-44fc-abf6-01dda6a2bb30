import { NotificationsService } from '../services/notifications.service';
export declare const USER_REGISTERED_EVENT = "user.registered";
export declare const USER_UPDATED_EVENT = "user.updated";
export declare const USER_DELETED_EVENT = "user.deleted";
export interface UserRegisteredPayload {
    userId: string;
    username: string;
    email: string;
}
export interface UserUpdatedPayload {
    userId: string;
    username: string;
    updatedFields: string[];
}
export declare class UserEventListener {
    private readonly notificationsService;
    private readonly logger;
    constructor(notificationsService: NotificationsService);
    handleUserRegisteredEvent(payload: UserRegisteredPayload): Promise<void>;
    handleUserUpdatedEvent(payload: UserUpdatedPayload): void;
    handleAllUserEvents(payload: any, eventName: string): void;
}

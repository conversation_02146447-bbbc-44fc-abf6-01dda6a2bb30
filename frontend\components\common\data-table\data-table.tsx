"use client"

/**
 * Component DataTable cung cấp bảng dữ liệu với các tính năng:
 * - Header cố định khi cuộn
 * - Cột cố định (sticky columns) ở bên trái và bên phải
 * - Hỗ trợ đầy đủ các tính năng của TanStack Table
 *
 * Để sử dụng sticky columns, hãy thêm thuộc tính meta vào định nghĩa cột:
 * ```
 * {
 *   id: 'actions',
 *   meta: {
 *     isSticky: true,
 *     position: 'right' // hoặc 'left'
 *   }
 * }
 * ```
 *
 * Xem thêm hướng dẫn chi tiết trong file README.md
 */

import { flexRender, Table as TableType } from "@tanstack/react-table";

import { cn } from "@/lib/utils";
import { NoData } from "./no-data";

// Định nghĩa kiểu dữ liệu cho thuộc tính meta của cột
declare module '@tanstack/react-table' {
  interface ColumnMeta<TData, TValue> {
    isSticky?: boolean;
    position?: 'left' | 'right';
    header?: string;
  }
}

interface DataTableProps<TData> {
    table: TableType<TData>
    className?: string
    isLoading?: boolean
    noDataTitle?: string
    noDataDescription?: string
    noDataIcon?: 'no-data' | 'empty'
}

export function DataTable<TData>({
    table,
    className,
    isLoading = false,
    noDataTitle,
    noDataDescription,
    noDataIcon = 'no-data',
}: DataTableProps<TData>) {
    return (
        <div className={cn("w-full flex flex-col h-full", className)}>
            {/* Table Container with border */}
            <div className="border flex flex-col flex-1 overflow-hidden">
                {/* Single table with sticky header */}
                <div className="table-scroll-container">
                    <div className="table-viewport">
                        <table className="data-table">
                            <thead className="sticky-header">
                                {table.getHeaderGroups().map((headerGroup) => (
                                    <tr key={headerGroup.id}>
                                        {headerGroup.headers.map((header, index) => {
                                            // Determine if this column has sticky metadata
                                            const hasSticky = header.column.columnDef.meta?.isSticky;

                                            return (
                                                <th
                                                    key={header.id}
                                                    data-column-id={header.column.id}
                                                    style={{
                                                        width: header.getSize(),
                                                        minWidth: header.getSize(),
                                                        maxWidth: header.getSize(),
                                                        ...(hasSticky && {
                                                            position: 'sticky',
                                                            [header.column.columnDef.meta?.position === 'right' ? 'right' : 'left']: 0,
                                                            zIndex: 60,
                                                            boxShadow: header.column.columnDef.meta?.position === 'right'
                                                                ? '-5px 0 5px -5px rgba(0,0,0,0.1)'
                                                                : '5px 0 5px -5px rgba(0,0,0,0.1)'
                                                        }),
                                                        // Không cần logic fallback cho first column nữa
                                                        // Tất cả sticky columns phải được định nghĩa qua meta
                                                    }}
                                                    className={cn(
                                                        "bg-background py-2 text-left align-middle font-medium text-muted-foreground",
                                                        hasSticky && `sticky-${header.column.columnDef.meta?.position || 'left'}`,
                                                        // Thêm class dựa trên column ID thay vì index
                                                        header.column.id === 'select' && "checkbox-header"
                                                    )}
                                                >
                                                    {header.isPlaceholder
                                                        ? null
                                                        : flexRender(
                                                            header.column.columnDef.header,
                                                            header.getContext()
                                                        )}
                                                </th>
                                            )
                                        })}
                                    </tr>
                                ))}
                            </thead>
                            <tbody>
                                {isLoading ? (
                                    <tr>
                                        <td
                                            colSpan={table.getAllColumns().length}
                                            className="p-0 w-full h-full"
                                        >
                                            <div className="w-full h-full flex items-center justify-center no-data-container">
                                                <NoData
                                                    title="Đang tải dữ liệu..."
                                                    description="Vui lòng chờ trong giây lát"
                                                    icon="empty"
                                                    size="md"
                                                    showIcon={false}
                                                />
                                            </div>
                                        </td>
                                    </tr>
                                ) : table.getRowModel().rows?.length ? (
                                    table.getRowModel().rows.map((row) => (
                                        <tr
                                            key={row.id}
                                            data-state={row.getIsSelected() && "selected"}
                                            className="border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted"
                                        >
                                            {row.getVisibleCells().map((cell, index) => {
                                                // Determine if this column has sticky metadata
                                                const hasSticky = cell.column.columnDef.meta?.isSticky;

                                                return (
                                                    <td
                                                        key={cell.id}
                                                        data-column-id={cell.column.id}
                                                        style={{
                                                            width: cell.column.getSize(),
                                                            minWidth: cell.column.getSize(),
                                                            maxWidth: cell.column.getSize(),
                                                            ...(hasSticky && {
                                                                position: 'sticky',
                                                                [cell.column.columnDef.meta?.position === 'right' ? 'right' : 'left']: 0,
                                                                backgroundColor: 'var(--background)',
                                                                zIndex: 30,
                                                                boxShadow: cell.column.columnDef.meta?.position === 'right'
                                                                    ? '-5px 0 5px -5px rgba(0,0,0,0.1)'
                                                                    : '5px 0 5px -5px rgba(0,0,0,0.1)'
                                                            }),
                                                            // Không cần logic fallback cho first column nữa
                                                            // Tất cả sticky columns phải được định nghĩa qua meta
                                                        }}
                                                        className={cn(
                                                            "py-1 px-2 align-middle",
                                                            hasSticky && `sticky-${cell.column.columnDef.meta?.position || 'left'}`,
                                                            // Thêm class dựa trên column ID
                                                            cell.column.id === 'select' && "checkbox-cell"
                                                        )}
                                                    >
                                                        {flexRender(
                                                            cell.column.columnDef.cell,
                                                            cell.getContext()
                                                        )}
                                                    </td>
                                                )
                                            })}
                                        </tr>
                                    ))
                                ) : (
                                    <tr>
                                        <td
                                            colSpan={table.getAllColumns().length}
                                            className="p-0 w-full h-full"
                                        >
                                            <div className="w-full h-full flex items-center justify-center no-data-container">
                                                <NoData
                                                    title={noDataTitle}
                                                    description={noDataDescription}
                                                    icon={noDataIcon}
                                                    size="md"
                                                />
                                            </div>
                                        </td>
                                    </tr>
                                )}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    )
}
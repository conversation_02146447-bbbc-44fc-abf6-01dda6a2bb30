"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const core_1 = require("@nestjs/core");
const app_module_1 = require("./app.module");
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const config_1 = require("@nestjs/config");
const response_interceptor_1 = require("./common/interceptors/response.interceptor");
const exception_filter_1 = require("./common/filters/exception.filter");
const helmet_1 = require("helmet");
const nestjs_i18n_1 = require("nestjs-i18n");
const nestjs_pino_1 = require("nestjs-pino");
const typeorm_transactional_1 = require("typeorm-transactional");
const typeorm_1 = require("typeorm");
const cors_config_1 = require("./config/cors.config");
async function bootstrap() {
    (0, typeorm_transactional_1.initializeTransactionalContext)();
    const app = await core_1.NestFactory.create(app_module_1.AppModule, {
        bufferLogs: true,
    });
    app.useLogger(app.get(nestjs_pino_1.Logger));
    const configService = app.get(config_1.ConfigService);
    const httpAdapterHost = app.get(core_1.HttpAdapterHost);
    const i18nService = app.get(nestjs_i18n_1.I18nService);
    const dataSource = app.get(typeorm_1.DataSource);
    if (!dataSource) {
        console.error('FATAL ERROR: Could not retrieve DataSource instance from NestJS container.');
        process.exit(1);
    }
    if (!dataSource.isInitialized) {
        await dataSource.initialize();
    }
    (0, typeorm_transactional_1.addTransactionalDataSource)(dataSource);
    app.use((0, helmet_1.default)());
    cors_config_1.CorsConfigFactory.validate(configService);
    app.enableCors(cors_config_1.CorsConfigFactory.create(configService));
    app.useGlobalPipes(new common_1.ValidationPipe({
        transform: true,
        whitelist: true,
        forbidNonWhitelisted: true,
        transformOptions: {
            enableImplicitConversion: true,
        },
    }), new nestjs_i18n_1.I18nValidationPipe({
        stopAtFirstError: true,
    }));
    app.useGlobalInterceptors(new common_1.ClassSerializerInterceptor(app.get(core_1.Reflector)));
    app.useGlobalInterceptors(new response_interceptor_1.ResponseInterceptor());
    app.useGlobalFilters(new exception_filter_1.AllExceptionsFilter(httpAdapterHost, i18nService));
    const globalPrefix = configService.get('API_PREFIX', 'api');
    app.setGlobalPrefix(globalPrefix);
    app.enableVersioning({
        type: common_1.VersioningType.URI,
        defaultVersion: '1',
    });
    const apiVersionPrefix = `${globalPrefix}/v1`;
    const swaggerConfig = new swagger_1.DocumentBuilder()
        .setTitle(configService.get('SWAGGER_TITLE', 'Phygital-V API'))
        .setDescription(configService.get('SWAGGER_DESC', 'The Phygital-V API description'))
        .setVersion(configService.get('SWAGGER_VERSION', '1.0'))
        .addBearerAuth()
        .addServer(`http://localhost:${configService.get('PORT', 3168)}`, 'Local Development')
        .addServer(configService.get('PROD_URL', 'https://your-production-domain.com'), 'Production')
        .addTag('auth', 'Authentication endpoints')
        .addTag('users', 'User management endpoints')
        .addTag('roles', 'Role management endpoints')
        .addTag('permissions', 'Permission management endpoints')
        .addTag('tokens', 'Token management endpoints')
        .addTag('token-categories', 'Token category management endpoints')
        .addTag('token-prices', 'Token price management endpoints')
        .addTag('token-assets', 'Token asset management endpoints')
        .addTag('order-book', 'Order book management endpoints')
        .addTag('transactions', 'Transaction management endpoints')
        .addTag('price-alerts', 'Price alert management endpoints')
        .addTag('wallets', 'Wallet management endpoints')
        .addTag('payment-methods', 'Payment method management endpoints')
        .addTag('payment-providers', 'Payment provider management endpoints')
        .addTag('banks', 'Bank management endpoints')
        .addTag('agents', 'Agent management endpoints')
        .addTag('agent-levels', 'Agent level management endpoints')
        .addTag('agent-customers', 'Agent customer management endpoints')
        .addTag('agent-commissions', 'Agent commission management endpoints')
        .addTag('agent-commission-withdrawals', 'Agent commission withdrawal management endpoints')
        .addTag('contracts', 'Contract management endpoints')
        .addTag('contract-metadata', 'Contract metadata management endpoints')
        .addTag('user-kyc', 'User KYC management endpoints')
        .addTag('attachments', 'Attachment management endpoints')
        .addTag('activity-logs', 'Activity log management endpoints')
        .addTag('system-configs', 'System configuration endpoints')
        .build();
    const document = swagger_1.SwaggerModule.createDocument(app, swaggerConfig, {
        deepScanRoutes: true,
        operationIdFactory: (controllerKey, methodKey) => `${controllerKey.replace('Controller', '')}_${methodKey}`,
    });
    const swaggerPath = configService.get('SWAGGER_PATH', 'api-docs');
    swagger_1.SwaggerModule.setup(swaggerPath, app, document, {
        swaggerOptions: {
            persistAuthorization: true,
            displayRequestDuration: true,
            filter: true,
            showCommonExtensions: true,
            showExtensions: true,
            syntaxHighlight: {
                activate: true,
                theme: 'monokai',
            },
        },
        customSiteTitle: configService.get('SWAGGER_TITLE', 'Phygital-V API Docs'),
    });
    app.use(`/${swaggerPath}-json`, (req, res) => {
        res.setHeader('Content-Disposition', 'inline; filename=openapi.json');
        res.json(document);
    });
    const port = configService.get('PORT', 3000);
    await app.listen(port);
    const serverUrl = `http://localhost:${port}`;
    app.get(nestjs_pino_1.Logger).log(`🚀 Application is running on: ${serverUrl}`);
    app
        .get(nestjs_pino_1.Logger)
        .log(`📚 Swagger UI available at: ${serverUrl}/${swaggerPath}`);
    app
        .get(nestjs_pino_1.Logger)
        .log(`📄 OpenAPI Spec available at: ${serverUrl}/${swaggerPath}-json`);
    app.get(nestjs_pino_1.Logger).log(`🎯 API Prefix: /${globalPrefix}`);
    app.get(nestjs_pino_1.Logger).log(`ℹ️  API Versioning enabled: v1 (Default)`);
}
bootstrap();
//# sourceMappingURL=main.js.map
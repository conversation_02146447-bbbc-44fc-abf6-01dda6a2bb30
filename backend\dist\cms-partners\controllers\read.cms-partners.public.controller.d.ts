import { CustomPaginationQueryDto } from 'src/common/dto/custom-pagination-query.dto';
import { PaginationResponseDto } from 'src/common/dto/pagination-response.dto';
import { CmsPartnerType } from '../dto';
import { CmsPartnerPublicDto, CmsPartnerPublicStatisticsDto } from '../dto/cms-partner.public.dto';
import { ReadCmsPartnersService } from '../services/read.cms-partners.service';
export declare class ReadCmsPartnersPublicController {
    private readonly readCmsPartnersService;
    private readonly logger;
    constructor(readCmsPartnersService: ReadCmsPartnersService);
    getActivePartners(paginationQuery: CustomPaginationQueryDto): Promise<PaginationResponseDto<CmsPartnerPublicDto>>;
    getPartnersByType(type: CmsPartnerType, paginationQuery: CustomPaginationQueryDto): Promise<PaginationResponseDto<CmsPartnerPublicDto>>;
    search(searchTerm: string, paginationQuery: CustomPaginationQueryDto): Promise<PaginationResponseDto<CmsPartnerPublicDto>>;
    findAllPublic(paginationQuery: CustomPaginationQueryDto): Promise<PaginationResponseDto<CmsPartnerPublicDto>>;
    findById(id: string): Promise<CmsPartnerPublicDto>;
    getPublicStatistics(): Promise<CmsPartnerPublicStatisticsDto>;
}

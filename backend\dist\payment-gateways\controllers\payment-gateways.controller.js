"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var PaymentGatewaysController_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.PaymentGatewaysController = void 0;
const openapi = require("@nestjs/swagger");
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const payment_gateways_service_1 = require("../payment-gateways.service");
const create_payment_dto_1 = require("../dto/create-payment.dto");
const payment_gateway_type_enum_1 = require("../enums/payment-gateway-type.enum");
let PaymentGatewaysController = PaymentGatewaysController_1 = class PaymentGatewaysController {
    paymentGatewaysService;
    logger = new common_1.Logger(PaymentGatewaysController_1.name);
    constructor(paymentGatewaysService) {
        this.paymentGatewaysService = paymentGatewaysService;
    }
    async createPayment(createPaymentDto) {
        try {
            const callbacks = {
                onPaymentCreated: async (result) => {
                    this.logger.log(`Payment created: ${result.transactionId}`);
                },
                onPaymentSuccess: async (result) => {
                    this.logger.log(`Payment success: ${result.transactionId}`);
                },
                onPaymentFailed: async (result) => {
                    this.logger.log(`Payment failed: ${result.transactionId} - ${result.message}`);
                },
            };
            return await this.paymentGatewaysService.createPayment(createPaymentDto, callbacks);
        }
        catch (error) {
            this.logger.error(`Error creating payment: ${error.message}`, error.stack);
            throw error;
        }
    }
    async handleVnpayReturn(query, res) {
        try {
            const result = await this.paymentGatewaysService.handleVnpayCallback(query);
            if (result.isSuccess) {
                const successUrl = `${process.env.FRONTEND_URL}/payment/success?transactionId=${result.transactionId}&amount=${result.amount}`;
                res.redirect(successUrl);
            }
            else {
                const failureUrl = `${process.env.FRONTEND_URL}/payment/failure?message=${encodeURIComponent(result.message || 'Payment failed')}`;
                res.redirect(failureUrl);
            }
        }
        catch (error) {
            this.logger.error(`Error handling VNPAY return: ${error.message}`, error.stack);
            const errorUrl = `${process.env.FRONTEND_URL}/payment/error?message=${encodeURIComponent('System error')}`;
            res.redirect(errorUrl);
        }
    }
    async handleVnpayIpn(query) {
        try {
            const result = await this.paymentGatewaysService.handleVnpayIpn(query);
            return {
                RspCode: result.code,
                Message: result.message,
            };
        }
        catch (error) {
            this.logger.error(`Error handling VNPAY IPN: ${error.message}`, error.stack);
            return {
                RspCode: '99',
                Message: 'Unknown error',
            };
        }
    }
    async queryTransaction(gatewayType, queryData) {
        try {
            return await this.paymentGatewaysService.queryTransaction(gatewayType, queryData);
        }
        catch (error) {
            this.logger.error(`Error querying transaction: ${error.message}`, error.stack);
            throw error;
        }
    }
    async refundTransaction(gatewayType, refundData) {
        try {
            return await this.paymentGatewaysService.refundTransaction(gatewayType, refundData);
        }
        catch (error) {
            this.logger.error(`Error refunding transaction: ${error.message}`, error.stack);
            throw error;
        }
    }
    async getTransactionStatus(transactionId) {
        try {
            return {
                message: 'Feature not implemented yet',
                transactionId,
            };
        }
        catch (error) {
            this.logger.error(`Error getting transaction status: ${error.message}`, error.stack);
            throw error;
        }
    }
    async testWebhook(body, req) {
        this.logger.log(`Webhook test received: ${JSON.stringify(body)}`);
        this.logger.log(`Headers: ${JSON.stringify(req.headers)}`);
        return {
            success: true,
            message: 'Webhook received successfully',
            timestamp: new Date().toISOString(),
            data: body,
        };
    }
    async healthCheck() {
        return {
            status: 'healthy',
            timestamp: new Date().toISOString(),
            services: {
                vnpay: 'configured',
                momo: 'configured',
                database: 'connected',
            },
        };
    }
};
exports.PaymentGatewaysController = PaymentGatewaysController;
__decorate([
    (0, common_1.Post)('create-payment'),
    (0, swagger_1.ApiOperation)({ summary: 'Tạo URL thanh toán' }),
    (0, swagger_1.ApiResponse)({ status: 201, description: 'Tạo thành công' }),
    (0, swagger_1.ApiBody)({ type: create_payment_dto_1.CreatePaymentDto }),
    openapi.ApiResponse({ status: 201, type: Object }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [create_payment_dto_1.CreatePaymentDto]),
    __metadata("design:returntype", Promise)
], PaymentGatewaysController.prototype, "createPayment", null);
__decorate([
    (0, common_1.Get)('vnpay/return'),
    (0, swagger_1.ApiOperation)({ summary: 'Xử lý VNPAY Return URL' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Xử lý thành công' }),
    openapi.ApiResponse({ status: 200 }),
    __param(0, (0, common_1.Query)()),
    __param(1, (0, common_1.Res)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", Promise)
], PaymentGatewaysController.prototype, "handleVnpayReturn", null);
__decorate([
    (0, common_1.Post)('vnpay/ipn'),
    (0, common_1.HttpCode)(common_1.HttpStatus.OK),
    (0, swagger_1.ApiOperation)({ summary: 'Xử lý VNPAY IPN' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'IPN processed' }),
    openapi.ApiResponse({ status: common_1.HttpStatus.OK }),
    __param(0, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], PaymentGatewaysController.prototype, "handleVnpayIpn", null);
__decorate([
    (0, common_1.Post)('query/:gatewayType'),
    (0, swagger_1.ApiOperation)({ summary: 'Truy vấn giao dịch từ gateway' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Truy vấn thành công' }),
    openapi.ApiResponse({ status: 201, type: Object }),
    __param(0, (0, common_1.Param)('gatewayType')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], PaymentGatewaysController.prototype, "queryTransaction", null);
__decorate([
    (0, common_1.Post)('refund/:gatewayType'),
    (0, swagger_1.ApiOperation)({ summary: 'Hoàn tiền từ gateway' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Hoàn tiền thành công' }),
    openapi.ApiResponse({ status: 201, type: Object }),
    __param(0, (0, common_1.Param)('gatewayType')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], PaymentGatewaysController.prototype, "refundTransaction", null);
__decorate([
    (0, common_1.Get)('transaction/:transactionId/status'),
    (0, swagger_1.ApiOperation)({ summary: 'Lấy trạng thái giao dịch' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Lấy thành công' }),
    openapi.ApiResponse({ status: 200, type: Object }),
    __param(0, (0, common_1.Param)('transactionId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], PaymentGatewaysController.prototype, "getTransactionStatus", null);
__decorate([
    (0, common_1.Post)('webhook/test'),
    (0, swagger_1.ApiOperation)({ summary: 'Test webhook endpoint' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Test successful' }),
    openapi.ApiResponse({ status: 201, type: Object }),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", Promise)
], PaymentGatewaysController.prototype, "testWebhook", null);
__decorate([
    (0, common_1.Get)('health'),
    (0, swagger_1.ApiOperation)({ summary: 'Health check' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Service healthy' }),
    openapi.ApiResponse({ status: 200, type: Object }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], PaymentGatewaysController.prototype, "healthCheck", null);
exports.PaymentGatewaysController = PaymentGatewaysController = PaymentGatewaysController_1 = __decorate([
    (0, swagger_1.ApiTags)('Payment Gateways'),
    (0, common_1.Controller)('payment-gateways'),
    __metadata("design:paramtypes", [payment_gateways_service_1.PaymentGatewaysService])
], PaymentGatewaysController);
//# sourceMappingURL=payment-gateways.controller.js.map
"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var RateLimiterService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.RateLimiterService = void 0;
const common_1 = require("@nestjs/common");
const config_1 = require("@nestjs/config");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const system_config_entity_1 = require("../../../system-configs/entities/system-config.entity");
const tradingview_data_model_1 = require("../models/tradingview-data.model");
let RateLimiterService = RateLimiterService_1 = class RateLimiterService {
    configService;
    systemConfigRepository;
    logger = new common_1.Logger(RateLimiterService_1.name);
    clientRequests = new Map();
    rateLimit;
    rateLimitWindow;
    constructor(configService, systemConfigRepository) {
        this.configService = configService;
        this.systemConfigRepository = systemConfigRepository;
        this.initializeRateLimits();
    }
    async initializeRateLimits() {
        try {
            const rateLimitConfig = await this.systemConfigRepository.findOne({
                where: { configKey: tradingview_data_model_1.TradingViewConfigKeys.RATE_LIMIT, isDeleted: false },
            });
            const rateLimitWindowConfig = await this.systemConfigRepository.findOne({
                where: { configKey: tradingview_data_model_1.TradingViewConfigKeys.RATE_LIMIT_WINDOW, isDeleted: false },
            });
            if (rateLimitConfig && rateLimitWindowConfig) {
                this.rateLimit = parseInt(rateLimitConfig.configValue, 10);
                this.rateLimitWindow = parseInt(rateLimitWindowConfig.configValue, 10);
            }
            else {
                this.rateLimit = this.configService.get('TRADINGVIEW_RATE_LIMIT', 100);
                this.rateLimitWindow = this.configService.get('TRADINGVIEW_RATE_LIMIT_WINDOW', 60);
                if (!rateLimitConfig) {
                    await this.systemConfigRepository.save({
                        configKey: tradingview_data_model_1.TradingViewConfigKeys.RATE_LIMIT,
                        configValue: this.rateLimit.toString(),
                        description: 'Giới hạn số lượng yêu cầu đến TradingView API trong một khoảng thời gian',
                        configGroup: 'tradingview',
                        configType: 'number',
                    });
                }
                if (!rateLimitWindowConfig) {
                    await this.systemConfigRepository.save({
                        configKey: tradingview_data_model_1.TradingViewConfigKeys.RATE_LIMIT_WINDOW,
                        configValue: this.rateLimitWindow.toString(),
                        description: 'Khoảng thời gian (giây) áp dụng giới hạn yêu cầu đến TradingView API',
                        configGroup: 'tradingview',
                        configType: 'number',
                    });
                }
            }
            this.logger.log(`Giới hạn tần suất đã được khởi tạo: ${this.rateLimit} yêu cầu trong ${this.rateLimitWindow} giây`);
        }
        catch (error) {
            this.logger.error(`Lỗi khi khởi tạo giới hạn tần suất: ${error.message}`, error.stack);
            this.rateLimit = 100;
            this.rateLimitWindow = 60;
        }
    }
    async isRateLimited(clientId) {
        const now = Date.now();
        let clientData = this.clientRequests.get(clientId);
        if (!clientData || now > clientData.resetTime) {
            clientData = {
                count: 1,
                resetTime: now + this.rateLimitWindow * 1000,
            };
            this.clientRequests.set(clientId, clientData);
            return false;
        }
        clientData.count++;
        if (clientData.count > this.rateLimit) {
            this.logger.warn(`Vượt quá giới hạn tần suất cho client ${clientId}: ${clientData.count} yêu cầu`);
            return true;
        }
        return false;
    }
    getRemainingRequests(clientId) {
        const clientData = this.clientRequests.get(clientId);
        if (!clientData) {
            return this.rateLimit;
        }
        const remaining = Math.max(0, this.rateLimit - clientData.count);
        return remaining;
    }
    getResetTime(clientId) {
        const clientData = this.clientRequests.get(clientId);
        if (!clientData) {
            return Date.now() + this.rateLimitWindow * 1000;
        }
        return clientData.resetTime;
    }
};
exports.RateLimiterService = RateLimiterService;
exports.RateLimiterService = RateLimiterService = RateLimiterService_1 = __decorate([
    (0, common_1.Injectable)(),
    __param(1, (0, typeorm_1.InjectRepository)(system_config_entity_1.SystemConfig)),
    __metadata("design:paramtypes", [config_1.ConfigService,
        typeorm_2.Repository])
], RateLimiterService);
//# sourceMappingURL=rate-limiter.service.js.map
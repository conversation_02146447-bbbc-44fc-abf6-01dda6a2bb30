import { ReadCmsCategoriesService } from '../services/read.cms-categories.service';
import { CmsCategoryDto } from '../dto/cms-category.dto';
import { CustomPaginationQueryDto } from '../../common/dto/custom-pagination-query.dto';
import { PaginationResponseDto } from '../../common/dto/pagination-response.dto';
export declare class ReadCmsCategoriesController {
    private readonly cmsCategoriesService;
    constructor(cmsCategoriesService: ReadCmsCategoriesService);
    findAll(paginationQuery: CustomPaginationQueryDto): Promise<PaginationResponseDto<CmsCategoryDto>>;
    getStatistics(): Promise<{
        total: number;
        statusCounts: {
            active: number;
            inactive: number;
        };
    }>;
    count(filter?: string): Promise<number>;
    search(keyword: string, paginationQuery: CustomPaginationQueryDto): Promise<PaginationResponseDto<CmsCategoryDto>>;
    findDeleted(paginationQuery: CustomPaginationQueryDto): Promise<PaginationResponseDto<CmsCategoryDto>>;
    findOne(id: string, relations?: string): Promise<CmsCategoryDto | null>;
}

"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var EcomProductsMigrationService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.EcomProductsMigrationService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const typeorm_transactional_1 = require("typeorm-transactional");
const ecom_products_entity_1 = require("../entity/ecom-products.entity");
const slug_ecom_products_service_1 = require("./slug.ecom-products.service");
let EcomProductsMigrationService = EcomProductsMigrationService_1 = class EcomProductsMigrationService {
    ecomProductRepository;
    dataSource;
    slugService;
    logger = new common_1.Logger(EcomProductsMigrationService_1.name);
    constructor(ecomProductRepository, dataSource, slugService) {
        this.ecomProductRepository = ecomProductRepository;
        this.dataSource = dataSource;
        this.slugService = slugService;
    }
    async generateSlugsForExistingProducts(userId) {
        try {
            this.logger.log('Bắt đầu tạo slug cho các sản phẩm chưa có slug');
            const productsWithoutSlugCount = await this.ecomProductRepository
                .createQueryBuilder('product')
                .where('(product.slug IS NULL OR product.slug = :empty)', { empty: '' })
                .andWhere('product.isDeleted = :isDeleted', { isDeleted: false })
                .getCount();
            if (productsWithoutSlugCount === 0) {
                return {
                    success: true,
                    message: 'Tất cả sản phẩm đã có slug',
                    data: {
                        totalProcessed: 0,
                        totalUpdated: 0,
                        totalSkipped: 0,
                        errors: []
                    }
                };
            }
            this.logger.log(`Tìm thấy ${productsWithoutSlugCount} sản phẩm chưa có slug`);
            const totalUpdated = await this.slugService.updateMissingSlugs();
            if (totalUpdated > 0) {
                await this.ecomProductRepository
                    .createQueryBuilder()
                    .update(ecom_products_entity_1.EcomProduct)
                    .set({
                    updatedAt: new Date(),
                    updatedBy: userId
                })
                    .where('slug IS NOT NULL')
                    .andWhere('slug != :empty', { empty: '' })
                    .andWhere('updatedBy IS NULL')
                    .execute();
            }
            const result = {
                success: totalUpdated > 0,
                message: totalUpdated > 0
                    ? `Đã tạo slug cho ${totalUpdated} sản phẩm`
                    : 'Không có sản phẩm nào được cập nhật',
                data: {
                    totalProcessed: productsWithoutSlugCount,
                    totalUpdated: totalUpdated,
                    totalSkipped: productsWithoutSlugCount - totalUpdated,
                    errors: []
                }
            };
            this.logger.log(`Hoàn thành migration: ${result.message}`);
            return result;
        }
        catch (error) {
            this.logger.error(`Lỗi khi tạo slug cho sản phẩm: ${error.message}`, error.stack);
            throw new common_1.InternalServerErrorException(`Không thể tạo slug cho sản phẩm: ${error.message}`);
        }
    }
    async getSlugStatistics() {
        try {
            const totalProducts = await this.ecomProductRepository.count({
                where: { isDeleted: false }
            });
            const productsWithSlug = await this.ecomProductRepository
                .createQueryBuilder('product')
                .where('product.isDeleted = :isDeleted', { isDeleted: false })
                .andWhere('product.slug IS NOT NULL')
                .andWhere('product.slug != :empty', { empty: '' })
                .getCount();
            const productsWithoutSlug = totalProducts - productsWithSlug;
            const slugCounts = await this.ecomProductRepository
                .createQueryBuilder('product')
                .select('product.slug', 'slug')
                .addSelect('COUNT(*)', 'count')
                .where('product.slug IS NOT NULL')
                .andWhere('product.slug != :empty', { empty: '' })
                .andWhere('product.isDeleted = :isDeleted', { isDeleted: false })
                .groupBy('product.slug')
                .having('COUNT(*) > 1')
                .getRawMany();
            const duplicateSlugs = slugCounts.map(item => item.slug);
            const allSlugs = await this.ecomProductRepository
                .createQueryBuilder('product')
                .select('product.slug', 'slug')
                .where('product.slug IS NOT NULL')
                .andWhere('product.slug != :empty', { empty: '' })
                .andWhere('product.isDeleted = :isDeleted', { isDeleted: false })
                .getRawMany();
            const invalidSlugs = allSlugs
                .map(item => item.slug)
                .filter(slug => !this.slugService.validateSlug(slug));
            return {
                totalProducts,
                productsWithSlug,
                productsWithoutSlug,
                duplicateSlugs,
                invalidSlugs
            };
        }
        catch (error) {
            this.logger.error(`Lỗi khi lấy thống kê slug: ${error.message}`, error.stack);
            throw new common_1.InternalServerErrorException(`Không thể lấy thống kê slug: ${error.message}`);
        }
    }
    async fixInvalidSlugs(userId) {
        try {
            this.logger.log('Bắt đầu sửa chữa các slug không hợp lệ');
            const allProducts = await this.ecomProductRepository.find({
                where: { isDeleted: false },
                select: ['id', 'slug', 'productName', 'productCode']
            });
            const productsWithInvalidSlugs = allProducts.filter(product => product.slug && !this.slugService.validateSlug(product.slug));
            if (productsWithInvalidSlugs.length === 0) {
                return {
                    success: true,
                    message: 'Tất cả slug đều hợp lệ',
                    data: {
                        totalProcessed: 0,
                        totalUpdated: 0,
                        totalSkipped: 0,
                        errors: []
                    }
                };
            }
            this.logger.log(`Tìm thấy ${productsWithInvalidSlugs.length} sản phẩm có slug không hợp lệ`);
            const result = {
                success: true,
                message: '',
                data: {
                    totalProcessed: productsWithInvalidSlugs.length,
                    totalUpdated: 0,
                    totalSkipped: 0,
                    errors: []
                }
            };
            const validSlugs = allProducts
                .map(p => p.slug)
                .filter(slug => slug && this.slugService.validateSlug(slug));
            for (const product of productsWithInvalidSlugs) {
                try {
                    const newSlug = await this.slugService.generateUniqueSlugForCreate(product.productName || product.productCode || `product-${product.id}`);
                    await this.ecomProductRepository.update({ id: product.id }, {
                        slug: newSlug,
                        updatedAt: new Date(),
                        updatedBy: userId
                    });
                    validSlugs.push(newSlug);
                    result.data.totalUpdated++;
                    this.logger.debug(`Đã sửa slug "${product.slug}" thành "${newSlug}" cho sản phẩm ID ${product.id}`);
                }
                catch (error) {
                    const errorMessage = `Lỗi khi sửa slug cho sản phẩm ID ${product.id}: ${error.message}`;
                    this.logger.error(errorMessage);
                    result.data.errors.push(errorMessage);
                    result.data.totalSkipped++;
                }
            }
            if (result.data.totalUpdated > 0) {
                result.message = `Đã sửa ${result.data.totalUpdated} slug không hợp lệ`;
                if (result.data.totalSkipped > 0) {
                    result.message += `, bỏ qua ${result.data.totalSkipped} sản phẩm do lỗi`;
                }
            }
            else {
                result.message = 'Không có slug nào được sửa';
                result.success = false;
            }
            this.logger.log(`Hoàn thành sửa chữa slug: ${result.message}`);
            return result;
        }
        catch (error) {
            this.logger.error(`Lỗi khi sửa chữa slug: ${error.message}`, error.stack);
            throw new common_1.InternalServerErrorException(`Không thể sửa chữa slug: ${error.message}`);
        }
    }
    async generateSlugsForProducts(productIds, userId) {
        try {
            this.logger.log(`Bắt đầu tạo slug cho ${productIds.length} sản phẩm`);
            const products = await this.ecomProductRepository
                .createQueryBuilder('product')
                .where('product.id IN (:...productIds)', { productIds })
                .andWhere('product.isDeleted = :isDeleted', { isDeleted: false })
                .select(['product.id', 'product.productName', 'product.productCode', 'product.slug'])
                .getMany();
            if (products.length === 0) {
                return {
                    success: false,
                    message: 'Không tìm thấy sản phẩm nào',
                    data: {
                        totalProcessed: 0,
                        totalUpdated: 0,
                        totalSkipped: 0,
                        errors: []
                    }
                };
            }
            const result = {
                success: true,
                message: '',
                data: {
                    totalProcessed: products.length,
                    totalUpdated: 0,
                    totalSkipped: 0,
                    errors: []
                }
            };
            for (const product of products) {
                try {
                    const newSlug = await this.slugService.generateUniqueSlugForCreate(product.productName || product.productCode || `product-${product.id}`);
                    await this.ecomProductRepository.update({ id: product.id }, {
                        slug: newSlug,
                        updatedAt: new Date(),
                        updatedBy: userId
                    });
                    result.data.totalUpdated++;
                    this.logger.debug(`Đã tạo slug "${newSlug}" cho sản phẩm ID ${product.id}`);
                }
                catch (error) {
                    const errorMessage = `Lỗi khi tạo slug cho sản phẩm ID ${product.id}: ${error.message}`;
                    this.logger.error(errorMessage);
                    result.data.errors.push(errorMessage);
                    result.data.totalSkipped++;
                }
            }
            result.message = `Đã tạo slug cho ${result.data.totalUpdated}/${products.length} sản phẩm`;
            this.logger.log(`Hoàn thành tạo slug: ${result.message}`);
            return result;
        }
        catch (error) {
            this.logger.error(`Lỗi khi tạo slug cho sản phẩm: ${error.message}`, error.stack);
            throw new common_1.InternalServerErrorException(`Không thể tạo slug cho sản phẩm: ${error.message}`);
        }
    }
};
exports.EcomProductsMigrationService = EcomProductsMigrationService;
__decorate([
    (0, typeorm_transactional_1.Transactional)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], EcomProductsMigrationService.prototype, "generateSlugsForExistingProducts", null);
__decorate([
    (0, typeorm_transactional_1.Transactional)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], EcomProductsMigrationService.prototype, "fixInvalidSlugs", null);
__decorate([
    (0, typeorm_transactional_1.Transactional)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Array, String]),
    __metadata("design:returntype", Promise)
], EcomProductsMigrationService.prototype, "generateSlugsForProducts", null);
exports.EcomProductsMigrationService = EcomProductsMigrationService = EcomProductsMigrationService_1 = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(ecom_products_entity_1.EcomProduct)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        typeorm_2.DataSource,
        slug_ecom_products_service_1.SlugEcomProductsService])
], EcomProductsMigrationService);
//# sourceMappingURL=migration.ecom-products.service.js.map
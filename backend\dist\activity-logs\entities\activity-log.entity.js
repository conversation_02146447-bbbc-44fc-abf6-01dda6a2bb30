"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ActivityLog = void 0;
const openapi = require("@nestjs/swagger");
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
const typeorm_1 = require("typeorm");
const user_entity_1 = require("../../users/entities/user.entity");
const base_entity_1 = require("../../common/entities/base.entity");
let ActivityLog = class ActivityLog extends base_entity_1.BaseEntity {
    userId;
    action;
    module;
    description;
    ipAddress;
    userAgent;
    metadata;
    user;
    getEntityName() {
        return 'activity_logs';
    }
    static _OPENAPI_METADATA_FACTORY() {
        return { userId: { required: true, type: () => String }, action: { required: true, type: () => String }, module: { required: true, type: () => String }, description: { required: true, type: () => String }, ipAddress: { required: true, type: () => String }, userAgent: { required: true, type: () => String }, metadata: { required: true, type: () => String }, user: { required: true, type: () => require("../../users/entities/user.entity").User } };
    }
};
exports.ActivityLog = ActivityLog;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'ID người dùng',
        example: '550e8400-e29b-41d4-a716-************',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, typeorm_1.Column)({ type: 'uuid', nullable: true, name: 'user_id' }),
    __metadata("design:type", String)
], ActivityLog.prototype, "userId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Hành động', example: 'LOGIN' }),
    (0, class_validator_1.IsNotEmpty)(),
    (0, class_validator_1.IsString)(),
    (0, typeorm_1.Column)({ type: 'varchar', length: 100, nullable: false, name: 'action' }),
    __metadata("design:type", String)
], ActivityLog.prototype, "action", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Module', example: 'auth' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, typeorm_1.Column)({ type: 'varchar', length: 100, nullable: true, name: 'module' }),
    __metadata("design:type", String)
], ActivityLog.prototype, "module", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Mô tả', example: 'Đăng nhập thành công' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, typeorm_1.Column)({ type: 'text', nullable: true, name: 'description' }),
    __metadata("design:type", String)
], ActivityLog.prototype, "description", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Địa chỉ IP', example: '***********' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, typeorm_1.Column)({ type: 'varchar', length: 45, nullable: true, name: 'ip_address' }),
    __metadata("design:type", String)
], ActivityLog.prototype, "ipAddress", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'User Agent',
        example: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, typeorm_1.Column)({ type: 'text', nullable: true, name: 'user_agent' }),
    __metadata("design:type", String)
], ActivityLog.prototype, "userAgent", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Dữ liệu bổ sung',
        example: '{"key": "value"}',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, typeorm_1.Column)({ type: 'text', nullable: true, name: 'metadata' }),
    __metadata("design:type", String)
], ActivityLog.prototype, "metadata", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => user_entity_1.User, (user) => user.activityLogs, { nullable: true }),
    (0, typeorm_1.JoinColumn)({ name: 'user_id' }),
    (0, swagger_1.ApiProperty)({
        type: () => user_entity_1.User,
        description: 'Người dùng tương ứng với log',
    }),
    __metadata("design:type", user_entity_1.User)
], ActivityLog.prototype, "user", void 0);
exports.ActivityLog = ActivityLog = __decorate([
    (0, typeorm_1.Entity)('activity_logs')
], ActivityLog);
//# sourceMappingURL=activity-log.entity.js.map
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AddDeletedAtOrderBook1747644178216 = void 0;
class AddDeletedAtOrderBook1747644178216 {
    async up(queryRunner) {
        const tableExists = await queryRunner.hasTable('order_book');
        if (!tableExists) {
            return;
        }
        const columnExists = await queryRunner.hasColumn('order_book', 'deleted_at');
        if (columnExists) {
            return;
        }
        await queryRunner.query(`
          ALTER TABLE "order_book" 
          ADD COLUMN "deleted_at" TIMESTAMP WITH TIME ZONE DEFAULT NULL
        `);
    }
    async down(queryRunner) {
        const tableExists = await queryRunner.hasTable('order_book');
        if (!tableExists) {
            return;
        }
        const columnExists = await queryRunner.hasColumn('order_book', 'deleted_at');
        if (!columnExists) {
            return;
        }
        await queryRunner.query(`
          ALTER TABLE "order_book" 
          DROP COLUMN "deleted_at"
        `);
    }
}
exports.AddDeletedAtOrderBook1747644178216 = AddDeletedAtOrderBook1747644178216;
//# sourceMappingURL=1747644178216-addDeletedAtOrderBook.js.map
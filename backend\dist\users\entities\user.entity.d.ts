import { ActivityLog } from '../../activity-logs/entities/activity-log.entity';
import { Agent } from '../../agents/entities/agent.entity';
import { BaseEntity } from '../../common/entities/base.entity';
import { PaymentMethod } from '../../payment-methods/entities/payment-method.entity';
import { PriceAlert } from '../../price-alerts/entities/price-alert.entity';
import { Asset } from '../../token-assets/entities/asset.entity';
import { Transaction } from '../../transactions/entities/transaction.entity';
import { UserKyc } from '../../user-kyc/entities/user-kyc.entity';
import { CryptoTransaction } from '../../crypto-transactions/entities/crypto-transaction.entity';
import { CryptoWallet } from '../../crypto-wallets/entities/crypto-wallet.entity';
import { OrderBook } from '../../order-book/entities/order-book.entity';
import { UserRole } from '../../users/entities/user-role.entity';
import { Wallet } from '../../wallets/entities/wallet.entity';
export declare class User extends BaseEntity {
    username: string;
    email: string;
    passwordHash: string;
    fullName: string;
    phone: string;
    address: string;
    bio: string;
    birthday: Date;
    isActive: boolean;
    isKycVerified: boolean;
    isAgent: boolean;
    twoFaEnabled: boolean;
    twoFaSecret: string;
    notificationEmail: boolean;
    notificationSms: boolean;
    referralCode: string;
    parentId: string | null;
    path: string | null;
    referredBy: User;
    referrals: User[];
    emailVerified: boolean;
    verificationToken: string;
    resetToken: string;
    resetTokenExpiry: Date;
    verificationTokenExpiry: Date;
    googleId: string;
    phoneVerified: boolean;
    phoneVerificationToken: string;
    phoneVerificationTokenExpiry: Date;
    avatarUrl: string;
    userRoles: UserRole[];
    tokenAssets: Asset[];
    activityLogs: ActivityLog[];
    agent: Agent;
    approvedAgents: Agent[];
    orderBooks: OrderBook[];
    paymentMethods: PaymentMethod[];
    priceAlerts: PriceAlert[];
    transactions: Transaction[];
    cryptoTransactions: CryptoTransaction[];
    userKycs: UserKyc[];
    wallets: Wallet[];
    cryptoWallets: CryptoWallet[];
    getEntityName(): string;
}

"use client"

import * as React from "react"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { cn } from "@/lib/utils"

interface LogoAvatarProps {
  src?: string | null
  alt: string
  fallback?: string
  size?: 'xs' | 'sm' | 'md' | 'lg' | 'xl'
  objectFit?: 'contain' | 'cover' | 'fill'
  className?: string
}

const sizeClasses = {
  xs: 'size-4',
  sm: 'size-6', 
  md: 'size-8',
  lg: 'size-10',
  xl: 'size-12'
}

const objectFitClasses = {
  contain: 'object-contain',
  cover: 'object-cover',
  fill: 'object-fill'
}

export function LogoAvatar({
  src,
  alt,
  fallback,
  size = 'md',
  objectFit = 'contain',
  className
}: LogoAvatarProps) {
  const fallbackText = fallback || (alt ? alt[0].toUpperCase() : '?')

  return (
    <Avatar className={cn(sizeClasses[size], 'shrink-0', className)}>
      <AvatarImage 
        src={src || undefined}
        alt={alt}
        className={objectFitClasses[objectFit]}
      />
      <AvatarFallback>{fallbackText}</AvatarFallback>
    </Avatar>
  )
}

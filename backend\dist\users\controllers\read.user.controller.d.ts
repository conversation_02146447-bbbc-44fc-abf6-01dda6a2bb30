import { UserDto } from '../dto/user.dto';
import { ReadUserService } from '../services/read.user.service';
import { PaginationResponseDto } from '../../common/dto/pagination-response.dto';
import { CustomPaginationQueryDto } from '../../common/dto/custom-pagination-query.dto';
export declare class ReadUserController {
    private readonly readUserService;
    constructor(readUserService: ReadUserService);
    findAll(paginationQuery: CustomPaginationQueryDto, userId?: string, roles?: string[]): Promise<PaginationResponseDto<UserDto>>;
    search(keyword: string, paginationQuery: CustomPaginationQueryDto, userId?: string, roles?: string[]): Promise<PaginationResponseDto<UserDto>>;
    getStatistics(): Promise<{
        total: number;
        activeCounts: {
            true: number;
            false: number;
            PENDING: number;
        };
    }>;
    count(filter?: string): Promise<number>;
    findDeleted(paginationQuery: CustomPaginationQueryDto): Promise<PaginationResponseDto<UserDto>>;
    findOne(id: string, relations?: string, userId?: string, roles?: string[]): Promise<UserDto>;
}

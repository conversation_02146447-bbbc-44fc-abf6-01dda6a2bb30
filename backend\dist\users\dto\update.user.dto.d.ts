import { UpdateUserRoleDto } from './update.user-role.dto';
import { AssetDto } from '../../token-assets/dto/asset.dto';
export declare class UpdateUserDto {
    id: string;
    username?: string;
    email?: string;
    password?: string;
    passwordHash?: string;
    fullName?: string;
    phone?: string;
    address?: string;
    bio?: string;
    birthday?: Date;
    isActive?: boolean;
    emailVerified?: boolean;
    verificationToken?: string;
    verificationTokenExpiry?: Date;
    twoFaEnabled?: boolean;
    notificationEmail?: boolean;
    notificationSms?: boolean;
    referralCode?: string;
    referredByCode?: string;
    referredById?: string;
    parentId?: string;
    path?: string;
    phoneVerified?: boolean;
    roles?: UpdateUserRoleDto[];
    tokenAssets?: AssetDto[];
    googleId?: string;
    avatarUrl?: string;
    version?: number;
}

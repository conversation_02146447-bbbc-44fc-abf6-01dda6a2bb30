"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var BaseCmsCategoriesService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.BaseCmsCategoriesService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const event_emitter_1 = require("@nestjs/event-emitter");
const class_transformer_1 = require("class-transformer");
const cms_categories_entity_1 = require("../entity/cms-categories.entity");
const cms_category_dto_1 = require("../dto/cms-category.dto");
let BaseCmsCategoriesService = BaseCmsCategoriesService_1 = class BaseCmsCategoriesService {
    categoryRepository;
    dataSource;
    eventEmitter;
    logger = new common_1.Logger(BaseCmsCategoriesService_1.name);
    EVENT_CATEGORY_CREATED = 'cms-category.created';
    EVENT_CATEGORY_UPDATED = 'cms-category.updated';
    EVENT_CATEGORY_DELETED = 'cms-category.deleted';
    validRelations = [
        'parent',
        'children',
        'creator',
        'updater',
        'deleter'
    ];
    constructor(categoryRepository, dataSource, eventEmitter) {
        this.categoryRepository = categoryRepository;
        this.dataSource = dataSource;
        this.eventEmitter = eventEmitter;
    }
    validateRelations(relations) {
        if (!relations || !Array.isArray(relations)) {
            return [];
        }
        return relations.filter(relation => {
            const isValid = this.validRelations.includes(relation);
            if (!isValid) {
                this.logger.warn(`Mối quan hệ không hợp lệ: ${relation}`);
            }
            return isValid;
        });
    }
    buildWhereClause(filter) {
        if (!filter) {
            return { isDeleted: false };
        }
        try {
            const filterObj = JSON.parse(filter);
            return {
                ...filterObj,
                isDeleted: false,
            };
        }
        catch (e) {
            return [
                { name: (0, typeorm_2.ILike)(`%${filter}%`), isDeleted: false },
                { description: (0, typeorm_2.ILike)(`%${filter}%`), isDeleted: false },
                { slug: (0, typeorm_2.ILike)(`%${filter}%`), isDeleted: false },
            ];
        }
    }
    async findById(id, relations = [], throwError = true) {
        const validatedRelations = this.validateRelations(relations);
        const category = await this.categoryRepository.findOne({
            where: { id, isDeleted: false },
            relations: validatedRelations,
        });
        if (!category && throwError) {
            throw new common_1.NotFoundException(`Không tìm thấy chuyên mục với ID: ${id}`);
        }
        return category;
    }
    async findBySlug(slug, postType, throwError = true) {
        const whereCondition = { slug, isDeleted: false };
        if (postType) {
            whereCondition.postType = postType;
        }
        const category = await this.categoryRepository.findOne({
            where: whereCondition,
        });
        if (!category && throwError) {
            throw new common_1.NotFoundException(`Không tìm thấy chuyên mục với slug: ${slug}`);
        }
        return category;
    }
    toDto(category) {
        if (!category) {
            return null;
        }
        const plainObj = Object.assign({}, category);
        if (category.parent) {
            plainObj.parentId = category.parent.id;
        }
        return (0, class_transformer_1.plainToInstance)(cms_category_dto_1.CmsCategoryDto, plainObj, {
            excludeExtraneousValues: true,
            enableImplicitConversion: true,
            exposeDefaultValues: true,
            exposeUnsetFields: false,
        });
    }
    toDtos(categories) {
        if (!categories || !Array.isArray(categories)) {
            return [];
        }
        return categories.map(category => this.toDto(category))
            .filter((dto) => dto !== null);
    }
    async isChildOf(childId, parentId) {
        const child = await this.categoryRepository.findOne({
            where: { id: childId },
            relations: ['parent'],
        });
        if (!child || !child.parent) {
            return false;
        }
        if (child.parent.id === parentId) {
            return true;
        }
        return this.isChildOf(child.parent.id, parentId);
    }
    async isDescendantOf(descendantId, ancestorId) {
        return this.isChildOf(descendantId, ancestorId);
    }
};
exports.BaseCmsCategoriesService = BaseCmsCategoriesService;
exports.BaseCmsCategoriesService = BaseCmsCategoriesService = BaseCmsCategoriesService_1 = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(cms_categories_entity_1.CmsCategories)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        typeorm_2.DataSource,
        event_emitter_1.EventEmitter2])
], BaseCmsCategoriesService);
//# sourceMappingURL=base.cms-categories.service.js.map
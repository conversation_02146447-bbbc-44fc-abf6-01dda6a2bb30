import { OnGatewayConnection, OnGatewayDisconnect } from '@nestjs/websockets';
import { Server, Socket } from 'socket.io';
import { TokenCategoryDto } from '../dto/token-category.dto';
export declare class TokenCategoryEventsGateway implements OnGatewayConnection, OnGatewayDisconnect {
    server: Server;
    private readonly logger;
    handleConnection(client: Socket, ...args: any[]): void;
    handleDisconnect(client: Socket): void;
    emitEntityCreated(dto: TokenCategoryDto): void;
    emitEntityUpdated(dto: TokenCategoryDto): void;
    emitStatusToggled(id: string, isActive: boolean): void;
    emitEntityDuplicated(dto: TokenCategoryDto): void;
    emitEntityDeleted(id: string, isSoftDelete: boolean): void;
    handleSubscribe(entityId: string, client: Socket): void;
    handleUnsubscribe(entityId: string, client: Socket): void;
}

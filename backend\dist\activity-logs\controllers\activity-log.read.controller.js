"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var ActivityLogReadController_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.ActivityLogReadController = void 0;
const openapi = require("@nestjs/swagger");
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const jwt_auth_guard_1 = require("../../common/guards/jwt-auth.guard");
const roles_guard_1 = require("../../common/guards/roles.guard");
const roles_decorator_1 = require("../../common/decorators/roles.decorator");
const get_user_decorator_1 = require("../../common/decorators/get-user.decorator");
const default_user_role_enum_1 = require("../../users/enums/default-user-role.enum");
const user_entity_1 = require("../../users/entities/user.entity");
const read_activity_log_service_1 = require("../services/read.activity-log.service");
const activity_log_dto_1 = require("../dto/activity-log.dto");
const custom_pagination_query_dto_1 = require("../../common/dto/custom-pagination-query.dto");
const pagination_response_dto_1 = require("../../common/dto/pagination-response.dto");
const custom_pagination_meta_dto_1 = require("../../common/dto/custom-pagination-meta.dto");
let ActivityLogReadController = ActivityLogReadController_1 = class ActivityLogReadController {
    activityLogService;
    logger = new common_1.Logger(ActivityLogReadController_1.name);
    constructor(activityLogService) {
        this.activityLogService = activityLogService;
    }
    async findMyActivities(user, limit, page, sortBy, sortOrder, module) {
        this.logger.debug(`Đang lấy danh sách lịch sử hoạt động của người dùng hiện tại: ${user.id}`);
        const paginationQuery = new custom_pagination_query_dto_1.CustomPaginationQueryDto();
        const queryParams = {
            limit,
            page
        };
        if (sortBy) {
            queryParams.sort = `${sortBy}:${sortOrder || 'DESC'}`;
        }
        if (module) {
            queryParams.filter = `module:${module}`;
        }
        Object.assign(paginationQuery, queryParams);
        const { data, total } = await this.activityLogService.findByUserId(user.id, paginationQuery);
        const meta = new custom_pagination_meta_dto_1.CustomPageMetaDto({
            pageQueryDto: paginationQuery,
            itemCount: total,
        });
        return new pagination_response_dto_1.PaginationResponseDto(data, meta);
    }
    async findAll(paginationQuery) {
        this.logger.debug(`Đang lấy danh sách lịch sử hoạt động với tham số: ${JSON.stringify(paginationQuery)}`);
        const { data, total } = await this.activityLogService.findAll(paginationQuery);
        const meta = new custom_pagination_meta_dto_1.CustomPageMetaDto({
            pageQueryDto: paginationQuery,
            itemCount: total,
        });
        return new pagination_response_dto_1.PaginationResponseDto(data, meta);
    }
    async search(paginationQuery) {
        const keyword = paginationQuery.search || '';
        this.logger.debug(`Đang tìm kiếm lịch sử hoạt động với từ khóa: ${keyword}`);
        const { data, total } = await this.activityLogService.search(keyword, paginationQuery);
        const meta = new custom_pagination_meta_dto_1.CustomPageMetaDto({
            pageQueryDto: paginationQuery,
            itemCount: total,
        });
        return new pagination_response_dto_1.PaginationResponseDto(data, meta);
    }
    async findOne(id, relations = '') {
        this.logger.debug(`Đang lấy thông tin lịch sử hoạt động với ID: ${id}`);
        const relationsList = relations ? relations.split(',') : [];
        return this.activityLogService.findOne(id, relationsList);
    }
    async findByUserId(userId, paginationQuery) {
        this.logger.debug(`Đang lấy danh sách lịch sử hoạt động của người dùng với ID: ${userId}`);
        const { data, total } = await this.activityLogService.findByUserId(userId, paginationQuery);
        const meta = new custom_pagination_meta_dto_1.CustomPageMetaDto({
            pageQueryDto: paginationQuery,
            itemCount: total,
        });
        return new pagination_response_dto_1.PaginationResponseDto(data, meta);
    }
    async findByAction(action, paginationQuery) {
        this.logger.debug(`Đang lấy danh sách lịch sử hoạt động với hành động: ${action}`);
        const { data, total } = await this.activityLogService.findByAction(action, paginationQuery);
        const meta = new custom_pagination_meta_dto_1.CustomPageMetaDto({
            pageQueryDto: paginationQuery,
            itemCount: total,
        });
        return new pagination_response_dto_1.PaginationResponseDto(data, meta);
    }
    async findByModule(module, paginationQuery) {
        this.logger.debug(`Đang lấy danh sách lịch sử hoạt động với module: ${module}`);
        const { data, total } = await this.activityLogService.findByModule(module, paginationQuery);
        const meta = new custom_pagination_meta_dto_1.CustomPageMetaDto({
            pageQueryDto: paginationQuery,
            itemCount: total,
        });
        return new pagination_response_dto_1.PaginationResponseDto(data, meta);
    }
    async count(filter) {
        this.logger.debug(`Đang đếm số lượng lịch sử hoạt động với bộ lọc: ${filter}`);
        const count = await this.activityLogService.count(filter);
        return { count };
    }
};
exports.ActivityLogReadController = ActivityLogReadController;
__decorate([
    (0, common_1.Get)('my-activities'),
    (0, swagger_1.ApiOperation)({ summary: 'Lấy danh sách lịch sử hoạt động của người dùng hiện tại' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Danh sách lịch sử hoạt động của người dùng hiện tại',
        type: pagination_response_dto_1.PaginationResponseDto,
    }),
    openapi.ApiResponse({ status: 200 }),
    __param(0, (0, get_user_decorator_1.GetUser)()),
    __param(1, (0, common_1.Query)('limit', new common_1.DefaultValuePipe(10), common_1.ParseIntPipe)),
    __param(2, (0, common_1.Query)('page', new common_1.DefaultValuePipe(1), common_1.ParseIntPipe)),
    __param(3, (0, common_1.Query)('sortBy')),
    __param(4, (0, common_1.Query)('sortOrder')),
    __param(5, (0, common_1.Query)('module')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [user_entity_1.User, Number, Number, String, String, String]),
    __metadata("design:returntype", Promise)
], ActivityLogReadController.prototype, "findMyActivities", null);
__decorate([
    (0, common_1.Get)(),
    (0, roles_decorator_1.Roles)(default_user_role_enum_1.PrimaryUserRole.ADMIN),
    (0, swagger_1.ApiOperation)({ summary: 'Lấy danh sách lịch sử hoạt động' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Danh sách lịch sử hoạt động',
        type: pagination_response_dto_1.PaginationResponseDto,
    }),
    openapi.ApiResponse({ status: 200 }),
    __param(0, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [custom_pagination_query_dto_1.CustomPaginationQueryDto]),
    __metadata("design:returntype", Promise)
], ActivityLogReadController.prototype, "findAll", null);
__decorate([
    (0, common_1.Get)('search'),
    (0, roles_decorator_1.Roles)(default_user_role_enum_1.PrimaryUserRole.ADMIN),
    (0, swagger_1.ApiOperation)({
        summary: 'Tìm kiếm lịch sử hoạt động',
        description: 'Tìm kiếm lịch sử hoạt động theo từ khóa. Sử dụng tham số search để cung cấp từ khóa tìm kiếm.'
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Danh sách lịch sử hoạt động phù hợp với từ khóa tìm kiếm',
        type: pagination_response_dto_1.PaginationResponseDto,
    }),
    openapi.ApiResponse({ status: 200 }),
    __param(0, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [custom_pagination_query_dto_1.CustomPaginationQueryDto]),
    __metadata("design:returntype", Promise)
], ActivityLogReadController.prototype, "search", null);
__decorate([
    (0, common_1.Get)(':id'),
    (0, roles_decorator_1.Roles)(default_user_role_enum_1.PrimaryUserRole.ADMIN),
    (0, swagger_1.ApiOperation)({ summary: 'Lấy thông tin lịch sử hoạt động theo ID' }),
    (0, swagger_1.ApiParam)({
        name: 'id',
        description: 'ID của lịch sử hoạt động cần lấy thông tin',
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Thông tin lịch sử hoạt động',
        type: activity_log_dto_1.ActivityLogDto,
    }),
    (0, swagger_1.ApiResponse)({
        status: 404,
        description: 'Không tìm thấy lịch sử hoạt động',
    }),
    openapi.ApiResponse({ status: 200, type: require("../dto/activity-log.dto").ActivityLogDto }),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __param(1, (0, common_1.Query)('relations')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String]),
    __metadata("design:returntype", Promise)
], ActivityLogReadController.prototype, "findOne", null);
__decorate([
    (0, common_1.Get)('by-user/:userId'),
    (0, roles_decorator_1.Roles)(default_user_role_enum_1.PrimaryUserRole.ADMIN),
    (0, swagger_1.ApiOperation)({ summary: 'Lấy danh sách lịch sử hoạt động của người dùng' }),
    (0, swagger_1.ApiParam)({
        name: 'userId',
        description: 'ID của người dùng cần lấy lịch sử hoạt động',
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Danh sách lịch sử hoạt động của người dùng',
        type: pagination_response_dto_1.PaginationResponseDto,
    }),
    openapi.ApiResponse({ status: 200 }),
    __param(0, (0, common_1.Param)('userId', common_1.ParseUUIDPipe)),
    __param(1, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, custom_pagination_query_dto_1.CustomPaginationQueryDto]),
    __metadata("design:returntype", Promise)
], ActivityLogReadController.prototype, "findByUserId", null);
__decorate([
    (0, common_1.Get)('by-action/:action'),
    (0, roles_decorator_1.Roles)(default_user_role_enum_1.PrimaryUserRole.ADMIN),
    (0, swagger_1.ApiOperation)({ summary: 'Lấy danh sách lịch sử hoạt động theo hành động' }),
    (0, swagger_1.ApiParam)({
        name: 'action',
        description: 'Hành động cần lấy lịch sử hoạt động',
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Danh sách lịch sử hoạt động theo hành động',
        type: pagination_response_dto_1.PaginationResponseDto,
    }),
    openapi.ApiResponse({ status: 200 }),
    __param(0, (0, common_1.Param)('action')),
    __param(1, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, custom_pagination_query_dto_1.CustomPaginationQueryDto]),
    __metadata("design:returntype", Promise)
], ActivityLogReadController.prototype, "findByAction", null);
__decorate([
    (0, common_1.Get)('by-module/:module'),
    (0, roles_decorator_1.Roles)(default_user_role_enum_1.PrimaryUserRole.ADMIN),
    (0, swagger_1.ApiOperation)({ summary: 'Lấy danh sách lịch sử hoạt động theo module' }),
    (0, swagger_1.ApiParam)({
        name: 'module',
        description: 'Module cần lấy lịch sử hoạt động',
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Danh sách lịch sử hoạt động theo module',
        type: pagination_response_dto_1.PaginationResponseDto,
    }),
    openapi.ApiResponse({ status: 200 }),
    __param(0, (0, common_1.Param)('module')),
    __param(1, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, custom_pagination_query_dto_1.CustomPaginationQueryDto]),
    __metadata("design:returntype", Promise)
], ActivityLogReadController.prototype, "findByModule", null);
__decorate([
    (0, common_1.Get)('count'),
    (0, roles_decorator_1.Roles)(default_user_role_enum_1.PrimaryUserRole.ADMIN),
    (0, swagger_1.ApiOperation)({ summary: 'Đếm số lượng lịch sử hoạt động' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Số lượng lịch sử hoạt động',
        type: Number,
    }),
    openapi.ApiResponse({ status: 200 }),
    __param(0, (0, common_1.Query)('filter')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], ActivityLogReadController.prototype, "count", null);
exports.ActivityLogReadController = ActivityLogReadController = ActivityLogReadController_1 = __decorate([
    (0, swagger_1.ApiTags)('activity-logs'),
    (0, swagger_1.ApiBearerAuth)(),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard, roles_guard_1.RolesGuard),
    (0, common_1.Controller)('activity-logs'),
    __metadata("design:paramtypes", [read_activity_log_service_1.ReadActivityLogService])
], ActivityLogReadController);
//# sourceMappingURL=activity-log.read.controller.js.map
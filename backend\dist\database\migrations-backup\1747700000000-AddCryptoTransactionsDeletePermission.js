"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AddCryptoTransactionsDeletePermission1747700000000 = void 0;
class AddCryptoTransactionsDeletePermission1747700000000 {
    async up(queryRunner) {
        await queryRunner.query(`
      INSERT INTO permissions (id, name, description, display_name, created_at, updated_at)
      VALUES (
        uuid_generate_v4(),
        'crypto-transactions:delete',
        'Cho phép xóa giao dịch điểm thưởng',
        'Xóa giao dịch điểm thưởng',
        NOW(),
        NOW()
      )
      ON CONFLICT (name) DO NOTHING;
    `);
        await queryRunner.query(`
      INSERT INTO role_permissions (id, role_id, permission_id, created_at, updated_at)
      SELECT 
        uuid_generate_v4(),
        r.id,
        p.id,
        NOW(),
        NOW()
      FROM roles r
      CROSS JOIN permissions p
      WHERE r.name = 'ADMIN'
      AND p.name = 'crypto-transactions:delete'
      ON CONFLICT (role_id, permission_id) DO NOTHING;
    `);
    }
    async down(queryRunner) {
        await queryRunner.query(`
      DELETE FROM role_permissions
      WHERE permission_id IN (
        SELECT id FROM permissions WHERE name = 'crypto-transactions:delete'
      )
      AND role_id IN (
        SELECT id FROM roles WHERE name = 'ADMIN'
      );
    `);
        await queryRunner.query(`
      DELETE FROM permissions
      WHERE name = 'crypto-transactions:delete';
    `);
    }
}
exports.AddCryptoTransactionsDeletePermission1747700000000 = AddCryptoTransactionsDeletePermission1747700000000;
//# sourceMappingURL=1747700000000-AddCryptoTransactionsDeletePermission.js.map
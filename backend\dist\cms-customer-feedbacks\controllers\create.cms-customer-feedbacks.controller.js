"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreateCmsCustomerFeedbacksController = void 0;
const openapi = require("@nestjs/swagger");
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const jwt_auth_guard_1 = require("../../common/guards/jwt-auth.guard");
const create_cms_customer_feedbacks_service_1 = require("../services/create.cms-customer-feedbacks.service");
const create_cms_customer_feedback_dto_1 = require("../dto/create.cms-customer-feedback.dto");
const api_response_dto_1 = require("../../common/dto/api-response.dto");
const get_user_decorator_1 = require("../../common/decorators/get-user.decorator");
const roles_decorator_1 = require("../../common/decorators/roles.decorator");
const permissions_decorator_1 = require("../../common/decorators/permissions.decorator");
let CreateCmsCustomerFeedbacksController = class CreateCmsCustomerFeedbacksController {
    cmsCustomerFeedbacksService;
    constructor(cmsCustomerFeedbacksService) {
        this.cmsCustomerFeedbacksService = cmsCustomerFeedbacksService;
    }
    async create(createCmsCustomerFeedbackDto, userId) {
        return this.cmsCustomerFeedbacksService.create(createCmsCustomerFeedbackDto, userId);
    }
    async bulkCreate(createCmsCustomerFeedbackDtos, userId) {
        return this.cmsCustomerFeedbacksService.bulkCreate(createCmsCustomerFeedbackDtos, userId);
    }
    async createFromPublicForm(createCmsCustomerFeedbackDto) {
        return this.cmsCustomerFeedbacksService.createFromPublicForm(createCmsCustomerFeedbackDto);
    }
    async createWithRating(customerName, feedbackText, rating, productServiceName, userId) {
        return this.cmsCustomerFeedbacksService.createWithRating(customerName, feedbackText, rating, productServiceName, userId);
    }
    async createTestimonial(createCmsCustomerFeedbackDto, userId) {
        return this.cmsCustomerFeedbacksService.createTestimonial(createCmsCustomerFeedbackDto, userId);
    }
    async importFeedbacks(feedbackData, userId) {
        return this.cmsCustomerFeedbacksService.importFeedbacks(feedbackData, userId);
    }
};
exports.CreateCmsCustomerFeedbacksController = CreateCmsCustomerFeedbacksController;
__decorate([
    (0, common_1.Post)(),
    (0, roles_decorator_1.Roles)('ADMIN', 'EDITOR'),
    (0, permissions_decorator_1.Permissions)('cms-customer-feedback:create'),
    (0, swagger_1.ApiOperation)({ summary: 'Tạo mới feedback khách hàng CMS' }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.CREATED,
        description: 'Feedback khách hàng CMS đã được tạo thành công.',
        type: api_response_dto_1.ApiResponseDto,
        schema: {
            properties: {
                data: { $ref: '#/components/schemas/CmsCustomerFeedbackDto' },
            },
        },
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.BAD_REQUEST,
        description: 'Dữ liệu đầu vào không hợp lệ.',
        type: api_response_dto_1.ApiResponseDto,
        schema: { properties: { data: { type: 'object', nullable: true } } },
    }),
    (0, swagger_1.ApiBody)({ type: create_cms_customer_feedback_dto_1.CreateCmsCustomerFeedbackDto }),
    openapi.ApiResponse({ status: 201, type: require("../dto/cms-customer-feedback.dto").CmsCustomerFeedbackDto }),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, get_user_decorator_1.GetUser)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [create_cms_customer_feedback_dto_1.CreateCmsCustomerFeedbackDto, String]),
    __metadata("design:returntype", Promise)
], CreateCmsCustomerFeedbacksController.prototype, "create", null);
__decorate([
    (0, common_1.Post)('bulk'),
    (0, roles_decorator_1.Roles)('ADMIN'),
    (0, permissions_decorator_1.Permissions)('cms-customer-feedback:create'),
    (0, swagger_1.ApiOperation)({ summary: 'Tạo nhiều feedback khách hàng CMS cùng lúc' }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.CREATED,
        description: 'Các feedback khách hàng CMS đã được tạo thành công.',
        type: api_response_dto_1.ApiResponseDto,
        schema: {
            properties: {
                data: {
                    type: 'array',
                    items: { $ref: '#/components/schemas/CmsCustomerFeedbackDto' },
                },
            },
        },
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.BAD_REQUEST,
        description: 'Dữ liệu đầu vào không hợp lệ.',
        type: api_response_dto_1.ApiResponseDto,
        schema: { properties: { data: { type: 'object', nullable: true } } },
    }),
    (0, swagger_1.ApiBody)({ type: [create_cms_customer_feedback_dto_1.CreateCmsCustomerFeedbackDto] }),
    openapi.ApiResponse({ status: 201, type: [require("../dto/cms-customer-feedback.dto").CmsCustomerFeedbackDto] }),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, get_user_decorator_1.GetUser)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Array, String]),
    __metadata("design:returntype", Promise)
], CreateCmsCustomerFeedbacksController.prototype, "bulkCreate", null);
__decorate([
    (0, common_1.Post)('public'),
    (0, swagger_1.ApiOperation)({ summary: 'Tạo feedback từ form công khai (không cần đăng nhập)' }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.CREATED,
        description: 'Feedback đã được gửi thành công.',
        type: api_response_dto_1.ApiResponseDto,
        schema: {
            properties: { data: { $ref: '#/components/schemas/CmsCustomerFeedbackDto' } },
        },
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.BAD_REQUEST,
        description: 'Dữ liệu đầu vào không hợp lệ.',
        type: api_response_dto_1.ApiResponseDto,
        schema: { properties: { data: { type: 'object', nullable: true } } },
    }),
    (0, swagger_1.ApiBody)({ type: create_cms_customer_feedback_dto_1.CreateCmsCustomerFeedbackDto }),
    openapi.ApiResponse({ status: 201, type: require("../dto/cms-customer-feedback.dto").CmsCustomerFeedbackDto }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [create_cms_customer_feedback_dto_1.CreateCmsCustomerFeedbackDto]),
    __metadata("design:returntype", Promise)
], CreateCmsCustomerFeedbacksController.prototype, "createFromPublicForm", null);
__decorate([
    (0, common_1.Post)('with-rating'),
    (0, roles_decorator_1.Roles)('ADMIN', 'EDITOR'),
    (0, permissions_decorator_1.Permissions)('cms-customer-feedback:create'),
    (0, swagger_1.ApiOperation)({ summary: 'Tạo feedback với rating cụ thể' }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.CREATED,
        description: 'Feedback với rating đã được tạo thành công.',
        type: api_response_dto_1.ApiResponseDto,
        schema: {
            properties: { data: { $ref: '#/components/schemas/CmsCustomerFeedbackDto' } },
        },
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.BAD_REQUEST,
        description: 'Rating không hợp lệ (phải từ 1-5).',
        type: api_response_dto_1.ApiResponseDto,
        schema: { properties: { data: { type: 'object', nullable: true } } },
    }),
    (0, swagger_1.ApiBody)({
        schema: {
            type: 'object',
            properties: {
                customerName: {
                    type: 'string',
                    description: 'Tên khách hàng',
                    example: 'Nguyễn Văn A',
                },
                feedbackText: {
                    type: 'string',
                    description: 'Nội dung feedback',
                    example: 'Dịch vụ rất tốt, tôi rất hài lòng.',
                },
                rating: {
                    type: 'number',
                    description: 'Đánh giá sao (1-5)',
                    example: 5,
                    minimum: 1,
                    maximum: 5,
                },
                productServiceName: {
                    type: 'string',
                    description: 'Tên sản phẩm/dịch vụ',
                    example: 'Dịch vụ mua bán vàng',
                },
            },
            required: ['customerName', 'feedbackText', 'rating'],
        },
    }),
    openapi.ApiResponse({ status: 201, type: require("../dto/cms-customer-feedback.dto").CmsCustomerFeedbackDto }),
    __param(0, (0, common_1.Body)('customerName')),
    __param(1, (0, common_1.Body)('feedbackText')),
    __param(2, (0, common_1.Body)('rating')),
    __param(3, (0, common_1.Body)('productServiceName')),
    __param(4, (0, get_user_decorator_1.GetUser)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String, Number, String, String]),
    __metadata("design:returntype", Promise)
], CreateCmsCustomerFeedbacksController.prototype, "createWithRating", null);
__decorate([
    (0, common_1.Post)('testimonial'),
    (0, roles_decorator_1.Roles)('ADMIN', 'EDITOR'),
    (0, permissions_decorator_1.Permissions)('cms-customer-feedback:create'),
    (0, swagger_1.ApiOperation)({ summary: 'Tạo testimonial (feedback đã được duyệt sẵn)' }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.CREATED,
        description: 'Testimonial đã được tạo thành công.',
        type: api_response_dto_1.ApiResponseDto,
        schema: {
            properties: { data: { $ref: '#/components/schemas/CmsCustomerFeedbackDto' } },
        },
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.BAD_REQUEST,
        description: 'Dữ liệu đầu vào không hợp lệ.',
        type: api_response_dto_1.ApiResponseDto,
        schema: { properties: { data: { type: 'object', nullable: true } } },
    }),
    (0, swagger_1.ApiBody)({ type: create_cms_customer_feedback_dto_1.CreateCmsCustomerFeedbackDto }),
    openapi.ApiResponse({ status: 201, type: require("../dto/cms-customer-feedback.dto").CmsCustomerFeedbackDto }),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, get_user_decorator_1.GetUser)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [create_cms_customer_feedback_dto_1.CreateCmsCustomerFeedbackDto, String]),
    __metadata("design:returntype", Promise)
], CreateCmsCustomerFeedbacksController.prototype, "createTestimonial", null);
__decorate([
    (0, common_1.Post)('import'),
    (0, roles_decorator_1.Roles)('ADMIN'),
    (0, permissions_decorator_1.Permissions)('cms-customer-feedback:create'),
    (0, swagger_1.ApiOperation)({ summary: 'Import feedback từ dữ liệu bên ngoài' }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.CREATED,
        description: 'Feedback đã được import thành công.',
        type: api_response_dto_1.ApiResponseDto,
        schema: {
            properties: {
                data: {
                    type: 'array',
                    items: { $ref: '#/components/schemas/CmsCustomerFeedbackDto' },
                },
            },
        },
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.BAD_REQUEST,
        description: 'Dữ liệu import không hợp lệ.',
        type: api_response_dto_1.ApiResponseDto,
        schema: { properties: { data: { type: 'object', nullable: true } } },
    }),
    (0, swagger_1.ApiBody)({
        schema: {
            type: 'array',
            items: {
                type: 'object',
                properties: {
                    customerName: { type: 'string' },
                    customerTitleCompany: { type: 'string' },
                    feedbackText: { type: 'string' },
                    rating: { type: 'number' },
                    avatarUrl: { type: 'string' },
                    productServiceName: { type: 'string' },
                    status: { type: 'string' },
                },
                required: ['customerName', 'feedbackText'],
            },
        },
    }),
    openapi.ApiResponse({ status: 201, type: [require("../dto/cms-customer-feedback.dto").CmsCustomerFeedbackDto] }),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, get_user_decorator_1.GetUser)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Array, String]),
    __metadata("design:returntype", Promise)
], CreateCmsCustomerFeedbacksController.prototype, "importFeedbacks", null);
exports.CreateCmsCustomerFeedbacksController = CreateCmsCustomerFeedbacksController = __decorate([
    (0, swagger_1.ApiTags)('cms-customer-feedbacks'),
    (0, swagger_1.ApiBearerAuth)(),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, common_1.Controller)('cms/customer-feedbacks'),
    __metadata("design:paramtypes", [create_cms_customer_feedbacks_service_1.CreateCmsCustomerFeedbacksService])
], CreateCmsCustomerFeedbacksController);
//# sourceMappingURL=create.cms-customer-feedbacks.controller.js.map
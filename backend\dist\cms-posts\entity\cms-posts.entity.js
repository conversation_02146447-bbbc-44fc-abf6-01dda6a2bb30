"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CmsPosts = exports.CmsPostStatus = exports.CmsPostType = void 0;
const openapi = require("@nestjs/swagger");
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
const typeorm_1 = require("typeorm");
const base_entity_1 = require("../../common/entities/base.entity");
const cms_categories_entity_1 = require("../../cms-categories/entity/cms-categories.entity");
const user_entity_1 = require("../../users/entities/user.entity");
var CmsPostType;
(function (CmsPostType) {
    CmsPostType["POST"] = "post";
    CmsPostType["NEWS"] = "news";
    CmsPostType["PRESS_RELEASE"] = "press_release";
    CmsPostType["EVENT"] = "event";
    CmsPostType["KNOWLEDGE_BASE"] = "knowledge_base";
})(CmsPostType || (exports.CmsPostType = CmsPostType = {}));
var CmsPostStatus;
(function (CmsPostStatus) {
    CmsPostStatus["DRAFT"] = "draft";
    CmsPostStatus["PUBLISHED"] = "published";
})(CmsPostStatus || (exports.CmsPostStatus = CmsPostStatus = {}));
let CmsPosts = class CmsPosts extends base_entity_1.BaseEntity {
    postType;
    title;
    slug;
    excerpt;
    content;
    featuredImageUrl;
    status;
    publishedAt;
    eventStartDate;
    eventEndDate;
    eventLocation;
    viewCount;
    metaTitle;
    metaDescription;
    metaKeywords;
    allowComments;
    isFeatured;
    category;
    author;
    tags;
    getEntityName() {
        return 'cms_posts';
    }
    static _OPENAPI_METADATA_FACTORY() {
        return { postType: { required: true, enum: require("./cms-posts.entity").CmsPostType }, title: { required: true, type: () => String, maxLength: 255 }, slug: { required: true, type: () => String, maxLength: 255 }, excerpt: { required: false, type: () => String }, content: { required: true, type: () => String }, featuredImageUrl: { required: false, type: () => String }, status: { required: true, enum: require("./cms-posts.entity").CmsPostStatus }, publishedAt: { required: false, type: () => Date, nullable: true }, eventStartDate: { required: false, type: () => Date, nullable: true }, eventEndDate: { required: false, type: () => Date, nullable: true }, eventLocation: { required: false, type: () => String, maxLength: 255 }, viewCount: { required: true, type: () => Number, minimum: 0 }, metaTitle: { required: false, type: () => String, maxLength: 255 }, metaDescription: { required: false, type: () => String }, metaKeywords: { required: false, type: () => String, maxLength: 255 }, allowComments: { required: true, type: () => Boolean }, isFeatured: { required: true, type: () => Boolean }, category: { required: false, type: () => require("../../cms-categories/entity/cms-categories.entity").CmsCategories, nullable: true }, author: { required: true, type: () => require("../../users/entities/user.entity").User }, tags: { required: false, type: () => [Object] } };
    }
};
exports.CmsPosts = CmsPosts;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Loại bài viết',
        example: CmsPostType.POST,
        enum: CmsPostType,
    }),
    (0, class_validator_1.IsEnum)(CmsPostType, { message: 'Loại bài viết không hợp lệ' }),
    (0, typeorm_1.Column)({
        type: 'varchar',
        length: 50,
        nullable: false,
        default: CmsPostType.POST,
        name: 'post_type'
    }),
    __metadata("design:type", String)
], CmsPosts.prototype, "postType", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Tiêu đề bài viết',
        example: 'Tin tức mới nhất về thị trường vàng',
    }),
    (0, class_validator_1.IsNotEmpty)({ message: 'Tiêu đề không được để trống' }),
    (0, class_validator_1.IsString)({ message: 'Tiêu đề phải là chuỗi' }),
    (0, class_validator_1.MaxLength)(255, { message: 'Tiêu đề không được vượt quá 255 ký tự' }),
    (0, typeorm_1.Column)({ type: 'varchar', length: 255, nullable: false, name: 'title' }),
    __metadata("design:type", String)
], CmsPosts.prototype, "title", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Chuỗi URL thân thiện',
        example: 'tin-tuc-moi-nhat-ve-thi-truong-vang',
    }),
    (0, class_validator_1.IsString)({ message: 'Slug phải là chuỗi' }),
    (0, class_validator_1.MaxLength)(255, { message: 'Slug không được vượt quá 255 ký tự' }),
    (0, typeorm_1.Column)({ type: 'varchar', length: 255, nullable: false, name: 'slug' }),
    __metadata("design:type", String)
], CmsPosts.prototype, "slug", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Đoạn tóm tắt ngắn',
        example: 'Thị trường vàng tuần này có nhiều biến động...',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)({ message: 'Tóm tắt phải là chuỗi' }),
    (0, typeorm_1.Column)({ type: 'text', nullable: true, name: 'excerpt' }),
    __metadata("design:type", String)
], CmsPosts.prototype, "excerpt", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Nội dung đầy đủ (HTML)',
        example: '<p>Nội dung chi tiết về thị trường vàng...</p>',
    }),
    (0, class_validator_1.IsNotEmpty)({ message: 'Nội dung không được để trống' }),
    (0, class_validator_1.IsString)({ message: 'Nội dung phải là chuỗi' }),
    (0, typeorm_1.Column)({ type: 'text', nullable: false, name: 'content' }),
    __metadata("design:type", String)
], CmsPosts.prototype, "content", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'URL hình ảnh đại diện',
        example: 'https://example.com/images/featured.jpg',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)({ message: 'URL hình ảnh phải là chuỗi' }),
    (0, typeorm_1.Column)({ type: 'varchar', nullable: true, name: 'featured_image_url' }),
    __metadata("design:type", String)
], CmsPosts.prototype, "featuredImageUrl", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Trạng thái bài viết',
        example: CmsPostStatus.DRAFT,
        enum: CmsPostStatus,
    }),
    (0, class_validator_1.IsEnum)(CmsPostStatus, { message: 'Trạng thái không hợp lệ' }),
    (0, typeorm_1.Column)({
        type: 'varchar',
        length: 50,
        nullable: false,
        default: CmsPostStatus.DRAFT,
        name: 'status'
    }),
    __metadata("design:type", String)
], CmsPosts.prototype, "status", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Thời gian xuất bản',
        example: '2023-01-01T00:00:00.000Z',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsDateString)({}, { message: 'Thời gian xuất bản không hợp lệ' }),
    (0, typeorm_1.Column)({ type: 'timestamp', nullable: true, name: 'published_at' }),
    __metadata("design:type", Object)
], CmsPosts.prototype, "publishedAt", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Ngày bắt đầu sự kiện (cho post_type = event)',
        example: '2023-01-01T09:00:00.000Z',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsDateString)({}, { message: 'Ngày bắt đầu sự kiện không hợp lệ' }),
    (0, typeorm_1.Column)({ type: 'timestamp', nullable: true, name: 'event_start_date' }),
    __metadata("design:type", Object)
], CmsPosts.prototype, "eventStartDate", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Ngày kết thúc sự kiện (cho post_type = event)',
        example: '2023-01-01T17:00:00.000Z',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsDateString)({}, { message: 'Ngày kết thúc sự kiện không hợp lệ' }),
    (0, typeorm_1.Column)({ type: 'timestamp', nullable: true, name: 'event_end_date' }),
    __metadata("design:type", Object)
], CmsPosts.prototype, "eventEndDate", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Địa điểm sự kiện (cho post_type = event)',
        example: 'Trung tâm Hội nghị Quốc gia',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)({ message: 'Địa điểm sự kiện phải là chuỗi' }),
    (0, class_validator_1.MaxLength)(255, { message: 'Địa điểm sự kiện không được vượt quá 255 ký tự' }),
    (0, typeorm_1.Column)({ type: 'varchar', length: 255, nullable: true, name: 'event_location' }),
    __metadata("design:type", String)
], CmsPosts.prototype, "eventLocation", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Số lượt xem',
        example: 100,
    }),
    (0, class_validator_1.IsInt)({ message: 'Số lượt xem phải là số nguyên' }),
    (0, class_validator_1.Min)(0, { message: 'Số lượt xem không được âm' }),
    (0, typeorm_1.Column)({ type: 'int', nullable: false, default: 0, name: 'view_count' }),
    __metadata("design:type", Number)
], CmsPosts.prototype, "viewCount", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Tiêu đề SEO',
        example: 'Tin tức thị trường vàng - Cập nhật mới nhất',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)({ message: 'Tiêu đề SEO phải là chuỗi' }),
    (0, class_validator_1.MaxLength)(255, { message: 'Tiêu đề SEO không được vượt quá 255 ký tự' }),
    (0, typeorm_1.Column)({ type: 'varchar', length: 255, nullable: true, name: 'meta_title' }),
    __metadata("design:type", String)
], CmsPosts.prototype, "metaTitle", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Mô tả SEO',
        example: 'Theo dõi tin tức và cập nhật mới nhất về thị trường vàng',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)({ message: 'Mô tả SEO phải là chuỗi' }),
    (0, typeorm_1.Column)({ type: 'text', nullable: true, name: 'meta_description' }),
    __metadata("design:type", String)
], CmsPosts.prototype, "metaDescription", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Từ khóa SEO',
        example: 'thị trường vàng, tin tức, giá vàng',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)({ message: 'Từ khóa SEO phải là chuỗi' }),
    (0, class_validator_1.MaxLength)(255, { message: 'Từ khóa SEO không được vượt quá 255 ký tự' }),
    (0, typeorm_1.Column)({ type: 'varchar', length: 255, nullable: true, name: 'meta_keywords' }),
    __metadata("design:type", String)
], CmsPosts.prototype, "metaKeywords", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Cho phép bình luận',
        example: true,
    }),
    (0, class_validator_1.IsBoolean)({ message: 'Cho phép bình luận phải là boolean' }),
    (0, typeorm_1.Column)({ type: 'boolean', nullable: false, default: true, name: 'allow_comments' }),
    __metadata("design:type", Boolean)
], CmsPosts.prototype, "allowComments", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Bài viết nổi bật',
        example: false,
    }),
    (0, class_validator_1.IsBoolean)({ message: 'Bài viết nổi bật phải là boolean' }),
    (0, typeorm_1.Column)({ type: 'boolean', nullable: false, default: false, name: 'is_featured' }),
    __metadata("design:type", Boolean)
], CmsPosts.prototype, "isFeatured", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Chuyên mục',
        example: '550e8400-e29b-41d4-a716-446655440000',
    }),
    (0, typeorm_1.ManyToOne)(() => cms_categories_entity_1.CmsCategories, { nullable: true }),
    (0, typeorm_1.JoinColumn)({ name: 'category_id' }),
    __metadata("design:type", Object)
], CmsPosts.prototype, "category", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Tác giả',
        example: '550e8400-e29b-41d4-a716-446655440000',
    }),
    (0, typeorm_1.ManyToOne)(() => user_entity_1.User, { nullable: false }),
    (0, typeorm_1.JoinColumn)({ name: 'author_id' }),
    __metadata("design:type", user_entity_1.User)
], CmsPosts.prototype, "author", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Danh sách thẻ của bài viết',
        type: () => [Object],
    }),
    (0, typeorm_1.ManyToMany)('CmsTags', 'posts'),
    (0, typeorm_1.JoinTable)({
        name: 'cms_post_tags',
        joinColumn: { name: 'post_id', referencedColumnName: 'id' },
        inverseJoinColumn: { name: 'tag_id', referencedColumnName: 'id' },
    }),
    __metadata("design:type", Array)
], CmsPosts.prototype, "tags", void 0);
exports.CmsPosts = CmsPosts = __decorate([
    (0, typeorm_1.Entity)('cms_posts'),
    (0, typeorm_1.Index)(['slug', 'postType'], { unique: true }),
    (0, typeorm_1.Index)(['title', 'postType']),
    (0, typeorm_1.Index)(['status', 'postType']),
    (0, typeorm_1.Index)(['publishedAt'])
], CmsPosts);
//# sourceMappingURL=cms-posts.entity.js.map
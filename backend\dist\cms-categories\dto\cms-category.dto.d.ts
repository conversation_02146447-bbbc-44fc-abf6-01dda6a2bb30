import { UserDto } from '../../users/dto/user.dto';
import { CmsCategoryStatus, CmsCategoryPostType } from '../entity/cms-categories.entity';
export declare class CmsCategoryDto {
    id: string;
    businessCode: string;
    name: string;
    slug: string;
    description?: string;
    postType: CmsCategoryPostType;
    imageUrl?: string;
    metaTitle?: string;
    metaDescription?: string;
    metaKeywords?: string;
    status: CmsCategoryStatus;
    parentId?: string;
    parent?: CmsCategoryDto;
    children?: CmsCategoryDto[];
    createdAt: Date;
    updatedAt: Date;
    createdBy?: string;
    updatedBy?: string;
    deletedBy?: string;
    isDeleted: boolean;
    deletedAt?: Date;
    creator?: UserDto;
    updater?: UserDto;
    deleter?: UserDto;
}

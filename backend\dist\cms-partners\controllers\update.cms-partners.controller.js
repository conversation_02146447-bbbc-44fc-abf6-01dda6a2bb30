"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var UpdateCmsPartnersController_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.UpdateCmsPartnersController = void 0;
const openapi = require("@nestjs/swagger");
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const roles_decorator_1 = require("../../common/decorators/roles.decorator");
const jwt_auth_guard_1 = require("../../common/guards/jwt-auth.guard");
const dto_1 = require("../dto");
const update_cms_partners_service_1 = require("../services/update.cms-partners.service");
let UpdateCmsPartnersController = UpdateCmsPartnersController_1 = class UpdateCmsPartnersController {
    updateCmsPartnersService;
    logger = new common_1.Logger(UpdateCmsPartnersController_1.name);
    constructor(updateCmsPartnersService) {
        this.updateCmsPartnersService = updateCmsPartnersService;
    }
    async update(id, updateCmsPartnerDto, req) {
        this.logger.log(`Updating CMS Partner: ${id}`);
        return this.updateCmsPartnersService.update(id, updateCmsPartnerDto, req.user.id);
    }
    async bulkUpdate(bulkUpdateDto, req) {
        this.logger.log(`Bulk updating ${bulkUpdateDto.ids.length} CMS Partners`);
        return this.updateCmsPartnersService.bulkUpdate(bulkUpdateDto, req.user.id);
    }
    async activate(id, req) {
        this.logger.log(`Activating CMS Partner: ${id}`);
        return this.updateCmsPartnersService.activate(id, req.user.id);
    }
    async deactivate(id, req) {
        this.logger.log(`Deactivating CMS Partner: ${id}`);
        return this.updateCmsPartnersService.deactivate(id, req.user.id);
    }
    async updateStatus(id, updateStatusDto, req) {
        this.logger.log(`Updating CMS Partner status: ${id} to ${updateStatusDto.status}`);
        return this.updateCmsPartnersService.updateStatus(id, updateStatusDto, req.user.id);
    }
    async updateDisplayOrder(id, updateDisplayOrderDto, req) {
        this.logger.log(`Updating CMS Partner display order: ${id} to ${updateDisplayOrderDto.displayOrder}`);
        return this.updateCmsPartnersService.updateDisplayOrder(id, updateDisplayOrderDto, req.user.id);
    }
    async updateType(id, updateTypeDto, req) {
        this.logger.log(`Updating CMS Partner type: ${id} to ${updateTypeDto.type}`);
        return this.updateCmsPartnersService.updateType(id, updateTypeDto, req.user.id);
    }
    async bulkActivate(bulkUpdateStatusDto, req) {
        this.logger.log(`Bulk activating ${bulkUpdateStatusDto.ids.length} CMS Partners`);
        return this.updateCmsPartnersService.bulkActivate(bulkUpdateStatusDto.ids, req.user.id);
    }
    async bulkDeactivate(bulkUpdateStatusDto, req) {
        this.logger.log(`Bulk deactivating ${bulkUpdateStatusDto.ids.length} CMS Partners`);
        return this.updateCmsPartnersService.bulkDeactivate(bulkUpdateStatusDto.ids, req.user.id);
    }
    async bulkUpdateStatus(bulkUpdateStatusDto, req) {
        this.logger.log(`Bulk updating status for ${bulkUpdateStatusDto.ids.length} CMS Partners to ${bulkUpdateStatusDto.status}`);
        return this.updateCmsPartnersService.bulkUpdateStatus(bulkUpdateStatusDto, req.user.id);
    }
    async restore(id, req) {
        this.logger.log(`Restoring CMS Partner: ${id}`);
        return this.updateCmsPartnersService.restore(id, req.user.id);
    }
};
exports.UpdateCmsPartnersController = UpdateCmsPartnersController;
__decorate([
    (0, common_1.Patch)(':id'),
    (0, roles_decorator_1.Roles)('ADMIN', 'EDITOR'),
    (0, common_1.UsePipes)(new common_1.ValidationPipe({ transform: true, whitelist: true })),
    (0, swagger_1.ApiOperation)({
        summary: 'Cập nhật đối tác',
        description: 'Cập nhật thông tin của một đối tác',
    }),
    (0, swagger_1.ApiParam)({
        name: 'id',
        description: 'ID của đối tác cần cập nhật',
        example: '123e4567-e89b-12d3-a456-426614174000',
    }),
    (0, swagger_1.ApiBody)({
        type: dto_1.UpdateCmsPartnerDto,
        description: 'Thông tin cập nhật đối tác',
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Đối tác đã được cập nhật thành công',
        type: dto_1.CmsPartnerDto,
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.BAD_REQUEST,
        description: 'Dữ liệu đầu vào không hợp lệ',
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.NOT_FOUND,
        description: 'Không tìm thấy đối tác',
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.UNAUTHORIZED,
        description: 'Không có quyền truy cập',
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.FORBIDDEN,
        description: 'Không có quyền thực hiện thao tác này',
    }),
    openapi.ApiResponse({ status: 200, type: require("../dto/cms-partner.dto").CmsPartnerDto }),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __param(2, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, dto_1.UpdateCmsPartnerDto, Object]),
    __metadata("design:returntype", Promise)
], UpdateCmsPartnersController.prototype, "update", null);
__decorate([
    (0, common_1.Patch)('bulk'),
    (0, roles_decorator_1.Roles)('ADMIN', 'EDITOR'),
    (0, common_1.UsePipes)(new common_1.ValidationPipe({ transform: true, whitelist: true })),
    (0, swagger_1.ApiOperation)({
        summary: 'Cập nhật nhiều đối tác',
        description: 'Cập nhật thông tin của nhiều đối tác cùng lúc',
    }),
    (0, swagger_1.ApiBody)({
        type: dto_1.BulkUpdateCmsPartnersDto,
        description: 'Thông tin cập nhật nhiều đối tác',
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Kết quả cập nhật nhiều đối tác',
        type: dto_1.BulkOperationResponseDto,
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.BAD_REQUEST,
        description: 'Dữ liệu đầu vào không hợp lệ',
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.UNAUTHORIZED,
        description: 'Không có quyền truy cập',
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.FORBIDDEN,
        description: 'Không có quyền thực hiện thao tác này',
    }),
    openapi.ApiResponse({ status: 200, type: require("../dto/create.cms-partner.dto").BulkOperationResponseDto }),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [dto_1.BulkUpdateCmsPartnersDto, Object]),
    __metadata("design:returntype", Promise)
], UpdateCmsPartnersController.prototype, "bulkUpdate", null);
__decorate([
    (0, common_1.Patch)(':id/activate'),
    (0, roles_decorator_1.Roles)('ADMIN', 'EDITOR'),
    (0, swagger_1.ApiOperation)({
        summary: 'Kích hoạt đối tác',
        description: 'Kích hoạt một đối tác (chuyển trạng thái thành active)',
    }),
    (0, swagger_1.ApiParam)({
        name: 'id',
        description: 'ID của đối tác cần kích hoạt',
        example: '123e4567-e89b-12d3-a456-426614174000',
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Đối tác đã được kích hoạt thành công',
        type: dto_1.CmsPartnerDto,
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.NOT_FOUND,
        description: 'Không tìm thấy đối tác',
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.UNAUTHORIZED,
        description: 'Không có quyền truy cập',
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.FORBIDDEN,
        description: 'Không có quyền thực hiện thao tác này',
    }),
    openapi.ApiResponse({ status: 200, type: require("../dto/cms-partner.dto").CmsPartnerDto }),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], UpdateCmsPartnersController.prototype, "activate", null);
__decorate([
    (0, common_1.Patch)(':id/deactivate'),
    (0, roles_decorator_1.Roles)('ADMIN', 'EDITOR'),
    (0, swagger_1.ApiOperation)({
        summary: 'Vô hiệu hóa đối tác',
        description: 'Vô hiệu hóa một đối tác (chuyển trạng thái thành inactive)',
    }),
    (0, swagger_1.ApiParam)({
        name: 'id',
        description: 'ID của đối tác cần vô hiệu hóa',
        example: '123e4567-e89b-12d3-a456-426614174000',
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Đối tác đã được vô hiệu hóa thành công',
        type: dto_1.CmsPartnerDto,
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.NOT_FOUND,
        description: 'Không tìm thấy đối tác',
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.UNAUTHORIZED,
        description: 'Không có quyền truy cập',
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.FORBIDDEN,
        description: 'Không có quyền thực hiện thao tác này',
    }),
    openapi.ApiResponse({ status: 200, type: require("../dto/cms-partner.dto").CmsPartnerDto }),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], UpdateCmsPartnersController.prototype, "deactivate", null);
__decorate([
    (0, common_1.Patch)(':id/status'),
    (0, roles_decorator_1.Roles)('ADMIN', 'EDITOR'),
    (0, common_1.UsePipes)(new common_1.ValidationPipe({ transform: true, whitelist: true })),
    (0, swagger_1.ApiOperation)({
        summary: 'Cập nhật trạng thái đối tác',
        description: 'Cập nhật trạng thái của một đối tác',
    }),
    (0, swagger_1.ApiParam)({
        name: 'id',
        description: 'ID của đối tác cần cập nhật trạng thái',
        example: '123e4567-e89b-12d3-a456-426614174000',
    }),
    (0, swagger_1.ApiBody)({
        type: dto_1.UpdateCmsPartnerStatusDto,
        description: 'Trạng thái mới của đối tác',
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Trạng thái đối tác đã được cập nhật thành công',
        type: dto_1.CmsPartnerDto,
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.BAD_REQUEST,
        description: 'Trạng thái không hợp lệ',
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.NOT_FOUND,
        description: 'Không tìm thấy đối tác',
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.UNAUTHORIZED,
        description: 'Không có quyền truy cập',
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.FORBIDDEN,
        description: 'Không có quyền thực hiện thao tác này',
    }),
    openapi.ApiResponse({ status: 200, type: require("../dto/cms-partner.dto").CmsPartnerDto }),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __param(2, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, dto_1.UpdateCmsPartnerStatusDto, Object]),
    __metadata("design:returntype", Promise)
], UpdateCmsPartnersController.prototype, "updateStatus", null);
__decorate([
    (0, common_1.Patch)(':id/display-order'),
    (0, roles_decorator_1.Roles)('ADMIN', 'EDITOR'),
    (0, common_1.UsePipes)(new common_1.ValidationPipe({ transform: true, whitelist: true })),
    (0, swagger_1.ApiOperation)({
        summary: 'Cập nhật thứ tự hiển thị đối tác',
        description: 'Cập nhật thứ tự hiển thị của một đối tác',
    }),
    (0, swagger_1.ApiParam)({
        name: 'id',
        description: 'ID của đối tác cần cập nhật thứ tự hiển thị',
        example: '123e4567-e89b-12d3-a456-426614174000',
    }),
    (0, swagger_1.ApiBody)({
        type: dto_1.UpdateCmsPartnerDisplayOrderDto,
        description: 'Thứ tự hiển thị mới của đối tác',
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Thứ tự hiển thị đối tác đã được cập nhật thành công',
        type: dto_1.CmsPartnerDto,
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.BAD_REQUEST,
        description: 'Thứ tự hiển thị không hợp lệ',
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.NOT_FOUND,
        description: 'Không tìm thấy đối tác',
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.UNAUTHORIZED,
        description: 'Không có quyền truy cập',
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.FORBIDDEN,
        description: 'Không có quyền thực hiện thao tác này',
    }),
    openapi.ApiResponse({ status: 200, type: require("../dto/cms-partner.dto").CmsPartnerDto }),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __param(2, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, dto_1.UpdateCmsPartnerDisplayOrderDto, Object]),
    __metadata("design:returntype", Promise)
], UpdateCmsPartnersController.prototype, "updateDisplayOrder", null);
__decorate([
    (0, common_1.Patch)(':id/type'),
    (0, roles_decorator_1.Roles)('ADMIN', 'EDITOR'),
    (0, common_1.UsePipes)(new common_1.ValidationPipe({ transform: true, whitelist: true })),
    (0, swagger_1.ApiOperation)({
        summary: 'Cập nhật loại đối tác',
        description: 'Cập nhật loại của một đối tác',
    }),
    (0, swagger_1.ApiParam)({
        name: 'id',
        description: 'ID của đối tác cần cập nhật loại',
        example: '123e4567-e89b-12d3-a456-426614174000',
    }),
    (0, swagger_1.ApiBody)({
        type: dto_1.UpdateCmsPartnerTypeDto,
        description: 'Loại mới của đối tác',
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Loại đối tác đã được cập nhật thành công',
        type: dto_1.CmsPartnerDto,
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.BAD_REQUEST,
        description: 'Loại đối tác không hợp lệ',
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.NOT_FOUND,
        description: 'Không tìm thấy đối tác',
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.UNAUTHORIZED,
        description: 'Không có quyền truy cập',
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.FORBIDDEN,
        description: 'Không có quyền thực hiện thao tác này',
    }),
    openapi.ApiResponse({ status: 200, type: require("../dto/cms-partner.dto").CmsPartnerDto }),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __param(2, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, dto_1.UpdateCmsPartnerTypeDto, Object]),
    __metadata("design:returntype", Promise)
], UpdateCmsPartnersController.prototype, "updateType", null);
__decorate([
    (0, common_1.Patch)('bulk/activate'),
    (0, roles_decorator_1.Roles)('ADMIN', 'EDITOR'),
    (0, common_1.UsePipes)(new common_1.ValidationPipe({ transform: true, whitelist: true })),
    (0, swagger_1.ApiOperation)({
        summary: 'Kích hoạt nhiều đối tác',
        description: 'Kích hoạt nhiều đối tác cùng lúc',
    }),
    (0, swagger_1.ApiBody)({
        type: dto_1.BulkUpdateCmsPartnersStatusDto,
        description: 'Danh sách ID đối tác cần kích hoạt',
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Kết quả kích hoạt nhiều đối tác',
        type: dto_1.BulkOperationResponseDto,
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.BAD_REQUEST,
        description: 'Dữ liệu đầu vào không hợp lệ',
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.UNAUTHORIZED,
        description: 'Không có quyền truy cập',
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.FORBIDDEN,
        description: 'Không có quyền thực hiện thao tác này',
    }),
    openapi.ApiResponse({ status: 200, type: require("../dto/create.cms-partner.dto").BulkOperationResponseDto }),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [dto_1.BulkUpdateCmsPartnersStatusDto, Object]),
    __metadata("design:returntype", Promise)
], UpdateCmsPartnersController.prototype, "bulkActivate", null);
__decorate([
    (0, common_1.Patch)('bulk/deactivate'),
    (0, roles_decorator_1.Roles)('ADMIN', 'EDITOR'),
    (0, common_1.UsePipes)(new common_1.ValidationPipe({ transform: true, whitelist: true })),
    (0, swagger_1.ApiOperation)({
        summary: 'Vô hiệu hóa nhiều đối tác',
        description: 'Vô hiệu hóa nhiều đối tác cùng lúc',
    }),
    (0, swagger_1.ApiBody)({
        type: dto_1.BulkUpdateCmsPartnersStatusDto,
        description: 'Danh sách ID đối tác cần vô hiệu hóa',
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Kết quả vô hiệu hóa nhiều đối tác',
        type: dto_1.BulkOperationResponseDto,
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.BAD_REQUEST,
        description: 'Dữ liệu đầu vào không hợp lệ',
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.UNAUTHORIZED,
        description: 'Không có quyền truy cập',
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.FORBIDDEN,
        description: 'Không có quyền thực hiện thao tác này',
    }),
    openapi.ApiResponse({ status: 200, type: require("../dto/create.cms-partner.dto").BulkOperationResponseDto }),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [dto_1.BulkUpdateCmsPartnersStatusDto, Object]),
    __metadata("design:returntype", Promise)
], UpdateCmsPartnersController.prototype, "bulkDeactivate", null);
__decorate([
    (0, common_1.Patch)('bulk/status'),
    (0, roles_decorator_1.Roles)('ADMIN', 'EDITOR'),
    (0, common_1.UsePipes)(new common_1.ValidationPipe({ transform: true, whitelist: true })),
    (0, swagger_1.ApiOperation)({
        summary: 'Cập nhật trạng thái nhiều đối tác',
        description: 'Cập nhật trạng thái của nhiều đối tác cùng lúc',
    }),
    (0, swagger_1.ApiBody)({
        type: dto_1.BulkUpdateCmsPartnersStatusDto,
        description: 'Thông tin cập nhật trạng thái nhiều đối tác',
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Kết quả cập nhật trạng thái nhiều đối tác',
        type: dto_1.BulkOperationResponseDto,
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.BAD_REQUEST,
        description: 'Dữ liệu đầu vào không hợp lệ',
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.UNAUTHORIZED,
        description: 'Không có quyền truy cập',
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.FORBIDDEN,
        description: 'Không có quyền thực hiện thao tác này',
    }),
    openapi.ApiResponse({ status: 200, type: require("../dto/create.cms-partner.dto").BulkOperationResponseDto }),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [dto_1.BulkUpdateCmsPartnersStatusDto, Object]),
    __metadata("design:returntype", Promise)
], UpdateCmsPartnersController.prototype, "bulkUpdateStatus", null);
__decorate([
    (0, common_1.Patch)(':id/restore'),
    (0, roles_decorator_1.Roles)('ADMIN', 'EDITOR'),
    (0, swagger_1.ApiOperation)({
        summary: 'Khôi phục đối tác đã xóa',
        description: 'Khôi phục một đối tác đã bị xóa mềm',
    }),
    (0, swagger_1.ApiParam)({
        name: 'id',
        description: 'ID của đối tác cần khôi phục',
        example: '123e4567-e89b-12d3-a456-426614174000',
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Đối tác đã được khôi phục thành công',
        type: dto_1.CmsPartnerDto,
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.NOT_FOUND,
        description: 'Không tìm thấy đối tác đã xóa',
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.UNAUTHORIZED,
        description: 'Không có quyền truy cập',
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.FORBIDDEN,
        description: 'Không có quyền thực hiện thao tác này',
    }),
    openapi.ApiResponse({ status: 200, type: require("../dto/cms-partner.dto").CmsPartnerDto }),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], UpdateCmsPartnersController.prototype, "restore", null);
exports.UpdateCmsPartnersController = UpdateCmsPartnersController = UpdateCmsPartnersController_1 = __decorate([
    (0, swagger_1.ApiTags)('CMS Partners - Update'),
    (0, common_1.Controller)('cms/partners'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, swagger_1.ApiBearerAuth)(),
    __metadata("design:paramtypes", [update_cms_partners_service_1.UpdateCmsPartnersService])
], UpdateCmsPartnersController);
//# sourceMappingURL=update.cms-partners.controller.js.map
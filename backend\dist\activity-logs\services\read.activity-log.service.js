"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ReadActivityLogService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("typeorm");
const base_activity_log_service_1 = require("./base.activity-log.service");
let ReadActivityLogService = class ReadActivityLogService extends base_activity_log_service_1.BaseActivityLogService {
    async findAll(params) {
        try {
            this.logger.debug(`Tìm tất cả lịch sử hoạt động với tham số: ${JSON.stringify(params)}`);
            const { limit, page, filter, search } = params;
            const relations = params.relationsArray || [];
            const validatedRelations = this.validateRelations(relations);
            const query = this.activityLogRepository
                .createQueryBuilder('activityLog')
                .where({ isDeleted: false });
            if (search) {
                query.andWhere(new typeorm_1.Brackets(qb => {
                    qb.where('LOWER(activityLog.action) LIKE LOWER(:search)', { search: `%${search}%` })
                        .orWhere('LOWER(activityLog.description) LIKE LOWER(:search)', { search: `%${search}%` })
                        .orWhere('LOWER(activityLog.module) LIKE LOWER(:search)', { search: `%${search}%` });
                }));
            }
            if (filter) {
                const filters = filter.split(',');
                filters.forEach(f => {
                    const [field, value] = f.split(':');
                    if (field && value) {
                        query.andWhere(`activityLog.${field} = :${field}`, { [field]: value });
                    }
                });
            }
            if (validatedRelations.length > 0) {
                validatedRelations.forEach(relation => {
                    query.leftJoinAndSelect(`activityLog.${relation}`, relation);
                });
            }
            if (params.sort) {
                const sortOptions = params.sortOptions;
                sortOptions.forEach(option => {
                    query.orderBy(`activityLog.${option.field}`, option.order);
                });
            }
            else {
                query.orderBy('activityLog.createdAt', 'DESC');
            }
            query.skip((page - 1) * limit).take(limit);
            const [data, total] = await query.getManyAndCount();
            return {
                data: data.map(item => this.toDto(item)),
                total,
            };
        }
        catch (error) {
            this.logger.error(`Lỗi khi tìm tất cả lịch sử hoạt động: ${error.message}`, error.stack);
            throw error;
        }
    }
    async findOne(id, relations = []) {
        try {
            this.logger.debug(`Tìm lịch sử hoạt động với ID: ${id}`);
            const activityLog = await this.findByIdOrFail(id, relations);
            return this.toDto(activityLog);
        }
        catch (error) {
            this.logger.error(`Lỗi khi tìm lịch sử hoạt động với ID ${id}: ${error.message}`, error.stack);
            throw error;
        }
    }
    async findByUserId(userId, params) {
        try {
            this.logger.debug(`Tìm lịch sử hoạt động của người dùng với ID: ${userId}`);
            const { limit, page, filter } = params;
            const relations = params.relationsArray || [];
            const validatedRelations = this.validateRelations(relations);
            const query = this.activityLogRepository
                .createQueryBuilder('activityLog')
                .where({ userId, isDeleted: false });
            if (filter) {
                const filters = filter.split(',');
                filters.forEach(f => {
                    const [field, value] = f.split(':');
                    if (field && value) {
                        query.andWhere(`activityLog.${field} = :${field}`, { [field]: value });
                    }
                });
            }
            if (validatedRelations.length > 0) {
                validatedRelations.forEach(relation => {
                    query.leftJoinAndSelect(`activityLog.${relation}`, relation);
                });
            }
            if (params.sort) {
                const sortOptions = params.sortOptions;
                sortOptions.forEach(option => {
                    query.orderBy(`activityLog.${option.field}`, option.order);
                });
            }
            else {
                query.orderBy('activityLog.createdAt', 'DESC');
            }
            query.skip((page - 1) * limit).take(limit);
            const [data, total] = await query.getManyAndCount();
            return {
                data: data.map(item => this.toDto(item)),
                total,
            };
        }
        catch (error) {
            this.logger.error(`Lỗi khi tìm lịch sử hoạt động của người dùng ${userId}: ${error.message}`, error.stack);
            throw error;
        }
    }
    async findByAction(action, params) {
        try {
            this.logger.debug(`Tìm lịch sử hoạt động với hành động: ${action}`);
            const { limit, page } = params;
            const relations = params.relationsArray || [];
            const validatedRelations = this.validateRelations(relations);
            const query = this.activityLogRepository
                .createQueryBuilder('activityLog')
                .where({ action, isDeleted: false });
            if (validatedRelations.length > 0) {
                validatedRelations.forEach(relation => {
                    query.leftJoinAndSelect(`activityLog.${relation}`, relation);
                });
            }
            if (params.sort) {
                const sortOptions = params.sortOptions;
                sortOptions.forEach(option => {
                    query.orderBy(`activityLog.${option.field}`, option.order);
                });
            }
            else {
                query.orderBy('activityLog.createdAt', 'DESC');
            }
            query.skip((page - 1) * limit).take(limit);
            const [data, total] = await query.getManyAndCount();
            return {
                data: data.map(item => this.toDto(item)),
                total,
            };
        }
        catch (error) {
            this.logger.error(`Lỗi khi tìm lịch sử hoạt động với hành động ${action}: ${error.message}`, error.stack);
            throw error;
        }
    }
    async findByModule(module, params) {
        try {
            this.logger.debug(`Tìm lịch sử hoạt động với module: ${module}`);
            const { limit, page } = params;
            const relations = params.relationsArray || [];
            const validatedRelations = this.validateRelations(relations);
            const query = this.activityLogRepository
                .createQueryBuilder('activityLog')
                .where({ module, isDeleted: false });
            if (validatedRelations.length > 0) {
                validatedRelations.forEach(relation => {
                    query.leftJoinAndSelect(`activityLog.${relation}`, relation);
                });
            }
            if (params.sort) {
                const sortOptions = params.sortOptions;
                sortOptions.forEach(option => {
                    query.orderBy(`activityLog.${option.field}`, option.order);
                });
            }
            else {
                query.orderBy('activityLog.createdAt', 'DESC');
            }
            query.skip((page - 1) * limit).take(limit);
            const [data, total] = await query.getManyAndCount();
            return {
                data: data.map(item => this.toDto(item)),
                total,
            };
        }
        catch (error) {
            this.logger.error(`Lỗi khi tìm lịch sử hoạt động với module ${module}: ${error.message}`, error.stack);
            throw error;
        }
    }
    async search(keyword, params) {
        try {
            this.logger.debug(`Tìm kiếm lịch sử hoạt động với từ khóa: ${keyword}`);
            const { limit, page } = params;
            const query = this.activityLogRepository
                .createQueryBuilder('activityLog')
                .leftJoinAndSelect('activityLog.user', 'user')
                .where(new typeorm_1.Brackets(qb => {
                qb.where('LOWER(activityLog.action) LIKE LOWER(:keyword)', { keyword: `%${keyword}%` })
                    .orWhere('LOWER(activityLog.description) LIKE LOWER(:keyword)', { keyword: `%${keyword}%` })
                    .orWhere('LOWER(activityLog.module) LIKE LOWER(:keyword)', { keyword: `%${keyword}%` })
                    .orWhere('LOWER(user.email) LIKE LOWER(:keyword)', { keyword: `%${keyword}%` })
                    .orWhere('LOWER(user.fullName) LIKE LOWER(:keyword)', { keyword: `%${keyword}%` });
            }))
                .andWhere({ isDeleted: false })
                .orderBy('activityLog.createdAt', 'DESC')
                .skip((page - 1) * limit)
                .take(limit);
            const [data, total] = await query.getManyAndCount();
            return {
                data: data.map(item => this.toDto(item)),
                total,
            };
        }
        catch (error) {
            this.logger.error(`Lỗi khi tìm kiếm lịch sử hoạt động: ${error.message}`, error.stack);
            throw error;
        }
    }
    async count(filter) {
        try {
            this.logger.debug(`Đếm số lượng lịch sử hoạt động với bộ lọc: ${filter}`);
            const query = this.activityLogRepository
                .createQueryBuilder('activityLog')
                .where({ isDeleted: false });
            if (filter) {
                const filters = filter.split(',');
                filters.forEach(f => {
                    const [field, value] = f.split(':');
                    if (field && value) {
                        query.andWhere(`activityLog.${field} = :${field}`, { [field]: value });
                    }
                });
            }
            return query.getCount();
        }
        catch (error) {
            this.logger.error(`Lỗi khi đếm số lượng lịch sử hoạt động: ${error.message}`, error.stack);
            throw error;
        }
    }
};
exports.ReadActivityLogService = ReadActivityLogService;
exports.ReadActivityLogService = ReadActivityLogService = __decorate([
    (0, common_1.Injectable)()
], ReadActivityLogService);
//# sourceMappingURL=read.activity-log.service.js.map
import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';
import { decodeToken } from '@/lib/auth';
import { PUBLIC_PATHS, EXCLUDE_REDIRECT_PATHS, ADMIN_PATHS, isPublicPath, isAdminPath, isExcludeRedirectPath } from '@/lib/constants/paths';

export function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl;

  // Skip middleware for static assets, API routes, and Next.js internals
  if (
    pathname.startsWith('/_next/') ||
    pathname.startsWith('/api/') ||
    pathname.startsWith('/static/') ||
    pathname.includes('.') || // Files with extensions (images, css, js, etc.)
    pathname === '/favicon_sgs.ico'
  ) {
    return NextResponse.next();
  }

  // Kiểm tra xem đường dẫn hiện tại có phải là public path không
  const isPublic = isPublicPath(pathname);

  // <PERSON>ểm tra xem đường dẫn hiện tại có phải là admin path không
  const isAdmin = isAdminPath(pathname);

  // Lấy token từ cookie
  const token = request.cookies.get('token')?.value;

  // Nếu không có token và đang truy cập trang yêu cầu xác thực
  if (!token && !isPublic) {
    // Chuyển hướng đến trang đăng nhập
    const loginUrl = new URL('/login', request.url);
    loginUrl.searchParams.set('callbackUrl', encodeURI(pathname));
    return NextResponse.redirect(loginUrl);
  }

  // Nếu có token
  if (token) {
    // Kiểm tra xem đường dẫn hiện tại có nằm trong danh sách loại trừ không
    const isExcluded = isExcludeRedirectPath(pathname);

    // Nếu đang truy cập trang công khai (như login, register) và không phải là trang loại trừ
    if (isPublic && !isExcluded && pathname !== '/reset-password') {
      try {
        // Giải mã token để kiểm tra vai trò
        const user = decodeToken(token);

        // Nếu là admin, chuyển hướng đến trang admin dashboard
        if (user?.roles?.includes('ADMIN')) {
          return NextResponse.redirect(new URL('/admin/dashboard', request.url));
        }

        // Nếu không phải admin, chuyển hướng đến trang dashboard thông thường
        return NextResponse.redirect(new URL('/dashboard', request.url));
      } catch (error) {
        // Nếu có lỗi khi giải mã token, chuyển hướng đến trang dashboard mặc định
        return NextResponse.redirect(new URL('/dashboard', request.url));
      }
    }

    // Nếu đang truy cập trang admin nhưng không phải là admin
    if (isAdmin) {
      try {
        // Giải mã token để kiểm tra vai trò
        const user = decodeToken(token);

        // Nếu không phải admin, chuyển hướng đến trang dashboard thông thường
        if (!user?.roles?.includes('ADMIN')) {
          return NextResponse.redirect(new URL('/dashboard', request.url));
        }
      } catch (error) {
        // Nếu có lỗi khi giải mã token, chuyển hướng đến trang dashboard mặc định
        return NextResponse.redirect(new URL('/dashboard', request.url));
      }
    }
  }

  return NextResponse.next();
}

// Chỉ áp dụng middleware cho các đường dẫn sau
export const config = {
  matcher: [
    /*
     * Match all request paths except:
     * 1. /api routes
     * 2. /_next (Next.js internals)
     * 3. /_static (inside /public)
     * 4. Static files (images, css, js, etc.)
     * 5. favicon.ico, sitemap.xml
     */
    '/((?!api|_next|_static|.*\\.|favicon.ico|sitemap.xml).*)',
  ],
};

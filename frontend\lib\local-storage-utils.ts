/**
 * Check if cached data is still valid
 */
export function isCacheValid(cache: any): boolean {
    if (!cache) return false;

    const now = Date.now();
    return now < cache.expiresAt;
}

/**
* Clear cache
*/
export function clearCacheByKey(cacheKey: string): void {
    if (typeof window !== 'undefined') {
        localStorage.removeItem(cacheKey);
    }
}

/**
 * Clear all cache
 */
export function clearAllCache(): void {
    if (typeof window !== 'undefined') {
        localStorage.clear();
    }
}

/**
 * Save cache to localStorage
 */
export function saveCacheToStorage(cacheKey: string, cache: any): void {
    if (typeof window === 'undefined' || !cache) return;

    try {
        localStorage.setItem(cacheKey, JSON.stringify(cache));
    } catch (error) {
        console.error('Failed to save metadata cache:', error);
    }
}

/**
 * Load cache from localStorage
 */
export function loadCacheFromStorage(cacheKey: string): void {
    if (typeof window === 'undefined') return;

    try {
        const cached = localStorage.getItem(cacheKey);
        if (cached) {
            return JSON.parse(cached);
        }
    } catch (error) {
        console.error('Failed to load metadata cache:', error);
    }
}

export function loadAllCache(): any[] {
    if (typeof window === 'undefined') return [];
    const cacheKeys = Object.keys(localStorage);
    const cached: any[] = [];

    for (const key of cacheKeys) {
        try {
            const value = localStorage.getItem(key);
            if (value) {
                // Kiểm tra nếu giá trị bắt đầu bằng { hoặc [ thì mới parse JSON
                if (value.startsWith('{') || value.startsWith('[')) {
                    const parsedValue = JSON.parse(value);
                    cached.push(parsedValue);
                }
            }
        } catch (error) {
            // Bỏ qua các giá trị không phải JSON
            console.debug(`Skipping non-JSON value for key: ${key}`);
        }
    }

    return cached;
}

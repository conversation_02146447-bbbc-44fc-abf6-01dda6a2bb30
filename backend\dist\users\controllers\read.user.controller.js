"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ReadUserController = void 0;
const openapi = require("@nestjs/swagger");
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const read_user_service_1 = require("../services/read.user.service");
const jwt_auth_guard_1 = require("../../common/guards/jwt-auth.guard");
const roles_guard_1 = require("../../common/guards/roles.guard");
const permissions_guard_1 = require("../../common/guards/permissions.guard");
const roles_decorator_1 = require("../../common/decorators/roles.decorator");
const permissions_decorator_1 = require("../../common/decorators/permissions.decorator");
const get_user_decorator_1 = require("../../common/decorators/get-user.decorator");
const api_response_dto_1 = require("../../common/dto/api-response.dto");
const pagination_response_dto_1 = require("../../common/dto/pagination-response.dto");
const custom_pagination_meta_dto_1 = require("../../common/dto/custom-pagination-meta.dto");
const custom_pagination_query_dto_1 = require("../../common/dto/custom-pagination-query.dto");
let ReadUserController = class ReadUserController {
    readUserService;
    constructor(readUserService) {
        this.readUserService = readUserService;
    }
    async findAll(paginationQuery, userId, roles) {
        let filter = paginationQuery.filter;
        if (!roles?.includes('ADMIN')) {
            filter = filter ? `${filter},id:${userId}` : `id:${userId}`;
        }
        const { data, total } = await this.readUserService.findAll({
            limit: paginationQuery.limit,
            page: paginationQuery.page,
            sortBy: paginationQuery.sort ? paginationQuery.sort.split(',')[0].split(':')[0] : undefined,
            sortOrder: paginationQuery.sort ?
                (paginationQuery.sort.split(',')[0].split(':')[1]?.toUpperCase() === 'DESC' ? 'DESC' : 'ASC') :
                undefined,
            filter: filter,
            relations: paginationQuery.relations ? paginationQuery.relations.split(',') : []
        });
        const meta = new custom_pagination_meta_dto_1.CustomPageMetaDto({
            pageQueryDto: paginationQuery,
            itemCount: total,
        });
        return new pagination_response_dto_1.PaginationResponseDto(data, meta);
    }
    async search(keyword, paginationQuery, userId, roles) {
        let queryParams = { ...paginationQuery };
        if (!roles?.includes('ADMIN')) {
            queryParams = {
                ...queryParams,
                filter: queryParams.filter ? `${queryParams.filter},id:${userId}` : `id:${userId}`
            };
        }
        const { data, total } = await this.readUserService.search(keyword, {
            limit: queryParams.limit,
            page: queryParams.page
        });
        const meta = new custom_pagination_meta_dto_1.CustomPageMetaDto({
            pageQueryDto: paginationQuery,
            itemCount: total,
        });
        return new pagination_response_dto_1.PaginationResponseDto(data, meta);
    }
    async getStatistics() {
        return this.readUserService.getStatistics();
    }
    async count(filter) {
        return this.readUserService.count(filter);
    }
    async findDeleted(paginationQuery) {
        const { data, total } = await this.readUserService.findDeleted({
            limit: paginationQuery.limit,
            page: paginationQuery.page
        });
        const meta = new custom_pagination_meta_dto_1.CustomPageMetaDto({
            pageQueryDto: paginationQuery,
            itemCount: total,
        });
        return new pagination_response_dto_1.PaginationResponseDto(data, meta);
    }
    async findOne(id, relations, userId, roles) {
        if (!roles?.includes('ADMIN') && id !== userId) {
            throw new common_1.BadRequestException('Bạn không có quyền xem thông tin người dùng này.');
        }
        const relationArray = relations ? relations.split(',') : [];
        return this.readUserService.findOne(id, relationArray);
    }
};
exports.ReadUserController = ReadUserController;
__decorate([
    (0, common_1.Get)(),
    (0, roles_decorator_1.Roles)('ADMIN'),
    (0, permissions_decorator_1.Permissions)('user:list'),
    (0, swagger_1.ApiOperation)({ summary: 'Lấy danh sách người dùng với phân trang và lọc' }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Danh sách người dùng.',
        type: api_response_dto_1.ApiResponseDto,
        schema: {
            properties: {
                data: {
                    type: 'array',
                    items: { $ref: '#/components/schemas/UserDto' },
                },
                meta: {
                    type: 'object',
                    properties: {
                        total: { type: 'number' },
                        page: { type: 'number' },
                        limit: { type: 'number' },
                        totalPages: { type: 'number' },
                    },
                },
            },
        },
    }),
    (0, swagger_1.ApiQuery)({ name: 'page', required: false, type: Number, description: 'Số trang' }),
    (0, swagger_1.ApiQuery)({ name: 'limit', required: false, type: Number, description: 'Số lượng mục trên mỗi trang' }),
    (0, swagger_1.ApiQuery)({
        name: 'sort',
        required: false,
        description: 'Sắp xếp theo định dạng field:order (ví dụ: createdAt:DESC,fullName:ASC)',
    }),
    (0, swagger_1.ApiQuery)({ name: 'filter', required: false, type: String, description: 'Lọc theo điều kiện (ví dụ: isActive:true)' }),
    (0, swagger_1.ApiQuery)({ name: 'search', required: false, type: String, description: 'Tìm kiếm theo từ khóa' }),
    (0, swagger_1.ApiQuery)({ name: 'relations', required: false, type: String, description: 'Các mối quan hệ cần tải (ví dụ: roles,wallet)' }),
    openapi.ApiResponse({ status: 200 }),
    __param(0, (0, common_1.Query)()),
    __param(1, (0, get_user_decorator_1.GetUser)('id')),
    __param(2, (0, get_user_decorator_1.GetUser)('roles')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [custom_pagination_query_dto_1.CustomPaginationQueryDto, String, Array]),
    __metadata("design:returntype", Promise)
], ReadUserController.prototype, "findAll", null);
__decorate([
    (0, common_1.Get)('search'),
    (0, roles_decorator_1.Roles)('ADMIN'),
    (0, permissions_decorator_1.Permissions)('user:list'),
    (0, swagger_1.ApiOperation)({ summary: 'Tìm kiếm người dùng theo từ khóa' }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Danh sách người dùng phù hợp với từ khóa.',
        type: api_response_dto_1.ApiResponseDto,
        schema: {
            properties: {
                data: {
                    type: 'array',
                    items: { $ref: '#/components/schemas/UserDto' },
                },
                meta: {
                    type: 'object',
                    properties: {
                        total: { type: 'number' },
                        page: { type: 'number' },
                        limit: { type: 'number' },
                        totalPages: { type: 'number' },
                    },
                },
            },
        },
    }),
    (0, swagger_1.ApiQuery)({
        name: 'keyword',
        required: true,
        description: 'Từ khóa để tìm kiếm trên các trường',
    }),
    (0, swagger_1.ApiQuery)({ name: 'page', required: false, type: Number, description: 'Số trang' }),
    (0, swagger_1.ApiQuery)({ name: 'limit', required: false, type: Number, description: 'Số lượng mục trên mỗi trang' }),
    (0, swagger_1.ApiQuery)({
        name: 'sort',
        required: false,
        description: 'Sắp xếp theo định dạng field:order (ví dụ: createdAt:DESC,fullName:ASC)',
    }),
    openapi.ApiResponse({ status: 200 }),
    __param(0, (0, common_1.Query)('keyword')),
    __param(1, (0, common_1.Query)()),
    __param(2, (0, get_user_decorator_1.GetUser)('id')),
    __param(3, (0, get_user_decorator_1.GetUser)('roles')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, custom_pagination_query_dto_1.CustomPaginationQueryDto, String, Array]),
    __metadata("design:returntype", Promise)
], ReadUserController.prototype, "search", null);
__decorate([
    (0, common_1.Get)('statistics'),
    (0, roles_decorator_1.Roles)('ADMIN'),
    (0, permissions_decorator_1.Permissions)('user:list'),
    (0, swagger_1.ApiOperation)({ summary: 'Lấy thống kê người dùng' }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Thống kê người dùng.',
        type: api_response_dto_1.ApiResponseDto,
        schema: {
            properties: {
                data: {
                    type: 'object',
                    properties: {
                        total: { type: 'number' },
                        activeCounts: {
                            type: 'object',
                            properties: {
                                true: { type: 'number' },
                                false: { type: 'number' },
                                PENDING: { type: 'number' }
                            }
                        }
                    }
                }
            }
        },
    }),
    openapi.ApiResponse({ status: 200 }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], ReadUserController.prototype, "getStatistics", null);
__decorate([
    (0, common_1.Get)('count'),
    (0, swagger_1.ApiOperation)({ summary: 'Đếm số lượng người dùng' }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Số lượng người dùng.',
        type: api_response_dto_1.ApiResponseDto,
        schema: { properties: { data: { type: 'number' } } },
    }),
    (0, swagger_1.ApiQuery)({
        name: 'filter',
        required: false,
        description: 'Lọc theo trường (ví dụ: status:active)',
    }),
    openapi.ApiResponse({ status: 200, type: Number }),
    __param(0, (0, common_1.Query)('filter')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], ReadUserController.prototype, "count", null);
__decorate([
    (0, common_1.Get)('deleted'),
    (0, roles_decorator_1.Roles)('ADMIN'),
    (0, permissions_decorator_1.Permissions)('user:list'),
    (0, swagger_1.ApiOperation)({ summary: 'Lấy danh sách người dùng đã xóa mềm' }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Danh sách người dùng đã xóa mềm được lấy thành công.',
        type: api_response_dto_1.ApiResponseDto,
        schema: {
            properties: {
                data: {
                    type: 'array',
                    items: { $ref: '#/components/schemas/UserDto' },
                },
                meta: {
                    type: 'object',
                    properties: {
                        total: { type: 'number' },
                        page: { type: 'number' },
                        limit: { type: 'number' },
                        totalPages: { type: 'number' },
                    },
                },
            },
        },
    }),
    (0, swagger_1.ApiQuery)({ name: 'page', required: false, type: Number, description: 'Số trang' }),
    (0, swagger_1.ApiQuery)({ name: 'limit', required: false, type: Number, description: 'Số lượng mục trên mỗi trang' }),
    openapi.ApiResponse({ status: 200 }),
    __param(0, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [custom_pagination_query_dto_1.CustomPaginationQueryDto]),
    __metadata("design:returntype", Promise)
], ReadUserController.prototype, "findDeleted", null);
__decorate([
    (0, common_1.Get)(':id'),
    (0, swagger_1.ApiOperation)({ summary: 'Lấy thông tin người dùng theo ID với các mối quan hệ' }),
    (0, swagger_1.ApiParam)({ name: 'id', description: 'ID của người dùng cần lấy thông tin' }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Lấy thông tin người dùng thành công.',
        type: api_response_dto_1.ApiResponseDto,
        schema: { properties: { data: { $ref: '#/components/schemas/UserDto' } } },
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.NOT_FOUND,
        description: 'Không tìm thấy người dùng.',
        type: api_response_dto_1.ApiResponseDto,
        schema: { properties: { data: { type: 'object', nullable: true } } },
    }),
    (0, swagger_1.ApiQuery)({
        name: 'relations',
        required: false,
        description: 'Các mối quan hệ cần tải (ví dụ: roles,tokenAssets)',
    }),
    openapi.ApiResponse({ status: 200, type: require("../dto/user.dto").UserDto }),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Query)('relations')),
    __param(2, (0, get_user_decorator_1.GetUser)('id')),
    __param(3, (0, get_user_decorator_1.GetUser)('roles')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String, String, Array]),
    __metadata("design:returntype", Promise)
], ReadUserController.prototype, "findOne", null);
exports.ReadUserController = ReadUserController = __decorate([
    (0, swagger_1.ApiTags)('users-read'),
    (0, common_1.Controller)('users'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard, roles_guard_1.RolesGuard, permissions_guard_1.PermissionsGuard),
    __metadata("design:paramtypes", [read_user_service_1.ReadUserService])
], ReadUserController);
//# sourceMappingURL=read.user.controller.js.map
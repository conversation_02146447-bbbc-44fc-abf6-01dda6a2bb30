"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ReadCmsCategoriesService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const event_emitter_1 = require("@nestjs/event-emitter");
const base_cms_categories_service_1 = require("./base.cms-categories.service");
const cms_categories_entity_1 = require("../entity/cms-categories.entity");
let ReadCmsCategoriesService = class ReadCmsCategoriesService extends base_cms_categories_service_1.BaseCmsCategoriesService {
    categoryRepository;
    dataSource;
    eventEmitter;
    constructor(categoryRepository, dataSource, eventEmitter) {
        super(categoryRepository, dataSource, eventEmitter);
        this.categoryRepository = categoryRepository;
        this.dataSource = dataSource;
        this.eventEmitter = eventEmitter;
    }
    async findAll(params) {
        try {
            this.logger.debug(`Đang tìm tất cả chuyên mục CMS với tham số: ${JSON.stringify(params)}`);
            const { limit, page, search } = params;
            const skip = (page - 1) * limit;
            let relationsArray = params.relationsArray || [];
            const requiredRelations = ['parent', 'children', 'creator', 'updater', 'deleter'];
            requiredRelations.forEach(relation => {
                if (!relationsArray.includes(relation)) {
                    relationsArray.push(relation);
                }
            });
            const validatedRelations = this.validateRelations(relationsArray);
            const queryBuilder = this.categoryRepository
                .createQueryBuilder('category')
                .where('category.isDeleted = :isDeleted', { isDeleted: false });
            if (search) {
                queryBuilder.andWhere(new typeorm_2.Brackets(qb => {
                    qb.where('LOWER(category.name) LIKE LOWER(:search)', { search: `%${search}%` })
                        .orWhere('LOWER(category.description) LIKE LOWER(:search)', { search: `%${search}%` })
                        .orWhere('LOWER(category.slug) LIKE LOWER(:search)', { search: `%${search}%` });
                }));
            }
            const sortOptions = params.sortOptions;
            if (sortOptions && sortOptions.length > 0) {
                const { field, order } = sortOptions[0];
                queryBuilder.orderBy(`category.${field}`, order);
            }
            else {
                queryBuilder.orderBy('category.createdAt', 'DESC');
            }
            validatedRelations.forEach(relation => {
                queryBuilder.leftJoinAndSelect(`category.${relation}`, relation);
            });
            queryBuilder.skip(skip).take(limit);
            const [categories, total] = await queryBuilder.getManyAndCount();
            const categoryDtos = this.toDtos(categories);
            return {
                data: categoryDtos,
                total,
            };
        }
        catch (error) {
            this.logger.error(`Lỗi khi tìm tất cả chuyên mục CMS: ${error.message}`, error.stack);
            if (error instanceof common_1.BadRequestException) {
                throw error;
            }
            throw new common_1.InternalServerErrorException(`Không thể lấy danh sách chuyên mục CMS: ${error.message}`);
        }
    }
    async search(keyword, params) {
        try {
            this.logger.debug(`Đang tìm kiếm chuyên mục CMS với từ khóa: ${keyword}`);
            const queryBuilder = this.categoryRepository
                .createQueryBuilder('category')
                .where('category.isDeleted = :isDeleted', { isDeleted: false });
            queryBuilder.andWhere(new typeorm_2.Brackets(qb => {
                qb.where('LOWER(category.name) LIKE LOWER(:keyword)', { keyword: `%${keyword}%` })
                    .orWhere('LOWER(category.description) LIKE LOWER(:keyword)', { keyword: `%${keyword}%` })
                    .orWhere('LOWER(category.slug) LIKE LOWER(:keyword)', { keyword: `%${keyword}%` });
            }));
            const sortOptions = params.sortOptions;
            if (sortOptions && sortOptions.length > 0) {
                const { field, order } = sortOptions[0];
                queryBuilder.orderBy(`category.${field}`, order);
            }
            else {
                queryBuilder.orderBy('category.createdAt', 'DESC');
            }
            const skip = (params.page - 1) * params.limit;
            queryBuilder.skip(skip).take(params.limit);
            const [categories, total] = await queryBuilder.getManyAndCount();
            const categoryDtos = this.toDtos(categories);
            return {
                data: categoryDtos,
                total,
            };
        }
        catch (error) {
            this.logger.error(`Lỗi khi tìm kiếm chuyên mục CMS: ${error.message}`, error.stack);
            throw new common_1.InternalServerErrorException(`Không thể tìm kiếm chuyên mục CMS: ${error.message}`);
        }
    }
    async count(filter) {
        try {
            const where = this.buildWhereClause(filter);
            return this.categoryRepository.count({ where });
        }
        catch (error) {
            this.logger.error(`Lỗi khi đếm số lượng chuyên mục CMS: ${error.message}`, error.stack);
            throw new common_1.InternalServerErrorException(`Không thể đếm số lượng chuyên mục CMS: ${error.message}`);
        }
    }
    async findOne(id, relations = []) {
        try {
            this.logger.debug(`Đang tìm chuyên mục CMS theo ID: ${id}`);
            const validatedRelations = this.validateRelations(relations);
            try {
                const category = await this.findById(id, validatedRelations, true);
                if (!category) {
                    throw new common_1.NotFoundException(`Không tìm thấy chuyên mục với ID: ${id}`);
                }
                return this.toDto(category);
            }
            catch (notFoundError) {
                if (notFoundError instanceof common_1.NotFoundException) {
                    return null;
                }
                throw notFoundError;
            }
        }
        catch (error) {
            this.logger.error(`Lỗi khi tìm chuyên mục CMS theo ID ${id}: ${error.message}`, error.stack);
            if (error instanceof common_1.NotFoundException) {
                return null;
            }
            throw new common_1.InternalServerErrorException(`Không thể lấy thông tin chuyên mục CMS: ${error.message}`);
        }
    }
    async findOneOrFail(id, relations = []) {
        try {
            this.logger.debug(`Đang tìm chuyên mục CMS theo ID: ${id}`);
            const validatedRelations = this.validateRelations(relations);
            const validRelationsToLoad = [...validatedRelations];
            if (!validRelationsToLoad.includes('parent')) {
                validRelationsToLoad.push('parent');
            }
            const category = await this.findById(id, validRelationsToLoad);
            if (!category) {
                throw new common_1.NotFoundException(`Không tìm thấy chuyên mục với ID: ${id}`);
            }
            return this.toDto(category);
        }
        catch (error) {
            this.logger.error(`Lỗi khi tìm chuyên mục CMS theo ID ${id}: ${error.message}`, error.stack);
            if (error instanceof common_1.NotFoundException) {
                throw error;
            }
            throw new common_1.InternalServerErrorException(`Không thể lấy thông tin chuyên mục CMS: ${error.message}`);
        }
    }
    async findDeleted(params) {
        try {
            this.logger.debug(`Đang tìm các chuyên mục CMS đã bị xóa mềm với tham số: ${JSON.stringify(params)}`);
            const { limit, page } = params;
            let relationsArray = params.relationsArray || [];
            const requiredRelations = ['parent', 'children', 'creator', 'updater', 'deleter'];
            requiredRelations.forEach(relation => {
                if (!relationsArray.includes(relation)) {
                    relationsArray.push(relation);
                }
            });
            const validatedRelations = this.validateRelations(relationsArray);
            const queryBuilder = this.categoryRepository
                .createQueryBuilder('category')
                .where('category.isDeleted = :isDeleted', { isDeleted: true });
            if (params.search) {
                queryBuilder.andWhere(new typeorm_2.Brackets(qb => {
                    qb.where('LOWER(category.name) LIKE LOWER(:search)', { search: `%${params.search}%` })
                        .orWhere('LOWER(category.description) LIKE LOWER(:search)', { search: `%${params.search}%` })
                        .orWhere('LOWER(category.slug) LIKE LOWER(:search)', { search: `%${params.search}%` });
                }));
            }
            const sortOptions = params.sortOptions;
            if (sortOptions && sortOptions.length > 0) {
                const { field, order } = sortOptions[0];
                queryBuilder.orderBy(`category.${field}`, order);
            }
            else {
                queryBuilder.orderBy('category.deletedAt', 'DESC');
            }
            validatedRelations.forEach(relation => {
                queryBuilder.leftJoinAndSelect(`category.${relation}`, relation);
            });
            const skip = (page - 1) * limit;
            queryBuilder.skip(skip).take(limit);
            const [categories, total] = await queryBuilder.getManyAndCount();
            const categoryDtos = this.toDtos(categories);
            return {
                data: categoryDtos,
                total,
            };
        }
        catch (error) {
            this.logger.error(`Lỗi khi tìm chuyên mục CMS đã xóa: ${error.message}`, error.stack);
            throw new common_1.InternalServerErrorException(`Không thể lấy danh sách chuyên mục CMS đã xóa: ${error.message}`);
        }
    }
    async getActiveCategories(params) {
        try {
            this.logger.debug(`Đang lấy chuyên mục CMS đang hoạt động với tham số: ${JSON.stringify(params)}`);
            const { limit, page, search } = params;
            const skip = (page - 1) * limit;
            const queryBuilder = this.categoryRepository
                .createQueryBuilder('category')
                .where('category.isDeleted = :isDeleted', { isDeleted: false })
                .andWhere('category.status = :status', { status: cms_categories_entity_1.CmsCategoryStatus.ACTIVE });
            if (search) {
                queryBuilder.andWhere(new typeorm_2.Brackets(qb => {
                    qb.where('LOWER(category.name) LIKE LOWER(:search)', { search: `%${search}%` })
                        .orWhere('LOWER(category.description) LIKE LOWER(:search)', { search: `%${search}%` })
                        .orWhere('LOWER(category.slug) LIKE LOWER(:search)', { search: `%${search}%` });
                }));
            }
            const sortOptions = params.sortOptions;
            if (sortOptions && sortOptions.length > 0) {
                const { field, order } = sortOptions[0];
                queryBuilder.orderBy(`category.${field}`, order);
            }
            else {
                queryBuilder.orderBy('category.name', 'ASC');
            }
            queryBuilder.leftJoinAndSelect('category.parent', 'parent');
            queryBuilder.skip(skip).take(limit);
            const [categories, total] = await queryBuilder.getManyAndCount();
            const categoryDtos = this.toDtos(categories);
            return {
                data: categoryDtos,
                total,
            };
        }
        catch (error) {
            this.logger.error(`Lỗi khi lấy chuyên mục CMS đang hoạt động: ${error.message}`, error.stack);
            throw new common_1.InternalServerErrorException(`Không thể lấy danh sách chuyên mục CMS đang hoạt động: ${error.message}`);
        }
    }
    async getStatistics() {
        try {
            const total = await this.categoryRepository.count({
                where: { isDeleted: false },
            });
            const activeCount = await this.categoryRepository.count({
                where: { status: cms_categories_entity_1.CmsCategoryStatus.ACTIVE, isDeleted: false },
            });
            const inactiveCount = await this.categoryRepository.count({
                where: { status: cms_categories_entity_1.CmsCategoryStatus.INACTIVE, isDeleted: false },
            });
            return {
                total,
                statusCounts: {
                    active: activeCount,
                    inactive: inactiveCount,
                },
            };
        }
        catch (error) {
            this.logger.error(`Lỗi khi lấy thống kê chuyên mục CMS: ${error.message}`, error.stack);
            throw new common_1.InternalServerErrorException(`Không thể lấy thống kê chuyên mục CMS: ${error.message}`);
        }
    }
};
exports.ReadCmsCategoriesService = ReadCmsCategoriesService;
exports.ReadCmsCategoriesService = ReadCmsCategoriesService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(cms_categories_entity_1.CmsCategories)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        typeorm_2.DataSource,
        event_emitter_1.EventEmitter2])
], ReadCmsCategoriesService);
//# sourceMappingURL=read.cms-categories.service.js.map
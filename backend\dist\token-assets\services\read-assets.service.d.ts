import { BaseAssetsService } from './base-assets.service';
import { AssetDto } from '../dto/asset.dto';
export declare class ReadAssetsService extends BaseAssetsService {
    findAll(params: {
        limit: number;
        page: number;
        sortBy?: string;
        sortOrder?: 'ASC' | 'DESC';
        filter?: string;
        search?: string;
        relations?: string[];
    }): Promise<{
        data: AssetDto[];
        total: number;
    }>;
    search(keyword: string, params: {
        limit: number;
        page: number;
        sortOrder?: string;
    }, filter?: string): Promise<{
        data: AssetDto[];
        total: number;
    }>;
    findOne(id: string, relations?: string[]): Promise<AssetDto>;
    findDeleted(params: {
        limit: number;
        page: number;
    }): Promise<{
        data: AssetDto[];
        total: number;
    }>;
    count(filter?: string): Promise<number>;
    export(format: 'csv' | 'json'): Promise<any>;
}

{"sorted_middleware": [], "middleware": {"/": {"files": ["server/edge/chunks/_b8fada48._.js", "server/edge/chunks/[root-of-the-server]__17f5e14d._.js", "server/edge/chunks/edge-wrapper_da667e18.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "/:nextData(_next/data/[^/]{1,})?/((?!api|_next|_static|.*\\.|favicon.ico|sitemap.xml).*){(\\\\.json)}?", "originalSource": "/((?!api|_next|_static|.*\\.|favicon.ico|sitemap.xml).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "MiT1oeakaJWkJ8EwFblXRNw+ISsoYbrMf8Y9opW88hE=", "__NEXT_PREVIEW_MODE_ID": "dfb83bca70e3d26f377c36c178f9e555", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "68efed5b2a33c3e382fe3e3be1f3c889c817de739c35c83a92b7b213d26b18bc", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "3371f9566e1e4514e39f78231fbc8d1dfaebaa46118ae8ed1281850269d12837"}}}, "instrumentation": null, "functions": {}}
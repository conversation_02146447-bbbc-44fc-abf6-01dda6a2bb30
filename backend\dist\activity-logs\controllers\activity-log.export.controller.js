"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var ActivityLogExportController_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.ActivityLogExportController = void 0;
const openapi = require("@nestjs/swagger");
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const jwt_auth_guard_1 = require("../../common/guards/jwt-auth.guard");
const roles_guard_1 = require("../../common/guards/roles.guard");
const roles_decorator_1 = require("../../common/decorators/roles.decorator");
const get_user_decorator_1 = require("../../common/decorators/get-user.decorator");
const default_user_role_enum_1 = require("../../users/enums/default-user-role.enum");
const user_entity_1 = require("../../users/entities/user.entity");
const export_activity_log_service_1 = require("../services/export.activity-log.service");
const custom_pagination_query_dto_1 = require("../../common/dto/custom-pagination-query.dto");
let ActivityLogExportController = ActivityLogExportController_1 = class ActivityLogExportController {
    activityLogService;
    logger = new common_1.Logger(ActivityLogExportController_1.name);
    constructor(activityLogService) {
        this.activityLogService = activityLogService;
    }
    async export(format = 'json') {
        this.logger.debug(`Đang xuất dữ liệu lịch sử hoạt động với định dạng: ${format}`);
        return this.activityLogService.export(format);
    }
    async exportByUser(userId, format = 'json', currentUserId, roles) {
        this.logger.debug(`Đang xuất dữ liệu lịch sử hoạt động của người dùng ${userId} với định dạng: ${format}`);
        if (!roles.includes('ADMIN') && userId !== currentUserId) {
            throw new Error('Bạn không có quyền xuất lịch sử hoạt động của người dùng khác');
        }
        return this.activityLogService.exportByUser(userId, format);
    }
    async exportByModule(module, format = 'json') {
        this.logger.debug(`Đang xuất dữ liệu lịch sử hoạt động của module ${module} với định dạng: ${format}`);
        return this.activityLogService.exportByModule(module, format);
    }
    async exportWithFilter(paginationQuery, format = 'json') {
        this.logger.debug(`Đang xuất dữ liệu lịch sử hoạt động với bộ lọc: ${JSON.stringify(paginationQuery)}`);
        return this.activityLogService.exportWithFilter(paginationQuery, format);
    }
    async exportMyActivities(user, format = 'json') {
        this.logger.debug(`Đang xuất dữ liệu lịch sử hoạt động của người dùng hiện tại: ${user.id}`);
        return this.activityLogService.exportByUser(user.id, format);
    }
};
exports.ActivityLogExportController = ActivityLogExportController;
__decorate([
    (0, common_1.Get)('export'),
    (0, roles_decorator_1.Roles)(default_user_role_enum_1.PrimaryUserRole.ADMIN),
    (0, swagger_1.ApiOperation)({ summary: 'Xuất dữ liệu lịch sử hoạt động' }),
    (0, swagger_1.ApiQuery)({
        name: 'format',
        description: 'Định dạng xuất (csv, json)',
        required: false,
        enum: ['csv', 'json'],
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Dữ liệu lịch sử hoạt động',
    }),
    openapi.ApiResponse({ status: 200, type: Object }),
    __param(0, (0, common_1.Query)('format')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], ActivityLogExportController.prototype, "export", null);
__decorate([
    (0, common_1.Get)('export/by-user/:userId'),
    (0, roles_decorator_1.Roles)(default_user_role_enum_1.PrimaryUserRole.ADMIN),
    (0, swagger_1.ApiOperation)({ summary: 'Xuất dữ liệu lịch sử hoạt động theo người dùng' }),
    (0, swagger_1.ApiParam)({
        name: 'userId',
        description: 'ID của người dùng cần xuất lịch sử hoạt động',
    }),
    (0, swagger_1.ApiQuery)({
        name: 'format',
        description: 'Định dạng xuất (csv, json)',
        required: false,
        enum: ['csv', 'json'],
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Dữ liệu lịch sử hoạt động của người dùng',
    }),
    openapi.ApiResponse({ status: 200, type: Object }),
    __param(0, (0, common_1.Param)('userId', common_1.ParseUUIDPipe)),
    __param(1, (0, common_1.Query)('format')),
    __param(2, (0, get_user_decorator_1.GetUser)('id')),
    __param(3, (0, get_user_decorator_1.GetUser)('roles')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String, String, Array]),
    __metadata("design:returntype", Promise)
], ActivityLogExportController.prototype, "exportByUser", null);
__decorate([
    (0, common_1.Get)('export/by-module/:module'),
    (0, roles_decorator_1.Roles)(default_user_role_enum_1.PrimaryUserRole.ADMIN),
    (0, swagger_1.ApiOperation)({ summary: 'Xuất dữ liệu lịch sử hoạt động theo module' }),
    (0, swagger_1.ApiParam)({
        name: 'module',
        description: 'Module cần xuất lịch sử hoạt động',
    }),
    (0, swagger_1.ApiQuery)({
        name: 'format',
        description: 'Định dạng xuất (csv, json)',
        required: false,
        enum: ['csv', 'json'],
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Dữ liệu lịch sử hoạt động của module',
    }),
    openapi.ApiResponse({ status: 200, type: Object }),
    __param(0, (0, common_1.Param)('module')),
    __param(1, (0, common_1.Query)('format')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String]),
    __metadata("design:returntype", Promise)
], ActivityLogExportController.prototype, "exportByModule", null);
__decorate([
    (0, common_1.Get)('export/with-filter'),
    (0, roles_decorator_1.Roles)(default_user_role_enum_1.PrimaryUserRole.ADMIN),
    (0, swagger_1.ApiOperation)({ summary: 'Xuất dữ liệu lịch sử hoạt động với bộ lọc' }),
    (0, swagger_1.ApiQuery)({
        name: 'format',
        description: 'Định dạng xuất (csv, json)',
        required: false,
        enum: ['csv', 'json'],
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Dữ liệu lịch sử hoạt động với bộ lọc',
    }),
    openapi.ApiResponse({ status: 200, type: Object }),
    __param(0, (0, common_1.Query)()),
    __param(1, (0, common_1.Query)('format')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [custom_pagination_query_dto_1.CustomPaginationQueryDto, String]),
    __metadata("design:returntype", Promise)
], ActivityLogExportController.prototype, "exportWithFilter", null);
__decorate([
    (0, common_1.Get)('export/my-activities'),
    (0, swagger_1.ApiOperation)({ summary: 'Xuất dữ liệu lịch sử hoạt động của người dùng hiện tại' }),
    (0, swagger_1.ApiQuery)({
        name: 'format',
        description: 'Định dạng xuất (csv, json)',
        required: false,
        enum: ['csv', 'json'],
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Dữ liệu lịch sử hoạt động của người dùng hiện tại',
    }),
    openapi.ApiResponse({ status: 200, type: Object }),
    __param(0, (0, get_user_decorator_1.GetUser)()),
    __param(1, (0, common_1.Query)('format')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [user_entity_1.User, String]),
    __metadata("design:returntype", Promise)
], ActivityLogExportController.prototype, "exportMyActivities", null);
exports.ActivityLogExportController = ActivityLogExportController = ActivityLogExportController_1 = __decorate([
    (0, swagger_1.ApiTags)('activity-logs'),
    (0, swagger_1.ApiBearerAuth)(),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard, roles_guard_1.RolesGuard),
    (0, common_1.Controller)('activity-logs'),
    __metadata("design:paramtypes", [export_activity_log_service_1.ExportActivityLogService])
], ActivityLogExportController);
//# sourceMappingURL=activity-log.export.controller.js.map
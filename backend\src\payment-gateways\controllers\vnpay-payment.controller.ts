import { 
  Controller, 
  Post, 
  Get, 
  Body, 
  Query, 
  Param, 
  HttpCode, 
  HttpStatus, 
  Logger,
  <PERSON>s,
  BadRequestException
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBody } from '@nestjs/swagger';
import { Response } from 'express';

import { VnpayPaymentService } from '../services/vnpay-payment.service';
import { VnpayTransactionRepository } from '../repositories/vnpay-transaction.repository';
import { 
  PaymentRequest, 
  PaymentResponse, 
  PaymentCallback,
  PaymentQueryRequest,
  PaymentQueryResponse,
  PaymentRefundRequest,
  PaymentRefundResponse
} from '../interfaces/payment-integration.interface';

@ApiTags('VNPAY Payment Gateway - Portable')
@Controller('vnpay-payment')
export class VnpayPaymentController {
  private readonly logger = new Logger(VnpayPaymentController.name);

  constructor(
    private readonly vnpayPaymentService: VnpayPaymentService,
    private readonly vnpayTransactionRepository: VnpayTransactionRepository,
  ) {}

  /**
   * Create VNPAY payment
   */
  @Post('create')
  @ApiOperation({ summary: 'Create VNPAY payment' })
  @ApiResponse({ status: 201, description: 'Payment created successfully' })
  async createPayment(@Body() request: PaymentRequest): Promise<PaymentResponse> {
    try {
      return await this.vnpayPaymentService.createPayment(request);
    } catch (error) {
      this.logger.error(`Error creating payment: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Handle VNPAY return callback
   */
  @Get('return')
  @ApiOperation({ summary: 'Handle VNPAY return callback' })
  @ApiResponse({ status: 200, description: 'Callback processed' })
  async handleReturn(
    @Query() query: Record<string, string>,
    @Res() res: Response
  ): Promise<void> {
    try {
      const callback = await this.vnpayPaymentService.handleReturnCallback(query);
      
      // Redirect based on result
      const baseUrl = process.env.FRONTEND_URL || 'http://localhost:3000';
      
      if (callback.isSuccess) {
        const successUrl = `${baseUrl}/payment/success?` +
          `merchantTxnRef=${callback.merchantTxnRef}&` +
          `amount=${callback.amount}&` +
          `vnpayTxnRef=${callback.vnpayTxnRef}&` +
          `externalRef=${callback.externalRef}`;
        res.redirect(successUrl);
      } else {
        const failureUrl = `${baseUrl}/payment/failure?` +
          `merchantTxnRef=${callback.merchantTxnRef}&` +
          `message=${encodeURIComponent(callback.message || 'Payment failed')}&` +
          `externalRef=${callback.externalRef}`;
        res.redirect(failureUrl);
      }
    } catch (error) {
      this.logger.error(`Error handling return: ${error.message}`, error.stack);
      const errorUrl = `${process.env.FRONTEND_URL}/payment/error?message=${encodeURIComponent('System error')}`;
      res.redirect(errorUrl);
    }
  }

  /**
   * Handle VNPAY IPN callback
   */
  @Post('ipn')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: 'Handle VNPAY IPN callback' })
  @ApiResponse({ status: 200, description: 'IPN processed' })
  async handleIpn(
    @Query() query: Record<string, string>
  ): Promise<{ RspCode: string; Message: string }> {
    try {
      const callback = await this.vnpayPaymentService.handleIpnCallback(query);
      
      if (callback.isValid && callback.isSuccess) {
        return { RspCode: '00', Message: 'Confirm Success' };
      } else if (callback.isValid) {
        return { RspCode: '01', Message: 'Order not found' };
      } else {
        return { RspCode: '97', Message: 'Invalid signature' };
      }
    } catch (error) {
      this.logger.error(`Error handling IPN: ${error.message}`, error.stack);
      return { RspCode: '99', Message: 'Unknown error' };
    }
  }

  /**
   * Query transaction status
   */
  @Post('query')
  @ApiOperation({ summary: 'Query VNPAY transaction status' })
  @ApiResponse({ status: 200, description: 'Query successful' })
  async queryTransaction(@Body() request: PaymentQueryRequest): Promise<PaymentQueryResponse> {
    try {
      return await this.vnpayPaymentService.queryTransaction(request);
    } catch (error) {
      this.logger.error(`Error querying transaction: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Refund transaction
   */
  @Post('refund')
  @ApiOperation({ summary: 'Refund VNPAY transaction' })
  @ApiResponse({ status: 200, description: 'Refund processed' })
  async refundTransaction(@Body() request: PaymentRefundRequest): Promise<PaymentRefundResponse> {
    try {
      return await this.vnpayPaymentService.refundTransaction(request);
    } catch (error) {
      this.logger.error(`Error refunding transaction: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Get transaction details
   */
  @Get('transaction/:merchantTxnRef')
  @ApiOperation({ summary: 'Get transaction details' })
  @ApiResponse({ status: 200, description: 'Transaction found' })
  async getTransaction(@Param('merchantTxnRef') merchantTxnRef: string): Promise<any> {
    try {
      const transaction = await this.vnpayPaymentService.getTransaction(merchantTxnRef);
      
      if (!transaction) {
        throw new BadRequestException(`Transaction not found: ${merchantTxnRef}`);
      }

      return {
        id: transaction.id,
        merchantTxnRef: transaction.merchantTxnRef,
        vnpayTxnRef: transaction.vnpayTxnRef,
        vnpayTxnNo: transaction.vnpayTxnNo,
        status: transaction.status,
        type: transaction.type,
        amount: transaction.amount,
        currency: transaction.currency,
        orderInfo: transaction.orderInfo,
        bankCode: transaction.bankCode,
        cardType: transaction.cardType,
        vnpayResponseCode: transaction.vnpayResponseCode,
        vnpayTransactionStatus: transaction.vnpayTransactionStatus,
        vnpayPayDate: transaction.vnpayPayDate,
        externalRef: transaction.externalRef,
        externalMetadata: transaction.externalMetadata ? 
          JSON.parse(transaction.externalMetadata) : null,
        errorMessage: transaction.errorMessage,
        createdAt: transaction.createdAt,
        updatedAt: transaction.updatedAt,
        processedAt: transaction.processedAt,
        expiresAt: transaction.expiresAt,
      };
    } catch (error) {
      this.logger.error(`Error getting transaction: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Get transaction statistics
   */
  @Get('statistics')
  @ApiOperation({ summary: 'Get payment statistics' })
  @ApiResponse({ status: 200, description: 'Statistics retrieved' })
  async getStatistics(
    @Query('externalRef') externalRef?: string,
    @Query('fromDate') fromDate?: string,
    @Query('toDate') toDate?: string,
  ): Promise<any> {
    try {
      const filter: any = {};
      
      if (externalRef) filter.externalRef = externalRef;
      if (fromDate) filter.fromDate = new Date(fromDate);
      if (toDate) filter.toDate = new Date(toDate);

      return await this.vnpayPaymentService.getStatistics(filter);
    } catch (error) {
      this.logger.error(`Error getting statistics: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Get transactions with filters
   */
  @Get('transactions')
  @ApiOperation({ summary: 'Get transactions with filters' })
  @ApiResponse({ status: 200, description: 'Transactions retrieved' })
  async getTransactions(
    @Query('externalRef') externalRef?: string,
    @Query('status') status?: string,
    @Query('fromDate') fromDate?: string,
    @Query('toDate') toDate?: string,
    @Query('page') page: number = 1,
    @Query('limit') limit: number = 20,
  ): Promise<any> {
    try {
      const filter: any = {};
      
      if (externalRef) filter.externalRef = externalRef;
      if (status) filter.status = status;
      if (fromDate) filter.fromDate = new Date(fromDate);
      if (toDate) filter.toDate = new Date(toDate);

      const result = await this.vnpayTransactionRepository.findWithFilters(
        filter, 
        page, 
        limit
      );

      return {
        transactions: result.transactions.map(t => ({
          id: t.id,
          merchantTxnRef: t.merchantTxnRef,
          vnpayTxnRef: t.vnpayTxnRef,
          status: t.status,
          type: t.type,
          amount: t.amount,
          orderInfo: t.orderInfo,
          bankCode: t.bankCode,
          externalRef: t.externalRef,
          createdAt: t.createdAt,
          processedAt: t.processedAt,
        })),
        total: result.total,
        page,
        limit,
        totalPages: Math.ceil(result.total / limit),
      };
    } catch (error) {
      this.logger.error(`Error getting transactions: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Health check
   */
  @Get('health')
  @ApiOperation({ summary: 'Health check' })
  @ApiResponse({ status: 200, description: 'Service healthy' })
  async healthCheck(): Promise<any> {
    return {
      status: 'healthy',
      timestamp: new Date().toISOString(),
      service: 'VNPAY Payment Gateway',
      version: '1.0.0',
      features: [
        'Payment Creation',
        'Return Callback',
        'IPN Callback', 
        'Transaction Query',
        'Transaction Refund',
        'Statistics',
        'Transaction History'
      ],
    };
  }

  /**
   * Mark expired transactions
   */
  @Post('maintenance/mark-expired')
  @ApiOperation({ summary: 'Mark expired transactions' })
  @ApiResponse({ status: 200, description: 'Expired transactions marked' })
  async markExpiredTransactions(): Promise<any> {
    try {
      const count = await this.vnpayTransactionRepository.markExpiredTransactions();
      
      return {
        success: true,
        message: `Marked ${count} expired transactions`,
        count,
        timestamp: new Date().toISOString(),
      };
    } catch (error) {
      this.logger.error(`Error marking expired transactions: ${error.message}`, error.stack);
      throw error;
    }
  }
}

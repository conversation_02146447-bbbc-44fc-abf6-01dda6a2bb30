import {
  Body,
  Controller,
  Get,
  HttpCode,
  HttpStatus,
  Logger,
  Post,
  Query,
  Res,
} from '@nestjs/common';
import { ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';
import { Response } from 'express';

import { VnpayPaymentService } from '../services/vnpay-payment.service';
import {
  PaymentRequest,
  PaymentResponse,
} from '../interfaces/payment-integration.interface';

@ApiTags('VNPAY Payment Gateway')
@Controller('vnpay-payment')
export class VnpayPaymentController {
  private readonly logger = new Logger(VnpayPaymentController.name);

  constructor(
    private readonly vnpayPaymentService: VnpayPaymentService,
  ) {}

  @Post('create')
  @ApiOperation({ summary: 'Create VNPAY payment' })
  @ApiResponse({ status: 201, description: 'Payment created successfully' })
  async createPayment(
    @Body() request: PaymentRequest,
  ): Promise<PaymentResponse> {
    try {
      return await this.vnpayPaymentService.createPayment(request);
    } catch (error) {
      this.logger.error(
        `Error creating payment: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  @Get('return')
  @ApiOperation({ summary: 'Handle VNPAY return callback' })
  @ApiResponse({ status: 200, description: 'Callback processed' })
  async handleReturn(
    @Query() query: Record<string, string>,
    @Res() res: Response,
  ): Promise<void> {
    try {
      const callback =
        await this.vnpayPaymentService.handleReturnCallback(query);
      const baseUrl = process.env.FRONTEND_URL || 'http://localhost:3000';

      if (callback.isSuccess) {
        const successUrl =
          `${baseUrl}/payment/success?` +
          `merchantTxnRef=${callback.merchantTxnRef}&` +
          `amount=${callback.amount}&` +
          `vnpayTxnRef=${callback.vnpayTxnRef}&` +
          `externalRef=${callback.externalRef}`;
        res.redirect(successUrl);
      } else {
        const failureUrl =
          `${baseUrl}/payment/failure?` +
          `merchantTxnRef=${callback.merchantTxnRef}&` +
          `message=${encodeURIComponent(callback.message || 'Payment failed')}&` +
          `externalRef=${callback.externalRef}`;
        res.redirect(failureUrl);
      }
    } catch (error) {
      this.logger.error(`Error handling return: ${error.message}`, error.stack);
      const errorUrl = `${process.env.FRONTEND_URL}/payment/error?message=${encodeURIComponent('System error')}`;
      res.redirect(errorUrl);
    }
  }

  @Post('ipn')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: 'Handle VNPAY IPN callback' })
  @ApiResponse({ status: 200, description: 'IPN processed' })
  async handleIpn(
    @Query() query: Record<string, string>,
  ): Promise<{ RspCode: string; Message: string }> {
    try {
      const callback = await this.vnpayPaymentService.handleIpnCallback(query);

      if (callback.isValid && callback.isSuccess) {
        return { RspCode: '00', Message: 'Confirm Success' };
      } else if (callback.isValid) {
        return { RspCode: '01', Message: 'Order not found' };
      } else {
        return { RspCode: '97', Message: 'Invalid signature' };
      }
    } catch (error) {
      this.logger.error(`Error handling IPN: ${error.message}`, error.stack);
      return { RspCode: '99', Message: 'Unknown error' };
    }
  }


}

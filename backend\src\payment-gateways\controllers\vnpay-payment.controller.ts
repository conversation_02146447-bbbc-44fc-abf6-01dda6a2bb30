import {
  Body,
  Controller,
  Get,
  HttpCode,
  HttpStatus,
  Logger,
  Post,
  Query,
  Res,
} from '@nestjs/common';
import { ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';
import { Response } from 'express';

import { VnpayPaymentService } from '../services/vnpay-payment.service';
import {
  PaymentRequest,
  PaymentResponse,
} from '../interfaces/payment-integration.interface';

// Controller xử lý API VNPAY
@ApiTags('VNPAY Payment Gateway')
@Controller('vnpay-payment')
export class VnpayPaymentController {
  private readonly logger = new Logger(VnpayPaymentController.name);

  constructor(private readonly vnpayPaymentService: VnpayPaymentService) {}

  // Tạo thanh toán VNPAY
  @Post('create')
  @ApiOperation({ summary: 'Tạo thanh toán VNPAY' })
  @ApiResponse({ status: 201, description: 'Tạo thanh toán thành công' })
  async createPayment(
    @Body() request: PaymentRequest,
  ): Promise<PaymentResponse> {
    try {
      return await this.vnpayPaymentService.createPayment(request);
    } catch (error) {
      this.logger.error(
        `Lỗi khi tạo thanh toán: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  // Xử lý callback return từ VNPAY
  @Get('return')
  @ApiOperation({ summary: 'Xử lý callback return từ VNPAY' })
  @ApiResponse({ status: 200, description: 'Xử lý callback thành công' })
  async handleReturn(
    @Query() query: Record<string, string>,
    @Res() res: Response,
  ): Promise<void> {
    try {
      const callback =
        await this.vnpayPaymentService.handleReturnCallback(query);
      const baseUrl = process.env.FRONTEND_URL || 'http://localhost:3000';

      // Chuyển hướng dựa trên kết quả thanh toán
      if (callback.isSuccess) {
        const successUrl =
          `${baseUrl}/payment/success?` +
          `merchantTxnRef=${callback.merchantTxnRef}&` +
          `amount=${callback.amount}&` +
          `vnpayTxnRef=${callback.vnpayTxnRef}&` +
          `externalRef=${callback.externalRef}`;
        res.redirect(successUrl);
      } else {
        const failureUrl =
          `${baseUrl}/payment/failure?` +
          `merchantTxnRef=${callback.merchantTxnRef}&` +
          `message=${encodeURIComponent(callback.message || 'Thanh toán thất bại')}&` +
          `externalRef=${callback.externalRef}`;
        res.redirect(failureUrl);
      }
    } catch (error) {
      this.logger.error(`Lỗi khi xử lý callback return: ${error.message}`, error.stack);
      const errorUrl = `${process.env.FRONTEND_URL}/payment/error?message=${encodeURIComponent('Lỗi hệ thống')}`;
      res.redirect(errorUrl);
    }
  }

  // Xử lý callback IPN từ VNPAY
  @Post('ipn')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: 'Xử lý callback IPN từ VNPAY' })
  @ApiResponse({ status: 200, description: 'Xử lý IPN thành công' })
  async handleIpn(
    @Query() query: Record<string, string>,
  ): Promise<{ RspCode: string; Message: string }> {
    try {
      const callback = await this.vnpayPaymentService.handleIpnCallback(query);

      // Trả về response code theo yêu cầu của VNPAY
      if (callback.isValid && callback.isSuccess) {
        return { RspCode: '00', Message: 'Xac nhan thanh cong' };
      } else if (callback.isValid) {
        return { RspCode: '01', Message: 'Khong tim thay don hang' };
      } else {
        return { RspCode: '97', Message: 'Chu ky khong hop le' };
      }
    } catch (error) {
      this.logger.error(`Lỗi khi xử lý IPN: ${error.message}`, error.stack);
      return { RspCode: '99', Message: 'Loi khong xac dinh' };
    }
  }
}

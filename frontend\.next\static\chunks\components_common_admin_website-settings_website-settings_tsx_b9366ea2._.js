(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/components/common/admin/website-settings/website-settings.tsx [app-client] (ecmascript, next/dynamic entry, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/components_17df2df7._.js",
  "static/chunks/node_modules__pnpm_d773f766._.js",
  "static/chunks/components_common_admin_website-settings_website-settings_tsx_01074b5c._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/components/common/admin/website-settings/website-settings.tsx [app-client] (ecmascript, next/dynamic entry)");
    });
});
}}),
}]);
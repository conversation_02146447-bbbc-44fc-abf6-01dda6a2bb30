import { CmsPostType, CmsPostStatus } from '../entity/cms-posts.entity';
export declare class CreateCmsPostDto {
    postType?: CmsPostType;
    title: string;
    slug?: string;
    excerpt?: string;
    content: string;
    featuredImageUrl?: string;
    status?: CmsPostStatus;
    publishedAt?: string;
    eventStartDate?: string;
    eventEndDate?: string;
    eventLocation?: string;
    metaTitle?: string;
    metaDescription?: string;
    metaKeywords?: string;
    allowComments?: boolean;
    isFeatured?: boolean;
    categoryId?: string;
    authorId: string;
}

"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AppModule = void 0;
const common_1 = require("@nestjs/common");
const config_1 = require("@nestjs/config");
const schedule_1 = require("@nestjs/schedule");
const throttler_1 = require("@nestjs/throttler");
const common_module_1 = require("./common/common.module");
const activity_logs_module_1 = require("./activity-logs/activity-logs.module");
const agent_commission_module_1 = require("./agent-commissions/agent-commission.module");
const agent_module_1 = require("./agents/agent.module");
const attachment_module_1 = require("./attachments/attachment.module");
const auth_module_1 = require("./auth/auth.module");
const banks_module_1 = require("./banks/banks.module");
const database_module_1 = require("./database/database.module");
const mailer_module_1 = require("./mailer/mailer.module");
const order_book_module_1 = require("./order-book/order-book.module");
const vnpay_payment_module_1 = require("./payment-gateways/vnpay-payment.module");
const payment_methods_module_1 = require("./payment-methods/payment-methods.module");
const permissions_module_1 = require("./permissions/permissions.module");
const price_alerts_module_1 = require("./price-alerts/price-alerts.module");
const role_module_1 = require("./roles/role.module");
const system_config_module_1 = require("./system-configs/system-config.module");
const asset_module_1 = require("./token-assets/asset.module");
const token_category_module_1 = require("./token-categories/token-category.module");
const token_price_module_1 = require("./token-prices/token-price.module");
const token_module_1 = require("./tokens/token.module");
const commands_module_1 = require("./commands/commands.module");
const crypto_transactions_module_1 = require("./crypto-transactions/crypto-transactions.module");
const crypto_wallets_module_1 = require("./crypto-wallets/crypto-wallets.module");
const transactions_module_1 = require("./transactions/transactions.module");
const user_kyc_module_1 = require("./user-kyc/user-kyc.module");
const user_module_1 = require("./users/user.module");
const wallets_module_1 = require("./wallets/wallets.module");
const cms_contacts_module_1 = require("./cms-contacts/cms-contacts.module");
const nestjs_i18n_1 = require("nestjs-i18n");
const path = require("path");
const cache_manager_1 = require("@nestjs/cache-manager");
const event_emitter_1 = require("@nestjs/event-emitter");
const cache_manager_redis_store_1 = require("cache-manager-redis-store");
const Joi = require("joi");
const nestjs_pino_1 = require("nestjs-pino");
const cms_banners_module_1 = require("./cms-banners/cms-banners.module");
const cms_categories_module_1 = require("./cms-categories/cms-categories.module");
const cms_customer_feedbacks_module_1 = require("./cms-customer-feedbacks/cms-customer-feedbacks.module");
const cms_menus_module_1 = require("./cms-menus/cms-menus.module");
const cms_pages_module_1 = require("./cms-pages/cms-pages.module");
const cms_partners_module_1 = require("./cms-partners/cms-partners.module");
const cms_posts_module_1 = require("./cms-posts/cms-posts.module");
const cms_showrooms_module_1 = require("./cms-showrooms/cms-showrooms.module");
const cms_tags_module_1 = require("./cms-tags/cms-tags.module");
const minio_1 = require("./common/minio");
const websockets_module_1 = require("./common/websockets/websockets.module");
const ecom_order_details_module_1 = require("./ecom-order-details/ecom-order-details.module");
const ecom_orders_module_1 = require("./ecom-orders/ecom-orders.module");
const ecom_product_categories_module_1 = require("./ecom-product-categories/ecom-product-categories.module");
const ecom_products_module_1 = require("./ecom-products/ecom-products.module");
const health_module_1 = require("./health/health.module");
const notifications_module_1 = require("./notifications/notifications.module");
const reports_module_1 = require("./reports/reports.module");
const schedule_jobs_module_1 = require("./schedules/schedule-jobs.module");
let AppModule = class AppModule {
    constructor() {
        const i18nPath = path.join(__dirname, '/i18n/');
        try {
            const fs = require('fs');
            const exists = fs.existsSync(i18nPath);
            if (exists) {
                const files = fs.readdirSync(i18nPath);
                const enPath = path.join(i18nPath, 'en', 'translation.json');
                const enExists = fs.existsSync(enPath);
            }
        }
        catch (err) {
            console.error('[AppModule] Lỗi khi kiểm tra thư mục i18n:', err);
        }
    }
};
exports.AppModule = AppModule;
exports.AppModule = AppModule = __decorate([
    (0, common_1.Module)({
        imports: [
            config_1.ConfigModule.forRoot({
                isGlobal: true,
                envFilePath: process.env.NODE_ENV === 'test' ? '.env.test' : '.env',
                validationSchema: Joi.object({
                    NODE_ENV: Joi.string()
                        .valid('development', 'production', 'test')
                        .default('development'),
                    PORT: Joi.number().default(3168),
                    API_PREFIX: Joi.string().default('api'),
                    API_DEFAULT_VERSION: Joi.string().default('1'),
                    CORS_ORIGIN: Joi.string().default('*'),
                    DATABASE_TYPE: Joi.string().required(),
                    DATABASE_HOST: Joi.string().required(),
                    DATABASE_PORT: Joi.number().required(),
                    DATABASE_USERNAME: Joi.string().required(),
                    DATABASE_PASSWORD: Joi.string().required(),
                    DATABASE_NAME: Joi.string().required(),
                    DATABASE_POOL_MAX: Joi.number().default(10),
                    DATABASE_POOL_IDLE_TIMEOUT: Joi.number().default(30000),
                    JWT_SECRET: Joi.string().required(),
                    JWT_EXPIRATION_TIME: Joi.string().required(),
                    SWAGGER_TITLE: Joi.string().default('API Docs'),
                    SWAGGER_DESC: Joi.string().default('API Documentation'),
                    SWAGGER_VERSION: Joi.string().default('1.0'),
                    SWAGGER_PATH: Joi.string().default('api-docs'),
                    PROD_URL: Joi.string().uri().optional(),
                    LOG_LEVEL: Joi.string()
                        .valid('debug', 'info', 'warn', 'error')
                        .default('debug'),
                    THROTTLE_TTL: Joi.number().default(60000),
                    THROTTLE_LIMIT: Joi.number().default(20),
                    MINIO_ENDPOINT: Joi.string().default('localhost'),
                    MINIO_PORT: Joi.number().default(9000),
                    MINIO_USE_SSL: Joi.boolean().default(false),
                    MINIO_ACCESS_KEY: Joi.string().default('minioadmin'),
                    MINIO_SECRET_KEY: Joi.string().default('minioadmin'),
                    MINIO_REGION: Joi.string().default('us-east-1'),
                    MINIO_DEFAULT_BUCKET: Joi.string().default('uploads'),
                }),
                validationOptions: {
                    allowUnknown: true,
                    abortEarly: false,
                },
            }),
            nestjs_pino_1.LoggerModule.forRootAsync({
                imports: [config_1.ConfigModule],
                inject: [config_1.ConfigService],
                useFactory: (configService) => {
                    const nodeEnv = configService.get('NODE_ENV', 'development');
                    const logLevel = configService.get('LOG_LEVEL', 'info');
                    const redactHeaders = configService
                        .get('LOG_REDACT_HEADERS', 'Authorization,Cookie,Set-Cookie')
                        ?.split(',') ?? [];
                    return {
                        pinoHttp: {
                            level: logLevel,
                            transport: nodeEnv !== 'production'
                                ? {
                                    target: 'pino-pretty',
                                    options: {
                                        singleLine: true,
                                        colorize: true,
                                        levelFirst: true,
                                        translateTime: 'SYS:yyyy-mm-dd HH:MM:ss.l',
                                        ignore: 'pid,hostname',
                                    },
                                }
                                : undefined,
                            redact: {
                                paths: [
                                    'req.headers.authorization',
                                    'req.headers.cookie',
                                    'res.headers["set-cookie"]',
                                ].concat(redactHeaders.map((h) => `req.headers["${h.toLowerCase().trim()}"]`)),
                                censor: '** REDACTED **',
                            },
                        },
                    };
                },
            }),
            cache_manager_1.CacheModule.registerAsync({
                isGlobal: true,
                imports: [config_1.ConfigModule],
                useFactory: async (configService) => ({
                    store: cache_manager_redis_store_1.redisStore,
                    host: configService.get('REDIS_HOST', 'localhost'),
                    port: configService.get('REDIS_PORT', 6379),
                    password: configService.get('REDIS_PASSWORD'),
                    ttl: configService.get('CACHE_TTL', 60),
                    db: configService.get('REDIS_DB', 0),
                }),
                inject: [config_1.ConfigService],
            }),
            database_module_1.DatabaseModule,
            common_module_1.CommonModule,
            nestjs_i18n_1.I18nModule.forRoot({
                fallbackLanguage: 'en',
                loaderOptions: {
                    path: path.join(__dirname, '/i18n/'),
                    watch: false,
                },
                resolvers: [
                    new nestjs_i18n_1.HeaderResolver(['x-custom-lang']),
                    nestjs_i18n_1.AcceptLanguageResolver,
                ],
            }),
            throttler_1.ThrottlerModule.forRootAsync({
                imports: [config_1.ConfigModule],
                inject: [config_1.ConfigService],
                useFactory: (config) => [
                    {
                        ttl: config.get('THROTTLE_TTL', 60000),
                        limit: config.get('THROTTLE_LIMIT', 20),
                    },
                ],
            }),
            schedule_1.ScheduleModule.forRoot(),
            schedule_jobs_module_1.ScheduleJobsModule,
            event_emitter_1.EventEmitterModule.forRoot({}),
            auth_module_1.AuthModule,
            user_module_1.UserModule,
            role_module_1.RoleModule,
            permissions_module_1.PermissionsModule,
            token_module_1.TokenModule,
            token_category_module_1.TokenCategoryModule,
            token_price_module_1.TokenPriceModule,
            asset_module_1.AssetModule,
            order_book_module_1.OrderBookModule,
            transactions_module_1.TransactionsModule,
            price_alerts_module_1.PriceAlertsModule,
            wallets_module_1.WalletsModule,
            crypto_wallets_module_1.CryptoWalletsModule,
            crypto_transactions_module_1.CryptoTransactionsModule,
            payment_methods_module_1.PaymentMethodsModule,
            vnpay_payment_module_1.VnpayPaymentModule,
            banks_module_1.BanksModule,
            agent_module_1.AgentModule,
            agent_commission_module_1.AgentCommissionModule,
            user_kyc_module_1.UserKycModule,
            attachment_module_1.AttachmentModule,
            activity_logs_module_1.ActivityLogsModule,
            system_config_module_1.SystemConfigModule,
            mailer_module_1.MailerModule,
            notifications_module_1.NotificationsModule,
            websockets_module_1.WebsocketsModule,
            health_module_1.HealthModule,
            reports_module_1.ReportsModule,
            minio_1.MinioModule,
            commands_module_1.CommandsModule,
            ecom_product_categories_module_1.EcomProductCategoriesModule,
            cms_categories_module_1.CmsCategoriesModule,
            cms_menus_module_1.CmsMenusModule,
            cms_posts_module_1.CmsPostsModule,
            cms_tags_module_1.CmsTagsModule,
            cms_banners_module_1.CmsBannersModule,
            cms_customer_feedbacks_module_1.CmsCustomerFeedbacksModule,
            cms_pages_module_1.CmsPagesModule,
            cms_showrooms_module_1.CmsShowroomsModule,
            cms_partners_module_1.CmsPartnersModule,
            ecom_products_module_1.EcomProductsModule,
            ecom_orders_module_1.EcomOrdersModule,
            ecom_order_details_module_1.EcomOrderDetailsModule,
            cms_contacts_module_1.CmsContactsModule,
        ],
        controllers: [],
        providers: [],
    }),
    __metadata("design:paramtypes", [])
], AppModule);
//# sourceMappingURL=app.module.js.map
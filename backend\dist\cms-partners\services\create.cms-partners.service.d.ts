import { Repository, DataSource } from 'typeorm';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { CmsPartners } from '../entity/cms-partners.entity';
import { BaseCmsPartnersService } from './base.cms-partners.service';
import { CreateCmsPartnerDto, BulkCreateCmsPartnersDto, CreateCmsPartnerFromTemplateDto, DuplicateCmsPartnerDto, ImportCmsPartnersDto, BulkOperationResponseDto, CmsPartnerDto } from '../dto';
export declare class CreateCmsPartnersService extends BaseCmsPartnersService {
    protected readonly partnerRepository: Repository<CmsPartners>;
    protected readonly eventEmitter: EventEmitter2;
    private readonly dataSource;
    constructor(partnerRepository: Repository<CmsPartners>, eventEmitter: EventEmitter2, dataSource: DataSource);
    create(createDto: CreateCmsPartnerDto, userId: string): Promise<CmsPartnerDto>;
    bulkCreate(bulkCreateDto: BulkCreateCmsPartnersDto, userId: string): Promise<BulkOperationResponseDto>;
    createFromTemplate(createFromTemplateDto: CreateCmsPartnerFromTemplateDto, userId: string): Promise<CmsPartnerDto>;
    duplicate(id: string, duplicateDto: DuplicateCmsPartnerDto, createdBy: string): Promise<CmsPartnerDto>;
    import(importDto: ImportCmsPartnersDto, userId: string): Promise<BulkOperationResponseDto>;
}

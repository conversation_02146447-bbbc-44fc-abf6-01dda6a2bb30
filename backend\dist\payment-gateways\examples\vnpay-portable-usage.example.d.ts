import { VnpayPaymentService } from '../services/vnpay-payment.service';
export declare class VnpayPortableUsageExample {
    private readonly vnpayPaymentService;
    constructor(vnpayPaymentService: VnpayPaymentService);
    private setupEventHandler;
    createWalletDepositPayment(userId: string, amount: number, clientIp?: string): Promise<string>;
    createOrderPayment(userId: string, orderId: string, amount: number, orderDescription: string, clientIp: string): Promise<string>;
    queryTransactionStatus(merchantTxnRef: string): Promise<void>;
    getPaymentStatistics(userId?: string): Promise<void>;
    demonstrateCompleteFlow(): Promise<void>;
    private updateWalletBalance;
    handlePaymentWithRetry(userId: string, amount: number, maxRetries?: number): Promise<string>;
}

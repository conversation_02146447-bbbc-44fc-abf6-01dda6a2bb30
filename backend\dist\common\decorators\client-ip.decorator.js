"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.GetClientIp = void 0;
const common_1 = require("@nestjs/common");
exports.GetClientIp = (0, common_1.createParamDecorator)((data, ctx) => {
    const request = ctx.switchToHttp().getRequest();
    const ip = request.headers['x-forwarded-for'] ||
        request.headers['x-real-ip'] ||
        request.headers['x-client-ip'] ||
        request.connection?.remoteAddress ||
        request.socket?.remoteAddress ||
        request.ip ||
        '127.0.0.1';
    return Array.isArray(ip) ? ip[0] : ip.split(',')[0].trim();
});
//# sourceMappingURL=client-ip.decorator.js.map
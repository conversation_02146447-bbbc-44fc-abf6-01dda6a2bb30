"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UpdateCmsCustomerFeedbacksController = void 0;
const openapi = require("@nestjs/swagger");
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const jwt_auth_guard_1 = require("../../common/guards/jwt-auth.guard");
const update_cms_customer_feedbacks_service_1 = require("../services/update.cms-customer-feedbacks.service");
const update_cms_customer_feedback_dto_1 = require("../dto/update.cms-customer-feedback.dto");
const cms_customer_feedbacks_entity_1 = require("../entity/cms-customer-feedbacks.entity");
const api_response_dto_1 = require("../../common/dto/api-response.dto");
const get_user_decorator_1 = require("../../common/decorators/get-user.decorator");
const roles_decorator_1 = require("../../common/decorators/roles.decorator");
const permissions_decorator_1 = require("../../common/decorators/permissions.decorator");
let UpdateCmsCustomerFeedbacksController = class UpdateCmsCustomerFeedbacksController {
    cmsCustomerFeedbacksService;
    constructor(cmsCustomerFeedbacksService) {
        this.cmsCustomerFeedbacksService = cmsCustomerFeedbacksService;
    }
    async update(id, updateCmsCustomerFeedbackDto, userId) {
        return this.cmsCustomerFeedbacksService.update(id, updateCmsCustomerFeedbackDto, userId);
    }
    async bulkUpdate(updates, userId) {
        return this.cmsCustomerFeedbacksService.bulkUpdate(updates, userId);
    }
    async approve(id, userId) {
        return this.cmsCustomerFeedbacksService.approve(id, userId);
    }
    async reject(id, userId) {
        return this.cmsCustomerFeedbacksService.reject(id, userId);
    }
    async updateStatus(id, status, userId) {
        return this.cmsCustomerFeedbacksService.updateStatus(id, status, userId);
    }
    async bulkApprove(ids, userId) {
        return this.cmsCustomerFeedbacksService.bulkApprove(ids, userId);
    }
    async bulkReject(ids, userId) {
        return this.cmsCustomerFeedbacksService.bulkReject(ids, userId);
    }
};
exports.UpdateCmsCustomerFeedbacksController = UpdateCmsCustomerFeedbacksController;
__decorate([
    (0, common_1.Patch)(':id'),
    (0, roles_decorator_1.Roles)('ADMIN', 'EDITOR'),
    (0, permissions_decorator_1.Permissions)('cms-customer-feedback:update'),
    (0, swagger_1.ApiOperation)({ summary: 'Cập nhật thông tin feedback khách hàng CMS' }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Feedback khách hàng CMS đã được cập nhật thành công.',
        type: api_response_dto_1.ApiResponseDto,
        schema: {
            properties: {
                data: { $ref: '#/components/schemas/CmsCustomerFeedbackDto' },
            },
        },
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.NOT_FOUND,
        description: 'Không tìm thấy feedback khách hàng CMS.',
        type: api_response_dto_1.ApiResponseDto,
        schema: { properties: { data: { type: 'object', nullable: true } } },
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.BAD_REQUEST,
        description: 'Dữ liệu đầu vào không hợp lệ.',
        type: api_response_dto_1.ApiResponseDto,
        schema: { properties: { data: { type: 'object', nullable: true } } },
    }),
    (0, swagger_1.ApiParam)({ name: 'id', type: String, description: 'ID của feedback khách hàng CMS' }),
    (0, swagger_1.ApiBody)({ type: update_cms_customer_feedback_dto_1.UpdateCmsCustomerFeedbackDto }),
    openapi.ApiResponse({ status: 200, type: Object }),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __param(1, (0, common_1.Body)()),
    __param(2, (0, get_user_decorator_1.GetUser)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, update_cms_customer_feedback_dto_1.UpdateCmsCustomerFeedbackDto, String]),
    __metadata("design:returntype", Promise)
], UpdateCmsCustomerFeedbacksController.prototype, "update", null);
__decorate([
    (0, common_1.Patch)('bulk'),
    (0, roles_decorator_1.Roles)('ADMIN'),
    (0, permissions_decorator_1.Permissions)('cms-customer-feedback:update'),
    (0, swagger_1.ApiOperation)({ summary: 'Cập nhật nhiều feedback khách hàng CMS cùng lúc' }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Các feedback khách hàng CMS đã được cập nhật thành công.',
        type: api_response_dto_1.ApiResponseDto,
        schema: {
            properties: {
                data: {
                    type: 'array',
                    items: { $ref: '#/components/schemas/CmsCustomerFeedbackDto' },
                },
            },
        },
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.BAD_REQUEST,
        description: 'Dữ liệu đầu vào không hợp lệ.',
        type: api_response_dto_1.ApiResponseDto,
        schema: { properties: { data: { type: 'object', nullable: true } } },
    }),
    (0, swagger_1.ApiBody)({
        schema: {
            type: 'array',
            items: {
                type: 'object',
                properties: {
                    id: { type: 'string', format: 'uuid' },
                    data: { $ref: '#/components/schemas/UpdateCmsCustomerFeedbackDto' },
                },
                required: ['id', 'data'],
            },
        },
    }),
    openapi.ApiResponse({ status: 200, type: [require("../dto/cms-customer-feedback.dto").CmsCustomerFeedbackDto] }),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, get_user_decorator_1.GetUser)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Array, String]),
    __metadata("design:returntype", Promise)
], UpdateCmsCustomerFeedbacksController.prototype, "bulkUpdate", null);
__decorate([
    (0, common_1.Patch)(':id/approve'),
    (0, roles_decorator_1.Roles)('ADMIN', 'EDITOR'),
    (0, permissions_decorator_1.Permissions)('cms-customer-feedback:update'),
    (0, swagger_1.ApiOperation)({ summary: 'Duyệt feedback khách hàng CMS' }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Feedback khách hàng CMS đã được duyệt thành công.',
        type: api_response_dto_1.ApiResponseDto,
        schema: {
            properties: {
                data: { $ref: '#/components/schemas/CmsCustomerFeedbackDto' },
            },
        },
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.NOT_FOUND,
        description: 'Không tìm thấy feedback khách hàng CMS.',
        type: api_response_dto_1.ApiResponseDto,
        schema: { properties: { data: { type: 'object', nullable: true } } },
    }),
    (0, swagger_1.ApiParam)({ name: 'id', type: String, description: 'ID của feedback khách hàng CMS' }),
    openapi.ApiResponse({ status: 200, type: Object }),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __param(1, (0, get_user_decorator_1.GetUser)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String]),
    __metadata("design:returntype", Promise)
], UpdateCmsCustomerFeedbacksController.prototype, "approve", null);
__decorate([
    (0, common_1.Patch)(':id/reject'),
    (0, roles_decorator_1.Roles)('ADMIN', 'EDITOR'),
    (0, permissions_decorator_1.Permissions)('cms-customer-feedback:update'),
    (0, swagger_1.ApiOperation)({ summary: 'Từ chối feedback khách hàng CMS' }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Feedback khách hàng CMS đã được từ chối.',
        type: api_response_dto_1.ApiResponseDto,
        schema: {
            properties: {
                data: { $ref: '#/components/schemas/CmsCustomerFeedbackDto' },
            },
        },
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.NOT_FOUND,
        description: 'Không tìm thấy feedback khách hàng CMS.',
        type: api_response_dto_1.ApiResponseDto,
        schema: { properties: { data: { type: 'object', nullable: true } } },
    }),
    (0, swagger_1.ApiParam)({ name: 'id', type: String, description: 'ID của feedback khách hàng CMS' }),
    openapi.ApiResponse({ status: 200, type: Object }),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __param(1, (0, get_user_decorator_1.GetUser)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String]),
    __metadata("design:returntype", Promise)
], UpdateCmsCustomerFeedbacksController.prototype, "reject", null);
__decorate([
    (0, common_1.Patch)(':id/status'),
    (0, roles_decorator_1.Roles)('ADMIN', 'EDITOR'),
    (0, permissions_decorator_1.Permissions)('cms-customer-feedback:update'),
    (0, swagger_1.ApiOperation)({ summary: 'Cập nhật trạng thái feedback khách hàng CMS' }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Trạng thái feedback khách hàng CMS đã được cập nhật.',
        type: api_response_dto_1.ApiResponseDto,
        schema: {
            properties: {
                data: { $ref: '#/components/schemas/CmsCustomerFeedbackDto' },
            },
        },
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.NOT_FOUND,
        description: 'Không tìm thấy feedback khách hàng CMS.',
        type: api_response_dto_1.ApiResponseDto,
        schema: { properties: { data: { type: 'object', nullable: true } } },
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.BAD_REQUEST,
        description: 'Trạng thái không hợp lệ.',
        type: api_response_dto_1.ApiResponseDto,
        schema: { properties: { data: { type: 'object', nullable: true } } },
    }),
    (0, swagger_1.ApiParam)({ name: 'id', type: String, description: 'ID của feedback khách hàng CMS' }),
    (0, swagger_1.ApiBody)({
        schema: {
            type: 'object',
            properties: {
                status: {
                    type: 'string',
                    enum: Object.values(cms_customer_feedbacks_entity_1.CmsCustomerFeedbackStatus),
                    description: 'Trạng thái mới',
                },
            },
            required: ['status'],
        },
    }),
    openapi.ApiResponse({ status: 200, type: Object }),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __param(1, (0, common_1.Body)('status')),
    __param(2, (0, get_user_decorator_1.GetUser)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String, String]),
    __metadata("design:returntype", Promise)
], UpdateCmsCustomerFeedbacksController.prototype, "updateStatus", null);
__decorate([
    (0, common_1.Patch)('bulk/approve'),
    (0, roles_decorator_1.Roles)('ADMIN', 'EDITOR'),
    (0, permissions_decorator_1.Permissions)('cms-customer-feedback:update'),
    (0, swagger_1.ApiOperation)({ summary: 'Duyệt nhiều feedback khách hàng CMS cùng lúc' }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Các feedback khách hàng CMS đã được duyệt thành công.',
        type: api_response_dto_1.ApiResponseDto,
        schema: {
            properties: {
                data: {
                    type: 'array',
                    items: { $ref: '#/components/schemas/CmsCustomerFeedbackDto' },
                },
            },
        },
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.BAD_REQUEST,
        description: 'Dữ liệu đầu vào không hợp lệ.',
        type: api_response_dto_1.ApiResponseDto,
        schema: { properties: { data: { type: 'object', nullable: true } } },
    }),
    (0, swagger_1.ApiBody)({
        schema: {
            type: 'object',
            properties: {
                ids: {
                    type: 'array',
                    items: { type: 'string', format: 'uuid' },
                    description: 'Danh sách ID của các feedback cần duyệt',
                },
            },
            required: ['ids'],
        },
    }),
    openapi.ApiResponse({ status: 200, type: [require("../dto/cms-customer-feedback.dto").CmsCustomerFeedbackDto] }),
    __param(0, (0, common_1.Body)('ids')),
    __param(1, (0, get_user_decorator_1.GetUser)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Array, String]),
    __metadata("design:returntype", Promise)
], UpdateCmsCustomerFeedbacksController.prototype, "bulkApprove", null);
__decorate([
    (0, common_1.Patch)('bulk/reject'),
    (0, roles_decorator_1.Roles)('ADMIN', 'EDITOR'),
    (0, permissions_decorator_1.Permissions)('cms-customer-feedback:update'),
    (0, swagger_1.ApiOperation)({ summary: 'Từ chối nhiều feedback khách hàng CMS cùng lúc' }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Các feedback khách hàng CMS đã được từ chối.',
        type: api_response_dto_1.ApiResponseDto,
        schema: {
            properties: {
                data: {
                    type: 'array',
                    items: { $ref: '#/components/schemas/CmsCustomerFeedbackDto' },
                },
            },
        },
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.BAD_REQUEST,
        description: 'Dữ liệu đầu vào không hợp lệ.',
        type: api_response_dto_1.ApiResponseDto,
        schema: { properties: { data: { type: 'object', nullable: true } } },
    }),
    (0, swagger_1.ApiBody)({
        schema: {
            type: 'object',
            properties: {
                ids: {
                    type: 'array',
                    items: { type: 'string', format: 'uuid' },
                    description: 'Danh sách ID của các feedback cần từ chối',
                },
            },
            required: ['ids'],
        },
    }),
    openapi.ApiResponse({ status: 200, type: [require("../dto/cms-customer-feedback.dto").CmsCustomerFeedbackDto] }),
    __param(0, (0, common_1.Body)('ids')),
    __param(1, (0, get_user_decorator_1.GetUser)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Array, String]),
    __metadata("design:returntype", Promise)
], UpdateCmsCustomerFeedbacksController.prototype, "bulkReject", null);
exports.UpdateCmsCustomerFeedbacksController = UpdateCmsCustomerFeedbacksController = __decorate([
    (0, swagger_1.ApiTags)('cms-customer-feedbacks'),
    (0, swagger_1.ApiBearerAuth)(),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, common_1.Controller)('cms/customer-feedbacks'),
    __metadata("design:paramtypes", [update_cms_customer_feedbacks_service_1.UpdateCmsCustomerFeedbacksService])
], UpdateCmsCustomerFeedbacksController);
//# sourceMappingURL=update.cms-customer-feedbacks.controller.js.map
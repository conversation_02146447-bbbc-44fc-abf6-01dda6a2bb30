import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { HttpService } from '@nestjs/axios';
import * as crypto from 'crypto';
import { firstValueFrom } from 'rxjs';

// Interface yêu cầu thanh toán VNPAY
export interface VnpayPaymentRequest {
  userId?: string; // ID người dùng
  walletId?: string; // ID ví (tùy chọn)
  amount: number; // Số tiền thanh toán (VND)
  description: string; // Mô tả giao dịch
  ipAddress: string; // Địa chỉ IP khách hàng
  bankCode?: string; // Mã ngân hàng (tùy chọn)
  locale?: string; // Ngôn ngữ (vn/en)
  orderType?: string; // Loại đơn hàng
}

// Interface tham số tạo thanh toán VNPAY
export interface VnpayCreatePaymentParams {
  vnp_Version: string; // Phiên bản API
  vnp_Command: string; // Lệnh API (pay)
  vnp_TmnCode: string; // Mã merchant
  vnp_Amount: number; // Số tiền (đã nhân 100)
  vnp_CurrCode: string; // Mã tiền tệ (VND)
  vnp_TxnRef: string; // Mã giao dịch merchant
  vnp_OrderInfo: string; // Thông tin đơn hàng
  vnp_OrderType: string; // Loại đơn hàng
  vnp_Locale: string; // Ngôn ngữ
  vnp_ReturnUrl: string; // URL trả về
  vnp_IpAddr: string; // IP khách hàng
  vnp_CreateDate: string; // Thời gian tạo
  vnp_ExpireDate?: string; // Thời gian hết hạn
  vnp_BankCode?: string; // Mã ngân hàng
}

// Interface yêu cầu truy vấn giao dịch VNPAY
export interface VnpayQueryRequest {
  vnp_RequestId: string; // ID yêu cầu
  vnp_Version: string; // Phiên bản API
  vnp_Command: string; // Lệnh API (querydr)
  vnp_TmnCode: string; // Mã merchant
  vnp_TxnRef: string; // Mã giao dịch merchant
  vnp_OrderInfo: string; // Thông tin đơn hàng
  vnp_TransactionDate: string; // Ngày giao dịch
  vnp_CreateDate: string; // Thời gian tạo yêu cầu
  vnp_IpAddr: string; // IP merchant
  vnp_TransactionNo?: string; // Số giao dịch VNPAY
  vnp_SecureHash: string; // Chữ ký
}

// Interface yêu cầu hoàn tiền VNPAY
export interface VnpayRefundRequest {
  vnp_RequestId: string; // ID yêu cầu
  vnp_Version: string; // Phiên bản API
  vnp_Command: string; // Lệnh API (refund)
  vnp_TmnCode: string; // Mã merchant
  vnp_TransactionType: string; // Loại giao dịch hoàn tiền
  vnp_TxnRef: string; // Mã giao dịch gốc
  vnp_Amount: number; // Số tiền hoàn
  vnp_OrderInfo: string; // Lý do hoàn tiền
  vnp_TransactionNo?: string; // Số giao dịch VNPAY gốc
  vnp_TransactionDate: string; // Ngày giao dịch gốc
  vnp_CreateBy: string; // Người tạo yêu cầu
  vnp_CreateDate: string; // Thời gian tạo yêu cầu
  vnp_IpAddr: string; // IP merchant
  vnp_SecureHash: string; // Chữ ký
}

// Service xử lý API VNPAY
@Injectable()
export class VnpayService {
  private readonly logger = new Logger(VnpayService.name);
  private readonly tmnCode: string; // Mã merchant VNPAY
  private readonly hashSecret: string; // Khóa bí mật VNPAY
  private readonly vnpayUrl: string; // URL thanh toán VNPAY
  private readonly vnpayApiUrl: string; // URL API VNPAY
  private readonly returnUrl: string; // URL trả về sau thanh toán
  private readonly ipnUrl: string; // URL nhận thông báo IPN

  constructor(
    private readonly configService: ConfigService,
    private readonly httpService: HttpService,
  ) {
    // Khởi tạo cấu hình từ environment variables
    this.tmnCode = this.configService.get<string>('VNPAY_TMN_CODE') || '';
    this.hashSecret = this.configService.get<string>('VNPAY_HASH_SECRET') || '';
    this.vnpayUrl =
      this.configService.get<string>('VNPAY_URL') ||
      'https://sandbox.vnpayment.vn/paymentv2/vpcpay.html';
    this.vnpayApiUrl =
      this.configService.get<string>('VNPAY_API_URL') ||
      'https://sandbox.vnpayment.vn/merchant_webapi/api/transaction';
    this.returnUrl = this.configService.get<string>('VNPAY_RETURN_URL') || '';
    this.ipnUrl = this.configService.get<string>('VNPAY_IPN_URL') || '';
  }

  // Tạo URL thanh toán VNPAY
  async createPaymentUrl(
    paymentData: VnpayPaymentRequest,
  ): Promise<{ paymentUrl: string; transactionId: string }> {
    try {
      // Kiểm tra cấu hình VNPAY
      if (!this.tmnCode || !this.hashSecret || !this.returnUrl) {
        throw new Error(
          'VNPAY configuration is missing. Please check TMN_CODE, HASH_SECRET, and RETURN_URL.',
        );
      }

      // Kiểm tra dữ liệu thanh toán
      if (
        !paymentData.userId ||
        !paymentData.amount ||
        paymentData.amount <= 0
      ) {
        throw new Error(
          'Invalid payment data. UserId and positive amount are required.',
        );
      }

      // Tạo các thông tin giao dịch
      const date = new Date();
      const createDate = this.formatDate(date);
      const orderId = `${paymentData.userId}_${Date.now()}`;
      const transactionId = orderId;
      const expireDate = new Date(date.getTime() + 15 * 60 * 1000); // Hết hạn sau 15 phút
      const vnp_ExpireDate = this.formatDate(expireDate);

      // Tạo tham số thanh toán VNPAY
      const vnpParams: VnpayCreatePaymentParams = {
        vnp_Version: '2.1.0',
        vnp_Command: 'pay',
        vnp_TmnCode: this.tmnCode,
        vnp_Amount: paymentData.amount * 100, // VNPAY yêu cầu nhân 100
        vnp_CurrCode: 'VND',
        vnp_TxnRef: orderId,
        vnp_OrderInfo: this.sanitizeOrderInfo(
          paymentData.description || `Thanh toan don hang ${orderId}`,
        ),
        vnp_OrderType: paymentData.orderType || 'other',
        vnp_Locale: paymentData.locale || 'vn',
        vnp_ReturnUrl: this.returnUrl,
        vnp_IpAddr: paymentData.ipAddress || '127.0.0.1',
        vnp_CreateDate: createDate,
        vnp_ExpireDate: vnp_ExpireDate,
      };

      // Thêm IPN URL nếu có cấu hình
      if (this.ipnUrl) {
        this.logger.debug(`IPN URL configured: ${this.ipnUrl}`);
      }

      // Thêm mã ngân hàng nếu có
      if (paymentData.bankCode) {
        vnpParams.vnp_BankCode = paymentData.bankCode;
      }

      // Tạo chữ ký và URL thanh toán
      const sortedParams = this.sortObject(vnpParams);
      const signData = this.createSignData(sortedParams);
      const vnp_SecureHash = this.createSecureHash(signData);
      sortedParams['vnp_SecureHash'] = vnp_SecureHash;

      const paymentUrl =
        this.vnpayUrl + '?' + this.createQueryString(sortedParams);

      this.logger.log(`Đã tạo URL thanh toán VNPAY cho đơn hàng: ${orderId}`);
      this.logger.debug(`Payment URL: ${paymentUrl}`);

      return { paymentUrl, transactionId };
    } catch (error) {
      this.logger.error(
        `Lỗi khi tạo URL thanh toán VNPAY: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  // Xác thực callback từ VNPAY (Return URL và IPN)
  async verifyReturnUrl(params: Record<string, string>): Promise<{
    isValid: boolean;
    transactionId?: string;
    amount?: number;
    responseCode?: string;
    transactionStatus?: string;
    message?: string;
    vnpayTransactionNo?: string;
    bankCode?: string;
    cardType?: string;
    payDate?: string;
  }> {
    try {
      // Lấy các thông tin từ callback
      const secureHash = params['vnp_SecureHash'];
      const orderId = params['vnp_TxnRef'];
      const responseCode = params['vnp_ResponseCode'];
      const transactionStatus = params['vnp_TransactionStatus'];
      const vnpayTransactionNo = params['vnp_TransactionNo'];
      const bankCode = params['vnp_BankCode'];
      const cardType = params['vnp_CardType'];
      const payDate = params['vnp_PayDate'];

      // Kiểm tra thông tin bắt buộc
      if (!secureHash || !orderId) {
        return {
          isValid: false,
          message: 'Thiếu thông tin bắt buộc từ VNPAY',
        };
      }

      // Tạo bản sao để xử lý
      const verifyParams = { ...params };

      // Xóa chữ ký để tạo lại và so sánh
      delete verifyParams['vnp_SecureHash'];
      delete verifyParams['vnp_SecureHashType'];

      // Sắp xếp các tham số theo thứ tự a-z
      const sortedParams = this.sortObject(verifyParams);

      // Tạo chuỗi ký
      const signData = this.createSignData(sortedParams);

      // Tạo chữ ký để so sánh
      const calculatedHash = this.createSecureHash(signData);

      // So sánh chữ ký
      const isValidSignature = secureHash === calculatedHash;

      if (!isValidSignature) {
        this.logger.warn(`Chữ ký không hợp lệ cho đơn hàng: ${orderId}`);
        this.logger.debug(
          `Expected: ${calculatedHash}, Received: ${secureHash}`,
        );

        return {
          isValid: false,
          transactionId: orderId,
          responseCode,
          message: 'Chữ ký không hợp lệ',
        };
      }

      // Chữ ký hợp lệ, kiểm tra kết quả thanh toán
      const amount = parseInt(params['vnp_Amount']) / 100; // Chuyển về đơn vị tiền tệ
      const isSuccessful = responseCode === '00' && transactionStatus === '00';

      if (isSuccessful) {
        this.logger.log(
          `Xác thực thanh toán VNPAY thành công - Đơn hàng: ${orderId}, Số tiền: ${amount}, VNPAY TxnNo: ${vnpayTransactionNo}`,
        );

        return {
          isValid: true,
          transactionId: orderId,
          amount,
          responseCode,
          transactionStatus,
          vnpayTransactionNo,
          bankCode,
          cardType,
          payDate,
          message: 'Thanh toán thành công',
        };
      } else {
        this.logger.warn(
          `Thanh toán không thành công - Đơn hàng: ${orderId}, ResponseCode: ${responseCode}, TransactionStatus: ${transactionStatus}`,
        );

        return {
          isValid: true, // Chữ ký hợp lệ nhưng thanh toán không thành công
          transactionId: orderId,
          amount,
          responseCode,
          transactionStatus,
          vnpayTransactionNo,
          bankCode,
          cardType,
          payDate,
          message: this.getErrorMessage(responseCode),
        };
      }
    } catch (error) {
      this.logger.error(
        `Lỗi khi xác thực callback VNPAY: ${error.message}`,
        error.stack,
      );

      return {
        isValid: false,
        message: `Lỗi xử lý: ${error.message}`,
      };
    }
  }

  // Xử lý thông báo IPN từ VNPAY
  async handleIpnCallback(params: Record<string, string>): Promise<{
    isValid: boolean;
    transactionId?: string;
    amount?: number;
    message?: string;
  }> {
    // IPN xử lý tương tự như verifyReturnUrl
    const result = await this.verifyReturnUrl(params);

    // Log cho IPN
    if (result.isValid) {
      this.logger.log(
        `IPN - Nhận thông báo thanh toán: ${result.transactionId}, Status: ${result.responseCode}`,
      );
    } else {
      this.logger.error(`IPN - Thông báo không hợp lệ: ${result.message}`);
    }

    return {
      isValid: result.isValid,
      transactionId: result.transactionId,
      amount: result.amount,
      message: result.message,
    };
  }

  // Tạo response cho IPN callback
  async createIpnResponse(params: Record<string, string>): Promise<{
    RspCode: string;
    Message: string;
  }> {
    try {
      const verifyResult = await this.verifyReturnUrl(params);

      if (!verifyResult.isValid) {
        this.logger.error(
          `IPN Response - Chữ ký không hợp lệ: ${verifyResult.message}`,
        );
        return {
          RspCode: '97',
          Message: 'Invalid signature',
        };
      }

      const responseCode = verifyResult.responseCode;
      const transactionStatus = verifyResult.transactionStatus;

      if (responseCode === '00' && transactionStatus === '00') {
        this.logger.log(
          `IPN Response - Giao dịch thành công: ${verifyResult.transactionId}`,
        );

        return {
          RspCode: '00',
          Message: 'Confirm Success',
        };
      } else {
        this.logger.warn(
          `IPN Response - Giao dịch không thành công: ${verifyResult.transactionId}, Code: ${responseCode}`,
        );

        return {
          RspCode: '00',
          Message: 'Transaction failed but confirmed',
        };
      }
    } catch (error) {
      this.logger.error(
        `IPN Response - Lỗi xử lý: ${error.message}`,
        error.stack,
      );
      return {
        RspCode: '99',
        Message: 'Unknown error',
      };
    }
  }

  // Định dạng thời gian theo GMT+7 (yyyyMMddHHmmss)
  private formatDate(date: Date): string {
    const vietnamTime = new Date(
      date.toLocaleString('en-US', { timeZone: 'Asia/Ho_Chi_Minh' }),
    );

    const year = vietnamTime.getFullYear();
    const month = String(vietnamTime.getMonth() + 1).padStart(2, '0');
    const day = String(vietnamTime.getDate()).padStart(2, '0');
    const hours = String(vietnamTime.getHours()).padStart(2, '0');
    const minutes = String(vietnamTime.getMinutes()).padStart(2, '0');
    const seconds = String(vietnamTime.getSeconds()).padStart(2, '0');

    return `${year}${month}${day}${hours}${minutes}${seconds}`;
  }

  // Làm sạch thông tin đơn hàng (loại bỏ dấu và ký tự đặc biệt)
  private sanitizeOrderInfo(orderInfo: string): string {
    return orderInfo
      .normalize('NFD')
      .replace(/[\u0300-\u036f]/g, '') // Loại bỏ dấu
      .replace(/[^a-zA-Z0-9\s]/g, '') // Loại bỏ ký tự đặc biệt
      .replace(/\s+/g, ' ') // Loại bỏ khoảng trắng thừa
      .trim()
      .substring(0, 255); // Giới hạn độ dài
  }

  // Tạo chuỗi ký cho checksum
  private createSignData(params: Record<string, any>): string {
    const result: string[] = [];

    for (const key in params) {
      if (
        params.hasOwnProperty(key) &&
        params[key] !== null &&
        params[key] !== undefined &&
        params[key] !== ''
      ) {
        result.push(
          `${encodeURIComponent(key)}=${encodeURIComponent(params[key])}`,
        );
      }
    }

    return result.join('&');
  }

  // Tạo chữ ký HMAC SHA512
  private createSecureHash(data: string): string {
    const hmac = crypto.createHmac('sha512', this.hashSecret);
    return hmac.update(Buffer.from(data, 'utf-8')).digest('hex');
  }

  // Tạo query string cho URL
  private createQueryString(params: Record<string, any>): string {
    const result: string[] = [];

    for (const key in params) {
      if (
        params.hasOwnProperty(key) &&
        params[key] !== null &&
        params[key] !== undefined &&
        params[key] !== ''
      ) {
        result.push(
          `${encodeURIComponent(key)}=${encodeURIComponent(params[key])}`,
        );
      }
    }

    return result.join('&');
  }

  // Sắp xếp các tham số theo thứ tự a-z
  private sortObject(obj: Record<string, any>): Record<string, any> {
    const sorted: Record<string, any> = {};
    const keys = Object.keys(obj).sort();

    for (const key of keys) {
      if (obj[key] !== null && obj[key] !== undefined && obj[key] !== '') {
        sorted[key] = obj[key];
      }
    }

    return sorted;
  }

  // Truy vấn kết quả giao dịch từ VNPAY
  async queryTransaction(queryData: {
    txnRef: string;
    transactionDate: string;
    transactionNo?: string;
    orderInfo: string;
    ipAddr: string;
  }): Promise<any> {
    try {
      // Kiểm tra tham số bắt buộc
      if (
        !queryData.txnRef ||
        !queryData.transactionDate ||
        !queryData.orderInfo ||
        !queryData.ipAddr
      ) {
        throw new Error('Missing required parameters for query transaction');
      }

      // Kiểm tra định dạng ngày (yyyyMMddHHmmss)
      if (!/^\d{14}$/.test(queryData.transactionDate)) {
        throw new Error(
          'Invalid transactionDate format. Expected: yyyyMMddHHmmss',
        );
      }

      const requestId = this.generateRequestId();
      const createDate = this.formatDate(new Date());

      const queryParams: VnpayQueryRequest = {
        vnp_RequestId: requestId,
        vnp_Version: '2.1.0',
        vnp_Command: 'querydr',
        vnp_TmnCode: this.tmnCode,
        vnp_TxnRef: queryData.txnRef,
        vnp_OrderInfo: queryData.orderInfo,
        vnp_TransactionDate: queryData.transactionDate,
        vnp_CreateDate: createDate,
        vnp_IpAddr: queryData.ipAddr,
        vnp_SecureHash: '',
      };

      if (queryData.transactionNo) {
        queryParams.vnp_TransactionNo = queryData.transactionNo;
      }

      // Tạo checksum theo quy tắc VNPAY
      const hashData = [
        requestId,
        '2.1.0',
        'querydr',
        this.tmnCode,
        queryData.txnRef,
        queryData.transactionDate,
        createDate,
        queryData.ipAddr,
        queryData.orderInfo,
      ].join('|');

      queryParams.vnp_SecureHash = this.createSecureHash(hashData);

      const response = await this.sendApiRequest(queryParams);

      this.logger.log(
        `Query transaction result for ${queryData.txnRef}: ${JSON.stringify(response)}`,
      );

      return response;
    } catch (error) {
      this.logger.error(
        `Lỗi khi truy vấn giao dịch: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  // Yêu cầu hoàn tiền từ VNPAY
  async refundTransaction(refundData: {
    txnRef: string;
    amount: number;
    orderInfo: string;
    transactionNo?: string;
    transactionDate: string;
    transactionType: '02' | '03'; // 02: toàn phần, 03: một phần
    createBy: string;
    ipAddr: string;
  }): Promise<any> {
    try {
      // Kiểm tra tham số bắt buộc
      if (
        !refundData.txnRef ||
        !refundData.amount ||
        refundData.amount <= 0 ||
        !refundData.orderInfo ||
        !refundData.transactionDate ||
        !refundData.createBy ||
        !refundData.ipAddr
      ) {
        throw new Error('Missing required parameters for refund transaction');
      }

      // Kiểm tra định dạng ngày
      if (!/^\d{14}$/.test(refundData.transactionDate)) {
        throw new Error(
          'Invalid transactionDate format. Expected: yyyyMMddHHmmss',
        );
      }

      // Kiểm tra loại hoàn tiền
      if (!['02', '03'].includes(refundData.transactionType)) {
        throw new Error(
          'Invalid transactionType. Must be "02" (full) or "03" (partial)',
        );
      }

      const requestId = this.generateRequestId();
      const createDate = this.formatDate(new Date());

      const refundParams: VnpayRefundRequest = {
        vnp_RequestId: requestId,
        vnp_Version: '2.1.0',
        vnp_Command: 'refund',
        vnp_TmnCode: this.tmnCode,
        vnp_TransactionType: refundData.transactionType,
        vnp_TxnRef: refundData.txnRef,
        vnp_Amount: refundData.amount * 100,
        vnp_OrderInfo: refundData.orderInfo,
        vnp_TransactionDate: refundData.transactionDate,
        vnp_CreateBy: refundData.createBy,
        vnp_CreateDate: createDate,
        vnp_IpAddr: refundData.ipAddr,
        vnp_SecureHash: '',
      };

      if (refundData.transactionNo) {
        refundParams.vnp_TransactionNo = refundData.transactionNo;
      }

      // Tạo checksum theo quy tắc VNPAY
      const hashData = [
        requestId,
        '2.1.0',
        'refund',
        this.tmnCode,
        refundData.transactionType,
        refundData.txnRef,
        refundData.amount * 100,
        refundData.transactionNo || '',
        refundData.transactionDate,
        refundData.createBy,
        createDate,
        refundData.ipAddr,
        refundData.orderInfo,
      ].join('|');

      refundParams.vnp_SecureHash = this.createSecureHash(hashData);

      const response = await this.sendApiRequest(refundParams);

      this.logger.log(
        `Refund transaction result for ${refundData.txnRef}: ${JSON.stringify(response)}`,
      );

      return response;
    } catch (error) {
      this.logger.error(`Lỗi khi hoàn tiền: ${error.message}`, error.stack);
      throw error;
    }
  }

  // Gửi API request đến VNPAY
  private async sendApiRequest(params: any): Promise<any> {
    try {
      const response = await firstValueFrom(
        this.httpService.post(this.vnpayApiUrl, params, {
          headers: {
            'Content-Type': 'application/json',
          },
          timeout: 30000,
        }),
      );

      return response.data;
    } catch (error) {
      this.logger.error(
        `Lỗi khi gửi API request đến VNPAY: ${error.message}`,
        error.stack,
      );

      if (error.response) {
        this.logger.error(`Response status: ${error.response.status}`);
        this.logger.error(
          `Response data: ${JSON.stringify(error.response.data)}`,
        );
      }

      throw error;
    }
  }

  // Tạo ID yêu cầu duy nhất
  private generateRequestId(): string {
    const timestamp = Date.now().toString();
    const random = Math.random().toString(36).substring(2, 8).toUpperCase();
    const requestId = `REQ${timestamp}${random}`;
    return requestId.substring(0, 32);
  }

  // Lấy thông báo lỗi theo mã response code
  private getErrorMessage(responseCode: string): string {
    const errorMessages: Record<string, string> = {
      '00': 'Giao dịch thành công',
      '07': 'Trừ tiền thành công. Giao dịch bị nghi ngờ (liên quan tới lừa đảo, giao dịch bất thường)',
      '09': 'Giao dịch không thành công do: Thẻ/Tài khoản của khách hàng chưa đăng ký dịch vụ InternetBanking tại ngân hàng',
      '10': 'Giao dịch không thành công do: Khách hàng xác thực thông tin thẻ/tài khoản không đúng quá 3 lần',
      '11': 'Giao dịch không thành công do: Đã hết hạn chờ thanh toán. Xin quý khách vui lòng thực hiện lại giao dịch',
      '12': 'Giao dịch không thành công do: Thẻ/Tài khoản của khách hàng bị khóa',
      '13': 'Giao dịch không thành công do Quý khách nhập sai mật khẩu xác thực giao dịch (OTP)',
      '24': 'Giao dịch không thành công do: Khách hàng hủy giao dịch',
      '51': 'Giao dịch không thành công do: Tài khoản của quý khách không đủ số dư để thực hiện giao dịch',
      '65': 'Giao dịch không thành công do: Tài khoản của Quý khách đã vượt quá hạn mức giao dịch trong ngày',
      '75': 'Ngân hàng thanh toán đang bảo trì',
      '79': 'Giao dịch không thành công do: KH nhập sai mật khẩu thanh toán quá số lần quy định',
      '99': 'Các lỗi khác (lỗi còn lại, không có trong danh sách mã lỗi đã liệt kê)',
    };

    return errorMessages[responseCode] || `Lỗi không xác định: ${responseCode}`;
  }
}

import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { HttpService } from '@nestjs/axios';
import * as crypto from 'crypto';
import { firstValueFrom } from 'rxjs';
import { IPaymentGateway } from '../interfaces/payment-gateway.interface';
import { CreatePaymentDto } from '../dto/create-payment.dto';

// Interfaces cho VNPAY
export interface VnpayCreatePaymentParams {
  vnp_Version: string;
  vnp_Command: string;
  vnp_TmnCode: string;
  vnp_Amount: number;
  vnp_CurrCode: string;
  vnp_TxnRef: string;
  vnp_OrderInfo: string;
  vnp_OrderType: string;
  vnp_Locale: string;
  vnp_ReturnUrl: string;
  vnp_IpAddr: string;
  vnp_CreateDate: string;
  vnp_ExpireDate?: string;
  vnp_BankCode?: string;
}

export interface VnpayQueryRequest {
  vnp_RequestId: string;
  vnp_Version: string;
  vnp_Command: string;
  vnp_TmnCode: string;
  vnp_TxnRef: string;
  vnp_OrderInfo: string;
  vnp_TransactionDate: string;
  vnp_CreateDate: string;
  vnp_IpAddr: string;
  vnp_TransactionNo?: string;
  vnp_SecureHash: string;
}

export interface VnpayRefundRequest {
  vnp_RequestId: string;
  vnp_Version: string;
  vnp_Command: string;
  vnp_TmnCode: string;
  vnp_TransactionType: string;
  vnp_TxnRef: string;
  vnp_Amount: number;
  vnp_OrderInfo: string;
  vnp_TransactionNo?: string;
  vnp_TransactionDate: string;
  vnp_CreateBy: string;
  vnp_CreateDate: string;
  vnp_IpAddr: string;
  vnp_SecureHash: string;
}

@Injectable()
export class VnpayService implements IPaymentGateway {
  private readonly logger = new Logger(VnpayService.name);
  private readonly tmnCode: string;
  private readonly hashSecret: string;
  private readonly vnpayUrl: string;
  private readonly vnpayApiUrl: string;
  private readonly returnUrl: string;
  private readonly ipnUrl: string;

  constructor(
    private readonly configService: ConfigService,
    private readonly httpService: HttpService,
  ) {
    this.tmnCode = this.configService.get<string>('VNPAY_TMN_CODE') || '';
    this.hashSecret = this.configService.get<string>('VNPAY_HASH_SECRET') || '';
    this.vnpayUrl =
      this.configService.get<string>('VNPAY_URL') ||
      'https://sandbox.vnpayment.vn/paymentv2/vpcpay.html';
    this.vnpayApiUrl =
      this.configService.get<string>('VNPAY_API_URL') ||
      'https://sandbox.vnpayment.vn/merchant_webapi/api/transaction';
    this.returnUrl = this.configService.get<string>('VNPAY_RETURN_URL') || '';
    this.ipnUrl = this.configService.get<string>('VNPAY_IPN_URL') || '';
  }

  /**
   * Tạo URL thanh toán VNPAY theo tài liệu chính thức
   * @param paymentData Dữ liệu thanh toán
   * @returns URL thanh toán và mã giao dịch
   */
  async createPaymentUrl(
    paymentData: CreatePaymentDto,
  ): Promise<{ paymentUrl: string; transactionId: string }> {
    try {
      // Validation các tham số bắt buộc
      if (!this.tmnCode || !this.hashSecret || !this.returnUrl) {
        throw new Error(
          'VNPAY configuration is missing. Please check TMN_CODE, HASH_SECRET, and RETURN_URL.',
        );
      }

      if (
        !paymentData.userId ||
        !paymentData.amount ||
        paymentData.amount <= 0
      ) {
        throw new Error(
          'Invalid payment data. UserId and positive amount are required.',
        );
      }

      const date = new Date();
      // Định dạng thời gian theo GMT+7: yyyyMMddHHmmss
      const createDate = this.formatDate(date);

      // Tạo mã giao dịch duy nhất (vnp_TxnRef)
      const orderId = `${paymentData.userId}_${Date.now()}`;
      const transactionId = orderId;

      // Thời gian hết hạn thanh toán (15 phút sau)
      const expireDate = new Date(date.getTime() + 15 * 60 * 1000);
      const vnp_ExpireDate = this.formatDate(expireDate);

      // Tham số bắt buộc theo tài liệu VNPAY
      const vnpParams: VnpayCreatePaymentParams = {
        vnp_Version: '2.1.0',
        vnp_Command: 'pay',
        vnp_TmnCode: this.tmnCode,
        vnp_Amount: paymentData.amount * 100, // Nhân 100 để loại bỏ phần thập phân
        vnp_CurrCode: 'VND',
        vnp_TxnRef: orderId,
        vnp_OrderInfo: this.sanitizeOrderInfo(
          paymentData.description || `Thanh toan don hang ${orderId}`,
        ),
        vnp_OrderType: paymentData.orderType || 'other', // Mã danh mục hàng hóa
        vnp_Locale: paymentData.locale || 'vn', // vn hoặc en
        vnp_ReturnUrl: this.returnUrl,
        vnp_IpAddr: paymentData.ipAddress || '127.0.0.1',
        vnp_CreateDate: createDate,
        vnp_ExpireDate: vnp_ExpireDate,
      };

      // Thêm IPN URL nếu có cấu hình
      if (this.ipnUrl) {
        // VNPAY không có tham số vnp_IpnUrl trong URL thanh toán
        // IPN URL được cấu hình trực tiếp trên merchant portal của VNPAY
        this.logger.debug(`IPN URL configured: ${this.ipnUrl}`);
      }

      // Thêm mã ngân hàng nếu có (tùy chọn)
      if (paymentData.bankCode) {
        vnpParams.vnp_BankCode = paymentData.bankCode;
      }

      // Sắp xếp các tham số theo thứ tự a-z
      const sortedParams = this.sortObject(vnpParams);

      // Tạo chuỗi ký theo định dạng URL encode
      const signData = this.createSignData(sortedParams);

      // Tạo chữ ký HMAC SHA512
      const vnp_SecureHash = this.createSecureHash(signData);

      // Thêm chữ ký vào tham số
      sortedParams['vnp_SecureHash'] = vnp_SecureHash;

      // Tạo URL thanh toán
      const paymentUrl =
        this.vnpayUrl + '?' + this.createQueryString(sortedParams);

      this.logger.log(`Đã tạo URL thanh toán VNPAY cho đơn hàng: ${orderId}`);
      this.logger.debug(`Payment URL: ${paymentUrl}`);

      return { paymentUrl, transactionId };
    } catch (error) {
      this.logger.error(
        `Lỗi khi tạo URL thanh toán VNPAY: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Xác thực callback từ VNPAY (Return URL và IPN)
   * @param params Tham số từ VNPAY
   * @returns Kết quả xác thực
   */
  async verifyReturnUrl(params: Record<string, string>): Promise<{
    isValid: boolean;
    transactionId?: string;
    amount?: number;
    responseCode?: string;
    transactionStatus?: string;
    message?: string;
    vnpayTransactionNo?: string;
    bankCode?: string;
    payDate?: string;
  }> {
    try {
      const secureHash = params['vnp_SecureHash'];
      const orderId = params['vnp_TxnRef'];
      const responseCode = params['vnp_ResponseCode'];
      const transactionStatus = params['vnp_TransactionStatus'];
      const vnpayTransactionNo = params['vnp_TransactionNo'];
      const bankCode = params['vnp_BankCode'];
      const payDate = params['vnp_PayDate'];

      if (!secureHash || !orderId) {
        return {
          isValid: false,
          message: 'Thiếu thông tin bắt buộc từ VNPAY',
        };
      }

      // Tạo bản sao để xử lý
      const verifyParams = { ...params };

      // Xóa chữ ký để tạo lại và so sánh
      delete verifyParams['vnp_SecureHash'];
      delete verifyParams['vnp_SecureHashType'];

      // Sắp xếp các tham số theo thứ tự a-z
      const sortedParams = this.sortObject(verifyParams);

      // Tạo chuỗi ký
      const signData = this.createSignData(sortedParams);

      // Tạo chữ ký để so sánh
      const calculatedHash = this.createSecureHash(signData);

      // So sánh chữ ký
      const isValidSignature = secureHash === calculatedHash;

      if (!isValidSignature) {
        this.logger.warn(`Chữ ký không hợp lệ cho đơn hàng: ${orderId}`);
        this.logger.debug(
          `Expected: ${calculatedHash}, Received: ${secureHash}`,
        );

        return {
          isValid: false,
          transactionId: orderId,
          responseCode,
          message: 'Chữ ký không hợp lệ',
        };
      }

      // Chữ ký hợp lệ, kiểm tra kết quả thanh toán
      const amount = parseInt(params['vnp_Amount']) / 100; // Chuyển về đơn vị tiền tệ
      const isSuccessful = responseCode === '00' && transactionStatus === '00';

      if (isSuccessful) {
        this.logger.log(
          `Xác thực thanh toán VNPAY thành công - Đơn hàng: ${orderId}, Số tiền: ${amount}, VNPAY TxnNo: ${vnpayTransactionNo}`,
        );

        return {
          isValid: true,
          transactionId: orderId,
          amount,
          responseCode,
          transactionStatus,
          vnpayTransactionNo,
          bankCode,
          payDate,
          message: 'Thanh toán thành công',
        };
      } else {
        this.logger.warn(
          `Thanh toán không thành công - Đơn hàng: ${orderId}, ResponseCode: ${responseCode}, TransactionStatus: ${transactionStatus}`,
        );

        return {
          isValid: true, // Chữ ký hợp lệ nhưng thanh toán không thành công
          transactionId: orderId,
          amount,
          responseCode,
          transactionStatus,
          vnpayTransactionNo,
          bankCode,
          payDate,
          message: this.getErrorMessage(responseCode),
        };
      }
    } catch (error) {
      this.logger.error(
        `Lỗi khi xác thực callback VNPAY: ${error.message}`,
        error.stack,
      );

      return {
        isValid: false,
        message: `Lỗi xử lý: ${error.message}`,
      };
    }
  }

  /**
   * Xử lý thông báo thanh toán tức thì (IPN) từ VNPAY
   * @param params Tham số từ VNPAY
   * @returns Kết quả xử lý theo interface
   */
  async handleIpnCallback(params: Record<string, string>): Promise<{
    isValid: boolean;
    transactionId?: string;
    amount?: number;
    message?: string;
  }> {
    // IPN xử lý tương tự như verifyReturnUrl nhưng có thể có logic khác
    const result = await this.verifyReturnUrl(params);

    // Log cho IPN
    if (result.isValid) {
      this.logger.log(
        `IPN - Nhận thông báo thanh toán: ${result.transactionId}, Status: ${result.responseCode}`,
      );
    } else {
      this.logger.error(`IPN - Thông báo không hợp lệ: ${result.message}`);
    }

    return {
      isValid: result.isValid,
      transactionId: result.transactionId,
      amount: result.amount,
      message: result.message,
    };
  }

  /**
   * Tạo response cho IPN callback theo định dạng VNPAY yêu cầu
   * @param params Tham số từ VNPAY
   * @returns Response cho VNPAY
   */
  async createIpnResponse(params: Record<string, string>): Promise<{
    RspCode: string;
    Message: string;
  }> {
    try {
      const verifyResult = await this.verifyReturnUrl(params);

      if (!verifyResult.isValid) {
        // Chữ ký không hợp lệ
        this.logger.error(
          `IPN Response - Chữ ký không hợp lệ: ${verifyResult.message}`,
        );
        return {
          RspCode: '97',
          Message: 'Invalid signature',
        };
      }

      // Kiểm tra trạng thái giao dịch
      const responseCode = verifyResult.responseCode;
      const transactionStatus = verifyResult.transactionStatus;

      if (responseCode === '00' && transactionStatus === '00') {
        // Giao dịch thành công
        this.logger.log(
          `IPN Response - Giao dịch thành công: ${verifyResult.transactionId}`,
        );

        // TODO: Cập nhật trạng thái giao dịch trong database
        // Merchant cần implement logic cập nhật database tại đây

        return {
          RspCode: '00',
          Message: 'Confirm Success',
        };
      } else {
        // Giao dịch không thành công
        this.logger.warn(
          `IPN Response - Giao dịch không thành công: ${verifyResult.transactionId}, Code: ${responseCode}`,
        );

        // TODO: Cập nhật trạng thái giao dịch thất bại trong database

        return {
          RspCode: '00', // Vẫn trả 00 vì đã nhận được thông tin
          Message: 'Transaction failed but confirmed',
        };
      }
    } catch (error) {
      this.logger.error(
        `IPN Response - Lỗi xử lý: ${error.message}`,
        error.stack,
      );
      return {
        RspCode: '99',
        Message: 'Unknown error',
      };
    }
  }

  /**
   * Định dạng thời gian theo GMT+7: yyyyMMddHHmmss
   * @param date Đối tượng Date
   * @returns Chuỗi thời gian đã định dạng
   */
  private formatDate(date: Date): string {
    // Tạo đối tượng Date với timezone GMT+7 (Asia/Ho_Chi_Minh)
    const vietnamTime = new Date(
      date.toLocaleString('en-US', { timeZone: 'Asia/Ho_Chi_Minh' }),
    );

    const year = vietnamTime.getFullYear();
    const month = String(vietnamTime.getMonth() + 1).padStart(2, '0');
    const day = String(vietnamTime.getDate()).padStart(2, '0');
    const hours = String(vietnamTime.getHours()).padStart(2, '0');
    const minutes = String(vietnamTime.getMinutes()).padStart(2, '0');
    const seconds = String(vietnamTime.getSeconds()).padStart(2, '0');

    return `${year}${month}${day}${hours}${minutes}${seconds}`;
  }

  /**
   * Làm sạch thông tin đơn hàng theo yêu cầu VNPAY
   * @param orderInfo Thông tin đơn hàng
   * @returns Thông tin đã làm sạch
   */
  private sanitizeOrderInfo(orderInfo: string): string {
    // VNPAY yêu cầu: Tiếng Việt không dấu và không bao gồm các ký tự đặc biệt
    return orderInfo
      .normalize('NFD')
      .replace(/[\u0300-\u036f]/g, '') // Loại bỏ dấu
      .replace(/[^a-zA-Z0-9\s]/g, '') // Loại bỏ ký tự đặc biệt
      .replace(/\s+/g, ' ') // Loại bỏ khoảng trắng thừa
      .trim()
      .substring(0, 255); // Giới hạn độ dài
  }

  /**
   * Tạo chuỗi ký cho checksum
   * @param params Tham số đã sắp xếp
   * @returns Chuỗi ký
   */
  private createSignData(params: Record<string, any>): string {
    const result: string[] = [];

    for (const key in params) {
      if (
        params.hasOwnProperty(key) &&
        params[key] !== null &&
        params[key] !== undefined &&
        params[key] !== ''
      ) {
        result.push(
          `${encodeURIComponent(key)}=${encodeURIComponent(params[key])}`,
        );
      }
    }

    return result.join('&');
  }

  /**
   * Tạo chữ ký HMAC SHA512
   * @param data Dữ liệu cần ký
   * @returns Chữ ký
   */
  private createSecureHash(data: string): string {
    const hmac = crypto.createHmac('sha512', this.hashSecret);
    return hmac.update(Buffer.from(data, 'utf-8')).digest('hex');
  }

  /**
   * Tạo query string cho URL
   * @param params Tham số
   * @returns Query string
   */
  private createQueryString(params: Record<string, any>): string {
    const result: string[] = [];

    for (const key in params) {
      if (
        params.hasOwnProperty(key) &&
        params[key] !== null &&
        params[key] !== undefined &&
        params[key] !== ''
      ) {
        result.push(
          `${encodeURIComponent(key)}=${encodeURIComponent(params[key])}`,
        );
      }
    }

    return result.join('&');
  }

  /**
   * Sắp xếp các tham số theo thứ tự a-z
   * @param obj Đối tượng cần sắp xếp
   * @returns Đối tượng đã sắp xếp
   */
  private sortObject(obj: Record<string, any>): Record<string, any> {
    const sorted: Record<string, any> = {};
    const keys = Object.keys(obj).sort();

    for (const key of keys) {
      if (obj[key] !== null && obj[key] !== undefined && obj[key] !== '') {
        sorted[key] = obj[key];
      }
    }

    return sorted;
  }

  /**
   * Truy vấn kết quả giao dịch từ VNPAY
   * @param queryData Dữ liệu truy vấn
   * @returns Kết quả truy vấn
   */
  async queryTransaction(queryData: {
    txnRef: string;
    transactionDate: string;
    transactionNo?: string;
    orderInfo: string;
    ipAddr: string;
  }): Promise<any> {
    try {
      // Validation
      if (
        !queryData.txnRef ||
        !queryData.transactionDate ||
        !queryData.orderInfo ||
        !queryData.ipAddr
      ) {
        throw new Error('Missing required parameters for query transaction');
      }

      // Validate transactionDate format (yyyyMMddHHmmss)
      if (!/^\d{14}$/.test(queryData.transactionDate)) {
        throw new Error(
          'Invalid transactionDate format. Expected: yyyyMMddHHmmss',
        );
      }

      const requestId = this.generateRequestId();
      const createDate = this.formatDate(new Date());

      const queryParams: VnpayQueryRequest = {
        vnp_RequestId: requestId,
        vnp_Version: '2.1.0',
        vnp_Command: 'querydr',
        vnp_TmnCode: this.tmnCode,
        vnp_TxnRef: queryData.txnRef,
        vnp_OrderInfo: queryData.orderInfo,
        vnp_TransactionDate: queryData.transactionDate,
        vnp_CreateDate: createDate,
        vnp_IpAddr: queryData.ipAddr,
        vnp_SecureHash: '', // Sẽ được tính toán bên dưới
      };

      if (queryData.transactionNo) {
        queryParams.vnp_TransactionNo = queryData.transactionNo;
      }

      // Tạo checksum theo quy tắc VNPAY cho QueryDR
      // Thứ tự: RequestId|Version|Command|TmnCode|TxnRef|TransactionDate|CreateDate|IpAddr|OrderInfo
      const hashData = [
        requestId,
        '2.1.0',
        'querydr',
        this.tmnCode,
        queryData.txnRef,
        queryData.transactionDate,
        createDate,
        queryData.ipAddr,
        queryData.orderInfo,
      ].join('|');

      queryParams.vnp_SecureHash = this.createSecureHash(hashData);

      // Gửi request đến VNPAY API
      const response = await this.sendApiRequest(queryParams);

      this.logger.log(
        `Query transaction result for ${queryData.txnRef}: ${JSON.stringify(response)}`,
      );

      return response;
    } catch (error) {
      this.logger.error(
        `Lỗi khi truy vấn giao dịch: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Yêu cầu hoàn tiền từ VNPAY
   * @param refundData Dữ liệu hoàn tiền
   * @returns Kết quả hoàn tiền
   */
  async refundTransaction(refundData: {
    txnRef: string;
    amount: number;
    orderInfo: string;
    transactionNo?: string;
    transactionDate: string;
    transactionType: '02' | '03'; // 02: toàn phần, 03: một phần
    createBy: string;
    ipAddr: string;
  }): Promise<any> {
    try {
      // Validation
      if (
        !refundData.txnRef ||
        !refundData.amount ||
        refundData.amount <= 0 ||
        !refundData.orderInfo ||
        !refundData.transactionDate ||
        !refundData.createBy ||
        !refundData.ipAddr
      ) {
        throw new Error('Missing required parameters for refund transaction');
      }

      // Validate transactionDate format (yyyyMMddHHmmss)
      if (!/^\d{14}$/.test(refundData.transactionDate)) {
        throw new Error(
          'Invalid transactionDate format. Expected: yyyyMMddHHmmss',
        );
      }

      // Validate transactionType
      if (!['02', '03'].includes(refundData.transactionType)) {
        throw new Error(
          'Invalid transactionType. Must be "02" (full) or "03" (partial)',
        );
      }

      const requestId = this.generateRequestId();
      const createDate = this.formatDate(new Date());

      const refundParams: VnpayRefundRequest = {
        vnp_RequestId: requestId,
        vnp_Version: '2.1.0',
        vnp_Command: 'refund',
        vnp_TmnCode: this.tmnCode,
        vnp_TransactionType: refundData.transactionType,
        vnp_TxnRef: refundData.txnRef,
        vnp_Amount: refundData.amount * 100, // Nhân 100
        vnp_OrderInfo: refundData.orderInfo,
        vnp_TransactionDate: refundData.transactionDate,
        vnp_CreateBy: refundData.createBy,
        vnp_CreateDate: createDate,
        vnp_IpAddr: refundData.ipAddr,
        vnp_SecureHash: '', // Sẽ được tính toán bên dưới
      };

      if (refundData.transactionNo) {
        refundParams.vnp_TransactionNo = refundData.transactionNo;
      }

      // Tạo checksum theo quy tắc VNPAY cho Refund
      // Thứ tự: RequestId|Version|Command|TmnCode|TransactionType|TxnRef|Amount|TransactionNo|TransactionDate|CreateBy|CreateDate|IpAddr|OrderInfo
      const hashData = [
        requestId,
        '2.1.0',
        'refund',
        this.tmnCode,
        refundData.transactionType,
        refundData.txnRef,
        refundData.amount * 100,
        refundData.transactionNo || '',
        refundData.transactionDate,
        refundData.createBy,
        createDate,
        refundData.ipAddr,
        refundData.orderInfo,
      ].join('|');

      refundParams.vnp_SecureHash = this.createSecureHash(hashData);

      // Gửi request đến VNPAY API
      const response = await this.sendApiRequest(refundParams);

      this.logger.log(
        `Refund transaction result for ${refundData.txnRef}: ${JSON.stringify(response)}`,
      );

      return response;
    } catch (error) {
      this.logger.error(`Lỗi khi hoàn tiền: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Gửi API request đến VNPAY
   * @param params Tham số request
   * @returns Response từ VNPAY
   */
  private async sendApiRequest(params: any): Promise<any> {
    try {
      this.debugLog('VNPAY API Request', params);

      const response = await firstValueFrom(
        this.httpService.post(this.vnpayApiUrl, params, {
          headers: {
            'Content-Type': 'application/json',
          },
          timeout: 30000, // 30 seconds timeout
        }),
      );

      this.debugLog('VNPAY API Response', response.data);

      // Validate response
      const expectedCommand = params.vnp_Command;
      if (!this.validateApiResponse(response.data, expectedCommand)) {
        throw new Error('Invalid response from VNPAY API');
      }

      return response.data;
    } catch (error) {
      this.logger.error(
        `Lỗi khi gửi API request đến VNPAY: ${error.message}`,
        error.stack,
      );

      if (error.response) {
        this.logger.error(`Response status: ${error.response.status}`);
        this.logger.error(
          `Response data: ${JSON.stringify(error.response.data)}`,
        );
      }

      throw error;
    }
  }

  /**
   * Tạo mã request ID duy nhất theo format VNPAY
   * @returns Request ID (max 32 ký tự)
   */
  private generateRequestId(): string {
    const timestamp = Date.now().toString();
    const random = Math.random().toString(36).substring(2, 8).toUpperCase();
    const requestId = `REQ${timestamp}${random}`;
    return requestId.substring(0, 32); // Đảm bảo không quá 32 ký tự
  }

  /**
   * Lấy thông báo lỗi theo mã response code
   * @param responseCode Mã response
   * @returns Thông báo lỗi
   */
  private getErrorMessage(responseCode: string): string {
    const errorMessages: Record<string, string> = {
      '00': 'Giao dịch thành công',
      '07': 'Trừ tiền thành công. Giao dịch bị nghi ngờ (liên quan tới lừa đảo, giao dịch bất thường)',
      '09': 'Giao dịch không thành công do: Thẻ/Tài khoản của khách hàng chưa đăng ký dịch vụ InternetBanking tại ngân hàng',
      '10': 'Giao dịch không thành công do: Khách hàng xác thực thông tin thẻ/tài khoản không đúng quá 3 lần',
      '11': 'Giao dịch không thành công do: Đã hết hạn chờ thanh toán. Xin quý khách vui lòng thực hiện lại giao dịch',
      '12': 'Giao dịch không thành công do: Thẻ/Tài khoản của khách hàng bị khóa',
      '13': 'Giao dịch không thành công do Quý khách nhập sai mật khẩu xác thực giao dịch (OTP)',
      '24': 'Giao dịch không thành công do: Khách hàng hủy giao dịch',
      '51': 'Giao dịch không thành công do: Tài khoản của quý khách không đủ số dư để thực hiện giao dịch',
      '65': 'Giao dịch không thành công do: Tài khoản của Quý khách đã vượt quá hạn mức giao dịch trong ngày',
      '75': 'Ngân hàng thanh toán đang bảo trì',
      '79': 'Giao dịch không thành công do: KH nhập sai mật khẩu thanh toán quá số lần quy định',
      '99': 'Các lỗi khác (lỗi còn lại, không có trong danh sách mã lỗi đã liệt kê)',
    };

    return errorMessages[responseCode] || `Lỗi không xác định: ${responseCode}`;
  }

  /**
   * Validate response từ VNPAY API
   * @param response Response từ VNPAY
   * @param expectedCommand Command mong đợi
   * @returns True nếu response hợp lệ
   */
  private validateApiResponse(response: any, expectedCommand: string): boolean {
    if (!response) {
      this.logger.error('Empty response from VNPAY API');
      return false;
    }

    // Kiểm tra các field bắt buộc
    const requiredFields = ['vnp_ResponseCode', 'vnp_Message'];
    for (const field of requiredFields) {
      if (!response[field]) {
        this.logger.error(`Missing required field: ${field}`);
        return false;
      }
    }

    // Kiểm tra command nếu có
    if (response.vnp_Command && response.vnp_Command !== expectedCommand) {
      this.logger.error(
        `Unexpected command: ${response.vnp_Command}, expected: ${expectedCommand}`,
      );
      return false;
    }

    // Kiểm tra response code
    if (response.vnp_ResponseCode !== '00') {
      this.logger.warn(
        `VNPAY API error: ${response.vnp_ResponseCode} - ${response.vnp_Message}`,
      );
      // Không return false vì đây có thể là lỗi business logic, không phải lỗi technical
    }

    return true;
  }

  /**
   * Log thông tin debug cho development
   * @param operation Tên operation
   * @param data Dữ liệu cần log
   */
  private debugLog(operation: string, data: any): void {
    if (process.env.NODE_ENV === 'development') {
      this.logger.debug(`[${operation}] ${JSON.stringify(data, null, 2)}`);
    }
  }
}

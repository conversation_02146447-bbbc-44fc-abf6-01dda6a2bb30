import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import * as crypto from 'crypto';
import { IPaymentGateway } from '../interfaces/payment-gateway.interface';
import { CreatePaymentDto } from '../dto/create-payment.dto';

@Injectable()
export class VnpayService implements IPaymentGateway {
  private readonly logger = new Logger(VnpayService.name);
  private readonly tmnCode: string;
  private readonly hashSecret: string;
  private readonly vnpayUrl: string;
  private readonly returnUrl: string;

  constructor(private readonly configService: ConfigService) {
    this.tmnCode = this.configService.get<string>('VNPAY_TMN_CODE') || '';
    this.hashSecret = this.configService.get<string>('VNPAY_HASH_SECRET') || '';
    this.vnpayUrl = this.configService.get<string>('VNPAY_URL') || '';
    this.returnUrl = this.configService.get<string>('VNPAY_RETURN_URL') || '';
  }

  /**
   * Tạo URL thanh toán VNPAY
   * @param paymentData Dữ liệu thanh toán
   * @returns URL thanh toán và mã giao dịch
   */
  async createPaymentUrl(
    paymentData: CreatePaymentDto,
  ): Promise<{ paymentUrl: string; transactionId: string }> {
    try {
      const date = new Date();
      const createDate = date.toISOString().replace(/:/g, '').slice(0, -5);
      const orderId = date.toISOString().replace(/:/g, '').slice(0, -5);
      const transactionId = paymentData.userId + '_' + orderId; // Tạo mã giao dịch duy nhất

      const vnpParams = {
        vnp_Version: '2.1.0',
        vnp_Command: 'pay',
        vnp_TmnCode: this.tmnCode,
        vnp_Locale: 'vn',
        vnp_CurrCode: 'VND',
        vnp_TxnRef: orderId,
        vnp_OrderInfo:
          paymentData.description || `Thanh toan cho ma GD: ${orderId}`,
        vnp_OrderType: 'billpayment',
        vnp_Amount: paymentData.amount * 100, // Số tiền * 100 (VNPay yêu cầu)
        vnp_ReturnUrl: this.returnUrl,
        vnp_IpAddr: paymentData.ipAddress || '127.0.0.1',
        vnp_CreateDate: createDate,
      };

      // Sắp xếp các tham số theo thứ tự a-z
      const sortedParams = this.sortObject(vnpParams);

      // Tạo chuỗi ký
      const signData = this.stringifyParams(sortedParams);

      // Tạo chữ ký
      const hmac = crypto.createHmac('sha512', this.hashSecret);
      const signed = hmac.update(Buffer.from(signData, 'utf-8')).digest('hex');

      // Thêm chữ ký vào tham số
      sortedParams['vnp_SecureHash'] = signed;

      // Tạo URL thanh toán
      const paymentUrl =
        this.vnpayUrl + '?' + this.stringifyParams(sortedParams);

      this.logger.log(`Đã tạo URL thanh toán VNPAY: ${paymentUrl}`);

      return { paymentUrl, transactionId };
    } catch (error) {
      this.logger.error(
        `Lỗi khi tạo URL thanh toán VNPAY: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Xác thực callback từ VNPAY
   * @param params Tham số từ VNPAY
   * @returns Kết quả xác thực
   */
  async verifyReturnUrl(params: Record<string, string>): Promise<{
    isValid: boolean;
    transactionId?: string;
    amount?: number;
    message?: string;
  }> {
    try {
      const secureHash = params['vnp_SecureHash'];
      const orderId = params['vnp_TxnRef'];
      const rspCode = params['vnp_ResponseCode'];

      // Xóa chữ ký để tạo lại và so sánh
      delete params['vnp_SecureHash'];
      delete params['vnp_SecureHashType'];

      // Sắp xếp các tham số theo thứ tự a-z
      const sortedParams = this.sortObject(params);

      // Tạo chuỗi ký
      const signData = this.stringifyParams(sortedParams);

      // Tạo chữ ký
      const hmac = crypto.createHmac('sha512', this.hashSecret);
      const signed = hmac.update(Buffer.from(signData, 'utf-8')).digest('hex');

      // So sánh chữ ký
      const isValidSignature = secureHash === signed;
      const isSuccessful = rspCode === '00';

      // Nếu chữ ký hợp lệ và thanh toán thành công
      if (isValidSignature && isSuccessful) {
        const amount = parseInt(params['vnp_Amount']) / 100; // Chuyển về đơn vị tiền tệ

        this.logger.log(
          `Xác thực thanh toán VNPAY thành công cho đơn hàng: ${orderId}, số tiền: ${amount}`,
        );

        return {
          isValid: true,
          transactionId: orderId,
          amount,
          message: 'Thanh toán thành công',
        };
      } else if (!isValidSignature) {
        this.logger.warn(`Chữ ký không hợp lệ cho đơn hàng: ${orderId}`);

        return {
          isValid: false,
          message: 'Chữ ký không hợp lệ',
        };
      } else {
        this.logger.warn(
          `Thanh toán không thành công cho đơn hàng: ${orderId}, mã lỗi: ${rspCode}`,
        );

        return {
          isValid: false,
          transactionId: orderId,
          message: `Thanh toán không thành công, mã lỗi: ${rspCode}`,
        };
      }
    } catch (error) {
      this.logger.error(
        `Lỗi khi xác thực callback VNPAY: ${error.message}`,
        error.stack,
      );

      return {
        isValid: false,
        message: `Lỗi xử lý: ${error.message}`,
      };
    }
  }

  /**
   * Xử lý thông báo thanh toán tức thì (IPN) từ VNPAY
   * @param params Tham số từ VNPAY
   * @returns Kết quả xử lý
   */
  async handleIpnCallback(params: Record<string, string>): Promise<{
    isValid: boolean;
    transactionId?: string;
    amount?: number;
    message?: string;
  }> {
    // IPN xử lý tương tự như verifyReturnUrl
    return this.verifyReturnUrl(params);
  }

  /**
   * Sắp xếp các tham số theo thứ tự a-z
   * @param obj Đối tượng cần sắp xếp
   * @returns Đối tượng đã sắp xếp
   */
  private sortObject(obj: Record<string, any>): Record<string, any> {
    const sorted: Record<string, any> = {};
    const keys = Object.keys(obj).sort();

    for (const key of keys) {
      sorted[key] = obj[key];
    }

    return sorted;
  }

  /**
   * Chuyển đổi đối tượng thành chuỗi query
   * @param obj Đối tượng cần chuyển đổi
   * @returns Chuỗi query
   */
  private stringifyParams(obj: Record<string, any>): string {
    const result: string[] = [];

    for (const key in obj) {
      if (obj.hasOwnProperty(key)) {
        result.push(key + '=' + obj[key]);
      }
    }

    return result.join('&');
  }
}

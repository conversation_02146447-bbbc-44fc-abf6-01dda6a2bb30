import { Repository } from 'typeorm';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { CmsPartners } from '../entity/cms-partners.entity';
import { BaseCmsPartnersService } from './base.cms-partners.service';
import { UpdateCmsPartnerDto, UpdateCmsPartnerStatusDto, UpdateCmsPartnerDisplayOrderDto, UpdateCmsPartnerTypeDto, BulkUpdateCmsPartnersDto, BulkUpdateCmsPartnersStatusDto, BulkOperationResponseDto, CmsPartnerDto } from '../dto';
export declare class UpdateCmsPartnersService extends BaseCmsPartnersService {
    protected readonly partnerRepository: Repository<CmsPartners>;
    protected readonly eventEmitter: EventEmitter2;
    constructor(partnerRepository: Repository<CmsPartners>, eventEmitter: EventEmitter2);
    update(id: string, updateDto: UpdateCmsPartnerDto, userId: string): Promise<CmsPartnerDto>;
    updateStatus(id: string, updateStatusDto: UpdateCmsPartnerStatusDto, userId: string): Promise<CmsPartnerDto>;
    activate(id: string, userId: string): Promise<CmsPartnerDto>;
    deactivate(id: string, userId: string): Promise<CmsPartnerDto>;
    updateDisplayOrder(id: string, updateDisplayOrderDto: UpdateCmsPartnerDisplayOrderDto, userId: string): Promise<CmsPartnerDto>;
    updateType(id: string, updateTypeDto: UpdateCmsPartnerTypeDto, userId: string): Promise<CmsPartnerDto>;
    bulkUpdate(bulkUpdateDto: BulkUpdateCmsPartnersDto, userId: string): Promise<BulkOperationResponseDto>;
    bulkUpdateStatus(bulkUpdateStatusDto: BulkUpdateCmsPartnersStatusDto, userId: string): Promise<BulkOperationResponseDto>;
    bulkActivate(ids: string[], userId: string): Promise<BulkOperationResponseDto>;
    bulkDeactivate(ids: string[], userId: string): Promise<BulkOperationResponseDto>;
    restore(id: string, userId: string): Promise<CmsPartnerDto>;
}

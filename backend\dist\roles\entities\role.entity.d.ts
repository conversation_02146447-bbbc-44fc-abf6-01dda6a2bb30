import { BaseEntity } from '../../common/entities/base.entity';
import { UserRole } from '../../users/entities/user-role.entity';
import { RolePermission } from './role-permission.entity';
export declare class Role extends BaseEntity {
    name: string;
    code: string;
    description: string | null;
    isPrimary: boolean;
    rolePermissions: RolePermission[];
    userRoles: UserRole[];
    getEntityName(): string;
}

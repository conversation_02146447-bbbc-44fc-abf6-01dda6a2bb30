{"version": 3, "file": "vnpay-usage.example.js", "sourceRoot": "", "sources": ["../../../src/payment-gateways/examples/vnpay-usage.example.ts"], "names": [], "mappings": ";;;;;;;;;;;;AAMA,2CAA4C;AAC5C,6DAAyD;AAEzD,kFAAwE;AAGjE,IAAM,iBAAiB,GAAvB,MAAM,iBAAiB;IACC;IAA7B,YAA6B,YAA0B;QAA1B,iBAAY,GAAZ,YAAY,CAAc;IAAG,CAAC;IAK3D,KAAK,CAAC,kBAAkB;QACtB,MAAM,WAAW,GAAqB;YACpC,MAAM,EAAE,sCAAsC;YAC9C,QAAQ,EAAE,sCAAsC;YAChD,MAAM,EAAE,MAAM;YACd,WAAW,EAAE,8CAAkB,CAAC,KAAK;YACrC,WAAW,EAAE,iBAAiB;YAC9B,SAAS,EAAE,WAAW;SACvB,CAAC;QAEF,IAAI,CAAC;YACH,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,YAAY,CAAC,gBAAgB,CAAC,WAAW,CAAC,CAAC;YACrE,OAAO,CAAC,GAAG,CAAC,cAAc,EAAE,MAAM,CAAC,UAAU,CAAC,CAAC;YAC/C,OAAO,CAAC,GAAG,CAAC,iBAAiB,EAAE,MAAM,CAAC,aAAa,CAAC,CAAC;YACrD,OAAO,MAAM,CAAC;QAChB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,yBAAyB,EAAE,KAAK,CAAC,CAAC;YAChD,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAKD,KAAK,CAAC,yBAAyB;QAC7B,MAAM,WAAW,GAAqB;YACpC,MAAM,EAAE,sCAAsC;YAC9C,QAAQ,EAAE,sCAAsC;YAChD,MAAM,EAAE,MAAM;YACd,WAAW,EAAE,8CAAkB,CAAC,KAAK;YACrC,WAAW,EAAE,4BAA4B;YACzC,SAAS,EAAE,eAAe;YAC1B,QAAQ,EAAE,SAAS;YACnB,MAAM,EAAE,IAAI;YACZ,SAAS,EAAE,aAAa;SACzB,CAAC;QAEF,IAAI,CAAC;YACH,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,YAAY,CAAC,gBAAgB,CAAC,WAAW,CAAC,CAAC;YACrE,OAAO,MAAM,CAAC;QAChB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,wCAAwC,EAAE,KAAK,CAAC,CAAC;YAC/D,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAKD,KAAK,CAAC,eAAe,CAAC,WAAmC;QACvD,IAAI,CAAC;YACH,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,YAAY,CAAC,eAAe,CAAC,WAAW,CAAC,CAAC;YAEpE,IAAI,MAAM,CAAC,OAAO,EAAE,CAAC;gBACnB,IAAI,MAAM,CAAC,YAAY,KAAK,IAAI,IAAI,MAAM,CAAC,iBAAiB,KAAK,IAAI,EAAE,CAAC;oBAEtE,OAAO,CAAC,GAAG,CAAC,0BAA0B,CAAC,CAAC;oBACxC,OAAO,CAAC,GAAG,CAAC,iBAAiB,EAAE,MAAM,CAAC,aAAa,CAAC,CAAC;oBACrD,OAAO,CAAC,GAAG,CAAC,SAAS,EAAE,MAAM,CAAC,MAAM,CAAC,CAAC;oBACtC,OAAO,CAAC,GAAG,CAAC,uBAAuB,EAAE,MAAM,CAAC,kBAAkB,CAAC,CAAC;oBAChE,OAAO,CAAC,GAAG,CAAC,YAAY,EAAE,MAAM,CAAC,QAAQ,CAAC,CAAC;oBAC3C,OAAO,CAAC,GAAG,CAAC,WAAW,EAAE,MAAM,CAAC,OAAO,CAAC,CAAC;oBAKzC,OAAO;wBACL,OAAO,EAAE,IAAI;wBACb,OAAO,EAAE,uBAAuB;wBAChC,IAAI,EAAE,MAAM;qBACb,CAAC;gBACJ,CAAC;qBAAM,CAAC;oBAEN,OAAO,CAAC,GAAG,CAAC,wBAAwB,CAAC,CAAC;oBACtC,OAAO,CAAC,GAAG,CAAC,gBAAgB,EAAE,MAAM,CAAC,YAAY,CAAC,CAAC;oBACnD,OAAO,CAAC,GAAG,CAAC,UAAU,EAAE,MAAM,CAAC,OAAO,CAAC,CAAC;oBAKxC,OAAO;wBACL,OAAO,EAAE,KAAK;wBACd,OAAO,EAAE,MAAM,CAAC,OAAO;wBACvB,IAAI,EAAE,MAAM;qBACb,CAAC;gBACJ,CAAC;YACH,CAAC;iBAAM,CAAC;gBAEN,OAAO,CAAC,GAAG,CAAC,yBAAyB,CAAC,CAAC;gBACvC,OAAO;oBACL,OAAO,EAAE,KAAK;oBACd,OAAO,EAAE,qBAAqB;oBAC9B,IAAI,EAAE,IAAI;iBACX,CAAC;YACJ,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,4BAA4B,EAAE,KAAK,CAAC,CAAC;YACnD,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAKD,KAAK,CAAC,iBAAiB,CAAC,WAAmC;QACzD,IAAI,CAAC;YAEH,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,YAAY,CAAC,iBAAiB,CAAC,WAAW,CAAC,CAAC;YAEtE,IAAI,MAAM,CAAC,OAAO,EAAE,CAAC;gBACnB,OAAO,CAAC,GAAG,CAAC,wBAAwB,CAAC,CAAC;gBAMtC,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,YAAY,CAAC,iBAAiB,CAAC,WAAW,CAAC,CAAC;gBAC3E,OAAO,WAAW,CAAC;YACrB,CAAC;iBAAM,CAAC;gBACN,OAAO,CAAC,GAAG,CAAC,8BAA8B,CAAC,CAAC;gBAC5C,OAAO;oBACL,OAAO,EAAE,IAAI;oBACb,OAAO,EAAE,mBAAmB;iBAC7B,CAAC;YACJ,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,qBAAqB,EAAE,KAAK,CAAC,CAAC;YAC5C,OAAO;gBACL,OAAO,EAAE,IAAI;gBACb,OAAO,EAAE,eAAe;aACzB,CAAC;QACJ,CAAC;IACH,CAAC;IAKD,KAAK,CAAC,sBAAsB,CAAC,MAAc,EAAE,eAAuB;QAClE,IAAI,CAAC;YACH,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,YAAY,CAAC,gBAAgB,CAAC;gBACtD,MAAM,EAAE,MAAM;gBACd,eAAe,EAAE,eAAe;gBAChC,SAAS,EAAE,sBAAsB,MAAM,EAAE;gBACzC,MAAM,EAAE,WAAW;aACpB,CAAC,CAAC;YAEH,OAAO,CAAC,GAAG,CAAC,eAAe,EAAE,MAAM,CAAC,CAAC;YAGrC,IAAI,MAAM,CAAC,gBAAgB,KAAK,IAAI,EAAE,CAAC;gBACrC,OAAO,CAAC,GAAG,CAAC,uBAAuB,CAAC,CAAC;gBACrC,OAAO,CAAC,GAAG,CAAC,qBAAqB,EAAE,MAAM,CAAC,qBAAqB,CAAC,CAAC;gBACjE,OAAO,CAAC,GAAG,CAAC,SAAS,EAAE,MAAM,CAAC,UAAU,CAAC,CAAC;gBAC1C,OAAO,CAAC,GAAG,CAAC,YAAY,EAAE,MAAM,CAAC,YAAY,CAAC,CAAC;gBAC/C,OAAO,CAAC,GAAG,CAAC,WAAW,EAAE,MAAM,CAAC,WAAW,CAAC,CAAC;YAC/C,CAAC;iBAAM,CAAC;gBACN,OAAO,CAAC,GAAG,CAAC,sBAAsB,EAAE,MAAM,CAAC,WAAW,CAAC,CAAC;YAC1D,CAAC;YAED,OAAO,MAAM,CAAC;QAChB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,6BAA6B,EAAE,KAAK,CAAC,CAAC;YACpD,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAKD,KAAK,CAAC,qBAAqB,CACzB,MAAc,EACd,cAAsB,EACtB,eAAuB,EACvB,kBAA2B;QAE3B,IAAI,CAAC;YACH,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,YAAY,CAAC,iBAAiB,CAAC;gBACvD,MAAM,EAAE,MAAM;gBACd,MAAM,EAAE,cAAc;gBACtB,SAAS,EAAE,gCAAgC,MAAM,EAAE;gBACnD,eAAe,EAAE,eAAe;gBAChC,eAAe,EAAE,IAAI;gBACrB,QAAQ,EAAE,cAAc;gBACxB,MAAM,EAAE,WAAW;gBACnB,aAAa,EAAE,kBAAkB;aAClC,CAAC,CAAC;YAEH,OAAO,CAAC,GAAG,CAAC,gBAAgB,EAAE,MAAM,CAAC,CAAC;YAEtC,IAAI,MAAM,CAAC,gBAAgB,KAAK,IAAI,EAAE,CAAC;gBACrC,OAAO,CAAC,GAAG,CAAC,wBAAwB,CAAC,CAAC;gBACtC,OAAO,CAAC,GAAG,CAAC,wBAAwB,EAAE,MAAM,CAAC,iBAAiB,CAAC,CAAC;YAClE,CAAC;iBAAM,CAAC;gBACN,OAAO,CAAC,GAAG,CAAC,uBAAuB,EAAE,MAAM,CAAC,WAAW,CAAC,CAAC;YAC3D,CAAC;YAED,OAAO,MAAM,CAAC;QAChB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,8BAA8B,EAAE,KAAK,CAAC,CAAC;YACrD,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAKD,KAAK,CAAC,wBAAwB,CAC5B,MAAc,EACd,YAAoB,EACpB,eAAuB,EACvB,kBAA2B;QAE3B,IAAI,CAAC;YACH,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,YAAY,CAAC,iBAAiB,CAAC;gBACvD,MAAM,EAAE,MAAM;gBACd,MAAM,EAAE,YAAY;gBACpB,SAAS,EAAE,+BAA+B,MAAM,EAAE;gBAClD,eAAe,EAAE,eAAe;gBAChC,eAAe,EAAE,IAAI;gBACrB,QAAQ,EAAE,cAAc;gBACxB,MAAM,EAAE,WAAW;gBACnB,aAAa,EAAE,kBAAkB;aAClC,CAAC,CAAC;YAEH,OAAO,CAAC,GAAG,CAAC,wBAAwB,EAAE,MAAM,CAAC,CAAC;YAC9C,OAAO,MAAM,CAAC;QAChB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,sCAAsC,EAAE,KAAK,CAAC,CAAC;YAC7D,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAKD,KAAK,CAAC,uBAAuB,CAAC,MAAc,EAAE,QAAgB,EAAE,MAAc;QAC5E,IAAI,CAAC;YAEH,OAAO,CAAC,GAAG,CAAC,+BAA+B,CAAC,CAAC;YAC7C,MAAM,WAAW,GAAqB;gBACpC,MAAM;gBACN,QAAQ;gBACR,MAAM;gBACN,WAAW,EAAE,8CAAkB,CAAC,KAAK;gBACrC,WAAW,EAAE,YAAY,MAAM,aAAa;gBAC5C,SAAS,EAAE,WAAW;aACvB,CAAC;YAEF,MAAM,aAAa,GAAG,MAAM,IAAI,CAAC,YAAY,CAAC,gBAAgB,CAAC,WAAW,CAAC,CAAC;YAC5E,OAAO,CAAC,GAAG,CAAC,sBAAsB,EAAE,aAAa,CAAC,UAAU,CAAC,CAAC;YAc9D,OAAO,aAAa,CAAC;QACvB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,6BAA6B,EAAE,KAAK,CAAC,CAAC;YACpD,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;CACF,CAAA;AAnRY,8CAAiB;4BAAjB,iBAAiB;IAD7B,IAAA,mBAAU,GAAE;qCAEgC,4BAAY;GAD5C,iBAAiB,CAmR7B;AAKD,SAAS,UAAU,CAAC,IAAU;IAC5B,MAAM,WAAW,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC,CAAC;IAEpE,MAAM,IAAI,GAAG,WAAW,CAAC,cAAc,EAAE,CAAC;IAC1C,MAAM,KAAK,GAAG,MAAM,CAAC,WAAW,CAAC,WAAW,EAAE,GAAG,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC;IACrE,MAAM,GAAG,GAAG,MAAM,CAAC,WAAW,CAAC,UAAU,EAAE,CAAC,CAAC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC;IAC9D,MAAM,KAAK,GAAG,MAAM,CAAC,WAAW,CAAC,WAAW,EAAE,CAAC,CAAC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC;IACjE,MAAM,OAAO,GAAG,MAAM,CAAC,WAAW,CAAC,aAAa,EAAE,CAAC,CAAC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC;IACrE,MAAM,OAAO,GAAG,MAAM,CAAC,WAAW,CAAC,aAAa,EAAE,CAAC,CAAC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC;IAErE,OAAO,GAAG,IAAI,GAAG,KAAK,GAAG,GAAG,GAAG,KAAK,GAAG,OAAO,GAAG,OAAO,EAAE,CAAC;AAC7D,CAAC"}
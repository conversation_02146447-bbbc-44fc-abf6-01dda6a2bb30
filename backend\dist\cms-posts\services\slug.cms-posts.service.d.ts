import { Repository } from 'typeorm';
import { CmsPosts, CmsPostType } from '../entity/cms-posts.entity';
import { BaseSlugService } from '../../common/services/base-slug.service';
import { UnifiedSlugService } from '../../common/services/unified-slug.service';
export declare class SlugCmsPostsService extends BaseSlugService<CmsPosts> {
    protected readonly unifiedSlugService: UnifiedSlugService;
    constructor(postRepository: Repository<CmsPosts>, unifiedSlugService: UnifiedSlugService);
    protected getSlugFieldName(): string;
    protected getTextFieldName(): string;
    protected getWhereConditions(excludeId?: string): any;
    protected generateFallbackSlug(): string;
    generateSlugFromTitle(title: string): string;
    getExistingSlugs(postType: CmsPostType, excludeId?: string): Promise<string[]>;
    generateUniqueSlugForCreate(title: string, postType: CmsPostType, providedSlug?: string): Promise<string>;
    generateUniqueSlugForUpdate(title: string, postType: CmsPostType, currentId: string, providedSlug?: string, currentSlug?: string): Promise<string | null>;
    isSlugExists(slug: string, postType: CmsPostType, excludeId?: string): Promise<boolean>;
}

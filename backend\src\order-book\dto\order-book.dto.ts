import {
  IsDate,
  IsDecimal,
  IsEnum,
  IsNotEmpty,
  IsOptional,
  IsString,
} from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';
import { OrderType } from '../enums/order-type.enum';
import { OrderStatus } from '../enums/order-status.enum';
import { BusinessType } from '../enums/business-type.enum';
import { ApproveStatus } from '../enums/approve-status.enum';
import { BaseDto } from '../../common/dto/base.dto';
import { User } from '../../users/entities/user.entity';
import { OrderBookDetail } from '../entities/order-book-detail.entity';
import { Expose } from 'class-transformer';
import { OrderBookDetailDto } from './order-book-detail.dto';

export class OrderBookDto extends BaseDto {
  @Expose()
  @ApiProperty({
    description: 'ID người dùng',
    example: '550e8400-e29b-41d4-a716-446655440000',
  })
  @IsNotEmpty()
  @IsString()
  userId: string;

  @Expose()
  @ApiProperty({
    description: '<PERSON><PERSON><PERSON> lệnh (mua/bán)',
    enum: OrderType,
    example: OrderType.BUY,
  })
  @IsNotEmpty()
  @IsEnum(OrderType)
  orderType: OrderType;

  @Expose()
  @ApiProperty({
    description: 'Trạng thái lệnh (mặc định là đã đặt cọc)',
    enum: OrderStatus,
    example: OrderStatus.DEPOSITED,
  })
  @IsNotEmpty()
  @IsEnum(OrderStatus)
  status: OrderStatus;

  @Expose()
  @ApiProperty({
    description: 'Loại hình giao dịch',
    enum: BusinessType,
    example: BusinessType.NORMAL,
  })
  @IsOptional()
  @IsEnum(BusinessType)
  businessType: BusinessType;

  @Expose()
  @ApiProperty({ description: 'Tổng tiền đơn hàng', example: '5000000.00' })
  @IsOptional()
  @IsDecimal({ decimal_digits: '2' })
  totalPrice: number;

  @Expose()
  @ApiProperty({ description: 'Tổng tiền gia công', example: '5000000.00' })
  @IsOptional()
  @IsDecimal({ decimal_digits: '2' })
  processingPrice: number;

  @Expose()
  @ApiProperty({
    description: 'Tổng tiền cọc',
    example: '1000000.00',
  })
  @IsOptional()
  @IsDecimal({ decimal_digits: '2' })
  depositPrice: number;

  @Expose()
  @ApiProperty({
    description: 'Phí lưu kho',
    example: '50000.00',
  })
  @IsOptional()
  @IsDecimal({ decimal_digits: '2' })
  storageFee: number;

  @Expose()
  @ApiProperty({
    description: 'Tổng tiền tất toán',
    example: '4000000.00',
  })
  @IsOptional()
  @IsDecimal({ decimal_digits: '2' })
  settlementPrice: number;

  @Expose()
  @ApiProperty({ description: 'Tổng tiền cuối', example: '5000000.00' })
  @IsOptional()
  @IsDecimal({ decimal_digits: '2' })
  totalPriceFinal: number;

  @Expose()
  @ApiProperty({
    description: 'Số hợp đồng',
    example: 'HD-2023-001',
  })
  @IsOptional()
  @IsString()
  contractNumber: string;

  @Expose()
  @ApiProperty({
    description: 'Hạn chót tất toán',
    example: '2023-12-31T23:59:59Z',
  })
  @IsOptional()
  @IsDate()
  settlementDeadline: Date;

  @Expose()
  @ApiProperty({
    description: 'Thời gian tất toán',
    example: '2023-12-15T10:30:00Z',
  })
  @IsOptional()
  @IsDate()
  settlementAt: Date;

  @Expose()
  @ApiProperty({
    description: 'Trạng thái phê duyệt',
    enum: ApproveStatus,
    example: ApproveStatus.PENDING,
  })
  @IsOptional()
  @IsEnum(ApproveStatus)
  approveStatus: ApproveStatus;

  @Expose()
  @ApiProperty({
    description: 'Thời gian phê duyệt',
    example: '2023-12-15T10:30:00Z',
  })
  @IsOptional()
  @IsDate()
  approvedAt: Date;

  @Expose()
  @ApiProperty({
    type: () => User,
    description: 'Người dùng liên quan đến đơn hàng',
  })
  user: User;

  @Expose()
  @ApiProperty({
    type: () => OrderBookDetailDto,
    isArray: true,
    description: 'Chi tiết các sản phẩm trong đơn hàng',
  })
  details: OrderBookDetailDto[];
 
  @Expose()
  @ApiProperty({
    type: () => User,
    description: 'Người tạo',
  })
  creator: User;

  @Expose()
  @ApiProperty({
    type: () => User,
    description: 'Người cập nhật',
  })
  updater: User;
  
}

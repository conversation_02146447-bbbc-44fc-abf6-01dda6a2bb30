"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var CreateSystemConfigService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreateSystemConfigService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const event_emitter_1 = require("@nestjs/event-emitter");
const typeorm_transactional_1 = require("typeorm-transactional");
const system_config_entity_1 = require("../entities/system-config.entity");
const create_system_config_dto_1 = require("../dto/create-system-config.dto");
const base_system_config_service_1 = require("./base.system-config.service");
let CreateSystemConfigService = CreateSystemConfigService_1 = class CreateSystemConfigService extends base_system_config_service_1.BaseSystemConfigService {
    systemConfigRepository;
    dataSource;
    eventEmitter;
    logger = new common_1.Logger(CreateSystemConfigService_1.name);
    constructor(systemConfigRepository, dataSource, eventEmitter) {
        super(systemConfigRepository, dataSource, eventEmitter);
        this.systemConfigRepository = systemConfigRepository;
        this.dataSource = dataSource;
        this.eventEmitter = eventEmitter;
    }
    async create(createSystemConfigDto) {
        try {
            this.logger.debug(`Tạo cấu hình mới với dữ liệu: ${JSON.stringify(createSystemConfigDto)}`);
            const existingConfig = await this.systemConfigRepository.findOne({
                where: { configKey: createSystemConfigDto.configKey },
            });
            if (existingConfig) {
                throw new common_1.ConflictException(`Cấu hình với khóa "${createSystemConfigDto.configKey}" đã tồn tại`);
            }
            const systemConfig = this.systemConfigRepository.create(createSystemConfigDto);
            const savedConfig = await this.systemConfigRepository.save(systemConfig);
            try {
                this.eventEmitter.emit(this.EVENT_SYSTEM_CONFIG_CREATED, savedConfig);
            }
            catch (emitError) {
                this.logger.warn(`Không thể phát sự kiện tạo mới: ${emitError.message}`);
            }
            return this.toDto(savedConfig);
        }
        catch (error) {
            this.logger.error(`Lỗi khi tạo cấu hình: ${error.message}`, error.stack);
            if (error instanceof common_1.ConflictException) {
                throw error;
            }
            throw new common_1.InternalServerErrorException(`Không thể tạo cấu hình: ${error.message}`);
        }
    }
    async createOrUpdateBulk(items) {
        try {
            this.logger.debug(`Tạo hoặc cập nhật ${items.length} cấu hình`);
            if (!items || items.length === 0) {
                throw new common_1.BadRequestException('Danh sách cấu hình không được để trống');
            }
            const configKeys = items.map(item => item.configKey);
            const existingConfigs = await this.systemConfigRepository.find({
                where: { configKey: (0, typeorm_2.In)(configKeys) },
            });
            const existingConfigMap = new Map();
            existingConfigs.forEach(config => {
                existingConfigMap.set(config.configKey, config);
            });
            const savedConfigs = [];
            for (const item of items) {
                let config;
                if (existingConfigMap.has(item.configKey)) {
                    config = existingConfigMap.get(item.configKey);
                    Object.assign(config, item);
                    config.updatedAt = new Date();
                }
                else {
                    config = this.systemConfigRepository.create(item);
                }
                const savedConfig = await this.systemConfigRepository.save(config);
                savedConfigs.push(savedConfig);
                try {
                    if (existingConfigMap.has(item.configKey)) {
                        this.eventEmitter.emit(this.EVENT_SYSTEM_CONFIG_UPDATED, savedConfig);
                    }
                    else {
                        this.eventEmitter.emit(this.EVENT_SYSTEM_CONFIG_CREATED, savedConfig);
                    }
                }
                catch (emitError) {
                    this.logger.warn(`Không thể phát sự kiện cho cấu hình ${item.configKey}: ${emitError.message}`);
                }
            }
            return savedConfigs.map(config => this.toDto(config));
        }
        catch (error) {
            this.logger.error(`Lỗi khi tạo hoặc cập nhật hàng loạt cấu hình: ${error.message}`, error.stack);
            if (error instanceof common_1.BadRequestException) {
                throw error;
            }
            throw new common_1.InternalServerErrorException(`Không thể tạo hoặc cập nhật hàng loạt cấu hình: ${error.message}`);
        }
    }
};
exports.CreateSystemConfigService = CreateSystemConfigService;
__decorate([
    (0, typeorm_transactional_1.Transactional)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [create_system_config_dto_1.CreateSystemConfigDto]),
    __metadata("design:returntype", Promise)
], CreateSystemConfigService.prototype, "create", null);
__decorate([
    (0, typeorm_transactional_1.Transactional)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Array]),
    __metadata("design:returntype", Promise)
], CreateSystemConfigService.prototype, "createOrUpdateBulk", null);
exports.CreateSystemConfigService = CreateSystemConfigService = CreateSystemConfigService_1 = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(system_config_entity_1.SystemConfig)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        typeorm_2.DataSource,
        event_emitter_1.EventEmitter2])
], CreateSystemConfigService);
//# sourceMappingURL=create.system-config.service.js.map
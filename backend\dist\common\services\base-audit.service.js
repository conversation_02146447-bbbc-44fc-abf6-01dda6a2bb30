"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.BaseAuditService = void 0;
const class_transformer_1 = require("class-transformer");
const user_dto_1 = require("../../users/dto/user.dto");
class BaseAuditService {
    mapAuditRelationships(entity, dto) {
        if (entity.creator) {
            dto.creator = (0, class_transformer_1.plainToInstance)(user_dto_1.UserDto, entity.creator, {
                excludeExtraneousValues: true,
            });
        }
        if (entity.updater) {
            dto.updater = (0, class_transformer_1.plainToInstance)(user_dto_1.UserDto, entity.updater, {
                excludeExtraneousValues: true,
            });
        }
        if (entity.deleter) {
            dto.deleter = (0, class_transformer_1.plainToInstance)(user_dto_1.UserDto, entity.deleter, {
                excludeExtraneousValues: true,
            });
        }
    }
    getAuditRelations() {
        return ['creator', 'updater', 'deleter'];
    }
    mergeAuditRelations(relations = [], includeAudit = true) {
        if (!includeAudit) {
            return relations;
        }
        const auditRelations = this.getAuditRelations();
        const mergedRelations = [...relations];
        auditRelations.forEach(relation => {
            if (!mergedRelations.includes(relation)) {
                mergedRelations.push(relation);
            }
        });
        return mergedRelations;
    }
}
exports.BaseAuditService = BaseAuditService;
//# sourceMappingURL=base-audit.service.js.map
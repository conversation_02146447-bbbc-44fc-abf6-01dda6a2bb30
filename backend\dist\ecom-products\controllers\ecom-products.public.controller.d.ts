import { PublicEcomProductsService } from '../services/public.ecom-products.service';
import { EcomProductPublicDto, HomepageSectionsResponseDto } from '../dto/ecom-product.public.dto';
import { CustomPaginationQueryDto } from '../../common/dto/custom-pagination-query.dto';
import { PaginationResponseDto } from '../../common/dto/pagination-response.dto';
export declare class EcomProductsPublicController {
    private readonly ecomProductsService;
    constructor(ecomProductsService: PublicEcomProductsService);
    getHomepageSections(productsPerCategory?: number): Promise<HomepageSectionsResponseDto>;
    findAll(paginationQuery: CustomPaginationQueryDto): Promise<PaginationResponseDto<EcomProductPublicDto>>;
    findById(id: string): Promise<EcomProductPublicDto>;
    findByCode(code: string): Promise<EcomProductPublicDto>;
    findBySlug(slug: string): Promise<EcomProductPublicDto>;
    findByCategory(categorySlug: string, paginationQuery: CustomPaginationQueryDto): Promise<PaginationResponseDto<EcomProductPublicDto>>;
}

import { Repository } from 'typeorm';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { BaseUserService } from './base.user.service';
import { User } from '../entities/user.entity';
import { Role } from '../../roles/entities/role.entity';
import { UserRole } from '../entities/user-role.entity';
import { UpdateUserDto } from '../dto/update.user.dto';
import { UpdateReferralCodeUserDto } from '../dto/update-referral-code.user.dto';
import { UserDto } from '../dto/user.dto';
import { EventsUserGateway } from '../events/events.user.gateway';
import { OptimisticLockingService } from '../../common/services/optimistic-locking.service';
import { ConnectionPoolService } from '../../common/services/connection-pool.service';
export declare class UpdateUserService extends BaseUserService {
    protected readonly userRepository: Repository<User>;
    protected readonly roleRepository: Repository<Role>;
    private readonly userRoleRepository;
    protected readonly eventEmitter: EventEmitter2;
    private readonly userEventsGateway;
    private readonly optimisticLockingService;
    private readonly connectionPoolService;
    constructor(userRepository: Repository<User>, roleRepository: Repository<Role>, userRoleRepository: Repository<UserRole>, eventEmitter: EventEmitter2, userEventsGateway: EventsUserGateway, optimisticLockingService: OptimisticLockingService, connectionPoolService: ConnectionPoolService);
    update(id: string, updateUserDto: UpdateUserDto, userId: string): Promise<UserDto>;
    toggleStatus(id: string, userId: string): Promise<UserDto>;
    updateReferralCode(id: string, updateReferralCodeDto: UpdateReferralCodeUserDto): Promise<UserDto>;
}

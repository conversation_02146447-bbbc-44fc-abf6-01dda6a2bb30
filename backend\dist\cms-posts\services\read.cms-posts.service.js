"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ReadCmsPostsService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const event_emitter_1 = require("@nestjs/event-emitter");
const base_cms_posts_service_1 = require("./base.cms-posts.service");
const cms_posts_entity_1 = require("../entity/cms-posts.entity");
let ReadCmsPostsService = class ReadCmsPostsService extends base_cms_posts_service_1.BaseCmsPostsService {
    postRepository;
    dataSource;
    eventEmitter;
    constructor(postRepository, dataSource, eventEmitter) {
        super(postRepository, dataSource, eventEmitter);
        this.postRepository = postRepository;
        this.dataSource = dataSource;
        this.eventEmitter = eventEmitter;
    }
    async findAll(params) {
        try {
            this.logger.debug(`Đang tìm tất cả bài viết CMS với tham số: ${JSON.stringify(params)}`);
            const { limit, page, search } = params;
            const skip = (page - 1) * limit;
            let relationsArray = params.relationsArray || [];
            const requiredRelations = ['category', 'author', 'creator', 'updater', 'deleter'];
            requiredRelations.forEach(relation => {
                if (!relationsArray.includes(relation)) {
                    relationsArray.push(relation);
                }
            });
            const validatedRelations = this.validateRelations(relationsArray);
            const queryBuilder = this.postRepository
                .createQueryBuilder('post')
                .where('post.isDeleted = :isDeleted', { isDeleted: false });
            if (search) {
                queryBuilder.andWhere(new typeorm_2.Brackets(qb => {
                    qb.where('LOWER(post.title) LIKE LOWER(:search)', { search: `%${search}%` })
                        .orWhere('LOWER(post.excerpt) LIKE LOWER(:search)', { search: `%${search}%` })
                        .orWhere('LOWER(post.content) LIKE LOWER(:search)', { search: `%${search}%` })
                        .orWhere('LOWER(post.slug) LIKE LOWER(:search)', { search: `%${search}%` });
                }));
            }
            const sortOptions = params.sortOptions;
            if (sortOptions && sortOptions.length > 0) {
                const { field, order } = sortOptions[0];
                queryBuilder.orderBy(`post.${field}`, order);
            }
            else {
                queryBuilder.orderBy('post.createdAt', 'DESC');
            }
            validatedRelations.forEach(relation => {
                queryBuilder.leftJoinAndSelect(`post.${relation}`, relation);
            });
            queryBuilder.skip(skip).take(limit);
            const [posts, total] = await queryBuilder.getManyAndCount();
            const postDtos = this.toDtos(posts);
            return {
                data: postDtos,
                total,
            };
        }
        catch (error) {
            this.logger.error(`Lỗi khi tìm tất cả bài viết CMS: ${error.message}`, error.stack);
            if (error instanceof common_1.BadRequestException) {
                throw error;
            }
            throw new common_1.InternalServerErrorException(`Không thể lấy danh sách bài viết CMS: ${error.message}`);
        }
    }
    async findByPostType(postType, params) {
        try {
            this.logger.debug(`Đang tìm bài viết CMS theo loại: ${postType}`);
            const queryBuilder = this.postRepository
                .createQueryBuilder('post')
                .where('post.isDeleted = :isDeleted', { isDeleted: false })
                .andWhere('post.postType = :postType', { postType });
            if (params.search) {
                queryBuilder.andWhere(new typeorm_2.Brackets(qb => {
                    qb.where('LOWER(post.title) LIKE LOWER(:search)', { search: `%${params.search}%` })
                        .orWhere('LOWER(post.excerpt) LIKE LOWER(:search)', { search: `%${params.search}%` })
                        .orWhere('LOWER(post.content) LIKE LOWER(:search)', { search: `%${params.search}%` });
                }));
            }
            const sortOptions = params.sortOptions;
            if (sortOptions && sortOptions.length > 0) {
                const { field, order } = sortOptions[0];
                queryBuilder.orderBy(`post.${field}`, order);
            }
            else {
                queryBuilder.orderBy('post.createdAt', 'DESC');
            }
            queryBuilder.leftJoinAndSelect('post.category', 'category')
                .leftJoinAndSelect('post.author', 'author');
            const skip = (params.page - 1) * params.limit;
            queryBuilder.skip(skip).take(params.limit);
            const [posts, total] = await queryBuilder.getManyAndCount();
            const postDtos = this.toDtos(posts);
            return { data: postDtos, total };
        }
        catch (error) {
            this.logger.error(`Lỗi khi tìm bài viết theo loại: ${error.message}`, error.stack);
            throw new common_1.InternalServerErrorException(`Không thể tìm bài viết theo loại: ${error.message}`);
        }
    }
    async findByStatus(status, params) {
        try {
            this.logger.debug(`Đang tìm bài viết CMS theo trạng thái: ${status}`);
            const queryBuilder = this.postRepository
                .createQueryBuilder('post')
                .where('post.isDeleted = :isDeleted', { isDeleted: false })
                .andWhere('post.status = :status', { status });
            if (params.search) {
                queryBuilder.andWhere(new typeorm_2.Brackets(qb => {
                    qb.where('LOWER(post.title) LIKE LOWER(:search)', { search: `%${params.search}%` })
                        .orWhere('LOWER(post.excerpt) LIKE LOWER(:search)', { search: `%${params.search}%` });
                }));
            }
            const sortOptions = params.sortOptions;
            if (sortOptions && sortOptions.length > 0) {
                const { field, order } = sortOptions[0];
                queryBuilder.orderBy(`post.${field}`, order);
            }
            else {
                queryBuilder.orderBy('post.createdAt', 'DESC');
            }
            queryBuilder.leftJoinAndSelect('post.category', 'category')
                .leftJoinAndSelect('post.author', 'author');
            const skip = (params.page - 1) * params.limit;
            queryBuilder.skip(skip).take(params.limit);
            const [posts, total] = await queryBuilder.getManyAndCount();
            const postDtos = this.toDtos(posts);
            return { data: postDtos, total };
        }
        catch (error) {
            this.logger.error(`Lỗi khi tìm bài viết theo trạng thái: ${error.message}`, error.stack);
            throw new common_1.InternalServerErrorException(`Không thể tìm bài viết theo trạng thái: ${error.message}`);
        }
    }
    async findPublished(params) {
        return this.findByStatus(cms_posts_entity_1.CmsPostStatus.PUBLISHED, params);
    }
    async findDrafts(params) {
        return this.findByStatus(cms_posts_entity_1.CmsPostStatus.DRAFT, params);
    }
    async search(keyword, params) {
        try {
            this.logger.debug(`Đang tìm kiếm bài viết CMS với từ khóa: ${keyword}`);
            const queryBuilder = this.postRepository
                .createQueryBuilder('post')
                .where('post.isDeleted = :isDeleted', { isDeleted: false });
            queryBuilder.andWhere(new typeorm_2.Brackets(qb => {
                qb.where('LOWER(post.title) LIKE LOWER(:keyword)', { keyword: `%${keyword}%` })
                    .orWhere('LOWER(post.excerpt) LIKE LOWER(:keyword)', { keyword: `%${keyword}%` })
                    .orWhere('LOWER(post.content) LIKE LOWER(:keyword)', { keyword: `%${keyword}%` })
                    .orWhere('LOWER(post.slug) LIKE LOWER(:keyword)', { keyword: `%${keyword}%` });
            }));
            const sortOptions = params.sortOptions;
            if (sortOptions && sortOptions.length > 0) {
                const { field, order } = sortOptions[0];
                queryBuilder.orderBy(`post.${field}`, order);
            }
            else {
                queryBuilder.orderBy('post.createdAt', 'DESC');
            }
            queryBuilder.leftJoinAndSelect('post.category', 'category')
                .leftJoinAndSelect('post.author', 'author');
            const skip = (params.page - 1) * params.limit;
            queryBuilder.skip(skip).take(params.limit);
            const [posts, total] = await queryBuilder.getManyAndCount();
            const postDtos = this.toDtos(posts);
            return { data: postDtos, total };
        }
        catch (error) {
            this.logger.error(`Lỗi khi tìm kiếm bài viết CMS: ${error.message}`, error.stack);
            throw new common_1.InternalServerErrorException(`Không thể tìm kiếm bài viết CMS: ${error.message}`);
        }
    }
    async count(filter) {
        try {
            const where = this.buildWhereClause(filter);
            return this.postRepository.count({ where });
        }
        catch (error) {
            this.logger.error(`Lỗi khi đếm số lượng bài viết CMS: ${error.message}`, error.stack);
            throw new common_1.InternalServerErrorException(`Không thể đếm số lượng bài viết CMS: ${error.message}`);
        }
    }
    async findOne(id, relations = []) {
        try {
            this.logger.debug(`Đang tìm bài viết CMS theo ID: ${id}`);
            const validatedRelations = this.validateRelations(relations);
            try {
                const post = await this.findById(id, validatedRelations, true);
                if (!post) {
                    throw new common_1.NotFoundException(`Không tìm thấy bài viết với ID: ${id}`);
                }
                return this.toDto(post);
            }
            catch (notFoundError) {
                if (notFoundError instanceof common_1.NotFoundException) {
                    return null;
                }
                throw notFoundError;
            }
        }
        catch (error) {
            this.logger.error(`Lỗi khi tìm bài viết CMS theo ID ${id}: ${error.message}`, error.stack);
            if (error instanceof common_1.NotFoundException) {
                return null;
            }
            throw new common_1.InternalServerErrorException(`Không thể lấy thông tin bài viết CMS: ${error.message}`);
        }
    }
    async findOneOrFail(id, relations = []) {
        try {
            this.logger.debug(`Đang tìm bài viết CMS theo ID: ${id}`);
            const validatedRelations = this.validateRelations(relations);
            const validRelationsToLoad = [...validatedRelations];
            if (!validRelationsToLoad.includes('category')) {
                validRelationsToLoad.push('category');
            }
            if (!validRelationsToLoad.includes('author')) {
                validRelationsToLoad.push('author');
            }
            const post = await this.findById(id, validRelationsToLoad);
            if (!post) {
                throw new common_1.NotFoundException(`Không tìm thấy bài viết với ID: ${id}`);
            }
            return this.toDto(post);
        }
        catch (error) {
            this.logger.error(`Lỗi khi tìm bài viết CMS theo ID ${id}: ${error.message}`, error.stack);
            if (error instanceof common_1.NotFoundException) {
                throw error;
            }
            throw new common_1.InternalServerErrorException(`Không thể lấy thông tin bài viết CMS: ${error.message}`);
        }
    }
    async findBySlugPublic(slug, postType, incrementView = true) {
        try {
            this.logger.debug(`Đang tìm bài viết CMS theo slug: ${slug}`);
            const postEntity = await this.postRepository
                .createQueryBuilder('post')
                .leftJoinAndSelect('post.category', 'category')
                .where('post.slug = :slug', { slug })
                .andWhere('post.isDeleted = :isDeleted', { isDeleted: false })
                .getOne();
            if (!postEntity || postEntity.status !== cms_posts_entity_1.CmsPostStatus.PUBLISHED) {
                return null;
            }
            if (incrementView) {
                await this.incrementViewCount(postEntity.id);
                postEntity.viewCount += 1;
            }
            return this.toDto(postEntity);
        }
        catch (error) {
            this.logger.error(`Lỗi khi tìm bài viết theo slug: ${error.message}`, error.stack);
            throw new common_1.InternalServerErrorException(`Không thể tìm bài viết theo slug: ${error.message}`);
        }
    }
    async findDeleted(params) {
        try {
            this.logger.debug(`Đang tìm các bài viết CMS đã bị xóa mềm với tham số: ${JSON.stringify(params)}`);
            const { limit, page } = params;
            const queryBuilder = this.postRepository
                .createQueryBuilder('post')
                .where('post.isDeleted = :isDeleted', { isDeleted: true });
            if (params.search) {
                queryBuilder.andWhere(new typeorm_2.Brackets(qb => {
                    qb.where('LOWER(post.title) LIKE LOWER(:search)', { search: `%${params.search}%` })
                        .orWhere('LOWER(post.excerpt) LIKE LOWER(:search)', { search: `%${params.search}%` })
                        .orWhere('LOWER(post.slug) LIKE LOWER(:search)', { search: `%${params.search}%` });
                }));
            }
            const sortOptions = params.sortOptions;
            if (sortOptions && sortOptions.length > 0) {
                const { field, order } = sortOptions[0];
                queryBuilder.orderBy(`post.${field}`, order);
            }
            else {
                queryBuilder.orderBy('post.deletedAt', 'DESC');
            }
            queryBuilder.leftJoinAndSelect('post.category', 'category')
                .leftJoinAndSelect('post.author', 'author')
                .leftJoinAndSelect('post.deleter', 'deleter');
            const skip = (page - 1) * limit;
            queryBuilder.skip(skip).take(limit);
            const [posts, total] = await queryBuilder.getManyAndCount();
            const postDtos = this.toDtos(posts);
            return { data: postDtos, total };
        }
        catch (error) {
            this.logger.error(`Lỗi khi tìm bài viết CMS đã xóa: ${error.message}`, error.stack);
            throw new common_1.InternalServerErrorException(`Không thể lấy danh sách bài viết CMS đã xóa: ${error.message}`);
        }
    }
    async getStatistics() {
        try {
            const total = await this.postRepository.count({
                where: { isDeleted: false },
            });
            const statusCounts = {};
            for (const status of Object.values(cms_posts_entity_1.CmsPostStatus)) {
                statusCounts[status] = await this.postRepository.count({
                    where: { status, isDeleted: false },
                });
            }
            const typeCounts = {};
            for (const type of Object.values(cms_posts_entity_1.CmsPostType)) {
                typeCounts[type] = await this.postRepository.count({
                    where: { postType: type, isDeleted: false },
                });
            }
            const today = new Date();
            today.setHours(0, 0, 0, 0);
            const tomorrow = new Date(today);
            tomorrow.setDate(tomorrow.getDate() + 1);
            const publishedToday = await this.postRepository.count({
                where: {
                    status: cms_posts_entity_1.CmsPostStatus.PUBLISHED,
                    publishedAt: (0, typeorm_2.Between)(today, tomorrow),
                    isDeleted: false,
                },
            });
            const result = await this.postRepository
                .createQueryBuilder('post')
                .select('SUM(post.viewCount)', 'totalViews')
                .where('post.isDeleted = :isDeleted', { isDeleted: false })
                .getRawOne();
            const totalViews = parseInt(result?.totalViews || '0', 10);
            return {
                total,
                statusCounts,
                typeCounts,
                publishedToday,
                totalViews,
            };
        }
        catch (error) {
            this.logger.error(`Lỗi khi lấy thống kê bài viết CMS: ${error.message}`, error.stack);
            throw new common_1.InternalServerErrorException(`Không thể lấy thống kê bài viết CMS: ${error.message}`);
        }
    }
    async findByCategorySlug(categorySlug, params) {
        try {
            this.logger.debug(`Đang tìm bài viết CMS theo slug category: ${categorySlug}`);
            const { limit, page, search, postType } = params;
            const skip = (page - 1) * limit;
            const queryBuilder = this.postRepository
                .createQueryBuilder('post')
                .leftJoinAndSelect('post.category', 'category')
                .where('post.isDeleted = :isDeleted', { isDeleted: false })
                .andWhere('post.status = :status', { status: cms_posts_entity_1.CmsPostStatus.PUBLISHED })
                .andWhere('category.slug = :categorySlug', { categorySlug });
            if (postType) {
                queryBuilder.andWhere('post.postType = :postType', { postType });
            }
            if (search) {
                queryBuilder.andWhere(new typeorm_2.Brackets(qb => {
                    qb.where('LOWER(post.title) LIKE LOWER(:search)', { search: `%${search}%` })
                        .orWhere('LOWER(post.excerpt) LIKE LOWER(:search)', { search: `%${search}%` })
                        .orWhere('LOWER(post.content) LIKE LOWER(:search)', { search: `%${search}%` })
                        .orWhere('LOWER(post.slug) LIKE LOWER(:search)', { search: `%${search}%` });
                }));
            }
            queryBuilder.orderBy('post.publishedAt', 'DESC');
            queryBuilder.skip(skip).take(limit);
            const [posts, total] = await queryBuilder.getManyAndCount();
            const postDtos = this.toDtos(posts);
            return { data: postDtos, total };
        }
        catch (error) {
            this.logger.error(`Lỗi khi tìm bài viết CMS theo slug category: ${error.message}`, error.stack);
            throw new common_1.InternalServerErrorException(`Không thể tìm bài viết theo slug category: ${error.message}`);
        }
    }
    async findByTagSlug(tagSlug, params) {
        try {
            this.logger.debug(`Đang tìm bài viết CMS theo slug tag: ${tagSlug}`);
            const { limit, page, search, postType } = params;
            const skip = (page - 1) * limit;
            const queryBuilder = this.postRepository
                .createQueryBuilder('post')
                .leftJoinAndSelect('post.tags', 'tag')
                .where('post.isDeleted = :isDeleted', { isDeleted: false })
                .andWhere('post.status = :status', { status: cms_posts_entity_1.CmsPostStatus.PUBLISHED })
                .andWhere('tag.slug = :tagSlug', { tagSlug });
            if (postType) {
                queryBuilder.andWhere('post.postType = :postType', { postType });
            }
            if (search) {
                queryBuilder.andWhere(new typeorm_2.Brackets(qb => {
                    qb.where('LOWER(post.title) LIKE LOWER(:search)', { search: `%${search}%` })
                        .orWhere('LOWER(post.excerpt) LIKE LOWER(:search)', { search: `%${search}%` })
                        .orWhere('LOWER(post.content) LIKE LOWER(:search)', { search: `%${search}%` })
                        .orWhere('LOWER(post.slug) LIKE LOWER(:search)', { search: `%${search}%` });
                }));
            }
            queryBuilder.orderBy('post.publishedAt', 'DESC');
            queryBuilder.skip(skip).take(limit);
            const [posts, total] = await queryBuilder.getManyAndCount();
            const postDtos = this.toDtos(posts);
            return { data: postDtos, total };
        }
        catch (error) {
            this.logger.error(`Lỗi khi tìm bài viết CMS theo slug tag: ${error.message}`, error.stack);
            throw new common_1.InternalServerErrorException(`Không thể tìm bài viết theo slug tag: ${error.message}`);
        }
    }
};
exports.ReadCmsPostsService = ReadCmsPostsService;
exports.ReadCmsPostsService = ReadCmsPostsService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(cms_posts_entity_1.CmsPosts)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        typeorm_2.DataSource,
        event_emitter_1.EventEmitter2])
], ReadCmsPostsService);
//# sourceMappingURL=read.cms-posts.service.js.map
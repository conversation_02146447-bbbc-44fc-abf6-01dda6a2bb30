"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UpdateCmsCategoriesController = void 0;
const openapi = require("@nestjs/swagger");
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const jwt_auth_guard_1 = require("../../common/guards/jwt-auth.guard");
const update_cms_categories_service_1 = require("../services/update.cms-categories.service");
const read_cms_categories_service_1 = require("../services/read.cms-categories.service");
const update_cms_category_dto_1 = require("../dto/update.cms-category.dto");
const api_response_dto_1 = require("../../common/dto/api-response.dto");
const get_user_decorator_1 = require("../../common/decorators/get-user.decorator");
const roles_decorator_1 = require("../../common/decorators/roles.decorator");
const permissions_decorator_1 = require("../../common/decorators/permissions.decorator");
let UpdateCmsCategoriesController = class UpdateCmsCategoriesController {
    updateCmsCategoriesService;
    readCmsCategoriesService;
    constructor(updateCmsCategoriesService, readCmsCategoriesService) {
        this.updateCmsCategoriesService = updateCmsCategoriesService;
        this.readCmsCategoriesService = readCmsCategoriesService;
    }
    async update(id, updateCmsCategoryDto, userId) {
        const existingCategory = await this.readCmsCategoriesService.findOne(id);
        if (!existingCategory) {
            throw new common_1.NotFoundException(`Không tìm thấy chuyên mục CMS với ID: ${id}`);
        }
        const result = await this.updateCmsCategoriesService.update(id, updateCmsCategoryDto, userId);
        if (!result) {
            throw new common_1.NotFoundException(`Không tìm thấy chuyên mục CMS với ID: ${id}`);
        }
        return result;
    }
    async bulkUpdate(updates, userId) {
        return this.updateCmsCategoriesService.bulkUpdate(updates, userId);
    }
    async updateStatus(id, status, userId) {
        const result = await this.updateCmsCategoriesService.updateStatus(id, status, userId);
        if (!result) {
            throw new common_1.NotFoundException(`Không tìm thấy chuyên mục CMS với ID: ${id}`);
        }
        return result;
    }
};
exports.UpdateCmsCategoriesController = UpdateCmsCategoriesController;
__decorate([
    (0, common_1.Patch)(':id'),
    (0, roles_decorator_1.Roles)('ADMIN'),
    (0, permissions_decorator_1.Permissions)('cms-category:update'),
    (0, swagger_1.ApiOperation)({ summary: 'Cập nhật thông tin chuyên mục CMS' }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Chuyên mục CMS đã được cập nhật thành công.',
        type: api_response_dto_1.ApiResponseDto,
        schema: {
            properties: {
                data: { $ref: '#/components/schemas/CmsCategoryDto' },
            },
        },
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.NOT_FOUND,
        description: 'Không tìm thấy chuyên mục CMS.',
        type: api_response_dto_1.ApiResponseDto,
        schema: { properties: { data: { type: 'object', nullable: true } } },
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.BAD_REQUEST,
        description: 'Dữ liệu đầu vào không hợp lệ.',
        type: api_response_dto_1.ApiResponseDto,
        schema: { properties: { data: { type: 'object', nullable: true } } },
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.CONFLICT,
        description: 'Slug đã tồn tại.',
        type: api_response_dto_1.ApiResponseDto,
        schema: { properties: { data: { type: 'object', nullable: true } } },
    }),
    (0, swagger_1.ApiParam)({ name: 'id', type: String, description: 'ID của chuyên mục CMS' }),
    (0, swagger_1.ApiBody)({ type: update_cms_category_dto_1.UpdateCmsCategoryDto }),
    openapi.ApiResponse({ status: 200, type: Object }),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __param(1, (0, common_1.Body)()),
    __param(2, (0, get_user_decorator_1.GetUser)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, update_cms_category_dto_1.UpdateCmsCategoryDto, String]),
    __metadata("design:returntype", Promise)
], UpdateCmsCategoriesController.prototype, "update", null);
__decorate([
    (0, common_1.Patch)('bulk'),
    (0, roles_decorator_1.Roles)('ADMIN'),
    (0, permissions_decorator_1.Permissions)('cms-category:update'),
    (0, swagger_1.ApiOperation)({ summary: 'Cập nhật nhiều chuyên mục CMS cùng lúc' }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Các chuyên mục CMS đã được cập nhật thành công.',
        type: api_response_dto_1.ApiResponseDto,
        schema: {
            properties: {
                data: {
                    type: 'array',
                    items: { $ref: '#/components/schemas/CmsCategoryDto' },
                },
            },
        },
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.BAD_REQUEST,
        description: 'Dữ liệu đầu vào không hợp lệ.',
        type: api_response_dto_1.ApiResponseDto,
        schema: { properties: { data: { type: 'object', nullable: true } } },
    }),
    (0, swagger_1.ApiBody)({
        schema: {
            type: 'array',
            items: {
                type: 'object',
                properties: {
                    id: { type: 'string', format: 'uuid' },
                    data: { $ref: '#/components/schemas/UpdateCmsCategoryDto' },
                },
                required: ['id', 'data'],
            },
        },
    }),
    openapi.ApiResponse({ status: 200, type: [require("../dto/cms-category.dto").CmsCategoryDto] }),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, get_user_decorator_1.GetUser)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Array, String]),
    __metadata("design:returntype", Promise)
], UpdateCmsCategoriesController.prototype, "bulkUpdate", null);
__decorate([
    (0, common_1.Patch)(':id/status'),
    (0, roles_decorator_1.Roles)('ADMIN'),
    (0, permissions_decorator_1.Permissions)('cms-category:update'),
    (0, swagger_1.ApiOperation)({ summary: 'Cập nhật trạng thái chuyên mục CMS' }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Trạng thái chuyên mục CMS đã được cập nhật thành công.',
        type: api_response_dto_1.ApiResponseDto,
        schema: {
            properties: {
                data: { $ref: '#/components/schemas/CmsCategoryDto' },
            },
        },
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.NOT_FOUND,
        description: 'Không tìm thấy chuyên mục CMS.',
        type: api_response_dto_1.ApiResponseDto,
        schema: { properties: { data: { type: 'object', nullable: true } } },
    }),
    (0, swagger_1.ApiParam)({ name: 'id', type: String, description: 'ID của chuyên mục CMS' }),
    (0, swagger_1.ApiBody)({
        schema: {
            type: 'object',
            properties: {
                status: {
                    type: 'string',
                    enum: ['active', 'inactive'],
                    description: 'Trạng thái mới của chuyên mục',
                },
            },
            required: ['status'],
        },
    }),
    openapi.ApiResponse({ status: 200, type: Object }),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __param(1, (0, common_1.Body)('status')),
    __param(2, (0, get_user_decorator_1.GetUser)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String, String]),
    __metadata("design:returntype", Promise)
], UpdateCmsCategoriesController.prototype, "updateStatus", null);
exports.UpdateCmsCategoriesController = UpdateCmsCategoriesController = __decorate([
    (0, swagger_1.ApiTags)('cms-categories'),
    (0, swagger_1.ApiBearerAuth)(),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, common_1.Controller)('cms/categories'),
    __metadata("design:paramtypes", [update_cms_categories_service_1.UpdateCmsCategoriesService,
        read_cms_categories_service_1.ReadCmsCategoriesService])
], UpdateCmsCategoriesController);
//# sourceMappingURL=update.cms-categories.controller.js.map
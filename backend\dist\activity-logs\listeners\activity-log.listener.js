"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var ActivityLogListener_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.ActivityLogListener = void 0;
const common_1 = require("@nestjs/common");
const event_emitter_1 = require("@nestjs/event-emitter");
const create_activity_log_service_1 = require("../services/create.activity-log.service");
const activity_log_event_constants_1 = require("../constants/activity-log-event.constants");
let ActivityLogListener = ActivityLogListener_1 = class ActivityLogListener {
    createActivityLogService;
    logger = new common_1.Logger(ActivityLogListener_1.name);
    constructor(createActivityLogService) {
        this.createActivityLogService = createActivityLogService;
    }
    async handleActivityEvent(payload) {
        this.logger.debug(`Xử lý sự kiện hoạt động: ${JSON.stringify(payload)}`);
        await this.createActivityLog({
            userId: payload.userId,
            action: payload.action,
            module: payload.module,
            description: payload.description,
            ipAddress: payload.ipAddress,
            userAgent: payload.userAgent,
            createdBy: payload.createdBy || payload.userId,
            metadata: payload.metadata ? JSON.stringify(payload.metadata) : undefined,
        });
    }
    async handleAllActivityEvents(payload) {
        this.logger.debug(`Xử lý sự kiện hoạt động cụ thể: ${JSON.stringify(payload)}`);
        await this.createActivityLog({
            userId: payload.userId,
            action: payload.action,
            module: payload.module,
            description: payload.description,
            ipAddress: payload.ipAddress,
            userAgent: payload.userAgent,
            createdBy: payload.createdBy || payload.userId,
            metadata: payload.metadata ? JSON.stringify(payload.metadata) : undefined,
        });
    }
    async handleLoginEvent(payload) {
        this.logger.debug(`Xử lý sự kiện đăng nhập: ${JSON.stringify(payload)}`);
        await this.createActivityLog({
            userId: payload.userId,
            action: 'LOGIN',
            module: 'auth',
            description: payload.description || 'Đăng nhập thành công',
            ipAddress: payload.ipAddress,
            userAgent: payload.userAgent,
            createdBy: payload.userId,
            metadata: payload.metadata ? JSON.stringify(payload.metadata) : undefined,
        });
    }
    async handleLogoutEvent(payload) {
        this.logger.debug(`Xử lý sự kiện đăng xuất: ${JSON.stringify(payload)}`);
        await this.createActivityLog({
            userId: payload.userId,
            action: 'LOGOUT',
            module: 'auth',
            description: payload.description || 'Đăng xuất thành công',
            ipAddress: payload.ipAddress,
            userAgent: payload.userAgent,
            createdBy: payload.userId,
            metadata: payload.metadata ? JSON.stringify(payload.metadata) : undefined,
        });
    }
    async handleFailedLoginEvent(payload) {
        this.logger.debug(`Xử lý sự kiện đăng nhập thất bại: ${JSON.stringify(payload)}`);
        await this.createActivityLog({
            userId: payload.userId,
            action: 'FAILED_LOGIN',
            module: 'auth',
            description: payload.description || 'Đăng nhập thất bại',
            ipAddress: payload.ipAddress,
            userAgent: payload.userAgent,
            createdBy: payload.userId,
            metadata: payload.metadata ? JSON.stringify(payload.metadata) : undefined,
        });
    }
    async createActivityLog(data) {
        try {
            await this.createActivityLogService.create(data);
        }
        catch (error) {
            this.logger.error(`Lỗi khi tạo activity log: ${error.message}`, error.stack);
        }
    }
};
exports.ActivityLogListener = ActivityLogListener;
__decorate([
    (0, event_emitter_1.OnEvent)(activity_log_event_constants_1.ACTIVITY_EVENTS.ACTIVITY),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], ActivityLogListener.prototype, "handleActivityEvent", null);
__decorate([
    (0, event_emitter_1.OnEvent)('activity.*'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], ActivityLogListener.prototype, "handleAllActivityEvents", null);
__decorate([
    (0, event_emitter_1.OnEvent)(activity_log_event_constants_1.ACTIVITY_EVENTS.AUTH_LOGIN),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], ActivityLogListener.prototype, "handleLoginEvent", null);
__decorate([
    (0, event_emitter_1.OnEvent)(activity_log_event_constants_1.ACTIVITY_EVENTS.AUTH_LOGOUT),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], ActivityLogListener.prototype, "handleLogoutEvent", null);
__decorate([
    (0, event_emitter_1.OnEvent)(activity_log_event_constants_1.ACTIVITY_EVENTS.AUTH_FAILED_LOGIN),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], ActivityLogListener.prototype, "handleFailedLoginEvent", null);
exports.ActivityLogListener = ActivityLogListener = ActivityLogListener_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [create_activity_log_service_1.CreateActivityLogService])
], ActivityLogListener);
//# sourceMappingURL=activity-log.listener.js.map
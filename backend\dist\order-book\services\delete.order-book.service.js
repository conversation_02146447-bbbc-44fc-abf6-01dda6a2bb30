"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.DeleteOrderBookService = void 0;
const common_1 = require("@nestjs/common");
const event_emitter_1 = require("@nestjs/event-emitter");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const typeorm_transactional_1 = require("typeorm-transactional");
const order_book_detail_entity_1 = require("../entities/order-book-detail.entity");
const order_book_entity_1 = require("../entities/order-book.entity");
const base_order_book_service_1 = require("./base.order-book.service");
let DeleteOrderBookService = class DeleteOrderBookService extends base_order_book_service_1.BaseOrderBookService {
    orderBookRepository;
    orderBookDetailRepository;
    eventEmitter;
    constructor(orderBookRepository, orderBookDetailRepository, eventEmitter) {
        super(orderBookRepository, eventEmitter);
        this.orderBookRepository = orderBookRepository;
        this.orderBookDetailRepository = orderBookDetailRepository;
        this.eventEmitter = eventEmitter;
    }
    async remove(id) {
        try {
            this.logger.log(`Đang xóa vĩnh viễn lệnh với ID: ${id}`);
            const orderBook = await this.orderBookRepository.findOne({
                where: { id },
                relations: ['details'],
            });
            if (!orderBook) {
                throw new common_1.NotFoundException(`Không tìm thấy lệnh với ID ${id}`);
            }
            if (orderBook.details && orderBook.details.length > 0) {
                await this.orderBookDetailRepository.remove(orderBook.details);
            }
            await this.orderBookRepository.remove(orderBook);
            this.eventEmitter.emit(this.EVENT_ORDER_DELETED, {
                id,
                isSoftDelete: false
            });
            this.logger.log(`Đã xóa thành công lệnh với ID: ${id}`);
        }
        catch (error) {
            this.logger.error(`Lỗi khi xóa lệnh: ${error.message}`, error.stack);
            if (error instanceof common_1.NotFoundException) {
                throw error;
            }
            throw new common_1.InternalServerErrorException(`Không thể xóa lệnh: ${error.message}`);
        }
    }
    async bulkDelete(ids) {
        try {
            this.logger.log(`Đang xóa hàng loạt lệnh với các ID: ${ids.join(', ')}`);
            if (!ids.length) {
                return { affected: 0 };
            }
            await this.orderBookDetailRepository.delete({ orderBookId: (0, typeorm_2.In)(ids) });
            const result = await this.orderBookRepository.delete(ids);
            const affectedCount = result.affected || 0;
            if (affectedCount > 0) {
                ids.forEach(id => {
                    this.eventEmitter.emit(this.EVENT_ORDER_DELETED, {
                        id,
                        isSoftDelete: false
                    });
                });
            }
            this.logger.log(`Đã xóa thành công ${affectedCount} lệnh`);
            return { affected: affectedCount };
        }
        catch (error) {
            this.logger.error(`Lỗi khi xóa hàng loạt lệnh: ${error.message}`, error.stack);
            throw new common_1.InternalServerErrorException(`Không thể xóa hàng loạt lệnh: ${error.message}`);
        }
    }
    async softDelete(id, userId) {
        try {
            this.logger.log(`Đang xóa mềm lệnh với ID: ${id}`);
            const orderBook = await this.orderBookRepository.findOne({
                where: { id, isDeleted: false },
                relations: ['details', 'details.token', 'user'],
            });
            if (!orderBook) {
                throw new common_1.NotFoundException(`Không tìm thấy lệnh với ID ${id}`);
            }
            await this.orderBookRepository.update(id, {
                isDeleted: true,
                deletedBy: userId,
                updatedBy: userId,
            });
            const deletedOrderBook = await this.orderBookRepository.findOne({
                where: { id },
                relations: ['details', 'details.token', 'user'],
                withDeleted: true,
            });
            if (!deletedOrderBook) {
                throw new common_1.NotFoundException(`Không thể tìm thấy lệnh sau khi xóa mềm`);
            }
            const dto = this.toDto(deletedOrderBook, ['details', 'details.token', 'user']);
            this.eventEmitter.emit(this.EVENT_ORDER_DELETED, {
                id,
                isSoftDelete: true
            });
            this.logger.log(`Đã xóa mềm thành công lệnh với ID: ${id}`);
            return dto;
        }
        catch (error) {
            this.logger.error(`Lỗi khi xóa mềm lệnh: ${error.message}`, error.stack);
            if (error instanceof common_1.NotFoundException) {
                throw error;
            }
            throw new common_1.InternalServerErrorException(`Không thể xóa mềm lệnh: ${error.message}`);
        }
    }
    async bulkSoftDelete(ids, userId) {
        try {
            this.logger.log(`Đang xóa mềm hàng loạt lệnh với các ID: ${ids.join(', ')}`);
            if (ids.length === 0) {
                return { affected: 0, dtos: [] };
            }
            const result = await this.orderBookRepository.update(ids, {
                isDeleted: true,
                deletedBy: userId,
                updatedBy: userId,
            });
            const affectedCount = result.affected || 0;
            let dtos = [];
            if (affectedCount > 0) {
                const deletedEntities = await this.orderBookRepository.find({
                    where: { id: (0, typeorm_2.In)(ids) },
                    relations: ['details', 'details.token', 'user'],
                    withDeleted: true,
                });
                dtos = deletedEntities
                    .filter(e => e.isDeleted)
                    .map(entity => this.toDto(entity, ['details', 'details.token', 'user']));
                dtos.forEach(dto => {
                    this.eventEmitter.emit(this.EVENT_ORDER_DELETED, {
                        id: dto.id,
                        isSoftDelete: true
                    });
                });
            }
            this.logger.log(`Đã xóa mềm thành công ${affectedCount} lệnh`);
            return { affected: affectedCount, dtos };
        }
        catch (error) {
            this.logger.error(`Lỗi khi xóa mềm hàng loạt lệnh: ${error.message}`, error.stack);
            throw new common_1.InternalServerErrorException(`Không thể xóa mềm hàng loạt lệnh: ${error.message}`);
        }
    }
    async restore(id, userId) {
        try {
            this.logger.log(`Đang khôi phục lệnh với ID: ${id}`);
            const orderBook = await this.orderBookRepository.findOne({
                where: { id, isDeleted: true },
                relations: ['details', 'details.token', 'user'],
            });
            if (!orderBook) {
                throw new common_1.NotFoundException('Lệnh không tồn tại hoặc chưa được xóa mềm');
            }
            await this.orderBookRepository.update(id, {
                isDeleted: false,
                deletedBy: undefined,
                updatedBy: userId,
            });
            const restoredOrderBook = await this.orderBookRepository.findOne({
                where: { id },
                relations: ['details', 'details.token', 'user'],
            });
            if (!restoredOrderBook) {
                throw new common_1.NotFoundException(`Không thể tìm thấy lệnh sau khi khôi phục`);
            }
            const dto = this.toDto(restoredOrderBook, ['details', 'details.token', 'user']);
            this.eventEmitter.emit(this.EVENT_ORDER_RESTORED, dto);
            this.logger.log(`Đã khôi phục thành công lệnh với ID: ${id}`);
            return dto;
        }
        catch (error) {
            this.logger.error(`Lỗi khi khôi phục lệnh: ${error.message}`, error.stack);
            if (error instanceof common_1.NotFoundException) {
                throw error;
            }
            throw new common_1.InternalServerErrorException(`Không thể khôi phục lệnh: ${error.message}`);
        }
    }
    async cleanupOldRecords(days) {
        try {
            this.logger.log(`Đang dọn dẹp các bản ghi lệnh cũ hơn ${days} ngày`);
            const date = new Date();
            date.setDate(date.getDate() - days);
            const ordersToDelete = await this.orderBookRepository.find({
                where: {
                    createdAt: (0, typeorm_2.LessThan)(date),
                    isDeleted: true,
                },
                select: ['id'],
            });
            if (ordersToDelete.length === 0) {
                return 0;
            }
            const orderIds = ordersToDelete.map(order => order.id);
            await this.orderBookDetailRepository.delete({
                orderBookId: (0, typeorm_2.In)(orderIds),
            });
            const result = await this.orderBookRepository.delete({
                id: (0, typeorm_2.In)(orderIds),
            });
            const affectedCount = result.affected || 0;
            if (affectedCount > 0) {
                this.eventEmitter.emit(this.EVENT_ORDER_CLEANUP, {
                    count: affectedCount,
                    olderThanDays: days,
                    timestamp: new Date(),
                });
            }
            this.logger.log(`Đã dọn dẹp ${affectedCount} bản ghi lệnh cũ`);
            return affectedCount;
        }
        catch (error) {
            this.logger.error(`Lỗi khi dọn dẹp các bản ghi lệnh cũ: ${error.message}`, error.stack);
            throw new common_1.InternalServerErrorException(`Không thể dọn dẹp các bản ghi lệnh cũ: ${error.message}`);
        }
    }
};
exports.DeleteOrderBookService = DeleteOrderBookService;
__decorate([
    (0, typeorm_transactional_1.Transactional)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], DeleteOrderBookService.prototype, "remove", null);
__decorate([
    (0, typeorm_transactional_1.Transactional)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Array]),
    __metadata("design:returntype", Promise)
], DeleteOrderBookService.prototype, "bulkDelete", null);
__decorate([
    (0, typeorm_transactional_1.Transactional)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String]),
    __metadata("design:returntype", Promise)
], DeleteOrderBookService.prototype, "softDelete", null);
__decorate([
    (0, typeorm_transactional_1.Transactional)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Array, String]),
    __metadata("design:returntype", Promise)
], DeleteOrderBookService.prototype, "bulkSoftDelete", null);
__decorate([
    (0, typeorm_transactional_1.Transactional)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String]),
    __metadata("design:returntype", Promise)
], DeleteOrderBookService.prototype, "restore", null);
__decorate([
    (0, typeorm_transactional_1.Transactional)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", Promise)
], DeleteOrderBookService.prototype, "cleanupOldRecords", null);
exports.DeleteOrderBookService = DeleteOrderBookService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(order_book_entity_1.OrderBook)),
    __param(1, (0, typeorm_1.InjectRepository)(order_book_detail_entity_1.OrderBookDetail)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        typeorm_2.Repository,
        event_emitter_1.EventEmitter2])
], DeleteOrderBookService);
//# sourceMappingURL=delete.order-book.service.js.map
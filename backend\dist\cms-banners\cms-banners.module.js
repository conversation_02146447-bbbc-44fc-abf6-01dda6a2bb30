"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CmsBannersModule = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const cms_banners_entity_1 = require("./entity/cms-banners.entity");
const user_entity_1 = require("../users/entities/user.entity");
const base_cms_banners_service_1 = require("./services/base.cms-banners.service");
const create_cms_banners_service_1 = require("./services/create.cms-banners.service");
const read_cms_banners_service_1 = require("./services/read.cms-banners.service");
const update_cms_banners_service_1 = require("./services/update.cms-banners.service");
const delete_cms_banners_service_1 = require("./services/delete.cms-banners.service");
const controllers_1 = require("./controllers");
let CmsBannersModule = class CmsBannersModule {
};
exports.CmsBannersModule = CmsBannersModule;
exports.CmsBannersModule = CmsBannersModule = __decorate([
    (0, common_1.Module)({
        imports: [
            typeorm_1.TypeOrmModule.forFeature([cms_banners_entity_1.CmsBanners, user_entity_1.User]),
        ],
        controllers: [
            controllers_1.CreateCmsBannersController,
            controllers_1.ReadCmsBannersController,
            controllers_1.ReadCmsBannersPublicController,
            controllers_1.UpdateCmsBannersController,
            controllers_1.DeleteCmsBannersController,
        ],
        providers: [
            base_cms_banners_service_1.BaseCmsBannersService,
            create_cms_banners_service_1.CreateCmsBannersService,
            read_cms_banners_service_1.ReadCmsBannersService,
            update_cms_banners_service_1.UpdateCmsBannersService,
            delete_cms_banners_service_1.DeleteCmsBannersService,
        ],
        exports: [
            typeorm_1.TypeOrmModule,
            base_cms_banners_service_1.BaseCmsBannersService,
            create_cms_banners_service_1.CreateCmsBannersService,
            read_cms_banners_service_1.ReadCmsBannersService,
            update_cms_banners_service_1.UpdateCmsBannersService,
            delete_cms_banners_service_1.DeleteCmsBannersService,
        ],
    })
], CmsBannersModule);
//# sourceMappingURL=cms-banners.module.js.map
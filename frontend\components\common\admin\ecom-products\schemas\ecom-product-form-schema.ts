import { z } from "zod";

/**
 * Schema cho form tạo mới sản phẩm
 * Dựa trên CreateEcomProductDto từ backend
 */
export const ecomProductFormSchema = z.object({
  productCode: z
    .string()
    .min(2, { message: "Mã sản phẩm phải có ít nhất 2 ký tự" })
    .max(50, { message: "Mã sản phẩm không được vượt quá 50 ký tự" }),
  productName: z
    .string()
    .min(2, { message: "Tên sản phẩm phải có ít nhất 2 ký tự" })
    .max(100, { message: "Tên sản phẩm không được vượt quá 100 ký tự" }),
  categoryId: z
    .string({ required_error: "Vui lòng chọn danh mục" })
    .min(1, { message: "<PERSON><PERSON> lòng chọn danh mục" }),
  description: z
    .string()
    .max(1000, { message: "<PERSON><PERSON> tả không được vượt quá 1000 ký tự" })
    .optional()
    .nullable()
    .or(z.literal("")),
  weight: z
    .string()
    .transform((val) => (val === '' ? null : Number(parseFloat(val).toFixed(3))))
    .nullable()
    .optional(),
  imageUrl: z
    .string()
    .optional()
    .nullable()
    .or(z.literal("")),
  regularPrice: z
    .string()
    .transform((val) => (val === '' ? 0 : Number(val)))
    .pipe(z.number()
      .min(0, { message: "Giá bán thông thường không được nhỏ hơn 0" })
    ),
  salePrice: z
    .string()
    .transform((val) => (val === '' ? null : Number(val)))
    .pipe(z.number()
      .min(0, { message: "Giá khuyến mãi không được nhỏ hơn 0" })
      .nullable()
    )
    .nullable(),
  stockQuantity: z
    .string()
    .transform((val) => (val === '' ? 0 : Number(val)))
    .pipe(z.number()
      .int({ message: "Số lượng tồn kho phải là số nguyên" })
      .min(0, { message: "Số lượng tồn kho không được nhỏ hơn 0" })
    ),
  isActive: z.boolean().default(true),
});

/**
 * Schema cho form cập nhật sản phẩm
 * Dựa trên UpdateEcomProductDto từ backend
 */
export const ecomProductUpdateFormSchema = ecomProductFormSchema.extend({
  id: z.string({ required_error: "ID sản phẩm là bắt buộc" }),
  updatedBy: z.string().optional().nullable().or(z.literal("")),
});

/**
 * Interface cho output của schema tạo mới
 */
export interface EcomProductFormOutput {
  productCode: string;
  productName: string;
  categoryId: string;
  description?: string | null;
  weight?: number | null;
  imageUrl?: string | null;
  regularPrice: number;
  salePrice?: number | null;
  stockQuantity: number;
  isActive: boolean;
}

/**
 * Interface cho output của schema cập nhật
 */
export interface EcomProductUpdateFormOutput extends EcomProductFormOutput {
  id: string;
  updatedBy?: string | null;
}

// Định nghĩa các kiểu dữ liệu cho form
export type EcomProductFormValues = z.input<typeof ecomProductFormSchema>;
export type EcomProductFormSubmitValues = z.output<typeof ecomProductFormSchema>;
export type EcomProductUpdateFormValues = z.input<typeof ecomProductUpdateFormSchema>;
export type EcomProductUpdateFormSubmitValues = z.output<typeof ecomProductUpdateFormSchema>;

/**
 * Kiểm tra logic: salePrice phải nhỏ hơn hoặc bằng regularPrice
 */
export const validateProductPrice = (values: EcomProductFormValues | EcomProductUpdateFormValues) => {
  if (values.salePrice !== undefined && values.regularPrice !== undefined &&
      values.salePrice !== '' && values.regularPrice !== '') {
    if (Number(values.salePrice) > Number(values.regularPrice)) {
      return {
        salePrice: "Giá khuyến mãi phải nhỏ hơn hoặc bằng giá bán thông thường"
      };
    }
  }
  return {};
};
"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var WalletDepositController_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.WalletDepositController = exports.CreateDepositDto = void 0;
const openapi = require("@nestjs/swagger");
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const jwt_auth_guard_1 = require("../../auth/guards/jwt-auth.guard");
const wallet_vnpay_service_1 = require("../services/wallet-vnpay.service");
const client_ip_decorator_1 = require("../../common/decorators/client-ip.decorator");
class CreateDepositDto {
    amount;
    description;
    bankCode;
}
exports.CreateDepositDto = CreateDepositDto;
let WalletDepositController = WalletDepositController_1 = class WalletDepositController {
    walletVnpayService;
    logger = new common_1.Logger(WalletDepositController_1.name);
    constructor(walletVnpayService) {
        this.walletVnpayService = walletVnpayService;
    }
    async createVnpayDeposit(createDepositDto, req, clientIp) {
        try {
            this.logger.log(`User ${req.user.id} creating VNPAY deposit: ${createDepositDto.amount} VND`);
            if (!createDepositDto.amount || createDepositDto.amount <= 0) {
                throw new common_1.BadRequestException('Số tiền phải lớn hơn 0');
            }
            if (createDepositDto.amount < 10000) {
                throw new common_1.BadRequestException('Số tiền nạp tối thiểu là 10,000 VND');
            }
            if (createDepositDto.amount > ********) {
                throw new common_1.BadRequestException('Số tiền nạp tối đa là 50,000,000 VND');
            }
            const depositRequest = {
                userId: req.user.id,
                amount: createDepositDto.amount,
                description: createDepositDto.description,
                clientIp: clientIp || req.ip,
                bankCode: createDepositDto.bankCode,
            };
            const result = await this.walletVnpayService.createDeposit(depositRequest);
            this.logger.log(`VNPAY deposit created successfully for user ${req.user.id}: ${result.merchantTxnRef}`);
            return result;
        }
        catch (error) {
            this.logger.error(`Error creating VNPAY deposit: ${error.message}`, error.stack);
            throw error;
        }
    }
    async getVnpayDepositStatus(merchantTxnRef, req) {
        try {
            this.logger.log(`User ${req.user.id} checking deposit status: ${merchantTxnRef}`);
            const status = await this.walletVnpayService.getDepositStatus(merchantTxnRef);
            return status;
        }
        catch (error) {
            this.logger.error(`Error getting deposit status: ${error.message}`, error.stack);
            throw error;
        }
    }
    async getVnpayBanks() {
        return this.walletVnpayService.getBankList();
    }
};
exports.WalletDepositController = WalletDepositController;
__decorate([
    (0, common_1.Post)('vnpay'),
    (0, swagger_1.ApiOperation)({ summary: 'Tạo yêu cầu nạp tiền qua VNPAY' }),
    (0, swagger_1.ApiResponse)({ status: 201, description: 'Tạo yêu cầu nạp tiền thành công' }),
    (0, swagger_1.ApiResponse)({ status: 400, description: 'Dữ liệu không hợp lệ' }),
    openapi.ApiResponse({ status: 201, type: Object }),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, common_1.Request)()),
    __param(2, (0, client_ip_decorator_1.GetClientIp)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [CreateDepositDto, Object, String]),
    __metadata("design:returntype", Promise)
], WalletDepositController.prototype, "createVnpayDeposit", null);
__decorate([
    (0, common_1.Get)('vnpay/status/:merchantTxnRef'),
    (0, swagger_1.ApiOperation)({ summary: 'Lấy trạng thái giao dịch nạp tiền VNPAY' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Lấy trạng thái thành công' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Không tìm thấy giao dịch' }),
    openapi.ApiResponse({ status: 200, type: Object }),
    __param(0, (0, common_1.Param)('merchantTxnRef')),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], WalletDepositController.prototype, "getVnpayDepositStatus", null);
__decorate([
    (0, common_1.Get)('vnpay/banks'),
    (0, swagger_1.ApiOperation)({ summary: 'Lấy danh sách ngân hàng hỗ trợ VNPAY' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Lấy danh sách ngân hàng thành công' }),
    openapi.ApiResponse({ status: 200, type: [Object] }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], WalletDepositController.prototype, "getVnpayBanks", null);
exports.WalletDepositController = WalletDepositController = WalletDepositController_1 = __decorate([
    (0, swagger_1.ApiTags)('Wallet Deposit'),
    (0, common_1.Controller)('wallets/deposit'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, swagger_1.ApiBearerAuth)(),
    __metadata("design:paramtypes", [wallet_vnpay_service_1.WalletVnpayService])
], WalletDepositController);
//# sourceMappingURL=wallet-deposit.controller.js.map
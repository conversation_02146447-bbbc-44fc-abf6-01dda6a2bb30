"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var WsJwtGuard_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.WsJwtGuard = void 0;
const common_1 = require("@nestjs/common");
const jwt_1 = require("@nestjs/jwt");
const config_1 = require("@nestjs/config");
let WsJwtGuard = WsJwtGuard_1 = class WsJwtGuard {
    jwtService;
    configService;
    logger = new common_1.Logger(WsJwtGuard_1.name);
    constructor(jwtService, configService) {
        this.jwtService = jwtService;
        this.configService = configService;
    }
    canActivate(context) {
        const client = context.switchToWs().getClient();
        const token = this.extractToken(client);
        if (!token) {
            this.logger.warn(`Client ${client.id}: No token provided.`);
            this.disconnectClient(client, 'Unauthorized: No token provided.');
            return false;
        }
        try {
            const payload = this.jwtService.verify(token, {
                secret: this.configService.get('JWT_SECRET'),
            });
            client.data.user = payload;
            const userRoom = `user_${payload.userId}`;
            client.join(userRoom);
            this.logger.verbose(`Client Authenticated & Joined Room: ${client.id}, UserID: ${payload.userId}, Room: ${userRoom}`);
            return true;
        }
        catch (error) {
            this.logger.warn(`Client ${client.id}: WebSocket Auth Error - ${error.message}`);
            this.disconnectClient(client, error.message || 'Unauthorized');
            return false;
        }
    }
    extractToken(client) {
        const authHeader = client.handshake.headers.authorization;
        let token = undefined;
        if (authHeader && typeof authHeader === 'string') {
            const parts = authHeader.split(' ');
            if (parts.length === 2 && parts[0] === 'Bearer') {
                token = parts[1];
            }
        }
        if (!token && client.handshake.auth) {
            token = client.handshake.auth.token;
        }
        return token;
    }
    disconnectClient(client, message) {
        client.emit('error', { message: message, code: 401 });
        client.disconnect(true);
    }
};
exports.WsJwtGuard = WsJwtGuard;
exports.WsJwtGuard = WsJwtGuard = WsJwtGuard_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [jwt_1.JwtService,
        config_1.ConfigService])
], WsJwtGuard);
//# sourceMappingURL=ws-jwt.guard.js.map
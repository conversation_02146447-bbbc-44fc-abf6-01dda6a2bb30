import { BaseActivityLogService } from './base.activity-log.service';
import { ActivityLogDto } from '../dto/activity-log.dto';
import { UpdateActivityLogDto } from '../dto/update-activity-log.dto';
export declare class UpdateActivityLogService extends BaseActivityLogService {
    update(id: string, updateActivityLogDto: UpdateActivityLogDto, userId?: string): Promise<ActivityLogDto>;
    bulkUpdate(updateActivityLogDtos: UpdateActivityLogDto[], userId?: string): Promise<ActivityLogDto[]>;
}

{"version": 3, "file": "order-book.service.js", "sourceRoot": "", "sources": ["../../src/order-book/order-book.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;AAIA,2CAMwB;AACxB,6CAAmD;AACnD,yDAAoD;AACpD,qCAAqC;AAKrC,yDAAoD;AAEpD,oEAAyD;AAEzD,yCAKoB;AAMb,IAAM,gBAAgB,wBAAtB,MAAM,gBAAgB;IAcR;IACA;IACA;IACA;IACA;IAjBF,MAAM,GAAG,IAAI,eAAM,CAAC,kBAAgB,CAAC,IAAI,CAAC,CAAC;IACpD,cAAc,GAAG,CAAC,MAAM,EAAE,OAAO,EAAE,UAAU,CAAC,CAAC;IAUvD,YAEmB,mBAA0C,EAC1C,aAAqC,EACrC,WAAiC,EACjC,aAAqC,EACrC,aAAqC;QAJrC,wBAAmB,GAAnB,mBAAmB,CAAuB;QAC1C,kBAAa,GAAb,aAAa,CAAwB;QACrC,gBAAW,GAAX,WAAW,CAAsB;QACjC,kBAAa,GAAb,aAAa,CAAwB;QACrC,kBAAa,GAAb,aAAa,CAAwB;IACrD,CAAC;IAGI,YAAY,CAAC,MAAwB;QAC3C,IAAI,CAAC,MAAM,EAAE,CAAC;YACZ,MAAM,IAAI,0BAAiB,CAAC,iBAAiB,CAAC,CAAC;QACjD,CAAC;QACD,OAAO,IAAA,mCAAe,EAAC,6BAAY,EAAE,MAAM,EAAE;YAC3C,uBAAuB,EAAE,IAAI;SAC9B,CAAC,CAAC;IACL,CAAC;IAEO,gBAAgB,CAAC,QAAqB;QAC5C,IAAI,CAAC,QAAQ,EAAE,MAAM;YAAE,OAAO,EAAE,CAAC;QACjC,OAAO,QAAQ,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC,CAAC;IAC7D,CAAC;IAGO,KAAK,CAAC,QAAQ,CAAC,EAAU;QAC/B,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,mBAAmB,CAAC,OAAO,CAAC;YACpD,KAAK,EAAE,EAAE,EAAE,EAAE,SAAS,EAAE,KAAK,EAAE;YAC/B,SAAS,EAAE,IAAI,CAAC,cAAc;SAC/B,CAAC,CAAC;QACH,IAAI,CAAC,MAAM,EAAE,CAAC;YACZ,MAAM,IAAI,0BAAiB,CAAC,iBAAiB,EAAE,YAAY,CAAC,CAAC;QAC/D,CAAC;QACD,OAAO,MAAM,CAAC;IAChB,CAAC;IASD,KAAK,CAAC,MAAM,CACV,kBAAsC,EACtC,MAAc;QAEd,OAAO,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,kBAAkB,EAAE,MAAM,CAAC,CAAC;IAC/D,CAAC;IAQD,KAAK,CAAC,UAAU,CACd,mBAAyC,EACzC,MAAc;QAEd,OAAO,IAAI,CAAC,aAAa,CAAC,UAAU,CAAC,mBAAmB,EAAE,MAAM,CAAC,CAAC;IACpE,CAAC;IAQD,KAAK,CAAC,SAAS,CAAC,EAAU,EAAE,MAAc;QACxC,OAAO,IAAI,CAAC,aAAa,CAAC,SAAS,CAAC,EAAE,EAAE,MAAM,CAAC,CAAC;IAClD,CAAC;IAQD,KAAK,CAAC,OAAO,CACX,MAAsE;QAEtE,OAAO,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;IAC1C,CAAC;IAQD,KAAK,CAAC,MAAM,CACV,IAAU,EACV,OAAe,EACf,MAA0B;QAE1B,OAAO,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;IAClD,CAAC;IAOD,KAAK,CAAC,KAAK,CAAC,MAAe;QACzB,OAAO,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;IACxC,CAAC;IAOD,KAAK,CAAC,aAAa,CAAC,MAAe;QAIjC,OAAO,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC;IAChD,CAAC;IAQD,KAAK,CAAC,OAAO,CAAC,EAAU,EAAE,YAAsB,EAAE;QAChD,OAAO,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,EAAE,EAAE,SAAS,CAAC,CAAC;IACjD,CAAC;IAOD,KAAK,CAAC,WAAW,CACf,MAA0B;QAE1B,OAAO,IAAI,CAAC,WAAW,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC;IAC9C,CAAC;IAMD,KAAK,CAAC,MAAM;QACV,OAAO,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,CAAC;IACnC,CAAC;IAUD,KAAK,CAAC,MAAM,CACV,EAAU,EACV,kBAAsC,EACtC,MAAc;QAEd,OAAO,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,EAAE,EAAE,kBAAkB,EAAE,MAAM,CAAC,CAAC;IACnE,CAAC;IAQD,KAAK,CAAC,UAAU,CACd,mBAA4D,EAC5D,MAAc;QAEd,OAAO,IAAI,CAAC,aAAa,CAAC,UAAU,CAAC,mBAAmB,EAAE,MAAM,CAAC,CAAC;IACpE,CAAC;IAQD,KAAK,CAAC,YAAY,CAAC,EAAU,EAAE,MAAc;QAC3C,OAAO,IAAI,CAAC,aAAa,CAAC,YAAY,CAAC,EAAE,EAAE,MAAM,CAAC,CAAC;IACrD,CAAC;IAQD,KAAK,CAAC,wBAAwB,CAC5B,WAAmB,EACnB,MAAc;QAEd,OAAO,IAAI,CAAC,aAAa,CAAC,wBAAwB,CAAC,WAAW,EAAE,MAAM,CAAC,CAAC;IAC1E,CAAC;IASD,KAAK,CAAC,0BAA0B,CAC9B,WAAmB,EACnB,MAAc,EACd,cAAoB;QAEpB,OAAO,IAAI,CAAC,aAAa,CAAC,0BAA0B,CAClD,WAAW,EACX,MAAM,EACN,cAAc,CACf,CAAC;IACJ,CAAC;IAQD,KAAK,CAAC,4BAA4B,CAChC,WAAmB,EACnB,MAAc;QAEd,OAAO,IAAI,CAAC,aAAa,CAAC,4BAA4B,CAAC,WAAW,EAAE,MAAM,CAAC,CAAC;IAC9E,CAAC;IAOD,KAAK,CAAC,MAAM,CAAC,EAAU;QACrB,OAAO,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;IACvC,CAAC;IAOD,KAAK,CAAC,UAAU,CAAC,GAAa;QAC5B,OAAO,IAAI,CAAC,aAAa,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC;IAC5C,CAAC;IAQD,KAAK,CAAC,UAAU,CAAC,EAAU,EAAE,MAAc;QACzC,OAAO,IAAI,CAAC,aAAa,CAAC,UAAU,CAAC,EAAE,EAAE,MAAM,CAAC,CAAC;IACnD,CAAC;IAQD,KAAK,CAAC,cAAc,CAClB,GAAa,EACb,MAAc;QAEd,OAAO,IAAI,CAAC,aAAa,CAAC,cAAc,CAAC,GAAG,EAAE,MAAM,CAAC,CAAC;IACxD,CAAC;IAQD,KAAK,CAAC,OAAO,CAAC,EAAU,EAAE,MAAc;QACtC,OAAO,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,EAAE,EAAE,MAAM,CAAC,CAAC;IAChD,CAAC;IAOD,KAAK,CAAC,iBAAiB,CAAC,IAAY;QAClC,OAAO,IAAI,CAAC,aAAa,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC;IACpD,CAAC;IAQD,KAAK,CAAC,qBAAqB,CACzB,kBAAsC,EACtC,MAAc;QAEd,IAAI,CAAC;YAEH,MAAM,SAAS,GAAG,MAAM,IAAI,CAAC,aAAa,CAAC,MAAM,CAC/C,kBAAkB,EAClB,MAAM,CACP,CAAC;YAGF,OAAO,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,SAAS,CAAC,EAAE,EAAE;gBAC5C,SAAS;gBACT,eAAe;gBACf,MAAM;aACP,CAAC,CAAC;QACL,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,KAAK,YAAY,0BAAiB,EAAE,CAAC;gBACvC,MAAM,KAAK,CAAC;YACd,CAAC;YACD,IAAI,KAAK,YAAY,4BAAmB,EAAE,CAAC;gBACzC,MAAM,KAAK,CAAC;YACd,CAAC;YACD,MAAM,IAAI,qCAA4B,CACpC,+BAA+B,KAAK,CAAC,OAAO,EAAE,CAC/C,CAAC;QACJ,CAAC;IACH,CAAC;IAQD,KAAK,CAAC,0BAA0B,CAC9B,EAAU,EACV,MAAc;QAEd,OAAO,IAAI,CAAC,aAAa,CAAC,0BAA0B,CAAC,EAAE,EAAE,MAAM,CAAC,CAAC;IACnE,CAAC;IAED,KAAK,CAAC,gBAAgB,CAAC,EAAU,EAAE,MAAc;QAC/C,OAAO,IAAI,CAAC,aAAa,CAAC,gBAAgB,CAAC,EAAE,EAAE,MAAM,CAAC,CAAC;IACzD,CAAC;IAQD,KAAK,CAAC,YAAY,CAAC,EAAU,EAAE,MAAc;QAC3C,OAAO,IAAI,CAAC,aAAa,CAAC,YAAY,CAAC,EAAE,EAAE,MAAM,CAAC,CAAC;IACrD,CAAC;CACF,CAAA;AA/WY,4CAAgB;2BAAhB,gBAAgB;IAD5B,IAAA,mBAAU,GAAE;IAcR,WAAA,IAAA,0BAAgB,EAAC,6BAAS,CAAC,CAAA;qCACU,oBAAU;QAChB,iCAAsB;QACxB,+BAAoB;QAClB,iCAAsB;QACtB,iCAAsB;GAlB7C,gBAAgB,CA+W5B"}
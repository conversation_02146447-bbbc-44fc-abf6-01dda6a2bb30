/**
 * Service xử lý các thao tác public cho sản phẩm
 */
import { Injectable, InternalServerErrorException, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, DataSource, Brackets } from 'typeorm';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { plainToInstance } from 'class-transformer';

import { BaseEcomProductsService } from './base.ecom-products.service';
import { EcomProductPublicDto, HomepageSectionDto, HomepageSectionsResponseDto } from '../dto/ecom-product.public.dto';
import { CustomPaginationQueryDto } from '../../common/dto/custom-pagination-query.dto';
import { EcomProduct } from '../entity/ecom-products.entity';
import { ReadEcomProductCategoriesService } from '../../ecom-product-categories/services/read.ecom-product-categories.service';
import { EcomProductCategoryPublicDto } from '../../ecom-product-categories/dto/ecom-product-category.public.dto';

@Injectable()
export class PublicEcomProductsService extends BaseEcomProductsService {

  /**
   * Fisher-Yates shuffle algorithm để trộn ngẫu nhiên mảng
   * @param array Mảng cần trộn
   * @returns Mảng đã được trộn ngẫu nhiên
   */
  private shuffleArray<T>(array: T[]): T[] {
    // Kiểm tra edge cases
    if (!array || array.length <= 1) {
      return [...array];
    }

    const shuffled = [...array]; // Tạo bản sao để không thay đổi mảng gốc

    // Fisher-Yates shuffle
    for (let i = shuffled.length - 1; i > 0; i--) {
      const j = Math.floor(Math.random() * (i + 1));
      [shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]];
    }

    this.logger.debug(`Shuffled array from ${array.length} items`);
    return shuffled;
  }
  constructor(
    @InjectRepository(EcomProduct)
    protected readonly ecomProductRepository: Repository<EcomProduct>,
    protected readonly dataSource: DataSource,
    protected readonly eventEmitter: EventEmitter2,
    private readonly readEcomProductCategoriesService: ReadEcomProductCategoriesService,
  ) {
    super(ecomProductRepository, dataSource, eventEmitter);
  }
  /**
   * Chuyển đổi entity EcomProducts thành Public DTO
   * @param product Entity EcomProducts cần chuyển đổi
   * @returns EcomProductPublicDto
   */
  private toPublicDto(product: EcomProduct): EcomProductPublicDto {
    return plainToInstance(EcomProductPublicDto, product, {
      excludeExtraneousValues: true,
      enableImplicitConversion: true,
      exposeDefaultValues: true,
      exposeUnsetFields: false,
    });
  }

  /**
   * Tìm sản phẩm theo ID (chỉ active products)
   * @param id ID của sản phẩm cần tìm
   * @returns Thông tin sản phẩm public
   * @throws NotFoundException nếu không tìm thấy sản phẩm
   * @throws InternalServerErrorException nếu có lỗi khác xảy ra
   */
  async findById(id: string): Promise<EcomProductPublicDto> {
    try {
      this.logger.debug(`Đang tìm sản phẩm public theo ID: ${id}`);
      
      // Luôn load category relation
      const requiredRelations = ['category'];
      const validatedRelations = this.validateRelations(requiredRelations);

      // Tạo query builder
      const queryBuilder = this.ecomProductRepository
        .createQueryBuilder('product')
        .where('product.id = :id', { id })
        .andWhere('product.isActive = :isActive', { isActive: true })
        .andWhere('product.isDeleted = :isDeleted', { isDeleted: false });

      // Tải các mối quan hệ
      validatedRelations.forEach(relation => {
        queryBuilder.leftJoinAndSelect(`product.${relation}`, relation);
      });

      // Thực hiện truy vấn
      const product = await queryBuilder.getOne();

      if (!product) {
        throw new NotFoundException(`Không tìm thấy sản phẩm với ID: ${id}`);
      }

      return this.toPublicDto(product);
    } catch (error) {
      this.logger.error(`Lỗi khi tìm sản phẩm public theo ID: ${error.message}`, error.stack);
      if (error instanceof NotFoundException) {
        throw error;
      }
      throw new InternalServerErrorException(`Không thể tìm sản phẩm: ${error.message}`);
    }
  }

  /**
   * Tìm sản phẩm theo mã sản phẩm (chỉ active products)
   * @param code Mã sản phẩm cần tìm
   * @returns Thông tin sản phẩm public
   * @throws NotFoundException nếu không tìm thấy sản phẩm
   * @throws InternalServerErrorException nếu có lỗi khác xảy ra
   */
  async findByCode(code: string): Promise<EcomProductPublicDto> {
    try {
      this.logger.debug(`Đang tìm sản phẩm public theo mã: ${code}`);
      
      // Luôn load category relation
      const requiredRelations = ['category'];
      const validatedRelations = this.validateRelations(requiredRelations);

      // Tạo query builder
      const queryBuilder = this.ecomProductRepository
        .createQueryBuilder('product')
        .where('product.productCode = :code', { code })
        .andWhere('product.isActive = :isActive', { isActive: true })
        .andWhere('product.isDeleted = :isDeleted', { isDeleted: false });

      // Tải các mối quan hệ
      validatedRelations.forEach(relation => {
        queryBuilder.leftJoinAndSelect(`product.${relation}`, relation);
      });

      // Thực hiện truy vấn
      const product = await queryBuilder.getOne();

      if (!product) {
        throw new NotFoundException(`Không tìm thấy sản phẩm với mã: ${code}`);
      }

      return this.toPublicDto(product);
    } catch (error) {
      this.logger.error(`Lỗi khi tìm sản phẩm public theo mã: ${error.message}`, error.stack);
      if (error instanceof NotFoundException) {
        throw error;
      }
      throw new InternalServerErrorException(`Không thể tìm sản phẩm theo mã: ${error.message}`);
    }
  }

  /**
   * Tìm sản phẩm theo slug (chỉ active products)
   * @param slug Slug của sản phẩm cần tìm
   * @returns Thông tin sản phẩm public
   * @throws NotFoundException nếu không tìm thấy sản phẩm
   * @throws InternalServerErrorException nếu có lỗi khác xảy ra
   */
  async findBySlug(slug: string): Promise<EcomProductPublicDto> {
    try {
      this.logger.debug(`Đang tìm sản phẩm public theo slug: ${slug}`);

      // Luôn load category relation
      const requiredRelations = ['category'];
      const validatedRelations = this.validateRelations(requiredRelations);

      // Tạo query builder
      const queryBuilder = this.ecomProductRepository
        .createQueryBuilder('product')
        .where('product.slug = :slug', { slug })
        .andWhere('product.isActive = :isActive', { isActive: true })
        .andWhere('product.isDeleted = :isDeleted', { isDeleted: false });

      // Tải các mối quan hệ
      validatedRelations.forEach(relation => {
        queryBuilder.leftJoinAndSelect(`product.${relation}`, relation);
      });

      // Thực hiện truy vấn
      const product = await queryBuilder.getOne();

      if (!product) {
        throw new NotFoundException(`Không tìm thấy sản phẩm với slug: ${slug}`);
      }

      return this.toPublicDto(product);
    } catch (error) {
      this.logger.error(`Lỗi khi tìm sản phẩm public theo slug: ${error.message}`, error.stack);
      if (error instanceof NotFoundException) {
        throw error;
      }
      throw new InternalServerErrorException(`Không thể tìm sản phẩm theo slug: ${error.message}`);
    }
  }

  /**
   * Tìm tất cả sản phẩm active với phân trang và lọc
   * @param params Tham số tìm kiếm
   * @returns Danh sách sản phẩm public và tổng số bản ghi
   * @throws InternalServerErrorException nếu có lỗi xảy ra
   */
  async findAll(params: CustomPaginationQueryDto): Promise<{ data: EcomProductPublicDto[]; total: number }> {
    try {
      const { limit, page, search, filter } = params;
      const skip = (page - 1) * limit;

      // Luôn load category relation
      const requiredRelations = ['category'];
      const validatedRelations = this.validateRelations(requiredRelations);

      // Tạo query builder với điều kiện chỉ active products
      const queryBuilder = this.ecomProductRepository
        .createQueryBuilder('product')
        .where('product.isActive = :isActive', { isActive: true })
        .andWhere('product.isDeleted = :isDeleted', { isDeleted: false });

      // Áp dụng bộ lọc tìm kiếm nếu được cung cấp
      if (search) {
        queryBuilder.andWhere(new Brackets(qb => {
          qb.where('LOWER(product.productName) LIKE LOWER(:search)', { search: `%${search}%` })
            .orWhere('LOWER(product.productCode) LIKE LOWER(:search)', { search: `%${search}%` })
            .orWhere('LOWER(product.description) LIKE LOWER(:search)', { search: `%${search}%` });
        }));
      }

      // Áp dụng bộ lọc nếu được cung cấp
      if (filter) {
        // Parse filter string
        const filters = filter.split(',');

        // Group filters by field
        const filterGroups: Record<string, string[]> = {};

        filters.forEach(filterItem => {
          const [field, value] = filterItem.split(':');
          if (field && value) {
            if (!filterGroups[field]) {
              filterGroups[field] = [];
            }
            filterGroups[field].push(value);
          }
        });

        // Apply filters
        Object.entries(filterGroups).forEach(([field, values]) => {
          if (values.length === 1) {
            // Single value filter
            queryBuilder.andWhere(`product.${field} = :${field}`, { [field]: values[0] });
          } else {
            // Multiple values for the same field - use IN operator
            queryBuilder.andWhere(`product.${field} IN (:...${field})`, { [field]: values });
          }
        });
      }

      // Tải các mối quan hệ
      validatedRelations.forEach(relation => {
        queryBuilder.leftJoinAndSelect(`product.${relation}`, relation);
      });

      // Áp dụng sắp xếp
      const sortOptions = params.sortOptions || [];
      if (sortOptions.length > 0) {
        sortOptions.forEach((option, index) => {
          // Xử lý đặc biệt cho các trường có quan hệ (ví dụ: category.name)
          if (option.field.includes('.')) {
            const [relation, field] = option.field.split('.');
            if (index === 0) {
              queryBuilder.orderBy(`${relation}.${field}`, option.order);
            } else {
              queryBuilder.addOrderBy(`${relation}.${field}`, option.order);
            }
          } else {
            // Xử lý bình thường cho các trường của product
            if (index === 0) {
              queryBuilder.orderBy(`product.${option.field}`, option.order);
            } else {
              queryBuilder.addOrderBy(`product.${option.field}`, option.order);
            }
          }
        });
      } else {
        // Mặc định sắp xếp theo ngày tạo giảm dần (sản phẩm mới nhất trước)
        queryBuilder.orderBy('product.createdAt', 'DESC');
      }

      // Áp dụng phân trang
      queryBuilder.skip(skip).take(limit);

      // Thực hiện truy vấn
      const [products, total] = await queryBuilder.getManyAndCount();

      // Chuyển đổi kết quả thành Public DTOs
      const productDtos = products.map(product => this.toPublicDto(product));

      return { data: productDtos, total };
    } catch (error) {
      this.logger.error(`Lỗi khi tìm tất cả sản phẩm public: ${error.message}`, error.stack);
      throw new InternalServerErrorException(`Không thể tìm tất cả sản phẩm: ${error.message}`);
    }
  }

  /**
   * Tìm sản phẩm theo danh mục (chỉ active products)
   * @param categorySlug Slug của danh mục
   * @param params Tham số tìm kiếm
   * @returns Danh sách sản phẩm public và tổng số bản ghi
   * @throws InternalServerErrorException nếu có lỗi xảy ra
   */
  async findByCategory(
    categorySlug: string,
    params: CustomPaginationQueryDto
  ): Promise<{ data: EcomProductPublicDto[]; total: number }> {
    try {
      const { limit, page, search, filter, sortOptions } = params;
      const skip = (page - 1) * limit;

      const queryBuilder = this.ecomProductRepository
        .createQueryBuilder('product')
        .leftJoinAndSelect('product.category', 'category')
        .where('product.isActive = :isActive', { isActive: true })
        .andWhere('product.isDeleted = :isDeleted', { isDeleted: false })
        .andWhere('category.slug = :categorySlug', { categorySlug });

      // Áp dụng bộ lọc tìm kiếm nếu được cung cấp
      if (search) {
        queryBuilder.andWhere(new Brackets(qb => {
          qb.where('LOWER(product.productName) LIKE LOWER(:search)', { search: `%${search}%` })
            .orWhere('LOWER(product.productCode) LIKE LOWER(:search)', { search: `%${search}%` })
            .orWhere('LOWER(product.description) LIKE LOWER(:search)', { search: `%${search}%` });
        }));
      }

      // Áp dụng các filter khác nếu có (ngoài category)
      if (filter) {
        // Parse filter string
        const filters = filter.split(',');
        const filterGroups: Record<string, string[]> = {};
        filters.forEach(filterItem => {
          const [field, value] = filterItem.split(':');
          if (field && value && field !== 'categoryId') { // Bỏ filter theo categoryId
            if (!filterGroups[field]) {
              filterGroups[field] = [];
            }
            filterGroups[field].push(value);
          }
        });
        Object.entries(filterGroups).forEach(([field, values]) => {
          if (values.length === 1) {
            queryBuilder.andWhere(`product.${field} = :${field}`, { [field]: values[0] });
          } else {
            queryBuilder.andWhere(`product.${field} IN (:...${field})`, { [field]: values });
          }
        });
      }

      // Áp dụng sắp xếp
      if (sortOptions && sortOptions.length > 0) {
        sortOptions.forEach((option, index) => {
          if (option.field.includes('.')) {
            const [relation, field] = option.field.split('.');
            if (index === 0) {
              queryBuilder.orderBy(`${relation}.${field}`, option.order);
            } else {
              queryBuilder.addOrderBy(`${relation}.${field}`, option.order);
            }
          } else {
            if (index === 0) {
              queryBuilder.orderBy(`product.${option.field}`, option.order);
            } else {
              queryBuilder.addOrderBy(`product.${option.field}`, option.order);
            }
          }
        });
      } else {
        queryBuilder.orderBy('product.createdAt', 'DESC');
      }

      // Áp dụng phân trang
      queryBuilder.skip(skip).take(limit);

      // Thực hiện truy vấn
      const [products, total] = await queryBuilder.getManyAndCount();
      const productDtos = products.map(product => this.toPublicDto(product));
      return { data: productDtos, total };
    } catch (error) {
      this.logger.error(`Lỗi khi tìm sản phẩm public theo danh mục (slug): ${error.message}`, error.stack);
      throw new InternalServerErrorException(`Không thể tìm sản phẩm theo danh mục: ${error.message}`);
    }
  }

  /**
   * Lấy dữ liệu cho các section trang chủ (danh mục + sản phẩm)
   * Sản phẩm trong mỗi danh mục sẽ được randomize để tạo sự đa dạng
   * @param productsPerCategory Số lượng sản phẩm mỗi danh mục (mặc định 4)
   * @returns Dữ liệu các section trang chủ (tất cả categories có products với thứ tự ngẫu nhiên)
   * @throws InternalServerErrorException nếu có lỗi xảy ra
   */
  async findHomepageSections(
    productsPerCategory: number = 4
  ): Promise<HomepageSectionsResponseDto> {
    try {
      this.logger.debug(`Đang tìm tất cả danh mục có sản phẩm với ${productsPerCategory} sản phẩm mỗi danh mục cho trang chủ`);

      this.logger.debug('Bắt đầu lấy danh sách danh mục active');

      // Log để debug
      this.logger.debug(`productsPerCategory: ${productsPerCategory}`);

      // Lấy tất cả danh sách danh mục active bằng service (không giới hạn số lượng)
      const categoriesQuery = Object.assign(new CustomPaginationQueryDto(), {
        page: 1,
        limit: 1000, // Lấy nhiều để đảm bảo lấy hết tất cả categories
        search: '',
        sort: 'createdAt:ASC',
        filter: ''
      });

      const categoriesResult = await this.readEcomProductCategoriesService.getActiveProductCategories(categoriesQuery);

      const categories = categoriesResult.data || [];
      this.logger.debug(`Tìm thấy ${categories?.length || 0} danh mục`);

      // Log chi tiết về categories
      categories.forEach((cat, index) => {
        this.logger.debug(`Category ${index}: ID=${cat.id}, Name=${cat.name}, Type=${typeof cat.id}`);
      });

      if (!categories || categories.length === 0) {
        this.logger.warn('Không tìm thấy danh mục nào cho trang chủ');
        return plainToInstance(HomepageSectionsResponseDto, {
          sections: [],
          totalCategories: 0,
          returnedCategories: 0
        });
      }

      // Lấy sản phẩm cho từng danh mục
      const sections: HomepageSectionDto[] = [];

      for (const category of categories) {
        this.logger.debug(`Processing category: ${category.id} - ${category.name} (Type: ${typeof category.id})`);

        // Lấy sản phẩm của danh mục này bằng query trực tiếp
        this.logger.debug(`Getting products for category ${category.id} (Type: ${typeof category.id})`);

        let products: any[] = [];
        try {
          // Query nhiều products hơn để có đủ dữ liệu cho randomize
          // Lấy gấp 3 lần số lượng cần thiết hoặc tối thiểu 20 products để đảm bảo tính ngẫu nhiên tốt
          const queryLimit = Math.max(productsPerCategory * 3, 20);

          const queryBuilder = this.ecomProductRepository
            .createQueryBuilder('product')
            .leftJoinAndSelect('product.category', 'category')
            .where('product.isActive = :isActive', { isActive: true })
            .andWhere('product.isDeleted = :isDeleted', { isDeleted: false })
            .andWhere('product.categoryId = :categoryId', { categoryId: category.id })
            .orderBy('product.createdAt', 'DESC')
            .limit(queryLimit);

          const productEntities = await queryBuilder.getMany();

          this.logger.debug(`Queried ${productEntities?.length || 0} products for randomization for category ${category.id}`);

          if (productEntities && productEntities.length > 0) {
            // Chuyển đổi entities thành public DTOs
            const allProducts = productEntities.map(product => this.toPublicDto(product));

            // Randomize thứ tự products
            const shuffledProducts = this.shuffleArray(allProducts);

            // Lấy số lượng products theo yêu cầu
            products = shuffledProducts.slice(0, productsPerCategory);

            this.logger.debug(`After randomization and limiting: ${products?.length || 0} products for category ${category.id}`);
          }
        } catch (findAllError) {
          this.logger.error(`Error getting products for category ${category.id}: ${findAllError.message}`);
          this.logger.error(`Error stack: ${findAllError.stack}`);
          throw findAllError;
        }

        this.logger.debug(`Products found for category ${category.id}: ${products?.length || 0}`);

        // Chỉ tạo section nếu category có ít nhất 1 product
        if (products && products.length > 0) {
          this.logger.debug(`Adding section for category ${category.id} with ${products.length} products`);

          // Chuyển đổi category thành public DTO
          const categoryPublicDto = plainToInstance(
            EcomProductCategoryPublicDto,
            category,
            {
              excludeExtraneousValues: true,
              enableImplicitConversion: true,
              exposeDefaultValues: true,
              exposeUnsetFields: false,
            }
          );

          sections.push(plainToInstance(HomepageSectionDto, {
            category: categoryPublicDto,
            products: products
          }));

          this.logger.debug(`Section added. Total sections now: ${sections.length}`);
        } else {
          this.logger.debug(`No products found for category ${category.id}, skipping section`);
        }
      }

      // Đếm tổng số danh mục có sẵn từ kết quả đã lấy
      const totalCategoriesCount = categoriesResult.total || 0;

      const result = plainToInstance(HomepageSectionsResponseDto, {
        sections,
        totalCategories: totalCategoriesCount || 0,
        returnedCategories: sections.length
      });

      this.logger.debug(`Trả về ${sections.length} section cho trang chủ`);
      return result;

    } catch (error) {
      this.logger.error(`Lỗi khi tìm dữ liệu section trang chủ: ${error.message}`, error.stack);
      throw new InternalServerErrorException(`Không thể tìm dữ liệu section trang chủ: ${error.message}`);
    }
  }
}

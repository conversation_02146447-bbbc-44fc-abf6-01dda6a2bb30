"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var SystemConfigCreateController_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.SystemConfigCreateController = void 0;
const openapi = require("@nestjs/swagger");
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const jwt_auth_guard_1 = require("../../common/guards/jwt-auth.guard");
const create_system_config_service_1 = require("../services/create.system-config.service");
const create_system_config_dto_1 = require("../dto/create-system-config.dto");
const api_response_dto_1 = require("../../common/dto/api-response.dto");
const get_user_decorator_1 = require("../../common/decorators/get-user.decorator");
const roles_decorator_1 = require("../../common/decorators/roles.decorator");
const permissions_decorator_1 = require("../../common/decorators/permissions.decorator");
let SystemConfigCreateController = SystemConfigCreateController_1 = class SystemConfigCreateController {
    systemConfigService;
    logger = new common_1.Logger(SystemConfigCreateController_1.name);
    constructor(systemConfigService) {
        this.systemConfigService = systemConfigService;
    }
    async create(createSystemConfigDto, userId) {
        this.logger.debug(`Đang tạo cấu hình hệ thống mới với dữ liệu: ${JSON.stringify(createSystemConfigDto)}`);
        createSystemConfigDto.createdBy = userId;
        return this.systemConfigService.create(createSystemConfigDto);
    }
    async createOrUpdateBulk(bulkDto, userId) {
        this.logger.debug(`Đang tạo hoặc cập nhật ${bulkDto.length} cấu hình hệ thống`);
        bulkDto.forEach(item => {
            item.updatedBy = userId;
        });
        return this.systemConfigService.createOrUpdateBulk(bulkDto);
    }
};
exports.SystemConfigCreateController = SystemConfigCreateController;
__decorate([
    (0, common_1.Post)(),
    (0, roles_decorator_1.Roles)('ADMIN'),
    (0, permissions_decorator_1.Permissions)('config:create'),
    (0, swagger_1.ApiOperation)({ summary: 'Tạo cấu hình hệ thống mới' }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.CREATED,
        description: 'Cấu hình hệ thống được tạo thành công.',
        type: api_response_dto_1.ApiResponseDto,
        schema: {
            properties: { data: { $ref: '#/components/schemas/SystemConfigDto' } },
        },
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.CONFLICT,
        description: 'Cấu hình hệ thống đã tồn tại.',
        type: api_response_dto_1.ApiResponseDto,
        schema: { properties: { data: { type: 'object', nullable: true } } },
    }),
    openapi.ApiResponse({ status: 201, type: require("../dto/system-config.dto").SystemConfigDto }),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, get_user_decorator_1.GetUser)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [create_system_config_dto_1.CreateSystemConfigDto, String]),
    __metadata("design:returntype", Promise)
], SystemConfigCreateController.prototype, "create", null);
__decorate([
    (0, common_1.Post)('bulk'),
    (0, roles_decorator_1.Roles)('ADMIN'),
    (0, permissions_decorator_1.Permissions)('config:create'),
    (0, swagger_1.ApiOperation)({ summary: 'Tạo hoặc cập nhật nhiều cấu hình hệ thống cùng lúc' }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.CREATED,
        description: 'Các cấu hình hệ thống được tạo hoặc cập nhật thành công.',
        type: api_response_dto_1.ApiResponseDto,
        schema: {
            properties: {
                data: {
                    type: 'array',
                    items: { $ref: '#/components/schemas/SystemConfigDto' },
                },
            },
        },
    }),
    openapi.ApiResponse({ status: 201, type: [require("../dto/system-config.dto").SystemConfigDto] }),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, get_user_decorator_1.GetUser)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Array, String]),
    __metadata("design:returntype", Promise)
], SystemConfigCreateController.prototype, "createOrUpdateBulk", null);
exports.SystemConfigCreateController = SystemConfigCreateController = SystemConfigCreateController_1 = __decorate([
    (0, swagger_1.ApiTags)('system-configs'),
    (0, swagger_1.ApiBearerAuth)(),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, common_1.Controller)('system-configs'),
    __metadata("design:paramtypes", [create_system_config_service_1.CreateSystemConfigService])
], SystemConfigCreateController);
//# sourceMappingURL=system-config.create.controller.js.map
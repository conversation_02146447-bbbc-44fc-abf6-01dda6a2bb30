"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.FixOrderStatus1745656078180 = void 0;
class FixOrderStatus1745656078180 {
    async up(queryRunner) {
        try {
            const enumExists = await queryRunner.query(`
                SELECT 1 FROM pg_type 
                WHERE typname = 'order_book_status_enum'
            `);
            if (enumExists && enumExists.length > 0) {
                await queryRunner.query(`DROP TYPE IF EXISTS "public"."order_book_status_enum_new"`);
                await queryRunner.query(`
                    CREATE TYPE "public"."order_book_status_enum_new" AS ENUM(
                        'COMPLETED', 
                        'WAIT_PAYMENT', 
                        'TERMINATED',
                        'WAIT_WITHDRAWAL',
                        'WITHDRAWN',
                        'CANCELLED'
                    )
                `);
                await queryRunner.query(`ALTER TABLE "order_book" ADD "status_temp" varchar`);
                await queryRunner.query(`UPDATE "order_book" SET "status_temp" = "status"::text`);
                await queryRunner.query(`ALTER TABLE "order_book" DROP CONSTRAINT IF EXISTS "order_book_status_check"`);
                await queryRunner.query(`ALTER TABLE "order_book" DROP COLUMN "status"`);
                await queryRunner.query(`ALTER TABLE "order_book" ADD "status" "public"."order_book_status_enum_new" NOT NULL DEFAULT 'WAIT_PAYMENT'`);
                await queryRunner.query(`
                    UPDATE "order_book" 
                    SET "status" = 
                        CASE 
                            WHEN "status_temp" = 'COMPLETED' THEN 'COMPLETED'::public.order_book_status_enum_new
                            WHEN "status_temp" = 'TERMINATED' THEN 'TERMINATED'::public.order_book_status_enum_new
                            WHEN "status_temp" = 'WAIT_PAYMENT' THEN 'WAIT_PAYMENT'::public.order_book_status_enum_new
                            ELSE 'WAIT_PAYMENT'::public.order_book_status_enum_new
                        END
                `);
                await queryRunner.query(`ALTER TABLE "order_book" DROP COLUMN "status_temp"`);
                await queryRunner.query(`DROP TYPE IF EXISTS "public"."order_book_status_enum"`);
                await queryRunner.query(`ALTER TYPE "public"."order_book_status_enum_new" RENAME TO "order_book_status_enum"`);
            }
            else {
                await queryRunner.query(`
                    CREATE TYPE "public"."order_book_status_enum" AS ENUM(
                        'COMPLETED', 
                        'WAIT_PAYMENT', 
                        'TERMINATED',
                        'WAIT_WITHDRAWAL',
                        'WITHDRAWN',
                        'CANCELLED'
                    )
                `);
                const columnExists = await queryRunner.query(`
                    SELECT 1 FROM information_schema.columns 
                    WHERE table_name = 'order_book' AND column_name = 'status'
                `);
                if (columnExists && columnExists.length > 0) {
                    await queryRunner.query(`ALTER TABLE "order_book" ALTER COLUMN "status" TYPE "public"."order_book_status_enum" USING "status"::text::"public"."order_book_status_enum"`);
                }
                else {
                    await queryRunner.query(`ALTER TABLE "order_book" ADD "status" "public"."order_book_status_enum" NOT NULL DEFAULT 'WAIT_PAYMENT'`);
                }
            }
        }
        catch (error) {
            console.error('Lỗi khi thực hiện migration FixOrderStatusEnum1745700000001:', error);
            throw error;
        }
    }
    async down(queryRunner) {
    }
}
exports.FixOrderStatus1745656078180 = FixOrderStatus1745656078180;
//# sourceMappingURL=1745656078180-FixOrderStatus.js.map
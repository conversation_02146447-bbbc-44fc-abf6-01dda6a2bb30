import { UserDto } from '../../users/dto/user.dto';
import { EcomProductCategoryDto } from '../../ecom-product-categories/dto/ecom-product-category.dto';
export declare class EcomProductDto {
    id: string;
    productCode: string;
    productName: string;
    categoryId: string;
    description?: string;
    weight?: number;
    imageUrl?: string;
    regularPrice: number;
    salePrice?: number;
    stockQuantity: number;
    isActive: boolean;
    slug?: string;
    isDeleted: boolean;
    createdAt: Date;
    updatedAt: Date;
    createdBy?: string | null;
    updatedBy?: string | null;
    deletedAt?: Date | null;
    deletedBy?: string | null;
    category: EcomProductCategoryDto;
    creator?: UserDto | null;
    updater?: UserDto | null;
    deleter?: UserDto | null;
}

import { Logger } from '@nestjs/common';
import { Repository, DataSource } from 'typeorm';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { SystemConfig } from '../entities/system-config.entity';
import { SystemConfigDto } from '../dto/system-config.dto';
import { BaseSystemConfigService } from './base.system-config.service';
import { CustomPaginationQueryDto } from '../../common/dto/custom-pagination-query.dto';
export declare class ReadSystemConfigService extends BaseSystemConfigService {
    protected readonly systemConfigRepository: Repository<SystemConfig>;
    protected readonly dataSource: DataSource;
    protected readonly eventEmitter: EventEmitter2;
    protected logger: Logger;
    constructor(systemConfigRepository: Repository<SystemConfig>, dataSource: DataSource, eventEmitter: EventEmitter2);
    findAll(paginationQuery: CustomPaginationQueryDto): Promise<{
        data: SystemConfigDto[];
        total: number;
    }>;
    search(keyword: string, paginationQuery: CustomPaginationQueryDto): Promise<{
        data: SystemConfigDto[];
        total: number;
    }>;
    findById(id: string, relations?: string[]): Promise<SystemConfigDto>;
    findByKey(key: string, relations?: string[]): Promise<SystemConfigDto>;
    findByGroup(group: string, paginationQuery: CustomPaginationQueryDto): Promise<{
        data: SystemConfigDto[];
        total: number;
    }>;
    findAllGroups(): Promise<SystemConfigDto[]>;
    private createDefaultGroupConfigs;
    findDeleted(paginationQuery: CustomPaginationQueryDto): Promise<{
        data: SystemConfigDto[];
        total: number;
    }>;
    private getDefaultGroupDisplayName;
    private getDefaultGroupDescription;
    private getDefaultGroupIcon;
}

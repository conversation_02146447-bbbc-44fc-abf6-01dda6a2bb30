"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.DeleteCmsPagesController = exports.UpdateCmsPagesController = exports.ReadCmsPagesPublicController = exports.ReadCmsPagesController = exports.CreateCmsPagesController = void 0;
var create_cms_pages_controller_1 = require("./create.cms-pages.controller");
Object.defineProperty(exports, "CreateCmsPagesController", { enumerable: true, get: function () { return create_cms_pages_controller_1.CreateCmsPagesController; } });
var read_cms_pages_controller_1 = require("./read.cms-pages.controller");
Object.defineProperty(exports, "ReadCmsPagesController", { enumerable: true, get: function () { return read_cms_pages_controller_1.ReadCmsPagesController; } });
Object.defineProperty(exports, "ReadCmsPagesPublicController", { enumerable: true, get: function () { return read_cms_pages_controller_1.ReadCmsPagesPublicController; } });
var update_cms_pages_controller_1 = require("./update.cms-pages.controller");
Object.defineProperty(exports, "UpdateCmsPagesController", { enumerable: true, get: function () { return update_cms_pages_controller_1.UpdateCmsPagesController; } });
var delete_cms_pages_controller_1 = require("./delete.cms-pages.controller");
Object.defineProperty(exports, "DeleteCmsPagesController", { enumerable: true, get: function () { return delete_cms_pages_controller_1.DeleteCmsPagesController; } });
//# sourceMappingURL=index.js.map
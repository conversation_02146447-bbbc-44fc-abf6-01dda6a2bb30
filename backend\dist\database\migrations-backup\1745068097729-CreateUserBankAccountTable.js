"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreateUserBankAccountTable************* = void 0;
class CreateUserBankAccountTable************* {
    name = 'CreateUserBankAccountTable*************';
    async up(queryRunner) {
        await queryRunner.query(`CREATE TABLE "user_bank_accounts" (
            "id" uuid NOT NULL DEFAULT uuid_generate_v4(), 
            "userId" uuid NOT NULL, 
            "bankId" uuid NOT NULL, 
            "type" character varying NOT NULL DEFAULT 'ACCOUNT', 
            "accountHolderName" character varying NOT NULL, 
            "accountNumber" character varying NOT NULL, 
            "branch" character varying, 
            "createdAt" TIMESTAMP NOT NULL DEFAULT now(), 
            "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), 
            "createdBy" character varying, 
            "updatedBy" character varying, 
            "isActive" boolean NOT NULL DEFAULT true, 
            "isDeleted" boolean NOT NULL DEFAULT false, 
            "deletedBy" character varying, 
            "deletedAt" TIMESTAMP, 
            CONSTRAINT "PK_94aaf3f58cfa2b25efc21ed115a" PRIMARY KEY ("id")
        )`);
        await queryRunner.query(`ALTER TABLE "user_bank_accounts" ADD CONSTRAINT "FK_ac0051501fca67bc5eb904d458e" FOREIGN KEY ("userId") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "user_bank_accounts" ADD CONSTRAINT "FK_ececf558df7e2391bc611e75e88" FOREIGN KEY ("bankId") REFERENCES "banks"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
    }
    async down(queryRunner) {
        await queryRunner.query(`ALTER TABLE "user_bank_accounts" DROP CONSTRAINT "FK_ececf558df7e2391bc611e75e88"`);
        await queryRunner.query(`ALTER TABLE "user_bank_accounts" DROP CONSTRAINT "FK_ac0051501fca67bc5eb904d458e"`);
        await queryRunner.query(`DROP TABLE "user_bank_accounts"`);
    }
}
exports.CreateUserBankAccountTable************* = CreateUserBankAccountTable*************;
//# sourceMappingURL=*************-CreateUserBankAccountTable.js.map
"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var VnpayPaymentController_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.VnpayPaymentController = void 0;
const openapi = require("@nestjs/swagger");
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const vnpay_payment_service_1 = require("../services/vnpay-payment.service");
let VnpayPaymentController = VnpayPaymentController_1 = class VnpayPaymentController {
    vnpayPaymentService;
    logger = new common_1.Logger(VnpayPaymentController_1.name);
    constructor(vnpayPaymentService) {
        this.vnpayPaymentService = vnpayPaymentService;
    }
    async createPayment(request) {
        try {
            return await this.vnpayPaymentService.createPayment(request);
        }
        catch (error) {
            this.logger.error(`Error creating payment: ${error.message}`, error.stack);
            throw error;
        }
    }
    async handleReturn(query, res) {
        try {
            const callback = await this.vnpayPaymentService.handleReturnCallback(query);
            const baseUrl = process.env.FRONTEND_URL || 'http://localhost:3000';
            if (callback.isSuccess) {
                const successUrl = `${baseUrl}/payment/success?` +
                    `merchantTxnRef=${callback.merchantTxnRef}&` +
                    `amount=${callback.amount}&` +
                    `vnpayTxnRef=${callback.vnpayTxnRef}&` +
                    `externalRef=${callback.externalRef}`;
                res.redirect(successUrl);
            }
            else {
                const failureUrl = `${baseUrl}/payment/failure?` +
                    `merchantTxnRef=${callback.merchantTxnRef}&` +
                    `message=${encodeURIComponent(callback.message || 'Payment failed')}&` +
                    `externalRef=${callback.externalRef}`;
                res.redirect(failureUrl);
            }
        }
        catch (error) {
            this.logger.error(`Error handling return: ${error.message}`, error.stack);
            const errorUrl = `${process.env.FRONTEND_URL}/payment/error?message=${encodeURIComponent('System error')}`;
            res.redirect(errorUrl);
        }
    }
    async handleIpn(query) {
        try {
            const callback = await this.vnpayPaymentService.handleIpnCallback(query);
            if (callback.isValid && callback.isSuccess) {
                return { RspCode: '00', Message: 'Confirm Success' };
            }
            else if (callback.isValid) {
                return { RspCode: '01', Message: 'Order not found' };
            }
            else {
                return { RspCode: '97', Message: 'Invalid signature' };
            }
        }
        catch (error) {
            this.logger.error(`Error handling IPN: ${error.message}`, error.stack);
            return { RspCode: '99', Message: 'Unknown error' };
        }
    }
};
exports.VnpayPaymentController = VnpayPaymentController;
__decorate([
    (0, common_1.Post)('create'),
    (0, swagger_1.ApiOperation)({ summary: 'Create VNPAY payment' }),
    (0, swagger_1.ApiResponse)({ status: 201, description: 'Payment created successfully' }),
    openapi.ApiResponse({ status: 201, type: Object }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], VnpayPaymentController.prototype, "createPayment", null);
__decorate([
    (0, common_1.Get)('return'),
    (0, swagger_1.ApiOperation)({ summary: 'Handle VNPAY return callback' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Callback processed' }),
    openapi.ApiResponse({ status: 200 }),
    __param(0, (0, common_1.Query)()),
    __param(1, (0, common_1.Res)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", Promise)
], VnpayPaymentController.prototype, "handleReturn", null);
__decorate([
    (0, common_1.Post)('ipn'),
    (0, common_1.HttpCode)(common_1.HttpStatus.OK),
    (0, swagger_1.ApiOperation)({ summary: 'Handle VNPAY IPN callback' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'IPN processed' }),
    openapi.ApiResponse({ status: common_1.HttpStatus.OK }),
    __param(0, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], VnpayPaymentController.prototype, "handleIpn", null);
exports.VnpayPaymentController = VnpayPaymentController = VnpayPaymentController_1 = __decorate([
    (0, swagger_1.ApiTags)('VNPAY Payment Gateway'),
    (0, common_1.Controller)('vnpay-payment'),
    __metadata("design:paramtypes", [vnpay_payment_service_1.VnpayPaymentService])
], VnpayPaymentController);
//# sourceMappingURL=vnpay-payment.controller.js.map
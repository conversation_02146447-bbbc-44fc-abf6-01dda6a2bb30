"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var VnpayPaymentController_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.VnpayPaymentController = void 0;
const openapi = require("@nestjs/swagger");
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const vnpay_payment_service_1 = require("../services/vnpay-payment.service");
const vnpay_transaction_repository_1 = require("../repositories/vnpay-transaction.repository");
let VnpayPaymentController = VnpayPaymentController_1 = class VnpayPaymentController {
    vnpayPaymentService;
    vnpayTransactionRepository;
    logger = new common_1.Logger(VnpayPaymentController_1.name);
    constructor(vnpayPaymentService, vnpayTransactionRepository) {
        this.vnpayPaymentService = vnpayPaymentService;
        this.vnpayTransactionRepository = vnpayTransactionRepository;
    }
    async createPayment(request) {
        try {
            return await this.vnpayPaymentService.createPayment(request);
        }
        catch (error) {
            this.logger.error(`Error creating payment: ${error.message}`, error.stack);
            throw error;
        }
    }
    async handleReturn(query, res) {
        try {
            const callback = await this.vnpayPaymentService.handleReturnCallback(query);
            const baseUrl = process.env.FRONTEND_URL || 'http://localhost:3000';
            if (callback.isSuccess) {
                const successUrl = `${baseUrl}/payment/success?` +
                    `merchantTxnRef=${callback.merchantTxnRef}&` +
                    `amount=${callback.amount}&` +
                    `vnpayTxnRef=${callback.vnpayTxnRef}&` +
                    `externalRef=${callback.externalRef}`;
                res.redirect(successUrl);
            }
            else {
                const failureUrl = `${baseUrl}/payment/failure?` +
                    `merchantTxnRef=${callback.merchantTxnRef}&` +
                    `message=${encodeURIComponent(callback.message || 'Payment failed')}&` +
                    `externalRef=${callback.externalRef}`;
                res.redirect(failureUrl);
            }
        }
        catch (error) {
            this.logger.error(`Error handling return: ${error.message}`, error.stack);
            const errorUrl = `${process.env.FRONTEND_URL}/payment/error?message=${encodeURIComponent('System error')}`;
            res.redirect(errorUrl);
        }
    }
    async handleIpn(query) {
        try {
            const callback = await this.vnpayPaymentService.handleIpnCallback(query);
            if (callback.isValid && callback.isSuccess) {
                return { RspCode: '00', Message: 'Confirm Success' };
            }
            else if (callback.isValid) {
                return { RspCode: '01', Message: 'Order not found' };
            }
            else {
                return { RspCode: '97', Message: 'Invalid signature' };
            }
        }
        catch (error) {
            this.logger.error(`Error handling IPN: ${error.message}`, error.stack);
            return { RspCode: '99', Message: 'Unknown error' };
        }
    }
    async queryTransaction(request) {
        try {
            return await this.vnpayPaymentService.queryTransaction(request);
        }
        catch (error) {
            this.logger.error(`Error querying transaction: ${error.message}`, error.stack);
            throw error;
        }
    }
    async refundTransaction(request) {
        try {
            return await this.vnpayPaymentService.refundTransaction(request);
        }
        catch (error) {
            this.logger.error(`Error refunding transaction: ${error.message}`, error.stack);
            throw error;
        }
    }
    async getTransaction(merchantTxnRef) {
        try {
            const transaction = await this.vnpayPaymentService.getTransaction(merchantTxnRef);
            if (!transaction) {
                throw new common_1.BadRequestException(`Transaction not found: ${merchantTxnRef}`);
            }
            return {
                id: transaction.id,
                merchantTxnRef: transaction.merchantTxnRef,
                vnpayTxnRef: transaction.vnpayTxnRef,
                vnpayTxnNo: transaction.vnpayTxnNo,
                status: transaction.status,
                type: transaction.type,
                amount: transaction.amount,
                currency: transaction.currency,
                orderInfo: transaction.orderInfo,
                bankCode: transaction.bankCode,
                cardType: transaction.cardType,
                vnpayResponseCode: transaction.vnpayResponseCode,
                vnpayTransactionStatus: transaction.vnpayTransactionStatus,
                vnpayPayDate: transaction.vnpayPayDate,
                externalRef: transaction.externalRef,
                externalMetadata: transaction.externalMetadata
                    ? JSON.parse(transaction.externalMetadata)
                    : null,
                errorMessage: transaction.errorMessage,
                createdAt: transaction.createdAt,
                updatedAt: transaction.updatedAt,
                processedAt: transaction.processedAt,
                expiresAt: transaction.expiresAt,
            };
        }
        catch (error) {
            this.logger.error(`Error getting transaction: ${error.message}`, error.stack);
            throw error;
        }
    }
    async getStatistics(externalRef, fromDate, toDate) {
        try {
            const filter = {};
            if (externalRef)
                filter.externalRef = externalRef;
            if (fromDate)
                filter.fromDate = new Date(fromDate);
            if (toDate)
                filter.toDate = new Date(toDate);
            return await this.vnpayPaymentService.getStatistics(filter);
        }
        catch (error) {
            this.logger.error(`Error getting statistics: ${error.message}`, error.stack);
            throw error;
        }
    }
    async getTransactions(externalRef, status, fromDate, toDate, page = 1, limit = 20) {
        try {
            const filter = {};
            if (externalRef)
                filter.externalRef = externalRef;
            if (status)
                filter.status = status;
            if (fromDate)
                filter.fromDate = new Date(fromDate);
            if (toDate)
                filter.toDate = new Date(toDate);
            const result = await this.vnpayTransactionRepository.findWithFilters(filter, page, limit);
            return {
                transactions: result.transactions.map((t) => ({
                    id: t.id,
                    merchantTxnRef: t.merchantTxnRef,
                    vnpayTxnRef: t.vnpayTxnRef,
                    status: t.status,
                    type: t.type,
                    amount: t.amount,
                    orderInfo: t.orderInfo,
                    bankCode: t.bankCode,
                    externalRef: t.externalRef,
                    createdAt: t.createdAt,
                    processedAt: t.processedAt,
                })),
                total: result.total,
                page,
                limit,
                totalPages: Math.ceil(result.total / limit),
            };
        }
        catch (error) {
            this.logger.error(`Error getting transactions: ${error.message}`, error.stack);
            throw error;
        }
    }
    async healthCheck() {
        return {
            status: 'healthy',
            timestamp: new Date().toISOString(),
            service: 'VNPAY Payment Gateway',
            version: '1.0.0',
            features: [
                'Payment Creation',
                'Return Callback',
                'IPN Callback',
                'Transaction Query',
                'Transaction Refund',
                'Statistics',
                'Transaction History',
            ],
        };
    }
    async markExpiredTransactions() {
        try {
            const count = await this.vnpayTransactionRepository.markExpiredTransactions();
            return {
                success: true,
                message: `Marked ${count} expired transactions`,
                count,
                timestamp: new Date().toISOString(),
            };
        }
        catch (error) {
            this.logger.error(`Error marking expired transactions: ${error.message}`, error.stack);
            throw error;
        }
    }
};
exports.VnpayPaymentController = VnpayPaymentController;
__decorate([
    (0, common_1.Post)('create'),
    (0, swagger_1.ApiOperation)({ summary: 'Create VNPAY payment' }),
    (0, swagger_1.ApiResponse)({ status: 201, description: 'Payment created successfully' }),
    openapi.ApiResponse({ status: 201, type: Object }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], VnpayPaymentController.prototype, "createPayment", null);
__decorate([
    (0, common_1.Get)('return'),
    (0, swagger_1.ApiOperation)({ summary: 'Handle VNPAY return callback' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Callback processed' }),
    openapi.ApiResponse({ status: 200 }),
    __param(0, (0, common_1.Query)()),
    __param(1, (0, common_1.Res)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", Promise)
], VnpayPaymentController.prototype, "handleReturn", null);
__decorate([
    (0, common_1.Post)('ipn'),
    (0, common_1.HttpCode)(common_1.HttpStatus.OK),
    (0, swagger_1.ApiOperation)({ summary: 'Handle VNPAY IPN callback' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'IPN processed' }),
    openapi.ApiResponse({ status: common_1.HttpStatus.OK }),
    __param(0, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], VnpayPaymentController.prototype, "handleIpn", null);
__decorate([
    (0, common_1.Post)('query'),
    (0, swagger_1.ApiOperation)({ summary: 'Query VNPAY transaction status' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Query successful' }),
    openapi.ApiResponse({ status: 201, type: Object }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], VnpayPaymentController.prototype, "queryTransaction", null);
__decorate([
    (0, common_1.Post)('refund'),
    (0, swagger_1.ApiOperation)({ summary: 'Refund VNPAY transaction' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Refund processed' }),
    openapi.ApiResponse({ status: 201, type: Object }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], VnpayPaymentController.prototype, "refundTransaction", null);
__decorate([
    (0, common_1.Get)('transaction/:merchantTxnRef'),
    (0, swagger_1.ApiOperation)({ summary: 'Get transaction details' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Transaction found' }),
    openapi.ApiResponse({ status: 200, type: Object }),
    __param(0, (0, common_1.Param)('merchantTxnRef')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], VnpayPaymentController.prototype, "getTransaction", null);
__decorate([
    (0, common_1.Get)('statistics'),
    (0, swagger_1.ApiOperation)({ summary: 'Get payment statistics' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Statistics retrieved' }),
    openapi.ApiResponse({ status: 200, type: Object }),
    __param(0, (0, common_1.Query)('externalRef')),
    __param(1, (0, common_1.Query)('fromDate')),
    __param(2, (0, common_1.Query)('toDate')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String, String]),
    __metadata("design:returntype", Promise)
], VnpayPaymentController.prototype, "getStatistics", null);
__decorate([
    (0, common_1.Get)('transactions'),
    (0, swagger_1.ApiOperation)({ summary: 'Get transactions with filters' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Transactions retrieved' }),
    openapi.ApiResponse({ status: 200, type: Object }),
    __param(0, (0, common_1.Query)('externalRef')),
    __param(1, (0, common_1.Query)('status')),
    __param(2, (0, common_1.Query)('fromDate')),
    __param(3, (0, common_1.Query)('toDate')),
    __param(4, (0, common_1.Query)('page')),
    __param(5, (0, common_1.Query)('limit')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String, String, String, Number, Number]),
    __metadata("design:returntype", Promise)
], VnpayPaymentController.prototype, "getTransactions", null);
__decorate([
    (0, common_1.Get)('health'),
    (0, swagger_1.ApiOperation)({ summary: 'Health check' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Service healthy' }),
    openapi.ApiResponse({ status: 200, type: Object }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], VnpayPaymentController.prototype, "healthCheck", null);
__decorate([
    (0, common_1.Post)('maintenance/mark-expired'),
    (0, swagger_1.ApiOperation)({ summary: 'Mark expired transactions' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Expired transactions marked' }),
    openapi.ApiResponse({ status: 201, type: Object }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], VnpayPaymentController.prototype, "markExpiredTransactions", null);
exports.VnpayPaymentController = VnpayPaymentController = VnpayPaymentController_1 = __decorate([
    (0, swagger_1.ApiTags)('VNPAY Payment Gateway - Portable'),
    (0, common_1.Controller)('vnpay-payment'),
    __metadata("design:paramtypes", [vnpay_payment_service_1.VnpayPaymentService,
        vnpay_transaction_repository_1.VnpayTransactionRepository])
], VnpayPaymentController);
//# sourceMappingURL=vnpay-payment.controller.js.map